import { getPdfScreenshots } from "@/pdf-to-png/pdfToPng";
import fs from "fs";
import path from "path";

describe("pdfToPng", () => {
  it("should convert a pdf to a png", async () => {
    const pdfBuffer = fs.readFileSync(path.join(__dirname, "fixtures/simple-pdf/test.pdf"));
    const pngResult = fs.readFileSync(path.join(__dirname, "fixtures/simple-pdf/test-1.png"));
    const { images, pages } = await getPdfScreenshots(pdfBuffer);

    const {
      value: { pageNumber, image }
    } = await images.next();

    expect(pageNumber).toEqual(0);
    expect(image).toEqual(pngResult);
    expect(pages).toEqual(1);
  });

  it("should convert a pdf with multiple pages in batches", async () => {
    const pdfBuffer = fs.readFileSync(path.join(__dirname, "fixtures/multi-page-pdf/test.pdf"));

    const { images, pages } = await getPdfScreenshots(pdfBuffer, 4);

    await Promise.all(
      Array.from({ length: pages }, async (_, i) => {
        const {
          value: { pageNumber }
        } = await images.next(i);

        expect(pageNumber).toEqual(i);
      })
    );
  }, 100000);
});
