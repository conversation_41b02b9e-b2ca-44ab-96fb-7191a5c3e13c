import { PDFiumLibrary, PDFiumPage } from "@hyzyla/pdfium";
import { PDFiumPageRenderOptions } from "@hyzyla/pdfium/dist/page.types.js";
import sharp from "sharp";

async function renderFunction(options: PDFiumPageRenderOptions) {
  return await sharp(options.data, {
    raw: {
      width: options.width,
      height: options.height,
      channels: 4
    }
  })
    .png()
    .toBuffer();
}

async function pageToPng(page: PDFiumPage) {
  const image = await page.render({
    scale: 4,
    render: renderFunction
  });

  return image.data;
}

export async function getPdfScreenshots(pdfBuffer: Uint8Array<ArrayBufferLike>, batchSize: number = 1) {
  if (batchSize < 1) {
    throw new Error("Batch size must be greater than 0");
  }

  const library = await PDFiumLibrary.init();
  const document = await library.loadDocument(pdfBuffer);

  return {
    images: (async function* (): AsyncGenerator<{ pageNumber: number; image: Uint8Array<ArrayBufferLike> }> {
      const iterator = document.pages();
      let iteratorDone = false;

      // TODO: handle errors, destroy when exception is thrown
      while (!iteratorDone) {
        const batch: PDFiumPage[] = [];

        while (batch.length < batchSize) {
          const { value: page, done } = iterator.next();
          if (done) {
            iteratorDone = true;
            break;
          }

          batch.push(page);
        }

        const images = await Promise.all(batch.map(pageToPng));

        for (let i = 0; i < batch.length; i++) {
          yield {
            pageNumber: batch[i].number,
            image: images[i]
          };
        }
      }

      document.destroy();
      library.destroy();
    })(),
    pages: document.getPageCount()
  };
}
