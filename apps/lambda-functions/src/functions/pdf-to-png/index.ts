import { GetObjectCommand, PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import path from "path";
import { getPdfScreenshots } from "./pdfToPng";

const s3 = new S3Client({});

const streamToBuffer = (stream: any): Promise<Buffer> => {
  return new Promise((resolve, reject) => {
    const chunks: any[] = [];
    stream.on("data", (chunk: any) => chunks.push(chunk));
    stream.on("error", reject);
    stream.on("end", () => resolve(Buffer.concat(chunks)));
  });
};

interface PdfToPngEvent {
  bucket: string;
  key: string;
}

export const handler = async (event: PdfToPngEvent) => {
  const { limitFunction } = await import("p-limit");
  const concurrency = 4;

  const { bucket, key } = event;

  if (!bucket || !key) {
    throw new Error("Bucket and key must be provided in the event");
  }

  console.log(`Processing ${key} from bucket ${bucket}`);

  try {
    const getObjectParams = {
      Bucket: bucket,
      Key: key
    };

    const { Body } = await s3.send(new GetObjectCommand(getObjectParams));

    console.log("Object received from S3");

    if (!Body) {
      throw new Error("S3 object has no body");
    }

    const pdfBuffer = await streamToBuffer(Body);

    const images = await getPdfScreenshots(pdfBuffer, concurrency);

    console.log("PDF loaded");

    const promises = Array.from(
      { length: images.pages },
      limitFunction(
        async () => {
          const {
            value: { pageNumber, image }
          } = await images.images.next();

          const pngKey = path.join(key, `page_${pageNumber + 1}.png`);

          console.log(`Uploading ${pngKey} to S3`);

          const putObjectParams = {
            Bucket: bucket,
            Key: pngKey,
            Body: image,
            ContentType: "image/png"
          };
          return s3.send(new PutObjectCommand(putObjectParams));
        },
        {
          concurrency
        }
      )
    );

    await Promise.all(promises);

    console.log(`Successfully uploaded ${images.pages} PNGs to ${bucket}/${key}`);
  } catch (error) {
    console.error(`Error processing ${key}:`, error);
    throw error;
  }
};
