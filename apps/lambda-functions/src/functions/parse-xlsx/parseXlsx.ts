import * as XLSX from "xlsx";
import { WorkBook } from "xlsx";
import * as cpexcel from "xlsx/dist/cpexcel";
import { JSDOM } from "jsdom";

export enum OutputType {
  HTML = "html",
  JSON = "json"
}

export const parseXlsx = (body: Buffer, outputType: OutputType) => {
  try {
    XLSX.set_cptable(cpexcel);
    // Parse the Excel file
    const workbook = XLSX.read(body, {
      type: "buffer",
      UTC: false,
      nodim: true,
      dateNF: "yyyy-mm-dd hh:mm:ss"
    });

    switch (outputType) {
      case OutputType.HTML:
        return parseXlsxToHtml(workbook);
      case OutputType.JSON:
        return parseXlsxToJson(workbook);
      default:
        throw new Error("Invalid output type");
    }
  } catch (error) {
    console.error("Error processing Excel file:", error);
    throw new Error("Failed to process Excel file");
  }
};

interface Sheet {
  index: number;
  name: string;
  html: string;
}

const parseXlsxToHtml = (workbook: WorkBook) => {
  const sheets: Sheet[] = [];

  workbook.SheetNames.forEach((sheetName, index) => {
    const worksheet = workbook.Sheets[sheetName];
    const html = XLSX.utils.sheet_to_html(worksheet);
    const dom = new JSDOM(html);
    const document = dom.window.document;
    const body = document.body;

    for (const element of body.querySelectorAll("td")) {
      element.removeAttribute("id");
      element.removeAttribute("data-t");
      element.removeAttribute("data-v");
      element.removeAttribute("data-z");
      element.removeAttribute("xml:space");
      element.innerHTML = element.textContent?.trim() || "";
    }

    // remove empty rows
    for (const element of body.querySelectorAll("tr")) {
      // remove empty cells backwards until a cell with content is found
      for (let i = element.children.length - 1; i >= 0; i--) {
        const child = element.children[i];
        const hasContent =
          child.textContent?.trim() !== "" || child.getAttribute("rowspan") || child.getAttribute("colspan");

        if (hasContent) {
          break;
        }

        element.children[i].remove();
      }

      // remove empty rows
      if (element.children.length === 0) {
        element.remove();
      }
    }

    const htmlOutput = body.innerHTML;
    // use regex to add line breaks to the table
    const htmlOutputWithLineBreaks = htmlOutput.replaceAll(
      /(<\/?(?:tr|table|tbody|)>|<(?:\/td|th)>)/g,
      "$1\n"
    );
    sheets.push({
      index: index,
      name: sheetName,
      html: htmlOutputWithLineBreaks
    });
  });

  return sheets;
};

interface JSONSheet {
  index: number;
  name: string;
  rows: any[];
}

const parseXlsxToJson = (workbook: WorkBook) => {
  const jsonOutput: JSONSheet[] = [];

  workbook.SheetNames.forEach((sheetName, index) => {
    const worksheet = workbook.Sheets[sheetName];
    jsonOutput.push({
      index: index,
      name: sheetName,
      rows: XLSX.utils.sheet_to_json(worksheet, { header: 1, dateNF: "yyyy-mm-dd hh:mm:ss", raw: false })
    });
  });

  return jsonOutput;
};
