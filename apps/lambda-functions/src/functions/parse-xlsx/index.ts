import { Handler } from "aws-lambda";
import { OutputType, parseXlsx } from "./parseXlsx";

export const handler: Handler = async (event) => {
  try {
    if (!event.body) {
      return {
        statusCode: 400,
        error: "Request body is missing"
      };
    }

    const outputType = event.queryStringParameters?.output_type || OutputType.JSON;

    // Decode base64 encoded file if needed
    const isBase64Encoded = event.isBase64Encoded || false;
    const body = isBase64Encoded ? Buffer.from(event.body, "base64") : event.body;

    // Parse the Excel file
    const result = parseXlsx(body, outputType);

    return {
      statusCode: 200,
      sheets: result
    };
  } catch (error) {
    return {
      statusCode: 500,
      error: "Failed to process Excel file"
    };
  }
};
