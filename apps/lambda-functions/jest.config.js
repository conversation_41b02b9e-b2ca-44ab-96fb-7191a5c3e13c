import { ESM_TS_JS_TRANSFORM_PATTERN, TS_EXT_TO_TREAT_AS_ESM } from "ts-jest";
const esmModules = ["p-limit", "yocto-queue"];

export default {
  testEnvironment: "node",
  roots: ["<rootDir>/src"],
  testMatch: ["<rootDir>/src/test/**/*.spec.ts"],
  transform: {
    "^.+\\.(ts|tsx)$": "ts-jest",
    [ESM_TS_JS_TRANSFORM_PATTERN]: ["ts-jest", { useESM: true }]
  },
  extensionsToTreatAsEsm: [...TS_EXT_TO_TREAT_AS_ESM],
  transformIgnorePatterns: [`node_modules/(?!(?:.pnpm/)?(${esmModules.join("|")}))`],
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/src/functions/$1"
  }
};
