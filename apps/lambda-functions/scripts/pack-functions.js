const fs = require("fs");
const path = require("path");
const { exec } = require("child_process");
const util = require("util");

const execPromise = util.promisify(exec);

const main = async () => {
  const baseDir = path.resolve(__dirname, "..");
  const distDir = path.join(baseDir, "dist");
  const functionsDir = path.join(distDir, "functions");
  const destinationDir = path.join(distDir, "zipped-functions");

  try {
    // clean up the destination directory
    if (fs.existsSync(destinationDir)) {
      await fs.promises.rm(destinationDir, { recursive: true });
    }

    await fs.promises.mkdir(destinationDir, { recursive: true });

    // go through all the folders in the functions directory
    const functionFolders = fs.readdirSync(functionsDir);
    for (const functionFolder of functionFolders) {
      const functionPath = path.join(functionsDir, functionFolder);
      const destinationZipPath = path.join(destinationDir, `${functionFolder}.zip`);

      // zip the function
      await execPromise(`zip -r ${destinationZipPath} .`, {
        cwd: functionPath
      });

      console.log(`Zipped ${functionPath}`);
    }
  } catch (error) {
    console.error(error);
    process.exit(1);
  }
};

main();
