const fs = require("fs");
const path = require("path");
const { exec } = require("child_process");
const util = require("util");
const { spawnSync } = require("child_process");

const execPromise = util.promisify(exec);

const main = async () => {
  const baseDir = path.resolve(__dirname, "..");
  const distDir = path.join(baseDir, "dist");
  const workDir = path.join(distDir, "tmp-layer");
  const targetZipPath = path.join(distDir, "layer.zip");

  // prepare the structure of the layer
  const nodeModulesDestDir = path.join(workDir, "nodejs", "node22");
  const sourceNodeModules = path.join(workDir, "node_modules");

  console.log(`Building layer in ${workDir}`);
  try {
    if (fs.existsSync(targetZipPath)) {
      console.log(`Removing existing layer`);
      await fs.promises.rm(targetZipPath);
    }

    // remove the work dir if it exists
    console.log(`Removing existing work dir`);
    if (fs.existsSync(workDir)) {
      await fs.promises.rm(workDir, { recursive: true });
    }

    console.log(`Creating work dir`);
    await fs.promises.mkdir(workDir, { recursive: true });
    await fs.promises.mkdir(nodeModulesDestDir, { recursive: true });

    console.log(`Copying package.json and package-lock.json to work dir`);

    // cp package.json and package lock.json to the work dir
    await fs.promises.cp(path.join(baseDir, "package.json"), path.join(workDir, "package.json"));
    await fs.promises.cp(path.join(baseDir, "package-lock.json"), path.join(workDir, "package-lock.json"));

    console.log(`Installing dependencies`);

    // install omit devDependencies
    spawnSync("npm", ["install", "--omit=dev"], { cwd: workDir, stdio: "inherit" });

    // Copy node_modules
    console.log(`Copying node_modules to ${nodeModulesDestDir}`);
    await fs.promises.cp(sourceNodeModules, path.join(nodeModulesDestDir, "node_modules"), {
      recursive: true
    });

    console.log(`Zipping directory`);
    spawnSync("zip", ["-r", targetZipPath, "./nodejs"], { cwd: workDir, stdio: "inherit" });

    // Clean up
    console.log(`Cleaning up old layer directory`);
    if (fs.existsSync(workDir)) {
      await fs.promises.rm(workDir, { recursive: true });
    }

    console.log("Build-layer script finished");
  } catch (error) {
    console.error("An error occurred:", error);
    process.exit(1);
  }
};

main();
