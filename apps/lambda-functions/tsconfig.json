{"compilerOptions": {"outDir": "./dist", "rootDir": "./src", "module": "NodeNext", "target": "ESNext", "esModuleInterop": true, "isolatedModules": true, "lib": ["ESNext"], "skipLibCheck": true, "declaration": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "inlineSourceMap": true, "inlineSources": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "typeRoots": ["./node_modules/@types"], "paths": {"@/*": ["./src/functions/*"]}}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "cdk.out", "dist"]}