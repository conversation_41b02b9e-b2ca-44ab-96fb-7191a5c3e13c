{"name": "lambda-functions", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc --build tsconfig.build.json", "pack:functions": "node scripts/pack-functions.js", "pack:layer": "node scripts/pack-layer.js", "jest": "jest"}, "author": "<PERSON> <<EMAIL>>", "license": "ISC", "description": "", "dependencies": {"jsdom": "^26.1.0", "xlsx": "file:vendor/xlsx-0.20.3.tgz", "@hyzyla/pdfium": "~2.1.9", "sharp": "~0.34.3", "p-limit": "~6.2.0"}, "devDependencies": {"@aws-sdk/client-s3": "~3.668.0", "@types/aws-lambda": "^8.10.149", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.3", "typescript": "^5.8.3", "jest": "~30.0.4", "ts-jest": "~29.4.0", "@types/jest": "~30.0.0"}}