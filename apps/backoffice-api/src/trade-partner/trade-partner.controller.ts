import { Controller, Get, NotFoundException, Param, ParseIntPipe, Query, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiGetByIdResponses,
  ApiGetManyResponses,
  GetTradePartnersDto,
  GetTradePartnersResponseDto,
  TradePartner
} from "nest-modules";
import { TradePartnerService } from "./trade-partner.service";

@ApiTags("Trade Partner API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("trade-partners")
export class TradePartnerController {
  constructor(private readonly tradePartnerService: TradePartnerService) {}

  @ApiOperation({ summary: "Get Trade Partners" })
  @ApiGetManyResponses({ type: GetTradePartnersResponseDto })
  @Get()
  async getTradePartners(@Query() getTradePartnersDto: GetTradePartnersDto) {
    return await this.tradePartnerService.getTradePartners(getTradePartnersDto);
  }

  @ApiOperation({ summary: "Get Trade Partner" })
  @ApiParam({ name: "id", type: "integer", description: "Trade Partner ID" })
  @ApiGetByIdResponses({ type: TradePartner })
  @Get(":id")
  async getTradePartnerById(@Param("id", ParseIntPipe) id: number) {
    const partner = await this.tradePartnerService.getTradePartnerById(id);
    if (!partner) throw new NotFoundException("Trade Partner not found");
    return partner;
  }
}
