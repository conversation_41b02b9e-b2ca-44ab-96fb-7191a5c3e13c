import { Inject, Injectable, Scope } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import {
  AuthenticatedRequest,
  FIND_TRADE_PARTNER_RELATIONS,
  getFindOptions,
  GetTradePartnersDto,
  GetTradePartnersResponseDto,
  TRADE_PARTNER_ENUM_KEYS,
  TradePartner,
  TradePartnerColumn,
  UserPermission
} from "nest-modules";
import { QueryRunner, Raw, Repository } from "typeorm";

@Injectable({ scope: Scope.REQUEST })
export class TradePartnerService {
  constructor(
    @InjectRepository(TradePartner)
    private readonly tradePartnerRepository: Repository<TradePartner>,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest
  ) {}

  async getTradePartners(getTradePartnersDto: GetTradePartnersDto): Promise<GetTradePartnersResponseDto> {
    // if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
    //   getTradePartnersDto.organizationId = this.request?.user?.organization?.id || -1;
    const { organizationId, ...dto } = getTradePartnersDto;
    const { where, order, skip, take } = getFindOptions<TradePartner>(
      dto,
      TRADE_PARTNER_ENUM_KEYS,
      [],
      TradePartnerColumn.id
    );
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN) {
      where.organization = {
        id: Raw((alias) => `${alias} = :organizationId OR ${alias} IS NULL`, {
          organizationId: this.request?.user?.organization?.id || -1
        })
      };
    } else if (organizationId) {
      where.organization = {
        id: organizationId
      };
    }
    const [partners, total] = await this.tradePartnerRepository.findAndCount({
      where,
      relations: FIND_TRADE_PARTNER_RELATIONS,
      order,
      skip,
      take
    });
    return {
      partners,
      total,
      skip,
      limit: take
    };
  }

  async getTradePartnerById(partnerId: number, queryRunner?: QueryRunner) {
    const tradePartnerRepository = queryRunner
      ? queryRunner.manager.getRepository(TradePartner)
      : this.tradePartnerRepository;
    return await tradePartnerRepository.findOne({
      where: {
        id: partnerId,
        organization: {
          id: Raw(
            (alias) =>
              `${alias} IS NULL OR ${alias} ${this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN ? "= :organizationId" : "IS NOT NULL"}`,
            { organizationId: this.request?.user?.organization?.id || -1 }
          )
        }
      },
      relations: FIND_TRADE_PARTNER_RELATIONS
    });
  }
}
