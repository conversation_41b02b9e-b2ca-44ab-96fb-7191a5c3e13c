import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { TradePartner } from "nest-modules";
import { TradePartnerController } from "./trade-partner.controller";
import { TradePartnerService } from "./trade-partner.service";

@Module({
  imports: [TypeOrmModule.forFeature([TradePartner])],
  providers: [TradePartnerService],
  controllers: [TradePartnerController],
  exports: [TradePartnerService]
})
export class TradePartnerModule {}
