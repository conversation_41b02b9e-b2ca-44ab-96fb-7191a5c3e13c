import { MiddlewareConsumer, Module, NestModule, RequestMethod } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { EventEmitterModule } from "@nestjs/event-emitter";
import { JwtModule } from "@nestjs/jwt";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  AuthModule,
  BaseImporterModule,
  CanadaAntiDumpingModule,
  CanadaExciseTaxCodeModule,
  CanadaGstExemptCodeModule,
  CanadaOgdModule,
  CanadaSimaCodeModule,
  CanadaTariffModule,
  CanadaTreatmentCodeModule,
  CanadaVfdCodeModule,
  CandataModule,
  CarmModule,
  CountryModule,
  DocumentTypeModule,
  ENTITY_LIST,
  FirebaseModule,
  LoginMethod,
  MatchingRuleModule,
  NodeEnv,
  RequestLoggerMiddleware,
  TrackingHistoryModule,
  UserModule,
  BaseDocusignModule,
  OrganizationCustomsBroker,
  CandataAccountAuthType
} from "nest-modules";
import { AppController } from "./app.controller";
import { DocusignModule } from "./docusign/docusign.module";
import { OAuthModule } from "./oauth/oauth.module";
import { TradePartnerModule } from "./trade-partner/trade-partner.module";
import { ImporterModule } from "./importer/importer.module";

export const NODE_ENV = (process.env.NODE_ENV as NodeEnv) || NodeEnv.LOCAL;
@Module({
  imports: [
    JwtModule.register({ global: true }),
    ConfigModule.forRoot({
      envFilePath: `.env.${NODE_ENV}`,
      isGlobal: true
    }),
    TypeOrmModule.forRoot({
      type: "postgres",
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT),
      username: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      synchronize: true,
      ssl: NODE_ENV !== NodeEnv.LOCAL,
      extra: {
        idleTimeoutMillis: 30000,
        ...(NODE_ENV !== NodeEnv.LOCAL ? { ssl: { rejectUnauthorized: false } } : {})
      },
      entities: ENTITY_LIST,
      connectTimeoutMS: 5000,
      poolSize: 20
    }),
    EventEmitterModule.forRoot({
      ignoreErrors: true
    }),
    AuthModule.register({
      accessTokenSecret: process.env.ACCESS_TOKEN_SECRET,
      refreshTokenSecret: process.env.REFRESH_TOKEN_SECRET,
      accessTokenExpiresInSec: parseInt(process.env.ACCESS_TOKEN_EXPIRES_IN_SEC),
      refreshTokenExpiresInSec: parseInt(process.env.REFRESH_TOKEN_EXPIRES_IN_SEC),
      refreshTokenGracePeriodSec: parseInt(process.env.REFRESH_TOKEN_GRACE_PERIOD_SEC),
      googleOAuthClientId: process.env.GOOGLE_OAUTH_CLIENT_ID,
      allowedLoginMethods: [LoginMethod.GOOGLE_SSO]
    }),
    UserModule.register({
      user: {
        readMany: true,
        readOne: true,
        create: true,
        update: true,
        delete: true
      },
      organization: {
        readMany: true,
        readOne: true,
        create: true,
        update: true,
        delete: true
      }
    }),
    CandataModule.register({
      accounts: [
        {
          account: OrganizationCustomsBroker.USAV,
          authType: CandataAccountAuthType.API_KEY,
          baseUrl: process.env.USAV_CANDATA_BASE_URL,
          apiKey: process.env.USAV_CANDATA_TOKEN
        },
        {
          account: OrganizationCustomsBroker.CLARO,
          authType: CandataAccountAuthType.CLIENT_ID_AND_SECRET,
          baseUrl: process.env.CLARO_CANDATA_BASE_URL,
          authUrl: process.env.CLARO_CANDATA_AUTH_URL,
          clientId: process.env.CLARO_CANDATA_CLIENT_ID,
          clientSecret: process.env.CLARO_CANDATA_CLIENT_SECRET
        }
      ],
      appId: process.env.CANDATA_REMOTE_SERVER_APP_ID
    }),
    FirebaseModule.register({
      projectId: process.env.FIREBASE_PROJECT_ID,
      privateKey: process.env.FIREBASE_PRIVATE_KEY,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL
    }),
    CanadaTariffModule.register({
      readMany: true,
      readOne: true,
      sync: true
    }),
    CanadaOgdModule.register({
      readMany: true,
      readOne: true,
      create: true,
      update: true,
      delete: true
    }),
    CanadaSimaCodeModule.register({
      readMany: true,
      readOne: true,
      create: true,
      update: true,
      delete: true
    }),
    CanadaTreatmentCodeModule.register({
      readMany: true,
      readOne: true,
      create: true,
      update: true,
      delete: true
    }),
    CanadaGstExemptCodeModule.register({
      readMany: true,
      readOne: true,
      create: true,
      update: true,
      delete: true
    }),
    CanadaVfdCodeModule.register({
      readMany: true,
      readOne: true,
      create: true,
      update: true,
      delete: true
    }),
    CanadaAntiDumpingModule.register({
      readMany: true,
      readOne: true,
      create: true,
      update: true,
      delete: true
    }),
    CanadaExciseTaxCodeModule.register({
      readMany: true,
      readOne: true,
      create: true,
      update: true,
      delete: true
    }),
    DocumentTypeModule.register({
      readMany: true,
      readOne: true,
      create: true,
      update: true,
      delete: true
    }),
    MatchingRuleModule.register({
      readMany: true,
      readOne: true,
      create: true,
      update: true,
      delete: true,
      query: true
    }),
    TrackingHistoryModule.register(),
    CarmModule.register({
      appId: process.env.CARM_APP_ID
    }),
    DocusignModule,
    BaseDocusignModule.register({
      integrationKey: process.env.DOCUSIGN_INTEGRATION_KEY,
      secretKey: process.env.DOCUSIGN_SECRET_KEY,
      poaTemplateId: process.env.DOCUSIGN_POA_TEMPLATE_ID,
      baseUrl: process.env.DOCUSIGN_BASE_URL
    }),
    OAuthModule,
    BaseImporterModule,
    ImporterModule,
    CountryModule,
    TradePartnerModule
  ],
  controllers: [AppController],
  providers: []
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(RequestLoggerMiddleware).forRoutes({ path: "*", method: RequestMethod.ALL });
  }
}
