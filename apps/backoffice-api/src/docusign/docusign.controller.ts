import { Body, Controller, HttpCode, Post } from "@nestjs/common";
import { ApiOkResponse, ApiOperation, ApiTags } from "@nestjs/swagger";
import { OnImporterPOASignedDto } from "nest-modules";
import { DocusignService } from "./docusign.service";

@ApiTags("Docusign API")
@Controller("docusign")
export class DocusignController {
  constructor(private readonly docusignService: DocusignService) {}

  @ApiOperation({ summary: "On Importer POA Signed" })
  @ApiOkResponse()
  @HttpCode(200)
  @Post("webhook/importer-poa")
  async onImporterPOASigned(@Body() onImporterPOASignedDto: OnImporterPOASignedDto) {
    await this.docusignService.onImporterPOASigned(onImporterPOASignedDto);
    return;
  }
}
