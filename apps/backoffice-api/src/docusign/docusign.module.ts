import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { DocusignToken } from "nest-modules";
import { ImporterModule } from "../importer/importer.module";
import { DocusignController } from "./docusign.controller";
import { DocusignService } from "./docusign.service";

@Module({
  imports: [TypeOrmModule.forFeature([DocusignToken]), ImporterModule],
  providers: [DocusignService],
  controllers: [DocusignController],
  exports: [DocusignService]
})
export class DocusignModule {}
