import { Inject, Injectable, RawBodyRequest, Scope } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { REQUEST } from "@nestjs/core";
import { createHmac, timingSafeEqual } from "crypto";
import {
  AuthenticatedRequest,
  FirebaseService,
  Importer,
  ImporterStatus,
  OnImporterPOASignedDto
} from "nest-modules";
import { DataSource } from "typeorm";
import { ImporterService } from "../importer/importer.service";
@Injectable({ scope: Scope.REQUEST })
export class DocusignService {
  constructor(
    @Inject(ConfigService)
    private readonly configService: ConfigService,
    @Inject(ImporterService)
    private readonly importerService: ImporterService,
    @Inject(FirebaseService)
    private readonly firebaseService: FirebaseService,
    @Inject(REQUEST)
    private readonly request: RawBodyRequest<AuthenticatedRequest>,
    private readonly dataSource: DataSource
  ) {}

  verifyDocusignWebhookRequest() {
    const DOCUSIGN_WEBHOOK_HMAC_KEY = this.configService.get<string>("DOCUSIGN_WEBHOOK_HMAC_KEY");
    const DOCUSIGN_INTEGRATION_KEY = this.configService.get<string>("DOCUSIGN_INTEGRATION_KEY");
    const DOCUSIGN_SECRET_KEY = this.configService.get<string>("DOCUSIGN_SECRET_KEY");
    const body = this.request?.rawBody?.toString();
    const userAgent = this.request?.headers?.["user-agent"];
    const hmacList = Object.entries(this.request?.headers || {})
      .filter(([key, _]) => key.toLowerCase().startsWith("x-docusign-signature-"))
      .reduce((list, [_, value]) => list.concat(Array.isArray(value) ? value : [value]), [] as Array<string>);
    const [basic, authB64] = this.request?.headers?.authorization?.split(" ") ?? [];
    const [username, password] =
      typeof authB64 === "string" ? Buffer.from(authB64, "base64").toString().split(":") : [];

    const hmac = createHmac("sha256", DOCUSIGN_WEBHOOK_HMAC_KEY);
    hmac.write(body);
    hmac.end();
    const calculatedHash = hmac.read().toString("base64");

    console.log(
      `User Agent: ${userAgent}, HMAC List: ${JSON.stringify(hmacList)}, calculated Hash: ${calculatedHash}, Username: ${username}, Password: ${password}`
    );
    return (
      userAgent?.toLowerCase() === "docusign" &&
      hmacList.some((hash) =>
        timingSafeEqual(Buffer.from(hash, "base64"), Buffer.from(calculatedHash, "base64"))
      ) &&
      basic === "Basic" &&
      username === DOCUSIGN_INTEGRATION_KEY &&
      password === DOCUSIGN_SECRET_KEY
    );
  }

  async onImporterPOASigned(onImporterPOASignedDto: OnImporterPOASignedDto) {
    if (!this.verifyDocusignWebhookRequest()) {
      console.warn("Request is not valid Docusign request, skipping...");
      return;
    }

    const envelopeId = onImporterPOASignedDto?.data?.envelopeId;
    if (!envelopeId) {
      console.warn(`Envelope ID ${envelopeId} not found, skipping...`);
      return;
    }

    const importer = await this.importerService.getImporterByEnvelopeId(envelopeId);

    if (!importer) {
      console.warn(`Importer with envelope id: ${envelopeId} not found on db, update onboarding instead...`);
      await this.firebaseService.updatePoaStatus(envelopeId);
      console.log(`Document with envelope id: ${envelopeId} updated,`);
      return;
    }

    if (importer.status !== ImporterStatus.PENDING_POA) {
      console.warn("Importer status is not Pending POA, skipping...");
      return;
    }

    console.log(`Importer ID: ${importer?.id}, status: ${importer?.status}`);

    await this.dataSource.manager.update(Importer, { id: importer.id }, { status: ImporterStatus.ACTIVE });
    // TODO send onboarding email
    return;
  }
}
