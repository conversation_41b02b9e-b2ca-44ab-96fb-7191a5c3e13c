import { ValidationPipe } from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import type { NestExpressApplication } from "@nestjs/platform-express";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import cookieParser from "cookie-parser";
import * as fs from "fs";
import { NodeEnv, Product } from "nest-modules";
import { AppModule, NODE_ENV } from "./app.module";

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    rawBody: true
  });
  const config = new DocumentBuilder()
    .setTitle("AutoCustoms Backoffice API")
    .setVersion("1.0")
    // .addServer('https://slam-api.anteklogistics.com/backoffice-dev')
    // .addApiKey({
    //   type: 'apiKey',
    //   in: 'header',
    //   name: 'X-API-Key'
    // }, 'apiKey')
    .addBearerAuth(
      {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
        description: "Access Token"
      },
      "accessToken"
    )
    .build();
  const document = SwaggerModule.createDocument(app, config, {
    extraModels: [Product]
  });
  fs.writeFileSync("swagger-spec.json", JSON.stringify(document));
  if (NODE_ENV === NodeEnv.LOCAL)
    SwaggerModule.setup("docs", app, document, {
      swaggerOptions: { redirect: false }
    });
  app.use(cookieParser());
  app.useBodyParser("json", { limit: "50mb" });
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidUnknownValues: true
    })
  );
  app.enableCors({
    origin: [
      "http://localhost:5173",
      "http://localhost:5174",
      "http://localhost:5175",
      "https://d2m3iv85zzcpfr.cloudfront.net",
      "https://backoffice-dev.clarocustoms.com"
    ],
    exposedHeaders: [],
    credentials: true
  });
  await app.listen(5000);
}
bootstrap();
