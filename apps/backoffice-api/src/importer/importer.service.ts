import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import {
  BaseDocusignService,
  BaseImporterService,
  CarmService,
  CreateImporterDto,
  EditImporterDto,
  Importer,
  ImporterStatus
} from "nest-modules";
import { Repository } from "typeorm";

@Injectable({ scope: Scope.REQUEST })
export class ImporterService {
  constructor(
    @InjectRepository(Importer)
    private readonly importerRepository: Repository<Importer>,
    @Inject(BaseImporterService)
    private readonly baseImporterService: BaseImporterService,
    @Inject(BaseDocusignService)
    private readonly baseDocusignService: BaseDocusignService,
    @Inject(CarmService)
    private readonly carmService: CarmService
  ) {}
  private readonly logger = new Logger(ImporterService.name);

  async createImporter(createImporterDto: CreateImporterDto) {
    try {
      const importer = await this.baseImporterService.createImporter(createImporterDto);

      // TODO send email
      // await this.emailService.sendOnboardingEmail(importer);

      return await this.baseImporterService.getImporterById(importer.id);
    } catch (error) {
      this.logger.error("Error in createImporter", error);
      throw new InternalServerErrorException(error);
    }
  }

  async editImporter(importerId: number, editImporterDto: EditImporterDto) {
    try {
      const importer = await this.baseImporterService.editImporter(importerId, editImporterDto);

      // TODO send email
      // await this.emailService.sendOnboardingEmail(importer);

      return await this.baseImporterService.getImporterById(importer.id);
    } catch (error) {
      this.logger.error("Error in editImporter", error);
      throw new InternalServerErrorException(error);
    }
  }

  async getImporterByEnvelopeId(envelopeId: string) {
    try {
      return await this.baseImporterService.getImporterByEnvelopeId(envelopeId);
    } catch (error) {
      this.logger.error("Error in getImporterByEnvelopeId", error);
      throw new InternalServerErrorException(error);
    }
  }

  async sendCarmRequest(importerId: number) {
    try {
      const importer = await this.baseImporterService.getImporterById(importerId);
      if (!importer) throw new NotFoundException("Importer not found");
      if (importer.status === ImporterStatus.DISABLED)
        throw new BadRequestException("Importer is not active");

      const businessNo = importer.businessNumber.slice(0, 9);

      if (businessNo.length !== 9)
        throw new BadRequestException("Business number must be exactly 9 characters");

      const carm = await this.carmService.sendRequest(businessNo);

      importer.carmStatus = carm?.result;
      await this.importerRepository.save(importer);

      return await this.baseImporterService.getImporterById(importer.id);
    } catch (error) {
      this.logger.error("Error in sendCarmRequest", error);
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(error);
    }
  }

  async resendPoa(importerId: number) {
    try {
      const importer = await this.baseImporterService.getImporterById(importerId);
      if (!importer) throw new NotFoundException("Importer not found");
      if (importer.status !== ImporterStatus.PENDING_POA)
        throw new BadRequestException("Importer is not pending POA");
      if (!importer.docusignEnvelopeId)
        throw new BadRequestException("Importer does not have a Docusign envelope ID");
      return await this.baseDocusignService.resendEnvelope(importer.docusignEnvelopeId);
    } catch (error) {
      this.logger.error("Error in resendPoa", error);
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(error);
    }
  }
}
