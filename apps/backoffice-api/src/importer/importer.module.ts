import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { BaseImporterModule, CarmModule, Importer } from "nest-modules";
import { ImporterService } from "./importer.service";
import { ImporterController } from "./importer.controller";

@Module({
  imports: [TypeOrmModule.forFeature([Importer]), BaseImporterModule, CarmModule],
  providers: [ImporterService],
  controllers: [ImporterController],
  exports: [ImporterService]
})
export class ImporterModule {}
