import { Body, Controller, Param, ParseIntPipe, Post, Put, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiEditResponses,
  CreateImporterDto,
  EditImporterDto,
  Importer
} from "nest-modules";
import { ImporterService } from "./importer.service";

@ApiAccessTokenAuthenticated()
@ApiTags("Importer API")
@UseGuards(AccessTokenGuard)
@Controller("importers")
export class ImporterController {
  constructor(private readonly importerService: ImporterService) {}

  @Post()
  @ApiOperation({ summary: "Create Importer" })
  @ApiCreateResponses({ type: Importer })
  async createImporter(@Body() createImporterDto: CreateImporterDto) {
    return await this.importerService.createImporter(createImporterDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Edit Importer" })
  @ApiParam({ name: "id", type: "integer", description: "Importer ID" })
  @ApiEditResponses({ type: Importer })
  async editImporter(@Param("id", ParseIntPipe) id: number, @Body() editImporterDto: EditImporterDto) {
    return await this.importerService.editImporter(id, editImporterDto);
  }

  @Post(":id/carm-request")
  @ApiOperation({ summary: "Send Carm Request" })
  @ApiParam({ name: "id", type: "integer", description: "Importer ID" })
  @ApiEditResponses({ type: Importer })
  async sendRequest(@Param("id", ParseIntPipe) id: number) {
    return await this.importerService.sendCarmRequest(id);
  }

  @Post(":id/resend-poa")
  @ApiOperation({ summary: "Resend POA" })
  @ApiParam({ name: "id", type: "integer", description: "Importer ID" })
  @ApiEditResponses({ type: Importer })
  async resendPoa(@Param("id", ParseIntPipe) id: number) {
    return await this.importerService.resendPoa(id);
  }
}
