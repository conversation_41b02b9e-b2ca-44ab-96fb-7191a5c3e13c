import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  NotImplementedException,
  Scope
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import axios from "axios";
import { createHash, createHmac, randomBytes } from "crypto";
import moment from "moment-timezone";
import {
  AuthenticatedRequest,
  AuthorizationCallbackResponseDto,
  DocusignAccessTokenResponse,
  DocusignGetUserInfoResponse,
  DocusignToken,
  FIND_DOCUSIGN_TOKEN_RELATIONS,
  GetDocusignAuthorizationUriResponseDto,
  NodeEnv,
  UserPermission,
  UserService
} from "nest-modules";
import { Repository } from "typeorm";
import { NODE_ENV } from "../app.module";

@Injectable({ scope: Scope.REQUEST })
export class OAuthService {
  constructor(
    @InjectRepository(DocusignToken)
    private readonly docusignTokenRepository: Repository<DocusignToken>,
    @Inject(UserService)
    private readonly userService: UserService,
    @Inject(ConfigService)
    private readonly configService: ConfigService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest
  ) {}
  static DOCUSIGN_DEV_BASE_URL = "https://account-d.docusign.com";
  static DOCUSIGN_PROD_BASE_URL = "https://account.docusign.com";

  async getDocusignAuthorizationUri(): Promise<GetDocusignAuthorizationUriResponseDto> {
    const DOCUSIGN_INTEGRATION_KEY = this.configService.get<string>("DOCUSIGN_INTEGRATION_KEY");
    const OAUTH_STATE_SECRET_KEY = this.configService.get<string>("OAUTH_STATE_SECRET_KEY");
    if (!DOCUSIGN_INTEGRATION_KEY)
      throw new InternalServerErrorException("Docusign integration key is not set");
    if (!OAUTH_STATE_SECRET_KEY) throw new InternalServerErrorException("OAuth state secret key is not set");
    const AUTH_TYPE = "docusign";
    let backofficeApiBaseUrl: string;
    switch (NODE_ENV) {
      case NodeEnv.LOCAL:
        backofficeApiBaseUrl = "http://localhost:5000";
        break;
      case NodeEnv.DEVELOPMENT:
        backofficeApiBaseUrl = "https://backoffice-api-dev.clarocustoms.com";
        break;
      case NodeEnv.PRODUCTION:
        // TODO: Set the production base URL
        backofficeApiBaseUrl = "https://backoffice-api-dev.clarocustoms.com";
        break;
      default:
        throw new NotImplementedException("Invalid Node Environment");
    }

    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user is not backoffice admin");
    const codeVerifier = randomBytes(32).toString("hex");
    const currentMsHmac = createHmac("sha256", OAUTH_STATE_SECRET_KEY)
      .update(`${AUTH_TYPE}:${codeVerifier}:${this.request?.user?.id}`)
      .digest("hex");
    const queryParams = {
      response_type: "code",
      scope: "extended signature",
      client_id: DOCUSIGN_INTEGRATION_KEY,
      state: Buffer.from(`${AUTH_TYPE}:${codeVerifier}:${this.request?.user?.id}:${currentMsHmac}`).toString(
        "base64url"
      ),
      redirect_uri: `${backofficeApiBaseUrl}/oauth/callback`,
      code_challenge: createHash("sha256").update(codeVerifier).digest("base64url"),
      code_challenge_method: "s256"
    };
    // TODO: Set the Docusign base URL based on the NODE_ENV
    let authorizationUri = `${OAuthService.DOCUSIGN_DEV_BASE_URL}/oauth/auth?${Object.entries(queryParams)
      .map(([key, value]) => key + "=" + encodeURIComponent(value))
      .join("&")}`;

    return {
      authorizationUri
    };
  }

  async authorizationCallback(code: string, state: string): Promise<AuthorizationCallbackResponseDto> {
    const DOCUSIGN_INTEGRATION_KEY = this.configService.get<string>("DOCUSIGN_INTEGRATION_KEY");
    const DOCUSIGN_SECRET_KEY = this.configService.get<string>("DOCUSIGN_SECRET_KEY");
    const OAUTH_STATE_SECRET_KEY = this.configService.get<string>("OAUTH_STATE_SECRET_KEY");
    const THIRTY_DAYS_MS = 30 * 24 * 60 * 60 * 1000;

    const currentMs = Date.now();
    //console.log(`Code: ${code}`);
    if (typeof code !== "string" || code.length <= 0) throw new BadRequestException("Invalid code");
    const [authType, codeVerifier, userId, hmac] =
      typeof state === "string" && state.length > 0
        ? Buffer.from(state, "base64url").toString().split(":")
        : [];
    //console.log(`Auth Type: ${authType}, code verifier: ${codeVerifier}, user ID: ${userId}, HMAC: ${hmac}`);
    if (
      typeof authType !== "string" ||
      authType.length <= 0 ||
      typeof codeVerifier !== "string" ||
      codeVerifier.length <= 0 ||
      typeof userId !== "string" ||
      !/^[1-9]\d*$/.test(userId) ||
      typeof hmac !== "string" ||
      hmac.length <= 0
    )
      throw new BadRequestException("Invalid state");
    const currentHmac = createHmac("sha256", OAUTH_STATE_SECRET_KEY)
      .update(`${authType}:${codeVerifier}:${userId}`)
      .digest("hex");
    //console.log(`Current HMAC: ${currentHmac}`);
    if (currentHmac !== hmac) throw new BadRequestException("Invalid signature");
    const user = await this.userService.getUserById(parseInt(userId), true);
    //console.log(`User: ${user?.id}`);
    if (!user) throw new NotFoundException("User not found");
    if (user.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user is not backoffice admin");

    switch (authType) {
      case "docusign":
        try {
          const accessTokenRes = await axios.post(
            `${OAuthService.DOCUSIGN_DEV_BASE_URL}/oauth/token`,
            {
              grant_type: "authorization_code",
              code_verifier: codeVerifier,
              code
            },
            {
              auth: {
                username: DOCUSIGN_INTEGRATION_KEY,
                password: DOCUSIGN_SECRET_KEY
              },
              headers: {
                "Content-Type": "application/x-www-form-urlencoded"
              }
            }
          );
          //console.log(`Access Token Res Status: ${accessTokenRes.status}, data: ${JSON.stringify(accessTokenRes?.data)}`);
          const { access_token, token_type, refresh_token, expires_in } =
            accessTokenRes?.data as DocusignAccessTokenResponse;
          const userInfoRes = await axios.get(`${OAuthService.DOCUSIGN_DEV_BASE_URL}/oauth/userinfo`, {
            headers: {
              Authorization: `Bearer ${access_token}`
            }
          });
          //console.log(`Get User Info status: ${userInfoRes?.status}, data: ${JSON.stringify(userInfoRes?.data)}`);
          const userInfo = userInfoRes?.data as DocusignGetUserInfoResponse;
          let docusignTokenRecord = await this.docusignTokenRepository.findOne({
            where: { accountId: userInfo.accounts[0].account_id },
            relations: FIND_DOCUSIGN_TOKEN_RELATIONS
          });
          if (!docusignTokenRecord) {
            //console.log('No existing Docusign Token record, creating new...');
            docusignTokenRecord = new DocusignToken();
            docusignTokenRecord.accountId = userInfo.accounts[0].account_id;
            docusignTokenRecord.createdBy = user || null;
          }
          docusignTokenRecord.lastEditedBy = user || null;
          docusignTokenRecord.baseUri = userInfo.accounts[0].base_uri;
          docusignTokenRecord.accessToken = access_token;
          docusignTokenRecord.accessTokenExpiresIn = moment
            .tz(currentMs + expires_in * 1000, "America/Toronto")
            .toDate();
          docusignTokenRecord.refreshToken = refresh_token;
          docusignTokenRecord.refreshTokenExpiresIn = moment
            .tz(currentMs + THIRTY_DAYS_MS, "America/Toronto")
            .toDate();
          docusignTokenRecord = await this.docusignTokenRepository.save(docusignTokenRecord);
          //console.log(`Docusign Token Record: ${docusignTokenRecord.accountId}`);
        } catch (error) {
          throw new InternalServerErrorException(
            `Got error while getting Docusign access token. Error: ${error.message}`
          );
        }
        return { message: "Docusign Authorization Successful" };
      default:
        throw new BadRequestException("Invalid auth type");
    }
  }
}
