import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { DocusignToken } from "nest-modules";
import { OAuthService } from "./oauth.service";
import { OAuthController } from "./oauth.controller";

@Module({
  imports: [TypeOrmModule.forFeature([DocusignToken])],
  providers: [OAuthService],
  controllers: [OAuthController],
  exports: [OAuthService]
})
export class OAuthModule {}
