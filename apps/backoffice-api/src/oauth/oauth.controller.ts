import { Controller, Get, NotFoundException, Query, UseGuards } from "@nestjs/common";
import { OAuthService } from "./oauth.service";
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags
} from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  AuthorizationCallbackResponseDto,
  ForbiddenResponseDto,
  GetDocusignAuthorizationUriResponseDto,
  InternalServerErrorResponseDto,
  NotFoundResponseDto
} from "nest-modules";

@ApiTags("OAuth API")
@Controller("oauth")
export class OAuthController {
  constructor(private readonly oauthService: OAuthService) {}

  @ApiOperation({ summary: "Get Docusign Authorization URI" })
  @ApiAccessTokenAuthenticated()
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiOkResponse({ type: GetDocusignAuthorizationUriResponseDto })
  @UseGuards(AccessTokenGuard)
  @Get("docusign")
  async getDocusignAuthorizationUri() {
    return await this.oauthService.getDocusignAuthorizationUri();
  }

  @ApiOperation({ summary: "Authorization Callback" })
  @ApiOkResponse({ type: AuthorizationCallbackResponseDto })
  @ApiBadRequestResponse({ type: AuthorizationCallbackResponseDto })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiInternalServerErrorResponse({ type: InternalServerErrorResponseDto })
  @Get("callback")
  async authorizationCallback(@Query("code") code: string, @Query("state") state: string) {
    return await this.oauthService.authorizationCallback(code, state);
  }
}
