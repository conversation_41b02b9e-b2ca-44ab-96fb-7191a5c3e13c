{"name": "backoffice-api", "version": "0.0.1", "private": true, "scripts": {"build": "nest build", "start": "nest start --watch", "start:prod": "nest start"}, "dependencies": {"nest-modules": "workspace:^", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "~2.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.2", "@nestjs/swagger": "^7.3.1", "@nestjs/typeorm": "^10.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.6", "express": "^4.19.2", "pg": "^8.11.5", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.20", "moment-timezone": "~0.5.46", "axios": "~1.7.7", "raw-body": "~3.0.0", "googleapis": "~144.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/cookie-parser": "^1.4.7", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}}