import RoutesRoot from "@/config/Routes.config";
import { useMemo } from "react";
import { RouterProvider, createBrowserRouter } from "react-router-dom";

const RouterBootstrap = () => {
  // const { session } = useStore().auth;

  // Filter the route by user action permission
  const route = useMemo(() => {
    // if (session?.permittedActions) {
    //   const routes: RouterObject = [];

    //   RoutesRoot.forEach((root, i) => {
    //     if (i == 1) {
    //       const privateRoute: RouterObject = [];

    //       if (root.children) {
    //         for (const child of root.children) {
    //           // TODO handle sub child route
    //           // const childRoute: RouterObject = [];

    //           // if (child.children) {
    //           // for (const sub of child.children) {
    //           //   if (
    //           //     !sub.permission ||
    //           //     session?.permittedActions.includes(sub.permission)
    //           //   )
    //           //     childRoute.push(sub);
    //           // }
    //           // privateRoute.push(childRoute);
    //           // child.children = childRoute;
    //           // } else {
    //           if (
    //             !child.permission ||
    //             session?.permittedActions.includes(child.permission)
    //           )
    //             privateRoute.push(child);
    //           // }
    //         }

    //         root.children = privateRoute;
    //         routes.push(root);
    //       }
    //     } else routes.push(root);
    //   });
    //   return createBrowserRouter(routes);
    // } else {
    return createBrowserRouter(RoutesRoot);
    // }
  }, []);

  return route && <RouterProvider router={route} />;
};

export default RouterBootstrap;
