import { env } from "@/config/Environment.config";
import { queryConfig } from "@/config/ReactQuery.config";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Suspense, useState } from "react";
import { Toaster } from "react-hot-toast";
import { Icons, Popup } from "ui";
import RouterBootstrap from "./Router.bootstrap";
import Store, { StoreProvider } from "./Store.bootstrap";

function App() {
  const [queryClient] = useState(new QueryClient(queryConfig));
  const [store] = useState(new Store());

  return (
    <StoreProvider store={store}>
      <QueryClientProvider client={queryClient}>
        <GoogleOAuthProvider clientId={env.googleId}>
          <Popup.PopupProvider>
            <Suspense
              fallback={
                <div className="flex h-screen w-full items-center justify-center">
                  <Icons.Loader />
                </div>
              }
            >
              <>
                {/* <RouterProvider router={route} /> */}
                <RouterBootstrap />
                <Toaster />
              </>
            </Suspense>
          </Popup.PopupProvider>
        </GoogleOAuthProvider>
      </QueryClientProvider>
    </StoreProvider>
  );
}
export default App;
