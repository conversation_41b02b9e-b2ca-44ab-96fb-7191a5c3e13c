import { ErrorCodes } from "@/common/Constant.common";
import { BaseError } from "@/common/Types.common";
import { Layout } from "@/components";
import { AfterResponseHook } from "ky";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect, useState } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import { Icons } from "ui";
import { minuteDifference } from "utils";
import { http } from "./Http.bootstrap";
import { useStore } from "./Store.bootstrap";
import { useQueryClient } from "@tanstack/react-query";

const Wrapper = observer(() => {
  const { session, refreshToken, isLoading, isHydrated, isReady, initialize, isAuthenticated, logout } =
    useStore().auth;
  const navigate = useNavigate();
  const [sessionTimer, setSessionTimer] = useState<NodeJS.Timeout>();
  const queryClient = useQueryClient();

  const handleLogout = useCallback(async () => {
    await logout();
    queryClient.removeQueries();
  }, [logout, queryClient]);

  //#region Error interceptor
  useEffect(() => {
    const errInterceptor: AfterResponseHook = async (_input, _options, response) => {
      if ((response.status === 401 || response.status === 403) && !isLoading) {
        const error: BaseError = await response.json();

        switch (error.message) {
          case ErrorCodes.InvalidAccessToken:
            refreshToken();
            break;
          case ErrorCodes.InvalidRefreshToken:
            handleLogout();
            break;
          default:
            handleLogout();
            break;
        }
      }
    };

    http.updateConfig({
      hooks: {
        afterResponse: [errInterceptor]
      }
    });
  }, [handleLogout, isLoading, refreshToken]);

  //#endregion

  //#region REFRESH TOKEN
  const checkToken = useCallback(async () => {
    if (session?.accessTokenExpiryDate) {
      const expiresAt = minuteDifference(new Date(session.accessTokenExpiryDate), new Date());

      if (expiresAt <= 10) {
        refreshToken();
      }
    }
  }, [refreshToken, session?.accessTokenExpiryDate]);

  useEffect(() => {
    if (session?.accessToken) {
      if (sessionTimer) return;

      setSessionTimer(
        setInterval(
          () => {
            checkToken();
          },
          9 * 60 * 1000
        )
      );
    } else {
      if (sessionTimer) {
        clearInterval(sessionTimer);
      }
    }
  }, [session?.accessToken, sessionTimer, checkToken]);

  //#endregion

  //#region Init
  useEffect(() => {
    if (isHydrated) {
      initialize();

      if (!isAuthenticated) navigate("/login", { replace: true });
      else {
        checkToken();
      }
    }
  }, [checkToken, initialize, isAuthenticated, isHydrated, navigate]);
  //#endregion

  return isReady ? (
    <Layout onLogout={handleLogout} name={session?.name}>
      <Outlet />
    </Layout>
  ) : (
    <div className="flex h-screen w-full items-center justify-center">
      <Icons.Loader />
    </div>
  );
});

export default Wrapper;
