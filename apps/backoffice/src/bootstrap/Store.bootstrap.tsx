import AuthStore from "@/modules/Auth/Store.auth";
import ThemeStore from "@/modules/Theme/Store.theme";
import { createContext, PropsWithChildren, useContext } from "react";

//#region ROOT STORE

export default class Store {
  auth: AuthStore;
  theme: ThemeStore;

  constructor() {
    this.auth = new AuthStore();
    this.theme = new ThemeStore();
  }
}

//#endregion

//#region CONTEXT

export const StoreContext = createContext({} as Store);

export function useStore(): Store {
  return useContext(StoreContext);
}

type Props = PropsWithChildren & { store: Store };
export const StoreProvider = ({ store, children }: Props) => (
  <StoreContext.Provider value={store}>{children}</StoreContext.Provider>
);

//#endregion
