import LocationService from "@/modules/Location/services";
import { Country } from "@/modules/Location/Types.location";
import { useEffect, useState } from "react";
import { URLSearchParamsInit, useSearchParams } from "react-router-dom";

/**
 * Save filters into url search params and vice-versa
 * @param filters filter object
 * @returns Parsed filter object
 */
export const useSaveFilter = <T>(filters?: object) => {
  const [params, setParams] = useSearchParams();
  const [values, setValues] = useState<T>();

  // Set filters object from url params
  useEffect(() => {
    const entries = [...params.entries()];
    const paramsToFilter = entries.reduce(
      (acc, [key, val]) => {
        acc[key] = val;
        return acc;
      },
      {} as Record<string, string>
    );

    setValues(paramsToFilter as T);
    // }
  }, [params]);

  // Set url params from filters object
  useEffect(() => {
    if (filters) {
      const filterToParams = [...Object.entries(filters)].reduce(
        (acc, [key, val]) => {
          if (val) acc[key] = val;
          return acc;
        },
        {} as Record<string, string>
      );
      setParams(filterToParams as URLSearchParamsInit);
    }
  }, [filters, setParams]);

  return values as T;
};

interface ParsedAddress {
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: Country;
  countryName?: string;
}
/**
 * Parse address components from google autocomplete
 * @param place autocomplete result
 * @param countries system country list
 * @returns Parsed address
 */
export const parseAddress = async (
  place?: google.maps.places.PlaceResult,
  isOnboarding = false
): Promise<ParsedAddress | undefined> => {
  if (!place) return;

  const _country = place?.address_components?.find((a) => a.types.includes("country"));
  let country: Country | undefined;
  let countryName: string | undefined;

  if (_country) {
    if (isOnboarding) {
      countryName = _country.long_name;
    } else {
      try {
        const data = await LocationService.getCountries({
          name: _country.long_name
        });
        country = data?.countries?.find((c) => c.name === _country?.long_name);
      } catch (error) {
        console.error("error", error);
      }
    }
  }

  const street = place?.name || "";
  const city = place?.address_components?.find((a) => a.types.includes("locality"))?.long_name || "";
  const state =
    place?.address_components?.find((a) => a.types.includes("administrative_area_level_1"))?.long_name || "";
  const zipCode = place?.address_components?.find((a) => a.types.includes("postal_code"))?.long_name || "";

  return {
    street,
    city,
    state,
    zipCode,
    country,
    countryName
  };
};
