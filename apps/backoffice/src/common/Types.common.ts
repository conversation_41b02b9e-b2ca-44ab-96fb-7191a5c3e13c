import { ReactElement } from "react";
import { RouteObject } from "react-router-dom";

export type RouterObject = Array<
  RouteObject & {
    permission?: string;
    children?: RouterObject;
  }
>;

export type NavObject = {
  label: string;
  path: string;
  icon?: ReactElement;
  pattern?: RouterObject;
  submenu?: NavObject[];
  permissions?: string[];
  action?: string;
};

export type BaseError = {
  message: string;
  error: string;
  statusCode: number;
};

// export type SelectOption = { label?: string; value: string | number };

//#region Common params
export type DetailParams = {
  id: string | number;
};
export type PaginationParams = {
  page: number;
  limit: number;
};
export type PaginationResponse = {
  skip: number;
  limit: number;
  total?: number;
};
export type OrderBy = "asc" | "desc";
export enum SortOrder {
  ASC = "asc",
  DESC = "desc"
}
export type SortParams<T> = {
  sortBy?: keyof T;
  sortOrder?: OrderBy;
};
export type ListParams<T> = Partial<T> & Partial<PaginationParams>;
// SortParams<T>;
//  & {
//   createdById?: number;
//   lastEditedById?: number;
//   createDateFrom?: string;
//   createDateTo?: string;
//   lastEditDateFrom?: string;
//   lastEditDateTo?: string;
// };
export type BatchParams<T> = {
  create?: T[];
  edit?: T[];
  delete?: number[];
};
//#endregion
