import { PropsWithChildren } from "react";
import Sidebar from "./Sidebar";

interface Props extends PropsWithChildren {
  name?: string;
  onLogout(): void;
}
const Layout = ({ name, onLogout, children }: Props) => {
  return (
    <div className="flex h-screen overflow-hidden bg-background dark:bg-neutral-800">
      <Sidebar name={name} onLogout={onLogout} />

      <div className="flex-1 overflow-y-auto overscroll-contain">
        <div>{children}</div>
      </div>
    </div>
  );
};
export default Layout;
