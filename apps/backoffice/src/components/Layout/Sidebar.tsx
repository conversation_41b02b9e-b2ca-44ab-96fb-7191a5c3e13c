import { NavObject } from "@/common/Types.common";
import NavigationItems from "@/config/Navigation.config";
import { DarkModeToggle } from "@/modules/Theme/components";
import { LogOut } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Dropdown, Icons } from "ui";
import { getNameInitials } from "utils";
import NavItem from "./NavItem";

type Props = { name?: string; onLogout(): void };
function Sidebar({ name, onLogout }: Props) {
  const navigate = useNavigate();
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const onItemClick = (item: NavObject, index: number) => {
    if (item.submenu) setActiveIndex((prevIndex) => (prevIndex === index ? null : index));
    else navigate(item.path);
  };

  return (
    <div className="flex flex-col h-full w-24 inset-y-0 start-0 z-20 bg-white border-e overflow-y-auto overscroll-contain overflow-x-hidden shadow border-gray-200 dark:bg-neutral-900 dark:border-neutral-700">
      <div className="min-h-16 flex items-center justify-center border-b">
        <a
          className="inline-block text-xl font-semibold focus:outline-none focus:opacity-80"
          aria-label="Slam"
        >
          <Icons.Logo
            className="w-16 fill-primary cursor-pointer dark:fill-white"
            onClick={() => navigate("/")}
          />
        </a>
      </div>

      <nav className="pb-6 pt-2 w-full flex flex-col flex-wrap">
        <ul className="space-y-3">
          {NavigationItems.map((item, index) => (
            <li key={index}>
              <NavItem item={item} isOpen={activeIndex === index} onClick={() => onItemClick(item, index)} />

              {item.submenu && (
                <ul>
                  {item.submenu.map((sub, subIndex) => (
                    <li key={subIndex}>
                      <NavItem
                        item={sub}
                        isOpen={activeIndex === subIndex}
                        isSubMenu
                        onClick={() => onItemClick(sub, subIndex)}
                      />
                    </li>
                  ))}
                </ul>
              )}
            </li>
          ))}
        </ul>
      </nav>

      <footer className="mt-auto mb-4 flex flex-col items-center gap-3">
        <DarkModeToggle iconOnly />

        {/* <LogOut size={16} onClick={onLogout} /> */}
        <Dropdown
          name="profile button"
          triggerElement={
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary dark:bg-neutral-800 font-medium text-white">
              {getNameInitials(name)}
            </div>
          }
          placement="top-start"
          containerStyle="z-30"
        >
          {/* TODO fix visibility */}
          <div className="p-2 flex gap-1 cursor-pointer dark:bg-neutral-800" onClick={onLogout}>
            <LogOut /> Sign Out
          </div>
        </Dropdown>
      </footer>
    </div>
  );
}
export default Sidebar;
