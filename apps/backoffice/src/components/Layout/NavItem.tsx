import { NavObject } from "@/common/Types.common";
import { matchRoutes, useLocation } from "react-router-dom";
import { twMerge } from "tailwind-merge";

type Props = {
  item: NavObject;
  isOpen?: boolean;
  isSubMenu?: boolean;
  onClick(): void;
};
function NavItem({ item, onClick }: Props) {
  const location = useLocation();
  const match = item.pattern ? matchRoutes(item.pattern, location.pathname) : undefined;

  return (
    <div
      className={twMerge(
        "flex flex-col gap-1 items-center font-medium text-neutral-700 hover:cursor-pointer dark:text-neutral-200",
        match && !item.submenu && ""
        // isSubMenu && "pl-16"
      )}
      onClick={onClick}
    >
      <div
        className={twMerge(
          "p-2 rounded-md hover:bg-neutral-100 dark:hover:bg-neutral-800",
          match && !item.submenu && "bg-neutral-100 text-primary dark:text-neutral-50 dark:bg-neutral-800"
        )}
      >
        {item.icon}
      </div>
      <div className={twMerge("text-xs text-center", match && !item.submenu && "font-semibold")}>
        {item.label}
      </div>
      {/* {item.submenu && (
        <ChevronDown
          className={twMerge(
            "ml-auto size-4 transition-transform duration-200",
            isOpen && "transform rotate-180"
          )}
        />
      )} */}
    </div>
    // <a
    //   className={twMerge(
    //     "flex items-center gap-x-3.5 py-2 pr-6 pl-8 text-sm font-medium text-neutral-700 bg-gradient-to-r hover:from-primary-50 hover:text-primary hover:cursor-pointer dark:text-neutral-200",
    //     match && !item.submenu && "text-primary from-primary-50",
    //     isSubMenu && "pl-16"
    //   )}
    //   onClick={onClick}
    // >
    //   {item.icon}
    //   {item.label}
    //   {item.submenu && (
    //     <ChevronDown
    //       className={twMerge(
    //         "ml-auto size-4 transition-transform duration-200",
    //         isOpen && "transform rotate-180"
    //       )}
    //     />
    //   )}
    // </a>
  );
}
export default NavItem;
