import { http } from "@/bootstrap/Http.bootstrap";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import {
  DocumentType,
  GetDocumentTypeList,
  GetDocumentTypeListResponse,
  SaveDocumentTypeParams
} from "../Types.document";

const DOCUMENT_TYPE_ROUTE = "document-types";
/**
 * Get document type list
 *
 * @returns
 */
async function list(params?: GetDocumentTypeList): Promise<GetDocumentTypeListResponse> {
  try {
    return await http.get(stringifyQueryParams(DOCUMENT_TYPE_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get document type detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<DocumentType> {
  try {
    return await http.get(`${DOCUMENT_TYPE_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create document type
 *
 * @returns
 */
async function create(params: SaveDocumentTypeParams): Promise<DocumentType> {
  try {
    return await http.post(DOCUMENT_TYPE_ROUTE, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit document type
 *
 * @returns
 */
async function edit({ id, ...params }: SaveDocumentTypeParams): Promise<DocumentType> {
  try {
    return await http.put(`${DOCUMENT_TYPE_ROUTE}/${id}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Delete document type
 *
 * @returns
 */
async function remove({ id }: DetailParams): Promise<void> {
  try {
    return await http.delete(`${DOCUMENT_TYPE_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}

export default { create, edit, get, list, remove };
