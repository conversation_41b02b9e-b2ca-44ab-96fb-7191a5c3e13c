import { http } from "@/bootstrap/Http.bootstrap";
import { handleError } from "utils";
import { BatchSaveDocumentTypeFieldParams } from "../Types.document";

const DOCUMENT_TYPE_ROUTE = "document-types";

/**
 * Batch save document type field
 *
 * @returns
 */
async function batchSave({ id, ...params }: BatchSaveDocumentTypeFieldParams): Promise<DocumentType> {
  try {
    return await http.post(`${DOCUMENT_TYPE_ROUTE}/${id}/fields/batch`, params);
  } catch (error) {
    throw await handleError(error);
  }
}

export default { batchSave };
