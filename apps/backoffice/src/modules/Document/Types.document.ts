import {
  BatchUpdateDocumentTypeFieldsDto,
  CreateDocumentType<PERSON>ieldDto,
  DocumentType as DocType,
  DocumentTypeField as DocTypeField,
  EditDocumentTypeDto,
  GetDocumentTypeFieldsDto,
  GetDocumentTypeFieldsResponseDto,
  GetDocumentTypesDto,
  GetDocumentTypesResponseDto
} from "nest-modules";
import { DetailParams, ListParams, PaginationResponse } from "utils";

export enum DocumentFieldDataType {
  STRING = "string",
  NUMBER = "number",
  BOOLEAN = "boolean",
  // DATETIME = "datetime",
  OBJECT = "object",
  ARRAY = "array"
}

export type DocumentType = DocType;
export type DocumentTypeField = DocTypeField;

export type GetDocumentTypeList = ListParams<GetDocumentTypesDto>;
export type GetDocumentTypeListResponse = PaginationResponse & GetDocumentTypesResponseDto;

export type SaveDocumentTypeParams = EditDocumentTypeDto & DetailParams;

export type GetDocumentTypeFieldList = ListParams<GetDocumentTypeFieldsDto>;
export type GetDocumentTypeFieldListResponse = PaginationResponse & GetDocumentTypeFieldsResponseDto;

export type SaveDocumentTypeFieldParams = CreateDocumentTypeFieldDto & DetailParams;

export type BatchSaveDocumentTypeFieldParams = BatchUpdateDocumentTypeFieldsDto & DetailParams;

export type DocumentTypeFormParams = SaveDocumentTypeParams & {
  fields: SaveDocumentTypeFieldParams[];
  deletedIds?: number[];
};
