import { RouterObject } from "@/common/Types.common";
import { BookMarked } from "lucide-react";
import { DocumentTypeList } from "./views";

const DocumentPages = {
  List: "/document",
  Type: "/document-type"
  // Detail: (shipment_id?: string) => `${DocumentPages.List}/${shipment_id}`,
  // Create: "create",
};
const DocumentNavigation = {
  label: "Document Types",
  icon: <BookMarked className="size-5" />,
  pattern: [{ path: `${DocumentPages.Type}/*` }],
  path: DocumentPages.Type
};
const DocumentRoutes: RouterObject = [
  {
    path: DocumentPages.Type,
    element: <DocumentTypeList />,
    children: [
      // { path: "", element: <DocumentTypeList /> },
      // {
      //   path: DocumentPages.Detail(":shipment_id"),
      //   element: <ShipmentDetail />,
      // },
      // {
      //   path: DocumentPages.Create,
      //   element: <CreateShipment />,
      // },
    ]
  }
];

export { DocumentNavigation, DocumentPages, DocumentRoutes };
