import { TableSchema } from "utils";
import { array, boolean, mixed, number, object, ObjectSchema, string } from "yup";
import { DocumentFieldDataType, DocumentType, DocumentTypeFormParams } from "./Types.document";

export const documentTypeTableSchema: TableSchema<DocumentType> = {
  name: {
    header: "Name",
    style: "w-fit",
    visible: true
  },
  promptTemplate: {
    header: "Prompt Template",
    style: "whitespace-normal",
    visible: true
  }
};

export const saveDocumentTypeSchema: ObjectSchema<DocumentTypeFormParams> = object({
  id: number().optional(),
  name: string().required("Name is required"),
  promptTemplate: string().required("Prompt template is required"),
  fields: array(
    object({
      id: number().optional(),
      name: string().required("Name is required"),
      dataType: mixed<DocumentFieldDataType>().required("Data type is required"),
      isMandatory: boolean().defined()
    })
  ).defined(),
  deletedIds: array(number().required("Deleted id is required")).optional()
});
