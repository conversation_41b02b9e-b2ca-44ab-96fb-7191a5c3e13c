import { useMutation } from "@tanstack/react-query";
import DocumentService from "../services";
import { BatchSaveDocumentTypeFieldParams } from "../Types.document";

const useBatchSaveDocumentTypeField = () =>
  useMutation({
    mutationFn: async (params: BatchSaveDocumentTypeFieldParams) =>
      await DocumentService.DocumentTypeField.batchSave(params)
  });

export { useBatchSaveDocumentTypeField };
