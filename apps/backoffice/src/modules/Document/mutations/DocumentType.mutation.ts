import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import DocumentService from "../services";
import { SaveDocumentTypeParams } from "../Types.document";

const useSaveDocumentType = () =>
  useMutation({
    mutationFn: async (params: SaveDocumentTypeParams) => {
      if (params.id) {
        return await DocumentService.DocumentType.edit(params);
      }
      return await DocumentService.DocumentType.create(params);
    }
  });

const useDeleteDocumentType = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await DocumentService.DocumentType.remove(params)
  });

export { useDeleteDocumentType, useSaveDocumentType };
