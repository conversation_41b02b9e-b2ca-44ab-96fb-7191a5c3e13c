import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { BaseError } from "@/common/Types.common";
import { useCallback, useEffect, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { <PERSON><PERSON>, Header, Pagination, Table } from "ui";
import { AddDocumentTypeModal, DocumentFilter } from "../../components";
import { useBatchSaveDocumentTypeField, useSaveDocumentType } from "../../mutations";
import { useGetDocumentTypeList } from "../../queries";
import { documentTypeTableSchema } from "../../Schema.document";
import {
  BatchSaveDocumentTypeFieldParams,
  DocumentType,
  DocumentTypeFormParams,
  GetDocumentTypeList
} from "../../Types.document";

function DocumentTypeList() {
  const [modal, toggleModal] = useReducer((r) => !r, false);
  const [documentType, setDocumentType] = useState<DocumentType>();
  const [page, setPage] = useState(1);
  const [filter, toggleFilter] = useReducer((r) => !r, false);
  const [filters, setFilters] = useState<GetDocumentTypeList>();

  const saveDocumentType = useSaveDocumentType();
  const saveDocumentField = useBatchSaveDocumentTypeField();

  const { data, error, isFetching, refetch } = useGetDocumentTypeList({
    page,
    limit: DEFAULT_LIMIT,
    ...filters
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const handleModal = (_data?: DocumentType) => {
    setDocumentType(_data);
    toggleModal();
  };

  const handleSaveDocumentType = useCallback(
    async ({ fields, deletedIds, ...params }: DocumentTypeFormParams) => {
      try {
        const document = await saveDocumentType.mutateAsync(params);

        const formatParams: BatchSaveDocumentTypeFieldParams = {
          id: params.id ?? document.id,
          // TODO create resable batch formatting
          ...(fields?.length > 0 && {
            create: fields.filter((f) => !f.id),
            edit: fields.filter((f) => typeof f.id === "number").map((f) => ({ ...f, id: f.id as number }))
          }),
          ...(deletedIds && deletedIds.length > 0 && { delete: deletedIds })
        };

        await saveDocumentField.mutateAsync(formatParams);

        const message = `Document Type ${params.name} ${documentType ? "updated" : "created"} successfully`;
        toast.success(message);

        handleModal();
        refetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [documentType, refetch, saveDocumentField, saveDocumentType]
  );

  const handleFilter = useCallback((values: GetDocumentTypeList) => {
    setFilters(values);
    setPage(1);
  }, []);

  return (
    <div className="flex flex-col">
      <AddDocumentTypeModal
        show={modal}
        info={documentType}
        isLoading={saveDocumentType.isPending || saveDocumentField.isPending}
        onClose={handleModal}
        onSubmit={handleSaveDocumentType}
      />

      <Header title="Document Type List">
        <Button label="Filters" kind="filter" onClick={toggleFilter} />
        <Button
          label="New Document Type"
          kind="create"
          onClick={handleModal}
          disabled={saveDocumentType.isPending || saveDocumentField.isPending}
        />
      </Header>

      <section>
        <DocumentFilter filterValues={handleFilter} isOpen={filter} />
      </section>

      <main>
        <Table
          data={data?.documentTypes}
          isLoading={isFetching}
          schema={documentTypeTableSchema}
          onClick={handleModal}
          footer={
            <Pagination currentPage={page} total={data?.total} limit={DEFAULT_LIMIT} onPageChange={setPage} />
          }
          total={data?.total}
        />
      </main>
    </div>
  );
}
export default DocumentTypeList;
