import { ORDER_BY } from "@/common/Constant.common";
import { useDebounce } from "@uidotdev/usehooks";
import { useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";
import { Card, Input, Select } from "ui";
import { OrderBy } from "utils";
import { DOCUMENT_TYPE_SORT_BY } from "../../Constant.document";
import { documentTypeTableSchema } from "../../Schema.document";
import { GetDocumentTypeList } from "../../Types.document";

type Props = {
  isOpen: boolean;
  filterValues(values?: GetDocumentTypeList): void;
};
const DocumentFilter = ({ isOpen, filterValues }: Props) => {
  const [values, setValues] = useState<GetDocumentTypeList>();

  const [_name, setName] = useState<string>();
  const [_prompt, setPrompt] = useState<string>();
  const name = useDebounce(_name, 500);
  const promptTemplate = useDebounce(_prompt, 500);

  useEffect(() => {
    setValues((prev) => ({
      ...prev,
      name: name ?? undefined,
      promptTemplate: promptTemplate ?? undefined
    }));
  }, [name, promptTemplate]);

  useEffect(() => {
    filterValues(values);
  }, [filterValues, values]);

  useEffect(() => () => setValues(undefined), []);

  return (
    <div
      className={twMerge(
        "overflow-hidden transition-max-height duration-300 ease-in-out",
        isOpen ? "max-h-96" : "max-h-0"
      )}
    >
      {isOpen && (
        <Card containerStyle="flex flex-wrap items-center gap-x-3 gap-y-2 px-6 py-4 border">
          <div className="flex gap-3">
            <Input label="Name" placeholder="Search" onTextChange={setName} kind="search" />
            <Input label="Prompt Template" placeholder="Search" onTextChange={setPrompt} kind="search" />
          </div>

          <div className="flex gap-3">
            <Select
              label="Sort By"
              onSelected={(sortBy: keyof typeof documentTypeTableSchema) =>
                setValues((prev) => ({ ...prev, sortBy }) as GetDocumentTypeList)
              }
              options={DOCUMENT_TYPE_SORT_BY}
              optional
            />
            <Select
              label="Sort Direction"
              onSelected={(sortOrder: OrderBy) =>
                setValues((prev) => ({ ...prev, sortOrder }) as GetDocumentTypeList)
              }
              options={ORDER_BY}
              optional
            />
          </div>
        </Card>
      )}
    </div>
  );
};
export default DocumentFilter;
