import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ikP<PERSON><PERSON>, useFormik } from "formik";
import { PlusIcon, TrashIcon } from "lucide-react";
import { Button, Checkbox, Input, Modal, Select, Textarea } from "ui";
import { formikInputOpts } from "utils";
import { DATA_TYPE } from "../../Constant.document";
import { saveDocumentTypeSchema } from "../../Schema.document";
import { DocumentType, DocumentTypeFormParams } from "../../Types.document";

type Props = {
  show: boolean;
  info?: DocumentType;
  isLoading?: boolean;
  onSubmit(params: DocumentTypeFormParams): void;
  onClose(): void;
};
function AddDocumentTypeModal({ show, info, isLoading, onSubmit, onClose }: Props) {
  const formik = useFormik({
    initialValues: {
      id: info?.id,
      name: info?.name || "",
      promptTemplate: info?.promptTemplate || "",
      fields: info?.fields || [],
      deletedIds: []
    },
    validationSchema: saveDocumentTypeSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit: onSubmit
  });

  return (
    <Modal
      id="document-type-modal"
      show={show}
      onClose={() => {
        formik.resetForm();
        onClose();
      }}
      size="lg"
      title={`${info ? "Edit" : "Add New"} Document Type`}
      actions={
        <Button
          label="Submit"
          kind="update"
          loading={isLoading}
          onClick={formik.handleSubmit}
          className="mt-2"
        />
      }
    >
      <form className="flex flex-col gap-3 px-2">
        <Input label="Name" placeholder="Input name" {...formikInputOpts(formik, "name")} />
        <Textarea
          label="Prompt Template"
          placeholder="Input prompt template"
          rows={6}
          {...formikInputOpts(formik, "promptTemplate")}
        />

        <FormikProvider value={formik}>
          <FieldArray
            name="fields"
            render={(arrayHelpers) => (
              <div className="flex flex-col gap-2">
                <div className="col-span-2 flex items-center justify-between gap-2">
                  <span>Fields</span>
                  <Button
                    kind="outline"
                    label="Add"
                    icon={<PlusIcon className="size-4" />}
                    className="py-1 px-2"
                    onClick={() =>
                      arrayHelpers.push({
                        name: "",
                        dataType: "",
                        isMandatory: false,
                        description: ""
                      })
                    }
                  />
                </div>

                {formik.values.fields.map((field, index) => (
                  <div className="flex gap-3" key={index}>
                    <Input
                      label="Name"
                      placeholder="Input field name"
                      {...formikInputOpts(formik, `fields.${index}.name`)}
                    />
                    <Select
                      label="Data Type"
                      options={DATA_TYPE}
                      {...formikInputOpts(formik, `fields.${index}.dataType`)}
                      optional
                    />
                    <Input
                      label="Description"
                      placeholder="Input description"
                      {...formikInputOpts(formik, `fields.${index}.description`)}
                    />
                    <Checkbox
                      label="Required"
                      name={`fields.${index}.isMandatory`}
                      containerStyle="mt-6"
                      checked={field.isMandatory ?? false}
                      onChange={formik.handleChange}
                    />
                    <TrashIcon
                      className="mt-6 ml-auto size-5 cursor-pointer text-danger"
                      onClick={() => {
                        const id = field.id;
                        if (id) {
                          formik.setFieldValue("deletedIds", [...formik.values.deletedIds, id]);
                        }
                        arrayHelpers.remove(index);
                      }}
                    />
                  </div>
                ))}
              </div>
            )}
          />
        </FormikProvider>
      </form>
    </Modal>
  );
}
export default AddDocumentTypeModal;
