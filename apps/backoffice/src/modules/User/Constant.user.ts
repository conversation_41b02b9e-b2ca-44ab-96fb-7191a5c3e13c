import { enumToSelectOptions } from "utils";
import { UserPermission } from "./Types.user";
import { OrganizationType } from "../Organization/Types.organization";

export const USER_PERMISSION = (organizationType?: OrganizationType) =>
  organizationType !== OrganizationType.BACKOFFICE
    ? enumToSelectOptions(UserPermission).filter((o) => o.value !== UserPermission.BACKOFFICE_ADMIN)
    : enumToSelectOptions(UserPermission).filter((o) => o.value === UserPermission.BACKOFFICE_ADMIN);
