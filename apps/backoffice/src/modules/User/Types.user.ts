import type { <PERSON><PERSON><PERSON>ser<PERSON><PERSON>, EditUserDto, GetUsersDto, GetUsersResponseDto, User as Usr } from "nest-modules";
import type { DetailParams, ListParams, PaginationResponse } from "utils";

export enum UserPermission {
  BACKOFFICE_ADMIN = "backoffice-admin",
  ORGANIZATION_ADMIN = "organization-admin",
  BASIC = "basic"
}

export type User = Usr;

export type GetUserList = ListParams<GetUsersDto>;
export type GetUserListResponse = PaginationResponse & GetUsersResponseDto;

export type SaveUserParams = (CreateUserDto | EditUserDto) & DetailParams;
