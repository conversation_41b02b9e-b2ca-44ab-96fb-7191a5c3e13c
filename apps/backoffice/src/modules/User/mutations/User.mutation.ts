import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import UserService from "../services";
import { SaveUserParams } from "../Types.user";

const useSaveUser = () =>
  useMutation({
    mutationFn: async (params: SaveUserParams) => {
      if (params.id) {
        return await UserService.edit(params);
      }
      return await UserService.create(params);
    }
  });

const useDeleteUser = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await UserService.remove(params)
  });

export { useDeleteUser, useSaveUser };
