import { useQuery } from "@tanstack/react-query";

import UserService from "../services";
import type { GetUserList, GetUserListResponse } from "../Types.user";

export const getUsersKey = "getUsers";
// Reusable queries for get users
export const useGetUsers = <R = GetUserListResponse>(
  params?: GetUserList,
  select?: (data: GetUserListResponse) => R,
  refetchOnMount = false
) =>
  useQuery({
    queryKey: [getUsersKey, params],
    queryFn: () => UserService.list(params),
    select,
    refetchOnMount,
    enabled: !!params?.organizationId
  });
// Get user list with refetch on mount
export const useGetUserList = (params?: GetUserList) => useGetUsers(params, undefined, true);
