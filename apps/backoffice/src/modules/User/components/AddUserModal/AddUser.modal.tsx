import { useFormik } from "formik";
import { Button, Input, Modal, Select } from "ui";
import { USER_PERMISSION } from "../../Constant.user";
import { saveUserSchema } from "../../Schema.user";
import { SaveUserParams, User } from "../../Types.user";
import { Organization } from "@/modules/Organization/Types.organization";

type Props = {
  show: boolean;
  info?: User;
  isLoading?: boolean;
  organization?: Organization;
  onSubmit(params: SaveUserParams): void;
  onClose(): void;
};
function AddUserModal({ show, info, isLoading, organization, onSubmit, onClose }: Props) {
  const formik = useFormik({
    initialValues: {
      id: info?.id,
      name: info?.name || "",
      email: info?.email || "",
      permission: info?.permission,
      password: ""
    },
    validationSchema: saveUserSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit: onSubmit
  });

  return (
    <Modal
      id="user-modal"
      show={show}
      onClose={() => {
        formik.resetForm();
        onClose();
      }}
      title={`${info ? "Edit" : "Add New"} User`}
    >
      <div className="flex flex-col gap-3 px-2">
        <Input
          label="Name"
          name="name"
          placeholder="Input name"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.name}
          error={formik.errors.name && formik.touched.name ? formik.errors.name : undefined}
        />
        <Input
          label="Email"
          name="email"
          type="email"
          placeholder="Input email"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.email}
          error={formik.errors.email && formik.touched.email ? formik.errors.email : undefined}
        />
        <Select
          label="Permission"
          name="permission"
          options={USER_PERMISSION(organization?.organizationType)}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.permission}
          error={formik.errors.permission && formik.touched.permission ? formik.errors.permission : undefined}
          optional
        />
        <Input
          label="Password"
          name="password"
          type="password"
          placeholder="Input password"
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          value={formik.values.password}
          error={formik.errors.password && formik.touched.password ? formik.errors.password : undefined}
        />

        <Button label="Submit" loading={isLoading} onClick={formik.handleSubmit} className="mt-2" />
      </div>
    </Modal>
  );
}
export default AddUserModal;
