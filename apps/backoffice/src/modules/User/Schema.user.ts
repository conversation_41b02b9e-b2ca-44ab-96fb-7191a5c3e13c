import { UserPermission } from "nest-modules";
import { TableSchema } from "utils";
import { mixed, number, object, ObjectSchema, string } from "yup";
import type { SaveUserParams, User } from "./Types.user";

export const userTableSchema: TableSchema<User> = {
  name: {
    header: "Name",
    style: "w-fit",
    visible: true
  },
  email: {
    header: "Email",
    visible: true
  },
  permission: {
    header: "Permission",
    renderer: "kebabCase",
    visible: true
  }
};

export const saveUserSchema: ObjectSchema<SaveUserParams> = object({
  id: number().optional(),
  name: string().required("Name is required"),
  email: string().required("Email is required").email("Email is invalid"),
  googleUserId: string().optional(),
  permission: mixed<UserPermission>().required("Permission is required"),
  password: string().when("id", {
    is: (value: number | undefined) => value !== undefined,
    then: () => string().optional(),
    otherwise: () => string().required("Password is required")
  }),
  organizationId: number().optional()
});
