import { useInfiniteQuery } from "@tanstack/react-query";
import { GetTradePartnersDto } from "nest-modules";
import { flattenQueryResults, getAuditNames, infiniteQueryMapper, ListParams } from "utils";
import PartnerService from "../services";
import { TradePartner } from "../Types.partner";

export const getPartnerDto = (partner: TradePartner) => ({
  ...partner,
  countryName: partner.country?.name || "",
  organizationName: partner.organization?.name || "",
  ...getAuditNames(partner),
  compute: partner.compute || null
});

export const getPartnersKey = "getPartners";

export const useGetInfinitePartners = (params?: ListParams<GetTradePartnersDto>, refetchOnMount = false) => {
  const result = useInfiniteQuery({
    queryKey: [getPartnersKey, params],
    queryFn: ({ pageParam }) => PartnerService.list({ ...params, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: infiniteQueryMapper,
    refetchOnMount,
    select: (data) => flattenQueryResults("partners", data.pages).map(getPartnerDto)
  });

  return result;
};
