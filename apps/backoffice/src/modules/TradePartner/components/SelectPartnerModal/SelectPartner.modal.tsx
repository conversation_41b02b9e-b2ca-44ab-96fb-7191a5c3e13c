import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { useState } from "react";
import { Button, Modal, Table } from "ui";
import { SortOrder } from "utils";
import { tradePartnerTableSchema } from "../../Schema.partner";
import { GetPartnerList, PartnerType, TradePartner, TradePartnerColumn } from "../../Types.partner";
import { useGetInfinitePartners } from "../../queries";
// import TradePartnerFilter from "../TradePartnerFilter";

type Props = {
  show: boolean;
  partnerType?: PartnerType;
  required?: boolean;
  multiple?: boolean;
  onSelect(item?: TradePartner): void;
  onClose(): void;
};
function SelectPartnerModal({ show, partnerType, required, multiple, onSelect, onClose }: Props) {
  const [selected, setSelected] = useState<TradePartner>();
  const [filters, setFilters] = useState<GetPartnerList>();

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } = useGetInfinitePartners(
    {
      partnerType,
      limit: DEFAULT_LIMIT,
      sortBy: TradePartnerColumn.createDate,
      sortOrder: SortOrder.DESC,
      ...filters
    },
    true
  );

  const handleSelect = () => {
    if (!multiple) onSelect(selected);
  };

  return (
    <Modal
      id="select-partner-modal"
      show={show}
      onClose={onClose}
      title="Select Partner"
      size="4xl"
      actions={
        <Button
          label={required ? "Select" : selected ? "Select" : "Clear"}
          onClick={handleSelect}
          disabled={!data?.length || (required && !selected)}
        />
      }
    >
      <div className="flex flex-col gap-4">
        {/* <TradePartnerFilter filterValues={setFilters} type={partnerType} /> */}
        <Table
          data={data}
          schema={tradePartnerTableSchema}
          checklist
          onClick={setSelected}
          isLoading={isLoading}
          onEndReached={fetchNextPage}
          hasNextPage={hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
          onSort={(key?: string, direction?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key as TradePartnerColumn,
              sortOrder: direction
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
        />
      </div>
    </Modal>
  );
}
export default SelectPartnerModal;
