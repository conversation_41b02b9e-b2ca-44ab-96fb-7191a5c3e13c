import { http } from "@/bootstrap/Http.bootstrap";
import { handleError, stringifyQueryParams } from "utils";
import type { GetPartnerList, GetPartnerListResponse } from "../Types.partner";

const PARTNER_ROUTE = "trade-partners";
/**
 * Get all trade partner
 *
 * @returns
 */
async function list(params?: GetPartnerList): Promise<GetPartnerListResponse> {
  try {
    return await http.get(stringifyQueryParams(PARTNER_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}

export { list };
