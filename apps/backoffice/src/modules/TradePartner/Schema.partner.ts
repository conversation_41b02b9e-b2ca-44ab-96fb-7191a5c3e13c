import { auditTableSchema, TableSchema } from "utils";
import { TradePartnerTable } from "./Types.partner";

export const tradePartnerTableSchema: TableSchema<TradePartnerTable> = {
  name: {
    header: "Name",
    style: "max-w-80 whitespace-normal",
    visible: true
  },
  partnerType: {
    header: "Type",
    style: "uppercase",
    visible: true
  },
  email: {
    header: "Email",
    visible: true
  },
  phoneNumber: {
    header: "Phone",
    visible: true
  },
  address: {
    header: "Address",
    style: "max-w-80 whitespace-normal",
    visible: true
  },
  city: {
    header: "City",
    visible: true
  },
  countryName: {
    header: "Country",
    visible: true,
    sortKey: "countryId"
  },
  organizationName: {
    header: "Organization",
    visible: true,
    sortKey: "organizationId"
  },
  ...auditTableSchema
};
