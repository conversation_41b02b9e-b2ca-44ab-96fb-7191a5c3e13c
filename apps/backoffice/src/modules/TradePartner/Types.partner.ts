import { GetTradePartnersDto, GetTradePartnersResponseDto, TradePartner as TP } from "nest-modules";
import { ListParams, PaginationResponse, WithAuditNames } from "utils";

export enum TradePartnerColumn {
  id = "id",
  partnerType = "partnerType",
  vendorCode = "vendorCode",
  name = "name",
  email = "email",
  phoneNumber = "phoneNumber",
  address = "address",
  city = "city",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  countryId = "countryId",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}
export enum PartnerType {
  VENDOR = "vendor",
  TRUCKER = "trucker",
  IMPORTER = "importer",
  SHIPPER = "shipper",
  AIR_CARRIER = "air-carrier",
  CUSTOMS_BROKER = "customs-broker",
  FORWARDER = "forwarder",
  OCEAN_CARRIER = "ocean-carrier",
  RAIL_COMPANY = "rail-company",
  TERMINAL = "terminal",
  WAREHOUSE = "warehouse",
  MANUFACTURER = "manufacturer"
}

export type TradePartner = TP;

export type TradePartnerTable = TradePartner &
  WithAuditNames & {
    countryName?: string;
    organizationName?: string;
  };

export type GetPartnerList = ListParams<GetTradePartnersDto>;
export type GetPartnerListResponse = PaginationResponse & GetTradePartnersResponseDto;
