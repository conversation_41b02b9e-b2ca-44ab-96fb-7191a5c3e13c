import { useInfiniteQuery } from "@tanstack/react-query";
import { flattenQueryResults, infiniteQueryMapper } from "utils";
import type { GetCountries } from "../Types.location";
import LocationService from "../services";

export const getCountriesKey = "getCountries";

export const useGetInfiniteCountries = (
  params?: GetCountries,
  options?: {
    refetchOnMount?: boolean;
    enabled?: boolean;
  }
) => {
  const result = useInfiniteQuery({
    queryKey: [getCountriesKey, params],
    queryFn: ({ pageParam }) => LocationService.getCountries({ ...params, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: infiniteQueryMapper,
    refetchOnMount: false,
    ...options,
    select: (data) =>
      flattenQueryResults("countries", data.pages).map((v) => ({
        label: `${v.name} (${v.alpha2})`,
        value: v.id
      }))
  });

  return result;
};
