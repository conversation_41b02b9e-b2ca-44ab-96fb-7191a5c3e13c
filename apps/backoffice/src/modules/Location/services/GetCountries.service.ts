import { http } from "@/bootstrap/Http.bootstrap";
import { handleError, stringifyQueryParams } from "utils";
import type { GetCountries, GetCountriesResponse } from "../Types.location";

/**
 * Get countries
 *
 * @returns
 */
async function getCountries(params?: GetCountries): Promise<GetCountriesResponse> {
  try {
    return await http.get(stringifyQueryParams("countries", params));
  } catch (error) {
    throw await handleError(error);
  }
}

export default getCountries;
