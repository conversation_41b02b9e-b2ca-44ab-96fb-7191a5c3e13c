import { http } from "@/bootstrap/Http.bootstrap";
import { handleError } from "utils";
import type { LoginParams, LogoutParams, RefreshTokenParams, Session } from "../Types.auth";

/**
 * Login.
 *
 * @returns
 */
async function login(params: LoginParams): Promise<Session> {
  try {
    return await http.post("auth/login", params);
  } catch (error) {
    throw await handleError(error);
  }
}

/**
 * Refresh Token.
 *
 * @returns
 */
async function refreshToken({ refreshToken }: RefreshTokenParams): Promise<Session> {
  try {
    return await http.post("auth/token", { refreshToken });
  } catch (error) {
    throw await handleError(error);
  }
}

/**
 * Logout.
 *
 * @returns
 */
async function logout(params: LogoutParams): Promise<void> {
  try {
    return await http.post("auth/logout", params);
  } catch (error) {
    throw await handleError(error);
  }
}

export { login, logout, refreshToken };
