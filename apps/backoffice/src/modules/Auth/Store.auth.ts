import { http } from "@/bootstrap/Http.bootstrap";
import { makeAutoObservable, runInAction } from "mobx";
import AuthService from "./services";

import { BaseError } from "@/common/Types.common";
import { clearPersistedStore, isHydrated, makePersistable } from "mobx-persist-store";
import type { LoginParams, Session } from "./Types.auth";

class AuthStore {
  session?: Session;
  isLoading = false;
  error?: BaseError;
  isReady = false;

  constructor() {
    makeAutoObservable(this);

    makePersistable(this, {
      name: "AuthStoreCustomsAdmin",
      properties: ["session"],
      storage: localStorage
    });
  }

  login = async (params: LoginParams) => {
    this.clear();
    this.isLoading = true;

    try {
      const _login = await AuthService.login(params);

      this.setSession(_login);
      this.setHeaders(_login.accessToken);
    } catch (error) {
      this.session = undefined;

      this.setError(error as BaseError);
    } finally {
      this.isLoading = false;
    }
  };

  // signup = async (params: SignupParams) => {
  //   this.clear();
  //   this.isLoading = true;

  //   try {
  //     const _signup = await AuthService.signup(params);

  //     this.setSession(_signup);
  //     this.setHeaders(_signup.accessToken);
  //   } catch (error) {
  //     this.session = undefined;

  //     this.setError(error as BaseError);
  //   } finally {
  //     this.isLoading = false;
  //   }
  // };

  refreshToken = async () => {
    this.clear();
    this.isLoading = true;

    const token = this.session?.refreshToken;

    try {
      if (token) {
        const refresh = await AuthService.refreshToken({
          refreshToken: token
        });

        this.setSession(refresh);
        this.setHeaders(refresh.accessToken);
      }
    } catch (error) {
      this.setError(error as BaseError);
    } finally {
      this.isLoading = false;
    }
  };

  // getUser = async () => {
  //   if (this.session) {
  //     try {
  //       const user = await UserService.me();
  //       const actions = await UserService.getPermittedActions();

  //       const session: Session = {
  //         ...this.session,
  //         permission: user.permission,
  //         permittedActions: actions.permittedActions,
  //       };
  //       this.setSession(session);
  //     } catch (error) {
  //       // this.setError(error as BaseError);
  //     }
  //   }
  // };

  logout = async () => {
    const token = this.session?.refreshToken;
    this.isLoading = true;

    try {
      if (token) {
        await AuthService.logout({
          refreshToken: token
        });
      }
    } catch (error) {
      this.setError(error as BaseError);
      this.clearPersistedData();
    } finally {
      runInAction(() => {
        this.session = undefined;
        this.isReady = false;
        this.isLoading = false;
      });
      this.clearHeaders();
      this.clear();
    }
  };

  initialize = () => {
    if (this.session && this.session.accessToken) {
      this.setHeaders(this.session.accessToken);
      runInAction(() => {
        this.isReady = true;
      });
    } else {
      this.logout();
    }
  };

  clear = () => {
    this.isLoading = false;
    this.error = undefined;
  };

  setError = (error: BaseError) => {
    runInAction(() => {
      this.error = error;
    });
  };
  setSession = (session: Session) => {
    runInAction(() => {
      this.session = session;
    });
  };

  setHeaders = (token?: string) => {
    http.updateConfig({
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
  };
  clearHeaders = () => {
    http.updateConfig({
      headers: {
        Authorization: ""
      }
    });
  };

  get isAuthenticated(): boolean {
    return this.session !== undefined && this.session.accessToken !== "";
  }

  get isHydrated(): boolean {
    return isHydrated(this);
  }
  clearPersistedData = async (): Promise<void> => {
    await clearPersistedStore(this);
  };
}

export default AuthStore;
