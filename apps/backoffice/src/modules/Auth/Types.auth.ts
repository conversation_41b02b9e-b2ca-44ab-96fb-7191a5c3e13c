import { CreateUserDto, LoginUserDto, RefreshAccessTokenResponseDto } from "nest-modules";

export enum LoginMethod {
  GOOGLE_SSO = "google-sso",
  EMAIL = "email"
}
export type Session = RefreshAccessTokenResponseDto;

// export type CsrfResponse = Pick<Session, 'csrfToken'>;
export type LoginParams = LoginUserDto;
export type SignupParams = CreateUserDto & {
  confirmPassword: string;
};
// export type LoginParams = any;
export type RefreshTokenParams = Pick<Session, "refreshToken">;
export type LogoutParams = RefreshTokenParams;

// export const EMAIL = LoginMethod.EMAIL;
