import { Link } from "react-router-dom";
import { Button, Input } from "ui";
import { AuthPages } from "../../Routes.auth";

const Signup = () => {
  // const { signup, isLoading, isAuthenticated, error } = useStore().auth;
  // const navigate = useNavigate();

  // const handleSignup = (params: SignupParams) => {
  //   signup(params);
  // };

  // useEffect(() => {
  //   if (isAuthenticated) navigate("/");

  //   if (error) {
  //     toast.error(`Signup failed: ${error.message}`);
  //   }
  // }, [error, navigate, isAuthenticated]);

  // const formik = useFormik({
  //   initialValues: {
  //     email: "",
  //     name: "",
  //     password: "",
  //     permission: UserPermission.BASIC,
  //     confirmPassword: "",
  //   },
  //   validationSchema: signupSchema,
  //   validateOnBlur: true,
  //   onSubmit: handleSignup,
  // });

  return (
    <div className="flex items-center justify-center h-screen bg-background dark:bg-primary-600">
      <div className="flex flex-col gap-5 p-8 items-center max-w-sm w-full rounded-md shadow-lg bg-white dark:bg-dark dark:text-white">
        <div className="text-center">
          <h3>Sign Up</h3>
          <p className="inline-flex gap-1 mt-2 text-sm text-gray-600 dark:text-neutral-400">
            Already have an account?
            <Link
              to={AuthPages.Login}
              className="text-primary-600 decoration-2 hover:underline font-medium dark:text-primary-100 cursor-pointer"
            >
              Sign in here
            </Link>
          </p>
        </div>

        {/* <GoogleLogin
          onSuccess={(credentialResponse) => {
            handleGoogleLogin(credentialResponse);
          }}
          onError={() => {
            alert("Login failed!");
            // toast.error(`Login failed`);
          }}
          text="signin_with"
        /> */}

        {/* <div className="w-full py-3 flex items-center text-xs text-neutral-500 uppercase before:flex-1 before:border-t before:border-neutral-300 before:me-4 after:flex-1 after:border-t after:border-neutral-300 after:ms-4 dark:text-neutral-500 dark:before:border-neutral-600 dark:after:border-neutral-600">
          Or
        </div> */}

        {/* <span className="text-xs text-neutral-600">
          Or sign in with credentials
        </span> */}

        <div className="flex flex-col gap-4 w-full">
          <Input
            label="Name"
            name="name"
            placeholder="Input your name"
            // onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            // value={formik.values.name}
            // error={
            //   formik.errors.name && formik.touched.name
            //     ? formik.errors.name
            //     : undefined
            // }
          />
          <Input
            label="Email"
            type="email"
            name="email"
            placeholder="Input your email address"
            // onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            // value={formik.values.email}
            // error={
            //   formik.errors.email && formik.touched.email
            //     ? formik.errors.email
            //     : undefined
            // }
          />
          <Input
            label="Password"
            type="password"
            name="password"
            placeholder="Input password"
            // onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            // value={formik.values.password}
            // error={
            //   formik.errors.password && formik.touched.password
            //     ? formik.errors.password
            //     : undefined
            // }
          />
          <Input
            label="Confirm Password"
            type="password"
            name="confirmPassword"
            placeholder="Confirm password"
            // onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            // value={formik.values.confirmPassword}
            // error={
            //   formik.errors.confirmPassword && formik.touched.confirmPassword
            //     ? formik.errors.confirmPassword
            //     : undefined
            // }
          />
        </div>

        <Button
          label="Sign Up"
          // loading={isLoading}
          // onClick={formik.handleSubmit}
          className="w-full"
        />
      </div>
    </div>
  );
};
export default Signup;
