import { useStore } from "@/bootstrap/Store.bootstrap";
import { DarkModeToggle } from "@/modules/Theme/components";
import { CredentialResponse, GoogleLogin } from "@react-oauth/google";
import { observer } from "mobx-react-lite";
import { useEffect } from "react";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { Card } from "ui";
import { LoginMethod } from "../../Types.auth";

const Login = observer(() => {
  const { darkMode } = useStore().theme;
  const { login, isAuthenticated, error } = useStore().auth;
  const navigate = useNavigate();

  const handleGoogleLogin = (res: CredentialResponse) => {
    if (res.credential) {
      login({ idToken: res.credential, loginMethod: LoginMethod.GOOGLE_SSO });
    }
  };

  // const handleLogin = (params: LoginParams) => {
  //   login(params);
  // };

  useEffect(() => {
    if (isAuthenticated) navigate("/");

    if (error) {
      toast.error(`Login failed: ${error.message}`);
    }
  }, [error, navigate, isAuthenticated]);

  // const formik = useFormik({
  //   initialValues: {
  //     email: "",
  //     password: "",
  //     loginMethod: LoginMethod.EMAIL,
  //   },
  //   validationSchema: loginSchema,
  //   validateOnBlur: true,
  //   onSubmit: handleLogin,
  // });

  return (
    <div className="flex items-center justify-center h-screen bg-gradient-to-b from-white via-neutral-100 to-white dark:from-neutral-800 dark:to-neutral-900">
      <Card containerStyle="flex flex-col gap-8 p-8 items-center w-full max-w-sm dark:bg-gray-900">
        <h3>Claro Admin</h3>

        <GoogleLogin
          onSuccess={(credentialResponse) => {
            handleGoogleLogin(credentialResponse);
          }}
          onError={() => {
            toast.error("Login failed!");
          }}
          text="signin_with"
          theme={darkMode ? "filled_black" : "outline"}
        />

        {/* <div className="w-full py-3 flex items-center text-xs text-neutral-500 uppercase before:flex-1 before:border-t before:border-neutral-300 before:me-4 after:flex-1 after:border-t after:border-neutral-300 after:ms-4 dark:text-neutral-500 dark:before:border-neutral-600 dark:after:border-neutral-600">
          Or
        </div> */}

        {/* <div className="flex flex-col gap-4 w-full">
          <Input
            label="Email"
            type="email"
            name="email"
            placeholder="<EMAIL>"
            // onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            // value={formik.values.email}
            // error={
            //   formik.errors.email && formik.touched.email
            //     ? formik.errors.email
            //     : undefined
            // }
          />
          <Input
            label="Password"
            type="password"
            placeholder="******"
            hint={
              <span className="cursor-pointer underline text-xs">
                Forgot password?
              </span>
            }
            name="password"
            // onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            // value={formik.values.password}
            // error={
            //   formik.errors.password && formik.touched.password
            //     ? formik.errors.password
            //     : undefined
            // }
          />
        </div> */}
        {/* <button className="py-1.5 px-3 inline-flex items-center justify-center text-sm font-medium rounded-lg shadow border border-transparent bg-primary-400 text-white w-full hover:bg-primary transition-all duration-200">
          Sign In
        </button> */}
        {/* <button className="py-1.5 px-3 inline-flex items-center justify-center text-sm font-medium rounded-lg shadow border border-transparent bg-blue-gray text-white w-full hover:bg-primary">
          Sign In
        </button> */}

        {/* <Button
          label="Sign In"
          // loading={isLoading}
          // onClick={formik.handleSubmit}
          className="w-full"
        /> */}

        {/* <p className="inline-flex gap-1 mt-2 text-sm text-gray-600 dark:text-neutral-400">
          Don't have an account?
          <Link
            to={AuthPages.Signup}
            className="text-primary-600 decoration-2 hover:underline font-medium dark:text-primary-100 cursor-pointer"
          >
            Sign up
          </Link>
        </p> */}

        <DarkModeToggle />
      </Card>
    </div>
  );
});
export default Login;
