import { makeAutoObservable } from "mobx";
import { makePersistable } from "mobx-persist-store";

class ThemeStore {
  darkMode = false;

  constructor() {
    makeAutoObservable(this);

    makePersistable(this, {
      name: "ThemeStoreCustomsAdmin",
      properties: ["darkMode"],
      storage: localStorage
    }).then(() => {
      // After the store is hydrated, apply the class based on the stored state
      this.applyDarkModeClass();
    });
  }

  toggleDarkMode = () => {
    this.darkMode = !this.darkMode;
    this.applyDarkModeClass();
  };

  applyDarkModeClass = () => {
    if (this.darkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  };
}

export default ThemeStore;
