import { useStore } from "@/bootstrap/Store.bootstrap";
import { MoonIcon, SunIcon } from "lucide-react";
import { observer } from "mobx-react-lite";

type Props = { iconOnly?: boolean };
const DarkModeToggle = observer(({ iconOnly }: Props) => {
  const { darkMode, toggleDarkMode } = useStore().theme;

  return (
    <button onClick={toggleDarkMode} className="text-sm inline-flex gap-3 items-center">
      {darkMode ? (
        <>
          <SunIcon size={16} />
          {!iconOnly && <span>Light Mode</span>}
        </>
      ) : (
        <>
          <MoonIcon size={16} className="text-warning-600 fill-warning" />
          {!iconOnly && <span>Dark Mode</span>}
        </>
      )}
    </button>
  );
});
// "🌙 Dark Mode" : "☀️ Light Mode"
export default DarkModeToggle;
