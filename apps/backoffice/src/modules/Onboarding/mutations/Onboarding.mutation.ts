import { useMutation } from "@tanstack/react-query";
import { OnboardingDocument } from "nest-modules";
import OnboardingService from "../services";
import { CarmRequestParams } from "@/modules/Importer/Types.importer";
import { ResendPoaParams } from "../Types.onboarding";

const useEditOnboarding = () =>
  useMutation({
    mutationFn: async (params: OnboardingDocument) => await OnboardingService.edit(params)
  });

const useCarmRequest = () =>
  useMutation({
    mutationFn: async (params: CarmRequestParams) => {
      return await OnboardingService.carmRequest(params);
    }
  });

const useResendPoA = () =>
  useMutation({
    mutationFn: async (params: ResendPoaParams) => {
      return await OnboardingService.resendPoA(params);
    }
  });

export { useEditOnboarding, useCarmRequest, useResendPoA };
