import { http } from "@/bootstrap/Http.bootstrap";
import { env } from "@/config/Environment.config";
import { CarmRequestResponse, CarmRequestParams } from "@/modules/Importer/Types.importer";
import { initializeApp } from "firebase/app";
import { FunctionsError, getFunctions, httpsCallable, HttpsCallableResult } from "firebase/functions";
import { OnboardingDocument, SendPoaResponse, WhereQueryDto } from "nest-modules";
import { DetailParams, handleError } from "utils";
import { ResendPoaParams } from "../Types.onboarding";

const ONBOARDING_ROUTE = "firebase";
/**
 * Get onboarding list
 *
 * @returns
 */
async function list(params?: WhereQueryDto): Promise<OnboardingDocument[]> {
  try {
    return await http.post(`${ONBOARDING_ROUTE}/onboarding`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get onboarding detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<OnboardingDocument> {
  try {
    return await http.get(`${ONBOARDING_ROUTE}/onboarding/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit onboarding
 *
 * @returns
 */
async function edit({ id, ...params }: OnboardingDocument): Promise<void> {
  try {
    return await http.put(`${ONBOARDING_ROUTE}/onboarding/${id}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Send Carm Request
 *
 * @returns
 */
async function carmRequest(params: CarmRequestParams): Promise<CarmRequestResponse> {
  try {
    return await http.post(`carm/request`, params, {
      timeout: 120000
    });
  } catch (error) {
    throw await handleError(error);
  }
}

const app = initializeApp(env.firebaseConfig);
const functions = getFunctions(app, "northamerica-northeast2");
/**
 * Send PoA.
 *
 * @returns
 */
async function resendPoA({
  envelopeId,
  importer
}: ResendPoaParams): Promise<HttpsCallableResult<SendPoaResponse>> {
  const callable = httpsCallable<ResendPoaParams, SendPoaResponse>(functions, "resendPoa");
  try {
    return await callable({ envelopeId, importer });
  } catch (error) {
    const firebaseError = error as FunctionsError;
    throw new Error(firebaseError.message || "An unknown error occurred");
  }
}

export default { list, get, edit, carmRequest, resendPoA };
