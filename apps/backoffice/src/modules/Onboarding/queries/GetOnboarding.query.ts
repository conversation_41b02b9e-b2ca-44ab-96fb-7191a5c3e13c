import { useQuery } from "@tanstack/react-query";
import type { OnboardingDocument, WhereQueryDto } from "nest-modules";
import OnboardingService from "../services";

export const getOnboardingKey = "getOnboarding";

export const useGetOnboarding = <R = OnboardingDocument[]>(
  params?: WhereQueryDto,
  select?: (data: OnboardingDocument[]) => R,
  refetchOnMount = false
) =>
  useQuery({
    queryKey: [getOnboardingKey, params],
    queryFn: () => OnboardingService.list(params),
    select,
    refetchOnMount,
    retry: 1
  });

export const useGetOnboardingList = (params?: WhereQueryDto) =>
  useGetOnboarding(
    params,
    (data: OnboardingDocument[]) => {
      return data;
    },
    true
  );
