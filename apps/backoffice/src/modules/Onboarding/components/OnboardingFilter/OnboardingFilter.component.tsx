import { ORDER_BY } from "@/common/Constant.common";
import { useDebounce } from "@uidotdev/usehooks";
import { useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";
import { Card, Input, Select } from "ui";
import { CARM_STATUS, ONBOARDING_SORT_BY, ONBOARDING_STEP, POA_STATUS } from "../../Constant.onboarding";
import { WhereQueryDto } from "nest-modules";
import { WhereFilterOp } from "firebase/firestore";
import { onboardingTableSchema } from "../../Schema.onboarding";
import { SortOrder } from "utils";

type Props = {
  isOpen: boolean;
  filterValues(values?: WhereQueryDto): void;
};

const updateFilterDto = (
  field: string,
  operator: WhereFilterOp,
  value: unknown,
  prev?: WhereQueryDto
): WhereQueryDto => {
  if (!prev) {
    return {
      whereQueries: value ? [[field, operator, value]] : undefined
    };
  }

  const whereQueries = prev.whereQueries || [];

  if (!value) {
    const filteredQueries = whereQueries.filter((query) => query[0] !== field);
    return {
      ...prev,
      whereQueries: filteredQueries.length > 0 ? filteredQueries : undefined
    };
  }

  const filteredQueries = whereQueries.filter((query) => query[0] !== field);

  return {
    ...prev,
    whereQueries: [...filteredQueries, [field, operator, value]]
  };
};

const OnboardingFilter = ({ isOpen, filterValues }: Props) => {
  const [values, setValues] = useState<WhereQueryDto>();

  const [_name, setName] = useState<string>();
  const name = useDebounce(_name, 500);
  const [_email, setEmail] = useState<string>();
  const email = useDebounce(_email, 500);

  useEffect(() => {
    setValues((prev) => {
      let result = prev || {};

      if (name) {
        result = updateFilterDto("name", ">=", name, result);
      } else if (result.whereQueries) {
        result = updateFilterDto("name", ">=", undefined, result);
      }

      if (email) {
        result = updateFilterDto("contactEmail", ">=", email, result);
      } else if (result.whereQueries) {
        result = updateFilterDto("contactEmail", ">=", undefined, result);
      }

      return result;
    });
  }, [email, name]);

  useEffect(() => {
    filterValues(values);
  }, [filterValues, values]);

  useEffect(() => () => setValues(undefined), []);

  return (
    <div
      className={twMerge(
        "overflow-hidden transition-max-height duration-300 ease-in-out",
        isOpen ? "max-h-96" : "max-h-0"
      )}
    >
      {isOpen && (
        <Card containerStyle="flex flex-wrap items-center gap-x-3 gap-y-2 px-6 py-4 border">
          <div className="flex gap-3">
            <Input label="Name" placeholder="Search" onTextChange={setName} kind="search" />
            <Input label="Email" placeholder="Search" onTextChange={setEmail} kind="search" />
            <Select
              label="Step"
              onSelected={(step: number) =>
                setValues((prev) => updateFilterDto("step", "==", Number(step), prev))
              }
              options={ONBOARDING_STEP}
              optional
            />
            <Select
              label="PoA Status"
              onSelected={(step: number) =>
                setValues((prev) => updateFilterDto("poaStatus", "==", step, prev))
              }
              options={POA_STATUS}
              optional
            />
            <Select
              label="Carm Status"
              onSelected={(step: number) =>
                setValues((prev) => updateFilterDto("carmStatus", "==", step, prev))
              }
              options={CARM_STATUS}
              optional
            />
          </div>

          <div className="flex gap-3">
            <Select
              label="Sort By"
              onSelected={(sortBy: keyof typeof onboardingTableSchema) =>
                setValues((prev) => ({ ...prev, sortBy }))
              }
              options={ONBOARDING_SORT_BY}
              optional
            />
            <Select
              label="Sort Direction"
              onSelected={(sortOrder: SortOrder) => setValues((prev) => ({ ...prev, sortOrder }))}
              options={ORDER_BY}
              optional
            />
          </div>
        </Card>
      )}
    </div>
  );
};
export default OnboardingFilter;
