import { StatusLabel } from "ui";
import { snakeCaseToCapitalize } from "utils";
import { OnboardingSteps } from "../../Types.onboarding";
import { onboardingStepLabelColor } from "../../Utils.onboarding";

type Props = { value?: OnboardingSteps };
function OnboardingStepLabel({ value }: Props) {
  const text = value ? snakeCaseToCapitalize(OnboardingSteps[value]) : "N/A";
  const color = onboardingStepLabelColor(value);

  return <StatusLabel text={text} color={color} className="w-fit" />;
}
export default OnboardingStepLabel;
