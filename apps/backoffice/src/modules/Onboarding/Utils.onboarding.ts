import { OnboardingSteps } from "./Types.onboarding";

export function onboardingStepLabelColor(status?: OnboardingSteps) {
  switch (status) {
    case OnboardingSteps.INTRODUCTION_VIDEO:
      return "info";
    case OnboardingSteps.IMPORTER_INFORMATION:
      return "bg-cyan-100 text-cyan-800 border-cyan-300";
    case OnboardingSteps.BUSINESS_VALIDATION:
      return "bg-indigo-100 text-indigo-800 border-indigo-300";
    case OnboardingSteps.POA:
      return "warning";
    case OnboardingSteps.COMPLETED:
      return "success";
    default:
      return "gray";
  }
}
