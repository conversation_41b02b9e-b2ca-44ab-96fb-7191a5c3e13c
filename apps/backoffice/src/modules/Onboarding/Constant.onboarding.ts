import { enumToSelectOptions, schemaToSelectOptions } from "utils";
import { CarmStatus, PoaStatus } from "../Importer/Types.importer";
import { onboardingTableSchema } from "./Schema.onboarding";
import { OnboardingSteps } from "./Types.onboarding";

export const ONBOARDING_STEP = Object.entries(OnboardingSteps)
  .filter(([key]) => isNaN(Number(key)))
  .map(([key, value]) => ({
    label: key
      .split("_")
      .map((word) => word.charAt(0) + word.slice(1).toLowerCase())
      .join(" "),
    value
  }));
export const POA_STATUS = enumToSelectOptions(PoaStatus);
export const CARM_STATUS = enumToSelectOptions(CarmStatus);
export const ONBOARDING_SORT_BY = schemaToSelectOptions(onboardingTableSchema);
