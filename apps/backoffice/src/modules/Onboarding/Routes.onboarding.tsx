import { RouterObject } from "@/common/Types.common";
import { User } from "lucide-react";
import { OnboardingList } from "./views";
import OnboardingDetail from "./views/OnboardingDetail";

const OnboardingPages = {
  List: "/onboarding",
  Detail: (onboarding_id?: string) => `${OnboardingPages.List}/${onboarding_id}`
};
const OnboardingNavigation = {
  label: "Onboarding",
  icon: <User className="size-5" />,
  pattern: [{ path: "/onboarding/*" }],
  path: OnboardingPages.List
};
const OnboardingRoutes: RouterObject = [
  {
    path: OnboardingPages.List,
    children: [
      { path: "", element: <OnboardingList /> },
      {
        path: ":onboarding_id",
        element: <OnboardingDetail />,
        handle: {
          crumb: "Onboarding Details"
        }
      }
    ]
  }
];

export { OnboardingNavigation, OnboardingPages, OnboardingRoutes };
