import { TableSchema } from "utils";
import { FailedBusinessVerification, OnboardingDocument } from "nest-modules";
import { OnboardingSteps } from "./Types.onboarding";
import { CarmLabel, PoaLabel } from "../Importer/components";
import { CarmStatus, PoaStatus } from "../Importer/Types.importer";
import { OnboardingStepLabel } from "./components";
import { object, string } from "yup";
import { isValidPhoneNumber } from "react-phone-number-input";

export const onboardingTableSchema: TableSchema<OnboardingDocument> = {
  name: {
    header: "Name",
    style: "font-medium",
    visible: true
  },
  step: {
    header: "Step",
    renderer: ({ value }: { value: OnboardingSteps }) => <OnboardingStepLabel value={value} />,
    visible: true
  },
  poaStatus: {
    header: "POA Status",
    renderer: ({ value }: { value: PoaStatus }) => <PoaLabel value={value} />,
    visible: true
  },
  carmStatus: {
    header: "Carm Status",
    renderer: ({ value }: { value: CarmStatus }) => <CarmLabel value={value} />,
    visible: true
  },
  contactEmail: {
    header: "Email",
    visible: true
  },
  companySize: {
    header: "Company Size",
    visible: true
  },
  monthlyImportVolume: {
    header: "Monthly Import Vol",
    visible: true
  },
  industry: {
    header: "Industry",
    visible: true
  },
  commodity: {
    header: "Commodity",
    visible: true
  },
  createdAt: {
    header: "Created At",
    renderer: "dateTime",
    visible: true
  },
  updatedAt: {
    header: "Updated At",
    renderer: "dateTime",
    visible: true
  }
};
export const failedCarmRequestTableSchema: TableSchema<FailedBusinessVerification> = {
  businessName: {
    header: "Business Name",
    visible: true
  },
  businessNumber: {
    header: "Business Number",
    visible: true
  },
  status: {
    header: "Status",
    renderer: ({ value }: { value: CarmStatus }) => <CarmLabel value={value} />,
    visible: true
  },
  timestamp: {
    header: "Timestamp",
    renderer: "dateTime",
    visible: true
  }
};
const { status: _omit, ...rest } = failedCarmRequestTableSchema;
export const failedBusinessVerificationTableSchema: TableSchema<FailedBusinessVerification> = {
  ...rest
};

export const onboardingFormSchema = object({
  companyName: string().required("Company name is required"),
  businessNumber: string()
    .required("Business number is required")
    .matches(
      /^\d{9}RM00\d{2}$/,
      "Business number must be in format: 9 digits + RM00 + 2 digits (e.g. 123456789RM0001)"
    ),
  email: string().email("Email is not valid").required("Email is required"),
  phoneNumber: string()
    .required("Phone number is required")
    .test("isValid", "Phone number is not valid", (value) => isValidPhoneNumber(value)),
  fax: string().optional(),
  officerNameAndTitle: string().required("Officer name is required")
});
