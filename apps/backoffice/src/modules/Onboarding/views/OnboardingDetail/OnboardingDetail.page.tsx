import { BaseError } from "@/common/Types.common";
import { CarmLabel, PoaLabel } from "@/modules/Importer/components";
import { CarmStatus, PoaStatus } from "@/modules/Importer/Types.importer";
import { useFormik } from "formik";
import { observer } from "mobx-react-lite";
import { FailedBusinessVerification, OnboardingDocument } from "nest-modules";
import { useCallback, useEffect, useMemo } from "react";
import toast from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";
import { Button, Card, FieldItem, Header, Icons, Input, PhoneNumberInput, Table } from "ui";
import { booleanToWord, formatNumber, formikInputOpts } from "utils";
import { OnboardingStepLabel } from "../../components";
import { useCarmRequest, useEditOnboarding, useResendPoA } from "../../mutations";
import { useGetOnboardingDetail } from "../../queries";
import { OnboardingPages } from "../../Routes.onboarding";
import {
  failedBusinessVerificationTableSchema,
  failedCarmRequestTableSchema,
  onboardingFormSchema
} from "../../Schema.onboarding";
import { OnboardingSteps } from "../../Types.onboarding";

const OnboardingDetail = observer(() => {
  const { onboarding_id } = useParams();
  const navigate = useNavigate();

  const {
    data: info,
    isFetching,
    error,
    refetch
  } = useGetOnboardingDetail({
    id: onboarding_id
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
      navigate(OnboardingPages.List);
    }
  }, [error, navigate]);

  const canResendPoa = useMemo(
    () => info?.poaStatus === PoaStatus.SENT && info?.envelopeId,
    [info?.poaStatus, info?.envelopeId]
  );
  const canRequestCarm = useMemo(
    () =>
      (!info?.carmStatus || info?.carmStatus === CarmStatus.INVALID) &&
      (info?.step ?? 0) > OnboardingSteps.BUSINESS_VALIDATION,
    [info?.carmStatus, info?.step]
  );

  const saveOnboarding = useEditOnboarding();
  const carmRequest = useCarmRequest();
  const resendPoa = useResendPoA();

  const handleSaveOnboarding = useCallback(
    async (
      params: OnboardingDocument["importer"],
      action?: "carmRequest" | "resendPoa",
      isDirty?: boolean
    ) => {
      if (!info) return;
      let carmStatus: CarmStatus | undefined;
      let envelopeId: string | undefined;

      try {
        if (action === "carmRequest") {
          const request = await carmRequest.mutateAsync({
            businessNo: params?.businessNumber?.slice(0, 9) ?? ""
          });
          carmStatus = request.result;
        } else if (action === "resendPoa") {
          const poa = await resendPoa.mutateAsync({
            envelopeId: info.envelopeId,
            ...(isDirty && { importer: { ...info.importer, ...params } })
          });
          envelopeId = poa.data?.envelopeId;
        }

        await saveOnboarding.mutateAsync({
          id: info.id,
          step: info.step,
          importer: { ...info.importer, ...params },
          ...(carmStatus && { carmStatus }),
          ...(envelopeId && { envelopeId })
        });

        toast.success(action === "carmRequest" ? `Carm request sent` : `POA resent`);
        refetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [carmRequest, info, refetch, resendPoa, saveOnboarding]
  );

  const formik = useFormik({
    initialValues: {
      companyName: info?.importer?.companyName ?? "",
      businessNumber: info?.importer?.businessNumber ?? "",
      email: info?.importer?.email ?? info?.contactEmail ?? "",
      phoneNumber: info?.importer?.phoneNumber ?? "",
      fax: info?.importer?.fax ?? "",
      officerNameAndTitle: info?.importer?.officerNameAndTitle ?? info?.name ?? ""
    },
    validationSchema: onboardingFormSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit: (values) => handleSaveOnboarding(values)
  });

  const handleSubmitWithAction = (action: "carmRequest" | "resendPoa") => {
    return () => {
      if (!formik.isValid) {
        formik.validateForm();
        return;
      }
      handleSaveOnboarding(formik.values, action, formik.dirty);
    };
  };

  return (
    <div>
      <Header title="Onboarding Detail">
        {canResendPoa && (
          <Button
            label="Resend POA"
            loading={resendPoa.isPending || saveOnboarding.isPending}
            disabled={isFetching || carmRequest.isPending}
            onClick={handleSubmitWithAction("resendPoa")}
          />
        )}
        {canRequestCarm && (
          <Button
            label="Carm Request"
            loading={carmRequest.isPending || saveOnboarding.isPending}
            disabled={isFetching || resendPoa.isPending}
            onClick={handleSubmitWithAction("carmRequest")}
          />
        )}
      </Header>

      {isFetching ? (
        <div className="py-2">
          <Icons.Loader />
        </div>
      ) : (
        <>
          <Card containerStyle="px-4 py-2 flex gap-4">
            <FieldItem
              label="Step:"
              customElement={<OnboardingStepLabel value={info?.step} />}
              direction="row"
            />
            <FieldItem
              label="PoA Status:"
              customElement={<PoaLabel value={info?.poaStatus} />}
              direction="row"
            />
            <FieldItem
              label="Carm Status:"
              customElement={<CarmLabel value={info?.carmStatus as CarmStatus} />}
              direction="row"
            />
          </Card>

          <Card>
            {/* <Breadcrumbs /> */}

            <div className="flex flex-col gap-2 p-4">
              <h5>Industry Information</h5>
              <div className="grid grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-2">
                <FieldItem label="Industry" value={info?.industry} />
                <FieldItem label="Commodity" value={info?.commodity} />
                <FieldItem label="Monthly Import Vol." value={info?.monthlyImportVolume} />
                <FieldItem label="Company Size" value={info?.companySize} />
              </div>

              <hr />

              <h5>Contact Information</h5>
              <div className="grid grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-2">
                <FieldItem label="Name" value={info?.name} />
                <FieldItem label="Email" value={info?.contactEmail} />
              </div>

              <hr />

              <h5>Importer Information</h5>
              <div className="grid grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-2">
                {canRequestCarm || canResendPoa ? (
                  <>
                    <Input
                      label="Company Name"
                      placeholder="Input company name"
                      {...formikInputOpts(formik, "companyName")}
                    />
                    <Input
                      label="Business Number"
                      placeholder="Input business number"
                      {...formikInputOpts(formik, "businessNumber")}
                      info="Business number format: 123456789RM0001"
                    />
                  </>
                ) : (
                  <>
                    <FieldItem label="Company Name" value={info?.importer?.companyName} />
                    <FieldItem label="Business Number" value={info?.importer?.businessNumber} />
                  </>
                )}
              </div>
              <div className="grid grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-2">
                {canResendPoa ? (
                  <>
                    <Input
                      label="Email"
                      type="email"
                      placeholder="Input email"
                      {...formikInputOpts(formik, "email")}
                    />
                    <PhoneNumberInput
                      placeholder="Input phone number"
                      containerStyle="flex-1"
                      {...formikInputOpts(formik, "phoneNumber")}
                      onChange={(value) => formik.setFieldValue("phoneNumber", value)}
                    />
                    <Input
                      label="Fax Number"
                      placeholder="Input fax number"
                      {...formikInputOpts(formik, "fax")}
                    />
                    <Input
                      label="Officer"
                      placeholder="Input officer"
                      {...formikInputOpts(formik, "officerNameAndTitle")}
                    />
                  </>
                ) : (
                  <>
                    <FieldItem label="Email" value={info?.importer?.email} />
                    <FieldItem label="Phone Number" value={info?.importer?.phoneNumber} />
                    <FieldItem label="Fax" value={info?.importer?.fax} />
                    <FieldItem label="Officer" value={info?.importer?.officerNameAndTitle} />
                  </>
                )}
              </div>
              <div className="grid grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-2">
                <FieldItem label="Address" value={info?.importer?.address} />
                <FieldItem label="City" value={info?.importer?.city} />
                <FieldItem label="State" value={info?.importer?.state} />
                <FieldItem label="Country" value={info?.importer?.countryName} />
                <FieldItem label="Postal Code" value={info?.importer?.postalCode} />
              </div>

              <hr />

              <h5>Video Information</h5>
              <div className="grid grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-2">
                <FieldItem label="Video Played" value={booleanToWord(info?.videoPlayed)} />
                <FieldItem label="Video Completed" value={booleanToWord(info?.videoCompleted)} />
                <FieldItem label="Played Seconds" value={formatNumber(info?.playedSeconds)} />
              </div>

              <hr />

              {info?.failedBusinessVerification && (
                <>
                  <h5>Failed Business Verification</h5>
                  <Table
                    data={info.failedBusinessVerification as FailedBusinessVerification[]}
                    schema={failedBusinessVerificationTableSchema}
                  />
                </>
              )}

              {info?.failedCarmRequest && (
                <>
                  <h5>Failed Carm Request</h5>
                  <Table
                    data={info.failedCarmRequest as FailedBusinessVerification[]}
                    schema={failedCarmRequestTableSchema}
                  />
                </>
              )}
            </div>
          </Card>
        </>
      )}
    </div>
  );
});
export default OnboardingDetail;
