import { OnboardingDocument, WhereQueryDto } from "nest-modules";
import { useCallback, useEffect, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { But<PERSON>, Header, Table } from "ui";
import { OnboardingFilter } from "../../components";
import { useGetOnboardingList } from "../../queries";
import { OnboardingPages } from "../../Routes.onboarding";
import { onboardingTableSchema } from "../../Schema.onboarding";

function OnboardingList() {
  const navigate = useNavigate();
  const [filter, toggleFilter] = useReducer((r) => !r, false);
  const [filters, setFilters] = useState<WhereQueryDto>();

  const { data, error, isFetching } = useGetOnboardingList({
    // sortBy: "createdAt",
    // sortOrder: SortOrder.DESC,
    ...filters
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const handleFilter = useCallback((values: WhereQueryDto) => {
    setFilters(values);
  }, []);

  return (
    <div className="flex flex-col">
      <Header title="Onboarding List">
        <Button label="Filters" kind="filter" onClick={toggleFilter} />
      </Header>

      <section>
        <OnboardingFilter filterValues={handleFilter} isOpen={filter} />
      </section>

      <main>
        <Table
          data={data}
          isLoading={isFetching}
          schema={onboardingTableSchema}
          onClick={(_data: OnboardingDocument) => {
            navigate(OnboardingPages.Detail(_data?.id?.toString() ?? ""));
          }}
          // footer={
          //   <Pagination currentPage={page} total={data?.total} limit={DEFAULT_LIMIT} onPageChange={setPage} />
          // }
          // total={data?.total}
        />
      </main>
    </div>
  );
}
export default OnboardingList;
