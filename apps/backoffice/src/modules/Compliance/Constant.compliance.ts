import { enumToSelectOptions, schemaToSelectOptions } from "utils";
import { complianceRuleTableSchema } from "./Schema.compliance";
import {
  MatchingConditionOperator,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus,
  ProductColumn
} from "./Types.compliance";

const PRIORITY_VALUES = Array.from({ length: 11 }, (_, i) => i);
export const PRIORITY = enumToSelectOptions(PRIORITY_VALUES);

export const SOURCE_TABLE = enumToSelectOptions(MatchingRuleSourceDatabaseTable).filter(
  (item) =>
    ![
      MatchingRuleSourceDatabaseTable.OGD_FILING,
      MatchingRuleSourceDatabaseTable.CERTIFICATE_OF_ORIGIN,
      MatchingRuleSourceDatabaseTable.SIMA_FILING,
      MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE
    ].includes(item.value as MatchingRuleSourceDatabaseTable)
);

export const DESTINATION_TABLE = enumToSelectOptions(MatchingRuleDestinationDatabaseTable);
export const OPERATOR = enumToSelectOptions(MatchingConditionOperator);
export const MATCHING_RULE_STATUS = enumToSelectOptions(MatchingRuleStatus);

export const MATCHING_RULE_SORT_BY = schemaToSelectOptions(complianceRuleTableSchema);
// TODO: Move to product module
export const PRODUCT_COLUMN = enumToSelectOptions(ProductColumn);
