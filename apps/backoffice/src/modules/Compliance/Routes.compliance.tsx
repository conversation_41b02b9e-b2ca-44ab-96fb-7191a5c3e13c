import { RouterObject } from "@/common/Types.common";
import { Shield } from "lucide-react";
import { ComplianceList } from "./views";

const CompliancePages = {
  List: "/compliance"
  // Type: "/compliance-rule",
  // Detail: (shipment_id?: string) => `${DocumentPages.List}/${shipment_id}`,
  // Create: "create",
};
const ComplianceNavigation = {
  label: "Compliance Rule",
  icon: <Shield className="size-5" />,
  pattern: [{ path: `${CompliancePages.List}/*` }],
  path: CompliancePages.List
};
const ComplianceRoutes: RouterObject = [
  {
    path: CompliancePages.List,
    element: <ComplianceList />,
    children: [
      // { path: "", element: <DocumentTypeList /> },
      // {
      //   path: DocumentPages.Detail(":shipment_id"),
      //   element: <ShipmentDetail />,
      // },
      // {
      //   path: DocumentPages.Create,
      //   element: <CreateShipment />,
      // },
    ]
  }
];

export { ComplianceNavigation, CompliancePages, ComplianceRoutes };
