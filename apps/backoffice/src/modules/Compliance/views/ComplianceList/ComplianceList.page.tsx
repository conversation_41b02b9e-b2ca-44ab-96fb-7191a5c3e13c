import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { BaseError } from "@/common/Types.common";
import { useCallback, useEffect, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { <PERSON><PERSON>, Header, Pagination, Table } from "ui";
import { SortOrder } from "utils";
import { AddComplianceModal, ComplianceFilter } from "../../components";
import {
  useBatchSaveMatchingCondition,
  useDeleteMatchingRule,
  useSaveMatchingRule,
  useUpdateMatchingRuleStatus
} from "../../mutations";
import { useGetMatchingRuleList } from "../../queries";
import { complianceRuleTableSchema } from "../../Schema.compliance";
import {
  BatchSaveMatchingConditionParams,
  GetMatchingRuleList,
  MatchingRule,
  MatchingRuleColumn,
  MatchingRuleFormParams,
  MatchingRuleStatus,
  SaveMatchingRuleParams,
  UpdateMatchingRuleStatusParams
} from "../../Types.compliance";
import { formatRuleConditions } from "../../Utils.compliance";

function ComplianceList() {
  const [modal, toggleModal] = useReducer((r) => !r, false);
  const [matchingRule, setMatchingRule] = useState<MatchingRule>();
  const [page, setPage] = useState(1);
  const [filter, toggleFilter] = useReducer((r) => !r, false);
  const [filters, setFilters] = useState<GetMatchingRuleList>();

  const saveMatchingRule = useSaveMatchingRule();
  const saveMatchingCondition = useBatchSaveMatchingCondition();
  const updateStatus = useUpdateMatchingRuleStatus();
  const deleteMatchingRule = useDeleteMatchingRule();

  const isPending =
    saveMatchingRule.isPending ||
    saveMatchingCondition.isPending ||
    updateStatus.isPending ||
    deleteMatchingRule.isPending;

  const { data, error, isFetching, refetch } = useGetMatchingRuleList({
    page,
    limit: DEFAULT_LIMIT,
    sortBy: MatchingRuleColumn.createDate,
    sortOrder: SortOrder.DESC,
    ...filters
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const handleSaveMatchingRule = useCallback(
    async ({ conditions, deletedIds, ...params }: MatchingRuleFormParams) => {
      const formatConditions = formatRuleConditions(conditions);
      const formatParams: SaveMatchingRuleParams = {
        ...params,
        priority: params.priority !== undefined ? Number(params.priority) : undefined,
        organizationId: params.organizationId ? Number(params.organizationId) : null,
        expiryDate: params.expiryDate || null
      };

      try {
        const rule = await saveMatchingRule.mutateAsync(formatParams);

        const formatBatchParams: BatchSaveMatchingConditionParams = {
          id: params.id ?? rule.id,
          // TODO create resable batch formatting
          ...(formatConditions?.length > 0 && {
            create: formatConditions.filter((c) => !c.id),
            edit: formatConditions
              .filter((f) => typeof f.id === "number")
              .map((f) => ({ ...f, id: f.id as number }))
          }),
          ...(deletedIds && deletedIds.length > 0 && { delete: deletedIds })
        };

        await saveMatchingCondition.mutateAsync(formatBatchParams);
        await updateStatus.mutateAsync({
          id: rule.id,
          status: MatchingRuleStatus.ACTIVE
        });

        const message = `Matching Rule ${params.name} ${params.id ? "updated" : "created"} successfully`;
        toast.success(message);

        toggleModal();
        setMatchingRule(undefined);
        refetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [refetch, saveMatchingCondition, saveMatchingRule, updateStatus]
  );

  const handleUpdateStatus = useCallback(
    async (status: MatchingRuleStatus) => {
      const params: UpdateMatchingRuleStatusParams = {
        id: matchingRule?.id,
        status
      };

      try {
        const update = await updateStatus.mutateAsync(params);

        toast.success("Matching Rule status updated successfully");

        if (status === MatchingRuleStatus.PENDING) {
          setMatchingRule(update);
        } else {
          toggleModal();
          setMatchingRule(undefined);
        }
        refetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [matchingRule?.id, refetch, updateStatus]
  );

  const handleDeleteMatchingRule = useCallback(async () => {
    try {
      await deleteMatchingRule.mutateAsync({ id: matchingRule?.id });

      toast.success("Matching Rule deleted successfully");

      toggleModal();
      setMatchingRule(undefined);
      refetch();
    } catch (error) {
      toast.error((error as BaseError).message);
    }
  }, [deleteMatchingRule, matchingRule?.id, refetch]);

  const handleFilter = useCallback((values: GetMatchingRuleList) => {
    setFilters(values);
    setPage(1);
  }, []);

  return (
    <div className="flex flex-col">
      {modal && (
        <AddComplianceModal
          show={modal}
          info={matchingRule}
          isLoading={isPending}
          onUpdateStatus={handleUpdateStatus}
          onSubmit={handleSaveMatchingRule}
          onDelete={handleDeleteMatchingRule}
          onClose={() => {
            toggleModal();
            setMatchingRule(undefined);
          }}
        />
      )}

      <Header title="Compliance Rule List">
        <Button label="Filters" kind="filter" onClick={toggleFilter} />
        <Button
          label="New Rule"
          kind="create"
          className="ml-auto"
          onClick={toggleModal}
          disabled={isPending}
        />
      </Header>

      <section>
        <ComplianceFilter filterValues={handleFilter} isOpen={filter} />
      </section>

      <main>
        <Table
          data={data?.matchingRules}
          isLoading={isFetching}
          schema={complianceRuleTableSchema}
          onClick={(_data: MatchingRule) => {
            setMatchingRule(_data);
            toggleModal();
          }}
          footer={
            <Pagination currentPage={page} total={data?.total} limit={DEFAULT_LIMIT} onPageChange={setPage} />
          }
          total={data?.total}
        />
      </main>
    </div>
  );
}
export default ComplianceList;
