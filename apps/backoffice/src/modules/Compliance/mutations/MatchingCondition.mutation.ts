import { useMutation } from "@tanstack/react-query";
import ComplianceService from "../services";
import type { BatchSaveMatchingConditionParams } from "../Types.compliance";

const useBatchSaveMatchingCondition = () =>
  useMutation({
    mutationFn: async (params: BatchSaveMatchingConditionParams) =>
      await ComplianceService.MatchingCondition.batchSave(params)
  });

export { useBatchSaveMatchingCondition };
