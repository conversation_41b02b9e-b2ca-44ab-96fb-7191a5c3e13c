import {
  BatchUpdateMatchingConditionsDto,
  CanadaAntiDumping,
  CanadaOgd,
  CanadaSimaCode,
  CanadaTariff,
  CanadaTreatmentCode,
  CanadaVfdCode,
  CreateMatchingConditionDto,
  EditMatchingRuleDto,
  GetMatchingConditionsDto,
  GetMatchingConditionsResponseDto,
  GetMatchingRulesDto,
  GetMatchingRulesResponseDto,
  MatchingCondition as MC,
  MatchingRule as MR,
  Product,
  QueryMatchingResultsDto,
  QueryMatchingResultsResponseDto,
  ReverseQueryMatchingResultsDto,
  ReverseQueryMatchingResultsResponseDto,
  UpdateMatchingRuleStatusDto
} from "nest-modules";
import { DetailParams, ListParams, PaginationParams, PaginationResponse } from "utils";

export enum MatchingRuleColumn {
  id = "id",
  status = "status",
  priority = "priority",
  name = "name",
  description = "description",
  sourceTable = "sourceTable",
  sourceId = "sourceId",
  destinationTable = "destinationTable",
  createdById = "createdById",
  createDate = "createDate",
  lastEditedById = "lastEditedById",
  lastEditDate = "lastEditDate"
}

export enum MatchingRuleStatus {
  PENDING = "pending",
  ACTIVE = "active",
  DISABLED = "disabled"
}

export enum MatchingRuleSourceDatabaseTable {
  CANADA_ANTI_DUMPING = "CanadaAntiDumping",
  CANADA_OGD = "CanadaOgd",
  CANADA_SIMA_CODE = "CanadaSimaCode",
  CANADA_TARIFF = "CanadaTariff",
  CANADA_TREATMENT_CODE = "CanadaTreatmentCode",
  CANADA_VFD_CODE = "CanadaVfdCode",
  CANADA_GST_EXEMPT_CODE = "CanadaGstExemptCode",
  CANADA_EXCISE_TAX_CODE = "CanadaExciseTaxCode",
  OGD_FILING = "OgdFiling",
  SIMA_FILING = "SimaFiling",
  CERTIFICATE_OF_ORIGIN = "CertificateOfOrigin"
}

export enum MatchingRuleDestinationDatabaseTable {
  PRODUCT = "Product"
}

export type SourceTable = CanadaAntiDumping &
  CanadaOgd &
  CanadaSimaCode &
  CanadaTariff &
  CanadaTreatmentCode &
  CanadaVfdCode;

export enum MatchingConditionOperator {
  EQUALS = "equals",
  CONTAINS = "contains",
  STARTS_WITH = "starts-with",
  ENDS_WITH = "ends-with",
  GREATER_THAN = "greater-than",
  LESS_THAN = "less-than",
  GREATER_THAN_OR_EQUAL_TO = "greater-than-or-equal-to",
  LESS_THAN_OR_EQUAL_TO = "less-than-or-equal-to",
  IN = "in",
  BETWEEN_INCLUSIVE = "between-inclusive",
  BETWEEN_EXCLUSIVE = "between-exclusive"
}
// TODO: Move to product module
export enum ProductColumn {
  PART_NUMBER = "partNumber",
  SKU = "sku",
  UPC = "upc",
  VENDOR_PART_NUMBER = "vendorPartNumber",
  DESCRIPTION = "description",
  HS_CODE = "hsCode",
  VENDOR = "vendorId",
  ORIGIN = "originId"
}
export type ProductTable = Product & {
  originName?: string;
  vendorCode?: string;
};

export type MatchingRule = MR;
export type MatchingCondition = MC;

export type MatchingRuleTable = MatchingRule & {
  sourceName?: string | number;
  organizationName?: string;
};

export type GetMatchingRuleList = ListParams<GetMatchingRulesDto>;
export type GetMatchingRuleListResponse = PaginationResponse & GetMatchingRulesResponseDto;

export type SaveMatchingRuleParams = EditMatchingRuleDto & DetailParams;

export type GetMatchingConditionList = ListParams<GetMatchingConditionsDto>;
export type GetMatchingConditionListResponse = PaginationResponse & GetMatchingConditionsResponseDto;

export type SaveMatchingConditionParams = CreateMatchingConditionDto &
  DetailParams & {
    valueRecord?: unknown;
  };

export type BatchSaveMatchingConditionParams = BatchUpdateMatchingConditionsDto & DetailParams;

export type MatchingRuleFormParams = SaveMatchingRuleParams & {
  conditions: SaveMatchingConditionParams[];
  deletedIds?: number[];
  sourceRecord?: SourceTable;
};

export type GetSourceTableParams = Partial<PaginationParams> & {
  url: string;
};
export type GetSourceTableResponse<T> = PaginationResponse & {
  // TODO: Remove this once we generalize the response
  data: T[];
};
export type GetQueryResultParams = QueryMatchingResultsDto & DetailParams;
export type GetQueryResultResponse = QueryMatchingResultsResponseDto;

export type SearchMatchingEntriesParams = ReverseQueryMatchingResultsDto;
export type SearchMatchingEntriesResponse = ReverseQueryMatchingResultsResponseDto;

export type UpdateMatchingRuleStatusParams = UpdateMatchingRuleStatusDto & DetailParams;
