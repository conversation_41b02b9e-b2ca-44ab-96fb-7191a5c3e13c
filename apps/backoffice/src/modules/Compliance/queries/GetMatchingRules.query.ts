import { useQuery } from "@tanstack/react-query";
import { CanadaOgd, OgdFiling } from "nest-modules";
import ComplianceService from "../services";
import type { GetMatchingRuleList, GetMatchingRuleListResponse, MatchingRule } from "../Types.compliance";

export const getMatchingRulesKey = "getMatchingRules";

export const useGetMatchingRules = <R = GetMatchingRuleListResponse>(
  params?: GetMatchingRuleList,
  select?: (data: GetMatchingRuleListResponse) => R,
  refetchOnMount = false
) =>
  useQuery({
    queryKey: [getMatchingRulesKey, params],
    queryFn: () => ComplianceService.MatchingRule.list(params),
    select,
    refetchOnMount
  });

export const useGetMatchingRuleList = (params?: GetMatchingRuleList) =>
  useGetMatchingRules(
    params,
    (data: GetMatchingRuleListResponse) => {
      const matchingRules = data.matchingRules.map((c: MatchingRule) => ({
        ...c,
        sourceName:
          (c.sourceRecord as CanadaOgd)?.displayName ?? (c.sourceRecord as OgdFiling)?.ogd?.displayName ?? "",
        organizationName: c.organization?.name ?? "",
        compute: c.compute || null
      }));
      return { ...data, matchingRules };
    },
    true
  );
