import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { flattenQueryResults, infiniteQueryMapper } from "utils";
import ComplianceService from "../services";
import type { GetSourceTableParams, GetSourceTableResponse, SourceTable } from "../Types.compliance";

export const getSourceTablesKey = "getSourceTables";

export const useGetSourceTables = <T, R = GetSourceTableResponse<T>>(
  params: GetSourceTableParams,
  select?: (data: GetSourceTableResponse<T>) => R,
  refetchOnMount = false
) =>
  useQuery({
    queryKey: [getSourceTablesKey, params],
    queryFn: () => ComplianceService.SourceTable.list<T>(params),
    select,
    refetchOnMount
  });

export const useGetInfiniteSourceTables = <T extends SourceTable>(
  params: GetSourceTableParams,
  refetchOnMount = false
) => {
  const result = useInfiniteQuery({
    queryKey: [getSourceTablesKey, params],
    queryFn: ({ pageParam }) =>
      ComplianceService.SourceTable.list<T>({
        ...params,
        page: pageParam
      }),
    initialPageParam: 1,
    getNextPageParam: infiniteQueryMapper,
    refetchOnMount,
    select: (data) => flattenQueryResults("data", data.pages)
  });

  return result;
};
