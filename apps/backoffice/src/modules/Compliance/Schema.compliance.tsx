import {
  CanadaAntiDumping,
  CanadaGstExemptCode,
  CanadaOgd,
  CanadaSimaCode,
  CanadaTariff,
  CanadaTreatmentCode,
  CanadaVfdCode,
  MatchingConditionOperator,
  MatchingRuleOperator,
  OgdFiling
} from "nest-modules";
import { TableSchema } from "utils";
import { array, boolean, mixed, number, object, ObjectSchema, string } from "yup";
import {
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleFormParams,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus,
  MatchingRuleTable,
  ProductTable,
  SourceTable
} from "./Types.compliance";
import { ComplianceLabel } from "./components";

export const complianceRuleTableSchema: TableSchema<MatchingRuleTable> = {
  name: {
    header: "Name",
    style: "max-w-80 whitespace-normal break-words",
    readOnly: true,
    visible: true
  },
  status: {
    header: "Status",
    style: "w-fit",
    renderer: ({ value }: { value: MatchingRuleStatus }) => <ComplianceLabel value={value} />,
    readOnly: true,
    visible: true
  },
  description: {
    header: "Description",
    style: "max-w-80 whitespace-normal break-words",
    visible: true
  },
  priority: {
    header: "Priority",
    visible: true
  },
  sourceTable: {
    header: "Source Table",
    renderer: "pascalCase",
    visible: true
  },
  sourceName: {
    header: "Source Record",
    style: "max-w-80 truncate",
    visible: true,
    sortKey: "sourceId"
  },
  expiryDate: {
    header: "Expiry Date",
    renderer: "dateLocal",
    visible: true
  },
  isGlobal: {
    header: "Global",
    renderer: "boolean",
    visible: true,
    disableSort: true
  },
  organizationName: {
    header: "Organization",
    visible: true
  },
  createDate: {
    header: "Created Date",
    renderer: "date"
  }
};

//#region Source table schema
export const antiDumpingSchema: TableSchema<CanadaAntiDumping> = {
  code: {
    header: "Code",
    style: "uppercase",
    visible: true
  },
  case: {
    header: "Case",
    style: "uppercase",
    visible: true
  },
  dumpingCountry: {
    header: "Dumping Country",
    visible: true
  },
  subsidyCountry: {
    header: "Subsidy Country",
    visible: true
  },
  caseType: {
    header: "Case Type",
    visible: true
  },
  productDefinition: {
    header: "Product Definition",
    visible: true
  },
  exclusion: {
    header: "Exclusion",
    visible: true
  },
  hsCodes: {
    header: "HS Codes",
    visible: true
  }
};
export const ogdSchema: TableSchema<CanadaOgd> = {
  agency: {
    header: "Agency",
    style: "uppercase",
    visible: true
  },
  program: {
    header: "Program",
    visible: true
  },
  commodityType: {
    header: "Commodity Type",
    visible: true
  }
};
export const simaCodeSchema: TableSchema<CanadaSimaCode> = {
  code: {
    header: "Code",
    style: "uppercase",
    visible: true
  },
  explanation: {
    header: "Explanation",
    visible: true
  }
};
export const canadaTariffSchema: TableSchema<CanadaTariff> = {
  hsCode: {
    header: "HS Code",
    style: "uppercase",
    visible: true
  },
  description: {
    header: "Description",
    style: "max-w-80 whitespace-normal",
    visible: true
  },
  uom: {
    header: "UOM",
    visible: true
  },
  expiryDate: {
    header: "Expiry Date",
    renderer: "date",
    visible: true
  },
  formattedHsCode: {
    header: "Formatted HS Code",
    visible: true
  }
};
export const treatmentCodeSchema: TableSchema<CanadaTreatmentCode> = {
  code: {
    header: "Code",
    style: "uppercase",
    visible: true
  },
  name: {
    header: "Name",
    visible: true
  },
  abbreviation: {
    header: "Abbreviation",
    visible: true
  }
};
export const vfdCodeSchema: TableSchema<CanadaVfdCode> = {
  code: {
    header: "Code",
    style: "uppercase",
    visible: true
  },
  explanation: {
    header: "Explanation",
    visible: true
  }
};
export const gstExemptCodeSchema: TableSchema<CanadaGstExemptCode> = {
  code: {
    header: "Code",
    style: "uppercase",
    visible: true
  },
  explanation: {
    header: "Explanation",
    visible: true
  }
};
export const ogdFilingSchema: TableSchema<OgdFiling> = {
  extensionCode: {
    header: "Extension Code",
    visible: true
  },
  endUse: {
    header: "End Use",
    visible: true
  },
  airsType: {
    header: "AIRS Type",
    visible: true
  },
  airsReferenceNumber: {
    header: "AIRS Reference Number",
    visible: true
  },
  productCategory: {
    header: "Product Category",
    visible: true
  },
  generalImportPermit: {
    header: "General Import Permit",
    visible: true
  },
  isExcluded: {
    header: "Excluded",
    renderer: "boolean",
    visible: true
  }
};
//#endregion

// TODO: Move to product module
export const productTableSchema: TableSchema<ProductTable> = {
  partNumber: {
    header: "Part No.",
    visible: true
  },
  hsCode: {
    header: "HS Code",
    visible: true
  },
  sku: {
    header: "SKU",
    visible: true
  },
  upc: {
    header: "UPC",
    visible: true
  },
  originName: {
    header: "Origin",
    visible: true
  },
  vendorCode: {
    header: "Vendor Code",
    visible: true
  },
  description: {
    header: "Description",
    visible: true
  }
};

export const saveMatchingRuleSchema: ObjectSchema<MatchingRuleFormParams> = object({
  id: number().optional(),
  name: string().required("Name is required"),
  priority: number().optional(),
  description: string().optional(),
  sourceTable: mixed<MatchingRuleSourceDatabaseTable>().required("Source is required"),
  sourceId: number().required("Source record is required"),
  sourceRecord: mixed<SourceTable>().optional(),
  destinationTable: mixed<MatchingRuleDestinationDatabaseTable>().required("Destination is required"),
  operator: mixed<MatchingRuleOperator>().optional(),
  organizationId: number().nullable(),
  expiryDate: string().optional().nullable(),
  conditions: array(
    object({
      id: number().optional(),
      attribute: string().required("Field is required"),
      operator: mixed<MatchingConditionOperator>().required("Operator is required"),
      value: string().required("Value is required"),
      isOperationInverted: boolean().defined()
    })
  ).defined(),
  deletedIds: array(number().required("Deleted id is required")).optional()
});
