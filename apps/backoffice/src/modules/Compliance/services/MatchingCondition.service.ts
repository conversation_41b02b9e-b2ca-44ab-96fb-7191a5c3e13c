import { http } from "@/bootstrap/Http.bootstrap";
import { handleError } from "utils";
import type { BatchSaveMatchingConditionParams, MatchingCondition } from "../Types.compliance";

const MATCHING_RULE_ROUTE = "matching-rules";

/**
 * Batch save matching condition
 *
 * @returns
 */
async function batchSave({ id, ...params }: BatchSaveMatchingConditionParams): Promise<MatchingCondition> {
  try {
    return await http.post(`${MATCHING_RULE_ROUTE}/${id}/conditions/batch`, params);
  } catch (error) {
    throw await handleError(error);
  }
}

export default { batchSave };
