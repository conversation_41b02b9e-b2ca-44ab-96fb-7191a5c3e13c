import { parseArrayString } from "utils";
import {
  antiDumpingSchema,
  canadaTariffSchema,
  gstExemptCodeSchema,
  ogdFilingSchema,
  ogdSchema,
  simaCodeSchema,
  treatmentCodeSchema,
  vfdCodeSchema
} from "./Schema.compliance";
import {
  MatchingConditionOperator,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus,
  ProductColumn,
  SaveMatchingConditionParams
} from "./Types.compliance";

export function getSchemaForSourceTable(sourceTable: MatchingRuleSourceDatabaseTable) {
  switch (sourceTable) {
    case MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING:
      return antiDumpingSchema;
    case MatchingRuleSourceDatabaseTable.CANADA_OGD:
      return ogdSchema;
    case MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE:
      return simaCodeSchema;
    case MatchingRuleSourceDatabaseTable.CANADA_TARIFF:
      return canadaTariffSchema;
    case MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE:
      return treatmentCodeSchema;
    case MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE:
      return vfdCodeSchema;
    case MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE:
      return gstExemptCodeSchema;
    case MatchingRuleSourceDatabaseTable.OGD_FILING:
      return ogdFilingSchema;
    default:
      return ogdFilingSchema;
  }
}

export function getResponseKeyForSourceTable(sourceTable: MatchingRuleSourceDatabaseTable) {
  switch (sourceTable) {
    case MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING:
      return "antiDumpings";
    case MatchingRuleSourceDatabaseTable.CANADA_OGD:
      return "ogds";
    case MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE:
      return "codes";
    case MatchingRuleSourceDatabaseTable.CANADA_TARIFF:
      return "tariffs";
    case MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE:
      return "codes";
    case MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE:
      return "codes";
    case MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE:
      return "codes";
  }
}

export function complianceLabelColor(status?: MatchingRuleStatus) {
  switch (status) {
    case MatchingRuleStatus.PENDING:
      return "warning";
    case MatchingRuleStatus.ACTIVE:
      return "success";
    case MatchingRuleStatus.DISABLED:
      return "disabled";
    default:
      return "gray";
  }
}

export function formatRuleConditions(conditions: SaveMatchingConditionParams[]) {
  return conditions.map((condition) =>
    condition.operator === MatchingConditionOperator.IN
      ? {
          ...condition,
          value: parseArrayString(condition.value)
        }
      : [ProductColumn.PART_NUMBER, ProductColumn.SKU, ProductColumn.UPC].includes(
            condition.attribute as ProductColumn
          )
        ? {
            ...condition,
            value: condition.value ? condition.value.toUpperCase() : condition.value
          }
        : condition
  );
}
