import { BaseError } from "@/common/Types.common";
import SelectCountry from "@/modules/Location/components/SelectCountry";
import { Country } from "@/modules/Location/Types.location";
import { SelectOrganizationModal } from "@/modules/Organization/components";
import { Organization } from "@/modules/Organization/Types.organization";
import { SelectPartnerModal } from "@/modules/TradePartner/components";
import { PartnerType, TradePartner } from "@/modules/TradePartner/Types.partner";
import { FieldArray, FormikProvider, getIn, useFormik } from "formik";
import { PlusIcon, SearchIcon, TrashIcon } from "lucide-react";
import { Product } from "nest-modules";
import { useCallback, useEffect, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { Button, Checkbox, Input, Modal, Popup, Select, StatusLabel, Table, Textarea } from "ui";
import { formikInputOpts, getFormikError, pascalToCapitalize } from "utils";
import {
  DESTINATION_TABLE,
  OPERATOR,
  PRIORITY,
  PRODUCT_COLUMN,
  SOURCE_TABLE
} from "../../Constant.compliance";
import { useSearchMatchingEntries } from "../../mutations";
import { productTableSchema, saveMatchingRuleSchema } from "../../Schema.compliance";
import {
  MatchingConditionOperator,
  MatchingRule,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleFormParams,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus,
  ProductColumn,
  SearchMatchingEntriesParams,
  SourceTable
} from "../../Types.compliance";
import ComplianceLabel from "../ComplianceLabel";
import SelectSourceModal from "../SelectSourceModal";

const newCondition = {
  attribute: ProductColumn.PART_NUMBER,
  operator: MatchingConditionOperator.EQUALS,
  value: "",
  isOperationInverted: false,
  valueRecord: null,
  id: undefined
};
type Props = {
  show: boolean;
  info?: MatchingRule;
  isLoading?: boolean;
  onSubmit(params: MatchingRuleFormParams): void;
  onUpdateStatus(status: MatchingRuleStatus): void;
  onDelete(): void;
  onClose(): void;
};
function AddComplianceModal({ show, info, isLoading, onSubmit, onUpdateStatus, onClose, onDelete }: Props) {
  const [modal, setModal] = useState<MatchingRuleSourceDatabaseTable>();
  const [source, setSource] = useState<SourceTable>();
  const [searchResults, setSearchResults] = useState<Product[]>();
  const { showPopup, hidePopup } = Popup.usePopup();

  const [partnerModal, togglePartnerModal] = useReducer((r) => !r, false);
  const [organizationModal, toggleOrganizationModal] = useReducer((r) => !r, false);
  const [partnerIndex, setPartnerIndex] = useState<number>();
  const [vendor, setVendor] = useState<Map<number, TradePartner>>(new Map());
  const [organization, setOrganization] = useState<Organization>();

  const searchEntries = useSearchMatchingEntries();

  const { setFieldValue, ...formik } = useFormik({
    initialValues: {
      id: info?.id,
      name: info?.name || "",
      priority: info?.priority,
      description: info?.description || "",
      sourceTable: info?.sourceTable,
      sourceId: info?.sourceId,
      sourceRecord: info?.sourceRecord as SourceTable,
      destinationTable: info?.destinationTable || MatchingRuleDestinationDatabaseTable.PRODUCT,
      organizationId: info?.organization?.id,
      expiryDate: info?.expiryDate,
      conditions: info?.conditions?.map((condition) => ({
        ...condition,
        value: condition.value ?? undefined
      })) || [newCondition],
      deletedIds: []
    },
    validationSchema: saveMatchingRuleSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit: onSubmit
  });

  const generateRuleName = useCallback(() => {
    const _source = formik.values.sourceTable;
    const _record = formik.values.sourceRecord;
    const _conditions = formik.values.conditions;

    if (!_source || !_conditions?.length) return;

    const sourceIdentifier = _record
      ? [_record.case, _record.agency, _record.hsCode, _record.code, _record.id].find((val) => val) +
          (formik.values.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_OGD
            ? ` ${_record.program ?? ""}`
            : "") || ""
      : "";

    const conditionsText = _conditions
      .filter((c) => c.value)
      .map((c) => {
        const value = c.isOperationInverted ? `NOT ${c.value}` : c.value;
        return `${pascalToCapitalize(c.attribute)} ${c.operator} ${value}`;
      })
      .join(" AND ");

    const ruleName = `${_source} - ${sourceIdentifier} - ${conditionsText}`.trim();
    setFieldValue("name", ruleName);
  }, [formik.values.conditions, formik.values.sourceRecord, formik.values.sourceTable, setFieldValue]);

  useEffect(() => {
    if (!info) {
      generateRuleName();
    }
  }, [
    formik.values.sourceTable,
    formik.values.sourceRecord,
    formik.values.conditions,
    generateRuleName,
    info
  ]);
  useEffect(() => {
    if (info?.organization) {
      setOrganization(info.organization);
    }
  }, [info?.organization]);

  const handleSearchEntries = useCallback(async () => {
    if (!formik.isValid) return;

    const params: SearchMatchingEntriesParams = {
      destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT,
      conditions: formik.values.conditions
    };

    try {
      const results = await searchEntries.mutateAsync(params);

      const formattedResults = results.destinationRecords.map((record) => ({
        ...record,
        originName: record.origin?.name,
        vendorCode: record.vendor?.vendorCode
      }));

      setSearchResults(formattedResults);
    } catch (error) {
      toast.error((error as BaseError).message);
    }
  }, [formik.isValid, formik.values.conditions, searchEntries]);

  const handleDelete = () => {
    showPopup(
      {
        content: "Are you sure you want to delete this rule?",
        onProceed: () => {
          onDelete();
          hidePopup();
        }
      },
      Popup.PopupType.DELETE_POPUP
    );
  };

  return (
    <Modal
      id="compliance-modal"
      show={show}
      onClose={() => {
        formik.resetForm();
        setSource(undefined);
        setSearchResults(undefined);
        onClose();
      }}
      size="4xl"
      title={
        <div className="flex items-center gap-2">
          <h5>{info ? info.status === MatchingRuleStatus.PENDING && "Edit" : "Create"} Compliance Rule</h5>
          {info && <ComplianceLabel value={info.status} />}
          {info?.isGlobal && <StatusLabel text="Global Rule" color="info" />}
        </div>
      }
      actions={
        <>
          {info && info.status === MatchingRuleStatus.PENDING && (
            <>
              <Button
                label="Delete"
                kind="delete"
                loading={isLoading}
                disabled={formik.dirty}
                onClick={handleDelete}
              />
              <Button
                label="Publish"
                loading={isLoading}
                className="bg-success hover:bg-success-600"
                disabled={formik.dirty}
                onClick={() => onUpdateStatus(MatchingRuleStatus.ACTIVE)}
              />
            </>
          )}

          {(!info || info?.status === MatchingRuleStatus.PENDING) && (
            <Button label={info ? "Update" : "Create"} loading={isLoading} onClick={formik.handleSubmit} />
          )}

          {info?.status === MatchingRuleStatus.ACTIVE && (
            <Button
              label="Edit"
              loading={isLoading}
              onClick={() => onUpdateStatus(MatchingRuleStatus.PENDING)}
            />
          )}
        </>
      }
    >
      {modal && (
        <SelectSourceModal
          show={!!modal}
          source={modal}
          onClose={() => setModal(undefined)}
          onSelect={(data) => {
            setSource(data);
            setFieldValue("sourceId", data?.id);
            setFieldValue("sourceRecord", data);
            setModal(undefined);
          }}
        />
      )}
      {partnerModal && (
        <SelectPartnerModal
          show={!!partnerModal}
          onClose={togglePartnerModal}
          partnerType={PartnerType.VENDOR}
          onSelect={(_partner: TradePartner) => {
            setFieldValue(`conditions.${partnerIndex}.value`, _partner.id.toString());
            const map = new Map(vendor);
            map.set(_partner.id, _partner);
            setVendor(map);

            setPartnerIndex(undefined);
            togglePartnerModal();
          }}
          required
        />
      )}
      {organizationModal && (
        <SelectOrganizationModal
          show={organizationModal}
          selected={!!organization}
          onClose={toggleOrganizationModal}
          onSelect={(_organization?: Organization) => {
            setFieldValue(`organizationId`, _organization?.id?.toString());
            setOrganization(_organization);
            toggleOrganizationModal();
          }}
        />
      )}

      <form className="flex flex-col divide-y">
        <fieldset disabled={info?.status === MatchingRuleStatus.ACTIVE}>
          <div className="flex gap-3 py-2">
            <Input
              label="Rule Name"
              placeholder="Input name"
              containerStyle="flex-1"
              {...formikInputOpts(formik, "name")}
            />
            <Textarea
              label="Description"
              placeholder="Input description"
              containerStyle="flex-1"
              {...formikInputOpts(formik, "description")}
            />
          </div>
          <div className="flex gap-3 pb-2">
            <Select
              label="Priority"
              options={PRIORITY}
              {...formikInputOpts(formik, "priority")}
              info="10 is the highest priority"
              optional
            />
            <Input
              label="Expiry Date"
              placeholder="Input Expiry Date"
              type="date"
              {...formikInputOpts(formik, "expiryDate")}
            />
            <Input
              label="Organization"
              name={`organizationId`}
              placeholder="Select organization"
              value={organization?.name ?? ""}
              error={getFormikError(formik, `organizationId`)}
              onClick={toggleOrganizationModal}
              readOnly
            />
          </div>

          <div className="py-2">
            <h5 className="mb-1">Source</h5>
            <div className="flex gap-3">
              <Select
                label="Source Table"
                containerStyle="flex-1"
                options={SOURCE_TABLE}
                {...formikInputOpts(formik, "sourceTable", (e) => {
                  formik.handleChange(e);
                  setFieldValue("sourceId", undefined);
                  setSource(undefined);
                })}
                optional
              />
              <Input
                label="Source Record"
                name="sourceId"
                placeholder="Select source table first"
                kind="search"
                containerStyle="flex-1"
                value={
                  source
                    ? (source.displayName ?? source.case ?? source.agency ?? source.hsCode ?? source.code)
                    : (formik.values.sourceId ?? "")
                }
                error={getFormikError(formik, "sourceId")}
                disabled={!formik.values.sourceTable}
                readOnly
                onClick={() => setModal(formik.values.sourceTable)}
              />
              <Select label="Destination Table" options={DESTINATION_TABLE} disabled />
            </div>
          </div>

          <FormikProvider value={{ ...formik, setFieldValue }}>
            <FieldArray
              name="conditions"
              render={(arrayHelpers) => (
                <div className="flex flex-col gap-2 py-2">
                  <div className="col-span-2 flex items-center justify-between gap-2">
                    <h5>Conditions</h5>
                    <Button
                      kind="outline"
                      label="Add"
                      icon={<PlusIcon className="size-4" />}
                      className="py-1 px-2"
                      onClick={() => arrayHelpers.push(newCondition)}
                    />
                  </div>
                  {formik.values.conditions.map((condition, index) => (
                    <div className="flex gap-3" key={index}>
                      <Select
                        label="Field"
                        containerStyle="flex-1"
                        options={PRODUCT_COLUMN}
                        {...formikInputOpts(formik, `conditions.${index}.attribute`, (e) => {
                          formik.handleChange(e);
                          setFieldValue(`conditions.${index}.operator`, MatchingConditionOperator.EQUALS);
                          setFieldValue(`conditions.${index}.value`, "");
                        })}
                      />
                      {condition.attribute !== ProductColumn.ORIGIN &&
                        condition.attribute !== ProductColumn.VENDOR && (
                          <>
                            <Select
                              label="Operator"
                              containerStyle="flex-1"
                              options={OPERATOR}
                              {...formikInputOpts(formik, `conditions.${index}.operator`)}
                            />
                            <Input
                              label="Value"
                              placeholder="Input value"
                              containerStyle="flex-1"
                              {...formikInputOpts(formik, `conditions.${index}.value`)}
                            />
                          </>
                        )}

                      {condition.attribute === ProductColumn.ORIGIN && (
                        <SelectCountry
                          name={`conditions.${index}.value`}
                          placeholder="Select Origin"
                          containerStyle="flex-1"
                          value={condition.valueRecord as Country}
                          onSelect={(value) => setFieldValue(`conditions.${index}.value`, value?.toString())}
                          error={getFormikError(formik, `conditions.${index}.value`)}
                        />
                      )}
                      {condition.attribute === ProductColumn.VENDOR && (
                        <Input
                          label="Vendor"
                          name={`conditions.${index}.value`}
                          placeholder="Select vendor"
                          containerStyle="flex-1"
                          value={
                            vendor.get(Number(condition.value))?.name ??
                            getIn(formik.values, `conditions.${index}.valueRecord.name`) ??
                            ""
                          }
                          error={getFormikError(formik, `conditions.${index}.value`)}
                          onClick={() => {
                            setPartnerIndex(index);
                            togglePartnerModal();
                          }}
                          readOnly
                        />
                      )}

                      <Checkbox
                        label="Inverted"
                        name={`conditions.${index}.isOperationInverted`}
                        containerStyle="mt-6 flex-1"
                        checked={condition.isOperationInverted ?? false}
                        onChange={formik.handleChange}
                      />

                      {(!info || info.status === MatchingRuleStatus.PENDING) && (
                        <TrashIcon
                          className="mt-6 ml-auto size-5 cursor-pointer text-danger"
                          onClick={() => {
                            const id = condition.id;
                            if (id) {
                              setFieldValue("deletedIds", [...(formik.values.deletedIds || []), id]);
                            }
                            arrayHelpers.remove(index);
                          }}
                        />
                      )}
                    </div>
                  ))}
                </div>
              )}
            />
          </FormikProvider>
        </fieldset>

        <div className="flex flex-col gap-3 py-2">
          <Button
            label="Search Matching Entries"
            kind="outline"
            icon={<SearchIcon size={16} />}
            loading={searchEntries.isPending}
            onClick={handleSearchEntries}
            className="w-fit"
            disabled={!formik.isValid}
          />

          {searchResults && (
            <Table schema={productTableSchema} data={searchResults} isLoading={searchEntries.isPending} />
          )}
        </div>
      </form>
    </Modal>
  );
}
export default AddComplianceModal;
