import { BOOLEAN_OPTIONS, ORDER_BY } from "@/common/Constant.common";
import { useDebounce } from "@uidotdev/usehooks";
import { useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";
import { Card, Input, Select } from "ui";
import { OrderBy, SortOrder } from "utils";
import { MATCHING_RULE_SORT_BY, MATCHING_RULE_STATUS, SOURCE_TABLE } from "../../Constant.compliance";
import { complianceRuleTableSchema } from "../../Schema.compliance";
import {
  type GetMatchingRuleList,
  type MatchingRuleSourceDatabaseTable,
  type MatchingRuleStatus
} from "../../Types.compliance";

type Props = {
  isOpen: boolean;
  filterValues(values?: GetMatchingRuleList): void;
};
const ComplianceFilter = ({ isOpen, filterValues }: Props) => {
  const [values, setValues] = useState<GetMatchingRuleList>();

  const [_name, setName] = useState<string>();
  const [_priority, setPriority] = useState<number>();
  const name = useDebounce(_name, 500);
  const priority = useDebounce(_priority, 500);

  useEffect(() => {
    setValues((prev) => ({
      ...prev,
      name: name ?? undefined,
      priority: priority ?? undefined
    }));
  }, [name, priority]);

  useEffect(() => {
    filterValues(values);
  }, [filterValues, values]);

  useEffect(() => () => setValues(undefined), []);

  return (
    <div
      className={twMerge(
        "overflow-hidden transition-max-height duration-300 ease-in-out",
        isOpen ? "max-h-96" : "max-h-0"
      )}
    >
      {isOpen && (
        <Card containerStyle="flex flex-wrap items-center gap-x-3 gap-y-2 px-6 py-4 border">
          <div className="flex gap-3">
            <Input label="Name" placeholder="Search" onTextChange={setName} kind="search" />
            <Select
              label="Status"
              onSelected={(status: MatchingRuleStatus) => setValues((prev) => ({ ...prev, status }))}
              options={MATCHING_RULE_STATUS}
              optional
            />
            <Input
              label="Priority"
              placeholder="Input priority"
              onNumberChange={(e) =>
                setPriority(e.currentTarget.value ? Number(e.currentTarget.value) : undefined)
              }
            />
            <Select
              label="Source"
              onSelected={(sourceTable: MatchingRuleSourceDatabaseTable) =>
                setValues((prev) => ({ ...prev, sourceTable }))
              }
              options={SOURCE_TABLE}
              optional
            />
            <Input
              label="Expiry Date From"
              type="date"
              onTextChange={(expiryDateFrom) => setValues((prev) => ({ ...prev, expiryDateFrom }))}
            />
            <Input
              label="Expiry Date To"
              type="date"
              onTextChange={(expiryDateTo) => setValues((prev) => ({ ...prev, expiryDateTo }))}
            />
            <Select
              label="Global Rule"
              onSelected={(isGlobal: boolean) => setValues((prev) => ({ ...prev, isGlobal }))}
              options={BOOLEAN_OPTIONS}
              optional
            />
          </div>

          <div className="flex gap-3">
            <Select
              label="Sort By"
              onSelected={(sortBy: keyof typeof complianceRuleTableSchema) =>
                setValues((prev) => ({ ...prev, sortBy }) as GetMatchingRuleList)
              }
              options={MATCHING_RULE_SORT_BY}
              defaultValue={"createDate"}
              optional
            />
            <Select
              label="Sort Direction"
              onSelected={(sortOrder: OrderBy) =>
                setValues((prev) => ({ ...prev, sortOrder }) as GetMatchingRuleList)
              }
              options={ORDER_BY}
              defaultValue={SortOrder.DESC}
              optional
            />
          </div>
        </Card>
      )}
    </div>
  );
};
export default ComplianceFilter;
