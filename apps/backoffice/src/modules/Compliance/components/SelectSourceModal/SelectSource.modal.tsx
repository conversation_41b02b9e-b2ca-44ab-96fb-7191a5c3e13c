import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { useMemo, useState } from "react";
import { Button, Modal, Table } from "ui";
import { camelToKebabCase } from "utils";
import { useGetInfiniteSourceTables } from "../../queries";
import { MatchingRuleSourceDatabaseTable, SourceTable } from "../../Types.compliance";
import { getSchemaForSourceTable } from "../../Utils.compliance";

type Props = {
  show: boolean;
  required?: boolean;
  multiple?: boolean;
  source: MatchingRuleSourceDatabaseTable;
  onSelect(item?: SourceTable): void;
  onClose(): void;
};
function SelectSourceModal({ show, required, multiple, source, onSelect, onClose }: Props) {
  const [selected, setSelected] = useState<SourceTable>();

  const { endpoint, schema } = useMemo(
    () => ({
      endpoint: `${camelToKebabCase(source)}s`,
      schema: getSchemaForSourceTable(source)
    }),
    [source]
  );

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useGetInfiniteSourceTables<SourceTable>({
      url: endpoint,
      limit: DEFAULT_LIMIT,
      ...(source === MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING && {
        sortBy: "case"
      })
    });

  const handleSelect = () => {
    if (!multiple) onSelect(selected);
  };

  return (
    <Modal
      id="select-source-modal"
      show={show}
      onClose={onClose}
      title="Select Source Data"
      size="4xl"
      actions={
        <>
          {!required && (
            <Button
              label="Clear"
              kind="cancel"
              onClick={() => {
                setSelected(undefined);
                onSelect(undefined);
              }}
            />
          )}

          <Button label="Select" onClick={handleSelect} disabled={!data?.length || !selected} />
        </>
      }
    >
      <div className="flex flex-col gap-4">
        <Table
          data={data}
          schema={schema}
          checklist
          selected={selected ? [selected] : []}
          onClick={setSelected}
          isLoading={isLoading}
          onEndReached={fetchNextPage}
          hasNextPage={hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
        />
      </div>
    </Modal>
  );
}
export default SelectSourceModal;
