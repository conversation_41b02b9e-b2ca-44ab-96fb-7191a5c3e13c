import { StatusLabel } from "ui";
import { kebabCaseToCapitalize } from "utils";
import type { MatchingRuleStatus } from "../../Types.compliance";
import { complianceLabelColor } from "../../Utils.compliance";

type Props = { value: MatchingRuleStatus };
function ComplianceLabel({ value }: Props) {
  const text = kebabCaseToCapitalize(value, true);
  const color = complianceLabelColor(value);
  return <StatusLabel text={text} color={color} className="w-fit" />;
}
export default ComplianceLabel;
