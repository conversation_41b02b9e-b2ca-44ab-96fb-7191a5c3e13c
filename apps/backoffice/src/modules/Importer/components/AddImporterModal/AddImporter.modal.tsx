import { Ban } from "lucide-react";
import { useRef } from "react";
import { useLocation } from "react-router-dom";
import { Button, FieldItem, Modal } from "ui";
import {
  CarmStatus,
  ImporterFormRef,
  ImporterStatus,
  type Importer,
  type SaveImporterParams
} from "../../Types.importer";
import CarmLabel from "../CarmLabel";
import ImporterForm from "../ImporterForm";
import ImporterLabel from "../ImporterLabel";

type Props = {
  show: boolean;
  info?: Importer;
  isLoading?: boolean;
  isDeleteLoading?: boolean;
  isCarmLoading?: boolean;
  isResendPoaLoading?: boolean;
  invalidBusinessNumber?: boolean;
  onSubmit(importer: SaveImporterParams): void;
  onDelete(): void;
  onClose(): void;
  onUpdateStatus(status: ImporterStatus): void;
  onRequestCarm(): void;
  onResendPoa(): void;
};
function AddImporterModal({
  show,
  info,
  isLoading,
  isDeleteLoading,
  isCarmLoading,
  isResendPoaLoading,
  invalidBusinessNumber,
  onSubmit,
  onDelete,
  onClose,
  onUpdateStatus,
  onRequestCarm,
  onResendPoa
}: Props) {
  const ref = useRef<ImporterFormRef>(null);
  const { state } = useLocation();

  return (
    <Modal
      id="importer-modal"
      show={show}
      size="4xl"
      onClose={() => {
        ref.current?.resetForm();
        onClose();
      }}
      title={
        <div className="flex items-center gap-2">
          <h5>{`${info ? "Edit" : "Add New"} Importer`}</h5>
          {info && <ImporterLabel value={info.status} />}
          {info && (
            <FieldItem
              label="Carm Status:"
              customElement={<CarmLabel value={info?.carmStatus as CarmStatus} />}
              direction="row"
            />
          )}
        </div>
      }
      actions={
        <>
          {info &&
            (!info?.carmStatus || info.carmStatus === CarmStatus.INVALID) &&
            info.status !== ImporterStatus.DISABLED && (
              <Button
                label="Request Carm"
                loading={isCarmLoading}
                onClick={onRequestCarm}
                disabled={isLoading || isDeleteLoading || isResendPoaLoading}
              />
            )}
          {info && info.status === ImporterStatus.PENDING_POA && (
            <Button
              label="Resend POA"
              loading={isResendPoaLoading}
              onClick={onResendPoa}
              disabled={ref.current?.dirty || isLoading || isDeleteLoading || isCarmLoading}
            />
          )}
          {info &&
            (info.status === ImporterStatus.DISABLED || info.status === ImporterStatus.PENDING_POA) && (
              <Button
                label="Delete"
                kind="delete"
                loading={isDeleteLoading}
                disabled={ref.current?.dirty || isLoading || isCarmLoading || isResendPoaLoading}
                onClick={onDelete}
              />
            )}
          {info && info.status === ImporterStatus.ACTIVE && (
            <Button
              label="Disable"
              kind="delete"
              className="ml-auto"
              icon={<Ban size={16} />}
              loading={isLoading}
              disabled={ref.current?.dirty || isDeleteLoading || isCarmLoading || isResendPoaLoading}
              onClick={() => onUpdateStatus(ImporterStatus.DISABLED)}
            />
          )}
          {info && info.status === ImporterStatus.DISABLED && (
            <Button
              label="Enable"
              className="ml-auto bg-success-600"
              loading={isLoading}
              disabled={isDeleteLoading || isCarmLoading || isResendPoaLoading}
              onClick={() => onUpdateStatus(ImporterStatus.ACTIVE)}
            />
          )}
          {info?.status !== ImporterStatus.DISABLED && (
            <Button
              label={info ? "Update" : "Add"}
              kind="update"
              loading={isLoading}
              disabled={isDeleteLoading || isCarmLoading || isResendPoaLoading}
              onClick={() => ref.current?.handleSubmit()}
            />
          )}
        </>
      }
    >
      <div className="flex flex-col gap-3">
        <ImporterForm
          ref={ref}
          importer={info}
          isLoading={isLoading || isDeleteLoading || isCarmLoading || isResendPoaLoading}
          onSubmit={onSubmit}
          invalidBusinessNumber={invalidBusinessNumber}
          organization={state?.organization}
        />
      </div>
    </Modal>
  );
}
export default AddImporterModal;
