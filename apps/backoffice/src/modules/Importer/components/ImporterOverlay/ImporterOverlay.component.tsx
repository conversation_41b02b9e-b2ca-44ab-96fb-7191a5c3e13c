import { Ban } from "lucide-react";
import { useCallback } from "react";
import toast from "react-hot-toast";
import { <PERSON>ton, FieldItem, Overlay, Popup, Textarea } from "ui";
import { ImporterStatus, VerifyImporterParams, type Importer } from "../../Types.importer";
import ImporterLabel from "../ImporterLabel";

type Props = {
  show: boolean;
  info?: Importer;
  isLoading?: boolean;
  onSubmit(importer: VerifyImporterParams): void;
  onUpdateStatus(status: ImporterStatus): void;
  onClose(): void;
};
function ImporterOverlay({ show, info, isLoading, onSubmit, onUpdateStatus, onClose }: Props) {
  const { showPopup, hidePopup } = Popup.usePopup();

  const handleReject = useCallback(() => {
    let rejectReason: string | undefined;
    showPopup(
      {
        content: (
          <div className="flex flex-col gap-2">
            <div>
              Are you sure you want to reject <b>{info?.companyName}</b>?
            </div>
            <div>
              <Textarea
                label="Reject Reason"
                onTextChange={(text) => {
                  rejectReason = text;
                }}
              />
            </div>
          </div>
        ),

        onProceed: async () => {
          if (!rejectReason) {
            toast.error("Please include reject reason!");
            return;
          }
          onSubmit({ id: info?.id, isVerified: false, rejectReason });
          hidePopup();
        }
      },
      Popup.PopupType.DELETE_POPUP
    );
  }, [hidePopup, info?.companyName, info?.id, onSubmit, showPopup]);

  const handleUpdateStatus = (status: ImporterStatus) => {
    showPopup(
      {
        content: `Are you sure you want to ${
          status === ImporterStatus.ACTIVE ? "enable" : "disable"
        } this importer?`,
        onProceed: () => {
          onUpdateStatus(status);
          hidePopup();
        }
      },
      status === ImporterStatus.DISABLED ? Popup.PopupType.DELETE_POPUP : undefined
    );
  };

  return (
    <Overlay
      show={show}
      onClose={onClose}
      title={"Importer"}
      id="importer-overlay"
      containerStyle="max-w-md"
      header={
        <>
          {info?.status === ImporterStatus.PENDING_VERIFICATION ? (
            <div className="flex-1 justify-end flex gap-3">
              <Button
                loading={isLoading}
                label="Approve"
                onClick={() => onSubmit({ id: info?.id, isVerified: true })}
              />
              <Button loading={isLoading} label="Reject" onClick={handleReject} className="bg-danger" />
            </div>
          ) : (
            info?.status && <ImporterLabel value={info.status} />
          )}
          {info && info.status === ImporterStatus.ACTIVE && (
            <Button
              label="Disable"
              kind="delete"
              className="ml-auto"
              icon={<Ban size={16} />}
              loading={isLoading}
              onClick={() => handleUpdateStatus(ImporterStatus.DISABLED)}
            />
          )}
          {info && info.status === ImporterStatus.DISABLED && (
            <Button
              label="Enable"
              className="ml-auto bg-success-600"
              loading={isLoading}
              onClick={() => handleUpdateStatus(ImporterStatus.ACTIVE)}
            />
          )}
        </>
      }
      closeButton
    >
      <div className="grid grid-cols-2 p-4 gap-2">
        <FieldItem label="Company Name" value={info?.companyName} />
        <FieldItem label="Business Number" value={info?.businessNumber} />
        <FieldItem label="Email" value={info?.email} />
        <FieldItem label="Phone Number" value={info?.phoneNumber} />
        <FieldItem label="Fax Number" value={info?.fax} />
        <FieldItem label="Address" value={info?.address} />
        <FieldItem label="City" value={info?.city} />
        <FieldItem label="State" value={info?.state} />
        <FieldItem label="Postal Code" value={info?.postalCode} />
        <FieldItem label="Country" value={info?.country.name} />
        <FieldItem
          label="Officer Name and Title"
          value={info?.officerNameAndTitle}
          containerStyle="col-span-2"
        />
        <FieldItem label="Receiver Email" value={info?.receiveEmail} containerStyle="col-span-2" />
        {info?.rejectReason && (
          <FieldItem label="Reject Reason" value={info.rejectReason} containerStyle="col-span-2" />
        )}
      </div>
    </Overlay>
  );
}
export default ImporterOverlay;
