import { parseAddress } from "@/common/Utils.common";
import { SelectCountry } from "@/modules/Location/components";
import { Country } from "@/modules/Location/Types.location";
import { useFormik } from "formik";
import { forwardRef, useCallback, useEffect, useImperativeHandle, useReducer, useState } from "react";
import { AddressAutoComplete, FieldItem, Input, InvalidBusinessNumber, PhoneNumberInput } from "ui";
import { formikInputOpts, getFormikError } from "utils";
import { saveImporterSchema } from "../../Schema.importer";
import { Importer, ImporterFormRef, ImporterStatus, SaveImporterParams } from "../../Types.importer";
import { env } from "@/config/Environment.config";
import { SelectOrganizationModal } from "@/modules/Organization/components";
import { Organization } from "@/modules/Organization/Types.organization";

type Props = {
  importer?: Importer;
  isLoading?: boolean;
  invalidBusinessNumber?: boolean;
  organization?: Organization;
  onSubmit: (values: SaveImporterParams) => void;
};
const ImporterForm = forwardRef<ImporterFormRef, Props>(
  ({ importer, isLoading, invalidBusinessNumber, onSubmit, organization: org }: Props, ref) => {
    const [country, setCountry] = useState<Country>();
    const [organizationModal, toggleOrganizationModal] = useReducer((r) => !r, false);
    const [organization, setOrganization] = useState<Organization | undefined>(org);

    useEffect(() => {
      setCountry(importer?.country);
    }, [importer?.country]);

    useEffect(() => {
      return () => setCountry(undefined);
    }, []);

    const formik = useFormik({
      initialValues: {
        id: importer?.id,
        companyName: importer?.companyName,
        businessNumber: importer?.businessNumber,
        email: importer?.email,
        phoneNumber: importer?.phoneNumber,
        fax: importer?.fax ?? "",
        address: importer?.address,
        city: importer?.city,
        state: importer?.state,
        postalCode: importer?.postalCode,
        countryId: importer?.country?.id,
        officerNameAndTitle: importer?.officerNameAndTitle,
        candataCustomerNumber: importer?.candataCustomerNumber ?? "",
        organizationId: importer?.organization?.id ?? org?.id,
        organizationType: importer?.organization?.organizationType ?? org?.organizationType
      },
      validationSchema: saveImporterSchema,
      validateOnBlur: true,
      enableReinitialize: true,
      onSubmit
    });

    useImperativeHandle(ref, () => {
      return {
        dirty: formik.dirty,
        handleSubmit: formik.handleSubmit,
        resetForm: formik.resetForm,
        setFieldError: formik.setFieldError
      };
    }, [formik]);

    const handleAddress = useCallback(
      async (place: google.maps.places.PlaceResult) => {
        const address = await parseAddress(place);
        if (address) {
          formik.setFieldValue("address", address.street);
          formik.setFieldValue("city", address.city);
          formik.setFieldValue("state", address.state);
          formik.setFieldValue("postalCode", address.zipCode);
          formik.setFieldValue("countryId", address.country?.id);
          setCountry(address.country);
        }
      },
      [formik]
    );

    return (
      <div className="flex flex-col gap-3">
        <SelectOrganizationModal
          show={organizationModal}
          onClose={toggleOrganizationModal}
          selected={!!organization}
          onSelect={(_organization?: Organization) => {
            formik.setFieldValue(`organizationId`, _organization?.id);
            formik.setFieldValue(`organizationType`, _organization?.organizationType);
            setOrganization(_organization);

            toggleOrganizationModal();
          }}
        />

        <InvalidBusinessNumber show={invalidBusinessNumber === true} />

        <form>
          <fieldset
            className="flex flex-col gap-2"
            disabled={importer?.status === ImporterStatus.DISABLED || isLoading}
          >
            <h5>Importer Company Information</h5>
            <div className="grid grid-cols-3 lg:grid-cols-4 gap-4">
              <Input
                label="Company Name"
                placeholder="Input company name"
                {...formikInputOpts(formik, "companyName")}
                disabled={!!importer}
              />
              <Input
                label="Business Number"
                placeholder="Input business number"
                {...formikInputOpts(formik, "businessNumber")}
                info="Business number format: 123456789RM0001"
                disabled={!!importer}
              />
              <Input
                label="Candata Customer No."
                placeholder="Input candata customer number"
                {...formikInputOpts(formik, "candataCustomerNumber")}
              />
              <Input
                label="Organization"
                placeholder="Select organization"
                {...formikInputOpts(formik, "organizationId")}
                value={organization?.name ?? importer?.organization?.name ?? ""}
                onClick={toggleOrganizationModal}
                readOnly
                disabled={!!importer || !!org}
              />
            </div>

            <h5 className="mt-2">Importer Contact</h5>
            <div className="grid grid-cols-3 lg:grid-cols-4 gap-4">
              <Input
                label="Email"
                type="email"
                placeholder="Input email"
                {...formikInputOpts(formik, "email")}
              />

              <PhoneNumberInput
                placeholder="Input phone number"
                containerStyle="flex-1"
                {...formikInputOpts(formik, "phoneNumber")}
                onChange={(value) => formik.setFieldValue("phoneNumber", value)}
              />
              <Input label="Fax Number" placeholder="Input fax number" {...formikInputOpts(formik, "fax")} />
              <Input
                label="Officer Name And Title"
                placeholder="Input officer name and title"
                {...formikInputOpts(formik, "officerNameAndTitle")}
              />
            </div>

            <h5 className="mt-2">Importer Address</h5>
            <div className="grid grid-cols-4 gap-4">
              <AddressAutoComplete
                apiKey={env.mapsApiKey}
                label="Address"
                placeholder="Input address"
                containerStyle="col-span-2"
                {...formikInputOpts(formik, "address")}
                onSelected={handleAddress}
              />
              <Input label="City" placeholder="Input city" {...formikInputOpts(formik, "city")} />
              <Input label="State" placeholder="Input state" {...formikInputOpts(formik, "state")} />
              <Input
                label="Postal Code"
                placeholder="Input postal code"
                {...formikInputOpts(formik, "postalCode")}
              />
              <SelectCountry
                label="Country"
                placeholder="Select country"
                value={country}
                onSelect={(value) => formik.setFieldValue("countryId", value?.value)}
                error={getFormikError(formik, "countryId")}
              />
            </div>
          </fieldset>
        </form>

        {importer?.receiveEmail && (
          <>
            <hr />
            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col gap-3">
                <div>
                  <h5>Customs Processing Email</h5>
                  <p className="text-xs text-gray-600">
                    Importers, truckers, freight forwarders, and carriers should send customs clearance
                    documents to this email for fast and secure processing.
                  </p>
                </div>
                <FieldItem
                  containerStyle="bg-grey-light text-primary px-2 py-1 rounded-lg w-fit"
                  value={importer?.receiveEmail}
                />
              </div>
            </div>
          </>
        )}
      </div>
    );
  }
);

export default ImporterForm;
