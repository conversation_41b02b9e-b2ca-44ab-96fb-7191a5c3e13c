import { ORDER_BY } from "@/common/Constant.common";
import { useDebounce } from "@uidotdev/usehooks";
import { useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";
import { Card, Input, Select } from "ui";
import { OrderBy, SortOrder } from "utils";
import { IMPORTER_SORT_BY, IMPORTER_STATUS } from "../../Constant.importer";
import { importerTableSchema } from "../../Schema.importer";
import { GetImporterList, ImporterStatus } from "../../Types.importer";

type Props = {
  status?: ImporterStatus;
  isOpen: boolean;
  filterValues(values?: GetImporterList): void;
};
const ImporterFilter = ({ status, isOpen, filterValues }: Props) => {
  const [values, setValues] = useState<GetImporterList>();

  const [_name, setName] = useState<string>();
  const companyName = useDebounce(_name, 500);

  useEffect(() => {
    setValues((prev) => ({
      ...prev,
      companyName: companyName ?? undefined
    }));
  }, [companyName]);

  useEffect(() => {
    filterValues(values);
  }, [filterValues, values]);

  useEffect(() => () => setValues(undefined), []);

  return (
    <div
      className={twMerge(
        "overflow-hidden transition-max-height duration-300 ease-in-out",
        isOpen ? "max-h-96" : "max-h-0"
      )}
    >
      {isOpen && (
        <Card containerStyle="flex flex-wrap items-center gap-x-3 gap-y-2 px-6 py-4 border">
          <div className="flex gap-3">
            <Input label="Name" placeholder="Search" onTextChange={setName} kind="search" />
            <Select
              label="Status"
              onSelected={(status: ImporterStatus) => setValues((prev) => ({ ...prev, status }))}
              options={IMPORTER_STATUS}
              defaultValue={status}
              disabled={!!status}
              optional
            />
          </div>

          <div className="flex gap-3">
            <Select
              label="Sort By"
              onSelected={(sortBy: keyof typeof importerTableSchema) =>
                setValues((prev) => ({ ...prev, sortBy }) as GetImporterList)
              }
              options={IMPORTER_SORT_BY}
              defaultValue={"createDate"}
              optional
            />
            <Select
              label="Sort Direction"
              onSelected={(sortOrder: OrderBy) =>
                setValues((prev) => ({ ...prev, sortOrder }) as GetImporterList)
              }
              options={ORDER_BY}
              defaultValue={SortOrder.DESC}
              optional
            />
          </div>
        </Card>
      )}
    </div>
  );
};
export default ImporterFilter;
