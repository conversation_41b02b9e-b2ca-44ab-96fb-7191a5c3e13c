import { StatusLabel } from "ui";
import { kebabCaseToCapitalize } from "utils";
import { CarmStatus } from "../../Types.importer";
import { carmLabelColor } from "../../Utils.importer";

type Props = { value?: CarmStatus };
function CarmLabel({ value }: Props) {
  const text = value ? kebabCaseToCapitalize(value, true) : "N/A";
  const color = carmLabelColor(value);

  return <StatusLabel text={text} color={color} className="w-fit" />;
}
export default CarmLabel;
