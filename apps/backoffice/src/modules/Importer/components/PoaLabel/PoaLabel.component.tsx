import { StatusLabel } from "ui";
import { kebabCaseToCapitalize } from "utils";
import { PoaStatus } from "../../Types.importer";
import { poaLabelColor } from "../../Utils.importer";

type Props = { value?: PoaStatus };
function PoaLabel({ value }: Props) {
  const text = value ? kebabCaseToCapitalize(value, true) : "N/A";
  const color = poaLabelColor(value);

  return <StatusLabel text={text} color={color} className="w-fit" />;
}
export default PoaLabel;
