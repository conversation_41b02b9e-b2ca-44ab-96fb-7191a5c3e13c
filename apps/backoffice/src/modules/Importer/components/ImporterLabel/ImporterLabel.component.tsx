import { StatusLabel } from "ui";
import { kebabCaseToCapitalize } from "utils";
import type { ImporterStatus } from "../../Types.importer";
import { importerLabelColor } from "../../Utils.importer";

type Props = { value: ImporterStatus };
function ImporterLabel({ value }: Props) {
  const text = kebabCaseToCapitalize(value, true);
  const color = importerLabelColor(value);
  return <StatusLabel text={text} color={color} className="w-fit" />;
}
export default ImporterLabel;
