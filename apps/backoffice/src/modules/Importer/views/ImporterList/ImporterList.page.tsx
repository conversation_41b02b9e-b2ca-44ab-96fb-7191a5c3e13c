import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { BaseError } from "@/common/Types.common";
import { useCallback, useEffect, useMemo, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { <PERSON><PERSON>, Header, Pagination, Popup, Table } from "ui";
import { AddImporterModal, ImporterFilter, ImporterOverlay } from "../../components";
import {
  useCarmRequest,
  useDeleteImporter,
  useDisableImporter,
  useEnableImporter,
  useResendPoa,
  useSaveImporter,
  useVerifyBusinessNumber,
  useVerifyImporter
} from "../../mutations";
import { useGetImporterList } from "../../queries";
import { importerTableSchema } from "../../Schema.importer";
import {
  GetImporterList,
  Importer,
  ImporterStatus,
  SaveImporterParams,
  VerifyImporterParams
} from "../../Types.importer";
import { emptyStringToNull, SortOrder } from "utils";
import { useLocation, useNavigate } from "react-router-dom";
import { ImporterColumn } from "nest-modules";
import { OrganizationType } from "@/modules/Organization/Types.organization";

function ImporterList() {
  const { showPopup, hidePopup } = Popup.usePopup();
  const { state } = useLocation();
  const navigate = useNavigate();
  const [overlay, toggleOverlay] = useReducer((r) => !r, false);
  const [importer, setImporter] = useState<Importer>();
  const [page, setPage] = useState(1);
  const [filter, toggleFilter] = useReducer((r) => !r, false);
  const [filters, setFilters] = useState<GetImporterList>();
  const [modal, setModal] = useState(false);
  const [isValid, setIsValid] = useState<boolean>();

  const verifyImporter = useVerifyImporter();
  const disableImporter = useDisableImporter();
  const enableImporter = useEnableImporter();
  const saveImporter = useSaveImporter();
  const deleteImporter = useDeleteImporter();
  const verifyBusiness = useVerifyBusinessNumber();
  const carmRequest = useCarmRequest();
  const resendPoa = useResendPoa();

  const loadingState = useMemo(
    () =>
      saveImporter.isPending ||
      verifyBusiness.isPending ||
      disableImporter.isPending ||
      enableImporter.isPending ||
      verifyImporter.isPending,
    [
      disableImporter.isPending,
      enableImporter.isPending,
      saveImporter.isPending,
      verifyBusiness.isPending,
      verifyImporter.isPending
    ]
  );

  const { data, error, isFetching, refetch } = useGetImporterList({
    page,
    limit: DEFAULT_LIMIT,
    sortBy: "createDate" as ImporterColumn,
    sortOrder: SortOrder.DESC,
    ...filters
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  useEffect(() => {
    if (state?.organization) {
      setModal(true);
    }
  }, [state?.organization]);

  const handleSaveImporter = useCallback(
    async (params: SaveImporterParams) => {
      setIsValid(undefined);
      const formatParams: SaveImporterParams = {
        ...emptyStringToNull(params),
        countryId: params.countryId ? Number(params.countryId) : undefined
      };

      try {
        if (!importer && formatParams?.organizationType !== OrganizationType.DEMO) {
          const response = await verifyBusiness.mutateAsync({
            business_number: params?.businessNumber?.slice(0, 9) ?? "",
            business_name: params?.companyName ?? ""
          });

          const _isValid = response.some((item) => item.isValid);

          if (!_isValid && !formatParams.candataCustomerNumber) {
            setIsValid(_isValid);
            toast.error("Invalid business number or company name");
            return;
          }
        }

        await saveImporter.mutateAsync(formatParams);

        const message = `Importer ${formatParams.companyName} is ${
          !importer ? "created" : "updated"
        } successfully`;
        toast.success(message);

        setModal(false);
        setImporter(undefined);

        refetch();
        navigate(location.pathname, {});
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [importer, navigate, refetch, saveImporter, verifyBusiness]
  );

  const handleDeleteImporter = () => {
    showPopup(
      {
        content: `Are you sure you want to delete this importer?`,
        onProceed: async () => {
          try {
            await deleteImporter.mutateAsync({ id: importer?.id });
            toast.success("Importer deleted successfully");

            setModal(false);
            setImporter(undefined);
            refetch();
          } catch (error) {
            toast.error((error as BaseError).message);
          }
          hidePopup();
        }
      },
      Popup.PopupType.DELETE_POPUP
    );
  };

  const handleVerifyImporter = useCallback(
    async (params: VerifyImporterParams) => {
      try {
        await verifyImporter.mutateAsync(params);

        const message = `Importer ${importer?.companyName} is ${params.isVerified ? "Approved" : "Rejected"}`;
        toast.success(message, { duration: 5000 });

        toggleOverlay();

        setImporter(undefined);
        refetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [importer?.companyName, refetch, verifyImporter]
  );

  const handleUpdateStatus = useCallback(
    async (status: ImporterStatus) => {
      showPopup(
        {
          content: `Are you sure you want to ${
            status === ImporterStatus.ACTIVE ? "enable" : "disable"
          } this importer?`,
          onProceed: async () => {
            try {
              if (status === ImporterStatus.DISABLED) {
                await disableImporter.mutateAsync({ id: importer?.id });
                toast.success("Importer disabled successfully");
              } else {
                await enableImporter.mutateAsync({ id: importer?.id });
                toast.success("Importer enabled successfully");
              }

              setModal(false);
              setImporter(undefined);
              refetch();
            } catch (error) {
              toast.error((error as BaseError).message);
            }
            hidePopup();
          }
        },
        status === ImporterStatus.DISABLED ? Popup.PopupType.DELETE_POPUP : undefined
      );
    },
    [disableImporter, enableImporter, hidePopup, importer?.id, refetch, showPopup]
  );

  const handleCarmRequest = useCallback(async () => {
    if (!importer?.id) return;
    try {
      await carmRequest.mutateAsync({ id: importer.id });
      toast.success("Carm request sent successfully");

      setModal(false);
      setImporter(undefined);

      refetch();
    } catch (error) {
      toast.error((error as BaseError).message);
    }
  }, [importer?.id, refetch, carmRequest]);

  const handleResendPoa = useCallback(async () => {
    if (!importer?.id) return;
    try {
      await resendPoa.mutateAsync({ id: importer.id });
      toast.success("POA resent successfully");

      setModal(false);
      setImporter(undefined);

      refetch();
    } catch (error) {
      toast.error((error as BaseError).message);
    }
  }, [importer?.id, refetch, resendPoa]);

  const handleFilter = useCallback((values: GetImporterList) => {
    setFilters(values);
    setPage(1);
  }, []);

  return (
    <div className="flex flex-col">
      <ImporterOverlay
        show={overlay}
        info={importer}
        isLoading={loadingState}
        onClose={toggleOverlay}
        onSubmit={handleVerifyImporter}
        onUpdateStatus={handleUpdateStatus}
      />
      <AddImporterModal
        show={modal}
        info={importer}
        isLoading={loadingState}
        isDeleteLoading={deleteImporter.isPending}
        isCarmLoading={carmRequest.isPending}
        isResendPoaLoading={resendPoa.isPending}
        onClose={() => {
          setModal(false);
          setImporter(undefined);
          navigate(location.pathname, {});
        }}
        onSubmit={handleSaveImporter}
        onDelete={handleDeleteImporter}
        onUpdateStatus={handleUpdateStatus}
        onRequestCarm={handleCarmRequest}
        onResendPoa={handleResendPoa}
        invalidBusinessNumber={isValid === false}
      />

      <Header title="Importer List">
        <Button label="Filters" kind="filter" onClick={toggleFilter} />
        <Button
          label="New Importer"
          kind="create"
          className="ml-auto"
          onClick={() => setModal(true)}
          disabled={loadingState || deleteImporter.isPending || carmRequest.isPending}
        />
      </Header>

      <section>
        <ImporterFilter filterValues={handleFilter} isOpen={filter} />
      </section>

      <main>
        <Table
          data={data?.importers}
          isLoading={isFetching}
          schema={importerTableSchema}
          onClick={(_data: Importer) => {
            setImporter(_data);
            setModal(true);
          }}
          footer={
            <Pagination currentPage={page} total={data?.total} limit={DEFAULT_LIMIT} onPageChange={setPage} />
          }
          total={data?.total}
        />
      </main>
    </div>
  );
}
export default ImporterList;
