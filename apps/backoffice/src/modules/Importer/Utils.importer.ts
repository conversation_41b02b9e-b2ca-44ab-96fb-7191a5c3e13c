import { CarmStatus, <PERSON>mporterStatus, PoaStatus } from "./Types.importer";

export function importerLabelColor(status?: ImporterStatus) {
  switch (status) {
    case ImporterStatus.PENDING_VERIFICATION:
      return "warning";
    case ImporterStatus.PENDING_POA:
      return "info";
    case ImporterStatus.REJECTED:
      return "danger";
    case ImporterStatus.ACTIVE:
      return "success";
    case ImporterStatus.DISABLED:
      return "disabled";
    default:
      return "gray";
  }
}

export function carmLabelColor(status?: CarmStatus) {
  switch (status) {
    case CarmStatus.SENT:
      return "info";
    case CarmStatus.PENDING:
      return "warning";
    case CarmStatus.ACTIVE:
      return "success";
    case CarmStatus.INVALID:
      return "danger";
    default:
      return "gray";
  }
}

export function poaLabelColor(status?: PoaStatus) {
  switch (status) {
    case PoaStatus.SENT:
      return "warning";
    case PoaStatus.SIGNED:
      return "success";
    default:
      return "gray";
  }
}
