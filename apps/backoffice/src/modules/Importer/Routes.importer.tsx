import { RouterObject } from "@/common/Types.common";
import { Container } from "lucide-react";
import { ImporterList } from "./views";

const ImporterPages = {
  List: "/importer"
  // Detail: (shipment_id?: string) => `${ImporterPages.List}/${shipment_id}`,
  // Create: "create",
};
const ImporterNavigation = {
  label: "Importers",
  icon: <Container className="size-5" />,
  pattern: [{ path: "/importer/*" }],
  path: ImporterPages.List
};
const ImporterRoutes: RouterObject = [
  {
    path: ImporterPages.List,
    // element: <ShipmentList />,
    children: [
      { path: "", element: <ImporterList /> }
      // {
      //   path: ImporterPages.Detail(":shipment_id"),
      //   element: <ShipmentDetail />,
      // },
      // {
      //   path: ImporterPages.Create,
      //   element: <CreateShipment />,
      // },
    ]
  }
];

export { ImporterNavigation, ImporterPages, ImporterRoutes };
