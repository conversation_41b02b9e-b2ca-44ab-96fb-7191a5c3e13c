import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import ImporterService from "../services";
import type {
  BusinessNumberValidationParams,
  SaveImporterParams,
  VerifyImporterParams
} from "../Types.importer";

const useVerifyImporter = () =>
  useMutation({
    mutationFn: async (params: VerifyImporterParams) => {
      return await ImporterService.verify(params);
    }
  });

const useDisableImporter = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await ImporterService.disable(params)
  });

const useEnableImporter = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await ImporterService.enable(params)
  });

const useDeleteImporter = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await ImporterService.remove(params)
  });

const useSaveImporter = () =>
  useMutation({
    mutationFn: async (params: SaveImporterParams) => {
      if (params.id) {
        return await ImporterService.edit(params);
      }
      return await ImporterService.create(params);
    }
  });

const useVerifyBusinessNumber = () =>
  useMutation({
    mutationFn: async (params: BusinessNumberValidationParams) => {
      return await ImporterService.verifyBusinessNumber(params);
    },
    retry: false
  });

const useCarmRequest = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => {
      return await ImporterService.carmRequest(params);
    }
  });

const useResendPoa = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => {
      return await ImporterService.resendPoa(params);
    }
  });

export {
  useDisableImporter,
  useEnableImporter,
  useDeleteImporter,
  useSaveImporter,
  useVerifyBusinessNumber,
  useVerifyImporter,
  useCarmRequest,
  useResendPoa
};
