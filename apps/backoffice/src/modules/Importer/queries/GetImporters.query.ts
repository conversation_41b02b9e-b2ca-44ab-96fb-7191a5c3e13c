import { useQuery } from "@tanstack/react-query";
import ImporterService from "../services";
import type { GetImporterList, GetImporterListResponse, Importer } from "../Types.importer";

export const getImportersKey = "getImporters";

export const useGetImporters = <R = GetImporterListResponse>(
  params?: GetImporterList,
  select?: (data: GetImporterListResponse) => R,
  refetchOnMount = false
) =>
  useQuery({
    queryKey: [getImportersKey, params],
    queryFn: () => ImporterService.list(params),
    select,
    refetchOnMount
  });

export const useGetImporterList = (params?: GetImporterList) =>
  useGetImporters(
    params,
    (data: GetImporterListResponse) => {
      const importers = data.importers.map((c: Importer) => ({
        ...c,
        countryName: c.country?.name || "",
        organizationName: c.organization?.name || ""
      }));
      return { ...data, importers };
    },
    true
  );
