import { http } from "@/bootstrap/Http.bootstrap";
import { DetailParams, handleError, Http, stringifyQueryParams } from "utils";
import type {
  BusinessNumberValidationParams,
  BusinessNumberValidationResponse,
  GetImporterList,
  GetImporterListResponse,
  Importer,
  SaveImporterParams,
  VerifyImporterParams
} from "../Types.importer";
import { env } from "@/config/Environment.config";

const IMPORTER_ROUTE = "importers";
/**
 * Get importer list
 *
 * @returns
 */
async function list(params?: GetImporterList): Promise<GetImporterListResponse> {
  try {
    return await http.get(stringifyQueryParams(IMPORTER_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get importer detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<Importer> {
  try {
    return await http.get(`${IMPORTER_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Verify importer
 *
 * @returns
 */
async function verify({ id, ...params }: VerifyImporterParams): Promise<Importer> {
  try {
    return await http.post(`${IMPORTER_ROUTE}/${id}/verify`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create importer
 *
 * @returns
 */
async function create({ organizationId, ...params }: SaveImporterParams): Promise<Importer> {
  try {
    return await http.post(IMPORTER_ROUTE, params, {
      timeout: 120000,
      headers: {
        "Claro-Organization-Id": organizationId?.toString() ?? ""
      }
    });
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit importer
 *
 * @returns
 */
async function edit({ id, organizationId, ...params }: SaveImporterParams): Promise<Importer> {
  try {
    return await http.put(`${IMPORTER_ROUTE}/${id}`, params, {
      headers: {
        "Claro-Organization-Id": organizationId?.toString() ?? ""
      }
    });
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Delete importer
 *
 * @returns
 */
async function remove({ id }: DetailParams): Promise<void> {
  try {
    return await http.delete(`${IMPORTER_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Disable importer
 *
 * @returns
 */
async function disable({ id }: DetailParams): Promise<void> {
  try {
    return await http.post(`${IMPORTER_ROUTE}/${id}/disable`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Enable importer
 *
 * @returns
 */
async function enable({ id }: DetailParams): Promise<void> {
  try {
    return await http.post(`${IMPORTER_ROUTE}/${id}/enable`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Send Carm Request
 *
 * @returns
 */
async function carmRequest({ id }: DetailParams): Promise<Importer> {
  try {
    return await http.post(`${IMPORTER_ROUTE}/${id}/carm-request`, undefined, {
      timeout: 120000
    });
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Resend POA
 *
 * @returns
 */
async function resendPoa({ id }: DetailParams): Promise<Importer> {
  try {
    return await http.post(`${IMPORTER_ROUTE}/${id}/resend-poa`);
  } catch (error) {
    throw await handleError(error);
  }
}
const apify = new Http({
  prefixUrl: env.apifyApiUrl,
  headers: {
    "Content-Type": "application/json"
  },
  timeout: 60000
});
/**
 * Verify business number.
 *
 * @returns
 */
async function verifyBusinessNumber(
  params: BusinessNumberValidationParams
): Promise<BusinessNumberValidationResponse> {
  try {
    return await apify.post(
      `antek~cra-business-number-check/run-sync-get-dataset-items?token=${env.apifyToken}`,
      params
    );
  } catch (error) {
    throw new Error(error as string);
  }
}

export default {
  disable,
  create,
  edit,
  enable,
  get,
  list,
  remove,
  verify,
  verifyBusinessNumber,
  carmRequest,
  resendPoa
};
