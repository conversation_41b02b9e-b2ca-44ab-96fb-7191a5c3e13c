import {
  EditImporterDto,
  GetImportersDto,
  GetImportersResponseDto,
  Importer as Imp,
  VerifyImporterDto
} from "nest-modules";
import { DetailParams, ListParams, PaginationResponse } from "utils";
import { OrganizationType } from "../Organization/Types.organization";

export enum ImporterStatus {
  PENDING_POA = "pending-poa",
  PENDING_VERIFICATION = "pending-verification",
  REJECTED = "rejected",
  ACTIVE = "active",
  DISABLED = "disabled"
}
export enum CarmStatus {
  SENT = "sent",
  PENDING = "pending",
  ACTIVE = "active",
  INVALID = "invalid",
  APPROVED = "Approved"
}
export enum PoaStatus {
  SENT = "sent",
  SIGNED = "signed"
}

export type Importer = Imp;

export type ImporterTable = Importer & {
  countryName?: string;
  organizationName?: string;
};

export type ImporterFormRef = {
  dirty: boolean;
  handleSubmit: () => void;
  resetForm: () => void;
  setFieldError: (field: string, error: string) => void;
};

export type GetImporterList = ListParams<GetImportersDto>;
export type GetImporterListResponse = PaginationResponse & GetImportersResponseDto;

export type SaveImporterParams = EditImporterDto &
  DetailParams & { organizationId?: number; organizationType?: OrganizationType };

export type VerifyImporterParams = VerifyImporterDto & DetailParams;

export type CarmRequestParams = {
  businessNo: string;
};
export type CarmRequestResponse = {
  result: CarmStatus;
  businessNo: string;
};

export type BusinessNumberValidationParams = {
  business_number: string;
  business_name: string;
};
export type BusinessNumberValidationResponse = Array<{
  isValid: boolean;
}>;
