import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { flattenQueryResults, infiniteQueryMapper } from "utils";
import OrganizationService from "../services";
import { GetOrganizationList, GetOrganizationListResponse } from "../Types.organization";

export const getOrganizationsKey = "getOrganizations";

export const useGetOrganizations = <R = GetOrganizationListResponse>(
  params?: GetOrganizationList,
  select?: (data: GetOrganizationListResponse) => R,
  refetchOnMount = false
) =>
  useQuery({
    queryKey: [getOrganizationsKey, params],
    queryFn: () => OrganizationService.list(params),
    select,
    refetchOnMount
  });

export const useGetOrganizationList = (params?: GetOrganizationList) =>
  useGetOrganizations(params, (data: GetOrganizationListResponse) => data, true);

export const useGetInfiniteOrganizations = (params?: GetOrganizationList, refetchOnMount = false) =>
  useInfiniteQuery({
    queryKey: [getOrganizationsKey, params],
    queryFn: ({ pageParam }) => OrganizationService.list({ ...params, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: infiniteQueryMapper,
    refetchOnMount,
    select: (data) => flattenQueryResults("organizations", data.pages)
  });
