import { useFormik } from "formik";
import { Button, Checkbox, Input, Modal, Select } from "ui";
import { saveOrganizationSchema } from "../../Schema.organization";
import { Organization, SaveOrganizationParams } from "../../Types.organization";
import { formikInputOpts } from "utils";
import { CUSTOMS_BROKER, ORGANIZATION_TYPE } from "../../Constant.organization";

type Props = {
  show: boolean;
  info?: Organization;
  isLoading?: boolean;
  onSubmit(params: SaveOrganizationParams): void;
  onClose(): void;
};
function AddOrganizationModal({ show, info, isLoading, onSubmit, onClose }: Props) {
  const formik = useFormik({
    initialValues: {
      id: info?.id,
      name: info?.name || "",
      organizationType: info?.organizationType,
      skipPoaCheck: info?.skipPoaCheck ?? false,
      customsBroker: info?.customsBroker
    },
    validationSchema: saveOrganizationSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit: onSubmit
  });

  return (
    <Modal
      id="organization-modal"
      show={show}
      onClose={() => {
        formik.resetForm();
        onClose();
      }}
      title={`${info ? "Edit" : "Add New"} Organization`}
    >
      <form className="flex flex-col gap-3 px-2">
        <Input label="Name" placeholder="Input name" {...formikInputOpts(formik, "name")} />
        <Select
          label="Organization type"
          options={ORGANIZATION_TYPE()}
          {...formikInputOpts(formik, "organizationType")}
          optional
        />
        <Select
          label="Customs broker"
          options={CUSTOMS_BROKER}
          {...formikInputOpts(formik, "customsBroker")}
          optional
        />
        <Checkbox
          label="Skip POA check"
          name="skipPoaCheck"
          checked={formik.values.skipPoaCheck ?? false}
          onChange={formik.handleChange}
        />

        <Button label="Submit" loading={isLoading} onClick={formik.handleSubmit} className="mt-2" />
      </form>
    </Modal>
  );
}
export default AddOrganizationModal;
