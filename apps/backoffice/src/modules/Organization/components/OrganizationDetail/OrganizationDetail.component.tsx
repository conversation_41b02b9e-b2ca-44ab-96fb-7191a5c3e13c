import { BaseError } from "@/common/Types.common";
import { ImporterPages } from "@/modules/Importer/Routes.importer";
import { AddUserModal } from "@/modules/User/components";
import { useSaveUser } from "@/modules/User/mutations";
import { useGetUserList } from "@/modules/User/queries";
import { userTableSchema } from "@/modules/User/Schema.user";
import { SaveUserParams, User } from "@/modules/User/Types.user";
import { useFormik } from "formik";
import { PlusIcon } from "lucide-react";
import { useCallback, useEffect, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { Button, Card, Checkbox, Icons, Input, Select, Table } from "ui";
import { emptyStringToNull, formikInputOpts } from "utils";
import { ORGANIZATION_TYPE } from "../../Constant.organization";
import { saveOrganizationSchema } from "../../Schema.organization";
import { OrganizationType, type Organization, type SaveOrganizationParams } from "../../Types.organization";

type Props = {
  organization?: Organization;
  isLoading?: boolean;
  onClose(): void;
  onSubmit(params: SaveOrganizationParams): void;
};
function OrganizationDetail({ organization, isLoading, onClose, onSubmit }: Props) {
  const navigate = useNavigate();
  const [modal, toggleModal] = useReducer((r) => !r, false);
  const [user, setUser] = useState<User>();

  const { data, error, isFetching, refetch } = useGetUserList({
    limit: 50,
    organizationId: organization?.id
  });

  useEffect(() => {
    if (error) {
      toast.error((error as unknown as BaseError).message);
    }
  }, [error]);

  const saveUser = useSaveUser();

  const formik = useFormik({
    initialValues: {
      id: organization?.id,
      name: organization?.name || "",
      organizationType: organization?.organizationType,
      skipPoaCheck: organization?.skipPoaCheck ?? false,
      customsBroker: organization?.customsBroker
    },
    validationSchema: saveOrganizationSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit: onSubmit
  });

  const handleSaveUser = useCallback(
    async (params: SaveUserParams) => {
      if (!organization?.id) return;

      try {
        await saveUser.mutateAsync({
          ...emptyStringToNull(params),
          organizationId: organization.id
        });

        const message = `User ${params.name} created successfully`;
        toast.success(message);

        toggleModal();

        setUser(undefined);
        refetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [organization?.id, refetch, saveUser]
  );

  if (!organization) return <></>;

  return (
    <>
      <AddUserModal
        show={modal}
        info={user}
        isLoading={saveUser.isPending}
        organization={organization}
        onClose={() => {
          setUser(undefined);
          toggleModal();
        }}
        onSubmit={handleSaveUser}
      />

      <Card containerStyle="p-4">
        <header className="flex mb-2">
          <h5>Edit Organization</h5>
          <button
            type="button"
            className="flex justify-center items-center ml-auto text-sm font-semibold rounded-full text-neutral-800 hover:bg-neutral-100 dark:text-white dark:hover:bg-neutral-700 dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-neutral-600"
            onClick={onClose}
          >
            <span className="sr-only">Close sidebar</span>
            <Icons.Close className="size-4" />
          </button>
        </header>

        <div className="flex gap-3">
          <Input label="Name" placeholder="Input name" {...formikInputOpts(formik, "name")} />
          <Select
            label="Organization type"
            options={ORGANIZATION_TYPE(organization?.organizationType)}
            {...formikInputOpts(formik, "organizationType")}
            optional
            disabled={organization?.organizationType === OrganizationType.BACKOFFICE}
          />
          <Checkbox
            label="Skip POA check"
            name="skipPoaCheck"
            containerStyle="mt-6 flex-1"
            checked={formik.values.skipPoaCheck ?? false}
            onChange={formik.handleChange}
          />
          <Button
            label="Update"
            kind="update"
            className="self-center"
            onClick={formik.handleSubmit}
            loading={isLoading}
          />
          <Button
            label="Create Importer"
            kind="outline"
            className="ml-auto self-center"
            onClick={() => {
              navigate(ImporterPages.List, { state: { organization } });
            }}
            disabled={isLoading}
          />
        </div>
      </Card>

      <Card containerStyle="p-4">
        <div className="flex items-center mb-4">
          <h5>Users</h5>
          <Button
            label="Add User"
            kind="outline"
            icon={<PlusIcon size={16} />}
            className="ml-auto py-1 px-2"
            disabled={isLoading}
            loading={saveUser.isPending}
            onClick={toggleModal}
          />
        </div>
        <Table
          data={data?.users}
          isLoading={isFetching}
          schema={userTableSchema}
          onClick={(_data: User) => {
            setUser(_data);
            toggleModal();
          }}
        />
      </Card>
    </>
  );
}
export default OrganizationDetail;
