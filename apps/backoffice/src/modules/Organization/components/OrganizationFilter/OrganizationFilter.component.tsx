import { BOOLEAN_OPTIONS, ORDER_BY } from "@/common/Constant.common";
import { useDebounce } from "@uidotdev/usehooks";
import { useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";
import { Card, Input, Select } from "ui";
import { OrderBy, SortOrder } from "utils";
import { CUSTOMS_BROKER, ORGANIZATION_SORT_BY, ORGANIZATION_TYPE } from "../../Constant.organization";
import { organizationTableSchema } from "../../Schema.organization";
import { GetOrganizationList, OrganizationCustomsBroker, OrganizationType } from "../../Types.organization";

type Props = {
  isOpen: boolean;
  filterValues(values?: GetOrganizationList): void;
};
const OrganizationFilter = ({ isOpen, filterValues }: Props) => {
  const [values, setValues] = useState<GetOrganizationList>();

  const [_name, setName] = useState<string>();
  const name = useDebounce(_name, 500);

  useEffect(() => {
    setValues((prev) => ({
      ...prev,
      name: name ?? undefined
    }));
  }, [name]);

  useEffect(() => {
    filterValues(values);
  }, [filterValues, values]);

  useEffect(() => () => setValues(undefined), []);

  return (
    <div
      className={twMerge(
        "overflow-hidden transition-max-height duration-300 ease-in-out",
        isOpen ? "max-h-96" : "max-h-0"
      )}
    >
      {isOpen && (
        <Card containerStyle="flex flex-wrap items-center gap-x-3 gap-y-2 px-6 py-4 border">
          <div className="flex gap-3">
            <Input label="Name" placeholder="Search" onTextChange={setName} kind="search" />
            <Select
              label="Type"
              onSelected={(organizationType: OrganizationType) =>
                setValues((prev) => ({ ...prev, organizationType }))
              }
              options={ORGANIZATION_TYPE(OrganizationType.BACKOFFICE)}
              optional
            />
            <Select
              label="Customs Broker"
              onSelected={(customsBroker: OrganizationCustomsBroker) =>
                setValues((prev) => ({ ...prev, customsBroker }))
              }
              options={CUSTOMS_BROKER}
              optional
            />
            <Select
              label="Skip POA Check"
              onSelected={(skipPoaCheck: boolean) => setValues((prev) => ({ ...prev, skipPoaCheck }))}
              options={BOOLEAN_OPTIONS}
              optional
            />
          </div>

          <div className="flex gap-3">
            <Select
              label="Sort By"
              onSelected={(sortBy: keyof typeof organizationTableSchema) =>
                setValues((prev) => ({ ...prev, sortBy }) as GetOrganizationList)
              }
              options={ORGANIZATION_SORT_BY}
              defaultValue={"createDate"}
              optional
            />
            <Select
              label="Sort Direction"
              onSelected={(sortOrder: OrderBy) =>
                setValues((prev) => ({ ...prev, sortOrder }) as GetOrganizationList)
              }
              options={ORDER_BY}
              defaultValue={SortOrder.DESC}
              optional
            />
          </div>
        </Card>
      )}
    </div>
  );
};
export default OrganizationFilter;
