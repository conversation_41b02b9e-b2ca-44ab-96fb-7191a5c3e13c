import { auditTableSchema, TableSchema } from "utils";
import { boolean, mixed, number, object, ObjectSchema, string } from "yup";
import {
  Organization,
  OrganizationCustomsBroker,
  OrganizationType,
  SaveOrganizationParams
} from "./Types.organization";

export const organizationTableSchema: TableSchema<Organization> = {
  name: {
    header: "Name",
    style: "w-fit font-medium",
    visible: true
  },
  organizationType: {
    header: "Type",
    style: "uppercase",
    visible: true,
    disableSort: true
  },
  customsBroker: {
    header: "Customs broker",
    style: "uppercase",
    visible: true
  },
  skipPoaCheck: {
    header: "Skip POA",
    renderer: "boolean",
    visible: true,
    disableSort: true
  },
  ...auditTableSchema
};

export const saveOrganizationSchema: ObjectSchema<SaveOrganizationParams> = object({
  id: number().optional(),
  name: string().required("Name is required"),
  organizationType: mixed<OrganizationType>().required("Organization type is required"),
  skipPoaCheck: boolean().optional(),
  customsBroker: mixed<OrganizationCustomsBroker>().required("Customs broker is required")
});
