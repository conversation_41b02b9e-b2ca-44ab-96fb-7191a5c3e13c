import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { BaseError } from "@/common/Types.common";
import { useCallback, useEffect, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { twMerge } from "tailwind-merge";
import { <PERSON><PERSON>, Header, Pagination, Table } from "ui";
import { AddOrganizationModal, OrganizationDetail, OrganizationFilter } from "../../components";
import { useSaveOrganization } from "../../mutations";
import { useGetOrganizationList } from "../../queries";
import { organizationTableSchema } from "../../Schema.organization";
import { GetOrganizationList, Organization, SaveOrganizationParams } from "../../Types.organization";
import { OrganizationColumn } from "nest-modules";
import { SortOrder } from "utils";

function OrganizationList() {
  const [modal, toggleModal] = useReducer((r) => !r, false);
  const [organization, setOrganization] = useState<Organization>();
  const [page, setPage] = useState(1);
  const [filter, toggleFilter] = useReducer((r) => !r, false);
  const [filters, setFilters] = useState<GetOrganizationList>();

  const saveOrganization = useSaveOrganization();

  const { data, error, isFetching, refetch } = useGetOrganizationList({
    page,
    limit: DEFAULT_LIMIT,
    sortBy: "createDate" as OrganizationColumn,
    sortOrder: SortOrder.DESC,
    ...filters
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const handleSaveOrganization = useCallback(
    async (params: SaveOrganizationParams) => {
      try {
        await saveOrganization.mutateAsync(params);

        const message = `Organization ${params.name} ${params.id ? "updated" : "created"} successfully`;
        toast.success(message);

        if (modal) toggleModal();

        setOrganization(undefined);
        refetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [refetch, saveOrganization, modal]
  );

  const handleFilter = useCallback((values: GetOrganizationList) => {
    setFilters(values);
    setPage(1);
  }, []);

  return (
    <div className="flex flex-col">
      <AddOrganizationModal
        show={modal}
        info={organization}
        isLoading={saveOrganization.isPending}
        onClose={() => {
          setOrganization(undefined);
          toggleModal();
        }}
        onSubmit={handleSaveOrganization}
      />

      <Header title="Organization List">
        <Button label="Filters" kind="filter" onClick={toggleFilter} />
        <Button
          label="New Organization"
          kind="create"
          className="ml-auto"
          onClick={toggleModal}
          disabled={saveOrganization.isPending}
        />
      </Header>

      <section>
        <OrganizationFilter filterValues={handleFilter} isOpen={filter} />
      </section>

      <main className="flex items-stretch gap-3">
        <section className={twMerge("transition-all duration-150", organization ? " basis-2/5" : "flex-1")}>
          <Table
            data={data?.organizations}
            isLoading={isFetching}
            schema={organizationTableSchema}
            getRowStyle={(d) => (d.id === organization?.id ? "bg-neutral-100" : "")}
            onClick={setOrganization}
            footer={
              <Pagination
                currentPage={page}
                total={data?.total}
                limit={DEFAULT_LIMIT}
                onPageChange={setPage}
              />
            }
            total={data?.total}
          />
        </section>

        <section
          className={twMerge(
            "w-0 transition-all duration-150 flex flex-col gap-2 mt-2",
            organization ? "flex-1" : "w-0 hidden"
          )}
        >
          <OrganizationDetail
            organization={organization}
            isLoading={saveOrganization.isPending}
            onClose={() => setOrganization(undefined)}
            onSubmit={handleSaveOrganization}
          />
        </section>
      </main>
    </div>
  );
}
export default OrganizationList;
