import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import OrganizationService from "../services";
import { SaveOrganizationParams } from "../Types.organization";

const useSaveOrganization = () =>
  useMutation({
    mutationFn: async (params: SaveOrganizationParams) => {
      if (params.id) {
        return await OrganizationService.edit(params);
      }
      return await OrganizationService.create(params);
    }
  });

const useDeleteOrganization = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await OrganizationService.remove(params)
  });

export { useDeleteOrganization, useSaveOrganization };
