import { RouterObject } from "@/common/Types.common";
import { Building2 } from "lucide-react";
import { OrganizationList } from "./views";

const OrganizationPages = {
  List: "/organization"
  // Detail: (shipment_id?: string) => `${DocumentPages.List}/${shipment_id}`,
  // Create: "create",
};
const OrganizationNavigation = {
  label: "Organizations",
  icon: <Building2 className="size-5" />,
  pattern: [{ path: `${OrganizationPages.List}/*` }],
  path: OrganizationPages.List
};
const OrganizationRoutes: RouterObject = [
  {
    path: OrganizationPages.List,
    element: <OrganizationList />,
    children: [
      // { path: "", element: <DocumentTypeList /> },
      // {
      //   path: DocumentPages.Detail(":shipment_id"),
      //   element: <ShipmentDetail />,
      // },
      // {
      //   path: DocumentPages.Create,
      //   element: <CreateShipment />,
      // },
    ]
  }
];

export { OrganizationNavigation, OrganizationPages, OrganizationRoutes };
