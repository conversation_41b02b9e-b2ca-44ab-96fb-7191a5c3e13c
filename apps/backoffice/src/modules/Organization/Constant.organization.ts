import { enumToSelectOptions, schemaToSelectOptions } from "utils";
import { OrganizationCustomsBroker, OrganizationType } from "./Types.organization";
import { organizationTableSchema } from "./Schema.organization";

export const ORGANIZATION_TYPE = (organizationType?: OrganizationType) =>
  organizationType !== OrganizationType.BACKOFFICE
    ? enumToSelectOptions(OrganizationType).filter((o) => o.value !== OrganizationType.BACKOFFICE)
    : enumToSelectOptions(OrganizationType);

export const CUSTOMS_BROKER = enumToSelectOptions(OrganizationCustomsBroker);

export const ORGANIZATION_SORT_BY = schemaToSelectOptions(organizationTableSchema);
