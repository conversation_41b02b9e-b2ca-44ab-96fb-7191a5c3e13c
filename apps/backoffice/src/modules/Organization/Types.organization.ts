import type {
  EditOrganizationDto,
  GetOrganizationsDto,
  GetOrganizationsResponseDto,
  Organization as Org
} from "nest-modules";
import { DetailParams, ListParams, PaginationResponse } from "utils";

export enum OrganizationType {
  BACKOFFICE = "Backoffice",
  TRADING = "Trading",
  AGENT = "Agent",
  DEMO = "Demo"
}
export enum OrganizationCustomsBroker {
  USAV = "usav",
  CLARO = "claro"
}

export type Organization = Org;

export type GetOrganizationList = ListParams<GetOrganizationsDto>;
export type GetOrganizationListResponse = PaginationResponse & GetOrganizationsResponseDto;

export type SaveOrganizationParams = EditOrganizationDto & DetailParams;
