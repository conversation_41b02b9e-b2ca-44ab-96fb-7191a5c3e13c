import { BaseError } from "@/common/Types.common";
import { observer } from "mobx-react-lite";
import { useCallback } from "react";
import toast from "react-hot-toast";
import { <PERSON><PERSON>, Header } from "ui";
import { useAuthorizeDocusign } from "../../mutations";

const Authorize = observer(() => {
  const { mutateAsync: authorizeDocusign, isPending } = useAuthorizeDocusign();

  const handleAuthorizeDocusign = useCallback(async () => {
    try {
      const auth = await authorizeDocusign();

      toast.success("Docusign auth");
      window.open(auth?.authorizationUri, "_blank");
    } catch (error) {
      toast.error((error as BaseError).message);
    }
  }, [authorizeDocusign]);

  return (
    <div>
      <Header title="Docusign">
        <Button label="Authorize" onClick={handleAuthorizeDocusign} loading={isPending} />
      </Header>
    </div>
  );
});
export default Authorize;
