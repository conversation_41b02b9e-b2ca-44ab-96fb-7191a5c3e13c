import { RouterObject } from "@/common/Types.common";
import { FileText } from "lucide-react";
import { Authorize } from "./views";

const DocusignPages = {
  Authorize: "/docusign"
};
const DocusignNavigation = {
  label: "Docusign",
  icon: <FileText className="size-5" />,
  pattern: [{ path: "/docusign/*" }],
  path: DocusignPages.Authorize
};
const DocusignRoutes: RouterObject = [
  {
    path: DocusignPages.Authorize,
    element: <Authorize />
  }
];

export { DocusignNavigation, DocusignPages, DocusignRoutes };
