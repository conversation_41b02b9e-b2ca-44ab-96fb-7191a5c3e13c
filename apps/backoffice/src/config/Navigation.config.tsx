import { NavObject } from "@/common/Types.common";
import { ComplianceNavigation } from "@/modules/Compliance/Routes.compliance";
import { DocusignNavigation } from "@/modules/Docusign/Routes.docusign";
import { ImporterNavigation } from "@/modules/Importer/Routes.importer";
import { OrganizationNavigation } from "@/modules/Organization/Routes.organization";
import { DocumentNavigation } from "../modules/Document/Routes.document";
import { OnboardingNavigation } from "@/modules/Onboarding/Routes.onboarding";

const NavigationItems: NavObject[] = [
  ImporterNavigation,
  OnboardingNavigation,
  DocumentNavigation,
  ComplianceNavigation,
  OrganizationNavigation,
  DocusignNavigation
];
export default NavigationItems;
