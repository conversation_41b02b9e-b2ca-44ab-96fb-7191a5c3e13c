import Wrapper from "@/bootstrap/Wrapper.bootstrap";
import { RouterObject } from "@/common/Types.common";
import { AuthPages, AuthRoutes } from "@/modules/Auth/Routes.auth";
import { ComplianceRoutes } from "@/modules/Compliance/Routes.compliance";
import { DocusignRoutes } from "@/modules/Docusign/Routes.docusign";
import { ImporterPages, ImporterRoutes } from "@/modules/Importer/Routes.importer";
import { OrganizationRoutes } from "@/modules/Organization/Routes.organization";
import { Navigate } from "react-router-dom";
import { DocumentRoutes } from "../modules/Document/Routes.document";
import { OnboardingRoutes } from "@/modules/Onboarding/Routes.onboarding";

const RoutesRoot: RouterObject = [
  // Fallback
  { path: "*", element: <Navigate to={AuthPages.Login} replace /> },

  // Private routes
  {
    path: "/",
    element: <Wrapper />,
    children: [
      { path: "", element: <Navigate to={ImporterPages.List} replace /> },
      ...ImporterRoutes,
      ...OnboardingRoutes,
      ...DocumentRoutes,
      ...ComplianceRoutes,
      ...OrganizationRoutes,
      ...DocusignRoutes
    ]
  },

  // Public routes
  ...AuthRoutes
];

export default RoutesRoot;
