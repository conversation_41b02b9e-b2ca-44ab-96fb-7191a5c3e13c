import react from "@vitejs/plugin-react-swc";
import path from "path";
import { defineConfig } from "vite";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src")
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // React core libraries
          "react-core": ["react", "react-dom", "react-router-dom"],

          // State management
          state: ["mobx", "mobx-persist-store", "mobx-react-lite", "@tanstack/react-query"],

          // Form handling and validation
          forms: ["formik", "yup", "@uidotdev/usehooks"],

          // Core UI utilities
          ui: ["ui"],
          "ui-libs": ["lucide-react", "react-hot-toast", "tailwind-merge"],

          // Data handling and business logic
          "data-utils": ["utils", "nest-modules"]
        }
      }
    },
    sourcemap: process.env.NODE_ENV === "development" ? "inline" : false,
    emptyOutDir: true,
    chunkSizeWarningLimit: 600
  },
  server: {
    port: 5174
  }
});
