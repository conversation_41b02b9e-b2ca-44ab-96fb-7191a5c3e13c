{"name": "backoffice", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "tsc -b && npm run lint && vite build", "lint": "eslint .", "preview": "vite preview", "start": "vite"}, "dependencies": {"@react-oauth/google": "~0.12.1", "@tanstack/react-query": "~5.59.0", "@uidotdev/usehooks": "~2.4.1", "firebase": "~11.7.1", "formik": "~2.4.6", "lucide-react": "~0.436.0", "mobx-persist-store": "~1.1.5", "mobx-react-lite": "~4.0.7", "mobx": "~6.13.1", "nest-modules": "workspace:^", "react-dom": "^18.3.1", "react-hot-toast": "~2.4.1", "react-phone-number-input": "~3.4.12", "react-router-dom": "~6.26.1", "react": "^18.3.1", "tailwind-merge": "~2.5.2", "ui": "workspace:^", "utils": "workspace:^", "yup": "~1.4.0"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/forms": "~0.5.8", "@types/google.maps": "~3.58.1", "@types/react-dom": "^18.3.0", "@types/react": "^18.3.3", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "~10.4.20", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "eslint": "^9.9.0", "globals": "^15.9.0", "ky": "~1.7.2", "postcss": "~8.4.41", "tailwind": "workspace:^", "tailwindcss": "~3.4.10", "typescript-eslint": "^8.0.1", "typescript": "~5.5.3", "vite": "^5.4.1"}}