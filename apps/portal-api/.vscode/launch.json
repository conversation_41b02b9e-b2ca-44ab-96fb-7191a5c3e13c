{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "name": "portal-api:start",
      "request": "launch",
      "skipFiles": ["<node_internals>/**"],
      "cwd": "${workspaceFolder}",
      "runtimeExecutable": "npm",
      "args": ["run", "start"],
      "console": "internalConsole",
      "outputCapture": "std",
      "stopOnEntry": false
    },
    {
      "type": "node",
      "name": "portal-api:start:repl",
      "request": "launch",
      "skipFiles": ["<node_internals>/**"],
      "cwd": "${workspaceFolder}",
      "runtimeExecutable": "npm",
      "args": ["run", "start:repl"],
      "console": "integratedTerminal",
      "outputCapture": "std",
      "stopOnEntry": false
    }
  ]
}
