import ngram from "wink-nlp-utils/src/string-ngram";

export function trigramSimilarity(lhs: string, rhs: string) {
  if (!lhs || !rhs) {
    return 0;
  }

  const trgmLhs = ngram(lhs.toLowerCase().replace(/[^a-z0-9]/g, ""), 3);
  const trgmRhs = ngram(rhs.toLowerCase().replace(/[^a-z0-9]/g, ""), 3);

  const total = [...new Set([...trgmLhs, ...trgmRhs])];
  const common = [];
  trgmLhs.forEach((trigramItem) => {
    if (trgmRhs.includes(trigramItem)) common.push(trigramItem);
  });

  return total.length === 0 ? 0 : common.length / total.length;
}
