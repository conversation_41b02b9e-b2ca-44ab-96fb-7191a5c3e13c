import { Inject, Injectable, Scope } from "@nestjs/common";
import { ModuleRef, REQUEST } from "@nestjs/core";
import { Test } from "@nestjs/testing";
import { AuthenticatedRequest, UserPermission } from "nest-modules";
import { ClsModule } from "nestjs-cls";
import { createRequestScopedFactory, RequestScopedServiceFactory } from "./request-scoped-service.util";

async function initTestApp(...providers: any[]) {
  const module = await Test.createTestingModule({
    imports: [
      ClsModule.forRoot({
        global: true
      })
    ],
    providers: [...providers]
  }).compile();

  const app = module.createNestApplication();
  await app.init();

  return app;
}

@Injectable({ scope: Scope.REQUEST })
class RequestScopedService {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(@Inject(REQUEST) public request: AuthenticatedRequest) {}
}

@Injectable({ scope: Scope.REQUEST })
class RequestScopedService2 {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  constructor(@Inject(REQUEST) public request: AuthenticatedRequest) {}
}

@Injectable()
class TestService<T> {
  public factory: RequestScopedServiceFactory<T>;

  constructor(private readonly moduleRef: ModuleRef) {}

  public initFactory(service: any) {
    this.factory = createRequestScopedFactory(this.moduleRef, service);
  }
}

describe("RequestScopedServiceUtil", () => {
  it("should create a request scoped service and pass the request to the service", async () => {
    const app = await initTestApp(TestService, RequestScopedService);

    const testService = app.get(TestService<RequestScopedService>);
    testService.initFactory(RequestScopedService);

    const scopedService = await testService.factory();

    expect(scopedService.request.user).toEqual({
      permission: UserPermission.BACKOFFICE_ADMIN
    });
  });

  it("should create a request scoped service and pass custom request to the service", async () => {
    const app = await initTestApp(TestService, RequestScopedService);

    const testService = app.get(TestService<RequestScopedService>);
    testService.initFactory(RequestScopedService);

    const scopedService: RequestScopedService = await testService.factory({
      user: {
        permission: UserPermission.BASIC,
        organization: {
          id: 123
        }
      }
    });

    expect(scopedService.request.user).toEqual({
      permission: UserPermission.BASIC,
      organization: {
        id: 123
      }
    });
  });

  it("should creare multiple request scoped services", async () => {
    const app = await initTestApp(TestService, RequestScopedService, RequestScopedService2);

    const testService = app.get(TestService<[RequestScopedService, RequestScopedService2]>);
    testService.initFactory([RequestScopedService, RequestScopedService2]);

    const [requestScopedService, requestScopedService2] = await testService.factory();

    expect(requestScopedService.request.user).toEqual({
      permission: UserPermission.BACKOFFICE_ADMIN
    });

    expect(requestScopedService2.request.user).toEqual({
      permission: UserPermission.BACKOFFICE_ADMIN
    });
  });
});
