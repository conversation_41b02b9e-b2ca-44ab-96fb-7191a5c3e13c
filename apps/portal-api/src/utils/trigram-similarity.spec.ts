import { trigramSimilarity } from "./trigram-similarity";

describe("trigramSimilarity", () => {
  it("should return 1 when both strings are identical", () => {
    const str = "test string";
    expect(trigramSimilarity(str, str)).toBe(1);
  });

  it("should return 0 when there are no common trigrams", () => {
    const str1 = "abc";
    const str2 = "xyz";
    expect(trigramSimilarity(str1, str2)).toBe(0);
  });

  it("should handle empty strings correctly", () => {
    expect(trigramSimilarity("", "")).toBe(0);
    expect(trigramSimilarity("abc", "")).toBe(0);
    expect(trigramSimilarity("", "xyz")).toBe(0);
  });

  it("should calculate similarity correctly for partial overlaps", () => {
    const str1 = "abcdef";
    const str2 = "abcfgh";
    // trigrams of str1: 'abc', 'bcd', 'cde', 'def'
    // trigrams of str2: 'abc', 'bcf', 'cfg', 'fgh'
    // common: 'abc'
    // total unique trigrams: 'abc', 'bcd', 'cde', 'def', 'bcf', 'cfg', 'fgh' => 7
    // similarity = 1 / 7 ≈ 0.142857
    expect(trigramSimilarity(str1, str2)).toBeCloseTo(1 / 7);
  });

  it("should be case-sensitive", () => {
    const str1 = "ABC";
    const str2 = "abc";
    expect(trigramSimilarity(str1, str2)).toBe(0);
  });

  it("should handle strings shorter than trigram length", () => {
    expect(trigramSimilarity("ab", "abc")).toBe(0);
    expect(trigramSimilarity("a", "b")).toBe(0);
  });
});
