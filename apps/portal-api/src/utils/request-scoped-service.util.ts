import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import { AuthenticatedRequest, Organization, User, UserPermission } from "nest-modules";

type PartialAuthenticatedRequest = Omit<Partial<AuthenticatedRequest>, "user"> & {
  user?: Omit<Partial<User>, "organization"> & {
    permission: UserPermission;
    organization?: Partial<Organization>;
  };
};

export type RequestScopedServiceFactory<T> = (
  customRequest?: PartialAuthenticatedRequest,
  strict?: boolean
) => Promise<T>;

/**
 * A utility to create a factory function for resolving request-scoped services
 * from singleton-scoped providers.
 *
 * By default, we will pass the user with permission BACKOFFICE_ADMIN.
 *
 * @param moduleRef The ModuleRef instance to resolve services
 * @param Service The service class to resolve
 * @param request Optional request object containing context data. If not provided, it must be passed when invoking the returned factory
 * @returns A function that can be called to get the request-scoped service instance
 */
export function createRequestScopedFactory(
  moduleRef: ModuleRef,
  serviceOrServices: any | any[]
): RequestScopedServiceFactory<any> {
  return async (
    customRequest: PartialAuthenticatedRequest = {
      user: {
        permission: UserPermission.BACKOFFICE_ADMIN
      }
    },
    strict: boolean = true
  ): Promise<any> => {
    const request = customRequest;

    // Get or create contextId from the request
    const contextId = ContextIdFactory.create();

    moduleRef.registerRequestByContextId(request, contextId);

    if (Array.isArray(serviceOrServices)) {
      return Promise.all(
        serviceOrServices.map(async (service) => {
          const resolvedService = await moduleRef.resolve(service, contextId, { strict });
          return resolvedService;
        })
      );
    }

    const resolvedService = await moduleRef.resolve(serviceOrServices, contextId, { strict });

    await new Promise((resolve) => process.nextTick(resolve));

    return resolvedService;
  };
}
