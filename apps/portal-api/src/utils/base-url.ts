import { NODE_ENV } from "@/app.module";
import { NotImplementedException } from "@nestjs/common";
import { NodeEnv } from "nest-modules";

export function getBaseUrl() {
  switch (NODE_ENV) {
    case NodeEnv.LOCAL:
      return "http://localhost:5001";
    case NodeEnv.DEVELOPMENT:
      return "https://api-dev.clarocustoms.com";
    case NodeEnv.PRODUCTION:
      // TODO: Set the production base URL
      return "https://api-dev.clarocustoms.com";
    default:
      throw new NotImplementedException("Invalid Node Environment");
  }
}
