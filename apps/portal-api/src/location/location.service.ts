import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import {
  AuthenticatedRequest,
  CountryService,
  CreateLocationDto,
  EditLocationDto,
  FIND_LOCATION_RELATIONS,
  getFindOptions,
  GetLocationsDto,
  GetLocationsResponseDto,
  Location,
  LOCATION_ENUM_KEYS,
  LOCATION_REQUIRED_KEYS,
  LocationColumn
} from "nest-modules";
import { DataSource, Not, QueryRunner, Repository } from "typeorm";

@Injectable({ scope: Scope.REQUEST })
export class LocationService {
  constructor(
    @InjectRepository(Location)
    private readonly locationRepository: Repository<Location>,
    @Inject(CountryService)
    private readonly countryService: CountryService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}

  async getLocations(getLocationsDto: GetLocationsDto): Promise<GetLocationsResponseDto> {
    const { where, order, skip, take } = getFindOptions(
      getLocationsDto,
      LOCATION_ENUM_KEYS,
      [],
      LocationColumn.id
    );
    const [locations, total] = await this.locationRepository.findAndCount({
      where,
      relations: FIND_LOCATION_RELATIONS,
      order,
      skip,
      take
    });
    return {
      locations,
      total,
      skip,
      limit: take
    };
  }

  async getLocationById(locationId: number, queryRunner?: QueryRunner) {
    const locationRepository = queryRunner
      ? queryRunner.manager.getRepository(Location)
      : this.locationRepository;

    return await locationRepository.findOne({
      where: {
        id: locationId
      },
      relations: FIND_LOCATION_RELATIONS
    });
  }

  async createLocation(createLocationDto: CreateLocationDto, queryRunner?: QueryRunner) {
    const { name, countryId, ...createLocationProps } = createLocationDto;

    const locationRepository = queryRunner
      ? queryRunner.manager.getRepository(Location)
      : this.locationRepository;
    if (
      await locationRepository.existsBy({
        name
      })
    )
      throw new BadRequestException("Location with the same name already exists");

    let newLocation = new Location();
    newLocation.createdBy = this.request?.user || null;
    newLocation.lastEditedBy = this.request?.user || null;
    newLocation.name = name;
    newLocation.country = await this.countryService.getCountryById(countryId);
    if (!newLocation.country) throw new NotFoundException("Country not found");
    for (const [key, value] of Object.entries(createLocationProps)) {
      if (!(LOCATION_REQUIRED_KEYS.includes(key) && [null, undefined].includes(value)))
        newLocation[key] = value;
    }
    newLocation = await locationRepository.save(newLocation);

    return await this.getLocationById(newLocation.id, queryRunner);
  }

  async editLocation(locationId: number, editLocationDto: EditLocationDto) {
    const location = await this.getLocationById(locationId);
    if (!location) throw new NotFoundException("Location not found");
    const { name, countryId, ...editLocationProps } = editLocationDto;

    location.lastEditedBy = this.request?.user || null;
    if (typeof countryId === "number") {
      location.country = await this.countryService.getCountryById(countryId);
      if (!location.country) throw new NotFoundException("Country not found");
    }
    if (typeof name === "string") {
      if (
        await this.locationRepository.existsBy({
          id: Not(locationId),
          name
        })
      )
        throw new BadRequestException("Location with the same name already exists");
      location.name = name;
    }
    for (const [key, value] of Object.entries(editLocationProps)) {
      if (!(LOCATION_REQUIRED_KEYS.includes(key) && [null, undefined].includes(value))) location[key] = value;
    }
    await this.locationRepository.save(location);

    return await this.getLocationById(location.id);
  }

  async deleteLocation(locationId: number) {
    const location = await this.getLocationById(locationId);
    if (!location) throw new NotFoundException("Location not found");
    await this.locationRepository.delete({ id: locationId });
    return;
  }
}
