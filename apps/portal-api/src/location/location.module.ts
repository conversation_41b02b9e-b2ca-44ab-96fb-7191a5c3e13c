import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CountryModule, Location } from "nest-modules";
import { LocationController } from "./location.controller";
import { LocationService } from "./location.service";

@Module({
  imports: [TypeOrmModule.forFeature([Location]), CountryModule],
  providers: [LocationService],
  controllers: [LocationController],
  exports: [LocationService]
})
export class LocationModule {}
