import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards
} from "@nestjs/common";
import { ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses,
  CreateLocationDto,
  EditLocationDto,
  GetLocationsDto,
  GetLocationsResponseDto,
  Location
} from "nest-modules";
import { LocationService } from "./location.service";

@ApiTags("Location API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("locations")
export class LocationController {
  constructor(private readonly locationService: LocationService) {}

  @ApiOperation({ summary: "Get Locations" })
  @ApiGetManyResponses({ type: GetLocationsResponseDto })
  @Get()
  async getLocations(@Query() getLocationDto: GetLocationsDto) {
    return await this.locationService.getLocations(getLocationDto);
  }

  @ApiOperation({ summary: "Get Location" })
  @ApiParam({ name: "id", type: "integer", description: "Location ID" })
  @ApiGetByIdResponses({ type: Location })
  @Get(":id")
  async getLocationById(@Param("id", ParseIntPipe) id: number) {
    const location = await this.locationService.getLocationById(id);
    if (!location) throw new NotFoundException("Location not found");
    return location;
  }

  @ApiOperation({ summary: "Create Location" })
  @ApiCreateResponses({ type: Location })
  @Post()
  async createLocation(@Body() createLocationDto: CreateLocationDto) {
    return await this.locationService.createLocation(createLocationDto);
  }

  @ApiOperation({ summary: "Edit Location" })
  @ApiParam({ name: "id", type: "integer", description: "Location ID" })
  @ApiEditResponses({ type: Location })
  @Put(":id")
  async editLocation(@Param("id", ParseIntPipe) id: number, @Body() editLocationDto: EditLocationDto) {
    return await this.locationService.editLocation(id, editLocationDto);
  }

  @ApiOperation({ summary: "Delete Location" })
  @ApiParam({ name: "id", type: "integer", description: "Location ID" })
  @ApiDeleteResponses()
  @HttpCode(204)
  @Delete(":id")
  async deleteLocation(@Param("id", ParseIntPipe) id: number) {
    await this.locationService.deleteLocation(id);
    return;
  }
}
