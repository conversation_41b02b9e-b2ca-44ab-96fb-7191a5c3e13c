import { ConfigModule } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";
import { ENTITY_LIST } from "nest-modules";
import { testTransaction } from "pg-transactional-tests";

export const useRefreshDatabase = () => {
  beforeAll(testTransaction.start);
  beforeEach(testTransaction.start);
  afterEach(testTransaction.rollback);
  afterAll(testTransaction.close);
};

export const getTypeOrmModule = () => {
  return [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: `.env.${process.env.NODE_ENV}`
    }),
    TypeOrmModule.forRoot({
      type: "postgres",
      applicationName: "portal-api",
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT),
      username: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      synchronize: false,
      // enable verbose loggin when VERBOSE is set, otherwise log only error and warn
      // track and log queries that is taking more than 1 second
      // @see: https://orkhan.gitbook.io/typeorm/docs/logging#log-long-running-queries
      maxQueryExecutionTime: 1000,
      ssl: process.env.DB_SSL_ENABLED === "true",
      extra: {
        // connectionTimeoutMillis will be set by TypeORM from connectTimeoutMS,
        idleTimeoutMillis: 30000,
        // TODO: use rds ca certificate when deployed to aws
        // ssl: { rejectUnauthorized: false }
        ...(process.env.DB_SSL_ENABLED === "true" ? { ssl: { rejectUnauthorized: false } } : {})
      },
      entities: ENTITY_LIST,
      connectTimeoutMS: 5000,
      poolSize: 20
    })
  ];
};
