import { Body, Controller, HttpCode, MessageEvent, Post, Sse } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { ApiBody, ApiOperation, ApiTags } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsNotEmpty, IsOptional, IsString, ValidateNested } from "class-validator";
import { map, Observable } from "rxjs";

class DebugEventDto {
  @IsString()
  @IsNotEmpty()
  event: string;

  @IsOptional()
  payload: any;
}

class MultiDebugEventDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DebugEventDto)
  events: DebugEventDto[];
}

@ApiTags("Debug Event")
@Controller("_debug/event")
export class EventController {
  constructor(private readonly eventEmitter: EventEmitter2) {}

  @ApiOperation({
    summary: "Fire an event"
  })
  @ApiBody({ type: DebugEventDto })
  @HttpCode(201)
  @Post("emit")
  async emitEvent(@Body() body: DebugEventDto) {
    this.eventEmitter.emit(body.event, body.payload);
  }

  @ApiOperation({
    summary: "Fire multiple events"
  })
  @ApiBody({ type: MultiDebugEventDto })
  @HttpCode(201)
  @Post("emit-multiple")
  async emitMultipleEvents(@Body() body: MultiDebugEventDto) {
    body.events.forEach((event) => {
      this.eventEmitter.emit(event.event, event.payload);
    });
  }

  @Sse("listen")
  listen(): Observable<MessageEvent> {
    const observable: Observable<{ event: string; payload: any }> = new Observable((subscriber) => {
      const handler = (event: string, payload: any) => {
        subscriber.next({ event, payload });
      };

      this.eventEmitter.onAny(handler);

      return () => {
        this.eventEmitter.offAny(handler);
      };
    });

    return observable.pipe(
      map(({ event, payload }) => ({
        type: event,
        data: JSON.stringify(payload, null, 2)
      }))
    );
  }
}
