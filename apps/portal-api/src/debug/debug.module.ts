import { Modu<PERSON> } from "@nestjs/common";
import { NodeEnv } from "nest-modules";
import { EventController } from "./controllers/event.controller";
import { LogEventEmitterService } from "./log-event-emitter.service";

@Module({})
export class DebugModule {
  static forRoot() {
    return {
      module: DebugModule,
      providers: [LogEventEmitterService],
      exports: [LogEventEmitterService],
      controllers: process.env.NODE_ENV === NodeEnv.LOCAL ? [EventController] : []
    };
  }
}
