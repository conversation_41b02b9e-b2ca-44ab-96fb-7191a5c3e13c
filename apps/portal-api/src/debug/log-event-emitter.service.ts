import { Injectable, Logger } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";

function getCircularReplacer() {
  const ancestors = [];
  return function (key, value) {
    if (typeof value !== "object" || value === null) {
      return value;
    }
    // `this` is the object that value is contained in,
    // i.e., its direct parent.
    while (ancestors.length > 0 && ancestors.at(-1) !== this) {
      ancestors.pop();
    }
    if (ancestors.includes(value)) {
      return "[Circular]";
    }
    ancestors.push(value);
    return value;
  };
}

@Injectable()
export class LogEventEmitterService {
  private readonly logger = new Logger("EventEmitter2");

  constructor(private readonly eventEmitter: EventEmitter2) {
    this.init();
  }

  init() {
    this.eventEmitter.onAny((event, payload) => {
      try {
        this.logger.log(`{ event: ${event}, payload: ${JSON.stringify(payload)} }`);
      } catch (e) {
        this.logger.warn(`{ event: ${event}, payload: ${e.message} }`);
      }
    });
  }
}
