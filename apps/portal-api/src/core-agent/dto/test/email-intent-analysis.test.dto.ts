import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString, ValidateNested } from "class-validator";
import { Type } from "class-transformer";

// Placeholder for Attachment DTO if needed, currently using any
class TestAttachmentDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  filename?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  contentType?: string;

  // Add other relevant attachment properties if necessary
}

export class TestAnalyzeEmailIntentsDto {
  @ApiProperty({
    description: "The main text content of the email.",
    example: "Hello, what is the status of my shipment #12345?"
  })
  @IsString()
  @IsNotEmpty()
  text: string;

  @ApiPropertyOptional({
    description: "The subject line of the email.",
    example: "Shipment Status Inquiry #12345"
  })
  @IsOptional()
  @IsString()
  subject?: string | null;

  @ApiPropertyOptional({
    description: "Array of strings representing the email history.",
    example: ["On Wed, 22 May 2024 10:00:00 +0000, User wrote:", "> Previous message"]
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  emailHistory?: string[];

  @ApiPropertyOptional({ description: "Array of attachment objects.", type: [TestAttachmentDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TestAttachmentDto)
  attachments?: TestAttachmentDto[];

  @ApiPropertyOptional({ description: "Optional organization ID to scope the request." })
  @IsOptional()
  @IsNumber()
  organizationId?: number;
}

/**
 * DTO for batch testing the email pipeline with multiple inputs.
 */
export class BatchTestAnalyzeEmailIntentsDto {
  @ApiProperty({
    description: "Array of email pipeline test cases.",
    type: [TestAnalyzeEmailIntentsDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TestAnalyzeEmailIntentsDto)
  tests: TestAnalyzeEmailIntentsDto[];
}
