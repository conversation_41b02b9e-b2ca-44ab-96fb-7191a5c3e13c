import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, <PERSON>N<PERSON>ber, IsOptional, IsString } from "class-validator";
import { Type } from "class-transformer";

/**
 * DTO for requests that primarily involve extracting structured requests from a natural language message.
 */
export class ExtractRequestsDto {
  @ApiProperty({
    description: "The natural language message containing requests.",
    example: "Please update the ETA for shipment HBL123 to tomorrow."
  })
  @IsString()
  @IsNotEmpty()
  message: string;
}

/**
 * DTO for classifying a user's query into a predefined category.
 */
export class ClassifyQueryDto {
  @ApiProperty({
    description: "The user's query to classify.",
    example: "What is the status of my shipment?"
  })
  @IsString()
  @IsNotEmpty()
  userQuery: string;
}

/**
 * DTO for retrieving a specific shipment by its ID.
 */
export class GetShipmentDto {
  @ApiProperty({ description: "The unique identifier for the shipment.", example: 123 })
  @IsNumber()
  @Type(() => Number)
  shipmentId: number;
}

/**
 * Base DTO for processing a text message, potentially scoped to an organization.
 */
export class ProcessTextInputDto {
  @ApiProperty({
    description: "The primary text message content to process.",
    example:
      "Can you tell me the arrival date for shipment with HBL HBLXYZ789? Also create a new shipment for PO 456."
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({
    description: "Optional organization ID to scope the processing.",
    example: 1,
    required: false
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  organizationId?: number;
}

/**
 * Extends ProcessTextInputDto to include an optional subject line.
 */
export class ProcessTextInputWithSubjectDto extends ProcessTextInputDto {
  @ApiProperty({
    description: "Optional subject line associated with the message.",
    example: "Shipment Status Request for HBL123456",
    required: false
  })
  @IsString()
  @IsOptional()
  subject?: string;
}

/**
 * DTO specifically for testing the handling of directly answerable queries
 * that require both the query text and a known shipment ID.
 */
export class TestDirectlyAnswerableDto {
  @ApiProperty({ description: "The user query text.", example: "What is the status of my shipment?" })
  @IsString()
  @IsNotEmpty()
  userQuery: string;

  @ApiProperty({ description: "The ID of the shipment relevant to the query.", example: 123 })
  @IsNumber()
  @IsNotEmpty()
  @Type(() => Number)
  shipmentId: number;
}
