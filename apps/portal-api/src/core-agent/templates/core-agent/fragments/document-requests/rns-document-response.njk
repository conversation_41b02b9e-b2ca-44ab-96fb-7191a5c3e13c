{# RNS Document Response - Consolidated Template
   Data dependencies:
   - shipment.customsStatus: Current customs status
   - smartTemplateContext.rnsDocumentAvailable: Whether RNS can be generated
   - smartTemplateContext.documentStatus: Document availability
   - smartTemplateContext.documentBlockerReason: Why document is blocked
   - complianceDetails.missingFieldsList: Missing compliance fields
   - rnsProofContent: RNS proof content (if available)
#}

{% if not smartTemplateContext.rnsDocumentAvailable %}
  {% if shipment.customsStatus in ['pending-commercial-invoice', 'pending-confirmation'] %}
We can't provide the RNS yet as {{ smartTemplateContext.documentBlockerReason or "we're missing required documents" }}.

    {% if shipment.customsStatus == 'pending-commercial-invoice' %}
CI & PL: {{ smartTemplateContext.documentStatus.ciPl or '**Missing**' }}

    {% elif complianceDetails.hasMissingFields %}
{% for field in complianceDetails.missingFieldsList %}{{ field }} {% endfor %}

    {% endif %}
  {% elif shipment.customsStatus in ['pending-arrival', 'live'] %}
The RNS can only be provided after the shipment has been submitted to customs.

  {% elif shipment.customsStatus == 'entry-submitted' %}
The RNS can only be provided after the shipment has been accepted by customs.

  {% else %}
RNS proof of release is not yet available for this shipment.

  {% endif %}
{% else %}
  {% if shipment.customsStatus == 'released' %}
RNS Proof of Release information:
  {% else %}
RNS information:
  {% endif %}

  {% if rnsProofContent %}
<div style="font-family: monospace; background-color: #f5f5f5; padding: 15px; border: 1px solid #ddd; border-radius: 4px;">
<strong>RNS PROOF OF RELEASE</strong><br /><br />
{{ rnsProofContent | safe }}
</div>
  {% endif %}
{% endif %} 