{# CAD Document Response - Consolidated Template
   Data dependencies:
   - shipment.customsStatus: Current customs status
   - smartTemplateContext.cadDocumentAvailable: Whether CAD can be generated
   - smartTemplateContext.documentStatus: Document availability
   - smartTemplateContext.documentBlockerReason: Why document is blocked
   - complianceDetails.missingFieldsList: Missing compliance fields
   - cadDocument: CAD attachment data (if available)
#}

{% if not smartTemplateContext.cadDocumentAvailable %}
  {% if shipment.customsStatus == 'pending-commercial-invoice' %}
We can't provide the CAD yet as we're missing the following document(s). Please send them at your earliest convenience so we can proceed with filing.

CI & PL: {{ smartTemplateContext.documentStatus.ciPl or '**Missing**' }}

  {% elif shipment.customsStatus == 'pending-confirmation' %}
We're currently unable to provide the CAD as there are compliance issues or missing required fields. Please respond to the email and provide additional information for the missing fields.

{% if complianceDetails.hasMissingFields %}
{% for field in complianceDetails.missingFieldsList %}{{ field }} {% endfor %}
{% endif %}

  {% elif shipment.customsStatus == 'pending-arrival' %}
Send email to backoffice to send them the CAD.

  {% else %}
We've received your request for the CAD (Customs Accounting Document), however it cannot be generated at this time.

  {% endif %}
{% else %}
Please see CAD document attached.

{% endif %} 