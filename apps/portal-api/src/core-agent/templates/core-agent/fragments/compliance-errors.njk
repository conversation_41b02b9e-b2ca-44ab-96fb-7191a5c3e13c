{# Compliance errors fragment #}
{% if SHOW_COMPLIANCE_ERRORS %}
{% if COMPLIANCE_ERRORS_PLACEHOLDER and COMPLIANCE_ERRORS_PLACEHOLDER != 'None' %}
<p><strong>Compliance Issues:</strong></p>
<p>The following compliance issues need to be addressed:</p>
<ul>
  {% set errors = COMPLIANCE_ERRORS_PLACEHOLDER.split(', ') %}
  {% for error in errors %}
  <li>{{ error }}</li>
  {% endfor %}
</ul>

{% if CONTACT_SUPPORT_MESSAGE %}
<p>{{ CONTACT_SUPPORT_MESSAGE }}</p>
{% endif %}
{% endif %}
{% endif %}