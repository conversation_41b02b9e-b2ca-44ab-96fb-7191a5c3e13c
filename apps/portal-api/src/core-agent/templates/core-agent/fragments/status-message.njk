{# Consolidated Status Messages Fragment
   Data dependencies:
   - shipment.customsStatus: Current customs status
   - missingFieldsFormatted: Formatted missing fields (for pending-confirmation)
   - smartTemplateContext.hasETA: Whether ETA is available
   - smartTemplateContext.etaDate: ETA date if available
   - shipment.releaseDate: Release date if available
#}
{% if shipment.customsStatus == 'pending-commercial-invoice' %}
<p>Please send the missing document shown below at your earliest convenience for the subject shipment, so we can file customs without delay.</p>

{% include "core-agent/fragments/compliance-error-block.njk" %}

{% elif shipment.customsStatus == 'pending-confirmation' %}
There are compliance issues or missing required fields preventing submission of the subject shipment. Please respond to the email and provide additional information for the missing fields.

{% include "core-agent/fragments/compliance-error-block.njk" %}

{% elif shipment.customsStatus == 'pending-arrival' %}
{% if smartTemplateContext.hasETA %}
The estimated time of arrival (ETA) at the port for the subject shipment is {{ smartTemplateContext.etaDate }}. We expect to submit the customs entry as the arrival date approaches.
{% else %}
The submission is pending the shipment's arrival. We expect to submit the customs entry as the arrival date approaches.
{% endif %}

{% elif shipment.customsStatus == 'live' %}
The submission for the subject shipment has been initiated. We will let you know once released by customs.

{% elif shipment.customsStatus == 'entry-submitted' %}
The entry for the subject shipment has been submitted. We will let you know once released by customs.

{% elif shipment.customsStatus == 'entry-accepted' %}
The subject shipment entry has been accepted by Customs and is awaiting arrival of goods.

{% elif shipment.customsStatus == 'exam' %}
The subject shipment has been selected by customs for examination. We are contacting you for further information.
{% if smartTemplateContext.hasETA %}
The ETA of the cargo at destination is {{ smartTemplateContext.etaDate }}.
{% endif %}

{% elif shipment.customsStatus == 'released' %}
{% if smartTemplateContext.formattedReleaseDate and smartTemplateContext.formattedReleaseDate != 'TBD' %}
The subject shipment has been released by CBSA on {{ smartTemplateContext.formattedReleaseDate }}.
{% else %}
The subject shipment has been released by CBSA.
{% endif %}

{% else %}
{# Fallback for unknown status #}
Status update: {{ shipment.customsStatus }}

{% endif %} 