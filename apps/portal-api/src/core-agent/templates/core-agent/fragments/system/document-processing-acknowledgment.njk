{# Document Processing Acknowledgment Fragment
   Data dependencies:
   - smartTemplateContext.transportMode: Transport mode for conditional transaction number message
   - acknowledgment: Optional custom acknowledgment message
#}
{% if acknowledgment %}
{{ acknowledgment }}
{% else %}
We have received the required documents for your shipment and are currently reviewing and processing them.{% if smartTemplateContext.transportMode == 'TRUCK' %} Transaction# will be sent to you shortly.{% endif %}
{% endif %}
