{# CAD Document Response Fragment
   Data dependencies:
   - shipment.customsStatus: Current customs status
   - cadDocument: CAD attachment data (if available - indicates CAD was actually generated)
   - smartTemplateContext.transactionNumber: Transaction number if available
   - formattedCustomsStatus: User-friendly status display
   - complianceDetails: Compliance error information for blocked statuses
#}
{% if cadDocument %}
{# CAD document was actually generated and attached #}
Please see CAD document attached.
{% if smartTemplateContext.transactionNumber %}
<br />Transaction Number: <strong>{{ smartTemplateContext.transactionNumber }}</strong>
{% endif %}

{% elif shipment.customsStatus == 'pending-commercial-invoice' %}
We can't provide the CAD yet as we're missing the following document(s). Please send them at your earliest convenience so we can proceed with filing.

{% include "core-agent/fragments/compliance-error-block.njk" %}

{% elif shipment.customsStatus == 'pending-confirmation' %}
We're currently unable to provide the CAD as there are compliance issues or missing required fields. Please respond to the email and provide additional information for the missing fields.

{% include "core-agent/fragments/compliance-error-block.njk" %}

{% elif shipment.customsStatus == 'pending-arrival' %}
The CAD document will be available once the customs entry is accepted. Current status: {{ formattedCustomsStatus or shipment.customsStatus }}.
{% if smartTemplateContext.transactionNumber %}
<br />Transaction Number: <strong>{{ smartTemplateContext.transactionNumber }}</strong>
{% endif %}

{% elif shipment.customsStatus == 'live' %}
The customs entry is ready for submission. The CAD document will be available once accepted by customs.
{% if smartTemplateContext.transactionNumber %}
<br />Transaction Number: <strong>{{ smartTemplateContext.transactionNumber }}</strong>
{% endif %}

{% elif shipment.customsStatus == 'entry-submitted' %}
The customs entry has been submitted and is awaiting acceptance. The CAD document will be available once accepted.
{% if smartTemplateContext.transactionNumber %}
<br />Transaction Number: <strong>{{ smartTemplateContext.transactionNumber }}</strong>
{% endif %}

{% else %}
{# Fallback for unknown status or statuses where CAD is not available #}
The CAD document is not yet available for this shipment status. Current status: {{ formattedCustomsStatus or shipment.customsStatus }}.
{% if smartTemplateContext.transactionNumber %}
<br />Transaction Number: <strong>{{ smartTemplateContext.transactionNumber }}</strong>
{% endif %}

{% endif %}