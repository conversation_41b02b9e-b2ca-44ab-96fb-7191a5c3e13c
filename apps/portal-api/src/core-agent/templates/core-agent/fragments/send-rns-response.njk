{# Consolidated Status Messages Fragment
   Data dependencies:
   - shipment.customsStatus: Current customs status
   - missingFieldsFormatted: Formatted missing fields (for pending-confirmation)
   - smartTemplateContext.hasETA: Whether ETA is available
   - smartTemplateContext.etaDate: ETA date if available
   - shipment.releaseDate: Release date if available
#}
{% if shipment.customsStatus == 'pending-commercial-invoice' %}
We can't provide the RNS yet as we're missing the following document(s). Please send them at your earliest convenience so we can proceed with filing.

{% include "core-agent/fragments/compliance-error-block.njk" %}

{% elif shipment.customsStatus == 'pending-confirmation' %}
We're currently unable to provide the RNS as there are compliance issues or missing required fields. Please respond to the email and provide additional information for the missing fields.

{% include "core-agent/fragments/compliance-error-block.njk" %}

{% elif shipment.customsStatus == 'pending-arrival' %}
The RNS can only be provided after the shipment has been submitted to customs.

{% elif shipment.customsStatus == 'live' %}
The RNS can only be provided after the shipment has been submitted to customs.

{% elif shipment.customsStatus == 'entry-submitted' %}
The RNS can only be provided after the shipment has been accepted by customs.

{% elif shipment.customsStatus == 'entry-accepted' %}
RNS information:
{% include 'core-agent/fragments/document-requests/rns-document-response.njk' %}

{% elif shipment.customsStatus == 'exam' %}
RNS information:
{% include 'core-agent/fragments/document-requests/rns-document-response.njk' %}

{% elif shipment.customsStatus == 'released' %}
RNS Proof of Release information:
{% include 'core-agent/fragments/document-requests/rns-document-response.njk' %}

{% elif shipment.customsStatus == 'accounting-completed' %}
RNS Proof of Release information:
{% include 'core-agent/fragments/document-requests/rns-document-response.njk' %}

{% else %}
{# Fallback for unknown status #}
Status update: {{ shipment.customsStatus }}
We're unable to provide the RNS at this time. Please contact our support team for assistance.

{% endif %} 