# Fragment-Based Email Response System

## Overview

The fragment-based system breaks down email responses into atomic, reusable template fragments that can be combined to create comprehensive responses while avoiding duplication.

## Architecture

### Fragment Types

1. **Status Messages** (`status-messages/`): Customs status-specific messages
2. **Details** (`details/`): Common information blocks (identifiers, document status, etc.)
3. **Document Requests** (`document-requests/`): Consolidated document request responses
4. **System** (`system/`): Error messages and system notifications

### Fragment Structure

Each fragment is a standalone Nunjucks template that:
- Contains a single logical unit of information
- Has clear data dependencies documented in comments
- Can be rendered independently
- Avoids overlapping information with other fragments

## Status Message Fragments

Located in `status-messages/`, these provide customs status-specific messages:

- `pending-commercial-invoice.njk` - Missing CI & PL documents
- `pending-confirmation.njk` - Compliance issues/missing fields
- `pending-arrival.njk` - Awaiting shipment arrival
- `live.njk` - Submission initiated
- `entry-submitted.njk` - Entry submitted to customs
- `entry-accepted.njk` - Entry accepted, awaiting arrival
- `exam.njk` - Selected for customs examination
- `released.njk` - Released by CBSA

## Detail Fragments

Located in `details/`, these provide common information blocks:

- `shipment-identifiers.njk` - CCN#, Container#, HBL# information
- `document-status.njk` - Document receipt status (HBL, AN/EMF, CI&PL)
- `status-line.njk` - Final status line with missing fields and customs status

## Document Request Fragments (Consolidated)

Located in `document-requests/`, these handle all scenarios for each document type:

- `rush-processing-response.njk` - **CONSOLIDATED** - All rush processing scenarios by customs status
- `cad-document-response.njk` - **CONSOLIDATED** - All CAD document availability scenarios by customs status
- `rns-document-response.njk` - **CONSOLIDATED** - All RNS document availability scenarios by customs status

### Consolidated Template Benefits

Each consolidated template handles multiple scenarios based on customs status and availability:

**Rush Processing Response:**
- `pending-commercial-invoice`: Shows missing documents message
- `pending-confirmation`: Shows compliance issues message
- `pending-arrival/live`: "We've received your rush request and will be submitting the entry right away"
- `entry-submitted`: "The entry has already been submitted"
- `entry-accepted`: "The entry has already been submitted and accepted"
- `exam`: "Selected for exam and will be released once examination is complete"
- `released`: "Already released by CBSA"

**CAD Document Response:**
- `pending-commercial-invoice`: "Can't provide CAD yet - missing documents"
- `pending-confirmation`: "Can't provide CAD - compliance issues"
- `pending-arrival`: "Send email to backoffice to send them the CAD"
- Available statuses: "Please see CAD document attached"

**RNS Document Response:**
- Early statuses: "RNS can only be provided after submission/acceptance"
- `entry-accepted/exam`: "RNS information:"
- `released`: "RNS Proof of Release information:" + formatted proof content

## System Fragments

Located in `system/`, these handle system-level messages:

- `error-message.njk` - Generic error message with optional custom reason
- `documentation-coming-acknowledgment.njk` - Acknowledgment for incoming documents
- `missing-documents-acknowledgment.njk` - Missing documents notification
- `document-processing-acknowledgment.njk` - Document processing confirmation

## Handler Implementation

### Fragment Selection Logic

```typescript
// Single consolidated fragment per intent
fragments.push({
  template: "core-agent/fragments/document-requests/rush-processing-response",
  priority: 1,
  fragmentContext: {
    shipment: context.shipment,
    smartTemplateContext: context.smartTemplateContext,
    complianceDetails: context.missingFieldsAnalysis
  }
});

// Detail fragments (priorities 10-12)
fragments.push({
  template: "core-agent/fragments/details/shipment-identifiers",
  priority: 10,
  fragmentContext: enhancedContext
});
```

### Priority System

- Priority 1: Primary response content (consolidated document responses)
- Priority 10-12: Detail blocks
- Lower numbers = higher priority in final output

### Context Sharing

All fragments receive the same enhanced context containing:
- Original shipment context
- Smart template context (availability flags)
- Compliance details (missing fields analysis)
- Document status information

## Benefits

1. **Deduplication**: Each piece of information appears only once
2. **Reusability**: Fragments can be used across different handlers
3. **Maintainability**: Changes to specific information types are centralized
4. **Testability**: Individual fragments can be tested in isolation
5. **Flexibility**: Easy to add/remove/reorder information blocks
6. **Consolidation**: Single templates handle all scenarios per intent type

## Migrated Handlers

The following handlers have been successfully migrated to the consolidated fragment system:

1. **✅ GetShipmentStatusHandler** - Uses status message + detail fragments
2. **✅ RequestCADDocumentHandler** - Uses consolidated CAD document response + detail fragments
3. **✅ RequestRNSProofHandler** - Uses consolidated RNS document response + detail fragments
4. **✅ RequestRushProcessingHandler** - Uses consolidated rush processing response + detail fragments

## Template Dependencies

### Rush Processing Response Template
- `shipment.customsStatus`: Current customs status
- `smartTemplateContext.documentStatus`: Document availability
- `smartTemplateContext.releaseDate`: Release date if available
- `complianceDetails.missingFieldsList`: Missing compliance fields

### CAD Document Response Template
- `shipment.customsStatus`: Current customs status
- `smartTemplateContext.cadDocumentAvailable`: Whether CAD can be generated
- `smartTemplateContext.documentStatus`: Document availability
- `complianceDetails.missingFieldsList`: Missing compliance fields

### RNS Document Response Template
- `shipment.customsStatus`: Current customs status
- `smartTemplateContext.rnsDocumentAvailable`: Whether RNS can be generated
- `smartTemplateContext.documentStatus`: Document availability
- `complianceDetails.missingFieldsList`: Missing compliance fields
- `rnsProofContent`: Formatted RNS proof content (if available)

## Migration Patterns & Lessons Learned

### Consolidated Template Patterns

1. **Single Intent, Multiple Scenarios**: Each document request type has one template that handles all customs statuses
2. **Availability-Based Logic**: Templates check availability flags before determining response type
3. **Status-Based Branching**: Use `{% if shipment.customsStatus == 'status' %}` for scenario selection
4. **Context Consistency**: All templates receive the same enhanced context structure

### Benefits of Consolidation

- **Reduced Template Count**: From 6+ templates per intent to 1 consolidated template
- **Easier Maintenance**: All scenarios for an intent in one location
- **Better Logic Reuse**: Common patterns handled once per intent
- **Simplified Handler Logic**: No complex template selection logic needed

## Future Enhancements

- Additional consolidated templates for other intent types
- Fragment composition rules for complex scenarios
- Fragment caching for performance optimization
- Automated testing framework for consolidated template scenarios
