{# Consolidated Shipment Identifiers Template
   Displays CCN#, Container#, and HBL# information
   
   Data dependencies:
   - shipmentIdentifiers.cargoControlNumber: CCN number
   - shipmentIdentifiers.hasMultipleContainers: Boolean for multiple containers
   - shipmentIdentifiers.formattedContainers: Formatted container list for multiple
   - shipmentIdentifiers.primaryContainer: Single container number
   - shipmentIdentifiers.hblNumber: HBL number
#}
{% if shipmentIdentifiers %}
  {% if shipmentIdentifiers.cargoControlNumber %}CCN#: <strong>{{ shipmentIdentifiers.cargoControlNumber }}</strong>{% if shipmentIdentifiers.hasMultipleContainers or shipmentIdentifiers.primaryContainer %}, {% endif %}{% endif %}
  {%- if shipmentIdentifiers.hasMultipleContainers -%}
    Containers#: <strong>{{ shipmentIdentifiers.formattedContainers }}</strong>
  {%- elif shipmentIdentifiers.primaryContainer -%}
    Container#: <strong>{{ shipmentIdentifiers.primaryContainer }}</strong>
  {%- endif -%}
  {%- if shipmentIdentifiers.hblNumber %}, HBL#: <strong>{{ shipmentIdentifiers.hblNumber }}</strong>{% endif %}<br/>
{% endif %}