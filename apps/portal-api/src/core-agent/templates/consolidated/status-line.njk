{# Consolidated Status Line Template
   Shows additional validation issues and final status with signature
   
   Data dependencies:
   - showAdditionalValidation: Boolean flag to control additional validation display
   - additionalValidation.issues: Array of additional validation issues
   - formattedCustomsStatus: Formatted customs status
   - shipment.customsStatus: Raw customs status (fallback)
#}
{# Additional validation line (when present) #}
{% if showAdditionalValidation and additionalValidation and additionalValidation.issues %}
  {% for issue in additionalValidation.issues %}{{ issue }} {% endfor %}<br/>
{% endif %}

Status: <strong>{{ formattedCustomsStatus or shipment.customsStatus }}</strong>

<p style="margin-top: 20px;">
Best regards,<br/>
Claro Customs
</p>