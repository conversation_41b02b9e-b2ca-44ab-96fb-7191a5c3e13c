{# Consolidated Main Messages Template
   <PERSON>les multiple intents with priority ordering
   
   Data dependencies:
   - mainMessages: Array of message objects with content, priority, type, and optional attachments
   
   Example structure:
   mainMessages: [
     { content: "Your shipment was released on March 15th.", priority: 1, type: "status" },
     { content: null, priority: 2, type: "cad-messages", attachments: { cadDocument: {...} } },
     { content: null, priority: 3, type: "document-processing-messages", attachments: { documents: [...] } },
     { content: null, priority: 4, type: "acknowledge-documents-messages", attachments: { acknowledgment: "..." } }
   ]
   
   For type-based messages, content is null and the appropriate template is included
#}
{% if mainMessages and mainMessages.length > 0 %}
  {% for message in mainMessages | sort(attribute='priority') %}
    {% if message.type == 'cad' %}
      <p>{% include 'consolidated/messages/cad-messages.njk' %}</p>
    {% elif message.type == 'cad-messages' %}
      <p>{% include 'consolidated/messages/cad-messages.njk' %}</p>
    {% elif message.type == 'rns' %}
      <p>{% include 'consolidated/messages/rns-messages.njk' %}</p>
    {% elif message.type == 'rush' %}
      <p>{% include 'consolidated/messages/rush-messages.njk' %}</p>
    {% elif message.type == 'rush-messages' %}
      <p>{% include 'consolidated/messages/rush-messages.njk' %}</p>
    {% elif message.type == 'document-processing-messages' %}
      <p>{% include 'consolidated/messages/document-processing-messages.njk' %}</p>
    {% elif message.type == 'acknowledge-documents-messages' %}
      <p>{% include 'consolidated/messages/acknowledge-documents-messages.njk' %}</p>
    {% elif message.type == 'acknowledge-missing-documents-messages' %}
      <p>{% include 'consolidated/messages/acknowledge-missing-documents-messages.njk' %}</p>
    {% elif message.type == 'documentation-coming-messages' %}
      <p>{% include 'consolidated/messages/documentation-coming-messages.njk' %}</p>
    {% elif message.type == 'manual-processing-messages' %}
      <p>{% include 'consolidated/messages/manual-processing-messages.njk' %}</p>
    {% elif message.type == 'hold-shipment-messages' %}
      <p>{% include 'consolidated/messages/hold-shipment-messages.njk' %}</p>
    {% elif message.type == 'shipment-update-messages' %}
      <p>{% include 'consolidated/messages/shipment-update-messages.njk' %}</p>
    {% elif message.type == 'shipment-update-error-messages' %}
      <p>{% include 'consolidated/messages/shipment-update-error-messages.njk' %}</p>
    {% elif message.content %}
      <p>{{ message.content }}</p>
    {% endif %}
    {# Attachment handling is done by email layer, not template #}
  {% endfor %}
{% endif %}