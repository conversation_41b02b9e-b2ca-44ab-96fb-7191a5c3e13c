{# Shipment Update Success Messages Template
   
   Data dependencies:
   - message.attachments.acknowledgment: Success acknowledgment message
   - message.attachments.shipmentId: ID of updated shipment
   - message.attachments.updatedFields: Array of field names that were updated
   - message.attachments.updateData: Object containing the updated values
   
   Returns shipment update success message
#}
{% if message.attachments.acknowledgment %}
{{ message.attachments.acknowledgment }}
{% else %}
The shipment information has been updated successfully.
{% endif %}

{% if message.attachments.updatedFields and message.attachments.updatedFields.length > 0 %}
<p><strong>Updated fields:</strong></p>
<ul>
{% for field in message.attachments.updatedFields %}
  <li>{{ field }}{% if message.attachments.updateData[field] %}: {{ message.attachments.updateData[field] }}{% endif %}</li>
{% endfor %}
</ul>
{% endif %}

{% if message.attachments.shipmentId %}
<p><strong>Shipment ID:</strong> {{ message.attachments.shipmentId }}</p>
{% endif %}