{# Acknowledge Missing Documents Messages Template
   
   Data dependencies:
   - message.attachments.hasMissingDocs: Whether there are missing documents
   - message.attachments.shipment: Shipment object
   - message.attachments.shipmentIdentifiers: Shipment identifiers
   - message.attachments.smartTemplateContext: Smart template context
   
   Returns missing documents acknowledgment message
#}
<p>Thank you for your inquiry about the required documents for your shipment.</p>

{% if message.attachments.hasMissingDocs %}
<p>We are currently missing some required documents for your shipment. The document status section below will show you exactly which documents are needed.</p>
{% endif %}

{% if message.attachments.shipment and message.attachments.shipment.hbl %}
<p><strong>House Bill of Lading:</strong> {{ message.attachments.shipment.hbl }}</p>
{% endif %}

<p>Please provide the missing documents at your earliest convenience so we can continue processing your shipment.</p>