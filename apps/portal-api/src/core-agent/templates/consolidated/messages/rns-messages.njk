{# RNS Proof Request Messages Template
   
   Data dependencies:
   - shipment.customsStatus: Current customs status
   - message.attachments.rnsData: RNS proof data (if available)
   - smartTemplateContext.rnsDocumentAvailable: Whether RNS can be generated
   
   Returns appropriate RNS message based on shipment status
#}
{% if message.attachments and message.attachments.rnsData %}
{# RNS proof data is available - show it #}
{% if shipment.customsStatus == 'released' %}
RNS Proof of Release information:
{% else %}
RNS information:
{% endif %}

{% if message.attachments.rnsData.content %}
{{ message.attachments.rnsData.content | safe }}
{% endif %}

{% elif shipment.customsStatus == 'pending-commercial-invoice' or shipment.customsStatus == 'pending-confirmation' %}
We can't provide the RNS yet as we're missing the following document(s). Please send them at your earliest convenience so we can proceed with filing.

{% elif shipment.customsStatus == 'pending-arrival' or shipment.customsStatus == 'live' %}
The RNS can only be provided after the shipment has been submitted to customs.

{% elif shipment.customsStatus == 'entry-submitted' %}
The RNS can only be provided after the shipment has been accepted by customs.

{% elif shipment.customsStatus == 'entry-accepted' or shipment.customsStatus == 'exam' %}
RNS information:
{% if smartTemplateContext.rnsDocumentAvailable %}
{# Show RNS content would go here, but not available in current context #}
RNS document is available for this shipment status.
{% else %}
RNS information is not yet available. Please contact support for assistance.
{% endif %}

{% elif shipment.customsStatus == 'released' %}
RNS Proof of Release information:
{% if smartTemplateContext.rnsDocumentAvailable %}
{# Show RNS content would go here, but not available in current context #}
RNS proof of release is available for this shipment.
{% else %}
RNS proof of release is not yet available. Please contact support for assistance.
{% endif %}

{% else %}
RNS information is not available for this shipment status.

{% endif %}