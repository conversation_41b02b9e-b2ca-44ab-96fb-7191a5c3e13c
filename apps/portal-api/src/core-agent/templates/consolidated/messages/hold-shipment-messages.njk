{# Hold Shipment Request Messages Template
   
   Data dependencies:
   - message.attachments.acknowledgment: Custom acknowledgment message
   - message.attachments.instructions: User instructions/requests
   - message.attachments.backofficeAlerts: Backoffice alert status
   
   Returns hold shipment acknowledgment message
#}
{% if message.attachments.acknowledgment %}
{{ message.attachments.acknowledgment }}
{% else %}
We've received your request to cancel/hold the entry. Our team has been notified and will take the necessary action.
{% endif %}

{% if message.attachments.backofficeAlerts.holdShipmentSent %}
<p>Your request has been forwarded to our team and they will handle this accordingly.</p>
{% else %}
<p>Our team will review this request and take the necessary action.</p>
{% endif %}