{# Rush Processing Request Messages Template
   
   Data dependencies:
   - shipment.customsStatus: Current customs status
   - smartTemplateContext.formattedReleaseDate: Release date if available
   - message.attachments.submissionResult: Submission workflow results
   - message.attachments.submissionError: Submission error message
   
   Returns appropriate rush message based on shipment status
#}
{% if shipment.customsStatus == 'pending-commercial-invoice' %}
Please send the missing document shown below at your earliest convenience for the subject shipment, so we can file customs without delay.

{% elif shipment.customsStatus == 'pending-confirmation' %}
There are compliance issues or missing required fields preventing submission of the subject shipment. Please respond to the email and provide additional information for the missing fields.

{% elif shipment.customsStatus == 'pending-arrival' or shipment.customsStatus == 'live' %}
We've received your rush request and will be submitting the entry right away.

{% elif shipment.customsStatus == 'entry-submitted' %}
The entry for the subject shipment has already been submitted. We will let you know once released by customs.

{% elif shipment.customsStatus == 'entry-accepted' %}
The subject shipment entry has already been submitted and accepted by Customs and is awaiting arrival of goods.

{% elif shipment.customsStatus == 'exam' %}
Please note the shipment has been selected for exam and will be released once the examination is complete.

{% elif shipment.customsStatus == 'released' %}
{% if smartTemplateContext.formattedReleaseDate %}
The subject shipment has been released by CBSA on {{ smartTemplateContext.formattedReleaseDate }}.
{% else %}
The subject shipment has been released by CBSA.
{% endif %}

{% else %}
We've received your rush request and our team will process this accordingly.

{% endif %}

{# Display submission results if available #}
{% if message.attachments.submissionResult %}
  {% if message.attachments.submissionResult.liveShipmentId %}
    <p><strong>Update:</strong> Your shipment entry has been submitted to customs as part of this rush request.</p>
  {% elif message.attachments.submissionResult.liveEntryUploadFailedShipment %}
    <p><strong>Note:</strong> We encountered an issue submitting your entry: {{ message.attachments.submissionResult.liveEntryUploadFailedShipment.failedReason }}</p>
  {% elif message.attachments.submissionResult.customsStatusCheckErrorShipment %}
    <p><strong>Status:</strong> {{ message.attachments.submissionResult.customsStatusCheckErrorShipment.errorMessage }}</p>
  {% elif message.attachments.submissionResult.shipmentStatusUpdate %}
    <p><strong>Status Update:</strong> Your shipment status has been updated to {{ message.attachments.submissionResult.shipmentStatusUpdate.newStatus }}.</p>
  {% endif %}
{% elif message.attachments.submissionError %}
  <p><strong>Note:</strong> We encountered an issue processing your rush request. Our team has been notified and will handle this manually.</p>
{% endif %}