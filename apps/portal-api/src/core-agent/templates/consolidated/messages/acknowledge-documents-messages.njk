{# Acknowledge Documents Messages Template
   
   Data dependencies:
   - message.attachments.allDocsReceived: Whether all documents have been received
   - message.attachments.transportMode: Transport mode of the shipment
   - message.attachments.shipment: Shipment object
   - message.attachments.shipmentIdentifiers: Shipment identifiers
   - message.attachments.smartTemplateContext: Smart template context
   
   Returns document acknowledgment message
#}
<p>Thank you for providing the documents. We have received them and will process your shipment accordingly.</p>

{% if message.attachments.allDocsReceived %}
<p>All required documents have been received and your shipment is now ready for processing.</p>
{% endif %}

{% if message.attachments.transportMode %}
<p><strong>Transport Mode:</strong> {{ message.attachments.transportMode }}</p>
{% endif %}

{% if message.attachments.shipment and message.attachments.shipment.hbl %}
<p><strong>House Bill of Lading:</strong> {{ message.attachments.shipment.hbl }}</p>
{% endif %}