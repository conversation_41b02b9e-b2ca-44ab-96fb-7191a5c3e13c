{# 
  VISUAL EMAIL TEMPLATE STRUCTURE
  
  This template shows the complete visual layout with placeholders for each section.
  All sections are represented to show the full email structure.
#}



{# ========== GREETING SECTION ========== #}
<p>Hello,</p>


{# ========== RNS INFORMATION SECTION ========== #}
{% if SHOW_RNS_PROOF %}
<div style="font-family: monospace; background-color: #f5f5f5; padding: 15px; border: 1px solid #ddd; border-radius: 4px;">
<strong>RNS PROOF OF RELEASE</strong><br /><br />
{{ RNS_RESPONSE_MESSAGE }}<br />
Importer Name: {{ IMPORTER_NAME }}<br />
Process Date: {{ PROCESS_DATE }}<br />
Response Date: {{ RESPONSE_DATE }}<br />
Transaction #: {{ TRANSACTION_NUMBER }}<br />
Cargo Control #: {{ CARGO_CONTROL_NUMBER }}<br />
Port Code: {{ PORT_CODE }}<br />
Sublocation Code: {{ SUBLOCATION_CODE }}<br />
Container #: {{ CONTAINER_NUMBERS }}<br />
Release Code: {{ PROCESSING_INDICATOR }} - {{ RNS_RESPONSE_MESSAGE }}
</div>
{% endif %}


{# ========== DOCUMENT PROCESSING LIST ========== #}
{% if PROCESSED_DOCUMENTS %}
<p style="margin-top: 15px;">We've received and processed the following document(s):</p>
<ul style="list-style-type: none; padding-left: 0;">
  {% for document in PROCESSED_DOCUMENTS %}
  <li style="border: 1px solid #eee; padding: 10px; margin-bottom: 10px; border-radius: 4px;">
    <strong>{{ document.filename }}</strong>
    <ul style="margin-top: 5px; padding-left: 20px;">
      <li>Document Type: {{ document.contentType }}</li>
      <li>Status: {{ document.aggregationStatus | capitalize }}
        {% if document.aggregationStatus == 'success' %} ✅
        {% elif document.aggregationStatus == 'failed' %} ❌
        {% elif document.aggregationStatus == 'processing' %} 🔄
        {% elif document.aggregationStatus == 'pending' %} ⏳
        {% endif %}
      </li>
      <li>Claro URL: <a href="{{ document.claroUrl }}">{{ document.claroUrl }}</a></li>
    </ul>
  </li>
  {% endfor %}
</ul>
{% endif %}

{# ========== EMAIL RESPONSE MESSAGES SECTION ========== #}
{# Document Acknowledgment Handlers #}
<p>{{ ACKNOWLEDGE_DOCUMENTS_MESSAGE }}</p>
<p>{{ ACKNOWLEDGE_MISSING_DOCUMENTS_MESSAGE }}</p>
<p>{{ DOCUMENTATION_COMING_MESSAGE }}</p>
<p>{{ PROCESS_DOCUMENT_MESSAGE }}</p>


<p>{{ REQUEST_CAD_DOCUMENT_MESSAGE }}</p>
<p>{{ REQUEST_RNS_PROOF_MESSAGE }}</p>

{# Processing Request Handlers #}
<p>{{ REQUEST_RUSH_PROCESSING_MESSAGE }}</p>
<p>{{ REQUEST_MANUAL_PROCESSING_MESSAGE }}</p>
<p>{{ REQUEST_HOLD_SHIPMENT_MESSAGE }}</p>

{# Data Update Handlers #}
<p>{{ UPDATE_SHIPMENT_MESSAGE }}</p>

{# Dynamic Content Generators #}
<p>{{ ETA_RESPONSE_MESSAGE }}</p>
<p>{{ TRANSACTION_NUMBER_RESPONSE_MESSAGE }}</p>
<p>{{ RELEASE_STATUS_RESPONSE_MESSAGE }}</p>
<p>{{ SHIPPING_STATUS_RESPONSE_MESSAGE }}</p>

{# Error/Fallback Messages #}
<p>{{ SYSTEM_ERROR_MESSAGE }}</p>
<p>{{ CONTACT_SUPPORT_MESSAGE }}</p>

{# Customs Status #}
<p>{{ GET_SHIPMENT_STATUS_MESSAGE }}</p>

{% if SHOW_VALIDATION_ISSUES %}
{# ========== VALIDATION ISSUES ========== #}
{%- if CI_PL_MISSING %}CI & PL: <strong>Missing</strong><br/>{% endif -%}
{%- if HBL_MISSING %}HBL: <strong>Missing</strong><br/>{% endif -%}
{%- if AN_EMF_MISSING %}AN/EMF: <strong>Missing</strong><br/>{% endif -%}
{%- if WEIGHT_MISSING or PORT_CODE_MISSING or CCN_MISSING or OGD_FILING_PENDING -%}
{%- if WEIGHT_MISSING %}Weight: <strong>missing</strong> {% endif -%}
{%- if PORT_CODE_MISSING %}Port code: <strong>missing</strong> {% endif -%}
{%- if CCN_MISSING %}CCN: <strong>missing</strong> {% endif -%}
{%- if OGD_FILING_PENDING %}OGD filing: <strong>Pending</strong> {% endif -%}<br/>
{%- endif %}
{% endif %}

{% if SHOW_DETAILS %}
{# ========== DETAILS SECTION ========== #}
<p>
  <strong><u>Details:</u></strong><br/>
  CCN#: <strong>{{ CCN_PLACEHOLDER }}</strong>, Container#: <strong>{{ CONTAINER_PLACEHOLDER }}</strong>, HBL#: <strong>{{ HBL_PLACEHOLDER }}</strong><br/>
  HBL: <strong>{{ HBL_STATUS_PLACEHOLDER }}</strong> AN/EMF: <strong>{{ AN_EMF_STATUS_PLACEHOLDER }}</strong> CI&PL: <strong>{{ CI_PL_STATUS_PLACEHOLDER }}</strong><br/>
  Status: <strong>{{ CUSTOMS_STATUS_LINE_PLACEHOLDER }}</strong>
</p>
{% endif %}

{% if SHOW_VALIDATION_ISSUES %}
{# ========== VALIDATION ISSUES SECTION ========== #}
<p><strong>Outstanding Items:</strong></p>
<ul>
  <li>{{ MISSING_DOCUMENTS_PLACEHOLDER }}</li>
  <li>{{ MISSING_FIELDS_PLACEHOLDER }}</li>
</ul>
{% endif %}

{% if SHOW_COMPLIANCE_ERRORS %}
{# ========== COMPLIANCE ERRORS SECTION ========== #}
<p><strong>Compliance Issues:</strong></p>
<ul>
  <li>{{ COMPLIANCE_ERRORS_PLACEHOLDER }}</li>
</ul>
{% endif %}

{# ========== FOOTER SECTION ========== #}
<p style="margin-top: 20px;">
  Best regards,<br/>
  Claro Customs
</p>