#!/bin/bash

# REQUEST_RUSH_PROCESSING Test Runner Script
# This script runs both unit and integration tests for the REQUEST_RUSH_PROCESSING handler

set -e

echo "🚀 REQUEST_RUSH_PROCESSING Test Suite"
echo "====================================="

# Function to print colored output
print_status() {
    echo -e "\033[1;34m$1\033[0m"
}

print_success() {
    echo -e "\033[1;32m$1\033[0m"
}

print_error() {
    echo -e "\033[1;31m$1\033[0m"
}

# Check if we're in the right directory
if [[ ! -f "src/core-agent/handlers/request-rush-processing.handler.ts" ]]; then
    print_error "❌ Error: This script must be run from the portal-api directory"
    exit 1
fi

# Check if build is up to date
print_status "🔧 Building project..."
if ! rushx build; then
    print_error "❌ Build failed. Please fix build errors before running tests."
    exit 1
fi

print_success "✅ Build completed successfully"

# Run unit tests
print_status "🧪 Running unit tests..."
echo ""

if rushx jest --testPathPattern="request-rush-processing.handler.spec.ts" --verbose; then
    print_success "✅ Unit tests passed"
else
    print_error "❌ Unit tests failed"
    exit 1
fi

echo ""

# Run integration tests
print_status "🔗 Running integration tests..."
echo ""

# Parse command line arguments
VERBOSE=""
NO_SIDE_EFFECTS=""
ORG_ID=""
SHIPMENT_ID=""
TEST_FLAGS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --verbose)
            VERBOSE="--verbose"
            shift
            ;;
        --no-side-effects)
            NO_SIDE_EFFECTS="--no-side-effects"
            shift
            ;;
        --org=*)
            ORG_ID="$1"
            shift
            ;;
        --shipment=*)
            SHIPMENT_ID="$1"
            shift
            ;;
        --test-null-cases)
            TEST_FLAGS="$TEST_FLAGS --test-null-cases"
            shift
            ;;
        --test-compliance-errors)
            TEST_FLAGS="$TEST_FLAGS --test-compliance-errors"
            shift
            ;;
        --test-transaction-runner)
            TEST_FLAGS="$TEST_FLAGS --test-transaction-runner"
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Build integration test command
INTEGRATION_CMD="node src/core-agent/testing/test-request-rush-processing-integration.js"

if [[ -n "$VERBOSE" ]]; then
    INTEGRATION_CMD="$INTEGRATION_CMD $VERBOSE"
fi

if [[ -n "$NO_SIDE_EFFECTS" ]]; then
    INTEGRATION_CMD="$INTEGRATION_CMD $NO_SIDE_EFFECTS"
fi

if [[ -n "$ORG_ID" ]]; then
    INTEGRATION_CMD="$INTEGRATION_CMD $ORG_ID"
fi

if [[ -n "$SHIPMENT_ID" ]]; then
    INTEGRATION_CMD="$INTEGRATION_CMD $SHIPMENT_ID"
fi

if [[ -n "$TEST_FLAGS" ]]; then
    INTEGRATION_CMD="$INTEGRATION_CMD $TEST_FLAGS"
fi

# Run integration tests
if eval "$INTEGRATION_CMD"; then
    print_success "✅ Integration tests passed"
else
    print_error "❌ Integration tests failed"
    exit 1
fi

echo ""
print_success "🎉 All REQUEST_RUSH_PROCESSING tests completed successfully!"

# Optional: Run specific test scenarios
echo ""
print_status "🔍 Available test scenarios:"
echo "  • Normal rush processing request"
echo "  • Null shipment handling"
echo "  • Cannot rush scenarios"
echo "  • Already submitted cases"
echo "  • Compliance error handling"
echo "  • Submission service errors"
echo "  • Missing entry service"
echo "  • Transaction runner injection"
echo ""
echo "To run specific scenarios:"
echo "  ./run-rush-processing-tests.sh --test-null-cases"
echo "  ./run-rush-processing-tests.sh --test-compliance-errors"
echo "  ./run-rush-processing-tests.sh --test-transaction-runner"
echo "  ./run-rush-processing-tests.sh --verbose --org=3"