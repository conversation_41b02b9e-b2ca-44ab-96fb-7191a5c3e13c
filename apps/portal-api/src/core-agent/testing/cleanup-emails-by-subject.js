#!/usr/bin/env node

/**
 * Cleanup Emails by Subject Pattern Script
 *
 * This script finds emails matching a subject pattern and deletes them completely
 * using the EmailDeletionService, which removes all related entities.
 *
 * Usage:
 *   node scripts/cleanup-emails-by-subject.js "subject-pattern" [--dry-run] [--limit=100]
 *
 * Examples:
 *   # Build first using Rush
 *   rushx --to portal-api build
 *   cd apps/portal-api
 *
 *   # Preview what would be deleted (dry run)
 *   node scripts/cleanup-emails-by-subject.js "%8FPDNBBH24120005%" --dry-run
 *
 *   # Actually delete the emails
 *   node scripts/cleanup-emails-by-subject.js "%8FPDNBBH24120005%"
 *
 *   # Delete with custom limit
 *   node scripts/cleanup-emails-by-subject.js "%8FPDNBBH24120005%" --limit=50
 *
 * ⚠️  WARNING: This is a destructive operation that cannot be undone!
 */

const { AppModule } = require("../dist/app.module");
const { ContextIdFactory, NestFactory } = require("@nestjs/core");
const { EmailDeletionService } = require("../dist/email/services/email-deletion.service");
const { Email } = require("nest-modules");
const { getRepositoryToken } = require("@nestjs/typeorm");

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);

  if (args.length === 0) {
    console.log(
      'Usage: node scripts/cleanup-emails-by-subject.js "subject-pattern" [--dry-run] [--limit=100]'
    );
    console.log("");
    console.log("Examples:");
    console.log('  node scripts/cleanup-emails-by-subject.js "%8FPDNBBH24120005%" --dry-run');
    console.log('  node scripts/cleanup-emails-by-subject.js "%8FPDNBBH24120005%"');
    console.log('  node scripts/cleanup-emails-by-subject.js "%test%" --limit=50');
    console.log("");
    console.log("⚠️  WARNING: This is a destructive operation that cannot be undone!");
    process.exit(1);
  }

  const subjectPattern = args[0];
  const dryRun = args.includes("--dry-run");

  let limit = 100; // Default limit
  const limitArg = args.find((arg) => arg.startsWith("--limit="));
  if (limitArg) {
    limit = parseInt(limitArg.split("=")[1]);
    if (isNaN(limit) || limit <= 0) {
      console.error("❌ Invalid limit value. Must be a positive number.");
      process.exit(1);
    }
  }

  return { subjectPattern, dryRun, limit };
}

// Function to format duration
function formatDuration(ms) {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);

  if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

// Function to find emails matching the subject pattern
async function findEmailsBySubject(emailRepository, subjectPattern, limit) {
  console.log(`🔍 Searching for emails with subject LIKE '${subjectPattern}'...`);

  const emails = await emailRepository
    .createQueryBuilder("email")
    .where("email.subject LIKE :pattern", { pattern: subjectPattern })
    .orderBy("email.id", "DESC")
    .limit(limit)
    .getMany();

  console.log(`📧 Found ${emails.length} emails matching the pattern`);

  if (emails.length > 0) {
    console.log("\n📋 Email Details:");
    emails.forEach((email, index) => {
      console.log(
        `   ${index + 1}. ID: ${email.id} | Subject: ${email.subject.substring(0, 80)}${email.subject.length > 80 ? "..." : ""}`
      );
      console.log(`      Created: ${email.createDate.toLocaleString()} | Status: ${email.status}`);
    });
  }

  return emails;
}

// Function to get deletion summary for multiple emails
async function getMultipleEmailDeletionSummary(emailDeletionService, emailIds) {
  console.log("\n📊 Getting deletion summary for all emails...");

  const summaries = [];
  let totalEntities = {
    emailUpdates: 0,
    shipments: 0,
    commercialInvoices: 0,
    documents: 0,
    files: 0,
    fileBatches: 0,
    emailThreads: 0
  };

  for (const emailId of emailIds) {
    try {
      const summary = await emailDeletionService.getEmailDeletionSummary(emailId);
      summaries.push({ emailId, summary });

      // Add to totals
      if (summary.emailExists) {
        totalEntities.emailUpdates += summary.emailUpdates;
        totalEntities.shipments += summary.shipments;
        totalEntities.commercialInvoices += summary.commercialInvoices;
        totalEntities.documents += summary.documents;
        totalEntities.files += summary.files;
        totalEntities.fileBatches += summary.fileBatches;
        totalEntities.emailThreads += summary.emailThreads;
      }
    } catch (error) {
      console.log(`   ⚠️  Error getting summary for email ${emailId}: ${error.message}`);
      summaries.push({ emailId, error: error.message });
    }
  }

  console.log("\n📊 Total Entities to be Deleted:");
  console.log(`   📧 Email Updates: ${totalEntities.emailUpdates}`);
  console.log(`   🚢 Shipments: ${totalEntities.shipments}`);
  console.log(`   📄 Commercial Invoices: ${totalEntities.commercialInvoices}`);
  console.log(`   📋 Documents: ${totalEntities.documents}`);
  console.log(`   📁 Files: ${totalEntities.files}`);
  console.log(`   📦 File Batches: ${totalEntities.fileBatches}`);
  console.log(`   🔗 Email Threads: ${totalEntities.emailThreads}`);

  return { summaries, totalEntities };
}

// Function to delete emails
async function deleteEmails(emailDeletionService, emailIds, dryRun) {
  if (dryRun) {
    console.log("\n🔍 DRY RUN MODE - No emails will be deleted");
    console.log("💡 Remove --dry-run flag to actually delete the emails");
    return { deleted: 0, failed: 0 };
  }

  console.log(`\n🗑️  Starting deletion of ${emailIds.length} emails...`);
  console.log("⚠️  This operation cannot be undone!");

  let deleted = 0;
  let failed = 0;
  const startTime = Date.now();

  for (let i = 0; i < emailIds.length; i++) {
    const emailId = emailIds[i];
    const progress = `[${i + 1}/${emailIds.length}]`;

    try {
      console.log(`${progress} 🗑️  Deleting email ${emailId}...`);
      await emailDeletionService.deleteEmailCompletely(emailId);
      deleted++;
      console.log(`${progress} ✅ Successfully deleted email ${emailId}`);
    } catch (error) {
      failed++;
      console.log(`${progress} ❌ Failed to delete email ${emailId}: ${error.message}`);
    }
  }

  const totalTime = Date.now() - startTime;

  console.log(`\n📊 Deletion Summary:`);
  console.log(`   ✅ Successfully deleted: ${deleted} emails`);
  console.log(`   ❌ Failed to delete: ${failed} emails`);
  console.log(`   ⏱️  Total time: ${formatDuration(totalTime)}`);

  return { deleted, failed };
}

async function bootstrap() {
  const { subjectPattern, dryRun, limit } = parseArgs();

  console.log("🧹 Email Cleanup by Subject Pattern");
  console.log("===================================\n");
  console.log(`🎯 Subject Pattern: ${subjectPattern}`);
  console.log(`📊 Limit: ${limit} emails`);
  console.log(`🔍 Mode: ${dryRun ? "DRY RUN (preview only)" : "LIVE DELETION"}`);

  if (!dryRun) {
    console.log("\n⚠️  WARNING: This will permanently delete emails and ALL related data!");
    console.log("⚠️  This operation cannot be undone!");
  }

  let app; // Declare app variable outside try block for proper cleanup

  try {
    // Create NestJS application context
    console.log("\n🔧 Initializing NestJS application context...");
    const contextId = ContextIdFactory.create();
    app = await NestFactory.createApplicationContext(AppModule);
    globalApp = app; // Set global reference for cleanup handlers

    // We don't need organization context for email deletion by subject
    // The EmailDeletionService will handle organization scoping internally

    // Get services and repositories
    const emailDeletionService = await app.resolve(EmailDeletionService, contextId);
    const emailRepository = await app.get(getRepositoryToken(Email));

    console.log("✅ NestJS context initialized\n");

    // Find emails matching the pattern
    const emails = await findEmailsBySubject(emailRepository, subjectPattern, limit);

    if (emails.length === 0) {
      console.log("✅ No emails found matching the pattern. Nothing to delete.");
      console.log("\n🔄 Closing application gracefully...");
      await app.close();
      console.log("✅ Application closed successfully");
      process.exit(0);
    }

    // Get deletion summary
    const emailIds = emails.map((email) => email.id);
    const { summaries, totalEntities } = await getMultipleEmailDeletionSummary(
      emailDeletionService,
      emailIds
    );

    // Confirm deletion if not dry run
    if (!dryRun) {
      console.log("\n❓ Are you sure you want to delete these emails and all related data?");
      console.log("💡 Press Ctrl+C to cancel, or wait 10 seconds to proceed...");

      // Wait 10 seconds for user to cancel
      await new Promise((resolve) => setTimeout(resolve, 10000));
      console.log("⏳ Proceeding with deletion...");
    }

    // Delete emails
    const result = await deleteEmails(emailDeletionService, emailIds, dryRun);

    console.log("\n🎉 Email cleanup completed!");

    if (!dryRun && result.deleted > 0) {
      console.log("\n💡 Tips:");
      console.log("- Check portal-api logs for detailed deletion information");
      console.log("- All related entities (shipments, documents, etc.) have been removed");
      console.log("- This operation cannot be undone");
      console.log("- You can now re-upload files for this shipment to test again");
    }

    console.log("\n🔄 Closing application gracefully...");
    await app.close();
    console.log("✅ Application closed successfully");
    process.exit(0);
  } catch (error) {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);

    // Try to close the app gracefully even on error
    try {
      if (app) {
        console.log("\n🔄 Attempting graceful shutdown after error...");
        await app.close();
        console.log("✅ Application closed after error");
      }
    } catch (closeError) {
      console.error("❌ Failed to close application gracefully:", closeError.message);
    }

    process.exit(1);
  }
}

// Global app reference for cleanup
let globalApp = null;

// Handle process termination
process.on("SIGINT", async () => {
  console.log("\n\n👋 Operation cancelled by user.");
  if (globalApp) {
    try {
      console.log("🔄 Closing application gracefully...");
      await globalApp.close();
      console.log("✅ Application closed successfully");
    } catch (error) {
      console.error("❌ Failed to close application:", error.message);
    }
  }
  process.exit(0);
});

// Run the script
bootstrap().catch(async (error) => {
  console.error(`\n💥 Unexpected error: ${error.message}`);
  if (globalApp) {
    try {
      console.log("🔄 Closing application after unexpected error...");
      await globalApp.close();
      console.log("✅ Application closed successfully");
    } catch (closeError) {
      console.error("❌ Failed to close application:", closeError.message);
    }
  }
  process.exit(1);
});
