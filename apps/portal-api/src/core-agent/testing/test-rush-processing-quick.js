#!/usr/bin/env node

/**
 * Quick REQUEST_RUSH_PROCESSING Test Script
 *
 * This script provides a quick validation of the REQUEST_RUSH_PROCESSING handler
 * focusing on null cases, transaction-runner injection, and compliance error paths.
 *
 * Usage:
 *   node src/core-agent/testing/test-rush-processing-quick.js [--verbose]
 */

const { AppModule } = require("../../../dist/app.module");
const { ContextIdFactory, NestFactory } = require("@nestjs/core");
const { DataSource } = require("typeorm");
const { generateRequest } = require("../../../dist/email/utils/generate-request");

// Import services
const { ShipmentContextService } = require("../../../dist/agent-context");
const {
  IntentHandlerRegistry
} = require("../../../dist/core-agent/services/intent-handler-registry.service");

// Entity imports
const { Organization, Shipment, FIND_ORGANIZATION_RELATIONS } = require("nest-modules");

async function quickTest() {
  const verbose = process.argv.includes("--verbose");
  console.log("🚀 Quick REQUEST_RUSH_PROCESSING Test");
  console.log("=====================================\n");

  let app;

  try {
    // Initialize NestJS context
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: verbose ? ["log", "error", "warn", "debug"] : ["error"]
    });

    const dataSource = await app.get(DataSource);
    console.log("✅ NestJS context initialized");

    // Load demo organization
    const organization = await dataSource.manager.findOne(Organization, {
      where: { id: 3 },
      relations: FIND_ORGANIZATION_RELATIONS
    });

    if (!organization) {
      throw new Error("Demo organization (ID: 3) not found");
    }
    console.log(`✅ Loaded organization: ${organization.name}`);

    // Create request context
    const contextId = ContextIdFactory.create();
    const requestContext = generateRequest(null, organization);
    app.registerRequestByContextId(requestContext, contextId);

    // Get services
    const shipmentContextService = await app.resolve(ShipmentContextService, contextId);
    const intentHandlerRegistry = await app.resolve(IntentHandlerRegistry, contextId);

    // Get handler
    const handler = intentHandlerRegistry.getHandler("REQUEST_RUSH_PROCESSING");
    if (!handler) {
      throw new Error("REQUEST_RUSH_PROCESSING handler not found");
    }
    console.log("✅ Handler resolved successfully");

    // Test cases
    const testCases = [
      {
        name: "Null Shipment Test",
        context: {
          shipment: null,
          organization,
          compliance: { isCompliant: true },
          canRush: false,
          isSubmitted: false,
          _services: {
            emailService: {
              sendEmail: async () => ({ id: "test" }),
              sendBackofficeAlert: async () => ({ id: "test-alert" })
            },
            entrySubmissionService: { attemptShipmentSubmission: async () => ({}) }
          }
        },
        expectedBehavior: "Should handle null shipment gracefully"
      },
      {
        name: "Missing Entry Service Test",
        context: {
          shipment: { id: 999, customsStatus: "DOCUMENTS_RECEIVED" },
          organization,
          compliance: { isCompliant: true },
          canRush: true,
          isSubmitted: false,
          _services: {
            emailService: {
              sendEmail: async () => ({ id: "test" }),
              sendBackofficeAlert: async () => ({ id: "test-alert" })
            },
            entrySubmissionService: null
          }
        },
        expectedBehavior: "Should handle missing entry service"
      },
      {
        name: "Compliance Error Test",
        context: {
          shipment: { id: 999, customsStatus: "DOCUMENTS_RECEIVED" },
          organization,
          compliance: {
            isCompliant: false,
            errors: ["Missing invoice"],
            warnings: [],
            missingFields: ["invoiceNumber"]
          },
          canRush: true,
          isSubmitted: false,
          _services: {
            emailService: {
              sendEmail: async () => ({ id: "test" }),
              sendBackofficeAlert: async () => ({ id: "test-alert" })
            },
            entrySubmissionService: { attemptShipmentSubmission: async () => ({}) }
          }
        },
        expectedBehavior: "Should handle compliance errors"
      },
      {
        name: "Transaction Runner Test",
        context: {
          shipment: { id: 999, customsStatus: "DOCUMENTS_RECEIVED" },
          organization,
          compliance: { isCompliant: true },
          canRush: true,
          isSubmitted: false,
          queryRunner: { isTransactionActive: true },
          _services: {
            emailService: {
              sendEmail: async () => ({ id: "test" }),
              sendBackofficeAlert: async () => ({ id: "test-alert" })
            },
            entrySubmissionService: { attemptShipmentSubmission: async () => ({}) }
          }
        },
        expectedBehavior: "Should handle transaction runner injection"
      }
    ];

    // Run tests
    let passCount = 0;
    for (const testCase of testCases) {
      console.log(`\n🧪 ${testCase.name}`);
      console.log(`📝 ${testCase.expectedBehavior}`);

      try {
        const validatedIntent = {
          intent: "REQUEST_RUSH_PROCESSING",
          instructions: ["Test rush processing request"],
          attachments: []
        };

        const startTime = Date.now();
        const fragments = await handler.handle(validatedIntent, testCase.context);
        const processingTime = Date.now() - startTime;

        console.log(`✅ Test passed in ${processingTime}ms`);
        console.log(`📊 Generated ${fragments.length} fragments`);

        if (verbose) {
          console.log(`📋 Fragment templates: ${fragments.map((f) => f.template).join(", ")}`);
          if (fragments[0].fragmentContext) {
            const keys = Object.keys(fragments[0].fragmentContext);
            console.log(`🔑 Context keys: ${keys.join(", ")}`);
          }
        }

        passCount++;
      } catch (error) {
        console.log(`❌ Test failed: ${error.message}`);
        if (verbose) {
          console.log(`💥 Stack: ${error.stack}`);
        }
      }
    }

    console.log(`\n📊 Test Summary: ${passCount}/${testCases.length} tests passed`);

    if (passCount === testCases.length) {
      console.log("🎉 All quick tests passed!");
    } else {
      console.log("⚠️  Some tests failed - see details above");
    }
  } catch (error) {
    console.error(`💥 Test failed: ${error.message}`);
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

// Handle empty instructions case
async function testEmptyInstructions() {
  console.log("\n🧪 Testing empty instructions case...");

  let app;
  try {
    app = await NestFactory.createApplicationContext(AppModule, { logger: ["error"] });
    const dataSource = await app.get(DataSource);

    const organization = await dataSource.manager.findOne(Organization, {
      where: { id: 3 },
      relations: FIND_ORGANIZATION_RELATIONS
    });

    const contextId = ContextIdFactory.create();
    const requestContext = generateRequest(null, organization);
    app.registerRequestByContextId(requestContext, contextId);

    const intentHandlerRegistry = await app.resolve(IntentHandlerRegistry, contextId);
    const handler = intentHandlerRegistry.getHandler("REQUEST_RUSH_PROCESSING");

    const validatedIntent = {
      intent: "REQUEST_RUSH_PROCESSING",
      instructions: [], // Empty instructions
      attachments: []
    };

    const context = {
      shipment: { id: 999 },
      organization,
      compliance: { isCompliant: true },
      canRush: true,
      isSubmitted: false,
      _services: {
        emailService: {
          sendEmail: async () => ({ id: "test" }),
          sendBackofficeAlert: async () => ({ id: "test-alert" })
        },
        entrySubmissionService: { attemptShipmentSubmission: async () => ({}) }
      }
    };

    try {
      await handler.handle(validatedIntent, context);
      console.log("❌ Expected error for empty instructions but test passed");
    } catch (error) {
      if (error.message.includes("No instructions provided")) {
        console.log("✅ Empty instructions error properly thrown");
      } else {
        console.log(`❌ Unexpected error: ${error.message}`);
      }
    }
  } catch (error) {
    console.log(`❌ Setup error: ${error.message}`);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

// Run tests
async function main() {
  await quickTest();
  await testEmptyInstructions();
}

main().catch(console.error);
