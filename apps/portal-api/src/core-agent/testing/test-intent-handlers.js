#!/usr/bin/env node

/**
 * Intent Handlers Test Script
 *
 * This script demonstrates and tests all 9 email intent handlers in the refactored core-agent system.
 * It uses real shipment data from the database and generates categorized single intents for each handler.
 *
 * Usage:
 *   node scripts/test-intent-handlers.js [options]
 *
 * Options:
 *   --org=ID                   Organization ID (default: 3 - demo org)
 *   --shipment=ID              Specific shipment ID to test with
 *   --intent=INTENT_NAME       Test only specific intent (e.g., GET_SHIPMENT_STATUS)
 *   --no-side-effects          Skip side effects like backoffice emails and document generation
 *   --verbose                  Show detailed logging
 *
 * Examples:
 *   # Test all intents with demo org
 *   node scripts/test-intent-handlers.js
 *
 *   # Test specific intent
 *   node scripts/test-intent-handlers.js --intent=REQUEST_CAD_DOCUMENT
 *
 *   # Test with specific shipment
 *   node scripts/test-intent-handlers.js --shipment=123 --org=1
 *
 *   # Test without side effects (no emails sent)
 *   node scripts/test-intent-handlers.js --no-side-effects
 */

const { AppModule } = require("../../../dist/app.module");
const { ContextIdFactory, NestFactory } = require("@nestjs/core");
const { DataSource } = require("typeorm");
const { generateRequest } = require("../../../dist/email/utils/generate-request");

// Import services and types
const { ShipmentContextService } = require("../../../dist/agent-context");
const {
  IntentHandlerRegistry
} = require("../../../dist/core-agent/services/intent-handler-registry.service");
const { ShipmentResponseService } = require("../../../dist/core-agent/services/shipment-response.service");

// Entity imports
const { Organization, Shipment, FIND_ORGANIZATION_RELATIONS } = require("nest-modules");

// Default configuration
const DEFAULTS = {
  organizationId: "3", // Demo organization
  verbose: false,
  sideEffects: true
};

// All 9 intent types with test scenarios
const INTENT_TEST_SCENARIOS = {
  GET_SHIPMENT_STATUS: {
    intent: "GET_SHIPMENT_STATUS",
    description: "General inquiry on entry status",
    instructions: [
      "What is the status of my shipment?",
      "Has the shipment been released?",
      "How long will it take to clear customs?"
    ],
    requiresShipment: true
  },
  PROCESS_DOCUMENT: {
    intent: "PROCESS_DOCUMENT",
    description: "Process attached documents",
    instructions: ["Please process the attached documents", "Submit the entry with these documents"],
    requiresShipment: true
  },
  REQUEST_RUSH_PROCESSING: {
    intent: "REQUEST_RUSH_PROCESSING",
    description: "Rush shipment processing",
    instructions: [
      "Can you please clear the shipment quickly?",
      "The driver is at the border, please expedite"
    ],
    requiresShipment: false
  },
  DOCUMENTATION_COMING: {
    intent: "DOCUMENTATION_COMING",
    description: "Will provide documentation later",
    instructions: [
      "Shipment has moved in-bond, will be sending the manifest shortly",
      "Documents are coming soon"
    ],
    requiresShipment: false
  },
  UPDATE_SHIPMENT: {
    intent: "UPDATE_SHIPMENT",
    description: "Update shipment information",
    instructions: ["Update port code to 0123", "Change sublocation to 4567", "Update CCN to ABC123456789"],
    requiresShipment: true
  },
  REQUEST_CAD_DOCUMENT: {
    intent: "REQUEST_CAD_DOCUMENT",
    description: "Request CAD document",
    instructions: ["Can you send me the CAD document?", "Please provide the customs accounting document"],
    requiresShipment: true
  },
  REQUEST_RNS_PROOF: {
    intent: "REQUEST_RNS_PROOF",
    description: "Request RNS proof of release",
    instructions: ["Can you send me the RNS proof of release?", "I need the release notification document"],
    requiresShipment: true
  },
  REQUEST_MANUAL_PROCESSING: {
    intent: "REQUEST_MANUAL_PROCESSING",
    description: "Request manual processing",
    instructions: ["Please cancel entry", "Can someone manually review this shipment?"],
    requiresShipment: false
  },
  REQUEST_HOLD_SHIPMENT: {
    intent: "REQUEST_HOLD_SHIPMENT",
    description: "Hold shipment processing",
    instructions: ["Please hold processing until approval", "Do not submit entry until CAD approval"],
    requiresShipment: false
  }
};

// Function to parse command line arguments
function parseCommandLineArguments(args) {
  const result = {
    organizationId: DEFAULTS.organizationId,
    shipmentId: null,
    intentFilter: null,
    sideEffects: DEFAULTS.sideEffects,
    verbose: DEFAULTS.verbose
  };

  const remainingArgs = args.slice(2);

  for (let i = 0; i < remainingArgs.length; i++) {
    const arg = remainingArgs[i];

    if (arg.startsWith("--org=")) {
      result.organizationId = arg.split("=")[1];
    } else if (arg.startsWith("--shipment=")) {
      result.shipmentId = parseInt(arg.split("=")[1]);
    } else if (arg.startsWith("--intent=")) {
      result.intentFilter = arg.split("=")[1];
    } else if (arg === "--no-side-effects") {
      result.sideEffects = false;
    } else if (arg === "--verbose") {
      result.verbose = true;
    }
  }

  return result;
}

// Function to find suitable shipments for testing
async function findTestShipments(dataSource, organizationId, limit = 5) {
  console.log(`🔍 Finding suitable shipments for organization ${organizationId}...`);

  const shipments = await dataSource.query(
    `
    SELECT s.id, s."customsStatus", s."hblNumber", s."modeOfTransport",
           s."createDate", s."portCode", s."subLocation"
    FROM shipment s
    WHERE s."organizationId" = $1
      AND s."customsStatus" IS NOT NULL
    ORDER BY s."createDate" DESC
    LIMIT $2
  `,
    [organizationId, limit]
  );

  console.log(`✅ Found ${shipments.length} shipments:`);
  shipments.forEach((shipment, index) => {
    console.log(`   ${index + 1}. ID: ${shipment.id}, HBL: ${shipment.hblNumber || "N/A"}`);
    console.log(`      Status: ${shipment.customsStatus}, Mode: ${shipment.modeOfTransport}`);
    console.log(`      Port: ${shipment.portCode || "N/A"}, SubLocation: ${shipment.subLocation || "N/A"}`);
    console.log(`      Created: ${shipment.createDate}`);
    console.log("");
  });

  return shipments;
}

// Function to test a single intent handler
async function testIntentHandler(
  intentName,
  scenario,
  context,
  intentHandlerRegistry,
  shipmentResponseService,
  config
) {
  console.log(`\n🧪 Testing Intent: ${intentName}`);
  console.log(`📝 Description: ${scenario.description}`);
  console.log(`📋 Instructions: ${scenario.instructions.slice(0, 2).join(", ")}...`);

  try {
    // Create validated intent object
    const validatedIntent = {
      intent: intentName,
      instructions: scenario.instructions,
      attachments: []
    };

    // Get the handler
    const handler = intentHandlerRegistry.getHandler(intentName);
    if (!handler) {
      console.log(`❌ No handler found for intent: ${intentName}`);
      return { success: false, error: "Handler not found" };
    }

    // Handle the intent
    console.log(`⚙️  Processing intent with handler...`);
    const startTime = Date.now();

    // Create a copy of context to avoid side effect pollution between tests
    // Note: We can't use JSON.parse/stringify due to circular references in TypeORM entities
    const testContext = {
      ...context,
      sideEffects: { backofficeAlerts: {} }, // Reset side effects for each test
      emailId: `test-email-${Date.now()}`
    };

    const fragments = await handler.handle(validatedIntent, testContext);
    const processingTime = Date.now() - startTime;

    console.log(`✅ Handler completed in ${processingTime}ms`);
    console.log(`📊 Generated ${fragments.length} response fragments`);

    // Show fragment details
    if (config.verbose && fragments.length > 0) {
      console.log(`📋 Fragment Details:`);
      fragments.forEach((fragment, index) => {
        console.log(`   ${index + 1}. Template: ${fragment.template}, Priority: ${fragment.priority}`);
        if (fragment.fragmentContext) {
          const contextKeys = Object.keys(fragment.fragmentContext);
          console.log(`      Context: ${contextKeys.join(", ")}`);
        }
      });
    }

    // Render fragments to HTML
    console.log(`🎨 Rendering fragments to HTML...`);
    const responseHtml = await shipmentResponseService.renderFragments(fragments, testContext);

    // Show side effects
    if (testContext.sideEffects && Object.keys(testContext.sideEffects).length > 0) {
      console.log(`🔧 Side Effects Generated:`);
      Object.keys(testContext.sideEffects).forEach((key) => {
        console.log(`   - ${key}: ${typeof testContext.sideEffects[key]}`);
      });
    }

    // Show rendered response (truncated)
    if (responseHtml && responseHtml.trim().length > 0) {
      const truncatedHtml = responseHtml.replace(/<[^>]*>/g, "").substring(0, 200);
      console.log(`📧 Response Preview: ${truncatedHtml}...`);
    } else {
      console.log(`⚠️  No response HTML generated`);
    }

    return {
      success: true,
      fragments: fragments.length,
      processingTime,
      hasResponse: responseHtml && responseHtml.trim().length > 0,
      sideEffects: Object.keys(testContext.sideEffects || {})
    };
  } catch (error) {
    console.log(`❌ Error testing intent ${intentName}: ${error.message}`);
    if (config.verbose) {
      console.log(`💥 Stack trace: ${error.stack}`);
    }
    return { success: false, error: error.message };
  }
}

// Function to run all tests
async function runAllTests(config, app, dataSource) {
  console.log("🚀 Starting Intent Handler Tests");
  console.log("================================\n");

  console.log("📝 Test Configuration:");
  console.log(`🏢 Organization ID: ${config.organizationId}`);
  console.log(`🚢 Shipment ID: ${config.shipmentId || "Auto-select"}`);
  console.log(`🎯 Intent Filter: ${config.intentFilter || "All intents"}`);
  console.log(`🔧 Side Effects: ${config.sideEffects ? "Enabled" : "Disabled"}`);
  console.log(`📊 Verbose: ${config.verbose ? "Yes" : "No"}`);
  console.log("");

  try {
    // 1. Load organization
    const organization = await dataSource.manager.findOne(Organization, {
      where: { id: parseInt(config.organizationId) },
      relations: FIND_ORGANIZATION_RELATIONS
    });
    if (!organization) {
      throw new Error(`Organization not found: ${config.organizationId}`);
    }
    console.log(`✅ Loaded organization: ${organization.name}`);

    // 2. Create request context
    const contextId = ContextIdFactory.create();
    const requestContext = generateRequest(null, organization);
    app.registerRequestByContextId(requestContext, contextId);

    // 3. Get services
    const shipmentContextService = await app.resolve(ShipmentContextService, contextId);
    const intentHandlerRegistry = await app.resolve(IntentHandlerRegistry, contextId);
    const shipmentResponseService = await app.resolve(ShipmentResponseService, contextId);

    console.log(`✅ Resolved core-agent services`);

    // 4. Find or use specified shipment
    let shipment;
    if (config.shipmentId) {
      shipment = await dataSource.manager.findOne(Shipment, {
        where: { id: config.shipmentId, organizationId: parseInt(config.organizationId) }
      });
      if (!shipment) {
        throw new Error(`Shipment not found: ${config.shipmentId}`);
      }
      console.log(`✅ Using specified shipment: ${shipment.id}`);
    } else {
      const shipments = await findTestShipments(dataSource, parseInt(config.organizationId), 1);
      if (shipments.length === 0) {
        throw new Error(`No shipments found for organization ${config.organizationId}`);
      }
      shipment = shipments[0];
      console.log(`✅ Auto-selected shipment: ${shipment.id}`);
    }

    // 5. Build shipment context
    console.log(`🔧 Building shipment context...`);
    const context = await shipmentContextService.buildContext(shipment.id, parseInt(config.organizationId));
    console.log(`✅ Built context for shipment ${shipment.id}`);

    // 6. Run tests
    const testResults = {};
    const intentsToTest = config.intentFilter ? [config.intentFilter] : Object.keys(INTENT_TEST_SCENARIOS);

    console.log(`\n🧪 Running tests for ${intentsToTest.length} intent(s)...\n`);

    for (const intentName of intentsToTest) {
      const scenario = INTENT_TEST_SCENARIOS[intentName];
      if (!scenario) {
        console.log(`⚠️  Unknown intent: ${intentName}`);
        continue;
      }

      // Skip intents that require shipment if we don't have one
      if (scenario.requiresShipment && !shipment) {
        console.log(`⏭️  Skipping ${intentName} (requires shipment)`);
        continue;
      }

      const result = await testIntentHandler(
        intentName,
        scenario,
        context,
        intentHandlerRegistry,
        shipmentResponseService,
        config
      );

      testResults[intentName] = result;
    }

    // 7. Show summary
    console.log(`\n📊 Test Results Summary`);
    console.log("======================");

    let successCount = 0;
    let totalFragments = 0;
    let totalProcessingTime = 0;

    Object.entries(testResults).forEach(([intentName, result]) => {
      const status = result.success ? "✅ PASS" : "❌ FAIL";
      console.log(`${status} ${intentName}`);

      if (result.success) {
        successCount++;
        totalFragments += result.fragments || 0;
        totalProcessingTime += result.processingTime || 0;

        if (config.verbose) {
          console.log(`     Fragments: ${result.fragments}, Time: ${result.processingTime}ms`);
          console.log(`     Response: ${result.hasResponse ? "Yes" : "No"}`);
          console.log(
            `     Side Effects: ${result.sideEffects.length > 0 ? result.sideEffects.join(", ") : "None"}`
          );
        }
      } else {
        console.log(`     Error: ${result.error}`);
      }
    });

    console.log(`\n📈 Overall Results:`);
    console.log(`   Tests Passed: ${successCount}/${Object.keys(testResults).length}`);
    console.log(`   Total Fragments: ${totalFragments}`);
    console.log(`   Total Processing Time: ${totalProcessingTime}ms`);
    console.log(`   Average Time per Intent: ${Math.round(totalProcessingTime / successCount)}ms`);

    return testResults;
  } catch (error) {
    console.error(`💥 Test execution failed: ${error.message}`);
    if (config.verbose) {
      console.error(`Stack trace: ${error.stack}`);
    }
    throw error;
  }
}

// Main bootstrap function
async function bootstrap() {
  const config = parseCommandLineArguments(process.argv);
  let app;

  try {
    // Create NestJS application context
    console.log("🔧 Initializing NestJS application context...");
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: config.verbose ? ["log", "error", "warn", "debug"] : ["error"]
    });

    const dataSource = await app.get(DataSource);
    console.log("✅ NestJS context initialized\n");

    // Run all tests
    await runAllTests(config, app, dataSource);

    console.log("\n🎉 Intent handler testing completed successfully!");
  } catch (error) {
    console.error(`💥 Bootstrap failed: ${error.message}`);
    process.exit(1);
  } finally {
    // Ensure app is always closed
    if (app) {
      try {
        await app.close();
      } catch (closeError) {
        console.error("Error closing app:", closeError.message);
      }
    }
    process.exit(0);
  }
}

// Handle process termination gracefully
process.on("SIGINT", () => {
  console.log("\nReceived SIGINT, shutting down gracefully...");
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("\nReceived SIGTERM, shutting down gracefully...");
  process.exit(0);
});

// Show usage if no arguments provided
if (process.argv.length === 2) {
  console.log("Intent Handlers Test Script Usage:");
  console.log("");
  console.log("Commands:");
  console.log("  --org=ID                   Organization ID (default: 3)");
  console.log("  --shipment=ID              Specific shipment ID to test with");
  console.log("  --intent=INTENT_NAME       Test only specific intent");
  console.log("  --no-side-effects          Skip side effects");
  console.log("  --verbose                  Show detailed logging");
  console.log("");
  console.log("Available Intents:");
  Object.keys(INTENT_TEST_SCENARIOS).forEach((intent) => {
    console.log(`  - ${intent}`);
  });
  console.log("");
  console.log("Examples:");
  console.log("  node scripts/test-intent-handlers.js");
  console.log("  node scripts/test-intent-handlers.js --intent=REQUEST_CAD_DOCUMENT");
  console.log("  node scripts/test-intent-handlers.js --shipment=123 --verbose");
  process.exit(0);
}

bootstrap();
