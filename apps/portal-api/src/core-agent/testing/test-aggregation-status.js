#!/usr/bin/env node

/**
 * Test script to verify aggregation status lookup functionality
 *
 * This script tests:
 * 1. DocumentProcessorService.processDocumentAttachments() with aggregation status
 * 2. DocumentProcessorService.fetchProcessedDocumentsFromDatabase() with aggregation status
 * 3. SQL queries to verify database structure
 */

const { AppModule } = require("../../../dist/app.module");
const { ContextIdFactory, NestFactory } = require("@nestjs/core");
const { DataSource } = require("typeorm");
const { generateRequest } = require("../../../dist/email/utils/generate-request");

// Import services and types
const { ShipmentContextService } = require("../../../dist/agent-context");
const { DocumentProcessorService } = require("../../../dist/core-agent/services/document-processor.service");

// Entity imports
const { Organization, Shipment, FIND_ORGANIZATION_RELATIONS } = require("nest-modules");

// Default configuration
const DEFAULTS = {
  organizationId: "3", // Demo organization
  verbose: false
};

// Function to parse command line arguments
function parseCommandLineArguments(args) {
  const result = {
    organizationId: DEFAULTS.organizationId,
    verbose: DEFAULTS.verbose
  };

  const remainingArgs = args.slice(2);

  for (let i = 0; i < remainingArgs.length; i++) {
    const arg = remainingArgs[i];

    if (arg.startsWith("--org=")) {
      result.organizationId = arg.split("=")[1];
    } else if (arg === "--verbose") {
      result.verbose = true;
    }
  }

  return result;
}

// Function to run all tests
async function runAllTests(config, app, dataSource) {
  console.log("🧪 Testing Aggregation Status Lookup");
  console.log("=====================================");

  console.log("📝 Test Configuration:");
  console.log(`🏢 Organization ID: ${config.organizationId}`);
  console.log(`📊 Verbose: ${config.verbose ? "Yes" : "No"}`);
  console.log("");

  try {
    // 1. Load organization
    const organization = await dataSource.manager.findOne(Organization, {
      where: { id: parseInt(config.organizationId) },
      relations: FIND_ORGANIZATION_RELATIONS
    });
    if (!organization) {
      throw new Error(`Organization not found: ${config.organizationId}`);
    }
    console.log(`✅ Loaded organization: ${organization.name}`);

    // 2. Create request context
    const contextId = ContextIdFactory.create();
    const requestContext = generateRequest(null, organization);
    app.registerRequestByContextId(requestContext, contextId);

    // 3. Get services
    const shipmentContextService = await app.resolve(ShipmentContextService, contextId);
    const documentProcessorService = await app.resolve(DocumentProcessorService, contextId);

    console.log(`✅ Resolved DocumentProcessorService`);

    // Test 1: processDocumentAttachments with mock data
    console.log("\n📋 Test 1: processDocumentAttachments with aggregation status");
    console.log("-----------------------------------------------------------");

    const mockValidatedIntent = {
      attachments: [
        {
          id: 510, // Document ID from our earlier test
          filename: "commercial-invoice.pdf",
          documentType: "COMMERCIAL_INVOICE"
        },
        {
          id: 511, // Document ID from our earlier test
          filename: "house-bill-of-lading.pdf",
          documentType: "HOUSE_OCEAN_BILL_OF_LADING"
        }
      ]
    };

    const processedDocuments = await documentProcessorService.processDocumentAttachments(mockValidatedIntent);

    console.log(`Found ${processedDocuments.length} processed documents:`);
    processedDocuments.forEach((doc, index) => {
      console.log(`  Document ${index + 1}:`);
      console.log(`    filename: "${doc.filename}"`);
      console.log(`    contentType: "${doc.contentType}"`);
      console.log(`    status: "${doc.status}"`);
      console.log(`    aggregationStatus: "${doc.aggregationStatus || "N/A"}"`);
      console.log(`    claroUrl: "${doc.claroUrl}"`);

      if (doc.aggregationStatus) {
        console.log(`    ✅ Aggregation status found: ${doc.aggregationStatus}`);
      } else {
        console.log(`    ⚠️  No aggregation status found`);
      }
    });

    // Test 2: fetchProcessedDocumentsFromDatabase with mock context
    console.log("\n📋 Test 2: fetchProcessedDocumentsFromDatabase with aggregation status");
    console.log("--------------------------------------------------------------------");

    // Build context for shipment 887
    const context = await shipmentContextService.buildContext(887, parseInt(config.organizationId));

    const fetchedDocuments = await documentProcessorService.fetchProcessedDocumentsFromDatabase(context);

    console.log(`Found ${fetchedDocuments.length} fetched documents:`);
    fetchedDocuments.forEach((doc, index) => {
      console.log(`  Document ${index + 1}:`);
      console.log(`    filename: "${doc.filename}"`);
      console.log(`    contentType: "${doc.contentType}"`);
      console.log(`    status: "${doc.status}"`);
      console.log(`    aggregationStatus: "${doc.aggregationStatus || "N/A"}"`);
      console.log(`    claroUrl: "${doc.claroUrl}"`);

      if (doc.aggregationStatus) {
        console.log(`    ✅ Aggregation status found: ${doc.aggregationStatus}`);
      } else {
        console.log(`    ⚠️  No aggregation status found`);
      }
    });

    // Test 3: Direct SQL verification
    console.log("\n📋 Test 3: Direct SQL verification");
    console.log("----------------------------------");

    const queryResult = await dataSource.query(
      `
      SELECT 
        d.id, 
        d.name, 
        d.status, 
        d."aggregationId", 
        da.status as aggregation_status 
      FROM document d 
      LEFT JOIN document_aggregation da ON d."aggregationId" = da.id 
      WHERE d."shipmentId" = $1 
      ORDER BY d.id
    `,
      [887]
    );

    console.log(`SQL Query Results (${queryResult.length} rows):`);
    queryResult.forEach((row, index) => {
      console.log(`  Row ${index + 1}:`);
      console.log(`    id: ${row.id}`);
      console.log(`    name: ${row.name}`);
      console.log(`    status: ${row.status}`);
      console.log(`    aggregationId: ${row.aggregationId || "N/A"}`);
      console.log(`    aggregation_status: ${row.aggregation_status || "N/A"}`);

      if (row.aggregation_status) {
        console.log(`    ✅ Aggregation status: ${row.aggregation_status}`);
      } else {
        console.log(`    ⚠️  No aggregation status`);
      }
    });

    console.log("\n🎉 All tests completed successfully!");
  } catch (error) {
    console.error("❌ Test failed:", error.message);
    if (config.verbose) {
      console.error(error.stack);
    }
    throw error;
  }
}

// Main bootstrap function
async function bootstrap() {
  const config = parseCommandLineArguments(process.argv);

  let app;
  try {
    app = await NestFactory.createApplicationContext(AppModule);
    const dataSource = app.get(DataSource);

    await runAllTests(config, app, dataSource);

    console.log("\n✅ Aggregation Status Test completed successfully!");
  } catch (error) {
    console.error("\n💥 Test failed:", error.message);
    process.exit(1);
  } finally {
    if (app) {
      await app.close();
    }
  }
}

// Show usage if no arguments provided or help requested
if (process.argv.includes("--help") || process.argv.includes("-h")) {
  console.log("Aggregation Status Test Script Usage:");
  console.log("====================================");
  console.log("");
  console.log("This script tests the aggregation status functionality");
  console.log("in the DocumentProcessorService.");
  console.log("");
  console.log("Commands:");
  console.log("  --org=ID        Organization ID (default: 3)");
  console.log("  --verbose       Show detailed logging");
  console.log("");
  console.log("Examples:");
  console.log("  # Test with demo organization");
  console.log("  node src/core-agent/testing/test-aggregation-status.js");
  console.log("");
  console.log("  # Test with verbose output");
  console.log("  node src/core-agent/testing/test-aggregation-status.js --verbose");
  console.log("");
  process.exit(0);
}

// Run the test
bootstrap().catch(console.error);
