#!/bin/bash

# E2E Email Pipeline Test with Automatic Logging
# This script runs the e2e email pipeline test and saves output to /tmp for dev analysis

set -e

# Generate timestamp for log file
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="/tmp/e2e-email-test-${TIMESTAMP}.log"

echo "🚀 Running E2E Email Pipeline Test with Logging"
echo "📝 Log file: ${LOG_FILE}"
echo "======================================================"

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Change to portal-api directory (go up from core-agent/testing to portal-api)
cd "${SCRIPT_DIR}/../../../"

# Run the e2e test and save output to log file
node src/core-agent/testing/e2e-email-pipeline-nestjs.js "$@" 2>&1 | tee "${LOG_FILE}"

echo ""
echo "✅ Test completed! Log saved to: ${LOG_FILE}"
echo "💡 You can review the log file for detailed analysis"
echo "🗑️  Log will be automatically cleaned up on system restart"
