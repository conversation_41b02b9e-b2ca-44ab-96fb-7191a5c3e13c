#!/usr/bin/env node

/**
 * Comprehensive Email Cleanup Script
 *
 * This script provides a clean slate by removing:
 * 1. All test emails (by subject patterns)
 * 2. Stuck emails (non-responded status and processing states)
 * 3. Any emails in problematic states
 *
 * Usage:
 *   node scripts/comprehensive-email-cleanup.js [--dry-run] [--limit=500]
 *
 * Examples:
 *   # Build first using Rush
 *   rushx --to portal-api build
 *   cd apps/portal-api
 *
 *   # Preview what would be deleted (dry run)
 *   node scripts/comprehensive-email-cleanup.js --dry-run
 *
 *   # Actually delete the emails
 *   node scripts/comprehensive-email-cleanup.js
 *
 *   # Delete with custom limit
 *   node scripts/comprehensive-email-cleanup.js --limit=1000
 *
 * ⚠️  WARNING: This is a destructive operation that cannot be undone!
 */

const { AppModule } = require("../dist/app.module");
const { ContextIdFactory, NestFactory } = require("@nestjs/core");
const { EmailDeletionService } = require("../dist/email/services/email-deletion.service");
const { Email, EmailStatus } = require("nest-modules");
const { getRepositoryToken } = require("@nestjs/typeorm");

// Global app reference for cleanup handlers
let globalApp = null;

// Graceful shutdown handlers
process.on("SIGINT", async () => {
  console.log("\n🛑 Received SIGINT, shutting down gracefully...");
  if (globalApp) {
    await globalApp.close();
  }
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("\n🛑 Received SIGTERM, shutting down gracefully...");
  if (globalApp) {
    await globalApp.close();
  }
  process.exit(0);
});

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  let dryRun = false;
  let limit = 500; // Default limit

  for (const arg of args) {
    if (arg === "--dry-run") {
      dryRun = true;
    } else if (arg.startsWith("--limit=")) {
      limit = parseInt(arg.split("=")[1], 10);
      if (isNaN(limit) || limit <= 0) {
        console.error("❌ Invalid limit value. Must be a positive number.");
        process.exit(1);
      }
    } else if (arg === "--help" || arg === "-h") {
      console.log("Usage: node scripts/comprehensive-email-cleanup.js [--dry-run] [--limit=500]");
      console.log("");
      console.log("Options:");
      console.log("  --dry-run    Preview what would be deleted without actually deleting");
      console.log("  --limit=N    Maximum number of emails to process (default: 500)");
      console.log("");
      console.log("⚠️  WARNING: This is a destructive operation that cannot be undone!");
      process.exit(0);
    } else {
      console.error(`❌ Unknown argument: ${arg}`);
      console.log("Use --help for usage information");
      process.exit(1);
    }
  }

  return { dryRun, limit };
}

// Function to find problematic emails
async function findProblematicEmails(emailRepository, limit) {
  console.log("🔍 Searching for problematic emails...");

  // Test email patterns (common test subjects)
  const testPatterns = [
    "%test%",
    "%Test%",
    "%TEST%",
    "%E2E%",
    "%e2e%",
    "%demo%",
    "%Demo%",
    "%DEMO%",
    "%8FPDNBBH%",
    "%nestjs-e2e-test%",
    "%<EMAIL>%"
  ];

  // Stuck/problematic statuses
  const problematicStatuses = [
    EmailStatus.AWAIT_SHIPMENT_SEARCH,
    EmailStatus.ANALYZING_INTENTS,
    EmailStatus.PROCESSING_INTENTS,
    EmailStatus.AGGREGATING_EMAIL,
    EmailStatus.RESPONDING,
    EmailStatus.FAILED_SHIPMENT_SEARCH,
    EmailStatus.FAILED_INTENT_ANALYSIS,
    EmailStatus.FAILED_INTENT_PROCESSING,
    EmailStatus.FAILED_AGGREGATION,
    EmailStatus.FAILED_RESPONSE_GENERATION
  ];

  const queryBuilder = emailRepository
    .createQueryBuilder("email")
    .leftJoinAndSelect("email.updates", "updates")
    .leftJoinAndSelect("email.organization", "organization");

  // Build WHERE clause for test patterns
  const testConditions = testPatterns.map((_, index) => `email.subject ILIKE :pattern${index}`).join(" OR ");
  const testParams = {};
  testPatterns.forEach((pattern, index) => {
    testParams[`pattern${index}`] = pattern;
  });

  // Add conditions for stuck emails (older than 30 minutes)
  const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

  queryBuilder
    .where(`(${testConditions})`, testParams)
    .orWhere("email.status IN (:...problematicStatuses)", { problematicStatuses })
    .orWhere("(email.status NOT IN (:...completedStatuses) AND email.lastEditDate < :cutoff)", {
      completedStatuses: [EmailStatus.RESPONDED, EmailStatus.DELETED],
      cutoff: thirtyMinutesAgo
    })
    .orderBy("email.createDate", "ASC")
    .limit(limit);

  const emails = await queryBuilder.getMany();

  console.log(`📧 Found ${emails.length} problematic emails to clean up`);

  if (emails.length > 0) {
    console.log("\n📋 Email breakdown:");

    // Group by status for summary
    const statusCounts = {};
    const testEmailCount = emails.filter((email) =>
      testPatterns.some((pattern) =>
        email.subject?.toLowerCase().includes(pattern.replace(/%/g, "").toLowerCase())
      )
    ).length;

    emails.forEach((email) => {
      statusCounts[email.status] = (statusCounts[email.status] || 0) + 1;
    });

    console.log(`   🧪 Test emails: ${testEmailCount}`);
    console.log("   📊 By status:");
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`      ${status}: ${count}`);
    });
  }

  return emails;
}

// Function to get deletion summary for multiple emails
async function getMultipleEmailDeletionSummary(emailDeletionService, emailIds) {
  console.log(`\n📊 Getting deletion summary for ${emailIds.length} emails...`);

  const summaries = [];
  const totalEntities = {
    emailUpdates: 0,
    shipments: 0,
    commercialInvoices: 0,
    documents: 0,
    files: 0,
    fileBatches: 0,
    emailThreads: 0
  };

  for (const emailId of emailIds) {
    try {
      const summary = await emailDeletionService.getEmailDeletionSummary(emailId);
      summaries.push({ emailId, summary });

      // Add to totals
      if (summary.emailExists) {
        totalEntities.emailUpdates += summary.emailUpdates;
        totalEntities.shipments += summary.shipments;
        totalEntities.commercialInvoices += summary.commercialInvoices;
        totalEntities.documents += summary.documents;
        totalEntities.files += summary.files;
        totalEntities.fileBatches += summary.fileBatches;
        totalEntities.emailThreads += summary.emailThreads;
      }
    } catch (error) {
      console.log(`   ⚠️  Error getting summary for email ${emailId}: ${error.message}`);
      summaries.push({ emailId, error: error.message });
    }
  }

  console.log("\n🗂️  Total entities that will be deleted:");
  console.log(`   📧 Emails: ${emailIds.length}`);
  console.log(`   📝 Email Updates: ${totalEntities.emailUpdates}`);
  console.log(`   🚢 Shipments: ${totalEntities.shipments}`);
  console.log(`   🧾 Commercial Invoices: ${totalEntities.commercialInvoices}`);
  console.log(`   📄 Documents: ${totalEntities.documents}`);
  console.log(`   📁 Files: ${totalEntities.files}`);
  console.log(`   📦 File Batches: ${totalEntities.fileBatches}`);
  console.log(`   💬 Email Threads: ${totalEntities.emailThreads}`);

  return { summaries, totalEntities };
}

// Function to delete emails
async function deleteEmails(emailDeletionService, emailIds, dryRun) {
  if (dryRun) {
    console.log("\n🔍 DRY RUN MODE - No emails will be deleted");
    console.log("💡 Remove --dry-run flag to actually delete the emails");
    return { deleted: 0, failed: 0 };
  }

  console.log(`\n🗑️  Starting deletion of ${emailIds.length} emails...`);
  console.log("⚠️  This operation cannot be undone!");

  let deleted = 0;
  let failed = 0;
  const startTime = Date.now();

  for (let i = 0; i < emailIds.length; i++) {
    const emailId = emailIds[i];
    const progress = `[${i + 1}/${emailIds.length}]`;

    try {
      console.log(`${progress} 🗑️  Deleting email ${emailId}...`);
      await emailDeletionService.deleteEmailCompletely(emailId);
      deleted++;
      console.log(`${progress} ✅ Successfully deleted email ${emailId}`);
    } catch (error) {
      failed++;
      console.log(`${progress} ❌ Failed to delete email ${emailId}: ${error.message}`);
    }
  }

  const duration = Math.round((Date.now() - startTime) / 1000);
  console.log(`\n📊 Deletion completed in ${duration}s:`);
  console.log(`   ✅ Successfully deleted: ${deleted}`);
  console.log(`   ❌ Failed to delete: ${failed}`);

  return { deleted, failed };
}

async function bootstrap() {
  const { dryRun, limit } = parseArgs();

  console.log("🧹 Comprehensive Email Cleanup");
  console.log("===============================\n");
  console.log(`📊 Limit: ${limit} emails`);
  console.log(`🔍 Mode: ${dryRun ? "DRY RUN (preview only)" : "LIVE DELETION"}`);

  if (!dryRun) {
    console.log("\n⚠️  WARNING: This will permanently delete emails and ALL related data!");
    console.log("⚠️  This includes test emails, stuck emails, and all associated entities!");
    console.log("⚠️  This operation cannot be undone!");
  }

  let app; // Declare app variable outside try block for proper cleanup

  try {
    // Create NestJS application context
    console.log("\n🔧 Initializing NestJS application context...");
    const contextId = ContextIdFactory.create();
    app = await NestFactory.createApplicationContext(AppModule);
    globalApp = app; // Set global reference for cleanup handlers

    // Get services and repositories
    const emailDeletionService = await app.resolve(EmailDeletionService, contextId);
    const emailRepository = await app.get(getRepositoryToken(Email));

    console.log("✅ NestJS context initialized\n");

    // Find problematic emails
    const emails = await findProblematicEmails(emailRepository, limit);

    if (emails.length === 0) {
      console.log("✅ No problematic emails found. Database is clean!");
      console.log("\n🔄 Closing application gracefully...");
      await app.close();
      console.log("✅ Application closed successfully");
      process.exit(0);
    }

    // Get deletion summary
    const emailIds = emails.map((email) => email.id);
    const { summaries, totalEntities } = await getMultipleEmailDeletionSummary(
      emailDeletionService,
      emailIds
    );

    // Confirm deletion if not dry run
    if (!dryRun) {
      console.log("\n❓ Are you sure you want to delete these emails and all related data?");
      console.log("💡 Press Ctrl+C to cancel, or wait 10 seconds to proceed...");

      // Wait 10 seconds for user to cancel
      await new Promise((resolve) => setTimeout(resolve, 10000));
      console.log("⏳ Proceeding with deletion...");
    }

    // Delete emails
    const result = await deleteEmails(emailDeletionService, emailIds, dryRun);

    console.log("\n🎉 Email cleanup completed!");

    if (!dryRun && result.deleted > 0) {
      console.log("\n💡 Tips:");
      console.log("- Check portal-api logs for detailed deletion information");
      console.log("- All related entities (shipments, documents, etc.) have been removed");
      console.log("- Database now has a clean slate for testing");
      console.log("- You can now run new tests without interference from old data");
    }

    console.log("\n🔄 Closing application gracefully...");
    await app.close();
    console.log("✅ Application closed successfully");
    process.exit(0);
  } catch (error) {
    console.error(`\n❌ Error: ${error.message}`);
    console.error("Stack trace:", error.stack);

    if (app) {
      console.log("\n🔄 Closing application due to error...");
      try {
        await app.close();
        console.log("✅ Application closed successfully");
      } catch (closeError) {
        console.error("❌ Error closing application:", closeError.message);
      }
    }

    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on("unhandledRejection", async (reason, promise) => {
  console.error("❌ Unhandled Rejection at:", promise, "reason:", reason);
  if (globalApp) {
    await globalApp.close();
  }
  process.exit(1);
});

// Handle uncaught exceptions
process.on("uncaughtException", async (error) => {
  console.error("❌ Uncaught Exception:", error);
  if (globalApp) {
    await globalApp.close();
  }
  process.exit(1);
});

// Run the script
bootstrap().catch(async (error) => {
  console.error("❌ Bootstrap failed:", error);
  if (globalApp) {
    await globalApp.close();
  }
  process.exit(1);
});
