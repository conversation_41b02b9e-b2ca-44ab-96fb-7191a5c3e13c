#!/usr/bin/env node

const readline = require("readline");
const http = require("http");

// Configuration
const API_BASE_URL = "http://localhost:5001";
const DEFAULT_LOGIN_EMAIL = process.env.TEST_LOGIN_EMAIL || ""; // Remove hardcoded email
const DEFAULT_LOGIN_PASSWORD = process.env.TEST_LOGIN_PASSWORD || ""; // Remove hardcoded password

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Utility function to prompt user input
function prompt(question, defaultValue = "") {
  return new Promise((resolve) => {
    const displayDefault = defaultValue ? ` (default: ${defaultValue})` : "";
    rl.question(`${question}${displayDefault}: `, (answer) => {
      resolve(answer.trim() || defaultValue);
    });
  });
}

// Utility function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = "";
      res.on("data", (chunk) => (body += chunk));
      res.on("end", () => {
        try {
          const response = {
            statusCode: res.statusCode,
            data: body ? JSON.parse(body) : null,
            headers: res.headers
          };
          resolve(response);
        } catch (e) {
          reject(new Error(`Failed to parse response: ${e.message}`));
        }
      });
    });

    req.on("error", reject);

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// Function to authenticate and get access token
async function authenticate(email, password) {
  console.log("🔐 Authenticating with portal-api...");

  const loginData = {
    loginMethod: "email",
    email: email,
    password: password
  };

  const options = {
    hostname: "localhost",
    port: 5001,
    path: "/auth/login",
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Content-Length": Buffer.byteLength(JSON.stringify(loginData))
    }
  };

  try {
    const response = await makeRequest(options, loginData);

    if (response.statusCode !== 200 && response.statusCode !== 201) {
      throw new Error(`Authentication failed: ${response.statusCode} - ${JSON.stringify(response.data)}`);
    }

    if (!response.data || !response.data.accessToken) {
      throw new Error("No access token received from authentication");
    }

    console.log("✅ Authentication successful\n");
    return response.data.accessToken;
  } catch (error) {
    throw new Error(`Authentication failed: ${error.message}`);
  }
}

// Function to check test status
async function checkTestStatus(accessToken, testRunId) {
  const options = {
    hostname: "localhost",
    port: 5001,
    path: `/emails/test/pipeline-test-status/${testRunId}`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${accessToken}`
    }
  };

  try {
    const response = await makeRequest(options);

    if (response.statusCode !== 200) {
      throw new Error(
        `Failed to check test status: ${response.statusCode} - ${JSON.stringify(response.data)}`
      );
    }

    return response.data;
  } catch (error) {
    throw new Error(`Failed to check test status: ${error.message}`);
  }
}

// Function to format duration
function formatDuration(ms) {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

// Function to display test status
function displayTestStatus(status) {
  console.log("📊 Test Status Report");
  console.log("====================\n");

  console.log(`🆔 Test Run ID: ${status.testRunId}`);
  console.log(`📧 Email ID: ${status.emailId}`);
  console.log(`📊 Current Status: ${status.currentStatus}`);
  console.log(`✅ Complete: ${status.isComplete ? "Yes" : "No"}`);
  console.log(`🎯 Success: ${status.isSuccess ? "Yes" : "No"}`);

  console.log(`\n⏰ Timeline:`);
  console.log(`   Started: ${new Date(status.startedAt).toLocaleString()}`);

  if (status.completedAt) {
    console.log(`   Completed: ${new Date(status.completedAt).toLocaleString()}`);
  }

  if (status.processingTimeMs) {
    console.log(`   Duration: ${formatDuration(status.processingTimeMs)}`);
  } else {
    const elapsed = Date.now() - new Date(status.startedAt).getTime();
    console.log(`   Elapsed: ${formatDuration(elapsed)}`);
  }

  if (status.createdEntities) {
    console.log(`\n📊 Created Entities:`);
    console.log(`   📧 Email Updates: ${status.createdEntities.emailUpdates}`);
    console.log(`   🚢 Shipments: ${status.createdEntities.shipments}`);
    console.log(`   📄 Commercial Invoices: ${status.createdEntities.commercialInvoices}`);
    console.log(`   📋 Documents: ${status.createdEntities.documents}`);
    console.log(`   📁 Files: ${status.createdEntities.files}`);
    console.log(`   📦 File Batches: ${status.createdEntities.fileBatches}`);
    console.log(`   🔗 Email Threads: ${status.createdEntities.emailThreads}`);
  }

  if (status.cleanupPerformed !== undefined) {
    console.log(`\n🧹 Cleanup: ${status.cleanupPerformed ? "Completed" : "Not performed"}`);
  }

  if (status.errorMessage) {
    console.log(`\n❌ Error: ${status.errorMessage}`);
  }
}

// Main function
async function main() {
  console.log("🔍 E2E Test Status Checker");
  console.log("==========================\n");

  try {
    // Get test run ID
    const testRunId = await prompt("Enter Test Run ID");

    if (!testRunId) {
      console.log("❌ Test Run ID is required");
      return;
    }

    // Get login credentials
    let email = DEFAULT_LOGIN_EMAIL;
    let password = DEFAULT_LOGIN_PASSWORD;

    // Only prompt if environment variables are not set
    if (!email) {
      email = await prompt("Enter Login Email");
    } else {
      console.log(`📧 Using email from environment: ${email}`);
    }

    if (!password) {
      password = await prompt("Enter Login Password");
    } else {
      console.log("🔑 Using password from environment variable");
    }

    if (!email || !password) {
      console.log("❌ Both email and password are required");
      console.log("💡 Set TEST_LOGIN_EMAIL and TEST_LOGIN_PASSWORD environment variables to avoid prompts");
      return;
    }

    // Authenticate
    const accessToken = await authenticate(email, password);

    // Check status
    console.log(`🔍 Checking status for test run: ${testRunId}\n`);
    const status = await checkTestStatus(accessToken, testRunId);

    // Display results
    displayTestStatus(status);

    if (!status.isComplete) {
      console.log("\n💡 Test is still running. You can:");
      console.log("   - Run this script again to check progress");
      console.log("   - Wait for the original test script to complete");
      console.log("   - Check portal-api logs for detailed processing info");
    }
  } catch (error) {
    console.error(`\n❌ Error: ${error.message}`);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Handle process termination
process.on("SIGINT", () => {
  console.log("\n\n👋 Goodbye!");
  rl.close();
  process.exit(0);
});

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error(`\n💥 Unexpected error: ${error.message}`);
    process.exit(1);
  });
}
