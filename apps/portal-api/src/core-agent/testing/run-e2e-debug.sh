#!/bin/bash

# E2E Email Pipeline Test with Debug Mode (Interactive Shipment Selection)
# This script lets the user select a shipment by status and org, then gathers all E2E test parameters.

set -e
set -o pipefail

# List of statuses (from STATUS_RECOMMENDATIONS in JS)
STATUSES=(
  pending-commercial-invoice
  pending-confirmation
  pending-arrival
  live
  entry-submitted
  entry-accepted
  exam
  released
  accounting-completed
)

# Loop until we find shipments and select one
while true; do
  # Prompt for status
  echo ""
  PS3="Select a shipment status: "
  select STATUS in "${STATUSES[@]}"; do
    if [[ -n "$STATUS" ]]; then
      break
    else
      echo "Invalid selection."
    fi
  done

  echo "You selected status: $STATUS"

  # Prompt for org number (default 3)
  read -p "Enter organization ID [3]: " ORG
  ORG=${ORG:-3}
  echo "Using organization ID: $ORG"

  # Run the menu-friendly shipment search (limit 10)
  echo "Searching for shipments..."
  echo "Debug: Running command: node \"$(dirname "$0")/find-test-shipments-menu.js\" --status=\"$STATUS\" --org=\"$ORG\" --limit=10"

  # Check if the Node.js script exists
  if [[ ! -f "$(dirname "$0")/find-test-shipments-menu.js" ]]; then
    echo "Error: find-test-shipments-menu.js not found in $(dirname "$0")"
    echo "Please ensure the script exists and you're running from the correct directory."
    exit 1
  fi

  # Try to run the search with more detailed error reporting
  SHIPMENT_LINES=""
  if SHIPMENT_LINES=$(node "$(dirname "$0")/find-test-shipments-menu.js" --status="$STATUS" --org="$ORG" --limit=10 2>/dev/null | grep -v "\[Nest\]" | grep -v "LOG" || true); then
    echo "Debug: Search completed successfully"
  else
    echo "Error: Node.js script failed"
    echo ""
    echo "Possible issues:"
    echo "1. Database connection failed"
    echo "2. Organization $ORG doesn't exist"
    echo "3. No shipments with status '$STATUS' found"
    echo ""
    read -p "Would you like to try a different organization or status? (y/n): " RETRY
    if [[ "$RETRY" =~ ^[Yy] ]]; then
      continue  # Go back to the beginning of the loop
    else
      exit 1
    fi
  fi

  # Check if we got any results
  if [[ -z "$SHIPMENT_LINES" ]]; then
    echo "No shipments found for status '$STATUS' and org '$ORG'."
    echo ""
    echo "Debug: Let's check what organizations and statuses are available..."
    
    # Try to show available organizations
    echo "Available organizations:"
    if node "$(dirname "$0")/db-query.js" orgs 2>/dev/null; then
      echo ""
    else
      echo "Could not retrieve organizations (database may be unavailable)"
    fi
    
    echo "Available statuses to try:"
    for status in "${STATUSES[@]}"; do
      echo "  - $status"
    done
    echo ""
    
    read -p "Would you like to try again with different parameters? (y/n): " RETRY
    if [[ "$RETRY" =~ ^[Yy] ]]; then
      continue  # Go back to the beginning of the loop
    else
      exit 0
    fi
  fi

  # Read lines into array using safer method
  SHIPMENTS=()
  while IFS= read -r line; do
    if [[ -n "$line" ]]; then
      SHIPMENTS+=("$line")
    fi
  done <<< "$SHIPMENT_LINES"

  if [[ ${#SHIPMENTS[@]} -eq 0 ]]; then
    echo "No valid shipments found after parsing."
    read -p "Would you like to try again with different parameters? (y/n): " RETRY
    if [[ "$RETRY" =~ ^[Yy] ]]; then
      continue  # Go back to the beginning of the loop
    else
      exit 0
    fi
  fi

  # Shipment selection
  echo "Found ${#SHIPMENTS[@]} shipments. Select a shipment to use:"
  select SHIPMENT in "${SHIPMENTS[@]}"; do
    if [[ -n "$SHIPMENT" ]]; then
      # SHIPMENT is tab-separated: id hblNumber customsStatus createDate
      HBL=$(echo "$SHIPMENT" | awk -F'\t' '{print $2}')
      SHIPMENT_ID=$(echo "$SHIPMENT" | awk -F'\t' '{print $1}')
      
      # Validate HBL
      if [[ -z "$HBL" || "$HBL" == "N/A" ]]; then
        echo "Warning: Selected shipment has no HBL number. Using shipment ID as subject."
        SUBJECT="Shipment-$SHIPMENT_ID"
      else
        SUBJECT="$HBL"
      fi
      
      echo "You selected shipment with HBL: $HBL (ID: $SHIPMENT_ID)"
      break 2  # Break out of both select and while loop
    else
      echo "Invalid selection."
    fi
  done
done

# Continue with the rest of the script after successful shipment selection
echo ""
echo "Great! Now let's configure the email details..."

# Prompt for from address
read -p "Enter FROM address [<EMAIL>]: " FROM_ADDR
FROM_ADDR=${FROM_ADDR:-<EMAIL>}

# Look up TO address (default inbox) for org
echo "Setting up inbox email for organization $ORG..."
if [[ "$ORG" == "3" ]]; then
  TO_ADDR="<EMAIL>"
  echo "Using demo org default: $TO_ADDR"
else
  echo "For organization $ORG, please enter the inbox email manually."
  read -p "Enter TO address: " TO_ADDR
  # Validate email format
  if [[ ! "$TO_ADDR" =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+$ ]]; then
    echo "Warning: TO address format may be invalid: $TO_ADDR"
  fi
fi

# Prompt for attachments
echo ""
echo "Include attachments in the email?"
PS3="Select an option: "
select ATTACH_OPTION in "Yes" "No"; do
  case $ATTACH_OPTION in
    "Yes")
      ATTACHMENTS="--with-attachments"
      echo "Attachments will be included"
      break
      ;;
    "No")
      ATTACHMENTS="--no-attachments"
      echo "No attachments will be included"
      break
      ;;
    *)
      echo "Invalid selection."
      ;;
  esac
done

echo ""
echo "=========================================="
echo "Ready to run E2E test with:"
echo "  Organization: $ORG"
echo "  Shipment ID: $SHIPMENT_ID"
echo "  From: $FROM_ADDR"
echo "  To: $TO_ADDR"
echo "  Subject: $SUBJECT"
echo "  Attachments: $ATTACH_OPTION"
echo "=========================================="

# Prompt for final confirmation
read -p "Run the E2E test now? (y/n): " CONFIRM
if [[ ! "$CONFIRM" =~ ^[Yy] ]]; then
  echo "Test cancelled."
  exit 0
fi

echo ""
echo "🚀 Starting E2E test with Node.js debugger..."
echo "🔍 Debug URL will be available at: chrome://inspect"
echo "💡 Set breakpoints in Chrome DevTools before the test starts processing"
echo ""

# Build the command with all parameters
E2E_SCRIPT="$(dirname "$0")/e2e-email-pipeline-nestjs.js"

# Check if the E2E script exists
if [[ ! -f "$E2E_SCRIPT" ]]; then
  echo "Error: e2e-email-pipeline-nestjs.js not found at $E2E_SCRIPT"
  exit 1
fi

# Build the command arguments
CMD_ARGS=(
  "--org=$ORG"
  "--from-email=$FROM_ADDR"
  "--inbox-email=$TO_ADDR"
  "--subject=$SUBJECT"
  "$ATTACHMENTS"
)

# Display the full command being executed
echo "Executing command:"
echo "node --inspect-brk=\"$E2E_SCRIPT\" ${CMD_ARGS[*]}"
echo ""

# Execute the command with debugger
exec node --inspect-brk "$E2E_SCRIPT" "${CMD_ARGS[@]}"
