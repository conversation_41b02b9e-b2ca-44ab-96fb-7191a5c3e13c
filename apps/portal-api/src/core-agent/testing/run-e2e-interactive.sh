#!/bin/bash

# Interactive E2E Email Pipeline Test
# This script provides an interactive interface to configure and run E2E email tests.
# Features: shipment selection by status, organization lookup, email configuration, and debug mode option.

set -e
set -o pipefail

# List of statuses (from STATUS_RECOMMENDATIONS in JS)
STATUSES=(
  "New Shipment"
  pending-commercial-invoice
  pending-confirmation
  pending-arrival
  live
  entry-submitted
  entry-accepted
  exam
  released
  accounting-completed
)

# Loop until we find shipments and select one
while true; do
  # Prompt for status
  echo ""
  PS3="Select a shipment status: "
  select STATUS in "${STATUSES[@]}"; do
    if [[ -n "$STATUS" ]]; then
      break
    else
      echo "Invalid selection."
    fi
  done

  echo "You selected status: $STATUS"

  # Prompt for org number (default 3)
  read -p "Enter organization ID [3]: " ORG
  ORG=${ORG:-3}
  echo "Using organization ID: $ORG"

  # Check if "New Shipment" was selected
  if [[ "$STATUS" == "New Shipment" ]]; then
    echo "Skipping shipment selection - will create new shipment email"
    # Generate a unique HBL for new shipment
    TIMESTAMP=$(date +%Y%m%d%H%M%S)
    HBL="NEW-$TIMESTAMP"
    SHIPMENT_ID="new"
    SUBJECT="$HBL"
    echo "Generated HBL: $HBL"
    break  # Exit the while loop
  fi

  # Run the menu-friendly shipment search (limit 10)
  echo "Searching for shipments..."
  echo "Debug: Running command: node \"$(dirname "$0")/find-test-shipments-menu.js\" --status=\"$STATUS\" --org=\"$ORG\" --limit=10"

  # Check if the Node.js script exists
  if [[ ! -f "$(dirname "$0")/find-test-shipments-menu.js" ]]; then
    echo "Error: find-test-shipments-menu.js not found in $(dirname "$0")"
    echo "Please ensure the script exists and you're running from the correct directory."
    exit 1
  fi

  # Try to run the search with more detailed error reporting
  SHIPMENT_LINES=""
  if SHIPMENT_LINES=$(node "$(dirname "$0")/find-test-shipments-menu.js" --status="$STATUS" --org="$ORG" --limit=10 2>/dev/null | grep -v "\[Nest\]" | grep -v "LOG" || true); then
    echo "Debug: Search completed successfully"
  else
    echo "Error: Node.js script failed"
    echo ""
    echo "Possible issues:"
    echo "1. Database connection failed"
    echo "2. Organization $ORG doesn't exist"
    echo "3. No shipments with status '$STATUS' found"
    echo ""
    read -p "Would you like to try a different organization or status? (y/n): " RETRY
    if [[ "$RETRY" =~ ^[Yy] ]]; then
      continue  # Go back to the beginning of the loop
    else
      exit 1
    fi
  fi

  # Check if we got any results
  if [[ -z "$SHIPMENT_LINES" ]]; then
    echo "No shipments found for status '$STATUS' and org '$ORG'."
    echo ""
    echo "Debug: Let's check what organizations and statuses are available..."
    
    # Try to show available organizations
    echo "Available organizations:"
    if node "$(dirname "$0")/db-query.js" orgs 2>/dev/null; then
      echo ""
    else
      echo "Could not retrieve organizations (database may be unavailable)"
    fi
    
    echo "Available statuses to try:"
    for status in "${STATUSES[@]}"; do
      echo "  - $status"
    done
    echo ""
    
    read -p "Would you like to try again with different parameters? (y/n): " RETRY
    if [[ "$RETRY" =~ ^[Yy] ]]; then
      continue  # Go back to the beginning of the loop
    else
      exit 0
    fi
  fi

  # Read lines into array using safer method
  SHIPMENTS=()
  while IFS= read -r line; do
    if [[ -n "$line" ]]; then
      SHIPMENTS+=("$line")
    fi
  done <<< "$SHIPMENT_LINES"

  if [[ ${#SHIPMENTS[@]} -eq 0 ]]; then
    echo "No valid shipments found after parsing."
    read -p "Would you like to try again with different parameters? (y/n): " RETRY
    if [[ "$RETRY" =~ ^[Yy] ]]; then
      continue  # Go back to the beginning of the loop
    else
      exit 0
    fi
  fi

  # Shipment selection
  echo "Found ${#SHIPMENTS[@]} shipments. Select a shipment to use:"
  select SHIPMENT in "${SHIPMENTS[@]}"; do
    if [[ -n "$SHIPMENT" ]]; then
      # SHIPMENT is tab-separated: id hblNumber customsStatus createDate
      HBL=$(echo "$SHIPMENT" | awk -F'\t' '{print $2}')
      SHIPMENT_ID=$(echo "$SHIPMENT" | awk -F'\t' '{print $1}')
      
      # Validate HBL
      if [[ -z "$HBL" || "$HBL" == "N/A" ]]; then
        echo "Warning: Selected shipment has no HBL number. Using shipment ID as subject."
        SUBJECT="Shipment-$SHIPMENT_ID"
      else
        SUBJECT="$HBL"
      fi
      
      echo "You selected shipment with HBL: $HBL (ID: $SHIPMENT_ID)"
      break 2  # Break out of both select and while loop
    else
      echo "Invalid selection."
    fi
  done
done

# Continue with the rest of the script after successful shipment selection
echo ""
echo "Great! Now let's configure the email details..."

# Prompt for from address
read -p "Enter FROM address [<EMAIL>]: " FROM_ADDR
FROM_ADDR=${FROM_ADDR:-<EMAIL>}

# Look up TO address (inbox) for org from database
echo "Looking up inbox email for organization $ORG..."
TO_ADDR=""

# Run the database query (disable exit on error temporarily)
set +e
QUERY_OUTPUT=$(node "$(dirname "$0")/db-query.js" sql "SELECT DISTINCT \"inboxEmail\" FROM email WHERE \"organizationId\" = $ORG AND \"inboxEmail\" LIKE '%@doc.clarocustoms.com' LIMIT 1;" 2>/dev/null)
QUERY_EXIT_CODE=$?
set -e

# Check if the query succeeded
if [[ $QUERY_EXIT_CODE -eq 0 && -n "$QUERY_OUTPUT" ]]; then
  # Filter out log messages and remove "Result:" prefix
  FILTERED_OUTPUT=$(echo "$QUERY_OUTPUT" | grep -v "\[Nest\]" | grep -v "LOG" | grep -v "Error:" | grep -v "Connection is closed" | sed 's/^Result: //' || true)
  
  # Extract email from the JSON result
  if command -v jq >/dev/null 2>&1; then
    TO_ADDR=$(echo "$FILTERED_OUTPUT" | jq -r '.[0].inboxEmail // empty' 2>/dev/null || echo "")
  else
    # Fallback parsing without jq
    TO_ADDR=$(echo "$FILTERED_OUTPUT" | grep -o '"inboxEmail":[[:space:]]*"[^"]*"' | sed 's/"inboxEmail":[[:space:]]*"\([^"]*\)"/\1/' || echo "")
  fi
  
  if [[ -n "$TO_ADDR" && "$TO_ADDR" != "null" && "$TO_ADDR" != "empty" ]]; then
    echo "Found inbox email for org $ORG: $TO_ADDR"
  else
    TO_ADDR=""
  fi
else
  echo "Database query failed or returned no results"
  TO_ADDR=""
fi

# Fallback if no email found or query failed
if [[ -z "$TO_ADDR" ]]; then
  echo "No inbox email found for organization $ORG in database."
  if [[ "$ORG" == "3" ]]; then
    TO_ADDR="<EMAIL>"
    echo "Using demo org default: $TO_ADDR"
  else
    echo "Please enter the inbox email manually."
    read -p "Enter TO address: " TO_ADDR
    # Validate email format
    if [[ ! "$TO_ADDR" =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+$ ]]; then
      echo "Warning: TO address format may be invalid: $TO_ADDR"
    fi
  fi
fi

# Prompt for message body
echo ""
read -p "Enter message body [Please process customs]: " MESSAGE_BODY
MESSAGE_BODY=${MESSAGE_BODY:-"Please process customs"}

# Prompt for subject addition
echo ""
read -p "Add to subject (or press Enter for nothing): " SUBJECT_ADD
if [[ -n "$SUBJECT_ADD" ]]; then
  SUBJECT="$SUBJECT - $SUBJECT_ADD"
fi

# Prompt for attachments
echo ""
echo "Include attachments in the email?"
PS3="Select an option: "
select ATTACH_OPTION in "Yes" "No"; do
  case $ATTACH_OPTION in
    "Yes")
      ATTACHMENTS="--with-attachments"
      echo "Attachments will be included"
      break
      ;;
    "No")
      ATTACHMENTS="--no-attachments"
      echo "No attachments will be included"
      break
      ;;
    *)
      echo "Invalid selection."
      ;;
  esac
done

# Prompt for debug mode
echo ""
echo "Run in debug mode?"
PS3="Select execution mode: "
select DEBUG_MODE in "Normal" "Debug"; do
  case $DEBUG_MODE in
    "Normal")
      RUN_MODE="normal"
      echo "Will run in normal mode"
      break
      ;;
    "Debug")
      RUN_MODE="debug"
      echo "Will run in debug mode with Node.js inspector"
      break
      ;;
    *)
      echo "Invalid selection."
      ;;
  esac
done

echo ""
echo "=========================================="
echo "Ready to run E2E test with:"
echo "  Organization: $ORG"
if [[ "$SHIPMENT_ID" == "new" ]]; then
  echo "  Shipment: New shipment (no existing shipment selected)"
else
  echo "  Shipment ID: $SHIPMENT_ID"
fi
echo "  From: $FROM_ADDR"
echo "  To: $TO_ADDR"
echo "  Subject: $SUBJECT"
echo "  Message: $MESSAGE_BODY"
echo "  Attachments: $ATTACH_OPTION"
echo "  Execution Mode: $DEBUG_MODE"
echo "=========================================="

# Prompt for final confirmation
read -p "Run the E2E test now? (y/n): " CONFIRM
if [[ ! "$CONFIRM" =~ ^[Yy] ]]; then
  echo "Test cancelled."
  exit 0
fi

echo ""
if [[ "$RUN_MODE" == "debug" ]]; then
  echo "🚀 Starting E2E test with Node.js debugger..."
  echo "🔍 Debug URL will be available at: chrome://inspect"
  echo "💡 Set breakpoints in Chrome DevTools before the test starts processing"
else
  echo "🚀 Starting E2E test in normal mode..."
fi
echo ""

# Build the command with all parameters
E2E_SCRIPT="$(dirname "$0")/e2e-email-pipeline-nestjs.js"

# Check if the E2E script exists
if [[ ! -f "$E2E_SCRIPT" ]]; then
  echo "Error: e2e-email-pipeline-nestjs.js not found at $E2E_SCRIPT"
  exit 1
fi

# Build the command arguments
CMD_ARGS=(
  "--org=$ORG"
  "--from-email=$FROM_ADDR"
  "--inbox-email=$TO_ADDR"
  "--subject=$SUBJECT"
  "--body=$MESSAGE_BODY"
  "$ATTACHMENTS"
)

# Display the full command being executed
echo "Executing command:"
if [[ "$RUN_MODE" == "debug" ]]; then
  echo "node --inspect-brk \"$E2E_SCRIPT\" ${CMD_ARGS[*]}"
  echo ""
  # Execute the command with debugger
  exec node --inspect-brk "$E2E_SCRIPT" "${CMD_ARGS[@]}"
else
  echo "node \"$E2E_SCRIPT\" ${CMD_ARGS[*]}"
  echo ""
  # Execute the command in normal mode
  exec node "$E2E_SCRIPT" "${CMD_ARGS[@]}"
fi
