#!/bin/bash

# Direct Processor Test with Automatic Logging
# This script runs the direct processor test and saves output to .ai-reference for dev analysis

set -e

# Generate timestamp for log file
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE=".ai-reference/processor-test-${TIMESTAMP}.log"

echo "🚀 Running Direct Processor Test with Logging"
echo "📝 Log file: ${LOG_FILE}"
echo "======================================================"

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Change to portal-api directory (go up from core-agent/testing to portal-api)
cd "${SCRIPT_DIR}/../../../"

# Ensure .ai-reference directory exists
mkdir -p .ai-reference

# Run the processor test and save output to log file
node src/core-agent/testing/test-processor-direct.js "$@" 2>&1 | tee "${LOG_FILE}"

echo ""
echo "✅ Test completed! Log saved to: ${LOG_FILE}"
echo "💡 You can review the log file for detailed analysis"
echo "🔍 Log file location: $(pwd)/${LOG_FILE}"
