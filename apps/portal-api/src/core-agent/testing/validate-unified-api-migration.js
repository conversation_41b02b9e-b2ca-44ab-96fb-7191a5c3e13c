#!/usr/bin/env node

/**
 * Validation Script for Unified Template API Migration
 * 
 * This script validates that all the compilation errors have been fixed
 * and the unified template API migration is complete.
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Validating Unified Template API Migration');
console.log('='.repeat(50));

// Check 1: Verify IntentHandler interface returns string
function checkIntentHandlerInterface() {
  console.log('\n📋 Checking IntentHandler interface...');
  
  const interfacePath = path.join(__dirname, '../interfaces/intent-handler.interface.ts');
  const content = fs.readFileSync(interfacePath, 'utf8');
  
  if (content.includes('Promise<string>')) {
    console.log('   ✅ IntentHandler interface returns Promise<string>');
    return true;
  } else {
    console.log('   ❌ IntentHandler interface does not return Promise<string>');
    return false;
  }
}

// Check 2: Verify handlers use UnifiedTemplateRendererService
function checkHandlersUseUnifiedService() {
  console.log('\n🎯 Checking intent handlers use UnifiedTemplateRendererService...');
  
  const handlersDir = path.join(__dirname, '../handlers');
  const handlerFiles = fs.readdirSync(handlersDir).filter(f => f.endsWith('.handler.ts'));
  
  let allGood = true;
  let checkedCount = 0;
  
  for (const file of handlerFiles) {
    if (file === 'base-intent-handler.ts') continue; // Skip base handler
    
    const filePath = path.join(handlersDir, file);
    const content = fs.readFileSync(filePath, 'utf8');
    
    if (content.includes('UnifiedTemplateRendererService')) {
      console.log(`   ✅ ${file}: Uses UnifiedTemplateRendererService`);
      checkedCount++;
    } else {
      console.log(`   ⚠️  ${file}: Does not use UnifiedTemplateRendererService`);
      allGood = false;
    }
  }
  
  console.log(`   📊 Checked ${checkedCount} handlers`);
  return allGood;
}

// Check 3: Verify specific fixes were applied
function checkSpecificFixes() {
  console.log('\n🔧 Checking specific compilation fixes...');
  
  let allGood = true;
  
  // Check process-document.handler.ts - acknowledgment property removed
  const processDocPath = path.join(__dirname, '../handlers/process-document.handler.ts');
  const processDocContent = fs.readFileSync(processDocPath, 'utf8');
  
  if (!processDocContent.includes('acknowledgment: cadData?.acknowledgment')) {
    console.log('   ✅ process-document.handler.ts: acknowledgment property removed');
  } else {
    console.log('   ❌ process-document.handler.ts: acknowledgment property still present');
    allGood = false;
  }
  
  // Check request-rns-proof.handler.ts - rnsProofContent removed  
  const rnsProofPath = path.join(__dirname, '../handlers/request-rns-proof.handler.ts');
  const rnsProofContent = fs.readFileSync(rnsProofPath, 'utf8');
  
  if (!rnsProofContent.includes('rnsProofContent: rnsProofText')) {
    console.log('   ✅ request-rns-proof.handler.ts: rnsProofContent property removed');
  } else {
    console.log('   ❌ request-rns-proof.handler.ts: rnsProofContent property still present');
    allGood = false;
  }
  
  return allGood;
}

// Check 4: Verify no old API imports remain
function checkNoOldAPIImports() {
  console.log('\n📦 Checking for old API imports...');
  
  const srcPath = path.join(__dirname, '../../..');
  const patterns = [
    'renderEmailResponse',
    '../constants/email-response-messages',
    'import.*TemplateSectionFlags',
    'import.*TemplateMessageData'
  ];
  
  let foundOldImports = false;
  
  function checkDirectory(dir) {
    const items = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const item of items) {
      const fullPath = path.join(dir, item.name);
      
      if (item.isDirectory() && item.name !== 'node_modules' && item.name !== 'dist') {
        checkDirectory(fullPath);
      } else if (item.isFile() && item.name.endsWith('.ts')) {
        const content = fs.readFileSync(fullPath, 'utf8');
        
        for (const pattern of patterns) {
          if (content.includes(pattern)) {
            console.log(`   ⚠️  Found old API usage "${pattern}" in ${fullPath}`);
            foundOldImports = true;
          }
        }
      }
    }
  }
  
  checkDirectory(srcPath);
  
  if (!foundOldImports) {
    console.log('   ✅ No old API imports found');
    return true;
  } else {
    return false;
  }
}

// Check 5: Validate that the project builds
async function checkProjectBuilds() {
  console.log('\n🏗️  Checking if project builds successfully...');
  
  return new Promise((resolve) => {
    const { spawn } = require('child_process');
    
    const buildProcess = spawn('npm', ['run', 'build'], {
      stdio: 'pipe',
      cwd: process.cwd()
    });
    
    let output = '';
    let errorOutput = '';
    
    buildProcess.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    buildProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    buildProcess.on('close', (code) => {
      if (code === 0) {
        console.log('   ✅ Project builds successfully');
        resolve(true);
      } else {
        console.log('   ❌ Project build failed');
        console.log('   Error output:', errorOutput.substring(0, 500));
        resolve(false);
      }
    });
  });
}

// Main validation function
async function runValidation() {
  const checks = [
    { name: 'IntentHandler Interface', fn: checkIntentHandlerInterface },
    { name: 'Handlers Use Unified Service', fn: checkHandlersUseUnifiedService },
    { name: 'Specific Compilation Fixes', fn: checkSpecificFixes },
    { name: 'No Old API Imports', fn: checkNoOldAPIImports }
  ];
  
  let allPassed = true;
  const results = [];
  
  for (const check of checks) {
    const passed = check.fn();
    results.push({ name: check.name, passed });
    if (!passed) allPassed = false;
  }
  
  // Check build separately as it's async
  const buildPassed = await checkProjectBuilds();
  results.push({ name: 'Project Builds', passed: buildPassed });
  if (!buildPassed) allPassed = false;
  
  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('📊 VALIDATION SUMMARY');
  console.log('='.repeat(50));
  
  for (const result of results) {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.name}`);
  }
  
  console.log(`\nOverall Result: ${allPassed ? '✅ ALL CHECKS PASSED' : '❌ SOME CHECKS FAILED'}`);
  
  if (allPassed) {
    console.log('\n🎉 Unified Template API migration completed successfully!');
    console.log('\nKey achievements:');
    console.log('  - All compilation errors fixed');
    console.log('  - Intent handlers return strings instead of fragments');
    console.log('  - UnifiedTemplateRendererService properly integrated');
    console.log('  - Old API references removed');
    console.log('  - Project builds without errors');
    
    console.log('\n📋 Testing recommendations:');
    console.log('  1. Run: ./src/core-agent/testing/unified-template-api-test.js');
    console.log('  2. Test email processing with various intents');
    console.log('  3. Verify email templates render correctly');
    console.log('  4. Test all customs status scenarios');
  } else {
    console.log('\n⚠️  Migration not complete. Please address the failed checks above.');
  }
  
  return allPassed;
}

if (require.main === module) {
  runValidation().catch(console.error);
}

module.exports = { runValidation };