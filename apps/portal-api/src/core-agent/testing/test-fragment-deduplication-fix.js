#!/usr/bin/env node

/**
 * Fragment Deduplication Fix Test Script
 *
 * This script specifically tests the fragment deduplication fix for the GetShipmentStatusHandler.
 * It verifies that when multiple questions are asked in a single email, all answers are preserved
 * instead of being overwritten due to duplicate template names.
 *
 * Usage:
 *   node src/core-agent/testing/test-fragment-deduplication-fix.js [options]
 *
 * Options:
 *   --org=ID                   Organization ID (default: 3 - demo org)
 *   --shipment=ID              Specific shipment ID to test with
 *   --verbose                  Show detailed logging
 *
 * Examples:
 *   # Test with auto-selected shipment
 *   node src/core-agent/testing/test-fragment-deduplication-fix.js --verbose
 *
 *   # Test with specific shipment
 *   node src/core-agent/testing/test-fragment-deduplication-fix.js --shipment=123 --verbose
 */

const { AppModule } = require("../../../dist/app.module");
const { ContextIdFactory, NestFactory } = require("@nestjs/core");
const { DataSource } = require("typeorm");
const { generateRequest } = require("../../../dist/email/utils/generate-request");

// Import services
const { ShipmentContextService } = require("../../../dist/agent-context");
const {
  IntentHandlerRegistry
} = require("../../../dist/core-agent/services/intent-handler-registry.service");
const { ShipmentResponseService } = require("../../../dist/core-agent/services/shipment-response.service");

// Entity imports
const { Organization, Shipment, FIND_ORGANIZATION_RELATIONS } = require("nest-modules");

// Test scenarios designed to trigger multiple question types
const MULTI_QUESTION_SCENARIOS = [
  {
    name: "Multiple Status Questions",
    instructions: [
      "What is the status of my shipment?",
      "What is the transaction number?",
      "When is the ETA?",
      "Has the shipment been released?"
    ],
    expectedFragments: [
      "answer-eta-template",
      "answer-transaction-number-template",
      "answer-release-status-template",
      "core-agent/fragments/status-message",
      "core-agent/fragments/details"
    ]
  },
  {
    name: "Mixed Questions",
    instructions: [
      "What is the ETA for my shipment?",
      "Can you provide the transaction number?",
      "What is the shipping status?"
    ],
    expectedFragments: [
      "answer-eta-template",
      "answer-transaction-number-template",
      "answer-shipping-status-template",
      "core-agent/fragments/status-message",
      "core-agent/fragments/details"
    ]
  },
  {
    name: "Single Question (Control)",
    instructions: ["What is the transaction number?"],
    expectedFragments: [
      "answer-transaction-number-template",
      "core-agent/fragments/status-message",
      "core-agent/fragments/details"
    ]
  }
];

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const config = {
    organizationId: "3", // Default to demo org
    shipmentId: null,
    verbose: false
  };

  args.forEach((arg) => {
    if (arg.startsWith("--org=")) {
      config.organizationId = arg.split("=")[1];
    } else if (arg.startsWith("--shipment=")) {
      config.shipmentId = arg.split("=")[1];
    } else if (arg === "--verbose") {
      config.verbose = true;
    }
  });

  return config;
}

// Find a test shipment
async function findTestShipment(dataSource, organizationId) {
  const shipmentRepository = dataSource.getRepository("Shipment");

  const shipments = await shipmentRepository.find({
    where: { organizationId: parseInt(organizationId) },
    take: 5,
    order: { createDate: "DESC" }
  });

  return shipments[0];
}

// Test fragment deduplication
async function testFragmentDeduplication(
  scenario,
  context,
  intentHandlerRegistry,
  shipmentResponseService,
  config
) {
  console.log(`\n🧪 Testing Scenario: ${scenario.name}`);
  console.log(`📋 Instructions: ${scenario.instructions.join(", ")}`);
  console.log(`🎯 Expected Fragments: ${scenario.expectedFragments.length}`);

  try {
    // Create validated intent
    const validatedIntent = {
      intent: "GET_SHIPMENT_STATUS",
      instructions: scenario.instructions,
      attachments: []
    };

    // Get the handler
    const handler = intentHandlerRegistry.getHandler("GET_SHIPMENT_STATUS");
    if (!handler) {
      throw new Error("GET_SHIPMENT_STATUS handler not found");
    }

    // Handle the intent
    const startTime = Date.now();
    const testContext = {
      ...context,
      sideEffects: { backofficeAlerts: {} },
      emailId: `test-email-${Date.now()}`
    };

    const fragments = await handler.handle(validatedIntent, testContext);
    const processingTime = Date.now() - startTime;

    console.log(`✅ Handler completed in ${processingTime}ms`);
    console.log(`📊 Generated ${fragments.length} fragments`);

    // Analyze fragments
    const fragmentTemplates = fragments.map((f) => f.template);
    const uniqueTemplates = [...new Set(fragmentTemplates)];

    console.log(`📋 Fragment Templates:`);
    fragments.forEach((fragment, index) => {
      const priority = fragment.priority || "N/A";
      console.log(`   ${index + 1}. ${fragment.template} (priority: ${priority})`);
    });

    // Check for deduplication issues
    const duplicateTemplates = fragmentTemplates.filter(
      (template, index) => fragmentTemplates.indexOf(template) !== index
    );

    if (duplicateTemplates.length > 0) {
      console.log(`❌ Found duplicate templates: ${duplicateTemplates.join(", ")}`);
      console.log(`   This indicates the deduplication fix may not be working properly`);
    } else {
      console.log(`✅ No duplicate templates found - deduplication fix working correctly`);
    }

    // Render fragments to verify they survive deduplication
    console.log(`🎨 Rendering fragments through deduplication process...`);
    const responseHtml = await shipmentResponseService.renderFragments(fragments, testContext);

    // Count rendered content sections
    const htmlSections = responseHtml.split("<p>").filter((section) => section.trim().length > 0);
    console.log(`📧 Rendered response has ${htmlSections.length} sections`);

    // Look for specific answer patterns
    const hasEtaAnswer = responseHtml.includes("ETA") || responseHtml.includes("arrival");
    const hasTransactionAnswer = responseHtml.includes("transaction number");
    const hasReleaseAnswer = responseHtml.includes("released") || responseHtml.includes("cleared");
    const hasShippingAnswer = responseHtml.includes("shipping status");

    console.log(`📋 Answer Types Found in Response:`);
    console.log(`   ETA Answer: ${hasEtaAnswer ? "✅" : "❌"}`);
    console.log(`   Transaction Number Answer: ${hasTransactionAnswer ? "✅" : "❌"}`);
    console.log(`   Release Status Answer: ${hasReleaseAnswer ? "✅" : "❌"}`);
    console.log(`   Shipping Status Answer: ${hasShippingAnswer ? "✅" : "❌"}`);

    // Show response preview
    if (config.verbose) {
      const cleanResponse = responseHtml.replace(/<[^>]*>/g, "").substring(0, 300);
      console.log(`📧 Response Preview: ${cleanResponse}...`);
    }

    return {
      success: true,
      fragments: fragments.length,
      uniqueTemplates: uniqueTemplates.length,
      duplicateTemplates: duplicateTemplates.length,
      hasMultipleAnswers: hasEtaAnswer + hasTransactionAnswer + hasReleaseAnswer + hasShippingAnswer > 1,
      processingTime
    };
  } catch (error) {
    console.log(`❌ Error testing scenario: ${error.message}`);
    if (config.verbose) {
      console.log(error.stack);
    }
    return { success: false, error: error.message };
  }
}

// Main test function
async function runTests(config, app, dataSource) {
  try {
    console.log(`🚀 Testing Fragment Deduplication Fix`);
    console.log(`📋 Organization: ${config.organizationId}`);
    console.log(`🎯 Testing ${MULTI_QUESTION_SCENARIOS.length} scenarios`);

    // 1. Get organization
    const organization = await dataSource.manager.findOne(Organization, {
      where: { id: parseInt(config.organizationId) },
      relations: FIND_ORGANIZATION_RELATIONS
    });

    if (!organization) {
      throw new Error(`Organization ${config.organizationId} not found`);
    }

    console.log(`✅ Loaded organization: ${organization.name}`);

    // 2. Create request context
    const contextId = ContextIdFactory.create();
    const requestContext = generateRequest(null, organization);
    app.registerRequestByContextId(requestContext, contextId);

    // 3. Get services
    const shipmentContextService = await app.resolve(ShipmentContextService, contextId);
    const intentHandlerRegistry = await app.resolve(IntentHandlerRegistry, contextId);
    const shipmentResponseService = await app.resolve(ShipmentResponseService, contextId);

    console.log(`✅ Resolved services`);

    // 4. Find test shipment
    let shipment;
    if (config.shipmentId) {
      shipment = await dataSource.manager.findOne(Shipment, {
        where: { id: parseInt(config.shipmentId), organizationId: parseInt(config.organizationId) }
      });
      if (!shipment) {
        throw new Error(`Shipment ${config.shipmentId} not found`);
      }
    } else {
      shipment = await findTestShipment(dataSource, config.organizationId);
      if (!shipment) {
        throw new Error(`No shipments found for organization ${config.organizationId}`);
      }
    }

    console.log(`✅ Using shipment ${shipment.id} (${shipment.hblNumber || "No HBL"})`);

    // 5. Build shipment context
    const context = await shipmentContextService.buildContext(shipment.id, parseInt(config.organizationId));

    // 6. Run test scenarios
    const results = {};

    for (const scenario of MULTI_QUESTION_SCENARIOS) {
      const result = await testFragmentDeduplication(
        scenario,
        context,
        intentHandlerRegistry,
        shipmentResponseService,
        config
      );
      results[scenario.name] = result;
    }

    // 7. Show summary
    console.log(`\n📊 Test Results Summary`);
    console.log("======================");

    let passCount = 0;
    let totalFragments = 0;
    let totalUniqueTemplates = 0;
    let totalDuplicateTemplates = 0;
    let multiAnswerCount = 0;

    Object.entries(results).forEach(([scenarioName, result]) => {
      const status = result.success ? "✅ PASS" : "❌ FAIL";
      console.log(`${status} ${scenarioName}`);

      if (result.success) {
        passCount++;
        totalFragments += result.fragments;
        totalUniqueTemplates += result.uniqueTemplates;
        totalDuplicateTemplates += result.duplicateTemplates;

        if (result.hasMultipleAnswers) {
          multiAnswerCount++;
        }

        console.log(`     Fragments: ${result.fragments}, Unique Templates: ${result.uniqueTemplates}`);
        console.log(`     Duplicate Templates: ${result.duplicateTemplates}`);
        console.log(`     Multiple Answers: ${result.hasMultipleAnswers ? "Yes" : "No"}`);
      } else {
        console.log(`     Error: ${result.error}`);
      }
    });

    console.log(`\n📈 Overall Results:`);
    console.log(`   Scenarios Passed: ${passCount}/${Object.keys(results).length}`);
    console.log(`   Total Fragments Generated: ${totalFragments}`);
    console.log(`   Total Unique Templates: ${totalUniqueTemplates}`);
    console.log(`   Total Duplicate Templates: ${totalDuplicateTemplates}`);
    console.log(`   Scenarios with Multiple Answers: ${multiAnswerCount}`);

    // Final assessment
    const fixWorking = totalDuplicateTemplates === 0 && multiAnswerCount > 0;
    console.log(
      `\n${fixWorking ? "✅ SUCCESS" : "❌ ISSUES FOUND"}: Fragment deduplication fix is ${fixWorking ? "working correctly" : "not working as expected"}`
    );

    if (totalDuplicateTemplates > 0) {
      console.log(
        `⚠️  Warning: Found ${totalDuplicateTemplates} duplicate templates - this may indicate the fix needs refinement`
      );
    }
  } catch (error) {
    console.error(`💥 Test failed: ${error.message}`);
    if (config.verbose) {
      console.error(error.stack);
    }
    throw error;
  }
}

// Bootstrap and run tests
async function bootstrap() {
  const config = parseArgs();
  let app;

  try {
    console.log("🔧 Initializing NestJS application context...");
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: config.verbose ? ["log", "error", "warn", "debug"] : ["error"]
    });

    const dataSource = await app.get(DataSource);
    console.log("✅ NestJS context initialized\n");

    await runTests(config, app, dataSource);

    console.log("\n🎉 Fragment deduplication fix testing completed!");
  } catch (error) {
    console.error(`💥 Bootstrap failed: ${error.message}`);
    process.exit(1);
  } finally {
    if (app) {
      try {
        await app.close();
      } catch (closeError) {
        console.error("Error closing app:", closeError.message);
      }
    }
    process.exit(0);
  }
}

// Show usage if help requested
if (process.argv.includes("--help") || process.argv.includes("-h")) {
  console.log("Fragment Deduplication Fix Test Script");
  console.log("=====================================");
  console.log("");
  console.log("Tests the fragment deduplication fix for GetShipmentStatusHandler");
  console.log("to ensure multiple questions generate multiple answers in responses.");
  console.log("");
  console.log("Options:");
  console.log("  --org=ID                   Organization ID (default: 3)");
  console.log("  --shipment=ID              Specific shipment ID to test with");
  console.log("  --verbose                  Show detailed logging");
  console.log("  --help, -h                 Show this help message");
  console.log("");
  console.log("Examples:");
  console.log("  node src/core-agent/testing/test-fragment-deduplication-fix.js --verbose");
  console.log("  node src/core-agent/testing/test-fragment-deduplication-fix.js --shipment=123");
  process.exit(0);
}

// Handle process termination gracefully
process.on("SIGINT", () => {
  console.log("\nReceived SIGINT, shutting down gracefully...");
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("\nReceived SIGTERM, shutting down gracefully...");
  process.exit(0);
});

bootstrap();
