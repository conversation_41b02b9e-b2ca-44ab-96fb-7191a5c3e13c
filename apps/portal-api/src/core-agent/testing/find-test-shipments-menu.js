#!/usr/bin/env node

/**
 * Find Test Shipments Menu Script
 *
 * Outputs a tab-separated list of shipments for use in shell menus.
 * Usage:
 *   node find-test-shipments-menu.js --status=STATUS [--org=ID] [--limit=N]
 *
 * Output:
 *   id\thblNumber\tcustomsStatus\tcreateDate
 */

// Disable all console output during NestJS startup
const originalConsole = {
  log: console.log,
  error: console.error,
  warn: console.warn,
  debug: console.debug
};

// Silence console during startup
console.log = () => {};
console.error = () => {};
console.warn = () => {};
console.debug = () => {};

const { AppModule } = require("../../../dist/app.module");
const { NestFactory } = require("@nestjs/core");
const { DataSource } = require("typeorm");

const DEFAULTS = {
  organizationId: "3",
  limit: 10
};

function parseCommandLineArguments(args) {
  const result = {
    organizationId: DEFAULTS.organizationId,
    customsStatus: null,
    limit: DEFAULTS.limit
  };
  const remainingArgs = args.slice(2);
  for (let i = 0; i < remainingArgs.length; i++) {
    const arg = remainingArgs[i];
    if (arg.startsWith("--org=")) {
      result.organizationId = arg.split("=")[1];
    } else if (arg.startsWith("--status=")) {
      result.customsStatus = arg.split("=")[1];
    } else if (arg.startsWith("--limit=")) {
      result.limit = parseInt(arg.split("=")[1]);
    }
  }
  return result;
}

async function findShipmentsByStatus(dataSource, organizationId, customsStatus, limit) {
  const shipments = await dataSource.query(
    `
    WITH all_shipments AS (
      SELECT 
        s.id, 
        s."customsStatus", 
        s."hblNumber",
        s."cargoControlNumber",
        s."createDate"
      FROM shipment s
      WHERE s."organizationId" = $1
        AND s."hblNumber" IS NOT NULL
        AND s."cargoControlNumber" IS NOT NULL
        AND s."hblNumber" != ''
        AND s."cargoControlNumber" != ''
    ),
    global_counts AS (
      SELECT 
        *,
        COUNT(*) OVER (PARTITION BY "hblNumber") as hbl_count,
        COUNT(*) OVER (PARTITION BY "cargoControlNumber") as ccn_count
      FROM all_shipments
    ),
    unique_shipments AS (
      SELECT * FROM global_counts
      WHERE hbl_count = 1 AND ccn_count = 1
    )
    SELECT 
      id, 
      "customsStatus", 
      "hblNumber",
      "createDate"
    FROM unique_shipments
    WHERE "customsStatus" = $2
    ORDER BY "createDate" DESC
    LIMIT $3
  `,
    [organizationId, customsStatus, limit]
  );

  // Restore console.log only for our output
  console.log = originalConsole.log;

  shipments.forEach((shipment) => {
    // Output: id\thblNumber\tcustomsStatus\tcreateDate
    console.log(
      [
        shipment.id,
        shipment.hblNumber || "N/A",
        shipment.customsStatus || "N/A",
        shipment.createDate instanceof Date
          ? shipment.createDate.toISOString().split("T")[0]
          : shipment.createDate
      ].join("\t")
    );
  });
}

async function bootstrap() {
  const config = parseCommandLineArguments(process.argv);
  if (!config.customsStatus) {
    // Restore console.error for our errors
    console.error = originalConsole.error;
    console.error("Error: --status=STATUS is required");
    process.exit(1);
  }
  let app;
  try {
    app = await NestFactory.createApplicationContext(AppModule, { logger: false });
    const dataSource = await app.get(DataSource);
    await findShipmentsByStatus(
      dataSource,
      parseInt(config.organizationId),
      config.customsStatus,
      config.limit
    );
  } catch (error) {
    // Restore console.error if it was overridden
    console.error = originalConsole.error;
    console.error(`💥 Bootstrap failed: ${error.message}`);
    if (error.stack) {
      console.error(error.stack);
    }
    process.exit(1);
  } finally {
    if (app) {
      try {
        await app.close();
      } catch {}
    }
    process.exit(0);
  }
}

bootstrap();
