#!/usr/bin/env node

/**
 * Find Test Shipments Script
 *
 * This script helps find suitable shipments for testing the intent handlers.
 * It analyzes shipments by customs status and provides recommendations for testing different scenarios.
 *
 * Usage:
 *   node scripts/find-test-shipments.js [options]
 *
 * Options:
 *   --org=ID                   Organization ID (default: 3 - demo org)
 *   --status=STATUS            Filter by customs status
 *   --limit=N                  Number of shipments to show (default: 10)
 *   --analysis                 Show detailed analysis by status
 *
 * Examples:
 *   # Find shipments for demo org
 *   node scripts/find-test-shipments.js
 *
 *   # Find shipments with specific status
 *   node scripts/find-test-shipments.js --status=released
 *
 *   # Show analysis of all statuses
 *   node scripts/find-test-shipments.js --analysis
 */

const { AppModule } = require("../../../dist/app.module");
const { NestFactory } = require("@nestjs/core");
const { DataSource } = require("typeorm");

// Default configuration
const DEFAULTS = {
  organizationId: "3", // Demo organization
  limit: 10
};

// Intent testing recommendations by customs status
const STATUS_RECOMMENDATIONS = {
  "pending-commercial-invoice": {
    goodFor: ["GET_SHIPMENT_STATUS", "DOCUMENTATION_COMING", "REQUEST_RUSH_PROCESSING"],
    avoid: ["REQUEST_CAD_DOCUMENT", "REQUEST_RNS_PROOF"],
    description: "Missing documents - good for testing status inquiries and rush requests"
  },
  "pending-confirmation": {
    goodFor: ["GET_SHIPMENT_STATUS", "UPDATE_SHIPMENT", "REQUEST_RUSH_PROCESSING"],
    avoid: ["REQUEST_CAD_DOCUMENT", "REQUEST_RNS_PROOF"],
    description: "Compliance issues - good for testing updates and status inquiries"
  },
  "pending-arrival": {
    goodFor: ["GET_SHIPMENT_STATUS", "REQUEST_RUSH_PROCESSING"],
    avoid: ["REQUEST_CAD_DOCUMENT", "REQUEST_RNS_PROOF"],
    description: "Awaiting arrival - good for testing status and rush requests"
  },
  live: {
    goodFor: ["GET_SHIPMENT_STATUS", "PROCESS_DOCUMENT", "REQUEST_CAD_DOCUMENT"],
    avoid: ["REQUEST_RNS_PROOF"],
    description: "Entry ready - good for testing document processing and CAD requests"
  },
  "entry-submitted": {
    goodFor: ["GET_SHIPMENT_STATUS", "REQUEST_CAD_DOCUMENT"],
    avoid: ["REQUEST_RNS_PROOF"],
    description: "Entry submitted - good for testing CAD document requests"
  },
  "entry-accepted": {
    goodFor: ["GET_SHIPMENT_STATUS", "REQUEST_CAD_DOCUMENT"],
    avoid: ["REQUEST_RNS_PROOF"],
    description: "Entry accepted - good for testing CAD document requests"
  },
  exam: {
    goodFor: ["GET_SHIPMENT_STATUS", "REQUEST_CAD_DOCUMENT"],
    avoid: ["REQUEST_RNS_PROOF"],
    description: "Under examination - good for testing status and CAD requests"
  },
  released: {
    goodFor: ["GET_SHIPMENT_STATUS", "REQUEST_CAD_DOCUMENT", "REQUEST_RNS_PROOF"],
    avoid: [],
    description: "Released - excellent for testing all document requests"
  },
  "accounting-completed": {
    goodFor: ["GET_SHIPMENT_STATUS", "REQUEST_CAD_DOCUMENT", "REQUEST_RNS_PROOF"],
    avoid: [],
    description: "Fully completed - excellent for testing all document requests"
  }
};

// Function to parse command line arguments
function parseCommandLineArguments(args) {
  const result = {
    organizationId: DEFAULTS.organizationId,
    customsStatus: null,
    limit: DEFAULTS.limit,
    analysis: false
  };

  const remainingArgs = args.slice(2);

  for (let i = 0; i < remainingArgs.length; i++) {
    const arg = remainingArgs[i];

    if (arg.startsWith("--org=")) {
      result.organizationId = arg.split("=")[1];
    } else if (arg.startsWith("--status=")) {
      result.customsStatus = arg.split("=")[1];
    } else if (arg.startsWith("--limit=")) {
      result.limit = parseInt(arg.split("=")[1]);
    } else if (arg === "--analysis") {
      result.analysis = true;
    }
  }

  return result;
}

// Function to get shipment statistics by status
async function getShipmentStatistics(dataSource, organizationId) {
  console.log(`📊 Analyzing shipments for organization ${organizationId}...\n`);

  const stats = await dataSource.query(
    `
    SELECT 
      s."customsStatus",
      COUNT(*) as count,
      MIN(s."createDate") as oldest,
      MAX(s."createDate") as newest
    FROM shipment s
    WHERE s."organizationId" = $1
      AND s."customsStatus" IS NOT NULL
    GROUP BY s."customsStatus"
    ORDER BY count DESC
  `,
    [organizationId]
  );

  console.log("📈 Shipment Statistics by Customs Status:");
  console.log("==========================================");

  stats.forEach((stat) => {
    const recommendation = STATUS_RECOMMENDATIONS[stat.customsStatus];
    console.log(`\n🏷️  ${stat.customsStatus.toUpperCase()}: ${stat.count} shipments`);
    console.log(
      `   📅 Date Range: ${stat.oldest.toISOString().split("T")[0]} to ${stat.newest.toISOString().split("T")[0]}`
    );

    if (recommendation) {
      console.log(`   📝 ${recommendation.description}`);
      console.log(`   ✅ Good for: ${recommendation.goodFor.join(", ")}`);
      if (recommendation.avoid.length > 0) {
        console.log(`   ❌ Avoid: ${recommendation.avoid.join(", ")}`);
      }
    } else {
      console.log(`   ⚠️  No testing recommendations available`);
    }
  });

  return stats;
}

// Function to find shipments by status
async function findShipmentsByStatus(dataSource, organizationId, customsStatus, limit) {
  console.log(`🔍 Finding shipments with status: ${customsStatus}\n`);

  const shipments = await dataSource.query(
    `
    SELECT 
      s.id, 
      s."customsStatus", 
      s."hblNumber",
      s."modeOfTransport",
      s."createDate", 
      s."portCode", 
      s."subLocation",
      s."cargoControlNumber"
    FROM shipment s
    WHERE s."organizationId" = $1
      AND s."customsStatus" = $2
    ORDER BY s."createDate" DESC
    LIMIT $3
  `,
    [organizationId, customsStatus, limit]
  );

  if (shipments.length === 0) {
    console.log(`❌ No shipments found with status: ${customsStatus}`);
    return [];
  }

  console.log(`✅ Found ${shipments.length} shipments with status: ${customsStatus}`);
  console.log("=".repeat(60));

  shipments.forEach((shipment, index) => {
    console.log(`\n${index + 1}. Shipment ID: ${shipment.id}`);
    console.log(`   📋 HBL: ${shipment.hblNumber || "N/A"}`);
    console.log(`   🚛 Mode: ${shipment.modeOfTransport || "N/A"}`);
    console.log(`   🏢 Port: ${shipment.portCode || "N/A"}`);
    console.log(`   📍 SubLocation: ${shipment.subLocation || "N/A"}`);
    console.log(`   📦 CCN: ${shipment.cargoControlNumber || "N/A"}`);
    console.log(`   📅 Created: ${shipment.createDate.toISOString().split("T")[0]}`);

    // Show testing recommendations
    const recommendation = STATUS_RECOMMENDATIONS[shipment.customsStatus];
    if (recommendation) {
      console.log(`   🧪 Test with: ${recommendation.goodFor.slice(0, 3).join(", ")}`);
    }
  });

  return shipments;
}

// Function to find diverse shipments for comprehensive testing
async function findDiverseShipments(dataSource, organizationId, limit) {
  console.log(`🎯 Finding diverse shipments for comprehensive testing...\n`);

  // Get one shipment from each major status category
  const diverseShipments = await dataSource.query(
    `
    WITH ranked_shipments AS (
      SELECT 
        s.id, 
        s."customsStatus", 
        s."hblNumber",
        s."modeOfTransport",
        s."createDate",
        s."portCode", 
        s."subLocation",
        ROW_NUMBER() OVER (PARTITION BY s."customsStatus" ORDER BY s."createDate" DESC) as rn
      FROM shipment s
      WHERE s."organizationId" = $1
        AND s."customsStatus" IS NOT NULL
    )
    SELECT * FROM ranked_shipments 
    WHERE rn = 1
    ORDER BY "createDate" DESC
    LIMIT $2
  `,
    [organizationId, limit]
  );

  if (diverseShipments.length === 0) {
    console.log(`❌ No shipments found for organization ${organizationId}`);
    return [];
  }

  console.log(`✅ Found ${diverseShipments.length} diverse shipments for testing:`);
  console.log("=".repeat(70));

  diverseShipments.forEach((shipment, index) => {
    const recommendation = STATUS_RECOMMENDATIONS[shipment.customsStatus];

    console.log(`\n${index + 1}. Shipment ID: ${shipment.id} (${shipment.customsStatus})`);
    console.log(`   📋 HBL: ${shipment.hblNumber || "N/A"}`);
    console.log(`   🚛 Mode: ${shipment.modeOfTransport || "N/A"}`);
    console.log(`   📅 Created: ${shipment.createDate.toISOString().split("T")[0]}`);

    if (recommendation) {
      console.log(`   🧪 Best for: ${recommendation.goodFor.join(", ")}`);
      console.log(`   📝 ${recommendation.description}`);
    }
  });

  console.log(`\n💡 Testing Suggestions:`);
  console.log(`   # Test all intents with first shipment:`);
  console.log(
    `   node src/core-agent/testing/test-intent-handlers.js --shipment=${diverseShipments[0].id} --verbose`
  );
  console.log(`   `);
  console.log(`   # Test specific intent with suitable shipment:`);
  diverseShipments.slice(0, 3).forEach((shipment) => {
    const recommendation = STATUS_RECOMMENDATIONS[shipment.customsStatus];
    if (recommendation && recommendation.goodFor.length > 0) {
      console.log(
        `   node src/core-agent/testing/test-intent-handlers.js --shipment=${shipment.id} --intent=${recommendation.goodFor[0]}`
      );
    }
  });

  return diverseShipments;
}

// Main bootstrap function
async function bootstrap() {
  const config = parseCommandLineArguments(process.argv);
  let app;

  try {
    console.log("🔧 Initializing NestJS application context...");
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: false // Suppress startup logs for cleaner output
    });

    const dataSource = await app.get(DataSource);
    console.log("✅ NestJS context initialized\n");

    console.log("📝 Search Configuration:");
    console.log(`🏢 Organization ID: ${config.organizationId}`);
    console.log(`🏷️  Customs Status: ${config.customsStatus || "All"}`);
    console.log(`📊 Limit: ${config.limit}`);
    console.log(`📈 Analysis: ${config.analysis ? "Yes" : "No"}`);
    console.log("");

    if (config.analysis) {
      // Show detailed analysis
      await getShipmentStatistics(dataSource, parseInt(config.organizationId));
    } else if (config.customsStatus) {
      // Find shipments by specific status
      await findShipmentsByStatus(
        dataSource,
        parseInt(config.organizationId),
        config.customsStatus,
        config.limit
      );
    } else {
      // Find diverse shipments for comprehensive testing
      await findDiverseShipments(dataSource, parseInt(config.organizationId), config.limit);
    }

    console.log("\n🎉 Shipment search completed successfully!");
  } catch (error) {
    console.error(`💥 Search failed: ${error.message}`);
    process.exit(1);
  } finally {
    // Ensure app is always closed
    if (app) {
      try {
        await app.close();
      } catch (closeError) {
        console.error("Error closing app:", closeError.message);
      }
    }
    process.exit(0);
  }
}

// Show usage if no arguments provided
if (process.argv.length === 2) {
  console.log("Find Test Shipments Script Usage:");
  console.log("");
  console.log("Commands:");
  console.log("  --org=ID                   Organization ID (default: 3)");
  console.log("  --status=STATUS            Filter by customs status");
  console.log("  --limit=N                  Number of shipments (default: 10)");
  console.log("  --analysis                 Show detailed analysis");
  console.log("");
  console.log("Available Customs Statuses:");
  Object.keys(STATUS_RECOMMENDATIONS).forEach((status) => {
    console.log(`  - ${status}`);
  });
  console.log("");
  console.log("Examples:");
  console.log("  node scripts/find-test-shipments.js");
  console.log("  node scripts/find-test-shipments.js --analysis");
  console.log("  node scripts/find-test-shipments.js --status=released");
  console.log("  node scripts/find-test-shipments.js --org=1 --limit=5");
  process.exit(0);
}

bootstrap();
