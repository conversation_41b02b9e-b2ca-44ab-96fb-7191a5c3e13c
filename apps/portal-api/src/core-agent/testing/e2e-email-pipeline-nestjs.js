#!/usr/bin/env node

/**
 * E2E Email Pipeline Test Script (Simplified)
 *
 * This script runs a complete end-to-end test of the email processing pipeline
 * with sensible defaults for quick testing. Focuses on document-submission scenario
 * with demo organization and manual cleanup via controller endpoints.
 *
 * Usage:
 *   node scripts/e2e-email-pipeline-nestjs.js [options]
 *
 * Options:
 *   --org=ID                   Organization ID (default: 3 - demo org)
 *   --inbox-email=EMAIL        Inbox email address (default: <EMAIL>)
 *   --from-email=EMAIL         From email address (default: <EMAIL>)
 *   --subject=SUBJECT          Email subject (default: auto-generated with unique HBL)
 *   --body=BODY                Email body message (default: "Please process customs")
 *   --with-attachments         Include generated test attachments (default: no attachments)
 *   --no-attachments           Explicitly send email without attachments (same as default)
 *   --no-startup-cleanup       Skip checking for and cleaning up leftover test data (default: performs startup cleanup)
 *
 * Examples:
 *   # Quick test with all defaults (recommended - includes startup cleanup)
 *   node scripts/e2e-email-pipeline-nestjs.js
 *
 *   # Skip startup cleanup
 *   node scripts/e2e-email-pipeline-nestjs.js --no-startup-cleanup
 *
 *   # Custom inbox email
 *   node scripts/e2e-email-pipeline-nestjs.js --inbox-email=<EMAIL>
 *
 *   # Send email with generated test attachments
 *   node scripts/e2e-email-pipeline-nestjs.js --with-attachments
 *
 *   # No attachments (default behavior)
 *   node scripts/e2e-email-pipeline-nestjs.js --no-attachments
 */

const { AppModule } = require("../../../dist/app.module");
const { ContextIdFactory, NestFactory } = require("@nestjs/core");
const { TestEmailService } = require("../../../dist/email/test/services/test-email.service");
const { EmailDeletionService } = require("../../../dist/email/services/email-deletion.service");
const { DataSource } = require("typeorm");
const { useContainer } = require("class-validator");
const fs = require("fs");
const path = require("path");

// Default configuration - optimized for quick testing
const DEFAULTS = {
  organizationId: "3", // Demo organization (organization 3)
  scenario: "document-submission",
  inboxEmail: "<EMAIL>", // Updated default recipient email
  fromEmail: "<EMAIL>",
  fromName: "Sylvester Test Client",
  messageBody: "Please process customs.",
  subject: null, // Will be auto-generated with unique HBL
  testId: `e2e-test-${Date.now()}`
};

// Function to read file and convert to base64
function fileToBase64(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    const fileBuffer = fs.readFileSync(filePath);
    const base64String = fileBuffer.toString("base64");
    const fileName = path.basename(filePath);

    return {
      fileName: fileName,
      mimeType: "application/pdf",
      b64Data: base64String
    };
  } catch (error) {
    throw new Error(`Failed to process file ${filePath}: ${error.message}`);
  }
}

// Function to format duration
function formatDuration(ms) {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

// Function to monitor test progress using NestJS services
async function monitorTestProgress(testEmailService, emailDeletionService, testRunId, initialStatus) {
  console.log("\n⏳ Monitoring test progress...");
  console.log("💡 This may take up to 5 minutes for commercial invoice aggregation");
  console.log("📊 Checking status every 30 seconds...\n");

  const startTime = Date.now();
  let lastStatus = "";
  let checkCount = 0;

  while (true) {
    const elapsedMs = Date.now() - startTime;

    // Check for timeouts
    if (lastStatus === "saved" || (lastStatus === "" && initialStatus === "saved")) {
      if (elapsedMs > 2 * 60 * 1000) {
        // 2-minute timeout if stuck in 'saved' status
        console.log(`\n⏰ Timeout: Test did not start processing after 2 minutes (stuck in 'saved' status)`);
        return null;
      }
    } else if (elapsedMs > 5 * 60 * 1000) {
      // 5-minute timeout for any other in-progress status
      console.log(`\n⏰ Timeout: Test did not complete after 5 minutes`);
      return null;
    }

    try {
      checkCount++;
      const status = await testEmailService.getPipelineTestStatus(testRunId);

      // Only log if status changed or every 5th check
      if (status.currentStatus !== lastStatus || checkCount % 5 === 0) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(
          `[${timestamp}] 📊 Status: ${status.currentStatus} | Elapsed: ${formatDuration(elapsedMs)}`
        );
        lastStatus = status.currentStatus;
      }

      // Check if complete
      if (status.isComplete) {
        console.log("\n🎉 Test completed!");

        if (status.isSuccess) {
          console.log("✅ Processing successful!");

          if (status.createdEntities) {
            console.log("\n📊 Created Entities:");
            console.log(`   📧 Email Updates: ${status.createdEntities.emailUpdates}`);
            console.log(`   🚢 Shipments: ${status.createdEntities.shipments}`);
            console.log(`   📄 Commercial Invoices: ${status.createdEntities.commercialInvoices}`);
            console.log(`   📋 Documents: ${status.createdEntities.documents}`);
            console.log(`   📁 Files: ${status.createdEntities.files}`);
            console.log(`   📦 File Batches: ${status.createdEntities.fileBatches}`);
            console.log(`   🔗 Email Threads: ${status.createdEntities.emailThreads}`);
          }

          if (status.cleanupPerformed) {
            console.log("\n🧹 Cleanup completed - all test entities removed");
          } else {
            console.log("\n⚠️  Cleanup not performed - test entities remain in database");
          }
        } else {
          console.log("❌ Processing failed!");
          if (status.errorMessage) {
            console.log(`💥 Error: ${status.errorMessage}`);
          }
        }

        if (status.processingTimeMs) {
          console.log(`\n⏱️  Total Processing Time: ${formatDuration(status.processingTimeMs)}`);
        }

        return status;
      }

      // Wait 30 seconds before next check
      await new Promise((resolve) => setTimeout(resolve, 30000));
    } catch (error) {
      // Check if the error is "Test run not found" - this could mean cleanup was successful
      if (error.message.includes("not found")) {
        console.log(`📋 Test run record not found - likely cleaned up successfully`);
        console.log("🎉 Test completed with cleanup!");
        return {
          isComplete: true,
          isSuccess: true,
          cleanupPerformed: true,
          currentStatus: "cleaned-up",
          processingTimeMs: Date.now() - startTime
        };
      }

      console.log(`❌ Error checking status: ${error.message}`);
      console.log("🔄 Retrying in 30 seconds...");
      await new Promise((resolve) => setTimeout(resolve, 30000));
    }
  }
}

// Function to parse command line options and file paths
function parseCommandLineArguments(args) {
  const result = {
    organizationId: DEFAULTS.organizationId,
    scenario: DEFAULTS.scenario,
    inboxEmail: DEFAULTS.inboxEmail,
    fromEmail: DEFAULTS.fromEmail,
    fromName: DEFAULTS.fromName,
    subject: DEFAULTS.subject,
    messageBody: DEFAULTS.messageBody,
    attachments: [], // No attachments by default - use --with-attachments to enable
    startupCleanup: true // Default to true, can be disabled with --no-startup-cleanup
  };

  // Process all arguments starting from index 2 (simplified - no positional args)
  const remainingArgs = args.slice(2);

  for (let i = 0; i < remainingArgs.length; i++) {
    const arg = remainingArgs[i];

    if (arg.startsWith("--org=")) {
      result.organizationId = arg.split("=")[1];
    } else if (arg.startsWith("--inbox-email=")) {
      result.inboxEmail = arg.split("=")[1];
    } else if (arg.startsWith("--from-email=")) {
      result.fromEmail = arg.split("=")[1];
    } else if (arg.startsWith("--from-name=")) {
      result.fromName = arg.split("=")[1];
    } else if (arg.startsWith("--subject=")) {
      result.subject = arg.split("=")[1];
    } else if (arg.startsWith("--body=")) {
      result.messageBody = arg.split("=")[1];
    } else if (arg === "--no-attachments") {
      result.attachments = [];
    } else if (arg === "--with-attachments") {
      result.attachments = undefined; // Use scenario defaults
    } else if (arg === "--no-startup-cleanup") {
      result.startupCleanup = false;
    }
  }

  return result;
}

// Function to perform cleanup via service (direct service call - no HTTP required)
async function performE2ECleanup(emailId, testEmailService, emailDeletionService) {
  console.log(`🧹 Starting cleanup for email ${emailId} via direct service call...`);

  try {
    // Get deletion summary before cleanup for logging
    const deletionSummary = await emailDeletionService.getEmailDeletionSummary(emailId);

    if (!deletionSummary.emailExists) {
      return {
        success: false,
        message: `Email ${emailId} not found - may have already been cleaned up`,
        cleanupTimestamp: new Date().toISOString()
      };
    }

    console.log(`📊 Entities to be cleaned up:`);
    console.log(`   📧 Email Updates: ${deletionSummary.emailUpdates}`);
    console.log(`   🚢 Shipments: ${deletionSummary.shipments}`);
    console.log(`   📄 Commercial Invoices: ${deletionSummary.commercialInvoices}`);
    console.log(`   📋 Documents: ${deletionSummary.documents}`);
    console.log(`   📁 Files: ${deletionSummary.files}`);
    console.log(`   📦 File Batches: ${deletionSummary.fileBatches}`);
    console.log(`   🔗 Email Threads: ${deletionSummary.emailThreads}`);

    // Perform the actual cleanup using EmailDeletionService
    await emailDeletionService.deleteEmailCompletely(emailId);

    const cleanupTimestamp = new Date().toISOString();
    console.log(`✅ Successfully cleaned up email ${emailId} and all related entities`);

    return {
      success: true,
      message: `Email ${emailId} and all related entities have been successfully deleted`,
      cleanupTimestamp,
      deletionSummary
    };
  } catch (error) {
    console.log(`❌ Cleanup failed for email ${emailId}: ${error.message}`);
    return {
      success: false,
      message: `Cleanup failed: ${error.message}`,
      cleanupTimestamp: new Date().toISOString(),
      error: error.message
    };
  }
}

// Function to perform manual cleanup via TestEmailService (alternative approach)
async function performManualCleanup(testEmailService, emailId) {
  console.log(`🔄 Attempting manual cleanup via TestEmailService for email ${emailId}...`);

  try {
    const result = await testEmailService.cleanupEmail(emailId);
    console.log(`✅ Manual cleanup completed: ${result.message}`);
    return {
      success: true,
      message: result.message,
      deletionSummary: result.deletionSummary
    };
  } catch (error) {
    console.log(`❌ Manual cleanup failed: ${error.message}`);
    return {
      success: false,
      message: `Manual cleanup failed: ${error.message}`,
      error: error.message
    };
  }
}

// Function to check for and clean up leftover test data
async function performStartupCleanup(emailDeletionService, organizationId, dataSource) {
  console.log("🔍 Checking for leftover test data from previous runs...");

  try {
    // Query for emails with test-related patterns
    const testEmails = await dataSource.query(
      `
      SELECT e.id, e.subject, e."gmailId", e.status, e."createDate"
      FROM email e
      WHERE e."organizationId" = $1
        AND (
          e.subject LIKE '%(test)%' OR
          e.subject LIKE '%BL-%-%-test%' OR
          e.subject LIKE '%TEST-%-%-test%' OR
          e.subject LIKE '%TEST-%-%' OR
          e."gmailId" LIKE 'test-%' OR
          e.subject LIKE '%TEST%-%'
        )
      ORDER BY e."createDate" DESC
      LIMIT 20
    `,
      [organizationId]
    );

    if (testEmails.length === 0) {
      console.log("✅ No leftover test emails found");
      return true;
    }

    console.log(`⚠️  Found ${testEmails.length} potential test emails:`);
    testEmails.forEach((email, index) => {
      console.log(`   ${index + 1}. ID: ${email.id}, Subject: ${email.subject.substring(0, 50)}...`);
      console.log(`      Gmail ID: ${email.gmailId}, Status: ${email.status}`);
      console.log(`      Created: ${email.createDate}`);
    });

    console.log("\n🧹 Cleaning up test emails...");
    let cleanedCount = 0;
    let failedCount = 0;

    for (const email of testEmails) {
      try {
        console.log(`   Cleaning email ${email.id}...`);
        await emailDeletionService.deleteEmailCompletely(email.id);
        cleanedCount++;
      } catch (cleanupError) {
        console.log(`   ❌ Failed to cleanup email ${email.id}: ${cleanupError.message}`);
        failedCount++;
      }
    }

    console.log(`✅ Startup cleanup completed: ${cleanedCount} cleaned, ${failedCount} failed`);
    return true;
  } catch (error) {
    console.log(`⚠️  Startup cleanup check failed: ${error.message}`);
    return false;
  }
}

async function bootstrap() {
  console.log("🚀 E2E Email Pipeline Test (NestJS Context Version)");
  console.log("====================================================\n");

  // Parse command line arguments
  const config = parseCommandLineArguments(process.argv);

  console.log("📝 Test Configuration:");
  console.log(`🏢 Organization ID: ${config.organizationId}`);
  console.log(`🎯 Scenario: ${config.scenario}`);
  console.log(`📧 Inbox Email: ${config.inboxEmail}`);
  console.log(`👤 From Email: ${config.fromEmail}`);
  console.log(`👤 From Name: ${config.fromName}`);
  console.log(`📋 Subject: ${config.subject}`);
  console.log(`💬 Message Body: ${config.messageBody.substring(0, 50)}...`);
  console.log(`🆔 Test ID: ${DEFAULTS.testId}`);
  console.log(`🔍 Startup Cleanup: ${config.startupCleanup ? "Yes (default)" : "No (--no-startup-cleanup)"}`);

  // Show attachment info
  if (config.attachments === undefined) {
    console.log(`📎 Attachments: Using scenario defaults (--with-attachments specified)`);
  } else if (config.attachments.length === 0) {
    console.log(`📎 Attachments: None (default - use --with-attachments to enable)`);
  } else {
    console.log(`📎 Attachments: ${config.attachments.length} custom file(s)`);
    config.attachments.forEach((att, i) => {
      console.log(`   ${i + 1}. ${att.fileName}`);
    });
  }
  console.log("");

  let app; // Declare app variable outside try block for proper cleanup

  try {
    // Create NestJS application context
    console.log("🔧 Initializing NestJS application context...");
    app = await NestFactory.createApplicationContext(AppModule);
    globalApp = app; // Set global reference for cleanup handlers

    // CRITICAL: Configure class-validator to use NestJS's DI container
    // This must happen BEFORE any validation occurs
    console.log("🔧 Configuring class-validator dependency injection...");
    useContainer(app.select(AppModule), { fallbackOnErrors: true });

    // Wait for DataSource to be fully initialized BEFORE resolving services
    // Combined with class-validator DI configuration above, this ensures validators work properly
    console.log("⏳ Waiting for database connection to be ready...");
    const dataSource = await app.get(DataSource);
    if (!dataSource.isInitialized) {
      console.log("🔄 DataSource not ready, waiting for initialization...");
      // Wait up to 30 seconds for DataSource to initialize
      let attempts = 0;
      const maxAttempts = 60; // 30 seconds with 500ms intervals
      while (!dataSource.isInitialized && attempts < maxAttempts) {
        await new Promise((resolve) => setTimeout(resolve, 500));
        attempts++;
      }

      if (!dataSource.isInitialized) {
        throw new Error("DataSource failed to initialize within 30 seconds");
      }
    }
    console.log("✅ Database connection ready");

    // Now it's safe to create context and resolve services
    const contextId = ContextIdFactory.create();

    // Inject request context with organization
    app.registerRequestByContextId(
      {
        user: {
          permission: "backoffice-admin",
          organization: {
            id: parseInt(config.organizationId)
          }
        }
      },
      contextId
    );

    // Get services - this is now safe because DataSource is initialized AND class-validator is configured
    const testEmailService = await app.resolve(TestEmailService, contextId);
    const emailDeletionService = await app.resolve(EmailDeletionService, contextId);

    // Set global references for cleanup
    globalTestEmailService = testEmailService;
    globalEmailDeletionService = emailDeletionService;

    console.log("✅ NestJS context initialized\n");

    // Perform startup cleanup if requested
    if (config.startupCleanup) {
      await performStartupCleanup(emailDeletionService, parseInt(config.organizationId), dataSource);
      console.log("");
    }

    // Generate unique subject if not provided
    const finalSubject = config.subject || `E2E Test - Document Processing - ${DEFAULTS.testId}`;

    // Prepare test data
    const testData = {
      scenario: config.scenario,
      inboxEmail: config.inboxEmail,
      testId: DEFAULTS.testId,
      messageBody: config.messageBody,
      subject: finalSubject,
      from: [{ address: config.fromEmail, name: config.fromName }],
      attachments: config.attachments // undefined = scenario defaults, [] = no attachments, [...] = custom files
    };

    // Start the async e2e test
    console.log("🚀 Starting async e2e pipeline test...");
    const testResult = await testEmailService.startE2EPipelineTest(testData);

    // Set global references for cleanup
    globalTestRunId = testResult.testRunId;
    globalEmailId = testResult.emailId;

    console.log("✅ E2E test started successfully");
    console.log(`🆔 Test Run ID: ${testResult.testRunId}`);
    console.log(`📧 Email ID: ${testResult.emailId}`);
    console.log(`📊 Initial Status: ${testResult.currentStatus}`);

    // Monitor progress until completion
    const finalStatus = await monitorTestProgress(
      testEmailService,
      emailDeletionService,
      testResult.testRunId,
      testResult.currentStatus // Pass initial status for timeout logic
    );

    // Always attempt cleanup before closing, regardless of test outcome
    let cleanupResult = null;
    if (finalStatus) {
      console.log("\n🎉 E2E pipeline test completed!");

      // Perform cleanup via direct service call (no HTTP required)
      console.log("\n🧹 Performing cleanup via direct service call...");
      try {
        cleanupResult = await performE2ECleanup(testResult.emailId, testEmailService, emailDeletionService);
        if (cleanupResult.success) {
          console.log(`✅ Cleanup completed: ${cleanupResult.message}`);
          console.log(`🗑️  Cleanup timestamp: ${cleanupResult.cleanupTimestamp}`);
        } else {
          console.log(`⚠️  Cleanup failed: ${cleanupResult.message}`);
          console.log("💡 Attempting fallback cleanup via TestEmailService...");

          // Try fallback cleanup via TestEmailService
          const fallbackResult = await performManualCleanup(testEmailService, testResult.emailId);
          if (fallbackResult.success) {
            console.log(`✅ Fallback cleanup completed: ${fallbackResult.message}`);
            cleanupResult = fallbackResult;
          } else {
            console.log(`⚠️  Fallback cleanup also failed: ${fallbackResult.message}`);
            cleanupResult = fallbackResult;
          }
        }
      } catch (cleanupError) {
        console.log(`⚠️  Cleanup error: ${cleanupError.message}`);
        console.log("💡 Attempting fallback cleanup via TestEmailService...");
        try {
          const fallbackResult = await performManualCleanup(testEmailService, testResult.emailId);
          console.log(`✅ Fallback cleanup completed: ${fallbackResult.message}`);
          cleanupResult = fallbackResult;
        } catch (fallbackError) {
          console.log(`⚠️  All cleanup methods failed: ${fallbackError.message}`);
          cleanupResult = { success: false, message: fallbackError.message };
        }
      }

      console.log("\n💡 Tips:");
      console.log("- Check portal-api logs for detailed processing information");
      console.log("- Test results are preserved in the EmailTestRun table");
      console.log("- Use the testRunId to reference this test run later");
      console.log(`- Test Run ID: ${testResult.testRunId}`);
    } else {
      console.log("\n⏰ Test monitoring timed out, but test may still be running in background");
      console.log(`- Use check-e2e-test-status.js with testRunId: ${testResult.testRunId}`);

      // Still attempt cleanup even on timeout
      console.log("\n🧹 Attempting cleanup despite timeout...");
      try {
        cleanupResult = await performE2ECleanup(testResult.emailId, testEmailService, emailDeletionService);
        if (cleanupResult.success) {
          console.log(`✅ Cleanup completed: ${cleanupResult.message}`);
        } else {
          console.log(`⚠️  Cleanup failed: ${cleanupResult.message}`);
          // Try fallback cleanup
          const fallbackResult = await performManualCleanup(testEmailService, testResult.emailId);
          if (fallbackResult.success) {
            console.log(`✅ Fallback cleanup completed: ${fallbackResult.message}`);
            cleanupResult = fallbackResult;
          }
        }
      } catch (cleanupError) {
        console.log(`⚠️  Cleanup error: ${cleanupError.message}`);
        cleanupResult = { success: false, message: cleanupError.message };
      }
    }

    console.log("\n🔄 Closing application gracefully...");
    await app.close();
    console.log("✅ Application closed successfully");
    process.exit(0);
  } catch (error) {
    console.error(`\n❌ Error: ${error.message}`);
    console.error(error.stack);

    // Perform cleanup on error
    await performCleanup("error");
    process.exit(1);
  }
}

// Global references for cleanup
let globalApp = null;
let globalTestEmailService = null;
let globalEmailDeletionService = null;
let globalTestRunId = null;
let globalEmailId = null;

// Enhanced cleanup function
async function performCleanup(reason = "interruption") {
  console.log(`\n🧹 Performing cleanup due to ${reason}...`);

  try {
    // Try to cleanup the test email using direct service calls
    if (globalEmailId && (globalTestEmailService || globalEmailDeletionService)) {
      console.log(`🗑️  Attempting to cleanup test email ${globalEmailId} via direct service...`);

      try {
        // Try primary cleanup via EmailDeletionService first
        if (globalEmailDeletionService) {
          const cleanupResult = await performE2ECleanup(
            globalEmailId,
            globalTestEmailService,
            globalEmailDeletionService
          );

          if (cleanupResult.success) {
            console.log(`✅ Successfully cleaned up test email ${globalEmailId}`);
          } else {
            console.log(`⚠️  Primary cleanup failed: ${cleanupResult.message}`);
            // Try fallback via TestEmailService
            if (globalTestEmailService) {
              console.log("🔄 Attempting fallback cleanup via TestEmailService...");
              const fallbackResult = await performManualCleanup(globalTestEmailService, globalEmailId);
              if (fallbackResult.success) {
                console.log(`✅ Fallback cleanup successful for email ${globalEmailId}`);
              } else {
                console.log(`⚠️  Fallback cleanup also failed: ${fallbackResult.message}`);
              }
            }
          }
        } else if (globalTestEmailService) {
          // Only TestEmailService available
          const cleanupResult = await performManualCleanup(globalTestEmailService, globalEmailId);
          if (cleanupResult.success) {
            console.log(`✅ Successfully cleaned up test email ${globalEmailId}`);
          } else {
            console.log(`⚠️  Cleanup failed: ${cleanupResult.message}`);
          }
        }
      } catch (cleanupError) {
        console.log(`⚠️  Cleanup error: ${cleanupError.message}`);
        console.log("💡 Test email may need to be manually cleaned up later");
      }
    } else if (globalEmailId) {
      console.log(`⚠️  Email ID ${globalEmailId} found but no cleanup services available`);
      console.log("💡 Test email may need to be manually cleaned up later");
    }

    // Close the NestJS application
    if (globalApp) {
      console.log("🔄 Closing application gracefully...");
      await globalApp.close();
      console.log("✅ Application closed successfully");
    }
  } catch (error) {
    console.error(`❌ Error during cleanup: ${error.message}`);
  }
}

// Handle process termination signals
process.on("SIGINT", async () => {
  console.log("\n\n🛑 Received SIGINT (Ctrl+C) - Initiating cleanup...");
  await performCleanup("SIGINT");
  console.log("👋 Cleanup completed. Exiting...");
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("\n\n🛑 Received SIGTERM - Initiating cleanup...");
  await performCleanup("SIGTERM");
  console.log("👋 Cleanup completed. Exiting...");
  process.exit(0);
});

// Run the script
bootstrap().catch(async (error) => {
  console.error(`\n💥 Unexpected error: ${error.message}`);
  await performCleanup("unexpected error");
  process.exit(1);
});
