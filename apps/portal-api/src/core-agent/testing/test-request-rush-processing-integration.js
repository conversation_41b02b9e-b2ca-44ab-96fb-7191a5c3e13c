#!/usr/bin/env node

/**
 * REQUEST_RUSH_PROCESSING Integration Test Script
 *
 * This script provides comprehensive integration testing for the REQUEST_RUSH_PROCESSING intent handler.
 * It covers null cases, transaction-runner injection, and compliance error paths.
 *
 * Usage:
 *   node src/core-agent/testing/test-request-rush-processing-integration.js [options]
 *
 * Options:
 *   --org=ID                   Organization ID (default: 3 - demo org)
 *   --shipment=ID              Specific shipment ID to test with
 *   --no-side-effects          Skip side effects like backoffice emails and document generation
 *   --verbose                  Show detailed logging
 *   --test-null-cases          Test null/undefined scenarios
 *   --test-compliance-errors   Test compliance error scenarios
 *   --test-transaction-runner  Test transaction runner injection
 *
 * Examples:
 *   # Run all integration tests
 *   node src/core-agent/testing/test-request-rush-processing-integration.js
 *
 *   # Test null cases specifically
 *   node src/core-agent/testing/test-request-rush-processing-integration.js --test-null-cases
 *
 *   # Test with specific shipment and verbose output
 *   node src/core-agent/testing/test-request-rush-processing-integration.js --shipment=123 --verbose
 */

const { AppModule } = require("../../../dist/app.module");
const { ContextIdFactory, NestFactory } = require("@nestjs/core");
const { DataSource } = require("typeorm");
const { generateRequest } = require("../../../dist/email/utils/generate-request");

// Import services and types
const { ShipmentContextService } = require("../../../dist/agent-context");
const {
  IntentHandlerRegistry
} = require("../../../dist/core-agent/services/intent-handler-registry.service");
const { ShipmentResponseService } = require("../../../dist/core-agent/services/shipment-response.service");

// Entity imports
const { Organization, Shipment, CustomsStatus, FIND_ORGANIZATION_RELATIONS } = require("nest-modules");

// Default configuration
const DEFAULTS = {
  organizationId: "3", // Demo organization
  verbose: false,
  sideEffects: true,
  testNullCases: false,
  testComplianceErrors: false,
  testTransactionRunner: false
};

// Test scenarios for REQUEST_RUSH_PROCESSING
const TEST_SCENARIOS = {
  NORMAL_RUSH_REQUEST: {
    name: "Normal Rush Request",
    description: "Standard rush processing request with compliant shipment",
    intent: "REQUEST_RUSH_PROCESSING",
    instructions: ["Can you rush this shipment?", "This is urgent, please expedite"],
    expectedFragments: 2,
    expectedTemplate: "core-agent/fragments/document-requests/rush-processing-response"
  },

  NULL_SHIPMENT: {
    name: "Null Shipment Case",
    description: "Rush request with null shipment in context",
    intent: "REQUEST_RUSH_PROCESSING",
    instructions: ["Rush processing needed"],
    mockNullShipment: true,
    expectedFragments: 2,
    expectedTemplate: "core-agent/fragments/document-requests/rush-processing-response"
  },

  CANNOT_RUSH: {
    name: "Cannot Rush Case",
    description: "Shipment that cannot be rushed due to business rules",
    intent: "REQUEST_RUSH_PROCESSING",
    instructions: ["Please expedite this shipment"],
    mockCannotRush: true,
    expectedFragments: 2,
    expectedTemplate: "core-agent/fragments/document-requests/rush-processing-response"
  },

  ALREADY_SUBMITTED: {
    name: "Already Submitted Case",
    description: "Shipment that has already been submitted",
    intent: "REQUEST_RUSH_PROCESSING",
    instructions: ["Can you rush this?"],
    mockAlreadySubmitted: true,
    expectedFragments: 2,
    expectedTemplate: "core-agent/fragments/document-requests/rush-processing-response"
  },

  COMPLIANCE_ERRORS: {
    name: "Compliance Errors Case",
    description: "Shipment with compliance errors preventing rush processing",
    intent: "REQUEST_RUSH_PROCESSING",
    instructions: ["Rush this shipment please"],
    mockComplianceErrors: true,
    expectedFragments: 2,
    expectedTemplate: "core-agent/fragments/document-requests/rush-processing-response"
  },

  SUBMISSION_ERROR: {
    name: "Submission Error Case",
    description: "Submission service throws error during rush processing",
    intent: "REQUEST_RUSH_PROCESSING",
    instructions: ["Urgent rush processing needed"],
    mockSubmissionError: true,
    expectedFragments: 2,
    expectedTemplate: "core-agent/fragments/document-requests/rush-processing-response"
  },

  MISSING_ENTRY_SERVICE: {
    name: "Missing Entry Service Case",
    description: "Entry submission service not available in context",
    intent: "REQUEST_RUSH_PROCESSING",
    instructions: ["Please rush this shipment"],
    mockMissingEntryService: true,
    expectedFragments: 2,
    expectedTemplate: "core-agent/fragments/document-requests/rush-processing-response"
  },

  TRANSACTION_RUNNER: {
    name: "Transaction Runner Case",
    description: "Rush processing with transaction runner injection",
    intent: "REQUEST_RUSH_PROCESSING",
    instructions: ["Rush processing with transaction"],
    mockTransactionRunner: true,
    expectedFragments: 2,
    expectedTemplate: "core-agent/fragments/document-requests/rush-processing-response"
  }
};

// Function to parse command line arguments
function parseCommandLineArguments(args) {
  const result = {
    organizationId: DEFAULTS.organizationId,
    shipmentId: null,
    sideEffects: DEFAULTS.sideEffects,
    verbose: DEFAULTS.verbose,
    testNullCases: DEFAULTS.testNullCases,
    testComplianceErrors: DEFAULTS.testComplianceErrors,
    testTransactionRunner: DEFAULTS.testTransactionRunner
  };

  const remainingArgs = args.slice(2);

  for (let i = 0; i < remainingArgs.length; i++) {
    const arg = remainingArgs[i];

    if (arg.startsWith("--org=")) {
      result.organizationId = arg.split("=")[1];
    } else if (arg.startsWith("--shipment=")) {
      result.shipmentId = parseInt(arg.split("=")[1]);
    } else if (arg === "--no-side-effects") {
      result.sideEffects = false;
    } else if (arg === "--verbose") {
      result.verbose = true;
    } else if (arg === "--test-null-cases") {
      result.testNullCases = true;
    } else if (arg === "--test-compliance-errors") {
      result.testComplianceErrors = true;
    } else if (arg === "--test-transaction-runner") {
      result.testTransactionRunner = true;
    }
  }

  return result;
}

// Function to find suitable test shipments
async function findTestShipments(dataSource, organizationId, limit = 3) {
  console.log(`🔍 Finding suitable shipments for organization ${organizationId}...`);

  const shipments = await dataSource.query(
    `
    SELECT s.id, s."customsStatus", s."hblNumber", s."modeOfTransport",
           s."createDate", s."portCode", s."subLocation", s."organizationId"
    FROM shipment s
    WHERE s."organizationId" = $1
      AND s."customsStatus" IS NOT NULL
      AND s."customsStatus" != 'SUBMITTED'
    ORDER BY s."createDate" DESC
    LIMIT $2
  `,
    [organizationId, limit]
  );

  console.log(`✅ Found ${shipments.length} suitable shipments for testing`);
  return shipments;
}

// Function to mock context based on test scenario
function mockContextForScenario(baseContext, scenario) {
  const mockedContext = { ...baseContext };

  if (scenario.mockNullShipment) {
    mockedContext.shipment = null;
    mockedContext.canRush = false;
    console.log(`🔧 Mocked null shipment for scenario: ${scenario.name}`);
  }

  if (scenario.mockCannotRush) {
    mockedContext.canRush = false;
    console.log(`🔧 Mocked canRush = false for scenario: ${scenario.name}`);
  }

  if (scenario.mockAlreadySubmitted) {
    mockedContext.isSubmitted = true;
    console.log(`🔧 Mocked isSubmitted = true for scenario: ${scenario.name}`);
  }

  if (scenario.mockComplianceErrors) {
    mockedContext.compliance = {
      ...mockedContext.compliance,
      isCompliant: false,
      errors: [
        "Missing commercial invoice",
        "Invalid HS code format",
        "Incomplete trade partner information"
      ],
      warnings: ["Port code may be incorrect"],
      missingFields: ["invoiceNumber", "hsCode", "tradePartner.address"]
    };
    console.log(`🔧 Mocked compliance errors for scenario: ${scenario.name}`);
  }

  if (scenario.mockSubmissionError) {
    // We'll need to mock the submission service to throw an error
    const originalSubmissionService = mockedContext._services.entrySubmissionService;
    mockedContext._services.entrySubmissionService = {
      ...originalSubmissionService,
      attemptShipmentSubmission: async () => {
        throw new Error("Submission failed: Invalid document format");
      }
    };
    console.log(`🔧 Mocked submission error for scenario: ${scenario.name}`);
  }

  if (scenario.mockMissingEntryService) {
    mockedContext._services.entrySubmissionService = null;
    console.log(`🔧 Mocked missing entry service for scenario: ${scenario.name}`);
  }

  if (scenario.mockTransactionRunner) {
    // Mock a transaction runner
    mockedContext.queryRunner = {
      manager: {},
      connection: {},
      isTransactionActive: true,
      commitTransaction: async () => {},
      rollbackTransaction: async () => {},
      release: async () => {},
      startTransaction: async () => {},
      connect: async () => {},
      query: async () => {}
    };
    console.log(`🔧 Mocked transaction runner for scenario: ${scenario.name}`);
  }

  return mockedContext;
}

// Function to test a single scenario
async function testRushProcessingScenario(
  scenario,
  baseContext,
  intentHandlerRegistry,
  shipmentResponseService,
  config
) {
  console.log(`\n🧪 Testing Scenario: ${scenario.name}`);
  console.log(`📝 Description: ${scenario.description}`);
  console.log(`📋 Instructions: ${scenario.instructions.join(", ")}`);

  try {
    // Create validated intent object
    const validatedIntent = {
      intent: scenario.intent,
      instructions: scenario.instructions,
      attachments: []
    };

    // Get the handler
    const handler = intentHandlerRegistry.getHandler(scenario.intent);
    if (!handler) {
      console.log(`❌ No handler found for intent: ${scenario.intent}`);
      return { success: false, error: "Handler not found" };
    }

    // Mock context for this specific scenario
    const testContext = mockContextForScenario(baseContext, scenario);

    // Handle the intent
    console.log(`⚙️  Processing intent with handler...`);
    const startTime = Date.now();

    const fragments = await handler.handle(validatedIntent, testContext);
    const processingTime = Date.now() - startTime;

    console.log(`✅ Handler completed in ${processingTime}ms`);
    console.log(`📊 Generated ${fragments.length} response fragments`);

    // Validate expected results
    if (fragments.length !== scenario.expectedFragments) {
      console.log(`⚠️  Expected ${scenario.expectedFragments} fragments, got ${fragments.length}`);
    }

    const mainFragment = fragments[0];
    if (mainFragment.template !== scenario.expectedTemplate) {
      console.log(`⚠️  Expected template ${scenario.expectedTemplate}, got ${mainFragment.template}`);
    }

    // Show fragment details
    if (config.verbose && fragments.length > 0) {
      console.log(`📋 Fragment Details:`);
      fragments.forEach((fragment, index) => {
        console.log(`   ${index + 1}. Template: ${fragment.template}, Priority: ${fragment.priority}`);
        if (fragment.fragmentContext) {
          const contextKeys = Object.keys(fragment.fragmentContext);
          console.log(`      Context Keys: ${contextKeys.join(", ")}`);

          // Show specific context values for debugging
          if (fragment.fragmentContext.submissionError) {
            console.log(`      Submission Error: ${fragment.fragmentContext.submissionError}`);
          }
          if (fragment.fragmentContext.submissionResult) {
            console.log(
              `      Submission Result: ${JSON.stringify(fragment.fragmentContext.submissionResult, null, 2)}`
            );
          }
          if (fragment.fragmentContext.backofficeAlerts) {
            console.log(
              `      Backoffice Alerts: ${JSON.stringify(fragment.fragmentContext.backofficeAlerts)}`
            );
          }
        }
      });
    }

    // Render fragments to HTML
    console.log(`🎨 Rendering fragments to HTML...`);
    const responseHtml = await shipmentResponseService.renderFragments(fragments, testContext);

    // Show rendered response (truncated)
    if (responseHtml && responseHtml.trim().length > 0) {
      const truncatedHtml = responseHtml.replace(/<[^>]*>/g, "").substring(0, 300);
      console.log(`📧 Response Preview: ${truncatedHtml}...`);
    } else {
      console.log(`⚠️  No response HTML generated`);
    }

    // Check for specific scenario validations
    const validationResults = validateScenarioResults(scenario, fragments, testContext);
    if (validationResults.length > 0) {
      console.log(`✅ Scenario Validations:`);
      validationResults.forEach((result) => console.log(`   - ${result}`));
    }

    return {
      success: true,
      fragments: fragments.length,
      processingTime,
      hasResponse: responseHtml && responseHtml.trim().length > 0,
      fragmentContext: mainFragment.fragmentContext,
      validations: validationResults
    };
  } catch (error) {
    console.log(`❌ Error testing scenario ${scenario.name}: ${error.message}`);
    if (config.verbose) {
      console.log(`💥 Stack trace: ${error.stack}`);
    }
    return { success: false, error: error.message };
  }
}

// Function to validate scenario-specific results
function validateScenarioResults(scenario, fragments, context) {
  const validations = [];
  const mainFragment = fragments[0];
  const fragmentContext = mainFragment.fragmentContext || {};

  switch (scenario.name) {
    case "Normal Rush Request":
      if (fragmentContext.submissionResult) {
        validations.push("Submission attempted successfully");
      }
      if (fragmentContext.backofficeAlerts?.rushProcessingSent) {
        validations.push("Backoffice alert sent");
      }
      break;

    case "Null Shipment Case":
      if (!fragmentContext.submissionResult && !fragmentContext.submissionError) {
        validations.push("No submission attempted for null shipment");
      }
      break;

    case "Cannot Rush Case":
      if (!fragmentContext.submissionResult) {
        validations.push("No submission attempted when canRush=false");
      }
      break;

    case "Already Submitted Case":
      if (!fragmentContext.submissionResult) {
        validations.push("No submission attempted when already submitted");
      }
      break;

    case "Compliance Errors Case":
      if (fragmentContext.complianceDetails && !fragmentContext.complianceDetails.isCompliant) {
        validations.push("Compliance errors properly captured");
      }
      break;

    case "Submission Error Case":
      if (fragmentContext.submissionError) {
        validations.push("Submission error properly captured");
      }
      break;

    case "Missing Entry Service Case":
      if (fragmentContext.submissionError === "Submission service not available") {
        validations.push("Missing service error properly handled");
      }
      break;

    case "Transaction Runner Case":
      if (context.queryRunner) {
        validations.push("Transaction runner properly injected");
      }
      break;
  }

  return validations;
}

// Function to run all tests
async function runAllTests(config, app, dataSource) {
  console.log("🚀 Starting REQUEST_RUSH_PROCESSING Integration Tests");
  console.log("===================================================\n");

  console.log("📝 Test Configuration:");
  console.log(`🏢 Organization ID: ${config.organizationId}`);
  console.log(`🚢 Shipment ID: ${config.shipmentId || "Auto-select"}`);
  console.log(`🔧 Side Effects: ${config.sideEffects ? "Enabled" : "Disabled"}`);
  console.log(`📊 Verbose: ${config.verbose ? "Yes" : "No"}`);
  console.log(`🧪 Test Null Cases: ${config.testNullCases ? "Yes" : "No"}`);
  console.log(`🧪 Test Compliance Errors: ${config.testComplianceErrors ? "Yes" : "No"}`);
  console.log(`🧪 Test Transaction Runner: ${config.testTransactionRunner ? "Yes" : "No"}`);
  console.log("");

  try {
    // 1. Load organization
    const organization = await dataSource.manager.findOne(Organization, {
      where: { id: parseInt(config.organizationId) },
      relations: FIND_ORGANIZATION_RELATIONS
    });
    if (!organization) {
      throw new Error(`Organization not found: ${config.organizationId}`);
    }
    console.log(`✅ Loaded organization: ${organization.name}`);

    // 2. Create request context
    const contextId = ContextIdFactory.create();
    const requestContext = generateRequest(null, organization);
    app.registerRequestByContextId(requestContext, contextId);

    // 3. Get services
    const shipmentContextService = await app.resolve(ShipmentContextService, contextId);
    const intentHandlerRegistry = await app.resolve(IntentHandlerRegistry, contextId);
    const shipmentResponseService = await app.resolve(ShipmentResponseService, contextId);

    console.log(`✅ Resolved core-agent services`);

    // 4. Find or use specified shipment
    let shipment;
    if (config.shipmentId) {
      shipment = await dataSource.manager.findOne(Shipment, {
        where: { id: config.shipmentId, organizationId: parseInt(config.organizationId) }
      });
      if (!shipment) {
        throw new Error(`Shipment not found: ${config.shipmentId}`);
      }
      console.log(`✅ Using specified shipment: ${shipment.id}`);
    } else {
      const shipments = await findTestShipments(dataSource, parseInt(config.organizationId), 1);
      if (shipments.length === 0) {
        throw new Error(`No suitable shipments found for organization ${config.organizationId}`);
      }
      shipment = shipments[0];
      console.log(`✅ Auto-selected shipment: ${shipment.id}`);
    }

    // 5. Build base shipment context
    console.log(`🔧 Building base shipment context...`);
    const baseContext = await shipmentContextService.buildContext(
      shipment.id,
      parseInt(config.organizationId)
    );
    console.log(`✅ Built base context for shipment ${shipment.id}`);

    // 6. Determine which tests to run
    let scenariosToTest = [];

    if (config.testNullCases) {
      scenariosToTest.push("NULL_SHIPMENT", "CANNOT_RUSH", "ALREADY_SUBMITTED", "MISSING_ENTRY_SERVICE");
    }

    if (config.testComplianceErrors) {
      scenariosToTest.push("COMPLIANCE_ERRORS", "SUBMISSION_ERROR");
    }

    if (config.testTransactionRunner) {
      scenariosToTest.push("TRANSACTION_RUNNER");
    }

    // If no specific tests selected, run all scenarios
    if (scenariosToTest.length === 0) {
      scenariosToTest = Object.keys(TEST_SCENARIOS);
    }

    console.log(`\n🧪 Running ${scenariosToTest.length} test scenarios...\n`);

    // 7. Run tests
    const testResults = {};

    for (const scenarioKey of scenariosToTest) {
      const scenario = TEST_SCENARIOS[scenarioKey];
      if (!scenario) {
        console.log(`⚠️  Unknown scenario: ${scenarioKey}`);
        continue;
      }

      const result = await testRushProcessingScenario(
        scenario,
        baseContext,
        intentHandlerRegistry,
        shipmentResponseService,
        config
      );

      testResults[scenarioKey] = result;
    }

    // 8. Show summary
    console.log(`\n📊 Test Results Summary`);
    console.log("======================");

    let successCount = 0;
    let totalFragments = 0;
    let totalProcessingTime = 0;

    Object.entries(testResults).forEach(([scenarioKey, result]) => {
      const status = result.success ? "✅ PASS" : "❌ FAIL";
      const scenario = TEST_SCENARIOS[scenarioKey];
      console.log(`${status} ${scenario.name}`);

      if (result.success) {
        successCount++;
        totalFragments += result.fragments || 0;
        totalProcessingTime += result.processingTime || 0;

        if (config.verbose) {
          console.log(`     Fragments: ${result.fragments}, Time: ${result.processingTime}ms`);
          console.log(`     Response: ${result.hasResponse ? "Yes" : "No"}`);
          if (result.validations && result.validations.length > 0) {
            console.log(`     Validations: ${result.validations.join(", ")}`);
          }
        }
      } else {
        console.log(`     Error: ${result.error}`);
      }
    });

    console.log(`\n📈 Overall Results:`);
    console.log(`   Tests Passed: ${successCount}/${Object.keys(testResults).length}`);
    console.log(`   Total Fragments: ${totalFragments}`);
    console.log(`   Total Processing Time: ${totalProcessingTime}ms`);
    console.log(`   Average Time per Test: ${Math.round(totalProcessingTime / successCount)}ms`);

    return testResults;
  } catch (error) {
    console.error(`💥 Test execution failed: ${error.message}`);
    if (config.verbose) {
      console.error(`Stack trace: ${error.stack}`);
    }
    throw error;
  }
}

// Main bootstrap function
async function bootstrap() {
  const config = parseCommandLineArguments(process.argv);
  let app;

  try {
    // Create NestJS application context
    console.log("🔧 Initializing NestJS application context...");
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: config.verbose ? ["log", "error", "warn", "debug"] : ["error"]
    });

    const dataSource = await app.get(DataSource);
    console.log("✅ NestJS context initialized\n");

    // Run all tests
    await runAllTests(config, app, dataSource);

    console.log("\n🎉 REQUEST_RUSH_PROCESSING integration testing completed successfully!");
  } catch (error) {
    console.error(`💥 Bootstrap failed: ${error.message}`);
    process.exit(1);
  } finally {
    // Ensure app is always closed
    if (app) {
      try {
        await app.close();
      } catch (closeError) {
        console.error("Error closing app:", closeError.message);
      }
    }
    process.exit(0);
  }
}

// Handle process termination gracefully
process.on("SIGINT", () => {
  console.log("\nReceived SIGINT, shutting down gracefully...");
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("\nReceived SIGTERM, shutting down gracefully...");
  process.exit(0);
});

// Show usage if no arguments provided
if (process.argv.length === 2) {
  console.log("REQUEST_RUSH_PROCESSING Integration Test Usage:");
  console.log("");
  console.log("Commands:");
  console.log("  --org=ID                   Organization ID (default: 3)");
  console.log("  --shipment=ID              Specific shipment ID to test with");
  console.log("  --no-side-effects          Skip side effects");
  console.log("  --verbose                  Show detailed logging");
  console.log("  --test-null-cases          Test null/undefined scenarios");
  console.log("  --test-compliance-errors   Test compliance error scenarios");
  console.log("  --test-transaction-runner  Test transaction runner injection");
  console.log("");
  console.log("Test Scenarios:");
  Object.entries(TEST_SCENARIOS).forEach(([key, scenario]) => {
    console.log(`  - ${scenario.name}: ${scenario.description}`);
  });
  console.log("");
  console.log("Examples:");
  console.log("  node src/core-agent/testing/test-request-rush-processing-integration.js");
  console.log("  node src/core-agent/testing/test-request-rush-processing-integration.js --test-null-cases");
  console.log(
    "  node src/core-agent/testing/test-request-rush-processing-integration.js --shipment=123 --verbose"
  );
  process.exit(0);
}

bootstrap();
