const { AppModule } = require("../../../dist/app.module");
const { NestFactory } = require("@nestjs/core");
const { DataSource } = require("typeorm");
const { getRepositoryToken } = require("@nestjs/typeorm");
const { Email, EmailUpdate, Shipment, Organization } = require("nest-modules");

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule, {
    logger: false // Suppress NestJS startup logs for cleaner output
  });
  const dataSource = await app.get(DataSource);

  const command = process.argv[2];
  const param1 = process.argv[3];

  try {
    switch (command) {
      case "email":
        await queryEmail(dataSource, param1);
        break;
      case "pipeline":
        await queryEmailPipeline(dataSource, param1);
        break;
      case "recent":
        await queryRecentEmails(dataSource, param1 || 10);
        break;
      case "orgs":
        await queryOrganizations(dataSource);
        break;
      case "sql":
        await executeSQL(dataSource, param1);
        break;
      default:
        showUsage();
    }
  } catch (error) {
    console.error("Error:", error.message);
  } finally {
    // Ensure app is always closed
    try {
      await app.close();
    } catch (closeError) {
      console.error("Error closing app:", closeError.message);
    }
    // Force exit to ensure process terminates
    process.exit(0);
  }
}

async function queryEmail(dataSource, emailId) {
  if (!emailId) {
    console.log("Usage: node scripts/db-query.js email <emailId>");
    return;
  }

  const result = await dataSource.query(
    `
    SELECT e.*, o.name as org_name 
    FROM email e 
    JOIN organization o ON e."organizationId" = o.id 
    WHERE e.id = $1
  `,
    [emailId]
  );

  if (result.length === 0) {
    console.log("Email not found");
    return;
  }

  const email = result[0];
  console.log(`Email ${email.id}:`);
  console.log(`  Subject: ${email.subject}`);
  console.log(`  Status: ${email.status}`);
  console.log(`  Organization: ${email.org_name}`);
  console.log(`  Created: ${email.createDate}`);
}

async function queryEmailPipeline(dataSource, emailId) {
  if (!emailId) {
    console.log("Usage: node scripts/db-query.js pipeline <emailId>");
    return;
  }

  const updates = await dataSource.query(
    `
    SELECT eu.*, e.subject, e.status as email_status
    FROM email_update eu
    JOIN email e ON eu."emailId" = e.id
    WHERE eu."emailId" = $1
    ORDER BY eu."createDate" ASC
  `,
    [emailId]
  );

  if (updates.length === 0) {
    console.log("No email updates found");
    return;
  }

  console.log(`Pipeline for Email ${emailId} (${updates[0].subject}):`);
  console.log(`Current Status: ${updates[0].email_status}\n`);

  updates.forEach((update, index) => {
    console.log(`${index + 1}. ${update.createDate} - ${update.status}`);
    if (update.intent) console.log(`   Intent: ${update.intent}`);
  });
}

async function queryRecentEmails(dataSource, limit) {
  const emails = await dataSource.query(
    `
    SELECT e.id, e.subject, e.status, e."createDate", o.name as org_name
    FROM email e
    JOIN organization o ON e."organizationId" = o.id
    ORDER BY e."createDate" DESC
    LIMIT $1
  `,
    [limit]
  );

  console.log(`Recent ${limit} emails:`);
  emails.forEach((email, index) => {
    console.log(`${index + 1}. ID: ${email.id}, Status: ${email.status}, Org: ${email.org_name}`);
    console.log(`   Subject: ${email.subject.substring(0, 60)}...`);
    console.log(`   Created: ${email.createDate}`);
    console.log("");
  });
}

async function queryOrganizations(dataSource) {
  const orgs = await dataSource.query(`
    SELECT id, name, "organizationType" 
    FROM organization 
    ORDER BY name
  `);

  console.log("Organizations:");
  orgs.forEach((org) => {
    console.log(`  ${org.id}: ${org.name} (${org.organizationType})`);
  });
}

async function executeSQL(dataSource, query) {
  if (!query) {
    console.log('Usage: node scripts/db-query.js sql "SELECT COUNT(*) FROM email"');
    return;
  }

  try {
    const result = await dataSource.query(query);
    console.log("Result:", JSON.stringify(result, null, 2));
  } catch (error) {
    console.error("SQL Error:", error.message);
  }
}

function showUsage() {
  console.log("Database Query Helper Usage:");
  console.log("");
  console.log("Commands:");
  console.log("  email <id>       - Get email details");
  console.log("  pipeline <id>    - Get email processing pipeline");
  console.log("  recent [limit]   - Get recent emails");
  console.log("  orgs             - List organizations");
  console.log('  sql "<query>"    - Execute raw SQL');
  console.log("");
  console.log("Examples:");
  console.log("  node scripts/db-query.js email 123");
  console.log("  node scripts/db-query.js pipeline 123");
  console.log("  node scripts/db-query.js recent 20");
  console.log('  node scripts/db-query.js sql "SELECT COUNT(*) FROM email"');
}

// Handle process termination gracefully
process.on("SIGINT", () => {
  console.log("\nReceived SIGINT, shutting down gracefully...");
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("\nReceived SIGTERM, shutting down gracefully...");
  process.exit(0);
});

bootstrap();
