import { EmailStatus } from "nest-modules";

/**
 * Core-Agent Email Processing Events
 *
 * Events emitted by Core-Agent to communicate email processing status updates
 * back to the Email module. This enables clean separation between modules
 * while maintaining proper status tracking and error handling.
 *
 * Event Flow:
 * Core-Agent → Status Update Events → Email Module Listeners → Database Updates
 */

/**
 * Base class for all Core-Agent email processing events
 */
export abstract class BaseCoreAgentEmailEvent {
  constructor({
    emailId,
    organizationId,
    status
  }: {
    emailId: number;
    organizationId: number;
    status: EmailStatus;
  }) {
    this.emailId = emailId;
    this.organizationId = organizationId;
    this.status = status;
  }

  /** ID of the email being processed */
  public readonly emailId: number;

  /** Organization ID of the email */
  public readonly organizationId: number;

  /** Current email status */
  public readonly status: EmailStatus;
}

/**
 * Event emitted when Core-Agent starts processing an email
 * Indicates that <PERSON>-<PERSON> has taken control and begun business logic processing
 */
export class CoreAgentProcessingStartedEvent extends BaseCoreAgentEmailEvent {
  constructor({
    emailId,
    organizationId,
    status = EmailStatus.AWAIT_SHIPMENT_SEARCH
  }: {
    emailId: number;
    organizationId: number;
    status?: EmailStatus;
  }) {
    super({ emailId, organizationId, status });
  }
}

/**
 * Event emitted when Core-Agent successfully completes email processing
 * Indicates that all business logic has been executed and email is ready for response
 */
export class CoreAgentProcessingCompletedEvent extends BaseCoreAgentEmailEvent {
  constructor({
    emailId,
    organizationId,
    status = EmailStatus.RESPONDED
  }: {
    emailId: number;
    organizationId: number;
    status?: EmailStatus;
  }) {
    super({ emailId, organizationId, status });
  }
}

/**
 * Event emitted when Core-Agent encounters an error during email processing
 * Includes error details for debugging and recovery purposes
 */
export class CoreAgentProcessingFailedEvent extends BaseCoreAgentEmailEvent {
  constructor({
    emailId,
    organizationId,
    status = EmailStatus.FAILED_RESPONDING,
    error
  }: {
    emailId: number;
    organizationId: number;
    status?: EmailStatus;
    error?: string;
  }) {
    super({ emailId, organizationId, status });
    this.error = error;
  }

  /** Error message describing the failure */
  public readonly error?: string;
}

/**
 * Event emitted when Core-Agent updates email status during processing
 * Used for intermediate status updates during the processing pipeline
 */
export class CoreAgentStatusUpdateEvent extends BaseCoreAgentEmailEvent {
  constructor({
    emailId,
    organizationId,
    status,
    previousStatus,
    details
  }: {
    emailId: number;
    organizationId: number;
    status: EmailStatus;
    previousStatus?: EmailStatus;
    details?: string;
  }) {
    super({ emailId, organizationId, status });
    this.previousStatus = previousStatus;
    this.details = details;
  }

  /** Previous email status before the update */
  public readonly previousStatus?: EmailStatus;

  /** Additional details about the status update */
  public readonly details?: string;
}

/**
 * Event names for Core-Agent email processing events
 * Used for event emission and listening
 */
export enum CoreAgentEmailEvent {
  PROCESSING_STARTED = "core-agent.processing.started",
  PROCESSING_COMPLETED = "core-agent.processing.completed",
  PROCESSING_FAILED = "core-agent.processing.failed",
  STATUS_UPDATE = "core-agent.status.update"
}

/**
 * Type definitions for event payloads
 * Used for type safety in event emission and handling
 */
export interface CoreAgentProcessingStartedPayload {
  emailId: number;
  organizationId: number;
  status: EmailStatus;
}

export interface CoreAgentProcessingCompletedPayload {
  emailId: number;
  organizationId: number;
  status: EmailStatus;
}

export interface CoreAgentProcessingFailedPayload {
  emailId: number;
  organizationId: number;
  status: EmailStatus;
  error?: string;
}

export interface CoreAgentStatusUpdatePayload {
  emailId: number;
  organizationId: number;
  status: EmailStatus;
  previousStatus?: EmailStatus;
  details?: string;
}
