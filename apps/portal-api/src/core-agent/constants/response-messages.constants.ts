import { CustomsStatus } from "nest-modules";

/**
 * Status-specific response messages that match exactly with requirements docs
 */
export const STATUS_RESPONSE_MESSAGES: Record<CustomsStatus, string> = {
  [CustomsStatus.PENDING_COMMERCIAL_INVOICE]: "Commercial invoice is missing for this shipment.",
  [CustomsStatus.PENDING_CONFIRMATION]:
    "There are compliance issues or missing required fields preventing submission of this shipment.",
  [CustomsStatus.PENDING_ARRIVAL]: "The submission is pending the shipment's arrival.",
  [CustomsStatus.LIVE]: "The submission has been initiated.",
  [CustomsStatus.ENTRY_SUBMITTED]:
    "The shipment entry has been submitted to customs and is awaiting validation.",
  [CustomsStatus.ENTRY_ACCEPTED]:
    "The shipment entry has been accepted by customs and is awaiting arrival of goods.",
  [CustomsStatus.EXAM]: "The shipment has been selected by customs for examination.",
  [CustomsStatus.RELEASED]: "This shipment has been released by customs.",
  [CustomsStatus.ACCOUNTING_COMPLETED]: "Accounting has been completed for this shipment."
};

/**
 * Rush-specific action messages for different statuses
 */
export const RUSH_ACTION_MESSAGES: Record<CustomsStatus, string> = {
  [CustomsStatus.PENDING_COMMERCIAL_INVOICE]: "will process once missing documents are provided",
  [CustomsStatus.PENDING_CONFIRMATION]: "will process once compliance issues are resolved",
  [CustomsStatus.PENDING_ARRIVAL]: "will be submitting the entry right away",
  [CustomsStatus.LIVE]: "will be submitting the entry right away",
  [CustomsStatus.ENTRY_SUBMITTED]: "note that the entry has already been submitted",
  [CustomsStatus.ENTRY_ACCEPTED]: "note that the entry has already been submitted and accepted",
  [CustomsStatus.EXAM]:
    "note the shipment has been selected for exam and will be released once examination is complete",
  [CustomsStatus.RELEASED]: "note that the shipment has already been released",
  [CustomsStatus.ACCOUNTING_COMPLETED]: "note that processing has been completed"
};

/**
 * Document blocking reasons for CAD/RNS when not available
 */
export const DOCUMENT_BLOCKER_REASONS: Record<CustomsStatus, string> = {
  [CustomsStatus.PENDING_COMMERCIAL_INVOICE]:
    "we're missing the following document(s). Please send them at your earliest convenience so we can proceed with filing.\n\nCI & PL: **Missing**",
  [CustomsStatus.PENDING_CONFIRMATION]:
    "there are compliance issues or missing required fields. Please respond to the email and provide additional information for the missing fields.",
  [CustomsStatus.PENDING_ARRIVAL]: "Send email to backoffice to send them the CAD.",
  [CustomsStatus.LIVE]: "document generation is in progress",
  [CustomsStatus.ENTRY_SUBMITTED]: "document generation is in progress",
  [CustomsStatus.ENTRY_ACCEPTED]: "document generation is in progress",
  [CustomsStatus.EXAM]: "document generation is in progress",
  [CustomsStatus.RELEASED]: "document generation is in progress",
  [CustomsStatus.ACCOUNTING_COMPLETED]: "document generation is in progress"
};

/**
 * RNS availability requirements
 */
export const RNS_REQUIRED_STATUS_MESSAGES: Record<string, string> = {
  pending_submission: "submitted to customs",
  pending_acceptance: "accepted by customs"
};

/**
 * Document type identifiers for template context
 */
export enum DocumentType {
  CAD = "CAD",
  RNS = "RNS"
}

/**
 * Transport mode mappings for template logic
 */
export enum TransportMode {
  OCEAN = "OCEAN",
  AIR = "AIR",
  TRUCK = "TRUCK"
}
