export const QUESTION_CATEGORIES = {
  GENERAL_STATUS: {
    category: "GENERAL_STATUS",
    description: "Vague or general status requests (e.g., 'What's the status?', 'Any update?')",
    examples: ["What is the status of HBL123?", "Any update on shipment XYZ?"]
  },
  SHIPPING_STATUS: {
    category: "SHIPPING_STATUS",
    description: "Physical shipment location or tracking status (e.g., 'Where is my container?')",
    examples: ["Where is my container for HBL123?", "What is the tracking status?"]
  },
  ETA: {
    category: "ETA",
    description: "Estimated Time of Arrival at port or destination (e.g., 'When will it arrive?')",
    examples: ["When is ASZ241100051 expected to arrive?"]
  },
  CUSTOMS_STATUS: {
    category: "CUSTOMS_STATUS",
    description: "Overall customs entry status (e.g., 'What is the customs status?')",
    examples: ["What is the customs status for HBL123?"]
  },
  RELEASE_STATUS: {
    category: "RELEASE_STATUS",
    description: "Whether customs has released the shipment (e.g., 'Has the shipment been released?')",
    examples: ["Has HBL123 cleared customs?"]
  },
  SUBMISSION_STATUS: {
    category: "SUBMISSION_STATUS",
    description: "Whether customs entry has been submitted (e.g., 'Has the entry been submitted?')",
    examples: ["Has the entry for ASZ241100051 been submitted to CBSA yet?"]
  },
  TRANSACTION_NUMBER: {
    category: "TRANSACTION_NUMBER",
    description: "Request for customs transaction number (e.g., 'What is the transaction number?')",
    examples: ["What is the transaction number for this shipment?"]
  },
  SHIPMENT_ISSUES: {
    category: "SHIPMENT_ISSUES",
    description:
      "Request to identify or notify about missing required documents, fields, compliance rule errors, missing compliance filings, or anything that could be holding up a shipment.",
    examples: [
      "Are any documents missing for my entry?",
      "Is anything missing for submission?",
      "What documents do I still need to provide?",
      "What is holding up my shipment?",
      "Are there any errors with my shipment?",
      "Why is it pending?"
    ]
  },
  SPAM: {
    category: "SPAM",
    description: "Spam or irrelevant queries that do not pertain to shipment or customs information.",
    examples: []
  },
  UNKNOWN: {
    category: "UNKNOWN",
    description: "Unknown or fallback category. Use if no other category fits.",
    examples: []
  }
} as const;

/**
 * Returns a map of question category keys to their category string values.
 * This provides a convenient way to access just the category strings.
 * @returns {Record<keyof typeof QUESTION_CATEGORIES, string>} Map of category keys to category strings
 */
export function getQuestionCategoriesMap(): Record<keyof typeof QUESTION_CATEGORIES, string> {
  return Object.entries(QUESTION_CATEGORIES).reduce(
    (acc, [key, value]) => ({
      ...acc,
      [key]: value.category
    }),
    {} as Record<keyof typeof QUESTION_CATEGORIES, string>
  );
}

// Type for all possible category string values
export type QuestionCategory = (typeof QUESTION_CATEGORIES)[keyof typeof QUESTION_CATEGORIES]["category"];
