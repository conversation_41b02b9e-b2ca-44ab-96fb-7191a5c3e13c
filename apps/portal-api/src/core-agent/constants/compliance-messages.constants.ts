import { NonCompliantReason } from "nest-modules";

export const NON_COMPLIANT_REASON_MESSAGES: Record<NonCompliantReason, string> = {
  [NonCompliantReason.MISSING_OGD_FILING]:
    "Missing required OGD filing information (Other Government Department).",
  [NonCompliantReason.MISSING_SIMA_FILING]:
    "Missing required SIMA filing information (Special Import Measures Act).",
  [NonCompliantReason.MISSING_OR_INVALID_MEASUREMENTS]:
    "Missing or invalid per-unit measurements required for OGD (Other Government Department).",
  [NonCompliantReason.MISSING_CERTIFICATE_OF_ORIGIN]:
    "Missing required Certificate of Origin for the product.",
  // Explicitly handle deprecated code if needed, or ensure calling code handles it.
  [NonCompliantReason.MISSING_SIMA_CODE]: "Missing SIMA code (Deprecated)."
};

// Additional specific error messages not tied to NonCompliantReason enum
export const COMPLIANCE_ERROR_MESSAGES = {
  INVALID_HS_CODE: "Invalid HS Code.",
  INVALID_QUANTITY: "Invalid Quantity (cannot be zero).",
  NO_COMMERCIAL_INVOICE: "No commercial invoice is associated with this shipment."
};
