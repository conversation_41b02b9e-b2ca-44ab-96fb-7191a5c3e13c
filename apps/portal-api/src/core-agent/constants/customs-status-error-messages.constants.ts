import { CustomsStatus } from "nest-modules";

/**
 * Enum of business logic error types for customs status checks.
 */
export enum CustomsStatusErrorType {
  INVALID_CCN = "Invalid CCN",
  MISSING_PLACE_OF_DELIVERY = "Missing Place of Delivery",
  MISSING_ETA_PORT_OR_DESTINATION = "Missing ETA Port or ETA Destination",
  MISSING_ETA_PORT = "Missing ETA Port",
  MISSING_ETA_DESTINATION = "Missing ETA Destination"
}

/**
 * Generates an error message for an invalid or missing CCN.
 * @param shipmentId - The shipment's ID.
 * @param customsStatus - The current customs status.
 * @returns The error message string.
 */
export function buildInvalidCcnErrorMessage(
  shipmentId: number,
  customsStatus: CustomsStatus | string
): string {
  return `Shipment ${shipmentId} is in ${customsStatus} customs status but is missing CCN or has an invalid CCN.`;
}

/**
 * Generates an error message for a missing place of delivery.
 * @param shipmentId - The shipment's ID.
 * @returns The error message string.
 */
export function buildMissingPlaceOfDeliveryErrorMessage(shipmentId: number): string {
  return `Ocean FCL Shipment ${shipmentId} cannot be checked for entry submission because it has no place of delivery and thus, cannot be determined if it is an inland or port shipment.`;
}

/**
 * Generates an error message for a missing ETA port or ETA destination (inland FCL).
 * @param shipmentId - The shipment's ID.
 * @returns The error message string.
 */
export function buildMissingEtaPortOrDestinationErrorMessage(shipmentId: number): string {
  return `Ocean FCL Inland Shipment ${shipmentId} cannot be checked for entry submission because it has no ETA port or ETA destination.`;
}

/**
 * Generates an error message for a missing ETA port (port FCL).
 * @param shipmentId - The shipment's ID.
 * @returns The error message string.
 */
export function buildMissingEtaPortErrorMessage(shipmentId: number): string {
  return `Ocean FCL Port Shipment ${shipmentId} cannot be checked for entry submission because it has no ETA port.`;
}

/**
 * Generates an error message for a missing ETA destination (LCL).
 * @param shipmentId - The shipment's ID.
 * @returns The error message string.
 */
export function buildMissingEtaDestinationErrorMessage(shipmentId: number): string {
  return `Ocean LCL Shipment ${shipmentId} cannot be checked for entry submission because it has no ETA destination.`;
}
