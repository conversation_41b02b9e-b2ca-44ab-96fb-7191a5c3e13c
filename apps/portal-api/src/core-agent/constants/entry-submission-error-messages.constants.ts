/**
 * Enum of error types for entry submission validation.
 */
export enum EntrySubmissionErrorType {
  MISSING_SHIPMENT_FIELDS = "Missing Shipment Fields",
  MISSING_COMMERCIAL_INVOICE_FIELDS = "Missing Commercial Invoice Fields",
  MISSING_SHIP_TO_FIELDS = "Missing Ship-To Fields",
  MISSING_VENDOR_FIELDS = "Missing Vendor Fields",
  INVALID_HS_CODE_LINES = "Invalid HS Code Lines",
  INVALID_QUANTITY_LINES = "Invalid Quantity Lines",
  NON_COMPLIANT_LINES = "Non-Compliant Lines"
}

/**
 * Generates an error message for missing fields in a commercial invoice.
 * @param invoiceNumber - The invoice number.
 * @param missingFields - The list of missing fields.
 * @returns The error message string.
 */
export function buildMissingCommercialInvoiceFieldsErrorMessage(
  invoiceNumber: string | number,
  missingFields: string[]
): string {
  return `The following fields in commercial invoice ${invoiceNumber} ${missingFields.length > 1 ? "are" : "is"} missing: ${missingFields.join(", ")}`;
}

/**
 * Generates an error message for missing fields in shipTo.
 * @param shipToName - The shipTo name.
 * @param missingFields - The list of missing fields.
 * @returns The error message string.
 */
export function buildMissingShipToFieldsErrorMessage(shipToName: string, missingFields: string[]): string {
  return `The following fields in shipTo ${shipToName} are missing: ${missingFields.join(", ")}`;
}

/**
 * Generates an error message for missing fields in vendor.
 * @param vendorName - The vendor name.
 * @param missingFields - The list of missing fields.
 * @returns The error message string.
 */
export function buildMissingVendorFieldsErrorMessage(vendorName: string, missingFields: string[]): string {
  return `The following fields in vendor ${vendorName} are missing: ${missingFields.join(", ")}`;
}

/**
 * Generates an error message for invalid HS code lines.
 * @param invoiceNumber - The invoice number.
 * @param lineIds - The list of line IDs with invalid HS codes.
 * @returns The error message string.
 */
export function buildInvalidHsCodeLinesErrorMessage(
  invoiceNumber: string | number,
  lineIds: Array<string | number>
): string {
  return `Some commercial invoice lines in invoice ${invoiceNumber} have invalid HS codes, please check again. Invalid HS code lines: ${lineIds.join(", ")}`;
}

/**
 * Generates an error message for invalid quantity lines.
 * @param invoiceNumber - The invoice number.
 * @param lineIds - The list of line IDs with invalid quantities.
 * @returns The error message string.
 */
export function buildInvalidQuantityLinesErrorMessage(
  invoiceNumber: string | number,
  lineIds: Array<string | number>
): string {
  return `Some commercial invoice lines in invoice ${invoiceNumber} have invalid quantities, please check again. Invalid quantity lines: ${lineIds.join(", ")}`;
}

/**
 * Generates an error message for non-compliant lines.
 * @param invoiceNumber - The invoice number.
 * @param lineIds - The list of non-compliant line IDs.
 * @returns The error message string.
 */
export function buildNonCompliantLinesErrorMessage(
  invoiceNumber: string | number,
  lineIds: Array<string | number>
): string {
  return `Some commercial invoice lines in invoice ${invoiceNumber} are not compliant, please check again. Non-compliant lines: ${lineIds.join(", ")}`;
}
