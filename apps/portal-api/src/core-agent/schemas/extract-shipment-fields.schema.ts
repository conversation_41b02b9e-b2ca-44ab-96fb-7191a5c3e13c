import { z } from "zod";

/**
 * Zod schema for extracting shipment update fields from unstructured text.
 * Matches partial EditShipmentDto format for UPDATE_SHIPMENT intents.
 */
export const ExtractShipmentFieldsSchema = z.object({
  cargoControlNumber: z
    .string()
    .optional()
    .describe("Cargo Control Number (CCN) - alphanumeric identifier for the shipment"),

  portCode: z
    .string()
    .optional()
    .describe("Port code - exactly 4 characters starting with 0 followed by 3 digits (e.g., 0123)"),

  subLocation: z.string().optional().describe("Sub-location code - exactly 4 digits (e.g., 5678)")
});

// Export the TypeScript type for easier use
export type ExtractShipmentFieldsOutput = z.infer<typeof ExtractShipmentFieldsSchema>;
