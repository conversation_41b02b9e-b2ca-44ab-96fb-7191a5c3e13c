import { z } from "zod";

import {
  QUESTION_CATEGORIES,
  QuestionCategory as QuestionCategoryConstant
} from "../constants/question-categories.constants";

// Dynamically build the enum from the category property values of QUESTION_CATEGORIES
const categoryValues = Object.values(QUESTION_CATEGORIES).map((c) => c.category) as [
  QuestionCategoryConstant,
  ...QuestionCategoryConstant[]
];
export const QuestionCategoryEnum = z.enum(categoryValues);

// Define the schema for the LLM output
export const ClassifyQuestionCategorySchema = z.object({
  category: QuestionCategoryEnum.describe(
    "The most specific category classifying the user's query about a shipment."
  )
});

// Define the TypeScript type corresponding to the schema
export type ClassifyQuestionCategoryOutput = z.infer<typeof ClassifyQuestionCategorySchema>;
