import z from "zod";

export const SHIPMENT_IDENTIFIER_TYPES = ["HBL", "CCN", "PARS"] as const;
export type ShipmentIdentifierType = (typeof SHIPMENT_IDENTIFIER_TYPES)[number];

export const ShipmentIdentifierSchema = z.object({
  type: z
    .enum(SHIPMENT_IDENTIFIER_TYPES)
    .describe(
      "Type of identifier found - House Bill of Lading (HBL), Cargo Control Number (CCN), or PARS number"
    ),
  value: z.string().describe("The actual identifier value found in the text"),
  context: z.string().describe("Quote the relevant part of the email text where this identifier was found")
});

export const ShipmentIdentifiersSchema = z
  .object({
    foundIdentifiers: z
      .array(ShipmentIdentifierSchema)
      .describe("List any shipment identifiers found in the email text, with their surrounding context")
  })
  .strict();

export type ShipmentIdentifier = z.infer<typeof ShipmentIdentifierSchema>;
export type ShipmentIdentifiers = z.infer<typeof ShipmentIdentifiersSchema>;
