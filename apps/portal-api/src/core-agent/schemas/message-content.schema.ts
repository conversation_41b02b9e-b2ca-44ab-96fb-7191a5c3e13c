import z from "zod";

// Size limit constants for message content validation
export const MESSAGE_SIZE_LIMITS = {
  SUBJECT_MAX_LENGTH: 500,
  TEXT_MAX_LENGTH: 100000,
  MESSAGE_HISTORY_MAX_LENGTH: 100000,
  FILENAME_MAX_LENGTH: 255,
  CONTENT_TYPE_MAX_LENGTH: 100
};

export const MessageAttachmentSchema = z.object({
  filename: z.string().describe("The filename of the attachment"),
  contentType: z.string().optional().describe("The content type of the attachment"),
  extractedData: z
    .record(z.string().or(z.number()).or(z.boolean()))
    .optional()
    .describe("Extracted data from the attachment")
});

export const MessageContentSchema = z
  .object({
    subject: z.string().nullable().optional().describe("Subject of the message"),
    text: z.string().describe("Text content of the message"),
    attachments: z.array(MessageAttachmentSchema).optional().describe("List of attachments in the message"),
    emailHistory: z
      .array(z.string())
      .optional()
      .describe("Previous message exchanges (deprecated, use messageHistory)"),
    messageHistory: z.array(z.string()).optional().describe("Previous message exchanges in the thread")
  })
  .strict();

export type MessageAttachment = z.infer<typeof MessageAttachmentSchema>;
export type MessageContent = z.infer<typeof MessageContentSchema>;
