import { z } from "zod";

// Define the possible classification outcomes based on data availability and required actions
const InquiryClassificationEnum = z.enum([
  "DIRECTLY_ANSWERABLE", // Can be answered directly from basic Shipment/Container data
  "REQUIRES_LOOKUP", // Requires fetching related data (tracking, logs, names, etc.)
  "REQUIRES_VALIDATION", // Requires running compliance/business logic/OGD checks
  "REQUIRES_CONTEXT_OR_PREDICTION", // Requires conversation history or future estimation
  "UNKNOWN_OR_UNSUPPORTED" // Vague, unrelated, or impossible question
]);

// Export the enum values as a type for use elsewhere
export type InquiryClassificationCategory = z.infer<typeof InquiryClassificationEnum>;

// Define the schema for the LLM's JSON output
export const InquiryClassificationSchema = z
  .object({
    reasoning: z
      .string()
      .describe("A brief explanation for why the query was classified into the chosen category."),
    category: InquiryClassificationEnum.describe(
      "The classified category based on data requirements to answer the user's query."
    )
  })
  .strict(); // Use strict to ensure no extra fields are returned

// Export the type for easier use
export type InquiryClassificationOutput = z.infer<typeof InquiryClassificationSchema>;
