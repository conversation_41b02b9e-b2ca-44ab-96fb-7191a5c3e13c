import { z } from "zod";

/**
 * Defines the valid types for shipment identifiers used in database lookups.
 */
export const IdentifierTypeEnum = z.enum([
  "cargoControlNumber",
  "containerNumber",
  "hblNumber",
  "transactionNumber"
]);

/**
 * Schema for validating the input parameters for the lookupShipmentDB tool/function.
 */
export const LookupShipmentDBSchema = z
  .object({
    /**
     * The type of identifier being provided.
     */
    identifierType: IdentifierTypeEnum.describe("The type of identifier (e.g., hblNumber, containerNumber)."),
    /**
     * The actual value of the identifier string.
     */
    identifierValue: z
      .string({
        required_error: "Identifier value is required.",
        invalid_type_error: "Identifier value must be a string."
      })
      .describe("The actual value of the identifier extracted from the source.")
  })
  .strict();

/**
 * TypeScript type inferred from the LookupShipmentDBSchema.
 * Represents the expected input structure for looking up a shipment in the database.
 */
export type LookupShipmentDBInput = z.infer<typeof LookupShipmentDBSchema>;

/**
 * Schema for validating the input parameters for the llmLookupShipmentDB tool/function.
 */
export const LlmLookupShipmentDBInputSchema = z
  .object({
    /**
     * The actual value of the identifier string to search for.
     */
    identifierValue: z
      .string({
        required_error: "Identifier value is required.",
        invalid_type_error: "Identifier value must be a string."
      })
      .describe("The actual value of the identifier extracted from the source to search for."),
    /**
     * Required but nullable: The initial guessed type of the identifier by the LLM.
     * The tool will search this type first if provided. Send null if not guessed.
     */
    initialGuessedType: IdentifierTypeEnum.nullable().describe(
      "Required but nullable: The initial guessed type of identifier (e.g., hblNumber, containerNumber). Send null if not guessed."
    )
  })
  .strict();

export type LlmLookupShipmentDBInput = z.infer<typeof LlmLookupShipmentDBInputSchema>;

/**
 * Describes the output structure of the llmLookupShipmentDB tool's underlying function.
 */
export type LlmLookupShipmentDBOutput = {
  shipmentId: number | null;
  matchedIdentifierType: z.infer<typeof IdentifierTypeEnum> | null; // Corresponds to IdentifierTypeEnum values
  identifierValue: string;
  reason: string;
};
