import { z } from "zod";

/**
 * Zod schema for the task decomposition output, returning an array of strings.
 */
export const TaskListSchema = z.object({
  tasks: z
    .array(z.string())
    .describe(
      "An ordered list of decomposed task strings, each describing a single actionable step including necessary details."
    )
});

/**
 * Type representing the task decomposition result as an array of strings.
 */
export type TaskList = z.infer<typeof TaskListSchema>;
