import { z } from "zod";
import { EMAIL_INTENTS } from "nest-modules"; // Import the source of truth

// Define the possible intent types using the imported constant
export const TaskIntentEnum = z.enum(EMAIL_INTENTS);

/**
 * Zod schema for the output of classifying a single task's intent.
 * Assumes the LLM returns an object with the intent key.
 */
export const ClassifyIntentOnlySchema = z.object({
  intent: TaskIntentEnum.describe("The classified intent of the task.")
});

/**
 * Zod schema for a single decomposed task string paired with its classified intent.
 */
export const ClassifiedTaskSchema = z.object({
  task: z.string().describe("The original decomposed task string."),
  intent: TaskIntentEnum.describe("The classified intent for the task.")
});

/**
 * Zod schema for an array of classified tasks.
 */
export const ClassifiedTasksSchema = z.object({
  classifiedTasks: z
    .array(ClassifiedTaskSchema)
    .describe("An array containing original tasks paired with their classified intents.")
});

// Export types
export type TaskIntent = z.infer<typeof TaskIntentEnum>;
export type ClassifyIntentOnlyOutput = z.infer<typeof ClassifyIntentOnlySchema>;
export type ClassifiedTask = z.infer<typeof ClassifiedTaskSchema>;
export type ClassifiedTasks = z.infer<typeof ClassifiedTasksSchema>;
