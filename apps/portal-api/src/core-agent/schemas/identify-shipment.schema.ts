import { z } from "zod";
import { IdentifierTypeEnum } from "./lookup-shipment-db.schema";

// Schema for a single identified shipment reference
const FoundIdentifierSchema = z.object({
  /**
   * The database ID of the identified shipment.
   */
  shipmentId: z.number().describe("The database ID of the identified shipment."),
  /**
   * The type of identifier that was successfully matched in the database.
   */
  identifierType: IdentifierTypeEnum.describe(
    "The type of identifier (e.g., hblNumber, containerNumber) that was matched."
  ),
  /**
   * The actual value of the identifier that was successfully matched.
   */
  identifierValue: z.string().describe("The value of the identifier that was matched."),
  /**
   * Context or evidence from the input that led to this identification.
   */
  context: z.string().describe("Context or evidence from the original input supporting the identification.")
});

/**
 * Defines the output structure for the shipment identification process.
 * It includes a list of identified shipments and a reason explaining the overall result.
 */
export const IdentifyShipmentOutputSchema = z.object({
  /**
   * An array containing details of each shipment identified.
   * If no shipments are found, this array will be empty.
   */
  foundIdentifiers: z
    .array(FoundIdentifierSchema)
    .describe("List of identified shipments, each with ID and context."),
  /**
   * An explanation summarizing the identification result.
   * E.g., "Found one matching shipment based on HBL", "Multiple shipments found", "No identifiers found in the email".
   */
  reason: z.string().describe("Explanation summarizing the identification result and process.")
});

/**
 * TypeScript type inferred from the IdentifyShipmentOutputSchema.
 * Represents the result of attempting to identify shipments from given content.
 */
export type IdentifyShipmentOutput = z.infer<typeof IdentifyShipmentOutputSchema>;
