{# 
  COMPREHENSIVE EMAIL TEMPLATE FOR ALL INTENT TYPES
  
  This template supports ALL intent sections from CleanAgentContextService:
  - PROCESS_DOCUMENT, GET_SHIPMENT_STATUS, REQUEST_CAD_DOCUMENT
  - REQUEST_RNS_PROOF, REQUEST_RUSH_PROCESSING, REQUEST_MANUAL_PROCESSING  
  - REQUEST_HOLD_SHIPMENT, UPDATE_SHIPMENT, ACK<PERSON><PERSON><PERSON>DGE_DOCUMENTS
  - ACKNOWLEDGE_MISSING_DOCUMENTS, DOCUMENTATION_COMING
  
  Uses conditional rendering to show only relevant sections with content.
#}



{# ========== GREETING SECTION ========== #}
<p>Hello,</p>


{# ========== RNS PROOF SECTION ========== #}
{% if SHOW_RNS_PROOF and RNS_PROOF_DATA %}
<div style="font-family: monospace; background-color: #f5f5f5; padding: 15px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 15px;">
<strong>RNS PROOF OF RELEASE</strong><br /><br />
{% if RNS_RESPONSE_MESSAGE %}{{ RNS_RESPONSE_MESSAGE }}<br />{% endif %}
{% if IMPORTER_NAME %}Importer Name: {{ IMPORTER_NAME }}<br />{% endif %}
{% if PROCESS_DATE %}Process Date: {{ PROCESS_DATE }}<br />{% endif %}
{% if RESPONSE_DATE %}Response Date: {{ RESPONSE_DATE }}<br />{% endif %}
{% if TRANSACTION_NUMBER %}Transaction #: {{ TRANSACTION_NUMBER }}<br />{% endif %}
{% if CARGO_CONTROL_NUMBER %}Cargo Control #: {{ CARGO_CONTROL_NUMBER }}<br />{% endif %}
{% if PORT_CODE %}Port Code: {{ PORT_CODE }}<br />{% endif %}
{% if SUBLOCATION_CODE %}Sublocation Code: {{ SUBLOCATION_CODE }}<br />{% endif %}
{% if CONTAINER_NUMBERS %}Container #: {{ CONTAINER_NUMBERS }}<br />{% endif %}
{% if PROCESSING_INDICATOR %}Release Code: {{ PROCESSING_INDICATOR }}{% if RNS_RESPONSE_MESSAGE %} - {{ RNS_RESPONSE_MESSAGE }}{% endif %}{% endif %}
</div>
{% endif %}


{# ========== CAD DOCUMENT ATTACHMENT SECTION ========== #}
{% if HAS_CAD_ATTACHMENT and CAD_DOCUMENT %}
<div style="background-color: #f0f8ff; padding: 15px; border: 1px solid #b0d4f1; border-radius: 4px; margin-bottom: 15px;">
<strong>📎 CAD Document Attached</strong><br />
<p>Please find the Customs Accounting Document (CAD) attached to this email.</p>
{% if CAD_DOCUMENT.filename %}<p>Filename: {{ CAD_DOCUMENT.filename }}</p>{% endif %}
{% if CAD_DOCUMENT.documentNumber %}<p>Document Number: {{ CAD_DOCUMENT.documentNumber }}</p>{% endif %}
</div>
{% endif %}


{# ========== DOCUMENT PROCESSING LIST ========== #}
{% if SHOW_PROCESS_DOCUMENT_MESSAGE %}
  <p>{{ PROCESS_DOCUMENT_MESSAGE }}</p>
{% endif %}

{% if PROCESSED_DOCUMENTS %}
<p style="margin-top: 15px;">We've received and processed the following document(s):</p>
<ul style="list-style-type: none; padding-left: 0;">
  {% for document in PROCESSED_DOCUMENTS %}
  <li style="border: 1px solid #eee; padding: 10px; margin-bottom: 10px; border-radius: 4px;">
    <strong>{{ document.filename }}</strong>
    <ul style="margin-top: 5px; padding-left: 20px;">
      <li>Document Type: {{ document.contentType }}</li>
      <li>Status: {{ document.aggregationStatus | capitalize }}
        {% if document.aggregationStatus == 'success' %} ✅
        {% elif document.aggregationStatus == 'failed' %} ❌
        {% elif document.aggregationStatus == 'processing' %} 🔄
        {% elif document.aggregationStatus == 'pending' %} ⏳
        {% endif %}
      </li>
      <li>Claro URL: <a href="{{ document.claroUrl }}">{{ document.claroUrl }}</a></li>
    </ul>
  </li>
  {% endfor %}
</ul>
{% endif %}


{# ========== SUBMISSION RESULT SECTION ========== #}
{% if HAS_SUBMISSION_RESULT and SUBMISSION_RESULT %}
<div style="background-color: #e8f5e8; padding: 15px; border: 1px solid #b8e6b8; border-radius: 4px; margin-bottom: 15px;">
<strong>📋 Submission Status</strong><br />
{% if SUBMISSION_RESULT.success %}
<p style="color: #2e7d2e;">✅ Successfully submitted to customs processing</p>
{% if SUBMISSION_RESULT.details %}<p>Details: {{ SUBMISSION_RESULT.details }}</p>{% endif %}
{% else %}
<p style="color: #d32f2f;">❌ Submission encountered issues</p>
{% if SUBMISSION_RESULT.error %}<p>Error: {{ SUBMISSION_RESULT.error }}</p>{% endif %}
{% endif %}
</div>
{% endif %}


{# ========== UPDATE RESULT SECTION ========== #}
{% if HAS_UPDATE_RESULT and UPDATE_RESULT %}
<div style="background-color: #fff3e0; padding: 15px; border: 1px solid #ffcc80; border-radius: 4px; margin-bottom: 15px;">
<strong>🔄 Update Status</strong><br />
{% if UPDATE_RESULT.success %}
<p style="color: #e65100;">✅ Shipment information updated successfully</p>
{% if UPDATE_RESULT.updatedFields %}
<p>Updated fields: {{ UPDATE_RESULT.updatedFields | join(', ') }}</p>
{% endif %}
{% else %}
<p style="color: #d32f2f;">❌ Update failed</p>
{% if UPDATE_RESULT.error %}<p>Error: {{ UPDATE_RESULT.error }}</p>{% endif %}
{% endif %}
</div>
{% endif %}


{# ========== BACKOFFICE ALERT SECTIONS ========== #}
{% if RUSH_PROCESSING_ALERT_SENT %}
<div style="background-color: #fce4ec; padding: 10px; border: 1px solid #f8bbd9; border-radius: 4px; margin-bottom: 10px;">
<p>🚨 <strong>Rush Processing Alert:</strong> Our priority processing team has been notified.</p>
</div>
{% endif %}

{% if MANUAL_PROCESSING_ALERT_SENT %}
<div style="background-color: #fce4ec; padding: 10px; border: 1px solid #f8bbd9; border-radius: 4px; margin-bottom: 10px;">
<p>🚨 <strong>Manual Processing Alert:</strong> Our specialist team has been notified to review your shipment manually.</p>
</div>
{% endif %}

{% if HOLD_SHIPMENT_ALERT_SENT %}
<div style="background-color: #fce4ec; padding: 10px; border: 1px solid #f8bbd9; border-radius: 4px; margin-bottom: 10px;">
<p>🚨 <strong>Hold Shipment Alert:</strong> Our team has been notified to hold/cancel processing as requested.</p>
</div>
{% endif %}


{# ========== EMAIL RESPONSE MESSAGES SECTION ========== #}
{# Document Acknowledgment Handlers - Only show if message has content #}
{% if ACKNOWLEDGE_DOCUMENTS_MESSAGE and ACKNOWLEDGE_DOCUMENTS_MESSAGE != '' %}
<p>{{ ACKNOWLEDGE_DOCUMENTS_MESSAGE }}</p>
{% endif %}

{% if ACKNOWLEDGE_MISSING_DOCUMENTS_MESSAGE and ACKNOWLEDGE_MISSING_DOCUMENTS_MESSAGE != '' %}
<p>{{ ACKNOWLEDGE_MISSING_DOCUMENTS_MESSAGE }}</p>
{% endif %}

{% if DOCUMENTATION_COMING_MESSAGE and DOCUMENTATION_COMING_MESSAGE != '' %}
<p>{{ DOCUMENTATION_COMING_MESSAGE }}</p>
{% endif %}

{# Document Request Handlers #}
{% if REQUEST_CAD_DOCUMENT_MESSAGE and REQUEST_CAD_DOCUMENT_MESSAGE != '' %}
<p>{{ REQUEST_CAD_DOCUMENT_MESSAGE }}</p>
{% endif %}

{% if REQUEST_RNS_PROOF_MESSAGE and REQUEST_RNS_PROOF_MESSAGE != '' %}
<p>{{ REQUEST_RNS_PROOF_MESSAGE }}</p>
{% endif %}

{# Processing Request Handlers #}
{% if REQUEST_RUSH_PROCESSING_MESSAGE and REQUEST_RUSH_PROCESSING_MESSAGE != '' %}
<p>{{ REQUEST_RUSH_PROCESSING_MESSAGE }}</p>
{% endif %}

{% if REQUEST_MANUAL_PROCESSING_MESSAGE and REQUEST_MANUAL_PROCESSING_MESSAGE != '' %}
<p>{{ REQUEST_MANUAL_PROCESSING_MESSAGE }}</p>
{% endif %}

{% if REQUEST_HOLD_SHIPMENT_MESSAGE and REQUEST_HOLD_SHIPMENT_MESSAGE != '' %}
<p>{{ REQUEST_HOLD_SHIPMENT_MESSAGE }}</p>
{% endif %}

{# Data Update Handlers #}
{% if UPDATE_SHIPMENT_MESSAGE and UPDATE_SHIPMENT_MESSAGE != '' %}
<p>{{ UPDATE_SHIPMENT_MESSAGE }}</p>
{% endif %}

{# Dynamic Content Generators #}
{% if ETA_RESPONSE_MESSAGE and ETA_RESPONSE_MESSAGE != '' %}
<p>{{ ETA_RESPONSE_MESSAGE }}</p>
{% endif %}

{% if TRANSACTION_NUMBER_RESPONSE_MESSAGE and TRANSACTION_NUMBER_RESPONSE_MESSAGE != '' %}
<p>{{ TRANSACTION_NUMBER_RESPONSE_MESSAGE }}</p>
{% endif %}

{% if RELEASE_STATUS_RESPONSE_MESSAGE and RELEASE_STATUS_RESPONSE_MESSAGE != '' %}
<p>{{ RELEASE_STATUS_RESPONSE_MESSAGE }}</p>
{% endif %}

{% if SHIPPING_STATUS_RESPONSE_MESSAGE and SHIPPING_STATUS_RESPONSE_MESSAGE != '' %}
<p>{{ SHIPPING_STATUS_RESPONSE_MESSAGE }}</p>
{% endif %}

{# Error/Fallback Messages #}
{% if SYSTEM_ERROR_MESSAGE and SYSTEM_ERROR_MESSAGE != '' %}
<p>{{ SYSTEM_ERROR_MESSAGE }}</p>
{% endif %}

{% if CONTACT_SUPPORT_MESSAGE and CONTACT_SUPPORT_MESSAGE != '' %}
<p>{{ CONTACT_SUPPORT_MESSAGE }}</p>
{% endif %}

{# Customs Status #}
{% if GET_SHIPMENT_STATUS_MESSAGE and GET_SHIPMENT_STATUS_MESSAGE != '' %}
<p>{{ GET_SHIPMENT_STATUS_MESSAGE }}</p>
{% endif %}

{% if SHOW_DETAILS %}
{# ========== DETAILS SECTION ========== #}
<p style="margin-top: 15px;">
  <strong><u>Details:</u></strong><br/>
  CCN#: <strong>{{ CCN_PLACEHOLDER }}</strong>, Container#: <strong>{{ CONTAINER_PLACEHOLDER }}</strong>, HBL#: <strong>{{ HBL_PLACEHOLDER }}</strong><br/>
  HBL: <strong>{{ HBL_STATUS_PLACEHOLDER }}</strong> AN/EMF: <strong>{{ AN_EMF_STATUS_PLACEHOLDER }}</strong> CI&PL: <strong>{{ CI_PL_STATUS_PLACEHOLDER }}</strong><br/>
  Status: <strong>{{ CUSTOMS_STATUS_LINE_PLACEHOLDER }}</strong>
</p>
{% endif %}

{% if SHOW_VALIDATION_ISSUES %}
{# ========== VALIDATION ISSUES SECTION ========== #}
<div style="background-color: #fef7e0; padding: 15px; border: 1px solid #fbc02d; border-radius: 4px; margin-top: 15px;">
<p><strong>⚠️ Outstanding Items:</strong></p>

{# Missing Documents #}
{% if CI_PL_MISSING or HBL_MISSING or AN_EMF_MISSING %}
<p><strong>Missing Documents:</strong></p>
<ul style="margin: 5px 0 10px 20px;">
  {% if CI_PL_MISSING %}<li>Commercial Invoice & Packing List</li>{% endif %}
  {% if HBL_MISSING %}<li>House Bill of Lading</li>{% endif %}
  {% if AN_EMF_MISSING %}<li>Arrival Notice/Empty Move Form</li>{% endif %}
</ul>
{% endif %}

{# Missing Fields #}
{% if WEIGHT_MISSING or PORT_CODE_MISSING or CCN_MISSING or OGD_FILING_PENDING %}
<p><strong>Missing Information:</strong></p>
<ul style="margin: 5px 0 10px 20px;">
  {% if WEIGHT_MISSING %}<li>Shipment Weight</li>{% endif %}
  {% if PORT_CODE_MISSING %}<li>Port Code</li>{% endif %}
  {% if CCN_MISSING %}<li>Cargo Control Number</li>{% endif %}
  {% if OGD_FILING_PENDING %}<li>OGD Filing Status: Pending</li>{% endif %}
</ul>
{% endif %}

{# Formatted placeholders as fallback #}
{% if MISSING_DOCUMENTS_PLACEHOLDER and MISSING_DOCUMENTS_PLACEHOLDER != 'None' %}
<p><strong>Missing Documents:</strong> {{ MISSING_DOCUMENTS_PLACEHOLDER }}</p>
{% endif %}
{% if MISSING_FIELDS_PLACEHOLDER and MISSING_FIELDS_PLACEHOLDER != 'None' %}
<p><strong>Missing Fields:</strong> {{ MISSING_FIELDS_PLACEHOLDER }}</p>
{% endif %}
</div>
{% endif %}

{% if SHOW_COMPLIANCE_ERRORS or (COMPLIANCE_ERRORS_PLACEHOLDER and COMPLIANCE_ERRORS_PLACEHOLDER != 'None') %}
{# ========== COMPLIANCE ERRORS SECTION ========== #}
<div style="background-color: #ffebee; padding: 15px; border: 1px solid #e57373; border-radius: 4px; margin-top: 15px;">
<p><strong>🚫 Compliance Issues:</strong></p>
<ul style="margin: 5px 0 0 20px;">
  <li>{{ COMPLIANCE_ERRORS_PLACEHOLDER }}</li>
</ul>
</div>
{% endif %}

{# ========== FOOTER SECTION ========== #}
<p style="margin-top: 20px;">
  Best regards,<br/>
  Claro Customs
</p>