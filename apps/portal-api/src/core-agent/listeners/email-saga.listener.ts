import { Injectable, Logger } from "@nestjs/common";
import { EventEmitter2, OnEvent } from "@nestjs/event-emitter";
import { InjectQueue, InjectFlowProducer } from "@nestjs/bullmq";
import { InjectDataSource } from "@nestjs/typeorm";
import {
  Email,
  EmailStatus,
  FileBatch,
  buildFlowProducer,
  EmailSavedEvent,
  FileBatchCreatedEvent,
  BatchDocumentAggregatedEvent,
  EMAIL_SAVED,
  FILE_BATCH_CREATED,
  BATCH_DOCUMENT_AGGREGATED_EVENT
} from "nest-modules";
import { DataSource, QueryRunner } from "typeorm";
import { FlowProducer } from "bullmq";
import { CoreAgentQueueName, HandleRequestMessageQueue, CoreAgentFlowProducer } from "../types/queue.types";

/**
 * Core-Agent Email Saga Listener
 *
 * Implements saga pattern for email processing with proper state management.
 * Uses pessimistic locking to ensure only SAVED emails are processed and
 * transitions emails to AGGREGATING status before queuing core-agent work.
 *
 * Event Flow:
 * EMAIL_SAVED (Email Module) → Core-Agent Saga Listener → State Transition → BullMQ Queue → EmailSavedProcessor
 *
 * Responsibilities:
 * - Listen for EMAIL_SAVED events with pessimistic locking
 * - Guard state transitions (only process SAVED emails)
 * - Transition email status to AGGREGATING_EMAIL
 * - Queue core-agent processing jobs via BullMQ
 * - Ensure idempotent processing and error handling
 */
@Injectable()
export class CoreAgentEmailSagaListener {
  private readonly logger = new Logger(CoreAgentEmailSagaListener.name);

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    @InjectQueue(CoreAgentQueueName.HANDLE_REQUEST_MESSAGE)
    private readonly handleRequestMessageQueue: HandleRequestMessageQueue,
    @InjectFlowProducer(CoreAgentFlowProducer.EMAIL_PROCESSING)
    private readonly flowProducer: FlowProducer,
    private readonly eventEmitter: EventEmitter2
  ) {}

  /**
   * Get FileBatch information including whether it has attachments and file IDs
   * This combines both attachment checking and file ID retrieval in a single database call
   *
   * @param gmailId - The Gmail ID that corresponds to FileBatch.id
   * @param queryRunner - Database query runner
   * @returns Promise<{hasAttachments: boolean, fileIds: number[]}> - Combined result
   */
  private async getFileBatchInfo(
    gmailId: string,
    queryRunner: QueryRunner
  ): Promise<{ hasAttachments: boolean; fileIds: number[] }> {
    const fileBatch = await queryRunner.manager.findOne(FileBatch, {
      where: { id: gmailId },
      relations: ["files"],
      select: {
        id: true,
        files: {
          id: true
        }
      }
    });

    if (!fileBatch || !fileBatch.files) {
      this.logger.warn(`[CORE_AGENT_SAGA] No FileBatch or files found for gmailId ${gmailId}`);
      return { hasAttachments: false, fileIds: [] };
    }

    const fileIds = fileBatch.files.map((file) => file.id);
    const hasAttachments = fileIds.length > 0;

    this.logger.log(
      `[CORE_AGENT_SAGA] FileBatch ${gmailId}: hasAttachments=${hasAttachments}, fileIds=[${fileIds.join(", ")}]`
    );

    return { hasAttachments, fileIds };
  }

  /**
   * Handle EMAIL_SAVED events with proper saga pattern
   * Uses pessimistic locking to ensure only SAVED emails are processed
   * Transitions email to AGGREGATING status and queues core-agent processing
   *
   * @param event - EmailSavedEvent containing email metadata
   */
  @OnEvent(EMAIL_SAVED)
  async onEmailSaved(event: EmailSavedEvent): Promise<void> {
    const { emailId, organizationId, gmailId, documents } = event;

    this.logger.log(
      `[CORE_AGENT_SAGA] EMAIL_SAVED event received - emailId: ${emailId}, organizationId: ${organizationId}, ` +
        `gmailId: ${gmailId}, documents: ${documents.length}`
    );

    // Use pessimistic locking for state machine safety
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Find email with pessimistic lock (without relations to avoid FOR UPDATE on outer join)
      const email = await queryRunner.manager.findOne(Email, {
        where: { id: emailId },
        lock: { mode: "pessimistic_write" }
      });

      if (!email) {
        this.logger.error(`[CORE_AGENT_SAGA] Email ${emailId} not found, stopped processing`);
        await queryRunner.rollbackTransaction();
        return;
      }

      // State machine guard: only process if SAVED
      if (email.status !== EmailStatus.SAVED) {
        this.logger.warn(
          `[CORE_AGENT_SAGA] Email ${emailId} is in state ${email.status}, expected ${EmailStatus.SAVED}. Already processed or processing. Skipping.`
        );
        await queryRunner.rollbackTransaction();
        return;
      }

      // Get FileBatch information including whether it has attachments and file IDs
      const { hasAttachments, fileIds } = await this.getFileBatchInfo(gmailId, queryRunner);
      let flow;

      // Use buildFlowProducer to create the appropriate flow based on attachments
      if (hasAttachments) {
        // Flow for emails WITH attachments: identify-shipment → emit event for aggregation
        this.logger.log(`[CORE_AGENT_SAGA] Building flow for email ${emailId} WITH attachments`);

        flow = buildFlowProducer([
          {
            name: "identify-shipment",
            queueName: CoreAgentQueueName.IDENTIFY_SHIPMENT,
            data: { emailId, organizationId, hasAttachments },
            opts: { failParentOnFailure: true }
          },
          {
            name: "emit-file-batch-created",
            queueName: CoreAgentQueueName.EVENT_EMITTER,
            data: {
              eventType: FILE_BATCH_CREATED,
              eventData: new FileBatchCreatedEvent(gmailId, organizationId, fileIds)
            }
          }
        ]);
      } else {
        // Flow for emails WITHOUT attachments: identify-shipment → handle-request-message
        this.logger.log(`[CORE_AGENT_SAGA] Building flow for email ${emailId} WITHOUT attachments`);

        flow = buildFlowProducer([
          {
            name: "identify-shipment",
            queueName: CoreAgentQueueName.IDENTIFY_SHIPMENT,
            data: { emailId, organizationId, hasAttachments },
            opts: { failParentOnFailure: true }
          },
          {
            name: "handle-request-message",
            queueName: CoreAgentQueueName.HANDLE_REQUEST_MESSAGE,
            data: { emailId, organizationId }
          }
        ]);
      }

      // add the jobs to the queue
      await this.flowProducer.add(flow);
      this.logger.log(`[CORE_AGENT_SAGA] Successfully queued flow for email ${emailId}`);

      // update the email status
      await queryRunner.manager.update(Email, { id: emailId }, { status: EmailStatus.AWAIT_SHIPMENT_SEARCH });
      this.logger.log(
        `[CORE_AGENT_SAGA] Updated email ${emailId} status to ${EmailStatus.AWAIT_SHIPMENT_SEARCH}`
      );

      // commit the transaction
      await queryRunner.commitTransaction();
      this.logger.log(`[CORE_AGENT_SAGA] Committed transaction for email ${emailId}`);
    } catch (error) {
      this.logger.error(
        `[CORE_AGENT_SAGA] Failed to process EMAIL_SAVED event for email ${emailId}. Rolling back transaction.`,
        {
          emailId,
          error: error.message,
          stack: error.stack
        }
      );
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Handle aggregation completion from external Aggregation module
   * Queues handle-request-message jobs for emails after aggregation completes
   *
   * This listener bridges the gap between aggregation completion and user intent processing.
   * When aggregation completes, we need to find the associated email and queue the final
   * processing step to handle user intents and generate responses.
   *
   * Uses pessimistic locking to ensure only AGGREGATING_EMAIL emails are processed
   * and transitions emails to EMAIL_AGGREGATED status before queuing processing.
   *
   * @param event - BatchDocumentAggregatedEvent containing aggregation results
   */
  @OnEvent(BATCH_DOCUMENT_AGGREGATED_EVENT)
  async onAggregationCompleted(event: BatchDocumentAggregatedEvent): Promise<void> {
    const { batchId, organizationId, result } = event;

    this.logger.log(
      `[CORE_AGENT_SAGA] BATCH_DOCUMENT_AGGREGATED_EVENT - Aggregation completed for batch ${batchId}: ${JSON.stringify(result)}`
    );

    // Use pessimistic locking for state machine safety
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Find email with pessimistic lock (without relations to avoid FOR UPDATE on outer join)
      const email = await queryRunner.manager.findOne(Email, {
        where: { gmailId: batchId },
        lock: { mode: "pessimistic_write" }
      });

      if (!email) {
        this.logger.warn(
          `[CORE_AGENT_SAGA] No email found for batchId ${batchId} - aggregation completion cannot be processed`
        );
        await queryRunner.rollbackTransaction();
        return;
      }

      // State machine guard: only process if AGGREGATING_EMAIL
      if (email.status !== EmailStatus.AGGREGATING_EMAIL) {
        this.logger.error(
          `[CORE_AGENT_SAGA] Email ${email.id} is in state ${email.status}, expected ${EmailStatus.AGGREGATING_EMAIL}. Already processed aggregation or not waiting for it. **Skipping.**`
        );
        await queryRunner.rollbackTransaction();
        return;
      }

      this.logger.log(
        `[CORE_AGENT_SAGA] Found email ${email.id} for completed batch ${batchId}, queuing handle-request-message`
      );

      // Queue handle request message job for post-aggregation processing
      const jobId = `handle-request-post-aggregation-${batchId}`;
      await this.handleRequestMessageQueue.add(jobId, {
        emailId: email.id,
        organizationId: organizationId // Use organizationId from the event
      });

      this.logger.log(
        `[CORE_AGENT_SAGA] Queued handle-request-message job ${jobId} for email ${email.id} after aggregation`
      );

      // Update email status to EMAIL_AGGREGATED
      await queryRunner.manager.update(Email, { id: email.id }, { status: EmailStatus.EMAIL_AGGREGATED });
      this.logger.log(
        `[CORE_AGENT_SAGA] Updated email ${email.id} status to ${EmailStatus.EMAIL_AGGREGATED}`
      );

      // Commit the transaction
      await queryRunner.commitTransaction();
      this.logger.log(`[CORE_AGENT_SAGA] Committed transaction for email ${email.id} aggregation completion`);
    } catch (error) {
      this.logger.error(
        `[CORE_AGENT_SAGA] Failed to process aggregation completion for batch ${batchId}. Rolling back transaction.`,
        {
          batchId,
          error: error.message,
          stack: error.stack
        }
      );
      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
