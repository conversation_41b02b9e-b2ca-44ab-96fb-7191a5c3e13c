import {
  Shipment,
  ValidateShipmentComplianceResponseDto,
  Organization,
  CommercialInvoice
} from "nest-modules";

/**
 * Comprehensive context interface containing all shipment-related business rule evaluations,
 * formatted display data, and service instances. This serves as the single source of truth
 * for all shipment response generation.
 */
export interface ShipmentContext {
  // Raw data
  shipment: Shipment;
  compliance: ValidateShipmentComplianceResponseDto;
  organization: Organization;

  // Business rule evaluations (evaluated once, used everywhere)
  canRush: boolean;
  canGenerateCAD: boolean;
  canGenerateRNSProof: boolean;
  isCompliant: boolean;
  isReleased: boolean;
  isSubmitted: boolean;
  canBeModified: boolean;
  isEntryUploaded: boolean;
  canUpdateEntry: boolean;
  isAllDocsReceived: boolean;

  // Detailed data for templates
  missingDocuments: string[];
  complianceErrors: string[];
  nonCompliantInvoices: CommercialInvoice[];
  rushBlockingReason: string;
  cadBlockingReason: string;
  rnsBlockingReason: string;

  // Formatted display values (from existing context builders)
  formattedCustomsStatus: string;
  shipmentIdentifiers: {
    hblNumber: string | null;
    cargoControlNumber: string | null;
    transactionNumber: string | null;
    containerNumbers: string[];
    formattedContainers: string;
    hasMultipleContainers: boolean;
    primaryContainer: string | null;
  };
  etaInformation: {
    etaPortValue?: string;
    portName?: string;
    etaDestinationValue?: string;
    destinationName?: string;
  };
  shippingInformation: {
    isTrackerOnline: boolean;
    shipmentStatus: string;
    isAir: boolean;
    trackingStatus?: string;
  };

  // Enhanced document and missing fields analysis
  documentDataStatus: {
    hasHBLDataForSubmission: boolean;
    hasAnEmfDataForSubmission: boolean;
    hasCompleteHBLData: boolean;
    hasCompleteAnEmfData: boolean;
    ciReceived: boolean;
    plReceived: boolean;
    hblStatus: "Received" | "Missing";
    anEmfStatus: "Received" | "Missing";
    ciPlStatus: "Received" | "Missing";
  };

  missingFieldsAnalysis: {
    missingIdentifiers: string[];
    missingMeasurements: string[];
    missingTiming: string[];
    missingLocations: string[];
    ogdFilingStatus: "pending" | "complete" | "not-required";
    formattedMissingFields: string[];
  };

  templateContext: {
    identifiers: {
      ccn: string;
      hbl: string;
      containers: string[];
      formattedContainers: string;
    };
    documentStatus: {
      hblStatus: "Received" | "Missing";
      anEmfStatus: "Received" | "Missing";
      ciPlStatus: "Received" | "Missing";
    };
    missingFields: {
      forPendingCommercialInvoice: string[];
      forPendingConfirmation: string[];
      formatted: string[];
    };
    timing: {
      etaPort: string | null;
      etaDestination: string | null;
      releaseDate: string | null;
      formattedEtaPort: string;
      formattedEtaDestination: string;
      formattedReleaseDate: string;
    };
    statusContext: {
      primaryMessage: string;
      secondaryDetails: string[];
      actionRequired: boolean;
    };
  };

  // Template-ready variables for smart templates
  smartTemplateContext: {
    statusResponseMessage: string;
    rushActionMessage?: string;
    documentBlockerReason?: string;
    transportMode: "OCEAN" | "AIR" | "TRUCK";
    hasETA: boolean;
    etaDate?: string;
    formattedReleaseDate?: string;
    transactionNumber?: string;

    // Document availability flags
    cadDocumentAvailable: boolean;
    rnsDocumentAvailable: boolean;

    // Document status tracking
    documentStatus: {
      hbl: "Received" | "Missing";
      anEmf: "Received" | "Missing";
      ciPl: "Received" | "Missing";
    };

    // Missing items list
    missingItems: string[];
    ogdFilingStatus: string;
  };

  // Backward compatibility - maintain existing document receipt status
  documentReceiptStatus: {
    hblReceived: boolean;
    anEmfReceived: boolean;
    ciReceived: boolean;
    plReceived: boolean;
  };
  missingFieldsStatus: string[];

  // User interaction flags (modified by intent handlers)
  directlyAsked: {
    [key: string]: boolean;
  };

  // Side effect results (populated by intent handlers)
  sideEffects: {
    cadDocument?: {
      fileName: string;
      mimeType: string;
      b64Data: string;
    };
    rnsProofData?: {
      content: string;
      releaseDate: string | null;
    };
    backofficeAlerts: {
      rushProcessingSent?: boolean;
      manualProcessingSent?: boolean;
      holdShipmentSent?: boolean;
    };
  };
}
