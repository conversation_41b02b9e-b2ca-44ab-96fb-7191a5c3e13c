import { RegisterQueueOptions } from "@nestjs/bullmq";
import { NestWorkerOptions } from "@nestjs/bullmq/dist/interfaces/worker-options.interface";
import { Job, JobsOptions, Queue } from "bullmq";
import { Processor, WorkerHost } from "@nestjs/bullmq";

export const DEFAULT_JOB_OPTIONS: JobsOptions = {
  attempts: 1,
  // backoff: {
  //   type: "exponential",
  //   delay: 1000,
  // },
  removeOnComplete: {
    age: 3600,
    count: 1000
  },
  removeOnFail: {
    age: 24 * 3600
  }
};

export const CORE_AGENT_WORKER_CONCURRENCY = parseInt(process.env.CORE_AGENT_WORKER_CONCURRENCY, 10) || 3;

export const DEFAULT_WORKER_OPTIONS: NestWorkerOptions = {
  concurrency: CORE_AGENT_WORKER_CONCURRENCY,
  limiter: {
    max: 100,
    duration: 1000
  }
};

/**
 * BullMQ wants a "flow" producer to be a different instance
 * from a "regular" producer. So we need to create a separate
 * producer for each flow.
 */
export enum CoreAgentFlowProducer {
  EMAIL_PROCESSING = "core-agent.email-processing"
}

export enum CoreAgentQueueName {
  IDENTIFY_SHIPMENT = "core-agent.identify-shipment",
  HANDLE_REQUEST_MESSAGE = "core-agent.handle-request-message",
  EVENT_EMITTER = "core-agent.event-emitter"
}

export const CORE_AGENT_QUEUES: Array<RegisterQueueOptions> = [
  {
    name: CoreAgentQueueName.IDENTIFY_SHIPMENT,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  },
  {
    name: CoreAgentQueueName.EVENT_EMITTER,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  },
  {
    name: CoreAgentQueueName.HANDLE_REQUEST_MESSAGE,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  }
];

// Identify Shipment Queue
export type IdentifyShipmentJobName = "identify-shipment";
export interface IdentifyShipmentJobData {
  emailId: number;
  organizationId: number;
  hasAttachments?: boolean;
}
export type IdentifyShipmentJob = Job<IdentifyShipmentJobData, null, IdentifyShipmentJobName>;
export type IdentifyShipmentQueue = Queue<IdentifyShipmentJobData, null, IdentifyShipmentJobName>;

// Event Emitter Queue
export interface EventEmitterJobData {
  eventType: string;
  eventData: Record<string, unknown>;
}
export type EventEmitterJob = Job<EventEmitterJobData, null, string>;
export type EventEmitterQueue = Queue<EventEmitterJobData, null, string>;

// Handle Request Message Queue
export interface HandleRequestMessageJobData {
  emailId: number;
  organizationId: number;
}
export type HandleRequestMessageJob = Job<HandleRequestMessageJobData, null, string>;
export type HandleRequestMessageQueue = Queue<HandleRequestMessageJobData, null, string>;

// Generic Core Agent Job
export type CoreAgentJobData = IdentifyShipmentJobData | EventEmitterJobData | HandleRequestMessageJobData;
export type CoreAgentJob = Job<CoreAgentJobData, null, string>;
