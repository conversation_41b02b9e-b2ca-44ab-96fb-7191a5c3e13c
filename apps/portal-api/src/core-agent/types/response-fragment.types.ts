import { ValidatedEmailAction } from "@/email/schemas/validated-email-intents.schema";

/**
 * Document processing result structure for fragment context
 */
export interface DocumentProcessingResult {
  /** Document ID */
  id?: number;
  /** Processing status */
  status: "success" | "failed" | "processed" | "pending";
  /** Whether processing was successful */
  success?: boolean;
  /** Whether document was processed */
  processed?: boolean;
  /** Error message if processing failed */
  error?: string;
  /** Document filename */
  filename?: string;
  /** Document type */
  documentType?: string;
}

/**
 * Side effects structure for context
 */
export interface ContextSideEffects {
  /** Document processing results */
  documentProcessing?: DocumentProcessingResult[];
  /** CAD document generation result */
  cadDocument?: {
    fileName: string;
    mimeType: string;
    b64Data: string;
  };
  /** RNS proof data */
  rnsProofData?: {
    content: string;
    releaseDate: string | null;
  };
  /** Backoffice alert statuses */
  backofficeAlerts?: {
    rushProcessingSent?: boolean;
    manualProcessingSent?: boolean;
    holdShipmentSent?: boolean;
  };
}

/**
 * Specific fragment context interface replacing generic Record<string, any>
 */
export interface FragmentContext {
  /** Error message for error fragments */
  errorMessage?: string;
  /** Document processing results */
  documentResults?: DocumentProcessingResult[];
  /** Missing fields formatted for display */
  missingFieldsFormatted?: string;
  /** Success indicator */
  success?: boolean;
  /** Error indicator */
  error?: string | boolean;
  /** Additional custom properties */
  [key: string]: unknown;
}

/**
 * Response fragment returned by intent handlers.
 * Each fragment represents a piece of the final response that should be rendered.
 */
export interface ResponseFragment {
  /** Template name to render (e.g., 'customs-status', 'cad-document-attached') */
  template: string;

  /** Priority for ordering fragments in final response (lower number = higher priority) */
  priority?: number;

  /** Additional context data specific to this fragment (merged with main context) */
  fragmentContext?: FragmentContext;
}

/**
 * Consolidated fragment context interface for template consolidation system.
 * Extends the base ShipmentContext with additional fields for consolidated templates.
 */
export interface ConsolidatedFragmentContext {
  /** Cumulative main messages from multiple intents */
  mainMessages?: Array<{
    content: string;
    priority: number;
    attachments?: { cadDocument?: any; rnsData?: any };
  }>;

  /** Validation issues structure */
  validationIssues?: {
    missingDocuments?: ("CI_PL" | "HBL" | "AN_EMF")[];
    missingFields?: ("weight" | "port_code" | "ccn" | "ogd_filing")[];
  };

  /** Explicit control over conditional fragments */
  showValidationIssues?: boolean;
  showDocumentStatus?: boolean;
  showAdditionalValidation?: boolean;

  /** Additional validation issues */
  additionalValidation?: {
    issues: string[];
  };
}

/**
 * Re-export existing ValidatedEmailAction as ValidatedIntent for consistency.
 * This aligns our intent handlers with the existing email processing system.
 */
export type ValidatedIntent = ValidatedEmailAction;
