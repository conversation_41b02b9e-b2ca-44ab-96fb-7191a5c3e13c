import * as path from "path";
import { <PERSON>, Module, forwardRef, Logger, DynamicModule } from "@nestjs/common";
import { BullModule } from "@nestjs/bullmq";
import { TypeOrmModule } from "@nestjs/typeorm";
import { TemplateManagerModule, FileBatch, Shipment } from "nest-modules";
import { CoreAgentService } from "./services/core-agent.service";
import { ShipmentModule } from "@/shipment/shipment.module";
import { AggregationModule } from "@/aggregation/aggregation.module";
import { EmailModule } from "@/email/email.module";
import { DocumentModule } from "@/document/document.module";
import { ImporterModule } from "@/importer/importer.module";
import { EmailIntentAnalysisService } from "./services/email-intent-analysis.service";
import { AnswerUserQueryService } from "./services/answer-user-query.service";
import { ShipmentIdentifierService } from "./services/shipment-identifier.service";
import { ShipmentFieldExtractionService } from "./services/shipment-field-extraction.service";
import { CoreAgentEmailSagaListener } from "./listeners/email-saga.listener";
import { CoreAgentTestController } from "./controllers/test/core-agent.test.controller";
import { ResponseServiceTestController } from "./controllers/test/response-service.test.controller";

import { EventEmitterProcessor } from "./processors/event-emitter.processor";
import { HandleRequestMessageProcessor } from "./processors/handle-request-message.processor";
import { IdentifyShipmentProcessor } from "./processors/identify-shipment.processor";
import { CORE_AGENT_QUEUES, CoreAgentFlowProducer } from "./types/queue.types";

// Fragment system services
import { ShipmentResponseService } from "./services/shipment-response.service";
import { DocumentProcessorService } from "./services/document-processor.service";
import { AgentContextModule } from "../agent-context";
import { IntentHandlerRegistry } from "./services/intent-handler-registry.service";

// Intent handlers - TEMPORARILY COMMENTED OUT FOR TESTING (except ProcessDocumentHandler)
// import { GetShipmentStatusHandler } from "./handlers/get-shipment-status.handler";
// import { RequestCADDocumentHandler } from "./handlers/request-cad-document.handler";
// import { RequestRushProcessingHandler } from "./handlers/request-rush-processing.handler";
// import { DocumentationComingHandler } from "./handlers/documentation-coming.handler";
// import { RequestRNSProofHandler } from "./handlers/request-rns-proof.handler";
// import { RequestManualProcessingHandler } from "./handlers/request-manual-processing.handler";
// import { RequestHoldShipmentHandler } from "./handlers/request-hold-shipment.handler";
import { ProcessDocumentHandler } from "./handlers/process-document.handler";
// import { UpdateShipmentHandler } from "./handlers/update-shipment.handler";
// import { AcknowledgeDocumentsHandler } from "./handlers/acknowledge-documents.handler";
// import { AcknowledgeMissingDocumentsHandler } from "./handlers/acknowledge-missing-documents.handler";

// UnifiedTemplateRenderer system
import { UnifiedTemplateRendererService } from "./services/unified-template-renderer.service";
// TEMPORARILY COMMENTED OUT FOR TESTING - these services use old API
// import { TemplateRendererTestService } from "./testing/template-renderer-test.service";
// import { ComprehensiveTemplateTestService } from "./testing/comprehensive-template-test.service";
// import { TemplateTestController } from "./controllers/template-test.controller";
import { TemplateMetricsService } from "./services/template-metrics.service";
import { CleanAgentContextModule } from "../clean-agent-context/clean-agent-context.module";

/**
 * @module CoreAgentModule
 * @description This module encapsulates the core AI agent logic for understanding
 * user queries, interacting with shipment data, validating compliance,
 * and generating responses.
 * It integrates with ShipmentModule and AggregationModule to access data and perform actions.
 * Marked as Global() to make its exported services available application-wide without explicit imports.
 */
@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([FileBatch, Shipment]),
    ShipmentModule,
    ImporterModule,
    forwardRef(() => AggregationModule),
    forwardRef(() => DocumentModule),
    BullModule.registerQueue(...CORE_AGENT_QUEUES),
    BullModule.registerFlowProducer({
      name: CoreAgentFlowProducer.EMAIL_PROCESSING
    }),
    TemplateManagerModule.forFeature({
      templatesPath: path.join(__dirname, "templates"),
      nunjucksConfig: {
        autoescape: true
      }
    }),
    forwardRef(() => EmailModule.forRoot()),
    AgentContextModule,
    CleanAgentContextModule
  ],
  providers: [
    CoreAgentService,
    EmailIntentAnalysisService,
    AnswerUserQueryService,
    ShipmentIdentifierService,
    ShipmentFieldExtractionService,
    CoreAgentEmailSagaListener,

    EventEmitterProcessor,
    HandleRequestMessageProcessor,
    IdentifyShipmentProcessor,

    // Fragment system services
    ShipmentResponseService,
    DocumentProcessorService,
    IntentHandlerRegistry,

    // Intent handlers - TEMPORARILY COMMENTED OUT FOR TESTING (except ProcessDocumentHandler)
    // GetShipmentStatusHandler,
    // RequestCADDocumentHandler,
    // RequestRushProcessingHandler,
    // DocumentationComingHandler,
    // RequestRNSProofHandler,
    // RequestManualProcessingHandler,
    // RequestHoldShipmentHandler,
    ProcessDocumentHandler,
    // UpdateShipmentHandler,
    // AcknowledgeDocumentsHandler,
    // AcknowledgeMissingDocumentsHandler,

    // UnifiedTemplateRenderer system
    UnifiedTemplateRendererService,
    // TEMPORARILY COMMENTED OUT FOR TESTING - these services use old API
    // TemplateRendererTestService,
    // ComprehensiveTemplateTestService,
    TemplateMetricsService,

    // Intent handler registry provider - TEMPORARILY ONLY ProcessDocumentHandler FOR TESTING
    {
      provide: "INTENT_HANDLERS",
      useFactory: (
        // getShipmentStatusHandler: GetShipmentStatusHandler,
        // requestCadDocumentHandler: RequestCADDocumentHandler,
        // requestRushProcessingHandler: RequestRushProcessingHandler,
        // documentationComingHandler: DocumentationComingHandler,
        // requestRnsProofHandler: RequestRNSProofHandler,
        // requestManualProcessingHandler: RequestManualProcessingHandler,
        // requestHoldShipmentHandler: RequestHoldShipmentHandler,
        processDocumentHandler: ProcessDocumentHandler,
        // updateShipmentHandler: UpdateShipmentHandler,
        // acknowledgeDocumentsHandler: AcknowledgeDocumentsHandler,
        // acknowledgeMissingDocumentsHandler: AcknowledgeMissingDocumentsHandler
      ) => [
        // getShipmentStatusHandler,
        // requestCadDocumentHandler,
        // requestRushProcessingHandler,
        // documentationComingHandler,
        // requestRnsProofHandler,
        // requestManualProcessingHandler,
        // requestHoldShipmentHandler,
        processDocumentHandler,
        // updateShipmentHandler,
        // acknowledgeDocumentsHandler,
        // acknowledgeMissingDocumentsHandler
      ],
      inject: [
        // GetShipmentStatusHandler,
        // RequestCADDocumentHandler,
        // RequestRushProcessingHandler,
        // DocumentationComingHandler,
        // RequestRNSProofHandler,
        // RequestManualProcessingHandler,
        // RequestHoldShipmentHandler,
        ProcessDocumentHandler,
        // UpdateShipmentHandler,
        // AcknowledgeDocumentsHandler,
        // AcknowledgeMissingDocumentsHandler
      ]
    }
  ],
  exports: [
    CoreAgentService,
    EmailIntentAnalysisService,
    AnswerUserQueryService,
    ShipmentIdentifierService,
    ShipmentResponseService,
    IntentHandlerRegistry,
    UnifiedTemplateRendererService
  ],
  controllers: [CoreAgentTestController, ResponseServiceTestController] // TemplateTestController temporarily commented out
})
export class CoreAgentModule {}
