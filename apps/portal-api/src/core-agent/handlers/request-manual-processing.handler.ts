import { Injectable } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";
import { EmailService } from "../../email/services/email.service";
import { UnifiedTemplateRendererService } from "../services/unified-template-renderer.service";

/**
 * <PERSON>les requests for manual processing of shipments.
 * Sends backoffice alerts and provides acknowledgment to users.
 */
@Injectable()
export class RequestManualProcessingHandler extends BaseIntentHandler {
  constructor(
    private readonly emailService: EmailService,
    private readonly unifiedTemplateRendererService: UnifiedTemplateRendererService
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "REQUEST_MANUAL_PROCESSING" as const,
    description: "User is requesting manual processing or intervention for their shipment",
    examples: [
      "Please cancel entry",
      "Can someone manually review this?",
      "I need manual processing",
      "Please handle this manually",
      "Cancel the entry submission"
    ],
    keywords: ["manual", "cancel", "review", "intervention", "manually", "cancel entry"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<string> {
    const instructions = this.extractInstructions(validatedIntent);

    this.logger.log(
      `Handling REQUEST_MANUAL_PROCESSING for shipment ${context.shipment?.id || "N/A"} with ${instructions.length} requests`
    );

    if (instructions.length === 0) {
      this.logger.error("Cannot process manual processing request: no instructions provided");
      throw new Error("No instructions provided for manual processing request");
    }

    try {
      // Send backoffice alert for manual processing request
      const alertData = await this.sendBackofficeAlert(
        "Manual Processing Request",
        context.shipment?.id || 0,
        instructions,
        this.emailService,
        context.organization.id
      );

      // Build template render options
      const renderOptions = {
        intentType: 'REQUEST_MANUAL_PROCESSING',
        backofficeAlerts: alertData.backofficeAlerts,
        acknowledgment: "We've received your request for manual processing and our team will review this accordingly. Thank you.",
        instructions: instructions,
        showValidationIssues: false,
        showDocumentStatus: false
      };

      this.logger.debug(
        `[REQUEST_MANUAL_PROCESSING] Template render options: ${JSON.stringify(renderOptions)}`
      );

      // Use unified template renderer service
      const renderedContent = await this.unifiedTemplateRendererService.renderTemplate(
        context.shipment?.id || 0,
        context.organization.id,
        validatedIntent,
        renderOptions
      );

      this.logger.log(
        `[REQUEST_MANUAL_PROCESSING] Generated rendered content for shipment ${context.shipment?.id || "N/A"}`
      );

      return renderedContent;
    } catch (error) {
      this.logger.error(
        `Failed to process manual processing request for shipment ${context.shipment?.id || "N/A"}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

}
