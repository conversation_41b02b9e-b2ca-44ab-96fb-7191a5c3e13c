import { Injectable } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContextWithServices } from "../../agent-context";
import { EmailService } from "../../email/services/email.service";
import { UnifiedTemplateRendererService } from "../services/unified-template-renderer.service";

/**
 * <PERSON>les requests to hold or cancel shipment processing.
 * Sends backoffice alerts and provides acknowledgment to users.
 */
@Injectable()
export class RequestHoldShipmentHandler extends BaseIntentHandler {
  constructor(
    private readonly emailService: EmailService,
    private readonly unifiedTemplateRendererService: UnifiedTemplateRendererService
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "REQUEST_HOLD_SHIPMENT" as const,
    description: "User is requesting to hold or cancel shipment processing",
    examples: [
      "Please hold processing until approval",
      "Do not submit entry until CAD approval",
      "Hold the shipment for verification",
      "Please cancel entry",
      "Cancel the shipment",
      "Put XXXXX on hold (where 'XXXXX' looks like a shipment identifier)",
      "Cancel XXXXX (where 'XXXXX' looks like a shipment identifier)"
    ],
    keywords: ["hold", "cancel", "wait", "approval", "verification", "before", "until", "pause", "stop"]
  };

  async handle(
    validatedIntent: ValidatedIntent,
    context: ShipmentContextWithServices
  ): Promise<string> {
    const instructions = this.extractInstructions(validatedIntent);

    this.logger.log(
      `Handling REQUEST_HOLD_SHIPMENT for shipment ${context.shipment?.id || "N/A"} with ${instructions.length} requests`
    );

    if (instructions.length === 0) {
      this.logger.error("Cannot process hold shipment request: no instructions provided");
      throw new Error("No instructions provided for hold shipment request");
    }

    try {
      // Send backoffice alert for hold shipment request
      const alertData = await this.sendBackofficeAlert(
        "Hold/Cancel Shipment Request",
        context.shipment?.id || 0,
        instructions,
        context._services.emailService,
        context.organization.id
      );

      // Build template render options
      const renderOptions = {
        intentType: 'REQUEST_HOLD_SHIPMENT',
        backofficeAlerts: alertData.backofficeAlerts,
        acknowledgment: "We've received your request to cancel/hold the entry. Our team has been notified and will take the necessary action.",
        instructions: instructions,
        showValidationIssues: false,
        showDocumentStatus: true
      };

      this.logger.debug(
        `[REQUEST_HOLD_SHIPMENT] Template render options: ${JSON.stringify(renderOptions)}`
      );

      // Use unified template renderer service
      const renderedContent = await this.unifiedTemplateRendererService.renderTemplate(
        context.shipment?.id || 0,
        context.organization.id,
        validatedIntent,
        renderOptions
      );

      this.logger.log(
        `[REQUEST_HOLD_SHIPMENT] Generated rendered content for shipment ${context.shipment?.id || "N/A"}`
      );

      return renderedContent;
    } catch (error) {
      this.logger.error(
        `Failed to process hold shipment request for shipment ${context.shipment?.id || "N/A"}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

}
