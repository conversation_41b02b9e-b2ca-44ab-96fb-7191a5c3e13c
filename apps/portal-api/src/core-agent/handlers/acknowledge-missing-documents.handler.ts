import { Injectable } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";
import { UnifiedTemplateRendererService } from "../services/unified-template-renderer.service";

@Injectable()
export class AcknowledgeMissingDocumentsHandler extends BaseIntentHandler {
  constructor(private readonly unifiedTemplateRendererService: UnifiedTemplateRendererService) {
    super();
  }
  readonly classificationMeta: IntentClassificationMeta = {
    intent: "ACKNOWLEDGE_MISSING_DOCUMENTS" as const,
    description: "Proactive notification about missing documents",
    examples: [
      "What documents do you need?",
      "What's missing for my shipment?",
      "Do you have everything you need?",
      "What documents are required?",
      "Is anything missing?"
    ],
    keywords: ["missing", "need", "required", "what documents", "do you have", "anything missing"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<string> {
    this.logger.log(`Handling ACKNOWLEDGE_MISSING_DOCUMENTS for shipment ${context.shipment?.id || "N/A"}`);

    try {
      // Build template render options
      const renderOptions = {
        intentType: 'ACKNOWLEDGE_MISSING_DOCUMENTS',
        hasMissingDocs: true,
        acknowledgment: "We will let you know if any additional documents are required.",
        showValidationIssues: false,
        showDocumentStatus: true
      };

      this.logger.debug(
        `[ACKNOWLEDGE_MISSING_DOCUMENTS] Template render options: ${JSON.stringify(renderOptions)}`
      );

      // Use unified template renderer service
      const renderedContent = await this.unifiedTemplateRendererService.renderTemplate(
        context.shipment?.id || 0,
        context.organization.id,
        validatedIntent,
        renderOptions
      );

      this.logger.log(
        `[ACKNOWLEDGE_MISSING_DOCUMENTS] Generated rendered content for shipment ${context.shipment?.id || "N/A"}`
      );

      return renderedContent;
    } catch (error) {
      this.logger.error(
        `Failed to process missing documents acknowledgment for shipment ${context.shipment?.id || "N/A"}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

}
