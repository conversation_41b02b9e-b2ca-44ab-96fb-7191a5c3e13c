import { Injectable, Inject } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";
import { Shipment } from "nest-modules";
import { RnsProofService } from "../../email/services/rns-proof-service";
import { RNSStatusChangeEmailSender } from "../../shipment/senders/rns-status-change-email.sender";
import { ImporterService } from "../../importer/importer.service";
import { ShipmentResponseService } from "../services/shipment-response.service";
import { UnifiedTemplateRendererService } from "../services/unified-template-renderer.service";

/**
 * Handles requests for RNS (Release Notification System) proof of release documents.
 * Evaluates business rules and generates RNS proof documents when possible.
 */
@Injectable()
export class RequestRNSProofHand<PERSON> extends BaseIntentHandler {
  constructor(
    private readonly rnsProofService: RnsProofService,
    private readonly rnsStatusChangeEmailSender: RNSStatusChangeEmailSender,
    private readonly importerService: ImporterService,
    @Inject(ShipmentResponseService) private readonly shipmentResponseService: ShipmentResponseService,
    private readonly unifiedTemplateRendererService: UnifiedTemplateRendererService
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "REQUEST_RNS_PROOF" as const,
    description: "User is requesting RNS proof of release or release notification document",
    examples: [
      "Can you send me the RNS proof of release?",
      "I need the release notification",
      "Please provide proof of release",
      "Send me the RNS document",
      "Do you have the release paperwork?"
    ],
    keywords: ["rns", "proof of release", "release notification", "release document", "release paperwork"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<string> {
    this.logger.log(`[REQUEST_RNS_PROOF] Handling request for shipment ${context.shipment?.id || "N/A"}`);

    if (!context.shipment) {
      this.logger.error("Cannot generate RNS proof: no shipment found in context");
      throw new Error("No shipment found for RNS proof generation");
    }

    try {
      // Generate RNS proof content if available
      let rnsProofData = undefined;
      if (context.smartTemplateContext.rnsDocumentAvailable) {
        try {
          const rnsContent = await this.generateRNSProofContent(
            context.shipment,
            context.smartTemplateContext,
            context.organization.id
          );
          rnsProofData = rnsContent.rnsProofData;
          this.logger.debug(`[REQUEST_RNS_PROOF] Generated RNS proof data for shipment ${context.shipment.id}`);
        } catch (error) {
          this.logger.error(`[REQUEST_RNS_PROOF] Failed to generate RNS proof data: ${error.message}`);
          // Continue without RNS data - template will handle the error state
        }
      } else {
        this.logger.debug(`[REQUEST_RNS_PROOF] RNS document not available for shipment ${context.shipment.id}`);
      }

      // Build template render options
      const renderOptions = {
        intentType: 'REQUEST_RNS_PROOF',
        rnsProofData,
        showValidationIssues: ["pending-commercial-invoice", "pending-confirmation"].includes(
          context.shipment.customsStatus
        ),
        showDocumentStatus: ["pending-commercial-invoice", "pending-confirmation"].includes(
          context.shipment.customsStatus
        )
      };

      this.logger.debug(`[REQUEST_RNS_PROOF] Template render options: ${JSON.stringify(renderOptions)}`);

      // Use unified template renderer service
      const renderedContent = await this.unifiedTemplateRendererService.renderTemplate(
        context.shipment.id,
        context.organization.id,
        validatedIntent,
        renderOptions
      );

      this.logger.log(
        `[REQUEST_RNS_PROOF] Generated rendered content for shipment ${context.shipment.id}`
      );

      return renderedContent;
    } catch (error) {
      this.logger.error(
        `Failed to generate RNS proof of release for shipment ${context.shipment.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }


  /**
   * Generate RNS proof content and return proof data
   */
  private async generateRNSProofContent(
    shipment: Shipment,
    smartTemplateContext: any,
    organizationId: number
  ): Promise<{ rnsProofData?: any }> {
    // Use the RnsProofService to get the RNS data
    const rnsProofData = await this.rnsProofService.getRNSProofOfRelease(shipment);

    if (!rnsProofData.isReleased || !rnsProofData.rnsResponse) {
      throw new Error("No release information found for this shipment");
    }

    // Get organization importer
    const importersResponse = await this.importerService.getImporters({
      organizationId: organizationId,
      limit: 1
    });
    const organizationImporter =
      importersResponse.importers.length > 0 ? importersResponse.importers[0] : null;

    // Use the existing prepareRNSStatusEmail method to generate the same email content
    const rnsEmailDto = this.rnsStatusChangeEmailSender.prepareRNSStatusEmail(
      shipment,
      organizationImporter,
      rnsProofData.rnsResponse,
      null, // no previous email for this context
      [] // no recipients needed for this context
    );

    // Instead of generating HTML here, provide the raw text content
    // Let the template handle HTML formatting
    const rnsProofText = rnsEmailDto.text;

    this.logger.log(`Successfully generated RNS proof of release for shipment ${shipment.id}.`);

    return {
      rnsProofData: {
        content: rnsProofText,
        releaseDate: rnsProofData.releaseDate
      }
    };
  }
}
