import { Injectable, BadRequestException } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { DataSource } from "typeorm";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext, ShipmentContextWithServices, ShipmentContextService } from "../../agent-context";
import { DocumentProcessorService } from "../services/document-processor.service";
import { Shipment, CommercialInvoice, OrganizationType, Importer, ImporterStatus } from "nest-modules";
import { UnifiedTemplateRendererService } from "../services/unified-template-renderer.service";

/**
 * Handles document processing requests and automatic entry submission.
 * This handler uses the agent-context module to reduce cross-module dependencies.
 */
@Injectable()
export class ProcessDocumentHandler extends BaseIntentHandler {
  constructor(
    private readonly documentProcessor: DocumentProcessorService,
    private readonly shipmentContextService: ShipmentContextService,
    @InjectDataSource() private readonly dataSource: DataSource,
    private readonly unifiedTemplateRendererService: UnifiedTemplateRendererService
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "PROCESS_DOCUMENT" as const,
    description:
      "The user is sending us documents as attachments and requesting for them to be processed as part of a shipment",
    examples: [
      "Please process the attached documents",
      "Process these documents for customs",
      "Submit the entry with these documents",
      "Use the attached paperwork",
      "Process the commercial invoice attached"
    ],
    keywords: ["process", "attached", "documents", "paperwork", "submit", "entry", "customs"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<string> {
    const instructions = this.extractInstructions(validatedIntent);

    this.logger.log(
      `Handling PROCESS_DOCUMENT for shipment ${context.shipment?.id || "N/A"} with ${instructions.length} instructions`
    );

    if (!context.shipment) {
      this.logger.error("Cannot process documents: no shipment found in context");
      throw new Error("No shipment found for document processing");
    }

    try {
      // Type guard to safely access services
      function hasServices(context: ShipmentContext): context is ShipmentContextWithServices {
        return "_services" in context && context._services !== undefined;
      }

      if (!hasServices(context)) {
        throw new BadRequestException("Context does not include required services");
      }

      const contextWithServices = context;

      // Handle post-document processing with customs flow
      const cadData = await this.handlePostDocumentProcessingWithCustomsFlow(
        context.shipment,
        contextWithServices
      );

      // Process document attachments
      const processedDocuments =
        validatedIntent.attachments && validatedIntent.attachments.length > 0
          ? await this.documentProcessor.processDocumentAttachments(validatedIntent)
          : await this.documentProcessor.fetchProcessedDocumentsFromDatabase(context);

      // Use pre-computed context data
      const hasAllRequiredDocuments = context.isAllDocsReceived;

      // Debug logging
      this.logger.debug(
        `🔍 DOCUMENT PROCESSING DEBUG for shipment ${context.shipment.id}: ` +
          `processedDocuments=${processedDocuments.length}, ` +
          `hasAllRequiredDocuments=${hasAllRequiredDocuments}, ` +
          `shipment.customsStatus=${context.shipment.customsStatus}`
      );

      // Build template render options
      const renderOptions = {
        intentType: 'PROCESS_DOCUMENT',
        hasProcessedDocuments: processedDocuments.length > 0,
        processedDocuments: processedDocuments,
        cadDocument: cadData?.cadDocument,
        showValidationIssues: ["pending-commercial-invoice", "pending-confirmation"].includes(
          context.shipment.customsStatus
        ),
        showDocumentStatus: ["pending-commercial-invoice", "pending-confirmation"].includes(
          context.shipment.customsStatus
        )
      };

      this.logger.debug(`[PROCESS_DOCUMENT] Template render options:`, renderOptions);

      // Use unified template renderer service
      const renderedContent = await this.unifiedTemplateRendererService.renderTemplate(
        context.shipment.id,
        context.organization.id,
        validatedIntent,
        renderOptions
      );

      this.logger.log(
        `[PROCESS_DOCUMENT] Generated rendered content for shipment ${context.shipment.id}`
      );

      return renderedContent;
    } catch (error) {
      this.logger.error(
        `Failed to process documents for shipment ${context.shipment.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Handle post-document processing with customs flow using agent-context services.
   * SIMPLIFIED: Uses IEntrySubmissionService.attemptShipmentSubmission() instead of manual workflow.
   */
  private async handlePostDocumentProcessingWithCustomsFlow(
    shipment: Shipment,
    context: ShipmentContextWithServices
  ): Promise<{ cadDocument?: any } | undefined> {
    this.logger.log(
      `🔄 SUBMISSION FLOW START: Handling post-document processing for shipment ${shipment.id} using agent-context services`
    );

    try {
      // Check if shipment is already submitted using context data
      if (context.isSubmitted) {
        this.logger.warn(
          `🛑 SUBMISSION FLOW STOPPED: Shipment ${shipment.id} is already submitted, skipping processing`
        );
        return;
      }

      // Use the agent-context submission service (replaces 100+ lines of manual logic)
      if (!context._services.entrySubmissionService) {
        throw new BadRequestException("Entry submission service not available");
      }

      const submissionResult = await context._services.entrySubmissionService.attemptShipmentSubmission(
        shipment,
        context.compliance,
        context.organization.id
      );

      if (submissionResult?.submissionError) {
        this.logger.error(`💥 SUBMISSION FLOW FAILED: ${submissionResult.submissionError}`);
        return;
      }

      // Refresh context after potential status changes
      await this.refreshContext(context);

      // For DEMO organizations, automatically generate CAD document after submission
      if (context.organization.organizationType === OrganizationType.DEMO) {
        this.logger.log(`🎯 DEMO CAD GENERATION: Attempting CAD generation for shipment ${shipment.id}`);
        const cadData = await this.handleDemoOrganizationCADGeneration(shipment, context);
        return cadData;
      }

      // Return empty object to match return type (no CAD document generated for non-demo orgs)
      return {};
    } catch (error) {
      this.logger.error(
        `💥 SUBMISSION FLOW EXCEPTION: Error in submission flow for shipment ${shipment.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Handle automatic CAD document generation for demo organizations.
   * SIMPLIFIED: Uses services from context instead of direct injection.
   */
  private async handleDemoOrganizationCADGeneration(
    shipment: Shipment,
    context: ShipmentContextWithServices
  ): Promise<{ cadDocument?: any; acknowledgment?: string }> {
    if (context.organization.organizationType !== OrganizationType.DEMO) {
      this.logger.warn(
        `🚫 DEMO CAD GENERATION: Shipment ${shipment.id} is not from a demo organization, skipping CAD generation`
      );
      return {};
    }

    this.logger.log(`🎯 DEMO CAD GENERATION: Starting CAD generation for demo shipment ${shipment.id}`);

    try {
      // Get commercial invoices for the shipment using direct query (since interface method doesn't exist yet)
      this.logger.log(`📋 DEMO CAD GENERATION: Retrieving commercial invoices for shipment ${shipment.id}`);
      const commercialInvoices = await this.getCommercialInvoicesForShipment(shipment.id);

      if (!commercialInvoices || commercialInvoices.length === 0) {
        this.logger.warn(
          `🚫 DEMO CAD GENERATION: No commercial invoices found for shipment ${shipment.id}. Deferring CAD generation.`
        );
        return {
          acknowledgment: "We will generate a CAD document once a commercial invoice is properly processed."
        };
      }

      this.logger.log(
        `✅ DEMO CAD GENERATION: Found ${commercialInvoices.length} commercial invoice(s) for shipment ${shipment.id}`
      );

      // Log commercial invoice data quality for debugging
      const invoiceDetails = commercialInvoices.map((inv) => ({
        id: inv.id,
        currency: inv.currency,
        linesCount: inv.commercialInvoiceLines?.length || 0,
        hasVendor: !!inv.vendor,
        hasPurchaser: !!inv.purchaser
      }));
      this.logger.debug(
        `📊 DEMO CAD GENERATION: Commercial invoice details for shipment ${shipment.id}: ${JSON.stringify(invoiceDetails)}`
      );

      // Get organization importer using direct repository access (bypasses REQUEST scope issues)
      this.logger.log(
        `🔍 DEMO CAD GENERATION: Looking up active importer for organization ${context.organization.id} (${context.organization.name})`
      );
      const organizationImporter = await this.getOrganizationImporter(context.organization.id);

      if (!organizationImporter) {
        this.logger.error(
          `❌ DEMO CAD GENERATION: No active importer found for organization ${context.organization.id} (${context.organization.name}). This will prevent CAD generation.`
        );
        return {};
      }

      this.logger.log(
        `✅ DEMO CAD GENERATION: Found active importer ${organizationImporter.id} (${organizationImporter.companyName}) for organization ${context.organization.id}`
      );

      if (!context._services.rnsStatusChangeEmailSender) {
        this.logger.error(
          `❌ DEMO CAD GENERATION: RNS status change email sender service not available for shipment ${shipment.id}. Check agent-context service injection.`
        );
        throw new BadRequestException("RNS status change email sender not available");
      }

      this.logger.log(
        `✅ DEMO CAD GENERATION: RNS status change email sender service available for shipment ${shipment.id}`
      );

      // Generate CAD attachment using context service
      this.logger.log(
        `🏭 DEMO CAD GENERATION: Generating CAD attachment for shipment ${shipment.id} with ${commercialInvoices.length} commercial invoice(s) and importer ${organizationImporter.id}`
      );
      const rawCadAttachment = await context._services.rnsStatusChangeEmailSender?.createCADAttachment(
        shipment,
        commercialInvoices,
        organizationImporter
      );

      if (!rawCadAttachment) {
        this.logger.error(
          `❌ DEMO CAD GENERATION: RNS status change email sender returned null/undefined CAD attachment for shipment ${shipment.id}. Check createCADAttachment method.`
        );
        return {};
      }

      this.logger.log(
        `📄 DEMO CAD GENERATION: Successfully generated raw CAD attachment for shipment ${shipment.id}, size: ${rawCadAttachment.b64Data?.length || 0} characters`
      );

      // Clean the base64 string
      this.logger.debug(
        `🧹 DEMO CAD GENERATION: Cleaning base64 data for shipment ${shipment.id}, original size: ${rawCadAttachment.b64Data?.length || 0}`
      );
      const cleanedCadAttachment = {
        ...rawCadAttachment,
        b64Data: rawCadAttachment.b64Data.replace(/[\s\r\n\t]/g, "")
      };
      this.logger.debug(
        `🧹 DEMO CAD GENERATION: Cleaned base64 data for shipment ${shipment.id}, new size: ${cleanedCadAttachment.b64Data?.length || 0}`
      );

      // Log final attachment properties before validation
      this.logger.debug(
        `📋 DEMO CAD GENERATION: Final attachment properties for shipment ${shipment.id}: fileName="${cleanedCadAttachment.fileName}", mimeType="${cleanedCadAttachment.mimeType}", hasB64Data=${!!cleanedCadAttachment.b64Data}`
      );

      // Validate using document processor (only non-agent-context service needed)
      this.logger.log(`🔍 DEMO CAD GENERATION: Validating CAD attachment for shipment ${shipment.id}`);
      if (this.documentProcessor.validateCADAttachment(cleanedCadAttachment)) {
        this.logger.log(
          `🎉 DEMO CAD GENERATION: Successfully generated and validated CAD document for demo shipment ${shipment.id}`
        );
        return { cadDocument: cleanedCadAttachment };
      } else {
        this.logger.error(
          `❌ DEMO CAD GENERATION: CAD attachment validation failed for shipment ${shipment.id}. Check DocumentProcessorService.validateCADAttachment method.`
        );
        return {};
      }
    } catch (error) {
      this.logger.error(
        `💥 DEMO CAD GENERATION: Error generating CAD document for shipment ${shipment.id}: ${error.message}`,
        error.stack
      );
      return {};
    }
  }

  /**
   * Get commercial invoices for a shipment using direct repository query.
   * This method is used for CAD generation in demo organizations.
   */
  private async getCommercialInvoicesForShipment(shipmentId: number): Promise<CommercialInvoice[]> {
    try {
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();

      try {
        const commercialInvoices = await queryRunner.manager.getRepository(CommercialInvoice).find({
          where: { shipment: { id: shipmentId } },
          relations: {
            vendor: true,
            purchaser: true,
            countryOfExport: true,
            stateOfExport: true,
            commercialInvoiceLines: {
              origin: true,
              originState: true,
              tt: true,
              tariffCode: true
            }
          }
        });

        this.logger.log(`Found ${commercialInvoices.length} commercial invoices for shipment ${shipmentId}`);
        return commercialInvoices;
      } finally {
        await queryRunner.release();
      }
    } catch (error) {
      this.logger.error(
        `Failed to get commercial invoices for shipment ${shipmentId}: ${error.message}`,
        error.stack
      );
      return [];
    }
  }

  /**
   * Get organization importer using direct repository access.
   * This bypasses the REQUEST-scoped ImporterService which doesn't work in processor contexts.
   * Based on the pattern from BaseEmailSender.getOrganizationImporters() that works in cron jobs.
   */
  private async getOrganizationImporter(organizationId: number): Promise<Importer | null> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();

    try {
      this.logger.debug(
        `🔍 Querying for active importer: organization=${organizationId}, status=${ImporterStatus.ACTIVE}`
      );

      const importer = await queryRunner.manager.findOne(Importer, {
        where: {
          organization: { id: organizationId },
          status: ImporterStatus.ACTIVE
        },
        relations: ["organization"],
        order: { id: "ASC" } // Get the first active importer
      });

      if (!importer) {
        // Check if there are any importers for this organization (regardless of status)
        const anyImporter = await queryRunner.manager.findOne(Importer, {
          where: { organization: { id: organizationId } },
          relations: ["organization"]
        });

        if (anyImporter) {
          this.logger.warn(
            `No ACTIVE importer found for organization ${organizationId}, but found importer ${anyImporter.id} with status '${anyImporter.status}'. Only ACTIVE importers can be used for CAD generation.`
          );
        } else {
          this.logger.warn(
            `No importers found at all for organization ${organizationId}. Organization may need to set up an importer first.`
          );
        }
      } else {
        this.logger.debug(
          `✅ Found active importer ${importer.id} (${importer.companyName}) for organization ${organizationId}`
        );
      }

      return importer;
    } catch (error) {
      this.logger.error(
        `💥 Database error while querying importer for organization ${organizationId}: ${error.message}`,
        error.stack
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Refresh context after submission to get latest data.
   * This ensures we have the most up-to-date shipment status after attemptShipmentSubmission().
   */
  private async refreshContext(context: ShipmentContextWithServices): Promise<void> {
    try {
      this.logger.debug(`🔄 Context refresh requested for shipment ${context.shipment.id}`);

      // Use ShipmentContextService to refresh the context with latest data
      await this.shipmentContextService.refreshShipmentInContext(context);

      this.logger.log(
        `✅ Successfully refreshed context for shipment ${context.shipment.id} ` +
          `- status: ${context.shipment.customsStatus}`
      );
    } catch (error) {
      this.logger.error(
        `⚠️ Failed to refresh context for shipment ${context.shipment?.id}: ${error.message}`,
        error.stack
      );

      // Don't throw - we can continue with the existing context
      // This follows the same error handling pattern as ShipmentContextService.refreshShipmentInContext
    }
  }

}
