import { Injectable } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext } from "@/agent-context";
import { ShipmentServicesAdapter } from "@/agent-context/services/shipment-services.adapter";
import { ShipmentFieldExtractionService } from "../services/shipment-field-extraction.service";
import { UnifiedTemplateRendererService } from "../services/unified-template-renderer.service";

/**
 * Handles shipment information updates (Port Code, Sublocation Code, Cargo Control Number).
 * Validates and updates shipment information through proper APIs.
 */
@Injectable()
export class UpdateShipmentHandler extends BaseIntentHandler {
  constructor(
    private readonly shipmentServicesAdapter: ShipmentServicesAdapter,
    private readonly shipmentFieldExtractionService: ShipmentFieldExtractionService,
    private readonly unifiedTemplateRendererService: UnifiedTemplateRendererService
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "UPDATE_SHIPMENT" as const,
    description:
      "User is requesting to update specific shipment information like port code, sublocation code, or cargo control number",
    examples: [
      "Update port code to 0123",
      "Change sublocation to 4567",
      "Update CCN to ABC123456789",
      "Shipment in-bond use attached manifest to clear",
      "Update cargo control number",
      "Change the port code"
    ],
    keywords: ["update", "change", "port", "sublocation", "ccn", "cargo control", "manifest", "in-bond"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<string> {
    const instructions = this.extractInstructions(validatedIntent);

    this.logger.log(
      `Handling UPDATE_SHIPMENT for shipment ${context.shipment?.id || "N/A"} with ${instructions.length} update requests`
    );

    if (!context.shipment) {
      this.logger.warn("Cannot update shipment information as no shipment is linked.");
      const renderOptions = {
        intentType: 'UPDATE_SHIPMENT',
        error: "We could not update the shipment information as we couldn't find the associated shipment from this email thread."
      };
      return await this.unifiedTemplateRendererService.renderTemplate(
        0,
        context.organization?.id || 0,
        validatedIntent,
        renderOptions
      );
    }

    if (instructions.length === 0) {
      this.logger.warn("No update instructions provided for shipment update request");
      const renderOptions = {
        intentType: 'UPDATE_SHIPMENT',
        error: "No specific update instructions were provided. Please specify what information you'd like to update."
      };
      return await this.unifiedTemplateRendererService.renderTemplate(
        context.shipment.id,
        context.organization.id,
        validatedIntent,
        renderOptions
      );
    }

    try {
      // Extract update information from instructions using LLM
      const instructionsText = instructions.join(" ");
      const updateData = await this.shipmentFieldExtractionService.extractShipmentFields(instructionsText);

      if (!updateData || Object.keys(updateData).length === 0) {
        this.logger.warn("Could not extract valid update data from instructions");
        const renderOptions = {
          intentType: 'UPDATE_SHIPMENT',
          error: "We could not identify the specific information to update. Please provide clear update instructions with the new values."
        };
        return await this.unifiedTemplateRendererService.renderTemplate(
          context.shipment.id,
          context.organization.id,
          validatedIntent,
          renderOptions
        );
      }

      this.logger.log(
        `Attempting to update shipment ${context.shipment.id} with data: ${JSON.stringify(updateData)}`
      );

      // Validate the update data
      const validationResult = this.validateUpdateData(updateData);
      if (!validationResult.isValid) {
        this.logger.warn(`Update data validation failed: ${validationResult.errors.join(", ")}`);
        const renderOptions = {
          intentType: 'UPDATE_SHIPMENT',
          error: `The update information provided is not valid: ${validationResult.errors.join(", ")}`
        };
        return await this.unifiedTemplateRendererService.renderTemplate(
          context.shipment.id,
          context.organization.id,
          validatedIntent,
          renderOptions
        );
      }

      // Update the shipment through agent-context shipment service adapter
      await this.shipmentServicesAdapter.editShipment(context.shipment.id, updateData);

      this.logger.log(
        `Successfully updated shipment ${context.shipment.id} with: ${Object.keys(updateData).join(", ")}`
      );

      // Build template render options for success
      const renderOptions = {
        intentType: 'UPDATE_SHIPMENT',
        updateResult: {
          success: true,
          acknowledgment: "The shipment information has been updated successfully.",
          shipmentId: context.shipment.id,
          updatedFields: Object.keys(updateData),
          updateData: updateData
        },
        showValidationIssues: false,
        showDocumentStatus: false
      };

      this.logger.debug(`[UPDATE_SHIPMENT] Template render options: ${JSON.stringify(renderOptions)}`);

      // Use unified template renderer service
      const renderedContent = await this.unifiedTemplateRendererService.renderTemplate(
        context.shipment.id,
        context.organization.id,
        validatedIntent,
        renderOptions
      );

      this.logger.log(
        `[UPDATE_SHIPMENT] Generated rendered content for shipment ${context.shipment?.id || "N/A"}`
      );

      return renderedContent;
    } catch (error) {
      this.logger.error(
        `Failed to update shipment ${context.shipment.id}. Error: ${error.message}`,
        error.stack
      );

      const renderOptions = {
        intentType: 'UPDATE_SHIPMENT',
        error: "We encountered an error while updating the shipment information. Our team will review this shortly."
      };
      return await this.unifiedTemplateRendererService.renderTemplate(
        context.shipment.id,
        context.organization.id,
        validatedIntent,
        renderOptions
      );
    }
  }

  /**
   * Validate update data according to business rules.
   * The LLM extraction already handles format validation, but we add additional business rule checks.
   */
  private validateUpdateData(updateData: Record<string, any>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate port code format: ^0[0-9]{3}$ (double-check LLM extraction)
    if (updateData.portCode && !/^0\d{3}$/.test(updateData.portCode)) {
      errors.push("Port code must be in format 0XXX (e.g., 0123)");
    }

    // Validate sublocation format: ^[0-9]{4}$ (double-check LLM extraction)
    if (updateData.subLocation && !/^\d{4}$/.test(updateData.subLocation)) {
      errors.push("Sublocation code must be 4 digits (e.g., 1234)");
    }

    // Validate CCN format (basic validation)
    if (updateData.cargoControlNumber && updateData.cargoControlNumber.length < 3) {
      errors.push("Cargo Control Number must be at least 3 characters");
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

}
