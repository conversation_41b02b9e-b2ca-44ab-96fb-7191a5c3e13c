import { Injectable } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";
import { UnifiedTemplateRendererService } from "../services/unified-template-renderer.service";

@Injectable()
export class AcknowledgeDocumentsHandler extends BaseIntentHandler {
  constructor(private readonly unifiedTemplateRendererService: UnifiedTemplateRendererService) {
    super();
  }
  readonly classificationMeta: IntentClassificationMeta = {
    intent: "ACKNOWLEDGE_DOCUMENTS" as const,
    description: "Acknowledging receipt of documents from importer",
    examples: [
      "Here are the missing documents",
      "I'm sending you the commercial invoice",
      "Attached is the packing list",
      "Documents are attached",
      "Please find the requested documents attached"
    ],
    keywords: ["attached", "here is", "sending", "documents", "invoice", "packing list", "find attached"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<string> {
    this.logger.log(`Handling ACKNOWLEDGE_DOCUMENTS for shipment ${context.shipment?.id || "N/A"}`);

    try {
      // Build template render options
      const renderOptions = {
        intentType: 'ACKNOWLEDGE_DOCUMENTS',
        allDocsReceived: true,
        acknowledgment: "Thank you for providing the documents. We will process them accordingly.",
        showValidationIssues: false,
        showDocumentStatus: false
      };

      this.logger.debug(
        `[ACKNOWLEDGE_DOCUMENTS] Template render options: ${JSON.stringify(renderOptions)}`
      );

      // Use unified template renderer service
      const renderedContent = await this.unifiedTemplateRendererService.renderTemplate(
        context.shipment?.id || 0,
        context.organization.id,
        validatedIntent,
        renderOptions
      );

      this.logger.log(
        `[ACKNOWLEDGE_DOCUMENTS] Generated rendered content for shipment ${context.shipment?.id || "N/A"}`
      );

      return renderedContent;
    } catch (error) {
      this.logger.error(
        `Failed to process document acknowledgment for shipment ${context.shipment?.id || "N/A"}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

}
