import { Injectable, Inject, Logger } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";
import { AnswerUserQueryService } from "../services/answer-user-query.service";
import { ShipmentResponseService } from "../services/shipment-response.service";
import { ClassifyQuestionCategoryOutput } from "../schemas/classify-question-category.schema";
import { QUESTION_CATEGORIES } from "../constants/question-categories.constants";
import { UnifiedTemplateRendererService } from "../services/unified-template-renderer.service";

/**
 * Handles shipment status inquiries and specific questions.
 * Classifies questions and generates appropriate response fragments using consolidated templates.
 *
 * MIGRATION: Updated to use consolidated template system while preserving all existing functionality.
 */
@Injectable()
export class GetShipmentStatusHandler extends BaseIntentHandler {
  constructor(
    @Inject(AnswerUserQueryService) private readonly answerUserQueryService: AnswerUserQueryService,
    @Inject(ShipmentResponseService) private readonly shipmentResponseService: ShipmentResponseService,
    private readonly unifiedTemplateRendererService: UnifiedTemplateRendererService
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "GET_SHIPMENT_STATUS" as const,
    description:
      "Checks and other inquiries on this shipment, including customs processing or clearance status. Covers any information about the shipment that is not covered by other intents.",
    examples: [
      "Where's my load CCN123456789 at? Did it clear customs yet?",
      "Any update on HBL987? What's the hold up?",
      "What's the current status of my shipment?",
      "Has my cargo been released?",
      "When will customs clear this?",
      "What is the transaction number?",
      "Provide the transaction number for shipment 829710033556",
      "Send the transaction number for shipment SH25050202T0 to the sender",
      "When will it arrive?",
      "Are any documents missing?"
    ],
    keywords: [
      "status",
      "update",
      "where",
      "customs",
      "clearance",
      "released",
      "cleared",
      "progress",
      "eta",
      "arrival",
      "missing",
      "documents",
      "transaction",
      "provide",
      "send"
    ]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<string> {
    const instructions = this.extractInstructions(validatedIntent);

    this.logger.log(
      `[GET_SHIPMENT_STATUS] Handling request for shipment ${context.shipment.id} with ${instructions.length} questions`
    );

    // Step 1: Classify the user's questions using LLM
    const classifications = await this.classifyQuestions(instructions);
    this.logger.debug(
      `[GET_SHIPMENT_STATUS] Question classifications: ${JSON.stringify([...classifications])}`
    );

    // Step 2: Build template render options
    const renderOptions = await this.buildTemplateRenderOptions(classifications, context, instructions);
    this.logger.debug(`[GET_SHIPMENT_STATUS] Template render options: ${JSON.stringify(renderOptions)}`);

    // Step 3: Use unified template renderer service
    const renderedContent = await this.unifiedTemplateRendererService.renderTemplate(
      context.shipment.id,
      context.organization.id,
      validatedIntent,
      renderOptions
    );

    this.logger.log(
      `[GET_SHIPMENT_STATUS] Generated rendered content for shipment ${context.shipment.id}`
    );

    return renderedContent;
  }

  /**
   * Classify user questions using the LLM service
   * PRESERVED: Existing classification logic unchanged
   */
  private async classifyQuestions(questions: string[]): Promise<Set<string>> {
    const classifications = new Set<string>();

    if (questions.length === 0) {
      // Default to general status if no specific questions
      classifications.add(QUESTION_CATEGORIES.GENERAL_STATUS.category);
      return classifications;
    }

    try {
      // Classify each question
      const classificationPromises = questions.map((q) => this.answerUserQueryService.classifyQuestion(q));
      const results = await Promise.all(classificationPromises);

      // Collect unique categories
      results.forEach((result: ClassifyQuestionCategoryOutput) => {
        if (result && result.category) {
          classifications.add(result.category);
        }
      });
    } catch (error: any) {
      this.logger.error(`[GET_SHIPMENT_STATUS] Error classifying questions: ${error.message}`, error.stack);
      // Fallback to general status
      classifications.add(QUESTION_CATEGORIES.GENERAL_STATUS.category);
    }

    return classifications;
  }

  /**
   * Build template render options for unified template renderer
   * MIGRATION: Replaces fragment-based logic with template render options
   */
  private async buildTemplateRenderOptions(
    classifications: Set<string>,
    context: ShipmentContext,
    questions: string[]
  ): Promise<any> {
    // Gather specific answers based on classifications
    const specificAnswers: any = {};

    // Handle specific question answers
    if (classifications.has(QUESTION_CATEGORIES.ETA.category)) {
      const etaAnswer = await this.generateEtaAnswer(context, questions);
      if (etaAnswer) {
        specificAnswers.etaAnswer = etaAnswer;
        this.logger.debug(`[GET_SHIPMENT_STATUS] Added ETA answer: ${etaAnswer}`);
      }
    }

    if (classifications.has(QUESTION_CATEGORIES.TRANSACTION_NUMBER.category)) {
      const transactionAnswer = this.generateTransactionNumberAnswer(context);
      if (transactionAnswer) {
        specificAnswers.transactionAnswer = transactionAnswer;
        this.logger.debug(`[GET_SHIPMENT_STATUS] Added transaction number answer: ${transactionAnswer}`);
      }
    }

    if (classifications.has(QUESTION_CATEGORIES.RELEASE_STATUS.category)) {
      const releaseAnswer = this.generateReleaseStatusAnswer(context);
      if (releaseAnswer) {
        specificAnswers.releaseAnswer = releaseAnswer;
        this.logger.debug(`[GET_SHIPMENT_STATUS] Added release status answer: ${releaseAnswer}`);
      }
    }

    if (classifications.has(QUESTION_CATEGORIES.SHIPPING_STATUS.category)) {
      const shippingAnswer = this.generateShippingStatusAnswer(context);
      if (shippingAnswer) {
        specificAnswers.shippingAnswer = shippingAnswer;
        this.logger.debug(`[GET_SHIPMENT_STATUS] Added shipping status answer: ${shippingAnswer}`);
      }
    }

    // Determine status message requirements
    const shouldAddCustomsStatus = this.shouldAddCustomsStatusResponse(classifications);
    const shouldAddMissingItems = this.shouldAddMissingItemsFragment(context);
    const hasReleaseStatusAnswer = classifications.has(QUESTION_CATEGORIES.RELEASE_STATUS.category);

    const shouldShowStatusMessage = (shouldAddCustomsStatus || shouldAddMissingItems) && !hasReleaseStatusAnswer;
    if (shouldShowStatusMessage) {
      const statusMessage = this.buildStatusMessage(context);
      specificAnswers.statusMessage = statusMessage;
      this.logger.debug(`[GET_SHIPMENT_STATUS] Added status message: ${statusMessage}`);
    }

    return {
      intentType: 'GET_SHIPMENT_STATUS',
      specificAnswers,
      showValidationIssues: shouldAddMissingItems,
      showDocumentStatus: this.shouldAddDetailsFragment(classifications) &&
        ["pending-commercial-invoice", "pending-confirmation"].includes(context.shipment.customsStatus)
    };
  }


  /**
   * Determine if customs status response should be added
   * PRESERVED: Existing logic unchanged
   */
  private shouldAddCustomsStatusResponse(classifications: Set<string>): boolean {
    return (
      classifications.has(QUESTION_CATEGORIES.GENERAL_STATUS.category) ||
      classifications.has(QUESTION_CATEGORIES.CUSTOMS_STATUS.category) ||
      classifications.has(QUESTION_CATEGORIES.SHIPMENT_ISSUES.category) ||
      classifications.has(QUESTION_CATEGORIES.RELEASE_STATUS.category) ||
      classifications.size === 0 // Fallback for no classifications
    );
  }

  /**
   * Determine if details fragment should be added - always add for GET_SHIPMENT_STATUS
   * PRESERVED: Existing logic unchanged
   */
  private shouldAddDetailsFragment(classifications: Set<string>): boolean {
    // Always add details fragment for any GET_SHIPMENT_STATUS question
    // This ensures transaction number, ETA, and other specific questions always show current status
    return classifications.size > 0;
  }

  /**
   * Determine if missing items fragment should be added - for shipments requiring client attention
   * PRESERVED: Existing logic unchanged
   */
  private shouldAddMissingItemsFragment(context: ShipmentContext): boolean {
    // Show missing items for statuses that require client attention
    const statusesRequiringClientAction = ["pending-commercial-invoice", "pending-confirmation"];
    return statusesRequiringClientAction.includes(context.shipment?.customsStatus);
  }

  /**
   * Generate ETA-specific answer
   * PRESERVED: Existing logic unchanged
   */
  private async generateEtaAnswer(context: ShipmentContext, questions: string[]): Promise<string | null> {
    const { smartTemplateContext } = context;

    if (smartTemplateContext?.hasETA && smartTemplateContext?.etaDate) {
      return `The estimated time of arrival (ETA) is ${smartTemplateContext.etaDate}.`;
    } else if (context.shipment?.etaPort) {
      return `The estimated time of arrival (ETA) is ${context.shipment.etaPort}.`;
    } else {
      return "The ETA is not yet available. We will update you once we receive this information from the carrier.";
    }
  }

  /**
   * Generate Transaction Number answer
   * PRESERVED: Existing logic unchanged
   */
  private generateTransactionNumberAnswer(context: ShipmentContext): string | null {
    if (context.shipment?.transactionNumber) {
      return `The transaction number is ${context.shipment.transactionNumber}.`;
    } else {
      return "The transaction number is not yet available. It will be provided once the entry is submitted to customs.";
    }
  }

  /**
   * Generate Release Status answer
   * PRESERVED: Existing logic unchanged
   */
  private generateReleaseStatusAnswer(context: ShipmentContext): string | null {
    const { shipment, smartTemplateContext } = context;

    if (shipment?.customsStatus === "released") {
      if (smartTemplateContext?.formattedReleaseDate && smartTemplateContext.formattedReleaseDate !== "TBD") {
        return `Yes, your shipment has been released by CBSA on ${smartTemplateContext.formattedReleaseDate}.`;
      } else if (shipment.releaseDate) {
        return `Yes, your shipment has been released by CBSA on ${shipment.releaseDate}.`;
      } else {
        return "Yes, your shipment has been released by CBSA.";
      }
    } else {
      // Use dynamic status information instead of hardcoded string
      const statusMessage =
        smartTemplateContext?.statusResponseMessage ||
        context.formattedCustomsStatus ||
        "Status information is not available";

      return `Your shipment has not yet been released by customs. Current status: ${statusMessage}. We will notify you as soon as it is cleared.`;
    }
  }

  /**
   * Generate Shipping Status answer
   * PRESERVED: Existing logic unchanged
   */
  private generateShippingStatusAnswer(context: ShipmentContext): string | null {
    const { shipment } = context;

    if (shipment?.trackingStatus) {
      return `The shipping status is: ${shipment.trackingStatus.toLowerCase().replace(/_/g, " ")}.`;
    } else {
      return "Shipping status information is not currently available.";
    }
  }

  /**
   * Format missing fields for display in status messages
   * PRESERVED: Existing logic unchanged (kept for potential future use)
   */
  private formatMissingFields(context: ShipmentContext): string {
    const formattedFields = context.missingFieldsAnalysis?.formattedMissingFields || [];
    return formattedFields.join("\n");
  }
}
