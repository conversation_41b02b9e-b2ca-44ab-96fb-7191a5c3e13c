import { Injectable, Inject } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";
import { CommercialInvoice, Shipment } from "nest-modules";
import { RNSStatusChangeEmailSender } from "../../shipment/senders/rns-status-change-email.sender";
import { ImporterService } from "../../importer/importer.service";
import { DataSource } from "typeorm";
import { ShipmentResponseService } from "../services/shipment-response.service";
import { UnifiedTemplateRendererService } from "../services/unified-template-renderer.service";

/**
 * <PERSON>les requests for CAD (Canada Entry) documents.
 * Evaluates business rules and generates CAD documents when possible.
 */
@Injectable()
export class RequestCADDocumentHandler extends BaseIntentHandler {
  constructor(
    private readonly rnsStatusChangeEmailSender: RNSStatusChangeEmailSender,
    private readonly importerService: ImporterService,
    private readonly dataSource: DataSource,
    @Inject(ShipmentResponseService) private readonly shipmentResponseService: ShipmentResponseService,
    private readonly unifiedTemplateRendererService: UnifiedTemplateRendererService
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "REQUEST_CAD_DOCUMENT" as const,
    description:
      "User is requesting for the Customs Accounting Document, or CAD, for this shipment to be generated and sent to them.",
    examples: [
      "Can you send me the CAD document?",
      "I need the Canada Entry",
      "Please provide the customs entry document",
      "Send me the entry paperwork"
    ],
    keywords: ["cad", "canada entry", "customs entry", "entry document", "paperwork"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<string> {
    this.logger.log(`[REQUEST_CAD_DOCUMENT] Handling request for shipment ${context.shipment?.id || "N/A"}`);

    if (!context.shipment) {
      this.logger.error("Cannot generate CAD document: no shipment found in context");
      throw new Error("No shipment found for CAD document generation");
    }

    try {
      // Generate CAD document if available
      let cadDocument = undefined;
      if (context.smartTemplateContext.cadDocumentAvailable) {
        try {
          const cadData = await this.generateCADDocument(context.shipment);
          cadDocument = cadData.cadDocument;
          this.logger.log(
            `[PDF_ATTACHMENT_TRACKING] CAD document generated for shipment ${context.shipment.id}`
          );
        } catch (error) {
          this.logger.error(`[REQUEST_CAD_DOCUMENT] Failed to generate CAD document: ${error.message}`);
          // Continue without CAD document - template will handle the error state
        }
      }

      // Build template render options
      const renderOptions = {
        intentType: 'REQUEST_CAD_DOCUMENT',
        cadDocument,
        showValidationIssues: ["pending-commercial-invoice", "pending-confirmation"].includes(context.shipment.customsStatus),
        showDocumentStatus: ["pending-commercial-invoice", "pending-confirmation"].includes(context.shipment.customsStatus)
      };

      this.logger.debug(
        `[REQUEST_CAD_DOCUMENT] Template render options: ${JSON.stringify(renderOptions)}`
      );

      // Use unified template renderer service
      const renderedContent = await this.unifiedTemplateRendererService.renderTemplate(
        context.shipment.id,
        context.organization.id,
        validatedIntent,
        renderOptions
      );

      this.logger.log(
        `[REQUEST_CAD_DOCUMENT] Generated rendered content for shipment ${context.shipment.id}`
      );

      return renderedContent;
    } catch (error) {
      this.logger.error(
        `Failed to generate CAD document for shipment ${context.shipment.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }


  /**
   * Generate CAD document and return document data
   */
  private async generateCADDocument(shipment: Shipment): Promise<{ cadDocument?: any }> {
    // Get commercial invoices for the shipment
    const commercialInvoices = await this.getCommercialInvoices(shipment.id);

    if (commercialInvoices.length === 0) {
      throw new Error("No commercial invoices found for this shipment");
    }

    let organizationImporter = null;
    try {
      // Get organization importer
      const importersResponse = await this.importerService.getImporters({
        organizationId: shipment.organizationId,
        limit: 1
      });
      organizationImporter = importersResponse.importers.length > 0 ? importersResponse.importers[0] : null;
    } catch (error) {
      this.logger.error(
        `Error fetching organization importer for organizationId ${shipment.organizationId}: ${error.message}`,
        error.stack
      );
      organizationImporter = null;
    }

    try {
      // Generate CAD attachment
      this.logger.log(`Requesting to create CAD attachment for shipment ${shipment.id}`);
      const cadAttachment = await this.rnsStatusChangeEmailSender.createCADAttachment(
        shipment,
        commercialInvoices,
        organizationImporter
      );

      // Clean the base64 string
      const cleanedCadAttachment = {
        ...cadAttachment,
        b64Data: cadAttachment.b64Data.replace(/[\s\r\n\t]/g, "")
      };

      this.logger.log(`Successfully generated CAD document for shipment ${shipment.id}.`);

      return {
        cadDocument: cleanedCadAttachment
      };
    } catch (error) {
      this.logger.error(
        `Failed to generate CAD document for shipment ${shipment.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Get commercial invoices for the shipment using TypeORM
   */
  private async getCommercialInvoices(shipmentId: number): Promise<CommercialInvoice[]> {
    try {
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();

      try {
        const commercialInvoices = await queryRunner.manager.getRepository(CommercialInvoice).find({
          where: { shipment: { id: shipmentId } },
          relations: {
            vendor: true,
            purchaser: true,
            countryOfExport: true,
            stateOfExport: true,
            commercialInvoiceLines: {
              origin: true,
              originState: true,
              tt: true,
              tariffCode: true
            }
          }
        });

        this.logger.log(`Found ${commercialInvoices.length} commercial invoices for shipment ${shipmentId}`);
        return commercialInvoices;
      } finally {
        await queryRunner.release();
      }
    } catch (error) {
      this.logger.error(
        `Failed to get commercial invoices for shipment ${shipmentId}: ${error.message}`,
        error.stack
      );
      return [];
    }
  }
}
