import { Injectable } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";
import { UnifiedTemplateRendererService } from "../services/unified-template-renderer.service";

/**
 * Handles user notifications about incoming documentation.
 * Provides acknowledgment and sets expectations.
 */
@Injectable()
export class DocumentationComingHandler extends BaseIntentHandler {
  constructor(private readonly unifiedTemplateRendererService: UnifiedTemplateRendererService) {
    super();
  }
  readonly classificationMeta: IntentClassificationMeta = {
    intent: "DOCUMENTATION_COMING" as const,
    description: "User is notifying that additional documents are coming or will be sent soon",
    examples: [
      "I'll send you the missing documents shortly",
      "Documents are coming",
      "Paperwork will be forwarded tomorrow",
      "Additional documentation incoming"
    ],
    keywords: ["coming", "sending", "forward", "incoming", "tomorrow", "shortly", "soon"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<string> {
    this.logger.log(`Handling DOCUMENTATION_COMING for shipment ${context.shipment?.id || "N/A"}`);

    try {
      // Build template render options
      const renderOptions = {
        intentType: 'DOCUMENTATION_COMING',
        acknowledgment: "Thank you for letting us know. We'll keep an eye out for the additional documentation.",
        showValidationIssues: false,
        showDocumentStatus: true
      };

      this.logger.debug(
        `[DOCUMENTATION_COMING] Template render options: ${JSON.stringify(renderOptions)}`
      );

      // Use unified template renderer service
      const renderedContent = await this.unifiedTemplateRendererService.renderTemplate(
        context.shipment?.id || 0,
        context.organization.id,
        validatedIntent,
        renderOptions
      );

      this.logger.log(
        `[DOCUMENTATION_COMING] Generated rendered content for shipment ${context.shipment?.id || "N/A"}`
      );

      return renderedContent;
    } catch (error) {
      this.logger.error(
        `Failed to process documentation coming for shipment ${context.shipment?.id || "N/A"}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

}
