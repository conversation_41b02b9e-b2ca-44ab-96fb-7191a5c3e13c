import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Logger } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { CoreAgentQueueName, EventEmitterJob, DEFAULT_WORKER_OPTIONS } from "../types/queue.types";

/**
 * Event Emitter Processor
 *
 * Generic BullMQ processor that handles event emission jobs.
 * Takes eventType and eventData from job payload and emits them via EventEmitter2.
 *
 * This processor enables decoupling of event emission from job processing,
 * allowing FlowProducer sequences to emit events as part of their workflow.
 *
 * Responsibilities:
 * - Process EVENT_EMITTER jobs from BullMQ queue
 * - Extract eventType and eventData from job payload
 * - Emit events via EventEmitter2 for other listeners to handle
 * - Provide logging for event emission tracking
 */
@Processor({ name: CoreAgentQueueName.EVENT_EMITTER }, DEFAULT_WORKER_OPTIONS)
export class EventEmitterProcessor extends WorkerHost {
  private readonly logger = new Logger(EventEmitterProcessor.name);

  constructor(private readonly eventEmitter: EventEmitter2) {
    super();
  }

  async process(job: EventEmitterJob): Promise<void> {
    const { eventType, eventData } = job.data;

    this.logger.log(`[EVENT_EMITTER] Emitting ${eventType} event`);
    this.logger.debug(`Event data: ${JSON.stringify(eventData)}`);
    this.eventEmitter.emit(eventType, eventData);
    this.logger.log(`[EVENT_EMITTER] Successfully emitted ${eventType} event`);
  }
}
