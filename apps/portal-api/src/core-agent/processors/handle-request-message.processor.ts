import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Logger } from "@nestjs/common";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectDataSource } from "@nestjs/typeorm";
import { DataSource, QueryRunner } from "typeorm";
import { TemplateManagerService } from "nest-modules";

// Entity imports
import {
  Email,
  EmailStatus,
  File,
  FIND_DOCUMENT_RELATIONS,
  FIND_ORGANIZATION_RELATIONS,
  FIND_SHIPMENT_RELATIONS,
  Organization,
  Shipment,
  EMAIL_INTENTS
} from "nest-modules";

// Services
import { EmailService } from "../../email/services/email.service";
import { FileBatchService } from "../../document/services/file-batch.service";
import { EmailIntentAnalysisService } from "../services/email-intent-analysis.service";
import { RNSStatusChangeEmailSender } from "../../shipment/senders/rns-status-change-email.sender";

// Event imports
import { EmailEvent } from "../../email/types/event.types";
import { EmailManualReviewRequiredEvent } from "../../email/dto/event.dto";
import { EmailManualReviewReason } from "../../email/types/email.types";

// Removed ShipmentResponseService - now using unified templates for all intents
import { UnifiedTemplateRendererService } from "../services/unified-template-renderer.service";
import { ShipmentContextService } from "@/agent-context";
import { CleanAgentContextService } from "@/clean-agent-context/clean-agent-context.service";
import { IntentHandlerRegistry } from "../services/intent-handler-registry.service";

// Queue types
import { CoreAgentQueueName, HandleRequestMessageJob, DEFAULT_WORKER_OPTIONS } from "../types/queue.types";

// Utility functions
import { generateEmailContent } from "../../email/utils/generate-email-content";
import { generateRequest } from "../../email/utils/generate-request";

// Schemas and types
import { MessageContent } from "../schemas/message-content.schema";
import { ValidatedEmailAction } from "../../email/schemas/validated-email-intents.schema";

/**
 * Handle Request Message Processor
 *
 * BullMQ processor that consolidates email intent processing into a single core-agent processor.
 * This processor handles the complete email intent processing pipeline using unified templates:
 *
 * 1. Load email + attachments + aggregation results from database
 * 2. Extract/classify intents using existing core-agent services
 * 3. Combine all PROCESS_DOCUMENT intents into single object with instruction list
 * 4. Handle each intent using intent handlers that return complete HTML strings
 * 5. Combine multiple intent responses into single email response
 * 6. Send unified template email response
 *
 * All intents now use UnifiedTemplateRendererService internally to generate final HTML responses.
 * This processor is called in two scenarios:
 * 1. Directly for emails without attachments (no aggregation needed)
 * 2. After aggregation completion for emails with attachments
 */
@Processor({ name: CoreAgentQueueName.HANDLE_REQUEST_MESSAGE }, DEFAULT_WORKER_OPTIONS)
export class HandleRequestMessageProcessor extends WorkerHost {
  private readonly logger = new Logger(HandleRequestMessageProcessor.name);

  constructor(
    @InjectDataSource() private readonly dataSource: DataSource,
    private readonly moduleRef: ModuleRef,
    private readonly eventEmitter: EventEmitter2,
    private readonly emailIntentAnalysisService: EmailIntentAnalysisService,
    // Removed shipmentResponseService - now using unified templates for all intents
    private readonly unifiedTemplateRendererService: UnifiedTemplateRendererService,
    private readonly shipmentContextService: ShipmentContextService,
    private readonly cleanAgentContextService: CleanAgentContextService,
    private readonly intentHandlerRegistry: IntentHandlerRegistry,
    private readonly templateManagerService: TemplateManagerService
  ) {
    super();
  }

  /**
   * Intent priority mapping to ensure PROCESS_DOCUMENT runs before status-dependent handlers.
   * Lower numbers indicate higher priority (run first).
   */
  private readonly INTENT_PRIORITIES = {
    PROCESS_DOCUMENT: 1, // Must run first to update status
    GET_SHIPMENT_STATUS: 2, // Uses current status
    REQUEST_RUSH_PROCESSING: 2, // Uses current status
    REQUEST_CAD_DOCUMENT: 2, // Uses current status
    REQUEST_RNS_PROOF: 2, // Uses current status
    UPDATE_SHIPMENT: 2, // May depend on processed documents
    CREATE_SHIPMENT: 2, // May depend on processed documents
    UPDATE_COMMERCIAL_INVOICE: 2, // May depend on processed documents
    CREATE_COMMERCIAL_INVOICE: 2, // May depend on processed documents
    UPDATE_CERTIFICATE_OF_ORIGIN: 2, // May depend on processed documents
    CREATE_CERTIFICATE_OF_ORIGIN: 2, // May depend on processed documents
    DOCUMENTATION_COMING: 3, // Acknowledgment handlers
    ACKNOWLEDGE_DOCUMENTS: 3, // Acknowledgment handlers
    ACKNOWLEDGE_MISSING_DOCUMENTS: 3,
    REQUEST_MANUAL_PROCESSING: 3, // Fallback handlers
    REQUEST_HOLD_SHIPMENT: 3, // Administrative handlers
    UNSORTED: 99, // Lowest priority
    UNKNOWN: 99, // Lowest priority
    SPAM: 99 // Lowest priority
  } as const;

  /**
   * Get the priority for a given intent type.
   * @param intent - The intent type
   * @returns Priority number (lower = higher priority)
   */
  private getIntentPriority(intent: string): number {
    return this.INTENT_PRIORITIES[intent as keyof typeof this.INTENT_PRIORITIES] || 50; // Default priority
  }

  /**
   * Sort intents by priority to ensure correct execution order.
   * PROCESS_DOCUMENT always runs first, followed by status-dependent handlers.
   * @param intents - Array of validated intents to sort
   * @returns Sorted array with highest priority intents first
   */
  private sortIntentsByPriority(intents: ValidatedEmailAction[]): ValidatedEmailAction[] {
    return [...intents].sort((a, b) => {
      const priorityA = this.getIntentPriority(a.intent);
      const priorityB = this.getIntentPriority(b.intent);
      return priorityA - priorityB; // Lower number = higher priority
    });
  }

  async process(job: HandleRequestMessageJob): Promise<void> {
    const { emailId, organizationId } = job.data;

    this.logger.log(
      `[HANDLE_REQUEST_MESSAGE] Processing email ${emailId} for organization ${organizationId}`
    );

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. Load organization with relations
      const organization = await queryRunner.manager.findOne(Organization, {
        where: { id: organizationId },
        relations: FIND_ORGANIZATION_RELATIONS
      });
      if (!organization) {
        throw new Error(`Organization not found: ${organizationId}`);
      }

      // 2. Create synthetic request context for request-scoped services
      const contextId = ContextIdFactory.create();
      const requestContext = generateRequest(null, organization);
      this.moduleRef.registerRequestByContextId(requestContext, contextId);

      // 3. Resolve request-scoped services
      const fileBatchService = await this.moduleRef.resolve(FileBatchService, contextId, { strict: false });
      const emailService = await this.moduleRef.resolve(EmailService, contextId, { strict: false });
      const rnsStatusChangeEmailSender = await this.moduleRef.resolve(RNSStatusChangeEmailSender, contextId, {
        strict: false
      });
      await new Promise((resolve) => process.nextTick(resolve)); // Wait for dependencies

      // 4. Load email with validation
      const email = await emailService.getEmailById(emailId, queryRunner);
      if (!email) {
        throw new Error(`Email not found: ${emailId}`);
      }
      if (email.organization?.id !== organization.id) {
        throw new Error(`Email organization mismatch: ${email.organization?.id} !== ${organizationId}`);
      }

      this.logger.log(`Processing email ${email.id} with status: ${email.status}`);

      // 5. Load attachments (gracefully handle missing FileBatch)
      let attachments: Array<File> = [];
      try {
        attachments = await fileBatchService.getFiles(email.gmailId, {
          documents: {
            ...FIND_DOCUMENT_RELATIONS,
            fields: true
          }
        });
        this.logger.log(`Found ${attachments.length} attachments for email ${email.id}`);

        // Log aggregation status for each document/file
        if (attachments.length > 0) {
          const documentStatuses = attachments.map((attachment) => {
            const fileName = attachment.name || "unknown";
            if (attachment.documents && attachment.documents.length > 0) {
              const document = attachment.documents[0]; // Take first document per file
              const status = document.status || "unknown";
              const documentType = document.documentType?.name || "unclassified";
              return `${fileName}(${documentType}/${status})`;
            } else {
              return `${fileName}(no_document/unprocessed)`;
            }
          });
          this.logger.log(`[AGGREGATION_STATUS] Document processing results: ${documentStatuses.join(" ")}`);
        }
      } catch (error) {
        this.logger.warn(`Could not load attachments for email ${email.gmailId}: ${error.message}`);
      }

      // 6. Load previous emails for context
      const previousEmails = await emailService.getPreviousEmails(emailId, queryRunner);
      this.logger.debug(`Found ${previousEmails.length} previous emails in thread`);

      // 7. Generate message content for intent analysis
      const emailContent = generateEmailContent(email, previousEmails, attachments);
      const messageContent: MessageContent = {
        subject: emailContent.subject,
        text: emailContent.text,
        messageHistory: emailContent.emailHistories,
        attachments: emailContent.attachments?.map((att) => ({
          filename: att.filename,
          contentType: att.documentType,
          extractedData: att.extractedData
        }))
      };

      // 8. Extract and validate user intents
      this.logger.log(`Analyzing email intents for email ${email.id}`);
      const validatedIntents = await this.emailIntentAnalysisService.analyzeEmailIntents(messageContent);

      if (!validatedIntents.intents || validatedIntents.intents.length === 0) {
        this.logger.log(`No intents found for email ${email.id}, sending fallback response`);
        await this.sendNoIntentsResponse(email, emailService, queryRunner);
        await queryRunner.commitTransaction();
        return;
      }

      this.logger.log(
        `Found ${validatedIntents.intents.length} intents: ${validatedIntents.intents.map((i) => i.intent).join(", ")}`
      );

      // 8.5. Double-pass classification for problematic intents
      const refinedIntents = await this.refineProblematicIntents(validatedIntents.intents, messageContent);
      this.logger.log(
        `After double-pass refinement: ${refinedIntents.length} intents: ${refinedIntents.map((i) => i.intent).join(", ")}`
      );

      // NEW: Log instructions for any intents that are still meaningless after refinement
      const leftoverMeaninglessIntents = refinedIntents.filter((intent) =>
        ["UNSORTED", "SPAM", "UNKNOWN"].includes(intent.intent)
      );
      if (leftoverMeaninglessIntents.length > 0) {
        this.logger.log(
          `[HANDLE_REQUEST_MESSAGE] Found ${leftoverMeaninglessIntents.length} leftover meaningless intents after refinement:`
        );
        leftoverMeaninglessIntents.forEach((intent, idx) => {
          const instructionText =
            intent.instructions && intent.instructions.length > 0
              ? intent.instructions.join(" ").replace(/\s+/g, " ").trim()
              : "(no instructions)";
          this.logger.log(
            `[HANDLE_REQUEST_MESSAGE] Meaningless Intent ${idx + 1} (${intent.intent}): ${instructionText}`
          );
        });
      }

      // 8.6. Check if all refined intents are still meaningless
      const meaningfulIntents = refinedIntents.filter(
        (intent) => !["UNSORTED", "SPAM", "UNKNOWN"].includes(intent.intent)
      );
      if (meaningfulIntents.length === 0 && refinedIntents.length > 0) {
        this.logger.log(
          `[HANDLE_REQUEST_MESSAGE] All ${refinedIntents.length} intents are still meaningless after double-pass, sending fallback response`
        );
        await this.sendNoIntentsResponse(email, emailService, queryRunner);
        await queryRunner.commitTransaction();
        return;
      }

      // 9. Combine PROCESS_DOCUMENT intents into single object
      const processedIntents = this.combineProcessDocumentIntents(refinedIntents);
      this.logger.log(`After combining PROCESS_DOCUMENT intents: ${processedIntents.length} intents`);

      // 10. Ensure we have at least one PROCESS_DOCUMENT intent if we have aggregated documents
      const finalIntents = await this.ensureProcessDocumentIntent(processedIntents, email, contextId);
      this.logger.log(`After ensuring PROCESS_DOCUMENT intent: ${finalIntents.length} intents`);

      // 11. Sort intents by priority to ensure PROCESS_DOCUMENT runs first
      const sortedIntents = this.sortIntentsByPriority(finalIntents);
      this.logger.log(
        `After sorting by priority: ${sortedIntents.map((i) => `${i.intent}(${this.getIntentPriority(i.intent)})`).join(", ")}`
      );

      // 12. Process intents using unified template system
      await this.processIntentsWithUnifiedTemplates(
        sortedIntents, // Use sorted intents instead of finalIntents
        email,
        organization,
        contextId,
        emailService,
        rnsStatusChangeEmailSender,
        queryRunner
      );

      // 13. Update email status
      await emailService.editEmail(
        email.id,
        {
          status: EmailStatus.RESPONDED,
          userIntents: validatedIntents.intents
        },
        queryRunner
      );

      await queryRunner.commitTransaction();
      this.logger.log(
        `[HANDLE_REQUEST_MESSAGE] Successfully processed email ${emailId} with ${finalIntents.length} intents`
      );
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `[HANDLE_REQUEST_MESSAGE] Failed to process email ${emailId}: ${error.message}`,
        error.stack
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Process intents using the unified template system
   */
  private async processIntentsWithUnifiedTemplates(
    intents: ValidatedEmailAction[],
    email: Email,
    organization: Organization,
    contextId: ReturnType<typeof ContextIdFactory.create>,
    emailService: EmailService,
    rnsStatusChangeEmailSender: RNSStatusChangeEmailSender,
    queryRunner: QueryRunner
  ): Promise<void> {
    this.logger.log(
      `[HANDLE_REQUEST_MESSAGE] Processing ${intents.length} intents using unified template system for email ${email.id}`
    );

    try {
      // 1. Find shipment for context building
      const shipment = await this.findShipmentForEmail(email, contextId, emailService, queryRunner);
      if (!shipment) {
        await this.sendFallbackResponse(email, emailService, queryRunner);
        return;
      }

      // 2. Log shipment status for the record (ensure fresh data)
      const freshShipment = await queryRunner.manager.findOne(Shipment, {
        where: { id: shipment.id },
        select: ["id", "hblNumber", "status", "trackingStatus", "customsStatus", "lastEditDate"]
      });

      if (freshShipment) {
        this.logger.log(
          `[HANDLE_REQUEST_MESSAGE] Shipment status for email ${email.id}: ` +
            `Shipment ${freshShipment.id} (HBL: ${freshShipment.hblNumber || "N/A"}) - ` +
            `Status: ${freshShipment.status}, Tracking: ${freshShipment.trackingStatus}, ` +
            `Customs: ${freshShipment.customsStatus}, Last Updated: ${freshShipment.lastEditDate}`
        );

        // Log associated documents for this shipment
        const shipmentDocuments = await queryRunner.manager
          .createQueryBuilder()
          .select(["doc.id", "doc.status", "doc.name"])
          .addSelect("docType.name", "document_type_name")
          .from("document", "doc")
          .leftJoin("document_type", "docType", 'doc."documentTypeId" = docType.id')
          .where("doc.shipmentId = :shipmentId", { shipmentId: freshShipment.id })
          .getRawMany();

        if (shipmentDocuments.length > 0) {
          const documentStatuses = shipmentDocuments.map((doc) => {
            const documentName = doc.doc_name || "unknown";
            const docType = doc.document_type_name || "unclassified";
            const status = doc.doc_status || "unknown";
            return `${documentName}(${docType}/${status})`;
          });
          this.logger.log(
            `[HANDLE_REQUEST_MESSAGE] Shipment ${freshShipment.id} documents: ${documentStatuses.join(" ")}`
          );
        } else {
          this.logger.log(
            `[HANDLE_REQUEST_MESSAGE] Shipment ${freshShipment.id} has no associated documents`
          );
        }
      } else {
        this.logger.warn(
          `[HANDLE_REQUEST_MESSAGE] Could not fetch fresh shipment data for shipment ${shipment.id}`
        );
      }

      // 3. Build comprehensive shipment context using CleanAgentContext with email context
      this.logger.log(
        `[HANDLE_REQUEST_MESSAGE] Building unified template context for shipment ${shipment.id} with email context`
      );

      // Create a fresh request context for CleanAgentContext
      const cleanAgentRequest = generateRequest(null, organization);
      const unifiedContext = await this.cleanAgentContextService.getUnifiedTemplateContext(
        shipment.id,
        organization.id,
        cleanAgentRequest,
        {
          emailId: email.id,
          gmailId: email.gmailId,
          includeEmailAttachments: true
        }
      );

      // Build legacy context for backward compatibility with existing intent handlers
      const context = await this.shipmentContextService.buildContext(shipment.id, organization.id);

      // 3b. Inject services into context for intent handlers
      this.shipmentContextService.injectServices(context, {
        emailService: emailService,
        rnsStatusChangeEmailSender: rnsStatusChangeEmailSender
      });

      // Note: unifiedContext is now available for enhanced template rendering
      // Can be accessed directly or integrated with existing templateContext
      this.logger.debug(
        `Unified context prepared with ${unifiedContext.PROCESSED_DOCUMENTS?.length || 0} processed documents`
      );

      // 3. Filter out meaningless intents and process meaningful ones to generate fragments
      const meaningfulIntentsOnly = intents.filter(
        (intent) => !["UNSORTED", "SPAM", "UNKNOWN"].includes(intent.intent)
      );

      if (meaningfulIntentsOnly.length < intents.length) {
        this.logger.log(
          `[HANDLE_REQUEST_MESSAGE] Filtered out ${intents.length - meaningfulIntentsOnly.length} meaningless intents, processing ${meaningfulIntentsOnly.length} meaningful intents`
        );
      }

      // 4. Process intents to get response strings
      const allResponses = await this.processIntentsToResponses(meaningfulIntentsOnly, context, email);

      // 5. Combine multiple responses if needed
      const responseHtml = this.combineIntentResponses(allResponses, meaningfulIntentsOnly, email);

      // 6. Check if response is empty or only contains greeting (no meaningful content)
      if (this.isEmptyResponse(responseHtml)) {
        this.logger.warn(
          `[HANDLE_REQUEST_MESSAGE] No meaningful content generated for email ${email.id}, escalating to manual review`
        );
        await this.sendFallbackResponse(email, emailService, queryRunner);
        return;
      }

      // 7. Send the response
      await this.sendUnifiedResponse(email, responseHtml, emailService, queryRunner);
    } catch (error) {
      this.logger.error(
        `[HANDLE_REQUEST_MESSAGE] Failed to process intents for email ${email.id}: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Find shipment for email using multiple discovery strategies
   */
  private async findShipmentForEmail(
    email: Email,
    contextId: ReturnType<typeof ContextIdFactory.create>,
    emailService: EmailService,
    queryRunner: QueryRunner
  ): Promise<Shipment | null> {
    // Strategy 1: Try to get shipment from email thread
    const threadShipment = await this.tryGetThreadShipment(email, emailService);
    if (threadShipment) {
      return threadShipment;
    }

    // Strategy 2: Check FileBatch for shipment and associate with thread
    const fileBatchShipment = await this.tryGetFileBatchShipment(email, contextId, emailService, queryRunner);
    if (fileBatchShipment) {
      return fileBatchShipment;
    }

    this.logger.warn(`[HANDLE_REQUEST_MESSAGE] No shipment found for email ${email.id}`);
    return null;
  }

  /**
   * Try to get shipment from email thread
   */
  private async tryGetThreadShipment(email: Email, emailService: EmailService): Promise<Shipment | null> {
    try {
      const shipment = await emailService.getEmailThreadShipment(email.threadId);
      if (shipment) {
        this.logger.log(`[HANDLE_REQUEST_MESSAGE] Found shipment ${shipment.id} from email thread`);
      }
      return shipment;
    } catch (error) {
      this.logger.warn(`[HANDLE_REQUEST_MESSAGE] Could not get shipment from thread: ${error.message}`);
      return null;
    }
  }

  /**
   * Try to get shipment from FileBatch and associate with thread
   */
  private async tryGetFileBatchShipment(
    email: Email,
    contextId: ReturnType<typeof ContextIdFactory.create>,
    emailService: EmailService,
    queryRunner: QueryRunner
  ): Promise<Shipment | null> {
    try {
      const fileBatchService = await this.moduleRef.resolve(FileBatchService, contextId, { strict: false });
      const fileBatch = await fileBatchService.getFileBatch(email.gmailId);

      if (!fileBatch?.shipmentId) {
        return null;
      }

      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Found shipment ${fileBatch.shipmentId} in FileBatch`);

      // Get the full shipment entity
      const shipmentEntity = await queryRunner.manager.findOne(Shipment, {
        where: { id: fileBatch.shipmentId },
        relations: FIND_SHIPMENT_RELATIONS
      });

      if (!shipmentEntity) {
        this.logger.error(`[HANDLE_REQUEST_MESSAGE] Shipment ${fileBatch.shipmentId} not found in database`);
        return null;
      }

      // Associate shipment with email thread
      await this.associateShipmentWithThread(email, shipmentEntity, emailService, queryRunner);
      return shipmentEntity;
    } catch (error) {
      this.logger.warn(`[HANDLE_REQUEST_MESSAGE] Error checking FileBatch for shipment: ${error.message}`);
      return null;
    }
  }

  /**
   * Associate shipment with email thread
   */
  private async associateShipmentWithThread(
    email: Email,
    shipment: Shipment,
    emailService: EmailService,
    queryRunner: QueryRunner
  ): Promise<void> {
    try {
      await emailService.setEmailThreadShipment(
        email.threadId,
        shipment,
        queryRunner,
        true // skipOrganizationCheck since we're in system context
      );
      this.logger.log(
        `[HANDLE_REQUEST_MESSAGE] Associated thread ${email.threadId} with shipment ${shipment.id}`
      );
    } catch (error) {
      this.logger.error(
        `[HANDLE_REQUEST_MESSAGE] Failed to associate shipment with thread: ${error.message}`
      );
      throw error;
    }
  }

  /**
   * Process all intents to generate response strings
   */
  private async processIntentsToResponses(
    intents: ValidatedEmailAction[],
    context: any,
    email: Email
  ): Promise<string[]> {
    const allResponses: string[] = [];

    for (const intent of intents) {
      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Processing intent: ${intent.intent}`);

      // Log detailed information for specific intent types
      this.logIntentDetails(intent, email);

      try {
        const response = await this.handleSingleIntent(intent, context);
        if (response && response.trim().length > 0) {
          allResponses.push(response);
          this.logger.log(`[HANDLE_REQUEST_MESSAGE] Generated response for intent ${intent.intent}`);
        }

        // Log document processing results for PROCESS_DOCUMENT intents
        if (intent.intent === 'PROCESS_DOCUMENT') {
          this.logDocumentProcessingResults(intent, response);
        }
      } catch (error) {
        this.logger.error(
          `[HANDLE_REQUEST_MESSAGE] Error handling intent ${intent.intent}: ${error.message}`,
          error.stack
        );

        this.logDocumentProcessingFailure(intent);
        // Skip this intent entirely - don't add error responses
        // If all intents fail, isEmptyResponse() will catch it and escalate to manual review
      }
    }

    return allResponses;
  }

  /**
   * Handle a single intent and return response string
   */
  private async handleSingleIntent(intent: ValidatedEmailAction, context: any): Promise<string> {
    const handler = this.intentHandlerRegistry.getHandler(intent.intent);

    if (!handler) {
      this.logger.warn(`No handler found for intent: ${intent.intent}`);
      return this.createFallbackResponse(intent);
    }

    return await handler.handle(intent, context);
  }

  /**
   * Log detailed information for specific intent types
   */
  private logIntentDetails(intent: ValidatedEmailAction, email: Email): void {
    if (intent.intent === "PROCESS_DOCUMENT" && intent.attachments?.length > 0) {
      this.logger.log(
        `[HANDLE_REQUEST_MESSAGE] PROCESS_DOCUMENT intent has ${intent.attachments.length} documents to process:`
      );
      intent.attachments.forEach((attachment, index) => {
        this.logger.log(
          `[HANDLE_REQUEST_MESSAGE] Document ${index + 1}: ${attachment.filename} (${attachment.documentType || "unknown type"})`
        );
      });
    }

    if (intent.intent === "UNSORTED") {
      this.logger.warn(
        `[HANDLE_REQUEST_MESSAGE] UNSORTED intent detected for email ${email.id}. Intent details: ${JSON.stringify(
          {
            intent: intent.intent,
            instructions: intent.instructions,
            attachments:
              intent.attachments?.map((att) => ({
                filename: att.filename,
                documentType: att.documentType
              })) || [],
            hasShippingData: !!intent.shippingData
          }
        )}`
      );
    }
  }

  /**
   * Log document processing results
   */
  private logDocumentProcessingResults(intent: ValidatedEmailAction, response: string): void {
    if (intent.intent === "PROCESS_DOCUMENT" && intent.attachments?.length > 0) {
      // Extract processing status from response content
      const documentStatuses = this.extractDocumentProcessingStatusFromResponse(intent, response);
      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Document processing results: ${documentStatuses.join(" ")}`);

      // Detailed logging for each document
      intent.attachments.forEach((attachment, index) => {
        const status = documentStatuses[index] || "processed";
        this.logger.log(`[HANDLE_REQUEST_MESSAGE] Document "${attachment.filename}": ${status}`);
      });
    }
  }

  /**
   * Extract document processing status from response string
   */
  private extractDocumentProcessingStatusFromResponse(intent: ValidatedEmailAction, response: string): string[] {
    if (!intent.attachments || intent.attachments.length === 0) {
      return [];
    }

    const documentStatuses = intent.attachments.map((attachment) => {
      const filename = attachment.filename || "unknown";
      // For unified template responses, parse the response text for processing indicators
      if (response.includes("successfully processed") || response.includes("uploaded") || response.includes("received")) {
        return `${filename}(processed/success)`;
      } else if (response.includes("failed") || response.includes("error") || response.includes("could not process")) {
        return `${filename}(processed/failed)`;
      } else {
        return `${filename}(processed/unknown)`;
      }
    });

    return documentStatuses;
  }

  /**
   * Log document processing failure
   */
  private logDocumentProcessingFailure(intent: ValidatedEmailAction): void {
    if (intent.intent === "PROCESS_DOCUMENT" && intent.attachments?.length > 0) {
      const failedStatuses = intent.attachments.map(() => "failed");
      this.logger.log(
        `[HANDLE_REQUEST_MESSAGE] Document processing results (all failed due to handler error): ${failedStatuses.join(" ")}`
      );
    }
  }

  /**
   * Combines multiple PROCESS_DOCUMENT intents into a single intent with merged instructions
   */
  private combineProcessDocumentIntents(intents: ValidatedEmailAction[]): ValidatedEmailAction[] {
    const processDocumentIntents = intents.filter((intent) => intent.intent === "PROCESS_DOCUMENT");
    // Also include UNSORTED intents that have attachments (they're likely document processing tasks)
    const unsortedDocumentIntents = intents.filter(
      (intent) => intent.intent === "UNSORTED" && intent.attachments && intent.attachments.length > 0
    );
    const documentIntents = [...processDocumentIntents, ...unsortedDocumentIntents];
    const otherIntents = intents.filter(
      (intent) =>
        intent.intent !== "PROCESS_DOCUMENT" &&
        !(intent.intent === "UNSORTED" && intent.attachments && intent.attachments.length > 0)
    );

    if (documentIntents.length <= 1) {
      return intents; // No combining needed
    }

    // Combine all document-related intents into one PROCESS_DOCUMENT intent
    const combinedIntent: ValidatedEmailAction = {
      intent: "PROCESS_DOCUMENT",
      instructions: documentIntents.flatMap((i) => i.instructions || []),
      attachments: documentIntents.flatMap((i) => i.attachments || [])
    };

    // Only add shippingData if there's actual data to combine
    const combinedShippingData = documentIntents.reduce(
      (acc, i) => ({ ...acc, ...(i.shippingData || {}) }),
      {}
    );

    if (Object.keys(combinedShippingData).length > 0) {
      // Type assertion is safe here since we're combining existing shippingData objects
      combinedIntent.shippingData = combinedShippingData as ValidatedEmailAction["shippingData"];
    }

    this.logger.log(
      `[HANDLE_REQUEST_MESSAGE] Combined ${documentIntents.length} document intents (${processDocumentIntents.length} PROCESS_DOCUMENT + ${unsortedDocumentIntents.length} UNSORTED with attachments) into 1 PROCESS_DOCUMENT intent`
    );

    return [...otherIntents, combinedIntent];
  }

  /**
   * Ensure we have at least one PROCESS_DOCUMENT intent if we have aggregated documents
   */
  private async ensureProcessDocumentIntent(
    intents: ValidatedEmailAction[],
    email: Email,
    contextId: any
  ): Promise<ValidatedEmailAction[]> {
    // Check if we already have a PROCESS_DOCUMENT intent
    const hasProcessDocumentIntent = intents.some((intent) => intent.intent === "PROCESS_DOCUMENT");
    if (hasProcessDocumentIntent) {
      this.logger.log(
        `[HANDLE_REQUEST_MESSAGE] Email ${email.id} already has PROCESS_DOCUMENT intent, no need to add one`
      );
      return intents;
    }

    // For now, skip checking for aggregated documents since we removed the helper method
    // TODO: Re-implement document checking if needed for the fragment system
    this.logger.log(`[HANDLE_REQUEST_MESSAGE] Skipping aggregated document check for email ${email.id}...`);
    return intents;

    // Add a synthetic PROCESS_DOCUMENT intent
    this.logger.log(
      `[HANDLE_REQUEST_MESSAGE] Email ${email.id} has aggregated documents but no PROCESS_DOCUMENT intent, adding synthetic intent`
    );

    const syntheticIntent: ValidatedEmailAction = {
      intent: "PROCESS_DOCUMENT",
      instructions: ["Process aggregated documents"],
      attachments: []
    };

    return [...intents, syntheticIntent];
  }

  /**
   * Create a fallback response for intents without handlers
   */
  private createFallbackResponse(intent: ValidatedEmailAction): string {
    return "Thank you for your e-mail. Our team will review this request and get back to you as soon as we can.";
  }

  /**
   * Send unified template response
   */
  private async sendUnifiedResponse(
    email: Email,
    responseHtml: string,
    emailService: EmailService,
    queryRunner: QueryRunner
  ): Promise<void> {
    if (!responseHtml || responseHtml.trim().length === 0) {
      this.logger.warn(`[HANDLE_REQUEST_MESSAGE] No response to send for email ${email.id}`);
      return;
    }

    try {
      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Sending unified template response for email ${email.id}`);
      this.logger.debug(`[HANDLE_REQUEST_MESSAGE] Sending response for email ${email.id}\n${responseHtml}`);
      
      await emailService.replyEmail(
        email.id,
        {
          html: responseHtml
        },
        queryRunner
      );
    } catch (error) {
      this.logger.error(
        `[HANDLE_REQUEST_MESSAGE] Failed to send response for email ${email.id}: ${error.message}`
      );
      // Fall back to manual review when email sending fails
      await this.sendFallbackResponse(email, emailService, queryRunner);
    }
  }

  /**
   * Combine multiple intent responses into a single HTML response
   */
  private combineIntentResponses(
    responses: string[],
    intents: ValidatedEmailAction[],
    email: Email
  ): string {
    if (responses.length === 0) {
      return "";
    }

    if (responses.length === 1) {
      return responses[0];
    }

    // For multiple responses, combine them logically
    this.logger.log(
      `[HANDLE_REQUEST_MESSAGE] Combining ${responses.length} intent responses for email ${email.id}`
    );

    // Join responses with line breaks, removing any duplicate greetings/closings
    const combinedResponse = responses
      .map((response, index) => {
        // Remove greeting from subsequent responses to avoid repetition
        if (index > 0) {
          return response.replace(/^<p>Hello,?<\/p>\s*/i, "");
        }
        return response;
      })
      .join("\n\n");

    return combinedResponse;
  }


  /**
   * Check if response is empty or only contains greeting (no meaningful content)
   */
  private isEmptyResponse(responseHtml: string): boolean {
    if (!responseHtml || responseHtml.trim().length === 0) {
      return true;
    }

    // Remove HTML tags and whitespace to check actual content
    const textContent = responseHtml.replace(/<[^>]*>/g, "").trim();

    // Check if response only contains greeting variations
    const greetingOnlyPatterns = [
      /^hello,?\s*$/i,
      /^hi,?\s*$/i,
      /^hello,?\s*thank you for your email\.?\s*$/i,
      /^hello,?\s*thank you for your inquiry\.?\s*$/i
    ];

    return greetingOnlyPatterns.some((pattern) => pattern.test(textContent));
  }

  /**
   * Send response when no meaningful intents are found
   */
  private async sendNoIntentsResponse(
    email: Email,
    emailService: EmailService,
    queryRunner: QueryRunner
  ): Promise<void> {
    this.logger.log(`[HANDLE_REQUEST_MESSAGE] Sending no intents response for email ${email.id}`);

    try {
      // Create a simple fallback response using the template manager
      const fallbackHtml = await this.templateManagerService.renderTemplate("contact-support", {
        reason:
          "Thank you for your email. Our team will review this request and get back to you as soon as we can.",
        shipment: null // No shipment context available
      });

      // Send the response
      await emailService.replyEmail(
        email.id,
        {
          html: `<p>Hello,</p>\n${fallbackHtml}`
        },
        queryRunner
      );

      // Update email status to RESPONDED
      await emailService.editEmail(
        email.id,
        {
          status: EmailStatus.RESPONDED,
          userIntents: [] // No intents found
        },
        queryRunner
      );

      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Sent no intents response for email ${email.id}`);
    } catch (error) {
      this.logger.error(
        `[HANDLE_REQUEST_MESSAGE] Failed to send no intents response for email ${email.id}: ${error.message}`
      );
      // Fallback to manual review if we can't send response
      await this.sendFallbackResponse(email, emailService, queryRunner);
    }
  }

  /**
   * Escalate email to manual review when no shipment is found
   */
  private async sendFallbackResponse(
    email: Email,
    emailService: EmailService,
    queryRunner: QueryRunner
  ): Promise<void> {
    this.logger.log(
      `[HANDLE_REQUEST_MESSAGE] Escalating email ${email.id} to manual review - no shipment found`
    );

    // Update email status to MANUAL_REVIEW
    await emailService.editEmail(
      email.id,
      {
        status: EmailStatus.MANUAL_REVIEW,
        error: "No shipment found for email thread"
      },
      queryRunner
    );

    // Emit manual review event to trigger automated reply
    this.eventEmitter.emit(
      EmailEvent.EMAIL_MANUAL_REVIEW_REQUIRED,
      new EmailManualReviewRequiredEvent({
        emailId: email.id,
        organizationId: email.organization.id,
        reviewReason: EmailManualReviewReason.NO_INTENTS
      })
    );

    this.logger.log(`[HANDLE_REQUEST_MESSAGE] Email ${email.id} escalated to manual review`);
  }

  /**
   * Double-pass classification for problematic intents (UNSORTED, SPAM, UNKNOWN)
   * Re-runs classification to give these intents another chance to be properly classified
   */
  private async refineProblematicIntents(
    intents: ValidatedEmailAction[],
    messageContent: MessageContent
  ): Promise<ValidatedEmailAction[]> {
    const refinedIntents: ValidatedEmailAction[] = [];
    const problematicIntentTypes = ["UNSORTED", "SPAM", "UNKNOWN"];

    for (const intent of intents) {
      if (problematicIntentTypes.includes(intent.intent)) {
        this.logger.log(
          `[HANDLE_REQUEST_MESSAGE] Double-pass classification for ${intent.intent} intent: "${intent.instructions?.join(" ") || ""}"`
        );

        try {
          // Re-classify each instruction separately for better accuracy
          const reClassifiedActions: ValidatedEmailAction[] = [];

          for (const instruction of intent.instructions || []) {
            const taskIntent = await this.emailIntentAnalysisService.classifyTaskIntent(instruction);

            // Convert the TaskIntent to ValidatedEmailAction format
            const reClassifiedAction: ValidatedEmailAction = {
              intent: this.mapTaskIntentToEmailIntent(taskIntent),
              instructions: [instruction],
              shipmentReference: intent.shipmentReference,
              shippingData: intent.shippingData,
              attachments: intent.attachments
            };

            reClassifiedActions.push(reClassifiedAction);
          }

          if (reClassifiedActions.length > 0) {
            // Check if any reclassification succeeded
            const improvedClassifications = reClassifiedActions.filter(
              (action) => !problematicIntentTypes.includes(action.intent)
            );

            if (improvedClassifications.length > 0) {
              this.logger.log(
                `[HANDLE_REQUEST_MESSAGE] Double-pass SUCCESS: Reclassified ${improvedClassifications.length}/${reClassifiedActions.length} instructions from ${intent.intent} to: ${improvedClassifications.map((a) => a.intent).join(", ")}`
              );
              refinedIntents.push(...improvedClassifications);

              // Keep any that still couldn't be classified
              const stillProblematic = reClassifiedActions.filter((action) =>
                problematicIntentTypes.includes(action.intent)
              );
              if (stillProblematic.length > 0) {
                refinedIntents.push(...stillProblematic);
              }
            } else {
              this.logger.log(
                `[HANDLE_REQUEST_MESSAGE] Double-pass FAILED: Could not improve classification for ${intent.intent}`
              );
              refinedIntents.push(intent); // Keep original
            }
          } else {
            refinedIntents.push(intent); // Keep original if no instructions
          }
        } catch (error) {
          this.logger.error(
            `[HANDLE_REQUEST_MESSAGE] Error in double-pass classification for ${intent.intent}: ${error.message}`
          );
          refinedIntents.push(intent); // Keep original on error
        }
      } else {
        // Keep non-problematic intents as-is
        refinedIntents.push(intent);
      }
    }

    return refinedIntents;
  }

  /**
   * Map TaskIntent enum to EMAIL_INTENTS string format
   */
  private mapTaskIntentToEmailIntent(taskIntent: any): (typeof EMAIL_INTENTS)[number] {
    // This mapping should match the logic in EmailIntentAnalysisService.adaptClassifiedTasksToValidatedIntents
    const mapping: Record<string, (typeof EMAIL_INTENTS)[number]> = {
      GET_SHIPMENT_STATUS: "GET_SHIPMENT_STATUS",
      SPAM: "SPAM",
      UNKNOWN: "UNKNOWN",
      PROCESS_DOCUMENT: "PROCESS_DOCUMENT",
      DOCUMENTATION_COMING: "DOCUMENTATION_COMING",
      REQUEST_RUSH_PROCESSING: "REQUEST_RUSH_PROCESSING",
      REQUEST_MANUAL_PROCESSING: "REQUEST_MANUAL_PROCESSING",
      REQUEST_HOLD_SHIPMENT: "REQUEST_HOLD_SHIPMENT",
      REQUEST_CAD_DOCUMENT: "REQUEST_CAD_DOCUMENT",
      REQUEST_RNS_PROOF: "REQUEST_RNS_PROOF",
      // These map to PROCESS_DOCUMENT for document-related operations
      CREATE_SHIPMENT: "PROCESS_DOCUMENT",
      CREATE_COMMERCIAL_INVOICE: "PROCESS_DOCUMENT",
      UPDATE_COMMERCIAL_INVOICE: "PROCESS_DOCUMENT",
      CREATE_CERTIFICATE_OF_ORIGIN: "PROCESS_DOCUMENT",
      UPDATE_CERTIFICATE_OF_ORIGIN: "PROCESS_DOCUMENT",
      // These map to UNSORTED per the original logic
      UPDATE_SHIPMENT: "UPDATE_SHIPMENT",
      UNSORTED: "UNSORTED"
    };

    return mapping[taskIntent] || "UNSORTED";
  }
}
