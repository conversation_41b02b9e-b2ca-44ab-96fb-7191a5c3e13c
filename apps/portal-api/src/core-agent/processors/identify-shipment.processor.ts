import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Logger } from "@nestjs/common";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectDataSource } from "@nestjs/typeorm";
import {
  Email,
  EmailStatus,
  File,
  FIND_ORGANIZATION_RELATIONS,
  Organization,
  FileBatch,
  Shipment
} from "nest-modules";
import { DataSource } from "typeorm";
import { FileBatchService } from "../../document/services/file-batch.service";
import { EmailService } from "../../email/services/email.service";
import { CoreAgentService } from "../services/core-agent.service";
import { EmailEvent } from "../../email/types/event.types";
import { ThreadShipmentNotFoundEvent, ThreadTooManyShipmentsFoundEvent } from "../../email/dto/event.dto";
import { generateEmailContent } from "../../email/utils/generate-email-content";
import { generateRequest } from "../../email/utils/generate-request";
import { MessageContent } from "../schemas/message-content.schema";
import {
  CoreAgentQueueName,
  DEFAULT_WORKER_OPTIONS,
  IdentifyShipmentJob,
  IdentifyShipmentJobData
} from "../types/queue.types";
import { FIND_DOCUMENT_RELATIONS } from "nest-modules";

/**
 * Checks if all found shipment identifiers point to the same unique shipment
 * @param foundShipments Array of found shipment identifiers
 * @returns true if all identifiers point to the same shipment, false otherwise
 */
function isSameShipment(foundShipments: Array<{ shipmentId?: number }>): boolean {
  if (foundShipments.length === 0) return false;
  if (foundShipments.length === 1) return true;

  const validShipmentIds = foundShipments
    .map((fs) => fs.shipmentId)
    .filter((id): id is number => id !== undefined);

  if (validShipmentIds.length === 0) return false;

  const uniqueShipmentIds = [...new Set(validShipmentIds)];
  return uniqueShipmentIds.length === 1;
}

/**
 * Core Agent Identify Shipment Processor
 *
 * BullMQ processor that handles shipment identification for emails.
 * This processor focuses on identifying shipments and setting up the next steps in the pipeline.
 *
 * Responsibilities:
 * - Gather EmailContent (leaner version of FindThreadShipment)
 * - Identify shipments using CoreAgentService
 * - Handle multiple shipment scenarios (emit events for manual review)
 * - Set email status for next listener
 * - Focus on shipment identification, not direct email-shipment association
 */
@Processor(
  {
    name: CoreAgentQueueName.IDENTIFY_SHIPMENT
  },
  DEFAULT_WORKER_OPTIONS
)
export class IdentifyShipmentProcessor extends WorkerHost {
  protected readonly logger = new Logger(IdentifyShipmentProcessor.name);

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly moduleRef: ModuleRef,
    private readonly eventEmitter: EventEmitter2,
    private readonly coreAgentService: CoreAgentService
  ) {
    super();
  }

  async process(job: IdentifyShipmentJob): Promise<void> {
    const { emailId, organizationId, hasAttachments = false } = job.data;

    this.logger.log(
      `[IDENTIFY_SHIPMENT] Processing email ${emailId}, organization ${organizationId}, hasAttachments: ${hasAttachments}`
    );

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Load organization with relations
      const organization = await queryRunner.manager.findOne(Organization, {
        where: { id: organizationId },
        relations: FIND_ORGANIZATION_RELATIONS
      });
      if (!organization) throw new Error("Organization not found");

      // Create synthetic request context for request-scoped services
      const contextId = ContextIdFactory.create();
      const requestContext = generateRequest(null, organization);
      this.moduleRef.registerRequestByContextId(requestContext, contextId);

      // Resolve request-scoped services (order matters!)
      const fileBatchService = await this.moduleRef.resolve(FileBatchService, contextId, {
        strict: false
      });
      const emailService = await this.moduleRef.resolve(EmailService, contextId, {
        strict: false
      });
      await new Promise((resolve) => process.nextTick(resolve)); // Wait for dependencies

      // Load email
      const email = await emailService.getEmailById(emailId, queryRunner);
      if (!email) throw new Error("Email not found");
      if (email.organization?.id !== organizationId) throw new Error("Email organization mismatch");

      this.logger.log(`Processing email ${emailId} with threadId: ${email.threadId}`);

      // Check if email already has a shipment from an existing thread
      const existingThreadShipment = await emailService.getEmailThreadShipment(email.threadId, queryRunner);

      let eventToEmit: EmailEvent | null = null;
      let eventData: any = null;
      let shipmentId: number | null = null;

      if (existingThreadShipment) {
        this.logger.log(
          `Found existing thread shipment ${existingThreadShipment.id} for email ${emailId} - skipping shipment identification`
        );
        shipmentId = existingThreadShipment.id;
      } else {
        this.logger.log(`No existing thread shipment found, proceeding with identification`);

        // Gather EmailContent (leaner version of FindThreadShipment approach)
        const threadAttachments: Array<File> = [];
        if (hasAttachments) {
          try {
            threadAttachments.push(
              ...(await fileBatchService.getFiles(email.gmailId, {
                documents: {
                  ...FIND_DOCUMENT_RELATIONS,
                  fields: true
                }
              }))
            );
          } catch (error) {
            this.logger.warn(`Error getting attachments for email ${email.gmailId}: ${error.message}`);
          }
        }

        // Generate EmailContent for shipment identification
        const emailContent = generateEmailContent(email, [], threadAttachments);

        // Convert to MessageContent for CoreAgentService
        const messageContent: MessageContent = {
          subject: emailContent.subject,
          text: emailContent.text,
          messageHistory: emailContent.emailHistories,
          attachments: emailContent.attachments?.map((attachment) => ({
            filename: attachment.filename
            // extractedData: attachment.extractedData
          }))
        };

        // Try to identify shipment using CoreAgent
        const identifyResult = await this.coreAgentService.identifyShipment(messageContent, organizationId);
        const foundShipments = identifyResult.foundIdentifiers || [];

        this.logger.log(
          `Shipment identification result: ${foundShipments.length} shipments found - ${identifyResult.reason}`
        );

        // Handle shipment identification results
        if (foundShipments.length === 0) {
          // Only emit THREAD_SHIPMENT_NOT_FOUND if there are no attachments & can't identify shipment
          // 'cause don't know what shipment you're talking about.
          if (!hasAttachments) {
            this.logger.warn(
              `No shipment found and no attachments, will emit thread shipment not found event`
            );
            await queryRunner.manager.update(
              Email,
              { id: emailId },
              { status: EmailStatus.FAILED_SHIPMENT_SEARCH }
            );
            eventToEmit = EmailEvent.THREAD_SHIPMENT_NOT_FOUND;
            eventData = new ThreadShipmentNotFoundEvent({
              threadId: email.threadId,
              organizationId
            });
          } else {
            this.logger.log(
              `No shipment found but has attachments - ignoring THREAD_SHIPMENT_NOT_FOUND condition`
            );
            await queryRunner.manager.update(
              Email,
              { id: emailId },
              { status: EmailStatus.AGGREGATING_EMAIL }
            );
          }
        } else if (foundShipments.length > 1 && !isSameShipment(foundShipments)) {
          this.logger.warn(
            `Multiple different shipments found (${foundShipments.length} identifiers pointing to different shipments), will emit too many shipments event for manual review`
          );
          await queryRunner.manager.update(
            Email,
            { id: emailId },
            { status: EmailStatus.FAILED_SHIPMENT_SEARCH }
          );
          eventToEmit = EmailEvent.THREAD_TOO_MANY_SHIPMENTS_FOUND;
          eventData = new ThreadTooManyShipmentsFoundEvent({
            threadId: email.threadId,
            organizationId
          });
          return;
        } else {
          // ** Single shipment found (or multiple identifiers pointing to same shipment) **
          shipmentId = foundShipments[0].shipmentId;

          // Log appropriate message based on scenario
          if (foundShipments.length > 1) {
            this.logger.log(
              `Multiple identifiers (${foundShipments.length}) all point to same shipment ${shipmentId}, proceeding with processing`
            );
          } else {
            this.logger.log(`Single shipment identified: ${shipmentId}, proceeding with processing`);
          }

          // Associate shipment with email thread for future emails in this thread
          try {
            // Load the complete shipment object with organization data for proper association
            const shipmentForAssociation = await queryRunner.manager.findOne(Shipment, {
              where: { id: shipmentId },
              relations: { organization: true }
            });

            if (!shipmentForAssociation) {
              throw new Error(`Shipment ${shipmentId} not found for thread association`);
            }

            await emailService.setEmailThreadShipment(
              email.threadId,
              shipmentForAssociation,
              queryRunner,
              true
            );
            this.logger.log(`Associated thread ${email.threadId} with identified shipment ${shipmentId}`);
          } catch (error) {
            // Thread might already be associated (race condition), which is fine
            this.logger.warn(`Thread association warning (likely already exists): ${error.message}`);
          }

          await queryRunner.manager.update(
            Email,
            { id: emailId },
            { status: EmailStatus.COMPLETED_SHIPMENT_SEARCH }
          );
        }
      }

      // If we identified a shipment and have attachments, associate shipment with filebatch
      if (hasAttachments && shipmentId) {
        try {
          await queryRunner.manager.update(
            FileBatch,
            { id: email.gmailId, organization: { id: organizationId } },
            { shipmentId }
          );
          this.logger.log(`Associated filebatch ${email.gmailId} with shipment ${shipmentId}`);
        } catch (error) {
          this.logger.warn(`Failed to associate filebatch with shipment: ${error.message}`);
          // Don't fail the entire job if filebatch association fails
        }
      }
      await queryRunner.commitTransaction();

      // Emit events after successful transaction commit
      if (eventToEmit && eventData) {
        this.logger.log(`Emitting event ${eventToEmit} for email ${emailId}`);
        this.eventEmitter.emit(eventToEmit, eventData);
      }

      this.logger.log(
        `Successfully processed email ${emailId} - ` + `${hasAttachments ? "with" : "no"} attachments`
      );
    } catch (error) {
      this.logger.error(`Error processing email ${emailId}:`, error);

      if (queryRunner.isTransactionActive) {
        await queryRunner.rollbackTransaction();
      }

      throw error; // Let BullMQ handle retries
    } finally {
      await queryRunner.release();
    }
  }
}
