import { EMAIL_INTENTS } from "nest-modules";
import { ShipmentContext } from "../../agent-context";
import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";

/**
 * Classification metadata for dynamic LLM prompt generation.
 * Each intent handler provides this information for the LLM to classify user requests.
 */
export interface IntentClassificationMeta {
  /** The intent type this handler manages (from EMAIL_INTENTS) */
  intent: (typeof EMAIL_INTENTS)[number];

  /** Human-readable description of when this intent should be used */
  description: string;

  /** Example user requests that should trigger this intent */
  examples: string[];

  /** Optional additional context or keywords that help classification */
  keywords?: string[];
}

/**
 * Interface that all intent handlers must implement.
 * Each handler corresponds to one EMAIL_INTENTS type and handles all user requests classified under that intent.
 */
export interface IntentHandler {
  /** Classification metadata for dynamic LLM prompt generation */
  readonly classificationMeta: IntentClassificationMeta;

  /**
   * Handles validated intents and returns rendered template content using the unified template API.
   *
   * @param validatedIntent - The ValidatedEmailAction with intent type and instructions
   * @param context - Complete shipment context with all business rules evaluated
   * @returns Rendered template content as string
   */
  handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<string>;
}
