import { Logger } from "@nestjs/common";

/**
 * Method decorator to automatically retry an async method on specified errors.
 *
 * @param maxRetries - Maximum number of attempts (default: 3)
 * @param shouldRetry - Predicate function: (error) => boolean. Return true to retry on this error.
 * @param delayMs - Optional delay (ms) between retries (default: 0)
 *
 * @example
 *   @Retry(3, (error) => error instanceof SomeTransientError, 1000)
 *   async myMethod() { ... }
 */
export function Retry(maxRetries = 3, shouldRetry: (error: any) => boolean = () => true, delayMs = 0) {
  const logger = new Logger("Retry");
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    descriptor.value = async function (...args: any[]) {
      let lastError;
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          return await originalMethod.apply(this, args);
        } catch (error) {
          lastError = error;
          if (!shouldRetry(error) || attempt === maxRetries) {
            throw error;
          }
          logger.warn(`Retrying ${propertyKey} (attempt ${attempt}) due to error: ${error.message}`);
          if (delayMs) await new Promise((res) => setTimeout(res, delayMs));
        }
      }
      throw lastError;
    };
    return descriptor;
  };
}
