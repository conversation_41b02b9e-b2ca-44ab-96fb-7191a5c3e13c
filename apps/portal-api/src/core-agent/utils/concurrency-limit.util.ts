/**
 * Limits the number of concurrently executing async tasks.
 *
 * @template T - The resolved type of the async task.
 * @param tasks - An array of functions returning promises (deferred execution).
 * @param concurrency - Maximum number of concurrent executions.
 * @returns Promise resolving to an array of results in the same order as input tasks.
 *
 * @example
 *   const results = await limitConcurrency([
 *     () => fetchData(1),
 *     () => fetchData(2),
 *     () => fetchData(3)
 *   ], 2);
 */
export async function limitConcurrency<T>(tasks: Array<() => Promise<T>>, concurrency: number): Promise<T[]> {
  const results: T[] = [];
  let nextIndex = 0;

  async function worker() {
    while (nextIndex < tasks.length) {
      const current = nextIndex++;
      results[current] = await tasks[current]();
    }
  }

  // Start up to `concurrency` workers
  await Promise.all(Array(Math.min(concurrency, tasks.length)).fill(0).map(worker));
  return results;
}
