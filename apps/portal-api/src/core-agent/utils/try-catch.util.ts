import { Result } from "../types/result.type";

/**
 * Executes a synchronous function and returns a Result<T, E> object.
 *
 * @template T - The return type of the function.
 * @template E - The error type (defaults to Error).
 * @param fn - The function to execute.
 * @param mapError - Optional function to map the caught error to a custom error type.
 * @returns Result<T, E>
 *
 * @example
 * // Basic usage
 * const result = tryCatchSync(() => {
 *   return JSON.parse('{"valid": "json"}');
 * });
 * // result: { ok: true, value: { valid: 'json' } }
 *
 * @example
 * // With error mapping
 * const result = tryCatchSync(
 *   () => JSON.parse('invalid json'),
 *   (err) => ({ code: 'PARSE_ERROR', message: err.message })
 * );
 * // result: { ok: false, error: { code: 'PARSE_ERROR', message: '...' } }
 */
export function tryCatchSync<T, E = Error>(fn: () => T, mapError?: (err: unknown) => E): Result<T, E> {
  try {
    return { ok: true, value: fn() };
  } catch (err) {
    return { ok: false, error: mapError ? mapError(err) : (err as E) };
  }
}

/**
 * Executes an async function and returns a Promise<Result<T, E>>.
 *
 * @template T - The resolved type of the Promise.
 * @template E - The error type (defaults to Error).
 * @param fn - The async function to execute.
 * @param mapError - Optional function to map the caught error to a custom error type.
 * @returns Promise<Result<T, E>>
 *
 * @example
 * // Basic usage with async function
 * const result = await tryCatch(async () => {
 *   const response = await fetch('https://api.example.com/data');
 *   return response.json();
 * });
 * // If successful: { ok: true, value: { ... } }
 * // If failed: { ok: false, error: Error }
 *
 * @example
 * // With custom error handling
 * const result = await tryCatch(
 *   () => shipmentService.getShipmentById(123),
 *   (err) => ({
 *     type: err instanceof NotFoundException ? 'NOT_FOUND' : 'UNKNOWN',
 *     message: err.message
 *   })
 * );
 * // Handle the result
 * if (result.ok) {
 *   const shipment = result.value;
 *   // Process shipment
 * } else {
 *   logger.error(`Failed to get shipment: ${result.error.message}`);
 * }
 */
export async function tryCatch<T, E = Error>(
  fn: () => Promise<T>,
  mapError?: (err: unknown) => E
): Promise<Result<T, E>> {
  try {
    const value = await fn();
    return { ok: true, value };
  } catch (err) {
    return { ok: false, error: mapError ? mapError(err) : (err as E) };
  }
}
