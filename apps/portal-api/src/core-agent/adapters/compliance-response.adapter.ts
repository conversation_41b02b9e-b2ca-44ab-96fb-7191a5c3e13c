import {
  NonCompliantReason,
  ShipmentColumn,
  CommercialInvoiceColumn,
  TradePartnerColumn,
  ValidateShipmentComplianceResponseDto
} from "nest-modules";
import {
  COMPLIANCE_ERROR_MESSAGES,
  NON_COMPLIANT_REASON_MESSAGES
} from "../constants/compliance-messages.constants";
import {
  MISSING_SHIPMENT_FIELD_DESCRIPTIONS,
  MISSING_COMMERCIAL_INVOICE_FIELD_DESCRIPTIONS,
  MISSING_TRADE_PARTNER_FIELD_DESCRIPTIONS
} from "../constants/validate-compliance-response.descriptions";
import { ERROR_FORMATTING_PREFIX } from "../constants/error-formatting.constants";

/**
 * Formats the raw ValidateShipmentComplianceResponseDto into a flat list
 * of user-friendly strings describing all compliance issues.
 *
 * @param {ValidateShipmentComplianceResponseDto} rawResponse - The raw validation DTO.
 * @returns {string[]} A flat list of strings describing compliance errors.
 */
export function formatComplianceResponseToStrings(
  rawResponse: ValidateShipmentComplianceResponseDto
): string[] {
  const errors: string[] = [];

  // Helper to add errors for missing fields
  const addMissingFieldErrors = (
    fields: Array<ShipmentColumn | CommercialInvoiceColumn | TradePartnerColumn>,
    descriptionMap: Record<string, string>,
    prefix: string
  ): void => {
    (fields || []).forEach((field) => {
      const description = descriptionMap[field] || `${ERROR_FORMATTING_PREFIX.UNKNOWN_FIELD} ${field}`;
      errors.push(`${prefix} ${description}`);
    });
  };

  // 1. Check for missing shipment fields
  addMissingFieldErrors(
    rawResponse.missingFields,
    MISSING_SHIPMENT_FIELD_DESCRIPTIONS,
    ERROR_FORMATTING_PREFIX.MISSING_SHIPMENT_INFO
  );

  // 2. Check if commercial invoice is missing entirely
  if (rawResponse.noCommercialInvoice) {
    errors.push(
      `${ERROR_FORMATTING_PREFIX.MISSING_COMMERCIAL_INVOICE} ${COMPLIANCE_ERROR_MESSAGES.NO_COMMERCIAL_INVOICE}`
    );
    // Stop processing further invoice/line errors if the whole invoice is missing
    return errors;
  }

  // 3. Process non-compliant commercial invoices
  (rawResponse.nonCompliantInvoices || []).forEach((invoiceResult, index) => {
    const invoicePrefix = ERROR_FORMATTING_PREFIX.COMMERCIAL_INVOICE_ID(
      index,
      invoiceResult.commercialInvoiceId
    );

    // 3a. Check for missing invoice fields
    addMissingFieldErrors(
      invoiceResult.missingFields,
      MISSING_COMMERCIAL_INVOICE_FIELD_DESCRIPTIONS,
      `${invoicePrefix} ${ERROR_FORMATTING_PREFIX.MISSING_INVOICE_INFO}`
    );

    // 3b. Check for missing Ship-To fields
    addMissingFieldErrors(
      invoiceResult.shipToMissingFields,
      MISSING_TRADE_PARTNER_FIELD_DESCRIPTIONS,
      `${invoicePrefix} ${ERROR_FORMATTING_PREFIX.MISSING_SHIP_TO_INFO}`
    );

    // 3c. Check for missing Vendor fields
    addMissingFieldErrors(
      invoiceResult.vendorMissingFields,
      MISSING_TRADE_PARTNER_FIELD_DESCRIPTIONS,
      `${invoicePrefix} ${ERROR_FORMATTING_PREFIX.MISSING_VENDOR_INFO}`
    );

    // 3d. Process non-compliant lines within the invoice
    (invoiceResult.nonCompliantLines || []).forEach((lineResult) => {
      const linePrefix = `${invoicePrefix} ${ERROR_FORMATTING_PREFIX.LINE_ID(lineResult.lineId)}`;

      if (lineResult.isHsCodeInvalid) {
        errors.push(`${linePrefix} ${COMPLIANCE_ERROR_MESSAGES.INVALID_HS_CODE}`);
      }

      if (lineResult.isQuantityInvalid) {
        errors.push(`${linePrefix} ${COMPLIANCE_ERROR_MESSAGES.INVALID_QUANTITY}`);
      }

      (lineResult.nonCompliantRecords || []).forEach((record) => {
        const reason = record.reason as NonCompliantReason;
        const message =
          NON_COMPLIANT_REASON_MESSAGES[reason] ||
          `${ERROR_FORMATTING_PREFIX.UNKNOWN_COMPLIANCE_ISSUE} ${reason}`;
        errors.push(`${linePrefix} ${ERROR_FORMATTING_PREFIX.COMPLIANCE_ISSUE} ${message}`);
      });
    });
  });

  return errors;
}
