import { Controller, Get, Post, Param, Body, Query } from "@nestjs/common";
import { ApiOperation, ApiTags, ApiParam, ApiBody } from "@nestjs/swagger";
import { ShipmentResponseService } from "../../services/shipment-response.service";
import { ShipmentContextService } from "../../../agent-context";
import { ResponseFragment } from "../../types/response-fragment.types";

/**
 * Test controller for debugging and validating the ShipmentResponseService.
 * Only available in development/test environments.
 */
@ApiTags("Debug - Response Service")
@Controller("debug/response-service")
export class ResponseServiceTestController {
  constructor(
    private readonly responseService: ShipmentResponseService,
    private readonly contextService: ShipmentContextService
  ) {}

  @Post("render-fragments/:shipmentId")
  @ApiOperation({ summary: "Test fragment rendering with mock fragments" })
  @ApiParam({ name: "shipmentId", description: "Shipment ID for context" })
  @ApiBody({
    description: "Array of response fragments to render",
    schema: {
      type: "object",
      properties: {
        fragments: {
          type: "array",
          items: {
            type: "object",
            properties: {
              template: { type: "string" },
              priority: { type: "number" },
              fragmentContext: { type: "object" }
            }
          }
        },
        organizationId: { type: "number" }
      }
    }
  })
  async testFragmentRendering(
    @Param("shipmentId") shipmentId: number,
    @Body() body: { fragments: ResponseFragment[]; organizationId: number }
  ) {
    try {
      // Build context for the shipment
      const context = await this.contextService.buildContext(shipmentId, body.organizationId);

      // Render the provided fragments
      const renderedResponse = await this.responseService.renderFragments(body.fragments, context);

      return {
        success: true,
        shipmentId,
        fragmentCount: body.fragments.length,
        uniqueFragments: [...new Set(body.fragments.map((f) => f.template))],
        renderedResponse,
        context: {
          shipmentStatus: context.shipment.customsStatus,
          businessRules: {
            canRush: context.canRush,
            canGenerateCAD: context.canGenerateCAD,
            isCompliant: context.isCompliant
          }
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        stack: error.stack
      };
    }
  }

  @Get("validate-template/:templateName")
  @ApiOperation({ summary: "Test if a template exists and can be rendered" })
  @ApiParam({ name: "templateName", description: "Name of template to validate" })
  async validateTemplate(@Param("templateName") templateName: string) {
    // Create sample context for validation
    const sampleContext = {
      shipment: { id: 1, customsStatus: "PENDING" },
      isCompliant: false,
      canRush: true,
      rushBlockingReason: "Test reason",
      missingDocuments: ["Commercial Invoice", "Packing List"],
      sideEffects: {
        backofficeAlerts: {
          rushProcessingSent: true
        }
      }
    };

    const isValid = await this.responseService.validateTemplate(templateName, sampleContext);

    return {
      templateName,
      isValid,
      sampleContext
    };
  }

  @Get("test-scenarios")
  @ApiOperation({ summary: "Get predefined test scenarios for fragment rendering" })
  getTestScenarios() {
    return {
      scenarios: [
        {
          name: "Rush Processing Success",
          fragments: [
            { template: "rush-processing-success", priority: 1 },
            { template: "customs-status", priority: 2 }
          ]
        },
        {
          name: "Rush Processing Blocked",
          fragments: [
            { template: "rush-processing-blocked", priority: 1 },
            { template: "compliance-errors", priority: 2 },
            { template: "acknowledgement-missing-docs", priority: 3 }
          ]
        },
        {
          name: "CAD Document Request",
          fragments: [
            { template: "cad-document-attached", priority: 1 },
            { template: "customs-status", priority: 2 }
          ]
        },
        {
          name: "CAD Document Not Ready",
          fragments: [
            { template: "cad-document-not-ready", priority: 1 },
            { template: "submission-required-notice", priority: 2 }
          ]
        },
        {
          name: "Status Check with Compliance Issues",
          fragments: [
            { template: "customs-status", priority: 1 },
            { template: "compliance-errors", priority: 2 },
            { template: "acknowledgement-missing-docs", priority: 3 },
            { template: "eta", priority: 4 }
          ]
        }
      ]
    };
  }

  @Get("deduplication-test")
  @ApiOperation({ summary: "Test fragment deduplication logic" })
  async testDeduplication(
    @Query("organizationId") organizationId: number = 1,
    @Query("shipmentId") shipmentId: number = 1
  ) {
    const duplicateFragments: ResponseFragment[] = [
      { template: "customs-status", priority: 1 },
      { template: "customs-status", priority: 2 }, // Duplicate
      { template: "eta", priority: 3 },
      { template: "customs-status", priority: 4 }, // Another duplicate
      { template: "acknowledgement-missing-docs", priority: 5 }
    ];

    // Use the provided shipment ID
    const context = await this.contextService.buildContext(shipmentId, organizationId);
    const rendered = await this.responseService.renderFragments(duplicateFragments, context);

    return {
      input: duplicateFragments,
      inputCount: duplicateFragments.length,
      expectedUniqueCount: 3, // customs-status, eta, acknowledgement-missing-docs
      rendered
    };
  }

  @Get("priority-sorting-test")
  @ApiOperation({ summary: "Test fragment priority sorting" })
  async testPrioritySorting(
    @Query("organizationId") organizationId: number = 1,
    @Query("shipmentId") shipmentId: number = 1
  ) {
    const unsortedFragments: ResponseFragment[] = [
      { template: "eta", priority: 20 },
      { template: "rush-processing-success", priority: 1 },
      { template: "customs-status", priority: 10 },
      { template: "acknowledgement-missing-docs" }, // No priority
      { template: "compliance-errors", priority: 15 }
    ];

    const context = await this.contextService.buildContext(shipmentId, organizationId);
    const rendered = await this.responseService.renderFragments(unsortedFragments, context);

    return {
      input: unsortedFragments,
      expectedOrder: [
        "rush-processing-success (priority: 1)",
        "customs-status (priority: 10)",
        "compliance-errors (priority: 15)",
        "eta (priority: 20)",
        "acknowledgement-missing-docs (no priority)"
      ],
      rendered
    };
  }

  @Get("system-diagnostics")
  @ApiOperation({ summary: "Get system diagnostics for response service" })
  getSystemDiagnostics() {
    return this.responseService.getSystemDiagnostics();
  }
}
