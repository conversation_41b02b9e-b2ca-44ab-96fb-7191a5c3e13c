import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsDefined, IsEnum, IsOptional, IsString, ValidateNested } from "class-validator";
import { EMAIL_INTENTS } from "nest-modules";

// Define a DTO for ValidatedEmailAction to be used within ValidatedEmailIntentsDto
class ValidatedEmailActionDto {
  @ApiProperty({ enum: EMAIL_INTENTS, description: "The classified intent of this action." })
  @IsEnum(EMAIL_INTENTS)
  intent: (typeof EMAIL_INTENTS)[number];

  @ApiProperty({
    type: [String],
    description: "The specific instruction(s) derived from the email for this action."
  })
  @IsArray()
  @IsString({ each: true })
  instructions: string[];

  @ApiProperty({ description: "Shipment reference associated with this action, if found.", required: false })
  @IsOptional()
  @IsString()
  shipmentReference?: string | null;

  @ApiProperty({
    description: "Extracted shipping data relevant to this action, if any.",
    required: false,
    type: "object"
  })
  @IsOptional()
  shippingData?: any | null; // Use 'any' or a more specific DTO if the structure is known

  @ApiProperty({ description: "Attachments relevant to this action.", required: false, type: ["object"] })
  @IsOptional()
  @IsArray()
  attachments?: any[] | null; // Use 'any' or a more specific Attachment DTO
}

// Main DTO for the overall structure
export class ValidatedEmailIntentsDto {
  @ApiProperty({
    type: [ValidatedEmailActionDto],
    description: "An array of validated email actions derived from the email content."
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ValidatedEmailActionDto)
  @IsDefined()
  intents: ValidatedEmailActionDto[];
}
