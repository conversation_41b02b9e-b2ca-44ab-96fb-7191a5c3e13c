import {
  Body,
  Controller,
  Logger,
  Post,
  Req,
  UseGuards,
  ValidationPipe,
  BadRequestException
} from "@nestjs/common";
import { ApiOperation, ApiTags, ApiResponse, ApiBody } from "@nestjs/swagger";
import { AccessTokenGuard, ApiAccessTokenAuthenticated, AuthenticatedRequest } from "nest-modules";
import { TestAnalyzeEmailIntentsDto } from "../../dto/test/email-intent-analysis.test.dto";
import { MessageContent } from "../../schemas/message-content.schema";
import { CoreAgentService } from "../../services/core-agent.service";
import { ShipmentIdentifierService } from "../../services/shipment-identifier.service";
import { IdentifyShipmentOutput } from "../../schemas/identify-shipment.schema";

@ApiTags("Core Agent Tests / Core Agent")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("test/core-agent")
export class CoreAgentTestController {
  private readonly logger = new Logger(CoreAgentTestController.name);

  constructor(
    private readonly coreAgentService: CoreAgentService,
    private readonly shipmentIdentifierService: ShipmentIdentifierService
  ) {}

  @Post("identify-shipment")
  @ApiOperation({ summary: "Test the shipment identification service directly" })
  @ApiBody({ type: TestAnalyzeEmailIntentsDto })
  @ApiResponse({
    status: 200,
    description: "Successfully identified shipments or returned reasons for failure.",
    schema: {
      type: "object",
      properties: {
        foundIdentifiers: {
          type: "array",
          items: {
            type: "object",
            properties: {
              shipmentId: { type: "number", nullable: true },
              identifierType: { type: "string", nullable: true },
              identifierValue: { type: "string" },
              context: { type: "string" }
            }
          }
        },
        reason: { type: "string" }
      }
    }
  })
  @ApiResponse({ status: 400, description: "Bad request (validation error)" })
  @ApiResponse({ status: 500, description: "Internal server error during identification" })
  async testIdentifyShipment(
    @Body(new ValidationPipe({ transform: true })) body: TestAnalyzeEmailIntentsDto,
    @Req() request: AuthenticatedRequest
  ): Promise<IdentifyShipmentOutput> {
    const organizationId = body.organizationId ?? request.user?.organization?.id;
    this.logger.log(
      `Received request to test shipment identification for org ${organizationId ?? "N/A"} via test controller`
    );

    const messageContent: MessageContent = {
      text: body.text,
      subject: body.subject,
      emailHistory: body.emailHistory,
      attachments: body.attachments as any[]
    };

    if (!organizationId) {
      throw new BadRequestException("Organization ID is required to test shipment identification.");
    }

    return this.shipmentIdentifierService.identifyShipment(messageContent, organizationId);
  }
}
