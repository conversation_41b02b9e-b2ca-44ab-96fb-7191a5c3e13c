import { Injectable, Logger } from "@nestjs/common";
import { EventEmitter2, OnEvent } from "@nestjs/event-emitter";

export interface TemplateRenderMetrics {
  templateType: string;
  customsStatus: string;
  fragmentsCount: number;
  renderDuration: number;
  timestamp: Date;
  success: boolean;
  error?: string;
  shipmentId: number;
  hasAttachments: boolean;
  cacheHit?: boolean;
}

export interface TemplateSystemHealth {
  totalRenders: number;
  successRate: number;
  avgRenderTime: number;
  p95RenderTime: number;
  maxRenderTime: number;
  errorRate: number;
  recentErrors: string[];
  cacheHitRate: number;
  uptimeMinutes: number;
}

export interface TemplatePerformanceStats {
  renderCount: number;
  avgRenderTime: number;
  minRenderTime: number;
  maxRenderTime: number;
  p50RenderTime: number;
  p95RenderTime: number;
  p99RenderTime: number;
  errorCount: number;
  successRate: number;
}

@Injectable()
export class TemplateMetricsService {
  private readonly logger = new Logger(TemplateMetricsService.name);
  private readonly metrics: TemplateRenderMetrics[] = [];
  private readonly maxMetricsHistory = 10000; // Keep last 10k metrics
  private readonly startTime = Date.now();
  private renderTimes: number[] = [];
  private errorCount = 0;
  private totalRenders = 0;
  private cacheHits = 0;
  private cacheAttempts = 0;

  constructor(private readonly eventEmitter: EventEmitter2) {}

  @OnEvent("template.render.success")
  handleRenderSuccess(data: {
    shipmentId: number;
    templateType: string;
    duration: number;
    fragmentCount: number;
    hasAttachments: boolean;
    customsStatus?: string;
    cacheHit?: boolean;
  }) {
    this.recordMetric({
      templateType: data.templateType,
      customsStatus: data.customsStatus || "unknown",
      fragmentsCount: data.fragmentCount,
      renderDuration: data.duration,
      timestamp: new Date(),
      success: true,
      shipmentId: data.shipmentId,
      hasAttachments: data.hasAttachments,
      cacheHit: data.cacheHit
    });

    this.totalRenders++;
    this.renderTimes.push(data.duration);

    if (data.cacheHit) {
      this.cacheHits++;
    }
    if (data.cacheHit !== undefined) {
      this.cacheAttempts++;
    }

    // Keep only recent render times for memory efficiency
    if (this.renderTimes.length > 1000) {
      this.renderTimes = this.renderTimes.slice(-1000);
    }

    // Log slow renders
    if (data.duration > 1000) {
      this.logger.warn(`Slow template render detected: ${data.duration}ms for shipment ${data.shipmentId}`);
    }
  }

  @OnEvent("template.render.error")
  handleRenderError(data: {
    shipmentId: number;
    templateType: string;
    duration: number;
    error: string;
    stack?: string;
    customsStatus?: string;
  }) {
    this.recordMetric({
      templateType: data.templateType,
      customsStatus: data.customsStatus || "unknown",
      fragmentsCount: 0,
      renderDuration: data.duration,
      timestamp: new Date(),
      success: false,
      error: data.error,
      shipmentId: data.shipmentId,
      hasAttachments: false
    });

    this.totalRenders++;
    this.errorCount++;

    this.logger.error(`Template render error for shipment ${data.shipmentId}: ${data.error}`);
  }

  private recordMetric(metric: TemplateRenderMetrics): void {
    this.metrics.push(metric);

    // Trim old metrics to prevent memory issues
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics.splice(0, this.metrics.length - this.maxMetricsHistory);
    }
  }

  getSystemHealth(): TemplateSystemHealth {
    const now = Date.now();
    const uptimeMinutes = Math.floor((now - this.startTime) / (1000 * 60));

    // Calculate success rate
    const successRate =
      this.totalRenders > 0 ? ((this.totalRenders - this.errorCount) / this.totalRenders) * 100 : 100;

    // Calculate render time statistics
    const sortedTimes = [...this.renderTimes].sort((a, b) => a - b);
    const avgRenderTime =
      this.renderTimes.length > 0
        ? this.renderTimes.reduce((sum, time) => sum + time, 0) / this.renderTimes.length
        : 0;

    const p95Index = Math.floor(sortedTimes.length * 0.95);
    const p95RenderTime = sortedTimes.length > 0 ? sortedTimes[p95Index] || 0 : 0;
    const maxRenderTime = sortedTimes.length > 0 ? Math.max(...sortedTimes) : 0;

    // Get recent errors (last 10)
    const recentErrors = this.metrics
      .filter((m) => !m.success && m.error)
      .slice(-10)
      .map((m) => `${m.timestamp.toISOString()}: ${m.error}`);

    // Calculate cache hit rate
    const cacheHitRate = this.cacheAttempts > 0 ? (this.cacheHits / this.cacheAttempts) * 100 : 0;

    // Calculate error rate
    const errorRate = this.totalRenders > 0 ? (this.errorCount / this.totalRenders) * 100 : 0;

    return {
      totalRenders: this.totalRenders,
      successRate: Math.round(successRate * 100) / 100,
      avgRenderTime: Math.round(avgRenderTime * 100) / 100,
      p95RenderTime: Math.round(p95RenderTime * 100) / 100,
      maxRenderTime,
      errorRate: Math.round(errorRate * 100) / 100,
      recentErrors,
      cacheHitRate: Math.round(cacheHitRate * 100) / 100,
      uptimeMinutes
    };
  }

  getPerformanceStats(templateType?: string): TemplatePerformanceStats {
    let relevantMetrics = this.metrics;

    if (templateType) {
      relevantMetrics = this.metrics.filter((m) => m.templateType === templateType);
    }

    const renderTimes = relevantMetrics
      .filter((m) => m.success)
      .map((m) => m.renderDuration)
      .sort((a, b) => a - b);

    const errorCount = relevantMetrics.filter((m) => !m.success).length;
    const renderCount = relevantMetrics.length;
    const successRate = renderCount > 0 ? ((renderCount - errorCount) / renderCount) * 100 : 100;

    const avgRenderTime =
      renderTimes.length > 0 ? renderTimes.reduce((sum, time) => sum + time, 0) / renderTimes.length : 0;

    const p50Index = Math.floor(renderTimes.length * 0.5);
    const p95Index = Math.floor(renderTimes.length * 0.95);
    const p99Index = Math.floor(renderTimes.length * 0.99);

    return {
      renderCount,
      avgRenderTime: Math.round(avgRenderTime * 100) / 100,
      minRenderTime: renderTimes.length > 0 ? renderTimes[0] : 0,
      maxRenderTime: renderTimes.length > 0 ? renderTimes[renderTimes.length - 1] : 0,
      p50RenderTime: renderTimes.length > 0 ? renderTimes[p50Index] || 0 : 0,
      p95RenderTime: renderTimes.length > 0 ? renderTimes[p95Index] || 0 : 0,
      p99RenderTime: renderTimes.length > 0 ? renderTimes[p99Index] || 0 : 0,
      errorCount,
      successRate: Math.round(successRate * 100) / 100
    };
  }

  getMetricsByTimeRange(startTime: Date, endTime: Date): TemplateRenderMetrics[] {
    return this.metrics.filter((m) => m.timestamp >= startTime && m.timestamp <= endTime);
  }

  getMetricsByCustomsStatus(customsStatus: string): TemplateRenderMetrics[] {
    return this.metrics.filter((m) => m.customsStatus === customsStatus);
  }

  getTopSlowRenders(limit: number = 10): TemplateRenderMetrics[] {
    return [...this.metrics]
      .filter((m) => m.success)
      .sort((a, b) => b.renderDuration - a.renderDuration)
      .slice(0, limit);
  }

  getErrorMetrics(limit: number = 50): TemplateRenderMetrics[] {
    return this.metrics.filter((m) => !m.success).slice(-limit);
  }

  getRenderTrends(intervalMinutes: number = 60): Array<{
    timestamp: Date;
    renderCount: number;
    avgRenderTime: number;
    errorCount: number;
  }> {
    const now = new Date();
    const intervals: Array<{
      timestamp: Date;
      renderCount: number;
      totalRenderTime: number;
      errorCount: number;
    }> = [];

    // Create intervals for the last 24 hours
    for (let i = 23; i >= 0; i--) {
      const intervalStart = new Date(now.getTime() - (i + 1) * intervalMinutes * 60 * 1000);
      const intervalEnd = new Date(now.getTime() - i * intervalMinutes * 60 * 1000);

      const intervalMetrics = this.getMetricsByTimeRange(intervalStart, intervalEnd);
      const successfulRenders = intervalMetrics.filter((m) => m.success);

      intervals.push({
        timestamp: intervalEnd,
        renderCount: intervalMetrics.length,
        totalRenderTime: successfulRenders.reduce((sum, m) => sum + m.renderDuration, 0),
        errorCount: intervalMetrics.filter((m) => !m.success).length
      });
    }

    return intervals.map((interval) => ({
      timestamp: interval.timestamp,
      renderCount: interval.renderCount,
      avgRenderTime:
        interval.renderCount > 0
          ? Math.round((interval.totalRenderTime / interval.renderCount) * 100) / 100
          : 0,
      errorCount: interval.errorCount
    }));
  }

  clearMetrics(): void {
    this.metrics.length = 0;
    this.renderTimes.length = 0;
    this.errorCount = 0;
    this.totalRenders = 0;
    this.cacheHits = 0;
    this.cacheAttempts = 0;
    this.logger.log("Template metrics cleared");
  }

  exportMetrics(): {
    systemHealth: TemplateSystemHealth;
    performanceStats: TemplatePerformanceStats;
    recentMetrics: TemplateRenderMetrics[];
  } {
    return {
      systemHealth: this.getSystemHealth(),
      performanceStats: this.getPerformanceStats(),
      recentMetrics: this.metrics.slice(-100) // Last 100 metrics
    };
  }
}
