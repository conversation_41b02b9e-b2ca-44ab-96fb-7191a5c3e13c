import { Injectable, Logger, InternalServerErrorException } from "@nestjs/common";
import { AskLLMService } from "@/llm/ask-llm/services/ask-llm.service";
import { PromptTemplateName } from "@/llm/prompt-template-manager";
import {
  ExtractShipmentFieldsSchema,
  ExtractShipmentFieldsOutput
} from "../schemas/extract-shipment-fields.schema";
import { CORE_AGENT_LLM_MODEL_FULL } from "../constants/llm.constants";

/**
 * Service for extracting shipment update fields from unstructured text instructions.
 * Used in UPDATE_SHIPMENT intent processing to extract structured data.
 */
@Injectable()
export class ShipmentFieldExtractionService {
  private readonly logger = new Logger(ShipmentFieldExtractionService.name);

  constructor(private readonly askLLMService: AskLLMService) {}

  /**
   * Extracts shipment update fields from instruction text using LLM.
   *
   * @param instructions - The unstructured text containing shipment update instructions
   * @returns Promise<ExtractShipmentFieldsOutput> - Structured object with extracted fields
   * @throws {InternalServerErrorException} If LLM processing fails
   */
  async extractShipmentFields(instructions: string): Promise<ExtractShipmentFieldsOutput> {
    if (!instructions?.trim()) {
      this.logger.debug("Empty instructions provided, returning empty result");
      return {};
    }

    this.logger.debug(`Extracting shipment fields from instructions: "${instructions}"`);

    try {
      const llmResponse = await this.askLLMService.ask({
        model: CORE_AGENT_LLM_MODEL_FULL,
        promptTemplate: PromptTemplateName.EXTRACT_SHIPMENT_FIELDS,
        variables: { instructions },
        zodSchema: ExtractShipmentFieldsSchema,
        temperature: 0.1,
        schemaName: "ExtractShipmentFieldsOutput",
        debug: true
      });

      // Debug: Log the raw response structure before parsing
      this.logger.debug(`Raw LLM Response ID: ${llmResponse.id}`);
      this.logger.debug(`Raw LLM Response Model: ${llmResponse.model}`);
      this.logger.debug(`Raw LLM Response Finish Reason: ${llmResponse.finishReason}`);
      this.logger.debug(`Raw LLM Response Message Content Type: ${typeof llmResponse.message?.content}`);
      this.logger.debug(`Raw LLM Response Message Content: ${JSON.stringify(llmResponse.message?.content)}`);
      this.logger.debug(`Raw LLM Response Parsed Field Exists: ${llmResponse.parsed !== undefined}`);
      this.logger.debug(`Raw LLM Response Parsed Value: ${JSON.stringify(llmResponse.parsed)}`);

      const extractedFields = llmResponse.parsed as ExtractShipmentFieldsOutput;

      // Clean up the extracted data to remove any malformed characters
      const cleanedFields = this.cleanExtractedFields(extractedFields);

      this.logger.debug(`Successfully extracted shipment fields: ${JSON.stringify(cleanedFields)}`);

      return cleanedFields;
    } catch (error) {
      this.logger.error(
        `Error extracting shipment fields from instructions: "${instructions}". Error: ${error.message}`,
        error.stack
      );
      throw new InternalServerErrorException(`Failed to extract shipment fields from instructions`);
    }
  }

  /**
   * Cleans up extracted fields to remove any malformed characters or invalid data.
   * This helps prevent validation errors caused by LLM response parsing issues.
   */
  private cleanExtractedFields(fields: ExtractShipmentFieldsOutput): ExtractShipmentFieldsOutput {
    const cleaned: ExtractShipmentFieldsOutput = {};

    if (fields.cargoControlNumber) {
      // Remove any non-alphanumeric characters except hyphens and underscores
      cleaned.cargoControlNumber = fields.cargoControlNumber.replace(/[^a-zA-Z0-9\-_]/g, "").trim();
      if (cleaned.cargoControlNumber.length === 0) {
        delete cleaned.cargoControlNumber;
      }
    }

    if (fields.portCode) {
      // Extract only digits and ensure it starts with 0
      const digits = fields.portCode.replace(/[^0-9]/g, "");
      if (digits.length === 4 && digits.startsWith("0")) {
        cleaned.portCode = digits;
      }
      // If it doesn't match the pattern, don't include it
    }

    if (fields.subLocation) {
      // Extract only digits and ensure it's exactly 4 characters
      const digits = fields.subLocation.replace(/[^0-9]/g, "");
      if (digits.length === 4) {
        cleaned.subLocation = digits;
      }
      // If it doesn't match the pattern, don't include it
    }

    this.logger.debug(`Cleaned fields: ${JSON.stringify(cleaned)} from original: ${JSON.stringify(fields)}`);
    return cleaned;
  }
}
