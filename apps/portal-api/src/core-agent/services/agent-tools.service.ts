import { CommercialInvoiceService } from "@/commercial-invoice/commercial-invoice.service";
import { DocumentService } from "@/document/services/document.service";
import { LlmToolDefinition } from "@/llm/ask-llm/interfaces/llm-tool.interface";
import { AskLLMService } from "@/llm/ask-llm/services/ask-llm.service";
import { ShipmentService } from "@/shipment/services/shipment.service";
import { Injectable } from "@nestjs/common";
import { z, ZodSchema } from "zod";
import { LookupShipmentDBSchema } from "../schemas/lookup-shipment-db.schema";
import { ShipmentIdentifierService } from "./shipment-identifier.service";
@Injectable()
export class AgentToolsService {
  constructor(
    private readonly askLLMService: AskLLMService,
    private readonly shipmentIdentifierService: ShipmentIdentifierService,
    private readonly shipmentService: ShipmentService,
    private readonly commercialInvoiceService: CommercialInvoiceService,
    private readonly documentService: DocumentService
  ) {}

  /**
   * Returns the list of LLM tool definitions available to the agent.
   */
  public getTools(organizationId: number): LlmToolDefinition<any>[] {
    const getShipmentByIdentifierTool: LlmToolDefinition<typeof LookupShipmentDBSchema._input> = {
      name: "getShipmentByIdentifier",
      description: "Queries the database to find a shipment ID based on an identifier type and value.",
      schema: LookupShipmentDBSchema,
      run: async (input) => {
        return this.shipmentIdentifierService.lookupShipmentInDB(input, organizationId);
      },
      strict: true
    };

    const getShipmentComplianceTool: LlmToolDefinition<{ shipmentId: number }> = {
      name: "getShipmentComplianceDetails",
      description: "Retrieves a detailed compliance validation report for a specific shipment.",
      schema: z.object({ shipmentId: z.number() }) as ZodSchema<{ shipmentId: number }>,
      run: async ({ shipmentId }) => {
        return this.shipmentService.getShipmentComplianceDetails(shipmentId);
      }
    };

    const getShipmentByIdTool: LlmToolDefinition<{ shipmentId: number }> = {
      name: "getShipmentById",
      description: "Fetches a shipment by its database ID, including all relations.",
      schema: z.object({ shipmentId: z.number() }) as ZodSchema<{ shipmentId: number }>,
      run: async ({ shipmentId }) => {
        return this.shipmentService.getShipmentById(shipmentId);
      }
    };

    const getShipmentCustomsActivitiesTool: LlmToolDefinition<{ shipmentId: number }> = {
      name: "getShipmentCustomsActivities",
      description: "Retrieves customs filing and activity history for a shipment.",
      schema: z.object({ shipmentId: z.number() }) as ZodSchema<{ shipmentId: number }>,
      run: async ({ shipmentId }) => {
        return this.shipmentService.getShipmentCustomsActivities(shipmentId);
      }
    };

    const getShipmentDutySummaryTool: LlmToolDefinition<{ shipmentId: number }> = {
      name: "getShipmentDutySummary",
      description: "Gets the total duties and taxes summary for a shipment.",
      schema: z.object({ shipmentId: z.number() }) as ZodSchema<{ shipmentId: number }>,
      run: async ({ shipmentId }) => {
        return this.shipmentService.getShipmentDutySummary(shipmentId);
      }
    };

    const getCommercialInvoicesTool: LlmToolDefinition<any> = {
      name: "getCommercialInvoices",
      description: "Lists commercial invoices linked to a shipment.",
      schema: z.object({
        shipmentId: z.number(),
        expandLines: z.boolean().optional(),
        skip: z.number().optional(),
        limit: z.number().optional()
      }),
      run: async ({ shipmentId, expandLines, skip, limit }) => {
        return this.commercialInvoiceService.getCommercialInvoices({
          shipmentId,
          expandLines,
          skip,
          limit
        });
      }
    };

    const getDocumentsTool: LlmToolDefinition<any> = {
      name: "getDocuments",
      description: "Lists documents attached to a shipment.",
      schema: z.object({
        shipmentId: z.number(),
        skip: z.number().optional(),
        limit: z.number().optional()
      }),
      run: async ({ shipmentId, skip, limit }) => {
        return this.documentService.getDocuments({ shipmentId, skip, limit });
      }
    };

    return [
      getShipmentByIdentifierTool,
      getShipmentComplianceTool,
      getShipmentByIdTool,
      getShipmentCustomsActivitiesTool,
      getShipmentDutySummaryTool,
      getCommercialInvoicesTool,
      getDocumentsTool
    ];
  }
}
