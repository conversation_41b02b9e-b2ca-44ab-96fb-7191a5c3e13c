import { Injectable, Logger, InternalServerErrorException, BadRequestException } from "@nestjs/common";
import { AskLLMService } from "@/llm/ask-llm/services/ask-llm.service";
import { MessageContent } from "../schemas/message-content.schema";
import { IdentifyShipmentOutput, IdentifyShipmentOutputSchema } from "../schemas/identify-shipment.schema";
import { DataSource, Brackets } from "typeorm";
import { Shipment, ShipmentColumn, Organization } from "nest-modules";
import {
  LookupShipmentDBInput,
  IdentifierTypeEnum,
  LlmLookupShipmentDBInput,
  LlmLookupShipmentDBOutput,
  LlmLookupShipmentDBInputSchema
} from "../schemas/lookup-shipment-db.schema";
import { PromptTemplateName } from "@/llm/prompt-template-manager";
import { LlmToolDefinition } from "@/llm/ask-llm/interfaces/llm-tool.interface";
import { LookupShipmentDBSchema } from "../schemas/lookup-shipment-db.schema";
import { CORE_AGENT_LLM_MODEL_MINI } from "../constants/llm.constants";
import { z } from "zod";

// Type alias for the inferred enum type
type ActualIdentifierType = z.infer<typeof IdentifierTypeEnum>;

const identifierTypeToColumnMapping: Record<ActualIdentifierType, ShipmentColumn> = {
  [IdentifierTypeEnum.enum.hblNumber]: ShipmentColumn.hblNumber,
  [IdentifierTypeEnum.enum.cargoControlNumber]: ShipmentColumn.cargoControlNumber,
  [IdentifierTypeEnum.enum.transactionNumber]: ShipmentColumn.transactionNumber, // Also used for PARS
  [IdentifierTypeEnum.enum.containerNumber]: ShipmentColumn.containerNumber
};

export const ALL_SEARCHABLE_IDENTIFIER_FIELDS: { key: ShipmentColumn; type: ActualIdentifierType }[] = [
  {
    key: ShipmentColumn.hblNumber,
    type: IdentifierTypeEnum.enum.hblNumber as ActualIdentifierType
  },
  {
    key: ShipmentColumn.cargoControlNumber,
    type: IdentifierTypeEnum.enum.cargoControlNumber as ActualIdentifierType
  },
  {
    key: ShipmentColumn.transactionNumber, // Used for PARS and direct Transaction Number searches
    type: IdentifierTypeEnum.enum.transactionNumber as ActualIdentifierType
  },
  {
    key: ShipmentColumn.containerNumber,
    type: IdentifierTypeEnum.enum.containerNumber as ActualIdentifierType
  }
];

// Whitelist of allowed column names for SQL injection prevention
const ALLOWED_SHIPMENT_COLUMNS = new Set([
  ShipmentColumn.hblNumber,
  ShipmentColumn.cargoControlNumber,
  ShipmentColumn.transactionNumber,
  ShipmentColumn.containerNumber
]);

@Injectable()
export class ShipmentIdentifierService {
  private readonly logger = new Logger(ShipmentIdentifierService.name);

  constructor(
    private readonly askLLMService: AskLLMService,
    private readonly dataSource: DataSource
  ) {}

  /**
   * Validates that a column name is safe to use in SQL queries.
   * Prevents SQL injection by ensuring only whitelisted column names are used.
   * @param columnName - The column name to validate
   * @returns true if the column name is safe to use
   * @throws BadRequestException if the column name is not in the whitelist
   */
  private validateColumnName(columnName: string): boolean {
    if (!ALLOWED_SHIPMENT_COLUMNS.has(columnName as ShipmentColumn)) {
      this.logger.error(`Invalid column name attempted: ${columnName}`);
      throw new BadRequestException(`Invalid column name: ${columnName}`);
    }
    return true;
  }

  /**
   * Uses LLM agent with a DB lookup tool to identify shipments from email content.
   * @param emailContent - The full message content with text, subject, and history
   * @param organizationId - Optional organization ID for data isolation
   * @returns Object containing found identifiers and a reason for the identification result
   * @throws InternalServerErrorException if no organization ID is available
   */
  async identifyShipment(
    emailContent: MessageContent,
    organizationId: number
  ): Promise<IdentifyShipmentOutput> {
    if (!organizationId) {
      throw new BadRequestException("Organization context missing for identifyShipment");
    }

    const llmLookupShipmentTool: LlmToolDefinition<LlmLookupShipmentDBInput> = {
      name: "llmLookupShipmentDB",
      description:
        "Queries the database to find a shipment ID based on an identifier value. It can optionally try an initialGuessedType first, then comprehensively searches all known identifier types. Returns the shipment ID, the actual type of identifier that matched, the original value, and a reason for the outcome.",
      schema: LlmLookupShipmentDBInputSchema,
      run: async (input: LlmLookupShipmentDBInput): Promise<LlmLookupShipmentDBOutput> => {
        return this._llmLookupShipmentInDB(input, organizationId);
      },
      strict: true
    };

    let result;
    try {
      result = await this.askLLMService.agent({
        promptTemplate: PromptTemplateName.IDENTIFY_SHIPMENT,
        variables: { emailContent: JSON.stringify(emailContent) },
        zodSchema: IdentifyShipmentOutputSchema,
        schemaName: "IdentifyShipmentOutput",
        model: CORE_AGENT_LLM_MODEL_MINI,
        tools: [llmLookupShipmentTool]
      });
    } catch (err) {
      this.logger.error(`LLM agent threw`, err.stack);
      return {
        foundIdentifiers: [],
        reason: `Error: LLM agent failed – ${err.message}`
      };
    }

    if (!result?.parsed) {
      this.logger.error("LLM agent did not return a valid parsed result for identifyShipment");
      return {
        foundIdentifiers: [],
        reason: "Error: Failed to identify shipments. LLM agent did not return a valid result."
      };
    }
    return result.parsed;
  }

  /**
   * Public wrapper for searching shipments in the database using specified identifier type and value.
   * Intended for use by external services like agent tools.
   * @param input - The validated input containing identifierType and identifierValue
   * @param organizationId - The organization ID for data isolation
   * @returns Object containing the shipment ID if found uniquely, the matched identifier type, and the original identifier value
   */
  async lookupShipmentInDB(
    input: LookupShipmentDBInput,
    organizationId: number
  ): Promise<{
    shipmentId: number | null;
    matchedIdentifierType?: ActualIdentifierType;
    identifierValue?: string;
  }> {
    return this._lookupShipmentInDB(input, organizationId);
  }

  /**
   * Private helper method that performs comprehensive search across all identifier fields.
   * Encapsulates the shared query building, execution, and result processing logic.
   * @param identifierValue - The identifier value to search for
   * @param organizationId - The organization ID for data isolation
   * @returns Object containing potential shipments and matched identifier type information
   */
  private async _performComprehensiveSearch(
    identifierValue: string,
    organizationId: number
  ): Promise<{
    potentialShipments: any[];
    matchedIdentifierType: ActualIdentifierType | null;
    shipmentId: number | null;
  }> {
    const queryBuilder = this.dataSource.manager
      .createQueryBuilder(Shipment, "shipment")
      .select("shipment.id", "id");

    ALL_SEARCHABLE_IDENTIFIER_FIELDS.forEach((field) => {
      queryBuilder.addSelect(`shipment.${field.key}`, field.key);
    });

    queryBuilder
      .where("shipment.organizationId = :organizationId", { organizationId })
      .andWhere(
        new Brackets((qb) => {
          ALL_SEARCHABLE_IDENTIFIER_FIELDS.forEach((field, index) => {
            const paramName = `identifierValue${index}`;
            // Use UPPER() for case-insensitive comparison at SQL level
            const condition = `UPPER(shipment.${field.key}) = UPPER(:${paramName})`;
            if (index === 0) {
              qb.where(condition, { [paramName]: identifierValue });
            } else {
              qb.orWhere(condition, { [paramName]: identifierValue });
            }
          });
        })
      )
      .take(2); // Need at most 2 to determine uniqueness

    const potentialShipments = await queryBuilder.getRawMany();

    if (!potentialShipments || potentialShipments.length === 0) {
      return {
        potentialShipments: [],
        matchedIdentifierType: null,
        shipmentId: null
      };
    }

    if (potentialShipments.length > 1) {
      return {
        potentialShipments,
        matchedIdentifierType: null,
        shipmentId: null
      };
    }

    // Exactly one shipment found
    const shipmentData = potentialShipments[0];
    const shipmentId = shipmentData.id;
    let matchedIdentifierType: ActualIdentifierType | null = null;

    for (const field of ALL_SEARCHABLE_IDENTIFIER_FIELDS) {
      const dbFieldValue = shipmentData[field.key];
      // Since SQL query now uses case-insensitive matching, we can use simple equality check
      // The database will only return records that match case-insensitively
      if (typeof dbFieldValue === "string" && dbFieldValue.toUpperCase() === identifierValue.toUpperCase()) {
        matchedIdentifierType = field.type;
        break;
      }
    }

    return {
      potentialShipments,
      matchedIdentifierType,
      shipmentId
    };
  }

  /**
   * Searches for shipments in the database using specified identifier type and value.
   * If the initial identifierType yields no result, it attempts to find the shipment
   * using other known identifier types.
   * @param input - The validated input containing identifierType and identifierValue
   * @param organizationId - The organization ID for data isolation
   * @returns Object containing the shipment ID if found uniquely, otherwise null
   */
  private async _lookupShipmentInDB(
    input: LookupShipmentDBInput,
    organizationId: number
  ): Promise<{
    shipmentId: number | null;
    matchedIdentifierType?: ActualIdentifierType;
    identifierValue?: string;
  }> {
    const { identifierValue } = input; // initialIdentifierType is logged but not strictly needed for the OR query
    this.logger.log(
      `[Tool Call] Received lookup request for value '${identifierValue}', initial guessed type: ${input.identifierType} (Org: ${organizationId})`
    );

    try {
      this.logger.debug(
        `[Tool Call] Executing single query for '${identifierValue}' across multiple fields in org ${organizationId}`
      );

      const searchResult = await this._performComprehensiveSearch(identifierValue, organizationId);

      if (searchResult.potentialShipments.length === 0) {
        this.logger.log(
          `[Tool Call] No shipment found for value '${identifierValue}' in org ${organizationId} across all types.`
        );
        return { shipmentId: null, identifierValue };
      } else if (searchResult.potentialShipments.length > 1) {
        this.logger.warn(
          `[Tool Call] Multiple shipments (${searchResult.potentialShipments.length}) found for value '${identifierValue}' in org ${organizationId}. IDs: ${searchResult.potentialShipments.map((s) => s.id).join(", ")}. Ambiguous. Returning null.`
        );
        return { shipmentId: null, identifierValue };
      }

      // Exactly one shipment found
      const { shipmentId, matchedIdentifierType } = searchResult;

      if (matchedIdentifierType) {
        this.logger.log(
          `[Tool Call] Found unique shipment ID ${shipmentId} for value '${identifierValue}' (matched type: ${matchedIdentifierType}) in org ${organizationId}`
        );
        return { shipmentId, matchedIdentifierType, identifierValue };
      } else {
        // This case should be rare if the query logic is correct, implies a match without knowing how
        this.logger.warn(
          `[Tool Call] Found unique shipment ID ${shipmentId} for '${identifierValue}' but could not determine matched type. Org: ${organizationId}. Returning without type.`
        );
        return { shipmentId, identifierValue }; // Still return shipmentId if found
      }
    } catch (err) {
      this.logger.error(
        `[Tool Call] Error during single query database search for '${identifierValue}' in org ${organizationId}: ${err.message}`,
        err.stack
      );
      return { shipmentId: null, identifierValue }; // Ensure identifierValue is returned on error for context
    }
  }

  /**
   * New tool implementation for llmLookupShipmentDB as per PRD.
   * Searches for shipments in the database using specified identifier value, an optional initial guessed type,
   * and then a comprehensive fallback search.
   * @param input - The validated input containing identifierValue and optional initialGuessedType
   * @param organizationId - The organization ID for data isolation
   * @returns LlmLookupShipmentDBOutput
   */
  private async _llmLookupShipmentInDB(
    input: LlmLookupShipmentDBInput,
    organizationId: number
  ): Promise<LlmLookupShipmentDBOutput> {
    const { identifierValue, initialGuessedType } = input;
    this.logger.log(
      `[Tool Call - llmLookupShipmentDB] Received lookup for value '${identifierValue}', initial guess: ${initialGuessedType || "None"} (Org: ${organizationId})`
    );

    // 1. Primary Search (if initialGuessedType provided)
    if (initialGuessedType) {
      const guessedColumn = identifierTypeToColumnMapping[initialGuessedType];
      if (guessedColumn) {
        // Validate column name to prevent SQL injection
        this.validateColumnName(guessedColumn);

        this.logger.debug(
          `[Tool Call - llmLookupShipmentDB] Primary search for '${identifierValue}' as ${initialGuessedType} (column: ${guessedColumn}) in org ${organizationId}`
        );
        const primarySearchQb = this.dataSource.manager
          .createQueryBuilder(Shipment, "shipment")
          .select("shipment.id", "id")
          .addSelect(`shipment.${guessedColumn}`, guessedColumn)
          .where("shipment.organizationId = :organizationId", { organizationId })
          .andWhere(`UPPER(shipment.${guessedColumn}) = UPPER(:identifierValue)`, { identifierValue })
          .take(2);

        try {
          const potentialShipments = await primarySearchQb.getRawMany();
          if (potentialShipments.length === 1) {
            const shipmentId = potentialShipments[0].id;
            this.logger.log(
              `[Tool Call - llmLookupShipmentDB] Primary search SUCCESS: Found unique shipment ID ${shipmentId} for '${identifierValue}' as ${initialGuessedType}. Org: ${organizationId}`
            );
            return {
              shipmentId,
              matchedIdentifierType: initialGuessedType,
              identifierValue,
              reason: `Found shipment ${shipmentId} matching ${initialGuessedType} '${identifierValue}'.`
            };
          } else if (potentialShipments.length > 1) {
            this.logger.warn(
              `[Tool Call - llmLookupShipmentDB] Primary search AMBIGUOUS: Multiple shipments for '${identifierValue}' as ${initialGuessedType}. Org: ${organizationId}. Proceeding to comprehensive.`
            );
            // Proceed to comprehensive search
          } else {
            this.logger.log(
              `[Tool Call - llmLookupShipmentDB] Primary search FAILED: No shipment found for '${identifierValue}' as ${initialGuessedType}. Org: ${organizationId}. Proceeding to comprehensive.`
            );
            // Proceed to comprehensive search
          }
        } catch (err) {
          this.logger.error(
            `[Tool Call - llmLookupShipmentDB] Error during primary search for '${identifierValue}' as ${initialGuessedType} in org ${organizationId}: ${err.message}`,
            err.stack
          );
          // Proceed to comprehensive search on error as well, to be safe
        }
      }
    }

    // 2. Comprehensive Fallback Search
    this.logger.debug(
      `[Tool Call - llmLookupShipmentDB] Comprehensive search for '${identifierValue}' across all types in org ${organizationId}`
    );

    try {
      const searchResult = await this._performComprehensiveSearch(identifierValue, organizationId);

      if (searchResult.potentialShipments.length === 0) {
        this.logger.log(
          `[Tool Call - llmLookupShipmentDB] Comprehensive FAILED: No shipment found for '${identifierValue}' across all types. Org: ${organizationId}`
        );
        return {
          shipmentId: null,
          matchedIdentifierType: null,
          identifierValue,
          reason: `No shipment found for '${identifierValue}' after checking HBL, CCN, PARS, Container Number, Transaction Number.`
        };
      } else if (searchResult.potentialShipments.length > 1) {
        const ids = searchResult.potentialShipments.map((s) => s.id).join(", ");
        this.logger.warn(
          `[Tool Call - llmLookupShipmentDB] Comprehensive AMBIGUOUS: Multiple shipments (${searchResult.potentialShipments.length}) found for '${identifierValue}'. IDs: ${ids}. Org: ${organizationId}.`
        );
        return {
          shipmentId: null,
          matchedIdentifierType: null,
          identifierValue,
          reason: `Multiple shipments found for '${identifierValue}'. Ambiguous.`
        };
      }

      // Exactly one shipment found in comprehensive search
      const { shipmentId, matchedIdentifierType } = searchResult;

      if (matchedIdentifierType) {
        this.logger.log(
          `[Tool Call - llmLookupShipmentDB] Comprehensive SUCCESS: Found unique shipment ID ${shipmentId} for '${identifierValue}' (matched type: ${matchedIdentifierType}). Org: ${organizationId}`
        );
        return {
          shipmentId,
          matchedIdentifierType,
          identifierValue,
          reason: `Found shipment ${shipmentId} matching ${matchedIdentifierType} '${identifierValue}'.`
        };
      } else {
        this.logger.warn(
          `[Tool Call - llmLookupShipmentDB] Comprehensive WARNING: Found unique shipment ID ${shipmentId} for '${identifierValue}' but could not determine matched type. This should be rare. Org: ${organizationId}.`
        );
        // This case is unlikely if the ORM query worked, but as a fallback:
        return {
          shipmentId, // Still return ID if found
          matchedIdentifierType: null,
          identifierValue,
          reason: `Found shipment ${shipmentId} for '${identifierValue}', but specific matching type could not be confirmed.`
        };
      }
    } catch (err) {
      this.logger.error(
        `[Tool Call - llmLookupShipmentDB] Error during comprehensive database search for '${identifierValue}' in org ${organizationId}: ${err.message}`,
        err.stack
      );
      return {
        shipmentId: null,
        matchedIdentifierType: null,
        identifierValue,
        reason: `Error during database search for '${identifierValue}': ${err.message}`
      };
    }
  }
}
