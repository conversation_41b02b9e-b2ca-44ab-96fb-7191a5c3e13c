import { Injectable, Inject, Logger, OnModuleInit } from "@nestjs/common";
import { EMAIL_INTENTS } from "nest-modules";
import { IntentHandler, IntentClassificationMeta } from "../interfaces/intent-handler.interface";

/**
 * Registry service that auto-discovers and manages all intent handlers.
 * Provides mapping between EMAIL_INTENTS and their corresponding handlers.
 */
@Injectable()
export class IntentHandlerRegistry implements OnModuleInit {
  private readonly logger = new Logger(IntentHandlerRegistry.name);
  private readonly handlerMap = new Map<(typeof EMAIL_INTENTS)[number], IntentHandler>();
  private registrationComplete = false;
  private registrationTime?: Date;

  constructor(
    // NestJS will automatically inject ALL implementations of IntentHandler
    @Inject("INTENT_HANDLERS") private readonly allHandlers: IntentHandler[]
  ) {
    this.logger.log(
      `IntentHandlerRegistry constructor called with ${this.allHandlers?.length || 0} handlers`
    );
  }

  async onModuleInit(): Promise<void> {
    this.logger.log("IntentHandlerRegistry.onModuleInit() called - starting handler registration");
    this.registerHandlers();
    this.logger.log("IntentHandlerRegistry.onModuleInit() completed");
  }

  /**
   * Register all injected intent handlers and validate the registry.
   */
  private registerHandlers(): void {
    this.logger.log(`Registering ${this.allHandlers.length} intent handlers...`);
    this.logger.debug(`Handler types: ${this.allHandlers.map((h) => h.constructor.name).join(", ")}`);

    // Clear existing registrations
    this.handlerMap.clear();

    // Register each handler
    for (const handler of this.allHandlers) {
      try {
        this.logger.debug(`Processing handler: ${handler.constructor.name}`);

        // Check if handler has classification metadata
        if (!handler.classificationMeta) {
          this.logger.error(`Handler ${handler.constructor.name} missing classificationMeta`);
          continue;
        }

        const { intent } = handler.classificationMeta;
        this.logger.debug(`Handler ${handler.constructor.name} declares intent: ${intent}`);

        // Check for duplicate registrations
        if (this.handlerMap.has(intent)) {
          const existingHandler = this.handlerMap.get(intent);
          this.logger.warn(
            `Duplicate handler registration for intent '${intent}': ` +
              `${existingHandler?.constructor.name} will be replaced by ${handler.constructor.name}`
          );
        }

        // Register the handler
        this.handlerMap.set(intent, handler);
      } catch (error) {
        this.logger.error(
          `Failed to register handler ${handler.constructor.name}: ${error.message}`,
          error.stack
        );
      }
    }

    this.registrationComplete = true;
    this.registrationTime = new Date();

    this.logger.log(
      `Successfully registered ${this.handlerMap.size} intent handlers. ` +
        `Missing: ${this.getMissingHandlers().join(", ") || "none"}`
    );
  }

  /**
   * Get the handler for a specific intent type.
   */
  getHandler(intentType: (typeof EMAIL_INTENTS)[number]): IntentHandler | undefined {
    // Ensure handlers are registered if not already done
    if (!this.registrationComplete && this.allHandlers.length > 0) {
      this.logger.warn("Handler map empty but handlers available - forcing registration");
      this.registerHandlers();
    }
    return this.handlerMap.get(intentType);
  }

  /**
   * Check if a handler exists for the given intent type.
   */
  hasHandler(intentType: (typeof EMAIL_INTENTS)[number]): boolean {
    return this.handlerMap.has(intentType);
  }

  /**
   * Get all supported intent types (those that have registered handlers).
   */
  getAllSupportedIntents(): (typeof EMAIL_INTENTS)[number][] {
    // Ensure handlers are registered if not already done
    if (!this.registrationComplete && this.allHandlers.length > 0) {
      this.logger.warn("Handler map empty but handlers available - forcing registration");
      this.registerHandlers();
    }
    return Array.from(this.handlerMap.keys());
  }

  /**
   * Get all registered handlers.
   */
  getAllHandlers(): IntentHandler[] {
    return Array.from(this.handlerMap.values());
  }

  /**
   * Get classification metadata for all registered handlers.
   * This can be used to dynamically generate LLM classification prompts.
   */
  getAllClassificationMeta(): IntentClassificationMeta[] {
    return this.getAllHandlers().map((handler) => handler.classificationMeta);
  }

  /**
   * Get classification metadata for a specific intent type.
   */
  getClassificationMeta(intentType: (typeof EMAIL_INTENTS)[number]): IntentClassificationMeta | undefined {
    const handler = this.getHandler(intentType);
    return handler?.classificationMeta;
  }

  /**
   * Get system diagnostics for monitoring and debugging.
   */
  getSystemDiagnostics(): {
    registeredHandlers: Array<{
      intentType: string;
      handlerClass: string;
      exampleCount: number;
      hasKeywords: boolean;
    }>;
    totalHandlers: number;
    registrationComplete: boolean;
    registrationTime?: Date;
    missingHandlers: string[];
  } {
    // Ensure handlers are registered if not already done
    if (!this.registrationComplete && this.allHandlers.length > 0) {
      this.logger.warn(
        "Handler map empty but handlers available - forcing registration in getSystemDiagnostics"
      );
      this.registerHandlers();
    }

    return {
      registeredHandlers: Array.from(this.handlerMap.entries()).map(([intentType, handler]) => ({
        intentType,
        handlerClass: handler.constructor.name,
        exampleCount: handler.classificationMeta.examples.length,
        hasKeywords: Boolean(handler.classificationMeta.keywords?.length)
      })),
      totalHandlers: this.handlerMap.size,
      registrationComplete: this.registrationComplete,
      registrationTime: this.registrationTime,
      missingHandlers: this.getMissingHandlers()
    };
  }

  /**
   * Validate that all handlers are properly configured.
   * Returns true if all handlers are valid, false otherwise.
   */
  validateAllHandlers(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check if we have handlers for critical intents
    const criticalIntents: (typeof EMAIL_INTENTS)[number][] = [
      "GET_SHIPMENT_STATUS",
      "REQUEST_CAD_DOCUMENT",
      "REQUEST_RNS_PROOF",
      "REQUEST_RUSH_PROCESSING",
      "PROCESS_DOCUMENT"
    ];

    for (const intent of criticalIntents) {
      if (!this.hasHandler(intent)) {
        errors.push(`Missing handler for critical intent: ${intent}`);
      }
    }

    // Validate each registered handler
    for (const [intent, handler] of this.handlerMap.entries()) {
      try {
        const meta = handler.classificationMeta;

        if (!meta.description || meta.description.trim().length === 0) {
          errors.push(`Handler ${handler.constructor.name} has empty description`);
        }

        if (!meta.examples || meta.examples.length === 0) {
          errors.push(`Handler ${handler.constructor.name} has no examples`);
        }

        if (meta.intent !== intent) {
          errors.push(
            `Handler ${handler.constructor.name} intent mismatch: ` +
              `registered as ${intent} but declares ${meta.intent}`
          );
        }
      } catch (error) {
        errors.push(`Handler ${handler.constructor.name} validation failed: ${error.message}`);
      }
    }

    const isValid = errors.length === 0;

    if (!isValid) {
      this.logger.warn(`Handler validation failed with ${errors.length} errors:`, errors);
    }

    return { isValid, errors };
  }

  /**
   * Force re-registration of all handlers (useful for testing).
   */
  forceReregistration(): void {
    this.logger.debug("Forcing re-registration of all intent handlers");
    this.registerHandlers();
  }

  /**
   * Get list of intent types that don't have handlers.
   */
  private getMissingHandlers(): string[] {
    const allIntentTypes = [...EMAIL_INTENTS];
    const registeredIntents = this.getAllSupportedIntents();

    return allIntentTypes.filter((intent) => !registeredIntents.includes(intent));
  }
}
