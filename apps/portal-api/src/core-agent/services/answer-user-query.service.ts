import { Injectable, Logger, InternalServerErrorException } from "@nestjs/common";
import { AskLLMService } from "@/llm/ask-llm/services/ask-llm.service";
import { PromptTemplateName } from "@/llm/prompt-template-manager";
import {
  ClassifyQuestionCategorySchema,
  ClassifyQuestionCategoryOutput
} from "../schemas/classify-question-category.schema";
import { CORE_AGENT_LLM_MODEL_FULL } from "../constants/llm.constants";
import { QUESTION_CATEGORIES } from "../constants/question-categories.constants";

/**
 * Service responsible for classifying user queries about shipments.
 */
@Injectable()
export class AnswerUserQueryService {
  private readonly logger = new Logger(AnswerUserQueryService.name);

  constructor(private readonly askLLMService: AskLLMService) {}

  async classifyQuestion(userQuery: string): Promise<ClassifyQuestionCategoryOutput> {
    this.logger.log("Classifying single user query");

    if (!userQuery || typeof userQuery !== "string" || userQuery.trim().length === 0) {
      this.logger.error("User query is empty or invalid.");
      throw new InternalServerErrorException("Cannot classify an empty query.");
    }

    try {
      // Transform to a plain object for the template
      const categoriesForPrompt = Object.fromEntries(
        Object.entries(QUESTION_CATEGORIES).map(([_, value]) => [
          value.category, // use the string category, not the enum key
          {
            description: value.description,
            examples: value.examples
          }
        ])
      );
      const llmResponse = await this.askLLMService.ask({
        model: CORE_AGENT_LLM_MODEL_FULL,
        promptTemplate: PromptTemplateName.CLASSIFY_QUESTION,
        variables: { userQuery, categories: categoriesForPrompt },
        zodSchema: ClassifyQuestionCategorySchema,
        schemaName: "ClassifyQuestionCategoryOutput",
        debug: true
      });

      // Validate the LLM response using Zod schema (already done by askLLMService)
      // but add additional runtime checks for safety
      if (!llmResponse.parsed || typeof llmResponse.parsed !== "object") {
        this.logger.error("LLM response is not a valid object");
        throw new InternalServerErrorException(
          "Could not classify user query. LLM response was not in the expected format."
        );
      }

      const parsed = llmResponse.parsed as ClassifyQuestionCategoryOutput;

      if (!parsed.category || typeof parsed.category !== "string") {
        this.logger.error("Failed to parse Question Category from LLM response");
        throw new InternalServerErrorException(
          "Could not classify user query. LLM response was not in the expected format."
        );
      }

      this.logger.log(`Successfully classified query as Category: ${parsed.category}`);
      return parsed;
    } catch (error) {
      this.logger.error(`Failed to classify user query: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Failed to classify user query: ${error.message}`);
    }
  }
}
