import { Injectable, Logger, BadRequestException, UnprocessableEntityException } from "@nestjs/common";
import { AskLLMService } from "@/llm/ask-llm/services/ask-llm.service";
import { IntentHandlerRegistry } from "./intent-handler-registry.service";
import { MessageContent } from "@/core-agent/schemas/message-content.schema";
import { PromptTemplateName } from "@/llm/prompt-template-manager";
import { TaskListSchema, TaskList } from "../schemas/task-decomposition.schema";
import {
  ClassifiedTasks,
  ClassifyIntentOnlyOutput,
  ClassifyIntentOnlySchema,
  TaskIntent,
  TaskIntentEnum
} from "../schemas/classified-task.schema";
import { ValidatedEmailIntents, ValidatedEmailAction } from "@/email/schemas/validated-email-intents.schema";
import { EMAIL_INTENTS } from "nest-modules";
import { CORE_AGENT_LLM_MODEL_MINI, CORE_AGENT_LLM_MODEL_FULL } from "../constants/llm.constants";
import { limitConcurrency } from "@/core-agent/utils/concurrency-limit.util";

/**
 * Service for analyzing email content to identify intents and actions.
 */
@Injectable()
export class EmailIntentAnalysisService {
  private readonly logger = new Logger(EmailIntentAnalysisService.name);

  constructor(
    private readonly askLLMService: AskLLMService,
    private readonly intentHandlerRegistry: IntentHandlerRegistry
  ) {}

  /**
   * Breaks down email content into actionable task strings using an LLM.
   * @param emailContent - The full message content with text, subject, and history
   * @returns An object containing an array of task strings
   * @throws InternalServerErrorException if LLM response parsing fails
   */
  private async decomposeEmailToTasks(emailContent: MessageContent): Promise<TaskList> {
    try {
      const llmResponse = await this.askLLMService.ask({
        model: CORE_AGENT_LLM_MODEL_FULL,
        promptTemplate: PromptTemplateName.DECOMPOSE_TASK,
        variables: { emailContent },
        zodSchema: TaskListSchema,
        schemaName: "TaskList",
        debug: true
      });

      if (!llmResponse.parsed) {
        this.logger.error("Failed to parse task decomposition strings from LLM response.");
        throw new UnprocessableEntityException(
          "Could not decompose tasks from message. LLM response was not in the expected format."
        );
      }
      const result: TaskList = llmResponse.parsed as TaskList;
      return result;
    } catch (error) {
      this.logger.error(`Failed to decompose tasks: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Classifies a single task string and returns its intent.
   * @param task - The task string to classify
   * @returns The classified intent enum value
   */
  async classifyTaskIntent(task: string): Promise<TaskIntent> {
    try {
      // Get dynamic handler metadata
      const handlerIntents = this.intentHandlerRegistry.getAllClassificationMeta();

      this.logger.debug(`Using ${handlerIntents.length} handler intents for classification`);

      const llmResponse = await this.askLLMService.ask({
        model: CORE_AGENT_LLM_MODEL_FULL,
        promptTemplate: PromptTemplateName.CLASSIFY_INTENT,
        variables: { task, handlerIntents },
        zodSchema: ClassifyIntentOnlySchema,
        schemaName: "ClassifyIntent",
        temperature: 0.1,
        debug: true
      });

      if (!llmResponse.parsed?.intent) {
        this.logger.error(`Could not parse intent of task: "${task}".`);
        throw new BadRequestException(`Failed to parse intent classification for task: "${task}".`);
      }

      const result = llmResponse.parsed as ClassifyIntentOnlyOutput;
      return result.intent;
    } catch (error) {
      this.logger.error(`LLM Error classifying intent for task "${task}": ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Classifies the intent for each task string in the provided collection.
   * @param decomposedTasks - Object containing the array of task strings
   * @returns An object containing an array of ClassifiedTask objects
   */
  private async classifyTaskIntents(decomposedTasks: TaskList): Promise<ClassifiedTasks> {
    try {
      if (!decomposedTasks || !decomposedTasks.tasks || decomposedTasks.tasks.length === 0) {
        this.logger.log("No tasks provided for intent classification.");
        return { classifiedTasks: [] };
      }

      this.logger.log(`Classifying intents for ${decomposedTasks.tasks.length} decomposed tasks.`);

      /**
       * Use concurrency limiter to avoid LLM rate limits.
       * Each task is wrapped as a deferred function for limitConcurrency.
       */
      const orderedClassifiedTasks = await limitConcurrency(
        decomposedTasks.tasks.map((task) => async () => ({
          task,
          intent: await this.classifyTaskIntent(task)
        })),
        5 // max concurrency
      );

      this.logger.log(`Finished classifying intents for ${orderedClassifiedTasks.length} tasks.`);

      return { classifiedTasks: orderedClassifiedTasks };
    } catch (error) {
      this.logger.error(`Error during classifyTaskIntents: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Maps classified tasks to the ValidatedEmailIntents format used by the email system.
   * @param classifiedTasks - The output from classifyTaskIntents
   * @returns ValidatedEmailIntents object ready for consumption by email handlers
   */
  private async adaptClassifiedTasksToValidatedIntents(
    classifiedTasks: ClassifiedTasks
  ): Promise<ValidatedEmailIntents> {
    try {
      this.logger.log(
        `Adapting ${classifiedTasks.classifiedTasks.length} classified tasks to ValidatedEmailIntents (using EMAIL_INTENTS strings)`
      );
      const validatedActions: ValidatedEmailAction[] = [];

      for (const classifiedTask of classifiedTasks.classifiedTasks) {
        let outputIntent: (typeof EMAIL_INTENTS)[number];

        switch (classifiedTask.intent) {
          case TaskIntentEnum.enum.GET_SHIPMENT_STATUS:
            outputIntent = "GET_SHIPMENT_STATUS";
            break;
          case TaskIntentEnum.enum.SPAM:
            outputIntent = "SPAM";
            break;
          case TaskIntentEnum.enum.UNKNOWN:
            outputIntent = "UNKNOWN";
            break;
          case TaskIntentEnum.enum.PROCESS_DOCUMENT:
            outputIntent = "PROCESS_DOCUMENT";
            break;
          case TaskIntentEnum.enum.DOCUMENTATION_COMING:
            outputIntent = "DOCUMENTATION_COMING";
            break;
          case TaskIntentEnum.enum.REQUEST_RUSH_PROCESSING:
            outputIntent = "REQUEST_RUSH_PROCESSING";
            break;
          case TaskIntentEnum.enum.REQUEST_MANUAL_PROCESSING:
            outputIntent = "REQUEST_MANUAL_PROCESSING";
            break;
          case TaskIntentEnum.enum.REQUEST_HOLD_SHIPMENT:
            outputIntent = "REQUEST_HOLD_SHIPMENT";
            break;
          case TaskIntentEnum.enum.REQUEST_CAD_DOCUMENT:
            outputIntent = "REQUEST_CAD_DOCUMENT";
            break;
          case TaskIntentEnum.enum.REQUEST_RNS_PROOF:
            outputIntent = "REQUEST_RNS_PROOF";
            break;
          case TaskIntentEnum.enum.UPDATE_SHIPMENT:
            outputIntent = "UPDATE_SHIPMENT";
            break;
          // Fallback for unmapped intents:
          case TaskIntentEnum.enum.UNSORTED:
          default:
            outputIntent = "UNSORTED";
            break;
        }

        const action: ValidatedEmailAction = {
          intent: outputIntent,
          instructions: [classifiedTask.task],
          shipmentReference: undefined,
          shippingData: undefined,
          attachments: undefined
        };

        validatedActions.push(action);
      }

      this.logger.log(`Adaptor Successfully created ${validatedActions.length} ValidatedEmailActions.`);
      return { intents: validatedActions };
    } catch (error) {
      this.logger.error(`Error during adaptClassifiedTasksToValidatedIntents: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Complete pipeline that analyzes email content to extract and validate intents.
   * @param emailContent - The full message content with text, subject, and history
   * @returns ValidatedEmailIntents containing the structured intents and actions
   */
  async analyzeEmailIntents(emailContent: MessageContent): Promise<ValidatedEmailIntents> {
    this.logger.log(`Starting full intent analysis pipeline.`);
    try {
      const decomposedTasks = await this.decomposeEmailToTasks(emailContent);
      if (!decomposedTasks.tasks || decomposedTasks.tasks.length === 0) {
        this.logger.log(`No tasks decomposed, returning empty intents.`);
        return { intents: [] };
      }

      const classifiedTasks = await this.classifyTaskIntents(decomposedTasks);

      const validatedIntents = await this.adaptClassifiedTasksToValidatedIntents(classifiedTasks);

      this.logger.log(`Intent analysis pipeline completed.`);
      return validatedIntents;
    } catch (error) {
      this.logger.error(`Error during analyzeEmailIntents pipeline: ${error.message}`, error.stack);
      throw error;
    }
  }
}
