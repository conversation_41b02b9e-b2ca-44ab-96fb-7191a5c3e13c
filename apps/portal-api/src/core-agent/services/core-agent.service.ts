import {
  Injectable,
  Scope,
  Logger,
  Inject,
  InternalServerErrorException,
  NotFoundException,
  forwardRef
} from "@nestjs/common";
import { DataSource, QueryRunner } from "typeorm";
import { Shipment } from "nest-modules";
import { ValidatedEmailIntents } from "@/email/schemas/validated-email-intents.schema";
import { MessageContent } from "../schemas/message-content.schema";
import { IdentifyShipmentOutput } from "../schemas/identify-shipment.schema";
import { AskLLMService } from "@/llm/ask-llm/services/ask-llm.service";
import { ShipmentService } from "@/shipment/services/shipment.service";
import { EmailIntentAnalysisService } from "./email-intent-analysis.service";
import { AnswerUserQueryService } from "./answer-user-query.service";
import { ShipmentIdentifierService } from "./shipment-identifier.service";
import { EmailService } from "@/email/services/email.service";

@Injectable({ scope: Scope.REQUEST })
export class CoreAgentService {
  private readonly logger = new Logger(CoreAgentService.name);

  constructor(
    private readonly dataSource: DataSource,
    private readonly askLLMService: AskLLMService,
    private readonly shipmentService: ShipmentService,
    private readonly emailIntentAnalysisService: EmailIntentAnalysisService,
    @Inject(forwardRef(() => AnswerUserQueryService))
    private readonly answerUserQueryService: AnswerUserQueryService,
    private readonly shipmentIdentifierService: ShipmentIdentifierService,
    @Inject(EmailService) private readonly emailService: EmailService
  ) {}

  /**
   * Retrieves a shipment by ID using the underlying ShipmentService.
   * The ShipmentService handles organization isolation via the REQUEST object.
   *
   * @param shipmentId - The ID of the shipment to retrieve
   * @param queryRunner - Optional queryRunner to use for the database operations
   * @returns The shipment if found and authorized
   * @throws {NotFoundException} If the shipment is not found
   */
  async getShipmentById(shipmentId: number, queryRunner?: QueryRunner): Promise<Shipment> {
    try {
      this.logger.debug(`Getting shipment ${shipmentId} with queryRunner: ${queryRunner}`);
      const shipment = await this.shipmentService.getShipmentById(shipmentId, queryRunner);
      this.logger.debug(`Shipment ${shipmentId} retrieved successfully. ${JSON.stringify(shipment)}`);

      if (!shipment) {
        throw new NotFoundException(`Shipment ${shipmentId} not found.`);
      }

      return shipment;
    } catch (error) {
      this.logger.error(`Error retrieving shipment ${shipmentId}: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(`Failed to retrieve shipment data for ID: ${shipmentId}`);
    }
  }

  /**
   * Identifies a shipment based on the provided email content using the LLM agent and DB lookup tool.
   * @param emailContent - The content of the email (subject, text, history).
   * @param organizationId - The organization ID for context.
   * @returns An object containing found identifiers and a reason.
   */
  async identifyShipment(
    emailContent: MessageContent,
    organizationId: number
  ): Promise<IdentifyShipmentOutput> {
    return this.shipmentIdentifierService.identifyShipment(emailContent, organizationId);
  }

  /**
   * Delegates to EmailIntentAnalysisService to analyze email content.
   * @param emailContent - The full message content
   * @returns Validated intents extracted from the email
   */
  async analyzeEmailIntents(emailContent: MessageContent): Promise<ValidatedEmailIntents> {
    return this.emailIntentAnalysisService.analyzeEmailIntents(emailContent);
  }
}
