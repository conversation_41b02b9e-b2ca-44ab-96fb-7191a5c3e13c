import { Injectable, Logger } from "@nestjs/common";
import { CleanAgentContextService } from "../../clean-agent-context/clean-agent-context.service";
import { TemplateManagerService } from "nest-modules";
import { EmailContextOptions } from "../../clean-agent-context/clean-agent-context.types";

export interface TemplateRenderOptions {
  emailContext?: EmailContextOptions;
  intentType?: string;
  hasProcessedDocuments?: boolean;
  processedDocuments?: any[];
  cadDocument?: any;
  rnsProofData?: any;
  submissionResult?: any;
  backofficeAlerts?: any;
  updateResult?: any;
}

@Injectable()
export class UnifiedTemplateRendererService {
  private readonly logger = new Logger(UnifiedTemplateRendererService.name);

  constructor(
    private readonly cleanAgentContextService: CleanAgentContextService,
    private readonly templateManagerService: TemplateManagerService
  ) {}

  async renderTemplate(
    shipmentId: number,
    organizationId: number,
    request: any,
    options: TemplateRenderOptions = {}
  ): Promise<string> {
    const start = Date.now();

    try {
      const context = await this.cleanAgentContextService.getUnifiedTemplateContext(
        shipmentId,
        organizationId,
        request,
        options.emailContext
      );

      // Apply intent-specific processing
      const enhancedContext = this.applyIntentSpecificProcessing(context, options);

      const content = await this.templateManagerService.renderTemplate(
        "email-response-complete-template",
        enhancedContext
      );

      this.logger.log(`Rendered template for shipment ${shipmentId} with intent ${options.intentType || 'generic'} in ${Date.now() - start}ms`);
      return content;
    } catch (error) {
      this.logger.error(`Template render failed for shipment ${shipmentId}`, error);
      throw new Error(`Template rendering failed: ${error.message}`);
    }
  }

  /**
   * Apply intent-specific processing to enhance the template context
   * Now delegates to CleanAgentContextService for centralized message generation
   */
  private applyIntentSpecificProcessing(context: any, options: TemplateRenderOptions): any {
    const enhancedContext = { ...context };
    const intentType = options.intentType;

    this.logger.debug(`Applying intent-specific processing for intent: ${intentType}`);

    // Use CleanAgentContextService to generate intent-specific messages
    const intentMessages = this.cleanAgentContextService.generateAllIntentMessages(
      enhancedContext,
      intentType || 'GENERIC',
      {
        hasProcessedDocuments: options.hasProcessedDocuments,
        processedDocuments: options.processedDocuments,
        cadDocument: options.cadDocument,
        rnsProofData: options.rnsProofData,
        submissionResult: options.submissionResult,
        backofficeAlerts: options.backofficeAlerts,
        updateResult: options.updateResult
      }
    );

    // Merge the intent-specific messages into the context
    Object.assign(enhancedContext, intentMessages);

    // Apply any additional processing specific to this service
    this.applyAdditionalProcessing(enhancedContext, options);

    // Set template section flags (keeping this for backward compatibility)
    this.setTemplateSectionFlags(enhancedContext, intentType);

    return enhancedContext;
  }

  /**
   * Apply additional processing that's specific to the renderer service
   * This handles options that don't fit into the centralized message generation
   */
  private applyAdditionalProcessing(context: any, options: TemplateRenderOptions): void {
    // Add processed documents if provided
    if (options.processedDocuments && options.processedDocuments.length > 0) {
      context.PROCESSED_DOCUMENTS = options.processedDocuments;
    }

    // Add CAD document if provided
    if (options.cadDocument) {
      context.CAD_DOCUMENT = options.cadDocument;
    }

    // Add RNS proof data if provided
    if (options.rnsProofData) {
      context.RNS_PROOF_DATA = options.rnsProofData;
    }

    // Add submission result if provided
    if (options.submissionResult) {
      context.SUBMISSION_RESULT = options.submissionResult;
    }

    // Add update result if provided
    if (options.updateResult) {
      context.UPDATE_RESULT = options.updateResult;
    }
  }


  /**
   * Set template section flags based on intent type
   * Note: Most flag logic is now handled by CleanAgentContextService,
   * but keeping this for backward compatibility and additional flags
   */
  private setTemplateSectionFlags(context: any, intentType: string): void {
    // Ensure default flags are set if not already set by CleanAgentContextService
    if (context.SHOW_RNS_PROOF === undefined) context.SHOW_RNS_PROOF = false;
    if (context.SHOW_PROCESS_DOCUMENT_MESSAGE === undefined) context.SHOW_PROCESS_DOCUMENT_MESSAGE = false;
    if (context.SHOW_VALIDATION_ISSUES === undefined) context.SHOW_VALIDATION_ISSUES = false;
    if (context.SHOW_DETAILS === undefined) context.SHOW_DETAILS = false;
    if (context.SHOW_COMPLIANCE_ERRORS === undefined) context.SHOW_COMPLIANCE_ERRORS = false;

    // Override specific flags based on intent if needed
    switch (intentType) {
      case 'REQUEST_RNS_PROOF':
        if (context.RNS_PROOF_DATA) {
          context.SHOW_RNS_PROOF = true;
        }
        break;
    }

    this.logger.debug(`Template section flags set for intent ${intentType}: SHOW_DETAILS=${context.SHOW_DETAILS}, SHOW_VALIDATION_ISSUES=${context.SHOW_VALIDATION_ISSUES}`);
  }
}
