import { Job, Queue } from "bullmq";

import { RegisterQueueOptions } from "@nestjs/bullmq";
import { NestWorkerOptions } from "@nestjs/bullmq/dist/interfaces/worker-options.interface";
import { JobsOptions } from "bullmq";

export const DEFAULT_JOB_OPTIONS: JobsOptions = {
  attempts: 1,
  removeOnComplete: {
    age: 3600,
    count: 1000
  },
  removeOnFail: {
    age: 24 * 3600
  }
};

export const DEFAULT_WORKER_OPTIONS: NestWorkerOptions = {
  concurrency: 3
};

export enum SimaFilingQueueName {
  AUTO_SIMA_FILING = "auto-sima-filing"
}

export const SIMA_FILING_QUEUES: Array<RegisterQueueOptions> = [
  {
    name: SimaFilingQueueName.AUTO_SIMA_FILING,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  }
];

// Auto SIMA Filing Queue
export interface AutoSimaFilingJobData {
  /**
   * The event type that triggered the auto SIMA filing
   */
  sourceEventType: "create" | "update";
  /**
   * ID of the product to file SIMA filing for
   */
  productId: number;
}
export type AutoSimaFilingJob = Job<AutoSimaFilingJobData, null, string>;
export type AutoSimaFilingQueue = Queue<AutoSimaFilingJobData, null, string>;
