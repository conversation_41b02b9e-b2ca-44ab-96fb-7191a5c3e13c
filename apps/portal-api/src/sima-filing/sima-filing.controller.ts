import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards
} from "@nestjs/common";
import { ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses,
  CreateOrEditSimaFilingAndMatchingRuleResponseDto,
  CreateSimaFilingAndMatchingRuleDto,
  CreateSimaFilingDto,
  EditSimaFilingAndMatchingRuleDto,
  EditSimaFilingDto,
  GetSimaFilingsDto,
  GetSimaFilingsResponseDto,
  SimaFiling
} from "nest-modules";
import { SimaFilingService } from "./sima-filing.service";

@ApiTags("SIMA Filing API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("sima-filings")
export class SimaFilingController {
  constructor(private readonly simaFilingService: SimaFilingService) {}

  @ApiOperation({ summary: "Get SIMA Filings" })
  @ApiGetManyResponses({ type: GetSimaFilingsResponseDto })
  @Get()
  async getSimaFilings(@Query() getSimaFilingsDto: GetSimaFilingsDto) {
    return this.simaFilingService.getSimaFilings(getSimaFilingsDto);
  }

  @ApiOperation({ summary: "Get SIMA Filing" })
  @ApiParam({ name: "id", type: "integer", description: "SIMA Filing ID" })
  @ApiGetByIdResponses({ type: SimaFiling })
  @Get(":id")
  async getSimaFilingById(@Param("id", ParseIntPipe) id: number) {
    const simaFiling = await this.simaFilingService.getSimaFilingById(id);
    if (!simaFiling) throw new NotFoundException("SIMA Filing not found");
    return simaFiling;
  }

  @ApiOperation({ summary: "Create SIMA Filing" })
  @ApiCreateResponses({ type: SimaFiling })
  @Post()
  async createSimaFiling(@Body() createSimaFilingDto: CreateSimaFilingDto) {
    return this.simaFilingService.createSimaFiling(createSimaFilingDto);
  }

  @ApiOperation({ summary: "Edit SIMA Filing" })
  @ApiParam({ name: "id", type: "integer", description: "SIMA Filing ID" })
  @ApiEditResponses({ type: SimaFiling })
  @Put(":id")
  async editSimaFiling(@Param("id", ParseIntPipe) id: number, @Body() editSimaFilingDto: EditSimaFilingDto) {
    return this.simaFilingService.editSimaFiling(id, editSimaFilingDto);
  }

  @ApiOperation({ summary: "Delete SIMA Filing" })
  @ApiParam({ name: "id", type: "integer", description: "SIMA Filing ID" })
  @ApiDeleteResponses()
  @HttpCode(204)
  @Delete(":id")
  async deleteSimaFiling(@Param("id", ParseIntPipe) id: number) {
    await this.simaFilingService.deleteSimaFiling(id);
    return;
  }

  @ApiOperation({ summary: "Create SIMA Filing and Matching Rule" })
  @ApiCreateResponses({
    type: CreateOrEditSimaFilingAndMatchingRuleResponseDto
  })
  @Post("filing-and-rule")
  async createSimaFilingAndMatchingRule(
    @Body()
    createSimaFilingAndMatchingRuleDto: CreateSimaFilingAndMatchingRuleDto
  ) {
    return this.simaFilingService.createSimaFilingAndMatchingRule(createSimaFilingAndMatchingRuleDto);
  }

  @ApiOperation({ summary: "Edit SIMA Filing and Matching Rule" })
  @ApiParam({ name: "id", type: "integer", description: "SIMA Filing ID" })
  @ApiParam({
    name: "ruleId",
    type: "integer",
    description: "Matching Rule ID"
  })
  @ApiEditResponses({ type: CreateOrEditSimaFilingAndMatchingRuleResponseDto })
  @Put(":id/filing-and-rule/:ruleId")
  async editSimaFilingAndMatchingRule(
    @Param("id", ParseIntPipe) id: number,
    @Param("ruleId", ParseIntPipe) ruleId: number,
    @Body() editSimaFilingAndMatchingRuleDto: EditSimaFilingAndMatchingRuleDto
  ) {
    return this.simaFilingService.editSimaFilingAndMatchingRule(id, ruleId, editSimaFilingAndMatchingRuleDto);
  }
}
