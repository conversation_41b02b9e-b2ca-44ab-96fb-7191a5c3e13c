import { CreateSimaFilingDto, EditSimaFilingDto, User } from "nest-modules";

export class BaseSimaFilingEventDto {
  constructor(
    /**
     * ID of the SIMA Filing
     */
    public readonly filingId: number,
    /**
     * User who triggered the event.
     */
    public readonly user?: User | null
  ) {}
}

export class SimaFilingCreatedEventDto extends BaseSimaFilingEventDto {
  constructor(
    filingId: number,
    user?: User | null,
    /**
     * DTO used to create the SIMA filing.
     */
    public readonly createSimaFilingDto?: CreateSimaFilingDto | null
  ) {
    super(filingId, user);
  }
}

export class SimaFilingEditedEventDto extends BaseSimaFilingEventDto {
  constructor(
    filingId: number,
    user?: User | null,
    /**
     * DTO used to edit the SIMA filing.
     */
    public readonly editSimaFilingDto?: EditSimaFilingDto | null
  ) {
    super(filingId, user);
  }
}

export class SimaFilingDeletedEventDto extends BaseSimaFilingEventDto {
  constructor(filingId: number, user?: User | null) {
    super(filingId, user);
  }
}
