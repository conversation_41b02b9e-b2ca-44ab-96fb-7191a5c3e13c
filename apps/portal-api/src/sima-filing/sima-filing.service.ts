import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import {
  AuthenticatedRequest,
  CanadaAntiDumpingService,
  CanadaSimaCodeService,
  CreateOrEditSimaFilingAndMatchingRuleResponseDto,
  CreateSimaFilingAndMatchingRuleDto,
  CreateSimaFilingDto,
  EditSimaFilingAndMatchingRuleDto,
  EditSimaFilingDto,
  FIND_SIMA_FILING_RELATIONS,
  getFindOptions,
  GetSimaFilingsDto,
  GetSimaFilingsResponseDto,
  MatchingConditionService,
  MatchingRuleService,
  MatchingRuleSourceDatabaseTable,
  SIMA_FILING_ENUM_KEYS,
  SIMA_FILING_REQUIRED_KEYS,
  SimaFiling,
  SimaFilingColumn,
  SimaSubjectCode,
  TransactionalEventEmitterService,
  UserPermission
} from "nest-modules";
import { DataSource, IsNull, Not, QueryRunner, Repository } from "typeorm";
import { SimaFilingEvent } from "./types/event.types";
import {
  SimaFilingCreatedEventDto,
  SimaFilingDeletedEventDto,
  SimaFilingEditedEventDto
} from "./dto/event.dto";

@Injectable({ scope: Scope.REQUEST })
export class SimaFilingService {
  constructor(
    @InjectRepository(SimaFiling)
    private readonly simaFilingRepository: Repository<SimaFiling>,
    @Inject(CanadaAntiDumpingService)
    private readonly canadaAntiDumpingService: CanadaAntiDumpingService,
    @Inject(CanadaSimaCodeService)
    private readonly canadaSimaCodeService: CanadaSimaCodeService,
    @Inject(MatchingRuleService)
    private readonly matchingRuleService: MatchingRuleService,
    @Inject(MatchingConditionService)
    private readonly matchingConditionService: MatchingConditionService,
    @Inject(forwardRef(() => TransactionalEventEmitterService))
    private readonly transactionalEventEmitterService: TransactionalEventEmitterService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}

  async getSimaFilings(getSimaFilingsDto: GetSimaFilingsDto): Promise<GetSimaFilingsResponseDto> {
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN) {
      getSimaFilingsDto.organizationId = this.request?.user?.organization?.id || -1;
    }

    const { where, order, skip, take } = getFindOptions(
      getSimaFilingsDto,
      SIMA_FILING_ENUM_KEYS,
      SIMA_FILING_REQUIRED_KEYS,
      SimaFilingColumn.id
    );

    const [simaFilings, total] = await this.simaFilingRepository.findAndCount({
      where,
      order,
      relations: FIND_SIMA_FILING_RELATIONS,
      skip,
      take
    });

    return {
      data: simaFilings,
      total,
      skip,
      limit: take
    };
  }

  async getSimaFilingById(filingId: number, queryRunner?: QueryRunner) {
    return (queryRunner ? queryRunner.manager.getRepository(SimaFiling) : this.simaFilingRepository).findOne({
      where: {
        id: filingId,
        organization: {
          id:
            this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN
              ? this.request?.user?.organization?.id || -1
              : Not(IsNull())
        }
      },
      relations: FIND_SIMA_FILING_RELATIONS
    });
  }

  async createSimaFiling(createSimaFilingDto: CreateSimaFilingDto, queryRunner?: QueryRunner) {
    if (!this.request?.user?.organization) throw new NotFoundException("Organization not found");
    const { subjectCode, ...createProps } = createSimaFilingDto;

    let newFiling = new SimaFiling();
    newFiling.subjectCode = subjectCode;
    newFiling.organization = this.request.user.organization;
    newFiling.createdBy = this.request.user;
    newFiling.lastEditedBy = this.request.user;

    for (const [key, value] of Object.entries(createProps)) {
      if (value === undefined) continue;
      const propertyName = key.replace(/Id$/, "");
      switch (propertyName) {
        case "measureInForce":
          if (typeof value === "number") {
            newFiling[propertyName] = await this.canadaAntiDumpingService.getCanadaAntiDumpingById(
              value,
              queryRunner
            );
            if (!newFiling[propertyName]) throw new NotFoundException("Measure in force not found");
          } else newFiling[propertyName] = null;
          break;
        case "simaCode":
          if (newFiling.subjectCode !== SimaSubjectCode.NON_SUBJECT && typeof value === "number") {
            newFiling[propertyName] = await this.canadaSimaCodeService.getCanadaSimaCodeById(
              value,
              queryRunner
            );
            if (!newFiling[propertyName]) throw new NotFoundException("SIMA code not found");
          } else newFiling[propertyName] = null;
          break;
        default:
          newFiling[propertyName] = newFiling.subjectCode !== SimaSubjectCode.NON_SUBJECT ? value : null;
          break;
      }
    }
    if (newFiling.subjectCode === SimaSubjectCode.NON_SUBJECT) {
      newFiling.security = false;
      newFiling.incoterms = null;
      newFiling.simaCode = null;
    } else if (!newFiling.simaCode)
      throw new BadRequestException("SIMA code is required if subjected or undertaking");

    const missingRequiredKeys = SIMA_FILING_REQUIRED_KEYS.filter((key) =>
      [null, undefined].includes(newFiling[key])
    );
    if (missingRequiredKeys.length > 0)
      throw new BadRequestException(`The following fields are required: ${missingRequiredKeys.join(", ")}`);

    newFiling = await (
      queryRunner ? queryRunner.manager.getRepository(SimaFiling) : this.simaFilingRepository
    ).save(newFiling);

    await this.transactionalEventEmitterService.enqueueEvent(
      SimaFilingEvent.SIMA_FILING_CREATED,
      queryRunner,
      new SimaFilingCreatedEventDto(newFiling.id, this.request?.user, createSimaFilingDto)
    );

    return this.getSimaFilingById(newFiling.id, queryRunner);
  }

  async editSimaFiling(
    simaFilingId: number,
    editSimaFilingDto: EditSimaFilingDto,
    queryRunner?: QueryRunner
  ) {
    const filing = await this.getSimaFilingById(simaFilingId, queryRunner);
    if (!filing) throw new NotFoundException("SIMA Filing not found");
    const { subjectCode, ...editProps } = editSimaFilingDto;
    if (subjectCode) filing.subjectCode = subjectCode;

    filing.lastEditedBy = this.request?.user || null;
    for (const [key, value] of Object.entries(editProps)) {
      if (value === undefined) continue;
      const propertyName = key.replace(/Id$/, "");
      switch (propertyName) {
        case "measureInForce":
          if (typeof value === "number") {
            filing[propertyName] = await this.canadaAntiDumpingService.getCanadaAntiDumpingById(
              value,
              queryRunner
            );
            if (!filing[propertyName]) throw new NotFoundException("Measure in force not found");
          } else filing[propertyName] = null;
          break;
        case "simaCode":
          if (filing.subjectCode !== SimaSubjectCode.NON_SUBJECT && typeof value === "number") {
            filing[propertyName] = await this.canadaSimaCodeService.getCanadaSimaCodeById(value, queryRunner);
            if (!filing[propertyName]) throw new NotFoundException("SIMA code not found");
          } else filing[propertyName] = null;
          break;
        default:
          filing[propertyName] = filing.subjectCode !== SimaSubjectCode.NON_SUBJECT ? value : null;
          break;
      }
    }
    if (filing.subjectCode === SimaSubjectCode.NON_SUBJECT) {
      filing.security = false;
      filing.incoterms = null;
      filing.simaCode = null;
    } else if (!filing.simaCode)
      throw new BadRequestException("SIMA code is required if subjected or undertaking");

    const missingRequiredKeys = SIMA_FILING_REQUIRED_KEYS.filter((key) =>
      [null, undefined].includes(filing[key])
    );
    if (missingRequiredKeys.length > 0)
      throw new BadRequestException(`The following fields are required: ${missingRequiredKeys.join(", ")}`);

    await (queryRunner ? queryRunner.manager.getRepository(SimaFiling) : this.simaFilingRepository).save(
      filing
    );

    await this.transactionalEventEmitterService.enqueueEvent(
      SimaFilingEvent.SIMA_FILING_EDITED,
      queryRunner,
      new SimaFilingEditedEventDto(filing.id, this.request?.user, editSimaFilingDto)
    );

    return this.getSimaFilingById(filing.id, queryRunner);
  }

  async deleteSimaFiling(simaFilingId: number) {
    const filing = await this.getSimaFilingById(simaFilingId);
    if (!filing) {
      throw new NotFoundException("SIMA Filing not found");
    }

    await this.simaFilingRepository.delete({ id: filing.id });

    await this.transactionalEventEmitterService.enqueueEvent(
      SimaFilingEvent.SIMA_FILING_DELETED,
      null,
      new SimaFilingDeletedEventDto(filing.id, this.request?.user)
    );

    return;
  }

  async createSimaFilingAndMatchingRule(
    createSimaFilingAndMatchingRuleDto: CreateSimaFilingAndMatchingRuleDto,
    queryRunner?: QueryRunner
  ): Promise<CreateOrEditSimaFilingAndMatchingRuleResponseDto> {
    const tQueryRunner = queryRunner ?? this.dataSource.createQueryRunner();
    if (!queryRunner) {
      await tQueryRunner.connect();
      await tQueryRunner.startTransaction();
    }

    try {
      const { simaFiling, matchingRule, matchingConditions } = createSimaFilingAndMatchingRuleDto;
      const newFiling = await this.createSimaFiling(simaFiling, tQueryRunner);

      const newRule = await this.matchingRuleService.createMatchingRule(
        {
          ...matchingRule,
          sourceTable: MatchingRuleSourceDatabaseTable.SIMA_FILING,
          sourceId: newFiling.id
        },
        tQueryRunner
      );

      const { matchingConditions: newConditions } =
        await this.matchingConditionService.batchUpdateMatchingConditions(
          newRule.id,
          {
            create: matchingConditions
          },
          tQueryRunner
        );

      if (!queryRunner) await tQueryRunner.commitTransaction();
      return {
        simaFiling: newFiling,
        matchingRule: await this.matchingRuleService.getMatchingRuleById(newRule.id, queryRunner)
      };
    } catch (error) {
      if (!queryRunner && tQueryRunner.isTransactionActive) await tQueryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner) await tQueryRunner.release();
    }
  }

  async editSimaFilingAndMatchingRule(
    simaFilingId: number,
    ruleId: number,
    editSimaFilingAndMatchingRuleDto: EditSimaFilingAndMatchingRuleDto
  ): Promise<CreateOrEditSimaFilingAndMatchingRuleResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const {
        simaFiling: editSimaFilingDto,
        matchingRule: editMatchingRuleWithoutSourceDto,
        matchingConditions: batchUpdateMatchingConditionsDto
      } = editSimaFilingAndMatchingRuleDto;
      let filing = await this.getSimaFilingById(simaFilingId, queryRunner);
      let rule = await this.matchingRuleService.getMatchingRuleById(ruleId, queryRunner);
      if (!filing) throw new NotFoundException("SIMA Filing not found");
      if (!rule) throw new NotFoundException("Matching rule not found");
      if (rule.sourceTable !== MatchingRuleSourceDatabaseTable.SIMA_FILING || rule.sourceId !== filing.id)
        throw new BadRequestException("Matching rule is not associated with the SIMA Filing");

      if (editSimaFilingDto) filing = await this.editSimaFiling(simaFilingId, editSimaFilingDto, queryRunner);
      if (editMatchingRuleWithoutSourceDto)
        rule = await this.matchingRuleService.editMatchingRule(
          ruleId,
          editMatchingRuleWithoutSourceDto,
          queryRunner
        );
      if (batchUpdateMatchingConditionsDto) {
        const { matchingConditions: updatedConditions } =
          await this.matchingConditionService.batchUpdateMatchingConditions(
            ruleId,
            batchUpdateMatchingConditionsDto,
            queryRunner
          );
      }

      await queryRunner.commitTransaction();

      return {
        simaFiling: await this.getSimaFilingById(filing.id),
        matchingRule: await this.matchingRuleService.getMatchingRuleById(rule.id)
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
