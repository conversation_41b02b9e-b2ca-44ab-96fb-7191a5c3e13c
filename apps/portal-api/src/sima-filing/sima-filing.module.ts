import { BullModule } from "@nestjs/bullmq";
import { DynamicModule, forwardRef, Logger, Module, ModuleMetadata, Provider } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { SimaFiling, TransactionalEventEmitterModule } from "nest-modules";
import { AutoSimaFilingService } from "./auto-sima-filing.service";
import { ProductEventListener } from "./listeners";
import { AutoSimaFilingProcessor } from "./processors";
import { SimaFilingController } from "./sima-filing.controller";
import { SimaFilingService } from "./sima-filing.service";
import { SIMA_FILING_QUEUES } from "./types";

const moduleProps: ModuleMetadata = {
  imports: [
    TypeOrmModule.forFeature([SimaFiling]),
    BullModule.registerQueue(...SIMA_FILING_QUEUES),
    forwardRef(() => TransactionalEventEmitterModule)
  ],
  providers: [SimaFilingService, AutoSimaFilingService],
  controllers: [SimaFilingController],
  exports: [SimaFilingService, AutoSimaFilingService]
};

@Module(moduleProps)
export class SimaFilingModule {
  static forRoot(): DynamicModule {
    const providers: Array<Provider> = [...moduleProps.providers];
    if (process.env.FEATURE) {
      Logger.warn("Auto SIMA filing processor is disabled when feature is set", "SimaFilingModule");
    } else {
      Logger.log("Auto SIMA filing processor is enabled when feature is not set", "SimaFilingModule");
      providers.push(AutoSimaFilingProcessor, ProductEventListener);
    }

    return {
      module: SimaFilingModule,
      ...moduleProps,
      providers
    };
  }
}
