import { ProductCreatedEventPayload, ProductUpdatedEventPayload } from "@/product/events/product.event";
import { Injectable } from "@nestjs/common";
import { AutoSimaFilingService } from "../auto-sima-filing.service";

@Injectable()
export class ProductEventListener {
  constructor(private readonly autoSimaFilingService: AutoSimaFilingService) {}

  // @OnEvent(ProductEvent.PRODUCT_CREATED)
  async handleProductCreated(product: ProductCreatedEventPayload) {
    await this.autoSimaFilingService.createProductSimaFiling(product);
  }

  // @OnEvent(ProductEvent.PRODUCT_UPDATED)
  async handleProductUpdated(product: ProductUpdatedEventPayload) {
    await this.autoSimaFilingService.updateProductSimaFiling(product);
  }
}
