/**
 * Record merging utilities for entity resolution
 */

/**
 * Select the best non-empty value from multiple entities
 * @param {Array} entities - Array of entities
 * @param {String} field - The field name to extract
 * @returns {Object} Object containing the best value and its source
 */
const getBestValue = (allValues, field) => {
  // Get all non-empty values with their sources
  const valuesWithSources = allValues;

  if (valuesWithSources.length === 0) return { value: null, source: null };

  // If only one value, return it with its source
  if (valuesWithSources.length === 1) return valuesWithSources[0];

  // If multiple values, select the most common one
  const valueCounts = {};
  valuesWithSources.forEach((item) => {
    const val = item.value;
    valueCounts[val] = valueCounts[val] || { count: 0, source: item.source };
    valueCounts[val].count += 1;
  });

  // Find the most frequent value
  let mostFrequentValue = valuesWithSources[0].value;
  let maxCount = valueCounts[mostFrequentValue].count;

  Object.keys(valueCounts).forEach((val) => {
    if (valueCounts[val].count > maxCount) {
      maxCount = valueCounts[val].count;
      mostFrequentValue = val;
    }
  });

  // Find the first occurrence of the most frequent value to get its source
  const sourceInfo = valuesWithSources.find((item) => item.value === mostFrequentValue).source;

  return { value: mostFrequentValue, source: sourceInfo };
};

const getAllValues = (entities, field) => {
  return entities
    .filter(
      (entity) => entity.data[field] !== null && entity.data[field] !== undefined && entity.data[field] !== ""
    )
    .map((entity) => ({
      value: entity.data[field],
      documentId: entity.source.documentId,
      type: entity.type,
      name: entity.source.name
    }));
};

/**
 * Merge a cluster of entities into a single canonical record
 * @param {Array} cluster - Array of entity records to merge
 * @returns {Object} A merged canonical record
 */
export const mergeEntityRecords = (cluster) => {
  if (!cluster || cluster.length === 0) {
    throw new Error("Cannot merge empty cluster");
  }

  // Extract all possible types from the cluster
  const allTypes = [...new Set(cluster.map((entity) => entity.type))];

  // Get the best values and their sources
  const nameData = getAllValues(cluster, "name");
  const streetData = getAllValues(cluster, "street");
  const cityData = getAllValues(cluster, "city");
  const stateData = getAllValues(cluster, "state");
  const postalCodeData = getAllValues(cluster, "postalCode");
  const countryData = getAllValues(cluster, "country");
  const emailData = getAllValues(cluster, "email");
  const phoneNumberData = getAllValues(cluster, "phoneNumber");

  // const best values
  const name = getBestValue(nameData, "name");
  const street = getBestValue(streetData, "street");
  const city = getBestValue(cityData, "city");
  const state = getBestValue(stateData, "state");
  const postalCode = getBestValue(postalCodeData, "postalCode");
  const country = getBestValue(countryData, "country");
  const email = getBestValue(emailData, "email");
  const phoneNumber = getBestValue(phoneNumberData, "phoneNumber");

  const source = {
    name: nameData,
    street: streetData,
    city: cityData,
    state: stateData,
    postalCode: postalCodeData,
    country: countryData,
    email: emailData,
    phoneNumber: phoneNumberData
  };

  // Create the merged data object
  const mergedData = {
    name: name.value,
    street: street.value,
    city: city.value,
    state: state.value,
    postalCode: postalCode.value,
    country: country.value,
    email: email.value,
    phoneNumber: phoneNumber.value
  };

  // Remove null values from both objects
  Object.keys(mergedData).forEach((key) => {
    if (mergedData[key] === null) {
      delete mergedData[key];
    }
  });

  // Create the merged record
  return {
    data: mergedData,
    types: allTypes,
    source: source,
    cluster: cluster
  };
};

/**
 * Process all clusters and generate merged records
 * @param {Array} clusters - Array of entity clusters
 * @returns {Array} Array of merged canonical records
 */
export const generateMergedRecords = (clusters) => {
  return clusters.map((cluster) => mergeEntityRecords(cluster));
};
