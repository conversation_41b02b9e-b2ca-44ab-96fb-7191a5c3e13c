export const ROLE_MATCH_MATRIX = [
  // Commercial Invoice
  ["shipper", "vendor"], // shipper can be vendor
  ["shipper", "purchaser"], // shipper can be purchaser
  ["purchaser", "consignee", "shipTo"], // purchaser can be consignee or shipTo

  // Certificate of Origin
  ["exporter", "vendor", "shipper"], // exporter can be vendor
  ["importer", "consignee", "purchaser"] // importer can be consignee or purchaser
];

export const MATCHING_THRESHOLD = 0.85;

// String preprocessing options
export const STRING_PREPROCESSING = {
  // Characters to remove during normalization
  REMOVE_CHARS: /[.,]+/g,
  NORMALIZE_SPACES: /\s+/g
};

// Utility function to normalize a string for comparison
export const normalizeString = (str: string) => {
  if (!str) return "";
  return str
    .toLowerCase()
    .replace(STRING_PREPROCESSING.REMOVE_CHARS, "")
    .replace(STRING_PREPROCESSING.NORMALIZE_SPACES, " ")
    .trim();
};

export const areRolesCompatible = (role1: string, role2: string) => {
  if (role1 === role2) return true;
  return ROLE_MATCH_MATRIX.some((roles) => roles.includes(role1) && roles.includes(role2));
};
