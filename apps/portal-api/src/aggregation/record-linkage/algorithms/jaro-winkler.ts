/**
 * Calculate Jaro-Winkler similarity between two strings
 * <PERSON><PERSON><PERSON><PERSON> is particularly good for short strings like names
 */

/**
 * Calculate Jaro similarity between two strings
 * @param {string} a First string
 * @param {string} b Second string
 * @returns {number} Similarity score between 0 and 1
 */
const jaro = (a: string, b: string): number => {
  if (a.length === 0 && b.length === 0) return 1;
  if (a.length === 0 || b.length === 0) return 0;

  // Matching window will be half the length of the longer string
  const matchDistance = Math.floor(Math.max(a.length, b.length) / 2) - 1;

  // Record matches
  const aMatches = new Array(a.length).fill(false);
  const bMatches = new Array(b.length).fill(false);

  let matches = 0;
  for (let i = 0; i < a.length; i++) {
    const start = Math.max(0, i - matchDistance);
    const end = Math.min(i + matchDistance + 1, b.length);

    for (let j = start; j < end; j++) {
      if (!bMatches[j] && a[i] === b[j]) {
        aMatches[i] = true;
        bMatches[j] = true;
        matches++;
        break;
      }
    }
  }

  if (matches === 0) return 0;

  // Count transpositions
  let transpositions = 0;
  let k = 0;

  for (let i = 0; i < a.length; i++) {
    if (aMatches[i]) {
      // Find the next match in b
      while (!bMatches[k]) k++;

      if (a[i] !== b[k]) transpositions++;
      k++;
    }
  }

  // Jaro similarity
  return (matches / a.length + matches / b.length + (matches - transpositions / 2) / matches) / 3;
};

/**
 * Calculate Jaro-Winkler similarity between two strings
 * @param {string} str1 First string
 * @param {string} str2 Second string
 * @returns {number} Similarity score between 0 and 1
 */
export const jaroWinkler = (str1: string, str2: string): number => {
  if (!str1 || !str2) return 0;

  // Convert to lowercase for case-insensitive comparison
  const s1 = str1.toLowerCase();
  const s2 = str2.toLowerCase();

  // Calculate Jaro similarity
  const jaroScore = jaro(s1, s2);

  // Calculate Jaro-Winkler
  // Prefix scale of 0.1 is standard for Jaro-Winkler
  const prefixScale = 0.1;

  // Find common prefix length (up to 4 characters)
  let prefixLength = 0;
  const maxPrefixLength = 4;

  for (let i = 0; i < Math.min(s1.length, s2.length, maxPrefixLength); i++) {
    if (s1[i] === s2[i]) {
      prefixLength++;
    } else {
      break;
    }
  }

  // Calculate Jaro-Winkler similarity
  return jaroScore + prefixLength * prefixScale * (1 - jaroScore);
};
