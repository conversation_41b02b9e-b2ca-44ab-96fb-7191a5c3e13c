import { generateComparisonVector } from "./comparison-vector-generator";
import { MATCHING_THRESHOLD } from "./config";
import { generateMergedRecords } from "./record-merger";

/**
 * Generate a unique entity ID
 */
const generateEntityId = (entity) => {
  return `d:${entity.source.documentId}:${entity.type}`;
};

/**
 * Generate comparison vectors for all entity pairs
 */
const generateAllComparisonVectors = (entities) => {
  const vectors = [];
  const entityMap = {};

  // Create a map of entities for easy lookup
  entities.forEach((entity) => {
    const id = generateEntityId(entity);
    entityMap[id] = entity;
  });

  // Generate vectors for all entity pairs
  for (let i = 0; i < entities.length; i++) {
    for (let j = i + 1; j < entities.length; j++) {
      const vector = generateComparisonVector(entities[i], entities[j]);

      // Only include vectors with compatible roles
      if (vector.rolesCompatible) {
        vectors.push(vector);
      }
    }
  }

  return { vectors, entityMap };
};

/**
 * Find the cluster containing a specific entity ID
 */
const findClusterIndex = (clusters, entityId) => {
  for (let i = 0; i < clusters.length; i++) {
    if (clusters[i].some((e) => generateEntityId(e) === entityId)) {
      return i;
    }
  }
  return -1;
};

/**
 * Cluster entities based on comparison vectors
 */
const clusterEntities = (entities, vectorData) => {
  const { vectors, entityMap } = vectorData;

  // Initialize each entity as its own cluster
  const clusters = entities.map((entity) => [entity]);

  // Sort vectors by overall similarity (highest first)
  const sortedVectors = [...vectors].sort((a, b) => b.overall - a.overall);

  console.log(`Processing ${sortedVectors.length} comparison vectors...`);

  // Helper function to evaluate a scoring rule
  const evaluateRule = (vector) => {
    if (vector.overall > MATCHING_THRESHOLD) {
      return true;
    }

    return false;
  };

  // Process vectors in order of similarity
  for (const vector of sortedVectors) {
    // Find the entities
    const entity1 = entityMap[vector.entity1];
    const entity2 = entityMap[vector.entity2];

    if (!entity1 || !entity2) {
      console.log(`Warning: Entity not found for IDs ${vector.entity1}, ${vector.entity2}`);
      continue;
    }

    // Find clusters containing these entities
    const cluster1Index = findClusterIndex(clusters, vector.entity1);
    const cluster2Index = findClusterIndex(clusters, vector.entity2);

    if (cluster1Index === -1 || cluster2Index === -1) {
      console.log(`Warning: Cluster not found for entities ${vector.entity1}, ${vector.entity2}`);
      continue;
    }

    // If entities are already in the same cluster, skip
    if (cluster1Index === cluster2Index) {
      continue;
    }

    // Determine if these clusters should be merged based on scoring rules
    const shouldMerge = evaluateRule(vector);

    if (shouldMerge) {
      console.log(`Merging clusters for ${entity1.data.name} and ${entity2.data.name})`);
      console.log(`  Types: ${entity1.type} - ${entity2.type}`);
      console.log(`  Vector: name=${vector.name}, address=${vector.address}, overall=${vector.overall}`);

      // Always merge the higher index into the lower index to avoid index errors
      const [sourceIdx, targetIdx] =
        cluster1Index > cluster2Index ? [cluster1Index, cluster2Index] : [cluster2Index, cluster1Index];

      clusters[targetIdx] = [...clusters[targetIdx], ...clusters[sourceIdx]];
      clusters.splice(sourceIdx, 1);
    }
  }

  return clusters;
};

/**
 * Output the final resolution results
 */
const displayResults = (clusters, mergedRecords) => {
  console.log("\nENTITY RESOLUTION RESULTS");
  console.log("========================");
  console.log(`Found ${clusters.length} distinct entities:\n`);

  mergedRecords.forEach((record, i) => {
    console.log(`\nEntity Group ${i + 1}: ${record.data.name}`);
    console.log("-".repeat(40));
    console.log(`Types: ${record.types.join(", ")}`);

    // console.table(
    //   Object.entries(record.data).filter(([key, value]) => value !== null).map(([key, value]) => ({
    //     field: key,
    //     value: value,
    //     source: `${record.fieldSources[key]?.documentId}:${record.fieldSources[key]?.sourceName}:${record.fieldSources[key]?.type}`,
    //   }))
    // )

    // console.log(`\nOriginal Records:`);
    // record.originalRecords.forEach(originalRecord => {
    //   console.log(`  • Doc ID: ${originalRecord.documentId}, Type: ${originalRecord.type}, Source: ${originalRecord.sourceName}`);
    // });
  });
};

/**
 * Main execution
 */
export const resolveEntities = (entities) => {
  // Generate comparison vectors
  console.log("Generating comparison vectors...");
  const vectorData = generateAllComparisonVectors(entities);

  // Cluster entities
  console.log("Clustering entities...");
  const clusters = clusterEntities(entities, vectorData);

  // Merge records
  console.log("Merging records...");
  const mergedRecords = generateMergedRecords(clusters);

  // Display results in a readable format
  displayResults(clusters, mergedRecords);

  console.log(`\nEntity resolution complete. Found ${clusters.length} distinct entities.`);
  return mergedRecords;
};
