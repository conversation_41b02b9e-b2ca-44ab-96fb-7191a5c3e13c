import { jaro<PERSON><PERSON><PERSON> } from "./algorithms/jaro-winkler";
import { areRolesCompatible, normalizeString } from "./config";

/**
 * Generate a comprehensive comparison vector between two entities
 * with individual field comparisons
 */
export const generateComparisonVector = (entity1, entity2) => {
  // Basic entity information
  const id1 = `d:${entity1.source.documentId}:${entity1.type}`;
  const id2 = `d:${entity2.source.documentId}:${entity2.type}`;
  const type1 = entity1.type;
  const type2 = entity2.type;

  // Check if roles are compatible
  const rolesCompatible = areRolesCompatible(type1, type2);

  // If roles are incompatible, return a vector with zeros
  if (!rolesCompatible) {
    return {
      entity1: id1,
      entity2: id2,
      type1: type1,
      type2: type2,
      rolesCompatible: false,
      name: 0,
      street: 0,
      city: 0,
      state: 0,
      postalCode: 0,
      country: 0,
      email: 0,
      phoneNumber: 0,
      overall: 0
    };
  }

  // Individual field comparisons

  // 1. Name comparison
  const name1 = entity1.data.name || "";
  const name2 = entity2.data.name || "";

  // Calculate name similarity with different methods
  let nameSimilarity = 0;

  const normalizedName1 = normalizeString(name1);
  const normalizedName2 = normalizeString(name2);

  if (normalizedName1 === normalizedName2) {
    // Exact match
    nameSimilarity = 1;
  } else if (name1 === "" || name2 === "") {
    // one of the names is empty
    nameSimilarity = 1;
  } else if (normalizedName1.includes(normalizedName2) || normalizedName2.includes(normalizedName1)) {
    // one of the names is a substring of the other
    nameSimilarity = Math.max(0.8, jaroWinkler(normalizedName1, normalizedName2));
  } else {
    // Jaro-Winkler similarity
    nameSimilarity = jaroWinkler(normalizedName1, normalizedName2);
  }

  // 2. Street comparison
  const street1 = entity1.data.street || "";
  const street2 = entity2.data.street || "";
  const streetSimilarity =
    street1 === street2 || street1 == "" || street2 == ""
      ? 1
      : jaroWinkler(normalizeString(street1), normalizeString(street2));

  // 3. City comparison
  const city1 = entity1.data.city || "";
  const city2 = entity2.data.city || "";
  const citySimilarity =
    city1 === city2 || city1 == "" || city2 == ""
      ? 1
      : jaroWinkler(normalizeString(city1), normalizeString(city2));

  // 4. State comparison
  const state1 = entity1.data.state || "";
  const state2 = entity2.data.state || "";
  const stateSimilarity =
    state1 === state2
      ? 1
      : state1 == "" || state2 == ""
        ? 0.98
        : state1.includes(state2) || state2.includes(state1)
          ? 0.8 // TODO: this might not be accurate
          : 0;

  // 5. Postal code comparison
  const postal1 = entity1.data.postalCode?.replace(/\s/g, "")?.toUpperCase() || "";
  const postal2 = entity2.data.postalCode?.replace(/\s/g, "")?.toUpperCase() || "";

  const postalSimilarity =
    normalizeString(postal1) === normalizeString(postal2) || postal1 == "" || postal2 == "" ? 1 : 0;

  // 6. Country comparison
  const country1 = normalizeString(entity1.data.country || "");
  const country2 = normalizeString(entity2.data.country || "");
  const countrySimilarity = country1 === country2 || country1 == "" || country2 == "" ? 1 : 0;

  // 7. Email comparison
  const email1 = normalizeString(entity1.data.email || "");
  const email2 = normalizeString(entity2.data.email || "");
  const emailSimilarity = email1 === email2 || email1 == "" || email2 == "" ? 1 : 0;

  // 8. Phone number comparison
  const phone1 = normalizeString(entity1.data.phoneNumber || "");
  const phone2 = normalizeString(entity2.data.phoneNumber || "");
  const phoneSimilarity = phone1 === phone2 || phone1 == "" || phone2 == "" ? 1 : 0;

  // 9. Calculate composite address similarity
  const addressSimilarity =
    streetSimilarity *
    // this is to make sure that country, city, state all matches, any difference will significantly decrease the similarity
    (countrySimilarity * citySimilarity * stateSimilarity * 0.6 + postalSimilarity * countrySimilarity * 0.4);

  // Overall similarity score with weights
  const overallSimilarity =
    nameSimilarity * 0.35 +
    addressSimilarity * 0.55 +
    // email phone
    emailSimilarity * 0.05 +
    phoneSimilarity * 0.05;

  // Format numeric values to 2 decimal places
  const formatNum = (num) => Number(num.toFixed(7));

  // Return the complete comparison vector with all fields
  return {
    entity1: id1,
    entity2: id2,
    type1: type1,
    type2: type2,
    rolesCompatible: rolesCompatible,
    name: formatNum(nameSimilarity),
    street: formatNum(streetSimilarity),
    city: formatNum(citySimilarity),
    state: formatNum(stateSimilarity),
    postalCode: formatNum(postalSimilarity),
    country: formatNum(countrySimilarity),
    email: formatNum(emailSimilarity),
    phoneNumber: formatNum(phoneSimilarity),
    address: formatNum(addressSimilarity),
    overall: formatNum(overallSimilarity)
  };
};
