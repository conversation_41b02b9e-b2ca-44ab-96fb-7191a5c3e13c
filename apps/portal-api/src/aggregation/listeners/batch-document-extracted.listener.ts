import { DocumentService } from "@/document/services/document.service";
import { FileBatchService } from "@/document/services/file-batch.service";
import { Inject, Injectable, Logger } from "@nestjs/common";
import { EventEmitter2, OnEvent } from "@nestjs/event-emitter";
import { DocumentStatus, FileBatchCreator, FileBatchStatus } from "nest-modules";
import { ClsService } from "nestjs-cls";
import { DataSource } from "typeorm";
import {
  BatchAggregationStartedEvent,
  BatchCheckingFailedEvent,
  BatchDocumentsExtractedEvent,
  BatchReadyForAggregationEvent,
  FileBatchEvent
} from "../../document";
import { AggregationService } from "../aggregation.service";
import { ShipmentAggregator } from "../aggregators/shipment.aggregator";
import { TradePartnerAggregator } from "../aggregators/trade-partner.aggregator";
import {
  BatchAggregationEvents,
  ShipmentCreatedFromBatchEvent,
  ShipmentCreationFromBatchFailedEvent
} from "../events";
import { ShipmentFieldFallbackService } from "../field-fallback/shipment-field-fallback.service";
import { DocumentToShipmentTransformer } from "../transformers/document-to-shipment.transformers";

@Injectable()
export class BatchDocumentExtractedListener {
  private readonly logger = new Logger(BatchDocumentExtractedListener.name);

  constructor(
    @Inject(FileBatchService)
    private readonly fileBatchService: FileBatchService,
    @Inject(AggregationService)
    private readonly aggregationService: AggregationService,
    @Inject(EventEmitter2)
    private readonly eventEmitter: EventEmitter2,
    private readonly cls: ClsService,
    private readonly documentService: DocumentService,
    @Inject(DocumentToShipmentTransformer)
    private readonly documentToShipmentTransformer: DocumentToShipmentTransformer,
    @Inject(DataSource)
    private readonly dataSource: DataSource,
    @Inject(ShipmentAggregator)
    private readonly shipmentAggregator: ShipmentAggregator,
    @Inject(TradePartnerAggregator)
    private readonly tradePartnerAggregator: TradePartnerAggregator,
    @Inject(ShipmentFieldFallbackService)
    private readonly shipmentFieldFallbackService: ShipmentFieldFallbackService
  ) {}

  private async createOrUpdateShipmentAndTradePartners(event: BatchReadyForAggregationEvent) {
    const documents = await this.documentService.getAllDocumentByIds(event.documentIds);

    let batchShipmentId = await this.fileBatchService.getBatchShipmentId(event.batchId);

    const shipment = await this.shipmentAggregator.aggregate(
      documents,
      event.organizationId,
      batchShipmentId,
      event.batchId
    );

    // if !shipmentId, create a new shipment
    if (!batchShipmentId) {
      try {
        await this.fileBatchService.setBatchShipmentId(event.batchId, shipment.id);
        batchShipmentId = shipment.id;

        this.eventEmitter.emit(
          BatchAggregationEvents.SHIPMENT_CREATED,
          new ShipmentCreatedFromBatchEvent(event.batchId, batchShipmentId)
        );
      } catch (error) {
        this.logger.error(`Error in batch document extracted event: ${error}`, error.stack);
        this.eventEmitter.emit(
          BatchAggregationEvents.SHIPMENT_CREATION_FAILED,
          new ShipmentCreationFromBatchFailedEvent(event.batchId, error.message)
        );
        return;
      }
    }

    // aggregate trade partners
    await this.tradePartnerAggregator.aggregate(batchShipmentId);

    // call shipment field fallback
    await this.shipmentFieldFallbackService.fallbackMissingFields(batchShipmentId);
  }

  @OnEvent(FileBatchEvent.DOCUMENT_EXTRACTED)
  async checkBatchCanCreateShipment(event: BatchDocumentsExtractedEvent) {
    // Process both API and EMAIL batches for aggregation
    if (event.audience !== FileBatchCreator.API && event.audience !== FileBatchCreator.EMAIL) {
      return;
    }

    const aggregatableDocuments = event.documents.filter(
      (document) => document.status === DocumentStatus.EXTRACTED
    );

    if (aggregatableDocuments.length === 0) {
      this.logger.warn("No aggregatable documents found");
      return;
    }

    this.logger.log(`Batch documents extracted event: ${JSON.stringify(event)}`);

    try {
      const batchHasShipment = await this.fileBatchService.checkBatchHasShipment(event.batchId);

      this.logger.debug(`Batch has shipment: ${batchHasShipment}`);

      if (!batchHasShipment) {
        // check if the batch can create a shipment
        await this.shipmentAggregator.checkBatchCanCreateShipment(
          aggregatableDocuments.map((document) => document.id),
          event.organizationId
        );
      }

      this.eventEmitter.emit(
        FileBatchEvent.READY_FOR_AGGREGATION,
        new BatchReadyForAggregationEvent(
          event.batchId,
          event.organizationId,
          event.audience,
          // TODO: remove in the future
          aggregatableDocuments.map((document) => document.id)
        )
      );
    } catch (error) {
      this.logger.error(`Batch document checking failed: ${error}`, error.stack);

      this.eventEmitter.emit(
        FileBatchEvent.CHECKING_FAILED,
        new BatchCheckingFailedEvent(event.batchId, event.organizationId, error.message)
      );
    }
  }

  @OnEvent(FileBatchEvent.READY_FOR_AGGREGATION)
  async aggregateBatch(event: BatchReadyForAggregationEvent) {
    // filter out the documents that are not extracted
    const documents = await this.fileBatchService
      .getDocuments(event.batchId)
      .then((documents) => documents.filter((document) => document.status === DocumentStatus.EXTRACTED));

    // set batch status to aggregating
    await this.fileBatchService.updateFileBatchStatus(event.batchId, FileBatchStatus.AGGREGATING);

    // emit event aggregating
    this.eventEmitter.emit(
      FileBatchEvent.AGGREGATION_STARTED,
      new BatchAggregationStartedEvent(event.batchId, event.organizationId)
    );

    // TODO: we need to separate the logic:
    // 1. aggregate trade-partners
    // 2. aggregate shipment
    // 3. during aggregating shipment, batch will listen to the create-shipment event and emit batch shipment created event
    await this.createOrUpdateShipmentAndTradePartners(event);

    // this is a workaround. for now we are expecting the shipment id to be set before the aggregation is triggered
    const shipmentId = await this.fileBatchService.getBatchShipmentId(event.batchId);

    // aggregate other documents
    await this.cls.run(async () => {
      this.cls.set("ORGANIZATION_ID", event.organizationId);

      return await this.aggregationService.aggregateDocumentsAsync(
        documents.map((document) => document.id),
        event.batchId,
        shipmentId
      );
    });
  }
}
