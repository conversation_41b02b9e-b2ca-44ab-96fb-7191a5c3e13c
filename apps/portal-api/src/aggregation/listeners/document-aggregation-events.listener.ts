import { FileBatchService } from "@/document/services/file-batch.service";
import { OnQueueEvent, QueueEventsHost, QueueEventsListener } from "@nestjs/bullmq";
import { Inject } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectRepository } from "@nestjs/typeorm";
import { DocumentAggregation, DocumentAggregationStatus } from "nest-modules";
import { Repository } from "typeorm";
import { AggregationService } from "../aggregation.service";
import { AggregationEvents, AggregationFailedEvent, AggregationSuccessEvent } from "../events";
import { Queue as AggregationQueue } from "../queues/config";

@QueueEventsListener(AggregationQueue.AGGREGATION_TASKS)
export class DocumentAggregationEventsListener extends QueueEventsHost {
  constructor(
    @Inject(EventEmitter2)
    private readonly eventEmitter: EventEmitter2,
    @InjectRepository(DocumentAggregation)
    private readonly documentAggregationRepository: Repository<DocumentAggregation>,
    @Inject(AggregationService)
    private readonly aggregationService: AggregationService,
    @Inject(FileBatchService)
    private readonly fileBatchService: FileBatchService
  ) {
    super();
  }

  @OnQueueEvent("completed")
  async onCompleted(args: { jobId: string; returnvalue: any }) {
    // TODO: handle completed event
    const { result, aggregationId, batchId, shipmentId } = args.returnvalue;

    const aggregation = await this.documentAggregationRepository.findOne({
      where: { id: aggregationId },
      relations: {
        documents: {
          shipment: true
        },
        dependencies: true
      }
    });

    if (!result.hasError) {
      await this.documentAggregationRepository.update(aggregationId, {
        status: DocumentAggregationStatus.SUCCESS
      });

      this.eventEmitter.emit(
        AggregationEvents.SUCCESS,
        new AggregationSuccessEvent({
          id: aggregation.id,
          organizationId: aggregation.organizationId,
          batchId: aggregation.batchId,
          shipmentId: aggregation.shipment?.id,
          targetId: aggregation.targetId,
          targetType: aggregation.targetType,
          documents: aggregation.documents.map((d) => ({ id: d.id, name: d.name })),
          documentId: aggregation.documents[0].id,
          documentName: aggregation.documents[0].name,
          action: aggregation.action
        })
      );

      aggregation.dependencies.forEach((dependency) => {
        this.aggregationService.applyAggregation(dependency.id);
      });
    } else {
      await this.aggregationService.setAggregationStatus(aggregation, DocumentAggregationStatus.FAILED);

      this.eventEmitter.emit(
        AggregationEvents.FAILED,
        new AggregationFailedEvent({
          id: aggregation.id,
          organizationId: aggregation.organizationId,
          batchId: aggregation.batchId,
          shipmentId: aggregation.shipment?.id,
          documents: aggregation.documents.map((d) => ({ id: d.id, name: d.name })),
          documentId: aggregation.documents[0].id,
          documentName: aggregation.documents[0].name,
          action: aggregation.action,
          error: result.error
        })
      );
    }
  }
}
