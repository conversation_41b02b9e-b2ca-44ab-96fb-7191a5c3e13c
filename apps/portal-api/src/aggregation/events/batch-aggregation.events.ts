export enum BatchAggregationEvents {
  SHIPMENT_CREATED = "file-batch.shipmentCreated",
  SHIPMENT_CREATION_FAILED = "file-batch.shipmentCreationFailed"
}

export class ShipmentCreatedFromBatchEvent {
  constructor(
    public readonly batchId: string,
    public readonly shipmentId: number
  ) {}
}

export class ShipmentCreationFromBatchFailedEvent {
  constructor(
    public readonly batchId: string,
    public readonly reason: string
  ) {}
}
