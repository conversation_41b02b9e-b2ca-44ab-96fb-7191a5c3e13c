import { AggregationTargetType, DocumentAggregationAction } from "nest-modules";

export enum AggregationEvents {
  CREATED = "aggregation.created",
  IN_PROGRESS = "aggregation.inProgress",
  SUCCESS = "aggregation.success",
  FAILED = "aggregation.failed"
}

abstract class BaseAggregationEvent {
  public readonly id: number;
  public readonly batchId: string;
  public readonly organizationId: number;

  /** @deprecated prefer to use targetId and targetType */
  public readonly shipmentId: number;

  /** @deprecated please use documents instead */
  public readonly documentId: number;

  /** @deprecated please use documents instead */
  public readonly documentName: string;

  public readonly documents: {
    id: number;
    name: string;
  }[];

  public readonly action: DocumentAggregationAction;

  constructor(payload: BaseAggregationEvent) {
    this.id = payload.id;
    this.batchId = payload.batchId;
    this.organizationId = payload.organizationId;
    this.documents = payload.documents;
    this.shipmentId = payload.shipmentId;
    this.documentId = payload.documentId;
    this.documentName = payload.documentName;
    this.action = payload.action;
  }
}

export class AggregationCreatedEvent extends BaseAggregationEvent {
  constructor(payload: AggregationCreatedEvent) {
    super(payload);
  }
}

export class AggregationInProgressEvent extends BaseAggregationEvent {
  constructor(payload: AggregationInProgressEvent) {
    super(payload);
  }
}

export class AggregationSuccessEvent extends BaseAggregationEvent {
  public readonly targetId: number;
  public readonly targetType: AggregationTargetType;

  constructor(payload: AggregationSuccessEvent) {
    super(payload);
    this.targetId = payload.targetId;
    this.targetType = payload.targetType;
  }
}

export class AggregationFailedEvent extends BaseAggregationEvent {
  public readonly error: string;

  constructor(payload: AggregationFailedEvent) {
    super(payload);
    this.error = payload.error;
  }
}
