import { BadRequestException, Injectable, Logger } from "@nestjs/common";
import { DocumentAggregationAction, FileBatchCreator } from "nest-modules";
import { AggregationService } from "./aggregation.service";
import { AggregationIntent, AggregationIntentContext } from "./intents";
import {
  AggregationResponseIntent,
  AggregationResult,
  SupportedAggregationIntent,
  UpdateResult
} from "./types";

import { BatchReadyForAggregationEvent, FileBatchEvent } from "@/document/events";
import { FileBatchService } from "@/document/services/file-batch.service";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { BATCH_DOCUMENT_AGGREGATED_EVENT } from "nest-modules";
import { UpdateCommercialInvoiceExecuter, UpdateShipmentExecuter } from "./intent-payload-executers";

@Injectable()
export class AggregationIntentService {
  private readonly logger = new Logger(AggregationIntentService.name);

  constructor(
    private readonly aggregationService: AggregationService,
    private readonly updateShipmentExecuter: UpdateShipmentExecuter,
    private readonly updateCommercialInvoiceExecuter: UpdateCommercialInvoiceExecuter,
    private readonly fileBatchService: FileBatchService,
    private readonly eventEmitter: EventEmitter2
  ) {}

  /**
   * Handle document aggregation intents.
   *
   * @param intent - The intent to handle.
   * @param context - The context of the intent.
   * @returns The result of the intent.
   */
  async handleUnsortedIntent(intent: AggregationIntent, context: AggregationIntentContext) {
    // emit ready for aggregation event
    this.eventEmitter.emit(
      FileBatchEvent.READY_FOR_AGGREGATION,
      new BatchReadyForAggregationEvent(
        context.batchId,
        context.organizationId,
        FileBatchCreator.EMAIL,
        intent.documentIds
      )
    );

    // wait for event emitter to emit the event
    const [payload] = await this.eventEmitter.waitFor(BATCH_DOCUMENT_AGGREGATED_EVENT, {
      filter: (event) => event.batchId === context.batchId,
      timeout: 900_000, // 15 minutes
      handleError: false,
      Promise: Promise,
      overload: false
    });

    return {
      intentId: intent.id,
      aggregationResults: payload.result,
      updateResult: null
    };
  }

  /**
   * Handle the intents from email module.
   *
   * @param intents - The intents to handle.
   * @param context - The context of the intents.
   * @returns The results of the intents.
   */
  async handleIntents(intents: AggregationIntent[], context: AggregationIntentContext) {
    const results: AggregationResponseIntent[] = [];

    for (const intent of intents) {
      results.push(await this.handleIntent(intent, context));
    }

    return results;
  }

  /**
   * Handle each intent.
   *
   * @param intent - The intent to handle.
   * @param context - The context of the intent.
   * @returns The result of the intent.
   */
  async handleIntent(intent: AggregationIntent, context: AggregationIntentContext) {
    if (intent.intent === SupportedAggregationIntent.UNSORTED) {
      return await this.handleUnsortedIntent(intent, context);
    }

    // FIXME: remove this code as this might not be needed
    const results: AggregationResponseIntent = {
      intentId: intent.id,
      aggregationResults: null,
      updateResult: intent.context ? await this.dispatchUpdate(intent, context) : null
    };

    return results;
  }

  /**
   * Process the intent.
   *
   * @param intent - The intent to process.
   */
  private async dispatchAggregation(
    intent: AggregationIntent,
    context: AggregationIntentContext
  ): Promise<AggregationResult[]> {
    const availableActions = await this.aggregationService.getAvailableActions({
      documentIds: intent.documentIds
    });

    const batchId = context.batchId;

    // TODO: this is a workaround to get the batchId from the intent

    // action mapping
    const actionMapping = {
      [SupportedAggregationIntent.CREATE_SHIPMENT]: DocumentAggregationAction.CREATE_SHIPMENT,
      [SupportedAggregationIntent.UPDATE_SHIPMENT]: DocumentAggregationAction.UPDATE_SHIPMENT,
      [SupportedAggregationIntent.CREATE_COMMERCIAL_INVOICE]:
        DocumentAggregationAction.CREATE_COMMERCIAL_INVOICE,
      [SupportedAggregationIntent.CREATE_CERTIFICATE_OF_ORIGIN]:
        DocumentAggregationAction.CREATE_CERTIFICATE_OF_ORIGIN
    };

    this.logger.debug("received intent", { intent }, "available actions", { availableActions });

    if (!availableActions.some((action) => action.action === actionMapping[intent.intent])) {
      throw new BadRequestException("The intent does not match any available actions");
    }

    const result = await this.aggregationService.aggregateDocuments(intent.documentIds, batchId);
    return result;
  }

  /**
   * Dispatch the update.
   *
   * @param intent - The intent to update.
   */
  private async dispatchUpdate(
    intent: AggregationIntent,
    context: AggregationIntentContext
  ): Promise<UpdateResult> {
    let result: UpdateResult;
    let handler: (context: any) => any;
    const shipmentId = intent.shipmentId ?? (await this.fileBatchService.getBatchShipmentId(context.batchId));

    switch (intent.intent) {
      case SupportedAggregationIntent.UPDATE_SHIPMENT:
        handler = (context: any) => this.updateShipment(shipmentId, context);
        break;
      case SupportedAggregationIntent.CREATE_SHIPMENT:
        handler = (context: any) => this.updateShipment(shipmentId, context);
        break;
      case SupportedAggregationIntent.UPDATE_COMMERCIAL_INVOICE:
        handler = this.updateCommercialInvoice;
        break;
    }

    if (!handler) {
      return {
        success: true
      };
    }

    try {
      await handler(intent.context);
      result = {
        success: true
      };
    } catch (error) {
      result = {
        success: false,
        error: error.message
      };
    }
    return result;
  }

  private async updateCommercialInvoice(context: any) {
    this.logger.debug("updating commercial invoice", { context });
    // TODO: get invoiceId from context
    const invoiceId = context.invoiceId;
    return await this.updateCommercialInvoiceExecuter.execute(invoiceId, context);
  }

  private async updateShipment(shipmentId: number, context: any) {
    this.logger.debug("updating shipment", { shipmentId, context });
    return await this.updateShipmentExecuter.execute(shipmentId, context);
  }
}
