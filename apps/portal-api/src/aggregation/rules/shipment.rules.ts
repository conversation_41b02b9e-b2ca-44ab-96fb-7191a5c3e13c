import { DocumentType } from "@/document/types";
import { Shipment } from "nest-modules";

export type FieldValueSource = {
  documentId: number;
  name: string;
  value: any;
};

const DocumentTypeGroup = {
  E_MANIFEST: [DocumentType.OCEAN_E_MANIFEST, DocumentType.AIR_E_MANIFEST],
  ARRIVAL_NOTICE: [
    DocumentType.OCEAN_ARRIVAL_NOTICE,
    DocumentType.AIR_ARRIVAL_NOTICE,
    DocumentType.ROAD_ARRIVAL_NOTICE
  ],
  BILL_OF_LADING: [
    DocumentType.HOUSE_OCEAN_BILL_OF_LADING,
    DocumentType.AIR_WAYBILL,
    DocumentType.ROAD_BILL_OF_LADING
  ],
  PARS_OVERLAY: [DocumentType.PARS_OVERLAY],
  CI_PL: [DocumentType.COMMERCIAL_INVOICE, DocumentType.PACKING_LIST]
};

const preferGroup = (groups: (keyof typeof DocumentTypeGroup)[]) => {
  return (values: FieldValueSource[]) => {
    for (const group of groups) {
      const value = values.find((value) => DocumentTypeGroup[group].includes(value.name as any));
      if (value) {
        return value.value;
      }
    }
    return null;
  };
};

export const shipmentAggregationRules: Partial<Record<keyof Shipment, any>> = {
  // identifiers
  hblNumber: [preferGroup(["BILL_OF_LADING", "ARRIVAL_NOTICE", "PARS_OVERLAY"])],
  cargoControlNumber: [preferGroup(["ARRIVAL_NOTICE", "E_MANIFEST", "PARS_OVERLAY"])],

  // dates
  etd: [preferGroup(["E_MANIFEST", "ARRIVAL_NOTICE", "BILL_OF_LADING", "PARS_OVERLAY", "CI_PL"])],
  etaPort: [preferGroup(["E_MANIFEST", "ARRIVAL_NOTICE", "BILL_OF_LADING", "PARS_OVERLAY"])],
  etaDestination: [preferGroup(["E_MANIFEST", "ARRIVAL_NOTICE", "BILL_OF_LADING"])],

  portCode: [preferGroup(["BILL_OF_LADING", "E_MANIFEST", "ARRIVAL_NOTICE"])],
  subLocation: [preferGroup(["BILL_OF_LADING", "E_MANIFEST", "ARRIVAL_NOTICE"])],

  // measurements
  quantity: [preferGroup(["BILL_OF_LADING", "E_MANIFEST", "ARRIVAL_NOTICE"])],
  quantityUOM: [preferGroup(["BILL_OF_LADING", "E_MANIFEST", "ARRIVAL_NOTICE"])],
  weight: [preferGroup(["BILL_OF_LADING", "E_MANIFEST", "ARRIVAL_NOTICE"])],
  weightUOM: [preferGroup(["BILL_OF_LADING", "E_MANIFEST", "ARRIVAL_NOTICE"])],
  volume: [preferGroup(["BILL_OF_LADING", "E_MANIFEST", "ARRIVAL_NOTICE"])],
  volumeUOM: [preferGroup(["BILL_OF_LADING", "E_MANIFEST", "ARRIVAL_NOTICE"])],

  // ocean specific
  voyageNumber: [preferGroup(["BILL_OF_LADING", "E_MANIFEST", "ARRIVAL_NOTICE"])],
  containers: [
    (values: FieldValueSource[]) => {
      const containers = {};

      // [{containerNumber: "123", containerType: ""}]
      for (const value of values) {
        for (const container of value.value) {
          const currentContainer = containers[container.containerNumber];

          if (!currentContainer) {
            containers[container.containerNumber] = container;
            continue;
          }

          // merge any null values
          for (const key of Object.keys(container)) {
            if (currentContainer[key] === null) {
              currentContainer[key] = container[key];
            }
          }
        }
      }

      return Object.values(containers);
    }
  ]
};

export const selectBestValue = (values: FieldValueSource[], key: keyof Shipment) => {
  const rules = shipmentAggregationRules[key];

  if (!rules) {
    // default rule
    return preferGroup(["BILL_OF_LADING", "E_MANIFEST", "ARRIVAL_NOTICE", "CI_PL"])(values);
  }

  for (const rule of rules) {
    const value = rule(values);
    if (value) {
      return value;
    }
  }

  return null;
};
