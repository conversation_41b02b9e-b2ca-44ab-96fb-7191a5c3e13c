import { DocumentAggregationAction } from "nest-modules";

// currently supported aggregation intents
export enum SupportedAggregationIntent {
  CREATE_SHIPMENT = "CREATE_SHIPMENT",
  UPDATE_SHIPMENT = "UPDATE_SHIPMENT",
  CREATE_COMMERCIAL_INVOICE = "CREATE_COMMERCIAL_INVOICE",
  UPDATE_COMMERCIAL_INVOICE = "UPDATE_COMMERCIAL_INVOICE",
  CREATE_CERTIFICATE_OF_ORIGIN = "CREATE_CERTIFICATE_OF_ORIGIN",
  UPDATE_CERTIFICATE_OF_ORIGIN = "UPDATE_CERTIFICATE_OF_ORIGIN",
  UNSORTED = "UNSORTED"
}

export type AggregationResult = {
  success: boolean;
  action: DocumentAggregationAction;
  error?: string;
  documentId: number;
  documentName: string;
  shipmentId?: number;
};

export type UpdateResult = {
  success: boolean;
  error?: string;
};

export type AggregationResponseIntent = {
  intentId: string;
  aggregationResults: AggregationResult[] | null;
  updateResult: UpdateResult | null;
};
