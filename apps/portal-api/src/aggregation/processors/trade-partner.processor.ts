import { DocumentService } from "@/document/services/document.service";
import { DocumentType } from "@/document/types";
import { TradePartnerService } from "@/trade-partner/trade-partner.service";
import { Inject, Injectable, Scope, forwardRef } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { instanceToInstance } from "class-transformer";
import { validateOrReject } from "class-validator";
import {
  AuthenticatedRequest,
  CertificateOfOrigin,
  CommercialInvoice,
  CreateTradePartnerDto,
  Document,
  DocumentAggregationStepLog,
  EditTradePartnerDto,
  PartnerType,
  Shipment,
  TradePartner
} from "nest-modules";
import { TradePartnerAggregator } from "../aggregators/trade-partner.aggregator";
import { ArrivalNoticeDocument, EMFDocument, ShipmentDocument } from "../constants";
import { TradePartnerLoader } from "../loaders/trade-partner.loader";
import { CountryMatcher } from "../matchers/country.matcher";
import { TradePartnerMatcher } from "../matchers/trade-partner.matcher";
import { BaseProcessor, ProcessorContext } from "./base.processor";

type TradePartnerKey = keyof Shipment | keyof CommercialInvoice | keyof CertificateOfOrigin;

type TradePartnerAction = "create" | "associate" | "skipped";

export type TradePartnerResult = {
  entity: TradePartner | null;
  action: TradePartnerAction;
  key: TradePartnerKey;
  confidence: number;
};

@Injectable({ scope: Scope.REQUEST })
export class TradePartnerProcessor extends BaseProcessor {
  constructor(
    @Inject(TradePartnerService)
    private readonly tradePartnerService: TradePartnerService,
    @Inject(TradePartnerMatcher)
    private readonly tradePartnerMatcher: TradePartnerMatcher,
    @Inject(CountryMatcher)
    private readonly countryMatcher: CountryMatcher,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    @Inject(DocumentService)
    private readonly documentService: DocumentService,
    @Inject(forwardRef(() => TradePartnerAggregator))
    private readonly tradePartnerAggregator: TradePartnerAggregator
  ) {
    super();
  }

  private tradePartnerRelations: TradePartnerKey[] = [
    // Shipment
    "forwarder",
    "consignee",
    "shipper",
    "carrier",

    // "pickupLocation",
    // "manufacturer",
    // "trucker",

    // Commercial Invoice
    "vendor",
    "purchaser",
    "shipTo",

    // Certificate of Origin
    "exporter",
    "producer",
    "importer"
  ];

  private getCarrierType(documentType: string) {
    switch (documentType) {
      case ShipmentDocument.AIR_WAYBILL:
      case ArrivalNoticeDocument.AIR_ARRIVAL_NOTICE:
      case EMFDocument.AIR_EMF:
        return PartnerType.AIR_CARRIER;
      case ShipmentDocument.HOUSE_OCEAN_BILL_OF_LADING:
      case ArrivalNoticeDocument.OCEAN_ARRIVAL_NOTICE:
      case EMFDocument.OCEAN_EMF:
        return PartnerType.OCEAN_CARRIER;
      case ShipmentDocument.ROAD_BILL_OF_LADING:
      case ShipmentDocument.ROAD_ARRIVAL_NOTICE:
      case EMFDocument.ROAD_EMF:
        return PartnerType.TRUCKER;
    }
  }

  private getTradePartnerType(documentType: string, key: TradePartnerKey) {
    if (key === "carrier") {
      return this.getCarrierType(documentType);
    }

    const mapping = {
      forwarder: PartnerType.FORWARDER,
      consignee: PartnerType.WAREHOUSE,
      shipper: PartnerType.SHIPPER,
      manufacturer: PartnerType.MANUFACTURER,
      trucker: PartnerType.TRUCKER,
      vendor: PartnerType.VENDOR,
      purchaser: PartnerType.IMPORTER,
      pickupLocation: PartnerType.WAREHOUSE,
      shipTo: PartnerType.IMPORTER,

      // certificate of origin
      exporter: PartnerType.SHIPPER,
      producer: PartnerType.MANUFACTURER,
      importer: PartnerType.IMPORTER
    };

    return mapping[key];
  }

  async findOrCreateTradePartner(
    data: any,
    documentType: string,
    key: TradePartnerKey,
    organizationId: number
  ): Promise<TradePartnerResult> {
    // TODO: workaround
    if (!data) {
      return {
        entity: null,
        action: "skipped",
        key: key,
        confidence: 1
      };
    }

    // Skip carrier for house ocean bill of lading
    if (key === "carrier" && documentType === DocumentType.HOUSE_OCEAN_BILL_OF_LADING) {
      return {
        entity: null,
        action: "skipped",
        key: key,
        confidence: 1
      };
    }

    // TODO: workaround for carrier
    let transformedData: any;
    if (typeof data === "string") {
      transformedData = {
        name: data
      };
    } else {
      transformedData = new TradePartnerLoader(data).get();
    }

    transformedData.partnerType = this.getTradePartnerType(documentType, key);

    const matchResult = await this.tradePartnerMatcher.matchTradePartner(
      transformedData,
      undefined,
      organizationId
    );

    if (matchResult.entity) {
      const entity = matchResult.entity;
      // check missing fields, but in the transformedData
      const fields = ["email", "phoneNumber"];
      let willUpdate = false;
      fields.forEach((field) => {
        if (!entity[field] && transformedData[field]) {
          willUpdate = true;
          entity[field] = transformedData[field];
        }
      });

      if (willUpdate) {
        const editTradePartnerDto = new EditTradePartnerDto();
        editTradePartnerDto.name = entity.name;
        editTradePartnerDto.email = entity.email;
        editTradePartnerDto.phoneNumber = entity.phoneNumber;
        editTradePartnerDto.address = entity.address;
        editTradePartnerDto.city = entity.city;
        editTradePartnerDto.postalCode = entity.postalCode;
        editTradePartnerDto.state = entity.state;
        // FIXME: we currently bypassed updating country
        // editTradePartnerDto.countryId = entity.country.id;
        await this.tradePartnerService.editTradePartner(entity.id, editTradePartnerDto);

        return {
          entity: entity,
          action: "associate",
          key: key,
          confidence: matchResult.confidence
        };
      }

      return {
        entity: entity,
        action: "associate",
        key: key,
        confidence: matchResult.confidence
      };
    }

    // dto validation
    let transformedDataDto = new CreateTradePartnerDto();
    transformedDataDto.name = transformedData.name;
    transformedDataDto.email = transformedData.email;
    transformedDataDto.phoneNumber = transformedData.phoneNumber;
    transformedDataDto.address = transformedData.address;
    transformedDataDto.city = transformedData.city;
    transformedDataDto.postalCode = transformedData.postalCode;
    transformedDataDto.state = transformedData.state;
    transformedDataDto.partnerType = transformedData.partnerType;

    if (transformedData.country) {
      const country = await this.countryMatcher.getCountryEntity(transformedData.country);

      transformedDataDto.countryId = country.id;
    }

    transformedDataDto = instanceToInstance(transformedDataDto);
    await validateOrReject(transformedDataDto);

    const result = await this.tradePartnerService.createTradePartner(transformedDataDto);

    return {
      entity: result,
      action: "create",
      key: key,
      confidence: 1
    };
  }

  // TODO: rename to process
  async get(
    context: ProcessorContext<{
      document: Document;
      documentId?: number;
      shipmentId?: number;
      documentType: string;
    }>
  ): Promise<{
    result: TradePartnerResult[];
    log: DocumentAggregationStepLog[];
  }> {
    // TODO: refactor this
    const { document, shipmentId, documentId } = context.params;
    const { dataSource, databaseLogger, setTarget } = context;

    const { data, documentType } = await this.documentService.getDocumentData(document.id);

    const tradePartners: TradePartnerResult[] = [];

    for (const [key, rawTradePartner] of Object.entries(data)) {
      if (!this.tradePartnerRelations.includes(key as TradePartnerKey)) {
        continue;
      }

      console.log(rawTradePartner);

      if (!rawTradePartner.name) {
        databaseLogger.addLog({
          message: `${key} doesn't have name, skipped`,
          field: `${key}.name`,
          level: "warning"
        });
        continue;
      }

      console.log("shipmentId", shipmentId);
      console.log("documentId", documentId);

      const canonicalTradePartner =
        documentId && shipmentId
          ? await this.tradePartnerAggregator.getCanonicalTradePartner(shipmentId, key, documentId)
          : null;

      if (canonicalTradePartner) {
        console.log("Canonical trade partner found", canonicalTradePartner);
      }

      const tradePartner = await this.findOrCreateTradePartner(
        canonicalTradePartner ?? rawTradePartner,
        documentType,
        key as TradePartnerKey,
        this.request.user.organization.id
      );

      if (tradePartner.action !== "skipped") {
        tradePartners.push(tradePartner);
      }
    }

    return {
      result: tradePartners,
      log: await databaseLogger.getLogs()
    };
  }
}
