import { Inject, Injectable, Logger } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { Document, DocumentAggregation, DocumentAggregationStepLog } from "nest-modules";
import { DataSource } from "typeorm";
import { CommercialInvoiceMatcher } from "../matchers/commercial-invoice.matcher";
import { BaseProcessor, ProcessorContext } from "./base.processor";

interface PackingListMatcherOutput {
  matchingResult: any;
  packingList: Document;
}

@Injectable()
export class PackingListMatcherProcessor extends BaseProcessor {
  private readonly logger = new Logger(PackingListMatcherProcessor.name);

  constructor(
    @Inject(CommercialInvoiceMatcher)
    private readonly invoiceMatcher: CommercialInvoiceMatcher,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {
    super();
  }

  async get(
    context: ProcessorContext<{
      commercialInvoice: Document;
      aggregation: DocumentAggregation;
    }>
  ): Promise<{
    result: PackingListMatcherOutput;
    log: DocumentAggregationStepLog[];
  }> {
    const { commercialInvoice, aggregation } = context.params;
    const { dataSource, databaseLogger, setTarget } = context;

    const ci = await this.dataSource.getRepository(Document).findOne({
      where: {
        id: commercialInvoice.id
      },
      relations: {
        file: {
          batch: true
        },
        referencedBy: { fields: true },
        fields: true
      }
    });

    if (ci.referencedBy && !ci.referencedBy.isHidden) {
      return {
        result: {
          matchingResult: null,
          packingList: ci.referencedBy
        },
        log: []
      };
    }

    const matchingResult = await this.invoiceMatcher.matchPackingList({
      lines: ci.fields.find((f) => f.name === "commercialInvoiceLines")?.value,
      vendor: ci.fields.find((f) => f.name === "vendor")?.value,
      purchaser: ci.fields.find((f) => f.name === "purchaser")?.value,
      shipTo: ci.fields.find((f) => f.name === "shipTo")?.value,
      shipmentId: ci.shipmentId
    });

    if (!matchingResult) {
      databaseLogger.info("No packing list found, matching skipped", "packingList");
      return {
        result: {
          matchingResult: null,
          packingList: null
        },
        log: []
      };
    }

    // query runner
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      ci.referencedBy = null;
      await queryRunner.manager.getRepository(Document).save(ci);

      const packingList = await queryRunner.manager.getRepository(Document).findOne({
        where: { id: matchingResult.id },
        relations: { fields: true }
      });

      packingList.reference = {
        id: commercialInvoice.id
      } as any;

      await queryRunner.manager.getRepository(Document).save(packingList);

      await queryRunner.commitTransaction();

      return {
        result: {
          matchingResult,
          packingList
        },
        log: []
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(error);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}
