import {
  commercialInvoiceSchema,
  CommercialInvoice as DocumentCommercialInvoice,
  CommercialInvoiceLine as DocumentCommercialInvoiceLine
} from "@/document/types";
import { trigramSimilarity } from "@/utils/trigram-similarity";
import { Inject, Injectable, Logger, Scope } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import {
  CommercialInvoice,
  CreateCommercialInvoiceLineDto,
  Currency,
  Document,
  DocumentAggregationStep,
  DocumentAggregationStepLog,
  Location,
  Product,
  Shipment,
  State,
  TradePartner,
  UnitOfMeasure,
  WeightUOM
} from "nest-modules";
import { DataSource } from "typeorm";
import { DatabaseLogger } from "../executer/database.logger";
import { CountryMatcher } from "../matchers";
import { BaseProcessor, ProcessorContext } from "./base.processor";

const MAX_LINE_PROCESSING_CONCURRENCY = 5;

export type MixedCILine = DocumentCommercialInvoiceLine &
  CreateCommercialInvoiceLineDto & {
    weight?: number;
    weightUOM?: WeightUOM;
    vendorId?: number;
    originId?: number;
    originStateId?: number;
    _product?: { action: "create" | "associate"; data: Product };
    index?: number;
    plIndex?: number;
  };

@Injectable({
  scope: Scope.TRANSIENT
})
export class MergeCommercialInvoiceLineProcessor extends BaseProcessor {
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    @Inject(CountryMatcher)
    private readonly countryMatcher: CountryMatcher
  ) {
    super();
  }

  private databaseLogger: DatabaseLogger;

  private readonly logger = new Logger(MergeCommercialInvoiceLineProcessor.name);

  /**
   * Check if a commercial invoice with the same invoice number already exists
   *
   * @param invoiceNumber
   * @param organizationId
   * @returns
   */
  private async checkCommercialInvoiceExistance(invoiceNumber: string, shipmentId: number) {
    if (!invoiceNumber) {
      return false;
    }

    return await this.dataSource.getRepository(CommercialInvoice).exists({
      where: {
        invoiceNumber,
        shipment: {
          id: shipmentId
        }
      }
    });
  }
  /**
   * Fallback to evenly distribute the weight of shipment to each commercial invoice
   *
   * If the commercial invoice does not have weight, we will use the weight of the shipment,
   * averaged to the number of commercial invoices
   *
   * @param shipment
   * @param queryRunner
   * @returns
   */
  private async fallbackWeightShipmentEven(shipment: Shipment): Promise<{
    count: number;
    weight: number;
    weightUOM: string;
    shipmentWeight: number;
  }> {
    const weight = shipment.weight;
    const weightUOM = shipment.weightUOM;

    const commercialInvoiceCount = await this.dataSource.manager.getRepository(Document).count({
      where: {
        name: "COMMERCIAL_INVOICE",
        shipment: {
          id: shipment.id
        }
      }
    });

    const weightPerInvoice = weight / commercialInvoiceCount;

    return {
      shipmentWeight: weight,
      count: commercialInvoiceCount,
      weight: weightPerInvoice,
      weightUOM
    };
  }

  /**
   * Fallback to evenly distribute the number of packages of shipment to each commercial invoice
   *
   * If the commercial invoice does not have number of packages, we will use the number of packages of the shipment,
   * averaged to the number of commercial invoices, minimum 1
   *
   * @param shipment
   */
  private async fallbackNumberOfPackagesShipmentEven(shipment: Shipment): Promise<{
    numberOfPackages: number;
    packageUOM: string;
    count: number;
    shipmentNumberOfPackages: number;
  }> {
    const numberOfPackages = shipment.quantity;
    const packageUOM = shipment.quantityUOM;
    const count = await this.dataSource.manager.getRepository(Document).count({
      where: {
        name: "COMMERCIAL_INVOICE",
        shipment: {
          id: shipment.id
        }
      }
    });

    const numberOfPackagesPerInvoice = Math.max(1, Math.ceil(numberOfPackages / count)); // package should be at least 1

    return {
      numberOfPackages: numberOfPackagesPerInvoice,
      packageUOM,
      count,
      shipmentNumberOfPackages: numberOfPackages
    };
  }

  async getFallbackOriginCountryAndState(
    data: any,
    shipment: Shipment,
    tradePartners: { key: string; entity: TradePartner }[]
  ) {
    let fallbackOriginCountry: string;
    let fallbackOriginCountryMessage: string;
    // fallback to port of loading's country if country of origin is not found
    if (!fallbackOriginCountry && shipment.portOfLoading) {
      const portOfLoading = await this.dataSource.manager.getRepository(Location).findOne({
        where: { id: shipment.portOfLoading.id },
        relations: {
          country: true
        }
      });
      fallbackOriginCountry = portOfLoading?.country.name;
      fallbackOriginCountryMessage = "use port of loading's country";
    }

    // fallback to vendor's country if country of origin is not found
    if (!fallbackOriginCountry && tradePartners.find((tp) => tp.key === "vendor")) {
      fallbackOriginCountry = tradePartners.find((tp) => tp.key === "vendor")?.entity.country?.alpha2;
      if (fallbackOriginCountry) {
        fallbackOriginCountryMessage = "use vendor's country";
      }
    }

    if (!fallbackOriginCountry) {
      fallbackOriginCountryMessage = "No origin country found";
    }

    let fallbackOriginState: string | null = data.originState;
    let fallbackOriginStateMessage: string | null = null;
    if (!fallbackOriginState) {
      // use vendor's country's state if origin state is not found
      if (fallbackOriginCountry === "US") {
        fallbackOriginState = tradePartners.find((tp) => tp.key === "vendor")?.entity.state;
        if (fallbackOriginState) {
          fallbackOriginStateMessage = "use vendor's state";
        } else {
          fallbackOriginStateMessage = "No origin state found";
        }
      }
    }

    return {
      fallbackOriginCountry,
      fallbackOriginCountryMessage,
      fallbackOriginState,
      fallbackOriginStateMessage
    };
  }

  async fallbackInvoiceDate(data: any, shipment: Shipment) {
    // use shipment's ETD as invoice date if invoice date is not found
    if (!data.invoiceDate && shipment.etd) {
      data.invoiceDate = shipment.etd;
      this.addLog({
        field: "invoiceDate",
        level: "info",
        message: `Invoice date not found, using shipment's ETD: ${shipment.etd}`
      });
    }
  }

  async fallbackGrossWeight(result: any, shipment: Shipment) {
    // case 1: the weight for each line is found
    if (
      result.commercialInvoiceLines.length > 0 &&
      result.commercialInvoiceLines.every((line) => line.grossWeight ?? line.netWeight)
    ) {
      result.grossWeight = result.commercialInvoiceLines.reduce(
        (acc, line) => acc + (parseInt(line.grossWeight ?? line.netWeight) || 0),
        0
      );
      result.weightUOM = result.commercialInvoiceLines[0].weightUOM;
      this.addLog({
        field: "grossWeight",
        level: "info",
        message: `Gross weight not found, using sum of weight of commercial invoice lines: ${result.grossWeight} ${result.weightUOM}`
      });
    } else {
      const { weight, weightUOM, count, shipmentWeight } = await this.fallbackWeightShipmentEven(shipment);
      result.grossWeight = weight;
      result.weightUOM = weightUOM;
      this.addLog({
        field: "grossWeight",
        level: "warning",
        message: `Gross weight not found, shipment's weight (${shipmentWeight} ${weightUOM}), averaged to ${count} commercial invoices = ${weight.toFixed(5)} ${weightUOM}`
      });
    }
  }

  async fallbackNumberOfPackages(result: any, shipment: Shipment) {
    if (result.commercialInvoiceLines.every((line) => line.package)) {
      result.numberOfPackages = result.commercialInvoiceLines.reduce(
        (acc, line) => acc + (parseInt(line.package) || 0),
        0
      );
      result.packageUOM = result.commercialInvoiceLines[0].packageUOM;
      this.addLog({
        field: "numberOfPackages",
        level: "info",
        message: `Number of packages not found, using sum of packages of commercial invoice lines: ${result.numberOfPackages} ${result.packageUOM}`
      });
    } else {
      const { numberOfPackages, packageUOM, count, shipmentNumberOfPackages } =
        await this.fallbackNumberOfPackagesShipmentEven(shipment);
      result.numberOfPackages = numberOfPackages;
      result.packageUOM = packageUOM;
      this.addLog({
        field: "numberOfPackages",
        level: "warning",
        message: `Number of packages not found, shipment's number of packages (${shipmentNumberOfPackages} ${packageUOM}), averaged to ${count} commercial invoices = ${numberOfPackages.toFixed(5)} ${packageUOM}`
      });
    }
  }

  fallbackLineOriginCountry(
    line: any,
    index: number,
    fallbackOriginCountry: string,
    fallbackOriginCountryMessage: string,
    fallbackOriginState: string,
    fallbackOriginStateMessage: string
  ) {
    if (!line.originCountry) {
      if (!fallbackOriginCountry) {
        this.addLog({
          field: `commercialInvoiceLines.${index}.originCountry`,
          level: "error",
          message: `Line does not have country of origin, and fallback country is not found`
        });
      } else {
        line.originCountry = fallbackOriginCountry;
        this.addLog({
          field: `commercialInvoiceLines.${index}.originCountry`,
          level: "info",
          message: `Line does not have country of origin, ${fallbackOriginCountryMessage}`
        });
      }
    }

    if (line.originCountry === "US" && !line.originState) {
      if (fallbackOriginState) {
        line.originState = fallbackOriginState;
        this.addLog({
          field: `commercialInvoiceLines.${index}.originState`,
          level: "info",
          message: `Line does not have state of origin, ${fallbackOriginStateMessage}`
        });
      }
    }
  }

  fallbackLineCurrencyCode(line: any, index: number) {
    this.addLog({
      field: `commercialInvoiceLines.${index}.currencyCode`,
      level: "warning",
      message: `Line does not have currency code, assuming USD`
    });
    line.currencyCode = Currency.USD;
  }

  async addLog(log: Pick<DocumentAggregationStepLog, "message" | "field" | "level">) {
    this.databaseLogger.addLog(log);
  }

  async saveResult(result: any) {
    // this.dataSource.manager
    //   .createQueryBuilder()
    //   .update(DocumentAggregationStep)
    //   .set({
    //     outputData: result
    //   })
    //   .where("id = :id", { id: this.stepLog.id })
    //   .execute();
  }

  async addOrUpdateLine(line: any, index: number) {
    // const escapedLine = JSON.stringify(line).replace(/'/g, "''");
    // this.dataSource.manager
    //   .createQueryBuilder()
    //   .update(DocumentAggregationStep)
    //   .set({
    //     outputData: () =>
    //       `jsonb_set("outputData", '{commercialInvoiceLines, ${index}}', '${escapedLine}'::jsonb)`
    //   })
    //   .where("id = :id", { id: this.stepLog.id })
    //   .execute();
  }

  private stepLog: DocumentAggregationStep;

  private lineHasValue(line: any, index: number) {
    if (line.unitPrice === 0 || line.totalLineValue === 0) {
      this.addLog({
        field: `commercialInvoiceLines.${index}.unitPrice`,
        level: "warning",
        message: `Unit price is 0, skipping`
      });
      return false;
    }
    return true;
  }

  async get(
    context: ProcessorContext<{
      data: any;
      shipment: Shipment;
      packingList: any;
      tradePartners: { key: string; entity: TradePartner }[];
    }>
  ) {
    let { data, shipment, packingList, tradePartners } = context.params;
    const { dataSource, databaseLogger, setTarget } = context;

    this.databaseLogger = databaseLogger;

    // parse data
    data = commercialInvoiceSchema.parse(data) as DocumentCommercialInvoice;

    if (!packingList) {
      this.addLog({
        field: "commercialInvoiceLines",
        level: "info",
        message: "No packing list found, merge skipped"
      });
    } else {
      packingList = commercialInvoiceSchema.parse(packingList) as DocumentCommercialInvoice;
    }

    // merge lines
    const mergedLines = (await this.mergeLines(
      data.commercialInvoiceLines,
      packingList?.commercialInvoiceLines || []
    )) as DocumentCommercialInvoiceLine[];

    // merge attributes
    const result = this.mergeAttribute(data, packingList);

    // check if commercial invoice exists
    const commercialInvoiceExists = await this.checkCommercialInvoiceExistance(
      result.invoiceNumber ?? result.poNumber,
      shipment.id
    );

    if (commercialInvoiceExists && !context.context.retry) {
      this.addLog({
        field: "commercialInvoiceLines",
        level: "info",
        message: "Commercial invoice exists, skipping"
      });

      throw new Error(
        `A commercial invoice with invoice number ${result.invoiceNumber ?? result.poNumber} already exists`
      );
    }

    // calculate total value
    result.linesValue = result.commercialInvoiceLines.reduce(
      (acc, line) => acc + (line.totalLineValue || 0),
      0
    );

    // get global origin country and state
    const {
      fallbackOriginCountry,
      fallbackOriginCountryMessage,
      fallbackOriginState,
      fallbackOriginStateMessage
    } = await this.getFallbackOriginCountryAndState(data, shipment, tradePartners);

    if (fallbackOriginCountry) {
      this.addLog({
        field: "commercialInvoiceLines",
        level: "info",
        message: `origin country will be fallback to: ${fallbackOriginCountry}, ${fallbackOriginCountryMessage}`
      });
    }

    if (fallbackOriginState) {
      this.addLog({
        field: "commercialInvoiceLines",
        level: "info",
        message: `origin state will be fallback to: ${fallbackOriginState}, ${fallbackOriginStateMessage}`
      });
    }

    // invoice date fallback
    if (!result.invoiceDate) {
      await this.fallbackInvoiceDate(result, shipment);
    }

    if (!result.grossWeight) {
      await this.fallbackGrossWeight(result, shipment);
    }

    if (result.grossWeight && !result.weightUOM) {
      result.weightUOM = "lbs";
      this.addLog({
        field: "weightUOM",
        level: "warning",
        message: "Weight unit not found, assuming lbs"
      });
    }

    if (!result.numberOfPackages) {
      await this.fallbackNumberOfPackages(result, shipment);
    }

    const limit = await import("p-limit").then((m) => m.default(MAX_LINE_PROCESSING_CONCURRENCY));

    // process each line
    await Promise.all(
      result.commercialInvoiceLines.map(async (line: MixedCILine, index) =>
        limit(async () => {
          this.logger.debug(`Processing line ${index}`);

          // fallback origin country and state
          if (!line.originCountry || !line.originState) {
            this.fallbackLineOriginCountry(
              line,
              index,
              fallbackOriginCountry,
              fallbackOriginCountryMessage,
              fallbackOriginState,
              fallbackOriginStateMessage
            );
          }

          // fallback weight
          line.weight = line.netWeight ?? line.grossWeight;

          if (!line.weight && result.netWeight) {
            line.weight = result.netWeight / result.commercialInvoiceLines.length;
            line.weightUOM = result.weightUOM;
            this.addLog({
              field: `commercialInvoiceLines.${index}.weight`,
              level: "warning",
              message: `Weight not found, using average net weight of commercial invoice: ${line.weight.toFixed(5)} ${line.weightUOM}`
            });
            this.addLog({
              field: `commercialInvoiceLines.${index}.weightUOM`,
              level: "warning",
              message: `Weight unit not found, using weight unit of commercial invoice: ${line.weightUOM}`
            });
          }

          // fallback weight (gross weight)
          if (!line.weight && result.grossWeight) {
            line.weight = result.grossWeight / result.commercialInvoiceLines.length;
            line.weightUOM = result.weightUOM;
            this.addLog({
              field: `commercialInvoiceLines.${index}.weight`,
              level: "warning",
              message: `Weight not found, using average gross weight of commercial invoice: ${line.weight.toFixed(5)} ${line.weightUOM}`
            });
            this.addLog({
              field: `commercialInvoiceLines.${index}.weightUOM`,
              level: "warning",
              message: `Weight unit not found, using weight unit of commercial invoice: ${line.weightUOM}`
            });
          }

          // fallback currency code
          if (!line.currencyCode) {
            this.fallbackLineCurrencyCode(line, index);
          }

          // set line vendor id
          line.vendorId = tradePartners.find((tp) => tp.key === "vendor")?.entity.id;

          await this.setOrigin(line);
        })
      )
    );

    // if any log is error, throw error
    if (await this.databaseLogger.stepHasError()) {
      return {
        result,
        // log: this.log,
        log: [],
        hasError: true
      };
    }

    return {
      result,
      // log: this.log
      log: []
    };
  }

  // this only applies to US shipment, and we should do this before the merge
  private async setOrigin(ciLine: any) {
    const origin = await this.countryMatcher.getCountryEntity(ciLine.originCountry);
    ciLine.originId = origin?.id;

    if (origin?.alpha2 === "US" && ciLine.originState) {
      const originState = await this.dataSource.manager.getRepository(State).findOne({
        where: {
          alpha2: ciLine.originState,
          countryId: origin?.id
        }
      });

      ciLine.originStateId = originState?.id;
    }
  }

  /**
   * Merge attributes from packing list to commercial invoice
   *
   * @param commercialInvoice
   * @param packingList
   * @returns
   */
  mergeAttribute(commercialInvoice: any, packingList: any) {
    const overridableAttributes = ["grossWeight", "netWeight", "numberOfPackages", "packageUOM", "weightUOM"];

    if (!packingList) {
      return commercialInvoice;
    }

    for (const attribute of Object.keys(packingList)) {
      const ciHasAttribute =
        commercialInvoice[attribute] !== null && commercialInvoice[attribute] !== undefined;

      const plHasAttribute = packingList[attribute] !== null && packingList[attribute] !== undefined;

      if ((!ciHasAttribute || overridableAttributes.includes(attribute)) && plHasAttribute) {
        commercialInvoice[attribute] = packingList[attribute];

        this.addLog({
          field: attribute,
          level: "info",
          message: `Use ${attribute} from packing list, ${ciHasAttribute ? commercialInvoice[attribute] : "null"} -> ${packingList[attribute]}`
        });
      }
    }

    if (!commercialInvoice.currencyCode) {
      commercialInvoice.currencyCode = Currency.USD;
      this.addLog({
        field: "currencyCode",
        level: "info",
        message: `Currency code not found, using USD`
      });
    }

    return commercialInvoice;
  }

  /**
   * Get the closest packing list line to the commercial invoice line
   *
   * @param ciLine
   * @param plLines
   * @returns
   */
  async getClosestPlLine(ciLine: any, plLines: any[]) {
    let closest = { line: null, similarity: 0 };

    for (const plLine of plLines) {
      const commonAttrs = new Set(Object.keys(ciLine).filter((attr) => plLine[attr]));

      let similarity = 0;
      if (commonAttrs.has("partNumber")) {
        similarity = plLine.partNumber === ciLine.partNumber ? 1 : 0;
        commonAttrs.delete("partNumber");
      }

      if (commonAttrs.has("goodsDescription")) {
        similarity = trigramSimilarity(ciLine.goodsDescription, plLine.goodsDescription);
        commonAttrs.delete("goodsDescription");
      }

      // foreach other attributes, check if they are the same
      for (const attribute of commonAttrs) {
        if (ciLine[attribute] === plLine[attribute]) {
          similarity += 0.2;
        }
      }

      if (similarity > closest.similarity) {
        closest = { line: plLine, similarity };
      }
    }

    return closest;
  }

  /**
   * Merge lines from packing list to commercial invoice
   *
   * @param commercialInvoiceLines
   * @param packingListLines
   * @returns
   */
  async mergeLines(commercialInvoiceLines: any[], packingListLines: any[]) {
    // add index to both lines
    commercialInvoiceLines.forEach((line, index) => {
      line.index = index;
    });
    packingListLines.forEach((line, index) => {
      line.index = index;
    });

    // don't merge if the line count is not the same
    if (commercialInvoiceLines.length !== packingListLines.length) {
      return commercialInvoiceLines;
    }

    for (const [index, ciLine] of commercialInvoiceLines.entries()) {
      const closestPlLine = await this.getClosestPlLine(ciLine, packingListLines);

      if (closestPlLine.similarity > 0.7) {
        await this.mergeLine(ciLine, closestPlLine.line, index);
      }
    }

    return commercialInvoiceLines;
  }

  private async mergeLine(ciLine: any, plLine: any, index: number) {
    // aggregation line's property is undefined, use line's property
    const properties = ["hsCode", "countryOfOrigin", "partNumber"];

    const overriddenProperties = [
      "netWeight",
      "grossWeight",
      "weightUOM",
      "volume",
      "volumeUOM",
      "package",
      "packageUOM"
    ];

    ciLine.plIndex = plLine.index;

    for (const property of properties) {
      if (!ciLine[property] && plLine[property]) {
        ciLine[property] = plLine[property];

        this.addLog({
          field: `commercialInvoiceLines.${index}.${property}`,
          level: "info",
          message: `Use ${property}: ${plLine[property]} from packing list`
        });
      }
    }

    if (!ciLine.quantityUOM) {
      ciLine.quantityUOM = UnitOfMeasure.NUMBER;
      this.addLog({
        field: `commercialInvoiceLines.${index}.quantity`,
        level: "info",
        message: `Line does not have unit for quantity, assuming number`
      });
    }

    for (const property of overriddenProperties) {
      if (plLine[property]) {
        ciLine[property] = plLine[property];

        if (ciLine[property] && ciLine[property] !== plLine[property]) {
          this.addLog({
            field: `commercialInvoiceLines.${index}.${property}`,
            level: "warning",
            message: `Override ${property}: ${ciLine[property]} with ${plLine[property]} from packing list`
          });
        } else {
          this.addLog({
            field: `commercialInvoiceLines.${index}.${property}`,
            level: "info",
            message: `Use ${property}: ${plLine[property]} from packing list`
          });
        }
      }
    }
  }
}
