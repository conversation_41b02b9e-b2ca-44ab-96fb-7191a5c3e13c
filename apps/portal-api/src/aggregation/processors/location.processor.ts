import { Inject, Injectable, Type } from "@nestjs/common";

import { LocationService } from "@/location/location.service";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import {
  CreateLocationDto,
  DocumentAggregationStepLog,
  Location,
  Shipment,
  UserPermission
} from "nest-modules";
import { LocationLoader } from "../loaders/location.loader";
import { CountryMatcher } from "../matchers/country.matcher";
import { LocationMatcher } from "../matchers/location.matcher";
import { BaseProcessor, ProcessorContext } from "./base.processor";

type LocationAction = "create" | "associate" | "skipped";

type LocationKey = keyof Shipment;

type LocationResult = {
  entity: Location | null;
  action: LocationAction;
  key: LocationKey;
  confidence: number;
};

@Injectable()
export class LocationProcessor extends BaseProcessor {
  constructor(
    @Inject(LocationMatcher)
    private readonly locationMatcher: LocationMatcher,
    @Inject(CountryMatcher)
    private readonly countryMatcher: CountryMatcher,
    @Inject(ModuleRef)
    private readonly moduleRef: ModuleRef
  ) {
    super();
  }

  protected locationRelations: Partial<keyof Shipment>[] = [
    "portOfLoading",
    "portOfDischarge",
    "placeOfDelivery"
  ];

  private async getService(service: Type<any>) {
    // set org id
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(
      {
        user: {
          permission: UserPermission.BACKOFFICE_ADMIN
        }
      },
      contextId
    );
    const resolved = await this.moduleRef.resolve(service, contextId, {
      strict: false
    });

    // @see: https://github.com/nestjs/nest/issues/5778
    await new Promise((resolve) => process.nextTick(resolve));

    return resolved;
  }

  private async matchLocation(data) {
    const location = await this.locationMatcher.fromLocationDetails(data);
    return location;
  }

  async findOrCreateLocation(data: any, key: LocationKey): Promise<LocationResult> {
    if (!data) return { action: "skipped", key, entity: null, confidence: 1 };

    if (!data.city) return { action: "skipped", key, entity: null, confidence: 1 };

    const transformedData = new LocationLoader(data).get();
    const country = await this.countryMatcher.getCountryEntity(transformedData.country.name);

    if (!country) return { action: "skipped", key, entity: null, confidence: 1 };

    const matchedLocation = await this.matchLocation({
      name: `${transformedData.city}, ${country.name}`,
      city: transformedData.city
    });

    if (matchedLocation.entity)
      return {
        entity: matchedLocation.entity,
        action: "associate",
        key,
        confidence: matchedLocation.confidence
      };

    const dto = new CreateLocationDto();
    dto.name = `${transformedData.city}, ${country.name}`;
    dto.city = transformedData.city;
    dto.countryId = country.id;

    try {
      const location = await (await this.getService(LocationService)).createLocation(dto);
      return {
        entity: location,
        action: "create",
        key,
        confidence: 1
      };
    } catch (error) {
      throw new Error("Failed to create location", {
        cause: {
          error,
          transformedData,
          key
        }
      });
    }
  }

  async get(context: ProcessorContext<{ data: any }>): Promise<{
    result: LocationResult[];
    log: DocumentAggregationStepLog[];
  }> {
    const { data } = context.params;
    const { dataSource, databaseLogger, setTarget } = context;

    const locations: LocationResult[] = [];
    for (const relation of this.locationRelations) {
      const location = await this.findOrCreateLocation(data[relation], relation);

      if (location.action === "skipped") continue;
      locations.push(location);
    }
    return {
      result: locations,
      log: []
    };
  }
}
