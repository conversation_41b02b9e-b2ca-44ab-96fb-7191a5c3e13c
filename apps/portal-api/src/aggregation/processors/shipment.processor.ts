import { forwardRef, Inject, Injectable, Logger } from "@nestjs/common";
import { AggregationTargetType, Document, DocumentAggregationStepLog, EditShipmentDto } from "nest-modules";
import { ShipmentAggregator } from "../aggregators/shipment.aggregator";
import { ShipmentFieldFallbackService } from "../field-fallback";
import { BaseProcessor, ProcessorContext } from "./base.processor";

@Injectable()
export class ShipmentProcessor extends BaseProcessor {
  private readonly logger = new Logger(ShipmentProcessor.name);
  constructor(
    @Inject(forwardRef(() => ShipmentAggregator))
    private readonly shipmentAggregator: ShipmentAggregator,
    @Inject(ShipmentFieldFallbackService)
    private readonly shipmentFieldFallbackService: ShipmentFieldFallbackService
  ) {
    super();
  }

  async get(
    context: ProcessorContext<{
      documents: Document[];
      organizationId: number;
    }>
  ): Promise<{
    result: EditShipmentDto;
    log: DocumentAggregationStepLog[];
  }> {
    const { documents, organizationId } = context.params;
    const { setTarget, getTarget } = context;

    let shipmentId = getTarget();

    this.logger.debug("shipmentId: " + shipmentId);

    const shipment = await this.shipmentAggregator.aggregate(
      documents,
      organizationId,
      shipmentId,
      undefined,
      true
    );

    if (shipmentId !== shipment.id) {
      setTarget(AggregationTargetType.SHIPMENT, shipment.id);
      shipmentId = shipment.id;
    }

    // call shipment field fallback
    await this.shipmentFieldFallbackService.fallbackMissingFields(shipmentId);

    return {
      result: shipment,
      log: []
    };
  }
}
