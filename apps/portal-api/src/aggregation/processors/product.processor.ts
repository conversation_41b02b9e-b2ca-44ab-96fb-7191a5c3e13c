import { ProductService } from "@/product/product.service";

import { Inject, Injectable, Logger, Scope } from "@nestjs/common";

import { validateOrReject } from "class-validator";
import { CreateProductDto, DocumentAggregationStepLog, Product } from "nest-modules";
import { PartNoGenerator } from "../generator/part-no.generator";
import { ProductLoader } from "../loaders/product.loader";
import { DatabaseCanadaTariffMatcher } from "../matchers";
import { CanadaTariffMatcher } from "../matchers/canada-tariff.matcher";
import { CountryMatcher } from "../matchers/country.matcher";
import { ProductMatcher } from "../matchers/product.matcher";
import { BaseProcessor, ProcessorContext } from "./base.processor";

/**
 * Get the first value from an array of functions that return a promise.
 *
 * @param fns - The array of functions to call.
 * @returns The first value that is not null.
 */
async function getFirstValue<T>(fns: (() => T | null | Promise<T | null>)[]): Promise<T | null> {
  let value: T = null;

  for (const fn of fns) {
    const result = await fn();
    if (result) {
      value = result;
      break;
    }
  }

  return value;
}

@Injectable({ scope: Scope.REQUEST })
export class ProductProcessor extends BaseProcessor {
  constructor(
    @Inject(ProductService)
    private readonly productService: ProductService,
    @Inject(CountryMatcher)
    private readonly countryMatcher: CountryMatcher,
    @Inject(ProductMatcher)
    private readonly productMatcher: ProductMatcher,
    @Inject(PartNoGenerator)
    private readonly partNoGenerator: PartNoGenerator,
    @Inject(CanadaTariffMatcher)
    private readonly canadaTariffMatcher: CanadaTariffMatcher,
    @Inject(DatabaseCanadaTariffMatcher)
    private readonly databaseCanadaTariffMatcher: DatabaseCanadaTariffMatcher
  ) {
    super();
  }

  private readonly logger = new Logger(ProductProcessor.name);

  private async getHsCode(productData: {
    description: string;
    partNumber?: string;
    hsCode?: string;
  }): Promise<string> {
    const useProvidedHsCodeIfValid = async () => {
      if (!productData.hsCode) return null;
      const providedHsCodeIsValid = await this.canadaTariffMatcher.isValidHSCode(productData.hsCode);
      return providedHsCodeIsValid ? productData.hsCode : null;
    };

    const findInDatabaseWithSamePartNumber = async () => {
      if (!productData.partNumber) return null;
      this.logger.debug(
        `Finding closest hs code for product ${productData.description} using part number ${productData.partNumber}`
      );
      const tariff = await this.databaseCanadaTariffMatcher.getClosestTariff(productData.partNumber);
      return tariff?.hsCode;
    };
    const useLlmToFindHsCode = async () => {
      if (!productData.description) return null;
      this.logger.debug(`Finding closest hs code for product ${productData.description}`);
      const result = await this.canadaTariffMatcher.getClosestTariffWithCache({
        description: productData.description,
        hsCode: productData.hsCode
      });
      return result.tariff?.hsCode;
    };

    return getFirstValue([
      findInDatabaseWithSamePartNumber, // find hscode in database through latest regular product
      useProvidedHsCodeIfValid, // use provided hs code if valid
      useLlmToFindHsCode // use llm to find hs code
    ]);
  }

  async findOrCreate(
    line: any
  ): Promise<{ data?: Product | CreateProductDto; action: "create" | "associate" }> {
    const productData = new ProductLoader(line).get();

    const dto = new CreateProductDto();
    dto.sku = productData.sku;
    dto.upc = productData.upc;
    dto.vendorPartNumber = productData.vendorPartNumber;

    dto.partNumber = productData.partNumber;
    dto.description = productData.description;

    dto.vendorId = line.vendorId;
    dto.originId = line.originId;
    dto.originStateId = line.originStateId;

    try {
      // TODO: should we also match by originStateId?
      let product = await this.productMatcher.matchProduct({
        sku: dto.sku,
        upc: dto.upc,
        description: dto.description,
        originId: dto.originId,
        vendorId: dto.vendorId,
        partNumber: dto.partNumber
      });

      if (product) {
        if (dto.partNumber) {
          return {
            data: product,
            action: "associate"
          };
        } else {
          dto.hsCode = product.hsCode;
        }
      }

      if (!dto.partNumber) {
        dto.partNumber = await this.partNoGenerator.generate(line);
      }

      dto.hsCode = await this.getHsCode(productData);

      await validateOrReject(dto);

      this.logger.debug("No Product Found, returning create product dto" + JSON.stringify(dto));

      return {
        data: dto,
        action: "create"
      };
    } catch (error) {
      this.logger.error({
        message: "Error creating product",
        error,
        dto
      });
      throw error;
    }
  }

  /**
   * Deprecated: remove this
   */
  async get(
    context: ProcessorContext<{
      lines: any[];
      tradePartners: any;
    }>
  ): Promise<{
    result: (Product & { action: "create" | "associate" })[];
    log: DocumentAggregationStepLog[];
  }> {
    const { lines, tradePartners } = context.params;
    const { dataSource, databaseLogger, setTarget } = context;

    const vendorId = tradePartners.find((partner) => partner.key === "vendor")?.entity?.id;

    if (!vendorId) {
      throw new Error("A vendor must be provided for creating products");
    }

    let log: DocumentAggregationStepLog[] = [];

    let products: (Product & { action: "create" | "associate" })[] = [];

    return {
      result: products,
      log: log
    };
  }
}
