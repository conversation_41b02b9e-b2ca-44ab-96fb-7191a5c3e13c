import { UnitOfMeasure } from "nest-modules";

type TypeOfMeasurement = "weight" | "volume" | "area" | "quantity" | "length" | "other";

export const UOM_MAP: Record<UnitOfMeasure, TypeOfMeasurement> = {
  // weight
  [UnitOfMeasure.MASS_IN_CARATS]: "weight",
  [UnitOfMeasure.MASS_IN_GRAMS]: "weight",
  [UnitOfMeasure.MASS_IN_KILOGRAMS]: "weight",
  [UnitOfMeasure.MASS_IN_METRIC_TONNES]: "weight",

  // weight - special
  [UnitOfMeasure.MASS_IN_KILOGRAM_AIR_DRY]: "weight",
  [UnitOfMeasure.MASS_IN_KILOGRAMS_OF_NAMED_SUBSTANCE]: "weight",
  [UnitOfMeasure.MASS_IN_METRIC_TONNES_AIR_DRY]: "weight",

  // volume
  [UnitOfMeasure.VOLUME_IN_CUBIC_METERS]: "volume",
  [UnitOfMeasure.VOLUME_IN_THOUSANDS_OF_CUBIC_METERS]: "volume",
  [UnitOfMeasure.LIQUID_VOLUME_IN_HECTOLITRES]: "volume",
  [UnitOfMeasure.LIQUID_VOLUME_IN_LITRES_OF_PURE_ALCOHOL]: "volume",
  [UnitOfMeasure.LIQUID_VOLUME_IN_LITRES]: "volume",

  // quantity
  [UnitOfMeasure.THOUSANDS]: "quantity",
  [UnitOfMeasure.NUMBER]: "quantity",
  [UnitOfMeasure.NUMBER_OF_PACKAGES]: "quantity",
  [UnitOfMeasure.NUMBER_OF_DOZEN]: "quantity",
  [UnitOfMeasure.NUMBER_OF_PAIRS]: "quantity",
  [UnitOfMeasure.NUMBER_OF_SETS]: "quantity",
  [UnitOfMeasure.GROSS_TWELVE_DOZEN]: "quantity",

  // area
  [UnitOfMeasure.AREA_IN_SQUARE_METERS]: "area",

  // length
  [UnitOfMeasure.METERS]: "length",

  // other
  [UnitOfMeasure.RADIOACTIVITY_IN_MEGABECQUERELS]: "other",
  [UnitOfMeasure.ELECTRICAL_ENERGY_IN_MEGAWATT_HOURS]: "other"
};
