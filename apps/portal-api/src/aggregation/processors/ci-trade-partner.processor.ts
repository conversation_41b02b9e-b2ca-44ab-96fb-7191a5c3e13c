import { TradePartnerService } from "@/trade-partner/trade-partner.service";
import { forwardRef, Inject, Injectable, Logger, Scope } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { instanceToInstance } from "class-transformer";
import { validateOrReject } from "class-validator";
import {
  AuthenticatedRequest,
  CreateTradePartnerDto,
  DocumentAggregationStepLog,
  PartnerType,
  Shipment,
  TradePartner
} from "nest-modules";
import { TradePartnerAggregator } from "../aggregators/trade-partner.aggregator";
import { TradePartnerLoader } from "../loaders/trade-partner.loader";
import { CountryMatcher } from "../matchers/country.matcher";
import { TradePartnerMatcher } from "../matchers/trade-partner.matcher";
import { BaseProcessor, ProcessorContext } from "./base.processor";

type TradePartnerKey = "vendor" | "purchaser" | "shipTo";

type TradePartnerAction = "create" | "associate" | "skipped";

type TradePartnerResult = {
  entity: TradePartner | null;
  action: TradePartnerAction;
  key: TradePartnerKey;
  confidence: number;
};

@Injectable({ scope: Scope.REQUEST })
export class CITradePartnerProcessor extends BaseProcessor {
  private readonly logger = new Logger(CITradePartnerProcessor.name);

  constructor(
    @Inject(TradePartnerService)
    private readonly tradePartnerService: TradePartnerService,
    @Inject(TradePartnerMatcher)
    private readonly tradePartnerMatcher: TradePartnerMatcher,
    @Inject(CountryMatcher)
    private readonly countryMatcher: CountryMatcher,
    @Inject(forwardRef(() => TradePartnerAggregator))
    private readonly tradePartnerAggregator: TradePartnerAggregator,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest
  ) {
    super();
  }

  private tradePartnerRelations: TradePartnerKey[] = ["vendor", "purchaser", "shipTo"];

  private getTradePartnerType(key: TradePartnerKey) {
    const mapping = {
      vendor: PartnerType.VENDOR,
      purchaser: PartnerType.IMPORTER,
      shipTo: PartnerType.IMPORTER
    };

    return mapping[key];
  }

  async findOrCreateTradePartner(data: any, key: TradePartnerKey): Promise<TradePartnerResult> {
    // TODO: workaround
    if (!data) {
      return {
        entity: null,
        action: "skipped",
        key: key,
        confidence: 1
      };
    }

    // TODO: workaround for carrier
    let transformedData: any;
    if (typeof data === "string") {
      transformedData = {
        name: data
      };
    } else {
      transformedData = new TradePartnerLoader(data).get();
    }

    transformedData.partnerType = this.getTradePartnerType(key);

    const matchResult = await this.tradePartnerMatcher.matchTradePartner(transformedData);

    console.log(matchResult);

    if (matchResult.entity !== null) {
      return {
        entity: matchResult.entity,
        action: "associate",
        key: key,
        confidence: matchResult.confidence ?? 0
      };
    }

    // dto validation
    let transformedDataDto = new CreateTradePartnerDto();
    transformedDataDto.name = transformedData.name;
    transformedDataDto.email = transformedData.email;
    transformedDataDto.phoneNumber = transformedData.phoneNumber;
    transformedDataDto.address = transformedData.address;
    transformedDataDto.city = transformedData.city;
    transformedDataDto.postalCode = transformedData.postalCode;
    transformedDataDto.state = transformedData.state;
    transformedDataDto.partnerType = transformedData.partnerType;

    if (transformedData.country) {
      const country = await this.countryMatcher.getCountryEntity(transformedData.country);

      transformedDataDto.countryId = country?.id;
    }

    transformedDataDto = instanceToInstance(transformedDataDto);
    await validateOrReject(transformedDataDto);

    const result = await this.tradePartnerService.createTradePartner(transformedDataDto);

    return {
      entity: result,
      action: "create",
      key: key,
      confidence: 1
    };
  }

  // TODO: rename to process
  async get(
    context: ProcessorContext<{
      data: any;
      documentId: number;
      shipmentId: number;
    }>
  ): Promise<{
    result: TradePartnerResult[];
    log: DocumentAggregationStepLog[];
  }> {
    const { data, shipmentId, documentId } = context.params;
    const { dataSource, databaseLogger, setTarget } = context;

    const tradePartners: TradePartnerResult[] = [];
    const log: DocumentAggregationStepLog[] = [];

    const shipmentTPMap = {
      vendor: "shipper",
      purchaser: "consignee",
      shipTo: "consignee"
    };

    const shipmentWithTp = await dataSource.manager.getRepository(Shipment).findOne({
      where: { id: shipmentId },
      relations: {
        shipper: {
          country: true
        },
        consignee: {
          country: true
        }
      }
    });

    for (const key of this.tradePartnerRelations) {
      if (!data[key]) {
        if (!shipmentWithTp) {
          throw new Error("Shipment not found");
        }

        tradePartners.push({
          action: "associate",
          key,
          entity: shipmentWithTp[shipmentTPMap[key]],
          confidence: 0
        });
        context.databaseLogger.info(
          `Commercial Invoice does not have ${key}, using shipment ${shipmentTPMap[key]}`,
          key
        );
        continue;
      }

      const canonicalTradePartner = await this.tradePartnerAggregator.getCanonicalTradePartner(
        shipmentId,
        key,
        documentId
      );

      // FIXME: we will still use the data[key] if canonicalTradePartner is null
      const tradePartner = await this.findOrCreateTradePartner(canonicalTradePartner ?? data[key], key);

      if (tradePartner.action !== "skipped") {
        tradePartners.push(tradePartner);
      }
    }

    return {
      result: tradePartners,
      log: log
    };
  }
}
