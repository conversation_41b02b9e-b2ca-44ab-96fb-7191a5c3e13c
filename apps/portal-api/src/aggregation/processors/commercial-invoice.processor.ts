import { BaseProcessor, ProcessorContext } from "./base.processor";

import { Inject, Injectable, Scope } from "@nestjs/common";

import { CommercialInvoiceService } from "@/commercial-invoice/commercial-invoice.service";
import { ShipmentService } from "@/shipment/services/shipment.service";
import { validateOrReject } from "class-validator";
import {
  AggregationTargetType,
  CommercialInvoice,
  CreateCommercialInvoiceDto,
  DocumentAggregationStepLog,
  EditCommercialInvoiceDto,
  Location,
  PackageUOM,
  Shipment,
  State,
  WeightUOM
} from "nest-modules";
import { DataSource } from "typeorm";
import { CommercialInvoiceLoader } from "../loaders/commercial-invoice.loader";
import { CountryMatcher } from "../matchers/country.matcher";

@Injectable({ scope: Scope.REQUEST })
export class CommercialInvoiceProcessor extends BaseProcessor {
  constructor(
    @Inject(CommercialInvoiceService)
    private readonly commercialInvoiceService: CommercialInvoiceService,
    @Inject(CountryMatcher)
    private readonly countryMatcher: CountryMatcher,
    @Inject(ShipmentService)
    private readonly shipmentService: ShipmentService
  ) {
    super();
  }

  async persist(
    data: CreateCommercialInvoiceDto | EditCommercialInvoiceDto,
    retry: boolean = false,
    id?: number
  ) {
    if (retry) {
      const commercialInvoice = await this.commercialInvoiceService.editCommercialInvoice(id, data);

      // remove all lines from the commercial invoice
      await this.commercialInvoiceService.clearCommercialInvoiceLines(commercialInvoice.id);

      return commercialInvoice;
    }

    return await this.commercialInvoiceService.createOrReplaceCommercialInvoice(
      data as CreateCommercialInvoiceDto
    );
  }

  async getPortOfLoadingCountryIdFromShipment(shipment: Shipment, dataSource: DataSource) {
    // so we need to load it from the database
    const portOfLoading = await dataSource.manager.getRepository(Location).findOne({
      where: { id: shipment.portOfLoading.id },
      relations: {
        country: true
      }
    });

    return portOfLoading.country.id;
  }

  private async generateInvoiceNumberFromShipment(shipment: Shipment, dataSource: DataSource) {
    const commercialInvoiceCount = await dataSource.manager.getRepository(CommercialInvoice).count({
      where: {
        shipment: {
          id: shipment.id
        },
        organization: {
          id: shipment.organizationId
        }
      }
    });
    return `CI-${shipment.hblNumber}-${commercialInvoiceCount + 1}`;
  }

  async rollback(result: CommercialInvoice) {
    if (result == null) return;

    try {
      await this.commercialInvoiceService.deleteCommercialInvoice(result.id);
    } catch (error) {
      console.error(error);
    }
  }

  async get(context: ProcessorContext<{ data: any; tradePartners: any; shipment: Shipment }>): Promise<{
    result: CommercialInvoice;
    log: DocumentAggregationStepLog[];
  }> {
    const { data, tradePartners, shipment } = context.params;
    const { dataSource, databaseLogger, setTarget } = context;

    const log: DocumentAggregationStepLog[] = [];

    // create commercial invoice
    const ci = new CommercialInvoiceLoader(data).get();

    const tradePartnerIds = {};

    for (const tradePartner of tradePartners) {
      if (tradePartner.action === "skipped") continue;
      tradePartnerIds[`${tradePartner.key}Id`] = tradePartner.entity.id;
    }

    let countryOfExportId;
    let stateOfExportId;

    if (!shipment || !shipment.portOfLoading?.id) {
      databaseLogger.error("No port of loading found in shipment", "countryOfExport");

      const vendor = tradePartners.find((t) => t.key === "vendor");
      // fallback to use vendor's country
      const countryOfExport = await this.countryMatcher.getCountryEntity(vendor?.entity?.country?.name);
      if (countryOfExport) {
        countryOfExportId = countryOfExport?.id;

        if (countryOfExport.alpha2 === "US") {
          const stateOfExport = await dataSource.manager.getRepository(State).findOne({
            where: {
              alpha2: vendor.entity.state,
              country: {
                id: countryOfExport.id
              }
            }
          });

          if (stateOfExport) {
            stateOfExportId = stateOfExport.id;
          }

          databaseLogger.info("Country of export is US, the state of export is required", "stateOfExport");
        }
      }
    } else {
      countryOfExportId = await this.getPortOfLoadingCountryIdFromShipment(shipment, dataSource);
    }

    if (!countryOfExportId) {
      databaseLogger.warning("No country of export found", "countryOfExport");
    }

    if (!ci.invoiceNumber) {
      ci.invoiceNumber = await this.generateInvoiceNumberFromShipment(shipment, dataSource);
      ci.poNumber = ci.invoiceNumber;
    }

    if (!ci.packageUOM) {
      ci.packageUOM = PackageUOM.PACKAGE;
      databaseLogger.info("Package UOM not found, using PACKAGE", "packageUOM");
    }

    if (!ci.weightUOM) {
      ci.weightUOM = WeightUOM.POUND;
      databaseLogger.info("Weight UOM not found, using POUND", "weightUOM");
    }

    const dto = new CreateCommercialInvoiceDto();

    Object.assign(dto, {
      ...ci,
      shipmentId: shipment.id,
      countryOfExportId: countryOfExportId,
      stateOfExportId: stateOfExportId,
      ...tradePartnerIds
    });

    await validateOrReject(dto);

    if (context.context.retry) {
      try {
        if (context.context.aggregation.targetId) {
          // remove current commercial invoice
          await this.commercialInvoiceService.deleteCommercialInvoice(context.context.aggregation.targetId);
        }
      } catch (error) {
        console.error(error);
      }
    }

    const commercialInvoice = await this.persist(dto);

    setTarget(AggregationTargetType.COMMERCIAL_INVOICE, commercialInvoice.id);

    return {
      result: commercialInvoice,
      log: []
    };
  }
}
