import { Inject, Injectable } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import {
  AggregationTargetType,
  CertificateOfOrigin,
  CreateMatchingRuleDto,
  MatchingConditionOperator,
  MatchingConditionService,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleService,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus,
  TradePartner
} from "nest-modules";
import { DataSource, QueryRunner } from "typeorm";
import {
  CertificateOfOriginLineLoader,
  CertificateOfOriginLoader
} from "../loaders/certificate-of-origin.loader";
import { CertificateOfOriginWithLines } from "../loaders/dto/certificate-of-origin.dto";
import { CountryMatcher } from "../matchers/country.matcher";
import { BaseProcessor, ProcessorContext } from "./base.processor";
import { TradePartnerResult } from "./trade-partner.processor";

@Injectable()
export class CertificateOfOriginProcessor extends BaseProcessor {
  constructor(
    @Inject(CountryMatcher)
    private readonly countryMatcher: CountryMatcher,
    @Inject(MatchingRuleService)
    private readonly matchingRuleService: MatchingRuleService,
    @Inject(MatchingConditionService)
    private readonly matchingConditionService: MatchingConditionService,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {
    super();
  }

  async findRelation(key: string, tradePartners: TradePartnerResult[]): Promise<TradePartner | null> {
    return tradePartners.find((tp) => tp.key === key)?.entity ?? null;
  }

  async get(context: ProcessorContext<{ data: any; tradePartners: TradePartnerResult[] }>): Promise<any> {
    const { data, tradePartners } = context.params;
    const cooData = new CertificateOfOriginLoader(data).get();

    // foreach line
    const lines = [];
    for (const line of data.certificateLine) {
      const cooLineData = new CertificateOfOriginLineLoader(line).get();
      lines.push(cooLineData);
    }

    // TODO: add exporter, producer, importer
    cooData.exporter = await this.findRelation("exporter", tradePartners);
    cooData.producer = await this.findRelation("producer", tradePartners);
    cooData.importer = await this.findRelation("importer", tradePartners);

    const country = await this.countryMatcher.getCountryEntity(cooData.countryOfOrigin.name);

    cooData.countryOfOrigin = country;
    cooData.lines = lines;

    cooData.organization = {
      id: context.context.aggregation.organizationId
    } as any;

    const cooRepository = this.dataSource.getRepository(CertificateOfOrigin);
    const coo = await cooRepository.save(cooData);
    cooData.id = coo.id;

    // set aggregation target
    context.setTarget(AggregationTargetType.CERTIFICATE_OF_ORIGIN, coo.id);

    return {
      result: await this.createComplianceRules(cooData),
      log: []
    };
  }

  /**
   * Get the name for the matching rule
   *
   * @param certificateOfOrigin
   *
   * @returns
   */
  private getMatchingRuleName(certificateOfOrigin: CertificateOfOriginWithLines, hsCode: string) {
    if (!certificateOfOrigin) {
      throw new Error("Certificate of origin is missing");
    }

    if (!certificateOfOrigin.countryOfOrigin) {
      throw new Error("Certificate of origin is missing country of origin");
    }

    if (!certificateOfOrigin.exporter) {
      throw new Error("Certificate of origin is missing exporter");
    }

    return (
      "Certificate of Origin - " +
      certificateOfOrigin.id +
      " - " +
      certificateOfOrigin.countryOfOrigin.alpha2 +
      " - " +
      certificateOfOrigin.exporter.displayName +
      " - HS code starts with " +
      hsCode.slice(0, 6)
    );
  }

  /**
   * Get the unique HS codes from the certificate of origin lines
   *
   * @param certificateOfOrigin
   *
   * @returns
   */
  private getUniqueHSCodeSet(certificateOfOrigin: CertificateOfOriginWithLines) {
    return Array.from(new Set(certificateOfOrigin.lines.map((line) => line.hsCode)));
  }

  private getMatchingConditions(hsCode: string, certificateOfOrigin: CertificateOfOriginWithLines) {
    return [
      {
        attribute: "hsCode",
        operator: MatchingConditionOperator.STARTS_WITH,
        value: hsCode.slice(0, 6),
        isOperationInverted: false
      },
      {
        attribute: "originId",
        operator: MatchingConditionOperator.EQUALS,
        value: certificateOfOrigin.countryOfOrigin.id.toString(),
        isOperationInverted: false
      },
      {
        attribute: "vendorId",
        operator: MatchingConditionOperator.EQUALS,
        value: certificateOfOrigin.exporter.id.toString(),
        isOperationInverted: false
      }
    ];
  }

  private async createMatchingRuleToCertificate(
    hsCode: string,
    certificateOfOrigin: CertificateOfOriginWithLines,
    queryRunner: QueryRunner
  ) {
    const dto = new CreateMatchingRuleDto();
    dto.name = this.getMatchingRuleName(certificateOfOrigin, hsCode);
    dto.sourceTable = MatchingRuleSourceDatabaseTable.CERTIFICATE_OF_ORIGIN;
    dto.sourceId = certificateOfOrigin.id;
    dto.destinationTable = MatchingRuleDestinationDatabaseTable.PRODUCT;

    const matchingRule = await this.matchingRuleService.createMatchingRule(dto, queryRunner);

    await this.matchingConditionService.batchUpdateMatchingConditions(
      matchingRule.id,
      {
        create: this.getMatchingConditions(hsCode, certificateOfOrigin)
      },
      queryRunner
    );

    await this.matchingRuleService.updateMatchingRuleStatus(
      matchingRule.id,
      {
        status: MatchingRuleStatus.ACTIVE
      },
      queryRunner
    );
  }

  async createComplianceRules(certificateOfOrigin: CertificateOfOriginWithLines) {
    const uniqueHSCodeSet = this.getUniqueHSCodeSet(certificateOfOrigin);

    // start query runner
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    return Promise.all(
      uniqueHSCodeSet.map(async (hsCode) => {
        await this.createMatchingRuleToCertificate(hsCode, certificateOfOrigin, queryRunner);
      })
    )
      .then(async (matchingRules) => {
        await queryRunner.commitTransaction();
        return matchingRules;
      })
      .catch(async (error) => {
        await queryRunner.rollbackTransaction();
        throw error;
      })
      .finally(async () => {
        await queryRunner.release();
      });
  }
}
