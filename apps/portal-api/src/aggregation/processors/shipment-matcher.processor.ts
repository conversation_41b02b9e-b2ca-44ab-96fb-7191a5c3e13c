import { ShipmentService } from "@/shipment/services/shipment.service";
import { Inject, Injectable } from "@nestjs/common";
import { DocumentAggregationStepLog, Shipment } from "nest-modules";
import { ShipmentMatcher } from "../matchers/shipment.matcher";
import { BaseProcessor, ProcessorContext } from "./base.processor";

@Injectable()
export class ShipmentMatcherProcessor extends BaseProcessor {
  constructor(
    @Inject(ShipmentMatcher)
    private readonly shipmentMatcher: ShipmentMatcher,
    @Inject(ShipmentService)
    private readonly shipmentService: ShipmentService
  ) {
    super();
  }

  async get(context: ProcessorContext<{ data: any; shipmentId: number }>): Promise<{
    result: Shipment;
    log: DocumentAggregationStepLog[];
  }> {
    const { data, shipmentId } = context.params;
    const { dataSource, databaseLogger, setTarget } = context;

    let shipment: Shipment;

    if (shipmentId) {
      shipment = await this.shipmentService.getShipmentById(shipmentId);
    } else {
      shipment = await this.shipmentMatcher.fromCommercialInvoice({
        shipper: {
          name: data.vendor.name,
          street: data.vendor.street
        },
        quantity: data.numberOfPackages
      });
    }

    if (!shipment) {
      throw new Error("Shipment not found");
    }

    return {
      result: shipment,
      log: []
    };
  }
}
