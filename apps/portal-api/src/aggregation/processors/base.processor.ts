import { AggregationTargetType, DocumentAggregation, DocumentAggregationStepLog } from "nest-modules";
import { DataSource } from "typeorm";
import { DatabaseLogger } from "../executer/database.logger";

export type ProcessorContext<T> = {
  params: T;
  context: {
    aggregation: DocumentAggregation;
    retry: boolean;
  };
  dataSource: DataSource;
  databaseLogger: DatabaseLogger;
  setTarget: (targetType: AggregationTargetType, targetId: number) => void;
  getTarget: () => number;
};

export abstract class BaseProcessor {
  abstract get(context: ProcessorContext<any>):
    | Promise<{
        result: any;
        hasError?: boolean;
        log?: DocumentAggregationStepLog[];
      }>
    | {
        result: any;
        hasError?: boolean;
        log?: DocumentAggregationStepLog[];
      };

  rollback(result: any): Promise<void> {
    return;
  }
}
