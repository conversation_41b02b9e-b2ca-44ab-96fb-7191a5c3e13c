import { Inject, Logger, Scope } from "@nestjs/common";

import { CommercialInvoiceLineService } from "@/commercial-invoice/commercial-invoice-line.service";

import { Injectable } from "@nestjs/common";
import {
  CommercialInvoiceLine,
  CreateCommercialInvoiceLineDto,
  DocumentAggregationStepLog,
  UnitOfMeasure,
  UnitOfMeasureType
} from "nest-modules";
import { DatabaseLogger } from "../executer/database.logger";
import { CommercialInvoiceLineWithUOM } from "../loaders/commercial-invoice-line.loader";
import { CanadaTariffMatcher } from "../matchers/canada-tariff.matcher";
import { QuantityMatcher } from "../matchers/quantity.matcher";
import { parseToUOM } from "../parsers/uom";
import { UnitConversionService } from "../unit-conversion/unit-conversion.service";
import { BaseProcessor, ProcessorContext } from "./base.processor";
import { UOM_MAP } from "./constants";
import { MixedCILine } from "./merge-commercial-invoice-line.processor";
import { ProductProcessor } from "./product.processor";

const MAX_LINE_PROCESSING_CONCURRENCY = 5;

@Injectable({ scope: Scope.TRANSIENT })
export class CommercialInvoiceLineProcessor extends BaseProcessor {
  constructor(
    @Inject(CommercialInvoiceLineService)
    private readonly commercialInvoiceLineService: CommercialInvoiceLineService,
    @Inject(CanadaTariffMatcher)
    private readonly canadaTariffMatcher: CanadaTariffMatcher,
    @Inject(UnitConversionService)
    private readonly unitConversionService: UnitConversionService,
    @Inject(QuantityMatcher)
    private readonly quantityMatcher: QuantityMatcher,
    @Inject(ProductProcessor)
    private readonly productProcessor: ProductProcessor
  ) {
    super();
  }

  private readonly logger = new Logger(CommercialInvoiceLineProcessor.name);

  private databaseLogger: DatabaseLogger;

  lineToDto(line: any): CreateCommercialInvoiceLineDto {
    const lineData = line;

    const dto = new CreateCommercialInvoiceLineDto();

    if (lineData.productId) {
      dto.productId = lineData.productId;
    } else if (lineData.temporaryProduct) {
      dto.temporaryProduct = lineData.temporaryProduct;
    }

    dto.sequence = lineData.index + 1;
    dto.goodsDescription = lineData.goodsDescription;
    dto.quantity = lineData.quantity;
    dto.unitPrice = lineData.unitPrice;
    dto.totalLineValue = lineData.totalLineValue;
    dto.originId = lineData.originId;
    dto.originStateId = lineData.originStateId;
    // dto.unitOfMeasure = lineData.unitOfMeasure;
    dto.quantity = lineData.quantity;
    dto.measurements = lineData.measurements;

    return dto;
  }

  private parseOrConvertUOM(value: number, fromUOM: string, toUOM: string) {
    if (parseToUOM(fromUOM) === null) {
      try {
        const convertedValue = this.unitConversionService.convert(value, fromUOM, toUOM);

        return {
          value: convertedValue,
          uom: toUOM
        };
      } catch (error) {
        this.databaseLogger.addLog({
          field: `commercialInvoiceLines.unitConversion`,
          level: "warning",
          message: `Cannot convert quantity from ${value} ${fromUOM} to ${toUOM}`
        });
      }
    } else {
      return {
        value,
        uom: parseToUOM(fromUOM)
      };
    }
  }

  // TODO: this needs to be refactored, we either need to convert it here or in the line dto
  private setLineAdditionalMeasurements(line: CommercialInvoiceLineWithUOM) {
    line.measurements = [];

    const quantityUnitType = line.quantityUOM
      ? this.unitConversionService.getUnitType(line.quantityUOM)
      : null;

    if (
      line.volume &&
      line.volumeUOM &&
      this.unitConversionService.getUnitType(line.volumeUOM) === "volume"
    ) {
      // line has its own volume uom
      const volume = this.parseOrConvertUOM(
        line.volume,
        line.volumeUOM,
        UnitOfMeasure.VOLUME_IN_CUBIC_METERS
      );
      if (volume) {
        line.measurements.push({
          type: UnitOfMeasureType.VOLUME,
          unitOfMeasure: volume.uom,
          value: volume.value
        } as any);
      }

      // line has volume, but quantity type is volume
    } else if (line.volume && quantityUnitType === "volume") {
      const volume = this.parseOrConvertUOM(
        line.volume,
        line.quantityUOM,
        UnitOfMeasure.VOLUME_IN_CUBIC_METERS
      );
      if (volume) {
        line.measurements.push({
          type: UnitOfMeasureType.VOLUME,
          unitOfMeasure: volume.uom,
          value: volume.value
        } as any);
      }
    }

    if (
      line.weight &&
      line.weightUOM &&
      this.unitConversionService.getUnitType(line.weightUOM) === "weight"
    ) {
      // line has its own weight uom
      const weight = this.parseOrConvertUOM(line.weight, line.weightUOM, UnitOfMeasure.MASS_IN_KILOGRAMS);
      if (weight) {
        line.measurements.push({
          type: UnitOfMeasureType.WEIGHT,
          unitOfMeasure: weight.uom,
          value: weight.value
        } as any);
      }
    } else if (line.weight && quantityUnitType === "weight") {
      // line has weight, but quantity type is weight
      const weight = this.parseOrConvertUOM(line.weight, line.quantityUOM, UnitOfMeasure.MASS_IN_KILOGRAMS);
      if (weight) {
        line.measurements.push({
          type: UnitOfMeasureType.WEIGHT,
          unitOfMeasure: weight.uom,
          value: weight.value
        } as any);
      }
    }

    // if line has quantity, add quantity measurements
    if (line.quantity) {
      if (quantityUnitType === "quantity" || quantityUnitType === null) {
        line.measurements.push({
          type: UnitOfMeasureType.PER_UNIT,
          unitOfMeasure: UnitOfMeasure.NUMBER,
          value: line.quantity
        } as any);
      } else {
        if (quantityUnitType === "area") {
          const area = this.parseOrConvertUOM(
            line.quantity,
            line.quantityUOM,
            UnitOfMeasure.AREA_IN_SQUARE_METERS
          );
          if (area) {
            line.measurements.push({
              type: UnitOfMeasureType.AREA,
              unitOfMeasure: area.uom,
              value: area.value
            } as any);
          }
        } else if (quantityUnitType === "length") {
          const length = this.parseOrConvertUOM(line.quantity, line.quantityUOM, UnitOfMeasure.METERS);
          if (length) {
            line.measurements.push({
              type: UnitOfMeasureType.LENGTH,
              unitOfMeasure: length.uom,
              value: length.value
            } as any);
          }
        }
      }
    }
  }

  private async matchOrCreateProduct(ciLine: MixedCILine) {
    let productPromise: any;

    productPromise = this.productProcessor.findOrCreate(ciLine);

    const product = await productPromise;

    if (product.action === "associate") {
      this.logger.debug(`Product ${product.data.id} found`);
      ciLine.productId = product.data.id;
      this.databaseLogger.info("Product Found", `commercialInvoiceLines.${ciLine.index}.productId`);
    } else {
      this.logger.debug(`Product ${product.data.id} not found, creating temporary product`);

      this.databaseLogger.info(
        "Product not found, creating temporary product",
        `commercialInvoiceLines.${ciLine.index}.temporaryProduct`
      );
      ciLine.temporaryProduct = product.data;
    }

    if (product.data.hsCode) {
      ciLine.hsCode = product.data.hsCode;
      this.databaseLogger.info(
        "Product Found, use product's HS code",
        `commercialInvoiceLines.${ciLine.index}.hsCode`
      );
    }
  }

  /**
   * Get the raw value of the quantity
   *
   * @param line
   * @param index
   * @returns
   */
  private getQuantityRawValue(line: CommercialInvoiceLineWithUOM): {
    value: number;
    unit: string;
  } {
    // if line has quantity
    if (line.quantity) {
      let quantityUnitType;

      if (!line.quantityUOM) {
        return {
          value: line.quantity,
          unit: "nmb"
        };
      }

      let convertedUnit = this.unitConversionService.getUnit(line.quantityUOM);

      // get the type of quantity's UoM
      if (convertedUnit) {
        quantityUnitType = this.unitConversionService.getUnitType(convertedUnit.name);
        // this.databaseLogger.addLog({
        //   field: `commercialInvoiceLines.${index}.unitOfMeasure`,
        //   level: "info",
        //   message: `Quantity's UoM type: ${line.quantityUOM} is ${quantityUnitType}`
        // });
      } else {
        // this.addLog({
        //   field: `commercialInvoiceLines.${index}.quantity`,
        //   level: "warning",
        //   message: `Assuming quantity's UoM ${line.quantityUOM} is number`
        // });
        quantityUnitType = "quantity";
      }

      const requiredUomType = UOM_MAP[line.unitOfMeasure];

      if (quantityUnitType === requiredUomType) {
        return {
          value: line.quantity,
          unit: convertedUnit?.name ?? "nmb"
        };
      }
    }

    // if line's quantity does not match the required UoM
    line.quantity = null;

    const valueColumn = UOM_MAP[line.unitOfMeasure];
    const uomColumn = `${valueColumn}UOM`;

    const value = line[valueColumn];
    const unit = line[uomColumn];

    return {
      value,
      unit
    };
  }

  private async getLineQuantity(line: CommercialInvoiceLineWithUOM, index: number) {
    const targetUnit = line.unitOfMeasure;

    const { value, unit } = this.getQuantityRawValue(line);

    let finalQuantity = value;

    if (!unit || !value) {
      if (
        // we only convert if the target unit is weight or volume, and the line has volume or weight
        (line.volumeUOM &&
          line.volume &&
          this.unitConversionService.getUnitType(line.volumeUOM) === "volume" &&
          this.unitConversionService.getUnitType(targetUnit) === "weight") ||
        (line.weightUOM &&
          line.weight &&
          this.unitConversionService.getUnitType(line.weightUOM) === "weight" &&
          this.unitConversionService.getUnitType(targetUnit) === "volume")
      ) {
        const result = await this.quantityMatcher.convert({
          goodsDescription: line.goodsDescription,
          unitOfMeasure: line.unitOfMeasure,
          quantity: line.quantity,
          quantityUOM: line.quantityUOM,
          weight: line.weight,
          weightUOM: line.weightUOM,
          volume: line.volume,
          volumeUOM: line.volumeUOM
        });

        finalQuantity = result.value;

        this.databaseLogger.addLog({
          field: `commercialInvoiceLines.${index}.quantity`,
          level: "info",
          message: `Approximated quantity: ${finalQuantity}\nReasoning: ${result.reasoning}`
        });
      } else {
        this.databaseLogger.addLog({
          field: `commercialInvoiceLines.${index}.quantity`,
          level: "warning",
          message: `Cannot get quantity in ${targetUnit} from line`
        });
      }
    } else {
      let isSameUnit = false;

      try {
        isSameUnit = this.unitConversionService.isSameUnit(unit, targetUnit);
      } catch (error) {
        this.databaseLogger.addLog({
          field: `commercialInvoiceLines.${index}.quantity`,
          level: "warning",
          message: error.message
        });
      }

      if (isSameUnit) {
        this.databaseLogger.addLog({
          field: `commercialInvoiceLines.${index}.quantity`,
          level: "info",
          message: `unit ${unit} is the same as required unit ${targetUnit}`
        });
      } else {
        if (this.unitConversionService.isSameType(unit, targetUnit)) {
          try {
            // conversion
            finalQuantity = this.unitConversionService.convert(value, unit, targetUnit);

            this.databaseLogger.addLog({
              field: `commercialInvoiceLines.${index}.quantity`,
              level: "warning",
              message: `Convert ${value} ${unit} to ${finalQuantity} ${targetUnit}`
            });
          } catch (error) {
            this.databaseLogger.addLog({
              field: `commercialInvoiceLines.${index}.quantity`,
              level: "warning",
              message: `Cannot convert quantity from ${value} ${unit} to ${targetUnit}`
            });
          }
        } else if (
          (this.unitConversionService.getUnitType(unit) === "volume" &&
            this.unitConversionService.getUnitType(targetUnit) === "weight") ||
          (this.unitConversionService.getUnitType(unit) === "weight" &&
            this.unitConversionService.getUnitType(targetUnit) === "volume")
        ) {
          const result = await this.quantityMatcher.convert({
            goodsDescription: line.goodsDescription,
            unitOfMeasure: line.unitOfMeasure,
            quantity: line.quantity,
            quantityUOM: line.quantityUOM,
            weight: line.weight,
            weightUOM: line.weightUOM,
            volume: line.volume,
            volumeUOM: line.volumeUOM
          });

          finalQuantity = result.value;

          this.databaseLogger.addLog({
            field: `commercialInvoiceLines.${index}.quantity`,
            level: "info",
            message: `Approximated quantity: ${finalQuantity}\nReasoning: ${result.reasoning}`
          });
        } else {
          this.databaseLogger.addLog({
            field: `commercialInvoiceLines.${index}.quantity`,
            level: "warning",
            message: `Cannot convert quantity from ${value} ${unit} to ${targetUnit}`
          });
        }
      }
    }

    line.quantity = finalQuantity ?? 0; // we set the quantity to 0 if it is not found

    this.databaseLogger.addLog({
      field: `commercialInvoiceLines.${index}.quantity`,
      level: "info",
      message: `Quantity adjusted to ${finalQuantity} ${line.unitOfMeasure}`
    });
  }

  private calculateUnitPrice(line: CommercialInvoiceLineWithUOM, index: number) {
    const originalUnitPrice = line.unitPrice;

    if (line.totalLineValue && line.quantity) {
      const calculatedUnitPrice = line.totalLineValue / line.quantity;

      if (Math.abs(originalUnitPrice - calculatedUnitPrice) > 0.01) {
        this.databaseLogger.addLog({
          field: `commercialInvoiceLines.${index}.unitPrice`,
          level: "warning",
          message: `Unit price is calculated to ${calculatedUnitPrice}`
        });

        line.unitPrice = calculatedUnitPrice;
      }
    }
  }

  private async matchHSCodeAndFindUOM(line: CommercialInvoiceLineWithUOM, index: number) {
    try {
      const tariff = await this.canadaTariffMatcher.getTariff(line.hsCode);

      if (!tariff) {
        if (line.hsCode) {
          this.databaseLogger.addLog({
            field: `commercialInvoiceLines.${index}.hsCode`,
            level: "error",
            message: `The provided HS code is invalid: ${line.hsCode}`
          });
        }
        return;
      }

      line.unitOfMeasure = tariff.uom ?? UnitOfMeasure.NUMBER;

      this.databaseLogger.addLog({
        field: `commercialInvoiceLines.${index}.unitOfMeasure`,
        level: "info",
        message: `HS Code ${line.hsCode} requires the unit to be ${line.unitOfMeasure}`
      });
    } catch (error) {
      console.error(error);
    }
  }

  /**
   * change this to save line to db, perform again if the product is changed
   *
   * scenario:
   * - product is not found at the moment, but another same product is being created,
   * thus we need to retry this step to associate the product
   * - only product description: we will simply create a new product
   * - product create successfully
   * - product association succeed
   *
   * therefore: we need to create a commercial invoice first,
   * then perform creation for each line
   *
   * therefore: we need to populate the data for commercial invoice first
   * then perform creation for each line
   *
   * issue: use shipment's weight, qty: multiple ci in shipment - and the number can be different
   * solution: we will drop the support for this, we only fallback field based on the un-permutable fields
   *
   * leaway: we will still fallback to shipment's trade-partner at this time
   *
   * this is the final mapping from the commercial invoice line to the commercial invoice line dto
   *
   */
  private async finalizeCreateLine(ciLine: any, index: number) {
    const newLine = { ...ciLine };
    // match or create temp product
    // attribute add: product | productId
    await this.matchOrCreateProduct(newLine);

    // match HS code and find UoM
    await this.matchHSCodeAndFindUOM(newLine, index);

    // set line additional measurements
    this.setLineAdditionalMeasurements(newLine);

    // recalculate quantity because the unit might be changed
    if (newLine.unitOfMeasure) {
      await this.getLineQuantity(newLine, index);
    }

    // re-calculate the unit price
    this.calculateUnitPrice(newLine, index);

    return newLine;
  }

  async get(context: ProcessorContext<{ lines: any; commercialInvoiceId: number }>): Promise<{
    result: {
      success: boolean;
      sequence: number;
      originalLine: any;
      line?: CommercialInvoiceLine;
      error?: any;
    }[];
    log: DocumentAggregationStepLog[];
  }> {
    const { lines, commercialInvoiceId } = context.params;
    const { dataSource, databaseLogger, setTarget } = context;

    this.databaseLogger = databaseLogger;

    const result: {
      success: boolean;
      sequence: number;
      originalLine: any;
      line?: CommercialInvoiceLine;
      dto?: CreateCommercialInvoiceLineDto;
      error?: any;
    }[] = [];

    const limit = await import("p-limit").then((m) => m.default(MAX_LINE_PROCESSING_CONCURRENCY));

    const promises = lines.map(async (line, index) =>
      limit(async () => {
        let lineDto: CreateCommercialInvoiceLineDto;

        try {
          let allowedRetries = 2;
          // save to db
          let createdLine: CommercialInvoiceLine;
          // retry to create the line if it fails
          while (allowedRetries > 0) {
            try {
              const finalizedLine = await this.finalizeCreateLine(line, index);
              lineDto = this.lineToDto(finalizedLine);
              createdLine = await this.commercialInvoiceLineService.createCommercialInvoiceLine(
                commercialInvoiceId,
                lineDto
              );
              break;
            } catch (error) {
              allowedRetries--;
              this.logger.error(error, error.stack);
              if (allowedRetries === 0) {
                throw error;
              }
            }
          }

          result.push({
            success: true,
            sequence: line.index + 1,
            originalLine: line,
            line: createdLine,
            dto: lineDto
          });
        } catch (error) {
          result.push({ success: false, sequence: line.index + 1, originalLine: line, dto: lineDto, error });
        }
      })
    );

    let previousPendingCount = 0;

    const interval = setInterval(() => {
      if (previousPendingCount !== limit.pendingCount) {
        this.logger.log(`Creating lines: ${promises.length - limit.pendingCount}/${promises.length}`);
        previousPendingCount = limit.pendingCount;
      }
    }, 1000);

    await Promise.all(promises);

    clearInterval(interval);

    return {
      result: result,
      // log: await this.databaseLogger.getLogs()
      log: []
    };
  }
}
