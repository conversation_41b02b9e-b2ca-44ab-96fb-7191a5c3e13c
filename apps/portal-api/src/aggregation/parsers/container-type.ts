import { ContainerType } from "nest-modules";

const CONTAINER_TYPE_MAPPING: Record<ContainerType, string[]> = {
  // general purpose
  [ContainerType.FCL_20GP]: [
    "20GENERALPURPOSE",
    "20GP",
    // iso type group
    "22GP",
    // iso size type
    "22G0",
    "22G1" // passive vents
  ],
  [ContainerType.FCL_40GP]: [
    "40GENERALPURPOSE",
    "40GP",
    // iso type group
    "42GP",
    // iso size type
    "42G0",
    "42G1" // passive vents
  ],

  // high cube
  [ContainerType.FCL_40HQ]: [
    "40HIGHCUBE",
    "40HQ",
    "40HC",
    "40HQGPC",
    // iso type group
    "45GP",
    // iso size type
    "45G0",
    "45G1" // passive vents
  ],
  [ContainerType.FCL_45HQ]: [
    "45HIGHCUBE",
    "45HQ",
    "45HC",
    "45HQGPC",
    // iso type group
    "L5GP",
    // iso size type
    "L5G0",
    "L5G1" // passive vents
  ],

  // refrigerated
  [ContainerType.FCL_20RF]: [
    "20REEFER",
    "20RF",
    // iso type group and code
    "22RT",
    "22R1",

    // non foodstuff
    "22RC",
    "22R9"
  ],
  [ContainerType.FCL_40RF]: [
    "40REEFER",
    "40RF",

    // iso type group and code
    "42RT",
    "42R1",

    // non foodstuff
    "42RC",
    "42R9"
  ],

  // refrigerated high cube
  [ContainerType.FCL_20RH]: [
    "20REEFERHIGHCUBE",
    "20RH",

    // iso type group
    "25RT",
    "25R1"
  ],
  [ContainerType.FCL_40RH]: [
    "40REEFERHIGHCUBE",
    "40RH",
    // iso type group
    "45RT",
    "45R1",

    // non foodstuff
    "45RC",
    "45R9"
  ],
  [ContainerType.FCL_45RH]: [
    "45REEFERHIGHCUBE",
    "45RH",
    // iso type group
    "L5RT",
    // iso size type
    "L5R1"
  ],

  // open top
  [ContainerType.FCL_20OT]: [
    "20OPENTOP",
    "20OT",

    // iso type group
    "20UT",
    "20U1",

    "22UT",
    "22U1"
  ],
  [ContainerType.FCL_40OT]: [
    "40OPENTOP",
    "40OT",
    // iso type group
    "42UT",
    "42U1"
  ],

  // flat rack
  [ContainerType.FCL_20FR]: [
    "20FLATRACK",
    "20FR",

    // iso type group
    "20PF",
    "22PF",
    "22PC",
    // iso size type
    "20P1",
    "22P1",
    "22P3",
    "22P8"
  ],
  [ContainerType.FCL_40FR]: [
    "40FLATRACK",
    "40FR",
    // iso type group
    "42PF",
    "42P1"
  ]
};

const REVERSED_CONTAINER_TYPE_MAPPING: Record<string, ContainerType> = Object.entries(
  CONTAINER_TYPE_MAPPING
).reduce(
  (acc, [key, synonyms]) => {
    synonyms.forEach((synonym) => {
      acc[synonym] = key as ContainerType;
    });

    // append original key to the mapping
    acc[key] = key as ContainerType;
    return acc;
  },
  {} as Record<string, ContainerType>
);

export const parseToContainerType = (value: string) => {
  if (!value) {
    return null;
  }

  const sanitized = value.replace(/[^a-zA-Z0-9]/g, "").toUpperCase();
  return REVERSED_CONTAINER_TYPE_MAPPING[sanitized] ?? null;
};
