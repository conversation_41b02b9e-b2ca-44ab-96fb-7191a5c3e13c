import Big from "big.js";
import moment from "moment-timezone";
import { Currency } from "nest-modules";

// TODO: check timezones
export const parseDate = (value: string) => {
  const d = moment.tz(value, "YYYY-MM-DD", "America/Toronto");

  if (d.isValid()) {
    return d.startOf("day").toDate();
  }

  return null;
};

/**
 * Parse a string to a Big object
 *
 * @param value - the string to parse
 * @returns a Big object or null if the string is not a valid number
 */
export const parseBig = (value: string) => {
  try {
    if (!value || value.trim() === "") {
      return null;
    }
    return Big(value);
  } catch (error) {
    return null;
  }
};

export const parseBoolean = (value: string) => {
  return value === "true";
};

export const parseNumber = (value: string) => {
  const n = parseFloat(value);

  if (!Number.isNaN(n)) {
    return n;
  }

  return null;
};

export const parseToEnum = <T extends { [key: string]: string | number }>(enumType: T) => {
  return (value: string) => {
    if (!value) {
      return null;
    }

    if (value.toUpperCase() in enumType) {
      return enumType[value.toUpperCase()] as T[keyof T];
    }

    if (Object.values(enumType).indexOf(value.toLowerCase() as T[keyof T]) > -1) {
      return value.toLowerCase() as T[keyof T];
    }

    return null;
  };
};

export const parseCurrency = (value: string) => {
  return parseToEnum(Currency)(value);
};

export const parseTrim = (value: string) => {
  return value?.trim();
};

export const parseWhitespace = (value: string) => {
  return value?.replace(/\s/g, "");
};

export const parseUpper = (value: string) => {
  return value?.trim().toUpperCase();
};

export const numericOnly = (value: string) => {
  return value?.replace(/[^0-9]/g, "");
};

export const parseHSCode = (value: string) => {
  return numericOnly(value);
};

export const parseAwbNumber = (value: string) => {
  // we allow hyphens in the AWB number
  return value?.replace(/[^A-Za-z0-9-]/g, "").toUpperCase();
};

export const parseHBLNumber = (value: string) => {
  // we do not allow hyphens in the HBL number
  return alphaNumericOnly(value)?.toUpperCase();
};

export const parseCCN = (value: string) => {
  // we allow hyphens in the CCN
  return value?.replace(/[^A-Za-z0-9-]/g, "").toUpperCase();
};

export const alphaNumericOnly = (value: string) => {
  return value?.replace(/[^a-zA-Z0-9]/g, "");
};

export const parsePortCode = (value: string) => {
  if (value === null || value === undefined) {
    return null;
  }

  const trimmedValue = value.trim();

  // if already has leading 0 and 3 digits, return as is
  if (/^0\d{3}$/.test(trimmedValue)) {
    return trimmedValue;
  }

  // if only has 3 digits, add a leading 0
  if (/^\d{3}$/.test(trimmedValue)) {
    return `0${trimmedValue}`;
  }

  return null;
};

export const parseEmail = (value: string) => {
  const regex =
    /^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/;
  // check if email is valid, otherwise return null
  if (!value || !regex.test(value.toLowerCase().trim())) {
    return null;
  }

  return value;
};
