import { PackageUOM, UnitOfMeasure, WeightUOM } from "nest-modules";
import { parseToPackageUOM, parseToUOM, parseToWeightUOM } from "./uom";

describe("parseToUOM", () => {
  it("should return the canonical UOM", () => {
    expect(parseToUOM("kg")).toBe(UnitOfMeasure.MASS_IN_KILOGRAMS);
  });
  it("should return the canonical UOM", () => {
    expect(parseToUOM("m3")).toBe(UnitOfMeasure.VOLUME_IN_CUBIC_METERS);
  });
});

describe("parseToPackageUOM", () => {
  it("should return the canonical UOM", () => {
    expect(parseToPackageUOM("boxes")).toBe(PackageUOM.BOX);
  });
});

describe("parseToWeightUOM", () => {
  it("should return the canonical UOM", () => {
    expect(parseToWeightUOM("KG")).toBe(WeightUOM.KILOGRAM);
  });
});
