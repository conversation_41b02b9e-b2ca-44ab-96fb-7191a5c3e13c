import { Currency } from "nest-modules";
import { parseCurrency, parseEmail, parseHSCode } from "./parser";

describe("parseHSCode", () => {
  it("should remove dots", () => {
    expect(parseHSCode("123.45")).toBe("12345");
  });

  it("should remove spaces", () => {
    expect(parseHSCode(" 12345 ")).toBe("12345");
  });

  it("should remove dots and spaces", () => {
    expect(parseHSCode("12.45 2300 00")).toBe("1245230000");
  });
});

describe("parseCurrency", () => {
  it("should parse currency", () => {
    expect(parseCurrency("USD")).toBe(Currency.USD);
  });
});

describe("parseEmail", () => {
  it("should parse email", () => {
    expect(parseEmail("<EMAIL>")).toBe("<EMAIL>");
  });

  it("should return null for invalid email", () => {
    expect(parseEmail("test")).toBeNull();
  });
});
