import { PackageUOM } from "nest-modules";
import { alphaNumericOnly } from "../parser";

const PACKAGE_UOM_MAPPING: Record<PackageUOM, string[]> = {
  [PackageUOM.PACKAGE]: ["pkg", "package", "pc", "pkgs", "packages", "pcs", "pks", "set", "sets", "ea"],
  [PackageUOM.CARTON]: ["ctn", "carton", "cartons", "ctns", "cts"],
  [PackageUOM.PALLET]: ["pallet", "pal", "pallets", "pals"],
  [PackageUOM.BAG]: ["bag", "bags", "bgs"],
  [PackageUOM.BOX]: ["box", "boxes", "boxs", "bxs"],
  [PackageUOM.DRUM]: ["drum", "drm", "drums", "drs"],
  [PackageUOM.CASE]: ["case", "cases"],
  [PackageUOM.AEROSOL]: ["aerosol"],
  [PackageUOM.AMPOULE_NON_PROTECTED]: ["ampoulenonprotected"],
  [PackageUOM.AMPOULE_PROTECTED]: ["ampouleprotected"],
  [PackageUOM.ATOMIZER]: ["atomizer"],
  [PackageUOM.BALE_COMPRESSED]: ["balecompressed"],
  [PackageUOM.BALE_NON_COMPRESSED]: ["balenoncompressed"],
  [PackageUOM.BALLOON_NON_PROTECTED]: ["ballonnonprotected"],
  [PackageUOM.BALLOON_PROTECTED]: ["ballonprotected"],
  [PackageUOM.BAR]: ["bar"],
  [PackageUOM.BARREL]: ["barrel", "barrels"],
  [PackageUOM.BARS_IN_BUNDLE]: ["barsinbundle"],
  [PackageUOM.BASKET]: ["basket", "baskets"],
  [PackageUOM.BEER_CRATE]: ["beercrate", "beercrates"],
  [PackageUOM.BIN]: ["bin", "bins"],
  [PackageUOM.BOARD]: ["board", "boards"],
  [PackageUOM.BOARD_IN_BUNDLE]: ["boardinbundle"],
  [PackageUOM.BOBBIN]: ["bobbin", "bobbins"],
  [PackageUOM.BOLT]: ["bolt", "bolts"],
  [PackageUOM.BOTTLE_NON_PROTECTED_CYLINDRICAL]: ["bottlenonprotectedcylindrical"],
  [PackageUOM.BOTTLE_NON_PROTECTED_BULBOUS]: ["bottlenonprotectedbulboous"],
  [PackageUOM.BOTTLE_PROTECTED_CYLINDRICAL]: ["bottleprotectedcylindrical"],
  [PackageUOM.BOTTLE_PROTECTED_BULBOUS]: ["bottleprotectedbulboous"],
  [PackageUOM.BOTTLE_CRATE]: ["bottlecrate", "bottlecrates"],
  [PackageUOM.BUCKET]: ["bucket", "buckets"],
  [PackageUOM.BULK_LIQUEFIED_GAS]: ["bulkliquefiedgas"],
  [PackageUOM.BULK_GAS]: ["bulkgas"],
  [PackageUOM.BULK_LIQUID]: ["bulkliquid"],
  [PackageUOM.BULK_SOLID_FINE_PARTICLES]: ["bulksolidfineparticles"],
  [PackageUOM.BULK_SOLID_GRANULAR_PARTICLES]: ["bulksolidgranularparticles"],
  [PackageUOM.BULK_SOLID_LARGE_PARTICLES]: ["bulksolidlargeparticles"],
  [PackageUOM.BUNCH]: ["bunch", "bunches"],
  [PackageUOM.BUNDLE]: ["bundle", "bundles"],
  [PackageUOM.BUTT]: ["butt", "butts"],
  [PackageUOM.CAGE]: ["cage", "cages"],
  [PackageUOM.CAN_RECTANGULAR]: ["canrectangular"],
  [PackageUOM.CAN_CYLINDRICAL]: ["cancylindrical"],
  [PackageUOM.CANISTER]: ["canister", "canisters"],
  [PackageUOM.CANVAS]: ["canvas", "canvases"],
  [PackageUOM.CARBOY_NON_PROTECTED]: ["carboynonprotected"],
  [PackageUOM.CARBOY_PROTECTED]: ["carboyprotected"],
  [PackageUOM.CASK]: ["cask", "casks"],
  [PackageUOM.CHEST]: ["chest", "chests"],
  [PackageUOM.CHURN]: ["churn", "churns"],
  [PackageUOM.COFFER]: ["coffer", "coffers"],
  [PackageUOM.COFFIN]: ["coffin", "coffins"],
  [PackageUOM.COIL]: ["coil", "coils"],
  [PackageUOM.COVER]: ["cover", "covers"],
  [PackageUOM.CRATE]: ["crate", "crates"],
  [PackageUOM.CREEL]: ["creel", "creels"],
  [PackageUOM.CUP]: ["cup", "cups"],
  [PackageUOM.CYLINDER]: ["cylinder", "cylinders"],
  [PackageUOM.DEMIJOHN_NON_PROTECTED]: ["demijohnnonprotected"],
  [PackageUOM.DEMIJOHN_PROTECTED]: ["demijohnprotected"],
  [PackageUOM.ENVELOPE]: ["envelope", "envelopes"],
  [PackageUOM.FILMPACK]: ["filmpack", "filmpacks"],
  [PackageUOM.FIRKIN]: ["firkin", "firkins"],
  [PackageUOM.FLASK]: ["flask", "flasks"],
  [PackageUOM.FOOTLOCKER]: ["footlocker", "footlockers"],
  [PackageUOM.FRAME]: ["frame", "frames"],
  [PackageUOM.FRAME_CRATE]: ["framecrate", "framecrates"],
  [PackageUOM.FRUIT_CRATE]: ["fruitcrate", "fruitcrates"],
  [PackageUOM.GAS_BOTTLE]: ["gasbottle", "gasbottles"],
  [PackageUOM.GIRDER]: ["girder", "girders"],
  [PackageUOM.GIRDERS_IN_BUNDLE]: ["girdersinbundle"],
  [PackageUOM.HAMPER]: ["hamper", "hampers"],
  [PackageUOM.HOGSHEAD]: ["hogshead", "hogsheads"],
  [PackageUOM.INGOT]: ["ingot", "ingots"],
  [PackageUOM.INGOTS_IN_BUNDLE]: ["ingotsinbundle"],
  [PackageUOM.JAR]: ["jar", "jars"],
  [PackageUOM.JERRICAN_RECTANGULAR]: ["jerricanrectangular"],
  [PackageUOM.JERRICAN_CYLINDRICAL]: ["jerricancylindrical"],
  [PackageUOM.JUG]: ["jug", "jugs"],
  [PackageUOM.JUTE_BAG]: ["jutebag", "jutebags"],
  [PackageUOM.KEG]: ["keg", "kegs"],
  [PackageUOM.LOG]: ["log", "logs"],
  [PackageUOM.LOGS_IN_BUNDLE]: ["logsinbundle"],
  [PackageUOM.MILK_CRATE]: ["milkcrate", "milkcrates"],
  [PackageUOM.MULTIPLY_BAG]: ["multiplybag", "multiplybags"],
  [PackageUOM.MULTIWALL_SACK]: ["multiwallsack", "multiwallsacks"],
  [PackageUOM.MAT]: ["mat", "mats"],
  [PackageUOM.MATCH_BOX]: ["matchbox", "matchboxes"],
  [PackageUOM.NEST]: ["nest", "nests"],
  [PackageUOM.NET]: ["net", "nets"],
  [PackageUOM.PACKET]: ["packet", "packets"],
  [PackageUOM.PAIL]: ["pail", "pails"],
  [PackageUOM.PARCEL]: ["parcel", "parcels"],
  [PackageUOM.PIPE]: ["pipe", "pipes"],
  [PackageUOM.PIPES_IN_BUNDLE]: ["pipesinbundle"],
  [PackageUOM.PITCHER]: ["pitcher", "pitchers"],
  [PackageUOM.PLANK]: ["plank", "planks"],
  [PackageUOM.PLATE]: ["plate", "plates"],
  [PackageUOM.PLATES_IN_BUNDLE]: ["platesinbundle"],
  [PackageUOM.POT]: ["pot", "pots"],
  [PackageUOM.POUCH]: ["pouch", "pouches"],
  [PackageUOM.REDNET]: ["rednet", "rednets"],
  [PackageUOM.REEL]: ["reel", "reels"],
  [PackageUOM.RING]: ["ring", "rings"],
  [PackageUOM.ROD]: ["rod", "rods"],
  [PackageUOM.RODS_IN_BUNDLE]: ["rodsinbundle"],
  [PackageUOM.SACHET]: ["sachet", "sachets"],
  [PackageUOM.SACK]: ["sack", "sacks"],
  [PackageUOM.SEA_CHEST]: ["seachest", "seachests"],
  [PackageUOM.SHALLOW_CRATE]: ["shallowcrate", "shallowcrates"],
  [PackageUOM.SHEET]: ["sheet", "sheets"],
  [PackageUOM.SHEET_METAL]: ["sheetmetal", "sheetmetals"],
  [PackageUOM.SHEETS_IN_BUNDLE]: ["sheetsinbundle"],
  [PackageUOM.SHRINK_WRAPPED]: ["shrinkwrapped"],
  [PackageUOM.SKELETON_CASE]: ["skeletoncase", "skeletoncases"],
  [PackageUOM.SKID]: ["skid", "skids"],
  [PackageUOM.SLIPSHEET]: ["slipsheet", "slipsheets"],
  [PackageUOM.SPINDLE]: ["spindle", "spindles"],
  [PackageUOM.SUITCASE]: ["suitcase", "suitcases"],
  [PackageUOM.TANK_RECTANGULAR]: ["tankrectangular"],
  [PackageUOM.TANK_CYLINDRICAL]: ["tankcylindrical"],
  [PackageUOM.TEA_CHEST]: ["teachest", "teachests"],
  [PackageUOM.TIN]: ["tin", "tins"],
  [PackageUOM.TRAY]: ["tray", "trays"],
  [PackageUOM.TRUNK]: ["trunk", "trunks"],
  [PackageUOM.TRUSS]: ["truss", "trusses"],
  [PackageUOM.TUB]: ["tub", "tubs"],
  [PackageUOM.TUBE]: ["tube", "tubes"],
  [PackageUOM.TUBE_COLLAPSIBLE]: ["tubecollapsible"],
  [PackageUOM.TUBES_IN_BUNDLE]: ["tubesinbundle"],
  [PackageUOM.TUN]: ["tun", "tuns"],
  [PackageUOM.UNPACKED]: ["unpacked"],
  [PackageUOM.VACUUM_PACKED]: ["vacuumpacked"],
  [PackageUOM.VAT]: ["vat"],
  [PackageUOM.VIAL]: ["vial", "vials"],
  [PackageUOM.WICKER_BOTTLE]: ["wickerbottle"]
};

const REVERSED_PACKAGE_UOM_MAPPING: Record<string, PackageUOM> = Object.entries(PACKAGE_UOM_MAPPING).reduce(
  (acc, [key, synonyms]) => {
    synonyms.forEach((synonym) => {
      acc[synonym] = key as PackageUOM;
    });

    // append original key to the mapping
    acc[key] = key as PackageUOM;
    return acc;
  },
  {} as Record<string, PackageUOM>
);

export function parseToPackageUOM(value?: string): PackageUOM | null {
  if (!value) {
    return null;
  }

  const sanitized = alphaNumericOnly(value.toLowerCase());

  return REVERSED_PACKAGE_UOM_MAPPING[sanitized] ?? null;
}
