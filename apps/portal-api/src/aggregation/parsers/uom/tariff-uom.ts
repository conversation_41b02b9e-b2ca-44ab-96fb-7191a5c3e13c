import { UnitOfMeasure } from "nest-modules";
import { alphaNumericOnly } from "..";

const UOM_ALIAS_MAP: Partial<Record<UnitOfMeasure, string>> = {
  [UnitOfMeasure.NUMBER]: "nmb,number,ea,each",
  [UnitOfMeasure.MASS_IN_CARATS]: "ct",
  [UnitOfMeasure.MASS_IN_KILOGRAMS]: "kg,kgs",
  [UnitOfMeasure.MASS_IN_GRAMS]: "g,gs",
  [UnitOfMeasure.MASS_IN_METRIC_TONNES]: "t,tns",
  [UnitOfMeasure.NUMBER_OF_DOZEN]: "dz",
  [UnitOfMeasure.THOUSANDS]: "k",
  [UnitOfMeasure.NUMBER_OF_PACKAGES]: "pk",
  [UnitOfMeasure.NUMBER_OF_PAIRS]: "pr",
  [UnitOfMeasure.NUMBER_OF_SETS]: "set",
  [UnitOfMeasure.LIQUID_VOLUME_IN_HECTOLITRES]: "hl",
  [UnitOfMeasure.LIQUID_VOLUME_IN_LITRES]: "l",
  [UnitOfMeasure.AREA_IN_SQUARE_METERS]: "m2",
  [UnitOfMeasure.METERS]: "m",
  [UnitOfMeasure.VOLUME_IN_CUBIC_METERS]: "m3"

  // unused
  // [UnitOfMeasure.GROSS_TWELVE_DOZEN]: "dz",
  // [UnitOfMeasure.LIQUID_VOLUME_IN_LITRES_OF_PURE_ALCOHOL]: "l",

  // [UnitOfMeasure.VOLUME_IN_THOUSANDS_OF_CUBIC_METERS]: null,
  // [UnitOfMeasure.RADIOACTIVITY_IN_MEGABECQUERELS]: null,
  // [UnitOfMeasure.ELECTRICAL_ENERGY_IN_MEGAWATT_HOURS]: null,
  // [UnitOfMeasure.MASS_IN_KILOGRAMS_OF_NAMED_SUBSTANCE]: null,
  // [UnitOfMeasure.MASS_IN_KILOGRAM_AIR_DRY]: null,
  // [UnitOfMeasure.MASS_IN_METRIC_TONNES_AIR_DRY]: null
};

export const parseToUOM = (value?: string) => {
  if (!value) {
    return null;
  }

  const uomToCanonicalMap = Object.entries(UOM_ALIAS_MAP).reduce(
    (acc, [uom, aliases]) => {
      return {
        ...acc,
        ...aliases.split(",").reduce(
          (acc2, alias) => {
            acc2[alias] = uom as UnitOfMeasure;
            return acc2;
          },
          {} as Record<string, UnitOfMeasure>
        ),
        [uom]: uom
      };
    },
    {} as Record<string, UnitOfMeasure>
  );

  const sanitized = alphaNumericOnly(value.toLowerCase());

  return uomToCanonicalMap[sanitized] ?? null;
};
