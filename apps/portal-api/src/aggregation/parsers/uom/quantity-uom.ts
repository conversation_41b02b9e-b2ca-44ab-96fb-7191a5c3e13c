import { QuantityUOM } from "nest-modules";
import { alphaNumericOnly } from "../parser";

const QUANTITY_UOM_MAPPING: Record<QuantityUOM, string[]> = {
  [QuantityUOM.PACKAGE]: [
    // ANSI
    // "pk", // TODO: difference between pack and package?
    // other
    "packages",
    "pkgs"
  ],
  [QuantityUOM.CARTON]: [
    // ANSI
    "ct",
    // other
    "carton",
    "cartons",
    "ctns",
    "cts"
  ],
  [QuantityUOM.PALLET]: [
    // ANSI
    "pl", // ANSI - Pallet/Unit Load,
    "pf", // ANSI - Pallet (Lift),
    // other
    "pallet",
    "pallets",
    "pals",
    "pls",
    "pfs",
    // skid
    "skid",
    "skids"
  ],
  [QuantityUOM.BAG]: [
    // ANSI
    "bg", // ANSI - Bag,
    // other
    "bag",
    "bags",
    "bgs"
  ],
  [QuantityUOM.ROLL]: [
    // ANSI
    "rl", // ANSI - Roll,
    // other
    "roll",
    "rolls",
    "rls"
  ],
  [QuantityUOM.BOX]: [
    // ANSI
    "bx", // ANSI - Box,
    // other
    "box",
    "boxes",
    "bxs",
    "boxs"
  ],
  [QuantityUOM.BOTTLE]: [
    // ANSI
    "bo", // ANSI - Bottle,
    // other
    "bottle",
    "bottles",
    "btls"
  ],
  [QuantityUOM.DRUM]: [
    // ANSI
    "dr", // ANSI - Drum,
    // other
    "drum",
    "drums",
    "drms"
  ],
  [QuantityUOM.CASE]: [
    // ANSI
    "ca", // ANSI - Case,
    // other
    "case",
    "cases"
  ],
  [QuantityUOM.PACK]: [
    // ANSI
    // other
    "pack",
    "packs",
    "pks"
  ]
};

const REVERSED_QUANTITY_UOM_MAPPING: Record<string, QuantityUOM> = Object.entries(
  QUANTITY_UOM_MAPPING
).reduce(
  (acc, [key, synonyms]) => {
    synonyms.forEach((synonym) => {
      acc[synonym] = key as QuantityUOM;
    });
    acc[key] = key as QuantityUOM;
    return acc;
  },
  {} as Record<string, QuantityUOM>
);

export const parseToQuantityUOM = (value?: string) => {
  if (!value) {
    return null;
  }

  const sanitized = alphaNumericOnly(value.toLowerCase());
  return REVERSED_QUANTITY_UOM_MAPPING[sanitized] ?? null;
};
