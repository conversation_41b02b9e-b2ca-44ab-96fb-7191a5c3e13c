import { VolumeUOM } from "nest-modules";
import { alphaNumericOnly } from "../parser";

const VOLUME_UOM_MAPPING: Record<VolumeUOM, string[]> = {
  [VolumeUOM.CBM]: [
    // ANSI
    "cr", // ANSI X-12 - Cubic Meter,
    // other
    "cbm3",
    "cm3",
    "m3",
    "cum",
    "cubic meter",
    "cbms"
  ],
  [VolumeUOM.CFT]: [
    // ANSI
    "cf", // ANSI X-12 - Cubic Foot,
    // other
    "cft3",
    "cf3",
    "ft3",
    "cuf",
    "cuft",
    "cubic foot",
    "cfts"
  ]
};

const REVERSED_VOLUME_UOM_MAPPING: Record<string, VolumeUOM> = Object.entries(VOLUME_UOM_MAPPING).reduce(
  (acc, [key, synonyms]) => {
    synonyms.forEach((synonym) => {
      acc[synonym] = key as VolumeUOM;
    });

    // append original key to the mapping
    acc[key] = key as VolumeUOM;
    return acc;
  },
  {} as Record<string, VolumeUOM>
);

export const parseToVolumeUOM = (value?: string) => {
  if (!value) {
    return null;
  }

  const sanitized = alphaNumericOnly(value.toLowerCase());
  return REVERSED_VOLUME_UOM_MAPPING[sanitized] ?? null;
};
