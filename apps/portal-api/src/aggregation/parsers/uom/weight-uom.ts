import { WeightUOM } from "nest-modules";
import { alphaNumericOnly } from "../parser";

const WEIGHT_UOM_MAPPING: Record<WeightUOM, string[]> = {
  [WeightUOM.KILOGRAM]: ["kg", "kgs", "kilogram", "kgms", "kilograms"],
  [WeightUOM.POUND]: ["lb", "lbs", "pound", "lbs", "pounds"],
  [WeightUOM.METRIC_CARAT]: ["mc", "metriccarat", "mc", "metriccarats"],
  [WeightUOM.DECITON]: ["dc", "deciton", "dcs", "decitons"],
  [WeightUOM.GRAM]: ["gr", "g", "gram", "grs", "grams"],
  [WeightUOM.HECTOGRAM]: ["hg", "hectogram", "hgs", "hectograms"],
  [WeightUOM.KILOGRAM_NAMED_SUBSTANCE]: ["kns", "kilogramnamedsubstance"],
  [WeightUOM.KILOGRAM_AIR_DRY]: ["ksd", "kilogramairdry"],
  [WeightUOM.KILOTON]: ["kt", "kiloton", "kts", "kilotons"],
  [WeightUOM.MILLIGRAM]: ["mg", "milligram", "mgs", "milligrams"],
  [WeightUOM.GRAMS_ODP_WEIGHTED]: ["gdw", "gramsodpweighted"],
  [WeightUOM.KILOGRAM_ODP_WEIGHTED]: ["kgdw", "kilogramodpweighted"],
  [WeightUOM.MILLIGRAM_ODP_WEIGHTED]: ["mgdw", "milligramodpweighted"],
  [WeightUOM.METRIC_TON]: ["mt", "metricton", "mts", "metrictons"],
  [WeightUOM.TONNE_AIR_DRY]: ["tad", "tonneairdry", "tads", "tonneairdries"]
};

const REVERSED_WEIGHT_UOM_MAPPING: Record<string, WeightUOM> = Object.entries(WEIGHT_UOM_MAPPING).reduce(
  (acc, [key, synonyms]) => {
    synonyms.forEach((synonym) => {
      acc[synonym] = key as WeightUOM;
    });

    // append original key to the mapping
    acc[key] = key as WeightUOM;
    return acc;
  },
  {} as Record<string, WeightUOM>
);

export const parseToWeightUOM = (value?: string) => {
  if (!value) {
    return null;
  }

  const sanitized = alphaNumericOnly(value.toLowerCase());
  return REVERSED_WEIGHT_UOM_MAPPING[sanitized] ?? null;
};
