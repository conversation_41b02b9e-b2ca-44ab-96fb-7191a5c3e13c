import { Shipment, ShipmentMode } from "nest-modules";
import { ArrivalNoticeDocument, EMFDocument } from "../constants";
import {
  parseAwbNumber,
  parseCCN,
  parseDate,
  parseHBLNumber,
  parseToQuantityUOM,
  parseToVolumeUOM,
  parseToWeightUOM
} from "../parsers";
import { Aggregator, Computed, Constant, FromField, FromFieldOptions } from "./abstract.loader";
import { UpdateShipmentArrivalNoticeDto } from "./dto/update-shipment-arrival-notice.dto";

export class PartialShipmentLoader extends Aggregator<Partial<Shipment>> {
  protected mapping: Partial<Record<keyof Shipment, FromFieldOptions>> = {
    // we will use this to get the shipment
    hblNumber: FromField("hblNumber", parseHBLNumber),
    // duplicated fields from shipment created from B/L
    etaPort: FromField("etaPort", parseDate),
    etaDestination: FromField("etaDestination", parseDate),
    volume: FromField("volume"),
    weight: FromField("weight"),
    quantity: FromField("quantity"),
    weightUOM: FromField("weightUOM", parseToWeightUOM),
    volumeUOM: FromField("volumeUOM", parseToVolumeUOM),
    quantityUOM: FromField("quantityUOM", parseToQuantityUOM),

    cargoControlNumber: Computed(this.getCCN),
    carrierCode: Computed(this.getCarrierCode)
  };

  private getCarrierCode(context: { carrierCode?: string; CCN: string }): string {
    const carrierCode = context.carrierCode ?? "";

    if (carrierCode.length === 4) {
      return carrierCode;
    }

    const sanitizedCCN = parseCCN(context["CCN"]);

    if (sanitizedCCN.length < 4) {
      return null;
    }

    return sanitizedCCN.substring(0, 4);
  }

  private getCCN(context: { CCN?: string; carrierCode?: string }): string {
    let originalCCN = context.CCN ?? "";

    originalCCN = originalCCN.replace(/\s/g, "");

    // if a carrier code is provided, and the first 4 characters of the CCN do not match the carrier code,
    // we need to prepend the carrier code to the CCN
    if (
      context.carrierCode?.length === 4 &&
      originalCCN.length > 4 &&
      originalCCN.substring(0, 4) !== context.carrierCode
    ) {
      return `${context.carrierCode}${originalCCN}`;
    }

    return originalCCN;
  }

  protected readonly oceanANMapping: Partial<Record<keyof Shipment, FromFieldOptions>> = {
    ...this.mapping,
    modeOfTransport: Constant(ShipmentMode.OCEAN_FCL),
    containerNumber: FromField("containerNumber"),
    subLocation: Computed((v) => v.subLocation?.code),
    portCode: FromField("portCode")
  };

  protected readonly airANMapping: Partial<Record<keyof Shipment, FromFieldOptions>> = {
    ...this.mapping,
    modeOfTransport: Constant(ShipmentMode.AIR),
    containerNumber: FromField("containerNumber"),
    portCode: FromField("portCode"),
    subLocation: Computed((v) => v.subLocation?.code),
    hblNumber: FromField("hblNumber", parseAwbNumber)
  };

  protected readonly roadANMapping: Partial<Record<keyof Shipment, FromFieldOptions>> = {
    ...this.mapping,
    modeOfTransport: Constant(ShipmentMode.LAND),
    portCode: FromField("portCode"),
    subLocation: Computed((v) => v.subLocation?.code)
  };

  protected readonly airEMFMapping: Partial<Record<keyof Shipment, FromFieldOptions>> = {
    ...this.airANMapping
  };

  protected readonly oceanEMFMapping: Partial<Record<keyof Shipment, FromFieldOptions>> = {
    ...this.oceanANMapping
  };

  private documentType: string;

  setDocumentType(documentType: string) {
    this.documentType = documentType;
    return this;
  }

  protected getMapping(): Partial<Record<keyof Shipment, FromFieldOptions>> {
    switch (this.documentType) {
      case ArrivalNoticeDocument.OCEAN_ARRIVAL_NOTICE:
        return this.oceanANMapping;
      case ArrivalNoticeDocument.AIR_ARRIVAL_NOTICE:
        return this.airANMapping;
      case ArrivalNoticeDocument.ROAD_ARRIVAL_NOTICE:
        return this.roadANMapping;
      case EMFDocument.OCEAN_EMF:
        return this.oceanEMFMapping;
      case EMFDocument.AIR_EMF:
        return this.airEMFMapping;
      default:
        throw new Error("Invalid document type: " + this.documentType);
    }
  }

  protected postProcess(context?: string): void {
    // TODO: we don't have sublocation code field yet
    this.addValidation(UpdateShipmentArrivalNoticeDto, [
      "hblNumber",
      "containerNumber",
      "cargoControlNumber",
      "portCode"
    ]);
  }

  constructor(data: any) {
    super(data, Shipment);
  }
}
