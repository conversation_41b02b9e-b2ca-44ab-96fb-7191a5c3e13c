import { CreateShipmentDto, QuantityUOM, Shipment, ShipmentMode } from "nest-modules";
import { ShipmentDocument } from "../constants";
import {
  parseAwbNumber,
  parseCCN,
  parseDate,
  parseHBLNumber,
  parseToContainerType,
  parseToQuantityUOM,
  parseToVolumeUOM,
  parseToWeightUOM
} from "../parsers";
import { Aggregator, Computed, Constant, FromField, FromFieldOptions } from "./abstract.loader";

export class ShipmentLoader extends Aggregator<Shipment> {
  private documentType: string;

  protected readonly mapping: Partial<Record<keyof Shipment, FromFieldOptions>> = {
    hblNumber: FromField("hblNumber", parseHBLNumber),
    volume: FromField("volume"),
    volumeUOM: FromField("volumeUOM", parseToVolumeUOM),
    weight: FromField("weight"),
    weightUOM: FromField("weightUOM", parseToWeightUOM),
    quantity: FromField("quantity"),
    quantityUOM: FromField("quantityUOM", parseToQuantityUOM, QuantityUOM.PACKAGE),
    etd: FromField("etd", parseDate),
    etaPort: FromField("etaPort", parseDate),
    etaDestination: FromField("etaDestination", parseDate)
  };

  protected readonly oceanBLMapping: Partial<Record<keyof Shipment, FromFieldOptions>> = {
    ...this.mapping,
    // TODO: ocean fcl and ocean lcl
    modeOfTransport: Constant(ShipmentMode.OCEAN_FCL),
    voyageNumber: FromField("voyageNumber"),
    vessel: FromField("vessel"),
    containerNumber: FromField("containerNumber"),
    containerType: FromField("containerType", parseToContainerType)
  };

  protected readonly roadBLMapping: Partial<Record<keyof Shipment, FromFieldOptions>> = {
    ...this.mapping,
    hblNumber: Computed(this.getRoadHBLNumber),
    modeOfTransport: Constant(ShipmentMode.LAND),
    cargoControlNumber: FromField("CCN", parseCCN),
    carrierCode: Computed(this.getCarrierCode),
    portCode: FromField("portCode")
  };

  protected readonly airWaybillMapping: Partial<Record<keyof Shipment, FromFieldOptions>> = {
    ...this.mapping,
    hblNumber: FromField("hblNumber", parseAwbNumber),
    modeOfTransport: Constant(ShipmentMode.AIR)
  };

  protected readonly parsOverlayMapping: Partial<Record<keyof Shipment, FromFieldOptions>> = {
    cargoControlNumber: FromField("CCN", parseCCN),
    carrierCode: Computed(this.getCarrierCode),
    hblNumber: FromField("hblNumber", parseHBLNumber),
    modeOfTransport: Constant(ShipmentMode.LAND),
    etd: FromField("etd", parseDate),
    etaPort: FromField("etaPort", parseDate)
  };

  private getCarrierCode(context: { carrierCode?: string; CCN?: string }): string {
    const carrierCode = context.carrierCode ?? "";

    if (carrierCode.length === 4) {
      return carrierCode;
    }

    const sanitizedCCN = parseCCN(context["CCN"] ?? "");

    if (sanitizedCCN.length < 4) {
      return null;
    }

    return sanitizedCCN.substring(0, 4);
  }

  private getRoadHBLNumber(context: { hblNumber?: string; CCN?: string }): string {
    const hblNumber = context.hblNumber ?? context["CCN"];

    if (!hblNumber) {
      return null;
    }

    return parseHBLNumber(hblNumber);
  }

  protected readonly roadANMapping: Partial<Record<keyof Shipment, FromFieldOptions>> = {
    ...this.mapping,
    hblNumber: Computed(this.getRoadHBLNumber),
    modeOfTransport: Constant(ShipmentMode.LAND),
    portCode: FromField("portCode"),
    cargoControlNumber: FromField("CCN", parseCCN),
    carrierCode: Computed(this.getCarrierCode),
    etd: FromField("etd", parseDate),
    etaPort: FromField("etaPort", parseDate),
    etaDestination: FromField("etaDestination", parseDate)
  };

  setDocumentType(documentType: string) {
    this.documentType = documentType;
    return this;
  }

  protected getMapping(): Partial<Record<keyof Shipment, FromFieldOptions>> {
    this.logger.verbose("Document type: " + this.documentType);
    switch (this.documentType) {
      case ShipmentDocument.HOUSE_OCEAN_BILL_OF_LADING:
        return this.oceanBLMapping;
      case ShipmentDocument.ROAD_BILL_OF_LADING:
        return this.roadBLMapping;
      case ShipmentDocument.AIR_WAYBILL:
        return this.airWaybillMapping;
      case ShipmentDocument.ROAD_ARRIVAL_NOTICE:
        return this.roadANMapping;
      case ShipmentDocument.PARS_OVERLAY:
        return this.parsOverlayMapping;
      default:
        throw new Error("Invalid document type: " + this.documentType);
    }
  }

  constructor(data: any) {
    super(data, Shipment);
  }

  protected postProcess(context?: string): void {
    this.addValidation(CreateShipmentDto, ["modeOfTransport", "quantityUOM", "quantity", "hblNumber"]);
  }
}
