import { Product } from "nest-modules";
import { parseHSCode, parseUpper } from "../parsers/parser";
import { Aggregator, FromField, FromFieldOptions, FromFieldRelation } from "./abstract.loader";
import { CountryLoader } from "./country.loader";

export class ProductLoader extends Aggregator<Product> {
  protected readonly mapping: Partial<Record<keyof Product, FromFieldOptions>> = {
    sku: FromField("sku", parseUpper),
    upc: From<PERSON>ield("upc", parseUpper),
    vendorPartNumber: FromField("vendorPartNumber", parseUpper),

    // required fields
    partNumber: From<PERSON>ield("partNumber", parseUpper),
    origin: FromFieldRelation("originCountry", CountryLoader),
    description: From<PERSON>ield("goodsDescription"),
    hsCode: FromField("hsCode", parseHSCode)
  };

  constructor(data: any, defaultValue?: Partial<Product>) {
    super(data, Product, defaultValue);
  }
}
