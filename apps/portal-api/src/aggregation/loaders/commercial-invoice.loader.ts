import Big from "big.js";
import { CanadaTreatmentCode, CanadaVfdCode, CommercialInvoice, Currency, PartnerType } from "nest-modules";
import { parseCurrency, parseDate } from "../parsers";
import { parseToPackageUOM, parseToWeightUOM } from "../parsers/uom";
import { levenshteinDistance } from "../utils/levenshtein-distance.util";
import { Aggregator, Computed, FromField, FromFieldOptions } from "./abstract.loader";
import { TradePartnerLoader } from "./trade-partner.loader";

type AdditionalInfoForValidation = {
  discount: Big;
  total: Big;
  invoiceTotal: Big;
  tax: Big;
  transCost: Big;
  packCost: Big;
  miscCost: Big;
};

export class CommercialInvoiceLoader extends Aggregator<CommercialInvoice> {
  protected readonly mapping: Partial<Record<keyof CommercialInvoice, FromFieldOptions>> = {
    invoiceNumber: FromField("invoiceNumber", (value) =>
      value.match(/.*[a-zA-Z0-9].*/) ? value.trim() : null
    ),
    invoiceDate: FromField("invoiceDate", parseDate),
    poNumber: Computed((invoice) => invoice.poNumber ?? invoice.invoiceNumber),

    currency: Computed((invoice) => parseCurrency(invoice.currencyCode) ?? Currency.USD),
    numberOfPackages: FromField("numberOfPackages"),
    grossWeight: Computed((invoice) => invoice.grossWeight ?? invoice.netWeight),
    weightUOM: FromField("weightUOM", parseToWeightUOM),
    packageUOM: FromField("packageUOM", parseToPackageUOM)
  };

  private validationContext: AdditionalInfoForValidation;

  constructor(data: any) {
    super(data, CommercialInvoice);
  }

  protected preProcess(): void {
    this.validationContext = {
      discount: Big(this.data["discount"] ?? 0).abs(),
      // some documents only have INVOICETOTAL
      // some documents only have TOTAL
      total: Big(this.data["total"] ?? this.data["invoiceTotal"] ?? 0),
      invoiceTotal: Big(this.data["invoiceTotal"] ?? this.data["total"] ?? 0),
      tax: Big(this.data["tax"] ?? 0),
      transCost: Big(this.data["transCost"] ?? 0),
      packCost: Big(this.data["packCost"] ?? 0),
      miscCost: Big(this.data["miscCost"] ?? 0)
    };
  }

  private assignVendorToProducts(): void {
    const vendor = this.data["vendor"];

    if (vendor) {
      this.logger.debug("Vendor", vendor);
      this.targetEntity.commercialInvoiceLines.forEach((line, index) => {
        line.product.vendor = new TradePartnerLoader(vendor, {
          partnerType: PartnerType.VENDOR
        }).get("commercialInvoiceLines." + index + ".product.vendor");
      });
    } else {
      this.addValidationError(["vendor"], null, "Vendor is required");
    }
  }

  private getCanadaTreatmentCode(code: number): CanadaTreatmentCode {
    const canadaTreatmentCode = new CanadaTreatmentCode();
    canadaTreatmentCode.code = code;
    return canadaTreatmentCode;
  }

  private getCanadaVfdCode(code: number): CanadaVfdCode {
    const canadaVfdCode = new CanadaVfdCode();
    canadaVfdCode.code = code;
    return canadaVfdCode;
  }

  /**
   * Check if the invoice has additional costs
   *
   * checks if any of the additional costs are greater than 0
   * transCost, packCost, miscCost
   *
   * @returns true if the invoice has additional costs, false otherwise
   */
  private hasAdditionalCost(): boolean {
    return (
      this.validationContext.transCost.gt(0) ||
      this.validationContext.packCost.gt(0) ||
      this.validationContext.miscCost.gt(0)
    );
  }

  /**
   * Check if the vendor is the same as the purchaser
   *
   * We currently use a levenshtein distance of 2 to determine if the vendor is the same as the purchaser
   *
   * @returns true if the vendor is the same as the purchaser, false otherwise
   * @deprecated this is handled by the CommercialInvoiceService
   */
  private vendorIsSameAsPurchaser(): boolean {
    if (!this.targetEntity.vendor) {
      this.addValidationError(["vendor"], null, "Vendor is required");
    }
    if (!this.targetEntity.vendor?.name || !this.targetEntity.purchaser?.name) {
      return false;
    }
    const distance = levenshteinDistance(this.targetEntity.vendor.name, this.targetEntity.purchaser.name);

    this.logger.verbose(
      `Vendor: ${this.targetEntity.vendor.name}, Purchaser: ${this.targetEntity.purchaser.name}, Distance: ${distance}`
    );
    return distance <= 2;
  }

  /**
   * Get the VFD code based on the vendor and additional costs
   *
   * 1_: not related
   * 2_: related
   *
   * _3: without adjustments
   * _4: with adjustments
   *
   * @returns the VFD code
   * @deprecated this is handled by the CommercialInvoiceService
   */
  private getVfdCode(): number {
    return parseInt(`${this.vendorIsSameAsPurchaser() ? 2 : 1}${this.hasAdditionalCost() ? 4 : 3}`);
  }

  /**
   * Assign treatment codes to lines
   *
   * @deprecated this is handled by the CommercialInvoiceService
   */
  private assignTreatmentCodesToLines(): void {
    const vfdCode = this.getVfdCode();

    this.targetEntity.commercialInvoiceLines.forEach((line) => {
      line.tt = this.getCanadaTreatmentCode(2);
      line.vfd = this.getCanadaVfdCode(vfdCode);
    });
  }

  private assignCountryOfOriginToProducts(): void {
    const globalCountryOfOrigin = this.targetEntity.countryOfExport;

    this.targetEntity.commercialInvoiceLines.forEach((line) => {
      if (!line.product.origin) {
        line.product.origin = globalCountryOfOrigin;
      }
      if (!line.origin) {
        line.origin = globalCountryOfOrigin;
      }
    });
  }

  private validateAmounts(): void {
    let total = Big(0);
    let relatedFields = ["total"];
    this.targetEntity.commercialInvoiceLines.forEach((line, index) => {
      total = total.plus(Big(line.totalLineValue));
      relatedFields.push(`commercialInvoiceLines.${index}.totalLineValue`);
    });
    // FIXME: use big.js or other decimal library
    if (!total.eq(this.validationContext.total)) {
      this.validationErrors.push({
        sourceField: relatedFields,
        targetField: null,
        message: `Sum of line value (${total}) does not match total (${this.validationContext.total}) found in document`
      });
    }
  }

  private validateTotals(): void {
    const sum = [
      this.validationContext.total,
      this.validationContext.discount.neg(),
      this.validationContext.tax,
      this.validationContext.transCost,
      this.validationContext.packCost,
      this.validationContext.miscCost
    ].reduce((acc, curr) => acc.plus(curr), Big(0));
    // FIXME: use big.js or other decimal library
    if (!sum.eq(this.validationContext.invoiceTotal)) {
      this.validationErrors.push({
        sourceField: ["total", "discount", "tax", "transCost", "packCost", "miscCost"],
        targetField: null,
        message: `Calculated invoice total (${sum}) does not match found in document (${this.validationContext.invoiceTotal})`
      });
    }
  }

  private validateHasLines(): void {
    if (
      this.targetEntity.commercialInvoiceLines === null ||
      this.targetEntity.commercialInvoiceLines === undefined ||
      this.targetEntity.commercialInvoiceLines.length === 0
    ) {
      this.addValidationError(["commercialInvoiceLines"], null, "Invoice must have at least one line");
    }
  }

  private hasCommercialInvoiceLines(): boolean {
    return (
      this.targetEntity.commercialInvoiceLines !== null &&
      this.targetEntity.commercialInvoiceLines !== undefined &&
      this.targetEntity.commercialInvoiceLines.length > 0
    );
  }

  protected postProcess(): void {
    // this.validateHasLines();

    // if (this.hasCommercialInvoiceLines()) {
    //   this.validateAmounts();
    //   this.validateTotals();

    //   this.assignVendorToProducts();
    //   this.assignCountryOfOriginToProducts();
    //   this.assignTreatmentCodesToLines();
    // }

    // If invoice number is not set, use PO number
    this.targetEntity.invoiceNumber = this.targetEntity.invoiceNumber ?? this.targetEntity.poNumber;
  }
}
