import { UnitOfMeasure } from "nest-modules";

type UOMCategory = "weightUOM" | "volumeUOM" | "quantityUOM";

export const UOM_CATEGORY: Record<UnitOfMeasure, UOMCategory | null> = {
  [UnitOfMeasure.MASS_IN_KILOGRAMS]: "weightUOM",
  [UnitOfMeasure.MASS_IN_GRAMS]: "weightUOM",
  [UnitOfMeasure.MASS_IN_CARATS]: "weightUOM",
  [UnitOfMeasure.MASS_IN_METRIC_TONNES]: "weightUOM",
  [UnitOfMeasure.MASS_IN_KILOGRAM_AIR_DRY]: "weightUOM",
  [UnitOfMeasure.MASS_IN_KILOGRAMS_OF_NAMED_SUBSTANCE]: "weightUOM",
  [UnitOfMeasure.MASS_IN_METRIC_TONNES_AIR_DRY]: "weightUOM",

  [UnitOfMeasure.NUMBER_OF_PACKAGES]: "quantityUOM",
  [UnitOfMeasure.NUMBER]: "quantityUOM",
  [UnitOfMeasure.NUMBER_OF_PAIRS]: "quantityUOM",
  [UnitOfMeasure.NUMBER_OF_DOZEN]: "quantityUOM",
  [UnitOfMeasure.GROSS_TWELVE_DOZEN]: "quantityUOM",

  [UnitOfMeasure.LIQUID_VOLUME_IN_LITRES]: "volumeUOM",
  [UnitOfMeasure.LIQUID_VOLUME_IN_HECTOLITRES]: "volumeUOM",
  [UnitOfMeasure.LIQUID_VOLUME_IN_LITRES_OF_PURE_ALCOHOL]: "volumeUOM",
  [UnitOfMeasure.VOLUME_IN_CUBIC_METERS]: "volumeUOM",

  [UnitOfMeasure.NUMBER_OF_SETS]: null,
  [UnitOfMeasure.ELECTRICAL_ENERGY_IN_MEGAWATT_HOURS]: null,
  [UnitOfMeasure.RADIOACTIVITY_IN_MEGABECQUERELS]: null,
  [UnitOfMeasure.AREA_IN_SQUARE_METERS]: null,
  [UnitOfMeasure.METERS]: null,

  // TODO: this might need to be adjusted
  [UnitOfMeasure.THOUSANDS]: null,
  [UnitOfMeasure.VOLUME_IN_THOUSANDS_OF_CUBIC_METERS]: null
};
