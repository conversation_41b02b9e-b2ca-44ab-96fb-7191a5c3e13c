import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpt<PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString, <PERSON><PERSON><PERSON>th, ValidateIf } from "class-validator";
import { ShipmentMode } from "nest-modules";

export class UpdateShipmentArrivalNoticeDto {
  @IsNotEmpty()
  @IsEnum(ShipmentMode)
  modeOfTransport: ShipmentMode;

  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  hblNumber: string;

  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  containerNumber: string;

  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  cargoControlNumber: string;

  @IsNotEmpty()
  @ValidateIf((o) => o.modeOfTransport === ShipmentMode.LAND)
  @IsString()
  @MinLength(1)
  portCode: string;

  @IsNotEmpty()
  @ValidateIf((o) => o.modeOfTransport === ShipmentMode.LAND)
  @IsString()
  @MinLength(1)
  portOfExit: string;

  @IsOptional()
  @IsString()
  @MinLength(1)
  pickupNumber?: string;
}
