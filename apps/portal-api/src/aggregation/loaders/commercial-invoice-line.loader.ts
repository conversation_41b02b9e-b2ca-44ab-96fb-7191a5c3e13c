import { CommercialInvoiceLine, QuantityUOM, UnitOfMeasure, VolumeUOM, WeightUOM } from "nest-modules";
import { parseHSCode, parseNumber, parseToQuantityUOM, parseToVolumeUOM, parseToWeightUOM } from "../parsers";
import { Aggregator, Computed, Constant, FromField, FromFieldOptions } from "./abstract.loader";

export class CommercialInvoiceLineWithUOM extends CommercialInvoiceLine {
  quantityUOM: QuantityUOM;
  weight: number;
  weightUOM: WeightUOM;
  volume: number;
  volumeUOM: VolumeUOM;
  netWeight: number;
  grossWeight: number;
}

export class CommercialInvoiceLineLoader extends Aggregator<CommercialInvoiceLineWithUOM> {
  protected readonly mapping: Partial<Record<keyof CommercialInvoiceLineWithUOM, FromFieldOptions>> = {
    goodsDescription: FromField("goodsDescription"),
    hsCode: FromField("hsCode", parseHSCode),
    // INTERMEDIATE FIELDS we use to calculate the final fields
    quantity: FromField("quantity", parseNumber),
    quantityUOM: FromField("quantityUOM", parseToQuantityUOM),
    weight: Computed((line) => parseNumber(line.netWeight ?? line.grossWeight)),
    weightUOM: FromField("weightUOM", parseToWeightUOM),
    volume: FromField("volume", parseNumber),
    volumeUOM: FromField("volumeUOM", parseToVolumeUOM),
    // END INTERMEDIATE FIELDS
    unitOfMeasure: Constant(UnitOfMeasure.NUMBER),
    unitPrice: FromField("unitPrice", parseNumber),
    totalLineValue: FromField("totalLineValue", parseNumber)
  };

  constructor(data: any) {
    super(data, CommercialInvoiceLineWithUOM);
  }
}
