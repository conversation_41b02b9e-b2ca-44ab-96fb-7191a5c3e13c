import { CreateLocationDto, Location, LocationType } from "nest-modules";
import { parseToEnum } from "../parsers/parser";
import { Aggregator, FromField, FromFieldOptions, FromFieldRelation } from "./abstract.loader";
import { CountryLoader } from "./country.loader";

export class LocationLoader extends Aggregator<Location> {
  parseToLocationType(value?: string): LocationType | null {
    if (!value) {
      return null;
    }

    return parseToEnum(LocationType)(value.toLowerCase());
  }

  protected readonly mapping: Partial<Record<keyof Location, FromFieldOptions>> = {
    city: FromField("city"),
    timezone: FromField("$TIMEZONE"),
    country: FromFieldRelation("country", CountryLoader)
  };

  constructor(data: any, defaultValue?: Partial<Location>) {
    super(data, Location, defaultValue);
  }

  postProcess(context?: string): void {
    if (!this.targetEntity.country) {
      this.addValidationError(["country"], null, "Country is required", context);
    }
    this.addValidation(CreateLocationDto, ["city"], context);
  }
}
