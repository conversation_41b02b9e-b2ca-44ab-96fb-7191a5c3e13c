import { Logger } from "@nestjs/common";
import { validate } from "class-validator";
import { FieldValidationErrors } from "nest-modules";

type FieldGetterType = "field" | "relation" | "context-relation" | "computed" | "constant";

export interface FromFieldOptions {
  type: FieldGetterType;
  fieldKey?: string;
  parser?: (value?: string, context?: any) => any;
  loader?: new (data: any, defaultValue?: any) => Aggregator<any>;
  isArray?: boolean;
  defaultValue?: any;
  dependencies?: string[];
}

export const FromField = (
  fieldKey: string,
  parser?: (value: string, context?: string) => any,
  defaultValue?: any
): FromFieldOptions => {
  return {
    type: "field",
    fieldKey,
    parser,
    defaultValue
  };
};

/**
 * Define a computed field
 *
 * @param parser
 * @param defaultValue
 * @returns
 */
export const Computed = (parser: (context: any) => any, defaultValue?: any): FromFieldOptions => {
  return {
    type: "computed",
    parser: (_: string, context: any) => parser(context),
    defaultValue
  };
};

export const Constant = (value: any): FromFieldOptions => {
  return {
    type: "constant",
    defaultValue: value
  };
};

export const FromFieldRelation = <T>(
  fieldKey: string,
  loader: new (data: any, defaultValue?: Partial<T>) => Aggregator<T>,
  isArray = false,
  defaultValue?: Partial<T>
): FromFieldOptions => {
  return {
    type: "relation",
    fieldKey,
    isArray,
    defaultValue,
    loader: loader
  };
};

export const ContextRelation = <T>(
  loader: new (data: any, defaultValue?: Partial<T>) => Aggregator<T>,
  isArray = false,
  defaultValue?: Partial<T>
): FromFieldOptions => {
  return {
    type: "context-relation",
    fieldKey: null,
    isArray,
    defaultValue,
    loader: loader
  };
};

export abstract class Aggregator<T> {
  /**
   * The entity to aggregate
   */
  protected readonly targetEntity: T;

  /**
   * The data to aggregate
   */
  protected readonly data: any;

  protected abstract readonly mapping: Partial<Record<keyof T, FromFieldOptions>>;

  protected readonly logger = new Logger(this.constructor.name);

  constructor(data: any, targetEntity: new () => T, defaultValue?: Partial<T>) {
    this.targetEntity = new targetEntity();

    if (defaultValue) {
      Object.entries(defaultValue).forEach(([key, value]) => {
        this.targetEntity[key] = value;
      });
    }

    this.data = data;
    this.root = this;
  }

  protected getFieldValue(fieldKey: string): string | undefined | any[] {
    return this.data[fieldKey];
  }

  protected getMapping(): Partial<Record<keyof T, FromFieldOptions>> {
    return this.mapping;
  }

  protected root: Aggregator<any>;

  protected getRoot(): Aggregator<any> {
    return this.root;
  }

  protected setRoot(root: Aggregator<any>): Aggregator<any> {
    this.root = root;
    return this;
  }

  private getForEntry(entry: [string, FromFieldOptions], originalKey?: string): any {
    const [key, options] = entry;
    this.logger.verbose("[" + key + "] => [" + options.fieldKey + "]");

    let sourceValue: string | undefined | any[];
    let targetValue: any;

    switch (options.type) {
      // assign constant value
      case "constant":
        targetValue = options.defaultValue;
        break;
      // pass computed value
      case "computed":
        targetValue = options.parser(null, this.data);
        break;
      case "context-relation":
        const loader = new options.loader(this.data, options.defaultValue);
        loader.setRoot(this.getRoot());
        targetValue = loader.get(originalKey);
        break;
      default:
        try {
          sourceValue = this.getFieldValue(options.fieldKey);
        } catch (error) {
          this.logger.warn("Field " + options.fieldKey + " not found");
          return;
        }

        if (sourceValue === null || sourceValue === undefined) {
          this.logger.debug("Source value for field " + options.fieldKey + " is null or undefined");
          // NOTE: we dont want to assign default values directly to relations
          if (!options.defaultValue || options.type === "relation") {
            return;
          }

          targetValue = options.defaultValue;
          this.logger.debug(`Using default value: ${JSON.stringify(targetValue)}`);
        } else {
          const nextKey = originalKey ? originalKey + "." + options.fieldKey : options.fieldKey;

          let parser: (value: string, key?: any) => any;

          if (options.type === "relation") {
            parser = (value: string, key?: any) =>
              new options.loader(value, options.defaultValue).setRoot(this.getRoot()).get(key);
          } else {
            parser = options.parser ?? ((value: string, _: any) => value);
          }

          // get value
          if (options.isArray) {
            targetValue = (sourceValue as any[]).map((item: any, index: number) =>
              parser(item, nextKey + "." + index)
            );
          } else {
            targetValue = parser(sourceValue as string, nextKey);
          }
        }
    }

    // this.logger.verbose(
    //   JSON.stringify(sourceValue) + " => " + JSON.stringify(targetValue) + " (" + targetValue + ")"
    // );

    // if the value is null, do not set it
    if (targetValue === null) return;

    this.targetEntity[key] = targetValue;
  }

  protected preProcess(context?: string): void {
    // do nothing
  }

  protected postProcess(context?: string): void {
    // do nothing
  }

  get(key?: string): T {
    // this.logger.debug("Source: " + JSON.stringify(this.data));
    this.preProcess(key);
    // if no data, return null
    if (this.data === null || this.data === undefined) return null;

    // if the data is an object, and all values are null, return null
    if (typeof this.data === "object") {
      if (
        Object.entries(this.data)
          .map(([key, value]) => value === null)
          .every(Boolean)
      ) {
        return null;
      }
    }

    Object.entries(this.getMapping()).forEach((entry: [string, FromFieldOptions]) =>
      this.getForEntry(entry, key)
    );
    this.postProcess(key);
    // this.logger.debug("Mapped: " + JSON.stringify(this.targetEntity));
    return this.targetEntity;
  }

  /**
   * Get the database record
   * @returns The database record or undefined if the record is not found
   */
  protected databaseRecord(): T | undefined {
    return this.targetEntity;
  }

  // pending validations
  protected validationErrors: FieldValidationErrors = [];
  protected pendingValidations: Promise<void>[] = [];

  async getValidationErrors(): Promise<FieldValidationErrors> {
    await Promise.all(this.getRoot().pendingValidations);
    this.logger.debug("Validation errors: " + JSON.stringify(this.validationErrors));
    return this.validationErrors;
  }

  private findSourceField(targetFieldName: string): string | null {
    const options: FromFieldOptions = this.getMapping()[targetFieldName];

    if (options === null || options === undefined) {
      return null;
    }

    return options.fieldKey;
  }

  protected addValidationError(
    sourceField: string[] | string,
    targetField: string | null,
    message: string,
    context?: string
  ) {
    let fullSourceField: string[] | string = sourceField;

    if (context) {
      if (typeof sourceField === "string") {
        fullSourceField = [context, sourceField].join(".");
      } else if (Array.isArray(sourceField)) {
        fullSourceField = sourceField.map((field) => [context, field].join("."));
      }
    }

    this.getRoot().validationErrors.push({
      sourceField: fullSourceField,
      targetField,
      message
    });
  }

  protected addValidation<D extends object>(
    dtoClass: new () => D,
    fields: (keyof D & keyof T)[],
    srcParentField?: string
  ) {
    this.logger.debug("Adding validation for fields " + fields.join(", ") + " in context " + srcParentField);
    this.getRoot().pendingValidations.push(
      new Promise(async (resolve, reject) => {
        try {
          const dto = new dtoClass();
          fields.forEach((field) => {
            (dto as any)[field] = (this.targetEntity as any)[field];
          });
          const validationErrors = await validate(dto, {
            validationError: { target: false }
          });

          if (validationErrors.length > 0) {
            validationErrors.forEach((errors) => {
              const sourceField = [srcParentField, this.findSourceField(errors.property)]
                .filter((field) => field !== null && field !== undefined && field.length > 0)
                .join(".");

              if (!fields.includes(errors.property as keyof D & keyof T)) return;

              Object.entries(errors.constraints).forEach(([_, constraint]) => {
                this.logger.verbose("Adding validation error: " + errors.property + " => " + constraint);
                this.addValidationError(sourceField, errors.property, constraint);
              });
            });
          }
          resolve();
        } catch (error) {
          reject(error);
        }
      })
    );
  }
}
