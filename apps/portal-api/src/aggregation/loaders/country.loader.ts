import { Country } from "nest-modules/entities";
import { Aggregator, FromFieldOptions } from "./abstract.loader";

/**
 * CountryLoader
 *
 * Loads a country from a string
 *
 * We simply wrap the string in a country object
 */
export class CountryLoader extends Aggregator<Country> {
  protected mapping: Partial<Record<keyof Country, FromFieldOptions>> = {};

  constructor(data: any) {
    super(data, Country);
  }

  get(key?: string): Country {
    if (!this.data) return null;
    this.targetEntity.name = this.data;
    return this.targetEntity;
  }
}
