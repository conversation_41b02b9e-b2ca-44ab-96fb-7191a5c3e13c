import { TradePartner } from "nest-modules";
import { Aggregator, FromField, FromFieldOptions } from "./abstract.loader";

export class TradePartnerLoader extends Aggregator<TradePartner> {
  /**
   * Parse a phone number to E.164 format
   * @param phoneNumber
   * @returns
   */
  private parseToE164(phoneNumber: string) {
    let cleaned = phoneNumber.replace(/[^+|\d]/g, "");
    // if cleaned doesn't start with +, add it
    if (!cleaned.startsWith("+")) {
      cleaned = "+" + cleaned;
    }
    return cleaned;
  }

  private parseEmail(email: string) {
    // handle null, na, n/a
    if (["null", "na", "n/a"].includes(email)) {
      return null;
    }

    // if multiple emails are present, return the first one
    if (email.includes(",")) {
      return email.split(",")[0];
    }

    return email;
  }
  /**
   * Mapping for the trade partner
   *
   * NOTE: the partnerType is set in the context of the parent loader
   */
  protected readonly mapping: Record<string, FromFieldOptions> = {
    vendorCode: FromField("vendorCode"),
    name: From<PERSON><PERSON>("name"),
    email: FromField("email", this.parseEmail),
    address: FromField("street"),
    city: FromField("city"),
    postalCode: FromField("postalCode"),
    state: FromField("state"),
    // use derived fields
    phoneNumber: FromField("phoneNumber"),
    country: FromField("country")
  };

  constructor(data: any, defaultValue?: Partial<TradePartner>) {
    super(data, TradePartner, defaultValue);
  }
}
