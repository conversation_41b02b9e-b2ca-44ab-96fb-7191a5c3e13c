import { parseDate, parseHSCode } from "../parsers/parser";
import { Aggregator, FromField, FromFieldOptions, FromFieldRelation } from "./abstract.loader";
import { CountryLoader } from "./country.loader";
import { CertificateOfOriginLineDto, CertificateOfOriginWithLines } from "./dto/certificate-of-origin.dto";

export class CertificateOfOriginLineLoader extends Aggregator<CertificateOfOriginLineDto> {
  constructor(data: any) {
    super(data, CertificateOfOriginLineDto);
  }

  protected mapping: Partial<Record<keyof CertificateOfOriginLineDto, FromFieldOptions>> = {
    hsCode: FromField("hsCode", parseHSCode),
    descriptionOfGoods: FromField("descriptionOfGoods")
  };
}

export class CertificateOfOriginLoader extends Aggregator<CertificateOfOriginWithLines> {
  protected mapping: Partial<Record<keyof CertificateOfOriginWithLines, FromFieldOptions>> = {
    validFrom: FromField("validFrom", parseDate),
    validTo: FromField("validTo", parseDate),
    countryOfOrigin: FromFieldRelation("countryOfOrigin", CountryLoader, false)
  };

  constructor(data: any) {
    super(data, CertificateOfOriginWithLines);
  }
}
