import { FileBatchService } from "@/document/services/file-batch.service";
import { DocumentType } from "@/document/types/document-types";
import { BadRequestException, Injectable, Logger, NotFoundException } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import {
  AggregationTargetType,
  CustomsStatus,
  Document,
  DocumentAggregation,
  DocumentAggregationAction,
  DocumentAggregationStatus,
  DocumentField,
  DocumentStatus,
  Shipment,
  UserPermission
} from "nest-modules";
import { ClsService } from "nestjs-cls";
import { DataSource, In, Repository } from "typeorm";
import { FieldsToObjectAdapter } from "./adapter/fields-to-object.adapter";
import { DocumentAggregationActionMap, skippedDocumentTypes } from "./constants/aggregation-actions";
import { AggregationCreatedEvent, AggregationEvents } from "./events/";
import { AggregationExecuter } from "./executer/aggregation.executer";
import { AggregationDispatcher } from "./queues/aggregation.dispatcher";
import { AggregationResult } from "./types";

@Injectable()
export class AggregationService {
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
    @InjectRepository(DocumentAggregation)
    private readonly documentAggregationRepository: Repository<DocumentAggregation>,
    private readonly aggregationExecuter: AggregationExecuter,
    private readonly aggregationDispatcher: AggregationDispatcher,
    private readonly fileBatchService: FileBatchService,
    private readonly eventEmitter: EventEmitter2,
    private readonly cls: ClsService,
    @InjectRepository(Shipment)
    private readonly shipmentRepository: Repository<Shipment>
  ) {}

  private readonly logger = new Logger(AggregationService.name);

  private fieldsToObject(fields: DocumentField[]): any {
    return new FieldsToObjectAdapter(fields).toObject();
  }

  /**
   * Get document aggregation by id
   *
   * @param id - The id of the document aggregation
   * @returns The document aggregation
   */
  async getDocumentAggregation(id: number) {
    const where: any = { id };

    if (
      this.cls.has("ORGANIZATION_ID") &&
      this.cls.get("USER_PERMISSION") !== UserPermission.BACKOFFICE_ADMIN
    ) {
      where.organization = {
        id: this.cls.get("ORGANIZATION_ID")
      };
    }

    // check if exists
    const exists = await this.documentAggregationRepository.exists({ where });
    if (!exists) {
      throw new NotFoundException("Document aggregation not found");
    }

    return this.documentAggregationRepository.findOne({
      where,
      relations: {
        organization: true,
        shipment: true,
        documents: {
          fields: true,
          file: true,
          shipment: true
        },
        batch: true,
        steps: {
          logs: true
        },
        dependsOn: true
      },
      order: {
        steps: {
          id: "ASC",
          logs: {
            id: "ASC"
          }
        }
      }
    });
  }

  /**
   * Creates a new document aggregation.
   *
   * @todo deprecate data
   * @param document - The document to create the aggregation for.
   * @param data - The data to set to the aggregation.
   * @returns The created document aggregation.
   */
  private async createDocumentAggregation(
    documents: Document[],
    action: DocumentAggregationAction,
    batchId?: string,
    shipmentId?: number,
    dependsOnId?: number
  ) {
    if (documents.length === 0) {
      throw new BadRequestException("No documents provided");
    }

    // check document status, if any document is not extracted, throw error
    const notExtracted = documents.some((d) => d.status !== DocumentStatus.EXTRACTED);

    if (notExtracted) {
      throw new BadRequestException("Some documents are not extracted");
    }

    // TODO: check if this is needed
    // New guard to skip duplicates for the same batch and document
    if (batchId) {
      const existing = await this.documentAggregationRepository
        .createQueryBuilder("agg")
        .leftJoin("agg.documents", "doc")
        .where("agg.batchId = :batchId", { batchId })
        .andWhere("agg.action = :action", { action })
        .andWhere("doc.id = :docId", { docId: documents[0].id })
        .getOne();
      if (existing) {
        return this.getDocumentAggregation(existing.id);
      }
    }

    const documentAggregation = new DocumentAggregation();
    documentAggregation.status = DocumentAggregationStatus.PENDING;
    documentAggregation.dependsOnId = dependsOnId;

    documentAggregation.organization = documents[0].organization;
    documentAggregation.action = action;
    documentAggregation.batchId = batchId;
    documentAggregation.shipment = { id: shipmentId } as any;

    // set shipment id and type if the action is CREATE_SHIPMENT
    if (action === DocumentAggregationAction.CREATE_SHIPMENT) {
      documentAggregation.targetId = shipmentId;
      documentAggregation.targetType = AggregationTargetType.SHIPMENT;
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let aggregation: DocumentAggregation;

    try {
      const documentAggregationRepository = queryRunner.manager.getRepository(DocumentAggregation);
      const documentRepository = queryRunner.manager.getRepository(Document);

      aggregation = await documentAggregationRepository.save(documentAggregation);

      await documentRepository.update(
        documents.map((d) => d.id),
        {
          aggregation: { id: aggregation.id }
        }
      );

      await queryRunner.commitTransaction();
      return this.getDocumentAggregation(aggregation.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  public async setDocumentStatus(document: Document, status: DocumentStatus) {
    await this.documentRepository.update(document.id, {
      status: status
    });
  }

  public async abortAggregation(aggregationId: number) {
    const aggregation = await this.getDocumentAggregation(aggregationId);

    if (
      aggregation.status !== DocumentAggregationStatus.PROCESSING &&
      aggregation.status !== DocumentAggregationStatus.PENDING
    ) {
      throw new BadRequestException("Aggregation is not processing or pending");
    }

    // get the job id
    const jobId = aggregation.jobId;

    console.log("jobId", jobId);

    if (jobId) {
      // abort the job if it is running or pending
      if (await this.aggregationDispatcher.jobIsRunningOrPending(jobId)) {
        await this.aggregationDispatcher.abortJob(jobId);
      }
    }

    await this.setAggregationStatus(aggregation, DocumentAggregationStatus.FAILED);
  }

  /**
   * Get the aggregation action by document name
   *
   * @param name - The name of the document
   * @returns The aggregation action
   */
  private getAggregationAction(name: string): DocumentAggregationAction | null {
    if (skippedDocumentTypes.includes(name as any)) {
      return null;
    }

    if (name === "unknown" || name === "other") {
      return null;
    }

    for (const [action, documentNames] of Object.entries(DocumentAggregationActionMap)) {
      if (documentNames.includes(name)) {
        return action as DocumentAggregationAction;
      }
    }

    throw new BadRequestException(`Unknown document name: ${name} for aggregation action`);
  }

  /**
   * Get the aggregation by document id.
   *
   * @param documentId - The id of the document.
   * @returns The aggregation.
   */
  async getAggregationByDocumentId(documentId: number): Promise<DocumentAggregation> {
    const aggregation = await this._getAggregationByDocumentId(documentId);

    if (!aggregation) {
      throw new NotFoundException("Aggregation not found");
    }

    return aggregation;
  }

  private async _getAggregationByDocumentId(documentId: number) {
    return await this.documentAggregationRepository.findOne({
      relations: {
        documents: {
          file: true,
          fields: true
        },
        shipment: true
      },
      where: { documents: { id: documentId } }
    });
  }

  public async setAggregationStatus(aggregation: DocumentAggregation, status: DocumentAggregationStatus) {
    return this.documentAggregationRepository.update(aggregation.id, {
      status: status
    });
  }

  /**
   * Apply the aggregation
   *
   * @param aggregationId - The id of the aggregation
   * @returns The aggregation result
   */
  async applyAggregation(aggregationId: number, retry: boolean = false) {
    const aggregation = await this.getDocumentAggregation(aggregationId);

    switch (aggregation.status) {
      case DocumentAggregationStatus.PROCESSING:
        return {
          success: false,
          action: aggregation.action,
          error: "aggregation in progress",
          documentId: aggregation?.documents[0]?.id,
          documentName: aggregation?.documents[0]?.name,
          shipmentId: aggregation?.shipment?.id
        };

      case DocumentAggregationStatus.SUCCESS:
        if (!retry) {
          this.logger.log("Document is already aggregated, skipping, set retry to true to re-run", {
            aggregationId: aggregation.id,
            documentId: aggregation.documents[0]?.id,
            documentName: aggregation.documents[0]?.name,
            shipmentId: aggregation.shipment?.id
          });
          return {
            success: false,
            action: aggregation.action,
            error: "skipped, document already aggregated",
            documentId: aggregation.documents[0]?.id,
            documentName: aggregation.documents[0]?.name,
            shipmentId: aggregation.shipment?.id
          };
        }
    }

    if (aggregation.dependsOn && aggregation.dependsOn.status !== DocumentAggregationStatus.SUCCESS) {
      return {
        success: false,
        action: aggregation.action,
        error: "skipped, dependency not aggregated",
        documentId: aggregation.documents[0]?.id,
        documentName: aggregation.documents[0]?.name,
        shipmentId: aggregation.shipment?.id
      };
    }

    const status = [
      CustomsStatus.ENTRY_SUBMITTED,
      CustomsStatus.ENTRY_ACCEPTED,
      CustomsStatus.EXAM,
      CustomsStatus.RELEASED,
      CustomsStatus.ACCOUNTING_COMPLETED
    ];

    const isSubmitted = aggregation.documents.some((document) =>
      status.includes(document.shipment?.customsStatus as CustomsStatus)
    );

    // check if the aggregation's documents's shipment status is entry submitted, if so, we are not allowed to aggregate
    if (isSubmitted && this.cls.get("USER_PERMISSION") !== UserPermission.BACKOFFICE_ADMIN) {
      return {
        success: false,
        action: aggregation.action,
        error: "Shipment is already submitted"
      };
    }

    await this.setAggregationStatus(aggregation, DocumentAggregationStatus.PROCESSING);

    const job = await this.aggregationDispatcher.dispatchAggregationTask(
      aggregation.id,
      aggregation.organizationId,
      aggregation.documents[0]?.file?.batchId,
      retry
    );

    await this.setJobId(aggregation.id, job.id);

    return {
      success: true,
      job
    };
  }

  async setJobId(aggregationId: number, jobId: string) {
    await this.documentAggregationRepository.update(aggregationId, {
      jobId: jobId
    });
  }

  /**
   * @deprecated use applyAggregation instead
   * @param aggregationId
   * @returns
   */
  async applyAggregationSync(aggregationId: number) {
    const aggregation = await this.getDocumentAggregation(aggregationId);

    switch (aggregation.status) {
      case DocumentAggregationStatus.PROCESSING:
        return {
          success: false,
          action: aggregation.action,
          error: "aggregation in progress",
          documentId: aggregation.documents[0].id,
          documentName: aggregation.documents[0].name,
          shipmentId: aggregation.shipment?.id
        };

      case DocumentAggregationStatus.SUCCESS:
        this.logger.log("(sync) Document is already aggregated, skipping", {
          aggregationId: aggregation.id,
          documentId: aggregation.documents[0].id,
          documentName: aggregation.documents[0].name,
          shipmentId: aggregation.shipment?.id
        });
        return {
          success: false,
          action: aggregation.action,
          error: "skipped, document already aggregated",
          documentId: aggregation.documents[0].id,
          documentName: aggregation.documents[0].name,
          shipmentId: aggregation.shipment?.id
        };
    }

    await this.setAggregationStatus(aggregation, DocumentAggregationStatus.PROCESSING);

    return await this.cls.run(async () => {
      this.cls.set("ORGANIZATION_ID", aggregation.organizationId);
      const { hasError, error } = await this.aggregationExecuter.execute(aggregation);
      if (!hasError) {
        await this.documentAggregationRepository.update(aggregation.id, {
          status: DocumentAggregationStatus.SUCCESS
        });

        aggregation.documents.forEach((document) => {
          // this.setDocumentStatus(document, DocumentStatus.AGGREGATED);
          // this.eventEmitter.emit(
          //   DOCUMENT_AGGREGATION_SUCCESS_EVENT,
          //   new DocumentAggregationSuccessEvent(
          //     aggregation.id,
          //     aggregation.organizationId,
          //     document.id,
          //     document.file.batchId,
          //     aggregation.shipment?.id
          //   )
          // );
        });
      } else {
        aggregation.documents.forEach((document) => {
          // this.eventEmitter.emit(
          //   DOCUMENT_AGGREGATION_FAILED_EVENT,
          //   new DocumentAggregationFailedEvent(
          //     aggregation.id,
          //     aggregation.organizationId,
          //     document.id,
          //     document.file.batchId
          //   )
          // );
        });

        await this.setAggregationStatus(aggregation, DocumentAggregationStatus.FAILED);
      }

      return {
        success: !hasError,
        action: aggregation.action,
        error: error,
        documentId: aggregation.documents[0].id,
        documentName: aggregation.documents[0].name,
        shipmentId: aggregation.shipment?.id
      };
    });
  }
  /**
   * Get available actions for a list of documents
   *
   * @param payload - The payload containing the document IDs
   * @returns The available actions
   */
  async getAvailableActions(payload: { documentIds: number[] }) {
    const documents = await this.documentRepository.findBy({
      id: In(payload.documentIds)
    });

    const shipmentDocumentTypes = [
      // ocean
      DocumentType.HOUSE_OCEAN_BILL_OF_LADING,
      DocumentType.OCEAN_E_MANIFEST,
      DocumentType.OCEAN_ARRIVAL_NOTICE,
      // air
      DocumentType.AIR_WAYBILL,
      DocumentType.AIR_ARRIVAL_NOTICE,
      DocumentType.AIR_E_MANIFEST,
      // land
      DocumentType.ROAD_ARRIVAL_NOTICE,
      DocumentType.ROAD_BILL_OF_LADING,
      DocumentType.PARS_OVERLAY
      // CI_PL
      // DocumentType.COMMERCIAL_INVOICE
    ];

    const actions: { documentIds: number[]; action: DocumentAggregationAction }[] = [];

    const createShipmentAction = {
      documentIds: documents
        .filter((d) => shipmentDocumentTypes.includes(d.name as DocumentType))
        .map((d) => d.id),
      action: DocumentAggregationAction.CREATE_SHIPMENT
    };

    if (createShipmentAction.documentIds.length > 0) {
      actions.push(createShipmentAction);
    }

    documents
      .filter((d) => d.name === DocumentType.COMMERCIAL_INVOICE)
      .forEach((d) => {
        actions.push({
          documentIds: [d.id],
          action: DocumentAggregationAction.CREATE_COMMERCIAL_INVOICE
        });
      });

    const certificateOfOriginDocumentTypes = [
      DocumentType.CERTIFICATE_OF_ORIGIN,
      DocumentType.USMCA_CERTIFICATE_OF_ORIGIN
    ];

    documents
      .filter((d) => certificateOfOriginDocumentTypes.includes(d.name as DocumentType))
      .forEach((d) => {
        actions.push({
          documentIds: [d.id],
          action: DocumentAggregationAction.CREATE_CERTIFICATE_OF_ORIGIN
        });
      });

    return actions;
  }

  /**
   * Aggregate a list of documents
   *
   * @param documentIds - The ids of the documents
   * @param batchId - The id of the batch
   * @returns The aggregations
   * @deprecated use aggregateDocumentsAsync instead
   */
  async aggregateDocuments(documentIds: number[], batchId?: string): Promise<AggregationResult[]> {
    const actions = await this.getAvailableActions({ documentIds });

    const promises = actions.map(async (action) => {
      const documents = await this.documentRepository.find({
        where: { id: In(action.documentIds) },
        relations: {
          fields: true,
          organization: true,
          shipment: true
        }
      });

      const documentAggregation = await this.createDocumentAggregation(documents, action.action);

      return documentAggregation;
    });

    const aggregations = await Promise.all(promises);

    // run create shipment first
    const createShipmentAction = aggregations.find(
      (action) => action.action === DocumentAggregationAction.CREATE_SHIPMENT
    );

    let createShipmentResult;

    if (createShipmentAction) {
      createShipmentResult = await this.applyAggregationSync(createShipmentAction.id);
      this.fileBatchService.setBatchShipmentId(batchId, createShipmentResult.shipmentId);
    }

    const updateShipmentAction = aggregations.find(
      (action) => action.action === DocumentAggregationAction.UPDATE_SHIPMENT
    );

    let updateShipmentResult;

    if (updateShipmentAction) {
      updateShipmentResult = await this.applyAggregationSync(updateShipmentAction.id);
    }

    // for each aggregation, execute the aggregation
    const results = await Promise.all(
      aggregations
        .filter(
          (aggregation) =>
            aggregation.action !== DocumentAggregationAction.CREATE_SHIPMENT &&
            aggregation.action !== DocumentAggregationAction.UPDATE_SHIPMENT
        )
        .map((aggregation) => this.applyAggregationSync(aggregation.id))
    );

    if (createShipmentResult) {
      results.push(createShipmentResult);
    }

    if (updateShipmentResult) {
      results.push(updateShipmentResult);
    }

    return results;
  }

  private async getDocumentAggregationByPayload(
    action: DocumentAggregationAction,
    documentIds: number[],
    batchId: string
  ) {
    const aggregation = await this.documentAggregationRepository.findOne({
      where: {
        action: action,
        documents: { id: In(documentIds) },
        batch: { id: batchId }
      }
    });

    return aggregation;
  }

  private async actionToAggregation(
    action: DocumentAggregationAction,
    documentIds: number[],
    batchId: string,
    shipmentId?: number,
    dependsOnId?: number
  ) {
    const aggregation = await this.getDocumentAggregationByPayload(action, documentIds, batchId);

    if (aggregation) {
      return aggregation;
    }

    const documents = await this.documentRepository.find({
      where: { id: In(documentIds) },
      relations: {
        fields: true,
        organization: true,
        shipment: true
      }
    });
    const documentAggregation = await this.createDocumentAggregation(
      documents,
      action,
      batchId,
      shipmentId,
      dependsOnId
    );

    this.eventEmitter.emit(
      AggregationEvents.CREATED,
      new AggregationCreatedEvent({
        id: documentAggregation.id,
        documents: documents.map((d) => ({ id: d.id, name: d.name })),
        action: action,
        batchId: batchId,
        organizationId: documents[0].organizationId,
        shipmentId: shipmentId,
        documentId: documents[0].id,
        documentName: documents[0].name
      })
    );

    return documentAggregation;
  }

  /**
   * Aggregate a list of documents
   *
   * @param documentIds - The ids of the documents
   * @param batchId - The id of the batch
   * @returns The aggregations
   */
  async aggregateDocumentsAsync(
    documentIds: number[],
    batchId?: string,
    shipmentId?: number
  ): Promise<DocumentAggregation[]> {
    // check if document are inside the same batch
    const documents = await this.documentRepository.find({
      where: { id: In(documentIds) },
      relations: {
        file: {
          batch: true
        }
      }
    });

    const batchIds = new Set(documents.map((d) => d.file.batchId));

    if (batchIds.size > 1) {
      throw new BadRequestException("Documents are not in the same batch");
    }

    // set batchId ans shipmentId if no default values are provided
    batchId = batchId ?? documents[0].file.batchId;
    shipmentId = shipmentId ?? documents[0].file.batch.shipmentId;

    const actions = await this.getAvailableActions({ documentIds });

    let previousAction: DocumentAggregation | null = null;

    let allAggregations: DocumentAggregation[] = [];

    // get shipment action
    const shipmentAction = actions.find(
      (action) => action.action === DocumentAggregationAction.CREATE_SHIPMENT
    );

    if (shipmentAction) {
      const shipmentAggregation = await this.actionToAggregation(
        shipmentAction.action,
        shipmentAction.documentIds,
        batchId,
        shipmentId
      );

      allAggregations.push(shipmentAggregation);

      previousAction = shipmentAggregation;
    }

    for (const action of actions) {
      if (action.action === DocumentAggregationAction.CREATE_SHIPMENT) {
        continue;
      }

      // ONLY CREATE SHIPMENT ACTION CAN HAVE DEPENDENCIES
      const aggregation = await this.actionToAggregation(
        action.action,
        action.documentIds,
        batchId,
        shipmentId,
        previousAction?.id
      );

      allAggregations.push(aggregation);
    }

    const promises = allAggregations.map((aggregation) => {
      return this.applyAggregation(aggregation.id, true);
    });

    const results = await Promise.all(promises);
    console.log(results);

    return allAggregations;
  }

  async getAggregationByTarget(
    targetId: number,
    targetType: AggregationTargetType
  ): Promise<DocumentAggregation | null> {
    return this.documentAggregationRepository.findOne({
      where: {
        targetId: targetId,
        targetType: targetType
      },
      relations: {
        documents: true
      }
    });
  }
}
