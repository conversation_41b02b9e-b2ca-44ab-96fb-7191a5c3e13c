import { CommercialInvoiceService } from "@/commercial-invoice/commercial-invoice.service";
import { TradePartnerService } from "@/trade-partner/trade-partner.service";
import { Injectable } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { plainToInstance } from "class-transformer";
import { EditCommercialInvoiceDto, TradePartner } from "nest-modules";
import { DataSource, QueryRunner } from "typeorm";
import { CountryMatcher, TradePartnerMatcher } from "../matchers";

@Injectable()
export class UpdateCommercialInvoiceExecuter {
  constructor(
    private readonly commercialInvoiceService: CommercialInvoiceService,
    private readonly countryMatcher: CountryMatcher,
    private readonly tradePartnerMatcher: TradePartnerMatcher,
    private readonly tradePartnerService: TradePartnerService,
    @InjectDataSource()
    private dataSource: DataSource
  ) {}

  async execute(invoiceId: number, context: any) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const dto = plainToInstance(EditCommercialInvoiceDto, context);
      const tradePartnerIds: Partial<EditCommercialInvoiceDto> = {
        vendorId: await this.matchOrCreateTradePartner(context.vendor, queryRunner),
        shipToId: await this.matchOrCreateTradePartner(context.shipTo, queryRunner),
        purchaserId: await this.matchOrCreateTradePartner(context.purchaser, queryRunner)
      };

      if (context.countryOfExport) {
        const countryOfExport = await this.countryMatcher.getCountryEntity(context.countryOfExport);
        if (countryOfExport) {
          dto.countryOfExportId = countryOfExport.id;
        }
      }

      for (const [key, value] of Object.entries(tradePartnerIds)) {
        dto[key] = value;
      }

      const commercialInvoice = await this.commercialInvoiceService.editCommercialInvoice(
        invoiceId,
        dto,
        queryRunner
      );
      await queryRunner.commitTransaction();
      return commercialInvoice;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async matchOrCreateTradePartner(data: any, queryRunner: QueryRunner) {
    let tradePartner: TradePartner;
    let tradePartnerMatchResult = await this.tradePartnerMatcher.matchTradePartner(data, queryRunner);
    if (tradePartnerMatchResult.entity) {
      tradePartner = tradePartnerMatchResult.entity;
    }
    let country = await this.countryMatcher.getCountryEntity(data.country);
    if (!country) {
      throw new Error("Country not found");
    }
    tradePartner = await this.tradePartnerService.createTradePartner(
      {
        ...data,
        countryId: country.id
      },
      queryRunner
    );
    return tradePartner.id;
  }
}
