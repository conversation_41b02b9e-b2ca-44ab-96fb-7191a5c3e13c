import { LocationService } from "@/location/location.service";
import { ShipmentService } from "@/shipment/services/shipment.service";
import { TradePartnerService } from "@/trade-partner/trade-partner.service";
import { Injectable } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { plainToInstance } from "class-transformer";
import { EditShipmentDto, Location, TradePartner } from "nest-modules";
import { DataSource, QueryRunner } from "typeorm";
import { CountryMatcher, LocationMatcher, TradePartnerMatcher } from "../matchers";
import { alphaNumericOnly } from "../parsers";
@Injectable()
export class UpdateShipmentExecuter {
  constructor(
    private readonly shipmentService: ShipmentService,
    private readonly tradePartnerService: TradePartnerService,
    private readonly locationService: LocationService,
    private readonly locationMatcher: LocationMatcher,
    private readonly tradePartnerMatcher: TradePartnerMatcher,
    private readonly countryMatcher: CountryMatcher,
    @InjectDataSource()
    private dataSource: DataSource
  ) {}

  async execute(shipmentId: number, context: any) {
    try {
      return await this._execute(shipmentId, context);
    } catch (error) {
      throw error;
    }
  }

  async _execute(shipmentId: number, context: any) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const dto = plainToInstance(EditShipmentDto, context);

      // transform data
      dto.hblNumber = alphaNumericOnly(dto.hblNumber);
      dto.containerNumber = dto.containerNumber ? alphaNumericOnly(dto.containerNumber) : null;
      dto.cargoControlNumber = dto.cargoControlNumber ? alphaNumericOnly(dto.cargoControlNumber) : null;

      const tradePartnerIds: Partial<EditShipmentDto> = {
        shipperId: await this.matchOrCreateTradePartner(context.shipper, queryRunner),
        consigneeId: await this.matchOrCreateTradePartner(context.consignee, queryRunner),
        forwarderId: await this.matchOrCreateTradePartner(context.notifyParty, queryRunner)
      };

      const locationIds: Partial<EditShipmentDto> = {
        portOfLoadingId: await this.matchOrCreateLocation(context.origin, queryRunner),
        portOfDischargeId: await this.matchOrCreateLocation(context.destination, queryRunner),
        placeOfDeliveryId: await this.matchOrCreateLocation(context.placeOfDelivery, queryRunner)
      };

      for (const [key, value] of Object.entries(tradePartnerIds)) {
        dto[key] = value;
      }

      for (const [key, value] of Object.entries(locationIds)) {
        dto[key] = value;
      }

      const shipment = await this.shipmentService.editShipment(shipmentId, dto, queryRunner);
      await queryRunner.commitTransaction();
      return shipment;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async matchOrCreateTradePartner(data: any, queryRunner: QueryRunner) {
    let tradePartner: TradePartner;
    let tradePartnerMatchResult = await this.tradePartnerMatcher.matchTradePartner(data, queryRunner);
    if (tradePartnerMatchResult.entity) {
      tradePartner = tradePartnerMatchResult.entity;
    }
    let country = await this.countryMatcher.getCountryEntity(data.country);
    if (!country) {
      throw new Error("Country not found");
    }
    tradePartner = await this.tradePartnerService.createTradePartner(
      {
        ...data,
        countryId: country.id
      },
      queryRunner
    );
    return tradePartner.id;
  }

  async matchOrCreateLocation(data: any, queryRunner: QueryRunner) {
    let location: Location;
    let locationMatchResult = await this.locationMatcher.fromLocationDetails(data, queryRunner);
    if (locationMatchResult.entity) {
      location = locationMatchResult.entity;
    }
    let country = await this.countryMatcher.getCountryEntity(data.country);
    if (!country) {
      throw new Error("Country not found");
    }
    location = await this.locationService.createLocation(
      {
        ...data,
        countryId: country.id
      },
      queryRunner
    );
    return location.id;
  }
}
