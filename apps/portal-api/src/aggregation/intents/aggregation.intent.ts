import { v4 as uuidv4 } from "uuid";
import { SupportedAggregationIntent } from "../types";

export interface AggregationIntentContext {
  batchId: string;
  organizationId: number;
}

export class AggregationIntent {
  /**
   * The id of the intent.
   */
  id: string = uuidv4();

  /**
   * The intent of the aggregation.
   */
  intent: SupportedAggregationIntent;

  /**
   * The shipment id.
   */
  shipmentId?: number;

  /**
   * The document ids to aggregate.
   */
  documentIds: number[];

  /**
   * The context of the aggregation.
   */
  context?: Record<string, any>;
}
