import { CommercialInvoiceModule } from "@/commercial-invoice/commercial-invoice.module";
import { DocumentModule } from "@/document/document.module";
import { LocationModule } from "@/location/location.module";
import { ProductModule } from "@/product/product.module";
import { ShipmentModule } from "@/shipment/shipment.module";
import { TradePartnerModule } from "@/trade-partner/trade-partner.module";
import { BullModule } from "@nestjs/bullmq";
import { DynamicModule, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  CanadaTariff,
  CertificateOfOrigin,
  CommercialInvoice,
  CommercialInvoiceLine,
  Country,
  Document,
  DocumentAggregation,
  DocumentAggregationStep,
  DocumentAggregationStepLog,
  DocumentAggregationTradePartner,
  Location,
  Product,
  Shipment,
  TradePartner
} from "nest-modules";
import { AggregationIntentService } from "./aggregation-intent.service";
import { DocumentAggregationController } from "./aggregation.controller";
import { AggregationService } from "./aggregation.service";
import { AggregationExecuter } from "./executer/aggregation.executer";
import { PartNoGenerator } from "./generator/part-no.generator";
import { listeners } from "./listeners";
import {
  CanadaTariffMatcher,
  CommercialInvoiceMatcher,
  CountryMatcher,
  DatabaseCanadaTariffMatcher,
  LocationMatcher,
  ProductMatcher,
  QuantityMatcher,
  ShipmentMatcher,
  TradePartnerMatcher
} from "./matchers";

import {
  CertificateOfOriginProcessor,
  CITradePartnerProcessor,
  CommercialInvoiceLineProcessor,
  CommercialInvoiceProcessor,
  LocationProcessor,
  MergeCommercialInvoiceLineProcessor,
  PackingListMatcherProcessor,
  ProductProcessor,
  ShipmentMatcherProcessor,
  ShipmentProcessor,
  TradePartnerProcessor
} from "./processors";

import { UpdateCommercialInvoiceExecuter, UpdateShipmentExecuter } from "./intent-payload-executers";

import { ShipmentAggregator } from "./aggregators/shipment.aggregator";
import { TradePartnerAggregator } from "./aggregators/trade-partner.aggregator";
import { ShipmentFieldFallbackService } from "./field-fallback";
import { AggregationDispatcher } from "./queues/aggregation.dispatcher";
import { AggregationWorker } from "./queues/aggregation.worker";
import { QUEUES } from "./queues/config";
import { aggregationTransformers } from "./transformers";
import { UnitConversionService } from "./unit-conversion/unit-conversion.service";

const commonImports = [
  TypeOrmModule.forFeature([
    Document,
    Shipment,
    TradePartner,
    Location,
    CommercialInvoice,
    Country,
    Product,
    CommercialInvoiceLine,
    CertificateOfOrigin,
    DocumentAggregation,
    DocumentAggregationStep,
    DocumentAggregationStepLog,
    DocumentAggregationTradePartner,
    CanadaTariff
  ]),
  BullModule.registerQueue(...QUEUES),
  DocumentModule,
  TradePartnerModule,
  LocationModule,
  ShipmentModule,
  ProductModule,
  CommercialInvoiceModule
];

const commonProviders = [
  AggregationService,
  AggregationIntentService,
  // matchers
  ShipmentMatcher,
  CommercialInvoiceMatcher,
  CountryMatcher,
  TradePartnerMatcher,
  LocationMatcher,
  CanadaTariffMatcher,
  DatabaseCanadaTariffMatcher,
  QuantityMatcher,
  ProductMatcher,
  // executer
  AggregationExecuter,

  // processors
  TradePartnerProcessor,
  LocationProcessor,
  ShipmentProcessor,
  MergeCommercialInvoiceLineProcessor,
  ProductProcessor,
  CommercialInvoiceProcessor,
  CommercialInvoiceLineProcessor,
  CertificateOfOriginProcessor,
  ShipmentMatcherProcessor,
  PackingListMatcherProcessor,
  CITradePartnerProcessor,
  // generators
  PartNoGenerator,

  AggregationDispatcher,
  UnitConversionService,

  ...aggregationTransformers,
  ShipmentAggregator,
  TradePartnerAggregator,

  // intent executers
  UpdateShipmentExecuter,
  UpdateCommercialInvoiceExecuter,

  // shipment field fallback
  ShipmentFieldFallbackService
];

const commonExports = [
  AggregationService,
  ShipmentMatcher,
  CommercialInvoiceMatcher,
  CountryMatcher,
  TradePartnerMatcher,
  LocationMatcher,
  CanadaTariffMatcher,
  QuantityMatcher,

  // field fallback
  ShipmentFieldFallbackService
];

@Module({
  imports: commonImports,
  providers: commonProviders,
  exports: commonExports
})
export class AggregationModule {
  static forRoot(): DynamicModule {
    return {
      module: AggregationModule,
      imports: commonImports,
      controllers: [DocumentAggregationController],
      providers: [...commonProviders, ...listeners, ...(process.env.FEATURE ? [] : [AggregationWorker])],
      exports: commonExports
    };
  }
}
