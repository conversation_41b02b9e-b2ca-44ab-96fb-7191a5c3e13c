import { DocumentType } from "@/document/types/document-types";
import {
  AirArrivalNotice,
  AirEManifest,
  AirWaybill,
  HouseOceanBillOfLading,
  OceanArrivalNotice,
  OceanEManifest,
  ParsOverlay,
  RoadArrivalNotice,
  RoadBillOfLading
} from "@/document/types/document-types.zod";
import { Injectable, Logger } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import {
  CommercialInvoice,
  EditShipmentDto,
  Port,
  QuantityUOM,
  ShipmentContainerDto,
  ShipmentMode
} from "nest-modules";
import { DataSource, ILike } from "typeorm";

@Injectable()
export class DocumentToShipmentTransformer {
  private readonly logger = new Logger(DocumentToShipmentTransformer.name);

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  private async getPortCodeFromIataCode(iataCode: string): Promise<string> {
    const port = await this.dataSource.getRepository(Port).findOne({ where: { iataCode } });
    return port?.code;
  }

  private async getPortCode(portOfEntry: string): Promise<string> {
    console.log("getPortCode", portOfEntry);
    const city = portOfEntry.split(",")[0];
    let port = await this.dataSource.getRepository(Port).findOne({
      where: {
        city: ILike(city),
        road: true
      }
    });

    if (!port) {
      port = await this.dataSource.getRepository(Port).findOne({
        where: {
          name: ILike(`${city}%`),
          road: true
        }
      });
    }

    if (!port) {
      port = await this.dataSource.getRepository(Port).findOne({
        where: {
          name: ILike(`${portOfEntry}%`),
          road: true
        }
      });
    }

    console.log("port", port);

    return port?.code;
  }

  private getFullCCN(CCN: string, carrierCode: string): string {
    if (carrierCode) {
      const isPrefixed = CCN.substring(0, 4) === carrierCode;

      if (!isPrefixed) {
        return `${carrierCode}${CCN}`;
      }
    }

    return CCN;
  }

  /**
   * Get the carrier code from a CCN
   *
   * @param CCN The CCN to get the carrier code from
   * @returns The carrier code
   */
  private getCarrierCode(CCN: string): string {
    const carrierCode = CCN.substring(0, 4);

    if (carrierCode.length === 4) {
      return carrierCode;
    }
  }

  private checkIso6346ContainerNumber(containerNumber: string): boolean {
    if (!/^[A-Z]{4}\d{7}$/.test(containerNumber)) {
      this.logger.error(`Invalid container number: ${containerNumber}`);
      return false;
    }

    let sum = 0;

    for (let i = 0; i < 10; i++) {
      // map to number
      let n = containerNumber.charCodeAt(i);
      n -= n < 58 ? 48 : 55;

      // number such as 11,22,33 will be omitted
      n += (n - 1) / 10;

      // sum the numbers multiplied by weighting
      sum += n << i;
    }

    // modulus of 11, map 10 to 0
    const checksum = (sum % 11) % 10;

    const isValid = checksum === parseInt(containerNumber[10]);

    this.logger.debug(`Container number: ${containerNumber}, isValid: ${isValid}`);

    if (!isValid) {
      this.logger.log(`Checksum failed for container number: ${containerNumber}`);
    }

    return isValid;
  }

  private mapToContainersDto(
    documentData: OceanEManifest | OceanArrivalNotice | HouseOceanBillOfLading
  ): ShipmentContainerDto[] {
    const containers = documentData.containers ?? [];
    return containers
      .filter((c) => c.containerNumber)
      .filter((c) => this.checkIso6346ContainerNumber(c.containerNumber))
      .map((c) => {
        const dto = new ShipmentContainerDto();
        dto.containerNumber = c.containerNumber.toUpperCase().replace(/[^A-Z0-9]+/g, "");
        dto.containerType = c.containerType;
        dto.etaDestination = documentData.etaDestination;
        return dto;
      });
  }

  public async transform(documentData: any, documentType: DocumentType): Promise<EditShipmentDto> {
    if (!documentType) {
      return null;
    }

    const transformers = {
      // air
      [DocumentType.AIR_WAYBILL]: this.fromAirWaybill,
      [DocumentType.AIR_ARRIVAL_NOTICE]: this.fromAirEManifest,
      [DocumentType.AIR_E_MANIFEST]: this.fromAirEManifest,

      // ocean
      [DocumentType.HOUSE_OCEAN_BILL_OF_LADING]: this.fromHouseOceanBillOfLading,
      [DocumentType.OCEAN_ARRIVAL_NOTICE]: this.fromOceanEManifest,
      [DocumentType.OCEAN_E_MANIFEST]: this.fromOceanEManifest,

      // road
      [DocumentType.PARS_OVERLAY]: this.fromParsOverlay,
      [DocumentType.ROAD_ARRIVAL_NOTICE]: this.fromRoadArrivalNotice,
      [DocumentType.ROAD_BILL_OF_LADING]: this.fromRoadBillOfLading,

      // road - commercial invoice etd
      [DocumentType.COMMERCIAL_INVOICE]: this.fromCommercialInvoice
    };

    if (!transformers[documentType]) {
      this.logger.error(`No transformer found for document type ${documentType}`);
      return null;
    }

    return await transformers[documentType].bind(this)(documentData);
  }

  private fromHouseOceanBillOfLading(documentData: HouseOceanBillOfLading): Partial<EditShipmentDto> {
    return {
      hblNumber: documentData.hblNumber,
      modeOfTransport: ShipmentMode.OCEAN_FCL,
      volume: documentData.volume,
      volumeUOM: documentData.volumeUOM,
      weight: documentData.weight,
      weightUOM: documentData.weightUOM,
      quantity: documentData.quantity,
      quantityUOM: documentData.quantityUOM ?? QuantityUOM.PACKAGE,
      voyageNumber: documentData.voyageNumber,
      vessel: documentData.vessel,
      containers: this.mapToContainersDto(documentData),
      etd: documentData.etd,
      etaPort: documentData.etaPort,
      etaDestination: documentData.etaDestination
    };
  }

  private async fromAirWaybill(documentData: AirWaybill): Promise<Partial<EditShipmentDto>> {
    return {
      hblNumber: documentData.hblNumber,
      modeOfTransport: ShipmentMode.AIR,
      volume: documentData.volume,
      volumeUOM: documentData.volumeUOM,
      weight: documentData.weight,
      weightUOM: documentData.weightUOM,
      quantity: documentData.quantity,
      quantityUOM: documentData.quantityUOM ?? QuantityUOM.PACKAGE,
      etd: documentData.etd,
      etaDestination: documentData.etaDestination,
      portCode: documentData.destinationAirportCode
        ? await this.getPortCodeFromIataCode(documentData.destinationAirportCode)
        : undefined
    };
  }

  private async fromParsOverlay(documentData: ParsOverlay): Promise<Partial<EditShipmentDto>> {
    return {
      hblNumber: documentData.CCN,
      cargoControlNumber: documentData.CCN,
      etd: documentData.etd,
      etaPort: documentData.etaPort,

      // we set the etaPort as etaDestination for road shipments
      etaDestination: documentData.etaPort,
      modeOfTransport: ShipmentMode.LAND,
      portCode: documentData.portOfEntry ? await this.getPortCode(documentData.portOfEntry) : undefined
    };
  }

  private async fromRoadArrivalNotice(documentData: RoadArrivalNotice): Promise<Partial<EditShipmentDto>> {
    return {
      modeOfTransport: ShipmentMode.LAND,
      hblNumber: documentData.CCN,
      cargoControlNumber: documentData.CCN,
      weight: documentData.weight,
      weightUOM: documentData.weightUOM,
      quantity: documentData.quantity,
      quantityUOM: documentData.quantityUOM ?? QuantityUOM.PACKAGE,
      portCode:
        documentData.portCode ??
        (documentData.portOfEntry ? await this.getPortCode(documentData.portOfEntry) : undefined),
      etaPort: documentData.etaPort,

      // we set the etaPort as etaDestination for road shipments
      etaDestination: documentData.etaPort
    };
  }

  private async fromRoadBillOfLading(documentData: RoadBillOfLading): Promise<Partial<EditShipmentDto>> {
    return {
      modeOfTransport: ShipmentMode.LAND,
      weight: documentData.weight,
      weightUOM: documentData.weightUOM,
      quantity: documentData.quantity,
      quantityUOM: documentData.quantityUOM ?? QuantityUOM.PACKAGE,
      hblNumber: documentData.CCN,
      cargoControlNumber: documentData.CCN,
      portCode:
        documentData.portCode ??
        (documentData.portOfEntry ? await this.getPortCode(documentData.portOfEntry) : undefined),
      etd: documentData.etd,
      etaPort: documentData.etaPort,
      etaDestination: documentData.etaPort
    };
  }

  private async fromOceanEManifest(
    documentData: OceanEManifest | OceanArrivalNotice
  ): Promise<Partial<EditShipmentDto>> {
    const fullCCN = documentData.CCN
      ? this.getFullCCN(documentData.CCN, documentData.carrierCode)
      : undefined;

    return {
      hblNumber: documentData.hblNumber,
      cargoControlNumber: fullCCN,
      weight: documentData.weight,
      weightUOM: documentData.weightUOM,
      volume: documentData.volume,
      volumeUOM: documentData.volumeUOM,
      quantity: documentData.quantity,
      quantityUOM: documentData.quantityUOM ?? QuantityUOM.PACKAGE,
      containers: this.mapToContainersDto(documentData),
      etd: documentData.etd,
      etaPort: documentData.etaPort,
      etaDestination: documentData.etaDestination,
      portCode: documentData.portCode,
      subLocation: documentData.subLocation?.code
    };
  }

  private async fromAirEManifest(
    documentData: AirEManifest | AirArrivalNotice
  ): Promise<Partial<EditShipmentDto>> {
    return {
      hblNumber: documentData.hblNumber,
      cargoControlNumber: documentData.CCN,
      weight: documentData.weight,
      weightUOM: documentData.weightUOM,
      volume: documentData.volume,
      volumeUOM: documentData.volumeUOM,
      quantity: documentData.quantity,
      quantityUOM: documentData.quantityUOM ?? QuantityUOM.PACKAGE,
      etaDestination: documentData.etaDestination,
      portCode:
        documentData.portCode ??
        (documentData.destinationAirportCode
          ? await this.getPortCodeFromIataCode(documentData.destinationAirportCode)
          : undefined),
      subLocation: documentData.subLocation?.code
    };
  }

  private async fromCommercialInvoice(documentData: CommercialInvoice): Promise<Partial<EditShipmentDto>> {
    return {
      etd: documentData.invoiceDate
    };
  }
}
