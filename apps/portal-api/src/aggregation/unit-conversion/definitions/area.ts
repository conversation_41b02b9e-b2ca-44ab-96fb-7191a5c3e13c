import { Measure, Unit } from "./types";

export type AreaSystems = "metric" | "imperial";
export type AreaUnits = AreaMetricUnits | AreaImperialUnits;

export type AreaMetricUnits = "m2";
export type AreaImperialUnits = "ft2";

const metric: Record<AreaMetricUnits, Unit> = {
  m2: {
    name: "m2",
    alias: ["m2", "sqm", "mtk"],
    system: "metric",
    anchor: 1
  }
};

const imperial: Record<AreaImperialUnits, Unit> = {
  ft2: {
    name: "ft2",
    alias: ["ft2", "sqft"],
    system: "imperial",
    anchor: 1
  }
};

const measure: Measure<AreaSystems, AreaUnits> = {
  type: "area",
  systems: {
    metric,
    imperial
  },
  anchors: {
    metric: {
      imperial: 10.7639
    },
    imperial: {
      metric: 1 / 10.7639
    }
  }
};

export default measure;
