import { Measure, Unit } from "./types";

export type QuantitySystems = "quantity";
export type QuantityUnits = "mil" | "nmb" | "dzn" | "gro";

const quantity: Record<QuantityUnits, Unit> = {
  mil: {
    name: "mil",
    alias: ["mil", "thousand", "thousands"],
    system: "quantity",
    anchor: 1000
  },
  nmb: {
    name: "nmb",
    alias: [
      "nmb",
      "number",
      "numbers",
      "pcs",
      "piece",
      "pieces",
      "crate",
      "crates",
      "nap",
      "par",
      "set",
      "ctn",
      "ctns",
      "unit",
      "units",
      "item",
      "items"
    ],
    system: "quantity",
    anchor: 1
  },
  dzn: {
    name: "dzn",
    alias: ["dzn", "dozen", "dozens"],
    system: "quantity",
    anchor: 12
  },
  gro: {
    name: "gro",
    alias: ["grosstwelvedozen", "grosstwelvedozens", "gro"],
    system: "quantity",
    anchor: 144
  }
};

const measure: Measure<QuantitySystems, QuantityUnits> = {
  type: "quantity",
  systems: {
    quantity
  },
  anchors: {
    quantity: {
      quantity: 1
    }
  }
};

export default measure;
