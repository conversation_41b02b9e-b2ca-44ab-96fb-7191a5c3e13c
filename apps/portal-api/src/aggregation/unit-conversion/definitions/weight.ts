import { Measure, Unit } from "./types";

export type WeightSystems = "metric" | "imperial";
export type WeightUnits = WeightMetricUnits | WeightImperialUnits;

export type WeightMetricUnits = "carat" | "g" | "kg" | "mt";
export type WeightImperialUnits = "oz" | "lb" | "st" | "lt";

const metric: Record<WeightMetricUnits, Unit> = {
  carat: {
    name: "carat",
    alias: ["carat", "ctm"],
    system: "metric",
    anchor: 0.2
  },
  g: {
    name: "g",
    alias: ["g", "gram", "grams", "grm"],
    system: "metric",
    anchor: 1
  },
  kg: {
    name: "kg",
    alias: ["kg", "kilogram", "kilograms", "kgm", "kgs", "kns", "ksd"],
    system: "metric",
    anchor: 1000
  },
  mt: {
    name: "mt",
    alias: ["mt", "metrictonne", "metrictonnes", "tne", "tsd"],
    system: "metric",
    anchor: 1000000
  }
};

const imperial: Record<WeightImperialUnits, Unit> = {
  oz: {
    name: "oz",
    alias: ["oz", "ounce", "ounces"],
    system: "imperial",
    anchor: 1 / 16
  },
  lb: {
    name: "lb",
    alias: ["lb", "pound", "pounds", "lbs", "lbr"],
    system: "imperial",
    anchor: 1
  },
  st: {
    name: "st",
    alias: ["st", "stone", "stones"],
    system: "imperial",
    anchor: 14
  },
  lt: {
    name: "lt",
    alias: ["lt", "longton", "longtons"],
    system: "imperial",
    anchor: 2000 // 140 st * 16 lb
  }
};

const measure: Measure<WeightSystems, WeightUnits> = {
  type: "weight",
  systems: {
    metric,
    imperial
  },
  anchors: {
    metric: {
      imperial: 1 / 453.59237
    },
    imperial: {
      metric: 453.59237
    }
  }
};

export default measure;
