import { Measure, Unit } from "./types";

export type LengthSystems = "metric" | "imperial";
export type LengthUnits = LengthMetricUnits | LengthImperialUnits;

export type LengthMetricUnits = "millimeter" | "cm" | "m" | "km";
export type LengthImperialUnits = "in" | "ft" | "yd" | "mi";

const metric: Record<LengthMetricUnits, Unit> = {
  millimeter: {
    name: "millimeter",
    alias: ["mm", "millimeter", "millimeters"],
    system: "metric",
    anchor: 0.001
  },
  cm: {
    name: "cm",
    alias: ["cm", "centimeter", "centimeters"],
    system: "metric",
    anchor: 0.01
  },
  m: {
    name: "m",
    alias: ["m", "meter", "meters", "mtr"],
    system: "metric",
    anchor: 1
  },
  km: {
    name: "km",
    alias: ["km", "kilometer", "kilometers"],
    system: "metric",
    anchor: 1000
  }
};

const imperial: Record<LengthImperialUnits, Unit> = {
  in: {
    name: "in",
    alias: ["in", "inch", "inches"],
    system: "imperial",
    anchor: 1 / 12
  },
  ft: {
    name: "ft",
    alias: ["ft", "foot", "feet"],
    system: "imperial",
    anchor: 1
  },
  yd: {
    name: "yd",
    alias: ["yd", "yard", "yards"],
    system: "imperial",
    anchor: 3
  },
  mi: {
    name: "mi",
    alias: ["mi", "mile", "miles"],
    system: "imperial",
    anchor: 5280
  }
};

const measure: Measure<LengthSystems, LengthUnits> = {
  type: "length",
  systems: {
    metric,
    imperial
  },
  anchors: {
    metric: {
      imperial: 3.28084 // meters to feet
    },
    imperial: {
      metric: 1 / 3.28084 // feet to meters
    }
  }
};

export default measure;
