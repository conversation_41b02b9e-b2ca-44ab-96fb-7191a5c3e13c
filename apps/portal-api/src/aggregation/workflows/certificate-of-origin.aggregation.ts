import { FieldsToObjectAdapter } from "../adapter/fields-to-object.adapter";
import { AggregationStep } from "../executer/aggregation.executer";
import { CertificateOfOriginProcessor } from "../processors/certificate-of-origin.processor";
import { TradePartnerProcessor } from "../processors/trade-partner.processor";

export const CertificateOfOriginAggregation: AggregationStep[] = [
  {
    name: "FindOrCreateTradePartners",
    displayName: "Find or Create Trade Partners",
    loader: TradePartnerProcessor,
    parameters: {
      document: (aggregation) => {
        return aggregation.documents[0];
      },
      shipmentId: (aggregation) => {
        return aggregation.shipment?.id;
      },
      documentId: (aggregation) => {
        return aggregation.documents[0].id;
      }
    }
  },
  {
    name: "create-certificate-of-origin",
    displayName: "Create Certificate of Origin",
    loader: CertificateOfOriginProcessor,
    parameters: {
      data: (aggregation) => {
        return new FieldsToObjectAdapter(aggregation.documents[0].fields).toObject();
      },
      tradePartners: (_, output) => output["FindOrCreateTradePartners"]
    }
  }
];
