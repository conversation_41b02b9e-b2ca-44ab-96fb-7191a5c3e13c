import { DocumentAggregation } from "nest-modules";

import {
  CITradePartnerProcessor,
  CommercialInvoiceLineProcessor,
  CommercialInvoiceProcessor,
  MergeCommercialInvoiceLineProcessor,
  PackingListMatcherProcessor,
  ShipmentMatcherProcessor
} from "../processors";

import { FieldsToObjectAdapter } from "../adapter/fields-to-object.adapter";
import { AggregationStep } from "../executer/aggregation.executer";

export const CommercialInvoiceAggregation: AggregationStep[] = [
  {
    name: "FindOrCreateTradePartners",
    displayName: "Find or Create Trade Partners",
    loader: CITradePartnerProcessor,
    parameters: {
      data: (aggregation: DocumentAggregation) => {
        const commercialInvoice = aggregation.documents.find(
          (document) => document.name === "COMMERCIAL_INVOICE"
        );

        if (!commercialInvoice) {
          throw new Error("Commercial invoice not found in aggregation");
        }

        return new FieldsToObjectAdapter(commercialInvoice.fields).toObject();
      },
      documentId: (aggregation: DocumentAggregation) => {
        return aggregation.documents.find((document) => document.name === "COMMERCIAL_INVOICE")?.id;
      },
      shipmentId: (aggregation) =>
        aggregation.documents.find((document) => document.name === "COMMERCIAL_INVOICE")?.shipment?.id
    }
  },
  {
    name: "FindMatchingPackingList",
    displayName: "Find Matching Packing List",
    loader: PackingListMatcherProcessor,
    parameters: {
      commercialInvoice: (aggregation) =>
        aggregation.documents.find((document) => document.name === "COMMERCIAL_INVOICE"),
      aggregation: (aggregation) => aggregation
    }
  },
  {
    name: "GetShipment",
    displayName: "Get Shipment",
    loader: ShipmentMatcherProcessor,
    parameters: {
      data: (aggregation: DocumentAggregation) => {
        const commercialInvoice = aggregation.documents.find(
          (document) => document.name === "COMMERCIAL_INVOICE"
        );

        if (!commercialInvoice) {
          throw new Error("Commercial invoice not found in aggregation");
        }

        return new FieldsToObjectAdapter(commercialInvoice.fields).toObject();
      },
      shipmentId: (aggregation: DocumentAggregation) => {
        return aggregation.documents.find((document) => document.name === "COMMERCIAL_INVOICE")?.shipment?.id;
      }
    }
  },
  {
    name: "MergeCIPL",
    displayName: "Merge Commercial Invoice and Packing List",
    loader: MergeCommercialInvoiceLineProcessor,
    // todo: implement
    parameters: {
      data: (aggregation: DocumentAggregation) => {
        const commercialInvoice = aggregation.documents.find(
          (document) => document.name === "COMMERCIAL_INVOICE"
        );

        if (!commercialInvoice) {
          throw new Error("Commercial invoice not found in aggregation");
        }

        return new FieldsToObjectAdapter(commercialInvoice.fields).toObject();
      },
      shipment: (_, output) => output["GetShipment"],
      tradePartners: (_, output) => output["FindOrCreateTradePartners"],
      packingList: async (_, output) => {
        const document = output["FindMatchingPackingList"].packingList;
        if (document?.fields) {
          const converted = new FieldsToObjectAdapter(document.fields).toObject();
          return converted;
        }
        return null;
      }
    }
  },
  {
    name: "CreateCommercialInvoice",
    displayName: "Create Commercial Invoice",
    loader: CommercialInvoiceProcessor,
    parameters: {
      data: (_: any, output: any) => output["MergeCIPL"],
      tradePartners: (_, output) => output["FindOrCreateTradePartners"],
      shipment: (_, output) => output["GetShipment"]
    }
  },
  {
    name: "CreateCommercialInvoiceLine",
    displayName: "Create Commercial Invoice Line",
    loader: CommercialInvoiceLineProcessor,
    parameters: {
      lines: (_: any, output: any) => {
        return output["MergeCIPL"].commercialInvoiceLines;
      },
      products: (_: any, output: any) => output["CreateOrMatchProducts"],
      commercialInvoiceId: (_: any, output: any) => output["CreateCommercialInvoice"].id
    }
  }
];
