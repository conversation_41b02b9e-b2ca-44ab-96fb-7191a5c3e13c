import { AggregationStep } from "../executer/aggregation.executer";
import { ShipmentProcessor } from "../processors/shipment.processor";

export const ShipmentAggregation: AggregationStep[] = [
  {
    name: "CreateShipment",
    displayName: "Create Shipment",
    loader: ShipmentProcessor,
    parameters: {
      documents: (aggregation) => aggregation.documents,
      organizationId: (aggregation) => aggregation.organizationId
    }
  }
];
