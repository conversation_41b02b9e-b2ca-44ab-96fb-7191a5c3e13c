function termFreqMap(str: string) {
  const words = str.split(" ");
  const termFreq = {};
  words.forEach(function (w) {
    termFreq[w] = (termFreq[w] || 0) + 1;
  });
  return termFreq;
}

function addKeysToDict(map: Record<string, number>, dict: Record<string, boolean>) {
  for (const key in map) {
    dict[key] = true;
  }
}

function termFreqMapToVector(map: Record<string, number>, dict: Record<string, boolean>) {
  const termFreqVector = [];
  for (const term in dict) {
    termFreqVector.push(map[term] || 0);
  }
  return termFreqVector;
}

function vecDotProduct(vecA: number[], vecB: number[]) {
  let product = 0;
  for (let i = 0; i < vecA.length; i++) {
    product += vecA[i] * vecB[i];
  }
  return product;
}

function vecMagnitude(vec: number[]) {
  let sum = 0;
  for (let i = 0; i < vec.length; i++) {
    sum += vec[i] * vec[i];
  }
  return Math.sqrt(sum);
}

function cosineSimilarity(vecA: number[], vecB: number[]) {
  return vecDotProduct(vecA, vecB) / (vecMagnitude(vecA) * vecMagnitude(vecB));
}

export const textCosineSimilarity = (strA: string, strB: string) => {
  const termFreqA = termFreqMap(strA);
  const termFreqB = termFreqMap(strB);

  const dict = {};
  addKeysToDict(termFreqA, dict);
  addKeysToDict(termFreqB, dict);

  const termFreqVecA = termFreqMapToVector(termFreqA, dict);
  const termFreqVecB = termFreqMapToVector(termFreqB, dict);

  return cosineSimilarity(termFreqVecA, termFreqVecB);
};
