import {
  Body,
  Controller,
  Get,
  HttpCode,
  Inject,
  Logger,
  Param,
  ParseBoolPipe,
  ParseIntPipe,
  Post,
  Query,
  Req,
  UseGuards
} from "@nestjs/common";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { InjectDataSource } from "@nestjs/typeorm";
import {
  AccessTokenGuard,
  AggregationTargetType,
  ApiAccessTokenAuthenticated,
  AuthenticatedRequest,
  Document,
  DocumentAggregation
} from "nest-modules";
import { ClsService } from "nestjs-cls";
import { DataSource } from "typeorm";
import { AggregationService } from "./aggregation.service";
import { ShipmentAggregator } from "./aggregators/shipment.aggregator";
import { TradePartnerAggregator } from "./aggregators/trade-partner.aggregator";
import { CanadaTariffMatcher } from "./matchers";
@ApiTags("Document Aggregation API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller()
export class DocumentAggregationController {
  private readonly logger = new Logger(DocumentAggregationController.name);

  constructor(
    @Inject(TradePartnerAggregator)
    private readonly tradePartnerAggregator: TradePartnerAggregator,
    private readonly aggregationService: AggregationService,
    private readonly cls: ClsService,
    private readonly canadaTariffMatcher: CanadaTariffMatcher,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly shipmentAggregator: ShipmentAggregator
  ) {}

  @ApiOperation({
    summary: "Get document aggregation"
  })
  @HttpCode(200)
  @Get("document-aggregation/:id")
  async getDocumentAggregation(@Param("id", ParseIntPipe) id: number) {
    return this.aggregationService.getDocumentAggregation(id);
  }

  @ApiOperation({
    summary: "Apply aggregation"
  })
  @HttpCode(200)
  @Post("document-aggregation/:id/apply")
  async apply(
    @Req() request: AuthenticatedRequest,
    @Param("id", ParseIntPipe) id: number,
    @Query("retry", ParseBoolPipe) retry?: boolean
  ) {
    return await this.cls.run(async () => {
      this.cls.set("ORGANIZATION_ID", request.user.organization.id);
      return await this.aggregationService.applyAggregation(id, retry);
    });
  }

  @ApiOperation({
    summary: "Abort the aggregation for a document"
  })
  @HttpCode(200)
  @Post("document-aggregation/:id/abort")
  async abortAggregation(@Param("id", ParseIntPipe) id: number) {
    return await this.aggregationService.abortAggregation(id);
  }

  @ApiOperation({
    summary: "Get the aggregation for a document"
  })
  @HttpCode(200)
  @Get("document/:id/aggregation")
  async getAggregationForDocument(@Param("id", ParseIntPipe) id: number) {
    return this.aggregationService.getAggregationByDocumentId(id);
  }

  @ApiOperation({
    summary: "Create a new aggregation for a document"
  })
  @HttpCode(200)
  @Post("document/:id/aggregate")
  async createDocumentAggregation(@Param("id", ParseIntPipe) id: number) {
    return this.aggregationService.aggregateDocumentsAsync([id]);
  }

  @ApiOperation({
    summary: "Aggregate documents"
  })
  @HttpCode(200)
  @Post("document/run-aggregation")
  async runAggregation(@Body() body: { documentIds: number[]; batchId?: string }) {
    return this.aggregationService.aggregateDocumentsAsync(body.documentIds, body.batchId);
  }

  // @ApiExcludeEndpoint()
  // @Post("test-canada-tariff-matcher")
  // async testCanadaTariffMatcher(@Body() dto: TestCanadaTariffMatcherDto) {
  //   return await this.canadaTariffMatcher.getClosestTariff(dto);
  // }

  @ApiOperation({
    summary: "Get shipment aggregation details"
  })
  @HttpCode(200)
  @Get("shipment/:id/aggregation-details")
  async getShipmentAggregationDetails(
    @Req() request: AuthenticatedRequest,
    @Param("id", ParseIntPipe) id: number
  ) {
    // get the documents
    const documents = await this.dataSource.manager.getRepository(Document).find({
      where: {
        shipmentId: id,
        isHidden: false
      },
      relations: {
        fields: true
      }
    });

    const tradePartners = await this.tradePartnerAggregator.aggregate(id);

    const shipmentDto = await this.shipmentAggregator.aggregate(documents, request.user.organization.id, id);

    const keys = ["shipper", "consignee", "forwarder"];
    for (const key of keys) {
      tradePartners.forEach((tradePartner) => {
        if (tradePartner.types.includes(key)) {
          if (!shipmentDto[key]) {
            shipmentDto[key] = {
              values: [],
              value: tradePartner.data
            };
          }

          shipmentDto[key].values.push(tradePartner);
        }
      });
    }

    const aggregatedShipment = {};

    // foreach key in shipmentDto, add the value to the aggregatedShipment
    for (const key of Object.keys(shipmentDto)) {
      aggregatedShipment[key] = shipmentDto[key].value;
    }

    return {
      shipment: shipmentDto,
      tradePartners,
      documents: documents.map((d) => ({
        documentId: d.id,
        name: d.name
      })),
      aggregatedShipment
    };
  }

  @ApiOperation({
    summary: "Get aggregation by target type and id"
  })
  @HttpCode(200)
  @Get("document-aggregation/by-target/:type/:id")
  async getAggregationByTarget(
    @Param("type") type: AggregationTargetType,
    @Param("id", ParseIntPipe) id: number
  ): Promise<DocumentAggregation | null> {
    return this.aggregationService.getAggregationByTarget(id, type);
  }
}
