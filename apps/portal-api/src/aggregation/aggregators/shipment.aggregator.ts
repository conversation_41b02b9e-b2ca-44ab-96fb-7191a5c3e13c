import { DocumentService } from "@/document/services/document.service";
import { DocumentType } from "@/document/types/document-types";
import { HouseOceanBillOfLading } from "@/document/types/document-types.zod";
import { forwardRef, Inject, Injectable, Logger } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import {
  AggregationTargetType,
  Container,
  Document,
  DocumentAggregation,
  DocumentAggregationAction,
  DocumentAggregationStatus,
  DocumentField,
  Importer,
  Shipment,
  ShipmentMode
} from "nest-modules";
import { DataSource, In } from "typeorm";
import { LocationProcessor } from "../processors";
import { ProcessorContext } from "../processors/base.processor";
import { selectBestValue } from "../rules/shipment.rules";
import { DocumentToShipmentTransformer } from "../transformers/document-to-shipment.transformers";

@Injectable()
export class ShipmentAggregator {
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    @Inject(DocumentService)
    private readonly documentService: DocumentService,
    @Inject(DocumentToShipmentTransformer)
    private readonly documentToShipmentTransformer: DocumentToShipmentTransformer,
    // FIXME: we use this because we are exporting them from one entry point
    @Inject(forwardRef(() => LocationProcessor))
    private readonly locationProcessor: LocationProcessor
  ) {}

  private readonly logger = new Logger(ShipmentAggregator.name);

  private async getImporter(organizationId: number) {
    return this.dataSource.getRepository(Importer).findOne({
      where: {
        organization: {
          id: organizationId
        }
      }
    });
  }

  /**
   * Check if the batch can create a shipment
   *
   * @param documentIds
   * @param organizationId
   * @returns
   */
  public async checkBatchCanCreateShipment(documentIds: number[], organizationId: number) {
    const data = await this.dataSource.getRepository(DocumentField).find({
      where: {
        name: In(["hblNumber", "cargoControlNumber", "CCN"]),
        document: {
          id: In(documentIds)
        }
      },
      select: {
        name: true,
        value: true,
        document: {
          id: true,
          name: true
        }
      },
      relations: {
        document: true
      }
    });

    // group by name
    const grouped = data.reduce(
      (acc, curr) => {
        acc[curr.name].push({
          documentId: curr.document.id,
          name: curr.document.name,
          value: curr.value
        });
        return acc;
      },
      {
        hblNumber: [],
        cargoControlNumber: [],
        CCN: []
      }
    );

    const documentTypes = Array.from(new Set(data.map((d) => d.document.name)));

    const modeOfTransport = this.determineModeOfTransport(documentTypes);

    const result = {
      hblNumber: selectBestValue(grouped["hblNumber"], "hblNumber"),
      cargoControlNumber: selectBestValue(
        [...grouped["cargoControlNumber"], ...grouped["CCN"]],
        "cargoControlNumber"
      )
    };

    if (modeOfTransport === ShipmentMode.LAND) {
      result.hblNumber = result.cargoControlNumber;
    }

    // hblNumber cannot be null
    if (result.hblNumber === null) {
      throw new Error("HBL number cannot be null");
    }

    this.logger.debug("Aggregation result: " + JSON.stringify(result) + " organizationId: " + organizationId);

    // query for existing shipment with hblNumber and cargoControlNumber
    // validate hbl and ccns, we cannot create a shipment with the same hbl or ccns
    const isHBLOrCCNExists = await this.dataSource
      .getRepository(Shipment)
      .createQueryBuilder("shipment")
      .where([{ hblNumber: result.hblNumber }, { cargoControlNumber: result.cargoControlNumber }])
      .andWhere({
        organizationId: organizationId
      })
      .getExists();

    if (isHBLOrCCNExists) {
      throw new Error("HBL or CCN already exists");
    }

    return {
      ...result,
      modeOfTransport
    };
  }

  private determineModeOfTransport(documentTypes: string[]) {
    const OCEAN_DOCUMENT_TYPES = [
      DocumentType.HOUSE_OCEAN_BILL_OF_LADING,
      DocumentType.OCEAN_E_MANIFEST,
      DocumentType.OCEAN_ARRIVAL_NOTICE
    ];
    const AIR_DOCUMENT_TYPES = [
      DocumentType.AIR_WAYBILL,
      DocumentType.AIR_ARRIVAL_NOTICE,
      DocumentType.AIR_E_MANIFEST
    ];
    const LAND_DOCUMENT_TYPES = [
      DocumentType.ROAD_ARRIVAL_NOTICE,
      DocumentType.ROAD_BILL_OF_LADING,
      DocumentType.PARS_OVERLAY
    ];

    const possibleModeOfTransports = new Set<ShipmentMode>();

    documentTypes.map((d) => {
      if (OCEAN_DOCUMENT_TYPES.includes(d as DocumentType)) {
        possibleModeOfTransports.add(ShipmentMode.OCEAN_FCL);
      }
      if (AIR_DOCUMENT_TYPES.includes(d as DocumentType)) {
        possibleModeOfTransports.add(ShipmentMode.AIR);
      }
      if (LAND_DOCUMENT_TYPES.includes(d as DocumentType)) {
        possibleModeOfTransports.add(ShipmentMode.LAND);
      }
    });

    // we give preference to ocean, then air, finally land
    const order = [ShipmentMode.OCEAN_FCL, ShipmentMode.AIR, ShipmentMode.LAND];

    for (const mode of order) {
      if (possibleModeOfTransports.has(mode)) {
        return mode;
      }
    }

    return null;
  }

  // this is the consumer
  public async filterShipmentDocuments(documents: Document[]) {
    const supportedDocumentTypes = [
      // ocean
      DocumentType.HOUSE_OCEAN_BILL_OF_LADING,
      DocumentType.OCEAN_E_MANIFEST,
      DocumentType.OCEAN_ARRIVAL_NOTICE,
      // air
      DocumentType.AIR_WAYBILL,
      DocumentType.AIR_ARRIVAL_NOTICE,
      DocumentType.AIR_E_MANIFEST,
      // land
      DocumentType.ROAD_ARRIVAL_NOTICE,
      DocumentType.ROAD_BILL_OF_LADING,
      DocumentType.PARS_OVERLAY,
      // CI_PL
      DocumentType.COMMERCIAL_INVOICE
    ];

    return documents.filter((d) => supportedDocumentTypes.includes(d.name as DocumentType));
  }

  async _aggregate(documents: Document[], withSource: boolean = false): Promise<Partial<Shipment>> {
    if (documents.length === 0) {
      return {};
    }

    // determine mode of transport
    const modeOfTransport = this.determineModeOfTransport(documents.map((d) => d.name));

    const allDocumentData = await Promise.all(
      documents.map(async (d) => {
        const documentData = await this.documentService.getDocumentData(d.id);
        return {
          id: d.id,
          ...documentData
        };
      })
    );

    const transformedDocuments = await Promise.all(
      // filter out commercial invoice if it is not a road shipment
      allDocumentData.map(async (d) => {
        const transformedDocument = await this.documentToShipmentTransformer.transform(
          d.data,
          d.documentType as unknown as DocumentType
        );
        return {
          documentId: d.id,
          name: d.documentType,
          data: transformedDocument
        };
      })
    );

    // foreach keys, transform to key: [value]
    const shipmentData = {};

    // foreach transformed document, add the key and value to the shipmentDto
    for (const transformedDocument of transformedDocuments) {
      if (transformedDocument.data === null) {
        continue;
      }

      // if the key is not in the shipmentDto, add it
      for (const key of Object.keys(transformedDocument.data)) {
        const value = transformedDocument.data[key];

        // skip if the value is null
        if (value === null || value === undefined) {
          continue;
        }

        // if the key is not in the shipmentDto, add it
        if (!shipmentData[key]) {
          shipmentData[key] = {
            source: [],
            _uniqueValue: new Set(),
            isConcordant: true
          };
        }

        // add the value to the key
        shipmentData[key].source.push({
          documentId: transformedDocument.documentId,
          name: transformedDocument.name,
          value
        });

        // add the value to the unique value set
        shipmentData[key]._uniqueValue.add(JSON.stringify(value));
      }
    }

    const shipmentDto = {};

    for (const key of Object.keys(shipmentData)) {
      shipmentData[key].isConcordant = shipmentData[key]._uniqueValue.size === 1;
      delete shipmentData[key]._uniqueValue;

      // return any if is concordant
      if (shipmentData[key].isConcordant) {
        shipmentData[key].value = shipmentData[key].source[0].value;
      } else {
        // apply the rules
        shipmentData[key].value = selectBestValue(shipmentData[key].source, key as keyof Shipment);
      }

      shipmentDto[key] = shipmentData[key].value;
    }

    // add mode of transport
    shipmentDto["modeOfTransport"] = modeOfTransport;

    // TODO: this is a workaround to return the source data
    if (withSource) {
      return shipmentData as unknown;
    }

    return shipmentDto;
  }

  async aggregate(
    documents: Document[],
    organizationId: number,
    shipmentId?: number,
    batchId?: string,
    skipCreateAggregation: boolean = false
  ): Promise<Partial<Shipment>> {
    const filteredDocuments = await this.filterShipmentDocuments(documents);

    const { containers, ...shipmentDto } = await this._aggregate(filteredDocuments);

    if (!shipmentId) {
      // run aggregation and create shipment
      const importer = await this.getImporter(organizationId);

      // set shipment id
      const shipment = await this.dataSource.getRepository(Shipment).save({
        importer: importer,
        organization: {
          id: organizationId
        },
        ...shipmentDto
      });

      shipmentId = shipment.id;

      this.logger.debug("Shipment created: " + shipmentId);
    } else {
      await this.dataSource.getRepository(Shipment).update(shipmentId, {
        ...shipmentDto
      });

      this.logger.debug("Shipment updated: " + shipmentId);
    }

    if (containers && containers.length > 0) {
      await this.dataSource.getRepository(Container).upsert(
        containers.map((c) => ({
          ...c,
          shipment: {
            id: shipmentId
          }
        })),
        ["containerNumber", "shipment"]
      );
    }

    const oceanOrAirDocument = filteredDocuments.find(
      (d) => d.name === DocumentType.HOUSE_OCEAN_BILL_OF_LADING || d.name === DocumentType.AIR_WAYBILL
    );

    if (oceanOrAirDocument) {
      const documentData = await this.documentService.getDocumentData(oceanOrAirDocument.id);
      const data = documentData.data as HouseOceanBillOfLading;

      // handle locations
      const locations = {
        portOfLoading: null,
        portOfDischarge: null,
        placeOfDelivery: null
      };

      locations.portOfLoading = data.portOfLoading;
      locations.portOfDischarge = data.portOfDischarge;
      locations.placeOfDelivery = data.placeOfDelivery;

      // persist using location processor
      const results = await this.locationProcessor.get({
        params: {
          data: locations
        }
      } as ProcessorContext<{ data: any }>);

      // persist locations
      // TODO: check if the location can be overwritten by null
      await this.dataSource.getRepository(Shipment).update(shipmentId, {
        portOfLoading: results.result.find((r) => r.key === "portOfLoading")?.entity,
        portOfDischarge: results.result.find((r) => r.key === "portOfDischarge")?.entity,
        placeOfDelivery: results.result.find((r) => r.key === "placeOfDelivery")?.entity
      });
    }

    if (!skipCreateAggregation) {
      await this.createAggregation(filteredDocuments, organizationId, shipmentId, batchId);
    }

    return {
      id: shipmentId,
      ...shipmentDto
    };
  }

  async createAggregation(
    documents: Document[],
    organizationId: number,
    shipmentId: number,
    batchId: string
  ): Promise<DocumentAggregation> {
    const filteredDocuments = await this.filterShipmentDocuments(documents);

    // do not create aggregation if there already is one
    const aggregation = await this.dataSource.getRepository(DocumentAggregation).findOne({
      where: {
        documents: { id: In(filteredDocuments.map((d) => d.id)) },
        action: DocumentAggregationAction.CREATE_SHIPMENT,
        batch: { id: batchId }
      }
    });

    if (aggregation) {
      return aggregation;
    }

    const documentAggregation = this.dataSource.getRepository(DocumentAggregation).create({
      documents: filteredDocuments,
      action: DocumentAggregationAction.CREATE_SHIPMENT,
      batch: {
        id: batchId
      },
      organization: {
        id: organizationId
      },
      shipment: {
        id: shipmentId
      },
      targetType: AggregationTargetType.SHIPMENT,
      targetId: shipmentId,
      status: DocumentAggregationStatus.SUCCESS
    });

    // save document aggregation
    await this.dataSource.getRepository(DocumentAggregation).save(documentAggregation);

    return documentAggregation;
  }
}
