import { resolveEntities } from "@/aggregation/record-linkage";
import { DocumentService } from "@/document/services/document.service";
import { Inject, Injectable, Logger } from "@nestjs/common";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import { Document, DocumentAggregationTradePartner, Shipment, UserPermission } from "nest-modules";
import { ArrayContains, ArrayOverlap, DataSource, Repository } from "typeorm";
import { TradePartnerProcessor } from "../processors";

const SHIPMENT_TRADE_PARTNER_FIELDS = ["consignee", "shipper", "forwarder"];

const COMMERCIAL_INVOICE_TRADE_PARTNER_FIELDS = ["vendor", "shipTo", "purchaser"];

const CERTIFICATE_OF_ORIGIN_TRADE_PARTNER_FIELDS = ["exporter", "importer"];

const TRADE_PARTNER_FIELDS = [
  ...SHIPMENT_TRADE_PARTNER_FIELDS,
  ...COMMERCIAL_INVOICE_TRADE_PARTNER_FIELDS,
  ...CERTIFICATE_OF_ORIGIN_TRADE_PARTNER_FIELDS
];

// original trade partner with source
type TradePartnerWithSource = {
  data: any;
  type: string;
  source: {
    documentId: number;
    name: string;
  };
};

@Injectable()
export class TradePartnerAggregator {
  constructor(
    @InjectRepository(Shipment)
    private readonly shipmentRepository: Repository<Shipment>,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
    @Inject(DocumentService)
    private readonly documentService: DocumentService,
    @InjectRepository(DocumentAggregationTradePartner)
    private readonly documentAggregationTradePartnerRepository: Repository<DocumentAggregationTradePartner>,
    private readonly moduleRef: ModuleRef,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  private readonly logger = new Logger(TradePartnerAggregator.name);

  async aggregate(shipmentId: number) {
    const documents = await this.documentRepository.find({
      where: {
        shipment: { id: shipmentId },
        isHidden: false
      },
      relations: {
        documentType: true,
        fields: true
      }
    });

    let tradePartners: TradePartnerWithSource[] = [];

    for (const document of documents) {
      const documentData = await this.documentService.mapToDocumentData(document);

      for (const field of TRADE_PARTNER_FIELDS) {
        if (documentData.data[field]) {
          const tradePartner = documentData.data[field];

          // if the trade partner has no name, skip it
          if (!tradePartner.name) {
            continue;
          }

          tradePartners.push({
            data: tradePartner,
            type: field,
            source: {
              documentId: document.id,
              name: document.name
            }
          });
        }
      }
    }

    const resolvedTradePartners = resolveEntities(tradePartners);

    // TODO: remove this line later
    // Right now we are deleting all the trade partners for the shipment before persisting the new ones
    // to the database
    await this.documentAggregationTradePartnerRepository.delete({ shipment: { id: shipmentId } });

    const documentAggregationTradePartners = resolvedTradePartners.map((resolvedTradePartner: any) => {
      const documentAggregationTradePartner = new DocumentAggregationTradePartner();
      documentAggregationTradePartner.partnerTypes = resolvedTradePartner.types;
      // persist the source of the trade partner
      // TODO: this is temporary, we should normalize the table schema later
      documentAggregationTradePartner.source = resolvedTradePartner.cluster.map(
        (c) => `${c.source.documentId}:${c.type}`
      );
      documentAggregationTradePartner.aggregatedData = resolvedTradePartner.data;
      documentAggregationTradePartner.shipment = { id: shipmentId } as Shipment;
      return documentAggregationTradePartner;
    });

    await this.documentAggregationTradePartnerRepository.save(documentAggregationTradePartners);

    await this.aggregateShipmentTradePartners(shipmentId);

    return resolvedTradePartners;
  }

  async getShipmentTradePartners(shipmentId: number) {
    return this.documentAggregationTradePartnerRepository.find({
      where: { shipment: { id: shipmentId }, partnerTypes: ArrayOverlap(TRADE_PARTNER_FIELDS) }
    });
  }

  async getCanonicalTradePartner(shipmentId: number, field: string, documentId: number) {
    const tradePartner = await this.documentAggregationTradePartnerRepository.findOne({
      where: { shipment: { id: shipmentId }, source: ArrayContains([`${documentId}:${field}`]) }
    });

    if (!tradePartner) {
      return null;
    }

    return tradePartner.aggregatedData;
  }

  private async resolveTradePartnerProcessor(organizationId: number): Promise<TradePartnerProcessor> {
    this.logger.debug(`Resolving trade partner processor for organizationId: ${organizationId}`);
    // create scoped context and object
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(
      {
        user: {
          permission: UserPermission.ORGANIZATION_ADMIN,
          organization: {
            id: organizationId
          }
        }
      },
      contextId
    );

    return await this.moduleRef.resolve(TradePartnerProcessor, contextId, {
      strict: false
    });
  }

  async groupTradePartnersByType(
    tradePartners: DocumentAggregationTradePartner[]
  ): Promise<{ [key in (typeof TRADE_PARTNER_FIELDS)[number]]: DocumentAggregationTradePartner[] }> {
    return tradePartners.reduce(
      (acc, tradePartner) => {
        const partnerType = tradePartner.partnerTypes.filter((partnerType) =>
          TRADE_PARTNER_FIELDS.includes(partnerType)
        );
        acc[partnerType[0]] = [...(acc[partnerType[0]] || []), tradePartner];
        return acc;
      },
      {} as { [key in (typeof TRADE_PARTNER_FIELDS)[number]]: DocumentAggregationTradePartner[] }
    );
  }

  async aggregateShipmentTradePartners(shipmentId: number) {
    const tradePartners = await this.getShipmentTradePartners(shipmentId);

    // get shipment's organization id
    const shipment = await this.shipmentRepository.findOne({
      where: { id: shipmentId },
      relations: { organization: true }
    });

    const organizationId = shipment.organization.id;

    const tradePartnerProcessor = await this.resolveTradePartnerProcessor(organizationId);

    // try matching trade partners with existing trade partners
    const documentTypeMap = {
      OCEAN_FCL: "HOUSE_OCEAN_BILL_OF_LADING",
      OCEAN_LCL: "HOUSE_OCEAN_BILL_OF_LADING",
      AIR: "AIR_WAYBILL",
      ROAD: "ROAD_BILL_OF_LADING"
    };

    // for each trade partner, try to match it with an existing trade partner
    for (const tradePartner of tradePartners) {
      const partnerType = tradePartner.partnerTypes[0];

      const matchedTradePartner = await tradePartnerProcessor.findOrCreateTradePartner(
        tradePartner.aggregatedData as any,
        documentTypeMap[shipment.modeOfTransport],
        partnerType as any,
        organizationId
      );

      tradePartner.tradePartner = { id: matchedTradePartner.entity.id } as any;

      // associate the newly created tradePartner with the shipment scoped trade partner
      await this.documentAggregationTradePartnerRepository.update(tradePartner.id, {
        tradePartner: { id: matchedTradePartner.entity.id }
      });
    }

    // group by partner type
    const groupedTradePartners = await this.groupTradePartnersByType(tradePartners);

    // we have fallback trade partners for consignee and shipper
    const payload = {
      consignee:
        (groupedTradePartners.consignee?.[0] ?? groupedTradePartners.shipTo?.[0])?.tradePartner?.id ?? null,
      shipper:
        (groupedTradePartners.shipper?.[0] ?? groupedTradePartners.vendor?.[0])?.tradePartner?.id ?? null,
      forwarder: groupedTradePartners.forwarder?.[0]?.tradePartner?.id ?? null
    };

    // associate the trade partner with the shipment
    await this.dataSource.getRepository(Shipment).update(shipmentId, {
      consignee: { id: payload.consignee },
      shipper: { id: payload.shipper },
      forwarder: { id: payload.forwarder }
    });
  }
}
