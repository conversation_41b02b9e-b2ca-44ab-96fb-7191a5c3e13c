import { Injectable, Logger } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { Shipment } from "nest-modules/entities";
import { ClsService } from "nestjs-cls";
import { DataSource } from "typeorm";

export interface MatchShipmentFields {
  shipper: {
    name: string;
    street?: string;
  };
  quantity: number;
}

export interface ShipmentDocumentFields {
  hblNumber?: string;
  cargoControlNumber?: string;
  containerNumber?: string;
}

export type ShipmentDocumentFieldsKey = keyof ShipmentDocumentFields;

const MATCH_THRESHOLD = 0.8;

@Injectable()
export class ShipmentMatcher {
  private readonly logger = new Logger(ShipmentMatcher.name);

  constructor(
    @InjectDataSource()
    private dataSource: DataSource,
    private readonly cls: ClsService
  ) {}

  async fromCommercialInvoice(fields: MatchShipmentFields, batchId?: number) {
    const { shipper, quantity } = fields;

    if (!shipper.name) {
      throw new Error("Cannot match shipment without shipper name");
    }

    this.logger.debug(`shipment: fromCommercialInvoice fields: ${JSON.stringify(fields)}`);

    let weights = {
      name: 0.5,
      street: 0.5,
      quantity: 0.5
    };

    if (shipper.street) {
      weights.street = 0.2;
      weights.name = 0.3;
    }

    const results = await this.dataSource
      .createQueryBuilder()
      .select(["shipment.id AS id", "shipper.name AS name", "shipment.quantity AS quantity"])
      .from("shipment", "shipment")
      .leftJoin("trade_partner", "shipper", "shipper.id = shipment.shipperId")
      .addSelect(
        `${weights.name} * COALESCE(similarity(shipper.name, :shipperName), 0) + 
         ${weights.street} * COALESCE(similarity(shipper.address, :shipperAddress), 0) + 
         ${weights.quantity} * CASE WHEN shipment.quantity = :quantity THEN 1 ELSE 0 END`,
        "weighted_similarity"
      )
      .andWhere('shipment."organizationId" = :organizationId')
      .orderBy("weighted_similarity", "DESC")
      .limit(10)
      .setParameters({
        shipperName: shipper.name,
        shipperAddress: shipper.street,
        quantity,
        batchId: batchId,
        organizationId: this.cls.get("ORGANIZATION_ID")
      })
      .getRawMany();

    this.logger.debug("input", {
      shipperName: shipper.name,
      shipperAddress: shipper.street,
      quantity
    });

    this.logger.debug(`shipment: fromCommercialInvoice results: ${JSON.stringify(results)}`);

    if (results.length === 0) return null;

    const maxSimilarity = results[0].weighted_similarity;
    if (maxSimilarity < MATCH_THRESHOLD) return null;

    return this.dataSource.getRepository(Shipment).findOne({
      where: { id: results[0].id },
      relations: {
        portOfLoading: {
          country: true
        }
      }
    });
  }

  /*
   * Match shipment by shipment document fields
   *
   * Supported document types: AN / EMF
   *
   * @param fields - Shipment document fields
   * @returns Shipment or null if not found
   */
  async fromShipmentDocument(fields: ShipmentDocumentFields) {
    const keys = ["hblNumber", "cargoControlNumber", "containerNumber"] as ShipmentDocumentFieldsKey[];

    let shipment: Shipment | null = null;

    for (const key of keys) {
      if (!fields[key]) {
        continue;
      }

      shipment = await this.dataSource.getRepository(Shipment).findOne({
        where: { [key]: fields[key], organization: { id: this.cls.get("ORGANIZATION_ID") } },
        relations: {
          portOfLoading: {
            country: true
          }
        }
      });

      if (!shipment) {
        this.logger.debug(`shipment: ${key}: ${fields[key]} not found`);
        continue;
      }

      this.logger.debug(`shipment: ${key}: ${fields[key]} found`);

      break;
    }

    return shipment;
  }
}
