import { Injectable, Logger } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { Location } from "nest-modules";
import { DataSource, QueryRunner } from "typeorm";
import { MatchResult } from "./types";

export interface MatchLocationFields {
  city: string;
  name: string;
}

const MATCH_THRESHOLD = 0.8;
const CITY_WEIGHT = 0.5;
const NAME_WEIGHT = 0.5;

@Injectable()
export class LocationMatcher {
  private readonly logger = new Logger(LocationMatcher.name);

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  async fromLocationDetails(
    fields: MatchLocationFields,
    queryRunner?: QueryRunner
  ): Promise<MatchResult<Location>> {
    const { city, name } = fields;

    const repository = queryRunner
      ? queryRunner.manager.getRepository(Location)
      : this.dataSource.getRepository(Location);

    this.logger.debug(`location: fromLocationDetails fields: ${JSON.stringify(fields)}`);

    const results = await repository.query(
      `
      SELECT 
        location.id as id, city, name,
        (${CITY_WEIGHT} * COALESCE(similarity(city, $1), 0) +
         ${NAME_WEIGHT} * COALESCE(similarity(name, $2), 0)) AS weighted_similarity
      FROM
        location
      ORDER BY 
        weighted_similarity DESC
      LIMIT 10;
    `,
      [city, name]
    );

    this.logger.debug(`location: fromLocationDetails results: ${JSON.stringify(results)}`);

    if (results.length === 0) return { entity: null, confidence: 1 };

    const maxSimilarity = results[0].weighted_similarity;
    if (maxSimilarity < MATCH_THRESHOLD) return { entity: null, confidence: maxSimilarity };

    const location = await repository.findOne({
      where: { id: results[0].id },
      relations: { country: true }
    });

    return { entity: location, confidence: maxSimilarity };
  }
}
