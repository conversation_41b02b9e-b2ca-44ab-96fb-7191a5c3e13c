import { Inject, Injectable } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { Product } from "nest-modules/entities";
import { ClsService } from "nestjs-cls";
import { DataSource, IsNull, QueryRunner, Raw } from "typeorm";

type ProductFeatures = {
  description: string;
  vendorId: number;
  originId: number;
  partNumber?: string;
  sku?: string;
  upc?: string;
  hsCode?: string;
};

@Injectable()
export class ProductMatcher {
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    @Inject(ClsService)
    private readonly clsService: ClsService
  ) {}

  private async findProductWithUniqueIdentifier(
    features: ProductFeatures,
    queryRunner?: QueryRunner
  ): Promise<Product | null> {
    const { partNumber, vendorId, sku, upc, originId } = features;

    // at least one of partNumber, sku, upc is required
    if (!partNumber && !sku && !upc) {
      return null;
    }

    const where = {
      partNumber,
      sku: sku || IsNull(),
      upc: upc || IsNull(),
      origin: { id: originId },
      vendor: { id: vendorId },
      organization: { id: this.clsService.get("ORGANIZATION_ID") }
    };

    const repository = queryRunner
      ? queryRunner.manager.getRepository(Product)
      : this.dataSource.getRepository(Product);

    const product = await repository.findOneBy(where);

    return product;
  }

  private async findProductWithoutUniqueIdentifier(
    features: ProductFeatures,
    queryRunner?: QueryRunner
  ): Promise<Product | null> {
    const { originId, vendorId, hsCode, description } = features;

    const where = {
      origin: { id: originId },
      vendor: { id: vendorId },
      ...(hsCode && { hsCode: Raw((alias) => `LEFT(${alias}, 6) = ?`, [hsCode]) }),
      description: description,
      organization: { id: this.clsService.get("ORGANIZATION_ID") }
    };

    const repository = queryRunner
      ? queryRunner.manager.getRepository(Product)
      : this.dataSource.getRepository(Product);

    const product = await repository.findOneBy(where);

    return product;
  }

  /**
   * Match a product by exact match or similarity
   *
   * @param feature
   * @param queryRunner
   * @returns
   */
  async matchProduct(features: ProductFeatures, queryRunner?: QueryRunner) {
    const hasUniqueIdentifier = !!features.partNumber || !!features.sku || !!features.upc;

    if (hasUniqueIdentifier) {
      const exactMatch = await this.findProductWithUniqueIdentifier(features, queryRunner);

      return exactMatch;
    } else {
      const match = await this.findProductWithoutUniqueIdentifier(features, queryRunner);

      return match;
    }
  }
}
