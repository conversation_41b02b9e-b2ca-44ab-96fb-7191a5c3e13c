import { Test, TestingModule } from "@nestjs/testing";
import { getRepositoryToken, TypeOrmModule } from "@nestjs/typeorm";
import { Product } from "nest-modules";
import { ClsModule, ClsService } from "nestjs-cls";
import { getTypeOrmModule, useRefreshDatabase } from "src/test/hooks/use-test-database";
import { DatabaseCanadaTariffMatcher } from "./database-canada-tariff.matcher";

const products = [
  {
    id: 7777700002,
    description: "Product 2",
    hsCode: "**********",
    partNumber: "PART-2",
    productType: "regular",
    vendor: {
      id: 1443
    },
    origin: {
      id: 51
    },
    organization: {
      id: 1
    },
    lastEditDate: new Date("2025-06-01")
  }
];

describe("DatabaseCanadaTariffMatcher", () => {
  useRefreshDatabase();
  let moduleRef: TestingModule;
  let matcher: DatabaseCanadaTariffMatcher;
  let cls: ClsService<any>;

  beforeAll(async () => {
    moduleRef = await Test.createTestingModule({
      imports: [ClsModule, ...getTypeOrmModule(), TypeOrmModule.forFeature([Product])],
      providers: [DatabaseCanadaTariffMatcher]
    }).compile();

    matcher = moduleRef.get(DatabaseCanadaTariffMatcher);
    cls = moduleRef.get(ClsService);
  });

  afterAll(async () => {
    await moduleRef.close();
  });

  it("should be defined", () => {
    expect(DatabaseCanadaTariffMatcher).toBeDefined();
  });

  it("should throw an error if the organization ID is not set", async () => {
    await cls.runWith(
      {
        ORGANIZATION_ID: null
      },
      async () => {
        await expect(matcher.getClosestTariff("PART-1")).rejects.toThrow();
      }
    );
  });

  it("should find the hsCode using the latest regular product with the same partNumber", async () => {
    const partNumber = "PART-1";
    // create products
    const product1 = {
      id: 123400001,
      description: "Product 1",
      hsCode: "**********",
      partNumber: partNumber,
      productType: "regular",
      vendor: {
        id: 1443
      },
      origin: {
        id: 51
      },
      organization: {
        id: 1
      },
      lastEditDate: new Date("2025-01-01")
    };

    const product2 = {
      id: 123400002,
      description: "Product 2",
      hsCode: "3303000010",
      partNumber: partNumber,
      productType: "regular",
      vendor: {
        id: 1444
      },
      origin: {
        id: 51
      },
      organization: {
        id: 1
      },
      lastEditDate: new Date("2025-06-01")
    };

    const productRepository = moduleRef.get(getRepositoryToken(Product));
    await productRepository.save(product1);
    await productRepository.save(product2);

    await cls.runWith(
      {
        ORGANIZATION_ID: 1
      },
      async () => {
        const result = await matcher.getClosestTariff(partNumber);
        expect(result?.hsCode).toEqual(product2.hsCode);
      }
    );
  });
});
