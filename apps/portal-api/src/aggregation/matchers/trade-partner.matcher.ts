import { TradePartner as DocumentTradePartner } from "@/document/types/document-types.zod";
import { Injectable, Logger } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { TradePartner } from "nest-modules";
import { ClsService } from "nestjs-cls";
import { Brackets, DataSource, Equal, IsNull, Or, QueryRunner } from "typeorm";
import { CountryMatcher } from "./country.matcher";
import { MatchResult } from "./types";

export interface MatchTradePartnerFields extends Partial<DocumentTradePartner> {
  name: string;
  email?: string;
  phoneNumber?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
}

export const MATCH_THRESHOLD = 0.8;

@Injectable()
export class TradePartnerMatcher {
  private readonly logger = new Logger(TradePartnerMatcher.name);

  constructor(
    @InjectDataSource()
    private dataSource: DataSource,
    private readonly countryMatcher: CountryMatcher,
    private readonly cls: ClsService
  ) {}

  async matchTradePartner(
    data: MatchTradePartnerFields,
    queryRunner?: QueryRunner,
    organizationId?: number
  ): Promise<MatchResult<TradePartner>> {
    let tradePartner;

    const tradePartnerRepository = queryRunner
      ? queryRunner.manager.getRepository(TradePartner)
      : this.dataSource.getRepository(TradePartner);

    const countryId = await this.countryMatcher.getCountryId(data.country);

    this.logger.debug(`[MATCHING] ${JSON.stringify(data)}`);

    tradePartner = await tradePartnerRepository.findOne({
      where: {
        name: data.name,
        country: { id: countryId },
        ...(data.city && { city: Or(Equal(data.city), IsNull()) }),
        ...(data.state && { state: Or(Equal(data.state), IsNull()) }),
        organization: { id: organizationId ?? this.cls.get("ORGANIZATION_ID") }
      },
      relations: ["country"]
    });

    if (tradePartner) {
      this.logger.debug(`[EXACT MATCH] ${JSON.stringify(tradePartner)}`);
      return { entity: tradePartner, confidence: 1 };
    }

    const query = await tradePartnerRepository
      .createQueryBuilder("trade_partner")
      .select("trade_partner.id")

      .where("trade_partner.organizationId = :organizationId", {
        organizationId: organizationId ?? this.cls.get("ORGANIZATION_ID")
      });

    if (countryId) {
      query.andWhere(
        new Brackets((qb) => {
          qb.where("trade_partner.countryId = :countryId", { countryId: countryId }).orWhere(
            "trade_partner.countryId IS NULL"
          );
        })
      );
    }
    if (data.city) {
      query.andWhere("similarity(trade_partner.city, :city) > 0.9", { city: data.city });
    }

    if (data.state) {
      query.andWhere("similarity(trade_partner.state, :state) > 0.9", { state: data.state });
    }

    query.andWhere("similarity(trade_partner.name, :name) > 0.9", { name: data.name });

    if (data.address) {
      query.andWhere(
        new Brackets((qb) => {
          qb.where("similarity(trade_partner.address, :address) > 0.5", { address: data.address }).orWhere(
            "trade_partner.address IS NULL"
          );
        })
      );
      query.orderBy(
        "similarity(trade_partner.name, :name) + similarity(trade_partner.address, :address)",
        "DESC"
      );
    } else {
      query.orderBy("similarity(trade_partner.name, :name)", "DESC");
    }

    const result = await query.limit(1).getRawOne();

    this.logger.debug(`trade-partner: matchTradePartner results: ${JSON.stringify(result)}`);

    if (!result) return { entity: null };

    tradePartner = await tradePartnerRepository.findOne({
      where: {
        id: result.trade_partner_id
      },
      relations: {
        country: true
      }
    });

    this.logger.debug(`[MATCH] ${JSON.stringify(tradePartner)}`);

    return {
      entity: tradePartner,
      confidence: 0.5
    };
  }
}
