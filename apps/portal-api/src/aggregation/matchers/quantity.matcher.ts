import { OpenAIService } from "@/llm";
import { Injectable } from "@nestjs/common";
import { zodResponseFormat } from "openai/helpers/zod";
import { z } from "zod";

export interface QuantityConversionInput {
  goodsDescription: string;
  unitOfMeasure: string;
  quantity?: number;
  quantityUOM?: string;
  weight?: number;
  weightUOM?: string;
  volume?: number;
  volumeUOM?: string;
}

export interface QuantityConversionOutput {
  value: number;
  reasoning: string;
}

const schema = z.object({
  value: z.number(),
  reasoning: z.string()
});

@Injectable()
export class QuantityMatcher {
  constructor(private readonly openAIService: OpenAIService) {}

  async convert({
    goodsDescription,
    quantity,
    unitOfMeasure,
    quantityUOM,
    weight,
    weightUOM,
    volume,
    volumeUOM
  }: QuantityConversionInput): Promise<QuantityConversionOutput> {
    // Validate that at least one measurement pair exists
    const hasQuantity = quantity !== undefined && quantityUOM;
    const hasWeight = weight !== undefined && weightUOM;
    const hasVolume = volume !== undefined && volumeUOM;

    if (!hasQuantity && !hasWeight && !hasVolume) {
      throw new Error(
        "At least one measurement pair (quantity & quantityUOM, weight & weightUOM, or volume & volumeUOM) must be provided."
      );
    }

    let measurements = `- **Goods Description:** ${goodsDescription}`;

    if (hasQuantity) {
      measurements += `\n- **Quantity:** ${quantity} ${quantityUOM}`;
    }

    if (hasWeight) {
      measurements += `\n- **Weight:** ${weight} ${weightUOM}`;
    }

    if (hasVolume) {
      measurements += `\n- **Volume:** ${volume} ${volumeUOM}`;
    }

    const prompt = `
      Please calculate the equivalent value in **${unitOfMeasure}** based on the following measurements:

      ${measurements}

      - guess the density of the goods if needed and use it to calculate the value
    `;

    try {
      const response = await this.openAIService.createChatCompletion({
        model: "gpt-4o",
        messages: [{ role: "system", content: prompt }],
        response_format: zodResponseFormat(schema, "unit_conversion"),
        temperature: 0.5,
        top_p: 0.2
      });

      const result = JSON.parse(response.choices[0].message.content);
      return result;
    } catch (error) {
      throw new Error("Failed to communicate with OpenAI");
    }
  }
}
