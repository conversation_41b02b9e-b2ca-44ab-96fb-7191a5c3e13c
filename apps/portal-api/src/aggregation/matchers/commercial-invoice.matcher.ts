import { Injectable, Logger } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { DocumentType } from "nest-modules";
import { ClsService } from "nestjs-cls";
import { DataSource } from "typeorm";

export interface MatchInvoiceFields {
  lines: string;
  shipmentId?: number;
  vendor?: string;
  purchaser?: string;
  shipTo?: string;
}

interface MatchInvoiceResult {
  id: number;
  documentType: string;
  similarity: number;
}

const MATCH_THRESHOLD = 0.4;

@Injectable()
export class CommercialInvoiceMatcher {
  private readonly logger = new Logger(CommercialInvoiceMatcher.name);

  constructor(
    @InjectDataSource()
    private dataSource: DataSource,
    private readonly cls: ClsService
  ) {}

  async getDocumentTypeId(documentType: string): Promise<number | null> {
    const result = await this.dataSource
      .getRepository(DocumentType)
      .findOne({ where: { name: documentType } });

    if (!result) return null;

    return result.id;
  }

  async getMatchingDocument(
    documentType: string,
    fields: MatchInvoiceFields
  ): Promise<MatchInvoiceResult | null> {
    const documentTypeId = await this.getDocumentTypeId(documentType);

    if (documentTypeId == null) return null;

    const { lines, shipmentId, vendor, purchaser, shipTo } = fields;

    let fieldCount = 0;
    if (lines) fieldCount++;
    if (vendor) fieldCount++;
    if (shipTo) fieldCount++;

    if (fieldCount === 0) return null;

    let WEIGHT = 1 / fieldCount;

    const query = await this.dataSource.query(
      `
        WITH
          pivot AS (
            SELECT
              document.id as id,
              document.name as "documentName",
              MAX(
                CASE
                  WHEN document_field.name = 'shipTo' THEN value
                END
              ) AS "shipTo",
              MAX(
                CASE
                  WHEN document_field.name = 'vendor' THEN value
                END
              ) AS "vendor",
              MAX(
                CASE
                  WHEN document_field.name = 'commercialInvoiceLines' THEN value
                END
              ) AS "commercialInvoiceLines",
              document."shipmentId",
              file.name as "fileName"
            FROM
              document
              LEFT JOIN document_field ON document.id = document_field."documentId"
              LEFT JOIN file ON document."fileId" = file.id
            WHERE
              document."documentTypeId" = $1
              AND document."organizationId" = $2
              AND document."shipmentId" = $3
              AND document."isHidden" = false
            GROUP BY
              document.id, document.name, document."shipmentId", file.name
          )
        SELECT
          pivot.id as "documentId",
          pivot."documentName",
          pivot."shipTo",
          pivot."vendor",
          pivot."commercialInvoiceLines" as value,
          pivot."fileName",
          pivot."shipmentId",
          (${WEIGHT} * COALESCE(similarity(pivot."shipTo", $4), 0) +
           ${WEIGHT} * COALESCE(similarity(pivot."vendor", $5), 0) +
           ${WEIGHT} * COALESCE(similarity(pivot."commercialInvoiceLines", $6), 0)) AS "similarity"
        FROM
          pivot
        ORDER BY
          "similarity" DESC
        LIMIT 10;
      `,
      [documentTypeId, this.cls.get("ORGANIZATION_ID"), shipmentId, shipTo || "", vendor || "", lines || ""]
    );

    const result = query.map((r) => ({
      id: r.documentId,
      documentType: r.documentName,
      similarity: r.similarity
    }));

    if (result.length === 0) return null;

    const maxSimilarity = result[0].similarity;

    this.logger.debug("maxSimilarity", result[0]);

    if (maxSimilarity < MATCH_THRESHOLD) return null;

    this.logger.debug(
      `Found matching ${documentType} with similarity ${maxSimilarity}`,
      JSON.stringify(result[0])
    );
    return result[0];
  }

  async matchCommercialInvoice(fields: MatchInvoiceFields) {
    return this.getMatchingDocument("COMMERCIAL_INVOICE", fields);
  }

  async matchPackingList(fields: MatchInvoiceFields) {
    return this.getMatchingDocument("PACKING_LIST", fields);
  }
}
