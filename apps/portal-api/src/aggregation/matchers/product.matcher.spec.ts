import { Test, TestingModule } from "@nestjs/testing";
import { getRepositoryToken, TypeOrmModule } from "@nestjs/typeorm";
import { Product } from "nest-modules/entities";
import { ClsModule, ClsService } from "nestjs-cls";
import { getTypeOrmModule, useRefreshDatabase } from "src/test/hooks/use-test-database";
import { Repository } from "typeorm";
import { ProductMatcher } from "./product.matcher";

describe("ProductMatcher", () => {
  useRefreshDatabase();
  let moduleRef: TestingModule;
  let matcher: ProductMatcher;
  let cls: ClsService<any>;
  let productRepository: Repository<Product>;

  beforeAll(async () => {
    moduleRef = await Test.createTestingModule({
      imports: [ClsModule, ...getTypeOrmModule(), TypeOrmModule.forFeature([Product])],
      providers: [ProductMatcher]
    }).compile();

    matcher = moduleRef.get(ProductMatcher);
    cls = moduleRef.get(ClsService);
    productRepository = moduleRef.get(getRepositoryToken(Product));
  });

  afterAll(async () => {
    await moduleRef.close();
  });

  describe("matchProduct", () => {
    it("should find product with part number", async () => {
      const product = await productRepository.save({
        hsCode: "**********",
        description: "Test Product",
        partNumber: "ABC123",
        vendor: { id: 1443 },
        origin: { id: 51 },
        organization: { id: 1 }
      });

      const features = {
        description: "Test Product",
        vendorId: 1443,
        originId: 51,
        partNumber: "ABC123",
        sku: undefined,
        upc: undefined
      };

      await cls.runWith({ ORGANIZATION_ID: 1 }, async () => {
        const result = await matcher.matchProduct(features);
        expect(result?.id).toEqual(product.id);
      });
    });

    it("should find product with SKU", async () => {
      const product = await productRepository.save({
        hsCode: "**********",
        partNumber: "ABC123",
        description: "Test Product",
        sku: "SKU123",
        vendor: { id: 1443 },
        origin: { id: 51 },
        organization: { id: 1 }
      });

      const features = {
        description: "Test Product",
        vendorId: 1443,
        originId: 51,
        partNumber: undefined,
        sku: "SKU123",
        upc: undefined
      };

      await cls.runWith({ ORGANIZATION_ID: 1 }, async () => {
        const result = await matcher.matchProduct(features);
        expect(result?.id).toEqual(product.id);
      });
    });

    it("should find product with UPC", async () => {
      const product = await productRepository.save({
        hsCode: "**********",
        description: "Test Product",
        partNumber: "",
        upc: "UPC123",
        vendor: { id: 1443 },
        origin: { id: 51 },
        organization: { id: 1 }
      });

      const features = {
        description: "Test Product",
        vendorId: 1443,
        originId: 51,
        partNumber: undefined,
        sku: undefined,
        upc: "UPC123"
      };

      await cls.runWith({ ORGANIZATION_ID: 1 }, async () => {
        const result = await matcher.matchProduct(features);
        expect(result?.id).toEqual(product.id);
      });
    });

    it("should find product by description and vendor/origin", async () => {
      const product = await productRepository.save({
        description: "Test Product",
        partNumber: "ABC123",
        hsCode: "**********",
        vendor: { id: 1443 },
        origin: { id: 51 },
        organization: { id: 1 }
      });

      const features = {
        description: "Test Product",
        vendorId: 1443,
        originId: 51,
        hsCode: undefined
      };

      await cls.runWith({ ORGANIZATION_ID: 1 }, async () => {
        const result = await matcher.matchProduct(features);
        expect(result?.id).toEqual(product.id);
      });
    });

    it("should return null if no product found", async () => {
      const features = {
        description: "Nonexistent Product",
        vendorId: 1443,
        originId: 51,
        partNumber: "DOESNOTEXIST"
      };

      await cls.runWith({ ORGANIZATION_ID: 1 }, async () => {
        const result = await matcher.matchProduct(features);
        expect(result).toBeNull();
      });
    });
  });
});
