import { Test, TestingModule } from "@nestjs/testing";
import { Country } from "nest-modules";
import { DataSource, Repository } from "typeorm";
import { CountryMatcher } from "./country.matcher";

describe("CountryMatcher", () => {
  let countryMatcher: CountryMatcher;
  let dataSource: DataSource;
  let countryRepository: Repository<Country>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CountryMatcher,
        {
          provide: DataSource,
          useValue: {
            query: jest.fn(),
            getRepository: jest.fn()
          }
        }
      ]
    }).compile();

    countryMatcher = module.get<CountryMatcher>(CountryMatcher);
    dataSource = module.get<DataSource>(DataSource);
    countryRepository = {
      createQueryBuilder: jest.fn()
    } as any;

    (dataSource.getRepository as jest.Mock).mockReturnValue(countryRepository);
  });

  it("should return country when a valid entity is provided", async () => {
    const mockEntity = { name: "United States" } as Country;
    const mockCountry = {
      id: 1,
      name: "United States",
      alpha3: "USA",
      alpha2: "US"
    };

    // Mock the query method to handle CREATE EXTENSION
    (dataSource.query as jest.Mock).mockResolvedValue(undefined);

    // Mock the query builder
    const mockQueryBuilder: any = {
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      setParameters: jest.fn().mockReturnThis(),
      getOne: jest.fn().mockResolvedValue(mockCountry)
    };
    (countryRepository.createQueryBuilder as jest.Mock).mockReturnValue(mockQueryBuilder);

    const result = await countryMatcher.getCountryEntity(mockEntity);

    expect(dataSource.query).toHaveBeenCalledWith(
      expect.stringContaining("CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;")
    );
    expect(countryRepository.createQueryBuilder).toHaveBeenCalledWith("country");
    expect(mockQueryBuilder.select).toHaveBeenCalledWith([
      "country.id",
      "country.name",
      "country.alpha3",
      "country.alpha2"
    ]);
    expect(mockQueryBuilder.addSelect).toHaveBeenCalledWith(
      `LEVENSHTEIN(country.name, :searchName) as distance`
    );
    expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith("distance", "ASC");
    expect(mockQueryBuilder.limit).toHaveBeenCalledWith(1);
    expect(mockQueryBuilder.setParameters).toHaveBeenCalledWith({
      searchName: "united states"
    });
    expect(result).toEqual(mockCountry);
  });

  it("should throw an error if entity is null", async () => {
    const result = await countryMatcher.getCountryEntity(null);
    expect(result).toBeNull();
  });

  it("should throw an error if country name is too short", async () => {
    const mockEntity = { name: "U" } as Country;
    await expect(countryMatcher.getCountryEntity(mockEntity)).rejects.toThrow("Country name is too short");
  });

  it("should throw an error if country is not found", async () => {
    const mockEntity = { name: "Unknown Country" } as Country;

    (dataSource.query as jest.Mock).mockResolvedValue(undefined);

    const mockQueryBuilder: any = {
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      setParameters: jest.fn().mockReturnThis(),
      getOne: jest.fn().mockResolvedValue(null)
    };
    (countryRepository.createQueryBuilder as jest.Mock).mockReturnValue(mockQueryBuilder);

    await expect(countryMatcher.getCountryEntity(mockEntity)).rejects.toThrow(
      "Country not found for Unknown Country"
    );
  });

  it("should select the correct column based on name length", async () => {
    const mockEntityShort = { name: "US" } as Country;
    const mockEntityLong = { name: "Canada" } as Country;

    const mockCountry = {
      id: 2,
      name: "Canada",
      alpha3: "CAN",
      alpha2: "CA"
    };

    // Mock for short name (length 2)
    const mockQueryBuilderShort: any = {
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      setParameters: jest.fn().mockReturnThis(),
      getOne: jest.fn().mockResolvedValue(mockCountry)
    };
    (countryRepository.createQueryBuilder as jest.Mock).mockReturnValue(mockQueryBuilderShort);

    await countryMatcher.getCountryEntity(mockEntityShort);
    expect(mockQueryBuilderShort.addSelect).toHaveBeenCalledWith(
      `LEVENSHTEIN(country.alpha2, :searchName) as distance`
    );

    // Mock for long name (length > 3)
    const mockQueryBuilderLong: any = {
      select: jest.fn().mockReturnThis(),
      addSelect: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      setParameters: jest.fn().mockReturnThis(),
      getOne: jest.fn().mockResolvedValue(mockCountry)
    };
    (countryRepository.createQueryBuilder as jest.Mock).mockReturnValue(mockQueryBuilderLong);

    await countryMatcher.getCountryEntity(mockEntityLong);
    expect(mockQueryBuilderLong.addSelect).toHaveBeenCalledWith(
      `LEVENSHTEIN(country.name, :searchName) as distance`
    );
  });
});
