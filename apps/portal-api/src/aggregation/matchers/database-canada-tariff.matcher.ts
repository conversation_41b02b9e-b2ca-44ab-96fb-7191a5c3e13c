import { Injectable } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { CanadaTariff, Product, ProductType } from "nest-modules";
import { ClsService } from "nestjs-cls";
import { DataSource, IsNull, Not } from "typeorm";

@Injectable()
export class DatabaseCanadaTariffMatcher {
  constructor(
    @InjectDataSource()
    private dataSource: DataSource,
    private readonly cls: ClsService
  ) {}

  async getClosestTariff(partNumber: string): Promise<CanadaTariff | null> {
    const organizationId = this.cls.get("ORGANIZATION_ID");

    if (!organizationId) {
      throw new Error("Organization ID is not set");
    }

    const product = await this.dataSource.manager.findOne(Product, {
      where: {
        partNumber: partNumber,
        productType: ProductType.REGULAR,
        hsCode: Not(IsNull()),
        organization: { id: organizationId }
      },
      order: {
        lastEditDate: "DESC"
      },
      select: {
        id: true,
        hsCode: true,
        lastEditDate: true
      }
    });

    if (!product || !product.hsCode) {
      return null;
    }

    const tariff = await this.dataSource.manager.findOne(CanadaTariff, {
      where: {
        hsCode: product?.hsCode
      }
    });

    return tariff;
  }
}
