import { PromptTemplateManager } from "@/llm";
// import { DeepSeekService as LLMService } from "@/llm/deepseek.service";
import { OpenAIService as LLMService } from "@/llm/openai.service";
import { Cache, CACHE_MANAGER } from "@nestjs/cache-manager";
import { forwardRef, Inject, Logger } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { createHash } from "crypto";
import { CanadaTariff } from "nest-modules";
import { DataSource, LessThanOrEqual, Like, MoreThanOrEqual, Raw } from "typeorm";
import { HS_CODE_CHAPTERS } from "../constants/hs-code-chapters";

interface HSCodePrefixSuggestion {
  hsCodePrefix: string;
  reason: string;
}

interface HSCodeSuggestion {
  hsCode: string;
  reason: string;
}

interface TariffCodeSuggestion {
  tariff: CanadaTariff | null;
  reason: string;
}

const CACHE_KEY_PREFIX = "canada-tariff-matcher";

export class CanadaTariffMatcher {
  private readonly logger = new Logger(CanadaTariffMatcher.name);

  /**
   * LLM model to use for the Canada tariff matcher
   */
  private readonly MODEL_NAME = "o3-mini";

  /**
   * Configuration for the llm model
   */
  private readonly MODEL_CONFIG = {
    // reasoning_effort: "medium",
  };

  constructor(
    @InjectDataSource()
    private dataSource: DataSource,
    @Inject(forwardRef(() => LLMService))
    private readonly llmService: LLMService,
    @Inject(forwardRef(() => PromptTemplateManager))
    private readonly promptTemplateManager: PromptTemplateManager,
    @Inject(CACHE_MANAGER)
    private readonly cacheManager: Cache
  ) {}

  async isValidHSCode(hsCode: string): Promise<boolean> {
    if (typeof hsCode !== "string" || hsCode.length !== 10) return false;

    const currentDate = new Date();

    return (
      (await this.dataSource.getRepository(CanadaTariff).count({
        where: {
          hsCode,
          effectiveDate: LessThanOrEqual(currentDate),
          expiryDate: MoreThanOrEqual(currentDate)
        }
      })) > 0
    );
  }

  /**
   * Get the tariff for a given HS code
   *
   * @param hsCode
   * @returns
   */
  async getTariff(hsCode: string): Promise<CanadaTariff> {
    const [tariffs, count] = await this.dataSource.getRepository(CanadaTariff).findAndCount({
      where: {
        hsCode: Like(hsCode + "%")
      }
    });

    switch (count) {
      case 0:
        throw new Error("No tariff found for hsCode: " + hsCode);
      case 1:
        return tariffs[0];
      default:
        throw new Error("Multiple tariffs found for hsCode: " + hsCode);
    }
  }

  /**
   * Get the closest tariff for a given HS code and description
   * @param hsCode - Customer-suggested HS code.
   * @param description - Description of the goods.
   * @returns
   */
  async getClosestTariff({
    hsCode,
    description
  }: {
    hsCode?: string | null;
    description: string;
  }): Promise<TariffCodeSuggestion> {
    const canadaTariffRepository = this.dataSource.getRepository(CanadaTariff);

    // Find existing tariff by HS code
    const tariff =
      typeof hsCode === "string" && hsCode.length === 10
        ? await canadaTariffRepository.findOne({ where: { hsCode } })
        : null;
    this.logger.debug(
      `Existing Tariff: ${tariff?.id}, Effective Date: ${tariff?.effectiveDate}, Expiry Date: ${tariff?.expiryDate}`
    );
    // TODO: do not return tariff if it is expired or not effective
    if (tariff)
      return {
        tariff,
        reason: "Found tariff record for provided HS code"
      };

    // If 2-digit HS code chapter is valid, use it
    // Otherwise, ask LLM for suggestion
    let hsCodeChapter: string | null = null;
    if (
      typeof hsCode === "string" &&
      hsCode.length >= 2 &&
      HS_CODE_CHAPTERS.some((chapter) => chapter.chapter.toString().padStart(2, "0") === hsCode.slice(0, 2))
    )
      hsCodeChapter = hsCode.slice(0, 2);
    else {
      // Create chapter suggestion prompt
      const chapterPrompt = this.promptTemplateManager.getPromptMultipleSectionsFromTemplate(
        "suggest-hs-code-chapter",
        {
          chapters: HS_CODE_CHAPTERS.map((chapter) => ({
            hsCodePrefix: chapter.chapter.toString().padStart(2, "0"),
            description: chapter.description
          })),
          input: {
            hsCode,
            description
          }
        }
      );

      // Ask LLM for chapter suggestion and parse the response
      let chapterSuggestion: HSCodePrefixSuggestion;
      try {
        const chapterResponse = await this.llmService.createChatCompletion(
          {
            model: this.MODEL_NAME,
            messages: chapterPrompt,
            response_format: { type: "json_object" },
            ...this.MODEL_CONFIG
          },
          CanadaTariffMatcher.name
        );
        this.logger.verbose(`Chapter Response: ${chapterResponse.choices[0].message.content}`);
        chapterSuggestion = JSON.parse(chapterResponse.choices[0].message.content) as HSCodePrefixSuggestion;
      } catch (error) {
        this.logger.error(
          `Error while getting chapter suggestion: ${error instanceof Error ? error.message : String(error)}`
        );
        // throw new InternalServerErrorException(`Failed to get chapter suggestion`);
        return {
          tariff: null,
          reason: "Failed to get HS code chapter suggestion"
        };
      }
      if (
        !(chapterSuggestion?.hsCodePrefix === null || typeof chapterSuggestion.hsCodePrefix === "string") ||
        !(chapterSuggestion?.reason === null || typeof chapterSuggestion.reason === "string")
      )
        return {
          tariff: null,
          reason: "Invalid HS code chapter suggestion response"
        };

      // If no HS code chapter is suggested, return null
      if (chapterSuggestion.hsCodePrefix === null)
        return {
          tariff: null,
          reason: chapterSuggestion.reason || "No HS code chapter is suggested by LLM"
        };

      hsCodeChapter = chapterSuggestion.hsCodePrefix;
    }
    this.logger.verbose(`Suggested HS code chapter: ${hsCodeChapter}`);

    if (!HS_CODE_CHAPTERS.some((chapter) => chapter.chapter.toString().padStart(2, "0") === hsCodeChapter))
      return {
        tariff: null,
        reason: `No HS code chapter found for the suggested HS code chapter: ${hsCodeChapter}`
      };

    // Get canada tariff records under the suggested HS code chapter
    const canadaTariffList = await canadaTariffRepository.find({
      select: ["id", "hsCode", "description", "effectiveDate", "expiryDate", "lastEditDate", "uom"],
      where: {
        hsCode: Raw((alias) => `${alias} LIKE '${hsCodeChapter}%' AND LENGTH(${alias}) = 10`)
      }
    });

    // Map tariffs into 4 digit categories
    const tariffCategoryMappings = new Map<string, { description: string; tariffs: Array<CanadaTariff> }>();
    for (const tariff of canadaTariffList) {
      const hsCodePrefix = tariff.hsCode.slice(0, 4);
      if (!tariffCategoryMappings.has(hsCodePrefix)) {
        tariffCategoryMappings.set(hsCodePrefix, {
          description: tariff.description,
          tariffs: [tariff]
        });
      } else {
        const category = tariffCategoryMappings.get(hsCodePrefix);
        const maxIndex = Math.min(category.description.length, tariff.description.length);
        let index = 0;
        while (index < maxIndex && category.description[index] === tariff.description[index]) index++;
        category.description = category.description.slice(0, index);
        category.tariffs.push(tariff);
        tariffCategoryMappings.set(hsCodePrefix, category);
      }
    }

    // If 4-digit HS code prefix is valid, use it
    // Otherwise, ask LLM for suggestion
    let hsCodeCategory: string | null = null;

    if (
      typeof hsCode === "string" &&
      hsCode.length >= 4 &&
      hsCode.slice(0, 2) === hsCodeChapter &&
      tariffCategoryMappings.has(hsCode.slice(0, 4))
    )
      hsCodeCategory = hsCode.slice(0, 4);
    else {
      // Generate category suggestion prompt
      const categoryPrompt = this.promptTemplateManager.getPromptMultipleSectionsFromTemplate(
        "suggest-hs-code-category",
        {
          categories: Array.from(tariffCategoryMappings.entries())
            .filter(([hsCodePrefix]) => hsCodePrefix.startsWith(hsCodeChapter))
            .map(([hsCodePrefix, { description }]) => ({
              hsCodePrefix,
              description
            })),
          input: {
            hsCode,
            description
          }
        }
      );

      // Ask LLM for category suggestion and parse the response
      let categorySuggestion: HSCodePrefixSuggestion;
      try {
        const categoryResponse = await this.llmService.createChatCompletion(
          {
            model: this.MODEL_NAME,
            messages: categoryPrompt,
            response_format: { type: "json_object" },
            ...this.MODEL_CONFIG
          },
          CanadaTariffMatcher.name
        );
        this.logger.verbose(`Category Response: ${categoryResponse.choices[0].message.content}`);

        categorySuggestion = JSON.parse(
          categoryResponse.choices[0].message.content
        ) as HSCodePrefixSuggestion;
      } catch (error) {
        this.logger.error(
          `Error while getting category suggestion: ${error instanceof Error ? error.message : String(error)}`
        );
        return {
          tariff: null,
          reason: "Failed to get HS code 4-digit category suggestion"
        };
      }

      if (
        !(categorySuggestion?.hsCodePrefix === null || typeof categorySuggestion.hsCodePrefix === "string") ||
        !(categorySuggestion?.reason === null || typeof categorySuggestion.reason === "string")
      )
        return {
          tariff: null,
          reason: "Invalid HS code 4-digit category suggestion response"
        };

      // If no HS code category is suggested, return null
      if (categorySuggestion.hsCodePrefix === null)
        return {
          tariff: null,
          reason: categorySuggestion.reason || "No HS code 4-digit category is suggested by LLM"
        };

      hsCodeCategory = categorySuggestion.hsCodePrefix;
    }
    this.logger.verbose(`Suggested HS code category: ${hsCodeCategory}`);

    if (!tariffCategoryMappings.has(hsCodeCategory))
      return {
        tariff: null,
        reason: `No tariff record found for the suggested HS code category: ${hsCodeCategory}`
      };
    // throw new NotFoundException(`No tariff found for HS code category: ${hsCodeCategory}`);

    // Ask LLM for the complete HS code suggestion
    const hsCodePrompt = this.promptTemplateManager.getPromptMultipleSectionsFromTemplate("suggest-hs-code", {
      tariffs: tariffCategoryMappings.get(hsCodeCategory).tariffs.map((tariff) => ({
        hsCode: tariff.hsCode,
        description: tariff.description
      })),
      input: {
        hsCode,
        description
      }
    });

    // Ask LLM for the complete HS code suggestion and parse the response
    let hsCodeSuggestion: HSCodeSuggestion;
    try {
      const hsCodeResponse = await this.llmService.createChatCompletion(
        {
          model: this.MODEL_NAME,
          messages: hsCodePrompt,
          response_format: { type: "json_object" },
          ...this.MODEL_CONFIG
        },
        CanadaTariffMatcher.name
      );
      this.logger.verbose(`HS Code Response: ${hsCodeResponse.choices[0].message.content}`);
      hsCodeSuggestion = JSON.parse(hsCodeResponse.choices[0].message.content) as HSCodeSuggestion;
    } catch (error) {
      this.logger.error(
        `Error while getting HS code suggestion: ${error instanceof Error ? error.message : String(error)}`
      );
      return {
        tariff: null,
        reason: "Failed to get HS code suggestion"
      };
      // throw new InternalServerErrorException(`Failed to get HS code suggestion`);
    }

    // Validate LLM resposne
    if (
      !(hsCodeSuggestion?.hsCode === null || typeof hsCodeSuggestion.hsCode === "string") ||
      !(hsCodeSuggestion?.reason === null || typeof hsCodeSuggestion.reason === "string")
    )
      return {
        tariff: null,
        reason: "Invalid HS code suggestion response"
      };
    // throw new InternalServerErrorException(`Invalid HS code suggestion`);

    if (hsCodeSuggestion.hsCode === null)
      return {
        tariff: null,
        reason: hsCodeSuggestion.reason || "No HS code suggestion provided"
      };

    // Find the tariff record that matches the suggested HS code
    const suggestedTariff = canadaTariffList.find((tariff) => tariff.hsCode === hsCodeSuggestion.hsCode);
    this.logger.debug(`Suggested HS Code: ${suggestedTariff?.hsCode}`);
    if (!suggestedTariff)
      return {
        tariff: null,
        reason: `No tariff record found for the suggested HS code: ${hsCodeSuggestion.hsCode}`
      };

    // TODO: do not return tariff if it is expired or not effective

    const result = {
      tariff: suggestedTariff,
      reason: hsCodeSuggestion.reason || "No reason provided"
    };

    return result;
  }

  /**
   * This is a wrapper around the getClosestTariff method that caches the result for 60 minutes.
   *
   * If multiple concurrent requests are made for the same key, only one will be executed and the others will wait for the result.
   *
   * @param hsCode - Customer-suggested HS code.
   * @param description - Description of the goods.
   * @returns
   */
  async getClosestTariffWithCache({
    hsCode,
    description
  }: {
    hsCode?: string | null;
    description: string;
  }): Promise<TariffCodeSuggestion> {
    const hash = createHash("sha256")
      .update(`${description}${hsCode ? `-${hsCode}` : ""}`)
      .digest("hex");
    const cacheKey = `${CACHE_KEY_PREFIX}:${hash}`;
    return this.cacheManager.wrap(
      cacheKey,
      async () => {
        return this.getClosestTariff({ hsCode, description });
      },
      {
        ttl: 3600 * 1000 // 60 minutes
      }
    );
  }
}
