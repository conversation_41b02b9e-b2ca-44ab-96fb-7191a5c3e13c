import { Injectable } from "@nestjs/common";
import { Country } from "nest-modules";
import { DataSource } from "typeorm";

@Injectable()
export class CountryMatcher {
  constructor(private readonly dataSource: DataSource) {}

  async getCountryEntity(countryNameOrIsoCode: string) {
    if (countryNameOrIsoCode === null || countryNameOrIsoCode === undefined) return null;

    const map = {
      2: "alpha2",
      3: "alpha3"
    };

    const nameLength = countryNameOrIsoCode.length;

    if (nameLength < 2) {
      throw new Error("Country name is too short");
    }

    const column = nameLength > 3 ? "name" : map[nameLength];
    const searchName = nameLength > 3 ? countryNameOrIsoCode.toLowerCase() : countryNameOrIsoCode;

    // run raw query to enable levenshtein distance

    await this.dataSource.query(`
      CREATE EXTENSION IF NOT EXISTS pg_trgm;
    `);

    const country = await this.dataSource
      .getRepository(Country)
      .createQueryBuilder("country")
      .select(["country.id", "country.name", "country.alpha3", "country.alpha2"])
      .addSelect(`similarity(country.${column}, :searchName) as similarity`)
      .orderBy("similarity", "DESC")
      .limit(1)
      .setParameters({ searchName })
      .getOne();

    if (!country) {
      throw new Error(`Country not found for ${countryNameOrIsoCode}`);
    }

    return country;
  }

  async getCountryId(countryNameOrIsoCode: string) {
    const country = await this.getCountryEntity(countryNameOrIsoCode);
    return country?.id;
  }
}
