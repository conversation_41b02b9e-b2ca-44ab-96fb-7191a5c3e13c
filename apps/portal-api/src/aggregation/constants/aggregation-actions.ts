import { DocumentAggregationAction } from "nest-modules";
import {
  ArrivalNoticeDocument,
  CertificateDocument,
  EMFDocument,
  InvoiceDocument,
  ShipmentDocument
} from "./document-types";

export const DocumentAggregationActionMap: Record<DocumentAggregationAction, string[]> = {
  [DocumentAggregationAction.CREATE_SHIPMENT]: [
    ShipmentDocument.HOUSE_OCEAN_BILL_OF_LADING,
    ShipmentDocument.AIR_WAYBILL,
    ShipmentDocument.ROAD_BILL_OF_LADING,
    ShipmentDocument.PARS_OVERLAY,

    // FIXME: currently we are using these documents for create shipment
    ArrivalNoticeDocument.ROAD_ARRIVAL_NOTICE,
    ArrivalNoticeDocument.OCEAN_ARRIVAL_NOTICE,
    ArrivalNoticeDocument.AIR_ARRIVAL_NOTICE,
    EMFDocument.OCEAN_EMF,
    EMFDocument.AIR_EMF
  ],
  [DocumentAggregationAction.UPDATE_SHIPMENT]: [],
  [DocumentAggregationAction.CREATE_COMMERCIAL_INVOICE]: [InvoiceDocument.COMMERCIAL_INVOICE],
  [DocumentAggregationAction.CREATE_CERTIFICATE_OF_ORIGIN]: [
    CertificateDocument.USMCA_CERTIFICATE_OF_ORIGIN,
    CertificateDocument.CERTIFICATE_OF_ORIGIN
  ]
};

export const skippedDocumentTypes = [InvoiceDocument.PACKING_LIST];
