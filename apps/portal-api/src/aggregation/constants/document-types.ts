export enum ShipmentDocument {
  HOUSE_OCEAN_BILL_OF_LADING = "HOUSE_OCEAN_BILL_OF_LADING",
  AIR_WAYBILL = "AIR_WAYBILL",
  ROAD_BILL_OF_LADING = "ROAD_BILL_OF_LADING",
  ROAD_ARRIVAL_NOTICE = "ROAD_ARRIVAL_NOTICE",
  PARS_OVERLAY = "PARS_OVERLAY"
}

export enum ArrivalNoticeDocument {
  OCEAN_ARRIVAL_NOTICE = "OCEAN_ARRIVAL_NOTICE",
  AIR_ARRIVAL_NOTICE = "AIR_ARRIVAL_NOTICE",
  ROAD_ARRIVAL_NOTICE = "ROAD_ARRIVAL_NOTICE"
}

export enum InvoiceDocument {
  COMMERCIAL_INVOICE = "COMMERCIAL_INVOICE",
  PACKING_LIST = "PACKING_LIST"
}

export enum EMFDocument {
  OCEAN_EMF = "OCEAN_E_MANIFEST",
  AIR_EMF = "AIR_E_MANIFEST",
  ROAD_EMF = "ROAD_E_MANIFEST"
}

export enum CertificateDocument {
  CERTIFICATE_OF_ORIGIN = "CERTIFICATE_OF_ORIGIN",
  USMCA_CERTIFICATE_OF_ORIGIN = "USMCA_CERTIFICATE_OF_ORIGIN"
}

export const DOCUMENT_PRIORITY = {
  // certificates
  [CertificateDocument.CERTIFICATE_OF_ORIGIN]: 4,
  [CertificateDocument.USMCA_CERTIFICATE_OF_ORIGIN]: 4,

  // shipment
  [ShipmentDocument.HOUSE_OCEAN_BILL_OF_LADING]: 2,
  [ShipmentDocument.AIR_WAYBILL]: 2,
  [ShipmentDocument.ROAD_ARRIVAL_NOTICE]: 2,
  [ShipmentDocument.PARS_OVERLAY]: 2,

  // arrival notice
  [ArrivalNoticeDocument.OCEAN_ARRIVAL_NOTICE]: 3,
  [ArrivalNoticeDocument.AIR_ARRIVAL_NOTICE]: 3,
  [ShipmentDocument.ROAD_BILL_OF_LADING]: 3,

  // EMF documents
  [EMFDocument.OCEAN_EMF]: 4,
  [EMFDocument.AIR_EMF]: 4,
  [EMFDocument.ROAD_EMF]: 4,

  // invoice documents
  [InvoiceDocument.COMMERCIAL_INVOICE]: 5,
  [InvoiceDocument.PACKING_LIST]: 5
};
