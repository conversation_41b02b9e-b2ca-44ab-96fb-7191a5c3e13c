import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString, MinLength } from "class-validator";

export class TestCanadaTariffMatcherDto {
  @ApiProperty({ type: "string", minLength: 1 })
  @IsString()
  @MinLength(1)
  @IsNotEmpty()
  description: string;

  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  hsCode?: string | null;
}
