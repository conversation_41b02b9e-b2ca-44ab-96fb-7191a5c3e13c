import { QueryRunner, SelectQueryBuilder } from "typeorm";
import { PartNoGenerator } from "./part-no.generator";

describe("PartNoGenerator", () => {
  let generator: PartNoGenerator;
  let mockQueryRunner: QueryRunner;
  let mockQueryBuilder: Partial<SelectQueryBuilder<any>>;

  beforeEach(() => {
    mockQueryBuilder = {
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      getRawOne: jest.fn()
    };

    mockQueryRunner = {
      manager: {
        createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder)
      }
    } as unknown as QueryRunner;

    generator = new PartNoGenerator();
  });

  describe("generate", () => {
    const testCases = [
      {
        description: "description with multiple words",
        input: { goodsDescription: "Golf Ball Set" },
        mockResolvedValue: { maxId: 100 },
        expectedPartNo: "G-GLF-BS-101",
        expectedSelect: "MAX(id)",
        expectedFrom: "product"
      },
      {
        description: "long description",
        input: { goodsDescription: "Foam Pad - Fire Retardant Ethylene Pad" },
        mockResolvedValue: { maxId: 101 },
        expectedPartNo: "G-FM-PFREP-102",
        expectedSelect: "MAX(id)",
        expectedFrom: "product"
      },
      {
        description: "description with special characters",
        input: { goodsDescription: "Item #123: Test-Product!" },
        mockResolvedValue: { maxId: 101 },
        expectedPartNo: "G-ITM-TP-102",
        expectedSelect: "MAX(id)",
        expectedFrom: "product"
      },
      {
        description: "single-word description",
        input: { goodsDescription: "Product" },
        mockResolvedValue: { maxId: 102 },
        expectedPartNo: "G-PRDCT-103",
        expectedSelect: "MAX(id)",
        expectedFrom: "product"
      },
      {
        description: "empty description",
        input: { goodsDescription: "" },
        mockResolvedValue: { maxId: 103 },
        expectedPartNo: "G-PART-104",
        expectedSelect: "MAX(id)",
        expectedFrom: "product"
      },
      {
        description: "description with multiple spaces",
        input: { goodsDescription: "  Multiple   Spaces  Here  " },
        mockResolvedValue: { maxId: 104 },
        expectedPartNo: "G-MLTPL-SH-105",
        expectedSelect: "MAX(id)",
        expectedFrom: "product"
      },
      {
        description: "undefined description",
        input: {},
        mockResolvedValue: { maxId: 105 },
        expectedPartNo: "G-PART-106",
        expectedSelect: "MAX(id)",
        expectedFrom: "product"
      },

      {
        description: "maxId is null",
        input: {},
        mockResolvedValue: { maxId: null },
        expectedPartNo: "G-PART-1",
        expectedSelect: "MAX(id)",
        expectedFrom: "product"
      }
    ];

    test.each(testCases)(
      "should handle $description correctly",
      async ({ input, mockResolvedValue, expectedPartNo, expectedSelect, expectedFrom }) => {
        mockQueryBuilder.getRawOne = jest.fn().mockResolvedValue(mockResolvedValue);

        const partNo = await generator.generate(input, mockQueryRunner);
        expect(partNo).toBe(expectedPartNo);
        expect(mockQueryBuilder.select).toHaveBeenCalledWith(expectedSelect, "maxId");
        expect(mockQueryBuilder.from).toHaveBeenCalledWith(expectedFrom, "product");
        expect(mockQueryBuilder.getRawOne).toHaveBeenCalled();
      }
    );
  });
});
