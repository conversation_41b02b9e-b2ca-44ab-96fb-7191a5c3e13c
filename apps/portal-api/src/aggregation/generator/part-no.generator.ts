import { Injectable } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { DataSource } from "typeorm";
interface PartialCommercialInvoiceLine {
  goodsDescription?: string;
}

const PREFIX = "G";
const MAX_PRODUCT_NAME_LENGTH = 8;

@Injectable()
export class PartNoGenerator {
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  async generate(line: PartialCommercialInvoiceLine): Promise<string> {
    const segment =
      line.goodsDescription
        ?.trim()
        ?.replace(/[-_]/g, " ")
        ?.replace(/[^a-zA-Z\s]/g, "")
        ?.replace(/[aeiou]/g, "")
        ?.split(/\s+/)
        ?.filter(Boolean) ?? [];

    const [{ id: nextId }] = await this.dataSource.query("SELECT nextval('product_id_seq') as id");

    let productName = "";

    productName =
      segment.length > 0 && segment[0].length
        ? segment[0].toUpperCase().substring(0, MAX_PRODUCT_NAME_LENGTH)
        : "PART";

    if (segment.length > 1) {
      productName +=
        "-" +
        segment
          .slice(1)
          .map((word) => word.charAt(0).toUpperCase())
          .join("")
          .substring(0, MAX_PRODUCT_NAME_LENGTH);
    }

    return `${PREFIX}-${productName}-${nextId}`;
  }
}
