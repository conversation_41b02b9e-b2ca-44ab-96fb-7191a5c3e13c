import { Job } from "bullmq";

import { On<PERSON>orkerEvent, Processor, WorkerHost } from "@nestjs/bullmq";
import { AggregationService } from "../aggregation.service";
import { AggregationExecuter } from "../executer/aggregation.executer";
import { Queue as AggregationQueue } from "./config";

export interface AggregationJobData {
  aggregationId: number;
  organizationId: number;
  batchId: string;
  retry: boolean;
}

@Processor(
  {
    name: AggregationQueue.AGGREGATION_TASKS
  },
  {
    concurrency: parseInt(process.env.AGGREGATION_PROCESSOR_CONCURRENCY || "1")
  }
)
export class AggregationWorker extends WorkerHost {
  constructor(
    private readonly aggregationService: AggregationService,
    private readonly aggregationExecuter: AggregationExecuter
  ) {
    super();
  }

  async process(job: Job<AggregationJobData>) {
    switch (job.name) {
      case "aggregate":
        return this.processAggregate(job);
    }
  }

  private async processAggregate(job: Job<AggregationJobData>) {
    const { aggregationId, organizationId, batchId, retry } = job.data;

    job.log("Processing aggregation" + JSON.stringify({ aggregationId, organizationId }));

    const aggregation = await this.aggregationService.getDocumentAggregation(aggregationId);

    const result = await this.aggregationExecuter.execute(aggregation, retry);

    return {
      aggregationId,
      organizationId,
      batchId,
      result: {
        id: aggregation.targetId,
        hasError: result.hasError,
        error: result.error
      }
    };
  }

  @OnWorkerEvent("error")
  onError(error: Error) {
    console.error(error);
  }
}
