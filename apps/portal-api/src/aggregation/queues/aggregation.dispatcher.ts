import { InjectQueue } from "@nestjs/bullmq";
import { Injectable } from "@nestjs/common";
import { Queue } from "bullmq";
import { Queue as AggregationQueue } from "./config";

@Injectable()
export class AggregationDispatcher {
  constructor(
    @InjectQueue(AggregationQueue.AGGREGATION_TASKS)
    private readonly aggregationQueue: Queue
  ) {}

  async dispatchAggregationTask(
    aggregationId: number,
    organizationId: number,
    batchId: string,
    retry: boolean = false
  ) {
    return await this.aggregationQueue.add(
      "aggregate",
      { aggregationId, organizationId, batchId, retry },
      {
        deduplication: {
          id: `aggregate:${aggregationId}`
        }
      }
    );
  }

  async jobIsRunningOrPending(jobId: string) {
    const job = await this.aggregationQueue.getJobState(jobId);
    return ["delayed", "waiting", "active"].includes(job);
  }

  async abortJob(jobId: string) {
    await this.aggregationQueue.remove(jobId);
  }
}
