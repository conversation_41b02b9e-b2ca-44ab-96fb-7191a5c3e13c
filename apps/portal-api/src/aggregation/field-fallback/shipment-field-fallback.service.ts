import { Injectable, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Shipment } from "nest-modules";
import { Repository } from "typeorm";

@Injectable()
export class ShipmentFieldFallbackService {
  constructor(
    @InjectRepository(Shipment)
    private readonly shipmentRepository: Repository<Shipment>
  ) {}

  private readonly logger = new Logger(ShipmentFieldFallbackService.name);

  async fallbackMissingFields(shipmentId: number) {
    this.logger.debug("Fallback missing fields for shipment id: " + shipmentId);
    const shipment = await this.shipmentRepository.findOne({
      where: {
        id: shipmentId
      },
      select: {
        id: true,
        etaPort: true,
        etaDestination: true
      }
    });

    if (!shipment) {
      this.logger.debug("Shipment not found, id: " + shipmentId);
      return;
    }

    this.fallbackETA(shipment);

    await this.shipmentRepository.update(shipmentId, shipment);
  }

  /**
   * Fallback ETA
   *
   * If etaPort is missing, set it to the current date
   * If etaDestination is missing, set it to the etaPort
   *
   * @param shipment
   */
  private fallbackETA(shipment: Pick<Shipment, "etaPort" | "etaDestination">) {
    const fallbackedFields = [];

    if (!shipment.etaPort) {
      shipment.etaPort = shipment.etaDestination ?? new Date();
      fallbackedFields.push("etaPort");
    }

    if (!shipment.etaDestination) {
      shipment.etaDestination = shipment.etaPort;
      fallbackedFields.push("etaDestination");
    }

    this.logger.debug("ETA fallback: " + fallbackedFields.join(", "));
  }
}
