import { DocumentField } from "nest-modules";

export class FieldsToObjectAdapter {
  constructor(private readonly fields: DocumentField[]) {}

  toObject(): any {
    const obj = {};

    const tryParse = (value: string) => {
      try {
        return JSON.parse(value);
      } catch (error) {
        return value;
      }
    };

    this.fields.forEach((field) => {
      if (field.dataType === "object" || field.dataType === "array") {
        obj[field.name] = tryParse(field.value);
      } else if (field.dataType === "number") {
        obj[field.name] = Number(field.value);
      } else {
        obj[field.name] = field.value;
      }
    });

    return obj;
  }
}
