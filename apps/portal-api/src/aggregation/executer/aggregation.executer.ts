import { Injectable, Logger } from "@nestjs/common";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import { InjectDataSource } from "@nestjs/typeorm";
import {
  AggregationTargetType,
  DocumentAggregation,
  DocumentAggregationAction,
  DocumentAggregationStep,
  DocumentAggregationStepLog,
  DocumentAggregationStepStatus,
  UserPermission
} from "nest-modules";
import { ClsService } from "nestjs-cls";
import { DataSource } from "typeorm";
import { ProcessorContext } from "../processors/base.processor";
import {
  CertificateOfOriginAggregation,
  CommercialInvoiceAggregation,
  ShipmentAggregation
} from "../workflows";
import { DatabaseLogger } from "./database.logger";

export type AggregationStep = {
  name: string;
  displayName: string;
  loader?: any;
  inline?: boolean;
  parameters: {
    [key: string]: ParameterGetter | string;
  };
};
type ParameterGetter = (aggregation: DocumentAggregation, output: any) => any;

@Injectable()
export class AggregationExecuter {
  private readonly logger = new Logger(AggregationExecuter.name);

  constructor(
    private moduleRef: ModuleRef,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly cls: ClsService
  ) {}

  private async compileParameters(parameters: any, aggregation: DocumentAggregation, output: any) {
    const entries = await Promise.all(
      Object.entries(parameters).map(async ([key, value]) => {
        if (typeof value === "function") {
          const result = await Promise.resolve(value(aggregation, output));
          return [key, result];
        }
        return [key, value];
      })
    );

    return Object.fromEntries(entries);
  }

  private populateRequest(aggregation: DocumentAggregation) {
    if (aggregation.organizationId) {
      return {
        user: {
          permission: UserPermission.ORGANIZATION_ADMIN,
          organization: {
            id: aggregation.organizationId
          }
        }
      };
    }

    throw new Error("Cannot execute aggregation when organization is not set");
  }

  async executeTask(
    task: any,
    compiledParameters: any,
    context: any,
    databaseLogger: DatabaseLogger,
    setTarget: (targetType: AggregationTargetType, targetId: number) => void
  ) {
    const { loader, inline } = task;
    const request = this.populateRequest(context.aggregation);
    console.log("request", request);
    return await this.cls.run(async () => {
      this.cls.set("ORGANIZATION_ID", context.aggregation.organizationId);
      let result: any;
      if (inline) {
        result = loader(compiledParameters);
      } else {
        const contextId = ContextIdFactory.create();
        this.moduleRef.registerRequestByContextId(request, contextId);
        const processor = await this.moduleRef.resolve(loader, contextId);
        // @see: https://github.com/nestjs/nest/issues/5778
        await new Promise((resolve) => process.nextTick(resolve));
        const bag: ProcessorContext<any> = {
          params: compiledParameters,
          context: context,
          dataSource: this.dataSource,
          databaseLogger,
          setTarget,
          getTarget: () => context.aggregation.targetId
        };
        result = await processor.get(bag);
      }

      return result;
    });
  }

  /**
   * Get the tasks for a given action
   *
   * @param action - The action to get the tasks for
   * @returns The tasks for the action
   */
  private getTasks(action: DocumentAggregationAction) {
    switch (action) {
      case DocumentAggregationAction.CREATE_SHIPMENT:
        return ShipmentAggregation;
      case DocumentAggregationAction.CREATE_COMMERCIAL_INVOICE:
        return CommercialInvoiceAggregation;
      case DocumentAggregationAction.CREATE_CERTIFICATE_OF_ORIGIN:
        return CertificateOfOriginAggregation;
    }
  }

  async updateStepStatus(
    aggregationStep: DocumentAggregationStep,
    status: DocumentAggregationStepStatus,
    params: {
      inputData?: any;
      outputData?: any;
      errorMessage?: string;
    }
  ) {
    aggregationStep.status = status;
    aggregationStep.inputData = params.inputData;
    aggregationStep.outputData = params.outputData;
    aggregationStep.errorMessage = params.errorMessage;

    const repository = this.dataSource.manager.getRepository(DocumentAggregationStep);
    await repository.save(aggregationStep);
  }

  async createStepLog(step: AggregationStep, aggregation: DocumentAggregation) {
    const stepEntity = new DocumentAggregationStep();
    stepEntity.name = step.name;
    stepEntity.status = DocumentAggregationStepStatus.PENDING;
    stepEntity.documentAggregation = aggregation;
    // stepEntity.logs = [];

    return await this.dataSource.manager.getRepository(DocumentAggregationStep).save(stepEntity);
  }

  /**
   * Rollback a step
   *
   * @param step - The step to rollback
   * @param aggregation - The aggregation
   * @param output - The output
   */
  async rollbackStep(step: AggregationStep, context: any, output: any) {
    const outputStep = output[step.name];
    const { loader, inline } = step;
    const request = this.populateRequest(context.aggregation);
    console.log("request", request);
    return await this.cls.run(async () => {
      this.cls.set("ORGANIZATION_ID", context.aggregation.organizationId);
      let result: any;
      if (inline) {
        // do nothing
      } else {
        const contextId = ContextIdFactory.create();
        this.moduleRef.registerRequestByContextId(request, contextId);
        const processor = await this.moduleRef.resolve(loader, contextId);
        // @see: https://github.com/nestjs/nest/issues/5778
        await new Promise((resolve) => process.nextTick(resolve));
        result = await processor.rollback(outputStep);
      }

      return result;
    });
  }

  async executeStep(step: AggregationStep, context: any, output: any) {
    let compiledParameters: any;
    let result: any;
    let log: DocumentAggregationStepLog[];
    let hasError = false;

    this.logger.debug("\nExecuting Step: " + step.name);

    const stepLog = await this.createStepLog(step, context.aggregation);
    const databaseLogger = new DatabaseLogger(
      this.dataSource.manager.getRepository(DocumentAggregationStepLog),
      stepLog
    );
    const setTarget = (targetType: AggregationTargetType, targetId: number) => {
      this.dataSource.manager.getRepository(DocumentAggregation).update(context.aggregation.id, {
        targetType,
        targetId
      });
    };
    try {
      compiledParameters = await this.compileParameters(step.parameters, context.aggregation, output);
      output[step.name] = {};

      const taskResult = await this.executeTask(step, compiledParameters, context, databaseLogger, setTarget);

      result = taskResult.result;
      hasError = taskResult.hasError;

      output[step.name] = result;

      if (hasError) {
        throw new Error("Error in step " + step.name);
      }

      await this.updateStepStatus(stepLog, DocumentAggregationStepStatus.COMPLETED, {
        inputData: compiledParameters,
        outputData: result
      });
    } catch (error) {
      output[step.name] = null;
      await this.updateStepStatus(stepLog, DocumentAggregationStepStatus.FAILED, {
        inputData: compiledParameters,
        outputData: result,
        errorMessage: error.message
      });
      throw error;
    }
  }

  async cleanSteps(aggregation: DocumentAggregation) {
    await this.dataSource.manager
      .getRepository(DocumentAggregationStep)
      .delete({ documentAggregation: aggregation });
  }

  async execute(aggregation: DocumentAggregation, retry: boolean = false) {
    console.log("Executing aggregation", aggregation.id);
    console.log("Organization ID", aggregation.organizationId);
    this.cleanSteps(aggregation);
    const output: any = {};
    const steps = this.getTasks(aggregation.action);
    let currentStep = 0;

    const context = {
      aggregation,
      retry
    };

    try {
      while (currentStep < steps.length) {
        const step = steps[currentStep];
        try {
          await this.executeStep(step, context, output);
          currentStep++;
        } catch (error) {
          this.logger.error(error);
          this.logger.error(error.stack);
          throw error;
        }
      }

      return {
        output,
        hasError: false,
        id: aggregation.id // deprecated
      };
    } catch (error) {
      while (currentStep >= 0) {
        const step = steps[currentStep];
        this.logger.debug("Rolling back step: " + step.name);
        await this.rollbackStep(step, context, output);
        currentStep--;
      }

      this.dataSource.manager.getRepository(DocumentAggregation).update(context.aggregation.id, {
        targetType: null,
        targetId: null
      });

      return {
        output,
        error: error.message,
        hasError: true,
        id: aggregation.id // deprecated
      };
    }
  }
}
