import { DocumentAggregationStep, DocumentAggregationStepLog } from "nest-modules";
import { Repository } from "typeorm";

export class DatabaseLogger {
  constructor(
    private readonly stepLogRepository: Repository<DocumentAggregationStepLog>,
    private readonly step: DocumentAggregationStep
  ) {}

  /**
   * Log an informational message
   * @param stepLog The step to add the log to
   * @param message The message to log
   * @param field The field related to the log (optional)
   */
  async info(message: string, field?: string): Promise<void> {
    await this.addLog({
      message,
      field: field || "",
      level: "info"
    });
  }

  /**
   * Log a warning message
   * @param stepLog The step to add the log to
   * @param message The message to log
   * @param field The field related to the log (optional)
   */
  async warning(message: string, field?: string): Promise<void> {
    await this.addLog({
      message,
      field: field || "",
      level: "warning"
    });
  }

  /**
   * Log an error message
   * @param stepLog The step to add the log to
   * @param message The message to log
   * @param field The field related to the log (optional)
   */
  async error(message: string, field?: string): Promise<void> {
    await this.addLog({
      message,
      field: field || "",
      level: "error"
    });
  }

  /**
   * Add a log entry to a step and save it to the database
   * @param stepLog The step to add the log to
   * @param log The log entry to add
   */
  public async addLog(log: Pick<DocumentAggregationStepLog, "message" | "field" | "level">): Promise<void> {
    // Add to in-memory array if it exists
    await this.stepLogRepository.insert({
      ...log,
      step: this.step
    });
  }

  public async stepHasError(): Promise<boolean> {
    const logs = await this.stepLogRepository.find({
      where: {
        step: {
          id: this.step.id
        },
        level: "error"
      }
    });
    return logs.length > 0;
  }

  /**
   * Get all logs for a step
   * @param stepLog The step to get logs for
   * @returns Array of log entries
   */
  async getLogs(): Promise<DocumentAggregationStepLog[]> {
    return this.stepLogRepository.find({
      where: {
        step: {
          id: this.step.id
        }
      }
    });
  }
}
