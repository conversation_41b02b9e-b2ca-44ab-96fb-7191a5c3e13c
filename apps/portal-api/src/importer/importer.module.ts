import { EmailModule } from "@/email/email.module";
import { forwardRef, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { BaseImporterModule, Importer, TransactionalEventEmitterModule } from "nest-modules";
import { ImporterController } from "./importer.controller";
import { ImporterService } from "./importer.service";

@Module({
  imports: [
    TypeOrmModule.forFeature([Importer]),
    BaseImporterModule,
    forwardRef(() => EmailModule),
    forwardRef(() => TransactionalEventEmitterModule)
  ],
  providers: [ImporterService],
  controllers: [ImporterController],
  exports: [ImporterService]
})
export class ImporterModule {}
