import { Body, Controller, Param, ParseIntPipe, Post, Put, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiEditResponses,
  CreateImporterDto,
  EditImporterDto,
  Importer
} from "nest-modules";
import { ImporterService } from "./importer.service";

@ApiTags("Importer API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("importers")
export class ImporterController {
  constructor(private readonly importerService: ImporterService) {}

  @ApiOperation({ summary: "Create Importer" })
  @ApiCreateResponses({ type: Importer })
  @Post()
  async createImporter(@Body() createImporterDto: CreateImporterDto) {
    return await this.importerService.createImporter(createImporterDto);
  }

  @ApiOperation({ summary: "Edit Importer" })
  @ApiParam({ name: "id", type: "integer", description: "Importer ID" })
  @ApiEditResponses({ type: Importer })
  @Put(":id")
  async editImporter(@Param("id", ParseIntPipe) id: number, @Body() editImporterDto: EditImporterDto) {
    return await this.importerService.editImporter(id, editImporterDto);
  }
}
