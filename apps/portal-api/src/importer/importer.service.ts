import { EmailService } from "@/email/services/email.service";
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import {
  AuthenticatedRequest,
  BaseImporterService,
  CandataCustomerDto,
  CandataCustomerProfileStatus,
  CandataProvince,
  CandataService,
  CreateImporterDto,
  EditImporterDto,
  FIND_IMPORTER_RELATIONS,
  GetImportersDto,
  Importer,
  ImporterStatus,
  toUpperSnakeCase,
  UserPermission
} from "nest-modules";
import { In, IsNull, Not, QueryRunner, Repository } from "typeorm";

@Injectable({ scope: Scope.REQUEST })
export class ImporterService {
  constructor(
    @InjectRepository(Importer)
    private readonly importerRepository: Repository<Importer>,
    @Inject(forwardRef(() => EmailService))
    private readonly emailService: EmailService,
    @Inject(CandataService)
    private readonly candataService: CandataService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    @Inject(BaseImporterService)
    private readonly baseImporterService: BaseImporterService
  ) {}
  private readonly logger = new Logger(ImporterService.name);

  async getImporters(getImportersDto: GetImportersDto, queryRunner?: QueryRunner) {
    try {
      return await this.baseImporterService.getImporters(getImportersDto, queryRunner);
    } catch (error) {
      this.logger.error("Error in getImporters", error);
      throw new InternalServerErrorException(error);
    }
  }

  async getImporterById(importerId: number, queryRunner?: QueryRunner) {
    try {
      return await this.baseImporterService.getImporterById(importerId, queryRunner);
    } catch (error) {
      this.logger.error("Error in getImporterById", error);
      throw new InternalServerErrorException(error);
    }
  }

  async getImporterByReceiveEmail(
    receiveEmails: Array<string>,
    queryRunner?: QueryRunner,
    skipOrganizationCheck = false
  ) {
    return await (
      queryRunner ? queryRunner.manager.getRepository(Importer) : this.importerRepository
    ).findOne({
      where: {
        receiveEmail: In(receiveEmails),
        organization: {
          id:
            skipOrganizationCheck || this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
              ? Not(IsNull())
              : this.request?.user?.organization?.id || -1
        }
      },
      relations: FIND_IMPORTER_RELATIONS
    });
  }

  async createImporter(
    createImporterDto: CreateImporterDto,
    queryRunner?: QueryRunner,
    skipCarmStatusCheck = false
  ) {
    try {
      const importer = await this.baseImporterService.createImporter(
        createImporterDto,
        queryRunner,
        skipCarmStatusCheck
      );

      await this.emailService.sendOnboardingEmail(importer, undefined, queryRunner);

      return await this.getImporterById(importer.id, queryRunner);
    } catch (error) {
      this.logger.error("Error in createImporter", error);
      throw new InternalServerErrorException(error);
    }
  }

  async editImporter(importerId: number, editImporterDto: EditImporterDto, queryRunner?: QueryRunner) {
    try {
      const importer = await this.baseImporterService.editImporter(importerId, editImporterDto, queryRunner);

      await this.emailService.sendOnboardingEmail(importer);

      return await this.getImporterById(importer.id, queryRunner);
    } catch (error) {
      this.logger.error("Error in editImporter", error);
      throw new InternalServerErrorException(error);
    }
  }

  async findOrCreateCandataImporter(importerId: number, queryRunner?: QueryRunner) {
    const importer = await this.getImporterById(importerId, queryRunner);
    if (!importer) throw new NotFoundException("Importer not found");
    if (importer.organization?.id !== this.request?.user?.organization?.id)
      throw new BadRequestException("Importer does not belong to the organization of current user");
    if (importer.status !== ImporterStatus.ACTIVE) throw new BadRequestException("Importer is not Active");

    let candataCustomer: CandataCustomerDto = null;
    if (importer.candataCustomerNumber) {
      try {
        candataCustomer = await this.candataService.getCandataCustomer(
          importer.candataCustomerNumber,
          importer.organization?.customsBroker
        );
        this.logger.log(`Candata customer found: ${candataCustomer?.clientNumber}`);
      } catch (error) {
        this.logger.error(
          `Error while getting Candata customer for importer ${importer.id}: ${error.message}`
        );
        candataCustomer = null;
      }
    }
    if (!candataCustomer) {
      this.logger.log(`No Candata customer, creating new...`);

      let candataCustomerNumberPostfix = 1,
        isCustomerNumberUnique = false;
      do {
        const candataCustomerNumber = `CLARO${candataCustomerNumberPostfix.toString().padStart(5, "0")}`;
        try {
          this.logger.log(`Checking if Candata customer number ${candataCustomerNumber} is unique...`);
          candataCustomer = await this.candataService.getCandataCustomer(
            candataCustomerNumber,
            importer.organization?.customsBroker
          );
          this.logger.log(`Candata customer found: ${candataCustomer?.clientNumber}, not unique`);
          candataCustomerNumberPostfix++;
          isCustomerNumberUnique = false;
        } catch (error) {
          this.logger.log(
            `Error while checking if customer number ${candataCustomerNumber} is unique: ${error.message}`
          );
          if (error.response?.status === 404 || error.message?.includes("404")) {
            this.logger.log(`Customer number ${candataCustomerNumber} is unique`);
            isCustomerNumberUnique = true;
          } else
            throw new InternalServerErrorException(
              `Error while checking if Candata customer number is unique`
            );
        }
      } while (!isCustomerNumberUnique && candataCustomerNumberPostfix <= 99999);
      if (candataCustomerNumberPostfix > 99999)
        throw new InternalServerErrorException(`No unique Candata customer number found`);

      try {
        candataCustomer = await this.candataService.createCandataCustomer(
          {
            clientNumber: `CLARO${candataCustomerNumberPostfix.toString().padStart(5, "0")}`,
            address: {
              address1: importer.address.slice(0, 35),
              address2: importer.address.slice(35, 70),
              city: importer.city,
              province:
                ["CA", "US"].includes(importer.country?.alpha2) && importer.state
                  ? Object.values(CandataProvince).includes(importer.state as CandataProvince)
                    ? importer.state
                    : CandataProvince[toUpperSnakeCase(importer.state)] || ""
                  : "",
              postalCode: importer.postalCode,
              country: importer.country?.alpha2,
              emailAddress: importer.email,
              contact: importer.officerNameAndTitle.slice(0, 30)
            },
            profile: {
              language: "English",
              temporary: false,
              name: importer.companyName,
              accountStatus: CandataCustomerProfileStatus.ACTIVE,
              phone: importer.phoneNumber,
              fax: importer.fax || "",
              emailAddress: importer.email,
              contact: importer.officerNameAndTitle.slice(0, 30)
            },
            customs: {
              businessNumber: importer.businessNumber,
              recapEntry: true,
              provincialTaxApplies: false,
              consigneeRequired: false,
              autoCreateB3: false
            }
          },
          importer.organization?.customsBroker
        );
        this.logger.log(`Candata customer created: ${candataCustomer?.clientNumber}`);
      } catch (error) {
        this.logger.error(
          `Error while creating Candata customer for importer ${importer.id}: ${error.message}`
        );
        throw new InternalServerErrorException("Error occurred while creating Candata customer");
      }
    }

    if (!candataCustomer?.clientNumber)
      throw new InternalServerErrorException(`No Candata customer found or created`);

    await (queryRunner ? queryRunner.manager.getRepository(Importer) : this.importerRepository).update(
      { id: importer.id },
      { candataCustomerNumber: candataCustomer.clientNumber }
    );
    return await this.getImporterById(importer.id, queryRunner);
  }
}
