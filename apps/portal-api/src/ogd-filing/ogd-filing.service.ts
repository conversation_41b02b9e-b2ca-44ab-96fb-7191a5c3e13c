import { TradePartnerService } from "@/trade-partner/trade-partner.service";
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import {
  AGENCY_PROPS,
  AuthenticatedRequest,
  CanadaGovernmentAgency,
  CanadaOgdService,
  CreateOgdFilingAndMatchingRuleDto,
  CreateOgdFilingDto,
  CreateOrEditOgdFilingAndMatchingRuleResponseDto,
  ECCC_PROPS,
  EditOgdFilingAndMatchingRuleDto,
  EditOgdFilingDto,
  FIND_OGD_FILING_RELATIONS,
  getFindOptions,
  GetOgdFilingsDto,
  GetOgdFilingsResponseDto,
  MatchingConditionService,
  MatchingRuleService,
  MatchingRuleSourceDatabaseTable,
  NON_OGD_FILING_KEYS,
  OGD_FILING_ENUM_KEYS,
  OGD_FILING_REQUIRED_KEYS,
  OgdFiling,
  OgdFilingColumn,
  PartnerType,
  TransactionalEventEmitterService,
  UserPermission
} from "nest-modules";
import { DataSource, IsNull, Not, QueryRunner, Repository } from "typeorm";
import { OgdFilingEvent } from "./types";
import { OgdFilingCreatedEventDto, OgdFilingDeletedEventDto, OgdFilingEditedEventDto } from "./dto";

@Injectable({ scope: Scope.REQUEST })
export class OgdFilingService {
  constructor(
    @InjectRepository(OgdFiling)
    private readonly ogdFilingRepository: Repository<OgdFiling>,
    @Inject(CanadaOgdService)
    private readonly canadaOgdService: CanadaOgdService,
    @Inject(MatchingRuleService)
    private readonly matchingRuleService: MatchingRuleService,
    @Inject(MatchingConditionService)
    private readonly matchingConditionService: MatchingConditionService,
    @Inject(forwardRef(() => TradePartnerService))
    private readonly tradePartnerService: TradePartnerService,
    @Inject(forwardRef(() => TransactionalEventEmitterService))
    private readonly transactionalEventEmitter: TransactionalEventEmitterService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}
  private readonly logger = new Logger(OgdFilingService.name);

  private async validateAndUpdateOgdFiling(
    ogdFiling: OgdFiling,
    dto: CreateOgdFilingDto | EditOgdFilingDto,
    queryRunner?: QueryRunner
  ) {
    // Check if agency is supported
    const { required: requiredKeys, optional: optionalKeys } = AGENCY_PROPS[ogdFiling.ogd.agency] ?? {};
    if (!Array.isArray(requiredKeys)) throw new BadRequestException("Unsupported government agency");
    const agencyKeys = [...requiredKeys, ...optionalKeys];
    this.logger.debug(`Agency keys: ${agencyKeys}`);

    // Set filing to excluded if provided in DTO
    if (typeof dto.isExcluded === "boolean") ogdFiling.isExcluded = dto.isExcluded;

    for (const key of Object.values(OgdFilingColumn)) {
      // If key is not related to OGD filing (like create/edit dates, organization, etc.) then skip
      if (NON_OGD_FILING_KEYS.includes(key)) continue;

      // If key is not supported by the agency, set it to null
      const propertyName = key.replace(/Id$/, "");
      if (ogdFiling.isExcluded || !agencyKeys.includes(key)) {
        ogdFiling[propertyName] = null;
        continue;
      }

      // If key is supported by agency and is in DTO, update OGD filing
      switch (key) {
        case OgdFilingColumn.manufacturerId:
          if (typeof dto[key] === "number") {
            ogdFiling.manufacturer = await this.tradePartnerService.getTradePartnerById(
              dto[key],
              queryRunner
            );
            if (!ogdFiling.manufacturer) throw new NotFoundException(`Manufacturer not found`);
            if (ogdFiling.manufacturer.organization?.id !== ogdFiling.organization?.id)
              throw new BadRequestException(
                `Manufacturer does not belong to the organization of current user`
              );
            if (ogdFiling.manufacturer.partnerType !== PartnerType.MANUFACTURER)
              throw new BadRequestException(`Manufacturer is not in type manufacturer`);
          } else if (dto[key] !== undefined) ogdFiling[propertyName] = null;
          break;
        default:
          if (dto[key] !== undefined) ogdFiling[propertyName] = dto[key];
          break;
      }
    }

    // Perform validations only if filing is not excluded
    if (!ogdFiling.isExcluded) {
      // Check if all required fields are set
      const missingRequiredFields = requiredKeys.filter((key) =>
        [null, undefined].includes(ogdFiling[key.replace(/Id$/, "")])
      );
      if (missingRequiredFields.length > 0)
        throw new BadRequestException(
          `The following fields are required for ${ogdFiling.ogd.agency.toUpperCase()} filings: ${missingRequiredFields.join(", ")}`
        );

      // Check for agency-specific validations
      switch (ogdFiling.ogd.agency) {
        case CanadaGovernmentAgency.CFIA:
          if (ogdFiling.airsReferenceNumber && !ogdFiling.airsType)
            throw new BadRequestException(
              "AIRS type must be selected when AIRS reference number is provided"
            );
          break;
        case CanadaGovernmentAgency.ECCC:
          const missingEcccRequiredFields = (ECCC_PROPS[ogdFiling.ecccSubType]?.required || [])?.filter(
            (key) => [null, undefined].includes(ogdFiling[key.replace(/Id$/, "")])
          );
          this.logger.debug(`Missing ECCC required fields: ${missingEcccRequiredFields}`);
          if (missingEcccRequiredFields.length > 0)
            throw new BadRequestException(
              `The following fields are required for ECCC ${ogdFiling.ecccSubType} filings: ${missingEcccRequiredFields.join(", ")}`
            );
          break;
      }
    }

    return ogdFiling;
  }

  async getOgdFilings(getOgdFilingsDto: GetOgdFilingsDto): Promise<GetOgdFilingsResponseDto> {
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      getOgdFilingsDto.organizationId = this.request?.user?.organization?.id || -1;
    const { where, order, skip, take } = getFindOptions(
      getOgdFilingsDto,
      OGD_FILING_ENUM_KEYS,
      OGD_FILING_REQUIRED_KEYS,
      OgdFilingColumn.id
    );
    const [ogdFilings, total] = await this.ogdFilingRepository.findAndCount({
      where,
      order,
      relations: FIND_OGD_FILING_RELATIONS,
      skip,
      take
    });
    return {
      data: ogdFilings,
      total,
      skip,
      limit: take
    };
  }

  async getOgdFilingById(filingId: number, queryRunner?: QueryRunner) {
    return (queryRunner ? queryRunner.manager.getRepository(OgdFiling) : this.ogdFilingRepository).findOne({
      where: {
        id: filingId,
        organization: {
          id:
            this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN
              ? this.request?.user?.organization?.id || -1
              : Not(IsNull())
        }
      },
      relations: FIND_OGD_FILING_RELATIONS
    });
  }

  async createOgdFiling(createOgdFilingDto: CreateOgdFilingDto, queryRunner?: QueryRunner) {
    if (!this.request?.user?.organization) throw new NotFoundException(`Organization not found`);
    const ogd = await this.canadaOgdService.getCanadaOgdById(createOgdFilingDto.ogdId, queryRunner);
    if (!ogd) throw new NotFoundException("Canada OGD not found");

    let newFiling = new OgdFiling();
    newFiling.ogd = ogd;
    newFiling.organization = this.request?.user?.organization;
    newFiling.createdBy = this.request?.user || null;
    newFiling.lastEditedBy = this.request?.user || null;

    newFiling = await this.validateAndUpdateOgdFiling(newFiling, createOgdFilingDto, queryRunner);

    newFiling = await (
      queryRunner ? queryRunner.manager.getRepository(OgdFiling) : this.ogdFilingRepository
    ).save(newFiling);

    await this.transactionalEventEmitter.enqueueEvent(
      OgdFilingEvent.OGD_FILING_CREATED,
      queryRunner,
      new OgdFilingCreatedEventDto(newFiling.id, this.request?.user, createOgdFilingDto)
    );

    return await this.getOgdFilingById(newFiling.id, queryRunner);
  }

  async editOgdFiling(ogdFilingId: number, editOgdFilingDto: EditOgdFilingDto, queryRunner?: QueryRunner) {
    if (!this.request?.user?.organization) throw new NotFoundException(`Organization not found`);
    let filing = await this.getOgdFilingById(ogdFilingId, queryRunner);
    if (!filing) throw new NotFoundException(`OGD Filing not found`);
    if (typeof editOgdFilingDto.ogdId === "number") {
      filing.ogd = await this.canadaOgdService.getCanadaOgdById(editOgdFilingDto.ogdId, queryRunner);
      if (!filing.ogd) throw new NotFoundException(`Canada OGD not found`);
    }

    filing = await this.validateAndUpdateOgdFiling(filing, editOgdFilingDto, queryRunner);

    await (queryRunner ? queryRunner.manager.getRepository(OgdFiling) : this.ogdFilingRepository).save(
      filing
    );

    await this.transactionalEventEmitter.enqueueEvent(
      OgdFilingEvent.OGD_FILING_EDITED,
      queryRunner,
      new OgdFilingEditedEventDto(filing.id, this.request?.user, editOgdFilingDto)
    );

    return await this.getOgdFilingById(filing.id, queryRunner);
  }

  async deleteOgdFiling(ogdFilingId: number) {
    if (!this.request?.user?.organization) throw new NotFoundException(`Organization not found`);
    const filing = await this.getOgdFilingById(ogdFilingId);
    if (!filing) throw new NotFoundException(`OGD Filing not found`);
    if (
      await this.matchingRuleService.isMatchingRuleForSourceRecordExists(
        MatchingRuleSourceDatabaseTable.OGD_FILING,
        filing.id
      )
    )
      throw new BadRequestException("OGD Filing is being used in a matching rule");
    await this.ogdFilingRepository.delete({ id: filing.id });

    await this.transactionalEventEmitter.enqueueEvent(
      OgdFilingEvent.OGD_FILING_DELETED,
      null,
      new OgdFilingDeletedEventDto(filing.id, this.request?.user)
    );

    return;
  }

  async createOgdFilingAndMatchingRule(
    createOgdFilingAndMatchingRuleDto: CreateOgdFilingAndMatchingRuleDto,
    queryRunner?: QueryRunner
  ): Promise<CreateOrEditOgdFilingAndMatchingRuleResponseDto> {
    // If queryRunner is not provided, create a new one, otherwise use the provided one
    const tQueryRunner = queryRunner ?? this.dataSource.createQueryRunner();
    if (!queryRunner) {
      // If queryRunner is not provided, that means this function will manage the transaction and thus, it needs to be connected and started here.
      await tQueryRunner.connect();
      await tQueryRunner.startTransaction();
    }

    try {
      const { ogdFiling, matchingRule, matchingConditions } = createOgdFilingAndMatchingRuleDto;
      const newFiling = await this.createOgdFiling(ogdFiling, tQueryRunner);

      const newRule = await this.matchingRuleService.createMatchingRule(
        {
          ...matchingRule,
          sourceTable: MatchingRuleSourceDatabaseTable.OGD_FILING,
          sourceId: newFiling.id
        },
        tQueryRunner
      );

      await this.matchingConditionService.batchUpdateMatchingConditions(
        newRule.id,
        {
          create: matchingConditions
        },
        tQueryRunner
      );

      if (!queryRunner) await tQueryRunner.commitTransaction();

      return {
        ogdFiling: await this.getOgdFilingById(newFiling.id, queryRunner),
        matchingRule: await this.matchingRuleService.getMatchingRuleById(newRule.id, queryRunner)
      };
    } catch (error) {
      if (!queryRunner && tQueryRunner.isTransactionActive) await tQueryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner) await tQueryRunner.release();
    }
  }

  async editOgdFilingAndMatchingRule(
    filingId: number,
    ruleId: number,
    editOgdFilingAndMatchingRuleDto: EditOgdFilingAndMatchingRuleDto
  ): Promise<CreateOrEditOgdFilingAndMatchingRuleResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const {
        ogdFiling: editOgdFilingDto,
        matchingRule: editMatchingRuleWithoutSourceDto,
        matchingConditions: batchUpdateMatchingConditionsDto
      } = editOgdFilingAndMatchingRuleDto;
      let filing = await this.getOgdFilingById(filingId, queryRunner);
      let rule = await this.matchingRuleService.getMatchingRuleById(ruleId, queryRunner);
      if (!filing) throw new NotFoundException("OGD Filing not found");
      if (!rule) throw new NotFoundException("Matching rule not found");
      if (rule.sourceTable !== MatchingRuleSourceDatabaseTable.OGD_FILING || rule.sourceId !== filing.id)
        throw new BadRequestException("Matching rule is not associated with the OGD Filing");

      if (editOgdFilingDto) filing = await this.editOgdFiling(filingId, editOgdFilingDto, queryRunner);
      if (editMatchingRuleWithoutSourceDto)
        rule = await this.matchingRuleService.editMatchingRule(
          ruleId,
          editMatchingRuleWithoutSourceDto,
          queryRunner
        );
      if (batchUpdateMatchingConditionsDto) {
        await this.matchingConditionService.batchUpdateMatchingConditions(
          ruleId,
          batchUpdateMatchingConditionsDto,
          queryRunner
        );
      }

      await queryRunner.commitTransaction();

      return {
        ogdFiling: await this.getOgdFilingById(filing.id),
        matchingRule: await this.matchingRuleService.getMatchingRuleById(rule.id)
      };
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner.isReleased) await queryRunner.release();
    }
  }
}
