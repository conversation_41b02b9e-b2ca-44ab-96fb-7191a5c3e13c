import { ProductCreatedEventPayload, ProductUpdatedEventPayload } from "@/product/events/product.event";
import { Injectable } from "@nestjs/common";
import { AutoOgdFilingService } from "../auto-ogd-filing.service";

@Injectable()
export class ProductEventListener {
  constructor(private readonly autoOgdFilingService: AutoOgdFilingService) {}

  // @OnEvent(ProductEvent.PRODUCT_CREATED)
  async handleProductCreated(product: ProductCreatedEventPayload) {
    await this.autoOgdFilingService.createProductOgdFiling(product);
  }

  // @OnEvent(ProductEvent.PRODUCT_UPDATED)
  async handleProductUpdated(product: ProductUpdatedEventPayload) {
    await this.autoOgdFilingService.updateProductOgdFiling(product);
  }
}
