import { RegisterQueueOptions } from "@nestjs/bullmq";
import { NestWorkerOptions } from "@nestjs/bullmq/dist/interfaces/worker-options.interface";

import { Job, JobsOptions, Queue } from "bullmq";

export const DEFAULT_JOB_OPTIONS: JobsOptions = {
  attempts: 1,
  removeOnComplete: {
    age: 3600,
    count: 1000
  },
  removeOnFail: {
    age: 24 * 3600
  }
};

export const DEFAULT_WORKER_OPTIONS: NestWorkerOptions = {
  concurrency: 3
};

export enum OgdFilingQueueName {
  AUTO_OGD_FILING = "auto-ogd-filing"
}

export const OGD_FILING_QUEUES: Array<RegisterQueueOptions> = [
  {
    name: OgdFilingQueueName.AUTO_OGD_FILING,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  }
];

// Auto OGD Filing Queue
export interface AutoOgdFilingJobData {
  /**
   * The event type that triggered the auto OGD filing
   */
  sourceEventType: "create" | "update";
  /**
   * ID of the product to file OGD filing for
   */
  productId: number;
}
export type AutoOgdFilingJob = Job<AutoOgdFilingJobData, null, string>;
export type AutoOgdFilingQueue = Queue<AutoOgdFilingJobData, null, string>;
