import { TradePartnerModule } from "@/trade-partner/trade-partner.module";
import { BullModule } from "@nestjs/bullmq";
import { DynamicModule, forwardRef, Logger, Module, ModuleMetadata, Provider } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { OgdFiling, TransactionalEventEmitterModule } from "nest-modules";
import { ProductModule } from "../product/product.module";
import { AutoOgdFilingService } from "./auto-ogd-filing.service";
import { ProductEventListener } from "./listeners";
import { OgdFilingController } from "./ogd-filing.controller";
import { OgdFilingService } from "./ogd-filing.service";
import { AutoOgdFilingProcessor } from "./processors/auto-ogd-filing.processor";
import { OGDFILING_STRATEGIES } from "./strategies";
import { OGD_FILING_QUEUES } from "./types";

const moduleProps: ModuleMetadata = {
  imports: [
    TypeOrmModule.forFeature([OgdFiling]),
    BullModule.registerQueue(...OGD_FILING_QUEUES),
    forwardRef(() => TradePartnerModule),
    forwardRef(() => ProductModule),
    forwardRef(() => TransactionalEventEmitterModule)
  ],
  providers: [OgdFilingService, AutoOgdFilingService, ...OGDFILING_STRATEGIES],
  controllers: [OgdFilingController],
  exports: [OgdFilingService, AutoOgdFilingService]
};

@Module(moduleProps)
export class OgdFilingModule {
  static forRoot(): DynamicModule {
    const providers: Array<Provider> = [...moduleProps.providers];
    if (process.env.FEATURE) {
      Logger.warn("Auto OGD filing processor is disabled when feature is set", "OgdFilingModule");
    } else {
      Logger.log("Auto OGD filing processor is enabled when feature is not set", "OgdFilingModule");
      providers.push(AutoOgdFilingProcessor, ProductEventListener);
    }

    return {
      module: OgdFilingModule,
      ...moduleProps,
      providers
    };
  }
}
