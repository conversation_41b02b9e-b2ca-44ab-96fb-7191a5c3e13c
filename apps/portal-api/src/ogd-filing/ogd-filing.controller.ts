import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards
} from "@nestjs/common";
import { ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses,
  CreateOgdFilingAndMatchingRuleDto,
  CreateOgdFilingDto,
  CreateOrEditOgdFilingAndMatchingRuleResponseDto,
  EditOgdFilingAndMatchingRuleDto,
  EditOgdFilingDto,
  GetOgdFilingsDto,
  GetOgdFilingsResponseDto,
  OgdFiling
} from "nest-modules";
import { ProductService } from "../product/product.service";
import { OgdFilingService } from "./ogd-filing.service";
@ApiTags("OGD Filing API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("ogd-filings")
export class OgdFilingController {
  constructor(
    private readonly ogdFilingService: OgdFilingService,
    private readonly productService: ProductService
  ) {}

  @ApiOperation({ summary: "Get OGD Filings" })
  @ApiGetManyResponses({ type: GetOgdFilingsResponseDto })
  @Get()
  async getOgdFilings(@Query() getOgdFilingsDto: GetOgdFilingsDto) {
    return this.ogdFilingService.getOgdFilings(getOgdFilingsDto);
  }

  @ApiOperation({ summary: "Get OGD Filing" })
  @ApiParam({ name: "id", type: "integer", description: "OGD Filing ID" })
  @ApiGetByIdResponses({ type: OgdFiling })
  @Get(":id")
  async getOgdFilingById(@Param("id", ParseIntPipe) id: number) {
    const ogdFiling = await this.ogdFilingService.getOgdFilingById(id);
    if (!ogdFiling) throw new NotFoundException("OGD Filing not found");
    return ogdFiling;
  }

  @ApiOperation({ summary: "Create OGD Filing" })
  @ApiCreateResponses({ type: OgdFiling })
  @Post()
  async createOgdFiling(@Body() createOgdFilingDto: CreateOgdFilingDto) {
    return this.ogdFilingService.createOgdFiling(createOgdFilingDto);
  }

  @ApiOperation({ summary: "Edit OGD Filing" })
  @ApiParam({ name: "id", type: "integer", description: "OGD Filing ID" })
  @ApiEditResponses({ type: OgdFiling })
  @Put(":id")
  async editOgdFiling(@Param("id", ParseIntPipe) id: number, @Body() editOgdFilingDto: EditOgdFilingDto) {
    return this.ogdFilingService.editOgdFiling(id, editOgdFilingDto);
  }

  @ApiOperation({ summary: "Delete OGD Filing" })
  @ApiParam({ name: "id", type: "integer", description: "OGD Filing ID" })
  @ApiDeleteResponses()
  @HttpCode(204)
  @Delete(":id")
  async deleteOgdFiling(@Param("id", ParseIntPipe) id: number) {
    await this.ogdFilingService.deleteOgdFiling(id);
    return;
  }

  @ApiOperation({ summary: "Create OGD Filing and Matching Rule" })
  @ApiCreateResponses({ type: CreateOrEditOgdFilingAndMatchingRuleResponseDto })
  @Post("filing-and-rule")
  async createOgdFilingAndMatchingRule(
    @Body() createOgdFilingAndMatchingRuleDto: CreateOgdFilingAndMatchingRuleDto
  ) {
    return this.ogdFilingService.createOgdFilingAndMatchingRule(createOgdFilingAndMatchingRuleDto);
  }

  @ApiOperation({ summary: "Edit OGD Filing and Matching Rule" })
  @ApiParam({ name: "id", type: "integer", description: "OGD Filing ID" })
  @ApiParam({ name: "ruleId", type: "integer", description: "Matching Rule ID" })
  @ApiEditResponses({ type: CreateOrEditOgdFilingAndMatchingRuleResponseDto })
  @Put(":id/filing-and-rule/:ruleId")
  async editOgdFilingAndMatchingRule(
    @Param("id", ParseIntPipe) id: number,
    @Param("ruleId", ParseIntPipe) ruleId: number,
    @Body() editOgdFilingAndMatchingRuleDto: EditOgdFilingAndMatchingRuleDto
  ) {
    return this.ogdFilingService.editOgdFilingAndMatchingRule(id, ruleId, editOgdFilingAndMatchingRuleDto);
  }
}
