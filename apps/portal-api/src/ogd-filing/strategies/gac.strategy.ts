import { Injectable } from "@nestjs/common";
import { GeneralImportPermit, Product } from "nest-modules";
import { BaseStrategy, OgdProgramCollection } from "./base.strategy";
import { ApplicableOgdFiling } from "../types/strategy.types";

@Injectable()
export class GacStrategy extends BaseStrategy {
  async getApplicableFilings(
    _: Product,
    programCollection: OgdProgramCollection
  ): Promise<ApplicableOgdFiling[]> {
    const commodityTypeMap = {
      "Carbon Steel": GeneralImportPermit.GIP80,
      "Specialty Steel": GeneralImportPermit.GIP81,
      Aluminum: GeneralImportPermit.GIP83
    };

    const applicableFilings: ApplicableOgdFiling[] = [];

    for (const program of programCollection.getPrograms()) {
      if (Object.keys(commodityTypeMap).includes(program.commodityType)) {
        const code = commodityTypeMap[program.commodityType];
        applicableFilings.push({
          program: program.program,
          payload: {
            ogdId: program.id,
            generalImportPermit: code,
            isExcluded: false,
            isRegulated: false
          }
        });
      }
    }

    return applicableFilings;
  }
}
