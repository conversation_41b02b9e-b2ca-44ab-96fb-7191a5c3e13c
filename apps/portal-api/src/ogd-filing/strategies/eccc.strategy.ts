import { Injectable } from "@nestjs/common";
import {
  ECCCEmissionProgram,
  ECCCSubType,
  ECCCWildlifeCompliance,
  OGD_ECCC_PROGRAM,
  Product
} from "nest-modules";
import { BaseStrategy, OgdProgramCollection } from "./base.strategy";
import { ApplicableOgdFiling } from "../types/strategy.types";

@Injectable()
export class EcccStrategy extends BaseStrategy {
  private readonly supportedPrograms = [
    OGD_ECCC_PROGRAM.TRANSPORT_PROGRAM,
    OGD_ECCC_PROGRAM.WILDLIFE_ENFORCEMENT_SOMETIMES_REGULATED
  ];

  private readonly excludedHSCodePrefixes = [
    "0101",
    "0102",
    "0103",
    "0104",
    "0105",
    "0106",
    "0201",
    "0202",
    "0204",
    "0205",
    "0206",
    "020724",
    "020725",
    "020726",
    "020727",
    "020741",
    "020742",
    "020743",
    "020744",
    "020745",
    "020751",
    "020752",
    "020753",
    "020754",
    "020755",
    "02076",
    "0208",
    "0210",
    "0301",
    "0302",
    "0303",
    "0304",
    "0305",
    "0306",
    "0307",
    "0308",
    "0309902100",
    "0309902100",
    "0309902900",
    "0309902900",
    "0309902900",
    "0309100000",
    "04101",
    "04109",
    "05021",
    "0504",
    "0505",
    "0506",
    "0507",
    "0508",
    "0510",
    "0511",
    "0601101",
    "0601102",
    "06012",
    "0602",
    "060313",
    "060319",
    "080111",
    "080112",
    "080119",
    "1211",
    "130190",
    "130219",
    "1504101",
    "1504109",
    "15042",
    "15043",
    "1602901",
    "1602909",
    "1603",
    "1604",
    "1605",
    "160556",
    "160557",
    "160558"
  ];

  private readonly isHsCodeStartsWithExcludedPrefix = (hsCode: string) => {
    return this.excludedHSCodePrefixes.some((prefix) => hsCode.startsWith(prefix));
  };

  async getApplicableFilings(
    product: Product,
    programCollection: OgdProgramCollection
  ): Promise<ApplicableOgdFiling[]> {
    // we will not fill for products that are excluded from the ECCC program
    if (this.isHsCodeStartsWithExcludedPrefix(product.hsCode)) {
      return [];
    }

    const applicableFilings: ApplicableOgdFiling[] = [];

    // if the product is not applicable to any of the supported programs, return an empty array
    if (this.supportedPrograms.every((program) => !programCollection.hasProgram(program))) {
      return [];
    }

    // Transport Program
    if (programCollection.hasProgram(OGD_ECCC_PROGRAM.TRANSPORT_PROGRAM)) {
      const transportProgram = programCollection.getProgram(OGD_ECCC_PROGRAM.TRANSPORT_PROGRAM);

      applicableFilings.push({
        program: transportProgram.program,
        payload: {
          ogdId: transportProgram.id,
          ecccSubType: ECCCSubType.EMISSIONS,
          emissionProgram: ECCCEmissionProgram.EXEMPT
        }
      });
    }

    // Wildlife Enforcement Sometimes Regulated
    if (programCollection.hasProgram(OGD_ECCC_PROGRAM.WILDLIFE_ENFORCEMENT_SOMETIMES_REGULATED)) {
      const wildlifeProgram = programCollection.getProgram(
        OGD_ECCC_PROGRAM.WILDLIFE_ENFORCEMENT_SOMETIMES_REGULATED
      );

      applicableFilings.push({
        program: wildlifeProgram.program,
        payload: {
          ogdId: wildlifeProgram.id,
          ecccSubType: ECCCSubType.WILDLIFE,
          wildlifeCompliance: ECCCWildlifeCompliance.NOT_ENDANGERED
        }
      });
    }

    return applicableFilings;
  }
}
