import { Injectable } from "@nestjs/common";
import { NRCANSubType, OGD_NRCAN_PROGRAM, Product } from "nest-modules";
import { BaseStrategy, OgdProgramCollection } from "./base.strategy";
import { ApplicableOgdFiling } from "../types/strategy.types";

@Injectable()
export class NrcanStrategy extends BaseStrategy {
  async getApplicableFilings(
    _: Product,
    programCollection: OgdProgramCollection
  ): Promise<ApplicableOgdFiling[]> {
    const applicableFilings: ApplicableOgdFiling[] = [];

    for (const program of programCollection.getPrograms()) {
      if (program.program === OGD_NRCAN_PROGRAM.OFFICE_OF_ENERGY_EFFICIENCY) {
        applicableFilings.push({
          program: program.program,
          payload: {
            ogdId: program.id,
            nrcanSubType: NRCANSubType.ENERGY_EFFICIENCY,
            isRegulated: false,
            isExcluded: false
          }
        });
      }
    }

    return applicableFilings;
  }
}
