import { OpenAIService } from "@/llm";
import { Injectable } from "@nestjs/common";
import {
  CanadaOgd,
  HCIntendedUse,
  HCProductCategory,
  OGD_HEALTH_CANADA_PROGRAM,
  Product
} from "nest-modules";
import { zodResponseFormat } from "openai/helpers/zod";
import { z } from "zod";
import { BaseStrategy, OgdProgramCollection } from "./base.strategy";
import { ApplicableOgdFiling } from "../types/strategy.types";

@Injectable()
export class HealthCanadaStrategy extends BaseStrategy {
  constructor(private readonly openAiService: OpenAIService) {
    super();
  }

  async getApplicablePrograms(product: Product, programs: Partial<CanadaOgd>[]) {
    const candidatePrograms = programs.map((program) => program.program);

    const prompt = `
    You are a helpful assistant that is given a list of programs that are applicable to a product.
    Your job is to determine which program the product falls under.

    Product Description: ${product.description}
    Candidate programs: ${candidatePrograms.join(", ")}

    Return the program that the product falls under.
    `;

    const responseFormat = z.object({
      program: z.string(),
      reasoning: z.string()
    });

    const response = await this.openAiService.createChatCompletion({
      model: "o3-mini",
      messages: [{ role: "user", content: prompt }],
      response_format: zodResponseFormat(responseFormat, "program")
    });

    console.log(response.choices[0].message.content);

    return JSON.parse(response.choices[0].message.content).program;
  }

  async getApplicableFilings(
    product: Product,
    programCollection: OgdProgramCollection
  ): Promise<ApplicableOgdFiling[]> {
    const hasConsumerProductSafetyProgram = programCollection.hasProgram(
      OGD_HEALTH_CANADA_PROGRAM.CONSUMER_PRODUCT_SAFETY
    );

    if (!hasConsumerProductSafetyProgram) {
      return [];
    }

    const consumerProductSafetyProgram = programCollection.getProgram(
      OGD_HEALTH_CANADA_PROGRAM.CONSUMER_PRODUCT_SAFETY
    );

    // we only fill the Consumer Product Safety program
    if (programCollection.getProgramCount() > 1) {
      const programName = await this.getApplicablePrograms(product, programCollection.getPrograms());

      if (programName !== OGD_HEALTH_CANADA_PROGRAM.CONSUMER_PRODUCT_SAFETY) {
        return [];
      }
    }

    return [
      {
        program: consumerProductSafetyProgram.program,
        payload: {
          ogdId: consumerProductSafetyProgram.id,
          productCategory: HCProductCategory.CONSUMER_PRODUCTS_CONSUMER_PRODUCT_FOR_ALL_AGES, // HC37
          endUse: HCIntendedUse.CONSUMER_PRODUCTS_SALE_DISTRIBUTION, // HC21
          isExcluded: false,
          isRegulated: false
        }
      }
    ];
  }
}
