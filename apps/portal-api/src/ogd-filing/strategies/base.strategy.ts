import { CanadaOgd, Product } from "nest-modules/entities";
import { ApplicableOgdFiling } from "../types/strategy.types";

export class OgdProgramCollection {
  private readonly programs: Map<string, Partial<CanadaOgd>>;

  constructor(programs: Partial<CanadaOgd>[]) {
    this.programs = new Map(programs.map((program) => [program.program, program]));
  }

  hasProgram(program: string) {
    return this.programs.has(program);
  }

  getProgram(program: string) {
    return this.programs.get(program);
  }

  getPrograms() {
    return Array.from(this.programs.values());
  }

  getProgramCount() {
    return this.programs.size;
  }
}

export abstract class BaseStrategy {
  abstract getApplicableFilings(
    product: Product,
    programCollection: OgdProgramCollection
  ): Promise<ApplicableOgdFiling[]>;
}
