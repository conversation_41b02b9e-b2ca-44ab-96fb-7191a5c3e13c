import { CreateOgdFilingDto, EditOgdFilingDto, User } from "nest-modules";

export class BaseOgdFilingEventDto {
  constructor(
    /**
     * ID of the OGD Filing
     */
    public readonly filingId: number,
    /**
     * User who triggered the event.
     */
    public readonly user?: User | null
  ) {}
}

export class OgdFilingCreatedEventDto extends BaseOgdFilingEventDto {
  constructor(
    filingId: number,
    user?: User | null,
    /**
     * DTO used to create the OGD filing.
     */
    public readonly createOgdFilingDto?: CreateOgdFilingDto | null
  ) {
    super(filingId, user);
  }
}

export class OgdFilingEditedEventDto extends BaseOgdFilingEventDto {
  constructor(
    filingId: number,
    user?: User | null,
    /**
     * DTO used to edit the OGD filing.
     */
    public readonly editOgdFilingDto?: EditOgdFilingDto | null
  ) {
    super(filingId, user);
  }
}

export class OgdFilingDeletedEventDto extends BaseOgdFilingEventDto {
  constructor(filingId: number, user?: User | null) {
    super(filingId, user);
  }
}
