import { ComplianceValidationService } from "@/shipment/services/compliance-validation.service";
import { ShipmentService } from "@/shipment/services/shipment.service";
import { InjectQueue } from "@nestjs/bullmq";
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import {
  Container,
  FIND_CONTAINER_RELATIONS,
  Shipment,
  ShipmentMode,
  ShipmentStatus,
  TrackingHistoryService,
  TrackingStatus
} from "nest-modules";
import { DataSource, Repository } from "typeorm";
import {
  CarrierTrackingQueue,
  PickupReturnTrackingQueue,
  PortTrackingQueue,
  RailTrackingQueue,
  TrackingQueueName
} from "../types/queue.types";
import { md5Hash } from "../utils/md5-hash";
import { SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS } from "../types/container.types";
import { ContainerService } from "@/shipment/services/container.service";
import { daysInMilliseconds } from "../utils/days-in-milliseconds";
@Injectable({ scope: Scope.REQUEST })
export class ShipmentTrackingService {
  constructor(
    @InjectRepository(Container)
    private readonly containerRepository: Repository<Container>,
    @InjectQueue(TrackingQueueName.SHIPMENT_CARRIER_TRACKING)
    private readonly carrierTrackingQueue: CarrierTrackingQueue,
    @InjectQueue(TrackingQueueName.SHIPMENT_PORT_TRACKING)
    private readonly portTrackingQueue: PortTrackingQueue,
    @InjectQueue(TrackingQueueName.SHIPMENT_RAIL_TRACKING)
    private readonly railTrackingQueue: RailTrackingQueue,
    @InjectQueue(TrackingQueueName.SHIPMENT_PICKUP_RETURN_TRACKING)
    private readonly pickupReturnTrackingQueue: PickupReturnTrackingQueue,
    @Inject(forwardRef(() => ShipmentService))
    private readonly shipmentService: ShipmentService,
    @Inject(forwardRef(() => ContainerService))
    private readonly containerService: ContainerService,
    @Inject(forwardRef(() => ComplianceValidationService))
    private readonly complianceValidationService: ComplianceValidationService,
    @Inject(forwardRef(() => TrackingHistoryService))
    private readonly trackingHistoryService: TrackingHistoryService,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}
  private readonly logger = new Logger(ShipmentTrackingService.name);

  /**
   * Checks if a container is valid for carrier tracking
   * @param container - The container to check
   * @returns An object containing a boolean indicating if the container is valid and an array of invalid reasons
   */
  isValidCarrierTrackingContainer(container: Container) {
    const invalidReasons: Array<string> = [];
    // Container must be in new or in transit status
    if (![ShipmentStatus.NEW, ShipmentStatus.IN_TRANSIT].includes(container.status))
      invalidReasons.push("Not in new or in transit status");
    // Shipment must be an ocean shipment
    if (![ShipmentMode.OCEAN_FCL, ShipmentMode.OCEAN_LCL].includes(container?.shipment?.modeOfTransport))
      invalidReasons.push("Not an ocean shipment");
    return {
      isValid: invalidReasons.length <= 0,
      invalidReasons
    };
  }

  /**
   * Checks if a container is valid for port tracking
   * @param container - The container to check
   * @returns An object containing a boolean indicating if the shipment is valid and an array of invalid reasons
   */
  isValidPortTrackingContainer(container: Container) {
    const invalidReasons: Array<string> = [];
    // Container must be at port or current date is at or after ETA Port + 1 day
    if (
      container.status !== ShipmentStatus.AT_PORT &&
      !(
        [ShipmentStatus.NEW, ShipmentStatus.IN_TRANSIT].includes(container.status) &&
        container.shipment.etaPort instanceof Date &&
        container.shipment.etaPort.getTime() + daysInMilliseconds(1) <= Date.now()
      )
    )
      invalidReasons.push("Not in at port status and (ETA Port + 1 day) has not passed");
    // Shipment must be an ocean shipment
    if (![ShipmentMode.OCEAN_FCL, ShipmentMode.OCEAN_LCL].includes(container?.shipment?.modeOfTransport))
      invalidReasons.push("Not an ocean shipment");
    // Shipment must be delivered to an ocean port city
    if (
      !container?.shipment?.placeOfDelivery ||
      this.complianceValidationService.isShipmentPODInland(container?.shipment)
    )
      invalidReasons.push("Place of delivery missing or is not an ocean port city");
    // Container's Pickup LFD must be empty
    if (container?.pickupLfd) invalidReasons.push("Pickup LFD is already set");
    return {
      isValid: invalidReasons.length <= 0,
      invalidReasons
    };
  }

  /**
   * Checks if a container is valid for rail tracking
   * @param container - The container to check
   * @returns An object containing a boolean indicating if the container is valid and an array of invalid reasons
   */
  isValidRailTrackingContainer(container: Container) {
    const invalidReasons: Array<string> = [];
    // Container must be at port or on rail or current date is at or after ETA Port + 3 days
    if (
      ![ShipmentStatus.AT_PORT, ShipmentStatus.ON_RAIL].includes(container.status) &&
      !(
        [ShipmentStatus.NEW, ShipmentStatus.IN_TRANSIT].includes(container.status) &&
        container.shipment.etaPort instanceof Date &&
        container.shipment.etaPort.getTime() + daysInMilliseconds(3) <= Date.now()
      )
    )
      invalidReasons.push("Not in at port or on rail status and (ETA Port + 3 days) has not passed");
    // Shipment must be an ocean shipment
    if (![ShipmentMode.OCEAN_FCL, ShipmentMode.OCEAN_LCL].includes(container?.shipment?.modeOfTransport))
      invalidReasons.push("Not an ocean shipment");
    // Shipment must NOT be delivered to an ocean port city
    if (
      !container?.shipment?.placeOfDelivery ||
      !this.complianceValidationService.isShipmentPODInland(container?.shipment)
    )
      invalidReasons.push("Place of delivery missing or is an ocean port city");
    // Container's Pickup LFD must be empty
    if (container.pickupLfd) invalidReasons.push("Pickup LFD is already set");
    return {
      isValid: invalidReasons.length <= 0,
      invalidReasons
    };
  }

  /**
   * Checks if a container is valid for pickup return tracking
   * @param container - The container to check
   * @returns An object containing a boolean indicating if the container is valid and an array of invalid reasons
   */
  isValidPickupReturnTrackingContainer(container: Container) {
    const invalidReasons: Array<string> = [];
    // Container must be at port, at terminal or picked up
    if (
      ![ShipmentStatus.AT_PORT, ShipmentStatus.AT_TERMINAL, ShipmentStatus.PICKED_UP].includes(
        container.status
      )
    )
      invalidReasons.push("Not in at port, at terminal or picked up status");
    // Shipment must be an ocean shipment
    if (![ShipmentMode.OCEAN_FCL, ShipmentMode.OCEAN_LCL].includes(container?.shipment?.modeOfTransport))
      invalidReasons.push("Not an ocean shipment");
    // Container must have a pickup LFD
    if (!container.pickupLfd) invalidReasons.push("Missing pickup LFD");
    return {
      isValid: invalidReasons.length <= 0,
      invalidReasons
    };
  }

  async trackOceanContainer(shipmentId: number, containerId: number) {
    const container = await this.containerService.getContainerById(
      shipmentId,
      containerId,
      undefined,
      false,
      SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS
    );
    if (!container) throw new NotFoundException("Container not found");
    if (container.trackingStatus === TrackingStatus.TRACKING)
      throw new BadRequestException("Tracking for this container is already in progress");

    const { isValid: isValidCarrierTracking, invalidReasons: invalidReasonsCarrierTracking } =
      this.isValidCarrierTrackingContainer(container);
    this.logger.debug(
      `Is Carrier Tracking Valid: ${isValidCarrierTracking}, Invalid Reasons: ${invalidReasonsCarrierTracking}`
    );
    const { isValid: isValidPortTracking, invalidReasons: invalidReasonsPortTracking } =
      this.isValidPortTrackingContainer(container);
    this.logger.debug(
      `Is Port Tracking Valid: ${isValidPortTracking}, Invalid Reasons: ${invalidReasonsPortTracking}`
    );
    const { isValid: isValidRailTracking, invalidReasons: invalidReasonsRailTracking } =
      this.isValidRailTrackingContainer(container);
    this.logger.debug(
      `Is Rail Tracking Valid: ${isValidRailTracking}, Invalid Reasons: ${invalidReasonsRailTracking}`
    );
    const { isValid: isValidPickupReturnTracking, invalidReasons: invalidReasonsPickupReturnTracking } =
      this.isValidPickupReturnTrackingContainer(container);
    this.logger.debug(
      `Is Pickup Return Tracking Valid: ${isValidPickupReturnTracking}, Invalid Reasons: ${invalidReasonsPickupReturnTracking}`
    );

    if (isValidPickupReturnTracking || isValidRailTracking || isValidPortTracking || isValidCarrierTracking) {
      this.logger.log(`Container is valid for tracking, updating tracking status...`);
      const updateResult = await this.dataSource.manager.update(
        Container,
        { id: container.id },
        { trackingStatus: TrackingStatus.TRACKING }
      );
      this.logger.debug(`Update Result: ${updateResult}`);
    }
    if (isValidPickupReturnTracking) {
      this.logger.log(
        `Container is valid for pickup return tracking, adding to pickup return tracking queue...`
      );
      await this.pickupReturnTrackingQueue.add(md5Hash(container.id.toString()), {
        containerIds: [container.id]
      });
    } else if (isValidRailTracking) {
      this.logger.log(`Container is valid for rail tracking, adding to rail tracking queue...`);
      await this.railTrackingQueue.add(md5Hash(container.id.toString()), {
        containerIds: [container.id]
      });
    } else if (isValidPortTracking) {
      this.logger.log(`Container is valid for port tracking, adding to port tracking queue...`);
      await this.portTrackingQueue.add(md5Hash(container.id.toString()), {
        containerIds: [container.id]
      });
    } else if (isValidCarrierTracking) {
      this.logger.log(`Container is valid for carrier tracking, adding to carrier tracking queue...`);
      await this.carrierTrackingQueue.add(md5Hash(container.id.toString()), {
        containerIds: [container.id]
      });
    } else {
      this.logger.log(`Container is not valid for any tracking, updating tracking history...`);
      const invalidTrackingHistory = await this.trackingHistoryService.createTrackingHistory({
        timestamp: new Date(),
        result: `Container is not valid for any tracking.`,
        shipmentId: container?.shipment?.id,
        party: "UNKNOWN"
      });
      this.logger.debug(`Invalid Tracking History: ${invalidTrackingHistory}`);
    }

    return await this.containerService.getContainerById(shipmentId, containerId);
  }
}
