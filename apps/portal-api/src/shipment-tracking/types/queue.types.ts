import { RegisterQueueOptions } from "@nestjs/bullmq";
import { NestWorkerOptions } from "@nestjs/bullmq/dist/interfaces/worker-options.interface";
import { Job, JobsOptions, Queue } from "bullmq";

export const DEFAULT_JOB_OPTIONS: JobsOptions = {
  attempts: 1,
  removeOnComplete: {
    age: 3600,
    count: 1000
  },
  removeOnFail: {
    age: 24 * 3600
  }
};

export const DEFAULT_WORKER_OPTIONS: NestWorkerOptions = {
  concurrency: 1
};

export enum TrackingQueueName {
  SHIPMENT_CARRIER_TRACKING = "shipment-carrier-tracking",
  SHIPMENT_PORT_TRACKING = "shipment-port-tracking",
  SHIPMENT_RAIL_TRACKING = "shipment-rail-tracking",
  SHIPMENT_PICKUP_RETURN_TRACKING = "shipment-pickup-return-tracking"
}

export const TRACKING_QUEUES: Array<RegisterQueueOptions> = [
  {
    name: TrackingQueueName.SHIPMENT_CARRIER_TRACKING,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  },
  {
    name: TrackingQueueName.SHIPMENT_PORT_TRACKING,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  },
  {
    name: TrackingQueueName.SHIPMENT_RAIL_TRACKING,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  },
  {
    name: TrackingQueueName.SHIPMENT_PICKUP_RETURN_TRACKING,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  }
];

// Carrier Tracking Queue
export interface CarrierTrackingJobData {
  // shipmentIds: Array<number>;
  containerIds: Array<number>;
}
export type CarrierTrackingJob = Job<CarrierTrackingJobData, null, string>;
export type CarrierTrackingQueue = Queue<CarrierTrackingJobData, null, string>;

// Port Tracking Queue
export interface PortTrackingJobData {
  containerIds: Array<number>;
}
export type PortTrackingJob = Job<PortTrackingJobData, null, string>;
export type PortTrackingQueue = Queue<PortTrackingJobData, null, string>;

// Rail Tracking Queue
export interface RailTrackingJobData {
  containerIds: Array<number>;
}
export type RailTrackingJob = Job<RailTrackingJobData, null, string>;
export type RailTrackingQueue = Queue<RailTrackingJobData, null, string>;

// Pickup Return Tracking Queue
export interface PickupReturnTrackingJobData {
  containerIds: Array<number>;
}
export type PickupReturnTrackingJob = Job<PickupReturnTrackingJobData, null, string>;
export type PickupReturnTrackingQueue = Queue<PickupReturnTrackingJobData, null, string>;

// Generic Tracking Job Data
export type TrackingJobData =
  | CarrierTrackingJobData
  | PortTrackingJobData
  | RailTrackingJobData
  | PickupReturnTrackingJobData;
export type TrackingJob = Job<TrackingJobData, null, string>;
