export enum TrackingEvent {
  // Cronjob Events
  CARRIER_TRACKING_SCHEDULED_RUN = "carrier-tracking.scheduled-run",
  PORT_TRACKING_SCHEDULED_RUN = "port-tracking.scheduled-run",
  RAIL_TRACKING_SCHEDULED_RUN = "rail-tracking.scheduled-run",
  PIC<PERSON>UP_RETURN_TRACKING_SCHEDULED_RUN = "pickup-return-tracking.scheduled-run",

  // Error Events
  CARRIER_TRACKING_ERROR = "carrier-tracking.error",
  PORT_TRACKING_ERROR = "port-tracking.error",
  RAIL_TRACKING_ERROR = "rail-tracking.error",
  PICKUP_RETURN_TRACKING_ERROR = "pickup-return-tracking.error"
}
