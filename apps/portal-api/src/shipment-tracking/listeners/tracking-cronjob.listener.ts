import { InjectQueue } from "@nestjs/bullmq";
import { Injectable, Logger } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { InjectRepository } from "@nestjs/typeorm";
import {
  Container,
  FIND_CONTAINER_RELATIONS,
  FIND_SHIPMENT_RELATIONS,
  Shipment,
  ShipmentMode,
  ShipmentStatus,
  TrackingStatus
} from "nest-modules";
import { In, IsNull, LessThanOrEqual, MoreThanOrEqual, Not, Raw, Repository } from "typeorm";
import { TrackingEvent } from "../types/event.types";
import { OCEAN_PORT_CITIES } from "../types/port.types";
import {
  CarrierTrackingQueue,
  PickupReturnTrackingQueue,
  PortTrackingQueue,
  RailTrackingQueue,
  TrackingQueueName
} from "../types/queue.types";
import { md5Hash } from "../utils/md5-hash";
import { SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS } from "../types/container.types";
import { daysInMilliseconds } from "../utils/days-in-milliseconds";

@Injectable()
export class TrackingCronjobListener {
  constructor(
    @InjectRepository(Shipment)
    private readonly shipmentRepository: Repository<Shipment>,
    @InjectRepository(Container)
    private readonly containerRepository: Repository<Container>,
    @InjectQueue(TrackingQueueName.SHIPMENT_CARRIER_TRACKING)
    private readonly carrierTrackingQueue: CarrierTrackingQueue,
    @InjectQueue(TrackingQueueName.SHIPMENT_PORT_TRACKING)
    private readonly portTrackingQueue: PortTrackingQueue,
    @InjectQueue(TrackingQueueName.SHIPMENT_RAIL_TRACKING)
    private readonly railTrackingQueue: RailTrackingQueue,
    @InjectQueue(TrackingQueueName.SHIPMENT_PICKUP_RETURN_TRACKING)
    private readonly pickupReturnTrackingQueue: PickupReturnTrackingQueue
  ) {}
  private readonly logger = new Logger(TrackingCronjobListener.name);

  @OnEvent(TrackingEvent.CARRIER_TRACKING_SCHEDULED_RUN)
  async onCarrierTrackingScheduledRun() {
    this.logger.log(`Received carrier tracking scheduled run event, finding valid shipments...`);

    // Get valid containers
    const validContainers = await this.containerRepository.find({
      where: {
        trackingStatus: Not(TrackingStatus.TRACKING),
        status: In([ShipmentStatus.NEW, ShipmentStatus.IN_TRANSIT]),
        shipment: {
          modeOfTransport: In([ShipmentMode.OCEAN_FCL, ShipmentMode.OCEAN_LCL])
        }
      },
      relations: SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS
    });
    this.logger.log(`Found ${validContainers.length} valid containers for carrier tracking`);

    if (validContainers.length > 0) {
      // Update tracking status to tracking
      const updateResult = await this.containerRepository
        .createQueryBuilder()
        .update()
        .set({ trackingStatus: TrackingStatus.TRACKING })
        .where("id IN (:...containerIds)", { containerIds: validContainers.map((container) => container.id) })
        .execute();
      this.logger.log(`Updated ${updateResult.affected} containers to tracking`);

      // Add containers to carrier tracking queue
      const newJob = await this.carrierTrackingQueue.add(
        md5Hash(validContainers.map((c) => c.id).join(",")),
        {
          containerIds: validContainers.map((c) => c.id)
        }
      );
      this.logger.log(
        `Added new job ${newJob.id} to carrier tracking queue. Job Data: ${JSON.stringify(newJob.data)}`
      );
    }
  }

  @OnEvent(TrackingEvent.PORT_TRACKING_SCHEDULED_RUN)
  async onPortTrackingScheduledRun() {
    this.logger.log(`Received port tracking scheduled run event, finding valid shipments...`);

    // Get valid containers
    const validContainers = await this.containerRepository.find({
      where: [
        {
          trackingStatus: Not(TrackingStatus.TRACKING),
          status: ShipmentStatus.AT_PORT,
          pickupLfd: IsNull(),
          shipment: {
            modeOfTransport: In([ShipmentMode.OCEAN_FCL, ShipmentMode.OCEAN_LCL]),
            placeOfDelivery: {
              name: Raw(
                (alias) =>
                  `${alias} IS NOT NULL AND (${OCEAN_PORT_CITIES.map((city) => `UPPER(${alias}) LIKE '%${city}%'`).join(" OR ")})`
              )
            }
          }
        },
        {
          trackingStatus: Not(TrackingStatus.TRACKING),
          status: In([ShipmentStatus.NEW, ShipmentStatus.IN_TRANSIT]),
          shipment: {
            modeOfTransport: In([ShipmentMode.OCEAN_FCL, ShipmentMode.OCEAN_LCL]),
            etaPort: LessThanOrEqual(new Date(Date.now() - daysInMilliseconds(1))),
            placeOfDelivery: {
              name: Raw(
                (alias) =>
                  `${alias} IS NOT NULL AND (${OCEAN_PORT_CITIES.map((city) => `UPPER(${alias}) LIKE '%${city}%'`).join(" OR ")})`
              )
            }
          }
        }
      ],
      relations: SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS
    });
    this.logger.log(`Found ${validContainers.length} valid containers for port tracking`);

    if (validContainers.length > 0) {
      // Update tracking status to tracking
      const updateResult = await this.containerRepository
        .createQueryBuilder()
        .update()
        .set({ trackingStatus: TrackingStatus.TRACKING })
        .where("id IN (:...containerIds)", { containerIds: validContainers.map((c) => c.id) })
        .execute();
      this.logger.log(`Updated ${updateResult.affected} containers to tracking`);

      // Add containers to port tracking queue
      const newJob = await this.portTrackingQueue.add(md5Hash(validContainers.map((c) => c.id).join(",")), {
        containerIds: validContainers.map((c) => c.id)
      });
      this.logger.log(
        `Added new job ${newJob.id} to port tracking queue. Job Data: ${JSON.stringify(newJob.data)}`
      );
    }
  }

  @OnEvent(TrackingEvent.RAIL_TRACKING_SCHEDULED_RUN)
  async onRailTrackingScheduledRun() {
    this.logger.log(`Received rail tracking scheduled run event, finding valid shipments...`);

    // Get valid containers
    const validContainers = await this.containerRepository.find({
      where: [
        {
          trackingStatus: Not(TrackingStatus.TRACKING),
          status: In([ShipmentStatus.AT_PORT, ShipmentStatus.ON_RAIL]),
          pickupLfd: IsNull(),
          shipment: {
            modeOfTransport: In([ShipmentMode.OCEAN_FCL, ShipmentMode.OCEAN_LCL]),
            placeOfDelivery: {
              name: Raw(
                (alias) =>
                  `${alias} IS NOT NULL AND ${OCEAN_PORT_CITIES.map((city) => `UPPER(${alias}) NOT LIKE '%${city}%'`).join(" AND ")}`
              )
            }
          }
        },
        {
          trackingStatus: Not(TrackingStatus.TRACKING),
          status: In([ShipmentStatus.NEW, ShipmentStatus.IN_TRANSIT]),
          shipment: {
            modeOfTransport: In([ShipmentMode.OCEAN_FCL, ShipmentMode.OCEAN_LCL]),
            etaPort: LessThanOrEqual(new Date(Date.now() - daysInMilliseconds(3))),
            placeOfDelivery: {
              name: Raw(
                (alias) =>
                  `${alias} IS NOT NULL AND ${OCEAN_PORT_CITIES.map((city) => `UPPER(${alias}) NOT LIKE '%${city}%'`).join(" AND ")}`
              )
            }
          }
        }
      ],
      relations: SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS
    });
    this.logger.log(`Found ${validContainers.length} valid containers for rail tracking`);

    if (validContainers.length > 0) {
      // Update tracking status to tracking
      const updateResult = await this.containerRepository
        .createQueryBuilder()
        .update()
        .set({ trackingStatus: TrackingStatus.TRACKING })
        .where("id IN (:...containerIds)", { containerIds: validContainers.map((c) => c.id) })
        .execute();
      this.logger.log(`Updated ${updateResult.affected} containers to tracking`);

      // Add containers to rail tracking queue
      const newJob = await this.railTrackingQueue.add(md5Hash(validContainers.map((c) => c.id).join(",")), {
        containerIds: validContainers.map((c) => c.id)
      });
      this.logger.log(
        `Added new job ${newJob.id} to rail tracking queue. Job Data: ${JSON.stringify(newJob.data)}`
      );
    }
  }

  @OnEvent(TrackingEvent.PICKUP_RETURN_TRACKING_SCHEDULED_RUN)
  async onPickupReturnTrackingScheduledRun() {
    this.logger.log(`Received pickup return tracking scheduled run event, finding valid shipments...`);

    // Get valid containers
    const validContainers = await this.containerRepository.find({
      where: {
        trackingStatus: Not(TrackingStatus.TRACKING),
        status: In([ShipmentStatus.AT_PORT, ShipmentStatus.AT_TERMINAL, ShipmentStatus.PICKED_UP]),
        pickupLfd: Not(IsNull()),
        shipment: {
          modeOfTransport: In([ShipmentMode.OCEAN_FCL, ShipmentMode.OCEAN_LCL])
        }
      },
      relations: SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS
    });
    this.logger.log(`Found ${validContainers.length} valid containers for pickup return tracking`);

    if (validContainers.length > 0) {
      // Update tracking status to tracking
      const updateResult = await this.containerRepository
        .createQueryBuilder()
        .update()
        .set({ trackingStatus: TrackingStatus.TRACKING })
        .where("id IN (:...containerIds)", { containerIds: validContainers.map((c) => c.id) })
        .execute();
      this.logger.log(`Updated ${updateResult.affected} containers to tracking`);

      // Add containers to pickup return tracking queue
      const newJob = await this.pickupReturnTrackingQueue.add(
        md5Hash(validContainers.map((c) => c.id).join(",")),
        {
          containerIds: validContainers.map((c) => c.id)
        }
      );
      this.logger.log(
        `Added new job ${newJob.id} to pickup return tracking queue. Job Data: ${JSON.stringify(newJob.data)}`
      );
    }
  }
}
