import { forwardRef, Inject, Injectable, Logger } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { InjectDataSource } from "@nestjs/typeorm";
import {
  Container,
  FIND_CONTAINER_RELATIONS,
  FIND_SHIPMENT_RELATIONS,
  Shipment,
  TrackingHistoryService,
  TrackingStatus
} from "nest-modules";
import { DataSource, In } from "typeorm";
import { TrackingEvent } from "../types/event.types";
import {
  CarrierTrackingJob,
  PickupReturnTrackingJob,
  PortTrackingJob,
  RailTrackingJob
} from "../types/queue.types";
import { SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS } from "../types/container.types";

@Injectable()
export class TrackingQueueListener {
  constructor(
    @Inject(forwardRef(() => TrackingHistoryService))
    private readonly trackingHistoryService: TrackingHistoryService,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}
  private readonly logger = new Logger(TrackingQueueListener.name);

  @OnEvent(TrackingEvent.CARRIER_TRACKING_ERROR)
  async onCarrierTrackingError(job: CarrierTrackingJob, jobError?: Error) {
    this.logger.error(
      `On Carrier Tracking Error. Job Data: ${JSON.stringify(job?.data)}. Error: ${jobError?.message}`
    );

    const { containerIds } = job?.data ?? {};
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const containerRepository = queryRunner.manager.getRepository(Container);

      // Get containers
      const containers =
        (containerIds || []).length > 0
          ? await containerRepository.find({
              where: {
                id: In(containerIds)
              },
              relations: SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS
            })
          : [];

      if (containers.length > 0) {
        // Update container tracking status to error
        for (const container of containers) {
          const updateResult = await containerRepository.update(container.id, {
            trackingStatus: TrackingStatus.ERROR
          });
          this.logger.debug(
            `Container ${container.id} of shipment ${container?.shipment?.id} updated: ${updateResult.affected}`
          );

          //  Create error tracking history
          const errorTrackingHistory = await this.trackingHistoryService.createTrackingHistory({
            timestamp: new Date(),
            result: `Error in carrier tracking for container ${container?.containerNumber}: ${jobError?.message || "Unknown error"}`,
            shipmentId: container?.shipment?.id,
            party: "ERROR"
          });
          this.logger.debug(
            `Error tracking history created for container ${container.id} of shipment ${container?.shipment?.id}: ${errorTrackingHistory?.id}`
          );
        }
      }

      // TODO: Send error email to backoffice admin

      await queryRunner.commitTransaction();
    } catch (error) {
      this.logger.error(
        `Got error in carrier tracking error listener: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
  }

  @OnEvent(TrackingEvent.PORT_TRACKING_ERROR)
  async onPortTrackingError(job: PortTrackingJob, jobError?: Error) {
    this.logger.error(
      `On Port Tracking Error. Job Data: ${JSON.stringify(job?.data)}. Error: ${jobError?.message}`
    );

    const { containerIds } = job?.data ?? {};
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const shipmentRepository = queryRunner.manager.getRepository(Shipment);
      const containerRepository = queryRunner.manager.getRepository(Container);

      // Get containers
      const containers =
        (containerIds || []).length > 0
          ? await containerRepository.find({
              where: {
                id: In(containerIds)
              },
              relations: SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS
            })
          : [];

      if (containers.length > 0) {
        // Update container tracking status to error
        for (const container of containers) {
          const updateResult = await containerRepository.update(container.id, {
            trackingStatus: TrackingStatus.ERROR
          });
          this.logger.debug(
            `Container ${container.id} of shipment ${container?.shipment?.id} updated: ${updateResult.affected}`
          );

          //  Create error tracking history
          const errorTrackingHistory = await this.trackingHistoryService.createTrackingHistory({
            timestamp: new Date(),
            result: `Error in port tracking for container ${container?.containerNumber}: ${jobError?.message || "Unknown error"}`,
            shipmentId: container?.shipment?.id,
            party: "ERROR"
          });
          this.logger.debug(
            `Error tracking history created for container ${container.id} of shipment ${container?.shipment?.id}: ${errorTrackingHistory?.id}`
          );
        }
      }

      // TODO: Send error email to backoffice admin

      await queryRunner.commitTransaction();
    } catch (error) {
      this.logger.error(
        `Got error in port tracking error listener: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
  }

  @OnEvent(TrackingEvent.RAIL_TRACKING_ERROR)
  async onRailTrackingError(job: RailTrackingJob, jobError?: Error) {
    this.logger.error(
      `On Rail Tracking Error. Job Data: ${JSON.stringify(job?.data)}. Error: ${jobError?.message}`
    );

    const { containerIds } = job?.data ?? {};
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const shipmentRepository = queryRunner.manager.getRepository(Shipment);
      const containerRepository = queryRunner.manager.getRepository(Container);

      // Get containers
      const containers =
        (containerIds || []).length > 0
          ? await containerRepository.find({
              where: {
                id: In(containerIds)
              },
              relations: SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS
            })
          : [];

      if (containers.length > 0) {
        // Update container tracking status to error
        for (const container of containers) {
          const updateResult = await containerRepository.update(container.id, {
            trackingStatus: TrackingStatus.ERROR
          });
          this.logger.debug(
            `Container ${container.id} of shipment ${container?.shipment?.id} updated: ${updateResult.affected}`
          );

          //  Create error tracking history
          const errorTrackingHistory = await this.trackingHistoryService.createTrackingHistory({
            timestamp: new Date(),
            result: `Error in rail tracking for container ${container?.containerNumber}: ${jobError?.message || "Unknown error"}`,
            shipmentId: container?.shipment?.id,
            party: "ERROR"
          });
          this.logger.debug(
            `Error tracking history created for container ${container.id} of shipment ${container?.shipment?.id}: ${errorTrackingHistory?.id}`
          );
        }
      }

      // TODO: Send error email to backoffice admin

      await queryRunner.commitTransaction();
    } catch (error) {
      this.logger.error(
        `Got error in rail tracking error listener: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
  }

  @OnEvent(TrackingEvent.PICKUP_RETURN_TRACKING_ERROR)
  async onPickupReturnTrackingError(job: PickupReturnTrackingJob, jobError?: Error) {
    this.logger.error(
      `On Pickup Return Tracking Error. Job Data: ${JSON.stringify(job?.data)}. Error: ${jobError?.message}`
    );

    const { containerIds } = job?.data ?? {};
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const containerRepository = queryRunner.manager.getRepository(Container);

      // Get containers
      const containers =
        (containerIds || []).length > 0
          ? await containerRepository.find({
              where: {
                id: In(containerIds)
              },
              relations: SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS
            })
          : [];

      if (containers.length > 0) {
        // Update container tracking status to error
        for (const container of containers) {
          const updateResult = await containerRepository.update(container.id, {
            trackingStatus: TrackingStatus.ERROR
          });
          this.logger.debug(
            `Container ${container.id} of shipment ${container?.shipment?.id} updated: ${updateResult.affected}`
          );

          //  Create error tracking history
          const errorTrackingHistory = await this.trackingHistoryService.createTrackingHistory({
            timestamp: new Date(),
            result: `Error in pickup return tracking for container ${container?.containerNumber}: ${jobError?.message || "Unknown error"}`,
            shipmentId: container?.shipment?.id,
            party: "ERROR"
          });
          this.logger.debug(
            `Error tracking history created for container ${container.id} of shipment ${container?.shipment?.id}: ${errorTrackingHistory?.id}`
          );
        }
      }

      // TODO: Send error email to backoffice admin

      await queryRunner.commitTransaction();
    } catch (error) {
      this.logger.error(
        `Got error in pickup return tracking error listener: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
  }
}
