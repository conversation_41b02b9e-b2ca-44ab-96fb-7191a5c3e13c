import {
  Controller,
  HttpCode,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  UseGuards
} from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import {
  ApiBadRequestResponse,
  ApiExcludeEndpoint,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags
} from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  BadRequestResponseDto,
  Container,
  NotFoundResponseDto,
  Shipment
} from "nest-modules";
import { ShipmentTrackingService } from "../services/shipment-tracking.service";
import { TrackingEvent } from "../types/event.types";
// import { OldShipmentTrackingService } from "../services/old-shipment-tracking.service";

@Controller("shipment-tracking")
@ApiTags("Shipment Tracking API")
@UseGuards(AccessTokenGuard)
@ApiAccessTokenAuthenticated()
export class ShipmentTrackingController {
  constructor(
    private readonly shipmentTrackingService: ShipmentTrackingService,
    private readonly eventEmitter: EventEmitter2
  ) {}

  @ApiOperation({ summary: "Track Ocean Container" })
  @ApiParam({ name: "id", type: "integer", description: "Shipment ID" })
  @ApiParam({ name: "containerId", type: "integer", description: "Container ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiOkResponse({ type: Container })
  @HttpCode(200)
  @Post(":id/containers/:containerId")
  async trackOceanContainer(
    @Param("id", ParseIntPipe) id: number,
    @Param("containerId", ParseIntPipe) containerId: number
  ) {
    return await this.shipmentTrackingService.trackOceanContainer(id, containerId);
  }

  @ApiExcludeEndpoint()
  @Post("event/:event")
  async emitTrackingEvent(@Param("event") event: string) {
    switch (event) {
      case "carrier":
        this.eventEmitter.emit(TrackingEvent.CARRIER_TRACKING_SCHEDULED_RUN);
        break;
      case "port":
        this.eventEmitter.emit(TrackingEvent.PORT_TRACKING_SCHEDULED_RUN);
        break;
      case "rail":
        this.eventEmitter.emit(TrackingEvent.RAIL_TRACKING_SCHEDULED_RUN);
        break;
      case "pickup-return":
        this.eventEmitter.emit(TrackingEvent.PICKUP_RETURN_TRACKING_SCHEDULED_RUN);
        break;
      default:
        throw new NotFoundException("Invalid tracking event");
    }
    return;
  }
}
