import { Logger } from "@nestjs/common";
import { CarrierAbbreviation } from "../types/carrier.types";
import moment from "moment-timezone";

/**
 * Convert carrier-specific date string to date
 * @param carrierAbbreviation Carrier abbreviation
 * @param dateString Carrier-specific date string
 * @param caller Caller function name
 * @returns Date object
 */
export function carrierDateStringToDate(
  carrierAbbreviation: CarrierAbbreviation,
  dateString: string,
  caller: string
) {
  switch (carrierAbbreviation) {
    case CarrierAbbreviation.EGL:
      return moment.tz(dateString, "MMM-DD-YYYY", "America/Toronto").toDate();
    case CarrierAbbreviation.COSCO:
      return moment.tz(dateString, "YYYY-MM-DD HH:mm:ss", "America/Toronto").toDate();
    case CarrierAbbreviation.MSC:
      return moment.tz(dateString, "DD/MM/YYYY", "America/Toronto").toDate();
    case CarrierAbbreviation.ONE:
      return moment.tz(dateString, "YYYY-MM-DD HH:mm", "America/Toronto").toDate();
    case CarrierAbbreviation.CMA:
      return moment.tz(dateString, "dddd, DD-MMM-YYYY", "America/Toronto").toDate();
    case CarrierAbbreviation.SML:
      return moment.tz(dateString, "YYYY-MM-DD HH:mm", "America/Toronto").toDate();
    case CarrierAbbreviation.MAERSK:
      return moment.tz(dateString, "DD MMM YYYY HH:mm", "America/Toronto").toDate();
    default:
      Logger.warn(
        `Unknown carrier abbreviation: ${carrierAbbreviation}, converting to date without specific format...`,
        caller
      );
      return moment.tz(dateString, "America/Toronto").toDate();
  }
}
