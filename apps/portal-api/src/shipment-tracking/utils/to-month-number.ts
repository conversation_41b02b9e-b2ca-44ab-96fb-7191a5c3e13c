/**
 * Convert month abbreviation to month represented in number. Janurary is 0, February is 1, etc. If month abbreviation is not valid, return null.
 * @param monthAbbreviation The month abbreviation
 * @returns The month number
 */
export function toMonthNumber(monthAbbreviation: string) {
  const MONTH_ABBREVIATIONS = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec"
  ];
  const monthNumber = MONTH_ABBREVIATIONS.indexOf(monthAbbreviation);
  return monthNumber <= -1 ? null : monthNumber;
}
