import { ShipmentModule } from "@/shipment/shipment.module";
import { TradePartnerModule } from "@/trade-partner/trade-partner.module";
import { BullModule } from "@nestjs/bullmq";
import { DynamicModule, forwardRef, Logger, Module, Provider } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  ApifyModule,
  Container,
  Country,
  CountryModule,
  Location,
  Shipment,
  TrackingHistory,
  TradePartner
} from "nest-modules";
import { ShipmentTrackingController } from "./controllers/shipment-tracking.controller";
import { TrackingCronjobListener } from "./listeners/tracking-cronjob.listener";
import { TrackingQueueListener } from "./listeners/tracking-queue.listener";
import { CarrierTrackingProcessor } from "./processors/carrier-tracking.processor";
import { PickupReturnTrackingProcessor } from "./processors/pickup-return-tracking.processor";
import { PortTrackingProcessor } from "./processors/port-tracking.processor";
import { RailTrackingProcessor } from "./processors/rail-tracking.processor";
import { ShipmentTrackingService } from "./services/shipment-tracking.service";
import { TRACKING_QUEUES } from "./types/queue.types";

const listeners = [TrackingCronjobListener, TrackingQueueListener];

@Module({})
export class ShipmentTrackingModule {
  static forRoot(): DynamicModule {
    const providers: Array<Provider> = [
      // Services
      ShipmentTrackingService
    ];
    // TODO: Processors should be registered when feature is set to shipment tracking
    if (process.env.FEATURE) {
      Logger.warn("Shipment tracking processors are disabled when feature is set", "ShipmentTrackingModule");
    } else {
      Logger.log(
        "Shipment tracking processors are enabled when feature is not set",
        "ShipmentTrackingModule"
      );
      providers.push(
        // Processors
        CarrierTrackingProcessor,
        PortTrackingProcessor,
        RailTrackingProcessor,
        PickupReturnTrackingProcessor,
        ...listeners
      );
    }

    return {
      module: ShipmentTrackingModule,
      imports: [
        TypeOrmModule.forFeature([Shipment, Container, TradePartner, Location, TrackingHistory, Country]),
        BullModule.registerQueue(...TRACKING_QUEUES),
        ApifyModule,
        CountryModule,
        ShipmentModule,
        forwardRef(() => TradePartnerModule)
      ],
      controllers: [ShipmentTrackingController],
      providers,
      exports: [ShipmentTrackingService]
    };
  }
}
