import { Processor, WorkerHost } from "@nestjs/bullmq";
import { forwardRef, Inject, Logger } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectDataSource } from "@nestjs/typeorm";
import {
  Container,
  FIND_TRADE_PARTNER_RELATIONS,
  PartnerType,
  Shipment,
  ShipmentStatus,
  TrackingHistoryService,
  TrackingStatus,
  TradePartner,
  UserPermission
} from "nest-modules";
import { DataSource, In, IsNull, QueryRunner } from "typeorm";
import { ApifyActorRunDto } from "nest-modules";
import { ApifyService } from "nest-modules";
import { ShipmentTrackingService } from "../services/shipment-tracking.service";
import {
  ActorRunStatus,
  APIFY_CONTAINER_STATUS_CHECK_ACTOR_NAMES_AND_IDS,
  APIFY_GET_RUN_STATUS_INTERVAL_MS,
  APIFY_GET_RUN_STATUS_MAX_ATTEMPTS,
  APIFY_RESULT_STORE_NAMES,
  Apify<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ck<PERSON>esult,
  INITIAL_RUN_STATUSES,
  TERMINAL_RUN_STATUSES,
  TRANSITIONAL_RUN_STATUSES
} from "nest-modules";
import { TrackingEvent } from "../types/event.types";
import { CarrierTrackingJob, DEFAULT_WORKER_OPTIONS, TrackingQueueName } from "../types/queue.types";
import { waitForTimeout } from "../utils/wait-for-timeout";
import { SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS } from "../types/container.types";
import { ShipmentService } from "@/shipment/services/shipment.service";
import { CARRIER_MAPPINGS, CarrierAbbreviation } from "nest-modules";
import { TradePartnerService } from "@/trade-partner/trade-partner.service";
import { carrierDateStringToDate } from "../utils/carrier-date-string-to-date";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";

@Processor(
  {
    name: TrackingQueueName.SHIPMENT_CARRIER_TRACKING
  },
  DEFAULT_WORKER_OPTIONS
)
export class CarrierTrackingProcessor extends WorkerHost {
  constructor(
    @Inject(forwardRef(() => ShipmentService))
    private readonly shipmentService: ShipmentService,
    @Inject(forwardRef(() => TradePartnerService))
    private readonly tradePartnerService: TradePartnerService,
    @Inject(TrackingHistoryService)
    private readonly trackingHistoryService: TrackingHistoryService,
    @Inject(ApifyService)
    private readonly apifyService: ApifyService,
    @Inject(ShipmentTrackingService)
    private readonly shipmentTrackingService: ShipmentTrackingService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly moduleRef: ModuleRef,
    private readonly eventEmitter: EventEmitter2
  ) {
    super();
  }
  private readonly logger = new Logger(CarrierTrackingProcessor.name);

  /**
   * Get an instance of {@link TradePartnerService} that is scoped to a backoffice admin user.
   * @returns Scoped {@link TradePartnerService} instance.
   */
  private async getScopedTradePartnerService() {
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(
      {
        user: {
          permission: UserPermission.BACKOFFICE_ADMIN
        }
      },
      contextId
    );
    const tradePartnerService = await this.moduleRef.resolve(TradePartnerService, contextId, {
      strict: false
    });
    await new Promise((resolve) => process.nextTick(resolve));

    return tradePartnerService;
  }

  /**
   * Get all trade partners for each carrier listed in {@link CARRIER_MAPPINGS}.
   * @returns Mappings between carrier abbreviation and the respective trade partner object.
   */
  private async getCarrierTradePartners(
    queryRunner: QueryRunner
  ): Promise<Map<CarrierAbbreviation, TradePartner>> {
    const tradePartnerRepository = queryRunner.manager.getRepository(TradePartner);
    const scopedTradePartnerService = await this.getScopedTradePartnerService();

    const carrierTradePartnerMappings = new Map<CarrierAbbreviation, TradePartner>();
    for (const carrierAbbreviation of Object.keys(CARRIER_MAPPINGS) as Array<CarrierAbbreviation>) {
      const carrierMapping = CARRIER_MAPPINGS[carrierAbbreviation];
      this.logger.log(`Searching trade partner for carrier ${carrierAbbreviation}...`);
      let carrierTradePartner = await tradePartnerRepository.findOne({
        where: [
          {
            name: carrierMapping.name,
            organization: { id: IsNull() }
          },
          {
            eManifestCarrierCode: carrierMapping.eManifestCode,
            organization: { id: IsNull() }
          }
        ],
        relations: FIND_TRADE_PARTNER_RELATIONS
      });
      if (!carrierTradePartner) {
        this.logger.log(
          `No existing trade partner found for carrier ${carrierAbbreviation}, creating new...`
        );
        carrierTradePartner = await scopedTradePartnerService.createTradePartner(
          {
            isGlobal: true,
            partnerType: PartnerType.OCEAN_CARRIER,
            name: carrierMapping.name,
            eManifestCarrierCode: carrierMapping.eManifestCode
          },
          queryRunner
        );
      } else if (carrierTradePartner.eManifestCarrierCode !== carrierMapping.eManifestCode) {
        this.logger.log(
          `Existing trade partner for carrier ${carrierAbbreviation} has different eManifest code, updating...`
        );
        carrierTradePartner = await scopedTradePartnerService.editTradePartner(
          carrierTradePartner.id,
          { eManifestCarrierCode: carrierMapping.eManifestCode },
          queryRunner
        );
      }
      this.logger.log(`Carrier: ${carrierAbbreviation}, Trade Partner: ${carrierTradePartner.id}`);

      carrierTradePartnerMappings.set(carrierAbbreviation as CarrierAbbreviation, carrierTradePartner);
    }

    return carrierTradePartnerMappings;
  }

  async process(job: CarrierTrackingJob) {
    const UNKNOWN_CARRIER = "UNKNOWN";
    this.logger.log(`Starting carrier tracking. Job ID: ${job.id}, Data: ${JSON.stringify(job.data)}`);
    const { containerIds } = job.data;

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const shipmentRepository = queryRunner.manager.getRepository(Shipment);
      const containerRepository = queryRunner.manager.getRepository(Container);

      // Get carrier trade partners
      const carrierTradePartnerMappings = await this.getCarrierTradePartners(queryRunner);
      this.logger.log(`Got ${carrierTradePartnerMappings.size} carrier trade partners`);

      // Get containers
      if (containerIds.length <= 0) throw new Error(`No container IDs provided`);
      const containers = await containerRepository.find({
        where: {
          id: In(containerIds)
        },
        relations: SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS
      });
      this.logger.log(`Found containers: ${JSON.stringify(containers.map((c) => c.id))}`);
      if (containers.length <= 0) throw new Error(`No containers found IDs ${JSON.stringify(containerIds)}`);
      const validContainersByCarrier: Record<string, Array<Container>> = {};
      for (const container of containers) {
        const [carrierAbbreviation, carrierMapping] = Object.entries(CARRIER_MAPPINGS).find(
          ([_, mapping]) =>
            mapping.name === container?.shipment?.carrier?.name ||
            mapping.eManifestCode === container?.shipment?.carrier?.eManifestCarrierCode ||
            mapping.prefixes.includes(container?.containerNumber?.slice(0, 4))
        ) ?? [null, null];
        const { name: actorName, id: actorId } = (carrierAbbreviation
          ? APIFY_CONTAINER_STATUS_CHECK_ACTOR_NAMES_AND_IDS[carrierAbbreviation]
          : undefined) ?? { name: null, id: null };
        this.logger.log(
          `Shipment: ${container?.shipment?.id}, Container Number: ${container.containerNumber}, Status: ${container.status}, Mode of Transport: ${container?.shipment?.modeOfTransport}, Shipment Carrier: ${container?.shipment?.carrier?.name}, Carrier Mapping: ${carrierMapping?.name}, Actor Name: ${actorName}, Actor ID: ${actorId}`
        );
        const { isValid, invalidReasons } =
          this.shipmentTrackingService.isValidCarrierTrackingContainer(container);
        if (isValid) {
          this.logger.log(`Container ${container.id} is valid for carrier tracking`);
          if (!Array.isArray(validContainersByCarrier[actorName ? carrierAbbreviation : UNKNOWN_CARRIER]))
            validContainersByCarrier[actorName ? carrierAbbreviation : UNKNOWN_CARRIER] = [];
          validContainersByCarrier[actorName ? carrierAbbreviation : UNKNOWN_CARRIER].push(container);
        } else {
          this.logger.log(
            `Container ${container.id} is invalid for carrier tracking, invalid reasons: ${invalidReasons.join(", ")}`
          );
          const updateResult = await containerRepository.update(container.id, {
            trackingStatus: TrackingStatus.OFFLINE
          });
          this.logger.debug(
            `Updated container ${container.id} of shipment ${container?.shipment?.id} to offline. Update result: ${updateResult.affected}`
          );
          const invalidShipmentHistory = await this.trackingHistoryService.createTrackingHistory(
            {
              timestamp: new Date(),
              result: `Container ${container.containerNumber} is invalid for carrier tracking. Reasons: ${invalidReasons.join(", ")}`,
              shipmentId: container?.shipment?.id,
              party: carrierAbbreviation || UNKNOWN_CARRIER
            },
            queryRunner
          );
          this.logger.debug(`Created invalid shipment history: ${invalidShipmentHistory?.id}`);
        }
      }

      // Get run objects for each carrier
      const carrierAbbreviationsToRun: Array<CarrierAbbreviation> = [];
      const runPromiseList: Array<Promise<ApifyActorRunDto>> = [];
      for (const [carrierAbbreviation, { name: actorName, id: actorId }] of Object.entries(
        APIFY_CONTAINER_STATUS_CHECK_ACTOR_NAMES_AND_IDS
      )) {
        const relatedContainers = (validContainersByCarrier[carrierAbbreviation] || []).concat(
          validContainersByCarrier[UNKNOWN_CARRIER] || []
        );
        this.logger.log(
          `Carrier abbreviation: ${carrierAbbreviation}, Actor name: ${actorName}, Actor ID: ${actorId}, Related containers: ${JSON.stringify(relatedContainers.map((c) => c.id))}`
        );
        if (relatedContainers.length <= 0) continue;

        this.logger.log(`Running actor ${actorName} for carrier ${carrierAbbreviation}...`);
        carrierAbbreviationsToRun.push(carrierAbbreviation as CarrierAbbreviation);
        runPromiseList.push(
          this.apifyService.runActor(
            actorName,
            {
              container_no: relatedContainers.map((c) => c.containerNumber).join(","),
              claro: true,
              record_name: `${carrierAbbreviation.replaceAll(" ", "_").toUpperCase()}_CLARO`
            },
            {
              build: "latest"
            }
          )
        );
      }

      if (runPromiseList.length > 0) {
        this.logger.log(`Waiting for ${runPromiseList.length} runs to finish...`);
        let runObjectList = await Promise.all(runPromiseList);

        // Wait for all runs to finish
        let attempts = 0;
        while (
          attempts < APIFY_GET_RUN_STATUS_MAX_ATTEMPTS &&
          runObjectList.some(
            (r) =>
              INITIAL_RUN_STATUSES.includes(r?.data?.status) ||
              TRANSITIONAL_RUN_STATUSES.includes(r?.data?.status)
          )
        ) {
          this.logger.debug(`Some run statuses are not terminal, wait and get statuses again`);
          attempts++;
          // TODO: Implement a better way to wait for run to finish
          await waitForTimeout(APIFY_GET_RUN_STATUS_INTERVAL_MS);
          runObjectList = await Promise.all(runObjectList.map((r) => this.apifyService.getRun(r?.data?.id)));
          this.logger.debug(
            `-- Attempt ${attempts}, Run statuses: ${JSON.stringify(
              runObjectList.map((r) => ({
                actId: r?.data?.actId,
                id: r?.data?.id,
                status: r?.data?.status
              }))
            )}`
          );
        }
        this.logger.debug(
          `Final run statuses: ${JSON.stringify(
            runObjectList.map((r) => ({ actId: r?.data?.actId, id: r?.data?.id, status: r?.data?.status }))
          )}`
        );
        if (runObjectList.some((r) => !TERMINAL_RUN_STATUSES.includes(r?.data?.status)))
          throw new Error(`Some runs are not in terminal statuses`);
        // if (runObjectList.some((r) => r?.data?.status !== ActorRunStatus.SUCCEEDED))
        //   throw new Error(`Some runs are not in succeeded status`);

        // Get run results from key-value stores
        const completeResultRecords: Array<
          ApifyContainerStatusCheckResult & { carrierAbbreviation: CarrierAbbreviation }
        > = [];
        for (const carrierAbbreviation of carrierAbbreviationsToRun) {
          const { id: actorId } = APIFY_CONTAINER_STATUS_CHECK_ACTOR_NAMES_AND_IDS[carrierAbbreviation];
          const carrierRunObject = runObjectList.find((r) => r?.data?.actId === actorId);
          this.logger.log(
            `Carrier: ${carrierAbbreviation}, Actor ID: ${actorId}, Run status: ${carrierRunObject?.data?.status}`
          );
          if (!carrierRunObject) {
            this.logger.error(
              `No run object found for carrier ${carrierAbbreviation}, skip getting results...`
            );
            continue;
          }
          if (carrierRunObject?.data?.status !== ActorRunStatus.SUCCEEDED) {
            this.logger.error(
              `Run ${carrierRunObject?.data?.id} for carrier ${carrierAbbreviation} is not in succeeded status, skip getting results...`
            );
            continue;
          }

          this.logger.log(`Getting ${carrierAbbreviation} results from key-value store...`);
          const resultRecord = (await this.apifyService.getKeyValueStoreRecord(
            APIFY_RESULT_STORE_NAMES.CARRIER,
            `${carrierAbbreviation.replaceAll(" ", "_").toUpperCase()}_CLARO`
          )) as Array<ApifyContainerStatusCheckResult>;
          if (!Array.isArray(resultRecord)) {
            this.logger.error(
              `No result record found for key ${carrierAbbreviation.replaceAll(" ", "_").toUpperCase()}_CLARO`
            );
            continue;
          } else {
            completeResultRecords.push(...resultRecord.map((rr) => ({ ...rr, carrierAbbreviation })));
            this.logger.log(`Got ${resultRecord.length} results for carrier ${carrierAbbreviation}`);
          }
        }

        const toBeRecalculatedShipments: Array<Shipment> = [];
        // Update container and tracking history with results
        const runsStartedAt = Object.values(runObjectList).reduce(
          (startedAt, ro) =>
            typeof ro.data?.startedAt === "string" &&
            ro.data?.startedAt?.length > 0 &&
            ro.data?.startedAt <= startedAt
              ? ro.data?.startedAt
              : startedAt,
          new Date().toISOString()
        );
        const validContainers = Object.values(validContainersByCarrier).flat();
        for (const container of validContainers) {
          const containerResult = completeResultRecords.find(
            (r) => r.container === container.containerNumber
          );
          this.logger.debug(
            `-- Shipment: ${container?.shipment?.id}, Container: ${container?.id}, Result: ${JSON.stringify(containerResult)}`
          );
          const shipmentUpdate: Partial<Shipment> = {};
          const containerUpdate: Partial<Container> = {};
          if (containerResult) {
            containerUpdate.trackingStatus = TrackingStatus.ONLINE;
            containerUpdate.status = containerResult.isDischarged
              ? ShipmentStatus.AT_PORT
              : ShipmentStatus.IN_TRANSIT;
            // shipmentUpdate.carrierCode = containerResult.carrierCode;
            shipmentUpdate.carrier =
              carrierTradePartnerMappings.get(containerResult.carrierAbbreviation) ?? null;
            if (!containerResult.isDischarged && containerResult.eta) {
              shipmentUpdate.etaPort = carrierDateStringToDate(
                containerResult.carrierAbbreviation,
                containerResult.eta?.trim(),
                CarrierTrackingProcessor.name
              );
              shipmentUpdate.etaPortString = containerResult.eta.trim();
            }
          } else {
            containerUpdate.trackingStatus = TrackingStatus.OFFLINE;
            containerUpdate.status = ShipmentStatus.NEW;
          }
          this.logger.log(
            `Updating container ${container.id} with object: ${JSON.stringify(containerUpdate)}`
          );
          const containerUpdateResult = await containerRepository.update(container.id, containerUpdate);
          this.logger.debug(`Container update result: ${containerUpdateResult.affected}`);
          if (Object.keys(shipmentUpdate).length > 0) {
            this.logger.log(
              `Updating shipment ${container?.shipment?.id} with object: ${JSON.stringify(shipmentUpdate)}`
            );
            const shipmentUpdateResult = await shipmentRepository.update(
              container?.shipment?.id,
              shipmentUpdate
            );
            this.logger.debug(`Shipment update result: ${shipmentUpdateResult.affected}`);
          }
          this.logger.log(`Creating tracking history for shipment ${container?.shipment?.id}...`);
          const trackingHistory = await this.trackingHistoryService.createTrackingHistory(
            {
              timestamp: new Date(runsStartedAt),
              result:
                containerResult?.mark ||
                `No result returned by carrier tracking actor for container ${container?.containerNumber}`,
              shipmentId: container?.shipment?.id,
              party: containerResult?.carrierAbbreviation || UNKNOWN_CARRIER
            },
            queryRunner
          );
          this.logger.debug(
            `Created tracking history for container ${container?.id} of shipment ${container?.shipment?.id}: ${trackingHistory?.id}`
          );
          // Adding shipemnt to be recalculated if the container status is updated
          if (
            container?.shipment &&
            ![null, undefined].includes(containerUpdate.status) &&
            !toBeRecalculatedShipments.some((s) => s.id === container.shipment.id)
          ) {
            this.logger.log(`Adding shipment ${container?.shipment.id} to be recalculated...`);
            toBeRecalculatedShipments.push(container.shipment);
          }
        }

        if (toBeRecalculatedShipments.length > 0) {
          this.logger.log(
            `Recalculating shipment statuses for ${toBeRecalculatedShipments.length} shipments...`
          );
          await this.shipmentService.recalculateShipmentDatesAndStatus(
            toBeRecalculatedShipments,
            queryRunner
          );
        }
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      const errorMessage = `Error in carrier tracking processor: ${error instanceof Error ? error.message : String(error)}. Container IDs: ${containerIds.join(",")}`;
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(errorMessage, errorStack);
      this.eventEmitter.emit(TrackingEvent.CARRIER_TRACKING_ERROR, job, error);
    } finally {
      await queryRunner.release();
    }
  }
}
