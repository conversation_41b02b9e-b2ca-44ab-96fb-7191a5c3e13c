import { Processor, WorkerHost } from "@nestjs/bullmq";
import { forwardRef, Inject, Logger } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectDataSource } from "@nestjs/typeorm";
import moment from "moment-timezone";
import {
  Container,
  Country,
  FIND_CONTAINER_RELATIONS,
  FIND_SHIPMENT_RELATIONS,
  PartnerType,
  Shipment,
  ShipmentStatus,
  TrackingHistoryService,
  TrackingStatus,
  TradePartner
} from "nest-modules";
import { DataSource, In, IsNull, QueryRunner } from "typeorm";
import { ApifyService } from "nest-modules";
import { ShipmentTrackingService } from "../services/shipment-tracking.service";
import {
  ActorRunStatus,
  APIFY_GET_RUN_STATUS_INTERVAL_MS,
  APIFY_GET_RUN_STATUS_MAX_ATTEMPTS,
  APIFY_PORT_ACTOR_NAMES_AND_IDS,
  APIFY_RESULT_STORE_NAMES,
  ApifyPortCheckResult,
  INITIAL_RUN_STATUSES,
  TERMINAL_RUN_STATUSES,
  TRANSITIONAL_RUN_STATUSES
} from "nest-modules";
import { TrackingEvent } from "../types/event.types";
import { DEFAULT_WORKER_OPTIONS, PortTrackingJob, TrackingQueueName } from "../types/queue.types";
import { waitForTimeout } from "../utils/wait-for-timeout";
import { ShipmentService } from "@/shipment/services/shipment.service";

@Processor(
  {
    name: TrackingQueueName.SHIPMENT_PORT_TRACKING
  },
  DEFAULT_WORKER_OPTIONS
)
export class PortTrackingProcessor extends WorkerHost {
  constructor(
    @Inject(forwardRef(() => ShipmentService))
    private readonly shipmentService: ShipmentService,
    @Inject(TrackingHistoryService)
    private readonly trackingHistoryService: TrackingHistoryService,
    @Inject(ApifyService)
    private readonly apifyService: ApifyService,
    @Inject(ShipmentTrackingService)
    private readonly shipmentTrackingService: ShipmentTrackingService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly eventEmitter: EventEmitter2
  ) {
    super();
  }
  private readonly logger = new Logger(PortTrackingProcessor.name);

  private async getPortTradePartner(portName: string, queryRunner: QueryRunner) {
    const tradePartnerRepository = queryRunner.manager.getRepository(TradePartner);
    const countryRepository = queryRunner.manager.getRepository(Country);
    let portTradePartner = await tradePartnerRepository.findOne({
      where: { name: portName, organization: { id: IsNull() } }
    });
    if (!portTradePartner) {
      portTradePartner = new TradePartner();
      portTradePartner.name = portName[0].toUpperCase() + portName.slice(1).toLowerCase();
      portTradePartner.partnerType = PartnerType.OCEAN_CARRIER;
      portTradePartner.country = await countryRepository.findOneBy({ alpha2: "CA" });
      portTradePartner = await tradePartnerRepository.save(portTradePartner);
    }
    return portTradePartner;
  }

  async process(job: PortTrackingJob) {
    this.logger.log(`Starting port tracking. Job ID: ${job.id}, Data: ${JSON.stringify(job.data)}`);

    const { containerIds } = job.data;

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const shipmentRepository = queryRunner.manager.getRepository(Shipment);
      const containerRepository = queryRunner.manager.getRepository(Container);

      // Get containers from DB
      const containers = await containerRepository.find({
        where: { id: In(containerIds) },
        relations: {
          ...FIND_CONTAINER_RELATIONS,
          shipment: {
            placeOfDelivery: true
          }
        }
      });
      this.logger.log(`Found containers: ${JSON.stringify(containers.map((c) => c.id))}`);
      if (containers.length <= 0) throw new Error(`No containers found IDs ${JSON.stringify(containerIds)}`);

      // Filter valid containers
      const validContainers: Array<Container> = [];
      for (const container of containers) {
        this.logger.log(
          `Shipment: ${container?.shipment?.id}, Container Number: ${container.containerNumber}, Status: ${container.status}, Mode of Transport: ${container?.shipment?.modeOfTransport}, PODelivery: ${container?.shipment?.placeOfDelivery?.name}`
        );
        const { isValid, invalidReasons } =
          this.shipmentTrackingService.isValidPortTrackingContainer(container);

        if (isValid) {
          this.logger.log(
            `Container ${container.id} of shipment ${container?.shipment?.id} is valid for port tracking`
          );
          validContainers.push(container);
        } else {
          this.logger.log(
            `Container ${container.id} of shipment ${container?.shipment?.id} is invalid for port tracking, invalid reasons: ${invalidReasons.join(", ")}`
          );
          const updateResult = await containerRepository.update(container.id, {
            trackingStatus: TrackingStatus.OFFLINE
          });
          this.logger.debug(
            `Updated container ${container.id} of shipment ${container?.shipment?.id} to offline. Update result: ${updateResult.affected}`
          );
          const invalidShipmentHistory = await this.trackingHistoryService.createTrackingHistory(
            {
              timestamp: new Date(),
              result: `Container ${container.containerNumber} is invalid for port tracking. Reasons: ${invalidReasons.join(", ")}`,
              shipmentId: container?.shipment?.id,
              party: "UNKNOWN"
            },
            queryRunner
          );
          this.logger.debug(`Created invalid shipment history: ${invalidShipmentHistory?.id}`);
        }
      }
      this.logger.log(`Valid containers: ${JSON.stringify(validContainers.map((c) => c.id))}`);

      const toBeRecalculatedShipments: Array<Shipment> = [];
      if (validContainers.length > 0) {
        // Get port tracking actor names
        const portTrackingActorNamesAndIds = Object.values(APIFY_PORT_ACTOR_NAMES_AND_IDS);

        // Run port tracking actors
        let runObjectList = await Promise.all(
          portTrackingActorNamesAndIds.map(({ name: actorName }) =>
            this.apifyService.runActor(
              actorName,
              {
                container_no: validContainers.map((c) => c.containerNumber).join(","),
                claro: true
              },
              {
                build: "latest"
              }
            )
          )
        );
        this.logger.debug(
          `Started runs: ${JSON.stringify(runObjectList.map((r) => ({ actId: r?.data?.actId, id: r?.data?.id, status: r?.data?.status })))}`
        );
        if (runObjectList.some((r) => !r?.data?.id)) throw new Error(`Some runs have no ID returned`);

        // Wait for all runs to finish
        let attempts = 0;
        while (
          attempts < APIFY_GET_RUN_STATUS_MAX_ATTEMPTS &&
          runObjectList.some(
            (r) =>
              INITIAL_RUN_STATUSES.includes(r?.data?.status) ||
              TRANSITIONAL_RUN_STATUSES.includes(r?.data?.status)
          )
        ) {
          this.logger.debug(`Some run statuses are not terminal, wait and get statuses again`);
          attempts++;
          // TODO: Implement a better way to wait for run to finish
          await waitForTimeout(APIFY_GET_RUN_STATUS_INTERVAL_MS);
          runObjectList = await Promise.all(runObjectList.map((r) => this.apifyService.getRun(r?.data?.id)));
          this.logger.debug(
            `-- Attempt ${attempts}, Run statuses: ${JSON.stringify(
              runObjectList.map((r) => ({
                actId: r?.data?.actId,
                id: r?.data?.id,
                status: r?.data?.status
              }))
            )}`
          );
        }
        this.logger.debug(
          `Final run statuses: ${JSON.stringify(
            runObjectList.map((r) => ({ actId: r?.data?.actId, id: r?.data?.id, status: r?.data?.status }))
          )}`
        );
        if (runObjectList.some((r) => !TERMINAL_RUN_STATUSES.includes(r?.data?.status)))
          throw new Error(`Some runs are not in terminal statuses`);
        // if (runObjectList.some((r) => r?.data?.status !== ActorRunStatus.SUCCEEDED))
        //   throw new Error(`Some runs are not in succeeded status`);

        // Get run results from key-value stores and get port trade partners
        const portTradePartners: Record<string, TradePartner> = {};
        const completeResultRecords: Array<ApifyPortCheckResult & { portName: string }> = [];
        for (const portName of Object.keys(APIFY_PORT_ACTOR_NAMES_AND_IDS)) {
          const { id: actorId } = APIFY_PORT_ACTOR_NAMES_AND_IDS[portName];
          const portRunObject = runObjectList.find((r) => r?.data?.actId === actorId);
          this.logger.log(
            `Port: ${portName}, Actor ID: ${actorId}, Run status: ${portRunObject?.data?.status}`
          );
          if (!portRunObject) {
            this.logger.error(`No run object found for port ${portName}, skip getting results...`);
            continue;
          }
          if (portRunObject?.data?.status !== ActorRunStatus.SUCCEEDED) {
            this.logger.error(
              `Run ${portRunObject?.data?.id} for port ${portName} is not in succeeded status, skip getting results...`
            );
            continue;
          }

          this.logger.log(`Getting ${portName} results from key-value store...`);
          const resultRecord = (await this.apifyService.getKeyValueStoreRecord(
            APIFY_RESULT_STORE_NAMES.PORT,
            `${portName}_CLARO`
          )) as Array<ApifyPortCheckResult>;
          if (!Array.isArray(resultRecord))
            throw new Error(`No result record found for key ${portName}_CLARO`);
          completeResultRecords.push(...resultRecord.map((rr) => ({ ...rr, portName })));
          portTradePartners[portName] = await this.getPortTradePartner(portName.trim(), queryRunner);
        }

        // Update container and tracking history with results
        for (const container of validContainers) {
          const containerResult = completeResultRecords.find(
            (r) => r.containerNumber === container.containerNumber
          );
          this.logger.debug(
            `-- Shipment: ${container?.shipment.id}, Container: ${container?.id}, Result: ${JSON.stringify(containerResult)}`
          );
          const containerUpdate: Partial<Container> = {};
          const shipmentUpdate: Partial<Shipment> = {};
          if (containerResult) {
            containerUpdate.status = ShipmentStatus.AT_PORT;
            containerUpdate.trackingStatus = TrackingStatus.ONLINE;
            if (portTradePartners[containerResult.portName])
              shipmentUpdate.pickupLocation = portTradePartners[containerResult.portName];
            if (containerResult.lastFreeDay) {
              containerUpdate.pickupLfdString = containerResult.lastFreeDay.trim();
              switch (containerResult.portName) {
                case "CENTERM":
                  containerUpdate.pickupLfd = moment
                    .tz(containerResult.lastFreeDay, "DD/MM/YYYY HH:mm", "America/Vancouver")
                    .toDate();
                  break;
                case "GCT":
                  containerUpdate.pickupLfd = moment
                    .tz(containerResult.lastFreeDay, "DD-MMM-YYYY", "America/Vancouver")
                    .toDate();
                  break;
                default:
                  containerUpdate.pickupLfd = moment
                    .tz(containerResult.lastFreeDay, "America/Vancouver")
                    .toDate();
                  break;
              }

              // Only adding shipment to be recalculated if pickup LFD is updated
              // if (!toBeRecalculatedShipments.some((s) => s.id === container?.shipment?.id)) {
              //   this.logger.log(`Adding shipment ${container?.shipment?.id} to be recalculated...`);
              //   toBeRecalculatedShipments.push(container?.shipment);
              // }
            }
          } else {
            containerUpdate.trackingStatus = TrackingStatus.OFFLINE;
          }
          this.logger.log(
            `Updating container ${container.id} of shipment ${container?.shipment?.id} with object: ${JSON.stringify(containerUpdate)}`
          );
          const containerUpdateResult = await containerRepository.update(container.id, containerUpdate);
          this.logger.debug(`Container update result: ${containerUpdateResult.affected}`);
          if (Object.keys(shipmentUpdate).length > 0) {
            this.logger.log(
              `Updating shipment ${container?.shipment?.id} with object: ${JSON.stringify(shipmentUpdate)}`
            );
            const shipmentUpdateResult = await shipmentRepository.update(
              container?.shipment?.id,
              shipmentUpdate
            );
            this.logger.debug(`Shipment update result: ${shipmentUpdateResult.affected}`);
          }
          this.logger.log(`Creating tracking history for container ${container.id}...`);
          const trackingHistory = await this.trackingHistoryService.createTrackingHistory(
            {
              timestamp: new Date(runObjectList[0]?.data?.startedAt),
              result:
                containerResult?.mark ||
                `No result returned from port tracking for container ${container?.containerNumber}`,
              shipmentId: container?.shipment?.id,
              party: containerResult?.portName
            },
            queryRunner
          );
          this.logger.debug(
            `Created tracking history for container ${container.id} of shipment ${container?.shipment?.id}: ${trackingHistory?.id}`
          );

          // Adding shipment to be recalculated if pickup LFD or container status is updated
          if (
            container?.shipment &&
            (![null, undefined].includes(containerUpdate.status) ||
              containerUpdate.pickupLfd instanceof Date) &&
            !toBeRecalculatedShipments.some((s) => s.id === container.shipment.id)
          ) {
            this.logger.log(`Adding shipment ${container.shipment.id} to be recalculated...`);
            toBeRecalculatedShipments.push(container.shipment);
          }
        }
      }

      if (toBeRecalculatedShipments.length > 0) {
        this.logger.log(
          `Recalculating shipment dates and statuses for ${toBeRecalculatedShipments.length} shipments...`
        );
        await this.shipmentService.recalculateShipmentDatesAndStatus(toBeRecalculatedShipments, queryRunner);
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      const errorMessage = `Error in port tracking processor: ${error instanceof Error ? error.message : String(error)}. Container IDs: ${containerIds.join(",")}`;
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(errorMessage, errorStack);
      this.eventEmitter.emit(TrackingEvent.PORT_TRACKING_ERROR, job, error);
    } finally {
      await queryRunner.release();
    }
  }
}
