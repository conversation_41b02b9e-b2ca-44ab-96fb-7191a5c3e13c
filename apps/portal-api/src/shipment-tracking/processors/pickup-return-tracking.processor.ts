import { ShipmentService } from "@/shipment/services/shipment.service";
import { Processor, WorkerHost } from "@nestjs/bullmq";
import { forwardRef, Inject, Logger } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectDataSource } from "@nestjs/typeorm";
import moment from "moment-timezone";
import {
  Container,
  FIND_SHIPMENT_RELATIONS,
  FIND_TRADE_PARTNER_RELATIONS,
  PartnerType,
  Shipment,
  ShipmentStatus,
  TrackingHistoryService,
  TrackingStatus,
  TradePartner,
  UserPermission
} from "nest-modules";
import { DataSource, In, IsNull, QueryRunner } from "typeorm";
import { ApifyActorRunDto, ApifyService } from "nest-modules";
import { ShipmentTrackingService } from "../services/shipment-tracking.service";
import {
  ActorRunStatus,
  APIFY_GET_RUN_STATUS_INTERVAL_MS,
  APIFY_GET_RUN_STATUS_MAX_ATTEMPTS,
  APIFY_PICKUP_RETURN_ACTOR_NAMES_AND_IDS,
  APIFY_RESULT_STORE_NAMES,
  ApifyPickupReturnCheckResult,
  INITIAL_RUN_STATUSES,
  TERMINAL_RUN_STATUSES,
  TRANSITIONAL_RUN_STATUSES
} from "nest-modules";
import { TrackingEvent } from "../types/event.types";
import { DEFAULT_WORKER_OPTIONS, PickupReturnTrackingJob, TrackingQueueName } from "../types/queue.types";
import { waitForTimeout } from "../utils/wait-for-timeout";
import { CARRIER_MAPPINGS, CarrierAbbreviation } from "nest-modules";
import { carrierDateStringToDate } from "../utils/carrier-date-string-to-date";
import { TradePartnerService } from "@/trade-partner/trade-partner.service";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";

@Processor(
  {
    name: TrackingQueueName.SHIPMENT_PICKUP_RETURN_TRACKING
  },
  DEFAULT_WORKER_OPTIONS
)
export class PickupReturnTrackingProcessor extends WorkerHost {
  constructor(
    @Inject(forwardRef(() => ShipmentService))
    private readonly shipmentService: ShipmentService,
    @Inject(forwardRef(() => TradePartnerService))
    private readonly tradePartnerService: TradePartnerService,
    @Inject(TrackingHistoryService)
    private readonly trackingHistoryService: TrackingHistoryService,
    @Inject(ApifyService)
    private readonly apifyService: ApifyService,
    @Inject(ShipmentTrackingService)
    private readonly shipmentTrackingService: ShipmentTrackingService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly moduleRef: ModuleRef,
    private readonly eventEmitter: EventEmitter2
  ) {
    super();
  }
  private readonly logger = new Logger(PickupReturnTrackingProcessor.name);

  /**
   * Add working days to a date
   * @param date Date to add days to
   * @param days Number of working days to add
   * @returns Date after adding days
   */
  private addWorkingDays(date: Date, days: number) {
    const momentDate = moment.tz(date, "America/Toronto");
    let daysLeftToAdd = days;
    while (daysLeftToAdd > 0) {
      // If current date is a weekend, do not reduce days left to add
      if (![6, 0].includes(momentDate.day())) daysLeftToAdd--;
      momentDate.add(1, "day");
    }
    return momentDate.toDate();
  }

  /**
   * Calculate return last free day based on carrier
   * @param carrierAbbreviation Carrier abbreviation
   * @param container Container
   * @param shipment Shipment
   * @param pickupDate Pickup date
   * @returns Return LFD
   */
  private calculateReturnLfd(
    carrierAbbreviation: CarrierAbbreviation,
    container: Container,
    shipment: Shipment,
    pickupDate: Date
  ) {
    let returnStartDate: Date | null = null,
      calendarDaysToAdd = 0,
      workingDaysToAdd = 0;
    switch (carrierAbbreviation) {
      case CarrierAbbreviation.COSCO:
        returnStartDate = container.etaDestination || shipment.etaPort || null;
        workingDaysToAdd = 5;
        break;
      case CarrierAbbreviation.EGL:
        returnStartDate = container.etaDestination || shipment.etaPort || null;
        workingDaysToAdd = 4;
        break;
      case CarrierAbbreviation.ONE:
        returnStartDate = pickupDate || null;
        calendarDaysToAdd = 4;
        break;
      case CarrierAbbreviation.OOCL:
        returnStartDate = pickupDate || null;
        calendarDaysToAdd = 4;
        break;
      case CarrierAbbreviation.SML:
        returnStartDate = pickupDate || null;
        workingDaysToAdd = 5;
        break;
      case CarrierAbbreviation.CMA:
        returnStartDate = pickupDate || null;
        workingDaysToAdd = 3;
        break;
      case CarrierAbbreviation.HMM:
        returnStartDate = container.etaDestination || shipment.etaPort || null;
        workingDaysToAdd = 2;
        break;
      case CarrierAbbreviation.YML:
        returnStartDate = container.etaDestination || shipment.etaPort || null;
        workingDaysToAdd = 2;
        break;
      case CarrierAbbreviation.MSC:
        if (container.etaDestination) {
          // 5 calendar days for inland
          returnStartDate =
            (pickupDate < container.etaDestination ? pickupDate : container.etaDestination) || null;
          calendarDaysToAdd = 5;
        } else if (shipment.etaPort) {
          // 7 calendar days for port
          returnStartDate = (pickupDate < shipment.etaPort ? pickupDate : shipment.etaPort) || null;
          calendarDaysToAdd = 7;
        }
        break;
      case CarrierAbbreviation.ZIM:
        returnStartDate = pickupDate || null;
        calendarDaysToAdd = 3;
        break;
      default:
        this.logger.warn(`No Return LFD calculations for carrier abbreviation ${carrierAbbreviation}`);
        returnStartDate = null;
        break;
    }

    return returnStartDate
      ? this.addWorkingDays(
          moment.tz(returnStartDate, "America/Toronto").add(calendarDaysToAdd, "days").toDate(),
          workingDaysToAdd
        )
      : null;
  }

  /**
   * Get an instance of {@link TradePartnerService} that is scoped to a backoffice admin user.
   * @returns Scoped {@link TradePartnerService} instance.
   */
  private async getScopedTradePartnerService() {
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(
      {
        user: {
          permission: UserPermission.BACKOFFICE_ADMIN
        }
      },
      contextId
    );
    const tradePartnerService = await this.moduleRef.resolve(TradePartnerService, contextId, {
      strict: false
    });
    await new Promise((resolve) => process.nextTick(resolve));

    return tradePartnerService;
  }

  /**
   * Get all trade partners for each carrier listed in {@link CARRIER_MAPPINGS}.
   * @returns Mappings between carrier abbreviation and the respective trade partner object.
   */
  private async getCarrierTradePartners(
    queryRunner: QueryRunner
  ): Promise<Map<CarrierAbbreviation, TradePartner>> {
    const tradePartnerRepository = queryRunner.manager.getRepository(TradePartner);
    const scopedTradePartnerService = await this.getScopedTradePartnerService();

    const carrierTradePartnerMappings = new Map<CarrierAbbreviation, TradePartner>();
    for (const carrierAbbreviation of Object.keys(CARRIER_MAPPINGS) as Array<CarrierAbbreviation>) {
      const carrierMapping = CARRIER_MAPPINGS[carrierAbbreviation];
      this.logger.log(`Searching trade partner for carrier ${carrierAbbreviation}...`);
      let carrierTradePartner = await tradePartnerRepository.findOne({
        where: [
          {
            name: carrierMapping.name,
            organization: { id: IsNull() }
          },
          {
            eManifestCarrierCode: carrierMapping.eManifestCode,
            organization: { id: IsNull() }
          }
        ],
        relations: FIND_TRADE_PARTNER_RELATIONS
      });
      if (!carrierTradePartner) {
        this.logger.log(
          `No existing trade partner found for carrier ${carrierAbbreviation}, creating new...`
        );
        carrierTradePartner = await scopedTradePartnerService.createTradePartner(
          {
            isGlobal: true,
            partnerType: PartnerType.OCEAN_CARRIER,
            name: carrierMapping.name,
            eManifestCarrierCode: carrierMapping.eManifestCode
          },
          queryRunner
        );
      } else if (carrierTradePartner.eManifestCarrierCode !== carrierMapping.eManifestCode) {
        this.logger.log(
          `Existing trade partner for carrier ${carrierAbbreviation} has different eManifest code, updating...`
        );
        carrierTradePartner = await scopedTradePartnerService.editTradePartner(
          carrierTradePartner.id,
          { eManifestCarrierCode: carrierMapping.eManifestCode },
          queryRunner
        );
      }
      this.logger.log(`Carrier: ${carrierAbbreviation}, Trade Partner: ${carrierTradePartner.id}`);

      carrierTradePartnerMappings.set(carrierAbbreviation as CarrierAbbreviation, carrierTradePartner);
    }

    return carrierTradePartnerMappings;
  }

  async process(job: PickupReturnTrackingJob) {
    this.logger.log(`Starting pickup return tracking. Job ID: ${job.id}, Data: ${JSON.stringify(job.data)}`);

    const { containerIds } = job.data;
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const UNKNOWN_CARRIER = "UNKNOWN";
      const shipmentRepository = queryRunner.manager.getRepository(Shipment);
      const containerRepository = queryRunner.manager.getRepository(Container);

      // Get carrier trade partners
      const carrierTradePartnerMappings = await this.getCarrierTradePartners(queryRunner);
      this.logger.log(`Got ${carrierTradePartnerMappings.size} carrier trade partners`);

      // Get containers
      const containers = await containerRepository.find({
        where: {
          id: In(containerIds)
        },
        relations: FIND_SHIPMENT_RELATIONS
      });
      this.logger.log(`Found containers: ${JSON.stringify(containers.map((c) => c.id))}`);
      if (containers.length <= 0) throw new Error(`No containers found IDs ${JSON.stringify(containerIds)}`);

      // Filter valid shipments
      const validContainersByCarrier: Record<string, Array<Container>> = {};
      for (const container of containers) {
        const [carrierAbbreviation, carrierMapping] = Object.entries(CARRIER_MAPPINGS).find(
          ([_, mapping]) =>
            mapping.name === container?.shipment?.carrier?.name ||
            mapping.eManifestCode === container?.shipment?.carrier?.eManifestCarrierCode ||
            mapping.prefixes.includes(container?.containerNumber?.slice(0, 4))
        ) ?? [null, null];
        const { name: actorName, id: actorId } = (carrierAbbreviation
          ? APIFY_PICKUP_RETURN_ACTOR_NAMES_AND_IDS[carrierAbbreviation]
          : undefined) ?? { name: null, id: null };
        this.logger.log(
          `Shipment: ${container?.shipment?.id}, Container: ${container.id}, Container Number: ${container.containerNumber}, Status: ${container.status}, Mode of Transport: ${container?.shipment?.modeOfTransport}, PODelivery: ${container?.shipment?.placeOfDelivery?.name}, Pickup LFD: ${container.pickupLfd}, Shipment Carrier: ${container?.shipment?.carrier?.name}, Carrier Mapping: ${carrierMapping?.name}, Actor Name: ${actorName}, Actor ID: ${actorId}`
        );

        const { isValid, invalidReasons } =
          this.shipmentTrackingService.isValidPickupReturnTrackingContainer(container);
        if (isValid) {
          this.logger.log(
            `Container ${container.id} of shipment ${container?.shipment?.id} is valid for pickup return tracking`
          );
          if (!Array.isArray(validContainersByCarrier[actorName ? carrierAbbreviation : UNKNOWN_CARRIER]))
            validContainersByCarrier[actorName ? carrierAbbreviation : UNKNOWN_CARRIER] = [];
          validContainersByCarrier[actorName ? carrierAbbreviation : UNKNOWN_CARRIER].push(container);
        } else {
          this.logger.log(
            `Container ${container.id} of shipment ${container?.shipment?.id} is invalid for pickup return tracking, invalid reasons: ${invalidReasons.join(", ")}`
          );
          const updateResult = await containerRepository.update(container.id, {
            trackingStatus: TrackingStatus.OFFLINE
          });
          this.logger.debug(
            `Updated container ${container.id} of shipment ${container?.shipment?.id} to offline. Update result: ${updateResult.affected}`
          );
          const invalidContainerHistory = await this.trackingHistoryService.createTrackingHistory({
            timestamp: new Date(),
            result: `Container ${container.containerNumber} is invalid for pickup return tracking. Reasons: ${invalidReasons.join(", ")}`,
            shipmentId: container?.shipment?.id,
            party: carrierAbbreviation || UNKNOWN_CARRIER
          });
          this.logger.debug(`Created invalid container history: ${invalidContainerHistory?.id}`);
        }
      }

      // Get run objects for each carrier
      const carrierAbbreviationsToRun: Array<CarrierAbbreviation> = [];
      const runPromiseList: Array<Promise<ApifyActorRunDto>> = [];
      for (const [carrierAbbreviation, { name: actorName, id: actorId }] of Object.entries(
        APIFY_PICKUP_RETURN_ACTOR_NAMES_AND_IDS
      )) {
        const relatedContainers = (validContainersByCarrier[carrierAbbreviation] || []).concat(
          validContainersByCarrier[UNKNOWN_CARRIER] || []
        );
        this.logger.log(
          `Carrier abbreviation: ${carrierAbbreviation}, Actor name: ${actorName}, Actor ID: ${actorId}, Related containers: ${JSON.stringify(relatedContainers.map((c) => c.id))}`
        );
        if (relatedContainers.length <= 0) continue;

        this.logger.log(`Running actor ${actorName} for carrier ${carrierAbbreviation}...`);
        carrierAbbreviationsToRun.push(carrierAbbreviation as CarrierAbbreviation);
        runPromiseList.push(
          this.apifyService.runActor(
            actorName,
            {
              container_no: relatedContainers.map((c) => c.containerNumber).join(","),
              claro: true,
              record_name: `${carrierAbbreviation.replaceAll(" ", "_").toUpperCase()}_CLARO`
            },
            {
              build: "latest"
            }
          )
        );
      }

      const toBeRecalculatedShipments: Array<Shipment> = [];
      if (runPromiseList.length > 0) {
        this.logger.log(`Waiting for ${runPromiseList.length} runs to finish...`);
        let runObjectList = await Promise.all(runPromiseList);

        // Wait for all runs to finish
        let attempts = 0;
        while (
          attempts < APIFY_GET_RUN_STATUS_MAX_ATTEMPTS &&
          runObjectList.some(
            (r) =>
              INITIAL_RUN_STATUSES.includes(r?.data?.status) ||
              TRANSITIONAL_RUN_STATUSES.includes(r?.data?.status)
          )
        ) {
          this.logger.debug(`Some run statuses are not terminal, wait and get statuses again`);
          attempts++;
          // TODO: Implement a better way to wait for run to finish
          await waitForTimeout(APIFY_GET_RUN_STATUS_INTERVAL_MS);
          runObjectList = await Promise.all(runObjectList.map((r) => this.apifyService.getRun(r?.data?.id)));
          this.logger.debug(
            `-- Attempt ${attempts}, Run statuses: ${JSON.stringify(
              runObjectList.map((r) => ({ actId: r?.data?.actId, id: r?.data?.id, status: r?.data?.status }))
            )}`
          );
        }
        this.logger.debug(
          `Final run statuses: ${JSON.stringify(
            runObjectList.map((r) => ({ actId: r?.data?.actId, id: r?.data?.id, status: r?.data?.status }))
          )}`
        );
        if (runObjectList.some((r) => !TERMINAL_RUN_STATUSES.includes(r?.data?.status)))
          throw new Error(`Some runs are not in terminal statuses`);
        // if (runObjectList.some((r) => r?.data?.status !== ActorRunStatus.SUCCEEDED))
        //   throw new Error(`Some runs are not in succeeded status`);

        // Get run results from key-value stores
        const completeResultRecords: Array<
          ApifyPickupReturnCheckResult & { carrierAbbreviation: CarrierAbbreviation }
        > = [];
        for (const carrierAbbreviation of carrierAbbreviationsToRun) {
          const { id: actorId } = APIFY_PICKUP_RETURN_ACTOR_NAMES_AND_IDS[carrierAbbreviation];
          const carrierRunObject = runObjectList.find((r) => r?.data?.actId === actorId);
          this.logger.log(
            `Carrier: ${carrierAbbreviation}, Actor ID: ${actorId}, Run status: ${carrierRunObject?.data?.status}`
          );
          if (!carrierRunObject) {
            this.logger.error(
              `No run object found for carrier ${carrierAbbreviation}, skip getting results...`
            );
            continue;
          }
          if (carrierRunObject?.data?.status !== ActorRunStatus.SUCCEEDED) {
            this.logger.error(
              `Run ${carrierRunObject?.data?.id} for carrier ${carrierAbbreviation} is not in succeeded status, skip getting results...`
            );
            continue;
          }

          this.logger.log(`Getting ${carrierAbbreviation} results from key-value store...`);
          const resultRecord = (await this.apifyService.getKeyValueStoreRecord(
            APIFY_RESULT_STORE_NAMES.PICKUP_RETURN,
            `${carrierAbbreviation.replaceAll(" ", "_").toUpperCase()}_CLARO`
          )) as Array<ApifyPickupReturnCheckResult>;
          if (!Array.isArray(resultRecord)) {
            this.logger.error(
              `No result record found for key ${carrierAbbreviation.replaceAll(" ", "_").toUpperCase()}_CLARO`
            );
            continue;
          } else {
            completeResultRecords.push(...resultRecord.map((rr) => ({ ...rr, carrierAbbreviation })));
            this.logger.log(`Got ${resultRecord.length} results for carrier ${carrierAbbreviation}`);
          }
        }

        // Update shipment and tracking history with results
        const runsStartedAt = Object.values(runObjectList).reduce(
          (startedAt, ro) =>
            typeof ro.data?.startedAt === "string" &&
            ro.data?.startedAt?.length > 0 &&
            ro.data?.startedAt <= startedAt
              ? ro.data?.startedAt
              : startedAt,
          new Date().toISOString()
        );
        const validContainers = Object.values(validContainersByCarrier).flat();
        for (const container of validContainers) {
          const containerResult = completeResultRecords.find(
            (r) => r.container === container.containerNumber
          );
          this.logger.debug(
            `-- Container: ${container.id}, Shipment: ${container?.shipment?.id}, Result: ${JSON.stringify(containerResult)}`
          );
          const shipmentUpdate: Partial<Shipment> = {};
          const containerUpdate: Partial<Container> = {};
          if (containerResult) {
            containerUpdate.trackingStatus = TrackingStatus.ONLINE;
            containerUpdate.status = containerResult.returnTime
              ? ShipmentStatus.COMPLETED
              : containerResult.pickupTime
                ? ShipmentStatus.PICKED_UP
                : container.status;
            // shipmentUpdate.carrierCode = containerResult.carrierCode;
            shipmentUpdate.carrier =
              carrierTradePartnerMappings.get(containerResult.carrierAbbreviation) ?? null;
            if (containerResult.returnTime) {
              containerUpdate.returnDate = carrierDateStringToDate(
                containerResult.carrierAbbreviation,
                containerResult.returnTime,
                PickupReturnTrackingProcessor.name
              );
              containerUpdate.returnDateString = containerResult.returnTime;
            }
            if (containerResult.pickupTime) {
              containerUpdate.pickupDate = carrierDateStringToDate(
                containerResult.carrierAbbreviation,
                containerResult.pickupTime,
                PickupReturnTrackingProcessor.name
              );
              containerUpdate.pickupDateString = containerResult.pickupTime;
              const calculatedReturnLfd = this.calculateReturnLfd(
                containerResult.carrierAbbreviation,
                container,
                container.shipment,
                containerUpdate.pickupDate
              );
              if (!container.returnLfd && calculatedReturnLfd) {
                containerUpdate.returnLfd = calculatedReturnLfd;
                containerUpdate.returnLfdString = calculatedReturnLfd.toISOString();
              }
            }
          } else containerUpdate.trackingStatus = TrackingStatus.OFFLINE;
          this.logger.log(
            `Updating container ${container.id} of shipment ${container?.shipment?.id} with object: ${JSON.stringify(containerUpdate)}`
          );
          const containerUpdateResult = await containerRepository.update(container.id, containerUpdate);
          this.logger.debug(`Container update result: ${containerUpdateResult.affected}`);
          if (Object.keys(shipmentUpdate).length > 0) {
            this.logger.log(
              `Updating shipment ${container?.shipment?.id} with object: ${JSON.stringify(shipmentUpdate)}`
            );
            const shipmentUpdateResult = await shipmentRepository.update(
              container?.shipment?.id,
              shipmentUpdate
            );
            this.logger.debug(`Shipment update result: ${shipmentUpdateResult.affected}`);
          }
          this.logger.log(`Creating tracking history for container ${container.id}...`);
          const trackingHistory = await this.trackingHistoryService.createTrackingHistory(
            {
              timestamp: new Date(runsStartedAt),
              result:
                containerResult?.mark ||
                `No result returned by pickup return actor for container ${container.containerNumber}`,
              shipmentId: container?.shipment?.id,
              party: containerResult?.carrierAbbreviation || UNKNOWN_CARRIER
            },
            queryRunner
          );
          this.logger.debug(
            `Created tracking history for container ${container.id} of shipment ${container?.shipment?.id}: ${trackingHistory?.id}`
          );
          // Only adding shipment to be recalculated if status/pickup date/return date/return LFD is updated
          if (
            container?.shipment &&
            (![null, undefined].includes(containerUpdate.status) ||
              ["pickupDate", "returnDate", "returnLfd"].some(
                (key) => containerUpdate[key] instanceof Date
              )) &&
            !toBeRecalculatedShipments.some((s) => s.id === container.shipment.id)
          ) {
            this.logger.log(`Adding shipment ${container.shipment.id} to be recalculated...`);
            toBeRecalculatedShipments.push(container.shipment);
          }
        }
      }

      if (toBeRecalculatedShipments.length > 0) {
        this.logger.log(
          `Recalculating shipment dates and status for ${toBeRecalculatedShipments.length} shipments...`
        );
        await this.shipmentService.recalculateShipmentDatesAndStatus(toBeRecalculatedShipments, queryRunner);
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      const errorMessage = `Error in pickup return tracking processor: ${error instanceof Error ? error.message : String(error)}. Container IDs: ${containerIds.join(",")}`;
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(errorMessage, errorStack);
      this.eventEmitter.emit(TrackingEvent.PICKUP_RETURN_TRACKING_ERROR, job, error);
    } finally {
      await queryRunner.release();
    }
  }
}
