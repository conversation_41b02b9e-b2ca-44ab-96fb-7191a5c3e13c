import { Processor, WorkerHost } from "@nestjs/bullmq";
import { forwardRef, Inject, Logger } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectDataSource } from "@nestjs/typeorm";
import moment from "moment-timezone";
import {
  Container,
  Country,
  FIND_SHIPMENT_RELATIONS,
  PartnerType,
  Shipment,
  ShipmentStatus,
  TrackingHistoryService,
  TrackingStatus,
  TradePartner
} from "nest-modules";
import { DataSource, In, IsNull, QueryRunner } from "typeorm";
import { ApifyService } from "nest-modules";
import { ShipmentTrackingService } from "../services/shipment-tracking.service";
import {
  ActorRunStatus,
  APIFY_GET_RUN_STATUS_INTERVAL_MS,
  APIFY_GET_RUN_STATUS_MAX_ATTEMPTS,
  APIFY_RAILWAY_ACTOR_NAMES_AND_IDS,
  APIFY_RESULT_STORE_NAMES,
  APifyRailway<PERSON><PERSON><PERSON><PERSON><PERSON>ult,
  INITIAL_RUN_STATUSES,
  TERMINAL_RUN_STATUSES,
  TRANSITIONAL_RUN_STATUSES
} from "nest-modules";
import { TrackingEvent } from "../types/event.types";
import { DEFAULT_WORKER_OPTIONS, RailTrackingJob, TrackingQueueName } from "../types/queue.types";
import { RAIL_COMPANIES, RailCompanyCode } from "../types/railway.types";
import { waitForTimeout } from "../utils/wait-for-timeout";
import { SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS } from "../types/container.types";
import { ShipmentService } from "@/shipment/services/shipment.service";
import { ApifyActorRunDto } from "nest-modules";
import { toMonthNumber } from "../utils/to-month-number";

@Processor(
  {
    name: TrackingQueueName.SHIPMENT_RAIL_TRACKING
  },
  DEFAULT_WORKER_OPTIONS
)
export class RailTrackingProcessor extends WorkerHost {
  constructor(
    @Inject(forwardRef(() => ShipmentService))
    private readonly shipmentService: ShipmentService,
    @Inject(TrackingHistoryService)
    private readonly trackingHistoryService: TrackingHistoryService,
    @Inject(ApifyService)
    private readonly apifyService: ApifyService,
    @Inject(ShipmentTrackingService)
    private readonly shipmentTrackingService: ShipmentTrackingService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly eventEmitter: EventEmitter2
  ) {
    super();
  }
  private readonly logger = new Logger(RailTrackingProcessor.name);

  /**
   * Get container number in CN format. Example: "ABCU1234567" -> "ABCU123456"
   * @param originalContainerNumber The original container number
   * @returns The container number in CN format
   */
  private getCNContainerNumber(originalContainerNumber: string) {
    return (
      originalContainerNumber.substring(0, 4) +
      (originalContainerNumber[4] === "0" ? "" : originalContainerNumber[4]) +
      originalContainerNumber.substring(5, originalContainerNumber.length - 1)
    );
  }

  /**
   * Get container number in CP format. Example: "ABCU1234567" -> "ABCU123456"
   * @param originalContainerNumber The original container number
   * @returns The container number in CP format
   */
  private getCPContainerNumber(originalContainerNumber: string) {
    return originalContainerNumber.substring(0, originalContainerNumber.length - 1);
  }

  /**
   * Parse date time string in CN format. Example: "04 Mar 13:45" -> "2025-03-04 13:45:00"
   * @param dateTimeString The date time string in CN format
   * @returns The date object
   */
  private parseCNDateTime(dateTimeString: string) {
    const ONE_MONTH_MS = 30 * 24 * 60 * 60 * 1000;
    // CN date has no year, so assume it's current year
    const currentYear = new Date().getFullYear();
    this.logger.log(`Parsing CN date time string: ${dateTimeString}, Current year: ${currentYear}`);

    const [dayOfMonthString, monthAbbreviation, timeString] = dateTimeString.split(" ");
    const dayOfMonth = /^(0[1-9]|[1-2][0-9]|3[0-1])$/.test(dayOfMonthString.trim())
      ? parseInt(dayOfMonthString.trim())
      : null;
    const monthNumber = toMonthNumber(monthAbbreviation);
    const [hourString, minuteString] = timeString.split(":");
    const hour = /^(0[0-9]|1[0-9]|2[0-3])$/.test(hourString.trim()) ? parseInt(hourString.trim()) : null;
    const minute = /^(0[0-9]|[1-5][0-9])$/.test(minuteString.trim()) ? parseInt(minuteString.trim()) : null;
    this.logger.log(
      `Parsed CN datetime string: ${dayOfMonth}, ${monthNumber}, ${hour}, ${minute}, ${currentYear}`
    );
    if (typeof dayOfMonth !== "number")
      throw new Error(`Invalid day of month for CN date time string: ${dateTimeString}`);
    if (typeof monthNumber !== "number")
      throw new Error(`Invalid month for CN date time string: ${dateTimeString}`);
    if (typeof hour !== "number") throw new Error(`Invalid hour for CN date time string: ${dateTimeString}`);
    if (typeof minute !== "number")
      throw new Error(`Invalid minute for CN date time string: ${dateTimeString}`);

    let result = moment.tz([currentYear, monthNumber, dayOfMonth, hour, minute], "America/Toronto").toDate();
    this.logger.log(`First parsed CN date time: ${result}`);
    if (Date.now() - result.getTime() > ONE_MONTH_MS) {
      // If the date is more than one month earlier than current date, it's probably next year
      result = moment
        .tz([currentYear + 1, monthNumber, dayOfMonth, hour, minute], "America/Toronto")
        .toDate();
      this.logger.log(`Second parsed CN date time: ${result}`);
    }
    return result;
  }

  /**
   * Parse date time string in CP format. Example: "02/26/2025 18:06" -> "2025-02-26 18:06:00"
   * @param dateTimeString The date time string in CP format
   * @returns The date object
   */
  private parseCPDateTime(dateTimeString: string) {
    return moment.tz(dateTimeString, "MM/DD/YYYY HH:mm", "America/Toronto").toDate();
  }

  private async getRailCompanyTradePartner(railway: RailCompanyCode, queryRunner: QueryRunner) {
    const tradePartnerRepository = queryRunner.manager.getRepository(TradePartner);
    const countryRepository = queryRunner.manager.getRepository(Country);
    let railCompanyTradePartner = await tradePartnerRepository.findOne({
      where: { name: RAIL_COMPANIES[railway], organization: { id: IsNull() } }
    });
    if (!railCompanyTradePartner) {
      railCompanyTradePartner = new TradePartner();
      railCompanyTradePartner.name = RAIL_COMPANIES[railway];
      railCompanyTradePartner.partnerType = PartnerType.RAIL_COMPANY;
      railCompanyTradePartner.country = await countryRepository.findOneBy({ alpha2: "CA" });
      railCompanyTradePartner = await tradePartnerRepository.save(railCompanyTradePartner);
    }
    return railCompanyTradePartner;
  }

  async process(job: RailTrackingJob) {
    this.logger.log(`Starting rail tracking. Job ID: ${job.id}, Data: ${JSON.stringify(job.data)}`);

    const { containerIds } = job.data;

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const shipmentRepository = queryRunner.manager.getRepository(Shipment);
      const containerRepository = queryRunner.manager.getRepository(Container);

      // Get containers from DB
      const containers = await containerRepository.find({
        where: { id: In(containerIds) },
        relations: SHIPMENT_TRACKING_FIND_CONTAINER_RELATIONS
      });
      this.logger.log(`Found containers: ${JSON.stringify(containers.map((c) => c.id))}`);
      if (containers.length <= 0) throw new Error(`No containers found IDs ${JSON.stringify(containerIds)}`);

      // Filter valid containers
      const validContainers: Array<Container> = [];
      for (const container of containers) {
        this.logger.log(
          `Shipment: ${container?.shipment?.id}, Container: ${container.id}, Container Number: ${container.containerNumber}, Status: ${container.status}, Mode of Transport: ${container?.shipment?.modeOfTransport}, PODelivery: ${container?.shipment?.placeOfDelivery?.name}, Pickup Location: (${container?.shipment?.pickupLocation?.id}, "${container?.shipment?.pickupLocation?.name}")`
        );
        const { isValid, invalidReasons } =
          this.shipmentTrackingService.isValidRailTrackingContainer(container);

        if (isValid) {
          this.logger.log(
            `Container ${container.id} of shipment ${container?.shipment?.id} is valid for rail tracking`
          );
          validContainers.push(container);
        } else {
          this.logger.log(
            `Container ${container.id} of shipment ${container?.shipment?.id} is invalid for rail tracking, invalid reasons: ${invalidReasons.join(", ")}`
          );
          const updateResult = await containerRepository.update(container.id, {
            trackingStatus: TrackingStatus.OFFLINE
          });
          this.logger.debug(
            `Updated container ${container.id} of shipment ${container?.shipment?.id} to offline. Update result: ${updateResult.affected}`
          );
          const invalidContainerHistory = await this.trackingHistoryService.createTrackingHistory(
            {
              timestamp: new Date(),
              result: `Container ${container.containerNumber} is invalid for rail tracking. Reasons: ${invalidReasons.join(", ")}`,
              shipmentId: container?.shipment?.id,
              party: "UNKNOWN"
            },
            queryRunner
          );
          this.logger.debug(`Created invalid container history: ${invalidContainerHistory?.id}`);
        }
      }
      this.logger.log(`Valid containers: ${JSON.stringify(validContainers.map((c) => c.id))}`);

      const toBeRecalculatedShipments: Array<Shipment> = [];
      if (validContainers.length > 0) {
        // Get rail company trade partners
        const railCompanyTradePartners: Record<RailCompanyCode, TradePartner> = {
          CN: await this.getRailCompanyTradePartner("CN", queryRunner),
          CP: await this.getRailCompanyTradePartner("CP", queryRunner)
        };

        // Filter shipment by rail company
        const cnContainers = validContainers.filter(
          (c) => c.shipment?.pickupLocation?.id === railCompanyTradePartners.CN.id
        );
        const cpContainers = validContainers.filter(
          (c) => c.shipment?.pickupLocation?.id === railCompanyTradePartners.CP.id
        );
        const unknownContainers = validContainers.filter(
          (c) =>
            c.shipment?.pickupLocation?.id !== railCompanyTradePartners.CN.id &&
            c.shipment?.pickupLocation?.id !== railCompanyTradePartners.CP.id
        );
        this.logger.debug(`CN containers: ${JSON.stringify(cnContainers.map((c) => c.id))}`);
        this.logger.debug(`CP containers: ${JSON.stringify(cpContainers.map((c) => c.id))}`);
        this.logger.debug(`Unknown containers: ${JSON.stringify(unknownContainers.map((c) => c.id))}`);

        // Run railway tracking actors
        const runObjectPromiseList: Array<Promise<ApifyActorRunDto>> = [];
        if (cnContainers.length > 0 || unknownContainers.length > 0)
          runObjectPromiseList.push(
            this.apifyService.runActor(
              APIFY_RAILWAY_ACTOR_NAMES_AND_IDS.CN.name,
              {
                container_no: cnContainers
                  .concat(unknownContainers)
                  .map((c) => this.getCNContainerNumber(c.containerNumber))
                  .join(","),
                claro: true
              },
              {
                build: "latest"
              }
            )
          );
        if (cpContainers.length > 0 || unknownContainers.length > 0)
          runObjectPromiseList.push(
            this.apifyService.runActor(
              APIFY_RAILWAY_ACTOR_NAMES_AND_IDS.CP.name,
              {
                container_no: cpContainers
                  .concat(unknownContainers)
                  .map((c) => c.containerNumber)
                  .join(","),
                claro: true
              },
              {
                build: "latest"
              }
            )
          );

        let runObjectList = await Promise.all(runObjectPromiseList);
        this.logger.debug(`Started runs: ${JSON.stringify(runObjectList.map((r) => r?.data?.id))}`);
        if (runObjectList.some((r) => !r?.data?.id)) throw new Error(`Some runs have no ID returned`);

        // Wait for all runs to finish
        let attempts = 0;
        while (
          attempts < APIFY_GET_RUN_STATUS_MAX_ATTEMPTS &&
          runObjectList.some(
            (r) =>
              INITIAL_RUN_STATUSES.includes(r?.data?.status) ||
              TRANSITIONAL_RUN_STATUSES.includes(r?.data?.status)
          )
        ) {
          this.logger.debug(`Some run statuses are not terminal, wait and get statuses again`);
          attempts++;
          // TODO: Implement a better way to wait for run to finish
          await waitForTimeout(APIFY_GET_RUN_STATUS_INTERVAL_MS);
          runObjectList = await Promise.all(runObjectList.map((r) => this.apifyService.getRun(r?.data?.id)));
          this.logger.debug(
            `-- Attempt ${attempts}, Run statuses: ${JSON.stringify(
              runObjectList.map((r) => ({
                actId: r?.data?.actId,
                id: r?.data?.id,
                status: r?.data?.status
              }))
            )}`
          );
        }
        this.logger.debug(
          `Final run statuses: ${JSON.stringify(
            runObjectList.map((r) => ({
              actId: r?.data?.actId,
              id: r?.data?.id,
              status: r?.data?.status
            }))
          )}`
        );
        if (runObjectList.some((r) => !TERMINAL_RUN_STATUSES.includes(r?.data?.status)))
          throw new Error(`Some runs are not in terminal statuses`);

        // Separate run results by railway company
        const cnRunObject = runObjectList.find(
          (r) => r?.data?.actId === APIFY_RAILWAY_ACTOR_NAMES_AND_IDS.CN.id
        );
        const cpRunObject = runObjectList.find(
          (r) => r?.data?.actId === APIFY_RAILWAY_ACTOR_NAMES_AND_IDS.CP.id
        );
        this.logger.debug(`CN run object: ${JSON.stringify(cnRunObject)}`);
        this.logger.debug(`CP run object: ${JSON.stringify(cpRunObject)}`);

        // Calculate run started at date
        const runsStartedAt = runObjectList.reduce((startedAt, r) => {
          this.logger.log(`Run ID: ${r?.data?.id}, Started At: ${r?.data?.startedAt}`);
          return typeof r?.data?.startedAt === "string" &&
            r?.data?.startedAt?.length > 0 &&
            r?.data?.startedAt <= startedAt
            ? r?.data?.startedAt
            : startedAt;
        }, new Date().toISOString());

        // Get run results from key-value stores
        const cnResultRecords =
          cnRunObject?.data?.status === ActorRunStatus.SUCCEEDED
            ? await this.apifyService.getKeyValueStoreRecord<Array<APifyRailwayCheckResult>>(
                APIFY_RESULT_STORE_NAMES.RAIL,
                "CN_CLARO"
              )
            : [];
        if (!Array.isArray(cnResultRecords)) throw new Error(`No result record found for key CN_CLARO`);
        const cpResultRecords =
          cpRunObject?.data?.status === ActorRunStatus.SUCCEEDED
            ? await this.apifyService.getKeyValueStoreRecord<Array<APifyRailwayCheckResult>>(
                APIFY_RESULT_STORE_NAMES.RAIL,
                "CP_CLARO"
              )
            : [];
        if (!Array.isArray(cpResultRecords)) throw new Error(`No result record found for key CP_CLARO`);

        // Find results from both CN and CP runs for unknown containers
        for (const container of unknownContainers) {
          const cnResult = cnResultRecords.find(
            (r) => r.containerNumber === this.getCNContainerNumber(container.containerNumber)
          );
          const cpResult = cpResultRecords.find(
            (r) => r.containerNumber === this.getCPContainerNumber(container.containerNumber)
          );
          this.logger.debug(
            `-- Unknown container: ${container.id}, shipment: ${container?.shipment?.id}, CN result: ${JSON.stringify(cnResult)}, CP result: ${JSON.stringify(cpResult)}`
          );
          const containerUpdate: Partial<Container> = {};
          const shipmentUpdate: Partial<Shipment> = {};
          if (cnResult) {
            containerUpdate.trackingStatus = TrackingStatus.ONLINE;
            shipmentUpdate.pickupLocation = railCompanyTradePartners.CN;
            containerUpdate.status = ShipmentStatus.ON_RAIL;
            if (cnResult.eta) {
              containerUpdate.etaDestination = this.parseCNDateTime(cnResult.eta);
              containerUpdate.etaDestinationString = cnResult.eta;
            }
          } else if (cpResult) {
            containerUpdate.trackingStatus = TrackingStatus.ONLINE;
            shipmentUpdate.pickupLocation = railCompanyTradePartners.CP;
            containerUpdate.status = ShipmentStatus.ON_RAIL;
            if (cpResult.eta) {
              containerUpdate.etaDestination = this.parseCPDateTime(cpResult.eta);
              containerUpdate.etaDestinationString = cpResult.eta;
            }
          } else {
            containerUpdate.trackingStatus = TrackingStatus.OFFLINE;
          }
          this.logger.log(
            `Updating container ${container.id} of shipment ${container?.shipment?.id} with object: ${JSON.stringify(containerUpdate)}`
          );
          const containerUpdateResult = await containerRepository.update(container.id, containerUpdate);
          this.logger.debug(`Container update result: ${containerUpdateResult.affected}`);
          if (Object.keys(shipmentUpdate).length > 0) {
            this.logger.log(
              `Updating shipment ${container?.shipment?.id} with object: ${JSON.stringify(shipmentUpdate)}`
            );
            const shipmentUpdateResult = await shipmentRepository.update(
              container?.shipment?.id,
              shipmentUpdate
            );
            this.logger.debug(`Shipment update result: ${shipmentUpdateResult.affected}`);
          }
          this.logger.log(`Creating tracking history for container ${container.id}...`);
          const trackingHistory = await this.trackingHistoryService.createTrackingHistory(
            {
              timestamp: new Date(runsStartedAt),
              result:
                (cnResult ? cnResult.mark : cpResult ? cpResult.mark : null) ||
                `No result returned from railway tracking for container ${container.containerNumber}`,
              shipmentId: container?.shipment?.id,
              party: cnResult ? "CN" : cpResult ? "CP" : "UNKNOWN"
            },
            queryRunner
          );
          this.logger.debug(
            `Created tracking history for container ${container.id} of shipment ${container?.shipment?.id}: ${trackingHistory?.id}`
          );
          // Only adding shipment to be recalculated if ETA destination or status is updated
          if (
            container?.shipment &&
            (![null, undefined].includes(containerUpdate.status) ||
              containerUpdate.etaDestination instanceof Date) &&
            !toBeRecalculatedShipments.some((s) => s.id === container.shipment.id)
          ) {
            this.logger.log(`Adding shipment ${container.shipment.id} to be recalculated...`);
            toBeRecalculatedShipments.push(container.shipment);
          }
        }

        // Update CN containers and tracking history with results
        for (const container of cnContainers) {
          const cnResult = cnResultRecords.find(
            (r) => r.containerNumber === this.getCNContainerNumber(container.containerNumber)
          );
          this.logger.debug(
            `-- Container: ${container.id}, shipment: ${container?.shipment?.id}, Result: ${JSON.stringify(cnResult)}`
          );
          const containerUpdate: Partial<Container> = {};
          if (cnResult) {
            containerUpdate.trackingStatus = TrackingStatus.ONLINE;
            containerUpdate.status = cnResult.lastFreeDay
              ? ShipmentStatus.AT_TERMINAL
              : ShipmentStatus.ON_RAIL;
            if (cnResult.lastFreeDay) {
              // TODO: Check if CN LFD needs to minus 1 day
              containerUpdate.pickupLfd = this.parseCNDateTime(`${cnResult.lastFreeDay} 00:00`);
              containerUpdate.pickupLfdString = cnResult.lastFreeDay;
              // // Only adding shipment to be recalculated if pickup LFD or status is updated
              // if (!toBeRecalculatedShipments.some((s) => s.id === container?.shipment?.id)) {
              //   this.logger.log(`Adding shipment ${container?.shipment?.id} to be recalculated...`);
              //   toBeRecalculatedShipments.push(container?.shipment);
              // }
            }
          } else {
            containerUpdate.trackingStatus = TrackingStatus.OFFLINE;
          }
          this.logger.log(
            `Updating container ${container.id} of shipment ${container?.shipment?.id} with object: ${JSON.stringify(containerUpdate)}`
          );
          const containerUpdateResult = await containerRepository.update(container.id, containerUpdate);
          this.logger.debug(`Container update result: ${containerUpdateResult.affected}`);
          this.logger.log(`Creating tracking history for container ${container.id}...`);
          const trackingHistory = await this.trackingHistoryService.createTrackingHistory({
            timestamp: new Date(runsStartedAt),
            result:
              cnResult?.mark ||
              `No result returned from railway tracking for container ${container.containerNumber}`,
            shipmentId: container?.shipment?.id,
            party: "CN"
          });
          this.logger.debug(
            `Created tracking history for container ${container.id} of shipment ${container?.shipment?.id}: ${trackingHistory?.id}`
          );
          // Adding shipment to be recalculated if pickup LFD or container status is updated
          if (
            (![null, undefined].includes(containerUpdate.status) ||
              containerUpdate.pickupLfd instanceof Date) &&
            !toBeRecalculatedShipments.some((s) => s.id === container.shipment?.id)
          ) {
            this.logger.log(`Adding shipment ${container?.shipment?.id} to be recalculated...`);
            toBeRecalculatedShipments.push(container?.shipment);
          }
        }

        // Update CP containers and tracking history with results
        for (const container of cpContainers) {
          const cpResult = cpResultRecords.find(
            (r) => r.containerNumber === this.getCPContainerNumber(container.containerNumber)
          );
          this.logger.debug(
            `-- Container: ${container.id}, shipment: ${container?.shipment?.id}, Result: ${JSON.stringify(cpResult)}`
          );
          const containerUpdate: Partial<Container> = {};
          if (cpResult) {
            containerUpdate.trackingStatus = TrackingStatus.ONLINE;
            containerUpdate.status = cpResult.lastFreeDay
              ? ShipmentStatus.AT_TERMINAL
              : ShipmentStatus.ON_RAIL;
            if (cpResult.lastFreeDay) {
              containerUpdate.pickupLfd = this.parseCPDateTime(cpResult.lastFreeDay);
              containerUpdate.pickupLfdString = cpResult.lastFreeDay;
              // // Only adding shipment to be recalculated if pickup LFD or status is updated
              // if (!toBeRecalculatedShipments.some((s) => s.id === container?.shipment?.id)) {
              //   this.logger.log(`Adding shipment ${container?.shipment?.id} to be recalculated...`);
              //   toBeRecalculatedShipments.push(container?.shipment);
              // }
            }
          } else {
            containerUpdate.trackingStatus = TrackingStatus.OFFLINE;
          }
          this.logger.log(
            `Updating container ${container.id} of shipment ${container?.shipment?.id} with object: ${JSON.stringify(containerUpdate)}`
          );
          const containerUpdateResult = await containerRepository.update(container.id, containerUpdate);
          this.logger.debug(`Container update result: ${containerUpdateResult.affected}`);
          this.logger.log(`Creating tracking history for container ${container.id}...`);
          const trackingHistory = await this.trackingHistoryService.createTrackingHistory({
            timestamp: new Date(runsStartedAt),
            result:
              cpResult?.mark ||
              `No result returned from railway tracking for container ${container.containerNumber}`,
            shipmentId: container?.shipment?.id,
            party: "CP"
          });
          this.logger.debug(
            `Created tracking history for container ${container.id} of shipment ${container?.shipment?.id}: ${trackingHistory?.id}`
          );
          // Adding shipment to be recalculated if pickup LFD or container status is updated
          if (
            (![null, undefined].includes(containerUpdate.status) ||
              containerUpdate.pickupLfd instanceof Date) &&
            !toBeRecalculatedShipments.some((s) => s.id === container.shipment?.id)
          ) {
            this.logger.log(`Adding shipment ${container?.shipment?.id} to be recalculated...`);
            toBeRecalculatedShipments.push(container?.shipment);
          }
        }
      }

      if (toBeRecalculatedShipments.length > 0) {
        this.logger.log(`Recalculating shipment dates for ${toBeRecalculatedShipments.length} shipments...`);
        await this.shipmentService.recalculateShipmentDatesAndStatus(toBeRecalculatedShipments, queryRunner);
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      const errorMessage = `Error in rail tracking processor: ${error instanceof Error ? error.message : String(error)}. Container IDs: ${containerIds.join(",")}`;
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(errorMessage, errorStack);
      this.eventEmitter.emit(TrackingEvent.RAIL_TRACKING_ERROR, job, error);
    } finally {
      await queryRunner.release();
    }
  }
}
