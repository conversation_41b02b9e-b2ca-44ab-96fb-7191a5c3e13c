import { Logger, ValidationPipe } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { NestFactory } from "@nestjs/core";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import { useContainer } from "class-validator";
import cookieParser from "cookie-parser";
import { json } from "express";
import * as fs from "fs";
import { NodeEnv } from "nest-modules";
import { SpelunkerModule } from "nestjs-spelunker";
import { AppModule, NODE_ENV } from "./app.module";

async function bootstrap() {
  // Set log level based on environment
  const logLevel = process.env.VERBOSE ? "verbose" : NODE_ENV === NodeEnv.LOCAL ? "debug" : "log";
  Logger.log(`Log level: ${logLevel}`, "Main");

  const app = await NestFactory.create(AppModule, {
    logger: [logLevel]
  });

  // Log feature flag statuses in local environment
  if (NODE_ENV === NodeEnv.LOCAL) {
    const configService = app.get(ConfigService);
    const logger = new Logger("FeatureFlagStatus");
    logger.log(
      "-------------------- Local Dev Feature Flag Status (Environment Variables) --------------------"
    );

    const featureFlags = [
      "DISABLE_SEND_EMAIL_ALL",
      "DISABLE_SEND_CUSTOMS_STATUS_EMAIL",
      "DISABLE_SEND_BACK_OFFICE_EMAIL",
      "DISABLE_INCOMING_EMAIL_SYNC",
      "DISABLE_SEND_REPLY_EMAIL",
      "DISABLE_CUSTOMS_STATUS_CRON_JOB_LOCALLY"
    ];

    featureFlags.forEach((flagName) => {
      const isEnabled = configService.get<string>(flagName) !== "true";
      logger.log(`${flagName}: ${isEnabled ? "not_set" : "true"}`);
    });
    logger.log("-------------------------------------------------------------------------------");
  }

  // use container to inject dependencies into class-validator
  useContainer(app.select(AppModule), {
    fallbackOnErrors: true
  });

  const config = new DocumentBuilder()
    .setTitle("AutoCustoms Portal API")
    .setVersion("1.0")
    .addBearerAuth(
      {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
        description: "Access Token"
      },
      "accessToken"
    )
    .build();
  const document = SwaggerModule.createDocument(app, config);
  fs.writeFileSync("swagger-spec.json", JSON.stringify(document));
  if (NODE_ENV === NodeEnv.LOCAL) {
    SwaggerModule.setup("docs", app, document, {
      swaggerOptions: { redirect: false }
    });

    const tree = SpelunkerModule.explore(app);
    const graph = SpelunkerModule.graph(tree);
    const edges = SpelunkerModule.findGraphEdges(graph);
    // fs write to file
    fs.writeFileSync("dependency-graph.json", JSON.stringify(edges));
  }
  app.use(cookieParser());
  app.use(json({ limit: "50mb" }));
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidUnknownValues: true
    })
  );
  app.enableCors({
    origin: [
      "http://localhost:5173",
      "http://localhost:5174",
      "http://localhost:5175",
      "https://d2om6on601mdcs.cloudfront.net",
      "https://portal-dev.clarocustoms.com"
    ],
    exposedHeaders: [],
    credentials: true
  });

  await app.listen(5001);
}
bootstrap();
