import { InjectQueue } from "@nestjs/bullmq";
import { forwardRef, Inject, Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import { Email, EmailOrigin, EmailStatus, GmailToken, Importer } from "nest-modules";
import { FileService } from "src/document/services/file.service";
import { ImporterService } from "src/importer/importer.service";
import { DataSource, QueryRunner, Repository } from "typeorm";
import { CreateEmailDto } from "../../dto/email.dto";
import { GetGmailMessageProcessor } from "../../processors/get-gmail-message.processor";
import { EmailDeletionService } from "../../services/email-deletion.service";
import { EmailService } from "../../services/email.service";
import { EmailProcessingInput } from "../../types/email-processing.types";
import { GmailMessageFormat } from "../../types/gmail.types";
import { EmailQueueName, GetGmailMessageQueue } from "../../types/queue.types";
import { generateRequest } from "../../utils/generate-request";
import {
  AutoTestEmailDto,
  EmailPipelineTestDto,
  EmailPipelineTestResultDto,
  EmailPipelineTestStatusDto,
  ManualTestEmailDto,
  SimulateIncomingTestEmailDto,
  StartEmailPipelineTestDto,
  StartEmailPipelineTestResultDto,
  TestAttachmentDto,
  TestEmailScenario
} from "../dto/test-email.dto";
import { generateSampleAttachments, generateTestEmailData } from "../utils/test-data-generators";

// Default values for e2e test emails
const DEFAULTS = {
  TO_EMAIL: "<EMAIL>",
  FROM_EMAIL: "<EMAIL>",
  FROM_NAME: "Sylvester Test Client",
  DEFAULT_BODY: "Please process customs"
};

// In-memory test run tracking (replaces EmailTestRun entity)
export enum EmailTestRunStatus {
  STARTED = "started",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed",
  CLEANED_UP = "cleaned-up"
}

export interface InMemoryTestRun {
  testRunId: string;
  scenario: string;
  status: EmailTestRunStatus;
  emailId: number;
  autoCleanup: boolean;
  createdEntities?: {
    emailUpdates: number;
    shipments: number;
    commercialInvoices: number;
    documents: number;
    files: number;
    fileBatches: number;
    emailThreads: number;
  } | null;
  cleanupPerformed?: boolean | null;
  errorMessage?: string | null;
  startedAt: Date;
  completedAt?: Date | null;
  processingTimeMs?: number | null;
  userTestId?: string | null;
}

// Interface for email processing context (mirrors Gmail processor)
interface TestEmailProcessingContext {
  importer: Importer;
  contextId: any;
  emailService: EmailService;
  fileService: FileService;
}

@Injectable()
export class TestEmailService {
  private readonly logger = new Logger(TestEmailService.name);

  // In-memory storage for test runs (replaces database table)
  private readonly testRuns = new Map<string, InMemoryTestRun>();

  constructor(
    @Inject(forwardRef(() => ImporterService))
    private readonly importerService: ImporterService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly moduleRef: ModuleRef,
    private readonly configService: ConfigService,
    @InjectRepository(GmailToken)
    private readonly gmailTokenRepository: Repository<GmailToken>,
    @InjectRepository(Email)
    private readonly emailRepository: Repository<Email>,
    private readonly emailDeletionService: EmailDeletionService,
    @InjectQueue(EmailQueueName.GET_GMAIL_MESSAGE)
    private readonly getGmailMessageQueue: GetGmailMessageQueue,
    private readonly getGmailMessageProcessor: GetGmailMessageProcessor
  ) {
    // Set up periodic cleanup of old test runs (every 6 hours)
    setInterval(
      () => {
        this.cleanupOldTestRuns(24); // Clean up test runs older than 24 hours
      },
      6 * 60 * 60 * 1000
    ); // Run every 6 hours
  }

  private get SYSTEM_FROM_EMAIL(): string {
    return this.configService.get<string>("SYSTEM_FROM_EMAIL");
  }

  /**
   * Get the default Gmail token email address (the Gmail account that should "receive" test emails)
   */
  private async getDefaultGmailTokenEmail(): Promise<string> {
    const defaultGmailToken = await this.gmailTokenRepository.findOneBy({
      isDefaultMailbox: true
    });

    if (!defaultGmailToken) {
      throw new Error(
        "No default Gmail token found. Test emails require a default Gmail account to be configured."
      );
    }

    return defaultGmailToken.email;
  }

  /**
   * Resolve importer and set up processing context (mirrors Gmail processor)
   */
  private async resolveImporterAndContext(
    inboxEmail: string,
    queryRunner: QueryRunner
  ): Promise<TestEmailProcessingContext> {
    // Look up importer based on inbox email (like Gmail processor does with 'to' emails)
    const importer = await this.importerService.getImporterByReceiveEmail(
      [inboxEmail],
      queryRunner,
      true // Skip organization check to find any importer
    );

    this.logger.log(
      `Inbox email: ${inboxEmail}; Importer: ${importer?.id}; Organization: ${importer?.organization?.id}`
    );

    if (!importer) {
      throw new Error(`No importer found for inbox email: ${inboxEmail}`);
    }

    // Create context with importer's organization (mirrors Gmail processor)
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(generateRequest(null, importer.organization), contextId);

    // Resolve scoped services within the importer's organization context
    const emailService = await this.moduleRef.resolve(EmailService, contextId);
    const fileService = await this.moduleRef.resolve(FileService, contextId, {
      strict: false
    });

    await new Promise((resolve) => process.nextTick(resolve)); // Wait for children dependencies to be initialized

    return { importer, contextId, emailService, fileService };
  }

  /**
   * Determine attachments based on the new attachment logic
   * Defaults to 3 attachments for document-submission scenario
   */
  private determineAttachments(dto: SimulateIncomingTestEmailDto): TestAttachmentDto[] {
    if (dto.attachments !== undefined) {
      // Explicit attachments provided (could be empty array or populated)
      return dto.attachments;
    } else {
      // No attachments property provided - use scenario defaults (2 attachments for document-submission)
      return generateSampleAttachments(dto.scenario || TestEmailScenario.DOCUMENT_SUBMISSION);
    }
  }

  /**
   * Simulate an incoming test email (unified endpoint)
   * Directly calls GetGmailMessageProcessor.processEmailFromGmailMessage() to simulate the full pipeline
   */
  async simulateIncomingTestEmail(dto: SimulateIncomingTestEmailDto): Promise<Email> {
    this.logger.log(`Simulating incoming test email for scenario: ${dto.scenario}`);

    try {
      // Use defaults or provided values
      const toEmail = dto.inboxEmail || DEFAULTS.TO_EMAIL;
      const fromEmail = DEFAULTS.FROM_EMAIL;
      const fromName = DEFAULTS.FROM_NAME;
      const messageBody = dto.messageBody || DEFAULTS.DEFAULT_BODY;
      const subject = dto.subject || `Test Email - ${dto.scenario}`;

      // Determine attachments - use 2 attachments by default for document-submission scenario
      const attachments = this.determineAttachments(dto);

      // Create mock Gmail message structure with attachments
      const mockGmailMessage = this.createMockGmailMessageFromDto({
        toEmail,
        fromEmail,
        fromName,
        subject,
        messageBody,
        attachments
      });

      // Create input for the Gmail processor
      const processingInput: EmailProcessingInput = {
        email: toEmail, // The inbox email that "received" this test email
        message: mockGmailMessage,
        targetFormat: GmailMessageFormat.FULL,
        existingEmailRecord: null,
        latestHistoryId: null
      };

      // Call the Gmail processor directly to simulate the full pipeline
      const email = await this.getGmailMessageProcessor.processEmailFromGmailMessage(processingInput);

      this.logger.log(
        `Incoming test email simulated successfully with ID: ${email.id} for scenario: ${dto.scenario}`
      );
      return email;
    } catch (error) {
      this.logger.error(
        `Failed to simulate incoming test email for scenario ${dto.scenario}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Create a test email from manually provided data
   */
  async createManualTestEmail(dto: ManualTestEmailDto): Promise<Email> {
    this.logger.log(`Creating manual test email with subject: "${dto.subject}"`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Use provided inbox email or default
      const processedDto = { ...dto };
      if (!processedDto.inboxEmail || processedDto.inboxEmail === "SYSTEM_FROM_EMAIL_PLACEHOLDER") {
        processedDto.inboxEmail = DEFAULTS.TO_EMAIL;
      }

      // Resolve importer and set up organization context
      const context = await this.resolveImporterAndContext(processedDto.inboxEmail, queryRunner);

      // Create email entity using organization context
      const email = await this.processTestEmailWithContext(
        processedDto,
        processedDto.attachments,
        context,
        queryRunner
      );

      await queryRunner.commitTransaction();

      this.logger.log(
        `Manual test email created successfully with ID: ${email.id} in organization: ${context.importer.organization.id}`
      );
      return email;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to create manual test email: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Create a test email from auto-generated scenario data
   */
  async createAutoTestEmail(dto: AutoTestEmailDto): Promise<Email> {
    this.logger.log(`Creating auto test email for scenario: ${dto.scenario}`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Generate test email data based on scenario
      const generatedEmailData = generateTestEmailData(dto.scenario, dto.includeAttachments, dto.inboxEmail);

      // Override processFullPipeline setting
      generatedEmailData.processFullPipeline = dto.processFullPipeline;

      // Use provided inbox email or default
      if (
        !generatedEmailData.inboxEmail ||
        generatedEmailData.inboxEmail === "SYSTEM_FROM_EMAIL_PLACEHOLDER"
      ) {
        generatedEmailData.inboxEmail = DEFAULTS.TO_EMAIL;
      }

      // Resolve importer and set up organization context
      const context = await this.resolveImporterAndContext(generatedEmailData.inboxEmail, queryRunner);

      // Create email using generated data with organization context
      const email = await this.processTestEmailWithContext(
        generatedEmailData,
        generatedEmailData.attachments,
        context,
        queryRunner
      );

      await queryRunner.commitTransaction();

      this.logger.log(
        `Auto test email created successfully with ID: ${email.id} for scenario: ${dto.scenario} in organization: ${context.importer.organization.id}`
      );
      return email;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Failed to create auto test email for scenario ${dto.scenario}: ${error.message}`,
        error.stack
      );
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Process test email with organization context (mirrors Gmail processor)
   */
  private async processTestEmailWithContext(
    emailData: ManualTestEmailDto,
    attachments: TestAttachmentDto[] | undefined,
    context: TestEmailProcessingContext,
    queryRunner: QueryRunner
  ): Promise<Email> {
    // Generate unique test identifiers
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substr(2, 9);
    const testGmailId = `test-${timestamp}-${randomId}`;
    const testThreadId = `thread-test-${timestamp}`;

    // Determine email status based on attachments
    const hasAttachments = attachments && attachments.length > 0;
    const emailStatus = hasAttachments ? EmailStatus.UPLOADING_ATTACHMENTS : EmailStatus.SAVED;

    // Get the default Gmail token email to use as inboxEmail (the Gmail account that "received" this email)
    // This ensures the inboxEmail has a valid Gmail token for sending responses
    const defaultGmailEmail = await this.getDefaultGmailTokenEmail();

    // Create email DTO
    const createEmailDto: CreateEmailDto = {
      origin: EmailOrigin.INBOX,
      status: emailStatus,
      inboxEmail: defaultGmailEmail, // Use Gmail token email, not the target recipient email
      gmailId: testGmailId,
      threadId: testThreadId, // Test thread ID - will be skipped for responses to avoid Gmail errors
      historyId: timestamp.toString(),
      receiveDate: new Date(),
      from: emailData.from,
      to: emailData.to,
      cc: emailData.cc || null,
      replyTo: emailData.replyTo || null,
      subject: emailData.subject || null,
      text: emailData.text || null,
      html: emailData.html || null,
      messageId: `<test-${testGmailId}@gmail.com>`,
      requestSummary: null,
      processingOutcome: null,
      error: null,
      userIntents: null
    };

    // Create email record using scoped EmailService (within importer's organization)
    const savedEmail = await context.emailService.createEmail(createEmailDto);
    this.logger.log(
      `Test email record created with ID: ${savedEmail.id}, Gmail ID: ${testGmailId} in organization: ${context.importer.organization.id}`
    );

    // Process attachments if provided
    if (hasAttachments && attachments.length > 0) {
      await this.processTestAttachmentsWithContext(savedEmail, attachments, context);

      // Update email status to SAVED after successful attachment processing
      const updatedEmail = await context.emailService.editEmail(savedEmail.id, {
        status: EmailStatus.SAVED
      });

      this.logger.log(`Updated email ${savedEmail.id} status to SAVED after attachment processing`);

      // DO NOT emit EMAIL_SAVED event here - FileListener will emit it when documents are extracted
      // The FileListener.onBatchDocumentsExtracted will emit EMAIL_SAVED event with actual documents

      return updatedEmail;
    } else {
      // No attachments - trigger processing pipeline immediately if requested
      if (emailData.processFullPipeline) {
        await this.triggerEmailProcessingPipeline(savedEmail);
      }

      return savedEmail;
    }
  }

  /**
   * Process test email attachments with organization context
   */
  private async processTestAttachmentsWithContext(
    email: Email,
    attachments: TestAttachmentDto[],
    context: TestEmailProcessingContext
  ): Promise<void> {
    this.logger.log(
      `Processing ${attachments.length} test attachments for email ${email.id} in organization: ${context.importer.organization.id}`
    );

    try {
      // Convert test attachments to Express.Multer.File format
      const multerFiles: Express.Multer.File[] = attachments.map((attachment) => {
        const buffer = Buffer.from(attachment.b64Data, "base64");

        return {
          fieldname: "files",
          originalname: attachment.fileName,
          encoding: "7bit",
          mimetype: attachment.mimeType,
          size: buffer.length,
          buffer: buffer,
          stream: null,
          destination: null,
          filename: attachment.fileName,
          path: null
        };
      });

      // Use scoped FileService to save attachments within the importer's organization
      const { files: uploadedFiles, batchId } = await context.fileService.saveEmailAttachments(
        email.gmailId,
        multerFiles,
        {
          organizationId: context.importer.organization.id
        }
      );

      this.logger.log(
        `Successfully uploaded ${uploadedFiles.length} test attachments. Batch ID: ${batchId} in organization: ${context.importer.organization.id}`
      );

      if (uploadedFiles.length !== attachments.length) {
        throw new Error(
          `Attachment upload failed: ${uploadedFiles.length}/${attachments.length} files uploaded successfully`
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to process test attachments for email ${email.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Trigger the email processing pipeline by directly calling the refactored processor method
   * This bypasses Gmail API calls while testing the complete email processing pipeline
   */
  private async triggerEmailProcessingPipeline(email: Email): Promise<void> {
    this.logger.log(
      `Triggering email processing pipeline for email ${email.id} using refactored processor method`
    );

    // Get the default Gmail token email to use as the "email" parameter
    const defaultGmailEmail = await this.getDefaultGmailTokenEmail();

    // Create a mock Gmail message structure based on the test email
    const mockGmailMessage = this.createMockGmailMessage(email);

    // Create input for the refactored email processing method
    const processingInput: EmailProcessingInput = {
      email: defaultGmailEmail, // The Gmail account that "received" this email
      message: mockGmailMessage, // Mock Gmail message structure
      targetFormat: GmailMessageFormat.FULL, // Use FULL format for complete processing
      existingEmailRecord: email, // Pass the existing test email record
      latestHistoryId: null // Full sync mode for test emails
    };

    // Call the refactored email processing method directly
    await this.getGmailMessageProcessor.processEmailFromGmailMessage(processingInput);

    this.logger.log(
      `Email processing pipeline completed for email ${email.id} using refactored processor method`
    );
  }

  /**
   * Create a mock Gmail message structure from test DTO parameters
   * This allows the Gmail processor to work with test data without Gmail API calls
   */
  private createMockGmailMessageFromDto(params: {
    toEmail: string;
    fromEmail: string;
    fromName: string;
    subject: string;
    messageBody: string;
    attachments?: TestAttachmentDto[];
  }): any {
    // Generate unique test identifiers
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substr(2, 9);
    const testGmailId = `test-${timestamp}-${randomId}`;
    const testThreadId = `thread-test-${timestamp}`;
    const messageId = `<test-${testGmailId}@gmail.com>`;

    // Create Gmail API v1 compatible message structure
    const gmailMessage = {
      id: testGmailId,
      threadId: testThreadId,
      labelIds: ["INBOX"], // Ensure it passes the inbox check
      snippet: params.messageBody.substring(0, 100),
      historyId: timestamp.toString(),
      internalDate: Date.now().toString(),
      sizeEstimate: params.messageBody.length,
      payload: {
        partId: "",
        mimeType: params.attachments && params.attachments.length > 0 ? "multipart/mixed" : "text/plain",
        filename: "",
        headers: [
          { name: "From", value: `${params.fromName} <${params.fromEmail}>` },
          { name: "To", value: params.toEmail },
          { name: "Subject", value: params.subject },
          { name: "Message-ID", value: messageId },
          { name: "Date", value: new Date().toUTCString() }
        ],
        body:
          params.attachments && params.attachments.length > 0
            ? {}
            : {
                size: params.messageBody.length,
                data: Buffer.from(params.messageBody).toString("base64")
              },
        parts:
          params.attachments && params.attachments.length > 0
            ? [
                // Text part
                {
                  partId: "0",
                  mimeType: "text/plain",
                  filename: "",
                  headers: [{ name: "Content-Type", value: "text/plain; charset=UTF-8" }],
                  body: {
                    size: params.messageBody.length,
                    data: Buffer.from(params.messageBody).toString("base64")
                  }
                },
                // Attachment parts
                ...params.attachments.map((attachment, index) => ({
                  partId: (index + 1).toString(),
                  mimeType: attachment.mimeType,
                  filename: attachment.fileName,
                  headers: [
                    { name: "Content-Type", value: attachment.mimeType },
                    { name: "Content-Disposition", value: `attachment; filename="${attachment.fileName}"` }
                  ],
                  body: {
                    attachmentId: `test-attachment-${index + 1}-${randomId}`,
                    size: Buffer.from(attachment.b64Data, "base64").length,
                    data: attachment.b64Data
                  }
                }))
              ]
            : undefined
      }
    };

    return gmailMessage;
  }

  /**
   * Create a mock Gmail message structure from a test email record
   * This allows the refactored processor to work with test data without Gmail API calls
   * Follows the actual Gmail API v1 message structure
   */
  private createMockGmailMessage(email: Email): any {
    // Generate a test Message-ID following the existing test convention
    const messageId = email.messageId || `<test-${email.gmailId}@gmail.com>`;

    // Helper function to format email addresses properly
    const formatEmailAddresses = (addresses: Array<{ name?: string; address: string }> | null): string => {
      if (!addresses || addresses.length === 0) return "";
      return addresses
        .map((addr) => {
          if (addr.name && addr.name.trim()) {
            return `${addr.name} <${addr.address}>`;
          }
          return addr.address;
        })
        .join(", ");
    };

    // Create Gmail API v1 compatible message structure
    const gmailMessage = {
      id: email.gmailId,
      threadId: email.threadId,
      labelIds: ["INBOX"], // Ensure it passes the inbox check
      snippet: email.text ? email.text.substring(0, 100) : "",
      historyId: email.historyId,
      internalDate: email.receiveDate.getTime().toString(),
      sizeEstimate: email.text ? email.text.length + (email.html?.length || 0) : 100,
      payload: {
        partId: "",
        mimeType: email.html ? "multipart/alternative" : "text/plain",
        filename: "",
        headers: [
          { name: "From", value: formatEmailAddresses(email.from) },
          { name: "To", value: formatEmailAddresses(email.to) },
          { name: "Subject", value: email.subject || "" },
          { name: "Message-ID", value: messageId },
          { name: "Date", value: email.receiveDate.toUTCString() }
        ],
        body: email.html
          ? {}
          : {
              size: email.text?.length || 0,
              data: email.text ? Buffer.from(email.text).toString("base64") : ""
            },
        parts: email.html
          ? [
              {
                partId: "0",
                mimeType: "text/plain",
                filename: "",
                headers: [{ name: "Content-Type", value: "text/plain; charset=UTF-8" }],
                body: {
                  size: email.text?.length || 0,
                  data: email.text ? Buffer.from(email.text).toString("base64") : ""
                }
              },
              {
                partId: "1",
                mimeType: "text/html",
                filename: "",
                headers: [{ name: "Content-Type", value: "text/html; charset=UTF-8" }],
                body: {
                  size: email.html.length,
                  data: Buffer.from(email.html).toString("base64")
                }
              }
            ]
          : undefined
      }
    };

    // Add optional headers if they exist
    if (email.cc && email.cc.length > 0) {
      gmailMessage.payload.headers.push({ name: "Cc", value: formatEmailAddresses(email.cc) });
    }
    if (email.replyTo && email.replyTo.length > 0) {
      gmailMessage.payload.headers.push({ name: "Reply-To", value: formatEmailAddresses(email.replyTo) });
    }

    return gmailMessage;
  }

  /**
   * Start an async e2e email pipeline test (recommended for long processes)
   */
  async startE2EPipelineTest(dto: StartEmailPipelineTestDto): Promise<StartEmailPipelineTestResultDto> {
    const testRunId = `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const startedAt = new Date();

    this.logger.log(`Starting async e2e pipeline test with ID: ${testRunId} for scenario: ${dto.scenario}`);

    try {
      // Step 1: Create the test email
      const simulateDto: SimulateIncomingTestEmailDto = {
        scenario: dto.scenario,
        inboxEmail: dto.inboxEmail,
        messageBody: dto.messageBody,
        subject: dto.subject,
        attachments: dto.attachments,
        processFullPipeline: true // Always process full pipeline for e2e tests
      };

      const email = await this.simulateIncomingTestEmail(simulateDto);
      this.logger.log(`Test email created with ID: ${email.id} for test run: ${testRunId}`);

      // Step 2: Create in-memory test run record
      const testRun: InMemoryTestRun = {
        testRunId,
        scenario: dto.scenario || "shipment-inquiry",
        status: EmailTestRunStatus.PROCESSING,
        emailId: email.id,
        autoCleanup: dto.autoCleanup !== false,
        startedAt,
        userTestId: dto.testId || null
      };

      this.testRuns.set(testRunId, testRun);

      // Step 3: Start background monitoring only if autoCleanup is enabled
      // When autoCleanup is false, the script will handle monitoring and cleanup manually
      if (testRun.autoCleanup) {
        this.startBackgroundMonitoring(testRunId).catch((error) => {
          this.logger.error(
            `Background monitoring failed for test run ${testRunId}: ${error.message}`,
            error.stack
          );
        });
        this.logger.log(`Background monitoring started for test run ${testRunId} (autoCleanup enabled)`);
      } else {
        this.logger.log(
          `Background monitoring skipped for test run ${testRunId} (autoCleanup disabled - manual monitoring expected)`
        );
      }

      // Step 4: Return immediately with test run info
      const statusUrl = `/emails/test/pipeline-test-status/${testRunId}`;

      return {
        testRunId,
        emailId: email.id,
        gmailId: email.gmailId,
        threadId: email.threadId,
        currentStatus: email.status,
        statusUrl,
        autoCleanup: testRun.autoCleanup,
        startedAt: startedAt.toISOString()
      };
    } catch (error) {
      this.logger.error(`Failed to start async e2e pipeline test: ${error.message}`, error.stack);

      // Try to create a failed test run record in memory
      try {
        const failedTestRun: InMemoryTestRun = {
          testRunId,
          scenario: dto.scenario || "shipment-inquiry",
          status: EmailTestRunStatus.FAILED,
          emailId: 0, // No email created since creation failed
          autoCleanup: dto.autoCleanup !== false,
          startedAt,
          completedAt: new Date(),
          errorMessage: error.message,
          userTestId: dto.testId || null
        };

        this.testRuns.set(testRunId, failedTestRun);
      } catch (saveError) {
        this.logger.error(`Failed to save failed test run record: ${saveError.message}`);
      }

      throw error;
    }
  }

  /**
   * Get the status of an async e2e pipeline test
   */
  async getPipelineTestStatus(testRunId: string): Promise<EmailPipelineTestStatusDto> {
    const testRun = this.testRuns.get(testRunId);

    if (!testRun) {
      throw new Error(`Test run with ID ${testRunId} not found`);
    }

    // Get current email status if email exists
    let currentStatus = "unknown";
    let email: Email | null = null;

    if (testRun.emailId > 0) {
      try {
        email = await this.emailRepository.findOne({ where: { id: testRun.emailId } });
        currentStatus = email?.status || "unknown";
      } catch (error) {
        this.logger.warn(
          `Failed to fetch email ${testRun.emailId} for test run ${testRunId}: ${error.message}`
        );
      }
    }

    let isComplete =
      testRun.status === EmailTestRunStatus.COMPLETED ||
      testRun.status === EmailTestRunStatus.FAILED ||
      testRun.status === EmailTestRunStatus.CLEANED_UP;

    // If not marked complete but email processing is done, update the test run
    if (!isComplete && email && this.isProcessingComplete(email.status)) {
      await this.completeTestRun(testRunId);
      // Refresh the test run data from memory
      const updatedTestRun = this.testRuns.get(testRunId);
      if (updatedTestRun) {
        Object.assign(testRun, updatedTestRun);
        isComplete = true;
      }
    }

    const isSuccess =
      testRun.status === EmailTestRunStatus.COMPLETED || testRun.status === EmailTestRunStatus.CLEANED_UP;

    return {
      testRunId,
      emailId: testRun.emailId,
      currentStatus,
      isComplete,
      isSuccess,
      createdEntities: testRun.createdEntities || undefined,
      cleanupPerformed: testRun.cleanupPerformed || undefined,
      errorMessage: testRun.errorMessage || undefined,
      startedAt: testRun.startedAt.toISOString(),
      completedAt: testRun.completedAt?.toISOString() || undefined,
      processingTimeMs: testRun.processingTimeMs || undefined
    };
  }

  /**
   * Run a complete e2e email pipeline test with optional cleanup (synchronous with timeout)
   */
  async runE2EPipelineTest(dto: EmailPipelineTestDto): Promise<EmailPipelineTestResultDto> {
    const startTime = Date.now();
    this.logger.log(`Starting e2e pipeline test for scenario: ${dto.scenario}`);

    let email: Email;
    let processingSuccess = false;
    let cleanupPerformed = false;
    let errorMessage: string | undefined;
    let createdEntities: any = {};

    try {
      // Step 1: Create the test email
      const simulateDto: SimulateIncomingTestEmailDto = {
        scenario: dto.scenario,
        inboxEmail: dto.inboxEmail,
        messageBody: dto.messageBody,
        subject: dto.subject,
        attachments: dto.attachments,
        processFullPipeline: true // Always process full pipeline for e2e tests
      };

      email = await this.simulateIncomingTestEmail(simulateDto);
      this.logger.log(`Test email created with ID: ${email.id}`);

      // Step 2: Wait for processing to complete
      const finalStatus = await this.waitForEmailProcessingCompletion(email.id, dto.maxWaitSeconds || 30);
      processingSuccess = this.isProcessingSuccessful(finalStatus);

      // Step 3: Get summary of created entities
      createdEntities = await this.emailDeletionService.getEmailDeletionSummary(email.id);

      // Step 4: Perform cleanup if requested
      if (dto.autoCleanup !== false) {
        await this.emailDeletionService.deleteEmailCompletely(email.id);
        cleanupPerformed = true;
        this.logger.log(`Cleanup completed for email ${email.id}`);
      }

      const processingTimeMs = Date.now() - startTime;

      return {
        emailId: email.id,
        gmailId: email.gmailId,
        threadId: email.threadId,
        finalStatus,
        createdEntities,
        processingSuccess,
        cleanupPerformed,
        processingTimeMs
      };
    } catch (error) {
      errorMessage = error.message;
      this.logger.error(`E2E pipeline test failed: ${error.message}`, error.stack);

      // Attempt cleanup even if processing failed
      if (email && dto.autoCleanup !== false) {
        try {
          await this.emailDeletionService.deleteEmailCompletely(email.id);
          cleanupPerformed = true;
          this.logger.log(`Cleanup completed after error for email ${email.id}`);
        } catch (cleanupError) {
          this.logger.error(`Cleanup failed for email ${email.id}: ${cleanupError.message}`);
        }
      }

      const processingTimeMs = Date.now() - startTime;

      return {
        emailId: email?.id || 0,
        gmailId: email?.gmailId || "unknown",
        threadId: email?.threadId || "unknown",
        finalStatus: "failed",
        createdEntities: createdEntities || {},
        processingSuccess: false,
        cleanupPerformed,
        errorMessage,
        processingTimeMs
      };
    }
  }

  /**
   * Clean up an email and all related entities
   */
  async cleanupEmail(emailId: number): Promise<{ message: string; deletionSummary: any }> {
    this.logger.log(`Starting cleanup for email ${emailId}`);

    try {
      // Get summary before deletion
      const deletionSummary = await this.emailDeletionService.getEmailDeletionSummary(emailId);

      if (!deletionSummary.emailExists) {
        throw new Error(`Email with ID ${emailId} not found`);
      }

      // Perform deletion
      await this.emailDeletionService.deleteEmailCompletely(emailId);

      this.logger.log(`Successfully cleaned up email ${emailId}`);

      return {
        message: `Email ${emailId} and all related entities have been successfully deleted`,
        deletionSummary
      };
    } catch (error) {
      this.logger.error(`Failed to cleanup email ${emailId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Wait for email processing to complete by polling the email status
   */
  private async waitForEmailProcessingCompletion(emailId: number, maxWaitSeconds: number): Promise<string> {
    const pollIntervalMs = 1000; // Poll every second
    const maxAttempts = maxWaitSeconds;
    let attempts = 0;

    this.logger.log(`Waiting for email ${emailId} processing to complete (max ${maxWaitSeconds}s)`);

    while (attempts < maxAttempts) {
      try {
        // Get current email status
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();

        try {
          const email = await queryRunner.manager.findOne(Email, {
            where: { id: emailId }
          });

          if (!email) {
            throw new Error(`Email ${emailId} not found during processing wait`);
          }

          const status = email.status;
          this.logger.debug(
            `Email ${emailId} current status: ${status} (attempt ${attempts + 1}/${maxAttempts})`
          );

          // Check if processing is complete (success or failure)
          if (this.isProcessingComplete(status)) {
            this.logger.log(`Email ${emailId} processing completed with status: ${status}`);
            return status;
          }

          // Continue waiting
          attempts++;
          if (attempts < maxAttempts) {
            await new Promise((resolve) => setTimeout(resolve, pollIntervalMs));
          }
        } finally {
          await queryRunner.release();
        }
      } catch (error) {
        this.logger.error(`Error while waiting for email ${emailId} processing: ${error.message}`);
        throw error;
      }
    }

    // Timeout reached
    this.logger.warn(`Timeout reached waiting for email ${emailId} processing (${maxWaitSeconds}s)`);
    return "timeout";
  }

  /**
   * Check if email processing is complete (either success or failure)
   */
  private isProcessingComplete(status: string): boolean {
    const completedStatuses = [
      "responded",
      "manual-review", // Fixed: was "manual-review-required" but should be "manual-review"
      "failed-saving-email",
      "failed-shipment-search",
      "failed-analyzing-intents",
      "failed-processing-intents",
      "failed-aggregating-email",
      "failed-generating-response"
    ];

    return completedStatuses.includes(status);
  }

  /**
   * Check if email processing was successful
   */
  private isProcessingSuccessful(status: string): boolean {
    const successStatuses = ["responded"]; // Fixed: was "response-sent" but should be "responded"

    return successStatuses.includes(status);
  }

  /**
   * Start background monitoring for an async test run
   */
  private async startBackgroundMonitoring(testRunId: string): Promise<void> {
    this.logger.log(`Starting background monitoring for test run: ${testRunId}`);

    // Use setTimeout to avoid blocking the main thread
    setTimeout(async () => {
      try {
        await this.monitorTestRunCompletion(testRunId);
      } catch (error) {
        this.logger.error(
          `Background monitoring error for test run ${testRunId}: ${error.message}`,
          error.stack
        );

        // Mark test run as failed in memory
        try {
          const testRun = this.testRuns.get(testRunId);
          if (testRun) {
            testRun.status = EmailTestRunStatus.FAILED;
            testRun.completedAt = new Date();
            testRun.errorMessage = `Monitoring failed: ${error.message}`;
            this.testRuns.set(testRunId, testRun);
          }
        } catch (updateError) {
          this.logger.error(`Failed to update test run status: ${updateError.message}`);
        }
      }
    }, 1000); // Start monitoring after 1 second
  }

  /**
   * Monitor test run completion in the background
   */
  private async monitorTestRunCompletion(testRunId: string): Promise<void> {
    const maxMonitoringTime = 30 * 60 * 1000; // 30 minutes max
    const pollInterval = 10 * 1000; // Check every 10 seconds
    const startTime = Date.now();

    this.logger.log(`Monitoring test run ${testRunId} for completion (max 30 minutes)`);

    while (Date.now() - startTime < maxMonitoringTime) {
      try {
        const testRun = this.testRuns.get(testRunId);

        if (!testRun || testRun.emailId === 0) {
          this.logger.error(`Test run ${testRunId} or email not found during monitoring`);
          return;
        }

        // Get current email status
        const email = await this.emailRepository.findOne({ where: { id: testRun.emailId } });
        if (!email) {
          this.logger.error(`Email ${testRun.emailId} not found during monitoring for test run ${testRunId}`);
          return;
        }

        // Check if processing is complete
        if (this.isProcessingComplete(email.status)) {
          this.logger.log(
            `Email processing completed for test run ${testRunId} with status: ${email.status}`
          );
          await this.completeTestRun(testRunId);
          return;
        }

        // Wait before next check
        await new Promise((resolve) => setTimeout(resolve, pollInterval));
      } catch (error) {
        this.logger.error(`Error during monitoring test run ${testRunId}: ${error.message}`);
        // Continue monitoring despite errors
        await new Promise((resolve) => setTimeout(resolve, pollInterval));
      }
    }

    // Timeout reached
    this.logger.warn(`Monitoring timeout reached for test run ${testRunId} (30 minutes)`);
    const testRun = this.testRuns.get(testRunId);
    if (testRun) {
      testRun.status = EmailTestRunStatus.FAILED;
      testRun.completedAt = new Date();
      testRun.errorMessage = "Processing timeout - exceeded 30 minutes";
      this.testRuns.set(testRunId, testRun);
    }
  }

  /**
   * Complete a test run by gathering results and performing cleanup
   */
  private async completeTestRun(testRunId: string): Promise<void> {
    const testRun = this.testRuns.get(testRunId);

    if (!testRun || testRun.emailId === 0) {
      this.logger.error(`Test run ${testRunId} not found for completion`);
      return;
    }

    // Get the email to check its status
    const email = await this.emailRepository.findOne({ where: { id: testRun.emailId } });
    if (!email) {
      this.logger.error(`Email ${testRun.emailId} not found for test run ${testRunId} completion`);
      return;
    }

    const completedAt = new Date();
    const processingTimeMs = completedAt.getTime() - testRun.startedAt.getTime();
    const isSuccess = this.isProcessingSuccessful(email.status);

    try {
      // Get summary of created entities
      const createdEntities = await this.emailDeletionService.getEmailDeletionSummary(testRun.emailId);

      let cleanupPerformed = false;
      let finalStatus = isSuccess ? EmailTestRunStatus.COMPLETED : EmailTestRunStatus.FAILED;

      // Update test run with final results BEFORE cleanup
      if (testRun.autoCleanup && isSuccess) {
        finalStatus = EmailTestRunStatus.CLEANED_UP;
      }

      // Update in-memory test run
      testRun.status = finalStatus;
      testRun.createdEntities = createdEntities;
      testRun.cleanupPerformed = false; // Will be updated after cleanup if successful
      testRun.completedAt = completedAt;
      testRun.processingTimeMs = processingTimeMs;
      this.testRuns.set(testRunId, testRun);

      // Perform cleanup if requested and processing was successful
      if (testRun.autoCleanup && isSuccess) {
        try {
          await this.emailDeletionService.deleteEmailCompletely(testRun.emailId);
          cleanupPerformed = true;
          this.logger.log(`Cleanup completed for test run ${testRunId}`);

          // Update in-memory test run to reflect successful cleanup
          testRun.cleanupPerformed = true;
          this.testRuns.set(testRunId, testRun);
        } catch (cleanupError) {
          this.logger.error(`Cleanup failed for test run ${testRunId}: ${cleanupError.message}`);
          // Update test run to reflect cleanup failure
          testRun.status = EmailTestRunStatus.COMPLETED; // Revert to completed since cleanup failed
          testRun.cleanupPerformed = false;
          testRun.errorMessage = `Cleanup failed: ${cleanupError.message}`;
          this.testRuns.set(testRunId, testRun);
        }
      }

      this.logger.log(`Test run ${testRunId} completed successfully`);
    } catch (error) {
      this.logger.error(`Failed to complete test run ${testRunId}: ${error.message}`, error.stack);

      // Mark as failed in memory
      testRun.status = EmailTestRunStatus.FAILED;
      testRun.completedAt = completedAt;
      testRun.processingTimeMs = processingTimeMs;
      testRun.errorMessage = `Completion failed: ${error.message}`;
      this.testRuns.set(testRunId, testRun);
    }
  }

  /**
   * Clean up old completed test runs from memory to prevent memory leaks
   * Called periodically or when memory usage is high
   */
  private cleanupOldTestRuns(maxAgeHours: number = 24): void {
    const cutoffTime = Date.now() - maxAgeHours * 60 * 60 * 1000;
    let cleanedCount = 0;

    for (const [testRunId, testRun] of this.testRuns.entries()) {
      // Only clean up completed test runs that are older than the cutoff
      if (testRun.completedAt && testRun.completedAt.getTime() < cutoffTime) {
        this.testRuns.delete(testRunId);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.log(
        `Cleaned up ${cleanedCount} old test runs from memory (older than ${maxAgeHours} hours)`
      );
    }
  }

  /**
   * Get all active test runs (for debugging/monitoring)
   */
  getActiveTestRuns(): { testRunId: string; status: string; startedAt: string; emailId: number }[] {
    return Array.from(this.testRuns.values()).map((testRun) => ({
      testRunId: testRun.testRunId,
      status: testRun.status,
      startedAt: testRun.startedAt.toISOString(),
      emailId: testRun.emailId
    }));
  }

  /**
   * Test commercial invoice template generation
   * This method tests the Nunjucks template rendering process and returns both
   * the rendered content and template variables for validation purposes
   */
  async testCommercialInvoiceTemplate(): Promise<{
    success: boolean;
    invoiceNumber: string;
    templateVariables: any;
    renderedContentBase64: string;
    renderedContentPlainText: string;
    templateRenderTime: number;
  }> {
    this.logger.log("Testing commercial invoice template generation");

    try {
      const startTime = Date.now();

      // Import the generator function dynamically to avoid circular dependencies
      const { generateTestEmailData, generateSampleAttachments } = await import(
        "../utils/test-data-generators.js"
      );

      // Generate test data for document submission scenario
      const testEmailData = generateTestEmailData(TestEmailScenario.DOCUMENT_SUBMISSION, true);
      const attachments = generateSampleAttachments(TestEmailScenario.DOCUMENT_SUBMISSION);

      // Find the commercial invoice attachment
      const ciAttachment = attachments.find((att) => att.fileName.includes("commercial_invoice"));

      if (!ciAttachment) {
        throw new Error("Commercial invoice attachment not found in test data");
      }

      // Decode the base64 content to get the plain text
      const renderedContentPlainText = Buffer.from(ciAttachment.b64Data, "base64").toString("utf-8");

      const renderTime = Date.now() - startTime;

      // Extract invoice number from filename
      const invoiceNumberMatch = ciAttachment.fileName.match(/commercial_invoice_(.+)\.txt/);
      const invoiceNumber = invoiceNumberMatch ? invoiceNumberMatch[1] : "UNKNOWN";

      // Create a mock template variables object for display
      const templateVariables = {
        invoice_number: invoiceNumber,
        company_name: "Demo Test Electronics Ltd.",
        country: "CHINA",
        customer_company_name: "Sample Import Corp",
        total_packages: 3,
        gross_weight: "26.8",
        weight_unit: "KG",
        line_items_count: 3,
        template_render_time_ms: renderTime
      };

      this.logger.log(`Commercial invoice template generated successfully in ${renderTime}ms`);

      return {
        success: true,
        invoiceNumber,
        templateVariables,
        renderedContentBase64: ciAttachment.b64Data,
        renderedContentPlainText,
        templateRenderTime: renderTime
      };
    } catch (error) {
      this.logger.error(`Failed to test commercial invoice template: ${error.message}`, error.stack);
      throw error;
    }
  }
}
