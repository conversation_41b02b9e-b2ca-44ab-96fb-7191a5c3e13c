import { EmailAddressDto } from "nest-modules";
import nunjucks from "nunjucks";
import { TestEmailScenario, TestAttachmentDto, ManualTestEmailDto } from "../dto/test-email.dto";
import { TEMPLATE_CI_TEXT } from "./test-ci-template";

// Line item interface for commercial invoice
interface CommercialInvoiceLineItem {
  partNumber: string;
  description: string;
  quantity: number;
  unit: string;
  unitPrice: number;
  hsCode: string;
  countryOfOrigin: string;
  eanCode: string;
}

// Comprehensive options interface for commercial invoice generation
interface CommercialInvoiceOptions {
  // Date options
  invoiceDate?: string;
  blDate?: string;

  // Shipping options
  containerNumber?: string;
  vesselName?: string;
  fromPort?: string;
  toPort?: string;
  poNumber?: string;

  // Company/Vendor options
  companyName?: string;
  companyAddress?: string;
  companyPhone?: string;
  companyEmail?: string;
  country?: string;

  // Customer options
  customerCompanyName?: string;
  customerAddress?: string;
  customerPhone?: string;
  customerFax?: string;

  // Invoice options
  paymentTerms?: string;

  // Line items
  lineItems?: CommercialInvoiceLineItem[];

  // Additional costs
  transCost?: number;
  tax?: number;
  packCost?: number;
  miscCost?: number;
  discount?: number;

  // Validation data options
  includeValidationData?: boolean;
  manufacturerName?: string;
  manufacturingCountry?: string;
  countryOfExport?: string;

  // New fields for the updated generateCommercialInvoiceTXT function
  grossWeight?: number;
  weightUOM?: string;
  numberOfPackages?: number;
  packageUOM?: string;
  currency?: string;
}

// Comprehensive defaults for commercial invoice test data
const DEFAULT_TEST_CI_OPTIONS: Required<Omit<CommercialInvoiceOptions, "invoiceDate" | "blDate">> = {
  // Shipping details - consistent with Bill of Lading
  containerNumber: "TSTU1234567", // Will be overridden with unique container number
  vesselName: "TEST VESSEL MSC / V.123T", // Consistent with Bill of Lading
  fromPort: "YANTIAN, CHINA", // Consistent with Bill of Lading
  toPort: "VANCOUVER, CANADA", // Consistent with Bill of Lading
  poNumber: "PO-TEST-789",

  // Company/Vendor details - consistent with Bill of Lading
  companyName: "Demo Test Electronics Ltd.", // Consistent with Bill of Lading shipper
  companyAddress: "Building 8, Futian Industrial Zone, Shenzhen, Guangdong 518000, China",
  companyPhone: "+86-755-8888-0001",
  companyEmail: "<EMAIL>",
  country: "CHINA",

  // Customer details - consistent with Bill of Lading
  customerCompanyName: "Sample Import Corp", // Consistent with Bill of Lading consignee
  customerAddress: "1200 Burrard Street, Suite 800, Vancouver, BC V6Z 2C7, Canada",
  customerPhone: "******-555-0123",
  customerFax: "******-555-0124",

  // Invoice details
  paymentTerms: "T/T AT SIGHT",

  // Line items (3 items with clean calculations - no discounts or additional charges)
  lineItems: [
    {
      partNumber: "TEST-COMP-001",
      description: "Electronic Test Components - Sample Type A",
      quantity: 150,
      unit: "PCS",
      unitPrice: 10.0, // 150 * 10.00 = $1,500.00
      hsCode: "8542390000", // Electronic integrated circuits - valid 10-digit HS code
      countryOfOrigin: "CN", // China - valid ISO alpha-2 code
      eanCode: "1234567890123" // Valid 13-digit EAN code
    },
    {
      partNumber: "TEST-COMP-002",
      description: "Electronic Test Components - Sample Type B",
      quantity: 75,
      unit: "PCS",
      unitPrice: 30.0, // 75 * 30.00 = $2,250.00
      hsCode: "8542320000", // Memory circuits - valid 10-digit HS code
      countryOfOrigin: "CN", // China - valid ISO alpha-2 code
      eanCode: "2345678901234" // Valid 13-digit EAN code
    },
    {
      partNumber: "TEST-PKG-001",
      description: "Test Packaging Materials - Demo Bags",
      quantity: 300,
      unit: "PCS",
      unitPrice: 5.0, // 300 * 5.00 = $1,500.00
      hsCode: "3923100000", // Plastic bags - valid 10-digit HS code
      countryOfOrigin: "CN", // China - valid ISO alpha-2 code
      eanCode: "3456789012345" // Valid 13-digit EAN code
    }
  ],
  // Total quantities: 150 + 75 + 300 = 525 PCS
  // Total value: $1,500.00 + $2,250.00 + $1,500.00 = $5,250.00 (clean total, no adjustments)
  // This clean total is consistently used across HBL, AN, and CI documents

  // Additional costs - all zero for clean test data
  transCost: 0.0,
  tax: 0.0,
  packCost: 0.0,
  miscCost: 0.0,
  discount: 0.0, // No discount needed - clean line item totals

  // Validation data options
  includeValidationData: true,
  manufacturerName: "Demo Test Electronics Ltd.",
  manufacturingCountry: "CN",
  countryOfExport: "CN",

  // Required fields for proper extraction
  grossWeight: 26.8,
  weightUOM: "KG",
  packageUOM: "PACKAGES",
  numberOfPackages: 3,
  currency: "USD"

  // NOTE: All values above (weight, packages, line items, totals) are designed to be
  // consistent across HBL, Ocean Arrival Notice, and Commercial Invoice documents
  // to ensure proper test data alignment and document processing validation.
};

// Default dates for test data
const DEFAULT_TEST_DATES = {
  invoiceDate: "2025-06-23",
  blDate: "2025-06-23",
  etd: "2025-06-25"
};

/**
 * Generate a valid Cargo Control Number (CCN) that matches the required format:
 * ^[0-9A-Z]{3}(-|[0-9A-Z])[0-9A-Z]{1,21}$
 * Max total length: 25 characters
 */
function generateValidCCN(baseId: string): string {
  // Extract a unique part from the baseId and ensure it's alphanumeric
  const shortId = baseId
    .replace(/[^A-Z0-9]/g, "")
    .slice(-12)
    .toUpperCase();
  // Format: ABC-XXXXXXXXXXXX (3 chars + dash + up to 21 chars, total max 25)
  // Ensure we have at least some content after the dash
  const suffix = shortId.length > 0 ? shortId : "TEST123456789";
  return `ABC-${suffix}`;
}

// Enhanced HBL TXT template with all required shipment fields for proper extraction using Nunjucks
export const TEMPLATE_HBL_TEXT = `HOUSE OCEAN BILL OF LADING

SHIPMENT DETAILS:
Bill of Lading Number: {{ hbl_number }}
CCN: {{ cargo_control_number }}
Cargo Control Number: {{ cargo_control_number }}

SHIPPING INFORMATION:
Port of Loading: {{ port_of_loading }}
Port Code: {{ port_code }}
Port: {{ port_code }}
Sub-location: {{ sub_location }}
Sublocation: {{ sub_location }}
Sub Location Code: {{ sub_location }}
ETD (Estimated Time of Departure): {{ etd }}

CARGO DETAILS:
Total Weight: {{ weight }} {{ weight_uom }}
Number of Packages: {{ number_of_packages }}
Package Type: {{ package_type }}

VESSEL INFORMATION:
Vessel Name: {{ vessel_name }}
Container Number: {{ container_number }}

PARTIES:
Shipper: {{ shipper_name }}
{{ shipper_address }}

Consignee: {{ consignee_name }}
{{ consignee_address }}
`;

// Ocean Arrival Notice template for CBSA customs processing using Nunjucks
export const TEMPLATE_OCEAN_ARRIVAL_NOTICE_TEXT = `CANADA BORDER SERVICES AGENCY
OCEAN ARRIVAL NOTICE

SHIPMENT ARRIVAL NOTIFICATION
Notice Number: {{ notice_number }}
Date Issued: {{ date_issued }}
Port of Entry: {{ port_of_entry }}

CARGO IDENTIFICATION:
House Bill of Lading: {{ hbl_number }}
HBL Number: {{ hbl_number }}
Cargo Control Number (CCN): {{ ccn }}
CCN: {{ ccn }}
Carrier Code: {{ carrier_code }}

CBSA PROCESSING DETAILS:
Port Code: {{ port_code }}
Port: {{ port_code }}
Sub-location Code: {{ sub_location }}
Sub-location: {{ sub_location }}
Sublocation: {{ sub_location }}
Examination Facility: {{ examination_facility }}

VESSEL INFORMATION:
Vessel Name: {{ vessel_name }}
Voyage Number: {{ voyage_number }}
Container Number: {{ container_number }}

CARGO DETAILS:
Weight: {{ weight }} {{ weight_uom }}
Quantity: {{ quantity }} {{ quantity_uom }}
ETA Port: {{ eta_port }}
ETA Destination: {{ eta_destination }}

PARTIES:
Consignee: {{ consignee_name }}
{{ consignee_address }}

Notify Party: {{ notify_party_name }}
{{ notify_party_address }}

CBSA INSTRUCTIONS:
- Cargo available for examination
- Contact CBSA for release authorization
- Present this notice for cargo pickup
- Examination required before release
`;

function generateHouseBillOfLadingTXT(
  hblNumber: string,
  options: {
    cargoControlNumber: string;
    portOfLoading: string;
    portCode: string;
    subLocation: string;
    weight: number;
    weightUOM: string;
    etd: string;
    numberOfPackages?: number;
    packageType?: string;
    vesselName?: string;
    containerNumber?: string;
    shipperName?: string;
    shipperAddress?: string;
    consigneeName?: string;
    consigneeAddress?: string;
  }
): string {
  // Prepare template variables for the HBL template
  const templateVariables = {
    hbl_number: hblNumber,
    cargo_control_number: options.cargoControlNumber,
    port_of_loading: options.portOfLoading,
    port_code: options.portCode,
    sub_location: options.subLocation,
    weight: options.weight,
    weight_uom: options.weightUOM,
    etd: options.etd,
    number_of_packages: options.numberOfPackages || 3,
    package_type: options.packageType || "PACKAGES",
    vessel_name: options.vesselName || "TEST VESSEL MSC / V.123T",
    container_number: options.containerNumber || "TSTU1234567",
    shipper_name: options.shipperName || "Demo Test Electronics Ltd.",
    shipper_address:
      options.shipperAddress || "Building 8, Futian Industrial Zone, Shenzhen, Guangdong 518000, China",
    consignee_name: options.consigneeName || "Sample Import Corp",
    consignee_address:
      options.consigneeAddress || "1200 Burrard Street, Suite 800, Vancouver, BC V6Z 2C7, Canada"
  };

  try {
    const env = new nunjucks.Environment(null, {
      autoescape: false,
      throwOnUndefined: false
    });
    const renderedContent = env.renderString(TEMPLATE_HBL_TEXT, templateVariables);

    // Log the completed HBL for debugging
    console.log("=== GENERATED HBL CONTENT ===");
    console.log(renderedContent);
    console.log("=== END HBL CONTENT ===");

    return renderedContent;
  } catch (error) {
    console.error("Error rendering HBL Nunjucks template:", error);
    const fallbackContent = `HOUSE OCEAN BILL OF LADING - FALLBACK\n\nBill of Lading Number: ${hblNumber}`;
    return fallbackContent;
  }
}

/**
 * Generate Ocean Arrival Notice TXT using Nunjucks template
 * This document provides CBSA-specific fields (CCN, Port Code, Sub-location) for ocean shipment validation
 */
function generateOceanArrivalNoticeTXT(
  hblNumber: string,
  options: {
    ccn: string;
    portCode: string;
    subLocation: string;
    carrierCode: string;
    weight: number;
    weightUOM: string;
    quantity?: number;
    quantityUOM?: string;
    etaPort?: string;
    etaDestination?: string;
    vesselName?: string;
    voyageNumber?: string;
    containerNumber?: string;
    consigneeName?: string;
    consigneeAddress?: string;
    notifyPartyName?: string;
    notifyPartyAddress?: string;
    portOfEntry?: string;
    examinationFacility?: string;
  }
): string {
  // Generate unique notice number
  const timestamp = Date.now();
  const noticeNumber = `AN-${new Date().getFullYear()}-${timestamp.toString().slice(-6)}`;
  const dateIssued = new Date().toISOString().split("T")[0]; // YYYY-MM-DD format

  // Prepare template variables for the Ocean Arrival Notice template
  const templateVariables = {
    notice_number: noticeNumber,
    date_issued: dateIssued,
    port_of_entry: options.portOfEntry || "Vancouver, BC",
    hbl_number: hblNumber,
    ccn: options.ccn,
    carrier_code: options.carrierCode,
    port_code: options.portCode,
    sub_location: options.subLocation,
    examination_facility: options.examinationFacility || "CBSA Examination Facility - Vancouver",
    vessel_name: options.vesselName || "TEST VESSEL MSC / V.123T",
    voyage_number: options.voyageNumber || "V.123T",
    container_number: options.containerNumber || "TSTU1234567",
    weight: options.weight,
    weight_uom: options.weightUOM,
    quantity: options.quantity || 3,
    quantity_uom: options.quantityUOM || "PACKAGES",
    eta_port: options.etaPort || dateIssued,
    eta_destination: options.etaDestination || dateIssued,
    consignee_name: options.consigneeName || "Sample Import Corp",
    consignee_address:
      options.consigneeAddress || "1200 Burrard Street, Suite 800, Vancouver, BC V6Z 2C7, Canada",
    notify_party_name: options.notifyPartyName || "Sample Import Corp",
    notify_party_address:
      options.notifyPartyAddress || "1200 Burrard Street, Suite 800, Vancouver, BC V6Z 2C7, Canada"
  };

  try {
    const env = new nunjucks.Environment(null, {
      autoescape: false,
      throwOnUndefined: false
    });
    const renderedContent = env.renderString(TEMPLATE_OCEAN_ARRIVAL_NOTICE_TEXT, templateVariables);

    // Log the completed Ocean Arrival Notice for debugging
    console.log("=== GENERATED OCEAN ARRIVAL NOTICE CONTENT ===");
    console.log(renderedContent);
    console.log("=== END OCEAN ARRIVAL NOTICE CONTENT ===");

    return renderedContent;
  } catch (error) {
    console.error("Error rendering Ocean Arrival Notice Nunjucks template:", error);
    const fallbackContent = `OCEAN ARRIVAL NOTICE - FALLBACK\n\nHBL Number: ${hblNumber}\nCCN: ${options.ccn}`;
    return fallbackContent;
  }
}

/**
 * Get default test email addresses
 */
export function getDefaultTestEmailAddresses(): {
  defaultSender: EmailAddressDto;
} {
  const defaultSender = {
    address: "<EMAIL>",
    name: "Sylvester Test Client"
  };

  return { defaultSender };
}

/**
 * Generate unique identifiers for test data to avoid conflicts
 * Uses CI-TEST-2024-XXXXXX format for commercial invoices as requested
 * HBL format updated to stay within Candata's 20-character limit
 */
function generateUniqueTestIds() {
  const timestamp = Date.now();
  const shortTimestamp = timestamp.toString().slice(-8); // Last 8 digits for uniqueness
  const randomSuffix = Math.random().toString(36).substr(2, 4).toUpperCase(); // 4 chars instead of 6
  const year = new Date().getFullYear();

  return {
    hblNumber: `TEST-${shortTimestamp}-${randomSuffix}`, // 18 chars max, fits in 20-char limit
    ccnNumber: `CCN-TEST-${timestamp}-${randomSuffix}`,
    commercialInvoice: `CI-TEST-${year}-${randomSuffix}`,
    oceanArrivalNotice: `AN-TEST-${year}-${randomSuffix}`,
    containerNumber: `TEST${randomSuffix}${timestamp.toString().slice(-4)}`
  };
}

/**
 * Generate email content based on scenario type
 */
export function generateEmailContent(scenario: TestEmailScenario): {
  subject: string;
  text: string;
  html: string;
} {
  const { defaultSender } = getDefaultTestEmailAddresses();
  const uniqueIds = generateUniqueTestIds();

  switch (scenario) {
    case TestEmailScenario.DOCUMENT_SUBMISSION:
      return {
        subject: `${uniqueIds.hblNumber} (test)`,
        text: `Please clear this shipment.


---
Note: This is an automated test email for the e2e pipeline testing system.
---

Thank you,
${defaultSender.name}`,
        html: `<p>Please clear this shipment.</p>
<br />
<p><em>Note: This is an automated test email for the e2e pipeline testing system.</em></p>
<hr>
<p>Thank you,<br>${defaultSender.name}</p>`
      };

    default:
      throw new Error(`Unknown test email scenario: ${scenario}`);
  }
}

/**
 * Generate sample attachments for testing
 */
export function generateSampleAttachments(scenario: TestEmailScenario): TestAttachmentDto[] {
  const attachments: TestAttachmentDto[] = [];
  const uniqueIds = generateUniqueTestIds();

  switch (scenario) {
    case TestEmailScenario.DOCUMENT_SUBMISSION:
      // Log data alignment for verification
      console.log("=== TEST DATA ALIGNMENT VERIFICATION ===");
      console.log("HBL/AN Weight:", DEFAULT_TEST_CI_OPTIONS.grossWeight, DEFAULT_TEST_CI_OPTIONS.weightUOM);
      console.log(
        "HBL/AN Packages:",
        DEFAULT_TEST_CI_OPTIONS.numberOfPackages,
        DEFAULT_TEST_CI_OPTIONS.packageUOM
      );

      // Calculate CI totals for verification
      const ciSubtotal = DEFAULT_TEST_CI_OPTIONS.lineItems.reduce(
        (sum, item) => sum + item.quantity * item.unitPrice,
        0
      );
      const ciFinalTotal = ciSubtotal - DEFAULT_TEST_CI_OPTIONS.discount;
      const totalQuantity = DEFAULT_TEST_CI_OPTIONS.lineItems.reduce((sum, item) => sum + item.quantity, 0);

      console.log("CI Line Items:");
      DEFAULT_TEST_CI_OPTIONS.lineItems.forEach((item, index) => {
        console.log(
          `  ${index + 1}. ${item.description}: ${item.quantity} ${item.unit} @ $${item.unitPrice} = $${item.quantity * item.unitPrice}`
        );
      });
      console.log("CI Subtotal:", `$${ciSubtotal.toFixed(2)}`);
      console.log("CI Discount:", `$${DEFAULT_TEST_CI_OPTIONS.discount.toFixed(2)}`);
      console.log("CI Final Total:", `$${ciFinalTotal.toFixed(2)}`);
      console.log("CI Total Quantity:", totalQuantity, "PCS");
      console.log("=== END DATA ALIGNMENT ===");

      // Bill of Lading (required for shipment creation) - enhanced with all required fields
      attachments.push({
        fileName: `house_ocean_bill_of_lading_${uniqueIds.hblNumber}.txt`,
        mimeType: "text/plain",
        b64Data: Buffer.from(
          generateHouseBillOfLadingTXT(uniqueIds.hblNumber, {
            cargoControlNumber: generateValidCCN(uniqueIds.ccnNumber),
            portOfLoading: "YANTIAN, CHINA",
            portCode: "0315", // Correct format: ^0[0-9]{3}$ (Yantian port code)
            subLocation: "0001", // Must be 4 digits: ^[0-9]{4}$ (different from port code)
            weight: DEFAULT_TEST_CI_OPTIONS.grossWeight,
            weightUOM: DEFAULT_TEST_CI_OPTIONS.weightUOM,
            etd: DEFAULT_TEST_DATES.etd,
            numberOfPackages: DEFAULT_TEST_CI_OPTIONS.numberOfPackages,
            packageType: DEFAULT_TEST_CI_OPTIONS.packageUOM,
            vesselName: DEFAULT_TEST_CI_OPTIONS.vesselName,
            containerNumber: uniqueIds.containerNumber,
            shipperName: DEFAULT_TEST_CI_OPTIONS.companyName,
            shipperAddress: DEFAULT_TEST_CI_OPTIONS.companyAddress,
            consigneeName: DEFAULT_TEST_CI_OPTIONS.customerCompanyName,
            consigneeAddress: DEFAULT_TEST_CI_OPTIONS.customerAddress
          })
        ).toString("base64")
      });
      // Commercial Invoice TXT (for commercial invoice creation) - minimal required fields only
      attachments.push({
        fileName: `commercial_invoice_${uniqueIds.commercialInvoice}.txt`,
        mimeType: "text/plain",
        b64Data: generateCommercialInvoiceTXT(uniqueIds.commercialInvoice, {
          companyName: DEFAULT_TEST_CI_OPTIONS.companyName,
          companyAddress: DEFAULT_TEST_CI_OPTIONS.companyAddress,
          customerCompanyName: DEFAULT_TEST_CI_OPTIONS.customerCompanyName,
          customerAddress: DEFAULT_TEST_CI_OPTIONS.customerAddress,
          countryOfExport: DEFAULT_TEST_CI_OPTIONS.countryOfExport,
          grossWeight: DEFAULT_TEST_CI_OPTIONS.grossWeight,
          weightUOM: DEFAULT_TEST_CI_OPTIONS.weightUOM,
          numberOfPackages: DEFAULT_TEST_CI_OPTIONS.numberOfPackages,
          packageUOM: DEFAULT_TEST_CI_OPTIONS.packageUOM,
          currency: DEFAULT_TEST_CI_OPTIONS.currency,
          discount: DEFAULT_TEST_CI_OPTIONS.discount, // Include discount to match HBL/AN totals
          lineItems: DEFAULT_TEST_CI_OPTIONS.lineItems // Use all default line items
        })
      });

      // Ocean Arrival Notice TXT (provides CBSA-specific CCN, Port Code, Sub-location fields)
      attachments.push({
        fileName: `ocean_arrival_notice_${uniqueIds.oceanArrivalNotice}.txt`,
        mimeType: "text/plain",
        b64Data: Buffer.from(
          generateOceanArrivalNoticeTXT(uniqueIds.hblNumber, {
            ccn: generateValidCCN(uniqueIds.ccnNumber),
            portCode: "0315", // Vancouver port code (4 digits starting with 0)
            subLocation: "0001", // CBSA examination facility code (4 digits)
            carrierCode: "ABCD", // Test carrier code
            weight: DEFAULT_TEST_CI_OPTIONS.grossWeight,
            weightUOM: DEFAULT_TEST_CI_OPTIONS.weightUOM,
            quantity: DEFAULT_TEST_CI_OPTIONS.numberOfPackages,
            quantityUOM: DEFAULT_TEST_CI_OPTIONS.packageUOM,
            etaPort: DEFAULT_TEST_DATES.etd,
            etaDestination: DEFAULT_TEST_DATES.etd,
            vesselName: DEFAULT_TEST_CI_OPTIONS.vesselName,
            voyageNumber: "V.123T",
            containerNumber: uniqueIds.containerNumber,
            consigneeName: DEFAULT_TEST_CI_OPTIONS.customerCompanyName,
            consigneeAddress: DEFAULT_TEST_CI_OPTIONS.customerAddress,
            notifyPartyName: DEFAULT_TEST_CI_OPTIONS.customerCompanyName,
            notifyPartyAddress: DEFAULT_TEST_CI_OPTIONS.customerAddress,
            portOfEntry: "Vancouver, BC",
            examinationFacility: "CBSA Examination Facility - Vancouver"
          })
        ).toString("base64")
      });
  }

  return attachments;
}

/**
 * Convert plain text to simple HTML
 */
export function convertTextToHtml(text: string): string {
  if (!text) return "";

  return (
    text
      // Convert line breaks to <br> tags
      .replace(/\n\n/g, "</p><p>")
      .replace(/\n/g, "<br>")
      // Wrap in paragraph tags
      .replace(/^/, "<p>")
      .replace(/$/, "</p>")
      // Handle lists (simple detection)
      .replace(/<p>(\d+\.\s)/g, "<ol><li>")
      .replace(/(<li>.*?)<\/p>/g, "$1</li></ol>")
      .replace(/<p>(-\s|\*\s)/g, "<ul><li>")
      .replace(/(<li>.*?)<\/p>/g, "$1</li></ul>")
      // Bold text (simple **text** pattern)
      .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
      // Clean up any double paragraph tags
      .replace(/<\/p><p><\/p>/g, "</p>")
      .replace(/<p><\/p>/g, "")
  );
}

/**
 * Generate complete test email data for a scenario
 */
export function generateTestEmailData(
  scenario: TestEmailScenario,
  includeAttachments: boolean = false,
  inboxEmail: string = "<EMAIL>"
): ManualTestEmailDto {
  const { defaultSender } = getDefaultTestEmailAddresses();
  const content = generateEmailContent(scenario);

  const testEmail: ManualTestEmailDto = {
    from: [defaultSender],
    to: [{ address: inboxEmail, name: "System" }],
    subject: content.subject,
    text: content.text,
    html: content.html,
    inboxEmail,
    processFullPipeline: true
  };

  if (includeAttachments) {
    testEmail.attachments = generateSampleAttachments(scenario);
  }

  return testEmail;
}

/**
 * Generate Commercial Invoice TXT using Nunjucks template with comprehensive parameterization
 * Ensures all template variables are properly populated and calculations are accurate
 */
function generateCommercialInvoiceTXT(
  invoiceNumber = "TEST-CI-2024-001",
  options: CommercialInvoiceOptions = {}
): string {
  // Merge defaults with provided options
  const config = {
    ...DEFAULT_TEST_CI_OPTIONS,
    ...options
  };

  // Process all line items and calculate totals
  const lineItems = config.lineItems.map((item) => ({
    description: item.description,
    hsCode: item.hsCode,
    countryOfOrigin: item.countryOfOrigin,
    quantity: item.quantity,
    unit: item.unit,
    unitPrice: item.unitPrice,
    totalValue: item.quantity * item.unitPrice
  }));

  // Calculate invoice total from all line items
  const subtotal = lineItems.reduce((sum, item) => sum + item.totalValue, 0);
  const finalTotal = subtotal - (config.discount || 0);

  // Prepare template variables for the CI template
  const templateVariables = {
    company_name: config.companyName,
    company_address: config.companyAddress,
    customer_company_name: config.customerCompanyName,
    customer_address: config.customerAddress,
    invoice_number: invoiceNumber,
    currency: config.currency || "USD",
    country_of_export: config.countryOfExport || config.country,
    gross_weight: config.grossWeight || "26.8",
    weight_unit: config.weightUOM || "KG",
    total_packages: config.numberOfPackages || 3,
    package_type: config.packageUOM || "PACKAGES",
    lineItems,
    invoice_total: finalTotal.toFixed(2)
  };

  try {
    const env = new nunjucks.Environment(null, {
      autoescape: false,
      throwOnUndefined: false
    });
    const renderedContent = env.renderString(TEMPLATE_CI_TEXT, templateVariables);

    // Log the CI content for debugging
    console.log("=== GENERATED COMMERCIAL INVOICE CONTENT ===");
    console.log(renderedContent);
    console.log("=== END CI CONTENT ===");

    return Buffer.from(renderedContent).toString("base64");
  } catch (error) {
    console.error("Error rendering Nunjucks template:", error);
    const fallbackContent = `COMMERCIAL INVOICE - FALLBACK\n\nInvoice Number: ${invoiceNumber}`;
    return Buffer.from(fallbackContent).toString("base64");
  }
}
