import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  Is<PERSON>rray,
  IsBase64,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsMimeType,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  <PERSON>,
  <PERSON><PERSON><PERSON>th,
  ValidateNested
} from "class-validator";
import { EmailAddressDto } from "nest-modules";

export class TestAttachmentDto {
  @ApiProperty({ type: "string", minLength: 1, description: "File name of the attachment" })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  fileName: string;

  @ApiProperty({ type: "string", minLength: 1, description: "MIME type of the attachment" })
  @IsNotEmpty()
  @IsMimeType()
  mimeType: string;

  @ApiProperty({
    type: "string",
    format: "byte",
    minLength: 1,
    description: "Base64-encoded attachment data"
  })
  @IsNotEmpty()
  @IsBase64()
  @MinLength(1)
  b64Data: string;
}

export class ManualTestEmailDto {
  @ApiProperty({
    type: () => [EmailAddressDto],
    minItems: 1,
    description: "List of sender email addresses and names"
  })
  @Type(() => EmailAddressDto)
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  from: EmailAddressDto[];

  @ApiProperty({
    type: () => [EmailAddressDto],
    minItems: 1,
    description: "List of recipient email addresses and names"
  })
  @Type(() => EmailAddressDto)
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  to: EmailAddressDto[];

  @ApiPropertyOptional({
    type: () => [EmailAddressDto],
    minItems: 0,
    description: "List of carbon-copy recipient email addresses and names"
  })
  @Type(() => EmailAddressDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  cc?: EmailAddressDto[];

  @ApiPropertyOptional({
    type: () => [EmailAddressDto],
    minItems: 0,
    description: "List of reply-to email addresses and names"
  })
  @Type(() => EmailAddressDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  replyTo?: EmailAddressDto[];

  @ApiPropertyOptional({ type: "string", description: "Subject of the email" })
  @IsOptional()
  @IsString()
  subject?: string;

  @ApiPropertyOptional({ type: "string", description: "Text content of the email" })
  @IsOptional()
  @IsString()
  text?: string;

  @ApiPropertyOptional({ type: "string", description: "HTML content of the email" })
  @IsOptional()
  @IsString()
  html?: string;

  @ApiProperty({
    type: "string",
    format: "email",
    description: "Inbox email address that received this email"
  })
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  inboxEmail: string;

  @ApiPropertyOptional({
    type: () => [TestAttachmentDto],
    description: "Optional attachments for the email"
  })
  @Type(() => TestAttachmentDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  attachments?: TestAttachmentDto[];

  @ApiPropertyOptional({
    type: "boolean",
    description: "Whether to process through full pipeline",
    default: true
  })
  @IsOptional()
  @IsBoolean()
  processFullPipeline?: boolean = true;
}

export enum TestEmailScenario {
  DOCUMENT_SUBMISSION = "document-submission"
}

export class AutoTestEmailDto {
  @ApiProperty({
    enum: TestEmailScenario,
    description: "Type of test email scenario to generate"
  })
  @IsEnum(TestEmailScenario)
  scenario: TestEmailScenario;

  @ApiPropertyOptional({
    type: "boolean",
    description: "Whether to include sample attachments",
    default: false
  })
  @IsOptional()
  @IsBoolean()
  includeAttachments?: boolean = false;

  @ApiPropertyOptional({
    type: "string",
    format: "email",
    description: "Inbox email address that received this email",
    default: "<EMAIL>"
  })
  @IsOptional()
  @IsString()
  @IsEmail()
  inboxEmail?: string = "<EMAIL>";

  @ApiPropertyOptional({
    type: "boolean",
    description: "Whether to process through full pipeline",
    default: true
  })
  @IsOptional()
  @IsBoolean()
  processFullPipeline?: boolean = true;
}

export class SimulateIncomingTestEmailDto {
  @ApiPropertyOptional({
    enum: TestEmailScenario,
    description: "Type of test email scenario to generate",
    default: TestEmailScenario.DOCUMENT_SUBMISSION
  })
  @IsOptional()
  @IsEnum(TestEmailScenario)
  scenario?: TestEmailScenario = TestEmailScenario.DOCUMENT_SUBMISSION;

  @ApiPropertyOptional({
    type: "string",
    format: "email",
    description:
      "Inbox email address that received this email. If not provided, uses system from email. Use specific addresses like '<EMAIL>' to trigger specific organization processing."
  })
  @IsOptional()
  @IsString()
  @IsEmail()
  inboxEmail?: string;

  @ApiPropertyOptional({
    type: "string",
    description:
      "Custom message body (text version). If provided, HTML version will be auto-generated. If not provided, uses pre-generated content based on scenario."
  })
  @IsOptional()
  @IsString()
  messageBody?: string;

  @ApiPropertyOptional({
    type: "string",
    description: "Custom subject line. If not provided, uses pre-generated subject based on scenario."
  })
  @IsOptional()
  @IsString()
  subject?: string;

  @ApiPropertyOptional({
    type: () => [EmailAddressDto],
    description:
      "Custom sender information. If not provided, uses default sender (<EMAIL>)."
  })
  @Type(() => EmailAddressDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  from?: EmailAddressDto[];

  @ApiPropertyOptional({
    type: () => [TestAttachmentDto],
    description:
      "Attachments behavior: undefined = use scenario defaults, empty array = no attachments, populated array = use provided attachments"
  })
  @Type(() => TestAttachmentDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  attachments?: TestAttachmentDto[];

  @ApiPropertyOptional({
    type: "boolean",
    description: "Whether to process through full pipeline",
    default: true
  })
  @IsOptional()
  @IsBoolean()
  processFullPipeline?: boolean = true;
}

// Keep for backward compatibility
export class FlexibleTestEmailDto extends SimulateIncomingTestEmailDto {}

/**
 * DTO for starting e2e email pipeline tests (async)
 */
export class StartEmailPipelineTestDto {
  @ApiPropertyOptional({
    enum: TestEmailScenario,
    description: "Test scenario to simulate",
    default: TestEmailScenario.DOCUMENT_SUBMISSION
  })
  @IsOptional()
  @IsEnum(TestEmailScenario)
  scenario?: TestEmailScenario = TestEmailScenario.DOCUMENT_SUBMISSION;

  @ApiPropertyOptional({
    type: "string",
    description: "Inbox email address to use for the test",
    example: "<EMAIL>"
  })
  @IsOptional()
  @IsEmail()
  inboxEmail?: string;

  @ApiPropertyOptional({
    type: "string",
    description: "Custom message body to override scenario default"
  })
  @IsOptional()
  @IsString()
  messageBody?: string;

  @ApiPropertyOptional({
    type: "string",
    description: "Custom subject to override scenario default"
  })
  @IsOptional()
  @IsString()
  subject?: string;

  @ApiPropertyOptional({
    type: () => [TestAttachmentDto],
    description:
      "Attachments behavior: undefined = use scenario defaults, empty array = no attachments, populated array = use provided attachments"
  })
  @Type(() => TestAttachmentDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  attachments?: TestAttachmentDto[];

  @ApiPropertyOptional({
    type: "boolean",
    description: "Whether to automatically clean up created entities after test completion",
    default: true
  })
  @IsOptional()
  @IsBoolean()
  autoCleanup?: boolean = true;

  @ApiPropertyOptional({
    type: "string",
    description: "Optional test identifier for tracking"
  })
  @IsOptional()
  @IsString()
  testId?: string;
}

/**
 * DTO for synchronous e2e email pipeline tests (with short timeout)
 */
export class EmailPipelineTestDto extends StartEmailPipelineTestDto {
  @ApiPropertyOptional({
    type: "number",
    description:
      "Maximum time to wait for email processing to complete (in seconds). For long processes, use async endpoints instead.",
    default: 30
  })
  @IsOptional()
  @IsNumber()
  @Min(5)
  @Max(300)
  maxWaitSeconds?: number = 30;
}

/**
 * Response DTO for email pipeline test results
 */
export class EmailPipelineTestResultDto {
  @ApiProperty({
    type: "number",
    description: "ID of the created test email"
  })
  emailId: number;

  @ApiProperty({
    type: "string",
    description: "Gmail ID of the created test email"
  })
  gmailId: string;

  @ApiProperty({
    type: "string",
    description: "Thread ID of the created test email"
  })
  threadId: string;

  @ApiProperty({
    type: "string",
    description: "Final status of the email processing"
  })
  finalStatus: string;

  @ApiProperty({
    type: "object",
    description: "Summary of entities created during processing"
  })
  createdEntities: {
    emailUpdates: number;
    shipments: number;
    commercialInvoices: number;
    documents: number;
    files: number;
    fileBatches: number;
    emailThreads: number;
  };

  @ApiProperty({
    type: "boolean",
    description: "Whether processing completed successfully"
  })
  processingSuccess: boolean;

  @ApiProperty({
    type: "boolean",
    description: "Whether cleanup was performed"
  })
  cleanupPerformed: boolean;

  @ApiPropertyOptional({
    type: "string",
    description: "Error message if processing failed"
  })
  errorMessage?: string;

  @ApiProperty({
    type: "number",
    description: "Total processing time in milliseconds"
  })
  processingTimeMs: number;
}

/**
 * Response DTO for starting async email pipeline test
 */
export class StartEmailPipelineTestResultDto {
  @ApiProperty({
    type: "string",
    description: "Unique test run identifier for tracking"
  })
  testRunId: string;

  @ApiProperty({
    type: "number",
    description: "ID of the created test email"
  })
  emailId: number;

  @ApiProperty({
    type: "string",
    description: "Gmail ID of the created test email"
  })
  gmailId: string;

  @ApiProperty({
    type: "string",
    description: "Thread ID of the created test email"
  })
  threadId: string;

  @ApiProperty({
    type: "string",
    description: "Current status of the email processing"
  })
  currentStatus: string;

  @ApiProperty({
    type: "string",
    description: "URL to check test progress"
  })
  statusUrl: string;

  @ApiProperty({
    type: "boolean",
    description: "Whether auto cleanup is enabled"
  })
  autoCleanup: boolean;

  @ApiProperty({
    type: "string",
    description: "Timestamp when test was started"
  })
  startedAt: string;
}

/**
 * Response DTO for checking email pipeline test status
 */
export class EmailPipelineTestStatusDto {
  @ApiProperty({
    type: "string",
    description: "Test run identifier"
  })
  testRunId: string;

  @ApiProperty({
    type: "number",
    description: "Email ID being tested"
  })
  emailId: number;

  @ApiProperty({
    type: "string",
    description: "Current processing status"
  })
  currentStatus: string;

  @ApiProperty({
    type: "boolean",
    description: "Whether processing is complete"
  })
  isComplete: boolean;

  @ApiProperty({
    type: "boolean",
    description: "Whether processing was successful"
  })
  isSuccess: boolean;

  @ApiPropertyOptional({
    type: "object",
    description: "Summary of entities created (only available when complete)"
  })
  createdEntities?: {
    emailUpdates: number;
    shipments: number;
    commercialInvoices: number;
    documents: number;
    files: number;
    fileBatches: number;
    emailThreads: number;
  };

  @ApiPropertyOptional({
    type: "boolean",
    description: "Whether cleanup was performed (only when complete)"
  })
  cleanupPerformed?: boolean;

  @ApiPropertyOptional({
    type: "string",
    description: "Error message if processing failed"
  })
  errorMessage?: string;

  @ApiProperty({
    type: "string",
    description: "Timestamp when test was started"
  })
  startedAt: string;

  @ApiPropertyOptional({
    type: "string",
    description: "Timestamp when test completed"
  })
  completedAt?: string;

  @ApiPropertyOptional({
    type: "number",
    description: "Total processing time in milliseconds (only when complete)"
  })
  processingTimeMs?: number;
}
