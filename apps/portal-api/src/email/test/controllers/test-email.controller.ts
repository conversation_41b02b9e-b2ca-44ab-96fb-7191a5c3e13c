import { Body, Controller, Get, Param, Post, UseGuards } from "@nestjs/common";
import {
  ApiBadRequestResponse,
  ApiCreatedResponse,
  ApiExcludeEndpoint,
  ApiOkResponse,
  ApiOperation,
  ApiTags
} from "@nestjs/swagger";
import { AccessTokenGuard, ApiAccessTokenAuthenticated, BadRequestResponseDto, Email } from "nest-modules";
import {
  ManualTestEmailDto,
  AutoTestEmailDto,
  FlexibleTestEmailDto,
  SimulateIncomingTestEmailDto,
  EmailPipelineTestDto,
  EmailPipelineTestResultDto,
  StartEmailPipelineTestDto,
  StartEmailPipelineTestResultDto,
  EmailPipelineTestStatusDto
} from "../dto/test-email.dto";
import { TestEmailService } from "../services/test-email.service";

@ApiTags("Test Emails")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("emails/test")
export class TestEmailController {
  constructor(private readonly testEmailService: TestEmailService) {}

  @ApiOperation({
    summary: "Create test email from manual data",
    description: `
Create a test email using manually provided email data and process it through the email pipeline.
This endpoint bypasses Gmail API calls and allows you to test the email processing system with custom data.

Features:
- Accepts custom email content (from, to, subject, text, html)
- Supports file attachments via base64 encoding
- Triggers full email processing pipeline
- Creates realistic test data for development and testing

Use this endpoint when you need precise control over the email content and attachments.
    `
  })
  @ApiCreatedResponse({
    type: Email,
    description: "Test email created successfully and processing pipeline triggered"
  })
  @ApiBadRequestResponse({
    type: BadRequestResponseDto,
    description: "Invalid email data provided"
  })
  @Post("create-manual")
  async createManualTestEmail(@Body() dto: ManualTestEmailDto): Promise<Email> {
    return await this.testEmailService.createManualTestEmail(dto);
  }

  @ApiOperation({
    summary: "Create test email from auto-generated scenario",
    description: `
Create a test email using auto-generated content based on predefined business scenarios.
This endpoint generates realistic email content and attachments for common use cases.

Available scenarios:
- **shipment-inquiry**: Status update requests with container numbers
- **document-submission**: Commercial invoices and packing lists with attachments
- **customs-urgent**: Urgent customs clearance requests with time-sensitive content
- **status-update**: Shipment routing changes and port notifications

Features:
- Generates realistic email content automatically
- Optional sample attachments (PDFs, CSVs) based on scenario
- Configurable inbox email address
- Triggers full email processing pipeline

Use this endpoint for quick testing with realistic business scenarios.
    `
  })
  @ApiCreatedResponse({
    type: Email,
    description: "Test email created successfully from scenario and processing pipeline triggered"
  })
  @ApiBadRequestResponse({
    type: BadRequestResponseDto,
    description: "Invalid scenario or configuration provided"
  })
  @Post("create-auto")
  async createAutoTestEmail(@Body() dto: AutoTestEmailDto): Promise<Email> {
    return await this.testEmailService.createAutoTestEmail(dto);
  }

  @ApiOperation({
    summary: "Create flexible test email with custom overrides",
    description: `
Create a test email with maximum flexibility - mix custom content with auto-generated content.
This endpoint allows you to override specific parts while using generated content for the rest.

Key Features:
- **Custom Inbox Email**: Set specific inbox addresses (e.g., '<EMAIL>' for DEMO organization)
- **Custom Message Body**: Provide your own text content (HTML version auto-generated)
- **Custom Subject**: Override the subject line
- **Custom Sender**: Specify who the email is from
- **Custom Attachments**: Provide specific files or use auto-generated ones
- **Scenario-Based Defaults**: Any missing content is generated based on the selected scenario

Use Cases:
- Test specific organization processing by setting inbox email
- Test custom message content while keeping realistic attachments
- Mix real business content with generated test data
- Create emails that trigger specific processing paths

**Note**: These endpoints create INCOMING test emails for processing - they do not send outbound emails.
    `
  })
  @ApiCreatedResponse({
    type: Email,
    description: "Flexible test email created successfully with custom overrides applied"
  })
  @ApiBadRequestResponse({
    type: BadRequestResponseDto,
    description: "Invalid configuration or custom content provided"
  })
  @Post("create-flexible")
  async createFlexibleTestEmail(@Body() dto: FlexibleTestEmailDto): Promise<Email> {
    return await this.testEmailService.simulateIncomingTestEmail(dto);
  }

  @ApiOperation({
    summary: "Simulate incoming test email (unified endpoint)",
    description: `
Simulate an incoming test email with maximum flexibility and simplicity.
This unified endpoint combines all test email functionality into one easy-to-use interface.

Key Features:
- **Simple Defaults**: Just specify a scenario (defaults to 'shipment-inquiry') and you're done
- **Custom Content**: Override any part - message body, subject, sender, inbox email
- **Smart Attachments**:
  - undefined = use scenario defaults
  - empty array = no attachments
  - populated array = use provided attachments
- **Organization Testing**: Set inbox email to '<EMAIL>' for DEMO organization
- **Full Pipeline**: Processes through complete email pipeline and sends real response emails

Examples:
- Minimal: \`{"scenario": "shipment-inquiry"}\`
- DEMO org: \`{"scenario": "customs-urgent", "inboxEmail": "<EMAIL>"}\`
- Custom: \`{"messageBody": "Custom message", "attachments": []}\`

**Note**: Creates INCOMING test emails - response emails will be <NAME_EMAIL>
    `
  })
  @ApiCreatedResponse({
    type: Email,
    description: "Incoming test email simulated successfully and processing pipeline triggered"
  })
  @ApiBadRequestResponse({
    type: BadRequestResponseDto,
    description: "Invalid configuration or content provided"
  })
  @Post("simulate-incoming-test-email")
  async simulateIncomingTestEmail(@Body() dto: SimulateIncomingTestEmailDto): Promise<Email> {
    return await this.testEmailService.simulateIncomingTestEmail(dto);
  }

  @ApiOperation({
    summary: "Start async e2e email pipeline test (recommended for long processes)",
    description: `
Start a complete end-to-end test of the email processing pipeline asynchronously.
This endpoint is designed for long-running processes (10-20+ minutes) and returns immediately with a test run ID.

Key Features:
- **Async Processing**: Returns immediately, doesn't wait for completion
- **Long Process Support**: Designed for 10-20+ minute processing times
- **Progress Tracking**: Use test run ID to check progress via status endpoint
- **Automatic Cleanup**: Optionally cleans up all created entities when complete
- **No Timeouts**: Doesn't suffer from HTTP timeout issues

Perfect for:
- **Long Commercial Invoice Aggregation**: Processes that take 10-20+ minutes
- **Production-like Testing**: Test with realistic processing times
- **CI/CD Integration**: Start test, continue with other tasks, check results later
- **Batch Testing**: Start multiple tests and monitor them separately

Example Usage:
- Basic test: \`{"scenario": "document-submission"}\`
- DEMO org test: \`{"scenario": "customs-urgent", "inboxEmail": "<EMAIL>"}\`
- With tracking: \`{"scenario": "shipment-inquiry", "testId": "my-test-123"}\`

**Workflow**:
1. Call this endpoint → get testRunId
2. Use GET /emails/test/pipeline-test-status/{testRunId} to check progress
3. Processing completes automatically with optional cleanup

**Note**: This is a destructive operation when autoCleanup=true. Use only for testing purposes.
    `
  })
  @ApiCreatedResponse({
    type: StartEmailPipelineTestResultDto,
    description: "E2E pipeline test started successfully, use testRunId to track progress"
  })
  @ApiBadRequestResponse({
    type: BadRequestResponseDto,
    description: "Invalid test configuration provided"
  })
  @Post("start-e2e-pipeline-test")
  async startE2EPipelineTest(
    @Body() dto: StartEmailPipelineTestDto
  ): Promise<StartEmailPipelineTestResultDto> {
    return await this.testEmailService.startE2EPipelineTest(dto);
  }

  @ApiOperation({
    summary: "Check status of async e2e pipeline test",
    description: `
Check the current status and progress of an async e2e pipeline test.
Use this endpoint to monitor long-running tests started with start-e2e-pipeline-test.

Returns:
- Current processing status
- Whether test is complete
- Created entities summary (when complete)
- Processing time and cleanup status (when complete)
- Error details (if failed)

**Polling Recommendation**: Check every 30-60 seconds for long processes.
    `
  })
  @ApiOkResponse({
    type: EmailPipelineTestStatusDto,
    description: "Current status of the pipeline test"
  })
  @ApiBadRequestResponse({
    type: BadRequestResponseDto,
    description: "Test run not found"
  })
  @Get("pipeline-test-status/:testRunId")
  async getPipelineTestStatus(@Param("testRunId") testRunId: string): Promise<EmailPipelineTestStatusDto> {
    return await this.testEmailService.getPipelineTestStatus(testRunId);
  }

  @ApiOperation({
    summary: "Run e2e email pipeline test with short timeout (synchronous)",
    description: `
Run a complete end-to-end test of the email processing pipeline synchronously.
This endpoint waits for completion but has a maximum timeout of 5 minutes.

**⚠️ WARNING**: This endpoint is NOT suitable for long processes (10-20+ minutes).
For long processes, use start-e2e-pipeline-test instead.

Use this endpoint only for:
- **Quick Tests**: Processes that complete in under 5 minutes
- **Simple Scenarios**: Basic email processing without heavy aggregation
- **Development**: Quick feedback during development

For production-like testing with long commercial invoice aggregation,
use the async start-e2e-pipeline-test endpoint instead.

**Note**: This is a destructive operation when autoCleanup=true. Use only for testing purposes.
    `
  })
  @ApiCreatedResponse({
    type: EmailPipelineTestResultDto,
    description: "E2E pipeline test completed successfully with results and optional cleanup"
  })
  @ApiBadRequestResponse({
    type: BadRequestResponseDto,
    description: "Invalid test configuration provided or timeout exceeded"
  })
  @Post("run-e2e-pipeline-test-sync")
  async runE2EPipelineTest(@Body() dto: EmailPipelineTestDto): Promise<EmailPipelineTestResultDto> {
    return await this.testEmailService.runE2EPipelineTest(dto);
  }

  @ApiOperation({
    summary: "Clean up email and all related entities",
    description: `
Delete an email and ALL entities created during its processing.
This is a destructive operation that cannot be undone.

**⚠️ WARNING: DESTRUCTIVE OPERATION**
This endpoint will permanently delete:
- The email itself
- All EmailUpdates created from the email
- All shipments created from the email's processing
- All commercial invoices, documents, files, and other related entities
- All email threads associated with the email

Use this endpoint to clean up after testing or to remove problematic emails.

**Correlation Tracking**: Uses EmailUpdate.id correlation to find all entities created from the email's processing pipeline.
    `
  })
  @ApiCreatedResponse({
    description: "Email and all related entities deleted successfully"
  })
  @ApiBadRequestResponse({
    type: BadRequestResponseDto,
    description: "Email not found or deletion failed"
  })
  @Post("cleanup-email/:emailId")
  async cleanupEmail(@Param("emailId") emailId: number): Promise<{ message: string; deletionSummary: any }> {
    return await this.testEmailService.cleanupEmail(emailId);
  }

  @ApiOperation({
    summary: "Manual cleanup after E2E test completion",
    description: `
Clean up test email after E2E pipeline test completes successfully.
This endpoint is designed to be called by scripts after test completion
to avoid race conditions with auto-cleanup during processing.

**Use Case**:
- E2E scripts that disable auto-cleanup and perform manual cleanup
- Ensures all processing (including email responses) completes before cleanup
- Provides detailed logging for test result verification

**Workflow**:
1. Run E2E test with autoCleanup=false
2. Wait for test completion and verify results
3. Call this endpoint to clean up test data
4. Write test results to log files

**Note**: This is a destructive operation that cannot be undone.
    `
  })
  @ApiCreatedResponse({
    description: "Test email cleaned up successfully after completion"
  })
  @ApiBadRequestResponse({
    type: BadRequestResponseDto,
    description: "Email not found or cleanup failed"
  })
  @Post("manual-cleanup-after-completion/:emailId")
  async manualCleanupAfterCompletion(@Param("emailId") emailId: number): Promise<{
    message: string;
    deletionSummary: any;
    cleanupTimestamp: string;
  }> {
    const result = await this.testEmailService.cleanupEmail(emailId);
    return {
      ...result,
      cleanupTimestamp: new Date().toISOString()
    };
  }

  @ApiOperation({
    summary: "Test commercial invoice template generation",
    description: `
Test the commercial invoice template generation to ensure it produces valid content for validation.
This endpoint generates a commercial invoice using the Nunjucks template and returns both the
rendered content and validation information.

Features:
- Generates commercial invoice using current template
- Returns both base64 and plain text versions
- Shows all template variables used
- Validates template rendering process
- Useful for debugging template issues

Use this endpoint to verify that the commercial invoice template generates correctly
and includes all necessary fields for validation.
    `
  })
  @ApiOkResponse({
    description: "Commercial invoice template generated successfully"
  })
  @ApiBadRequestResponse({
    type: BadRequestResponseDto,
    description: "Template generation failed"
  })
  @Get("test-ci-template")
  async testCommercialInvoiceTemplate(): Promise<{
    success: boolean;
    invoiceNumber: string;
    templateVariables: any;
    renderedContentBase64: string;
    renderedContentPlainText: string;
    templateRenderTime: number;
  }> {
    return await this.testEmailService.testCommercialInvoiceTemplate();
  }

  @ApiExcludeEndpoint()
  @Post("e2e-cleanup/:emailId")
  async e2eCleanup(@Param("emailId") emailId: number): Promise<{
    success: boolean;
    message: string;
    deletionSummary?: any;
    cleanupTimestamp: string;
    emailId: number;
  }> {
    try {
      const result = await this.testEmailService.cleanupEmail(emailId);
      return {
        success: true,
        message: result.message,
        deletionSummary: result.deletionSummary,
        cleanupTimestamp: new Date().toISOString(),
        emailId
      };
    } catch (error) {
      return {
        success: false,
        message: `Cleanup failed: ${error.message}`,
        cleanupTimestamp: new Date().toISOString(),
        emailId
      };
    }
  }
}
