import { Injectable, Logger } from "@nestjs/common";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import { OnEvent } from "@nestjs/event-emitter";
import { EmailStatus, FIND_ORGANIZATION_RELATIONS, Organization } from "nest-modules";
import { DataSource } from "typeorm";
import { EmailService } from "../services/email.service";
import { EmailEvent } from "../types/event.types";
import { GetGmailMessageJob } from "../types/queue.types";
import { generateRequest } from "../utils/generate-request";

@Injectable()
export class QueueListener {
  constructor(
    private readonly moduleRef: ModuleRef,
    private readonly dataSource: DataSource
  ) {}
  private readonly logger = new Logger(QueueListener.name);

  private async getUnscopedServices() {
    const contextId = ContextIdFactory.create();
    const emailService = await this.moduleRef.resolve(EmailService, contextId);
    await new Promise((resolve) => process.nextTick(resolve)); // Wait for children dependencies to be initialized

    return {
      emailService
    };
  }

  private async getScopedServices(organizationId: number) {
    const organization = await this.dataSource.manager.findOne(Organization, {
      where: { id: organizationId },
      relations: FIND_ORGANIZATION_RELATIONS
    });
    if (!organization) {
      this.logger.error(`Organization not found, skipping...`);
      return {};
    }

    // Create scoped services
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(generateRequest(null, organization), contextId);
    const emailService = await this.moduleRef.resolve(EmailService, contextId);
    await new Promise((resolve) => process.nextTick(resolve)); // Wait for children dependencies to be initialized

    return {
      organization,
      emailService,
      contextId
    };
  }

  @OnEvent(EmailEvent.GET_GMAIL_MESSAGE_FAILED)
  async onGetGmailMessageFailed(job: GetGmailMessageJob, error: Error) {
    const { email: inboxEmail, gmailId } = job?.data ?? {};
    this.logger.error(
      `Get Gmail Message Failed. Inbox Email: ${inboxEmail}, Gmail ID: ${gmailId}, error: ${error.message || "Unknown Error"}`
    );

    if (!inboxEmail || !gmailId) {
      this.logger.error(`Inbox Email or Gmail ID not found, skipping...`);
      return;
    }

    const { emailService: unscopedEmailService } = await this.getUnscopedServices();
    const emailRecord = await unscopedEmailService.getEmailByGmailId(gmailId, null, true);
    this.logger.debug(`Email Record: ${emailRecord?.id}`);
    if (!emailRecord) {
      this.logger.error(`Email record not found, skipping...`);
      return;
    }

    const { emailService } = await this.getScopedServices(emailRecord?.organization?.id);
    if (!emailService) {
      this.logger.error(`Scoped email service not returned, skipping...`);
      return;
    }

    const updatedEmailRecord = await emailService.editEmail(emailRecord?.id, {
      status: EmailStatus.FAILED_SAVING_EMAIL,
      error: error.message || "Unknown error when getting Gmail message"
    });
    this.logger.debug(
      `Updated Email Record: ${updatedEmailRecord?.id}, status: ${updatedEmailRecord?.status}, error: ${updatedEmailRecord?.error}`
    );

    const repliedEmail = await emailService.replyFailedEmail(emailRecord?.id);
    this.logger.debug(`Replied Email: ${repliedEmail?.id}`);
  }
}
