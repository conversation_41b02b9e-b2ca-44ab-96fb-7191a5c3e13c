import { InjectQueue } from "@nestjs/bullmq";
import { Inject, Injectable, Lo<PERSON>, Scope } from "@nestjs/common";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import { EventEmitter2, OnEvent } from "@nestjs/event-emitter";
import { InjectRepository } from "@nestjs/typeorm";
import {
  Email,
  EmailOrigin,
  EmailStatus,
  FIND_ORGANIZATION_RELATIONS,
  GmailToken,
  Organization
} from "nest-modules";
import { DataSource, In, Repository } from "typeorm";
import {
  EmailAggregatedEvent,
  EmailIntentsExtractedEvent,
  EmailIntentsProcessedEvent,
  EmailManualReviewRequiredEvent,
  ThreadShipmentFoundEvent,
  ThreadShipmentNotFoundEvent,
  ThreadTooManyShipmentsFoundEvent
} from "../dto/event.dto";

import { EmailService } from "../services/email.service";
import { GmailService } from "../services/gmail.service";
import { EmailManualReviewReason } from "../types/email.types";
import { EmailEvent } from "../types/event.types";
import { GmailHistoryType } from "../types/gmail.types";
import { EmailQueueName, GetGmailMessageQueue } from "../types/queue.types";
import { generateRequest } from "../utils/generate-request";
import moment from "moment-timezone";

@Injectable({ scope: Scope.REQUEST })
export class EmailListener {
  constructor(
    @InjectRepository(GmailToken)
    private readonly gmailTokenRepository: Repository<GmailToken>,
    @InjectQueue(EmailQueueName.GET_GMAIL_MESSAGE)
    private readonly getGmailMessageQueue: GetGmailMessageQueue,
    @Inject(EmailService)
    private readonly emailService: EmailService,
    @Inject(GmailService)
    private readonly gmailService: GmailService,
    private readonly moduleRef: ModuleRef,
    private readonly dataSource: DataSource,
    private readonly eventEmitter: EventEmitter2
  ) {}
  private readonly logger = new Logger(EmailListener.name);

  private async getScopedServices(organizationId: number) {
    const organization = await this.dataSource.manager.findOne(Organization, {
      where: { id: organizationId },
      relations: FIND_ORGANIZATION_RELATIONS
    });
    if (!organization) {
      this.logger.error(`Organization not found, skipping...`);
      return {};
    }

    // Create scoped services
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(generateRequest(null, organization), contextId);
    const emailService = await this.moduleRef.resolve(EmailService, contextId);
    await new Promise((resolve) => process.nextTick(resolve)); // Wait for children dependencies to be initialized

    return {
      organization,
      emailService,
      contextId
    };
  }

  private async getCompletedShipmentSearchEmails(threadId: string, organizationId: number) {
    const { organization, emailService, contextId } = await this.getScopedServices(organizationId);

    const targetEmails = await emailService.getEmailsByThreadId(
      threadId,
      null,
      EmailStatus.COMPLETED_SHIPMENT_SEARCH
    );
    this.logger.log(`Target Emails: ${JSON.stringify(targetEmails?.map((email) => email.id))}`);
    return {
      emails: targetEmails,
      emailService,
      organization,
      contextId
    };
  }

  @OnEvent(EmailEvent.EMAIL_SYNC)
  async onEmailSync() {
    this.logger.log(`Email sync event received`);

    const defaultGmailToken = await this.gmailTokenRepository.findOneBy({
      isDefaultMailbox: true
    });
    if (!defaultGmailToken) {
      this.logger.error(`No default Gmail token found, stopping...`);
      return;
    }

    // Check if the mailbox is currently rate-limited for read operations
    if (defaultGmailToken.rateLimitReadUntil && new Date() < defaultGmailToken.rateLimitReadUntil) {
      this.logger.warn(
        `Email sync for ${defaultGmailToken.email} skipped due to active read rate limit. Will retry after ${defaultGmailToken.rateLimitReadUntil.toISOString()}.`
      );
      return; // Exit early, do not proceed with sync
    }

    this.logger.log(
      `Default Gmail token: ${defaultGmailToken.email}, Last synced history ID: ${defaultGmailToken.lastSyncedHistoryId}`
    );

    const addedMessageIds: Array<string> = [];
    const deletedMessageIds: Array<string> = [];
    let latestHistoryId: string | null = null;
    // Check if lastSyncedHistoryId is a non-empty string of digits
    const hasValidLastSyncedHistoryId =
      typeof defaultGmailToken.lastSyncedHistoryId === "string" &&
      defaultGmailToken.lastSyncedHistoryId.length > 0 &&
      /^\d+$/.test(defaultGmailToken.lastSyncedHistoryId);

    let requireFullSync = !hasValidLastSyncedHistoryId;

    if (hasValidLastSyncedHistoryId) {
      this.logger.log(
        `Last synced history ID found (${defaultGmailToken.lastSyncedHistoryId}), performing partial sync...`
      );
      try {
        // Call list histories
        const listHistoriesRes = await this.gmailService.listHistories(
          defaultGmailToken.email,
          defaultGmailToken.lastSyncedHistoryId, // Pass as string, which is expected
          null,
          [GmailHistoryType.MESSAGE_ADDED, GmailHistoryType.MESSAGE_DELETED]
        );

        // Filter message IDs from histories into added or deleted messages
        for (const history of listHistoriesRes?.histories || []) {
          addedMessageIds.push(
            ...(history?.messagesAdded || []).map((addHistory) => addHistory?.message?.id)
          );
          deletedMessageIds.push(
            ...(history?.messagesDeleted || []).map((deletedHistory) => deletedHistory?.message?.id)
          );
        }

        // Extract latest history ID in the inbox
        latestHistoryId =
          typeof listHistoriesRes?.latestHistoryId === "string" &&
          /^\d+$/.test(listHistoriesRes?.latestHistoryId)
            ? listHistoriesRes?.latestHistoryId
            : null;
      } catch (error) {
        this.logger.error(
          `Error while listing Gmail histories for email sync: ${error instanceof Error ? error.message : String(error)}`
        );
        this.logger.warn(`List histories failed, setting require full sync to true...`);
        requireFullSync = true;
      }
    }

    if (requireFullSync) {
      this.logger.log(`Full sync required, performing full sync...`);

      // Get latest inbox email from database
      const latestInboxEmail = await this.emailService.getLatestEmail(
        defaultGmailToken.email,
        EmailOrigin.INBOX
      );
      this.logger.log(
        `Latest inbox email: ${latestInboxEmail?.id}, Receive Date: ${latestInboxEmail?.receiveDate}, History ID: ${latestInboxEmail?.historyId}`
      );

      // Call list messages
      const listMessagesRes = await this.gmailService.listMessages(
        defaultGmailToken.email,
        [
          "label:inbox",
          latestInboxEmail?.receiveDate instanceof Date
            ? `after:${moment.tz(latestInboxEmail?.receiveDate, "America/Toronto").format("YYYY/MM/DD")}`
            : ""
        ]
          .filter((q) => typeof q === "string" && q.length > 0)
          .join(" "),
        500
      );

      // Append message IDs to added messages
      addedMessageIds.push(...(listMessagesRes?.messages || []).map((message) => message.id));

      // For full sync, we don't need to get history ID from first message
      // Individual message processors will handle history ID updates with each message's own history ID
      this.logger.log(
        `Full sync: Found ${addedMessageIds.length} messages to process. History ID will be updated by individual processors.`
      );
    }

    // Filter out added message IDs that are already deleted
    const filteredMessageIds = addedMessageIds.filter((id) => !deletedMessageIds.includes(id));

    // Set status to deleted for deleted message IDs
    // Dispatch get gmail message jobs
    this.logger.log(
      `Latest History ID: ${latestHistoryId}, Filtered Message IDs Count: ${filteredMessageIds.length}, Deleted Message IDs Count: ${deletedMessageIds.length}`
    );

    // Only update history ID for partial sync - full sync will be handled by individual message processors
    if (!requireFullSync && typeof latestHistoryId === "string" && /^\d+$/.test(latestHistoryId)) {
      const updateHistoryIdResult = await this.dataSource.manager.update(
        GmailToken,
        { email: defaultGmailToken.email },
        { lastSyncedHistoryId: latestHistoryId }
      );
      this.logger.debug(
        `Updated ${updateHistoryIdResult.affected} Gmail tokens to last synced history ID ${latestHistoryId}`
      );
    } else if (requireFullSync) {
      this.logger.log(`Full sync in progress - history ID will be updated by individual message processors`);
    }

    if (deletedMessageIds.length > 0) {
      const updateResult = await this.dataSource.manager.update(
        Email,
        { gmailId: In(deletedMessageIds) },
        { status: EmailStatus.DELETED }
      );
      this.logger.debug(`Updated ${updateResult.affected} emails to deleted`);
    }
    if (filteredMessageIds.length > 0)
      await this.getGmailMessageQueue.addBulk(
        filteredMessageIds.map((messageId) => ({
          name: messageId, // Use messageId as job name as this would not add duplicated jobs for getting the same message
          data: {
            email: defaultGmailToken.email,
            gmailId: messageId,
            // For full sync, pass null so individual processors handle history ID updates
            // For partial sync, pass the latestHistoryId from the sync operation
            latestHistoryId: requireFullSync ? null : latestHistoryId
          }
        }))
      );
  }

  // NOTE: EMAIL_SAVED event handling has been moved to EmailSagaCoordinator
  // The saga coordinator now handles the complete email processing flow using FlowProducer

  // NOTE: THREAD_SHIPMENT_FOUND event handling has been moved to EmailSagaCoordinator
  // The saga coordinator now handles the complete email processing flow using FlowProducer

  // NOTE: THREAD_SHIPMENT_NOT_FOUND event handling has been moved to EmailSagaCoordinator
  // The saga coordinator now handles the complete email processing flow using FlowProducer

  @OnEvent(EmailEvent.THREAD_TOO_MANY_SHIPMENTS_FOUND)
  async onThreadTooManyShipmentsFound(event: ThreadTooManyShipmentsFoundEvent) {
    const { threadId, organizationId } = event;
    this.logger.log(
      `Thread too many shipments found event received. Thread ID: ${threadId}, Organization ID: ${organizationId}`
    );

    const { emails: pendingEmails, emailService } = await this.getCompletedShipmentSearchEmails(
      threadId,
      organizationId
    );
    if (pendingEmails?.length <= 0) {
      this.logger.error(`No pending emails found, skipping...`);
      return;
    }

    // Send email for manual review
    for (const email of pendingEmails) {
      const updatedEmail = await emailService.editEmail(email.id, {
        status: EmailStatus.MANUAL_REVIEW,
        error: "Too many shipments found"
      });
      this.logger.log(`Updated email: ${updatedEmail?.id}, status: ${updatedEmail?.status}`);
      this.eventEmitter.emit(
        EmailEvent.EMAIL_MANUAL_REVIEW_REQUIRED,
        new EmailManualReviewRequiredEvent({
          emailId: updatedEmail?.id,
          organizationId: updatedEmail?.organization?.id,
          reviewReason: EmailManualReviewReason.TOO_MANY_SHIPMENTS_FOUND
        })
      );
    }
  }

  @OnEvent(EmailEvent.EMAIL_MANUAL_REVIEW_REQUIRED)
  async onEmailManualReviewRequired(event: EmailManualReviewRequiredEvent) {
    const { emailId, organizationId, reviewReason } = event;
    this.logger.log(
      `Email manual review required event received. Email ID: ${emailId}, Organization ID: ${organizationId}, Review Reason: ${reviewReason}`
    );

    const { emailService } = await this.getScopedServices(organizationId);
    if (!emailService) {
      this.logger.error(`Scoped email service not returned, skipping manual review for email ${emailId}...`);
      return;
    }

    const email = await emailService.getEmailById(emailId);
    this.logger.debug(`Email: ${email?.id}`);
    if (!email) {
      this.logger.error(`Email ${emailId} not found, skipping manual review...`);
      return;
    }
    if (email?.organization?.id !== organizationId) {
      this.logger.error(
        `Email ${emailId} organization ID does not match the job organization ID, skipping manual review...`
      );
      return;
    }
    if (email.status !== EmailStatus.MANUAL_REVIEW) {
      this.logger.error(
        `Email ${emailId} status is not ${EmailStatus.MANUAL_REVIEW}, skipping manual review...`
      );
      return;
    }

    try {
      const repliedEmail = await emailService.replyFailedEmail(email.id);
      if (repliedEmail?.id) {
        this.logger.log(
          `✅ Automated manual review response sent successfully. Email ID: ${emailId}, Reply ID: ${repliedEmail.id}, Review Reason: ${reviewReason}`
        );
      } else {
        this.logger.warn(
          `⚠️ Failed to send automated manual review response for email ${emailId}. Reply email not created.`
        );
      }
      this.logger.debug(`Replied Email: ${repliedEmail?.id}`);
    } catch (error) {
      this.logger.error(
        `❌ Error sending automated manual review response for email ${emailId}: ${error.message}`,
        error.stack
      );
    }
  }

  // NOTE: EMAIL_INTENTS_EXTRACTED event handling has been moved to EmailSagaCoordinator
  // The saga coordinator now handles the complete email processing flow using FlowProducer

  // NOTE: EMAIL_INTENTS_PROCESSED event handling has been moved to EmailSagaCoordinator
  // The saga coordinator now handles the complete email processing flow using FlowProducer

  // NOTE: EMAIL_AGGREGATED event handling has been moved to EmailSagaCoordinator
  // The saga coordinator now handles the complete email processing flow using FlowProducer
}
