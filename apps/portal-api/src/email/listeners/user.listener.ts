import { Inject, Injectable, Logger, NotFoundException } from "@nestjs/common";
import { EmailService } from "../services/email.service";
import { InjectDataSource } from "@nestjs/typeorm";
import { ModuleRef } from "@nestjs/core";
import { DataSource } from "typeorm";
import { OnEvent } from "@nestjs/event-emitter";
import {
  UserEvent,
  UserService,
  PasswordService,
  User,
  FIND_USER_RELATIONS,
  Importer,
  FIND_IMPORTER_RELATIONS
} from "nest-modules";
import { EmailTemplateName } from "../types/email.types";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class UserListener {
  constructor(
    @Inject(EmailService)
    private readonly emailService: EmailService,
    @Inject(UserService)
    private readonly userService: UserService,
    @Inject(PasswordService)
    private readonly passwordService: PasswordService,
    @Inject(ConfigService)
    private readonly configService: ConfigService,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}
  private readonly logger = new Logger(UserListener.name);

  private get PORTAL_FRONTEND_URL() {
    return this.configService.get<string>("PORTAL_FRONTEND_URL");
  }

  private get SYSTEM_FROM_EMAIL() {
    return this.configService.get<string>("SYSTEM_FROM_EMAIL");
  }

  @OnEvent(UserEvent.SEND_RESET_PASSWORD_EMAIL)
  async onSendResetPasswordEmail(event: { email: string }) {
    const { email: emailAddress } = event;
    this.logger.log(`Sending reset password email to ${emailAddress}...`);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const user = await queryRunner.manager.findOne(User, {
        where: { email: emailAddress },
        relations: FIND_USER_RELATIONS
      });
      this.logger.log(`Reset Password User: ${user?.id}`);
      if (!user) throw new NotFoundException(`User with email ${emailAddress} not found`);

      const importer = await queryRunner.manager.findOne(Importer, {
        where: { organization: { id: user?.organization?.id || -1 } },
        relations: FIND_IMPORTER_RELATIONS
      });
      this.logger.log(
        `User orgnaization's importer: ${importer?.id}, receive email: ${importer?.receiveEmail}`
      );

      const { token, resetPasswordTokenRecord } = await this.passwordService.generateResetPasswordToken(
        user,
        queryRunner
      );
      this.logger.log(
        `Reset Password Token for user ${user?.id}: ${resetPasswordTokenRecord.id}. Expires at: ${resetPasswordTokenRecord.expiryDate}`
      );

      const { subject, body } = this.emailService.renderEmailTemplateWithSubject(
        EmailTemplateName.RESET_PASSWORD_EMAIL,
        {
          type: "reset",
          userName: user.name,
          resetPasswordUrl: `${this.PORTAL_FRONTEND_URL}/create-password?token=${token}`
        }
      );
      this.logger.debug(`Subject: ${subject}`);
      this.logger.debug(`Body: ${body}`);

      const email = await this.emailService.sendEmail(
        {
          from: this.SYSTEM_FROM_EMAIL,
          to: [emailAddress],
          replyTo: importer?.receiveEmail,
          subject,
          html: body
        },
        queryRunner,
        user.organization
      );
      this.logger.log(`Reset password email ${email?.id} sent to ${emailAddress}`);

      await queryRunner.commitTransaction();
    } catch (error) {
      this.logger.error(
        `Got error while sending reset password email: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
    } finally {
      await queryRunner.release();
    }
  }
}
