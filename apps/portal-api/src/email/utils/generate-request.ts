import { AuthenticatedRequest, Organization, UserPermission } from "nest-modules";

export function generateRequest(
  currentRequest: AuthenticatedRequest | null,
  organization: Organization,
  permission = UserPermission.BACKOFFICE_ADMIN
) {
  if (currentRequest?.user?.organization?.id) return currentRequest;
  else
    return {
      user: {
        permission,
        organization
      }
    };
}
