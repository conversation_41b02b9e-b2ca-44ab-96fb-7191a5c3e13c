import { DataSource, QueryRunner } from "typeorm";

export async function runTransaction<ReturnType>(
  dataSource: DataSource,
  func: (queryRunner: QueryRunner, ...args: Array<any>) => Promise<ReturnType>,
  ...args: Array<any>
): Promise<ReturnType> {
  const queryRunner = dataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    const result = await func(queryRunner, ...args);
    await queryRunner.commitTransaction();
    return result;
  } catch (error) {
    if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
    throw error;
  } finally {
    await queryRunner.release();
  }
}
