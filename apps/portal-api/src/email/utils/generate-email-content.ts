import { DocumentFieldDataType, Email, File } from "nest-modules";
import { EmailContent } from "../schemas/email-content.schema";

export function generateEmailContent(
  currentEmail: Email,
  previousEmails: Array<Email>,
  attachments: Array<File>
): EmailContent {
  return {
    fromAddresses: (currentEmail?.from || []).map(({ address, name }) =>
      typeof name === "string" && name.length > 0 ? `${name} <${address}>` : address
    ),
    subject: currentEmail?.subject || null,
    text: currentEmail?.text || currentEmail?.html || null,
    emailHistories: previousEmails.map(
      (email) =>
        `${email?.requestSummary || "No request summary saved for this email"}\n${email?.processingOutcome || "No processing outcome saved for this email"}`
    ),
    attachments: attachments.reduce(
      (att, file) => {
        att.push(
          ...file.documents.map((document) => ({
            id: document.id,
            filename: file.name,
            documentType: document.documentType?.name,
            extractedData: document.fields.reduce(
              (extractedData, field) => {
                switch (field.dataType) {
                  case DocumentFieldDataType.ARRAY:
                  case DocumentFieldDataType.OBJECT:
                    extractedData[field.name] = JSON.parse(field.value);
                    break;
                  case DocumentFieldDataType.BOOLEAN:
                    extractedData[field.name] = field.value.trim().toLowerCase() === "true";
                    break;
                  case DocumentFieldDataType.NUMBER:
                    extractedData[field.name] = Number(field.value);
                    break;
                  case DocumentFieldDataType.DATETIME:
                    extractedData[field.name] = new Date(field.value);
                    break;
                  default:
                    extractedData[field.name] = field.value;
                    break;
                }
                return extractedData;
              },
              {} as Record<string, any>
            )
          }))
        );
        return att;
      },
      [] as Array<{
        filename: string;
        documentType: string;
        extractedData: any;
      }>
    )
  };
}
