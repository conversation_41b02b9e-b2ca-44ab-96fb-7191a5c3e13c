import z from "zod";

export const EmailAttachmentSchema = z
  .object({
    id: z.number().int().describe("Database ID of the attachment"),
    filename: z.string().describe("Name of the attachment file"),
    documentType: z.string().describe("Type of the extracted document"),
    extractedData: z.any().nullable().describe("Extracted data from the attachment")
  })
  .strict();

export const EmailContentSchema = z
  .object({
    fromAddresses: z.array(z.string()).describe("List of sender email addresses and names"),
    subject: z.string().nullable().describe("Subject of the email"),
    text: z.string().nullable().describe("Text content of the email"),
    emailHistories: z.array(z.string()).optional().describe("Previous email exchanges in the thread"),
    attachments: z.array(EmailAttachmentSchema).optional().describe("List of attachments in the email")
  })
  .strict();

export type EmailContent = z.infer<typeof EmailContentSchema>;
export type EmailAttachment = z.infer<typeof EmailAttachmentSchema>;
