import z from "zod";
import { DOCUMENT_TYPES, EMAIL_INTENTS } from "nest-modules";

// Local shipment identifier schema to avoid core-agent dependency
const SHIPMENT_IDENTIFIER_TYPES = ["HBL", "CCN", "PARS"] as const;

const ShipmentIdentifierSchema = z.object({
  type: z
    .enum(SHIPMENT_IDENTIFIER_TYPES)
    .describe(
      "Type of identifier found - House Bill of Lading (HBL), Cargo Control Number (CCN), or PARS number"
    ),
  value: z.string().describe("The actual identifier value found in the text"),
  context: z.string().describe("Quote the relevant part of the email text where this identifier was found")
});

// The core schema that represents a single intent extracted from an email
export const EmailActionSchema = z.object({
  // High-level business operation being requested
  intent: z.enum(EMAIL_INTENTS).describe(`
    The user-requested action or operation for this entry.
    Each email can contain multiple distinct intents, each with its own instructions.
    Intents can be determined from both email text and document presence.
      - CREATE_SHIPMENT: New customs clearance request or initial documentation submission
      - UPDATE_SHIPMENT: Modify existing shipment details or customs documentation
      - CREATE_COMMERCIAL_INVOICE: Submit new commercial invoice for customs valuation
      - UPDATE_COMMERCIAL_INVOICE: Modify existing commercial invoice
      - CREATE_CERTIFICATE_OF_ORIGIN: Submit origin documentation for duties/preferential treatment
      - GET_SHIPMENT_STATUS: Check customs processing or clearance status
      - UNKNOWN: Intent unclear, this email will be flagged for human review
      - SPAM: Conclusively identified as spam/phishing
  `),

  shipmentReference: ShipmentIdentifierSchema.optional().describe(
    "Identifier referencing the shipment involved in this request"
  ),

  instructions: z.array(z.string()).describe(`
    A list of instructions or requests explicitly provided in the email text for this particular intent.
    Each entry represents a discrete directive—for example, "Change the ETD to January 20" or "Add a container with 100 crates." Instructions should only be created from email text content, not from document contents or assumptions. Empty array is valid when intent is determined by document presence alone.
  `),
  shippingData: z.string().optional().describe(`
      A stringified JSON object representing the structured shipping data extracted and normalized from the instructions that is being referenced by the intent.
      This represents the actual data changes being requested or new data being submitted.
    `),
  attachments: z
    .array(
      z.object({
        id: z.number().int().describe("Database ID of the attachment"),
        filename: z.string().describe("Filename of the attachment, including extension"),
        documentType: z
          .enum(DOCUMENT_TYPES)
          .describe("The specific shipping document type this file represents"),
        reason: z.enum(["NEW", "REPLACE"]).describe(`
          Indicates why this attachment is included:
          - NEW: A new document submitted for this request
          - REPLACE: A revision or update to a previously submitted document
        `)
      })
    )
    .optional().describe(`
  A list of file attachments relevant to this intent.
  Each attachment represents a distinct shipping-related document 
  that the user has provided or updated (e.g., Bill of Lading, Certificate of Origin).
  Document contents will be automatically processed by the system - do not create
  instructions based on document contents.
  Use "NEW" to indicate a brand-new document submission
  and "REPLACE" to signal that this file supersedes an existing version.`)
});

// Schema for all intents found in a single email
export const EmailIntentsSchema = z.object({
  intents: z.array(EmailActionSchema).describe(`
  An array of all discrete actions (intents) parsed from the email. Each
  entry represents a single requested operation (e.g., CREATE_SHIPMENT, 
  UPDATE_COMMERCIAL_INVOICE) determined from email text and/or document presence.
  Multiple intents can exist for the same shipment, and the same intent type
  can appear multiple times for different shipments.`)
});

// Export the TypeScript type for use in other parts of the application
export type EmailIntents = z.infer<typeof EmailIntentsSchema>;
export type EmailAction = z.infer<typeof EmailActionSchema>;
