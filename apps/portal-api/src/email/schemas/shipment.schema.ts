import z from "zod";
import { LocationSchema } from "./location.schema";
import { TradePartnerSchema } from "./trade-partner.schema";

// Type definitions for common field types
const DateString = z.string().nullable().describe("ISO 8601 formatted date string");
const StringOrNull = z.union([z.string(), z.null()]).describe("String or null value");

// Constants for enums to keep schema cleaner
export const SHIPMENT_STATUS = [
  "new",
  "in-transit",
  "at-port",
  "on-rail",
  "at-terminal",
  "picked-up",
  "completed"
] as const;

export const CUSTOMS_STATUS = [
  "pending-commercial-invoice",
  "pending-confirmation",
  "pending-arrival",
  "live",
  "entry-submitted",
  "entry-accepted",
  "exam",
  "released",
  "accounting-completed"
] as const;

export const TRACKING_STATUS = ["tracking", "online", "offline", "error"] as const;

export const NON_COMPLIANT_REASONS = [
  "missing-ogd-filing",
  "missing-sima-filing",
  "missing-sima-code"
] as const;

export const DESTINATION_PROVINCES = [
  "AB",
  "BC",
  "MB",
  "NB",
  "NL",
  "NS",
  "NT",
  "NU",
  "ON",
  "PE",
  "QC",
  "SK",
  "YT"
] as const;

const TRANSPORT_MODES = ["air", "ocean-fcl", "ocean-lcl", "land", "small-package"] as const;

const CONTAINER_TYPES = ["fcl-20gp", "fcl-40gp", "fcl-40hq", "fcl-45hq", "fcl-40rh"] as const;

const VOLUME_UNITS = ["cbm", "cft"] as const;

const WEIGHT_UNITS = [
  "ctm",
  "dtn",
  "grm",
  "hgm",
  "kgm",
  "kns",
  "ksd",
  "ktn",
  "lbr",
  "mgm",
  "odg",
  "odk",
  "odm",
  "tne",
  "tsd"
] as const;

const QUANTITY_UNITS = ["box", "pk", "pal", "ctn", "bg", "cs", "rl", "btl", "drm", "pkg"] as const;

export const PACKAGE_UOM = [
  "ae",
  "am",
  "ap",
  "at",
  "bg",
  "bl",
  "bn",
  "bf",
  "bp",
  "br",
  "ba",
  "bz",
  "bk",
  "cb",
  "bi",
  "bd",
  "by",
  "bb",
  "bt",
  "bo",
  "bs",
  "bq",
  "bv",
  "bc",
  "bx",
  "bj",
  "vq",
  "vg",
  "vl",
  "vy",
  "vr",
  "vo",
  "bh",
  "be",
  "bu",
  "cg",
  "ca",
  "cx",
  "ci",
  "cz",
  "co",
  "cp",
  "ct",
  "cs",
  "ck",
  "ch",
  "cc",
  "cf",
  "cj",
  "cl",
  "cv",
  "cr",
  "ce",
  "cu",
  "cy",
  "dj",
  "dp",
  "dr",
  "en",
  "fp",
  "fi",
  "fl",
  "fo",
  "fr",
  "fd",
  "fc",
  "gb",
  "gi",
  "gz",
  "hr",
  "hg",
  "in",
  "iz",
  "jr",
  "jc",
  "jy",
  "jg",
  "jt",
  "kg",
  "lg",
  "lz",
  "mc",
  "mb",
  "ms",
  "mt",
  "mx",
  "ns",
  "nt",
  "pk",
  "pa",
  "pl",
  "pc",
  "pi",
  "pz",
  "ph",
  "pn",
  "pg",
  "py",
  "pt",
  "po",
  "pf",
  "rt",
  "rl",
  "rg",
  "rd",
  "rz",
  "sh",
  "sa",
  "se",
  "sc",
  "st",
  "sm",
  "sz",
  "sw",
  "sk",
  "sv",
  "sl",
  "sd",
  "su",
  "tk",
  "ty",
  "tc",
  "tn",
  "pu",
  "tr",
  "ts",
  "tb",
  "tu",
  "td",
  "tz",
  "to",
  "ne",
  "vp",
  "va",
  "vi",
  "wb"
] as const;

export const CURRENCY_CODES = [
  "aed",
  "afn",
  "all",
  "amd",
  "ang",
  "aoa",
  "ars",
  "aud",
  "awg",
  "azn",
  "bam",
  "bbd",
  "bdt",
  "bgn",
  "bhd",
  "bif",
  "bmd",
  "bnd",
  "bob",
  "bov",
  "brl",
  "bsd",
  "btn",
  "bwp",
  "byn",
  "bzd",
  "cad",
  "cdf",
  "che",
  "chf",
  "chw",
  "clf",
  "clp",
  "cny",
  "cop",
  "cou",
  "crc",
  "cuc",
  "cup",
  "cve",
  "czk",
  "djf",
  "dkk",
  "dop",
  "dzd",
  "egp",
  "ern",
  "etb",
  "eur",
  "fjd",
  "fkp",
  "gbp",
  "gel",
  "ghs",
  "gip",
  "gmd",
  "gnf",
  "gtq",
  "gyd",
  "hkd",
  "hnl",
  "hrk",
  "htg",
  "huf",
  "idr",
  "ils",
  "inr",
  "iqd",
  "irr",
  "isk",
  "jmd",
  "jod",
  "jpy",
  "kes",
  "kgs",
  "khr",
  "kmf",
  "kpw",
  "krw",
  "kwd",
  "kyd",
  "kzt",
  "lak",
  "lbp",
  "lkr",
  "lrd",
  "lsl",
  "lyd",
  "mad",
  "mdl",
  "mga",
  "mkd",
  "mmk",
  "mnt",
  "mop",
  "mro",
  "mur",
  "mvr",
  "mwk",
  "mxn",
  "mxv",
  "myr",
  "mzn",
  "nad",
  "ngn",
  "nio",
  "nok",
  "npr",
  "nzd",
  "omr",
  "pab",
  "pen",
  "pgk",
  "php",
  "pkr",
  "pln",
  "pyg",
  "qar",
  "ron",
  "rsd",
  "rub",
  "rwf",
  "sar",
  "sbd",
  "scr",
  "sdg",
  "sek",
  "sgd",
  "shp",
  "sll",
  "sos",
  "srd",
  "ssp",
  "stn",
  "svc",
  "syp",
  "szl",
  "thb",
  "tjs",
  "tmt",
  "tnd",
  "top",
  "try",
  "ttd",
  "twd",
  "tzs",
  "uah",
  "ugx",
  "usd",
  "usn",
  "uyi",
  "uyu",
  "uzs",
  "vef",
  "vnd",
  "vuv",
  "wst",
  "xaf",
  "xag",
  "xau",
  "xba",
  "xbb",
  "xbc",
  "xbd",
  "xcd",
  "xdr",
  "xof",
  "xpd",
  "xpf",
  "xpt",
  "xsu",
  "xts",
  "xua",
  "xxx",
  "yer",
  "zar",
  "zmw",
  "zwl"
] as const;

// Define Commercial Invoice Line Schema
export const CommercialInvoiceLineSchema = z
  .object({
    candataId: z.string().optional().describe("Candata identifier"),
    hsCode: z.string().optional().describe("Harmonized System code"),
    goodsDescription: z.string().optional().describe("Description of the goods"),
    quantity: z.number().optional().describe("Quantity of items"),
    unitOfMeasure: z.enum(PACKAGE_UOM).optional().describe("Unit of measure for the items"),
    unitPrice: z.number().optional().describe("Price per unit"),
    totalLineValue: z.number().optional().describe("Total value for this line"),
    totalLineValueCad: z.number().optional().describe("Total value in CAD"),
    timeLimitType: z.string().optional().describe("Type of time limit"),
    timeLimitStartDate: DateString.optional().describe("Start date of time limit"),
    timeLimitEndDate: DateString.optional().describe("End date of time limit"),
    authorityPermit: z.string().optional().describe("Authority permit reference"),
    dutiesReliefLicence: z.string().optional().describe("Duties relief licence reference"),
    surtaxSubjectCode: z.string().optional().describe("Surtax subject code"),
    surtaxCode: z.string().optional().describe("Surtax code"),
    safeguardSubjectCode: z.string().optional().describe("Safeguard subject code"),
    safeguardCode: z.string().optional().describe("Safeguard code"),
    simaQuantity: z.number().optional().describe("SIMA quantity"),
    simaUnitOfMeasure: z.enum(PACKAGE_UOM).optional().describe("SIMA unit of measure"),
    provincialAlcoholTax: z.number().optional().describe("Provincial alcohol tax amount"),
    provincialSalesTax: z.number().optional().describe("Provincial sales tax amount"),
    provincialTobaccoTax: z.number().optional().describe("Provincial tobacco tax amount"),
    provincialCannabisExciseDuty: z.number().optional().describe("Provincial cannabis excise duty amount"),
    alcoholPercentage: z.number().optional().describe("Alcohol percentage"),
    pstHst: z.number().optional().describe("PST/HST amount"),
    destinationProvince: z.enum(DESTINATION_PROVINCES).optional().describe("Destination province code"),
    cadCalculationDate: DateString.optional().describe("CAD calculation date"),
    valueForDuty: z.number().optional().describe("Value for duty calculation"),
    antiDumping: z.number().optional().describe("Anti-dumping duty amount"),
    countervailing: z.number().optional().describe("Countervailing duty amount"),
    customsDuties: z.number().optional().describe("Customs duties amount"),
    exciseDuties: z.number().optional().describe("Excise duties amount"),
    exciseTax: z.number().optional().describe("Excise tax amount"),
    gst: z.number().optional().describe("GST amount"),
    safeguard: z.number().optional().describe("Safeguard amount"),
    surtax: z.number().optional().describe("Surtax amount"),
    totalDutiesAndTaxes: z.number().optional().describe("Total duties and taxes amount"),
    additionalInfo: z.string().optional().describe("Additional line item information")
  })
  .strict();

export const ShippingDataSchema = z
  .object({
    // Required core fields (from shipment.ts)
    modeOfTransport: z
      .enum(TRANSPORT_MODES)
      .optional()
      .describe("The mode of transportation for this shipment"),
    hblNumber: z.string().optional().describe("House Bill of Lading number (e.g., 'HBL-123456')"),

    // All other fields are optional
    invoiceNumber: StringOrNull.optional().describe("Reference number of the invoice"),
    currency: z.enum(CURRENCY_CODES).nullable().optional().describe("Three-letter currency code"),
    grossWeight: z.number().nullable().optional().describe("Gross weight of the shipment"),
    weightUOM: z.enum(WEIGHT_UNITS).nullable().optional().describe("Unit of measure for weight"),

    // Status fields
    status: z.enum(SHIPMENT_STATUS).nullable().optional().describe("Current status of the shipment"),
    customsStatus: z.enum(CUSTOMS_STATUS).nullable().optional().describe("Current customs processing status"),
    trackingStatus: z.enum(TRACKING_STATUS).nullable().optional().describe("Current tracking status"),

    // Optional core fields
    mblNumber: StringOrNull.optional().describe("Master Bill of Lading number"),
    cargoControlNumber: StringOrNull.optional().describe("Cargo Control Number for customs tracking"),
    PARS: StringOrNull.optional().describe("PARS number for road shipments"),
    CCN: StringOrNull.optional().describe("Cargo Control Number, 5-25 alphanumeric characters"),
    carrierCode: StringOrNull.optional().describe("Carrier's identification code"),
    containerNumber: z
      .union([z.string(), z.array(z.string())])
      .nullable()
      .optional()
      .describe("Container number(s)"),
    containerType: z
      .enum(CONTAINER_TYPES)
      .nullable()
      .optional()
      .describe("Type of container for FCL shipments"),

    // Measurements
    volume: z.number().nullable().optional().describe("Volume of the shipment"),
    volumeUOM: z.enum(VOLUME_UNITS).nullable().optional().describe("Unit of measure for volume"),
    weight: z.number().nullable().optional().describe("Weight of the shipment"),
    netWeight: z.number().nullable().optional().describe("Net weight of the shipment"),
    quantity: z.number().nullable().optional().describe("Quantity of items"),
    quantityUOM: z.enum(QUANTITY_UNITS).nullable().optional().describe("Unit of measure for quantity"),
    numberOfPackages: z.number().nullable().optional().describe("Total number of packages"),
    packageUOM: z.enum(PACKAGE_UOM).nullable().optional().describe("Unit of measure for packages"),

    // Dates
    etd: DateString.optional().describe("Estimated Time of Departure"),
    eta: DateString.optional().describe("Estimated Time of Arrival"),
    etaPort: DateString.optional().describe("Estimated Time of Arrival at Port"),
    etaDestination: DateString.optional().describe("Estimated Time of Arrival at Final Destination"),
    pickupLfd: DateString.optional().describe("Last Free Day for pickup"),
    pickupDate: DateString.optional().describe("Actual pickup date"),
    returnLfd: DateString.optional().describe("Last Free Day for return"),
    returnDate: DateString.optional().describe("Actual return date"),
    releaseDate: DateString.optional().describe("Release date"),
    surrenderDate: DateString.optional().describe("Surrender date"),
    adviceNoteDate: DateString.optional().describe("Advice note date"),
    validFrom: DateString.optional().describe("Start date of document validity"),
    validTo: DateString.optional().describe("End date of document validity"),
    invoiceDate: DateString.optional().describe("Date of invoice issuance"),

    // References
    pickupNumber: StringOrNull.optional().describe("Pickup reference number"),
    vessel: StringOrNull.optional().describe("Vessel name"),
    voyageNumber: StringOrNull.optional().describe("Voyage or flight number"),
    transactionNumber: StringOrNull.optional().describe("Internal transaction reference"),
    customsFileNumber: StringOrNull.optional().describe("Customs file reference"),
    portCode: StringOrNull.optional().describe("Port code"),
    portOfExit: StringOrNull.optional().describe("Port of exit code"),
    portOfEntry: StringOrNull.optional().describe("Port of entry (e.g., 'SARNIA, ON')"),
    subLocation: StringOrNull.optional().describe("Sub-location code or reference"),
    progress: StringOrNull.optional().describe("Current shipment status"),
    specialHandlingInstructions: z.string().nullable().optional().describe("Special handling instructions"),
    flight: StringOrNull.optional().describe("Flight number for air shipments"),
    destinationAirportCode: StringOrNull.optional().describe("Three-letter IATA airport code"),
    poNumber: StringOrNull.optional().describe("Purchase order reference number"),
    countryOfOrigin: StringOrNull.optional().describe("Country where goods originated"),

    // Financial Information
    discount: z.number().nullable().optional().describe("Discount amount"),
    transCost: z.number().nullable().optional().describe("Transportation cost"),
    packCost: z.number().nullable().optional().describe("Packing related costs"),
    miscCost: z.number().nullable().optional().describe("Miscellaneous costs"),
    total: z.number().nullable().optional().describe("Total amount before adjustments"),
    tax: z.number().nullable().optional().describe("Tax amount"),
    invoiceTotal: z.number().nullable().optional().describe("Final invoice amount"),

    // Location References
    portOfDischarge: LocationSchema.nullable().optional().describe("Port where cargo will be discharged"),
    portOfLoading: LocationSchema.nullable().optional().describe("Port where cargo was loaded"),
    placeOfDelivery: LocationSchema.nullable().optional().describe("Final delivery location"),
    pickupLocation: LocationSchema.nullable().optional().describe("Location for pickup"),

    // Trade Partner References
    carrier: TradePartnerSchema.nullable().optional().describe("The carrier handling the shipment"),
    manufacturer: TradePartnerSchema.nullable().optional().describe("The manufacturer of the goods"),
    shipper: TradePartnerSchema.nullable().optional().describe("The party shipping the goods"),
    consignee: TradePartnerSchema.nullable().optional().describe("The party receiving the goods"),
    forwarder: TradePartnerSchema.nullable().optional().describe("The freight forwarder"),
    trucker: TradePartnerSchema.nullable().optional().describe("The trucking company"),
    purchaser: TradePartnerSchema.nullable().optional().describe("The party purchasing the goods"),
    vendor: TradePartnerSchema.nullable().optional().describe("The party selling/supplying the goods"),
    producer: TradePartnerSchema.nullable().optional().describe("The party producing the goods"),
    exporter: TradePartnerSchema.nullable().optional().describe("The party exporting the goods"),
    importer: TradePartnerSchema.nullable().optional().describe("The party importing the goods"),

    // Other
    fieldsNotFound: z
      .array(z.string())
      .optional()
      .describe("List of '<possible_field_name>: <instructions>' that could not be mapped"),

    // Commercial Invoice Fields
    includedTransCost: z.number().nullable().optional().describe("Included transportation cost"),
    excludedTransCost: z.number().nullable().optional().describe("Excluded transportation cost"),
    includedPackCost: z.number().nullable().optional().describe("Included packing cost"),
    excludedPackCost: z.number().nullable().optional().describe("Excluded packing cost"),
    includedMiscCost: z.number().nullable().optional().describe("Included miscellaneous cost"),
    excludedMiscCost: z.number().nullable().optional().describe("Excluded miscellaneous cost"),
    valueIncludesDuty: z.boolean().nullable().optional().describe("Whether value includes duty"),
    additionalInfo: z.string().nullable().optional().describe("Additional information"),
    nonCompliantReason: z
      .enum(NON_COMPLIANT_REASONS)
      .nullable()
      .optional()
      .describe("Reason for non-compliance"),
    destinationProvince: z
      .enum(DESTINATION_PROVINCES)
      .nullable()
      .optional()
      .describe("Destination province code"),
    commercialInvoiceLines: z
      .array(CommercialInvoiceLineSchema)
      .nullable()
      .optional()
      .describe("Commercial invoice line items")
  })
  .strict();

export type ShippingData = z.infer<typeof ShippingDataSchema>;
