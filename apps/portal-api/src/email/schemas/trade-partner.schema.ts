import z from "zod";
import { CountrySchema } from "./country.schema";

// Constants for enums
const PARTNER_TYPES = [
  "vendor",
  "trucker",
  "importer",
  "shipper",
  "manufacturer",
  "air-carrier",
  "customs-broker",
  "forwarder",
  "ocean-carrier",
  "rail-company",
  "terminal",
  "warehouse",
  "consignee",
  "carrier"
] as const;

export const TradePartnerSchema = z
  .object({
    partnerType: z.enum(PARTNER_TYPES).optional().describe("Type of trade partner"),
    vendorCode: z.string().nullable().optional().describe("Vendor identification code"),
    name: z.string().optional().describe("Legal name of the trade partner"),
    email: z.string().nullable().optional().describe("Primary contact email address"),
    phoneNumber: z.string().nullable().optional().describe("Primary contact phone number"),
    address: z.string().nullable().optional().describe("Street address"),
    city: z.string().nullable().optional().describe("City name"),
    state: z.string().nullable().optional().describe("State or province"),
    postalCode: z.string().nullable().optional().describe("Postal or ZIP code"),
    country: CountrySchema.nullable().optional().describe("Country information")
  })
  .strict();
