import z from "zod";
import { EmailActionSchema } from "./email-intent.schema";
import { ShippingDataSchema } from "./shipment.schema";

// Combines EmailAction with ShippingData by overriding the shipping_data field
export const ValidatedEmailActionSchema = EmailActionSchema.extend({
  shippingData: ShippingDataSchema.optional().describe(`
    The structured shipping data extracted and normalized from the instructions.
    This represents the actual data changes or new data being requested.
    For CREATE intents, this contains all the new shipping data.
    For UPDATE intents, this contains only the fields being modified.
  `)
});

// Schema for all validated intents found in a single email
export const ValidatedEmailIntentsSchema = z.object({
  intents: z.array(ValidatedEmailActionSchema).describe(`
    An array of all discrete actions parsed from the email, validated with
    their corresponding structured shipping data. Each entry represents both
    the requested operation and the specific data changes involved.
  `)
});

// Export types
export type ValidatedEmailAction = z.infer<typeof ValidatedEmailActionSchema>;
export type ValidatedEmailIntents = z.infer<typeof ValidatedEmailIntentsSchema>;
