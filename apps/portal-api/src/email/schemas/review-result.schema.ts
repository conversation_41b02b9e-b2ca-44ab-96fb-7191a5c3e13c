import z from "zod";

// Schema for the review output
export const ReviewResultSchema = z
  .object({
    observations: z
      .array(z.string())
      .describe(
        "Observations about the email processing, extracted fields, definitions and nested objects, and other comments"
      ),
    recommendations: z.array(z.string()).describe("Suggestions for improving processing accuracy, if any"),
    fieldAnalysis: z
      .object({
        correctFields: z.number().describe("Number of correctly extracted fields"),
        incorrectFields: z.number().describe("Number of incorrectly extracted fields"),
        missingFields: z.number().describe("Number of fields that should have been extracted"),
        comments: z.array(z.string()).describe("Specific comments about field extraction")
      })
      .strict(),
    correctedOutput: z
      .string()
      .optional()
      .describe("Stringified JSON of the corrected output, only included if there are issues to fix")
  })
  .strict();

export type ReviewResult = z.infer<typeof ReviewResultSchema>;
