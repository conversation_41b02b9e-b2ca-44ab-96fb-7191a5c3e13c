import z from "zod";
import { CountrySchema } from "./country.schema";

// Constants for enums
const LOCATION_TYPES = ["ocean-port", "inland-port", "ocean-and-inland-port"] as const;

export const LocationSchema = z
  .object({
    locationType: z.enum(LOCATION_TYPES).optional().describe("Type of shipping location"),
    name: z.string().optional().describe("Name of the location"),
    streetAddress: z.string().optional().describe("Street address"),
    city: z.string().optional().describe("City name"),
    state: z.string().optional().describe("State or province"),
    postalCode: z.string().optional().describe("Postal or ZIP code"),
    timezone: z.string().optional().describe("IANA timezone database identifier (e.g., 'America/New_York')"),
    country: CountrySchema.optional().describe("Country information")
  })
  .strict();
