import z from "zod";
import { FILTERED_EMAIL_INTENTS } from "nest-modules";
import { ShippingDataSchema } from "./shipment.schema";

export const RequestedActionSchema = z.object({
  requestId: z.number().int().describe("Unique identifier for the request"),
  intent: z.enum(FILTERED_EMAIL_INTENTS).describe(`
    The user-requested action or operation for this entry.
    Each email can contain multiple distinct intents, each with its own instructions.
    Intents can be determined from both email text and document presence.
      - CREATE_SHIPMENT: New customs clearance request or initial documentation submission
      - UPDATE_COMMERCIAL_INVOICE: Modify existing commercial invoice
      - GET_SHIPMENT_STATUS: Check customs processing or clearance status
      - PROCESS_DOCUMENT: Process a document for shipment-related records
      - DOCUMENTATION_COMING: Sender is informing us that more documentation, attachments, or files are pending in a future communication
      - REQUEST_RUSH_PROCESSING: Request to process this shipment with urgency
      - REQUEST_MANUAL_PROCESSING: User requests that we handle this shipment manually, or is asking for some form of manual request not covered
      - REQUEST_HOLD_SHIPMENT: Request to hold the shipment for any reason and not have it processed through
      - UNSORTED: A catch-all for intents that don't fit into the above categories
  `),
  shippingData: ShippingDataSchema.optional().describe(`
    The structured shipping data extracted and normalized from the instructions.
    This represents the actual data changes or new data being requested.
    For CREATE intents, this contains all the new shipping data.
    For UPDATE intents, this contains only the fields being modified.
  `),
  attachments: z
    .array(
      z.object({
        filename: z.string().describe("Filename of the attachment, including extension"),
        documentType: z.string().describe("The specific shipping document type this file represents")
      })
    )
    .optional().describe(`
    A list of file attachments relevant to this intent.
    Each attachment represents a distinct shipping-related document 
    that the user has provided or updated (e.g., Bill of Lading, Certificate of Origin).
  `)
});

export const ProcessingStatusSchema = z
  .object({
    requestId: z.number().int().describe("Unique identifier for the request"),
    success: z.boolean().describe("Whether the request was processed successfully"),
    failedReason: z.string().describe("The reason for the request failure if the request was not successful"),
    timestamp: z.string().describe("ISO timestamp of when the request was processed")
  })
  .strict();

export const EmailResponseSchema = z
  .object({
    responseEmail: z
      .object({
        subject: z.string().describe("Subject line for the response email"),
        body: z.string().describe("Generated email body text")
      })
      .strict(),
    summaries: z
      .object({
        requestSummary: z.string().describe("One-line summary of what was requested in the original email"),
        processingOutcome: z.string().describe("One-line summary of the processing results")
      })
      .strict()
  })
  .strict();

export type RequestedAction = z.infer<typeof RequestedActionSchema>;
export type ProcessingStatus = z.infer<typeof ProcessingStatusSchema>;
export type EmailResponse = z.infer<typeof EmailResponseSchema>;
