import { AggregationModule } from "@/aggregation/aggregation.module";
import { LlmModule } from "@/llm/llm.module";
import { BullModule } from "@nestjs/bullmq";
import { DynamicModule, forwardRef, Logger, Module, ModuleMetadata, Provider } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  Document,
  Email,
  EmailThread,
  EmailUpdate,
  FileBatch,
  GmailToken,
  TemplateManagerModule
} from "nest-modules";
import { join } from "path";
import { ShipmentModule } from "src/shipment/shipment.module";
import { DocumentModule } from "../document/document.module";
import { ImporterModule } from "../importer/importer.module";
import { EmailController } from "./controllers/email.controller";
import { GmailController } from "./controllers/gmail.controller";
import { TestEmailController } from "./test/controllers/test-email.controller";

import { EmailListener } from "./listeners/email.listener";

import { QueueListener } from "./listeners/queue.listener";
import { UserListener } from "./listeners/user.listener";
import { GetGmailMessageProcessor } from "./processors/get-gmail-message.processor";
import { EmailUpdateService } from "./services/email-update.service";
import { EmailService } from "./services/email.service";
import { EmailDeletionService } from "./services/email-deletion.service";
import { GmailService } from "./services/gmail.service";
import { RnsProofService } from "./services/rns-proof-service";
import { TestEmailService } from "./test/services/test-email.service";

import { EMAIL_QUEUES, EMAIL_FLOW_PRODUCERS } from "./types/queue.types";

const listeners = [
  // Event Listeners
  EmailListener,
  QueueListener,
  UserListener
];

const moduleProps: ModuleMetadata = {
  imports: [
    TypeOrmModule.forFeature([GmailToken, Email, EmailUpdate, EmailThread, Document, FileBatch]),
    TemplateManagerModule.forFeature({
      templatesPath: join(__dirname, "templates"),
      nunjucksConfig: {
        autoescape: true
      }
    }),
    forwardRef(() => ImporterModule),
    forwardRef(() => ShipmentModule),
    BullModule.registerQueue(...EMAIL_QUEUES),
    BullModule.registerFlowProducer(...EMAIL_FLOW_PRODUCERS),
    forwardRef(() => DocumentModule),
    forwardRef(() => AggregationModule),
    forwardRef(() => LlmModule)
  ],
  providers: [
    // Services
    GmailService,
    EmailService,
    EmailUpdateService,
    EmailDeletionService,
    RnsProofService,
    TestEmailService,
    // Processors (always available for testing)
    GetGmailMessageProcessor
  ],
  controllers: [GmailController, EmailController, TestEmailController],
  exports: [GmailService, EmailService, EmailUpdateService, RnsProofService, GetGmailMessageProcessor]
};

@Module(moduleProps)
export class EmailModule {
  static forRoot(): DynamicModule {
    const providers: Array<Provider> = [...moduleProps.providers];
    if (process.env.FEATURE) {
      Logger.warn("Email module is disabled when feature is set", "EmailModule");
    } else {
      Logger.log("Email module is enabled when feature is not set", "EmailModule");
      providers.push(
        // Listeners (only when email module is enabled)
        ...listeners
      );
    }

    return {
      module: EmailModule,
      ...moduleProps,
      providers
    };
  }
}
