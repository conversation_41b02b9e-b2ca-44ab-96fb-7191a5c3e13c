import { getBaseUrl } from "@/utils/base-url";
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import axios from "axios";
import parseEmailAddresses, { ParsedGroup, ParsedMailbox } from "email-addresses";
import { gmail_v1, google } from "googleapis";
import {
  AuthenticatedRequest,
  EmailAddressDto,
  getFindOptions,
  GetGmailTokensDto,
  GetGmailTokensResponseDto,
  GmailToken,
  GmailTokenSortBy,
  UserPermission
} from "nest-modules";
import { DataSource, Not, Repository } from "typeorm";
import { GmailRefreshAccessTokenResponseDto } from "../dto/gmail.dto";
import {
  GmailHeaderName,
  GmailHistoryT<PERSON>,
  GmailMessageFormat,
  PARSE_EMAIL_ADDR_DEFAULT_CONFIG,
  ParsedGmailMessage,
  ParsedGmailMessagePart
} from "../types/gmail.types";

@Injectable({ scope: Scope.REQUEST })
export class GmailService {
  constructor(
    @InjectRepository(GmailToken)
    private readonly gmailTokenRepository: Repository<GmailToken>,
    @Inject(ConfigService)
    private readonly configService: ConfigService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}
  private readonly logger = new Logger(GmailService.name);

  private async _handleApiErrorAndUpdateReadRateLimit(email: string, error: any): Promise<void> {
    const statusCode = error?.code || error?.response?.status;
    if (statusCode === 429) {
      let retryUntilDate: Date | null = null;
      const headers = error?.response?.headers ?? {};
      // Axios normalises to lower-case; fall back to original case just in case
      const retryAfterHeader = headers["retry-after"] ?? headers["Retry-After"];
      let usedHeader = false;

      if (retryAfterHeader) {
        const trimmedHeader = retryAfterHeader.trim();
        const retryAfterSeconds = parseInt(trimmedHeader, 10);
        if (!isNaN(retryAfterSeconds)) {
          retryUntilDate = new Date(Date.now() + retryAfterSeconds * 1000);
          usedHeader = true;
        } else {
          const dateFromHeader = new Date(trimmedHeader);
          if (!isNaN(dateFromHeader.getTime())) {
            retryUntilDate = dateFromHeader;
            usedHeader = true;
          }
        }
      }

      // If Retry-After header was not found or not parsable, try to parse from error message
      if (!retryUntilDate && error?.message) {
        const match = error.message.match(/Retry after (\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z)/);
        if (match && match[1]) {
          const dateFromMessage = new Date(match[1]);
          if (!isNaN(dateFromMessage.getTime())) {
            retryUntilDate = dateFromMessage;
          }
        }
      }

      if (retryUntilDate && retryUntilDate.getTime() > Date.now()) {
        try {
          await this.gmailTokenRepository.update({ email }, { rateLimitReadUntil: retryUntilDate });
          const source = usedHeader ? `Retry-After header (${retryAfterHeader})` : "error message";
          this.logger.warn(
            `Gmail API read operation rate limit encountered for ${email}. Read sync paused until ${retryUntilDate.toISOString()}. Source: ${source}.`
          );
        } catch (dbError) {
          this.logger.error(`Failed to update rateLimitReadUntil for ${email} in DB: ${dbError.message}`);
        }
      } else {
        const allHeaders = error?.response?.headers
          ? JSON.stringify(error.response.headers)
          : "No headers found in error response.";
        this.logger.warn(
          `Received 429 for read op for ${email}. Could not determine a valid future Retry-After time. Header: '${retryAfterHeader || "N/A"}'. Message: '${error?.message || "N/A"}'. All Headers: ${allHeaders}`
        );
      }
    }
  }

  async parseGmailMessage(email: string, message: gmail_v1.Schema$Message): Promise<ParsedGmailMessage> {
    // Parse email headers
    const fromHeader =
      message?.payload?.headers?.find(({ name }) => name.trim().toLowerCase() === GmailHeaderName.FROM) ||
      null;
    const replyToHeader =
      message?.payload?.headers?.find(({ name }) => name.trim().toLowerCase() === GmailHeaderName.REPLY_TO) ||
      null;
    const toHeader =
      message?.payload?.headers?.find(({ name }) => name.trim().toLowerCase() === GmailHeaderName.TO) || null;
    const ccHeader =
      message?.payload?.headers?.find(({ name }) => name.trim().toLowerCase() === GmailHeaderName.CC) || null;
    const subjectHeader =
      message?.payload?.headers?.find(({ name }) => name.trim().toLowerCase() === GmailHeaderName.SUBJECT) ||
      null;
    const messageIdHeader =
      message?.payload?.headers?.find(({ name }) => name.trim().toLowerCase() === "message-id") || null;
    const parsedMessage: ParsedGmailMessage = {
      from: this.parseAddressHeader(fromHeader),
      replyTo: this.parseAddressHeader(replyToHeader),
      to: this.parseAddressHeader(toHeader),
      cc: this.parseAddressHeader(ccHeader),
      subject: subjectHeader?.value || null,
      messageId: messageIdHeader?.value || null,
      text: null,
      html: null,
      attachments: []
    };

    // Parse email body and attachments
    const parsedMessagePart = await this.parseMessagePart(email, message.id, message?.payload);
    if (parsedMessagePart?.text) parsedMessage.text = parsedMessagePart.text;
    if (parsedMessagePart?.html) parsedMessage.html = parsedMessagePart.html;
    if (parsedMessagePart?.attachments) parsedMessage.attachments.push(...parsedMessagePart.attachments);

    return parsedMessage;
  }

  private parseAddressHeader(header?: gmail_v1.Schema$MessagePartHeader): Array<EmailAddressDto> {
    if (!header) return [];
    const parseResult = parseEmailAddresses({
      input: header.value,
      ...PARSE_EMAIL_ADDR_DEFAULT_CONFIG
    });
    if (Array.isArray(parseResult?.addresses))
      return parseResult.addresses.reduce((addressList, addressObj) => {
        switch (addressObj.type) {
          case "group":
            addressList.push(
              ...(addressObj as ParsedGroup).addresses.map(({ address, name, local }) => {
                const dto = new EmailAddressDto();
                dto.address = address;
                dto.name = name || local;
                return dto;
              })
            );
            break;
          default:
            const parsedMailbox = addressObj as ParsedMailbox;
            const dto = new EmailAddressDto();
            dto.address = parsedMailbox.address;
            dto.name = parsedMailbox.name || parsedMailbox.local;
            addressList.push(dto);
            break;
        }
        return addressList;
      }, [] as Array<EmailAddressDto>);
  }

  /**
   * Parse the body of a Gmail message part into a buffer. Null if the body cannot be parsed.
   * @param email - The email address of the user.
   * @param messageId - The ID of the message.
   * @param partBody - The body of the message part.
   * @returns The parsed message part body as a buffer.
   */
  private async parseMessagePartBody(
    email: string,
    messageId: string,
    partBody: gmail_v1.Schema$MessagePartBody
  ) {
    let b64UrlData: string | null = null;
    if (partBody?.data) b64UrlData = partBody.data;
    else if (partBody?.attachmentId) {
      try {
        const attachment = await this.getAttachment(email, messageId, partBody.attachmentId);
        b64UrlData = attachment?.data || null;
      } catch (error) {
        this.logger.error(
          `Cannot get attachment ${partBody?.attachmentId} for message part body ${messageId}. Email: ${email}`
        );
        b64UrlData = null;
      }
    }
    if (b64UrlData) return Buffer.from(b64UrlData, "base64url");
    else return null;
  }

  /**
   * Parse a Gmail message part recursively.
   * @param email - The email address of the user.
   * @param messageId - The ID of the message.
   * @param part - The part to parse.
   * @returns The parsed message part, including text, HTML and list of attachments.
   */
  private async parseMessagePart(
    email: string,
    messageId: string,
    part: gmail_v1.Schema$MessagePart
  ): Promise<ParsedGmailMessagePart> {
    const parsedMessagePart: ParsedGmailMessagePart = {
      text: null,
      html: null,
      attachments: []
    };
    const bodyBuffer = await this.parseMessagePartBody(email, messageId, part?.body);
    const isAttachment = typeof part?.filename === "string" && part?.filename?.length > 0;
    if (isAttachment) {
      this.logger.log(
        `Part ${part?.partId} of message ${messageId} is an attachment, adding to attachment list...`
      );
      parsedMessagePart.attachments = [
        {
          fileName: part?.filename || null,
          mimeType: part?.mimeType || null,
          buffer: bodyBuffer || null
        }
      ];
    } else if (part?.mimeType === "text/plain") {
      this.logger.log(`Part ${part?.partId} of message ${messageId} is a text part, saving to text field...`);
      parsedMessagePart.text = bodyBuffer?.toString("utf-8") || null;
    } else if (part?.mimeType === "text/html") {
      this.logger.log(
        `Part ${part?.partId} of message ${messageId} is an HTML part, saving to html field...`
      );
      parsedMessagePart.html = bodyBuffer?.toString("utf-8") || null;
    } else if (Array.isArray(part?.parts) && part?.parts?.length > 0) {
      this.logger.log(
        `Part ${part?.partId} of message ${messageId} has children parts, parsing recursively...`
      );
      for (const childPart of part.parts) {
        const childParsedMessagePart = await this.parseMessagePart(email, messageId, childPart);
        if (childParsedMessagePart?.text) parsedMessagePart.text = childParsedMessagePart.text;
        if (childParsedMessagePart?.html) parsedMessagePart.html = childParsedMessagePart.html;
        if (childParsedMessagePart?.attachments)
          parsedMessagePart.attachments.push(...childParsedMessagePart.attachments);
      }
    } else
      this.logger.warn(
        `Part ${part?.partId} of message ${messageId} is not a text, HTML, or attachment part, skipping...`
      );
    return parsedMessagePart;
  }

  private getGmailOAuthClient() {
    const GOOGLE_OAUTH_CLIENT_ID = this.configService.get<string>("GOOGLE_OAUTH_CLIENT_ID");
    const GOOGLE_OAUTH_CLIENT_SECRET = this.configService.get<string>("GOOGLE_OAUTH_CLIENT_SECRET");
    const portalApiBaseUrl = getBaseUrl();

    return new google.auth.OAuth2(
      GOOGLE_OAUTH_CLIENT_ID,
      GOOGLE_OAUTH_CLIENT_SECRET,
      `${portalApiBaseUrl}/gmail/oauth/callback`
    );
  }

  getGmailAuthorizationUri() {
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user is not allowed to get Gmail authorization URI");
    const oauthClient = this.getGmailOAuthClient();
    return {
      url: oauthClient.generateAuthUrl({
        access_type: "offline",
        scope: [
          "openid",
          "https://www.googleapis.com/auth/gmail.modify",
          "https://www.googleapis.com/auth/userinfo.email",
          "https://www.googleapis.com/auth/userinfo.profile"
        ],
        include_granted_scopes: true,
        prompt: "consent"
        //TODO: Add CSRF token to state
      })
    };
  }

  async updateGmailToken(
    email: string,
    accessToken: string | null,
    refreshToken: string | null,
    accessTokenExpiryDate: Date | null
  ) {
    let gmailTokenRecord = await this.gmailTokenRepository.findOneBy({ email });

    if (!gmailTokenRecord) {
      gmailTokenRecord = new GmailToken();
      gmailTokenRecord.email = email;
      gmailTokenRecord.isDefaultMailbox = false;
    }
    gmailTokenRecord.accessToken = accessToken;
    gmailTokenRecord.refreshToken = refreshToken;
    gmailTokenRecord.accessTokenExpiryDate = accessTokenExpiryDate;
    return await this.gmailTokenRepository.save(gmailTokenRecord);
  }

  async updateGmailTokenLastSyncedHistoryId(email: string, lastSyncedHistoryId: string) {
    const gmailTokenRecord = await this.gmailTokenRepository.findOneBy({ email });
    if (!gmailTokenRecord) throw new NotFoundException(`Gmail token for ${email} not found`);
    if (!/^\d+$/.test(lastSyncedHistoryId))
      throw new BadRequestException(`Last synced history ID is not a numeric string`);
    gmailTokenRecord.lastSyncedHistoryId = lastSyncedHistoryId;
    return await this.gmailTokenRepository.save(gmailTokenRecord);
  }

  async gmailOAuthCallback(code: string | null, error: string | null, state: string | null) {
    if (error) throw new BadRequestException(`Got error from callback: ${error}`);
    if (!code) throw new BadRequestException(`No code from callback`);
    const oauthClient = this.getGmailOAuthClient();
    const { tokens } = await oauthClient.getToken(code);
    const { email } = await oauthClient.getTokenInfo(tokens.access_token);
    // TODO: Validate callback using state
    await this.updateGmailToken(
      email,
      tokens.access_token || null,
      tokens.refresh_token || null,
      new Date(tokens.expiry_date)
    );
    return "Successfully authenticated with Gmail";
  }

  async refreshGmailAccessToken(email: string) {
    const GOOGLE_OAUTH_CLIENT_ID = this.configService.get<string>("GOOGLE_OAUTH_CLIENT_ID");
    const GOOGLE_OAUTH_CLIENT_SECRET = this.configService.get<string>("GOOGLE_OAUTH_CLIENT_SECRET");
    const EXPIRY_DATE_BUFFER_MS = 10000;

    const gmailTokenRecord = await this.gmailTokenRepository.findOneBy({
      email
    });
    if (!gmailTokenRecord) throw new NotFoundException(`Gmail token for ${email} not found`);
    if (gmailTokenRecord.accessTokenExpiryDate.getTime() - EXPIRY_DATE_BUFFER_MS > Date.now()) {
      this.logger.log(`Gmail token for ${email} has not expired, returning original token`);
      return gmailTokenRecord;
    }
    if (!gmailTokenRecord.refreshToken) throw new NotFoundException(`Refresh token for ${email} not found`);
    const oauthClient = this.getGmailOAuthClient();
    const res = await axios.post(
      "https://oauth2.googleapis.com/token",
      {
        client_id: GOOGLE_OAUTH_CLIENT_ID,
        client_secret: GOOGLE_OAUTH_CLIENT_SECRET,
        refresh_token: gmailTokenRecord.refreshToken,
        grant_type: "refresh_token"
      },
      {
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        timeout: 30000,
        timeoutErrorMessage: "Refresh Gmail access token timed out"
      }
    );
    const { access_token, expires_in } = res.data as GmailRefreshAccessTokenResponseDto;
    this.logger.log(
      `Refresh Gmail access token for ${email} successful, new token expires in ${expires_in} seconds`
    );
    return await this.updateGmailToken(
      email,
      access_token,
      gmailTokenRecord.refreshToken,
      new Date(Date.now() + expires_in * 1000)
    );
  }

  private async getGmailInstance(email: string) {
    const gmailTokenRecord = await this.refreshGmailAccessToken(email);
    const oauthClient = this.getGmailOAuthClient();
    oauthClient.setCredentials({ access_token: gmailTokenRecord.accessToken });
    return google.gmail({ version: "v1", auth: oauthClient });
  }

  async getGmailTokens(getGmailTokensDto: GetGmailTokensDto): Promise<GetGmailTokensResponseDto> {
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user is not allowed to get Gmail tokens");
    const { skip, take, order, where } = getFindOptions(getGmailTokensDto, [], [], GmailTokenSortBy.email);

    const [data, total] = await this.gmailTokenRepository.findAndCount({
      where,
      select: ["isDefaultMailbox", "email", "accessTokenExpiryDate", "lastSyncedHistoryId"],
      order,
      skip,
      take
    });

    return {
      data,
      total,
      skip,
      limit: take
    };
  }

  async getGmailTokenByEmail(email: string) {
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user is not allowed to get Gmail token");
    return await this.gmailTokenRepository.findOne({
      where: { email },
      select: ["isDefaultMailbox", "email", "accessTokenExpiryDate", "lastSyncedHistoryId"]
    });
  }

  async setDefaultGmailToken(email: string) {
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user is not allowed to set default mailbox");

    if (!this.gmailTokenRepository.existsBy({ email }))
      throw new NotFoundException("Current email is not logged in");

    await this.dataSource.transaction(async (entityManager) => {
      await entityManager.update(GmailToken, { email }, { isDefaultMailbox: true });
      await entityManager.update(GmailToken, { email: Not(email) }, { isDefaultMailbox: false });
    });

    return await this.getGmailTokenByEmail(email);
  }

  async listMessages(email: string, q?: string, maxResults?: number, maxRetry = 5) {
    const gmail = await this.getGmailInstance(email);

    let pageToken: string | null = null;
    const messages: Array<gmail_v1.Schema$Message> = [];
    do {
      let isPageSuccessful = false;
      for (let retryCount = 0; retryCount < maxRetry; retryCount++) {
        this.logger.log(
          `-- Attempt ${retryCount + 1} to list messages for ${email}, current page token: ${pageToken}`
        );
        try {
          // Check rate limit before making the API call
          const currentTokenStateBeforeAttempt = await this.gmailTokenRepository.findOneBy({ email });
          if (
            currentTokenStateBeforeAttempt?.rateLimitReadUntil &&
            new Date() < currentTokenStateBeforeAttempt.rateLimitReadUntil
          ) {
            this.logger.warn(
              `List messages for ${email} aborted before attempt: rate limited until ${currentTokenStateBeforeAttempt.rateLimitReadUntil.toISOString()}`
            );
            throw new InternalServerErrorException(
              `Rate limit active for ${email}, listMessages aborted. Please retry after ${currentTokenStateBeforeAttempt.rateLimitReadUntil.toISOString()}.`
            );
          }

          const listMessagesRes = await gmail.users.messages.list({
            userId: "me",
            pageToken: pageToken || undefined,
            maxResults: Math.min(
              500,
              typeof maxResults === "number" ? Math.max(0, maxResults - messages.length) : 500
            ),
            q: q || undefined
          });
          const { nextPageToken, messages: currentPageMessages } = listMessagesRes.data;
          this.logger.log(
            `-- Fetched ${currentPageMessages?.length} messages for ${email}, next page token: ${nextPageToken}`
          );
          pageToken = nextPageToken;
          if (Array.isArray(currentPageMessages)) messages.push(...currentPageMessages);
          if (typeof maxResults === "number" && messages.length >= maxResults) {
            this.logger.log(`-- Reached max results (${maxResults}) for ${email}, stopping listMessages.`);
            pageToken = null;
          }
          isPageSuccessful = true;

          // If successful and a rate limit was previously set, clear it.
          if (currentTokenStateBeforeAttempt?.rateLimitReadUntil) {
            await this.gmailTokenRepository.update({ email }, { rateLimitReadUntil: null });
            this.logger.log(`Cleared rateLimitReadUntil for ${email} after successful listMessages call.`);
          }
          break; // Break from retry loop for this page
        } catch (error) {
          this.logger.error(
            `Error during listMessages attempt ${retryCount + 1} for ${email}: ${error.message}`
          );
          await this._handleApiErrorAndUpdateReadRateLimit(email, error); // Sets rateLimitReadUntil on 429

          // Check if rateLimitReadUntil was set by the call above
          const tokenStateAfterErrorHandling = await this.gmailTokenRepository.findOneBy({ email });
          if (
            tokenStateAfterErrorHandling?.rateLimitReadUntil &&
            new Date() < tokenStateAfterErrorHandling.rateLimitReadUntil
          ) {
            this.logger.warn(
              `Rate limit activated for ${email} during listMessages. Stopping operation. Retry after ${tokenStateAfterErrorHandling.rateLimitReadUntil.toISOString()}`
            );
            throw error; // Re-throw the original error to exit listMessages immediately
          }

          if (retryCount === maxRetry - 1) {
            this.logger.error(`Final attempt to list messages failed for ${email} on this page.`);
            // Do not throw here, let the !isPageSuccessful block handle it if all retries for the page failed.
          }
        }
      }

      if (!isPageSuccessful) {
        this.logger.error(`All retry attempts failed for current page while listing messages for ${email}.`);
        // The error that caused the final failure (and potentially set the rate limit) would have been thrown from the loop.
        // If we reach here, it implies the loop was exited due to max retries without success, and the last error wasn't a rate-limiting one that forced an early exit.
        // Or, if the last error WAS a rate-limiting one, it was already thrown. To be safe, re-throw a generic error.
        throw new InternalServerErrorException(
          `Failed to list messages for ${email} after ${maxRetry} attempts on a page.`
        );
      }
    } while (pageToken);

    return {
      messages
    };
  }

  async getMessage(
    email: string,
    messageId: string,
    format: GmailMessageFormat,
    metadataHeaders?: Array<string>
  ) {
    const gmail = await this.getGmailInstance(email);

    try {
      const getMessageRes = await gmail.users.messages.get({
        userId: "me",
        id: messageId,
        format: format,
        metadataHeaders:
          format === GmailMessageFormat.METADATA && Array.isArray(metadataHeaders)
            ? metadataHeaders
            : undefined
      });
      return getMessageRes.data;
    } catch (error) {
      await this._handleApiErrorAndUpdateReadRateLimit(email, error);
      this.logger.error(
        `Got error while getting gmail message for ${email}, ID ${messageId}: ${error?.message}`,
        error?.stack
      );
      throw new InternalServerErrorException(
        `Failed to get Gmail message for ID ${messageId}. Original error: ${error?.message}`
      );
    }
  }

  async listHistories(
    email: string,
    startHistoryId: string,
    labelId?: string,
    historyTypes?: Array<GmailHistoryType>,
    maxResults?: number,
    maxRetry = 5
  ) {
    const gmail = await this.getGmailInstance(email);

    let pageToken: string | null = null;
    const histories: Array<gmail_v1.Schema$History> = [];
    let latestHistoryId: number | null = null;
    do {
      let isPageSuccessful = false;
      for (let retryCount = 0; retryCount < maxRetry; retryCount++) {
        this.logger.log(`-- Attempt ${retryCount + 1}, current page token: ${pageToken}`);
        try {
          const listHistoriesRes = await gmail.users.history.list({
            userId: "me",
            pageToken: pageToken || undefined,
            startHistoryId,
            labelId: labelId || undefined,
            historyTypes: historyTypes || undefined,
            maxResults: Math.min(
              500,
              typeof maxResults === "number" ? Math.max(0, maxResults - histories.length) : 500
            )
          });
          const { nextPageToken, history: currentPageHistories, historyId } = listHistoriesRes.data;
          this.logger.log(
            `-- History count: ${currentPageHistories?.length}, next page token: ${nextPageToken}, history ID: ${historyId}`
          );
          pageToken = nextPageToken;
          if (Array.isArray(currentPageHistories)) histories.push(...currentPageHistories);
          if (typeof historyId === "string" && /^\d+$/.test(historyId)) {
            const parsedHistoryId = parseInt(historyId);
            if (
              !Number.isNaN(parsedHistoryId) &&
              (latestHistoryId === null || parsedHistoryId > latestHistoryId)
            )
              latestHistoryId = parsedHistoryId;
          }
          if (histories.length >= maxResults) {
            this.logger.log(`-- Reached max results, stopping...`);
            pageToken = null;
          }
          isPageSuccessful = true;
          break;
        } catch (error) {
          this.logger.error(`Got error while listing histories for ${email}: ${error.message}`);
        }
      }

      if (!isPageSuccessful) {
        this.logger.error(`Failed too many times, stopping...`);
        throw new InternalServerErrorException(`Gmail list histories failed too many times`);
      }
    } while (pageToken);

    return {
      histories,
      latestHistoryId
    };
  }

  async getAttachment(email: string, messageId: string, attachmentId: string) {
    const gmail = await this.getGmailInstance(email);

    try {
      const getAttachmentRes = await gmail.users.messages.attachments.get({
        userId: "me",
        messageId,
        id: attachmentId
      });
      return getAttachmentRes.data;
    } catch (error) {
      await this._handleApiErrorAndUpdateReadRateLimit(email, error);
      this.logger.error(
        `Got error while getting gmail attachment for ${email}: ${error.message}`,
        error?.stack
      );
      throw new InternalServerErrorException(
        `Failed to get Gmail attachment. Original error: ${error?.message}`
      );
    }
  }

  async sendMessage(email: string, message: gmail_v1.Schema$Message) {
    const gmail = await this.getGmailInstance(email);

    try {
      const sendMessageRes = await gmail.users.messages.send({
        userId: "me",
        requestBody: message
      });
      return sendMessageRes.data;
    } catch (error) {
      this.logger.error(`Got error while sending message for ${email}: ${error.message}`);
      throw new InternalServerErrorException(`Failed to send Gmail message`);
    }
  }
}
