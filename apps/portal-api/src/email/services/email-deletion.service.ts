import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { Email, EmailUpdate, EmailThread, Document, File, FileBatch, Shipment } from "nest-modules";
import { DataSource, QueryRunner } from "typeorm";

/**
 * Service for completely deleting an email and all its related data created during processing.
 * This utility follows the deletion order to avoid foreign key constraint violations.
 *
 * ⚠️ DESTRUCTIVE OPERATION - Use only for debugging/testing purposes.
 *
 * ## Usage via REPL:
 *
 * 1. Start the REPL:
 *    ```bash
 *    rushx start:repl
 *    ```
 *    (or from apps/portal-api directory: `npm run start:repl`)
 *
 * 2. In the REPL console:
 *    ```typescript
 *    // Get the service through the module context
 *    const emailDeletionService = get('EmailDeletionService')
 *
 *    // Preview what would be deleted (optional)
 *    const summary = await emailDeletionService.getEmailDeletionSummary(123)
 *    console.log(summary)
 *
 *    // Perform the deletion (DESTRUCTIVE!)
 *    await emailDeletionService.deleteEmailCompletely(123)
 *    ```
 *
 * The service will delete the email and ALL related data created during its processing.
 * This operation cannot be undone.
 */
@Injectable()
export class EmailDeletionService {
  private readonly logger = new Logger(EmailDeletionService.name);

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  /**
   * Completely removes an email and all its related data from the database.
   * This operation is irreversible and should only be used for testing purposes.
   *
   * @param emailId - The ID of the email to delete
   * @param queryRunner - Optional query runner for transaction management
   * @returns Promise<void>
   * @throws NotFoundException if email doesn't exist
   */
  async deleteEmailCompletely(emailId: number, queryRunner?: QueryRunner): Promise<void> {
    const tQueryRunner = queryRunner || this.dataSource.createQueryRunner();

    if (!queryRunner) {
      await tQueryRunner.connect();
      await tQueryRunner.startTransaction();
    }

    try {
      this.logger.log(`Starting complete deletion of email ${emailId}`);

      // First verify the email exists
      const email = await tQueryRunner.manager.findOne(Email, {
        where: { id: emailId },
        relations: ["updates"]
      });

      if (!email) {
        throw new NotFoundException(`Email with ID ${emailId} not found`);
      }

      this.logger.log(`Found email ${emailId}, proceeding with deletion`);

      // Step 1: Get all EmailUpdates for correlation tracking
      const emailUpdateIds = email.updates?.map((update) => update.id) || [];
      this.logger.log(`Found ${emailUpdateIds.length} email updates: [${emailUpdateIds.join(", ")}]`);

      // Step 2: Delete shipments created from this email's EmailUpdates
      // These are tracked via EmailUpdate.id being used as batchId in aggregation
      if (emailUpdateIds.length > 0) {
        await this.deleteShipmentsFromEmailUpdates(emailUpdateIds, tQueryRunner);
      }

      // Step 3: Delete documents and files associated with this email
      await this.deleteDocumentsAndFiles(emailUpdateIds, tQueryRunner);

      // Step 4: Delete EmailUpdate-Document associations
      this.logger.log(`Deleting email update document associations for email ${emailId}`);
      await tQueryRunner.manager.query(
        `DELETE FROM email_update_documents WHERE "emailUpdateId" IN (
          SELECT eu.id FROM email_update eu WHERE eu."emailId" = $1
        )`,
        [emailId]
      );

      // Step 5: Delete EmailUpdates
      this.logger.log(`Deleting email updates for email ${emailId}`);
      await tQueryRunner.manager.query(`DELETE FROM email_update WHERE "emailId" = $1`, [emailId]);

      // Step 6: Delete EmailThreads associated with this email's threadId
      this.logger.log(`Deleting email threads for email ${emailId}`);
      await tQueryRunner.manager.query(`DELETE FROM email_thread WHERE "threadId" = $1`, [email.threadId]);

      // Step 7: Finally, delete the email itself
      this.logger.log(`Deleting email ${emailId}`);
      const deleteResult = await tQueryRunner.manager.delete(Email, {
        id: emailId
      });

      if (deleteResult.affected === 0) {
        throw new Error(`Failed to delete email ${emailId} - no rows affected`);
      }

      if (!queryRunner) {
        await tQueryRunner.commitTransaction();
      }

      this.logger.log(`Successfully completed deletion of email ${emailId}`);
    } catch (error) {
      if (!queryRunner && tQueryRunner.isTransactionActive) {
        await tQueryRunner.rollbackTransaction();
      }
      this.logger.error(`Failed to delete email ${emailId}: ${error.message}`, error.stack);
      throw error;
    } finally {
      if (!queryRunner) {
        await tQueryRunner.release();
      }
    }
  }

  /**
   * Delete shipments that were created from EmailUpdates of this email.
   * Uses the correlation between EmailUpdate.id and aggregation batchId.
   */
  private async deleteShipmentsFromEmailUpdates(
    emailUpdateIds: number[],
    queryRunner: QueryRunner
  ): Promise<void> {
    this.logger.log(`Looking for shipments created from email updates: [${emailUpdateIds.join(", ")}]`);

    // Find shipments created from these EmailUpdates by checking document_aggregation records
    // The correlation is: EmailUpdate.id -> document_aggregation.batchId -> shipment
    const shipmentIds = await queryRunner.manager.query(
      `
      SELECT DISTINCT da."shipmentId"
      FROM document_aggregation da
      WHERE da."batchId" = ANY($1::text[])
      AND da."shipmentId" IS NOT NULL
      `,
      [emailUpdateIds.map((id) => id.toString())]
    );

    const shipmentIdsToDelete = shipmentIds.map((row: any) => row.shipmentId);

    if (shipmentIdsToDelete.length > 0) {
      this.logger.log(
        `Found ${shipmentIdsToDelete.length} shipments to delete: [${shipmentIdsToDelete.join(", ")}]`
      );

      // Use the existing ShipmentDeletionService logic for each shipment
      for (const shipmentId of shipmentIdsToDelete) {
        await this.deleteShipmentCompletely(shipmentId, queryRunner);
      }
    } else {
      this.logger.log(`No shipments found to delete from email updates`);
    }
  }

  /**
   * Delete documents and files associated with this email.
   * The relationship is through FileBatch entities that are created during email processing.
   */
  private async deleteDocumentsAndFiles(emailUpdateIds: number[], queryRunner: QueryRunner): Promise<void> {
    this.logger.log(`Deleting documents and files for email updates: [${emailUpdateIds.join(", ")}]`);

    if (emailUpdateIds.length === 0) {
      this.logger.log(`No email updates to process for document/file deletion`);
      return;
    }

    // Find FileBatches associated with these EmailUpdates through document_aggregation.batchId
    // The correlation is: EmailUpdate.id -> document_aggregation.batchId -> file_batch.id
    const fileBatches = await queryRunner.manager.query(
      `
      SELECT DISTINCT fb.id
      FROM file_batch fb
      INNER JOIN document_aggregation da ON da."batchId" = fb.id
      WHERE da."batchId" = ANY($1::text[])
      `,
      [emailUpdateIds.map((id) => id.toString())]
    );

    const fileBatchIds = fileBatches.map((row: any) => row.id);

    if (fileBatchIds.length > 0) {
      this.logger.log(`Found ${fileBatchIds.length} file batches to process: [${fileBatchIds.join(", ")}]`);

      // Delete documents associated with these file batches
      await queryRunner.manager.query(
        `DELETE FROM document WHERE "fileId" IN (
          SELECT f.id FROM file f WHERE f."batchId" = ANY($1::text[])
        )`,
        [fileBatchIds]
      );

      // Delete files associated with these file batches
      await queryRunner.manager.query(`DELETE FROM file WHERE "batchId" = ANY($1::text[])`, [fileBatchIds]);

      // Delete the file batches themselves
      await queryRunner.manager.query(`DELETE FROM file_batch WHERE id = ANY($1::text[])`, [fileBatchIds]);
    } else {
      this.logger.log(`No file batches found for email updates`);
    }
  }

  /**
   * Delete a shipment completely (reusing logic from ShipmentDeletionService).
   * This is a simplified version focused on the core deletion logic.
   */
  private async deleteShipmentCompletely(shipmentId: number, queryRunner: QueryRunner): Promise<void> {
    this.logger.log(`Deleting shipment ${shipmentId} and all related data`);

    // Step 1: Delete Commercial Invoice Line Measurements
    await queryRunner.manager.query(
      `
      DELETE FROM commercial_invoice_line_measurement
      WHERE "commercialInvoiceLineId" IN (
        SELECT cil.id FROM commercial_invoice_line cil
        INNER JOIN commercial_invoice ci ON ci.id = cil."commercialInvoiceId"
        WHERE ci."shipmentId" = $1
      )
    `,
      [shipmentId]
    );

    // Step 2: Delete Commercial Invoice Lines
    await queryRunner.manager.query(
      `
      DELETE FROM commercial_invoice_line
      WHERE "commercialInvoiceId" IN (
        SELECT ci.id FROM commercial_invoice ci
        WHERE ci."shipmentId" = $1
      )
    `,
      [shipmentId]
    );

    // Step 3: Delete Commercial Invoices
    await queryRunner.manager.query(`DELETE FROM commercial_invoice WHERE "shipmentId" = $1`, [shipmentId]);

    // Step 4: Delete Tracking Histories
    await queryRunner.manager.query(`DELETE FROM tracking_history WHERE "shipmentId" = $1`, [shipmentId]);

    // Step 5: Delete Email Threads
    await queryRunner.manager.query(`DELETE FROM email_thread WHERE "shipmentId" = $1`, [shipmentId]);

    // Step 6: Update related entities to NULL (preserve them but remove shipment reference)
    await queryRunner.manager.query(
      `UPDATE document_aggregation SET "shipmentId" = NULL WHERE "shipmentId" = $1`,
      [shipmentId]
    );
    await queryRunner.manager.query(`UPDATE document SET "shipmentId" = NULL WHERE "shipmentId" = $1`, [
      shipmentId
    ]);
    await queryRunner.manager.query(`UPDATE file SET "shipmentId" = NULL WHERE "shipmentId" = $1`, [
      shipmentId
    ]);
    await queryRunner.manager.query(`UPDATE file_batch SET "shipmentId" = NULL WHERE "shipmentId" = $1`, [
      shipmentId
    ]);

    // Step 7: Delete Containers
    await queryRunner.manager.query(`DELETE FROM container WHERE "shipmentId" = $1`, [shipmentId]);

    // Step 8: Finally, delete the shipment itself
    const deleteResult = await queryRunner.manager.delete(Shipment, { id: shipmentId });

    if (deleteResult.affected === 0) {
      this.logger.warn(`Shipment ${shipmentId} was not found or already deleted`);
    } else {
      this.logger.log(`Successfully deleted shipment ${shipmentId}`);
    }
  }

  /**
   * Get a summary of all entities that would be deleted for an email.
   * Useful for verification before actual deletion.
   *
   * @param emailId - The ID of the email to analyze
   * @returns Promise<object> containing counts of related entities
   */
  async getEmailDeletionSummary(emailId: number): Promise<{
    emailExists: boolean;
    emailUpdates: number;
    shipments: number;
    commercialInvoices: number;
    documents: number;
    files: number;
    fileBatches: number;
    emailThreads: number;
  }> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();

    try {
      // Check if email exists
      const email = await queryRunner.manager.findOne(Email, {
        where: { id: emailId },
        relations: ["updates"]
      });

      if (!email) {
        return {
          emailExists: false,
          emailUpdates: 0,
          shipments: 0,
          commercialInvoices: 0,
          documents: 0,
          files: 0,
          fileBatches: 0,
          emailThreads: 0
        };
      }

      const emailUpdateIds = email.updates?.map((update) => update.id) || [];

      // Count all related entities
      const [emailUpdates, shipments, commercialInvoices, documents, files, fileBatches, emailThreads] =
        await Promise.all([
          Promise.resolve(emailUpdateIds.length),
          this.countShipmentsFromEmailUpdates(emailUpdateIds, queryRunner),
          this.countCommercialInvoicesFromEmailUpdates(emailUpdateIds, queryRunner),
          this.countDocumentsFromEmailUpdates(emailUpdateIds, queryRunner),
          this.countFilesFromEmailUpdates(emailUpdateIds, queryRunner),
          this.countFileBatchesFromEmailUpdates(emailUpdateIds, queryRunner),
          queryRunner.manager
            .query(`SELECT COUNT(*) as count FROM email_thread WHERE "threadId" = $1`, [email.threadId])
            .then((result) => parseInt(result[0]?.count || 0))
        ]);

      return {
        emailExists: true,
        emailUpdates,
        shipments,
        commercialInvoices,
        documents,
        files,
        fileBatches,
        emailThreads
      };
    } finally {
      await queryRunner.release();
    }
  }

  private async countShipmentsFromEmailUpdates(
    emailUpdateIds: number[],
    queryRunner: QueryRunner
  ): Promise<number> {
    if (emailUpdateIds.length === 0) return 0;

    const result = await queryRunner.manager.query(
      `
      SELECT COUNT(DISTINCT da."shipmentId") as count
      FROM document_aggregation da
      WHERE da."batchId" = ANY($1::text[])
      AND da."shipmentId" IS NOT NULL
      `,
      [emailUpdateIds.map((id) => id.toString())]
    );

    return parseInt(result[0]?.count || 0);
  }

  private async countCommercialInvoicesFromEmailUpdates(
    emailUpdateIds: number[],
    queryRunner: QueryRunner
  ): Promise<number> {
    if (emailUpdateIds.length === 0) return 0;

    const result = await queryRunner.manager.query(
      `
      SELECT COUNT(DISTINCT ci.id) as count
      FROM commercial_invoice ci
      INNER JOIN document_aggregation da ON da."shipmentId" = ci."shipmentId"
      WHERE da."batchId" = ANY($1::text[])
      `,
      [emailUpdateIds.map((id) => id.toString())]
    );

    return parseInt(result[0]?.count || 0);
  }

  private async countDocumentsFromEmailUpdates(
    emailUpdateIds: number[],
    queryRunner: QueryRunner
  ): Promise<number> {
    if (emailUpdateIds.length === 0) return 0;

    const result = await queryRunner.manager.query(
      `
      SELECT COUNT(DISTINCT d.id) as count
      FROM document d
      INNER JOIN file f ON f.id = d."fileId"
      INNER JOIN file_batch fb ON fb.id = f."batchId"
      INNER JOIN document_aggregation da ON da."batchId" = fb.id
      WHERE da."batchId" = ANY($1::text[])
      `,
      [emailUpdateIds.map((id) => id.toString())]
    );

    return parseInt(result[0]?.count || 0);
  }

  private async countFilesFromEmailUpdates(
    emailUpdateIds: number[],
    queryRunner: QueryRunner
  ): Promise<number> {
    if (emailUpdateIds.length === 0) return 0;

    const result = await queryRunner.manager.query(
      `
      SELECT COUNT(DISTINCT f.id) as count
      FROM file f
      INNER JOIN file_batch fb ON fb.id = f."batchId"
      INNER JOIN document_aggregation da ON da."batchId" = fb.id
      WHERE da."batchId" = ANY($1::text[])
      `,
      [emailUpdateIds.map((id) => id.toString())]
    );

    return parseInt(result[0]?.count || 0);
  }

  private async countFileBatchesFromEmailUpdates(
    emailUpdateIds: number[],
    queryRunner: QueryRunner
  ): Promise<number> {
    if (emailUpdateIds.length === 0) return 0;

    const result = await queryRunner.manager.query(
      `
      SELECT COUNT(DISTINCT fb.id) as count
      FROM file_batch fb
      INNER JOIN document_aggregation da ON da."batchId" = fb.id
      WHERE da."batchId" = ANY($1::text[])
      `,
      [emailUpdateIds.map((id) => id.toString())]
    );

    return parseInt(result[0]?.count || 0);
  }
}
