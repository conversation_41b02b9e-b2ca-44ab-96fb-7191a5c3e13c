import { BadRequestException, Inject, Injectable, Logger, NotFoundException } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import {
  AuthenticatedRequest,
  Document,
  EMAIL_UPDATE_REQUIRED_KEYS,
  EmailUpdate,
  EmailUpdateStatus,
  FIND_EMAIL_UPDATE_RELATIONS,
  UserPermission
} from "nest-modules";
import { DataSource, FindOptionsRelations, In, IsNull, Not, QueryRunner, Repository } from "typeorm";
import {
  BatchUpdateEmailUpdatesDto,
  CreateEmailUpdateDto,
  EditEmailUpdateDto
} from "../dto/email-update.dto";
import { EmailService } from "./email.service";

@Injectable()
export class EmailUpdateService {
  constructor(
    @InjectRepository(EmailUpdate)
    private readonly emailUpdateRepository: Repository<EmailUpdate>,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
    @Inject(EmailService)
    private readonly emailService: EmailService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}
  private readonly logger = new Logger(EmailUpdateService.name);

  private async updateAndValidateEmailUpdate(
    updateRecord: EmailUpdate,
    createOrUpdateDto: CreateEmailUpdateDto | EditEmailUpdateDto,
    queryRunner?: QueryRunner,
    errorMessagePrefix = ""
  ) {
    const documentRepository = queryRunner
      ? queryRunner.manager.getRepository(Document)
      : this.documentRepository;

    for (const [key, value] of Object.entries(createOrUpdateDto)) {
      if (value === undefined) continue;
      switch (key) {
        case "emailId":
          if (typeof value === "number") {
            updateRecord.email = await this.emailService.getEmailById(value, queryRunner);
            if (!updateRecord.email) throw new NotFoundException(`${errorMessagePrefix}Email not found`);
          } else updateRecord.email = null;
          break;
        case "documentIds":
          if (Array.isArray(value) && value.every((id) => typeof id === "number")) {
            const filteredValue = value.reduce((ids, id) => {
              if (!ids.includes(id)) ids.push(id);
              return ids;
            }, [] as Array<number>);
            updateRecord.documents = await documentRepository.findBy({
              id: In(filteredValue)
            });
            if (updateRecord.documents?.length !== filteredValue.length)
              throw new NotFoundException(`${errorMessagePrefix}Documents not found`);
          } else updateRecord.documents = null;
          break;
        default:
          updateRecord[key] = value;
          break;
      }
    }

    const missingRequiredKeys = EMAIL_UPDATE_REQUIRED_KEYS.filter((key) =>
      [null, undefined].includes(updateRecord[key])
    );
    if (missingRequiredKeys.length > 0)
      throw new BadRequestException(
        `${errorMessagePrefix}The following fields are required: ${missingRequiredKeys.join(", ")}`
      );

    if (
      !updateRecord.updateJson &&
      !(Array.isArray(updateRecord.documents) && updateRecord.documents.length > 0)
    )
      throw new BadRequestException(
        `${errorMessagePrefix}Update JSON and documents cannot be empty at the same time`
      );

    if (updateRecord.status === EmailUpdateStatus.FAILED && !updateRecord.failedReasons)
      throw new BadRequestException(`Failed reasons must be provided when status is failed`);

    // TODO: Add validation for document's organization and update record's organization

    return updateRecord;
  }

  async getEmailUpdateById(emailUpdateId: number, queryRunner?: QueryRunner, skipOrganizationCheck = false) {
    return (
      queryRunner ? queryRunner.manager.getRepository(EmailUpdate) : this.emailUpdateRepository
    ).findOne({
      where: {
        id: emailUpdateId,
        email: {
          organization: {
            id:
              skipOrganizationCheck || this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
                ? Not(IsNull())
                : this.request?.user?.organization?.id || -1
          }
        }
      },
      relations: FIND_EMAIL_UPDATE_RELATIONS
    });
  }

  /**
   * Get all email updates by Email ID
   * @param emailId
   * @param queryRunner
   * @param skipOrganizationCheck
   * @param relations - Relations to include in the query
   * @returns
   */
  async getEmailUpdatesByEmailId(
    emailId: number,
    queryRunner?: QueryRunner,
    skipOrganizationCheck = false,
    relations: FindOptionsRelations<EmailUpdate> = FIND_EMAIL_UPDATE_RELATIONS
  ) {
    return (queryRunner ? queryRunner.manager.getRepository(EmailUpdate) : this.emailUpdateRepository).find({
      where: {
        email: {
          id: emailId,
          organization: {
            id:
              skipOrganizationCheck || this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
                ? Not(IsNull())
                : this.request?.user?.organization?.id || -1
          }
        }
      },
      relations
    });
  }

  /**
   * Get all email updates by Gmail Thread ID
   * @param threadId - Gmail Thread ID
   * @param queryRunner - QueryRunner instance. Used for handling transactions
   * @param skipOrganizationCheck - Whether to skip organization check
   * @returns All email updates by Gmail Thread ID
   */
  async getEmailUpdatesByThreadId(
    threadId: string,
    queryRunner?: QueryRunner,
    skipOrganizationCheck = false
  ) {
    return (queryRunner ? queryRunner.manager.getRepository(EmailUpdate) : this.emailUpdateRepository).find({
      where: {
        email: {
          threadId,
          organization: {
            id:
              skipOrganizationCheck || this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
                ? Not(IsNull())
                : this.request?.user?.organization?.id || -1
          }
        }
      },
      relations: FIND_EMAIL_UPDATE_RELATIONS
    });
  }

  async createEmailUpdate(createEmailUpdateDto: CreateEmailUpdateDto, queryRunner?: QueryRunner) {
    if (!this.request?.user?.organization) throw new NotFoundException("Unknown organization");

    let newUpdate = new EmailUpdate();
    newUpdate = await (
      queryRunner ? queryRunner.manager.getRepository(EmailUpdate) : this.emailUpdateRepository
    ).save(await this.updateAndValidateEmailUpdate(newUpdate, createEmailUpdateDto, queryRunner));

    return await this.getEmailUpdateById(newUpdate.id, queryRunner);
  }

  async editEmailUpdate(
    emailUpdateId: number,
    editEmailUpdateDto: EditEmailUpdateDto,
    queryRunner?: QueryRunner
  ) {
    if (!this.request?.user?.organization) throw new NotFoundException("Unknown organization");

    let updateRecord = await this.getEmailUpdateById(emailUpdateId, queryRunner);
    if (!updateRecord) throw new NotFoundException("Email update not found");

    updateRecord = await (
      queryRunner ? queryRunner.manager.getRepository(EmailUpdate) : this.emailUpdateRepository
    ).save(await this.updateAndValidateEmailUpdate(updateRecord, editEmailUpdateDto, queryRunner));

    return await this.getEmailUpdateById(updateRecord.id, queryRunner);
  }

  async batchUpdateEmailUpdates(
    batchUpdateEmailUpdatesDto: BatchUpdateEmailUpdatesDto,
    queryRunner?: QueryRunner
  ) {
    let tQueryRunner = queryRunner;
    if (!queryRunner) {
      tQueryRunner = this.dataSource.createQueryRunner();
      tQueryRunner.connect();
      tQueryRunner.startTransaction();
    }

    try {
      if (!this.request?.user?.organization) throw new NotFoundException("Unknown organization");

      const { create, edit } = batchUpdateEmailUpdatesDto;
      const toBeSavedEmailUpdates: Array<EmailUpdate> = [];

      for (let i = 0; i < (create ?? []).length; i++) {
        const errorMessagePrefix = `Error on create list index ${i}: `;
        const createEmailUpdateDto = create[i];
        let newUpdate = new EmailUpdate();
        newUpdate.status = EmailUpdateStatus.PENDING;
        newUpdate = await this.updateAndValidateEmailUpdate(
          newUpdate,
          createEmailUpdateDto,
          tQueryRunner,
          errorMessagePrefix
        );
        toBeSavedEmailUpdates.push(newUpdate);
      }

      for (let i = 0; i < (edit ?? []).length; i++) {
        const errorMessagePrefix = `Error on edit list index ${i}: `;
        const editEmailUpdateDto = edit[i];
        let updateRecord = await this.getEmailUpdateById(editEmailUpdateDto.id, tQueryRunner);
        if (!updateRecord) throw new NotFoundException("Email update not found");
        updateRecord = await this.updateAndValidateEmailUpdate(
          updateRecord,
          editEmailUpdateDto,
          tQueryRunner,
          errorMessagePrefix
        );
        toBeSavedEmailUpdates.push(updateRecord);
      }

      const savedEmailUpdates = await tQueryRunner.manager
        .getRepository(EmailUpdate)
        .save(toBeSavedEmailUpdates);

      if (!queryRunner) await tQueryRunner.commitTransaction();

      return savedEmailUpdates;
    } catch (error) {
      if (!queryRunner) await tQueryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner) await tQueryRunner.release();
    }
  }
}
