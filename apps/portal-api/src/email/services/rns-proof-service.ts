import { Injectable, Logger, Scope } from "@nestjs/common";
import { CandataService, CandataRNSResponseDto, Shipment } from "nest-modules";
import { categorizeRNSResponse } from "@/shipment/utils/categorize-rns-response";
import { RNSResponseCategory } from "@/shipment/types/customs-status.types";

export interface RNSProofOfReleaseResult {
  isReleased: boolean;
  rnsResponse: CandataRNSResponseDto | null;
  releaseDate: string | null;
}

@Injectable({ scope: Scope.REQUEST })
export class RnsProofService {
  private readonly logger = new Logger(RnsProofService.name);

  constructor(private readonly candataService: CandataService) {}

  /**
   * Gets RNS proof of release data for a shipment.
   * Replaces the non-existent getRNSProofOfRelease tool call from core-agent.
   *
   * @param shipment - The shipment to get RNS proof for
   * @returns RNS proof of release data including release status and response
   */
  async getRNSProofOfRelease(shipment: Shipment): Promise<RNSProofOfReleaseResult> {
    this.logger.log(`Getting RNS proof of release for shipment ${shipment.id}`);

    try {
      // Get RNS responses for the shipment using cargo control number
      const rnsResponses = await this.candataService.findRnsResponseByCargoControlNumbers(
        [shipment.cargoControlNumber],
        shipment.organization?.customsBroker
      );

      if (!rnsResponses || rnsResponses.length === 0) {
        this.logger.warn(`No RNS responses found for shipment ${shipment.id}`);
        return {
          isReleased: false,
          rnsResponse: null,
          releaseDate: null
        };
      }

      // Filter and sort RNS responses to find the latest valid one
      const filteredRNSResponses = rnsResponses
        .filter((r) => categorizeRNSResponse(r) !== RNSResponseCategory.UNKNOWN)
        .sort((r1, r2) => new Date(r2.responseDate).getTime() - new Date(r1.responseDate).getTime());

      if (filteredRNSResponses.length === 0) {
        this.logger.warn(`No valid RNS responses found for shipment ${shipment.id}`);
        return {
          isReleased: false,
          rnsResponse: null,
          releaseDate: null
        };
      }

      // Find the latest RELEASED response
      const releasedResponse = filteredRNSResponses.find(
        (r) => categorizeRNSResponse(r) === RNSResponseCategory.RELEASED
      );

      if (!releasedResponse) {
        this.logger.log(`No RELEASED RNS response found for shipment ${shipment.id}`);
        return {
          isReleased: false,
          rnsResponse: filteredRNSResponses[0], // Return latest response even if not released
          releaseDate: null
        };
      }

      this.logger.log(`Found RELEASED RNS response for shipment ${shipment.id}`);
      return {
        isReleased: true,
        rnsResponse: releasedResponse,
        releaseDate: releasedResponse.responseDate
      };
    } catch (error) {
      this.logger.error(
        `Failed to get RNS proof of release for shipment ${shipment.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }
}
