import { ImporterService } from "@/importer/importer.service";
import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { REQUEST } from "@nestjs/core";
import { EventEmitter2, OnEvent } from "@nestjs/event-emitter";
import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import moment from "moment-timezone";
import {
  AuthenticatedRequest,
  Email,
  EMAIL_ENUM_KEYS,
  EMAIL_NON_ID_KEYS,
  EMAIL_REQUIRED_KEYS,
  EmailColumn,
  EmailOrigin,
  EmailStatus,
  EmailThread,
  EmailWithAttachments,
  FileBatch,
  FIND_EMAIL_RELATIONS,
  FIND_SHIPMENT_RELATIONS,
  GetEmailsDto,
  GetEmailsResponseDto,
  getQueryConditions,
  Importer,
  Organization,
  OrganizationService,
  OrganizationType,
  queryBuilderRecursiveLeftJoin,
  Shipment,
  SortOrder,
  TemplateManagerService,
  UserPermission
} from "nest-modules";
import { DataSource, In, IsNull, LessThanOrEqual, Not, QueryRunner, Repository } from "typeorm";
import { CreateEmailDto, EditEmailDto, ReplyEmailDto, SendEmailDto } from "../dto/email.dto";
import {
  EmailAggregatedEvent,
  EmailIntentsExtractedEvent,
  EmailIntentsProcessedEvent,
  ThreadShipmentFoundEvent,
  ThreadShipmentNotFoundEvent
} from "../dto/event.dto";
import { EmailSavedEvent, EMAIL_SAVED } from "nest-modules";
import { EmailTemplateName } from "../types/email.types";
import { EmailEvent } from "../types/event.types";
import { GmailMessageFormat } from "../types/gmail.types";
import { GmailService } from "./gmail.service";
import { ImporterEvent } from "@/importer/types/event.types";
import { NODE_ENV } from "@/app.module";
import { NodeEnv } from "nest-modules";
import { FileBatchService } from "@/document/services/file-batch.service";

const EMAIL_DOMAIN_DEFAULT = "clarocustoms.com";

@Injectable({ scope: Scope.REQUEST })
export class EmailService {
  constructor(
    @InjectRepository(Email)
    private readonly emailRepository: Repository<Email>,
    @InjectRepository(EmailThread)
    private readonly emailThreadRepository: Repository<EmailThread>,
    @InjectRepository(FileBatch)
    private readonly fileBatchRepository: Repository<FileBatch>,
    @Inject(TemplateManagerService)
    private readonly templateManagerService: TemplateManagerService,
    @Inject(GmailService)
    private readonly gmailService: GmailService,
    @Inject(OrganizationService)
    private readonly organizationService: OrganizationService,
    @Inject(FileBatchService)
    private readonly fileBatchService: FileBatchService,
    @Inject(forwardRef(() => ImporterService))
    private readonly importerService: ImporterService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly eventEmitter: EventEmitter2,
    @Inject(ConfigService)
    private readonly configService: ConfigService
  ) {}
  private readonly logger = new Logger(EmailService.name);

  private static readonly MULTIPART_BOUNDARY_LENGTH = 28;

  private get SYSTEM_FROM_EMAIL(): string {
    return this.configService.get<string>("SYSTEM_FROM_EMAIL");
  }

  private get BACKOFFICE_EMAIL(): string {
    return this.configService.get<string>("BACKOFFICE_EMAIL_ADDRESS");
  }

  /**
   * Encode a message to base64
   * @param message - Message to encode
   * @returns Base64 encoded message
   */
  private toBase64(message: string) {
    return Buffer.from(message).toString("base64");
  }

  /**
   * Encode a message to base64url
   * @param message - Message to encode
   * @returns Base64Url encoded message
   */
  private toBase64Url(message: string) {
    return Buffer.from(message).toString("base64url");
  }

  /**
   * Create a multipart boundary for a given length.
   * @param length - Length of the boundary
   * @returns Multipart boundary
   */
  private createMultipartBoundary(length: number = EmailService.MULTIPART_BOUNDARY_LENGTH) {
    const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    const boundaryCharList = Array.from(new Array(Math.max(length, 0)), () =>
      characters.charAt(Math.floor(Math.random() * characters.length))
    );
    return boundaryCharList.join("");
  }

  private async updateAndValidateEmail(
    email: Email,
    createOrEditDto: CreateEmailDto | EditEmailDto,
    queryRunner?: QueryRunner
  ) {
    const emailRepository = queryRunner ? queryRunner.manager.getRepository(Email) : this.emailRepository;

    email.lastEditedBy = this.request?.user?.id ? this.request?.user : null;
    for (const [key, value] of Object.entries(createOrEditDto)) {
      if (value === undefined) continue;
      email[key] = value;
    }
    const missingRequiredKeys = EMAIL_REQUIRED_KEYS.filter((key) => [null, undefined].includes(email[key]));
    if (missingRequiredKeys.length > 0)
      throw new BadRequestException(`The following fields are required: ${missingRequiredKeys.join(", ")}`);

    if (
      await emailRepository.existsBy({
        id: Not(email?.id ? email.id : IsNull()),
        gmailId: email.gmailId
      })
    )
      throw new BadRequestException(`Another email with the same Gmail ID already exists`);

    if (!email.status.startsWith("failed-") && email.status !== EmailStatus.MANUAL_REVIEW) email.error = null;

    // TODO: Check if all emails in the same thread belong to the same organization

    return email;
  }

  async getEmailThreadShipment(threadId: string, queryRunner?: QueryRunner) {
    const emailThread = await (
      queryRunner ? queryRunner.manager.getRepository(EmailThread) : this.emailThreadRepository
    ).findOne({
      where: {
        threadId
      },
      relations: { shipment: FIND_SHIPMENT_RELATIONS }
    });
    return emailThread?.shipment || null;
  }

  async setEmailThreadShipment(
    threadId: string,
    shipment: Shipment,
    queryRunner?: QueryRunner,
    skipOrganizationCheck = false
  ) {
    const emailThreadRepository = queryRunner
      ? queryRunner.manager.getRepository(EmailThread)
      : this.emailThreadRepository;
    if (await emailThreadRepository.existsBy({ threadId }))
      throw new ConflictException("Email thread is already associated with a shipment");

    const emails = await this.getEmailsByThreadId(threadId, null, null, queryRunner, skipOrganizationCheck);
    if (emails?.length <= 0) throw new NotFoundException("No emails found for this thread");
    if (emails[0].organization?.id !== shipment?.organization?.id)
      throw new BadRequestException("Shipment and thread belong to different organization");

    let emailThread = new EmailThread();
    emailThread.threadId = threadId;
    emailThread.shipment = shipment;
    emailThread = await emailThreadRepository.save(emailThread);
    return await this.getEmailThreadShipment(threadId, queryRunner);
  }

  async getEmailsByThreadId(
    threadId: string,
    earlierThan?: Date,
    status?: EmailStatus,
    queryRunner?: QueryRunner,
    skipOrganizationCheck = false
  ) {
    return await (queryRunner ? queryRunner.manager.getRepository(Email) : this.emailRepository).find({
      where: {
        threadId,
        status: status || Not(IsNull()),
        receiveDate: earlierThan ? LessThanOrEqual(earlierThan) : Not(IsNull()),
        organization: {
          id:
            skipOrganizationCheck || this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
              ? Not(IsNull())
              : this.request?.user?.organization?.id || -1
        }
      },
      order: {
        receiveDate: SortOrder.DESC
      },
      relations: FIND_EMAIL_RELATIONS
    });
  }

  async getPreviousEmails(emailId: number, queryRunner?: QueryRunner) {
    const email = await this.getEmailById(emailId, queryRunner);
    if (!email) throw new NotFoundException("Email not found");

    return (await this.getEmailsByThreadId(email.threadId, email.receiveDate, null, queryRunner)).slice(1);
  }

  async getLatestEmail(email: string, origin = EmailOrigin.INBOX) {
    return await this.emailRepository.findOne({
      where: {
        inboxEmail: email,
        origin
      },
      order: {
        receiveDate: SortOrder.DESC,
        historyId: SortOrder.DESC
      }
    });
  }

  async getEmailByGmailId(gmailId: string, queryRunner?: QueryRunner, skipOrganizationCheck = false) {
    return await (queryRunner ? queryRunner.manager.getRepository(Email) : this.emailRepository).findOne({
      where: {
        gmailId,
        organization: {
          id:
            skipOrganizationCheck || this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
              ? Not(IsNull())
              : this.request?.user?.organization?.id || -1
        }
      },
      relations: FIND_EMAIL_RELATIONS
    });
  }

  /**
   * Get an email by its ID
   * @param emailId - ID of the email to get
   * @param queryRunner - QueryRunner instance. Used for handling transactions
   * @param organization - Organization to associate with the email. If not provided, the current user's organization will be used
   * @returns The email record
   */
  async getEmailById(emailId: number, queryRunner?: QueryRunner, organization?: Organization) {
    return await (queryRunner ? queryRunner.manager.getRepository(Email) : this.emailRepository).findOne({
      where: {
        id: emailId,
        organization: {
          id:
            this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
              ? Not(IsNull())
              : organization?.id || this.request?.user?.organization?.id || -1
        }
      },
      relations: FIND_EMAIL_RELATIONS
    });
  }

  async getEmails(getEmailsDto: GetEmailsDto, queryRunner?: QueryRunner): Promise<GetEmailsResponseDto> {
    const QUERY_ALIAS = "email";
    function _emailAddressSubQuery(key: string) {
      return `(SELECT SUM(CASE WHEN ${["name", "address"].map((k) => `LOWER(value->>'${k}') LIKE LOWER('%' || replace(replace(replace(:${key}, '\\', '\\\\'), '%', '\\%'), '_', '\\_') || '%')`).join(" OR ")} THEN 1 ELSE 0 END) FROM jsonb_array_elements("${QUERY_ALIAS}"."${key}")) > 0`;
    }

    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      getEmailsDto.organizationId = this.request?.user?.organization?.id || -1;
    const { includeAttachments, shipmentId, from, to, cc, replyTo, content, ...dto } = getEmailsDto;
    const { conditions, values, sortBy, sortOrder, skip, take } = getQueryConditions(
      dto,
      EMAIL_ENUM_KEYS,
      EMAIL_NON_ID_KEYS,
      EmailColumn.id,
      QUERY_ALIAS
    );
    if (from) {
      conditions.push(_emailAddressSubQuery("from"));
      values.from = from;
    }
    if (to) {
      conditions.push(_emailAddressSubQuery("to"));
      values.to = to;
    }
    if (cc) {
      conditions.push(_emailAddressSubQuery("cc"));
      values.cc = cc;
    }
    if (replyTo) {
      conditions.push(_emailAddressSubQuery("replyTo"));
      values.replyTo = replyTo;
    }
    if (content) {
      conditions.push(
        `(LOWER("${QUERY_ALIAS}"."text") LIKE LOWER(:content) OR LOWER("${QUERY_ALIAS}"."html") LIKE LOWER(:content))`
      );
      values.content = `%${content}%`;
    }
    if (shipmentId) {
      conditions.push(`"thread"."shipmentId" = :shipmentId`);
      values.shipmentId = shipmentId;
    }

    let queryBuilder = (queryRunner ? queryRunner.manager : this.dataSource).createQueryBuilder(
      Email,
      QUERY_ALIAS,
      queryRunner
    );
    queryBuilder = queryBuilderRecursiveLeftJoin(queryBuilder, QUERY_ALIAS, FIND_EMAIL_RELATIONS).leftJoin(
      EmailThread,
      "thread",
      `thread."threadId" = ${QUERY_ALIAS}."threadId"`
    );

    const [data, total] = await queryBuilder
      .where(conditions.join(" AND "), values)
      .orderBy(`${QUERY_ALIAS}.${sortBy}`, sortOrder)
      .skip(skip)
      .take(take)
      .getManyAndCount();

    if (includeAttachments && data.length > 0) {
      const fileBatches = await (
        queryRunner ? queryRunner.manager.getRepository(FileBatch) : this.fileBatchRepository
      ).find({
        where: {
          id: In(data.map((email) => email.gmailId))
        },
        relations: {
          files: true
        }
      });
      for (let i = 0; i < data.length; i++) {
        (data[i] as EmailWithAttachments).files =
          fileBatches.find((batch) => batch.id === data[i].gmailId)?.files || [];
      }
    }

    return {
      data,
      total,
      skip,
      limit: take
    };
  }

  /**
   * Create a new email record
   * @param createEmailDto - DTO for creating an email
   * @param queryRunner - QueryRunner instance. Used for handling transactions
   * @param organization - Organization to associate with the email. If not provided, the current user's organization will be used
   * @returns The created email record
   */
  async createEmail(createEmailDto: CreateEmailDto, queryRunner?: QueryRunner, organization?: Organization) {
    if (!(organization || this.request?.user?.organization))
      throw new NotFoundException("Unknown organization");

    let newEmail = new Email();
    newEmail.organization = organization || this.request?.user?.organization;
    newEmail.createdBy = this.request?.user?.id ? this.request?.user : null;
    newEmail = await this.updateAndValidateEmail(newEmail, createEmailDto);
    this.logger.log(`New email validated`);

    newEmail = await (queryRunner ? queryRunner.manager.getRepository(Email) : this.emailRepository).save(
      newEmail
    );
    return await this.getEmailById(newEmail.id, queryRunner, organization);
  }

  /**
   * Edit an existing email record
   * @param emailId - ID of the email to edit
   * @param editEmailDto - DTO for editing an email
   * @param queryRunner - QueryRunner instance. Used for handling transactions
   * @param organization - Organization to associate with the email. If not provided, the current user's organization will be used
   * @returns The edited email record
   */
  async editEmail(
    emailId: number,
    editEmailDto: EditEmailDto,
    queryRunner?: QueryRunner,
    organization?: Organization
  ) {
    if (!(organization || this.request?.user?.organization))
      throw new NotFoundException("Unknown organization");

    let email = await this.getEmailById(emailId, queryRunner, organization);
    if (!email) throw new NotFoundException("Email not found");

    email = await this.updateAndValidateEmail(email, editEmailDto);
    email = await (queryRunner ? queryRunner.manager.getRepository(Email) : this.emailRepository).save(email);

    return await this.getEmailById(email.id, queryRunner, organization);
  }

  /**
   * Delete an existing email record
   * @param emailId - ID of the email to delete
   * @param queryRunner - QueryRunner instance. Used for handling transactions
   * @param organization - Organization to associate with the email. If not provided, the current user's organization will be used
   */
  async deleteEmail(emailId: number, queryRunner?: QueryRunner, organization?: Organization) {
    if (!(organization || this.request?.user?.organization))
      throw new NotFoundException("Unknown organization");

    const email = await this.getEmailById(emailId, queryRunner, organization);
    if (!email) throw new NotFoundException("Email not found");

    await (queryRunner ? queryRunner.manager.getRepository(Email) : this.emailRepository).delete({
      id: email.id
    });

    return;
  }

  /**
   * Render an email template
   * @param templateName - Name of the template to render
   * @param data - Data to pass to the template
   * @returns The rendered template string
   */
  renderEmailTemplate(templateName: EmailTemplateName, data: Record<string, any>) {
    const rendered = this.templateManagerService.renderTemplate(templateName, data);
    return rendered;
  }

  /**
   * Render an email template with both subject and body.
   * @param templateName - Name of the template to render
   * @param data - Data to pass to the template
   * @returns The rendered template string with both subject and body
   */
  renderEmailTemplateWithSubject(templateName: EmailTemplateName, data: Record<string, any>) {
    const renderedTemplate = this.renderEmailTemplate(templateName, data);
    const sections = renderedTemplate.split(/^# (subject|body):/m);
    const result = {
      subject: null,
      body: null
    };
    for (let i = 1; i < sections.length; i += 2) {
      const sectionName = sections[i] as "subject" | "body";
      const content = sections[i + 1]?.trim() || "";

      if (sectionName === "subject") result.subject = content;
      else result.body = content;
    }

    return result;
  }

  /**
   * Send an email
   * @param sendEmailDto - DTO for sending an email
   * @param queryRunner - QueryRunner instance. Used for handling transactions
   * @param organization - Organization to associate with the email. If not provided, the current user's organization will be used
   * @returns The sent email record
   */
  async sendEmail(
    sendEmailDto: SendEmailDto,
    queryRunner?: QueryRunner,
    organization?: Organization
  ): Promise<Email | null> {
    if (this.configService.get<string>("DISABLE_SEND_EMAIL_ALL") === "true" && NODE_ENV === NodeEnv.LOCAL) {
      this.logger.warn(
        `Email sending is globally disabled via DISABLE_SEND_EMAIL_ALL. Suppressed email to: ${sendEmailDto.to.join(
          ", "
        )}, subject: '${sendEmailDto.subject}'`
      );
      return null;
    }

    if (!(organization || this.request?.user?.organization))
      throw new NotFoundException("Unknown organization");

    // Generate Message-ID for threading
    const domain = this.configService.get<string>("SYSTEM_FROM_EMAIL")?.split("@")[1] || EMAIL_DOMAIN_DEFAULT;
    const newMessageId = `<${Date.now()}.${Math.random().toString(36).substr(2, 9)}@${domain}>`;

    // Get In-Reply-To for threading (if replying)
    let inReplyTo: string | null = null;
    if (sendEmailDto.inReplyToMessageId) {
      // Use the specific Message-ID if provided (most accurate)
      inReplyTo = sendEmailDto.inReplyToMessageId;
    } else if (sendEmailDto.threadId) {
      // Fallback to thread-based lookup for backward compatibility
      try {
        const threadEmails = await this.getEmailsByThreadId(
          sendEmailDto.threadId,
          null,
          null,
          queryRunner,
          true
        );
        const latestInboxEmail = threadEmails
          .filter((e) => e.origin === EmailOrigin.INBOX)
          .sort((a, b) => b.receiveDate.getTime() - a.receiveDate.getTime())[0];
        inReplyTo = latestInboxEmail?.messageId || null;
      } catch (error) {
        this.logger.warn(`Could not fetch thread emails for In-Reply-To: ${error.message}`);
      }
    }

    // Construct RFC2822 message
    const hasAttachments = sendEmailDto.attachments && sendEmailDto.attachments.length > 0;
    const hasText = !!sendEmailDto.text;
    const hasHtml = !!sendEmailDto.html;
    const hasBothTextAndHtml = hasText && hasHtml;

    this.logger.log(
      `[PDF_ATTACHMENT_TRACKING] Sending email with ${sendEmailDto.attachments?.length || 0} attachments`
    );

    // Determine content type based on content complexity
    let contentType: string;
    let boundary: string | undefined;
    const bodyList: string[] = [];

    if (hasAttachments) {
      // Use multipart/mixed when there are attachments
      contentType = "multipart/mixed";
      boundary = this.createMultipartBoundary(28);
      bodyList.push(`Content-Type: ${contentType}; boundary="${boundary}"`, "MIME-Version: 1.0");
    } else if (hasBothTextAndHtml) {
      // Use multipart/alternative for both text and HTML content
      contentType = "multipart/alternative";
      boundary = this.createMultipartBoundary(28);
      bodyList.push(`Content-Type: ${contentType}; boundary="${boundary}"`, "MIME-Version: 1.0");
    } else if (hasHtml) {
      // Single HTML content
      contentType = 'text/html; charset="UTF-8"';
      bodyList.push(`Content-Type: ${contentType}`, "Content-Transfer-Encoding: 7bit", "MIME-Version: 1.0");
    } else {
      // Single text content or no content
      contentType = 'text/plain; charset="UTF-8"';
      bodyList.push(`Content-Type: ${contentType}`, "Content-Transfer-Encoding: 7bit", "MIME-Version: 1.0");
    }

    // Add threading headers
    bodyList.push(`Message-ID: ${newMessageId}`);
    if (inReplyTo) {
      bodyList.push(`In-Reply-To: ${inReplyTo}`);
    }

    if (sendEmailDto.subject) bodyList.push(`Subject: =?UTF-8?B?${this.toBase64(sendEmailDto.subject)}?=`);
    bodyList.push(`From: ${sendEmailDto.from}`);
    bodyList.push(`To: ${sendEmailDto.to.join(", ")}`);
    if (Array.isArray(sendEmailDto.cc) && sendEmailDto.cc.length > 0)
      bodyList.push(`Cc: ${sendEmailDto.cc.join(", ")}`);
    if (Array.isArray(sendEmailDto.bcc) && sendEmailDto.bcc.length > 0)
      bodyList.push(`Bcc: ${sendEmailDto.bcc.join(", ")}`);
    if (sendEmailDto.replyTo) bodyList.push(`Reply-To: ${sendEmailDto.replyTo}`);
    bodyList.push("");

    // Add email body content based on content type
    if (hasAttachments) {
      // Multipart/mixed structure with attachments
      if (hasBothTextAndHtml) {
        // Nested multipart structure: mixed containing alternative
        const altBoundary = this.createMultipartBoundary(28);
        bodyList.push(
          `--${boundary}`,
          `Content-Type: multipart/alternative; boundary="${altBoundary}"`,
          "",
          `--${altBoundary}`,
          `Content-Type: text/plain; charset="UTF-8"`,
          "Content-Transfer-Encoding: 7bit",
          "",
          sendEmailDto.text,
          "",
          `--${altBoundary}`,
          `Content-Type: text/html; charset="UTF-8"`,
          "Content-Transfer-Encoding: 7bit",
          "",
          sendEmailDto.html,
          "",
          `--${altBoundary}--`,
          ""
        );
      } else {
        // Single content type with attachments
        if (hasText) {
          bodyList.push(
            `--${boundary}`,
            `Content-Type: text/plain; charset="UTF-8"`,
            "Content-Transfer-Encoding: 7bit",
            "",
            sendEmailDto.text,
            ""
          );
        }
        if (hasHtml) {
          bodyList.push(
            `--${boundary}`,
            `Content-Type: text/html; charset="UTF-8"`,
            "Content-Transfer-Encoding: 7bit",
            "",
            sendEmailDto.html,
            ""
          );
        }
      }

      // Add attachments
      for (const attachment of sendEmailDto.attachments || []) {
        bodyList.push(
          `--${boundary}`,
          `Content-Type: ${attachment.mimeType}`,
          "Content-Transfer-Encoding: base64",
          `Content-Disposition: attachment; filename="${this.sanitizeFilename(attachment.fileName)}"`,
          "",
          attachment.b64Data,
          ""
        );
      }

      bodyList.push(`--${boundary}--`);
    } else if (hasBothTextAndHtml) {
      // Multipart/alternative structure (text + HTML, no attachments)
      bodyList.push(
        `--${boundary}`,
        `Content-Type: text/plain; charset="UTF-8"`,
        "Content-Transfer-Encoding: 7bit",
        "",
        sendEmailDto.text,
        "",
        `--${boundary}`,
        `Content-Type: text/html; charset="UTF-8"`,
        "Content-Transfer-Encoding: 7bit",
        "",
        sendEmailDto.html,
        "",
        `--${boundary}--`
      );
    } else {
      // Single part content (text OR HTML, no attachments)
      if (hasHtml) {
        bodyList.push(sendEmailDto.html);
      } else if (hasText) {
        bodyList.push(sendEmailDto.text);
      }
      // Note: No boundary markers needed for single part emails
    }
    const rawRfc2822Message = bodyList.join("\n");

    // Send message to Gmail
    const gmailRequestPayload = {
      raw: this.toBase64Url(rawRfc2822Message),
      threadId: sendEmailDto.threadId || undefined
    };

    const newGmailMessage = await this.gmailService.sendMessage(sendEmailDto.from, gmailRequestPayload);
    if (!newGmailMessage?.id) throw new InternalServerErrorException("Failed to send reply email to Gmail");
    const fullGmailMessage = await this.gmailService.getMessage(
      sendEmailDto.from,
      newGmailMessage.id,
      GmailMessageFormat.FULL
    );
    if (!fullGmailMessage?.id || !fullGmailMessage?.threadId || !fullGmailMessage?.historyId)
      throw new InternalServerErrorException(`No valid Gmail message returned`);

    // Validate full Gmail Message
    const parsedMessage = await this.gmailService.parseGmailMessage(sendEmailDto.from, fullGmailMessage);

    // Create new email record
    const createEmailDto = new CreateEmailDto();
    createEmailDto.status = EmailStatus.RESPONDED;
    createEmailDto.origin = EmailOrigin.SENT;
    createEmailDto.inboxEmail = sendEmailDto.from;
    createEmailDto.gmailId = fullGmailMessage.id;
    createEmailDto.threadId = fullGmailMessage.threadId;
    createEmailDto.historyId = fullGmailMessage.historyId;
    createEmailDto.receiveDate = moment
      .tz(Number.parseInt(fullGmailMessage.internalDate), "America/Toronto")
      .toDate();
    createEmailDto.from = parsedMessage.from;
    createEmailDto.replyTo = parsedMessage.replyTo;
    createEmailDto.to = parsedMessage.to;
    createEmailDto.cc = parsedMessage.cc;
    createEmailDto.subject = parsedMessage.subject;
    createEmailDto.text = parsedMessage.text;
    createEmailDto.html = parsedMessage.html;
    createEmailDto.messageId = newMessageId;

    return await this.createEmail(createEmailDto, queryRunner, organization);
  }

  /**
   * Reply to an incoming email
   * @param emailId - ID of the email to reply to
   * @param replyEmailDto - DTO for replying to an email
   * @param queryRunner - QueryRunner instance. Used for handling transactions
   * @returns The replied email record
   */
  async replyEmail(
    emailId: number,
    replyEmailDto: ReplyEmailDto,
    queryRunner?: QueryRunner
  ): Promise<Email | null> {
    if (this.configService.get<string>("DISABLE_SEND_REPLY_EMAIL") === "true" && NODE_ENV === NodeEnv.LOCAL) {
      this.logger.warn(
        `Reply email sending is disabled via DISABLE_SEND_REPLY_EMAIL for email ID: ${emailId}.`
      );
      return null;
    }

    if (!this.request?.user?.organization) throw new NotFoundException("Unknown organization");

    const { replyTo, text, html, attachments } = replyEmailDto;

    this.logger.log(
      `[PDF_ATTACHMENT_TRACKING] Replying to email with ${attachments?.length || 0} attachments`
    );

    const email = await this.getEmailById(emailId, queryRunner);
    if (!email) throw new NotFoundException("Email not found");
    if (email.origin !== EmailOrigin.INBOX)
      throw new BadRequestException("Cannot reply to emails not coming from inbox");

    // Construct SendEmailDto
    const sendEmailDto = new SendEmailDto();

    // Skip thread ID for test emails to avoid "Invalid thread_id" errors
    const isTestEmail = email.gmailId?.startsWith("test-") || email.threadId?.startsWith("thread-test-");
    if (!isTestEmail) {
      sendEmailDto.threadId = email.threadId;
    } else {
      this.logger.log(`Skipping thread ID for test email ${email.id} to avoid Gmail API errors`);
    }

    if (email.messageId) {
      sendEmailDto.inReplyToMessageId = email.messageId;
    }
    sendEmailDto.from = this.SYSTEM_FROM_EMAIL; // Always send from system email, not inbox email
    sendEmailDto.to = (
      Array.isArray(email.replyTo) && email.replyTo.length > 0 ? email.replyTo : email.from
    ).map(({ address }) => address);

    // Set reply-to to the original received email address (importer's receiveEmail) if not explicitly provided
    if (replyTo) {
      sendEmailDto.replyTo = replyTo;
      this.logger.log(`Reply-to explicitly set to: ${replyTo} for email ${emailId}`);
    } else {
      // Find importer related to email to get the original received email address
      try {
        // Safely extract and validate 'to' addresses
        const toEmails =
          email.to
            ?.map(({ address }) => address)
            .filter((addr) => addr && typeof addr === "string" && addr.includes("@")) || [];

        if (toEmails.length === 0) {
          this.logger.warn(
            `No valid 'to' addresses found for email ${emailId}, cannot set reply-to from importer. Original 'to': ${JSON.stringify(email.to)}`
          );
          sendEmailDto.replyTo = undefined;
        } else {
          this.logger.log(
            `Looking up importer for reply-to using addresses: ${toEmails.join(", ")} for email ${emailId}`
          );

          // Perform importer lookup with timeout protection
          const lookupPromise = this.importerService.getImporterByReceiveEmail(toEmails, queryRunner);
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error("Importer lookup timeout")), 5000)
          );

          const importer = (await Promise.race([lookupPromise, timeoutPromise])) as Importer | null;

          if (importer?.receiveEmail?.includes("@")) {
            sendEmailDto.replyTo = importer.receiveEmail;
            this.logger.log(
              `Reply-to automatically set to importer receive email: ${importer.receiveEmail} for email ${emailId} (importer ID: ${importer.id})`
            );
          } else if (importer) {
            this.logger.warn(
              `Importer found but no valid receiveEmail configured for email ${emailId} (importer ID: ${importer.id}). ReceiveEmail: ${importer.receiveEmail}`
            );
            sendEmailDto.replyTo = undefined;
          } else {
            this.logger.warn(
              `No importer found for addresses [${toEmails.join(", ")}] for email ${emailId}, reply-to will not be set`
            );
            sendEmailDto.replyTo = undefined;
          }
        }
      } catch (error) {
        this.logger.error(
          `Failed to lookup importer for reply-to setting for email ${emailId}: ${error.message}`,
          error.stack
        );
        sendEmailDto.replyTo = undefined;
        // Continue processing - this is not a critical failure that should stop email sending

        // Additional error context for debugging
        if (error.name === "QueryFailedError") {
          this.logger.error(
            `Database query failed during importer lookup for email ${emailId}. This may indicate a database connectivity issue.`
          );
        } else if (error.message?.includes("timeout")) {
          this.logger.error(
            `Importer lookup timed out for email ${emailId}. This may indicate database performance issues.`
          );
        }
      }
    }
    sendEmailDto.subject = email.subject?.startsWith("Re: ") ? email.subject : `Re: ${email.subject || ""}`;
    sendEmailDto.text = text || undefined;
    sendEmailDto.html = html || undefined;
    sendEmailDto.attachments = attachments || undefined;

    // Send reply email
    let replyEmail = await this.sendEmail(sendEmailDto, queryRunner);
    this.logger.log(`Reply email sent: ${replyEmail?.id}`);
    if (!replyEmail?.id) throw new InternalServerErrorException("Failed to send reply email");

    // Update reply email request summary and processing outcome
    replyEmail = await this.editEmail(
      replyEmail?.id,
      {
        requestSummary: `Replied email "${email?.subject || "unknown subject"}" (ID: ${email?.id})`,
        processingOutcome: "Reply successful"
      },
      queryRunner
    );
    this.logger.log(
      `Reply email updated: ${replyEmail?.id}, Request summary: ${replyEmail?.requestSummary}, Processing Outcome: ${replyEmail?.processingOutcome}`
    );

    return await this.getEmailById(replyEmail?.id, queryRunner);
  }

  /**
   * Reply to an email that failed to be processed
   * @param emailId - ID of the email to reply to
   * @param queryRunner - QueryRunner instance. Used for handling transactions
   * @returns The replied email record
   */
  async replyFailedEmail(emailId: number, queryRunner?: QueryRunner): Promise<Email | null> {
    if (!this.request?.user?.organization) throw new NotFoundException("Unknown organization");

    const email = await this.getEmailById(emailId, queryRunner);
    if (!email) throw new NotFoundException("Email not found");
    if (email.origin !== EmailOrigin.INBOX)
      throw new BadRequestException("Cannot reply to emails not coming from inbox");

    // Find importer related to email
    const toEmails = email.to.map(({ address }) => address);
    const importer = await this.importerService.getImporterByReceiveEmail(toEmails, queryRunner);
    if (!importer) throw new NotFoundException("Importer related to email not found");

    let emailText: string;
    switch (email.status) {
      case EmailStatus.FAILED_SAVING_EMAIL:
      case EmailStatus.FAILED_SHIPMENT_SEARCH:
      case EmailStatus.FAILED_ANALYZING_INTENTS:
      case EmailStatus.FAILED_PROCESSING_INTENTS:
      case EmailStatus.FAILED_AGGREGATING_EMAIL:
      case EmailStatus.FAILED_RESPONDING:
        emailText = this.templateManagerService.renderTemplate(EmailTemplateName.PROCESSING_FAILED_EMAIL, {});
        break;
      case EmailStatus.MANUAL_REVIEW:
        emailText = this.templateManagerService.renderTemplate(EmailTemplateName.MANUAL_REVIEW_EMAIL, {});
        break;
      default:
        throw new BadRequestException(`The email status ${email.status} is not one of the failed statuses`);
    }

    return await this.replyEmail(email.id, {
      replyTo: importer?.receiveEmail || undefined,
      text: emailText
    });
  }

  /**
   * Send an onboarding email to the importer
   * @param importer - Importer object
   * @param whitelistEmails - Array of whitelist emails
   * @param queryRunner - QueryRunner instance. Used for handling transactions
   */
  @OnEvent(ImporterEvent.SEND_ONBOARDING_EMAIL)
  async sendOnboardingEmail(importer: Importer, whitelistEmails?: string[], queryRunner?: QueryRunner) {
    const html = this.renderEmailTemplate(EmailTemplateName.IMPORTER_ONBOARDED_EMAIL, {
      importerName: importer.companyName,
      receiverEmail: importer.receiveEmail
    });

    await this.sendEmail(
      {
        from: this.SYSTEM_FROM_EMAIL,
        to: [importer.email, ...(whitelistEmails || [])],
        subject: "Welcome to Claro! Your Unique Customs Processing Email & Next Steps",
        html
      },
      queryRunner,
      importer.organization
    );
  }

  async rerunEmailProcessing(emailId: number) {
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user is not permitted to rerun email processing");

    let email = await this.getEmailById(emailId);
    if (!email) throw new NotFoundException("Email not found");

    switch (email.status) {
      case EmailStatus.FAILED_SAVING_EMAIL:
        // TODO: Implement rerun email saving
        throw new BadRequestException("Cannot rerun email processing for emails that failed to save");
      case EmailStatus.FAILED_SHIPMENT_SEARCH:
        this.logger.log(`Rerunning shipment search for email: ${email.id}`);
        email = await this.editEmail(email.id, { status: EmailStatus.SAVED });

        // Emit EMAIL_SAVED event for core-agent processing
        this.eventEmitter.emit(
          EMAIL_SAVED,
          new EmailSavedEvent({
            emailId: email.id,
            organizationId: email.organization?.id,
            gmailId: email.gmailId,
            documents: [] // No attachments for retry scenarios
          })
        );
        return email;
      case EmailStatus.FAILED_ANALYZING_INTENTS:
        this.logger.log(`Rerunning intent analysis for email: ${email.id}`);
        email = await this.editEmail(email.id, { status: EmailStatus.COMPLETED_SHIPMENT_SEARCH });
        const threadShipment = await this.getEmailThreadShipment(email.threadId);
        if (threadShipment)
          this.eventEmitter.emit(
            EmailEvent.THREAD_SHIPMENT_FOUND,
            new ThreadShipmentFoundEvent({
              threadId: email.threadId,
              organizationId: email.organization?.id,
              shipmentId: threadShipment?.id
            })
          );
        else
          this.eventEmitter.emit(
            EmailEvent.THREAD_SHIPMENT_NOT_FOUND,
            new ThreadShipmentNotFoundEvent({
              threadId: email.threadId,
              organizationId: email.organization?.id
            })
          );
        return email;
      case EmailStatus.FAILED_PROCESSING_INTENTS:
        this.logger.log(`Rerunning intent processing for email: ${email.id}`);
        email = await this.editEmail(email.id, { status: EmailStatus.INTENTS_ANALYZED });
        this.eventEmitter.emit(
          EmailEvent.EMAIL_INTENTS_EXTRACTED,
          new EmailIntentsExtractedEvent({
            emailId: email.id,
            organizationId: email.organization?.id
          })
        );
        return email;
      case EmailStatus.FAILED_AGGREGATING_EMAIL:
        this.logger.log(`🔄 [RETRY] Rerunning email aggregation for email: ${email.id}`);
        email = await this.editEmail(email.id, { status: EmailStatus.INTENTS_PROCESSED });
        this.logger.log(`🎯 [RETRY] Emitting EMAIL_INTENTS_PROCESSED retry event for email: ${email.id}`);
        this.eventEmitter.emit(
          EmailEvent.EMAIL_INTENTS_PROCESSED,
          new EmailIntentsProcessedEvent({
            emailId: email.id,
            organizationId: email.organization?.id
          })
        );
        return email;
      case EmailStatus.FAILED_RESPONDING:
        this.logger.log(`Rerunning email responding for email: ${email.id}`);
        email = await this.editEmail(email.id, { status: EmailStatus.EMAIL_AGGREGATED });
        this.eventEmitter.emit(
          EmailEvent.EMAIL_AGGREGATED,
          new EmailAggregatedEvent({
            emailId: email.id,
            organizationId: email.organization?.id
          })
        );
        return email;
      default:
        throw new BadRequestException(`The email status ${email.status} is not one of the failed statuses`);
    }
  }

  /**
   * Sends an alert email to the backoffice address.
   * Throws an error in non-production if required config is missing or if sending fails.
   * @param subject - Email subject
   * @param body - Email body
   * @param organization - Organization context (can be ID or Organization entity)
   * @param queryRunner - Optional queryRunner for transactions
   * @throws {InternalServerErrorException} If config is missing (non-production)
   */
  async sendBackofficeAlert(
    subject: string,
    body: string,
    organization: number | Organization,
    queryRunner?: QueryRunner
  ): Promise<Email | null> {
    this.logger.log(`[ sendBackofficeAlert ] Attempting to send backoffice email. Subject: "${subject}"`);

    if (
      this.configService.get<string>("DISABLE_SEND_BACK_OFFICE_EMAIL") === "true" &&
      NODE_ENV === NodeEnv.LOCAL
    ) {
      this.logger.warn(
        `[ sendBackofficeAlert ] Backoffice email sending is disabled via DISABLE_SEND_BACK_OFFICE_EMAIL. Subject: "${subject}"`
      );
      return null;
    }

    const backofficeEmail = this.BACKOFFICE_EMAIL;
    const fromEmail = this.SYSTEM_FROM_EMAIL;

    const missingConfig = [];
    if (!backofficeEmail) missingConfig.push("BACKOFFICE_EMAIL_ADDRESS");
    if (!fromEmail) missingConfig.push("SYSTEM_FROM_EMAIL");

    if (missingConfig.length > 0) {
      const errorMsg = `Missing required email configuration: ${missingConfig.join(", ")}`;
      this.logger.error(`[ sendBackofficeAlert ] CRITICAL: ${errorMsg}. Backoffice alerts will not be sent!`);

      // Always throw in non-production for immediate feedback
      if (process.env.NODE_ENV !== "production") {
        throw new InternalServerErrorException(errorMsg);
      }

      // In production, log as critical error and return null
      // This ensures the error is visible in logs for monitoring/alerting
      this.logger.error(
        `[ sendBackofficeAlert ] Production environment detected missing config. Subject: "${subject}"`
      );
      return null;
    }

    const sendEmailDto: SendEmailDto = {
      from: fromEmail,
      to: [backofficeEmail],
      subject: `[Claro System Alert] ${subject}`,
      text: body
    };

    try {
      const organizationEntity =
        typeof organization === "number" ? ({ id: organization } as Organization) : organization;
      const sentEmail = await this.sendEmail(sendEmailDto, queryRunner, organizationEntity);
      if (sentEmail) {
        this.logger.log(`Successfully sent backoffice email. Email ID: ${sentEmail.id}`);
      } else {
        this.logger.log(
          `Backoffice email was not sent due to configuration or other restrictions. Subject: "${subject}"`
        );
      }
      return sentEmail;
    } catch (error) {
      this.logger.error(
        `Failed to send backoffice email. Subject: "${subject}". Error: ${error.message}`,
        error.stack
      );
      if (process.env.NODE_ENV !== "production") {
        throw error;
      }
      return null;
    }
  }

  /**
   * Reassign an email to a different organization. This can only be used for emails that are:
   * 1. In manual review status
   * 2. Assgined to backoffice organization
   * @param emailId - ID of the email to reassign
   * @param organizationId - ID of the new organization to assign the email to
   */
  async reassignEmailOrganization(emailId: number, organizationId: number) {
    let email = await this.getEmailById(emailId);
    if (!email) throw new NotFoundException("Email not found");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user is not permitted to reassign email organization");
    if (
      email.status !== EmailStatus.MANUAL_REVIEW ||
      email.organization?.organizationType !== OrganizationType.BACKOFFICE
    )
      throw new BadRequestException(
        "Email organization cannot be reassigned because it is not in manual review status or not assigned to backoffice organization"
      );

    // Update the email organization
    const newEmailOrg = await this.organizationService.getOrganizationById(organizationId, true);
    if (!newEmailOrg) throw new NotFoundException("Organization for reassignment not found");
    const isOrganizationChanged = email.organization?.id !== newEmailOrg.id;
    email.organization = newEmailOrg;
    email.status = EmailStatus.SAVED;
    email = await this.emailRepository.save(email);
    this.logger.log(
      `Email ${email.id} reassigned to organization ${organizationId} and status set to ${EmailStatus.SAVED}`
    );

    // TODO: Update the file batch organization
    if (isOrganizationChanged)
      await this.fileBatchService.moveFileBatchToOrganization(email.gmailId, newEmailOrg.id);

    // Emit the EMAIL_SAVED event for core-agent to process the email
    this.eventEmitter.emit(
      EMAIL_SAVED,
      new EmailSavedEvent({
        emailId: email.id,
        organizationId: email.organization?.id,
        gmailId: email.gmailId,
        documents: [] // Documents will be populated after extraction
      })
    );
    return await this.getEmailById(email.id);
  }

  /**
   * Sanitize filename for use in email headers to prevent header injection
   * @param filename - Original filename
   * @returns Sanitized filename safe for email headers
   */
  private sanitizeFilename(filename: string): string {
    if (!filename) return "attachment";

    // Remove or replace dangerous characters that could cause header injection
    return filename
      .replace(/[\r\n\t]/g, "") // Remove newlines and tabs
      .replace(/"/g, "'") // Replace double quotes with single quotes
      .replace(/[^\x20-\x7E]/g, "_") // Replace non-ASCII characters with underscore
      .substring(0, 255); // Limit length to prevent excessively long filenames
  }
}
