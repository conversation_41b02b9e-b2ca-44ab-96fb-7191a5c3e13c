import {
  Controller,
  ForbiddenException,
  Get,
  HttpCode,
  Inject,
  NotFoundException,
  Param,
  Post,
  Put,
  Query,
  UseGuards
} from "@nestjs/common";
import {
  ApiExcludeEndpoint,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags
} from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiGetByIdResponses,
  AuthenticatedRequest,
  ForbiddenResponseDto,
  GetGmailAuthorizationUriResponseDto,
  GetGmailTokensDto,
  NotFoundResponseDto,
  SimplifiedGmailToken,
  UserPermission
} from "nest-modules";
import { REQUEST } from "@nestjs/core";
import { GmailService } from "../services/gmail.service";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { EmailEvent } from "../types/event.types";

@ApiTags("Gmail API")
@Controller("gmail")
export class GmailController {
  constructor(
    private readonly gmailService: GmailService,
    private readonly eventEmitter: EventEmitter2,
    @Inject(REQUEST) private readonly request: AuthenticatedRequest
  ) {}

  // TODO: Move to backoffice
  @ApiExcludeEndpoint()
  @Get("oauth/callback")
  async gmailOAuthCallback(
    @Query("error") error?: string,
    @Query("code") code?: string,
    @Query("state") state?: string
  ) {
    return await this.gmailService.gmailOAuthCallback(code, error, state);
  }

  // TODO: Move to backoffice
  @ApiOperation({ summary: "Get Gmail Authorization URI" })
  @ApiAccessTokenAuthenticated()
  @ApiOkResponse({ type: GetGmailAuthorizationUriResponseDto })
  @UseGuards(AccessTokenGuard)
  @Get("oauth")
  async getGmailAuthorizationUri() {
    return this.gmailService.getGmailAuthorizationUri();
  }

  // TODO: Move to backoffice
  @ApiOperation({ summary: "Get Gmail Tokens" })
  @ApiAccessTokenAuthenticated()
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiOkResponse({ type: GetGmailTokensDto })
  @UseGuards(AccessTokenGuard)
  @Get("tokens")
  async getGmailTokens(@Query() getGmailTokensDto: GetGmailTokensDto) {
    return this.gmailService.getGmailTokens(getGmailTokensDto);
  }

  // TODO: Move to backoffice
  @ApiOperation({ summary: "Get Gmail Token" })
  @ApiAccessTokenAuthenticated()
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: SimplifiedGmailToken })
  @UseGuards(AccessTokenGuard)
  @Get("tokens/:email")
  async getGmailTokenByEmail(@Param("email") email: string) {
    const token = await this.gmailService.getGmailTokenByEmail(email);
    if (!token) throw new NotFoundException("Gmail token not found");
    return token;
  }

  // TODO: Move to backoffice
  @ApiOperation({ summary: "Set Default Gmail Token" })
  @ApiAccessTokenAuthenticated()
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiOkResponse({ type: SimplifiedGmailToken })
  @UseGuards(AccessTokenGuard)
  @HttpCode(200)
  @Put("tokens/:email/default")
  async setDefaultGmailTokenByEmail(@Param("email") email: string) {
    return this.gmailService.setDefaultGmailToken(email);
  }

  @ApiExcludeEndpoint()
  @ApiOperation({ summary: "Manually trigger Gmail Sync" })
  @ApiOkResponse({ description: "Gmail sync process initiated." })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiAccessTokenAuthenticated()
  @UseGuards(AccessTokenGuard)
  @HttpCode(202)
  @Post("trigger-sync")
  async triggerGmailSync() {
    if (this.request.user?.permission !== UserPermission.BACKOFFICE_ADMIN) {
      throw new ForbiddenException("Only back-office admins can trigger a manual sync");
    }
    this.eventEmitter.emit(EmailEvent.EMAIL_SYNC);
    return { message: "Gmail sync process initiated." };
  }
}
