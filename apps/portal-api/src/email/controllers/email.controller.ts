import {
  Body,
  Controller,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  ParseEnumPipe,
  ParseIntPipe,
  Post,
  Query,
  UseGuards
} from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import {
  ApiBadRequestResponse,
  ApiExcludeEndpoint,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags
} from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiGetByIdResponses,
  ApiGetManyResponses,
  BadRequestResponseDto,
  Email,
  ForbiddenResponseDto,
  GetEmailsByThreadIdResponseDto,
  GetEmailsDto,
  GetEmailsResponseDto,
  NotFoundResponseDto
} from "nest-modules";

import { EmailService } from "../services/email.service";
import { EmailEvent } from "../types/event.types";

@ApiTags("Emails")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("emails")
export class EmailController {
  constructor(
    private readonly emailService: EmailService,
    private readonly eventEmitter: EventEmitter2
  ) {}

  // TODO: Remove this endpoint after testing
  @ApiExcludeEndpoint()
  @Post("test-event/:event")
  async testEvent(@Param("event", new ParseEnumPipe(EmailEvent)) event: EmailEvent, @Body() eventData: any) {
    this.eventEmitter.emit(event, eventData);
  }

  @ApiOperation({ summary: "Get emails" })
  @ApiGetManyResponses({ type: GetEmailsResponseDto })
  @Get()
  async getEmails(@Query() getEmailsDto: GetEmailsDto) {
    return this.emailService.getEmails(getEmailsDto);
  }

  @ApiOperation({ summary: "Get emails by thread ID" })
  @ApiGetManyResponses({ type: GetEmailsByThreadIdResponseDto })
  @Get("thread/:threadId")
  async getEmailsByThreadId(@Param("threadId") threadId: string) {
    const emails = await this.emailService.getEmailsByThreadId(threadId);
    return {
      data: emails
    };
  }

  @ApiOperation({ summary: "Get email" })
  @ApiGetByIdResponses({ type: Email })
  @Get(":id")
  async getEmailById(@Param("id", ParseIntPipe) id: number) {
    const email = await this.emailService.getEmailById(id);
    if (!email) throw new NotFoundException("Email not found");
    return email;
  }

  @ApiOperation({ summary: "Rerun email processing" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiOkResponse({ type: Email })
  @Post(":id/process")
  async rerunEmailProcessing(@Param("id", ParseIntPipe) id: number) {
    return this.emailService.rerunEmailProcessing(id);
  }

  @ApiOperation({ summary: "Reassign email organization" })
  @ApiParam({ name: "id", type: "number", description: "Email ID" })
  @ApiParam({ name: "organizationId", type: "number", description: "Organization ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiOkResponse({ type: Email })
  @HttpCode(200)
  @Post(":id/reassign-organization/:organizationId")
  async reassignEmailOrganization(
    @Param("id", ParseIntPipe) id: number,
    @Param("organizationId", ParseIntPipe) organizationId: number
  ) {
    return this.emailService.reassignEmailOrganization(id, organizationId);
  }
}
