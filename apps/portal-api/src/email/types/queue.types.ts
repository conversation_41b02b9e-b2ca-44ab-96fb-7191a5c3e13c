import { RegisterQueueOptions, RegisterFlowProducerOptions } from "@nestjs/bullmq";
import { NestWorkerOptions } from "@nestjs/bullmq/dist/interfaces/worker-options.interface";
import { Job, JobsOptions, Queue } from "bullmq";

export const DEFAULT_JOB_OPTIONS: JobsOptions = {
  attempts: 1,
  // backoff: {
  //   type: "exponential",
  //   delay: 1000,
  // },
  removeOnComplete: {
    age: 3600,
    count: 1000
  },
  removeOnFail: {
    age: 24 * 3600
  }
};

export const DEFAULT_WORKER_OPTIONS: NestWorkerOptions = {
  concurrency: 1,
  lockDuration: 1000 * 60 * 2 // setting 2 minutes for llm calls to complete
};

export const AGGREGATE_EMAIL_WORKER_OPTIONS: NestWorkerOptions = {
  concurrency: 1,
  lockDuration: 1000 * 60 * 15 // setting 15 minutes for aggregation calls to accomodate long running calls temporarily until pipeline fix
};

export enum EmailQueueName {
  GET_GMAIL_MESSAGE = "get-gmail-message"
}

export const EMAIL_QUEUES: Array<RegisterQueueOptions> = [
  {
    name: EmailQueueName.GET_GMAIL_MESSAGE,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  }
];

// Get Gmail Message Queue
export interface GetGmailMessageJobData {
  email: string;
  gmailId: string;
  latestHistoryId: string | null;
}
export type GetGmailMessageJob = Job<GetGmailMessageJobData, null, string>;
export type GetGmailMessageQueue = Queue<GetGmailMessageJobData, null, string>;

// Generic Email Job
export type EmailJobData = GetGmailMessageJobData;
export type EmailJob = Job<EmailJobData, null, string>;

// Email FlowProducer Configuration - Removed since email saga processing moved to core-agent
export const EMAIL_FLOW_PRODUCERS: RegisterFlowProducerOptions[] = [];
