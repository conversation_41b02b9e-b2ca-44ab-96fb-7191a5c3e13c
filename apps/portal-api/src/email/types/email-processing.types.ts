import { Impo<PERSON>, Email, Organization } from "nest-modules";
import { gmail_v1 } from "googleapis";
import { EmailService } from "../services/email.service";
import { FileService } from "src/document/services/file.service";
import { GmailMessageFormat } from "./gmail.types";

/**
 * Attachment data extracted from Gmail message
 */
export interface AttachmentData {
  fileName: string | null;
  mimeType: string | null;
  buffer: Buffer | null;
}

/**
 * Gmail message data with target format
 */
export interface GmailMessageData {
  message: gmail_v1.Schema$Message;
  targetFormat: GmailMessageFormat;
}

/**
 * Email processing context containing resolved services and organization context
 */
export interface EmailProcessingContext {
  /** Whether the importer is unknown for the email */
  unknownImporter: boolean;
  organization: Organization;
  contextId: any;
  emailService: EmailService;
  fileService: FileService;
}

/**
 * Input parameters for email processing method
 * Contains all data needed to process an email without making Gmail API calls
 */
export interface EmailProcessingInput {
  /** Email address (inbox email) */
  email: string;
  /** Gmail message object */
  message: gmail_v1.Schema$Message;
  /** Target format for message processing */
  targetFormat: GmailMessageFormat;
  /** Existing email record if updating, null if creating new */
  existingEmailRecord: Email | null;
  /** Latest history ID for batch operations, null for full sync */
  latestHistoryId: string | null;
}

/**
 * Result of email processing operation
 */
export interface EmailProcessingResult {
  /** The processed email record */
  emailRecord: Email;
  /** Number of attachments processed */
  attachmentCount: number;
  /** Whether the email was newly created or updated */
  isNewRecord: boolean;
}
