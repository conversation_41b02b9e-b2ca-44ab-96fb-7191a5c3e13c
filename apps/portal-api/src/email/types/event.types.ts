export enum EmailEvent {
  EMAIL_SYNC = "email.sync",
  EMAIL_MANUAL_REVIEW_REQUIRED = "email.manual-review-required",
  EMAIL_INTENTS_EXTRACTED = "email.intents-extracted",
  EMAIL_INTENTS_PROCESSED = "email.intents-processed",
  EMAIL_AGGREGATED = "email.aggregated",
  THREAD_SHIPMENT_FOUND = "thread.shipment-found",
  THREAD_SHIPMENT_NOT_FOUND = "thread.shipment-not-found",
  THREAD_TOO_MANY_SHIPMENTS_FOUND = "thread.too-many-shipments-found",

  // Job failed events
  GET_GMAIL_MESSAGE_FAILED = "job.get-gmail-message-failed",
  FIND_THREAD_SHIPMENT_FAILED = "job.find-thread-shipment-failed",
  EXTRACT_USER_INTENTS_FAILED = "job.extract-user-intents-failed",
  PROCESS_USER_INTENTS_FAILED = "job.process-user-intents-failed",
  AGGREGATE_EMAIL_FAILED = "job.aggregate-email-failed",
  GENERATE_EMAIL_RESPONSE_FAILED = "job.generate-email-response-failed"
}
