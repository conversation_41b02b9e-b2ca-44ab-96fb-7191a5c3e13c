export enum EmailManualReviewReason {
  TOO_MANY_SHIPMENTS_FOUND = "too-many-shipments-found",
  SPAM = "spam",
  NO_INTENTS = "no-intents",
  INTENTS_NOT_ALLOWED = "intents-not-allowed",
  UNKNOWN_ORGANIZATION = "unknown-organization"
}

export enum EmailTemplateName {
  MANUAL_REVIEW_EMAIL = "manual-review-email",
  PROCESSING_FAILED_EMAIL = "processing-failed-email",
  ENTRY_SUBMISSION_ERROR_EMAIL = "entry-submission-error-email",
  IMPORTER_ONBOARDED_EMAIL = "importer-onboarded-email",
  DOCUMENT_SENT_IN_RESPONSE_EMAIL = "document-sent-in-response-email",
  DOCUMENT_SENT_IN_RESPONSE_WITH_QUERIES_EMAIL = "document-sent-in-response-with-queries-email",
  ONBOARDING_CREATE_USER_ERROR_EMAIL = "onboarding-create-user-error-email",
  ACCOUNTING_NOT_COMPLETED_WARNING_EMAIL = "accounting-not-completed-warning-email",

  // Customs status check emails
  LIVE_SHIPMENT_EMAIL = "live-shipment-email",
  LIVE_ENTRY_UPLOAD_FAILED_EMAIL = "live-entry-upload-failed-email",
  RNS_STATUS_CHANGE_EMAIL = "rns-status-change-email",
  ENTRY_NOT_ACCEPTED_WARNING_EMAIL = "entry-not-accepted-warning-email",
  CUSTOMS_STATUS_CHECK_ERROR_EMAIL = "customs-status-check-error-email",
  LIVE_SHIPMENT_MANDATORY_FIELDS_UPDATE_EMAIL = "live-shipment-mandatory-fields-update-email",

  // Entry update emails
  MANUAL_ENTRY_SUBMISSION_REQUEST_EMAIL = "manual-entry-submission-request-email",
  UPDATE_ENTRY_ERROR_EMAIL = "update-entry-error-email",

  // Reset password email
  RESET_PASSWORD_EMAIL = "reset-password-email"
}
