import { EmailAddressDto } from "nest-modules";

export enum GmailMessageFormat {
  MINIMAL = "minimal",
  FULL = "full",
  RAW = "raw",
  METADATA = "metadata"
}

export enum GmailHistoryType {
  MESSAGE_ADDED = "messageAdded",
  MESSAGE_DELETED = "messageDeleted",
  LABEL_ADDED = "labelAdded",
  LABEL_REMOVED = "labelRemoved"
}

export enum GmailHeaderName {
  FROM = "from",
  REPLY_TO = "reply-to",
  TO = "to",
  CC = "cc",
  SUBJECT = "subject"
}

export const PARSE_EMAIL_ADDR_DEFAULT_CONFIG = {
  rfc6532: true, // unicode
  partial: false, // return failed parses
  simple: false, // simple AST
  strict: false, // turn off obs- features in the rfc
  rejectTLD: false, // domains require a "."
  startAt: null,
  atInDisplayName: true,
  commaInDisplayName: false
};

export interface ParsedGmailMessagePart {
  text?: string | null;
  html?: string | null;
  attachments?: Array<{
    fileName: string | null;
    mimeType: string | null;
    buffer: Buffer | null;
  }> | null;
}

export interface ParsedGmailMessage extends ParsedGmailMessagePart {
  from: Array<EmailAddressDto> | null;
  replyTo: Array<EmailAddressDto> | null;
  to: Array<EmailAddressDto> | null;
  cc: Array<EmailAddressDto> | null;
  subject: string | null;
  messageId: string | null;
}
