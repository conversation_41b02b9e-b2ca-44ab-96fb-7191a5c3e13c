import { Document } from "nest-modules";
import { EmailManualReviewReason } from "../types/email.types";

class BaseEmailEvent {
  constructor({ emailId, organizationId }: { emailId: number; organizationId: number }) {
    this.emailId = emailId;
    this.organizationId = organizationId;
  }

  /** ID of the email saved in database */
  public readonly emailId: number;

  /** Organization ID of the email */
  public readonly organizationId: number;
}

export class EmailManualReviewRequiredEvent extends BaseEmailEvent {
  constructor({
    emailId,
    organizationId,
    reviewReason
  }: {
    emailId: number;
    organizationId: number;
    reviewReason: EmailManualReviewReason;
  }) {
    super({ emailId, organizationId });
    this.reviewReason = reviewReason;
  }

  /** Reason for manual review */
  public readonly reviewReason: EmailManualReviewReason;
}

export class EmailIntentsExtractedEvent extends BaseEmailEvent {}

export class EmailIntentsProcessedEvent extends BaseEmailEvent {}

export class EmailAggregatedEvent extends BaseEmailEvent {}

export class ThreadShipmentFoundEvent {
  constructor({
    threadId,
    organizationId,
    shipmentId
  }: {
    threadId: string;
    organizationId: number;
    shipmentId: number;
  }) {
    this.threadId = threadId;
    this.organizationId = organizationId;
    this.shipmentId = shipmentId;
  }

  /** Gmail thread ID */
  public readonly threadId: string;

  /** Organization ID of the thread and shipment */
  public readonly organizationId: number;

  /** Shipment ID associated with the thread */
  public readonly shipmentId: number;
}

export class ThreadShipmentNotFoundEvent {
  constructor({ threadId, organizationId }: { threadId: string; organizationId: number }) {
    this.threadId = threadId;
    this.organizationId = organizationId;
  }

  /** Gmail thread ID */
  public readonly threadId: string;

  /** Organization ID of the thread and shipment */
  public readonly organizationId: number;
}

export class ThreadTooManyShipmentsFoundEvent extends ThreadShipmentNotFoundEvent {}
