import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  ArrayUnique,
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  Min,
  ValidateIf,
  ValidateNested
} from "class-validator";
import { EmailIntentType, EmailUpdateStatus } from "nest-modules";

export class CreateEmailUpdateDto {
  @ApiProperty({ enum: EmailUpdateStatus })
  @IsNotEmpty()
  @IsEnum(EmailUpdateStatus)
  status: EmailUpdateStatus;

  @ApiProperty({
    type: "integer",
    minimum: 1,
    description: "Email ID that the update relates to"
  })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  emailId: number;

  @ApiProperty({
    enum: EmailIntentType,
    description: "Type of intent that the update is related to"
  })
  @IsNotEmpty()
  @IsEnum(EmailIntentType)
  intent: EmailIntentType;

  @ApiPropertyOptional({
    type: "object",
    description: "JSON containing the updates to apply"
  })
  @IsOptional()
  @IsObject()
  updateJson?: Record<string, any> | null;

  @ApiPropertyOptional({
    type: "integer",
    description: "IDs of the documents to be used in the update",
    minimum: 1
  })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  documentIds?: number[] | null;

  @ApiPropertyOptional({
    type: "string",
    description: "Failed reasons of the update. Required when status is failed"
  })
  @ValidateIf((obj) => obj.status === EmailUpdateStatus.FAILED)
  @IsString()
  failedReasons?: string | null;
}

export class EditEmailUpdateDto {
  @ApiProperty({
    enum: EmailUpdateStatus
  })
  @IsNotEmpty()
  @IsEnum(EmailUpdateStatus)
  status: EmailUpdateStatus;

  @ApiPropertyOptional({
    type: "string",
    description: "Failed reasons of the update. Required when status is failed"
  })
  @ValidateIf((obj) => obj.status === EmailUpdateStatus.FAILED)
  @IsString()
  failedReasons?: string | null;
}

export class EditEmailUpdateDtoWithId extends EditEmailUpdateDto {
  @ApiProperty({ type: "integer", minimum: 1, description: "Email Update ID" })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  id: number;
}

export class BatchUpdateEmailUpdatesDto {
  @ApiPropertyOptional({ type: () => [CreateEmailUpdateDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateEmailUpdateDto)
  create?: CreateEmailUpdateDto[] | null;

  @ApiPropertyOptional({ type: () => [EditEmailUpdateDtoWithId] })
  @IsOptional()
  @IsArray()
  @ArrayUnique((obj) => obj.id)
  @ValidateNested({ each: true })
  @Type(() => EditEmailUpdateDtoWithId)
  edit?: EditEmailUpdateDtoWithId[] | null;
}
