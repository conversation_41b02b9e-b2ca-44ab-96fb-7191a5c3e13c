import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  ArrayMinSize,
  IsArray,
  IsBase64,
  IsDate,
  IsEmail,
  IsEnum,
  IsMimeType,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  Matches,
  MinLength,
  ValidateNested
} from "class-validator";
import { EmailAddressDto, EmailOrigin, EmailStatus } from "nest-modules";

export class CreateEmailDto {
  @ApiProperty({ enum: EmailOrigin })
  @IsNotEmpty()
  @IsEnum(EmailOrigin)
  origin: EmailOrigin;

  @ApiProperty({ enum: EmailStatus })
  @IsNotEmpty()
  @IsEnum(EmailStatus)
  status: EmailStatus;

  @ApiProperty({
    type: "string",
    format: "email",
    description: "Gmail Inbox Email Address"
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  inboxEmail: string;

  @ApiProperty({
    type: "string",
    minLength: 1,
    description: "Email ID from Gmail"
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  gmailId: string;

  @ApiProperty({
    type: "string",
    minLength: 1,
    description: "Thread ID from Gmail"
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  threadId: string;

  @ApiProperty({
    type: "string",
    minLength: 1,
    description: "History ID from Gmail"
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  historyId: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @Type(() => Date)
  @IsNotEmpty()
  @IsDate()
  receiveDate: Date;

  @ApiProperty({
    type: () => [EmailAddressDto],
    minItems: 1,
    description: "List of author email addresses and names"
  })
  @Type(() => EmailAddressDto)
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  from: Array<EmailAddressDto>;

  @ApiPropertyOptional({
    type: () => [EmailAddressDto],
    minItems: 0,
    description: "List of reply-to email addresses and names"
  })
  @Type(() => EmailAddressDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  replyTo?: Array<EmailAddressDto> | null;

  @ApiProperty({
    type: () => [EmailAddressDto],
    minItems: 1,
    description: "List of recipient email addresses and names"
  })
  @Type(() => EmailAddressDto)
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  to: Array<EmailAddressDto>;

  @ApiPropertyOptional({
    type: () => [EmailAddressDto],
    minItems: 0,
    description: "List of carbon-copy recipient email addresses and names"
  })
  @Type(() => EmailAddressDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  cc?: Array<EmailAddressDto> | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  @Matches(/^[^\r\n]*$/, { message: "subject cannot contain carriage return or newline characters" })
  subject?: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "Text content of the email"
  })
  @IsOptional()
  @IsString()
  text?: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "HTML content of the email"
  })
  @IsOptional()
  @IsString()
  html?: string | null;

  @ApiPropertyOptional({
    type: "object",
    description: "Extracted user intents from the email"
  })
  @IsOptional()
  @IsArray()
  @IsObject({ each: true })
  userIntents?: Array<Record<string, any>> | null;

  @ApiPropertyOptional({
    type: "string",
    description: "One line summary of the email request"
  })
  @IsOptional()
  @IsString()
  requestSummary?: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "One line summary of the process outcome of the email request"
  })
  @IsOptional()
  @IsString()
  processingOutcome?: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "Error message if the email processing failed"
  })
  @IsOptional()
  @IsString()
  error?: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "RFC 2822 Message-ID header"
  })
  @IsOptional()
  @IsString()
  @Matches(/^[^\r\n]*$/, { message: "messageId cannot contain carriage return or newline characters" })
  messageId?: string | null;
}

export class EditEmailDto {
  @ApiPropertyOptional({ enum: EmailOrigin })
  @IsOptional()
  @IsEnum(EmailOrigin)
  origin?: EmailOrigin | null;

  @ApiPropertyOptional({ enum: EmailStatus })
  @IsOptional()
  @IsEnum(EmailStatus)
  status?: EmailStatus | null;

  @ApiPropertyOptional({
    type: "string",
    format: "email",
    description: "Gmail Inbox Email Address"
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  inboxEmail?: string | null;

  @ApiPropertyOptional({
    type: "string",
    minLength: 1,
    description: "Email ID from Gmail"
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  gmailId?: string | null;

  @ApiPropertyOptional({
    type: "string",
    minLength: 1,
    description: "Thread ID from Gmail"
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  threadId?: string | null;

  @ApiPropertyOptional({
    type: "string",
    minLength: 1,
    description: "History ID from Gmail"
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  historyId?: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  receiveDate?: Date | null;

  @ApiPropertyOptional({
    type: () => [EmailAddressDto],
    minItems: 1,
    description: "List of author email addresses and names"
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Type(() => EmailAddressDto)
  @ValidateNested({ each: true })
  from?: Array<EmailAddressDto> | null;

  @ApiPropertyOptional({
    type: () => [EmailAddressDto],
    minItems: 0,
    description: "List of reply-to email addresses and names"
  })
  @IsOptional()
  @IsArray()
  @Type(() => EmailAddressDto)
  @ValidateNested({ each: true })
  replyTo?: Array<EmailAddressDto> | null;

  @ApiPropertyOptional({
    type: () => [EmailAddressDto],
    minItems: 1,
    description: "List of recipient email addresses and names"
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @Type(() => EmailAddressDto)
  @ValidateNested({ each: true })
  to?: Array<EmailAddressDto> | null;

  @ApiPropertyOptional({
    type: () => [EmailAddressDto],
    minItems: 0,
    description: "List of carbon-copy recipient email addresses and names"
  })
  @IsOptional()
  @IsArray()
  @Type(() => EmailAddressDto)
  @ValidateNested({ each: true })
  cc?: Array<EmailAddressDto> | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  @Matches(/^[^\r\n]*$/, { message: "subject cannot contain carriage return or newline characters" })
  subject?: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "Text content of the email"
  })
  @IsOptional()
  @IsString()
  text?: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "HTML content of the email"
  })
  @IsOptional()
  @IsString()
  html?: string | null;

  @ApiPropertyOptional({
    type: "object",
    description: "Extracted user intents from the email"
  })
  @IsOptional()
  @IsArray()
  @IsObject({ each: true })
  userIntents?: Array<Record<string, any>> | null;

  @ApiPropertyOptional({
    type: "string",
    description: "One line summary of the email request"
  })
  @IsOptional()
  @IsString()
  requestSummary?: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "One line summary of the process outcome of the email request"
  })
  @IsOptional()
  @IsString()
  processingOutcome?: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "Error message if the email processing failed"
  })
  @IsOptional()
  @IsString()
  error?: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "RFC 2822 Message-ID header"
  })
  @IsOptional()
  @IsString()
  @Matches(/^[^\r\n]*$/, { message: "messageId cannot contain carriage return or newline characters" })
  messageId?: string | null;
}

export class EmailAttachmentDto {
  @ApiProperty({ type: "string", minLength: 1, description: "File name of the attachment" })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  @Matches(/^[^\r\n]*$/, { message: "fileName cannot contain carriage return or newline characters" })
  fileName: string;

  @ApiProperty({ type: "string", minLength: 1, description: "MIME type of the attachment" })
  @IsNotEmpty()
  @IsMimeType()
  @MinLength(1)
  mimeType: string;

  @ApiProperty({
    type: "string",
    format: "byte",
    minLength: 1,
    description: "Base64-encoded attachment data"
  })
  @IsNotEmpty()
  @IsBase64()
  @MinLength(1)
  b64Data: string;
}

export class ReplyEmailDto {
  @ApiPropertyOptional({
    type: "string",
    format: "email",
    description: "Reply-to email address"
  })
  @IsOptional()
  @IsEmail()
  replyTo?: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "Text content of the reply email"
  })
  @IsOptional()
  @IsString()
  text?: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "HTML content of the reply email"
  })
  @IsOptional()
  @IsString()
  html?: string | null;

  @ApiPropertyOptional({
    type: () => [EmailAttachmentDto],
    description: "List of attachments to be included in the reply email"
  })
  @Type(() => EmailAttachmentDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  attachments?: Array<EmailAttachmentDto> | null;
}

export class SendEmailDto {
  @ApiPropertyOptional({
    type: "string",
    minLength: 1,
    description: "Gmail thread ID. Used to reply to the same thread."
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  threadId?: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "Message-ID of the email being replied to (for proper threading)"
  })
  @IsOptional()
  @IsString()
  @Matches(/^[^\r\n]*$/, {
    message: "inReplyToMessageId cannot contain carriage return or newline characters"
  })
  inReplyToMessageId?: string | null;

  @ApiProperty({
    type: "string",
    format: "email",
    description: "Sender email address. Must be an authenticated Gmail account."
  })
  @IsNotEmpty()
  @IsEmail()
  from: string;

  @ApiProperty({
    type: "array",
    minItems: 1,
    items: { type: "string", format: "email" },
    description: "List of recipient email addresses"
  })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  @IsEmail({}, { each: true })
  to: Array<string>;

  @ApiPropertyOptional({
    type: "array",
    minItems: 0,
    items: { type: "string", format: "email" },
    description: "List of carbon-copy recipient email addresses"
  })
  @IsOptional()
  @IsArray()
  @IsEmail({}, { each: true })
  cc?: Array<string> | null;

  @ApiPropertyOptional({
    type: "array",
    minItems: 0,
    items: { type: "string", format: "email" },
    description: "List of blind-carbon-copy recipient email addresses"
  })
  @IsOptional()
  @IsArray()
  @IsEmail({}, { each: true })
  bcc?: Array<string> | null;

  @ApiPropertyOptional({ type: "string", format: "email", description: "Reply-to email address" })
  @IsOptional()
  @IsEmail()
  replyTo?: string | null;

  @ApiPropertyOptional({ type: "string", description: "Subject of the email" })
  @IsOptional()
  @IsString()
  @Matches(/^[^\r\n]*$/, { message: "subject cannot contain carriage return or newline characters" })
  subject?: string | null;

  @ApiPropertyOptional({ type: "string", description: "Text content of the email" })
  @IsOptional()
  @IsString()
  text?: string | null;

  @ApiPropertyOptional({ type: "string", description: "HTML content of the email" })
  @IsOptional()
  @IsString()
  html?: string | null;

  @ApiPropertyOptional({ type: () => [EmailAttachmentDto] })
  @Type(() => EmailAttachmentDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  attachments?: Array<EmailAttachmentDto> | null;
}
