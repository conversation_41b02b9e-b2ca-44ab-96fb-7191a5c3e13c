import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsInt, IsNotEmpty, IsOptional, IsString, IsUrl, Min, MinLength } from "class-validator";

export class EntrySubmissionErrorEmailDto {
  constructor({
    shipmentId,
    shipmentUrl,
    transactionNumber,
    fileNumber,
    errorMessage,
    stackTrace
  }: {
    shipmentId: number;
    shipmentUrl: string;
    transactionNumber?: string | null;
    fileNumber?: string | null;
    errorMessage: string;
    stackTrace?: string | null;
  }) {
    this.shipmentId = shipmentId;
    this.shipmentUrl = shipmentUrl;
    this.transactionNumber = transactionNumber;
    this.fileNumber = fileNumber;
    this.errorMessage = errorMessage;
    this.stackTrace = stackTrace;
  }

  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  shipmentId: number;

  @ApiProperty({ type: "string", format: "uri" })
  @IsNotEmpty()
  @IsUrl()
  shipmentUrl: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  transactionNumber?: string | null;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  fileNumber?: string | null;

  @ApiProperty({ type: "string", minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  errorMessage: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  stackTrace?: string | null;
}
