import { Test, TestingModule } from "@nestjs/testing";
import { ConfigService } from "@nestjs/config";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { ModuleRef } from "@nestjs/core";
import { DataSource, Repository } from "typeorm";
import { getRepositoryToken } from "@nestjs/typeorm";
import { NotFoundException } from "@nestjs/common";

import { GetGmailMessageProcessor } from "../../processors/get-gmail-message.processor";
import { GmailService } from "../../services/gmail.service";
import { EmailService } from "../../services/email.service";
import { ImporterService } from "src/importer/importer.service";

import { Email, EmailOrigin, EmailStatus, Importer, EmailAddressDto } from "nest-modules";
import { CreateEmailDto, EditEmailDto } from "../../dto/email.dto";
import { GmailMessageFormat } from "../../types/gmail.types";
import { EmailEvent } from "../../types/event.types";

/**
 * Unit Tests for GetGmailMessageProcessor
 *
 * These tests focus on the individual extracted functions to verify
 * business logic without complex integration setup.
 *
 * Run with: npm run jest -- get-gmail-message.processor.unit.spec.ts
 */
describe("GetGmailMessageProcessor - Unit Tests", () => {
  let processor: GetGmailMessageProcessor;
  let gmailService: jest.Mocked<GmailService>;
  let configService: jest.Mocked<ConfigService>;
  let eventEmitter: jest.Mocked<EventEmitter2>;

  const mockEmail = "<EMAIL>";
  const mockGmailId = "gmail123";
  const mockGmailMessage = {
    id: mockGmailId,
    threadId: "thread123",
    historyId: "12345",
    internalDate: "1640995200000", // 2022-01-01
    labelIds: ["INBOX"]
  };

  const mockEmailRecord = {
    id: 1,
    gmailId: mockGmailId,
    status: EmailStatus.SAVED,
    organization: { id: 1 }
  } as Email;

  beforeEach(async () => {
    const mockGmailService = {
      getMessage: jest.fn(),
      parseGmailMessage: jest.fn()
    };

    const mockConfigService = {
      get: jest.fn()
    };

    const mockEventEmitter = {
      emit: jest.fn()
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GetGmailMessageProcessor,
        { provide: GmailService, useValue: mockGmailService },
        { provide: EmailService, useValue: {} },
        { provide: ImporterService, useValue: {} },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: EventEmitter2, useValue: mockEventEmitter },
        { provide: DataSource, useValue: {} },
        { provide: ModuleRef, useValue: {} },
        { provide: getRepositoryToken(Email), useValue: {} }
      ]
    }).compile();

    processor = module.get<GetGmailMessageProcessor>(GetGmailMessageProcessor);
    gmailService = module.get(GmailService);
    configService = module.get(ConfigService);
    eventEmitter = module.get(EventEmitter2);
  });

  describe("fetchGmailMessage", () => {
    it("should determine FULL format for new emails", async () => {
      gmailService.getMessage.mockResolvedValue(mockGmailMessage);

      const result = await processor["fetchGmailMessage"](mockEmail, mockGmailId, null);

      expect(result.targetFormat).toBe(GmailMessageFormat.FULL);
      expect(gmailService.getMessage).toHaveBeenCalledWith(mockEmail, mockGmailId, GmailMessageFormat.FULL);
    });

    it("should determine MINIMAL format for existing emails", async () => {
      gmailService.getMessage.mockResolvedValue(mockGmailMessage);

      const result = await processor["fetchGmailMessage"](mockEmail, mockGmailId, mockEmailRecord);

      expect(result.targetFormat).toBe(GmailMessageFormat.MINIMAL);
      expect(gmailService.getMessage).toHaveBeenCalledWith(
        mockEmail,
        mockGmailId,
        GmailMessageFormat.MINIMAL
      );
    });

    it("should throw NotFoundException for null response", async () => {
      gmailService.getMessage.mockResolvedValue(null);

      await expect(processor["fetchGmailMessage"](mockEmail, mockGmailId, null)).rejects.toThrow(
        NotFoundException
      );
    });
  });

  describe("extractAttachmentData", () => {
    it("should skip parsing for MINIMAL format", async () => {
      const result = await processor["extractAttachmentData"](
        mockEmail,
        mockGmailMessage,
        GmailMessageFormat.MINIMAL
      );

      expect(result.attachments).toEqual([]);
      expect(result.parsedMessage).toBeNull();
      expect(gmailService.parseGmailMessage).not.toHaveBeenCalled();
    });

    it("should parse message for FULL format", async () => {
      const mockAttachments = [
        { fileName: "test.pdf", mimeType: "application/pdf", buffer: Buffer.from("test") }
      ];
      const mockParsedMessage = { subject: "Test", text: "content" };

      gmailService.parseGmailMessage.mockResolvedValue({
        ...mockParsedMessage,
        attachments: mockAttachments
      });

      const result = await processor["extractAttachmentData"](
        mockEmail,
        mockGmailMessage,
        GmailMessageFormat.FULL
      );

      expect(result.attachments).toEqual(mockAttachments);
      expect(result.parsedMessage).toEqual(mockParsedMessage);
      expect(gmailService.parseGmailMessage).toHaveBeenCalledWith(mockEmail, mockGmailMessage);
    });
  });

  describe("determineEmailStatus", () => {
    beforeEach(() => {
      configService.get.mockImplementation((key) => {
        if (key === "SYSTEM_FROM_EMAIL") return "<EMAIL>";
        if (key === "BACKOFFICE_EMAIL_ADDRESS") return "<EMAIL>";
        return null;
      });
    });

    it("should identify system emails", () => {
      const dto = new CreateEmailDto();
      const systemEmail = new EmailAddressDto();
      systemEmail.address = "<EMAIL>";
      systemEmail.name = "System";
      dto.from = [systemEmail];

      processor["determineEmailStatus"](dto, null);

      expect(dto.status).toBe(EmailStatus.SYSTEM_EMAIL_SKIPPED);
    });

    it("should preserve status for regular emails", () => {
      const dto = new CreateEmailDto();
      const userEmail = new EmailAddressDto();
      userEmail.address = "<EMAIL>";
      userEmail.name = "User";
      dto.from = [userEmail];
      dto.status = EmailStatus.SAVED;

      processor["determineEmailStatus"](dto, null);

      expect(dto.status).toBe(EmailStatus.SAVED);
    });

    it("should handle case-insensitive system email detection", () => {
      const dto = new CreateEmailDto();
      const systemEmail = new EmailAddressDto();
      systemEmail.address = "<EMAIL>"; // uppercase
      systemEmail.name = "System";
      dto.from = [systemEmail];

      processor["determineEmailStatus"](dto, null);

      expect(dto.status).toBe(EmailStatus.SYSTEM_EMAIL_SKIPPED);
    });
  });

  describe("handleEventEmission", () => {
    it("should emit EMAIL_SAVED for emails without attachments", () => {
      processor["handleEventEmission"](mockEmailRecord, []);

      expect(eventEmitter.emit).toHaveBeenCalledWith(
        EmailEvent.EMAIL_SAVED,
        expect.objectContaining({
          emailId: mockEmailRecord.id,
          gmailId: mockEmailRecord.gmailId,
          organizationId: mockEmailRecord.organization?.id,
          documents: []
        })
      );
    });

    it("should not emit events for emails with attachments", () => {
      const attachments = [
        { fileName: "test.pdf", mimeType: "application/pdf", buffer: Buffer.from("test") }
      ];

      processor["handleEventEmission"](mockEmailRecord, attachments);

      expect(eventEmitter.emit).not.toHaveBeenCalled();
    });

    it("should not emit events for system emails", () => {
      const systemEmailRecord = { ...mockEmailRecord, status: EmailStatus.SYSTEM_EMAIL_SKIPPED };

      processor["handleEventEmission"](systemEmailRecord, []);

      expect(eventEmitter.emit).not.toHaveBeenCalled();
    });
  });

  describe("Business Logic Edge Cases", () => {
    it("should handle malformed email addresses gracefully", () => {
      const dto = new CreateEmailDto();
      dto.from = []; // Empty array
      dto.status = EmailStatus.SAVED;

      processor["determineEmailStatus"](dto, null);

      expect(dto.status).toBe(EmailStatus.SAVED); // Should not change
    });

    it("should handle missing configuration gracefully", () => {
      configService.get.mockReturnValue(null); // No system emails configured

      const dto = new CreateEmailDto();
      const email = new EmailAddressDto();
      email.address = "<EMAIL>";
      email.name = "Any";
      dto.from = [email];
      dto.status = EmailStatus.SAVED;

      processor["determineEmailStatus"](dto, null);

      expect(dto.status).toBe(EmailStatus.SAVED); // Should not change
    });
  });
});
