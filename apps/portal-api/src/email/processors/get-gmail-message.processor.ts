import { Processor, WorkerHost } from "@nestjs/bullmq";
import { forwardRef, Inject, Logger, NotFoundException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import { validate } from "class-validator";
import { gmail_v1 } from "googleapis";
import moment from "moment";
import {
  Email,
  EMAIL_SAVED,
  EmailOrigin,
  EmailSavedEvent,
  EmailStatus,
  GmailToken,
  Organization,
  OrganizationType
} from "nest-modules";
import { FileBatchService } from "src/document/services/file-batch.service";
import { FileService } from "src/document/services/file.service";
import { ImporterService } from "src/importer/importer.service";
import { DataSource, Query<PERSON><PERSON><PERSON>, Repository } from "typeorm";
import { CreateEmailDto, EditEmailDto } from "../dto/email.dto";
import { EmailService } from "../services/email.service";
import { GmailService } from "../services/gmail.service";
import {
  AttachmentData,
  EmailProcessingContext,
  EmailProcessingInput,
  GmailMessageData
} from "../types/email-processing.types";
import { EmailEvent } from "../types/event.types";
import { GmailMessageFormat, ParsedGmailMessage } from "../types/gmail.types";
import { DEFAULT_WORKER_OPTIONS, EmailQueueName, GetGmailMessageJob } from "../types/queue.types";
import { DEFAULT_VALIDATION_OPTIONS } from "../types/validation.types";
import { generateRequest } from "../utils/generate-request";
import { EmailManualReviewRequiredEvent } from "../dto/event.dto";
import { EmailManualReviewReason } from "../types/email.types";

@Processor(
  {
    name: EmailQueueName.GET_GMAIL_MESSAGE
  },
  DEFAULT_WORKER_OPTIONS
)
export class GetGmailMessageProcessor extends WorkerHost {
  constructor(
    @InjectRepository(Email)
    private readonly emailRepository: Repository<Email>,
    @Inject(GmailService)
    private readonly gmailService: GmailService,
    @Inject(EmailService)
    private readonly emailService: EmailService,
    @Inject(forwardRef(() => ImporterService))
    private readonly importerService: ImporterService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly moduleRef: ModuleRef,
    private readonly eventEmitter: EventEmitter2,
    @Inject(ConfigService)
    private readonly configService: ConfigService
  ) {
    super();
    this.logger = new Logger(GetGmailMessageProcessor.name);
  }
  private readonly logger: Logger;

  /**
   * Fetches Gmail message and determines target format
   * Pure function that handles Gmail API calls
   */
  private async fetchGmailMessage(
    email: string,
    gmailId: string,
    existingEmailRecord: Email | null
  ): Promise<GmailMessageData> {
    const targetFormat = existingEmailRecord ? GmailMessageFormat.MINIMAL : GmailMessageFormat.FULL;
    this.logger.log(`Existing email record: ${existingEmailRecord?.id}, Format: ${targetFormat}`);

    const message = await this.gmailService.getMessage(email, gmailId, targetFormat);
    this.logger.log(`Gmail message ID: ${message?.id}, Label IDs: ${message?.labelIds}`);

    if (!message) {
      throw new NotFoundException("No Gmail message returned");
    }

    return { message, targetFormat };
  }

  /**
   * Updates Gmail token history ID for full sync operations
   */
  private async updateGmailTokenHistoryId(
    email: string,
    message: gmail_v1.Schema$Message,
    latestHistoryId: string | null
  ): Promise<void> {
    // Update Gmail token with this message's history ID only during full sync (latestHistoryId is null)
    // For partial sync, the listener handles history ID updates to prevent race conditions
    if (latestHistoryId === null && message?.historyId && /^\d+$/.test(message.historyId)) {
      this.logger.log(`Full sync: Updating Gmail token with message's history ID: ${message.historyId}`);

      const updateTokenResult = await this.dataSource.manager.update(
        GmailToken,
        { email },
        { lastSyncedHistoryId: message.historyId }
      );
      this.logger.log(
        `Updated ${updateTokenResult.affected} Gmail tokens to history ID ${message.historyId}`
      );
    } else if (latestHistoryId !== null) {
      this.logger.log(`Partial sync: Skipping token history ID update (handled by listener)`);
    }
  }

  /**
   * Extracts attachment data from Gmail message
   * Pure function that processes Gmail message and returns attachment data
   */
  private async extractAttachmentData(
    email: string,
    message: gmail_v1.Schema$Message,
    targetFormat: GmailMessageFormat
  ): Promise<{ attachments: AttachmentData[]; parsedMessage: ParsedGmailMessage | null }> {
    let attachments: AttachmentData[] = [];
    let parsedMessage: ParsedGmailMessage | null = null;

    if (targetFormat === GmailMessageFormat.FULL) {
      this.logger.log("Parsing full email message...");

      const { attachments: parsedAttachments, ...messageData } = await this.gmailService.parseGmailMessage(
        email,
        message
      );

      parsedMessage = { ...messageData, attachments: parsedAttachments };
      attachments = parsedAttachments || [];
    } else {
      this.logger.log(`Email format is minimal, skip parsing...`);
    }

    return { attachments, parsedMessage };
  }

  /**
   * Builds email DTO from Gmail message data
   * Pure function that creates and validates DTO
   */
  private async buildEmailDto(
    message: gmail_v1.Schema$Message,
    existingEmailRecord: Email | null,
    parsedMessage: ParsedGmailMessage | null,
    attachments: AttachmentData[]
  ): Promise<CreateEmailDto | EditEmailDto> {
    let createOrEditEmailDto: CreateEmailDto | EditEmailDto;

    if (!existingEmailRecord) {
      createOrEditEmailDto = new CreateEmailDto();
      createOrEditEmailDto.origin = EmailOrigin.INBOX;
      createOrEditEmailDto.inboxEmail = message.id; // This will be set properly in the main process
      createOrEditEmailDto.gmailId = message.id;
      createOrEditEmailDto.threadId = message.threadId;
      createOrEditEmailDto.receiveDate = moment
        .tz(Number.parseInt(message.internalDate), "America/Toronto")
        .toDate();
    } else {
      createOrEditEmailDto = new EditEmailDto();
    }

    createOrEditEmailDto.historyId = message.historyId;

    // Apply parsed message data if available
    if (parsedMessage) {
      Object.assign(createOrEditEmailDto, parsedMessage);
      // Setting email status based on attachments
      createOrEditEmailDto.status =
        attachments.length > 0 ? EmailStatus.UPLOADING_ATTACHMENTS : EmailStatus.SAVED;
    }

    // Validate DTO
    const validationErrors = await validate(createOrEditEmailDto, DEFAULT_VALIDATION_OPTIONS);
    if (validationErrors.length > 0) {
      this.logger.error(`DTO validation failed: ${JSON.stringify(validationErrors)}`);
      throw new Error(`DTO validation failed`);
    }

    return createOrEditEmailDto;
  }

  /**
   * Determines if email is from system and updates status accordingly
   * Pure function that checks sender addresses against system emails
   * @returns {EmailStatus | null} - The determined email status, or null if not a system email
   */
  private determineEmailStatus(
    createOrEditEmailDto: CreateEmailDto | EditEmailDto,
    existingEmailRecord: Email | null
  ): EmailStatus | null {
    const systemEmails = [
      this.configService.get<string>("SYSTEM_FROM_EMAIL"),
      this.configService.get<string>("BACKOFFICE_EMAIL_ADDRESS")
    ]
      .map((e) => e?.toLowerCase())
      .filter(Boolean);

    // Use from field from createOrEditEmailDto, or fallback to emailRecord if missing
    const senderAddresses = (
      createOrEditEmailDto.from && createOrEditEmailDto.from.length > 0
        ? createOrEditEmailDto.from
        : existingEmailRecord?.from || []
    )
      // filter out falsy / malformed items before lower-casing to prevent runtime errors
      .filter((f) => !!f?.address)
      .map((f) => f.address.toLowerCase());

    if (senderAddresses.some((addr) => systemEmails.includes(addr))) {
      this.logger.warn(
        `[SYSTEM_EMAIL_HANDLER] Identified system email from: ${senderAddresses.join(", ")}. Setting status to SYSTEM_EMAIL_SKIPPED.`
      );
      // Skip processing pipeline for system emails to prevent infinite loops
      return EmailStatus.SYSTEM_EMAIL_SKIPPED;
    }
    return null;
  }

  /**
   * Resolves importer and sets up processing context
   * Function that handles importer resolution and dependency injection context
   */
  private async resolveImporterAndContext(
    createOrEditEmailDto: CreateEmailDto | EditEmailDto,
    existingEmailRecord: Email | null,
    queryRunner: QueryRunner
  ): Promise<EmailProcessingContext> {
    let organization: Organization | null = null;
    let unknownImporter: boolean = false;
    if (createOrEditEmailDto.status === EmailStatus.SYSTEM_EMAIL_SKIPPED) {
      // Set current organization to backoffice organization
      this.logger.log(
        `System email found, using backoffice organization instead... Gmail ID: ${createOrEditEmailDto.gmailId}`
      );
      organization = await queryRunner.manager.findOneBy(Organization, {
        organizationType: OrganizationType.BACKOFFICE
      });
    } else {
      // Set current organization to the from email address's organization
      const toEmails = createOrEditEmailDto?.to || existingEmailRecord?.to || [];
      const importer =
        toEmails.length > 0
          ? await this.importerService.getImporterByReceiveEmail(
              toEmails.map(({ address }) => address),
              queryRunner,
              true
            )
          : null;

      this.logger.log(
        `To emails: ${toEmails.map(({ address }) => address).join(", ")}; Importer: ${importer?.id}`
      );

      if (!importer) {
        this.logger.warn(
          `No importer found for emails ${toEmails.map(({ address }) => address).join(", ")}, setting organization to backoffice`
        );
        organization = await queryRunner.manager.findOneBy(Organization, {
          organizationType: OrganizationType.BACKOFFICE
        });
        unknownImporter = true;
      } else organization = importer.organization;
    }

    if (!organization) throw new Error(`No organization found for email ${createOrEditEmailDto.gmailId}`);

    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(generateRequest(null, organization), contextId);

    const emailService = await this.moduleRef.resolve(EmailService, contextId);
    const fileService = await this.moduleRef.resolve(FileService, contextId, {
      strict: false
    });

    await new Promise((resolve) => process.nextTick(resolve)); // Wait for children dependencies to be initialized

    return { unknownImporter, organization, contextId, emailService, fileService };
  }

  /**
   * Processes email record creation or update
   * Function that handles email record persistence
   */
  private async processEmailRecord(
    existingEmailRecord: Email | null,
    createOrEditEmailDto: CreateEmailDto | EditEmailDto,
    context: EmailProcessingContext,
    queryRunner: QueryRunner,
    email: string
  ): Promise<Email> {
    // Set the inbox email properly for new records
    if (!existingEmailRecord && "inboxEmail" in createOrEditEmailDto) {
      createOrEditEmailDto.inboxEmail = email;
    }

    const emailRecord = await (existingEmailRecord
      ? context.emailService.editEmail(
          existingEmailRecord.id,
          createOrEditEmailDto as EditEmailDto,
          queryRunner
        )
      : context.emailService.createEmail(createOrEditEmailDto as CreateEmailDto, queryRunner));

    this.logger.log(`Saved email record: ${emailRecord?.id}`);
    return emailRecord;
  }

  /**
   * Handles attachment upload and FileBatch association
   * Uploads email attachments to file storage and associates them with existing thread shipments
   *
   * @param attachments - Array of attachment data extracted from Gmail message
   * @param emailRecord - The email record to associate attachments with
   * @param context - Email processing context containing resolved services
   * @param queryRunner - Database transaction query runner
   * @returns Updated email record after attachment processing
   */
  private async handleAttachmentUpload(
    attachments: AttachmentData[],
    emailRecord: Email,
    context: EmailProcessingContext,
    queryRunner: QueryRunner
  ): Promise<Email> {
    // Only upload attachments for non-system emails
    if (attachments.length === 0 || emailRecord.status === EmailStatus.SYSTEM_EMAIL_SKIPPED) {
      if (emailRecord.status === EmailStatus.SYSTEM_EMAIL_SKIPPED && attachments.length > 0) {
        this.logger.log(
          `[SYSTEM_EMAIL_HANDLER] Email ${emailRecord.id} is a system email with ${attachments.length} attachments. Skipping attachment upload.`
        );
      }
      return emailRecord;
    }

    this.logger.log(`Got ${attachments?.length} attachments, uploading...`);

    const { files: uploadedFiles, batchId } = await context.fileService.saveEmailAttachments(
      emailRecord.gmailId,
      attachments.map(({ buffer, fileName, mimeType }) => ({
        fieldname: null,
        originalname: fileName,
        encoding: null,
        mimetype: mimeType,
        size: buffer?.length,
        stream: null,
        destination: null,
        filename: fileName,
        path: null,
        buffer
      })),
      {
        organizationId: context.organization.id
      }
    );

    // Check for existing thread shipment and associate FileBatch if found
    const existingThreadShipment = await context.emailService.getEmailThreadShipment(
      emailRecord.threadId,
      queryRunner
    );

    if (existingThreadShipment) {
      this.logger.log(
        `Found existing thread shipment ${existingThreadShipment.id}, associating with FileBatch ${batchId}`
      );
      const fileBatchService = await this.moduleRef.resolve(FileBatchService, context.contextId, {
        strict: false
      });
      try {
        await fileBatchService.setBatchShipmentId(batchId, existingThreadShipment.id);
      } catch (error) {
        this.logger.error(
          `Failed to associate FileBatch ${batchId} with shipment ${existingThreadShipment.id}: ${error.message}`,
          error.stack
        );
        // Do not re-throw to allow main transaction to continue
      }
    }

    this.logger.log(`Uploaded ${uploadedFiles?.length} attachments. Batch ID: ${batchId}`);
    if (uploadedFiles?.length !== attachments.length) {
      throw new Error(
        `Attachment upload failed: ${uploadedFiles?.length || 0}/${attachments.length} files uploaded successfully`
      );
    }

    return emailRecord;
  }

  /**
   * Processes email from Gmail message data without making Gmail API calls
   *
   * This method handles steps 4-9 of the email processing pipeline:
   * - Step 4: Extract attachment data and build email DTO
   * - Step 5: Determine if email is from system and update status
   * - Step 6: Resolve importer and set up processing context
   * - Step 7: Process email record (create or update)
   * - Step 8: Handle attachment upload and FileBatch association
   * - Step 9: Handle email processing initiation (emit events)
   *
   * This method can be called directly from test scripts to bypass Gmail API calls
   * while still testing the complete email processing and saving logic.
   *
   * @param input - Email processing input containing Gmail message and metadata
   * @param input.email - The email address (inbox email)
   * @param input.message - Gmail message object from Gmail API
   * @param input.targetFormat - Target format for message processing (FULL or MINIMAL)
   * @param input.existingEmailRecord - Existing email record if updating, null if creating new
   * @param input.latestHistoryId - Latest history ID for batch operations, null for full sync
   * @returns Promise<Email> - The processed and saved email record
   * @throws Error if email processing fails (importer not found, validation errors, etc.)
   *
   * @example
   * ```typescript
   * // Example usage in test scripts
   * const result = await processor.processEmailFromGmailMessage({
   *   email: '<EMAIL>',
   *   message: mockGmailMessage,
   *   targetFormat: GmailMessageFormat.FULL,
   *   existingEmailRecord: null,
   *   latestHistoryId: null
   * });
   * ```
   */
  public async processEmailFromGmailMessage(input: EmailProcessingInput): Promise<Email> {
    const { email, message, targetFormat, existingEmailRecord } = input;

    this.logger.log(
      `Processing email from Gmail message for ${email} with ID ${message.id}. Format: ${targetFormat}`
    );

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      let emailRecord = existingEmailRecord;

      // Step 4: Extract attachment data and build email DTO
      const { attachments, parsedMessage } = await this.extractAttachmentData(email, message, targetFormat);
      const createOrEditEmailDto = await this.buildEmailDto(message, emailRecord, parsedMessage, attachments);

      // Step 5: Determine if email is from system and update status
      const systemStatus = this.determineEmailStatus(createOrEditEmailDto, emailRecord);
      if (systemStatus === EmailStatus.SYSTEM_EMAIL_SKIPPED) {
        createOrEditEmailDto.status = systemStatus;
        await queryRunner.commitTransaction();
        return emailRecord;
      }

      // Step 6: Resolve importer and set up processing context
      const context = await this.resolveImporterAndContext(createOrEditEmailDto, emailRecord, queryRunner);

      // Step 7: Process email record (create or update)
      emailRecord = await this.processEmailRecord(
        emailRecord,
        createOrEditEmailDto,
        context,
        queryRunner,
        email
      );

      // Set status to UPLOADING_ATTACHMENTS before attachment upload
      emailRecord = await context.emailService.editEmail(
        emailRecord.id,
        { status: EmailStatus.UPLOADING_ATTACHMENTS },
        queryRunner
      );

      // Step 8: Handle attachment upload and FileBatch association
      try {
        emailRecord = await this.handleAttachmentUpload(attachments, emailRecord, context, queryRunner);
      } catch (error) {
        this.logger.error(`Attachment handling failed for email ${emailRecord.id}: ${error.message}`);
        emailRecord = await context.emailService.editEmail(
          emailRecord.id,
          { status: EmailStatus.FAILED_SAVING_EMAIL },
          queryRunner
        );
        await queryRunner.commitTransaction();
        return emailRecord;
      }

      // Update email status to SAVED after successful attachment upload
      // If the importer is unknown, the email should go to manual review and wait for backoffice admin to re-assign it to the proper organization
      emailRecord = await context.emailService.editEmail(
        emailRecord.id,
        context.unknownImporter
          ? {
              status: EmailStatus.MANUAL_REVIEW,
              error: "Unknown organization for the email. Reassignment required."
            }
          : { status: EmailStatus.SAVED },
        queryRunner
      );

      await queryRunner.commitTransaction();

      await new Promise((resolve) => process.nextTick(resolve)); // Wait for completion of the transaction

      this.logger.log(`Successfully processed email record: ${emailRecord.id}, emitting EMAIL_SAVED event`);

      // Step 9: Handle email processing initiation
      if (context.unknownImporter) {
        // Email EMAIL_MANUAL_REVIEW_REQUIRED event to send manual review email
        this.eventEmitter.emit(
          EmailEvent.EMAIL_MANUAL_REVIEW_REQUIRED,
          new EmailManualReviewRequiredEvent({
            emailId: emailRecord.id,
            organizationId: emailRecord.organization?.id,
            reviewReason: EmailManualReviewReason.UNKNOWN_ORGANIZATION
          })
        );
      } else {
        // Emit EMAIL_SAVED event for core-agent module to handle
        this.eventEmitter.emit(
          EMAIL_SAVED,
          new EmailSavedEvent({
            emailId: emailRecord.id,
            organizationId: emailRecord.organization?.id,
            gmailId: emailRecord.gmailId,
            documents: [] // Documents will be populated after extraction
          })
        );
      }

      return emailRecord;
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      const errorMessage = `Email processing from Gmail message failed: ${error.message}. Email: ${email}, Gmail ID: ${message.id}`;
      this.logger.error(errorMessage);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async process(job: GetGmailMessageJob) {
    this.logger.log(
      `Starting get Gmail message for ${job.data.email} with ID ${job.data.gmailId}. Batch Latest History ID: ${job.data.latestHistoryId}`
    );

    const { email, gmailId, latestHistoryId } = job.data;

    try {
      // Step 1: Get existing email record and fetch Gmail message
      const emailRecord = await this.emailService.getEmailByGmailId(gmailId);
      const { message, targetFormat } = await this.fetchGmailMessage(email, gmailId, emailRecord);

      // Step 2: Update Gmail token history ID if needed
      await this.updateGmailTokenHistoryId(email, message, latestHistoryId);

      // Step 3: Skip email that is not from inbox
      if (!message?.labelIds?.includes("INBOX")) {
        this.logger.log(`Email is not from inbox, skipping...`);
        return;
      }

      // Steps 4-9: Process email using the new email processing method
      await this.processEmailFromGmailMessage({
        email,
        message,
        targetFormat,
        existingEmailRecord: emailRecord,
        latestHistoryId
      });
    } catch (error) {
      const errorMessage = `Get Gmail message process failed: ${error.message}. Email: ${email}, Gmail ID: ${gmailId}`;
      this.logger.error(errorMessage);
      this.eventEmitter.emit(EmailEvent.GET_GMAIL_MESSAGE_FAILED, job, error);
    }
  }
}
