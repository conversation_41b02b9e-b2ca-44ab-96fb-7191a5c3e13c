import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import {
  AuthenticatedRequest,
  CommercialInvoice,
  convertFromCamelCase,
  CountryService,
  CreateTradePartnerDto,
  EditTradePartnerDto,
  FIND_TRADE_PARTNER_RELATIONS,
  getFindOptions,
  GetTradePartnersDto,
  GetTradePartnersResponseDto,
  MatchingCondition,
  MatchingConditionOperator,
  MergeTradePartnerDto,
  OgdFiling,
  Product,
  Shipment,
  TRADE_PARTNER_ENUM_KEYS,
  TRADE_PARTNER_ID_ATTRIBUTES,
  TRADE_PARTNER_REQUIRED_KEYS,
  TradePartner,
  TradePartnerColumn,
  TradePartnerLinkedRelationKey,
  TradePartnerUsageCount,
  UserPermission
} from "nest-modules";
import { DataSource, <PERSON>Null, Not, Query<PERSON>unner, Raw, Repository, SelectQueryBuilder } from "typeorm";

@Injectable({ scope: Scope.REQUEST })
export class TradePartnerService {
  constructor(
    @InjectRepository(TradePartner)
    private readonly tradePartnerRepository: Repository<TradePartner>,
    @Inject(CountryService)
    private readonly countryService: CountryService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}

  private readonly logger = new Logger(TradePartnerService.name);

  private readonly relationTypeMap: Record<
    TradePartnerLinkedRelationKey,
    typeof CommercialInvoice | typeof Shipment | typeof Product | typeof OgdFiling
  > = {
    purchaserCommercialInvoice: CommercialInvoice,
    exporterCommercialInvoice: CommercialInvoice,
    vendorCommercialInvoice: CommercialInvoice,
    manufacturerCommercialInvoice: CommercialInvoice,
    shipToCommercialInvoice: CommercialInvoice,
    carrierShipments: Shipment,
    manufacturerShipments: Shipment,
    shipperShipments: Shipment,
    consigneeShipments: Shipment,
    forwarderShipments: Shipment,
    truckerShipments: Shipment,
    pickupLocationShipments: Shipment,
    vendorProducts: Product,
    manufacturerProducts: Product,
    manufacturerOgdFilings: OgdFiling
  };

  async getTradePartners(getTradePartnersDto: GetTradePartnersDto): Promise<GetTradePartnersResponseDto> {
    // if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
    //   getTradePartnersDto.organizationId = this.request?.user?.organization?.id || -1;
    const { organizationId, ...dto } = getTradePartnersDto;
    const { where, order, skip, take } = getFindOptions<TradePartner>(
      dto,
      TRADE_PARTNER_ENUM_KEYS,
      [],
      TradePartnerColumn.id
    );
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN) {
      where.organization = {
        id: Raw((alias) => `${alias} = :organizationId OR ${alias} IS NULL`, {
          organizationId: this.request?.user?.organization?.id || -1
        })
      };
    } else if (organizationId) {
      where.organization = {
        id: organizationId
      };
    }
    const [partners, total] = await this.tradePartnerRepository.findAndCount({
      where,
      relations: FIND_TRADE_PARTNER_RELATIONS,
      order,
      skip,
      take
    });
    return {
      partners,
      total,
      skip,
      limit: take
    };
  }

  async getTradePartnerById(partnerId: number, queryRunner?: QueryRunner) {
    const tradePartnerRepository = queryRunner
      ? queryRunner.manager.getRepository(TradePartner)
      : this.tradePartnerRepository;
    return await tradePartnerRepository.findOne({
      where: {
        id: partnerId,
        organization: {
          id: Raw(
            (alias) =>
              `${alias} IS NULL OR ${alias} ${this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN ? "= :organizationId" : "IS NOT NULL"}`,
            { organizationId: this.request?.user?.organization?.id || -1 }
          )
        }
      },
      relations: FIND_TRADE_PARTNER_RELATIONS
    });
  }

  async createTradePartner(createTradePartnerDto: CreateTradePartnerDto, queryRunner?: QueryRunner) {
    const tradePartnerRepository = queryRunner
      ? queryRunner.manager.getRepository(TradePartner)
      : this.tradePartnerRepository;
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      createTradePartnerDto.isGlobal = false;
    const { isGlobal, name, vendorCode, countryId, ...createTradePartnerProps } = createTradePartnerDto;
    if (
      await tradePartnerRepository.existsBy({
        name,
        ...(createTradePartnerDto.city ? { city: createTradePartnerDto.city } : {}),
        ...(createTradePartnerDto.state ? { state: createTradePartnerDto.state } : {}),
        ...(typeof countryId === "number" ? { country: { id: countryId } } : {}),
        organization: { id: isGlobal ? IsNull() : this.request?.user?.organization?.id }
      })
    )
      throw new BadRequestException("Trade Partner already exists");

    let newPartner = new TradePartner();
    newPartner.name = name;
    if (typeof countryId === "number") {
      newPartner.country = await this.countryService.getCountryById(countryId);
      if (!newPartner.country) throw new NotFoundException("Country not found");
    }
    newPartner.organization = isGlobal ? null : this.request?.user?.organization;
    newPartner.createdBy = this.request?.user || null;
    newPartner.lastEditedBy = this.request?.user || null;
    for (const [key, value] of Object.entries(createTradePartnerProps)) {
      if (value === undefined) continue;
      if (TRADE_PARTNER_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      newPartner[key] = value;
    }
    // if (newPartner.partnerType === PartnerType.VENDOR) {
    //   if (typeof vendorCode !== "string" || vendorCode.length <= 0)
    //     throw new BadRequestException(
    //       "Vendor code is required for vendor type"
    //     );
    //   newPartner.vendorCode = vendorCode;
    // }
    newPartner = await tradePartnerRepository.save(newPartner);

    return await this.getTradePartnerById(newPartner.id, queryRunner);
  }

  async editTradePartner(
    partnerId: number,
    editTradePartnerDto: EditTradePartnerDto,
    queryRunner?: QueryRunner
  ) {
    const tradePartnerRepository = queryRunner
      ? queryRunner.manager.getRepository(TradePartner)
      : this.tradePartnerRepository;

    const partner = await this.getTradePartnerById(partnerId, queryRunner);
    if (!partner) throw new NotFoundException("Trade Partner not found");
    if (!partner.organization && this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user cannot edit global trade partner");
    if (partner.organization && partner.organization?.id !== this.request?.user?.organization?.id)
      throw new BadRequestException("Trade Partner does not belong to the organization of current user");
    const { name, vendorCode, countryId, ...editTradePartnerProps } = editTradePartnerDto;

    partner.lastEditedBy = this.request?.user || null;
    if (typeof name === "string") {
      if (
        await this.tradePartnerRepository.existsBy({
          id: Not(partnerId),
          name,
          ...(editTradePartnerDto.city ? { city: editTradePartnerDto.city } : {}),
          ...(editTradePartnerDto.state ? { state: editTradePartnerDto.state } : {}),
          ...(typeof countryId === "number" ? { country: { id: countryId } } : {}),
          organization: { id: this.request?.user?.organization?.id }
        })
      )
        throw new BadRequestException("Trade Partner with the same name already exists");
      partner.name = name;
    }
    if (typeof countryId === "number") {
      partner.country = await this.countryService.getCountryById(countryId);
      if (!partner.country) throw new NotFoundException("Country not found");
    }
    for (const [key, value] of Object.entries(editTradePartnerProps)) {
      if (value === undefined) continue;
      if (TRADE_PARTNER_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      partner[key] = value;
    }
    // if (partner.partnerType === PartnerType.VENDOR) {
    //   if (typeof vendorCode === "string" && vendorCode.length > 0)
    //     partner.vendorCode = vendorCode;
    //   else if (
    //     typeof partner.vendorCode !== "string" ||
    //     partner.vendorCode.length <= 0
    //   )
    //     throw new BadRequestException(
    //       "Vendor code is required for vendor type"
    //     );
    // } else partner.vendorCode = null;
    await tradePartnerRepository.save(partner);

    return await this.getTradePartnerById(partnerId, queryRunner);
  }

  async deleteTradePartner(partnerId: number) {
    const partner = await this.getTradePartnerById(partnerId);
    if (!partner) throw new NotFoundException("Trade Partner not found");
    if (!partner.organization && this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user cannot delete global trade partner");
    if (partner.organization && partner.organization?.id !== this.request?.user?.organization?.id)
      throw new BadRequestException("Trade Partner does not belong to the organization of current user");

    const canRemoveTradePartner = await this.checkCanRemoveTradePartner(partnerId);

    if (!canRemoveTradePartner) {
      throw new BadRequestException("Trade partner is in use");
    }

    await this.tradePartnerRepository.delete({ id: partnerId });

    return;
  }

  /**
   * Add the empty trade partner relation filters to the query
   *
   * This is used to find trade partners that are not associated with any other entities
   *
   * @param query
   */
  private addEmptyTradePartnerRelationFilters(query: SelectQueryBuilder<TradePartner>) {
    for (const [relation, _] of Object.entries(this.relationTypeMap)) {
      query.leftJoin(`trade_partner.${relation}`, relation);
      query.andHaving(`count("${relation}") = 0`);
    }

    query.groupBy("trade_partner.id");
  }

  public async getTradePartnerUsageCount(tradePartnerId: number): Promise<TradePartnerUsageCount> {
    const query = this.tradePartnerRepository
      .createQueryBuilder("trade_partner")
      .select("trade_partner.id as trade_partner_id")
      .where("trade_partner.id = :tradePartnerId", { tradePartnerId });

    for (const [relation, _] of Object.entries(this.relationTypeMap)) {
      query.leftJoin(`trade_partner.${relation}`, relation);
      query.addSelect(`count("${relation}") as "${relation}"`);
    }

    query.groupBy("trade_partner.id");

    const result = await query.getRawOne<TradePartnerUsageCount>();

    // cast the result to a number
    for (const [key, value] of Object.entries(result)) {
      result[key] = Number(value);
    }

    return result;
  }

  async pruneTradePartners(dryRun: boolean = true) {
    const query = this.tradePartnerRepository
      .createQueryBuilder("trade_partner")
      .where("trade_partner.organizationId = :organizationId", {
        organizationId: this.request?.user?.organization?.id
      });

    this.addEmptyTradePartnerRelationFilters(query);

    const tradePartners = await query.getMany();

    if (dryRun) {
      return tradePartners;
    }

    for (const tradePartner of tradePartners) {
      await this.deleteTradePartner(tradePartner.id);
    }
    return tradePartners;
  }

  /**
   * Check if a trade partner can be removed
   *
   * @param tradePartnerId
   * @returns
   */
  async checkCanRemoveTradePartner(tradePartnerId: number) {
    const query = this.tradePartnerRepository
      .createQueryBuilder("trade_partner")
      .select("trade_partner.id")
      .where("trade_partner.id = :tradePartnerId", { tradePartnerId });

    this.addEmptyTradePartnerRelationFilters(query);

    // if the count is 1, then the trade partner is not in use
    const count = await query.getCount();

    return count === 1;
  }

  private async getTradePartnerInversedRelation() {
    return this.tradePartnerRepository.metadata.relations
      .filter((relation) => Object.keys(this.relationTypeMap).includes(relation.propertyName))
      .map((relation) => {
        return {
          propertyName: relation.inverseRelation.propertyName,
          type: relation.type
        };
      });
  }

  /**
   * Merge two trade partners into one, and update all relations to point to the target trade partner.
   *
   * @param mergeTradePartnerDto
   * @returns
   */
  async mergeTradePartners(mergeTradePartnerDto: MergeTradePartnerDto) {
    const { sourceTradePartnerId, targetTradePartnerId, mergedData } = mergeTradePartnerDto;

    if (sourceTradePartnerId === targetTradePartnerId) {
      throw new BadRequestException("Source and target trade partner cannot be the same");
    }

    const sourceTradePartner = await this.getTradePartnerById(sourceTradePartnerId);
    const targetTradePartner = await this.getTradePartnerById(targetTradePartnerId);

    if (!sourceTradePartner || !targetTradePartner) {
      throw new NotFoundException("Trade Partner not found");
    }

    if (sourceTradePartner.organization?.id !== targetTradePartner.organization?.id) {
      throw new BadRequestException("Source and target trade partner must belong to the same organization");
    }

    const inversedRelations = await this.getTradePartnerInversedRelation();

    // Update the target trade partner with the merged data

    // Update all relations of the source trade partner to point to the target trade partner
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      await this.editTradePartner(targetTradePartnerId, mergedData, queryRunner);

      for (const relation of inversedRelations) {
        const relationRepository = queryRunner.manager.getRepository(relation.type);

        const reposityName = relationRepository.metadata.name;

        const { affected } = await relationRepository.update(
          { [relation.propertyName]: sourceTradePartnerId },
          { [relation.propertyName]: targetTradePartnerId }
        );

        this.logger.log(
          `[${reposityName}.${relation.propertyName}] ${sourceTradePartnerId} -> ${targetTradePartnerId} (${affected} updated)`
        );
      }

      // Update matching conditions that reference the source trade partner
      const matchingConditionRepository = queryRunner.manager.getRepository(MatchingCondition);

      for (const attribute in TRADE_PARTNER_ID_ATTRIBUTES) {
        const { affected } = await matchingConditionRepository.update(
          {
            // NOTE: we currently don't support in relation
            attribute: attribute,
            operator: MatchingConditionOperator.EQUALS,
            value: String(sourceTradePartnerId)
          },
          {
            value: String(targetTradePartnerId),
            valueInteger: [targetTradePartnerId]
          }
        );

        this.logger.log(
          `[MatchingCondition] ${attribute} ${sourceTradePartnerId} -> ${targetTradePartnerId} (${affected} updated)`
        );
      }

      await queryRunner.manager.getRepository(TradePartner).delete({ id: sourceTradePartnerId });

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new BadRequestException(`Failed to merge trade partners: ${error.message}`);
    } finally {
      await queryRunner.release();
    }

    return targetTradePartner;
  }
}
