import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CountryModule, TradePartner } from "nest-modules";
import { TradePartnerController } from "./trade-partner.controller";
import { TradePartnerService } from "./trade-partner.service";

@Module({
  imports: [TypeOrmModule.forFeature([TradePartner]), CountryModule],
  providers: [TradePartnerService],
  controllers: [TradePartnerController],
  exports: [TradePartnerService]
})
export class TradePartnerModule {}
