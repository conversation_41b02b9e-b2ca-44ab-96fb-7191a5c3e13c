import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  ParseBoolPipe,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards
} from "@nestjs/common";
import { ApiOperation, ApiParam, ApiQuery, ApiTags } from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses,
  CreateTradePartnerDto,
  EditTradePartnerDto,
  GetTradePartnersDto,
  GetTradePartnersResponseDto,
  MergeTradePartnerDto,
  TradePartner,
  TradePartnerUsageCount
} from "nest-modules";
import { TradePartnerService } from "./trade-partner.service";

@ApiTags("Trade Partner API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("trade-partners")
export class TradePartnerController {
  constructor(private readonly tradePartnerService: TradePartnerService) {}

  @ApiOperation({ summary: "Get Trade Partners" })
  @ApiGetManyResponses({ type: GetTradePartnersResponseDto })
  @Get()
  async getTradePartners(@Query() getTradePartnersDto: GetTradePartnersDto) {
    return await this.tradePartnerService.getTradePartners(getTradePartnersDto);
  }

  @ApiOperation({ summary: "Get Trade Partner" })
  @ApiParam({ name: "id", type: "integer", description: "Trade Partner ID" })
  @ApiGetByIdResponses({ type: TradePartner })
  @Get(":id")
  async getTradePartnerById(@Param("id", ParseIntPipe) id: number) {
    const partner = await this.tradePartnerService.getTradePartnerById(id);
    if (!partner) throw new NotFoundException("Trade Partner not found");
    return partner;
  }

  @ApiOperation({ summary: "Create Trade Partner" })
  @ApiCreateResponses({ type: TradePartner })
  @Post()
  async createTradePartner(@Body() createTradePartnerDto: CreateTradePartnerDto) {
    return await this.tradePartnerService.createTradePartner(createTradePartnerDto);
  }

  @ApiOperation({ summary: "Edit Trade Partner" })
  @ApiParam({ name: "id", type: "integer", description: "Trade Partner ID" })
  @ApiEditResponses({ type: TradePartner })
  @Put(":id")
  async editTradePartner(
    @Param("id", ParseIntPipe) id: number,
    @Body() editTradePartnerDto: EditTradePartnerDto
  ) {
    return await this.tradePartnerService.editTradePartner(id, editTradePartnerDto);
  }

  @ApiOperation({ summary: "Prune Dangling Trade Partners" })
  @Get("/maintenance/prune")
  @ApiQuery({
    name: "dryRun",
    type: "boolean",
    required: false,
    description: "Whether to actually delete the trade partners"
  })
  async pruneTradePartners(
    @Query("dryRun", new DefaultValuePipe(true), ParseBoolPipe) dryRun: boolean = true
  ) {
    return await this.tradePartnerService.pruneTradePartners(dryRun);
  }

  @ApiOperation({ summary: "Delete Trade Partner" })
  @ApiParam({ name: "id", type: "integer", description: "Trade Partner ID" })
  @ApiDeleteResponses()
  @HttpCode(204)
  @Delete(":id")
  async deleteTradePartner(@Param("id", ParseIntPipe) id: number) {
    await this.tradePartnerService.deleteTradePartner(id);
    return;
  }

  @ApiOperation({ summary: "Get Trade Partner Usage Count" })
  @ApiParam({ name: "id", type: "integer", description: "Trade Partner ID" })
  @ApiGetByIdResponses({ type: TradePartnerUsageCount })
  @Get(":id/usage-count")
  async getTradePartnerUsageCount(@Param("id", ParseIntPipe) id: number) {
    return await this.tradePartnerService.getTradePartnerUsageCount(id);
  }

  @ApiOperation({ summary: "Merge Trade Partners" })
  @ApiCreateResponses({ type: TradePartner })
  @Post("merge")
  async mergeTradePartners(@Body() mergeTradePartnerDto: MergeTradePartnerDto) {
    return await this.tradePartnerService.mergeTradePartners(mergeTradePartnerDto);
  }
}
