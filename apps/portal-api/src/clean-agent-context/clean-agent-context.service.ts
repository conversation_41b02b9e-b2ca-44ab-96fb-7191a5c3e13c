import { Injectable, Logger, Type } from "@nestjs/common";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import { InjectDataSource } from "@nestjs/typeorm";
import { ClsService } from "nestjs-cls";
import { DataSource } from "typeorm";
import { ShipmentDataGatherer } from "./shipment-data-gatherer.service";
import { DocumentDataGatherer } from "./document-data-gatherer.service";
import { CandataDataGatherer } from "./candata-data-gatherer.service";
import { ImporterDataGatherer } from "./importer-data-gatherer.service";
import { ComplianceValidationDataGatherer } from "./compliance-validation-data-gatherer.service";
import {
  ShipmentTemplateData,
  DocumentTemplateData,
  RnsTemplateData,
  ValidationTemplateData,
  ImporterTemplateData,
  UnifiedTemplateContext,
  EmailContextOptions,
  DocumentGatherRequest
} from "./clean-agent-context.types";
import { Shipment } from "nest-modules";
import { emailResponseMessages } from "./email-response-messages";

/**
 * Clean Agent Context Service - Service Integration Hub
 *
 * A singleton service designed for use in BullMQ processors that demonstrates
 * how to resolve both request-scoped and default-scoped services from a
 * DEFAULT-scoped service using the proper processor patterns.
 *
 * Integrates with multiple services to gather comprehensive template data.
 */
@Injectable()
export class CleanAgentContextService {
  private logger: Logger;

  constructor(
    private readonly moduleRef: ModuleRef,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly cls: ClsService,
    private readonly documentDataGatherer: DocumentDataGatherer,
    private readonly complianceValidationDataGatherer: ComplianceValidationDataGatherer
  ) {
    console.log("CleanAgentContextService constructor called");
    this.initializeLogger();
    console.log("Logger initialized:", !!this.logger);
  }

  /**
   * Initialize logger with proper fallback handling
   */
  private initializeLogger(): void {
    try {
      this.logger = new Logger(CleanAgentContextService.name);
    } catch (error) {
      // Fallback to console if Logger initialization fails
      console.warn("Failed to initialize NestJS Logger, falling back to console:", error.message);
      this.logger = {
        log: console.log.bind(console),
        debug: console.debug?.bind(console) || console.log.bind(console),
        warn: console.warn.bind(console),
        error: console.error.bind(console)
      } as Logger;
    }
  }

  /**
   * Safe logger method with fallback
   */
  private safeLog(
    level: "log" | "debug" | "warn" | "error",
    message: string,
    ...optionalParams: any[]
  ): void {
    try {
      if (this.logger && this.logger[level]) {
        this.logger[level](message, ...optionalParams);
      } else {
        console[level](`[CleanAgentContextService] ${message}`, ...optionalParams);
      }
    } catch (error) {
      console.log(`[CleanAgentContextService] ${message}`, ...optionalParams);
    }
  }

  /**
   * Gather shipment template data using ShipmentDataGatherer processor
   * @param shipmentId - The shipment ID to gather data for
   * @param organizationId - The organization ID for proper scoping
   * @param request - Request object with user context
   * @returns Shipment template data ready for use
   */
  async gatherShipmentData(
    shipmentId: number,
    organizationId: number,
    request: any
  ): Promise<ShipmentTemplateData> {
    console.log("gatherShipmentData called, logger is:", !!this.logger);
    this.safeLog("log", `Gathering shipment data for shipment ${shipmentId}, org ${organizationId}`);

    // Use ClsService to run with proper context, just like AggregationExecuter
    return await this.cls.run(async () => {
      // Set organization ID in CLS context
      this.cls.set("ORGANIZATION_ID", organizationId);

      // Resolve request-scoped ShipmentDataGatherer processor
      this.safeLog("debug", "Resolving ShipmentDataGatherer processor with request context");
      const gatherer = await this.resolveRequestScopedService(ShipmentDataGatherer, request);

      // Use the processor to gather data
      this.safeLog("debug", `Using processor to gather shipment ${shipmentId} data`);
      const templateData = await gatherer.gather({ shipmentId });

      this.safeLog("log", `Template data prepared for shipment ${shipmentId}`);
      return templateData;
    });
  }

  /**
   * Gather document template data using DocumentDataGatherer processor
   * @param shipmentId - The shipment ID to gather data for
   * @param organizationId - The organization ID for proper scoping
   * @param request - Request object with user context
   * @param options - Optional email context options for email-specific processing
   * @returns Document template data ready for use
   */
  async gatherDocumentData(
    shipmentId: number,
    organizationId: number,
    _request: any, // eslint-disable-line @typescript-eslint/no-unused-vars
    options?: EmailContextOptions
  ): Promise<DocumentTemplateData> {
    this.safeLog("log", `Gathering document data for shipment ${shipmentId}, org ${organizationId}`);

    // Use ClsService to run with proper context, just like AggregationExecuter
    return await this.cls.run(async () => {
      // Set organization ID in CLS context
      this.cls.set("ORGANIZATION_ID", organizationId);

      // For DEFAULT-scoped services, inject directly (no ModuleRef needed)
      // DocumentDataGatherer uses DocumentService (DEFAULT with ClsService)
      this.safeLog("debug", "Using DocumentDataGatherer processor (DEFAULT-scoped)");
      const gatherer = this.documentDataGatherer;

      // Use the processor to gather data
      this.safeLog("debug", `Using processor to gather document data for shipment ${shipmentId}`);

      // Build request with optional email context
      const gatherRequest: DocumentGatherRequest = {
        shipmentId,
        emailContext:
          options?.gmailId && options?.includeEmailAttachments
            ? {
                emailId: options.emailId!,
                gmailId: options.gmailId,
                includeEmailAttachments: options.includeEmailAttachments
              }
            : undefined
      };

      const templateData = await gatherer.gather(gatherRequest);

      this.safeLog("log", `Document template data prepared for shipment ${shipmentId}`);
      return templateData;
    });
  }

  /**
   * Gather RNS template data using CandataDataGatherer processor
   * @param shipmentId - The shipment ID to gather data for
   * @param organizationId - The organization ID for proper scoping
   * @param request - Request object with user context
   * @param shipmentData - Optional shipment data to provide context
   * @returns RNS template data ready for use
   */
  async gatherRnsData(
    shipmentId: number,
    organizationId: number,
    request: any,
    shipmentData?: any
  ): Promise<RnsTemplateData> {
    this.safeLog("log", `Gathering RNS data for shipment ${shipmentId}, org ${organizationId}`);

    // Use ClsService to run with proper context, just like AggregationExecuter
    return await this.cls.run(async () => {
      // Set organization ID in CLS context
      this.cls.set("ORGANIZATION_ID", organizationId);

      // For REQUEST-scoped services, use ModuleRef resolution
      // CandataDataGatherer uses CandataService (REQUEST-scoped)
      this.safeLog("debug", "Resolving CandataDataGatherer processor with request context");
      const gatherer = await this.resolveRequestScopedService(CandataDataGatherer, request);

      // Prepare parameters from shipment data
      const gatherParams = {
        transactionNumber: shipmentData?.TRANSACTION_NUMBER || shipmentData?.transactionNumber,
        cargoControlNumber: shipmentData?.CARGO_CONTROL_NUMBER || shipmentData?.cargoControlNumber,
        shipment: shipmentData?.shipment || shipmentData
      };

      // Use the processor to gather data
      this.safeLog("debug", `Using processor to gather RNS data for shipment ${shipmentId}`);
      const templateData = await gatherer.gather(gatherParams);

      this.safeLog("log", `RNS template data prepared for shipment ${shipmentId}`);
      return templateData;
    });
  }

  /**
   * Gather importer template data using ImporterDataGatherer processor
   * @param organizationId - The organization ID for proper scoping
   * @param request - Request object with user context
   * @param importerId - Optional importer ID
   * @param importerEmail - Optional importer email
   * @param importer - Optional importer data to provide context
   * @returns Importer template data ready for use
   */
  async gatherImporterData(
    organizationId: number,
    request: any,
    importerId?: number,
    importerEmail?: string,
    importer?: any
  ): Promise<ImporterTemplateData> {
    this.safeLog(
      "log",
      `Gathering importer data for ID ${importerId} or email ${importerEmail}, org ${organizationId}`
    );

    // Use ClsService to run with proper context, just like AggregationExecuter
    return await this.cls.run(async () => {
      // Set organization ID in CLS context
      this.cls.set("ORGANIZATION_ID", organizationId);

      // For REQUEST-scoped services, use ModuleRef resolution
      // ImporterDataGatherer uses ImporterService (REQUEST-scoped)
      this.safeLog("debug", "Resolving ImporterDataGatherer processor with request context");
      const gatherer = await this.resolveRequestScopedService(ImporterDataGatherer, request);

      // Prepare parameters
      const gatherParams = {
        importerId,
        importerEmail,
        importer
      };

      // Use the processor to gather data
      this.safeLog("debug", `Using processor to gather importer data`);
      const templateData = await gatherer.gather(gatherParams);

      this.safeLog("log", `Importer template data prepared`);
      return templateData;
    });
  }

  /**
   * Gather validation template data using ComplianceValidationDataGatherer processor
   * @param shipmentId - The shipment ID to gather data for
   * @param organizationId - The organization ID for proper scoping
   * @param request - Request object with user context
   * @param shipmentData - Optional shipment data to provide context
   * @returns Validation template data ready for use
   */
  async gatherValidationData(
    shipmentId: number,
    organizationId: number,
    _request: any, // eslint-disable-line @typescript-eslint/no-unused-vars
    shipmentData?: any
  ): Promise<ValidationTemplateData> {
    this.safeLog("log", `Gathering validation data for shipment ${shipmentId}, org ${organizationId}`);

    // Use ClsService to run with proper context, just like AggregationExecuter
    return await this.cls.run(async () => {
      // Set organization ID in CLS context
      this.cls.set("ORGANIZATION_ID", organizationId);

      // For DEFAULT-scoped services, inject directly (no ModuleRef needed)
      // ComplianceValidationDataGatherer uses ComplianceValidationService (DEFAULT-scoped)
      this.safeLog("debug", "Using ComplianceValidationDataGatherer processor (DEFAULT-scoped)");
      const gatherer = this.complianceValidationDataGatherer;

      // Prepare parameters
      const gatherParams = {
        shipmentId,
        shipment: shipmentData?.shipment || shipmentData
      };

      // Use the processor to gather data
      this.safeLog("debug", `Using processor to gather validation data for shipment ${shipmentId}`);
      const templateData = await gatherer.gather(gatherParams);

      this.safeLog("log", `Validation template data prepared for shipment ${shipmentId}`);
      return templateData;
    });
  }

  /**
   * Get unified template context containing all template data and formatted variables
   * @param shipmentId - The shipment ID to gather data for
   * @param organizationId - The organization ID for proper scoping
   * @param request - Request object with user context
   * @param options - Optional email context options for email-specific processing
   * @returns Complete unified template context ready for template rendering
   */
  async getUnifiedTemplateContext(
    shipmentId: number,
    organizationId: number,
    request: any,
    options?: EmailContextOptions
  ): Promise<UnifiedTemplateContext> {
    this.safeLog(
      "log",
      `Gathering unified template context for shipment ${shipmentId}, org ${organizationId}`
    );

    // Use ClsService to run with proper context
    return await this.cls.run(async () => {
      // Set organization ID in CLS context
      this.cls.set("ORGANIZATION_ID", organizationId);

      // 1. Gather all raw data from existing methods
      const [shipmentData, documentData, importerData, validationData] = await Promise.all([
        this.gatherShipmentData(shipmentId, organizationId, request),
        this.gatherDocumentData(shipmentId, organizationId, request, options),
        this.gatherImporterData(organizationId, request),
        this.gatherValidationData(shipmentId, organizationId, request)
      ]);

      // Gather RNS data separately as it depends on shipment data
      const rnsData = await this.gatherRnsData(shipmentId, organizationId, request, shipmentData);

      // 2. Apply formatting and business logic (to be implemented)
      const formattedContext = this.buildFormattedContext(
        shipmentData,
        documentData,
        rnsData,
        importerData,
        validationData
      );

      // 3. Generate status messages for template rendering
      const statusMessages = this.generateStatusMessages({
        shipmentData,
        documentData,
        rnsData,
        importerData,
        validationData
      });

      this.safeLog("log", `Unified template context prepared for shipment ${shipmentId}`);

      return {
        // Raw data from individual gatherers
        shipmentData,
        documentData,
        rnsData,
        importerData,
        validationData,

        // Formatted context variables (ensure all required properties are present)
        CCN_PLACEHOLDER: formattedContext.CCN_PLACEHOLDER!,
        CONTAINER_PLACEHOLDER: formattedContext.CONTAINER_PLACEHOLDER!,
        HBL_PLACEHOLDER: formattedContext.HBL_PLACEHOLDER!,
        HBL_STATUS_PLACEHOLDER: formattedContext.HBL_STATUS_PLACEHOLDER!,
        AN_EMF_STATUS_PLACEHOLDER: formattedContext.AN_EMF_STATUS_PLACEHOLDER!,
        CI_PL_STATUS_PLACEHOLDER: formattedContext.CI_PL_STATUS_PLACEHOLDER!,
        CUSTOMS_STATUS_LINE_PLACEHOLDER: formattedContext.CUSTOMS_STATUS_LINE_PLACEHOLDER!,
        MISSING_DOCUMENTS_PLACEHOLDER: formattedContext.MISSING_DOCUMENTS_PLACEHOLDER!,
        MISSING_FIELDS_PLACEHOLDER: formattedContext.MISSING_FIELDS_PLACEHOLDER!,
        COMPLIANCE_ERRORS_PLACEHOLDER: formattedContext.COMPLIANCE_ERRORS_PLACEHOLDER!,

        // Status and compliance data
        COMPLIANCE_STATUS: formattedContext.COMPLIANCE_STATUS!,
        COMPLIANCE_SCORE: formattedContext.COMPLIANCE_SCORE!,
        STATUS_CATEGORY: formattedContext.STATUS_CATEGORY!,
        STATUS_PRIORITY: formattedContext.STATUS_PRIORITY!,
        STATUS_UPDATED: formattedContext.STATUS_UPDATED!,

        // Processed documents
        PROCESSED_DOCUMENTS: formattedContext.PROCESSED_DOCUMENTS,

        // Status messages for template rendering
        ...statusMessages
      };
    });
  }

  /**
   * Build formatted template context from raw data
   * This method contains all the business logic moved from UnifiedTemplateRendererService
   */
  private buildFormattedContext(
    shipmentData: ShipmentTemplateData,
    documentData: DocumentTemplateData,
    rnsData: RnsTemplateData,
    importerData: ImporterTemplateData,
    validationData: ValidationTemplateData
  ): Partial<UnifiedTemplateContext> {
    const shipment = shipmentData.shipment;

    return {
      // Placeholder variables
      CCN_PLACEHOLDER: shipment.cargoControlNumber || "N/A",
      CONTAINER_PLACEHOLDER: this.formatContainers(shipment.containers || []),
      HBL_PLACEHOLDER: shipment.hblNumber || "N/A",
      HBL_STATUS_PLACEHOLDER: documentData.HBL_MISSING ? "Missing" : "Received",
      AN_EMF_STATUS_PLACEHOLDER: documentData.AN_EMF_MISSING ? "Missing" : "Received",
      CI_PL_STATUS_PLACEHOLDER: documentData.CI_PL_MISSING ? "Missing" : "Received",
      CUSTOMS_STATUS_LINE_PLACEHOLDER: this.formatCustomsStatus(shipment.customsStatus),
      MISSING_DOCUMENTS_PLACEHOLDER: this.formatMissingDocuments(documentData),
      MISSING_FIELDS_PLACEHOLDER: this.formatMissingFields(validationData),
      COMPLIANCE_ERRORS_PLACEHOLDER: this.formatComplianceErrors(validationData),

      // Compliance and status data
      COMPLIANCE_STATUS: validationData.COMPLIANCE_ERRORS_COUNT > 0 ? "ISSUES_FOUND" : "COMPLIANT",
      COMPLIANCE_SCORE: this.calculateComplianceScore(validationData),
      STATUS_CATEGORY: this.getStatusCategory(shipment.customsStatus),
      STATUS_PRIORITY: this.getStatusPriority(shipment.customsStatus),
      STATUS_UPDATED: new Date().toISOString(),

      // Processed documents
      PROCESSED_DOCUMENTS: documentData.PROCESSED_DOCUMENTS
    };
  }

  /**
   * Format containers for display - moved from UnifiedTemplateRendererService
   */
  private formatContainers(containers: any[]): string {
    if (!containers?.length) return "N/A";
    return containers.map((c) => c.containerNumber || c.number || "Unknown").join(", ");
  }

  /**
   * Format customs status for display - moved from UnifiedTemplateRendererService
   */
  private formatCustomsStatus(status: string): string {
    if (!status) return "Unknown";

    // Convert technical status to user-friendly display
    const statusMap = {
      "pending-commercial-invoice": "Pending Commercial Invoice",
      "pending-confirmation": "Pending Confirmation",
      "pending-arrival": "Pending Arrival",
      live: "Live/In Progress",
      "entry-submitted": "Entry Submitted",
      "entry-accepted": "Entry Accepted",
      exam: "Selected for Examination",
      released: "Released"
    };

    return statusMap[status] || status.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
  }

  /**
   * Format missing documents for display - moved from UnifiedTemplateRendererService
   */
  private formatMissingDocuments(documentData: DocumentTemplateData): string {
    const missing = [];
    if (documentData.CI_PL_MISSING) missing.push("Commercial Invoice & Packing List");
    if (documentData.HBL_MISSING) missing.push("House Bill of Lading");
    if (documentData.AN_EMF_MISSING) missing.push("Arrival Notice/Empty Move Form");

    return missing.length > 0 ? missing.join(", ") : "None";
  }

  /**
   * Format missing fields for display - moved from UnifiedTemplateRendererService
   */
  private formatMissingFields(validationData: ValidationTemplateData): string {
    const missing = [];
    if (validationData.WEIGHT_MISSING) missing.push("Shipment Weight");
    if (validationData.PORT_CODE_MISSING) missing.push("Port Code");
    if (validationData.CCN_MISSING) missing.push("Cargo Control Number");

    return missing.length > 0 ? missing.join(", ") : "None";
  }

  /**
   * Format compliance errors for display - moved from UnifiedTemplateRendererService
   */
  private formatComplianceErrors(validationData: ValidationTemplateData): string {
    // This will be populated from actual compliance validation data
    if (validationData.COMPLIANCE_ERRORS_COUNT > 0) {
      return `${validationData.COMPLIANCE_ERRORS_COUNT} compliance issue(s) found`;
    }
    return "None";
  }

  /**
   * Calculate compliance score - moved from UnifiedTemplateRendererService
   */
  private calculateComplianceScore(validationData: ValidationTemplateData): number {
    // Simple scoring algorithm - can be enhanced
    let score = 100;
    if (validationData.COMPLIANCE_ERRORS_COUNT > 0) {
      score -= Math.min(validationData.COMPLIANCE_ERRORS_COUNT * 10, 50);
    }
    return Math.max(score, 0);
  }

  /**
   * Get status category - moved from UnifiedTemplateRendererService
   */
  private getStatusCategory(status: string): string {
    const categories = {
      "pending-commercial-invoice": "DOCUMENTATION",
      "pending-confirmation": "REVIEW",
      "pending-arrival": "TRANSIT",
      live: "PROCESSING",
      "entry-submitted": "CUSTOMS",
      "entry-accepted": "CUSTOMS",
      exam: "INSPECTION",
      released: "COMPLETED"
    };
    return categories[status] || "UNKNOWN";
  }

  /**
   * Get status priority - moved from UnifiedTemplateRendererService
   */
  private getStatusPriority(status: string): number {
    const priorities = {
      released: 1,
      exam: 2,
      "entry-accepted": 3,
      "entry-submitted": 4,
      live: 5,
      "pending-confirmation": 6,
      "pending-arrival": 7,
      "pending-commercial-invoice": 8
    };
    return priorities[status] || 9;
  }

  /**
   * Generate status messages for template rendering
   * Moved from UnifiedTemplateRendererService to centralize message generation
   * This method is now deprecated in favor of generateAllIntentMessages
   * @deprecated Use generateAllIntentMessages instead
   */
  private generateStatusMessages(context: any): any {
    const messages: any = {};

    // ETA message with template variable substitution
    if (context.shipmentData?.ETA_PORT) {
      messages.ETA_RESPONSE_MESSAGE = emailResponseMessages.etaResponse.available
        .replace('{{ eta | date(\'F j, Y\') }}', this.formatDate(context.shipmentData.ETA_PORT));
    } else {
      messages.ETA_RESPONSE_MESSAGE = emailResponseMessages.etaResponse.unavailable;
    }

    // Transaction number message with template variable substitution
    if (context.shipmentData?.TRANSACTION_NUMBER) {
      messages.TRANSACTION_NUMBER_RESPONSE_MESSAGE = emailResponseMessages.transactionNumberResponse.available
        .replace('{{ TRANSACTION_NUMBER }}', context.shipmentData.TRANSACTION_NUMBER);
    } else {
      messages.TRANSACTION_NUMBER_RESPONSE_MESSAGE = emailResponseMessages.transactionNumberResponse.unavailable;
    }

    // Release status message with template variable substitution
    const customsStatus = context.shipmentData?.CUSTOMS_STATUS || context.shipmentData?.shipment?.customsStatus;
    if (customsStatus === "released") {
      const releaseDate = context.shipmentData?.releaseDate || context.shipmentData?.shipment?.releaseDate;
      if (releaseDate) {
        messages.RELEASE_STATUS_RESPONSE_MESSAGE = emailResponseMessages.releaseStatusResponse.released
          .replace('{{ shipment.releaseDate | date(\'F j, Y\') }}', this.formatDate(releaseDate));
      } else {
        messages.RELEASE_STATUS_RESPONSE_MESSAGE = 'Your shipment has been released by CBSA.';
      }
    } else {
      messages.RELEASE_STATUS_RESPONSE_MESSAGE = emailResponseMessages.releaseStatusResponse.not_released;
    }

    // Shipping status message
    if (context.shipmentData?.SHIPPING_STATUS) {
      messages.SHIPPING_STATUS_RESPONSE_MESSAGE = emailResponseMessages.shippingStatusResponse.default
        .replace('{{ SHIPPING_STATUS }}', context.shipmentData.SHIPPING_STATUS);
    } else {
      messages.SHIPPING_STATUS_RESPONSE_MESSAGE = 'Shipping status information is not currently available.';
    }

    // General status message based on customs status
    messages.GET_SHIPMENT_STATUS_MESSAGE = this.getCustomsStatusMessage(customsStatus, context.shipmentData);

    return messages;
  }

  /**
   * Master method to generate intent-specific messages
   * Routes to appropriate intent-specific method based on intent type
   */
  generateAllIntentMessages(context: any, intentType: string, options: any = {}): any {
    this.safeLog("debug", `Generating messages for intent type: ${intentType}`);

    switch (intentType) {
      case 'PROCESS_DOCUMENT':
        return this.generateDocumentProcessingMessages(context, options.hasProcessedDocuments);

      case 'GET_SHIPMENT_STATUS':
        return this.generateGetShipmentStatusMessages(context, options);

      case 'REQUEST_CAD_DOCUMENT':
        return this.generateRequestCADDocumentMessages(context, options);

      case 'REQUEST_RUSH_PROCESSING':
        return this.generateRequestRushProcessingMessages(context, options);

      case 'DOCUMENTATION_COMING':
        return this.generateDocumentationComingMessages(context, options);

      case 'REQUEST_RNS_PROOF':
        return this.generateRequestRNSProofMessages(context, options);

      case 'REQUEST_MANUAL_PROCESSING':
        return this.generateRequestManualProcessingMessages(context, options);

      case 'REQUEST_HOLD_SHIPMENT':
        return this.generateRequestHoldShipmentMessages(context, options);

      case 'UPDATE_SHIPMENT':
        return this.generateUpdateShipmentMessages(context, options);

      case 'ACKNOWLEDGE_DOCUMENTS':
        return this.generateAcknowledgeDocumentsMessages(context, options);

      case 'ACKNOWLEDGE_MISSING_DOCUMENTS':
        return this.generateAcknowledgeMissingDocumentsMessages(context, options);

      default:
        this.safeLog("warn", `No specific message generation defined for intent type: ${intentType}`);
        return this.generateGenericMessages(context, options);
    }
  }

  /**
   * Generate document processing messages for ProcessDocumentHandler
   * This determines which acknowledge messages should be shown based on document status
   */
  generateDocumentProcessingMessages(context: any, hasProcessedDocuments: boolean = false): any {
    const messages: any = {};
    const shipmentData = context.shipmentData;
    const documentData = context.documentData;

    // Show process document acknowledgment if documents were processed
    if (hasProcessedDocuments) {
      messages.SHOW_PROCESS_DOCUMENT_MESSAGE = true;
      messages.PROCESS_DOCUMENT_MESSAGE = emailResponseMessages.processDocument.success;
    }

    // Determine if we should show acknowledge documents or acknowledge missing documents
    const hasMissingDocuments = documentData?.CI_PL_MISSING || documentData?.HBL_MISSING || documentData?.AN_EMF_MISSING;
    
    if (hasMissingDocuments) {
      // Show acknowledge missing documents message
      messages.ACKNOWLEDGE_MISSING_DOCUMENTS_MESSAGE = emailResponseMessages.acknowledgeMissingDocuments.default;
      messages.ACKNOWLEDGE_DOCUMENTS_MESSAGE = ""; // Don't show the regular acknowledgment
    } else {
      // Show acknowledge documents message based on shipment type
      const shipmentType = this.getShipmentType(shipmentData);
      messages.ACKNOWLEDGE_DOCUMENTS_MESSAGE = emailResponseMessages.acknowledgeDocuments[shipmentType] || 
                                                emailResponseMessages.acknowledgeDocuments.default;
      messages.ACKNOWLEDGE_MISSING_DOCUMENTS_MESSAGE = ""; // Don't show missing docs message
    }

    // Show documentation coming message if needed
    messages.DOCUMENTATION_COMING_MESSAGE = emailResponseMessages.documentationComing.default;

    // Set template section flags
    messages.SHOW_DETAILS = true;
    messages.SHOW_VALIDATION_ISSUES = this.shouldShowValidationIssues(context);
    messages.SHOW_PROCESS_DOCUMENT_MESSAGE = hasProcessedDocuments;

    this.safeLog("debug", `Generated PROCESS_DOCUMENT messages: hasProcessedDocuments=${hasProcessedDocuments}, hasMissingDocuments=${hasMissingDocuments}`);
    return messages;
  }

  /**
   * Generate messages for GET_SHIPMENT_STATUS intent
   */
  generateGetShipmentStatusMessages(context: any, _options: any = {}): any {
    const messages: any = {};
    const shipmentData = context.shipmentData;
    const customsStatus = shipmentData?.CUSTOMS_STATUS || shipmentData?.shipment?.customsStatus;

    // ETA message
    if (shipmentData?.ETA_PORT) {
      messages.ETA_RESPONSE_MESSAGE = emailResponseMessages.etaResponse.available
        .replace('{{ eta | date(\'F j, Y\') }}', this.formatDate(shipmentData.ETA_PORT));
    } else {
      messages.ETA_RESPONSE_MESSAGE = emailResponseMessages.etaResponse.unavailable;
    }

    // Transaction number message  
    if (shipmentData?.TRANSACTION_NUMBER) {
      messages.TRANSACTION_NUMBER_RESPONSE_MESSAGE = emailResponseMessages.transactionNumberResponse.available
        .replace('{{ TRANSACTION_NUMBER }}', shipmentData.TRANSACTION_NUMBER);
    } else {
      messages.TRANSACTION_NUMBER_RESPONSE_MESSAGE = emailResponseMessages.transactionNumberResponse.unavailable;
    }

    // Release status message
    if (customsStatus === "released") {
      const releaseDate = shipmentData?.releaseDate || shipmentData?.shipment?.releaseDate;
      if (releaseDate) {
        messages.RELEASE_STATUS_RESPONSE_MESSAGE = emailResponseMessages.releaseStatusResponse.released
          .replace('{{ shipment.releaseDate | date(\'F j, Y\') }}', this.formatDate(releaseDate));
      } else {
        messages.RELEASE_STATUS_RESPONSE_MESSAGE = 'Your shipment has been released by CBSA.';
      }
    } else {
      messages.RELEASE_STATUS_RESPONSE_MESSAGE = emailResponseMessages.releaseStatusResponse.not_released;
    }

    // Shipping status message
    if (shipmentData?.SHIPPING_STATUS) {
      messages.SHIPPING_STATUS_RESPONSE_MESSAGE = emailResponseMessages.shippingStatusResponse.default
        .replace('{{ SHIPPING_STATUS }}', shipmentData.SHIPPING_STATUS);
    } else {
      messages.SHIPPING_STATUS_RESPONSE_MESSAGE = 'Shipping status information is not currently available.';
    }

    // Main status message based on customs status
    messages.GET_SHIPMENT_STATUS_MESSAGE = this.getCustomsStatusMessage(customsStatus, shipmentData);

    // Set template section flags
    messages.SHOW_DETAILS = true;
    messages.SHOW_VALIDATION_ISSUES = this.shouldShowValidationIssues(context);

    this.safeLog("debug", `Generated GET_SHIPMENT_STATUS messages for status: ${customsStatus}`);
    return messages;
  }

  /**
   * Generate messages for REQUEST_CAD_DOCUMENT intent
   */
  generateRequestCADDocumentMessages(context: any, options: any = {}): any {
    const messages: any = {};
    const shipmentData = context.shipmentData;
    const customsStatus = shipmentData?.CUSTOMS_STATUS || shipmentData?.shipment?.customsStatus;

    // Get appropriate message based on customs status
    messages.REQUEST_CAD_DOCUMENT_MESSAGE = emailResponseMessages.requestCADDocument[customsStatus] || 
                                           'CAD document request received and is being processed.';

    // Add CAD document attachment flag if provided
    if (options.cadDocument) {
      messages.CAD_DOCUMENT = options.cadDocument;
      messages.HAS_CAD_ATTACHMENT = true;
    }

    // Set template section flags
    messages.SHOW_DETAILS = true;
    messages.SHOW_VALIDATION_ISSUES = this.shouldShowValidationIssues(context);

    this.safeLog("debug", `Generated REQUEST_CAD_DOCUMENT messages for status: ${customsStatus}`);
    return messages;
  }

  /**
   * Generate messages for REQUEST_RUSH_PROCESSING intent
   */
  generateRequestRushProcessingMessages(context: any, options: any = {}): any {
    const messages: any = {};
    const shipmentData = context.shipmentData;
    const customsStatus = shipmentData?.CUSTOMS_STATUS || shipmentData?.shipment?.customsStatus;

    // Get appropriate message based on customs status
    let message = emailResponseMessages.requestRushProcessing[customsStatus] || 
                  "We've received your rush request and our team will prioritize your shipment.";

    // Handle release date substitution for released status
    if (customsStatus === 'released') {
      const releaseDate = shipmentData?.releaseDate || shipmentData?.shipment?.releaseDate;
      if (releaseDate) {
        message = message.replace('{{ shipment.releaseDate | date(\'d-m-Y\') }}', this.formatDate(releaseDate, 'd-m-Y'));
      } else {
        message = 'The subject shipment has been released by CBSA.';
      }
    }

    messages.REQUEST_RUSH_PROCESSING_MESSAGE = message;

    // Add submission result if provided
    if (options.submissionResult) {
      messages.SUBMISSION_RESULT = options.submissionResult;
      messages.HAS_SUBMISSION_RESULT = true;
    }

    // Add backoffice alert status if provided
    if (options.backofficeAlerts?.rushProcessingSent) {
      messages.RUSH_PROCESSING_ALERT_SENT = true;
    }

    // Set template section flags
    messages.SHOW_DETAILS = true;
    messages.SHOW_VALIDATION_ISSUES = this.shouldShowValidationIssues(context);

    this.safeLog("debug", `Generated REQUEST_RUSH_PROCESSING messages for status: ${customsStatus}`);
    return messages;
  }

  /**
   * Generate messages for DOCUMENTATION_COMING intent
   */
  generateDocumentationComingMessages(context: any, _options: any = {}): any {
    const messages: any = {};

    messages.DOCUMENTATION_COMING_MESSAGE = emailResponseMessages.documentationComing.default;

    // Set template section flags
    messages.SHOW_DETAILS = true;
    messages.SHOW_VALIDATION_ISSUES = this.shouldShowValidationIssues(context);

    this.safeLog("debug", 'Generated DOCUMENTATION_COMING messages');
    return messages;
  }

  /**
   * Generate messages for REQUEST_RNS_PROOF intent
   */
  generateRequestRNSProofMessages(context: any, options: any = {}): any {
    const messages: any = {};
    const shipmentData = context.shipmentData;
    const customsStatus = shipmentData?.CUSTOMS_STATUS || shipmentData?.shipment?.customsStatus;

    // Get appropriate message based on customs status
    messages.REQUEST_RNS_PROOF_MESSAGE = emailResponseMessages.requestRNSProof[customsStatus] || 
                                        'RNS proof request received and is being processed.';

    // Add RNS proof data if provided
    if (options.rnsProofData) {
      messages.RNS_PROOF_DATA = options.rnsProofData;
      messages.SHOW_RNS_PROOF = true;
      
      // Map RNS data to template variables
      if (options.rnsProofData.content) {
        messages.RNS_RESPONSE_MESSAGE = options.rnsProofData.content;
      }
      if (options.rnsProofData.releaseDate) {
        messages.RESPONSE_DATE = options.rnsProofData.releaseDate;
      }
    }

    // Set template section flags
    messages.SHOW_DETAILS = true;
    messages.SHOW_VALIDATION_ISSUES = this.shouldShowValidationIssues(context);

    this.safeLog("debug", `Generated REQUEST_RNS_PROOF messages for status: ${customsStatus}`);
    return messages;
  }

  /**
   * Generate messages for REQUEST_MANUAL_PROCESSING intent
   */
  generateRequestManualProcessingMessages(context: any, options: any = {}): any {
    const messages: any = {};

    messages.REQUEST_MANUAL_PROCESSING_MESSAGE = emailResponseMessages.requestManualProcessing.default;

    // Add backoffice alert status if provided
    if (options.backofficeAlerts?.manualProcessingSent) {
      messages.MANUAL_PROCESSING_ALERT_SENT = true;
    }

    // Set template section flags
    messages.SHOW_DETAILS = true;
    messages.SHOW_VALIDATION_ISSUES = this.shouldShowValidationIssues(context);

    this.safeLog("debug", 'Generated REQUEST_MANUAL_PROCESSING messages');
    return messages;
  }

  /**
   * Generate messages for REQUEST_HOLD_SHIPMENT intent
   */
  generateRequestHoldShipmentMessages(context: any, options: any = {}): any {
    const messages: any = {};

    messages.REQUEST_HOLD_SHIPMENT_MESSAGE = emailResponseMessages.requestHoldShipment.default;

    // Add backoffice alert status if provided
    if (options.backofficeAlerts?.holdShipmentSent) {
      messages.HOLD_SHIPMENT_ALERT_SENT = true;
    }

    // Set template section flags
    messages.SHOW_DETAILS = true;
    messages.SHOW_VALIDATION_ISSUES = this.shouldShowValidationIssues(context);

    this.safeLog("debug", 'Generated REQUEST_HOLD_SHIPMENT messages');
    return messages;
  }

  /**
   * Generate messages for UPDATE_SHIPMENT intent
   */
  generateUpdateShipmentMessages(context: any, options: any = {}): any {
    const messages: any = {};

    if (options.updateResult?.success) {
      messages.UPDATE_SHIPMENT_MESSAGE = emailResponseMessages.updateShipment.success;
    } else {
      messages.UPDATE_SHIPMENT_MESSAGE = emailResponseMessages.updateShipment.error;
    }

    // Add update result if provided
    if (options.updateResult) {
      messages.UPDATE_RESULT = options.updateResult;
      messages.HAS_UPDATE_RESULT = true;
    }

    // Set template section flags
    messages.SHOW_DETAILS = true;
    messages.SHOW_VALIDATION_ISSUES = this.shouldShowValidationIssues(context);

    this.safeLog("debug", `Generated UPDATE_SHIPMENT messages: success=${options.updateResult?.success}`);
    return messages;
  }

  /**
   * Generate messages for ACKNOWLEDGE_DOCUMENTS intent
   */
  generateAcknowledgeDocumentsMessages(context: any, _options: any = {}): any {
    const messages: any = {};
    const shipmentData = context.shipmentData;
    const shipmentType = this.getShipmentType(shipmentData);

    // Get appropriate message based on shipment type
    messages.ACKNOWLEDGE_DOCUMENTS_MESSAGE = emailResponseMessages.acknowledgeDocuments[shipmentType] || 
                                           emailResponseMessages.acknowledgeDocuments.default;

    // Set template section flags
    messages.SHOW_DETAILS = true;
    messages.SHOW_VALIDATION_ISSUES = true; // Always show for document acknowledgments

    this.safeLog("debug", `Generated ACKNOWLEDGE_DOCUMENTS messages for shipment type: ${shipmentType}`);
    return messages;
  }

  /**
   * Generate messages for ACKNOWLEDGE_MISSING_DOCUMENTS intent
   */
  generateAcknowledgeMissingDocumentsMessages(context: any, _options: any = {}): any {
    const messages: any = {};

    messages.ACKNOWLEDGE_MISSING_DOCUMENTS_MESSAGE = emailResponseMessages.acknowledgeMissingDocuments.default;

    // Set template section flags
    messages.SHOW_DETAILS = true;
    messages.SHOW_VALIDATION_ISSUES = true; // Always show for missing document acknowledgments

    this.safeLog("debug", 'Generated ACKNOWLEDGE_MISSING_DOCUMENTS messages');
    return messages;
  }

  /**
   * Generate generic/fallback messages for unknown intent types
   */
  generateGenericMessages(context: any, _options: any = {}): any {
    const messages: any = {};

    // Set default empty messages for all placeholders to prevent template errors
    const messageKeys = [
      'ACKNOWLEDGE_DOCUMENTS_MESSAGE',
      'ACKNOWLEDGE_MISSING_DOCUMENTS_MESSAGE',
      'DOCUMENTATION_COMING_MESSAGE',
      'REQUEST_CAD_DOCUMENT_MESSAGE',
      'REQUEST_RNS_PROOF_MESSAGE',
      'REQUEST_RUSH_PROCESSING_MESSAGE',
      'REQUEST_MANUAL_PROCESSING_MESSAGE',
      'REQUEST_HOLD_SHIPMENT_MESSAGE',
      'UPDATE_SHIPMENT_MESSAGE',
      'ETA_RESPONSE_MESSAGE',
      'TRANSACTION_NUMBER_RESPONSE_MESSAGE',
      'RELEASE_STATUS_RESPONSE_MESSAGE',
      'SHIPPING_STATUS_RESPONSE_MESSAGE',
      'GET_SHIPMENT_STATUS_MESSAGE',
      'SYSTEM_ERROR_MESSAGE',
      'CONTACT_SUPPORT_MESSAGE'
    ];

    messageKeys.forEach(key => {
      messages[key] = '';
    });

    // Set basic template section flags
    messages.SHOW_DETAILS = true;
    messages.SHOW_VALIDATION_ISSUES = this.shouldShowValidationIssues(context);

    this.safeLog("debug", 'Generated generic messages with empty defaults');
    return messages;
  }

  /**
   * Determine shipment type for message selection
   */
  private getShipmentType(shipmentData: any): string {
    // This logic should be based on your actual shipment type determination
    // For now, using a simple approach based on available data
    if (shipmentData?.TRANSPORTATION_MODE) {
      const mode = shipmentData.TRANSPORTATION_MODE.toLowerCase();
      if (mode.includes('truck')) return 'truck';
      if (mode.includes('ocean') || mode.includes('air')) return 'ocean_air';
    }
    return 'default';
  }

  /**
   * Format date for display in templates
   * Supports different formats for different use cases
   */
  private formatDate(dateInput: string | Date, format: string = 'F j, Y'): string {
    try {
      const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
      
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }

      const months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ];

      const monthsShort = [
        'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
      ];

      const day = date.getDate();
      const month = date.getMonth();
      const year = date.getFullYear();

      switch (format) {
        case 'F j, Y': // January 1, 2024
          return `${months[month]} ${day}, ${year}`;
        case 'd-m-Y': // 01-01-2024
          return `${day.toString().padStart(2, '0')}-${(month + 1).toString().padStart(2, '0')}-${year}`;
        case 'M j, Y': // Jan 1, 2024
          return `${monthsShort[month]} ${day}, ${year}`;
        case 'Y-m-d': // 2024-01-01
          return `${year}-${(month + 1).toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
        default:
          return date.toLocaleDateString();
      }
    } catch (error) {
      this.safeLog("error", `Date formatting error: ${error.message}`);
      return 'Date unavailable';
    }
  }

  /**
   * Get customs status message with template variable substitution
   * Enhanced version that handles template variables in status messages
   */
  private getCustomsStatusMessage(customsStatus: string, shipmentData?: any): string {
    let message = emailResponseMessages.getCustomsStatus[customsStatus] || 
                  `Current status: ${customsStatus}`;

    // Handle template variable substitutions
    if (message.includes('{{ eta') && shipmentData?.ETA_PORT) {
      message = message.replace('{{ eta | date(\'F j, Y\') }}', this.formatDate(shipmentData.ETA_PORT));
    }

    return message;
  }

  /**
   * Determine if validation issues should be shown
   * This logic determines when to display validation issues in the template
   */
  private shouldShowValidationIssues(context: any): boolean {
    const shipmentData = context.shipmentData;
    const documentData = context.documentData;
    const customsStatus = shipmentData?.CUSTOMS_STATUS || shipmentData?.shipment?.customsStatus;

    // Show validation issues for statuses that require client attention
    const statusesRequiringClientAction = ['pending-commercial-invoice', 'pending-confirmation'];
    
    // Check if there are actual missing documents or fields
    const hasMissingDocuments = documentData?.CI_PL_MISSING || documentData?.HBL_MISSING || documentData?.AN_EMF_MISSING;
    const hasMissingFields = context.validationData?.WEIGHT_MISSING || context.validationData?.PORT_CODE_MISSING || context.validationData?.CCN_MISSING;
    const hasComplianceErrors = context.validationData?.COMPLIANCE_ERRORS_COUNT > 0;

    return statusesRequiringClientAction.includes(customsStatus) && 
           (hasMissingDocuments || hasMissingFields || hasComplianceErrors);
  }

  /**
   * Resolve request-scoped service using ModuleRef pattern
   * This is the key pattern for using request-scoped services in BullMQ processors
   */
  private async resolveRequestScopedService<T>(serviceClass: Type<T>, request: any): Promise<T> {
    this.safeLog("debug", `Resolving request-scoped service: ${serviceClass.name}`);

    // Create context ID for this specific request
    const contextId = ContextIdFactory.create();

    // Register the request with the context
    this.moduleRef.registerRequestByContextId(request, contextId);

    // Resolve the service with the context
    const service = await this.moduleRef.resolve(serviceClass, contextId);

    // Important: Wait for next tick to ensure proper resolution
    // This is a known pattern from NestJS documentation
    await new Promise((resolve) => process.nextTick(resolve));

    this.safeLog("debug", `Successfully resolved ${serviceClass.name}`);
    return service;
  }
}
