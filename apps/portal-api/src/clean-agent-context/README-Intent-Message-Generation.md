# Intent-Specific Message Generation Enhancement

## Overview

The CleanAgentContextService has been extended to support context generation for ALL intent types, providing centralized, intent-specific message generation for email templates. This replaces the scattered intent processing logic that was previously in the UnifiedTemplateRendererService.

## Architecture Changes

### Before
- Intent-specific logic scattered across UnifiedTemplateRendererService
- Hardcoded messages within processing methods
- Duplicate logic for message generation
- Template variable substitution handled inconsistently

### After
- Centralized intent message generation in CleanAgentContextService
- All messages sourced from `emailResponseMessages` constants
- Consistent template variable substitution
- Clean separation of concerns between services

## New Methods Added

### Master Method
- `generateAllIntentMessages(context, intentType, options)` - Routes to appropriate intent-specific method

### Intent-Specific Methods
- `generateProcessDocumentMessages()` - PROCESS_DOCUMENT intent
- `generateGetShipmentStatusMessages()` - GET_SHIPMENT_STATUS intent
- `generateRequestCADDocumentMessages()` - REQUEST_CAD_DOCUMENT intent
- `generateRequestRushProcessingMessages()` - REQUEST_RUSH_PROCESSING intent
- `generateDocumentationComingMessages()` - DOCUMENTATION_COMING intent
- `generateRequestRNSProofMessages()` - REQUEST_RNS_PROOF intent
- `generateRequestManualProcessingMessages()` - REQUEST_MANUAL_PROCESSING intent
- `generateRequestHoldShipmentMessages()` - REQUEST_HOLD_SHIPMENT intent
- `generateUpdateShipmentMessages()` - UPDATE_SHIPMENT intent
- `generateAcknowledgeDocumentsMessages()` - ACKNOWLEDGE_DOCUMENTS intent
- `generateAcknowledgeMissingDocumentsMessages()` - ACKNOWLEDGE_MISSING_DOCUMENTS intent
- `generateGenericMessages()` - Fallback for unknown intent types

### Helper Methods
- `formatDate()` - Consistent date formatting for templates
- `getCustomsStatusMessage()` - Enhanced customs status messages with template variables
- `shouldShowValidationIssues()` - Improved validation issue display logic

## Intent Processing Logic

Each intent-specific method:

1. **Analyzes context data** (shipmentData, documentData, validationData)
2. **Applies business logic** to determine appropriate messages based on:
   - Customs status
   - Missing documents/fields
   - Shipment type
   - Data availability
3. **Uses centralized emailResponseMessages** for consistent messaging
4. **Handles template variable substitution** (dates, transaction numbers, etc.)
5. **Returns message object** with appropriate message keys and template flags
6. **Includes additional context data** (attachments, flags, results)

## Example Usage

```typescript
const messages = cleanAgentContextService.generateAllIntentMessages(
  context,
  'REQUEST_CAD_DOCUMENT',
  {
    cadDocument: { filename: 'cad.pdf' },
    backofficeAlerts: { rushProcessingSent: true }
  }
);

// Returns:
// {
//   REQUEST_CAD_DOCUMENT_MESSAGE: "We can't provide the CAD yet as we're missing...",
//   CAD_DOCUMENT: { filename: 'cad.pdf' },
//   HAS_CAD_ATTACHMENT: true,
//   SHOW_DETAILS: true,
//   SHOW_VALIDATION_ISSUES: true
// }
```

## Message Categories

### Document Acknowledgment Messages
- `ACKNOWLEDGE_DOCUMENTS_MESSAGE` - Based on shipment type
- `ACKNOWLEDGE_MISSING_DOCUMENTS_MESSAGE` - Missing document requests
- `DOCUMENTATION_COMING_MESSAGE` - Document update acknowledgments
- `PROCESS_DOCUMENT_MESSAGE` - Document processing confirmations

### Status & Information Messages
- `GET_SHIPMENT_STATUS_MESSAGE` - Based on customs status
- `ETA_RESPONSE_MESSAGE` - ETA availability messages
- `TRANSACTION_NUMBER_RESPONSE_MESSAGE` - Transaction number messages
- `RELEASE_STATUS_RESPONSE_MESSAGE` - Customs release status
- `SHIPPING_STATUS_RESPONSE_MESSAGE` - Shipping status updates

### Processing Request Messages
- `REQUEST_CAD_DOCUMENT_MESSAGE` - CAD document requests
- `REQUEST_RNS_PROOF_MESSAGE` - RNS proof requests
- `REQUEST_RUSH_PROCESSING_MESSAGE` - Rush processing requests
- `REQUEST_MANUAL_PROCESSING_MESSAGE` - Manual processing requests
- `REQUEST_HOLD_SHIPMENT_MESSAGE` - Hold/cancel requests

### Update Messages
- `UPDATE_SHIPMENT_MESSAGE` - Shipment update confirmations

### Template Section Flags
- `SHOW_DETAILS` - Show shipment details section
- `SHOW_VALIDATION_ISSUES` - Show validation/compliance issues
- `SHOW_RNS_PROOF` - Show RNS proof information
- `SHOW_PROCESS_DOCUMENT_MESSAGE` - Show document processing acknowledgment

## Business Logic Examples

### GET_SHIPMENT_STATUS Intent
```typescript
// ETA handling
if (shipmentData?.ETA_PORT) {
  messages.ETA_RESPONSE_MESSAGE = emailResponseMessages.etaResponse.available
    .replace('{{ eta | date(\'F j, Y\') }}', this.formatDate(shipmentData.ETA_PORT));
} else {
  messages.ETA_RESPONSE_MESSAGE = emailResponseMessages.etaResponse.unavailable;
}

// Transaction number handling
if (shipmentData?.TRANSACTION_NUMBER) {
  messages.TRANSACTION_NUMBER_RESPONSE_MESSAGE = emailResponseMessages.transactionNumberResponse.available
    .replace('{{ TRANSACTION_NUMBER }}', shipmentData.TRANSACTION_NUMBER);
}
```

### REQUEST_CAD_DOCUMENT Intent
```typescript
// Status-based message selection
messages.REQUEST_CAD_DOCUMENT_MESSAGE = emailResponseMessages.requestCADDocument[customsStatus] || 
                                       'CAD document request received and is being processed.';

// Attachment handling
if (options.cadDocument) {
  messages.CAD_DOCUMENT = options.cadDocument;
  messages.HAS_CAD_ATTACHMENT = true;
}
```

### PROCESS_DOCUMENT Intent
```typescript
// Document processing acknowledgment
if (hasProcessedDocuments) {
  messages.SHOW_PROCESS_DOCUMENT_MESSAGE = true;
  messages.PROCESS_DOCUMENT_MESSAGE = emailResponseMessages.processDocument.success;
}

// Missing vs received documents logic
const hasMissingDocuments = documentData?.CI_PL_MISSING || documentData?.HBL_MISSING || documentData?.AN_EMF_MISSING;
if (hasMissingDocuments) {
  messages.ACKNOWLEDGE_MISSING_DOCUMENTS_MESSAGE = emailResponseMessages.acknowledgeMissingDocuments.default;
} else {
  const shipmentType = this.getShipmentType(shipmentData);
  messages.ACKNOWLEDGE_DOCUMENTS_MESSAGE = emailResponseMessages.acknowledgeDocuments[shipmentType];
}
```

## Template Variable Substitution

The service handles template variable substitution for:

- **Dates**: `{{ eta | date('F j, Y') }}` → "August 15, 2024"
- **Transaction Numbers**: `{{ TRANSACTION_NUMBER }}` → "TX123456"
- **Shipping Status**: `{{ SHIPPING_STATUS }}` → "In Transit"
- **Release Dates**: `{{ shipment.releaseDate | date('F j, Y') }}` → "August 10, 2024"

## Testing

Run the comprehensive test suite:
```bash
node src/clean-agent-context/testing/test-intent-message-generation.js
```

This test verifies:
- All intent types generate appropriate messages
- Template variables are properly substituted
- Business logic produces expected results
- Fallback handling for unknown intents works
- Template section flags are set correctly

## Integration with UnifiedTemplateRendererService

The UnifiedTemplateRendererService has been updated to:
1. Delegate all intent message generation to CleanAgentContextService
2. Focus solely on template rendering
3. Handle additional processing that doesn't belong in message generation
4. Maintain backward compatibility for template section flags

## Benefits

1. **Centralized Logic**: All intent processing in one service
2. **Consistent Messaging**: Uses centralized `emailResponseMessages`
3. **Better Testability**: Isolated message generation logic
4. **Maintainability**: Single place to update intent behavior
5. **Extensibility**: Easy to add new intent types
6. **Template Variable Consistency**: Unified date/variable formatting
7. **Business Logic Clarity**: Clear separation of message generation from rendering

## Future Enhancements

1. **Message Localization**: Support for multiple languages
2. **A/B Testing**: Different message variants based on configuration
3. **Context-Aware Messaging**: More sophisticated business rules
4. **Message Analytics**: Track message effectiveness
5. **Dynamic Message Loading**: Load messages from database/configuration