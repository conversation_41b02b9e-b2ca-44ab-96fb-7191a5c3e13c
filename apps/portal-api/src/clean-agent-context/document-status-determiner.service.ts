import { Injectable, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Document, DocumentStatus, Shipment } from "nest-modules";

/**
 * Clean implementation of document status determination
 *
 * This service determines document receipt status based on actual document entities
 * and their status, without relying on the broken adapter logic.
 *
 * Key Business Rules (based on aggregation module):
 * - HBL: House Bill of Lading (mode-specific: HOUSE_OCEAN_BILL_OF_LADING, AIR_WAYBILL, ROAD_BILL_OF_LADING)
 * - AN/EMF: Arrival Notice OR E-Manifest (interchangeable - either satisfies requirement)
 * - CI: Commercial Invoice (required)
 * - PL: Packing List (optional but tracked)
 *
 * Document Interchangeability:
 * - Ocean: OCEAN_ARRIVAL_NOTICE OR OCEAN_E_MANIFEST (for AN/EMF requirement)
 * - Air: AIR_ARRIVAL_NOTICE OR AIR_E_MANIFEST (for AN/EMF requirement)
 * - Land: ROAD_ARRIVAL_NOTICE (serves BOTH HBL and AN/EMF requirement)
 * - Land: PARS_OVERLAY (can also serve as HBL)
 */
@Injectable()
export class DocumentStatusDeterminer {
  private readonly logger = new Logger(DocumentStatusDeterminer.name);

  constructor(
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>
  ) {}

  /**
   * Determine document status for a shipment based on actual document records
   */
  async determineDocumentStatus(shipment: Shipment): Promise<{
    hblReceived: boolean;
    anEmfReceived: boolean;
    ciReceived: boolean;
    plReceived: boolean;
  }> {
    if (!shipment.id) {
      return this.getEmptyDocumentStatus();
    }

    // Get all documents for the shipment
    const documents = await this.documentRepository.find({
      where: { shipmentId: shipment.id },
      relations: ["documentType"]
    });

    const documentsByType = this.groupDocumentsByType(documents);
    const modeOfTransport = shipment.modeOfTransport;

    return {
      hblReceived: this.isHBLReceived(documentsByType, modeOfTransport),
      anEmfReceived: this.isANEMFReceived(documentsByType, modeOfTransport),
      ciReceived: this.isCIReceived(documentsByType),
      plReceived: this.isPLReceived(documentsByType)
    };
  }

  /**
   * Group documents by their type name for easier lookup
   */
  private groupDocumentsByType(documents: Document[]): Map<string, Document[]> {
    const grouped = new Map<string, Document[]>();

    documents.forEach((doc) => {
      const typeName = doc.documentType?.name || doc.name;
      if (typeName) {
        if (!grouped.has(typeName)) {
          grouped.set(typeName, []);
        }
        grouped.get(typeName)!.push(doc);
      }
    });

    return grouped;
  }

  /**
   * Check if House Bill of Lading is received based on transport mode
   * Note: For LAND transport, ROAD_ARRIVAL_NOTICE serves as both HBL and AN/EMF
   */
  private isHBLReceived(documentsByType: Map<string, Document[]>, modeOfTransport: string): boolean {
    const hblTypes = this.getHBLDocumentTypes(modeOfTransport);

    return hblTypes.some((type) => {
      const docs = documentsByType.get(type) || [];
      return docs.some((doc) => this.isDocumentReceived(doc));
    });
  }

  /**
   * Check if Arrival Notice/E-Manifest is received based on transport mode
   * Note: AN and EMF are interchangeable - either one satisfies the requirement
   */
  private isANEMFReceived(documentsByType: Map<string, Document[]>, modeOfTransport: string): boolean {
    const anEmfTypes = this.getANEMFDocumentTypes(modeOfTransport);

    // Either AN OR EMF satisfies the requirement (they're interchangeable)
    return anEmfTypes.some((type) => {
      const docs = documentsByType.get(type) || [];
      return docs.some((doc) => this.isDocumentReceived(doc));
    });
  }

  /**
   * Check if Commercial Invoice is received
   */
  private isCIReceived(documentsByType: Map<string, Document[]>): boolean {
    const ciDocs = documentsByType.get("COMMERCIAL_INVOICE") || [];
    return ciDocs.some((doc) => this.isDocumentReceived(doc));
  }

  /**
   * Check if Packing List is received
   */
  private isPLReceived(documentsByType: Map<string, Document[]>): boolean {
    const plDocs = documentsByType.get("PACKING_LIST") || [];
    return plDocs.some((doc) => this.isDocumentReceived(doc));
  }

  /**
   * Check if a document is considered "received" (successfully processed)
   */
  private isDocumentReceived(document: Document): boolean {
    // Document is received if:
    // 1. Status is EXTRACTED or AGGREGATED (successfully processed)
    // 2. No shipment mismatch
    const validStatuses = [DocumentStatus.EXTRACTED, DocumentStatus.AGGREGATED];
    return validStatuses.includes(document.status as DocumentStatus) && !document.isShipmentMismatch;
  }

  /**
   * Get HBL document types based on transport mode
   * Note: For LAND, ROAD_ARRIVAL_NOTICE and PARS_OVERLAY can serve as HBL
   */
  private getHBLDocumentTypes(modeOfTransport: string): string[] {
    switch (modeOfTransport?.toUpperCase()) {
      case "OCEAN_FCL":
      case "OCEAN_LCL":
        return ["HOUSE_OCEAN_BILL_OF_LADING"];
      case "AIR":
        return ["AIR_WAYBILL"];
      case "LAND":
        // For LAND transport, multiple document types can serve as HBL
        return ["ROAD_BILL_OF_LADING", "ROAD_ARRIVAL_NOTICE", "PARS_OVERLAY"];
      default:
        return [
          "HOUSE_OCEAN_BILL_OF_LADING",
          "AIR_WAYBILL",
          "ROAD_BILL_OF_LADING",
          "ROAD_ARRIVAL_NOTICE",
          "PARS_OVERLAY"
        ];
    }
  }

  /**
   * Get AN/EMF document types based on transport mode
   * Note: For LAND, ROAD_ARRIVAL_NOTICE serves as both HBL and AN/EMF
   */
  private getANEMFDocumentTypes(modeOfTransport: string): string[] {
    switch (modeOfTransport?.toUpperCase()) {
      case "OCEAN_FCL":
      case "OCEAN_LCL":
        return ["OCEAN_ARRIVAL_NOTICE", "OCEAN_E_MANIFEST"];
      case "AIR":
        return ["AIR_ARRIVAL_NOTICE", "AIR_E_MANIFEST"];
      case "LAND":
        // For LAND, ROAD_ARRIVAL_NOTICE serves dual purpose (HBL + AN/EMF)
        return ["ROAD_ARRIVAL_NOTICE"];
      default:
        return [
          "OCEAN_ARRIVAL_NOTICE",
          "OCEAN_E_MANIFEST",
          "AIR_ARRIVAL_NOTICE",
          "AIR_E_MANIFEST",
          "ROAD_ARRIVAL_NOTICE"
        ];
    }
  }

  /**
   * Get empty document status (all documents missing)
   */
  private getEmptyDocumentStatus() {
    return {
      hblReceived: false,
      anEmfReceived: false,
      ciReceived: false,
      plReceived: false
    };
  }

  /**
   * Format document status for templates (matching the stakeholder guidance format)
   */
  formatDocumentStatusForTemplate(status: {
    hblReceived: boolean;
    anEmfReceived: boolean;
    ciReceived: boolean;
    plReceived: boolean;
  }): {
    hbl: "Received" | "Missing";
    anEmf: "Received" | "Missing";
    ciPl: "Received" | "Missing";
  } {
    return {
      hbl: status.hblReceived ? "Received" : "Missing",
      anEmf: status.anEmfReceived ? "Received" : "Missing",
      ciPl: status.ciReceived && status.plReceived ? "Received" : "Missing"
    };
  }
}
