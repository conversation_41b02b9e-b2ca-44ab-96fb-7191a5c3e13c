import { Shipment, Importer } from "nest-modules";

/**
 * Clean Agent Context Types - Service integrations for email templates
 *
 * Contains template data interfaces for different service integrations
 * following the proven processor pattern.
 */

/**
 * Shipment template data - from ShipmentService
 */
export interface ShipmentTemplateData {
  // Basic shipment info
  shipment: Shipment;

  // Formatted fields
  IMPORTER_NAME: string;
  TRANSACTION_NUMBER: string;
  CARGO_CONTROL_NUMBER: string;
  PORT_CODE: string;
  CONTAINER_NUMBERS: string;
  HBL_NUMBER: string;

  // Status
  CUSTOMS_STATUS: string;

  // Simple flags
  HAS_TRANSACTION_NUMBER: boolean;
  HAS_CONTAINERS: boolean;
}

/**
 * Document template data - from DocumentService and AggregationService
 */
export interface DocumentTemplateData {
  PROCESSED_DOCUMENTS: Array<{
    filename: string;
    contentType: string;
    aggregationStatus: "success" | "failed" | "processing" | "pending";
    claroUrl: string;
    sourceContext?: "email_attachment" | "shipment_document"; // New: Source context for documents
    processedAt?: Date; // New: When document was processed
    fileSize?: number; // New: File size for email attachments
  }>;
  SHOW_VALIDATION_ISSUES: boolean;
  CI_PL_MISSING: boolean; // Commercial Invoice/Packing List missing
  HBL_MISSING: boolean; // House Bill of Lading missing
  AN_EMF_MISSING: boolean; // Arrival Notice/E-Manifest missing
  EMAIL_PROCESSING_SUMMARY?: EmailProcessingSummary; // New: Email-specific processing summary
  CONTEXT_TYPE?: "EMAIL_SPECIFIC" | "SHIPMENT_WIDE"; // New: Context mode indicator
}

/**
 * RNS template data - from CandataService (Release Notification System)
 */
export interface RnsTemplateData {
  SHOW_RNS_PROOF: boolean;
  RNS_RESPONSE_MESSAGE: string;
  PROCESS_DATE: string;
  RESPONSE_DATE: string;
  PROCESSING_INDICATOR: string;
  SUBLOCATION_CODE: string;
  TRANSACTION_NUMBER: string;
  CARGO_CONTROL_NUMBER: string;
  PORT_CODE: string;
  CONTAINER_NUMBERS: string;
  HAS_RNS_DATA: boolean;
}

/**
 * Validation template data - from ComplianceValidationService
 */
export interface ValidationTemplateData {
  SHOW_VALIDATION_ISSUES: boolean;
  WEIGHT_MISSING: boolean;
  PORT_CODE_MISSING: boolean;
  CCN_MISSING: boolean; // Cargo Control Number missing
  OGD_FILING_PENDING: boolean; // Other Government Department filing pending
  SHOW_DETAILS: boolean;
  SHOW_COMPLIANCE_ERRORS: boolean;
  VALIDATION_MESSAGE: string;
  MISSING_FIELDS_COUNT: number;
  COMPLIANCE_ERRORS_COUNT: number;
}

/**
 * Importer template data - from ImporterService
 */
export interface ImporterTemplateData {
  IMPORTER_NAME: string;
  IMPORTER_EMAIL: string;
  IMPORTER_PHONE: string;
  IMPORTER_ADDRESS: string;
  HAS_IMPORTER: boolean;
  importer: Importer | null;
}

/**
 * Processed document information for template context
 */
export interface ProcessedDocument {
  filename: string;
  contentType: string;
  aggregationStatus: "success" | "failed" | "processing" | "pending";
  claroUrl: string;
  sourceContext?: "email_attachment" | "shipment_document"; // Source context for documents
  processedAt?: Date; // When document was processed
  fileSize?: number; // File size for email attachments
}

/**
 * Email processing summary for template context
 */
export interface EmailProcessingSummary {
  totalAttachments: number;
  processedSuccessfully: number;
  processingFailed: number;
  stillProcessing: number;
}

/**
 * Email context options for CleanAgentContext methods
 */
export interface EmailContextOptions {
  emailId?: number; // Optional email ID for email-specific context
  gmailId?: string; // Optional Gmail ID for FileBatch lookup
  includeEmailAttachments?: boolean; // Flag to control email-specific processing
}

/**
 * Enhanced request parameters for document data gathering
 */
export interface DocumentGatherRequest {
  shipmentId: number;
  emailContext?: {
    emailId: number;
    gmailId: string;
    includeEmailAttachments: boolean;
  };
}

/**
 * Unified template context containing all formatted data for template rendering
 * This interface contains both raw data (existing) and formatted template variables (new)
 */
export interface UnifiedTemplateContext {
  // Raw data (existing from individual data gatherers)
  shipmentData: ShipmentTemplateData;
  documentData: DocumentTemplateData;
  rnsData: RnsTemplateData;
  importerData: ImporterTemplateData;
  validationData: ValidationTemplateData;

  // Formatted template variables (new - moved from UnifiedTemplateRendererService)
  CCN_PLACEHOLDER: string;
  CONTAINER_PLACEHOLDER: string;
  HBL_PLACEHOLDER: string;
  HBL_STATUS_PLACEHOLDER: string;
  AN_EMF_STATUS_PLACEHOLDER: string;
  CI_PL_STATUS_PLACEHOLDER: string;
  CUSTOMS_STATUS_LINE_PLACEHOLDER: string;
  MISSING_DOCUMENTS_PLACEHOLDER: string;
  MISSING_FIELDS_PLACEHOLDER: string;
  COMPLIANCE_ERRORS_PLACEHOLDER: string;

  // Status and compliance data (new - moved from UnifiedTemplateRendererService)
  COMPLIANCE_STATUS: string;
  COMPLIANCE_SCORE: number;
  STATUS_CATEGORY: string;
  STATUS_PRIORITY: number;
  STATUS_UPDATED: string;

  // Processed documents (existing, possibly enhanced)
  PROCESSED_DOCUMENTS?: ProcessedDocument[];
}
