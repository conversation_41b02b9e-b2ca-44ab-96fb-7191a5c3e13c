import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Shipment, Document } from "nest-modules";
import { ShipmentModule } from "@/shipment/shipment.module";
import { DocumentModule } from "@/document/document.module";
import { ImporterModule } from "@/importer/importer.module";
import { AggregationModule } from "@/aggregation/aggregation.module";
import { CleanAgentContextService } from "./clean-agent-context.service";
import { ShipmentDataGatherer } from "./shipment-data-gatherer.service";
import { DocumentDataGatherer } from "./document-data-gatherer.service";
import { CandataDataGatherer } from "./candata-data-gatherer.service";
import { ComplianceValidationDataGatherer } from "./compliance-validation-data-gatherer.service";
import { ImporterDataGatherer } from "./importer-data-gatherer.service";
import { DocumentStatusDeterminer } from "./document-status-determiner.service";

/**
 * Clean Agent Context Module - Service Integration Hub
 *
 * Demonstrates the pattern of using a singleton service that can resolve
 * both request-scoped and default-scoped services in BullMQ processors.
 * Integrates with multiple services to gather comprehensive template data
 * for email generation.
 *
 * Uses the processor pattern from AggregationExecuter - instead of trying
 * to resolve services directly, we resolve processors that have the proper
 * dependencies injected.
 */
@Module({
  imports: [
    // Entity imports for direct database access if needed
    TypeOrmModule.forFeature([Shipment, Document]),

    // Import service modules
    ShipmentModule.forFeature(), // Provides ShipmentService (REQUEST-scoped)
    DocumentModule, // Provides DocumentService (DEFAULT-scoped with ClsService)
    ImporterModule, // Provides ImporterService (REQUEST-scoped)
    AggregationModule // Provides AggregationService (DEFAULT-scoped with ClsService)
    // Note: CandataModule is globally imported in AppModule
  ],
  providers: [
    CleanAgentContextService,
    ShipmentDataGatherer, // REQUEST-scoped processor for ShipmentService
    DocumentDataGatherer, // DEFAULT-scoped processor for DocumentService
    CandataDataGatherer, // REQUEST-scoped processor for CandataService
    ImporterDataGatherer, // REQUEST-scoped processor for ImporterService
    ComplianceValidationDataGatherer, // DEFAULT-scoped processor for ComplianceValidationService
    DocumentStatusDeterminer // DEFAULT-scoped service for clean document status logic
  ],
  exports: [CleanAgentContextService, DocumentStatusDeterminer]
})
export class CleanAgentContextModule {}
