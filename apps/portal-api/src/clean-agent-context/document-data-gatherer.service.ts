import { Injectable, Logger } from "@nestjs/common";
import { DocumentService } from "@/document/services/document.service";
import { AggregationService } from "@/aggregation/aggregation.service";
import { FileBatchService } from "@/document/services/file-batch.service";
import { AggregationTargetType } from "nest-modules";
import {
  DocumentTemplateData,
  DocumentGatherRequest,
  EmailProcessingSummary
} from "./clean-agent-context.types";

/**
 * Document Data Gatherer - DEFAULT-scoped processor
 *
 * This service acts as a processor for gathering document data for templates.
 * Since DocumentService and AggregationService use ClsService (DEFAULT scope), we can inject them directly.
 * This mirrors the pattern used in AggregationExecuter.
 *
 * Integrates with both DocumentService and AggregationService to provide complete document processing data.
 */
@Injectable()
export class DocumentDataGatherer {
  private readonly logger = new Logger(DocumentDataGatherer.name);

  constructor(
    private readonly documentService: DocumentService,
    private readonly aggregationService: AggregationService,
    private readonly fileBatchService: FileBatchService
  ) {}

  /**
   * Gather document template data
   * @param request - Enhanced request with optional email context
   * @returns Document template data ready for use
   */
  async gather(request: DocumentGatherRequest): Promise<DocumentTemplateData> {
    const { shipmentId, emailContext } = request;

    this.logger.log(
      `Gathering document data for shipment ${shipmentId}${emailContext ? " with email context" : ""}`
    );

    // Check if email-specific processing is requested
    if (emailContext?.includeEmailAttachments) {
      return await this.gatherEmailSpecificDocuments(shipmentId, emailContext.gmailId);
    } else {
      return await this.gatherShipmentWideDocuments(shipmentId);
    }
  }

  /**
   * Gather shipment-wide document data (existing functionality)
   * Shows all documents for the shipment with missing document analysis
   */
  private async gatherShipmentWideDocuments(shipmentId: number): Promise<DocumentTemplateData> {
    this.logger.log(`Gathering shipment-wide document data for shipment ${shipmentId}`);

    // Get documents for the shipment
    const documentsResponse = await this.documentService.getDocuments({
      shipmentId: shipmentId,
      limit: 100, // Get all documents
      skip: 0
    });

    // Get aggregation for the shipment using AggregationService
    const aggregation = await this.aggregationService.getAggregationByTarget(
      shipmentId,
      AggregationTargetType.SHIPMENT
    );

    // Create a map of document ID to aggregation status for quick lookup
    const aggregationStatusMap = new Map<number, string>();
    if (aggregation && aggregation.documents && aggregation.documents.length > 0) {
      for (const doc of aggregation.documents) {
        aggregationStatusMap.set(doc.id, aggregation.status || "pending");
      }
    }

    // Process documents into template format
    const processedDocuments = documentsResponse.documents.map((doc) => {
      // Get real aggregation status from AggregationService or fallback to document-based status
      const aggregationStatus = this.getAggregationStatus(doc, aggregationStatusMap);

      return {
        filename: doc.name || "Unknown Document",
        contentType: doc.documentType?.name || "Unknown Type",
        aggregationStatus: aggregationStatus,
        claroUrl: this.generateClaroUrl(doc.id, shipmentId),
        sourceContext: "shipment_document" as const
      };
    });

    // Check for missing document types
    const documentTypes = documentsResponse.documents.map(
      (doc) => doc.documentType?.name?.toLowerCase() || ""
    );

    const templateData: DocumentTemplateData = {
      PROCESSED_DOCUMENTS: processedDocuments,
      SHOW_VALIDATION_ISSUES: this.hasValidationIssues(documentsResponse.documents),
      CI_PL_MISSING: this.isMissingCommercialInvoiceOrPackingList(documentTypes),
      HBL_MISSING: this.isMissingHouseBillOfLading(documentTypes),
      AN_EMF_MISSING: this.isMissingArrivalNoticeOrEManifest(documentTypes),
      CONTEXT_TYPE: "SHIPMENT_WIDE"
    };

    return templateData;
  }

  /**
   * Gather email-specific document data (new functionality)
   * Shows only documents from current email attachments with processing status
   */
  private async gatherEmailSpecificDocuments(
    shipmentId: number,
    gmailId: string
  ): Promise<DocumentTemplateData> {
    this.logger.log(
      `Gathering email-specific document data for shipment ${shipmentId}, Gmail ID: ${gmailId}`
    );

    try {
      // 1. Get documents from FileBatch using Gmail ID
      const fileBatch = await this.fileBatchService.getFiles(gmailId, {
        documents: {
          documentType: true
        }
      });

      if (!fileBatch || fileBatch.length === 0) {
        this.logger.warn(`No FileBatch found for Gmail ID: ${gmailId}`);
        return this.createEmptyEmailContext();
      }

      // 2. Get document IDs for aggregation status lookup
      const documentIds = fileBatch
        .filter((file) => file.documents?.length > 0)
        .flatMap((file) => file.documents.map((doc) => doc.id));

      if (documentIds.length === 0) {
        this.logger.warn(`No documents found in FileBatch for Gmail ID: ${gmailId}`);
        return this.createEmptyEmailContext();
      }

      // 3. Get aggregation status for the shipment and filter for our documents
      const aggregation = await this.aggregationService.getAggregationByTarget(
        shipmentId,
        AggregationTargetType.SHIPMENT
      );

      // Create a map of document ID to aggregation status, filtering for our email documents
      const aggregationStatusMap = new Map<number, string>();
      if (aggregation && aggregation.documents && aggregation.documents.length > 0) {
        for (const doc of aggregation.documents) {
          // Only include documents that are from our email attachments
          if (documentIds.includes(doc.id)) {
            aggregationStatusMap.set(doc.id, aggregation.status || "pending");
          }
        }
      }

      // 4. Format documents for template with email-specific context
      const emailAttachmentDocuments = fileBatch
        .map((file) => {
          const document = file.documents?.[0];
          if (!document) return null;

          const aggregationStatus = this.getAggregationStatus(document, aggregationStatusMap);

          return {
            filename: file.name || document.name || "Unknown Document",
            contentType: document.documentType?.name || "Unknown Type",
            aggregationStatus: aggregationStatus,
            claroUrl: this.generateClaroUrl(document.id, shipmentId),
            sourceContext: "email_attachment" as const,
            processedAt: document.lastEditDate || document.createDate,
            fileSize: null // File size not available in current File entity
          };
        })
        .filter(Boolean);

      // 5. Generate processing summary
      const emailProcessingSummary: EmailProcessingSummary = {
        totalAttachments: fileBatch.length,
        processedSuccessfully: emailAttachmentDocuments.filter((doc) => doc.aggregationStatus === "success")
          .length,
        processingFailed: emailAttachmentDocuments.filter((doc) => doc.aggregationStatus === "failed").length,
        stillProcessing: emailAttachmentDocuments.filter((doc) => doc.aggregationStatus === "processing")
          .length
      };

      // 6. For email context, we don't show missing document analysis (that's shipment-wide)
      const templateData: DocumentTemplateData = {
        PROCESSED_DOCUMENTS: emailAttachmentDocuments,
        SHOW_VALIDATION_ISSUES: false, // Email context focuses on processing, not validation
        CI_PL_MISSING: false,
        HBL_MISSING: false,
        AN_EMF_MISSING: false,
        EMAIL_PROCESSING_SUMMARY: emailProcessingSummary,
        CONTEXT_TYPE: "EMAIL_SPECIFIC"
      };

      this.logger.log(
        `Email-specific document data prepared: ${emailAttachmentDocuments.length} documents from ${fileBatch.length} attachments`
      );
      return templateData;
    } catch (error) {
      this.logger.error(`Error gathering email-specific documents for Gmail ID ${gmailId}:`, error);
      return this.createEmptyEmailContext();
    }
  }

  /**
   * Create empty email context for error cases
   */
  private createEmptyEmailContext(): DocumentTemplateData {
    return {
      PROCESSED_DOCUMENTS: [],
      SHOW_VALIDATION_ISSUES: false,
      CI_PL_MISSING: false,
      HBL_MISSING: false,
      AN_EMF_MISSING: false,
      EMAIL_PROCESSING_SUMMARY: {
        totalAttachments: 0,
        processedSuccessfully: 0,
        processingFailed: 0,
        stillProcessing: 0
      },
      CONTEXT_TYPE: "EMAIL_SPECIFIC"
    };
  }

  /**
   * Get aggregation status from AggregationService data or fallback to document status
   */
  private getAggregationStatus(
    document: any,
    aggregationStatusMap: Map<number, string>
  ): "success" | "failed" | "processing" | "pending" {
    // First, try to get real aggregation status from AggregationService
    const realAggregationStatus = aggregationStatusMap.get(document.id);
    if (realAggregationStatus) {
      // Map aggregation status to template expected values
      switch (realAggregationStatus.toLowerCase()) {
        case "completed":
        case "success":
          return "success";
        case "failed":
        case "error":
          return "failed";
        case "processing":
        case "running":
        case "in_progress":
          return "processing";
        case "pending":
        case "queued":
        default:
          return "pending";
      }
    }

    // Fallback: derive status from document status if no aggregation data
    return this.getDocumentStatusBasedAggregationStatus(document);
  }

  /**
   * Get aggregation status based on document status (fallback approach)
   */
  private getDocumentStatusBasedAggregationStatus(
    document: any
  ): "success" | "failed" | "processing" | "pending" {
    // Map document status to aggregation status approximation
    switch (document.status) {
      case "extracted":
        return "success";
      case "extracting":
        return "processing";
      case "failed":
        return "failed";
      case "pending":
      default:
        return "pending";
    }
  }

  /**
   * Generate Claro URL for document viewing
   */
  private generateClaroUrl(documentId: number, shipmentId: number): string {
    // This would typically use a configuration service for the base URL
    // For now, using a placeholder pattern
    return `/portal/shipments/${shipmentId}/documents/${documentId}`;
  }

  /**
   * Check if there are validation issues
   */
  private hasValidationIssues(documents: any[]): boolean {
    // Check for validation errors in documents
    const hasDocumentErrors = documents.some(
      (doc) => doc.validationErrors && doc.validationErrors.length > 0
    );

    // Check if any documents have failed status
    const hasFailedDocuments = documents.some((doc) => doc.status === "failed");

    return hasDocumentErrors || hasFailedDocuments;
  }

  /**
   * Check if Commercial Invoice or Packing List is missing
   */
  private isMissingCommercialInvoiceOrPackingList(documentTypes: string[]): boolean {
    const hasCI = documentTypes.some((type) => type.includes("commercial invoice") || type.includes("ci"));
    const hasPL = documentTypes.some((type) => type.includes("packing list") || type.includes("pl"));

    return !hasCI || !hasPL;
  }

  /**
   * Check if House Bill of Lading is missing
   */
  private isMissingHouseBillOfLading(documentTypes: string[]): boolean {
    return !documentTypes.some(
      (type) =>
        type.includes("house bill of lading") || type.includes("hbl") || type.includes("bill of lading")
    );
  }

  /**
   * Check if Arrival Notice or E-Manifest is missing
   */
  private isMissingArrivalNoticeOrEManifest(documentTypes: string[]): boolean {
    const hasAN = documentTypes.some((type) => type.includes("arrival notice") || type.includes("an"));
    const hasEMF = documentTypes.some(
      (type) => type.includes("e-manifest") || type.includes("emf") || type.includes("manifest")
    );

    return !hasAN && !hasEMF;
  }
}
