/**
 * Consolidated Email Response Messages
 *
 * This centralizes all email response messages used by intent handlers.
 * Messages are organized by intent handler type and customs status.
 *
 * Structure: handler -> customsStatus -> message
 */
export const emailResponseMessages = {
  // ========== DOCUMENT ACKNOWLEDGMENT HANDLERS ==========

  acknowledgeDocuments: {
    ocean_air:
      "We have received the required documents for your shipment and are currently reviewing and processing them.",
    truck:
      "We have received the required documents for your shipment and are currently reviewing and processing them. Transaction# will be sent to you shortly.",
    default:
      "We have received the required documents for your shipment and are currently reviewing and processing them."
  },

  acknowledgeMissingDocuments: {
    default:
      "Please send the missing document shown below at your earliest convenience for the subject shipment, so we can file customs without delay."
  },

  documentationComing: {
    default: "Thank you for the update. We'll be waiting for the additional or updated documents."
  },

  processDocument: {
    success: "Thank you for providing the requested documents. We have received and processed the following:",
    default: "We have processed your document submission."
  },

  // ========== STATUS & INFORMATION HANDLERS ==========

  getCustomsStatus: {
    "pending-commercial-invoice":
      "Please send the missing document shown below at your earliest convenience for the subject shipment, so we can file customs without delay.",
    "pending-confirmation":
      "There are compliance issues or missing required fields preventing submission of the subject shipment. Please respond to the email and provide additional information for the missing fields.",
    "pending-arrival":
      "The estimated time of arrival (ETA) at the port for the subject shipment is {{ eta | date('F j, Y') }}. We expect to submit the customs entry as the arrival date approaches.",
    live: "The submission for the subject shipment has been initiated. We will let you know once released by customs.",
    "entry-submitted":
      "The entry for the subject shipment has been submitted. We will let you know once released by customs.",
    "entry-accepted":
      "The subject shipment entry has been accepted by Customs and is awaiting arrival of goods.",
    exam: "The subject shipment has been selected by customs for examination. We are contacting you for further information.<br /><br />The ETA of the cargo at destination is {{ eta | date('F j, Y') }}.",
    released: "The subject shipment has been released by customs."
  },

  requestCADDocument: {
    "pending-commercial-invoice":
      "We can't provide the CAD yet as we're missing the following document(s). Please send them at your earliest convenience so we can proceed with filing.",
    "pending-confirmation":
      "We're currently unable to provide the CAD as there are compliance issues or missing required fields. Please respond to the email and provide additional information for the missing fields.",
    "pending-arrival": "Send email to backoffice to send them the CAD.",
    live: "Please see CAD document attached.",
    "entry-submitted": "Please see CAD document attached.",
    "entry-accepted": "Please see CAD document attached.",
    exam: "Please see CAD document attached.",
    released: "Please see CAD document attached."
  },

  requestRNSProof: {
    "pending-commercial-invoice":
      "We can't provide the RNS yet as we're missing the following document(s). Please send them at your earliest convenience so we can proceed with filing.",
    "pending-confirmation":
      "We're currently unable to provide the RNS as there are compliance issues or missing required fields. Please respond to the email and provide additional information for the missing fields.",
    "pending-arrival": "The RNS can only be provided after the shipment has been submitted to customs.",
    live: "The RNS can only be provided after the shipment has been submitted to customs.",
    "entry-submitted": "The RNS can only be provided after the shipment has been accepted by customs.",
    "entry-accepted": "RNS information:",
    exam: "RNS information:",
    released: "RNS Proof of Release information:"
  },

  // ========== PROCESSING REQUEST HANDLERS ==========

  requestRushProcessing: {
    "pending-commercial-invoice":
      "Please send the missing document shown below at your earliest convenience for the subject shipment, so we can file customs without delay.",
    "pending-confirmation":
      "There are compliance issues or missing required fields preventing submission of the subject shipment. Please respond to the email and provide additional information for the missing fields.",
    "pending-arrival": "We've received your rush request and will be submitting the entry right away.",
    live: "We've received your rush request and will be submitting the entry right away.",
    "entry-submitted":
      "The entry for the subject shipment has already been submitted. We will let you know once released by customs.",
    "entry-accepted":
      "The subject shipment entry has already been submitted and accepted by Customs and is awaiting arrival of goods.",
    exam: "Please note the shipment has been selected for exam and will be released once the examination is complete.",
    released: "The subject shipment has been released by CBSA on {{ shipment.releaseDate | date('d-m-Y') }}."
  },

  requestManualProcessing: {
    default:
      "Your request for manual processing has been received. Our team will review your shipment and contact you with any questions or updates."
  },

  requestHoldShipment: {
    default:
      "We've received your request to cancel/hold the entry. Our team has been notified and will take the necessary action."
  },

  // ========== DATA UPDATE HANDLERS ==========

  updateShipment: {
    success: "Your shipment information has been successfully updated.",
    error: "There was an error updating your shipment information."
  },

  // ========== DYNAMIC CONTENT GENERATORS (GetShipmentStatusHandler) ==========

  etaResponse: {
    available: "The estimated time of arrival (ETA) for your shipment is {{ eta | date('F j, Y') }}.",
    unavailable: "ETA information is not available for this shipment at this time."
  },

  transactionNumberResponse: {
    available: "The transaction number for your shipment is {{ TRANSACTION_NUMBER }}.",
    unavailable: "Transaction number is not yet available for this shipment."
  },

  releaseStatusResponse: {
    released: "Your shipment was released by CBSA on {{ shipment.releaseDate | date('F j, Y') }}.",
    not_released: "Your shipment has not yet been released by customs."
  },

  shippingStatusResponse: {
    default: "Current shipping status: {{ SHIPPING_STATUS }}"
  },

  // ========== ERROR/FALLBACK MESSAGES ==========

  systemError: {
    default: "We're currently experiencing technical difficulties. Please contact support for assistance."
  },

  contactSupport: {
    default: "For immediate assistance, please contact our support team."
  }
};
