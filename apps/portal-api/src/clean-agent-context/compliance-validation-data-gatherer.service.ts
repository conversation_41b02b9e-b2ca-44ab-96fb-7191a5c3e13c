import { Injectable, Logger } from "@nestjs/common";
import { ComplianceValidationService } from "@/shipment/services/compliance-validation.service";
import { Shipment } from "nest-modules";
import { ValidationTemplateData } from "./clean-agent-context.types";

/**
 * ComplianceValidationDataGatherer - DEFAULT-scoped processor
 *
 * This service integrates with ComplianceValidationService to gather validation flags
 * and compliance data for email templates. It processes shipment compliance data
 * and transforms it into template-ready format.
 *
 * Uses ComplianceValidationService (DEFAULT-scoped) for:
 * - Shipment compliance validation
 * - Missing field detection
 * - OGD filing status
 * - Required field validation
 *
 * Key Template Variables Provided:
 * - SHOW_VALIDATION_ISSUES - Whether to show validation section
 * - WEIGHT_MISSING - Weight field missing
 * - PORT_CODE_MISSING - Port code missing
 * - CCN_MISSING - Cargo control number missing
 * - OGD_FILING_PENDING - Other Government Department filing pending
 * - SHOW_DETAILS - Show details section
 * - SHOW_COMPLIANCE_ERRORS - Show compliance errors
 */
@Injectable()
export class ComplianceValidationDataGatherer {
  private readonly logger: Logger;

  constructor(private readonly complianceValidationService: ComplianceValidationService) {
    this.logger = new Logger("ComplianceValidationDataGatherer");
  }

  /**
   * Gather validation template data using ComplianceValidationService
   * @param params - Parameters containing shipment ID and optional shipment data
   * @returns Validation template data ready for use
   */
  async gather(params: { shipmentId: number; shipment?: Shipment }): Promise<ValidationTemplateData> {
    this.logger.debug(`Gathering validation data for shipment ${params.shipmentId}`);

    try {
      // Get shipment compliance data
      const shipmentCompliances = await this.complianceValidationService.getShipmentCompliances([
        params.shipmentId
      ]);

      if (shipmentCompliances.length === 0) {
        this.logger.warn(`No compliance data found for shipment ${params.shipmentId}`);
        return this.getEmptyValidationData();
      }

      const compliance = shipmentCompliances[0];
      const shipment = compliance.shipment || params.shipment;

      // Validate compliance to get specific errors
      const validationResults = this.complianceValidationService.validateShipmentCompliances(
        shipmentCompliances,
        false // Don't skip filings validation
      );

      const validationResult = validationResults[0];

      // Analyze missing fields
      const missingFields = compliance.missingFields || [];
      const missingFieldsCount = missingFields.length;

      // Check for specific missing fields
      const weightMissing =
        this.isFieldMissing(shipment, "weight") ||
        missingFields.some((field) => field.toString().toLowerCase().includes("weight"));

      const portCodeMissing =
        this.isFieldMissing(shipment, "portCode") ||
        missingFields.some((field) => field.toString().toLowerCase().includes("port"));

      const ccnMissing =
        this.isFieldMissing(shipment, "cargoControlNumber") ||
        missingFields.some((field) => field.toString().toLowerCase().includes("cargo"));

      // Count compliance errors across all invoices
      const complianceErrorsCount = this.countComplianceErrors(validationResult);

      // Check for OGD filing pending status
      const ogdFilingPending = this.hasOgdFilingPending(validationResult);

      // Determine visibility flags
      const hasValidationIssues = missingFieldsCount > 0 || complianceErrorsCount > 0;
      const showDetails = hasValidationIssues;
      const showComplianceErrors = complianceErrorsCount > 0;

      // Generate validation message
      const validationMessage = this.generateValidationMessage(
        missingFieldsCount,
        complianceErrorsCount,
        weightMissing,
        portCodeMissing,
        ccnMissing,
        ogdFilingPending
      );

      const templateData: ValidationTemplateData = {
        SHOW_VALIDATION_ISSUES: hasValidationIssues,
        WEIGHT_MISSING: weightMissing,
        PORT_CODE_MISSING: portCodeMissing,
        CCN_MISSING: ccnMissing,
        OGD_FILING_PENDING: ogdFilingPending,
        SHOW_DETAILS: showDetails,
        SHOW_COMPLIANCE_ERRORS: showComplianceErrors,
        VALIDATION_MESSAGE: validationMessage,
        MISSING_FIELDS_COUNT: missingFieldsCount,
        COMPLIANCE_ERRORS_COUNT: complianceErrorsCount
      };

      this.logger.debug(
        `Validation data prepared for shipment ${params.shipmentId}: ${missingFieldsCount} missing fields, ${complianceErrorsCount} compliance errors`
      );
      return templateData;
    } catch (error) {
      this.logger.error(`Error gathering validation data for shipment ${params.shipmentId}:`, error);

      // Return safe fallback data
      return this.getEmptyValidationData();
    }
  }

  /**
   * Check if a specific field is missing from the shipment
   */
  private isFieldMissing(shipment: Shipment | undefined, fieldName: string): boolean {
    if (!shipment) return true;

    const value = shipment[fieldName];
    return value === null || value === undefined || value === "";
  }

  /**
   * Count compliance errors from validation results
   */
  private countComplianceErrors(validationResult: any): number {
    if (!validationResult) return 0;

    let errorCount = 0;

    // Count missing fields
    if (validationResult.missingFields) {
      errorCount += validationResult.missingFields.length;
    }

    // Count non-compliant invoices and their errors
    if (validationResult.nonCompliantInvoices) {
      for (const invoice of validationResult.nonCompliantInvoices) {
        errorCount += (invoice.missingFields || []).length;
        errorCount += (invoice.shipToMissingFields || []).length;
        errorCount += (invoice.vendorMissingFields || []).length;
        errorCount += (invoice.nonCompliantLines || []).length;
      }
    }

    return errorCount;
  }

  /**
   * Check if there are pending OGD filings
   */
  private hasOgdFilingPending(validationResult: any): boolean {
    if (!validationResult?.nonCompliantInvoices) return false;

    for (const invoice of validationResult.nonCompliantInvoices) {
      if (invoice.nonCompliantLines?.length > 0) {
        for (const line of invoice.nonCompliantLines) {
          if (line.nonCompliantRecords?.length > 0) {
            const hasOgdRecord = line.nonCompliantRecords.some(
              (record) => record.reason === "MISSING_OGD_FILING"
            );
            if (hasOgdRecord) return true;
          }
        }
      }
    }

    return false;
  }

  /**
   * Generate a human-readable validation message
   */
  private generateValidationMessage(
    missingFieldsCount: number,
    complianceErrorsCount: number,
    weightMissing: boolean,
    portCodeMissing: boolean,
    ccnMissing: boolean,
    ogdFilingPending: boolean
  ): string {
    const issues: string[] = [];

    if (missingFieldsCount > 0) {
      issues.push(`${missingFieldsCount} missing field${missingFieldsCount === 1 ? "" : "s"}`);
    }

    if (complianceErrorsCount > 0) {
      issues.push(`${complianceErrorsCount} compliance error${complianceErrorsCount === 1 ? "" : "s"}`);
    }

    // Add specific field mentions
    const specificFields: string[] = [];
    if (weightMissing) specificFields.push("weight");
    if (portCodeMissing) specificFields.push("port code");
    if (ccnMissing) specificFields.push("cargo control number");

    if (specificFields.length > 0) {
      issues.push(`missing ${specificFields.join(", ")}`);
    }

    if (ogdFilingPending) {
      issues.push("OGD filing required");
    }

    if (issues.length === 0) {
      return "No validation issues found";
    }

    return `Validation issues: ${issues.join(", ")}.`;
  }

  /**
   * Get empty validation data (no issues found)
   */
  private getEmptyValidationData(): ValidationTemplateData {
    return {
      SHOW_VALIDATION_ISSUES: false,
      WEIGHT_MISSING: false,
      PORT_CODE_MISSING: false,
      CCN_MISSING: false,
      OGD_FILING_PENDING: false,
      SHOW_DETAILS: false,
      SHOW_COMPLIANCE_ERRORS: false,
      VALIDATION_MESSAGE: "No validation issues found",
      MISSING_FIELDS_COUNT: 0,
      COMPLIANCE_ERRORS_COUNT: 0
    };
  }
}
