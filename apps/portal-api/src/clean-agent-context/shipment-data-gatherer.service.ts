import { Injectable, Inject, Scope } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { AuthenticatedRequest } from "nest-modules";
import { ShipmentService } from "@/shipment/services/shipment.service";
import { ShipmentTemplateData } from "./clean-agent-context.types";

/**
 * Shipment Data Gatherer - REQUEST-scoped processor
 *
 * This service acts as a processor that can be resolved by the
 * CleanAgentContextService with proper request context.
 * This mirrors the pattern used in AggregationExecuter.
 */
@Injectable({ scope: Scope.REQUEST })
export class ShipmentDataGatherer {
  constructor(
    @Inject(REQUEST) private readonly request: AuthenticatedRequest,
    private readonly shipmentService: ShipmentService
  ) {}

  /**
   * Gather shipment template data
   * @param params - Parameters for data gathering
   * @returns Shipment template data ready for use
   */
  async gather(params: { shipmentId: number }): Promise<ShipmentTemplateData> {
    const { shipmentId } = params;

    // Get shipment with needed relations
    const shipment = await this.shipmentService.getShipmentById(shipmentId, undefined, {
      importer: true,
      containers: true,
      portOfLoading: true,
      placeOfDelivery: true
    });

    if (!shipment) {
      throw new Error(`Shipment ${shipmentId} not found`);
    }

    // Transform to template format
    const templateData: ShipmentTemplateData = {
      // Core data
      shipment,

      // Formatted fields
      IMPORTER_NAME: shipment.importer?.companyName || "Unknown Importer",
      TRANSACTION_NUMBER: shipment.transactionNumber || "",
      CARGO_CONTROL_NUMBER: shipment.cargoControlNumber || "",
      PORT_CODE: shipment.portCode || "",
      CONTAINER_NUMBERS: this.formatContainerNumbers(shipment),
      HBL_NUMBER: shipment.hblNumber || "",

      // Status
      CUSTOMS_STATUS: shipment.customsStatus || "unknown",

      // Simple flags
      HAS_TRANSACTION_NUMBER: Boolean(shipment.transactionNumber),
      HAS_CONTAINERS: Boolean(shipment.containers && shipment.containers.length > 0)
    };

    return templateData;
  }

  /**
   * Format container numbers for display
   */
  private formatContainerNumbers(shipment: any): string {
    if (!shipment.containers || shipment.containers.length === 0) {
      return "No containers";
    }

    return (
      shipment.containers
        .map((c) => c.containerNumber)
        .filter(Boolean)
        .join(", ") || "Unknown containers"
    );
  }
}
