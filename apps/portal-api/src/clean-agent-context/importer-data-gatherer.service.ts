import { Injectable, Logger, Inject, Scope } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { ImporterService } from "@/importer/importer.service";
import { AuthenticatedRequest } from "nest-modules";
import { Importer } from "nest-modules";
import { ImporterTemplateData } from "./clean-agent-context.types";

/**
 * ImporterDataGatherer - REQUEST-scoped processor
 *
 * This service integrates with ImporterService to gather importer data
 * for email templates. It processes importer information and transforms
 * it into template-ready format.
 *
 * Uses ImporterService (REQUEST-scoped) for:
 * - Importer retrieval by ID
 * - Importer lookup by email
 * - Organization-scoped queries
 *
 * Key Template Variables Provided:
 * - IMPORTER_NAME - Company name
 * - IMPORTER_EMAIL - Contact email
 * - IMPORTER_PHONE - Contact phone
 * - IMPORTER_ADDRESS - Full address
 * - HAS_IMPORTER - Whether importer data exists
 */
@Injectable({ scope: Scope.REQUEST })
export class ImporterDataGatherer {
  private readonly logger: Logger;

  constructor(
    @Inject(REQUEST) private readonly request: AuthenticatedRequest,
    private readonly importerService: ImporterService
  ) {
    this.logger = new Logger("ImporterDataGatherer");
  }

  /**
   * Gather importer template data using ImporterService
   * @param params - Parameters containing importer ID or email
   * @returns Importer template data ready for use
   */
  async gather(params: {
    importerId?: number;
    importerEmail?: string;
    importer?: Importer;
  }): Promise<ImporterTemplateData> {
    this.logger.debug(`Gathering importer data for ID ${params.importerId} or email ${params.importerEmail}`);

    try {
      let importer: Importer | null = null;

      // Use provided importer if available
      if (params.importer) {
        importer = params.importer;
      }
      // Otherwise try to fetch by ID or email
      else if (params.importerId) {
        importer = await this.importerService.getImporterById(params.importerId);
      } else if (params.importerEmail) {
        importer = await this.importerService.getImporterByReceiveEmail([params.importerEmail]);
      }

      if (!importer) {
        this.logger.warn(`No importer found for ID ${params.importerId} or email ${params.importerEmail}`);
        return this.getEmptyImporterData();
      }

      // Transform importer data for template
      const templateData: ImporterTemplateData = {
        IMPORTER_NAME: importer.companyName || "Unknown Importer",
        IMPORTER_EMAIL: this.getImporterEmail(importer),
        IMPORTER_PHONE: importer.phoneNumber || "",
        IMPORTER_ADDRESS: this.formatImporterAddress(importer),
        HAS_IMPORTER: true,
        importer
      };

      this.logger.debug(`Importer data prepared for: ${importer.companyName}`);
      return templateData;
    } catch (error) {
      this.logger.error(`Error gathering importer data:`, error);

      // Return safe fallback data
      return this.getEmptyImporterData();
    }
  }

  /**
   * Get the primary email address for the importer
   */
  private getImporterEmail(importer: Importer): string {
    // Check for receive email (primary contact)
    if (importer.receiveEmail) {
      return importer.receiveEmail;
    }

    // Fallback to general email fields if available
    if (importer.email) {
      return importer.email;
    }

    return "";
  }

  /**
   * Format importer address for template display
   */
  private formatImporterAddress(importer: Importer): string {
    const addressParts: string[] = [];

    if (importer.address) {
      addressParts.push(importer.address);
    }

    if (importer.city) {
      addressParts.push(importer.city);
    }

    if (importer.state) {
      addressParts.push(importer.state);
    }

    if (importer.postalCode) {
      addressParts.push(importer.postalCode);
    }

    if (importer.country) {
      addressParts.push(importer.country.name);
    }

    return addressParts.join(", ");
  }

  /**
   * Get empty importer data (no importer found)
   */
  private getEmptyImporterData(): ImporterTemplateData {
    return {
      IMPORTER_NAME: "Unknown Importer",
      IMPORTER_EMAIL: "",
      IMPORTER_PHONE: "",
      IMPORTER_ADDRESS: "",
      HAS_IMPORTER: false,
      importer: null
    };
  }
}
