#!/usr/bin/env node

/**
 * Test Intent Message Generation in CleanAgentContextService
 * 
 * This script tests the new generateAllIntentMessages method to ensure
 * all intent types produce appropriate messages.
 */

const { NestFactory } = require('@nestjs/core');
const { AppModule } = require('../../../dist/app.module');

async function testIntentMessageGeneration() {
  console.log('🧪 Testing Intent Message Generation');
  console.log('====================================\n');

  const app = await NestFactory.createApplicationContext(AppModule, {
    logger: ['error', 'warn']
  });

  try {
    const { CleanAgentContextService } = require('../../../dist/clean-agent-context/clean-agent-context.service');
    const cleanAgentContextService = app.get(CleanAgentContextService);

    // Mock context data
    const mockContext = {
      shipmentData: {
        CUSTOMS_STATUS: 'pending-commercial-invoice',
        TRANSACTION_NUMBER: 'TX123456',
        ETA_PORT: '2024-08-15',
        TRANSPORTATION_MODE: 'Ocean',
        releaseDate: '2024-08-10'
      },
      documentData: {
        CI_PL_MISSING: true,
        HBL_MISSING: false,
        AN_EMF_MISSING: false
      },
      validationData: {
        COMPLIANCE_ERRORS_COUNT: 2,
        WEIGHT_MISSING: true,
        PORT_CODE_MISSING: false,
        CCN_MISSING: false
      }
    };

    // Test all intent types
    const intentTypes = [
      'PROCESS_DOCUMENT',
      'GET_SHIPMENT_STATUS',
      'REQUEST_CAD_DOCUMENT',
      'REQUEST_RUSH_PROCESSING',
      'DOCUMENTATION_COMING',
      'REQUEST_RNS_PROOF',
      'REQUEST_MANUAL_PROCESSING',
      'REQUEST_HOLD_SHIPMENT',
      'UPDATE_SHIPMENT',
      'ACKNOWLEDGE_DOCUMENTS',
      'ACKNOWLEDGE_MISSING_DOCUMENTS'
    ];

    for (const intentType of intentTypes) {
      console.log(`🎯 Testing Intent: ${intentType}`);
      console.log('-'.repeat(50));

      try {
        // Test with different options for different intents
        const options = getTestOptionsForIntent(intentType);
        
        const messages = cleanAgentContextService.generateAllIntentMessages(
          mockContext,
          intentType,
          options
        );

        console.log('✅ Generated Messages:');
        Object.keys(messages).forEach(key => {
          if (messages[key] && typeof messages[key] === 'string' && messages[key].trim() !== '') {
            console.log(`   ${key}: ${messages[key].substring(0, 100)}${messages[key].length > 100 ? '...' : ''}`);
          } else if (messages[key] && typeof messages[key] === 'boolean') {
            console.log(`   ${key}: ${messages[key]}`);
          }
        });

        // Verify that key messages are present
        const expectedMessages = getExpectedMessagesForIntent(intentType);
        expectedMessages.forEach(expectedKey => {
          if (!messages[expectedKey]) {
            console.log(`⚠️  Warning: Expected message key '${expectedKey}' not found`);
          }
        });

      } catch (error) {
        console.log(`❌ Error testing ${intentType}: ${error.message}`);
      }

      console.log('\n');
    }

    // Test unknown intent type
    console.log('🎯 Testing Unknown Intent Type');
    console.log('-'.repeat(50));
    const unknownMessages = cleanAgentContextService.generateAllIntentMessages(
      mockContext,
      'UNKNOWN_INTENT',
      {}
    );
    console.log('✅ Unknown intent handled gracefully with fallback messages');
    console.log(`   Generated ${Object.keys(unknownMessages).length} message keys\n`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await app.close();
  }
}

function getTestOptionsForIntent(intentType) {
  switch (intentType) {
    case 'PROCESS_DOCUMENT':
      return {
        hasProcessedDocuments: true,
        processedDocuments: [{ name: 'test-document.pdf' }]
      };
    case 'REQUEST_CAD_DOCUMENT':
      return {
        cadDocument: { filename: 'cad-document.pdf' }
      };
    case 'REQUEST_RNS_PROOF':
      return {
        rnsProofData: {
          content: 'RNS proof content',
          releaseDate: '2024-08-10'
        }
      };
    case 'REQUEST_RUSH_PROCESSING':
      return {
        submissionResult: { success: true },
        backofficeAlerts: { rushProcessingSent: true }
      };
    case 'REQUEST_MANUAL_PROCESSING':
      return {
        backofficeAlerts: { manualProcessingSent: true }
      };
    case 'REQUEST_HOLD_SHIPMENT':
      return {
        backofficeAlerts: { holdShipmentSent: true }
      };
    case 'UPDATE_SHIPMENT':
      return {
        updateResult: { success: true }
      };
    default:
      return {};
  }
}

function getExpectedMessagesForIntent(intentType) {
  const commonMessages = ['SHOW_DETAILS', 'SHOW_VALIDATION_ISSUES'];
  
  switch (intentType) {
    case 'PROCESS_DOCUMENT':
      return [...commonMessages, 'PROCESS_DOCUMENT_MESSAGE', 'ACKNOWLEDGE_DOCUMENTS_MESSAGE'];
    case 'GET_SHIPMENT_STATUS':
      return [...commonMessages, 'GET_SHIPMENT_STATUS_MESSAGE', 'ETA_RESPONSE_MESSAGE'];
    case 'REQUEST_CAD_DOCUMENT':
      return [...commonMessages, 'REQUEST_CAD_DOCUMENT_MESSAGE'];
    case 'REQUEST_RUSH_PROCESSING':
      return [...commonMessages, 'REQUEST_RUSH_PROCESSING_MESSAGE'];
    case 'DOCUMENTATION_COMING':
      return [...commonMessages, 'DOCUMENTATION_COMING_MESSAGE'];
    case 'REQUEST_RNS_PROOF':
      return [...commonMessages, 'REQUEST_RNS_PROOF_MESSAGE'];
    case 'REQUEST_MANUAL_PROCESSING':
      return [...commonMessages, 'REQUEST_MANUAL_PROCESSING_MESSAGE'];
    case 'REQUEST_HOLD_SHIPMENT':
      return [...commonMessages, 'REQUEST_HOLD_SHIPMENT_MESSAGE'];
    case 'UPDATE_SHIPMENT':
      return [...commonMessages, 'UPDATE_SHIPMENT_MESSAGE'];
    case 'ACKNOWLEDGE_DOCUMENTS':
      return [...commonMessages, 'ACKNOWLEDGE_DOCUMENTS_MESSAGE'];
    case 'ACKNOWLEDGE_MISSING_DOCUMENTS':
      return [...commonMessages, 'ACKNOWLEDGE_MISSING_DOCUMENTS_MESSAGE'];
    default:
      return commonMessages;
  }
}

// Run the test
testIntentMessageGeneration()
  .then(() => {
    console.log('✅ Intent message generation test completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });