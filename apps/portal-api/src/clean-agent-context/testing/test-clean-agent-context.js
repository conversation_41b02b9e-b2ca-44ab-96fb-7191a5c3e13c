#!/usr/bin/env node

/**
 * Test Script for Clean Agent Context Service
 *
 * This script tests the clean agent context service directly, simulating
 * how it would be used in a BullMQ processor. It gathers template data
 * for a shipment and logs the results to a markdown file for review.
 *
 * Usage:
 *   node src/clean-agent-context/testing/test-clean-agent-context.js [options]
 *
 * Options:
 *   --org=ID        Organization ID (default: 3)
 *   --shipment=ID   Specific shipment ID to test
 *   --verbose       Show detailed output
 *   --all-data      Include all data types (RNS, documents, compliance)
 */

const { AppModule } = require("../../../dist/app.module");
const { NestFactory } = require("@nestjs/core");
const { DataSource } = require("typeorm");
const fs = require("fs");
const path = require("path");

// Mock request structure for creating context in BullMQ processors
function createMockRequest(organizationId) {
  return {
    user: {
      permission: "ORGANIZATION_ADMIN", // UserPermission.ORGANIZATION_ADMIN
      organization: {
        id: organizationId
      }
    }
  };
}

// Default configuration
const DEFAULTS = {
  organizationId: 3,
  verbose: false,
  allData: false
};

// Parse command line arguments
function parseArgs(args) {
  const result = { ...DEFAULTS };

  for (const arg of args.slice(2)) {
    if (arg.startsWith("--org=")) {
      result.organizationId = parseInt(arg.split("=")[1]);
    } else if (arg.startsWith("--shipment=")) {
      result.shipmentId = parseInt(arg.split("=")[1]);
    } else if (arg === "--verbose") {
      result.verbose = true;
    } else if (arg === "--all-data") {
      result.allData = true;
    } else if (arg === "--help" || arg === "-h") {
      showUsage();
      process.exit(0);
    }
  }

  return result;
}

function showUsage() {
  console.log("Clean Agent Context Test Script");
  console.log("==============================");
  console.log("");
  console.log("Usage:");
  console.log("  node test-clean-agent-context.js [options]");
  console.log("");
  console.log("Options:");
  console.log("  --org=ID        Organization ID (default: 3)");
  console.log("  --shipment=ID   Specific shipment ID to test");
  console.log("  --verbose       Show detailed output");
  console.log("  --all-data      Include all data types (RNS, documents, compliance)");
  console.log("  --help, -h      Show this help message");
  console.log("");
  console.log("Examples:");
  console.log("  # Test with auto-selected shipment");
  console.log("  node test-clean-agent-context.js");
  console.log("");
  console.log("  # Test specific shipment with all data");
  console.log("  node test-clean-agent-context.js --shipment=123 --all-data");
}

// Find a suitable test shipment
async function findTestShipment(dataSource, organizationId) {
  const shipments = await dataSource.query(
    `
    SELECT s.id, s."hblNumber", s."customsStatus", s."transactionNumber",
           s."cargoControlNumber", s."createDate"
    FROM shipment s
    WHERE s."organizationId" = $1
      AND s."customsStatus" IS NOT NULL
    ORDER BY s."createDate" DESC
    LIMIT 5
  `,
    [organizationId]
  );

  if (shipments.length === 0) {
    throw new Error(`No shipments found for organization ${organizationId}`);
  }

  console.log(`Found ${shipments.length} test shipments:`);
  shipments.forEach((s, i) => {
    console.log(`  ${i + 1}. ID: ${s.id}, HBL: ${s.hblNumber || "N/A"}`);
    console.log(`     Status: ${s.customsStatus}, TN: ${s.transactionNumber || "N/A"}`);
  });
  console.log("");

  return shipments[0];
}

// Format object for markdown output
function formatObjectForMarkdown(obj, indent = 0) {
  const spaces = " ".repeat(indent);
  let result = "";

  for (const [key, value] of Object.entries(obj)) {
    if (value === null || value === undefined) {
      result += `${spaces}- **${key}**: null\n`;
    } else if (typeof value === "object" && !Array.isArray(value) && !(value instanceof Date)) {
      result += `${spaces}- **${key}**:\n`;
      result += formatObjectForMarkdown(value, indent + 2);
    } else if (Array.isArray(value)) {
      result += `${spaces}- **${key}**: [${value.length} items]\n`;
      if (value.length > 0 && typeof value[0] === "object") {
        value.slice(0, 3).forEach((item, i) => {
          result += `${spaces}  ${i + 1}. ${JSON.stringify(item)}\n`;
        });
        if (value.length > 3) {
          result += `${spaces}  ... and ${value.length - 3} more\n`;
        }
      }
    } else if (value instanceof Date) {
      result += `${spaces}- **${key}**: ${value.toISOString()}\n`;
    } else if (typeof value === "string" && value.length > 100) {
      result += `${spaces}- **${key}**: "${value.substring(0, 100)}..."\n`;
    } else {
      result += `${spaces}- **${key}**: ${JSON.stringify(value)}\n`;
    }
  }

  return result;
}

// Main test function
async function runTest(config) {
  console.log("🚀 Starting Clean Agent Context Test");
  console.log("====================================\n");

  let app;

  try {
    // Initialize NestJS application
    console.log("🔧 Initializing NestJS application...");
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: config.verbose ? ["log", "error", "warn", "debug"] : ["error"]
    });

    const dataSource = await app.get(DataSource);

    // Dynamic import to handle compiled code
    const { CleanAgentContextService } = await import(
      "../../../dist/clean-agent-context/clean-agent-context.service.js"
    );
    const cleanAgentContext = await app.get(CleanAgentContextService);

    console.log("✅ Application initialized\n");

    // Find or use specified shipment
    let shipment;
    if (config.shipmentId) {
      console.log(`📦 Using specified shipment: ${config.shipmentId}`);
      shipment = { id: config.shipmentId };
    } else {
      shipment = await findTestShipment(dataSource, config.organizationId);
      console.log(`📦 Auto-selected shipment: ${shipment.id}`);
    }

    console.log("\n🧪 Running Clean Agent Context Test (All Services)");
    console.log("");

    const startTime = Date.now();

    // Run the test
    let shipmentData;
    let documentData;
    let rnsData;
    let validationData;
    let shipmentError = null;
    let documentError = null;
    let rnsError = null;
    let validationError = null;

    try {
      // Create mock request for testing
      const mockRequest = createMockRequest(config.organizationId);

      // Test shipment data gathering
      console.log("📦 Testing shipment data gathering...");
      shipmentData = await cleanAgentContext.gatherShipmentData(
        shipment.id,
        config.organizationId,
        mockRequest
      );
      console.log("✅ Shipment data gathered successfully");

      // Test document data gathering
      console.log("📄 Testing document data gathering...");
      documentData = await cleanAgentContext.gatherDocumentData(
        shipment.id,
        config.organizationId,
        mockRequest
      );
      console.log("✅ Document data gathered successfully");

      // Test RNS data gathering (pass shipment data for context)
      console.log("🔄 Testing RNS data gathering...");
      rnsData = await cleanAgentContext.gatherRnsData(
        shipment.id,
        config.organizationId,
        mockRequest,
        shipmentData
      );
      console.log("✅ RNS data gathered successfully");

      // Test validation data gathering
      console.log("⚠️ Testing validation data gathering...");
      validationData = await cleanAgentContext.gatherValidationData(
        shipment.id,
        config.organizationId,
        mockRequest,
        shipmentData
      );
      console.log("✅ Validation data gathered successfully");
    } catch (err) {
      // Determine which service failed
      if (!shipmentData) {
        shipmentError = err;
        console.error("❌ Error gathering shipment data:", err.message);
      } else if (!documentData) {
        documentError = err;
        console.error("❌ Error gathering document data:", err.message);
      } else if (!rnsData) {
        rnsError = err;
        console.error("❌ Error gathering RNS data:", err.message);
      } else {
        validationError = err;
        console.error("❌ Error gathering validation data:", err.message);
      }

      if (config.verbose) {
        console.error(err.stack);
      }
    }

    const duration = Date.now() - startTime;
    console.log(`⏱️  Duration: ${duration}ms\n`);

    // Generate markdown report
    const reportPath = path.join(__dirname, "test-results.md");
    let report = `# Clean Agent Context Test Results\n\n`;
    report += `**Test Date**: ${new Date().toISOString()}\n`;
    report += `**Shipment ID**: ${shipment.id}\n`;
    report += `**Organization ID**: ${config.organizationId}\n`;
    report += `**Duration**: ${duration}ms\n`;
    const overallSuccess = !shipmentError && !documentError && !rnsError && !validationError;
    report += `**Status**: ${overallSuccess ? "✅ SUCCESS" : "❌ FAILED"}\n\n`;

    if (shipmentError || documentError || rnsError || validationError) {
      report += `## Error Details\n\n`;

      if (shipmentError) {
        report += `### Shipment Service Error\n`;
        report += `**Message**: ${shipmentError.message}\n\n`;
        report += `**Stack Trace**:\n\`\`\`\n${shipmentError.stack}\n\`\`\`\n\n`;
      }

      if (documentError) {
        report += `### Document Service Error\n`;
        report += `**Message**: ${documentError.message}\n\n`;
        report += `**Stack Trace**:\n\`\`\`\n${documentError.stack}\n\`\`\`\n\n`;
      }

      if (rnsError) {
        report += `### RNS Service Error\n`;
        report += `**Message**: ${rnsError.message}\n\n`;
        report += `**Stack Trace**:\n\`\`\`\n${rnsError.stack}\n\`\`\`\n\n`;
      }

      if (validationError) {
        report += `### Validation Service Error\n`;
        report += `**Message**: ${validationError.message}\n\n`;
        report += `**Stack Trace**:\n\`\`\`\n${validationError.stack}\n\`\`\`\n\n`;
      }
    }

    if (shipmentData) {
      report += `## Shipment Data Summary\n\n`;
      report += `### Basic Information\n`;
      report += `- **HBL Number**: ${shipmentData.HBL_NUMBER || "N/A"}\n`;
      report += `- **Transaction Number**: ${shipmentData.TRANSACTION_NUMBER || "N/A"}\n`;
      report += `- **CCN**: ${shipmentData.CARGO_CONTROL_NUMBER || "N/A"}\n`;
      report += `- **Port Code**: ${shipmentData.PORT_CODE || "N/A"}\n`;
      report += `- **Customs Status**: ${shipmentData.CUSTOMS_STATUS || "N/A"}\n`;
      report += `- **Importer**: ${shipmentData.IMPORTER_NAME || "N/A"}\n`;
      report += `- **Containers**: ${shipmentData.CONTAINER_NUMBERS || "None"}\n`;
      report += `\n`;

      report += `### Flags\n`;
      report += `- **Has Transaction Number**: ${shipmentData.HAS_TRANSACTION_NUMBER}\n`;
      report += `- **Has Containers**: ${shipmentData.HAS_CONTAINERS}\n`;
      report += `\n`;

      if (config.verbose) {
        report += `### Full Shipment Data\n\n`;
        report += `\`\`\`json\n${JSON.stringify(shipmentData, null, 2)}\n\`\`\`\n\n`;
      }
    }

    if (documentData) {
      report += `## Document Data Summary\n\n`;
      report += `### Document Information\n`;
      report += `- **Processed Documents**: ${documentData.PROCESSED_DOCUMENTS?.length || 0}\n`;
      report += `- **Show Validation Issues**: ${documentData.SHOW_VALIDATION_ISSUES}\n`;
      report += `- **CI/PL Missing**: ${documentData.CI_PL_MISSING}\n`;
      report += `- **HBL Missing**: ${documentData.HBL_MISSING}\n`;
      report += `- **AN/EMF Missing**: ${documentData.AN_EMF_MISSING}\n`;
      report += `\n`;

      if (documentData.PROCESSED_DOCUMENTS?.length > 0) {
        report += `### Document List\n`;
        documentData.PROCESSED_DOCUMENTS.slice(0, 5).forEach((doc, i) => {
          report += `${i + 1}. **${doc.filename}** (${doc.contentType}) - Status: ${doc.aggregationStatus}\n`;
        });
        if (documentData.PROCESSED_DOCUMENTS.length > 5) {
          report += `... and ${documentData.PROCESSED_DOCUMENTS.length - 5} more documents\n`;
        }
        report += `\n`;
      }

      if (config.verbose) {
        report += `### Full Document Data\n\n`;
        report += `\`\`\`json\n${JSON.stringify(documentData, null, 2)}\n\`\`\`\n\n`;
      }
    }

    if (rnsData) {
      report += `## RNS Data Summary\n\n`;
      report += `### RNS Information\n`;
      report += `- **Has RNS Data**: ${rnsData.HAS_RNS_DATA}\n`;
      report += `- **Show RNS Proof**: ${rnsData.SHOW_RNS_PROOF}\n`;
      report += `- **RNS Response Message**: ${rnsData.RNS_RESPONSE_MESSAGE}\n`;
      report += `- **Process Date**: ${rnsData.PROCESS_DATE}\n`;
      report += `- **Response Date**: ${rnsData.RESPONSE_DATE}\n`;
      report += `- **Processing Indicator**: ${rnsData.PROCESSING_INDICATOR}\n`;
      report += `- **Sublocation Code**: ${rnsData.SUBLOCATION_CODE}\n`;
      report += `\n`;

      if (config.verbose) {
        report += `### Full RNS Data\n\n`;
        report += `\`\`\`json\n${JSON.stringify(rnsData, null, 2)}\n\`\`\`\n\n`;
      }
    }

    if (validationData) {
      report += `## Validation Data Summary\n\n`;
      report += `### Validation Information\n`;
      report += `- **Show Validation Issues**: ${validationData.SHOW_VALIDATION_ISSUES}\n`;
      report += `- **Weight Missing**: ${validationData.WEIGHT_MISSING}\n`;
      report += `- **Port Code Missing**: ${validationData.PORT_CODE_MISSING}\n`;
      report += `- **CCN Missing**: ${validationData.CCN_MISSING}\n`;
      report += `- **OGD Filing Pending**: ${validationData.OGD_FILING_PENDING}\n`;
      report += `- **Show Details**: ${validationData.SHOW_DETAILS}\n`;
      report += `- **Show Compliance Errors**: ${validationData.SHOW_COMPLIANCE_ERRORS}\n`;
      report += `- **Validation Message**: ${validationData.VALIDATION_MESSAGE}\n`;
      report += `- **Missing Fields Count**: ${validationData.MISSING_FIELDS_COUNT}\n`;
      report += `- **Compliance Errors Count**: ${validationData.COMPLIANCE_ERRORS_COUNT}\n`;
      report += `\n`;

      if (config.verbose) {
        report += `### Full Validation Data\n\n`;
        report += `\`\`\`json\n${JSON.stringify(validationData, null, 2)}\n\`\`\`\n\n`;
      }
    }

    // Write report
    fs.writeFileSync(reportPath, report);
    console.log(`📄 Test results written to: ${reportPath}`);

    // Summary
    console.log("\n📊 Test Summary:");
    if (overallSuccess) {
      console.log("✅ Successfully gathered shipment, document, RNS, and validation data");

      if (shipmentData) {
        console.log("📦 Shipment Data:");
        console.log(`  📋 HBL Number: ${shipmentData.HBL_NUMBER || "None"}`);
        console.log(`  📦 Transaction Number: ${shipmentData.TRANSACTION_NUMBER || "None"}`);
        console.log(`  🏢 Importer: ${shipmentData.IMPORTER_NAME}`);
        console.log(`  📊 Customs Status: ${shipmentData.CUSTOMS_STATUS}`);
        console.log(`  📦 Has Containers: ${shipmentData.HAS_CONTAINERS ? "Yes" : "No"}`);
      }

      if (documentData) {
        console.log("📄 Document Data:");
        console.log(`  📁 Documents Found: ${documentData.PROCESSED_DOCUMENTS?.length || 0}`);
        console.log(`  ⚠️  Validation Issues: ${documentData.SHOW_VALIDATION_ISSUES ? "Yes" : "No"}`);
        console.log(`  📋 CI/PL Missing: ${documentData.CI_PL_MISSING ? "Yes" : "No"}`);
        console.log(`  📄 HBL Missing: ${documentData.HBL_MISSING ? "Yes" : "No"}`);
        console.log(`  📨 AN/EMF Missing: ${documentData.AN_EMF_MISSING ? "Yes" : "No"}`);
      }

      if (rnsData) {
        console.log("🔄 RNS Data:");
        console.log(`  📊 Has RNS Data: ${rnsData.HAS_RNS_DATA ? "Yes" : "No"}`);
        console.log(`  🔍 Show RNS Proof: ${rnsData.SHOW_RNS_PROOF ? "Yes" : "No"}`);
        console.log(`  📝 Response Message: ${rnsData.RNS_RESPONSE_MESSAGE}`);
        console.log(`  📅 Process Date: ${rnsData.PROCESS_DATE}`);
        console.log(`  🏷️  Processing Indicator: ${rnsData.PROCESSING_INDICATOR}`);
      }

      if (validationData) {
        console.log("⚠️ Validation Data:");
        console.log(`  📊 Has Validation Issues: ${validationData.SHOW_VALIDATION_ISSUES ? "Yes" : "No"}`);
        console.log(`  ⚖️  Weight Missing: ${validationData.WEIGHT_MISSING ? "Yes" : "No"}`);
        console.log(`  🏭 Port Code Missing: ${validationData.PORT_CODE_MISSING ? "Yes" : "No"}`);
        console.log(`  📦 CCN Missing: ${validationData.CCN_MISSING ? "Yes" : "No"}`);
        console.log(`  🏛️  OGD Filing Pending: ${validationData.OGD_FILING_PENDING ? "Yes" : "No"}`);
        console.log(`  📝 Validation Message: ${validationData.VALIDATION_MESSAGE}`);
        console.log(`  📊 Missing Fields: ${validationData.MISSING_FIELDS_COUNT}`);
        console.log(`  ❌ Compliance Errors: ${validationData.COMPLIANCE_ERRORS_COUNT}`);
      }
    } else {
      console.log("❌ Test failed:");
      if (shipmentError) {
        console.log("  📦 Shipment service failed - see report for details");
      }
      if (documentError) {
        console.log("  📄 Document service failed - see report for details");
      }
      if (rnsError) {
        console.log("  🔄 RNS service failed - see report for details");
      }
      if (validationError) {
        console.log("  ⚠️ Validation service failed - see report for details");
      }
    }
  } catch (error) {
    console.error("💥 Fatal error:", error.message);
    if (config.verbose) {
      console.error(error.stack);
    }
  } finally {
    if (app) {
      await app.close();
    }
  }
}

// Entry point
const config = parseArgs(process.argv);

// Handle termination gracefully
process.on("SIGINT", () => {
  console.log("\nShutting down...");
  process.exit(0);
});

// Run the test
runTest(config)
  .then(() => {
    console.log("\n✨ Test completed");
    process.exit(0);
  })
  .catch((err) => {
    console.error("💥 Unexpected error:", err);
    process.exit(1);
  });
