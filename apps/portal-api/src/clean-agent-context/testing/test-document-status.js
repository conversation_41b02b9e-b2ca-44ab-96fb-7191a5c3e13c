#!/usr/bin/env node

/**
 * Test script for DocumentStatusDeterminer
 * 
 * Tests the clean document status determination logic
 * Usage: node test-document-status.js [shipmentId]
 */

const { execSync } = require('child_process');
const path = require('path');

// Default to a shipment ID if none provided
const shipmentId = process.argv[2] || '1';

console.log(`🧪 Testing Document Status Determination for Shipment ${shipmentId}`);
console.log('===========================================================');

try {
  // Test the DocumentStatusDeterminer service
  const testCode = `
    import { NestFactory } from '@nestjs/core';
    import { AppModule } from '../app.module';
    import { DocumentStatusDeterminer } from '../clean-agent-context/document-status-determiner.service';
    import { ShipmentService } from '../shipment/services/shipment.service';
    import { generateRequest } from '../email/utils/generate-request';
    import { ContextIdFactory, REQUEST } from '@nestjs/core';
    
    async function testDocumentStatus() {
      const app = await NestFactory.createApplicationContext(AppModule);
      
      try {
        // Get organization first (using organization ID 1 for test)
        const contextId = ContextIdFactory.create();
        app.registerRequestByContextId(generateRequest(null, { id: 1 }), contextId);
        
        // Resolve services
        const shipmentService = await app.resolve(ShipmentService, contextId);
        const documentStatusDeterminer = app.get(DocumentStatusDeterminer);
        
        // Get shipment
        const shipment = await shipmentService.getShipmentById(${shipmentId});
        if (!shipment) {
          console.log('❌ Shipment not found');
          return;
        }
        
        console.log('📋 Shipment Info:');
        console.log(\`   ID: \${shipment.id}\`);
        console.log(\`   HBL: \${shipment.hblNumber || 'N/A'}\`);
        console.log(\`   CCN: \${shipment.cargoControlNumber || 'N/A'}\`);
        console.log(\`   Mode: \${shipment.modeOfTransport || 'N/A'}\`);
        console.log(\`   Status: \${shipment.customsStatus || 'N/A'}\`);
        console.log('');
        
        // Test document status determination
        const documentStatus = await documentStatusDeterminer.determineDocumentStatus(shipment);
        
        console.log('📄 Document Status Results:');
        console.log(\`   HBL Received: \${documentStatus.hblReceived}\`);
        console.log(\`   AN/EMF Received: \${documentStatus.anEmfReceived}\`);
        console.log(\`   CI Received: \${documentStatus.ciReceived}\`);
        console.log(\`   PL Received: \${documentStatus.plReceived}\`);
        console.log('');
        
        // Format for template
        const templateFormat = documentStatusDeterminer.formatDocumentStatusForTemplate(documentStatus);
        console.log('📝 Template Format (matches stakeholder guidance):');
        console.log(\`   HBL: \${templateFormat.hbl}\`);
        console.log(\`   AN/EMF: \${templateFormat.anEmf}\`);
        console.log(\`   CI & PL: \${templateFormat.ciPl}\`);
        
        console.log('');
        console.log('✅ Document status determination test completed successfully!');
        
      } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.stack) {
          console.error(error.stack);
        }
      } finally {
        await app.close();
      }
    }
    
    testDocumentStatus();
  `;

  // Write test file
  const testFile = path.join(__dirname, 'temp-test.mjs');
  require('fs').writeFileSync(testFile, testCode);

  // Run the test
  const result = execSync(`cd ${path.dirname(__dirname)} && node --loader ts-node/esm ${testFile}`, {
    encoding: 'utf8',
    stdio: 'pipe'
  });

  console.log(result);

  // Cleanup
  require('fs').unlinkSync(testFile);

} catch (error) {
  console.error('❌ Test execution failed:', error.message);
  if (error.stdout) {
    console.log('STDOUT:', error.stdout);
  }
  if (error.stderr) {
    console.error('STDERR:', error.stderr);
  }
}