import { Injectable, Inject, Scope } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { AuthenticatedRequest } from "nest-modules";
import { CandataService } from "nest-modules";
import { RnsTemplateData } from "./clean-agent-context.types";

/**
 * Candata Data Gatherer - REQUEST-scoped processor
 *
 * This service acts as a processor that can be resolved by the
 * CleanAgentContextService with proper request context for accessing
 * the CandataService (which is REQUEST-scoped and needs organization context).
 * This mirrors the pattern used in ShipmentDataGatherer.
 */
@Injectable({ scope: Scope.REQUEST })
export class CandataDataGatherer {
  constructor(
    @Inject(REQUEST) private readonly request: AuthenticatedRequest,
    private readonly candataService: CandataService
  ) {}

  /**
   * Gather RNS template data
   * @param params - Parameters for data gathering
   * @returns RNS template data ready for use
   */
  async gather(params: {
    transactionNumber?: string;
    cargoControlNumber?: string;
    shipment?: any;
  }): Promise<RnsTemplateData> {
    const { transactionNumber, cargoControlNumber, shipment } = params;

    // Try to get RNS data using available identifiers
    let rnsData: any[] = [];

    // First try with transaction number if available
    if (transactionNumber && this.isValidTransactionNumber(transactionNumber)) {
      try {
        rnsData = await this.candataService.findRnsResponseByTransactionNumbers([transactionNumber]);
      } catch (error) {
        console.warn(`Failed to get RNS data by transaction number: ${error.message}`);
      }
    }

    // Fall back to cargo control number if no RNS data and CCN is available
    if (rnsData.length === 0 && cargoControlNumber && this.isValidCargoControlNumber(cargoControlNumber)) {
      try {
        rnsData = await this.candataService.findRnsResponseByCargoControlNumbers([cargoControlNumber]);
      } catch (error) {
        console.warn(`Failed to get RNS data by cargo control number: ${error.message}`);
      }
    }

    // Transform to template format
    const templateData: RnsTemplateData = {
      HAS_RNS_DATA: rnsData.length > 0,
      SHOW_RNS_PROOF: rnsData.length > 0,

      // Use first RNS response if available, otherwise use fallback data
      RNS_RESPONSE_MESSAGE: this.getRnsResponseMessage(rnsData),
      PROCESS_DATE: this.formatDate(rnsData[0]?.processDate),
      RESPONSE_DATE: this.formatDate(rnsData[0]?.responseDate),
      PROCESSING_INDICATOR: rnsData[0]?.processingIndicator || "Unknown",
      SUBLOCATION_CODE: rnsData[0]?.sublocation || shipment?.subLocation || "Unknown",

      // Include shipment-related data for template consistency
      TRANSACTION_NUMBER: transactionNumber || shipment?.transactionNumber || "",
      CARGO_CONTROL_NUMBER: cargoControlNumber || shipment?.cargoControlNumber || "",
      PORT_CODE: rnsData[0]?.port || shipment?.portCode || "",
      CONTAINER_NUMBERS: this.formatContainerNumbers(rnsData[0]?.containers, shipment)
    };

    return templateData;
  }

  /**
   * Validate transaction number format (14 digits)
   */
  private isValidTransactionNumber(transactionNumber: string): boolean {
    return /^[0-9]{14}$/.test(transactionNumber);
  }

  /**
   * Validate cargo control number format
   */
  private isValidCargoControlNumber(cargoControlNumber: string): boolean {
    return /^[0-9A-Z]{3}(-|[0-9A-Z])[0-9A-Z]{1,21}$/.test(cargoControlNumber);
  }

  /**
   * Get RNS response message from RNS data
   */
  private getRnsResponseMessage(rnsData: any[]): string {
    if (rnsData.length === 0) {
      return "No RNS data available";
    }

    const rns = rnsData[0];

    // Prioritize different message fields
    if (rns.message) return rns.message;
    if (rns.documentMessage) return rns.documentMessage;
    if (rns.rejectReasons) return `Rejected: ${rns.rejectReasons}`;

    return "RNS response received";
  }

  /**
   * Format date for display
   */
  private formatDate(dateString?: string | null): string {
    if (!dateString) return "Unknown";

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-CA"); // YYYY-MM-DD format
    } catch {
      return dateString;
    }
  }

  /**
   * Format container numbers for display
   */
  private formatContainerNumbers(rnsContainers?: string | null, shipment?: any): string {
    // First try RNS container data
    if (rnsContainers) {
      return rnsContainers;
    }

    // Fall back to shipment container data
    if (shipment?.containers && shipment.containers.length > 0) {
      return (
        shipment.containers
          .map((c: any) => c.containerNumber)
          .filter(Boolean)
          .join(", ") || "Unknown containers"
      );
    }

    // Single container from shipment
    if (shipment?.containerNumber) {
      return shipment.containerNumber;
    }

    return "No containers";
  }
}
