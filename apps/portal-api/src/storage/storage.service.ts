import {
  ChecksumAlgorithm,
  CopyObjectCommand,
  CopyObjectCommandInput,
  CopyObjectCommandOutput,
  DeleteObjectCommand,
  DeleteObjectCommandInput,
  DeleteObjectCommandOutput,
  GetObjectAttributesCommand,
  GetObjectAttributesCommandInput,
  GetObjectAttributesCommandOutput,
  GetObjectCommand,
  GetObjectCommandInput,
  GetObjectCommandOutput,
  HeadObjectCommand,
  HeadObjectCommandInput,
  ObjectAttributes,
  PutObjectCommand,
  PutObjectCommandInput,
  PutObjectCommandOutput,
  S3Client,
  S3ClientConfig
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { RequestPresigningArguments } from "@aws-sdk/types";
import { Inject, Injectable, Logger, Scope } from "@nestjs/common";
import { NodeJsClient } from "@smithy/types";
import fs from "fs";
import mime from "mime-types";
import { Readable } from "stream";
import { StorageModuleOptions } from "./storage.module";

@Injectable({ scope: Scope.DEFAULT })
export class StorageService {
  private readonly logger = new Logger(StorageService.name);
  private readonly client: NodeJsClient<S3Client>;

  constructor(
    @Inject("STORAGE_MODULE_OPTIONS")
    private readonly options: StorageModuleOptions
  ) {
    const config: S3ClientConfig = {
      region: options.awsRegion,
      endpoint: options.awsS3Endpoint,
      forcePathStyle: options.awsS3ForcePathStyle
    };

    if (options.awsAccessKey && options.awsSecretKey) {
      config.credentials = {
        accessKeyId: options.awsAccessKey,
        secretAccessKey: options.awsSecretKey
      };
    }

    this.client = new S3Client(config) as NodeJsClient<S3Client>;
  }

  /**
   * Get the bucket name
   *
   * @returns The name of the bucket
   */
  getBucket(): string {
    return this.options.awsS3Bucket;
  }

  /**
   * Upload a file to S3
   *
   * @param file The file to upload
   * @param path The path to the file
   * @returns The result of the upload
   */
  async uploadFile(
    file: Express.Multer.File,
    path: string,
    options?: PutObjectCommandInput
  ): Promise<PutObjectCommandOutput> {
    const command = new PutObjectCommand({
      Bucket: this.options.awsS3Bucket,
      Key: path,
      Body: file.buffer,
      ContentType: file.mimetype,
      ChecksumAlgorithm: ChecksumAlgorithm.SHA256,
      ...options
    });

    return this.client.send(command);
  }

  async uploadLocalFileToS3(
    localPath: string,
    path: string,
    options?: PutObjectCommandInput
  ): Promise<PutObjectCommandOutput> {
    try {
      if (!fs.existsSync(localPath)) {
        throw new Error("File does not exist");
      }

      const fileBuffer = fs.readFileSync(localPath);
      const contentType = mime.lookup(localPath) || "application/octet-stream";

      const command = new PutObjectCommand({
        Bucket: this.options.awsS3Bucket,
        Key: path,
        Body: fileBuffer,
        ContentType: contentType,
        ...options
      });

      return this.client.send(command);
    } catch (error) {
      this.logger.error(`Failed to upload file to S3: ${error}`);
      throw error;
    }
  }

  async copyFile(
    sourceKey: string,
    destinationKey: string,
    sourceBucket?: string,
    destinationBucket?: string,
    options?: Omit<CopyObjectCommandInput, "Bucket" | "CopySource" | "Key">
  ): Promise<void> {
    const sourceBucketName = sourceBucket || this.options.awsS3Bucket;
    const destinationBucketName = destinationBucket || this.options.awsS3Bucket;

    if (sourceBucketName === destinationBucketName && sourceKey === destinationKey) {
      return;
    }

    try {
      const command = new CopyObjectCommand({
        Bucket: destinationBucketName,
        CopySource: `${sourceBucketName}/${sourceKey}`,
        Key: destinationKey,
        ...options
      });

      await this.client.send(command);
    } catch (error) {
      throw new Error("Cannot copy file", { cause: error });
    }
  }

  /**
   * Move a file from one path to another
   *
   * @param sourceKey The path of the source file
   * @param destinationKey The path of the destination file
   * @param sourceBucket The bucket of the source file
   * @param destinationBucket The bucket of the destination file
   * @param options The options for the copy operation
   * @returns The result of the move operation
   */
  async moveFile(
    sourceKey: string,
    destinationKey: string,
    sourceBucket?: string,
    destinationBucket?: string,
    options?: Omit<CopyObjectCommandInput, "Bucket" | "CopySource" | "Key">
  ): Promise<void> {
    const sourceBucketName = sourceBucket || this.options.awsS3Bucket;
    const destinationBucketName = destinationBucket || this.options.awsS3Bucket;

    try {
      await this.copyFile(sourceKey, destinationKey, sourceBucketName, destinationBucketName, options);
      await this.deleteFile(sourceKey, { Bucket: sourceBucketName });
    } catch (error) {
      throw new Error("Cannot move file", { cause: error });
    }
  }

  async deleteFile(path: string, options?: Omit<DeleteObjectCommandInput, "Key">): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.options.awsS3Bucket,
        Key: path,
        ...options
      });

      await this.client.send(command);
    } catch (error) {
      throw new Error("Cannot delete file", { cause: error });
    }
  }

  /**
   * Get a file from S3
   *
   * @param path The path to the file
   * @returns The file
   */
  async getFile(path: string, options?: GetObjectCommandInput): Promise<GetObjectCommandOutput> {
    const command = new GetObjectCommand({
      Bucket: this.options.awsS3Bucket,
      Key: path,
      ...options
    });

    return this.client.send(command);
  }

  /**
   * Check if a file exists
   *
   * @param path The path to the file
   * @returns Whether the file exists
   */
  async isFileExists(path: string, options?: HeadObjectCommandInput): Promise<boolean> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.options.awsS3Bucket,
        Key: path,
        ...options
      });
      await this.client.send(command);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get a stream from S3
   *
   * @param path The path to the file
   * @returns The stream
   */
  async getFileStream(path: string, options?: GetObjectCommandInput): Promise<Readable> {
    const response = await this.getFile(path, options);

    if (!response.Body) {
      throw new Error("No body found in response");
    }

    return response.Body as Readable;
  }

  /**
   * Get a blob from S3
   *
   * @param path The path to the file
   * @returns The blob
   */
  async getFileBlob(path: string, options?: GetObjectCommandInput): Promise<Blob> {
    const response = await this.getFile(path, options);

    const type = response.ContentType;

    if (!response.Body) {
      throw new Error("No body found in response");
    }

    if (!type) {
      throw new Error("No content type found in response");
    }

    return new Blob([await response.Body.transformToByteArray()], { type });
  }

  /**
   * Get a signed URL from S3 to download/upload a file
   *
   * @param path The path to the file
   * @returns The signed URL
   */
  async getSignedUrl(
    path: string,
    options: { name?: string } & RequestPresigningArguments = {
      expiresIn: 15 * 60 // 15 minutes
    },
    getObjectOptions?: GetObjectCommandInput
  ): Promise<string> {
    const opts: GetObjectCommandInput = {
      Bucket: this.options.awsS3Bucket,
      Key: path,
      ...getObjectOptions
    };

    if (options.name) {
      opts.ResponseContentDisposition = `filename="${encodeURIComponent(options.name)}"`;
    }

    const command = new GetObjectCommand(opts);

    return getSignedUrl(this.client, command, options);
  }

  /**
   * Get the SHA-256 hash of a file as a base64 encoded string
   *
   * @param path The path to the file
   * @returns The SHA-256 hash of the file as a base64 encoded string
   */
  async getFileHash(path: string): Promise<string> {
    const attributes = await this.getFileAttributes(path, [ObjectAttributes.CHECKSUM]);
    return attributes.Checksum?.ChecksumSHA256;
  }

  /**
   * Get the SHA-256 hash of a file as a hex string
   *
   * @param path The path to the file
   * @returns The SHA-256 hash of the file as a hex string
   */
  async getFileHashHex(path: string): Promise<string> {
    const hash = await this.getFileHash(path);
    return Buffer.from(hash, "base64").toString("hex");
  }

  /**
   * Get the attributes of a file
   *
   * @param path The path to the file
   * @returns The attributes of the file
   * @see https://docs.aws.amazon.com/AmazonS3/latest/API/API_GetObjectAttributes.html
   */
  async getFileAttributes(
    path: string,
    attributes: ObjectAttributes[] = [],
    options?: GetObjectAttributesCommandInput
  ): Promise<GetObjectAttributesCommandOutput> {
    const command = new GetObjectAttributesCommand({
      Bucket: this.options.awsS3Bucket,
      Key: path,
      ObjectAttributes: attributes,
      ...options
    });
    return this.client.send(command);
  }
}
