import fs from "fs";
import os from "os";
import path from "path";

export async function useTempDir(prefix: string, handler: (tmpDir: string) => Promise<any>) {
  const tmpDir = fs.mkdtempSync(path.join(os.tmpdir(), prefix));
  let result;

  try {
    result = await handler(tmpDir);
  } finally {
    try {
      fs.rmSync(tmpDir, { recursive: true });
    } catch (error) {
      console.error(`Error deleting temp directory ${tmpDir}:`, error);
    }
  }

  return result;
}
