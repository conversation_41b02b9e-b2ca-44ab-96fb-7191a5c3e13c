import { DynamicModule, Global, Module } from "@nestjs/common";
import { StorageService } from "./storage.service";

export interface StorageModuleOptions {
  awsS3Bucket: string;
  awsRegion: string;
  awsAccessKey?: string;
  awsSecretKey?: string;
  awsS3Endpoint?: string;
  awsS3ForcePathStyle?: boolean;
}

@Global()
@Module({})
export class StorageModule {
  static forRoot(options: StorageModuleOptions): DynamicModule {
    return {
      module: StorageModule,
      providers: [
        {
          provide: "STORAGE_MODULE_OPTIONS",
          useValue: options
        },
        StorageService
      ],
      exports: [StorageService]
    };
  }
}
