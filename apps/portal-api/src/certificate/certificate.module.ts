import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CertificateOfOrigin } from "nest-modules";
import { CertificateController } from "./certificate.controller";
import { CertificateService } from "./certificate.service";

@Module({
  imports: [TypeOrmModule.forFeature([CertificateOfOrigin])],
  providers: [CertificateService],
  controllers: [CertificateController]
})
export class CertificateModule {}
