import { Controller, Get, NotFoundException, Param, ParseIntPipe, Query, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiGetByIdResponses,
  ApiGetManyResponses,
  CertificateOfOrigin,
  GetCertificatesOfOriginDto,
  GetCertificatesOfOriginResponseDto
} from "nest-modules";
import { CertificateService } from "./certificate.service";

@ApiTags("Certificate API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("certificates")
export class CertificateController {
  constructor(private readonly certificateService: CertificateService) {}

  @Get()
  @ApiOperation({ summary: "Get all Certificates of Origin" })
  @ApiGetManyResponses({ type: GetCertificatesOfOriginResponseDto })
  async getCertificatesOfOrigin(
    @Query() getCertificatesDto: GetCertificatesOfOriginDto
  ): Promise<GetCertificatesOfOriginResponseDto> {
    return await this.certificateService.getCertificatesOfOrigin(getCertificatesDto);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get Certificate of Origin by ID" })
  @ApiGetByIdResponses({ type: CertificateOfOrigin })
  async getCertificateOfOrigin(@Param("id", ParseIntPipe) id: number): Promise<CertificateOfOrigin> {
    const certificate = await this.certificateService.getCertificateOfOrigin(id);
    if (!certificate) throw new NotFoundException("Certificate of Origin not found");
    return certificate;
  }
}
