import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import {
  CertificateOfOrigin,
  FIND_CERTIFICATE_OF_ORIGIN_RELATIONS,
  GetCertificatesOfOriginDto,
  GetCertificatesOfOriginResponseDto,
  getFindOptions,
  UserPermission
} from "nest-modules";
import { ClsService } from "nestjs-cls";
import { Raw, Repository } from "typeorm";

@Injectable()
export class CertificateService {
  constructor(
    @InjectRepository(CertificateOfOrigin)
    private readonly certificateRepository: Repository<CertificateOfOrigin>,
    private readonly cls: ClsService
  ) {}

  async getCertificateOfOrigin(id: number): Promise<CertificateOfOrigin> {
    return this.certificateRepository.findOne({
      where: { id },
      relations: FIND_CERTIFICATE_OF_ORIGIN_RELATIONS
    });
  }

  async getCertificatesOfOrigin(
    getCertificatesDto: GetCertificatesOfOriginDto
  ): Promise<GetCertificatesOfOriginResponseDto> {
    const { organizationId, isValid, ...dto } = getCertificatesDto;
    const { where, order, skip, take } = getFindOptions<CertificateOfOrigin>(dto, [], [], "id");

    const userPermission = this.cls.get("USER_PERMISSION");
    const userOrganizationId = this.cls.get("ORGANIZATION_ID");
    // Handle organization filtering based on user permissions
    if (userPermission !== UserPermission.BACKOFFICE_ADMIN) {
      where.organization = {
        id: Raw((alias) => `${alias} = :organizationId OR ${alias} IS NULL`, {
          organizationId: userOrganizationId || -1
        })
      };
    } else if (organizationId) {
      where.organization = {
        id: organizationId
      };
    }

    const query = this.certificateRepository.createQueryBuilder("certificate").setFindOptions({
      where,
      relations: FIND_CERTIFICATE_OF_ORIGIN_RELATIONS,
      order,
      skip,
      take
    });

    // validity
    if (isValid !== undefined) {
      const validWhere = {
        validTo: Raw((alias) => `${alias} >= NOW()`),
        validFrom: Raw((alias) => `${alias} <= NOW()`)
      };

      const invalidWhere = [
        {
          validTo: Raw((alias) => `${alias} < NOW()`)
        },
        {
          validFrom: Raw((alias) => `${alias} > NOW()`)
        }
      ];
      query.andWhere(isValid ? validWhere : invalidWhere);
    }

    const [certificates, total] = await query.getManyAndCount();

    return {
      certificates,
      total,
      skip,
      limit: take
    };
  }
}
