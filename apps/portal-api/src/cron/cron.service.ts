import { InjectQueue } from "@nestjs/bullmq";
import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { Cron, SchedulerRegistry } from "@nestjs/schedule";
import { Queue } from "bullmq";
import { createHash } from "crypto";
import moment from "moment-timezone";
import { CRON_QUEUE_NAME } from "nest-modules";
import { CronJobName } from "./types/cron.types";
import { NODE_ENV } from "@/app.module";
import { NodeEnv } from "nest-modules";

@Injectable()
export class CronService {
  constructor(
    @InjectQueue(CRON_QUEUE_NAME)
    private readonly cronjobsQueue: Queue,
    private readonly scheduleRegistry: SchedulerRegistry,
    private readonly configService: ConfigService
  ) {}
  private readonly logger = new Logger(CronService.name);
  static readonly TORONTO_TIMEZONE = "America/Toronto";

  private generateQueueJobId(jobName: string, executionDate: Date) {
    const unhashedId = `${jobName}-${moment.tz(executionDate, CronService.TORONTO_TIMEZONE).utc().format("YYYY-MM-DDTHH:mm:ss")}`;
    const hashedId = createHash("md5").update(unhashedId).digest("hex");
    this.logger.log(`Unhashed ID: ${unhashedId}, Hashed ID: ${hashedId}`);
    return hashedId;
  }

  private getCurrentExecutionDate(cronjobName: string) {
    const cronjob = this.scheduleRegistry.getCronJob(cronjobName);
    if (!cronjob || !cronjob.lastDate()) {
      this.logger.error(`Cronjob ${cronjobName} last execution date not found, setting to current datetime`);
      return new Date();
    }
    return cronjob.lastDate();
  }

  private async addCronJobToQueue(cronJobName: CronJobName) {
    const executionDate = this.getCurrentExecutionDate(cronJobName);
    const jobId = this.generateQueueJobId(cronJobName, executionDate);
    this.logger.log(`Function: ${cronJobName}, Job ID: ${jobId}`);
    return await this.cronjobsQueue.add(
      jobId,
      {
        jobName: cronJobName,
        jobId,
        executionDate
      },
      {
        deduplication: {
          id: jobId
        }
      }
    );
  }

  @Cron("*/5 * * * *", {
    name: CronJobName.SYNC_EMAILS,
    timeZone: CronService.TORONTO_TIMEZONE
  })
  async syncEmails() {
    if (
      this.configService.get<string>("DISABLE_INCOMING_EMAIL_SYNC") === "true" &&
      NODE_ENV === NodeEnv.LOCAL
    ) {
      this.logger.log(
        `Incoming email sync (cron job: ${CronJobName.SYNC_EMAILS}) is disabled via DISABLE_INCOMING_EMAIL_SYNC environment variable.`
      );
      return;
    }

    await this.addCronJobToQueue(CronJobName.SYNC_EMAILS);
  }

  @Cron("0 9 * * *", {
    name: CronJobName.CARRIER_TRACKING,
    timeZone: CronService.TORONTO_TIMEZONE
  })
  @Cron("0 17 * * *", {
    name: CronJobName.CARRIER_TRACKING,
    timeZone: CronService.TORONTO_TIMEZONE
  })
  async carrierTracking() {
    await this.addCronJobToQueue(CronJobName.CARRIER_TRACKING);
  }

  @Cron("30 9 * * *", {
    name: CronJobName.PORT_TRACKING,
    timeZone: CronService.TORONTO_TIMEZONE
  })
  @Cron("30 17 * * *", {
    name: CronJobName.PORT_TRACKING,
    timeZone: CronService.TORONTO_TIMEZONE
  })
  async portTracking() {
    await this.addCronJobToQueue(CronJobName.PORT_TRACKING);
  }

  @Cron("0 */3 * * *", {
    name: CronJobName.RAIL_TRACKING,
    timeZone: CronService.TORONTO_TIMEZONE
  })
  async railTracking() {
    await this.addCronJobToQueue(CronJobName.RAIL_TRACKING);
  }

  @Cron("5 9 * * *", {
    name: CronJobName.PICKUP_RETURN_TRACKING,
    timeZone: CronService.TORONTO_TIMEZONE
  })
  @Cron("5 17 * * *", {
    name: CronJobName.PICKUP_RETURN_TRACKING,
    timeZone: CronService.TORONTO_TIMEZONE
  })
  async pickupReturnTracking() {
    await this.addCronJobToQueue(CronJobName.PICKUP_RETURN_TRACKING);
  }

  @Cron("*/30 * * * *", {
    name: CronJobName.CUSTOMS_STATUS_CHECK,
    timeZone: CronService.TORONTO_TIMEZONE
  })
  async customsStatusCheck() {
    if (
      this.configService.get<string>("DISABLE_CUSTOMS_STATUS_CRON_JOB_LOCALLY") === "true" &&
      NODE_ENV === NodeEnv.LOCAL
    ) {
      this.logger.log(
        `Customs status check (cron job: ${CronJobName.CUSTOMS_STATUS_CHECK}) is disabled via DISABLE_CUSTOMS_STATUS_CRON_JOB_LOCALLY environment variable.`
      );
      return;
    }
    await this.addCronJobToQueue(CronJobName.CUSTOMS_STATUS_CHECK);
  }

  @Cron("0 8 * * *", {
    name: CronJobName.CLEAN_UP_TEMPORARY_PRODUCTS,
    timeZone: CronService.TORONTO_TIMEZONE
  })
  async cleanUpTemporaryProducts() {
    await this.addCronJobToQueue(CronJobName.CLEAN_UP_TEMPORARY_PRODUCTS);
  }

  @Cron("0 10-18/4 * * *", {
    name: CronJobName.CHECK_CARM_STATUS,
    timeZone: CronService.TORONTO_TIMEZONE
  })
  async checkCarmStatus() {
    await this.addCronJobToQueue(CronJobName.CHECK_CARM_STATUS);
  }

  @Cron("0 9 * * *", {
    name: CronJobName.SHIPMENT_ACCOUNTING_CHECK,
    timeZone: CronService.TORONTO_TIMEZONE
  })
  async shipmentAccountingCheck() {
    await this.addCronJobToQueue(CronJobName.SHIPMENT_ACCOUNTING_CHECK);
  }

  @Cron("0 0 * * *", {
    name: CronJobName.CLEAN_UP_MATCHING_RULES,
    timeZone: CronService.TORONTO_TIMEZONE
  })
  async cleanUpMatchingRules() {
    await this.addCronJobToQueue(CronJobName.CLEAN_UP_MATCHING_RULES);
  }
}
