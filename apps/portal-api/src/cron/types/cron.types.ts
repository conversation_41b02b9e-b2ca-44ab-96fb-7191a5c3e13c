import { Job } from "bullmq";

export enum CronJobName {
  SYNC_EMAILS = "syncEmails",
  CARRIER_TRACKING = "carrierTracking",
  PORT_TRACKING = "portTracking",
  RAIL_TRACKING = "railTracking",
  PICKUP_RETURN_TRACKING = "pickupReturnTracking",
  CUSTOMS_STATUS_CHECK = "customsStatusCheck",
  CLEAN_UP_TEMPORARY_PRODUCTS = "cleanUpTemporaryProducts",
  CHECK_CARM_STATUS = "checkCarmStatus",
  SHIPMENT_ACCOUNTING_CHECK = "shipmentAccountingCheck",
  CLEAN_UP_MATCHING_RULES = "cleanUpMatchingRules"
}

export interface CronJobData {
  jobName: string;
  jobId: string;
  executionDate: Date;
}

export type CronJob = Job<CronJobData, null, string>;
