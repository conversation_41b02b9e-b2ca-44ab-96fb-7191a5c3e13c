import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Logger } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { CRON_QUEUE_NAME, MatchingRuleEvent } from "nest-modules";
import { EmailEvent } from "src/email/types/event.types";
import { CronJob, CronJobName } from "./types/cron.types";
import { ShipmentEvent } from "@/shipment/types/event.types";
import { ProductEvent } from "@/product/events";
import { OnboardingEvent } from "@/onboarding/types/onboarding.types";

@Processor({
  name: CRON_QUEUE_NAME
})
export class CronConsumer extends WorkerHost {
  constructor(private eventEmitter: EventEmitter2) {
    super();
  }
  private readonly logger = new Logger(CronConsumer.name);

  async process(job: CronJob) {
    this.logger.log(`Processing job ${job.id}, data: ${job.data}`);
    switch (job.data.jobName) {
      case CronJobName.CLEAN_UP_MATCHING_RULES:
        this.eventEmitter.emit(MatchingRuleEvent.MATCHING_RULE_CLEAN_UP);
        break;
      case CronJobName.SYNC_EMAILS:
        this.eventEmitter.emit(EmailEvent.EMAIL_SYNC);
        break;
      case CronJobName.CUSTOMS_STATUS_CHECK:
        this.eventEmitter.emit(ShipmentEvent.CUSTOMS_STATUS_CHECK);
        break;
      case CronJobName.CLEAN_UP_TEMPORARY_PRODUCTS:
        this.eventEmitter.emit(ProductEvent.PRODUCT_CLEAN_UP);
        break;
      case CronJobName.CHECK_CARM_STATUS:
        this.eventEmitter.emit(OnboardingEvent.CHECK_CARM_STATUS);
        break;
      case CronJobName.SHIPMENT_ACCOUNTING_CHECK:
        this.eventEmitter.emit(ShipmentEvent.ACCOUNTING_CHECK);
        break;
      // TODO: Uncomment these when shipment tracking is ready
      // case CronJobName.CARRIER_TRACKING:
      //   this.eventEmitter.emit(TrackingEvent.CARRIER_TRACKING_SCHEDULED_RUN);
      //   break;
      // case CronJobName.PORT_TRACKING:
      //   this.eventEmitter.emit(TrackingEvent.PORT_TRACKING_SCHEDULED_RUN);
      //   break;
      // case CronJobName.RAIL_TRACKING:
      //   this.eventEmitter.emit(TrackingEvent.RAIL_TRACKING_SCHEDULED_RUN);
      //   break;
      // case CronJobName.PICKUP_RETURN_TRACKING:
      //   this.eventEmitter.emit(TrackingEvent.PICKUP_RETURN_TRACKING_SCHEDULED_RUN);
      //   break;
      default:
        this.logger.warn(`Unknown cron job: ${job.data.jobName}, skipping...`);
    }
    return null;
  }
}
