import { BullModule } from "@nestjs/bullmq";
import { DynamicModule, Logger, Module } from "@nestjs/common";
import { CRON_QUEUE_NAME } from "nest-modules";
import { CronConsumer } from "./cron.processor";
import { CronService } from "./cron.service";

@Module({})
export class CronModule {
  static register(): DynamicModule {
    if ((!process.env.REDIS_HOST && !process.env.REDIS_PORT) || process.env.FEATURE) {
      Logger.warn("Redis not configured or worker feature is set, skipping registering module", "CronModule");
      return {
        module: CronModule
      };
    }

    return {
      module: CronModule,
      imports: [
        BullModule.registerQueue({
          name: CRON_QUEUE_NAME,
          defaultJobOptions: {
            removeOnComplete: {
              age: 3600,
              count: 1000
            },
            removeOnFail: {
              age: 24 * 3600
            }
          }
        })
      ],
      providers: [CronService, CronConsumer],
      exports: [CronService]
    };
  }
}
