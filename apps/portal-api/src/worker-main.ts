import { Logger } from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import { AppModule } from "./app.module";
import { CronJobName } from "./cron/types/cron.types";
import { CustomStatusService } from "./shipment/services/custom-status.service";

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule); // no HTTP server
  const feature = process.env.FEATURE;

  async function getService() {
    switch (feature) {
      case CronJobName.CUSTOMS_STATUS_CHECK: {
        const service = await app.resolve(CustomStatusService);
        return service.customsStatusCheck.bind(service);
      }
      default:
        throw new Error(`Invalid feature: ${feature}`);
    }
  }

  try {
    const serviceMethod = await getService();
    await serviceMethod();
    process.exitCode = 0;
  } catch (err) {
    Logger.error("Worker error:", err);
    process.exitCode = 1;
  } finally {
    await app.close();
    process.exit();
  }
}

bootstrap();
