import { CommercialInvoiceModule } from "@/commercial-invoice/commercial-invoice.module";
import { forwardRef, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  CommercialInvoiceLine,
  CountryModule,
  Product,
  Shipment,
  TransactionalEventEmitterModule
} from "nest-modules";
import { TradePartnerModule } from "../trade-partner/trade-partner.module";
import { ProductService } from "./product.service";
import { ProductsController } from "./products.controller";
@Module({
  imports: [
    TypeOrmModule.forFeature([Product, CommercialInvoiceLine, Shipment]),
    forwardRef(() => TradePartnerModule),
    CountryModule,
    forwardRef(() => CommercialInvoiceModule),
    TransactionalEventEmitterModule
  ],
  providers: [ProductService],
  controllers: [ProductsController],
  exports: [ProductService]
})
export class ProductModule {}
