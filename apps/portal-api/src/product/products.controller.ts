import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards
} from "@nestjs/common";
import { ApiNotFoundResponse, ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses,
  BatchUpdateProductsDto,
  BatchUpdateProductsResponseDto,
  CreateProductDto,
  EditProductDto,
  GetProductsDto,
  GetProductsResponseDto,
  GetRelatedCommercialInvoiceLinesDto,
  GetRelatedCommercialInvoiceLinesResponseDto,
  NotFoundResponseDto,
  Product
} from "nest-modules";
import { ProductService } from "./product.service";

@ApiTags("Product API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("products")
export class ProductsController {
  constructor(private readonly productService: ProductService) {}

  @ApiOperation({ summary: "Get products" })
  @ApiGetManyResponses({ type: GetProductsResponseDto })
  @Get()
  async getProducts(@Query() getProductsDto: GetProductsDto) {
    return await this.productService.getProducts(getProductsDto);
  }

  @ApiOperation({ summary: "Get product" })
  @ApiParam({ name: "id", type: "number", description: "Product ID" })
  @ApiGetByIdResponses({ type: Product })
  @Get(":id")
  async getProductById(@Param("id", ParseIntPipe) id: number) {
    const product = await this.productService.getProductById(id);
    if (!product) throw new NotFoundException("Product not found");
    return product;
  }

  @ApiOperation({ summary: "Create product" })
  @ApiCreateResponses({ type: Product })
  @Post()
  async createProduct(@Body() createProductDto: CreateProductDto) {
    return await this.productService.createProduct(createProductDto);
  }

  @ApiOperation({ summary: "Edit product" })
  @ApiParam({ name: "id", type: "number", description: "Product ID" })
  @ApiEditResponses({ type: Product })
  @Put(":id")
  async editProduct(@Param("id", ParseIntPipe) id: number, @Body() editProductDto: EditProductDto) {
    return await this.productService.editProduct(id, editProductDto);
  }

  @ApiOperation({ summary: "Delete product" })
  @ApiParam({ name: "id", type: "number", description: "Product ID" })
  @ApiDeleteResponses()
  @HttpCode(204)
  @Delete(":id")
  async deleteProduct(@Param("id", ParseIntPipe) id: number) {
    await this.productService.deleteProduct(id);
    return;
  }

  @ApiOperation({ summary: "Batch update products" })
  @ApiEditResponses({ type: BatchUpdateProductsResponseDto })
  @HttpCode(200)
  @Post("batch")
  async batchUpdateProducts(@Body() batchUpdateProductsDto: BatchUpdateProductsDto) {
    return await this.productService.batchUpdateProducts(batchUpdateProductsDto);
  }

  @ApiOperation({ summary: "Get related commercial invoice lines" })
  @ApiParam({ name: "id", type: "number", description: "Product ID" })
  @ApiGetManyResponses({ type: GetRelatedCommercialInvoiceLinesResponseDto })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @Get(":id/commercial-invoice-lines")
  async getRelatedCommercialInvoiceLines(
    @Param("id", ParseIntPipe) id: number,
    @Query() getRelatedCommercialInvoiceLinesDto: GetRelatedCommercialInvoiceLinesDto
  ) {
    return await this.productService.getRelatedCommercialInvoiceLines(
      id,
      getRelatedCommercialInvoiceLinesDto
    );
  }
}
