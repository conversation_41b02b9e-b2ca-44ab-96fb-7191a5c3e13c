import { CommercialInvoiceLineService } from "@/commercial-invoice/commercial-invoice-line.service";
import { TradePartnerService } from "@/trade-partner/trade-partner.service";
import { NotFoundException } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { Test, TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import {
  AuthenticatedRequest,
  CanadaTariffService,
  CandataService,
  CountryService,
  CreateProductDto,
  MatchingRuleService,
  Product,
  StateService,
  TransactionalEventEmitterService,
  UserPermission
} from "nest-modules";
import { DataSource, Repository } from "typeorm";
import { ProductService } from "./product.service";

describe("ProductService", () => {
  let service: ProductService;
  let productRepository: Repository<Product>;
  let countryService: CountryService;
  let stateService: StateService;
  let tradePartnerService: TradePartnerService;
  let canadaTariffService: CanadaTariffService;

  const mockUser = {
    id: 1,
    organization: { id: 1 },
    permission: UserPermission.ORGANIZATION_ADMIN
  };

  const mockRequest: AuthenticatedRequest = {
    user: mockUser
  } as AuthenticatedRequest;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProductService,
        {
          provide: getRepositoryToken(Product),
          useValue: {
            findOne: jest.fn(),
            findAndCount: jest.fn(),
            existsBy: jest.fn(),
            save: jest.fn(),
            delete: jest.fn()
          }
        },
        {
          provide: REQUEST,
          useValue: mockRequest
        },
        {
          provide: TradePartnerService,
          useValue: {
            getTradePartnerById: jest.fn()
          }
        },
        {
          provide: CountryService,
          useValue: {
            getCountryById: jest.fn()
          }
        },
        {
          provide: StateService,
          useValue: {
            getStateById: jest.fn()
          }
        },
        {
          provide: CommercialInvoiceLineService,
          useValue: {
            syncLineHsCodeWithProduct: jest.fn()
          }
        },
        {
          provide: MatchingRuleService,
          useValue: {
            invalidateMatchingHistories: jest.fn()
          }
        },
        {
          provide: CandataService,
          useValue: {}
        },
        {
          provide: CanadaTariffService,
          useValue: {
            getCanadaTariffByHsCode: jest.fn()
          }
        },
        {
          provide: TransactionalEventEmitterService,
          useValue: {
            enqueueEvent: jest.fn()
          }
        },
        {
          provide: DataSource,
          useValue: {
            createQueryRunner: jest.fn()
          }
        },
        {
          provide: EventEmitter2,
          useValue: {
            emit: jest.fn()
          }
        }
      ]
    }).compile();

    service = await module.resolve<ProductService>(ProductService);
    productRepository = module.get<Repository<Product>>(getRepositoryToken(Product));
    countryService = module.get<CountryService>(CountryService);
    stateService = module.get<StateService>(StateService);
    tradePartnerService = await module.resolve<TradePartnerService>(TradePartnerService);
    canadaTariffService = await module.resolve<CanadaTariffService>(CanadaTariffService);
  });

  describe("createProduct", () => {
    const mockVendor = { id: 1, organization: { id: 1 }, partnerType: "vendor" };
    const mockNonUsCountry = { id: 2, alpha2: "CA" };
    const mockUsCountry = { id: 3, alpha2: "US" };
    const mockState = { id: 4, name: "California" };

    const baseCreateProductDto: CreateProductDto = {
      partNumber: "PART123",
      vendorId: 1,
      originId: 2,
      description: "Test product",
      hsCode: "123456"
    };

    beforeEach(() => {
      jest.spyOn(tradePartnerService, "getTradePartnerById").mockResolvedValue(mockVendor as any);
      jest.spyOn(countryService, "getCountryById").mockResolvedValue(mockNonUsCountry as any);
      jest.spyOn(canadaTariffService, "getCanadaTariffByHsCode").mockResolvedValue({} as any);
      jest.spyOn(productRepository, "existsBy").mockResolvedValue(false);
      jest
        .spyOn(productRepository, "save")
        .mockImplementation((product) => Promise.resolve({ id: 1, ...product } as any));
      jest.spyOn(service, "getProductById").mockResolvedValue({ id: 1, ...baseCreateProductDto } as any);
    });

    it("should successfully create a product with non-US origin", async () => {
      // Arrange
      const createProductDto = { ...baseCreateProductDto };

      // Act
      const result = await service.createProduct(createProductDto);

      // Assert
      expect(countryService.getCountryById).toHaveBeenCalledWith(2, undefined);
      expect(stateService.getStateById).not.toHaveBeenCalled();
      expect(productRepository.save).toHaveBeenCalled();
      expect(result).toBeDefined();
      expect(result.id).toBe(1);
    });

    it("should successfully create a product with US origin and state", async () => {
      // Arrange
      const createProductDto = {
        ...baseCreateProductDto,
        originId: 3,
        originStateId: 4
      };

      jest.spyOn(countryService, "getCountryById").mockResolvedValue(mockUsCountry as any);
      jest.spyOn(stateService, "getStateById").mockResolvedValue(mockState as any);

      // Act
      const result = await service.createProduct(createProductDto);

      // Assert
      expect(countryService.getCountryById).toHaveBeenCalledWith(3, undefined);
      expect(stateService.getStateById).toHaveBeenCalledWith(4);
      expect(productRepository.save).toHaveBeenCalled();
      expect(result).toBeDefined();
      expect(result.id).toBe(1);
    });

    it("should throw NotFoundException if US origin is provided but state is not found", async () => {
      // Arrange
      const createProductDto = {
        ...baseCreateProductDto,
        originId: 3,
        originStateId: 999
      };

      jest.spyOn(countryService, "getCountryById").mockResolvedValue(mockUsCountry as any);
      jest.spyOn(stateService, "getStateById").mockResolvedValue(null);

      // Act & Assert
      await expect(service.createProduct(createProductDto)).rejects.toThrow(NotFoundException);
      expect(stateService.getStateById).toHaveBeenCalledWith(999);
    });

    it("should not validate origin state if country is not US", async () => {
      // Arrange
      const createProductDto = {
        ...baseCreateProductDto,
        originId: 2,
        originStateId: 4 // This should be ignored
      };

      // Act
      const result = await service.createProduct(createProductDto);

      // Assert
      expect(countryService.getCountryById).toHaveBeenCalledWith(2, undefined);
      expect(stateService.getStateById).not.toHaveBeenCalled();
      expect(productRepository.save).toHaveBeenCalled();
      expect(result).toBeDefined();
    });
  });

  describe("editProduct", () => {
    const mockVendor = { id: 1, organization: { id: 1 }, partnerType: "vendor" };
    const mockNonUsCountry = { id: 2, alpha2: "CA" };
    const mockUsCountry = { id: 3, alpha2: "US" };
    const mockState = { id: 4, alpha2: "CA" };

    const existingProduct = {
      id: 1,
      partNumber: "PART123",
      vendor: mockVendor,
      origin: mockNonUsCountry,
      description: "Test product",
      hsCode: "123456",
      organization: { id: 1 }
    };

    beforeEach(() => {
      jest.spyOn(service, "getProductById").mockResolvedValue(existingProduct as any);
      jest.spyOn(canadaTariffService, "getCanadaTariffByHsCode").mockResolvedValue({} as any);
      jest.spyOn(productRepository, "existsBy").mockResolvedValue(false);
      jest
        .spyOn(productRepository, "save")
        .mockImplementation((product) => Promise.resolve({ ...product } as any));
    });

    it("should successfully edit a product changing origin from non-US to US with state", async () => {
      // Arrange
      const editProductDto = {
        originId: 3,
        originStateId: 4,
        description: "Updated description"
      };

      jest.spyOn(countryService, "getCountryById").mockResolvedValue(mockUsCountry as any);
      jest.spyOn(stateService, "getStateById").mockResolvedValue(mockState as any);

      // Act
      const result = await service.editProduct(1, editProductDto);

      // Assert
      expect(countryService.getCountryById).toHaveBeenCalledWith(3, undefined);
      expect(stateService.getStateById).toHaveBeenCalledWith(4);
      expect(productRepository.save).toHaveBeenCalled();
      expect(result).toBeDefined();
    });

    it("should throw NotFoundException if US origin is provided but state is not found during edit", async () => {
      // Arrange
      const editProductDto = {
        originId: 3,
        originStateId: 999
      };

      jest.spyOn(countryService, "getCountryById").mockResolvedValue(mockUsCountry as any);
      jest.spyOn(stateService, "getStateById").mockResolvedValue(null);

      // Act & Assert
      await expect(service.editProduct(1, editProductDto)).rejects.toThrow(NotFoundException);
      expect(stateService.getStateById).toHaveBeenCalledWith(999);
    });

    it("should not validate origin state if edited country is not US", async () => {
      // Arrange
      const editProductDto = {
        originId: 2,
        originStateId: 4 // This should be ignored for non-US country
      };

      jest.spyOn(countryService, "getCountryById").mockResolvedValue(mockNonUsCountry as any);

      // Act
      const result = await service.editProduct(1, editProductDto);

      // Assert
      expect(countryService.getCountryById).toHaveBeenCalledWith(2, undefined);
      expect(stateService.getStateById).not.toHaveBeenCalled();
      expect(productRepository.save).toHaveBeenCalled();
      expect(result).toBeDefined();
    });

    it("should handle keeping US origin but updating the state", async () => {
      // Arrange
      const usProduct = {
        ...existingProduct,
        origin: mockUsCountry,
        originState: { id: 5, alpha2: "TX" }
      };

      jest.spyOn(service, "getProductById").mockResolvedValue(usProduct as any);
      jest.spyOn(countryService, "getCountryById").mockResolvedValue(mockUsCountry as any);

      const editProductDto = {
        originId: 3,
        originStateId: 4 // Changing from Texas to California
      };

      jest.spyOn(stateService, "getStateById").mockResolvedValue(mockState as any);

      // Act
      const result = await service.editProduct(1, editProductDto);

      expect(result).toMatchObject({
        origin: mockUsCountry,
        originState: mockState
      });
    });

    it("should nullify origin state if edited country is not US", async () => {
      // Arrange
      const usProduct = {
        ...existingProduct,
        origin: mockUsCountry,
        originState: { id: 5, alpha2: "TX" }
      };

      const editProductDto = {
        originId: 2
      };

      jest.spyOn(service, "getProductById").mockResolvedValue(usProduct as any);

      jest.spyOn(countryService, "getCountryById").mockResolvedValue(mockNonUsCountry as any);

      // Act
      const result = await service.editProduct(1, editProductDto);

      // Assert
      expect(result).toMatchObject({
        origin: mockNonUsCountry,
        originState: null
      });
    });
  });
});
