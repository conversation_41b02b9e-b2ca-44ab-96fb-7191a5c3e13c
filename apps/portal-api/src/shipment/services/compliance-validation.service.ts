import { CommercialInvoiceLineEnricher } from "@/commercial-invoice/enrichers";
import { CommercialInvoiceEnricher } from "@/commercial-invoice/enrichers/commercial-invoice.enricher";
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import {
  CanadaAntiDumping,
  CanadaGovernmentAgency,
  CanadaOgd,
  CanadaTariff,
  CertificateOfOrigin,
  CommercialInvoice,
  CommercialInvoiceColumn,
  CommercialInvoiceLine,
  CustomsStatus,
  FIND_COMMERCIAL_INVOICE_LINE_RELATIONS,
  FIND_IMPORTER_RELATIONS,
  FIND_SHIPMENT_RELATIONS,
  Importer,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleSourceDatabaseTable,
  NonCompliantReason,
  OgdFiling,
  OrganizationType,
  Product,
  RuleQueryService,
  Shipment,
  ShipmentColumn,
  ShipmentMode,
  SimaFiling,
  TradePartner,
  TradePartnerColumn,
  UnitOfMeasureType,
  ValidateCommercialInvoiceComplianceResponseDto,
  ValidateCommercialInvoiceLineComplianceResponseDto,
  ValidateShipmentComplianceResponseDto
} from "nest-modules";
import { In, QueryRunner, Repository } from "typeorm";
import {
  CI_REQUIRED_FIELDS,
  OTHER_REQUIRED_FIELDS,
  SHIPMENT_REQUIRED_FIELDS,
  TRADE_PARTNER_REQUIRED_FIELDS
} from "../candata/required-fields.constant";
import {
  CommercialInvoiceCompliance,
  CommercialInvoiceLineCompliance,
  OCEAN_PORT_CITIES,
  ProductCompliance,
  ShipmentCompliance,
  TradePartnerCompliance,
  VALIDATION_FIND_COMMERCIAL_INVOICE_RELATIONS
} from "../types/compliance-validation.types";
import { getApplicableCountryFromTTCode } from "@/commercial-invoice/rules/tariff-treatment-code";

@Injectable()
export class ComplianceValidationService {
  constructor(
    @InjectRepository(CommercialInvoiceLine)
    private readonly commercialInvoiceLineRepository: Repository<CommercialInvoiceLine>,
    @InjectRepository(CommercialInvoice)
    private readonly commercialInvoiceRepository: Repository<CommercialInvoice>,
    @InjectRepository(Shipment)
    private readonly shipmentRepository: Repository<Shipment>,
    @InjectRepository(Product)
    private readonly productRepository: Repository<Product>,
    @InjectRepository(CanadaTariff)
    private readonly canadaTariffRepository: Repository<CanadaTariff>,
    @InjectRepository(Importer)
    private readonly importerRepository: Repository<Importer>,
    @Inject(forwardRef(() => RuleQueryService))
    private readonly ruleQueryService: RuleQueryService,
    @Inject(CommercialInvoiceEnricher)
    private readonly commercialInvoiceEnricher: CommercialInvoiceEnricher,
    @Inject(CommercialInvoiceLineEnricher)
    private readonly commercialInvoiceLineEnricher: CommercialInvoiceLineEnricher
  ) {}
  private readonly logger = new Logger(ComplianceValidationService.name);

  /**
   * Check if the shipment is from a demo organization.
   * @param shipment- The shipment to check
   * @returns True if the shipment is from a demo organization, false otherwise.
   */
  isDemoShipment(shipment: Shipment) {
    if (!shipment?.organization) throw new BadRequestException("Shipment organization is not set");
    return shipment?.organization?.organizationType === OrganizationType.DEMO;
  }

  /**
   * Check if shipment's Place of Delivery is inland. Noted that this function only valid for Ocean FCL shipments.
   * For other shipment modes, it will throw a BadRequestException.
   * @param shipment - The shipment to check
   * @returns True if the shipment's Place of Delivery is inland, false if it is at a port city.
   */
  isShipmentPODInland(shipment: Shipment) {
    if (shipment.modeOfTransport !== ShipmentMode.OCEAN_FCL)
      throw new BadRequestException("This function is only valid for Ocean FCL shipments");
    if (!shipment.placeOfDelivery) throw new BadRequestException("Place of delivery is not set");

    return !OCEAN_PORT_CITIES.some((city) => shipment.placeOfDelivery?.name?.toUpperCase()?.includes(city));
  }

  /**
   * Check if the shipment entry has been uploaded to Candata. This function is different from {@link isShipmentSubmitted} as the shipment can be uploaded to Candata but not yet submitted.
   * @param shipment - The shipment to check
   * @returns True if the shipment entry has been uploaded to Candata, false otherwise.
   */
  isShipmentEntryUploaded(shipment: Shipment) {
    return [
      CustomsStatus.LIVE,
      CustomsStatus.ENTRY_SUBMITTED,
      CustomsStatus.ENTRY_ACCEPTED,
      CustomsStatus.EXAM,
      CustomsStatus.RELEASED,
      CustomsStatus.ACCOUNTING_COMPLETED
    ].includes(shipment.customsStatus);
  }

  /**
   * Check if the shipment is already submitted. This function is different from {@link isShipmentEntryUploaded} as the shipment can be submitted but not yet uploaded to Candata.
   * @param shipment - The shipment to check
   * @returns True if the shipment is already submitted, false otherwise.
   */
  isShipmentSubmitted(shipment: Shipment) {
    return [
      CustomsStatus.ENTRY_SUBMITTED,
      CustomsStatus.ENTRY_ACCEPTED,
      CustomsStatus.EXAM,
      CustomsStatus.RELEASED,
      CustomsStatus.ACCOUNTING_COMPLETED
    ].includes(shipment.customsStatus);
  }

  /**
   * Check if the shipment's Candata entry can be updated. The conditions are:
   * - Shipment's entry has been uploaded to Candata
   * - Shipment's customs status is not {@link CustomsStatus.ACCOUNTING_COMPLETED}
   * - Shipment's transaction number is set
   * @param shipment - The shipment to check
   * @returns True if the shipment's Candata entry can be updated, false otherwise.
   */
  canShipmentUpdateEntry(shipment: Shipment) {
    return (
      this.isShipmentEntryUploaded(shipment) &&
      shipment.customsStatus !== CustomsStatus.ACCOUNTING_COMPLETED &&
      (shipment.transactionNumber || "").length > 0
    );
  }

  /**
   * Get the lists of shipment missing fields that are required for entry submission
   * @param shipment - The shipment to check
   * @returns The list of missing fields from arrival notice and other required fields
   */
  getShipmentMissingFields(shipment: Shipment) {
    // Get the required fields based on shipment mode
    if ([ShipmentMode.SMALL_PACKAGE].includes(shipment.modeOfTransport))
      throw new BadRequestException("Small package is not supported");
    // If mode of transport is null, then the required field should be mode of transport
    const ARRIVAL_NOTICE_REQUIRED_FIELDS = SHIPMENT_REQUIRED_FIELDS[shipment.modeOfTransport] ?? [
      ShipmentColumn.modeOfTransport
    ];

    // Check if all required fields are present
    const missingAnFields = ARRIVAL_NOTICE_REQUIRED_FIELDS.filter((field) => {
      const propertyName = field.replace(/Id$/, "");
      return [null, undefined].includes(shipment[propertyName]);
    });
    const missingOtherFields = OTHER_REQUIRED_FIELDS.filter((field) => {
      const propertyName = field.replace(/Id$/, "");
      return [null, undefined].includes(shipment[propertyName]);
    });

    return {
      missingAnFields,
      missingOtherFields
    };
  }

  /**
   * Retrun a valid Candata shipment identifier used to call Candata API.
   * @param transactionNumber - The transaction number of the shipment
   * @param fileNumber - The file number of the shipment
   * @returns The valid Candata shipment identifier. If transaction number is valid, it will be returned, otherwise file number will be returned.
   */
  getCandataShipmentIdentifier(transactionNumber?: string, fileNumber?: string) {
    const TRANSACTION_NUMBER_REGEX = /^[0-9]{14}$/;
    if (transactionNumber && TRANSACTION_NUMBER_REGEX.test(transactionNumber)) return transactionNumber;
    else if (fileNumber) return fileNumber;
    else throw new BadRequestException("No valid Candata shipment identifier found");
  }

  /**
   * Check trade partner compliance for entry submission. This includes:
   * - Missing required fields
   * - Whether the address is from either US or Canada
   * @param tradePartner - The trade partner to check
   * @param partnerType - The type of partner to check
   * @param importer - The importer of the organization used for fallback
   * @param shipper - The shipper of the shipment used for fallback
   * @returns The trade partner compliance
   */
  private getTradePartnerCompliance(
    tradePartner: TradePartner,
    partnerType: "vendor" | "shipTo",
    importer?: Importer,
    shipper?: TradePartner
  ): TradePartnerCompliance {
    const fallbackedFields: Map<string, { value: string; rule: string }> = new Map();
    // fallback1: use importer's phoneNumber / email
    if (importer) {
      const fallbackFields = ["phoneNumber", "email"];

      // Fill in the missing fields
      for (const field of fallbackFields) {
        if ([null, undefined, ""].includes(tradePartner[field]) && importer[field]) {
          tradePartner[field] = importer[field];
          fallbackedFields.set(field, {
            value: importer[field],
            rule: "use importer's phone number or email"
          });
        }
      }
    }

    // fallback2: vendor use shipper's address
    if (shipper && partnerType === "vendor") {
      const shipperFallbackFields = ["address", "city", "province", "postalCode", "country"];

      for (const field of shipperFallbackFields) {
        if ([null, undefined, ""].includes(tradePartner[field]) && shipper[field]) {
          tradePartner[field] = shipper[field];
          fallbackedFields.set(field, {
            value: shipper[field],
            rule: "use shipper's address"
          });
        }
      }
    }

    for (const [field, { value, rule }] of fallbackedFields.entries()) {
      this.logger.debug(`[getTradePartnerCompliance][${rule}] {${partnerType}.${field}: ${value}}`);
    }

    return {
      tradePartner,
      missingFields: TRADE_PARTNER_REQUIRED_FIELDS[partnerType].filter((field) => {
        const propertyName = field.replace(/Id$/, "");
        return [null, undefined, ""].includes(tradePartner[propertyName]);
      }),
      isUSOrCanadaAddress: ["CA", "US"].includes(tradePartner.country?.alpha2),
      hasPostalCode: typeof tradePartner.postalCode === "string" && tradePartner.postalCode.length > 0
    };
  }

  /**
   * Get all compliance records for the products.
   * @param productsOrProductIds - List of products or product IDs to check
   * @param queryRunner - The query runner to use for the operation
   * @returns The list of compliance records for each product
   */
  private async getProductCompliances(
    productsOrProductIds: Array<Product> | Array<number>,
    queryRunner?: QueryRunner
  ): Promise<Array<ProductCompliance>> {
    const productRepository = queryRunner
      ? queryRunner.manager.getRepository(Product)
      : this.productRepository;

    // If no products are provided, return an empty array as fallback
    if (productsOrProductIds.length <= 0) return [];

    // Get products
    let products: Array<Product> = undefined;
    if (productsOrProductIds.every((productOrId) => typeof productOrId === "number")) {
      products = await productRepository.find({ where: { id: In(productsOrProductIds) } });
      const missingProductIds = productsOrProductIds.filter(
        (id) => !products.some((product) => product.id === id)
      );
      if (missingProductIds.length > 0)
        throw new NotFoundException(`Products with IDs ${missingProductIds.join(", ")} not found`);
    } else products = productsOrProductIds;

    // Get matching results
    const matchingResultsList =
      products.length > 0
        ? await this.ruleQueryService.queryMultipleMatchingResults(
            {
              destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT,
              destinationIds: products.map((p) => p.id),
              sourceTables: [
                MatchingRuleSourceDatabaseTable.CANADA_OGD,
                MatchingRuleSourceDatabaseTable.OGD_FILING,
                MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING,
                MatchingRuleSourceDatabaseTable.SIMA_FILING,
                MatchingRuleSourceDatabaseTable.CERTIFICATE_OF_ORIGIN
              ]
            },
            queryRunner,
            true
          )
        : [];

    // Return product compliances
    return matchingResultsList.map(({ destinationId: productId, matchingResults }) => {
      const currentProductCompliance: ProductCompliance = {
        product: products.find((p) => p.id === productId),
        ogdMappings: [],
        antiDumpingMappings: [],
        certificateOfOrigin: null
      };

      const { sourceRecords: canadaOgdRecords } =
        (matchingResults || []).find(
          (result) => result.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_OGD
        ) ?? {};
      const { sourceRecords: ogdFilingRecords } =
        (matchingResults || []).find(
          (result) => result.sourceTable === MatchingRuleSourceDatabaseTable.OGD_FILING
        ) ?? {};
      const { sourceRecords: canadaAntiDumpingRecords } =
        (matchingResults || []).find(
          (result) => result.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING
        ) ?? {};
      const { sourceRecords: simaFilingRecords } =
        (matchingResults || []).find(
          (result) => result.sourceTable === MatchingRuleSourceDatabaseTable.SIMA_FILING
        ) ?? {};
      const { sourceRecords: certificateOfOriginRecords } =
        (matchingResults || []).find(
          (result) => result.sourceTable === MatchingRuleSourceDatabaseTable.CERTIFICATE_OF_ORIGIN
        ) ?? {};

      // Get all Canada OGDs and their related OGD filings
      for (const canadaOgd of (canadaOgdRecords || []) as Array<CanadaOgd>) {
        const relatedOgdFiling = ((ogdFilingRecords || []) as Array<OgdFiling>).find(
          (ogdFiling) => ogdFiling.ogd?.agency === canadaOgd.agency
        );
        currentProductCompliance.ogdMappings.push({
          canadaOgd,
          ogdFiling: relatedOgdFiling ?? null
        });
      }

      // Get all Canada Anti-Dumping records and their related SIMA filings
      for (const canadaAntiDumping of (canadaAntiDumpingRecords || []) as Array<CanadaAntiDumping>) {
        const relatedSimaFiling = ((simaFilingRecords || []) as Array<SimaFiling>).find(
          (simaFiling) => simaFiling.measureInForce?.id === canadaAntiDumping.id
        );
        currentProductCompliance.antiDumpingMappings.push({
          canadaAntiDumping,
          simaFiling: relatedSimaFiling ?? null
        });
      }

      // Get the first certificate of origin with country of origin
      if (Array.isArray(certificateOfOriginRecords) && certificateOfOriginRecords.length > 0) {
        const targetCertificate = certificateOfOriginRecords.find(
          (coo: CertificateOfOrigin) => typeof coo.countryOfOrigin?.id === "number"
        ) as CertificateOfOrigin;
        currentProductCompliance.certificateOfOrigin = targetCertificate ?? null;
      }

      return currentProductCompliance;
    });
  }

  /**
   * Get the list of commercial invoice line compliances required for entry submission. This includes:
   * - Valid Canada Tariff record based on the line's HS code
   * - Compliance record for the line's product (@see {@link getProductCompliances} for details)
   * @param linesOrLineIds - The list of commercial invoice lines or line IDs to validate.
   * @param queryRunner - The query runner to use for the operation
   * @returns List of commercial invoice line compliances
   */
  async getCommercialInvoiceLineCompliances(
    linesOrLineIds: Array<CommercialInvoiceLine> | Array<number>,
    queryRunner?: QueryRunner
  ): Promise<Array<CommercialInvoiceLineCompliance>> {
    const commercialInvoiceLineRepository = queryRunner
      ? queryRunner.manager.getRepository(CommercialInvoiceLine)
      : this.commercialInvoiceLineRepository;
    const canadaTariffRepository = queryRunner
      ? queryRunner.manager.getRepository(CanadaTariff)
      : this.canadaTariffRepository;
    let commercialInvoiceLines: Array<CommercialInvoiceLine> = undefined;

    // If no lines are provided, return an empty array as fallback
    if (linesOrLineIds.length <= 0) return [];

    // Get commercial invoice lines
    if (linesOrLineIds.every((lineOrId) => typeof lineOrId === "number")) {
      commercialInvoiceLines = await commercialInvoiceLineRepository.find({
        where: { id: In(linesOrLineIds) },
        relations: FIND_COMMERCIAL_INVOICE_LINE_RELATIONS
      });
      const missingLineIds = linesOrLineIds.filter(
        (id) => !commercialInvoiceLines.some((line) => line.id === id)
      );
      if (missingLineIds.length > 0)
        throw new NotFoundException(
          `Commercial invoice lines with IDs ${missingLineIds.join(", ")} not found`
        );
    } else commercialInvoiceLines = linesOrLineIds;

    // Get related Canada Tariff records for the HS codes in the commercial invoice lines
    const toBeCheckedHsCodeList = commercialInvoiceLines.reduce((list, line) => {
      if (typeof line.hsCode === "string" && line.hsCode.length === 10 && !list.includes(line.hsCode))
        list.push(line.hsCode);
      return list;
    }, [] as Array<string>);
    const relatedCanadaTariffs = await canadaTariffRepository.findBy({
      hsCode: In(toBeCheckedHsCodeList)
    });

    // Get the product compliances for all products in the lines
    const toBeCheckedProductIdList = commercialInvoiceLines.reduce((list, line) => {
      if (typeof line.product?.id === "number" && !list.includes(line.product.id)) list.push(line.product.id);
      return list;
    }, [] as Array<number>);

    // we are getting the product compliances for product in all lines
    // TODO: we need to change the way for populating the product compliances
    const productCompliances = await this.getProductCompliances(toBeCheckedProductIdList, queryRunner);

    // Check if the HS code and quantity of each line are valid
    const enrichedCommercialInvoiceLines =
      await this.commercialInvoiceLineEnricher.enrichMany(commercialInvoiceLines);
    return await Promise.all(
      enrichedCommercialInvoiceLines.map(async (line) => {
        const relatedCanadaTariff = relatedCanadaTariffs.find((tariff) => tariff.hsCode === line.hsCode);
        const productCompliance =
          productCompliances.find((pc) => pc.product?.id === line.product?.id) ?? null;

        return {
          line: line,
          quantity: line.quantity,
          perUnitMeasurements: (line.measurements || []).filter((m) => m.type === UnitOfMeasureType.PER_UNIT),
          canadaTariff: relatedCanadaTariff ?? null,
          productCompliance: productCompliance ?? null
        };
      })
    );
  }

  /**
   * Get the list of commercial invoice compliances required for entry submission. This includes:
   * - Missing required fields in the commercial invoices
   * - Trade partner compliances for `shipTo` and `vendor` (@see {@link getTradePartnerCompliance} for details)
   * - Commercial invoice line compliances (@see {@link getCommercialInvoiceLineCompliances} for details)
   * @param invoicesOrInvoiceIds - The list of commercial invoices or invoice IDs to validate
   * @param queryRunner - The query runner to use for the operation
   * @returns List of commercial invoice compliances
   */
  async getCommercialInvoiceCompliances(
    invoicesOrInvoiceIds: Array<CommercialInvoice> | Array<number>,
    queryRunner?: QueryRunner
  ): Promise<Array<CommercialInvoiceCompliance>> {
    const commercialInvoiceRepository = queryRunner
      ? queryRunner.manager.getRepository(CommercialInvoice)
      : this.commercialInvoiceRepository;
    const importerRepository = queryRunner
      ? queryRunner.manager.getRepository(Importer)
      : this.importerRepository;
    const shipmentRepository = queryRunner
      ? queryRunner.manager.getRepository(Shipment)
      : this.shipmentRepository;

    // If no invoices are provided, return an empty array as fallback
    if (invoicesOrInvoiceIds.length <= 0) return [];

    // Get commercial invoices
    let commercialInvoices: Array<CommercialInvoice> = undefined;
    if (invoicesOrInvoiceIds.every((invoiceOrId) => typeof invoiceOrId === "number")) {
      commercialInvoices = await commercialInvoiceRepository.find({
        where: { id: In(invoicesOrInvoiceIds) },
        relations: VALIDATION_FIND_COMMERCIAL_INVOICE_RELATIONS
      });
      const missingInvoiceIds = invoicesOrInvoiceIds.filter(
        (id) => !commercialInvoices.some((invoice) => invoice.id === id)
      );
      if (missingInvoiceIds.length > 0)
        throw new NotFoundException(`Commercial invoices with IDs ${missingInvoiceIds.join(", ")} not found`);
    } else commercialInvoices = invoicesOrInvoiceIds;

    // Get line compliances
    const lineComplianceList = await this.getCommercialInvoiceLineCompliances(
      commercialInvoices.flatMap((invoice) => invoice.commercialInvoiceLines),
      queryRunner
    );

    // Get importers for all organizations in the CIs
    const organizationIds = commercialInvoices.reduce((ids, ci) => {
      if (typeof ci?.organization?.id === "number" && !ids.includes(ci?.organization?.id))
        ids.push(ci?.organization?.id);
      return ids;
    }, [] as Array<number>);
    const importers =
      organizationIds.length > 0
        ? await importerRepository.find({
            where: { organization: { id: In(organizationIds) } },
            relations: FIND_IMPORTER_RELATIONS
          })
        : [];

    // Get shippers for all shipments in the CIs
    const shipmentIds = commercialInvoices.reduce((ids, ci) => {
      if (typeof ci?.shipment?.id === "number" && !ids.includes(ci?.shipment?.id)) ids.push(ci?.shipment?.id);
      return ids;
    }, [] as Array<number>);
    const shipperShipments =
      shipmentIds.length > 0
        ? await shipmentRepository.find({
            where: { id: In(shipmentIds) },
            relations: { shipper: true }
          })
        : [];

    // Return commercial invoice compliances
    return await Promise.all(
      commercialInvoices.map(async (invoice) => {
        const invoiceLineIds = invoice.commercialInvoiceLines.map((line) => line.id);
        const lineCompliances = lineComplianceList.filter((lc) => invoiceLineIds.includes(lc.line?.id));

        // Check if all required fields are present in the commercial invoice
        const missingFields = CI_REQUIRED_FIELDS.filter((field) => {
          const propertyName = field.replace(/Id$/, "");
          return [null, undefined, ""].includes(invoice[propertyName]);
        });

        // if the country of export is us, the state of export is required
        const isUSExport = invoice.countryOfExport?.alpha2 === "US";
        const missingStateOfExport = !invoice.stateOfExport;

        if (isUSExport && missingStateOfExport) {
          missingFields.push(CommercialInvoiceColumn.stateOfExportId);
        }

        // Get importer for the CI organization
        const importer = importers.find((i) => i.organization.id === invoice?.organization?.id) ?? null;

        // Get shipper for the shipment
        const shipper =
          shipperShipments.find((shipment) => shipment.id === invoice?.shipment?.id)?.shipper ?? null;

        return {
          commercialInvoice: invoice,
          missingFields,
          shipToCompliance: invoice.shipTo
            ? this.getTradePartnerCompliance(invoice.shipTo, "shipTo", importer)
            : null,
          vendorCompliance: invoice.vendor
            ? this.getTradePartnerCompliance(invoice.vendor, "vendor", importer, shipper)
            : null,
          lineCompliances: lineCompliances ?? null
        };
      })
    );
  }

  /**
   * Get the compliance records for shipments. The compliance includes:
   * - Whether the shipment has already been submitted
   * - Missing required fields in the shipments
   * - Commercial invoice compliances (@see {@link getCommercialInvoiceCompliances} for details)
   * @param shipmentsOrShipmentIds - The list of shipments or shipment IDs to validate
   * @param queryRunner - The query runner to use for the operation
   * @returns List of shipment compliances
   */
  async getShipmentCompliances(
    shipmentsOrShipmentIds: Array<Shipment> | Array<number>,
    queryRunner?: QueryRunner
  ): Promise<Array<ShipmentCompliance>> {
    const shipmentRepository = queryRunner
      ? queryRunner.manager.getRepository(Shipment)
      : this.shipmentRepository;
    const commercialInvoiceRepository = queryRunner
      ? queryRunner.manager.getRepository(CommercialInvoice)
      : this.commercialInvoiceRepository;

    // If no shipments are provided, return an empty array as fallback
    if (shipmentsOrShipmentIds.length <= 0) return [];

    // Get shipments
    let shipments: Array<Shipment> = undefined;
    if (shipmentsOrShipmentIds.every((shipmentOrId) => typeof shipmentOrId === "number")) {
      shipments = await shipmentRepository.find({
        where: { id: In(shipmentsOrShipmentIds) },
        relations: FIND_SHIPMENT_RELATIONS
      });
      const missingShipmentIds = shipmentsOrShipmentIds.filter(
        (id) => !shipments.some((shipment) => shipment.id === id)
      );
      if (missingShipmentIds.length > 0)
        throw new NotFoundException(`Shipments with IDs ${missingShipmentIds.join(", ")} not found`);
    } else shipments = shipmentsOrShipmentIds;

    // Get invoice compliances
    const commercialInvoiceList = await commercialInvoiceRepository.find({
      where: { shipment: { id: In(shipments.map((s) => s.id)) } },
      relations: VALIDATION_FIND_COMMERCIAL_INVOICE_RELATIONS
    });

    // we will enrich the commercial invoices with the fallback enricher
    const enrichedCommercialInvoiceList = await Promise.all(
      commercialInvoiceList.map(async (invoice) => {
        return this.commercialInvoiceEnricher.enrich(invoice);
      })
    );

    const invoiceCompliances = await this.getCommercialInvoiceCompliances(
      enrichedCommercialInvoiceList,
      queryRunner
    );

    // Return shipment compliances
    return shipments.map((shipment) => {
      // Check for shipment missing fields
      const { missingAnFields, missingOtherFields } = this.getShipmentMissingFields(shipment);

      // Get invoice compliances for shipment
      const shipmentInvoices = commercialInvoiceList.filter((ci) => ci.shipment.id === shipment.id);
      const shipmentInvoiceCompliances = invoiceCompliances.filter((ic) =>
        shipmentInvoices.some((invoice) => invoice.id === ic.commercialInvoice?.id)
      );

      return {
        shipment,
        isSubmitted: this.isShipmentSubmitted(shipment),
        missingFields: [...missingAnFields, ...missingOtherFields],
        invoiceCompliances: shipmentInvoiceCompliances
      };
    });
  }

  /**
   * Validate commercial invoice line compliances. The check includes:
   * - Whether the HS code is valid
   * - Whether the line quantity is valid
   * - Whether the line has any compliance rule errors
   * @param lineCompliances - The list of commercial invoice line compliances to validate
   * @param skipFilingsValidation - Whether to skip the filings validation. Default is false.
   * @returns List of validation results for each line
   */
  validateCommercialInvoiceLineCompliances(
    lineCompliances: Array<CommercialInvoiceLineCompliance>,
    skipFilingsValidation = false
  ): Array<ValidateCommercialInvoiceLineComplianceResponseDto> {
    const validationResults: Array<ValidateCommercialInvoiceLineComplianceResponseDto> = [];
    for (const { line, quantity, perUnitMeasurements, canadaTariff, productCompliance } of lineCompliances) {
      // Check for invalid HS code
      const isHsCodeInvalid = !canadaTariff;

      // Check for invalid quantity
      const isQuantityInvalid = canadaTariff && quantity === 0;

      // Check for missing OGD filings
      // If skip filings validation is true, the missing OGD filings will be returned as empty array
      const missingOgdFilings = !skipFilingsValidation
        ? (productCompliance?.ogdMappings || [])
            .filter((mapping) => !mapping.ogdFiling)
            .map((mapping) => ({
              sourceTable: MatchingRuleSourceDatabaseTable.CANADA_OGD,
              sourceId: mapping.canadaOgd.id,
              sourceRecord: mapping.canadaOgd,
              reason: NonCompliantReason.MISSING_OGD_FILING
            }))
        : [];

      // Check for missing SIMA filings
      // If skip filings validation is true, the missing SIMA filings will be returned as empty array
      const missingSimaFilings = !skipFilingsValidation
        ? !(productCompliance?.antiDumpingMappings || []).some((mapping) => mapping.simaFiling)
          ? (productCompliance?.antiDumpingMappings || []).map((mapping) => ({
              sourceTable: MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING,
              sourceId: mapping.canadaAntiDumping.id,
              sourceRecord: mapping.canadaAntiDumping,
              reason: NonCompliantReason.MISSING_SIMA_FILING
            }))
          : []
        : [];

      // Check for invalid measurements
      // If skip filings validation is true, the invalid measurements will be returned as empty array
      const invalidMeasurementsOgds = !skipFilingsValidation
        ? (productCompliance?.ogdMappings || [])
            .filter(
              (mapping) =>
                perUnitMeasurements.length <= 0 &&
                [CanadaGovernmentAgency.CFIA, CanadaGovernmentAgency.GAC].includes(mapping.canadaOgd.agency)
            )
            .map((mapping) => ({
              sourceTable: MatchingRuleSourceDatabaseTable.CANADA_OGD,
              sourceId: mapping.canadaOgd.id,
              sourceRecord: mapping.canadaOgd,
              reason: NonCompliantReason.MISSING_OR_INVALID_MEASUREMENTS
            }))
        : [];

      const isUsStateOfOriginNotSet = line.origin?.alpha2 === "US" && !line.originState;

      // Check for valid certificate of origin and if it matches the TT
      const ttRequiredOriginCountryAlpha2 = getApplicableCountryFromTTCode(line?.tt?.code);
      const certifiedOriginCountryAlpha2 =
        productCompliance?.certificateOfOrigin?.countryOfOrigin?.alpha2 ?? null;

      const ttRequiresAnyCountry =
        typeof ttRequiredOriginCountryAlpha2 === "boolean" && ttRequiredOriginCountryAlpha2 === true;

      const missingCertificateOfOrigin =
        !ttRequiresAnyCountry &&
        (!certifiedOriginCountryAlpha2 || certifiedOriginCountryAlpha2 !== ttRequiredOriginCountryAlpha2)
          ? [
              {
                sourceTable: MatchingRuleSourceDatabaseTable.CERTIFICATE_OF_ORIGIN,
                sourceId: productCompliance?.certificateOfOrigin?.id ?? null,
                sourceRecord: productCompliance?.certificateOfOrigin ?? null,
                reason: NonCompliantReason.MISSING_CERTIFICATE_OF_ORIGIN
              }
            ]
          : [];

      // Add validation result for the line
      validationResults.push({
        lineId: line.id,
        productId: productCompliance?.product?.id ?? null,
        isHsCodeInvalid,
        isQuantityInvalid,
        isUsStateOfOriginNotSet,
        nonCompliantRecords: [
          ...missingOgdFilings,
          ...missingSimaFilings,
          ...invalidMeasurementsOgds,
          ...missingCertificateOfOrigin
        ]
      });
    }

    return validationResults;
  }

  /**
   * Validate commercial invoice compliances. The check includes:
   * - Missing required fields in the commercial invoices.
   * - Missing required fields in the `shipTo` and `vendor` trade partners.
   * - Non-compliant commercial invoice lines. (@see {@link validateCommercialInvoiceLineCompliances} for details)
   * @param invoiceCompliances - The list of commercial invoice compliances to validate
   * @param skipFilingsValidation - Whether to skip the filings validation. Default is false.
   * @returns List of validation results for each commercial invoice
   */
  validateCommercialInvoiceCompliances(
    invoiceCompliances: Array<CommercialInvoiceCompliance>,
    skipFilingsValidation = false
  ): Array<ValidateCommercialInvoiceComplianceResponseDto> {
    return invoiceCompliances.map(
      ({ commercialInvoice, missingFields, shipToCompliance, vendorCompliance, lineCompliances }) => {
        return {
          commercialInvoiceId: commercialInvoice.id,
          missingFields,
          shipToMissingFields: (shipToCompliance?.missingFields || []).concat(
            shipToCompliance?.isUSOrCanadaAddress && !shipToCompliance?.hasPostalCode
              ? [TradePartnerColumn.postalCode]
              : []
          ),
          vendorMissingFields: (vendorCompliance?.missingFields || []).concat(
            vendorCompliance?.isUSOrCanadaAddress && !vendorCompliance?.hasPostalCode
              ? [TradePartnerColumn.postalCode]
              : []
          ),
          nonCompliantLines: this.validateCommercialInvoiceLineCompliances(
            lineCompliances,
            skipFilingsValidation
          ).filter(
            ({ isHsCodeInvalid, isQuantityInvalid, isUsStateOfOriginNotSet, nonCompliantRecords }) =>
              isHsCodeInvalid ||
              isQuantityInvalid ||
              isUsStateOfOriginNotSet ||
              nonCompliantRecords.length > 0
          )
        };
      }
    );
  }

  /**
   * Validate shipment compliances.
   *
   * The check includes:
   * - Missing required fields in the shipments
   * - Commercial invoice compliances (@see {@link validateCommercialInvoiceCompliances} for details)
   *
   * @param shipmentCompliances - The list of shipment compliances to validate
   * @param skipFilingsValidation - Whether to skip the filings validation. Default is false.
   * @returns List of validation results for each shipment
   */
  validateShipmentCompliances(
    shipmentCompliances: Array<ShipmentCompliance>,
    skipFilingsValidation = false
  ): Array<ValidateShipmentComplianceResponseDto> {
    return shipmentCompliances.map(({ shipment, missingFields, invoiceCompliances }) => ({
      shipmentId: shipment.id,
      noCommercialInvoice: invoiceCompliances.length <= 0,
      missingFields,
      nonCompliantInvoices: this.validateCommercialInvoiceCompliances(
        invoiceCompliances,
        skipFilingsValidation
      ).filter(
        ({ missingFields, shipToMissingFields, vendorMissingFields, nonCompliantLines }) =>
          missingFields.length > 0 ||
          shipToMissingFields.length > 0 ||
          vendorMissingFields.length > 0 ||
          nonCompliantLines.length > 0
      )
    }));
  }
}
