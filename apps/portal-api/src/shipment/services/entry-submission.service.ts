import { NODE_ENV } from "@/app.module";
import { NodeEnv } from "nest-modules";
import { CommercialInvoiceLineService } from "@/commercial-invoice/commercial-invoice-line.service";
import { EntrySubmissionErrorEmailDto } from "@/email/dto/email-template.dto";
import { EmailService } from "@/email/services/email.service";
import { EmailTemplateName } from "@/email/types/email.types";
import { ImporterService } from "@/importer/importer.service";
import { UnitConversionService } from "@/unit-conversion/unit-conversion.service";
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import {
  CanadaGovernmentAgency,
  CandataPartyDto,
  CandataService,
  CandataShipmentDto,
  CciDto,
  CommercialInvoice,
  CommercialInvoiceLine,
  CustomsStatus,
  ECCCSubType,
  Email,
  InvoiceDetailsDto,
  NRCANSubType,
  OGD_HEALTH_CANADA_PROGRAM,
  OgdFiling,
  OrganizationCustomsBroker,
  PackageUOM,
  Shipment,
  SimaSubjectCode,
  TariffTreatmentCode,
  TradePartner,
  UNIT_OF_MEASURE_TYPE_MAP,
  UnitOfMeasure,
  UnitOfMeasureType,
  VfdCode,
  WeightUOM
} from "nest-modules";
import { DataSource, QueryRunner, Repository } from "typeorm";
import { MapperFacade } from "../candata/mapper/mapper.facade";
import { HCProgramMap } from "../candata/product-category-group.constant";
import { ShipmentEnricher } from "../enrichers/shipment.enricher";
import { CommercialInvoiceCompliance, ShipmentCompliance } from "../types/compliance-validation.types";
import { convertModeOfTransport } from "../utils/convert-mode-of-transport";
import { ComplianceValidationService } from "./compliance-validation.service";
import { ShipmentService } from "./shipment.service";

@Injectable({ scope: Scope.REQUEST })
export class EntrySubmissionService {
  constructor(
    @InjectRepository(Shipment)
    private readonly shipmentRepository: Repository<Shipment>,
    @Inject(ImporterService)
    private readonly importerService: ImporterService,
    @Inject(forwardRef(() => CommercialInvoiceLineService))
    private readonly commercialInvoiceLineService: CommercialInvoiceLineService,
    @Inject(CandataService)
    private readonly candataService: CandataService,
    @Inject(forwardRef(() => ShipmentService))
    private readonly shipmentService: ShipmentService,
    @Inject(forwardRef(() => ComplianceValidationService))
    private readonly complianceValidationService: ComplianceValidationService,
    @Inject(forwardRef(() => EmailService))
    private readonly emailService: EmailService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    @Inject(UnitConversionService)
    private readonly unitConversionService: UnitConversionService,
    @Inject(ConfigService)
    private readonly configService: ConfigService,
    @Inject(ShipmentEnricher)
    private readonly enricher: ShipmentEnricher
  ) {}
  private readonly logger = new Logger(EntrySubmissionService.name);
  // Common key for marking a CCI or CCI detail as deleted
  static readonly DELETE_KEY = "DELETE";

  private get BACKOFFICE_EMAIL(): string {
    return this.configService.get<string>("BACKOFFICE_EMAIL_ADDRESS");
  }

  private get SYSTEM_FROM_EMAIL(): string {
    return this.configService.get<string>("SYSTEM_FROM_EMAIL");
  }

  private get PORTAL_FRONTEND_URL(): string {
    return this.configService.get<string>("PORTAL_FRONTEND_URL");
  }

  private async sendShipmentEntryErrorEmail(
    entrySubmissionErrorEmailDto: EntrySubmissionErrorEmailDto
  ): Promise<Email | null> {
    try {
      const emailText = this.emailService.renderEmailTemplate(
        EmailTemplateName.ENTRY_SUBMISSION_ERROR_EMAIL,
        entrySubmissionErrorEmailDto
      );
      if (NODE_ENV === NodeEnv.LOCAL) {
        this.logger.log(`Skip sending shipment entry error email in local`);
        this.logger.log(`Email text: ${emailText}`);
        return null;
      }
      return await this.emailService.sendEmail({
        from: this.SYSTEM_FROM_EMAIL,
        to: [this.BACKOFFICE_EMAIL],
        subject: `Shipment Entry Submission Error - ID:${entrySubmissionErrorEmailDto.shipmentId}`,
        text: emailText
      });
    } catch (error) {
      this.logger.error(
        `Failed to send shipment entry error email: ${error instanceof Error ? error.message : String(error)}`
      );
      return null;
    }
  }

  /**
   * Format a trade partner to Candata Party format
   * @param partner The trade partner to format
   * @returns The formatted trade partner in Candata Party format
   */
  private toCandataParty(partner: TradePartner): CandataPartyDto {
    return MapperFacade.tradePartnerToCandataPartyDto(partner);
  }

  private mapInvoiceTradePartners(shipmentCompliance: ShipmentCompliance) {
    // Convert shipTo and vendor in invoices to Candata Party format
    const invoiceVendorMap = new Map<number, CandataPartyDto>();
    const invoiceConsigneeMap = new Map<number, CandataPartyDto>();
    for (const invoiceCompliance of shipmentCompliance.invoiceCompliances) {
      invoiceVendorMap.set(
        invoiceCompliance.commercialInvoice.id,
        this.toCandataParty(invoiceCompliance.vendorCompliance.tradePartner)
      );
      invoiceConsigneeMap.set(
        invoiceCompliance.commercialInvoice.id,
        this.toCandataParty(invoiceCompliance.shipToCompliance.tradePartner)
      );
    }
    this.logger.log(`Invoice vendor map: ${JSON.stringify(Array.from(invoiceVendorMap.entries()))}`);
    this.logger.log(`Invoice consignee map: ${JSON.stringify(Array.from(invoiceConsigneeMap.entries()))}`);

    return {
      invoiceVendorMap,
      invoiceConsigneeMap
    };
  }

  private async getShipmentImporter(shipment: Shipment, queryRunner?: QueryRunner) {
    const importer = shipment.importer
      ? await this.importerService.findOrCreateCandataImporter(shipment.importer?.id, queryRunner)
      : null;
    if (!importer) throw new NotFoundException("Shipment importer not found");
    if (!importer.candataCustomerNumber)
      throw new BadRequestException("Importer missing Candata customer number");
    return importer;
  }

  private async performDutyAndTaxCalculation(
    transactionNumberOrFileNumber: string,
    customsBroker: OrganizationCustomsBroker
  ) {
    const dutiesAndTaxesMapping = new Map<
      number,
      Pick<
        CommercialInvoiceLine,
        | "cadCalculationDate"
        | "valueForDuty"
        | "valueForTax"
        | "antiDumping"
        | "countervailing"
        | "customsDuties"
        | "exciseDuties"
        | "exciseTax"
        | "gst"
        | "safeguard"
        | "surtax"
        | "totalDutiesAndTaxes"
      >
    >();
    let calculatedCandataShipment: CandataShipmentDto | null = null;

    try {
      calculatedCandataShipment = await this.candataService.calculateCad(
        transactionNumberOrFileNumber,
        customsBroker
      );
      this.logger.log(
        `Calculated Candata Shipment: ${calculatedCandataShipment?.transactionNumber}, ${calculatedCandataShipment?.fileNumber}`
      );
      if (
        !calculatedCandataShipment ||
        !calculatedCandataShipment?.transactionNumber ||
        !calculatedCandataShipment?.fileNumber
      )
        throw new BadRequestException(`No valid calculated Candata shipment returned`);

      for (const cci of calculatedCandataShipment?.ccis || []) {
        for (const line of cci?.invoiceSummary?.details || []) {
          const isCalculationValid = !line?.dutyTaxCalculationError && line?.calculatedDateTime;
          this.logger.log(
            `CCI: ${cci?.id}, Line: ${line?.id}, Calculated Datetime: ${line?.calculatedDateTime}, Calculation error: ${line?.dutyTaxCalculationError}, Is calculation valid: ${isCalculationValid}`
          );

          dutiesAndTaxesMapping.set(line.id, {
            cadCalculationDate: isCalculationValid ? new Date(line?.calculatedDateTime) : null,
            valueForDuty: isCalculationValid ? line.valueForDuty || 0 : null,
            valueForTax: isCalculationValid ? line.valueForTax || 0 : null,
            antiDumping: isCalculationValid ? line.antiDumpingDuty || 0 : null,
            countervailing: isCalculationValid ? line.countervailingDutyAmount || 0 : null,
            customsDuties: isCalculationValid ? line.duty || 0 : null,
            exciseDuties: isCalculationValid ? line.exciseDutyAmount || 0 : null,
            exciseTax: isCalculationValid ? line.exciseTaxAmount || 0 : null,
            gst: isCalculationValid ? line.gstTax || 0 : null,
            safeguard: isCalculationValid ? line.safeguardSurtax || 0 : null,
            surtax: isCalculationValid ? line.surtax || 0 : null,
            totalDutiesAndTaxes: isCalculationValid ? line.totalDuty || 0 : null
          });
        }
      }
    } catch (error) {
      this.logger.error(
        `Failed to perform duty and tax calculation for transaction number or file number ${transactionNumberOrFileNumber}: ${error instanceof Error ? error.message : String(error)}`
      );
    }

    return { dutiesAndTaxesMapping, calculatedCandataShipment };
  }

  private async tryClearingCandataCciErrors(
    transactionNumber: string,
    customsBroker: OrganizationCustomsBroker
  ) {
    try {
      await this.candataService.clearCandataCci(transactionNumber, customsBroker);
    } catch (error) {
      this.logger.error(
        `Failed to clear Candata CCI errors for transaction number ${transactionNumber}: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
    }
  }

  private async tryDeletingCandataCci(transactionNumber: string, customsBroker: OrganizationCustomsBroker) {
    try {
      await this.candataService.deleteCandataCci(transactionNumber, customsBroker);
    } catch (error) {
      this.logger.error(
        `Failed to delete Candata CCI for transaction number ${transactionNumber}: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
    }
  }

  private async mapCandataCcis(
    invoiceCompliances: Array<CommercialInvoiceCompliance>,
    invoiceVendorMap: Map<number, CandataPartyDto>,
    invoiceConsigneeMap: Map<number, CandataPartyDto>,
    shipment: Shipment,
    candataShipment?: CandataShipmentDto,
    queryRunner?: QueryRunner
  ) {
    // Convert mode of transport to direct shipment mode
    const directShipmentMode = convertModeOfTransport(shipment.modeOfTransport);
    if (!directShipmentMode)
      throw new BadRequestException(
        "Cannot find corresponding direct shipment mode for the current mode of transport"
      );

    // Map candata CCIs
    const candataCcis: Array<CciDto> = [];
    for (const invoiceCompliance of invoiceCompliances) {
      const ci = invoiceCompliance.commercialInvoice;
      const existingCandataCci =
        typeof ci.candataId === "number"
          ? (candataShipment?.ccis || []).find((cci) => cci?.id === ci?.candataId)
          : null;
      const defaultTt = await this.commercialInvoiceLineService.getDefaultTt(ci, queryRunner);
      const defaultVfd = await this.commercialInvoiceLineService.getDefaultVfd(ci, queryRunner);

      // Map each invoice line to a Candata CCI line
      const cciLines: Array<InvoiceDetailsDto> = [];
      for (const lineCompliance of invoiceCompliance.lineCompliances.sort((a, b) => {
        if (typeof a.line.sequence === "number" && typeof b.line.sequence === "number")
          return a.line.sequence - b.line.sequence;
        else return a.line.id - b.line.id;
      })) {
        // Get existing Candata CCI line if exists
        const existingCandataCciDetails =
          typeof lineCompliance.line.candataId === "number" && existingCandataCci
            ? existingCandataCci?.invoiceSummary?.details?.find(
                (detail) => detail.id === lineCompliance.line.candataId
              )
            : null;

        // Get all compliance related to the line
        const simaFilings = (lineCompliance.productCompliance?.antiDumpingMappings || [])
          .map((mapping) => mapping.simaFiling)
          .filter((simaFiling) => simaFiling);
        const targetSimaFiling = simaFilings.length > 0 ? simaFilings[0] : null;

        const ogdFilings = (lineCompliance.productCompliance?.ogdMappings || []).map(
          (mapping) => mapping.ogdFiling
        );
        const filteredOgdFilings: Array<OgdFiling> = [];
        for (const filing of ogdFilings) {
          if (filing && !filteredOgdFilings.some((fof) => fof.ogd?.agency === filing.ogd?.agency))
            filteredOgdFilings.push(filing);
        }

        // Push the mapped CCI line to the list
        cciLines.push({
          id: existingCandataCciDetails?.id || undefined,
          productNumber: `CLAROLINE${lineCompliance.line.id}`,
          description: (
            lineCompliance.line.goodsDescription ||
            lineCompliance.line.product?.description ||
            ""
          )?.slice(0, 256),
          classification: lineCompliance.line.hsCode,
          vfdCode: (lineCompliance.line.vfd || defaultVfd)?.code?.toString() as VfdCode,
          tariffTreatment: (lineCompliance.line.tt || defaultTt)?.code as TariffTreatmentCode,
          unitOfMeasure: (lineCompliance.line.unitOfMeasure || UnitOfMeasure.NUMBER).toUpperCase(),
          countryOfOrigin:
            lineCompliance.line.origin?.alpha2 === "US"
              ? `U${lineCompliance.line.originState?.alpha2}`
              : lineCompliance.line.origin?.alpha2,
          quantity: lineCompliance.line.quantity,
          valueForConversion: lineCompliance.line.totalLineValue,
          discount: 0,
          netValueForConversion: lineCompliance.line.totalLineValue,
          unitPrice: lineCompliance.line.unitPrice || 0,
          invoiceDiscountAmount: 0,

          // Details
          gstCode: (lineCompliance.line.gstExemptCode?.code || 1).toString().padStart(3, "0"),
          annexCode: lineCompliance.line.tariffCode?.hsCode || undefined,

          // Special Authority
          orderInCouncil: lineCompliance.line.orderInCouncil || undefined,
          warehouseTimeLimitCode: lineCompliance.line.timeLimitType || undefined,
          warehouseTimeLimitStart: lineCompliance.line.timeLimitStartDate
            ? MapperFacade.dateToCandataDate(lineCompliance.line.timeLimitStartDate)
            : undefined,
          warehouseTimeLimitEnd: lineCompliance.line.timeLimitEndDate
            ? MapperFacade.dateToCandataDate(lineCompliance.line.timeLimitEndDate)
            : undefined,
          remission: lineCompliance.line.authorityPermit || undefined,
          dutiesReliefLicence: lineCompliance.line.dutiesReliefLicence || undefined,

          // Duties and Taxes
          exciseTaxCode: lineCompliance.line.exciseTaxCode?.code || undefined,
          pstTobaccoAmount: lineCompliance.line.provincialTobaccoTax || undefined,
          pstCannabis: lineCompliance.line.provincialCannabisExciseDuty || undefined,
          alcoholPercentage: lineCompliance.line.alcoholPercentage || undefined,
          province: lineCompliance.line.destinationProvince || undefined,

          // SIMA
          simaSubjectCode: targetSimaFiling?.subjectCode || SimaSubjectCode.NON_SUBJECT,
          sima: [SimaSubjectCode.SUBJECT, SimaSubjectCode.UNDERTAKING].includes(targetSimaFiling?.subjectCode)
            ? targetSimaFiling.simaCode?.code?.toString()
            : undefined,
          measureInForce: targetSimaFiling?.measureInForce?.code || undefined,
          simaIncoterms:
            ([SimaSubjectCode.SUBJECT, SimaSubjectCode.UNDERTAKING].includes(targetSimaFiling?.subjectCode) &&
              targetSimaFiling?.incoterms) ||
            undefined,
          simaSecurity: [SimaSubjectCode.SUBJECT, SimaSubjectCode.UNDERTAKING].includes(
            targetSimaFiling?.subjectCode
          )
            ? targetSimaFiling.security
            : undefined,
          simaUnitOfMeasure:
            ([SimaSubjectCode.SUBJECT, SimaSubjectCode.UNDERTAKING].includes(targetSimaFiling?.subjectCode) &&
              lineCompliance.line.simaUnitOfMeasure?.toUpperCase()) ||
            undefined,
          simaQuantity:
            ([SimaSubjectCode.SUBJECT, SimaSubjectCode.UNDERTAKING].includes(targetSimaFiling?.subjectCode) &&
              lineCompliance.line.quantity) ||
            undefined,

          // Surtax and Safeguard
          surtaxSubjectCode: lineCompliance.line.surtaxSubjectCode || undefined,
          surtaxCode: lineCompliance.line.surtaxCode || undefined,
          safeguardSubjectCode: lineCompliance.line.safeguardSubjectCode || undefined,
          safeguardCode: lineCompliance.line.safeguardCode || undefined,

          // OGD
          pgas: filteredOgdFilings
            .map((filing) => {
              const perUnitMeasurement = (lineCompliance.line.measurements || []).find((m) =>
                UNIT_OF_MEASURE_TYPE_MAP[UnitOfMeasureType.PER_UNIT].includes(m.unitOfMeasure)
              );
              const ogdProgram = filing.ogd?.program;
              switch (filing.ogd?.agency) {
                case CanadaGovernmentAgency.CFIA:
                  return {
                    type: 1,
                    subType: "Food",
                    unitCount: !filing.isExcluded
                      ? perUnitMeasurement?.value || lineCompliance.line.quantity || 0
                      : undefined,
                    unitType: !filing.isExcluded
                      ? (perUnitMeasurement?.unitOfMeasure === UnitOfMeasure.NUMBER_OF_PACKAGES
                          ? UnitOfMeasure.NUMBER
                          : perUnitMeasurement?.unitOfMeasure || UnitOfMeasure.NUMBER
                        )?.toUpperCase()
                      : undefined,
                    excluded: filing.isExcluded,
                    pgadetails: !filing.isExcluded
                      ? [
                          {
                            detailType: "A01",
                            descriptionCode: filing.extensionCode || ""
                          },
                          {
                            detailType: "A02",
                            descriptionCode: filing.endUse || ""
                          },
                          {
                            detailType: "A03"
                          }
                        ]
                      : [],
                    lpcos:
                      !filing.isExcluded && filing.airsType
                        ? [
                            {
                              documentType: filing.airsType,
                              referenceNumber: filing.airsReferenceNumber || "",
                              documentDesc: filing.airsType === "893" ? "Safe Food for Canadians Licence" : ""
                            }
                          ]
                        : []
                  };
                case CanadaGovernmentAgency.GAC:
                  return {
                    type: 3,
                    subType: "Steel",
                    unitCount: !filing.isExcluded
                      ? perUnitMeasurement?.value || lineCompliance.line.quantity || 0
                      : undefined,
                    unitType: !filing.isExcluded
                      ? (perUnitMeasurement?.unitOfMeasure === UnitOfMeasure.NUMBER_OF_PACKAGES
                          ? UnitOfMeasure.NUMBER
                          : perUnitMeasurement?.unitOfMeasure || UnitOfMeasure.NUMBER
                        )?.toUpperCase()
                      : undefined,
                    excluded: filing.isExcluded,
                    lpcos: !filing.isExcluded
                      ? [
                          {
                            documentType: "2006",
                            referenceNumber: filing.generalImportPermit?.toUpperCase() || ""
                          }
                        ]
                      : []
                  };
                case CanadaGovernmentAgency.HC:
                  const qualifier = ogdProgram ? HCProgramMap[ogdProgram] : undefined;

                  if (!qualifier) {
                    this.logger.error(`Unknown OGD program ${ogdProgram}, skipping filing ${filing.id}`);
                    return null;
                  }

                  const programSubTypeMap = {
                    [OGD_HEALTH_CANADA_PROGRAM.CONSUMER_PRODUCT_SAFETY]: {
                      subType: "Products",
                      program: 4
                    }
                  };

                  // we will use new api if it is defined
                  // otherwise, we will use the old format
                  const isUsingNewApi = programSubTypeMap[ogdProgram] !== undefined;

                  if (isUsingNewApi) {
                    return {
                      type: 12,
                      subType: programSubTypeMap[ogdProgram]?.subType,
                      excluded: filing.isExcluded,
                      selection: filing.isExcluded ? "Excluded" : "Choose",
                      program: programSubTypeMap[ogdProgram]?.program,
                      endUse: filing.endUse || "",
                      pgadetails: !filing.isExcluded
                        ? [
                            {
                              detailType: qualifier || "",
                              descriptionCode: filing.productCategory || ""
                            }
                          ]
                        : []
                    };
                  } else {
                    return {
                      type: 12,
                      subType: "HealthExcluded",
                      excluded: filing.isExcluded,
                      selection: filing.isExcluded ? "Excluded" : "Choose",
                      endUse: filing.endUse || "",
                      pgadetails: !filing.isExcluded
                        ? [
                            {
                              detailType: "HC00",
                              descriptionCode: filing.productCategory || ""
                            }
                          ]
                        : []
                    };
                  }
                case CanadaGovernmentAgency.TC:
                  return {
                    type: 13,
                    subType: "Tires",
                    excluded: true,
                    endUse: !filing.isExcluded ? filing.endUse : "",
                    pgadetails: !filing.isExcluded
                      ? [
                          {
                            detailType: "223",
                            description: filing.brandName || ""
                          },
                          {
                            detailType: "TC01",
                            descriptionCode: filing.tireClass || ""
                          },
                          {
                            detailType: "TC02",
                            descriptionCode: filing.tireType || ""
                          },
                          {
                            detailType: "TC03",
                            descriptionCode: filing.tireSize || ""
                          }
                        ]
                      : [],
                    parties:
                      !filing.isExcluded && filing.manufacturer
                        ? [
                            {
                              type: "MF",
                              ...this.toCandataParty(filing.manufacturer)
                            }
                          ]
                        : [],
                    exceptions: !filing.isExcluded
                      ? [
                          {
                            detailType: "XT01"
                          }
                        ]
                      : [],
                    compliances: !filing.isExcluded
                      ? [
                          {
                            detailType: filing.tireCompliance || ""
                          }
                        ]
                      : []
                  };
                case CanadaGovernmentAgency.NRCAN:
                  return {
                    type: 21,
                    subType: !filing.isExcluded ? filing.nrcanSubType : NRCANSubType.ENERGY_EFFICIENCY,
                    endUse:
                      !filing.isExcluded && filing.nrcanSubType === NRCANSubType.EXPLOSIVES
                        ? "NR04"
                        : undefined,
                    excluded: filing.isExcluded,
                    pgadetails: !filing.isExcluded
                      ? filing.nrcanSubType === NRCANSubType.ENERGY_EFFICIENCY
                        ? [
                            {
                              detailType: "221"
                            },
                            {
                              detailType: "57"
                            },
                            {
                              detailType: "223"
                            },
                            {
                              detailType: "NR01",
                              descriptionCode: filing.isRegulated ? "NR02" : "NR01"
                            }
                          ]
                        : [
                            {
                              detailType: "57"
                            },
                            {
                              detailType: "EXP"
                            },
                            {
                              detailType: "UN1"
                            }
                          ]
                      : []
                  };
                case CanadaGovernmentAgency.ECCC:
                  return {
                    type: 22,
                    subType: !filing.isExcluded ? filing.ecccSubType : ECCCSubType.EMISSIONS,
                    excluded: filing.isExcluded,
                    pgadetails: !filing.isExcluded
                      ? ((subType) => {
                          switch (subType) {
                            case ECCCSubType.WILDLIFE:
                              return [
                                {
                                  detailType: "209"
                                },
                                {
                                  detailType: "212"
                                },
                                {
                                  detailType: "221"
                                },
                                {
                                  detailType: "250"
                                },
                                {
                                  detailType: "60"
                                },
                                {
                                  detailType: "APH"
                                },
                                {
                                  detailType: "CV"
                                },
                                {
                                  detailType: "CW"
                                },
                                {
                                  detailType: "CX"
                                },
                                {
                                  detailType: "CY"
                                },
                                {
                                  detailType: "CZ"
                                },
                                {
                                  detailType: "EC03"
                                },
                                {
                                  detailType: "ML"
                                },
                                {
                                  detailType: "TSN"
                                }
                              ];
                            case ECCCSubType.EMISSIONS:
                              return [
                                {
                                  detailType: "221"
                                },
                                {
                                  detailType: "223"
                                },
                                {
                                  detailType: "228"
                                },
                                {
                                  detailType: "CW"
                                },
                                {
                                  detailType: "EC01"
                                },
                                {
                                  detailType: "EC02"
                                },
                                {
                                  detailType: "EC04"
                                },
                                {
                                  detailType: "EE"
                                },
                                {
                                  detailType: "EFN"
                                },
                                {
                                  detailType: "EVN"
                                },
                                {
                                  detailType: "VTG"
                                }
                              ];
                            default:
                              return [];
                          }
                        })(filing.ecccSubType)
                      : [],
                    compliances:
                      !filing.isExcluded && filing.ecccSubType === ECCCSubType.WILDLIFE
                        ? [
                            {
                              detailType: filing.wildlifeCompliance || ""
                            }
                          ]
                        : [],
                    exceptions:
                      !filing.isExcluded && filing.ecccSubType === ECCCSubType.EMISSIONS
                        ? [
                            {
                              detailType: filing.emissionProgram || ""
                            }
                          ]
                        : []
                  };
                default:
                  this.logger.warn(
                    `Unknown PGA type for agency ${filing.ogd?.agency}, skipping filing ${filing.id}`
                  );
                  return null;
              }
            })
            .filter((filing) => filing !== null)
        });
      }

      // If some existing Candata CCI line is not matched to any invoice line, set its product number to "DELETE" and HS code to all 0's
      cciLines.push(
        ...(existingCandataCci?.invoiceSummary?.details || [])
          .filter((existingLine) => !cciLines.some((newLine) => newLine.id === existingLine.id))
          .map((existingLine) => ({
            id: existingLine.id,
            productNumber: EntrySubmissionService.DELETE_KEY,
            classification: "0000000000",
            description: EntrySubmissionService.DELETE_KEY,
            quantity: 0,
            unitPrice: 0,
            valueForConversion: 0,
            discount: 0,
            netValueForConversion: 0,
            invoiceDiscountAmount: 0
          }))
      );

      // Push the mapped CCI to the list
      candataCcis.push({
        id: existingCandataCci?.id || undefined,
        vendor: invoiceVendorMap.get(ci.id),
        consignee: invoiceConsigneeMap.get(ci.id),
        purchaseOrder: ci.poNumber || ci.invoiceNumber,
        purchaseDate: MapperFacade.dateToCandataDate(shipment.etd),
        directShipmentMode,
        directShipmentLocation: shipment.portOfLoading?.name || "",
        currency: ci.currency.toUpperCase(),
        grossWeight: ci.grossWeight || 0,
        measurement: ci.grossWeight || 0,
        measurementUOM: (ci.weightUOM || WeightUOM.KILOGRAM).toUpperCase(),
        numberOfPackages: ci.numberOfPackages || 0,
        packageUOM: (ci.packageUOM || PackageUOM.BOX).toUpperCase(),
        countryOfExport:
          ci.countryOfExport?.alpha2 === "US" ? `U${ci.stateOfExport?.alpha2}` : ci.countryOfExport?.alpha2,
        countryDirectShipment:
          ci.countryOfExport?.alpha2 === "US" ? `U${ci.stateOfExport?.alpha2}` : ci.countryOfExport?.alpha2,
        transportationChargesIncluded: ci.includedTransCost || 0,
        transportationChargesExcluded: ci.excludedTransCost || 0,
        constructionChargesIncluded: ci.includedMiscCost || 0,
        commissionsExcluded: ci.excludedMiscCost || 0,
        exportPackingIncluded: ci.includedPackCost || 0,
        exportPackingExcluded: ci.excludedPackCost || 0,
        invoiceSummary: {
          invoiceNumber: ci.id.toString(),
          placeOfExport:
            ci.countryOfExport?.alpha2 === "US" ? `U${ci.stateOfExport?.alpha2}` : ci.countryOfExport?.alpha2,
          invoiceAmount: invoiceCompliance.lineCompliances.reduce(
            (total, lineCompliance) => total + (lineCompliance.line.totalLineValue || 0),
            0
          ),
          details: cciLines
        }
      });
    }

    // If some existing Cnadata CCI is not matched to any invoice, set its invoice number to "DELETE"
    candataCcis.push(
      ...(candataShipment?.ccis || [])
        .filter((existingCci) => !candataCcis.some((newCci) => newCci.id === existingCci.id))
        .map((existingCci) => ({
          id: existingCci.id,
          purchaseOrder: EntrySubmissionService.DELETE_KEY,
          invoiceSummary: {
            invoiceNumber: EntrySubmissionService.DELETE_KEY,
            details: (existingCci?.invoiceSummary?.details || []).map(() => ({
              productNumber: EntrySubmissionService.DELETE_KEY,
              hsCode: "0000000000",
              description: EntrySubmissionService.DELETE_KEY,
              quantity: 0,
              unitPrice: 0,
              valueForConversion: 0,
              discount: 0,
              netValueForConversion: 0,
              invoiceDiscountAmount: 0
            }))
          }
        }))
    );

    return candataCcis;
  }

  /**
   * Get the mappings between the IDs of the Candata CCI and the commercial invoices, as well as the Candata CCI details and the commercial invoice lines
   * @param candataShipment - The Candata shipment to match the IDs to
   * @param commercialInvoiceList - The list of commercial invoices to match the IDs to
   * @return The ID mappings
   */
  private matchCandataIdsToCommercialInvoices(
    candataShipment: CandataShipmentDto,
    commercialInvoiceList: Array<CommercialInvoice>
  ) {
    const candataLineIdMapping = new Map<number, number>();
    const candataInvoiceIdMapping = new Map<number, number>();
    for (const ci of commercialInvoiceList) {
      // Get Candata CCI by ID first, if not found, use invoice number instead
      const candataCciById = ci.candataId
        ? (candataShipment?.ccis || []).find((cci) => cci?.id === ci.candataId)
        : null;
      const candataCciByInvoiceNumber = (candataShipment?.ccis || []).find(
        (cci) => cci?.invoiceSummary?.invoiceNumber === ci.id.toString()
      );
      const candataCci = candataCciById || candataCciByInvoiceNumber || null;
      candataInvoiceIdMapping.set(ci.id, candataCci?.id ?? null);

      for (const line of ci.commercialInvoiceLines) {
        // Get Candata CCI line by ID first, if not found, use product number instead
        const candataLineById = line.candataId
          ? (candataCci?.invoiceSummary?.details || []).find((ciDetail) => ciDetail.id === line.candataId)
          : null;
        const candataLineByProductNumber = (candataCci?.invoiceSummary?.details || []).find(
          (ciDetail) => ciDetail.productNumber === `CLAROLINE${line.id}`
        );
        const candataLine = candataLineById || candataLineByProductNumber || null;

        candataLineIdMapping.set(line.id, candataLine?.id ?? null);
      }
    }

    return {
      candataLineIdMapping,
      candataInvoiceIdMapping
    };
  }

  /**
   * Update the shipment entry. Note that this function only updates shipment CIs.
   * @param shipmentOrShipmentId - The shipment or its ID to update
   * @param queryRunner - The query runner to use
   */
  async updateShipmentEntry(shipmentOrShipmentId: Shipment | number, queryRunner?: QueryRunner) {
    const shipmentRepository = queryRunner
      ? queryRunner.manager.getRepository(Shipment)
      : this.shipmentRepository;

    const shipment =
      typeof shipmentOrShipmentId === "number"
        ? await this.shipmentService.getShipmentById(shipmentOrShipmentId, queryRunner)
        : shipmentOrShipmentId;
    if (!shipment) throw new NotFoundException("Shipment not found");
    if (!this.complianceValidationService.isShipmentEntryUploaded(shipment))
      throw new BadRequestException("Shipment is not yet uploaded");
    if (!this.complianceValidationService.canShipmentUpdateEntry(shipment))
      throw new BadRequestException(
        "Shipment entry cannot be updated, potentially because shipment accounting is completed or transaction number is missing"
      );

    const enrichedShipment = await this.enricher.enrich(shipment);

    // If shipment is from a demo organization, skip filings validation
    const skipFilingsValidation = this.complianceValidationService.isDemoShipment(enrichedShipment);

    // Get all compliances info for the shipment
    const shipmentCompliance = (
      await this.complianceValidationService.getShipmentCompliances([enrichedShipment], queryRunner)
    )[0];
    const commercialInvoiceList = shipmentCompliance.invoiceCompliances.map((ic) => ic.commercialInvoice);

    // Get shipment compliance validations
    const validationResult = this.complianceValidationService.validateShipmentCompliances(
      [shipmentCompliance],
      skipFilingsValidation
    )[0];

    // If shipment has no commercial invoice, set shipment to requires re-upload
    if (validationResult.noCommercialInvoice) {
      await shipmentRepository.update({ id: shipment.id }, { requiresReupload: true });
      throw new NotFoundException("Shipment has no commercial invoices");
    }

    // If shipment has non-compliant invoices, set shipment to require re-upload and throw error
    for (const invoiceValidationResult of validationResult.nonCompliantInvoices) {
      // Set shipment to require re-upload
      await shipmentRepository.update({ id: shipment.id }, { requiresReupload: true });

      // Get related commercial invoice
      const relatedCommercialInvoice = commercialInvoiceList.find(
        (ci) => ci.id === invoiceValidationResult.commercialInvoiceId
      );

      // Check if commercial invoice has missing fields
      if (invoiceValidationResult.missingFields.length > 0)
        throw new BadRequestException(
          `The following fields in commercial invoice ${relatedCommercialInvoice?.invoiceNumber} ${invoiceValidationResult.missingFields.length > 1 ? "are" : "is"} missing: ${invoiceValidationResult.missingFields.map((field) => field.replace(/Id$/, "")).join(", ")}`
        );

      // Check if shipTo and vendor has any missing fields
      if (invoiceValidationResult.shipToMissingFields.length > 0)
        throw new BadRequestException(
          `The following fields in shipTo ${relatedCommercialInvoice.shipTo?.name} are missing: ${invoiceValidationResult.shipToMissingFields.map((field) => field.replace(/Id$/, "")).join(", ")}`
        );
      if (invoiceValidationResult.vendorMissingFields.length > 0)
        throw new BadRequestException(
          `The following fields in vendor ${relatedCommercialInvoice.vendor?.name} are missing: ${invoiceValidationResult.vendorMissingFields.map((field) => field.replace(/Id$/, "")).join(", ")}`
        );

      // Check if any lines have invalid HS code
      const invalidHsCodeLineIds = invoiceValidationResult.nonCompliantLines
        .filter((lineValidationResult) => lineValidationResult.isHsCodeInvalid)
        .map((lineValidationResult) => lineValidationResult.lineId);
      if (invalidHsCodeLineIds.length > 0)
        throw new BadRequestException(
          `Some commercial invoice lines in invoice ${relatedCommercialInvoice.invoiceNumber} have invalid HS codes, please check again. Invalid HS code lines: ${invalidHsCodeLineIds.join(", ")}`
        );

      // Check if any lines have invalid quantity
      const invalidQuantityLineIds = invoiceValidationResult.nonCompliantLines
        .filter((lineValidationResult) => lineValidationResult.isQuantityInvalid)
        .map((lineValidationResult) => lineValidationResult.lineId);
      if (invalidQuantityLineIds.length > 0)
        throw new BadRequestException(
          `Some commercial invoice lines in invoice ${relatedCommercialInvoice.invoiceNumber} have invalid quantities, please check again. Invalid quantity lines: ${invalidQuantityLineIds.join(", ")}`
        );

      // Check if any lines have non-compliant records
      const nonCompliantLineIds = invoiceValidationResult.nonCompliantLines
        .filter((lineValidationResult) => lineValidationResult.nonCompliantRecords.length > 0)
        .map((lineValidationResult) => lineValidationResult.lineId);
      if (nonCompliantLineIds.length > 0)
        throw new BadRequestException(
          `Some commercial invoice lines in invoice ${relatedCommercialInvoice.invoiceNumber} are not compliant, please check again. Non-compliant lines: ${nonCompliantLineIds.join(", ")}`
        );
    }

    // Convert shipTo and vendor in invoices to Candata Party format
    const { invoiceVendorMap, invoiceConsigneeMap } = this.mapInvoiceTradePartners(shipmentCompliance);

    try {
      // Get Candata shipment identifier because of a bug in Candata which might generate incomplete transaction number
      let candataShipmentIdentifier = this.complianceValidationService.getCandataShipmentIdentifier(
        shipment.transactionNumber,
        shipment.customsFileNumber
      );

      // Get existing Candata shipment
      const candataShipment = await this.candataService.getCandataShipment(
        candataShipmentIdentifier,
        shipment.organization?.customsBroker
      );
      this.logger.debug(`Candata shipment: ${JSON.stringify(candataShipment)}`);

      let updatedCandataShipment: CandataShipmentDto | null = null;
      try {
        updatedCandataShipment = await this.candataService.updateCandataShipment(candataShipmentIdentifier, {
          billOfLading: shipment.hblNumber,
          weight: shipment.weight
            ? this.unitConversionService.convert(shipment.weight, shipment.weightUOM, "kg")
            : undefined,
          volume: shipment.volume
            ? this.unitConversionService.convert(shipment.volume, shipment.volumeUOM, "m3").toFixed(2)
            : undefined,
          ccis: await this.mapCandataCcis(
            shipmentCompliance.invoiceCompliances,
            invoiceVendorMap,
            invoiceConsigneeMap,
            shipment,
            candataShipment,
            queryRunner
          )
        });
      } catch (error) {
        this.logger.error(`Got error while updating Candata shipment: ${error.message}`);
        this.logger.error(error);
      }

      this.logger.log(
        `Updated Candata shipment: ${updatedCandataShipment?.transactionNumber}, ${updatedCandataShipment?.fileNumber}`
      );
      if (
        !updatedCandataShipment ||
        !updatedCandataShipment?.transactionNumber ||
        !updatedCandataShipment?.fileNumber
      )
        throw new BadRequestException("Failed to update Candata shipment");

      // Get latest Candata shipment identifier in case something changes (which should not happen)
      candataShipmentIdentifier = this.complianceValidationService.getCandataShipmentIdentifier(
        updatedCandataShipment?.transactionNumber,
        updatedCandataShipment?.fileNumber
      );

      // If any CCI or CCI detail is marked as delete, call delete Candata CCI API
      const hasDeletedCcisOrCciDetails = (updatedCandataShipment?.ccis || []).some(
        (cci) =>
          cci?.purchaseOrder === EntrySubmissionService.DELETE_KEY ||
          (cci?.invoiceSummary?.details || []).some(
            (detail) => detail?.productNumber === EntrySubmissionService.DELETE_KEY
          )
      );
      if (hasDeletedCcisOrCciDetails)
        await this.tryDeletingCandataCci(
          updatedCandataShipment.transactionNumber,
          shipment.organization?.customsBroker
        );

      // Clear Candata CCI errors
      await this.tryClearingCandataCciErrors(
        updatedCandataShipment.transactionNumber,
        shipment.organization?.customsBroker
      );

      // Calculate CAD amounts for the shipment
      const { dutiesAndTaxesMapping } = await this.performDutyAndTaxCalculation(
        candataShipmentIdentifier,
        shipment.organization?.customsBroker
      );

      // Get candata ID for each CI and CI line
      const { candataLineIdMapping, candataInvoiceIdMapping } = this.matchCandataIdsToCommercialInvoices(
        updatedCandataShipment,
        commercialInvoiceList
      );

      // Update shipment and commercial invoices
      const tQueryRunner = queryRunner || this.dataSource.createQueryRunner();
      if (!queryRunner) {
        await tQueryRunner.connect();
        await tQueryRunner.startTransaction();
      }

      try {
        // Update shipment
        await tQueryRunner.manager.update(
          Shipment,
          { id: shipment.id },
          {
            requiresReupload: false,
            customsStatus: [
              CustomsStatus.LIVE,
              CustomsStatus.ENTRY_SUBMITTED,
              CustomsStatus.ENTRY_ACCEPTED
            ].includes(shipment.customsStatus)
              ? CustomsStatus.LIVE
              : shipment.customsStatus
          }
        );

        // Update commercial invoices' Candata ID
        for (const [invoiceId, candataInvoiceId] of candataInvoiceIdMapping.entries()) {
          if (!candataInvoiceId) continue;
          await tQueryRunner.manager.update(
            CommercialInvoice,
            { id: invoiceId },
            { candataId: candataInvoiceId }
          );
        }

        // Update commercial invoice lines' Candata ID
        for (const [lineId, candataLineId] of candataLineIdMapping.entries()) {
          if (!candataLineId) continue;
          const extractedDutiesAndTaxes = dutiesAndTaxesMapping.get(candataLineId);
          await tQueryRunner.manager.update(
            CommercialInvoiceLine,
            { id: lineId },
            {
              candataId: candataLineId,
              ...(extractedDutiesAndTaxes || {})
            }
          );
        }

        if (!queryRunner) await tQueryRunner.commitTransaction();
      } catch (tError) {
        if (!queryRunner && tQueryRunner.isTransactionActive) await tQueryRunner.rollbackTransaction();
        throw tError;
      } finally {
        if (!queryRunner) await tQueryRunner.release();
      }

      return await this.shipmentService.getShipmentById(shipment.id, queryRunner);
    } catch (error) {
      this.logger.error(
        `Got error while updating shipment entry: ${error instanceof Error ? error.message : String(error)}`
      );
      await this.sendShipmentEntryErrorEmail(
        new EntrySubmissionErrorEmailDto({
          shipmentId: shipment.id,
          shipmentUrl: `${this.PORTAL_FRONTEND_URL}/shipment/${shipment.id}`,
          transactionNumber: shipment?.transactionNumber || undefined,
          fileNumber: shipment?.customsFileNumber || undefined,
          errorMessage: error instanceof Error ? error.message : String(error),
          stackTrace: error instanceof Error ? error.stack : undefined
        })
      );
      throw error;
    }
  }

  async submitShipmentEntry(shipmentOrShipmentId: Shipment | number, queryRunner?: QueryRunner) {
    //#region magic number for debugging
    const DEBUG_VIEW_PAYLOAD_ONLY = "CLARO-0000";
    //#endregion

    const shipmentRepository = queryRunner
      ? queryRunner.manager.getRepository(Shipment)
      : this.shipmentRepository;

    const shipment: Shipment =
      typeof shipmentOrShipmentId === "number"
        ? await this.shipmentService.getShipmentById(shipmentOrShipmentId, queryRunner)
        : shipmentOrShipmentId;

    if (!shipment) {
      throw new NotFoundException("Shipment not found");
    }

    if (this.complianceValidationService.isShipmentEntryUploaded(shipment)) {
      throw new BadRequestException("Shipment entry is already uploaded");
    }

    // enrich shipment
    const enrichedShipment = await this.enricher.enrich(shipment);

    // Get all compliances info for the shipment
    const shipmentCompliance = (
      await this.complianceValidationService.getShipmentCompliances([enrichedShipment], queryRunner)
    )[0];
    const commercialInvoiceList = shipmentCompliance.invoiceCompliances.map((ic) => ic.commercialInvoice);

    // If shipment is from a demo organization, skip filings validation
    const skipFilingsValidation = this.complianceValidationService.isDemoShipment(enrichedShipment);

    // Get shipment compliance validations
    const validationResult = this.complianceValidationService.validateShipmentCompliances(
      [shipmentCompliance],
      skipFilingsValidation
    )[0];

    // If no commercial invoice, set shipment to pending commercial invoice
    if (validationResult.noCommercialInvoice) {
      await shipmentRepository.update(
        { id: shipment.id },
        { customsStatus: CustomsStatus.PENDING_COMMERCIAL_INVOICE }
      );
      throw new NotFoundException("Shipment has no commercial invoices");
    }

    // If shipment has missing fields, set shipment to pending confirmation
    if (validationResult.missingFields.length > 0) {
      await shipmentRepository.update(
        { id: shipment.id },
        { customsStatus: CustomsStatus.PENDING_CONFIRMATION }
      );
      throw new BadRequestException(
        `The following fields in shipment are missing: ${validationResult.missingFields.map((field) => field.replace(/Id$/, "")).join(", ")}`
      );
    }

    // If shipment has non-compliant invoices, set shipment to pending confirmation and throw error
    for (const invoiceValidationResult of validationResult.nonCompliantInvoices) {
      // Set shipment to pending confirmation
      await shipmentRepository.update(
        { id: shipment.id },
        { customsStatus: CustomsStatus.PENDING_CONFIRMATION }
      );

      // Get related commercial invoice
      const relatedCommercialInvoice = commercialInvoiceList.find(
        (ci) => ci.id === invoiceValidationResult.commercialInvoiceId
      );

      // Check if commercial invoice has missing fields
      if (invoiceValidationResult.missingFields.length > 0)
        throw new BadRequestException(
          `The following fields in commercial invoice ${relatedCommercialInvoice?.invoiceNumber} ${invoiceValidationResult.missingFields.length > 1 ? "are" : "is"} missing: ${invoiceValidationResult.missingFields.map((field) => field.replace(/Id$/, "")).join(", ")}`
        );

      // Check if shipTo and vendor has any missing fields
      if (invoiceValidationResult.shipToMissingFields.length > 0)
        throw new BadRequestException(
          `The following fields in shipTo ${relatedCommercialInvoice.shipTo?.name} are missing: ${invoiceValidationResult.shipToMissingFields.map((field) => field.replace(/Id$/, "")).join(", ")}`
        );
      if (invoiceValidationResult.vendorMissingFields.length > 0)
        throw new BadRequestException(
          `The following fields in vendor ${relatedCommercialInvoice.vendor?.name} are missing: ${invoiceValidationResult.vendorMissingFields.map((field) => field.replace(/Id$/, "")).join(", ")}`
        );

      // Check if any lines have invalid HS code
      const invalidHsCodeLineIds = invoiceValidationResult.nonCompliantLines
        .filter((lineValidationResult) => lineValidationResult.isHsCodeInvalid)
        .map((lineValidationResult) => lineValidationResult.lineId);
      if (invalidHsCodeLineIds.length > 0)
        throw new BadRequestException(
          `Some commercial invoice lines in invoice ${relatedCommercialInvoice.invoiceNumber} have invalid HS codes, please check again. Invalid HS code lines: ${invalidHsCodeLineIds.join(", ")}`
        );

      // Check if any lines have invalid quantity
      const invalidQuantityLineIds = invoiceValidationResult.nonCompliantLines
        .filter((lineValidationResult) => lineValidationResult.isQuantityInvalid)
        .map((lineValidationResult) => lineValidationResult.lineId);
      if (invalidQuantityLineIds.length > 0)
        throw new BadRequestException(
          `Some commercial invoice lines in invoice ${relatedCommercialInvoice.invoiceNumber} have invalid quantities, please check again. Invalid quantity lines: ${invalidQuantityLineIds.join(", ")}`
        );

      // check if any lines have missing state of origin
      const missingStateOfOriginLineIds = invoiceValidationResult.nonCompliantLines
        .filter((lineValidationResult) => lineValidationResult.isUsStateOfOriginNotSet)
        .map((lineValidationResult) => lineValidationResult.lineId);
      if (missingStateOfOriginLineIds.length > 0)
        throw new BadRequestException(
          `Some commercial invoice lines in invoice ${relatedCommercialInvoice.invoiceNumber} have missing state of origin, please check again. Missing state of origin lines: ${missingStateOfOriginLineIds.join(", ")}`
        );

      // Check if any lines have non-compliant records
      const nonCompliantLineIds = invoiceValidationResult.nonCompliantLines
        .filter((lineValidationResult) => lineValidationResult.nonCompliantRecords.length > 0)
        .map((lineValidationResult) => lineValidationResult.lineId);
      if (nonCompliantLineIds.length > 0)
        throw new BadRequestException(
          `Some commercial invoice lines in invoice ${relatedCommercialInvoice.invoiceNumber} are not compliant, please check again. Non-compliant lines: ${nonCompliantLineIds.join(", ")}`
        );
    }

    // Convert shipTo and vendor in invoices to Candata Party format
    const { invoiceVendorMap, invoiceConsigneeMap } = this.mapInvoiceTradePartners(shipmentCompliance);

    let newCandataShipment: CandataShipmentDto | null = null;
    try {
      // Get importer
      const importer = await this.getShipmentImporter(shipment, queryRunner);

      const payload = {
        containers: (shipment.containers || []).map((c) => c.containerNumber).join(","),
        billOfLading: shipment.hblNumber,
        customerNumber: importer?.candataCustomerNumber,
        cargoControlNumbers: shipment.cargoControlNumber,
        port: shipment.portCode,
        // number of containers in candata is actually the quantity of the whole shipment
        numberOfContainers: (shipment.quantity || 1).toString(),
        weight: enrichedShipment.weight
          ? this.unitConversionService.convert(enrichedShipment.weight, enrichedShipment.weightUOM, "kg")
          : undefined,
        volume: enrichedShipment.volume
          ? this.unitConversionService
              .convert(enrichedShipment.volume, enrichedShipment.volumeUOM, "m3")
              .toFixed(2)
          : undefined,
        etaDate: MapperFacade.dateToCandataDate(shipment.etaDestination),
        directShipmentDate: MapperFacade.dateToCandataDate(shipment.etd),
        sublocationCode: shipment.subLocation || "",
        vendor: invoiceVendorMap.get(commercialInvoiceList[0].id),
        ccis: await this.mapCandataCcis(
          shipmentCompliance.invoiceCompliances,
          invoiceVendorMap,
          invoiceConsigneeMap,
          shipment,
          null,
          queryRunner
        )
      };

      //#region submit shipment debug
      if (shipment.mblNumber === DEBUG_VIEW_PAYLOAD_ONLY) {
        throw new BadRequestException({
          message: "Please ignore this error, this is for debugging only",
          payload: payload
        });
      }
      //#endregion

      try {
        newCandataShipment = await this.candataService.createCandataShipment(
          payload,
          shipment.organization?.customsBroker
        );
      } catch (error) {
        this.logger.error(`Got error while creating new Candata shipment: ${error.message}`);
        this.logger.error(error);
      }

      this.logger.log(
        `Candata Shipment: ${newCandataShipment?.transactionNumber}, ${newCandataShipment?.fileNumber}`
      );
      if (!newCandataShipment || !newCandataShipment?.transactionNumber || !newCandataShipment?.fileNumber)
        throw new BadRequestException("Failed to create new Candata shipment");

      // Get Candata shipment identifier because of a bug in Candata which might generate incomplete transaction number
      const candataShipmentIdentifier = this.complianceValidationService.getCandataShipmentIdentifier(
        newCandataShipment?.transactionNumber,
        newCandataShipment?.fileNumber
      );

      // Clear Candata CCI errors
      await this.tryClearingCandataCciErrors(
        newCandataShipment.transactionNumber,
        shipment.organization?.customsBroker
      );

      // Calculate CAD amounts for the shipment
      const { dutiesAndTaxesMapping } = await this.performDutyAndTaxCalculation(
        candataShipmentIdentifier,
        shipment.organization?.customsBroker
      );

      // Get candata ID for each CI and CI line
      const { candataLineIdMapping, candataInvoiceIdMapping } = this.matchCandataIdsToCommercialInvoices(
        newCandataShipment,
        commercialInvoiceList
      );

      const tQueryRunner = queryRunner || this.dataSource.createQueryRunner();
      if (!queryRunner) {
        await tQueryRunner.connect();
        await tQueryRunner.startTransaction();
      }

      try {
        await tQueryRunner.manager.update(
          Shipment,
          { id: shipment.id },
          {
            customsStatus: CustomsStatus.LIVE,
            transactionNumber: newCandataShipment?.transactionNumber,
            customsFileNumber: newCandataShipment?.fileNumber
          }
        );

        for (const [invoiceId, candataInvoiceId] of candataInvoiceIdMapping.entries()) {
          if (!candataInvoiceId) continue;
          await tQueryRunner.manager.update(
            CommercialInvoice,
            { id: invoiceId },
            { candataId: candataInvoiceId }
          );
        }

        for (const [lineId, candataLineId] of candataLineIdMapping.entries()) {
          if (!candataLineId) continue;
          const extractedDutiesAndTaxes = dutiesAndTaxesMapping.get(candataLineId);
          await tQueryRunner.manager.update(
            CommercialInvoiceLine,
            { id: lineId },
            {
              candataId: candataLineId,
              ...(extractedDutiesAndTaxes || {})
            }
          );
        }

        // update ci line fields
        for (const ciLine of shipmentCompliance.invoiceCompliances.flatMap(
          (ic) => ic.commercialInvoice.commercialInvoiceLines
        )) {
          await tQueryRunner.manager.update(
            CommercialInvoiceLine,
            { id: ciLine.id },
            {
              tt: {
                id: ciLine.tt?.id
              },
              exciseTaxCode: {
                id: ciLine.exciseTaxCode?.id
              }
            }
          );
        }

        if (!queryRunner) await tQueryRunner.commitTransaction();
      } catch (tError) {
        if (!queryRunner && tQueryRunner.isTransactionActive) await tQueryRunner.rollbackTransaction();
        throw tError;
      } finally {
        if (!queryRunner) await tQueryRunner.release();
      }

      return await this.shipmentService.getShipmentById(shipment.id, queryRunner);
    } catch (error) {
      this.logger.error(
        `Got error while submitting shipment entry: ${error instanceof Error ? error.message : String(error)}`
      );
      await this.sendShipmentEntryErrorEmail(
        new EntrySubmissionErrorEmailDto({
          shipmentId: shipment.id,
          shipmentUrl: `${this.PORTAL_FRONTEND_URL}/shipment/${shipment.id}`,
          transactionNumber: newCandataShipment?.transactionNumber || undefined,
          fileNumber: newCandataShipment?.fileNumber || undefined,
          errorMessage: error instanceof Error ? error.message : String(error),
          stackTrace: error instanceof Error ? error.stack : undefined
        })
      );
      throw error;
    }
  }

  /**
   * Recalculate duties and taxes for all commercial invoices in a shipment.
   * @param shipmentOrShipmentId - Shipment ID or Shipment object
   * @param queryRunner - QueryRunner object
   * @returns Updated shipment object
   */
  async recalculateDutiesAndTaxes(shipmentOrShipmentId: Shipment | number, queryRunner?: QueryRunner) {
    const shipment: Shipment =
      typeof shipmentOrShipmentId === "number"
        ? await this.shipmentService.getShipmentById(shipmentOrShipmentId, queryRunner)
        : shipmentOrShipmentId;
    if (!shipment) throw new NotFoundException("Shipment not found");
    if (!shipment?.transactionNumber && !shipment?.customsFileNumber)
      throw new BadRequestException("Shipment has no transaction number and file number");

    // Get Candata shipment identifier because of a bug in Candata which might generate incomplete transaction number
    const candataShipmentIdentifier = this.complianceValidationService.getCandataShipmentIdentifier(
      shipment?.transactionNumber,
      shipment?.customsFileNumber
    );

    // Get all commercial invoices for the shipment
    const commercialInvoiceList = await (queryRunner ? queryRunner.manager : this.dataSource.manager).find(
      CommercialInvoice,
      {
        where: { shipment: { id: shipment.id } },
        relations: {
          commercialInvoiceLines: true
        }
      }
    );
    this.logger.log(
      `Commercial invoices for shipment: ${shipment?.id}: ${JSON.stringify(commercialInvoiceList.map((ci) => ci.id))}`
    );
    if (commercialInvoiceList.length === 0)
      throw new NotFoundException("Shipment has no commercial invoices");

    // Calculate CAD amounts for the shipment
    const { dutiesAndTaxesMapping, calculatedCandataShipment } = await this.performDutyAndTaxCalculation(
      candataShipmentIdentifier,
      shipment.organization?.customsBroker
    );
    if (!calculatedCandataShipment) throw new BadRequestException("No calculated Candata shipment returned");

    // Get candata ID for each CI and CI line
    const { candataLineIdMapping } = this.matchCandataIdsToCommercialInvoices(
      calculatedCandataShipment,
      commercialInvoiceList
    );

    const tQueryRunner = queryRunner ?? this.dataSource.createQueryRunner();
    if (!queryRunner) {
      await tQueryRunner.connect();
      await tQueryRunner.startTransaction();
    }

    try {
      for (const [lineId, candataLineId] of candataLineIdMapping.entries()) {
        if (!candataLineId) continue;
        const extractedDutiesAndTaxes = dutiesAndTaxesMapping.get(candataLineId);
        await tQueryRunner.manager.update(
          CommercialInvoiceLine,
          { id: lineId },
          {
            candataId: candataLineId,
            ...(extractedDutiesAndTaxes || {})
          }
        );
      }

      if (!queryRunner) await tQueryRunner.commitTransaction();
    } catch (error) {
      if (!queryRunner && tQueryRunner.isTransactionActive) await tQueryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner) await tQueryRunner.release();
    }

    return await this.shipmentService.getShipmentById(shipment.id, queryRunner);
  }
}
