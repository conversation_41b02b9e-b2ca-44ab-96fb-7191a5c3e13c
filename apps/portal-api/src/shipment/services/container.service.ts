import { forwardRef, Inject, Injectable, Scope } from "@nestjs/common";
import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import { AuthenticatedRequest, Container, FIND_CONTAINER_RELATIONS, UserPermission } from "nest-modules";
import { DataSource, IsNull, Not, QueryRunner, Repository } from "typeorm";
import { ShipmentService } from "./shipment.service";
import { REQUEST } from "@nestjs/core";

@Injectable({ scope: Scope.REQUEST })
export class ContainerService {
  constructor(
    @InjectRepository(Container)
    private readonly containerRepository: Repository<Container>,
    @Inject(forwardRef(() => ShipmentService))
    private readonly shipmentService: ShipmentService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  async getContainerById(
    shipmentId: number,
    containerId: number,
    queryRunner?: QueryRunner,
    skipOrganizationCheck = false,
    relations = FIND_CONTAINER_RELATIONS
  ) {
    return await (
      queryRunner ? queryRunner.manager.getRepository(Container) : this.containerRepository
    ).findOne({
      where: {
        id: containerId,
        shipment: {
          id: shipmentId,
          organization: {
            id:
              !skipOrganizationCheck && this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN
                ? this.request?.user?.organization?.id || -1
                : Not(IsNull())
          }
        }
      },
      relations
    });
  }
}
