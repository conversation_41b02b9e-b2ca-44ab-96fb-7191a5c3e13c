import { UnitConversionService } from "@/unit-conversion/unit-conversion.service";
import { Inject, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { CommercialInvoice } from "nest-modules";
import { Repository } from "typeorm";

@Injectable()
export class ShipmentCommercialInvoiceService {
  constructor(
    @InjectRepository(CommercialInvoice)
    private readonly ciRepository: Repository<CommercialInvoice>,
    @Inject(UnitConversionService)
    private readonly unitConversionService: UnitConversionService
  ) {}

  private getMostOccuredElements<T>(elements: T[]): T {
    const elementCount = new Map<T, number>();
    for (const element of elements) {
      elementCount.set(element, (elementCount.get(element) || 0) + 1);
    }
    return Array.from(elementCount.entries()).sort((a, b) => b[1] - a[1])[0][0];
  }

  async getCommercialInvoiceSummary(shipmentId: number): Promise<{
    invoiceCount: number;
    weightUOM: string;
    totalGrossWeight: number;
    packageUOM: string;
    totalNumOfPackages: number;
  }> {
    const commercialInvoices = await this.ciRepository.find({
      where: {
        shipment: { id: shipmentId }
      },
      select: {
        id: true,
        grossWeight: true,
        weightUOM: true,
        numberOfPackages: true,
        packageUOM: true
      }
    });

    if (!commercialInvoices.length) {
      return {
        invoiceCount: 0,
        weightUOM: null,
        totalGrossWeight: 0,
        packageUOM: null,
        totalNumOfPackages: 0
      };
    }

    const majorityWeightUOM = this.getMostOccuredElements(commercialInvoices.map((ci) => ci.weightUOM));
    const majorityPackageUOM = this.getMostOccuredElements(commercialInvoices.map((ci) => ci.packageUOM));
    const totalGrossWeight = commercialInvoices.reduce(
      (acc, ci) => acc + this.unitConversionService.convert(ci.grossWeight, ci.weightUOM, majorityWeightUOM),
      0
    );
    const totalNumOfPackages = commercialInvoices.reduce((acc, ci) => acc + ci.numberOfPackages, 0);

    return {
      invoiceCount: commercialInvoices.length,
      totalGrossWeight,
      weightUOM: majorityWeightUOM,
      totalNumOfPackages,
      packageUOM: majorityPackageUOM
    };
  }
}
