import { Injectable, NotFoundException } from "@nestjs/common";
import { ShipmentService } from "./shipment.service";
import { ComplianceValidationService } from "./compliance-validation.service";
import { OrganizationType, ValidateShipmentComplianceResponseDto } from "nest-modules";
import { QueryRunner } from "typeorm";

@Injectable()
export class ShipmentComplianceQueryService {
  constructor(
    private readonly shipmentService: ShipmentService,
    private readonly complianceValidationService: ComplianceValidationService
  ) {}

  /**
   * Retrieves a detailed compliance validation report for a specific shipment.
   * @param shipmentId The ID of the shipment to validate
   * @returns The compliance validation DTO for the shipment
   */
  async getShipmentComplianceDetails(
    shipmentId: number,
    queryRunner?: QueryRunner
  ): Promise<ValidateShipmentComplianceResponseDto> {
    const shipment = await this.shipmentService.getShipmentById(shipmentId, queryRunner);
    if (!shipment) throw new NotFoundException(`Shipment ${shipmentId} not found.`);
    const shipmentCompliances = await this.complianceValidationService.getShipmentCompliances(
      [shipment],
      queryRunner
    );
    if (!shipmentCompliances.length) {
      throw new NotFoundException(`No compliance data found for shipment ${shipmentId}.`);
    }
    const skipFilingsValidation = shipment.organization?.organizationType === OrganizationType.DEMO;
    const validationResults = this.complianceValidationService.validateShipmentCompliances(
      [shipmentCompliances[0]],
      skipFilingsValidation
    );
    if (!validationResults.length) {
      throw new NotFoundException(`No validation results for shipment ${shipmentId}.`);
    }
    return validationResults[0];
  }
}
