import { Inject, Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import { InjectDataSource } from "@nestjs/typeorm";
import moment from "moment";
import {
  CandataRNSResponseDto,
  CandataService,
  CustomsStatus,
  FIND_SHIPMENT_RELATIONS,
  Organization,
  OrganizationCustomsBroker,
  Shipment,
  ShipmentMode,
  UserPermission
} from "nest-modules";
import { DataSource, In, IsNull, Repository } from "typeorm";
import { CustomsStatusCheckErrorEmailSender } from "../senders/customs-status-check-error-email.sender";
import { EntryNotAcceptedWarningEmailSender } from "../senders/entry-not-accepted-warning-email.sender";
import { LiveEntryUploadFailedEmailSender } from "../senders/live-entry-upload-failed-email.sender";
import { LiveShipmentEmailSender } from "../senders/live-shipment-email.sender";
import { RNSStatusChangeEmailSender } from "../senders/rns-status-change-email.sender";
import { RNS_RESPONSE_CATEGORY_TO_CUSTOMS_STATUS, RNSResponseCategory } from "../types/customs-status.types";
import { categorizeRNSResponse } from "../utils/categorize-rns-response";
import { isValidCCN } from "../utils/is-valid-ccn";
import { ComplianceValidationService } from "./compliance-validation.service";
import { EntrySubmissionService } from "./entry-submission.service";
import { NODE_ENV } from "@/app.module";
import { NodeEnv } from "nest-modules";

@Injectable()
export class CustomStatusService {
  private readonly logger = new Logger(CustomStatusService.name);

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    @Inject(CandataService)
    private readonly candataService: CandataService,
    @Inject(ComplianceValidationService)
    private readonly complianceValidationService: ComplianceValidationService,
    @Inject(ConfigService)
    private readonly configService: ConfigService,
    @Inject(ModuleRef)
    private readonly moduleRef: ModuleRef,
    @Inject(LiveShipmentEmailSender)
    private readonly liveShipmentEmailSender: LiveShipmentEmailSender,
    @Inject(LiveEntryUploadFailedEmailSender)
    private readonly liveEntryUploadFailedEmailSender: LiveEntryUploadFailedEmailSender,
    @Inject(RNSStatusChangeEmailSender)
    private readonly rnsStatusChangeEmailSender: RNSStatusChangeEmailSender,
    @Inject(EntryNotAcceptedWarningEmailSender)
    private readonly entryNotAcceptedWarningEmailSender: EntryNotAcceptedWarningEmailSender,
    @Inject(CustomsStatusCheckErrorEmailSender)
    private readonly customsStatusCheckErrorEmailSender: CustomsStatusCheckErrorEmailSender
  ) {}

  async customsStatusCheck() {
    this.logger.log(`Received customs status check event. Checking all shipments...`);

    // List of shipments that should send live shipment emails
    let liveShipmentIds: Array<number> = [];
    // List of shipments that should send live entry upload failed emails
    let liveEntryUploadFailedShipments: Array<{ shipmentId: number; failedReason: string }> = [];
    // List of shipments that should send RNS status change emails
    let rnsStatusChangeShipments: Array<{ shipmentId: number; rnsResponse: CandataRNSResponseDto }> = [];
    // List of shipments that should send entry not accepted warning emails
    let entryNotAcceptedShipments: Array<{ shipmentId: number; entrySubmittedDate: string }> = [];
    // List of shipments that should send error emails
    let customsStatusCheckErrorShipments: Array<{
      shipmentId: number;
      errorType: string;
      errorMessage: string;
      stackTrace?: string;
    }> = [];

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const shipmentRepository = queryRunner.manager.getRepository(Shipment);

      // Get shipments that needs to check RNS responses
      // 1. Shipment that is not released but uploaded to Candata
      // 2. Shipment that is released but does not have a release date
      const rnsCheckShipmentList = await shipmentRepository.find({
        where: [
          {
            customsStatus: In([
              CustomsStatus.LIVE,
              CustomsStatus.ENTRY_SUBMITTED,
              CustomsStatus.ENTRY_ACCEPTED,
              CustomsStatus.EXAM
            ])
          },
          {
            customsStatus: CustomsStatus.RELEASED,
            releaseDate: IsNull()
          }
        ],
        relations: FIND_SHIPMENT_RELATIONS
      });
      this.logger.log(`Found ${rnsCheckShipmentList.length} shipment to check for RNS responses...`);

      // Filter out shipments that don't have CCN
      const invalidCCNShipments: Array<Shipment> = [];
      // const rnsCheckWithCCNShipments: Array<Shipment> = [];
      const missingCustomsBrokerShipments: Array<Shipment> = [];
      const rnsCheckWithCCNShipmentsByCustomsBroker = new Map<OrganizationCustomsBroker, Array<Shipment>>();
      for (const shipment of rnsCheckShipmentList) {
        if (!isValidCCN(shipment.cargoControlNumber)) invalidCCNShipments.push(shipment);
        else if (!shipment.organization?.customsBroker) missingCustomsBrokerShipments.push(shipment);
        else {
          const brokerShipments =
            rnsCheckWithCCNShipmentsByCustomsBroker.get(shipment.organization?.customsBroker) || [];
          brokerShipments.push(shipment);
          rnsCheckWithCCNShipmentsByCustomsBroker.set(shipment.organization?.customsBroker, brokerShipments);
        }
      }
      this.logger.log(
        `Found ${invalidCCNShipments.length} shipments that do not have a valid CCN and ${missingCustomsBrokerShipments.length} shipments that do not have a customs broker...`
      );

      // Send error email to backoffice for shipments that should check RNS response but don't have CCN
      customsStatusCheckErrorShipments.push(
        ...invalidCCNShipments.map((shipment) => ({
          shipmentId: shipment.id,
          errorType: "Invalid CCN",
          errorMessage: `Shipment ${shipment.id} is in ${shipment.customsStatus} customs status but is missing CCN or has an invalid CCN.`
        }))
      );

      // Send error email to backoffice for shipments that should check RNS response but don't have customs broker
      customsStatusCheckErrorShipments.push(
        ...missingCustomsBrokerShipments.map((shipment) => ({
          shipmentId: shipment.id,
          errorType: "Unknown Customs Broker",
          errorMessage: `Shipment ${shipment.id} is in ${shipment.customsStatus} customs status but has unknown customs broker "${shipment.organization?.customsBroker}".`
        }))
      );

      // Get RNS responses from Candata
      for (const [
        customsBroker,
        brokerRnsCheckShipments
      ] of rnsCheckWithCCNShipmentsByCustomsBroker.entries()) {
        const rnsResponseList = await this.candataService.findRnsResponseByCargoControlNumbers(
          brokerRnsCheckShipments.map((s) => s.cargoControlNumber)
        );
        this.logger.log(
          `Found ${rnsResponseList.length} RNS responses for customs broker ${customsBroker}...`
        );

        // Update shipment customs status based on RNS responses
        for (const shipment of brokerRnsCheckShipments) {
          const shipmentRNSResponses = rnsResponseList.filter(
            (r) => r.cargoControlNumber === shipment.cargoControlNumber
          );
          this.logger.debug(`Shipment ${shipment.id} has ${shipmentRNSResponses.length} RNS responses`);

          // Use the shared checkCustomsStatus logic
          const statusResult = await this.checkCustomsStatus(shipment, shipmentRNSResponses);
          const {
            latestCustomsStatus,
            latestRNSResponse,
            isStatusChanged,
            isStuckInValidated,
            releaseDate: releaseDateString
          } = statusResult;

          if (latestRNSResponse === null) {
            this.logger.log(`Shipment ${shipment.id} has no valid RNS responses. Skipping...`);
            continue;
          }

          const releaseDate =
            releaseDateString && !Number.isNaN(Date.parse(releaseDateString))
              ? new Date(releaseDateString)
              : null;
          this.logger.log(
            `Shipment original release date: ${shipment.releaseDate?.toISOString()}, new release date: ${releaseDate?.toISOString()}`
          );
          if (
            releaseDate &&
            (!shipment.releaseDate || shipment.releaseDate.getTime() !== releaseDate.getTime())
          ) {
            this.logger.log(
              `Shipment ${shipment.id} release date changed from ${shipment.releaseDate?.toISOString()} to ${releaseDate.toISOString()}, updating...`
            );
            const updateResult = await shipmentRepository.update({ id: shipment.id }, { releaseDate });
            this.logger.log(
              `Shipment ${shipment.id} release date update result: ${updateResult?.affected} affected rows`
            );
          }

          this.logger.log(
            `Shipment ${shipment.id} current customs status: ${shipment.customsStatus}, new customs status: ${latestCustomsStatus}. Latest RNS response: ${JSON.stringify(latestRNSResponse)}`
          );
          if (isStatusChanged) {
            this.logger.log(`Customs status changed for shipment ${shipment.id}, updating...`);

            const updateResult = await shipmentRepository.update(
              { id: shipment.id },
              { customsStatus: latestCustomsStatus }
            );
            this.logger.log(`Shipment ${shipment.id} update result: ${updateResult?.affected} affected rows`);

            // Send email notification to importer, trucker and CC to backoffice
            rnsStatusChangeShipments.push({
              shipmentId: shipment.id,
              rnsResponse: latestRNSResponse
            });
          }

          // Send error email to backoffice if stuck in Validated status and not change to Accepted/Waiting status after certain time
          if (isStuckInValidated) {
            this.logger.log(
              `Shipment ${shipment.id} is stuck in validated status, sending error email... Latest RNS Response: ${JSON.stringify(latestRNSResponse)}`
            );
            entryNotAcceptedShipments.push({
              shipmentId: shipment.id,
              entrySubmittedDate: latestRNSResponse.responseDate
            });
          }
        }
      }

      // Get all shipments that haven't been submitted yet
      const notSubmittedShipments = await shipmentRepository.find({
        where: {
          customsStatus: In([
            CustomsStatus.PENDING_COMMERCIAL_INVOICE,
            CustomsStatus.PENDING_CONFIRMATION,
            CustomsStatus.PENDING_ARRIVAL
          ])
        },
        relations: FIND_SHIPMENT_RELATIONS
      });
      this.logger.log(`Found ${notSubmittedShipments.length} shipments that haven't been submitted yet...`);

      // Divide not submitted shipments into batches
      const BATCH_SIZE = 20;
      const numberOfBatches = Math.ceil(notSubmittedShipments.length / BATCH_SIZE);
      this.logger.log(
        `Dividing ${notSubmittedShipments.length} shipments into ${numberOfBatches} batches...`
      );
      for (let i = 0; i < numberOfBatches; i++) {
        const batchNotSubmittedShipments = notSubmittedShipments.slice(i * BATCH_SIZE, (i + 1) * BATCH_SIZE);
        this.logger.log(`Batch ${i + 1} has ${batchNotSubmittedShipments.length} shipments...`);

        // Get the shipment compliances
        const batchShipmentComplianceList = await this.complianceValidationService.getShipmentCompliances(
          batchNotSubmittedShipments,
          queryRunner
        );
        this.logger.log(`Batch ${i + 1} Shipment Compliance List: ${batchShipmentComplianceList.length}`);

        // Validate the not submitted shipments and submit to Candata if possible
        for (const shipment of batchNotSubmittedShipments) {
          const shipmentCompliance = batchShipmentComplianceList.find(
            (sc) => sc?.shipment?.id === shipment.id
          );
          if (!shipmentCompliance) {
            this.logger.warn(`Shipment ${shipment.id} has no shipment compliance. Skipping...`);
            continue;
          }
          const validationResult = this.complianceValidationService.validateShipmentCompliances(
            [shipmentCompliance],
            this.complianceValidationService.isDemoShipment(shipment) // Skip filings validation for demo shipments
          )[0];

          const result = await this.processShipmentForCustomsStatus(shipment, validationResult, queryRunner);
          if (result.shipmentStatusUpdate) {
            await this.updateShipmentStatus(
              shipment,
              result.shipmentStatusUpdate.newStatus,
              shipmentRepository
            );
            // update the in-memory shipment status
            shipment.customsStatus = result.shipmentStatusUpdate.newStatus;
          }
          if (result.liveShipmentId) liveShipmentIds.push(result.liveShipmentId);
          if (result.liveEntryUploadFailedShipment)
            liveEntryUploadFailedShipments.push(result.liveEntryUploadFailedShipment);
          if (result.customsStatusCheckErrorShipment)
            customsStatusCheckErrorShipments.push(result.customsStatusCheckErrorShipment);
        }
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      const errorMessage = error instanceof Error ? error.message : String(error);
      const stackTrace = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error while checking customs status: ${errorMessage}`, stackTrace);

      // Reset the email lists because the transaction is not committed
      liveShipmentIds = [];
      liveEntryUploadFailedShipments = [];
      rnsStatusChangeShipments = [];
      entryNotAcceptedShipments = [];
      customsStatusCheckErrorShipments = [];

      // TODO: Send error email to backoffice
    } finally {
      await queryRunner.release();
    }

    // Check if customs status emails are disabled
    if (
      this.configService.get<string>("DISABLE_SEND_CUSTOMS_STATUS_EMAIL") === "true" &&
      NODE_ENV === NodeEnv.LOCAL
    ) {
      this.logger.warn(
        "Customs status email sending is disabled via DISABLE_SEND_CUSTOMS_STATUS_EMAIL environment variable. Skipping all customs status related emails."
      );
      return; // Exit before sending any emails
    }

    // Send all email notifications
    if (liveShipmentIds.length > 0)
      await this.liveShipmentEmailSender.sendLiveShipmentEmails(liveShipmentIds);
    if (liveEntryUploadFailedShipments.length > 0)
      await this.liveEntryUploadFailedEmailSender.sendLiveEntryUploadFailedEmails(
        liveEntryUploadFailedShipments
      );
    if (rnsStatusChangeShipments.length > 0)
      await this.rnsStatusChangeEmailSender.sendRNSStatusChangeEmails(rnsStatusChangeShipments);
    if (entryNotAcceptedShipments.length > 0)
      await this.entryNotAcceptedWarningEmailSender.sendEntryNotAcceptedWarningEmails(
        entryNotAcceptedShipments
      );
    if (customsStatusCheckErrorShipments.length > 0)
      await this.customsStatusCheckErrorEmailSender.sendCustomsStatusCheckErrorEmails(
        customsStatusCheckErrorShipments
      );
  }
  /**
   * Check if a shipment is ready to be submitted for customs.
   * @param validationResult The validation result for the shipment
   * @returns True if ready, false otherwise
   */
  private isShipmentReadyToSubmit(validationResult: any): boolean {
    return (
      !validationResult?.noCommercialInvoice &&
      Array.isArray(validationResult?.missingFields) &&
      validationResult.missingFields.length === 0 &&
      Array.isArray(validationResult?.nonCompliantInvoices) &&
      validationResult.nonCompliantInvoices.length === 0
    );
  }

  /**
   * Get the new customs status for a shipment that is not ready to submit.
   * @param validationResult The validation result for the shipment
   * @returns The new customs status
   */
  private getNewCustomsStatus(validationResult: any): CustomsStatus {
    return validationResult?.noCommercialInvoice
      ? CustomsStatus.PENDING_COMMERCIAL_INVOICE
      : CustomsStatus.PENDING_CONFIRMATION;
  }

  /**
   * Check if a shipment can be submitted immediately based on its mode and ETA.
   * @param shipment The shipment entity
   * @returns [canSubmit: boolean, error?: any]
   */
  private canSubmitImmediately(shipment: Shipment): [boolean, any?] {
    // For DEMO organizations, always allow immediate submission
    if (this.complianceValidationService.isDemoShipment(shipment)) {
      return [true];
    }

    const currentDate = moment.tz("America/Toronto");
    switch (shipment.modeOfTransport) {
      case ShipmentMode.LAND: {
        return [true];
      }
      case ShipmentMode.OCEAN_FCL: {
        if (!shipment.placeOfDelivery) {
          return [
            false,
            {
              shipmentId: shipment.id,
              errorType: "Missing Place of Delivery",
              errorMessage: `Ocean FCL Shipment ${shipment.id} cannot be checked for entry submission because it has no place of delivery and thus, cannot be determined if it is an inland or port shipment.`
            }
          ];
        }
        if (this.complianceValidationService.isShipmentPODInland(shipment)) {
          if (!shipment.etaPort || !shipment.etaDestination) {
            return [
              false,
              {
                shipmentId: shipment.id,
                errorType: "Missing ETA Port or ETA Destination",
                errorMessage: `Ocean FCL Inland Shipment ${shipment.id} cannot be checked for entry submission because it has no ETA port or ETA destination.`
              }
            ];
          }
          const etaPortDate = moment.tz(shipment.etaPort, "America/Toronto");
          const etaDestinationDate = moment.tz(shipment.etaDestination, "America/Toronto");
          return [
            currentDate.isSameOrAfter(etaPortDate, "day") ||
              currentDate.isSameOrAfter(etaDestinationDate.subtract(3, "days"), "day")
          ];
        } else {
          if (!shipment.etaPort) {
            return [
              false,
              {
                shipmentId: shipment.id,
                errorType: "Missing ETA Port",
                errorMessage: `Ocean FCL Port Shipment ${shipment.id} cannot be checked for entry submission because it has no ETA port.`
              }
            ];
          }
          const etaPortDate = moment.tz(shipment.etaPort, "America/Toronto");
          return [currentDate.isSameOrAfter(etaPortDate.subtract(3, "days"), "day")];
        }
      }
      case ShipmentMode.AIR:
      case ShipmentMode.OCEAN_LCL: {
        if (!shipment.etaDestination) {
          return [
            false,
            {
              shipmentId: shipment.id,
              errorType: "Missing ETA Destination",
              errorMessage: `Ocean LCL Shipment ${shipment.id} cannot be checked for entry submission because it has no ETA destination.`
            }
          ];
        }
        const etaDestinationDate = moment.tz(shipment.etaDestination, "America/Toronto");
        return [currentDate.isSameOrAfter(etaDestinationDate, "day")];
      }
      default: {
        this.logger.warn(
          `Shipment ${shipment.id} mode of transport ${shipment.modeOfTransport} is not supported for entry submission`
        );
        return [false];
      }
    }
  }

  /**
   * Checks and computes the customs status for a single shipment based on latest RNS responses.
   * Does not update the DB or send emails; returns the computed result for inspection or further action.
   *
   * @param shipment The shipment entity to check.
   * @returns An object with the latest customs status, and any status change or error info.
   */
  private async checkCustomsStatus(
    shipment: Shipment,
    fetchedRNSResponses: CandataRNSResponseDto[] | null = null
  ): Promise<{
    latestCustomsStatus: CustomsStatus;
    latestRNSResponse: CandataRNSResponseDto | null;
    isStatusChanged: boolean;
    isStuckInValidated: boolean;
    releaseDate: string | null;
  }> {
    let shipmentRNSResponses: CandataRNSResponseDto[] = [];

    shipmentRNSResponses =
      fetchedRNSResponses && fetchedRNSResponses.length > 0
        ? fetchedRNSResponses
        : await this.candataService.findRnsResponseByCargoControlNumbers(
            [shipment.cargoControlNumber],
            shipment.organization?.customsBroker
          );
    this.logger.debug(`Shipment ${shipment.id} has ${shipmentRNSResponses?.length} RNS responses`);

    // Filter and sort RNS responses
    const filteredRNSResponses = (shipmentRNSResponses || [])
      .filter((r) => categorizeRNSResponse(r) !== RNSResponseCategory.UNKNOWN)
      .sort((r1, r2) => new Date(r2.responseDate).getTime() - new Date(r1.responseDate).getTime());
    if (filteredRNSResponses.length === 0) {
      return {
        latestCustomsStatus: shipment.customsStatus,
        latestRNSResponse: null,
        isStatusChanged: false,
        isStuckInValidated: false,
        releaseDate: null
      };
    }

    const latestRNSResponse = filteredRNSResponses[0];
    const latestCustomsStatus =
      RNS_RESPONSE_CATEGORY_TO_CUSTOMS_STATUS[
        categorizeRNSResponse(latestRNSResponse) as Exclude<RNSResponseCategory, RNSResponseCategory.UNKNOWN>
      ];
    const isStatusChanged = latestCustomsStatus !== shipment.customsStatus;
    // Check if stuck in validated
    const isStuckInValidated = this.isShipmentStuckInValidated(shipment, latestRNSResponse);
    return {
      latestCustomsStatus,
      latestRNSResponse,
      isStatusChanged,
      isStuckInValidated,
      releaseDate:
        filteredRNSResponses.find((r) => categorizeRNSResponse(r) === RNSResponseCategory.RELEASED)
          ?.responseDate || null
    };
  }

  /**
   * Update the customs status for a shipment.
   * @param shipment The shipment entity
   * @param newStatus The new customs status
   * @param shipmentRepository The shipment repository
   */
  private async updateShipmentStatus(
    shipment: Shipment,
    newStatus: CustomsStatus,
    shipmentRepository: Repository<Shipment>
  ): Promise<void> {
    const updateResult = await shipmentRepository.update({ id: shipment.id }, { customsStatus: newStatus });
    this.logger.log(`Shipment ${shipment.id} update result: ${updateResult?.affected} affected rows`);
    // update the in-memory shipment status
    shipment.customsStatus = newStatus;
  }

  private async getScopedEntrySubmissionService(organization: Organization) {
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(
      {
        user: {
          permission: UserPermission.BACKOFFICE_ADMIN,
          organization
        }
      },
      contextId
    );
    const entrySubmissionService = await this.moduleRef.resolve(EntrySubmissionService, contextId, {
      strict: false
    });
    await new Promise((resolve) => process.nextTick(resolve));
    return entrySubmissionService;
  }

  private isShipmentStuckInValidated(shipment: Shipment, latestRNSResponse: CandataRNSResponseDto) {
    if (categorizeRNSResponse(latestRNSResponse) !== RNSResponseCategory.VALIDATED) return false;

    const currentDate = moment.tz("America/Toronto");
    switch (shipment.modeOfTransport) {
      case ShipmentMode.LAND:
        return currentDate.isSameOrAfter(
          moment.tz(latestRNSResponse.responseDate, "America/Toronto").add(30, "minutes")
        );
      case ShipmentMode.AIR:
        return currentDate.isSameOrAfter(
          moment.tz(latestRNSResponse.responseDate, "America/Toronto").add(1, "hours")
        );
      case ShipmentMode.OCEAN_FCL:
      case ShipmentMode.OCEAN_LCL:
        return currentDate.isSameOrAfter(
          moment.tz(latestRNSResponse.responseDate, "America/Toronto").add(2, "hours")
        );
      default:
        this.logger.error(
          `Shipment mode of transport ${shipment.modeOfTransport} is not supported for checking if shipment is stuck in validated status`
        );
        return false;
    }
  }

  /**
   * Submit a shipment entry and handle errors.
   * @param shipment The shipment entity
   * @param queryRunner The TypeORM query runner
   * @returns [result: Shipment | null, error: string | null]
   */
  private async trySubmitShipmentEntry(
    shipment: Shipment,
    queryRunner: any
  ): Promise<[Shipment | null, string | null]> {
    let submissionResult: Shipment = null;
    let submissionError: string = null;
    try {
      submissionResult = await (
        await this.getScopedEntrySubmissionService(shipment.organization)
      ).submitShipmentEntry(shipment, queryRunner);
    } catch (error) {
      submissionError = error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Failed to submit shipment entry for shipment ${shipment.id}: ${submissionError}`,
        error instanceof Error ? error.stack : undefined
      );
    }
    return [submissionResult, submissionError];
  }

  /**
   * Processes a single shipment through the customs status workflow to determine if it can be submitted
   * to customs and handles the submission process. This method orchestrates the entire customs entry
   * submission flow for shipments that haven't been submitted yet.
   *
   * **Workflow Logic:**
   * 1. **Validation Check**: Determines if the shipment is ready for customs submission based on:
   *    - Presence of commercial invoice
   *    - All required fields are populated
   *    - All invoices are compliant
   *
   * 2. **Timing Check**: For ready shipments, validates if they can be submitted immediately based on:
   *    - Mode of transport (LAND, AIR, OCEAN_FCL, OCEAN_LCL)
   *    - ETA dates (port arrival, destination arrival)
   *    - Current date relative to submission windows
   *
   * 3. **Submission Attempt**: For eligible shipments, attempts to submit the customs entry via
   *    the EntrySubmissionService and handles success/failure scenarios
   *
   * **Status Updates:**
   * - Not ready: Updates to PENDING_COMMERCIAL_INVOICE or PENDING_CONFIRMATION
   * - Ready but early: Updates to PENDING_ARRIVAL
   * - Successful submission: Adds to live shipment list for email notifications
   * - Failed submission: Adds to failed upload list for error notifications
   *
   * **Error Handling:**
   * - Missing ETA dates for time-sensitive modes
   * - Invalid place of delivery for ocean shipments
   * - Entry submission failures
   *
   * @param shipment The shipment entity to process through customs workflow
   * @param validationResult The compliance validation result containing missing fields,
   *                        non-compliant invoices, and commercial invoice status
   * @param queryRunner The TypeORM query runner for database operations within transaction
   *
   * @returns Promise resolving to an object containing:
   * - `liveShipmentId`: Shipment ID if successfully submitted (for success email)
   * - `liveEntryUploadFailedShipment`: Shipment ID and error if submission failed (for error email)
   * - `customsStatusCheckErrorShipment`: Error details if timing validation failed (for error email)
   * - `shipmentStatusUpdate`: New customs status if shipment status should be updated
   *
   * @example
   * ```typescript
   * const result = await this.processShipmentForCustomsStatus(shipment, validationResult, queryRunner);
   * if (result.liveShipmentId) {
   *   // Send success notification
   * }
   * if (result.shipmentStatusUpdate) {
   *   // Update shipment status in database
   * }
   * ```
   */
  async processShipmentForCustomsStatus(
    shipment: Shipment,
    validationResult: any,
    queryRunner: any
  ): Promise<{
    liveShipmentId: number | null;
    liveEntryUploadFailedShipment: { shipmentId: number; failedReason: string } | null;
    customsStatusCheckErrorShipment: any | null;
    shipmentStatusUpdate: { shipmentId: number; newStatus: CustomsStatus } | null;
  }> {
    let liveShipmentId: number | null = null;
    let liveEntryUploadFailedShipment: { shipmentId: number; failedReason: string } | null = null;
    let customsStatusCheckErrorShipment: any | null = null;
    let shipmentStatusUpdate: { shipmentId: number; newStatus: CustomsStatus } | null = null;

    this.logger.log(
      `Shipment ${shipment.id}. Is ready to submit: ${this.isShipmentReadyToSubmit(validationResult)}`
    );
    this.logger.debug(`Shipment ${shipment.id} validation result: ${JSON.stringify(validationResult)}`);

    if (!this.isShipmentReadyToSubmit(validationResult)) {
      const newCustomsStatus = this.getNewCustomsStatus(validationResult);
      this.logger.log(
        `Shipment ${shipment.id} is not ready to be submitted. Old customs status: ${shipment.customsStatus}, new customs status: ${newCustomsStatus}`
      );
      shipmentStatusUpdate = { shipmentId: shipment.id, newStatus: newCustomsStatus };
      return {
        liveShipmentId,
        liveEntryUploadFailedShipment,
        customsStatusCheckErrorShipment,
        shipmentStatusUpdate
      };
    }

    const [canSubmit, error] = this.canSubmitImmediately(shipment);
    if (!canSubmit) {
      if (error) customsStatusCheckErrorShipment = error;
      this.logger.log(
        `Shipment ${shipment.id} CANNOT submit immediately, updating customs status to ${CustomsStatus.PENDING_ARRIVAL}`
      );
      shipmentStatusUpdate = { shipmentId: shipment.id, newStatus: CustomsStatus.PENDING_ARRIVAL };

      return {
        liveShipmentId,
        liveEntryUploadFailedShipment,
        customsStatusCheckErrorShipment,
        shipmentStatusUpdate
      };
    }

    this.logger.log(`Shipment ${shipment.id} CAN submit immediately, submitting entry...`);
    const [submissionResult, submissionError] = await this.trySubmitShipmentEntry(shipment, queryRunner);
    this.logger.log(
      `Shipment ${shipment.id} submission result: (${submissionResult?.transactionNumber}, ${submissionResult?.customsFileNumber})`
    );
    if (submissionResult?.id && submissionResult?.transactionNumber && submissionResult?.customsFileNumber) {
      this.logger.log(`Shipment ${shipment.id} submitted entry successfully, adding to live shipment IDs...`);
      liveShipmentId = shipment.id;
      // Return the status update from the submission so caller can sync in-memory object
      shipmentStatusUpdate = {
        shipmentId: shipment.id,
        newStatus: submissionResult.customsStatus
      };
    } else {
      this.logger.warn(
        `Shipment ${shipment.id} failed to submit entry, sending error email to backoffice...`
      );
      liveEntryUploadFailedShipment = {
        shipmentId: shipment.id,
        failedReason: submissionError
      };
    }
    return {
      liveShipmentId,
      liveEntryUploadFailedShipment,
      customsStatusCheckErrorShipment,
      shipmentStatusUpdate
    };
  }
}
