import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { Shipment } from "nest-modules";
import { DataSource, QueryRunner } from "typeorm";

/**
 * Service for completely deleting a shipment and all its related data.
 * This utility follows the deletion order specified to avoid foreign key constraint violations.
 *
 * ⚠️ DESTRUCTIVE OPERATION - Use only for debugging/testing purposes.
 *
 * ## Usage via REPL:
 *
 * 1. Start the REPL:
 *    ```bash
 *    rushx start:repl
 *    ```
 *    (or from apps/portal-api directory: `npm run start:repl`)
 *
 * 2. In the REPL console:
 *    ```typescript
 *    // Get the service through the module context
 *    const shipmentDeletionService = get('ShipmentDeletionService')
 *
 *    // Preview what would be deleted (optional)
 *    const summary = await shipmentDeletionService.getShipmentDeletionSummary(123)
 *    console.log(summary)
 *
 *    // Perform the deletion (DESTRUCTIVE!)
 *    await shipmentDeletionService.deleteShipmentCompletely(123)
 *    ```
 *
 * The service will delete the shipment and ALL related data across multiple tables.
 * This operation cannot be undone.
 */
@Injectable()
export class ShipmentDeletionService {
  private readonly logger = new Logger(ShipmentDeletionService.name);

  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  /**
   * Completely removes a shipment and all its related data from the database.
   * This operation is irreversible and should only be used for testing purposes.
   *
   * @param shipmentId - The ID of the shipment to delete
   * @param queryRunner - Optional query runner for transaction management
   * @returns Promise<void>
   * @throws NotFoundException if shipment doesn't exist
   */
  async deleteShipmentCompletely(shipmentId: number, queryRunner?: QueryRunner): Promise<void> {
    const tQueryRunner = queryRunner || this.dataSource.createQueryRunner();

    if (!queryRunner) {
      await tQueryRunner.connect();
      await tQueryRunner.startTransaction();
    }

    try {
      this.logger.log(`Starting complete deletion of shipment ${shipmentId}`);

      // First verify the shipment exists
      const shipment = await tQueryRunner.manager.findOne(Shipment, {
        where: { id: shipmentId }
      });

      if (!shipment) {
        throw new NotFoundException(`Shipment with ID ${shipmentId} not found`);
      }

      this.logger.log(`Found shipment ${shipmentId}, proceeding with deletion`);

      // Step 1: Delete Commercial Invoice Line Measurements
      // These are the deepest nested entities and must be deleted first
      this.logger.log(`Deleting commercial invoice line measurements for shipment ${shipmentId}`);
      await tQueryRunner.manager.query(
        `
        DELETE FROM commercial_invoice_line_measurement 
        WHERE "commercialInvoiceLineId" IN (
          SELECT cil.id FROM commercial_invoice_line cil
          INNER JOIN commercial_invoice ci ON ci.id = cil."commercialInvoiceId"
          WHERE ci."shipmentId" = $1
        )
      `,
        [shipmentId]
      );

      // Step 2: Delete Commercial Invoice Lines
      this.logger.log(`Deleting commercial invoice lines for shipment ${shipmentId}`);
      await tQueryRunner.manager.query(
        `
        DELETE FROM commercial_invoice_line 
        WHERE "commercialInvoiceId" IN (
          SELECT ci.id FROM commercial_invoice ci
          WHERE ci."shipmentId" = $1
        )
      `,
        [shipmentId]
      );

      // Step 3: Delete Commercial Invoices
      this.logger.log(`Deleting commercial invoices for shipment ${shipmentId}`);
      await tQueryRunner.manager.query(`DELETE FROM commercial_invoice WHERE "shipmentId" = $1`, [
        shipmentId
      ]);

      // Step 4: Delete Tracking Histories
      // These have CASCADE delete but we'll be explicit
      this.logger.log(`Deleting tracking histories for shipment ${shipmentId}`);
      await tQueryRunner.manager.query(`DELETE FROM tracking_history WHERE "shipmentId" = $1`, [shipmentId]);

      // Step 5: Delete Email Threads
      // These have CASCADE delete but we'll be explicit
      this.logger.log(`Deleting email threads for shipment ${shipmentId}`);
      await tQueryRunner.manager.query(`DELETE FROM email_thread WHERE "shipmentId" = $1`, [shipmentId]);

      // Step 6: Update related entities to NULL (preserve them but remove shipment reference)
      this.logger.log(`Setting shipment references to NULL for related entities`);

      // Document Aggregations
      await tQueryRunner.manager.query(
        `UPDATE document_aggregation SET "shipmentId" = NULL WHERE "shipmentId" = $1`,
        [shipmentId]
      );

      // Documents
      await tQueryRunner.manager.query(`UPDATE document SET "shipmentId" = NULL WHERE "shipmentId" = $1`, [
        shipmentId
      ]);

      // Files
      await tQueryRunner.manager.query(`UPDATE file SET "shipmentId" = NULL WHERE "shipmentId" = $1`, [
        shipmentId
      ]);

      // File Batches
      await tQueryRunner.manager.query(`UPDATE file_batch SET "shipmentId" = NULL WHERE "shipmentId" = $1`, [
        shipmentId
      ]);

      // Step 7: Delete Containers
      // These have cascade: true but we'll be explicit for logging
      this.logger.log(`Deleting containers for shipment ${shipmentId}`);
      await tQueryRunner.manager.query(`DELETE FROM container WHERE "shipmentId" = $1`, [shipmentId]);

      // Step 8: Finally, delete the shipment itself
      this.logger.log(`Deleting shipment ${shipmentId}`);
      const deleteResult = await tQueryRunner.manager.delete(Shipment, {
        id: shipmentId
      });

      if (deleteResult.affected === 0) {
        throw new Error(`Failed to delete shipment ${shipmentId} - no rows affected`);
      }

      if (!queryRunner) {
        await tQueryRunner.commitTransaction();
      }

      this.logger.log(`Successfully completed deletion of shipment ${shipmentId}`);
    } catch (error) {
      if (!queryRunner && tQueryRunner.isTransactionActive) {
        await tQueryRunner.rollbackTransaction();
      }
      this.logger.error(`Failed to delete shipment ${shipmentId}: ${error.message}`, error.stack);
      throw error;
    } finally {
      if (!queryRunner) {
        await tQueryRunner.release();
      }
    }
  }

  /**
   * Get a summary of all entities that would be deleted for a shipment.
   * Useful for verification before actual deletion.
   *
   * @param shipmentId - The ID of the shipment to analyze
   * @returns Promise<object> containing counts of related entities
   */
  async getShipmentDeletionSummary(shipmentId: number): Promise<{
    shipmentExists: boolean;
    commercialInvoices: number;
    commercialInvoiceLines: number;
    commercialInvoiceLineMeasurements: number;
    trackingHistories: number;
    emailThreads: number;
    containers: number;
    documents: number;
    files: number;
    documentAggregations: number;
    fileBatches: number;
  }> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();

    try {
      // Check if shipment exists
      const shipment = await queryRunner.manager.findOne(Shipment, {
        where: { id: shipmentId }
      });

      if (!shipment) {
        return {
          shipmentExists: false,
          commercialInvoices: 0,
          commercialInvoiceLines: 0,
          commercialInvoiceLineMeasurements: 0,
          trackingHistories: 0,
          emailThreads: 0,
          containers: 0,
          documents: 0,
          files: 0,
          documentAggregations: 0,
          fileBatches: 0
        };
      }

      // Count all related entities using raw queries to avoid relation path issues
      const [
        commercialInvoices,
        commercialInvoiceLines,
        commercialInvoiceLineMeasurements,
        trackingHistories,
        emailThreads,
        containers,
        documents,
        files,
        documentAggregations,
        fileBatches
      ] = await Promise.all([
        queryRunner.manager
          .query(`SELECT COUNT(*) as count FROM commercial_invoice WHERE "shipmentId" = $1`, [shipmentId])
          .then((result) => parseInt(result[0]?.count || 0)),
        queryRunner.manager
          .query(
            `
            SELECT COUNT(*) as count FROM commercial_invoice_line cil
            INNER JOIN commercial_invoice ci ON ci.id = cil."commercialInvoiceId"
            WHERE ci."shipmentId" = $1
          `,
            [shipmentId]
          )
          .then((result) => parseInt(result[0]?.count || 0)),
        queryRunner.manager
          .query(
            `
          SELECT COUNT(*) as count FROM commercial_invoice_line_measurement 
          WHERE "commercialInvoiceLineId" IN (
            SELECT cil.id FROM commercial_invoice_line cil
            INNER JOIN commercial_invoice ci ON ci.id = cil."commercialInvoiceId"
            WHERE ci."shipmentId" = $1
          )
        `,
            [shipmentId]
          )
          .then((result) => parseInt(result[0]?.count || 0)),
        queryRunner.manager
          .query(`SELECT COUNT(*) as count FROM tracking_history WHERE "shipmentId" = $1`, [shipmentId])
          .then((result) => parseInt(result[0]?.count || 0)),
        queryRunner.manager
          .query(`SELECT COUNT(*) as count FROM email_thread WHERE "shipmentId" = $1`, [shipmentId])
          .then((result) => parseInt(result[0]?.count || 0)),
        queryRunner.manager
          .query(`SELECT COUNT(*) as count FROM container WHERE "shipmentId" = $1`, [shipmentId])
          .then((result) => parseInt(result[0]?.count || 0)),
        queryRunner.manager
          .query(`SELECT COUNT(*) as count FROM document WHERE "shipmentId" = $1`, [shipmentId])
          .then((result) => parseInt(result[0]?.count || 0)),
        queryRunner.manager
          .query(`SELECT COUNT(*) as count FROM file WHERE "shipmentId" = $1`, [shipmentId])
          .then((result) => parseInt(result[0]?.count || 0)),
        queryRunner.manager
          .query(`SELECT COUNT(*) as count FROM document_aggregation WHERE "shipmentId" = $1`, [shipmentId])
          .then((result) => parseInt(result[0]?.count || 0)),
        queryRunner.manager
          .query(`SELECT COUNT(*) as count FROM file_batch WHERE "shipmentId" = $1`, [shipmentId])
          .then((result) => parseInt(result[0]?.count || 0))
      ]);

      return {
        shipmentExists: true,
        commercialInvoices,
        commercialInvoiceLines,
        commercialInvoiceLineMeasurements,
        trackingHistories,
        emailThreads,
        containers,
        documents,
        files,
        documentAggregations,
        fileBatches
      };
    } finally {
      await queryRunner.release();
    }
  }
}
