import { CandataRNSResponseDto, RNSProcessingIndicator } from "nest-modules";
import { RNSResponseCategory } from "../types/customs-status.types";

export function categorizeRNSResponse(rnsResponse: CandataRNSResponseDto) {
  switch (rnsResponse.processingIndicator) {
    case RNSProcessingIndicator.ACCEPTED_WAITING:
      // if (rnsResponse.message.startsWith("961 - ")) return RNSResponseCategory.VALIDATED;
      // else if (!rnsResponse.message) return RNSResponseCategory.ACCEPTED_WAITING;
      // else return RNSResponseCategory.UNKNOWN;
      if (!rnsResponse?.message) return RNSResponseCategory.ACCEPTED_WAITING;
      else if ((rnsResponse.message || "").startsWith("961 - ")) return RNSResponseCategory.VALIDATED;
      else return RNSResponseCategory.UNKNOWN;
    case RNSProcessingIndicator.RELEASED:
      return RNSResponseCategory.RELEASED;
    case RNSProcessingIndicator.EXAM_REQUIRED:
      return RNSResponseCategory.EXAM;
    default:
      return RNSResponseCategory.UNKNOWN;
  }
}
