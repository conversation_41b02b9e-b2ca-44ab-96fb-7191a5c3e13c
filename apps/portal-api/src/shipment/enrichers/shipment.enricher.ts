import { FallbackEnricher } from "@/fallback-enricher";
import { Inject, Injectable } from "@nestjs/common";
import { FieldFallbackResult, QuantityUOM, Shipment } from "nest-modules";
import { ShipmentCommercialInvoiceService } from "../services/shipment-commercial-invoice.service";

const SOURCE_CALCULATED_FROM_CI = "calculated from CI";

@Injectable()
export class ShipmentEnricher implements FallbackEnricher<Shipment> {
  constructor(
    @Inject(ShipmentCommercialInvoiceService)
    private readonly shipmentCommercialInvoiceService: ShipmentCommercialInvoiceService
  ) {}

  private readonly ENRICHABLE_FIELDS: Array<keyof Shipment> = [
    "weight",
    "weightUOM",
    "quantity",
    "quantityUOM"
  ];

  async getFallbacks(
    entity: Shipment,
    missingFields?: Array<keyof Shipment>
  ): Promise<FieldFallbackResult[]> {
    const fieldsToFallback = missingFields ?? this.ENRICHABLE_FIELDS;

    const fallbacks: FieldFallbackResult[] = [];

    const commercialInvoiceSummary = await this.shipmentCommercialInvoiceService.getCommercialInvoiceSummary(
      entity.id
    );

    if (commercialInvoiceSummary.invoiceCount === 0) {
      return fallbacks;
    }

    if (fieldsToFallback.includes("weight")) {
      fallbacks.push({
        key: "weight",
        value: commercialInvoiceSummary.totalGrossWeight,
        source: SOURCE_CALCULATED_FROM_CI
      });
    }

    if (fieldsToFallback.includes("weightUOM")) {
      fallbacks.push({
        key: "weightUOM",
        value: commercialInvoiceSummary.weightUOM,
        controlledBy: "weight",
        source: SOURCE_CALCULATED_FROM_CI
      });
    }

    if (fieldsToFallback.includes("quantity")) {
      fallbacks.push({
        key: "quantity",
        value: commercialInvoiceSummary.totalNumOfPackages,
        source: SOURCE_CALCULATED_FROM_CI
      });
    }

    if (fieldsToFallback.includes("quantityUOM")) {
      fallbacks.push({
        key: "quantityUOM",
        value: QuantityUOM.PACKAGE, // TODO: set using real unit from commercialInvoiceSummary.packageUOM,
        controlledBy: "quantity",
        source: SOURCE_CALCULATED_FROM_CI
      });
    }
    return fallbacks;
  }

  async enrich(entity: Shipment): Promise<Shipment> {
    const missingFields = this.ENRICHABLE_FIELDS.filter(
      (field) => entity[field] === undefined || entity[field] === null
    );

    const fallbacks = await this.getFallbacks(entity, missingFields);

    for (const fallback of fallbacks) {
      entity[fallback.key] = fallback.value;
    }
    return entity;
  }
}
