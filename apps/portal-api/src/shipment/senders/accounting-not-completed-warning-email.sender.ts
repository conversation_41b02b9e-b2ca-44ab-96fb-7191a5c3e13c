import { forwardRef, Inject, Injectable, Logger } from "@nestjs/common";
import { BaseEmailSender } from "./base.sender";
import { EmailService } from "@/email/services/email.service";
import { DataSource, Repository } from "typeorm";
import { ConfigService } from "@nestjs/config";
import { Email, Shipment } from "nest-modules";
import { InjectRepository } from "@nestjs/typeorm";
import { EmailThread } from "nest-modules";
import { SendEmailDto } from "@/email/dto/email.dto";
import { EmailTemplateName } from "@/email/types/email.types";
import moment from "moment-timezone";

@Injectable()
export class AccountingNotCompletedWarningEmailSender extends BaseEmailSender {
  constructor(
    @InjectRepository(EmailThread)
    private readonly emailThreadRepository: Repository<EmailThread>,
    @InjectRepository(Email)
    private readonly emailRepository: Repository<Email>,
    @Inject(forwardRef(() => EmailService))
    emailService: EmailService,
    @Inject(forwardRef(() => DataSource))
    dataSource: DataSource,
    @Inject(ConfigService)
    configService: ConfigService
  ) {
    super(emailService, dataSource, new Logger(AccountingNotCompletedWarningEmailSender.name), configService);
  }

  private prepareAccountingNotCompletedWarningEmail(shipment: Shipment): SendEmailDto {
    const currentDate = moment.tz("America/Toronto");
    const releaseDate = moment.tz(shipment.releaseDate, "America/Toronto");

    const { subject, body } = this.emailService.renderEmailTemplateWithSubject(
      EmailTemplateName.ACCOUNTING_NOT_COMPLETED_WARNING_EMAIL,
      {
        daysSinceRelease: currentDate.diff(releaseDate, "days"),
        shipmentId: shipment.id,
        claroUrl: `${this.PORTAL_FRONTEND_URL}/shipment/${shipment.id}`,
        transactionNumber: shipment.transactionNumber,
        cargoControlNumber: shipment.cargoControlNumber,
        hblNumber: shipment.hblNumber,
        containerNumbers:
          Array.isArray(shipment?.containers) && shipment?.containers?.length > 0
            ? shipment.containers?.map((c) => c.containerNumber).join(",")
            : null,
        organizationName: shipment.organization?.name
      }
    );

    return {
      from: this.SYSTEM_FROM_EMAIL,
      to: [this.BACKOFFICE_EMAIL],
      subject,
      text: body
    };
  }

  async sendAccountingNotCompletedWarningEmails(shipmentIds: Array<number>) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    this.logger.log(
      `Sending accounting not completed warning emails for shipments: ${JSON.stringify(shipmentIds)}`
    );
    try {
      // Get shipments
      const shipments = await this.getShipments(shipmentIds, queryRunner);

      // Prepare email DTOs and send emails
      const emailsAndShipments = shipments
        .filter((shipment) => shipment.releaseDate instanceof Date)
        .map((shipment) => ({
          shipment,
          email: this.prepareAccountingNotCompletedWarningEmail(shipment)
        }));
      const sentEmails = await this.sendEmails(emailsAndShipments, queryRunner);
      this.logger.log(`Sent ${sentEmails.length} accounting not completed warning emails`);

      await queryRunner.commitTransaction();
      return sentEmails;
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error while sending accounting not completed warning emails: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
    } finally {
      await queryRunner.release();
    }
  }
}
