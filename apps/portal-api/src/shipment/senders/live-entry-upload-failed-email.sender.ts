import { SendEmailDto } from "@/email/dto/email.dto";
import { EmailService } from "@/email/services/email.service";
import { EmailTemplateName } from "@/email/types/email.types";
import { forwardRef, Inject, Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { Email, EmailThread, Shipment } from "nest-modules";
import { DataSource, In, Like, QueryRunner, Repository } from "typeorm";
import { BaseEmailSender } from "./base.sender";
import { InjectRepository } from "@nestjs/typeorm";

@Injectable()
export class LiveEntryUploadFailedEmailSender extends BaseEmailSender {
  constructor(
    @InjectRepository(EmailThread)
    private readonly emailThreadRepository: Repository<EmailThread>,
    @InjectRepository(Email)
    private readonly emailRepository: Repository<Email>,
    @Inject(forwardRef(() => EmailService))
    emailService: EmailService,
    @Inject(forwardRef(() => DataSource))
    dataSource: DataSource,
    @Inject(ConfigService)
    configService: ConfigService
  ) {
    super(emailService, dataSource, new Logger(LiveEntryUploadFailedEmailSender.name), configService);
  }

  /**
   * Search for latest previous live entry upload failed email for a shipment
   * @param shipment - Shipment to search for previous emails
   * @param queryRunner - QueryRunner instance. Used for handling transactions
   * @returns The previous latest live entry upload failed email or null if no previous email is found
   */
  private async searchForPreviousEmail(shipment: Shipment, queryRunner?: QueryRunner) {
    const shipmentThreads = await (
      queryRunner ? queryRunner.manager.getRepository(EmailThread) : this.emailThreadRepository
    ).find({
      where: { shipment: { id: shipment.id } }
    });
    this.logger.log(`Shipment ${shipment.id} has ${shipmentThreads.length} email threads`);
    const previousEmail = await (
      queryRunner ? queryRunner.manager.getRepository(Email) : this.emailRepository
    ).findOne({
      where: {
        subject: Like(`[***URGENT***: Candata upload failed in Live entry]%`),
        threadId: In(shipmentThreads.map((emailThread) => emailThread.threadId))
      },
      order: { receiveDate: "DESC" }
    });
    return previousEmail || null;
  }

  private prepareLiveEntryUploadFailedEmail(shipment: Shipment, failedReason: string): SendEmailDto {
    const { subject, body } = this.emailService.renderEmailTemplateWithSubject(
      EmailTemplateName.LIVE_ENTRY_UPLOAD_FAILED_EMAIL,
      {
        hblNumber: shipment.hblNumber,
        cargoControlNumber: shipment.cargoControlNumber,
        // containerNumber: shipment.containerNumber,
        containerNumbers: (shipment.containers || []).map((c) => c.containerNumber).join(",") || null,
        organizationName: shipment.organization?.name,
        claroUrl: `${this.PORTAL_FRONTEND_URL}/shipment/${shipment.id}`,
        failedReason
      }
    );

    return {
      from: this.BACKOFFICE_EMAIL,
      to: [this.BACKOFFICE_EMAIL],
      subject,
      text: body
    };
  }

  async sendLiveEntryUploadFailedEmails(
    shipmentIdsAndFailedReasons: Array<{ shipmentId: number; failedReason: string }>
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // List of shipments that should send live entry upload failed emails
      const shipments = await this.getShipments(
        shipmentIdsAndFailedReasons.map(({ shipmentId }) => shipmentId),
        queryRunner
      );

      // Do not send email if already sent within last 12 hours
      const filteredShipments: Array<Shipment> = [];
      for (const shipment of shipments) {
        const previousEmail = await this.searchForPreviousEmail(shipment, queryRunner);
        this.logger.log(
          `Shipment ${shipment.id} previous email: ${previousEmail?.id}, receive date: ${previousEmail?.receiveDate}`
        );
        if (!previousEmail || previousEmail?.receiveDate?.getTime() <= Date.now() - 12 * 60 * 60 * 1000)
          filteredShipments.push(shipment);
      }
      this.logger.log(`Filtered ${filteredShipments.length} shipments to send emails`);

      // Prepare email DTOs and send emails
      const emailsAndShipments = filteredShipments.map((shipment) => ({
        shipment,
        email: this.prepareLiveEntryUploadFailedEmail(
          shipment,
          shipmentIdsAndFailedReasons.find(({ shipmentId }) => shipmentId === shipment.id)?.failedReason
        )
      }));
      const sentEmails = await this.sendEmails(emailsAndShipments, queryRunner);
      this.logger.log(`Sent ${sentEmails.length} live entry upload failed emails`);

      await queryRunner.commitTransaction();
      return sentEmails;
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error while sending live entry upload failed emails: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
    } finally {
      await queryRunner.release();
    }
  }
}
