import { SendEmailDto } from "@/email/dto/email.dto";
import { EmailService } from "@/email/services/email.service";
import { Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import {
  CommercialInvoice,
  Email,
  FIND_COMMERCIAL_INVOICE_LINE_RELATIONS,
  FIND_COMMERCIAL_INVOICE_RELATIONS,
  FIND_IMPORTER_RELATIONS,
  FIND_SHIPMENT_RELATIONS,
  Importer,
  Shipment
} from "nest-modules";
import { DataSource, In, QueryRunner } from "typeorm";

export class BaseEmailSender {
  constructor(
    protected readonly emailService: EmailService,
    protected readonly dataSource: DataSource,
    protected readonly logger: Logger,
    protected readonly configService: ConfigService
  ) {}

  protected readonly EMAIL_BATCH_SIZE = 5;

  protected get SYSTEM_FROM_EMAIL(): string {
    return this.configService.get<string>("SYSTEM_FROM_EMAIL");
  }

  protected get BACKOFFICE_EMAIL(): string {
    return this.configService.get<string>("BACKOFFICE_EMAIL_ADDRESS");
  }

  protected get PORTAL_FRONTEND_URL(): string {
    return this.configService.get<string>("PORTAL_FRONTEND_URL");
  }

  protected async getCommercialInvoicesByShipmentIds(shipmentIds: Array<number>, queryRunner?: QueryRunner) {
    if (shipmentIds.length <= 0) {
      this.logger.warn("No shipment IDs provided, skipping get commercial invoices...");
      return [];
    }
    const commercialInvoices = await (
      queryRunner
        ? queryRunner.manager.getRepository(CommercialInvoice)
        : this.dataSource.getRepository(CommercialInvoice)
    ).find({
      where: { shipment: { id: In(shipmentIds) } },
      relations: {
        ...FIND_COMMERCIAL_INVOICE_RELATIONS,
        commercialInvoiceLines: FIND_COMMERCIAL_INVOICE_LINE_RELATIONS
      }
    });
    return commercialInvoices;
  }

  /**
   * Get shipments for a list of shipment IDs
   * @param shipmentIds List of shipment IDs to get shipments for
   * @param queryRunner Optional query runner to use for the transaction. If provided, the transaction will be managed by the caller.
   * @returns List of shipments
   */
  protected async getShipments(shipmentIds: Array<number>, queryRunner?: QueryRunner) {
    if (shipmentIds.length <= 0) {
      this.logger.warn("No shipment IDs provided, skipping get shipments...");
      return [];
    }
    const shipments = await (
      queryRunner ? queryRunner.manager.getRepository(Shipment) : this.dataSource.getRepository(Shipment)
    ).find({
      where: { id: In(shipmentIds) },
      relations: FIND_SHIPMENT_RELATIONS
    });
    this.logger.log(`Found ${shipments.length} shipments`);
    const missingShipmentIds = shipmentIds.filter((id) => !shipments.some((shipment) => shipment.id === id));
    if (missingShipmentIds.length > 0)
      this.logger.warn(`Missing following shipment IDs: ${JSON.stringify(missingShipmentIds)}`);
    return shipments;
  }

  /**
   * Get organization importers for a list of shipments
   * @param shipments List of shipments to get organization importers for
   * @param queryRunner Optional query runner to use for the transaction. If provided, the transaction will be managed by the caller.
   * @returns List of organization importers
   */
  protected async getOrganizationImporters(shipments: Array<Shipment>, queryRunner?: QueryRunner) {
    if (shipments.length <= 0) {
      this.logger.warn("No shipments provided, skipping get organization importers...");
      return [];
    }
    const organizationIds = shipments.reduce((ids, shipment) => {
      if (!ids.includes(shipment.organization?.id)) ids.push(shipment.organization?.id);
      return ids;
    }, [] as Array<number>);
    return await (
      queryRunner ? queryRunner.manager.getRepository(Importer) : this.dataSource.getRepository(Importer)
    ).find({
      where: { organization: { id: In(organizationIds) } },
      relations: FIND_IMPORTER_RELATIONS
    });
  }

  /**
   * Send emails and associate them with shipments
   * @param emailsAndShipments List of emails and shipments to send
   * @param queryRunner Optional query runner to use for the transaction. If provided, the transaction will be managed by the caller.
   * @returns List of sent emails
   */
  protected async sendEmails(
    emailsAndShipments: Array<{ email: SendEmailDto; shipment: Shipment }>,
    queryRunner?: QueryRunner
  ) {
    this.logger.log(`Sending ${emailsAndShipments.length} emails...`);

    // If queryRunner is not provided, create a new tQueryRunner that is controlled by this function
    const tQueryRunner = queryRunner ?? this.dataSource.createQueryRunner();
    if (!queryRunner) {
      await tQueryRunner.connect();
      await tQueryRunner.startTransaction();
    }

    try {
      // Filter out emails that have no valid to addresses
      const filteredEmailsAndShipments = emailsAndShipments.filter(({ email }) =>
        email.to.some((address) => address)
      );
      this.logger.log(`Filtered ${filteredEmailsAndShipments.length} emails to send`);

      const sentEmails: Array<Email> = [];
      const batchCount = Math.ceil(filteredEmailsAndShipments.length / this.EMAIL_BATCH_SIZE);
      this.logger.log(`${batchCount} batches will be sent`);
      for (let i = 0; i < batchCount; i++) {
        const batchEmailsAndShipments = filteredEmailsAndShipments.slice(
          i * this.EMAIL_BATCH_SIZE,
          (i + 1) * this.EMAIL_BATCH_SIZE
        );
        this.logger.log(
          `Sending batch ${i + 1} of ${batchCount}... (${batchEmailsAndShipments.length} emails)`
        );
        const batchSentEmails = await Promise.all(
          batchEmailsAndShipments.map(async ({ email, shipment }) => {
            const sentEmail = await this.emailService.sendEmail(email, tQueryRunner, shipment.organization);
            if (sentEmail?.threadId && !email.threadId)
              await this.emailService.setEmailThreadShipment(
                sentEmail.threadId,
                shipment,
                tQueryRunner,
                true
              );
            return sentEmail;
          })
        );
        this.logger.log(
          `Batch ${i + 1} sent ${batchSentEmails.length} emails. Email IDs: ${batchSentEmails.map((e) => e?.id).join(", ")}`
        );
        sentEmails.push(...batchSentEmails);
      }

      if (!queryRunner) await tQueryRunner.commitTransaction();
      return sentEmails;
    } catch (error) {
      if (!queryRunner && tQueryRunner.isTransactionActive) await tQueryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner) await tQueryRunner.release();
    }
  }
}
