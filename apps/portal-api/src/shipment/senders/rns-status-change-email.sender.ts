import { SendEmailDto } from "@/email/dto/email.dto";
import { EmailService } from "@/email/services/email.service";
import { EmailTemplateName } from "@/email/types/email.types";
import { forwardRef, Inject, Injectable, Logger, NotFoundException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { InjectRepository } from "@nestjs/typeorm";
import moment from "moment-timezone";
import {
  ApiTemplateService,
  CadDto,
  CandataRNSResponseDto,
  CandataService,
  CommercialInvoice,
  Currency,
  Email,
  EmailThread,
  FIND_SHIPMENT_RELATIONS,
  Importer,
  OrganizationCustomsBroker,
  Shipment
} from "nest-modules";
import { DataSource, In, Like, QueryRunner, Repository } from "typeorm";
import { RNS_RESPONSE_MESSAGE, RNSResponseCategory } from "../types/customs-status.types";
import { categorizeRNSResponse } from "../utils/categorize-rns-response";
import { convertModeOfTransport } from "../utils/convert-mode-of-transport";
import { formatNumber } from "../utils/format-number";
import { BaseEmailSender } from "./base.sender";

@Injectable()
export class RNSStatusChangeEmailSender extends BaseEmailSender {
  constructor(
    @InjectRepository(EmailThread)
    private readonly emailThreadRepository: Repository<EmailThread>,
    @InjectRepository(Email)
    private readonly emailRepository: Repository<Email>,
    @Inject(CandataService)
    private readonly candataService: CandataService,
    @Inject(forwardRef(() => EmailService))
    emailService: EmailService,
    @Inject(forwardRef(() => DataSource))
    dataSource: DataSource,
    @Inject(ConfigService)
    configService: ConfigService,
    @Inject(ApiTemplateService)
    private readonly apiTemplateService: ApiTemplateService
  ) {
    super(emailService, dataSource, new Logger(RNSStatusChangeEmailSender.name), configService);
  }

  private readonly CAD_TEMPLATE_ID = "1db77b2330119e4e";

  /**
   * Search for previous RNS status change emails for a shipment
   * @param shipment - Shipment to search for previous emails
   * @param queryRunner - QueryRunner instance. Used for handling transactions
   * @returns The previous latest RNS status change email or null if no previous email is found
   */
  private async searchForPreviousEmail(shipment: Shipment, queryRunner?: QueryRunner) {
    const shipmentThreads = await (
      queryRunner ? queryRunner.manager.getRepository(EmailThread) : this.emailThreadRepository
    ).find({
      where: { shipment: { id: shipment.id } }
    });
    this.logger.log(`Shipment ${shipment.id} has ${shipmentThreads.length} email threads`);
    const previousEmails = await (
      queryRunner ? queryRunner.manager.getRepository(Email) : this.emailRepository
    ).find({
      where: Object.values(RNS_RESPONSE_MESSAGE)
        .map((message) => [
          {
            subject: Like(`[${message}]%`),
            threadId: In(shipmentThreads.map((emailThread) => emailThread.threadId))
          },
          {
            subject: Like(`Re: [${message}]%`),
            threadId: In(shipmentThreads.map((emailThread) => emailThread.threadId))
          }
        ])
        .flatMap((condition) => condition),
      order: { receiveDate: "DESC" }
    });
    return previousEmails.length > 0 ? previousEmails[0] : null;
  }

  private async generateCADB64(
    shipment: Shipment,
    commercialInvoices: Array<CommercialInvoice>,
    organizationImporter: Importer
  ) {
    this.logger.log(`Generating CAD for shipment ${shipment.id}...`);
    const totals = commercialInvoices
      .flatMap((invoice) => invoice.commercialInvoiceLines)
      .reduce(
        (acc, line) => {
          const totalValueForDuty = (acc.totalValueForDuty || 0) + (line.valueForDuty || 0);
          const totalAntiDumping = (acc.totalAntiDumping || 0) + (line.antiDumping || 0);
          const totalCountervailing = (acc.totalCountervailing || 0) + (line.countervailing || 0);
          const totalCustomsDuties = (acc.totalCustomsDuties || 0) + (line.customsDuties || 0);
          const totalExciseDuties = (acc.totalExciseDuties || 0) + (line.exciseDuties || 0);
          const totalExciseTax = (acc.totalExciseTax || 0) + (line.exciseTax || 0);
          const totalGst = (acc.totalGst || 0) + (line.gst || 0);
          const totalPstHst = (acc.totalPstHst || 0) + (line.pstHst || 0);
          const totalProvincialAlcoholTax =
            (acc.totalProvincialAlcoholTax || 0) + (line.provincialAlcoholTax || 0);
          const totalProvincialCannabisExciseDuty =
            (acc.totalProvincialCannabisExciseDuty || 0) + (line.provincialCannabisExciseDuty || 0);
          const totalProvincialTobaccoTax =
            (acc.totalProvincialTobaccoTax || 0) + (line.provincialTobaccoTax || 0);
          const totalSafeguard = (acc.totalSafeguard || 0) + (line.safeguard || 0);
          const totalSurtax = (acc.totalSurtax || 0) + (line.surtax || 0);
          const totalTotalDutiesAndTaxes =
            (acc.totalTotalDutiesAndTaxes || 0) + (line.totalDutiesAndTaxes || 0);

          return {
            totalValueForDuty,
            totalAntiDumping,
            totalCountervailing,
            totalCustomsDuties,
            totalExciseDuties,
            totalExciseTax,
            totalGst,
            totalPstHst,
            totalProvincialAlcoholTax,
            totalProvincialCannabisExciseDuty,
            totalProvincialTobaccoTax,
            totalSafeguard,
            totalSurtax,
            totalTotalDutiesAndTaxes
          };
        },
        {
          totalValueForDuty: 0,
          totalAntiDumping: 0,
          totalCountervailing: 0,
          totalCustomsDuties: 0,
          totalExciseDuties: 0,
          totalExciseTax: 0,
          totalGst: 0,
          totalPstHst: 0,
          totalProvincialAlcoholTax: 0,
          totalProvincialCannabisExciseDuty: 0,
          totalProvincialTobaccoTax: 0,
          totalSafeguard: 0,
          totalSurtax: 0,
          totalTotalDutiesAndTaxes: 0
        }
      );

    // Looking into invoices to see what currencies are used and get the exchange rates
    const exchangeRates = new Map<Currency, number>();
    for (const invoice of commercialInvoices) {
      if (invoice.currency && !exchangeRates.has(invoice.currency) && invoice.currency !== Currency.CAD) {
        try {
          const response = await this.candataService.getExchangeRate(
            {
              currencyCode: invoice.currency.toUpperCase(),
              date: shipment.etd ? moment.tz(shipment.etd, "America/Toronto").format("YYYY-MM-DD") : undefined
            },
            shipment.organization?.customsBroker
          );
          if (typeof response?.rate === "number") exchangeRates.set(invoice.currency, response.rate);
          else
            throw new Error(
              `Invalid exchange rate response for ${invoice.currency}: ${JSON.stringify(response)}`
            );
        } catch (error) {
          this.logger.error(
            `Failed to get exchange rate for ${invoice.currency}: ${error?.message}`,
            error?.stack
          );
        }
      }
    }

    const cadPayload: CadDto = {
      type: "AB",
      wsType: "",
      submissionDate: "",
      transactionNumber: shipment.transactionNumber ?? "",
      portCode: shipment.portCode ?? "", // office no
      modeOfTransport: convertModeOfTransport(shipment.modeOfTransport),
      releaseDate: shipment.releaseDate
        ? moment.tz(shipment.releaseDate, "America/Toronto").format("YYYY-MM-DD")
        : "",
      weight: shipment.weight ? formatNumber(shipment.weight, 2) : "",
      carrierCode: shipment.carrierCode ?? "",
      preCarm: "", //
      rpp: "", //
      // row 2
      importerBusinessNumber: shipment.importer?.businessNumber ?? "",
      importerCompanyName: shipment.importer?.companyName ?? "",
      importerAddress: shipment.importer?.address ?? "",
      importerCity: shipment.importer?.city ?? "",
      importerPhoneNumber: shipment.importer?.phoneNumber ?? "",
      customsBroker: shipment.organization?.customsBroker ?? OrganizationCustomsBroker.CLARO,
      cargoControlNumber: shipment.cargoControlNumber ?? "",
      intent: "",
      previousTransactionNumber: "",
      acceptedDate: "", //
      originalTransactionNumber: "",
      prevTransNoWhse: "",
      pou: "", //
      newBusinessNumber: "",
      // row 3
      reasonCode1: "",
      reasonCode2: "",
      reasonCode3: "",
      authorityCode1: "",
      authorityCode2: "",
      authorityCode3: "",
      remark1: "",
      remark2: "",
      remark3: "",
      // row 4
      notes: "",
      warehouseIn: "",
      warehouseOut: "",
      name: (shipment.importer || organizationImporter)?.officerNameAndTitle ?? "",
      today: moment.tz("America/Toronto").format("YYYY-MM-DD"),
      // totals
      totalValueForDuty: formatNumber(totals?.totalValueForDuty, 2),
      totalPstHst: formatNumber(totals?.totalPstHst, 2),
      totalProvincialCannabisExciseDuty: formatNumber(totals?.totalProvincialCannabisExciseDuty, 2),
      totalProvincialAlcoholTax: formatNumber(totals?.totalProvincialAlcoholTax, 2),
      totalProvincialTobaccoTax: formatNumber(totals?.totalProvincialTobaccoTax, 2),
      totalRelieved: "0.0", //
      totalCustomsDuties: formatNumber(totals?.totalCustomsDuties, 2),
      totalExciseDuties: formatNumber(totals?.totalExciseDuties, 2),
      totalExciseTax: formatNumber(totals?.totalExciseTax, 2),
      totalGst: formatNumber(totals?.totalGst, 2),
      totalAntiDumping: formatNumber(totals?.totalAntiDumping, 2),
      totalCountervailing: formatNumber(totals?.totalCountervailing, 2),
      totalSurtax: formatNumber(totals?.totalSurtax, 2),
      totalSafeguard: formatNumber(totals?.totalSafeguard, 2),
      totalInterest: "0.0",
      totalTotalDutiesAndTaxes: formatNumber(totals?.totalTotalDutiesAndTaxes, 2),
      // page 2
      // commercialInvoices
      commercialInvoices: commercialInvoices?.map((invoice) => ({
        vendorName: invoice.vendor?.name ?? "",
        vendorAddress: `${invoice.vendor?.address ?? ""}, ${
          invoice.vendor?.city ?? ""
        }, ${invoice.vendor?.postalCode ?? ""}`,
        vendorPhoneNumber: invoice.vendor?.phoneNumber ?? "",
        purchaserName: invoice.purchaser?.name ?? "",
        purchaserAddress: `${invoice.purchaser?.address ?? ""}, ${
          invoice.purchaser?.city ?? ""
        }, ${invoice.purchaser?.postalCode ?? ""}`,
        purchaserPhoneNumber: invoice.purchaser?.phoneNumber ?? "",
        invoiceNumber: invoice.invoiceNumber,
        invoiceValue: formatNumber(
          (invoice.commercialInvoiceLines || []).reduce((acc, line) => acc + (line.totalLineValue || 0), 0),
          2
        ), //
        invoiceCurrency: invoice.currency.toUpperCase(),
        poNumber: invoice.poNumber,
        freightCharges: "", //
        portOfExit: "", //
        // remarks
        vendorReasonCode1: "",
        vendorReasonCode2: "",
        vendorReasonCode3: "",
        vendorAuthorityCode1: "",
        vendorAuthorityCode2: "",
        vendorAuthorityCode3: "",
        vendorRemark1: "",
        vendorRemark2: "",
        vendorRemark3: "",
        vendorTradeProgram1: "",
        vendorTradeProgram2: "",
        vendorTradeProgram3: "",
        // line items
        lines: invoice.commercialInvoiceLines
          .sort((a, b) => {
            if (typeof a.sequence === "number" && typeof b.sequence === "number")
              return a.sequence - b.sequence;
            else return a.id - b.id;
          })
          .map((line) => ({
            sequence: line.sequence ?? 0, // duty
            previousLineNo: "", //
            hsCode: line.hsCode ?? "",
            hsCodeDescription: "", //
            description: line.goodsDescription ?? "",
            quantity: line.quantity ? formatNumber(line.quantity, 2) : "",
            quantityUOM: line.unitOfMeasure?.toUpperCase() ?? "",
            timeLimitType: (line.timeLimitType ?? "") as string, //
            extensionDate: "", //
            countryOfOrigin: line.origin?.alpha2 ?? "", //
            countryOfOriginState: line.origin?.alpha2 === "US" ? (line.originState?.alpha2 ?? "") : "", // - only for US
            countryOfExport: invoice.countryOfExport?.alpha2 ?? "", //
            countryOfExportState:
              invoice.countryOfExport?.alpha2 === "US" ? (invoice.stateOfExport?.alpha2 ?? "") : "", // - only for US
            directShipmentDate: shipment.etd
              ? moment.tz(shipment.etd, "America/Toronto").format("YYYY-MM-DD")
              : "", //
            tt: line.tt ? String(line.tt?.code) : "", //
            tariffCode: line.tariffCode?.hsCode ?? "", //
            timeLimitFrom: line.timeLimitStartDate
              ? moment.tz(line.timeLimitStartDate, "America/Toronto").format("YYYY-MM-DD")
              : "", //
            timeLimitTo: line.timeLimitEndDate
              ? moment.tz(line.timeLimitEndDate, "America/Toronto").format("YYYY-MM-DD")
              : "", //
            destinationProvince: line.destinationProvince ?? "", //
            valueForCurrencyConversion: line.totalLineValue ? formatNumber(line.totalLineValue, 2) : "", // duty
            currency: invoice.currency?.toUpperCase() ?? "", // duty
            exchangeRate: formatNumber(exchangeRates.get(invoice.currency) || 1, 4), // duty
            valueForDuty: line.valueForDuty ? formatNumber(line.valueForDuty, 2) : "", // duty
            drpLicense: line.dutiesReliefLicence ?? "", //
            orderInCouncil: line.orderInCouncil ?? "", //
            authorityPermit: line.authorityPermit ?? "", //
            customsDuties: line.customsDuties ? formatNumber(line.customsDuties, 2) : "", // duty
            exciseTax: line.exciseTax ? formatNumber(line.exciseTax, 2) : "", // duty
            exciseDuties: line.exciseDuties ? formatNumber(line.exciseDuties, 2) : "", // duty
            surTax: line.surtax ? formatNumber(line.surtax, 2) : "", // duty
            antiDumping: line.antiDumping ? formatNumber(line.antiDumping, 2) : "", // duty
            safeguard: line.safeguard ? formatNumber(line.safeguard, 2) : "", // duty
            countervailing: line.countervailing ? formatNumber(line.countervailing, 2) : "", // duty
            valueForTax: line.valueForTax ? formatNumber(line.valueForTax, 2) : "", // duty
            gst: line.gst ? formatNumber(line.gst, 2) : "", // duty
            pstHst: line.pstHst ? formatNumber(line.pstHst, 2) : "", // duty
            provincialAlcoholTax: line.provincialAlcoholTax ? formatNumber(line.provincialAlcoholTax, 2) : "", // duty
            provincialTobaccoTax: line.provincialTobaccoTax ? formatNumber(line.provincialTobaccoTax, 2) : "", // duty
            alcoholPercentage: line.alcoholPercentage ? formatNumber(line.alcoholPercentage, 0) : "",
            provincialCannabisExciseDuty: line.provincialCannabisExciseDuty
              ? formatNumber(line.provincialCannabisExciseDuty, 2)
              : "", // duty
            cbsaCaseNo: "", //
            rulingNo: "", //
            appealCaseNo: "", //
            complCaseNo: "", //
            totalDutiesAndTaxes: line.totalDutiesAndTaxes ? formatNumber(line.totalDutiesAndTaxes, 2) : "", // duty
            // remarks
            reasonCode1: "",
            reasonCode2: "",
            reasonCode3: "",
            authorityCode1: "",
            authorityCode2: "",
            authorityCode3: "",
            commodityRemark1: "",
            commodityRemark2: "",
            commodityRemark3: "",
            commodityAppealsProgram1: "",
            commodityAppealsProgram2: "",
            commodityAppealsProgram3: ""
          }))
      }))
    };
    return (await this.apiTemplateService.createPdf(
      {
        template_id: this.CAD_TEMPLATE_ID,
        export_type: "file",
        expiration: 5,
        output_format: "pdf",
        export_in_base64: "1",
        filename: `CAD-${shipment.hblNumber}-${shipment.cargoControlNumber}.pdf`,
        direct_download: "0"
      },
      cadPayload
    )) as string;
  }

  /**
   * Prepares RNS status change emails for a shipment.
   * Returns 2 emails for ACCEPTED_WAITING (importer with CAD, trucker without CAD).
   * Returns 1 email for all other statuses (both importer and trucker).
   */
  private async prepareRNSStatusChangeEmails(
    shipment: Shipment,
    organizationImporter: Importer,
    commercialInvoices: Array<CommercialInvoice>,
    rnsResponse: CandataRNSResponseDto,
    previousEmail?: Email | null
  ): Promise<Array<SendEmailDto>> {
    const rnsCategory = categorizeRNSResponse(rnsResponse);

    if (rnsCategory === RNSResponseCategory.ACCEPTED_WAITING) {
      return [
        await this.prepareAcceptedWaitingImporterEmail(
          shipment,
          organizationImporter,
          commercialInvoices,
          rnsResponse,
          previousEmail
        ),
        this.prepareRNSStatusEmail(shipment, organizationImporter, rnsResponse, previousEmail, [
          shipment.trucker?.email
        ])
      ];
    } else {
      return [
        this.prepareRNSStatusEmail(shipment, organizationImporter, rnsResponse, previousEmail, [
          (shipment.importer || organizationImporter)?.email,
          shipment.trucker?.email
        ])
      ];
    }
  }

  /**
   * Prepares a standard RNS status email with specified recipients.
   */
  prepareRNSStatusEmail(
    shipment: Shipment,
    organizationImporter: Importer,
    rnsResponse: CandataRNSResponseDto,
    previousEmail: Email | null | undefined,
    recipients: (string | null | undefined)[]
  ): SendEmailDto {
    const rnsCategory = categorizeRNSResponse(rnsResponse);
    const rnsResponseMessage = RNS_RESPONSE_MESSAGE[rnsCategory];
    const { subject, body } = this.emailService.renderEmailTemplateWithSubject(
      EmailTemplateName.RNS_STATUS_CHANGE_EMAIL,
      {
        rnsResponseMessage,
        hblNumber: shipment.hblNumber,
        cargoControlNumber: shipment.cargoControlNumber,
        containerNumbers: (shipment.containers || []).map((c) => c.containerNumber).join(",") || null,
        transactionNumber: shipment.transactionNumber,
        importerName: (shipment.importer || organizationImporter)?.companyName,
        processDate: moment.tz(rnsResponse.processDate, "America/Toronto").format("YYYY-MM-DD HH:mm"),
        responseDate: moment.tz(rnsResponse.responseDate, "America/Toronto").format("YYYY-MM-DD HH:mm"),
        portCode: rnsResponse.port,
        sublocationCode: rnsResponse.sublocation,
        processingIndicator: rnsResponse.processingIndicator
      }
    );

    return {
      threadId: previousEmail?.threadId ?? undefined,
      from: this.BACKOFFICE_EMAIL,
      to: recipients.filter(Boolean) as string[],
      cc: [this.BACKOFFICE_EMAIL],
      subject: previousEmail ? `Re: ${previousEmail?.subject}` : subject,
      text: body
    };
  }

  /**
   * Creates a CAD attachment object for email.
   * Public API for generating CAD attachments across the application.
   */
  async createCADAttachment(
    shipment: Shipment,
    commercialInvoices: Array<CommercialInvoice>,
    organizationImporter: Importer,
    queryRunner?: QueryRunner
  ): Promise<{ fileName: string; mimeType: string; b64Data: string }> {
    this.logger.log(`📄 Creating CAD attachment for shipment ${shipment.id}`);

    this.logger.log(`📋 Retrieving up-to-date shipment data for shipment ${shipment.id} with full relations`);
    const upToDateShipment = await (queryRunner ? queryRunner.manager : this.dataSource.manager).findOne(
      Shipment,
      {
        where: { id: shipment.id },
        relations: FIND_SHIPMENT_RELATIONS
      }
    );

    if (!upToDateShipment) {
      this.logger.error(`❌ Shipment ${shipment.id} not found in database during CAD generation`);
      throw new NotFoundException(`Shipment ${shipment.id} not found`);
    }

    this.logger.log(
      `✅ Retrieved shipment data: hblNumber=${upToDateShipment.hblNumber}, cargoControlNumber=${upToDateShipment.cargoControlNumber}, organization=${upToDateShipment.organization?.name}`
    );

    const fileName = `CAD-${upToDateShipment.hblNumber}-${upToDateShipment.cargoControlNumber}.pdf`;
    this.logger.log(`🏭 Starting PDF generation for file: ${fileName}`);

    try {
      const b64Data = await this.generateCADB64(upToDateShipment, commercialInvoices, organizationImporter);

      this.logger.log(
        `✅ Successfully generated CAD PDF for shipment ${shipment.id}, base64 size: ${b64Data?.length || 0} characters`
      );

      return {
        fileName,
        mimeType: "application/pdf",
        b64Data
      };
    } catch (error) {
      this.logger.error(
        `❌ Failed to generate CAD PDF for shipment ${shipment.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Prepares the special importer email with CAD attachment for ACCEPTED_WAITING status.
   */
  private async prepareAcceptedWaitingImporterEmail(
    shipment: Shipment,
    organizationImporter: Importer,
    commercialInvoices: Array<CommercialInvoice>,
    rnsResponse: CandataRNSResponseDto,
    previousEmail?: Email | null
  ): Promise<SendEmailDto> {
    const baseEmail = this.prepareRNSStatusEmail(shipment, organizationImporter, rnsResponse, previousEmail, [
      (shipment.importer || organizationImporter)?.email
    ]);

    return {
      ...baseEmail,
      attachments: [await this.createCADAttachment(shipment, commercialInvoices, organizationImporter)]
    };
  }

  async sendRNSStatusChangeEmails(
    shipmentIdsAndRNSResponses: Array<{ shipmentId: number; rnsResponse: CandataRNSResponseDto }>
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get shipments
      const shipments = await this.getShipments(
        shipmentIdsAndRNSResponses.map(({ shipmentId }) => shipmentId),
        queryRunner
      );

      // Get commercial invoices
      const commercialInvoices = await this.getCommercialInvoicesByShipmentIds(
        shipments.map(({ id }) => id),
        queryRunner
      );
      this.logger.log(`Found ${commercialInvoices.length} commercial invoices`);

      // Get organization importers
      const organizationImporters = await this.getOrganizationImporters(shipments, queryRunner);
      this.logger.log(`Found ${organizationImporters.length} organization importers`);

      // Prepare email DTOs
      const cachedPreviousEmails = new Map<number, Email>();
      const importerEmailsAndShipments: Array<{ email: SendEmailDto; shipment: Shipment }> = [];
      const truckerEmailsAndShipments: Array<{ email: SendEmailDto; shipment: Shipment }> = [];
      for (const shipment of shipments) {
        const shipmentInvoices = commercialInvoices.filter((ci) => ci.shipment?.id === shipment.id);
        this.logger.log(`Found ${shipmentInvoices.length} commercial invoices for shipment ${shipment.id}`);

        const organizationImporter = organizationImporters.find(
          (importer) => importer.organization?.id === shipment.organization?.id
        );
        const rnsResponse = shipmentIdsAndRNSResponses.find(
          ({ shipmentId }) => shipmentId === shipment.id
        )?.rnsResponse;
        let previousEmail = cachedPreviousEmails.get(shipment.id);
        if (!previousEmail) {
          previousEmail = await this.searchForPreviousEmail(shipment, queryRunner);
          cachedPreviousEmails.set(shipment.id, previousEmail);
        }

        const emails = await this.prepareRNSStatusChangeEmails(
          shipment,
          organizationImporter,
          shipmentInvoices,
          rnsResponse,
          previousEmail
        );
        importerEmailsAndShipments.push({
          email: emails[0],
          shipment
        });
        if (emails.length >= 2)
          truckerEmailsAndShipments.push({
            email: emails[1],
            shipment
          });
      }

      // Send importer emails first
      const sentImporterEmails = await this.sendEmails(importerEmailsAndShipments, queryRunner);
      this.logger.log(`Sent ${sentImporterEmails.length} importer RNS status change emails`);

      // Update trucker emails' thread ID if needed, and send them
      const sentTruckerEmails = await this.sendEmails(
        truckerEmailsAndShipments.map(({ email, shipment }) => {
          if (!email.threadId) {
            email.threadId = sentImporterEmails.find(({ subject }) => subject === email.subject)?.threadId;
          }
          return { email, shipment };
        })
      );
      this.logger.log(`Sent ${sentTruckerEmails.length} trucker RNS status change emails`);

      await queryRunner.commitTransaction();
      return sentImporterEmails.concat(sentTruckerEmails);
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error while sending RNS status change emails: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
    } finally {
      await queryRunner.release();
    }
  }
}
