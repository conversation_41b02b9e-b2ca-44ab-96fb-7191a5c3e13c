import { SendEmailDto } from "@/email/dto/email.dto";
import { EmailService } from "@/email/services/email.service";
import { EmailTemplateName } from "@/email/types/email.types";
import { forwardRef, Inject, Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { Email, EmailThread, Importer, Shipment } from "nest-modules";
import { DataSource, Repository } from "typeorm";
import { BaseEmailSender } from "./base.sender";
import { InjectRepository } from "@nestjs/typeorm";

@Injectable()
export class LiveShipmentEmailSender extends BaseEmailSender {
  constructor(
    @InjectRepository(EmailThread)
    private readonly emailThreadRepository: Repository<EmailThread>,
    @InjectRepository(Email)
    private readonly emailRepository: Repository<Email>,
    @Inject(forwardRef(() => EmailService))
    emailService: EmailService,
    @Inject(forwardRef(() => DataSource))
    dataSource: DataSource,
    @Inject(ConfigService)
    configService: ConfigService
  ) {
    super(emailService, dataSource, new Logger(LiveShipmentEmailSender.name), configService);
  }

  private prepareLiveShipmentEmail(
    shipment: Shipment,
    organizationImporter: Importer,
    threadId?: string
  ): SendEmailDto {
    const { subject, body } = this.emailService.renderEmailTemplateWithSubject(
      EmailTemplateName.LIVE_SHIPMENT_EMAIL,
      {
        placeOfDelivery: shipment.placeOfDelivery?.name,
        modeOfTransport: shipment.modeOfTransport,
        // containerNumber: shipment.containerNumber,
        containers: (shipment.containers || []).map((c) => ({
          containerNumber: c.containerNumber,
          status: c.status
        })),
        hblNumber: shipment.hblNumber,
        transactionNumber: shipment.transactionNumber,
        etaDestination: shipment.etaDestination,
        organizationName: shipment.organization?.name,
        // status: shipment.status,
        shipper: shipment.shipper?.name,
        importer: (shipment.importer || organizationImporter)?.companyName,
        fileNumber: shipment.customsFileNumber,
        claroUrl: `${this.PORTAL_FRONTEND_URL}/shipment/${shipment.id}`
      }
    );

    return {
      threadId: threadId ?? undefined,
      from: this.BACKOFFICE_EMAIL,
      to: [this.BACKOFFICE_EMAIL],
      subject,
      text: body
    };
  }

  async sendLiveShipmentEmails(shipmentIds: Array<number>) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get live shipments
      const liveShipments = await this.getShipments(shipmentIds, queryRunner);

      // Get organization importers
      const organizationImporters = await this.getOrganizationImporters(liveShipments, queryRunner);
      this.logger.log(`Found ${organizationImporters.length} organization importers`);

      // Prepare email DTOs and send emails
      const emailsAndShipments = liveShipments.map((shipment) => ({
        shipment,
        email: this.prepareLiveShipmentEmail(
          shipment,
          organizationImporters.find((importer) => importer.organization?.id === shipment.organization?.id)
        )
      }));
      const sentEmails = await this.sendEmails(emailsAndShipments, queryRunner);
      this.logger.log(`Sent ${sentEmails.length} live shipment emails`);

      await queryRunner.commitTransaction();
      return sentEmails;
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error while sending live shipment emails: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
    } finally {
      await queryRunner.release();
    }
  }
}
