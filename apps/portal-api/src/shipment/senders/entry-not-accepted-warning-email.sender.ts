import { SendEmailDto } from "@/email/dto/email.dto";
import { EmailService } from "@/email/services/email.service";
import { EmailTemplateName } from "@/email/types/email.types";
import { forwardRef, Inject, Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import moment from "moment-timezone";
import { Email, EmailThread, FIND_SHIPMENT_RELATIONS, Importer, Shipment, ShipmentMode } from "nest-modules";
import { DataSource, In, Like, QueryRunner, Repository } from "typeorm";
import { BaseEmailSender } from "./base.sender";
import { InjectRepository } from "@nestjs/typeorm";
@Injectable()
export class EntryNotAcceptedWarningEmailSender extends BaseEmailSender {
  constructor(
    @InjectRepository(EmailThread)
    private readonly emailThreadRepository: Repository<EmailThread>,
    @InjectRepository(Email)
    private readonly emailRepository: Repository<Email>,
    @Inject(forwardRef(() => EmailService))
    emailService: EmailService,
    @Inject(forwardRef(() => DataSource))
    dataSource: DataSource,
    @Inject(ConfigService)
    configService: ConfigService
  ) {
    super(emailService, dataSource, new Logger(EntryNotAcceptedWarningEmailSender.name), configService);
  }

  private async searchForPreviousEmail(shipment: Shipment, queryRunner?: QueryRunner) {
    const shipmentThreads = await (
      queryRunner ? queryRunner.manager.getRepository(EmailThread) : this.emailThreadRepository
    ).find({
      where: { shipment: { id: shipment.id } }
    });
    this.logger.log(`Shipment ${shipment.id} has ${shipmentThreads.length} email threads`);
    const previousEmail = await (
      queryRunner ? queryRunner.manager.getRepository(Email) : this.emailRepository
    ).findOne({
      where: {
        subject: Like(`[IMPORTANT - Entry Not Accepted]%`),
        threadId: In(shipmentThreads.map((emailThread) => emailThread.threadId))
      },
      order: { receiveDate: "DESC" }
    });
    return previousEmail || null;
  }

  private prepareEntryNotAcceptedEmail(
    shipment: Shipment,
    organizationImporter: Importer,
    entrySubmittedDate: string
  ): SendEmailDto {
    let timeSinceEntryValidation: string = null;
    switch (shipment.modeOfTransport) {
      case ShipmentMode.LAND:
        timeSinceEntryValidation = "30 minutes";
        break;
      case ShipmentMode.AIR:
        timeSinceEntryValidation = "1 hour";
        break;
      case ShipmentMode.OCEAN_FCL:
      case ShipmentMode.OCEAN_LCL:
        timeSinceEntryValidation = "2 hours";
        break;
      default:
        throw new Error(
          `Shipment mode of transport ${shipment.modeOfTransport} is not supported for sending entry not accepted warning email`
        );
    }

    const { subject, body } = this.emailService.renderEmailTemplateWithSubject(
      EmailTemplateName.ENTRY_NOT_ACCEPTED_WARNING_EMAIL,
      {
        hblNumber: shipment.hblNumber,
        cargoControlNumber: shipment.cargoControlNumber,
        // containerNumber: shipment.containerNumber,
        containerNumbers: (shipment.containers || []).map((c) => c.containerNumber).join(",") || null,
        transactionNumber: shipment.transactionNumber,
        importerName: (shipment.importer || organizationImporter)?.companyName,
        entrySubmittedDate: moment.tz(entrySubmittedDate, "America/Toronto").format("YYYY-MM-DD HH:mm"),
        timeSinceEntryValidation
      }
    );

    return {
      from: this.BACKOFFICE_EMAIL,
      to: [this.BACKOFFICE_EMAIL],
      subject,
      text: body
    };
  }

  async sendEntryNotAcceptedWarningEmails(
    shipmentIdsAndDates: Array<{
      shipmentId: number;
      entrySubmittedDate: string;
    }>
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const shipmentRepository = queryRunner.manager.getRepository(Shipment);

      // List of shipments that should send entry not accepted warning emails
      const shipments = await shipmentRepository.find({
        where: { id: In(shipmentIdsAndDates.map(({ shipmentId }) => shipmentId)) },
        relations: FIND_SHIPMENT_RELATIONS
      });
      this.logger.log(`Found ${shipments.length} shipments`);
      const missingShipmentIds = shipmentIdsAndDates.filter(
        ({ shipmentId }) => !shipments.some((shipment) => shipment.id === shipmentId)
      );
      if (missingShipmentIds.length > 0)
        this.logger.warn(`Missing following shipment IDs: ${JSON.stringify(missingShipmentIds)}`);

      // Get organization importers
      const organizationImporters = await this.getOrganizationImporters(shipments, queryRunner);
      this.logger.log(`Found ${organizationImporters.length} organization importers`);

      // Do not send email if already sent within last 12 hours
      const filteredShipments: Array<Shipment> = [];
      for (const shipment of shipments) {
        const previousEmail = await this.searchForPreviousEmail(shipment, queryRunner);
        this.logger.log(
          `Shipment ${shipment.id} previous email: ${previousEmail?.id}, receive date: ${previousEmail?.receiveDate}`
        );
        if (!previousEmail || previousEmail?.receiveDate?.getTime() <= Date.now() - 12 * 60 * 60 * 1000)
          filteredShipments.push(shipment);
      }
      this.logger.log(`Filtered ${filteredShipments.length} shipments to send emails`);

      // Prepare email DTOs and send emails
      const emailsAndShipments = filteredShipments.map((shipment) => ({
        shipment,
        email: this.prepareEntryNotAcceptedEmail(
          shipment,
          organizationImporters.find((importer) => importer.organization?.id === shipment.organization?.id),
          shipmentIdsAndDates.find(({ shipmentId }) => shipmentId === shipment.id)?.entrySubmittedDate
        )
      }));
      const sentEmails = await this.sendEmails(emailsAndShipments, queryRunner);
      this.logger.log(`Sent ${sentEmails.length} entry not accepted warning emails`);

      await queryRunner.commitTransaction();
      return sentEmails;
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error while sending entry not accepted warning emails: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
    } finally {
      await queryRunner.release();
    }
  }
}
