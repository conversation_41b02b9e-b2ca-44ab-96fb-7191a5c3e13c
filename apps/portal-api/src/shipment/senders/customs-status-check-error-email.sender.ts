import { SendEmailDto } from "@/email/dto/email.dto";
import { EmailService } from "@/email/services/email.service";
import { EmailTemplateName } from "@/email/types/email.types";
import { forwardRef, Inject, Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { Email, EmailThread, Shipment } from "nest-modules";
import { DataSource, In, Like, QueryRunner, Repository } from "typeorm";
import { BaseEmailSender } from "./base.sender";
import { InjectRepository } from "@nestjs/typeorm";

@Injectable()
export class CustomsStatusCheckErrorEmailSender extends BaseEmailSender {
  constructor(
    @InjectRepository(EmailThread)
    private readonly emailThreadRepository: Repository<EmailThread>,
    @InjectRepository(Email)
    private readonly emailRepository: Repository<Email>,
    @Inject(forwardRef(() => EmailService))
    emailService: EmailService,
    @Inject(forwardRef(() => DataSource))
    dataSource: DataSource,
    @Inject(ConfigService)
    configService: ConfigService
  ) {
    super(emailService, dataSource, new Logger(CustomsStatusCheckErrorEmailSender.name), configService);
  }

  private async searchForPreviousEmail(shipment: Shipment, queryRunner?: QueryRunner) {
    const shipmentThreads = await (
      queryRunner ? queryRunner.manager.getRepository(EmailThread) : this.emailThreadRepository
    ).find({
      where: { shipment: { id: shipment.id } }
    });
    this.logger.log(`Shipment ${shipment.id} has ${shipmentThreads.length} email threads`);
    const previousEmail = await (
      queryRunner ? queryRunner.manager.getRepository(Email) : this.emailRepository
    ).findOne({
      where: {
        text: Like(`%The following shipment has errors during customs status check:%`),
        threadId: In(shipmentThreads.map((emailThread) => emailThread.threadId))
      },
      order: { receiveDate: "DESC" }
    });
    return previousEmail || null;
  }

  private prepareCustomsStatusCheckErrorEmail(
    shipment: Shipment,
    errorType: string,
    errorMessage: string,
    stackTrace?: string
  ): SendEmailDto {
    const { subject, body } = this.emailService.renderEmailTemplateWithSubject(
      EmailTemplateName.CUSTOMS_STATUS_CHECK_ERROR_EMAIL,
      {
        errorType,
        hblNumber: shipment.hblNumber,
        cargoControlNumber: shipment.cargoControlNumber,
        //containerNumber: shipment.containerNumber,
        containerNumbers: (shipment.containers || []).map((c) => c.containerNumber).join(",") || null,
        transactionNumber: shipment.transactionNumber,
        fileNumber: shipment.customsFileNumber,
        organizationName: shipment.organization?.name,
        shipmentId: shipment.id,
        claroUrl: `${this.PORTAL_FRONTEND_URL}/shipment/${shipment.id}`,
        errorMessage,
        stackTrace
      }
    );

    return {
      from: this.BACKOFFICE_EMAIL,
      to: [this.BACKOFFICE_EMAIL],
      subject,
      text: body
    };
  }

  async sendCustomsStatusCheckErrorEmails(
    shipmentIdsAndErrors: Array<{
      shipmentId: number;
      errorType: string;
      errorMessage: string;
      stackTrace?: string;
    }>
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // List of shipments that should send customs status check error emails
      const shipments = await this.getShipments(
        shipmentIdsAndErrors.map(({ shipmentId }) => shipmentId),
        queryRunner
      );

      // Do not send email if already sent within last 24 hours
      const filteredShipments: Array<Shipment> = [];
      for (const shipment of shipments) {
        const previousEmail = await this.searchForPreviousEmail(shipment, queryRunner);
        this.logger.log(
          `Shipment ${shipment.id} previous email: ${previousEmail?.id}, receive date: ${previousEmail?.receiveDate}`
        );
        if (!previousEmail || previousEmail?.receiveDate?.getTime() <= Date.now() - 24 * 60 * 60 * 1000)
          filteredShipments.push(shipment);
      }
      this.logger.log(`Filtered ${filteredShipments.length} shipments to send emails`);

      // Prepare email DTOs and send emails
      const emailsAndShipments = filteredShipments.map((shipment) => {
        const { errorType, errorMessage, stackTrace } = shipmentIdsAndErrors.find(
          ({ shipmentId }) => shipmentId === shipment.id
        );
        return {
          shipment,
          email: this.prepareCustomsStatusCheckErrorEmail(shipment, errorType, errorMessage, stackTrace)
        };
      });
      const sentEmails = await this.sendEmails(emailsAndShipments, queryRunner);
      this.logger.log(`Sent ${sentEmails.length} customs status check error emails`);

      await queryRunner.commitTransaction();
      return sentEmails;
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error while sending customs status check error emails: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
    } finally {
      await queryRunner.release();
    }
  }
}
