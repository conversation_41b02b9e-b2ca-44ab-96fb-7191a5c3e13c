import { CandataPartyDto, CandataProvince, toUpperSnakeCase, TradePartner } from "nest-modules";

export class TradePartnerMapper {
  /**
   * Maps a TradePartner entity to a CandataPartyDto
   * @param partner The TradePartner entity to map
   * @returns A CandataPartyDto object
   */
  toCandataPartyDto(partner: TradePartner): CandataPartyDto {
    const isUSOrCanadaAddress = ["CA", "US"].includes(partner?.country?.alpha2);

    return {
      name: (partner?.name || "").slice(0, 35),
      name2:
        typeof partner?.name === "string" && partner?.name?.length > 35 ? partner?.name?.slice(35, 70) : "",
      address1: (partner?.address || "").slice(0, 35),
      address2:
        typeof partner?.address === "string" && partner?.address?.length > 35
          ? partner?.address?.slice(35, 70)
          : "",
      city: partner?.city || "",
      province:
        isUSOrCanadaAddress && partner?.state
          ? Object.values(CandataProvince).includes(partner?.state as CandataProvince)
            ? partner?.state
            : CandataProvince[toUpperSnakeCase(partner?.state)] || ""
          : "",
      postalCode: partner?.postalCode || "",
      country: partner?.country?.alpha2 || "",
      phone: partner?.phoneNumber || "",
      emailAddress: partner?.email || "",
      contact: partner?.name || ""
    };
  }
}
