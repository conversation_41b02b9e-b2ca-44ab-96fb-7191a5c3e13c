import { CandataPartyDto, TradePartner } from "nest-modules";
import { DateMapper } from "./date.mapper";
import { TradePartnerMapper } from "./trade-partner.mapper";

export class MapperFacade {
  static tradePartnerToCandataPartyDto(partner: TradePartner): CandataPartyDto {
    return new TradePartnerMapper().toCandataPartyDto(partner);
  }

  static dateToCandataDate(date: Date) {
    return new DateMapper().toCandataDate(date);
  }
}
