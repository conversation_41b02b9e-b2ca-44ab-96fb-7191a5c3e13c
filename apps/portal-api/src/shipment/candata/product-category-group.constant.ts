import { OGD_HEALTH_CANADA_PROGRAM } from "nest-modules";

/**
 * Product Category Group
 *
 * @description
 * This enum contains the product category groups that are used in the IID System.
 *
 * @date 2025-04-21
 * @reference SWI_ECCD_v5.0_DRAFT(v2) - Appendix G.G21 - Product Category Group - p. 443
 */
export enum ProductCategoryGroupQualifier {
  HC_ACTIVE_PHARMACEUTICAL_INGREDIENTS = "HC01",
  HC_BLOOD_AND_BLOOD_COMPONENTS = "HC02",
  HC_CELLS_TISSUES_AND_ORGANS = "HC03",
  HC_DONOR_SEMEN_OR_OVA = "HC04",
  HC_HUMAN_DRUGS = "HC05",
  HC_MEDICAL_DEVICES = "HC06",
  HC_NATURAL_HEALTH_PRODUCTS = "HC07",
  HC_VETERINARY_DRUGS = "HC08",
  HC_OFFICE_OF_CONTROLLED_SUBSTANCES = "HC09",
  HC_CONSUMER_PRODUCTS = "HC11",
  HC_RADIATION_EMITTING_DEVICES = "HC12",
  HC_PEST_CONTROL_PRODUCTS = "HC13",

  EC_VEHICLE_CLASS = "EC01",
  EC_ENGINE_CLASS = "EC02",
  EC_WILDLIFE_ENFORCEMENT = "EC03",
  EC_ALTERNATIVE_STANDARD_OF_ENGINE = "EC04",

  TC_TIRE_CLASS = "TC01",
  TC_TIRE_TYPE = "TC02",
  TC_TIRE_SIZE = "TC03",
  TC_TITLE_STATUS = "TC04",
  TC_VEHICLE_STATUS = "TC05",

  FA_FREE_TRADE_AGREEMENT = "FA01",

  FO_AQUATIC_BIOTECHNOLOGY = "FO01",
  FO_TRADE_TRACKING = "FO02",

  PH_HUMAN_TERRESTRIAL_ANIMAL_PATHOGENS = "PH01",

  NR_OFFICE_OF_ENERGY_EFFICIENCY = "NR01"
}

export const HCProgramMap: Record<OGD_HEALTH_CANADA_PROGRAM, ProductCategoryGroupQualifier> = {
  [OGD_HEALTH_CANADA_PROGRAM.ACTIVE_PHARMACEUTICAL_INGREDIENTS]:
    ProductCategoryGroupQualifier.HC_ACTIVE_PHARMACEUTICAL_INGREDIENTS,
  [OGD_HEALTH_CANADA_PROGRAM.BLOOD_AND_BLOOD_COMPONENTS]:
    ProductCategoryGroupQualifier.HC_BLOOD_AND_BLOOD_COMPONENTS,
  [OGD_HEALTH_CANADA_PROGRAM.CELL_TISSUES_AND_ORGAN]:
    ProductCategoryGroupQualifier.HC_CELLS_TISSUES_AND_ORGANS,
  [OGD_HEALTH_CANADA_PROGRAM.DONOR_SEMEN_AND_OVA_PROGRAM]:
    ProductCategoryGroupQualifier.HC_DONOR_SEMEN_OR_OVA,
  [OGD_HEALTH_CANADA_PROGRAM.HUMAN_DRUGS]: ProductCategoryGroupQualifier.HC_HUMAN_DRUGS,
  [OGD_HEALTH_CANADA_PROGRAM.MEDICAL_DEVICES]: ProductCategoryGroupQualifier.HC_MEDICAL_DEVICES,
  [OGD_HEALTH_CANADA_PROGRAM.NATURAL_HEALTH_PRODUCTS]:
    ProductCategoryGroupQualifier.HC_NATURAL_HEALTH_PRODUCTS,
  [OGD_HEALTH_CANADA_PROGRAM.VETERINARY_DRUGS]: ProductCategoryGroupQualifier.HC_VETERINARY_DRUGS,
  [OGD_HEALTH_CANADA_PROGRAM.CONSUMER_PRODUCT_SAFETY]: ProductCategoryGroupQualifier.HC_CONSUMER_PRODUCTS,
  [OGD_HEALTH_CANADA_PROGRAM.OFFICE_OF_CONTROLLED_SUBSTANCES]:
    ProductCategoryGroupQualifier.HC_OFFICE_OF_CONTROLLED_SUBSTANCES,
  [OGD_HEALTH_CANADA_PROGRAM.PESTICIDES_PEST_MANAGEMENT_REGULATORY_AGENCY]:
    ProductCategoryGroupQualifier.HC_PEST_CONTROL_PRODUCTS,
  [OGD_HEALTH_CANADA_PROGRAM.RADIATION_EMITTING_DEVICES]:
    ProductCategoryGroupQualifier.HC_RADIATION_EMITTING_DEVICES
};
