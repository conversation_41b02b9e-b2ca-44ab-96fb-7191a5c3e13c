import { CommercialInvoiceColumn, ShipmentColumn, ShipmentMode, TradePartnerColumn } from "nest-modules";

export const SHIPMENT_REQUIRED_FIELDS: Record<ShipmentMode, Array<ShipmentColumn>> = {
  [ShipmentMode.AIR]: [
    ShipmentColumn.cargoControlNumber,
    ShipmentColumn.portCode,
    ShipmentColumn.subLocation,
    ShipmentColumn.modeOfTransport,
    ShipmentColumn.weight,
    ShipmentColumn.weightUOM,
    ShipmentColumn.portOfLoadingId
  ],
  [ShipmentMode.OCEAN_FCL]: [
    ShipmentColumn.cargoControlNumber,
    ShipmentColumn.portCode,
    ShipmentColumn.subLocation,
    ShipmentColumn.modeOfTransport,
    ShipmentColumn.weight,
    ShipmentColumn.weightUOM,
    ShipmentColumn.portOfLoadingId
  ],
  [ShipmentMode.LAND]: [
    ShipmentColumn.cargoControlNumber,
    ShipmentColumn.portCode,
    ShipmentColumn.modeOfTransport,
    // TODO: currently we have not implemented port of exit
    // ShipmentColumn.portOfExit
    ShipmentColumn.weight,
    ShipmentColumn.weightUOM
  ],
  [ShipmentMode.OCEAN_LCL]: [
    ShipmentColumn.cargoControlNumber,
    ShipmentColumn.portCode,
    ShipmentColumn.subLocation,
    ShipmentColumn.modeOfTransport,
    ShipmentColumn.weight,
    ShipmentColumn.weightUOM,
    ShipmentColumn.portOfLoadingId
  ],
  [ShipmentMode.SMALL_PACKAGE]: []
};

export const OTHER_REQUIRED_FIELDS = [
  ShipmentColumn.importerId,
  ShipmentColumn.etaDestination,
  ShipmentColumn.etd
];

export const CI_REQUIRED_FIELDS = [
  CommercialInvoiceColumn.currency,
  CommercialInvoiceColumn.grossWeight,
  CommercialInvoiceColumn.weightUOM,
  CommercialInvoiceColumn.numberOfPackages,
  CommercialInvoiceColumn.packageUOM,
  CommercialInvoiceColumn.countryOfExportId,
  CommercialInvoiceColumn.vendorId,
  CommercialInvoiceColumn.shipToId
];

export const TRADE_PARTNER_REQUIRED_FIELDS = {
  vendor: [TradePartnerColumn.address, TradePartnerColumn.city, TradePartnerColumn.countryId],
  shipTo: [TradePartnerColumn.address, TradePartnerColumn.city, TradePartnerColumn.countryId]
};
