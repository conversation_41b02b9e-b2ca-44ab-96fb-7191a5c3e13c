import { CommercialInvoiceModule } from "@/commercial-invoice/commercial-invoice.module";
import { EmailModule } from "@/email/email.module";
import { ImporterModule } from "@/importer/importer.module";
import { OgdFilingModule } from "@/ogd-filing/ogd-filing.module";
import { ProductModule } from "@/product/product.module";
import { SimaFilingModule } from "@/sima-filing/sima-filing.module";
import { UnitConversionModule } from "@/unit-conversion/unit-conversion.module";
import { BullModule } from "@nestjs/bullmq";
import { DynamicModule, forwardRef, Logger, Module, ModuleMetadata, Provider } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  CanadaTariff,
  CarmModule,
  CommercialInvoice,
  CommercialInvoiceLine,
  Container,
  Email,
  EmailThread,
  Importer,
  MatchingHistory,
  OgdFiling,
  Product,
  Shipment,
  SimaFiling,
  TransactionalEventEmitterModule
} from "nest-modules";
import { DocumentModule } from "../document/document.module";
import { LocationModule } from "../location/location.module";
import { TradePartnerModule } from "../trade-partner/trade-partner.module";
import { ShipmentEnricher } from "./enrichers/shipment.enricher";
import { CommercialInvoiceListener } from "./listeners/commercial-invoice.listener";
import { CustomsStatusListener } from "./listeners/customs-status.listener";
import { OgdAndSimaFilingListener } from "./listeners/ogd-and-sima-filing.listener";
import { ShipmentAccountingListener } from "./listeners/shipment-accounting.listener";
import { ShipmentListener } from "./listeners/shipment.listener";
import { UpdateEntryProcessor } from "./processors/update-entry.processor";
import { AccountingNotCompletedWarningEmailSender } from "./senders/accounting-not-completed-warning-email.sender";
import { CustomsStatusCheckErrorEmailSender } from "./senders/customs-status-check-error-email.sender";
import { EntryNotAcceptedWarningEmailSender } from "./senders/entry-not-accepted-warning-email.sender";
import { LiveEntryUploadFailedEmailSender } from "./senders/live-entry-upload-failed-email.sender";
import { LiveShipmentEmailSender } from "./senders/live-shipment-email.sender";
import { RNSStatusChangeEmailSender } from "./senders/rns-status-change-email.sender";
import { ComplianceValidationService } from "./services/compliance-validation.service";
import { ContainerService } from "./services/container.service";
import { CustomStatusService } from "./services/custom-status.service";
import { EntrySubmissionService } from "./services/entry-submission.service";
import { ShipmentCommercialInvoiceService } from "./services/shipment-commercial-invoice.service";
import { ShipmentComplianceQueryService } from "./services/shipment-compliance-query.service";
import { ShipmentDeletionService } from "./services/shipment-deletion.service";
import { ShipmentService } from "./services/shipment.service";

import { ShipmentController } from "./shipment.controller";
import { SHIPMENT_QUEUES } from "./types/queue.types";

const listeners = [
  // Listeners
  CustomsStatusListener,
  ShipmentListener,
  CommercialInvoiceListener,
  OgdAndSimaFilingListener,
  ShipmentAccountingListener
];

const moduleProps: ModuleMetadata = {
  imports: [
    TypeOrmModule.forFeature([
      Shipment,
      Container,
      CommercialInvoice,
      CommercialInvoiceLine,
      CanadaTariff,
      Product,
      Importer,
      Email,
      EmailThread,
      OgdFiling,
      SimaFiling,
      MatchingHistory
    ]),
    BullModule.registerQueue(...SHIPMENT_QUEUES),
    forwardRef(() => LocationModule),
    forwardRef(() => TradePartnerModule),
    forwardRef(() => ImporterModule),
    forwardRef(() => DocumentModule),
    forwardRef(() => CommercialInvoiceModule),
    forwardRef(() => ProductModule),
    CarmModule.register(),
    forwardRef(() => EmailModule),
    OgdFilingModule,
    SimaFilingModule,
    UnitConversionModule,
    TransactionalEventEmitterModule
  ],
  providers: [
    // Services
    ShipmentService,
    ContainerService,
    ComplianceValidationService,
    EntrySubmissionService,
    ShipmentComplianceQueryService,
    ShipmentDeletionService,
    CustomStatusService,
    ShipmentCommercialInvoiceService,

    // Email senders
    LiveShipmentEmailSender,
    LiveEntryUploadFailedEmailSender,
    RNSStatusChangeEmailSender,
    EntryNotAcceptedWarningEmailSender,
    CustomsStatusCheckErrorEmailSender,
    AccountingNotCompletedWarningEmailSender,

    // Enrichers
    ShipmentEnricher
  ],
  controllers: [ShipmentController],
  exports: [
    ShipmentService,
    ContainerService,
    ComplianceValidationService,
    ShipmentComplianceQueryService,
    ShipmentEnricher,
    ShipmentDeletionService,
    CustomStatusService,
    EntrySubmissionService,
    // Email senders
    RNSStatusChangeEmailSender
  ]
};

@Module(moduleProps)
export class ShipmentModule {
  static forRoot(): DynamicModule {
    const providers: Array<Provider> = [...moduleProps.providers];
    if (process.env.FEATURE) {
      Logger.warn("Update entry processor is disabled when feature is set", "ShipmentModule");
    } else {
      Logger.log("Update entry processor is enabled when feature is not set", "ShipmentModule");
      providers.push(UpdateEntryProcessor, ...listeners);
    }

    return {
      module: ShipmentModule,
      ...moduleProps,
      providers
    };
  }

  static forFeature(): DynamicModule {
    return {
      module: ShipmentModule,
      ...moduleProps
    };
  }
}
