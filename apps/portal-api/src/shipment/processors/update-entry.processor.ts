import { EmailService } from "@/email/services/email.service";
import { EmailTemplateName } from "@/email/types/email.types";
import { AutoOgdFilingService } from "@/ogd-filing/auto-ogd-filing.service";
import { Processor, WorkerHost } from "@nestjs/bullmq";
import { BadRequestException, forwardRef, Inject, Logger, NotFoundException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import { InjectDataSource } from "@nestjs/typeorm";
import {
  CandataService,
  CommercialInvoice,
  CommercialInvoiceLine,
  CustomsStatus,
  FIND_SHIPMENT_RELATIONS,
  Organization,
  Product,
  Shipment,
  UserPermission
} from "nest-modules";
import { DataSource, QueryRunner } from "typeorm";
import { ComplianceValidationService } from "../services/compliance-validation.service";
import { EntrySubmissionService } from "../services/entry-submission.service";
import { DEFAULT_WORKER_OPTIONS, ShipmentQueueName, UpdateEntryJob } from "../types/queue.types";
import { AutoSimaFilingService } from "@/sima-filing/auto-sima-filing.service";

@Processor(
  {
    name: ShipmentQueueName.UPDATE_ENTRY
  },
  DEFAULT_WORKER_OPTIONS
)
export class UpdateEntryProcessor extends WorkerHost {
  constructor(
    @Inject(forwardRef(() => ComplianceValidationService))
    private readonly complianceValidationService: ComplianceValidationService,
    @Inject(forwardRef(() => EntrySubmissionService))
    private readonly entrySubmissionService: EntrySubmissionService,
    @Inject(forwardRef(() => AutoOgdFilingService))
    private readonly autoOgdFilingService: AutoOgdFilingService,
    @Inject(forwardRef(() => AutoSimaFilingService))
    private readonly autoSimaFilingService: AutoSimaFilingService,
    @Inject(forwardRef(() => EmailService))
    private readonly emailService: EmailService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly moduleRef: ModuleRef,
    @Inject(ConfigService)
    private readonly configService: ConfigService
  ) {
    super();
  }
  private readonly logger = new Logger(UpdateEntryProcessor.name);

  private get BACKOFFICE_EMAIL(): string {
    return this.configService.get<string>("BACKOFFICE_EMAIL_ADDRESS");
  }

  private get SYSTEM_FROM_EMAIL(): string {
    return this.configService.get<string>("SYSTEM_FROM_EMAIL");
  }

  private get PORTAL_FRONTEND_URL(): string {
    return this.configService.get<string>("PORTAL_FRONTEND_URL");
  }

  private async getScopedEntrySubmissionService(organization: Organization) {
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(
      {
        user: {
          permission: UserPermission.BACKOFFICE_ADMIN,
          organization
        }
      },
      contextId
    );
    const entrySubmissionService = await this.moduleRef.resolve(EntrySubmissionService, contextId, {
      strict: false
    });
    await new Promise((resolve) => process.nextTick(resolve));
    return entrySubmissionService;
  }

  private async sendErrorEmail(shipmentId: number, error: Error) {
    const shipment = await this.dataSource.manager.findOne(Shipment, {
      where: { id: shipmentId },
      relations: FIND_SHIPMENT_RELATIONS
    });

    // Set shipment to require reupload
    if (shipment && !shipment.requiresReupload)
      await this.dataSource.manager.update(Shipment, { id: shipmentId }, { requiresReupload: true });

    // Send email to backoffice
    const { subject, body } = this.emailService.renderEmailTemplateWithSubject(
      EmailTemplateName.UPDATE_ENTRY_ERROR_EMAIL,
      {
        shipmentId: shipment?.id,
        hblNumber: shipment?.hblNumber,
        cargoControlNumber: shipment?.cargoControlNumber,
        containerNumbers: (shipment?.containers || []).map((c) => c.containerNumber).join(",") || null,
        transactionNumber: shipment?.transactionNumber,
        fileNumber: shipment?.customsFileNumber,
        claroUrl: `${this.PORTAL_FRONTEND_URL}/shipment/${shipment?.id}`,
        errorMessage: error?.message || "Unknown error",
        stackTrace: error?.stack
      }
    );
    const sentEmail = await this.emailService.sendEmail(
      {
        from: this.SYSTEM_FROM_EMAIL,
        to: [this.BACKOFFICE_EMAIL],
        subject,
        text: body
      },
      undefined,
      shipment?.organization
    );
    this.logger.log(`Update entry error email: ${sentEmail?.id}`);
  }

  private async getShipmentProductIds(shipmentId: number, queryRunner?: QueryRunner) {
    const productIds = await (queryRunner?.manager ?? this.dataSource.manager)
      .createQueryBuilder(queryRunner)
      .select("p.id", "productId")
      .from(Product, "p")
      .leftJoin(CommercialInvoiceLine, "cil", `cil."productId" = p.id`)
      .leftJoin(CommercialInvoice, "ci", `ci.id = cil."commercialInvoiceId"`)
      .leftJoin(Shipment, "s", `s.id = ci."shipmentId"`)
      .where("s.id = :shipmentId", { shipmentId })
      .distinctOn(["p.id"])
      .getRawMany<{ productId: number }>();
    return productIds.map(({ productId }) => productId);
  }

  async process(job: UpdateEntryJob) {
    this.logger.log(`Processing update entry job. ID: ${job.id}, Data: ${JSON.stringify(job.data)}`);
    const { shipmentId } = job.data;

    // Check if shipment has ongoing auto OGD and SIMA filing jobs
    const shipmentProductIds = await this.getShipmentProductIds(shipmentId);
    this.logger.debug(`Shipment product IDs: ${JSON.stringify(shipmentProductIds)}`);
    if (shipmentProductIds.length > 0) {
      this.logger.log(
        `Shipment ${shipmentId} has ${shipmentProductIds.length} products, checking ongoing auto filing jobs...`
      );
      const ongoingAutoOgdFilingJobs =
        await this.autoOgdFilingService.getOngoingAutoOgdFilingJobs(shipmentProductIds);
      const ongoingAutoSimaFilingJobs =
        await this.autoSimaFilingService.getOngoingAutoSimaFilingJobs(shipmentProductIds);
      this.logger.debug(`Ongoing OGD filing jobs: ${JSON.stringify(ongoingAutoOgdFilingJobs)}`);
      this.logger.debug(`Ongoing SIMA filing jobs: ${JSON.stringify(ongoingAutoSimaFilingJobs)}`);

      // If there are ongoing auto OGD and SIMA filing jobs, stop the current update entry job and let it retry in later time
      if (ongoingAutoOgdFilingJobs.length > 0 || ongoingAutoSimaFilingJobs.length > 0) {
        this.logger.warn(
          `Shipment ${shipmentId} has ${ongoingAutoOgdFilingJobs.length} and ${ongoingAutoSimaFilingJobs.length} ongoing auto OGD and SIMA filing jobs respectively, stopping...`
        );
        throw new Error(`Shipment ${shipmentId} has ongoing auto OGD and SIMA filing jobs`);
      } else
        this.logger.log(`Shipment ${shipmentId} has no ongoing auto OGD and SIMA filing jobs, continuing...`);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const shipmentRepository = queryRunner.manager.getRepository(Shipment);

      // Get shipment
      const shipment = await shipmentRepository.findOne({
        where: { id: shipmentId },
        relations: FIND_SHIPMENT_RELATIONS
      });
      this.logger.debug(`Shipment: ${shipment?.id}`);
      if (!shipment) throw new NotFoundException(`Shipment ${shipmentId} not found`);
      if (!this.complianceValidationService.isShipmentEntryUploaded(shipment))
        throw new BadRequestException("Shipment is not uploaded to Candata yet");
      if (!this.complianceValidationService.canShipmentUpdateEntry(shipment))
        throw new BadRequestException(
          "Shipment entry cannot be updated, potentially because shipment accounting is completed or transaction number is missing"
        );

      // Update shipment entry
      const updatedShipment = await (
        await this.getScopedEntrySubmissionService(shipment.organization)
      ).updateShipmentEntry(shipment, queryRunner);
      this.logger.debug(`Updated shipment: ${updatedShipment?.id}`);

      // Send email to backoffice asking for manual entry submission
      const { subject, body } = this.emailService.renderEmailTemplateWithSubject(
        EmailTemplateName.MANUAL_ENTRY_SUBMISSION_REQUEST_EMAIL,
        {
          hblNumber: updatedShipment.hblNumber,
          cargoControlNumber: updatedShipment.cargoControlNumber,
          containerNumbers:
            (updatedShipment.containers || []).map((c) => c.containerNumber).join(",") || null,
          transactionNumber: shipment.transactionNumber,
          organizationName: shipment.organization?.name,
          claroUrl: `${this.PORTAL_FRONTEND_URL}/shipment/${updatedShipment.id}`
        }
      );
      this.logger.debug(`Email subject: ${subject}`);
      this.logger.debug(`Email body: ${body}`);

      const sentEmail = await this.emailService.sendEmail(
        {
          from: this.SYSTEM_FROM_EMAIL,
          to: [this.BACKOFFICE_EMAIL],
          subject,
          text: body
        },
        queryRunner,
        shipment?.organization
      );
      this.logger.log(`Manual submission email: ${sentEmail?.id}`);

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      await this.sendErrorEmail(shipmentId, error);
    } finally {
      await queryRunner.release();
    }
  }
}
