import {
  OgdFilingCreatedEventDto,
  OgdFilingDeletedEventDto,
  OgdFilingEditedEventDto
} from "@/ogd-filing/dto";
import { OgdFilingEvent } from "@/ogd-filing/types";
import { SimaFilingDeletedEventDto } from "@/sima-filing/dto";
import { SimaFilingCreatedEventDto } from "@/sima-filing/dto";
import { SimaFilingEditedEventDto } from "@/sima-filing/dto";
import { SimaFilingEvent } from "@/sima-filing/types";
import { InjectQueue } from "@nestjs/bullmq";
import { forwardRef, Inject, Injectable, Logger, Type } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { ShipmentQueueName, UpdateEntryQueue } from "../types/queue.types";
import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import { ArrayContains, DataSource, IsNull } from "typeorm";
import {
  CommercialInvoice,
  CommercialInvoiceLine,
  CustomsStatus,
  FIND_OGD_FILING_RELATIONS,
  MatchingHistory,
  MatchingRule,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleService,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus,
  OgdFiling,
  Product,
  RuleQueryService,
  Shipment,
  SimaFiling,
  UserPermission
} from "nest-modules";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";

@Injectable()
export class OgdAndSimaFilingListener {
  constructor(
    @InjectQueue(ShipmentQueueName.UPDATE_ENTRY)
    private readonly updateEntryQueue: UpdateEntryQueue,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly moduleRef: ModuleRef
  ) {}
  private readonly logger = new Logger(OgdAndSimaFilingListener.name);

  private async getScopedService(organizationId: number, service: string | symbol | Function | Type<any>) {
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(
      {
        user: {
          permission: UserPermission.ORGANIZATION_ADMIN,
          organization: {
            id: organizationId
          }
        }
      },
      contextId
    );
    const scopedService = await this.moduleRef.resolve(service, contextId, {
      strict: false
    });
    await new Promise((resolve) => process.nextTick(resolve));
    return scopedService;
  }

  private async getScopedRuleQueryService(organizationId: number): Promise<RuleQueryService> {
    return await this.getScopedService(organizationId, RuleQueryService);
  }

  private async getScopedMatchingRuleService(organizationId: number): Promise<MatchingRuleService> {
    return await this.getScopedService(organizationId, MatchingRuleService);
  }

  /**
   * Get matching rules which the source record is the given filing.
   * @param filing - OGD filing or SIMA filing
   * @returns Matching rules
   */
  private async getMatchingRulesForFiling(filing: OgdFiling | SimaFiling) {
    const sourceTable =
      filing instanceof OgdFiling
        ? MatchingRuleSourceDatabaseTable.OGD_FILING
        : MatchingRuleSourceDatabaseTable.SIMA_FILING;
    this.logger.log(
      `Getting matching rules for ${sourceTable} ${filing.id}, organization: ${filing?.organization?.id}`
    );

    const matchingRuleService = await this.getScopedMatchingRuleService(filing?.organization?.id);
    const matchingRuleList: Array<MatchingRule> = [];
    const RECORDS_PER_PAGE = 10000;
    let pageIdx = 0,
      isCompleted = false;
    do {
      const { matchingRules: pageMatchingRules, total } = await matchingRuleService.getMatchingRules({
        status: MatchingRuleStatus.ACTIVE,
        sourceTable,
        sourceId: filing.id,
        destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT,
        skip: pageIdx * RECORDS_PER_PAGE,
        limit: RECORDS_PER_PAGE
      });
      matchingRuleList.push(...pageMatchingRules);
      this.logger.log(
        `-- Page ${pageIdx}, Got ${pageMatchingRules.length} matching rules, total: ${total}, current matching rules count: ${matchingRuleList.length}`
      );
      pageIdx++;
      isCompleted = pageMatchingRules.length <= 0 || matchingRuleList.length >= total;
    } while (!isCompleted);

    return matchingRuleList;
  }

  /**
   * Get valid matching histories for the given filing.
   * @param filing - OGD filing or SIMA filing
   * @returns Valid matching histories
   */
  private async getValidMatchingHistories(filing: OgdFiling | SimaFiling) {
    const sourceTable =
      filing instanceof OgdFiling
        ? MatchingRuleSourceDatabaseTable.OGD_FILING
        : MatchingRuleSourceDatabaseTable.SIMA_FILING;

    const matchingHistories = await this.dataSource.manager.find(MatchingHistory, {
      where: {
        sourceTable,
        sourceIds: ArrayContains([filing.id]),
        destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT,
        invalidDate: IsNull()
      },
      order: {
        queryDate: "DESC"
      }
    });

    return matchingHistories;
  }

  /**
   * Get IDs of shipments uploaded to Candata using product IDs.
   * @param productIds - Product IDs
   * @returns Shipment IDs
   */
  private async getUploadedShipmentIdsFromProductIds(productIds: Array<number>) {
    const BATCH_SIZE = 30000;
    const shipmentIds: Array<number> = [];
    for (let batchIdx = 0; batchIdx < Math.ceil(productIds.length / BATCH_SIZE); batchIdx++) {
      const batchProductIds = productIds.slice(BATCH_SIZE * batchIdx, BATCH_SIZE * (batchIdx + 1));
      const result = await this.dataSource
        .createQueryBuilder()
        .select(`s.id`, "shipmentId")
        .from(Shipment, "s")
        .leftJoin(CommercialInvoice, "ci", `ci."shipmentId" = s.id`)
        .leftJoin(CommercialInvoiceLine, "cil", `cil."commercialInvoiceId" = ci.id`)
        .where(`cil."productId" IN (:...productIds)`, { productIds: batchProductIds })
        .andWhere(`s."customsStatus" NOT IN (:...customsStatuses)`, {
          customsStatuses: [
            CustomsStatus.PENDING_COMMERCIAL_INVOICE,
            CustomsStatus.PENDING_CONFIRMATION,
            CustomsStatus.PENDING_ARRIVAL
          ]
        })
        .distinctOn([`s.id`])
        .getRawMany<{ shipmentId: number }>();
      result.forEach(({ shipmentId }) => {
        if (!shipmentIds.includes(shipmentId)) shipmentIds.push(shipmentId);
      });
    }

    return shipmentIds;
  }

  /**
   * Add shipments that are both uploaded to Candata and related to the given OGD/SIMA filing to update entry queue.
   * @param filingId - OGD/SIMA filing ID
   * @param sourceType - Record type of the filing, either OGD or SIMA.
   */
  private async addRelatedShipmentToUpdateEntryQueue(filingId: number, sourceType: "OGD" | "SIMA") {
    try {
      // Get OGD/SIMA filing
      const filing = await this.dataSource.manager.findOne<OgdFiling | SimaFiling>(
        sourceType === "OGD" ? OgdFiling : SimaFiling,
        {
          where: { id: filingId },
          relations: FIND_OGD_FILING_RELATIONS
        }
      );
      this.logger.log(`${sourceType} Filing: ${filing?.id}`);
      if (!filing) {
        this.logger.warn(`${sourceType} Filing not found for ID ${filingId}, skipping...`);
        return;
      }

      // Get valid matching histories for the OGD/SIMA filing
      const matchingHistories = await this.getValidMatchingHistories(filing);
      this.logger.log(
        `Got ${matchingHistories.length} valid matching histories for ${sourceType} Filing ${filing.id}`
      );

      // Extract product IDs related to the OGD/SIMA filing from the histories
      const productIds = matchingHistories.reduce((list, mh) => {
        if (!list.includes(mh.destinationId)) list.push(mh.destinationId);
        return list;
      }, [] as Array<number>);
      this.logger.log(
        `Product IDs related to ${sourceType} Filing ${filing.id} from histories: ${productIds.join(", ")}`
      );

      // Get matching rules for OGD/SIMA filing
      const matchingRules = await this.getMatchingRulesForFiling(filing);
      this.logger.log(`Got ${matchingRules.length} matching rules for ${sourceType} Filing ${filing.id}`);
      if (matchingRules.length <= 0) {
        this.logger.warn(`No matching rules found for ${sourceType} filing ${filing.id}, skipping...`);
        return;
      }

      // Only run reverse query for rules that are not present in the valid matching histories
      const ruleQueryService = await this.getScopedRuleQueryService(filing.organization?.id);
      for (const rule of matchingRules) {
        const relatedHistory = matchingHistories.find(
          (history) =>
            history.sourceTable === rule.sourceTable &&
            history.sourceIds.includes(filing.id) &&
            history.destinationTable === MatchingRuleDestinationDatabaseTable.PRODUCT
        );
        this.logger.log(`Matching Rule: ${rule.id}, Related History: ${relatedHistory?.id}`);
        if (relatedHistory) {
          this.logger.log(`Related history found, skip reverse query for rule ${rule.id}...`);
          continue;
        }

        // Run reverse query for the rule
        const { destinationRecords: ruleProducts } = await ruleQueryService.reverseQueryMatchingResults({
          destinationTable: rule.destinationTable,
          conditions: rule.conditions
        });
        this.logger.log(`Got ${ruleProducts.length} products from reverse querying rule ${rule.id}`);

        for (const product of ruleProducts) if (!productIds.includes(product.id)) productIds.push(product.id);
      }

      this.logger.log(`Product IDs related to ${sourceType} Filing ${filing.id}: ${productIds.join(", ")}`);
      if (productIds.length <= 0) {
        this.logger.warn(`No product IDs related to ${sourceType} filing ${filing.id}, skipping...`);
        return;
      }

      // Get uploaded shipment IDs
      const shipmentIds = await this.getUploadedShipmentIdsFromProductIds(productIds);
      this.logger.log(`Shipments to update: ${shipmentIds.join(", ")}`);
      if (shipmentIds.length <= 0) {
        this.logger.warn(`No uploaded shipments found for ${sourceType} filing ${filing.id}, skipping...`);
        return;
      }

      // Add shipments to update entry queue
      await this.updateEntryQueue.addBulk(
        shipmentIds.map((shipmentId) => ({
          name: shipmentId.toString(),
          data: { shipmentId },
          opts: {
            deduplication: {
              id: shipmentId.toString(),
              ttl: 5000 // 5 seconds
            }
          }
        }))
      );
    } catch (error) {
      this.logger.error(
        `Error while adding filing related shipments to update entry queue: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
    }
  }

  @OnEvent(OgdFilingEvent.OGD_FILING_CREATED)
  @OnEvent(OgdFilingEvent.OGD_FILING_EDITED)
  @OnEvent(OgdFilingEvent.OGD_FILING_DELETED)
  async onOgdFilingUpdated(
    event: OgdFilingCreatedEventDto | OgdFilingEditedEventDto | OgdFilingDeletedEventDto
  ) {
    this.logger.log(`On OGD Filing Updated: ${JSON.stringify(event)}`);
    const { filingId } = event;
    await this.addRelatedShipmentToUpdateEntryQueue(filingId, "OGD");
  }

  @OnEvent(SimaFilingEvent.SIMA_FILING_CREATED)
  @OnEvent(SimaFilingEvent.SIMA_FILING_EDITED)
  @OnEvent(SimaFilingEvent.SIMA_FILING_DELETED)
  async onSimaFilingUpdated(
    event: SimaFilingCreatedEventDto | SimaFilingEditedEventDto | SimaFilingDeletedEventDto
  ) {
    this.logger.log(`On SIMA Filing Updated: ${JSON.stringify(event)}`);
    const { filingId } = event;
    await this.addRelatedShipmentToUpdateEntryQueue(filingId, "SIMA");
  }
}
