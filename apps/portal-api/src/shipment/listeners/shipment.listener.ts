import { forwardRef, Inject, Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { OnEvent } from "@nestjs/event-emitter";
import { ShipmentEvent } from "../types/event.types";
import {
  ShipmentAttemptEditSubmissionMandatoryFields,
  ShipmentDeleteCandataEntryEventDto,
  ShipmentDeletedEventDto
} from "../dto/event.dto";
import { QueryRunner, Repository } from "typeorm";
import { InjectRepository } from "@nestjs/typeorm";
import {
  CandataService,
  CandataShipmentDto,
  CustomsStatus,
  FIND_SHIPMENT_RELATIONS,
  Importer,
  Shipment,
  ShipmentColumn
} from "nest-modules";
import { EmailService } from "@/email/services/email.service";
import { EmailTemplateName } from "@/email/types/email.types";
import { ComplianceValidationService } from "../services/compliance-validation.service";
import { NON_EDITABLE_FIELDS_POST_SUBMISSION } from "../types/shipment.types";
import { ImporterService } from "@/importer/importer.service";

@Injectable()
export class ShipmentListener {
  constructor(
    @InjectRepository(Shipment)
    private readonly shipmentRepository: Repository<Shipment>,
    @InjectRepository(Importer)
    private readonly importerRepository: Repository<Importer>,
    @Inject(ComplianceValidationService)
    private readonly complianceValidationService: ComplianceValidationService,
    @Inject(forwardRef(() => EmailService))
    private readonly emailService: EmailService,
    @Inject(CandataService)
    private readonly candataService: CandataService,
    @Inject(ConfigService)
    private readonly configService: ConfigService
  ) {}
  private readonly logger = new Logger(ShipmentListener.name);

  private get BACKOFFICE_EMAIL(): string {
    return this.configService.get<string>("BACKOFFICE_EMAIL_ADDRESS");
  }

  private get PORTAL_FRONTEND_URL(): string {
    return this.configService.get<string>("PORTAL_FRONTEND_URL");
  }

  private toCandataShipmentKey(shipmentKey: ShipmentColumn) {
    switch (shipmentKey) {
      case ShipmentColumn.cargoControlNumber:
        return "cargoControlNumbers";
      case ShipmentColumn.portCode:
        return "port";
      case ShipmentColumn.subLocation:
        return "sublocationCode";
      default:
        this.logger.warn(`Unsupported shipment key: ${shipmentKey}`);
        return "";
    }
  }

  /**
   * Event listener for when a shipment attempt edit submission mandatory fields event is received. The following will be done in this listener:
   * - Send email to backoffice notifying them that the shipment's mandatory field has been updated
   * - Update Candata entry if the shipment is live
   * @param event - Shipment attempt edit submission mandatory fields event
   */
  @OnEvent(ShipmentEvent.SHIPMENT_ATTEMPT_EDIT_SUBMISSION_MANDATORY_FIELDS)
  async onShipmentAttemptEditSubmissionMandatoryFields(event: ShipmentAttemptEditSubmissionMandatoryFields) {
    const { shipmentId, user, changedFields } = event;
    this.logger.log(
      `Received shipment attempt edit after submission event for shipment ${shipmentId}. Changed fields: ${JSON.stringify(changedFields)}`
    );

    // Get shipment and validate if it is live or submitted
    const shipment = await this.shipmentRepository.findOne({
      where: { id: shipmentId },
      relations: FIND_SHIPMENT_RELATIONS
    });
    this.logger.log(
      `Shipment: ${shipment?.id}. Customs status: ${shipment?.customsStatus}. Transaction number: ${shipment?.transactionNumber}`
    );
    if (!shipment) {
      this.logger.warn(`Shipment ${shipmentId} not found, skipping...`);
      return;
    }
    if (!this.complianceValidationService.isShipmentEntryUploaded(shipment)) {
      this.logger.warn(`Shipment ${shipment.id} is not uploaded to Candata, skipping...`);
      return;
    }

    // Update Candata entry if the shipment is live
    if (shipment.customsStatus === CustomsStatus.LIVE) {
      try {
        if (!shipment.transactionNumber && !shipment.customsFileNumber)
          throw new Error("Shipment has no transaction number and file number");
        const candataShipmentIdentifier = this.complianceValidationService.getCandataShipmentIdentifier(
          shipment.transactionNumber,
          shipment.customsFileNumber
        );

        const updateCandataShipmentDto: Omit<Partial<CandataShipmentDto>, "b3" | "declaration"> = {
          billOfLading: shipment.hblNumber
        };
        NON_EDITABLE_FIELDS_POST_SUBMISSION.forEach((field) => {
          const changedField = changedFields.find((cf) => cf.field === field);
          if (changedField)
            updateCandataShipmentDto[this.toCandataShipmentKey(field)] = changedField.newValue ?? "";
        });

        const candataShipment = await this.candataService.updateCandataShipment(
          candataShipmentIdentifier,
          updateCandataShipmentDto,
          shipment.organization?.customsBroker
        );
        this.logger.log(`Candata shipment updated: ${candataShipment?.transactionNumber}`);
        this.logger.debug(`Updated Candata shipment: ${JSON.stringify(candataShipment)}`);
      } catch (error) {
        this.logger.error(
          `Error updating Candata shipment: ${error instanceof Error ? error.message : String(error)}`
        );
      }
    }

    // Send email to backoffice notifying them that the shipment's mandatory field has been updated
    const { subject, body } = this.emailService.renderEmailTemplateWithSubject(
      EmailTemplateName.LIVE_SHIPMENT_MANDATORY_FIELDS_UPDATE_EMAIL,
      {
        customsStatus: shipment.customsStatus,
        hblNumber: shipment.hblNumber,
        cargoControlNumber: shipment.cargoControlNumber,
        // containerNumber: shipment.containerNumber,
        containerNumbers: (shipment.containers || []).map((c) => c.containerNumber).join(",") || null,
        transactionNumber: shipment.transactionNumber,
        organizationName: shipment.organization?.name,
        importerName: shipment.importer?.companyName,
        claroUrl: `${this.PORTAL_FRONTEND_URL}/shipment/${shipment.id}`,
        changes: changedFields
      }
    );
    this.logger.log(`Sending email to backoffice with subject: ${subject}`);
    this.logger.debug(`Email body: ${body}`);

    try {
      const sentEmail = await this.emailService.sendEmail(
        {
          from: this.BACKOFFICE_EMAIL,
          to: [this.BACKOFFICE_EMAIL],
          subject,
          text: body
        },
        null,
        shipment.organization
      );
      this.logger.log(`Email sent to backoffice: ${sentEmail.id}`);
    } catch (error) {
      this.logger.error(
        `Error sending email to backoffice: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  @OnEvent(ShipmentEvent.SHIPMENT_DELETE_CANDATA_ENTRY)
  async onShipmentDeleteCandataEntry(event: ShipmentDeleteCandataEntryEventDto) {
    const { shipmentId, transactionNumberOrFileNumber, importerId, user } = event;
    this.logger.log(
      `Received shipment delete candata entry event for shipment ${shipmentId}. Transaction number or file number: ${transactionNumberOrFileNumber}. Importer ID: ${importerId}. User: ${user?.id}`
    );

    try {
      const importer = await this.importerRepository.findOne({
        where: {
          id: importerId
        },
        select: {
          id: true,
          candataCustomerNumber: true,
          organization: {
            id: true,
            customsBroker: true
          }
        },
        relations: {
          organization: true
        }
      });
      this.logger.log(`Importer: ${JSON.stringify(importer)}`);
      if (!importer) throw new Error(`Importer ${importerId} not found`);
      if (!importer.candataCustomerNumber)
        throw new Error(`Importer ${importerId} has no Candata customer number`);

      const deletedCandataShipment = await this.candataService.deleteCandataShipment(
        importer.candataCustomerNumber,
        transactionNumberOrFileNumber,
        importer.organization?.customsBroker
      );
      this.logger.log(
        `Deleted Candata shipment transaction number: ${deletedCandataShipment?.transactionNumber}, File number: ${deletedCandataShipment?.fileNumber}`
      );
    } catch (error) {
      this.logger.error(
        `Error deleting Candata entry for shipment ${shipmentId}: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
    }
  }
}
