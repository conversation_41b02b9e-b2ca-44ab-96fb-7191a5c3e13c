import { forwardRef, Inject, Injectable, Logger } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { ShipmentEvent } from "../types/event.types";
import { InjectDataSource } from "@nestjs/typeorm";
import { DataSource, In } from "typeorm";
import { ModuleRef } from "@nestjs/core";
import {
  CandataErrorHandling,
  CandataService,
  CandataShipmentDto,
  CustomsStatus,
  FIND_SHIPMENT_RELATIONS,
  OrganizationCustomsBroker,
  Shipment
} from "nest-modules";
import moment from "moment-timezone";
import { EmailService } from "@/email/services/email.service";
import { ConfigService } from "@nestjs/config";
import { EmailTemplateName } from "@/email/types/email.types";
import { AccountingNotCompletedWarningEmailSender } from "../senders/accounting-not-completed-warning-email.sender";
import { ComplianceValidationService } from "../services/compliance-validation.service";

@Injectable()
export class ShipmentAccountingListener {
  constructor(
    @Inject(AccountingNotCompletedWarningEmailSender)
    private readonly accountingNotCompletedWarningEmailSender: AccountingNotCompletedWarningEmailSender,
    @Inject(ComplianceValidationService)
    private readonly complianceValidationService: ComplianceValidationService,
    @Inject(CandataService)
    private readonly candataService: CandataService,
    @Inject(forwardRef(() => EmailService))
    private readonly emailService: EmailService,
    @Inject(ConfigService)
    private readonly configService: ConfigService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly moduleRef: ModuleRef
  ) {}
  private readonly logger = new Logger(ShipmentAccountingListener.name);

  /**
   * Try getting Candata shipment for a transaction number.
   * If error occurs, log error and return null.
   * @param transactionNumber - Transaction number of the shipment
   * @returns Candata shipment or null if error occurs
   */
  private async tryGettingCandataShipment(
    transactionNumber: string,
    customsBroker: OrganizationCustomsBroker
  ) {
    // TODO: Add retry logic
    this.logger.log(`Try getting Candata shipment for transaction number: ${transactionNumber}`);
    try {
      return await this.candataService.getCandataShipment(transactionNumber, customsBroker);
    } catch (error) {
      this.logger.error(
        `Error getting Candata shipment for transaction number: ${transactionNumber}: ${error instanceof Error ? error.message : String(error)}`
      );
      return null;
    }
  }

  private async checkShipmentAccountingStatus(
    transactionNumbersOrFileNumbers: Array<string>,
    customsBroker: OrganizationCustomsBroker
  ): Promise<Array<{ transactionNumber: string; fileNumber: string; isAccountingCompleted: boolean }>> {
    this.logger.log(
      `Checking accounting status for ${transactionNumbersOrFileNumbers.length} transaction numbers or file numbers...`
    );

    const BATCH_SIZE = 100;
    const batchCount = Math.ceil(transactionNumbersOrFileNumbers.length / BATCH_SIZE);
    const resultList: Array<{
      transactionNumber: string;
      fileNumber: string;
      isAccountingCompleted: boolean;
    }> = [];
    for (let i = 0; i < batchCount; i++) {
      const batchTransactionNumbersOrFileNumbers = transactionNumbersOrFileNumbers.slice(
        i * BATCH_SIZE,
        (i + 1) * BATCH_SIZE
      );
      this.logger.log(
        `- Checking batch ${i + 1} of ${batchCount}. Transaction number or file number count: ${batchTransactionNumbersOrFileNumbers.length}`
      );

      const batchCandataShipments = await this.candataService.getMultipleCandataShipments(
        batchTransactionNumbersOrFileNumbers,
        CandataErrorHandling.IGNORE_ERRORS,
        customsBroker
      );

      resultList.push(
        ...batchCandataShipments
          .filter((candataShipment) => candataShipment !== null)
          .map((candataShipment) => ({
            transactionNumber: candataShipment.transactionNumber,
            fileNumber: candataShipment.fileNumber,
            isAccountingCompleted:
              typeof candataShipment?.declaration?.responseIssueDateTime === "string" &&
              candataShipment?.declaration?.responseIssueDateTime?.length > 0
          }))
      );
    }

    return resultList;
  }

  @OnEvent(ShipmentEvent.ACCOUNTING_CHECK)
  async onAccountingCheck() {
    this.logger.log(`Received accounting check event. Checking released shipments...`);

    const toBeSentWarningShipments: Array<Shipment> = [];

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const shipmentRepository = queryRunner.manager.getRepository(Shipment);

      // Get all released shipments
      const releasedShipments = await shipmentRepository.find({
        where: {
          customsStatus: CustomsStatus.RELEASED
        },
        relations: FIND_SHIPMENT_RELATIONS
      });
      this.logger.log(`Found ${releasedShipments.length} released shipments to check for accounting...`);

      // Split shipments by customs broker
      const candataShipmentIdentifiersByCustomsBroker = new Map<OrganizationCustomsBroker, Array<string>>();
      for (const shipment of releasedShipments) {
        if (!shipment.organization?.customsBroker) {
          this.logger.error(`Shipment ${shipment.id} has no customs broker, skipping...`);
          continue;
        }

        let candataShipmentIdentifier: string | null = null;
        try {
          candataShipmentIdentifier = this.complianceValidationService.getCandataShipmentIdentifier(
            shipment.transactionNumber,
            shipment.customsFileNumber
          );
        } catch (candataIdError) {
          this.logger.error(
            `Error getting Candata shipment identifier for shipment ${shipment.id}: ${candataIdError instanceof Error ? candataIdError.message : String(candataIdError)}`
          );
          candataShipmentIdentifier = null;
        }
        if (!candataShipmentIdentifier) {
          this.logger.error(`Shipment ${shipment.id} has no valid Candata shipment identifier, skipping...`);
          continue;
        }

        const brokerCandataShipmentIdentifiers =
          candataShipmentIdentifiersByCustomsBroker.get(shipment.organization?.customsBroker) || [];
        if (!brokerCandataShipmentIdentifiers.includes(candataShipmentIdentifier))
          brokerCandataShipmentIdentifiers.push(candataShipmentIdentifier);
        candataShipmentIdentifiersByCustomsBroker.set(
          shipment.organization?.customsBroker,
          brokerCandataShipmentIdentifiers
        );
      }
      this.logger.log(
        `Found ${candataShipmentIdentifiersByCustomsBroker.size} customs brokers with released shipments...`
      );

      // Get shipment entries from Candata
      const accountingStatusListByCustomsBroker = new Map<
        OrganizationCustomsBroker,
        Array<{ transactionNumber: string; fileNumber: string; isAccountingCompleted: boolean }>
      >();
      for (const [
        customsBroker,
        candataShipmentIdentifiers
      ] of candataShipmentIdentifiersByCustomsBroker.entries()) {
        const accountingStatusList = await this.checkShipmentAccountingStatus(
          candataShipmentIdentifiers,
          customsBroker
        );
        accountingStatusListByCustomsBroker.set(customsBroker, accountingStatusList);
        this.logger.log(
          `Got ${accountingStatusList.length} accounting statuses from Candata for customs broker ${customsBroker}...`
        );
      }

      // Filter out shipments that have accounting completed and not completed respectively
      const currentDate = moment.tz("America/Toronto");
      for (const shipment of releasedShipments) {
        if (!shipment.organization?.customsBroker) {
          this.logger.error(`Shipment ${shipment.id} has no customs broker, skipping...`);
          continue;
        }
        const accountingStatusList = accountingStatusListByCustomsBroker.get(
          shipment.organization?.customsBroker
        );
        if (!accountingStatusList) {
          this.logger.error(
            `No accounting status list found for customs broker ${shipment.organization?.customsBroker}, skipping...`
          );
          continue;
        }
        const accountingStatus = accountingStatusList.find(
          (accountingStatus) =>
            accountingStatus.transactionNumber === shipment.transactionNumber ||
            accountingStatus.fileNumber === shipment.customsFileNumber
        );
        this.logger.log(
          `Shipment ${shipment.id} transaction number: ${shipment.transactionNumber}, file number: ${shipment.customsFileNumber}, accounting status: ${accountingStatus?.isAccountingCompleted}`
        );

        // Skip shipment if no accounting status found in Candata.
        // Likely due to no transaction number
        if (!accountingStatus) {
          this.logger.log(`Shipment ${shipment.id} has no accounting status found in Candata, skipping...`);
          continue;
        }

        if (accountingStatus.isAccountingCompleted) {
          // Update shipment customs status if accounting completed
          this.logger.log(`Shipment ${shipment.id} has accounting completed, updating customs status...`);
          const updateResult = await shipmentRepository.update(
            { id: shipment.id },
            { customsStatus: CustomsStatus.ACCOUNTING_COMPLETED }
          );
          this.logger.log(
            `Shipment ${shipment.id} accounting status update result: ${updateResult?.affected} affected rows`
          );
        } else if (shipment.releaseDate instanceof Date) {
          // If shipment is not accounting completed and has a release date, check if it has been released for more than 3 days
          const releaseDate = moment.tz(shipment.releaseDate, "America/Toronto");
          this.logger.log(
            `Shipment ${shipment.id} has NOT accounting completed. Release date: ${releaseDate.format("YYYY-MM-DD")}, Current date: ${currentDate.format("YYYY-MM-DD")}`
          );
          if (currentDate.isSameOrAfter(releaseDate.add(3, "days"), "days")) {
            this.logger.log(
              `-> Shipment ${shipment.id} has been released for more than 3 days but still has no accounting completed. Sending warning email...`
            );
            toBeSentWarningShipments.push(shipment);
          }
        } else {
          // If shipment is not accounting completed and no release date found, skip sending warning email
          this.logger.log(
            `Shipment ${shipment.id} is NOT accounting completed and no release date found, skip sending warning email...`
          );
        }
      }

      await queryRunner.commitTransaction();

      // Send warning emails
      // TODO: Send warning emails in a queue
      if (toBeSentWarningShipments.length > 0)
        await this.accountingNotCompletedWarningEmailSender.sendAccountingNotCompletedWarningEmails(
          toBeSentWarningShipments.map((shipment) => shipment.id)
        );
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      const errorMessage = error instanceof Error ? error.message : String(error);
      const stackTrace = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error while checking accounting status: ${errorMessage}`, stackTrace);

      // TODO: Send error email to backoffice
    } finally {
      await queryRunner.release();
    }
  }
}
