import { InjectQueue } from "@nestjs/bullmq";
import { forwardRef, Inject, Injectable, Logger } from "@nestjs/common";
import { ShipmentQueueName, UpdateEntryQueue } from "../types/queue.types";
import { InjectDataSource } from "@nestjs/typeorm";
import { DataSource } from "typeorm";
import { OnEvent } from "@nestjs/event-emitter";
import { CommercialInvoiceEvent } from "@/commercial-invoice/types/event.types";
import {
  CommercialInvoiceEditedEventDto,
  CommercialInvoiceDeletedEventDto,
  CommercialInvoiceLineCreatedEventDto,
  CommercialInvoiceLineEditedEventDto,
  CommercialInvoiceLineDeletedEventDto
} from "@/commercial-invoice/dto/event.dto";
import { Shipment, FIND_SHIPMENT_RELATIONS } from "nest-modules";
import { ComplianceValidationService } from "../services/compliance-validation.service";

@Injectable()
export class CommercialInvoiceListener {
  constructor(
    @InjectQueue(ShipmentQueueName.UPDATE_ENTRY)
    private readonly updateEntryQueue: UpdateEntryQueue,
    @Inject(forwardRef(() => ComplianceValidationService))
    private readonly complianceValidationService: ComplianceValidationService,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}
  private readonly logger = new Logger(CommercialInvoiceListener.name);

  @OnEvent(CommercialInvoiceEvent.COMMERCIAL_INVOICE_EDITED)
  @OnEvent(CommercialInvoiceEvent.COMMERCIAL_INVOICE_DELETED)
  @OnEvent(CommercialInvoiceEvent.COMMERCIAL_INVOICE_LINE_CREATED)
  @OnEvent(CommercialInvoiceEvent.COMMERCIAL_INVOICE_LINE_EDITED)
  @OnEvent(CommercialInvoiceEvent.COMMERCIAL_INVOICE_LINE_DELETED)
  async onCommercialInvoiceUpdated(
    event:
      | CommercialInvoiceEditedEventDto
      | CommercialInvoiceDeletedEventDto
      | CommercialInvoiceLineCreatedEventDto
      | CommercialInvoiceLineEditedEventDto
      | CommercialInvoiceLineDeletedEventDto
  ) {
    this.logger.log(`On Commercial Invoice Updated: ${JSON.stringify(event)}`);

    const shipment = await this.dataSource.manager.findOne(Shipment, {
      where: {
        id: event.shipmentId
      },
      relations: FIND_SHIPMENT_RELATIONS
    });
    this.logger.log(
      `Entry to be updated shipment: ${shipment?.id}, Customs status: ${shipment?.customsStatus}`
    );

    if (!shipment) {
      this.logger.warn(`Shipment not found for commercial invoice ${event.commercialInvoiceId}, skipping...`);
      return;
    }
    if (!this.complianceValidationService.isShipmentEntryUploaded(shipment)) {
      this.logger.warn(`Shipment ${shipment.id} is not uploaded to Candata, skipping...`);
      return;
    }
    if (!this.complianceValidationService.canShipmentUpdateEntry(shipment)) {
      this.logger.warn(`Shipment ${shipment.id} entry cannot be updated, skipping...`);
      return;
    }

    this.logger.log(`Adding shipment ${shipment.id} to update entry queue...`);
    await this.updateEntryQueue.add(
      shipment.id.toString(),
      {
        shipmentId: shipment.id
      },
      {
        deduplication: {
          id: shipment.id.toString(),
          ttl: 5000 // 5 seconds
        }
      }
    );
  }
}
