import { CronJobName } from "@/cron/types/cron.types";
import { runWorkerTask } from "@/worker/worker.utils";
import { Inject, Injectable, Logger } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { ShipmentEvent } from "../types/event.types";
import { NodeEnv } from "nest-modules";
import { CustomStatusService } from "../services/custom-status.service";

@Injectable()
export class CustomsStatusListener {
  constructor(
    @Inject(CustomStatusService)
    private readonly customStatusService: CustomStatusService
  ) {}

  private readonly logger = new Logger(CustomsStatusListener.name);

  /**
   * This event is triggered by the cronjob to check the customs status changes of all shipments
   */
  @OnEvent(ShipmentEvent.CUSTOMS_STATUS_CHECK)
  async onCustomsStatusCheck() {
    if (process.env.NODE_ENV !== NodeEnv.PRODUCTION) {
      await this.customStatusService.customsStatusCheck();
      return;
    }
    this.logger.log("Received customs status check event. Run worker task...");
    await runWorkerTask(CronJobName.CUSTOMS_STATUS_CHECK);
  }
}
