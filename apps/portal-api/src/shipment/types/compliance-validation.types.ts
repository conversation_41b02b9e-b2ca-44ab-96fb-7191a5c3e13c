import {
  CanadaAntiDumping,
  CanadaOgd,
  CanadaTariff,
  CertificateOfOrigin,
  CommercialInvoice,
  CommercialInvoiceColumn,
  CommercialInvoiceLine,
  CommercialInvoiceLineMeasurement,
  FIND_COMMERCIAL_INVOICE_LINE_RELATIONS,
  FIND_COMMERCIAL_INVOICE_RELATIONS,
  OgdFiling,
  Product,
  Shipment,
  ShipmentColumn,
  SimaFiling,
  TradePartner,
  TradePartnerColumn
} from "nest-modules";
import { FindOptionsRelations } from "typeorm";

export interface ProductCompliance {
  product: Product;
  ogdMappings: Array<{ canadaOgd: CanadaOgd; ogdFiling?: OgdFiling | null }>;
  antiDumpingMappings: Array<{ canadaAntiDumping: CanadaAntiDumping; simaFiling?: SimaFiling | null }>;
  certificateOfOrigin?: CertificateOfOrigin | null;
}

export interface TradePartnerCompliance {
  tradePartner: TradePartner;
  missingFields: Array<TradePartnerColumn>;
  isUSOrCanadaAddress: boolean;
  hasPostalCode: boolean;
}

export interface CommercialInvoiceLineCompliance {
  line: CommercialInvoiceLine;
  quantity: number;
  perUnitMeasurements: Array<CommercialInvoiceLineMeasurement>;
  canadaTariff?: CanadaTariff | null;
  productCompliance?: ProductCompliance | null;
}

export interface CommercialInvoiceCompliance {
  commercialInvoice: CommercialInvoice;
  missingFields: Array<CommercialInvoiceColumn>;
  shipToCompliance?: TradePartnerCompliance | null;
  vendorCompliance?: TradePartnerCompliance | null;
  lineCompliances: Array<CommercialInvoiceLineCompliance>;
}

export interface ShipmentCompliance {
  shipment: Shipment;
  isSubmitted: boolean;
  missingFields: Array<ShipmentColumn>;
  invoiceCompliances: Array<CommercialInvoiceCompliance>;
}

export const VALIDATION_FIND_COMMERCIAL_INVOICE_RELATIONS: FindOptionsRelations<CommercialInvoice> = {
  ...FIND_COMMERCIAL_INVOICE_RELATIONS,
  shipTo: {
    country: true
  },
  vendor: {
    country: true
  },
  commercialInvoiceLines: FIND_COMMERCIAL_INVOICE_LINE_RELATIONS
};

export const OCEAN_PORT_CITIES = ["VANCOUVER"];
