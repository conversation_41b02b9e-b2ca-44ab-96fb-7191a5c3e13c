import { RegisterQueueOptions } from "@nestjs/bullmq";
import { NestWorkerOptions } from "@nestjs/bullmq/dist/interfaces/worker-options.interface";
import { Job, JobsOptions, Queue } from "bullmq";

export const DEFAULT_JOB_OPTIONS: JobsOptions = {
  attempts: 10,
  backoff: {
    type: "fixed",
    delay: 10000 // 10 seconds
  },
  removeOnComplete: {
    age: 3600,
    count: 1000
  },
  removeOnFail: {
    age: 24 * 3600
  }
};

export const DEFAULT_WORKER_OPTIONS: NestWorkerOptions = {
  concurrency: 1
};

export enum ShipmentQueueName {
  UPDATE_ENTRY = "update-entry"
}

export const SHIPMENT_QUEUES: Array<RegisterQueueOptions> = [
  {
    name: ShipmentQueueName.UPDATE_ENTRY,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  }
];

// Update Entry Queue
export interface UpdateEntryJobData {
  shipmentId: number;
}
export type UpdateEntryJob = Job<UpdateEntryJobData, null, string>;
export type UpdateEntryQueue = Queue<UpdateEntryJobData, null, string>;
