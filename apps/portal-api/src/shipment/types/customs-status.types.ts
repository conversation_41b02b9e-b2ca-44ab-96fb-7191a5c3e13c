import { CustomsStatus } from "nest-modules";

export enum RNSResponseCategory {
  VALIDATED = "validated",
  ACCEPTED_WAITING = "accepted-waiting",
  RELEASED = "released",
  EXAM = "exam",
  UNKNOWN = "unknown"
}

export const RNS_RESPONSE_CATEGORY_TO_CUSTOMS_STATUS: Record<
  Exclude<RNSResponseCategory, RNSResponseCategory.UNKNOWN>,
  CustomsStatus
> = {
  [RNSResponseCategory.VALIDATED]: CustomsStatus.ENTRY_SUBMITTED,
  [RNSResponseCategory.ACCEPTED_WAITING]: CustomsStatus.ENTRY_ACCEPTED,
  [RNSResponseCategory.RELEASED]: CustomsStatus.RELEASED,
  [RNSResponseCategory.EXAM]: CustomsStatus.EXAM
};

export const RNS_RESPONSE_MESSAGE: Record<
  Exclude<RNSResponseCategory, RNSResponseCategory.UNKNOWN>,
  string
> = {
  [RNSResponseCategory.VALIDATED]: "Declaration Validated",
  [RNSResponseCategory.ACCEPTED_WAITING]: "Declaration Accepted, Awaiting Arrival of Goods",
  [RNSResponseCategory.RELEASED]: "Goods Released",
  [RNSResponseCategory.EXAM]: "Goods Required for Examinations"
};
