import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards
} from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import {
  ApiBadRequestResponse,
  ApiExcludeEndpoint,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags
} from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses,
  BadRequestResponseDto,
  Container,
  CreateShipmentDto,
  EditShipmentDto,
  FallbackEnricherResponseDto,
  GetShipmentCustomsActivitiesResponseDto,
  GetShipmentDutySummaryResponseDto,
  GetShipmentsDto,
  GetShipmentsResponseDto,
  NotFoundResponseDto,
  Shipment,
  ValidateShipmentComplianceResponseDto
} from "nest-modules";
import { ComplianceValidationService } from "./services/compliance-validation.service";
import { ContainerService } from "./services/container.service";
import { EntrySubmissionService } from "./services/entry-submission.service";
import { ShipmentService } from "./services/shipment.service";
import { ShipmentEvent } from "./types/event.types";

@ApiTags("Shipment API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("shipments")
export class ShipmentController {
  constructor(
    private readonly complianceValidationService: ComplianceValidationService,
    private readonly entrySubmissionService: EntrySubmissionService,
    private readonly shipmentService: ShipmentService,
    private readonly containerService: ContainerService,
    private readonly eventEmitter: EventEmitter2
  ) {}

  @ApiExcludeEndpoint()
  @Get(":id/compliance")
  async getShipmentCompliance(@Param("id", ParseIntPipe) id: number) {
    return await this.complianceValidationService.getShipmentCompliances([id]);
  }

  @ApiExcludeEndpoint()
  @Get("customs-status-check")
  async testCustomsStatusCheck() {
    this.eventEmitter.emit(ShipmentEvent.CUSTOMS_STATUS_CHECK);
    return;
  }

  @ApiExcludeEndpoint()
  @Post("shipment-accounting-check")
  async testShipmentAccountingCheck() {
    this.eventEmitter.emit(ShipmentEvent.ACCOUNTING_CHECK);
    return;
  }

  @ApiOperation({ summary: "Get Shipments" })
  @ApiGetManyResponses({ type: GetShipmentsResponseDto })
  @Get()
  async getShipments(@Query() getShipmentsDto: GetShipmentsDto) {
    return await this.shipmentService.getShipments(getShipmentsDto);
  }

  @ApiOperation({ summary: "Get Shipment" })
  @ApiParam({ name: "id", type: "integer", description: "Shipment ID" })
  @ApiGetByIdResponses({ type: Shipment })
  @Get(":id")
  async getShipmentById(@Param("id", ParseIntPipe) id: number) {
    const shipment = await this.shipmentService.getShipmentById(id);
    if (!shipment) throw new NotFoundException("Shipment not found");
    return shipment;
  }

  @ApiOperation({ summary: "Create Shipment" })
  @ApiCreateResponses({ type: Shipment })
  @Post()
  async createShipment(@Body() createShipmentDto: CreateShipmentDto) {
    return await this.shipmentService.createShipment(createShipmentDto);
  }

  @ApiOperation({ summary: "Edit Shipment" })
  @ApiParam({ name: "id", type: "integer", description: "Shipment ID" })
  @ApiEditResponses({ type: Shipment })
  @Put(":id")
  async editShipment(@Param("id", ParseIntPipe) id: number, @Body() editShipmentDto: EditShipmentDto) {
    return await this.shipmentService.editShipment(id, editShipmentDto);
  }

  @ApiOperation({ summary: "Delete Shipment" })
  @ApiParam({ name: "id", type: "integer", description: "Shipment ID" })
  @ApiDeleteResponses()
  @HttpCode(204)
  @Delete(":id")
  async deleteShipment(@Param("id", ParseIntPipe) id: number) {
    await this.shipmentService.deleteShipment(id);
  }

  @ApiOperation({
    summary: "Validate Shipment Compliance",
    description:
      "Validate compliance of all commercial invoices associated with the shipment, and update the customs status of the shipment accordingly"
  })
  @ApiParam({ name: "id", type: "integer", description: "Shipment ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiOkResponse({ type: ValidateShipmentComplianceResponseDto })
  @HttpCode(200)
  @Post(":id/validate-compliance")
  async validateShipmentCompliance(@Param("id", ParseIntPipe) id: number) {
    return await this.shipmentService.validateShipmentCompliance(id);
  }

  @ApiOperation({ summary: "Get Shipment Duty Summary" })
  @ApiParam({ name: "id", type: "integer", description: "Shipment ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiOkResponse({ type: GetShipmentDutySummaryResponseDto })
  @Get(":id/duty-summary")
  async getShipmentDutySummary(@Param("id", ParseIntPipe) id: number) {
    return await this.shipmentService.getShipmentDutySummary(id);
  }

  @ApiOperation({ summary: "Get Shipment Customs Activities" })
  @ApiParam({ name: "id", type: "integer", description: "Shipment ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiOkResponse({ type: GetShipmentCustomsActivitiesResponseDto })
  @Get(":id/customs-activities")
  async getShipmentCustomsActivities(@Param("id", ParseIntPipe) id: number) {
    return await this.shipmentService.getShipmentCustomsActivities(id);
  }

  @ApiOperation({ summary: "Submit Shipment Entry" })
  @ApiParam({ name: "id", type: "integer", description: "Shipment ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiOkResponse({ type: Shipment })
  @HttpCode(200)
  @Post(":id/submit-entry")
  async submitShipmentEntry(@Param("id", ParseIntPipe) id: number) {
    return await this.entrySubmissionService.submitShipmentEntry(id);
  }

  @ApiOperation({ summary: "Update Shipment Entry" })
  @ApiParam({ name: "id", type: "integer", description: "Shipment ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiOkResponse({ type: Shipment })
  @HttpCode(200)
  @Post(":id/update-entry")
  async updateShipmentEntry(@Param("id", ParseIntPipe) id: number) {
    return await this.entrySubmissionService.updateShipmentEntry(id);
  }

  @ApiOperation({ summary: "Recalculate Shipment Duties and Taxes" })
  @ApiParam({ name: "id", type: "integer", description: "Shipment ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiOkResponse({ type: Shipment })
  @HttpCode(200)
  @Post(":id/recalculate-duties-and-taxes")
  async recalculateDutiesAndTaxes(@Param("id", ParseIntPipe) id: number) {
    return await this.entrySubmissionService.recalculateDutiesAndTaxes(id);
  }

  @ApiOperation({ summary: "Get Shipment Enrichment" })
  @ApiParam({ name: "id", type: "integer", description: "Shipment ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiOkResponse({ type: FallbackEnricherResponseDto })
  @HttpCode(200)
  @Get(":id/fallbacks")
  async getShipmentFallbacks(@Param("id", ParseIntPipe) id: number) {
    return await this.shipmentService.getEnrichmentResult(id);
  }

  //#region Container Endpoints
  @ApiOperation({ summary: "Get Shipment Container" })
  @ApiParam({ name: "id", type: "integer", description: "Shipment ID" })
  @ApiParam({ name: "containerId", type: "integer", description: "Container ID" })
  @ApiGetByIdResponses({ type: Container })
  @Get(":id/containers/:containerId")
  async getShipmentContainer(
    @Param("id", ParseIntPipe) id: number,
    @Param("containerId", ParseIntPipe) containerId: number
  ) {
    const container = await this.containerService.getContainerById(id, containerId);
    if (!container) throw new NotFoundException("Container not found");
    return container;
  }
  //#endregion Container Endpoints
}
