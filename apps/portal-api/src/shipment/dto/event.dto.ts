import { CreateShipmentDto, EditShipmentDto, ShipmentColumn, User } from "nest-modules";
import { QueryRunner } from "typeorm";

export class BaseShipmentChangeEventDto {
  constructor(
    /**
     * ID of the shipment
     */
    public readonly shipmentId: number,
    /**
     * Query runner to execute database operations.
     */
    // public readonly queryRunner?: QueryRunner | null,
    /**
     * User who triggered the event.
     */
    public readonly user?: User | null
  ) {}
}

export class ShipmentCreatedEventDto extends BaseShipmentChangeEventDto {
  constructor(
    shipmentId: number,
    //queryRunner?: QueryRunner | null,
    user?: User | null,
    /**
     * DTO used to create the shipment.
     */
    public readonly createShipmentDto?: CreateShipmentDto | null
  ) {
    super(shipmentId, user);
  }
}

export class ShipmentEditedEventDto extends BaseShipmentChangeEventDto {
  constructor(
    shipmentId: number,
    user?: User | null,
    /**
     * DTO used to edit the shipment.
     */
    public readonly editShipmentDto?: EditShipmentDto | null
  ) {
    super(shipmentId, user);
  }
}

export class ShipmentDeletedEventDto extends BaseShipmentChangeEventDto {
  constructor(shipmentId: number, user?: User | null) {
    super(shipmentId, user);
  }
}

export class ShipmentChangedFieldDto {
  constructor(
    /**
     * Field that was changed.
     */
    public readonly field: ShipmentColumn,
    /**
     * Old value of the field.
     */
    public readonly oldValue: any,
    /**
     * New value of the field.
     */
    public readonly newValue: any
  ) {}
}

export class ShipmentAttemptEditSubmissionMandatoryFields extends BaseShipmentChangeEventDto {
  constructor(
    shipmentId: number,
    user?: User | null,
    /**
     * List of fields that were changed.
     */
    public readonly changedFields?: ShipmentChangedFieldDto[]
  ) {
    super(shipmentId, user);
  }
}

export class ShipmentDeleteCandataEntryEventDto extends BaseShipmentChangeEventDto {
  constructor(
    shipmentId: number,
    /**
     * Transaction number or file number of the shipment
     */
    public readonly transactionNumberOrFileNumber: string,
    /**
     * Importer ID of the shipment
     */
    public readonly importerId: number,
    user?: User | null
  ) {
    super(shipmentId, user);
  }
}
