import { DynamicModule, Logger, Module, Provider } from "@nestjs/common";
import { BullModule } from "@nestjs/bullmq";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  TariffSyncHistory,
  UsTariff,
  UsPgaRequirement,
  UsTariffPgaRequirement,
  UsTariffModule as BaseUsTariffModule
} from "nest-modules";
import { UsTariffDownloadProcessor } from "./processors/us-tariff-download.processor";
import { UsTariffUploadController } from "./us-tariff-upload.controller";
import { UsTariffQueueName } from "./types/apify.types";

@Module({})
export class UsTariffModule {
  static forRoot(): DynamicModule {
    const providers: Array<Provider> = [];

    // Register processor only when not in feature mode
    if (process.env.FEATURE) {
      Logger.warn("US Tariff processors are disabled when feature is set", "UsTariffModule");
    } else {
      Logger.log("US Tariff processors are enabled when feature is not set", "UsTariffModule");
      providers.push(UsTariffDownloadProcessor);
    }

    return {
      module: UsTariffModule,
      imports: [
        TypeOrmModule.forFeature([UsTariff, TariffSyncHistory, UsPgaRequirement, UsTariffPgaRequirement]),
        BaseUsTariffModule.forRoot(),
        BullModule.registerQueue({
          name: UsTariffQueueName.US_TARIFF_DOWNLOAD,
          defaultJobOptions: {
            attempts: 1,
            removeOnComplete: {
              age: 3600,
              count: 1000
            },
            removeOnFail: {
              age: 24 * 3600
            }
          }
        })
      ],
      controllers: [UsTariffUploadController],
      providers,
      exports: []
    };
  }
}
