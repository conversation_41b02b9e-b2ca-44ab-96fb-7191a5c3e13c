// Import base types from nest-modules instead of duplicating them
export { APIFY_US_TARIFF_ACTOR, APIFY_US_TARIFF_STORE_NAME, ApifyUsTariffResult } from "nest-modules";

// Application-specific types (these should stay here)
export enum UsTariffQueueName {
  US_TARIFF_DOWNLOAD = "us-tariff-download"
}

export interface UsTariffDownloadJobData {
  date?: string; // MMDDYY format, optional (defaults to today)
  forceDownload?: boolean; // Force download even if exists
  organizationId?: number; // Optional for audit
}
