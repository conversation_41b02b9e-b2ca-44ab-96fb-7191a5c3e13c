import { StorageService } from "../../storage/storage.service";
import { DocumentModuleOptions } from "../document.module";
import { BarcodeScannerService } from "../services/barcode-scanner/barcode-scanner.service";
import { ParserService } from "../services/parsers/parser.service";
import { LlamaParseParserService } from "../services/parsers/parsers/llama-parse/llama-parse.service";

export const ParserServiceProvider = {
  provide: ParserService,
  useFactory: (
    options: DocumentModuleOptions,
    storageService: StorageService,
    barcodeScannerService: BarcodeScannerService
  ) => {
    // return new TikaService(options, storageService);
    // return new TextractService(options, storageService);
    return new LlamaParseParserService(options, storageService, barcodeScannerService);
  },
  inject: ["DOCUMENT_MODULE_OPTIONS", StorageService, BarcodeScannerService]
};
