import { registerDecorator, ValidationArguments, ValidationOptions } from "class-validator";

export function isLessThanOrEqual(value: number, relatedValue: number) {
  return typeof value === "number" && typeof relatedValue === "number" && value <= relatedValue;
}

export function IsLessThanOrEqual(property: string, validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: "IsLessThanOrEqual",
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [property],
      validator: {
        validate(value: any, args: ValidationArguments): boolean {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];
          return isLessThanOrEqual(value, relatedValue);
        }
      }
    });
  };
}
