import { FileValidator, Logger } from "@nestjs/common";
import { IFile } from "@nestjs/common/pipes/file/interfaces";

export type SupportedFileTypeValidatorOptions = {
  message?: string;
};

/**
 * Validates supported file type for DocumentProcessing module
 *
 * @see [Apache Tika Mime Types](https://github.com/apache/tika/blob/main/tika-core/src/main/resources/org/apache/tika/mime/tika-mimetypes.xml)
 */
export class SupportedFileTypeValidator extends FileValidator<
  SupportedFileTypeValidatorOptions | undefined,
  IFile
> {
  private readonly logger = new Logger(SupportedFileTypeValidator.name);

  constructor(options?: SupportedFileTypeValidatorOptions) {
    if (!options) {
      options = {
        message: "Unsupported file type, supported types are: Image, PDF, DOC, DOCX, XLS, XLSX"
      };
    }

    super(options);
  }

  private readonly supportedFileTypes: string[] = [
    // images
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/tiff",

    // pdf
    "application/pdf",

    // doc, docx
    "application/msword",
    "application/vnd.ms-word",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",

    // xls, xlsx
    "application/ms-excel",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  ];

  buildErrorMessage(): string {
    return this.validationOptions.message;
  }

  isValid(file?: IFile): boolean | Promise<boolean> {
    const isValid = !!file && "mimetype" in file && this.supportedFileTypes.includes(file?.mimetype);

    if (!isValid) {
      this.logger.debug("Mime " + file?.mimetype + " is not valid");
    }

    return isValid;
  }
}
