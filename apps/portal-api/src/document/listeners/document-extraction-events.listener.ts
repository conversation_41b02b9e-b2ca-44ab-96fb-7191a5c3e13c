import { Injectable } from "@nestjs/common";
import { EventEmitter2, OnEvent } from "@nestjs/event-emitter";
import { InjectRepository } from "@nestjs/typeorm";
import { Document, DocumentStatus } from "nest-modules";
import { Repository } from "typeorm";
import {
  DOCUMENT_EXTRACTION_COMPLETED_EVENT,
  DOCUMENT_EXTRACTION_FAILED_EVENT,
  DOCUMENT_EXTRACTION_STARTED_EVENT,
  DocumentExtractionCompletedEvent,
  DocumentExtractionFailedEvent,
  DocumentExtractionStartedEvent
} from "../events/document-extraction-events";

@Injectable()
export class DocumentExtractionEventsListener {
  constructor(
    @InjectRepository(Document) private readonly documentRepository: Repository<Document>,
    private readonly eventEmitter: EventEmitter2
  ) {}

  @OnEvent(DOCUMENT_EXTRACTION_STARTED_EVENT)
  async onDocumentExtractionStarted(event: DocumentExtractionStartedEvent) {
    this.eventEmitter.emit(`document.status-updated:${event.documentId}`, {
      status: DocumentStatus.EXTRACTING
    });
  }

  @OnEvent(DOCUMENT_EXTRACTION_FAILED_EVENT)
  async onDocumentExtractionFailed(event: DocumentExtractionFailedEvent) {
    this.eventEmitter.emit(`document.status-updated:${event.documentId}`, {
      status: DocumentStatus.EXTRACTION_FAILED
    });
  }

  @OnEvent(DOCUMENT_EXTRACTION_COMPLETED_EVENT)
  async onDocumentExtractionCompleted(event: DocumentExtractionCompletedEvent) {
    this.eventEmitter.emit(`document.status-updated:${event.documentId}`, {
      status: DocumentStatus.EXTRACTED
    });
  }
}
