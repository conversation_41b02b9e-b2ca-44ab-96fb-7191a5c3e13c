import { Injectable, Logger } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { DocumentCreatedEvent, DocumentEvent, DocumentUpdatedEvent } from "../events/document.events";
import { DocumentExtractionTaskDispatcher } from "../queues/dispatchers";
import { DocumentService } from "../services/document.service";

function hasTriggerExtraction(
  event: DocumentCreatedEvent | DocumentUpdatedEvent
): event is DocumentCreatedEvent {
  return "triggerExtraction" in event;
}

@Injectable()
export class DocumentCreatedListener {
  constructor(
    private readonly dispatcher: DocumentExtractionTaskDispatcher,
    private readonly documentService: DocumentService
  ) {}

  private readonly logger = new Logger(DocumentCreatedListener.name);

  @OnEvent(DocumentEvent.CREATED)
  @OnEvent(DocumentEvent.UPDATED)
  async handleDocumentCreatedEvent(event: DocumentCreatedEvent | DocumentUpdatedEvent) {
    if (hasTriggerExtraction(event) && !event.triggerExtraction) {
      this.logger.debug(
        `Document ${event.documentId} has triggerExtraction set to false, skipping extraction`
      );
      return;
    }

    const job = await this.dispatcher.processDocument({
      id: event.documentId,
      documentType: event.name,
      organizationId: event.organizationId,
      batchId: event.batchId,
      model: "gpt-4o"
    });

    await this.documentService.updateDocumentJobId(event.documentId, job.id);
  }
}
