import { Injectable } from "@nestjs/common";
import { OnEvent } from "@nestjs/event-emitter";
import { FILE_CREATED_EVENT, FileCreatedEvent } from "../events";
import { FileTaskDispatcher } from "../queues/dispatchers";

@Injectable()
export class FileListener {
  constructor(private readonly fileTaskDispatcher: FileTaskDispatcher) {}

  @OnEvent(FILE_CREATED_EVENT)
  async handleFileCreated(event: FileCreatedEvent) {
    // await this.fileTaskDispatcher.parse({
    //   fileId: event.id,
    //   organizationId: event.organizationId
    // });
  }
}
