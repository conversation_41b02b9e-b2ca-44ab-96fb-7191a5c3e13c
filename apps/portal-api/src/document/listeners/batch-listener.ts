import { Inject, Injectable, Logger } from "@nestjs/common";
import { EventEmitter2, OnEvent } from "@nestjs/event-emitter";
import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import {
  BATCH_DOCUMENT_AGGREGATED_EVENT,
  BatchDocumentAggregatedEvent,
  DocumentAggregation,
  DocumentAggregationStatus,
  FILE_BATCH_CREATED,
  FileBatchCreatedEvent,
  FileBatchStatus
} from "nest-modules";
import {
  AggregationCreatedEvent,
  AggregationEvents,
  AggregationFailedEvent,
  AggregationInProgressEvent,
  AggregationSuccessEvent,
  BatchAggregationEvents,
  ShipmentCreatedFromBatchEvent,
  ShipmentCreationFromBatchFailedEvent
} from "src/aggregation/events";
import { DataSource, EntityManager, In, Not, Repository } from "typeorm";
import {
  BatchCheckingFailedEvent,
  BatchDocumentsExtractedEvent,
  BatchReadyForAggregationEvent,
  FILE_STATUS_UPDATED_EVENT,
  FileBatchEvent,
  FileStatusUpdatedEvent
} from "../events";
import {
  DOCUMENT_EXTRACTION_COMPLETED_EVENT,
  DOCUMENT_EXTRACTION_STARTED_EVENT,
  DocumentExtractionCompletedEvent,
  DocumentExtractionStartedEvent
} from "../events/document-extraction-events";
import { DocumentCreatedEvent, DocumentEvent, DocumentShipmentChangedEvent } from "../events/document.events";
import { FileBatchTaskDispatcher } from "../queues/dispatchers";
import { DocumentShipmentService } from "../services/document-shipment.service";
import { DocumentService } from "../services/document.service";
import { FileBatchService } from "../services/file-batch.service";
import { SseEventService } from "../services/sse-event.service";

@Injectable()
export class BatchListener {
  private readonly logger = new Logger(BatchListener.name);

  constructor(
    @Inject(EventEmitter2)
    private readonly eventEmitter: EventEmitter2,
    private readonly sseEventService: SseEventService,
    @Inject(FileBatchService)
    private readonly fileBatchService: FileBatchService,
    @Inject(DocumentService)
    private readonly documentService: DocumentService,
    @Inject(DocumentShipmentService)
    private readonly documentShipmentService: DocumentShipmentService,
    @InjectRepository(DocumentAggregation)
    private readonly documentAggregationRepository: Repository<DocumentAggregation>,
    @Inject(FileBatchTaskDispatcher)
    private readonly fileBatchTaskDispatcher: FileBatchTaskDispatcher,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  @OnEvent(FILE_BATCH_CREATED)
  handleFileBatchCreated(event: FileBatchCreatedEvent) {
    this.fileBatchTaskDispatcher.dispatchFileBatchTasks(
      event.fileBatchId,
      event.organizationId,
      event.fileIds,
      event.shipmentId
    );
  }

  @OnEvent(DocumentEvent.CREATED)
  async handleDocumentCreated(event: DocumentCreatedEvent) {
    const { batchId, fileId, name, documentId } = event;
    console.log(event);
    this.sendSseEvent(batchId, "document-created", {
      documentId,
      fileId,
      name
    });

    // associate the document with the shipment
    const batch = await this.fileBatchService.getFileBatch(batchId);
    if (batch.shipmentId) {
      await this.documentService.associateWithShipment(event.documentId, batch.shipmentId);
    }
  }

  /**
   * TODO: this needs to be moved to a different listener
   */
  @OnEvent(DocumentEvent.SHIPMENT_CHANGED, { async: true })
  async handleDocumentShipmentChanged(event: DocumentShipmentChangedEvent) {
    // return await this.documentShipmentService.verifyDocumentBelongsToCurrentShipment(event.documentId);
  }

  @OnEvent(DOCUMENT_EXTRACTION_COMPLETED_EVENT)
  async handleDocumentExtracted(event: DocumentExtractionCompletedEvent) {
    const { batchId } = event;
    this.sendSseEvent(batchId, "document-extracted", {
      documentId: event.documentId
    });

    // TODO: this is for single document extraction

    // update file batch status
    // await this.fileBatchService.updateFileBatchStatus(batchId, FileBatchStatus.EXTRACTED);
  }

  private sendSseEvent(batchId: string, eventName: string, data: any) {
    this.sseEventService.send(`batch-${batchId}`, eventName, {
      ...data,
      date: new Date()
    });
  }

  @OnEvent(FileBatchEvent.READY_FOR_AGGREGATION)
  handleBatchDocumentsValidated(event: BatchReadyForAggregationEvent) {
    this.logger.debug(`Batch documents validated: ${JSON.stringify(event)}`);
    const { batchId } = event;
    this.sendSseEvent(batchId, "batch-documents-validated", {
      batchId
    });
  }

  @OnEvent(FileBatchEvent.CHECKING_FAILED)
  handleBatchCheckingFailed(event: BatchCheckingFailedEvent) {
    this.logger.debug(`Batch checking failed: ${JSON.stringify(event)}`);
    const { batchId, error } = event;
    this.sendSseEvent(batchId, "batch-checking-failed", {
      batchId,
      error
    });
  }

  @OnEvent(FILE_STATUS_UPDATED_EVENT)
  handleFileStatusUpdated(event: FileStatusUpdatedEvent) {
    this.logger.debug(`File status updated: ${JSON.stringify(event)}`);
    const { batchId, fileId, status } = event;
    this.sendSseEvent(batchId, "file-status-updated", {
      fileId,
      batchId,
      status
    });
  }

  @OnEvent(AggregationEvents.CREATED)
  handleDocumentAggregationCreated(event: AggregationCreatedEvent) {
    this.logger.debug(`Document aggregation created: ${JSON.stringify(event)}`);

    if (event.batchId) {
      this.sendSseEvent(event.batchId, "document-aggregation-created", {
        aggregationId: event.id,
        action: event.action,
        documents: event.documents.map((d) => ({ id: d.id, name: d.name }))
      });
    }
  }

  @OnEvent(AggregationEvents.CREATED)
  @OnEvent(AggregationEvents.IN_PROGRESS)
  async setBatchStatusToAggregating(event: AggregationCreatedEvent | AggregationInProgressEvent) {
    if (event.batchId) {
      await this.fileBatchService.updateFileBatchStatus(event.batchId, FileBatchStatus.AGGREGATING);
    }
  }

  @OnEvent(BatchAggregationEvents.SHIPMENT_CREATION_FAILED)
  handleShipmentCreationFailed(event: ShipmentCreationFromBatchFailedEvent) {
    this.logger.debug(`Shipment creation failed: ${JSON.stringify(event)}`);
    const { batchId, reason } = event;
    this.sendSseEvent(batchId, "batch.shipmentCreationFailed", {
      reason
    });
  }

  @OnEvent(BatchAggregationEvents.SHIPMENT_CREATED)
  handleShipmentCreated(event: ShipmentCreatedFromBatchEvent) {
    this.logger.debug(`Shipment created: ${JSON.stringify(event)}`);
    const { shipmentId, batchId } = event;

    // set shipment id
    this.fileBatchService.setBatchShipmentId(batchId, shipmentId);

    this.sendSseEvent(batchId, "batch.shipmentCreated", {
      shipmentId
    });
  }

  @OnEvent(FileBatchEvent.DOCUMENT_EXTRACTED)
  handleBatchDocumentExtracted(event: BatchDocumentsExtractedEvent) {
    this.sendSseEvent(event.batchId, "batch-documents-extracted", {});
  }

  @OnEvent(AggregationEvents.SUCCESS)
  handleDocumentAggregationSuccess(event: AggregationSuccessEvent) {
    // if batch id is not set, we don't need to set the shipment id
    if (event.batchId === null) {
      return;
    }

    this.logger.debug(`Document aggregation success: ${JSON.stringify(event)}`);
    const { batchId, shipmentId, documentId, id } = event;
    this.sendSseEvent(batchId, "document-aggregation-success", {
      shipmentId,
      documentId,
      aggregationId: id
    });

    if (shipmentId) {
      this.fileBatchService.setBatchShipmentId(batchId, shipmentId);
    }

    this.checkIfAllAggregationsAreCompleted(batchId);
  }

  private async checkIfAllAggregationsAreCompleted(batchId: string) {
    // start a transaction
    const allAggregationsCompleted = await this.dataSource.transaction(async (manager: EntityManager) => {
      // lock for update
      await this.fileBatchService.lockForUpdate(batchId, manager.queryRunner);
      // get file batch status
      const fileBatchStatus = await this.fileBatchService.getFileBatchStatus(batchId, manager.queryRunner);

      // if file batch status is not aggregating, skip
      if (fileBatchStatus !== FileBatchStatus.AGGREGATING) {
        this.logger.error(
          `Skipping batch document aggregation check because file batch status is not aggregating. current status: ${fileBatchStatus}`
        );
        return null;
      }

      const hasPendingAggregations = await manager.exists(DocumentAggregation, {
        where: {
          batchId,
          status: Not(In([DocumentAggregationStatus.SUCCESS, DocumentAggregationStatus.FAILED]))
        },
        select: {
          id: true,
          status: true
        }
      });

      if (hasPendingAggregations) {
        this.logger.debug(`hasPendingAggregations, batchId: ${batchId}`);
        return false;
      }

      // update status
      await this.fileBatchService.updateFileBatchStatus(
        batchId,
        FileBatchStatus.AGGREGATED,
        manager.queryRunner
      );
      return true;
    });

    if (!allAggregationsCompleted) {
      return;
    }

    // assemble result
    const aggregations = await this.documentAggregationRepository.find({
      where: { batchId },
      select: {
        id: true,
        status: true,
        action: true,
        targetId: true,
        targetType: true,
        organizationId: true,
        documents: {
          id: true,
          name: true
        },
        // TODO: remove this in the future
        shipment: {
          id: true
        }
      },
      relations: {
        documents: {
          shipment: true
        }
      }
    });

    const result: BatchDocumentAggregatedEvent["result"] = aggregations
      .map((aggregation) => {
        if (aggregation.documents.length === 0) {
          return null;
        }

        const success = aggregation.status === DocumentAggregationStatus.SUCCESS;

        return {
          id: aggregation.id,
          shipmentId: aggregation.documents[0].shipment?.id,
          documentId: aggregation.documents[0].id,
          documentName: aggregation.documents[0].name,
          action: aggregation.action,

          documents: aggregation.documents.map((document) => ({
            id: document.id,
            name: document.name
          })),

          // the result of aggregation
          ...(success
            ? {
                success: true as const,
                targetId: aggregation.targetId,
                targetType: aggregation.targetType
              }
            : {
                success: false as const,
                error: "Failed to aggregate documents"
              })
        };
      })
      .filter((r) => r != null);

    this.eventEmitter.emit(
      BATCH_DOCUMENT_AGGREGATED_EVENT,
      new BatchDocumentAggregatedEvent({
        batchId,
        organizationId: aggregations[0].organizationId,
        result
      })
    );
  }

  @OnEvent(AggregationEvents.FAILED)
  handleDocumentAggregationFailed(event: AggregationFailedEvent) {
    this.logger.debug(`Document aggregation failed: ${JSON.stringify(event)}`);
    const { batchId, shipmentId, documentId, id } = event;
    this.sendSseEvent(batchId, "document-aggregation-failed", {
      shipmentId,
      documentId,
      aggregationId: id
    });

    this.checkIfAllAggregationsAreCompleted(batchId);
  }

  @OnEvent(BATCH_DOCUMENT_AGGREGATED_EVENT)
  handleBatchDocumentAggregated(event: BatchDocumentAggregatedEvent) {
    this.logger.debug(`Batch document aggregated: ${JSON.stringify(event)}`);
    const { batchId, organizationId, result } = event;
    this.sendSseEvent(batchId, "batch-document-aggregated", {
      result
    });

    // first we try to find the create shipment action
    // TODO: remove this in the future
    let successCreateShipmentAction = result.find((r) => r.action === "create-shipment" && r.success);

    if (successCreateShipmentAction) {
      this.sendSseEvent(batchId, "batch-shipment-created", {
        shipmentId: successCreateShipmentAction.shipmentId
      });
    }
  }

  @OnEvent(DOCUMENT_EXTRACTION_STARTED_EVENT)
  async updateBatchStatusToExtracting(event: DocumentExtractionStartedEvent) {
    if (event.batchId) {
      await this.fileBatchService.updateFileBatchStatus(event.batchId, FileBatchStatus.EXTRACTING);
    }
  }
}
