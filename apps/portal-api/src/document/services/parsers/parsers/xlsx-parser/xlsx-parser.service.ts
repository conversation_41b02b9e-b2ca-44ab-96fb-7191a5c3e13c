import { DocumentModuleOptions } from "@/document/document.module";
import { InvokeCommand, LambdaClient } from "@aws-sdk/client-lambda";
import { Inject, Injectable, Logger } from "@nestjs/common";
import { StorageService } from "../../../../../storage/storage.service";
import { ParserError } from "../../errors/errors";
import { ParserService } from "../../parser.service";
import { ParseResult } from "../../types/types";
import { XlsxParseResult } from "./types";

@Injectable()
export class XlsxParserService extends ParserService {
  private readonly logger = new Logger(XlsxParserService.name);
  private lambdaClient: LambdaClient;

  constructor(
    @Inject("DOCUMENT_MODULE_OPTIONS")
    private readonly options: DocumentModuleOptions,
    @Inject(StorageService)
    private readonly storageService: StorageService
  ) {
    super();
    this.lambdaClient = new LambdaClient({
      region: options.awsRegion || "ca-central-1"
    });
  }

  private async loadFile(path: string): Promise<Uint8Array> {
    const fileStream = await this.storageService.getFileStream(path);
    const chunks: Uint8Array[] = [];

    return new Promise<Uint8Array>((resolve, reject) => {
      fileStream.on("data", (chunk) => {
        chunks.push(chunk);
      });

      fileStream.on("end", () => {
        const buffer = Buffer.concat(chunks);
        resolve(new Uint8Array(buffer));
      });

      fileStream.on("error", (error) => {
        this.logger.error(`Error reading file stream: ${error.message}`);
        reject(new ParserError("Error reading file stream"));
      });
    });
  }

  /**
   * Parses the XLSX file using AWS Lambda
   *
   * @param filePath - The path to the file
   * @returns The parse result
   */
  private async parseFile(filePath: string): Promise<XlsxParseResult[]> {
    const fileContent = await this.loadFile(filePath);

    try {
      const base64File = Buffer.from(fileContent).toString("base64");

      const payload = JSON.stringify({
        body: base64File,
        isBase64Encoded: true,
        queryStringParameters: {
          output_type: "html"
        }
      });

      // 6MB limit
      const limit = 1024 * 1024 * 6;
      if (Buffer.byteLength(payload) > limit) {
        throw new ParserError("XLSX file is too large to parse");
      }

      const command = new InvokeCommand({
        FunctionName: "XlsxParser_parseXlsx",
        InvocationType: "RequestResponse",
        Payload: JSON.stringify({
          body: base64File,
          isBase64Encoded: true,
          queryStringParameters: {
            output_type: "html"
          }
        })
      });
      const response = await this.lambdaClient.send(command);

      if (response.FunctionError) {
        throw new Error(`Lambda execution failed: ${response.FunctionError}`);
      }

      if (!response.Payload) {
        throw new Error("No payload returned from Lambda");
      }

      const result = JSON.parse(Buffer.from(response.Payload).toString());

      return result.sheets;
    } catch (error) {
      this.logger.error(`Error parsing XLSX file: ${error.message}`);
      throw new ParserError(`Error parsing XLSX file: ${error.message}`);
    }
  }

  async getParseResult(path: string): Promise<ParseResult> {
    const result = await this.parseFile(path);

    return {
      parser: "xlsx",
      numPages: result.length,
      data: {
        pages: result.map((r) => ({
          page: r.index + 1,
          md: `# ${r.name}\n\n${r.html}`
        }))
      }
    };
  }
}
