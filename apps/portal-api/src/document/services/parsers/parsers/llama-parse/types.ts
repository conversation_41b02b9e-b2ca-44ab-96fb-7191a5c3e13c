export interface LlamaParseResult {
  pages: LlamaParse.Page[];
  job_metadata: LlamaParse.JobMetadata;
}

export namespace LlamaParse {
  export interface Page {
    page: number;
    text: string;
    md: string;
    images: Image[];
    charts: any[];
    items: Item[];
    status: string;
    links: any[];
    width: number;
    height: number;
    triggeredAutoMode: boolean;
    structuredData: any;
    noStructuredContent: boolean;
    noTextContent: boolean;

    // below are used for fallback
    mode?: "agent" | "lvm";
    originalMd?: string;
    diff?: number;
    percentage?: number;
  }

  export interface Image {
    name: string;
    height: number;
    width: number;
    x: number;
    y: number;
    type: string;
  }

  export interface Item {
    type: string;
    rows?: string[][];
    md: string;
    isPerfectTable?: boolean;
    csv?: string;
    bBox: BBox;
    value?: string;
  }

  export interface BBox {
    x: number;
    y: number;
    w: number;
    h: number;
  }

  export interface JobMetadata {
    credits_used: number;
    job_credits_usage: number;
    job_pages: number;
    job_auto_mode_triggered_pages: number;
    job_is_cache_hit: boolean;
    credits_max: number;
  }
}
