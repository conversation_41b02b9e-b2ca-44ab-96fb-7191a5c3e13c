import { DocumentModuleOptions } from "@/document/document.module";
import { InvokeCommand, LambdaClient } from "@aws-sdk/client-lambda";
import { LlamaParseReader } from "@llamaindex/cloud";
import { Inject, Injectable, Logger } from "@nestjs/common";
import fs from "fs";
import os from "os";
import path from "path";
import removeMarkdown from "remove-markdown";
import { v4 as uuidv4 } from "uuid";
import { StorageService } from "../../../../../storage/storage.service";
import { BarcodeScannerService } from "../../../barcode-scanner/barcode-scanner.service";
import { ParserError } from "../../errors/errors";
import { ParserService } from "../../parser.service";
import { ParseResult } from "../../types/types";
import { LlamaParseResult } from "./types";

type Barcode = {
  text: string;
  format: string;
  bBox: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
};

@Injectable()
export class LlamaParseParserService extends ParserService {
  private readonly logger = new Logger(LlamaParseParserService.name);
  private reader: LlamaParseReader;
  private agentReader: LlamaParseReader;
  private lambdaClient: LambdaClient;

  constructor(
    @Inject("DOCUMENT_MODULE_OPTIONS")
    private readonly options: DocumentModuleOptions,
    @Inject(StorageService)
    private readonly storageService: StorageService,
    @Inject(BarcodeScannerService)
    private readonly barcodeScannerService: BarcodeScannerService
  ) {
    super();

    const commonConfig = {
      apiKey: options.llamaCloudApiKey,
      resultType: "json",
      takeScreenshot: true,
      output_tables_as_HTML: true,
      model: "anthropic-sonnet-3.5",

      system_prompt_append: `Do not wrap the result in a markdown code block. Include all items on the page without omission. Place the header, footer, and page number each in a separate section.`,

      job_timeout_in_seconds: 600,
      job_timeout_extra_time_per_page_in_seconds: 120,
      doNotCache: true,
      invalidateCache: true
    } as Partial<LlamaParseReader>;

    this.agentReader = new LlamaParseReader({
      ...commonConfig,
      premiumMode: true
    });

    this.reader = new LlamaParseReader({
      ...commonConfig,
      parse_mode: "parse_page_with_lvm"
    });

    this.lambdaClient = new LambdaClient({
      region: options.awsRegion || "ca-central-1"
    });
  }

  private async loadFile(path: string): Promise<Uint8Array> {
    const fileStream = await this.storageService.getFileStream(path);
    const chunks: Uint8Array[] = [];

    return new Promise<Uint8Array>((resolve, reject) => {
      fileStream.on("data", (chunk) => {
        chunks.push(chunk);
      });

      fileStream.on("end", () => {
        const buffer = Buffer.concat(chunks);
        resolve(new Uint8Array(buffer));
      });

      fileStream.on("error", (error) => {
        this.logger.error(`Error reading file stream: ${error.message}`);
        reject(new ParserError("Error reading file stream"));
      });
    });
  }

  /**
   * Parses the file using LlamaParse
   *
   * @param filePath - The path to the file
   * @returns The parse result
   */
  private async parseFile(filePath: string): Promise<LlamaParseResult[]> {
    return this.useTempDir(async (tmpDir) => {
      const fileContent = await this.loadFile(filePath);
      const fileName = path.basename(filePath);
      // save the file to the tmp dir
      const tmpFilePath = path.join(tmpDir, fileName);
      fs.writeFileSync(tmpFilePath, fileContent);

      this.agentReader.user_prompt = `[${uuidv4()}]`;
      this.reader.user_prompt = `[${uuidv4()}]`;

      const results = await Promise.allSettled([
        this.agentReader.loadJson(tmpFilePath),
        this.reader.loadJson(tmpFilePath)
      ]);

      const agentResults: LlamaParseResult[] =
        results[0].status === "fulfilled" ? (results[0].value as unknown as LlamaParseResult[]) : [];
      const lvmResults: LlamaParseResult[] =
        results[1].status === "fulfilled" ? (results[1].value as unknown as LlamaParseResult[]) : [];

      if (results[0].status === "rejected") {
        throw new ParserError("Error communicating with LlamaParse");
      }

      if (results[1].status === "rejected") {
        this.logger.error("Cannot parse results from lvm, skipping", results[1].reason);
        return agentResults;
      }

      try {
        // merge the results, use agentResult as base
        for (const [index, result] of agentResults.entries()) {
          for (const agentPage of result.pages) {
            agentPage.mode = "agent";

            const lvmPage = lvmResults[index].pages.find((p) => p.page === agentPage.page);

            if (!lvmPage) {
              this.logger.warn(`LVM page not found for agent page ${agentPage.page}, skipping`);
              continue;
            }

            const lvmPlainText = removeMarkdown(lvmPage.md);
            const agentPlainText = removeMarkdown(agentPage.md);

            const lvmPlainTextLength = lvmPlainText.length;
            const agentPlainTextLength = agentPlainText.length;

            const diff = lvmPlainTextLength - agentPlainTextLength;
            const percentage = diff / ((lvmPlainTextLength + agentPlainTextLength) / 2);

            // if the percentage of the difference is greater than 3%, use the lvm page
            if (percentage > 0.03) {
              agentPage.originalMd = agentPage.md;
              agentPage.diff = diff;
              agentPage.percentage = percentage;
              agentPage.md = lvmPage.md;
              agentPage.mode = "lvm";
            }
          }
        }
      } catch (error) {
        this.logger.error("Cannot parse or merge results from lvm, skipping", error);
      }

      return agentResults;
    });
  }

  private async useTempDir(handler: (tmpDir: string) => Promise<any>) {
    const prefix = "claro-llama-parse-";
    let tmpDir: string;
    let result: any;
    try {
      tmpDir = fs.mkdtempSync(path.join(os.tmpdir(), prefix));
      result = await handler(tmpDir);
    } catch (error) {
      this.logger.error(error);
      throw new ParserError(error.message);
    } finally {
      try {
        if (tmpDir) {
          fs.rmSync(tmpDir, {
            recursive: true
          });
        }
      } catch (error) {
        this.logger.error(error);
      }
    }
    return result;
  }

  /**
   * Get barcodes from image
   *
   * @param image - The image to get barcodes from
   * @returns The barcodes
   */
  async getBarcodesFromImage(image: Buffer | ArrayBuffer | Blob | ImageData) {
    try {
      const barcodes = await this.barcodeScannerService.decodeImage(image);

      return barcodes.map((barcode) => ({
        text: barcode.text,
        format: barcode.format,
        bBox: {
          x: barcode.position.topLeft.x,
          y: barcode.position.topLeft.y,
          w: barcode.position.topRight.x - barcode.position.topLeft.x,
          h: barcode.position.bottomLeft.y - barcode.position.topLeft.y
        }
      }));
    } catch (error) {
      this.logger.error(error);
      return [];
    }
  }

  /**
   * Download image
   *
   * @param job - The job to download image from
   * @param originalFilePath - The original file path
   * @returns The image
   */
  async downloadImage(originalFilePath: string, job: LlamaParseResult) {
    await this.useTempDir(async (tmpDir) => {
      // use lambda function to convert pdf to png
      const command = new InvokeCommand({
        FunctionName: "pdf-to-png",
        Payload: JSON.stringify({
          bucket: this.storageService.getBucket(),
          key: originalFilePath
        })
      });

      try {
        // check if response is successful
        await this.lambdaClient.send(command);
      } catch (error) {
        this.logger.error("Error converting pdf to png", error);
        throw new ParserError("Error converting pdf to png");
      }

      for (const page of job.pages as any[]) {
        // get image from s3
        const pageImageKey = path.join(originalFilePath, `page_${page.page}.png`);
        if (await this.storageService.isFileExists(pageImageKey)) {
          const pageImageBlob = await this.storageService.getFileBlob(pageImageKey);
          // get barcodes from image
          const barcodes = await this.getBarcodesFromImage(pageImageBlob);
          page.barcodes = barcodes.filter((barcode) => barcode.text.length > 0);
        } else {
          page.barcodes = [];
        }
      }
    });
  }

  /**
   * Get parse result
   *
   * @param path - The path to the file
   * @returns The parse result
   */
  async getParseResult(path: string): Promise<ParseResult> {
    const result = await this.parseFile(path);

    if (result.length) {
      await this.downloadImage(path, result[0]);

      return {
        parser: "llama-parse",
        numPages: result[0].pages.length,
        data: result[0]
      };
    }
  }
}
