import { AnalyzeDocumentCommandOutput } from "@aws-sdk/client-textract";

export class Ho<PERSON>r<PERSON>dapter {
  constructor(private readonly result: AnalyzeDocumentCommandOutput) {}

  multiplePageParse() {
    const resultData = {};

    this.result.Blocks.forEach((block) => {
      const page = block.Page;
      if (!resultData[page]) resultData[page] = {};

      if (block.BlockType === "PAGE") {
        block.Relationships[0].Ids.forEach((line) => {
          resultData[page][line] = {};
        });
      } else if (block.BlockType.startsWith("LAYOUT_") && block.BlockType !== "LAYOUT_LIST") {
        const blockData = {
          BlockType: block.BlockType,
          Confidence: block.Confidence,
          BoundingBox: block.Geometry.BoundingBox,
          Polygon: block.Geometry.Polygon,
          Children: {}
        };

        if (block.Relationships && block.Relationships.length > 0) {
          block.Relationships[0].Ids.forEach((childId) => {
            blockData.Children[childId] = resultData[page][childId];
            delete resultData[page][childId];
          });
        }

        resultData[page][block.Id] = blockData;
      } else if (block.BlockType === "LINE") {
        const lineData = {
          BlockType: block.BlockType,
          Confidence: block.Confidence,
          Text: block.Text,
          BoundingBox: block.Geometry.BoundingBox,
          Polygon: block.Geometry.Polygon,
          Children: {}
        };

        block.Relationships[0].Ids.forEach((word) => {
          const wordBlock = this.result.Blocks.find((wb) => wb.Id === word);
          if (wordBlock) {
            lineData.Children[word] = {
              BlockType: wordBlock.BlockType,
              Confidence: wordBlock.Confidence,
              Text: wordBlock.Text,
              TextType: wordBlock.TextType,
              BoundingBox: wordBlock.Geometry.BoundingBox,
              Polygon: wordBlock.Geometry.Polygon
            };
          }
        });

        resultData[page][block.Id] = lineData;
      }
    });

    return resultData;
  }

  private getBBOX(data: any, w: number, h: number) {
    return [
      Math.round(data.Left * w),
      Math.round(data.Top * h),
      Math.round((data.Width + data.Left) * w),
      Math.round((data.Height + data.Top) * h)
    ].join(" ");
  }

  getHocr() {
    const resultData = this.multiplePageParse();
    return this.toHocr(resultData);
  }

  private toHocr(resultData: any, w: number = 2550, h: number = 3300) {
    return [
      "<html>",
      "<body>",
      ...Object.keys(resultData).map((page) => {
        const pageContent = resultData[page];
        return [
          '<div class="page">',
          `<div class="ocr_page" id="page_${page}" title="bbox 0 0 ${w} ${h}">`,
          ...Object.keys(pageContent).map((layout) => {
            const layoutData = pageContent[layout];
            const layoutBoundingBox = layoutData.BoundingBox;

            if (!layoutData.Children || Object.keys(layoutData.Children).length === 0) return "";

            if (layoutData.BlockType === "LAYOUT_FIGURE") {
              return `<div class="ocr_image" title="bbox ${this.getBBOX(layoutBoundingBox, w, h)}"></div>`;
            }

            return [
              `<div class="ocr_carea" title="bbox ${this.getBBOX(layoutBoundingBox, w, h)}">`,
              ...Object.keys(layoutData.Children).map((line) => {
                const lineData = layoutData.Children[line];
                const lineBoundingBox = lineData.BoundingBox;
                const lineConfidence = Math.round(lineData.Confidence);

                return [
                  `<p title="bbox ${this.getBBOX(lineBoundingBox, w, h)} ; x_wconf ${lineConfidence}" class="ocr_line">`,
                  ...Object.keys(lineData.Children).map((word) => {
                    const wordData = lineData.Children[word];
                    const wordBoundingBox = wordData.BoundingBox;
                    const wordConfidence = Math.round(wordData.Confidence);

                    return `<span title="bbox ${this.getBBOX(wordBoundingBox, w, h)} ; x_wconf ${wordConfidence}" class="ocrx_word">${wordData.Text} </span>`;
                  }),
                  "</p>"
                ].join("");
              }),
              "</div>"
            ].join("");
          }),
          "</div>",
          "</div>"
        ].join("");
      }),
      "</body>",
      "</html>"
    ].join("");
  }
}
