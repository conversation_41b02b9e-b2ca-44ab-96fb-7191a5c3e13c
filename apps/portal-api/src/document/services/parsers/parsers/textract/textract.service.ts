import {
  GetDocumentAnalysisCommand,
  GetDocumentAnalysisCommandOutput,
  StartDocumentAnalysisCommand,
  TextractClient,
  TextractClientConfig
} from "@aws-sdk/client-textract";
import { Inject, Injectable, Logger } from "@nestjs/common";
import { StorageService } from "../../../../../storage/storage.service";
import { DocumentModuleOptions } from "../../../../document.module";
import { ParserService } from "../../parser.service";
import { ParseResult } from "../../types/types";
import { HocrAdapter } from "./hocr-adapter";

@Injectable()
export class TextractParserService extends ParserService {
  private readonly logger = new Logger(TextractParserService.name);
  private client: TextractClient;

  constructor(
    @Inject("DOCUMENT_MODULE_OPTIONS")
    private readonly options: DocumentModuleOptions,
    @Inject(StorageService)
    private readonly storageService: StorageService
  ) {
    super();

    const config: TextractClientConfig = {
      region: this.options.awsRegion
    };

    if (this.options.awsAccessKey && this.options.awsSecretKey) {
      config.credentials = {
        accessKeyId: this.options.awsAccessKey,
        secretAccessKey: this.options.awsSecretKey
      };
    }

    this.client = new TextractClient(config);
  }

  private async getDocumentAnalysisResult(
    jobId: string,
    nextToken?: string
  ): Promise<GetDocumentAnalysisCommandOutput> {
    const command = new GetDocumentAnalysisCommand({
      JobId: jobId,
      NextToken: nextToken
    });

    return await this.client.send(command);
  }

  /**
   * Analyze a document and wait for the result
   *
   * @param path
   * @returns
   */
  private async analyzeDocument(path: string): Promise<GetDocumentAnalysisCommandOutput> {
    this.logger.debug("Analyzing: " + path);
    const command = new StartDocumentAnalysisCommand({
      DocumentLocation: {
        S3Object: {
          Bucket: this.storageService.getBucket(),
          Name: path
        }
      },
      FeatureTypes: ["LAYOUT"]
    });

    const { JobId } = await this.client.send(command);

    let waitForResult = new Promise<GetDocumentAnalysisCommandOutput>((resolve, reject) => {
      let result: GetDocumentAnalysisCommandOutput;
      const fetchResult = async () => {
        result = await this.getDocumentAnalysisResult(JobId);
        this.logger.log("Textract job status: " + result.JobStatus);
        switch (result.JobStatus) {
          case "IN_PROGRESS":
            setTimeout(fetchResult, 2000);
            break;
          case "FAILED":
            reject(new Error("Textract job failed"));
            break;
          case "PARTIAL_SUCCESS":
            reject(new Error("Textract job partially succeeded"));
            break;
          case "SUCCEEDED": {
            let nextToken = result.NextToken;
            while (nextToken) {
              this.logger.debug("Getting next page of results: " + nextToken);
              const nextResult = await this.getDocumentAnalysisResult(JobId, nextToken);
              result.Blocks.push(...nextResult.Blocks);
              nextToken = nextResult.NextToken;
            }
            resolve(result);
            break;
          }
          default:
            reject(new Error("Textract job status unknown"));
        }
      };

      setTimeout(fetchResult, 2000);
    });

    return waitForResult;
  }

  async getParseResult(path: string): Promise<ParseResult> {
    const result = await this.analyzeDocument(path);
    this.logger.log("Textract result: " + JSON.stringify(result));

    return {
      parser: "textract",
      numPages: result.DocumentMetadata?.Pages || 1,
      data: new HocrAdapter(result).getHocr()
    };
  }
}
