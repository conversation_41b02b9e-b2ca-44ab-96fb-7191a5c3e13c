import {
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  ServiceUnavailableException
} from "@nestjs/common";
import axios from "axios";
import { JSDOM } from "jsdom";
import { StorageService } from "../../../../../storage/storage.service";
import { DocumentModuleOptions } from "../../../../document.module";
import { FileCorruptedError, ParserError } from "../../errors/errors";
import { ParserService } from "../../parser.service";
import { ParseResult } from "../../types/types";

interface TikaParsedResponse {
  "X-TIKA:Parsed-By": string[];
  "X-TIKA:Parsed-By-Full-Set": string[];
  "Content-Encoding": string;
  "X-TIKA:content": string;
  "Content-Length": string;
  "Content-Type-Override": string;
  "Content-Type": string;
  "xmpTPg:NPages": string;
}

const PACKAGE_TESSERACT_OCR_PARSER = "org.apache.tika.parser.ocr.TesseractOCRParser";
const PACKAGE_PDF_PARSER = "org.apache.tika.parser.pdf.PDFParser";

@Injectable()
export class TikaParserService extends ParserService {
  private readonly logger = new Logger(TikaParserService.name);

  constructor(
    @Inject("DOCUMENT_MODULE_OPTIONS")
    private readonly options: DocumentModuleOptions,
    @Inject(StorageService)
    private readonly storageService: StorageService
  ) {
    super();
  }

  async getVersion(): Promise<string> {
    try {
      const response = await axios.get(`${this.options.tikaEndpointUrl}/version`, {
        timeout: 3000
      });
      return response.data;
    } catch (error) {
      if (axios.isCancel(error)) {
        this.logger.error("Tika service is not responding");
        throw new ServiceUnavailableException();
      } else {
        this.logger.error("Error getting Tika version", error);
        throw new InternalServerErrorException();
      }
    }
  }

  async getTikaResponse(fileObject: Blob): Promise<TikaParsedResponse> {
    try {
      const response = await axios.put<TikaParsedResponse>(
        `${this.options.tikaEndpointUrl}/tika`,
        fileObject,
        {
          headers: {
            Accept: "application/json",
            "X-Tika-PDFocrStrategy": "ocr_only",
            "X-Tika-PDFenableAutoSpace": "true",
            "X-Tika-OCROutputType": "hocr",
            "X-Tika-OCRLanguage": "eng+chi_sim",
            "X-Tika-OCRPageSegMode": "3",
            "X-Tika-OCRTimeoutSeconds": 600
          }
        }
      );

      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 422) {
        throw new FileCorruptedError("Apache Tika cannot process the file");
      } else {
        this.logger.error(error);
        throw error;
      }
    }
  }

  async getParseResult(path: string): Promise<ParseResult> {
    this.logger.debug("Extracting: " + path);
    const fileObject = await this.storageService.getFileBlob(path);
    const tikaResponse = await this.getTikaResponse(fileObject);
    const content = this.getContent(tikaResponse);

    return {
      parser: "tika",
      numPages: tikaResponse["xmpTPg:NPages"]
        ? parseInt(tikaResponse["xmpTPg:NPages"], 10)
        : this.inferNumPages(content),
      data: content
    };
  }

  private getContent(response: TikaParsedResponse): string {
    if (!response["X-TIKA:content"]) {
      throw new ParserError("No content found in response");
    }

    if (!response["X-TIKA:Parsed-By-Full-Set"]) {
      throw new ParserError("No parser found in response");
    }

    // fix for tesseract ocr and pdf parser
    if (
      response["X-TIKA:Parsed-By-Full-Set"].includes(PACKAGE_TESSERACT_OCR_PARSER) &&
      response["X-TIKA:Parsed-By-Full-Set"].includes(PACKAGE_PDF_PARSER)
    ) {
      return this.fixPdfProcessedContent(response["X-TIKA:content"]);
    }

    return response["X-TIKA:content"];
  }

  private inferNumPages(content: string): number {
    this.logger.verbose("Inferring number of pages");
    let dom: JSDOM | null = null;
    try {
      dom = new JSDOM(content);
      const pages = dom.window.document.querySelectorAll(".page").length;
      return pages > 0 ? pages : 1;
    } catch (error) {
      this.logger.warn("Failed to parse content to infer number of pages", error);
      return 1;
    } finally {
      if (dom) {
        dom.window.close();
      }
    }
  }

  /**
   * Fix for result from tesseract ocr and pdf parser
   *
   * @param extractionResponse
   * @returns
   */
  private fixPdfProcessedContent(content: string): string {
    this.logger.debug("Fixing pdf processed content");
    const dom = new JSDOM(content);

    dom.window.document.querySelectorAll(".page").forEach((page) => {
      dom.window.document.body.appendChild(page);
    });

    return dom.serialize();
  }
}
