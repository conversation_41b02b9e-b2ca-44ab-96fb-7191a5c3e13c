export abstract class ParseResultReader {
  protected numPages: number = 0;

  abstract getPages(startPage?: number, endPage?: number): Array<any>;
  abstract getRawPages(startPage?: number, endPage?: number): Array<any>;

  protected getSliceRange(startPage?: number, endPage?: number) {
    const start = startPage ? startPage - 1 : 0;
    const end = endPage ? endPage : this.numPages;

    return { start, end };
  }

  protected setNumPages(numPages: number) {
    this.numPages = numPages;
  }

  public getNumPages() {
    return this.numPages;
  }
}
