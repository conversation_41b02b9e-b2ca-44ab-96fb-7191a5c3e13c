import { LlamaParse, LlamaParseResult } from "..";
import { ParseResultReader } from "./parse-result.reader";

export interface ParsedPage {
  page: number;
  text: string;
  barcodes: string[];
}

export class LlamaParseResultReader extends ParseResultReader {
  static load(llamaParseResult: LlamaParseResult): LlamaParseResultReader {
    return new LlamaParseResultReader(llamaParseResult);
  }

  constructor(private readonly llamaParseResult: LlamaParseResult) {
    super();
    this.setNumPages(llamaParseResult.pages.length);
  }

  getRawPages(startPage?: number, endPage?: number): Array<LlamaParse.Page> {
    const range = this.getSliceRange(startPage, endPage);

    return this.llamaParseResult.pages.slice(range.start, range.end);
  }

  getPages(startPage?: number, endPage?: number): Array<ParsedPage> {
    const range = this.getSliceRange(startPage, endPage);

    return this.llamaParseResult.pages.slice(range.start, range.end).map((page) => {
      return {
        page: page.page,
        text: page.md,
        barcodes: ((page as any).barcodes ?? []).map((barcode: any) => barcode.text)
      };
    });
  }

  getPageBarcodes(page: number): string[] {
    const pageData = this.llamaParseResult.pages.find((p) => p.page === page);

    if (!pageData) {
      return [];
    }

    return ((pageData as any).barcodes ?? []).map((barcode: any) => barcode.text);
  }

  async getPageScreenshotName(page: number) {
    const pageData = this.llamaParseResult.pages.find((p) => p.page === page);

    if (!pageData) {
      return null;
    }

    const image = pageData.images[0];

    if (!image) {
      return null;
    }

    return image.name;
  }
}
