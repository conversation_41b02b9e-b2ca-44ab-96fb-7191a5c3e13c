import * as fs from "fs";
import * as path from "path";
import { LlamaParseResult } from "..";
import { LlamaParseResultReader } from "./llama-parse-result.reader";

describe("LlamaParseResultReader", () => {
  let llamaParseResult: LlamaParseResult;
  let reader: LlamaParseResultReader;

  beforeEach(() => {
    const fixturePath = path.resolve(__dirname, "fixtures/llama-result.json");
    const fixtureData = fs.readFileSync(fixturePath, "utf-8");
    llamaParseResult = JSON.parse(fixtureData) as LlamaParseResult;
    reader = LlamaParseResultReader.load(llamaParseResult);
  });

  describe("load", () => {
    it("should create an instance of LlamaParseResultReader", () => {
      expect(reader).toBeInstanceOf(LlamaParseResultReader);
    });
  });

  describe("constructor", () => {
    it("should set the number of pages correctly", () => {
      expect(reader.getNumPages()).toBe(llamaParseResult.pages.length);
    });
  });

  describe("getRawPages", () => {
    it("should return all pages when no range is specified", () => {
      const pages = reader.getRawPages();
      expect(pages).toEqual(llamaParseResult.pages);
    });

    it("should return pages within the specified range", () => {
      const startPage = 2;
      const endPage = 2;
      const expectedPages = [llamaParseResult.pages[1]];
      const pages = reader.getRawPages(startPage, endPage);
      expect(pages).toEqual(expectedPages);
    });

    it("should handle ranges outside the bounds gracefully", () => {
      const startPage = -1;
      const endPage = llamaParseResult.pages.length + 10;
      const expectedPages = llamaParseResult.pages.slice(0, llamaParseResult.pages.length);
      const pages = reader.getRawPages(startPage, endPage);
      expect(pages).toEqual(expectedPages);
    });
  });

  describe("getPages", () => {
    it("should return all pages with page numbers and text when no range is specified", () => {
      const expectedPages = llamaParseResult.pages.map((page) => ({
        page: page.page,
        text: page.text
      }));
      const pages = reader.getPages();
      expect(pages).toEqual(expectedPages);
    });

    it("should return pages within the specified range with page numbers and text", () => {
      const startPage = 1;
      const endPage = 1;
      const expectedPages = [
        {
          page: llamaParseResult.pages[0].page,
          text: llamaParseResult.pages[0].text
        }
      ];
      const pages = reader.getPages(startPage, endPage);
      expect(pages).toEqual(expectedPages);
    });

    it("should handle empty ranges correctly", () => {
      const startPage = 5;
      const endPage = 5;
      const pages = reader.getPages(startPage, endPage);
      expect(pages).toEqual([]);
    });
  });
});
