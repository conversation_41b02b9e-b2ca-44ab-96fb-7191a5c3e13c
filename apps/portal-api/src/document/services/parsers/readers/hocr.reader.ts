import { Logger } from "@nestjs/common";
import { <PERSON><PERSON><PERSON> } from "jsdom";
import { ParseResultReader } from "./parse-result.reader";

export class Hocr<PERSON>eader extends ParseResultReader {
  private document: Document;
  private readonly logger = new Logger(HocrReader.name);

  constructor(private readonly hocr: string) {
    super();
    const dom = new JSDOM(this.hocr);
    this.document = dom.window.document;
  }

  static load(hocr: string): HocrReader {
    return new HocrReader(hocr);
  }

  /**
   * Get all pages in the document
   *
   * @returns An array of pages
   */
  private _getPagesElements(): Array<Element> {
    let pages: NodeListOf<Element> = this.document.querySelectorAll("body > .page");

    if (pages.length == 0) {
      pages = this.document.querySelectorAll("body");
    }

    this.logger.verbose("Number of pages: " + pages.length);

    this.setNumPages(pages.length);

    return Array.from(pages);
  }

  /**
   * Get all pages in the document
   *
   * @returns An array of pages
   */
  getRawPages(startPage?: number, endPage?: number): Array<string> {
    const pages = this._getPagesElements();
    const range = this.getSliceRange(startPage, endPage);

    return pages.slice(range.start, range.end).map((page) => page.innerHTML);
  }

  getPages(startPage?: number, endPage?: number): Array<string> {
    const pages = this._getPagesElements();
    const range = this.getSliceRange(startPage, endPage);

    return pages.slice(range.start, range.end).map((page) => this.getSimplifiedPage(page));
  }

  private getSimplifiedPage(element: Element): string {
    // const elementClone = new JSDOM(element.innerHTML);
    const elementClone = element.cloneNode(true) as Element;
    // elementClone.window.document.querySelectorAll('.ocr').forEach(e => {
    //   e.innerHTML = this.getSimplifiedOcr(e);
    // });
    // return elementClone.window.document.body.innerHTML;

    elementClone.querySelectorAll(".ocr_page").forEach((e) => {
      e.innerHTML = this.getSimplifiedOcr(e);
    });

    return elementClone.innerHTML;
  }

  private getSimplifiedOcr(element: Element): string {
    const fragment = JSDOM.fragment(element.innerHTML);
    return Array.from(fragment.querySelectorAll(".ocr_line"))
      .map((p) => {
        p.innerHTML = p.textContent.replace(/\s+/g, " ").trim();
        // p.innerHTML = [
        //   ...Array.from(p.querySelectorAll('.ocr_line')),
        //   ...Array.from(p.querySelectorAll('.ocr_caption'))
        // ].map(
        //     l => {
        //       l.innerHTML = l.textContent.replace(/\s+/g, ' ').trim();
        //       l.removeAttribute('id');
        //       l.removeAttribute('title');
        //       l.removeAttribute('lang');
        //       l.removeAttribute('bbox');
        //       l.removeAttribute('class');
        //       return l.outerHTML;
        //   }).join('\n');
        p.removeAttribute("id");
        p.removeAttribute("title");
        p.removeAttribute("lang");
        p.removeAttribute("bbox");
        p.removeAttribute("class");
        return p.outerHTML;
      })
      .join("");
  }
}
