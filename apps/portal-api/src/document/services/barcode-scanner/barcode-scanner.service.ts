import { Injectable } from "@nestjs/common";
import { readFileSync } from "fs";
import sharp from "sharp";
import { prepareZXingModule, readBarcodes, ReaderOptions, ReadResult } from "zxing-wasm/reader";

@Injectable()
export class BarcodeScannerService {
  private readonly readerOptions: ReaderOptions = {
    formats: ["Code39", "Code128"],
    characterSet: "ASCII",
    tryRotate: true,
    tryHarder: true
  };

  constructor() {
    const path = require.resolve("zxing-wasm/reader/zxing_reader.wasm");
    const wasmBuffer = readFileSync(path).buffer as ArrayBuffer;

    prepareZXingModule({
      overrides: {
        wasmBinary: wasmBuffer
      }
    });
  }

  async upscaleImage(image: Buffer): Promise<Buffer> {
    const sharpImage = sharp(image);
    // resize to 2x the original size
    const { width, height } = await sharpImage.metadata();

    const imageBuffer = await sharpImage
      .resize(width * 2, height * 2, {
        kernel: "lanczos3"
      })
      .toBuffer();

    return imageBuffer;
  }

  async decodeImage(image: Buffer | ArrayBuffer | Blob | ImageData): Promise<ReadResult[]> {
    let results = [];

    // try to decode the image
    results = await readBarcodes(image, this.readerOptions);

    // the reader returns empty result sometimes, so we need to filter them out
    return results.filter((result) => result.text.length > 0);
  }
}
