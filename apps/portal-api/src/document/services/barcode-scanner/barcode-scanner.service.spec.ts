import { Test, TestingModule } from "@nestjs/testing";
import { readFileSync } from "fs";
import path from "path";
import { BarcodeScannerService } from "./barcode-scanner.service";

describe("BarcodeScannerService", () => {
  let service: BarcodeScannerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [BarcodeScannerService]
    }).compile();

    service = module.get<BarcodeScannerService>(BarcodeScannerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should decode image and return barcodes", async () => {
    const imageBuffer = readFileSync(path.join(__dirname, "fixtures", "needs-upscale.jpg"));

    const mockReadResults = [{ text: "19UL PARS 1035300", format: "Code128" }];

    const results = await service.decodeImage(imageBuffer);

    for (const [index, result] of results.entries()) {
      expect(result.text).toBe(mockReadResults[index].text);
      expect(result.format).toBe(mockReadResults[index].format);
    }
  });
});
