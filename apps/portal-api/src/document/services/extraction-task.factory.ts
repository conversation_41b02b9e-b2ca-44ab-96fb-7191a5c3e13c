import { OpenAIService } from "@/llm/openai.service";
import { Inject, Injectable } from "@nestjs/common";
import { AbstractTask } from "./openai/abstract.task";
import { DocumentExtractionConfig } from "./openai/document-extraction-config";
import { DocumentExtractionResult } from "./openai/dto";
import { OpenAIExtractTaskError } from "./openai/errors";
import { ExtractArrayTask } from "./openai/extract-array.task";
import { ExtractTask } from "./openai/extract.task";

@Injectable()
export class ExtractionTaskFactory {
  constructor(
    @Inject(OpenAIService)
    private readonly openaiService: OpenAIService
  ) {}

  /**
   * Extract data from the document
   *
   * @deprecated
   * @param pages
   * @param config
   * @param model
   * @param image
   */
  createExtractionTask(pages: string[], config: DocumentExtractionConfig, model: string): AbstractTask {
    if (pages.length === 0) {
      throw new OpenAIExtractTaskError("No pages to extract data from");
    }

    let task: ExtractTask | ExtractArrayTask;

    if ((config.type === "COMMERCIAL_INVOICE" || config.type === "PACKING_LIST") && pages.length > 2) {
      task = new ExtractArrayTask(this.openaiService);
    } else {
      task = new ExtractTask(this.openaiService);
    }

    return task.setModel(model).setPages(pages).setConfig(config);
  }

  async execute(
    pages: string[],
    config: DocumentExtractionConfig,
    model: string,
    image?: Blob
  ): Promise<DocumentExtractionResult> {
    const task = this.createExtractionTask(pages, config, model);

    return task.execute();
  }
}
