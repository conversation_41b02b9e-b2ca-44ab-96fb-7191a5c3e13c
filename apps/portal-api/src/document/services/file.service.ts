import { Inject, Injectable, InternalServerErrorException, Logger, NotFoundException } from "@nestjs/common";

import {
  AuthenticatedRequest,
  File,
  FILE_ENUM_KEYS,
  FileBatch,
  FileBatchCreator,
  FileColumn,
  FilePage,
  FileStatus,
  FIND_FILE_RELATIONS,
  GetFilesDto,
  GetFilesResponseDto,
  getFindOptions,
  Importer,
  Organization,
  Shipment,
  SortOrder,
  UserPermission
} from "nest-modules";

import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import { DataSource, IsNull, Not, QueryRunner, Repository } from "typeorm";
import { StorageService } from "../../storage/storage.service";
import { inOrg } from "../helpers/scoped-query.helper";
import { ParseResult } from "./parsers";
import { getDestinationFolder, getUniqueNameForFile } from "./utils/file-upload.utils";

import { EventEmitter2 } from "@nestjs/event-emitter";
import archiver from "archiver";
import { createHash } from "crypto";
import { FILE_BATCH_CREATED, FileBatchCreatedEvent } from "nest-modules";
import { CLS_REQ } from "nestjs-cls";
import path from "path";
import { fromEvent, map, Observable, startWith, takeWhile } from "rxjs";
import { Readable } from "stream";
import { v4 as uuidv4 } from "uuid";
import { FILE_STATUS_UPDATED_EVENT } from "../events/file-status-updated.event";

type PartialFileRecord = {
  name: string;
  path: string;
  mimeType: string;
  hash: string;
  shipmentId?: number;
  batchId: string;
};

@Injectable()
export class FileService {
  private readonly logger = new Logger(FileService.name);

  constructor(
    // cls request
    @Inject(CLS_REQ)
    private readonly request: AuthenticatedRequest,

    // services
    @Inject(StorageService)
    private readonly storageService: StorageService,
    @Inject(EventEmitter2)
    private readonly eventEmitter: EventEmitter2,

    // repositories
    @InjectRepository(FileBatch)
    private readonly fileBatchRepository: Repository<FileBatch>,
    @InjectRepository(Shipment)
    private readonly shipmentRepository: Repository<Shipment>,
    @InjectRepository(Importer)
    private readonly importerRepository: Repository<Importer>,
    @InjectRepository(FilePage)
    private readonly filePageRepository: Repository<FilePage>,
    @InjectRepository(File)
    private readonly fileRepository: Repository<File>,

    // data source
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  /**
   * Save email attachments to the storage and create a file batch
   *
   * @param emailId The ID of the email
   * @param files The email attachments
   * @param shipmentId The shipment ID to associate the files with
   * @returns The created file batch
   */
  async saveEmailAttachments(
    emailId: string,
    files: Array<Express.Multer.File>,
    metadata: {
      organizationId: number;
      shipmentId?: number;
    }
  ): Promise<{
    files: File[];
    batchId: string;
    shipmentId?: number;
  }> {
    return await this.uploadFiles(files, {
      ...metadata,
      batchId: emailId,
      creator: FileBatchCreator.EMAIL
    });
  }

  /**
   * Upload files to the storage and create a file batch
   *
   * @param files The files to upload
   * @param metadata The metadata for the file batch
   * @returns The created file batch
   */
  async uploadFiles(
    files: Array<Express.Multer.File>,
    metadata: {
      shipmentId?: number;
      organizationId?: number;
      batchId?: string;
      creator?: FileBatchCreator;
    } = {}
  ): Promise<{
    files: File[];
    batchId: string;
    shipmentId?: number;
  }> {
    const organizationId = metadata.organizationId;

    const destinationFolder = getDestinationFolder(organizationId);

    const { shipmentId, creator = FileBatchCreator.API } = metadata;
    let { batchId } = metadata;

    batchId = batchId || uuidv4();

    await this.createFileBatch({
      batchId,
      shipmentId,
      creator,
      organizationId
    });

    const uploadedFiles = await Promise.all(
      files.map(async (file, index) => {
        const originalFilename = file.originalname;
        const fileName = getUniqueNameForFile(originalFilename, index);
        const filePath = `${destinationFolder}/${fileName}`;
        const mimeType = file.mimetype;
        const hash = createHash("sha256").update(new Uint8Array(file.buffer)).digest("hex");

        try {
          await this.storageService.uploadFile(file, filePath);
          return await this.createFileRecord(
            {
              name: originalFilename,
              path: filePath,
              mimeType,
              hash,
              batchId,
              shipmentId
            },
            organizationId
          );
        } catch (error) {
          this.logger.error(`Error uploading file ${originalFilename}: ${error}`);
          return null;
        }
      })
    );

    const successfullyUploadedFiles = uploadedFiles.filter((file) => file !== null);

    // Only emit FILE_BATCH_CREATED for API uploads, not EMAIL uploads
    // EMAIL uploads will be triggered by core-agent after shipment identification
    if (creator === FileBatchCreator.API) {
      this.eventEmitter.emit(
        FILE_BATCH_CREATED,
        new FileBatchCreatedEvent(
          batchId,
          organizationId,
          successfullyUploadedFiles.map((file) => file.id),
          shipmentId
        )
      );
    }

    return {
      files: successfullyUploadedFiles,
      batchId,
      shipmentId
    };
  }

  async moveFilesToOrganization(
    files: File[],
    organizationId: number,
    queryRunner?: QueryRunner
  ): Promise<void> {
    queryRunner = queryRunner || this.dataSource.createQueryRunner();
    const isTransactionOwner = !queryRunner;
    await queryRunner.startTransaction();

    const moved: { source: string; destination: string }[] = [];

    try {
      for (const file of files) {
        // resolve path
        const sourcePath = file.path;
        const { name: sourceKey } = path.parse(sourcePath);
        const destinationFolder = getDestinationFolder(organizationId);
        const destinationPath = `${destinationFolder}/${sourceKey}`;

        // perform copy
        await this.storageService.copyFile(sourcePath, destinationPath);

        await queryRunner.manager.getRepository(File).update(file.id, {
          organization: {
            id: organizationId
          },
          path: destinationPath
        });

        moved.push({ source: sourcePath, destination: destinationPath });
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();

      this.logger.error(`Error moving files to organization: ${error.message}`, error.cause, error.stack);

      // clean up moved files
      while (moved.length > 0) {
        const { destination } = moved.pop();
        try {
          await this.storageService.deleteFile(destination);
        } catch (error) {
          this.logger.error(`Error deleting moved file: ${error.message}`, error.cause, error.stack);
        }
      }

      throw error;
    } finally {
      if (isTransactionOwner) {
        await queryRunner.release();
      }
    }

    // clean up source files
    while (moved.length > 0) {
      const { source } = moved.pop();
      try {
        await this.storageService.deleteFile(source);
      } catch (error) {
        this.logger.error(`Error deleting source file: ${error.message}`, error.cause, error.stack);
      }
    }
  }

  private async createFileBatch({
    batchId,
    shipmentId,
    creator = FileBatchCreator.API,
    organizationId
  }: {
    batchId: string;
    shipmentId?: number;
    creator: FileBatchCreator;
    organizationId?: number;
  }) {
    const batch = new FileBatch();
    batch.id = batchId;
    batch.shipment = shipmentId ? await this.shipmentRepository.findOne({ where: { id: shipmentId } }) : null;
    batch.creator = creator;
    batch.organizationId = organizationId;
    return await this.fileBatchRepository.save(batch);
  }

  async getShipmentFiles(shipmentId: number): Promise<File[]> {
    return await this.fileRepository.find(
      inOrg(
        {
          where: {
            documents: {
              shipment: {
                id: shipmentId
              }
            }
          }
        },
        this.request.user,
        true
      )
    );
  }

  async downloadShipmentFiles(shipmentId: number): Promise<Readable> {
    const files = await this.getShipmentFiles(shipmentId);

    const archive = archiver("zip", {
      zlib: { level: 9 } // Sets the compression level.
    });

    // Handle archive events
    archive.on("warning", (err) => {
      if (err.code === "ENOENT") {
        // log warning
      } else {
        throw err;
      }
    });

    archive.on("error", (err) => {
      throw err;
    });

    // Append files to the archive
    for (const file of files) {
      const fileStream = await this.storageService.getFileStream(file.path);
      archive.append(fileStream, { name: file.name });
    }

    // Finalize the archive
    archive.finalize();

    return archive;
  }

  async getFiles(getFilesDto: GetFilesDto): Promise<GetFilesResponseDto> {
    // scope to the organization of the user
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      getFilesDto.organizationId = this.request?.user?.organization?.id || -1;

    if (!getFilesDto.sortBy || !getFilesDto.sortOrder) {
      getFilesDto.sortBy = FileColumn.lastEditDate;
      getFilesDto.sortOrder = SortOrder.DESC;
    }

    const hasOrphanDocuments = getFilesDto.hasOrphanDocuments ?? null;
    delete getFilesDto.hasOrphanDocuments;
    const shipmentId = getFilesDto.shipmentId ?? null;
    delete getFilesDto.shipmentId;

    const { where, order, skip, take } = getFindOptions(getFilesDto, FILE_ENUM_KEYS, ["name"], FileColumn.id);

    if (hasOrphanDocuments !== null) {
      if (hasOrphanDocuments) {
        where["documents"] = {
          shipmentId: IsNull()
        };
      } else {
        where["documents"] = {
          shipmentId: Not(IsNull())
        };
      }
    }

    if (shipmentId) {
      where["documents"] = {
        shipmentId: shipmentId
      };
    }

    const [files, total] = await this.fileRepository.findAndCount({
      where,
      order,
      skip,
      take,
      relations: {
        ...FIND_FILE_RELATIONS
      },
      select: {
        id: true,
        documents: {
          id: true,
          name: true,
          documentType: {
            id: true,
            name: true
          },
          shipment: {
            id: true,
            hblNumber: true
          },
          createDate: true,
          lastEditDate: true,
          status: true
        },
        name: true,
        mimeType: true,
        status: true,
        createDate: true,
        lastEditDate: true,
        createdBy: {
          id: true,
          name: true
        },
        lastEditedBy: {
          id: true,
          name: true
        },
        organization: {
          id: true,
          name: true
        }
      }
    });

    return {
      files,
      total,
      skip,
      limit: take
    };
  }

  async getFile(id: number): Promise<File> {
    const file = await this.fileRepository.findOne({
      where: {
        id
      },
      relations: {
        ...FIND_FILE_RELATIONS,
        documents: {
          documentType: true,
          pages: true
        },
        pages: true
      },
      order: {
        documents: {
          startPage: "ASC",
          pages: {
            documentPage: "ASC"
          }
        }
      }
    });
    if (!file) {
      throw new NotFoundException("File not found");
    }
    return file;
  }

  /**
   * Update the parse result of a file
   *
   * @param id The ID of the file to update
   * @param parseResult The parse result to update the file with
   * @returns The updated file
   */
  async updateFileParseResult(id: number, parseResult: ParseResult): Promise<void> {
    const result = await this.fileRepository.update(id, {
      parseResult: parseResult.data,
      numPages: parseResult.numPages,
      parser: parseResult.parser
    });

    // for each page in the parse result, create a file page
    const pages = parseResult.data.pages.map((page: any) => {
      const filePage = new FilePage();
      filePage.page = page.page;
      filePage.md = page.md;
      filePage.fileId = id;
      return filePage;
    });

    await this.filePageRepository.upsert(pages, {
      conflictPaths: ["page", "fileId"]
    });

    if (!result.affected) {
      throw new NotFoundException("Failed to update file parse result");
    }
  }

  /**
   * Update the status of a file
   *
   * @param id The ID of the file to update
   * @param status The new status
   */
  async updateFileStatus(id: number, status: FileStatus) {
    const file = await this.getFile(id);

    const updateResult = await this.fileRepository.update(
      { id },
      {
        status: status
      }
    );

    if (!updateResult.affected) {
      throw new NotFoundException("Failed to update file status");
    }

    this.eventEmitter.emit(FILE_STATUS_UPDATED_EVENT, {
      fileId: file.id,
      organizationId: file.organizationId,
      batchId: file.batchId,
      status
    });

    // FIXME: this is for sse api inside the controller, try move it to other place.
    this.eventEmitter.emit(`file.status-updated:${id}`, {
      status
    });
  }

  async getFileTemporaryUrl(id: number): Promise<string> {
    const file = await this.getFile(id);

    return this.storageService.getSignedUrl(file.path, { name: file.name });
  }

  /**
   * Get the path of a file
   *
   * @deprecated use getFileInfo instead
   * @param fileId The ID of the file
   * @returns The path of the file
   */
  async getFilePath(fileId: number): Promise<string> {
    // todo: refactor this using select query
    const file = await this.getFile(fileId);
    return file.path;
  }

  async getFileInfo(fileId: number): Promise<File> {
    const file = await this.fileRepository.findOne({
      where: { id: fileId },
      select: {
        name: true,
        path: true,
        mimeType: true,
        hash: true
      }
    });

    return file;
  }

  async getFileBlob(fileId: number): Promise<Blob> {
    try {
      const fileEntity = await this.getFile(fileId);

      const fileBlob = await this.storageService.getFileBlob(fileEntity.path);
      this.logger.debug("File object retrieved, size: " + fileBlob.size);
      return fileBlob;
    } catch (error) {
      this.logger.error(`Error getting file ${fileId}`, error);
      throw error;
    }
  }

  async deleteFile(id: number): Promise<{ success: boolean }> {
    const file = await this.getFile(id);

    try {
      await this.storageService.deleteFile(file.path);
      await this.fileRepository.delete({ id });
    } catch (error) {
      this.logger.error(`Error deleting file ${file.name}: ${error}`);
      throw new InternalServerErrorException("Error deleting file");
    }

    return { success: true };
  }

  /**
   * Create a file record in the database
   *
   * @param filename The name of the file
   * @param path The path to the file
   */
  private async createFileRecord(fileRecord: PartialFileRecord, organizationId: number): Promise<File> {
    const newFile = new File();
    newFile.name = fileRecord.name;
    newFile.organization = { id: organizationId } as Organization;
    try {
      newFile.createdBy = this.request.user;
      newFile.lastEditedBy = this.request.user;
    } catch (error) {
      this.logger.warn(error.message);
    }

    newFile.path = fileRecord.path;
    newFile.mimeType = fileRecord.mimeType;
    newFile.batchId = fileRecord.batchId;
    newFile.hash = fileRecord.hash;

    if (fileRecord.shipmentId) {
      try {
        newFile.shipment = await this.shipmentRepository.findOne({ where: { id: fileRecord.shipmentId } });
      } catch (error) {
        this.logger.error(
          `Failed to associate file with shipment ${fileRecord.shipmentId}: ${error.message}`
        );
        throw new NotFoundException(`Shipment with ID ${fileRecord.shipmentId} not found`);
      }
    }

    return await this.fileRepository.save(newFile);
  }

  async subscribeToFileProcessingProgress(id: number): Promise<Observable<MessageEvent>> {
    const file = await this.getFile(id);

    return fromEvent(this.eventEmitter, `file.status-updated:${file.id}`).pipe(
      startWith({ status: file.status }),
      map(
        (payload: { status: FileStatus }) =>
          ({
            data: payload
          }) as MessageEvent<{ status: FileStatus }>
      ),
      takeWhile((event: MessageEvent<{ status: FileStatus }>) => file.isProcessing(), true)
    );
  }
}
