import { FieldsToObjectAdapter } from "@/aggregation/adapter/fields-to-object.adapter";
import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Document, DocumentField, DocumentFieldDataType } from "nest-modules";
import { Repository } from "typeorm";
import { DocumentType, TYPE_SCHEMA_MAP } from "../types";

@Injectable()
export class DocumentFieldService {
  constructor(
    @InjectRepository(DocumentField)
    private readonly documentFieldRepository: Repository<DocumentField>,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>
  ) {}

  /**
   * Get the value of a document field by document key and field key.
   *
   * @deprecated
   * @param documentId - The id of the document.
   * @param fieldKey - The key of the field.
   * @returns The value of the field.
   */
  async getFieldValue<T extends DocumentFieldDataType>(
    documentId: number,
    fieldKey: string
  ): Promise<T | null> {
    const record = await this.documentFieldRepository.findOne({
      where: {
        document: {
          id: documentId
        },
        name: fieldKey
      }
    });

    if (!record) {
      return null;
    }

    return this.convertToType<T>(record);
  }

  /**
   * Get the data of a document by document id.
   *
   * @param documentId - The id of the document.
   * @returns The data of the document and the document type name.
   */
  async getDocumentData<T>(documentId: number): Promise<{
    documentType: DocumentType | null;
    data: T;
  }> {
    const document = await this.documentRepository.findOne({
      where: { id: documentId },
      relations: ["fields", "documentType"],
      select: {
        id: true,
        name: true,
        documentType: {
          name: true
        },
        fields: {
          id: true,
          name: true,
          value: true,
          dataType: true
        }
      }
    });

    if (!document) {
      throw new NotFoundException("Document not found");
    }

    const documentData = new FieldsToObjectAdapter(document.fields).toObject();

    if (!document.documentType?.name) {
      return {
        documentType: null,
        data: documentData
      };
    }

    const documentType = document.documentType.name as DocumentType;

    const zodSchema = TYPE_SCHEMA_MAP[documentType];

    return {
      documentType,
      data: zodSchema.parse(documentData)
    };
  }

  /**
   * Convert the value of a document field to the specified type.
   *
   * @param record - The document field record.
   * @returns The converted value.
   */
  private async convertToType<T extends DocumentFieldDataType>(record: DocumentField): Promise<T> {
    let value: any = record.value;
    // check record.dataType
    switch (record.dataType) {
      case DocumentFieldDataType.STRING:
        // we don't need to convert string
        break;
      case DocumentFieldDataType.NUMBER:
        value = Number(value);
        break;
      case DocumentFieldDataType.BOOLEAN:
        value = value === "true";
        break;
      case DocumentFieldDataType.DATETIME:
        value = new Date(value as string);
        break;
      case DocumentFieldDataType.OBJECT:
      case DocumentFieldDataType.ARRAY:
        value = JSON.parse(value as string);
        break;
      default:
        throw new Error(`Unsupported data type: ${record.dataType}`);
    }

    return value as T;
  }
}
