import { AggregationEvents, AggregationSuccessEvent } from "@/aggregation/events/aggregation.event";
import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  MessageEvent,
  NotFoundException
} from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import {
  Document,
  File,
  FILE_BATCH_ENUM_KEYS,
  FileBatch,
  FileBatchColumn,
  FileBatchCreator,
  FileBatchStatus,
  GetFileBatchesDto,
  GetFileBatchesResponseDto,
  getFindOptions
} from "nest-modules";
import { Observable } from "rxjs";
import { DataSource, EntityNotFoundError, FindOptionsRelations, QueryRunner, Repository } from "typeorm";
import { BatchDocumentsExtractedEvent, FileBatchEvent } from "../events";
import { DocumentService } from "./document.service";
import { FileService } from "./file.service";
import { SseEventService } from "./sse-event.service";

@Injectable()
export class FileBatchService {
  private readonly logger = new Logger(FileBatchService.name);

  constructor(
    @InjectRepository(FileBatch)
    private readonly fileBatchRepository: Repository<FileBatch>,
    @Inject(FileService)
    private readonly fileService: FileService,
    @Inject(SseEventService)
    private readonly sseEventService: SseEventService,
    @Inject(DocumentService)
    private readonly documentService: DocumentService,
    @Inject(EventEmitter2)
    private readonly eventEmitter: EventEmitter2,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  async getFileBatch(id: string): Promise<FileBatch> {
    return this.fileBatchRepository.findOneOrFail({ where: { id } });
  }

  /**
   * Get the status of a file batch
   *
   * @param id The ID of the file batch
   * @returns The status of the file batch
   */
  async getFileBatchStatus(id: string, queryRunner?: QueryRunner): Promise<FileBatchStatus> {
    const repository = queryRunner?.manager.getRepository(FileBatch) || this.fileBatchRepository;
    const fileBatch = await repository.findOneOrFail({ where: { id }, select: { status: true } });
    return fileBatch.status;
  }

  async lockForUpdate(id: string, queryRunner?: QueryRunner) {
    const repository = queryRunner?.manager.getRepository(FileBatch) || this.fileBatchRepository;
    const fileBatch = await repository.findOneOrFail({
      where: { id },
      select: { status: true },
      lock: { mode: "pessimistic_write" }
    });
    return fileBatch;
  }

  async aggregateFileBatch(id: string) {
    const fileBatch = await this.fileBatchRepository.findOneOrFail({
      where: { id },
      relations: { files: { documents: true } },
      select: {
        id: true,
        organizationId: true,
        files: {
          id: true,
          documents: {
            id: true,
            name: true,
            status: true
          }
        }
      }
    });

    if (fileBatch.status === FileBatchStatus.AGGREGATING) {
      return;
    }

    const documents = fileBatch.files.flatMap((file) => file.documents);

    this.eventEmitter.emit(
      FileBatchEvent.DOCUMENT_EXTRACTED,
      new BatchDocumentsExtractedEvent({
        batchId: fileBatch.id,
        organizationId: fileBatch.organizationId,
        audience: FileBatchCreator.API,
        documentIds: documents.map((document) => document.id),
        documents: documents.map((document) => ({
          id: document.id,
          name: document.name,
          status: document.status
        }))
      })
    );
  }

  async getFileBatches(getFileBatchesDto: GetFileBatchesDto): Promise<GetFileBatchesResponseDto> {
    const { where, skip, order, take } = getFindOptions(
      getFileBatchesDto,
      FILE_BATCH_ENUM_KEYS,
      [],
      FileBatchColumn.id
    );

    const [fileBatches, total] = await this.fileBatchRepository.findAndCount({
      where,
      order,
      skip,
      take,
      relations: {
        organization: true,
        files: {
          documents: {
            aggregation: true
          }
        },
        shipment: true,
        documentAggregations: true
      },
      select: {
        id: true,
        createDate: true,
        lastEditDate: true,
        creator: true,
        status: true,
        shipment: {
          id: true,
          hblNumber: true,
          cargoControlNumber: true
        },
        organization: {
          id: true
        },
        files: {
          id: true,
          createDate: true,
          lastEditDate: true,
          name: true,
          numPages: true,
          status: true,
          mimeType: true,
          batchId: true,
          documents: {
            id: true,
            name: true,
            createDate: true,
            lastEditDate: true,
            startPage: true,
            endPage: true,
            status: true,
            shipmentId: true,
            aggregation: {
              id: true,
              action: true,
              status: true,
              targetId: true,
              targetType: true
            }
          }
        },
        documentAggregations: {
          id: true
        }
      }
    });

    return {
      fileBatches,
      total,
      skip,
      limit: take
    };
  }

  /**
   * Get the details of a file batch
   *
   * @param id The ID of the file batch
   * @param orgId The ID of the organization
   * @returns The file batch details
   */
  async getFileBatchDetails(id: string, orgId: number): Promise<FileBatch> {
    try {
      const fileBatch = await this.fileBatchRepository.findOneOrFail({
        where: { id, organization: { id: orgId } },
        relations: {
          files: {
            documents: {
              aggregation: true
            }
          },
          shipment: true,
          documentAggregations: true
        },
        order: {
          files: {
            documents: {
              startPage: "ASC"
            }
          },
          documentAggregations: {
            createDate: "DESC"
          }
        },
        select: {
          id: true,
          createDate: true,
          lastEditDate: true,
          creator: true,
          organization: {
            id: true
          },
          status: true,
          shipment: {
            id: true,
            hblNumber: true,
            cargoControlNumber: true
          },
          files: {
            id: true,
            createDate: true,
            lastEditDate: true,
            name: true,
            numPages: true,
            status: true,
            mimeType: true,
            batchId: true,
            documents: {
              id: true,
              name: true,
              createDate: true,
              lastEditDate: true,
              startPage: true,
              endPage: true,
              status: true,
              shipmentId: true,
              aggregation: {
                id: true,
                action: true,
                status: true,
                targetId: true,
                targetType: true
              }
            }
          },
          documentAggregations: {
            id: true,
            action: true,
            status: true,
            targetId: true,
            targetType: true,
            createDate: true,
            lastEditDate: true
          }
        }
      });
      return fileBatch;
    } catch (error) {
      if (error instanceof EntityNotFoundError) {
        this.logger.error(`File batch not found: ${id}`, error.stack);
        throw new NotFoundException("File batch not found");
      }
      throw error;
    }
  }

  async isFileBatchExists(batchId: string): Promise<boolean> {
    return this.fileBatchRepository.exists({ where: { id: batchId } });
  }

  async moveFileBatchToOrganization(batchId: string, organizationId: number) {
    const fileBatch = await this.getFileBatch(batchId);

    if (fileBatch.organizationId === organizationId) {
      throw new BadRequestException("Batch already belongs to this organization");
    }

    const files = await this.getFiles(batchId);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.startTransaction();

    try {
      // move files to organization bucket
      await this.fileService.moveFilesToOrganization(files, organizationId, queryRunner);

      // update batch organization id
      await queryRunner.manager.getRepository(FileBatch).update(batchId, { organizationId });

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }

    return await this.getFileBatch(batchId);
  }

  async getFiles(batchId: string, relations?: FindOptionsRelations<File>): Promise<File[]> {
    try {
      const fileBatch = await this.fileBatchRepository.findOneOrFail({
        where: { id: batchId },
        relations: {
          files: relations || {
            documents: true
          }
        }
      });

      return fileBatch.files;
    } catch (error) {
      if (error instanceof EntityNotFoundError) {
        this.logger.warn(`FileBatch not found: ${batchId}. Returning empty files array.`);
        return [];
      }
      throw error;
    }
  }

  async setBatchShipmentId(batchId: string, shipmentId: number) {
    this.logger.log(`setting batch shipment id: ${batchId} to ${shipmentId}`);
    await this.fileBatchRepository.update(batchId, { shipmentId });
    const documents = await this.getDocuments(batchId);
    for (const document of documents) {
      // skip if already associated
      if (document.shipmentId) {
        continue;
      }
      await this.documentService.associateWithShipment(document.id, shipmentId);
    }
  }

  async getBatchShipmentId(batchId: string): Promise<number> {
    const fileBatch = await this.getFileBatch(batchId);
    return fileBatch.shipmentId;
  }

  async checkBatchHasShipment(batchId: string): Promise<boolean> {
    const fileBatch = await this.getFileBatch(batchId);
    return fileBatch.shipmentId !== null;
  }

  /**
   * Get all documents in a batch
   *
   * @param batchId The ID of the batch
   * @param relations The relations to include in the documents
   * @returns All documents in the batch
   */
  async getDocuments(batchId: string, relations?: FindOptionsRelations<Document>): Promise<Document[]> {
    const files = await this.getFiles(batchId, {
      documents: relations || true
    });

    return files.flatMap((file) => file.documents);
  }

  async updateFileBatchStatus(batchId: string, status: FileBatchStatus, queryRunner?: QueryRunner) {
    const repository = queryRunner?.manager.getRepository(FileBatch) || this.fileBatchRepository;
    await repository.update(batchId, { status });
  }

  /**
   * Replay aggregation success events for a batch
   *
   * For debug only, please don't use in other cases
   *
   * @param batchId The ID of the batch
   */
  async replayAggregationSuccessEvents(batchId: string) {
    const fileBatch = await this.fileBatchRepository.findOne({
      where: { id: batchId },
      relations: { documentAggregations: { documents: true } }
    });

    this.logger.log(`replaying aggregation success events for batch ${batchId}`);

    // set status to aggregating
    await this.updateFileBatchStatus(batchId, FileBatchStatus.AGGREGATING);

    const successEvents = fileBatch.documentAggregations
      .map((aggregation) => {
        if (aggregation.documents.length === 0) {
          return null;
        }

        return new AggregationSuccessEvent({
          batchId: fileBatch.id,
          organizationId: fileBatch.organizationId,
          shipmentId: fileBatch.shipmentId,
          targetId: fileBatch.shipmentId,
          targetType: aggregation.targetType,
          id: aggregation.id,
          documentId: aggregation.documents[0].id,
          documentName: aggregation.documents[0].name,
          action: aggregation.action,
          documents: aggregation.documents.map((document) => ({
            id: document.id,
            name: document.name
          }))
        });
      })
      .filter((event) => event !== null);

    successEvents.forEach((event) => {
      this.eventEmitter.emit(AggregationEvents.SUCCESS, event);
    });

    this.logger.log(`replayed ${successEvents.length} aggregation success events for batch ${batchId}`);
  }

  /**
   * Check if all documents in a batch are extracted
   *
   * @param batchId The ID of the batch
   * @returns Whether all documents in the batch are extracted
   */
  async isBatchDocumentsExtracted(batchId: string): Promise<boolean> {
    const files = await this.getFiles(batchId);

    return (
      files.every((file) => !file.isProcessing()) &&
      files.every((file) => file.documents.every((document) => !document.isProcessing()))
    );
  }

  /**
   * Subscribe to the file batch processing progress
   *
   * @param id The ID of the batch
   * @returns An observable that emits the file batch processing progress
   */
  async subscribeToFileBatchProcessingProgress(id: string): Promise<Observable<MessageEvent>> {
    const fileBatch = await this.getFileBatch(id);

    return this.sseEventService.listen(`batch-${fileBatch.id}`);
  }
}
