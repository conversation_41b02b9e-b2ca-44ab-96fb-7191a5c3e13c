import { OpenAIService } from "@/llm";
import { Logger } from "@nestjs/common";
import OpenAI from "openai";

/**
 * @deprecated
 */
export abstract class AbstractTask {
  protected readonly OPENAI_MODEL_NAME: string = "o3-mini";
  protected readonly logger = new Logger(this.constructor.name, {
    timestamp: true
  });

  constructor(protected readonly openaiService: OpenAIService) {}

  protected abstract getParams(): any;

  abstract execute(): Promise<any>;

  protected formatUsage(usage: OpenAI.Completions.CompletionUsage): string {
    return `prompt: ${usage.prompt_tokens} (${usage.prompt_tokens_details.cached_tokens} cached), \
completion: ${usage.completion_tokens}, \
total: ${usage.total_tokens}`;
  }

  protected logResponse(response: OpenAI.Chat.Completions.ChatCompletion): void {
    this.logger.debug(response.choices[0].message.content);
    const finishReason = response.choices[0].finish_reason;
    this.logger.log(`Model: ${response.model} \{${this.formatUsage(response.usage!)}\}, ${finishReason}`);
    if (finishReason !== "stop") {
      this.logger.warn(`Finish reason: ${finishReason}`);
    }
  }
}
