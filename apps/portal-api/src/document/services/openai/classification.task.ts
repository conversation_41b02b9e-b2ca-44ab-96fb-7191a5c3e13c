import { Inject, Injectable } from "@nestjs/common";
import { DocumentTypeService } from "nest-modules";
import { zodResponseFormat } from "openai/helpers/zod";
import z from "zod";
import { OpenAIService } from "../../../llm";
import { PromptTemplateManager } from "../../../llm/prompt-template-manager";
import { ParsedPage } from "../parsers/readers/llama-parse-result.reader";
import { OpenAIOperatorBuilder, OpenAIOperatorOutput } from "./operators/openai-operator.builder";
import { Pipeline } from "./pipeline/pipeline";
import { Operation } from "./pipeline/task";
import {
  DocumentSegment,
  DocumentSegmentationResponseV2,
  DocumentSegmentationSchemaV2,
  DocumentSegmentResponse
} from "./types";

export type DocumentClassificationResponse = {
  documents: {
    type: string;
    reasoning: string;
    startPage: number;
    endPage: number;
  }[];
};

@Injectable()
export class ClassificationTask {
  private readonly promptManager: PromptTemplateManager;
  private readonly openaiClient: OpenAIService;

  constructor(
    @Inject(OpenAIService)
    openaiClient: OpenAIService,
    @Inject(PromptTemplateManager)
    promptManager: PromptTemplateManager,
    @Inject(DocumentTypeService)
    private readonly documentTypeService: DocumentTypeService
  ) {
    this.openaiClient = openaiClient;
    this.promptManager = promptManager;
  }

  private readonly UNSUPPORTED_DOCUMENT_TYPES = ["unknown", "other"];

  private async getDocumentTypes() {
    const types = await this.documentTypeService.getAllDocumentTypes();
    return [...types.map((type) => type.name), ...this.UNSUPPORTED_DOCUMENT_TYPES] as [string, ...string[]];
  }

  private async getDocumentTypeSchema() {
    const documentTypeEnum = z.enum(await this.getDocumentTypes());

    return z.object({
      documents: z.array(
        z.object({
          type: documentTypeEnum.describe("The type of the document"),
          reasoning: z.string().describe("The reasoning for the document type"),
          startPage: z.number().describe("The start page of the document"),
          endPage: z.number().describe("The end page of the document")
        })
      )
    });
  }

  private pipeline: Pipeline;

  private async buildPipeline() {
    const builder = new OpenAIOperatorBuilder(this.promptManager, this.openaiClient);

    const segmentPageV2 = builder.get<
      { pages: ParsedPage[] },
      OpenAIOperatorOutput<DocumentSegmentationResponseV2>
    >(
      {
        model: "gpt-4o",
        promptTemplatePath: "segment-pages",
        temperature: 0.2,
        top_p: 0.1,
        response_format: zodResponseFormat(DocumentSegmentationSchemaV2, "SegmentedPages")
      },
      "segment-pages-v2"
    );

    const concatPagesV2 = new Operation<DocumentSegmentationResponseV2 & { pages: ParsedPage[] }, any>(
      ({ segments, pages }) => {
        const result = [];

        let currentSegment = {
          reasoning: "",
          pages: [pages.find((p) => p.page === segments[0].page)]
        };

        for (let i = 1; i < segments.length; i++) {
          const segment = segments[i];

          if (segment.isTOSOnly) {
            continue;
          }

          if (!segment.isContinuation) {
            result.push(currentSegment);

            currentSegment = {
              reasoning: "",
              pages: [pages.find((p) => p.page === segment.page)]
            };
          } else {
            currentSegment.pages.push(pages.find((p) => p.page === segment.page));
          }
        }

        result.push(currentSegment);

        return {
          segments: result
        };
      }
    );

    // classify the document segments into document types
    const classifyDocument = builder.get<
      DocumentSegmentResponse & { segments: DocumentSegment[] },
      DocumentClassificationResponse
    >(
      {
        model: "gpt-4o",
        promptTemplatePath: "document-classification",
        temperature: 0.2,
        top_p: 0.1,
        response_format: zodResponseFormat(await this.getDocumentTypeSchema(), "DocumentSegmentationResult")
      },
      "classify-document"
    );

    const classifyPages = new Operation(async ({ segments }) => {
      const results = [];

      for (const segment of segments) {
        const result = await classifyDocument.execute({
          segments: [segment]
        });
        for (const document of result.data.documents) {
          results.push({
            type: document.type,
            reasoning: document.reasoning,
            startPage: document.startPage,
            endPage: document.endPage
          });
        }
      }

      return {
        documents: results
      };
    });

    const pipeline = new Pipeline({
      outputShape: {
        documents: "classify-pages:documents"
      }
      // logger: new Logger(ClassificationTask.name),
    });

    pipeline.addTask("segment-pages-v2", segmentPageV2, {
      inputMap: {
        pages: "context:pages"
      }
    });
    pipeline.addTask("concat-pages-v2", concatPagesV2, {
      inputMap: {
        segments: "segment-pages-v2:data.segments",
        pages: "context:pages"
      }
    });
    pipeline.addTask("classify-pages", classifyPages, {
      inputMap: {
        segments: "concat-pages-v2:segments"
      }
    });

    return pipeline;
  }

  async execute({ pages }: { pages: ParsedPage[] }) {
    return await (await this.buildPipeline()).invoke({ pages });
  }
}
