import { DocumentFieldDataType } from "nest-modules";

export class DocumentExtractionConfig {
  /**
   * The type of the document
   */
  type: string;

  /**
   * The prompt template for the document
   */
  typeSpecificPromptTemplate?: string;

  /**
   * The fields to extract from the document
   */
  fields: FieldExtractionConfig[];

  getFieldByName(name: string): FieldExtractionConfig {
    return this.fields.find((field) => field.name === name);
  }
}

export class FieldExtractionConfig {
  name: string;
  isMandatory: boolean;
  type: DocumentFieldDataType;
  description?: string;
}
