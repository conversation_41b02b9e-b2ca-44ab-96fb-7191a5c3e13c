import OpenAI from "openai";

import { DocumentFieldDataType } from "nest-modules";
import { AutoParseableResponseFormat } from "openai/lib/parser.mjs";
import { ChatCompletionContentPart, ChatCompletionReasoningEffort } from "openai/resources/index.mjs";
import { AbstractTask } from "./abstract.task";
import { DocumentExtractionConfig, FieldExtractionConfig } from "./document-extraction-config";
import { DocumentExtractionResult } from "./dto";
import { OpenAIExtractTaskError } from "./errors";
import { ConfigToSchemaMapper } from "./mapper";

export class ExtractTask extends AbstractTask {
  protected pages: string[];
  protected config: DocumentExtractionConfig;

  protected modelName = "gpt-4o";

  // configure for non-reasoning models
  protected TOP_P = 0.01;
  protected TEMPERATURE = 0;

  // configure for reasoning models
  protected REASONING_EFFORT: ChatCompletionReasoningEffort = "medium";

  protected getSchema(): AutoParseableResponseFormat<any> {
    return new ConfigToSchemaMapper(this.config).getOutputSchema();
  }

  /**
   * Full prompt
   */
  protected getPrompt(context?: string): string {
    return `\
Act as an expert data extractor for ${this.config.type} documents. Your task is to extract all relevant information from the provided document and output the results in JSON format following the specified schema.

Guidelines:
- If a field is missing in the document, set its value to null.
- Only include data that is explicitly found in the document.
- Accurately map data to the corresponding fields using contextual clues.
- When processing address-related fields, ensure that each component (e.g., street, city, state, country) contains only the information appropriate to that component. Remove any redundant or overlapping details so that each field holds solely its intended data.

${this.config.typeSpecificPromptTemplate ? `\nNote about fields:\n${this.config.typeSpecificPromptTemplate}`.trim() : ""}

${this.extraPrompt ? `\nExtra instructions:\n${this.extraPrompt}`.trim() : ""}

${context ? "\nContext:\n" + context : ""}
`.trim();
  }

  protected getValidationPrompt(context?: string): string {
    return `\
Please validate the extraction result for ${this.config.type} documents.

Guidelines:
- If a field is missing in the document, set its value to null.
- Check if the extracted result has extra information that is not in the document. If so, remove it.
- Check if the extracted result has missing information that is in the document. If so, add it.
- When processing address-related fields, ensure that each component (e.g., street, city, state, country) contains only the information appropriate to that component. Remove any redundant or overlapping details so that each field holds solely its intended data.
- Infer the value of the country if it is not explicitly provided.
- For the country field, convert the country name to the ISO 3166-1 alpha-2 code.
- For the state field, convert the state name to the ISO 3166-2 alpha-2 code, do not prefix it with the country code.

${context ? "\nContext:\n" + context : ""}
    `.trim();
  }

  protected async getData(): Promise<string | ChatCompletionContentPart[]> {
    return this.pages.join("\n");
  }

  protected async getParams(): Promise<OpenAI.Chat.Completions.ChatCompletionCreateParamsNonStreaming> {
    return {
      model: this.modelName,
      messages: [
        { role: "system", content: this.getPrompt() },
        { role: "user", content: await this.getData() }
      ],
      response_format: this.getSchema(),
      max_completion_tokens: 16383,
      ...(this.modelName === "o3-mini"
        ? { reasoning_effort: this.REASONING_EFFORT }
        : { temperature: this.TEMPERATURE, top_p: this.TOP_P })
    };
  }

  protected async getValidationParams(
    extractedResult: string
  ): Promise<OpenAI.Chat.Completions.ChatCompletionCreateParamsNonStreaming> {
    return {
      model: "gpt-4o",
      messages: [
        { role: "system", content: this.getPrompt() },
        {
          role: "user",
          content: await this.getData()
        },
        {
          role: "assistant",
          content: extractedResult
        },
        {
          role: "user",
          content: this.getValidationPrompt()
        }
      ],
      response_format: this.getSchema(),
      max_completion_tokens: 16383,
      ...(this.modelName === "o3-mini"
        ? { reasoning_effort: this.REASONING_EFFORT }
        : { temperature: this.TEMPERATURE, top_p: this.TOP_P })
    };
  }

  async execute(): Promise<DocumentExtractionResult> {
    this.injectExtraFields();
    let request = await this.getParams();
    let response: OpenAI.Chat.Completions.ChatCompletion;
    try {
      this.logger.debug("Extracting response");
      response = await this.openaiService.createChatCompletion(request, "extract");
    } catch (error) {
      this.logger.error("Error getting response from OpenAI", error);
      throw new OpenAIExtractTaskError("Error getting response from OpenAI: " + error.message);
    }

    const validationRequest = await this.getValidationParams(response.choices[0].message.content);

    let validationResponse: OpenAI.Chat.Completions.ChatCompletion;

    try {
      this.logger.debug("Validating response");
      validationResponse = await this.openaiService.createChatCompletion(
        validationRequest,
        "validate-extraction"
      );
    } catch (error) {
      this.logger.error("Error getting response from OpenAI", error);
      throw new OpenAIExtractTaskError("Error getting response from OpenAI: " + error.message);
    }

    return this.validatedResponse(validationResponse.choices[0].message.content);
  }

  private removeEmptyColumnsFromArray(value: object[]): object[] {
    if (!value || value.length === 0) {
      return value;
    }

    const keys = Object.keys(value[0]);
    const keysToRemove = keys.filter((key) => value.every((obj) => obj[key] === null || obj[key] === ""));

    return value.map((obj) => {
      const newObj = { ...obj };
      keysToRemove.forEach((key) => {
        delete newObj[key];
      });
      return newObj;
    });
  }

  /**
   * Validate the response and return the result
   */
  async validatedResponse(content: string): Promise<DocumentExtractionResult> {
    try {
      const result = JSON.parse(content);

      const fields = [];

      Object.entries(result).forEach(([key, value]) => {
        // if value is empty string, do not include it
        if (value === "" || value === null) {
          return;
        }

        if (typeof value === "object") {
          if (Array.isArray(value)) {
            // remove empty columns
            value = this.removeEmptyColumnsFromArray(value);
          } else if (
            // do not include empty objects
            Object.values(value).every((v) => v === null || v === "")
          ) {
            return;
          } else {
            // remove empty values
            value = Object.fromEntries(Object.entries(value).filter(([_, v]) => v !== null && v !== ""));
          }
        }

        fields.push({ key, value });
      });

      return new DocumentExtractionResult(fields);
    } catch (error) {
      if (error instanceof SyntaxError) {
        throw new OpenAIExtractTaskError("Cannot parse response to JSON");
      } else if (Array.isArray(error) && error.length > 0) {
        throw new OpenAIExtractTaskError("Validation failed for response: " + error[0].toString());
      }

      throw new OpenAIExtractTaskError("Invalid response from OpenAI: " + error.message);
    }
  }

  setPages(pages: string[]): ExtractTask {
    this.pages = pages;
    return this;
  }

  setModel(model: string): ExtractTask {
    this.modelName = model;
    return this;
  }

  setConfig(config: DocumentExtractionConfig): ExtractTask {
    this.logger.verbose("setConfig", config);
    this.config = config;
    return this;
  }

  //////////////////////////////// EXTEND //////////////////////////////
  /**
   * Inject the extra fields to the config
   */
  protected injectExtraFields() {
    // inject the location and trade partner schemas
    this.injectLocationSchema();
  }
  /**
   * Extended prompt
   */
  get extraPrompt(): string {
    return `\
Original fields:
COUNTRY field must contains the original data
PHONENUMBER must contain the original data.

Derived fields:
$TIMEZONE is the timezone of the location, in IANA timezone database format.
`.trim();
  }

  protected injectLocationSchema() {
    const applicableFields = ["placeOfDelivery", "portOfLoading", "portOfDischarge"];

    const locationFields: FieldExtractionConfig[] = [
      {
        name: "$TIMEZONE",
        isMandatory: false,
        type: DocumentFieldDataType.STRING,
        description: "Timezone of the $1, in IANA timezone database format"
      }
    ];

    this.config.fields.forEach((field) => {
      if (applicableFields.includes(field.name)) {
        this.config.fields.push(
          ...locationFields.map((lf) => ({
            ...lf,
            name: `${field.name}.${lf.name}`
          }))
        );
      }
    });
  }
}
