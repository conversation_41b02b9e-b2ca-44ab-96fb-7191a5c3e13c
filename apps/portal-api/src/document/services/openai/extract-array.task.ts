import { <PERSON>AI } from "openai";
import { AutoParseableResponseFormat } from "openai/lib/parser.mjs";
import { DocumentExtractionResult } from "./dto";
import { OpenAIExtractTaskError } from "./errors";
import { ExtractTask } from "./extract.task";
import { mergeObject } from "./utils/merge-object.util";

export class ExtractArrayTask extends ExtractTask {
  async getResponse(prompt: string, data: string, schema: AutoParseableResponseFormat<any>): Promise<any> {
    this.logger.verbose("Prompt", prompt);
    this.logger.verbose("Data", data);
    this.logger.verbose("Schema", JSON.stringify(schema));

    const request: OpenAI.Chat.Completions.ChatCompletionCreateParamsNonStreaming = {
      ...(await this.getParams()),
      messages: [
        { role: "system", content: prompt },
        { role: "user", content: data }
      ],
      response_format: schema
    };

    try {
      const response = await this.openaiService.createChatCompletion(request, "extract-array");

      return this.parseResult(response.choices[0].message.content!);
    } catch (error) {
      this.logger.error("Error getting response from OpenAI", error);
      throw new OpenAIExtractTaskError("Error getting response from OpenAI: " + error.message);
    }
  }

  /**
   * Split the pages into chunks of 8192 characters
   */
  getChunkedPages(): string[] {
    const TEXT_CHUNK_SIZE = 8192;

    const chunkedPages: string[] = [this.pages[0]];

    let chunkIndex = 0;

    for (let i = 1; i < this.pages.length; ++i) {
      const page = this.pages[i];
      const currentChunkSize = chunkedPages[chunkIndex].length;

      // start a new chunk
      if (currentChunkSize + page.length > TEXT_CHUNK_SIZE) {
        chunkedPages.push("");
        chunkIndex++;
      }

      // add the page to the current chunk
      chunkedPages[chunkIndex] += "\n" + page;
    }

    console.log(chunkedPages);

    this.logger.verbose("Chunked to " + chunkedPages.length + " chunks");
    return chunkedPages;
  }

  /**
   * Get the first and last page
   */
  getFirstAndLastPage(): string {
    return this.pages[0] + "\n" + this.pages[this.pages.length - 1];
  }

  parseResult(result: string): Object {
    return JSON.parse(result);
  }

  notEmpty(value: any): boolean {
    return value !== null && value !== undefined && value !== "" && value !== "null";
  }

  async getPresentedColumns(sampledPageData: any) {
    this.logger.verbose("Getting result for first and last page");

    // Ensure there are commercialInvoiceLines to process
    if (!sampledPageData.commercialInvoiceLines || sampledPageData.commercialInvoiceLines.length === 0) {
      this.logger.warn("No commercialInvoiceLines available to filter.");
      return [];
    }

    // Get all unique keys from the commercialInvoiceLines
    const allKeys = Object.keys(sampledPageData.commercialInvoiceLines[0]);

    // Filter keys that have at least one non-empty value across all objects
    return allKeys.filter((key) =>
      sampledPageData.commercialInvoiceLines.some((line) => this.notEmpty(line[key]))
    );
  }

  makeContext(columns: string[], countryOfOrigin: string): string {
    let context = "";

    if (columns.length > 0) {
      context += "Commercial invoice lines columns: " + columns.join(", ") + "\n";
    }

    if (countryOfOrigin) {
      context += `Country of origin: ${countryOfOrigin}`;
    }

    return context;
  }

  async execute(): Promise<DocumentExtractionResult> {
    const firstAndLastPage = this.getFirstAndLastPage();
    const chunkedPages = this.getChunkedPages();

    this.logger.debug("Getting result for first and last page");
    const firstAndLastPageData = await this.getResponse(this.getPrompt(), firstAndLastPage, this.getSchema());

    const columns = await this.getPresentedColumns(firstAndLastPageData);

    // unset the commercialInvoiceLines array
    const result = firstAndLastPageData;
    result.commercialInvoiceLines = [];

    // promisify all to allow multiple requests to be made in parallel
    const promises = chunkedPages.map(async (page, i) => {
      this.logger.debug(`Getting result for page ${i + 1} of ${chunkedPages.length}`);
      const context = this.makeContext(columns, firstAndLastPageData.countryOfOrigin);
      return await this.getResponse(this.getPrompt(context), page, this.getSchema());
    });

    const pageResults = await Promise.all(promises);

    this.logger.debug("All pages processed");

    pageResults.forEach((pageResult) => {
      try {
        mergeObject(result, pageResult);
      } catch (error) {
        this.logger.error("Error merging object", error);
        throw new OpenAIExtractTaskError("Error merging object: " + error.message);
      }
    });

    this.logger.verbose("Result Merged", JSON.stringify(result));

    return this.validatedResponse(JSON.stringify(result));
  }
}
