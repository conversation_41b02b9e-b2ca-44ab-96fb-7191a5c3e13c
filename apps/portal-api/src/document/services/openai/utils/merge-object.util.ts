export function mergeObject(base: Object, addition: Object) {
  Object.entries(addition).forEach(([key, value]) => {
    if (value === "null") {
      return;
    }

    if (value === undefined || value === null) {
      return;
    }

    if (base[key] === undefined || base[key] === null) {
      base[key] = value;
      return;
    }

    if (typeof value === "object") {
      if (Array.isArray(base[key]) && Array.isArray(value)) {
        base[key] = [...base[key], ...value];
      } else {
        base[key] = mergeObject(base[key], value);
      }
    }
  });

  return base;
}
