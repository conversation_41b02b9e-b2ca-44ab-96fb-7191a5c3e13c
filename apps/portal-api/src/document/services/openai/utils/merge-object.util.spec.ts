import { mergeObject } from "./merge-object.util";

describe("mergeObject", () => {
  it("should merge objects", () => {
    const result = {};
    const base = { a: 1, b: 2 };
    const addition = { b: 3, c: 4 };
    mergeObject(result, base);
    mergeObject(result, addition);
    expect(result).toEqual({ a: 1, b: 2, c: 4 });
  });

  it("should merge objects with arrays", () => {
    const result = {};
    const base = { a: 1, b: [1, 2] };
    const addition = { b: [3, 4], c: 4 };
    mergeObject(result, base);
    mergeObject(result, addition);
    expect(result).toEqual({ a: 1, b: [1, 2, 3, 4], c: 4 });
  });

  it("should merge objects with nested objects", () => {
    const result = {};
    const base = { a: 1, b: { c: 2, d: 3 } };
    const addition = { b: { c: 4, e: 5 }, f: 6 };
    mergeObject(result, base);
    mergeObject(result, addition);
    expect(result).toEqual({ a: 1, b: { c: 2, d: 3, e: 5 }, f: 6 });
  });

  it("should not merge null values", () => {
    const result = { a: 1, b: null };
    const base = { a: 1, b: null, c: 3 };
    mergeObject(result, base);
    expect(result).toEqual({ a: 1, b: null, c: 3 });
  });

  it("should not merge nested null values", () => {
    const result = { a: 1, b: { c: null, d: 3 } };
    const base = { a: 1, b: { c: null, d: 3 }, e: 5 };
    mergeObject(result, base);
    expect(result).toEqual({ a: 1, b: { c: null, d: 3 }, e: 5 });
  });

  it('should treat string "null" as null', () => {
    const result = { a: 1, b: null };
    const base = { a: 1, b: "null", c: 3 };
    mergeObject(result, base);
    expect(result).toEqual({ a: 1, b: null, c: 3 });
  });
});
