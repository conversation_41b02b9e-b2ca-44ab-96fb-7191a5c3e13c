import { OpenAIService, PromptTemplateManager } from "@/llm";
import {
  ChatCompletionCreateParamsBase,
  ChatCompletionMessageParam
} from "openai/resources/chat/completions";
import { Operation } from "../pipeline/task";

export type OpenAIBuilderOptions = {
  model: string;
  promptTemplate?: string;
  promptTemplatePath?: string;
  temperature?: number;
} & Partial<ChatCompletionCreateParamsBase>;

export type OpenAIOperatorOutput<O> = {
  data: O;
};

export class OpenAIOperatorBuilder {
  private readonly promptManager: PromptTemplateManager;
  private readonly openaiService: OpenAIService;

  constructor(promptManager: PromptTemplateManager, openaiService: OpenAIService) {
    this.promptManager = promptManager;
    this.openaiService = openaiService;
  }

  private buildPrompt(options: OpenAIBuilderOptions, inputs: Record<string, any>) {
    if (options.promptTemplatePath) {
      return this.promptManager.getPromptFromTemplate(options.promptTemplatePath, inputs);
    } else if (options.promptTemplate) {
      return this.promptManager.getPrompt(options.promptTemplate, inputs);
    }

    throw new Error("No prompt template provided");
  }

  selectOutput(response: any, options: OpenAIBuilderOptions) {
    if (options.response_format.type === "json_schema") {
      return JSON.parse(response.choices[0].message.content);
    }

    return response.choices[0].message.content;
  }

  get<I, O>(options: OpenAIBuilderOptions, caller?: string): Operation<I, OpenAIOperatorOutput<O>> {
    return new Operation(async (inputs: I) => {
      const prompts = this.buildPrompt(options, inputs);

      const messages: ChatCompletionMessageParam[] = [{ role: "system", content: prompts.systemPrompt }];

      if (prompts.userPrompt.length > 0) {
        messages.push({ role: "user", content: prompts.userPrompt });
      }

      const request = {
        model: options.model,
        response_format: options.response_format,
        temperature: options.temperature,
        messages
      };

      const response = await this.openaiService.createChatCompletion(request, caller);

      return {
        data: this.selectOutput(response, options)
      };
    });
  }
}
