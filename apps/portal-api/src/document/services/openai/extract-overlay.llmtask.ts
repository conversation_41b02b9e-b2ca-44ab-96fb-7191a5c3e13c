import { OpenAIService } from "@/llm";
import { StorageService } from "@/storage/storage.service";
import { Injectable, Logger } from "@nestjs/common";
import { zodResponseFormat } from "openai/helpers/zod";
import path from "path";
import { ParsedPage } from "../parsers/readers/llama-parse-result.reader";
import {
  CargoControlInfo,
  CargoControlSchema,
  ExtractCargoControlInfoResponse,
  OverlaySchema,
  PageOverlay
} from "./types";

export interface ExtractedOverlay {
  page: number;
  data: CargoControlInfo | null;
}

@Injectable()
export class ExtractOverlayLlmTask {
  constructor(
    private readonly openaiService: OpenAIService,
    private readonly storageService: StorageService
  ) {}

  private readonly logger = new Logger(ExtractOverlayLlmTask.name);

  async handwrittenToStructured(handwrittenText: string): Promise<CargoControlInfo | null> {
    const prompt =
      `Please determine if the following handwritten text contains either the port of entry, eta, etd or CCN.

## CCN (Cargo Control Number)
Please omit the spaces between the numbers and the letters

Acceptable formats:
- 4 alpha numeric + PARS + 7 alpha numeric
- 4 alpha numeric + 7 alpha numeric

## Port of entry:
The port used for entering the country, aliases: POE, Port of Entry, Port of crossing, Border Crossing, should be a city / location name.

## ETD (Estimated Departure Time)
Format: YYYY-MM-DD

## ETA port (Estimated Arrival Time to the port)
Format: YYYY-MM-DD

<handwritten_content>
${handwrittenText}
</handwritten_content>
`.trim();

    const response = await this.openaiService.createChatCompletion(
      {
        model: "gpt-4o",
        messages: [{ role: "user", content: prompt }],
        response_format: zodResponseFormat(CargoControlSchema, "CargoControlSchema"),
        temperature: 0,
        top_p: 0.01
      },
      "structuralize-overlay"
    );

    const result = JSON.parse(response.choices[0].message.content) as ExtractCargoControlInfoResponse;

    if (!result.hasCargoControlInfo) {
      return null;
    }

    return result.data;
  }

  async extractHandwrittenText(screenshotUrl: string): Promise<PageOverlay> {
    const promptExtract = `Extract the handwritten text and the PARS code from a given image.
# Steps

1. Analyze the image to identify distinct text elements.
2. Differentiate between printed and handwritten text, focusing specifically on locating the handwritten text.
3. Extract the handwritten text elements precisely, ensuring accuracy in transcription.
4. Use your general knowledge to identify the handwritten text. If port of crossing is mentioned, it will most likely be a canada port / city name.

# Handwritten Text
- Provide the extracted handwritten text in plain text format.
- If multiple handwritten segments are detected, separate them clearly, maintaining the sequence and organization as they appear in the image.

# PARS Code
The PARS code could be either format as follows:
- The Letter PARS is part of the code, between the carrier code and numeric sequence, e.g. 9999 PARS 0001
The word PARS is printed either above the code / below the code

# Notes

- Make sure to focus solely on handwritten text, excluding any printed text from the extraction.
- Ensure high accuracy in reading and transcribing the handwritten elements.
    `;

    const responseExtract = await this.openaiService.createChatCompletion(
      {
        model: "gpt-4o",
        temperature: 0,
        top_p: 0.01,
        messages: [
          { role: "system", content: promptExtract },
          {
            role: "user",
            content: [
              {
                type: "image_url",
                image_url: {
                  url: screenshotUrl
                }
              }
            ]
          }
        ],
        response_format: zodResponseFormat(OverlaySchema, "OverlaySchema")
      },
      "extract-overlay"
    );

    const extractionResult: PageOverlay = JSON.parse(responseExtract.choices[0].message.content);

    return extractionResult;
  }

  async extractOverlay(pages: ParsedPage[], filePath: string): Promise<ExtractedOverlay[]> {
    const results = await Promise.all(
      pages.map(async (page) => {
        this.logger.debug(`Extracting overlay for page ${page.page}`);

        let extractionResult: PageOverlay | null = null;

        try {
          const screenshotUrl = await this.storageService.getSignedUrl(
            path.join(filePath, `page_${page.page}.png`)
          );
          extractionResult = await this.extractHandwrittenText(screenshotUrl);
        } catch (error) {
          this.logger.error(`Error extracting overlay for page ${page.page}: ${error}`);
          return null;
        }

        const barcodes = page.barcodes;

        let data: CargoControlInfo | null = null;

        if (extractionResult.handwritten_text) {
          data = await this.handwrittenToStructured(extractionResult.handwritten_text);
        }

        console.log(`Barcodes: ${barcodes.join(", ")}`);

        if (barcodes.length > 0) {
          data = {
            ...data,
            CCN: barcodes[0]
          };
        } else if (extractionResult.pars_code) {
          data = {
            ...data,
            CCN: extractionResult.pars_code
          };
        }

        if (data === null || Object.values(data).every((value) => value === null)) {
          return null;
        }

        return {
          page: page.page,
          data
        };
      })
    );

    return results.filter((o) => o !== null);
  }
}
