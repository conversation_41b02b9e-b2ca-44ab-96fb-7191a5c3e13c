import { z } from "zod";

export interface DocumentSegment {
  label: string;
  reasoning: string;
  pages: number[];
}

export interface DocumentSegmentResponse {
  segments: DocumentSegment[];
}

export interface DocumentSegmentationResponseV2 {
  segments: PageSummary[];
}

export interface PageSummary {
  page: number;
  reasoning: string;
  isContinuation: boolean;
  isTOSOnly: boolean;
}

export const PageSummarySchema = z.object({
  page: z.number(),
  reasoning: z.string(),
  isTOSOnly: z.boolean().describe("Indicate if the page contains ONLY Terms of Condition information"),
  isContinuation: z.boolean().describe("True if the page is a continuation of the previous page")
});

// Zod schemas for validation
export const DocumentSegmentSchema = z.object({
  label: z.string(),
  reasoning: z.string(),
  pages: z.array(z.number())
});

export const DocumentSegmentationSchema = z.object({
  segments: z.array(DocumentSegmentSchema)
});

export const DocumentSegmentationSchemaV2 = z.object({
  segments: z.array(PageSummarySchema)
});

export const CargoControlSchema = z.object({
  hasCargoControlInfo: z.boolean().describe("True if the document contains cargo control information"),
  reason: z.string().describe("Explanation or reasoning behind the cargo control information"),
  data: z.union([
    z.object({
      CCN: z
        .union([z.string(), z.null()])
        .describe(
          "Cargo Control Number, a serial number that contains 5 to 25 letters and numbers, can have spaces."
        ),
      etd: z.union([z.string(), z.null()]).describe("Estimated Departure Time, format: YYYY-MM-DD"),
      etaPort: z
        .union([z.string(), z.null()])
        .describe(
          "Estimated date and time for arrival at the border or crossing the border in YYYY-MM-DD format."
        ),
      portOfEntry: z
        .union([z.string(), z.null()])
        .describe(
          "Port name identified as 'POE', 'Port of Entry' or 'Port of crossing' or 'Border Crossing'."
        )
    }),
    z.null()
  ])
});

export interface CargoControlInfo {
  CCN: string | null;
  etd: string | null;
  etaPort: string | null;
  portOfEntry: string | null;
}

export interface ExtractCargoControlInfoResponse {
  hasCargoControlInfo: boolean;
  reason: string;
  data: CargoControlInfo | null;
}

export const OverlaySchema = z.object({
  handwritten_text: z.union([z.string(), z.null()]),
  pars_code: z.union([z.string(), z.null()])
});

export type PageOverlay = z.infer<typeof OverlaySchema>;
