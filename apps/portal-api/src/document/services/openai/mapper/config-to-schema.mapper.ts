import { DocumentFieldDataType } from "nest-modules";
import { zodResponseFormat } from "openai/helpers/zod";
import { AutoParseableResponseFormat } from "openai/lib/parser.mjs";
import { z } from "zod";
import { DocumentExtractionConfig } from "../document-extraction-config";

class Field {
  constructor(type: DocumentFieldDataType, description?: string) {
    this.type = type;
    this._description = description;
  }

  type: DocumentFieldDataType;
  _description?: string;
}

export class ConfigToSchemaMapper {
  private config: DocumentExtractionConfig;

  private typeMapping = {
    [DocumentFieldDataType.STRING]: z.string(),
    [DocumentFieldDataType.NUMBER]: z.number()
  };

  constructor(config: DocumentExtractionConfig) {
    this.config = config;
  }

  objToZod(obj: Field | any[], optional = false): z.ZodType {
    if (obj instanceof Field && obj.type in this.typeMapping) {
      let s = z.union([this.typeMapping[obj.type], z.null()]);
      if (obj._description) {
        s = s.describe(obj._description);
      }
      return s;
    }

    if (Array.isArray(obj)) {
      return z.array(this.objToZod(obj[0]));
    }

    const schemaMap = {};

    let description: string | undefined;
    if ("_description" in obj) {
      description = obj._description;
      delete obj._description;
    }

    Object.entries(obj).forEach(([key, value]) => {
      schemaMap[key] = this.objToZod(value, true);
    });

    let schema = z.object(schemaMap);

    if (description) {
      schema = schema.describe(description);
    }

    if (optional) {
      return z.union([schema, z.null()]).optional();
    }

    return schema;
  }

  fieldsToObject(): any {
    const obj = {};

    this.config.fields
      .sort((a, b) => a.name.localeCompare(b.name))
      .forEach((field) => {
        const parts = field.name.split(".");

        let current: object | object[] = obj;
        for (let i = 0; i < parts.length; i++) {
          const part = parts[i];
          // navigate to the path, if the path exists
          if (current[part]) {
            if (Array.isArray(current[part])) {
              current = current[part][0];
            } else {
              current = current[part];
            }
            continue;
          }

          // populate field
          if (field.type === "array") {
            current[part] = [{}];
          } else if (field.type === "object") {
            current[part] = {
              _description: field.description
            };
          } else {
            current[part] = new Field(field.type, field.description);
          }
        }
      });

    return obj;
  }

  getOutputSchema(): AutoParseableResponseFormat<z.infer<z.ZodSchema>> {
    const obj = this.fieldsToObject();
    const schema = this.objToZod(obj);
    return zodResponseFormat(schema, this.config.type);
  }
}
