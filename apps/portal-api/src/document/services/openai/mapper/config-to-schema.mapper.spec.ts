import { DocumentFieldDataType } from "nest-modules";
import { DocumentExtractionConfig } from "../models";
import { ConfigToSchemaMapper } from "./config-to-schema.mapper";

describe("ConfigToSchemaMapper", () => {
  it("should map config to schema", () => {
    const mapper = new ConfigToSchemaMapper({
      type: "invoice",
      fields: [
        {
          name: "invoice_number",
          isMandatory: true,
          type: DocumentFieldDataType.STRING,
          description: "Invoice number"
        },
        {
          name: "invoice_date",
          isMandatory: true,
          type: DocumentFieldDataType.STRING,
          description: "Invoice date"
        },
        { name: "due_date", isMandatory: true, type: DocumentFieldDataType.STRING, description: "Due date" }
      ]
    } as DocumentExtractionConfig);

    const schema = mapper.getOutputSchema().json_schema;
    // expect schema match the following json
    const expectedSchema = {
      invoice_number: { type: ["string", "null"], description: "Invoice number" },
      invoice_date: { type: ["string", "null"], description: "Invoice date" },
      due_date: { type: ["string", "null"], description: "Due date" }
    };

    expect(schema.schema.properties).toEqual(expectedSchema);
  });

  it("should map nested config to schema", () => {
    const mapper = new ConfigToSchemaMapper({
      type: "invoice",
      fields: [
        { name: "invoice_number", isMandatory: true, type: "object" },
        { name: "invoice_number.value", isMandatory: true, type: "string" },
        { name: "invoice_number.confidence", isMandatory: true, type: "number" },
        { name: "invoice_date", isMandatory: true, type: "object" },
        { name: "invoice_date.value", isMandatory: true, type: "string" }
      ]
    } as DocumentExtractionConfig);

    const schema = mapper.getOutputSchema().json_schema;
    const expectedSchema = {
      invoice_number: {
        type: "object",
        additionalProperties: false,
        properties: {
          value: { type: ["string", "null"] },
          confidence: { type: ["number", "null"] }
        },
        required: ["value", "confidence"]
      },
      invoice_date: {
        type: "object",
        additionalProperties: false,
        properties: {
          value: { type: ["string", "null"] }
        },
        required: ["value"]
      }
    };

    expect(schema.schema.properties).toEqual(expectedSchema);
  });

  it("should map nested array config to schema", () => {
    const mapper = new ConfigToSchemaMapper({
      type: "invoice",
      fields: [
        { name: "commercialInvoiceLines", isMandatory: true, type: "array" },
        { name: "commercialInvoiceLines.value", isMandatory: true, type: "string" }
      ]
    } as DocumentExtractionConfig);

    const schema = mapper.getOutputSchema().json_schema;
    const expectedSchema = {
      commercialInvoiceLines: {
        type: "array",
        items: {
          type: "object",
          additionalProperties: false,
          properties: {
            value: { type: ["string", "null"] }
          },
          required: ["value"]
        }
      }
    };

    expect(schema.schema.properties).toEqual(expectedSchema);
  });
});
