import { LoggerService } from "@nestjs/common";
import { isPromise } from "util/types";
import { Operator, Task } from "./task";

export interface PipelineOutputShape {
  [key: string]: string;
}

export class Pipeline {
  private tasks: Map<string, Task>;
  private dependencies: Map<string, Set<string>>;
  private results: Map<string, any>;
  private outputShape: PipelineOutputShape;
  private logger: LoggerService;
  constructor({ outputShape, logger }: { outputShape: PipelineOutputShape; logger?: LoggerService }) {
    /**
     * Initializes the Pipeline with empty task, dependency, and results maps.
     */
    this.tasks = new Map();
    this.dependencies = new Map();
    this.results = new Map();
    this.outputShape = outputShape;

    if (logger) {
      this.logger = logger;
    }
  }

  /**
   * Adds a task to the pipeline.
   * @param name - Unique name of the task.
   * @param task - Instance of a Task or an Operation.
   */
  addTask(
    name: string,
    operator: Operator<any, any>,
    taskOptions: {
      inputMap: Record<string, any>;
    }
  ): void {
    if (this.tasks.has(name)) {
      throw new Error(`Task '${name}' already exists.`);
    }
    this.tasks.set(name, {
      name: name,
      operator: operator,
      inputMap: taskOptions.inputMap
    });
    this.dependencies.set(name, new Set());

    // Automatically add dependencies based on inputShape
    Object.entries(taskOptions.inputMap).forEach(([_, refVar]) => {
      // Extract the producer task name from the reference variable
      const [producerTaskName, outputVar] = refVar.split(":");

      if (!producerTaskName || !outputVar) {
        throw new Error(
          `Invalid reference '${refVar}' in inputShape of task '${name}'. Expected format 'task:outputVar'.`
        );
      }

      if (producerTaskName === "context") {
        return;
      }

      if (!this.tasks.has(producerTaskName)) {
        throw new Error(`Dependency task '${producerTaskName}' does not exist for task '${name}'.`);
      }

      this.dependencies.get(name)!.add(producerTaskName);
    });
  }

  private accessNestedObject(obj: any, path: string): any {
    return path.split(".").reduce((acc, key) => acc && acc[key], obj);
  }

  private getVariableValue(varRef: string): any {
    const taskName = varRef.substring(0, varRef.indexOf(":"));
    const outputVar = varRef.substring(varRef.indexOf(":") + 1);

    let data = null;

    if (this.results.has(taskName)) {
      data = this.results.get(taskName);
    } else {
      data = this.results.get(outputVar);
    }

    return this.accessNestedObject(data, outputVar);
  }

  /**
   * Executes the pipeline tasks in order based on their dependencies.
   * Each task receives only the inputs it requires and pushes its outputs as named variables.
   * @param initialInputs - An object containing the initial input variables for the pipeline.
   * @returns An object containing all output variables produced by the pipeline.
   */
  async invoke(initialInputs: Record<string, any>): Promise<Record<string, any>> {
    // Initialize results with initial inputs under the "start" key
    Object.entries(initialInputs).forEach(([key, value]) => {
      this.results.set("context", { [key]: value });
    });

    const sortedTasks = this.topologicalSort();

    for (const taskName of sortedTasks) {
      this.logger?.log(`Executing task '${taskName}'`);

      const task = this.tasks.get(taskName)!;
      const input: Record<string, any> = {};
      // Map inputShape references to actual values
      Object.entries(task.inputMap).forEach(([localVar, refVar]) => {
        input[localVar] = this.getVariableValue(refVar);
      });

      this.logger?.log("Input: ", JSON.stringify(input, null, 2));

      // Execute the task
      const result = await this.executeTask(task, input);
      this.logger?.log("Result: ", JSON.stringify(result, null, 2));

      this.results.set(task.name, result);
    }

    // Return all results as an object
    return this.getFinalOutput();
  }

  private async executeTask(task: Task, input: Record<string, any>): Promise<any> {
    try {
      const result = task.operator.execute(input);
      if (isPromise(result)) {
        return await result;
      } else {
        return result;
      }
    } catch (error) {
      throw new Error(`Error executing task '${task.name}': ${error}`);
    }
  }

  private getFinalOutput() {
    const finalOutput: Record<string, any> = {};
    Object.entries(this.outputShape).forEach(([finalKey, refVar]) => {
      finalOutput[finalKey] = this.getVariableValue(refVar);
    });
    return finalOutput;
  }

  /**
   * Performs a topological sort of the tasks to determine execution order.
   * @returns Array of task names in execution order.
   * @throws Error if a cycle is detected in dependencies.
   */
  private topologicalSort(): string[] {
    const inDegree: Map<string, number> = new Map();

    // Initialize in-degree of all tasks to 0
    this.tasks.forEach((_, task) => {
      inDegree.set(task, 0);
    });

    // Populate in-degree map based on dependencies
    this.dependencies.forEach((deps, task) => {
      deps.forEach((dep) => {
        if (!this.tasks.has(dep)) {
          throw new Error(`Dependency task '${dep}' does not exist.`);
        }
        inDegree.set(task, inDegree.get(task)! + 1);
      });
    });

    const queue: string[] = [];

    // Enqueue tasks with in-degree 0
    inDegree.forEach((degree, task) => {
      if (degree === 0) {
        queue.push(task);
      }
    });

    const sorted: string[] = [];

    while (queue.length > 0) {
      const current = queue.shift()!;
      sorted.push(current);

      // Iterate through tasks that depend on 'current'
      this.dependencies.forEach((deps, task) => {
        if (deps.has(current)) {
          // Decrement in-degree of the dependent task
          inDegree.set(task, inDegree.get(task)! - 1);
          // If in-degree becomes 0, add to queue
          if (inDegree.get(task)! === 0) {
            queue.push(task);
          }
        }
      });
    }

    // If sorted tasks count doesn't match total tasks, a cycle exists
    if (sorted.length !== this.tasks.size) {
      throw new Error("Cycle detected in task dependencies.");
    }

    return sorted;
  }
}
