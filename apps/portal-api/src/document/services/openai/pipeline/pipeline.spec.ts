import { Pipeline } from "./pipeline";
import { Operation } from "./task";

interface TaskInputs {
  [key: string]: any;
}

interface TaskOutputs {
  [key: string]: any;
}

describe("Pipeline", () => {
  let consoleLogSpy: jest.SpyInstance;

  beforeEach(() => {
    // Mock console.log
    consoleLogSpy = jest.spyOn(console, "log").mockImplementation();
  });

  afterEach(() => {
    // Restore console.log after each test
    consoleLogSpy.mockRestore();
  });

  test("should execute tasks in the correct order with expected outputs", async () => {
    // Initialize the pipeline
    const pipeline = new Pipeline({
      outputShape: {
        output: "ReverseString:result"
      }
    });

    // Define Task1: PrintLength
    const printLength = new Operation<TaskInputs, TaskOutputs>((inputs) => {
      const str: string = inputs.initialInput;
      console.log(`The string "${str}" contains ${str.length} characters.`);
      return {};
    });
    pipeline.addTask("PrintLength", printLength, {
      inputMap: {
        initialInput: "context:initialInput"
      }
    });

    // Define Task2: ReverseString
    const reverseString = new Operation<TaskInputs, TaskOutputs>((inputs) => {
      const str: string = inputs.initialInput;
      const reversed = str.split("").reverse().join("");
      console.log(`Reversed string: ${reversed}`);
      return { result: reversed };
    });

    pipeline.addTask("ReverseString", reverseString, {
      inputMap: {
        initialInput: "context:initialInput"
      }
    });

    // Define dependencies manually by specifying outputVars and inputVars
    // Note: The Pipeline class automatically infers dependencies based on inputVars and outputVars

    // Execute the pipeline with initial input
    const finalResults = await pipeline.invoke({ initialInput: "apple" });

    // Assertions
    expect(consoleLogSpy).toHaveBeenCalledWith('The string "apple" contains 5 characters.');
    expect(consoleLogSpy).toHaveBeenCalledWith("Reversed string: elppa");

    // Ensure that PrintLength was called before ReverseString
    const printLengthCallIndex = consoleLogSpy.mock.calls.findIndex(
      (call) => call[0] === 'The string "apple" contains 5 characters.'
    );
    const reverseStringCallIndex = consoleLogSpy.mock.calls.findIndex(
      (call) => call[0] === "Reversed string: elppa"
    );

    expect(printLengthCallIndex).toBeLessThan(reverseStringCallIndex);

    // Ensure that the reversedString is correctly set in the results
    expect(finalResults).toHaveProperty("output", "elppa");
  });
});
