export type InputShape = Record<string, string>;

export interface Task {
  name: string;
  operator: Operator<any, any>;
  inputMap: InputShape;
}

export interface Operator<
  InputVars extends Record<keyof InputShape, any>,
  OutputVars extends Record<string, any>
> {
  /**
   * Executes the task with the provided inputs.
   * @param inputs - An object containing the required input variables.
   * @returns An object containing the output variables.
   */
  execute(inputs: InputVars): OutputVars | Promise<OutputVars>;
}

export class Operation<
  InputVars extends Record<keyof InputShape, any>,
  OutputVars extends Record<string, any>
> implements Operator<InputVars, OutputVars>
{
  private readonly action: (inputs: InputVars) => OutputVars | Promise<OutputVars>;

  /**
   * Creates an instance of Operation.
   * @param action - A function that performs the task's action.
   * @param inputVars - An array of input variable names required by the task.
   * @param outputVars - An array of output variable names produced by the task.
   */
  constructor(action: (inputs: InputVars) => OutputVars | Promise<OutputVars>) {
    this.action = action;
  }

  /**
   * Executes the task's action with the provided inputs.
   * @param inputs - An object containing the required input variables.
   * @returns An object containing the output variables.
   */
  async execute(inputs: InputVars): Promise<OutputVars> {
    return this.action(inputs);
  }
}
