import { Type } from "class-transformer";
import {
  ArrayNotEmpty,
  IsArray,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
  ValidateNested
} from "class-validator";
import { IsLessThanOrEqual } from "../../../validators";

class DocumentSplitResult {
  @IsString()
  @IsNotEmpty()
  type: string;

  @IsInt()
  @IsOptional()
  typeId: number;

  @IsInt()
  @Min(1)
  @IsLessThanOrEqual("endPage")
  startPage: number;

  @IsInt()
  @Min(1)
  endPage: number;

  @IsInt()
  @Min(0)
  @IsOptional()
  organizationId: number;
}

export class DocumentSplitResultDto {
  constructor(result: { documents: DocumentSplitResult[] }) {
    this.documents = result.documents;
  }

  @ValidateNested({ each: true })
  @Type(() => DocumentSplitResult)
  @IsArray()
  @ArrayNotEmpty()
  documents: DocumentSplitResult[];

  @IsInt()
  @Min(0)
  @IsOptional()
  fileId: number;

  @IsString()
  @IsNotEmpty()
  batchId: string;
}
