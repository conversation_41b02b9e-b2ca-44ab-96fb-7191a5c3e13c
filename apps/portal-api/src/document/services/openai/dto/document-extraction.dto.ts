import { Type } from "class-transformer";
import { IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>idate<PERSON>ested } from "class-validator";

class DocumentExtractedField {
  @IsString()
  key: string;

  @IsString()
  value: string;
}

export class DocumentExtractionResult {
  constructor(fields: DocumentExtractedField[]) {
    this.fields = fields;
  }

  @ValidateNested({ each: true })
  @Type(() => DocumentExtractedField)
  @IsArray()
  fields: DocumentExtractedField[];
}
