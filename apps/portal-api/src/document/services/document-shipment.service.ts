import { DocumentFieldService } from "@/document/services/document-field.service";
import { DocumentType } from "@/document/types";
import { BadRequestException, Inject, Injectable, Logger, NotFoundException } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectRepository } from "@nestjs/typeorm";
import { Document, Shipment, UpdateDocumentShipmentDto } from "nest-modules";
import { ClsService } from "nestjs-cls";
import { Repository } from "typeorm";
import { DocumentEvent, DocumentShipmentChangedEvent } from "../events";

@Injectable()
export class DocumentShipmentService {
  private readonly logger = new Logger(DocumentShipmentService.name);

  constructor(
    private readonly documentFieldService: DocumentFieldService,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
    @InjectRepository(Shipment)
    private readonly shipmentRepository: Repository<Shipment>,
    private readonly cls: ClsService,
    @Inject(EventEmitter2)
    private readonly eventEmitter: EventEmitter2
  ) {}

  /**
   * Update the shipment of a document
   *
   * @param document - The document to update
   * @param dto - The update document shipment DTO
   * @returns The updated document
   */
  async updateDocumentShipment(document: Document, dto: UpdateDocumentShipmentDto): Promise<void> {
    const shipment = await this.shipmentRepository.findOne({
      where: { id: dto.shipmentId }
    });

    if (!shipment) {
      throw new NotFoundException("Shipment not found");
    }

    if (document.organizationId !== shipment.organizationId) {
      throw new BadRequestException("Document organization does not match shipment organization");
    }

    const result = await this.documentRepository.update(document.id, {
      shipment: {
        id: shipment.id
      } as any
    });

    if (result.affected === 0) {
      this.logger.error(`Failed to update document ${document.id} with shipment ${shipment.id}`);
      throw new BadRequestException("Failed to update document shipment");
    }

    this.eventEmitter.emit(
      DocumentEvent.SHIPMENT_CHANGED,
      new DocumentShipmentChangedEvent(document.id, shipment.id)
    );

    await this.verifyDocumentBelongsToCurrentShipment(document.id);
  }

  async verifyDocumentBelongsToCurrentShipment(documentId: number) {
    const document = await this.documentRepository.findOne({
      where: { id: documentId }
    });

    this.logger.log(
      `Verifying document belongs to current shipment: ${document.id}, shipmentId: ${document.shipmentId}`
    );

    const documentBelongsToShipment = await this.canAddToShipment(documentId, document.shipmentId);

    await this.documentRepository.update(documentId, {
      isShipmentMismatch: !documentBelongsToShipment
    });

    return documentBelongsToShipment;
  }

  /**
   * Checks if a document can be added to a given shipment.
   *
   * @param document - The document to check
   * @returns A boolean indicating if the document can be added to the shipment
   */
  async canAddToShipment(documentId: number, shipmentId: number): Promise<boolean> {
    const document = await this.documentRepository.findOne({
      where: { id: documentId },
      relations: {
        documentType: true
      }
    });

    const shipment = await this.shipmentRepository.findOne({
      where: { id: shipmentId },
      relations: {
        consignee: true,
        shipper: true
      }
    });

    // limit organization to the organization of the document
    const organizationId = document.organizationId;

    return this.cls.run(async () => {
      this.cls.set("ORGANIZATION_ID", organizationId);

      if (!shipment) {
        return false;
      }

      switch (document.documentType.name) {
        case DocumentType.COMMERCIAL_INVOICE:
        case DocumentType.PACKING_LIST:
          return this.validateInvoice(document, shipment);
        case DocumentType.AIR_WAYBILL:
        case DocumentType.HOUSE_OCEAN_BILL_OF_LADING:
        case DocumentType.ROAD_BILL_OF_LADING:
          return this.validateShipmentDocument(document, shipment);
        case DocumentType.AIR_ARRIVAL_NOTICE:
        case DocumentType.OCEAN_ARRIVAL_NOTICE:
        case DocumentType.AIR_E_MANIFEST:
        case DocumentType.OCEAN_E_MANIFEST:
          return this.validateArrivalNotice(document, shipment);
        case DocumentType.ROAD_ARRIVAL_NOTICE:
          return this.validateRoadArrivalNotice(document, shipment);
        case DocumentType.CERTIFICATE_OF_ORIGIN:
        case DocumentType.USMCA_CERTIFICATE_OF_ORIGIN:
          return true;
        default:
          return false; // If the document type is not recognized, return false
      }
    });
  }

  private async validateRoadArrivalNotice(document: Document, shipment: Shipment): Promise<boolean> {
    return (
      ((await this.validateField(document, shipment, "hblNumber", "hblNumber")) &&
        (await this.validateField(document, shipment, "CCN", "cargoControlNumber"))) ||
      (await this.validateField(document, shipment, "CCN", "hblNumber"))
    );
  }

  private async validateShipmentDocument(document: Document, shipment: Shipment): Promise<boolean> {
    const hblNumberMatch = await this.validateField(document, shipment, "hblNumber", "hblNumber");
    const containerNumberMatch = await this.validateField(
      document,
      shipment,
      "containerNumber",
      "containerNumber"
    );

    return hblNumberMatch && containerNumberMatch;
  }

  private async validateArrivalNotice(document: Document, shipment: Shipment): Promise<boolean> {
    return (
      (await this.validateField(document, shipment, "hblNumber", "hblNumber")) &&
      (await this.validateField(document, shipment, "containerNumber", "containerNumber"))
    );
  }

  private async getShipmentField(shipment: Shipment, field: string): Promise<string | null> {
    return shipment[field] !== null && shipment[field] !== undefined ? shipment[field] : null;
  }

  private async validateField(
    document: Document,
    shipment: Shipment,
    field: string,
    shipmentField: string
  ): Promise<boolean> {
    const value = await this.documentFieldService.getFieldValue(document.id, field);
    const shipmentValue = await this.getShipmentField(shipment, shipmentField);
    const canMatch = value === null || shipmentValue === null || value === shipmentValue;

    this.logger.debug(
      `[${canMatch ? "OKAY" : "FAIL"}] Document: ${field}: ${value}, Shipment: ${shipmentField}: ${shipmentValue}`
    );

    return canMatch;
  }

  private async validateTradePartnerMatch(
    document: Document,
    shipment: Shipment,
    field: string,
    shipmentField: string
  ): Promise<boolean> {
    // FIXME: this is a temporary fix to allow the document to be added to the shipment
    return true;

    // // const tradePartner = await this.documentFieldService.getFieldValue<TradePartner>(document.id, field);

    // this.logger.debug(
    //   `${field}:\n${JSON.stringify(tradePartner)}\n${shipmentField}:\n${JSON.stringify(shipment[shipmentField])}`
    // );

    // if (tradePartner == null || shipment[shipmentField] == null) {
    //   return true;
    // }

    // const similarity = trigramSimilarity(tradePartner.name, shipment[shipmentField].name);

    // this.logger.debug(`${field} similarity: ${similarity}`);

    // return similarity > 0.6;
  }

  private async validateInvoice(document: Document, shipment: Shipment): Promise<boolean> {
    return (
      (await this.validateTradePartnerMatch(document, shipment, "vendor", "shipper")) &&
      (await this.validateTradePartnerMatch(document, shipment, "shipTo", "consignee"))
    );
  }
}
