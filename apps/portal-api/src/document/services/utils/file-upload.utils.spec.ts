import { getUniqueNameForFile, getDestinationFolder } from "./file-upload.utils";

describe("FileUploadUtils", () => {
  beforeEach(() => {
    jest.spyOn(global.Date, "now").mockReturnValue(1729704349657);
    jest.spyOn(Date.prototype, "getFullYear").mockReturnValue(2024);
    jest.spyOn(Date.prototype, "getMonth").mockReturnValue(9);
  });

  it("should get the correct destination folder", () => {
    const destinationFolder = getDestinationFolder(1);
    expect(destinationFolder).toBe("O1/2024/10");
  });

  const fileNameFixtures = [
    { original: "test.pdf", num: 0, expected: "test-6E9WGg.pdf" },
    { original: "test.pdf", num: 1, expected: "test-_BYPDg.pdf" },
    { original: "test", num: 0, expected: "test-QaFinA" }
  ];

  test.each(fileNameFixtures)("should generate unique name for file %s", ({ original, num, expected }) => {
    const uniqueName = getUniqueNameForFile(original, num);
    expect(uniqueName).toBe(expected);
  });

  it("should throw error if original filename is empty", () => {
    expect(() => getUniqueNameForFile("", 0)).toThrow("Original filename is required");
  });
});
