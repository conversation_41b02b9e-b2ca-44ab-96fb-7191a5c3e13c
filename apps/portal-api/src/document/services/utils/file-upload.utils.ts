import { createHash } from "crypto";

export function getUniqueNameForFile(originalFilename: string, num: number) {
  if (!originalFilename || originalFilename.length === 0) {
    throw new Error("Original filename is required");
  }

  const suffix = createHash("shake256", { outputLength: 4 })
    .update(`${Date.now()}-${num}-${originalFilename}`)
    .digest("base64url");

  return originalFilename.replace(/^([^.]+)$|(\.[^.]+)$/i, `$1-${suffix}\$2`);
}

export function getDestinationFolder(organizationId: number) {
  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth() + 1;

  return [`O${organizationId}`, year, month].join("/");
}
