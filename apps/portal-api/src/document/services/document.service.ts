import { FieldsToObjectAdapter } from "@/aggregation/adapter/fields-to-object.adapter";
import { DocumentType } from "@/document/types/document-types";
import { forwardRef, Inject, Injectable, Logger, NotFoundException } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectRepository } from "@nestjs/typeorm";
import {
  BatchSaveFileDocumentsDto,
  BatchUpdateDocumentFieldsDto,
  BatchUpdateDocumentFieldsResponseDto,
  Document,
  DOCUMENT_ENUM_KEYS,
  DocumentColumn,
  DocumentField,
  DocumentStatus,
  DocumentTypeService,
  DocumentValidationError,
  File,
  FilePage,
  FIND_DOCUMENT_RELATIONS,
  GetDocumentsDto,
  GetDocumentsResponseDto,
  getFindOptions,
  SetDocumentVisibilityDto,
  TransactionalEventEmitterService,
  UserPermission
} from "nest-modules";
import { ClsService } from "nestjs-cls";
import { fromEvent, map, Observable, startWith, takeWhile } from "rxjs";
import { FindOptionsRelations, In, QueryRunner, Raw, Repository } from "typeorm";
import { z, ZodIssue } from "zod";
import {
  DocumentCreatedEvent,
  DocumentEvent,
  DocumentShipmentChangedEvent,
  DocumentUpdatedEvent
} from "../events";
import { inOrgCls } from "../helpers/scoped-query.helper";
import { STRICT_TYPE_SCHEMA_MAP, TYPE_SCHEMA_MAP } from "../types/document-types.zod";
import { DocumentFieldService } from "./document-field.service";
import { DocumentSplitResultDto } from "./openai/dto/document-split.dto";

@Injectable()
export class DocumentService {
  private readonly logger = new Logger(DocumentService.name);

  constructor(
    @Inject(ClsService)
    private readonly cls: ClsService,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
    @InjectRepository(DocumentValidationError)
    private readonly documentValidationErrorRepository: Repository<DocumentValidationError>,
    @Inject(DocumentFieldService)
    private readonly documentFieldService: DocumentFieldService,
    @InjectRepository(FilePage)
    private readonly filePageRepository: Repository<FilePage>,
    @Inject(forwardRef(() => DocumentTypeService))
    private readonly documentTypeService: DocumentTypeService,
    private readonly eventEmitter: EventEmitter2,
    @Inject(TransactionalEventEmitterService)
    private readonly transactionalEventEmitter: TransactionalEventEmitterService
  ) {}

  /**
   * Get a document by id
   *
   * @param id - The document id
   * @param relations - The relations to include
   * @returns The document
   */
  async getDocument(id: number, relations: FindOptionsRelations<Document> = {}): Promise<Document> {
    const scopedWhere = inOrgCls(
      {
        where: {
          id: id
        }
      },
      this.cls,
      true
    );

    const document = await this.documentRepository.findOne({
      ...scopedWhere,
      relations: {
        pages: true,
        validationErrors: true,
        ...FIND_DOCUMENT_RELATIONS,
        ...relations
      },
      order: {
        pages: {
          documentPage: "ASC"
        }
      }
    });

    if (!document) {
      this.logger.debug("Document not found, id: " + id);
      throw new NotFoundException("Document not found");
    }

    return document;
  }

  /**
   * Get the document data as a typed object
   *
   * @param id - The document id
   * @returns The document data as a typed object
   */
  async getDocumentData<T>(id: number): Promise<{ documentType: DocumentType; data: T }> {
    return this.documentFieldService.getDocumentData<T>(id);
  }

  /**
   * Map the document to a typed object
   *
   * @deprecated
   * @param document - The document
   * @returns The document data as a typed object
   */
  async mapToDocumentData(document: Document) {
    const documentData = new FieldsToObjectAdapter(document.fields).toObject();

    if (!document.documentType?.name) {
      return {
        documentType: null,
        data: documentData
      };
    }

    const documentType = document.documentType.name as DocumentType;

    const zodSchema = TYPE_SCHEMA_MAP[documentType];
    // TODO: save errors and display them in the UI
    return {
      documentType,
      data: zodSchema.parse(documentData)
    };
  }

  /**
   * Delete a document by id
   *
   * @param id - The document id
   * @returns The deleted document
   */
  async deleteDocument(id: number) {
    const document = await this.getDocument(id);
    return await this.documentRepository.remove(document);
  }

  /**
   * Get all documents by ids
   *
   * @param ids - The document ids
   * @returns The documents
   */
  async getAllDocumentByIds(ids: number[]) {
    if (ids.length === 0) {
      return [];
    }
    return await this.documentRepository.find({ where: { id: In(ids) } });
  }

  /**
   * Get documents
   *
   * @param getDocumentsDto - The get documents dto
   * @returns The documents
   */
  async getDocuments(getDocumentsDto: GetDocumentsDto): Promise<GetDocumentsResponseDto> {
    // scope to the organization of the user
    if (this.cls.get("USER_PERMISSION") !== UserPermission.BACKOFFICE_ADMIN)
      getDocumentsDto.organizationId = this.cls.get("ORGANIZATION_ID") || -1;

    const { where, skip } = getFindOptions(getDocumentsDto, DOCUMENT_ENUM_KEYS, ["name"], DocumentColumn.id);

    let { order, take } = getFindOptions(getDocumentsDto, DOCUMENT_ENUM_KEYS, ["name"], DocumentColumn.id);

    take = 100;

    // sort by isHidden (false first)
    // then sort by extracting, pending, extracted
    order = {
      isHidden: "ASC",
      status: Raw(
        `FIELD(status, '${DocumentStatus.EXTRACTING}', '${DocumentStatus.PENDING}', '${DocumentStatus.EXTRACTED}')`
      ),
      name: "ASC",
      createDate: "DESC",
      ...order
    };

    const [documents, total] = await this.documentRepository.findAndCount({
      where,
      order,
      skip,
      take,
      relations: {
        file: {
          batch: true
        },
        documentType: true,
        createdBy: true,
        lastEditedBy: true,
        organization: true,
        aggregation: true,
        reference: {
          file: false
        },
        referencedBy: {
          file: false
        }
      }
    });

    return {
      documents,
      total,
      skip,
      limit: take
    };
  }

  async createDocumentsFromSplitResult(
    result: DocumentSplitResultDto,
    shipmentId?: number
  ): Promise<Document[]> {
    this.logger.debug(result.documents.length + " documents will be saved");
    // create documents
    const documentsToSave = result.documents.map((docResult) => {
      const document = new Document();
      document.name = docResult.type;
      document.startPage = docResult.startPage;
      document.endPage = docResult.endPage;
      document.status = DocumentStatus.PENDING;
      document.shipmentId = shipmentId;

      // create associations
      document.file = { id: result.fileId, batchId: result.batchId } as any;
      document.documentType = { id: docResult.typeId } as any;
      document.organization = { id: docResult.organizationId } as any;
      document.isHidden = docResult.type === "other" || docResult.type === "unknown";
      return document;
    });

    const documents = await this.createDocuments(documentsToSave, undefined, false);

    // automatically associate the CI and PL if they exist in the same file
    this.logger.debug("Associating CI and PL if they exist in the same file");
    const discoveredTypes = new Map<string, number>();

    for (const doc of documents) {
      discoveredTypes.set(doc.name, doc.id);
    }

    if (
      discoveredTypes.size === 2 &&
      discoveredTypes.has("PACKING_LIST") &&
      discoveredTypes.has("COMMERCIAL_INVOICE")
    ) {
      const ciId = discoveredTypes.get("COMMERCIAL_INVOICE");
      const plId = discoveredTypes.get("PACKING_LIST");

      this.logger.debug("CI ID: " + ciId);
      this.logger.debug("PL ID: " + plId);

      await this.documentRepository.update(plId, { reference: { id: ciId } as any });
    }

    return documents;
  }

  /**
   * Create documents
   *
   * @param documents - The documents to create
   * @param queryRunner - The query runner
   * @param triggerExtraction - Whether to trigger the extraction
   * @returns The created documents
   */
  private async createDocuments(
    documents: Document[],
    queryRunner?: QueryRunner,
    triggerExtraction = true
  ): Promise<Document[]> {
    const documentRepository = queryRunner
      ? queryRunner.manager.getRepository(Document)
      : this.documentRepository;

    const savedDocuments = await documentRepository.save(documents);

    // go through start, end page, set documentPage to the file page
    for (const document of savedDocuments) {
      for (let page = document.startPage; page <= document.endPage; page++) {
        const documentPage = page - document.startPage + 1;
        await this.filePageRepository.update(
          { page: page, fileId: document.fileId },
          { documentId: document.id, documentPage: documentPage }
        );
      }
    }

    savedDocuments.forEach((doc) => {
      const payload = new DocumentCreatedEvent(
        doc.id,
        doc.fileId,
        doc.organizationId,
        doc.file.batchId,
        doc.name,
        triggerExtraction
      );

      this.transactionalEventEmitter.enqueueEvent(DocumentEvent.CREATED, queryRunner, payload);
    });

    return savedDocuments;
  }

  /**
   * Delete documents by file id
   *
   * @param fileId - The file id
   */
  async deleteDocumentsByFileId(fileId: number): Promise<void> {
    const documents = await this.documentRepository.find({
      where: { file: { id: fileId } }
    });
    await this.documentRepository.remove(documents);
  }

  async dismissDocumentShipmentMismatchWarning(id: number) {
    // we use this because we don't have rbac
    await this.getDocument(id);
    return this.documentRepository.update(id, { isShipmentMismatch: false });
  }

  /**
   * Removes all documents associated with a file
   *
   * @param fileId - The file id
   * @param queryRunner - The query runner
   */
  async removeFileDocuments(fileId: number, queryRunner?: QueryRunner) {
    const documentRepository = queryRunner
      ? queryRunner.manager.getRepository(Document)
      : this.documentRepository;

    const documents = await documentRepository.find({
      where: { file: { id: fileId } }
    });

    await documentRepository.remove(documents);
  }

  private async updateDocument(id: number, document: Document, queryRunner?: QueryRunner) {
    const documentRepository = queryRunner
      ? queryRunner.manager.getRepository(Document)
      : this.documentRepository;

    const payload = new DocumentUpdatedEvent(
      document.id,
      document.fileId,
      document.organizationId,
      document.file.batchId,
      document.name
    );

    this.transactionalEventEmitter.enqueueEvent(DocumentEvent.UPDATED, queryRunner, payload);

    await documentRepository.update(id, document);
  }

  /**
   * Replaces the documents in the file
   *
   * @param fileId - The file id
   * @param documents - The documents to save
   * @returns The saved documents
   */
  async replaceFileDocuments(fileId: number, changeSet: BatchSaveFileDocumentsDto) {
    const documentTypeNames = await this.documentTypeService.getAllDocumentTypes();

    const queryRunner = this.documentRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const fileRepository = queryRunner.manager.getRepository(File);
    const documentRepository = queryRunner.manager.getRepository(Document);
    const documentPageRepository = queryRunner.manager.getRepository(FilePage);

    const file = await fileRepository.findOne({
      where: { id: fileId },
      select: { id: true, organizationId: true }
    });

    if (!file) {
      await queryRunner.rollbackTransaction();
      await queryRunner.release();
      throw new NotFoundException("File not found");
    }

    try {
      // documentPage will be set to null by the cascade
      if (changeSet.deleted.length > 0) {
        await documentRepository.delete(changeSet.deleted);
      }

      // get the affected document ids
      const affectedDocumentIds = changeSet.updated.flatMap((doc) => doc.id);

      // set the documentPage to null for the affected page ids
      await documentPageRepository.update({ documentId: In(affectedDocumentIds) }, { documentPage: null });

      // construct upsert payloads
      const updateDocumentPayloads = changeSet.updated.map((doc) => {
        const documentType = documentTypeNames.find((type) => type.id === doc.documentTypeId);
        return {
          id: doc.id,
          name: documentType.name,
          startPage: doc.startPage,
          documentType: { id: doc.documentTypeId }
        };
      });

      const upsertPagePayloads = changeSet.updated.flatMap((doc) =>
        doc.pageIds.map((pageId, index) => ({
          id: pageId,
          documentId: doc.id,
          documentPage: index + 1
        }))
      );

      await Promise.all([
        ...updateDocumentPayloads.map(({ id, ...payload }) => documentRepository.update(id, payload)),
        ...upsertPagePayloads.map(({ id, ...payload }) => documentPageRepository.update(id, payload))
      ]);

      await documentRepository.save(
        changeSet.created.map((doc) => {
          const documentType = documentTypeNames.find((type) => type.id === doc.documentTypeId);
          return {
            name: documentType.name,
            startPage: doc.startPage,
            file: { id: file.id },
            organization: { id: file.organizationId },
            documentType: { id: doc.documentTypeId },
            pages: doc.pageIds.map((pageId, index) => ({ id: pageId, documentPage: index + 1 }))
          };
        })
      );

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get all documents by file id
   *
   * @param fileId - The file id
   * @param queryRunner - The query runner
   * @returns The documents
   */
  private async getAllDocumentsByFileId(fileId: number, queryRunner?: QueryRunner) {
    const documentRepository = queryRunner
      ? queryRunner.manager.getRepository(Document)
      : this.documentRepository;

    return documentRepository.find({ where: { file: { id: fileId } } });
  }

  async setDocumentVisibility(id: number, dto: SetDocumentVisibilityDto) {
    return this.documentRepository.update(id, { isHidden: dto.isHidden });
  }

  /**
   * Replaces the document fields with the new fields, preserving locked fields
   *
   * @param id - The document id
   * @param fields - The new fields
   * @param overrideLocked - Whether to override locked fields
   * @returns The updated document
   */
  async setDocumentFields(id: number, fields: DocumentField[], overrideLocked = false) {
    const document = await this.getDocument(id);

    const queryRunner = this.documentRepository.manager.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const documentFieldRepository = queryRunner.manager.getRepository(DocumentField);
      // set database lock on the document fields
      const oldFields = await documentFieldRepository
        .createQueryBuilder("document_field")
        .where("document_field.documentId = :id", { id })
        .setLock("pessimistic_write")
        .getMany();

      // remove the old fields that are not in the new fields
      const fieldsToBeRemoved = oldFields.filter((oldField) => {
        const fieldIsNotInNewFields = !fields.some(
          (f) => f.name === oldField.name && f.dataType === oldField.dataType
        );
        const oldFieldIsLocked = oldField.locked;

        return fieldIsNotInNewFields && (!oldFieldIsLocked || overrideLocked);
      });

      const fieldsToBeSaved: DocumentField[] = [];

      fields.forEach((newField) => {
        const oldField = oldFields.find((f) => f.name === newField.name && f.dataType === newField.dataType);
        fieldsToBeSaved.push({
          id: oldField?.id,
          value: oldField?.locked && !overrideLocked ? oldField.value : newField.value,
          originalValue: oldField?.locked && !overrideLocked ? oldField.value : newField.value,
          name: newField.name,
          dataType: newField.dataType,
          document: { id: document.id } as any
        } as DocumentField);
      });

      this.logger.verbose("Fields to be saved: " + JSON.stringify(fieldsToBeSaved));
      this.logger.verbose("Fields to be removed: " + JSON.stringify(fieldsToBeRemoved));

      await documentFieldRepository.upsert(fieldsToBeSaved, { conflictPaths: ["name", "document"] });

      if (fieldsToBeRemoved.length > 0) {
        await documentFieldRepository.delete(fieldsToBeRemoved.map((field) => field.id));
      }

      const updatedFields = await documentFieldRepository.find({ where: { document: { id } } });

      document.fields = updatedFields;
      await this.validateDocumentFields(document, queryRunner);
      // const isValidationSuccess = await this.validateDocumentFields(document, queryRunner);
      // FIXME: right now we will bypass set the document status so the document can be proceed to the next step
      // @see SCRUM-771
      // document.status = isValidationSuccess ? DocumentStatus.EXTRACTED : DocumentStatus.VALIDATING;

      await queryRunner.commitTransaction();

      return document;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getDocumentFieldValidationErrors(id: number) {
    return this.documentValidationErrorRepository.find({ where: { document: { id } } });
  }

  /**
   * Updates the document fields with the new fields
   */
  async updateDocumentFields(
    id: number,
    fields: BatchUpdateDocumentFieldsDto
  ): Promise<BatchUpdateDocumentFieldsResponseDto> {
    const document = await this.getDocument(id);

    fields.edit?.forEach((field) => {
      const existingField = document.fields.find((f) => f.id === field.id);
      if (existingField) {
        existingField.name = field.name;
        existingField.dataType = field.dataType;
        existingField.value = field.value;
        existingField.locked = field.locked;
      }
    });

    fields.delete?.forEach((id) => {
      const existingField = document.fields.find((f) => f.id === id);
      if (existingField) {
        document.fields = document.fields.filter((f) => f.id !== id);
      }
    });

    fields.create?.forEach((field) => {
      const newField = new DocumentField();
      newField.name = field.name;
      newField.dataType = field.dataType;
      newField.value = field.value;
      newField.locked = field.locked;
      document.fields.push(newField);
    });

    const savedDocument = await this.documentRepository.save(document);

    await this.validateDocumentFields(savedDocument);

    return {
      documentFields: savedDocument.fields
    };
  }

  /**
   * Validates the document fields
   *
   * TODO: move to a separate service
   * @param document - The document
   * @returns Whether the document fields are valid
   */
  public async validateDocumentFields(document: Document, queryRunner?: QueryRunner): Promise<boolean> {
    const zodSchema = STRICT_TYPE_SCHEMA_MAP[document.documentType.name as DocumentType];
    const parsed = zodSchema.safeParse(this.getDocumentJson(document));

    const documentValidationErrorRepository = queryRunner
      ? queryRunner.manager.getRepository(DocumentValidationError)
      : this.documentValidationErrorRepository;

    const documentRepository = queryRunner
      ? queryRunner.manager.getRepository(Document)
      : this.documentRepository;

    await documentValidationErrorRepository.delete({ document: { id: document.id } });

    // FIXME: right now we will bypass set the document status so the document can be proceed to the next step
    // @see SCRUM-771
    // document.status = parsed.success ? DocumentStatus.EXTRACTED : DocumentStatus.VALIDATING;
    document.status = DocumentStatus.EXTRACTED;

    await documentRepository.update(document.id, {
      status: document.status
    });

    if (!parsed.success) {
      const validationErrors = parsed.error.issues.map((error: ZodIssue) => {
        return {
          path: error.path,
          code: error.code === z.ZodIssueCode.custom ? error.message : error.code
        };
      });
      await documentValidationErrorRepository.insert(
        validationErrors.map((error) => ({
          ...error,
          document: { id: document.id } as any
        }))
      );
    }

    return parsed.success;
  }

  async associateWithShipment(documentId: number, shipmentId: number, queryRunner?: QueryRunner) {
    console.log("ASSOCIATE WITH SHIPMENT");
    const documentRepository = queryRunner
      ? queryRunner.manager.getRepository(Document)
      : this.documentRepository;

    this.logger.debug(`Associating document ${documentId} with shipment ${shipmentId}`);
    const document = await this.getDocument(documentId);

    document.shipment = { id: shipmentId } as any;
    await documentRepository.save(document);

    // this means that a shipment is already exist in our system
    if (!queryRunner) {
      await this.eventEmitter.emitAsync(
        DocumentEvent.SHIPMENT_CHANGED,
        new DocumentShipmentChangedEvent(document.id, shipmentId)
      );
    }

    return document;
  }

  /**
   * Get the extracted fields as a JSON object
   *
   * @param id - The document id
   * @returns The document fields as a JSON object
   */
  async getDocumentJsonData(id: number) {
    const document = await this.getDocument(id);
    return this.getDocumentJson(document);
  }

  private getDocumentJson(document: Document) {
    // document field is a keyvalue, we need to convert it to a json object
    return document.fields.reduce((acc, field) => {
      const isJson = field.dataType === "array" || field.dataType === "object";
      const isNumber = field.dataType === "number";
      acc[field.name] = isNumber ? parseFloat(field.value) : isJson ? JSON.parse(field.value) : field.value;
      return acc;
    }, {});
  }

  async updateDocumentJobId(id: number, jobId: string) {
    return this.documentRepository.update(id, { jobId });
  }

  async updateDocumentStatusByJobId(jobId: string, status: DocumentStatus) {
    return await this.documentRepository.update({ jobId }, { status });
  }

  /**
   * Updates the document status
   *
   * @param id - The document id
   * @param status - The new status
   */
  async updateDocumentStatus(id: number, status: DocumentStatus) {
    const updateResult = await this.documentRepository.update(
      { id },
      {
        status: status
      }
    );

    if (!updateResult.affected) {
      throw new NotFoundException("Failed to update document status");
    }

    this.eventEmitter.emit(`document.status-updated:${id}`, {
      status: status
    });
  }

  /**
   * Subscribe to the document processing progress
   *
   * @param id - The document id
   * @returns The document processing progress
   */
  async subscribeToDocumentProcessingProgress(id: number): Promise<Observable<MessageEvent>> {
    const document = await this.getDocument(id);

    return fromEvent(this.eventEmitter, `document.status-updated:${document.id}`).pipe(
      startWith({ status: document.status }),
      map((payload: { status: DocumentStatus }) => {
        document.status = payload.status;
        return {
          data: payload
        } as MessageEvent<{ status: DocumentStatus }>;
      }),
      takeWhile((event: MessageEvent<{ status: DocumentStatus }>) => document.isProcessing(), true)
    );
  }
}
