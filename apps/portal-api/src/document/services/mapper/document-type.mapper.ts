import { DocumentType } from "nest-modules";
import { DocumentExtractionConfig } from "../openai/document-extraction-config";

export class DocumentTypeMapper {
  static mapToConfig(documentType: DocumentType): DocumentExtractionConfig {
    const config = new DocumentExtractionConfig();
    config.type = DocumentTypeMapper.mapName(documentType.name);
    config.typeSpecificPromptTemplate = documentType.promptTemplate;
    config.fields = documentType.fields.map((field) => ({
      name: DocumentTypeMapper.mapName(field.name),
      isMandatory: field.isMandatory,
      type: field.dataType,
      description: field.description
    }));
    return config;
  }

  // Currently we map the name as is
  static mapName(name: string) {
    return name;
  }

  static mapToNameIdPair(documentTypes: DocumentType[]): Record<string, number> {
    return documentTypes.reduce(
      (acc, docType) => {
        acc[DocumentTypeMapper.mapName(docType.name)] = docType.id;
        return acc;
      },
      {} as Record<string, number>
    );
  }
}
