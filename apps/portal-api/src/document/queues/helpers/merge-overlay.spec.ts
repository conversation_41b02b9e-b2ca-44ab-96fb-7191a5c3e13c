import { ExtractedOverlay } from "@/document/services/openai/extract-overlay.llmtask";
import { mergeOverlays } from "./merge-overlay";

describe("mergeOverlays", () => {
  it("should return null when all overlays have null values", () => {
    const overlays: ExtractedOverlay[] = [
      {
        page: 1,
        data: {
          portOfEntry: null,
          etaPort: null,
          etd: null,
          CCN: null
        }
      },
      {
        page: 2,
        data: {
          portOfEntry: null,
          etaPort: null,
          etd: null,
          CCN: null
        }
      }
    ];

    const result = mergeOverlays(overlays);
    expect(result).toBeNull();
  });

  it("should return null when all are undefined", () => {
    const overlays: ExtractedOverlay[] = [
      {
        page: 1,
        data: {
          portOfEntry: undefined,
          etaPort: undefined,
          etd: undefined,
          CCN: undefined
        }
      }
    ];

    const result = mergeOverlays(overlays);
    expect(result).toBeNull();
  });

  it("should merge overlays and take the first non-null value for each field", () => {
    const overlays: ExtractedOverlay[] = [
      {
        page: 1,
        data: {
          portOfEntry: "Sarnia",
          etaPort: null,
          etd: "2023-01-01",
          CCN: null
        }
      },
      {
        page: 2,
        data: {
          portOfEntry: "Sarnia",
          etaPort: "2023-01-15",
          etd: "2023-01-02",
          CCN: "23ABPARS123456"
        }
      }
    ];

    const result = mergeOverlays(overlays);
    expect(result).toEqual({
      portOfEntry: "Sarnia",
      etaPort: "2023-01-15",
      etd: "2023-01-01",
      CCN: "23ABPARS123456"
    });
  });

  it("should handle empty array of overlays", () => {
    const result = mergeOverlays([]);
    expect(result).toBeNull();
  });

  it("should handle a single overlay", () => {
    const overlay: ExtractedOverlay = {
      page: 1,
      data: {
        portOfEntry: "Sarnia",
        etaPort: "2023-02-15",
        etd: null,
        CCN: "23ABPARS123456"
      }
    };

    const result = mergeOverlays([overlay]);
    expect(result).toEqual({
      portOfEntry: "Sarnia",
      etaPort: "2023-02-15",
      etd: null,
      CCN: "23ABPARS123456"
    });
  });

  it("should handle overlays with some null data fields", () => {
    const overlays: ExtractedOverlay[] = [
      {
        page: 1,
        data: {
          portOfEntry: null,
          etaPort: null,
          etd: "2023-03-01",
          CCN: "23ABPARS123456"
        }
      },
      {
        page: 2,
        data: {
          portOfEntry: "Windsor",
          etaPort: null,
          etd: null,
          CCN: null
        }
      }
    ];

    const result = mergeOverlays(overlays);
    expect(result).toEqual({
      portOfEntry: "Windsor",
      etaPort: null,
      etd: "2023-03-01",
      CCN: "23ABPARS123456"
    });
  });

  it("should handle overlays with completely different sets of data", () => {
    const overlays: ExtractedOverlay[] = [
      {
        page: 1,
        data: {
          portOfEntry: "Sarnia",
          etaPort: null,
          etd: null,
          CCN: null
        }
      },
      {
        page: 2,
        data: {
          portOfEntry: null,
          etaPort: "2023-04-15",
          etd: null,
          CCN: null
        }
      },
      {
        page: 3,
        data: {
          portOfEntry: null,
          etaPort: null,
          etd: "2023-04-01",
          CCN: null
        }
      },
      {
        page: 4,
        data: {
          portOfEntry: null,
          etaPort: null,
          etd: null,
          CCN: "23ABPARS123456"
        }
      }
    ];

    const result = mergeOverlays(overlays);
    expect(result).toEqual({
      portOfEntry: "Sarnia",
      etaPort: "2023-04-15",
      etd: "2023-04-01",
      CCN: "23ABPARS123456"
    });
  });
});
