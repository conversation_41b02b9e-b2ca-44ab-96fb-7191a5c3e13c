import { ExtractedOverlay } from "@/document/services/openai/extract-overlay.llmtask";

function getFirstValue(set: Set<string>) {
  return set.size > 0 ? set.values().next().value : null;
}

export interface MergedOverlay {
  portOfEntry: string | null;
  etaPort: string | null;
  etd: string | null;
  CCN: string | null;
}

export function mergeOverlays(overlays: ExtractedOverlay[]): MergedOverlay {
  const allData = {
    portOfEntry: new Set<string>(),
    etaPort: new Set<string>(),
    etd: new Set<string>(),
    CCN: new Set<string>()
  };

  overlays.forEach((o) => {
    for (const key of Object.keys(allData)) {
      const value = o.data[key];
      if (value === undefined || value === null) continue;
      allData[key].add(value);
    }
  });

  const aggregatedFields: MergedOverlay = {
    portOfEntry: getFirstValue(allData.portOfEntry),
    etaPort: getFirstValue(allData.etaPort),
    etd: getFirstValue(allData.etd),
    CCN: getFirstValue(allData.CCN)
  };

  if (Object.values(aggregatedFields).every((value) => value === null)) {
    return null;
  }

  return aggregatedFields;
}
