import { Job } from "bullmq";
import { FileBatchCreator } from "nest-modules";
import { DocumentJobResult } from "./document-extraction.interfaces";

export enum FileBatchSteps {
  PARSE_FILES,
  WAIT_PARSE_FILES,
  EXTRACT_OVERLAY,
  WAIT_EXTRACT_OVERLAY,
  OUTPUT_RESULTS,
  FINISH
}

export interface FileBatchJobResult {
  fileBatchId: string;
  organizationId: number;
  creator: FileBatchCreator;
  documents: any[];
}

export interface FileBatchJobData {
  currentStep?: FileBatchSteps;
  fileBatchId: string;
  organizationId: number;
  fileIds: number[];
  shipmentId?: number;
  documents: DocumentJobResult[];
}

export type FileBatchJob = Job<FileBatchJobData, FileBatchJobResult, string>;
