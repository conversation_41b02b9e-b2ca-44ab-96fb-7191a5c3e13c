import { Job } from "bullmq";
import { DocumentStatus } from "nest-modules";

export interface DocumentJobData {
  documentId: number;
  documentType: string;
  organizationId: number;
  batchId?: string;
  model?: string;
}

export interface DocumentJobResult {
  id: number;
  type: string;
  name: string;
  status: DocumentStatus;

  context: {
    organizationId: number;
    batchId: string;
  };
}

export type DocumentJob = Job<DocumentJobData, DocumentJobResult, string>;
