import { Job } from "bullmq";
import { DocumentJobResult } from "./document-extraction.interfaces";

export interface FileProcessorJobData {
  currentStep?: FileProcessorSteps;
  fileId: number;
  organizationId: number;
  shipmentId?: number;
  batchId?: string;
}

export type FileProcessorJobResult = DocumentJobResult[];

export type FileProcessorJob = Job<FileProcessorJobData, FileProcessorJobResult, string>;

export enum FileProcessorSteps {
  PARSE,
  SPLIT,
  EXTRACT_DOCUMENTS,
  WAIT_ALL_DOCUMENTS_EXTRACTED,
  ALL_DOCUMENTS_EXTRACTED,
  FINISH
}
