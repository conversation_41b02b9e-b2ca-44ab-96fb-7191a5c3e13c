import { Processor, WorkerHost } from "@nestjs/bullmq";
import { Inject } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Document, DocumentField, DocumentStatus, DocumentTypeService, File, FilePage } from "nest-modules";
import { DataSource, Repository } from "typeorm";
import { DocumentTypeMapper } from "../services/mapper/document-type.mapper";
import { ExtractOverlayLlmTask } from "../services/openai/extract-overlay.llmtask";
import { LlamaParseResultReader } from "../services/parsers/readers/llama-parse-result.reader";
import { Queue as DocumentQueue } from "./constants";
import { mergeOverlays } from "./helpers/merge-overlay";
import { FileOverlayJob } from "./interfaces";

@Processor({
  name: DocumentQueue.FILE_OVERLAY_TASKS
})
export class FileOverlayProcessor extends WorkerHost {
  constructor(
    @Inject(ExtractOverlayLlmTask)
    private readonly extractOverlayLlmTask: ExtractOverlayLlmTask,
    @Inject(DataSource)
    private readonly dataSource: DataSource,
    @Inject(DocumentTypeService)
    private readonly documentTypeService: DocumentTypeService,
    @InjectRepository(FilePage)
    private readonly filePageRepository: Repository<FilePage>
  ) {
    super();
  }

  async process(job: FileOverlayJob, token: string): Promise<any> {
    const { data } = job;

    const file = await this.dataSource.manager.getRepository(File).findOne({
      where: { id: data.fileId }
    });

    if (
      file.mimeType.startsWith("openoffice") ||
      file.mimeType.startsWith("application/vnd.openxmlformats-officedocument") ||
      file.mimeType.startsWith("application/vnd.ms-excel")
    ) {
      job.log(`Skipping overlay extraction for file ${file.id} with mime type ${file.mimeType}`);
      return;
    }

    const shipmentId = file.shipmentId;

    return await this.extractOverlay(file, shipmentId);
  }

  private async extractOverlay(file: File, shipmentId?: number) {
    const documentTypeNameIdPairs = await this.documentTypeService
      .getAllDocumentTypes()
      .then(DocumentTypeMapper.mapToNameIdPair);

    const pages = LlamaParseResultReader.load(JSON.parse(file.parseResult)).getPages();

    const overlays = await this.extractOverlayLlmTask.extractOverlay(pages, file.path);

    const aggregatedFields = mergeOverlays(overlays);

    // only save the document if there are any aggregated fields
    if (aggregatedFields !== null) {
      // save as document as well
      const document = new Document();
      document.name = "PARS_OVERLAY";
      document.organizationId = file.organizationId;
      document.fileId = file.id;
      document.documentType = { id: documentTypeNameIdPairs["PARS_OVERLAY"] } as any;
      document.status = DocumentStatus.EXTRACTED;
      document.startPage = 0;
      document.endPage = 0;
      document.shipmentId = shipmentId;
      document.fields = Object.entries(aggregatedFields).map(([key, value]) => ({
        name: key,
        value: value,
        dataType: "string"
      })) as DocumentField[];

      const savedDocument = await this.dataSource.manager.getRepository(Document).save(document);

      return {
        id: savedDocument.id,
        name: savedDocument.name,
        status: savedDocument.status,
        type: savedDocument.name,
        context: {
          organizationId: savedDocument.organizationId,
          batchId: file.batchId
        }
      };
    }
  }
}
