import { Processor, WorkerHost } from "@nestjs/bullmq";
import { forwardRef, Inject, Logger, Scope } from "@nestjs/common";
import { DocumentField, DocumentStatus } from "nest-modules";
import { DocumentService } from "../services/document.service";
import { ExtractionTaskFactory } from "../services/extraction-task.factory";
import { DocumentTypeMapper } from "../services/mapper/document-type.mapper";
import { DocumentExtractionConfig } from "../services/openai/document-extraction-config";
import { DocumentExtractionResult } from "../services/openai/dto/document-extraction.dto";
import { LlamaParseResultReader } from "../services/parsers/readers/llama-parse-result.reader";
import { Queue as DocumentQueue } from "./constants";
import { DocumentJob, DocumentJobResult } from "./interfaces";

export const DOCUMENT_PROCESSOR_TASK = "extract-document";

@Processor(
  {
    name: DocumentQueue.DOCUMENT_TASKS,
    scope: Scope.REQUEST
  },
  {
    concurrency: parseInt(process.env.DOCUMENT_PROCESSOR_CONCURRENCY || "1", 10)
  }
)
export class DocumentExtractionProcessor extends WorkerHost {
  private readonly logger = new Logger(DocumentExtractionProcessor.name);

  constructor(
    @Inject(forwardRef(() => DocumentService))
    private readonly documentService: DocumentService,
    @Inject(ExtractionTaskFactory)
    private readonly extractionTaskFactory: ExtractionTaskFactory
  ) {
    super();
  }

  async process(job: DocumentJob): Promise<DocumentJobResult> {
    if (!job.data.documentId) {
      throw new Error("Document id is required");
    }

    // TODO: handle the case when the document is deleted
    // delete scenarios:
    // - before the job is processed
    // - after the job is processed

    // TODO: handle the case for files as well
    if (job.data.documentType === "OTHER" || job.data.documentType === "PARS_OVERLAY") {
      await this.documentService.setDocumentFields(job.data.documentId, []);

      return {
        id: job.data.documentId,
        type: job.data.documentType,
        name: job.data.documentType,
        status: DocumentStatus.EXTRACTED,
        context: {
          organizationId: job.data.organizationId,
          batchId: job.data.batchId
        }
      };
    }

    try {
      this.logger.log("Processing document, id: " + job.data.documentId);

      const document = await this.documentService.getDocument(job.data.documentId, {
        file: true,
        documentType: {
          fields: true
        },
        pages: true,
        organization: true
      });

      const config: DocumentExtractionConfig = DocumentTypeMapper.mapToConfig(document.documentType);

      this.logger.verbose("Parse result: " + document.file.parseResult);

      // TODO: refactor this. this is only used for barcode for now
      let pages = LlamaParseResultReader.load(JSON.parse(document.file.parseResult)).getPages(
        document.startPage,
        document.endPage
      );

      let content = [];

      content = document.pages.map((page) => page.md);

      this.logger.debug("Total pages: " + content.length);
      this.logger.verbose(content.join("\n\n"));

      let result: DocumentExtractionResult;

      result = await this.extractionTaskFactory.execute(content, config, job.data.model);

      if (
        ["OCEAN_E_MANIFEST", "OCEAN_ARRIVAL_NOTICE", "AIR_ARRIVAL_NOTICE", "AIR_E_MANIFEST"].includes(
          document.documentType.name
        )
      ) {
        // handle CCN and hblNumber
        const hblNumberField = result.fields.find((field) => field.key === "hblNumber");
        const ccnField = result.fields.find((field) => field.key === "CCN");
        if (hblNumberField && !ccnField) {
          if (hblNumberField.value.match(/^(\w{4})\s+(\w+)$/)) {
            let hblFromCCN = hblNumberField.value.match(/^(\w{4})\s+(\w+)$/)[2];
            result.fields.push({
              key: "CCN",
              value: hblNumberField.value.replace(/\s+/g, "")
            });
            // edit the hblNumberField
            hblNumberField.value = hblFromCCN;
          }
        }
        const barcodes = pages.flatMap((page) => page.barcodes);
        // deduplicate barcodes
        const deduplicatedBarcodes = [...new Set(barcodes)];

        this.logger.verbose("Deduplicated barcodes: " + deduplicatedBarcodes.join(", "));

        if (deduplicatedBarcodes.length === 1) {
          // replace the CCN field with the deduplicated barcode
          if (result.fields.find((field) => field.key === "CCN")) {
            result.fields = result.fields.map((field) => {
              if (field.key === "CCN") {
                return {
                  key: "CCN",
                  value: deduplicatedBarcodes[0]
                };
              }
              return field;
            });
          } else {
            result.fields.push({
              key: "CCN",
              value: deduplicatedBarcodes[0]
            });
          }
        }
      }

      // TODO: make openai service only return the object
      // TODO: validation is actually necessary since we supplied the schema to openai
      const savedDocument = await this.documentService.setDocumentFields(
        document.id,
        result.fields.map((field) => {
          const documentField = new DocumentField();
          // NOTE: already validated when returing the result
          documentField.dataType = config.getFieldByName(field.key).type;
          documentField.name = field.key;
          documentField.value = Array.isArray(field.value) ? JSON.stringify(field.value) : field.value;
          documentField.originalValue = documentField.value;
          return documentField;
        })
      );

      this.logger.debug("Document extraction completed");

      return {
        id: document.id,
        type: document.documentType.name,
        name: document.name,
        status: savedDocument.status,
        context: {
          organizationId: document.organization.id,
          batchId: document.file.batchId
        }
      };
    } catch (error) {
      this.logger.error(error.message, error.stack);
      throw new Error("Document processing failed (attempt " + job.attemptsMade + ")", { cause: error });
    }
  }
}
