import { InjectQueue, OnQueueEvent, QueueEventsHost, QueueEventsListener } from "@nestjs/bullmq";

import { Logger } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectRepository } from "@nestjs/typeorm";
import { Queue } from "bullmq";
import { Document, DocumentStatus } from "nest-modules";
import { Repository } from "typeorm";
import {
  DOCUMENT_EXTRACTION_COMPLETED_EVENT,
  DOCUMENT_EXTRACTION_FAILED_EVENT,
  DOCUMENT_EXTRACTION_STARTED_EVENT,
  DocumentExtractionCompletedEvent,
  DocumentExtractionFailedEvent,
  DocumentExtractionStartedEvent
} from "../../events/document-extraction-events";
import { Queue as DocumentQueue } from "../constants";
import { DocumentJobResult } from "../interfaces";

@QueueEventsListener(DocumentQueue.DOCUMENT_TASKS)
export class DocumentExtractionQueueListener extends QueueEventsHost {
  private readonly logger = new Logger(DocumentExtractionQueueListener.name);

  constructor(
    private readonly eventEmitter: EventEmitter2,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
    @InjectQueue(DocumentQueue.DOCUMENT_TASKS)
    private readonly documentQueue: Queue
  ) {
    super();
  }

  private async updateDocumentStatus(documentId: number, status: DocumentStatus) {
    await this.documentRepository.update(documentId, {
      status: status
    });
  }

  @OnQueueEvent("completed")
  async onCompleted(args: { jobId: string; returnvalue: DocumentJobResult }) {
    const { jobId, returnvalue } = args;

    this.eventEmitter.emit(
      DOCUMENT_EXTRACTION_COMPLETED_EVENT,
      new DocumentExtractionCompletedEvent(
        returnvalue.id,
        returnvalue.name,
        returnvalue.context.organizationId,
        returnvalue.context.batchId
      )
    );

    this.logger.debug(DOCUMENT_EXTRACTION_COMPLETED_EVENT, JSON.stringify(returnvalue));
  }

  @OnQueueEvent("active")
  async onActive(args: { jobId: string }) {
    const { jobId } = args;

    const job = await this.documentQueue.getJob(jobId);

    await this.updateDocumentStatus(job.data.documentId, DocumentStatus.EXTRACTING);

    this.eventEmitter.emit(
      DOCUMENT_EXTRACTION_STARTED_EVENT,
      new DocumentExtractionStartedEvent(
        job.data.documentId,
        job.data.documentType,
        job.data.organizationId,
        job.data.batchId
      )
    );
  }

  @OnQueueEvent("failed")
  async onFailed(args: { jobId: string; failedReason: string }) {
    const { jobId, failedReason } = args;

    const job = await this.documentQueue.getJob(jobId);

    await this.updateDocumentStatus(job.data.documentId, DocumentStatus.EXTRACTION_FAILED);

    this.eventEmitter.emit(
      DOCUMENT_EXTRACTION_FAILED_EVENT,
      new DocumentExtractionFailedEvent(
        job.data.documentId,
        job.data.documentType,
        job.data.organizationId,
        job.data.batchId,
        failedReason
      )
    );
  }
}
