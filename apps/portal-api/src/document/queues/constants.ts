import { RegisterFlowProducerOptions, RegisterQueueOptions } from "@nestjs/bullmq";
import { DefaultJobOptions } from "bullmq";

export enum Queue {
  DOCUMENT_TASKS = "document-tasks",
  FILE_TASKS = "file-tasks",
  FILE_BATCH_TASKS = "file-batch-tasks",
  FILE_OVERLAY_TASKS = "file-overlay-tasks"
}

export enum FlowProducer {
  EXTRACT_DOCUMENTS = "extract-documents"
}

export const DEFAULT_JOB_OPTIONS: DefaultJobOptions = {
  attempts: 3,
  removeOnComplete: {
    age: 3600,
    count: 1000
  },
  removeOnFail: {
    age: 24 * 3600
  },
  backoff: {
    type: "exponential",
    delay: 10_000 // 10s, 20s, 40s
  }
};

export const FLOW_PRODUCERS: RegisterFlowProducerOptions[] = [
  {
    name: FlowProducer.EXTRACT_DOCUMENTS
  }
];

export const QUEUES: RegisterQueueOptions[] = [
  {
    name: Queue.FILE_TASKS,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  },
  {
    name: Queue.DOCUMENT_TASKS,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  },
  {
    name: Queue.FILE_BATCH_TASKS,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  },
  {
    name: Queue.FILE_OVERLAY_TASKS,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  }
];
