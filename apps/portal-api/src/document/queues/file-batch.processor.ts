import { OnWor<PERSON>E<PERSON>, Processor, WorkerHost } from "@nestjs/bullmq";
import { Inject } from "@nestjs/common";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { WaitingChildrenError } from "bullmq";
import { FileBatchStatus } from "nest-modules";
import { DataSource } from "typeorm";
import { BatchDocumentsExtractedEvent, FileBatchEvent } from "../events";
import { FileBatchService } from "../services/file-batch.service";
import { ExtractOverlayLlmTask } from "../services/openai/extract-overlay.llmtask";
import { Queue as DocumentQueue } from "./constants";
import { DocumentExtractionTaskDispatcher } from "./dispatchers/document-extraction-task.dispatcher";
import { FileTaskDispatcher } from "./dispatchers/file-task.dispatcher";
import {
  DocumentJobResult,
  FileBatchJob,
  FileBatchJobData,
  FileBatchJobResult,
  FileBatchSteps
} from "./interfaces";

@Processor({
  name: DocumentQueue.FILE_BATCH_TASKS
})
export class FileBatchProcessor extends WorkerHost {
  constructor(
    @Inject(FileBatchService)
    private readonly fileBatchService: FileBatchService,
    @Inject(FileTaskDispatcher)
    private readonly fileTaskDispatcher: FileTaskDispatcher,
    @Inject(DocumentExtractionTaskDispatcher)
    private readonly documentExtractionTaskDispatcher: DocumentExtractionTaskDispatcher,
    @Inject(ExtractOverlayLlmTask)
    private readonly extractOverlayLlmTask: ExtractOverlayLlmTask,
    @Inject(DataSource)
    private readonly dataSource: DataSource,
    @Inject(EventEmitter2)
    private readonly eventEmitter: EventEmitter2
  ) {
    super();
  }

  @OnWorkerEvent("completed")
  async onCompleted(job: FileBatchJob) {
    const simplifiedDocuments = job.returnvalue.documents.map((document) => ({
      id: document.id,
      name: document.name,
      status: document.status
    }));

    await this.fileBatchService.updateFileBatchStatus(job.returnvalue.fileBatchId, FileBatchStatus.EXTRACTED);

    this.eventEmitter.emit(
      FileBatchEvent.DOCUMENT_EXTRACTED,
      new BatchDocumentsExtractedEvent({
        batchId: job.returnvalue.fileBatchId,
        organizationId: job.returnvalue.organizationId,
        audience: job.returnvalue.creator,
        documentIds: simplifiedDocuments.map((document) => document.id),
        documents: simplifiedDocuments
      })
    );
  }

  private async parseFiles(job: FileBatchJob) {
    await Promise.all(
      job.data.fileIds.map((fileId) =>
        this.fileTaskDispatcher.parse(
          {
            fileId: fileId,
            organizationId: job.data.organizationId,
            shipmentId: job.data.shipmentId,
            batchId: job.data.fileBatchId
          },
          {
            parent: {
              id: job.id,
              queue: job.queueQualifiedName
            },
            ignoreDependencyOnFailure: true
          }
        )
      )
    );
  }

  private async updateData(job: FileBatchJob, data: Partial<FileBatchJobData>) {
    await job.updateData({
      ...job.data,
      ...data
    });
  }

  private async waitAllDocumentsExtracted(job: FileBatchJob, token: string) {
    const shouldWait = await job.moveToWaitingChildren(token);

    if (shouldWait) {
      throw new WaitingChildrenError();
    }

    const children = await job.getChildrenValues();

    const childrenValues = Object.values(children)
      .flat()
      .filter((doc) => doc !== null);

    await this.updateData(job, {
      documents: childrenValues
    });
  }

  async extractOverlay(job: FileBatchJob) {
    // get file ids
    const fileIds = job.data.fileIds;

    // dispatch overlay extraction for each file
    await Promise.all(
      fileIds.map((fileId) =>
        this.fileTaskDispatcher.extractOverlay(
          { fileId },
          {
            parent: {
              id: job.id,
              queue: job.queueQualifiedName
            },
            ignoreDependencyOnFailure: true
          }
        )
      )
    );
  }

  private shouldExtractOverlay(documents: DocumentJobResult[]): boolean {
    const RoadDocumentTypes = [
      "ROAD_ARRIVAL_NOTICE",
      "ROAD_BILL_OF_LADING",
      "COMMERCIAL_INVOICE",
      "PACKING_LIST",
      "USMCA_CERTIFICATE_OF_ORIGIN"
    ];

    return documents.every((doc) => RoadDocumentTypes.includes(doc.type));
  }

  async process(job: FileBatchJob, token: string): Promise<FileBatchJobResult> {
    const { data } = job;

    const fileBatch = await this.fileBatchService.getFileBatch(data.fileBatchId);

    if (!fileBatch) {
      throw new Error("File batch not found");
    }

    let step = data.currentStep ?? 0;

    while (step !== FileBatchSteps.FINISH) {
      switch (step) {
        case FileBatchSteps.PARSE_FILES:
          job.log("Start parsing files...");
          await this.parseFiles(job);
          break;
        case FileBatchSteps.WAIT_PARSE_FILES:
          job.log("Waiting for all documents to be parsed...");
          await this.waitAllDocumentsExtracted(job, token);
          break;
        case FileBatchSteps.EXTRACT_OVERLAY:
          if (this.shouldExtractOverlay(job.data.documents)) {
            job.log("Extracting overlay...");
            await this.extractOverlay(job);
          } else {
            job.log("Overlay extraction skipped");
            ++step;
            await this.updateData(job, {
              currentStep: step
            });
          }
          break;
        case FileBatchSteps.WAIT_EXTRACT_OVERLAY:
          job.log("Waiting for overlay extraction to complete...");
          await this.waitAllDocumentsExtracted(job, token);
          break;
        case FileBatchSteps.OUTPUT_RESULTS:
          job.log("All documents processed");
          return {
            fileBatchId: job.data.fileBatchId,
            creator: fileBatch.creator,
            organizationId: job.data.organizationId,
            documents: job.data.documents
          };
        default:
          throw new Error("Invalid step");
      }

      ++step;
      await this.updateData(job, {
        currentStep: step
      });
    }
  }
}
