import { InjectFlowProducer, InjectQueue } from "@nestjs/bullmq";
import { Injectable, Logger } from "@nestjs/common";
import { FlowProducer, JobsOptions, Queue } from "bullmq";
import { FlowProducer as DocumentFlowProducer, Queue as DocumentQueue } from "../constants";
import { FileOverlayJobData, FileProcessorJobData, FileProcessorSteps } from "../interfaces";

@Injectable()
export class FileTaskDispatcher {
  private readonly logger = new Logger(FileTaskDispatcher.name);

  constructor(
    @InjectFlowProducer(DocumentFlowProducer.EXTRACT_DOCUMENTS)
    private readonly extractDocumentsProducer: FlowProducer,
    @InjectQueue(DocumentQueue.FILE_TASKS)
    private readonly fileQueue: Queue,
    @InjectQueue(DocumentQueue.FILE_OVERLAY_TASKS)
    private readonly fileOverlayQueue: Queue
  ) {}

  async parse(data: FileProcessorJobData, opts?: JobsOptions) {
    this.logger.log("Add parse file job, fileId: " + data.fileId);
    return await this.fileQueue.add("process-file", data, {
      deduplication: {
        id: `parse-${data.fileId}`
      },
      ...opts
    });
  }

  async split(data: FileProcessorJobData, opts?: JobsOptions) {
    this.logger.log("Add split file job, fileId: " + data.fileId);
    return await this.fileQueue.add(
      "split-file",
      {
        ...data,
        currentStep: FileProcessorSteps.SPLIT
      },
      {
        deduplication: {
          id: `split-${data.fileId}`
        },
        ...opts
      }
    );
  }

  async extractOverlay(data: FileOverlayJobData, opts?: JobsOptions) {
    this.logger.log("Add extract overlay job, fileId: " + data.fileId);
    return await this.fileOverlayQueue.add("extract-overlay", data, {
      deduplication: {
        id: `extract-overlay-${data.fileId}`
      },
      ...opts
    });
  }
}
