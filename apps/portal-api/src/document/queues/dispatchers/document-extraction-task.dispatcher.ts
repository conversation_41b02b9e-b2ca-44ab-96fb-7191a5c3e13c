import { InjectQueue } from "@nestjs/bullmq";
import { Injectable, Logger } from "@nestjs/common";
import { Job, JobsOptions, Queue } from "bullmq";
import { Queue as DocumentQueue } from "../constants";
import { DOCUMENT_PROCESSOR_TASK } from "../document-extraction.processor";
import { DocumentJobData } from "../interfaces";

interface ProcessDocumentParams {
  id: number;
  documentType: string;
  organizationId: number;
  batchId?: string;
  model: string;
}

@Injectable()
export class DocumentExtractionTaskDispatcher {
  private readonly logger = new Logger(DocumentExtractionTaskDispatcher.name);

  constructor(
    @InjectQueue(DocumentQueue.DOCUMENT_TASKS)
    private readonly documentQueue: Queue
  ) {}

  async processDocument(params: ProcessDocumentParams, opts?: JobsOptions): Promise<Job> {
    this.logger.log("Dispatching document extraction job", params);

    return await this.documentQueue.add(
      DOCUMENT_PROCESSOR_TASK,
      {
        documentId: params.id,
        documentType: params.documentType,
        organizationId: params.organizationId,
        batchId: params.batchId,
        model: params.model
      } as DocumentJobData,
      {
        deduplication: {
          id: `${params.id}-${params.model}`
        },
        ...opts
      }
    );
  }
}
