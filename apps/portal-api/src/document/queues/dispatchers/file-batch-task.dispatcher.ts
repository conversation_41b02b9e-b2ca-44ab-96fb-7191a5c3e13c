import { InjectQueue } from "@nestjs/bullmq";
import { Injectable } from "@nestjs/common";
import { Queue } from "bullmq";
import { Queue as DocumentQueue } from "../constants";

@Injectable()
export class FileBatchTaskDispatcher {
  constructor(
    @InjectQueue(DocumentQueue.FILE_BATCH_TASKS)
    private readonly fileBatchQueue: Queue
  ) {}

  async dispatchFileBatchTasks(
    fileBatchId: string,
    organizationId: number,
    fileIds: number[],
    shipmentId?: number
  ) {
    await this.fileBatchQueue.add("file-batch-tasks", { fileBatchId, organizationId, fileIds, shipmentId });
  }
}
