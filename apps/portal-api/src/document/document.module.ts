import { DynamicModule, forwardRef, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  DocumentController,
  DocumentExtractionController,
  FileBatchController,
  FileController,
  MetricsController,
  ShipmentFilesController
} from "./controllers";

// Note: There are JavaScript types for File and Document,
// missing imports will not cause TypeScript errors,
// but will cause runtime errors
import {
  Document,
  DocumentAggregation,
  DocumentField,
  DocumentValidationError,
  File,
  FileBatch,
  FilePage,
  Importer,
  Shipment,
  TransactionalEventEmitterModule
} from "nest-modules";

import { AggregationModule } from "@/aggregation/aggregation.module";
import { BullModule } from "@nestjs/bullmq";
import { MulterModule } from "@nestjs/platform-express";
import { ShipmentModule } from "../shipment/shipment.module";
import { DocumentCreatedListener } from "./listeners";
import { BatchListener } from "./listeners/batch-listener";
import { DocumentExtractionEventsListener } from "./listeners/document-extraction-events.listener";
import { FileListener } from "./listeners/file-listener";
import { ParserServiceProvider } from "./providers";
import {
  DocumentExtractionQueueListener,
  FileBatchQueueListener,
  FileOverlayProcessor,
  FileProcessor,
  FLOW_PRODUCERS,
  QUEUES
} from "./queues";
import {
  DocumentExtractionTaskDispatcher,
  FileBatchTaskDispatcher,
  FileTaskDispatcher
} from "./queues/dispatchers";
import { BarcodeScannerService } from "./services/barcode-scanner/barcode-scanner.service";
import { DocumentFieldService } from "./services/document-field.service";
import { DocumentShipmentService } from "./services/document-shipment.service";
import { DocumentService } from "./services/document.service";
import { ExtractionTaskFactory } from "./services/extraction-task.factory";
import { FileBatchService } from "./services/file-batch.service";
import { FileService } from "./services/file.service";
import { ClassificationTask } from "./services/openai/classification.task";
import { SseEventService } from "./services/sse-event.service";

import { DocumentExtractionProcessor } from "./queues/document-extraction.processor";
import { FileBatchProcessor } from "./queues/file-batch.processor";
import { ExtractOverlayLlmTask } from "./services/openai/extract-overlay.llmtask";
import { XlsxParserService } from "./services/parsers/parsers/xlsx-parser/xlsx-parser.service";

export interface DocumentModuleOptions {
  // tika service
  tikaEndpointUrl?: string;

  // llama cloud
  llamaCloudApiKey?: string;

  // textract
  awsRegion: string;
  awsAccessKey?: string;
  awsSecretKey?: string;
}

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Document,
      DocumentField,
      DocumentValidationError,
      File,
      FileBatch,
      FilePage,
      Shipment,
      DocumentAggregation,
      Importer
    ]),
    forwardRef(() => ShipmentModule),
    forwardRef(() => AggregationModule),
    TransactionalEventEmitterModule
  ],
  providers: [
    DocumentService,
    FileBatchService,
    FileService,
    SseEventService,
    DocumentFieldService,
    DocumentShipmentService,
    ExtractOverlayLlmTask
  ],
  exports: [DocumentService, FileBatchService, FileService, DocumentFieldService, DocumentShipmentService]
})
export class DocumentModule {
  static forRoot(options: DocumentModuleOptions): DynamicModule {
    return {
      module: DocumentModule,
      imports: [
        TypeOrmModule.forFeature([
          Document,
          DocumentField,
          File,
          FileBatch,
          Shipment,
          Importer,
          DocumentValidationError
        ]),
        BullModule.registerQueue(...QUEUES),
        BullModule.registerFlowProducer(...FLOW_PRODUCERS),
        // The following is a workaround for a bug in multer
        // https://github.com/expressjs/multer/issues/962
        // https://github.com/expressjs/multer/issues/1104
        MulterModule.register({
          fileFilter: (_, file, cb) => {
            file.originalname = Buffer.from(file.originalname, "latin1").toString("utf8");
            cb(null, true);
          }
        }),
        forwardRef(() => ShipmentModule),
        TransactionalEventEmitterModule
      ],
      controllers: [
        DocumentController,
        DocumentExtractionController,
        FileController,
        FileBatchController,
        MetricsController,
        ShipmentFilesController
      ],
      providers: [
        {
          provide: "DOCUMENT_MODULE_OPTIONS",
          useValue: options
        },
        // entity services
        FileService,
        FileBatchService,
        DocumentService,
        // parser
        ParserServiceProvider,
        XlsxParserService,
        // extraction
        ExtractionTaskFactory,
        // processors
        ...(process.env.FEATURE
          ? []
          : [FileProcessor, FileBatchProcessor, FileOverlayProcessor, DocumentExtractionProcessor]),
        // Dispatchers
        DocumentExtractionTaskDispatcher,
        FileTaskDispatcher,
        FileBatchTaskDispatcher,

        // Event listeners
        DocumentCreatedListener,
        DocumentExtractionQueueListener,
        DocumentExtractionEventsListener,
        BatchListener,
        FileListener,
        FileBatchQueueListener,
        SseEventService,

        ClassificationTask,

        DocumentFieldService,
        DocumentShipmentService,

        // barcode scanner
        BarcodeScannerService,

        ExtractOverlayLlmTask
      ],
      exports: [DocumentService, FileBatchService, FileService, DocumentFieldService, DocumentShipmentService]
    };
  }
}
