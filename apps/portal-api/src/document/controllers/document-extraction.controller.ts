import { Body, Controller, HttpCode, Param, ParseIntPipe, Post, Sse, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  DocumentStatus,
  ProcessDocumentDto
} from "nest-modules";
import { Observable } from "rxjs";
import { DocumentExtractionTaskDispatcher } from "../queues/dispatchers/document-extraction-task.dispatcher";
import { DocumentService } from "../services/document.service";

@ApiTags("Document Extraction API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("documents")
export class DocumentExtractionController {
  constructor(
    private readonly documentService: DocumentService,
    private readonly documentExtractionTaskDispatcher: DocumentExtractionTaskDispatcher
  ) {}

  /**
   * Subscribe to Document Processing Status
   *
   * @param id Document ID
   * @returns Observable<MessageEvent>
   */
  @ApiOperation({ summary: "Subscribe to Document Processing Status" })
  @ApiParam({ name: "id", type: "integer", description: "Document ID" })
  @Sse(":id/status")
  async subscribeToDocumentProcessingProgress(
    @Param("id", ParseIntPipe) id: number
  ): Promise<Observable<MessageEvent>> {
    return this.documentService.subscribeToDocumentProcessingProgress(id);
  }

  /**
   * Process Document
   *
   * @param id Document ID
   * @param processDocumentDto Process Document DTO
   * @returns void
   */
  @HttpCode(200)
  @Post(":id/process")
  async processDocument(
    @Param("id", ParseIntPipe) id: number,
    @Body() processDocumentDto: ProcessDocumentDto
  ) {
    const document = await this.documentService.getDocument(id);
    await this.documentService.updateDocumentStatus(id, DocumentStatus.PENDING);

    const job = await this.documentExtractionTaskDispatcher.processDocument({
      id,
      documentType: document.name,
      organizationId: document.organizationId,
      model: processDocumentDto.model
    });

    this.documentService.updateDocumentJobId(id, job.id);

    return {
      jobId: job.id
    };
  }
}
