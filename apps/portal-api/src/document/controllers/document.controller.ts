import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Inject,
  Param,
  ParseIntPipe,
  Put,
  Query,
  StreamableFile,
  UseGuards
} from "@nestjs/common";
import {
  ApiBody,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags
} from "@nestjs/swagger";
import archiver from "archiver";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses,
  BatchUpdateDocumentFieldsDto,
  BatchUpdateDocumentFieldsResponseDto,
  Document,
  DocumentTypeService,
  DocumentValidationError,
  GetDocumentsDto,
  GetDocumentsResponseDto,
  NotFoundResponseDto,
  SetDocumentVisibilityDto,
  UpdateDocumentShipmentDto
} from "nest-modules";
import { DocumentShipmentService } from "../services/document-shipment.service";
import { DocumentService } from "../services/document.service";
import { DocumentTypeMapper } from "../services/mapper/document-type.mapper";
import { ConfigToSchemaMapper } from "../services/openai/mapper/config-to-schema.mapper";

@ApiTags("Document API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("documents")
export class DocumentController {
  constructor(
    @Inject(DocumentService)
    private readonly documentService: DocumentService,
    @Inject(DocumentTypeService)
    private readonly documentTypeService: DocumentTypeService,
    @Inject(DocumentShipmentService)
    private readonly documentShipmentService: DocumentShipmentService
  ) {}

  /**
   * Get Documents
   *
   * @param getDocumentsDto Get Documents DTO
   * @returns Documents
   */
  @ApiOperation({ summary: "Get Documents" })
  @ApiGetManyResponses({ type: GetDocumentsResponseDto })
  @Get()
  async getDocuments(@Query() getDocumentsDto: GetDocumentsDto) {
    return await this.documentService.getDocuments(getDocumentsDto);
  }

  /**
   * Get Document
   *
   * @param id Document ID
   * @returns Document
   */
  @ApiOperation({ summary: "Get Document" })
  @ApiParam({ name: "id", type: "integer", description: "Document ID" })
  @ApiGetByIdResponses({ type: Document })
  @Get(":id")
  async getDocumentById(@Param("id", ParseIntPipe) id: number) {
    return this.documentService.getDocument(id, {
      documentType: {
        fields: true
      }
    });
  }

  /**
   * Delete Document
   *
   * @param id Document ID
   * @returns void
   */
  @ApiOperation({ summary: "Delete Document" })
  @ApiParam({ name: "id", type: "integer", description: "Document ID" })
  @ApiDeleteResponses()
  @HttpCode(204)
  @Delete(":id")
  async deleteDocument(@Param("id", ParseIntPipe) id: number) {
    return await this.documentService.deleteDocument(id);
  }

  /**
   * Update Shipment Associated with Document
   *
   * @param id Document ID
   * @param updateDocumentShipmentDto Update Document Shipment DTO
   * @returns Document
   */
  @ApiOperation({ summary: "Update Shipment Associated with Document" })
  @ApiParam({ name: "id", type: "integer", description: "Document ID" })
  @ApiBody({ type: UpdateDocumentShipmentDto })
  @ApiOkResponse({ type: Document })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @HttpCode(200)
  @Put(":id/shipment")
  async updateDocumentShipment(
    @Param("id", ParseIntPipe) id: number,
    @Body() updateDocumentShipmentDto: UpdateDocumentShipmentDto
  ) {
    // TODO: validate whether document can be moved to a different shipment
    const document = await this.documentService.getDocument(id);

    return await this.documentShipmentService.updateDocumentShipment(document, updateDocumentShipmentDto);
  }

  /**
   * Batch Update Document Fields
   *
   * @param id Document ID
   * @param updateDocumentFieldsDto Update Document Fields DTO
   * @returns Batch Update Document Fields Response DTO
   */
  @HttpCode(200)
  @ApiOperation({ summary: "Batch Update Document Fields" })
  @ApiParam({ name: "id", type: "integer", description: "Document ID" })
  @ApiBody({ type: BatchUpdateDocumentFieldsDto })
  @ApiEditResponses({ type: BatchUpdateDocumentFieldsResponseDto })
  @Put(":id/fields/batch")
  async updateDocumentFields(
    @Param("id", ParseIntPipe) id: number,
    @Body() updateDocumentFieldsDto: BatchUpdateDocumentFieldsDto
  ): Promise<BatchUpdateDocumentFieldsResponseDto> {
    // TODO: scope to organization
    return await this.documentService.updateDocumentFields(id, updateDocumentFieldsDto);
  }

  @ApiOperation({ summary: "Get Document Field Validation Error" })
  @ApiParam({ name: "id", type: "integer", description: "Document ID" })
  @ApiGetByIdResponses({ type: DocumentValidationError, isArray: true })
  @Get(":id/fields/validation-errors")
  async getDocumentFieldValidationErrors(@Param("id", ParseIntPipe) id: number) {
    // TODO: scope to organization
    return this.documentService.getDocumentFieldValidationErrors(id);
  }

  @ApiOperation({ summary: "Get Document JSON" })
  @ApiParam({ name: "id", type: "integer", description: "Document ID" })
  @ApiGetByIdResponses({ type: Document })
  @Get(":id/json")
  async getDocumentJson(@Param("id", ParseIntPipe) id: number) {
    return this.documentService.getDocumentJsonData(id);
  }

  @ApiOperation({ summary: "Dismiss Document Shipment Mismatch Warning" })
  @ApiParam({ name: "id", type: "integer", description: "Document ID" })
  @ApiOkResponse()
  @HttpCode(200)
  @Put(":id/shipment/dismiss-mismatch-warning")
  async dismissDocumentShipmentMismatchWarning(@Param("id", ParseIntPipe) id: number) {
    return this.documentService.dismissDocumentShipmentMismatchWarning(id);
  }

  @ApiOperation({ summary: "Get All Document Type Schemas" })
  @Get("/debug/schemas")
  async getDocumentTypeSchema() {
    // TODO: create a zip file with all the schemas
    // TODO: tmp folder
    const archive = archiver("zip", {
      zlib: { level: 9 }
    });

    archive.on("warning", (err) => {
      if (err.code === "ENOENT") {
        // log warning
      } else {
        throw err;
      }
    });

    archive.on("error", (err) => {
      throw err;
    });

    const documentTypes = await this.documentTypeService.getAllDocumentTypes({
      fields: true
    });

    const schemas = documentTypes.map((documentType) => {
      const config = DocumentTypeMapper.mapToConfig(documentType);

      const template = `
Act as an expert data extractor for ${config.type} documents. Your task is to extract all relevant information from the provided document and output the results in JSON format following the specified schema.

Guidelines:
- If a field is missing in the document, set its value to null.
- Only include data that is explicitly found in the document.
- Accurately map data to the corresponding fields using contextual clues.
- When processing address-related fields, ensure that each component (e.g., street, city, state, country) contains only the information appropriate to that component. Remove any redundant or overlapping details so that each field holds solely its intended data.

${config.typeSpecificPromptTemplate ? `\nNote about fields:\n${config.typeSpecificPromptTemplate}`.trim() : ""}
`.trim();

      const prompt = [
        {
          role: "system",
          content: template
        },
        {
          role: "user",
          content: "{{context}}"
        }
      ];

      return {
        name: documentType.name,
        prompt: prompt,
        schema: new ConfigToSchemaMapper(config).getOutputSchema()
      };
    });

    // add schemas to archive
    for (const schema of schemas) {
      archive.append(JSON.stringify(schema.schema, null, 2), {
        name: "schema.json",
        prefix: schema.name.toLowerCase()
      });
      archive.append(JSON.stringify(schema.prompt, null, 2), {
        name: "prompt.json",
        prefix: schema.name.toLowerCase()
      });
    }

    archive.finalize();

    return new StreamableFile(archive, {
      disposition: `attachment; filename=document-extraction-config.zip`,
      type: "application/zip"
    });
  }

  @ApiOperation({ summary: "Set Document Exclusion" })
  @ApiParam({ name: "id", type: "integer", description: "Document ID" })
  @ApiBody({ type: SetDocumentVisibilityDto })
  @HttpCode(200)
  @Put(":id/visibility")
  async setDocumentVisibility(
    @Param("id", ParseIntPipe) id: number,
    @Body() setDocumentVisibilityDto: SetDocumentVisibilityDto
  ) {
    return this.documentService.setDocumentVisibility(id, setDocumentVisibilityDto);
  }
}
