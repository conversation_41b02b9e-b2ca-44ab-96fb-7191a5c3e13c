import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Delete,
  Get,
  HttpCode,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Redirect,
  Req,
  Sse,
  UploadedFiles,
  UseGuards,
  UseInterceptors
} from "@nestjs/common";
import { FilesInterceptor } from "@nestjs/platform-express";
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiConsumes,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiTemporaryRedirectResponse
} from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiDeleteResponses,
  ApiGetManyResponses,
  AuthenticatedRequest,
  BadRequestResponseDto,
  BatchSaveFileDocumentsDto,
  File,
  GetFilesDto,
  GetFilesResponseDto,
  NotFoundResponseDto,
  UploadFilesDto,
  UploadFilesResponseDto
} from "nest-modules";
import { Observable } from "rxjs";
import { FileTaskDispatcher } from "../queues/dispatchers/file-task.dispatcher";
import { DocumentService } from "../services/document.service";
import { FileService } from "../services/file.service";
import { SupportedFileTypeValidator } from "../validators";

const MAX_FILE_SIZE = 25 * 1024 * 1024; // 25 MB

@ApiTags("File API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@UseInterceptors(ClassSerializerInterceptor)
@Controller("files")
export class FileController {
  constructor(
    private readonly fileService: FileService,
    private readonly documentService: DocumentService,
    private readonly fileTaskDispatcher: FileTaskDispatcher
  ) {}

  @ApiOperation({ summary: "Upload Files" })
  @UseInterceptors(FilesInterceptor("files"))
  @ApiConsumes("multipart/form-data")
  @ApiBody({
    description: "Files to upload",
    type: UploadFilesDto
  })
  @ApiQuery({ name: "shipmentId", type: "integer", required: false })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiCreatedResponse({ type: UploadFilesResponseDto })
  @HttpCode(201)
  @Post()
  async uploadFile(
    @UploadedFiles(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE, message: "File size must be less than 25 MB" }),
          new SupportedFileTypeValidator()
        ]
      })
    )
    files: Array<Express.Multer.File>,

    @Req() req: AuthenticatedRequest,
    // TODO: move to dto
    @Query("shipmentId") shipmentId?: number
  ) {
    // if no shipment id, we perform create_shipment
    // if shipment id, we perform update_shipment, create ci, etc.
    const result = await this.fileService.uploadFiles(files, {
      shipmentId,
      organizationId: req.user.organization.id
    });

    return result;
  }

  @ApiOperation({ summary: "Get Files" })
  @ApiGetManyResponses({ type: GetFilesResponseDto })
  @Get()
  async getFiles(@Query() getFilesDto: GetFilesDto) {
    return await this.fileService.getFiles(getFilesDto);
  }

  @ApiOperation({ summary: "Get File" })
  @ApiParam({ name: "id", type: "integer", description: "File ID" })
  @ApiOkResponse({ type: File })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @Get(":id")
  async getFile(@Param("id", ParseIntPipe) id: number) {
    return await this.fileService.getFile(id);
  }

  @ApiOperation({ summary: "Download File" })
  @ApiParam({ name: "id", type: "integer", description: "File ID" })
  @ApiTemporaryRedirectResponse({
    description: "Redirect to a temporary file url for download"
  })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @Get(":id/download")
  @Redirect(null, 302)
  async downloadFile(@Param("id", ParseIntPipe) id: number) {
    return {
      url: await this.fileService.getFileTemporaryUrl(id)
    };
  }

  @ApiOperation({ summary: "Delete File" })
  @ApiParam({ name: "id", type: "integer", description: "File ID" })
  @ApiDeleteResponses()
  @HttpCode(204)
  @Delete(":id")
  async deleteFile(@Param("id", ParseIntPipe) id: number) {
    return await this.fileService.deleteFile(id);
  }

  @ApiOperation({ summary: "Process File" })
  @ApiParam({ name: "id", type: "integer", description: "File ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiNoContentResponse({ description: "File is being processed" })
  @HttpCode(204)
  @Post(":id/process")
  async processFile(@Param("id", ParseIntPipe) id: number) {
    const file = await this.fileService.getFile(id);

    await this.fileTaskDispatcher.parse({
      fileId: file.id,
      organizationId: file.organization.id
    });

    return null;
  }

  @Post(":id/extract-overlay")
  async extractOverlay(@Param("id", ParseIntPipe) id: number) {
    const file = await this.fileService.getFile(id);
    return this.fileTaskDispatcher.extractOverlay({ fileId: file.id });
  }

  @ApiOperation({ summary: "Get File Processing Status" })
  @ApiParam({ name: "id", type: "integer", description: "File ID" })
  @Sse(":id/process/status")
  async getFileProcessingProgress(@Param("id", ParseIntPipe) id: number): Promise<Observable<MessageEvent>> {
    return this.fileService.subscribeToFileProcessingProgress(id);
  }

  @ApiOperation({ summary: "Split File" })
  @ApiParam({ name: "id", type: "integer", description: "File ID" })
  @ApiCreatedResponse({ type: File })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @HttpCode(202)
  @Post(":id/split")
  async splitFile(@Param("id", ParseIntPipe) id: number) {
    const file = await this.fileService.getFile(id);
    await this.documentService.deleteDocumentsByFileId(file.id);
    const job = await this.fileTaskDispatcher.split({
      fileId: file.id,
      organizationId: file.organization.id
    });

    return job;
  }

  @ApiOperation({ summary: "Debug Parse Result" })
  @ApiParam({ name: "id", type: "integer", description: "File ID" })
  @ApiOkResponse({ type: File })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @Get(":id/parse-result")
  async debugParseResult(@Param("id", ParseIntPipe) id: number) {
    const file = await this.fileService.getFile(id);
    return file.parseResult;
  }

  @ApiOperation({ summary: "Update Documents for File" })
  @ApiParam({ name: "id", type: "integer", description: "File ID" })
  @ApiBody({ type: BatchSaveFileDocumentsDto })
  @ApiNoContentResponse({ description: "Documents updated successfully" })
  @HttpCode(200)
  @Put(":id/documents")
  async updateFileDocuments(
    @Param("id", ParseIntPipe) id: number,
    @Body() changeSet: BatchSaveFileDocumentsDto
  ) {
    await this.documentService.replaceFileDocuments(id, changeSet);

    return null;
  }
}
