import { Controller, Get, MessageEvent, Param, Post, Query, Req, Sse, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiQuery, ApiTags } from "@nestjs/swagger";
import { InjectDataSource } from "@nestjs/typeorm";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiGetManyResponses,
  AuthenticatedRequest,
  GetFileBatchesDto,
  GetFileBatchesResponseDto,
  UserPermission
} from "nest-modules";
import { Observable } from "rxjs";
import { DataSource } from "typeorm";
import { FileBatchService } from "../services/file-batch.service";

@ApiTags("File Batch API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("file-batch")
export class FileBatchController {
  constructor(
    private readonly fileBatchService: FileBatchService,
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  @Get(":id")
  async getFileBatch(@Req() req: AuthenticatedRequest, @Param("id") id: string) {
    return this.fileBatchService.getFileBatchDetails(id, req.user.organization.id);
  }

  @ApiOperation({ summary: "Get File batches" })
  @ApiQuery({ type: GetFileBatchesDto })
  @ApiGetManyResponses({ type: GetFileBatchesResponseDto })
  @Get()
  async getFileBatches(@Req() req: AuthenticatedRequest, @Query() getFileBatchesDto: GetFileBatchesDto) {
    // scope to organization if not backoffice admin
    if (req.user.permission !== UserPermission.BACKOFFICE_ADMIN) {
      getFileBatchesDto.organizationId = req.user.organization.id;
    }

    return await this.fileBatchService.getFileBatches(getFileBatchesDto);
  }

  // @Get(":id/replay-success")
  // async replaySuccessEvents(@Param("id") id: string) {
  //   this.fileBatchService.replayAggregationSuccessEvents(id);
  // }

  @Sse(":id/status")
  @ApiOperation({ summary: "Get File batch processing progress" })
  async getFileBatchProcessingProgress(@Param("id") id: string): Promise<Observable<MessageEvent>> {
    return this.fileBatchService.subscribeToFileBatchProcessingProgress(id);
  }

  @Post(":id/aggregate")
  @ApiOperation({ summary: "Aggregate File batch" })
  async aggregateFileBatch(@Param("id") id: string) {
    return this.fileBatchService.aggregateFileBatch(id);
  }
}
