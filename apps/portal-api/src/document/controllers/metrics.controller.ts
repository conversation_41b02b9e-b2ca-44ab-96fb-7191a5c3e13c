import { InjectQueue } from "@nestjs/bullmq";
import { Controller, Get } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { Queue } from "bullmq";
import { Queue as DocumentQueue } from "../queues/constants";

@ApiTags("Metrics API")
@Controller("_metrics")
export class MetricsController {
  constructor(
    @InjectQueue(DocumentQueue.FILE_TASKS)
    private readonly fileQueue: Queue,
    @InjectQueue(DocumentQueue.DOCUMENT_TASKS)
    private readonly documentQueue: Queue
  ) {}

  @Get("queue/workers")
  async getWorkers() {
    return {
      workers: {
        fileQueue: await this.fileQueue.getWorkers(),
        documentQueue: await this.documentQueue.getWorkers()
      }
    };
  }

  @Get("queue/jobs")
  async getJobs() {
    return {
      fileQueue: await this.fileQueue.getJobCounts(),
      documentQueue: await this.documentQueue.getJobCounts()
    };
  }
}
