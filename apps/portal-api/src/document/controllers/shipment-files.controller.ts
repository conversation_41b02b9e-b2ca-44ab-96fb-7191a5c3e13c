import { ShipmentService } from "@/shipment/services/shipment.service";
import { Controller, Get, Param, StreamableFile, UseGuards } from "@nestjs/common";
import { ApiTags } from "@nestjs/swagger";
import { AccessTokenGuard, ApiAccessTokenAuthenticated } from "nest-modules";
import { FileService } from "../services/file.service";

@ApiTags("Shipment Files")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("shipments")
export class ShipmentFilesController {
  constructor(
    private readonly fileService: FileService,
    private readonly shipmentService: ShipmentService
  ) {}

  @Get(":shipmentId/download-files")
  async downloadShipmentFiles(@Param("shipmentId") shipmentId: number) {
    const file = await this.fileService.downloadShipmentFiles(shipmentId);
    const shipment = await this.shipmentService.getShipmentById(shipmentId);

    return new StreamableFile(file, {
      disposition: `attachment; filename=shipment-${shipment.hblNumber}.zip`,
      type: "application/zip"
    });
  }
}
