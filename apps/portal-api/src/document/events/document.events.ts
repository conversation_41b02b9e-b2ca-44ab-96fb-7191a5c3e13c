export enum DocumentEvent {
  CREATED = "document.created",
  UPDATED = "document.updated",
  SHIPMENT_CHANGED = "document.shipmentChanged"
}

export class DocumentCreatedEvent {
  constructor(
    public readonly documentId: number,
    public readonly fileId: number,
    public readonly organizationId: number,
    public readonly batchId: string,
    public readonly name: string,
    public readonly triggerExtraction: boolean = true
  ) {}
}

export class DocumentUpdatedEvent {
  constructor(
    public readonly documentId: number,
    public readonly fileId: number,
    public readonly organizationId: number,
    public readonly batchId: string,
    public readonly name: string
  ) {}
}

export class DocumentShipmentChangedEvent {
  constructor(
    public readonly documentId: number,
    public readonly shipmentId: number
  ) {}
}
