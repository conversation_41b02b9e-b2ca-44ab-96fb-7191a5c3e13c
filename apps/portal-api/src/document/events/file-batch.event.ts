import { DocumentStatus, FileBatchCreator } from "nest-modules";

export enum FileBatchEvent {
  // extraction events
  DOCUMENT_EXTRACTION_STARTED = "file-batch.documentExtractionStarted",
  DOCUMENT_EXTRACTED = "file-batch.documentExtracted",
  DOCUMENT_EXTRACTION_ERROR = "file-batch.documentExtractionError",

  // emit when the batch is ready for aggregation
  READY_FOR_AGGREGATION = "file-batch.readyForAggregation",

  // emit when the batch checking fails
  CHECKING_FAILED = "file-batch.checkingFailed",

  // aggregation events
  AGGREGATION_STARTED = "file-batch.aggregationStarted",
  AGGREGATION_ERROR = "file-batch.aggregationError",
  AGGREGATION_COMPLETED = "file-batch.aggregationCompleted"
}

export class BatchDocumentExtractionStartedEvent {
  constructor(
    public readonly batchId: string,
    public readonly organizationId: number
  ) {}
}

/**
 * Emit when all documents in a batch have been extracted
 *
 */
export class BatchDocumentsExtractedEvent {
  public readonly batchId: string;
  public readonly organizationId: number;
  public readonly audience: FileBatchCreator;

  /** @deprecated please use documents instead */
  public readonly documentIds: number[];

  public readonly documents: {
    id: number;
    status: DocumentStatus;
    name: string;
  }[];

  constructor(payload: BatchDocumentsExtractedEvent) {
    Object.assign(this, payload);
  }
}

export class BatchDocumentExtractionErrorEvent {
  constructor(
    public readonly batchId: string,
    public readonly organizationId: number,
    public readonly audience: FileBatchCreator,
    public readonly documentIds: number[],
    public readonly error: string
  ) {}
}

export class BatchReadyForAggregationEvent {
  constructor(
    public readonly batchId: string,
    public readonly organizationId: number,
    public readonly audience: FileBatchCreator,

    /** @deprecated please fetch the documents from the database instead */
    public readonly documentIds: number[]
  ) {}
}

export class BatchAggregationStartedEvent {
  constructor(
    public readonly batchId: string,
    public readonly organizationId: number
  ) {}
}

export class BatchCheckingFailedEvent {
  constructor(
    public readonly batchId: string,
    public readonly organizationId: number,
    public readonly error: string
  ) {}
}
