export const DOCUMENT_EXTRACTION_STARTED_EVENT = "document.extraction.started";
export const DOCUMENT_EXTRACTION_FAILED_EVENT = "document.extraction.failed";
export const DOCUMENT_EXTRACTION_COMPLETED_EVENT = "document.extraction.completed";

export class DocumentExtractionStartedEvent {
  constructor(
    public readonly documentId: number,
    public readonly documentType: string,
    public readonly organizationId: number,
    public readonly batchId: string
  ) {}
}

export class DocumentExtractionFailedEvent {
  constructor(
    public readonly documentId: number,
    public readonly documentType: string,
    public readonly organizationId: number,
    public readonly batchId: string,
    public readonly error: string
  ) {}
}

export class DocumentExtractionCompletedEvent {
  constructor(
    public readonly documentId: number,
    public readonly documentType: string,
    public readonly organizationId: number,
    public readonly batchId: string
  ) {}
}
