/**
 * AUTO-GENERATED FILE - DO NOT MODIFY MANUALLY
 *
 * This file contains all standard Zod schemas for document types and custom data types.
 * Generated on: 2025-03-21
 */

import {
  alphaNumericOnly,
  parseCCN,
  parseDate,
  parseEmail,
  parseHSCode,
  parsePortCode,
  parseToContainerType,
  parseToPackageUOM,
  parseToVolumeUOM,
  parseToWeightUOM
} from "@/aggregation/parsers";
import { parseToQuantityUOM } from "@/aggregation/parsers/uom";
import { z } from "zod";
import { DocumentType } from "./document-types";

// =============================================================================
// CUSTOM DATA TYPES
// =============================================================================

// These types represent reusable data structures that document types can reference

export const subLocationSchema = z.object({
  name: z.string().nullish(),
  code: z.string().nullish()
});

export type SubLocation = z.infer<typeof subLocationSchema>;

export const certificateLineSchema = z.object({
  descriptionOfGoods: z.string().nullish(),
  hsCode: z.string().nullish()
});

export const containerSchema = z.object({
  containerNumber: z.string().nullish().transform(alphaNumericOnly),
  containerType: z.string().nullish().transform(parseToContainerType),
  sealNumber: z.string().nullish()
});

export type Container = z.infer<typeof containerSchema>;

export type CertificateLine = z.infer<typeof certificateLineSchema>;

export const locationSchema = z.object({
  name: z.string().nullish(),
  city: z.string().nullish(),
  country: z.string().nullish()
});

export type Location = z.infer<typeof locationSchema>;

export const tradePartnerSchema = z.object({
  name: z.string().nullish(),
  street: z.string().nullish(),
  city: z.string().nullish(),
  state: z.string().nullish(),
  postalCode: z.string().nullish(),
  country: z.string().nullish(),
  email: z.string().nullish().transform(parseEmail),
  phoneNumber: z.string().nullish().transform(alphaNumericOnly)
});

export type TradePartner = z.infer<typeof tradePartnerSchema>;

export const commercialInvoiceLineSchema = z
  .object({
    goodsDescription: z.string().nullish(),
    grossWeight: z.number().nullish(),
    netWeight: z.number().nullish(),
    weightUOM: z.string().nullish().transform(parseToWeightUOM),
    quantity: z.number().nullish(),
    // we don't transform quantity uom because it can be any uom
    quantityUOM: z.string().nullish(),
    package: z.number().nullish(),
    packageUOM: z.string().nullish().transform(parseToPackageUOM),
    hsCode: z.string().nullish().transform(parseHSCode),
    partNumber: z.string().nullish(),
    sku: z.string().nullish(),
    upc: z.string().nullish(),
    originState: z.string().nullish(),
    originCountry: z.string().nullish(),
    unitPrice: z.number().nullish(),
    discount: z.number().nullish(),
    totalLineValue: z.number().nullish(),
    currencyCode: z.string().nullish(),
    volume: z.number().nullish(),
    volumeUOM: z.string().nullish().transform(parseToVolumeUOM)
  })
  .transform((data) => {
    const newData = { ...data };
    if (data.totalLineValue && data.quantity && !data.unitPrice) {
      newData.unitPrice = data.totalLineValue / data.quantity;
    }
    return newData;
  });

export type CommercialInvoiceLine = z.infer<typeof commercialInvoiceLineSchema>;

// =============================================================================
// DOCUMENT TYPES
// =============================================================================

// These types represent the main document structures used in the application

export const airArrivalNoticeSchema = z.object({
  weightUOM: z.string().nullish().transform(parseToWeightUOM),
  weight: z.number().nullish(),
  volumeUOM: z.string().nullish().transform(parseToVolumeUOM),
  volume: z.number().nullish(),
  subLocation: subLocationSchema.nullish(),
  quantityUOM: z.string().nullish().transform(parseToQuantityUOM),
  quantity: z.number().nullish(),
  portCode: z.string().nullish().transform(parsePortCode),
  hblNumber: z.string().nullish(),
  flight: z.string().nullish(),
  etaDestination: z.string().nullish().transform(parseDate),
  destinationAirportCode: z.string().nullish(),
  CCN: z.string().nullish().transform(parseCCN),
  carrierCode: z.string().nullish(),
  carrier: z.string().nullish()
});

export type AirArrivalNotice = z.infer<typeof airArrivalNoticeSchema>;

export const airEManifestSchema = z.object({
  weightUOM: z.string().nullish().transform(parseToWeightUOM),
  weight: z.number().nullish(),
  volumeUOM: z.string().nullish().transform(parseToVolumeUOM),
  volume: z.number().nullish(),
  subLocation: subLocationSchema.nullish(),
  quantityUOM: z.string().nullish().transform(parseToQuantityUOM),
  quantity: z.number().nullish(),
  portCode: z.string().nullish().transform(parsePortCode),
  hblNumber: z.string().nullish(),
  flight: z.string().nullish(),
  etaDestination: z.string().nullish().transform(parseDate),
  destinationAirportCode: z.string().nullish(),
  consignee: tradePartnerSchema.nullish(),
  CCN: z.string().nullish().transform(parseCCN),
  carrier: z.string().nullish()
});

export type AirEManifest = z.infer<typeof airEManifestSchema>;

export const airWaybillSchema = z.object({
  weightUOM: z.string().nullish().transform(parseToWeightUOM),
  weight: z.number().nullish(),
  volumeUOM: z.string().nullish().transform(parseToVolumeUOM),
  volume: z.number().nullish(),
  shipper: tradePartnerSchema.nullish(),
  quantityUOM: z.string().nullish().transform(parseToQuantityUOM),
  quantity: z.number().nullish(),
  portOfLoading: locationSchema.nullish(),
  placeOfDelivery: locationSchema.nullish(),
  hblNumber: z.string().nullish(),
  forwarder: tradePartnerSchema.nullish(),
  flight: z.string().nullish(),
  etd: z.string().nullish().transform(parseDate),
  etaDestination: z.string().nullish().transform(parseDate),
  destinationAirportCode: z.string().nullish(),
  consignee: tradePartnerSchema.nullish(),
  carrier: z.string().nullish()
});

export type AirWaybill = z.infer<typeof airWaybillSchema>;

export const certificateOfOriginSchema = z.object({
  validTo: z.string().nullish().transform(parseDate),
  validFrom: z.string().nullish().transform(parseDate),
  countryOfOrigin: z.string().nullish()
});

export type CertificateOfOrigin = z.infer<typeof certificateOfOriginSchema>;

// TODO: check the number later
export const commercialInvoiceSchema = z.object({
  vendor: tradePartnerSchema.nullish(),
  shipTo: tradePartnerSchema.nullish(),
  purchaser: tradePartnerSchema.nullish(),
  poNumber: z.string().nullish(),
  originState: z.string().nullish(),
  originCountry: z.string().nullish(),
  numberOfPackages: z.number().nullish().default(0),
  packageUOM: z.string().nullish().transform(parseToPackageUOM),
  invoiceSubtotal: z.number().nullish(),
  miscCost: z.number().nullish().default(0),
  transCost: z.number().nullish().default(0),
  packCost: z.number().nullish().default(0),
  discount: z.number().nullish().default(0),
  tax: z.number().nullish().default(0),
  invoiceTotal: z.number().nullish().default(0),
  invoiceNumber: z.string().nullish(),
  invoiceDate: z.string().nullish().transform(parseDate),
  grossWeight: z.number().nullish(),
  netWeight: z.number().nullish(),
  weightUOM: z.string().nullish().transform(parseToWeightUOM),
  currencyCode: z.string().nullish(),
  commercialInvoiceLines: z.array(commercialInvoiceLineSchema).nullish()
});

export type CommercialInvoice = z.infer<typeof commercialInvoiceSchema>;

function isNumberEqual(a: number, b: number, tolerance: number = 0.001) {
  return Math.abs(a - b) < tolerance;
}

export const commercialInvoiceSchemaStrict = z
  .object({
    ...commercialInvoiceSchema.shape,
    commercialInvoiceLines: z.array(commercialInvoiceLineSchema).nonempty()
  })
  .superRefine((data, ctx) => {
    const TOLERANCE = 0.001;

    if (data.invoiceSubtotal === undefined || data.invoiceSubtotal === null) {
      data.invoiceSubtotal =
        data.commercialInvoiceLines?.reduce((acc, line) => acc + line.totalLineValue, 0) ?? 0;
    } else if (
      !isNumberEqual(
        data.invoiceSubtotal,
        data.commercialInvoiceLines?.reduce((acc, line) => acc + line.totalLineValue, 0)
      )
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "subtotal_mismatch",
        path: ["invoiceSubtotal"]
      });
    }

    const calculatedInvoiceTotal =
      data.invoiceSubtotal +
      data.transCost +
      data.tax +
      data.packCost +
      data.miscCost -
      Math.abs(data.discount);

    if (!isNumberEqual(data.invoiceTotal, calculatedInvoiceTotal)) {
      console.log(data.invoiceTotal, calculatedInvoiceTotal);
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "total_mismatch",
        path: ["invoiceTotal"]
      });
    }
  });

export const foodSafeLicenceSchema = z.object({
  validTo: z.string().nullish().transform(parseDate),
  validFrom: z.string().nullish().transform(parseDate),
  licenceHolderAddress: z.string().nullish()
});

export type FoodSafeLicence = z.infer<typeof foodSafeLicenceSchema>;

export const houseOceanBillOfLadingSchema = z.object({
  weightUOM: z.string().nullish().transform(parseToWeightUOM),
  weight: z.number().nullish(),
  voyageNumber: z.string().nullish(),
  volumeUOM: z.string().nullish().transform(parseToVolumeUOM),
  volume: z.number().nullish(),
  vessel: z.string().nullish(),
  shipper: tradePartnerSchema.nullish(),
  quantityUOM: z.string().nullish().transform(parseToQuantityUOM),
  quantity: z.number().nullish(),
  prompt: z.string().nullish(),
  portOfLoading: locationSchema.nullish(),
  portOfDischarge: locationSchema.nullish(),
  placeOfDelivery: locationSchema.nullish(),
  hblNumber: z.string().nullish(),
  forwarder: tradePartnerSchema.nullish(),
  etd: z.string().nullish().transform(parseDate),
  etaPort: z.string().nullish().transform(parseDate),
  etaDestination: z.string().nullish().transform(parseDate),
  containers: containerSchema.array().nullish(),
  consignee: tradePartnerSchema.nullish()
});

export type HouseOceanBillOfLading = z.infer<typeof houseOceanBillOfLadingSchema>;

export const oceanArrivalNoticeSchema = z
  .object({
    weightUOM: z.string().nullish().transform(parseToWeightUOM),
    weight: z.number().nullish(),
    volumeUOM: z.string().nullish().transform(parseToVolumeUOM),
    volume: z.number().nullish(),
    subLocation: subLocationSchema.nullish(),
    quantityUOM: z.string().nullish().transform(parseToQuantityUOM),
    quantity: z.number().nullish(),
    portCode: z.string().nullish().transform(parsePortCode),
    pickupNumber: z.string().nullish(),
    hblNumber: z.string().nullish(),
    etd: z.string().nullish().transform(parseDate),
    etaPort: z.string().nullish().transform(parseDate),
    etaDestination: z.string().nullish().transform(parseDate),
    containers: containerSchema.array().nullish(),
    CCN: z.string().nullish().transform(parseCCN),
    carrierCode: z.string().nullish()
  })
  .transform((data) => {
    if (data.carrierCode && data.CCN) {
      if (data.CCN.substring(0, 4) !== data.carrierCode) {
        data.CCN = data.carrierCode + data.CCN;
      }
    }
    return data;
  });

export type OceanArrivalNotice = z.infer<typeof oceanArrivalNoticeSchema>;

export const oceanEManifestSchema = z
  .object({
    weightUOM: z.string().nullish().transform(parseToWeightUOM),
    weight: z.number().nullish(),
    volumeUOM: z.string().nullish().transform(parseToVolumeUOM),
    volume: z.number().nullish(),
    subLocation: subLocationSchema.nullish(),
    quantityUOM: z.string().nullish().transform(parseToQuantityUOM),
    quantity: z.number().nullish(),
    portCode: z.string().nullish().transform(parsePortCode),
    hblNumber: z.string().nullish(),
    etd: z.string().nullish().transform(parseDate),
    etaPort: z.string().nullish().transform(parseDate),
    etaDestination: z.string().nullish().transform(parseDate),
    containers: containerSchema.array().nullish(),
    consignee: tradePartnerSchema.nullish(),
    CCN: z.string().nullish().transform(parseCCN),
    carrierCode: z.string().nullish()
  })
  .transform((data) => {
    if (data.carrierCode && data.CCN) {
      if (data.CCN.substring(0, 4) !== data.carrierCode) {
        data.CCN = data.carrierCode + data.CCN;
      }
    }
    return data;
  });

export type OceanEManifest = z.infer<typeof oceanEManifestSchema>;

export const packingListSchema = z.object({
  weightUOM: z.string().nullish().transform(parseToWeightUOM),
  vendor: tradePartnerSchema.nullish(),
  transCost: z.number().nullish(),
  tax: z.number().nullish(),
  shipTo: tradePartnerSchema.nullish(),
  purchaser: tradePartnerSchema.nullish(),
  poNumber: z.string().nullish(),
  packCost: z.number().nullish(),
  packageUOM: z.string().nullish().transform(parseToPackageUOM),
  originState: z.string().nullish(),
  originCountry: z.string().nullish(),
  numberOfPackages: z.number().nullish(),
  netWeight: z.number().nullish(),
  miscCost: z.number().nullish(),
  invoiceTotal: z.number().nullish(),
  invoiceNumber: z.string().nullish(),
  invoiceDate: z.string().nullish().transform(parseDate),
  grossWeight: z.number().nullish(),
  discount: z.number().nullish(),
  currencyCode: z.string().nullish(),
  commercialInvoiceLines: z.array(commercialInvoiceLineSchema).nullish()
});

export type PackingList = z.infer<typeof packingListSchema>;

export const phytosanitaryCertificateSchema = z.object({
  validTo: z.string().nullish().transform(parseDate),
  validFrom: z.string().nullish().transform(parseDate),
  country: z.string().nullish()
});

export type PhytosanitaryCertificate = z.infer<typeof phytosanitaryCertificateSchema>;

export const roadArrivalNoticeSchema = z.object({
  weightUOM: z.string().nullish().transform(parseToWeightUOM),
  weight: z.number().nullish(),
  subLocation: subLocationSchema.nullish(),
  shipper: tradePartnerSchema.nullish(),
  quantityUOM: z.string().nullish().transform(parseToQuantityUOM),
  quantity: z.number().nullish(),
  portOfEntry: z.string().nullish(),
  portCode: z.string().nullish().transform(parsePortCode),
  CCN: z.string().nullish().transform(parseCCN),
  hblNumber: z.string().nullish(),
  etaPort: z.string().nullish().transform(parseDate),
  consignee: tradePartnerSchema.nullish(),
  carrier: tradePartnerSchema.nullish()
});

export type RoadArrivalNotice = z.infer<typeof roadArrivalNoticeSchema>;

export const roadBillOfLadingSchema = z.object({
  weightUOM: z.string().nullish().transform(parseToWeightUOM),
  weight: z.number().nullish(),
  shipper: tradePartnerSchema.nullish(),
  quantityUOM: z.string().nullish().transform(parseToQuantityUOM),
  quantity: z.number().nullish(),
  portOfEntry: z.string().nullish(),
  portCode: z.string().nullish().transform(parsePortCode),
  CCN: z.string().nullish().transform(parseCCN),
  hblNumber: z.string().nullish(),
  etd: z.string().nullish().transform(parseDate),
  etaPort: z.string().nullish().transform(parseDate),
  consignee: tradePartnerSchema.nullish(),
  carrier: z.string().nullish()
});

export type RoadBillOfLading = z.infer<typeof roadBillOfLadingSchema>;

export const usmcaCertificateOfOriginSchema = z.object({
  validTo: z.string().nullish().transform(parseDate),
  validFrom: z.string().nullish().transform(parseDate),
  producer: tradePartnerSchema.nullish(),
  invoiceNumber: z.string().nullish(),
  importer: tradePartnerSchema.nullish(),
  exporter: tradePartnerSchema.nullish(),
  countryOfOrigin: z.string().nullish(),
  certificateLine: z.array(certificateLineSchema).nullish()
});

export type UsmcaCertificateOfOrigin = z.infer<typeof usmcaCertificateOfOriginSchema>;

export const parsOverlaySchema = z.object({
  etaPort: z.string().nullish().transform(parseDate),
  etd: z.string().nullish().transform(parseDate),
  CCN: z.string().nullish().transform(parseCCN),
  portOfEntry: z.string().nullish()
});

export type ParsOverlay = z.infer<typeof parsOverlaySchema>;

export const TYPE_SCHEMA_MAP: Record<Exclude<DocumentType, DocumentType.OTHER>, z.ZodSchema> = {
  [DocumentType.AIR_ARRIVAL_NOTICE]: airArrivalNoticeSchema,
  [DocumentType.AIR_E_MANIFEST]: airEManifestSchema,
  [DocumentType.AIR_WAYBILL]: airWaybillSchema,
  [DocumentType.CERTIFICATE_OF_ORIGIN]: certificateOfOriginSchema,
  [DocumentType.COMMERCIAL_INVOICE]: commercialInvoiceSchema,
  [DocumentType.HOUSE_OCEAN_BILL_OF_LADING]: houseOceanBillOfLadingSchema,
  [DocumentType.OCEAN_ARRIVAL_NOTICE]: oceanArrivalNoticeSchema,
  [DocumentType.OCEAN_E_MANIFEST]: oceanEManifestSchema,
  [DocumentType.PACKING_LIST]: packingListSchema,
  [DocumentType.ROAD_ARRIVAL_NOTICE]: roadArrivalNoticeSchema,
  [DocumentType.ROAD_BILL_OF_LADING]: roadBillOfLadingSchema,
  [DocumentType.USMCA_CERTIFICATE_OF_ORIGIN]: usmcaCertificateOfOriginSchema,
  [DocumentType.PARS_OVERLAY]: parsOverlaySchema
};

export const STRICT_TYPE_SCHEMA_MAP: Record<Exclude<DocumentType, DocumentType.OTHER>, z.ZodSchema> = {
  [DocumentType.AIR_ARRIVAL_NOTICE]: airArrivalNoticeSchema,
  [DocumentType.AIR_E_MANIFEST]: airEManifestSchema,
  [DocumentType.AIR_WAYBILL]: airWaybillSchema,
  [DocumentType.CERTIFICATE_OF_ORIGIN]: certificateOfOriginSchema,
  [DocumentType.HOUSE_OCEAN_BILL_OF_LADING]: houseOceanBillOfLadingSchema,
  [DocumentType.OCEAN_ARRIVAL_NOTICE]: oceanArrivalNoticeSchema,
  [DocumentType.OCEAN_E_MANIFEST]: oceanEManifestSchema,
  [DocumentType.PACKING_LIST]: packingListSchema,
  [DocumentType.ROAD_ARRIVAL_NOTICE]: roadArrivalNoticeSchema,
  [DocumentType.ROAD_BILL_OF_LADING]: roadBillOfLadingSchema,
  [DocumentType.USMCA_CERTIFICATE_OF_ORIGIN]: usmcaCertificateOfOriginSchema,
  [DocumentType.PARS_OVERLAY]: parsOverlaySchema,
  [DocumentType.COMMERCIAL_INVOICE]: commercialInvoiceSchemaStrict
};
