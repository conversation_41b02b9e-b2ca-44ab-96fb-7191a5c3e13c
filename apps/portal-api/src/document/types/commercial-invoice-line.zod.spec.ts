import { commercialInvoiceLineSchema } from "./document-types.zod";

describe("commercialInvoiceLineSchema", () => {
  it("should validate a complete commercial invoice line", () => {
    const invoiceLine = {
      goodsDescription: "Test Product",
      grossWeight: 100,
      netWeight: 95,
      weightUOM: "KG",
      quantity: 10,
      quantityUOM: "EA",
      package: 2,
      packageUOM: "BOX",
      hsCode: "1234.56.78",
      partNumber: "PART-123",
      sku: "SKU-456",
      upc: "123456789012",
      originState: "CA",
      originCountry: "US",
      unitPrice: 25.99,
      discount: 5,
      totalLineValue: 259.9,
      currencyCode: "USD",
      volume: 0.5,
      volumeUOM: "CBM"
    };

    const parsed = commercialInvoiceLineSchema.parse(invoiceLine);

    expect(parsed).toMatchObject({
      goodsDescription: "Test Product",
      grossWeight: 100,
      netWeight: 95,
      weightUOM: "kgm",
      quantity: 10,
      quantityUOM: "nmb",
      package: 2,
      packageUOM: "bx",
      hsCode: "12345678",
      partNumber: "PART-123",
      sku: "SKU-456",
      upc: "123456789012",
      originState: "CA",
      originCountry: "US",
      unitPrice: 25.99,
      discount: 5,
      totalLineValue: 259.9,
      currencyCode: "USD",
      volume: 0.5,
      volumeUOM: "cbm"
    });
  });

  it("should allow partial data", () => {
    const partialInvoiceLine = {
      goodsDescription: "Test Product",
      quantity: 10,
      totalLineValue: 250
    };

    const parsed = commercialInvoiceLineSchema.parse(partialInvoiceLine);

    expect(parsed.goodsDescription).toBe("Test Product");
    expect(parsed.quantity).toBe(10);
    expect(parsed.unitPrice).toBe(25.0);
    expect(parsed.totalLineValue).toBe(250);
  });

  it("should not calculate unitPrice when it is already provided", () => {
    const invoiceLine = {
      goodsDescription: "Test Product",
      quantity: 10,
      unitPrice: 20,
      totalLineValue: 250 // This would result in 25 if calculated
    };

    const parsed = commercialInvoiceLineSchema.parse(invoiceLine);

    expect(parsed.unitPrice).toBe(20); // Should keep the original value
  });

  it("should not calculate unitPrice when quantity is missing", () => {
    const invoiceLine = {
      goodsDescription: "Test Product",
      totalLineValue: 250
    };

    const parsed = commercialInvoiceLineSchema.parse(invoiceLine);

    expect(parsed.unitPrice).toBeUndefined();
  });

  it("should not calculate unitPrice when totalLineValue is missing", () => {
    const invoiceLine = {
      goodsDescription: "Test Product",
      quantity: 10
    };

    const parsed = commercialInvoiceLineSchema.parse(invoiceLine);

    expect(parsed.unitPrice).toBeUndefined();
  });

  it("should handle empty input", () => {
    const emptyInvoiceLine = {};

    const parsed = commercialInvoiceLineSchema.parse(emptyInvoiceLine);

    expect(parsed).toEqual({
      packageUOM: null,
      quantityUOM: null,
      volumeUOM: null,
      weightUOM: null
    });
  });

  it("should transform UOM values", () => {
    const invoiceLine = {
      weightUOM: "KG",
      quantityUOM: "EA",
      packageUOM: "BOX",
      volumeUOM: "CBM"
    };

    const parsed = commercialInvoiceLineSchema.parse(invoiceLine);

    // The actual values after transformation would depend on the implementation
    // of the parse functions. Here we're just checking that the transformations
    // were applied in some way.
    expect(parsed.weightUOM).not.toBeUndefined();
    expect(parsed.quantityUOM).not.toBeUndefined();
    expect(parsed.packageUOM).not.toBeUndefined();
    expect(parsed.volumeUOM).not.toBeUndefined();
  });
});
