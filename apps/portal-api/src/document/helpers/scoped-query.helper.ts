import { User, UserPermission } from "nest-modules";
import { ClsService } from "nestjs-cls";
import { FindOptionsWhere, IsNull, Not } from "typeorm";

/**
 * Scope query to the organization of the user
 *
 * @param options - The TypeORM query options
 * @param user - The user to scope the query to
 * @param adminAll - If true, the admin can access entity belongs to any organization
 *
 * @returns The scoped query options
 */
export function inOrg<E, T extends { where?: FindOptionsWhere<E> | FindOptionsWhere<E>[] }>(
  options: T,
  user?: User,
  adminAll = false
): T {
  if (user == undefined) {
    return options;
  }

  if (adminAll && user?.permission === UserPermission.BACKOFFICE_ADMIN) {
    Object.assign(options.where, { ...options.where, organization: { id: Not(IsNull()) } });
  } else {
    Object.assign(options.where, {
      ...options.where,
      organization: { id: user?.organization?.id || -1 }
    });
  }

  return options;
}

export function inOrgCls<E, T extends { where?: FindOptionsWhere<E> | FindOptionsWhere<E>[] }>(
  options: T,
  cls?: ClsService,
  adminAll = false
): T {
  if (cls == undefined) {
    console.log("cls is not set");
    return options;
  }

  const organizationId = cls.get("ORGANIZATION_ID");
  const userPermission = cls.get("USER_PERMISSION") || UserPermission.ORGANIZATION_ADMIN;

  if (adminAll && userPermission === UserPermission.BACKOFFICE_ADMIN) {
    Object.assign(options.where, { ...options.where, organization: { id: Not(IsNull()) } });
  } else {
    Object.assign(options.where, { ...options.where, organization: { id: organizationId } });
  }

  return options;
}
