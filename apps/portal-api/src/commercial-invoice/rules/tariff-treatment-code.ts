export const DEFAULT_CANADA_TARIFF_TREATMENT_CODE = 2;

export const COUNTRY_OF_ORIGIN_ALPHA2_TO_TARIFF_TREATMENT_CODE_MAP = {
  // must have valid CUSMA certificate present
  US: 10,
  MX: 11
};

export function getApplicableTTFromOriginCountryAlpha2(countryAlpha2: string): number {
  return (
    COUNTRY_OF_ORIGIN_ALPHA2_TO_TARIFF_TREATMENT_CODE_MAP[countryAlpha2] ||
    DEFAULT_CANADA_TARIFF_TREATMENT_CODE
  );
}

/**
 * Returns the country of origin alpha-2 code for a given tariff treatment code.
 * If undefined, it means the code can be applied to all countries.
 *
 * @param ttCode - The tariff treatment code to look up.
 * @returns The alpha-2 country code or true if applicable to all countries, undefined means we have not implemented it yet.
 */
export function getApplicableCountryFromTTCode(ttCode: number): string | boolean | undefined {
  if (ttCode === DEFAULT_CANADA_TARIFF_TREATMENT_CODE) {
    return true; // This code applies to all countries
  }

  return Object.keys(COUNTRY_OF_ORIGIN_ALPHA2_TO_TARIFF_TREATMENT_CODE_MAP).find(
    (key) => COUNTRY_OF_ORIGIN_ALPHA2_TO_TARIFF_TREATMENT_CODE_MAP[key] === ttCode
  );
}
