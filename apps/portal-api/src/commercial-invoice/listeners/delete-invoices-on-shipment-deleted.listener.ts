import { ShipmentDeletedEventDto } from "@/shipment/dto/event.dto";
import { ShipmentEvent } from "@/shipment/types/event.types";
import { createRequestScopedFactory } from "@/utils/request-scoped-service.util";
import { Injectable, Logger } from "@nestjs/common";
import { ModuleRef } from "@nestjs/core";
import { OnEvent } from "@nestjs/event-emitter";
import { CommercialInvoiceService } from "../commercial-invoice.service";

@Injectable()
export class DeleteInvoicesOnShipmentDeletedListener {
  private readonly logger = new Logger(DeleteInvoicesOnShipmentDeletedListener.name);

  private readonly getCommercialInvoiceService: (customRequest?: any) => Promise<CommercialInvoiceService>;

  constructor(private readonly moduleRef: ModuleRef) {
    this.getCommercialInvoiceService = createRequestScopedFactory(this.moduleRef, CommercialInvoiceService);
  }

  @OnEvent(ShipmentEvent.SHIPMENT_DELETED)
  async handleShipmentDeleted(event: ShipmentDeletedEventDto) {
    const shipmentId = event.shipmentId;
    const commercialInvoiceService = await this.getCommercialInvoiceService();

    try {
      const commercialInvoiceIds = await commercialInvoiceService.getShipmentCommercialInvoiceIds(shipmentId);

      if (commercialInvoiceIds.length === 0) {
        return;
      }

      await Promise.all(
        commercialInvoiceIds.map((id) => {
          commercialInvoiceService.deleteCommercialInvoice(id);
        })
      );
    } catch (error) {
      this.logger.error(`Error removing dangling commercial invoices for shipment ${shipmentId}: ${error}`);
    }
  }
}
