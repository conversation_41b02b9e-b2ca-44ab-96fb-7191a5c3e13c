import { ProductService } from "@/product/product.service";
import { ComplianceValidationService } from "@/shipment/services/compliance-validation.service";
import {
  BadRequestException,
  ConflictException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import {
  AuthenticatedRequest,
  COMMERCIAL_INVOICE_ENUM_KEYS,
  COMMERCIAL_INVOICE_NON_ID_KEYS,
  COMMERCIAL_INVOICE_REQUIRED_KEYS,
  CommercialInvoice,
  CommercialInvoiceColumn,
  CommercialInvoiceLine,
  convertFromCamelCase,
  CountryService,
  CreateCommercialInvoiceDto,
  CustomsStatus,
  EditCommercialInvoiceDto,
  FallbackEnricherResponseDto,
  FIND_COMMERCIAL_INVOICE_LINE_RELATIONS,
  FIND_COMMERCIAL_INVOICE_RELATIONS,
  GetCommercialInvoicesDto,
  GetCommercialInvoicesResponseDto,
  getFindOptions,
  OrganizationService,
  StateService,
  TransactionalEventEmitterService,
  UserPermission,
  ValidateCommercialInvoiceComplianceResponseDto
} from "nest-modules";
import { DataSource, IsNull, Not, QueryRunner, Repository } from "typeorm";
import { ShipmentService } from "../shipment/services/shipment.service";
import { TradePartnerService } from "../trade-partner/trade-partner.service";

import { VALIDATION_FIND_COMMERCIAL_INVOICE_RELATIONS } from "@/shipment/types/compliance-validation.types";
import {
  CommercialInvoiceCreatedEventDto,
  CommercialInvoiceDeletedEventDto,
  CommercialInvoiceEditedEventDto
} from "./dto/event.dto";
import { CommercialInvoiceEnricher } from "./enrichers";
import { CommercialInvoiceEvent } from "./types/event.types";

@Injectable({ scope: Scope.REQUEST })
export class CommercialInvoiceService {
  private readonly logger = new Logger(CommercialInvoiceService.name);

  constructor(
    @InjectRepository(CommercialInvoice)
    private readonly ciRepository: Repository<CommercialInvoice>,
    @InjectRepository(CommercialInvoiceLine)
    private readonly ciLineRepository: Repository<CommercialInvoiceLine>,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    @Inject(forwardRef(() => ShipmentService))
    private readonly shipmentService: ShipmentService,
    @Inject(forwardRef(() => ComplianceValidationService))
    private readonly complianceValidationService: ComplianceValidationService,
    @Inject(forwardRef(() => TradePartnerService))
    private readonly tradePartnerService: TradePartnerService,
    @Inject(CountryService)
    private readonly countryService: CountryService,
    @Inject(StateService)
    private readonly stateService: StateService,
    @Inject(forwardRef(() => OrganizationService))
    private readonly organizationService: OrganizationService,
    @Inject(forwardRef(() => TransactionalEventEmitterService))
    private readonly transactionalEventEmitterService: TransactionalEventEmitterService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly eventEmitter: EventEmitter2,
    @Inject(forwardRef(() => ProductService))
    private readonly productService: ProductService,
    @Inject(CommercialInvoiceEnricher)
    private readonly commercialInvoiceEnricher: CommercialInvoiceEnricher
  ) {}

  // FIXME: TypeORM already support this, we can consider remove this method
  private async runTransaction<T>(func: (queryRunner: QueryRunner) => Promise<T>): Promise<T> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const result = await func(queryRunner);
      await queryRunner.commitTransaction();
      return result;
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getCommercialInvoices(
    getCommercialInvoicesDto: GetCommercialInvoicesDto
  ): Promise<GetCommercialInvoicesResponseDto> {
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      getCommercialInvoicesDto.organizationId = this.request?.user?.organization?.id || -1;
    const { expandLines, ...dto } = getCommercialInvoicesDto;
    const { where, order, skip, take } = getFindOptions(
      dto,
      COMMERCIAL_INVOICE_ENUM_KEYS,
      COMMERCIAL_INVOICE_NON_ID_KEYS,
      CommercialInvoiceColumn.id
    );

    const { commercialInvoice, ...ciLineRelations } = FIND_COMMERCIAL_INVOICE_LINE_RELATIONS;

    const [commercialInvoices, total] = await this.ciRepository.findAndCount({
      where,
      relations: {
        ...FIND_COMMERCIAL_INVOICE_RELATIONS,
        commercialInvoiceLines: expandLines ? ciLineRelations : true
      },
      order,
      skip,
      take
    });
    return { commercialInvoices, total, skip, limit: take };
  }

  async getCommercialInvoiceById(commercialInvoiceId: number, queryRunner?: QueryRunner) {
    return await (
      queryRunner ? queryRunner.manager.getRepository(CommercialInvoice) : this.ciRepository
    ).findOne({
      where: {
        id: commercialInvoiceId,
        organization: {
          id:
            this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
              ? Not(IsNull())
              : this.request?.user?.organization?.id || -1
        }
      },
      relations: FIND_COMMERCIAL_INVOICE_RELATIONS
    });
  }

  async createCommercialInvoice(
    createCommercialInvoiceDto: CreateCommercialInvoiceDto,
    queryRunner?: QueryRunner
  ) {
    return this._createCommercialInvoiceWithTransaction(createCommercialInvoiceDto, queryRunner);
  }

  /**
   * Create a commercial invoice with automatic transaction handling for replacement scenarios.
   * Use this method when you want automatic transaction wrapping for invoice replacement logic.
   *
   * @param createCommercialInvoiceDto - The DTO for creating the commercial invoice
   * @returns The created commercial invoice
   */
  async createOrReplaceCommercialInvoice(createCommercialInvoiceDto: CreateCommercialInvoiceDto) {
    // Always use transaction for replacement logic when invoice number is provided
    if (createCommercialInvoiceDto.invoiceNumber) {
      return this.runTransaction((qr) =>
        this._createCommercialInvoiceWithTransaction(createCommercialInvoiceDto, qr)
      );
    }

    // No invoice number, no replacement needed, no transaction required
    return this._createCommercialInvoiceWithTransaction(createCommercialInvoiceDto);
  }

  private async _createCommercialInvoiceWithTransaction(
    createCommercialInvoiceDto: CreateCommercialInvoiceDto,
    queryRunner?: QueryRunner
  ) {
    const ciRepository = queryRunner
      ? queryRunner.manager.getRepository(CommercialInvoice)
      : this.ciRepository;

    let newCommercialInvoice = new CommercialInvoice();
    newCommercialInvoice.organization = this.request?.user?.organization;
    newCommercialInvoice.createdBy = this.request?.user || null;
    newCommercialInvoice.lastEditedBy = this.request?.user || null;

    for (const [key, value] of Object.entries(createCommercialInvoiceDto)) {
      if (value === undefined) continue;
      const propertyName = key.replace(/Id$/, "");
      switch (propertyName) {
        case "shipment":
          if (typeof value === "number") {
            newCommercialInvoice[propertyName] = await this.shipmentService.getShipmentById(
              value,
              queryRunner
            );
            if (!newCommercialInvoice[propertyName])
              throw new NotFoundException(`${convertFromCamelCase(propertyName)} not found`);
            if (newCommercialInvoice[propertyName].organization?.id !== this.request?.user?.organization?.id)
              throw new BadRequestException(
                `${convertFromCamelCase(propertyName)} does not belong to the organization of current user`
              );
          } else newCommercialInvoice[propertyName] = null;
          break;
        case "origin":
          throw new BadRequestException(
            "Origin in commercial invoice is deprecated. Please set the origin in commercial invoice line instead."
          );
        case "countryOfExport": {
          if (typeof value !== "number") {
            newCommercialInvoice[propertyName] = null;
            break;
          }

          const country = await this.countryService.getCountryById(value, queryRunner);

          if (!country) {
            throw new NotFoundException(`${convertFromCamelCase(propertyName)} not found`);
          }

          newCommercialInvoice[propertyName] = country;
          break;
        }
        case "stateOfExport": {
          const country = await this.countryService.getCountryById(
            createCommercialInvoiceDto.countryOfExportId,
            queryRunner
          );
          if (country.alpha2 === "US") {
            const state = await this.stateService.getStateById(value);

            if (!state) {
              throw new NotFoundException(`${convertFromCamelCase(propertyName)} not found`);
            }

            newCommercialInvoice[propertyName] = state;
          }
          break;
        }
        case "exporter":
        case "vendor":
        case "manufacturer":
        case "shipTo":
        case "purchaser":
          if (typeof value === "number") {
            newCommercialInvoice[propertyName] = await this.tradePartnerService.getTradePartnerById(
              value,
              queryRunner
            );
            if (!newCommercialInvoice[propertyName])
              throw new NotFoundException(`${convertFromCamelCase(propertyName)} not found`);
            if (newCommercialInvoice[propertyName].organization?.id !== this.request?.user?.organization?.id)
              throw new BadRequestException(
                `${convertFromCamelCase(propertyName)} does not belong to the organization of current user`
              );
          } else newCommercialInvoice[propertyName] = null;
          break;
        default:
          newCommercialInvoice[propertyName] = value;
          break;
      }
    }

    const missingRequiredKeys = COMMERCIAL_INVOICE_REQUIRED_KEYS.filter((key) =>
      [null, undefined].includes(newCommercialInvoice[key])
    );
    if (missingRequiredKeys.length > 0)
      throw new BadRequestException(`The following fields are required: ${missingRequiredKeys.join(", ")}`);

    // Check for existing commercial invoice and handle replacement
    const existingCI = await ciRepository.findOne({
      where: {
        invoiceNumber: newCommercialInvoice.invoiceNumber,
        organization: { id: newCommercialInvoice.organization?.id }
      },
      relations: { shipment: true, organization: true }
    });

    if (existingCI) {
      const priorShipmentId = existingCI.shipment?.id;
      // 2.1 Orphan CI (no shipment) → replace
      if (!priorShipmentId) {
        this.logger.log(`Orphan CI found (no shipment), replacing CI ${existingCI.id}`);
        await this.deleteCommercialInvoiceWithQueryRunner(existingCI.id, queryRunner);
      } else {
        // Load that shipment's status under the transaction
        const priorShipment = await this.shipmentService.getShipmentById(priorShipmentId, queryRunner);
        if (!priorShipment) {
          throw new NotFoundException(`Shipment ${priorShipmentId} not found`);
        }
        const priorStatus = priorShipment.customsStatus;
        // 2.2.a If accounting complete → fail
        if (priorStatus === CustomsStatus.ACCOUNTING_COMPLETED) {
          throw new ConflictException(
            `Cannot overwrite CI ${existingCI.invoiceNumber} on shipment ${priorShipmentId} because it's ${priorStatus}`
          );
        }
        // 2.2.b & 2.3 Not accounting complete (same or different shipment) → replace
        this.logger.log(
          `Replacing existing CI ${existingCI.id} on shipment ${priorShipmentId} (status=${priorStatus})`
        );
        await this.deleteCommercialInvoiceWithQueryRunner(existingCI.id, queryRunner);
      }
    }

    newCommercialInvoice = await ciRepository.save(newCommercialInvoice);

    await this.transactionalEventEmitterService.enqueueEvent(
      CommercialInvoiceEvent.COMMERCIAL_INVOICE_CREATED,
      queryRunner,
      new CommercialInvoiceCreatedEventDto(
        newCommercialInvoice.id,
        newCommercialInvoice.shipment?.id ?? -1,
        this.request?.user,
        createCommercialInvoiceDto
      )
    );

    return await this.getCommercialInvoiceById(newCommercialInvoice.id, queryRunner);
  }

  async editCommercialInvoice(
    ciId: number,
    editCommercialInvoiceDto: EditCommercialInvoiceDto,
    queryRunner?: QueryRunner
  ) {
    const commercialInvoice = await this.getCommercialInvoiceById(ciId, queryRunner);
    if (!commercialInvoice) throw new NotFoundException("Commercial Invoice not found");
    commercialInvoice.lastEditedBy = this.request?.user || null;

    for (const [key, value] of Object.entries(editCommercialInvoiceDto)) {
      if (value === undefined) continue;
      const propertyName = key.replace(/Id$/, "");
      switch (propertyName) {
        case "shipment":
          if (typeof value === "number") {
            commercialInvoice[propertyName] = await this.shipmentService.getShipmentById(value);
            if (!commercialInvoice[propertyName])
              throw new NotFoundException(`${convertFromCamelCase(propertyName)} not found`);
            if (commercialInvoice[propertyName].organization?.id !== this.request?.user?.organization?.id)
              throw new BadRequestException(
                `${convertFromCamelCase(propertyName)} does not belong to the organization of current user`
              );
          } else commercialInvoice[propertyName] = null;
          break;
        case "origin":
          // if (typeof (value) === 'number') {
          //     commercialInvoice[propertyName] = await this.countryService.getCountryById(value);
          //     if (!commercialInvoice[propertyName]) throw new NotFoundException(`${convertFromCamelCase(propertyName)} not found`);
          // }
          throw new BadRequestException(
            "Origin in commercial invoice is deprecated. Please set the origin in commercial invoice line instead."
          );
        case "countryOfExport": {
          if (typeof value !== "number") {
            commercialInvoice[propertyName] = null;
            break;
          }

          const country = await this.countryService.getCountryById(value, queryRunner);

          if (!country) {
            throw new NotFoundException(`${convertFromCamelCase(propertyName)} not found`);
          }

          commercialInvoice[propertyName] = country;
          break;
        }
        case "stateOfExport": {
          const country = await this.countryService.getCountryById(
            editCommercialInvoiceDto.countryOfExportId,
            queryRunner
          );
          if (country.alpha2 === "US") {
            const state = await this.stateService.getStateById(value);

            if (!state) {
              throw new NotFoundException(`${convertFromCamelCase(propertyName)} not found`);
            }

            commercialInvoice[propertyName] = state;
          }
          break;
        }
        case "exporter":
        case "vendor":
        case "manufacturer":
        case "shipTo":
        case "purchaser":
          if (typeof value === "number") {
            commercialInvoice[propertyName] = await this.tradePartnerService.getTradePartnerById(
              value,
              queryRunner
            );
            if (!commercialInvoice[propertyName])
              throw new NotFoundException(`${convertFromCamelCase(propertyName)} not found`);
            if (commercialInvoice[propertyName].organization?.id !== this.request?.user?.organization?.id)
              throw new BadRequestException(
                `${convertFromCamelCase(propertyName)} does not belong to the organization of current user`
              );
          } else commercialInvoice[propertyName] = null;
          break;
        default:
          // if (COMMERCIAL_INVOICE_REQUIRED_KEYS.includes(propertyName) && value === null) throw new BadRequestException(`${propertyName} is required`);
          commercialInvoice[propertyName] = value;
          break;
      }
    }

    // if (!commercialInvoice?.shipment) throw new BadRequestException('Shipment is required for commercial invoice');
    const missingRequiredKeys = COMMERCIAL_INVOICE_REQUIRED_KEYS.filter((key) =>
      [null, undefined].includes(commercialInvoice[key])
    );
    if (missingRequiredKeys.length > 0)
      throw new BadRequestException(`The following fields are required: ${missingRequiredKeys.join(", ")}`);

    if (
      await this.ciRepository.existsBy({
        id: Not(commercialInvoice.id),
        invoiceNumber: commercialInvoice.invoiceNumber,
        shipment: { id: commercialInvoice.shipment?.id }
      })
    )
      throw new ConflictException(
        "Commercial invoice with the same invoice number already exists in the shipment"
      );

    await (queryRunner ? queryRunner.manager.getRepository(CommercialInvoice) : this.ciRepository).save(
      commercialInvoice
    );
    // this.eventEmitter.emit(
    //   CommercialInvoiceEvent.COMMERCIAL_INVOICE_EDITED,
    //   new CommercialInvoiceEditedEventDto(commercialInvoice.id, this.request?.user, editCommercialInvoiceDto)
    // );
    await this.transactionalEventEmitterService.enqueueEvent(
      CommercialInvoiceEvent.COMMERCIAL_INVOICE_EDITED,
      queryRunner,
      new CommercialInvoiceEditedEventDto(
        commercialInvoice.id,
        commercialInvoice.shipment?.id ?? -1,
        this.request?.user,
        editCommercialInvoiceDto
      )
    );
    return await this.getCommercialInvoiceById(commercialInvoice.id, queryRunner);
  }

  /**
   * Get commercial invoice id that is related to a shipment
   *
   * @param shipmentId - The id of the shipment
   * @returns The ids of the commercial invoices that are related to the shipment
   */
  async getShipmentCommercialInvoiceIds(shipmentId: number): Promise<number[]> {
    const commercialInvoices = await this.ciRepository
      .createQueryBuilder("commercialInvoice")
      .select("id")
      .where('"shipmentId" = :shipmentId', { shipmentId })
      .getRawMany();

    return commercialInvoices.map((ci) => ci.id);
  }

  /**
   * Delete a commercial invoice
   *
   * @param ciId - The id of the commercial invoice
   */
  async deleteCommercialInvoice(ciId: number) {
    if (!ciId) throw new BadRequestException("ciId cannot be null");

    const commercialInvoice = await this.getCommercialInvoiceById(ciId);

    if (!commercialInvoice) throw new NotFoundException("Commercial Invoice not found");

    await this.clearCommercialInvoiceLines(ciId);
    await this.ciRepository.delete({ id: ciId });
    await this.transactionalEventEmitterService.enqueueEvent(
      CommercialInvoiceEvent.COMMERCIAL_INVOICE_DELETED,
      null,
      new CommercialInvoiceDeletedEventDto(
        commercialInvoice.id,
        commercialInvoice.shipment?.id ?? -1,
        this.request?.user
      )
    );
    return;
  }

  /**
   * Delete a commercial invoice within a transaction
   *
   * @param ciId - The id of the commercial invoice
   * @param queryRunner - The query runner for the transaction
   */
  private async deleteCommercialInvoiceWithQueryRunner(ciId: number, queryRunner: QueryRunner) {
    if (!ciId) throw new BadRequestException("ciId cannot be null");

    const commercialInvoice = await this.getCommercialInvoiceById(ciId, queryRunner);

    if (!commercialInvoice) throw new NotFoundException("Commercial Invoice not found");

    await this.clearCommercialInvoiceLinesWithQueryRunner(ciId, queryRunner);
    await queryRunner.manager.getRepository(CommercialInvoice).delete({ id: ciId });
    await this.transactionalEventEmitterService.enqueueEvent(
      CommercialInvoiceEvent.COMMERCIAL_INVOICE_DELETED,
      queryRunner,
      new CommercialInvoiceDeletedEventDto(
        commercialInvoice.id,
        commercialInvoice.shipment?.id ?? -1,
        this.request?.user
      )
    );
    return;
  }

  async validateCommercialInvoiceCompliance(
    commercialInvoiceId: number
  ): Promise<ValidateCommercialInvoiceComplianceResponseDto> {
    const commercialInvoice = await this.ciRepository.findOne({
      where: {
        id: commercialInvoiceId
      },
      relations: VALIDATION_FIND_COMMERCIAL_INVOICE_RELATIONS
    });

    if (!commercialInvoice) {
      throw new NotFoundException("Commercial Invoice not found");
    }

    const shipment = await this.shipmentService.getShipmentById(commercialInvoice?.shipment?.id);

    if (!shipment) {
      throw new NotFoundException("Commercial Invoice Shipment not found");
    }

    if (!shipment.requiresReupload && this.complianceValidationService.isShipmentEntryUploaded(shipment)) {
      throw new BadRequestException("Shipment entry is already uploaded");
    }

    const enrichedCommercialInvoice = await this.commercialInvoiceEnricher.enrich(commercialInvoice);

    return this.complianceValidationService.validateCommercialInvoiceCompliances(
      await this.complianceValidationService.getCommercialInvoiceCompliances([enrichedCommercialInvoice]),
      this.complianceValidationService.isDemoShipment(shipment) // Skip filings validation for demo shipments
    )[0];
  }

  /**
   * Clear the commercial invoice lines and remove unreferenced temporary products
   *
   * @param commercialInvoiceId - The ID of the commercial invoice
   */
  async clearCommercialInvoiceLines(commercialInvoiceId: number) {
    const productIds = (
      await this.ciLineRepository.find({
        where: {
          commercialInvoice: { id: commercialInvoiceId }
        },
        relations: {
          product: true
        }
      })
    ).map((line) => line.product.id);

    this.logger.log(`${productIds.length} associated products found`);
    this.logger.debug(`Product IDs: ${productIds}`);

    await this.ciLineRepository.delete({
      commercialInvoice: { id: commercialInvoiceId }
    });

    await this.productService.checkAndRemoveUnreferencedTemporaryProducts(productIds);
  }

  /**
   * Clear the commercial invoice lines and remove unreferenced temporary products within a transaction
   *
   * @param commercialInvoiceId - The ID of the commercial invoice
   * @param queryRunner - The query runner for the transaction
   */
  private async clearCommercialInvoiceLinesWithQueryRunner(
    commercialInvoiceId: number,
    queryRunner: QueryRunner
  ) {
    const ciLineRepository = queryRunner.manager.getRepository(CommercialInvoiceLine);

    const productIds = (
      await ciLineRepository.find({
        where: {
          commercialInvoice: { id: commercialInvoiceId }
        },
        relations: {
          product: true
        }
      })
    ).map((line) => line.product.id);

    this.logger.log(`${productIds.length} associated products found`);
    this.logger.debug(`Product IDs: ${productIds}`);

    await ciLineRepository.delete({
      commercialInvoice: { id: commercialInvoiceId }
    });

    await this.productService.checkAndRemoveUnreferencedTemporaryProducts(productIds);
  }

  async getCommercialInvoiceWithFallbacks(commercialInvoiceId: number): Promise<FallbackEnricherResponseDto> {
    const commercialInvoice = await this.getCommercialInvoiceById(commercialInvoiceId);
    if (!commercialInvoice) throw new NotFoundException("Commercial Invoice not found");
    return {
      fields: await this.commercialInvoiceEnricher.getFallbacks(commercialInvoice)
    };
  }
}
