import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards
} from "@nestjs/common";
import {
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags
} from "@nestjs/swagger";
import {
  AccessTokenGuard,
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses,
  BadRequestResponseDto,
  BatchUpdateCommercialInvoiceLinesDto,
  BatchUpdateCommercialInvoiceLinesResponseDto,
  CommercialInvoice,
  CommercialInvoiceLine,
  CreateCommercialInvoiceDto,
  CreateCommercialInvoiceLineDto,
  EditCommercialInvoiceDto,
  EditCommercialInvoiceLineDto,
  FallbackEnricherResponseDto,
  GetCommercialInvoiceLinesDto,
  GetCommercialInvoiceLinesResponseDto,
  GetCommercialInvoicesDto,
  GetCommercialInvoicesResponseDto,
  NotFoundResponseDto,
  ValidateCommercialInvoiceComplianceResponseDto,
  ValidateCommercialInvoiceLineComplianceResponseDto
} from "nest-modules";
import { CommercialInvoiceLineService } from "./commercial-invoice-line.service";
import { CommercialInvoiceService } from "./commercial-invoice.service";

@ApiTags("Commercial Invoice API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("commercial-invoices")
export class CommercialInvoiceController {
  constructor(
    private readonly commercialInvoiceService: CommercialInvoiceService,
    private readonly commercialInvoiceLineService: CommercialInvoiceLineService
  ) {}

  //#region CommercialInvoice endpoints
  @ApiOperation({ summary: "Get Commercial Invoices" })
  @ApiGetManyResponses({ type: GetCommercialInvoicesResponseDto })
  @Get()
  async getCommercialInvoices(@Query() getCommercialInvoicesDto: GetCommercialInvoicesDto) {
    return await this.commercialInvoiceService.getCommercialInvoices(getCommercialInvoicesDto);
  }

  @ApiOperation({ summary: "Get Commercial Invoice by ID" })
  @ApiParam({ name: "id", type: "integer", description: "Commercial Invoice ID" })
  @ApiGetByIdResponses({ type: CommercialInvoice })
  @Get(":id")
  async getCommercialInvoiceById(@Param("id", ParseIntPipe) id: number) {
    const commercialInvoice = await this.commercialInvoiceService.getCommercialInvoiceById(id);
    if (!commercialInvoice) throw new NotFoundException("Commercial Invoice not found");
    return commercialInvoice;
  }

  @ApiOperation({ summary: "Create Commercial Invoice" })
  @ApiCreateResponses({ type: CommercialInvoice })
  @Post()
  async createCommercialInvoice(@Body() createCommercialInvoiceDto: CreateCommercialInvoiceDto) {
    return await this.commercialInvoiceService.createCommercialInvoice(createCommercialInvoiceDto);
  }

  @ApiOperation({ summary: "Edit Commercial Invoice" })
  @ApiParam({ name: "id", type: "integer", description: "Commercial Invoice ID" })
  @ApiEditResponses({ type: CommercialInvoice })
  @Put(":id")
  async editCommercialInvoice(
    @Param("id", ParseIntPipe) id: number,
    @Body() editCommercialInvoiceDto: EditCommercialInvoiceDto
  ) {
    return await this.commercialInvoiceService.editCommercialInvoice(id, editCommercialInvoiceDto);
  }

  @ApiOperation({ summary: "Delete Commercial Invoice" })
  @ApiParam({ name: "id", type: "integer", description: "Commercial Invoice ID" })
  @ApiDeleteResponses()
  @HttpCode(204)
  @Delete(":id")
  async deleteCommercialInvoice(@Param("id", ParseIntPipe) id: number) {
    await this.commercialInvoiceService.deleteCommercialInvoice(id);
    return;
  }

  @ApiOperation({ summary: "Validate Commercial Invoice Compliance" })
  @ApiParam({ name: "id", type: "integer", description: "Commercial Invoice ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiOkResponse({ type: ValidateCommercialInvoiceComplianceResponseDto })
  @Post(":id/validate-compliance")
  async validateCommercialInvoiceCompliance(@Param("id", ParseIntPipe) id: number) {
    return await this.commercialInvoiceService.validateCommercialInvoiceCompliance(id);
  }

  /**
   * Get the commercial invoice fallbacks
   *
   * @param id - The ID of the commercial invoice
   * @returns The fallbacks for the commercial invoice
   */
  @ApiOperation({ summary: "Get Commercial Invoice with Fallbacks" })
  @ApiParam({ name: "id", type: "integer", description: "Commercial Invoice ID" })
  @ApiOkResponse({ type: FallbackEnricherResponseDto })
  @Get(":id/fallbacks")
  async getCommercialInvoiceFallbacks(@Param("id", ParseIntPipe) id: number) {
    return await this.commercialInvoiceService.getCommercialInvoiceWithFallbacks(id);
  }

  //#endregion
  //#region CommercialInvoiceLine endpoints

  /**
   * Get the commercial invoice line fallbacks for a commercial invoice line or from a product
   *
   * We put this method here because otherwise the path will conflict with the get commercial invoice line by id
   *
   * @param commercialInvoiceId - The ID of the commercial invoice
   * @param lineId - The ID of the commercial invoice line
   * @param productId - The ID of the product
   * @returns The fallbacks for the commercial invoice line or product
   */
  @ApiOperation({ summary: "Get Commercial Invoice Line with Fallbacks" })
  @ApiParam({ name: "commercialInvoiceId", type: "integer", description: "Commercial Invoice ID" })
  @ApiParam({ name: "lineId", type: "integer", description: "Commercial Invoice Line ID" })
  @ApiOkResponse({ type: FallbackEnricherResponseDto })
  @Get(":commercialInvoiceId/lines/:lineId/fallbacks")
  async getCommercialInvoiceLineFallbacks(
    @Param("commercialInvoiceId", ParseIntPipe) commercialInvoiceId: number,
    @Param("lineId", new ParseIntPipe({ optional: true })) lineId?: number,
    @Query("productId") productId?: number
  ) {
    return await this.commercialInvoiceLineService.getCommercialInvoiceLineWithFallbacks(
      commercialInvoiceId,
      lineId,
      productId
    );
  }

  @ApiOperation({ summary: "Get Commercial Invoice Line fallbacks from Product" })
  @ApiParam({ name: "commercialInvoiceId", type: "integer", description: "Commercial Invoice ID" })
  @ApiOkResponse({ type: FallbackEnricherResponseDto })
  @Get(":commercialInvoiceId/lines/fallbacks")
  async getCommercialInvoiceLineFallbacksFromProduct(
    @Param("commercialInvoiceId", ParseIntPipe) commercialInvoiceId: number,
    @Query("productId") productId?: number
  ) {
    return await this.commercialInvoiceLineService.getCommercialInvoiceLineWithFallbacks(
      commercialInvoiceId,
      undefined,
      productId
    );
  }

  @ApiOperation({ summary: "Get Commercial Invoice Lines" })
  @ApiParam({ name: "commercialInvoiceId", type: "integer", description: "Commercial Invoice ID" })
  @ApiGetManyResponses({ type: GetCommercialInvoiceLinesResponseDto })
  @Get(":commercialInvoiceId/lines")
  async getCommercialInvoiceLines(
    @Param("commercialInvoiceId", ParseIntPipe) commercialInvoiceId: number,
    @Query() getCommercialInvoiceLinesDto: GetCommercialInvoiceLinesDto
  ) {
    return await this.commercialInvoiceLineService.getCommercialInvoiceLines(
      commercialInvoiceId,
      getCommercialInvoiceLinesDto
    );
  }

  @ApiOperation({ summary: "Get Commercial Invoice Line by ID" })
  @ApiParam({ name: "commercialInvoiceId", type: "integer", description: "Commercial Invoice ID" })
  @ApiParam({ name: "lineId", type: "integer", description: "Commercial Invoice Line ID" })
  @ApiGetByIdResponses({ type: CommercialInvoiceLine })
  @Get(":commercialInvoiceId/lines/:lineId")
  async getCommercialInvoiceLineById(
    @Param("commercialInvoiceId", ParseIntPipe) commercialInvoiceId: number,
    @Param("lineId", ParseIntPipe) lineId: number
  ) {
    const line = await this.commercialInvoiceLineService.getCommercialInvoiceLineById(
      commercialInvoiceId,
      lineId
    );
    if (!line) throw new NotFoundException("Commercial Invoice Line not found");
    return line;
  }

  @ApiOperation({ summary: "Create Commercial Invoice Line" })
  @ApiParam({ name: "commercialInvoiceId", type: "integer", description: "Commercial Invoice ID" })
  @ApiCreateResponses({ type: CommercialInvoiceLine })
  @Post(":commercialInvoiceId/lines")
  async createCommercialInvoiceLine(
    @Param("commercialInvoiceId", ParseIntPipe) commercialInvoiceId: number,
    @Body() createCommercialInvoiceLineDto: CreateCommercialInvoiceLineDto
  ) {
    return await this.commercialInvoiceLineService.createCommercialInvoiceLine(
      commercialInvoiceId,
      createCommercialInvoiceLineDto
    );
  }

  @ApiOperation({ summary: "Edit Commercial Invoice Line" })
  @ApiParam({ name: "commercialInvoiceId", type: "integer", description: "Commercial Invoice ID" })
  @ApiParam({ name: "lineId", type: "integer", description: "Commercial Invoice Line ID" })
  @ApiEditResponses({ type: CommercialInvoiceLine })
  @Put(":commercialInvoiceId/lines/:lineId")
  async editCommercialInvoiceLine(
    @Param("commercialInvoiceId", ParseIntPipe) commercialInvoiceId: number,
    @Param("lineId", ParseIntPipe) lineId: number,
    @Body() editCommercialInvoiceLineDto: EditCommercialInvoiceLineDto
  ) {
    return await this.commercialInvoiceLineService.editCommercialInvoiceLine(
      commercialInvoiceId,
      lineId,
      editCommercialInvoiceLineDto
    );
  }

  @ApiOperation({ summary: "Delete Commercial Invoice Line" })
  @ApiParam({ name: "commercialInvoiceId", type: "integer", description: "Commercial Invoice ID" })
  @ApiParam({ name: "lineId", type: "integer", description: "Commercial Invoice Line ID" })
  @ApiDeleteResponses()
  @HttpCode(204)
  @Delete(":commercialInvoiceId/lines/:lineId")
  async deleteCommercialInvoiceLine(
    @Param("commercialInvoiceId", ParseIntPipe) commercialInvoiceId: number,
    @Param("lineId", ParseIntPipe) lineId: number
  ) {
    await this.commercialInvoiceLineService.deleteCommercialInvoiceLine(commercialInvoiceId, lineId);
    return;
  }

  @HttpCode(200)
  @ApiOperation({ summary: "Batch Update Commercial Invoice Lines" })
  @ApiParam({ name: "commercialInvoiceId", type: "integer", description: "Commercial Invoice ID" })
  @ApiEditResponses({ type: BatchUpdateCommercialInvoiceLinesResponseDto })
  @Post(":commercialInvoiceId/lines/batch")
  async batchUpdateCommercialInvoiceLines(
    @Param("commercialInvoiceId", ParseIntPipe) commercialInvoiceId: number,
    @Body() batchUpdateCommercialInvoiceLinesDto: BatchUpdateCommercialInvoiceLinesDto
  ) {
    return await this.commercialInvoiceLineService.batchUpdateCommercialInvoiceLines(
      commercialInvoiceId,
      batchUpdateCommercialInvoiceLinesDto
    );
  }

  @ApiOperation({ summary: "Validate Commercial Invoice Line Compliance" })
  @ApiParam({ name: "commercialInvoiceId", type: "integer", description: "Commercial Invoice ID" })
  @ApiParam({ name: "lineId", type: "integer", description: "Commercial Invoice Line ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiOkResponse({ type: ValidateCommercialInvoiceLineComplianceResponseDto })
  @HttpCode(200)
  @Post(":commercialInvoiceId/lines/:lineId/validate-compliance")
  async validateCommercialInvoiceLineCompliance(
    @Param("commercialInvoiceId", ParseIntPipe) commercialInvoiceId: number,
    @Param("lineId", ParseIntPipe) lineId: number
  ) {
    return await this.commercialInvoiceLineService.validateCommercialInvoiceLineCompliance(
      commercialInvoiceId,
      lineId
    );
  }

  //#endregion
}
