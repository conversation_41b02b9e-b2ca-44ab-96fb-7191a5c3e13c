import { ComplianceValidationService } from "@/shipment/services/compliance-validation.service";
import { ShipmentService } from "@/shipment/services/shipment.service";
import {
  BadRequestException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  Scope,
  forwardRef
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import {
  AuthenticatedRequest,
  BatchUpdateCommercialInvoiceLinesDto,
  BatchUpdateCommercialInvoiceLinesResponseDto,
  COMMERCIAL_INVOICE_LINE_ENUM_KEYS,
  COMMERCIAL_INVOICE_LINE_NON_ID_KEYS,
  COMMERCIAL_INVOICE_LINE_REQUIRED_KEYS,
  CanadaExciseTaxCodeService,
  CanadaGstExemptCodeService,
  CanadaTariff,
  CanadaTariffService,
  CanadaTreatmentCode,
  CanadaTreatmentCodeService,
  CanadaVfdCode,
  CanadaVfdCodeService,
  CommercialInvoice,
  CommercialInvoiceLine,
  CommercialInvoiceLineColumn,
  CountryService,
  CreateCommercialInvoiceLineDto,
  CreateProductDto,
  DutiesAndTaxesCalculatorResponseDto,
  EditCommercialInvoiceLineDto,
  FIND_COMMERCIAL_INVOICE_LINE_RELATIONS,
  FallbackEnricherResponseDto,
  GetCommercialInvoiceLinesDto,
  Importer,
  ProductColumn,
  ProductType,
  RuleQueryService,
  Shipment,
  SortOrder,
  StateService,
  SurtaxSubjectCode,
  TransactionalEventEmitterService,
  UnitOfMeasure,
  UserPermission,
  ValidateCommercialInvoiceLineComplianceResponseDto,
  convertFromCamelCase,
  getFindOptions
} from "nest-modules";
import { DataSource, FindOptionsOrder, In, IsNull, Not, QueryRunner, Repository } from "typeorm";
import { ProductService } from "../product/product.service";
import { CommercialInvoiceService } from "./commercial-invoice.service";
import {
  CommercialInvoiceLineCreatedEventDto,
  CommercialInvoiceLineDeletedEventDto,
  CommercialInvoiceLineEditedEventDto
} from "./dto/event.dto";
import { CommercialInvoiceLineEnricher } from "./enrichers";
import { CommercialInvoiceEvent } from "./types/event.types";

@Injectable({ scope: Scope.REQUEST })
export class CommercialInvoiceLineService {
  constructor(
    @InjectRepository(CommercialInvoiceLine)
    private readonly commercialInvoiceLineRepository: Repository<CommercialInvoiceLine>,
    @Inject(CommercialInvoiceService)
    private readonly commercialInvoiceService: CommercialInvoiceService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    @Inject(forwardRef(() => ProductService))
    private readonly productService: ProductService,
    @Inject(forwardRef(() => ShipmentService))
    private readonly shipmentService: ShipmentService,
    @Inject(CanadaVfdCodeService)
    private readonly canadaVfdCodeService: CanadaVfdCodeService,
    @Inject(CanadaTreatmentCodeService)
    private readonly canadaTreatmentCodeService: CanadaTreatmentCodeService,
    @Inject(CountryService)
    private readonly countryService: CountryService,
    @Inject(StateService)
    private readonly stateService: StateService,
    @Inject(CanadaTariffService)
    private readonly canadaTariffService: CanadaTariffService,
    @Inject(CanadaGstExemptCodeService)
    private readonly canadaGstExemptCodeService: CanadaGstExemptCodeService,
    @Inject(CanadaExciseTaxCodeService)
    private readonly canadaExciseTaxCodeService: CanadaExciseTaxCodeService,
    @Inject(forwardRef(() => ComplianceValidationService))
    private readonly complianceValidationService: ComplianceValidationService,
    @Inject(forwardRef(() => RuleQueryService))
    private readonly ruleQueryService: RuleQueryService,
    @Inject(forwardRef(() => TransactionalEventEmitterService))
    private readonly transactionalEventEmitterService: TransactionalEventEmitterService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly eventEmitter: EventEmitter2,
    @Inject(CommercialInvoiceLineEnricher)
    private readonly commercialInvoiceLineEnricher: CommercialInvoiceLineEnricher
  ) {}
  private logger = new Logger(CommercialInvoiceLineService.name);

  /**
   * @deprecated This function is deprecated because Candata calculation is used instead of CARM calculation.
   */
  extractAmountsFromDutiesAndTaxesCalculatorResponse(
    datcResponseDto: DutiesAndTaxesCalculatorResponseDto,
    cadCalculationDate?: Date
  ) {
    if (!datcResponseDto?.Response?.Declaration?.GoodsShipment?.GovernmentAgencyGoodsItem?.Commodity) {
      this.logger.warn("No commodity found in the Duties and Taxes Calculator response");
      return null;
    }
    const commodity = datcResponseDto.Response.Declaration.GoodsShipment.GovernmentAgencyGoodsItem.Commodity;
    const dutyTaxFeeList = commodity.DutyTaxFee || [];
    return {
      cadCalculationDate: cadCalculationDate || new Date(),
      valueForDuty:
        dutyTaxFeeList.find((dutyTaxFee) => dutyTaxFee.TypeCode === "TOT")?.DutyTaxFeeAssessmentBasis
          ?.AdValoremTaxBaseAmount?.TextValue || 0,
      antiDumping:
        dutyTaxFeeList.find((dutyTaxFee) => dutyTaxFee.TypeCode === "ADD")?.Payment?.PaymentAmount
          ?.TextValue || 0,
      countervailing:
        dutyTaxFeeList.find((dutyTaxFee) => dutyTaxFee.TypeCode === "CVD")?.Payment?.PaymentAmount
          ?.TextValue || 0,
      customsDuties:
        dutyTaxFeeList.find((dutyTaxFee) => dutyTaxFee.TypeCode === "CUD")?.Payment?.PaymentAmount
          ?.TextValue || 0,
      exciseDuties:
        dutyTaxFeeList.find((dutyTaxFee) => dutyTaxFee.TypeCode === "EXC")?.Payment?.PaymentAmount
          ?.TextValue || 0,
      exciseTax:
        dutyTaxFeeList.find((dutyTaxFee) => dutyTaxFee.TypeCode === "FET")?.Payment?.PaymentAmount
          ?.TextValue || 0,
      gst:
        dutyTaxFeeList.find((dutyTaxFee) => dutyTaxFee.TypeCode === "GST")?.Payment?.PaymentAmount
          ?.TextValue || 0,
      safeguard:
        dutyTaxFeeList.find((dutyTaxFee) => dutyTaxFee.TypeCode === "OTH")?.Payment?.PaymentAmount
          ?.TextValue || 0,
      surtax:
        dutyTaxFeeList.find((dutyTaxFee) => dutyTaxFee.TypeCode === "SUR")?.Payment?.PaymentAmount
          ?.TextValue || 0,
      totalDutiesAndTaxes:
        dutyTaxFeeList.find((dutyTaxFee) => dutyTaxFee.TypeCode === "TOT")?.Payment?.PaymentAmount
          ?.TextValue || 0
    };
  }

  async getDefaultVfd(commercialInvoice: CommercialInvoice, queryRunner?: QueryRunner) {
    const entityManager = queryRunner ? queryRunner.manager : this.dataSource.manager;
    const organizationImporters = await entityManager.findBy(Importer, {
      organization: { id: commercialInvoice.organization?.id }
    });
    const isVendorSameAsImporter = organizationImporters.some(
      (importer) =>
        importer?.companyName?.trim()?.toLowerCase() === commercialInvoice?.vendor?.name?.trim().toLowerCase()
    );
    const isCostsSet =
      (typeof commercialInvoice.includedMiscCost === "number" && commercialInvoice.includedMiscCost > 0) ||
      (typeof commercialInvoice.includedTransCost === "number" && commercialInvoice.includedTransCost > 0) ||
      (typeof commercialInvoice.includedPackCost === "number" && commercialInvoice.includedPackCost > 0) ||
      (typeof commercialInvoice.excludedMiscCost === "number" && commercialInvoice.excludedMiscCost > 0) ||
      (typeof commercialInvoice.excludedTransCost === "number" && commercialInvoice.excludedTransCost > 0) ||
      (typeof commercialInvoice.excludedPackCost === "number" && commercialInvoice.excludedPackCost > 0);
    const code = (isVendorSameAsImporter ? 2 : 1) * 10 + (isCostsSet ? 4 : 3);
    const canadaVfdCode = await entityManager.findOneBy(CanadaVfdCode, {
      code
    });
    return canadaVfdCode || null;
  }

  /**
   * Get default treatment code for Canada
   */
  async getDefaultTt(commercialInvoice: CommercialInvoice, queryRunner?: QueryRunner) {
    const canadaTreatmentCode = await (queryRunner ? queryRunner.manager : this.dataSource.manager).findOneBy(
      CanadaTreatmentCode,
      { code: 2 }
    );
    return canadaTreatmentCode || null;
  }

  async getCommercialInvoiceLines(
    commercialInvoiceId: number,
    getCommercialInvoiceLinesDto: GetCommercialInvoiceLinesDto
  ) {
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      getCommercialInvoiceLinesDto.organizationId = this.request?.user?.organization?.id || -1;
    if (!(await this.commercialInvoiceService.getCommercialInvoiceById(commercialInvoiceId)))
      throw new NotFoundException("Commercial Invoice not found");
    const { sortBy, sortOrder, ...getManyDto } = getCommercialInvoiceLinesDto;
    const { where, skip, take } = getFindOptions(
      { ...getManyDto, commercialInvoiceId },
      COMMERCIAL_INVOICE_LINE_ENUM_KEYS,
      COMMERCIAL_INVOICE_LINE_NON_ID_KEYS,
      CommercialInvoiceLineColumn.id
    );

    let order: FindOptionsOrder<CommercialInvoiceLine> = {};
    if (sortBy) {
      if (sortBy === ProductColumn.partNumber) order.product = { partNumber: sortOrder || SortOrder.ASC };
      else if (sortBy.endsWith("Id") && !COMMERCIAL_INVOICE_LINE_NON_ID_KEYS.includes(sortBy))
        order[sortBy.replace(/Id$/, "")] = { id: sortOrder || SortOrder.ASC };
      else order[sortBy] = sortOrder || SortOrder.ASC;
    } else order[CommercialInvoiceLineColumn.sequence] = SortOrder.ASC;

    const [commercialInvoiceLines, total] = await this.commercialInvoiceLineRepository.findAndCount({
      where,
      relations: FIND_COMMERCIAL_INVOICE_LINE_RELATIONS,
      order,
      skip,
      take
    });

    const enrichedCommercialInvoiceLines =
      await this.commercialInvoiceLineEnricher.enrichMany(commercialInvoiceLines);

    return {
      commercialInvoiceLines: enrichedCommercialInvoiceLines,
      total,
      skip,
      limit: take
    };
  }

  async getCommercialInvoiceLineById(
    commercialInvoiceId: number,
    commercialInvoiceLineId: number,
    queryRunner?: QueryRunner
  ) {
    if (!(await this.commercialInvoiceService.getCommercialInvoiceById(commercialInvoiceId, queryRunner)))
      throw new NotFoundException("Commercial Invoice not found");

    return await (
      queryRunner
        ? queryRunner.manager.getRepository(CommercialInvoiceLine)
        : this.commercialInvoiceLineRepository
    ).findOne({
      where: {
        id: commercialInvoiceLineId,
        commercialInvoice: { id: commercialInvoiceId },
        organization: {
          id:
            this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
              ? Not(IsNull())
              : this.request?.user?.organization?.id || -1
        }
      },
      relations: FIND_COMMERCIAL_INVOICE_LINE_RELATIONS
    });
  }

  private async getNextSequence(commercialInvoiceId: number, queryRunner?: QueryRunner) {
    // select largest sequence and + 1
    const commercialInvoiceLineRepository = queryRunner
      ? queryRunner.manager.getRepository(CommercialInvoiceLine)
      : this.commercialInvoiceLineRepository;

    const sequence = await commercialInvoiceLineRepository.findOne({
      where: { commercialInvoice: { id: commercialInvoiceId } },
      order: { sequence: "DESC" }
    });

    return (sequence?.sequence || 0) + 1;
  }

  async _createCommercialInvoiceLine(
    commercialInvoiceId: number,
    createDto: CreateCommercialInvoiceLineDto,
    queryRunner?: QueryRunner,
    errorMsgPrefix = ""
  ) {
    const commercialInvoice = await this.commercialInvoiceService.getCommercialInvoiceById(
      commercialInvoiceId,
      queryRunner
    );
    // if (!commercialInvoice) throw new NotFoundException(`${errorMsgPrefix}Commercial Invoice not found`);

    let newCommercialInvoiceLine = new CommercialInvoiceLine();
    newCommercialInvoiceLine.organization = this.request?.user?.organization;
    newCommercialInvoiceLine.createdBy = this.request?.user || null;
    newCommercialInvoiceLine.lastEditedBy = this.request?.user || null;
    newCommercialInvoiceLine.commercialInvoice = commercialInvoice;
    for (const [key, value] of Object.entries(createDto)) {
      if (value === undefined) continue;
      const propertyName = COMMERCIAL_INVOICE_LINE_NON_ID_KEYS.includes(key) ? key : key.replace(/Id$/, "");
      switch (propertyName) {
        case "temporaryProduct":
          if (typeof createDto.productId === "number") continue;
          try {
            newCommercialInvoiceLine.product = await this.productService.createProduct(
              value as CreateProductDto,
              queryRunner,
              true
            );
            newCommercialInvoiceLine.hsCode = newCommercialInvoiceLine.product?.hsCode;
          } catch (error) {
            throw new BadRequestException(
              `${errorMsgPrefix}Failed to create temporary product due to: ${error instanceof Error ? error.message : String(error)}`
            );
          }
          break;
        case "product":
          if (typeof value === "number") {
            if (!Number.isInteger(value) || value <= 0)
              throw new BadRequestException(`${errorMsgPrefix}productId must be a positive integer`);
            newCommercialInvoiceLine[propertyName] = await this.productService.getProductById(
              value,
              queryRunner
            );
            if (!newCommercialInvoiceLine[propertyName])
              throw new NotFoundException(`${errorMsgPrefix}${convertFromCamelCase(propertyName)} not found`);
            if (
              newCommercialInvoiceLine[propertyName].organization?.id !== this.request?.user?.organization?.id
            )
              throw new BadRequestException(
                `${errorMsgPrefix}${convertFromCamelCase(propertyName)} does not belong to the organization of current user`
              );
            newCommercialInvoiceLine.hsCode = newCommercialInvoiceLine[propertyName]?.hsCode;
          } else newCommercialInvoiceLine[propertyName] = null;
          break;
        case "vfd":
          if (typeof value === "number") {
            newCommercialInvoiceLine[propertyName] = await this.canadaVfdCodeService.getCanadaVfdCodeById(
              value,
              queryRunner
            );
            if (!newCommercialInvoiceLine[propertyName])
              throw new NotFoundException(`${errorMsgPrefix}${propertyName} not found`);
          } else newCommercialInvoiceLine[propertyName] = null;
          break;
        case "tt":
          if (typeof value === "number") {
            newCommercialInvoiceLine[propertyName] =
              await this.canadaTreatmentCodeService.getCanadaTreatmentCodeById(value, queryRunner);
            if (!newCommercialInvoiceLine[propertyName])
              throw new NotFoundException(`${errorMsgPrefix}${propertyName} not found`);
          } else newCommercialInvoiceLine[propertyName] = null;
          break;
        case "origin":
          if (typeof value === "number") {
            newCommercialInvoiceLine[propertyName] = await this.countryService.getCountryById(
              value,
              queryRunner
            );
            if (!newCommercialInvoiceLine[propertyName])
              throw new NotFoundException(`${errorMsgPrefix}${propertyName} not found`);
          } else newCommercialInvoiceLine[propertyName] = null;
          break;
        case "originState":
          const country = await this.countryService.getCountryById(createDto.originId, queryRunner);

          if (country.alpha2 === "US") {
            newCommercialInvoiceLine[propertyName] = await this.stateService.getStateById(value);
          } else newCommercialInvoiceLine[propertyName] = null;
          break;
        case "gstExemptCode":
          if (typeof value === "number") {
            newCommercialInvoiceLine[propertyName] =
              await this.canadaGstExemptCodeService.getCanadaGstExemptCodeById(value, queryRunner);
            if (!newCommercialInvoiceLine[propertyName])
              throw new NotFoundException(`${errorMsgPrefix}${propertyName} not found`);
          } else newCommercialInvoiceLine[propertyName] = null;
          break;
        case "tariffCode":
          if (typeof value === "number") {
            newCommercialInvoiceLine[propertyName] = await this.canadaTariffService.getCanadaTariffById(
              value,
              queryRunner
            );
            if (!newCommercialInvoiceLine[propertyName])
              throw new NotFoundException(`${errorMsgPrefix}${propertyName} not found`);
            if (newCommercialInvoiceLine[propertyName].hsCode.length !== 4)
              throw new BadRequestException(`${errorMsgPrefix}${propertyName} must be a 4-digit Tariff Code`);
          } else newCommercialInvoiceLine[propertyName] = null;
          break;
        case "exciseTaxCode":
          if (typeof value === "number") {
            newCommercialInvoiceLine[propertyName] =
              await this.canadaExciseTaxCodeService.getCanadaExciseTaxCodeById(value, queryRunner);
            if (!newCommercialInvoiceLine[propertyName])
              throw new NotFoundException(`${errorMsgPrefix}${propertyName} not found`);
          } else newCommercialInvoiceLine[propertyName] = null;
          break;
        case "measurements":
          if (Array.isArray(value)) {
            newCommercialInvoiceLine[propertyName] = value.map(
              (measurement) =>
                ({
                  type: measurement.type,
                  unitOfMeasure: measurement.unitOfMeasure,
                  value: measurement.value
                }) as any
            );
          } else newCommercialInvoiceLine[propertyName] = null;
          break;
        default:
          newCommercialInvoiceLine[propertyName] = value;
          break;
      }
    }

    // Set UOM based on HS code
    let lineTariff: CanadaTariff | null = null;
    if (newCommercialInvoiceLine.hsCode) {
      lineTariff = await this.canadaTariffService.getCanadaTariffByHsCode(
        newCommercialInvoiceLine.hsCode,
        true,
        queryRunner
      );
      newCommercialInvoiceLine.unitOfMeasure = lineTariff?.uom ?? UnitOfMeasure.NUMBER;

      if (Array.isArray(newCommercialInvoiceLine.measurements)) {
        // Check if measurement already has the same UOM.
        // If so, check if the values are the same.
        const sameUomMeasurements = newCommercialInvoiceLine.measurements.filter(
          (m) => m.unitOfMeasure === newCommercialInvoiceLine.unitOfMeasure
        );
        if (
          sameUomMeasurements.some(
            (m) => Math.abs(m.value - (newCommercialInvoiceLine.quantity || 0)) >= 0.01
          )
        )
          throw new BadRequestException(
            `Some measurements have the same UOM as the line but with different quantity`
          );
      }
    }

    // set sequence to the next number
    if (newCommercialInvoiceLine.sequence === undefined || newCommercialInvoiceLine.sequence === 0) {
      newCommercialInvoiceLine.sequence = await this.getNextSequence(commercialInvoiceId, queryRunner);
    }

    if (!newCommercialInvoiceLine.surtaxSubjectCode)
      newCommercialInvoiceLine.surtaxSubjectCode = SurtaxSubjectCode.NON_SUBJECT;
    if (!newCommercialInvoiceLine.vfd)
      newCommercialInvoiceLine.vfd = await this.getDefaultVfd(commercialInvoice, queryRunner);
    // if (!newCommercialInvoiceLine.tt)
    // newCommercialInvoiceLine.tt = await this.getDefaultTt(commercialInvoice, queryRunner);

    // FIXME: check tt requirement
    const missingRequiredKeys = COMMERCIAL_INVOICE_LINE_REQUIRED_KEYS.filter((key) =>
      [null, undefined].includes(newCommercialInvoiceLine[key])
    );
    if (missingRequiredKeys.length > 0)
      throw new BadRequestException(
        `${errorMsgPrefix}The following fields are required: ${missingRequiredKeys.join(", ")}`
      );

    if (!lineTariff)
      throw new BadRequestException(`${errorMsgPrefix}HS code is invalid under current Canada tariff`);
    return newCommercialInvoiceLine;
  }

  async _editCommercialInvoiceLine(
    commercialInvoiceId: number,
    commercialInvoiceLineId: number,
    editDto: EditCommercialInvoiceLineDto,
    queryRunner?: QueryRunner,
    errorMsgPrefix = ""
  ) {
    const commercialInvoice = await this.commercialInvoiceService.getCommercialInvoiceById(
      commercialInvoiceId,
      queryRunner
    );
    if (!commercialInvoice) throw new NotFoundException(`${errorMsgPrefix}Commercial Invoice not found`);
    const commercialInvoiceLine = await this.getCommercialInvoiceLineById(
      commercialInvoiceId,
      commercialInvoiceLineId,
      queryRunner
    );
    if (!commercialInvoiceLine)
      throw new NotFoundException(`${errorMsgPrefix}Commercial invoice line not found`);

    commercialInvoiceLine.lastEditedBy = this.request?.user || null;
    const { hsCode, ...restOfEditDto } = editDto;
    for (const [key, value] of Object.entries(restOfEditDto)) {
      if (value === undefined) continue;
      const propertyName = COMMERCIAL_INVOICE_LINE_NON_ID_KEYS.includes(key) ? key : key.replace(/Id$/, "");
      switch (propertyName) {
        case "temporaryProduct":
          if (typeof restOfEditDto.productId === "number") continue;
          try {
            commercialInvoiceLine.product = await this.productService.createProduct(
              value as CreateProductDto,
              queryRunner,
              true
            );
            commercialInvoiceLine.hsCode = commercialInvoiceLine.product?.hsCode;
          } catch (error) {
            throw new BadRequestException(
              `${errorMsgPrefix}Failed to create temporary product due to: ${error instanceof Error ? error.message : String(error)}`
            );
          }
          break;
        case "product":
          if (typeof value === "number") {
            commercialInvoiceLine[propertyName] = await this.productService.getProductById(
              value,
              queryRunner
            );
            if (!commercialInvoiceLine[propertyName])
              throw new NotFoundException(`${errorMsgPrefix}${propertyName} not found`);
            if (commercialInvoiceLine[propertyName].organization?.id !== this.request?.user?.organization?.id)
              throw new BadRequestException(
                `${errorMsgPrefix}${propertyName} does not belong to the organization of current user`
              );
            commercialInvoiceLine.hsCode = commercialInvoiceLine[propertyName]?.hsCode;
          } else commercialInvoiceLine[propertyName] = null;
          break;
        case "vfd":
          if (typeof value === "number") {
            commercialInvoiceLine[propertyName] = await this.canadaVfdCodeService.getCanadaVfdCodeById(
              value,
              queryRunner
            );
            if (!commercialInvoiceLine[propertyName])
              throw new NotFoundException(`${errorMsgPrefix}${propertyName} not found`);
          } else commercialInvoiceLine[propertyName] = null;
          break;
        case "tt":
          if (typeof value === "number") {
            commercialInvoiceLine[propertyName] =
              await this.canadaTreatmentCodeService.getCanadaTreatmentCodeById(value, queryRunner);
            if (!commercialInvoiceLine[propertyName])
              throw new NotFoundException(`${errorMsgPrefix}${propertyName} not found`);
          } else commercialInvoiceLine[propertyName] = null;
          break;
        case "origin":
          if (typeof value === "number") {
            const origin = await this.countryService.getCountryById(value, queryRunner);

            if (!origin) {
              throw new NotFoundException(`${errorMsgPrefix}${propertyName} not found`);
            }

            if (origin.alpha2 !== "US") {
              // unset the origin state
              console.log("unsetting origin state");
              commercialInvoiceLine["originState"] = null;
            }

            commercialInvoiceLine[propertyName] = origin;
          } else {
            commercialInvoiceLine[propertyName] = null;
          }
          break;
        case "originState":
          const country = await this.countryService.getCountryById(
            restOfEditDto.originId || commercialInvoiceLine.origin?.id,
            queryRunner
          );

          if (typeof value === "number" && country.alpha2 === "US") {
            commercialInvoiceLine[propertyName] = await this.stateService.getStateById(value);
          } else commercialInvoiceLine[propertyName] = null;
          break;
        case "gstExemptCode":
          if (typeof value === "number") {
            commercialInvoiceLine[propertyName] =
              await this.canadaGstExemptCodeService.getCanadaGstExemptCodeById(value, queryRunner);
            if (!commercialInvoiceLine[propertyName])
              throw new NotFoundException(`${errorMsgPrefix}${propertyName} not found`);
          } else commercialInvoiceLine[propertyName] = null;
          break;
        case "tariffCode":
          if (typeof value === "number") {
            commercialInvoiceLine[propertyName] = await this.canadaTariffService.getCanadaTariffById(
              value,
              queryRunner
            );
            if (!commercialInvoiceLine[propertyName])
              throw new NotFoundException(`${errorMsgPrefix}${propertyName} not found`);
            if (commercialInvoiceLine[propertyName].hsCode.length !== 4)
              throw new BadRequestException(`${errorMsgPrefix}${propertyName} must be a 4-digit Tariff Code`);
          } else commercialInvoiceLine[propertyName] = null;
          break;
        case "exciseTaxCode":
          if (typeof value === "number") {
            commercialInvoiceLine[propertyName] =
              await this.canadaExciseTaxCodeService.getCanadaExciseTaxCodeById(value, queryRunner);
            if (!commercialInvoiceLine[propertyName])
              throw new NotFoundException(`${errorMsgPrefix}${propertyName} not found`);
          } else commercialInvoiceLine[propertyName] = null;
          break;
        case "measurements":
          if (Array.isArray(value)) {
            commercialInvoiceLine[propertyName] = value.map(
              (measurement) =>
                ({
                  type: measurement.type,
                  unitOfMeasure: measurement.unitOfMeasure,
                  value: measurement.value
                }) as any
            );
          } else commercialInvoiceLine[propertyName] = null;
          break;
        default:
          commercialInvoiceLine[propertyName] = value;
          break;
      }
    }
    // If hsCode is provided, update both the line's and the product's HS code
    if (hsCode) {
      if (commercialInvoiceLine.product.productType !== ProductType.TEMPORARY)
        throw new BadRequestException(
          "Cannot update HS code of commercial invoice line when product is not temporary"
        );
      commercialInvoiceLine.hsCode = hsCode;
      if (commercialInvoiceLine.product.hsCode !== hsCode) {
        try {
          commercialInvoiceLine.product = await this.productService.editProduct(
            commercialInvoiceLine.product.id,
            { hsCode },
            [commercialInvoiceLine.id],
            queryRunner
          );
        } catch (error) {
          throw new BadRequestException(
            `${errorMsgPrefix}Failed to edit product HS code due to: ${error instanceof Error ? error.message : String(error)}`
          );
        }
      }
    }

    // Update UOM if HS code is updated
    let lineTariff: CanadaTariff | null = null;
    if (commercialInvoiceLine.hsCode) {
      lineTariff = await this.canadaTariffService.getCanadaTariffByHsCode(
        commercialInvoiceLine.hsCode,
        true,
        queryRunner
      );
      const newUOM = lineTariff?.uom ?? UnitOfMeasure.NUMBER;
      if (newUOM !== commercialInvoiceLine.unitOfMeasure) {
        commercialInvoiceLine.unitOfMeasure = newUOM;
        const sameUomMeasurements = Array.isArray(commercialInvoiceLine.measurements)
          ? commercialInvoiceLine.measurements.filter(
              (m) => m.unitOfMeasure === commercialInvoiceLine.unitOfMeasure
            )
          : [];
        if (typeof restOfEditDto.quantity !== "number") {
          // If no new quantity is provided, we assume the existing quantity is for the old UOM and need to recalculate quantity and unit price.
          const newQuantity = sameUomMeasurements.length > 0 ? sameUomMeasurements[0].value : 0;
          const newUnitPrice = newQuantity > 0 ? commercialInvoiceLine.totalLineValue / newQuantity : 0;
          commercialInvoiceLine.quantity = newQuantity;
          commercialInvoiceLine.unitPrice = newUnitPrice;
        } else {
          // If new quantity is provided, we assume it's for the new UOM and need to check the values of the same UOM measurements.
          if (
            sameUomMeasurements.some((m) => Math.abs(m.value - (commercialInvoiceLine.quantity || 0)) >= 0.01)
          )
            throw new BadRequestException(
              "Some measurements have the same UOM as the line but with different quantity"
            );
        }
      }
    }

    const missingRequiredKeys = COMMERCIAL_INVOICE_LINE_REQUIRED_KEYS.filter((key) =>
      [null, undefined].includes(commercialInvoiceLine[key])
    );
    if (missingRequiredKeys.length > 0)
      throw new BadRequestException(
        `${errorMsgPrefix}The following fields are required: ${missingRequiredKeys.join(", ")}`
      );
    if (!lineTariff)
      throw new BadRequestException(`${errorMsgPrefix}HS code is invalid under current Canada tariff`);

    return commercialInvoiceLine;
  }

  async createCommercialInvoiceLine(
    commercialInvoiceId: number,
    createDto: CreateCommercialInvoiceLineDto,
    queryRunner?: QueryRunner
  ) {
    const tQueryRunner = queryRunner ?? this.dataSource.createQueryRunner();
    if (!queryRunner) {
      await tQueryRunner.connect();
      await tQueryRunner.startTransaction();
    }

    try {
      // create a line
      let newCommercialInvoiceLine = await this._createCommercialInvoiceLine(
        commercialInvoiceId,
        createDto,
        tQueryRunner
      );

      // Get the shipment related to the commercial invoice
      const shipment = await tQueryRunner.manager.findOne(Shipment, {
        where: {
          commercialInvoices: {
            id: newCommercialInvoiceLine.commercialInvoice.id
          }
        },
        select: ["id"]
      });

      // persist the line
      newCommercialInvoiceLine = await tQueryRunner.manager
        .getRepository(CommercialInvoiceLine)
        .save(newCommercialInvoiceLine);
      await this.transactionalEventEmitterService.enqueueEvent(
        CommercialInvoiceEvent.COMMERCIAL_INVOICE_LINE_CREATED,
        tQueryRunner,
        new CommercialInvoiceLineCreatedEventDto(
          commercialInvoiceId,
          newCommercialInvoiceLine.id,
          shipment?.id ?? -1,
          this.request?.user,
          createDto
        )
      );

      // check and convert temporary product to regular product
      await this.productService.checkAndConvertTemporaryToRegularProduct(
        newCommercialInvoiceLine.product.id,
        tQueryRunner
      );

      // commit the transaction
      if (!queryRunner) await tQueryRunner.commitTransaction();

      // return the line

      // FIXME: this is a workaround, if any error is thrown from the get commercial invoice line by id, we will discard them
      try {
        return await this.getCommercialInvoiceLineById(
          commercialInvoiceId,
          newCommercialInvoiceLine.id,
          queryRunner
        );
      } catch (error) {
        this.logger.error(`Error getting commercial invoice line by id: ${error}`, error.stack);
      }
    } catch (error) {
      if (!queryRunner && tQueryRunner.isTransactionActive) await tQueryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner) await tQueryRunner.release();
    }
  }

  async editCommercialInvoiceLine(
    commercialInvoiceId: number,
    commercialInvoiceLineId: number,
    editDto: EditCommercialInvoiceLineDto,
    queryRunner?: QueryRunner
  ) {
    const tQueryRunner = queryRunner ?? this.dataSource.createQueryRunner();

    if (!queryRunner) {
      await tQueryRunner.connect();
      await tQueryRunner.startTransaction();
    }

    try {
      // get the original line
      let originalCommercialInvoiceLine = await this.getCommercialInvoiceLineById(
        commercialInvoiceId,
        commercialInvoiceLineId,
        tQueryRunner
      );

      // edit the line
      let commercialInvoiceLine = await this._editCommercialInvoiceLine(
        commercialInvoiceId,
        commercialInvoiceLineId,
        editDto,
        tQueryRunner
      );

      // persist the line
      commercialInvoiceLine = await tQueryRunner.manager
        .getRepository(CommercialInvoiceLine)
        .save(commercialInvoiceLine);

      // check and convert temporary product to regular product
      await this.productService.checkAndConvertTemporaryToRegularProduct(
        commercialInvoiceLine.product.id,
        tQueryRunner
      );

      // delete unreferenced temporary products
      await this.productService.checkAndRemoveUnreferencedTemporaryProducts(
        [originalCommercialInvoiceLine.product.id],
        tQueryRunner
      );

      // Get the shipment related to the commercial invoice
      const shipment = await tQueryRunner.manager.findOne(Shipment, {
        where: {
          commercialInvoices: {
            id: commercialInvoiceLine.commercialInvoice.id
          }
        },
        select: ["id"]
      });

      await this.transactionalEventEmitterService.enqueueEvent(
        CommercialInvoiceEvent.COMMERCIAL_INVOICE_LINE_EDITED,
        tQueryRunner,
        new CommercialInvoiceLineEditedEventDto(
          commercialInvoiceId,
          commercialInvoiceLine.id,
          shipment?.id ?? -1,
          this.request?.user,
          editDto
        )
      );

      if (!queryRunner) await tQueryRunner.commitTransaction();
      return await this.getCommercialInvoiceLineById(
        commercialInvoiceId,
        commercialInvoiceLine.id,
        queryRunner
      );
    } catch (error) {
      if (!queryRunner && tQueryRunner.isTransactionActive) await tQueryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner) await tQueryRunner.release();
    }
  }

  async deleteCommercialInvoiceLine(commercialInvoiceId: number, commercialInvoiceLineId: number) {
    const commercialInvoice =
      await this.commercialInvoiceService.getCommercialInvoiceById(commercialInvoiceId);
    if (!commercialInvoice) throw new NotFoundException("Commercial Invoice not found");
    const commercialInvoiceLine = await this.getCommercialInvoiceLineById(
      commercialInvoiceId,
      commercialInvoiceLineId
    );
    if (!commercialInvoiceLine) throw new NotFoundException("Commercial invoice line not found");
    await this.commercialInvoiceLineRepository.delete({
      id: commercialInvoiceLineId,
      commercialInvoice: { id: commercialInvoiceId }
    });
    // check and remove unreferenced temporary product
    await this.productService.checkAndRemoveUnreferencedTemporaryProduct(commercialInvoiceLine.product.id);
    await this.transactionalEventEmitterService.enqueueEvent(
      CommercialInvoiceEvent.COMMERCIAL_INVOICE_LINE_DELETED,
      null,
      new CommercialInvoiceLineDeletedEventDto(
        commercialInvoiceId,
        commercialInvoiceLineId,
        commercialInvoice?.shipment?.id ?? -1,
        this.request?.user
      )
    );
    return;
  }

  async batchUpdateCommercialInvoiceLines(
    commercialInvoiceId: number,
    batchUpdateCommercialInvoiceLinesDto: BatchUpdateCommercialInvoiceLinesDto
  ): Promise<BatchUpdateCommercialInvoiceLinesResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const savedCommercialInvoiceLineIds: Array<number> = [];
    try {
      const commercialInvoiceLineRepository = queryRunner.manager.getRepository(CommercialInvoiceLine);
      const { create: createList, edit: editList, delete: deleteList } = batchUpdateCommercialInvoiceLinesDto;
      const toBeSavedCommercialInvoiceLines: Array<CommercialInvoiceLine> = [];
      const deletedLineProductIds: Array<number> = [];

      const commercialInvoice = await this.commercialInvoiceService.getCommercialInvoiceById(
        commercialInvoiceId,
        queryRunner
      );
      if (!commercialInvoice) throw new NotFoundException("Commercial Invoice not found");

      for (let i = 0; i < (createList || []).length; i++) {
        const createCommercialInvoiceLineDto = createList[i];
        const errorMsgPrefix = `Error on create list index ${i}: `;
        let newCommercialInvoiceLine = await this._createCommercialInvoiceLine(
          commercialInvoiceId,
          createCommercialInvoiceLineDto,
          queryRunner,
          errorMsgPrefix
        );
        toBeSavedCommercialInvoiceLines.push(newCommercialInvoiceLine);
      }

      for (let i = 0; i < (editList || []).length; i++) {
        const editCommercialInvoiceLineDto = editList[i];
        const errorMsgPrefix = `Error on edit list index ${i}: `;
        const commercialInvoiceLine = await this.getCommercialInvoiceLineById(
          commercialInvoiceId,
          editCommercialInvoiceLineDto.id,
          queryRunner
        );
        if (!commercialInvoiceLine)
          throw new NotFoundException(`${errorMsgPrefix}Commercial invoice line not found`);
        let newCommercialInvoiceLine = await this._editCommercialInvoiceLine(
          commercialInvoiceId,
          commercialInvoiceLine.id,
          editCommercialInvoiceLineDto,
          queryRunner,
          errorMsgPrefix
        );
        toBeSavedCommercialInvoiceLines.push(newCommercialInvoiceLine);
      }

      for (let i = 0; i < (deleteList || []).length; i++) {
        const commercialInvoiceLineId = deleteList[i];
        const errorMsgPrefix = `Error on delete list index ${i}: `;
        const commercialInvoiceLine = await this.getCommercialInvoiceLineById(
          commercialInvoice.id,
          commercialInvoiceLineId,
          queryRunner
        );
        if (!commercialInvoiceLine)
          throw new NotFoundException(`${errorMsgPrefix}Commercial Invoice Line not found`);
        if (toBeSavedCommercialInvoiceLines.some((c) => c.id === commercialInvoiceLine.id))
          throw new BadRequestException(`${errorMsgPrefix}Commercial Invoice Line is being edited`);
        deletedLineProductIds.push(commercialInvoiceLine.product.id);
      }

      const savedCommercialInvoiceLines = await commercialInvoiceLineRepository.save(
        toBeSavedCommercialInvoiceLines
      );
      const savedCommercialInvoiceLineIds = savedCommercialInvoiceLines.map((c) => c.id);
      const productIds = savedCommercialInvoiceLines.map((c) => c.product.id);

      // for each line in the saved list, we will run the temporary product check and remove
      for (const savedProductId of productIds) {
        await this.productService.checkAndRemoveUnreferencedTemporaryProduct(savedProductId, queryRunner);
        await this.productService.checkAndConvertTemporaryToRegularProduct(savedProductId, queryRunner);
      }

      if (Array.isArray(deleteList) && deleteList.length > 0) {
        await commercialInvoiceLineRepository.delete({ id: In(deleteList) });
        await this.productService.checkAndRemoveUnreferencedTemporaryProducts(
          deletedLineProductIds,
          queryRunner
        );
        for (const deletedLineId of deleteList) {
          await this.transactionalEventEmitterService.enqueueEvent(
            CommercialInvoiceEvent.COMMERCIAL_INVOICE_LINE_DELETED,
            queryRunner,
            new CommercialInvoiceLineDeletedEventDto(
              commercialInvoiceId,
              deletedLineId,
              commercialInvoice?.shipment?.id ?? -1,
              this.request?.user
            )
          );
        }
      }

      for (const savedLineId of savedCommercialInvoiceLineIds) {
        const editDto = editList.find((dto) => dto.id === savedLineId);
        // TODO: Find a way to match the created line ID with the create DTO
        await this.transactionalEventEmitterService.enqueueEvent(
          editDto
            ? CommercialInvoiceEvent.COMMERCIAL_INVOICE_LINE_EDITED
            : CommercialInvoiceEvent.COMMERCIAL_INVOICE_LINE_CREATED,
          queryRunner,
          editDto
            ? new CommercialInvoiceLineEditedEventDto(
                commercialInvoiceId,
                savedLineId,
                commercialInvoice?.shipment?.id ?? -1,
                this.request?.user,
                editDto
              )
            : new CommercialInvoiceLineCreatedEventDto(
                commercialInvoiceId,
                savedLineId,
                commercialInvoice?.shipment?.id ?? -1,
                this.request?.user,
                null
              )
        );
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner.isReleased) queryRunner.release();
    }

    return {
      commercialInvoiceLines: await this.commercialInvoiceLineRepository.find({
        where: { id: In(savedCommercialInvoiceLineIds) },
        relations: FIND_COMMERCIAL_INVOICE_LINE_RELATIONS
      })
    };
  }

  async validateCommercialInvoiceLineCompliance(
    invoiceId: number,
    lineId: number
  ): Promise<ValidateCommercialInvoiceLineComplianceResponseDto> {
    const commercialInvoice = await this.commercialInvoiceService.getCommercialInvoiceById(invoiceId);
    if (!commercialInvoice) throw new NotFoundException("Commercial Invoice not found");
    const commercialInvoiceLine = await this.getCommercialInvoiceLineById(commercialInvoice.id, lineId);
    if (!commercialInvoiceLine) throw new NotFoundException("Commercial Invoice Line not found");
    const shipment = await this.shipmentService.getShipmentById(commercialInvoice?.shipment?.id);
    if (!shipment) throw new NotFoundException("Commercial Invoice Shipment not found");
    if (!shipment?.requiresReupload && this.complianceValidationService.isShipmentEntryUploaded(shipment))
      throw new BadRequestException("Shipment entry is already uploaded");

    return this.complianceValidationService.validateCommercialInvoiceLineCompliances(
      await this.complianceValidationService.getCommercialInvoiceLineCompliances([commercialInvoiceLine]),
      this.complianceValidationService.isDemoShipment(shipment) // Skip filings validation for demo shipments
    )[0];
  }

  /**
   * Get the commercial invoice line fallbacks for a commercial invoice line or from a product
   *
   * @param commercialInvoiceId - The ID of the commercial invoice
   * @param lineId - The ID of the commercial invoice line
   * @param productId - The ID of the product
   * @returns The fallbacks for the commercial invoice line or product
   */
  async getCommercialInvoiceLineWithFallbacks(
    commercialInvoiceId: number,
    lineId?: number,
    productId?: number
  ): Promise<FallbackEnricherResponseDto> {
    if (!lineId && !productId) {
      throw new BadRequestException("Either lineId or productId must be provided");
    }

    if (productId) {
      const product = await this.productService.getProductById(productId);
      if (!product) throw new NotFoundException("Product not found");
      const fallbacks = await this.commercialInvoiceLineEnricher.getFallbacksFromProduct(product);
      return {
        fields: fallbacks
      };
    }

    const commercialInvoiceLine = await this.getCommercialInvoiceLineById(commercialInvoiceId, lineId);

    if (!commercialInvoiceLine) throw new NotFoundException("Commercial Invoice Line not found");

    const fallbacks = await this.commercialInvoiceLineEnricher.getFallbacks(commercialInvoiceLine);

    return {
      fields: fallbacks
    };
  }
}
