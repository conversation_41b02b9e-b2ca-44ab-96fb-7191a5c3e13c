import { ProductService } from "@/product/product.service";
import { ComplianceValidationService } from "@/shipment/services/compliance-validation.service";
import { BadRequestException, NotFoundException } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { Test, TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import {
  AuthenticatedRequest,
  CommercialInvoice,
  CommercialInvoiceLine,
  CountryService,
  FIND_COMMERCIAL_INVOICE_RELATIONS,
  OrganizationService,
  StateService,
  TransactionalEventEmitterService,
  UserPermission
} from "nest-modules";
import { DataSource, Repository } from "typeorm";
import { ShipmentService } from "../shipment/services/shipment.service";
import { TradePartnerService } from "../trade-partner/trade-partner.service";
import { CommercialInvoiceService } from "./commercial-invoice.service";

describe("CommercialInvoiceService", () => {
  let service: CommercialInvoiceService;
  let ciRepository: Repository<CommercialInvoice>;
  let ciLineRepository: Repository<CommercialInvoiceLine>;
  let productService: ProductService;
  let shipmentService: ShipmentService;
  let countryService: CountryService;
  let stateService: StateService;
  let tradePartnerService: TradePartnerService;
  let complianceValidationService: ComplianceValidationService;
  let eventEmitter: EventEmitter2;

  const mockUser = {
    id: 1,
    organization: { id: 1 },
    permission: UserPermission.ORGANIZATION_ADMIN
  };

  const mockRequest: AuthenticatedRequest = {
    user: mockUser
  } as AuthenticatedRequest;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CommercialInvoiceService,
        {
          provide: getRepositoryToken(CommercialInvoice),
          useValue: {
            findOne: jest.fn(),
            findAndCount: jest.fn(),
            existsBy: jest.fn(),
            save: jest.fn(),
            delete: jest.fn()
          }
        },
        {
          provide: getRepositoryToken(CommercialInvoiceLine),
          useValue: {
            find: jest.fn(),
            delete: jest.fn()
          }
        },
        {
          provide: REQUEST,
          useValue: mockRequest
        },
        {
          provide: ShipmentService,
          useValue: {
            getShipmentById: jest.fn()
          }
        },
        {
          provide: ComplianceValidationService,
          useValue: {
            validateCommercialInvoiceCompliances: jest.fn(),
            getCommercialInvoiceCompliances: jest.fn(),
            isShipmentEntryUploaded: jest.fn()
          }
        },
        {
          provide: TradePartnerService,
          useValue: {
            getTradePartnerById: jest.fn()
          }
        },
        {
          provide: CountryService,
          useValue: {
            getCountryById: jest.fn()
          }
        },
        {
          provide: StateService,
          useValue: {
            getStateById: jest.fn()
          }
        },
        {
          provide: OrganizationService,
          useValue: {}
        },
        {
          provide: TransactionalEventEmitterService,
          useValue: {
            enqueueEvent: jest.fn()
          }
        },
        {
          provide: DataSource,
          useValue: {
            createQueryRunner: jest.fn()
          }
        },
        {
          provide: EventEmitter2,
          useValue: {
            emit: jest.fn()
          }
        },
        {
          provide: ProductService,
          useValue: {
            checkAndRemoveUnreferencedTemporaryProducts: jest.fn()
          }
        }
      ]
    }).compile();

    service = await module.resolve<CommercialInvoiceService>(CommercialInvoiceService);
    ciRepository = module.get<Repository<CommercialInvoice>>(getRepositoryToken(CommercialInvoice));
    ciLineRepository = module.get<Repository<CommercialInvoiceLine>>(
      getRepositoryToken(CommercialInvoiceLine)
    );
    productService = await module.resolve<ProductService>(ProductService);
    shipmentService = await module.resolve<ShipmentService>(ShipmentService);
    countryService = await module.resolve<CountryService>(CountryService);
    tradePartnerService = await module.resolve<TradePartnerService>(TradePartnerService);
    complianceValidationService =
      await module.resolve<ComplianceValidationService>(ComplianceValidationService);
    eventEmitter = module.get<EventEmitter2>(EventEmitter2);
  });

  describe("getCommercialInvoiceById", () => {
    it("should return a commercial invoice by id", async () => {
      // Arrange
      const commercialInvoiceId = 1;
      const mockInvoice = { id: commercialInvoiceId };

      jest.spyOn(ciRepository, "findOne").mockResolvedValue(mockInvoice as any);

      // Act
      const result = await service.getCommercialInvoiceById(commercialInvoiceId);

      // Assert
      expect(ciRepository.findOne).toHaveBeenCalledWith({
        where: {
          id: commercialInvoiceId,
          organization: {
            id: mockUser.organization.id
          }
        },
        relations: FIND_COMMERCIAL_INVOICE_RELATIONS
      });
      expect(result).toEqual(mockInvoice);
    });

    it("should handle queryRunner if provided", async () => {
      // Arrange
      const commercialInvoiceId = 1;
      const mockInvoice = { id: commercialInvoiceId };
      const mockQueryRunner = {
        manager: {
          getRepository: jest.fn().mockReturnValue({
            findOne: jest.fn().mockResolvedValue(mockInvoice)
          })
        }
      };

      // Act
      const result = await service.getCommercialInvoiceById(commercialInvoiceId, mockQueryRunner as any);

      // Assert
      expect(mockQueryRunner.manager.getRepository).toHaveBeenCalled();
      expect(result).toEqual(mockInvoice);
    });
  });

  describe("clearCommercialInvoiceLines", () => {
    it("should clear commercial invoice lines and remove unreferenced temporary products", async () => {
      // Arrange
      const commercialInvoiceId = 1;
      const productIds = [101, 102];
      const mockLines = [{ product: { id: 101 } }, { product: { id: 102 } }];

      jest.spyOn(ciLineRepository, "find").mockResolvedValue(mockLines as any);
      jest.spyOn(ciLineRepository, "delete").mockResolvedValue(undefined);
      jest.spyOn(productService, "checkAndRemoveUnreferencedTemporaryProducts").mockResolvedValue(undefined);

      // Act
      await service.clearCommercialInvoiceLines(commercialInvoiceId);

      // Assert
      expect(ciLineRepository.find).toHaveBeenCalledWith({
        where: {
          commercialInvoice: { id: commercialInvoiceId }
        },
        relations: ["product"]
      });
      expect(ciLineRepository.delete).toHaveBeenCalledWith({
        commercialInvoice: { id: commercialInvoiceId }
      });
      expect(productService.checkAndRemoveUnreferencedTemporaryProducts).toHaveBeenCalledWith(productIds);
    });
  });

  describe("getCommercialInvoices", () => {
    it("should return commercial invoices for organization user", async () => {
      // Arrange
      const mockDto = { page: 1, limit: 10 };
      const mockInvoices = [{ id: 1 }, { id: 2 }];
      const mockTotal = 2;

      jest.spyOn(ciRepository, "findAndCount").mockResolvedValue([mockInvoices as any, mockTotal]);

      // Act
      const result = await service.getCommercialInvoices(mockDto);

      // Assert
      expect(ciRepository.findAndCount).toHaveBeenCalled();
      expect(result).toEqual({
        commercialInvoices: mockInvoices,
        total: mockTotal,
        skip: expect.any(Number),
        limit: expect.any(Number)
      });
    });
  });

  describe("validateCommercialInvoiceCompliance", () => {
    it("should throw NotFoundException when commercial invoice is not found", async () => {
      // Arrange
      const commercialInvoiceId = 999;
      jest.spyOn(service, "getCommercialInvoiceById").mockResolvedValue(null);

      // Act & Assert
      await expect(service.validateCommercialInvoiceCompliance(commercialInvoiceId)).rejects.toThrow(
        NotFoundException
      );
    });

    it("should throw BadRequestException when shipment entry is already uploaded", async () => {
      // Arrange
      const commercialInvoiceId = 1;
      const mockInvoice = {
        id: commercialInvoiceId,
        shipment: {
          requiresReupload: false
        }
      };

      jest.spyOn(service, "getCommercialInvoiceById").mockResolvedValue(mockInvoice as any);
      jest.spyOn(complianceValidationService, "isShipmentEntryUploaded").mockReturnValue(true);

      // Act & Assert
      await expect(service.validateCommercialInvoiceCompliance(commercialInvoiceId)).rejects.toThrow(
        BadRequestException
      );
    });

    it("should return validation result when compliance check is successful", async () => {
      // Arrange
      const commercialInvoiceId = 1;
      const mockInvoice = {
        id: commercialInvoiceId,
        shipment: {
          requiresReupload: true
        }
      };
      const mockCompliances = [{ id: commercialInvoiceId }];
      const mockValidationResult = { isValid: true };

      jest.spyOn(service, "getCommercialInvoiceById").mockResolvedValue(mockInvoice as any);
      jest
        .spyOn(complianceValidationService, "getCommercialInvoiceCompliances")
        .mockResolvedValue(mockCompliances as any);
      jest
        .spyOn(complianceValidationService, "validateCommercialInvoiceCompliances")
        .mockReturnValue([mockValidationResult] as any);

      // Act
      const result = await service.validateCommercialInvoiceCompliance(commercialInvoiceId);

      // Assert
      expect(result).toEqual(mockValidationResult);
    });
  });

  describe("createCommercialInvoice", () => {
    it("should create a commercial invoice and return it", async () => {
      // Arrange
      const mockDto = {
        invoiceNumber: "INV-001",
        shipmentId: 1,
        currency: "USD",
        grossWeight: 100,
        weightUOM: "kg",
        numberOfPackages: 1,
        packageUOM: "pcs",
        countryOfExport: 1,
        vendor: 1
      };

      const mockShipment = {
        id: 1,
        organization: { id: 1 }
      };

      const mockTradePartner = {
        id: 1,
        organization: { id: 1 }
      };

      const mockCountry = {
        id: 1,
        organization: { id: 1 }
      };

      const mockSavedInvoice = {
        id: 1,
        ...mockDto,
        organization: mockUser.organization,
        createdBy: mockUser,
        lastEditedBy: mockUser
      };

      jest.spyOn(shipmentService, "getShipmentById").mockResolvedValue(mockShipment as any);
      jest.spyOn(tradePartnerService, "getTradePartnerById").mockResolvedValue(mockTradePartner as any);
      jest.spyOn(countryService, "getCountryById").mockResolvedValue(mockCountry as any);
      jest.spyOn(ciRepository, "existsBy").mockResolvedValue(false);
      jest.spyOn(ciRepository, "save").mockResolvedValue(mockSavedInvoice as any);
      jest.spyOn(service, "getCommercialInvoiceById").mockResolvedValue(mockSavedInvoice as any);

      // Act
      const result = await service.createCommercialInvoice(mockDto as any);

      // Assert
      expect(ciRepository.save).toHaveBeenCalled();
      expect(eventEmitter.emit).toHaveBeenCalled();
      expect(result).toEqual(mockSavedInvoice);
    });
  });

  describe("deleteCommercialInvoice", () => {
    it("should delete a commercial invoice", async () => {
      // Arrange
      const commercialInvoiceId = 1;
      const mockInvoice = { id: commercialInvoiceId, shipment: {} };

      jest.spyOn(service, "getCommercialInvoiceById").mockResolvedValue(mockInvoice as any);
      jest.spyOn(service, "clearCommercialInvoiceLines").mockResolvedValue(undefined);
      jest.spyOn(ciRepository, "delete").mockResolvedValue(undefined as any);

      // Act
      await service.deleteCommercialInvoice(commercialInvoiceId);

      // Assert
      expect(service.clearCommercialInvoiceLines).toHaveBeenCalledWith(commercialInvoiceId);
      expect(ciRepository.delete).toHaveBeenCalledWith({ id: commercialInvoiceId });
      expect(eventEmitter.emit).toHaveBeenCalled();
    });

    it("should throw NotFoundException when commercial invoice is not found", async () => {
      // Arrange
      const commercialInvoiceId = 999;
      jest.spyOn(service, "getCommercialInvoiceById").mockResolvedValue(null);

      // Act & Assert
      await expect(service.deleteCommercialInvoice(commercialInvoiceId)).rejects.toThrow(NotFoundException);
    });
  });
});
