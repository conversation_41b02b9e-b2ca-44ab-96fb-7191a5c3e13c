import { ImporterModule } from "@/importer/importer.module";
import { forwardRef, <PERSON><PERSON><PERSON>, Scope } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import {
  CanadaTreatmentCode,
  CanadaVfdCode,
  CommercialInvoice,
  CommercialInvoiceLine,
  CountryModule,
  TransactionalEventEmitterModule,
  UserModule
} from "nest-modules";
import { ProductModule } from "../product/product.module";
import { ShipmentModule } from "../shipment/shipment.module";
import { TradePartnerModule } from "../trade-partner/trade-partner.module";
import { CommercialInvoiceLineSyncService } from "./commercial-invoice-line-sync.service";
import { CommercialInvoiceLineService } from "./commercial-invoice-line.service";
import { CommercialInvoiceController } from "./commercial-invoice.controller";
import { CommercialInvoiceService } from "./commercial-invoice.service";
import { CommercialInvoiceEnricher, CommercialInvoiceLineEnricher } from "./enrichers";
import { listeners } from "./listeners";

@Module({
  imports: [
    TypeOrmModule.forFeature([CommercialInvoice, CommercialInvoiceLine, CanadaTreatmentCode, CanadaVfdCode]),
    forwardRef(() => ShipmentModule),
    forwardRef(() => TradePartnerModule),
    CountryModule,
    forwardRef(() => UserModule),
    forwardRef(() => ProductModule),
    forwardRef(() => ImporterModule),
    forwardRef(() => TransactionalEventEmitterModule)
  ],
  controllers: [CommercialInvoiceController],
  providers: [
    // Services
    {
      provide: CommercialInvoiceService,
      useClass: CommercialInvoiceService,
      scope: Scope.REQUEST
    },
    CommercialInvoiceLineService,
    CommercialInvoiceLineSyncService,

    // Listeners
    ...listeners,

    // Enrichers
    CommercialInvoiceEnricher,
    CommercialInvoiceLineEnricher
  ],
  exports: [
    CommercialInvoiceService,
    CommercialInvoiceLineService,
    CommercialInvoiceLineSyncService,
    CommercialInvoiceEnricher,
    CommercialInvoiceLineEnricher
  ]
})
export class CommercialInvoiceModule {}
