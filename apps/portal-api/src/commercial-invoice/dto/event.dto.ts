import {
  BatchUpdateCommercialInvoiceLinesDto,
  CreateCommercialInvoiceDto,
  CreateCommercialInvoiceLineDto,
  EditCommercialInvoiceDto,
  EditCommercialInvoiceLineDto,
  User
} from "nest-modules";

export class BaseCommercialInvoiceEventDto {
  constructor(
    /**
     * ID of the commercial invoice
     */
    public readonly commercialInvoiceId: number,
    /**
     * ID of the shipment which the commercial invoice belongs to
     */
    public readonly shipmentId: number,
    /**
     * User who triggered the event.
     */
    public readonly user?: User | null
  ) {}
}

export class CommercialInvoiceCreatedEventDto extends BaseCommercialInvoiceEventDto {
  constructor(
    commercialInvoiceId: number,
    shipmentId: number,
    user?: User | null,
    /**
     * DTO used to create the commercial invoice.
     */
    public readonly createCommercialInvoiceDto?: CreateCommercialInvoiceDto | null
  ) {
    super(commercialInvoiceId, shipmentId, user);
  }
}

export class CommercialInvoiceEditedEventDto extends BaseCommercialInvoiceEventDto {
  constructor(
    commercialInvoiceId: number,
    shipmentId: number,
    user?: User | null,
    /**
     * DTO used to edit the commercial invoice.
     */
    public readonly editCommercialInvoiceDto?: EditCommercialInvoiceDto | null
  ) {
    super(commercialInvoiceId, shipmentId, user);
  }
}

export class CommercialInvoiceDeletedEventDto extends BaseCommercialInvoiceEventDto {
  constructor(commercialInvoiceId: number, shipmentId: number, user?: User | null) {
    super(commercialInvoiceId, shipmentId, user);
  }
}

export class BaseCommercialInvoiceLineEventDto {
  constructor(
    /**
     * ID of the commercial invoice
     */
    public readonly commercialInvoiceId: number,
    /**
     * ID of the commercial invoice line
     */
    public readonly commercialInvoiceLineId: number,
    /**
     * ID of the shipment which the commercial invoice line belongs to
     */
    public readonly shipmentId: number,
    /**
     * User who triggered the event.
     */
    public readonly user?: User | null
  ) {}
}

export class CommercialInvoiceLineCreatedEventDto extends BaseCommercialInvoiceLineEventDto {
  constructor(
    commercialInvoiceId: number,
    commercialInvoiceLineId: number,
    shipmentId: number,
    user?: User | null,
    /**
     * DTO used to create the commercial invoice line.
     */
    public readonly createCommercialInvoiceLineDto?: CreateCommercialInvoiceLineDto | null
  ) {
    super(commercialInvoiceId, commercialInvoiceLineId, shipmentId, user);
  }
}

export class CommercialInvoiceLineEditedEventDto extends BaseCommercialInvoiceLineEventDto {
  constructor(
    commercialInvoiceId: number,
    commercialInvoiceLineId: number,
    shipmentId: number,
    user?: User | null,
    /**
     * DTO used to edit the commercial invoice line.
     */
    public readonly editCommercialInvoiceLineDto?: EditCommercialInvoiceLineDto | null
  ) {
    super(commercialInvoiceId, commercialInvoiceLineId, shipmentId, user);
  }
}

export class CommercialInvoiceLineDeletedEventDto extends BaseCommercialInvoiceLineEventDto {
  constructor(
    commercialInvoiceId: number,
    commercialInvoiceLineId: number,
    shipmentId: number,
    user?: User | null
  ) {
    super(commercialInvoiceId, commercialInvoiceLineId, shipmentId, user);
  }
}
