import { FallbackEnricher } from "@/fallback-enricher";
import { Inject, Injectable, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import {
  CanadaExciseTaxCode,
  CanadaGstExemptCode,
  CanadaTreatmentCode,
  CertificateOfOrigin,
  CommercialInvoiceLine,
  Country,
  FieldFallbackResult,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleSourceDatabaseTable,
  Product,
  QueryMatchingResultsResponseDto,
  RuleQueryService
} from "nest-modules";
import { Repository } from "typeorm";
import {
  COUNTRY_OF_ORIGIN_ALPHA2_TO_TARIFF_TREATMENT_CODE_MAP,
  DEFAULT_CANADA_TARIFF_TREATMENT_CODE
} from "../rules/tariff-treatment-code";

@Injectable()
export class CommercialInvoiceLineEnricher implements FallbackEnricher<CommercialInvoiceLine> {
  constructor(
    @InjectRepository(CommercialInvoiceLine)
    private readonly commercialInvoiceLineRepository: Repository<CommercialInvoiceLine>,
    @Inject(RuleQueryService)
    private readonly ruleQueryService: RuleQueryService,
    @InjectRepository(CanadaTreatmentCode)
    private readonly canadaTreatmentCodeRepository: Repository<CanadaTreatmentCode>
  ) {}

  private readonly logger = new Logger(CommercialInvoiceLineEnricher.name, { timestamp: true });

  /**
   * Fields that can be enriched for a commercial invoice line
   *
   * - tt: Tariff Treatment Code
   * - exciseTaxCode: Excise Tax Code
   * - gstExemptCode: GST Exempt Code
   */
  private readonly ENRICHABLE_FIELDS: Array<keyof CommercialInvoiceLine> = [
    "tt",
    "exciseTaxCode",
    "gstExemptCode"
  ];

  private async getCertifiedOriginCountry(
    product: Product,
    matchingResults?: QueryMatchingResultsResponseDto
  ): Promise<Country> {
    const sourceRecords =
      (matchingResults.matchingResults.find(
        (result) =>
          result.sourceTable === MatchingRuleSourceDatabaseTable.CERTIFICATE_OF_ORIGIN &&
          result.sourceRecords.length > 0
      )?.sourceRecords as CertificateOfOrigin[]) ?? [];

    // find certificate that is not expired
    const certificateOfOrigin = sourceRecords.find((co) => co.isValid);

    return certificateOfOrigin?.countryOfOrigin as Country;
  }

  private async getTTEntity(code: number): Promise<CanadaTreatmentCode> {
    const tt = await this.canadaTreatmentCodeRepository.findOne({
      where: {
        code: code
      },
      cache: {
        id: `db:${this.canadaTreatmentCodeRepository.metadata.tableName}:${code}`,
        milliseconds: 30000
      }
    });

    return tt;
  }

  private async getTT(certifiedOriginCountry: Country): Promise<CanadaTreatmentCode> {
    if (!certifiedOriginCountry) {
      return this.getTTEntity(DEFAULT_CANADA_TARIFF_TREATMENT_CODE);
    }

    const ttCode =
      COUNTRY_OF_ORIGIN_ALPHA2_TO_TARIFF_TREATMENT_CODE_MAP[certifiedOriginCountry.alpha2] ||
      DEFAULT_CANADA_TARIFF_TREATMENT_CODE;

    return this.getTTEntity(ttCode);
  }

  private async getExciseTaxCode(
    product: Product,
    matchingResults?: QueryMatchingResultsResponseDto
  ): Promise<CanadaExciseTaxCode> {
    const sourceRecords =
      (matchingResults.matchingResults.find(
        (result) =>
          result.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE &&
          result.sourceRecords.length > 0
      )?.sourceRecords as CanadaExciseTaxCode[]) ?? [];

    return sourceRecords?.length ? sourceRecords[0] : null;
  }

  private async getGSTExemptCode(
    product: Product,
    matchingResults?: QueryMatchingResultsResponseDto
  ): Promise<CanadaGstExemptCode> {
    const sourceRecords =
      (matchingResults.matchingResults.find(
        (result) =>
          result.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE &&
          result.sourceRecords.length > 0
      )?.sourceRecords as CanadaGstExemptCode[]) ?? [];

    return sourceRecords?.length ? sourceRecords[0] : null;
  }

  /**
   * Get fallbacks for a commercial invoice line
   *
   * @param entity - The commercial invoice line to get fallbacks for
   * @param missingFields - The fields to get fallbacks for, if not provided, all enrichable fields will be used
   * @returns The fallbacks for the commercial invoice line
   */
  async getFallbacks(
    entity: CommercialInvoiceLine,
    missingFields?: Array<keyof CommercialInvoiceLine>
  ): Promise<FieldFallbackResult[]> {
    // use provided missing fields or default to all enrichable fields
    missingFields = missingFields ?? this.ENRICHABLE_FIELDS;

    return this.getFallbacksFromProduct(entity.product, missingFields);
  }

  /**
   * Get fallbacks from a product
   *
   * @param product - The product to get fallbacks from
   * @param missingFields - The fields to get fallbacks for, if not provided, all enrichable fields will be used
   * @returns The fallbacks for the product
   */
  async getFallbacksFromProduct(
    product: Product,
    missingFields?: Array<keyof CommercialInvoiceLine>
  ): Promise<FieldFallbackResult[]> {
    const fallbacks: FieldFallbackResult[] = [];

    // use provided missing fields or default to all enrichable fields
    missingFields = missingFields ?? this.ENRICHABLE_FIELDS;

    // map missing fields to their corresponding source tables
    const missingFieldDependencyMap: Partial<
      Record<keyof CommercialInvoiceLine, MatchingRuleSourceDatabaseTable>
    > = {
      tt: MatchingRuleSourceDatabaseTable.CERTIFICATE_OF_ORIGIN,
      exciseTaxCode: MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE,
      gstExemptCode: MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE
    };

    // get all source tables for the missing fields
    const sourceTablesToQuery = new Set(missingFields.map((field) => missingFieldDependencyMap[field]));

    // query matching results for the product
    const matchingResults = await this.ruleQueryService.queryMatchingResults(
      {
        sourceTables: Array.from(sourceTablesToQuery),
        destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT,
        destinationId: product.id
      },
      undefined,
      true
    );

    if (missingFields.includes("tt")) {
      const certifiedOriginCountry = await this.getCertifiedOriginCountry(product, matchingResults);

      fallbacks.push({
        key: "tt",
        value: await this.getTT(certifiedOriginCountry),
        source: certifiedOriginCountry
          ? `A certificate of origin verifies this product comes from ${certifiedOriginCountry.alpha2}`
          : `No valid certificate of origin found for this product, use default TT`
      });
    }

    if (missingFields.includes("exciseTaxCode")) {
      const exciseTaxCode = await this.getExciseTaxCode(product, matchingResults);

      if (exciseTaxCode) {
        fallbacks.push({
          key: "exciseTaxCode",
          value: exciseTaxCode,
          source: "A matching excise tax code was found for this product"
        });
      }
    }

    if (missingFields.includes("gstExemptCode")) {
      const gstExemptCode = await this.getGSTExemptCode(product, matchingResults);

      if (gstExemptCode) {
        fallbacks.push({
          key: "gstExemptCode",
          value: gstExemptCode,
          source: "A matching GST exempt code was found for this product"
        });
      }
    }

    return fallbacks;
  }

  async enrich(entity: CommercialInvoiceLine): Promise<CommercialInvoiceLine> {
    // get all missing fields
    const missingFields = this.ENRICHABLE_FIELDS.filter(
      (field) => entity[field] === undefined || entity[field] === null
    );

    const fallbacks = await this.getFallbacks(entity, missingFields);

    if (missingFields.includes("tt")) {
      entity.tt = fallbacks.find((fallback) => fallback.key === "tt")?.value as CanadaTreatmentCode;
    }

    if (missingFields.includes("exciseTaxCode")) {
      entity.exciseTaxCode = fallbacks.find((fallback) => fallback.key === "exciseTaxCode")
        ?.value as CanadaExciseTaxCode;
    }

    if (missingFields.includes("gstExemptCode")) {
      entity.gstExemptCode = fallbacks.find((fallback) => fallback.key === "gstExemptCode")
        ?.value as CanadaGstExemptCode;
    }

    return entity;
  }

  /**
   * Enrich many commercial invoice lines
   *
   * TODO: switch to batch quering the matching rule
   *
   * @param entities - The commercial invoice lines to enrich
   * @returns The enriched commercial invoice lines
   */
  async enrichMany(entities: CommercialInvoiceLine[]): Promise<CommercialInvoiceLine[]> {
    this.logger.debug(`Enriching ${entities.length} commercial invoice lines...`);
    const enrichedEntities = [];

    for (const entity of entities) {
      enrichedEntities.push(await this.enrich(entity));
    }

    this.logger.debug(`Done`);

    return enrichedEntities;
  }
}
