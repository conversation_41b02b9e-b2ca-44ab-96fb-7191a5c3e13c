import { FallbackEnricher } from "@/fallback-enricher";
import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { CommercialInvoice, FieldFallbackResult } from "nest-modules";
import { Repository } from "typeorm";

@Injectable()
export class CommercialInvoiceEnricher implements FallbackEnricher<CommercialInvoice> {
  constructor(
    @InjectRepository(CommercialInvoice)
    private readonly commercialInvoiceRepository: Repository<CommercialInvoice>
  ) {}

  async getFallbacks(entity: CommercialInvoice): Promise<FieldFallbackResult[]> {
    const fallbacks: FieldFallbackResult[] = [];

    const ciWithShipmentConsignee = await this.commercialInvoiceRepository.findOne({
      where: { id: entity.id },
      relations: {
        shipment: {
          consignee: {
            country: true
          }
        }
      },
      select: {
        id: true
      }
    });

    if (!entity.shipTo && ciWithShipmentConsignee?.shipment?.consignee) {
      fallbacks.push({
        key: "shipTo",
        value: ciWithShipmentConsignee.shipment.consignee,
        source: "shipment consignee"
      });
    }

    return fallbacks;
  }

  async enrich(entity: CommercialInvoice): Promise<CommercialInvoice> {
    const fallbacks = await this.getFallbacks(entity);
    if (fallbacks.length) {
      const shipToFallback = fallbacks.find((fallback) => fallback.key === "shipTo");
      if (shipToFallback) {
        entity.shipTo = shipToFallback.value;
      }
    }
    return entity;
  }
}
