import { ProductService } from "@/product/product.service";
import { ShipmentService } from "@/shipment/services/shipment.service";
import { forwardRef, Inject, Injectable, Logger } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import {
  AuthenticatedRequest,
  CanadaTariffService,
  CommercialInvoice,
  CommercialInvoiceLine,
  CustomsStatus,
  FIND_PRODUCT_RELATIONS,
  Product,
  ProductType,
  Shipment,
  TransactionalEventEmitterService,
  UnitOfMeasure
} from "nest-modules";
import { DataSource, In, QueryRunner, Repository } from "typeorm";
import { CommercialInvoiceService } from "./commercial-invoice.service";
import { CommercialInvoiceLineEditedEventDto } from "./dto/event.dto";
import { CommercialInvoiceEvent } from "./types/event.types";
@Injectable()
export class CommercialInvoiceLineSyncService {
  private readonly logger = new Logger(CommercialInvoiceLineSyncService.name);

  constructor(
    @InjectRepository(CommercialInvoiceLine)
    private readonly commercialInvoiceLineRepository: Repository<CommercialInvoiceLine>,
    @Inject(forwardRef(() => ProductService))
    private readonly productService: ProductService,
    @Inject(forwardRef(() => CommercialInvoiceService))
    private readonly commercialInvoiceService: CommercialInvoiceService,
    @Inject(forwardRef(() => ShipmentService))
    private readonly shipmentService: ShipmentService,
    @Inject(forwardRef(() => TransactionalEventEmitterService))
    private readonly transactionalEventEmitterService: TransactionalEventEmitterService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    @Inject(forwardRef(() => DataSource))
    private readonly dataSource: DataSource,
    @Inject(forwardRef(() => CanadaTariffService))
    private readonly canadaTariffService: CanadaTariffService
  ) {}

  private async getToBeUpdatedLines(productId: number, queryRunner?: QueryRunner) {
    const commercialInvoiceLineRepository = queryRunner
      ? queryRunner.manager.getRepository(CommercialInvoiceLine)
      : this.commercialInvoiceLineRepository;

    return await commercialInvoiceLineRepository
      .createQueryBuilder("cil", queryRunner)
      .leftJoin(Product, "p", `p.id = cil."productId"`)
      .leftJoinAndSelect(CommercialInvoice, "ci", `ci.id = cil."commercialInvoiceId"`)
      .leftJoin(Shipment, "s", `s.id = ci."shipmentId"`)
      .where("p.id = :productId", { productId })
      .andWhere(
        `((p."productType" = :regularType AND s."customsStatus" in (:...customsStatuses)) OR (p."productType" = :tempType AND s."customsStatus" IS NOT NULL))`,
        {
          regularType: ProductType.REGULAR,
          tempType: ProductType.TEMPORARY,
          customsStatuses: [
            CustomsStatus.PENDING_COMMERCIAL_INVOICE,
            CustomsStatus.PENDING_CONFIRMATION,
            CustomsStatus.PENDING_ARRIVAL
          ]
        }
      )
      .getMany();
  }

  /**
   * Update all CI line with the same product ID with the product's HS code.
   * - For temporary product, all line will be changed no matter the shipment customs status.
   * - For regular product, only the lines with shipment customs status before `live` will be changed.
   * @param productId - The product ID to sync the HS code with.
   * @param excludeLineIds - The CI Line IDs to exclude from the update.
   * @param queryRunner - The query runner to use.
   */
  async syncLineHsCodeWithProduct(
    productId: number,
    excludeLineIds?: Array<number>,
    queryRunner?: QueryRunner
  ) {
    const commercialInvoiceLineRepository = queryRunner
      ? queryRunner.manager.getRepository(CommercialInvoiceLine)
      : this.commercialInvoiceLineRepository;

    const product = await (queryRunner ? queryRunner.manager : this.dataSource.manager).findOne(Product, {
      where: { id: productId },
      relations: FIND_PRODUCT_RELATIONS
    });
    this.logger.debug(`Product to update lines' HS code: ${product?.id}`);
    if (!product) {
      this.logger.warn(`Product with ID ${productId} not found, skip syncing lines' HS code`);
      return;
    }

    // Get to be updated CI lines
    const toBeUpdatedLines = (await this.getToBeUpdatedLines(productId, queryRunner)).filter(
      (line) => !(excludeLineIds || []).includes(line.id)
    );

    this.logger.log(`Found ${toBeUpdatedLines.length} CI lines to update HS code with product ${productId}`);
    this.logger.debug(`To be updated lines: ${JSON.stringify(toBeUpdatedLines.map((cil) => cil.id))}`);

    if (toBeUpdatedLines.length <= 0) return;

    // Update CI lines' HS code with the product's HS code
    const updateResult = await commercialInvoiceLineRepository.update(
      { id: In(toBeUpdatedLines.map((line) => line.id)) },
      { hsCode: product.hsCode }
    );
    this.logger.log(`Updated ${updateResult.affected} CI lines' HS code with product ${productId}`);

    // Emit event for each updated line
    for (const line of toBeUpdatedLines) {
      await this.updateLineUOMRelatedFields(line, product, queryRunner);

      // Get the shipment related to the commercial invoice
      const shipment = await (queryRunner ? queryRunner.manager : this.dataSource.manager).findOne(Shipment, {
        where: {
          commercialInvoices: {
            id: line.commercialInvoice?.id
          }
        },
        select: ["id"]
      });

      // it seems we are not using dto on any listeners now
      await this.transactionalEventEmitterService.enqueueEvent(
        CommercialInvoiceEvent.COMMERCIAL_INVOICE_LINE_EDITED,
        queryRunner,
        new CommercialInvoiceLineEditedEventDto(
          line.commercialInvoiceId,
          line.id,
          shipment?.id ?? -1,
          this.request?.user,
          {
            hsCode: product.hsCode
          }
        )
      );
    }
  }

  /**
   * Update the UOM and related fields of the CI line.
   *
   * HS Code for testing:
   * 1905102100 - KGM // weight
   * 4203291000 - PAR // per-unit
   * 3918101000 - MTK // area
   * 4403120000 - MTQ // volume
   * 4403210010 - MTR // length
   * 3005900040 - NULL
   *
   * @param line - The CI line to update.
   * @param product - The product to update the UOM and unit price with.
   * @param queryRunner - The query runner to use.
   */
  private async updateLineUOMRelatedFields(
    line: CommercialInvoiceLine,
    product: Product,
    queryRunner?: QueryRunner
  ) {
    const commercialInvoiceLineRepository = queryRunner
      ? queryRunner.manager.getRepository(CommercialInvoiceLine)
      : this.commercialInvoiceLineRepository;

    const commercialInvoiceLine = await commercialInvoiceLineRepository.findOne({
      where: {
        id: line.id
      },
      relations: {
        measurements: true
      },
      select: {
        id: true,
        unitOfMeasure: true,
        totalLineValue: true,
        measurements: true
      }
    });

    const newUOM =
      (await this.canadaTariffService.getCanadaTariffByHsCode(product.hsCode).then((tariff) => tariff.uom)) ??
      UnitOfMeasure.NUMBER;

    const currentUOM = commercialInvoiceLine.unitOfMeasure;

    this.logger.debug(`Current UOM: ${currentUOM}, New UOM: ${newUOM}`);

    this.logger.debug(`Measurements: ${JSON.stringify(commercialInvoiceLine.measurements)}`);

    // do nothing if the uom is not changed
    if (newUOM === currentUOM) return;

    const newQtyInMeasurements = commercialInvoiceLine.measurements.find(
      (measurement) => measurement.unitOfMeasure === newUOM
    )?.value;

    const newUnitPrice = commercialInvoiceLine.totalLineValue / newQtyInMeasurements;

    // if uom is presented in measurements, update the uom and unit price, otherwise only update the uom
    if (newQtyInMeasurements && newQtyInMeasurements > 0) {
      // update the uom and unit price
      await commercialInvoiceLineRepository.update(
        { id: line.id },
        { unitOfMeasure: newUOM, unitPrice: newUnitPrice, quantity: newQtyInMeasurements }
      );
    } else {
      // update the uom, set quantity and unit price to 0
      await commercialInvoiceLineRepository.update(
        { id: line.id },
        { unitOfMeasure: newUOM, quantity: 0, unitPrice: 0 }
      );
    }
  }
}
