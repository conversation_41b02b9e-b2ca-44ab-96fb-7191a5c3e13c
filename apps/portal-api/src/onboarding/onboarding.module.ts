import { forwardRef, <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { OnboardingService } from "./onboarding.service";
import { ImporterModule } from "@/importer/importer.module";
import { EmailModule } from "@/email/email.module";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Country } from "nest-modules";

@Module({
  imports: [
    TypeOrmModule.forFeature([Country]),
    forwardRef(() => ImporterModule),
    forwardRef(() => EmailModule)
  ],
  providers: [OnboardingService],
  exports: [OnboardingService]
})
export class OnboardingModule {}
