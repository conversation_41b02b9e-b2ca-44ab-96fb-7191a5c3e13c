import { FailedBusinessVerification, OnboardingDocument } from "nest-modules";

export type ConvertedTimestampOnboardingDocument = Omit<
  OnboardingDocument,
  "createdAt" | "updatedAt" | "poaSignedAt" | "failedBusinessVerification" | "failedCarmRequest"
> & {
  createdAt: string | null;
  updatedAt: string | null;
  poaSignedAt: string | null;
  failedBusinessVerification?: Array<
    Omit<FailedBusinessVerification, "timestamp"> & {
      timestamp: string | null;
    }
  >;
  failedCarmRequest?: Array<
    Omit<FailedBusinessVerification, "timestamp"> & {
      timestamp: string | null;
    }
  >;
};
