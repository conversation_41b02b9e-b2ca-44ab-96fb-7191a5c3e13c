import { EmailService } from "@/email/services/email.service";
import { EmailTemplateName } from "@/email/types/email.types";
import { ImporterService } from "@/importer/importer.service";
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException
} from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { ContextIdFactory, ModuleRef } from "@nestjs/core";
import { OnEvent } from "@nestjs/event-emitter";
import { InjectDataSource } from "@nestjs/typeorm";
import { validate, ValidatorOptions } from "class-validator";
import {
  CarmService,
  CarmStatus,
  Country,
  CreateImporterDto,
  CreateUserDto,
  FIND_COUNTRY_RELATIONS,
  FirebaseService,
  OnboardingSteps,
  Organization,
  OrganizationService,
  OrganizationType,
  PoaStatus,
  UserPermission,
  UserService,
  PasswordService,
  OrganizationCustomsBroker
} from "nest-modules";
import { DataSource, QueryRunner } from "typeorm";
import { ConvertedTimestampOnboardingDocument } from "./types/firebase.types";
import { OnboardingEvent } from "./types/onboarding.types";

@Injectable()
export class OnboardingService {
  constructor(
    @Inject(ConfigService)
    private readonly configService: ConfigService,
    @Inject(FirebaseService)
    private readonly firebaseService: FirebaseService,
    @Inject(CarmService)
    private readonly carmService: CarmService,
    @Inject(forwardRef(() => EmailService))
    private readonly emailService: EmailService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly moduleRef: ModuleRef
  ) {}
  private readonly logger = new Logger(OnboardingService.name);

  private async sendOnboardingCreateUserErrorEmail(
    onboardingDoc: ConvertedTimestampOnboardingDocument,
    error: any
  ) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const stackTrace = error instanceof Error ? error.stack : undefined;
    const { subject, body } = this.emailService.renderEmailTemplateWithSubject(
      EmailTemplateName.ONBOARDING_CREATE_USER_ERROR_EMAIL,
      {
        onboardingDocumentId: onboardingDoc.id,
        importer: onboardingDoc.importer,
        errorMessage,
        stackTrace
      }
    );
    const errorEmail = await this.emailService.sendEmail({
      from: this.configService.get<string>("SYSTEM_FROM_EMAIL"),
      to: [this.configService.get<string>("BACKOFFICE_EMAIL")],
      subject,
      text: body
    });
    this.logger.log(`Sent onboarding create user error email to backoffice: ${errorEmail.id}`);
  }

  private async getScopedServices(organization?: Organization) {
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(
      {
        user: {
          permission: UserPermission.BACKOFFICE_ADMIN,
          organization
        }
      },
      contextId
    );
    const organizationService = await this.moduleRef.resolve(OrganizationService, contextId, {
      strict: false
    });
    const importerService = await this.moduleRef.resolve(ImporterService, contextId, { strict: false });
    const userService = await this.moduleRef.resolve(UserService, contextId, { strict: false });
    const passwordService = await this.moduleRef.resolve(PasswordService, contextId, { strict: false });
    await new Promise((resolve) => process.nextTick(resolve));
    return { organizationService, importerService, userService, passwordService };
  }

  private async createOrganizationForOnboarding(
    onboardingDoc: ConvertedTimestampOnboardingDocument,
    queryRunner: QueryRunner
  ) {
    const { organizationService } = await this.getScopedServices();
    const { importer } = onboardingDoc;
    let newOrgName = importer.companyName,
      duplicateCount = 0;
    while (await queryRunner.manager.existsBy(Organization, { name: newOrgName })) {
      this.logger.warn(`Organization with name ${newOrgName} already exists, trying another name`);
      newOrgName = `${importer.companyName}-${++duplicateCount}`;
    }
    this.logger.log(`Organization name ${newOrgName} is unique, creating organization`);
    const newOrg = await organizationService.createOrganization(
      {
        name: newOrgName,
        organizationType: OrganizationType.TRADING,
        customsBroker: OrganizationCustomsBroker.CLARO,
        skipPoaCheck: true
      },
      true,
      queryRunner
    );
    this.logger.log(`Organization ${newOrg.name} created successfully`);
    return newOrg;
  }

  @OnEvent(OnboardingEvent.CHECK_CARM_STATUS)
  async checkCarmStatus() {
    try {
      // Get onboarding doc that's poa signed and carm status is pending or sent
      const docs = await this.firebaseService.getOnboarding([
        ["poaStatus", "==", PoaStatus.SIGNED],
        ["carmStatus", "in", [CarmStatus.PENDING, CarmStatus.SENT]]
      ]);
      this.logger.debug(`docs: ${JSON.stringify(docs)}`);

      const requestStatus = await this.carmService.getRequestStatus();
      this.logger.debug(`carmRequest: ${JSON.stringify(requestStatus)}`);

      // Filter onboarding docs that have a matching business number and carm status is approved
      const approved = docs.filter((item) =>
        requestStatus.some(
          (request) =>
            request.business_number === item.importer.businessNumber.slice(0, 9) &&
            request.status === CarmStatus.APPROVED
        )
      );
      this.logger.debug(`approved: ${JSON.stringify(approved)}`);
      // Update onboarding doc with carm status active
      if (approved.length > 0) {
        for (const doc of approved) {
          const queryRunner = this.dataSource.createQueryRunner();
          await queryRunner.connect();
          await queryRunner.startTransaction();

          try {
            // Create Organization
            const newOrganization = await this.createOrganizationForOnboarding(doc, queryRunner);
            this.logger.log(`Organization ${newOrganization.id} created`);

            // Get services scoped to the new organization
            const VALIDATOR_OPTIONS: ValidatorOptions = {
              whitelist: true,
              forbidUnknownValues: true,
              validationError: {
                target: false
              }
            };
            const { importerService, userService, passwordService } =
              await this.getScopedServices(newOrganization);

            // Create importer
            const country = await queryRunner.manager.findOne(Country, {
              where: { name: doc.importer.countryName },
              relations: FIND_COUNTRY_RELATIONS
            });
            if (!country)
              throw new NotFoundException(
                `Country ${doc.importer.countryName} not found and thus, cannot create importer`
              );
            const createImporterDto = new CreateImporterDto();
            Object.assign(createImporterDto, {
              ...doc.importer,
              fax: doc.importer.fax || null,
              countryId: country.id
            });
            const importerDtoValidationErrors = await validate(createImporterDto, VALIDATOR_OPTIONS);
            if (importerDtoValidationErrors.length > 0)
              throw new BadRequestException(
                `Create importer validation error: ${JSON.stringify(importerDtoValidationErrors)}`
              );
            const newImporter = await importerService.createImporter(createImporterDto, queryRunner, true);
            this.logger.log(`Importer ${newImporter.id} created`);

            // Create user
            const createUserDto = new CreateUserDto();
            createUserDto.email = doc.contactEmail;
            createUserDto.name = doc.name;
            createUserDto.organizationId = newOrganization.id;
            createUserDto.permission = UserPermission.ORGANIZATION_ADMIN;
            const userDtoValidationErrors = await validate(createUserDto, VALIDATOR_OPTIONS);
            if (userDtoValidationErrors.length > 0)
              throw new BadRequestException(
                `Create user validation error: ${JSON.stringify(userDtoValidationErrors)}`
              );
            const newUser = await userService.createUser(createUserDto, queryRunner);
            this.logger.log(`User ${newUser.id} created`);

            // Generate create password token
            const { token, resetPasswordTokenRecord } = await passwordService.generateResetPasswordToken(
              newUser.id,
              queryRunner
            );
            this.logger.log(
              `Reset password token ${resetPasswordTokenRecord.id} generated for user ${newUser.id}`
            );

            // Send create user password email
            const { subject, body } = this.emailService.renderEmailTemplateWithSubject(
              EmailTemplateName.RESET_PASSWORD_EMAIL,
              {
                type: "create",
                userName: newUser.name,
                resetPasswordUrl: `${this.configService.get<string>("PORTAL_FRONTEND_URL")}/create-password?token=${token}`
              }
            );
            const newEmail = await this.emailService.sendEmail(
              {
                from: this.configService.get<string>("SYSTEM_FROM_EMAIL"),
                to: [newUser.email],
                replyTo: newImporter.receiveEmail,
                subject,
                html: body
              },
              queryRunner,
              newOrganization
            );
            this.logger.log(`Create user password email ${newEmail.id} sent to ${newUser.email}`);

            await queryRunner.commitTransaction();

            // Update onboarding doc to active CARM status
            await this.firebaseService.updateOnboarding(doc.id, {
              carmStatus: CarmStatus.ACTIVE,
              step: OnboardingSteps.COMPLETED
            });
          } catch (transactionError) {
            if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
            this.logger.error(
              `Error when creating organization, importer and user: ${transactionError instanceof Error ? transactionError.message : String(transactionError)}`
            );
            await this.sendOnboardingCreateUserErrorEmail(doc, transactionError);
          } finally {
            await queryRunner.release();
          }
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(`Error in checkCarmStatus: ${errorMessage}`, errorStack);
      throw new InternalServerErrorException(error);
    }
  }
}
