#!/usr/bin/env node

/**
 * Performance Comparison Tests for ShipmentContextService Refactor
 *
 * This script measures performance characteristics of the refactored ShipmentContextService:
 * 1. Context building performance
 * 2. Memory usage analysis
 * 3. Interface method performance
 * 4. Concurrent context building
 * 5. Error handling performance
 *
 * Usage:
 *   node src/agent-context/testing/test-performance-comparison.js [options]
 *
 * Options:
 *   --shipment-id=N        Shipment ID to test with (default: 860)
 *   --organization-id=N    Organization ID to test with (default: 1)
 *   --iterations=N         Number of iterations for performance tests (default: 50)
 *   --concurrent=N         Number of concurrent operations (default: 5)
 *   --verbose              Show detailed performance metrics
 *   --timeout=SECONDS      Test timeout (default: 300)
 */

const { NestFactory } = require("@nestjs/core");
const { AppModule } = require("../../../dist/app.module");
const { ShipmentContextService } = require("../../../dist/agent-context/services/shipment-context.service");

/**
 * Performance Test Results Collector
 */
class PerformanceResults {
  constructor() {
    this.tests = [];
  }

  addTest(name, duration, iterations, throughput, memoryUsage = null, errorCount = 0) {
    this.tests.push({
      name,
      duration,
      iterations,
      throughput,
      memoryUsage,
      errorCount,
      avgTime: duration / iterations,
      successRate: ((iterations - errorCount) / iterations) * 100
    });
  }

  getSummary() {
    return {
      totalTests: this.tests.length,
      totalDuration: this.tests.reduce((sum, test) => sum + test.duration, 0),
      tests: this.tests
    };
  }

  printResults(verbose = false) {
    console.log("\n📊 ShipmentContextService Performance Results");
    console.log("==============================================");

    for (const test of this.tests) {
      console.log(`\n🔥 ${test.name}`);
      console.log(`   Duration: ${test.duration}ms`);
      console.log(`   Iterations: ${test.iterations}`);
      console.log(`   Throughput: ${test.throughput.toFixed(2)} ops/sec`);
      console.log(`   Avg Time: ${test.avgTime.toFixed(2)}ms per operation`);
      console.log(`   Success Rate: ${test.successRate.toFixed(1)}%`);

      if (test.errorCount > 0) {
        console.log(`   ⚠️ Errors: ${test.errorCount}`);
      }

      if (test.memoryUsage && verbose) {
        console.log(`   Memory: ${(test.memoryUsage.heapUsed / 1024 / 1024).toFixed(2)}MB heap`);
        console.log(`   RSS: ${(test.memoryUsage.rss / 1024 / 1024).toFixed(2)}MB`);
      }
    }

    const summary = this.getSummary();
    console.log(`\n📈 Summary: ${summary.totalTests} tests completed in ${summary.totalDuration}ms`);
  }
}

/**
 * Measure memory usage
 */
function getMemoryUsage() {
  return process.memoryUsage();
}

/**
 * Run a performance test
 */
async function runPerformanceTest(name, testFn, iterations, results, config) {
  console.log(`\n⚡ Running ${name} (${iterations} iterations)...`);

  const startMemory = getMemoryUsage();
  const startTime = Date.now();
  let errorCount = 0;

  for (let i = 0; i < iterations; i++) {
    try {
      await testFn();
    } catch (error) {
      errorCount++;
      if (config.verbose) {
        console.log(`   ❌ Error in iteration ${i + 1}: ${error.message}`);
      }
    }
  }

  const endTime = Date.now();
  const endMemory = getMemoryUsage();

  const duration = endTime - startTime;
  const throughput = (iterations / duration) * 1000; // ops per second

  results.addTest(name, duration, iterations, throughput, endMemory, errorCount);

  if (config.verbose) {
    console.log(`   ✅ Completed in ${duration}ms (${throughput.toFixed(2)} ops/sec)`);
    if (errorCount > 0) {
      console.log(`   ⚠️ ${errorCount} errors occurred`);
    }
  }
}

/**
 * Test context building performance
 */
async function testContextBuildingPerformance(app, config, results) {
  console.log("\n🔧 Testing Context Building Performance...");

  const shipmentContextService = app.get(ShipmentContextService);

  await runPerformanceTest(
    "Context Building",
    async () => {
      const context = await shipmentContextService.buildContext(config.shipmentId, config.organizationId);

      // Verify context has expected properties
      if (!context.shipment || !context.organization) {
        throw new Error("Invalid context structure");
      }
    },
    config.iterations,
    results,
    config
  );
}

/**
 * Test interface method performance
 */
async function testInterfaceMethodPerformance(app, config, results) {
  console.log("\n🔍 Testing Interface Method Performance...");

  const dataProvider = app.get("SHIPMENT_DATA_PROVIDER");
  const ruleEvaluator = app.get("BUSINESS_RULE_EVALUATOR");
  const formatter = app.get("CONTEXT_FORMATTER");

  // Test data provider performance
  await runPerformanceTest(
    "Data Provider - Fetch Shipment",
    async () => {
      await dataProvider.fetchShipmentData(config.shipmentId, config.organizationId);
    },
    Math.floor(config.iterations / 2),
    results,
    config
  );

  // Test rule evaluator performance
  await runPerformanceTest(
    "Rule Evaluator - Business Rules",
    async () => {
      // Get sample data first
      const shipmentData = await dataProvider.fetchShipmentData(config.shipmentId, config.organizationId);

      // Test various rule evaluations
      ruleEvaluator.canShipmentBeRushed(shipmentData.shipment, shipmentData.compliance);
      ruleEvaluator.canGenerateCAD(shipmentData.shipment.customsStatus);
      ruleEvaluator.canGenerateRNSProof(shipmentData.shipment.customsStatus);
      ruleEvaluator.isCompliant(shipmentData.compliance);
    },
    Math.floor(config.iterations / 2),
    results,
    config
  );
}

/**
 * Test concurrent context building performance
 */
async function testConcurrentPerformance(app, config, results) {
  console.log("\n🚀 Testing Concurrent Context Building Performance...");

  const shipmentContextService = app.get(ShipmentContextService);

  const startTime = Date.now();
  let errorCount = 0;

  // Create concurrent operations
  const promises = [];
  for (let i = 0; i < config.concurrent; i++) {
    const promise = (async () => {
      for (let j = 0; j < Math.floor(config.iterations / config.concurrent); j++) {
        try {
          await shipmentContextService.buildContext(config.shipmentId, config.organizationId);
        } catch (error) {
          errorCount++;
          if (config.verbose) {
            console.log(`   ❌ Concurrent error: ${error.message}`);
          }
        }
      }
    })();
    promises.push(promise);
  }

  await Promise.all(promises);

  const duration = Date.now() - startTime;
  const throughput = (config.iterations / duration) * 1000;

  results.addTest("Concurrent Context Building", duration, config.iterations, throughput, null, errorCount);

  if (config.verbose) {
    console.log(`   ✅ ${config.concurrent} concurrent workers completed in ${duration}ms`);
  }
}

/**
 * Test memory usage patterns
 */
async function testMemoryUsagePatterns(app, config, results) {
  console.log("\n💾 Testing Memory Usage Patterns...");

  const shipmentContextService = app.get(ShipmentContextService);
  const memorySnapshots = [];

  // Take initial memory snapshot
  memorySnapshots.push({ point: "initial", memory: getMemoryUsage() });

  // Build contexts and track memory
  for (let i = 0; i < Math.min(config.iterations, 20); i++) {
    await shipmentContextService.buildContext(config.shipmentId, config.organizationId);

    if (i % 5 === 0) {
      memorySnapshots.push({ point: `after_${i + 1}_contexts`, memory: getMemoryUsage() });
    }
  }

  // Force garbage collection if available
  if (global.gc) {
    global.gc();
    memorySnapshots.push({ point: "after_gc", memory: getMemoryUsage() });
  }

  if (config.verbose) {
    console.log("\n   📊 Memory Usage Snapshots:");
    for (const snapshot of memorySnapshots) {
      const heapMB = (snapshot.memory.heapUsed / 1024 / 1024).toFixed(2);
      const rssMB = (snapshot.memory.rss / 1024 / 1024).toFixed(2);
      console.log(`   ${snapshot.point}: ${heapMB}MB heap, ${rssMB}MB RSS`);
    }
  }

  // Calculate memory growth
  const initialHeap = memorySnapshots[0].memory.heapUsed;
  const finalHeap = memorySnapshots[memorySnapshots.length - 1].memory.heapUsed;
  const memoryGrowth = finalHeap - initialHeap;

  console.log(`\n   📈 Memory Growth: ${(memoryGrowth / 1024 / 1024).toFixed(2)}MB`);
}

/**
 * Parse command line arguments
 */
function parseArgs() {
  const args = process.argv.slice(2);
  const config = {
    shipmentId: 860,
    organizationId: 1,
    iterations: 50,
    concurrent: 5,
    verbose: false,
    timeout: 300
  };

  for (const arg of args) {
    if (arg.startsWith("--shipment-id=")) {
      config.shipmentId = parseInt(arg.split("=")[1]);
    } else if (arg.startsWith("--organization-id=")) {
      config.organizationId = parseInt(arg.split("=")[1]);
    } else if (arg.startsWith("--iterations=")) {
      config.iterations = parseInt(arg.split("=")[1]);
    } else if (arg.startsWith("--concurrent=")) {
      config.concurrent = parseInt(arg.split("=")[1]);
    } else if (arg.startsWith("--timeout=")) {
      config.timeout = parseInt(arg.split("=")[1]);
    } else if (arg === "--verbose") {
      config.verbose = true;
    }
  }

  return config;
}

/**
 * Main execution function
 */
async function main() {
  const config = parseArgs();
  const results = new PerformanceResults();
  let app;

  console.log("🚀 ShipmentContextService Performance Comparison");
  console.log("================================================");
  console.log(`📋 Configuration:`);
  console.log(`   Shipment ID: ${config.shipmentId}`);
  console.log(`   Organization ID: ${config.organizationId}`);
  console.log(`   Iterations: ${config.iterations}`);
  console.log(`   Concurrent Workers: ${config.concurrent}`);
  console.log(`   Timeout: ${config.timeout}s`);
  console.log(`   Verbose: ${config.verbose}`);

  const timeout = setTimeout(() => {
    console.error("💥 Test timeout exceeded");
    process.exit(1);
  }, config.timeout * 1000);

  try {
    console.log("\n🚀 Starting NestJS application...");
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: config.verbose ? ["log", "debug", "error", "warn"] : ["error"]
    });
    console.log("✅ Application started successfully");

    // Run performance tests
    await testContextBuildingPerformance(app, config, results);
    await testInterfaceMethodPerformance(app, config, results);
    await testConcurrentPerformance(app, config, results);
    await testMemoryUsagePatterns(app, config, results);

    // Print results
    results.printResults(config.verbose);

    console.log("\n✅ Performance comparison completed successfully!");
  } catch (error) {
    console.error("💥 Failed to run performance tests:", error.message);
    if (config.verbose) {
      console.error(error.stack);
    }
    process.exit(1);
  } finally {
    clearTimeout(timeout);
    if (app) {
      await app.close();
    }
  }
}

// Run the tests
if (require.main === module) {
  main().catch((error) => {
    console.error("💥 Unhandled error:", error);
    process.exit(1);
  });
}

module.exports = { main, PerformanceResults };
