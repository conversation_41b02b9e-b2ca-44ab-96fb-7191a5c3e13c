import { Injectable, Logger } from "@nestjs/common";

/**
 * Utility for safely evaluating operations with fallback values and logging.
 * Prevents context build failures from individual rule evaluation errors.
 */
@Injectable()
export class SafeEvaluationUtil {
  private readonly logger = new Logger(SafeEvaluationUtil.name);

  /**
   * Safely evaluates an operation with fallback and logging.
   *
   * @param operation - The operation to evaluate
   * @param defaultValue - The fallback value if evaluation fails
   * @param context - Context description for logging
   * @returns The result of the operation or the default value if it fails
   */
  evaluate<T>(operation: () => T, defaultValue: T, context: string): T {
    try {
      return operation();
    } catch (error) {
      this.logger.warn(`Business rule evaluation failed for ${context}, using fallback: ${error.message}`);
      return defaultValue;
    }
  }

  /**
   * Safely evaluates an async operation with fallback and logging.
   *
   * @param operation - The async operation to evaluate
   * @param defaultValue - The fallback value if evaluation fails
   * @param context - Context description for logging
   * @returns The result of the operation or the default value if it fails
   */
  async evaluateAsync<T>(operation: () => Promise<T>, defaultValue: T, context: string): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      this.logger.warn(
        `Async business rule evaluation failed for ${context}, using fallback: ${error.message}`
      );
      return defaultValue;
    }
  }
}
