import { ValidateShipmentComplianceResponseDto } from "nest-modules";

/**
 * Simplified formatter for compliance response that converts compliance issues to user-friendly strings.
 * This is a minimal version that focuses on the most common compliance issues.
 */
export function formatComplianceResponseToStrings(
  compliance: ValidateShipmentComplianceResponseDto
): string[] {
  const errors: string[] = [];

  // Missing fields
  if (compliance.missingFields?.length > 0) {
    compliance.missingFields.forEach((field) => {
      errors.push(`Missing required field: ${field}`);
    });
  }

  // Missing commercial invoice
  if (compliance.noCommercialInvoice) {
    errors.push("Commercial invoice required for customs clearance");
  }

  // Non-compliant invoices
  if (compliance.nonCompliantInvoices?.length > 0) {
    compliance.nonCompliantInvoices.forEach((invoice, index) => {
      const invoicePrefix = `Invoice ${index + 1}`;

      // Missing invoice fields
      if (invoice.missingFields?.length > 0) {
        invoice.missingFields.forEach((field) => {
          errors.push(`${invoicePrefix}: Missing ${field}`);
        });
      }

      // Missing ship-to fields
      if (invoice.shipToMissingFields?.length > 0) {
        invoice.shipToMissingFields.forEach((field) => {
          errors.push(`${invoicePrefix}: Missing ship-to ${field}`);
        });
      }

      // Missing vendor fields
      if (invoice.vendorMissingFields?.length > 0) {
        invoice.vendorMissingFields.forEach((field) => {
          errors.push(`${invoicePrefix}: Missing vendor ${field}`);
        });
      }

      // Non-compliant lines
      if (invoice.nonCompliantLines?.length > 0) {
        invoice.nonCompliantLines.forEach((line) => {
          const linePrefix = `${invoicePrefix} Line ${line.lineId}`;

          if (line.isHsCodeInvalid) {
            errors.push(`${linePrefix}: Invalid HS code`);
          }

          if (line.isQuantityInvalid) {
            errors.push(`${linePrefix}: Invalid quantity`);
          }

          if (line.nonCompliantRecords?.length > 0) {
            line.nonCompliantRecords.forEach((record) => {
              errors.push(`${linePrefix}: ${record.reason || "Compliance issue"}`);
            });
          }
        });
      }
    });
  }

  return errors;
}
