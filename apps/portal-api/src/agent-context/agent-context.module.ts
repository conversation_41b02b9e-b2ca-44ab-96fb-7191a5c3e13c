import { Modu<PERSON> } from "@nestjs/common";
import { ShipmentContextService } from "./services/shipment-context.service";
import { ShipmentServicesAdapter } from "./services/shipment-services.adapter";
import { SafeEvaluationUtil } from "./utils/safe-evaluation.util";

/**
 * AgentContextModule provides clean, reusable context building services for agent systems.
 *
 * This module follows KISS principles:
 * - Single responsibility: Context building only
 * - Minimal dependencies: Uses interfaces instead of concrete services
 * - Clean separation: Adapter contains complexity, service orchestrates
 */
@Module({
  providers: [
    // Core service (minimal, focused)
    ShipmentContextService,

    // Utility
    SafeEvaluationUtil,

    // Adapter (contains all business logic)
    ShipmentServicesAdapter,

    // Interface implementations (all point to the adapter)
    {
      provide: "SHIPMENT_DATA_PROVIDER",
      useExisting: ShipmentServicesAdapter
    },
    {
      provide: "BUSINESS_RULE_EVALUATOR",
      useExisting: ShipmentServicesAdapter
    },
    {
      provide: "CONTEXT_FORMATTER",
      useExisting: ShipmentServicesAdapter
    },
    {
      provide: "ENTRY_SUBMISSION_SERVICE",
      useExisting: ShipmentServicesAdapter
    }
  ],
  exports: [
    // Only export the main service - consumers don't need to know about internal structure
    ShipmentContextService,
    // Export adapter for ProcessDocumentHandler dependency injection
    ShipmentServicesAdapter
  ]
})
export class AgentContextModule {}
