// Main module
export { AgentContextModule } from "./agent-context.module";

// Core service
export { ShipmentContextService, ShipmentContextWithServices } from "./services/shipment-context.service";

// Types
export { ShipmentContext } from "./types/shipment-context.types";

// Interfaces (for testing and advanced usage)
export {
  IShipmentDataProvider,
  IBusinessRuleEvaluator,
  IContextFormatter,
  IEmailService,
  IRNSStatusChangeEmailSender,
  IRnsProofService,
  IImporterService,
  ICustomsStatusListener,
  IEntrySubmissionService,
  ICommercialInvoiceService
} from "./interfaces/shipment-context-services.interface";

// Utilities
export { SafeEvaluationUtil } from "./utils/safe-evaluation.util";
