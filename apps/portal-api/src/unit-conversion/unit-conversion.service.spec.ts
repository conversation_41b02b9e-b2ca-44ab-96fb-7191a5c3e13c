import { UnitConversionService } from "./unit-conversion.service";

describe("UnitConversionService", () => {
  let service: UnitConversionService;

  beforeEach(() => {
    service = new UnitConversionService();
  });

  it("should convert values within the same system correctly", () => {
    // Example: Convert 1000 grams to kilograms within the metric system
    const value = 1000;
    const from = "g";
    const to = "kg";
    const expected = 1; // 1000g = 1kg
    const result = service.convert(value, from, to);
    expect(result).toBe(expected);
  });

  it("should convert values between different systems correctly", () => {
    // Example: Convert 1 kilogram to pounds
    const value = 1;
    const from = "kg";
    const to = "lb";
    const expected = 2.20462; // 1 kg = 2.20462 lb
    const result = service.convert(value, from, to);
    expect(result).toBeCloseTo(expected, 5);
  });

  it("should throw an error for unsupported units", () => {
    expect(() => service.convert(100, "unknown", "kg")).toThrow("Unsupported unit provided.");
  });

  it("should throw an error when converting between different unit types", () => {
    expect(() => service.convert(100, "kg", "m")).toThrow("Cannot convert between different unit types.");
  });

  it("should handle zero value conversion", () => {
    const value = 0;
    const from = "kg";
    const to = "lb";
    const expected = 0;
    const result = service.convert(value, from, to);
    expect(result).toBe(expected);
  });

  it("should handle negative values correctly", () => {
    const value = -5;
    const from = "kg";
    const to = "lb";
    const expected = -11.0231; // -5 kg = -11.0231 lb
    const result = service.convert(value, from, to);
    expect(result).toBeCloseTo(expected, 4);
  });
});
