import { Injectable } from "@nestjs/common";
import { definitions, Unit } from "./definitions";
// Import other unit types as needed

@Injectable()
export class UnitConversionService {
  // Initialize the unit cache with unit type
  private unitCache: Map<string, { unit: Unit; type: string }> = new Map();

  constructor() {
    this.initializeCache();
  }

  /**
   * Get the type of a unit
   *
   * @param unitName - The name of the unit
   * @returns The type of the unit or null if the unit is not found
   */
  public getUnitType(unitName: string): string | null {
    return this.unitCache.get(this.sanitizeUnitName(unitName))?.type || null;
  }

  private sanitizeUnitName(unitName: string): string {
    return unitName.toLowerCase().replaceAll(/[^a-z0-9]/g, "");
  }

  // Method to populate the cache with units, their aliases, and types
  private initializeCache() {
    for (const definition of definitions) {
      const type = definition.type; // Get the type from definition
      for (const systemKey in definition.systems) {
        const system: Record<string, Unit> = definition.systems[systemKey];

        for (const unit of Object.values(system)) {
          this.unitCache.set(unit.name, { unit, type });
          unit.alias.forEach((alias) => this.unitCache.set(alias, { unit, type }));
        }
      }
    }
  }

  public isSameUnit(from: string, to: string): boolean {
    const fromUnit = this.findUnit(from);
    const toUnit = this.findUnit(to);
    return fromUnit.unit.name === toUnit.unit.name;
  }

  public isSameType(from: string, to: string): boolean {
    const fromUnit = this.findUnit(from);
    const toUnit = this.findUnit(to);
    return fromUnit.type === toUnit.type;
  }

  public convert(value: number, from: string, to: string): number {
    if (typeof value !== "number") {
      throw new Error(`Value is not a number: ${value}`);
    }

    const fromEntry = this.findUnit(from);
    const toEntry = this.findUnit(to);

    if (fromEntry.type !== toEntry.type) {
      throw new Error(`Cannot convert ${from} (${fromEntry.type}) to ${to} (${toEntry.type})`);
    }

    const fromUnit = fromEntry.unit;
    const toUnit = toEntry.unit;

    if (!fromUnit || !toUnit) {
      throw new Error("Unsupported unit provided.");
    }

    if (fromUnit.name === toUnit.name) {
      return value;
    }

    let result: number;

    result = (value * fromUnit.anchor) / toUnit.anchor;

    if (fromUnit.system !== toUnit.system) {
      const conversionFactor = this.getConversionFactor(fromUnit.system, toUnit.system, fromEntry.type);

      result = result * conversionFactor;
    }

    return result;
  }

  public getUnit(unitName: string): Unit | null {
    return this.unitCache.get(this.sanitizeUnitName(unitName))?.unit || null;
  }

  private findUnit(unitName: string): { unit: Unit; type: string } | null {
    const unit = this.unitCache.get(this.sanitizeUnitName(unitName)) || null;

    if (!unit) {
      throw new Error(`Unsupported unit provided: ${unitName}`);
    }
    return unit;
  }

  private getConversionFactor(fromSystem: string, toSystem: string, type: string): number {
    console.log(fromSystem, toSystem);
    const measure = definitions.find((d) => d.systems[fromSystem] && d.type === type);
    if (!measure) {
      throw new Error("Unsupported system provided.");
    }
    return measure.anchors[fromSystem as any][toSystem as any];
  }
}
