import { Measure, Unit } from "./types";

export type VolumeSystems = "metric" | "imperial";
export type VolumeUnits = VolumeMetricUnits | VolumeImperialUnits;

export type VolumeMetricUnits = "m3" | "l" | "1000_m3" | "hl";
export type VolumeImperialUnits = "ft3" | "fl-oz";

const metric: Record<VolumeMetricUnits, Unit> = {
  l: {
    name: "l",
    alias: ["l", "litre", "litres", "lit", "ltr", "lpa"],
    system: "metric",
    anchor: 1
  },
  hl: {
    name: "hl",
    alias: ["hl", "hectolitre", "hectolitres", "hlt"],
    system: "metric",
    anchor: 100
  },
  m3: {
    name: "m3",
    alias: ["m3", "cubicmeter", "cubicmeters", "cbm", "mtq"],
    system: "metric",
    anchor: 1000
  },
  "1000_m3": {
    name: "1000_m3",
    alias: ["1k_m3", "tmq"],
    system: "metric",
    anchor: 1000 * 1000
  }
};

const imperial: Record<VolumeImperialUnits, Unit> = {
  "fl-oz": {
    name: "fl-oz",
    alias: ["fl-oz", "fluidounce", "fluidounces", "floz"],
    system: "imperial",
    anchor: 1
  },
  ft3: {
    name: "ft3",
    alias: ["ft3", "cubicfoot", "cubicfeet", "cft"],
    system: "imperial",
    anchor: 957.506
  }
};

const measure: Measure<VolumeSystems, VolumeUnits> = {
  type: "volume",
  systems: {
    metric,
    imperial
  },
  anchors: {
    metric: {
      imperial: 33.8140226
    },
    imperial: {
      metric: 1 / 33.8140226
    }
  }
};

export default measure;
