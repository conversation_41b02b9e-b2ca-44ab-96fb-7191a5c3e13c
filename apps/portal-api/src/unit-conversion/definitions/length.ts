import { Measure, Unit } from "./types";

export type LengthSystems = "metric" | "imperial";
export type LengthUnits = LengthMetricUnits | LengthImperialUnits;

export enum StandardLengthUOM {
  /** Millimeter */
  MM = "mm",
  /** Centimeter */
  CM = "cm",
  /** Meter */
  MR = "mr",
  /** Kilometer */
  KM = "dk",
  /** Inch */
  IN = "in",
  /** Foot */
  FT = "ft",
  /** Yard */
  YD = "yd",
  /** Mile */
  MI = "dh"
}

export type LengthMetricUnits =
  | StandardLengthUOM.MM
  | StandardLengthUOM.CM
  | StandardLengthUOM.MR
  | StandardLengthUOM.KM;
export type LengthImperialUnits =
  | StandardLengthUOM.IN
  | StandardLengthUOM.FT
  | StandardLengthUOM.YD
  | StandardLengthUOM.MI;

const metric: Record<LengthMetricUnits, Unit> = {
  [StandardLengthUOM.MM]: {
    name: StandardLengthUOM.MM,
    alias: ["mm", "millimeter", "millimeters"],
    system: "metric",
    anchor: 0.001
  },
  [StandardLengthUOM.CM]: {
    name: StandardLengthUOM.CM,
    alias: ["cm", "centimeter", "centimeters"],
    system: "metric",
    anchor: 0.01
  },
  [StandardLengthUOM.MR]: {
    name: StandardLengthUOM.MR,
    alias: ["m", "meter", "meters", "mtr"],
    system: "metric",
    anchor: 1
  },
  [StandardLengthUOM.KM]: {
    name: StandardLengthUOM.KM,
    alias: ["km", "kilometer", "kilometers"],
    system: "metric",
    anchor: 1000
  }
};

const imperial: Record<LengthImperialUnits, Unit> = {
  [StandardLengthUOM.IN]: {
    name: StandardLengthUOM.IN,
    alias: ["in", "inch", "inches"],
    system: "imperial",
    anchor: 1 / 12
  },
  [StandardLengthUOM.FT]: {
    name: StandardLengthUOM.FT,
    alias: ["ft", "foot", "feet"],
    system: "imperial",
    anchor: 1
  },
  [StandardLengthUOM.YD]: {
    name: StandardLengthUOM.YD,
    alias: ["yd", "yard", "yards"],
    system: "imperial",
    anchor: 3
  },
  [StandardLengthUOM.MI]: {
    name: StandardLengthUOM.MI,
    alias: ["mi", "mile", "miles"],
    system: "imperial",
    anchor: 5280
  }
};

const measure: Measure<LengthSystems, LengthUnits> = {
  type: "length",
  systems: {
    metric,
    imperial
  },
  anchors: {
    metric: {
      imperial: 3.28084 // meters to feet
    },
    imperial: {
      metric: 1 / 3.28084 // feet to meters
    }
  }
};

export default measure;
