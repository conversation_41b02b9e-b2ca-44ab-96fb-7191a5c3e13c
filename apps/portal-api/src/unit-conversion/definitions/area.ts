import { Measure, Unit } from "./types";

export type AreaSystems = "metric" | "imperial";
export type AreaUnits = AreaMetricUnits | AreaImperialUnits;

export enum StandardAreaUOM {
  /** Square Meter */
  SM = "sm",
  /** Square Foot */
  SF = "sf"
}

export type AreaMetricUnits = StandardAreaUOM.SM;
export type AreaImperialUnits = StandardAreaUOM.SF;

const metric: Record<AreaMetricUnits, Unit> = {
  [StandardAreaUOM.SM]: {
    name: StandardAreaUOM.SM,
    alias: ["m2", "sqm", "mtk", "mtq"],
    system: "metric",
    anchor: 1
  }
};

const imperial: Record<AreaImperialUnits, Unit> = {
  [StandardAreaUOM.SF]: {
    name: StandardAreaUOM.SF,
    alias: ["ft2", "sqft", "sqf"],
    system: "imperial",
    anchor: 1
  }
};

const measure: Measure<AreaSystems, AreaUnits> = {
  type: "area",
  systems: {
    metric,
    imperial
  },
  anchors: {
    metric: {
      imperial: 10.7639
    },
    imperial: {
      metric: 1 / 10.7639
    }
  }
};

export default measure;
