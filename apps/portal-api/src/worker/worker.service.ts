import { Injectable, Logger } from "@nestjs/common";

@Injectable()
export class WorkerService {
  private readonly logger = new Logger(WorkerService.name);

  async runWorker(): Promise<void> {
    this.logger.log("Worker job started...");
    // Your heavy logic
    console.log("simulating a long running task");
    console.log("task running....");
    console.log("task still running....");
    console.log("task still running....");
    console.log("task still running....");
    setTimeout(() => {
      console.log("task still running....");
      console.log("task still running....");
    }, 10000);

    setTimeout(() => {
      console.log("task finished....");
    }, 1000);

    await new Promise((resolve) => setTimeout(resolve, 5000)); // mock async work
    this.logger.log("Worker job completed!");
  }
}
