import { <PERSON>ronJobName } from "@/cron/types/cron.types";
import { DescribeTasksCommand, ECSClient, RunTaskCommand } from "@aws-sdk/client-ecs";
import { SSMClient, GetParameterCommand } from "@aws-sdk/client-ssm";
import { InternalServerErrorException, Logger, NotFoundException } from "@nestjs/common";
import { NodeEnv } from "nest-modules";

const ssm = new SSMClient({ region: process.env.AWS_REGION });
const ecsClient = new ECSClient({ region: process.env.AWS_REGION });
const env = process.env.ENVIRONMENT ?? "staging";

async function getParam(name: string) {
  const command = new GetParameterCommand({ Name: name });
  const result = await ssm.send(command);
  if (!result.Parameter?.Value) {
    throw new NotFoundException(`Missing SSM parameter: ${name}`);
  }
  return result.Parameter.Value;
}

export async function runWorkerTask(feature: CronJobName) {
  if (process.env.NODE_ENV !== NodeEnv.PRODUCTION) {
    Logger.warn(`Skipping worker task for feature: ${feature} in development`, "runWorkerTask");
    return;
  }

  Logger.log(`Running worker task for feature: ${feature}`, "runWorkerTask");

  // const cluster = await getParam("/ecs/cluster-name");
  // const taskDefinition = await getParam("/ecs/portal-service-task-def");

  const cluster = await getParam(env === "staging" ? "/ecs/cluster-name" : `/ecs/cluster-name-${env}`);
  const taskDefinition = await getParam(
    env === "staging" ? "/ecs/portal-service-task-def" : `/ecs/portal-service-task-def-${env}`
  );
  // const subnets = await getParam(env === "staging" ? "/ecs/subnets" : `/ecs/subnets-${env}`);
  // const securityGroups = await getParam(
  //   env === "staging" ? "/ecs/security-groups" : `/ecs/security-groups-${env}`
  // );

  const command = new RunTaskCommand({
    cluster,
    taskDefinition,
    launchType: "EC2",
    // networkConfiguration: {
    //   awsvpcConfiguration: {
    //     subnets: [subnets],
    //     securityGroups: [securityGroups],
    //     assignPublicIp: "DISABLED" // Use DISABLED for private subnets with NAT
    //   }
    // },
    overrides: {
      containerOverrides: [
        {
          name: "PortalContainer",
          command: ["node", "apps/portal-api/dist/worker-main.js"],
          environment: [
            // TODO add more flags here
            {
              name: "FEATURE",
              value: feature
            }
          ]
        }
      ]
    }
  });

  try {
    const response = await ecsClient.send(command);

    Logger.log(`Started worker task: ${response.tasks?.[0]?.taskArn}`, "runWorkerTask");
  } catch (error) {
    throw new InternalServerErrorException("Error running task:" + error);
  }
}

// Example to get task state by taskArn
async function getTaskState(taskArn: string) {
  const cluster = await getParam("/ecs/cluster-name");

  const command = new DescribeTasksCommand({
    cluster,
    tasks: [taskArn]
  });

  try {
    const response = await ecsClient.send(command);
    const task = response.tasks?.[0];

    if (!task) {
      throw new NotFoundException("Task not found");
    }

    return {
      taskArn: task.taskArn,
      status: task.lastStatus,
      desiredStatus: task.desiredStatus,
      containers: task.containers?.map((container) => ({
        name: container.name,
        status: container.lastStatus,
        reason: container.reason
      }))
    };
  } catch (error) {
    throw new InternalServerErrorException("Error describing task: " + error);
  }
}
