import { Inject, Injectable, Logger } from "@nestjs/common";
import OpenA<PERSON> from "openai";
import { LlmLogService } from "./llm-log.service";
import { LlmModuleOptions } from "./llm.module";

@Injectable()
export class DeepSeekService {
  private readonly logger = new Logger(DeepSeekService.name);
  private client: OpenAI;

  constructor(
    @Inject("LLM_MODULE_OPTIONS")
    private readonly options: LlmModuleOptions,
    @Inject(LlmLogService)
    private readonly llmLogService: LlmLogService
  ) {
    if (!options.deepseekApiKey) {
      throw new Error("DeepSeek API key is not set");
    }

    this.client = new OpenAI({
      baseURL: "https://api.deepseek.com",
      apiKey: options.deepseekApiKey
    });
  }

  getClient(): OpenAI {
    return this.client;
  }

  /**
   * Create a chat completion
   *
   * @param params
   * @returns OpenAI.Chat.Completions.ChatCompletion
   */
  async createChatCompletion(
    params: OpenAI.Chat.Completions.ChatCompletionCreateParamsNonStreaming,
    caller?: string
  ) {
    const llmLog = await this.llmLogService.createLlmLog({
      request: JSON.stringify(params),
      caller,
      model: params?.model || undefined
    });

    try {
      const response = await this.client.chat.completions.create(params);
      await this.llmLogService.updateLlmLog(llmLog?.id, JSON.stringify(response));
      return response;
    } catch (error) {
      await this.llmLogService.updateLlmLog(llmLog?.id, error.message);
      throw error;
    }
  }
}
