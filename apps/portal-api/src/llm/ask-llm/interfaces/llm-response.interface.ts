import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>lm<PERSON>ool<PERSON>all, Llm<PERSON><PERSON>ge } from "../types/ask-llm.types";

/**
 * A unified response from an LLM interaction
 * This represents the standardized response format across all providers
 */
export interface LlmResponse {
  // Identifiers from the provider
  id?: string;
  model?: string;
  created?: number;

  // The response message from the LLM
  message: LlmMessage;

  // Usage information for tracking token consumption
  usage?: LlmUsage;

  // Convenience access to tool calls if present
  toolCalls?: LlmToolCall[];

  // Reason the LLM stopped generating
  finishReason?: "stop" | "length" | "content_filter" | "tool_calls" | string;

  // For structured outputs - parsed result
  parsed?: any;

  // Original provider-specific response
  raw: Record<string, any>;

  // The full conversation history (messages sent and received) if requested
  /**
   * The full conversation history (all messages sent and received in this turn).
   * Only present if the conversation flag is set to true in the ask() call.
   * Useful for continuing the conversation in the next turn.
   */
  conversation?: LlmMessage[];
}
