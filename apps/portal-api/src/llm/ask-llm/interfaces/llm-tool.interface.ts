import { ZodSchema } from "zod";

/**
 * Definition for an LLM tool, to be passed to agent() or ask().
 *
 * @template T - The validated argument type for the tool function.
 */
export interface LlmToolDefinition<T extends object> {
  /**
   * The async function to execute for this tool.
   */
  run: (args: T) => Promise<object>;
  /**
   * The Zod schema for validating tool arguments.
   */
  schema: ZodSchema<T>;
  /**
   * Optional description for the tool.
   */
  description?: string;
  /**
   * Optional name for the tool (if not provided, logic might need to infer or require it).
   * It's generally recommended to provide a name.
   */
  name: string; // Making name required for clarity
  strict?: boolean; // Add this line to support strict tool parsing
}
