export interface LlmProviderStream<T = any> {
  /**
   * Fired whenever the provider emits a new chunk.
   * @param chunk The chunk received from the provider.
   */
  on(event: "data", listener: (chunk: T) => void): this;
  /**
   * Fired when the stream ends.
   */
  on(event: "end", listener: () => void): this;
  /**
   * Fired when an error occurs in the stream.
   */
  on(event: "error", listener: (err: Error) => void): this;
}
