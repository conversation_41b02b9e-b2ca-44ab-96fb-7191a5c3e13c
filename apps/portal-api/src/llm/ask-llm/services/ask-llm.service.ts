import { Injectable, Logger } from "@nestjs/common";
import * as fs from "fs";
import { TemplateManagerService } from "nest-modules";
import * as path from "path";
import { LlmResponse } from "../interfaces/llm-response.interface";
import { LlmProviderFactory } from "../providers/llm-provider.factory";
import { AskLLMParams, ContentPart, LlmMessage, AgentLLMParams } from "../types/ask-llm.types";
import * as z from "zod";
import { executeToolCalls } from "../utils/zod-tool-builder";

@Injectable()
export class AskLLMService {
  protected readonly logger = new Logger(this.constructor.name);

  constructor(
    private readonly llmProviderFactory: LlmProviderFactory,
    private readonly templateManager: TemplateManagerService
  ) {}

  /**
   * A versatile method to interact with LLM models with support for:
   * - Direct prompts
   * - Message arrays
   * - Template-based prompts
   * - Image inputs
   * - Zod schema validation
   * - Tool calling
   * - Streaming responses
   *
   * @param params Configuration for the LLM request
   * @param params.model - The model to use (e.g., "gpt-4o")
   * @param params.prompt - A direct string prompt with optional Nunjucks variables and role markers
   * @param params.promptTemplate - Name of a template file from the prompt-templates directory
   * @param params.variables - Variables to substitute in the prompt or template
   * @param params.system - Optional system message to override or add to the conversation
   * @param params.messages - Optional existing messages to continue a conversation
   * @param params.images - Optional array of image file paths to include in the last user message
   * @param params.zodSchema - Optional Zod schema for structured output validation
   * @param params.schemaName - Name for the Zod schema (required if zodSchema is provided)
   * @param params.debug - Whether to log debug information about the request
   * @param params.stream - Whether to stream the completion
   * @param params.conversation - Whether to include conversation history in the response
   * @returns Complete LLM response object or a stream for streaming responses
   */
  async ask(params: AskLLMParams): Promise<LlmResponse | any> {
    // Extract parameters we need to process ourselves
    const {
      model,
      prompt,
      promptTemplate,
      variables,
      system,
      images,
      zodSchema,
      schemaName,
      messages: inputMessages,
      debug = false,
      stream = false,
      conversation = false,
      // Parameters below are directly passed to LLM
      ...passthroughParams
    } = params;

    if (!model) {
      throw new Error("AskLLMService.ask: 'model' parameter is required.");
    }

    // Get the appropriate provider based on the model
    const llmProvider = this.llmProviderFactory.getProviderForModel(model);

    // Determine if we need to create a structured output
    const isZodSchema = zodSchema && schemaName;

    // Initialize messages array
    const processedMessages = this.buildMessages(
      system,
      prompt,
      images,
      variables,
      inputMessages,
      promptTemplate
    );

    // Debug logging
    if (debug) {
      this.logger.debug(
        `Processing LLM request for model: ${model}, Stream: ${stream}, ZodSchema: ${!!zodSchema}`
      );
      this.logger.debug(`Messages count: ${processedMessages.length}`);
    }

    // Handle streaming requests
    if (stream) {
      this.logger.debug("Using streaming completion");
      // NOTE: Conversation tracking is not supported for streaming responses.

      // Check if processedMessages is empty (this would cause errors)
      if (!processedMessages || processedMessages.length === 0) {
        this.logger.error("ERROR: Empty messages array for streaming request");
        throw new Error("Messages array is required and cannot be empty for streaming completion");
      }

      this.logger.debug(
        `Messages for streaming: ${JSON.stringify(
          processedMessages.map((m) => ({
            role: m.role,
            content: typeof m.content === "string" ? m.content.substring(0, 50) + "..." : m.content
          }))
        )}`
      );

      if (isZodSchema) {
        return await llmProvider.createStructuredCompletionStream({
          ...passthroughParams,
          model,
          messages: processedMessages,
          zodSchema,
          schemaName,
          stream: true
        });
      } else {
        this.logger.debug("Calling provider.createCompletionStream");
        const stream = await llmProvider.createCompletionStream({
          ...passthroughParams,
          model,
          messages: processedMessages,
          stream: true
        });
        this.logger.debug(`Stream received: ${!!stream}`);
        return stream;
      }
    }

    // Handle non-streaming requests
    if (isZodSchema) {
      try {
        // Log schema details before sending - simplified logging
        this.logger.debug(`Using structured output with schema: ${schemaName}`);

        if (zodSchema) {
          // Only log schema type at debug level
          const schemaType = zodSchema.constructor.name;
          this.logger.debug(`Constructor name: ${zodSchema.constructor.name}, Schema type: ${schemaType}`);
        }

        // Pass the Zod schema to the provider
        const result = await llmProvider.createStructuredCompletion({
          ...passthroughParams,
          model,
          messages: processedMessages,
          zodSchema,
          schemaName
        });

        // Log the response more concisely
        if (result) {
          // Log minimal info at debug level
          if (debug) {
            this.logger.debug(`Response received: parsed=${!!result.parsed}, raw=${!!result.raw}`);

            // For parsed results, log type and small preview
            if (result.parsed) {
              try {
                const parsedType = typeof result.parsed;
                const parsedStr = JSON.stringify(result.parsed);
                const preview = parsedStr.length > 100 ? parsedStr.substring(0, 100) + "..." : parsedStr;

                this.logger.debug(`Parsed result (${parsedType}): ${preview}`);
              } catch (error) {
                this.logger.debug(`Could not stringify parsed result: ${error.message}`);
              }
            }
          }

          // Handle content safely whether it's a string or ContentPart[]
          const contentPreview =
            typeof result.message?.content === "string"
              ? result.message.content.substring(0, 200)
              : Array.isArray(result.message?.content)
                ? JSON.stringify(result.message.content).substring(0, 200)
                : "No content available";

          this.logger.debug(`- Message content preview: ${contentPreview}...`);

          if (result.parsed) {
            try {
              this.logger.debug(`Parsed result type: ${typeof result.parsed}`);
              // Only log a brief preview of the parsed result to avoid overwhelming logs
              const parsedStr = JSON.stringify(result.parsed);
              this.logger.debug(
                `Parsed result preview: ${parsedStr.substring(0, 200)}${parsedStr.length > 200 ? "..." : ""}`
              );
            } catch (error) {
              this.logger.warn(`Could not stringify parsed result: ${error.message}`);
            }
          } else {
            this.logger.warn("No parsed data available in the response");
          }

          // If we're having trouble with the response, try to parse it manually
          const parsedResult = result.parsed;

          // Try to extract JSON if not already parsed
          if (!parsedResult && result.message?.content) {
            let parsed: any = undefined;
            try {
              parsed = JSON.parse(result.message.content.toString());
              this.logger.debug("Successfully parsed JSON manually from content");
            } catch (e) {
              this.logger.warn(`Failed to parse JSON from content: ${e.message}`);

              // Handle content safely to avoid type errors
              const contentPreview =
                typeof result.message.content === "string"
                  ? result.message.content.substring(0, 200)
                  : Array.isArray(result.message.content)
                    ? JSON.stringify(result.message.content).substring(0, 200)
                    : "No content available";

              this.logger.debug(`Failed parsing. Raw content preview: ${contentPreview}...`);
            }

            // Use the manually parsed value if available
            if (parsed !== undefined) {
              this.logger.debug("Using manually parsed JSON value");
              return { parsed, raw: result.raw };
            }
          }

          // Attach conversation if requested
          if (conversation && result && result.message) {
            // The conversation is all sent messages plus the received message
            return {
              ...result,
              conversation: [...processedMessages, result.message]
            };
          }
        } else {
          this.logger.warn("Empty result received from provider");
        }

        return result;
      } catch (error) {
        if (error instanceof z.ZodError) {
          // Provide a user-friendly error message with schema requirements
          throw new Error(
            `Response did not match the expected schema. Please check your schema and ensure it follows these rules:\n` +
              `- No custom .refine() or .transform()\n` +
              `- No min/max/email/regex validations\n` +
              `- Only use plain objects, arrays, strings, numbers, booleans, and enums\n` +
              `Zod error: ${error.issues.map((i) => `${i.path.join(".")}: ${i.message}`).join("; ")}`
          );
        }
        this.logger.error(`Structured output error: ${error.message}`);
        this.logger.error(`Error type: ${error.constructor.name}`);
        if (error.stack) {
          this.logger.debug(`Error stack: ${error.stack.split("\n").slice(0, 3).join("\n")}`);
        }
        throw error;
      }
    } else {
      // Call provider for standard completion
      const result = await llmProvider.createCompletion({
        ...passthroughParams,
        model,
        messages: processedMessages
      });

      // Attach conversation if requested
      if (conversation && result && result.message) {
        return {
          ...result,
          conversation: [...processedMessages, result.message]
        };
      }
      return result;
    }
  }

  /**
   * Prepare messages based on the input params type
   *
   * Template handling works in three different ways:
   *
   * 1. promptTemplate: Loads a template file from the prompt-templates directory.
   *    - Uses templateManager.renderTemplate(templateName, variables) to load and render the file
   *    - Then splits the result by role markers (# system:, # user:, etc.)
   *
   * 2. prompt with role markers: Processes a direct string containing role markers.
   *    - First splits the string by role markers
   *    - Then applies variable substitution to each message part separately
   *    - This preserves backward compatibility with existing code
   *
   * 3. prompt without role markers: Treats the string as a simple user message.
   *    - Applies variable substitution to the entire string
   *    - Adds it as a user message
   *
   * @param system Optional system message
   * @param prompt Optional direct prompt string with or without role markers
   * @param images Optional array of image paths to add to the last user message
   * @param variables Optional variables to substitute in templates
   * @param existingMessages Optional array of existing messages for conversation continuity
   * @param promptTemplate Optional name of a template file to load from the prompt-templates directory
   * @returns An array of processed LLM messages ready for the completion request
   */
  private buildMessages(
    system?: string,
    prompt?: string,
    images?: string[],
    variables?: Record<string, any>,
    existingMessages?: LlmMessage[],
    promptTemplate?: string
  ): LlmMessage[] {
    if (existingMessages) {
      this.logger.debug(
        `Existing messages (${existingMessages.length}): ${JSON.stringify(
          existingMessages.map((m) => ({
            role: m.role,
            content: typeof m.content === "string" ? m.content.substring(0, 50) + "..." : m.content
          }))
        )}`
      );
    }

    // If this is an ongoing conversation, do not add prompt or promptTemplate again
    if (existingMessages && existingMessages.length > 0) {
      // Only add system message and images if provided, but do not re-add user/assistant messages
      let resultMessages = [...existingMessages];
      // Handle system message if provided directly
      if (system) {
        let processedSystem = system;
        if (variables && Object.keys(variables).length > 0) {
          try {
            processedSystem = this.templateManager.renderString(system, variables);
          } catch (error) {
            this.logger.error("Template rendering error for system message:", error);
          }
        }
        // Remove any existing system messages
        resultMessages = resultMessages.filter((msg) => msg.role !== "system");
        // Add the new system message at the beginning
        resultMessages.unshift({
          role: "system",
          content: processedSystem
        });
      }
      // Add images to the latest user message if provided
      if (images && images.length > 0) {
        resultMessages = this.addImagesToMessages(resultMessages, images);
      }
      return resultMessages;
    }

    // Initialize with existing messages if provided
    let resultMessages: LlmMessage[] = existingMessages || [];
    const isOngoingConversation = existingMessages && existingMessages.length > 0;

    // Handle template-based prompts if promptTemplate is provided
    if (promptTemplate) {
      try {
        // First render the template from file with variables
        const renderedTemplate = this.templateManager.renderTemplate(promptTemplate, variables || {});

        // Then split the rendered template by role markers
        let promptMessages = this.splitTemplateByRoles(renderedTemplate);

        // In an ongoing conversation, filter out system messages
        if (isOngoingConversation) {
          promptMessages = promptMessages.filter((msg) => msg.role !== "system");
        }

        // Add the processed messages
        resultMessages = [...resultMessages, ...promptMessages];
      } catch (error) {
        this.logger.error(`Failed to render template ${promptTemplate}:`, error);
        throw new Error(`Template rendering error: ${error.message}`);
      }
    }
    // Handle direct string prompt if provided
    else if (prompt) {
      // First check if the prompt has role markers
      if (prompt.match(/^# (system|user|assistant|developer|tool):/m)) {
        // Approach 1: Original approach (backward compatibility)
        let promptMessages = this.splitTemplateByRoles(prompt);

        // In an ongoing conversation, filter out system messages from the prompt
        if (isOngoingConversation) {
          promptMessages = promptMessages.filter((msg) => msg.role !== "system");
        }

        // Apply variable substitution to each message content
        if (variables && Object.keys(variables).length > 0) {
          for (const message of promptMessages) {
            if (typeof message.content === "string") {
              try {
                message.content = this.templateManager.renderString(message.content, variables);
              } catch (error) {
                this.logger.error("Template rendering error for role content:", error);
              }
            }
          }
        }

        // Add the processed messages
        resultMessages = [...resultMessages, ...promptMessages];
      } else {
        // If no role markers, treat as a simple user message with variable substitution
        let processedPrompt = prompt;
        if (variables && Object.keys(variables).length > 0) {
          try {
            this.logger.debug(
              `[AskLLMService.buildMessages] Rendering direct prompt. Variables keys: ${Object.keys(variables).join(", ")}`
            );
            const promptBeforeRender = prompt; // Store original for comparison

            processedPrompt = this.templateManager.renderString(prompt, variables);

            if (processedPrompt !== promptBeforeRender) {
              this.logger.debug(`[AskLLMService.buildMessages] Direct prompt RENDERED successfully.`);
            } else {
              this.logger.warn(
                `[AskLLMService.buildMessages] Direct prompt rendering did NOT change the string. Template Service issue?`
              );
            }
          } catch (error) {
            this.logger.error(
              "[AskLLMService.buildMessages] Template rendering error for direct prompt:",
              error
            );
            // Fall back to original prompt
            processedPrompt = prompt; // Ensure fallback happens
          }
        }

        // Add as a user message
        resultMessages.push({
          role: "user",
          content: processedPrompt
        });
      }
    }

    // If no messages were generated, throw an error
    if (resultMessages.length === 0) {
      throw new Error(
        "No messages were provided. Please provide either messages, system+prompt, or a template with role markers."
      );
    }

    // Handle system message if provided directly
    if (system) {
      // Apply variable substitution to system message
      let processedSystem = system;
      if (variables && Object.keys(variables).length > 0) {
        try {
          processedSystem = this.templateManager.renderString(system, variables);
        } catch (error) {
          this.logger.error("Template rendering error for system message:", error);
          // Fall back to original system message
        }
      }

      // Filter out any existing system messages
      resultMessages = resultMessages.filter((msg) => msg.role !== "system");

      // Add the new system message at the beginning
      resultMessages.unshift({
        role: "system",
        content: processedSystem
      });
    }

    // Add images to the latest user message if provided
    if (images && images.length > 0) {
      resultMessages = this.addImagesToMessages(resultMessages, images);
    }

    return resultMessages;
  }

  /**
   * Split a template string by role markers
   * @param template The template string to split
   * @returns Array of {role, content} objects, always including at least a user message
   */
  private splitTemplateByRoles(template: string): LlmMessage[] {
    // Split by role markers that are on their own line with a newline after the colon
    const sections = template.split(/^# (system|user|assistant|developer|tool):\n/m);

    // If no role markers were found, return the template as a user message
    if (sections.length === 1) {
      return [
        {
          role: "user",
          content: template.trim()
        }
      ];
    }

    // Process the sections into messages
    const messages: LlmMessage[] = [];
    for (let i = 1; i < sections.length; i += 2) {
      const roleString = sections[i].trim();
      const content = sections[i + 1]?.trim() || "";

      // Map 'developer' role to 'system' for compatibility
      const role =
        roleString === "developer" ? "system" : (roleString as "system" | "user" | "assistant" | "tool");

      // Create base message
      const message: Partial<LlmMessage> = {
        role,
        content
      };

      // Add tool_call_id for tool messages
      if (role === "tool") {
        (message as any).tool_call_id = ""; // Default empty ID, should be filled in by caller
      }

      messages.push(message as LlmMessage);
    }

    return messages;
  }

  /**
   * Add images to the last user message
   */
  private addImagesToMessages(messages: LlmMessage[], images: string[]): LlmMessage[] {
    if (images.length === 0 || messages.length === 0) {
      return messages;
    }

    // Find the last user message
    const lastUserMessageIndex = messages
      .map((msg, index) => ({ role: msg.role, index }))
      .filter((item) => item.role === "user")
      .pop()?.index;

    if (lastUserMessageIndex === undefined) {
      return messages;
    }

    const updatedMessages = [...messages];
    const userMessage = updatedMessages[lastUserMessageIndex];

    // Create content parts array
    const contentParts: ContentPart[] = [];

    // Add text content if it exists
    if (typeof userMessage.content === "string" && userMessage.content) {
      contentParts.push({
        type: "text",
        text: userMessage.content
      });
    }
    // If content is already an array, preserve it
    else if (Array.isArray(userMessage.content)) {
      contentParts.push(...(userMessage.content as ContentPart[]));
    }

    // Add each image to the content array
    for (const imagePath of images) {
      try {
        const base64Image = fs.readFileSync(imagePath, { encoding: "base64" });
        const mimeType = this.getMimeTypeFromPath(imagePath);
        contentParts.push({
          type: "image",
          url: `data:${mimeType};base64,${base64Image}`
        });
      } catch (error) {
        this.logger.error(`Failed to read image at ${imagePath}`, error);
      }
    }

    // Update the message with the new content parts
    updatedMessages[lastUserMessageIndex] = {
      ...userMessage,
      content: contentParts
    };

    return updatedMessages;
  }

  /**
   * Get MIME type from file extension
   */
  private getMimeTypeFromPath(filePath: string): string {
    const extension = path.extname(filePath).toLowerCase();
    const mimeTypes: Record<string, string> = {
      ".jpg": "image/jpeg",
      ".jpeg": "image/jpeg",
      ".png": "image/png",
      ".gif": "image/gif",
      ".webp": "image/webp",
      ".svg": "image/svg+xml",
      ".bmp": "image/bmp"
    };

    return mimeTypes[extension] || "image/jpeg"; // Default to jpeg if unknown
  }

  /**
   * Conversational agent loop that handles tool calling automatically.
   *
   * @param params - The LLM request parameters (AgentLLMParams). Messages will be managed by the agent.
   * @returns {Promise<LlmResponse>} - The final LLM response after all tool calls are resolved.
   */
  async agent(params: AgentLLMParams): Promise<LlmResponse> {
    this.logger.debug("[agent] Starting agent loop");
    // Start the conversation (messages array)
    let conversation: LlmMessage[] = params.messages ? [...params.messages] : [];
    let lastResponse: LlmResponse | undefined;
    let loopCount = 0;
    const MAX_LOOPS = 10;

    while (true) {
      this.logger.debug(`[agent] Loop ${loopCount} - conversation length: ${conversation.length}`);
      // 1. Call ask() with conversation and conversation=true
      // Remove tools from params before passing to ask(), as ask() expects ChatCompletionTool[]
      const { tools: _agentTools, ...askParams } = params;

      lastResponse = await this.ask({
        ...askParams,
        messages: conversation,
        conversation: true,
        tools: params.tools // Pass tool definitions directly
      });
      // Add new messages to conversation
      if (lastResponse.conversation) {
        conversation = lastResponse.conversation;
      } else if (lastResponse.message !== undefined && lastResponse.message !== null) {
        // Always add the full assistant message, including tool_calls and all metadata
        conversation = [...conversation, { ...lastResponse.message }];
      }
      // Log the last assistant message
      const lastAssistant = conversation.filter((m) => m.role === "assistant").slice(-1)[0];
      this.logger.debug(
        `[agent] Last assistant message: ${lastAssistant ? JSON.stringify(lastAssistant) : "none"}`
      );
      // 2. Check for tool calls in the response
      const toolCalls =
        lastResponse.toolCalls || (lastResponse.message && (lastResponse.message as any).tool_calls) || [];
      this.logger.debug(`[agent] Tool calls detected: ${JSON.stringify(toolCalls)}`);
      if (!toolCalls.length) {
        this.logger.debug("[agent] No tool calls found, breaking loop.");
        break;
      }
      // Log tool call arguments
      for (const call of toolCalls) {
        this.logger.debug(
          `[agent] Tool call: id=${call.id}, name=${call.function.name}, args=${call.function.arguments}`
        );
      }

      // 3. Execute tool calls and append results as tool messages
      let toolMessages;
      if (loopCount >= MAX_LOOPS) {
        // If we've reached the maximum number of loops, return error messages for tool calls
        // instead of executing them
        this.logger.warn(
          `[agent] Max loops (${MAX_LOOPS}) reached. Returning error responses for tool calls.`
        );
        toolMessages = toolCalls.map((call) => ({
          role: "tool",
          tool_call_id: call.id,
          content: `Error: Maximum number of tool calls (${MAX_LOOPS}) has been reached. Please provide a final answer without using additional tools.`
        }));
      } else {
        // Normal execution within loop limit
        toolMessages = await executeToolCalls(toolCalls, params.tools);
      }

      this.logger.debug(`[agent] Tool messages: ${JSON.stringify(toolMessages)}`);
      conversation = [...conversation, ...toolMessages];
      // Log conversation preview
      const preview = conversation.slice(-5).map((m) => ({
        role: m.role,
        content: typeof m.content === "string" ? m.content.substring(0, 80) : m.content
      }));
      this.logger.debug(`[agent] Conversation preview (last 5): ${JSON.stringify(preview, null, 2)}`);
      // 4. Now, loop will call ask() again with updated conversation
      loopCount++;

      // Continue the loop even after MAX_LOOPS, but with error responses for tools
      // We'll let the LLM decide when to stop based on the error messages
      if (loopCount > MAX_LOOPS + 2) {
        // Safety mechanism - if the LLM keeps trying to use tools despite errors
        // for twice the MAX_LOOPS count, force a break
        this.logger.error(
          `[agent] LLM continued calling tools despite errors for ${loopCount} loops. Forcing exit.`
        );
        break;
      }
    }
    this.logger.debug(
      "[agent] Agent loop complete. Returning final response:\n" + JSON.stringify(lastResponse, null, 2)
    );
    return lastResponse!;
  }
}
