import { Inject, Injectable, Logger } from "@nestjs/common";
import { OpenAI } from "openai";
import { zodResponseFormat } from "openai/helpers/zod";
import {
  ChatCompletionCreateParamsNonStreaming,
  ChatCompletionMessageParam,
  ChatCompletionContentPartText,
  ChatCompletionContentPartImage,
  ChatCompletionContentPartInputAudio
} from "openai/resources/chat/completions";
import { OpenAIEnhancedService } from "../../openai-enhanced.service";
import { LlmProvider } from "../interfaces/llm-provider.interface";
import { LlmResponse } from "../interfaces/llm-response.interface";
import {
  AskLLMParams,
  AskLLMStructuredParams,
  LlmMessage,
  LlmToolCall,
  LlmUsage
} from "../types/ask-llm.types";
import { zodToJsonSchema } from "zod-to-json-schema";
import type { ContentPart } from "../types/ask-llm.types";
import type { ToolMessage } from "../types";

@Injectable()
export class OpenAILlmProvider implements LlmProvider {
  protected readonly logger = new Logger(this.constructor.name);

  constructor(
    @Inject(OpenAIEnhancedService)
    private readonly openAIService: OpenAIEnhancedService
  ) {}

  /**
   * Convert our generic LlmMessages to OpenAI's message format
   * Handles string, null, and ContentPart[] (text, image, audio, video, input_audio)
   * @param messages LlmMessage[]
   * @returns ChatCompletionMessageParam[]
   */
  adaptMessages(messages: LlmMessage[]): ChatCompletionMessageParam[] {
    // Helper to map ContentPart[] to OpenAI's content part format
    const mapContentParts = (
      parts: ContentPart[]
    ): (
      | ChatCompletionContentPartText
      | ChatCompletionContentPartImage
      | ChatCompletionContentPartInputAudio
    )[] =>
      parts
        .map((part: ContentPart) => {
          switch (part.type) {
            case "text":
              return { type: "text", text: part.text } as ChatCompletionContentPartText;
            case "image":
              return {
                type: "image_url",
                image_url: { url: part.url, ...(part.detail ? { detail: part.detail } : {}) }
              } as ChatCompletionContentPartImage;
            case "input_audio":
            case "audio":
            case "video":
              this.logger.warn(
                `${part.type} content part is not supported by OpenAI chat API and will be ignored.`
              );
              return undefined;
            default:
              this.logger.warn(`Unknown content part type: ${(part as any).type}`);
              return undefined;
          }
        })
        .filter(Boolean) as (
        | ChatCompletionContentPartText
        | ChatCompletionContentPartImage
        | ChatCompletionContentPartInputAudio
      )[];

    return messages.map((message) => {
      switch (message.role) {
        case "system":
        case "developer":
          return {
            role: message.role,
            content:
              typeof message.content === "string" || message.content === null
                ? message.content
                : mapContentParts(message.content).filter((p) => p.type === "text"),
            ...(message.name ? { name: message.name } : {})
          };
        case "user":
          return {
            role: "user",
            content:
              typeof message.content === "string" || message.content === null
                ? message.content
                : mapContentParts(message.content),
            ...(message.name ? { name: message.name } : {})
          };
        case "assistant": {
          // Do NOT include audio in the payload sent to OpenAI (not supported by API)
          // If present, audio will be re-attached after receiving the response
          return {
            role: "assistant",
            ...(typeof message.content === "string" || message.content === null
              ? { content: message.content }
              : { content: mapContentParts(message.content).filter((p) => p.type === "text") }),
            ...(message.name ? { name: message.name } : {}),
            ...(message.tool_calls ? { tool_calls: message.tool_calls } : {}),
            ...(message.function_call ? { function_call: message.function_call } : {})
            // `refusal` is a model-only property – omit when sending to OpenAI
            // audio is intentionally omitted here
          };
        }
        case "tool":
          return {
            role: "tool",
            content:
              typeof message.content === "string" || message.content === null
                ? message.content
                : mapContentParts(message.content).filter((p) => p.type === "text"),
            tool_call_id: (message as ToolMessage).tool_call_id
          };
        default:
          throw new Error(`Unknown message role: ${(message as any).role}`);
      }
    }) as ChatCompletionMessageParam[];
  }

  /**
   * Convert OpenAI's response to our unified format (canonical adapter for LLM pipeline)
   * Ensures all assistant message properties (tool_calls, function_call, name, etc.) are preserved.
   */
  private adaptOpenAIResponse(response: any, parsed?: any): LlmResponse {
    const choices = response.choices || [];
    const firstChoice = choices[0] || {};
    const message = firstChoice.message || {};

    // Extract content
    const content = message.content || null;

    // Extract tool calls if present
    const toolCalls: LlmToolCall[] = [];
    if (message.tool_calls && message.tool_calls.length > 0) {
      for (const toolCall of message.tool_calls) {
        try {
          toolCalls.push({
            id: toolCall.id,
            type: toolCall.type,
            function: {
              name: toolCall.function.name,
              arguments: toolCall.function.arguments
            }
          });
        } catch (error) {
          this.logger.warn(`Failed to parse tool call arguments: ${error.message}`);
        }
      }
    }

    // Extract usage information
    const usage: LlmUsage = {
      promptTokens: response.usage?.prompt_tokens || 0,
      completionTokens: response.usage?.completion_tokens || 0,
      totalTokens: response.usage?.total_tokens || 0,
      raw: response.usage || {}
    };

    // Build the assistant message, preserving all relevant properties
    const assistantMessage: any = {
      role: "assistant",
      content
    };
    if (toolCalls.length > 0) assistantMessage.tool_calls = toolCalls;
    if (message.function_call) assistantMessage.function_call = message.function_call;
    if (message.name) assistantMessage.name = message.name;
    // Add any other assistant message properties as needed

    // Build the unified response
    const unifiedResponse: LlmResponse = {
      id: response.id,
      model: response.model,
      created: response.created,
      message: assistantMessage,
      usage,
      finishReason: firstChoice.finish_reason,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      raw: response
    };

    // Add parsed data if available
    if (parsed !== undefined) {
      unifiedResponse.parsed = parsed;
    }

    return unifiedResponse;
  }

  /**
   * Convert generic params to OpenAI-specific params
   */
  private adaptParams(params: AskLLMParams): ChatCompletionCreateParamsNonStreaming {
    const { messages, system, prompt, max_completion_tokens, tools, ...openaiParams } = params;

    // Create base params
    const completionParams: ChatCompletionCreateParamsNonStreaming = {
      model: params.model || "gpt-4o-mini",
      messages: [],
      stream: false
    };

    // Build messages array from various inputs
    if (messages && messages.length > 0) {
      // If messages array is provided directly, use it
      completionParams.messages = this.adaptMessages(messages);
    } else if (system || prompt) {
      // Otherwise build from system/prompt if available
      const builtMessages: LlmMessage[] = [];

      // Add system message if provided
      if (system) {
        builtMessages.push({
          role: "system",
          content: system
        });
      }

      // Add user message from prompt if provided
      if (prompt) {
        builtMessages.push({
          role: "user",
          content: prompt
        });
      }

      completionParams.messages = this.adaptMessages(builtMessages);
    }

    // Validate messages
    if (!completionParams.messages || completionParams.messages.length === 0) {
      throw new Error("Messages array is required and cannot be empty");
    }

    // Map our tool definitions to OpenAI format if provided
    if (tools && Array.isArray(tools)) {
      this.logger.debug(
        `[OpenAILlmProvider.adaptParams] Incoming tools: ${JSON.stringify(tools.map((t) => ({ name: t.name, description: t.description, schema: !!t.schema })))} (count: ${tools.length})`
      );
      // Helper to extract the correct OpenAI parameters object from zodToJsonSchema output
      const getOpenAIParameters = (params: any) => {
        if (params && typeof params === "object") {
          if ("properties" in params) return params;
          if ("definitions" in params && params.definitions.parameters) return params.definitions.parameters;
        }
        return params;
      };
      const mappedTools = tools.map((tool: any) => {
        const openaiParams = getOpenAIParameters(tool.schema ? zodToJsonSchema(tool.schema) : {});
        this.logger.debug(
          `[OpenAILlmProvider.adaptParams] Mapping tool: ${tool.name}, OpenAI params keys: ${Object.keys(openaiParams)}`
        );
        const openAITool: OpenAI.Chat.Completions.ChatCompletionTool = {
          type: "function",
          function: {
            name: tool.name,
            description: tool.description,
            parameters: openaiParams
          }
        };

        // Add strict property to the function definition if present in the LlmToolDefinition
        if (tool.strict !== undefined && openAITool.function) {
          (openAITool.function as any).strict = tool.strict;
        }

        return openAITool;
      });
      this.logger.debug(
        `[OpenAILlmProvider.adaptParams] Mapped OpenAI tools: ${JSON.stringify(mappedTools.map((t) => t.function.name))}`
      );
      completionParams.tools = mappedTools;
    }

    // Copy over other OpenAI parameters, but exclude audio to avoid type issues
    const { audio, ...safeParams } = openaiParams; // drop non-OpenAI keys
    Object.assign(completionParams, safeParams);

    // Add max_tokens if specified
    if (max_completion_tokens !== undefined) {
      completionParams.max_tokens = max_completion_tokens;
    }

    return completionParams;
  }

  async createCompletion(params: AskLLMParams): Promise<LlmResponse> {
    try {
      const openaiParams = this.adaptParams(params);
      const rawResponse = await this.openAIService.createChatCompletion(openaiParams);

      // Convert the raw response to our standardized format
      return this.adaptOpenAIResponse(rawResponse);
    } catch (error) {
      this.logger.error(`Chat completion error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Creates a chat completion with structured output validation using a Zod schema
   * @param params Parameters for creating a structured completion
   * @returns The LLM response with parsed data
   */
  async createStructuredCompletion<T>(
    params: AskLLMStructuredParams<T>
  ): Promise<LlmResponse & { parsed?: T }> {
    try {
      const { zodSchema, schemaName, ...rest } = params;
      const openaiParams = this.adaptParams(rest);

      // Delegate to the OpenAIEnhancedService's enhanced method that returns parsed data
      const result = await this.openAIService.createStructuredChatCompletionWithParsed(
        openaiParams,
        zodSchema,
        schemaName || "StructuredResponse"
      );

      // Extract the raw response and parsed data
      const { response: rawResponse, parsed } = result;

      // Convert the raw response to our standardized format
      return this.adaptOpenAIResponse(rawResponse, parsed);
    } catch (error) {
      this.logger.error(`Structured completion error: ${error.message}`);
      throw error;
    }
  }

  private extractJsonFromResponse(content: string): any {
    try {
      // First attempt: direct JSON parsing
      return JSON.parse(content);
    } catch (error) {
      // Second attempt: find JSON object in the content
      const jsonMatches = content.match(/```(?:json)?\s*({[\s\S]*?})\s*```|({[\s\S]*})/);
      if (jsonMatches) {
        const jsonString = jsonMatches[1] || jsonMatches[2];
        try {
          return JSON.parse(jsonString);
        } catch (nestedError) {
          this.logger.warn(`Failed to parse JSON from extracted content: ${nestedError.message}`);
        }
      }

      // No valid JSON found
      return null;
    }
  }

  async createStructuredCompletionStream<T>(params: AskLLMStructuredParams<T>): Promise<any> {
    try {
      const { zodSchema, schemaName, ...rest } = params;
      const openaiParams = this.adaptParams(rest);

      // We need to specify response_format for structured output
      const streamingParams = {
        ...openaiParams,
        stream: true as const,
        response_format: zodResponseFormat(zodSchema, schemaName)
      };

      // Use the OpenAIEnhancedService's streaming capability directly
      return await this.openAIService.createStreamingCompletion(streamingParams);
    } catch (error) {
      this.logger.error(`Structured streaming completion error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a streaming chat completion
   * @param params Generic parameters
   * @returns Provider-specific stream that should be adapted by the caller
   */
  async createCompletionStream(params: AskLLMParams): Promise<any> {
    try {
      const openaiParams = this.adaptParams(params);

      // Set stream to true for streaming
      const streamingParams = {
        ...openaiParams,
        stream: true as const
      };

      // Use the OpenAIEnhancedService's streaming capability directly
      return await this.openAIService.createStreamingCompletion(streamingParams);
    } catch (error) {
      this.logger.error(`Streaming chat completion error: ${error.message}`);
      throw error;
    }
  }
}
