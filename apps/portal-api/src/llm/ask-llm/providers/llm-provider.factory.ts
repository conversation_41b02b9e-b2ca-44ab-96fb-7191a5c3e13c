import { Inject, Injectable, Logger } from "@nestjs/common";
import { LlmModuleOptions } from "../../llm.module";
import { LlmProvider } from "../interfaces/llm-provider.interface";

/**
 * Factory service that manages LLM provider selection and instantiation
 */
@Injectable()
export class LlmProviderFactory {
  private readonly logger = new Logger(LlmProviderFactory.name);
  private providers: Map<string, LlmProvider> = new Map();

  constructor(
    @Inject("LLM_MODULE_OPTIONS") private readonly options: LlmModuleOptions,
    @Inject("LLM_PROVIDERS") private readonly providerEntries: { name: string; provider: LlmProvider }[]
  ) {
    // Register all provided providers
    for (const { name, provider } of providerEntries) {
      this.registerProvider(name, provider);
    }

    // Set default provider based on configuration
    if (options.defaultProvider) {
      if (this.providers.has(options.defaultProvider.toLowerCase())) {
        this.logger.log(`Using default provider: ${options.defaultProvider}`);
      } else {
        this.logger.error(
          `Configured default provider "${options.defaultProvider}" was not registered – falling back to "openai".`
        );
      }
    }
  }

  /**
   * Get LLM provider by name
   * @param name Provider name (defaults to the default provider from options)
   * @returns LLM provider instance
   */
  getProvider(name?: string): LlmProvider {
    const providerName = name || this.options.defaultProvider || "openai";
    const providerKey = providerName.toLowerCase();

    const provider = this.providers.get(providerKey);
    if (!provider) {
      const fallbackKey = (this.options.defaultProvider ?? "openai").toLowerCase();
      const fallback = this.providers.get(fallbackKey);
      if (!fallback) {
        throw new Error(`LLM provider "${providerName}" not found and no suitable fallback registered.`);
      }
      this.logger.warn(
        `Provider "${providerName}" not found, falling back to "${this.options.defaultProvider ?? "openai"}".`
      );
      return fallback;
    }

    return provider;
  }

  /**
   * Register a new provider
   * @param name Provider name
   * @param provider Provider instance
   */
  registerProvider(name: string, provider: LlmProvider): void {
    const key = name.toLowerCase();
    if (this.providers.has(key)) {
      this.logger.warn(`Provider "${key}" already registered, overwriting`);
    }

    this.providers.set(key, provider);
    this.logger.log(`Registered provider: ${key}`);
  }

  /**
   * Get the appropriate provider for the given model
   * @param model Model name
   * @returns LLM provider instance
   */
  getProviderForModel(model?: string): LlmProvider {
    if (!model) {
      return this.getProvider();
    }

    // Check model prefixes to determine provider
    if (model.startsWith("gpt-") || model.includes("gpt")) {
      return this.getProvider("openai");
    }

    // TODO: Implement more robust model detection logic.
    // This could involve checking for other known prefixes (e.g., 'claude-')
    // or maintaining a map of known model names/patterns to providers.
    // Add more model detection logic here as needed

    // Default to configured provider
    return this.getProvider();
  }
}
