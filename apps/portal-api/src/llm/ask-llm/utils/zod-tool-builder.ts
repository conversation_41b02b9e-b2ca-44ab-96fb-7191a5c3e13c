import { LlmToolCall, ToolMessage } from "../types/ask-llm.types";
import { LlmToolDefinition } from "../interfaces/llm-tool.interface";

/**
 * Executes an array of LlmToolCall using the provided LlmToolDefinition array,
 * returning ToolMessages with results or errors.
 *
 * @param toolCalls - The tool calls to execute (from the LLM).
 * @param tools - The available tool definitions (LlmToolDefinition) to match and execute.
 * @returns {Promise<ToolMessage[]>} - An array of ToolMessages, one per tool call.
 */
export async function executeToolCalls(
  toolCalls: LlmToolCall[],
  tools: LlmToolDefinition<any>[]
): Promise<ToolMessage[]> {
  const results: ToolMessage[] = [];

  for (const toolCall of toolCalls) {
    const tool = tools.find((t) => t.name === toolCall.function.name);
    // Debug log for tool call id and details
    // eslint-disable-next-line no-console
    console.debug(
      `[executeToolCalls] Processing tool call: id=${toolCall.id}, name=${toolCall.function.name}, args=${toolCall.function.arguments}`
    );
    if (!tool) {
      results.push({
        role: "tool",
        tool_call_id: toolCall.id,
        content: `Tool not found: ${toolCall.function.name}`
      });
      continue;
    }
    try {
      const argsObject = JSON.parse(toolCall.function.arguments);
      const validatedArgs = tool.schema.parse(argsObject);
      const result = await tool.run(validatedArgs);
      // Debug log for tool response
      // eslint-disable-next-line no-console
      console.debug(`[executeToolCalls] Tool response for id=${toolCall.id}: ${JSON.stringify(result)}`);
      results.push({
        role: "tool",
        tool_call_id: toolCall.id,
        content: typeof result === "string" ? result : JSON.stringify(result)
      });
    } catch (err: any) {
      const errorType = err instanceof Error ? err.constructor.name : "Unknown Error";
      const errorMessage = err instanceof Error ? err.message : String(err);
      console.error(
        `[executeToolCalls] Error executing tool ${tool.name} (ID: ${toolCall.id}): ${errorType} - ${errorMessage}`,
        err.stack
      );
      results.push({
        role: "tool",
        tool_call_id: toolCall.id,
        content: `Tool execution error for ${tool.name}: ${errorMessage}`
      });
    }
  }
  return results;
}
