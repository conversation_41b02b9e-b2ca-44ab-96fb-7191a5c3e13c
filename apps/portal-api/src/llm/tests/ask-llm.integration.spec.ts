import * as dotenv from "dotenv";
import * as fs from "fs";
import * as path from "path";
import { z } from "zod";
import { LlmProviderFactory } from "../ask-llm/providers/llm-provider.factory";
import { OpenAILlmProvider } from "../ask-llm/providers/openai-llm-provider";
import { AskLLMService } from "../ask-llm/services/ask-llm.service";
import { OpenAIEnhancedService } from "../openai-enhanced.service";

// Load environment variables from .env.local in portal-api directory
const envPath = path.resolve(process.cwd(), ".env.local");
if (fs.existsSync(envPath)) {
  console.log(`Loading environment from: ${envPath}`);
  dotenv.config({ path: envPath });
} else {
  console.warn(`No .env.local file found at: ${envPath}`);
}

describe("AskLLMService Integration Tests", () => {
  // Helper to check if we should run tests
  const hasApiKey = !!process.env.OPENAI_API_KEY;
  let askLLMService: AskLLMService;

  // Timeout for all tests - API calls can take time
  jest.setTimeout(30000);

  beforeEach(async () => {
    if (!hasApiKey) {
      console.warn("Skipping LLM integration tests - no OPENAI_API_KEY found in .env.local");
      return;
    }

    console.log("Found API key - running live integration tests with OpenAI");

    // Create real dependencies but mock the template service
    const mockTemplateManager = {
      renderString: jest.fn().mockImplementation((template) => template),
      renderTemplate: jest.fn().mockImplementation((template, variables) => {
        if (template === "faq-intent-filter") {
          return `
            # system:
            You are an intent detection assistant for a customs management chatbot. Your job is to classify user requests into one of the following supported intents or mark them as "HUMAN_REVIEW" if they don't clearly match any supported intent.

            ## Supported Intents:
            **Information Retrieval Questions:**
            - HAS_SHIPMENT_RELEASED: User asks explicitly if the shipment has been released.
            - CURRENT_CUSTOMS_STATUS: User asks explicitly about the current status of their customs entry.

            ## User Request:
            ${variables.userRequest}

            ## Intent: 
          `;
        }
        return template;
      })
    };

    const mockLlmLogService = {
      createLlmLog: jest.fn().mockResolvedValue({ id: 1 }),
      updateLlmLog: jest.fn().mockResolvedValue(true)
    };

    // Create real OpenAIService with API key
    const openaiService = new OpenAIEnhancedService(
      {
        openaiApiKey: process.env.OPENAI_API_KEY,
        deepseekApiKey: "",
        defaultProvider: "openai"
      },
      mockLlmLogService as any
    );

    // Create real OpenAIProvider
    const openaiProvider = new OpenAILlmProvider(openaiService);

    // Create real ProviderFactory
    const providerFactory = new LlmProviderFactory(
      {
        openaiApiKey: process.env.OPENAI_API_KEY,
        deepseekApiKey: "",
        defaultProvider: "openai"
      },
      [{ name: "openai", provider: openaiProvider }]
    );

    // Create the service to test with real dependencies
    askLLMService = new AskLLMService(providerFactory, mockTemplateManager as any, {
      openaiApiKey: process.env.OPENAI_API_KEY,
      deepseekApiKey: "",
      defaultProvider: "openai"
    });
  });

  it("should process a basic prompt with real API", async () => {
    if (!hasApiKey) return;

    const startTime = Date.now();

    const result = await askLLMService.ask({
      prompt: "Hello, what is 2+2?",
      model: "gpt-4o-mini",
      temperature: 0.2
    });

    const executionTime = Date.now() - startTime;

    console.log(`API Response: "${result.message.content}"`);
    console.log(`Execution time: ${executionTime}ms`);

    // Verify real API interaction
    // Removed timing assertion to avoid flakiness due to variable API latency
    expect(result.message.content).toMatch(/4|four/i);
  });

  it("should process a prompt with template against real API", async () => {
    if (!hasApiKey) return;

    const startTime = Date.now();

    const result = await askLLMService.ask({
      promptTemplate: "faq-intent-filter",
      variables: { userRequest: "Has my shipment been released yet?" },
      model: "gpt-4o-mini",
      temperature: 0.2
    });

    const executionTime = Date.now() - startTime;

    console.log(`Template API Response: "${result.message.content}"`);
    console.log(`Template execution time: ${executionTime}ms`);

    // Verify real API interaction
    // Removed timing assertion to avoid flakiness due to variable API latency
  });

  it("should handle structured output with schema validation using real API", async () => {
    if (!hasApiKey) return;

    const startTime = Date.now();

    const TestSchema = z.object({
      answer: z.string(),
      confidence: z.number()
    });

    const result = await askLLMService.ask({
      // OpenAI requires 'json' to be in the prompt when using response_format: json_object
      prompt:
        'What is the capital of France? Provide your answer in JSON format with an "answer" field containing your response and a "confidence" field with a number between 0 and 1.',
      zodSchema: TestSchema,
      schemaName: "TestResponse",
      model: "gpt-4o-mini",
      temperature: 0.1
    });

    const executionTime = Date.now() - startTime;

    console.log(`Structured API Response:`, result.parsed);
    console.log(`Structured execution time: ${executionTime}ms`);

    // Verify real API interaction
    // Removed timing assertion to avoid flakiness due to variable API latency
    expect(result.parsed).toBeDefined();

    if (result.parsed) {
      expect(result.parsed.answer).toMatch(/Paris/i);
      expect(typeof result.parsed.confidence).toBe("number");
      expect(result.parsed.confidence).toBeGreaterThanOrEqual(0);
      expect(result.parsed.confidence).toBeLessThanOrEqual(1);
    }
  });
  it("should process tool calls using agent() with a simple tool", async () => {
    if (!hasApiKey) return;

    // Define a simple add tool using LlmToolDefinition structure
    const addTool = {
      run: async ({ a, b }: { a: number; b: number }) => (a + b).toString(),
      schema: z.object({
        a: z.number(),
        b: z.number()
      }),
      description: "Adds two numbers",
      name: "add"
    };

    // Start a conversation that will trigger a tool call
    const result = await askLLMService.agent({
      model: "gpt-4o-mini",
      prompt: "What is 2 plus 3? Use the add tool.",
      temperature: 0.2,
      tools: [addTool]
    });

    // The agent should have called the tool and returned the result
    expect(result).toBeDefined();
    expect(result.conversation).toBeDefined();
    const toolMessage = result.conversation?.find((msg: any) => msg.role === "tool" && msg.content);
    expect(toolMessage).toBeDefined();
    expect(toolMessage.content).toBe("5");
  });

  it("should process multi-step tool calls using agent() with two tools and output a zodSchema", async () => {
    if (!hasApiKey) return;

    // Define add tool using LlmToolDefinition structure
    const addTool = {
      run: async ({ a, b }: { a: number; b: number }) => (a + b).toString(),
      schema: z.object({
        a: z.number(),
        b: z.number()
      }),
      description: "Adds two numbers",
      name: "add"
    };

    // Define multiply tool using LlmToolDefinition structure
    const multiplyTool = {
      run: async ({ a, b }: { a: number; b: number }) => (a * b).toString(),
      schema: z.object({
        a: z.number(),
        b: z.number()
      }),
      description: "Multiplies two numbers",
      name: "multiply"
    };

    // Define the output schema
    const OutputSchema = z.object({
      answer: z.number(),
      steps: z.array(z.string())
    });

    // Ask the agent to solve a multi-step problem
    const result = await askLLMService.agent({
      model: "gpt-4o-mini",
      prompt:
        "What is (2 + 3) * 4? Use the add and multiply tools. Return the answer and a list of steps as JSON.",
      temperature: 0.2,
      zodSchema: OutputSchema,
      schemaName: "OutputSchema",
      tools: [addTool, multiplyTool]
    });

    expect(result).toBeDefined();
    expect(result.parsed).toBeDefined();
    expect(typeof result.parsed.answer).toBe("number");
    expect(result.parsed.answer).toBe(20);
    expect(Array.isArray(result.parsed.steps)).toBe(true);
    expect(result.parsed.steps.length).toBeGreaterThanOrEqual(2);
    expect(result.parsed.steps.join(" ")).toMatch(/add|multiply/i);
  });
});
