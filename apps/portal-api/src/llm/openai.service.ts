import { Inject, Injectable, Logger } from "@nestjs/common";
import { OpenAI } from "openai";
import { EmbeddingModel } from "openai/resources/embeddings";
import tiktoken from "tiktoken";
import { LlmLogService } from "./llm-log.service";
import { LlmModuleOptions } from "./llm.module";

@Injectable()
export class OpenAIService {
  protected readonly logger = new Logger(this.constructor.name);
  protected client: OpenAI;

  constructor(
    @Inject("LLM_MODULE_OPTIONS")
    private readonly options: LlmModuleOptions,
    @Inject(LlmLogService)
    protected readonly llmLogService: LlmLogService
  ) {
    if (!options.openaiApiKey) {
      throw new Error("OpenAI API key is not set");
    }

    this.client = new OpenAI({
      apiKey: options.openaiApiKey
    });
  }

  /**
   * Get the OpenAI client
   *
   * @returns OpenAI
   */
  getClient(): OpenAI {
    return this.client;
  }

  /**
   * Create a chat completion
   *
   * @param params
   * @returns OpenAI.Chat.Completions.ChatCompletion
   */
  async createChatCompletion(
    params: OpenAI.Chat.Completions.ChatCompletionCreateParamsNonStreaming,
    caller?: string
  ) {
    const llmLog = await this.llmLogService.createLlmLog({
      request: JSON.stringify(params),
      caller,
      model: params?.model || undefined
    });

    try {
      const response = await this.client.chat.completions.create(params);
      await this.llmLogService.updateLlmLog(llmLog?.id, JSON.stringify(response));
      return response;
    } catch (error) {
      await this.llmLogService.updateLlmLog(llmLog?.id, error.message);
      throw error;
    }
  }

  /**
   * Get the number of tokens in a text.
   * @param text Text to get the number of tokens for
   * @param embeddingModel Embedding model to use. Defaults to "text-embedding-3-small"
   * @returns Number of tokens in the text
   */
  getNumberOfTokens(text: string, embeddingModel: EmbeddingModel = "text-embedding-3-small") {
    const encoding = tiktoken.encoding_for_model(embeddingModel);
    const tokens = encoding.encode(text).length;
    encoding.free();
    return tokens;
  }

  /**
   * Create embeddings for a list of texts.
   * @param text Text to create an embedding for
   * @param embeddingModel Embedding model to use. Defaults to "text-embedding-3-small"
   * @returns Embedding of the text
   */
  async createEmbeddings(
    text: Array<string>,
    embeddingModel: EmbeddingModel = "text-embedding-3-small"
  ): Promise<Array<OpenAI.Embeddings.Embedding> | null> {
    try {
      if (text.length <= 0 || text.some((t) => typeof t !== "string" || t.length <= 0))
        throw new Error("Invalid text");

      const embedding = await this.client.embeddings.create({
        model: embeddingModel,
        input: text
      });
      return embedding.data;
    } catch (error) {
      this.logger.error(`Create Embedding Error: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  /**
   * Get the cosine similarity between two vectors.
   * @param vecA Vector A
   * @param vecB Vector B
   * @returns Cosine similarity between the two vectors
   */
  getCosineSimilarity(vecA: Array<number>, vecB: Array<number>) {
    if (vecA.length !== vecB.length) throw new Error("Vector dimensions must match");
    if (vecA.every((v) => v === 0) || vecB.every((v) => v === 0))
      throw new Error(`Zero vectors are not allowed in cosine similarity calculation`);

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    for (let i = 0; i < vecA.length; i++) {
      dotProduct += vecA[i] * vecB[i];
      normA += vecA[i] * vecA[i];
      normB += vecB[i] * vecB[i];
    }
    normA = Math.sqrt(normA);
    normB = Math.sqrt(normB);
    return dotProduct / (normA * normB);
  }
}
