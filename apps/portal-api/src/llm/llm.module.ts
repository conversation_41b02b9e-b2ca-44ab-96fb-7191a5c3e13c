import { DynamicModule, Global, Module, Provider } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";
import { LlmLog, TemplateManagerModule } from "nest-modules";
import * as path from "path";
import { LlmProviderFactory } from "./ask-llm/providers/llm-provider.factory";
import { OpenAILlmProvider } from "./ask-llm/providers/openai-llm-provider";
import { AskLLMService } from "./ask-llm/services/ask-llm.service";
import { DeepSeekService } from "./deepseek.service";
import { LlmLogService } from "./llm-log.service";
import { OpenAIEnhancedService } from "./openai-enhanced.service";
import { OpenAIStreamingService } from "./openai-streaming.service";
import { OpenAIService } from "./openai.service";
import { PromptTemplateManager } from "./prompt-template-manager";

export interface LlmModuleOptions {
  openaiApiKey?: string;
  deepseekApiKey?: string;
  defaultProvider?: string;
}

/**
 * Creates the provider configuration for the LLM module
 * This avoids duplication between the static module and dynamic module registrations
 */
export const createLlmProviders = (): Provider[] => {
  return [
    LlmProviderFactory,
    OpenAILlmProvider,
    AskLLMService,
    OpenAIService,
    OpenAIStreamingService,
    DeepSeekService,
    LlmLogService,
    OpenAIEnhancedService,
    PromptTemplateManager,
    {
      provide: "LLM_MODULE_OPTIONS",
      useFactory: (configService: ConfigService, passedOptions: LlmModuleOptions) => ({
        openaiApiKey: passedOptions?.openaiApiKey ?? configService.get<string>("OPENAI_API_KEY") ?? "",
        deepseekApiKey: passedOptions?.deepseekApiKey ?? configService.get<string>("DEEPSEEK_API_KEY") ?? "",
        defaultProvider:
          passedOptions?.defaultProvider ?? configService.get<string>("DEFAULT_LLM_PROVIDER") ?? "openai"
      }),
      inject: [ConfigService, "LLM_MODULE_OPTIONS_RAW"]
    },
    {
      provide: "LLM_PROVIDERS",
      useFactory: (openaiProvider: OpenAILlmProvider) => [
        { name: "openai", provider: openaiProvider }
        // Add other providers here as needed
      ],
      inject: [OpenAILlmProvider]
    }
  ];
};

/**
 * LLM Module for Claro
 * Provides access to various LLM services including OpenAI, DeepSeek, and high-level AskLLM
 */
@Global()
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([LlmLog]),
    TemplateManagerModule.forFeature({
      templatesPath: path.join(__dirname, "prompt-templates"),
      nunjucksConfig: {
        autoescape: true
      }
    })
  ],
  providers: [{ provide: "LLM_MODULE_OPTIONS_RAW", useValue: {} }, ...createLlmProviders()],
  exports: [AskLLMService, OpenAIService, OpenAIStreamingService, DeepSeekService, PromptTemplateManager]
})
export class LlmModule {
  static register(options: LlmModuleOptions = {}): DynamicModule {
    return {
      module: LlmModule,
      imports: [
        ConfigModule,
        TypeOrmModule.forFeature([LlmLog]),
        TemplateManagerModule.forFeature({
          templatesPath: path.join(__dirname, "prompt-templates"),
          nunjucksConfig: {
            autoescape: true
          }
        })
      ],
      providers: [{ provide: "LLM_MODULE_OPTIONS_RAW", useValue: options }, ...createLlmProviders()],
      exports: [AskLLMService, OpenAIService, OpenAIStreamingService, DeepSeekService, PromptTemplateManager]
    };
  }
}
