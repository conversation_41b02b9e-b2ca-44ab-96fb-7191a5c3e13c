import { Inject, Injectable, Logger } from "@nestjs/common";
import { TemplateManagerService } from "nest-modules";
import { join } from "path";

const TEMPLATES_PATH = join(__dirname, "prompt-templates");

export enum PromptTemplateName {
  CLASSIFY_QUESTION = "classify-question-category",
  DECOMPOSE_TASK = "decompose-task",
  CLASSIFY_INTENT = "classify-intent-only",
  EXTRACT_SHIPMENT_IDENTIFIERS = "extract-shipment-identifiers",
  IDENTIFY_SHIPMENT = "identify-shipment-prompt",
  EXTRACT_SHIPMENT_FIELDS = "extract-shipment-fields"
}

interface ParsedPrompts {
  systemPrompt: string;
  userPrompt: string;
}

interface PromptSection {
  role: "system" | "user" | "assistant";
  content: string;
}

@Injectable()
export class PromptTemplateManager {
  private readonly logger = new Logger(PromptTemplateManager.name);

  constructor(
    @Inject(TemplateManagerService)
    private readonly templateManagerService: TemplateManagerService
  ) {}

  getPrompt(template: string, data: Record<string, any>): ParsedPrompts {
    const processedTemplate = this.templateManagerService.renderString(template, data);
    return this.processPrompt(processedTemplate);
  }

  getPromptFromTemplate(template: string, data: Record<string, any>): ParsedPrompts {
    const processedTemplate = this.templateManagerService.renderTemplate(template, data);
    return this.processPrompt(processedTemplate);
  }

  getPromptMultipleSectionsFromTemplate(template: string, data: Record<string, any>): Array<PromptSection> {
    const processedTemplate = this.templateManagerService.renderTemplate(template, data);
    return this.processPromptMultipleSections(processedTemplate);
  }

  /**
   * Processes the prompt based on the provided parameters.
   *
   * @see https://microsoft.github.io/promptflow/reference/tools-reference/llm-tool.html#how-to-write-a-chat-prompt
   * @param templateName The name of the template file (e.g., 'prompt.njk').
   * @param variables An object containing the data to be injected into the template.
   * @returns The system and user prompts
   */
  private processPrompt(processedTemplate: string): ParsedPrompts {
    let systemPrompt = "";
    let userPrompt = "";

    const sections = processedTemplate.split(/^# (system|user|assistant):/m);

    for (let i = 1; i < sections.length; i += 2) {
      const role = sections[i];
      const content = sections[i + 1]?.trim() || "";

      if (role === "system") {
        systemPrompt = content;
      } else if (role === "user") {
        userPrompt = content;
      }
    }

    return { systemPrompt, userPrompt };
  }

  private processPromptMultipleSections(processedTemplate: string): Array<PromptSection> {
    const promptSections: Array<PromptSection> = [];
    const sections = processedTemplate.split(/^# (system|user|assistant):/m);

    for (let i = 1; i < sections.length; i += 2) {
      const role = sections[i] as "system" | "user" | "assistant";
      const content = sections[i + 1]?.trim() || "";

      promptSections.push({ role, content });
    }

    return promptSections;
  }
}

export default PromptTemplateManager;
