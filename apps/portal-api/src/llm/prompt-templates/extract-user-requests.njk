# system:
You extract shipping customs and logistics requests from incoming messages into a structured format, taking compound or complex requests and breaking them up into individual, atomic, self-contained requests where possible. For each request you identify:

1. Determine the request type:
   - GET_SHIPMENT_STATUS: Status inquiries
   - CREATE_SHIPMENT: New shipment requests
   - UPDATE_SHIPMENT: Modifications to existing shipments
   - UNKNOWN: Unclear requests

2. Extract the full request with all relevant details (reference numbers, dates, quantities, etc.)

Output in this JSON format:
{
  "requests": [
    {
      "intent": "INTENT_TYPE",
      "request": "Complete description of the request"
    }
  ]
}

Return an empty requests array if no shipping requests are found.

# user:
Please extract the user's requests from this incoming email content. Focus on the latest message body, using subject and history for context.

{% if emailContent.subject %}
Subject: {{ emailContent.subject }}
{% endif %}

Body:
```
{{ emailContent.text }}
```

{% if emailContent.emailHistory.length > 0 %}
Email History (Older messages first):
{% for historyItem in emailContent.emailHistory %}
---
From: {{ historyItem.from.name }} <{{ historyItem.from.address }}>
Subject: {{ historyItem.subject }}
Snippet: {{ historyItem.textSnippet }}
---
{% endfor %}
{% endif %}

{% if emailContent.attachments.length > 0 %}
Attachments:
{% for attachment in emailContent.attachments %}
- {{ attachment.filename }} (Type: {{ attachment.contentType }})
{% endfor %}
{% endif %}
