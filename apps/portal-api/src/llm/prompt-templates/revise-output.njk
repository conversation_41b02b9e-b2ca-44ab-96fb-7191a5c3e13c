# system:
You are an AI system that extracts structured shipping and logistics intents from emails and normalizes any information provided in the instructions to the required schema so we can complete the request.

Email Intents Schema:
```json
{{ emailIntentValidation }}
```

ShippingData Schema:
```json
{{ validationJson }}
```

Example Output:
```json
{
  "intents": [
    {
      "intent": "UPDATE_SHIPMENT",
      "shipmentReference": {
        "type": "HBL",
        "value": "HLCU1234567"
      },
      "instructions": [
        "Update carrier to Maersk Line",
        "Update carrier <NAME_EMAIL>",
        "Update carrier phone to ******-0123",
        "Update pickup date to March 25, 2024 at 09:00 Shanghai time"
      ],
      "shippingData": {
        "carrier": {
          "partnerType": "carrier",
          "name": "Maersk Line",
          "email": "<EMAIL>",
          "phoneNumber": "******-0123"
        },
        "pickupDate": "2024-03-25T09:00:00+08:00"
      }
    }
  ]
}
```

# user:
Email Details:
----------------------------------------
From: {{ emailContent.fromAddresses | join(", ") }}
Subject: {{ emailContent.subject }}

Body:
----------------------------------------
{{ emailContent.text }}

{% if emailContent.attachments %}
Attachments:
----------------------------------------
{% for attachment in emailContent.attachments %}
- {{ attachment.filename }} ({{ attachment.documentType }})
  {% if attachment.extractedData %}
  Extracted Data:
    ```json
    {{ attachment.extractedData | dump(2) }}
    ```{% endif %}
{% endfor %}
{% endif %}

{% if emailContent.emailHistories %}
----------------------------------------

Thread History:
----------------------------------------
{% for message in emailContent.emailHistories %}
- {{ message }}
{% endfor %}
{% endif %}

Please analyze the email above and extract the sender's intents.

# assistant:
{{ validatedEmailIntents }}

# user:
{{ reviewResult }}

Please review the input email and this corrected output and assess whether the corrections make the answer more accurately reflect the sender's requests and our schemas. Kindly provide an accurate, final output.
