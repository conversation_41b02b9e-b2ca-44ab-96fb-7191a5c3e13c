import { Injectable, Logger, InternalServerErrorException, BadRequestException } from "@nestjs/common";
import { OpenAI } from "openai";
import { zodResponseFormat } from "openai/helpers/zod";
import { z } from "zod";
import { LlmResponse } from "./ask-llm/interfaces/llm-response.interface";
import { LlmToolCall, LlmUsage } from "./ask-llm/types/ask-llm.types";
import { OpenAIService } from "./openai.service";
import { LlmLogService } from "./llm-log.service";

/**
 * Service for handling OpenAI chat completions with beta features enabled.
 * Extends OpenAIService to utilize beta capabilities provided by OpenAI.
 */
@Injectable()
export class OpenAIEnhancedService extends OpenAIService {
  protected readonly logger = new Logger(this.constructor.name);

  /**
   * Adapts an OpenAI response to our unified LlmResponse format
   */
  private adaptOpenAIResponse(response: any, parsed?: any): LlmResponse {
    const choices = response.choices || [];
    const firstChoice = choices[0] || {};
    const message = firstChoice.message || {};

    // Check for refusal first
    if (message.refusal) {
      this.logger.warn(`LLM Refusal Detected: ${message.refusal}`);
      throw new BadRequestException(`LLM Refusal: ${message.refusal}`);
    }

    // Check if generation was cut off due to length
    if (firstChoice.finish_reason === "length") {
      this.logger.warn("LLM response incomplete (finish_reason: length)");
      throw new BadRequestException("Incomplete LLM response due to token limit");
    }

    // Extract content
    const content = message.content || null;

    // Extract tool calls if present
    const toolCalls: LlmToolCall[] = [];
    if (message.tool_calls && message.tool_calls.length > 0) {
      for (const toolCall of message.tool_calls) {
        try {
          toolCalls.push({
            id: toolCall.id,
            type: toolCall.type,
            function: {
              name: toolCall.function.name,
              arguments: (() => {
                try {
                  return JSON.parse(toolCall.function.arguments ?? "{}");
                } catch {
                  this.logger.warn(`Malformed tool-call arguments: ${toolCall.function.arguments}`);
                  return {};
                }
              })()
            }
          });
        } catch (error) {
          this.logger.warn(`Failed to parse tool call arguments: ${error.message}`);
        }
      }
    }

    // Extract usage information
    const usage: LlmUsage = {
      promptTokens: response.usage?.prompt_tokens || 0,
      completionTokens: response.usage?.completion_tokens || 0,
      totalTokens: response.usage?.total_tokens || 0,
      raw: response.usage || {}
    };

    // Build the unified response
    const unifiedResponse: LlmResponse = {
      id: response.id,
      model: response.model,
      created: response.created,
      message: {
        role: "assistant",
        content,
        tool_calls: toolCalls.length > 0 ? toolCalls : undefined
      },
      usage,
      finishReason: firstChoice.finish_reason,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      raw: response
    };

    // Add parsed data if available
    if (parsed !== undefined) {
      unifiedResponse.parsed = parsed;
    }

    return unifiedResponse;
  }

  /**
   * Create a chat completion using beta features
   *
   * @param params Chat completion parameters
   * @param caller Optional identifier for logging purposes
   * @returns OpenAI chat completion response
   */
  async createChatCompletion(
    params: OpenAI.Chat.Completions.ChatCompletionCreateParamsNonStreaming,
    caller?: string
  ) {
    const llmLog = await this.llmLogService.createLlmLog({
      request: JSON.stringify(params),
      caller,
      model: params?.model || undefined
    });

    try {
      // Use beta parse endpoint only if response_format is present
      const useBetaParse = !!params.response_format && this.client.beta?.chat?.completions?.parse;
      const target = useBetaParse
        ? this.client.beta.chat.completions.parse
        : this.client.chat.completions.create;
      const response = await target.call(this.client, params);
      await this.llmLogService.updateLlmLog(llmLog?.id, JSON.stringify(response));
      return response; // Return response directly to maintain compatibility with base class
    } catch (error) {
      await this.llmLogService.updateLlmLog(llmLog?.id, error.message);
      throw error;
    }
  }

  /**
   * Creates a chat completion with structured output validation using a Zod schema
   * This keeps the original method signature compatible with inheritance requirements
   *
   * @param params Chat completion parameters
   * @param zodSchema Zod schema for validating response
   * @param schemaName Name of the schema for documentation
   * @param caller Optional identifier for logging purposes
   * @returns OpenAI chat completion response
   */
  async createStructuredChatCompletion<T>(
    params: OpenAI.Chat.Completions.ChatCompletionCreateParamsNonStreaming,
    zodSchema: z.ZodType<T>,
    schemaName: string = "StructuredResponse",
    caller?: string
  ) {
    // Get the OpenAI response format from the zod schema
    const responseFormat = zodResponseFormat(zodSchema, schemaName);

    // Add Zod structured response format to params
    const parseParams = {
      ...params,
      response_format: responseFormat
    };

    // Keep a minimal log indicating structured output is used
    this.logger.debug(`[${schemaName}] Using structured output (response_format).`);

    const llmLog = await this.llmLogService.createLlmLog({
      request: JSON.stringify(parseParams),
      caller,
      model: parseParams?.model || undefined
    });

    try {
      // Attempt to use beta parse API if available
      if (this.client.beta?.chat?.completions?.parse) {
        try {
          this.logger.debug(`Using openai beta.chat.completions.parse API with model: ${params.model}`);
          const response = await this.client.beta.chat.completions.parse(parseParams);
          await this.llmLogService.updateLlmLog(llmLog?.id, JSON.stringify(response));
          return response;
        } catch (parseError) {
          // Log the error but continue with fallback
          this.logger.warn(
            `Beta parse API error: ${parseError.message}, falling back to standard completion`
          );
        }
      }

      // Fallback to standard completion with JSON response format
      this.logger.debug(`Falling back to standard completion API with model: ${params.model}`);
      const response = await this.client.chat.completions.create({
        ...parseParams
      });

      await this.llmLogService.updateLlmLog(llmLog?.id, JSON.stringify(response));
      return response;
    } catch (error) {
      this.logger.error(`Error in structured chat completion: ${error.message}`);
      await this.llmLogService.updateLlmLog(llmLog?.id, error.message);
      throw error;
    }
  }

  /**
   * Enhanced version that returns both the response and parsed data
   * This provides additional functionality beyond the base class
   *
   * @param params Chat completion parameters
   * @param zodSchema Zod schema for validating response
   * @param schemaName Name of the schema for documentation
   * @param caller Optional identifier for logging purposes
   * @returns Object containing the raw OpenAI response and parsed data
   */
  async createStructuredChatCompletionWithParsed<T>(
    params: OpenAI.Chat.Completions.ChatCompletionCreateParamsNonStreaming,
    zodSchema: z.ZodType<T>,
    schemaName: string = "StructuredResponse",
    caller?: string
  ): Promise<{ response: any; parsed?: T }> {
    // Get the OpenAI response format from the zod schema
    const responseFormat = zodResponseFormat(zodSchema, schemaName);

    // Add Zod structured response format
    const parseParams = {
      ...params,
      response_format: responseFormat
    };

    // Log the prompt (all message.content items) before sending the API call
    const promptContents = LlmLogService.getLastMessageContent(parseParams?.messages);
    this.logger.debug(`[Calling LLM with structured output schema '${schemaName}']:\n\n${promptContents}`);

    const llmLog = await this.llmLogService.createLlmLog({
      request: JSON.stringify(parseParams),
      caller,
      model: parseParams?.model || undefined
    });

    try {
      // Attempt to use beta parse API if available
      if (this.client.beta?.chat?.completions?.parse) {
        try {
          this.logger.debug(`Using beta parse API with model: ${params.model}\n`);
          const response = await this.client.beta.chat.completions.parse(parseParams);
          await this.llmLogService.updateLlmLog(llmLog?.id, JSON.stringify(response));

          // Extract parsed data if available
          let parsed: T | undefined;
          if (response.choices[0]?.message?.parsed) {
            parsed = response.choices[0].message.parsed as T;
            this.logger.debug(`Successfully parsed response with schema: ${schemaName}`);
          } else {
            this.logger.warn(`No parsed data available in the response for schema: ${schemaName}`);
          }

          // Log all message.content text items (formatted, joined by double newlines)
          const allContents = LlmLogService.extractAllMessageContents(response);
          if (allContents) {
            this.logger.debug(`[LLM message.content items]:\n${allContents}`);
          }

          // Return the response and parsed data as separate properties
          return { response, parsed };
        } catch (parseError) {
          // Log the error but continue with fallback
          this.logger.warn(
            `Beta parse API error: ${parseError.message}, falling back to standard completion`
          );
        }
      }

      // Fallback to standard completion with JSON response format
      this.logger.debug(`Falling back to standard completion API with model: ${params.model}`);
      const response = await this.client.chat.completions.create({
        ...parseParams
      });

      let parsed: T | undefined;

      try {
        if (response.choices[0]?.message?.content) {
          const content = response.choices[0].message.content;
          this.logger.debug(`Attempting to parse content: ${content.substring(0, 200)}...`);
          const jsonData = this.extractJsonFromResponse(content);
          parsed = zodSchema.parse(jsonData);
          this.logger.debug(`Successfully manually parsed JSON response with schema: ${schemaName}`);
        }
      } catch (error) {
        this.logger.error(`Failed to parse response as JSON: ${error.message}`);
      }

      // Log all message.content text items (formatted, joined by double newlines)
      const allContents = LlmLogService.extractAllMessageContents(response);
      if (allContents) {
        this.logger.debug(`[LLM message.content items]:\n${allContents}`);
      }

      await this.llmLogService.updateLlmLog(llmLog?.id, JSON.stringify(response));
      // Return the response and parsed data as separate properties
      return { response, parsed };
    } catch (error) {
      this.logger.error(`Error in structured chat completion: ${error.message}`);
      await this.llmLogService.updateLlmLog(llmLog?.id, error.message);
      throw error;
    }
  }

  /**
   * Helper method to extract JSON from response content
   * that might be wrapped in markdown code blocks in callback functions
   */
  private extractJsonFromResponse(content: string): any {
    try {
      // First try direct parsing
      return JSON.parse(content);
    } catch (e) {
      // Look for JSON in code blocks
      const jsonRegex = /```(?:json)?\s*([\s\S]*?)\s*```/;
      const match = content.match(jsonRegex);

      if (match?.[1]) {
        try {
          return JSON.parse(match[1]);
        } catch (innerError) {
          this.logger.error(`Failed to parse JSON from code block: ${innerError.message}`);
        }
      }

      // Try to find anything that looks like a JSON object
      const objectRegex = /({[\s\S]*})/;
      const objectMatch = content.match(objectRegex);

      if (objectMatch?.[1]) {
        try {
          return JSON.parse(objectMatch[1]);
        } catch (innerError) {
          this.logger.error(`Failed to parse JSON object: ${innerError.message}`);
        }
      }

      throw new Error("Could not extract JSON from response");
    }
  }

  /**
   * Create a streaming chat completion
   *
   * @param params Streaming parameters (stream will be set to true automatically)
   * @param caller Optional identifier for logging purposes
   * @returns OpenAI streaming chat completion with event handlers
   * @throws Error if messages array is empty or API call fails
   */
  async createStreamingCompletion(
    params: OpenAI.Chat.Completions.ChatCompletionCreateParamsStreaming,
    caller?: string
  ) {
    if (!params.messages?.length) {
      this.logger.error("No messages provided for chat completion");
      throw new Error("Messages array is required and cannot be empty");
    }

    const llmLog = await this.llmLogService.createLlmLog({
      request: JSON.stringify(params),
      caller,
      model: params?.model || undefined
    });

    try {
      if (this.client.beta?.chat?.completions?.stream) {
        const stream = await this.client.beta.chat.completions.stream({ ...params, stream: true });
        // Set up logging for completion
        let aggregatedResponse = "";
        stream.on("content.delta", ({ delta }) => {
          if (typeof delta === "string") {
            aggregatedResponse += delta;
          } else if (delta && typeof delta === "object" && "content" in delta) {
            aggregatedResponse += (delta as { content?: string }).content ?? "";
          }
        });

        stream.on("content.done", async () => {
          await this.llmLogService.updateLlmLog(llmLog?.id, aggregatedResponse);
        });

        // Flush logs on error or early termination
        stream.on("error", async (err) => {
          await this.llmLogService.updateLlmLog(llmLog?.id, String(err instanceof Error ? err.message : err));
        });
        stream.on("end", async () => {
          await this.llmLogService.updateLlmLog(llmLog?.id, aggregatedResponse);
        });

        return stream;
      }
      throw new Error(
        "Streaming is not supported by this OpenAI SDK version. Please upgrade to a version with beta.chat.completions.stream."
      );
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Streaming Chat Completion Error: ${errorMessage}`);
      await this.llmLogService.updateLlmLog(llmLog?.id, errorMessage);
      throw error;
    }
  }
}
