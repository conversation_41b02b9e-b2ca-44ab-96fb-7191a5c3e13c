import { Injectable } from "@nestjs/common";
import { OpenAI } from "openai";
import { OpenAIService } from "./openai.service";
import { LlmProviderStream } from "./ask-llm/interfaces/llm-provider-stream.interface";

/**
 * Service for handling OpenAI streaming completions.
 * Extends OpenAIService to provide streaming capabilities with logging.
 *
 * Example:
 * ```typescript
 * const stream = await service.createStreamingCompletion({
 *   model: 'gpt-4o-mini',
 *   messages: [{ role: 'user', content: 'Hello' }]
 * });
 *
 * // Event-based streaming
 * stream.on('content.delta', ({ delta }) => console.log(delta));
 * stream.on('content.done', () => console.log('Done!'));
 *
 * // Wait for completion and get final result
 * await stream.done();
 * const completion = await stream.finalChatCompletion();
 * ```
 */
@Injectable()
export class OpenAIStreamingService extends OpenAIService {
  /**
   * Create a streaming chat completion
   *
   * @param params Streaming parameters (stream will be set to true automatically)
   * @param caller Optional identifier for logging purposes
   * @returns OpenAI streaming chat completion with event handlers
   * @throws Error if messages array is empty or API call fails
   */
  async createStreamingCompletion(
    params: OpenAI.Chat.Completions.ChatCompletionCreateParamsStreaming,
    caller?: string
  ): Promise<LlmProviderStream<string>> {
    if (!params.messages?.length) {
      this.logger.error("No messages provided for chat completion");
      throw new Error("Messages array is required and cannot be empty");
    }
    const llmLog = await this.llmLogService.createLlmLog({
      request: JSON.stringify(params),
      caller,
      model: params?.model || undefined
    });

    const MAX_RESPONSE_SIZE = 1024 * 1024; // 1MB
    const TIMEOUT_MS = 60000; // 60 seconds
    const abortController = new AbortController();
    const logger = this.logger;
    const timeoutId = setTimeout(() => {
      logger.warn(`OpenAI stream timed out after ${TIMEOUT_MS}ms`);
      abortController.abort();
    }, TIMEOUT_MS);

    let stream: any; // Use a more flexible type to avoid breaking changes
    try {
      stream = await this.client.beta.chat.completions.stream({
        ...params,
        stream: true,
        signal: abortController.signal
      });
    } catch (err) {
      await this.llmLogService.updateLlmLog(llmLog?.id, `[ERROR] ${String(err)}`);
      throw err;
    }

    let aggregatedResponse = "";
    stream.on("content.delta", ({ delta }) => {
      if (aggregatedResponse.length + delta.length > MAX_RESPONSE_SIZE) {
        logger.warn(`OpenAI stream response exceeded ${MAX_RESPONSE_SIZE} bytes, aborting.`);
        abortController.abort();
        return;
      }
      aggregatedResponse += delta;
    });

    stream.on("error", (err) => {
      logger.error(`OpenAI stream error: ${err}`);
      (async () => {
        try {
          const errorMsg = err instanceof Error ? err.message : String(err);
          await this.llmLogService.updateLlmLog(llmLog?.id, `[ERROR] ${errorMsg}`);
        } catch (logErr) {
          logger.error(`Failed to persist LLM log error: ${logErr}`);
        }
      })().catch((e) => logger.error(`Error in error-handler wrapper: ${e}`));
    });

    stream.on("abort", async () => {
      logger.warn("OpenAI stream was aborted.");
      await this.llmLogService.updateLlmLog(llmLog?.id, aggregatedResponse + " [STREAM ABORTED]");
      clearTimeout(timeoutId);
    });

    stream.on("content.done", async () => {
      await this.llmLogService.updateLlmLog(llmLog?.id, aggregatedResponse);
      clearTimeout(timeoutId);
    });

    return stream;
  }
}
