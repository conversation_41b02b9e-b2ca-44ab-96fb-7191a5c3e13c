import { Injectable, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { LlmLog } from "nest-modules";
import { Repository } from "typeorm";

@Injectable()
export class LlmLogService {
  constructor(
    @InjectRepository(LlmLog)
    private readonly llmLogRepository: Repository<LlmLog>
  ) {}
  private readonly logger = new Logger(LlmLogService.name);

  async createLlmLog({ request, caller, model }: { request: string; caller?: string; model?: string }) {
    try {
      return await this.llmLogRepository.save({
        request,
        caller,
        model
      });
    } catch (error) {
      this.logger.error(`Error creating LLM log: ${error.message}`);
      return null;
    }
  }

  async updateLlmLog(logId: number, response: string) {
    try {
      return await this.llmLogRepository.update(logId, { response });
    } catch (error) {
      this.logger.error(`Error updating LLM Log: ${error.message}`);
      return null;
    }
  }

  /**
   * Extracts all message.content text items from an array of messages or an OpenAI response object, preserving formatting.
   * Joins them with double newlines. Ignores roles and JSON structure.
   * @param input - Array of messages (with .content) or OpenAI API response object
   * @returns A single string with all content items joined by double newlines
   */
  static extractAllMessageContents(input: any[] | { choices?: any[] }): string {
    try {
      if (Array.isArray(input)) {
        return input
          .map((msg: any) => (msg?.content ? String(msg.content) : ""))
          .filter((content: string) => content.length > 0)
          .join("\n\n");
      }
      if (input?.choices && Array.isArray(input.choices)) {
        return input.choices
          .map((choice: any) => (choice?.message?.content ? String(choice.message.content) : ""))
          .filter((content: string) => content.length > 0)
          .join("\n\n");
      }
      return "";
    } catch {
      return "";
    }
  }

  /**
   * Returns the content of the last message from an array of messages or an OpenAI response object.
   * Returns an empty string if not found or on error.
   * @param input - Array of messages (with .content) or OpenAI API response object
   * @returns The content of the last message as a string, or empty string if not found
   */
  static getLastMessageContent(input: any[] | { choices?: any[] }): string {
    try {
      if (Array.isArray(input) && input.length > 0) {
        const last = input[input.length - 1];
        return last?.content ? String(last.content) : "";
      }
      if (
        !Array.isArray(input) &&
        input?.choices &&
        Array.isArray(input.choices) &&
        input.choices.length > 0
      ) {
        const last = input.choices[input.choices.length - 1];
        return last?.message?.content ? String(last.message.content) : "";
      }
      return "";
    } catch {
      return "";
    }
  }
}
