const { NestFactory } = require("@nestjs/core");
const { AppModule } = require("./dist/app.module");

async function testClassification() {
  const app = await NestFactory.createApplicationContext(AppModule);

  const emailIntentAnalysisService = app.get("EmailIntentAnalysisService");

  // Test cases
  const testCases = [
    {
      name: "GET_SHIPMENT_STATUS",
      task: "Hi, what is the current status of my shipment? Has it cleared customs yet? When will it be released?"
    },
    {
      name: "REQUEST_CAD_DOCUMENT",
      task: "Hello, can you please send me the CAD document for my shipment? I need the Customs Accounting Document for my records."
    },
    {
      name: "CREATE_SHIPMENT (should map to PROCESS_DOCUMENT)",
      task: "I need to create a new shipment clearance for container arriving tomorrow on vessel MSC MAYA. Please start the customs process."
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n=== Testing: ${testCase.name} ===`);
    console.log(`Task: ${testCase.task}`);

    try {
      const result = await emailIntentAnalysisService.classifyTaskIntent(testCase.task);
      console.log(`Classified as: ${result}`);
    } catch (error) {
      console.error(`Error: ${error.message}`);
    }
  }

  await app.close();
}

testClassification().catch(console.error);
