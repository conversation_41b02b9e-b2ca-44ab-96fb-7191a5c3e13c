{"name": "portal-api", "version": "0.0.1", "private": true, "scripts": {"build": "nest build", "start": "nest start --watch", "start:worker": "nest start --entryFile worker-main", "start:prod": "nest start", "start:repl": "nest start --watch --entryFile repl", "build:docs": "typedoc", "jest": "jest"}, "dependencies": {"@aws-sdk/client-ecs": "~3.826.0", "@aws-sdk/client-lambda": "~3.799.0", "@aws-sdk/client-s3": "~3.668.0", "@aws-sdk/client-ssm": "~3.826.0", "@aws-sdk/client-textract": "~3.699.0", "@aws-sdk/s3-request-presigner": "~3.669.0", "@llamaindex/cloud": "~4.0.12", "@nestjs/bullmq": "~10.2.3", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "~2.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "~4.1.1", "@nestjs/swagger": "^7.3.1", "@nestjs/typeorm": "^10.0.2", "archiver": "~7.0.1", "async-mutex": "~0.5.0", "axios": "~1.7.7", "big.js": "~6.2.2", "bullmq": "~5.30.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.6", "docusign-esign": "~8.0.1", "email-addresses": "~5.0.0", "express": "^4.19.2", "googleapis": "~144.0.0", "jexl": "~2.3.0", "jsdom": "~25.0.1", "llamaindex": "~0.11.4", "mime-types": "~2.1.35", "moment-timezone": "~0.5.46", "moment": "~2.30.1", "nest-modules": "workspace:^", "nestjs-cls": "~6.0.0", "nestjs-spelunker": "~1.3.2", "nunjucks": "~3.2.4", "openai": "~4.82.0", "p-limit": "~6.2.0", "pg": "^8.11.5", "reflect-metadata": "^0.1.13", "remove-markdown": "~0.6.2", "rxjs": "^7.8.1", "sharp": "~0.33.5", "tiktoken": "~1.0.20", "typeorm": "^0.3.20", "uuid": "~11.0.3", "wink-bm25-text-search": "~3.1.2", "wink-eng-lite-web-model": "~1.8.1", "wink-nlp-utils": "~2.1.0", "wink-nlp": "~2.3.2", "zod-to-json-schema": "^3.24.5", "zod": "~3.24.2", "zxing-wasm": "~2.1.0", "@nestjs/cache-manager": "~3.0.1", "cache-manager": "~7.0.0", "@keyv/redis": "~4.4.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/cookie-parser": "^1.4.7", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3", "@types/docusign-esign": "~5.19.6", "@types/multer": "~1.4.12", "@aws-sdk/types": "~3.664.0", "@types/jsdom": "~21.1.7", "@smithy/types": "~3.5.0", "@types/big.js": "~6.2.2", "@types/nunjucks": "~3.2.6", "@types/archiver": "~6.0.3", "typedoc": "~0.28.5", "typedoc-theme-hierarchy": "^6.0.0", "pg-transactional-tests": "~1.2.0"}}