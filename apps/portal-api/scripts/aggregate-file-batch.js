const { AppModule } = require("../dist/app.module");
const { ContextIdFactory, NestFactory } = require("@nestjs/core");
const { getRepositoryToken } = require("@nestjs/typeorm");
const { FileBatch, Document } = require("nest-modules");
const { EventEmitter2 } = require("@nestjs/event-emitter");

async function bootstrap() {
  // check args
  if (process.argv.length < 3) {
    console.log("Usage: node scripts/list-file-batch-documents.js <organizationId> <limit> <skip>");
    process.exit(1);
  }

  const organizationId = parseInt(process.argv[2]);
  const limit = parseInt(process.argv[3] ?? 10);
  const skip = parseInt(process.argv[4] ?? 0);

  const contextId = ContextIdFactory.create();

  const app = await NestFactory.createApplicationContext(AppModule, {
    logger: ["error"]
  });

  console.log(`Organization ID: ${organizationId}`);

  // inject request context, so we can use the organizationId
  app.registerRequestByContextId(
    {
      user: {
        permission: "backoffice-admin",
        organization: {
          id: organizationId
        }
      }
    },
    contextId
  );

  const fileBatchRepository = await app.get(getRepositoryToken(FileBatch));
  const eventEmitter = await app.get(EventEmitter2);
  const documentRepository = await app.get(getRepositoryToken(Document));

  const fileBatches = await fileBatchRepository.find({
    where: {
      organizationId: organizationId
    },
    relations: {
      files: {
        documents: true
      }
    },
    order: {
      createDate: "DESC"
    },
    skip: skip,
    take: limit
  });

  // Query for file batches with associated documents
  console.log(`Found ${fileBatches.length} file batches`);

  // Display file batches and their documents
  for (const fileBatch of fileBatches) {
    // flatten documents in fileBatch.files
    const documents = fileBatch.files.flatMap((file) => file.documents);

    console.log(`${fileBatch.id}, ${documents.length} document(s)`);

    for (const document of documents) {
      console.log(`${document.id}\t${document.name}`);

      // set document status to "extracted"
      document.status = "extracted";
      document.aggregation = null;
      await documentRepository.save(document);
    }

    console.log();

    // dispatch event to aggregate documents
    eventEmitter.emit("file-batch.documentExtracted", {
      batchId: fileBatch.id,
      documentIds: documents.map((d) => d.id),
      audience: "api",
      organizationId: organizationId
    });

    // await for receive another event
    try {
      const result = await new Promise((resolve, reject) => {
        console.log("waiting for shipment created event...");

        const promises = [
          eventEmitter
            .waitFor("batch.shipmentCreated", {
              timeout: 10_000,
              filter: (event) => {
                return event.batchId === fileBatch.id;
              }
            })
            .then(() => {
              return "shipmentCreated";
            }),
          eventEmitter
            .waitFor("batch.readyForAggregation", {
              timeout: 10_000,
              filter: (event) => {
                return event.batchId === fileBatch.id;
              }
            })
            .then(() => {
              return "readyForAggregation";
            }),
          eventEmitter
            .waitFor("batch.shipmentCreationFailed", {
              timeout: 10_000,
              filter: (event) => {
                return event.batchId === fileBatch.id;
              }
            })
            .then(() => {
              return "shipmentCreationFailed";
            })
        ];

        Promise.any(promises)
          .then((promise) => {
            console.log(`${promise} event received`);
            resolve();
          })
          .catch((error) => {
            console.log("Timeout waiting for event");
            resolve();
          });

        // clean up event listeners
        return () => {
          promises.forEach((promise) => {
            promise.cancel();
          });
        };
      });

      console.log(JSON.stringify(result, null, 2));
    } catch (error) {
      console.error(error);
    }

    await new Promise((resolve) => setTimeout(resolve, 1_000));
  }

  console.log("Waiting app to close...");

  await app.close();
  process.exit(0);
}

bootstrap();
