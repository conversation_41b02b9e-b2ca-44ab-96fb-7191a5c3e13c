const { AppModule } = require("../dist/app.module");
const { ContextIdFactory, NestFactory } = require("@nestjs/core");
const { ShipmentService } = require("../dist/shipment/services/shipment.service");
const { Shipment } = require("nest-modules");
const { getRepositoryToken } = require("@nestjs/typeorm");
async function bootstrap() {
  // check args
  if (process.argv.length < 3) {
    console.log("Usage: node scripts/clear-shipment.js <organizationId>");
    process.exit(1);
  }

  const organizationId = process.argv[2];

  const contextId = ContextIdFactory.create();
  const app = await NestFactory.createApplicationContext(AppModule);

  // inject request context, so we can use the organizationId
  app.registerRequestByContextId(
    {
      user: {
        permission: "backoffice-admin",
        organization: {
          id: organizationId
        }
      }
    },
    contextId
  );

  const shipmentService = await app.resolve(ShipmentService, contextId);

  const shipmentRepository = await app.get(getRepositoryToken(Shipment));

  // get all shipments
  const shipments = await shipmentRepository.find({
    where: {
      organization: {
        id: organizationId
      }
    }
  });

  console.log(`Clearing ${shipments.length} shipments...`);

  // delete all shipments
  for (const shipment of shipments) {
    try {
      await shipmentService.deleteShipment(shipment.id);
      console.log(`Deleted shipment ID: ${shipment.id}`);
    } catch (error) {
      console.error(`Error deleting shipment ID: ${shipment.id}:`, error.message);
    }
  }

  console.log("Done");

  await app.close();
}

bootstrap();
