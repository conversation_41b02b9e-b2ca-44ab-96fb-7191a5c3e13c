const { AppModule } = require("../dist/app.module");
const { ContextIdFactory, NestFactory } = require("@nestjs/core");
const { ProductService } = require("../dist/product/product.service");
const { Product } = require("nest-modules");
const { getRepositoryToken } = require("@nestjs/typeorm");

async function bootstrap() {
  // check args
  if (process.argv.length < 3) {
    console.log("Usage: node scripts/clear-product.js <organizationId>");
    process.exit(1);
  }

  const organizationId = parseInt(process.argv[2]);

  const contextId = ContextIdFactory.create();
  const app = await NestFactory.createApplicationContext(AppModule);

  console.log(`Organization ID: ${organizationId}`);

  // inject request context, so we can use the organizationId
  app.registerRequestByContextId(
    {
      user: {
        permission: "backoffice-admin",
        organization: {
          id: organizationId
        }
      }
    },
    contextId
  );

  const productService = await app.resolve(ProductService, contextId);

  const productRepository = await app.get(getRepositoryToken(Product));

  // get all products
  const products = await productRepository.find({
    where: {
      organization: {
        id: organizationId
      }
    }
  });

  console.log(`Clearing ${products.length} products...`);

  // delete all products
  for (const product of products) {
    try {
      await productService.deleteProduct(product.id);
      console.log(`Deleted product ID: ${product.id}`);
    } catch (error) {
      console.error(`Error deleting product ID: ${product.id}:`, error.message);
    }
  }

  console.log("Done");

  await app.close();
}

bootstrap();
