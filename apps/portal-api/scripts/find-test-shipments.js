#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to find ideal shipments for core-agent email response testing scenarios
 * This script queries the database to find shipments that match the required attributes
 * for each testing scenario in the email intent mapping table.
 */

const { NestFactory } = require("@nestjs/core");
const { AppModule } = require("../dist/app.module");
const { DataSource } = require("typeorm");

async function findTestShipments() {
  console.log("🔍 Finding ideal shipments for core-agent email response testing...\n");

  const app = await NestFactory.createApplicationContext(AppModule);

  try {
    const dataSource = await app.get(DataSource);

    // Define the scenarios we need to find shipments for
    const scenarios = [
      {
        name: "Status: Pending Commercial Invoice",
        customsStatus: "pending-commercial-invoice",
        attributes: ["cargoControlNumber", "hblNumber", "containers"]
      },
      {
        name: "Status: Pending Confirmation",
        customsStatus: "pending-confirmation",
        attributes: ["cargoControlNumber", "hblNumber", "weight"]
      },
      {
        name: "Status: Pending Arrival",
        customsStatus: "pending-arrival",
        attributes: ["cargoControlNumber", "hblNumber", "etaPort"]
      },
      {
        name: "Status: Live",
        customsStatus: "live",
        attributes: ["cargoControlNumber", "hblNumber"]
      },
      {
        name: "Status: Entry Submitted",
        customsStatus: "entry-submitted",
        attributes: ["cargoControlNumber", "hblNumber"]
      },
      {
        name: "Status: Entry Accepted",
        customsStatus: "entry-accepted",
        attributes: ["cargoControlNumber", "hblNumber"]
      },
      {
        name: "Status: Exam",
        customsStatus: "exam",
        attributes: ["cargoControlNumber", "hblNumber", "etaPort"]
      },
      {
        name: "Status: Released",
        customsStatus: "released",
        attributes: ["cargoControlNumber", "hblNumber", "releaseDate"]
      }
    ];

    console.log("📊 Testing Scenarios and Ideal Shipments:\n");
    console.log(
      "| Scenario | Customer Question | Shipment ID | CCN | HBL | Container | Additional Attributes |"
    );
    console.log(
      "|----------|-------------------|-------------|-----|-----|-----------|----------------------|"
    );

    for (const scenario of scenarios) {
      try {
        // Query for shipments matching this scenario
        const query = `
          SELECT 
            s.id,
            s."cargoControlNumber",
            s."hblNumber", 
            s."customsStatus",
            s.weight,
            s."etaPort",
            s."releaseDate",
            s."modeOfTransport",
            c."containerNumber"
          FROM shipment s
          LEFT JOIN container c ON c."shipmentId" = s.id
          WHERE s."customsStatus" = $1
            AND s."cargoControlNumber" IS NOT NULL
            AND s."hblNumber" IS NOT NULL
            AND s."deletedAt" IS NULL
          ORDER BY s."updatedAt" DESC
          LIMIT 3
        `;

        const shipments = await dataSource.query(query, [scenario.customsStatus]);

        if (shipments.length > 0) {
          const shipment = shipments[0]; // Take the first (most recent) match

          // Build additional attributes string
          let additionalAttrs = [];
          if (scenario.customsStatus === "pending-confirmation" && !shipment.weight) {
            additionalAttrs.push("missing weight ✓");
          }
          if (scenario.customsStatus === "pending-arrival" && shipment.etaPort) {
            additionalAttrs.push(`ETA: ${shipment.etaPort.toISOString().split("T")[0]}`);
          }
          if (scenario.customsStatus === "exam" && shipment.etaPort) {
            additionalAttrs.push(`ETA: ${shipment.etaPort.toISOString().split("T")[0]}`);
          }
          if (scenario.customsStatus === "released" && shipment.releaseDate) {
            additionalAttrs.push(`Released: ${shipment.releaseDate.toISOString().split("T")[0]}`);
          }
          if (shipment.modeOfTransport) {
            additionalAttrs.push(`Mode: ${shipment.modeOfTransport}`);
          }

          const customerQuestion = "What's the status of my shipment?";

          console.log(
            `| ${scenario.name} | ${customerQuestion} | ${shipment.id} | ${shipment.cargoControlNumber || "N/A"} | ${shipment.hblNumber || "N/A"} | ${shipment.containerNumber || "N/A"} | ${additionalAttrs.join(", ") || "N/A"} |`
          );
        } else {
          console.log(
            `| ${scenario.name} | What's the status of my shipment? | NO MATCH | - | - | - | Need shipment with ${scenario.customsStatus} status |`
          );
        }
      } catch (error) {
        console.log(
          `| ${scenario.name} | What's the status of my shipment? | ERROR | - | - | - | ${error.message} |`
        );
      }
    }

    // Now find shipments for different transport modes for document scenarios
    console.log("\n\n📋 Document Request Scenarios:\n");
    console.log("| Scenario | Customer Question | Shipment ID | CCN | HBL | Mode | Status |");
    console.log("|----------|-------------------|-------------|-----|-----|------|--------|");

    const transportModes = ["ocean-fcl", "ocean-lcl", "air", "land"];
    const docStatuses = ["pending-arrival", "live", "entry-submitted", "entry-accepted", "exam", "released"];

    for (const mode of transportModes) {
      for (const status of docStatuses) {
        try {
          const query = `
            SELECT 
              s.id,
              s."cargoControlNumber",
              s."hblNumber", 
              s."customsStatus",
              s."modeOfTransport"
            FROM shipment s
            WHERE s."customsStatus" = $1
              AND s."modeOfTransport" = $2
              AND s."cargoControlNumber" IS NOT NULL
              AND s."hblNumber" IS NOT NULL
              AND s."deletedAt" IS NULL
            ORDER BY s."updatedAt" DESC
            LIMIT 1
          `;

          const shipments = await dataSource.query(query, [status, mode]);

          if (shipments.length > 0) {
            const shipment = shipments[0];
            const docType =
              status === "released"
                ? "RNS Proof of Release"
                : ["entry-accepted", "exam"].includes(status)
                  ? "RNS"
                  : "CAD";

            console.log(
              `| ${docType} - ${mode} - ${status} | Can you send me the ${docType}? | ${shipment.id} | ${shipment.cargoControlNumber} | ${shipment.hblNumber} | ${shipment.modeOfTransport} | ${shipment.customsStatus} |`
            );
            break; // Only show one example per mode
          }
        } catch (error) {
          // Skip errors for this detailed search
        }
      }
    }

    // Find shipments for rush scenarios (same as status but with urgent flag concept)
    console.log("\n\n⚡ Rush Request Scenarios (use same shipments as status scenarios):\n");
    console.log("| Scenario | Customer Question | Shipment ID | Notes |");
    console.log("|----------|-------------------|-------------|-------|");
    console.log(
      "| Rush - Any Status | This is urgent, please expedite my shipment | Use same IDs as status scenarios | Add urgent=true flag in testing |"
    );

    // Find shipments for cancellation
    console.log("\n\n❌ Cancellation Scenarios:\n");
    console.log("| Scenario | Customer Question | Shipment ID | CCN | HBL | Status |");
    console.log("|----------|-------------------|-------------|-----|-----|--------|");

    try {
      const query = `
        SELECT 
          s.id,
          s."cargoControlNumber",
          s."hblNumber", 
          s."customsStatus"
        FROM shipment s
        WHERE s."customsStatus" IN ('pending-arrival', 'live', 'entry-submitted')
          AND s."cargoControlNumber" IS NOT NULL
          AND s."hblNumber" IS NOT NULL
          AND s."deletedAt" IS NULL
        ORDER BY s."updatedAt" DESC
        LIMIT 3
      `;

      const shipments = await dataSource.query(query);

      if (shipments.length > 0) {
        shipments.forEach((shipment, index) => {
          if (index < 1) {
            // Just show one example
            console.log(
              `| Cancel Shipment | Please cancel my shipment | ${shipment.id} | ${shipment.cargoControlNumber} | ${shipment.hblNumber} | ${shipment.customsStatus} |`
            );
          }
        });
      }
    } catch (error) {
      console.log(`| Cancel Shipment | Please cancel my shipment | ERROR | - | - | ${error.message} |`);
    }

    console.log("\n✅ Search completed! Use these shipment IDs in your core-agent email response tests.\n");
    console.log("💡 Tips:");
    console.log("- Test with the exact Customer Questions shown above");
    console.log("- Use the Shipment IDs in your test scenarios");
    console.log("- For Rush scenarios, use the same shipment IDs as the corresponding status scenarios");
    console.log("- For missing documents scenarios, you may need to temporarily modify test data");
    console.log("- Verify shipment details in the portal before running tests\n");
  } catch (error) {
    console.error("❌ Error finding test shipments:", error);
  } finally {
    await app.close();
  }
}

// Run the script
findTestShipments().catch(console.error);
