const { AppModule } = require("../dist/app.module");
const { ContextIdFactory, NestFactory } = require("@nestjs/core");
const { TradePartnerService } = require("../dist/trade-partner/trade-partner.service");
const { TradePartner } = require("nest-modules");
const { getRepositoryToken } = require("@nestjs/typeorm");

async function bootstrap() {
  // check args
  if (process.argv.length < 3) {
    console.log("Usage: node scripts/clear-product.js <organizationId>");
    process.exit(1);
  }

  const organizationId = parseInt(process.argv[2]);

  const contextId = ContextIdFactory.create();
  const app = await NestFactory.createApplicationContext(AppModule);

  console.log(`Organization ID: ${organizationId}`);

  // inject request context, so we can use the organizationId
  app.registerRequestByContextId(
    {
      user: {
        permission: "backoffice-admin",
        organization: {
          id: organizationId
        }
      }
    },
    contextId
  );

  const tradePartnerService = await app.resolve(TradePartnerService, contextId);

  const tradePartnerRepository = await app.get(getRepositoryToken(TradePartner));

  // get all trade partners
  const tradePartners = await tradePartnerRepository.find({
    where: {
      organization: {
        id: organizationId
      }
    }
  });

  console.log(`Clearing ${tradePartners.length} trade partners...`);

  // delete all trade partners
  for (const tradePartner of tradePartners) {
    try {
      await tradePartnerService.deleteTradePartner(tradePartner.id);
      console.log(`Deleted trade partner ID: ${tradePartner.id}`);
    } catch (error) {
      console.error(`Error deleting trade partner ID: ${tradePartner.id}:`, error.message);
    }
  }

  console.log("Done");

  await app.close();
}

bootstrap();
