# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=
DB_USER=
DB_PASSWORD=
DB_SSL_ENABLED=

# Redis
REDIS_HOST=localhost
REDIS_PORT=6380

# JWT
ACCESS_TOKEN_SECRET=
REFRESH_TOKEN_SECRET=
ACCESS_TOKEN_EXPIRES_IN_SEC=3600
REFRESH_TOKEN_EXPIRES_IN_SEC=31536000
REFRESH_TOKEN_GRACE_PERIOD_SEC=10

# Google OAuth
GOOGLE_OAUTH_CLIENT_ID=
GOOGLE_OAUTH_CLIENT_SECRET=

# Docusign
DOCUSIGN_SECRET_KEY=
DOCUSIGN_INTEGRATION_KEY=  
OAUTH_STATE_SECRET_KEY=
DOCUSIGN_POA_TEMPLATE_ID=
DOCUSIGN_US_POA_TEMPLATE_ID=
DOCUSIGN_BASE_URL=

# Tika
TIKA_ENDPOINT_URL=http://localhost:9998

# AWS S3
AWS_S3_BUCKET=claro-document-storage-dev
# AWS_S3_REGION=ca-central-1
# AWS Region
AWS_REGION=ca-central-1

# Llama Cloud
LLAMA_CLOUD_API_KEY=

# OpenAI
OPENAI_API_KEY=

# DeepSeek
DEEPSEEK_API_KEY=

# Candata
USAV_CANDATA_BASE_URL=
USAV_CANDATA_TOKEN=
CLARO_CANDATA_BASE_URL=
CLARO_CANDATA_AUTH_URL=
CLARO_CANDATA_CLIENT_ID=
CLARO_CANDATA_CLIENT_SECRET=
CANDATA_REMOTE_SERVER_APP_ID=

# APIFY
APIFY_TOKEN=
APIFY_US_TARIFF_STORE_NAME=
APIFY_US_TARIFF_ACTOR_NAME=
APIFY_US_TARIFF_ACTOR_ID=

# BullMQ (Note: these needs to be set as environment variables manually)
DOCUMENT_PROCESSOR_CONCURRENCY=12
FILE_PROCESSOR_CONCURRENCY=12
AGGREGATION_PROCESSOR_CONCURRENCY=6

# API Template
API_TEMPLATE_API_KEY=

# Firebase
FIREBASE_PROJECT_ID=
FIREBASE_CLIENT_EMAIL=
FIREBASE_PRIVATE_KEY=

# Carm
CARM_APP_ID=

# Email
BACKOFFICE_EMAIL_ADDRESS=
SYSTEM_FROM_EMAIL=

# Front-end URL
PORTAL_FRONTEND_URL=

# Local Dev feature flags
DISABLE_INCOMING_EMAIL_SYNC=false
DISABLE_SEND_EMAIL_ALL=false
DISABLE_SEND_CUSTOMS_STATUS_EMAIL=false
DISABLE_SEND_BACK_OFFICE_EMAIL=false
DISABLE_SEND_REPLY_EMAIL=false
DISABLE_CUSTOMS_STATUS_CRON_JOB_LOCALLY=false

# Feature flag for worker instance
FEATURE=
