import { EnvelopeDefinition, TemplateRole } from "docusign-esign";
import { logger } from "firebase-functions/v2";
import { DOCUSIGN_ACCOUNT_ID, DOCUSIGN_POA_TEMPLATE_ID } from "./constant";
import { getEnvelopesApi } from "./service";
import { EmailParams } from "./types";

export const sendPoaEmail = async (params: EmailParams) => {
  const envelopesApi = await getEnvelopesApi();
  // TODO: Use the email subject and blurb from the docusign service
  const emailSubject = `Signature Required: Importer ${params.companyName} POA`;
  const emailBlurb = [
    `Hi ${params.officerNameAndTitle},`,
    "",
    `Please sign the following Power of Attorney for the company ${params.companyName}.`,
    "",
    "Thanks,",
    "Claro Customs"
  ].join("\n");

  const templateRole: TemplateRole = {
    email: params.email,
    name: params.officerNameAndTitle,
    roleName: "Signer",
    tabs: {
      textTabs: [
        {
          tabLabel: "company_name",
          value: params.companyName
        },
        {
          tabLabel: "business_number",
          value: params.businessNumber
        },
        {
          tabLabel: "company_address",
          value: `${params.address},\n${params.city}, ${params.state} ${params.postalCode},\n${params.countryName}`
        },
        {
          tabLabel: "phone_number",
          value: params.phoneNumber
        },
        {
          tabLabel: "email",
          value: params.email
        },
        {
          tabLabel: "officer",
          value: params.officerNameAndTitle
        },
        {
          tabLabel: "place",
          value: `${params.city}, ${params.state} ${params.postalCode}, ${params.countryName}`
        },
        {
          tabLabel: "fax",
          value: params.fax || ""
        }
      ],
      signHereTabs: [
        {
          tabLabel: "sign_poa"
        },
        {
          tabLabel: "sign_gst"
        }
      ],
      dateSignedTabs: [
        {
          tabLabel: "date_signed"
        }
      ]
    }
  };

  const envelope: EnvelopeDefinition = {
    emailSubject,
    emailBlurb,
    templateId: DOCUSIGN_POA_TEMPLATE_ID.value(),
    templateRoles: [templateRole],
    status: "sent"
  };

  try {
    const envelopeRes = await envelopesApi.createEnvelope(DOCUSIGN_ACCOUNT_ID.value(), {
      envelopeDefinition: envelope
    });

    return envelopeRes.envelopeId;
  } catch (error) {
    logger.error("createEnvelope", error);
    throw new Error(String(error));
  }
};

export const resendPoaEmail = async (envelopeId: string, importer?: EmailParams) => {
  const envelopesApi = await getEnvelopesApi();

  try {
    if (!importer) {
      const envelopeRes = await envelopesApi.update(DOCUSIGN_ACCOUNT_ID.value(), envelopeId, {
        resendEnvelope: "true"
      });
      return envelopeRes.envelopeId;
    } else {
      // Change email address
      // void previous envelope
      await envelopesApi.update(DOCUSIGN_ACCOUNT_ID.value(), envelopeId, {
        status: "voided",
        voidedReason: "Changed email address"
      });

      // Send new envelope
      return sendPoaEmail(importer);
    }
  } catch (error) {
    logger.error("resendEnvelope", error);
    throw new Error(String(error));
  }
};
