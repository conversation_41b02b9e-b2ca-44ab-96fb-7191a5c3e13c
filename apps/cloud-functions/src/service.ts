import docusign, { ApiClient, Envelopes<PERSON>pi } from "docusign-esign";
import { logger } from "firebase-functions/v2";
import fs from "fs";
import {
  DOCUSIGN_ACCOUNT_BASE_URL,
  DOCUSIGN_API_BASE_URL,
  DOCUSIGN_INTEGRATION_KEY,
  DOCUSIGN_RSA_KEY,
  DOCUSIGN_USER_ID
} from "./constant";
import { DocusignAccessTokenError } from "./types";

const expiresIn = 600; // 10 minutes
let cachedToken: string;
let tokenExpiry = 0;

/**
 * Get Access Token.
 *
 * @returns
 */
async function getAccessToken(): Promise<string> {
  const now = Date.now();
  if (cachedToken && now < tokenExpiry) {
    return cachedToken;
  }

  const rsaKey = fs.readFileSync(DOCUSIGN_RSA_KEY.value());
  const apiClient = new docusign.ApiClient();
  apiClient.setOAuthBasePath(DOCUSIGN_ACCOUNT_BASE_URL.value().replace("https://", ""));
  apiClient.setBasePath(DOCUSIGN_API_BASE_URL.value());

  try {
    const requestToken = await apiClient.requestJWTUserToken(
      DOCUSIGN_INTEGRATION_KEY.value(),
      DOCUSIGN_USER_ID.value(),
      ["signature", "impersonation"],
      rsaKey,
      expiresIn
    );

    cachedToken = requestToken.body.access_token;
    tokenExpiry = now + (requestToken.body.expires_in - 60) * 1000;
    return cachedToken;
  } catch (error) {
    if (error && typeof error === "object" && "response" in error) {
      const apiError = error as DocusignAccessTokenError;
      const body = apiError.response?.body || apiError.response?.data;
      if (body) {
        const errorMessage = `\nGet access token error code: ${apiError.response.status}, message: ${
          body?.error
        }. ${body?.error_description ?? ""}
        `;
        logger.error(errorMessage);
        throw new Error(errorMessage);
      }
    }
    logger.error("getAccessToken", error);
    throw new Error(String(error));
  }
}
/**
 * Get Envelopes API.
 *
 * @param accessToken
 * @returns
 */
async function getEnvelopesApi(): Promise<EnvelopesApi> {
  const accessToken = await getAccessToken();
  const apiClient = new ApiClient();
  apiClient.setBasePath(DOCUSIGN_API_BASE_URL.value());
  apiClient.addDefaultHeader("Authorization", `Bearer ${accessToken}`);

  return new EnvelopesApi(apiClient);
}

export { getEnvelopesApi };
