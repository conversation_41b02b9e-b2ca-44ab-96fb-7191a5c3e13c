export type DocusigTokenParams = {
  grant_type: string;
  client_id: string;
  client_secret: string;
  username: string;
  password: string;
};
export type DocusignTokenResponse = {
  access_token: string;
  token_type: string;
  expires_in: number;
};
export type EmailParams = {
  address: string;
  businessNumber: string;
  city: string;
  companyName: string;
  email: string;
  fax?: string;
  officerNameAndTitle: string;
  phoneNumber: string;
  postalCode: string;
  state: string;
  countryName?: string;
};

export interface DocusignAccessTokenError {
  response: {
    status: number;
    body?: { error?: string; error_description?: string };
    data?: { error?: string; error_description?: string };
  };
}
