import { logger, setGlobalOptions } from "firebase-functions/v2";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import { resendPoaEmail, sendPoaEmail } from "./docusign";

setGlobalOptions({ region: "northamerica-northeast2" });

export const sendPoa = onCall(
  // {
  //   enforceAppCheck: true // Reject requests with missing or invalid App Check tokens.
  //   // consumeAppCheckToken: true // Consume the token after verification.
  // },
  async ({ data }) => {
    try {
      const envelopeId = await sendPoaEmail(data);
      return { envelopeId };
    } catch (error: unknown) {
      logger.error("Send POA error:", error);

      throw new HttpsError("unknown", (error as Error).message);
    }
  }
);

export const resendPoa = onCall(
  // {
  //   enforceAppCheck: true
  //   // consumeAppCheckToken: true
  // },
  async ({ data }) => {
    if (!data.envelopeId) {
      throw new HttpsError("invalid-argument", "Envelope ID is required");
    }

    try {
      const envelopeId = await resendPoaEmail(data.envelopeId, data.importer);
      return { envelopeId };
    } catch (error: unknown) {
      logger.error("Resend POA error:", error);

      throw new HttpsError("unknown", (error as Error).message);
    }
  }
);
