{"name": "cloud-functions", "private": true, "version": "0.1.0", "description": "Firebase cloud functions", "main": "lib/index.js", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "keywords": [], "author": "danny-aistribute <<EMAIL>>", "license": "ISC", "dependencies": {"docusign-esign": "^8.0.1", "firebase-admin": "~13.2.0", "firebase-functions": "~6.3.2"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "typescript": "^4.9.0", "@types/docusign-esign": "^5.19.8"}}