// TODO: move to shared module
import { RegisterFlowProducerOptions, RegisterQueueOptions } from "@nestjs/bullmq";

export enum DocumentQueue {
  DOCUMENT_TASKS = "document-tasks",
  FILE_TASKS = "file-tasks",
  FILE_BATCH_TASKS = "file-batch-tasks",
  FILE_OVERLAY_TASKS = "file-overlay-tasks"
}

export enum FlowProducers {
  EXTRACT_DOCUMENTS = "extract-documents"
}

export const DEFAULT_REMOVE_ON_COMPLETE = {
  removeOnComplete: {
    age: 3600,
    count: 1000
  },
  removeOnFail: {
    age: 24 * 3600
  }
};

export const FLOW_PRODUCERS: RegisterFlowProducerOptions[] = [
  {
    name: FlowProducers.EXTRACT_DOCUMENTS
  }
];

export const QUEUES: RegisterQueueOptions[] = [
  {
    name: DocumentQueue.FILE_TASKS,
    defaultJobOptions: DEFAULT_REMOVE_ON_COMPLETE
  },
  {
    name: DocumentQueue.DOCUMENT_TASKS,
    defaultJobOptions: DEFAULT_REMOVE_ON_COMPLETE
  },
  {
    name: DocumentQueue.FILE_BATCH_TASKS,
    defaultJobOptions: DEFAULT_REMOVE_ON_COMPLETE
  },
  {
    name: DocumentQueue.FILE_OVERLAY_TASKS,
    defaultJobOptions: DEFAULT_REMOVE_ON_COMPLETE
  }
];
