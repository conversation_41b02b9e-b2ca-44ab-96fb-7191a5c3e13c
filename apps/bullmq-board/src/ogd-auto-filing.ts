import { RegisterQueueOptions } from "@nestjs/bullmq";

export enum OgdFilingQueueName {
  AUTO_OGD_FILING = "auto-ogd-filing"
}

export const DEFAULT_JOB_OPTIONS = {
  attempts: 1,
  removeOnComplete: {
    age: 3600,
    count: 1000
  },
  removeOnFail: {
    age: 24 * 3600
  }
};

export const OGD_FILING_QUEUES: Array<RegisterQueueOptions> = [
  {
    name: OgdFilingQueueName.AUTO_OGD_FILING,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  }
];
