import { RegisterQueueOptions } from "@nestjs/bullmq";

export enum CoreAgentQueueName {
  EMAIL_SAVED = "core-agent-email-saved"
}

export const DEFAULT_JOB_OPTIONS = {
  attempts: 1,
  removeOnComplete: {
    age: 3600,
    count: 1000
  },
  removeOnFail: {
    age: 24 * 3600
  }
};

export const CORE_AGENT_QUEUES: Array<RegisterQueueOptions> = [
  {
    name: CoreAgentQueueName.EMAIL_SAVED,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  }
];
