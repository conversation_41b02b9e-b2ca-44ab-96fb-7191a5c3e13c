import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { ExpressAdapter } from "@bull-board/express";
import { BullBoardModule } from "@bull-board/nestjs";
import { BullModule } from "@nestjs/bullmq";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { QUEUES as AGGREGATION_QUEUES } from "./aggregation";
import { CORE_AGENT_QUEUES } from "./core-agent";
import { CRON_QUEUE_OPTIONS } from "./cron";
import { QUEUES } from "./document";
import { EMAIL_QUEUES } from "./email";
import { OGD_FILING_QUEUES } from "./ogd-auto-filing";
import { SIMA_FILING_QUEUES } from "./sima-auto-filing";

export const NODE_ENV = process.env.NODE_ENV || "local";
@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: `.env.${NODE_ENV}`,
      isGlobal: true
    }),
    BullModule.forRoot({
      connection: {
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT ? parseInt(process.env.REDIS_PORT) : 6380
      }
    }),
    BullBoardModule.forRoot({
      route: "/",
      adapter: ExpressAdapter
    }),
    BullModule.registerQueue(
      ...[
        ...QUEUES,
        ...AGGREGATION_QUEUES,
        ...CORE_AGENT_QUEUES,
        ...EMAIL_QUEUES,
        ...OGD_FILING_QUEUES,
        ...SIMA_FILING_QUEUES,
        ...CRON_QUEUE_OPTIONS
      ]
    ),
    BullBoardModule.forFeature(
      ...[
        ...QUEUES.map((queue) => ({
          name: queue.name,
          adapter: BullMQAdapter
        })),
        ...AGGREGATION_QUEUES.map((queue) => ({
          name: queue.name,
          adapter: BullMQAdapter
        })),
        ...CORE_AGENT_QUEUES.map((queue) => ({
          name: queue.name,
          adapter: BullMQAdapter
        })),
        ...EMAIL_QUEUES.map((queue) => ({
          name: queue.name,
          adapter: BullMQAdapter
        })),
        ...OGD_FILING_QUEUES.map((queue) => ({
          name: queue.name,
          adapter: BullMQAdapter
        })),
        ...SIMA_FILING_QUEUES.map((queue) => ({
          name: queue.name,
          adapter: BullMQAdapter
        })),
        ...CRON_QUEUE_OPTIONS.map((queue) => ({
          name: queue.name,
          adapter: BullMQAdapter
        }))
      ]
    )
  ]
})
export class AppModule {}
