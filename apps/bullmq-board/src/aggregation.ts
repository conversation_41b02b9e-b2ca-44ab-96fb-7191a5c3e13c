import { RegisterQueueOptions } from "@nestjs/bullmq";

export enum Queue {
  AGGREGATION_TASKS = "aggregation-tasks"
}

export const DEFAULT_REMOVE_ON_COMPLETE = {
  removeOnComplete: {
    age: 3600,
    count: 1000
  },
  removeOnFail: {
    age: 24 * 3600
  }
};

export const QUEUES: RegisterQueueOptions[] = [
  {
    name: Queue.AGGREGATION_TASKS,
    defaultJobOptions: DEFAULT_REMOVE_ON_COMPLETE
  }
];
