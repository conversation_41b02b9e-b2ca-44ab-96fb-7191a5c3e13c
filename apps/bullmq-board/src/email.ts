import { RegisterQueueOptions } from "@nestjs/bullmq";

export enum EmailQueueName {
  GET_GMAIL_MESSAGE = "get-gmail-message",
  FIND_THREAD_SHIPMENT = "find-thread-shipment",
  EXTRACT_USER_INTENTS = "extract-user-intents",
  PROCESS_USER_INTENTS = "process-user-intents",
  AGGREGATE_EMAIL = "aggregate-email",
  GENERATE_EMAIL_RESPONSE = "generate-email-response"
}

export const DEFAULT_JOB_OPTIONS = {
  attempts: 1,
  // backoff: {
  //   type: "exponential",
  //   delay: 1000,
  // },
  removeOnComplete: {
    age: 3600,
    count: 1000
  },
  removeOnFail: {
    age: 24 * 3600
  }
};

export const EMAIL_QUEUES: Array<RegisterQueueOptions> = [
  {
    name: EmailQueueName.GET_GMAIL_MESSAGE,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  },
  {
    name: EmailQueueName.FIND_THREAD_SHIPMENT,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  },
  {
    name: EmailQueueName.EXTRACT_USER_INTENTS,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  },
  {
    name: EmailQueueName.PROCESS_USER_INTENTS,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  },
  {
    name: EmailQueueName.AGGREGATE_EMAIL,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  },
  {
    name: EmailQueueName.GENERATE_EMAIL_RESPONSE,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  }
];
