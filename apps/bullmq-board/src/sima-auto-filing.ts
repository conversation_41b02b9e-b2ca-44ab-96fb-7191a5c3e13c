import { RegisterQueueOptions } from "@nestjs/bullmq";

export enum SimaFilingQueueName {
  AUTO_SIMA_FILING = "auto-sima-filing"
}

export const DEFAULT_JOB_OPTIONS = {
  attempts: 1,
  removeOnComplete: {
    age: 3600,
    count: 1000
  },
  removeOnFail: {
    age: 24 * 3600
  }
};

export const SIMA_FILING_QUEUES: Array<RegisterQueueOptions> = [
  {
    name: SimaFilingQueueName.AUTO_SIMA_FILING,
    defaultJobOptions: DEFAULT_JOB_OPTIONS
  }
];
