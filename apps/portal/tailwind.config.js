import forms from "@tailwindcss/forms";
import typography from "@tailwindcss/typography";
import preset from "tailwind";

/** @type {import('tailwindcss').Config} */
export default {
  presets: [preset],
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  // theme: {
  //   extend: {
  //     colors: {
  //       primary: "var(--color-primary)",
  //       secondary: 'var(--color-secondary)',
  //       Add other color variables as needed
  //     },
  //   },
  // },
  plugins: [forms, typography]
};
