import { NavObject } from "@/common/Types.common";
import { ChevronDown } from "lucide-react";
import { matchRoutes, useLocation } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import { Tooltip } from "ui";

type Props = {
  item: NavObject;
  isOpen?: boolean;
  isSubMenu?: boolean;
  isCollapsed: boolean;
  onClick(): void;
};
function NavItem({ item, isCollapsed, isOpen, isSubMenu, onClick }: Props) {
  const location = useLocation();
  const match = item.pattern ? matchRoutes(item.pattern, location.pathname) : undefined;

  return (
    <div
      className={twMerge(
        "flex gap-2 p-2 items-center rounded-lg font-medium text-neutral-700 hover:cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-700 dark:text-neutral-200",
        match && !item.submenu && "bg-neutral-100 dark:bg-neutral-800",
        isCollapsed && "block",
        isSubMenu && "pl-6",
        isCollapsed && isSubMenu && "pl-4"
      )}
      onClick={onClick}
    >
      <Tooltip text={isCollapsed ? item.label : ""} position="right" fixed>
        <div
          className={twMerge(match && !item.submenu && "text-primary dark:text-white dark:bg-neutral-800")}
        >
          {item.icon}
        </div>
      </Tooltip>
      {!isCollapsed && (
        <div className={twMerge(match && !item.submenu && "text-primary dark:text-white")}>{item.label}</div>
      )}
      {item.submenu && !isCollapsed && (
        <ChevronDown
          className={twMerge(
            "ml-auto size-4 transition-transform duration-200",
            isOpen && "transform rotate-180"
          )}
        />
      )}
    </div>
  );
}
export default NavItem;
