import { useStore } from "@/bootstrap/Store.bootstrap";
import { observer } from "mobx-react-lite";
import { PropsWithChildren, useReducer } from "react";
import Header from "./Header";
import Sidebar from "./Sidebar";

interface Props extends PropsWithChildren {
  importerOnboarded?: boolean;
  onLogout(): void;
}
const Layout = observer(({ children, importerOnboarded, onLogout }: Props) => {
  const { session, isSuperAdmin, updateOrganization, organization } = useStore().auth;
  const [isCollapsed, toggleCollapse] = useReducer((r) => !r, false);

  return (
    <div className="flex flex-col h-screen bg-background dark:bg-dark">
      <Header
        onLogout={onLogout}
        name={session?.name}
        organization={organization}
        importerOnboarded={importerOnboarded}
        isSuperAdmin={isSuperAdmin}
        onSelectOrganization={updateOrganization}
      />

      <div className="flex-1 flex overflow-hidden">
        {importerOnboarded && (
          <Sidebar
            onLogout={onLogout}
            name={session?.organization?.name}
            isCollapsed={isCollapsed}
            onToggleCollapse={toggleCollapse}
          />
        )}

        <main className="flex-1 flex flex-col overflow-y-auto overscroll-contain">{children}</main>
      </div>
    </div>
  );
});
export default Layout;
