import { SelectOrganizationModal } from "@/modules/Auth/components";
import { Organization } from "@/modules/Auth/Types.auth";
import { SearchBox } from "@/modules/Search/components";
import { SettingsPages } from "@/modules/Settings/Routes.settings";
import { Settings, UserRoundCog } from "lucide-react";
import { observer } from "mobx-react-lite";
import { PropsWithChildren, useReducer, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Dropdown, Icons, Input } from "ui";
import { getNameInitials } from "utils";

interface ItemProps extends PropsWithChildren {
  onClick(): void;
}
const HeaderItem = ({ onClick, children }: ItemProps) => (
  <div
    className="flex w-full items-center gap-x-3 py-2 p-3 rounded-lg text-sm cursor-pointer text-neutral-800 hover:bg-neutral-100 dark:text-neutral-200 dark:hover:bg-neutral-700"
    onClick={onClick}
  >
    {children}
  </div>
);
interface Props {
  name?: string;
  importerOnboarded?: boolean;
  isSuperAdmin?: boolean;
  organization?: Organization;
  onSelectOrganization(organization: Organization): void;
  onLogout(): void;
}
const Header = observer(
  ({ name, importerOnboarded, isSuperAdmin, onLogout, onSelectOrganization, organization }: Props) => {
    // const { country, setCountry } = useStore().settings;
    const navigate = useNavigate();
    const [searchBox, toggleSearchBox] = useReducer((r) => !r, false);
    const [organizationModal, toggleOrganizationModal] = useReducer((r) => !r, false);
    const [org, setOrganization] = useState<Organization | undefined>(organization);

    return (
      <header className="sticky h-fit top-0 z-[48] w-full bg-primary shadow text-sm">
        {organizationModal && (
          <SelectOrganizationModal
            show={!!organizationModal}
            onClose={toggleOrganizationModal}
            selected={!!org}
            onSelect={(_organization: Organization) => {
              setOrganization(_organization);
              onSelectOrganization(_organization);
              toggleOrganizationModal();
            }}
          />
        )}
        {searchBox && <SearchBox show={!!searchBox} onClose={toggleSearchBox} />}

        <nav
          className="grid grid-cols-3 basis-full items-center w-full mx-auto px-6 py-2 gap-3"
          aria-label="Header"
        >
          <Icons.Logo className="w-20 fill-white cursor-pointer" onClick={() => navigate("/")} />

          <Input
            placeholder="Search..."
            kind="search"
            containerStyle="justify-self-center bg-transparent w-60"
            inputStyle="text-center placeholder-neutral-200 bg-white/10 cursor-pointer focus-visible:outline-none"
            onClick={toggleSearchBox}
            readOnly
          />

          <div className="flex items-center justify-end gap-3">
            {isSuperAdmin && (
              <Input
                name="organization"
                placeholder="Select organization"
                value={org?.name ?? ""}
                inputStyle="cursor-pointer"
                onClick={toggleOrganizationModal}
                readOnly
              />
            )}
            {/* TODO enable this once us support is done */}
            {/* <div className="flex items-center justify-center gap-2">
              <Dropdown
                name="country selector"
                triggerElement={
                  <>
                    <span className={twMerge("fi shadow", country === Country.CA ? "fi-ca" : "fi-us")} />
                    <ChevronDown className="text-slate-400" size={16} />
                  </>
                }
                placement="bottom-end"
                containerStyle="inline-flex cursor-pointer items-center gap-1"
                hideOnClick
              >
                <div className="p-1 flex flex-col gap-1 dark:bg-neutral-900">
                  <HeaderItem onClick={() => setCountry(Country.CA)}>
                    <span className="fi fi-ca" />
                    Canada
                  </HeaderItem>
                  <HeaderItem onClick={() => setCountry(Country.US)}>
                    <span className="fi fi-us" />
                    United States
                  </HeaderItem>
                </div>
              </Dropdown>
            </div> */}

            <Dropdown
              name="profile button"
              triggerElement={
                <>
                  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-white/5 dark:bg-neutral-900 font-medium text-white">
                    {getNameInitials(name)}
                  </div>
                  <h5 className="text-white">{name}</h5>
                  <UserRoundCog className="text-neutral-100 hover:text-neutral-200" />
                </>
              }
              containerStyle="inline-flex cursor-pointer items-center gap-2"
              hideOnClick
            >
              <div className="px-4 py-3 bg-gray-50 dark:bg-gray-800">
                <div className="text-sm text-neutral-600 dark:text-gray-100">Signed in as</div>
                <div className="text-sm font-medium text-neutral-800 dark:text-neutral-200">{name}</div>
              </div>

              <div className="p-1 flex flex-col gap-1 dark:bg-neutral-900">
                {/* <HeaderItem onClick={() => {}}>
                <DarkModeToggle />
              </HeaderItem> */}
                {/* <HeaderItem onClick={() => {}}>
                <ThemeSwitcher />
              </HeaderItem> */}
                {importerOnboarded && (
                  <HeaderItem onClick={() => navigate(SettingsPages.List)}>
                    <Settings className="size-4" />
                    Settings
                  </HeaderItem>
                )}
                {/* <HeaderItem onClick={() => navigate(MailPages.List)}>
                <Mail className="size-4" />
                Mail
              </HeaderItem> */}

                <hr />

                <HeaderItem onClick={onLogout}>Sign Out</HeaderItem>
              </div>
            </Dropdown>
          </div>
        </nav>
      </header>
    );
  }
);
export default Header;
