import { NavObject } from "@/common/Types.common";
import NavigationItems from "@/config/Navigation.config";
import { PanelLeftClose, PanelLeftOpen } from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import { getNameInitials } from "utils";
import NavItem from "./NavItem";

type Props = {
  name?: string;
  onLogout(): void;
  isCollapsed: boolean;
  onToggleCollapse(): void;
};
function Sidebar({ name, isCollapsed, onToggleCollapse }: Props) {
  const navigate = useNavigate();
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const onItemClick = (item: NavObject, index: number) => {
    if (item.submenu) setActiveIndex((prevIndex) => (prevIndex === index ? null : index));
    else navigate(item.path);
  };

  return (
    <div
      className={twMerge(
        `flex h-full flex-col transition-width duration-300 inset-y-0 start-0 z-20 bg-white border-e overflow-x-hidden shadow border-gray-200 dark:bg-gray-800 dark:border-gray-700 w-60`,
        isCollapsed && "w-18"
      )}
    >
      <header className="shrink-0 flex items-center justify-between px-4 py-3 border-b min-h-16">
        {!isCollapsed && (
          <div className="flex items-center gap-2 min-w-0 flex-1">
            <div className="flex shrink-0 size-10 items-center justify-center rounded-lg bg-primary/20 dark:bg-neutral-800 font-medium">
              {getNameInitials(name)}
            </div>
            <h5 className="truncate">{name}</h5>
          </div>
        )}

        {isCollapsed ? (
          <PanelLeftOpen
            className="cursor-pointer text-neutral dark:text-neutral-300 mx-auto"
            onClick={onToggleCollapse}
          />
        ) : (
          <PanelLeftClose
            className="cursor-pointer text-neutral dark:text-neutral-300"
            onClick={onToggleCollapse}
          />
        )}
      </header>

      <nav
        className={twMerge(
          "flex-1 overflow-y-auto overscroll-contain p-4 pb-6 w-full flex flex-col flex-wrap",
          isCollapsed && "overflow-x-hidden"
        )}
      >
        <ul className="space-y-1.5">
          {NavigationItems.map((item, index) => (
            <li key={index}>
              <NavItem
                item={item}
                isOpen={activeIndex === index}
                onClick={() => onItemClick(item, index)}
                isCollapsed={isCollapsed}
              />

              {item.submenu && activeIndex === index && (
                <ul>
                  {item.submenu.map((sub, subIndex) => (
                    <li key={subIndex}>
                      <NavItem
                        item={sub}
                        isOpen={activeIndex === subIndex}
                        isSubMenu
                        isCollapsed={isCollapsed}
                        onClick={() => onItemClick(sub, subIndex)}
                      />
                    </li>
                  ))}
                </ul>
              )}
            </li>
          ))}
        </ul>
      </nav>

      <footer className="shrink-0 mt-auto mb-4 flex flex-col items-center gap-3">
        {/* <DarkModeToggle iconOnly /> */}
        {/* <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary dark:bg-neutral-800 font-medium text-white">
          {getNameInitials(name)}
        </div> */}
        {/* <LogOut size={16} onClick={onLogout} /> */}
        {/* <Dropdown
          name="profile button"
          triggerElement={
            <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary dark:bg-neutral-800 font-medium text-white">
              {getNameInitials(name)}
            </div>
          }
          placement="top-start"
          containerStyle="z-20"
        >
          <div className="p-1 flex gap-1 dark:bg-neutral-800">
            <LogOut /> Sign Out
          </div>
        </Dropdown> */}
      </footer>
    </div>
  );
}
export default Sidebar;
