import { useEffect, useMemo } from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { Tabs } from "ui";

type RouterTabItem = {
  title: string;
  path: string;
};

type Props = {
  items: RouterTabItem[];
  context?: unknown;
  kind?: "tabs" | "pills";
  tabsStyle?: string;
  tabItemStyle?: string;
  contentStyle?: string;
  onTabChange?: (index: number) => void;
  defaultActiveTab?: number;
  disabled?: boolean;
};

const RouterTabs = ({ items, context, onTabChange, disabled, ...props }: Props) => {
  const tabs = useMemo(
    () =>
      items.map(({ path, title }) => ({
        title,
        path,
        content: <Outlet context={context} />
      })),
    [context, items]
  );

  const { pathname } = useLocation();
  const navigate = useNavigate();

  const defaultActiveTab = useMemo(() => {
    return Math.max(
      tabs.findIndex((t) => t.path && pathname.includes(t.path.toLowerCase())),
      0
    );
  }, [pathname, tabs]);

  useEffect(() => {
    onTabChange?.(defaultActiveTab);
  }, [onTabChange, defaultActiveTab]);

  const handleActiveTab = (index: number) => {
    navigate(tabs[index].path);
    onTabChange?.(index);
  };

  return (
    <Tabs
      items={tabs}
      defaultActiveTab={defaultActiveTab}
      onTabChange={handleActiveTab}
      disabled={disabled}
      {...props}
    />
  );
};

export default RouterTabs;
