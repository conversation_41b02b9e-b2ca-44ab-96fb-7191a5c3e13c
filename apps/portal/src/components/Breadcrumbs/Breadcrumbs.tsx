import { ReactNode } from "react";
import { Params, UIMatch, useMatches } from "react-router-dom";
import { twMerge } from "tailwind-merge";

type RouteHandle = {
  crumb(params: Params<string>): ReactNode;
  icon?: ReactNode;
};
type Props = {
  containerStyle?: string;
};
function Breadcrumbs({ containerStyle }: Props) {
  const matches = useMatches() as UIMatch<unknown, RouteHandle>[];
  const crumbs = matches
    // first get rid of any matches that don't have handle and crumb
    .filter((match: UIMatch<unknown, RouteHandle>) => Boolean(match.handle?.crumb))
    .map((match: UIMatch<unknown, RouteHandle>) => ({
      link: match.handle.crumb(match.params)
      // icon: match.handle.icon,
    }));

  return (
    crumbs.length > 1 && (
      <nav aria-label="breadcrumb" className={twMerge("px-6 py-1 border-y", containerStyle)}>
        <ol className="flex items-center">
          {crumbs.map(({ link }, index) => (
            <li
              key={index}
              className="flex items-center"
              // {...(icon && {
              //   icon: () => <span className="mr-2">{icon}</span>,
              // })}
            >
              <span
                className={twMerge(
                  "font-medium",
                  index !== crumbs.length - 1 && "hover:underline text-neutral"
                )}
              >
                {link}
              </span>
              {index !== crumbs.length - 1 && <span className="mx-2 text-neutral">/</span>}
            </li>
          ))}
        </ol>
      </nav>
    )
  );
}
export default Breadcrumbs;
