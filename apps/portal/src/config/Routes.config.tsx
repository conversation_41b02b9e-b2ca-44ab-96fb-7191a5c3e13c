import Wrapper from "@/bootstrap/Wrapper.bootstrap";
import { RouterObject } from "@/common/Types.common";
import { AuthPages, AuthRoutes } from "@/modules/Auth/Routes.auth";
import { CertificateRoutes } from "@/modules/Certificate/Routes.certificate";
import { ComplianceRoutes } from "@/modules/Compliance/Routes.compliance";
import { DocumentRoutes, FileBatchRoutes } from "@/modules/Document/Routes.documents";
import { MailRoutes } from "@/modules/Mail/Routes.mail";
import { ProductRoutes } from "@/modules/Product/Routes.product";
import { SettingsRoutes } from "@/modules/Settings/Routes.settings";
import { ShipmentPages, ShipmentRoutes } from "@/modules/Shipment/Routes.shipment";
import { Navigate } from "react-router-dom";

const RoutesRoot = (isSuperAdmin?: boolean): RouterObject => [
  // Fallback
  { path: "*", element: <Navigate to={AuthPages.Login} replace /> },
  // Private routes
  {
    path: "/",
    element: <Wrapper />,
    children: [
      { path: "", element: <Navigate to={ShipmentPages.List} replace /> },
      ...ShipmentRoutes,
      ...SettingsRoutes(isSuperAdmin),
      ...ProductRoutes,
      ...ComplianceRoutes,
      ...DocumentRoutes,
      ...FileBatchRoutes,
      ...MailRoutes,
      ...CertificateRoutes
    ]
  },

  // Public routes
  ...AuthRoutes
];

export default RoutesRoot;
