import { LOCATION_TABLE_KEY } from "@/modules/Location/Constant.location";
import { Currency, enumToSelectOptions } from "utils";

export const DEFAULT_LIMIT = 20;
export const ORDER_BY = [
  { label: "Ascending", value: "asc" },
  { label: "Descending", value: "desc" }
];
export const BOOLEAN_OPTIONS = [
  { label: "Yes", value: "true" },
  { label: "No", value: "false" }
];
export const CURRENCY = enumToSelectOptions(Currency, { allCaps: true });

export const urlValidation =
  /^((ftp|http|https):\/\/)?(www.)?(?!.*(ftp|http|https|www.))[a-zA-Z0-9_-]+(\.[a-zA-Z]+)+((\/)[\w#-]+)*(\/\w+\?[a-zA-Z0-9_-]+=\w+(&[a-zA-Z0-9_-]+=\w+)*)?$/gm;

export const ErrorCodes = {
  InvalidAccessToken: "Invalid access token",
  InvalidRefreshToken: "Invalid refresh token"
};
export const GLOBAL_TABLE_KEYS = [LOCATION_TABLE_KEY];
