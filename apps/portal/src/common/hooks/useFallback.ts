import { useCallback } from "react";
import { FallbackEnricherResponse } from "../Types.common";

export const useFallback = (fallback?: FallbackEnricherResponse) => {
  /**
   * Get the placeholder of the fallback field
   *
   * @deprecated Use wrapValue instead
   * @param key - The key of the field
   * @param defaultValue - The default value of the field
   * @param renderer - The renderer of the field
   * @returns The placeholder of the field
   */
  const getFallbackPlaceholder = useCallback(
    (key: string, defaultValue?: string, renderer?: (value: any) => string): string | undefined => {
      if (!fallback || !fallback.fields) {
        return defaultValue;
      }

      const field = fallback.fields.find((field) => field.key === key);

      if (field) {
        return renderer ? renderer(field.value) : field.value;
      }

      return defaultValue;
    },
    [fallback]
  );

  /**
   * Get the value of the fallback field
   *
   * @param key - The key of the field
   * @returns The value of the field
   */
  const getFallbackValue = useCallback(
    (key: string) => {
      if (!fallback?.fields) {
        return undefined;
      }

      const field = fallback.fields.find((field) => field.key === key);
      return field?.value;
    },
    [fallback]
  );

  /**
   * Get the source of the fallback field
   *
   * @param key - The key of the field
   * @returns The source of the field
   */
  const getFallbackSource = useCallback(
    (key: string) => {
      if (!fallback?.fields) {
        return undefined;
      }

      return fallback.fields.find((field) => field.key === key)?.source;
    },
    [fallback]
  );

  const valueWithFallback = (key: string, value: any, defaultValue: any) => {
    const fallbackValue = getFallbackValue(key);
    return value ?? fallbackValue ?? defaultValue;
  };

  const getFallback = (key: string) => {
    if (!fallback?.fields) {
      return undefined;
    }
    return fallback.fields.find((field) => field.key === key);
  };

  const wrapOnChange = useCallback(
    (key: string, onChange: (value: any) => void) => {
      const fallbackValue = getFallbackValue(key);

      return (value: any) => {
        if (value === fallbackValue) {
          return;
        }
        onChange(value);
      };
    },
    [fallback]
  );

  return {
    getFallbackPlaceholder,
    getFallbackValue,
    getFallback,
    getFallbackSource,
    valueWithFallback,
    wrapOnChange
  };
};
