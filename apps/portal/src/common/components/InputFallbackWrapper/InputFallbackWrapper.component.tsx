import { LucideLink2 } from "lucide-react";
import { ReactElement, ReactNode, cloneElement, useMemo } from "react";
import { Tooltip } from "ui";

interface InputFallbackWrapperProps {
  /**
   * The fallback.
   */
  fallback?: {
    key: string;
    value: any;
    source: string;
  };

  fallbackEnabled?: boolean;

  /**
   * The value provided by the user.
   */
  value: any;

  /**
   * A comparator function to compare the value and the fallback value.
   */
  comparator?: (value: any, fallbackValue: any) => boolean;

  valueTransformer?: (value: any) => any;

  onReset?: (fallbackValue: any) => void;

  /**
   * If true, we will display a reset button
   */
  withReset?: boolean;

  /**
   * If true, when user value is same as fallback value, we will regard it as if we are using the fallback value.
   * Otherwise, we will regard it as if we are using the user value.
   */
  materialized?: boolean;

  children: ReactElement<{ label: ReactNode } & any>;
}

function InputFallbackWrapper({
  fallback,
  children,
  value,
  onReset,
  withReset = false,
  fallbackEnabled = true,
  valueTransformer,
  materialized = false,
  comparator = (value, fallbackValue) => value === fallbackValue
}: InputFallbackWrapperProps) {
  const hint = useMemo(() => {
    if (!fallback || !fallbackEnabled) {
      return null;
    }

    if (!value || (materialized && comparator(value, fallback.value))) {
      return (
        <Tooltip text={fallback.source} position="right">
          <LucideLink2 className="w-3 h-3 text-green-600 hover:brightness-125" strokeWidth={2} />
        </Tooltip>
      );
    }

    if (value && withReset && onReset) {
      return (
        <>
          <span className="text-xs text-gray-500 cursor-pointer inline-block italic font-normal">
            (Modified)
          </span>
          <Tooltip
            text={`Reset to ${valueTransformer ? valueTransformer(fallback.value) : fallback.value}, ${
              fallback.source
            }`}
            position="right"
          >
            <span
              className="text-xs text-primary underline cursor-pointer inline-block hover:brightness-125"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                onReset?.(fallback.value);
              }}
            >
              Reset
            </span>
          </Tooltip>
        </>
      );
    }
  }, [fallback, value, valueTransformer, comparator, onReset, withReset, materialized, fallbackEnabled]);

  if (fallback && !value && fallbackEnabled) {
    const shadowElement = cloneElement(children, {
      value: valueTransformer ? valueTransformer(fallback.value) : fallback.value,
      label: (
        <div className="flex items-center gap-1.5">
          {children.props.label}
          {hint}
        </div>
      )
    });

    return shadowElement;
  }

  return cloneElement(children, {
    label: (
      <div className="flex items-center gap-1.5">
        {children.props.label}
        {hint}
      </div>
    )
  });
}

export default InputFallbackWrapper;
