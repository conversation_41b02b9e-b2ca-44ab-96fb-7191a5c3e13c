import { CURRENCY } from "@/common/Constant.common";
import { InputHTMLAttributes, useEffect, useState } from "react";
import { AutoComplete } from "ui";
import { Currency, SelectOption } from "utils";

type Props = Omit<InputHTMLAttributes<HTMLInputElement>, "value"> & {
  value?: Currency;
  error?: string;
  containerStyle?: string;
  onSelect(value?: Currency): void;
};
function SelectCurrency({ value, onSelect, error, containerStyle, ...props }: Props) {
  const [currency, setCurrency] = useState<Currency>();
  const [filteredOptions, setFilteredOptions] = useState<SelectOption[]>(CURRENCY);

  useEffect(() => {
    setCurrency(value);
  }, [value]);

  const handleSearch = (searchTerm: string) => {
    const filtered = CURRENCY.filter((option) =>
      option?.label?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredOptions(filtered);
  };

  const handleCurrencySelect = (option: SelectOption) => {
    setCurrency(option.value as Currency);
    onSelect(option.value as Currency);
  };

  // const handleClear = useCallback(() => {
  //   setCurrency(undefined);
  //   setFilteredOptions(CURRENCY);
  //   onSelect(undefined);
  // }, []);

  return (
    <AutoComplete
      {...props}
      label="Currency"
      placeholder="Select"
      containerStyle={containerStyle}
      items={filteredOptions}
      value={
        currency
          ? {
              label: currency?.toUpperCase(),
              value: currency
            }
          : undefined
      }
      onSearch={handleSearch}
      onSelect={handleCurrencySelect}
      // onClear={handleClear}
      error={error}
    />
  );
}

export default SelectCurrency;
