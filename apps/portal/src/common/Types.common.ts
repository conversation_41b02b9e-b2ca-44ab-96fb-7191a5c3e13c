import { FallbackEnricherResponseDto, User } from "nest-modules";
import { ReactElement } from "react";
import { RouteObject } from "react-router-dom";

export enum Country {
  CA = "ca",
  US = "us"
}

export type RouterObject = Array<
  RouteObject & {
    permission?: string;
    children?: RouterObject;
  }
>;

export type NavObject = {
  label: string;
  path: string;
  icon?: ReactElement;
  pattern?: RouterObject;
  submenu?: NavObject[];
  permissions?: string[];
  action?: string;
};

export type BaseError = {
  message: string;
  error: string;
  statusCode: number;
};

export type Timestamp = {
  createDate?: string;
  createdBy?: User;
  lastEditDate?: string;
  lastEditedBy?: User;
};

export type FallbackEnricherResponse = FallbackEnricherResponseDto;
