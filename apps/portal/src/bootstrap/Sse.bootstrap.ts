import { env } from "@/config/Environment.config";
import {
  EventStreamContentType,
  fetchEventSource,
  FetchEventSourceInit
} from "@microsoft/fetch-event-source";

class Sse {
  baseUrl: string;
  config?: FetchEventSourceInit;

  constructor({ baseUrl }: { baseUrl: string }) {
    this.baseUrl = baseUrl;
  }

  private joinUrl(...paths: string[]) {
    return paths.map((path) => path.replace(/^\/+|\/+$/, "")).join("/");
  }

  create(url: string, config?: FetchEventSourceInit) {
    const controller = new AbortController();

    fetchEventSource(this.joinUrl(this.baseUrl, url), {
      ...this.config,
      async onopen(response) {
        if (response.ok && response.headers.get("content-type") === EventStreamContentType) {
          return; // everything's good
        }
        throw new Error("Failed to connect to SSE");
      },
      ...config,
      signal: controller.signal
    });

    return {
      close: () => {
        controller.abort();
      }
    };
  }

  updateConfig(config: FetchEventSourceInit) {
    this.config = config;
  }
}

const sse = new Sse({
  baseUrl: env.apiUrl
});

export default sse;
