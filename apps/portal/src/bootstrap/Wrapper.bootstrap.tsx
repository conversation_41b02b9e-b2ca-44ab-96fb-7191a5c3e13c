import { ErrorCodes } from "@/common/Constant.common";
import { BaseError } from "@/common/Types.common";
import { Layout } from "@/components";
import { useRefreshToken } from "@/modules/Auth/mutations";
import { ImporterOnboarding } from "@/modules/Importers/components";
import { useQueryClient } from "@tanstack/react-query";
import { AfterResponseHook } from "ky";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import { Icons } from "ui";
import { minuteDifference } from "utils";
import http from "./Http.bootstrap";
import { useStore } from "./Store.bootstrap";

const Wrapper = observer(() => {
  const {
    session,
    refreshToken,
    isLoading,
    isHydrated,
    isReady,
    initialize,
    isAuthenticated,
    importerOnboarded,
    isSuperAdmin,
    logout
  } = useStore().auth;
  const { clearSettings } = useStore().settings;
  const navigate = useNavigate();
  const [sessionTimer, setSessionTimer] = useState<NodeJS.Timeout>();
  const { mutateAsync } = useRefreshToken();
  const queryClient = useQueryClient();

  const handleLogout = useCallback(async () => {
    await logout();
    queryClient.removeQueries();
    await clearSettings();
  }, [clearSettings, logout, queryClient]);

  //#region Error interceptor
  useEffect(() => {
    const errInterceptor: AfterResponseHook = async (_input, _options, response) => {
      if ((response.status === 401 || response.status === 403) && !isLoading) {
        const error: BaseError = await response.json();

        switch (error.message) {
          case ErrorCodes.InvalidAccessToken:
            refreshToken();
            break;
          case ErrorCodes.InvalidRefreshToken:
            handleLogout();
            break;
          default:
            handleLogout();
            break;
        }
      }
    };

    http.updateConfig({
      hooks: {
        afterResponse: [errInterceptor]
      }
    });
  }, [handleLogout, isLoading, refreshToken]);

  //#endregion

  //#region REFRESH TOKEN
  const checkToken = useCallback(async () => {
    if (session?.accessTokenExpiryDate && !isLoading) {
      const expiresAt = minuteDifference(new Date(session.accessTokenExpiryDate), new Date());

      if (expiresAt <= 10) {
        await mutateAsync({ refreshToken: session.refreshToken });
      }
    }
  }, [isLoading, mutateAsync, session?.accessTokenExpiryDate, session?.refreshToken]);

  useEffect(() => {
    if (session?.accessToken) {
      if (sessionTimer) return;

      const timer = setInterval(
        () => {
          checkToken();
        },
        9 * 60 * 1000
      );

      setSessionTimer(timer);
    } else {
      if (sessionTimer) {
        clearInterval(sessionTimer);
        setSessionTimer(undefined);
      }
    }
  }, [session?.accessToken, sessionTimer, checkToken]);

  //#endregion

  //#region Init
  const onboarded = useMemo(() => {
    return isSuperAdmin || importerOnboarded;
  }, [isSuperAdmin, importerOnboarded]);

  useEffect(() => {
    if (isHydrated) {
      initialize();

      if (!onboarded) navigate("/", { replace: true });
      if (!isAuthenticated) navigate("/login", { replace: true });
      else {
        checkToken();
      }
    }
  }, [checkToken, onboarded, initialize, isAuthenticated, isHydrated, navigate]);
  //#endregion

  return isReady ? (
    <Layout onLogout={handleLogout} importerOnboarded={onboarded}>
      {onboarded ? <Outlet /> : <ImporterOnboarding />}
    </Layout>
  ) : (
    <div className="flex h-screen w-full items-center justify-center">
      <Icons.Loader />
    </div>
  );
});

export default Wrapper;
