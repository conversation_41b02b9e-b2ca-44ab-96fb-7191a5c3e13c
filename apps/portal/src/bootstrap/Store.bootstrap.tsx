import Store from "@/config/Store.config";
import { createContext, PropsWithChildren, useContext } from "react";

export const StoreContext = createContext({} as Store);

export function useStore(): Store {
  return useContext(StoreContext);
}

type Props = PropsWithChildren & { store: Store };
export const StoreProvider = ({ store, children }: Props) => (
  <StoreContext.Provider value={store}>{children}</StoreContext.Provider>
);
