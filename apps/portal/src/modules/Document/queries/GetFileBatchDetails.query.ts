import { useQuery } from "@tanstack/react-query";
import { DetailParams } from "utils";
import { FileBatchService } from "../services";

export const getFileBatchDetailKey = "getFileBatchDetail";

export const useGetFileBatchDetail = ({ id }: DetailParams) =>
  useQuery({
    queryKey: [getFileBatchDetailKey, { id }],
    queryFn: async () => await FileBatchService.get({ id }),
    enabled: !!id
  });
