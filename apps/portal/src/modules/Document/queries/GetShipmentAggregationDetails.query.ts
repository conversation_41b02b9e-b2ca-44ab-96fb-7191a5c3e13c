import { useQuery } from "@tanstack/react-query";
import { DetailParams } from "utils";
import { DocumentAggregationService } from "../services";

export const getShipmentAggregationDetailsKey = "getShipmentAggregationDetails";

export const useGetShipmentAggregationDetails = ({ id }: DetailParams) =>
  useQuery({
    queryKey: [getShipmentAggregationDetailsKey, { id }],
    queryFn: async () => await DocumentAggregationService.getShipmentAggregationDetails({ id }),
    enabled: !!id
  });
