import { useQuery } from "@tanstack/react-query";
import { DetailParams } from "utils";
import { DocumentService } from "../services";

export const getDocumentValidationErrorsKey = "getDocumentValidationErrors";

export const useGetDocumentValidationErrors = ({ id }: DetailParams) =>
  useQuery({
    queryKey: [getDocumentValidationErrorsKey, { id }],
    queryFn: async () => await DocumentService.getFieldValidationErrors({ id }),
    enabled: !!id
  });
