import { useQuery } from "@tanstack/react-query";
import { getAuditNames } from "utils";
import { DocumentService } from "../services";
import type { GetDocumentList, GetDocumentListResponse } from "../Types.document";

export const getDocumentsKey = "getDocuments";

export const useGetDocuments = <R = GetDocumentListResponse>(
  params?: GetDocumentList,
  options?: {
    select?: (data: GetDocumentListResponse) => R;
    refetchOnMount?: boolean;
    enabled?: boolean;
    refetchInterval?: number;
  }
) =>
  useQuery({
    queryKey: [getDocumentsKey, params],
    queryFn: async () => await DocumentService.list(params),
    refetchOnMount: false,
    refetchInterval: options?.refetchInterval,
    ...options
  });

export const useGetDocumentList = (params?: GetDocumentList, refetchInterval?: number) =>
  useGetDocuments(params, {
    select: (data: GetDocumentListResponse) => {
      const documents = data.documents.map((d) => ({
        ...d,
        ...getAuditNames(d)
      }));
      return { ...data, documents };
    },
    refetchOnMount: true,
    refetchInterval
  });
