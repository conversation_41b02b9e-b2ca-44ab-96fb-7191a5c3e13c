import { useGetFileList, useGetFiles } from "./GetFiles.query";

import { useGetDocumentTypeList, useGetDocumentTypes } from "./GetDocumentTypes.query";
import { useGetFileDetail } from "./GetFileDetails.query";

import { useGetDocumentList, useGetDocuments } from "./GetDocuments.query";

import { useGetAggregationByTarget } from "./GetAggregationByTarget.query";
import { useGetDocumentAggregation } from "./GetDocumentAggregation.query";
import { useGetDocumentAggregationForDocument } from "./GetDocumentAggregationForDocument.query";
import { useGetDocumentValidationErrors } from "./GetDocumentValidationErrors.query";
import { useGetFileBatchDetail } from "./GetFileBatchDetails.query";

export {
  useGetAggregationByTarget,
  useGetDocumentAggregation,
  // document aggregation
  useGetDocumentAggregationForDocument,
  useGetDocumentList,
  // documents
  useGetDocuments,
  useGetDocumentTypeList,
  // document types
  useGetDocumentTypes,
  // document validation errors
  useGetDocumentValidationErrors,
  // file batch details
  useGetFileBatchDetail,
  // file details
  useGetFileDetail,
  useGetFileList,
  // files
  useGetFiles
};
