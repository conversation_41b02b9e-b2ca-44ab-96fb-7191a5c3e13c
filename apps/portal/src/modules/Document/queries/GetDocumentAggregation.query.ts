import { useQuery } from "@tanstack/react-query";
import { DetailParams } from "utils";
import { DocumentAggregationService } from "../services";

export const getDocumentAggregationKey = "getDocumentAggregation";

export const useGetDocumentAggregation = ({ id }: DetailParams) =>
  useQuery({
    queryKey: [getDocumentAggregationKey, { id }],
    queryFn: async () => await DocumentAggregationService.getAggregation({ id }),
    enabled: !!id
  });
