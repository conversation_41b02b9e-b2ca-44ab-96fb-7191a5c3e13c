import { useQuery } from "@tanstack/react-query";
import { DetailParams } from "utils";
import { DocumentAggregationService } from "../services";

export const getDocumentAggregationForDocumentKey = "getDocumentAggregationForDocument";

export const useGetDocumentAggregationForDocument = ({ id }: DetailParams) =>
  useQuery({
    queryKey: [getDocumentAggregationForDocumentKey, { id }],
    queryFn: async () => await DocumentAggregationService.getAggregationByDocument({ id }),
    enabled: !!id
  });
