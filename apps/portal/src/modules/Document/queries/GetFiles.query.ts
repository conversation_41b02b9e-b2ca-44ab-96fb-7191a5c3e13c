import { useQuery } from "@tanstack/react-query";
import { getAuditNames } from "utils";
import { FileService } from "../services";
import type { GetFileList, GetFileListResponse } from "../Types.document";

export const getFilesKey = "getFiles";

export const useGetFiles = <R = GetFileListResponse>(
  params?: GetFileList,
  options?: {
    select?: (data: GetFileListResponse) => R;
    refetchOnMount?: boolean;
    enabled?: boolean;
    refetchInterval?: number;
  }
) =>
  useQuery({
    queryKey: [getFilesKey, params],
    queryFn: async () => await FileService.list(params),
    refetchOnMount: false,
    refetchInterval: options?.refetchInterval,
    ...options
  });

export const useGetFileList = (params?: GetFileList, refetchInterval?: number) =>
  useGetFiles(params, {
    select: (data: GetFileListResponse) => {
      const files = data.files.map((f) => ({
        ...f,
        organizationName: f.organization?.name,
        ...getAuditNames(f)
      }));
      return { ...data, files };
    },
    refetchOnMount: true,
    refetchInterval
  });
