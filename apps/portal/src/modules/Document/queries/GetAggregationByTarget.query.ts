import { useQuery } from "@tanstack/react-query";
import { DocumentAggregationService } from "../services";
import { GetAggregationByTargetParams } from "../Types.document";

export const getAggregationByTargetKey = "getAggregationByTarget";

export const useGetAggregationByTarget = ({ id, targetType }: GetAggregationByTargetParams) =>
  useQuery({
    queryKey: [getAggregationByTargetKey, { id, targetType }],
    queryFn: async () => await DocumentAggregationService.getAggregationByTarget({ id, targetType }),
    enabled: !!id && !!targetType
  });
