import { useQuery } from "@tanstack/react-query";
import { DocumentTypeService } from "../services";
import { GetDocumentTypeList, GetDocumentTypeListResponse } from "../Types.document";

export const getDocumentTypesKey = "getDocumentTypes";

export const useGetDocumentTypes = <R = GetDocumentTypeListResponse>(
  params?: GetDocumentTypeList,
  select?: (data: GetDocumentTypeListResponse) => R,
  refetchOnMount = false
) =>
  useQuery({
    queryKey: [getDocumentTypesKey, params],
    queryFn: () => DocumentTypeService.list(params),
    select,
    refetchOnMount
  });

export const useGetDocumentTypeList = (params?: GetDocumentTypeList) =>
  useGetDocumentTypes(params, (data: GetDocumentTypeListResponse) => data, true);
