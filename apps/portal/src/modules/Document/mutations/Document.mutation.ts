import { useMutation } from "@tanstack/react-query";
import { DocumentService } from "../services";
import type {
  BatchSaveDocumentFieldParams,
  ProcessDocumentParams,
  SetDocumentVisibilityParams,
  UpdateDocumentShipmentParams
} from "../Types.document";

const useUpdateDocumentFields = () =>
  useMutation({
    mutationFn: async (params: BatchSaveDocumentFieldParams) => await DocumentService.saveFields(params)
  });

const useUpdateDocumentShipment = () =>
  useMutation({
    mutationFn: async (params: UpdateDocumentShipmentParams) => await DocumentService.updateShipment(params)
  });

const useDismissDocumentShipmentMismatchWarning = () =>
  useMutation({
    mutationFn: async (id: number) => await DocumentService.dismissShipmentMismatchWarning(id)
  });

const useProcessDocument = () =>
  useMutation({
    mutationFn: async (params: ProcessDocumentParams) => await DocumentService.process(params)
  });

const useSetDocumentVisibility = () =>
  useMutation({
    mutationFn: async (params: SetDocumentVisibilityParams) => await DocumentService.setVisibility(params)
  });

export {
  useDismissDocumentShipmentMismatchWarning,
  useProcessDocument,
  useSetDocumentVisibility,
  useUpdateDocumentFields,
  useUpdateDocumentShipment
};
