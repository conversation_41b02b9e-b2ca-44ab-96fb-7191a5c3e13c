import { useProcessDocument, useUpdateDocumentFields, useUpdateDocumentShipment } from "./Document.mutation";
import { useAggregateDocument, useRollbackAggregateDocument } from "./DocumentAggregation.mutation";
import { useDeleteFile, useUploadFiles } from "./File.mutation";
import { useAggregateFileBatch } from "./FileBatch.mutation";
import { useDownloadPdf } from "./Pdf.mutation";
export {
  useAggregateDocument,
  useAggregateFileBatch,
  useDeleteFile,
  useDownloadPdf,
  useProcessDocument,
  useRollbackAggregateDocument,
  useUpdateDocumentFields,
  useUpdateDocumentShipment,
  useUploadFiles
};
