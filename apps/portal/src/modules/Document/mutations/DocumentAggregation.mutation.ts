import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import { DocumentAggregationService } from "../services";

const useAggregateDocument = () =>
  useMutation({
    mutationFn: async (params: { retry?: boolean } & DetailParams) =>
      await DocumentAggregationService.aggregate(params)
  });

const useAbortAggregateDocument = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await DocumentAggregationService.abortAggregation(params)
  });

const useRollbackAggregateDocument = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await DocumentAggregationService.rollback(params)
  });

const useCreateDocumentAggregation = () =>
  useMutation({
    mutationFn: async (params: DetailParams) =>
      await DocumentAggregationService.createDocumentAggregation(params)
  });

export {
  useAbortAggregateDocument,
  useAggregateDocument,
  useCreateDocumentAggregation,
  useRollbackAggregateDocument
};
