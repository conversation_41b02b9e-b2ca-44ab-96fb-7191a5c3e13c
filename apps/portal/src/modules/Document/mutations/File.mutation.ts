import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import { FileService } from "../services";
import type { BatchSaveFileDocumentsParams, UploadFilesParams } from "../Types.document";

const useUploadFiles = () =>
  useMutation({
    mutationFn: async (params: UploadFilesParams) => await FileService.upload(params)
  });

const useDeleteFile = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await FileService.remove(params)
  });

const useReplaceDocuments = () =>
  useMutation({
    mutationFn: async (params: BatchSaveFileDocumentsParams) => await FileService.replaceDocuments(params)
  });

export { useDeleteFile, useReplaceDocuments, useUploadFiles };
