import Http from "@/bootstrap/Http.bootstrap";
import sse from "@/bootstrap/Sse.bootstrap";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import type {
  BatchSaveDocumentFieldParams,
  Document,
  DocumentStatus,
  DocumentValidationError,
  GetDocumentList,
  GetDocumentListResponse,
  ProcessDocumentParams,
  SetDocumentVisibilityParams,
  UpdateDocumentShipmentParams
} from "../Types.document";

const DOCUMENT_ROUTE = "documents";

async function list(params?: GetDocumentList): Promise<GetDocumentListResponse> {
  try {
    return await Http.get(stringifyQueryParams(DOCUMENT_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}

async function get({ id }: DetailParams): Promise<Document> {
  try {
    return await Http.get(`${DOCUMENT_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}

async function getFieldValidationErrors({ id }: DetailParams): Promise<DocumentValidationError[]> {
  try {
    return await Http.get(`${DOCUMENT_ROUTE}/${id}/fields/validation-errors`);
  } catch (error) {
    throw await handleError(error);
  }
}

async function saveFields(params: BatchSaveDocumentFieldParams): Promise<Document> {
  try {
    return await Http.put(`documents/${params.id}/fields/batch`, params);
  } catch (error) {
    throw await handleError(error);
  }
}

async function updateShipment(params: UpdateDocumentShipmentParams): Promise<Document> {
  try {
    return await Http.put(`documents/${params.id}/shipment`, params);
  } catch (error) {
    throw await handleError(error);
  }
}

async function dismissShipmentMismatchWarning(id: number): Promise<Document> {
  try {
    return await Http.put(`documents/${id}/shipment/dismiss-mismatch-warning`);
  } catch (error) {
    throw await handleError(error);
  }
}

async function process(params: ProcessDocumentParams): Promise<Document> {
  try {
    return await Http.post(`documents/${params.id}/process`, params);
  } catch (error) {
    throw await handleError(error);
  }
}

function subscribeToStatus(params: DetailParams, callback: (status: DocumentStatus) => void) {
  return sse.create(`/documents/${params.id}/status`, {
    onmessage(event) {
      if (event.data === "") return;
      try {
        const data = JSON.parse(event.data);
        callback(data.status);
      } catch (error) {
        console.error("onmessage", error);
      }
    }
  });
}

async function setVisibility(params: SetDocumentVisibilityParams): Promise<Document> {
  try {
    return await Http.put(`documents/${params.id}/visibility`, params);
  } catch (error) {
    throw await handleError(error);
  }
}

export {
  dismissShipmentMismatchWarning,
  get,
  getFieldValidationErrors,
  list,
  process,
  saveFields,
  setVisibility,
  subscribeToStatus,
  updateShipment
};
