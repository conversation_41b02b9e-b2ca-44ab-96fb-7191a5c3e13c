import Http from "@/bootstrap/Http.bootstrap";
import { DetailParams, handleError } from "utils";
import type {
  AggregateDocumentParams,
  DocumentAggregation,
  GetAggregationByTargetParams
} from "../Types.document";

// deprecated
const AGGREGATION_ROUTE = "document-aggregation";
const DOCUMENT_ROUTE = "document";

// deprecated
async function getShipment(params: AggregateDocumentParams): Promise<DocumentAggregation> {
  const queryParams = params.ids.map((id) => `ids=${id}`).join("&");
  try {
    return await Http.get(`${AGGREGATION_ROUTE}/shipment?${queryParams}`);
  } catch (error) {
    throw await handleError(error);
  }
}

async function getShipmentAggregationDetails(params: DetailParams): Promise<any> {
  try {
    return await Http.get(`shipment/${params.id}/aggregation-details`);
  } catch (error) {
    throw await handleError(error);
  }
}

async function aggregate(params: { retry?: boolean } & DetailParams): Promise<DocumentAggregation> {
  const queryParams = params.retry ? "?retry=true" : "";
  try {
    return await Http.post(`${AGGREGATION_ROUTE}/${params.id}/apply${queryParams}`);
  } catch (error) {
    throw await handleError(error);
  }
}

async function abortAggregation(params: DetailParams): Promise<void> {
  try {
    return await Http.post(`${AGGREGATION_ROUTE}/${params.id}/abort`);
  } catch (error) {
    throw await handleError(error);
  }
}

// TODO: change response type
async function getAggregationByDocument(params: DetailParams): Promise<DocumentAggregation> {
  try {
    return await Http.get(`${DOCUMENT_ROUTE}/${params.id}/aggregation`);
  } catch (error) {
    throw await handleError(error);
  }
}

async function getAggregation(params: DetailParams): Promise<DocumentAggregation> {
  try {
    return await Http.get(`${AGGREGATION_ROUTE}/${params.id}`);
  } catch (error) {
    throw await handleError(error);
  }
}

async function createDocumentAggregation(params: DetailParams): Promise<void> {
  try {
    return await Http.post(`${DOCUMENT_ROUTE}/${params.id}/aggregate`);
  } catch (error) {
    throw await handleError(error);
  }
}

async function rollback(params: DetailParams): Promise<void> {
  try {
    return await Http.post(`${DOCUMENT_ROUTE}/${params.id}/rollback-aggregation`);
  } catch (error) {
    throw await handleError(error);
  }
}

async function getAggregationByTarget(params: GetAggregationByTargetParams): Promise<DocumentAggregation> {
  try {
    return await Http.get(`${AGGREGATION_ROUTE}/by-target/${params.targetType}/${params.id}`);
  } catch (error) {
    throw await handleError(error);
  }
}

export {
  abortAggregation,
  aggregate,
  createDocumentAggregation,
  getAggregation,
  getAggregationByDocument,
  getAggregationByTarget,
  getShipment,
  getShipmentAggregationDetails,
  rollback
};
