import Http from "@/bootstrap/Http.bootstrap";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import { DocumentType, GetDocumentTypeList, GetDocumentTypeListResponse } from "../Types.document";

const DOCUMENT_TYPE_ROUTE = "document-types";
/**
 * Get document type list
 *
 * @returns
 */
async function list(params?: GetDocumentTypeList): Promise<GetDocumentTypeListResponse> {
  try {
    return await Http.get(stringifyQueryParams(DOCUMENT_TYPE_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get document type detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<DocumentType> {
  try {
    return await Http.get(`${DOCUMENT_TYPE_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}

export { get, list };
