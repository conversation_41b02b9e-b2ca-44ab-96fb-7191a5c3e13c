import Http from "@/bootstrap/Http.bootstrap";
import sse from "@/bootstrap/Sse.bootstrap";
import { EventSourceMessage } from "@microsoft/fetch-event-source";
import { DetailParams, handleError } from "utils";
import { FileBatch } from "../Types.document";

const FILE_BATCH_ROUTE = "file-batch";

function subscribeToBatchProcessingStatus(
  { id }: DetailParams,
  callback: (event: EventSourceMessage) => void,
  onClose?: () => void
) {
  return sse.create(`/file-batch/${id}/status`, {
    onmessage(event) {
      if (event.data == "") return;
      try {
        callback(event);
      } catch (error) {
        console.error("onmessage", error);
      }
    },
    onclose() {
      console.log("closed");
      onClose?.();
    },
    openWhenHidden: true
  });
}

async function get({ id }: DetailParams): Promise<FileBatch> {
  try {
    console.log("get", id);
    return await Http.get(`${FILE_BATCH_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}

async function aggregate({ id }: DetailParams): Promise<FileBatch> {
  try {
    return await Http.post(`${FILE_BATCH_ROUTE}/${id}/aggregate`);
  } catch (error) {
    throw await handleError(error);
  }
}

export { aggregate, get, subscribeToBatchProcessingStatus };
