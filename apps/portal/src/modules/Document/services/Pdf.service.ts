import { env } from "@/config/Environment.config";
import { handleError, Http } from "utils";
import type { PdfParams, PdfResponse } from "../Types.document";

const http = new Http({
  headers: {
    "X-API-KEY": env.apiTemplate
  }
});

/**
 * Download PDF from template
 *
 * @returns
 */
async function download<T>({ templateId, fileName, body }: PdfParams<T>): Promise<PdfResponse> {
  try {
    return await http.post(
      `https://rest-us.apitemplate.io/v2/create-pdf?template_id=${templateId}&expiration=1&filename=${fileName}.pdf`,
      body,
      { timeout: 60000 }
    );
  } catch (error) {
    throw await handleError(error);
  }
}

export { download };
