import Http from "@/bootstrap/Http.bootstrap";
import sse from "@/bootstrap/Sse.bootstrap";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import type {
  BatchSaveFileDocumentsParams,
  File,
  FileStatus,
  GetFileList,
  GetFileListResponse,
  UploadFilesParams,
  UploadFilesResponse
} from "../Types.document";

const FILE_ROUTE = "files";

async function list(params?: GetFileList): Promise<GetFileListResponse> {
  try {
    return await Http.get(stringifyQueryParams(FILE_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}

async function get({ id }: DetailParams): Promise<File> {
  try {
    return await Http.get(`${FILE_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}

async function upload(params: UploadFilesParams): Promise<UploadFilesResponse> {
  if (!params.files || params.files.length === 0) {
    throw new Error("No files provided for upload.");
  }
  try {
    const formData = new FormData();

    params.files.forEach((file) => {
      formData.append("files", file);
    });

    return await Http.postFormData(
      params.shipmentId ? stringifyQueryParams(FILE_ROUTE, { shipmentId: params.shipmentId }) : FILE_ROUTE,
      formData
    );
  } catch (error) {
    throw await handleError(error);
  }
}

async function remove({ id }: DetailParams) {
  try {
    return await Http.delete(`${FILE_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}

type TemporaryFileUrlResponse = {
  url: string;
};

async function download({ id }: DetailParams): Promise<TemporaryFileUrlResponse> {
  try {
    return await Http.get(`${FILE_ROUTE}/${id}/download`);
  } catch (error) {
    throw await handleError(error);
  }
}

async function replaceDocuments({ id, ...params }: BatchSaveFileDocumentsParams) {
  try {
    return await Http.put(`${FILE_ROUTE}/${id}/documents`, params);
  } catch (error) {
    throw await handleError(error);
  }
}

async function processFile({ id }: DetailParams) {
  try {
    return await Http.post(`${FILE_ROUTE}/${id}/process`);
  } catch (error) {
    throw await handleError(error);
  }
}

function subscribeToStatus(
  { id }: DetailParams,
  callback: (status: FileStatus) => void,
  onClose?: () => void
) {
  return sse.create(`/files/${id}/process/status`, {
    onmessage(event) {
      if (event.data === "") return;
      try {
        const data = JSON.parse(event.data);
        callback(data.status);
      } catch (error) {
        console.error("onmessage", error);
      }
    },
    onclose() {
      onClose?.();
    },
    onerror(error) {
      console.error("on error", error);
      onClose?.();
      throw error;
    }
  });
}

async function downloadShipmentFiles({ id }: DetailParams): Promise<Blob> {
  try {
    return await Http.getBlob(`shipments/${id}/download-files`);
  } catch (error) {
    throw await handleError(error);
  }
}

export {
  download,
  downloadShipmentFiles,
  get,
  list,
  processFile,
  remove,
  replaceDocuments,
  subscribeToStatus,
  upload
};
