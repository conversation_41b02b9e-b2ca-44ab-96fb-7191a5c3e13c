import { <PERSON>CheckIcon, ClockIcon, LoaderCircleIcon, TriangleAlertIcon } from "lucide-react";
import { useMemo } from "react";
import { twMerge } from "tailwind-merge";
import { FileStatus as FileStatusType } from "../../Types.document";

const FileStatus = ({
  value,
  className,
  showText = true
}: {
  value: FileStatusType;
  className?: string;
  showText?: boolean;
}) => {
  const fileStatus = useMemo(() => {
    switch (value) {
      case FileStatusType.SPLITTING:
      case FileStatusType.PARSING:
        return {
          className: "text-blue-500",
          icon: LoaderCircleIcon,
          iconClassName: "animate-spin"
        };
      case FileStatusType.PROCESSED:
        return { className: "text-green-600", icon: CircleCheckIcon };
      case FileStatusType.NO_DOCUMENT_DETECTED:
        return { className: "text-amber-500", icon: TriangleAlertIcon };
      case FileStatusType.PARSE_FAILED:
      case FileStatusType.FILE_CORRUPTED:
      case FileStatusType.SPLIT_FAILED:
      case FileStatusType.ERROR:
        return { className: "text-red-500", icon: TriangleAlertIcon };
      default:
        return {
          className: "text-purple-500",
          icon: ClockIcon
        };
    }
  }, [value]);

  return (
    <span
      className={twMerge("inline-flex items-center gap-1.5 font-medium", fileStatus.className, className)}
    >
      <fileStatus.icon className={twMerge("w-4 h-4", fileStatus.iconClassName)} strokeWidth={3} />
      {showText && (
        <span>
          {value.split("_").join(" ").charAt(0).toUpperCase() + value.split("_").join(" ").slice(1)}
        </span>
      )}
    </span>
  );
};

export default FileStatus;
