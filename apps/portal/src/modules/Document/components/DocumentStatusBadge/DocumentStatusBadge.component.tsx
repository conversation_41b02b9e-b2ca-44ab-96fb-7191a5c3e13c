import { RefreshCwIcon } from "lucide-react";
import { useMemo } from "react";
import { StatusLabel } from "ui";
import { DocumentStatus } from "../../Types.document";

const DocumentStatusBadge = ({
  status = "UNKNOWN",
  className
}: {
  status?: DocumentStatus | "UNKNOWN";
  className?: string;
}) => {
  const getColorMapping = useMemo(() => {
    switch (status) {
      case DocumentStatus.PENDING:
        return "bg-purple-200 text-purple-900";
      case DocumentStatus.EXTRACTING:
        return "info";
      case DocumentStatus.AGGREGATION_FAILED:
      case DocumentStatus.EXTRACTION_FAILED:
        return "danger";
      case DocumentStatus.AGGREGATED:
      case DocumentStatus.EXTRACTED:
        return "success";
      case DocumentStatus.VALIDATING:
      case DocumentStatus.SHIPMENT_MISMATCH:
        return "warning";
      default:
        return "bg-gray-200 text-gray-900";
    }
  }, [status]);

  const getLabelMapping = useMemo(() => {
    return status?.replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase());
  }, [status]);

  const getIconMapping = useMemo(() => {
    switch (status) {
      case DocumentStatus.EXTRACTING:
        return <RefreshCwIcon className="w-3 h-3 animate-spin [animation-duration:3s]" />;
    }
  }, [status]);

  return (
    <StatusLabel icon={getIconMapping} text={getLabelMapping} color={getColorMapping} className={className} />
  );
};

export default DocumentStatusBadge;
