import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  rectIntersection,
  useDroppable,
  useSensor,
  useSensors
} from "@dnd-kit/core";
import { SortableContext, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { GripVertical, Plus, Trash2 } from "lucide-react";
import { forwardRef, HTMLAttributes, useCallback, useEffect, useMemo, useState } from "react";
import { Link } from "react-router-dom";
import { Button, Select } from "ui";
import { useGetDocumentTypeList } from "../../queries";
import { BatchSaveFileDocumentItemDto, Document } from "../../Types.document";

interface FilePage {
  id: number;
  page: number;
  documentPage: number | null;
  documentId: number | null;
}

interface FileDocumentListProps {
  documents: Document[];
  pages: FilePage[];
  isEditing: boolean;
  onSubmit: (changeSet: {
    updated: BatchSaveFileDocumentItemDto[];
    created: BatchSaveFileDocumentItemDto[];
    deleted: number[];
  }) => void;
}

interface SortablePageProps {
  page: FilePage;
  isOverlay?: boolean;
  isEditing: boolean;
}

interface SortableData {
  type: "page" | "document";
  page?: FilePage;
  documentId?: number;
}

const PageItem = forwardRef<HTMLDivElement, SortablePageProps & HTMLAttributes<HTMLDivElement>>(
  ({ page, isOverlay = false, isEditing, ...props }, ref) => {
    return (
      <div
        {...props}
        className={`
        px-2 py-0.5 flex items-center gap-1
        bg-gray-50 hover:bg-gray-100 borderborder-gray-200 rounded-sm
        text-xs
        ${isOverlay ? "shadow-lg z-50" : ""}
      `}
        ref={ref}
      >
        {isEditing && <GripVertical className="size-3" />}
        <span className="text-xs">Page {page.page}</span>
      </div>
    );
  }
);

function SortablePage({ page, isOverlay = false, isEditing }: SortablePageProps) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: `page-${page.id}`,
    data: {
      type: "page",
      id: page.id,
      documentId: page.documentId,
      documentPage: page.documentPage
    } as SortableData
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1
  };

  return (
    <PageItem
      page={page}
      isOverlay={isOverlay}
      isEditing={isEditing}
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
    />
  );
}

function DocumentContainer({
  document,
  children,
  onDelete,
  onDocumentTypeChange,
  isEditing,
  documentTypeOptions
}: {
  document: Document & { saved: boolean };
  children: React.ReactNode;
  onDelete: (documentId: number) => void;
  onDocumentTypeChange: (documentId: number, documentTypeId: number) => void;
  isEditing: boolean;
  documentTypeOptions: { label: string; value: number }[];
}) {
  const isEmpty = document.pages.length === 0;
  const isUnassigned = document.id === 0;

  const { setNodeRef } = useDroppable({
    id: `document-${document.id}`,
    data: {
      type: "document",
      documentId: document.id,
      items: document.pages
    } as SortableData
  });

  const isParsOverlay = document.documentType?.name === "PARS_OVERLAY";

  return (
    <div>
      <div className="flex items-center gap-1 bg-gray-50 py-1 px-2 rounded-t-md cursor-pointer justify-between">
        <div className="text-sm">
          {isEditing && !isUnassigned ? (
            <div className="flex items-center gap-1">
              <Select
                options={documentTypeOptions}
                value={document.documentType?.id ?? 0}
                disabled={isParsOverlay}
                inputStyle="py-0.5"
                onChange={(e) => onDocumentTypeChange(document.id, Number(e.target.value))}
              />
              {document.saved ? null : <span className="text-xs text-gray-500">(unsaved)</span>}
            </div>
          ) : (
            <>
              {isUnassigned ? (
                <span className="text-sm">Unassigned</span>
              ) : (
                <Link to={`/document/${document.id}`} className="hover:underline">
                  <span className="text-sm">{document.name}</span>
                </Link>
              )}
            </>
          )}
        </div>
        {isEditing && !isUnassigned && (
          <Trash2
            className="size-3 text-danger-500 hover:text-danger-600"
            onClick={() => onDelete(document.id)}
          />
        )}
      </div>
      {!isParsOverlay && (
        <div
          id={`document-${document.id}`}
          data-document-id={document.id}
          className={`
           flex flex-col gap-1 p-1 border rounded-b-md
           ${isEmpty ? "border-dashed border-blue-200" : "border-gray-100"}
          `}
          ref={setNodeRef}
        >
          {isEmpty ? (
            <div className="text-center text-xs text-gray-500 flex items-center justify-center hover:bg-gray-100 transition-colors py-0.5">
              {isUnassigned ? "No unassigned pages" : "Drop pages here"}
            </div>
          ) : (
            children
          )}
        </div>
      )}
    </div>
  );
}

function FileDocumentList({
  documents: initialDocuments,
  pages,
  isEditing,
  onSubmit
}: FileDocumentListProps) {
  const [_, setActiveId] = useState<number | null>(null);
  const [activePage, setActivePage] = useState<FilePage | null>(null);

  const originalDocuments = useMemo<(Document & { saved: boolean })[]>(
    () => [
      ...(structuredClone(initialDocuments).map((doc) => ({ ...doc, saved: true })) as any),
      {
        id: 0,
        name: `Unassigned Pages`,
        saved: true,
        pages: pages.filter((page) => page.documentId === null)
      }
    ],
    [initialDocuments, pages]
  );

  const [documents, setDocuments] = useState<(Document & { saved: boolean })[]>(
    structuredClone(originalDocuments)
  );

  useEffect(() => {
    setDocuments(structuredClone(originalDocuments));
  }, [originalDocuments]);

  const { data: documentTypesRes } = useGetDocumentTypeList({
    page: 1,
    limit: 999
  });

  const documentTypeOptions = useMemo(() => {
    const mappedTypes =
      documentTypesRes?.documentTypes?.map((type) => ({
        label: type.name,
        value: type.id
      })) ?? [];

    return [{ label: "Select Document Type", value: 0 }, ...mappedTypes];
  }, [documentTypesRes]);

  const handleDocumentTypeChange = useCallback(
    (documentId: number, documentTypeId: number) => {
      setDocuments(
        documents.map((doc) =>
          doc.id === documentId
            ? ({ ...doc, documentTypeId: documentTypeId, documentType: { id: documentTypeId } } as any)
            : doc
        )
      );
    },
    [documents]
  );

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8
      }
    })
  );

  // Flatten all pages with their document information
  const allPages: FilePage[] = documents.flatMap((doc: Document) =>
    doc.pages.map((page: { id: number; page: number; documentPage: number | null }) => ({
      id: page.id,
      page: page.page,
      documentPage: page.documentPage,
      documentId: doc.id
    }))
  );

  const handleDragStart = useCallback(
    (event: DragStartEvent) => {
      const { active } = event;
      console.log("handleDragStart", event);
      setActiveId(active.data.current?.id as number);

      const page = allPages.find((p) => p.id === active.data.current?.id);
      setActivePage(page || null);
    },
    [allPages]
  );

  const updateDocumentPage = useCallback((document: Document, index: number) => {
    if (index === document.pages.length) {
      return document;
    }

    console.log("updateDocumentPage (document, index)", document.id, index);
    for (let i = index; i < document.pages.length; i++) {
      document.pages[i].documentPage = i + 1;
      console.log(
        "updateDocumentPage (pageId, documentPage)",
        document.pages[i].id,
        document.pages[i].documentPage
      );
    }
    return document;
  }, []);

  const movePageBetweenDocuments = useCallback(
    (pageId: number, fromDocumentId: number, toDocumentId: number, toDocumentPage: number) => {
      console.log("movePageBetweenDocuments", pageId, fromDocumentId, toDocumentId, toDocumentPage);
      const updatedDocuments = [...documents];

      const fromDocument = updatedDocuments.find((doc) => doc.id === fromDocumentId);
      const toDocument = updatedDocuments.find((doc) => doc.id === toDocumentId);

      if (!fromDocument || !toDocument) return;

      // find the page and set the documentId
      const page = fromDocument.pages.find((p: { id: number }) => p.id === pageId);

      if (page) {
        page.documentId = toDocumentId;
        page.documentPage = toDocumentPage;

        const fromDocumentPageIndex = fromDocument.pages.indexOf(page);
        const toDocumentPageIndex = toDocumentPage - 1;

        fromDocument?.pages.splice(fromDocumentPageIndex, 1);
        toDocument?.pages.splice(toDocumentPageIndex, 0, page);

        updateDocumentPage(fromDocument, fromDocumentPageIndex);
        updateDocumentPage(toDocument, toDocumentPageIndex);
      }

      setDocuments(updatedDocuments);
    },
    [documents]
  );

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      console.log("handleDragEnd", event);
      const { active, over } = event;

      if (!over || !over.data.current || !active.data.current) return;

      if (over.id === active.id) return;

      if (active.data.current.type === "page") {
        const pageId = active.data.current.id;
        const fromDocumentId = active.data.current.documentId;
        const toDocumentId = over.data.current.documentId;
        const overType = over.data.current.type;

        if (overType === "document") {
          movePageBetweenDocuments(pageId, fromDocumentId, toDocumentId, 1);
        } else if (overType === "page") {
          console.log("over.data.current", over.data.current);
          const toDocumentPage = over.data.current.documentPage;
          movePageBetweenDocuments(pageId, fromDocumentId, toDocumentId, toDocumentPage);
        }
      }

      setActiveId(null);
      setActivePage(null);
    },
    [documents, allPages]
  );

  const handleDragOver = useCallback(
    (event: DragOverEvent) => {
      console.log("handleDragOver", event);
      const { over, active } = event;
      // setActiveId(null)
      if (over?.data.current?.documentId !== active?.data.current?.documentId) {
        const pageId = active?.data.current?.id;
        const fromDocumentId = active?.data.current?.documentId;
        const toDocumentId = over?.data.current?.documentId;
        const overType = over?.data.current?.type;

        if (overType === "document" && over?.data.current?.items.length === 0) {
          movePageBetweenDocuments(pageId, fromDocumentId, toDocumentId, 1);
        } else if (overType === "page") {
          const isBelowOverItem =
            over &&
            active.rect.current.translated &&
            active.rect.current.translated.top > over.rect.top + over.rect.height;
          const toDocumentPage = isBelowOverItem
            ? over?.data.current?.documentPage + 1
            : over?.data.current?.documentPage;
          movePageBetweenDocuments(pageId, fromDocumentId, toDocumentId, toDocumentPage);
        }
      }
      // setActivePage(null)
    },
    [documents, allPages]
  );

  const handleDelete = useCallback(
    (documentId: number) => {
      const documentToDelete = documents.find((doc) => doc.id === documentId);
      const pagesToDelete = documentToDelete?.pages ?? [];
      const unassigendDocument = documents.find((doc) => doc.id === 0);

      setDocuments([
        ...(documents.filter((doc) => doc.id !== documentId && doc.id !== 0) as any),
        {
          ...unassigendDocument,
          pages: [...(unassigendDocument?.pages ?? []), ...pagesToDelete]
        }
      ]);
    },
    [documents]
  );

  const handleSubmit = useCallback(() => {
    const originalDocumentsHash = new Map(
      originalDocuments.map((doc) => [
        doc.id,
        doc.documentType?.id + "_" + doc.pages.map((page: FilePage) => page.id).join(",")
      ])
    );
    const documentsHash = new Map(
      documents.map((doc) => [
        doc.id,
        doc.documentType?.id + "_" + doc.pages.map((page: FilePage) => page.id).join(",")
      ])
    );

    const changeSet = {
      updated: [] as BatchSaveFileDocumentItemDto[],
      created: [] as BatchSaveFileDocumentItemDto[],
      deleted: [] as number[]
    };

    // remove incomplete documents
    setDocuments(documents.filter((doc) => !(doc.pages.length === 0 && !doc.saved)));

    // check for deleted documents
    for (const documentId of originalDocumentsHash.keys()) {
      if (!documentsHash.has(documentId)) {
        changeSet.deleted.push(documentId);
      }
    }

    for (const document of documents) {
      // we don't include unassigned pages in the change set
      if (document.id === 0) {
        continue;
      }

      // we don't include empty documents in the change set
      if (document.pages.length === 0) {
        // include to delete set
        if (document.id && document.saved) {
          changeSet.deleted.push(document.id);
        }
        continue;
      }

      const documentId = document.id;

      if (originalDocumentsHash.has(documentId)) {
        const originalDocument = originalDocumentsHash.get(documentId);
        const documentHash = documentsHash.get(documentId);

        if (originalDocument !== documentHash) {
          changeSet.updated.push({
            id: document.id,
            documentTypeId: document.documentType?.id ?? 0,
            startPage: document.pages[0].page,
            pageIds: document.pages.map((page: { id: number }) => page.id)
          });
        }
      } else {
        changeSet.created.push({
          documentTypeId: document.documentType?.id ?? 0,
          startPage: document.pages[0].page,
          pageIds: document.pages.map((page: { id: number }) => page.id)
        });
      }
    }

    onSubmit(changeSet);
  }, [documents]);

  return (
    <>
      <div className="flex justify-between mb-3">
        <div>
          <h5>Documents</h5>
          <p className="text-xs text-gray-500">
            {documents.filter((doc) => doc.id !== 0).length} documents, {pages.length} pages
          </p>
        </div>
        {isEditing && <Button label="Save" onClick={handleSubmit} />}
      </div>
      <DndContext
        sensors={sensors}
        collisionDetection={rectIntersection}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="space-y-3">
          {documents.map((document) => {
            return (
              <DocumentContainer
                key={document.id}
                document={document}
                documentTypeOptions={documentTypeOptions}
                onDocumentTypeChange={handleDocumentTypeChange}
                onDelete={handleDelete}
                isEditing={isEditing}
              >
                <div id={`document-${document.id}`} className="flex flex-col gap-1">
                  <SortableContext
                    disabled={!isEditing}
                    items={document.pages.map(
                      (page: { id: number; page: number; documentPage: number | null }) => `page-${page.id}`
                    )}
                    id={`document-${document.id}`}
                    strategy={verticalListSortingStrategy}
                  >
                    {document.pages.map((page: { id: number; page: number; documentPage: number | null }) => (
                      <SortablePage
                        key={page.id}
                        isEditing={isEditing}
                        page={{
                          id: page.id,
                          page: page.page,
                          documentPage: page.documentPage,
                          documentId: document.id
                        }}
                      />
                    ))}
                  </SortableContext>
                </div>
              </DocumentContainer>
            );
          })}

          <div
            className="flex items-center gap-1 bg-gray-50 py-1 px-2 rounded-md cursor-pointer justify-center"
            onClick={() => {
              setDocuments([
                ...(documents.slice(0, -1) as any),
                {
                  id: Date.now(),
                  name: `Document ${Date.now()}`,
                  saved: false,
                  pages: []
                },
                ...(documents.slice(-1) as any)
              ]);
            }}
          >
            <Plus className="size-3" />
            <h4 className="text-sm">Add document</h4>
          </div>
        </div>

        <DragOverlay>
          {activePage ? <SortablePage page={activePage} isOverlay isEditing={isEditing} /> : null}
        </DragOverlay>
      </DndContext>
    </>
  );
}

export default FileDocumentList;
