import { Fragment } from "react";
import { Link } from "react-router-dom";
import { FilePages } from "../../Routes.documents";
import { Document, File, FileStatus as FileStatusType } from "../../Types.document";

const DocumentRenderer = ({ value, row }: { value: Document[]; row?: File }) => {
  return (
    <div>
      {value.length === 0 && row?.status === FileStatusType.NO_DOCUMENT_DETECTED ? (
        <span className="text-amber-500">
          <Link
            to={FilePages.Detail(row?.id.toString() ?? "") + "?edit=true"}
            className="text-amber-500 hover:underline"
          >
            Configure Document in this file
          </Link>
        </span>
      ) : (
        <>
          {value
            .filter((document) => document.name !== "unknown" && document.name !== "other")
            .splice(0, 3)
            .map((document, index) => (
              <Fragment key={document.id}>
                <Link to={`/document/${document.id}`} className="text-primary hover:underline">
                  {document.name}
                </Link>
                {index < value.length - 1 && index !== 2 && ", "}
              </Fragment>
            ))}
          {value.length > 3 && <span className="text-gray-500"> ({value.length - 3} more)</span>}
        </>
      )}
    </div>
  );
};

export default DocumentRenderer;
