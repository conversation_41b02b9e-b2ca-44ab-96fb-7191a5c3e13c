import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "ui";
import { useGetAggregationByTarget } from "../../queries";
import { AggregationTargetType } from "../../Types.document";

interface CreateFromAggregationMessageProps {
  id?: number;
  targetType: AggregationTargetType;
}

function CreateFromAggregationMessage({ id, targetType }: CreateFromAggregationMessageProps) {
  if (!id) return null;

  const { data: aggregation } = useGetAggregationByTarget({ id, targetType });

  if (!aggregation) return null;

  return (
    <Alert
      title={`This ${targetType.replace("-", " ")} is created from documents`}
      type="info"
      compact
      actionPosition="end"
      action={
        <Link to={`/document-aggregation/${aggregation.id}`}>
          <Button label="View" kind="outline" onClick={() => {}} />
        </Link>
      }
    />
  );
}

export default CreateFromAggregationMessage;
