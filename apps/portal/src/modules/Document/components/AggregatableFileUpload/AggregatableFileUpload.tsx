import {
  Document as DocumentEntity,
  DocumentStatus,
  File as FileEntity,
  FileStatus,
  UploadFilesResponse
} from "@/modules/Document/Types.document";
import { useEffect, useMemo, useState } from "react";
import { toast } from "react-hot-toast";
import { FileUpload } from "..";

import { EventSourceMessage } from "@microsoft/fetch-event-source";
import { subscribeToBatchProcessingStatus } from "../../services/FileBatch.service";
import AggregationEventList from "./AggregationEvents";
import UploadedFileList from "./UploadedFileList.component";
// import { uploadedFileFixture } from "./fixtures/files";
import { ShipmentPages } from "@/modules/Shipment/Routes.shipment";
import { useNavigate } from "react-router-dom";
import { Alert, Button } from "ui";
import AggregationResultList from "./AggregationResult";
import { ResultItem, UploadedFile } from "./types";

interface FileUploadPreviewerProps {
  // onAggregate: (documentId: number) => Promise<void>;
  onProcessing: (isProcessing: boolean) => void;
  multiple?: boolean;
}

enum State {
  WAITING_FOR_UPLOAD,
  PROCESSING,
  COMPLETED
}

const AggregatableFileUpload = ({
  // onAggregate,
  onProcessing,
  multiple
}: FileUploadPreviewerProps) => {
  const [state, setState] = useState<State>(State.WAITING_FOR_UPLOAD);
  const [files, setFiles] = useState<Partial<FileEntity>[]>([]);
  const [documents, setDocuments] = useState<Partial<DocumentEntity>[]>([]);

  const [batchId, setBatchId] = useState<string | null>(null);

  const updateFileStatus = (data: { fileId: number; status: string }) => {
    const { fileId, status } = data;
    const file = files.find((file) => file.id === fileId);
    if (file) {
      file.status = status as FileStatus;
      setFiles((prevFiles) => [...prevFiles]);
    }
  };

  const reset = () => {
    setState(State.WAITING_FOR_UPLOAD);
    setFiles([]);
    setDocuments([]);
    setResults([]);
    setError(null);
  };

  const [results, setResults] = useState<ResultItem[]>([]);
  const [error, setError] = useState<string | null>(null);

  const createDocument = (data: { fileId: number; documentId: number; name: string }) => {
    const { fileId, documentId, name } = data;
    setDocuments((prevDocuments) => [
      ...prevDocuments,
      {
        id: documentId,
        fileId: fileId,
        name: name,
        status: DocumentStatus.PENDING
      }
    ]);
  };

  const updateDocumentStatus = (data: { documentId: number; status: string }) => {
    const { documentId, status } = data;
    setDocuments((prevDocuments) =>
      prevDocuments.map((doc) => (doc.id === documentId ? { ...doc, status: status as DocumentStatus } : doc))
    );
  };

  const navigate = useNavigate();

  const batchShipmentCreated = (data: { shipmentId: number }) => {
    const { shipmentId } = data;
    toast.success("Redirecting to shipment detail page...");
    setState(State.COMPLETED);
    setTimeout(() => {
      navigate(ShipmentPages.Detail(shipmentId));
    }, 3000);
  };

  const [events, setEvents] = useState<EventSourceMessage[]>([]);

  const uploadedFiles = useMemo((): UploadedFile[] => {
    return files.map((file) => ({
      id: file.id!,
      name: file.name!,
      status: file.status!,
      documents: documents
        .filter((doc) => doc.fileId === file.id)
        .map((doc) => ({
          id: doc.id!,
          name: doc.name!,
          status: doc.status!
        }))
    }));
  }, [files, documents]);

  const handleBatchProcessingUpdate = (event: EventSourceMessage) => {
    setEvents((prevEvents) => [...prevEvents, event]);
    const data = JSON.parse(event.data);

    switch (event.event) {
      case "file-status-updated":
        updateFileStatus(data);
        break;
      case "document-created":
        createDocument({
          ...data,
          status: DocumentStatus.PENDING
        });
        break;
      case "document-extracted":
        updateDocumentStatus({
          ...data,
          status: DocumentStatus.EXTRACTED
        });
        break;
      case "batch.shipmentCreated":
        batchShipmentCreated(data);
        break;
      case "batch-id-received":
        if (data.batchId) {
          setBatchId(data.batchId);
        }
        break;
      case "batch-document-aggregated":
        setResults(data.result);
        setState(State.COMPLETED);
        onProcessing(false);
        break;
      case "batch-documents-validated":
        break;
      case "batch-checking-failed":
        setError(data.error);
        setState(State.COMPLETED);
        onProcessing(false);
        break;
      default:
        console.warn(`Unhandled event type: ${event.event}`);
    }
  };

  useEffect(() => {
    if (batchId) {
      const sse = subscribeToBatchProcessingStatus({ id: batchId }, (event) => {
        handleBatchProcessingUpdate(event);
      });
      return () => {
        sse.close();
      };
    }
  }, [batchId]);

  const onFileUploaded = (res: UploadFilesResponse) => {
    toast.success("File uploaded");
    setFiles(res.files);
    setDocuments([]);
    setState(State.PROCESSING);
    setBatchId(res.batchId);
  };

  const onUploading = (_isUploading: boolean) => {
    onProcessing(true);
  };

  switch (state) {
    case State.WAITING_FOR_UPLOAD:
      return <FileUpload onFileUploaded={onFileUploaded} onUploading={onUploading} multiple={multiple} />;

    case State.PROCESSING:
      return (
        <div className="flex gap-4">
          <div className="flex-1 shrink-0">
            <UploadedFileList files={uploadedFiles} />
          </div>
          <div className="w-1/2">
            <AggregationEventList events={events} />
          </div>
        </div>
      );

    case State.COMPLETED:
      return (
        <div>
          {error && (
            <Alert
              type="error"
              title="Shipment cannot be created"
              message={error}
              actionPosition="end"
              className="mb-4"
              compact
              action={
                <>
                  <Button label="Try other documents" onClick={reset} kind="upload" />
                </>
              }
            />
          )}
          <div className="flex gap-4">
            <div className="flex-1 shrink-0">
              <UploadedFileList files={uploadedFiles} />
            </div>
            <div className="w-1/2">
              {error ? <AggregationEventList events={events} /> : <AggregationResultList results={results} />}
            </div>
          </div>
        </div>
      );
  }
};

export default AggregatableFileUpload;
