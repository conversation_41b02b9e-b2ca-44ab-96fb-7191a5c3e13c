import { EventSourceMessage } from "@microsoft/fetch-event-source";

// Complete events data
const events: EventSourceMessage[] = [
  {
    event: "file-status-updated",
    id: "1",
    data: JSON.stringify({
      fileId: 211,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "parsing",
      date: new Date()
    })
  },
  {
    event: "file-status-updated",
    id: "2",
    data: JSON.stringify({
      fileId: 211,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "splitting",
      date: new Date()
    })
  },
  {
    event: "file-status-updated",
    id: "3",
    data: JSON.stringify({
      fileId: 210,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "parsing",
      date: new Date()
    })
  },
  {
    event: "file-status-updated",
    id: "4",
    data: JSON.stringify({
      fileId: 210,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "splitting",
      date: new Date()
    })
  },
  {
    event: "file-status-updated",
    id: "5",
    data: JSON.stringify({
      fileId: 212,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "parsing",
      date: new Date()
    })
  },
  {
    event: "file-status-updated",
    id: "6",
    data: JSON.stringify({
      fileId: 212,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "splitting",
      date: new Date()
    })
  },
  {
    event: "file-status-updated",
    id: "7",
    data: JSON.stringify({
      fileId: 209,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "parsing",
      date: new Date()
    })
  },
  {
    event: "file-status-updated",
    id: "8",
    data: JSON.stringify({
      fileId: 209,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "splitting",
      date: new Date()
    })
  },
  {
    event: "file-status-updated",
    id: "9",
    data: JSON.stringify({
      fileId: 213,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "parsing",
      date: new Date()
    })
  },
  {
    event: "file-status-updated",
    id: "10",
    data: JSON.stringify({
      fileId: 213,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "splitting",
      date: new Date()
    })
  },
  {
    event: "file-status-updated",
    id: "11",
    data: JSON.stringify({
      fileId: 211,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "splitting",
      date: new Date()
    })
  },
  {
    event: "document-created",
    id: "12",
    data: JSON.stringify({ documentId: 455 })
  },
  {
    event: "document-created",
    id: "13",
    data: JSON.stringify({ documentId: 456 })
  },
  {
    event: "document-created",
    id: "14",
    data: JSON.stringify({ documentId: 457 })
  },
  {
    event: "file-status-updated",
    id: "15",
    data: JSON.stringify({
      fileId: 211,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "processed",
      date: new Date()
    })
  },
  {
    event: "file-status-updated",
    id: "16",
    data: JSON.stringify({
      fileId: 210,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "splitting",
      date: new Date()
    })
  },
  {
    event: "document-created",
    id: "17",
    data: JSON.stringify({ documentId: 458 })
  },
  {
    event: "file-status-updated",
    id: "18",
    data: JSON.stringify({
      fileId: 210,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "processed",
      date: new Date()
    })
  },
  {
    event: "file-status-updated",
    id: "19",
    data: JSON.stringify({
      fileId: 212,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "splitting",
      date: new Date()
    })
  },
  {
    event: "document-created",
    id: "20",
    data: JSON.stringify({ documentId: 459, date: new Date() })
  },
  {
    event: "file-status-updated",
    id: "21",
    data: JSON.stringify({
      fileId: 212,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "processed",
      date: new Date()
    })
  },
  {
    event: "file-status-updated",
    id: "22",
    data: JSON.stringify({
      fileId: 209,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "splitting",
      date: new Date()
    })
  },
  {
    event: "document-extracted",
    id: "23",
    data: JSON.stringify({ documentId: 455, date: new Date() })
  },
  {
    event: "document-extracted",
    id: "24",
    data: JSON.stringify({ documentId: 456, date: new Date() })
  },
  {
    event: "document-created",
    id: "25",
    data: JSON.stringify({ documentId: 460, date: new Date() })
  },
  {
    event: "file-status-updated",
    id: "26",
    data: JSON.stringify({
      fileId: 209,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "processed",
      date: new Date()
    })
  },
  {
    event: "file-status-updated",
    id: "27",
    data: JSON.stringify({
      fileId: 213,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "splitting",
      date: new Date()
    })
  },
  {
    event: "document-created",
    id: "28",
    data: JSON.stringify({ documentId: 461, date: new Date() })
  },
  {
    event: "file-status-updated",
    id: "29",
    data: JSON.stringify({
      fileId: 213,
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      status: "processed",
      date: new Date()
    })
  },
  {
    event: "document-extracted",
    id: "30",
    data: JSON.stringify({ documentId: 457, date: new Date() })
  },
  {
    event: "document-extracted",
    id: "31",
    data: JSON.stringify({ documentId: 458, date: new Date() })
  },
  {
    event: "document-extracted",
    id: "32",
    data: JSON.stringify({ documentId: 459, date: new Date() })
  },
  {
    event: "all-documents-extracted",
    id: "35",
    data: JSON.stringify({
      batchId: "480640ed-daf3-44cc-b979-6ef4b8c0a907",
      date: new Date()
    })
  },
  {
    event: "document-aggregation-created",
    id: "36",
    data: JSON.stringify({
      aggregationId: 506,
      documentId: 455,
      date: new Date()
    })
  },
  {
    event: "document-aggregation-created",
    id: "37",
    data: JSON.stringify({
      aggregationId: 504,
      documentId: 461,
      date: new Date()
    })
  },
  {
    event: "document-aggregation-created",
    id: "38",
    data: JSON.stringify({
      aggregationId: 507,
      documentId: 458,
      date: new Date()
    })
  },
  {
    event: "document-aggregation-created",
    id: "39",
    data: JSON.stringify({
      aggregationId: 505,
      documentId: 459,
      date: new Date()
    })
  },
  {
    event: "document-aggregation-created",
    id: "40",
    data: JSON.stringify({
      aggregationId: 508,
      documentId: 456,
      date: new Date()
    })
  },
  {
    event: "document-aggregation-created",
    id: "41",
    data: JSON.stringify({
      aggregationId: 509,
      documentId: 457,
      date: new Date()
    })
  },
  {
    event: "document-aggregation-success",
    id: "42",
    data: JSON.stringify({
      documentId: 461,
      aggregationId: 504,
      date: new Date()
    })
  },
  {
    event: "document-aggregation-failed",
    id: "43",
    data: JSON.stringify({
      documentId: 461,
      aggregationId: 504,
      date: new Date()
    })
  },
  {
    event: "document-aggregation-success",
    id: "44",
    data: JSON.stringify({
      documentId: 456,
      aggregationId: 508,
      date: new Date()
    })
  },
  {
    event: "document-aggregation-failed",
    id: "45",
    data: JSON.stringify({
      documentId: 456,
      aggregationId: 508,
      date: new Date()
    })
  },
  {
    event: "document-aggregation-success",
    id: "46",
    data: JSON.stringify({
      documentId: 455,
      aggregationId: 506,
      date: new Date()
    })
  },
  {
    event: "document-aggregation-failed",
    id: "47",
    data: JSON.stringify({
      documentId: 455,
      aggregationId: 506,
      date: new Date()
    })
  },
  {
    event: "document-aggregation-failed",
    id: "48",
    data: JSON.stringify({
      documentId: 459,
      aggregationId: 505,
      date: new Date()
    })
  },
  {
    event: "document-aggregation-failed",
    id: "49",
    data: JSON.stringify({
      documentId: 458,
      aggregationId: 507,
      date: new Date()
    })
  }
];

export default events;
