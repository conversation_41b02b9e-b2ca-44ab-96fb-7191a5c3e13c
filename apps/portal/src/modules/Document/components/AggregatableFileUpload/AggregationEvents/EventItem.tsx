import { FormattedEvent, getStatusColor, getStatusIcon } from "./eventUtils";

interface EventItemProps {
  event: FormattedEvent;
}

export const EventItem: React.FC<EventItemProps> = ({ event }) => {
  const StatusIcon = getStatusIcon(event.status);
  const statusColor = getStatusColor(event.status);

  return (
    <div className="flex items-start gap-2 text-sm">
      <StatusIcon className={`w-4 h-4 ${statusColor} shrink-0`} />
      <span className="text-gray-500 dark:text-gray-400 shrink-0">[{event.date.toLocaleString()}]</span>
      <span className="text-gray-700 dark:text-gray-300">{event.message}</span>
    </div>
  );
};
