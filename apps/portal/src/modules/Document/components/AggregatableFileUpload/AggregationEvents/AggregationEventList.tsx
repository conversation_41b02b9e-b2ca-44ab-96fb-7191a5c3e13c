// Import necessary libraries
import { EventSourceMessage } from "@microsoft/fetch-event-source";
import { useEffect, useMemo, useRef } from "react";
import { EventItem } from "./EventItem";
import { getEventDetails } from "./eventUtils";

// REGION TESTING
// import events from "./fixtures/events";
// END REGION TESTING

interface AggregationEventListProps {
  events: EventSourceMessage[];
}

// Component to display events
const AggregationEventList = ({ events }: AggregationEventListProps) => {
  const eventListRef = useRef<HTMLDivElement>(null);

  // auto scroll to bottom when new event is added
  useEffect(() => {
    if (eventListRef.current) {
      eventListRef.current.scrollTop = eventListRef.current.scrollHeight;
    }
  }, [events]);

  const formattedEventList = useMemo(() => events.map((event) => getEventDetails(event)), [events]);

  return (
    <div className="overflow-hidden overflow-y-auto max-h-full" ref={eventListRef}>
      <ol className="space-y-2">
        {formattedEventList.map((event) => (
          <li key={event.id}>
            <EventItem event={event} />
          </li>
        ))}
      </ol>
    </div>
  );
};

export default AggregationEventList;
