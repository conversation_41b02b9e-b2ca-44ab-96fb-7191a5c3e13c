import { File } from "lucide-react";
import { DocumentStatusBadge, FileStatus as FileStatusBadge } from "..";
import { UploadedFile } from "./types";

interface FileListProps {
  files: UploadedFile[];
}

const ProcessingFile = ({ file }: { file: UploadedFile }) => (
  <div className="flex items-center justify-between px-3 py-2">
    <div className="flex items-center gap-2">
      <File className="w-4 h-4 text-gray-500" />
      <span className="font-medium">{file.name}</span>
    </div>
    <FileStatusBadge value={file.status} showText={false} />
  </div>
);

const SplittedFile = ({ file }: { file: UploadedFile }) => (
  <div>
    <div className="flex items-center justify-between px-3 py-2">
      <div className="flex items-center gap-2">
        <File className="w-4 h-4 text-gray-500" />
        <span className="font-medium">{file.name}</span>
      </div>
      <FileStatusBadge value={file.status} showText={false} />
    </div>
    <div className="space-y-2 bg-gray-50 dark:bg-gray-800 px-3 py-2">
      {file.documents!.map((doc, docIndex) => (
        <div key={docIndex} className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm">{doc.name}</span>
          </div>
          <DocumentStatusBadge status={doc.status} className="py-0.5 px-3 text-xs rounded-full" />
        </div>
      ))}
    </div>
  </div>
);

const UploadedFileList = ({ files }: FileListProps) => {
  return (
    <div className="bg-white dark:bg-gray-900 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 max-h-full overflow-y-auto">
      {files.map((file) => (
        <div key={file.id}>
          {file.documents && file.documents.length > 0 ? (
            <SplittedFile file={file} />
          ) : (
            <ProcessingFile file={file} />
          )}
        </div>
      ))}
    </div>
  );
};

export default UploadedFileList;
