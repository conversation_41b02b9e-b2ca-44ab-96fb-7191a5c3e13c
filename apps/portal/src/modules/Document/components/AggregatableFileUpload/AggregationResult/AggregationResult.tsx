import { CheckCircle, XCircle } from "lucide-react";
import { ResultItem } from "../types";

interface AggregationResultListProps {
  results: ResultItem[];
}

function AggregationResultList({ results }: AggregationResultListProps) {
  return (
    <div className="space-y-2 max-h-full">
      {results.map((result, index) => (
        <div key={index} className="bg-white border rounded-lg shadow-sm p-3">
          <div className="flex items-center mb-2">
            {result.success ? (
              <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
            ) : (
              <XCircle className="h-4 w-4 text-red-500 mr-2" />
            )}
            <span className="font-semibold text-sm capitalize">{result.action}</span>
          </div>
          <div className="text-xs space-y-1">
            <a href={`/documents/${result.documentId}`} className="text-blue-600 hover:underline">
              {result.documentName}
            </a>
            {result.shipmentId && (
              <p>
                <span className="font-medium">Shipment ID:</span> {result.shipmentId}
              </p>
            )}
            {!result.success && result.error && <p className="text-red-600 mt-1">{result.error}</p>}
          </div>
        </div>
      ))}
    </div>
  );
}

export default AggregationResultList;
