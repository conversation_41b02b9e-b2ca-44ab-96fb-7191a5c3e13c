import { DocumentStatus, FileStatus } from "@/modules/Document/Types.document";
import { UploadedFile } from "../types";

export const uploadedFileFixture: UploadedFile[] = [
  {
    id: 1,
    name: "file1.pdf",
    status: FileStatus.PROCESSED,
    documents: [
      {
        id: 101,
        name: "document1",
        status: DocumentStatus.EXTRACTED
      },
      {
        id: 102,
        name: "document2",
        status: DocumentStatus.PENDING
      }
    ]
  },
  {
    id: 2,
    name: "file2.docx",
    status: FileStatus.PROCESSED,
    documents: [
      {
        id: 201,
        name: "document3",
        status: DocumentStatus.PENDING
      }
    ]
  },
  {
    id: 3,
    name: "file3.docx",
    status: FileStatus.SPLITTING
  },
  {
    id: 4,
    name: "file4.docx",
    status: FileStatus.PENDING
  }
];
