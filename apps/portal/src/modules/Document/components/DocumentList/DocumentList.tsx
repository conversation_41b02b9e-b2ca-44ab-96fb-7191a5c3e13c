import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";
import { useColumnVisibility } from "@/modules/Settings/Hooks.settings";
import { ShipmentContext } from "@/modules/Shipment/Types.shipment";
import { DownloadIcon, RefreshCwIcon } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { Outlet, useNavigate, useOutletContext } from "react-router-dom";
import { Button, ColumnSelector, Header, Table } from "ui";
import { SortOrder } from "utils";
import { FileUploadProgress } from "..";
import { DOCUMENT_TABLE_KEY } from "../../Constant.document";
import { useGetDocumentList } from "../../queries";
import { documentTableSchema } from "../../Schema.documents";
import { downloadShipmentFiles } from "../../services/File.service";
import { Document, DocumentColumn, GetDocumentList } from "../../Types.document";
import { documentIsProcessing } from "../../Utils.document";

const DownloadAllFileButton = () => {
  const { shipment } = useOutletContext<ShipmentContext>();
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownloadShipmentFiles = useCallback(() => {
    setIsDownloading(true);
    downloadShipmentFiles({ id: shipment?.id }).then((res) => {
      const url = window.URL.createObjectURL(res);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `shipment-${shipment?.hblNumber}.zip`);
      document.body.appendChild(link);
      link.click();
      setIsDownloading(false);
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success("Files downloaded");
    });
  }, [shipment?.id]);

  return (
    <Button
      label="Download"
      icon={<DownloadIcon size={16} />}
      onClick={handleDownloadShipmentFiles}
      loading={isDownloading}
    />
  );
};

const DocumentList = () => {
  const { shipment } = useOutletContext<ShipmentContext>();
  const navigate = useNavigate();
  const [filters, setFilters] = useState<GetDocumentList>();
  const [isProcessing, setIsProcessing] = useState(false);
  const { canCrudBackoffice } = useBackofficePermission();

  const { visibleColumns, handleToggleColumn } = useColumnVisibility({
    defaultColumns: documentTableSchema,
    key: DOCUMENT_TABLE_KEY
  });

  const { data, error, refetch, isFetching } = useGetDocumentList(
    {
      shipmentId: shipment?.id,
      sortBy: DocumentColumn.createDate,
      sortOrder: SortOrder.DESC,
      ...filters
    },
    isProcessing ? 5000 : undefined
  );

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  useEffect(() => {
    setIsProcessing(documentIsProcessing(data?.documents ?? []));
  }, [data]);

  const onFileProcessed = useCallback(() => {
    toast.success("File processed successfully");
    refetch();
  }, [refetch]);

  const handleRowClick = (document: Document) => {
    navigate(document.id.toString());
  };

  return (
    <div>
      <Header title={<h5>Documents</h5>} containerStyle="py-2 min-h-0">
        <div className="flex items-center gap-3">
          <ColumnSelector
            columns={visibleColumns}
            onToggleColumn={handleToggleColumn}
            containerStyle="ml-auto"
          />
          <Button label="Refresh" icon={<RefreshCwIcon size={16} />} onClick={refetch} loading={isFetching} />

          <DownloadAllFileButton />
        </div>
      </Header>

      <Table
        schema={visibleColumns}
        data={data?.documents}
        isLoading={isFetching}
        getRowStyle={(row) => {
          return row.isHidden ? "opacity-50" : "";
        }}
        header={
          canCrudBackoffice && (
            <div className="w-full py-2 bg-white dark:bg-transparent">
              <FileUploadProgress shipmentId={shipment?.id} onFileProcessed={onFileProcessed} />
            </div>
          )
        }
        onClick={handleRowClick}
        onSort={(key?: string, sortOrder?: SortOrder) => {
          setFilters((prev) => ({
            ...prev,
            sortBy: key as DocumentColumn,
            sortOrder
          }));
        }}
        sortKey={filters?.sortBy}
        sortDirection={filters?.sortOrder}
      />
      <Outlet />
    </div>
  );
};

export default DocumentList;
