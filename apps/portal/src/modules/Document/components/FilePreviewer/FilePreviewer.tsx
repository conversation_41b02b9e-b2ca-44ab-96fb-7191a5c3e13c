import { EyeIcon, FileTextIcon, SidebarIcon } from "lucide-react";
import { useMemo, useState } from "react";
import Markdown from "react-markdown";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";
import { SegmentedControl } from "ui";
import SheetPreviewer from "./SheetPreviewer";

type FilePreviewerProps = {
  url: string;
  mimeType: string;
  startPage: number;
  parseResult: any;
};

export default function FilePreviewer({ url, mimeType, startPage, parseResult }: FilePreviewerProps) {
  const previewer = useMemo(() => {
    if (mimeType.startsWith("image/")) {
      return <img className="h-full w-auto mx-auto" src={url} />;
    }

    switch (mimeType) {
      // pdf
      case "application/pdf":
        return <embed className="w-full h-full" src={`${url}#page=${startPage}`} />;
      // docx
      case "application/msword":
      case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        return (
          <div className="text-sm text-gray-500 w-full h-full flex items-center justify-center">
            Document Preview is not available for this file type
          </div>
        );
      // xlsx
      case "application/vnd.ms-excel":
      case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        return <SheetPreviewer url={url} />;
      // other
      default:
        return <div>Cannot Preview Document</div>;
    }
  }, [mimeType, startPage, url]);

  const [currentTab, setCurrentTab] = useState<string>("preview");

  const markdownPreviewer = useMemo(() => {
    if (!parseResult) return null;
    return (
      <div className="flex flex-col gap-3 bg-gray-100 p-3 overflow-auto w-full h-full">
        {JSON.parse(parseResult).pages.map((page: any) => {
          const sanitized = page.md.trim().startsWith("```markdown")
            ? page.md.trim().slice(11, page.md.endsWith("```") ? -3 : undefined)
            : page.md;

          return (
            <div key={page.page} className="bg-white p-3 rounded-lg prose max-w-none">
              <div className="text-xs text-gray-500 mb-2">Page {page.page}</div>
              <Markdown rehypePlugins={[rehypeRaw]} remarkPlugins={[remarkGfm]}>
                {sanitized}
              </Markdown>
            </div>
          );
        })}
      </div>
    );
  }, [parseResult]);

  return (
    <div className="w-full h-full flex flex-col">
      <SegmentedControl
        size="sm"
        className="py-2 justify-center"
        items={[
          {
            id: "preview",
            label: "Preview",
            icon: <EyeIcon className="size-4" />
          },
          {
            id: "markdown",
            label: "Markdown",
            icon: <FileTextIcon className="size-4" />
          },
          {
            id: "side-by-side",
            label: "Side by Side",
            icon: <SidebarIcon className="size-4" />
          }
        ]}
        onChange={setCurrentTab}
        value={currentTab}
      />
      <>
        {currentTab === "preview" && previewer}
        {currentTab === "markdown" && markdownPreviewer}
        {currentTab === "side-by-side" && (
          <div className="flex flex-row gap-3 w-full h-full">
            <div className="w-1/2">{previewer}</div>
            <div className="w-1/2">{markdownPreviewer}</div>
          </div>
        )}
      </>
    </div>
  );
}
