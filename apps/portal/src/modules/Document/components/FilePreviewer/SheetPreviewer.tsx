import "react-data-grid/lib/styles.css";
import "./sheet-previewer.css";

import { useStore } from "@/bootstrap/Store.bootstrap";
import { useEffect, useState } from "react";
import DataGrid, { Column } from "react-data-grid";
import { twMerge } from "tailwind-merge";
import { Tabs } from "ui";
import { read, utils, WorkBook, WorkSheet } from "xlsx";

type SheetPreviewerProps = {
  url: string;
};

type DataSet = {
  [index: string]: WorkSheet;
};
type Row = any[];
type AOAColumn = Column<Row>;
type RowCol = { rows: Row[]; columns: AOAColumn[] };

// This was copied from SheetJS, currently not used
// function arrayify(rows: any[]): Row[] {
//   return rows.map((row) => {
//     if (Array.isArray(row)) return row;
//     var length = Object.keys(row).length;
//     for (; length > 0; --length) if (row[length - 1] != null) break;
//     return Array.from({ length, ...row });
//   });
// }

const getRowsCols = (data: DataSet, sheetName: string): RowCol => ({
  rows: utils.sheet_to_json<Row>(data[sheetName], {
    header: 1,
    raw: false
  }),
  columns: Array.from(
    {
      length: utils.decode_range(data[sheetName]["!ref"] || "A1").e.c + 1
    },
    (_, i) => ({
      key: String(i),
      name: utils.encode_col(i)
    })
  )
});

const SheetPreviewer = ({ url }: SheetPreviewerProps) => {
  const [columns, setColumns] = useState<AOAColumn[]>([]);
  const [rows, setRows] = useState<Row[]>([]);
  const [sheets, setSheets] = useState<string[]>([]);
  const [workBook, setWorkBook] = useState<DataSet>();

  const { darkMode } = useStore().theme;

  const loadFile = async (url: string): Promise<WorkBook> => {
    const file = await fetch(url);
    const arrayBuffer = await file.arrayBuffer();

    return read(arrayBuffer, {
      cellStyles: true
    });
  };

  const loadRowsCols = (data: DataSet, sheetName: string) => {
    const { rows, columns } = getRowsCols(data, sheetName);
    setRows(rows);
    setColumns(columns);
  };

  const selectSheet = (index: number) => {
    if (!workBook) return;
    loadRowsCols(workBook, sheets[index]);
  };

  const initSheet = async () => {
    try {
      const data = await loadFile(url);
      setWorkBook(data.Sheets);
      setSheets(data.SheetNames);
      loadRowsCols(data.Sheets, data.SheetNames[0]);
    } catch (error) {
      console.error("Error loading file", error);
    }
  };

  useEffect(() => {
    initSheet();
  }, [url]);

  return (
    <>
      {sheets.length > 1 && (
        <Tabs
          onTabChange={selectSheet}
          items={sheets.map((sheet) => ({
            title: sheet,
            content: <span />
          }))}
        />
      )}
      <DataGrid
        className={twMerge(darkMode ? "rdg-dark" : "rdg-light", "h-full")}
        columns={columns}
        rows={rows}
      />
    </>
  );
};

export default SheetPreviewer;
