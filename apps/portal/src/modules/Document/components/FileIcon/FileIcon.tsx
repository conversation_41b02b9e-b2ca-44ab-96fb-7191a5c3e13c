import { LucideFile, LucideFileImage, LucideFileSpreadsheet, LucideFileText } from "lucide-react";
import { twMerge } from "tailwind-merge";

type FileIconProps = {
  mimeType: string;
  className?: string;
};

const FileIcon = ({ mimeType, className }: FileIconProps) => {
  if (mimeType.startsWith("image/")) {
    return <LucideFileImage className={twMerge("text-primary", className)} />;
  }

  switch (mimeType) {
    case "application/pdf":
      return <LucideFileText className={twMerge("text-red-600", className)} />;
    case "application/msword":
    case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
      return <LucideFileText className={twMerge("text-blue-600", className)} />;
    case "application/vnd.ms-excel":
    case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
      return <LucideFileSpreadsheet className={twMerge("text-green-600", className)} />;
    default:
      return <LucideFile className={className} />;
  }
};

export default FileIcon;
