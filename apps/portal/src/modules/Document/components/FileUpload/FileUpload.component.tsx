import { BaseError } from "@/common/Types.common";
import { UploadFilesResponse } from "@/modules/Document/Types.document";
import { useFormik } from "formik";
import { useCallback, useEffect } from "react";
import toast from "react-hot-toast";
import { FileUploader, Icons } from "ui";
import { ACCEPT_FILE_TYPES } from "../../Constant.document";
import { UploadFilesParams } from "../../Types.document";
import { useUploadFiles } from "../../mutations";

interface FileUploadProps {
  onFileUploaded?: (files: UploadFilesResponse) => void;
  onUploading?: (isUploading: boolean) => void;
  shipmentId?: number;
  multiple?: boolean;
}

const FileUpload = ({ onFileUploaded, onUploading, shipmentId, multiple = false }: FileUploadProps) => {
  const uploadFiles = useUploadFiles();

  useEffect(() => {
    return () => {
      if (uploadFiles.isPending) {
        uploadFiles.reset();
      }
    };
  }, [uploadFiles]);

  const onSubmit = useCallback(
    async (params: UploadFilesParams) => {
      try {
        onUploading?.(true);
        const result = await uploadFiles.mutateAsync({
          ...params,
          shipmentId
        });
        onFileUploaded?.(result);
      } catch (error) {
        toast.error((error as BaseError).message);
      } finally {
        onUploading?.(false);
      }
    },
    [onUploading, uploadFiles, shipmentId, onFileUploaded]
  );

  const formik = useFormik({
    initialValues: {
      files: []
    },
    onSubmit: onSubmit
  });

  const handleFileChange = (files: File[]) => {
    formik.setFieldValue("files", files);
    formik.submitForm();
  };

  return (
    <div>
      <div className="relative">
        <FileUploader
          name="files"
          accept={ACCEPT_FILE_TYPES}
          onChange={handleFileChange}
          multiple={multiple}
        />

        {uploadFiles.isPending && (
          <div className="absolute inset-0 bg-white flex flex-col items-center justify-center rounded-lg">
            <Icons.Loader className="size-8 m-0" />
            <div className="text-sm font-medium text-primary mt-2">Uploading files...</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FileUpload;
