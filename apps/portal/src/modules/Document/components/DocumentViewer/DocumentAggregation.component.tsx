import { LucideExternalLink } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { Button } from "ui";
import { DocumentAggregation } from "../../Types.document";
import { AggregationStatusBadge } from "../AggregationStatusBadge";

const AggregationCard = ({ aggregation }: { aggregation: DocumentAggregation }) => {
  return (
    <div className="flex items-end gap-2">
      <AggregationStatusBadge status={aggregation.status} />
      <Link to={`/document-aggregation/${aggregation.id}`}>
        <LucideExternalLink className="w-4 h-4 text-gray-400 inline hover:text-primary" />
      </Link>
    </div>
  );
};

const DocumentAggregationPane = ({
  aggregation,
  createDocumentAggregation
}: {
  aggregation?: DocumentAggregation;
  createDocumentAggregation: () => void;
}) => {
  return (
    <div>
      <div className="flex flex-col gap-2">
        {aggregation && (
          <div>
            <AggregationCard aggregation={aggregation} />
          </div>
        )}
        {!aggregation && (
          <div>
            <Button label="Aggregate" onClick={createDocumentAggregation} />
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentAggregationPane;
