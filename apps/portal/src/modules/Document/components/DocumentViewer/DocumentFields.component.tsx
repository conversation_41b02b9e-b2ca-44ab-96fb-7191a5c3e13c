import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ik<PERSON><PERSON>ider, useFormik } from "formik";
import {
  ChevronDownIcon,
  ChevronUpIcon,
  LockIcon,
  Maximize2Icon,
  Minimize2Icon,
  TrashIcon,
  UnlockIcon
} from "lucide-react";
import { forwardRef, useMemo, useReducer, useState } from "react";
import { twMerge } from "tailwind-merge";
import { Alert, Button, Modal } from "ui";
import { saveDocumentFieldSchema } from "../../Schema.documents";
import { DocumentField, DocumentValidationError, ProcessDocumentModel } from "../../Types.document";
import { DocumentFieldSelect } from "./components";
import { Array<PERSON>ield<PERSON>enderer, FieldRenderer, ObjectFieldRenderer } from "./DocumentFieldRenderer.component";

type BatchEditDocumentFieldParams = {
  fields: DocumentField[];
  deletedIds: number[];
};

const LockToggle = forwardRef<
  HTMLInputElement,
  { locked: boolean } & React.InputHTMLAttributes<HTMLInputElement>
>(({ locked, onChange, checked: _, ...props }, ref) => {
  return (
    <label>
      <input type="checkbox" ref={ref} checked={locked} onChange={onChange} className="hidden" {...props} />
      {locked ? (
        <LockIcon className="size-3 cursor-pointer text-primary" />
      ) : (
        <UnlockIcon className="size-3 cursor-pointer text-primary" />
      )}
      <span className="sr-only">{locked ? "Locked" : "Unlocked"}</span>
    </label>
  );
});

const MaximizeMinimizeIconRenderer = ({
  maximized,
  toggleMaximized
}: {
  maximized: boolean;
  toggleMaximized: () => void;
}) => {
  const Icon = maximized ? Minimize2Icon : Maximize2Icon;

  return <Icon className="size-3 cursor-pointer stroke-2" onClick={toggleMaximized} />;
};

const CollapseIconRenderer = ({
  collapsed,
  toggleCollapsed
}: {
  collapsed: boolean;
  toggleCollapsed: () => void;
}) => {
  const Icon = collapsed ? ChevronDownIcon : ChevronUpIcon;

  return <Icon className="size-5 cursor-pointer" onClick={toggleCollapsed} />;
};

const RootFieldRenderer = ({
  isEditing,
  field,
  selectedFields,
  onRemove,
  formik,
  index,
  documentFields
}: {
  index: number;
  isEditing: boolean;
  field: DocumentField;
  onRemove: () => void;
  formik: ReturnType<typeof useFormik<BatchEditDocumentFieldParams>>;
  selectedFields: string[];
  documentFields: { label: string; value: string }[];
}) => {
  const hasNested = field.dataType === "array" || field.dataType === "object";

  const [collapsed, setCollapsed] = useState(false);
  const [maximized, setMaximized] = useState(false);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  const toggleMaximized = () => {
    setMaximized(!maximized);
  };

  const filteredDocumentFields = useMemo(() => {
    return documentFields.filter((df) => df.value.split(".").length === 1);
  }, [documentFields, field.name]);

  const NestedFieldLabel = useMemo(() => {
    return (
      <div className="flex justify-between items-center px-3 py-2 bg-gray-50">
        {isEditing ? (
          <div className="flex gap-2 items-center justify-center">
            <TrashIcon onClick={onRemove} className="size-4 cursor-pointer text-danger" />
            <LockToggle
              locked={field.locked}
              name={`fields.${index}.locked`}
              onChange={formik.handleChange}
            />
            <div className="capitalize text-sm">{field.name}</div>
          </div>
        ) : (
          <div className="flex gap-2 items-center">
            {field.locked && <LockIcon className="size-3 cursor-pointer text-primary" />}
            <div className="capitalize text-sm">{field.name}</div>
          </div>
        )}
        <div className="flex gap-2 items-center">
          <MaximizeMinimizeIconRenderer maximized={maximized} toggleMaximized={toggleMaximized} />
          <CollapseIconRenderer collapsed={collapsed} toggleCollapsed={toggleCollapsed} />
        </div>
      </div>
    );
  }, [field.name, isEditing, selectedFields, filteredDocumentFields, collapsed, maximized]);

  const FieldLabel = useMemo(() => {
    return isEditing ? (
      <div className="flex gap-2 items-center justify-center px-2">
        <TrashIcon onClick={onRemove} className="size-4 cursor-pointer text-danger" />
        <LockToggle locked={field.locked} name={`fields.${index}.locked`} onChange={formik.handleChange} />
        <DocumentFieldSelect
          fieldName={field.name}
          name={`fields.${index}.name`}
          availableFields={filteredDocumentFields}
          selectedFields={selectedFields}
          onChange={formik.handleChange}
        />
      </div>
    ) : (
      <div className="flex gap-2 items-center px-2 py-2">
        {field.locked && <LockIcon className="size-3 cursor-pointer text-primary" />}
        <div className="text-xs font-bold">{field.name}</div>
      </div>
    );
  }, [field.name, isEditing, selectedFields, filteredDocumentFields]);

  return (
    <>
      {!maximized && (
        <>
          <div className={twMerge(hasNested ? "col-span-2 rounded border" : "border-r")} key={field.name}>
            {hasNested ? NestedFieldLabel : FieldLabel}
          </div>
          <div className={twMerge(hasNested ? "col-span-2" : "col-span-1", collapsed ? "hidden" : "")}>
            {field.dataType === "array" ? (
              <ArrayFieldRenderer
                isEditing={isEditing}
                name={`fields.${index}.value`}
                availableFields={documentFields}
                field={field}
                onChange={formik.handleChange}
              />
            ) : field.dataType === "object" ? (
              <ObjectFieldRenderer
                isEditing={isEditing}
                field={field}
                name={`fields.${index}.value`}
                onChange={formik.handleChange}
                availableFields={documentFields}
              />
            ) : (
              <FieldRenderer
                isEditing={isEditing}
                name={`fields.${index}.value`}
                field={field}
                onChange={formik.handleChange}
              />
            )}
          </div>
        </>
      )}
      {maximized && (
        <Modal
          show
          id="maximized-field-modal"
          onClose={() => setMaximized(false)}
          bodyStyle="p-0"
          size="6xl"
          title={field.name}
        >
          {field.dataType === "array" ? (
            <ArrayFieldRenderer
              isEditing={isEditing}
              name={`fields.${index}.value`}
              availableFields={documentFields}
              field={field}
              onChange={formik.handleChange}
            />
          ) : field.dataType === "object" ? (
            <ObjectFieldRenderer
              isEditing={isEditing}
              field={field}
              name={`fields.${index}.value`}
              onChange={formik.handleChange}
              availableFields={documentFields}
            />
          ) : null}
        </Modal>
      )}
    </>
  );
};

const FieldValidationErrorList = ({
  validationErrors = []
}: {
  validationErrors: DocumentValidationError[];
}) => {
  if (validationErrors.length === 0) {
    return null;
  }

  const errorMessage = {
    subtotal_mismatch: "The subtotal does not match with the sum of line values",
    total_mismatch: "The total does not match with subtotal after other cost and discounts",
    missing_cargo_control_number: "The cargo control number is missing"
  };

  return (
    <Alert
      type="error"
      className="mb-2"
      title="There are some errors in the document"
      message={
        <ul className="gap-y-0.5 flex flex-col">
          {validationErrors.map((error) => (
            <li key={error.id}>{errorMessage[error.code]}</li>
          ))}
        </ul>
      }
      compact
    />
  );
};

const DocumentFields = ({
  fields,
  documentFields,
  validationErrors = [],
  onSubmit,
  processHandler
}: {
  fields: DocumentField[];
  documentFields: { label: string; value: string }[];
  validationErrors: DocumentValidationError[];
  processHandler: (model: ProcessDocumentModel) => void;
  onSubmit: (fields: DocumentField[], deletedIds: number[]) => Promise<void>;
}) => {
  const [isEditing, toggleIsEditing] = useReducer((state: boolean) => !state, false);

  const sortedFields = useMemo(() => {
    const fieldOrder = [
      "hblNumber",
      "cargoControlNumber",
      "CCN",
      "carrierCode",
      "shipper",
      "consignee",
      "forwarder",
      "portOfLoading",
      "portOfDischarge",
      "placeOfDelivery",
      "portOfExit",
      "portOfEntry",
      "destinationAirportCode",
      "portCode",
      "subLocation",
      "vessel",
      "flight",
      "voyageNumber",
      "etd",
      "etaPort",
      "etaDestination",
      "containerNumber",
      "invoiceNumber",
      "poNumber",
      "invoiceDate",
      "vendor",
      "purchaser",
      "shipTo",
      "commercialInvoiceLines",
      "countryOfOrigin",
      "currencyCode",
      "invoiceSubtotal",
      "discount",
      "miscCost",
      "packCost",
      "transCost",
      "tax",
      "invoiceTotal",
      "netWeight",
      "grossWeight",
      "weight",
      "weightUOM",
      "volume",
      "volumeUOM",
      "quantity",
      "quantityUOM",
      "numberOfPackages",
      "packageUOM"
    ];
    return [...fields].sort((a, b) => {
      const aIndex = fieldOrder.indexOf(a.name);
      const bIndex = fieldOrder.indexOf(b.name);

      if (aIndex !== -1 && bIndex !== -1) {
        return aIndex - bIndex;
      }

      if (aIndex !== -1) {
        return -1;
      }

      if (bIndex !== -1) {
        return 1;
      }

      return a.name.localeCompare(b.name);
    });
  }, [fields]);

  const formik = useFormik<BatchEditDocumentFieldParams>({
    initialValues: {
      fields: sortedFields,
      deletedIds: []
    },
    validateOnBlur: true,
    enableReinitialize: true,
    validationSchema: saveDocumentFieldSchema,
    onSubmit: async (values) => {
      await onSubmit(values.fields, values.deletedIds);
      toggleIsEditing();
    }
  });

  const handleSave = () => {
    formik.handleSubmit();
  };

  const selectedFields = useMemo(() => {
    return formik.values.fields.map((f: DocumentField) => f.name);
  }, [formik.values.fields]);

  const isAllLocked = useMemo(() => {
    return formik.values.fields.every((f) => f.locked);
  }, [formik.values.fields]);

  return (
    <FormikProvider value={formik}>
      <FieldArray
        name="fields"
        render={(arrayHelpers) => (
          <>
            <div className="flex justify-between gap-2 items-center mb-2 mt-4">
              <div className="flex gap-2">
                {isEditing && <Button label="Save" onClick={handleSave} />}
                <Button
                  kind={isEditing ? "cancel" : "edit"}
                  label={isEditing ? "Cancel" : "Edit"}
                  onClick={() => toggleIsEditing()}
                />
                {isEditing && (
                  <>
                    <Button
                      label={isAllLocked ? "Unlock All" : "Lock All"}
                      onClick={() => {
                        formik.setFieldValue(
                          "fields",
                          formik.values.fields.map((f) => ({ ...f, locked: !isAllLocked }))
                        );
                      }}
                    />
                    <Button
                      label="Add Field"
                      onClick={() =>
                        arrayHelpers.insert(0, {
                          name: "",
                          value: "",
                          dataType: "string"
                        })
                      }
                    />
                  </>
                )}
                {!isEditing && (
                  <div className="flex gap-2">
                    {/* <Button
                      label="Re-extract data (o3-mini)"
                      onClick={() => processHandler(ProcessDocumentModel.O3_MINI)}
                    /> */}
                    <Button
                      label="Re-extract data"
                      kind="outline"
                      onClick={() => processHandler(ProcessDocumentModel.GPT_4O)}
                    />
                  </div>
                )}
              </div>
            </div>

            <FieldValidationErrorList validationErrors={validationErrors} />
            {/* <Collapse
              activeKeys={activeKeys}
              onChange={setActiveKeys}
              items={getFieldsItem(arrayHelpers)}
              headerStyle="px-0"
              contentStyle="px-0"
            /> */}
            <div className="grid grid-cols-2 divide-x divide-y border rounded-md">
              {formik.values.fields.map((field, index) => (
                <RootFieldRenderer
                  key={index}
                  index={index}
                  field={field}
                  formik={formik}
                  isEditing={isEditing}
                  onRemove={() => {
                    arrayHelpers.remove(index);
                    // add to deletedIds
                    formik.setFieldValue("deletedIds", [...formik.values.deletedIds, field.id]);
                  }}
                  selectedFields={selectedFields}
                  documentFields={documentFields}
                />
              ))}
            </div>
          </>
        )}
      />
    </FormikProvider>
  );
};

export default DocumentFields;
