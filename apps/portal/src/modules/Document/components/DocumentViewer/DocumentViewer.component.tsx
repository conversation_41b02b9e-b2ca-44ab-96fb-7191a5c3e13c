import { BaseError } from "@/common/Types.common";
import { LucideChevronLeft, LucideChevronRight } from "lucide-react";
import { useMemo, useReducer } from "react";
import toast from "react-hot-toast";
import { twMerge } from "tailwind-merge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "ui";
import { FilePreviewer } from "..";
import {
  BatchSaveDocumentFieldParams,
  Document,
  DocumentField,
  DocumentStatus,
  ProcessDocumentModel,
  Shipment
} from "../../Types.document";
import { useProcessDocument, useUpdateDocumentFields, useUpdateDocumentShipment } from "../../mutations";
import { useDownloadFile } from "../../queries/DownloadFile.query";

// internal components
import { useDismissDocumentShipmentMismatchWarning } from "../../mutations/Document.mutation";
import { useCreateDocumentAggregation } from "../../mutations/DocumentAggregation.mutation";
import { useGetDocumentAggregationForDocument } from "../../queries";
import DocumentAggregationPane from "./DocumentAggregation.component";
import DocumentFields from "./DocumentFields.component";
import DocumentFieldsExtracting from "./DocumentFieldsExtracting.component";
import DocumentMeta from "./DocumentMeta.component";
import DocumentRelationPane from "./DocumentRelation.component";
import DocumentShipment from "./DocumentShipment.component";

interface DocumentViewerProps {
  document?: Document;
  loading: boolean;
  verticalSplit?: boolean;
  refetch: () => void;
}

const DocumentViewer = ({ document, loading, refetch, verticalSplit = false }: DocumentViewerProps) => {
  // TODO: temporary type, api needs to be improved first
  const { data } = useDownloadFile({ id: document?.file.id });

  const url = useMemo(() => data?.url ?? "", [data]);

  const [isOpen, toggleIsOpen] = useReducer((state: boolean) => !state, true);

  const documentFields = useMemo(() => {
    return (
      document?.documentType?.fields.map((field) => ({
        label: field.name,
        value: field.name
      })) ?? []
    );
  }, [document]);

  const updateDocumentFields = useUpdateDocumentFields();

  const handleSaveFields = async (fields: DocumentField[], deletedIds: number[]) => {
    try {
      const formatParams: BatchSaveDocumentFieldParams = {
        id: document?.id,
        create: fields.filter((f) => !f.id),
        edit: fields.filter((f) => typeof f.id === "number").map((f) => ({ ...f, id: f.id as number })),
        delete: deletedIds
      };
      await updateDocumentFields.mutateAsync(formatParams);
      toast.success("Document fields updated successfully");
      refetch();
    } catch (error) {
      toast.error((error as BaseError).message);
    }
  };

  const updateDocumentShipment = useUpdateDocumentShipment();

  const updateShipment = async (shipment: Shipment) => {
    return updateDocumentShipment
      .mutateAsync({
        id: document?.id,
        shipmentId: shipment.id
      })
      .then(() => {
        toast.success("Document shipment updated successfully");
        refetch();
      })
      .catch((error) => {
        toast.error((error as BaseError).message);
      });
  };

  const processDocument = useProcessDocument();

  const process = (model: ProcessDocumentModel) => {
    processDocument
      .mutateAsync({ id: document?.id, model })
      .then(() => {
        toast.success("Document submitted for processing");
        refetch();
      })
      .catch((error) => {
        toast.error(error.message);
      });
  };

  const dismissShipmentMismatchWarningMutation = useDismissDocumentShipmentMismatchWarning();

  const dismissShipmentMismatchWarning = () => {
    return dismissShipmentMismatchWarningMutation
      .mutateAsync(document!.id)
      .then(() => {
        refetch();
      })
      .catch((error) => {
        toast.error(error.message);
      });
  };

  const { data: documentAggregation } = useGetDocumentAggregationForDocument({
    id: document?.id
  });

  const createDocumentAggregationMutation = useCreateDocumentAggregation();

  const createDocumentAggregation = () => {
    createDocumentAggregationMutation.mutateAsync({ id: document!.id });
  };

  const collapseItems = useMemo(() => {
    return [
      {
        key: "shipment",
        title: "Shipment",
        children: (
          <DocumentShipment
            isShipmentMismatch={document?.isShipmentMismatch ?? false}
            shipment={document?.shipment}
            updateShipment={updateShipment}
            dismissShipmentMismatchWarning={dismissShipmentMismatchWarning}
          />
        )
      },
      {
        key: "relation",
        title: "Relation",
        children: <DocumentRelationPane document={document!} />
      },
      {
        key: "aggregration",
        title: "Aggregration",
        children: (
          <DocumentAggregationPane
            aggregation={documentAggregation}
            createDocumentAggregation={createDocumentAggregation}
          />
        )
      },
      {
        key: "fields",
        title: "Fields",
        children: (
          <>
            {document?.status === DocumentStatus.EXTRACTING ? (
              <DocumentFieldsExtracting />
            ) : (
              <DocumentFields
                fields={document?.fields ?? []}
                documentFields={documentFields}
                validationErrors={document?.validationErrors ?? []}
                onSubmit={handleSaveFields}
                processHandler={process}
              />
            )}
          </>
        )
      }
    ];
  }, [document, documentAggregation]);

  const memoizedFilePreviewer = useMemo(() => {
    if (loading || !document || url === "") return null;
    return (
      <FilePreviewer
        url={url}
        mimeType={document.file.mimeType}
        startPage={document.startPage}
        parseResult={document.file.parseResult}
      />
    );
  }, [url, loading, document]);

  return (
    <div className={twMerge("flex h-full", verticalSplit ? "flex-col divide-y" : "flex-row divide-x")}>
      {/* preview pane */}
      <div
        className={twMerge(
          "transition-all flex flex-col",
          verticalSplit ? "h-1/2 w-full" : "h-full",
          isOpen ? "w-1/2 flex-1" : "w-0"
        )}
      >
        {loading && <Icons.Loader />}
        {memoizedFilePreviewer}
      </div>
      {/* document fields */}
      <div
        className={twMerge(
          "relative transition-all",
          isOpen ? "w-1/2" : "w-full",
          verticalSplit ? "h-1/2 w-full" : "h-auto"
        )}
      >
        {!verticalSplit && (
          <div className="w-4 absolute top-0 left-0 bottom-0 group">
            <Button
              className="opacity-0 group-hover:opacity-100 absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 transition-opacity"
              shape="rounded"
              icon={
                isOpen ? (
                  <LucideChevronLeft className="size-6 -left-[1px]" />
                ) : (
                  <LucideChevronRight className="size-6 -right-[1px]" />
                )
              }
              onClick={toggleIsOpen}
            />
          </div>
        )}
        <div className="h-full overflow-y-auto [scrollbar-gutter:stable]">
          {loading && (
            <div className="h-full flex justify-center items-center">
              <Icons.Loader />
            </div>
          )}
          {!loading && document && (
            <>
              <div className="p-4 border-b sticky top-0 z-10 bg-white">
                <DocumentMeta document={document} />
              </div>
              <Collapse
                items={collapseItems}
                activeKeys={["document", "fields", "shipment", "aggregration", "relation"]}
              />
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentViewer;
