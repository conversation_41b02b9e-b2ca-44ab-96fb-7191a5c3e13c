import { Link } from "react-router-dom";
import { Document } from "../../Types.document";

const RelationCard = ({ document }: { document: Document }) => {
  return (
    <Link to={`/document/${document.id}`} replace>
      <div className="border rounded-md p-2 hover:bg-gray-100">
        <div className="font-medium">{document.name}</div>
        <div className="text-sm">{document.file?.name}</div>
      </div>
    </Link>
  );
};

const DocumentRelationPane = ({ document }: { document: Document }) => {
  return (
    <div>
      {document.reference && <RelationCard document={document.reference} />}
      {document.referencedBy && <RelationCard document={document.referencedBy} />}

      {!document.reference && !document.referencedBy && (
        <div className="text-sm text-gray-500">This document is not related to any other document</div>
      )}
    </div>
  );
};

export default DocumentRelationPane;
