import { useFormik } from "formik";
import { Spark<PERSON><PERSON><PERSON>, TrashIcon } from "lucide-react";
import { ChangeEvent, ChangeEventH<PERSON>ler, Fragment, useEffect, useMemo } from "react";
import { twMerge } from "tailwind-merge";
import { Input } from "ui";
import { DocumentField } from "../../Types.document";
import { DocumentFieldSelect } from "./components";

import ArrayFieldRenderer from "./components/ArrayFieldRenderer.component";

// TODO: define type for obj
const KeyValueRenderer = ({ obj, depth, parentName }: { obj: any; depth: number; parentName: string }) => {
  const isNotNullObject = (obj: any) => obj !== null && typeof obj === "object";

  return (
    <Fragment key={parentName}>
      {Object.keys(obj).map((key) => {
        const isObj = isNotNullObject(obj[key]);
        const fullKey = parentName ? `${parentName}.${key}` : key;

        return (
          <Fragment key={fullKey}>
            <div
              className={twMerge("p-1", isObj ? "bg-primary/10 col-span-2" : "")}
              key={fullKey}
              style={{
                paddingLeft: `${depth * 0.5 - 0.25}rem`
              }}
            >
              <div className="font-medium text-xs">{key}</div>
            </div>

            {isObj && <KeyValueRenderer obj={obj[key]} depth={depth + 1} parentName={fullKey} />}
            {!isObj && (
              <div className="text-right text-sm px-1" key={fullKey + "_value"}>
                {obj[key]}
              </div>
            )}
          </Fragment>
        );
      })}
    </Fragment>
  );
};

type NestedField = {
  name: string;
  value?: string | number;
};

// TODO: value starts with "$" are generated by the system, don't allow editing
const ObjectFieldRenderer = ({
  isEditing,
  field,
  name,
  availableFields,
  onChange
}: {
  isEditing: boolean;
  field: DocumentField;
  name: string;
  availableFields: { label: string; value: string }[];
  onChange: ChangeEventHandler<HTMLInputElement>;
}) => {
  const sortOrder = ["name", "street", "city", "state", "postalCode", "country", "email", "phoneNumber"];
  const getParsedValue = () => {
    try {
      return Object.entries(JSON.parse(field.value))
        .map(
          ([k, v]) =>
            ({
              name: k,
              value: v
            }) as NestedField
        )
        .sort((a, b) => sortOrder.indexOf(a.name) - sortOrder.indexOf(b.name));
    } catch (error) {
      return [];
    }
  };

  const formik = useFormik({
    initialValues: {
      entries: getParsedValue()
    },
    onSubmit: () => {}
  });

  useEffect(() => {
    const serialized = JSON.stringify(
      formik.values.entries.reduce((acc: Record<string, string | undefined>, v) => {
        acc[v.name] = v.value?.toString();
        return acc;
      }, {})
    );

    onChange({
      target: {
        value: serialized,
        name: name
      }
    } as ChangeEvent<HTMLInputElement>);
  }, [formik.values.entries]);

  const selectedFields = useMemo(() => {
    return formik.values.entries.map((v) => v.name);
  }, [formik.values]);

  const filteredAvailableFields = useMemo(() => {
    return availableFields
      .filter((af) => {
        return af.value.startsWith(field.name + ".");
      })
      .map((af) => ({
        label: af.value.substring(field.name.length + 1),
        value: af.value.substring(field.name.length + 1)
      }));
  }, [availableFields, field.name, selectedFields]);

  return formik.values.entries.map((field, index) => {
    const isSystemGenerated = field.name.startsWith("$");

    return (
      <div key={field.name} className="grid grid-cols-2 gap-2">
        {isEditing && !isSystemGenerated ? (
          <div className="flex gap-2 items-center justify-center px-2">
            <TrashIcon
              onClick={() => {
                formik.values.entries.splice(index, 1);
                // TODO: this is a hack to force Formik to re-render
                formik.setValues(formik.values);
              }}
              className="size-4 cursor-pointer text-danger"
            />
            <DocumentFieldSelect
              name={`entries.${index}.name`}
              fieldName={field.name}
              onChange={formik.handleChange}
              availableFields={filteredAvailableFields}
              selectedFields={selectedFields}
            />
          </div>
        ) : (
          <div className="text-xs px-1 flex items-center">
            {isSystemGenerated ? <SparklesIcon className="size-3 mr-1 text-purple-700" /> : null}
            {field.name}
          </div>
        )}

        <div>
          {isSystemGenerated ? (
            <div className="italic flex items-center text-xs px-1">
              {JSON.stringify(field.value, null, 2)}
            </div>
          ) : (
            <div>
              {isEditing ? (
                <Input
                  name={`entries.${index}.value`}
                  value={field.value ?? ""}
                  onChange={formik.handleChange}
                />
              ) : (
                <div className="text-xs px-1">{field.value}</div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  });
};

const FieldRenderer = ({
  isEditing,
  name,
  field,
  onChange
}: {
  isEditing: boolean;
  name: string;
  field: DocumentField;
  onChange: ChangeEventHandler<HTMLInputElement>;
}) => {
  return isEditing ? (
    <Input value={field.value} name={name} onChange={onChange} />
  ) : (
    <div className={twMerge("text-xs px-1 text-left font-mono")}>{field.value ?? "(empty)"}</div>
  );
};

export { ArrayFieldRenderer, FieldRenderer, ObjectFieldRenderer };
