import { LucideExternalLink } from "lucide-react";
import { ReactNode } from "react";
import { <PERSON> } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import { Checkbox, Tooltip } from "ui";
import { getValueByPath } from "utils";
import { useSetDocumentVisibility } from "../../mutations/Document.mutation";
import { FilePages } from "../../Routes.documents";
import { Document, DocumentStatus, File } from "../../Types.document";
import DocumentStatusBadge from "../DocumentStatusBadge";

const DocumentMeta = ({ document }: { document: Document }) => {
  const setDocumentVisibility = useSetDocumentVisibility();

  const handleSetDocumentVisibility = (isHidden: boolean) => {
    return setDocumentVisibility.mutateAsync({ id: document.id, isHidden });
  };

  const fieldsForDisplay: Array<{
    key: string;
    label: string;
    className?: string;
    description?: string;
    formatter?: (value: any, row: Document) => ReactNode;
  }> = [
    {
      key: "file",
      label: "File",
      formatter: (value: File) => (
        <Link className="text-primary hover:underline" to={FilePages.Detail(value.id.toString())}>
          {value.name} <LucideExternalLink className="size-3 inline-block" />
        </Link>
      )
    },
    { key: "documentType.name", label: "Type" },
    { key: "name", label: "Name" },
    {
      key: "status",
      label: "Status",
      formatter: (value: DocumentStatus) => <DocumentStatusBadge status={value} />
    },
    {
      key: "createDate",
      label: "Created At",
      formatter: (value: string) => new Date(value).toLocaleString()
    },
    // {
    //   key: "startPage",
    //   label: "Page",
    //   formatter: (_value: number, row: Document) => `${row.startPage} - ${row.endPage}`
    // },
    {
      key: "isHidden",
      className: "col-span-3",
      label: "Exclude for processing",
      formatter: (value: boolean) => (
        <Tooltip
          position="right"
          text={
            value
              ? "This document won't be used for aggregation"
              : "This document will be used for aggregation"
          }
        >
          <Checkbox
            checked={value}
            onChange={async (e) => {
              document.isHidden = e.target.checked;
              await handleSetDocumentVisibility(e.target.checked);
            }}
            label={value ? "Excluded" : "Not Excluded"}
            disabled={setDocumentVisibility.isPending}
          />
        </Tooltip>
      )
    }
  ];
  return (
    <div className="gap-2 grid grid-cols-3 w-full min-w-0 pb-4">
      {fieldsForDisplay.map((field) => (
        <div key={field.key} className={twMerge("col-span-1", field.className)}>
          <div className="font-medium text-xs">{field.label}</div>
          <div className="break-all text-sm">
            {field.formatter
              ? field.formatter(getValueByPath(document, field.key), document)
              : getValueByPath(document, field.key)}
          </div>
        </div>
      ))}
    </div>
  );
};

export default DocumentMeta;
