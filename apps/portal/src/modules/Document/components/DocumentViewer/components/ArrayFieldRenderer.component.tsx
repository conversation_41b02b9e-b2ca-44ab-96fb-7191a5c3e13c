import { DocumentField } from "nest-modules";

import { PlusI<PERSON>, TrashIcon } from "lucide-react";
import { ChangeE<PERSON>, ChangeEventHandler, useEffect, useMemo, useState } from "react";
import DataGrid, { Column, SelectColumn, textEditor } from "react-data-grid";
import toast from "react-hot-toast";
import { <PERSON><PERSON>, But<PERSON> } from "ui";
import { DocumentFieldSelect } from ".";

const CI_LINE_COLUMN_ORDER = [
  "hsCode",
  "goodsDescription",
  "partNumber",
  "sku",
  "upc",
  "netWeight",
  "grossWeight",
  "weightUOM",
  "volume",
  "volumeUOM",
  "package",
  "packageUOM",
  "currencyCode",
  "quantityUOM",
  "quantity",
  "unitPrice",
  "discount",
  "totalLineValue"
];

const ArrayFieldRenderer = ({
  isEditing,
  field,
  name,
  availableFields,
  onChange
}: {
  isEditing: boolean;
  field: DocumentField;
  name: string;
  availableFields: { label: string; value: string }[];
  onChange: ChangeEventHandler<HTMLInputElement>;
}) => {
  if (field.dataType !== "array") {
    return "Field is not an array";
  }

  useEffect(() => {
    try {
      const parsedValue = JSON.parse(field.value);
      setParsedValue(parsedValue);

      // FIXME: this is a workaround to set the row id
      for (const [index, row] of parsedValue.entries()) {
        row.id = index + 1;
      }

      setColumnsOrdered(Object.keys(parsedValue[0]));
    } catch (error) {
      console.error("parseError", error);
      setParsedValue([]);
      setColumns([]);
    }
  }, [field.value]);

  const setColumnsOrdered = (columns: string[]) => {
    const ordered = columns.sort((a, b) => CI_LINE_COLUMN_ORDER.indexOf(a) - CI_LINE_COLUMN_ORDER.indexOf(b));
    setColumns(ordered);
  };

  const [parsedValue, setParsedValue] = useState<any[]>([]);
  const [columns, setColumns] = useState<string[]>([]);

  const filteredAvailableFields = useMemo(() => {
    return availableFields
      .filter((af) => {
        return af.value.startsWith(field.name + ".");
      })
      .map((af) => ({
        label: af.value.substring(field.name.length + 1),
        value: af.value.substring(field.name.length + 1)
      }));
  }, [availableFields, field.name]);

  const emitOnChange = (value: any[]) => {
    console.debug("emitOnChange", value);
    onChange({
      target: { name: name, value: JSON.stringify(value) }
    } as ChangeEvent<HTMLInputElement>);
  };

  const handleColumnChange = (prevColumn: string, newColumn: string) => {
    console.debug("handleColumnChange", prevColumn, newColumn);
    setColumns((columns) => columns.map((c) => (c === prevColumn ? newColumn : c)));
    setParsedValue((parsedValue) => {
      const value = parsedValue.map((v) => {
        return { [newColumn]: v[prevColumn], [prevColumn]: undefined, ...v };
      });

      emitOnChange(value);
      return value;
    });
  };

  const handleColumnRemove = (column: string) => {
    console.debug("handleColumnRemove", column);
    setColumnsOrdered(columns.filter((c) => c !== column));
    setParsedValue((parsedValue) => {
      const value = parsedValue.map((v) => {
        const { [column]: _, ...rest } = v;
        return rest;
      });

      emitOnChange(value);
      return value;
    });
  };

  const HeaderCell = ({ column }: { column: string }) => {
    return useMemo(() => {
      if (isEditing) {
        return (
          <div className="flex items-center gap-2">
            <TrashIcon
              className="size-4 cursor-pointer text-danger shrink-0"
              onClick={() => handleColumnRemove(column)}
            />
            <DocumentFieldSelect
              containerStyle="min-w-32 flex-1 -mt-1"
              name={`${field.name}.${column}`}
              fieldName={column}
              onChange={(e) => handleColumnChange(column, e.target.value)}
              availableFields={filteredAvailableFields}
              selectedFields={columns}
            />
          </div>
        );
      }

      return <div>{column}</div>;
    }, [isEditing, column]);
  };

  const handleAddColumn = () => {
    setColumns([...columns, ""]);
  };

  const gridColumns: Column<any>[] = useMemo(
    () => [
      ...(isEditing ? [SelectColumn] : []),
      {
        key: "row",
        name: "#",
        frozen: true,
        width: 32,
        renderCell: ({ rowIdx }) => rowIdx + 1,
        cellClass: "text-right",
        headerCellClass: "text-right"
      },
      ...columns.map((c) => ({
        key: c,
        name: c,
        resizable: true,
        editable: c !== "",
        renderHeaderCell: () => <HeaderCell column={c} />,
        renderSummaryCell: ({ row }: { row: any }) => <div>{row[c]}</div>,
        renderEditCell: textEditor
      })),
      ...(isEditing
        ? [
            {
              key: "add_column",
              name: "Add Column",
              width: 128,
              renderHeaderCell: () => (
                <button
                  className="flex items-center justify-center cursor-pointer gap-2 hover:bg-gray-200 text-xs rounded-sm px-2 py-1"
                  onClick={() => handleAddColumn()}
                >
                  <PlusIcon className="size-4" />
                  Add Column
                </button>
              )
            }
          ]
        : [])
    ],
    [columns, isEditing]
  );

  const summaryRow = useMemo(() => {
    const row: any = {};

    columns.forEach((c) => {
      if (["grossWeight", "netWeight", "quantity", "package", "totalLineValue", "discount"].includes(c)) {
        row[c] = parsedValue.reduce((acc: number, v: any) => acc + v[c], 0);
      }
    });

    return [row];
  }, [parsedValue, isEditing]);

  const handleRowsChange = (rows: any[]) => {
    console.debug("handleRowsChange", rows);

    // cast to number
    const numberColumns = [
      "quantity",
      "totalLineValue",
      "unitPrice",
      "discount",
      "grossWeight",
      "netWeight",
      "package",
      "volume"
    ];

    const castedRows = rows.map((row) => {
      const castedRow: any = { ...row };
      for (const c of numberColumns) {
        if (c in castedRow) {
          castedRow[c] = Number(castedRow[c]) || 0;
        }
      }
      return castedRow;
    });

    emitOnChange(castedRows);
  };

  const [selectedRows, setSelectedRows] = useState((): ReadonlySet<any> => new Set());

  const hasSelectedRows = useMemo(() => {
    return selectedRows.size > 0;
  }, [selectedRows]);

  const handleDeleteRows = () => {
    setParsedValue((parsedValue) => {
      const value = parsedValue.filter((v) => !selectedRows.has(v.id));
      emitOnChange(value);
      return value;
    });
    setSelectedRows(new Set());
    toast.success(`${selectedRows.size} ${selectedRows.size === 1 ? "row" : "rows"} deleted`);
  };

  const rowKeyGetter = (row: any) => {
    return row.id;
  };

  return (
    <div className="overflow-x-auto col-span-2">
      {hasSelectedRows && (
        <Alert
          className="m-2"
          type="info"
          title={`${selectedRows.size} ${selectedRows.size === 1 ? "row" : "rows"} selected`}
          actionPosition="end"
          compact
          action={
            <Button
              label="Delete"
              kind="delete"
              className="text-xs px-3 py-1.5 gap-1.5"
              onClick={() => handleDeleteRows()}
            />
          }
        />
      )}

      <DataGrid
        columns={gridColumns}
        className="rdg-light text-xs"
        rows={parsedValue}
        rowKeyGetter={rowKeyGetter}
        selectedRows={selectedRows}
        onSelectedRowsChange={setSelectedRows}
        bottomSummaryRows={summaryRow}
        onRowsChange={handleRowsChange}
      />

      <div className="text-xs italic">{parsedValue.length} row(s)</div>
    </div>
  );
};

export default ArrayFieldRenderer;
