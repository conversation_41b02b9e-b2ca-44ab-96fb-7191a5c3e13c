import { ChangeEvent<PERSON><PERSON><PERSON>, useMemo } from "react";
import { Select } from "ui";

const FieldSelect = ({
  onChange,
  fieldName,
  containerStyle = "w-full",
  name,
  availableFields,
  selectedFields
}: {
  onChange: ChangeEventHandler<HTMLSelectElement>;
  fieldName: string;
  containerStyle?: string;
  name: string;
  availableFields: { label: string; value: string }[];
  selectedFields: string[];
}) => {
  const documentFieldSelectOptions = useMemo(() => {
    return availableFields
      .map((df) => ({
        ...df,
        disabled: fieldName !== df.value && selectedFields.includes(df.value)
      }))
      .sort((a, b) => {
        if (a.disabled === b.disabled) {
          return a.label.localeCompare(b.label);
        }
        return a.disabled ? 1 : -1;
      });
  }, [availableFields, fieldName, selectedFields]);

  return (
    <Select
      name={name}
      containerStyle={containerStyle}
      onChange={onChange}
      value={fieldName}
      options={documentFieldSelectOptions}
      optional
    />
  );
};

export default FieldSelect;
