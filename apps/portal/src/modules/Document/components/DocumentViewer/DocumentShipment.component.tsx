import { SelectShipmentModal } from "@/modules/Shipment/components";
import { Shipment } from "@/modules/Shipment/Types.shipment";
import { useReducer } from "react";
import { Link } from "react-router-dom";
import { Alert, Button } from "ui";

const ShipmentCard = ({ shipment }: { shipment: Shipment }) => {
  return (
    <Link to={`/shipment/${shipment.id}`}>
      <div className="border rounded-md p-2 hover:bg-gray-100">
        <div className="font-medium">{shipment.hblNumber}</div>
        <div className="text-sm">{shipment.modeOfTransport}</div>
      </div>
    </Link>
  );
};

export default function DocumentShipment({
  isShipmentMismatch,
  shipment,
  updateShipment,
  dismissShipmentMismatchWarning
}: {
  isShipmentMismatch: boolean;
  shipment?: Shipment | null;
  updateShipment: (shipment: Shipment) => Promise<void>;
  dismissShipmentMismatchWarning: () => Promise<void>;
}) {
  const [showSelectShipmentModal, toggleSelectShipmentModal] = useReducer((state: boolean) => !state, false);

  const handleSelectShipment = (shipment: Shipment) => {
    updateShipment(shipment).finally(() => {
      toggleSelectShipmentModal();
    });
  };

  return (
    <>
      <Alert
        type="warning"
        title="Document may not belong to this shipment"
        message="Please check if this document belongs to this shipment."
        isVisible={isShipmentMismatch}
        action={
          <>
            <Button label="Move to another shipment" onClick={toggleSelectShipmentModal} />
            <Button label="Keep in this shipment" onClick={dismissShipmentMismatchWarning} kind="cancel" />
          </>
        }
      />
      {shipment ? (
        <>
          <p className="mb-2">This document has been assigned to a shipment.</p>
          <ShipmentCard shipment={shipment} />
        </>
      ) : (
        <>
          <p className="mb-2">This document has not been assigned to a shipment.</p>
          <Button label="Add to Shipment" onClick={toggleSelectShipmentModal} />
        </>
      )}
      <SelectShipmentModal
        show={showSelectShipmentModal}
        onSelect={handleSelectShipment}
        onClose={() => {
          toggleSelectShipmentModal();
        }}
      />
    </>
  );
}
