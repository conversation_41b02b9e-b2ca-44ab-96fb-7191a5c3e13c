import { useNavigate } from "react-router-dom";
import { FilePages } from "../../Routes.documents";
import { File } from "../../Types.document";
import FileIcon from "../FileIcon";

const FileRenderer = ({ value }: { value?: File }) => {
  const navigate = useNavigate();
  if (!value) return null;
  return (
    <div
      className="text-primary hover:underline hover:cursor-pointer"
      onClick={(e) => {
        e.stopPropagation();
        navigate(FilePages.Detail(value.id?.toString() ?? ""));
      }}
    >
      <div className="flex items-center gap-2">
        <FileIcon mimeType={value.mimeType ?? ""} className="size-4" />
        <span>{value.name ?? ""}</span>
      </div>
    </div>
  );
};

export default FileRenderer;
