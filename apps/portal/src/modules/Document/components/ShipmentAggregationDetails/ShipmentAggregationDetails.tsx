import { ShipmentFormRef } from "@/modules/Shipment/components/ShipmentForm";
import { LucideCheckCircle2 } from "lucide-react";
import { useEffect, useState } from "react";
import { Card, Icons } from "ui";
import { useGetShipmentAggregationDetails } from "../../queries/GetShipmentAggregationDetails.query";
import TradePartnersAggregationDetails from "./TradePartnersAggregationDetails";
type ShipmentAggregationDetailsProps = {
  shipmentId: number;
  shipmentFormRef: React.RefObject<ShipmentFormRef>;
};

const ShipmentAggregationLoading = () => {
  return (
    <div className="flex items-center justify-center mb-2">
      <Icons.Loader className="w-8 h-8 text-primary animate-spin" />
    </div>
  );
};

const ShipmentAggregationFieldDetails = ({ field, fieldName }: { field: any; fieldName: string }) => {
  return (
    <div className="mb-2">
      <div className="py-1 px-2 bg-gray-100 rounded-md mb-2">
        <h6 className="flex items-center gap-3 font-medium">
          {field.isConcordant && <LucideCheckCircle2 className="w-4 h-4 text-green-500" />}
          {fieldName}
          {field.value && (
            <pre className="text-xs text-gray-500 whitespace-pre-wrap">
              {JSON.stringify(field.value, null, 2)}
            </pre>
          )}
        </h6>
      </div>
      <div className="flex flex-col gap-1">
        {field.source?.map((source: any) => (
          <div key={source.documentId}>
            <div className="text-sm">{JSON.stringify(source.value, null, 2)}</div>
            <div className="text-xs text-gray-500">{source.name}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

const ShipmentAggregationDetails = ({ shipmentId, shipmentFormRef }: ShipmentAggregationDetailsProps) => {
  const { data, isLoading } = useGetShipmentAggregationDetails({ id: shipmentId });

  const [_, setCurrentFocusedField] = useState<string | null>(null);

  const handleFocus = (event: FocusEvent) => {
    const fieldName = (event.target as HTMLInputElement).name;
    setCurrentFocusedField(fieldName);
  };

  useEffect(() => {
    if (shipmentFormRef.current?.element) {
      shipmentFormRef.current?.element?.addEventListener("focusin", handleFocus);
      return () => {
        shipmentFormRef.current?.element?.removeEventListener("focusin", handleFocus);
      };
    }
  }, []);

  return (
    <Card containerStyle="m-4 p-4">
      <h5 className="mb-3">Shipment Aggregation Details</h5>
      {isLoading ? (
        <ShipmentAggregationLoading />
      ) : (
        <div>
          <TradePartnersAggregationDetails tradePartnerAggregationDetails={data?.tradePartners} />
          <div className="flex flex-col gap-2">
            {Object.entries(data?.shipment).map(([fieldName, field]) => (
              <ShipmentAggregationFieldDetails key={fieldName} field={field} fieldName={fieldName} />
            ))}
          </div>
        </div>
      )}
    </Card>
  );
};

export default ShipmentAggregationDetails;
