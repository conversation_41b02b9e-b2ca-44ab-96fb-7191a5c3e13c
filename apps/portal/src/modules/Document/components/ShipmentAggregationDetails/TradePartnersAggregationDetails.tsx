import { useState } from "react";
import { StatusLabel } from "ui";
import { Entity, KeyValueField } from "./KeyValueField";

export default function TradePartnersAggregationDetails({
  tradePartnerAggregationDetails
}: {
  tradePartnerAggregationDetails: Entity[];
}) {
  // State to track which fields are expanded
  const [expandedFields, setExpandedFields] = useState<Record<string, boolean>>({});

  // Toggle expanded state for a field
  const toggleField = (entityIndex: number, field: string) => {
    const key = `${entityIndex}-${field}`;
    setExpandedFields((prev) => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Check if a field is expanded
  const isExpanded = (entityIndex: number, field: string) => {
    const key = `${entityIndex}-${field}`;
    return expandedFields[key] || false;
  };

  // Helper function to format field labels
  const formatFieldLabel = (field: string): string => {
    const labelMap: Record<string, string> = {
      name: "Name",
      street: "Street",
      city: "City",
      state: "State",
      postalCode: "Postal",
      country: "Country",
      phoneNumber: "Phone",
      email: "Email"
    };

    return labelMap[field] || field.charAt(0).toUpperCase() + field.slice(1);
  };

  return (
    <div className="space-y-4">
      {tradePartnerAggregationDetails.map((entity, entityIndex) => (
        <div key={entityIndex} className="border rounded-md p-2">
          {/* Type Tags in separate row */}
          <div className="flex flex-wrap gap-1 mb-2">
            {entity.types.map((type, idx) => (
              <StatusLabel key={idx} color="info" className="text-xs capitalize" text={type} />
            ))}
          </div>

          {/* Key-value pairs - one per row */}
          <div className="text-sm">
            {/* Dynamically render fields by looping through entity.data */}
            {Object.entries(entity.data).map(([field, value]) => (
              <KeyValueField
                key={field}
                label={formatFieldLabel(field)}
                value={value}
                sources={entity.source[field as keyof typeof entity.source] || []}
                isExpanded={isExpanded(entityIndex, field)}
                onToggle={() => toggleField(entityIndex, field)}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
