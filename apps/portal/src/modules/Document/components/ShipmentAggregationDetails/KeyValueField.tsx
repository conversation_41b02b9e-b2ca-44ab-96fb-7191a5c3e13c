import { Ch<PERSON><PERSON>Down, <PERSON><PERSON><PERSON>Up, <PERSON>ideExternalLink } from "lucide-react";
import { Link } from "react-router-dom";
import { StatusLabel } from "ui";

// Define the data types
export interface SourceItem {
  value: string;
  documentId: number;
  type: string;
  name: string;
}

export interface Source {
  name: SourceItem[];
  street: SourceItem[];
  city: SourceItem[];
  state: SourceItem[];
  postalCode: SourceItem[];
  country: SourceItem[];
  email: SourceItem[];
  phoneNumber: SourceItem[];
}

export interface EntityData {
  name?: string;
  street?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  phoneNumber?: string;
  email?: string;
}

export interface Entity {
  data: EntityData;
  types: string[];
  source: Source;
}

interface KeyValueFieldProps {
  label: string;
  value: string | undefined;
  sources: SourceItem[];
  isExpanded: boolean;
  onToggle: () => void;
}

export function KeyValueField({ label, value, sources, isExpanded, onToggle }: KeyValueFieldProps) {
  if (!value) return null;

  return (
    <>
      <div
        className="grid grid-cols-[4rem_1fr] items-center cursor-pointer hover:bg-gray-100 rounded-sm -mx-2 py-[4px] px-2"
        onClick={onToggle}
      >
        <span className="text-xs font-medium">{label}</span>
        <div className="flex items-center justify-between">
          <span className="text-xs">{value}</span>
          {sources.length > 0 && (
            <span className="text-slate-400">
              {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </span>
          )}
        </div>
      </div>

      {isExpanded && sources.length > 0 && (
        <div className="text-xs rounded-sm">
          <ul className="-mx-2 divide-y divide-gray-200 border-y border-gray-200 bg-gray-100">
            {sources.map((source, idx) => (
              <li key={idx} className="flex flex-col py-[2px] px-2 hover:bg-gray-200">
                <div>
                  <span className="text-xs">{source.value}</span>
                </div>
                <div className="flex items-center gap-1 mt-[1px] text-gray-500 text-[0.7rem]">
                  <StatusLabel
                    color="bg-gray-200 text-gray-500 italic"
                    className="text-[0.7rem] px-1 py-[2px] leading-none"
                    text={source.type}
                  />
                  <span className="text-[0.7rem]">in</span>
                  <div className="italic">{source.name}</div>
                  <Link to={`documents/${source.documentId}#key=${source.type}`}>
                    <LucideExternalLink className="size-2" />
                  </Link>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </>
  );
}
