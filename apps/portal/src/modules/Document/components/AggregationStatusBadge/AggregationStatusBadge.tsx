import { <PERSON>ide<PERSON>lock3, <PERSON><PERSON><PERSON><PERSON>der2, LucideXCircle } from "lucide-react";

import { LucideCheckCircle2 } from "lucide-react";

import { DocumentAggregationStatus } from "../../Types.document";

const AggregationStatusBadge = ({
  status = DocumentAggregationStatus.PENDING
}: {
  status?: DocumentAggregationStatus;
}) => {
  if (!status) return null;
  const iconMap: Record<DocumentAggregationStatus, React.ReactNode> = {
    [DocumentAggregationStatus.SUCCESS]: (
      <LucideCheckCircle2 className="w-4 h-4 text-green-600" strokeWidth={3} />
    ),
    [DocumentAggregationStatus.FAILED]: <LucideXCircle className="w-4 h-4 text-red-500" strokeWidth={3} />,
    [DocumentAggregationStatus.PROCESSING]: (
      <LucideLoader2 className="w-4 h-4 text-blue-500 animate-spin" strokeWidth={3} />
    ),
    [DocumentAggregationStatus.PENDING]: <LucideClock3 className="w-4 h-4 text-gray-500" strokeWidth={3} />
  };

  const colorMap: Record<DocumentAggregationStatus, string> = {
    [DocumentAggregationStatus.SUCCESS]: "text-green-600",
    [DocumentAggregationStatus.FAILED]: "text-red-500",
    [DocumentAggregationStatus.PROCESSING]: "text-blue-500",
    [DocumentAggregationStatus.PENDING]: "text-gray-500"
  };

  return (
    <span className={`inline-flex items-center ${colorMap[status]}`}>
      <span className="mr-1">{iconMap[status]}</span>
      <span className="text-md capitalize  font-medium">{status}</span>
    </span>
  );
};

export { AggregationStatusBadge };
