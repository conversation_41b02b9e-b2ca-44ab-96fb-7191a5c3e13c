import { File as FileEntity, FileStatus, UploadFilesResponse } from "@/modules/Document/Types.document";
import { useCallback, useEffect, useMemo, useState } from "react";
import toast from "react-hot-toast";
import { Icons } from "ui";
import { snakeCaseToCapitalize } from "utils";
import { FileUpload } from "..";
import { subscribeToStatus } from "../../services/File.service";

enum State {
  UPLOADING,
  PARSING,
  SPLITTING,
  IDLE
}
interface Props {
  shipmentId?: number;
  onFileProcessed(): void;
}
const FileUploadProgress = ({ shipmentId, onFileProcessed }: Props) => {
  const [file, setFile] = useState<FileEntity | null>(null);
  const [state, setState] = useState<State>(State.IDLE);

  const subscribeToFileProcessingStatus = useCallback(
    (fileId: number) => {
      return subscribeToStatus(
        { id: fileId },
        (status) => {
          switch (status) {
            case FileStatus.PARSING:
              setState(State.PARSING);
              break;
            case FileStatus.SPLITTING:
              setState(State.SPLITTING);
              break;
            case FileStatus.PROCESSED:
              setState(State.IDLE);
              onFileProcessed();
              break;
            case FileStatus.NO_DOCUMENT_DETECTED:
            case FileStatus.FILE_CORRUPTED:
            case FileStatus.PARSE_FAILED:
            case FileStatus.SPLIT_FAILED:
            case FileStatus.ERROR:
              toast.error(snakeCaseToCapitalize(status));
              break;
          }
        },
        () => {
          setState(State.IDLE);
        }
      );
    },
    [onFileProcessed]
  );

  useEffect(() => {
    if (file) {
      const sse = subscribeToFileProcessingStatus(file.id);
      return () => {
        sse.close();
      };
    }
  }, [file, subscribeToFileProcessingStatus]);

  const onFileUploaded = (res: UploadFilesResponse) => {
    setFile(res.files[0]);
  };

  const getStateLabel = useMemo(() => {
    switch (state) {
      case State.UPLOADING:
        return "Uploading file...";
      case State.PARSING:
        return "Processing file...";
      case State.SPLITTING:
        return "Identifying documents...";
      default:
        return "";
    }
  }, [state]);

  return (
    <div>
      {state !== State.IDLE ? (
        <div className="flex flex-col items-center justify-center h-full w-full">
          <Icons.Loader className="size-8" />
          <div className="text-sm font-medium text-primary mt-2">{getStateLabel}</div>
        </div>
      ) : (
        <FileUpload
          shipmentId={shipmentId}
          onFileUploaded={onFileUploaded}
          onUploading={(_state) => _state && setState(State.UPLOADING)}
        />
      )}
    </div>
  );
};

export default FileUploadProgress;
