import { BOOLEAN_OPTIONS, ORDER_BY } from "@/common/Constant.common";
import { Organization } from "@/modules/Auth/Types.auth";
import { SelectOrganizationModal } from "@/modules/Auth/components";
import { SelectShipmentModal } from "@/modules/Shipment/components";
import { useDebounce } from "@uidotdev/usehooks";
import { useEffect, useReducer, useState } from "react";
import { twMerge } from "tailwind-merge";
import { Card, Input, Select } from "ui";
import { OrderBy } from "utils";
import { FILE_SORT_BY, FILE_STATUS } from "../../Constant.document";
import { fileTableSchema } from "../../Schema.documents";
import { FileStatus as FileStatusType, GetFileList, Shipment } from "../../Types.document";

type Props = {
  isOpen: boolean;
  isSuperAdmin?: boolean;
  filterValues(values?: GetFileList): void;
};
const FileFilter = ({ isOpen, isSuperAdmin, filterValues }: Props) => {
  const [values, setValues] = useState<GetFileList>();
  const [formValues, setFormValues] = useState<Partial<GetFileList>>();
  const debouncedValues = useDebounce(formValues, 500);

  const [organizationModal, toggleOrganizationModal] = useReducer((r) => !r, false);
  const [organization, setOrganization] = useState<Organization>();
  const [shipmentModal, toggleShipmentModal] = useReducer((r) => !r, false);
  const [shipment, setShipment] = useState<Shipment>();

  useEffect(() => {
    setValues((prev) => ({
      ...prev,
      ...debouncedValues
    }));
  }, [debouncedValues]);

  useEffect(() => {
    filterValues(values);
  }, [filterValues, values]);

  useEffect(() => () => setValues(undefined), []);

  return (
    <div
      className={twMerge(
        "overflow-hidden transition-max-height duration-300 ease-in-out",
        isOpen ? "max-h-96" : "max-h-0"
      )}
    >
      {organizationModal && (
        <SelectOrganizationModal
          show={organizationModal}
          onClose={toggleOrganizationModal}
          selected={!!organization}
          onSelect={(organization) => {
            setValues((prev) => ({
              ...prev,
              organizationId: organization?.id
            }));
            setOrganization(organization);
            toggleOrganizationModal();
          }}
        />
      )}
      {shipmentModal && (
        <SelectShipmentModal
          show={shipmentModal}
          onClose={toggleShipmentModal}
          onSelect={(shipment) => {
            setValues((prev) => ({
              ...prev,
              shipmentId: shipment?.id
            }));
            setShipment(shipment);
            toggleShipmentModal();
          }}
        />
      )}
      {isOpen && (
        <Card containerStyle="flex flex-wrap items-center gap-x-3 gap-y-2 px-6 py-4">
          <div className="flex gap-3">
            <Input
              label="File Name"
              placeholder="Search"
              onTextChange={(name: string) => setFormValues((prev) => ({ ...prev, name }))}
              kind="search"
            />
            <Select
              label="Status"
              onSelected={(status: FileStatusType) => setValues((prev) => ({ ...prev, status }))}
              options={FILE_STATUS}
              optional
            />
            <Input
              label="Shipment"
              placeholder="Select shipment"
              value={shipment?.hblNumber ?? ""}
              onClick={toggleShipmentModal}
              readOnly
            />
            <Select
              label="Has Shipment"
              onSelected={(hasOrphanDocuments?: string) =>
                setValues((prev) => ({
                  ...prev,
                  hasOrphanDocuments: hasOrphanDocuments ? hasOrphanDocuments !== "true" : undefined
                }))
              }
              options={BOOLEAN_OPTIONS}
              optional
            />
            {isSuperAdmin && (
              <Input
                label="Organization"
                placeholder="Select organization"
                value={organization?.name ?? ""}
                onClick={toggleOrganizationModal}
                readOnly
              />
            )}
          </div>

          <div className="flex gap-3">
            <Select
              label="Sort By"
              onSelected={(sortBy: keyof typeof fileTableSchema) =>
                setValues((prev) => ({ ...prev, sortBy }) as GetFileList)
              }
              options={FILE_SORT_BY}
              defaultValue={"createDate"}
              optional
            />

            <Select
              label="Sort Direction"
              onSelected={(sortOrder: OrderBy) =>
                setValues((prev) => ({ ...prev, sortOrder }) as GetFileList)
              }
              options={ORDER_BY}
              defaultValue={"desc"}
              optional
            />
          </div>
        </Card>
      )}
    </div>
  );
};
export default FileFilter;
