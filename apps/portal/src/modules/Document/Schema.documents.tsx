import { LucideMail } from "lucide-react";
import { auditTableSchema, TableSchema } from "utils";
import { array, mixed, number, object, ObjectSchema, string } from "yup";
import { DocumentStatusBadge, FileRenderer } from "./components";
import DocumentAggregationRenderer from "./components/TableRenderer/DocumentAggregationRenderer.component";
import DocumentRenderer from "./components/TableRenderer/DocumentRenderer.component";
import FileStatusRenderer from "./components/TableRenderer/FileStatusRenderer.component";
import {
  Document,
  DocumentAggregation,
  DocumentFieldDataType,
  DocumentFieldFormParams,
  DocumentStatus,
  File,
  FileStatus,
  Shipment
} from "./Types.document";

export const fileTableSchema: TableSchema<File> = {
  status: {
    header: "Status",
    renderer: ({ value }: { value: FileStatus }) => <FileStatusRenderer value={value} />,
    visible: true,
    width: 156
  },
  name: {
    header: "File Name",
    renderer: ({ row: value }: { value: string; row?: File }) => <FileRenderer value={value} />,
    visible: true,
    width: 300
  },
  documents: {
    header: "Documents",
    renderer: ({ value, row }: { value: Document[]; row?: File }) => (
      <DocumentRenderer value={value} row={row} />
    ),
    style: "max-w-80 whitespace-normal break-words",
    visible: true,
    disableSort: true
  },
  ...auditTableSchema
};

export const fileDocumentTableSchema: TableSchema<Document> = {
  status: {
    header: "Status",
    renderer: ({ value }: { value: DocumentStatus }) => <DocumentStatusBadge status={value} />,
    visible: true,
    width: 156
  },
  name: {
    header: "Document Name",
    visible: true,
    width: 300
  },
  shipment: {
    header: "Shipment HBL",
    renderer: ({ value }: { value: Shipment }) => (value ? value.hblNumber : "No shipment"),
    style: "max-w-80 whitespace-normal break-words",
    visible: true
  },
  createDate: {
    header: "Created Date",
    renderer: "date",
    visible: true
  }
};

export const documentTableSchema: TableSchema<Document> = {
  name: {
    header: "Name",
    visible: true,
    renderer: ({ value, row }: { value: string; row: Document }) => {
      return (
        <>
          {value}
          {row.file?.batch?.creator === "email" && (
            <LucideMail className="w-3 h-3 inline-block ml-1 text-gray-500" />
          )}
        </>
      );
    }
  },
  status: {
    header: "Status",
    renderer: ({ value }: { value: DocumentStatus }) => <DocumentStatusBadge status={value} />,
    visible: true,
    width: 156
  },
  aggregation: {
    header: "Aggregration",
    renderer: ({ value }: { value: DocumentAggregation }) => <DocumentAggregationRenderer value={value} />,
    visible: true,
    width: 156
  },
  documentType: {
    header: "Type",
    renderer: ({ value }: { value: DocumentType }) => value?.name,
    visible: true,
    sortKey: "documentTypeId"
  },
  reference: {
    header: "Reference",
    renderer: ({ row }: { value?: Document; row?: Document }) =>
      row?.reference?.name ?? row?.referencedBy?.name,
    visible: true
  },
  file: {
    header: "File",
    renderer: ({ value }: { value: File }) => <FileRenderer value={value} />,
    visible: true,
    sortKey: "fileId"
  },
  ...auditTableSchema
};

export const saveDocumentFieldSchema: ObjectSchema<DocumentFieldFormParams> = object({
  fields: array(
    object({
      id: number().optional(),
      name: string().required("Name is required").min(1),
      value: string().required("Value is required").min(1),
      dataType: mixed<DocumentFieldDataType>().required()
    })
  ).defined(),
  deletedIds: array(number().required("Deleted id is required")).optional()
});
