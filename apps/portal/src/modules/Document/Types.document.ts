import {
  // Aggregation
  AggregateDocumentDto,
  AggregateDocumentValidationErrorResponseDto,
  // target type
  BatchSaveFileDocumentItemDto,
  // file documents
  BatchSaveFileDocumentsDto,
  // Document Fields
  BatchUpdateDocumentFieldsDto,
  CreateDocumentFieldDto,
  DocumentValidationError,
  // documents
  GetDocumentsDto,
  GetDocumentsResponseDto,
  GetDocumentTypesDto,
  GetDocumentTypesResponseDto,
  GetFilesDto,
  GetFilesResponseDto,
  ProcessDocumentDto,
  // document
  SetDocumentVisibilityDto,
  DocumentFieldDataType as TDocFieldDataType,
  Document as TDocument,
  DocumentAggregation as TDocumentAggregation,
  DocumentAggregationStep as TDocumentAggregationStep,
  DocumentAggregationStepLog as TDocumentAggregationStepLog,
  // document fields
  DocumentField as TDocumentField,
  DocumentType as TDocumentType,
  // Document Types
  File as TFile,
  FileBatch as TFileBatch,
  // location
  Location as TLocation,
  Shipment as TShipment,
  // trade partner
  TradePartner as TTradePartner,
  UpdateDocumentShipmentDto,
  // files
  UploadFilesDto,
  UploadFilesResponseDto
} from "nest-modules";

export type { BatchSaveFileDocumentItemDto, BatchSaveFileDocumentsDto, DocumentValidationError };

import { DetailParams, ListParams } from "utils";

// Files
export type File = TFile;
export type FileBatch = TFileBatch;

export type UploadFilesParams = UploadFilesDto & {
  shipmentId?: number;
};
export type UploadFilesResponse = UploadFilesResponseDto;
export type GetFileList = ListParams<GetFilesDto>;
export type GetFileListResponse = GetFilesResponseDto;
export type BatchSaveFileDocumentsParams = BatchSaveFileDocumentsDto & DetailParams;

// Documents
export type Document = TDocument;
export type GetDocumentList = ListParams<GetDocumentsDto>;
export type GetDocumentListResponse = GetDocumentsResponseDto;
export type DocumentField = TDocumentField;
export type UpdateDocumentShipmentParams = UpdateDocumentShipmentDto & DetailParams;
export type ProcessDocumentParams = ProcessDocumentDto & DetailParams;
export type SetDocumentVisibilityParams = SetDocumentVisibilityDto & DetailParams;

// Document Aggregation
export type AggregateDocumentParams = AggregateDocumentDto;
export type Shipment = TShipment;

export type AggregateDocumentValidationErrorResponse = AggregateDocumentValidationErrorResponseDto;

// Document Types
export type DocumentType = TDocumentType;
export type GetDocumentTypeList = ListParams<GetDocumentTypesDto>;
export type GetDocumentTypeListResponse = GetDocumentTypesResponseDto;

// Aggregation
export type DocumentAggregation = TDocumentAggregation;
export type DocumentAggregationStep = TDocumentAggregationStep;
export type DocumentAggregationStepLog = TDocumentAggregationStepLog;

// location
export type Location = TLocation;

// trade partner
export type TradePartner = TTradePartner;

export enum FileStatus {
  // default
  PENDING = "pending", // The file is uploaded, and is pending to be processed

  // normal
  PARSING = "parsing", // The file is being parsed
  SPLITTING = "splitting", // Splitting the documents in the file
  PROCESSED = "processed", // The file is processed

  NO_DOCUMENT_DETECTED = "no_document_detected", // No document detected in the file

  // error
  FILE_CORRUPTED = "file_corrupted", // specific error for file corrupted
  PARSE_FAILED = "parse_failed", // specific error for parsing failed
  SPLIT_FAILED = "split_failed", // specific error for split failed

  ERROR = "error" // general error
}

export enum DocumentStatus {
  // default
  PENDING = "pending",
  EXTRACTING = "extracting",
  EXTRACTED = "extracted",

  // validating
  VALIDATING = "validating",

  // aggregation
  AGGREGATED = "aggregated",

  // error
  EXTRACTION_FAILED = "extraction_failed",
  AGGREGATION_FAILED = "aggregation_failed",

  // shipment mismatch
  SHIPMENT_MISMATCH = "shipment_mismatch"
}

export enum DocumentAggregationStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  SUCCESS = "success",
  FAILED = "failed"
}

export enum ProcessDocumentModel {
  GPT_4O = "gpt-4o",
  O3_MINI = "o3-mini"
}

export enum FileColumn {
  id = "id",
  path = "path",
  name = "name",
  parseResult = "parseResult",
  parser = "parser",
  numPages = "numPages",
  status = "status",
  mimeType = "mimeType",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById",
  batchId = "batchId",
  hash = "hash"
}
export enum DocumentColumn {
  id = "id",
  name = "name",
  startPage = "startPage",
  endPage = "endPage",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  fileId = "fileId",
  documentTypeId = "documentTypeId",
  createdById = "createdById",
  lastEditedById = "lastEditedById",
  status = "status",
  shipmentId = "shipmentId",
  organizationId = "organizationId",
  aggregationId = "aggregationId",
  referenceId = "referenceId"
}

export enum AggregationTargetType {
  SHIPMENT = "shipment",
  COMMERCIAL_INVOICE = "commercial-invoice",
  CERTIFICATE_OF_ORIGIN = "certificate-of-origin"
}

export type SaveDocumentFieldParams = CreateDocumentFieldDto & DetailParams;
export type DocumentFieldFormParams = {
  fields: SaveDocumentFieldParams[];
  deletedIds?: number[];
};
export type DocumentFieldDataType = TDocFieldDataType;

export type BatchSaveDocumentFieldParams = BatchUpdateDocumentFieldsDto & DetailParams;

//#region Pdf
export type PdfParams<T> = {
  templateId: string;
  fileName?: string;
  body: T;
};
export type PdfResponse = {
  download_url: string;
  status: string;
  template_id: string;
  total_pages: number;
  transaction_ref: string;
};
//#endregion

export type GetAggregationByTargetParams = DetailParams & { targetType: AggregationTargetType };
