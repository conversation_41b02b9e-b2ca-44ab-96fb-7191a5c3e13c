import { LucideExternalLink } from "lucide-react";
import { <PERSON>, Outlet, useParams } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "ui";
import { formatDateTime } from "utils";
import { AggregationStatusBadge } from "../../components/AggregationStatusBadge";
import FileIcon from "../../components/FileIcon";
import { useAggregateFileBatch } from "../../mutations";
import { useGetFileBatchDetail } from "../../queries";
import { DocumentAggregation } from "../../Types.document";

const FileBatchAggregationList = ({ aggregations }: { aggregations: DocumentAggregation[] }) => {
  return aggregations.map((aggregation) => (
    <Link to={`/document-aggregation/${aggregation.id}`} key={aggregation.id}>
      <div className="flex justify-between px-4 py-1 hover:bg-gray-100 transition-colors">
        <div className="flex-1">
          <h6 className="text-sm font-semibold">{aggregation.action}</h6>
          <AggregationStatusBadge status={aggregation.status} />
        </div>

        <div>
          <span className="text-xs text-gray-500">{formatDateTime(aggregation.createDate)}</span>
        </div>
      </div>
    </Link>
  ));
};

export default function FileBatchDetailPage() {
  const { fileBatchId } = useParams();

  const { data } = useGetFileBatchDetail({ id: fileBatchId });

  const { mutate: aggregate, isPending: isAggregating } = useAggregateFileBatch();

  return (
    <main>
      <Header
        title={
          <div>
            <h4>File Batch</h4>
            {data && (
              <div className="flex items-center gap-2 mt-1">
                <StatusLabel text={data.id} />
                <StatusLabel text={data.status.toUpperCase()} />
              </div>
            )}
          </div>
        }
        children={
          <div className="flex items-center gap-2">
            <Button
              label="Aggregate"
              loading={isAggregating}
              onClick={() => aggregate({ id: fileBatchId })}
            />
          </div>
        }
      ></Header>

      <section className="rounded-md shadow bg-white dark:bg-gray-900 m-4">
        <h5 className="p-4">Files</h5>
        <Collapse
          items={
            data?.files.map((file) => ({
              key: file.id,
              title: (
                <div className="flex items-center gap-2 font-medium text-sm">
                  <FileIcon mimeType={file.mimeType ?? ""} className="size-4" />
                  {file.name}
                </div>
              ),
              children: (
                <div className="ml-3">
                  {file.documents.length === 0 && (
                    <div className="text-xs text-gray-500">No documents found</div>
                  )}
                  {file.documents.map((document) => (
                    <Link to={`/document/file-batch/${fileBatchId}/document/${document.id}`}>
                      <div
                        key={document.id}
                        className="mb-px flex gap-3 items-center border-l-2 pl-2 border-gray-400 border-dotted"
                      >
                        <div className="text-xs text-gray-500">
                          {document.startPage} - {document.endPage}
                        </div>
                        <h6 className="mb-px text-black hover:underline font-normal text-xs">
                          {document.name}
                        </h6>
                      </div>
                    </Link>
                  ))}
                </div>
              )
            })) ?? []
          }
        />
      </section>

      <section className="rounded-md shadow bg-white dark:bg-gray-900 m-4">
        <h5 className="p-4">Aggregations</h5>
        <FileBatchAggregationList aggregations={data?.documentAggregations ?? []} />
      </section>

      {data?.shipment && (
        <Link to={`/shipment/${data?.shipment.id}`}>
          <section className="rounded-md shadow bg-white dark:bg-gray-900 m-4 p-4">
            <h5 className="flex items-center gap-2">
              Shipment
              <LucideExternalLink className="size-4" />
            </h5>
          </section>
        </Link>
      )}

      <Outlet />
    </main>
  );
}
