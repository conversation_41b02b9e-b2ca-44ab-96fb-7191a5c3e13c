import { BaseError } from "@/common/Types.common";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";
import { Download, LucideExternalLink, RefreshCcw } from "lucide-react";
import { useEffect, useMemo, useReducer } from "react";
import toast from "react-hot-toast";
import { Link, useNavigate, useParams, useSearchParams } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import { Button, Icons, Modal } from "ui";
import { FileDocumentList, FilePreviewer, FileStatus } from "../../components";
import { useReplaceDocuments } from "../../mutations/File.mutation";
import { useDownloadFile } from "../../queries/DownloadFile.query";
import { useGetFileDetail } from "../../queries/GetFileDetails.query";
import { FileBatchPages } from "../../Routes.documents";
import { FileService } from "../../services";
import { subscribeToStatus } from "../../services/File.service";
import { BatchSaveFileDocumentItemDto, FileStatus as FileStatusType } from "../../Types.document";

const FileDetail = () => {
  const navigate = useNavigate();
  const { fileId } = useParams();
  const [searchParams] = useSearchParams();
  const { canCrudBackoffice } = useBackofficePermission();

  const { data: file, isFetching, refetch, error } = useGetFileDetail({ id: fileId });

  useEffect(() => {
    let mounted = true;
    if (error) {
      if (mounted) {
        toast.error(error.message);
        navigate(-1);
      }
    }
    return () => {
      mounted = false;
    };
  }, [error, navigate]);

  useEffect(() => {
    if (file?.id) {
      const sse = subscribeToStatus({ id: file.id }, (status) => {
        if (file?.status != status) {
          refetch();
        }
      });

      return () => {
        sse.close();
      };
    }
  }, [file]);

  // TODO: temporary type, api needs to be improved first
  const { data, refetch: refetchFile } = useDownloadFile({ id: file?.id });

  const url = useMemo(() => data?.url ?? "", [data]);

  const handleDownload = async () => {
    const { data } = await refetchFile();
    window.open(data?.url, "_blank");
  };

  const replaceDocuments = useReplaceDocuments();

  // TODO: define type
  const handleSubmit = async (changeSet: {
    updated: BatchSaveFileDocumentItemDto[];
    created: BatchSaveFileDocumentItemDto[];
    deleted: number[];
  }) => {
    if (changeSet.created.length === 0 && changeSet.updated.length === 0 && changeSet.deleted.length === 0) {
      toggleEditing();
      return;
    }

    if (file?.id) {
      try {
        await replaceDocuments.mutateAsync({
          id: file.id,
          updated: changeSet.updated,
          created: changeSet.created,
          deleted: changeSet.deleted
        });
        toggleEditing();
        refetch();
        toast.success("Documents updated successfully");
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    }
  };

  const [editing, toggleEditing] = useReducer((prev) => !prev, searchParams.get("edit") === "true");

  const canRetryOCR = useMemo(() => {
    const statues = new Set([
      FileStatusType.FILE_CORRUPTED,
      FileStatusType.PARSE_FAILED,
      FileStatusType.PROCESSED
    ]);

    return statues.has(file?.status as FileStatusType);
  }, [file]);

  const canEdit = useMemo(() => {
    const statues = new Set([
      FileStatusType.PROCESSED,
      FileStatusType.SPLITTING,
      FileStatusType.NO_DOCUMENT_DETECTED
    ]);

    return statues.has(file?.status as FileStatusType);
  }, [file]);

  const handleProcess = async () => {
    if (file?.id) {
      try {
        await FileService.processFile({ id: file.id });
        refetch();
        toast.success("File is being processed");
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    }
  };

  const memoizedPreview = useMemo(
    () => (
      <>
        {isFetching && <Icons.Loader />}
        {!isFetching && file && url && (
          <FilePreviewer url={url} mimeType={file.mimeType} startPage={1} parseResult={file.parseResult} />
        )}
      </>
    ),
    [url]
  );

  return (
    <Modal
      id="file-detail-modal"
      show={true}
      onClose={() => {
        navigate(-1);
      }}
      size="7xl"
      containerStyle="h-[80vh] [&_main]:h-full [&_main]:p-0"
      title={file?.name}
    >
      <div className="h-full flex flex-col w-full">
        {file && (
          <>
            <div className="px-4 py-3 bg-neutral-10 dark:bg-grey-dark sticky top-0">
              <div className="flex justify-between items-center">
                <div>{file && <FileStatus value={file.status} />}</div>

                <div className="flex gap-2">
                  {canEdit && canCrudBackoffice && (
                    <Button label={editing ? "Cancel" : "Edit Document"} onClick={toggleEditing} />
                  )}
                  {canRetryOCR && <Button label="Process" onClick={handleProcess} />}
                  <Button label="Download" icon={<Download className="size-4" />} onClick={handleDownload} />
                  <Button label="Refresh" icon={<RefreshCcw className="size-4" />} onClick={refetch} />
                </div>
              </div>
            </div>

            <div className="flex flex-1 w-full h-full">
              <div className={twMerge("overflow-y-auto h-full w-80")}>
                <div className="p-4 pt-2">
                  <FileDocumentList
                    documents={file.documents}
                    pages={file.pages}
                    onSubmit={handleSubmit}
                    isEditing={editing}
                  />
                </div>

                <div className="p-4">
                  <h5 className="text-sm font-semibold">Batch</h5>
                  <p className="text-xs text-gray-500 mb-1">
                    This file is part of a batch, view all files and check the status of the batch
                  </p>
                  <Link
                    to={FileBatchPages.Detail(file.batchId ?? "")}
                    className="text-primary hover:underline text-sm"
                  >
                    {file.batchId} <LucideExternalLink className="size-3 inline-block" />
                  </Link>
                </div>
              </div>

              <div className="flex-1 h-full w-full">{memoizedPreview}</div>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};

export default FileDetail;
