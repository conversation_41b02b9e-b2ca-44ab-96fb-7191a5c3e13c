import { LucidePanelBottom, LucidePanelRight } from "lucide-react";
import { useEffect, useReducer } from "react";
import toast from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";
import { Overlay } from "ui";
import { DocumentViewer } from "../../components";
import { useGetDocumentDetail } from "../../queries/GetDocumentDetails.query";
import { subscribeToStatus } from "../../services/Document.service";

const DocumentDetail = () => {
  const navigate = useNavigate();
  const { documentId } = useParams();
  const [verticalSplit, toggleVerticalSplit] = useReducer((state: boolean) => !state, false);

  const { data: document, isFetching, refetch, error } = useGetDocumentDetail({ id: documentId });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
      navigate(-1);
    }
  }, [error, navigate]);

  useEffect(() => {
    if (document) {
      const sse = subscribeToStatus({ id: document.id }, (status) => {
        if (status !== document.status) {
          refetch();
        }
      });

      return () => {
        sse.close();
      };
    }
  }, [document]);

  const Icon = verticalSplit ? LucidePanelRight : LucidePanelBottom;

  return (
    <Overlay
      id="document-detail-overlay"
      show={true}
      onClose={() => {
        navigate(-1);
      }}
      containerStyle="max-w-6xl"
      closeButton
      title="Document Detail"
      header={
        <div className="flex gap-2 flex-1 justify-end">
          <button
            type="button"
            className="flex justify-center items-center size-7 ml-auto text-sm font-semibold rounded-full text-neutral-800 hover:bg-neutral-100 dark:text-white dark:hover:bg-neutral-700 dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-neutral-600"
            onClick={toggleVerticalSplit}
          >
            <span className="sr-only">Toggle Pane</span>
            <Icon className="size-4" />
          </button>
        </div>
      }
    >
      <DocumentViewer
        document={document}
        refetch={refetch}
        loading={isFetching}
        verticalSplit={verticalSplit}
      />
    </Overlay>
  );
};

export default DocumentDetail;
