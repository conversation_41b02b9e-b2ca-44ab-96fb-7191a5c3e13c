import { CircleXIcon, InfoIcon, TriangleAlertIcon } from "lucide-react";
import { DocumentAggregationStepLog } from "../../Types.document";

const AggregationOperationLogViewer = ({ logs }: { logs: DocumentAggregationStepLog[] }) => {
  const getStyleClasses = (level: DocumentAggregationStepLog["level"]) => {
    switch (level) {
      case "info":
        return "bg-blue-100 border-blue-200 text-blue-700";
      case "warning":
        return "bg-yellow-100 border-yellow-200 text-yellow-700";
      case "error":
        return "bg-red-100 border-red-200 text-red-700";
      default:
        return "bg-gray-100 border-gray-200 text-gray-700";
    }
  };

  const getIcon = (level: DocumentAggregationStepLog["level"]) => {
    switch (level) {
      case "info":
        return <InfoIcon className="size-4 text-blue-500" />;
      case "warning":
        return <TriangleAlertIcon className="size-4 text-yellow-500" />;
      case "error":
        return <CircleXIcon className="size-4 text-red-500" />;
      default:
        return <InfoIcon className="size-4 text-gray-500" />;
    }
  };

  return (
    <div className="h-full bg-gray-100 flex flex-col flex-1">
      <div className="flex items-center justify-between py-4 px-6">
        <div className="flex items-center gap-2">
          <InfoIcon className="text-gray-600 size-4" />
          <span className="text-gray-500 font-medium">{logs.length}</span>
        </div>
        <div className="flex items-center gap-2">
          <TriangleAlertIcon className="text-yellow-500 size-4" />
          <span className="text-yellow-600 font-medium">
            {logs.filter((log) => log.level === "warning").length}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <CircleXIcon className="text-red-600 size-4" />
          <span className="text-red-500 font-medium">
            {logs.filter((log) => log.level === "error").length}
          </span>
        </div>
      </div>

      <ul className="space-y-2 w-full p-4 pt-0 overflow-y-auto">
        {logs.map((log, index) => (
          <li key={index} id={`log-${log.field}`}>
            <div className={`flex items-start p-2 border rounded ${getStyleClasses(log.level)}`}>
              <div className="shrink-0 mr-2">{getIcon(log.level)}</div>
              <div className="flex flex-col w-full">
                <span className="font-semibold">{log.field}</span>
                <span>{log.message}</span>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default AggregationOperationLogViewer;
