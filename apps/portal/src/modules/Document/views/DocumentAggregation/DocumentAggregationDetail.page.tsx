import { CheckCircle2, CircleXIcon, FileSymlinkIcon, Loader2Icon, LucideScrollText } from "lucide-react";
import { lazy, Suspense, useEffect } from "react";
import DataGrid from "react-data-grid";
import "react-data-grid/lib/styles.css";
import { ErrorBoundary } from "react-error-boundary";
import { toast } from "react-hot-toast";
import { Link, useParams } from "react-router-dom";
import { Al<PERSON>, Button, Collapse, <PERSON><PERSON>, I<PERSON>s, StatusLabel } from "ui";
import { flattenObject } from "utils";
import { useAggregateDocument } from "../../mutations";
import { useAbortAggregateDocument } from "../../mutations/DocumentAggregation.mutation";
import { useGetDocumentAggregation } from "../../queries";
import { DocumentAggregation, DocumentAggregationStatus } from "../../Types.document";
import AggregationOperationLogViewer from "./AggregationOperationLogViewer.component";
import TwoPaneLayout from "./TwoPaneLayout.component";

export function DocumentAggregationTargetAlert({
  documentAggregation
}: {
  documentAggregation: DocumentAggregation;
}) {
  const message = (() => {
    switch (documentAggregation.targetType) {
      case "shipment":
        return {
          message: "A shipment has been created",
          link: `/shipment/${documentAggregation.targetId}`
        };
      case "commercial-invoice":
        return {
          message: "A commercial invoice has been created",
          link: `/shipment/${documentAggregation.batch?.shipmentId}/commercial-invoice`
        };
      case "certificate-of-origin":
        return {
          message: "A certificate of origin has been created",
          link: `/certificates/${documentAggregation.targetId}`
        };
      default:
        return null;
    }
  })();

  if (!message) {
    return null;
  }

  return (
    <Alert
      title="Document Aggregated"
      className="rounded-none border-x-0 px-6"
      icon={null}
      compact
      actionPosition="end"
      type="success"
      message={message.message}
      action={
        <Link to={message.link}>
          <Button label="View" kind="outline" />
        </Link>
      }
    />
  );
}

export function DefaultAggregationOperationOutput({ operation }: { operation: any }) {
  if (operation.errorMessage) {
    return <pre>{operation.errorMessage}</pre>;
  }

  if (typeof operation.outputData === "string") {
    return <pre>{operation.outputData}</pre>;
  }

  if (Array.isArray(operation.outputData)) {
    if (operation.outputData.length === 0) {
      return <div>No data</div>;
    }
    const flattenedData = operation.outputData.map(flattenObject);
    const columns = Object.keys(flattenedData[0]).map((key) => ({
      key,
      name: key,
      resizable: true
    }));
    return <DataGrid columns={columns} rows={flattenedData} className="rdg-light text-xs" />;
  }

  if (typeof operation.outputData === "object") {
    return <pre>{JSON.stringify(operation.outputData, null, 2)}</pre>;
  }

  return null;
}

const renderers = [
  "FindOrCreateTradePartners",
  "FindOrCreateLocations",
  "FindMatchingPackingList",
  "MergeCIPL",
  "CreateCommercialInvoice",
  "CreateCommercialInvoiceLine"
];

function AggregationOperationOutput({ operation }: { operation: any }) {
  const Renderer = renderers.includes(operation.name)
    ? lazy(() => import(`./renderers/${operation.name}.tsx`))
    : DefaultAggregationOperationOutput;
  return (
    <TwoPaneLayout
      containerClassName="p-0"
      leftPane={
        <ErrorBoundary fallback={<div>An error occurred when displaying the output</div>}>
          <Suspense fallback={<div>Loading...</div>}>
            <Renderer operation={operation} />
          </Suspense>
        </ErrorBoundary>
      }
      rightPane={<AggregationOperationLogViewer logs={operation.logs} />}
    />
  );
}

export default function DocumentAggregationDetailPage() {
  const { aggregationId } = useParams();
  const {
    data: documentAggregation,
    isFetching,
    refetch
  } = useGetDocumentAggregation({
    id: aggregationId
  });

  const aggregateDocument = useAggregateDocument();
  const abortAggregateDocument = useAbortAggregateDocument();

  const actions =
    documentAggregation?.steps.map((operation) => {
      const hasError = operation.status === "failed";

      const statusIcon = hasError ? (
        <CircleXIcon className="text-red-500 size-4" />
      ) : (
        <CheckCircle2 className="text-green-500 size-4" />
      );
      return {
        key: operation.name,
        title: (
          <div className="flex items-center gap-2 justify-center">
            <span>{statusIcon}</span>
            {operation.name}
          </div>
        ),
        children: <AggregationOperationOutput operation={operation} />
      };
    }) ?? [];

  const isAggregated = documentAggregation?.status === DocumentAggregationStatus.SUCCESS;
  const isAggregating =
    documentAggregation?.status === DocumentAggregationStatus.PROCESSING ||
    documentAggregation?.status === DocumentAggregationStatus.PENDING;

  useEffect(() => {
    if (documentAggregation?.status === DocumentAggregationStatus.PROCESSING) {
      const interval = setInterval(() => {
        refetch();
      }, 10000);
      return () => clearInterval(interval);
    }
  }, [documentAggregation]);

  const handleAggregate = () => {
    aggregateDocument
      .mutateAsync({ id: aggregationId, retry: true })
      .then(() => {
        refetch();
      })
      .catch((error) => {
        toast.error("Failed to aggregate document");
        console.error(error);
      });
  };

  const handleAbort = () => {
    abortAggregateDocument
      .mutateAsync({ id: aggregationId })
      .then(() => {
        refetch();
      })
      .catch((error) => {
        toast.error("Failed to abort aggregation");
        console.error(error);
      });
  };

  const STATUS_COLOR_MAP = {
    [DocumentAggregationStatus.SUCCESS]: "success",
    [DocumentAggregationStatus.FAILED]: "danger",
    [DocumentAggregationStatus.PENDING]: "info",
    [DocumentAggregationStatus.PROCESSING]: "info"
  };

  return (
    <main>
      <Header
        title={
          <div>
            <h4>Document Aggregation</h4>
            {documentAggregation && (
              <div className="flex items-center gap-2 mt-1">
                <StatusLabel text={documentAggregation.action.toUpperCase()} />
                <StatusLabel
                  icon={isAggregating ? <Loader2Icon className="animate-spin size-3" /> : undefined}
                  text={documentAggregation.status.toUpperCase()}
                  color={STATUS_COLOR_MAP[documentAggregation.status]}
                />
              </div>
            )}
          </div>
        }
      >
        {isAggregated && <Button label="Retry" onClick={handleAggregate} />}
        {!isAggregated && (
          <Button
            label={isAggregating ? "Cancel" : isAggregated ? "Aggregated" : "Aggregate"}
            onClick={isAggregating ? handleAbort : handleAggregate}
          />
        )}
      </Header>

      {documentAggregation?.targetType && documentAggregation.targetId && (
        <DocumentAggregationTargetAlert documentAggregation={documentAggregation} />
      )}

      <section className="rounded-md shadow bg-white dark:bg-gray-900 m-4">
        <h5 className="p-4">Aggregation Log</h5>
        {isFetching && actions.length === 0 ? <Icons.Loader /> : <Collapse items={actions} />}
        {!isFetching && actions.length === 0 && (
          <div className="p-4 flex flex-col items-center gap-2">
            <LucideScrollText className="size-8 text-gray-500" />
            <div className="text-gray-500 font-medium">No log available</div>
          </div>
        )}
      </section>

      <section className="rounded-md shadow bg-white dark:bg-gray-900 m-4">
        <h5 className="p-4">Documents</h5>
        <ul className="flex flex-col">
          {documentAggregation?.documents.map((document) => (
            <li key={document.id}>
              <Link to={`/document/${document.id}`}>
                <div className="hover:bg-gray-100 dark:hover:bg-gray-800 px-4 py-1">
                  <div className="text-sm">{document.name}</div>
                  <Link to={`/document/file/${document.file.id}`}>
                    <div className="text-xs text-gray-500 flex items-center gap-1 mt-px hover:underline">
                      <FileSymlinkIcon className="size-3" />
                      {document.file.name}
                    </div>
                  </Link>
                </div>
              </Link>
            </li>
          ))}
        </ul>
      </section>
    </main>
  );
}
