import { DocumentAggregationStep } from "@/modules/Document/Types.document";
import { ShipmentDetailPages, ShipmentPages } from "@/modules/Shipment/Routes.shipment";
import { Link } from "react-router-dom";
import { Alert, Button } from "ui";

const CreateCommercialInvoice = ({ operation }: { operation: DocumentAggregationStep }) => {
  return (
    <>
      {operation.status === "failed" ? (
        <div className="text-red-500 font-bold">{operation.errorMessage}</div>
      ) : (
        <div>
          <Alert
            type="success"
            title="Commercial invoice created successfully"
            action={
              <Link
                to={`${ShipmentPages.Detail(operation.outputData?.shipment.id)}/${ShipmentDetailPages.CommercialInvoice}`}
              >
                <Button label="View" />
              </Link>
            }
          />
        </div>
      )}
    </>
  );
};

export default CreateCommercialInvoice;
