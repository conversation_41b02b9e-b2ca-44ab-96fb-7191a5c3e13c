import { DocumentAggregationStep, TradePartner } from "@/modules/Document/Types.document";
import { MailIcon, PhoneIcon } from "lucide-react";
import { twMerge } from "tailwind-merge";

const PartnerInfo = ({ tradePartner, className }: { tradePartner: TradePartner; className?: string }) => (
  <div className={twMerge("p-4 text-sm border rounded-md space-y-1", className)}>
    <div>
      <div className="font-medium">{tradePartner.name}</div>
      <div className="text-xs">{tradePartner.address}</div>
    </div>
    <div className="space-y-0.5 text-gray-700 text-xs">
      {tradePartner.postalCode && <div>{tradePartner.postalCode}</div>}
      <div>
        {tradePartner.city}, {tradePartner.state}
      </div>
      <div>{tradePartner.country?.displayName}</div>
    </div>
    <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-xs">
      {tradePartner.phoneNumber && (
        <div className="flex items-center gap-1">
          <PhoneIcon className="w-2.5 h-2.5 text-gray-600" />
          {tradePartner.phoneNumber}
        </div>
      )}
      {tradePartner.email && (
        <div className="flex items-center gap-1">
          <MailIcon className="w-2.5 h-2.5 text-gray-600" />
          {tradePartner.email}
        </div>
      )}
    </div>
  </div>
);

type FindOrCreateTradePartnerOutput = {
  key: string;
  action: string;
  entity: TradePartner;
  confidence: number;
};

const FindOrCreateTradePartners = ({ operation }: { operation: DocumentAggregationStep }) => {
  const outputData = operation.outputData as FindOrCreateTradePartnerOutput[];
  if (outputData.length === 0) {
    return <div>No trade partners found</div>;
  }
  return (
    <div>
      <ul className="grid grid-cols-2 gap-x-4 gap-y-6 px-1">
        {outputData.map((result: FindOrCreateTradePartnerOutput) => (
          <li key={result.key}>
            <div className="font-medium text-sm mb-2">
              {result.key.charAt(0).toUpperCase() + result.key.slice(1)}
            </div>
            <PartnerInfo
              tradePartner={result.entity}
              className={result.action === "create" ? "ring-2 ring-green-500" : ""}
            />
          </li>
        ))}
      </ul>
    </div>
  );
};

export default FindOrCreateTradePartners;
