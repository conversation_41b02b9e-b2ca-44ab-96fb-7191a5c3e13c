import { DocumentAggregationStep, Location } from "@/modules/Document/Types.document";
import { twMerge } from "tailwind-merge";

const LocationInfo = ({ location, className }: { location: Location; className?: string }) => (
  <div className={twMerge("p-4 text-sm border rounded-md space-y-1", className)}>
    <div>
      <div>
        {location.city}, {location.country?.displayName}
      </div>
    </div>
  </div>
);

type FindOrCreateLocationOutput = {
  key: string;
  action: string;
  entity: Location;
  confidence: number;
};

const FindOrCreateLocations = ({ operation }: { operation: DocumentAggregationStep }) => {
  const outputData = operation.outputData as FindOrCreateLocationOutput[];
  if (outputData.length === 0) {
    return <div>No locations found</div>;
  }
  return (
    <div>
      <ul className="grid grid-cols-3 gap-x-4 gap-y-6">
        {outputData.map((result: FindOrCreateLocationOutput) => (
          <li key={result.key}>
            <div className="font-medium text-sm mb-2">
              {result.key.charAt(0).toUpperCase() + result.key.slice(1)}
            </div>
            <LocationInfo
              location={result.entity}
              className={result.action === "create" ? "ring-2 ring-green-500" : ""}
            />
          </li>
        ))}
      </ul>
    </div>
  );
};

export default FindOrCreateLocations;
