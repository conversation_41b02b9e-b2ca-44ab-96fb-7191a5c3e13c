import { DocumentPages } from "@/modules/Document/Routes.documents";
import { DocumentAggregationStep } from "@/modules/Document/Types.document";
import { Link } from "react-router-dom";

const FindMatchingPackingList = ({ operation }: { operation: DocumentAggregationStep }) => {
  if (operation.outputData?.packingList?.id) {
    return (
      <div>
        Found{" "}
        <Link
          to={DocumentPages.Detail(operation.outputData?.packingList?.id)}
          className="text-primary hover:underline"
        >
          {operation.outputData?.packingList?.name}
        </Link>
      </div>
    );
  }
  return <div>No packing list found</div>;
};

export default FindMatchingPackingList;
