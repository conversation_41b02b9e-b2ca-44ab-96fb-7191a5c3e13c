import { DocumentAggregationStep, DocumentAggregationStepLog } from "@/modules/Document/Types.document";
import { Product } from "nest-modules";
import { useMemo } from "react";
import DataGrid, { Column, ColumnOrColumnGroup, RenderCellProps } from "react-data-grid";
import "react-data-grid/lib/styles.css";
import { twMerge } from "tailwind-merge";
import { Alert } from "ui";
import { Currency, formatCurrency, formatNumber } from "utils";

interface CIRow {
  partNumber: string;
  goodsDescription: string;
  hsCode: string;
  countryOfOrigin: string;
  stateOfOrigin?: string;
  quantity: number;
  unitOfMeasure: string;
  unitPrice: number;
  totalLineValue: number;
  currencyCode: string;
  _product?: { action: "create" | "associate"; data: Product };
  index?: number;
  plIndex?: number;
}

const MergeCIPL = ({ operation }: { operation: DocumentAggregationStep }) => {
  const cellBgMap = {
    error: "bg-red-200",
    warning: "bg-yellow-200",
    info: "bg-blue-200"
  };

  const logLevels = ["error", "warning", "info"];

  const renderCell = ({ column, row, rowIdx }: RenderCellProps<Omit<CIRow, "_product">>) => {
    const logs = operation?.logs.filter(
      (log: DocumentAggregationStepLog) => log.field === `commercialInvoiceLines.${rowIdx}.${column.key}`
    );

    // sort logs by level
    logs.sort((a, b) => {
      return logLevels.indexOf(a.level) - logLevels.indexOf(b.level);
    });

    const log = logs[0];

    const cellBg = log ? cellBgMap[log.level as keyof typeof cellBgMap] : "";

    const value = row[column.key as keyof Omit<CIRow, "_product">];

    const formattedValue = useMemo(() => {
      if (!value) {
        return <span className="text-gray-500 text-xs">not provided</span>;
      }

      switch (column.key) {
        case "quantity":
          if (value && typeof value === "number") {
            return formatNumber(value);
          }
          break;
        case "unitPrice":
          if (value && typeof value === "number") {
            return formatCurrency(value, (row.currencyCode || Currency.USD) as Currency);
          }
          break;
        case "totalLineValue":
          if (value && typeof value === "number") {
            return formatCurrency(value, (row.currencyCode || Currency.USD) as Currency);
          }
          break;
        default:
          return value;
      }
    }, [value]);

    return (
      <div className={twMerge("mx-[-8px] h-full text-center flex items-center px-[8px]", cellBg)}>
        <a href={`#log-${log?.field ?? ""}`}>{formattedValue}</a>
      </div>
    );
  };

  const columns: readonly Column<CIRow>[] = [
    {
      key: "index",
      name: "CI",
      width: 48,
      renderCell: ({ row }) => {
        if (row.index !== undefined) {
          return row.index + 1;
        }
        return null;
      }
    },
    {
      key: "plIndex",
      name: "PL",
      width: 48,
      renderCell: ({ row }) => {
        if (row.plIndex !== undefined) {
          return row.plIndex + 1;
        }
        return null;
      }
    },
    {
      key: "partNumber",
      name: "Part Number",
      resizable: true,
      renderCell: renderCell
    },
    {
      key: "goodsDescription",
      name: "Goods Description",
      resizable: true,
      renderCell: renderCell
    },
    { key: "hsCode", name: "HS Code", resizable: true, renderCell: renderCell },
    {
      key: "originCountry",
      name: "Origin Country",
      resizable: true,
      renderCell: renderCell
    },
    {
      key: "originState",
      name: "Origin State",
      resizable: true,
      renderCell: renderCell
    },
    { key: "quantity", name: "Quantity", resizable: true, renderCell: renderCell },
    { key: "quantityUOM", name: "UoM", resizable: true, renderCell: renderCell },
    { key: "unitPrice", name: "Unit Price", resizable: true, renderCell: renderCell },
    { key: "totalLineValue", name: "Total Line Value", resizable: true, renderCell: renderCell },
    {
      key: "currencyCode",
      name: "Currency Code",
      resizable: true,
      renderCell: renderCell
    },
    {
      key: "weight",
      name: "Weight",
      resizable: true,
      renderCell: renderCell
    },
    {
      key: "weightUOM",
      name: "Weight UoM",
      resizable: true,
      renderCell: renderCell
    },
    {
      key: "volume",
      name: "Volume",
      resizable: true,
      renderCell: renderCell
    },
    {
      key: "volumeUOM",
      name: "Volume UoM",
      resizable: true,
      renderCell: renderCell
    }
  ];

  return (
    <div>
      {operation.status === "failed" && (
        <Alert type="error" title="Error" message={operation.errorMessage ?? "An unknown error occurred"} />
      )}
      {operation.outputData && (
        <>
          <div className="flex gap-3 justify-between">
            <div>Invoice Number: {operation.outputData?.invoiceNumber}</div>
            <div>Invoice Date: {operation.outputData?.invoiceDate}</div>
            <div>PO Number: {operation.outputData?.poNumber}</div>
          </div>

          <div className="h-px bg-gray-200 my-2" />

          <DataGrid
            // workaround for type error
            columns={columns as ColumnOrColumnGroup<unknown, unknown>[]}
            rows={operation.outputData?.commercialInvoiceLines || []}
            className="rdg-light text-xs"
            style={{
              blockSize: "100%"
            }}
          />

          <div className="h-px bg-gray-200 my-2" />

          <table className="table-auto">
            <tbody>
              <tr>
                <td>Gross Weight</td>
                <td>
                  {operation.outputData?.grossWeight} {operation.outputData?.weightUOM}
                </td>
              </tr>
              <tr>
                <td>Number of Packages</td>
                <td>
                  {operation.outputData?.numberOfPackages} {operation.outputData?.packageUOM}
                </td>
              </tr>
            </tbody>
          </table>

          <div className="h-px bg-gray-200 my-2" />

          <table className="table-auto">
            <tbody>
              <tr>
                <td>Currency</td>
                <td>{operation.outputData?.currencyCode}</td>
              </tr>
              <tr>
                <td>Total</td>
                <td>
                  {formatCurrency(
                    operation.outputData?.total ?? 0,
                    operation.outputData?.currencyCode || Currency.USD
                  )}
                </td>
              </tr>
              <tr>
                <td>Transportation Cost</td>
                <td>
                  {formatCurrency(
                    operation.outputData?.transCost ?? 0,
                    operation.outputData?.currencyCode || Currency.USD
                  )}
                </td>
              </tr>
              <tr>
                <td>Miscellaneous Cost</td>
                <td>
                  {formatCurrency(
                    operation.outputData?.miscCost ?? 0,
                    operation.outputData?.currencyCode || Currency.USD
                  )}
                </td>
              </tr>
              <tr>
                <td>Discount</td>
                <td>
                  {formatCurrency(
                    operation.outputData?.discount ?? 0,
                    operation.outputData?.currencyCode || Currency.USD
                  )}
                </td>
              </tr>
              <tr>
                <td>Invoice Total</td>
                <td>
                  {formatCurrency(
                    operation.outputData?.invoiceTotal ?? 0,
                    operation.outputData?.currencyCode || Currency.USD
                  )}
                </td>
              </tr>
            </tbody>
          </table>
        </>
      )}
    </div>
  );
};

export default MergeCIPL;
