import { commercialInvoiceLineTableSchema } from "@/modules/CommercialInvoice/Schema.commercial-invoice";
import { DocumentAggregationStep } from "@/modules/Document/Types.document";
import { Table } from "ui";

const commercialInvoiceLineCreateResultSchema = {
  ...commercialInvoiceLineTableSchema
};

const CreateCommercialInvoiceLine = ({ operation }: { operation: DocumentAggregationStep }) => {
  const mappedData = operation.outputData
    .map((result: any) => {
      const line = result.line;
      return {
        ...result.dto,
        ...line,
        productType: line?.product.productType,
        partNumber: line?.product.partNumber,
        sequence: result.sequence,
        vfdCode: line?.vfd.code,
        ttCode: line?.tt.code,
        originName: line?.origin.name,
        success: result.success,
        error: result.error
      };
    })
    .sort((a: any, b: any) => a.sequence - b.sequence);

  return (
    <div>
      <Table
        data={mappedData}
        schema={commercialInvoiceLineCreateResultSchema}
        expandableRow={(row: any) => {
          return (
            <div>
              <div>{JSON.stringify(row.error)}</div>
            </div>
          );
        }}
        getRowStyle={(row: any) => {
          return row.success ? "bg-green-100" : "bg-red-100";
        }}
      />
    </div>
  );
};

export default CreateCommercialInvoiceLine;
