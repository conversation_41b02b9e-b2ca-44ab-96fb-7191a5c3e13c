import { ChevronLeft, ChevronRight } from "lucide-react";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { twMerge } from "tailwind-merge";

interface TwoPaneLayoutProps {
  leftPane: React.ReactNode;
  rightPane: React.ReactNode;
  containerClassName?: string;
  initialRightPaneWidth?: number;
}

const TwoPaneLayout = ({
  leftPane,
  rightPane,
  containerClassName,
  initialRightPaneWidth = 400
}: TwoPaneLayoutProps) => {
  const [isRightPaneCollapsed, setIsRightPaneCollapsed] = useState(false);
  const [rightPaneWidth, setRightPaneWidth] = useState(initialRightPaneWidth);
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const resizerRef = useRef<HTMLDivElement>(null);
  const leftPaneRef = useRef<HTMLDivElement>(null);
  const rightPaneContainerRef = useRef<HTMLDivElement>(null);
  // Refs to store initial positions and widths
  const [height, setHeight] = useState<string | number>(200);

  // {{ Add refs to store initial pointer position and initial width }}
  const initialXRef = useRef<number>(0);
  const initialWidthRef = useRef<number>(rightPaneWidth);

  useEffect(() => {
    if (isResizing) return;
    if (!leftPaneRef.current) return;

    const observer = new ResizeObserver((entries) => {
      for (let entry of entries) {
        setHeight(Math.max(200, entry.contentRect.height));
      }
    });
    observer.observe(leftPaneRef.current);

    return () => {
      observer.disconnect();
    };
  }, [leftPaneRef, isResizing]);

  const toggleRightPaneCollapse = useCallback(() => {
    setIsRightPaneCollapsed((prev) => !prev);
  }, []);

  const clearSelection = useCallback(() => {
    window.getSelection()?.removeAllRanges();
  }, []);

  useEffect(() => {
    setRightPaneWidth(initialRightPaneWidth);
  }, [initialRightPaneWidth]);

  const handlePointerMove = useCallback((e: PointerEvent) => {
    if (!resizerRef.current) return;
    // {{ Update deltaX calculation based on initialXRef }}
    const deltaX = e.clientX - initialXRef.current;

    // {{ Compute new width based on initialWidthRef and deltaX }}
    let newWidth = initialWidthRef.current - deltaX;

    // Constrain the new width between 200px and containerWidth - 200px
    const containerWidth = containerRef.current
      ? containerRef.current.getBoundingClientRect().width
      : window.innerWidth;
    newWidth = Math.max(200, Math.min(newWidth, containerWidth - 200));

    setRightPaneWidth(newWidth);
  }, []);

  const handlePointerUp = useCallback(
    (e: PointerEvent) => {
      if (resizerRef.current) {
        resizerRef.current.releasePointerCapture(e.pointerId);
        document.removeEventListener("pointermove", handlePointerMove);
        document.removeEventListener("pointerup", handlePointerUp);
      }
      document.body.style.userSelect = "";
      setIsResizing(false);
    },
    [handlePointerMove]
  );

  const addResizerListeners = useCallback(() => {
    if (!resizerRef.current) return;
    document.addEventListener("pointermove", handlePointerMove);
    document.addEventListener("pointerup", handlePointerUp);
  }, [handlePointerMove, handlePointerUp]);

  const removeResizerListeners = useCallback(() => {
    if (!resizerRef.current) return;
    document.removeEventListener("pointermove", handlePointerMove);
    document.removeEventListener("pointerup", handlePointerUp);
  }, [handlePointerMove, handlePointerUp]);

  const handlePointerDown = useCallback(
    (e: React.PointerEvent) => {
      clearSelection();
      setIsResizing(true);
      document.body.style.userSelect = "none";
      if (resizerRef.current) {
        resizerRef.current.setPointerCapture(e.pointerId);
      }
      // {{ Set initial pointer and width values }}
      initialXRef.current = e.clientX;
      initialWidthRef.current = rightPaneWidth;
      addResizerListeners();
    },
    [clearSelection, rightPaneWidth, addResizerListeners]
  );

  useEffect(() => {
    const resizer = resizerRef.current;
    if (!resizer) return;

    return () => {
      removeResizerListeners();
      document.body.style.userSelect = "";
    };
  }, [removeResizerListeners]);

  const leftPaneMemorized = useMemo(() => <div ref={leftPaneRef}>{leftPane}</div>, [leftPane]);

  const rightPaneMemorized = useMemo(() => <div className="w-full">{rightPane}</div>, [rightPane]);

  return (
    <div ref={containerRef} className="flex flex-col" style={{ height: height }}>
      <div className="flex-grow flex h-full">
        <div className={twMerge("flex-grow p-4 bg-white overflow-auto", containerClassName)}>
          {leftPaneMemorized}
        </div>
        <div className="flex h-full">
          {!isRightPaneCollapsed && (
            <div
              ref={resizerRef}
              className={twMerge(
                "w-1 hover:bg-gray-300 cursor-ew-resize",
                isResizing ? "bg-primary/50" : "bg-gray-200"
              )}
              onPointerDown={handlePointerDown}
            />
          )}
          <button
            onClick={toggleRightPaneCollapse}
            className="bg-gray-200 hover:bg-gray-300 px-1 flex items-center"
          >
            {isRightPaneCollapsed ? (
              <ChevronLeft className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </button>
        </div>
        <div
          className={twMerge(
            "flex transition-all duration-300 ease-in-out",
            "p-4 overflow-auto",
            containerClassName
          )}
          style={{
            width: isRightPaneCollapsed ? 0 : rightPaneWidth,
            minWidth: isRightPaneCollapsed ? 0 : rightPaneWidth
          }}
          ref={rightPaneContainerRef}
        >
          {rightPaneMemorized}
        </div>
      </div>
    </div>
  );
};

export default TwoPaneLayout;
