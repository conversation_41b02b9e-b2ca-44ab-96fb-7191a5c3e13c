import { useStore } from "@/bootstrap/Store.bootstrap";
import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { useColumnVisibility } from "@/modules/Settings/Hooks.settings";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect, useMemo, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { Outlet, useNavigate } from "react-router-dom";
import { Button, ColumnSelector, Header, Modal, Pagination, Table } from "ui";
import { SortOrder } from "utils";
import { FileFilter, FileUpload } from "../../components";
import { FILE_TABLE_KEY } from "../../Constant.document";
import { useGetFileList } from "../../queries";
import { fileDocumentTableSchema, fileTableSchema } from "../../Schema.documents";
import { Document, File, FileColumn, GetFileList } from "../../Types.document";
import { fileIsProcessing } from "../../Utils.document";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";

const FileListPage = observer(() => {
  const { isSuperAdmin } = useStore().auth;
  const navigate = useNavigate();
  const [isUploadShow, toggleIsUploadShow] = useReducer((r) => !r, false);
  const [page, setPage] = useState(1);
  const [filter, toggleFilter] = useReducer((r) => !r, false);
  const [filters, setFilters] = useState<GetFileList>({
    sortBy: "createDate",
    sortOrder: "desc"
  } as GetFileList);
  const [isProcessing, setIsProcessing] = useState(false);
  const { canCrudBackoffice } = useBackofficePermission();

  const { visibleColumns, handleToggleColumn } = useColumnVisibility({
    defaultColumns: fileTableSchema,
    key: FILE_TABLE_KEY
  });

  const { data, isFetching, isLoading, isFetchedAfterMount, refetch, error } = useGetFileList(
    {
      page,
      limit: DEFAULT_LIMIT,
      sortBy: FileColumn.createDate,
      sortOrder: SortOrder.DESC,
      ...filters
    },
    isProcessing ? 5000 : undefined
  );

  const loadingState = useMemo(
    () => (!isFetchedAfterMount ? isFetching : isLoading),
    [isFetching, isLoading, isFetchedAfterMount]
  );

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  useEffect(() => {
    setIsProcessing(fileIsProcessing(data?.files));
  }, [data]);

  const handleFilter = useCallback((values: GetFileList) => {
    setFilters(values);
    setPage(1);
  }, []);

  const handleFileUploaded = () => {
    toast.success("Files uploaded");
    toggleIsUploadShow();
    refetch();
  };

  return (
    <div className="flex flex-col">
      <Modal id="upload-modal" title="Upload Files" show={isUploadShow} onClose={toggleIsUploadShow}>
        <FileUpload multiple={true} onFileUploaded={handleFileUploaded} />
      </Modal>

      <Header title="Document Management">
        <Button label="Filters" kind="filter" onClick={toggleFilter} />
        <ColumnSelector columns={visibleColumns} onToggleColumn={handleToggleColumn} />
        <Button label="Refresh" loading={isFetching} kind="refresh" onClick={refetch} />
        {canCrudBackoffice && (
          <Button label="Upload" kind="upload" onClick={toggleIsUploadShow} disabled={isFetching} />
        )}
      </Header>

      <section>
        <FileFilter filterValues={handleFilter} isOpen={filter} isSuperAdmin={isSuperAdmin} />
      </section>

      <main className="flex flex-col gap-3">
        <Table
          data={data?.files}
          isLoading={loadingState}
          schema={visibleColumns}
          onSort={(key?: string, direction?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key as FileColumn,
              sortOrder: direction
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
          expandableRow={(row: File) =>
            row.documents.length ? (
              <Table
                data={row.documents}
                schema={fileDocumentTableSchema}
                onClick={(row: Document) => {
                  navigate(`/document/${row.id}`);
                }}
                className="-mb-3 [&>div]:border-0 -mx-3 pl-8 lg:-mx-6 lg:pl-12"
              />
            ) : (
              <p className="text-neutral-500 dark:text-neutral-400">No document detected</p>
            )
          }
          footer={
            <Pagination currentPage={page} total={data?.total} limit={DEFAULT_LIMIT} onPageChange={setPage} />
          }
          total={data?.total}
        />
      </main>

      <Outlet />
    </div>
  );
});

export default FileListPage;
