import { enumToSelectOptions, schemaToSelectOptions } from "utils";
import { fileTableSchema } from "./Schema.documents";
import { FileStatus } from "./Types.document";

export const ACCEPT_FILE_TYPES = {
  "image/*": [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff"],
  "application/pdf": [".pdf"],
  "application/msword": [".doc"],
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".docx"],
  "application/vnd.ms-excel": [".xls"],
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx"],
  "text/csv": [".csv"]
};

export const FILE_STATUS = enumToSelectOptions(FileStatus);
export const FILE_SORT_BY = schemaToSelectOptions(fileTableSchema);

export const FILE_TABLE_KEY = "fileTable";
export const DOCUMENT_TABLE_KEY = "documentTable";
