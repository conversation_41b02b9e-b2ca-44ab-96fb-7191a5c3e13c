import { RouterObject } from "@/common/Types.common";
import { File, FileStack } from "lucide-react";
import { CertificateNavigation } from "../Certificate/Routes.certificate";
import { MailNavigation } from "../Mail/Routes.mail";
import { DocumentAggregationDetail, DocumentDetail, FileBatchDetail, FileDetail, FileList } from "./views";

const DocumentPages = {
  List: "/document",
  Detail: (document_id?: string) => `${DocumentPages.List}/${document_id}`
};

const DocumentAggregationPages = {
  List: "/document-aggregation",
  Detail: (aggregation_id?: string) => `${DocumentAggregationPages.List}/${aggregation_id}`
};

const FilePages = {
  List: "/document/file",
  Detail: (file_id?: string) => `${FilePages.List}/${file_id}`
};

const FileBatchPages = {
  List: "/document/file-batch",
  Detail: (file_batch_id?: string) => `${FileBatchPages.List}/${file_batch_id}`
};

const DocumentNavigation = {
  label: "Document",
  path: "",
  icon: <File className="size-6" />,
  submenu: [
    {
      label: "Documents",
      path: DocumentPages.List,
      icon: <FileStack className="size-6" />,
      pattern: [{ path: `${DocumentPages.List}/*` }]
    },
    MailNavigation,
    CertificateNavigation
  ]
};

const DocumentRoutes: RouterObject = [
  {
    path: DocumentPages.List,
    children: [
      {
        path: "",
        element: <FileList />,
        children: [
          { path: FilePages.Detail(":fileId"), element: <FileDetail /> },
          {
            path: DocumentPages.Detail(":documentId"),
            element: <DocumentDetail />
          }
        ]
      }
    ]
  },
  {
    path: DocumentAggregationPages.Detail(":aggregationId"),
    element: <DocumentAggregationDetail />
  }
];

const FileBatchRoutes: RouterObject = [
  {
    path: FileBatchPages.Detail(":fileBatchId"),
    element: <FileBatchDetail />,
    children: [
      {
        path: "file/:fileId",
        element: <FileDetail />
      },
      {
        path: "document/:documentId",
        element: <DocumentDetail />
      }
    ]
  }
];

export { DocumentNavigation, DocumentPages, DocumentRoutes, FileBatchPages, FileBatchRoutes, FilePages };
