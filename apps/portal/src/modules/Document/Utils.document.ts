import { File } from "nest-modules";
import { Document, DocumentStatus, FileStatus } from "./Types.document";

function fileIsProcessing(files?: File[]) {
  if (!files) return false;
  const status = files.some((file) =>
    [FileStatus.PENDING, FileStatus.PARSING, FileStatus.SPLITTING].includes(file.status)
  );
  return status;
}

function documentIsProcessing(documents?: Document[]) {
  if (!documents) return false;
  const status = documents.some((document) =>
    [DocumentStatus.PENDING, DocumentStatus.EXTRACTING].includes(document.status)
  );
  return status;
}

export { documentIsProcessing, fileIsProcessing };
