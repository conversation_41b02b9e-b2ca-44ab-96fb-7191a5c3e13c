import {
  EditImporterDto,
  GetImportersDto,
  GetImportersResponseDto,
  Importer as Imp,
  UpdateImporterWhitelistEmailsDto
} from "nest-modules";
import { DetailParams, ListParams, PaginationResponse, WithAuditNames } from "utils";

export enum ImporterColumn {
  id = "id",
  status = "status",
  companyName = "companyName",
  businessNumber = "businessNumber",
  address = "address",
  city = "city",
  state = "state",
  phoneNumber = "phoneNumber",
  fax = "fax",
  email = "email",
  officerNameAndTitle = "officerNameAndTitle",
  docusignEnvelopeId = "docusignEnvelopeId",
  rejectReason = "rejectReason",
  candataCustomerNumber = "candataCustomerNumber",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  countryId = "countryId",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export enum ImporterStatus {
  PENDING_POA = "pending-poa",
  PENDING_VERIFICATION = "pending-verification",
  REJECTED = "rejected",
  ACTIVE = "active",
  DISABLED = "disabled"
}
export enum CarmStatus {
  SENT = "sent",
  PENDING = "pending",
  ACTIVE = "active",
  INVALID = "invalid"
}
export enum PoaStatus {
  SENT = "sent",
  SIGNED = "signed"
}

export type Importer = Imp;

export type ImporterTable = Importer &
  WithAuditNames & {
    countryName?: string;
  };

export type GetImporterList = ListParams<GetImportersDto>;
export type GetImporterListResponse = PaginationResponse & GetImportersResponseDto;

export type SaveImporterParams = EditImporterDto & DetailParams;

export type ImporterFormRef = {
  dirty: boolean;
  handleSubmit: () => void;
  resetForm: () => void;
  setFieldError: (field: string, error: string) => void;
};

export type SaveImporterWhitelistParams = UpdateImporterWhitelistEmailsDto & DetailParams;

export type CarmRequestParams = {
  businessNo: string;
};
export type CarmRequestResponse = {
  result: CarmStatus;
  businessNo: string;
};
