import { ORDER_BY } from "@/common/Constant.common";
import { SelectCountry } from "@/modules/Location/components";
import { useDebounce } from "@uidotdev/usehooks";
import { useEffect, useState } from "react";
import { Input, Select } from "ui";
import { OrderBy } from "utils";
import { IMPORTER_SORT_BY, IMPORTER_STATUS } from "../../Constant.importer";
import { importerTableSchema } from "../../Schema.importer";
import { GetImporterList, ImporterStatus } from "../../Types.importer";

type Props = {
  status?: ImporterStatus;
  filterValues(values?: GetImporterList): void;
};
const ImporterFilter = ({ status, filterValues }: Props) => {
  const [values, setValues] = useState<GetImporterList>();

  const [_name, setName] = useState<string>();
  const companyName = useDebounce(_name, 500);

  useEffect(() => {
    setValues((prev) => ({
      ...prev,
      companyName: companyName ?? undefined
    }));
  }, [companyName]);

  useEffect(() => {
    filterValues(values);
  }, [filterValues, values]);

  useEffect(() => () => setValues(undefined), []);

  return (
    <div className="flex flex-wrap items-center gap-x-3 gap-y-2">
      <div className="flex gap-3">
        <Input label="Name" placeholder="Search" onTextChange={setName} kind="search" />
        <Select
          label="Status"
          onSelected={(status: ImporterStatus) => setValues((prev) => ({ ...prev, status }))}
          options={IMPORTER_STATUS}
          defaultValue={status}
          disabled={!!status}
          optional
        />
        <SelectCountry
          placeholder="Select Country"
          onSelect={(country) => {
            if (typeof country?.value === "number" || country?.value === undefined) {
              setValues((prev) => ({
                ...prev,
                countryId: country?.value as number
              }));
            }
          }}
        />
      </div>

      <div className="flex gap-3">
        <Select
          label="Sort By"
          onSelected={(sortBy: keyof typeof importerTableSchema) =>
            setValues((prev) => ({ ...prev, sortBy }) as GetImporterList)
          }
          options={IMPORTER_SORT_BY}
          optional
        />
        <Select
          label="Sort Direction"
          onSelected={(sortOrder: OrderBy) =>
            setValues((prev) => ({ ...prev, sortOrder }) as GetImporterList)
          }
          options={ORDER_BY}
          optional
        />
      </div>
    </div>
  );
};
export default ImporterFilter;
