import { useStore } from "@/bootstrap/Store.bootstrap";
import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { BaseError } from "@/common/Types.common";
import { useColumnVisibility } from "@/modules/Settings/Hooks.settings";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect, useMemo, useState } from "react";
import toast from "react-hot-toast";
import { useLocation } from "react-router-dom";
import { Button, ColumnSelector, Pagination, Popup, Table } from "ui";
import { emptyStringToNull, SortOrder } from "utils";
import { IMPORTER_TABLE_KEY } from "../../Constant.importer";
import { importerTableSchema } from "../../Schema.importer";
import {
  ImporterColumn,
  ImporterStatus,
  type GetImporterList,
  type Importer,
  type SaveImporterParams
} from "../../Types.importer";
import { useDeleteImporter, useSaveImporter } from "../../mutations";
import { useGetImporterList } from "../../queries/GetImporters.query";
import AddImporterModal from "../AddImporterModal";
import ImporterFilter from "../ImporterFilter";
import { useVerifyBusinessNumber } from "@/modules/Auth/mutations";

const ImporterList = observer(() => {
  const { state } = useLocation();
  const { isSuperAdmin, isAdmin } = useStore().auth;
  const [modal, setModal] = useState(false);
  const { showPopup, hidePopup } = Popup.usePopup();
  const [isValid, setIsValid] = useState<boolean>();

  const [importer, setImporter] = useState<Importer>();
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState<GetImporterList>();

  const { visibleColumns, handleToggleColumn } = useColumnVisibility({
    defaultColumns: importerTableSchema,
    key: IMPORTER_TABLE_KEY
  });

  const saveImporter = useSaveImporter();
  const deleteImporter = useDeleteImporter();
  const verifyBusiness = useVerifyBusinessNumber();

  const { data, error, isFetching, refetch } = useGetImporterList({
    page,
    limit: DEFAULT_LIMIT,
    ...filters
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  useEffect(() => {
    if (state?.from) {
      setModal(true);
    }
  }, [state?.from]);

  const canAddImporter = useMemo(() => {
    if (isSuperAdmin) {
      return false;
    }

    if (!data?.importers?.length) {
      return true;
    }

    const allImportersDisabled = data.importers.every(
      (importer) => importer.status === ImporterStatus.DISABLED
    );

    return allImportersDisabled;
  }, [isSuperAdmin, data?.importers]);

  //#region Handler
  const handleSaveImporter = useCallback(
    async (params: SaveImporterParams) => {
      setIsValid(undefined);
      const formatParams: SaveImporterParams = {
        ...emptyStringToNull(params),
        countryId: params.countryId ? Number(params.countryId) : undefined
      };

      try {
        if (!importer) {
          const response = await verifyBusiness.mutateAsync({
            business_number: params?.businessNumber?.slice(0, 9) ?? "",
            business_name: params?.companyName ?? ""
          });

          const _isValid = response.some((item) => item.isValid);
          setIsValid(_isValid);

          if (!_isValid) {
            toast.error("Invalid business number or company name");
            return;
          }
        }

        await saveImporter.mutateAsync(formatParams);

        const message = `Importer ${formatParams.companyName} is submitted for review. Please check your email for more instructions`;
        toast.success(message, { duration: 5000 });

        setModal(false);
        setImporter(undefined);

        refetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [importer, refetch, saveImporter, verifyBusiness]
  );

  const handleDeleteImporter = useCallback(async () => {
    try {
      await deleteImporter.mutateAsync({ id: importer?.id });

      toast.success("Importer deleted successfully");

      setModal(false);
      setImporter(undefined);
      refetch();
    } catch (error) {
      toast.error((error as BaseError).message);
    }
  }, [deleteImporter, importer?.id, refetch]);

  const handleDelete = () => {
    showPopup(
      {
        content: "Are you sure you want to delete this importer?",
        onProceed: () => {
          handleDeleteImporter();
          hidePopup();
        }
      },
      Popup.PopupType.DELETE_POPUP
    );
  };

  const handleFilter = useCallback((values: GetImporterList) => {
    setFilters(values);
    setPage(1);
  }, []);
  //#endregion

  return (
    <div className="flex flex-col gap-3">
      <AddImporterModal
        show={modal}
        info={importer}
        isAdmin={isAdmin}
        isLoading={saveImporter.isPending || deleteImporter.isPending || verifyBusiness.isPending}
        onClose={() => {
          setModal(false);
          setImporter(undefined);
        }}
        onSubmit={handleSaveImporter}
        onDelete={handleDelete}
        invalidBusinessNumber={isValid === false}
      />

      <header className="flex gap-3 items-end">
        <ImporterFilter filterValues={handleFilter} />
        <ColumnSelector
          columns={visibleColumns}
          onToggleColumn={handleToggleColumn}
          containerStyle="ml-auto"
        />
        {canAddImporter && (
          <Button
            label="New Importer"
            kind="create"
            className="ml-auto"
            onClick={() => setModal(true)}
            disabled={saveImporter.isPending || deleteImporter.isPending || verifyBusiness.isPending}
          />
        )}
      </header>

      <main>
        <Table
          data={data?.importers}
          isLoading={isFetching}
          schema={visibleColumns}
          onClick={(_data: Importer) => {
            setImporter(_data);
            setModal(true);
          }}
          onSort={(key?: string, sortOrder?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key as ImporterColumn,
              sortOrder
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
          footer={
            <Pagination currentPage={page} total={data?.total} limit={DEFAULT_LIMIT} onPageChange={setPage} />
          }
          total={data?.total}
        />
      </main>
    </div>
  );
});
export default ImporterList;
