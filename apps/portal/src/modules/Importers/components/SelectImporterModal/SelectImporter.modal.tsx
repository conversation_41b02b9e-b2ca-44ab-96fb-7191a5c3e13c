import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { useEffect, useState } from "react";
import { Button, Modal, Table } from "ui";
import { SortOrder } from "utils";
import { useGetInfiniteImporters } from "../../queries";
import { importerTableSchema } from "../../Schema.importer";
import { GetImporterList, Importer, ImporterColumn, ImporterStatus } from "../../Types.importer";
import ImporterFilter from "../ImporterFilter";

type Props = {
  show: boolean;
  importer?: Importer | null;
  required?: boolean;
  multiple?: boolean;
  onSelect(item?: Importer): void;
  onClose(): void;
};
function SelectImporterModal({ show, importer, required, multiple, onSelect, onClose }: Props) {
  const [selected, setSelected] = useState<Importer>();
  const [filters, setFilters] = useState<GetImporterList>();

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } = useGetInfiniteImporters({
    limit: DEFAULT_LIMIT,
    status: ImporterStatus.ACTIVE,
    ...filters
  });

  useEffect(() => {
    if (importer && data && data.length > 0) {
      const foundImporter = data.find((p) => p.id === importer.id);
      if (foundImporter) setSelected(foundImporter);
    }
  }, [importer, data]);

  const handleSelect = () => {
    if (!multiple) onSelect(selected ?? undefined);
  };

  return (
    <Modal
      id="select-importer-modal"
      show={show}
      onClose={onClose}
      title="Select Importer"
      size="4xl"
      actions={
        <>
          {/* <Button
            label="Create"
            kind="create"
            onClick={() => {
              navigate(`${SettingsPages.List}/${SettingsPages.Importers}`, {
                state: {
                  from: location.pathname
                }
              });
            }}
          /> */}

          <Button
            label={required ? "Select" : selected ? "Select" : "Clear"}
            onClick={handleSelect}
            disabled={!data?.length || (required && !selected)}
          />
        </>
      }
    >
      <div className="flex flex-col gap-4">
        <ImporterFilter filterValues={setFilters} status={ImporterStatus.ACTIVE} />
        <Table
          data={data}
          schema={importerTableSchema}
          checklist
          selected={selected ? [selected] : undefined}
          onClick={setSelected}
          isLoading={isLoading}
          onEndReached={fetchNextPage}
          hasNextPage={hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
          onSort={(key?: string, sortOrder?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key as ImporterColumn,
              sortOrder
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
        />
      </div>
    </Modal>
  );
}
export default SelectImporterModal;
