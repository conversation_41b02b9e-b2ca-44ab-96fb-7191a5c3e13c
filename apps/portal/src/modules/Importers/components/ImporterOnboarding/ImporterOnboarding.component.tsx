import { useStore } from "@/bootstrap/Store.bootstrap";
import { BaseError } from "@/common/Types.common";
import { useVerifyBusinessNumber } from "@/modules/Auth/mutations";
import { Upload } from "lucide-react";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import toast from "react-hot-toast";
import { Button, Card, Header, Popup } from "ui";
import { emptyStringToNull } from "utils";
import {
  ImporterFormRef,
  ImporterStatus,
  type Importer,
  type SaveImporterParams
} from "../../Types.importer";
import { useSaveImporter } from "../../mutations";
import { useGetImporterOnboarding } from "../../queries/GetImporters.query";
import ImporterForm from "../ImporterForm";
import ImporterWarning from "../ImporterWarning";

const ImporterOnboarding = observer(() => {
  const { session } = useStore().auth;
  const { showPopup, hidePopup } = Popup.usePopup();
  const [isValid, setIsValid] = useState<boolean>();

  const ref = useRef<ImporterFormRef>(null);
  const [importer, setImporter] = useState<Importer>();

  const verifyBusiness = useVerifyBusinessNumber();
  const saveImporter = useSaveImporter();

  const { data, error, isFetching, refetch } = useGetImporterOnboarding({
    limit: 1
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  useEffect(() => {
    if (data?.importers && data?.importers.length > 0) {
      setImporter(data?.importers[0]);
    }
  }, [data]);

  useEffect(() => {
    if (importer?.status === ImporterStatus.ACTIVE) {
      location.reload();
    }
  }, [importer]);

  const readOnly = useMemo(
    () =>
      importer &&
      importer.status !== ImporterStatus.PENDING_POA &&
      importer.status !== ImporterStatus.REJECTED,
    [importer]
  );

  //#region Submit handler
  const handleSaveImporter = useCallback(
    async (params: SaveImporterParams) => {
      setIsValid(undefined);

      const formatParams: SaveImporterParams = {
        ...emptyStringToNull(params),
        countryId: params.countryId ? Number(params.countryId) : undefined
      };

      try {
        if (!importer) {
          const response = await verifyBusiness.mutateAsync({
            business_number: params?.businessNumber?.slice(0, 9) ?? "",
            business_name: params?.companyName ?? ""
          });

          const _isValid = response.some((item) => item.isValid);
          setIsValid(_isValid);

          if (!_isValid) {
            ref.current?.setFieldError("businessNumber", "Invalid Business Number");

            toast.error("Invalid business number or company name");
            return;
          }
        }

        await saveImporter.mutateAsync(formatParams);

        const message = `Importer ${formatParams.companyName} is submitted for review. Please check your email for more instructions`;
        toast.success(message, { duration: 5000 });

        refetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [importer, refetch, saveImporter, verifyBusiness]
  );

  const submitPrompt = (values: SaveImporterParams) => {
    showPopup({
      content: "Are you sure all the information is correct?",
      onProceed: () => {
        handleSaveImporter(values);
        hidePopup();
      }
    });
  };
  //#endregion

  const renderHeader = () => {
    switch (importer?.status) {
      case ImporterStatus.PENDING_POA:
        return <h4>Importer Status: Waiting PoA (Power of Attorney)</h4>;
      case ImporterStatus.PENDING_VERIFICATION:
        return <h4>Importer Status: Waiting for Verification</h4>;
      case ImporterStatus.REJECTED:
        return <h4>Importer Status: Rejected</h4>;
      case ImporterStatus.ACTIVE:
        return null;
      default:
        return <h4>Let's add your first importer</h4>;
    }
  };

  return (
    <div className="flex flex-col gap-3 p-4">
      <h4>Welcome to Claro, {session?.organization?.name}!</h4>

      <Card>
        <Header title={renderHeader()} containerStyle="border">
          {!readOnly && (
            <Button
              label="Submit"
              icon={<Upload size={16} />}
              className="ml-auto"
              onClick={() => ref.current?.handleSubmit()}
              loading={saveImporter.isPending || verifyBusiness.isPending}
              disabled={isFetching}
            />
          )}
        </Header>

        <div className="p-4 pt-2 flex flex-col gap-3">
          {importer && <ImporterWarning importer={importer} />}

          <ImporterForm
            ref={ref}
            importer={importer}
            isLoading={isFetching || saveImporter.isPending || verifyBusiness.isPending}
            readOnly={readOnly}
            onSubmit={submitPrompt}
            invalidBusinessNumber={isValid === false}
          />
        </div>
      </Card>
    </div>
  );
});
export default ImporterOnboarding;
