import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";
import { useMemo, useRef } from "react";
import { Button, Modal } from "ui";
import {
  ImporterFormRef,
  ImporterStatus,
  type Importer,
  type SaveImporterParams
} from "../../Types.importer";
import ImporterForm from "../ImporterForm";
import ImporterWarning from "../ImporterWarning";

type Props = {
  show: boolean;
  info?: Importer;
  isLoading?: boolean;
  isAdmin?: boolean;
  invalidBusinessNumber?: boolean;
  onSubmit(importer: SaveImporterParams): void;
  onDelete(): void;
  onClose(): void;
};
function AddImporterModal({
  show,
  info,
  isLoading,
  isAdmin,
  invalidBusinessNumber,
  onSubmit,
  onDelete,
  onClose
}: Props) {
  const ref = useRef<ImporterFormRef>(null);
  const { canCrudBackoffice } = useBackofficePermission();

  const readOnly = useMemo(
    () =>
      (info && info.status !== ImporterStatus.PENDING_POA && info.status !== ImporterStatus.REJECTED) ||
      !canCrudBackoffice,
    [canCrudBackoffice, info]
  );

  return (
    <Modal
      id="importer-modal"
      show={show}
      size="4xl"
      onClose={() => {
        ref.current?.resetForm();
        onClose();
      }}
      title={`${info ? "Edit" : "Add New"} Importer`}
      actions={
        <>
          {info && isAdmin && !readOnly && (
            <Button
              label="Delete"
              kind="delete"
              loading={isLoading}
              disabled={ref.current?.dirty}
              onClick={onDelete}
            />
          )}
          {!readOnly && (
            <Button
              label={info ? "Update" : "Add"}
              kind="update"
              loading={isLoading}
              onClick={() => ref.current?.handleSubmit()}
            />
          )}
        </>
      }
    >
      <div className="flex flex-col gap-3">
        {info && <ImporterWarning importer={info} />}

        <ImporterForm
          ref={ref}
          importer={info}
          isLoading={isLoading}
          readOnly={readOnly}
          onSubmit={onSubmit}
          invalidBusinessNumber={invalidBusinessNumber}
        />
      </div>
    </Modal>
  );
}
export default AddImporterModal;
