import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Warning, <PERSON>Alert } from "lucide-react";
import { twMerge } from "tailwind-merge";
import { Importer, ImporterStatus } from "../../Types.importer";

type Props = { importer: Importer };
function ImporterWarning({ importer }: Props) {
  if (importer.status === ImporterStatus.ACTIVE || importer.status === ImporterStatus.DISABLED) return null;

  return (
    <div
      className={twMerge(
        "border text-sm rounded-lg p-4 pr-2",
        importer.status === ImporterStatus.PENDING_POA &&
          "bg-amber-50 border border-amber-300 text-amber-800 dark:bg-amber-800/10 dark:border-amber-900 dark:text-amber-500",
        importer.status === ImporterStatus.PENDING_VERIFICATION &&
          "bg-green-50 border border-green-300 text-green-800 dark:bg-green-800/10 dark:border-green-900 dark:text-green-500",
        importer.status === ImporterStatus.REJECTED &&
          "bg-red-50 border border-red-300 text-red-800 dark:bg-red-800/10 dark:border-red-900 dark:text-red-500"
      )}
      role="alert"
    >
      <div className="flex items-center">
        <div className="flex-shrink-0">
          {importer.status === ImporterStatus.PENDING_POA && <FileWarning />}
          {importer.status === ImporterStatus.PENDING_VERIFICATION && <TriangleAlert />}
          {importer.status === ImporterStatus.REJECTED && <CircleAlert />}
        </div>
        <div className="ms-4">
          <span className="font-medium">
            {importer.status === ImporterStatus.PENDING_POA &&
              "The PoA has been sent. Kindly check your email and provide your signature."}
            {importer.status === ImporterStatus.PENDING_VERIFICATION &&
              "The importer is currently in the process of verification. Please check back later for updates. Thank you."}
            {importer.status === ImporterStatus.REJECTED &&
              "The importer has been rejected. Please check the reason below resubmit, thank you."}
          </span>
          {importer.rejectReason && (
            <div className="mt-1 ml-1 text-sm text-balance text-red-700 dark:text-red-400">
              -- {importer.rejectReason}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
export default ImporterWarning;
