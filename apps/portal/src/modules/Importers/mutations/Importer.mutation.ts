import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import ImporterService from "../services";
import type { SaveImporterParams, SaveImporterWhitelistParams } from "../Types.importer";

const useSaveImporter = () =>
  useMutation({
    mutationFn: async (params: SaveImporterParams) => {
      if (params.id) {
        return await ImporterService.edit(params);
      }
      return await ImporterService.create(params);
    }
  });

const useDeleteImporter = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await ImporterService.remove(params)
  });

// const useSendPoa = () =>
//   useMutation({
//     mutationFn: async (params: DetailParams) => await ImporterService.sendPoa(params)
//   });

const useDisableImporter = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await ImporterService.disable(params)
  });

const useUpdateImporterWhitelist = () =>
  useMutation({
    mutationFn: async (params: SaveImporterWhitelistParams) => await ImporterService.updateWhitelist(params)
  });

export { useDeleteImporter, useDisableImporter, useSaveImporter, useUpdateImporterWhitelist };
