import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { flattenQueryResults, getAuditNames, infiniteQueryMapper } from "utils";
import ImporterService from "../services";
import {
  ImporterStatus,
  type GetImporterList,
  type GetImporterListResponse,
  type Importer
} from "../Types.importer";

export const getImporterDto = (importer: Importer) => ({
  ...importer,
  countryName: importer.country?.name || "",
  organizationName: importer.organization?.name || "",
  ...getAuditNames(importer)
});

export const getImportersKey = "getImporters";

export const useGetImporters = <R = GetImporterListResponse>(
  params?: GetImporterList,
  select?: (data: GetImporterListResponse) => R,
  refetchOnMount = false
) =>
  useQuery({
    queryKey: [getImportersKey, params],
    queryFn: () => ImporterService.list(params),
    select,
    refetchOnMount
  });

export const useGetImporterList = (params?: GetImporterList) =>
  useGetImporters(
    params,
    (data: GetImporterListResponse) => {
      const importers = data.importers.map(getImporterDto);
      return { ...data, importers };
    },
    true
  );

export const useGetImporterOnboarding = (params?: GetImporterList) =>
  useGetImporters(
    params,
    (data: GetImporterListResponse) => {
      const importers = data.importers
        .map(getImporterDto)
        .filter((importer) => importer.status !== ImporterStatus.DISABLED);

      return { ...data, importers };
    },
    true
  );

export const useGetActiveImporter = () =>
  useGetImporters({ limit: 1, status: ImporterStatus.ACTIVE }, (data: GetImporterListResponse) => {
    const importers = data.importers.map(getImporterDto);

    return importers.length > 0 ? importers[0] : undefined;
  });

export const useGetInfiniteImporters = (params?: GetImporterList, refetchOnMount = false) => {
  const result = useInfiniteQuery({
    queryKey: [getImportersKey, params],
    queryFn: ({ pageParam }) => ImporterService.list({ ...params, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: infiniteQueryMapper,
    refetchOnMount,
    select: (data) => flattenQueryResults("importers", data.pages).map(getImporterDto)
  });

  return result;
};
