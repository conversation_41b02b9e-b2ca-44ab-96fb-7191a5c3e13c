import { auditTableSchema, TableSchema } from "utils";
import { mixed, number, object, ObjectSchema, string } from "yup";
import type { CarmStatus, ImporterStatus, ImporterTable, SaveImporterParams } from "./Types.importer";
import { CarmLabel, ImporterLabel } from "./components";

export const importerTableSchema: TableSchema<ImporterTable> = {
  companyName: {
    header: "Company Name",
    style: "font-medium",
    visible: true
  },
  status: {
    header: "Status",
    renderer: ({ value }: { value: ImporterStatus }) => <ImporterLabel value={value} />,
    visible: true
  },
  carmStatus: {
    header: "Carm Status",
    renderer: ({ value }: { value: CarmStatus }) => <CarmLabel value={value} />,
    visible: true
  },
  email: {
    header: "Email",
    visible: true
  },
  receiveEmail: {
    header: "Customs Processing Email",
    visible: true
  },
  businessNumber: {
    header: "Business Number"
  },
  phoneNumber: {
    header: "Phone",
    visible: true
  },
  fax: {
    header: "Fax"
  },
  address: {
    header: "Address",
    visible: true
  },
  city: {
    header: "City",
    visible: true
  },
  countryName: {
    header: "Country",
    visible: true,
    sortKey: "countryId"
  },
  ...auditTableSchema
};

export const saveImporterSchema: ObjectSchema<SaveImporterParams> = object({
  id: number().optional(),
  companyName: string().required("Company name is required"),
  // businessNumber: string().required("Business number is required"),
  businessNumber: string()
    .required("Business number is required")
    .matches(
      /^\d{9}RM00\d{2}$/,
      "Business number must be in format: 9 digits + RM00 + 2 digits (e.g. 123456789RM0001)"
    ),
  email: string().email("Email is not valid").required("Email is required"),
  phoneNumber: string().required("Phone number is required"),
  fax: string().optional(),
  address: string().required("Address is required").max(70, "Address is limited to 70 characters"),
  city: string().required("City is required"),
  state: string().required("State is required"),
  postalCode: string().required("Postal code is required"),
  countryId: number().required("Country is required"),
  officerNameAndTitle: string().required("Officer name is required"),
  carmStatus: mixed<CarmStatus>().optional(),
  carmApiKey: string().optional(),
  candataCustomerNumber: string().optional()
  // whitelistEmails: array(
  //   string().email("Email is not valid").required("Whitelist email is required")
  // ).defined()
});
