import Http from "@/bootstrap/Http.bootstrap";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import type {
  GetImporterList,
  GetImporterListResponse,
  Importer,
  SaveImporterParams,
  SaveImporterWhitelistParams
} from "../Types.importer";

const IMPORTER_ROUTE = "importers";
/**
 * Get importer list
 *
 * @returns
 */
async function list(params?: GetImporterList): Promise<GetImporterListResponse> {
  try {
    return await Http.get(stringifyQueryParams(IMPORTER_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get importer detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<Importer> {
  try {
    return await Http.get(`${IMPORTER_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create importer
 *
 * @returns
 */
async function create(params: SaveImporterParams): Promise<Importer> {
  try {
    return await Http.post(IMPORTER_ROUTE, params, { timeout: 120000 });
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit importer
 *
 * @returns
 */
async function edit({ id, ...params }: SaveImporterParams): Promise<Importer> {
  try {
    return await Http.put(`${IMPORTER_ROUTE}/${id}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Delete importer
 *
 * @returns
 */
async function remove({ id }: DetailParams): Promise<void> {
  try {
    return await Http.delete(`${IMPORTER_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Send POA email
 *
 * @returns
 */
async function sendPoa({ id }: DetailParams): Promise<void> {
  try {
    return await Http.post(`${IMPORTER_ROUTE}/${id}/send-sign-poa-email`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Disable importer
 *
 * @returns
 */
async function disable({ id }: DetailParams): Promise<void> {
  try {
    return await Http.post(`${IMPORTER_ROUTE}/${id}/disable`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Update whitelist emails
 *
 * @returns
 */
async function updateWhitelist({ id, ...params }: SaveImporterWhitelistParams): Promise<void> {
  try {
    return await Http.put(`${IMPORTER_ROUTE}/${id}/whitelist-emails`, params);
  } catch (error) {
    throw await handleError(error);
  }
}

export { create, disable, edit, get, list, remove, sendPoa, updateWhitelist };
