import { enumToSelectOptions, schemaToSelectOptions } from "utils";
import { productTableSchema } from "./Schema.product";
import { ProductAttribute, ProductColumn, ProductType } from "./Types.product";

export const PRODUCT_COLUMN = enumToSelectOptions(ProductColumn);
export const PRODUCT_ATTRIBUTE = enumToSelectOptions(ProductAttribute);

export const PRODUCT_SORT_BY = schemaToSelectOptions(productTableSchema);
export const PRODUCT_TABLE_KEY = "productTable";
export const PRODUCT_TYPE = enumToSelectOptions(ProductType);

export const ProductPages = {
  List: "/product",
  Detail: (product_id?: string) => `${ProductPages.List}/${product_id}`
};
