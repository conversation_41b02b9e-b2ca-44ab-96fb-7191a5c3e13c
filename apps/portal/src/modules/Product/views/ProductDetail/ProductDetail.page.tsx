import { BaseError } from "@/common/Types.common";
import { Breadcrumbs } from "@/components";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect, useRef } from "react";
import toast from "react-hot-toast";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { <PERSON><PERSON>rdion, <PERSON><PERSON>, Card, Header, Popup } from "ui";
import { emptyStringToNull } from "utils";
import {
  ProductCertificateInformation,
  ProductComplianceInformation,
  ProductForm,
  ShipmentInformation
} from "../../components";
import { ProductPages } from "../../Constant.product";
import { useDeleteProduct, useSaveProduct } from "../../mutations";
import { useGetProductDetail } from "../../queries";
import { ProductFormRef, SaveProductParams } from "../../Types.product";

const ProductDetail = observer(() => {
  const { product_id } = useParams();
  const { state } = useLocation();
  const navigate = useNavigate();
  const isCreate = !product_id || product_id === "create";
  const { showPopup, hidePopup } = Popup.usePopup();

  const {
    data: info,
    isFetching,
    error,
    refetch
  } = useGetProductDetail({
    id: isCreate ? undefined : product_id
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
      navigate(ProductPages.List);
    }
  }, [error, navigate]);

  const { canCrudBackoffice } = useBackofficePermission(info?.organization?.id);

  const ref = useRef<ProductFormRef>(null);

  const saveProduct = useSaveProduct();
  const deleteProduct = useDeleteProduct();

  const handleSaveProduct = useCallback(
    async (params: SaveProductParams) => {
      const formatParams: SaveProductParams = {
        partNumber: params.partNumber?.toUpperCase(),
        sku: params.sku?.toUpperCase(),
        upc: params.upc?.toUpperCase(),
        originId: params.originId ? Number(params.originId) : undefined,
        ...emptyStringToNull(params)
      };
      try {
        const product = await saveProduct.mutateAsync(formatParams);
        toast.success(`Product ${formatParams.hsCode} ${product ? "saved" : "added"}`);

        if (state?.from) {
          navigate(state.from, {
            replace: true,
            state: {
              product
            }
          });
        } else if (isCreate) {
          navigate(ProductPages.Detail(String(product?.id)), {
            replace: true
          });
        } else refetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [isCreate, navigate, refetch, saveProduct, state?.from]
  );

  const handleDeleteProduct = useCallback(async () => {
    try {
      await deleteProduct.mutateAsync({ id: info?.id });

      toast.success("Product deleted successfully");

      navigate(-1);
    } catch (error) {
      toast.error((error as BaseError).message);
    }
  }, [deleteProduct, info?.id, navigate]);

  const handleDelete = () => {
    showPopup(
      {
        content: "Are you sure you want to delete this product?",
        onProceed: () => {
          handleDeleteProduct();
          hidePopup();
        }
      },
      Popup.PopupType.DELETE_POPUP
    );
  };

  return (
    <div>
      <Header title={`${info ? "Edit" : "Create"} Product`}>
        {info && canCrudBackoffice && (
          <Button
            label="Delete"
            kind="delete"
            loading={deleteProduct.isPending}
            disabled={ref.current?.dirty || saveProduct.isPending || isFetching}
            onClick={handleDelete}
          />
        )}
        {canCrudBackoffice && (
          <Button
            label={info ? "Update" : "Create"}
            kind="update"
            loading={saveProduct.isPending}
            disabled={isFetching}
            onClick={() => ref.current?.handleSubmit()}
          />
        )}
      </Header>

      <Card>
        <Breadcrumbs />

        <div className="flex flex-col gap-3 p-4">
          <ProductForm
            ref={ref}
            info={info}
            isLoading={saveProduct.isPending}
            isDeleteLoading={deleteProduct.isPending}
            onSubmit={handleSaveProduct}
          />

          {info && (
            <>
              <ProductComplianceInformation product={info} organization={info.organization} />
              <ProductCertificateInformation product={info} organization={info.organization} />
              <Accordion title="Expired Certificate Information">
                <span>Coming soon</span>
              </Accordion>
              <ShipmentInformation productId={info.id} />
            </>
          )}
        </div>
      </Card>
    </div>
  );
});
export default ProductDetail;
