import { useStore } from "@/bootstrap/Store.bootstrap";
import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { useColumnVisibility } from "@/modules/Settings/Hooks.settings";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { Button, ColumnSelector, Header, Pagination, Table } from "ui";
import { SortOrder } from "utils";
import { PRODUCT_TABLE_KEY, ProductPages } from "../../Constant.product";
import { productTableSchema } from "../../Schema.product";
import { GetProductList, Product, ProductColumn } from "../../Types.product";
import { ProductFilter } from "../../components";
import { useGetProductList } from "../../queries";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";

const ProductList = observer(() => {
  const navigate = useNavigate();
  const { isSuperAdmin } = useStore().auth;
  const [page, setPage] = useState(1);
  const [filter, toggleFilter] = useReducer((r) => !r, false);
  const [filters, setFilters] = useState<GetProductList>();
  const { canCrudBackoffice } = useBackofficePermission();

  const { visibleColumns, handleToggleColumn } = useColumnVisibility({
    defaultColumns: productTableSchema,
    key: PRODUCT_TABLE_KEY
  });

  const { data, error, isFetching } = useGetProductList({
    page,
    limit: DEFAULT_LIMIT,
    sortBy: ProductColumn.createDate,
    sortOrder: SortOrder.DESC,
    ...filters
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const handleFilter = useCallback((values: GetProductList) => {
    setFilters(values);
    setPage(1);
  }, []);

  return (
    <div className="flex flex-col">
      <Header title="Product List">
        <Button label="Filters" kind="filter" onClick={toggleFilter} />
        <ColumnSelector columns={visibleColumns} onToggleColumn={handleToggleColumn} />
        {canCrudBackoffice && (
          <Button
            label="New Product"
            kind="create"
            className="ml-auto"
            onClick={() => navigate(ProductPages.Detail("create"))}
          />
        )}
      </Header>

      <section>
        <ProductFilter filterValues={handleFilter} isOpen={filter} isSuperAdmin={isSuperAdmin} />
      </section>

      <main>
        <Table
          data={data?.products}
          isLoading={isFetching}
          schema={visibleColumns}
          onClick={(_data: Product) => navigate(ProductPages.Detail(_data.id.toString()))}
          onSort={(key?: string, direction?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key as ProductColumn,
              sortOrder: direction
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
          footer={
            <Pagination currentPage={page} total={data?.total} limit={DEFAULT_LIMIT} onPageChange={setPage} />
          }
          total={data?.total}
        />
      </main>
    </div>
  );
});
export default ProductList;
