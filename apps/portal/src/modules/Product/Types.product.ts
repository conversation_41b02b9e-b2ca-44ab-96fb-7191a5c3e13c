import {
  EditProductDto,
  GetProductsDto,
  GetProductsResponseDto,
  GetRelatedCommercialInvoiceLinesDto,
  GetRelatedCommercialInvoiceLinesResponseDto,
  Product as Prod
} from "nest-modules";
import { DetailParams, ListParams, PaginationResponse, WithAuditNames } from "utils";

export enum ProductAttribute {
  PRODUCT_ID = "id",
  PART_NUMBER = "partNumber",
  SKU = "sku",
  UPC = "upc",
  VENDOR_PART_NUMBER = "vendorPartNumber",
  DESCRIPTION = "description",
  HS_CODE = "hsCode",
  VENDOR = "vendorId",
  ORIGIN = "originId"
}
export enum ProductColumn {
  id = "id",
  partNumber = "partNumber",
  sku = "sku",
  upc = "upc",
  vendorPartNumber = "vendorPartNumber",
  description = "description",
  hsCode = "hsCode",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  vendorId = "vendorId",
  originId = "originId",
  manufacturerId = "manufacturerId",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}
export enum ProductType {
  TEMPORARY = "temporary",
  REGULAR = "regular"
}

export type Product = Prod;

export type ProductTable = Product &
  WithAuditNames & {
    originName?: string;
    vendorName?: string;
    vendorCode?: string;
    manufacturerName?: string;
  };

export type GetProductList = ListParams<GetProductsDto>;
export type GetProductListResponse = PaginationResponse & GetProductsResponseDto;

export type SaveProductParams = EditProductDto & DetailParams;

export type ProductFormRef = {
  dirty: boolean;
  handleSubmit: () => void;
  resetForm: () => void;
};

export type GetProductShipmentList = ListParams<GetRelatedCommercialInvoiceLinesDto> & DetailParams;
export type GetProductShipmentListResponse = PaginationResponse & GetRelatedCommercialInvoiceLinesResponseDto;
