import { useRef } from "react";
import { Button, Modal } from "ui";
import { Product, ProductFormRef, SaveProductParams } from "../../Types.product";
import ProductForm from "../ProductForm";
import { emptyStringToNull } from "utils";
import { useLocation } from "react-router-dom";
import { Country, State } from "@/modules/Location/Types.location";

type Props = {
  show: boolean;
  info?: Product;
  origin?: Country;
  originState?: State;
  isLoading?: boolean;
  onSubmit(params: SaveProductParams): void;
  onClose(): void;
};
function AddProductModal({ show, info, origin, originState, isLoading, onSubmit, onClose }: Props) {
  const { state } = useLocation();
  const ref = useRef<ProductFormRef>(null);
  const handleSubmit = (params: SaveProductParams) => {
    onSubmit({ ...emptyStringToNull(params) });
  };

  return (
    <Modal
      id="add-product-modal"
      show={show}
      onClose={onClose}
      title={info ? "Edit Product" : "Create Temporary Product"}
      size="4xl"
      actions={
        <Button
          label={info ? "Update" : "Create"}
          kind="update"
          loading={isLoading}
          onClick={() => ref.current?.handleSubmit()}
        />
      }
    >
      <ProductForm
        ref={ref}
        info={info}
        isLoading={isLoading}
        onSubmit={handleSubmit}
        vendor={state?.invoice?.vendor}
        manufacturer={state?.invoice?.manufacturer}
        origin={origin}
        originState={originState}
      />
    </Modal>
  );
}
export default AddProductModal;
