import { ShipmentPages } from "@/modules/Shipment/Routes.shipment";
import { RelatedCommercialInvoiceLineDto } from "nest-modules";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { Accordion, Table } from "ui";
import { useGetProductShipmentList } from "../../queries";
import { productShipmentSchema } from "../../Schema.product";

function ShipmentInformation({ productId }: { productId: number }) {
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(false);
  const [id, setId] = useState<number>();

  const { data, error, isFetching } = useGetProductShipmentList({
    id,
    limit: 1000
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  useEffect(() => {
    if (isOpen && !id) {
      setId(productId);
    }
  }, [isOpen, id, productId]);

  return (
    <Accordion title="Shipment Information" onToggle={setIsOpen}>
      <div className="flex flex-col gap-3">
        <Table
          data={data?.commercialInvoiceLines}
          isLoading={isFetching}
          schema={productShipmentSchema}
          onClick={(d: RelatedCommercialInvoiceLineDto) => {
            navigate(ShipmentPages.Detail(d.shipmentId));
          }}
        />
      </div>
    </Accordion>
  );
}

export default ShipmentInformation;
