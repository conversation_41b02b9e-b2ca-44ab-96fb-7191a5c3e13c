import { useStore } from "@/bootstrap/Store.bootstrap";
import { SelectSourceModal } from "@/modules/Compliance/components";
import { MatchingRuleSourceDatabaseTable } from "@/modules/Compliance/Types.compliance";
import SelectCountry from "@/modules/Location/components/SelectCountry";
import { SelectPartnerModal } from "@/modules/TradePartner/components";
import { PartnerType, TradePartner } from "@/modules/TradePartner/Types.partner";
import { useFormik } from "formik";
import { observer } from "mobx-react-lite";
import { forwardRef, useEffect, useImperativeHandle, useReducer, useState } from "react";
import { Input, InputHint, Textarea } from "ui";
import { formikInputOpts, getFormikError } from "utils";
import { saveProductSchema } from "../../Schema.product";
import { Product, ProductFormRef, SaveProductParams } from "../../Types.product";
import { Country, State } from "@/modules/Location/Types.location";
import { SelectState } from "@/modules/Location/components";
import { stateRequired } from "@/common/Utils.common";

type Props = {
  info?: Product;
  vendor?: TradePartner;
  manufacturer?: TradePartner;
  origin?: Country;
  originState?: State;
  isLoading?: boolean;
  isDeleteLoading?: boolean;
  onSubmit: (info: SaveProductParams) => void;
};
const ProductForm = observer(
  forwardRef<ProductFormRef, Props>(
    ({ info, vendor, manufacturer, origin, originState, onSubmit }: Props, ref) => {
      const { isSuperAdmin } = useStore().auth;

      const [hsCodeModal, toggleHsCodeModal] = useReducer((r) => !r, false);
      const [partnerModal, setPartnerModal] = useState<PartnerType>();
      const [_vendor, setVendor] = useState<TradePartner | undefined>(vendor);
      const [_manufacturer, setManufacturer] = useState<TradePartner | undefined>(manufacturer);

      useEffect(() => {
        if (info?.vendor) setVendor(info.vendor);
        if (info?.manufacturer) setManufacturer(info.manufacturer);
      }, [info?.vendor, info?.manufacturer]);

      const formik = useFormik({
        initialValues: {
          id: info?.id,
          partNumber: info?.partNumber || "",
          sku: info?.sku || "",
          upc: info?.upc || "",
          vendorPartNumber: info?.vendorPartNumber || "",
          description: info?.description || "",
          hsCode: info?.hsCode || "",
          vendorId: info?.vendor?.id || vendor?.id,
          manufacturerId: info?.manufacturer?.id || manufacturer?.id,
          originId: info?.origin?.id || origin?.id,
          stateRequired: stateRequired(info?.origin?.alpha2 || origin?.alpha2),
          originStateId: info?.originState?.id || originState?.id,
          origin: info?.origin || origin,
          originState: info?.originState || originState,
          vendor: info?.vendor || vendor,
          manufacturer: info?.manufacturer || manufacturer
        },
        validationSchema: saveProductSchema,
        validateOnBlur: true,
        enableReinitialize: true,
        onSubmit
      });

      useImperativeHandle(ref, () => {
        return {
          dirty: formik.dirty,
          handleSubmit: formik.handleSubmit,
          resetForm: formik.resetForm
        };
      }, [formik]);

      return (
        <div>
          {partnerModal && (
            <SelectPartnerModal
              show={!!partnerModal}
              onClose={() => setPartnerModal(undefined)}
              partnerType={partnerModal}
              partner={partnerModal === PartnerType.VENDOR ? _vendor : _manufacturer}
              onSelect={(_partner?: TradePartner) => {
                if (partnerModal === PartnerType.VENDOR) {
                  setVendor(_partner);
                  formik.setFieldValue("vendorId", _partner?.id);
                  formik.setFieldValue("vendor", _partner);
                } else {
                  setManufacturer(_partner);
                  formik.setFieldValue("manufacturerId", _partner?.id);
                  formik.setFieldValue("manufacturer", _partner);
                }
                setPartnerModal(undefined);
              }}
              required={partnerModal === PartnerType.VENDOR}
            />
          )}
          {hsCodeModal && (
            <SelectSourceModal
              show={hsCodeModal}
              source={MatchingRuleSourceDatabaseTable.CANADA_TARIFF}
              onClose={toggleHsCodeModal}
              hsCodeLength="10"
              onSelect={(data) => {
                formik.setFieldValue("hsCode", data?.hsCode);
                toggleHsCodeModal();
              }}
              canSelect={(item) => item.hsCode?.length === 10}
            />
          )}

          <div className="flex flex-col gap-3">
            <div className="flex gap-3">
              {/* <div className="border p-2 ">
              <span>Upload Document Area</span>
            </div> */}

              <form className="flex-1 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-2">
                <Input
                  label="Part Number"
                  placeholder="Input part number"
                  {...formikInputOpts(formik, "partNumber")}
                />
                <Input label="SKU" placeholder="Input SKU" {...formikInputOpts(formik, "sku")} />
                <Input label="UPC" placeholder="Input UPC" {...formikInputOpts(formik, "upc")} />
                <Input
                  label="Vendor Part Number"
                  placeholder="Input vendor part number"
                  {...formikInputOpts(formik, "vendorPartNumber")}
                />
                <Input
                  label="HS Code"
                  placeholder="Input HS Code"
                  {...formikInputOpts(formik, "hsCode")}
                  hint={<InputHint label="Search" onClick={toggleHsCodeModal} />}
                  kind="search"
                />
                <SelectCountry
                  name="originId"
                  placeholder="Select Origin"
                  value={formik.values.origin}
                  onSelect={(_origin) => {
                    formik.setFieldValue("originId", _origin?.value);
                    formik.setFieldValue("stateRequired", stateRequired(_origin?.alpha2));

                    if (!_origin || !stateRequired(_origin?.alpha2)) {
                      formik.setFieldValue("originStateId", undefined);
                    }
                  }}
                  getCountryObject={(value) => {
                    formik.setFieldValue("origin", value);
                  }}
                  error={getFormikError(formik, "originId")}
                />
                {formik.values.originId && formik.values.stateRequired && (
                  <SelectState
                    label="Origin State"
                    name="originStateId"
                    placeholder="Select Origin State"
                    value={formik.values.originState}
                    countryId={formik.values.originId}
                    onSelect={(value) => formik.setFieldValue("originStateId", value ?? "")}
                    onBlur={formik.handleBlur}
                    error={getFormikError(formik, "originStateId")}
                  />
                )}
                <Input
                  label="Vendor"
                  placeholder="Select Vendor"
                  value={_vendor?.name ?? ""}
                  onClick={() => {
                    setPartnerModal(PartnerType.VENDOR);
                  }}
                  error={getFormikError(formik, "vendorId")}
                  readOnly
                />
                <Input
                  label="Manufacturer"
                  placeholder="Select Manufacturer"
                  value={_manufacturer?.name ?? ""}
                  onClick={() => {
                    setPartnerModal(PartnerType.MANUFACTURER);
                  }}
                  error={getFormikError(formik, "manufacturerId")}
                  readOnly
                />
                <Textarea
                  label="Description"
                  placeholder="Input description"
                  {...formikInputOpts(formik, "description")}
                />
                {isSuperAdmin && (
                  <Input label="Organization" value={info?.organization?.name ?? ""} disabled />
                )}
              </form>
            </div>
          </div>
        </div>
      );
    }
  )
);
export default ProductForm;
