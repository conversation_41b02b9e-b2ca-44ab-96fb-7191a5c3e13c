import { useProductMatchingRules } from "@/modules/Compliance/queries";
import { CompliancePages } from "@/modules/Compliance/Routes.compliance";
import {
  IssueComplianceTable,
  MatchingRuleSourceDatabaseTable,
  SourceTable
} from "@/modules/Compliance/Types.compliance";
import { getSchemaForSourceTable, renderSubjectCode } from "@/modules/Compliance/Utils.compliance";
import { CanadaAntiDumping, CanadaOgd, SimaFiling } from "nest-modules";
import { useCallback, useEffect, useMemo, useState } from "react";
import toast from "react-hot-toast";
import { useLocation, useNavigate } from "react-router-dom";
import { Accordion, Icons, Input, Table } from "ui";
import { pascalToCapitalize } from "utils";
import { Product } from "../../Types.product";
import { Organization } from "@/modules/Auth/Types.auth";

function ProductComplianceInformation({
  product,
  isSima,
  organization
}: {
  product: Product;
  isSima?: boolean;
  organization?: Organization;
}) {
  const navigate = useNavigate();
  const location = useLocation();
  const [isOpen, setIsOpen] = useState(false);
  const [id, setId] = useState<number>();

  const { data, error, isFetching } = useProductMatchingRules(
    id,
    isSima ? [MatchingRuleSourceDatabaseTable.SIMA_FILING] : undefined
  );

  const simaFiling = useMemo(() => {
    return isSima ? (data?.[0]?.sourceRecords?.[0] as SimaFiling) : undefined;
  }, [data, isSima]);

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  useEffect(() => {
    if (isOpen && !id) {
      setId(product.id);
    }
  }, [isOpen, id, product.id]);

  const handleClick = useCallback(
    (
      sourceRecord: IssueComplianceTable<CanadaOgd | CanadaAntiDumping>,
      sourceTable: MatchingRuleSourceDatabaseTable
    ) => {
      if (sourceRecord.hasIssue) {
        navigate(CompliancePages.Detail("create"), {
          state: {
            sourceTable:
              sourceTable === MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING
                ? MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE
                : sourceTable,
            sourceRecord:
              sourceTable === MatchingRuleSourceDatabaseTable.CANADA_OGD ? sourceRecord : undefined,
            measureInForce:
              sourceTable === MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING ? sourceRecord : undefined,
            product,
            from: location.pathname,
            organization
          }
        });
      } else {
        navigate(CompliancePages.List, {
          state: {
            product
          }
        });
      }
    },
    [location.pathname, navigate, organization, product]
  );

  return (
    <Accordion title={isSima ? "SIMA Details" : "Compliance Information"} onToggle={setIsOpen}>
      {isFetching ? (
        <Icons.Loader />
      ) : isSima ? (
        simaFiling ? (
          <div className="flex flex-wrap gap-4 p-0.5">
            <Input readOnly label="Sima Subject" value={renderSubjectCode(simaFiling.subjectCode)} />
            <Input
              readOnly
              label="Sima Code"
              value={typeof simaFiling.simaCode === "number" ? simaFiling.simaCode : ""}
            />
            <Input readOnly label="Incoterms" value={simaFiling.incoterms ?? ""} />
            <Input
              containerStyle="flex-1"
              readOnly
              label="Measure In Force"
              value={`${simaFiling.measureInForce?.code ?? ""} - ${simaFiling.measureInForce?.case}`}
            />
            <Input readOnly label="Security" value={simaFiling.security ? "Yes" : "No"} />
          </div>
        ) : (
          <span>No SIMA filing found</span>
        )
      ) : (
        <div className="flex flex-col gap-3">
          {data && data.length > 0 ? (
            data.map((source) => (
              <Table
                key={source.sourceTable}
                header={pascalToCapitalize(source.sourceTable)}
                data={source.sourceRecords as SourceTable[]}
                schema={getSchemaForSourceTable(source.sourceTable, true)}
                onClick={(sourceRecord: IssueComplianceTable<CanadaOgd | CanadaAntiDumping>) =>
                  handleClick(sourceRecord, source.sourceTable)
                }
              />
            ))
          ) : (
            <span>No product compliance found</span>
          )}
        </div>
      )}
    </Accordion>
  );
}

export default ProductComplianceInformation;
