import { ORDER_BY } from "@/common/Constant.common";
import { SelectOrganizationModal } from "@/modules/Auth/components";
import { Organization } from "@/modules/Auth/Types.auth";
import { SelectCountry } from "@/modules/Location/components";
import { SelectPartnerModal } from "@/modules/TradePartner/components";
import { PartnerType, TradePartner } from "@/modules/TradePartner/Types.partner";
import { useDebounce } from "@uidotdev/usehooks";
import { useEffect, useReducer, useState } from "react";
import { twMerge } from "tailwind-merge";
import { Card, Input, Select } from "ui";
import { OrderBy } from "utils";
import { PRODUCT_SORT_BY, PRODUCT_TYPE } from "../../Constant.product";
import { productTableSchema } from "../../Schema.product";
import { GetProductList, ProductType } from "../../Types.product";

type Props = {
  isOpen: boolean;
  partNumber?: string;
  isSuperAdmin?: boolean;
  filterValues(values?: GetProductList): void;
};
const ProductFilter = ({ isOpen, partNumber, isSuperAdmin, filterValues }: Props) => {
  const [vendorModal, toggleVendorModal] = useReducer((r) => !r, false);
  const [organizationModal, toggleOrganizationModal] = useReducer((r) => !r, false);
  const [vendor, setVendor] = useState<TradePartner>();
  const [organization, setOrganization] = useState<Organization>();
  const [values, setValues] = useState<GetProductList>();

  const [formValues, setFormValues] = useState<Partial<GetProductList>>({ partNumber });
  const debouncedValues = useDebounce(formValues, 500);

  useEffect(() => {
    setValues((prev) => ({
      ...prev,
      ...debouncedValues
    }));
  }, [debouncedValues]);

  useEffect(() => {
    filterValues(values);
  }, [filterValues, values]);

  useEffect(() => () => setValues(undefined), []);

  return (
    <div
      className={twMerge(
        "overflow-hidden transition-max-height duration-300 ease-in-out",
        isOpen ? "max-h-96" : "max-h-0"
      )}
    >
      {vendorModal && (
        <SelectPartnerModal
          show={vendorModal}
          partnerType={PartnerType.VENDOR}
          onClose={toggleVendorModal}
          onSelect={(vendor) => {
            setValues((prev) => ({
              ...prev,
              vendorId: vendor?.id
            }));
            setVendor(vendor);
            toggleVendorModal();
          }}
        />
      )}
      {organizationModal && (
        <SelectOrganizationModal
          show={organizationModal}
          onClose={toggleOrganizationModal}
          selected={!!organization}
          onSelect={(organization) => {
            setValues((prev) => ({
              ...prev,
              organizationId: organization?.id
            }));
            setOrganization(organization);
            toggleOrganizationModal();
          }}
        />
      )}
      {isOpen && (
        <Card containerStyle="flex flex-wrap items-center gap-x-3 gap-y-2 px-6 py-4 border">
          <div className="flex gap-3 flex-wrap gap-x-3 gap-y-2">
            <Input
              label="HS Code"
              placeholder="Search"
              onTextChange={(hsCode: string) => setFormValues((prev) => ({ ...prev, hsCode }))}
              kind="search"
            />
            <Input
              label="Part Number"
              placeholder="Search"
              onTextChange={(partNumber: string) => setFormValues((prev) => ({ ...prev, partNumber }))}
              kind="search"
              value={formValues?.partNumber ?? ""}
            />
            <Input
              label="SKU"
              placeholder="Search"
              onTextChange={(sku: string) => setFormValues((prev) => ({ ...prev, sku }))}
              kind="search"
            />
            <Input
              label="UPC"
              placeholder="Search"
              onTextChange={(upc: string) => setFormValues((prev) => ({ ...prev, upc }))}
              kind="search"
            />
            <Select
              label="Product Type"
              onSelected={(productType: ProductType) =>
                setValues((prev) => ({ ...prev, productType }) as GetProductList)
              }
              options={PRODUCT_TYPE}
              optional
            />
            <SelectCountry
              placeholder="Select Origin"
              onSelect={(country) => {
                if (typeof country?.value === "number" || country?.value === undefined) {
                  setValues((prev) => ({
                    ...prev,
                    originId: country?.value as number
                  }));
                }
              }}
            />
            <Input
              label="Vendor"
              placeholder="Select vendor"
              value={vendor?.name ?? ""}
              onClick={toggleVendorModal}
              readOnly
            />
            <Input
              label="Vendor Part Number"
              placeholder="Search"
              onTextChange={(vendorPartNumber: string) =>
                setFormValues((prev) => ({ ...prev, vendorPartNumber }))
              }
              kind="search"
            />
            <Input
              label="Description"
              placeholder="Search"
              onTextChange={(description: string) => setFormValues((prev) => ({ ...prev, description }))}
              kind="search"
            />
            {isSuperAdmin && (
              <Input
                label="Organization"
                placeholder="Select organization"
                value={organization?.name ?? ""}
                onClick={toggleOrganizationModal}
                readOnly
              />
            )}
            <Select
              label="Sort By"
              onSelected={(sortBy: keyof typeof productTableSchema) =>
                setValues((prev) => ({ ...prev, sortBy }) as GetProductList)
              }
              options={PRODUCT_SORT_BY}
              defaultValue={"createDate"}
              optional
            />
            <Select
              label="Sort Direction"
              onSelected={(sortOrder: OrderBy) =>
                setValues((prev) => ({ ...prev, sortOrder }) as GetProductList)
              }
              options={ORDER_BY}
              defaultValue={"desc"}
              optional
            />
          </div>
        </Card>
      )}
    </div>
  );
};
export default ProductFilter;
