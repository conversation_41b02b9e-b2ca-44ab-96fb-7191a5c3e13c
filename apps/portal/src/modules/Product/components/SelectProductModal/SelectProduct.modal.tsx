import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { useEffect, useReducer, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button, Modal, Table } from "ui";
import { SortOrder } from "utils";
import { useGetInfiniteProducts } from "../../queries";
import { productTableSchema } from "../../Schema.product";
import { GetProductList, Product, ProductColumn } from "../../Types.product";
import ProductFilter from "../ProductFilter";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";
import { ProductPages } from "../../Constant.product";

type Props = {
  show: boolean;
  product?: Product | null;
  required?: boolean;
  multiple?: boolean;
  onSelect(item?: Product): void;
  onClose(): void;
};
function SelectProductModal({ show, product, required, multiple, onSelect, onClose }: Props) {
  const navigate = useNavigate();
  const [selected, setSelected] = useState<Product>();
  const [filters, setFilters] = useState<GetProductList>();
  const [filter, toggleFilter] = useReducer((r) => !r, false);
  const { canCrudBackoffice } = useBackofficePermission();

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } = useGetInfiniteProducts(
    {
      limit: DEFAULT_LIMIT,
      sortBy: ProductColumn.createDate,
      sortOrder: SortOrder.DESC,
      ...filters
    },
    true
  );

  useEffect(() => {
    if (product && data && data.length > 0) {
      const foundProduct = data.find((p) => p.id === product.id);
      if (foundProduct) setSelected(foundProduct);
    }
  }, [product, data]);

  const handleSelect = () => {
    if (!multiple) onSelect(selected);
  };

  return (
    <Modal
      id="select-product-modal"
      show={show}
      onClose={onClose}
      title="Select Product"
      size="4xl"
      bodyStyle="pt-0"
      actions={
        <>
          {!required && (
            <Button
              label="Clear"
              kind="cancel"
              onClick={() => {
                setSelected(undefined);
                onSelect(undefined);
              }}
            />
          )}
          <Button label="Filters" kind="filter" className="w-fit ml-auto" onClick={toggleFilter} />
          {selected && canCrudBackoffice && (
            <Button
              label="Edit"
              kind="edit"
              onClick={() => {
                navigate(ProductPages.Detail(selected.id.toString()), {
                  state: {
                    from: location.pathname
                  }
                });
              }}
            />
          )}
          {canCrudBackoffice && (
            <Button
              label="Create"
              kind="create"
              onClick={() => {
                navigate(ProductPages.Detail("create"), {
                  state: {
                    from: location.pathname
                  }
                });
              }}
            />
          )}

          <Button label="Select" onClick={handleSelect} disabled={!data?.length || !selected} />
        </>
      }
    >
      <div>
        <div className="sticky top-0 z-10 bg-white pt-2">
          <ProductFilter filterValues={setFilters} isOpen={filter} partNumber={product?.partNumber} />
        </div>

        <div className="overflow-auto flex-1 mt-2">
          <Table
            data={data}
            schema={productTableSchema}
            checklist
            selected={selected ? [selected] : undefined}
            onClick={setSelected}
            isLoading={isLoading}
            onEndReached={fetchNextPage}
            hasNextPage={hasNextPage}
            isFetchingNextPage={isFetchingNextPage}
            onSort={(key?: string, direction?: SortOrder) => {
              setFilters((prev) => ({
                ...prev,
                sortBy: key as ProductColumn,
                sortOrder: direction
              }));
            }}
            sortKey={filters?.sortBy}
            sortDirection={filters?.sortOrder}
          />
        </div>
      </div>
    </Modal>
  );
}
export default SelectProductModal;
