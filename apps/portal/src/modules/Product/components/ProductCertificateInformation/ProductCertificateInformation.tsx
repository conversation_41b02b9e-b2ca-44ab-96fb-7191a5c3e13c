import { Organization } from "@/modules/Auth/Types.auth";
import { CertificatePages } from "@/modules/Certificate/Constant.certificate";
import { certificateTableSchema } from "@/modules/Certificate/Schema.certificate";
import { CertificateOfOrigin } from "@/modules/Certificate/Types.certificate";
import { useProductMatchingRules } from "@/modules/Compliance/queries";
import { MatchingRuleSourceDatabaseTable } from "@/modules/Compliance/Types.compliance";
import { useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useLocation, useNavigate } from "react-router-dom";
import { Accordion, Icons, Table } from "ui";
import { pascalToCapitalize } from "utils";
import { Product } from "../../Types.product";

function ProductCertificateInformation({
  product,
  organization
}: {
  product: Product;
  organization?: Organization;
}) {
  const navigate = useNavigate();
  const location = useLocation();
  const [isOpen, setIsOpen] = useState(false);
  const [id, setId] = useState<number>();

  const { data, error, isFetching } = useProductMatchingRules(id, [
    MatchingRuleSourceDatabaseTable.CERTIFICATE_OF_ORIGIN
  ]);

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  useEffect(() => {
    if (isOpen && !id) {
      setId(product.id);
    }
  }, [isOpen, id, product.id]);

  const handleClick = useCallback(
    (id: number) => {
      navigate(CertificatePages.Detail(id.toString()), {
        state: {
          from: location.pathname,
          organization,
          product
        }
      });
    },
    [location.pathname, navigate, organization, product]
  );

  return (
    <Accordion title="Certificate Information" onToggle={setIsOpen}>
      {isFetching ? (
        <Icons.Loader />
      ) : (
        <div className="flex flex-col gap-3">
          {data && data.length > 0 ? (
            data.map((source) => (
              <Table
                key={source.sourceTable}
                header={pascalToCapitalize(source.sourceTable)}
                data={source.sourceRecords as CertificateOfOrigin[]}
                schema={certificateTableSchema}
                onClick={(row: CertificateOfOrigin) => handleClick(row.id)}
              />
            ))
          ) : (
            <span>No certificate found</span>
          )}
        </div>
      )}
    </Accordion>
  );
}

export default ProductCertificateInformation;
