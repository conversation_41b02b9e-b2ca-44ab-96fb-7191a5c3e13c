import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import ProductService from "../services";
import type { SaveProductParams } from "../Types.product";

const useSaveProduct = () =>
  useMutation({
    mutationFn: async (params: SaveProductParams) => {
      if (params.id) {
        return await ProductService.edit(params);
      }
      return await ProductService.create(params);
    }
  });

const useDeleteProduct = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await ProductService.remove(params)
  });

export { useDeleteProduct, useSaveProduct };
