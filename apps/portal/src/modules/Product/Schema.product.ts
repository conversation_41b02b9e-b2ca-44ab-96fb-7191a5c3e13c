import { CanadaAntiDumping, CanadaOgd, RelatedCommercialInvoiceLineDto } from "nest-modules";
import { lazy } from "react";
import { auditTableSchema, TableSchema } from "utils";
import { boolean, number, object, ObjectSchema, string } from "yup";
import { antiDumpingSchema, ogdFilingSchema, ogdSchema } from "../Compliance/Schema.compliance";
import { IssueComplianceTable, OgdFilingTable } from "../Compliance/Types.compliance";
import { ProductTable, SaveProductParams } from "./Types.product";
const IssueLabel = lazy(() => import("../Compliance/components/IssueLabel"));
const ShipmentLabel = lazy(() => import("../Shipment/components/ShipmentLabel"));

export const productTableSchema: TableSchema<ProductTable> = {
  partNumber: {
    header: "Part No.",
    visible: true
  },
  hsCode: {
    header: "HS Code",
    readOnly: true,
    visible: true
  },
  description: {
    header: "Description",
    style: "max-w-80 truncate",
    visible: true
  },
  sku: {
    header: "SKU",
    visible: true
  },
  upc: {
    header: "UPC"
  },
  productType: {
    header: "Product Type",
    renderer: "kebabCase",
    visible: true
  },
  originName: {
    header: "Origin",
    visible: true,
    sortKey: "originId"
  },
  vendorName: {
    header: "Vendor",
    visible: true,
    sortKey: "vendorId"
  },
  vendorCode: {
    header: "Vendor Code",
    visible: true,
    disableSort: true
  },
  vendorPartNumber: {
    header: "Vendor Part No."
  },
  manufacturerName: {
    header: "Manufacturer",
    visible: true,
    sortKey: "manufacturerId"
  },
  ...auditTableSchema
};

export const productOgdSchema: TableSchema<IssueComplianceTable<CanadaOgd>> = {
  hasIssue: {
    header: "Status",
    renderer: IssueLabel,
    visible: true
  },
  issueDetails: {
    header: "Details",
    style: "max-w-80 whitespace-normal underline font-medium",
    visible: true
  },
  ...ogdSchema
};
const { ogdName: _omit, ...rest } = ogdFilingSchema;
export const productOgdFilingSchema: TableSchema<OgdFilingTable> = {
  ...rest
};
export const productAntiDumpingSchema: TableSchema<IssueComplianceTable<CanadaAntiDumping>> = {
  hasIssue: {
    header: "Status",
    renderer: IssueLabel,
    visible: true
  },
  issueDetails: {
    header: "Details",
    style: "max-w-80 whitespace-normal underline font-medium",
    visible: true
  },
  ...antiDumpingSchema
};
export const productShipmentSchema: TableSchema<RelatedCommercialInvoiceLineDto> = {
  status: {
    header: "Status",
    renderer: ShipmentLabel,
    visible: true
  },
  modeOfTransport: {
    header: "Mode",
    renderer: "kebabCase",
    style: "uppercase",
    visible: true
  },
  hblNumber: {
    header: "HBL",
    visible: true
  },
  transactionNumber: {
    header: "Transaction No.",
    visible: true
  },
  etaDestination: {
    header: "ETA",
    renderer: "date",
    visible: true
  },
  portOfLoading: {
    header: "Port of Loading",
    visible: true
  },
  placeOfDelivery: {
    header: "Place of Delivery",
    visible: true
  },
  cargoControlNumber: {
    header: "CCN",
    visible: true
  },
  vfd: {
    header: "VFD",
    visible: true
  },
  tt: {
    header: "TT",
    visible: true
  },
  quantity: {
    header: "Qty",
    renderer: "number",
    visible: true
  },
  unitOfMeasure: {
    header: "UoM",
    style: "uppercase",
    visible: true
  },
  unitPrice: {
    header: "Unit Price",
    renderer: "number",
    visible: true
  },
  totalLineValue: {
    header: "Total Line Value",
    renderer: "number",
    visible: true
  },
  lineTotalDutiesAndTaxes: {
    header: "Total Line Duties & Taxes",
    renderer: "number",
    visible: true
  },
  currency: {
    header: "Currency",
    style: "uppercase",
    visible: true
  }
};
export const productComplianceSchema: TableSchema<ProductTable> = {
  partNumber: {
    header: "Part No.",
    visible: true
  },
  hsCode: {
    header: "HS Code",
    readOnly: true,
    visible: true
  },
  description: {
    header: "Description",
    style: "max-w-80 truncate",
    visible: true
  },
  sku: {
    header: "SKU",
    visible: true
  },
  upc: {
    header: "UPC",
    visible: true
  },
  productType: {
    header: "Product Type",
    renderer: "kebabCase",
    visible: true
  }
};

export const saveProductSchema: ObjectSchema<SaveProductParams> = object({
  id: number().optional(),
  partNumber: string().required("Part number is required"),
  sku: string().optional(),
  upc: string().optional(),
  vendorPartNumber: string().optional(),
  description: string().required("Description is required"),
  hsCode: string().required("HS code is required").length(10, "HS code must be 10 characters"),
  vendorId: number().required("Vendor is required"),
  originId: number().required("Origin is required"),
  stateRequired: boolean().optional(),
  originStateId: number().when("$stateRequired", {
    is: true,
    then: (schema) => schema.required("Origin state is required"),
    otherwise: (schema) => schema.optional()
  }),
  manufacturerId: number().optional()
});
