import { getAuditNames } from "utils";
import { Product } from "./Types.product";

export const GetProductDto = (product: Product) => ({
  ...product,
  originName: product.origin?.name || "",
  vendorName: product.vendor?.name || "",
  vendorCode: product.vendor?.vendorCode || "",
  manufacturerName: product.manufacturer?.name || "",
  organizationName: product.organization?.name || "",
  ...getAuditNames(product)
});

export const GetProductsDto = (products: Product[]) => products.map((c: Product) => GetProductDto(c));
