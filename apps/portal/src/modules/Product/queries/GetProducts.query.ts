import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { flattenQueryResults, infiniteQueryMapper, SelectOption } from "utils";
import { GetProductDto, GetProductsDto } from "../Dto.product";
import ProductService from "../services";
import { GetProductList, GetProductListResponse } from "../Types.product";

export const getProductsKey = "getProducts";

export const useGetProducts = <R = GetProductListResponse>(
  params?: GetProductList,
  select?: (data: GetProductListResponse) => R,
  refetchOnMount = false
) =>
  useQuery({
    queryKey: [getProductsKey, params],
    queryFn: () => ProductService.list(params),
    select,
    refetchOnMount
  });

export const useGetProductList = (params?: GetProductList) =>
  useGetProducts(
    params,
    (data: GetProductListResponse) => {
      const products = GetProductsDto(data.products);
      return { ...data, products };
    },
    true
  );

export const useGetInfiniteProducts = (params?: GetProductList, refetchOnMount = false) =>
  useInfiniteQuery({
    queryKey: [getProductsKey, params],
    queryFn: ({ pageParam }) => ProductService.list({ ...params, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: infiniteQueryMapper,
    refetchOnMount,
    select: (data) => flattenQueryResults("products", data.pages).map(GetProductDto)
  });

export const useGetProductsOptions = () =>
  useGetProducts({ limit: 50 }, (data: GetProductListResponse): SelectOption[] =>
    data.products.map((p) => ({ label: p.hsCode, value: p.id }))
  );
