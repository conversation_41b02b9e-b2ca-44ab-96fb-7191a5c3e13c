import { useQuery } from "@tanstack/react-query";
import ProductService from "../services";
import { GetProductShipmentList, GetProductShipmentListResponse } from "../Types.product";

export const getProductShipmentsKey = "getProductShipments";

export const useGetProductShipments = <R = GetProductShipmentListResponse>(
  params: GetProductShipmentList,
  select?: (data: GetProductShipmentListResponse) => R,
  refetchOnMount = false
) =>
  useQuery({
    queryKey: [getProductShipmentsKey, params],
    queryFn: () => ProductService.shipments(params),
    select,
    refetchOnMount,
    enabled: !!params.id
  });

export const useGetProductShipmentList = (params: GetProductShipmentList) =>
  useGetProductShipments(params, undefined, true);
