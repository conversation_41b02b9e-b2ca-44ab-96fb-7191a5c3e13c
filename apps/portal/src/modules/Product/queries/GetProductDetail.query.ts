import { useQuery } from "@tanstack/react-query";
import { DetailParams } from "utils";
import { Product } from "../Types.product";
import ProductService from "../services";

export const getProductDetailKey = "GetProductDetail";

export const useGetProductDetail = <R = Product>({ id }: DetailParams, select?: (data: Product) => R) =>
  useQuery({
    queryKey: [getProductDetailKey, id],
    queryFn: async () => await ProductService.get({ id }),
    enabled: !!id,
    select
  });
