import Http from "@/bootstrap/Http.bootstrap";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import type {
  GetProductList,
  GetProductListResponse,
  GetProductShipmentList,
  GetProductShipmentListResponse,
  Product,
  SaveProductParams
} from "../Types.product";

const PRODUCT_ROUTE = "products";
/**
 * Get all products
 *
 * @returns
 */
async function list(params?: GetProductList): Promise<GetProductListResponse> {
  try {
    return await Http.get(stringifyQueryParams(PRODUCT_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get product detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<Product> {
  try {
    return await Http.get(`${PRODUCT_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create product
 *
 * @returns
 */
async function create(params: SaveProductParams): Promise<Product> {
  try {
    return await Http.post(PRODUCT_ROUTE, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit product
 *
 * @returns
 */
async function edit({ id, ...params }: SaveProductParams): Promise<Product> {
  try {
    return await Http.put(`${PRODUCT_ROUTE}/${id}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Delete product
 *
 * @returns
 */
async function remove({ id }: DetailParams): Promise<void> {
  try {
    return await Http.delete(`${PRODUCT_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get product shipments
 *
 * @returns
 */
async function shipments({ id }: GetProductShipmentList): Promise<GetProductShipmentListResponse> {
  try {
    return await Http.get(`${PRODUCT_ROUTE}/${id}/commercial-invoice-lines`);
  } catch (error) {
    throw await handleError(error);
  }
}

export { create, edit, get, list, remove, shipments };
