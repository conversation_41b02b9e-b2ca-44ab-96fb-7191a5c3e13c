import { RouterObject } from "@/common/Types.common";
import { Package } from "lucide-react";
import { Link, Params } from "react-router-dom";
import { ProductDetail, ProductList } from "./views";
import { ProductPages } from "./Constant.product";

const ProductNavigation = {
  label: "Product",
  path: ProductPages.List,
  icon: <Package className="size-6" />,
  pattern: [{ path: `${ProductPages.List}/*` }]
};

const ProductRoutes: RouterObject = [
  {
    path: ProductPages.List,
    handle: {
      crumb: () => <Link to={ProductPages.List}>Product List</Link>
    },
    children: [
      { path: "", element: <ProductList /> },
      {
        path: ProductPages.Detail(":product_id"),
        element: <ProductDetail />,
        handle: {
          crumb: (params: Params) => (params.product_id === "create" ? "Create Product" : "Product Details")
        }
      }
    ]
  }
];

export { ProductNavigation, ProductRoutes };
