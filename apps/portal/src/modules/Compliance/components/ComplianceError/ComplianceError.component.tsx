import { NonCompliantReason } from "@/modules/CommercialInvoice/Types.commercial-invoice";
import { CircleAlert } from "lucide-react";
import {
  CanadaOgd,
  NonCompliantRecordDto,
  ValidateCommercialInvoiceComplianceResponseDto,
  ValidateCommercialInvoiceLineComplianceResponseDto,
  ValidateShipmentComplianceResponseDto
} from "nest-modules";
import { useCallback, useMemo } from "react";
import { capitalizeFirstLetter, kebabCaseToCapitalize, pascalToCapitalize } from "utils";
import { MatchingRuleSourceDatabaseTable } from "../../Types.compliance";

type Props = {
  title?: string;
  compliance?:
    | ValidateShipmentComplianceResponseDto
    | ValidateCommercialInvoiceComplianceResponseDto
    | ValidateCommercialInvoiceLineComplianceResponseDto;
  onIssueClick?: (record: NonCompliantRecordDto) => void;
};

// TODO put this in a shared utils
// Type guard for checking if sourceRecord is OGD
function isCanadaOgd(obj: NonCompliantRecordDto["sourceRecord"]): obj is CanadaOgd {
  return obj !== null && typeof obj === "object" && "agency" in obj && typeof obj.agency === "string";
}

function ComplianceError({ title, compliance, onIssueClick }: Props) {
  const renderMissingFields = (fields: Array<string>, title: string) => {
    if (!fields || fields.length === 0) return null;

    return (
      <div>
        <span className="font-medium">Missing {title} fields:</span>
        <ul className="list-disc list-inside ml-2">
          {fields.map((field, index) => {
            const cleanField = field.endsWith("Id") ? field.slice(0, -2) : field;
            return (
              <li key={index}>
                <span>{capitalizeFirstLetter(pascalToCapitalize(cleanField))}</span>
              </li>
            );
          })}
        </ul>
      </div>
    );
  };

  const renderNonCompliantRecords = useCallback(
    (records: Array<NonCompliantRecordDto>) => {
      if (!records || records.length === 0) return null;

      return (
        <div>
          <span className="font-medium">Compliance issues:</span>
          <ul className="list-disc list-inside ml-2">
            {records.map((record, index) => (
              <li key={index}>
                {record.reason !== NonCompliantReason.MISSING_OR_INVALID_MEASUREMENTS ? (
                  <span
                    className={onIssueClick ? "hover:underline cursor-pointer" : ""}
                    onClick={() => onIssueClick && onIssueClick(record)}
                  >
                    {record.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_OGD &&
                    isCanadaOgd(record.sourceRecord)
                      ? `Missing ${record.sourceRecord.agency.toUpperCase()} ${
                          record.sourceRecord.program ?? ""
                        } Filing`
                      : kebabCaseToCapitalize(record.reason)}
                  </span>
                ) : (
                  <span>
                    {isCanadaOgd(record.sourceRecord)
                      ? `Missing or invalid measurements for ${record.sourceRecord.agency.toUpperCase()} ${
                          record.sourceRecord.program ?? ""
                        }. Please add/edit Per Unit additional measurement.`
                      : `Missing or invalid measurements. Please add/edit Per Unit additional measurement.`}
                  </span>
                )}
              </li>
            ))}
          </ul>
        </div>
      );
    },
    [onIssueClick]
  );

  const renderComplianceContent = useMemo(() => {
    if (!compliance) return null;

    // Shipment compliance
    if ("shipmentId" in compliance) {
      if (compliance.noCommercialInvoice || compliance.missingFields.length > 0) {
        return (
          <>
            {compliance.noCommercialInvoice && (
              <span className="font-medium">Missing commercial invoice</span>
            )}
            {renderMissingFields(compliance.missingFields, "required")}
          </>
        );
      }
    }

    // Commercial invoice compliance
    if ("commercialInvoiceId" in compliance) {
      if (
        compliance.missingFields.length > 0 ||
        compliance.shipToMissingFields.length > 0 ||
        compliance.vendorMissingFields.length > 0
      ) {
        return (
          <>
            {renderMissingFields(compliance.missingFields, "required")}
            {renderMissingFields(compliance.shipToMissingFields, "Ship To")}
            {renderMissingFields(compliance.vendorMissingFields, "Vendor")}
          </>
        );
      }
    }

    // Commercial invoice line compliance
    if ("lineId" in compliance) {
      if (
        compliance.isHsCodeInvalid ||
        compliance.isQuantityInvalid ||
        compliance.isUsStateOfOriginNotSet ||
        compliance.nonCompliantRecords.length > 0
      ) {
        return (
          <>
            {compliance.isHsCodeInvalid && <span className="font-medium">Invalid HS code</span>}
            {compliance.isQuantityInvalid && <span className="font-medium">Invalid quantity</span>}
            {compliance.isUsStateOfOriginNotSet && <span className="font-medium">Missing origin state</span>}
            {renderNonCompliantRecords(compliance.nonCompliantRecords)}
          </>
        );
      }
    }
  }, [compliance, renderNonCompliantRecords]);

  return (
    renderComplianceContent && (
      <div className="flex gap-2 p-2 bg-red-50 rounded-md border border-red-300">
        <div className="shrink-0">
          <CircleAlert className="size-6 text-red-600" />
        </div>
        <div className="flex flex-col gap-2">
          <h5>There are issues with this {title || "item"}</h5>
          {renderComplianceContent}
        </div>
      </div>
    )
  );
}

export default ComplianceError;
