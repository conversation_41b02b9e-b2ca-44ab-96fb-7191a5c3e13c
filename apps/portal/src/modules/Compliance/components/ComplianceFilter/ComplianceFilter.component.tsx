import { BOOLEAN_OPTIONS, ORDER_BY } from "@/common/Constant.common";
import { SelectProductModal } from "@/modules/Product/components";
import { Product } from "@/modules/Product/Types.product";
import { useDebounce } from "@uidotdev/usehooks";
import { useEffect, useReducer, useState } from "react";
import { twMerge } from "tailwind-merge";
import { Card, Input, Select, Tooltip } from "ui";
import { OrderBy } from "utils";
import { MATCHING_RULE_SORT_BY, MATCHING_RULE_STATUS, SOURCE_TABLE } from "../../Constant.compliance";
import { complianceRuleTableSchema } from "../../Schema.compliance";
import {
  MatchingRuleDestinationDatabaseTable,
  type GetMatchingRuleList,
  type MatchingRuleSourceDatabaseTable,
  type MatchingRuleStatus
} from "../../Types.compliance";

type Props = {
  isOpen: boolean;
  product?: Product;
  filterValues(values?: GetMatchingRuleList): void;
};
const ComplianceFilter = ({ isOpen, product: _product, filterValues }: Props) => {
  const [productModal, toggleProductModal] = useReducer((r) => !r, false);
  const [product, setProduct] = useState<Product>();
  const [values, setValues] = useState<GetMatchingRuleList>();
  // const filters = useSaveFilter<GetMatchingRuleList>(values);

  const [formValues, setFormValues] = useState<Partial<GetMatchingRuleList>>();
  const debouncedValues = useDebounce(formValues, 500);

  useEffect(() => {
    setValues((prev) => ({
      ...prev,
      ...debouncedValues
    }));
  }, [debouncedValues]);

  useEffect(() => {
    if (_product) {
      setProduct(_product);
      setValues((prev) => ({
        ...prev,
        destinationId: _product?.id,
        destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT
      }));
    }
  }, [_product]);

  useEffect(() => {
    // Set filters from search params
    filterValues(values);
    // setValues(filters);
    // Set values with debounce
    // setName(filters?.name);
    // setPriority(filters?.priority);
  }, [filterValues, values]);

  useEffect(() => () => setValues(undefined), []);

  return (
    <div
      className={twMerge(
        "overflow-hidden transition-max-height duration-300 ease-in-out",
        isOpen ? "max-h-96" : "max-h-0"
      )}
    >
      {productModal && (
        <SelectProductModal
          show={productModal}
          product={product}
          onClose={toggleProductModal}
          onSelect={(product) => {
            setValues((prev) => ({
              ...prev,
              destinationId: product?.id,
              destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT
            }));
            setProduct(product);
            toggleProductModal();
          }}
        />
      )}
      {isOpen && (
        <Card containerStyle="flex flex-wrap items-center gap-x-3 gap-y-2 px-6 py-4 border">
          <div className="flex gap-3">
            <Input
              label="Name"
              placeholder="Search"
              onTextChange={(name: string) => setFormValues((prev) => ({ ...prev, name }))}
              kind="search"
            />
            <Select
              label="Status"
              onSelected={(status: MatchingRuleStatus) => setValues((prev) => ({ ...prev, status }))}
              options={MATCHING_RULE_STATUS}
              optional
            />
            <Input
              label="Priority"
              placeholder="Input priority"
              onNumberChange={(e) => {
                const value = e.currentTarget.value;
                setFormValues((prev) => ({
                  ...prev,
                  priority: value === "" ? undefined : Number(value)
                }));
              }}
            />
            <Select
              label="Source"
              onSelected={(sourceTable: MatchingRuleSourceDatabaseTable) =>
                setValues((prev) => ({ ...prev, sourceTable }))
              }
              options={SOURCE_TABLE}
              optional
            />
            <Select
              label="Global Rule"
              onSelected={(isGlobal: boolean) => setValues((prev) => ({ ...prev, isGlobal }))}
              options={BOOLEAN_OPTIONS}
              defaultValue={"false"}
              optional
            />
            <Tooltip
              text={
                product
                  ? `${product?.partNumber ?? ""} - ${product?.hsCode ?? ""} - ${product?.description ?? ""}`
                  : ""
              }
            >
              <Input
                label="Product"
                placeholder="Select product"
                inputStyle="cursor-pointer"
                value={
                  product
                    ? `${product?.partNumber ?? ""} - ${product?.hsCode ?? ""} - ${
                        product?.description ?? ""
                      }`
                    : ""
                }
                onClick={toggleProductModal}
                readOnly
              />
            </Tooltip>
          </div>

          <div className="flex gap-3">
            <Input
              label="Expiry Date From"
              type="date"
              onTextChange={(expiryDateFrom) => setValues((prev) => ({ ...prev, expiryDateFrom }))}
            />
            <Input
              label="Expiry Date To"
              type="date"
              onTextChange={(expiryDateTo) => setValues((prev) => ({ ...prev, expiryDateTo }))}
            />
            <Select
              label="Sort By"
              onSelected={(sortBy: keyof typeof complianceRuleTableSchema) =>
                setValues((prev) => ({ ...prev, sortBy }) as GetMatchingRuleList)
              }
              options={MATCHING_RULE_SORT_BY}
              defaultValue={"createDate"}
              optional
            />
            <Select
              label="Sort Direction"
              onSelected={(sortOrder: OrderBy) =>
                setValues((prev) => ({ ...prev, sortOrder }) as GetMatchingRuleList)
              }
              options={ORDER_BY}
              defaultValue={"desc"}
              optional
            />
          </div>
        </Card>
      )}
    </div>
  );
};
export default ComplianceFilter;
