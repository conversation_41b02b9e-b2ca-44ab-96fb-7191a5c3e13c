import { useDebounce } from "@uidotdev/usehooks";
import { useEffect, useState } from "react";
import { Input, Select } from "ui";
import { pascalToCapitalize } from "utils";
import { AGENCY } from "../../Constant.compliance";
import {
  CanadaGovernmentAgency,
  GetSourceTableParams,
  MatchingRuleSourceDatabaseTable,
  SourceTable
} from "../../Types.compliance";

type Props = {
  source?: MatchingRuleSourceDatabaseTable;
  record?: SourceTable | null;
  filterValues(values?: GetSourceTableParams): void;
};
const SourceRecordFilter = ({ source, filterValues, record }: Props) => {
  const [values, setValues] = useState<GetSourceTableParams>();

  const [formValues, setFormValues] = useState<Partial<GetSourceTableParams>>({
    hsCode: record?.hsCode,
    code: record?.code
  });
  const debouncedValues = useDebounce(formValues, 500);

  useEffect(() => {
    setValues((prev) => ({
      ...prev,
      ...debouncedValues
    }));
  }, [debouncedValues]);

  useEffect(() => {
    filterValues(values);
  }, [filterValues, values]);

  useEffect(() => () => setValues(undefined), []);

  const renderFilter = () => {
    switch (source) {
      case MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING:
        return (
          <>
            <Input
              placeholder="Case"
              kind="search"
              containerStyle="max-w-32"
              onTextChange={(caseName: string) => setFormValues((prev) => ({ ...prev, case: caseName }))}
            />
            <Input
              placeholder="Dumping Country"
              kind="search"
              containerStyle="max-w-32"
              onTextChange={(dumpingCountry: string) =>
                setFormValues((prev) => ({ ...prev, dumpingCountry }))
              }
            />
            <Input
              placeholder="Subsidy Country"
              kind="search"
              containerStyle="max-w-32"
              onTextChange={(subsidyCountry: string) =>
                setFormValues((prev) => ({ ...prev, subsidyCountry }))
              }
            />
            <Input
              placeholder="Case Type"
              kind="search"
              containerStyle="max-w-32"
              onTextChange={(caseType: string) => setFormValues((prev) => ({ ...prev, caseType }))}
            />
            <Input
              placeholder="HS Codes"
              kind="search"
              containerStyle="max-w-32"
              onTextChange={(hsCodes: string) => setFormValues((prev) => ({ ...prev, hsCodes }))}
            />
            <Input
              placeholder="Product Definition"
              kind="search"
              containerStyle="max-w-32"
              onTextChange={(productDefinition: string) =>
                setFormValues((prev) => ({ ...prev, productDefinition }))
              }
            />
            <Input
              placeholder="Exclusion"
              kind="search"
              containerStyle="max-w-32"
              onTextChange={(exclusion: string) => setFormValues((prev) => ({ ...prev, exclusion }))}
            />
          </>
        );
      case MatchingRuleSourceDatabaseTable.CANADA_OGD:
        return (
          <>
            <Select
              options={AGENCY}
              onSelected={(agency: CanadaGovernmentAgency) => setValues((prev) => ({ ...prev, agency }))}
              optional
            />
          </>
        );
      case MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE:
      case MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE:
      case MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE:
        return (
          <>
            <Input
              placeholder="Code"
              kind="search"
              onTextChange={(code: string) => setFormValues((prev) => ({ ...prev, code }))}
              value={formValues?.code ?? ""}
            />
            <Input
              placeholder="Explanation"
              kind="search"
              onTextChange={(explanation: string) => setFormValues((prev) => ({ ...prev, explanation }))}
            />
          </>
        );
      case MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE:
        return (
          <>
            <Input
              placeholder="Code"
              kind="search"
              onTextChange={(code: string) => setFormValues((prev) => ({ ...prev, code }))}
            />
            <Input
              placeholder="Category"
              kind="search"
              onTextChange={(category: string) => setFormValues((prev) => ({ ...prev, category }))}
            />
            <Input
              placeholder="Rate Type"
              kind="search"
              onTextChange={(rateType: string) => setFormValues((prev) => ({ ...prev, rateType }))}
            />
            <Input
              placeholder="Explanation"
              kind="search"
              onTextChange={(explanation: string) => setFormValues((prev) => ({ ...prev, explanation }))}
            />
          </>
        );
      case MatchingRuleSourceDatabaseTable.CANADA_TARIFF:
        return (
          <>
            <Input
              placeholder="HS Code"
              kind="search"
              onTextChange={(hsCode: string) => setFormValues((prev) => ({ ...prev, hsCode }))}
              value={formValues?.hsCode ?? ""}
            />
            <Input
              placeholder="Description"
              kind="search"
              onTextChange={(description: string) => setFormValues((prev) => ({ ...prev, description }))}
            />
          </>
        );
      case MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE:
        return (
          <>
            <Input
              placeholder="Code"
              kind="search"
              onTextChange={(code: string) => setFormValues((prev) => ({ ...prev, code }))}
              value={formValues?.code ?? ""}
            />
            <Input
              placeholder="Name"
              kind="search"
              onTextChange={(name: string) => setFormValues((prev) => ({ ...prev, name }))}
            />
          </>
        );
      case MatchingRuleSourceDatabaseTable.OGD_FILING:
        return (
          <>
            <Input
              placeholder="Extension Code"
              kind="search"
              containerStyle="max-w-32"
              onTextChange={(extensionCode: string) => setFormValues((prev) => ({ ...prev, extensionCode }))}
            />
            <Input
              placeholder="End Use"
              kind="search"
              containerStyle="max-w-32"
              onTextChange={(endUse: string) => setFormValues((prev) => ({ ...prev, endUse }))}
            />
            <Input
              placeholder="AIRS Type"
              kind="search"
              containerStyle="max-w-32"
              onTextChange={(airsType: string) => setFormValues((prev) => ({ ...prev, airsType }))}
            />
            <Input
              placeholder="AIRS Ref No."
              kind="search"
              containerStyle="max-w-32"
              onTextChange={(airsReferenceNumber: string) =>
                setFormValues((prev) => ({ ...prev, airsReferenceNumber }))
              }
            />
          </>
        );
      default:
        return <></>;
    }
  };

  return (
    <div className="flex-1 flex items-center justify-between gap-3">
      <span className="">{pascalToCapitalize(source)}</span>
      <div className="flex flex-wrap items-center gap-x-3 gap-y-2">{renderFilter()}</div>
    </div>
  );
};
export default SourceRecordFilter;
