import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { useEffect, useMemo, useState } from "react";
import { Button, Modal, Table } from "ui";
import { camelToKebabCase, SortOrder } from "utils";
import { useGetInfiniteSourceTables } from "../../queries";
import { GetSourceTableParams, MatchingRuleSourceDatabaseTable, SourceTable } from "../../Types.compliance";
import { getSchemaForSourceTable } from "../../Utils.compliance";
import SourceRecordFilter from "../SourceRecordFilter";

type Props = {
  show: boolean;
  required?: boolean;
  multiple?: boolean;
  source: MatchingRuleSourceDatabaseTable;
  record?: SourceTable | null;
  hsCodeLength?: "4" | "10";
  onSelect(item?: SourceTable): void;
  onClose(): void;
  canSelect?(item: SourceTable): boolean;
};
function SelectSourceModal({
  show,
  required,
  multiple,
  source,
  record,
  hsCodeLength,
  onSelect,
  onClose,
  canSelect
}: Props) {
  const [selected, setSelected] = useState<SourceTable>();
  const [filters, setFilters] = useState<GetSourceTableParams>();

  const currentDate = useMemo(() => new Date().toISOString(), []);

  const { endpoint, schema } = useMemo(
    () => ({
      endpoint: `${camelToKebabCase(source)}s`,
      schema: getSchemaForSourceTable(source)
    }),
    [source]
  );

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useGetInfiniteSourceTables<SourceTable>(
      {
        url: endpoint,
        limit: DEFAULT_LIMIT,
        ...(source === MatchingRuleSourceDatabaseTable.CANADA_OGD && {
          sortBy: "agency"
        }),
        ...(source === MatchingRuleSourceDatabaseTable.CANADA_TARIFF && {
          effectiveDateTo: currentDate,
          expiryDateFrom: currentDate
        }),
        type: hsCodeLength,
        ...filters
      },
      {
        refetchOnMount: source === MatchingRuleSourceDatabaseTable.OGD_FILING
      }
    );

  useEffect(() => {
    if (record && data && data.length > 0) {
      const foundRecord = data.find((p) => p.id === record.id);
      if (foundRecord) setSelected(foundRecord);
    }
  }, [record, data]);

  const handleSelect = () => {
    if (!multiple) onSelect(selected);
  };

  return (
    <Modal
      id="select-source-modal"
      show={show}
      onClose={onClose}
      title="Select Source Data"
      size="4xl"
      actions={
        <>
          {!required && (
            <Button
              label="Clear"
              kind="cancel"
              onClick={() => {
                setSelected(undefined);
                onSelect(undefined);
              }}
            />
          )}

          <Button label="Select" onClick={handleSelect} disabled={!data?.length || !selected} />
        </>
      }
    >
      <div className="flex flex-col gap-4">
        <Table
          data={data}
          schema={schema}
          header={<SourceRecordFilter source={source} filterValues={setFilters} record={record} />}
          checklist
          selected={selected ? [selected] : []}
          onClick={(row: SourceTable) => {
            if (!canSelect || canSelect(row)) {
              setSelected(row);
            }
          }}
          disableRow={(row: SourceTable) => (canSelect ? !canSelect(row) : false)}
          isLoading={isLoading}
          onEndReached={fetchNextPage}
          hasNextPage={hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
          onSort={(key?: string, direction?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key,
              sortOrder: direction
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
        />
      </div>
    </Modal>
  );
}
export default SelectSourceModal;
