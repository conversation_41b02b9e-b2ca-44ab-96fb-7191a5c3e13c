import { useQuery } from "@tanstack/react-query";
import { DetailParams } from "utils";
import { MatchingRule } from "../Types.compliance";
import ComplianceService from "../services";

export const getMatchingRuleDetailKey = "GetMatchingRuleDetail";

export const useGetMatchingRuleDetail = <R = MatchingRule>(
  { id }: DetailParams,
  select?: (data: MatchingRule) => R
) =>
  useQuery({
    queryKey: [getMatchingRuleDetailKey, id],
    queryFn: async () => await ComplianceService.MatchingRule.get({ id }),
    enabled: !!id,
    select
  });
