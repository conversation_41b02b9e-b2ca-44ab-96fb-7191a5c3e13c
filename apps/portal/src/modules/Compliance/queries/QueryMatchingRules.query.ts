import { useQuery } from "@tanstack/react-query";
import { CanadaAntiDumping, CanadaOgd, OgdFiling, SimaFiling } from "nest-modules";
import { SOURCE_TABLE_ARRAY } from "../Constant.compliance";
import ComplianceService from "../services";
import {
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleSourceDatabaseTable,
  type GetQueryResultParams,
  type GetQueryResultResponse
} from "../Types.compliance";

export const queryMatchingRulesKey = "queryMatchingRules";

export const useQueryMatchingRules = <R = GetQueryResultResponse>(
  params?: GetQueryResultParams,
  select?: (data: GetQueryResultResponse) => R,
  refetchOnMount = false
) =>
  useQuery({
    queryKey: [queryMatchingRulesKey, params],
    queryFn: () => ComplianceService.MatchingRule.query(params),
    select,
    refetchOnMount,
    enabled: !!params?.destinationId
  });

export const useProductMatchingRules = (
  destinationId?: number,
  sourceTables: MatchingRuleSourceDatabaseTable[] = SOURCE_TABLE_ARRAY
) =>
  useQueryMatchingRules(
    {
      destinationId: destinationId as number,
      destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT,
      sourceTables
    },
    (data) => {
      // Filter out the results that have no source records
      const filteredData = data.matchingResults.filter((result) => result.sourceRecords.length > 0);

      const formatData = filteredData.map((data) => {
        // Check OGD Filing
        if (data.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_OGD) {
          const sourceRecords = (data.sourceRecords as CanadaOgd[]).map((ogd) => {
            // Find if there is a filing for this ogd
            const ogdFiling = filteredData.find(
              (_data) =>
                _data.sourceTable === MatchingRuleSourceDatabaseTable.OGD_FILING &&
                (_data.sourceRecords as OgdFiling[]).some((record) => record.ogd?.agency === ogd.agency)
            );
            return {
              ...ogd,
              hasIssue: !ogdFiling,
              issueDetails: !ogdFiling
                ? `${ogd.agency?.toUpperCase()} filing is required. Click here to create a filing.`
                : ""
            };
          });
          return { ...data, sourceRecords };
        }

        // Check SIMA Code
        if (data.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING) {
          const sourceRecords = (data.sourceRecords as CanadaAntiDumping[]).map((antiDumping) => {
            // Find if sima code is exist
            const simaFiling = filteredData.find(
              (_data) => _data.sourceTable === MatchingRuleSourceDatabaseTable.SIMA_FILING
            );
            return {
              ...antiDumping,
              hasIssue: !simaFiling,
              issueDetails: !simaFiling ? "SIMA filing is required. Click here to create a filing." : ""
            };
          });
          return { ...data, sourceRecords };
        }
        // Format SIMA filing table
        if (data.sourceTable === MatchingRuleSourceDatabaseTable.SIMA_FILING) {
          const sourceRecords = (data.sourceRecords as SimaFiling[]).map((simaFiling) => {
            return {
              ...simaFiling,
              simaCode: simaFiling.simaCode?.code,
              case: simaFiling.measureInForce?.case,
              caseType: simaFiling.measureInForce?.caseType
            };
          });
          return { ...data, sourceRecords };
        }
        // Format OGD filing table
        if (data.sourceTable === MatchingRuleSourceDatabaseTable.OGD_FILING) {
          const sourceRecords = (data.sourceRecords as OgdFiling[]).map((ogdFiling) => {
            return {
              ...ogdFiling,
              agencyName: ogdFiling?.ogd?.agency?.toUpperCase(),
              manufacturerName: ogdFiling?.manufacturer?.name || ""
            };
          });
          return { ...data, sourceRecords };
        }
        return data;
      });

      return formatData;
    }
  );
