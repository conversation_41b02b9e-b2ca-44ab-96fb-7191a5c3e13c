import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { CanadaTariff } from "nest-modules";
import { flattenQueryResults, infiniteQueryMapper } from "utils";
import { GetSourceTableDto } from "../Dto.compliance";
import ComplianceService from "../services";
import type { GetSourceTableParams, GetSourceTableResponse, SourceTable } from "../Types.compliance";

export const getSourceTablesKey = "getSourceTables";

export const useGetSourceTables = <T, R = GetSourceTableResponse<T>>(
  params: GetSourceTableParams,
  options?: {
    select?: (data: GetSourceTableResponse<T>) => R;
    refetchOnMount?: boolean;
    enabled?: boolean;
  }
) =>
  useQuery({
    queryKey: [getSourceTablesKey, params],
    queryFn: () => ComplianceService.SourceTable.list<T>(params),
    refetchOnMount: false,
    ...options
  });

export const useSearchTariff = (hsCode?: string) =>
  useGetSourceTables<CanadaTariff>({ url: "canada-tariffs", limit: 1, hsCode }, { enabled: !!hsCode });

export const useGetInfiniteSourceTables = <T extends SourceTable>(
  params: GetSourceTableParams,
  options?: {
    refetchOnMount?: boolean;
    enabled?: boolean;
  }
) => {
  const result = useInfiniteQuery({
    queryKey: [getSourceTablesKey, params],
    queryFn: ({ pageParam }) =>
      ComplianceService.SourceTable.list<T>({
        ...params,
        page: pageParam
      }),
    initialPageParam: 1,
    getNextPageParam: infiniteQueryMapper,
    refetchOnMount: false,
    ...options,
    select: (data) => flattenQueryResults("data", data.pages).map(GetSourceTableDto)
  });

  return result;
};
