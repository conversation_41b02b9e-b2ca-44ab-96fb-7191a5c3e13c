import {
  CommercialInvoiceLineTable,
  GetCommercialInvoiceLineList
} from "@/modules/CommercialInvoice/Types.commercial-invoice";
import { useQuery } from "@tanstack/react-query";
import {
  CanadaOgd,
  OgdFiling,
  SimaFiling,
  ValidateCommercialInvoiceLineComplianceResponseDto
} from "nest-modules";
import ComplianceService from "../services";
import {
  GetQueryMultipleResultParams,
  GetQueryMultipleResultResponse,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleSourceDatabaseTable
} from "../Types.compliance";
import { useGetCommercialInvoiceLineList } from "@/modules/CommercialInvoice/queries";

export const queryMultipleMatchingRulesKey = "queryMultipleMatchingRules";

export const useQueryMultipleMatchingRules = <R = GetQueryMultipleResultResponse>(
  params?: GetQueryMultipleResultParams,
  options?: {
    select?: (data: GetQueryMultipleResultResponse) => R;
    refetchOnMount?: boolean;
    enabled?: boolean;
  }
) =>
  useQuery({
    queryKey: [queryMultipleMatchingRulesKey, params],
    queryFn: () => ComplianceService.MatchingRule.queryMultiple(params),
    refetchOnMount: false,
    enabled: !!params?.destinationIds && params?.destinationIds.length > 0 && (options?.enabled ?? true),
    ...options
  });

export const useGetDutySimaCode = (lines?: CommercialInvoiceLineTable[]) => {
  const destinationIds = [...new Set(lines?.map((line) => line.product.id))];

  return useQueryMultipleMatchingRules(
    {
      destinationIds,
      destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT,
      sourceTables: [MatchingRuleSourceDatabaseTable.SIMA_FILING]
    },
    {
      select: (data: GetQueryMultipleResultResponse) => {
        // Filter out results without sima filing
        const filteredData = data.filter((results) =>
          results.matchingResults.some((result) => result.sourceRecords.length > 0)
        );
        if (filteredData.length === 0) return lines;

        const dutySimaCode = lines?.map((line) => {
          const productId = line.product.id;
          const hasSimaCode = filteredData.find((result) => result.destinationId === productId);

          return {
            ...line,
            simaCode: hasSimaCode
              ? (hasSimaCode.matchingResults[0].sourceRecords[0] as SimaFiling)?.simaCode?.code
              : undefined
          };
        });

        return dutySimaCode;
      }
    }
  );
};

export const useGetInvoiceLinesWithOgd = (
  params: GetCommercialInvoiceLineList,
  compliance?: ValidateCommercialInvoiceLineComplianceResponseDto[],
  enabled?: boolean
) => {
  const { data: lines, refetch: refetchLines } = useGetCommercialInvoiceLineList(params, compliance, enabled);

  const destinationIds = [...new Set(lines?.commercialInvoiceLines?.map((line) => line.product.id))];

  const {
    data,
    refetch: refetchRules,
    error,
    isFetching
  } = useQueryMultipleMatchingRules(
    {
      destinationIds,
      destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT,
      sourceTables: [MatchingRuleSourceDatabaseTable.CANADA_OGD, MatchingRuleSourceDatabaseTable.OGD_FILING]
    },
    {
      select: (rules: GetQueryMultipleResultResponse) => {
        // Filter out results without ogd records
        const filteredRules = rules.filter((rule) =>
          rule.matchingResults.some(
            (result) =>
              result.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_OGD &&
              result.sourceRecords.length > 0
          )
        );
        if (filteredRules.length === 0) return lines;

        const productOgdAgencies = filteredRules.map((rule) => {
          const canadaOgds = rule.matchingResults.find(
            (result) => result.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_OGD
          );

          const ogdFilings = rule.matchingResults.find(
            (result) => result.sourceTable === MatchingRuleSourceDatabaseTable.OGD_FILING
          );

          const agencyMap = new Map<string, boolean>();

          canadaOgds?.sourceRecords?.forEach((record) => {
            const { agency } = record as CanadaOgd;
            const hasFiling = ogdFilings?.sourceRecords?.some(
              (filing) => (filing as OgdFiling).ogd?.agency === agency
            );
            if (!agencyMap.has(agency)) {
              agencyMap.set(agency, Boolean(hasFiling));
            }
          });

          const agencies = Array.from(agencyMap.entries()).map(([agencyName, hasFiling]) => ({
            agency: agencyName.toUpperCase(),
            ogdFiled: hasFiling
          }));

          return {
            destinationId: rule.destinationId,
            agencies
          };
        });

        const formatLines = lines?.commercialInvoiceLines?.map((line) => {
          const productId = line.product.id;
          const ogdAgencies = productOgdAgencies.find((rule) => rule.destinationId === productId);
          return { ...line, ogdAgencies: ogdAgencies?.agencies };
        });

        return { ...lines, commercialInvoiceLines: formatLines };
      },
      enabled: destinationIds.length > 0,
      refetchOnMount: true
    }
  );

  const refetchAll = async () => {
    await Promise.all([refetchLines(), refetchRules()]);
  };

  return { data, refetchAll, refetchLines, refetchRules, error, isFetching };
};
