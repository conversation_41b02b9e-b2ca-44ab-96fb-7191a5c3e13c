import { useQuery } from "@tanstack/react-query";
import { getMatchingRuleDto } from "../Dto.compliance";
import ComplianceService from "../services";
import type { GetMatchingRuleList, GetMatchingRuleListResponse } from "../Types.compliance";

export const getMatchingRulesKey = "getMatchingRules";

export const useGetMatchingRules = <R = GetMatchingRuleListResponse>(
  params?: GetMatchingRuleList,
  select?: (data: GetMatchingRuleListResponse) => R,
  refetchOnMount = false
) =>
  useQuery({
    queryKey: [getMatchingRulesKey, params],
    queryFn: () => ComplianceService.MatchingRule.list(params),
    select,
    refetchOnMount
  });

export const useGetMatchingRuleList = (params?: GetMatchingRuleList, organizationId?: number) =>
  useGetMatchingRules(
    {
      ...params,
      organizationId
    },
    (data: GetMatchingRuleListResponse) => {
      const matchingRules = data.matchingRules.map(getMatchingRuleDto);

      return { ...data, matchingRules };
    },
    true
  );
