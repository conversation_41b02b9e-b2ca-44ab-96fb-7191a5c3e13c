import { CanadaOgd, OgdFiling } from "nest-modules";
import { getAuditNames } from "utils";
import { MatchingRule, SourceTable } from "./Types.compliance";
import { generateTitleForSourceTable } from "./Utils.compliance";

export const GetSourceTableDto = (sourceTable: SourceTable) => ({
  ...sourceTable,
  ...(sourceTable.ogd && {
    ogdName: sourceTable.ogd?.displayName || ""
  })
});

export const getMatchingRuleDto = (rule: MatchingRule) => ({
  ...rule,
  organizationName: rule.organization?.name ?? "",
  sourceName:
    (rule.sourceRecord as CanadaOgd)?.displayName ??
    (rule.sourceRecord as OgdFiling)?.ogd?.displayName ??
    generateTitleForSourceTable(rule.sourceTable, rule.sourceRecord as SourceTable),
  compute: rule.compute || null,
  ...getAuditNames(rule)
});
