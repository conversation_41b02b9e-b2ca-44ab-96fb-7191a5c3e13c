import { RouterObject } from "@/common/Types.common";
import { Shield } from "lucide-react";
import { Link, Params } from "react-router-dom";
import { ComplianceDetail, ComplianceList } from "./views";

const CompliancePages = {
  List: "/compliance",
  Detail: (compliance_id?: string) => `${CompliancePages.List}/${compliance_id}`
};
const ComplianceNavigation = {
  label: "Compliance Rule",
  icon: <Shield className="size-6" />,
  pattern: [{ path: `${CompliancePages.List}/*` }],
  path: CompliancePages.List
};
const ComplianceRoutes: RouterObject = [
  {
    path: CompliancePages.List,
    handle: {
      crumb: () => <Link to={CompliancePages.List}>Compliance List</Link>
    },
    children: [
      { path: "", element: <ComplianceList /> },
      {
        path: CompliancePages.Detail(":compliance_id"),
        element: <ComplianceDetail />,
        handle: {
          crumb: (params: Params) =>
            params.compliance_id === "create" ? "Create Compliance" : "Compliance Details"
        }
      }
    ]
  }
];

export { ComplianceNavigation, CompliancePages, ComplianceRoutes };
