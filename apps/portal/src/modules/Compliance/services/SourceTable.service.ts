import http from "@/bootstrap/Http.bootstrap";
import { handleError, stringifyQueryParams } from "utils";
import type { GetSourceTableParams, GetSourceTableResponse } from "../Types.compliance";

/**
 * Get source table list
 *
 * @returns
 */
async function list<T>({ url, ...params }: GetSourceTableParams): Promise<GetSourceTableResponse<T>> {
  try {
    return await http.get(stringifyQueryParams(url ?? "", params));
  } catch (error) {
    throw await handleError(error);
  }
}

export default { list };
