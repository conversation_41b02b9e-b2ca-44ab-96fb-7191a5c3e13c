import http from "@/bootstrap/Http.bootstrap";
import { SimaFiling } from "nest-modules";
import { DetailParams, handleError } from "utils";
import type {
  CreateSimaFilingWithRuleParams,
  EditSimaFilingWithRuleParams,
  EditSimaFilingWithRuleResponse,
  SaveSimaFilingParams
} from "../Types.compliance";

const SIMA_FILING_ROUTE = "sima-filings";
/**
 * Get sima filing list
 *
 * @returns
 */
// async function list(
//   params?: GetOgdFilingList
// ): Promise<GetOgdFilingListResponse> {
//   try {
//     return await http.get(stringifyQueryParams(SIMA_FILING_ROUTE, params));
//   } catch (error) {
//     throw await handleError(error);
//   }
// }
/**
 * Get sima filing detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<SimaFiling> {
  try {
    return await http.get(`${SIMA_FILING_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create sima filing
 *
 * @returns
 */
async function create(params: SaveSimaFilingParams): Promise<SimaFiling> {
  try {
    return await http.post(SIMA_FILING_ROUTE, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit sima filing
 *
 * @returns
 */
async function edit({ id, ...params }: SaveSimaFilingParams): Promise<SimaFiling> {
  try {
    return await http.put(`${SIMA_FILING_ROUTE}/${id}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Delete sima filing
 *
 * @returns
 */
async function remove({ id }: DetailParams): Promise<void> {
  try {
    return await http.delete(`${SIMA_FILING_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create sima filing with matching rule
 *
 * @returns
 */
async function createWithRule(
  params: CreateSimaFilingWithRuleParams
): Promise<EditSimaFilingWithRuleResponse> {
  try {
    return await http.post(`${SIMA_FILING_ROUTE}/filing-and-rule`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit sima filing with matching rule
 *
 * @returns
 */
async function editWithRule({
  id,
  ruleId,
  ...params
}: EditSimaFilingWithRuleParams): Promise<EditSimaFilingWithRuleResponse> {
  try {
    return await http.put(`${SIMA_FILING_ROUTE}/${id}/filing-and-rule/${ruleId}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}

export default { create, edit, get, remove, createWithRule, editWithRule };
