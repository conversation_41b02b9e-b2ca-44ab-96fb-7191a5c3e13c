import http from "@/bootstrap/Http.bootstrap";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import type {
  GetMatchingRuleList,
  GetMatchingRuleListResponse,
  GetQueryMultipleResultParams,
  GetQueryMultipleResultResponse,
  GetQueryResultParams,
  GetQueryResultResponse,
  MatchingRule,
  SaveMatchingRuleParams,
  SearchMatchingEntriesParams,
  SearchMatchingEntriesResponse,
  UpdateMatchingRuleStatusParams
} from "../Types.compliance";

const MATCHING_RULE_ROUTE = "matching-rules";
/**
 * Get matching rule list
 *
 * @returns
 */
async function list(params?: GetMatchingRuleList): Promise<GetMatchingRuleListResponse> {
  try {
    return await http.get(stringifyQueryParams(MATCHING_RULE_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get matching rule detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<MatchingRule> {
  try {
    return await http.get(`${MATCHING_RULE_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create matching rule
 *
 * @returns
 */
async function create(params: SaveMatchingRuleParams): Promise<MatchingRule> {
  try {
    return await http.post(MATCHING_RULE_ROUTE, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit matching rule
 *
 * @returns
 */
async function edit({ id, ...params }: SaveMatchingRuleParams): Promise<MatchingRule> {
  try {
    return await http.put(`${MATCHING_RULE_ROUTE}/${id}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Delete matching rule
 *
 * @returns
 */
async function remove({ id }: DetailParams): Promise<void> {
  try {
    return await http.delete(`${MATCHING_RULE_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Query matching rule results
 *
 * @returns matchingResults: MatchingResult[]
 */
async function query(params?: GetQueryResultParams): Promise<GetQueryResultResponse> {
  try {
    return await http.post(`${MATCHING_RULE_ROUTE}/query-results`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Query multiple matching rule results
 *
 * @returns matchingResults: MatchingResult[]
 */
async function queryMultiple(params?: GetQueryMultipleResultParams): Promise<GetQueryMultipleResultResponse> {
  try {
    return await http.post(`${MATCHING_RULE_ROUTE}/query-multiple-results`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Search matching rule entries
 *
 * @returns destinationRecords: Product[]
 */
async function search(params: SearchMatchingEntriesParams): Promise<SearchMatchingEntriesResponse> {
  try {
    return await http.post(`${MATCHING_RULE_ROUTE}/reverse-query-results`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Update matching rule status
 *
 * @returns
 */
async function updateStatus({ id, ...params }: UpdateMatchingRuleStatusParams): Promise<MatchingRule> {
  try {
    return await http.post(`${MATCHING_RULE_ROUTE}/${id}/status`, params);
  } catch (error) {
    throw await handleError(error);
  }
}

export default {
  create,
  edit,
  get,
  list,
  query,
  queryMultiple,
  remove,
  search,
  updateStatus
};
