import http from "@/bootstrap/Http.bootstrap";
import { OgdFiling } from "nest-modules";
import { DetailParams, handleError } from "utils";
import type {
  CreateOgdFilingWithRuleParams,
  EditOgdFilingWithRuleParams,
  EditOgdFilingWithRuleResponse,
  SaveOgdFilingParams
} from "../Types.compliance";

const OGD_FILING_ROUTE = "ogd-filings";
/**
 * Get ogd filing list
 *
 * @returns
 */
// async function list(
//   params?: GetOgdFilingList
// ): Promise<GetOgdFilingListResponse> {
//   try {
//     return await http.get(stringifyQueryParams(OGD_FILING_ROUTE, params));
//   } catch (error) {
//     throw await handleError(error);
//   }
// }
/**
 * Get ogd filing detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<OgdFiling> {
  try {
    return await http.get(`${OGD_FILING_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create ogd filing
 *
 * @returns
 */
async function create(params: SaveOgdFilingParams): Promise<OgdFiling> {
  try {
    return await http.post(OGD_FILING_ROUTE, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit ogd filing
 *
 * @returns
 */
async function edit({ id, ...params }: SaveOgdFilingParams): Promise<OgdFiling> {
  try {
    return await http.put(`${OGD_FILING_ROUTE}/${id}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Delete ogd filing
 *
 * @returns
 */
async function remove({ id }: DetailParams): Promise<void> {
  try {
    return await http.delete(`${OGD_FILING_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create ogd filing with matching rule
 *
 * @returns
 */
async function createWithRule(params: CreateOgdFilingWithRuleParams): Promise<EditOgdFilingWithRuleResponse> {
  try {
    return await http.post(`${OGD_FILING_ROUTE}/filing-and-rule`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit ogd filing with matching rule
 *
 * @returns
 */
async function editWithRule({
  id,
  ruleId,
  ...params
}: EditOgdFilingWithRuleParams): Promise<EditOgdFilingWithRuleResponse> {
  try {
    return await http.put(`${OGD_FILING_ROUTE}/${id}/filing-and-rule/${ruleId}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}

export default { create, edit, get, remove, createWithRule, editWithRule };
