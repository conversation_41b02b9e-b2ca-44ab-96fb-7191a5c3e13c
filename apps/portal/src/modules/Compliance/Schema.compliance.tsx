import {
  CanadaAntiDumping,
  CanadaExciseTaxCode,
  CanadaGstExemptCode,
  CanadaOgd,
  CanadaSimaCode,
  CanadaTariff,
  CanadaTreatmentCode,
  CanadaVfdCode,
  MatchingConditionOperator,
  MatchingRuleOperator,
  SimaIncoterms,
  TCTireClass,
  TCTireCompliance,
  TCTireSize,
  TCTireType
} from "nest-modules";
import { auditTableSchema, TableSchema } from "utils";
import { array, boolean, mixed, number, object, ObjectSchema, string } from "yup";
import { ProductTable } from "../Product/Types.product";
import { ComplianceLabel } from "./components";
import {
  CanadaGovernmentAgency,
  ECCCEmissionProgram,
  ECCCSubType,
  ECCCWildlifeCompliance,
  GeneralImportPermit,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleFormParams,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus,
  MatchingRuleTable,
  NRCANSubType,
  OgdFilingTable,
  SimaFilingTable,
  SimaSubjectCode,
  SourceTable
} from "./Types.compliance";
import { renderSubjectCode } from "./Utils.compliance";
import { TIRES_PROGRAM } from "./views/ComplianceDetail/constans";

export const complianceRuleTableSchema: TableSchema<MatchingRuleTable> = {
  name: {
    header: "Rule Name",
    style: "max-w-80 whitespace-normal break-words",
    readOnly: true,
    visible: true
  },
  status: {
    header: "Status",
    style: "w-fit",
    renderer: ({ value }: { value: MatchingRuleStatus }) => <ComplianceLabel value={value} />,
    readOnly: true,
    visible: true
  },
  description: {
    header: "Rule Description",
    style: "max-w-80 whitespace-normal break-words"
  },
  priority: {
    header: "Priority",
    visible: true
  },
  sourceTable: {
    header: "Source Table",
    renderer: "pascalCase",
    visible: true
  },
  sourceName: {
    header: "Source Record",
    style: "max-w-80 truncate",
    visible: true,
    sortKey: "sourceId"
  },
  expiryDate: {
    header: "Expiry Date",
    renderer: "dateLocal",
    visible: true
  },
  isGlobal: {
    header: "Global",
    renderer: "boolean",
    visible: true,
    disableSort: true
  },
  ...auditTableSchema
};

//#region Source table schema
export const antiDumpingSchema: TableSchema<CanadaAntiDumping> = {
  code: {
    header: "Code",
    style: "uppercase",
    visible: true
  },
  case: {
    header: "Case",
    style: "uppercase max-w-80 whitespace-normal break-words",
    visible: true
  },
  dumpingCountry: {
    header: "Dumping Country",
    style: "max-w-60 whitespace-normal break-words",
    visible: true
  },
  subsidyCountry: {
    header: "Subsidy Country",
    style: "max-w-60 whitespace-normal break-words",
    visible: true
  },
  caseType: {
    header: "Case Type",
    visible: true
  },
  hsCodes: {
    header: "HS Codes",
    style: "max-w-80 whitespace-normal break-words",
    visible: true
  },
  productDefinition: {
    header: "Product Definition",
    style: "max-w-80 truncate",
    visible: true
  },
  exclusion: {
    header: "Exclusion",
    style: "max-w-80 truncate",
    visible: true
  }
};
export const ogdSchema: TableSchema<CanadaOgd> = {
  agency: {
    header: "Agency",
    style: "uppercase",
    visible: true
  },
  program: {
    header: "Program",
    visible: true
  },
  commodityType: {
    header: "Commodity Type",
    visible: true
  }
};
export const simaCodeSchema: TableSchema<CanadaSimaCode> = {
  code: {
    header: "Code",
    style: "uppercase",
    visible: true
  },
  explanation: {
    header: "Explanation",
    style: "max-w-80 whitespace-normal",
    visible: true
  }
};
export const canadaTariffSchema: TableSchema<CanadaTariff> = {
  hsCode: {
    header: "HS Code",
    style: "uppercase",
    visible: true
  },
  description: {
    header: "Description",
    style: "max-w-80 whitespace-normal",
    visible: true
  },
  uom: {
    header: "UOM",
    style: "uppercase",
    visible: true
  },
  formattedHsCode: {
    header: "Formatted HS Code"
  }
};
export const treatmentCodeSchema: TableSchema<CanadaTreatmentCode> = {
  code: {
    header: "Code",
    style: "uppercase",
    visible: true
  },
  name: {
    header: "Name",
    visible: true
  },
  abbreviation: {
    header: "Abbreviation",
    visible: true
  }
};
export const vfdCodeSchema: TableSchema<CanadaVfdCode> = {
  code: {
    header: "Code",
    style: "uppercase",
    visible: true
  },
  explanation: {
    header: "Explanation",
    style: "max-w-80 whitespace-normal",
    visible: true
  }
};
export const gstExemptCodeSchema: TableSchema<CanadaGstExemptCode> = {
  code: {
    header: "Code",
    style: "uppercase",
    visible: true
  },
  explanation: {
    header: "Explanation",
    style: "max-w-80 whitespace-normal",
    visible: true
  }
};
export const ogdFilingSchema: TableSchema<OgdFilingTable> = {
  type: {
    header: "Type",
    visible: true,
    renderer: "capitalize"
  },
  agencyName: {
    header: "Agency",
    style: "uppercase",
    visible: true
  },
  isExcluded: {
    header: "Excluded",
    renderer: "boolean",
    visible: true
  },
  extensionCode: {
    header: "Extension Code",
    visible: true
  },
  endUse: {
    header: "End Use",
    visible: true
  },
  airsType: {
    header: "AIRS Type",
    visible: true
  },
  airsReferenceNumber: {
    header: "AIRS Reference Number",
    visible: true
  },
  productCategory: {
    header: "Product Category",
    visible: true
  },
  generalImportPermit: {
    header: "General Import Permit",
    style: "uppercase",
    visible: true
  },
  brandName: {
    header: "Brand Name",
    visible: true
  },
  manufacturerName: {
    header: "Manufacturer",
    visible: true
  },
  tireType: {
    header: "Tire Type",
    visible: true
  },
  tireSize: {
    header: "Tire Size",
    visible: true
  },
  tireCompliance: {
    header: "Compliance Statement",
    visible: true
  },
  tireClass: {
    header: "Tire Class",
    visible: true
  },
  ogdName: {
    header: "OGD Name",
    style: "max-w-80 whitespace-normal",
    visible: true
  }
};
export const exciseTaxCodeSchema: TableSchema<CanadaExciseTaxCode> = {
  code: {
    header: "Code",
    style: "uppercase",
    visible: true
  },
  category: {
    header: "Category",
    visible: true
  },
  rateType: {
    header: "Rate Type",
    visible: true
  },
  explanation: {
    header: "Explanation",
    style: "max-w-80 whitespace-normal",
    visible: true
  }
};
export const simaFilingSchema: TableSchema<SimaFilingTable> = {
  simaCode: {
    header: "SIMA Code",
    visible: true
  },
  subjectCode: {
    header: "Subject Code",
    renderer: ({ value }: { value: SimaSubjectCode }) => renderSubjectCode(value),
    visible: true
  },
  incoterms: {
    header: "Incoterms",
    visible: true
  },
  case: {
    header: "Case",
    visible: true
  },
  caseType: {
    header: "Case Type",
    visible: true
  },
  security: {
    header: "Security",
    renderer: "boolean",
    visible: true
  }
};
//#endregion

export const matchingEntryTableSchema: TableSchema<ProductTable> = {
  hsCode: {
    header: "HS Code",
    readOnly: true,
    visible: true
  },
  partNumber: {
    header: "Part No.",
    visible: true
  },
  sku: {
    header: "SKU",
    visible: true
  },
  upc: {
    header: "UPC",
    visible: true
  },
  originName: {
    header: "Origin",
    visible: true
  },
  vendorName: {
    header: "Vendor",
    visible: true
  },
  vendorCode: {
    header: "Vendor Code",
    visible: true
  },
  vendorPartNumber: {
    header: "Vendor Part No.",
    visible: true
  },
  manufacturerName: {
    header: "Manufacturer",
    visible: true
  },
  description: {
    header: "Description",
    visible: true
  }
};

export const saveMatchingRuleSchema: ObjectSchema<MatchingRuleFormParams> = object({
  id: number().optional(),
  name: string().required("Name is required"),
  priority: number().optional(),
  description: string().optional(),
  sourceTable: mixed<MatchingRuleSourceDatabaseTable>().required("Source table is required"),
  sourceId: number().when(["$simaFiling.subjectCode", "sourceTable"], {
    is: (subjectCode: SimaSubjectCode, sourceTable: MatchingRuleSourceDatabaseTable) =>
      (sourceTable === MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE ||
        sourceTable === MatchingRuleSourceDatabaseTable.SIMA_FILING) &&
      subjectCode === SimaSubjectCode.NON_SUBJECT,
    then: (schema) => schema.optional(),
    otherwise: (schema) => schema.required("Source record is required")
  }),
  destinationTable: mixed<MatchingRuleDestinationDatabaseTable>().required("Destination is required"),
  operator: mixed<MatchingRuleOperator>().optional(),
  organizationId: number().optional(),
  expiryDate: string().optional().nullable(),
  conditions: array(
    object({
      id: number().optional(),
      attribute: string().required("Field is required"),
      operator: mixed<MatchingConditionOperator>().required("Operator is required"),
      value: string().required("Value is required"),
      isOperationInverted: boolean().defined(),
      inValue: mixed<string[]>().nullable()
    })
  ).defined(),
  deletedIds: array(number().required("Deleted id is required")).optional(),
  sourceRecord: mixed<SourceTable>().optional(),
  ogdFiling: object({
    isExcluded: boolean().defined(),
    extensionCode: string().nullable().optional(),
    endUse: string()
      .nullable()
      .when(["$sourceRecord", "isExcluded"], {
        is: (sourceRecord: SourceTable, isExcluded: boolean) =>
          (sourceRecord?.agency === CanadaGovernmentAgency.HC ||
            (sourceRecord?.agency === CanadaGovernmentAgency.TC &&
              sourceRecord?.program === TIRES_PROGRAM)) &&
          !isExcluded,
        then: (schema) => schema.required("End Use is required"),
        otherwise: (schema) => schema.optional()
      }),
    airsType: string()
      .nullable()
      .optional()
      .matches(/^\d+$/, "AIRS Type must be a number")
      .test("range", "AIRS Type must be between 1 and 999", (value) => {
        if (!value) return true; // Skip validation if empty (handled by required())
        const num = parseInt(value);
        return num >= 1 && num <= 999;
      }),
    airsReferenceNumber: string().nullable().optional(),
    generalImportPermit: mixed<GeneralImportPermit>()
      .nullable()
      .when(["$sourceRecord", "isExcluded"], {
        is: (sourceRecord: SourceTable, isExcluded: boolean) =>
          sourceRecord?.agency === CanadaGovernmentAgency.GAC && !isExcluded,
        then: (schema) => schema.required("General Import Permit is required"),
        otherwise: (schema) => schema.optional()
      }),
    productCategory: string()
      .nullable()
      .when(["$sourceRecord", "isExcluded"], {
        is: (sourceRecord: SourceTable, isExcluded: boolean) =>
          sourceRecord?.agency === CanadaGovernmentAgency.HC && !isExcluded,
        then: (schema) => schema.required("Product Category is required"),
        otherwise: (schema) => schema.optional()
      }),
    brandName: string()
      .nullable()
      .when(["$sourceRecord", "isExcluded"], {
        is: (sourceRecord: SourceTable, isExcluded: boolean) =>
          sourceRecord?.agency === CanadaGovernmentAgency.TC &&
          sourceRecord?.program === TIRES_PROGRAM &&
          !isExcluded,
        then: (schema) => schema.required("Brand Name is required"),
        otherwise: (schema) => schema.optional()
      }),
    manufacturerId: number()
      .nullable()
      .when(["$sourceRecord", "isExcluded"], {
        is: (sourceRecord: SourceTable, isExcluded: boolean) =>
          sourceRecord?.agency === CanadaGovernmentAgency.TC &&
          sourceRecord?.program === TIRES_PROGRAM &&
          !isExcluded,
        then: (schema) => schema.required("Manufacturer is required"),
        otherwise: (schema) => schema.optional()
      }),
    tireType: mixed<TCTireType>()
      .nullable()
      .when(["$sourceRecord", "isExcluded"], {
        is: (sourceRecord: SourceTable, isExcluded: boolean) =>
          sourceRecord?.agency === CanadaGovernmentAgency.TC &&
          sourceRecord?.program === TIRES_PROGRAM &&
          !isExcluded,
        then: (schema) => schema.required("Tire Type is required"),
        otherwise: (schema) => schema.optional()
      }),
    tireSize: mixed<TCTireSize>()
      .nullable()
      .when(["$sourceRecord", "isExcluded"], {
        is: (sourceRecord: SourceTable, isExcluded: boolean) =>
          sourceRecord?.agency === CanadaGovernmentAgency.TC &&
          sourceRecord?.program === TIRES_PROGRAM &&
          !isExcluded,
        then: (schema) => schema.required("Tire Size is required"),
        otherwise: (schema) => schema.optional()
      }),
    tireCompliance: mixed<TCTireCompliance>()
      .nullable()
      .when(["$sourceRecord", "isExcluded"], {
        is: (sourceRecord: SourceTable, isExcluded: boolean) =>
          sourceRecord?.agency === CanadaGovernmentAgency.TC &&
          sourceRecord?.program === TIRES_PROGRAM &&
          !isExcluded,
        then: (schema) => schema.required("Compliance Statement is required"),
        otherwise: (schema) => schema.optional()
      }),
    tireClass: mixed<TCTireClass>()
      .nullable()
      .when(["$sourceRecord", "isExcluded"], {
        is: (sourceRecord: SourceTable, isExcluded: boolean) =>
          sourceRecord?.agency === CanadaGovernmentAgency.TC &&
          sourceRecord?.program === TIRES_PROGRAM &&
          !isExcluded,
        then: (schema) => schema.required("Tire Class is required"),
        otherwise: (schema) => schema.optional()
      }),
    ecccSubType: mixed<ECCCSubType>()
      .nullable()
      .when(["$sourceRecord"], {
        is: (sourceRecord: SourceTable) => sourceRecord?.agency === CanadaGovernmentAgency.ECCC,
        then: (schema) => schema.required("ECCC Sub Type is required"),
        otherwise: (schema) => schema.optional()
      }),
    wildlifeCompliance: mixed<ECCCWildlifeCompliance>()
      .nullable()
      .when(["$sourceRecord", "ecccSubType"], {
        is: (sourceRecord: SourceTable, ecccSubType: ECCCSubType) =>
          sourceRecord?.agency === CanadaGovernmentAgency.ECCC && ecccSubType === ECCCSubType.WILDLIFE,
        then: (schema) => schema.required("Wildlife Compliance is required"),
        otherwise: (schema) => schema.optional()
      }),
    emissionProgram: mixed<ECCCEmissionProgram>()
      .nullable()
      .when(["$sourceRecord", "ecccSubType"], {
        is: (sourceRecord: SourceTable, ecccSubType: ECCCSubType) =>
          sourceRecord?.agency === CanadaGovernmentAgency.ECCC && ecccSubType === ECCCSubType.EMISSIONS,
        then: (schema) => schema.required("Emission Program is required"),
        otherwise: (schema) => schema.optional()
      }),
    nrcanSubType: mixed<NRCANSubType>()
      .nullable()
      .when(["$sourceRecord"], {
        is: (sourceRecord: SourceTable) => sourceRecord?.agency === CanadaGovernmentAgency.NRCAN,
        then: (schema) => schema.required("NRCAN Sub Type is required"),
        otherwise: (schema) => schema.optional()
      }),
    isRegulated: boolean().defined()
  }).optional(),
  simaFiling: object({
    subjectCode: mixed<SimaSubjectCode>().when("$sourceTable", {
      is: (sourceTable: MatchingRuleSourceDatabaseTable) =>
        sourceTable === MatchingRuleSourceDatabaseTable.SIMA_FILING ||
        sourceTable === MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE,
      then: (schema) => schema.required("Subject Code is required"),
      otherwise: (schema) => schema.optional()
    }),
    incoterms: mixed<SimaIncoterms>().nullable().optional(),
    security: boolean().optional(),
    measureInForceId: number().when("$sourceTable", {
      is: (sourceTable: MatchingRuleSourceDatabaseTable) =>
        sourceTable === MatchingRuleSourceDatabaseTable.SIMA_FILING ||
        sourceTable === MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE,
      then: (schema) => schema.required("Measure in force is required"),
      otherwise: (schema) => schema.optional()
    })
  }).optional()
});
