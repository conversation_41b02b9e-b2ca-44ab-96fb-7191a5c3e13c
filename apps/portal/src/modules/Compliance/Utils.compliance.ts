import { snakeCaseToCapitalize } from "utils";
import {
  productAntiDumpingSchema,
  productOgdFilingSchema,
  productOgdSchema
} from "../Product/Schema.product";
import { ProductAttribute } from "../Product/Types.product";
import { Shipment, ShipmentMode } from "../Shipment/Types.shipment";
import { canSubmitEntry } from "../Shipment/Utils.shipment";
import {
  antiDumpingSchema,
  canadaTariffSchema,
  exciseTaxCodeSchema,
  gstExemptCodeSchema,
  ogdFilingSchema,
  ogdSchema,
  simaCodeSchema,
  simaFilingSchema,
  treatmentCodeSchema,
  vfdCodeSchema
} from "./Schema.compliance";
import {
  MatchingCondition,
  MatchingConditionOperator,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus,
  SaveMatchingConditionParams,
  SimaSubjectCode,
  SourceTable
} from "./Types.compliance";

export function getSchemaForSourceTable(sourceTable: MatchingRuleSourceDatabaseTable, isProduct?: boolean) {
  switch (sourceTable) {
    case MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING:
      return isProduct ? productAntiDumpingSchema : antiDumpingSchema;
    case MatchingRuleSourceDatabaseTable.CANADA_OGD:
      return isProduct ? productOgdSchema : ogdSchema;
    case MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE:
      return simaCodeSchema;
    case MatchingRuleSourceDatabaseTable.CANADA_TARIFF:
      return canadaTariffSchema;
    case MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE:
      return treatmentCodeSchema;
    case MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE:
      return vfdCodeSchema;
    case MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE:
      return gstExemptCodeSchema;
    case MatchingRuleSourceDatabaseTable.OGD_FILING:
      return isProduct ? productOgdFilingSchema : ogdFilingSchema;
    case MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE:
      return exciseTaxCodeSchema;
    case MatchingRuleSourceDatabaseTable.SIMA_FILING:
      return simaFilingSchema;
    default:
      return ogdFilingSchema;
  }
}

export function getResponseKeyForSourceTable(sourceTable: MatchingRuleSourceDatabaseTable) {
  switch (sourceTable) {
    case MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING:
      return "antiDumpings";
    case MatchingRuleSourceDatabaseTable.CANADA_OGD:
      return "ogds";
    case MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE:
      return "codes";
    case MatchingRuleSourceDatabaseTable.CANADA_TARIFF:
      return "tariffs";
    case MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE:
      return "codes";
    case MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE:
      return "codes";
    case MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE:
      return "codes";
  }
}

export function generateTitleForSourceTable(
  sourceTable: MatchingRuleSourceDatabaseTable,
  sourceRecord?: SourceTable
) {
  if (!sourceRecord) return "";

  switch (sourceTable) {
    case MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING:
      return `${sourceRecord.case} - ${sourceRecord.caseType} - ${sourceRecord.productDefinition ?? ""}`;
    case MatchingRuleSourceDatabaseTable.CANADA_OGD:
      return `${sourceRecord.agency} - ${sourceRecord.program} - ${sourceRecord.commodityType ?? ""}`;
    case MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE:
      return `${sourceRecord.code} - ${sourceRecord.explanation}`;
    case MatchingRuleSourceDatabaseTable.CANADA_TARIFF:
      return `${sourceRecord.hsCode} - ${sourceRecord.description}`;
    case MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE:
      return `${sourceRecord.code} - ${sourceRecord.name}`;
    case MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE:
      return `${sourceRecord.code} - ${sourceRecord.explanation}`;
    case MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE:
      return `${sourceRecord.code} - ${sourceRecord.explanation}`;
    case MatchingRuleSourceDatabaseTable.OGD_FILING:
      return `${sourceRecord.ogd.agency ?? ""} - ${sourceRecord.ogd.program ?? ""} - ${
        sourceRecord.ogd.commodityType ?? ""
      }`;
  }
}

export function complianceLabelColor(status?: MatchingRuleStatus) {
  switch (status) {
    case MatchingRuleStatus.PENDING:
      return "warning";
    case MatchingRuleStatus.ACTIVE:
      return "success";
    case MatchingRuleStatus.DISABLED:
      return "disabled";
    default:
      return "gray";
  }
}

export function objectToFilteredArray<T extends object>(source: T) {
  const FILTERED_FIELDS = [
    "createDate",
    "lastEditDate",
    "id",
    "createdBy",
    "lastEditedBy",
    "displayName",
    "hasIssue",
    "issueDetails"
  ];
  return Object.entries(source).filter(
    ([key, value]) => !FILTERED_FIELDS.includes(key) && typeof value !== "object"
  );
}

export function formatRuleConditions(conditions: SaveMatchingConditionParams[]) {
  return conditions.map((condition) =>
    [ProductAttribute.PART_NUMBER, ProductAttribute.SKU, ProductAttribute.UPC].includes(
      condition.attribute as ProductAttribute
    )
      ? {
          ...condition,
          value: condition.value ? condition.value.toUpperCase() : condition.value
        }
      : condition
  );
}

export function renderSubjectCode(value: SimaSubjectCode) {
  const entries = Object.entries(SimaSubjectCode);
  const entry = entries.find(([_, val]) => val === value);
  return entry ? snakeCaseToCapitalize(entry[0]) : value;
}

export function hasOrganizationIdInCondition(condition: MatchingCondition, organizationId: number) {
  return condition.attribute === "organizationId" && condition.value === organizationId.toString();
}

export function canCheckCompliance(shipment?: Shipment) {
  return (
    (canSubmitEntry(shipment?.customsStatus) || shipment?.requiresReupload) &&
    shipment?.modeOfTransport !== ShipmentMode.SMALL_PACKAGE
  );
}

export function operatorIsArray(operator: MatchingConditionOperator) {
  return [
    MatchingConditionOperator.IN,
    MatchingConditionOperator.BETWEEN_INCLUSIVE,
    MatchingConditionOperator.BETWEEN_EXCLUSIVE
  ].includes(operator);
}
