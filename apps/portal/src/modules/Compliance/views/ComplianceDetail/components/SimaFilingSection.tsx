import { CanadaAntiDumping } from "nest-modules";
import { useMemo } from "react";
import { Card, Checkbox, Input, Select, Tooltip } from "ui";
import { formikInputOpts, getFormikError } from "utils";
import { SIMA_INCOTERMS, SIMA_SUBJECT_CODE } from "../../../Constant.compliance";
import {
  ComplianceFormikProps,
  ComplianceFormState,
  MatchingRule,
  MatchingRuleSourceDatabaseTable,
  SourceTable
} from "../../../Types.compliance";

interface SimaFilingSectionProps {
  info?: MatchingRule;
  formik: ComplianceFormikProps;
  sourceRecord?: SourceTable;
  measureInForce?: CanadaAntiDumping;
  isCreate?: boolean;
  state?: ComplianceFormState;
  onSelectMeasureInForce(): void;
}

const SimaFilingSection = ({
  info,
  formik,
  sourceRecord,
  isCreate,
  measureInForce,
  state,
  onSelectMeasureInForce
}: SimaFilingSectionProps) => {
  const isSimaFiling = useMemo(
    () =>
      (formik.values.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE && isCreate) ||
      (formik.values.sourceTable === MatchingRuleSourceDatabaseTable.SIMA_FILING && !isCreate),
    [formik.values.sourceTable, isCreate]
  );

  if (!isSimaFiling) return null;

  return (
    <Card containerStyle="p-2 border bg-background">
      <div className="flex items-center justify-between gap-4 mb-2">
        <h5>Regulation for SIMA Code {sourceRecord?.code}</h5>
        <Checkbox
          label="Security"
          name="simaFiling.security"
          checked={formik.values.simaFiling?.security ?? false}
          onChange={formik.handleChange}
        />
      </div>
      <fieldset className="grid grid-cols-2 gap-2">
        <Select
          label="Subject Code"
          inputStyle="bg-neutral-10"
          options={SIMA_SUBJECT_CODE}
          {...formikInputOpts(formik, "simaFiling.subjectCode")}
          optional
        />
        <Select
          label="Incoterms"
          inputStyle="bg-neutral-10"
          options={SIMA_INCOTERMS}
          {...formikInputOpts(formik, "simaFiling.incoterms")}
          optional
        />
        <div className="col-span-2">
          <Tooltip text={measureInForce?.displayName ?? ""}>
            <Input
              label="Measure in Force"
              name="simaFiling.measureInForceId"
              placeholder="Select measure in force"
              inputStyle="bg-neutral-10"
              kind="search"
              value={measureInForce?.displayName ?? ""}
              error={getFormikError(formik, "simaFiling.measureInForceId")}
              disabled={
                !formik.values.sourceTable ||
                (info?.sourceTable === MatchingRuleSourceDatabaseTable.SIMA_FILING && !isCreate) ||
                Boolean(state?.sourceRecord)
              }
              readOnly
              onClick={onSelectMeasureInForce}
            />
          </Tooltip>
        </div>
      </fieldset>
    </Card>
  );
};

export default SimaFilingSection;
