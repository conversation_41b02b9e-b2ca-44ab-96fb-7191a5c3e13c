import { PartnerType, TradePartner } from "@/modules/TradePartner/Types.partner";
import { SelectPartnerModal } from "@/modules/TradePartner/components";
import { getIn } from "formik";
import { useEffect, useMemo, useReducer, useState } from "react";
import { Card, Checkbox, Input, Select } from "ui";
import { formikInputOpts, getFormikError } from "utils";
import {
  CFIA_AIR_TYPE,
  ECCC_EMISSION_PROGRAM,
  ECCC_SUB_TYPE,
  ECCC_WILDLIFE_COMPLIANCE,
  GAC_LINK,
  GENERAL_IMPORT_PERMIT,
  HC_END_USE,
  HC_PRODUCT_CATEGORY,
  NRCAN_SUB_TYPE,
  TC_TIRE_CLASS,
  TC_TIRE_COMPLIANCE,
  TC_TIRE_END_USE,
  TC_TIRE_SIZE,
  TC_TIRE_TYPE
} from "../../../Constant.compliance";
import {
  CanadaGovernmentAgency,
  ComplianceFormikProps,
  ECCCSubType,
  GeneralImportPermit,
  MatchingRuleSourceDatabaseTable,
  SourceTable
} from "../../../Types.compliance";
import { TIRES_PROGRAM } from "../constans";

interface OgdFilingSectionProps {
  formik: ComplianceFormikProps;
  sourceRecord?: SourceTable;
  isCreate?: boolean;
  tireManufacturer?: TradePartner | null;
}

const OgdFilingSection = ({ formik, sourceRecord, isCreate, tireManufacturer }: OgdFilingSectionProps) => {
  const [partnerModal, togglePartnerModal] = useReducer((r) => !r, false);
  const [manufacturer, setManufacturer] = useState<TradePartner>();

  const isOgdFiling = useMemo(
    () =>
      ((formik.values.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_OGD && isCreate) ||
        (formik.values.sourceTable === MatchingRuleSourceDatabaseTable.OGD_FILING && !isCreate)) &&
      sourceRecord,
    [formik.values.sourceTable, isCreate, sourceRecord]
  );

  useEffect(() => {
    if (tireManufacturer) {
      setManufacturer(tireManufacturer);
    }
  }, [tireManufacturer]);

  if (!isOgdFiling) return null;

  return (
    <>
      {partnerModal && (
        <SelectPartnerModal
          show={!!partnerModal}
          onClose={togglePartnerModal}
          partnerType={PartnerType.MANUFACTURER}
          partner={manufacturer}
          onSelect={(_partner: TradePartner) => {
            // const manufacturer = [
            //   _partner.name,
            //   [_partner.address, _partner.city, _partner.country?.name, _partner.postalCode]
            //     .filter(Boolean)
            //     .join(", "),
            //   [_partner.email, _partner.phoneNumber].filter(Boolean).join(", ")
            // ]
            //   .filter(Boolean)
            //   .join("\n");
            formik.setFieldValue(`ogdFiling.manufacturerId`, _partner.id);
            setManufacturer(_partner);
            togglePartnerModal();
          }}
          required
        />
      )}
      <Card containerStyle="p-2 border bg-background">
        <div className="flex items-center justify-between gap-4 mb-2">
          <h5>Regulation for {sourceRecord?.agency?.toUpperCase()}</h5>
          {sourceRecord?.agency !== CanadaGovernmentAgency.ECCC &&
            sourceRecord?.agency !== CanadaGovernmentAgency.NRCAN && (
              <Checkbox
                label="This product is excluded"
                name="ogdFiling.isExcluded"
                checked={formik.values.ogdFiling?.isExcluded ?? false}
                onChange={formik.handleChange}
              />
            )}
        </div>
        <fieldset
          className="grid grid-cols-2 gap-x-3 gap-y-2"
          disabled={formik.values.ogdFiling?.isExcluded ?? false}
        >
          {sourceRecord?.agency === CanadaGovernmentAgency.CFIA && (
            <>
              <Input
                label="Extension Code"
                placeholder="Input extension code"
                inputStyle="bg-neutral-10"
                {...formikInputOpts(formik, "ogdFiling.extensionCode")}
              />
              <Input
                label="End Use"
                placeholder="Input end use"
                inputStyle="bg-neutral-10"
                {...formikInputOpts(formik, "ogdFiling.endUse")}
              />
              <Select
                label="AIRS Type"
                inputStyle="bg-neutral-10"
                {...formikInputOpts(formik, "ogdFiling.airsType")}
                onSelected={(value) => {
                  formik.setFieldValue("ogdFiling.airsType", value);
                  if (!value) formik.setFieldValue("ogdFiling.airsReferenceNumber", "");
                }}
                options={CFIA_AIR_TYPE}
                optional
              />
              <Input
                label="AIRS Ref No."
                placeholder="Input AIRS reference number"
                inputStyle="bg-neutral-10"
                {...formikInputOpts(formik, "ogdFiling.airsReferenceNumber")}
                disabled={!formik.values.ogdFiling?.airsType}
              />
              <a
                href="https://airs-sari.inspection.gc.ca/airs_external/english/decisions-eng.aspx"
                target="_blank"
              >
                <span className="underline">Check AIRS</span>
              </a>
            </>
          )}
          {sourceRecord?.agency === CanadaGovernmentAgency.GAC && (
            <>
              <Select
                label="General Import Permit"
                inputStyle="bg-neutral-10"
                {...formikInputOpts(formik, "ogdFiling.generalImportPermit")}
                options={GENERAL_IMPORT_PERMIT}
                optional
              />
              {getIn(formik.values, "ogdFiling.generalImportPermit") && (
                <a
                  href={
                    GAC_LINK[getIn(formik.values, "ogdFiling.generalImportPermit") as GeneralImportPermit]
                  }
                  target="_blank"
                  className="self-end mb-2"
                >
                  <span className="underline">Import Permit Details</span>
                </a>
              )}
            </>
          )}
          {sourceRecord?.agency === CanadaGovernmentAgency.HC && (
            <>
              <Select
                label="End Use"
                inputStyle="bg-neutral-10"
                {...formikInputOpts(formik, "ogdFiling.endUse")}
                options={HC_END_USE}
                optional
              />
              <Select
                label="Product Category"
                inputStyle="bg-neutral-10"
                {...formikInputOpts(formik, "ogdFiling.productCategory")}
                options={HC_PRODUCT_CATEGORY}
                optional
              />
            </>
          )}
          {sourceRecord?.agency === CanadaGovernmentAgency.TC && sourceRecord.program === TIRES_PROGRAM && (
            <>
              <Select
                label="End Use"
                inputStyle="bg-neutral-10"
                {...formikInputOpts(formik, "ogdFiling.endUse")}
                options={TC_TIRE_END_USE}
                optional
              />
              <Select
                label="Tire Type"
                inputStyle="bg-neutral-10"
                {...formikInputOpts(formik, "ogdFiling.tireType")}
                options={TC_TIRE_TYPE}
                optional
              />
              <Select
                label="Tire Size"
                inputStyle="bg-neutral-10"
                {...formikInputOpts(formik, "ogdFiling.tireSize")}
                options={TC_TIRE_SIZE}
                optional
              />
              <Select
                label="Compliance Statement"
                inputStyle="bg-neutral-10"
                {...formikInputOpts(formik, "ogdFiling.tireCompliance")}
                options={TC_TIRE_COMPLIANCE}
                optional
              />
              <Select
                label="Tire Class"
                inputStyle="bg-neutral-10"
                {...formikInputOpts(formik, "ogdFiling.tireClass")}
                options={TC_TIRE_CLASS}
                optional
              />
              <Input
                label="Brand Name"
                placeholder="Input brand name"
                inputStyle="bg-neutral-10"
                {...formikInputOpts(formik, "ogdFiling.brandName")}
              />
              <Input
                label="Manufacturer"
                name="ogdFiling.manufacturerId"
                placeholder="Select manufacturer"
                inputStyle="bg-neutral-10"
                value={manufacturer?.name ?? ""}
                readOnly
                onClick={togglePartnerModal}
                error={getFormikError(formik, "ogdFiling.manufacturerId")}
              />
            </>
          )}
          {sourceRecord?.agency === CanadaGovernmentAgency.ECCC && (
            <>
              <Select
                label="ECCC Sub Type"
                inputStyle="bg-neutral-10"
                {...formikInputOpts(formik, "ogdFiling.ecccSubType")}
                options={ECCC_SUB_TYPE}
                optional
              />
              {getIn(formik.values, "ogdFiling.ecccSubType") === ECCCSubType.WILDLIFE && (
                <Select
                  label="Wildlife Compliance"
                  inputStyle="bg-neutral-10"
                  {...formikInputOpts(formik, "ogdFiling.wildlifeCompliance")}
                  options={ECCC_WILDLIFE_COMPLIANCE}
                  optional
                />
              )}
              {getIn(formik.values, "ogdFiling.ecccSubType") === ECCCSubType.EMISSIONS && (
                <Select
                  label="Emission Program"
                  inputStyle="bg-neutral-10"
                  {...formikInputOpts(formik, "ogdFiling.emissionProgram")}
                  options={ECCC_EMISSION_PROGRAM}
                  optional
                />
              )}
            </>
          )}
          {sourceRecord?.agency === CanadaGovernmentAgency.NRCAN && (
            <>
              <Select
                label="NRCAN Sub Type"
                inputStyle="bg-neutral-10"
                {...formikInputOpts(formik, "ogdFiling.nrcanSubType")}
                options={NRCAN_SUB_TYPE}
                optional
              />
              <Checkbox
                label="This product is regulated"
                name="ogdFiling.isRegulated"
                containerStyle="mt-6"
                checked={formik.values.ogdFiling?.isRegulated ?? false}
                onChange={formik.handleChange}
              />
            </>
          )}
        </fieldset>
      </Card>
    </>
  );
};

export default OgdFilingSection;
