import {
  ComplianceFormikProps,
  MatchingConditionOperator,
  MatchingRule,
  MatchingRuleStatus
} from "@/modules/Compliance/Types.compliance";
import { SelectCountry } from "@/modules/Location/components";
import { Country } from "@/modules/Location/Types.location";
import { PRODUCT_ATTRIBUTE } from "@/modules/Product/Constant.product";
import { ProductAttribute } from "@/modules/Product/Types.product";
import { SelectPartnerModal } from "@/modules/TradePartner/components";
import { PartnerType } from "@/modules/TradePartner/Types.partner";
import { FieldArray, FormikProvider, getIn } from "formik";
import { PlusIcon, TrashIcon } from "lucide-react";
import { TradePartner } from "nest-modules";
import { Fragment, useReducer, useState } from "react";
import { Button, Checkbox, ChipInput, Input, Select } from "ui";
import { formikInputOpts, getFormikError } from "utils";
import { MATCHING_RULE_OPERATOR, NEW_CONDITION, OPERATOR } from "../../../Constant.compliance";
import { twMerge } from "tailwind-merge";
import { operatorIsArray } from "@/modules/Compliance/Utils.compliance";

interface Props {
  formik: ComplianceFormikProps;
  info?: MatchingRule;
  isFiling?: boolean;
}
const ConditionsSection = ({ formik, info, isFiling }: Props) => {
  const [partnerModal, togglePartnerModal] = useReducer((r) => !r, false);
  const [partnerIndex, setPartnerIndex] = useState<number>();
  const [vendor, setVendor] = useState<Map<number, TradePartner>>(new Map());

  const isCreateFiling = isFiling && !info;

  const handleChipInputChange = (index: number, newValues: string[]) => {
    const operator = formik.values.conditions[index].operator;
    let finalValues = newValues;

    // Limit to 2 values for BETWEEN_INCLUSIVE and BETWEEN_EXCLUSIVE operator
    if (
      operator === MatchingConditionOperator.BETWEEN_INCLUSIVE ||
      operator === MatchingConditionOperator.BETWEEN_EXCLUSIVE
    ) {
      finalValues = newValues.slice(0, 2);
    }

    formik.setFieldValue(`conditions.${index}.inValue`, finalValues.length > 0 ? finalValues : undefined);
    formik.setFieldValue(
      `conditions.${index}.value`,
      finalValues.length > 0 ? JSON.stringify(finalValues) : ""
    );
  };

  return (
    <>
      {partnerModal && (
        <SelectPartnerModal
          show={!!partnerModal}
          onClose={togglePartnerModal}
          partnerType={PartnerType.VENDOR}
          partner={
            partnerIndex !== undefined
              ? (vendor.get(Number(formik.values.conditions[partnerIndex]?.value)) ??
                getIn(formik.values, `conditions.${partnerIndex}.valueRecord`))
              : undefined
          }
          onSelect={(_partner: TradePartner) => {
            formik.setFieldValue(`conditions.${partnerIndex}.value`, _partner.id.toString());
            const map = new Map(vendor);
            map.set(_partner.id, _partner);
            setVendor(map);

            setPartnerIndex(undefined);
            togglePartnerModal();
          }}
          required
        />
      )}
      <FormikProvider value={formik}>
        <fieldset disabled={isCreateFiling}>
          <FieldArray
            name="conditions"
            render={(arrayHelpers) => (
              <div className="flex flex-col gap-2 pt-2">
                <div className="col-span-2 flex items-center justify-between gap-2">
                  <h5>Conditions</h5>
                  {!isCreateFiling && (
                    <Button
                      kind="outline"
                      label="Add"
                      icon={<PlusIcon size={16} />}
                      className="py-1 px-2"
                      onClick={() => arrayHelpers.push(NEW_CONDITION)}
                    />
                  )}
                </div>
                {formik.values.conditions.map((condition, index) => (
                  <Fragment key={index}>
                    {index > 0 && (
                      <div
                        className={twMerge(
                          "w-full flex items-center text-sm font-medium before:flex-1 before:border-t before:border-neutral-200 before:me-4 after:flex-1 after:border-t after:border-neutral-200 after:ms-4",
                          index > 1 && "py-2"
                        )}
                      >
                        {index === 1 ? (
                          <Select options={MATCHING_RULE_OPERATOR} {...formikInputOpts(formik, `operator`)} />
                        ) : (
                          formik.values?.operator?.toUpperCase()
                        )}
                      </div>
                    )}
                    <div className="flex gap-3">
                      <Select
                        label="Field"
                        containerStyle="flex-1"
                        options={PRODUCT_ATTRIBUTE}
                        {...formikInputOpts(formik, `conditions.${index}.attribute`, (e) => {
                          formik.handleChange(e);
                          formik.setFieldValue(
                            `conditions.${index}.operator`,
                            MatchingConditionOperator.EQUALS
                          );
                          formik.setFieldValue(`conditions.${index}.value`, "");
                        })}
                      />
                      {condition.attribute !== ProductAttribute.ORIGIN &&
                        condition.attribute !== ProductAttribute.VENDOR && (
                          <>
                            <Select
                              label="Operator"
                              containerStyle="flex-1"
                              options={OPERATOR}
                              {...formikInputOpts(formik, `conditions.${index}.operator`)}
                            />
                            {operatorIsArray(condition.operator) ? (
                              <ChipInput
                                label="Value"
                                placeholder={
                                  condition.operator === MatchingConditionOperator.BETWEEN_INCLUSIVE
                                    ? "Enter max of 2 values"
                                    : "Type and press Enter"
                                }
                                containerStyle="flex-1"
                                name={`conditions.${index}.inValue`}
                                values={(condition.inValue ?? []).map(String)}
                                onChange={(newValues) => handleChipInputChange(index, newValues)}
                                error={getFormikError(formik, `conditions.${index}.value`)}
                                disabled={isCreateFiling || info?.status === MatchingRuleStatus.ACTIVE}
                              />
                            ) : (
                              <Input
                                label="Value"
                                placeholder="Input value"
                                containerStyle="flex-1"
                                {...formikInputOpts(formik, `conditions.${index}.value`)}
                              />
                            )}
                          </>
                        )}
                      {condition.attribute === ProductAttribute.ORIGIN && (
                        <SelectCountry
                          name={`conditions.${index}.value`}
                          placeholder="Select Origin"
                          containerStyle="flex-1"
                          value={condition.valueRecord as Country}
                          onSelect={(value) =>
                            formik.setFieldValue(`conditions.${index}.value`, value?.value?.toString())
                          }
                          error={getFormikError(formik, `conditions.${index}.value`)}
                        />
                      )}
                      {condition.attribute === ProductAttribute.VENDOR && (
                        <Input
                          label="Vendor"
                          name={`conditions.${index}.value`}
                          placeholder="Select vendor"
                          containerStyle="flex-1"
                          value={
                            vendor.get(Number(condition.value))?.name ??
                            getIn(formik.values, `conditions.${index}.valueRecord.name`) ??
                            ""
                          }
                          error={getFormikError(formik, `conditions.${index}.value`)}
                          onClick={() => {
                            setPartnerIndex(index);
                            togglePartnerModal();
                          }}
                          readOnly
                        />
                      )}

                      <Checkbox
                        label="Inverted"
                        name={`conditions.${index}.isOperationInverted`}
                        containerStyle="mt-6 flex-1"
                        checked={condition.isOperationInverted ?? false}
                        onChange={formik.handleChange}
                      />

                      {(!info || info.status === MatchingRuleStatus.PENDING) && !isCreateFiling && (
                        <TrashIcon
                          className="mt-6 ml-auto size-5 cursor-pointer text-danger"
                          onClick={() => {
                            const id = condition.id;
                            if (id) {
                              formik.setFieldValue("deletedIds", [...(formik.values.deletedIds || []), id]);
                            }
                            arrayHelpers.remove(index);
                          }}
                        />
                      )}
                    </div>
                  </Fragment>
                ))}
              </div>
            )}
          />
        </fieldset>
      </FormikProvider>
    </>
  );
};

export default ConditionsSection;
