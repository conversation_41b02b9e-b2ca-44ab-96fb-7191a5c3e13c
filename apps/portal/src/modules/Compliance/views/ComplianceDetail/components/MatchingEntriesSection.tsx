import { BaseError } from "@/common/Types.common";
import { GetProductsDto } from "@/modules/Product/Dto.product";
import { SearchIcon } from "lucide-react";
import { Product } from "nest-modules";
import { useCallback, useMemo, useState } from "react";
import toast from "react-hot-toast";
import { Button, Table } from "ui";
import { SortOrder, sortTable } from "utils";
import { useSearchMatchingEntries } from "../../../mutations";
import { matchingEntryTableSchema } from "../../../Schema.compliance";
import {
  ComplianceFormikProps,
  MatchingRuleDestinationDatabaseTable,
  SearchMatchingEntriesParams
} from "../../../Types.compliance";
import { formatRuleConditions } from "../../../Utils.compliance";

interface Props {
  formik: ComplianceFormikProps;
}
const MatchingEntriesSection = ({ formik }: Props) => {
  const searchEntries = useSearchMatchingEntries();
  const [searchResults, setSearchResults] = useState<Product[]>();
  const [filters, setFilters] = useState<{
    sortBy?: string;
    sortOrder?: SortOrder;
  }>({});

  const handleSearchEntries = useCallback(async () => {
    if (!formik.isValid) return;

    const params: SearchMatchingEntriesParams = {
      destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT,
      conditions: formatRuleConditions(formik.values.conditions)
    };

    try {
      const results = await searchEntries.mutateAsync(params);

      const formattedResults = GetProductsDto(results.destinationRecords);

      setSearchResults(formattedResults);
    } catch (error) {
      toast.error((error as BaseError).message);
    }
  }, [formik.isValid, formik.values.conditions, searchEntries]);

  const sortedEntries = useMemo(
    () => sortTable(searchResults, filters.sortBy, filters.sortOrder),
    [searchResults, filters.sortBy, filters.sortOrder]
  );

  return (
    <div className="flex flex-col gap-3 border-t py-2">
      <Button
        label="Search Matching Products"
        kind="outline"
        icon={<SearchIcon size={16} />}
        loading={searchEntries.isPending}
        onClick={handleSearchEntries}
        className="w-fit"
        disabled={!formik.isValid}
      />

      {searchResults && (
        <Table
          data={sortedEntries}
          schema={matchingEntryTableSchema}
          isLoading={searchEntries.isPending}
          onSort={(key?: string, sortOrder?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key,
              sortOrder
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
        />
      )}
    </div>
  );
};

export default MatchingEntriesSection;
