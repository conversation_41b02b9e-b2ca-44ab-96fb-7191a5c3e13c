import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON>, StatusLabel } from "ui";
import { ComplianceLabel } from "../../../components";
import { MatchingRule, MatchingRuleStatus } from "../../../Types.compliance";

interface Props {
  info?: MatchingRule;
  isCreate?: boolean;
  isLoading?: boolean;
  readOnly?: boolean;
  from?: string;
  onSubmit(): void;
  onUpdateStatus(status: MatchingRuleStatus): void;
  onDelete(): void;
}
const ComplianceHeader = ({
  info,
  isCreate,
  isLoading,
  readOnly,
  from,
  onSubmit,
  onUpdateStatus,
  onDelete
}: Props) => {
  const navigate = useNavigate();

  return (
    <Header
      title={
        <div className="flex items-center gap-2">
          <h4>{info ? info.status === MatchingRuleStatus.PENDING && "Edit" : "Create"} Compliance Rule</h4>
          {info && <ComplianceLabel value={info.status} />}
          {info?.isGlobal && <StatusLabel text="Global Rule" color="info" />}
        </div>
      }
    >
      {isCreate && from && (
        <Button label="Cancel" kind="cancel" disabled={isLoading} onClick={() => navigate(from)} />
      )}

      {info && info.status === MatchingRuleStatus.PENDING && !info.isGlobal && !readOnly && (
        <>
          <Button label="Delete" kind="delete" loading={isLoading} onClick={onDelete} />
          {/* <Button
            label="Publish"
            loading={isLoading}
            className="bg-success hover:bg-success-600"
            disabled={isDirty}
            onClick={() => onUpdateStatus(MatchingRuleStatus.ACTIVE)}
          /> */}
        </>
      )}

      {(!info || info?.status === MatchingRuleStatus.PENDING) && !info?.isGlobal && !readOnly && (
        <Button label={info ? "Update" : "Create"} kind="update" loading={isLoading} onClick={onSubmit} />
      )}

      {info?.status === MatchingRuleStatus.ACTIVE && !info.isGlobal && !readOnly && (
        <Button
          label="Edit"
          kind="edit"
          loading={isLoading}
          onClick={() => onUpdateStatus(MatchingRuleStatus.PENDING)}
        />
      )}
    </Header>
  );
};

export default ComplianceHeader;
