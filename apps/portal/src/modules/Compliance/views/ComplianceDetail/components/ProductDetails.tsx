import { objectToFilteredArray } from "@/modules/Compliance/Utils.compliance";
import { Product } from "@/modules/Product/Types.product";
import { useMemo } from "react";
import { Accordion, Card, FieldItem } from "ui";
import { capitalizeLetter, pascalToCapitalize } from "utils";

function ProductDetails({ product }: { product?: Product }) {
  const productDetails = useMemo(() => (product ? objectToFilteredArray(product) : undefined), [product]);

  if (!productDetails) return null;

  return (
    <Card containerStyle="px-2 pb-2 border bg-background">
      <Accordion title="Product Details" contentStyle="flex flex-col" toggleStyle="border-gray-50">
        {productDetails.map(([key, value]) => (
          <FieldItem
            key={key}
            direction="row"
            label={capitalizeLetter(pascalToCapitalize(key))}
            value={value}
          />
        ))}
      </Accordion>
    </Card>
  );
}
export default ProductDetails;
