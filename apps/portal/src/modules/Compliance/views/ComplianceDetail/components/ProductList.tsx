import { productComplianceSchema } from "@/modules/Product/Schema.product";
import { Product } from "@/modules/Product/Types.product";
import { Accordion, Card, Table } from "ui";

function ProductList({ products }: { products?: Product[] }) {
  if (!products) return null;

  return (
    <Card containerStyle="px-2 pb-2 border bg-background">
      <Accordion title="Product List" contentStyle="flex flex-col" toggleStyle="border-gray-50">
        <Table data={products} schema={productComplianceSchema} />
      </Accordion>
    </Card>
  );
}
export default ProductList;
