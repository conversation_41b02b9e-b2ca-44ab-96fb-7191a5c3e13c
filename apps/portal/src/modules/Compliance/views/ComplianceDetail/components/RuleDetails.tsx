import { SourceTable } from "@/modules/Compliance/Types.compliance";
import { objectToFilteredArray } from "@/modules/Compliance/Utils.compliance";
import { useMemo } from "react";
import { Accordion, Card } from "ui";
import { capitalizeLetter, pascalToCapitalize } from "utils";

function RuleDetails({ source }: { source?: SourceTable }) {
  const ruleDetails = useMemo(() => source && objectToFilteredArray(source), [source]);

  if (!ruleDetails) return null;

  return (
    <Card containerStyle="px-2 pb-2 border bg-background">
      <Accordion title="Rule Details" contentStyle="flex flex-col" toggleStyle="border-gray-50">
        {ruleDetails.map(([key, value]) => (
          <div key={key} className="inline space-x-1">
            <span className="font-medium whitespace-nowrap">
              {capitalizeLetter(pascalToCapitalize(key))}:
            </span>
            <span className="text-balance whitespace-normal max-w-full" style={{ wordBreak: "break-word" }}>
              {typeof value === "boolean" ? (value ? "Yes" : "No") : (value ?? "-")}
            </span>
          </div>
        ))}
      </Accordion>
    </Card>
  );
}
export default RuleDetails;
