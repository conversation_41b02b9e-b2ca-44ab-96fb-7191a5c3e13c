import { useStore } from "@/bootstrap/Store.bootstrap";
import { BaseError } from "@/common/Types.common";
import { Breadcrumbs } from "@/components";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";
import { Product, ProductAttribute } from "@/modules/Product/Types.product";
import { TradePartner } from "@/modules/TradePartner/Types.partner";
import { useFormik } from "formik";
import { observer } from "mobx-react-lite";
import type {
  CanadaAntiDumping,
  CreateMatchingRuleDto,
  CreateOgdFilingDto,
  CreateSimaFilingDto,
  OgdFiling,
  SimaFiling
} from "nest-modules";
import { useCallback, useEffect, useMemo, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { Card, Input, Popup, Select, Textarea } from "ui";
import { emptyStringToNull, formikInputOpts, getFormikError, pascalToCapitalize } from "utils";
import { SelectSourceModal } from "../../components";
import { DESTINATION_TABLE, NEW_CONDITION, PRIORITY, SOURCE_TABLE } from "../../Constant.compliance";
import {
  useBatchSaveMatchingCondition,
  useCreateOgdFilingWithRule,
  useCreateSimaFilingWithRule,
  useDeleteMatchingRule,
  useDeleteOgdFiling,
  useEditOgdFilingWithRule,
  useEditSimaFilingWithRule,
  useSaveMatchingRule,
  useUpdateMatchingRuleStatus
} from "../../mutations";
import { useDeleteSimaFiling } from "../../mutations/SimaFiling.mutation";
import { useGetMatchingRuleDetail } from "../../queries";
import { CompliancePages } from "../../Routes.compliance";
import { saveMatchingRuleSchema } from "../../Schema.compliance";
import {
  BatchSaveMatchingConditionParams,
  ECCCSubType,
  MatchingConditionOperator,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleFormParams,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus,
  SaveMatchingRuleParams,
  SaveOgdFilingParams,
  SaveSimaFilingParams,
  SimaSubjectCode,
  SourceTable,
  UpdateMatchingRuleStatusParams
} from "../../Types.compliance";
import { formatRuleConditions, operatorIsArray } from "../../Utils.compliance";
import {
  ComplianceHeader,
  ConditionsSection,
  MatchingEntriesSection,
  OgdFilingSection,
  ProductDetails,
  ProductList,
  RuleDetails,
  SimaFilingSection
} from "./components";

const ComplianceDetail = observer(() => {
  //#region State
  const { isSuperAdmin, organization } = useStore().auth;
  const { compliance_id } = useParams();
  const { state } = useLocation();
  const navigate = useNavigate();
  const { showPopup, hidePopup } = Popup.usePopup();
  const { canCrudBackoffice } = useBackofficePermission();

  const [sourceModal, setSourceModal] = useState<MatchingRuleSourceDatabaseTable>();
  const [source, setSource] = useState<SourceTable>();
  const [measureInForceModal, toggleMeasureInForceModal] = useReducer((r) => !r, false);
  const [measureInForce, setMeasureInForce] = useState<CanadaAntiDumping>();
  const [tireManufacturer, setTireManufacturer] = useState<TradePartner>();

  const isCreate = !compliance_id || compliance_id === "undefined" || compliance_id === "create";
  //#endregion

  //#region Data apis
  const {
    data: info,
    error,
    isFetching,
    refetch
  } = useGetMatchingRuleDetail({
    id: isCreate ? undefined : compliance_id
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
      navigate(CompliancePages.List);
    }
  }, [error, navigate]);

  const saveMatchingRule = useSaveMatchingRule();
  const saveMatchingCondition = useBatchSaveMatchingCondition();
  const updateStatus = useUpdateMatchingRuleStatus();
  const deleteMatchingRule = useDeleteMatchingRule();
  const createOgdFiling = useCreateOgdFilingWithRule();
  const editOgdFiling = useEditOgdFilingWithRule();
  const deleteOgdFiling = useDeleteOgdFiling();
  const createSimaFiling = useCreateSimaFilingWithRule();
  const editSimaFiling = useEditSimaFilingWithRule();
  const deleteSimaFiling = useDeleteSimaFiling();

  const isLoading =
    saveMatchingRule.isPending ||
    saveMatchingCondition.isPending ||
    updateStatus.isPending ||
    deleteMatchingRule.isPending ||
    createOgdFiling.isPending ||
    editOgdFiling.isPending ||
    deleteOgdFiling.isPending ||
    createSimaFiling.isPending ||
    editSimaFiling.isPending ||
    deleteSimaFiling.isPending;
  //#endregion

  //#region Computed values
  const isOgdFiling = useMemo(
    () => info?.sourceTable === MatchingRuleSourceDatabaseTable.OGD_FILING && !isCreate,

    [info?.sourceTable, isCreate]
  );
  const isSimaFiling = useMemo(
    () => info?.sourceTable === MatchingRuleSourceDatabaseTable.SIMA_FILING && !isCreate,

    [info?.sourceTable, isCreate]
  );
  //#endregion

  //#region Handler
  const handleUpdateStatus = useCallback(
    async (status: MatchingRuleStatus, id?: number) => {
      const params: UpdateMatchingRuleStatusParams = {
        id: id ?? info?.id,
        status
      };

      try {
        await updateStatus.mutateAsync(params);

        if (!isCreate) refetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [info?.id, isCreate, refetch, updateStatus]
  );

  const handleSaveMatchingRule = useCallback(
    async ({ conditions, deletedIds, ogdFiling, simaFiling, ...params }: MatchingRuleFormParams) => {
      const formatConditions = formatRuleConditions(conditions);
      if (isSuperAdmin && isCreate) {
        if (organization) {
          params.organizationId = organization.id;
        } else {
          toast.error("Please select an organization first");
          return;
        }
      }

      const formatParams: SaveMatchingRuleParams = {
        ...params,
        priority: params.priority !== undefined ? Number(params.priority) : undefined,
        expiryDate: params.expiryDate || null
      };

      const formatBatchParams: BatchSaveMatchingConditionParams = {
        // TODO create batch formatting utils
        ...(formatConditions?.length > 0 && {
          create: formatConditions.filter((c) => !c.id),
          edit: formatConditions
            .filter((f) => typeof f.id === "number")
            .map((f) => ({ ...f, id: f.id as number }))
        }),
        ...(deletedIds && deletedIds.length > 0 && { delete: deletedIds })
      };

      try {
        if ((isOgdFiling || params.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_OGD) && ogdFiling) {
          const formatOgdFiling: SaveOgdFilingParams = {
            ...emptyStringToNull(ogdFiling),
            emissionProgram:
              ogdFiling.ecccSubType === ECCCSubType.EMISSIONS ? ogdFiling.emissionProgram! : undefined,
            wildlifeCompliance:
              ogdFiling.ecccSubType === ECCCSubType.WILDLIFE ? ogdFiling.wildlifeCompliance! : undefined,
            ogdId: isCreate ? (formatParams.sourceId ?? undefined) : (ogdFiling.ogdId ?? undefined),
            isExcluded: ogdFiling.isExcluded || false
          };

          if (isCreate) {
            const filing = await createOgdFiling.mutateAsync({
              ogdFiling: formatOgdFiling as CreateOgdFilingDto,
              matchingRule: formatParams as CreateMatchingRuleDto,
              matchingConditions: formatConditions
            });

            await handleUpdateStatus(MatchingRuleStatus.ACTIVE, filing.matchingRule.id);

            navigate(
              state?.originalFrom
                ? state?.originalFrom
                : state?.from
                  ? state.from
                  : CompliancePages.Detail(String(filing.matchingRule.id)),
              {
                replace: true
              }
            );
          } else {
            await editOgdFiling.mutateAsync({
              id: ogdFiling.id as number,
              ruleId: formatParams.id as number,
              ogdFiling: formatOgdFiling,
              matchingRule: formatParams,
              matchingConditions: formatBatchParams
            });
          }
        } else if (
          (isSimaFiling || params.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE) &&
          simaFiling
        ) {
          const formatSimaFiling: SaveSimaFilingParams = {
            ...emptyStringToNull(simaFiling),
            simaCodeId: isCreate
              ? (formatParams.sourceId ?? undefined)
              : (simaFiling.simaCodeId ?? formatParams.sourceId ?? undefined),
            incoterms:
              simaFiling.subjectCode !== SimaSubjectCode.SUBJECT ? null : (simaFiling.incoterms ?? undefined),
            security:
              simaFiling.subjectCode !== SimaSubjectCode.SUBJECT ? false : (simaFiling.security ?? false)
          };

          if (isCreate) {
            const filing = await createSimaFiling.mutateAsync({
              simaFiling: formatSimaFiling as CreateSimaFilingDto,
              matchingRule: formatParams as CreateMatchingRuleDto,
              matchingConditions: formatConditions
            });

            await handleUpdateStatus(MatchingRuleStatus.ACTIVE, filing.matchingRule.id);

            navigate(
              state?.originalFrom
                ? state?.originalFrom
                : state?.from
                  ? state.from
                  : CompliancePages.Detail(String(filing.matchingRule.id)),
              {
                replace: true
              }
            );
          } else {
            await editSimaFiling.mutateAsync({
              id: simaFiling.id as number,
              ruleId: formatParams.id as number,
              simaFiling: formatSimaFiling,
              matchingRule: formatParams,
              matchingConditions: formatBatchParams
            });
          }
        } else {
          const rule = await saveMatchingRule.mutateAsync(formatParams);

          await saveMatchingCondition.mutateAsync({
            ...formatBatchParams,
            id: params.id ?? rule.id
          });

          if (isCreate) {
            await handleUpdateStatus(MatchingRuleStatus.ACTIVE, rule.id);

            navigate(state?.from ? state.from : CompliancePages.Detail(String(rule.id)), {
              replace: true
            });
          }
        }

        if (!isCreate) await handleUpdateStatus(MatchingRuleStatus.ACTIVE, formatParams.id as number);

        const message = `Matching Rule ${params.name} ${params.id ? "updated" : "created"} successfully`;
        toast.success(message);
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [
      isSuperAdmin,
      isCreate,
      organization,
      isOgdFiling,
      isSimaFiling,
      createOgdFiling,
      navigate,
      state?.originalFrom,
      state?.from,
      editOgdFiling,
      createSimaFiling,
      editSimaFiling,
      saveMatchingRule,
      saveMatchingCondition,
      handleUpdateStatus
    ]
  );

  const handleDeleteMatchingRule = useCallback(async () => {
    try {
      await deleteMatchingRule.mutateAsync({ id: info?.id });

      if (isOgdFiling) await deleteOgdFiling.mutateAsync({ id: info?.sourceRecord?.id });

      toast.success("Matching Rule deleted successfully");

      navigate(-1);
    } catch (error) {
      toast.error((error as BaseError).message);
    }
  }, [deleteMatchingRule, deleteOgdFiling, info?.id, info?.sourceRecord?.id, isOgdFiling, navigate]);

  const handleDelete = () => {
    showPopup(
      {
        content: "Are you sure you want to delete this rule?",
        onProceed: () => {
          handleDeleteMatchingRule();
          hidePopup();
        }
      },
      Popup.PopupType.DELETE_POPUP
    );
  };
  //#endregion

  //#region Formik
  const formik = useFormik<MatchingRuleFormParams>({
    initialValues: {
      id: info?.id,
      name: info?.name || "",
      priority: info?.priority,
      description: info?.description || "",
      sourceTable: info?.sourceTable,
      sourceRecord: isOgdFiling
        ? ((info?.sourceRecord as OgdFiling)?.ogd as SourceTable)
        : isSimaFiling
          ? (((info?.sourceRecord as SimaFiling)?.simaCode as SourceTable) ?? undefined)
          : (info?.sourceRecord as SourceTable),
      sourceId: info?.sourceId,
      destinationTable: info?.destinationTable || MatchingRuleDestinationDatabaseTable.PRODUCT,
      expiryDate: info?.expiryDate,
      operator: info?.operator,
      conditions: info?.conditions.map((condition) => ({
        ...condition,
        value: condition.value ?? undefined,
        inValue: operatorIsArray(condition.operator)
          ? (condition.valueString ??
            condition.valueInteger ??
            condition.valueFloat ??
            condition.valueDateTime ??
            condition.valueBoolean ??
            null)
          : null
      })) || [NEW_CONDITION],
      deletedIds: [],
      ogdFiling: {
        id: isOgdFiling ? info?.sourceRecord?.id : undefined,
        extensionCode: isOgdFiling ? ((info?.sourceRecord as OgdFiling)?.extensionCode ?? "") : "",
        endUse: isOgdFiling ? ((info?.sourceRecord as OgdFiling)?.endUse ?? "") : "",
        airsType: isOgdFiling ? ((info?.sourceRecord as OgdFiling)?.airsType ?? "") : "",
        airsReferenceNumber: isOgdFiling
          ? ((info?.sourceRecord as OgdFiling)?.airsReferenceNumber ?? "")
          : "",
        productCategory: isOgdFiling ? ((info?.sourceRecord as OgdFiling)?.productCategory ?? "") : "",
        generalImportPermit: isOgdFiling
          ? ((info?.sourceRecord as OgdFiling)?.generalImportPermit ?? undefined)
          : undefined,
        brandName: isOgdFiling ? ((info?.sourceRecord as OgdFiling)?.brandName ?? "") : "",
        manufacturerId: isOgdFiling ? (info?.sourceRecord as OgdFiling)?.manufacturer?.id : undefined,
        tireType: isOgdFiling ? ((info?.sourceRecord as OgdFiling)?.tireType ?? undefined) : undefined,
        tireSize: isOgdFiling ? ((info?.sourceRecord as OgdFiling)?.tireSize ?? undefined) : undefined,
        tireCompliance: isOgdFiling
          ? ((info?.sourceRecord as OgdFiling)?.tireCompliance ?? undefined)
          : undefined,
        tireClass: isOgdFiling ? ((info?.sourceRecord as OgdFiling)?.tireClass ?? undefined) : undefined,
        ecccSubType: isOgdFiling ? ((info?.sourceRecord as OgdFiling)?.ecccSubType ?? undefined) : undefined,
        wildlifeCompliance: isOgdFiling
          ? ((info?.sourceRecord as OgdFiling)?.wildlifeCompliance ?? undefined)
          : undefined,
        emissionProgram: isOgdFiling
          ? ((info?.sourceRecord as OgdFiling)?.emissionProgram ?? undefined)
          : undefined,
        nrcanSubType: isOgdFiling
          ? ((info?.sourceRecord as OgdFiling)?.nrcanSubType ?? undefined)
          : undefined,
        isRegulated: isOgdFiling ? ((info?.sourceRecord as OgdFiling)?.isRegulated ?? false) : false,
        isExcluded: isOgdFiling ? ((info?.sourceRecord as OgdFiling)?.isExcluded ?? false) : false,
        ogdId: isOgdFiling ? (info?.sourceRecord as OgdFiling)?.ogd?.id : undefined
      },
      simaFiling: {
        id: isSimaFiling ? info?.sourceRecord?.id : undefined,
        subjectCode: isSimaFiling
          ? ((info?.sourceRecord as SimaFiling)?.subjectCode ?? undefined)
          : undefined,
        measureInForceId: isSimaFiling
          ? ((info?.sourceRecord as SimaFiling)?.measureInForce?.id ?? undefined)
          : undefined,
        incoterms: isSimaFiling ? ((info?.sourceRecord as SimaFiling)?.incoterms ?? undefined) : undefined,
        security: isSimaFiling ? (info?.sourceRecord as SimaFiling)?.security : false,
        simaCodeId: isSimaFiling ? ((info?.sourceRecord as SimaFiling)?.simaCode?.id ?? undefined) : undefined
      }
    },
    validationSchema: saveMatchingRuleSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit: handleSaveMatchingRule
  });
  const { setFieldValue } = formik;
  //#endregion

  //#region Listeners
  // Check if the rule org match the assumed org
  useEffect(() => {
    if (info && organization) {
      if (organization.id !== info.organization?.id) navigate(-1);
    }
  }, [info, organization, navigate]);

  // Set source when sourceRecord exist
  useEffect(() => {
    if (info?.sourceRecord) {
      if (isOgdFiling) {
        setSource((info.sourceRecord as OgdFiling)?.ogd as SourceTable);
        setTireManufacturer((info.sourceRecord as OgdFiling)?.manufacturer ?? undefined);
      } else if (isSimaFiling) {
        setSource((info.sourceRecord as SimaFiling)?.simaCode as SourceTable);
        setMeasureInForce((info.sourceRecord as SimaFiling)?.measureInForce);
      } else {
        setSource(info.sourceRecord as SourceTable);
      }
    }
  }, [info?.sourceRecord, isOgdFiling, isSimaFiling]);

  // set seource when creating rule from product/ci
  useEffect(() => {
    if (state) {
      setFieldValue("sourceTable", state.sourceTable);
      setFieldValue("sourceRecord", state.sourceRecord);
      setFieldValue("sourceId", state.sourceRecord?.id);
      if (state.measureInForce) {
        setFieldValue("simaFiling.measureInForceId", state.measureInForce.id);
        setMeasureInForce(state.measureInForce);
      }
      if (state.partner) {
        setTireManufacturer(state.partner);
        setFieldValue("ogdFiling.manufacturerId", state.partner.id);
      }
      if (state.organization && organization && state.organization.id !== organization.id) {
        navigate(-1);
      }
      if (state.product) {
        setFieldValue("conditions", [
          {
            ...NEW_CONDITION,
            attribute: ProductAttribute.PRODUCT_ID,
            value: state.product.id?.toString()
          }
        ]);
      }
      if (state.products) {
        setFieldValue("priority", 0);
        setFieldValue("conditions", [
          {
            ...NEW_CONDITION,
            attribute: ProductAttribute.HS_CODE,
            operator: MatchingConditionOperator.IN,
            value: JSON.stringify(state.products.map((p: Product) => p.hsCode)),
            inValue: state.products.map((p: Product) => p.hsCode)
          }
        ]);
      }
      setSource(state.sourceRecord);
    }
  }, [navigate, organization, setFieldValue, state]);

  const generateRuleName = useCallback(() => {
    let _source = formik.values.sourceTable as string;
    const _record = formik.values.sourceRecord;
    const _conditions = formik.values.conditions;

    if (!_source || !_conditions?.length) return;

    _source =
      _source +
      (formik.values.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_OGD ||
      formik.values.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE
        ? "Filing"
        : "");

    const sourceIdentifier = _record
      ? [_record.case, _record.agency, _record.hsCode, _record.code, _record.id].find((val) => val) +
          (formik.values.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_OGD
            ? ` ${_record.program ?? ""}`
            : "") || ""
      : "";

    const conditionsText = _conditions
      .filter((c) => c.value)
      .map((c) => {
        const value = c.isOperationInverted ? `NOT ${c.value}` : c.value;
        return `${pascalToCapitalize(c.attribute)} ${c.operator} ${value}`;
      })
      .join(" AND ");

    const ruleName = `${_source} - ${sourceIdentifier} - ${conditionsText}`.trim();
    setFieldValue("name", ruleName);
  }, [formik.values.conditions, formik.values.sourceRecord, formik.values.sourceTable, setFieldValue]);

  useEffect(() => {
    if (isCreate) {
      generateRuleName();
    }
  }, [
    formik.values.sourceTable,
    formik.values.sourceRecord,
    formik.values.conditions,
    generateRuleName,
    isCreate
  ]);
  //#endregion

  return (
    <div>
      {sourceModal && (
        <SelectSourceModal
          show={!!sourceModal}
          source={sourceModal}
          onClose={() => setSourceModal(undefined)}
          onSelect={(data) => {
            if (measureInForceModal) {
              setMeasureInForce(data);
              setFieldValue("simaFiling.measureInForceId", data?.id);
              toggleMeasureInForceModal();
            } else {
              setSource(data);
              setFieldValue("sourceId", data?.id);
              setFieldValue("sourceRecord", data);
            }
            setSourceModal(undefined);
          }}
          hsCodeLength="4"
          canSelect={(item) => {
            if (sourceModal === MatchingRuleSourceDatabaseTable.CANADA_TARIFF)
              return item.hsCode.startsWith("99");

            return true;
          }}
        />
      )}

      <ComplianceHeader
        info={info}
        isCreate={isCreate}
        isLoading={isLoading}
        readOnly={!canCrudBackoffice}
        from={state?.from}
        onSubmit={formik.handleSubmit}
        onUpdateStatus={handleUpdateStatus}
        onDelete={handleDelete}
      />

      <Card>
        <Breadcrumbs />

        <div className="p-4">
          <form className="flex flex-col gap-2">
            <fieldset
              disabled={info?.status === MatchingRuleStatus.ACTIVE || info?.isGlobal || isFetching}
              className="flex flex-col divide-y gap-2"
            >
              <div className="flex gap-3">
                <Input
                  label="Rule Name"
                  placeholder="Input name"
                  containerStyle="flex-1"
                  {...formikInputOpts(formik, "name")}
                />
                <Select
                  label="Priority"
                  options={PRIORITY}
                  {...formikInputOpts(formik, "priority")}
                  info="10 is the highest priority"
                  optional
                />
                <Input
                  label="Expiry Date"
                  placeholder="Input Expiry Date"
                  type="date"
                  {...formikInputOpts(formik, "expiryDate")}
                />
                <Textarea
                  label="Rule Description"
                  placeholder="Input description"
                  containerStyle="flex-1"
                  {...formikInputOpts(formik, "description")}
                />
              </div>

              <div className="pt-2 flex flex-col gap-2">
                <div>
                  <h5 className="mb-1">Source</h5>
                  <div className="flex gap-3">
                    <Select
                      label="Source Table"
                      containerStyle="flex-1"
                      options={SOURCE_TABLE}
                      {...formikInputOpts(formik, "sourceTable", (e) => {
                        formik.handleChange(e);
                        setFieldValue("sourceId", undefined);
                        setSource(undefined);
                      })}
                      optional
                      disabled={isOgdFiling || isSimaFiling || Boolean(state)}
                    />
                    <Input
                      label="Source Record"
                      name="sourceId"
                      placeholder="Select source table first"
                      kind="search"
                      containerStyle="flex-1"
                      value={
                        source
                          ? (source.displayName ??
                            source.case ??
                            source.agency ??
                            source.hsCode ??
                            source.code ??
                            source.ogd?.agency)
                          : ""
                      }
                      error={getFormikError(formik, "sourceId")}
                      disabled={!formik.values.sourceTable || isOgdFiling || Boolean(state?.sourceRecord)}
                      readOnly
                      onClick={() =>
                        setSourceModal(
                          isSimaFiling
                            ? MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE
                            : formik.values.sourceTable
                        )
                      }
                    />
                    <Select label="Destination Table" options={DESTINATION_TABLE} disabled />
                  </div>
                </div>

                <OgdFilingSection
                  formik={formik}
                  sourceRecord={source}
                  isCreate={isCreate}
                  tireManufacturer={tireManufacturer}
                />

                <SimaFilingSection
                  formik={formik}
                  sourceRecord={source}
                  isCreate={isCreate}
                  measureInForce={measureInForce}
                  state={state}
                  onSelectMeasureInForce={() => {
                    setSourceModal(MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING);
                    toggleMeasureInForceModal();
                  }}
                />

                <RuleDetails source={source} />

                <ProductDetails product={state?.product} />

                <ProductList products={state?.products} />
              </div>

              <ConditionsSection
                formik={formik}
                info={info}
                isFiling={isOgdFiling || isSimaFiling || Boolean(state)}
              />
            </fieldset>

            <MatchingEntriesSection formik={formik} />
          </form>
        </div>
      </Card>
    </div>
  );
});
export default ComplianceDetail;
