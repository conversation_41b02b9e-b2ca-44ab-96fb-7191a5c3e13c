import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { useColumnVisibility } from "@/modules/Settings/Hooks.settings";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { useLocation, useNavigate } from "react-router-dom";
import { Button, ColumnSelector, Header, Pagination, Table } from "ui";
import { SortOrder } from "utils";
import { ComplianceFilter } from "../../components";
import { COMPLIANCE_TABLE_KEY } from "../../Constant.compliance";
import { useGetMatchingRuleList } from "../../queries";
import { CompliancePages } from "../../Routes.compliance";
import { complianceRuleTableSchema } from "../../Schema.compliance";
import { GetMatchingRuleList, MatchingRule, MatchingRuleColumn } from "../../Types.compliance";
import { useStore } from "@/bootstrap/Store.bootstrap";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";

const ComplianceList = observer(() => {
  const { isSuperAdmin, organization } = useStore().auth;
  const navigate = useNavigate();
  const { state } = useLocation();
  const [filter, toggleFilter] = useReducer((r) => !r, !!state?.product);
  const [filters, setFilters] = useState<GetMatchingRuleList>();
  const [page, setPage] = useState(1);
  const { canCrudBackoffice } = useBackofficePermission();

  const { visibleColumns, handleToggleColumn } = useColumnVisibility({
    defaultColumns: complianceRuleTableSchema,
    key: COMPLIANCE_TABLE_KEY
  });

  const { data, error, isFetching } = useGetMatchingRuleList(
    {
      page,
      limit: DEFAULT_LIMIT,
      sortBy: MatchingRuleColumn.createDate,
      sortOrder: SortOrder.DESC,
      isGlobal: false,
      ...filters
    },
    isSuperAdmin ? organization?.id : undefined
  );

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const handleFilter = useCallback((values: GetMatchingRuleList) => {
    setFilters(values);
    setPage(1);
  }, []);

  return (
    <div className="flex flex-col">
      <Header title="Compliance Rule List">
        <Button label="Filters" kind="filter" onClick={toggleFilter} />
        <ColumnSelector columns={visibleColumns} onToggleColumn={handleToggleColumn} />
        {canCrudBackoffice && (
          <Button
            label="New Rule"
            kind="create"
            className="ml-auto"
            onClick={() => navigate(CompliancePages.Detail("create"))}
          />
        )}
      </Header>

      <section>
        <ComplianceFilter filterValues={handleFilter} isOpen={filter} product={state?.product} />
      </section>

      <main>
        <Table
          data={data?.matchingRules}
          isLoading={isFetching}
          schema={visibleColumns}
          onClick={(_data: MatchingRule) => {
            navigate(CompliancePages.Detail(String(_data.id)));
          }}
          onSort={(key?: string, direction?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key as MatchingRuleColumn,
              sortOrder: direction
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
          footer={
            <Pagination currentPage={page} total={data?.total} limit={DEFAULT_LIMIT} onPageChange={setPage} />
          }
          total={data?.total}
        />
      </main>
    </div>
  );
});
export default ComplianceList;
