import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import ComplianceService from "../services";
import type {
  GetQueryResultParams,
  SaveMatchingRuleParams,
  SearchMatchingEntriesParams,
  UpdateMatchingRuleStatusParams
} from "../Types.compliance";

const useSaveMatchingRule = () =>
  useMutation({
    mutationFn: async (params: SaveMatchingRuleParams) => {
      if (params.id) {
        return await ComplianceService.MatchingRule.edit(params);
      }
      return await ComplianceService.MatchingRule.create(params);
    }
  });

const useDeleteMatchingRule = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await ComplianceService.MatchingRule.remove(params)
  });

const useQueryMatchingRule = () =>
  useMutation({
    mutationFn: async (params: GetQueryResultParams) => await ComplianceService.MatchingRule.query(params)
  });

const useSearchMatchingEntries = () =>
  useMutation({
    mutationFn: async (params: SearchMatchingEntriesParams) =>
      await ComplianceService.MatchingRule.search(params)
  });

const useUpdateMatchingRuleStatus = () =>
  useMutation({
    mutationFn: async (params: UpdateMatchingRuleStatusParams) =>
      await ComplianceService.MatchingRule.updateStatus(params)
  });

export {
  useDeleteMatchingRule,
  useQueryMatchingRule,
  useSaveMatchingRule,
  useSearchMatchingEntries,
  useUpdateMatchingRuleStatus
};
