import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import ComplianceService from "../services";
import type {
  CreateOgdFilingWithRuleParams,
  EditOgdFilingWithRuleParams,
  SaveOgdFilingParams
} from "../Types.compliance";

const useSaveOgdFiling = () =>
  useMutation({
    mutationFn: async (params: SaveOgdFilingParams) => {
      if (params.id) {
        return await ComplianceService.OgdFiling.edit(params);
      }
      return await ComplianceService.OgdFiling.create(params);
    }
  });

const useDeleteOgdFiling = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await ComplianceService.OgdFiling.remove(params)
  });

const useCreateOgdFilingWithRule = () =>
  useMutation({
    mutationFn: async (params: CreateOgdFilingWithRuleParams) =>
      await ComplianceService.OgdFiling.createWithRule(params)
  });

const useEditOgdFilingWithRule = () =>
  useMutation({
    mutationFn: async (params: EditOgdFilingWithRuleParams) =>
      await ComplianceService.OgdFiling.editWithRule(params)
  });

export { useCreateOgdFilingWithRule, useDeleteOgdFiling, useEditOgdFilingWithRule, useSaveOgdFiling };
