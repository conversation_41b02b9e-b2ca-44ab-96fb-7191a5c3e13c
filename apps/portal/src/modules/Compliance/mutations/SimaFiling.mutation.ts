import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import ComplianceService from "../services";
import type {
  CreateSimaFilingWithRuleParams,
  EditSimaFilingWithRuleParams,
  SaveSimaFilingParams
} from "../Types.compliance";

const useSaveSimaFiling = () =>
  useMutation({
    mutationFn: async (params: SaveSimaFilingParams) => {
      if (params.id) {
        return await ComplianceService.SimaFiling.edit(params);
      }
      return await ComplianceService.SimaFiling.create(params);
    }
  });

const useDeleteSimaFiling = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await ComplianceService.SimaFiling.remove(params)
  });

const useCreateSimaFilingWithRule = () =>
  useMutation({
    mutationFn: async (params: CreateSimaFilingWithRuleParams) =>
      await ComplianceService.SimaFiling.createWithRule(params)
  });

const useEditSimaFilingWithRule = () =>
  useMutation({
    mutationFn: async (params: EditSimaFilingWithRuleParams) =>
      await ComplianceService.SimaFiling.editWithRule(params)
  });

export { useCreateSimaFilingWithRule, useDeleteSimaFiling, useEditSimaFilingWithRule, useSaveSimaFiling };
