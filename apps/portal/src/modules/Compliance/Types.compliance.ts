import { FormikProps } from "formik";
import type {
  BatchUpdateMatchingConditionsDto,
  CanadaAntiDumping,
  CanadaExciseTaxCode,
  CanadaGstExemptCode,
  CanadaOgd,
  CanadaSimaCode,
  CanadaTariff,
  CanadaTreatmentCode,
  CanadaVfdCode,
  CreateMatchingConditionDto,
  CreateOgdFilingAndMatchingRuleDto,
  CreateOrEditOgdFilingAndMatchingRuleResponseDto,
  CreateOrEditSimaFilingAndMatchingRuleResponseDto,
  CreateSimaFilingAndMatchingRuleDto,
  EditMatchingRuleDto,
  EditOgdFilingAndMatchingRuleDto,
  EditOgdFilingDto,
  EditSimaFilingAndMatchingRuleDto,
  EditSimaFilingDto,
  GetMatchingConditionsDto,
  GetMatchingConditionsResponseDto,
  GetMatchingRulesDto,
  GetMatchingRulesResponseDto,
  MatchingCondition as MC,
  MatchingRule as MR,
  OgdFiling,
  QueryMatchingResultsDto,
  QueryMatchingResultsResponseDto,
  QueryMatchingResultsWithDestinationIdResponseDto,
  QueryMultipleMatchingResultsDto,
  ReverseQueryMatchingResultsDto,
  ReverseQueryMatchingResultsResponseDto,
  SimaFiling,
  UpdateMatchingRuleStatusDto
} from "nest-modules";
import type {
  DetailParams,
  ListParams,
  Nullable,
  PaginationParams,
  PaginationResponse,
  SortOrder,
  WithAuditNames
} from "utils";

export type IssueComplianceTable<T> = T & {
  hasIssue?: boolean;
  issueDetails?: string;
};

//#region Matching rule
export enum MatchingRuleColumn {
  id = "id",
  status = "status",
  priority = "priority",
  name = "name",
  description = "description",
  sourceTable = "sourceTable",
  sourceId = "sourceId",
  destinationTable = "destinationTable",
  createdById = "createdById",
  createDate = "createDate",
  lastEditedById = "lastEditedById",
  lastEditDate = "lastEditDate"
}

export enum MatchingRuleStatus {
  PENDING = "pending",
  ACTIVE = "active",
  DISABLED = "disabled"
}

export enum MatchingRuleOperator {
  AND = "and",
  OR = "or"
}

export enum MatchingRuleSourceDatabaseTable {
  CANADA_ANTI_DUMPING = "CanadaAntiDumping",
  CANADA_OGD = "CanadaOgd",
  CANADA_SIMA_CODE = "CanadaSimaCode",
  CANADA_TARIFF = "CanadaTariff",
  CANADA_TREATMENT_CODE = "CanadaTreatmentCode",
  CANADA_VFD_CODE = "CanadaVfdCode",
  CANADA_GST_EXEMPT_CODE = "CanadaGstExemptCode",
  CANADA_EXCISE_TAX_CODE = "CanadaExciseTaxCode",
  OGD_FILING = "OgdFiling",
  SIMA_FILING = "SimaFiling",
  CERTIFICATE_OF_ORIGIN = "CertificateOfOrigin"
}

export enum MatchingRuleDestinationDatabaseTable {
  PRODUCT = "Product"
}

export type SourceTable = CanadaAntiDumping &
  CanadaOgd &
  CanadaSimaCode &
  CanadaTariff &
  CanadaTreatmentCode &
  CanadaVfdCode &
  CanadaGstExemptCode &
  OgdFiling &
  CanadaExciseTaxCode;

export enum MatchingConditionOperator {
  EQUALS = "equals",
  CONTAINS = "contains",
  STARTS_WITH = "starts-with",
  ENDS_WITH = "ends-with",
  GREATER_THAN = "greater-than",
  LESS_THAN = "less-than",
  GREATER_THAN_OR_EQUAL_TO = "greater-than-or-equal-to",
  LESS_THAN_OR_EQUAL_TO = "less-than-or-equal-to",
  IN = "in",
  BETWEEN_INCLUSIVE = "between-inclusive",
  BETWEEN_EXCLUSIVE = "between-exclusive"
}

export type MatchingRule = MR;
export type MatchingCondition = MC;

export type MatchingRuleTable = MatchingRule &
  WithAuditNames & {
    sourceName?: string | number;
  };

export type GetMatchingRuleList = ListParams<GetMatchingRulesDto>;
export type GetMatchingRuleListResponse = PaginationResponse & GetMatchingRulesResponseDto;

export type SaveMatchingRuleParams = EditMatchingRuleDto & DetailParams;

export type GetMatchingConditionList = ListParams<GetMatchingConditionsDto>;
export type GetMatchingConditionListResponse = PaginationResponse & GetMatchingConditionsResponseDto;

export type SaveMatchingConditionParams = CreateMatchingConditionDto &
  DetailParams & {
    valueRecord?: unknown;
    inValue?: string[] | number[] | boolean[] | Date[] | null;
  };

export type BatchSaveMatchingConditionParams = BatchUpdateMatchingConditionsDto & DetailParams;

export type MatchingRuleFormParams = SaveMatchingRuleParams & {
  conditions: SaveMatchingConditionParams[];
  deletedIds?: number[];
  ogdFiling?: Nullable<SaveOgdFilingParams>;
  simaFiling?: Nullable<SaveSimaFilingParams>;
  sourceRecord?: SourceTable;
};
export type ComplianceFormState = {
  sourceRecord?: SourceTable;
};
export type ComplianceFormikProps = FormikProps<MatchingRuleFormParams>;

export type GetSourceTableParams = Partial<PaginationParams> & {
  url?: string;
  code?: string;
  hsCode?: string;
  description?: string;
  agency?: CanadaGovernmentAgency;
  type?: "4" | "10";
  sortBy?: string;
  sortOrder?: SortOrder;
};
export type GetSourceTableResponse<T> = PaginationResponse & {
  // TODO: Remove this once we generalize the response
  data: T[];
};
export type GetQueryResultParams = QueryMatchingResultsDto & DetailParams;
export type GetQueryResultResponse = QueryMatchingResultsResponseDto;

export type GetQueryMultipleResultParams = Partial<QueryMultipleMatchingResultsDto>;
export type GetQueryMultipleResultResponse = QueryMatchingResultsWithDestinationIdResponseDto[];

export type SearchMatchingEntriesParams = ReverseQueryMatchingResultsDto;
export type SearchMatchingEntriesResponse = ReverseQueryMatchingResultsResponseDto;

export type UpdateMatchingRuleStatusParams = UpdateMatchingRuleStatusDto & DetailParams;
//#endregion

//#region OGD FIling
export enum CanadaGovernmentAgency {
  CFIA = "cfia",
  CNSC = "cnsc",
  DFO = "dfo",
  ECCC = "eccc",
  GAC = "gac",
  HC = "hc",
  PHAC = "phac",
  NRCAN = "nrcan",
  TC = "tc"
}
export const CFIAAirsType = {
  893: "Safe Food for Canadians License"
};
export enum HCEndUse {
  HC01 = "Generic - Human Therapeutic Use",
  HC02 = "Generic - Special Access",
  HC03 = "Medical Devices - Investigational Testing",
  HC04 = "Medical Devices - Custom Made",
  HC05 = "Generic - Human Clinical Trial Use",
  HC06 = "Pesticides - Manufacture",
  HC07 = "Generic – Research & Development",
  HC08 = "Pesticides - Own Use",
  HC09 = "Pesticides - Resale",
  HC10 = "Generic - Veterinary Therapeutic Use",
  HC11 = "Veterinary Drugs - Veterinary Experimental Study",
  HC12 = "Veterinary Drugs - Veterinary Clinical Study",
  HC13 = "Generic - Manufacturing or Industrial Use",
  HC14 = "Veterinary Drugs - Veterinary Emergency Drug Release (EDR)",
  HC15 = "Office of Controlled Substances - Research or Scientific use",
  HC16 = "Office of Controlled Substances - Agricultural Use",
  HC17 = "Office of Controlled Substances - Processing",
  HC18 = "Office of Controlled Substances - Non-viable seed/grain",
  HC19 = "Office of Controlled Substances - Human Consumption (food use)",
  HC20 = "Office of Controlled Substances - Substances in a Final Product",
  HC21 = "Consumer Products - For Sale or Distribution",
  HC22 = "Consumer Products - Educational Purposes",
  HC23 = "Consumer Products - Resale",
  HC24 = "Consumer Products - Charitable Purposes",
  HC26 = "Consumer Products - Repair Purposes",
  HC27 = "Consumer Products - For Immediate Re-Exportation",
  HC28 = "Office of Controlled Substances – Resale",
  HC29 = "Generic – Other",
  HC30 = "Medical Devices – Trade Shows/Exhibitions",
  HC31 = "Urgent Public Health Need"
}
export enum HCProductCategory {
  HC01 = "Active Pharmaceutical Ingredients - Active Pharmaceutical Ingredients",
  HC02 = "Blood & Blood Components - Blood & Blood Components",
  HC04 = "Donor Semen - Donor Semen",
  HC05 = "Human Drugs - Human Drug other than Radiopharmaceutical",
  HC06 = "Human Drugs - Radiopharmaceutical",
  HC07 = "Human Drugs - Phase I Clinical Trial Drug",
  HC08 = "Human Drugs - Phase II Clinical Trial Drug",
  HC09 = "Human Drugs - Phase III Clinical Trial Drug",
  HC10 = "Human Drugs - Phase IV Clinical Trial Drug",
  HC11 = "Medical Devices - Class 1 Medical Device",
  HC12 = "Medical Devices - Class 2 Medical Device",
  HC13 = "Medical Devices - Class 3 Medical Device",
  HC14 = "Medical Devices - Class 4 Medical Device",
  HC15 = "Natural Health Products - Natural Health Product",
  HC16 = "Veterinary Drugs - Veterinary Drug",
  HC17 = "Veterinary Drugs - Veterinary Drug-Low Risk",
  HC18 = "Office of Controlled Substances - Medical Marihuana",
  HC19 = "Office of Controlled Substances - Narcotics",
  HC20 = "Office of Controlled Substances - Controlled Drugs",
  HC21 = "Office of Controlled Substances - Restricted Drugs",
  HC22 = "Office of Controlled Substances - Benzodiazepine",
  HC23 = "Office of Controlled Substances - Industrial Hemp, Seed/Grain",
  HC24 = "Office of Controlled Substances - Class A - Precursor",
  HC25 = "Office of Controlled Substances - Class B - Precursor",
  HC26 = "Cells, Tissues & Organs - Cells",
  HC27 = "Cells, Tissues & Organs - Tissues",
  HC28 = "Cells, Tissues & Organs - Organs",
  HC29 = "Consumer Products - Consumer Product for Infants (0-18 months)",
  HC30 = "Consumer Products - Consumer Product for Toddlers (19-36 months)",
  HC31 = "Consumer Products - Consumer Product for Children (3-6 years)",
  HC32 = "Consumer Products - Consumer Product for Children (6-8 years)",
  HC33 = "Consumer Products - Consumer Product for Children (8-12 years)",
  HC34 = "Consumer Products - Consumer Product for People Aged 13+ years",
  HC35 = "Consumer Products - Consumer Chemical",
  HC36 = "Consumer Products - Cosmetic",
  HC37 = "Consumer Products - Consumer Product (for all ages)",
  HC38 = "Pesticides - Registered Pest Control Product (other than a device)",
  HC39 = "Pesticides - Registered Device",
  HC40 = "Radiation Emitting Devices - Tanning Equipment",
  HC41 = "Radiation Emitting Devices - X-ray Device",
  HC42 = "Radiation Emitting Devices – Ultrasound Therapy Device",
  HC43 = "Radiation Emitting Devices - Microwave Oven",
  HC44 = "Radiation Emitting Devices - Laser",
  HC45 = "Radiation Emitting Devices - Other"
}
export enum GeneralImportPermit {
  GIP80 = "gip80",
  GIP81 = "gip81",
  GIP83 = "gip83"
}
export enum TCTireEndUse {
  TC01 = "Sale",
  TC02 = "Export",
  TC03 = "Not Regulated",
  TC04 = "For Retread",
  TC05 = "Scrap Tire"
}
export enum TCTireClass {
  TC01 = "New",
  TC02 = "Retreaded",
  TC03 = "Used"
}
export enum TCTireType {
  TC04 = "On-Road",
  TC05 = "Off-Road"
}
export enum TCTireSize {
  TC06 = "Below 14 in",
  TC07 = "14 in",
  TC08 = "15 in",
  TC09 = "16 in",
  TC10 = "17 in",
  TC11 = "18 in",
  TC12 = "19 in",
  TC13 = "Above 19 in"
}
export enum TCTireCompliance {
  TC01 = "New/Retreaded Tires from All Countries",
  TC02 = "New/Retreaded Tires from USA",
  TC07 = "Used Tires from All Countries",
  TC08 = "Used Tires from USA"
}
export enum ECCCSubType {
  EMISSIONS = "Emissions",
  WILDLIFE = "Wildlife"
}
export enum ECCCWildlifeCompliance {
  ENDANGERED = "EC17",
  NOT_ENDANGERED = "EC18"
}
export enum ECCCEmissionProgram {
  EXEMPT = "XE99"
}
export enum NRCANSubType {
  ENERGY_EFFICIENCY = "EnergyEfficiency",
  EXPLOSIVES = "Explosives"
}

export enum OgdFilingColumn {
  id = "id",
  isExcluded = "isExcluded",
  extensionCode = "extensionCode",
  endUse = "endUse",
  airsType = "airsType",
  airsReferenceNumber = "airsReferenceNumber",
  productCategory = "productCategory",
  generalImportPermit = "generalImportPermit",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  ogdId = "ogdId",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}
export type OgdFilingTable = OgdFiling & {
  agencyName?: string;
  ogdName?: string;
  manufacturerName?: string;
};
export type SaveOgdFilingParams = EditOgdFilingDto & DetailParams;
export type CreateOgdFilingWithRuleParams = CreateOgdFilingAndMatchingRuleDto;
export type EditOgdFilingWithRuleParams = EditOgdFilingAndMatchingRuleDto & DetailParams & { ruleId: number };
export type EditOgdFilingWithRuleResponse = CreateOrEditOgdFilingAndMatchingRuleResponseDto;
//#endregion

//#region SIMA Filing
export enum SimaSubjectCode {
  SUBJECT = "S",
  NON_SUBJECT = "N",
  UNDERTAKING = "U"
}
export enum SimaIncoterms {
  CFR = "CFR",
  CIF = "CIF",
  CIP = "CIP",
  CPT = "CPT",
  DAP = "DAP",
  DAT = "DAT",
  DDP = "DDP",
  DPU = "DPU",
  EWX = "EWX",
  FAS = "FAS",
  FCA = "FCA",
  FOB = "FOB"
}

export type SimaFilingTable = SimaFiling & {
  simaCode?: number;
  case?: string;
  caseType?: string;
};
export type SaveSimaFilingParams = EditSimaFilingDto & DetailParams;
export type CreateSimaFilingWithRuleParams = CreateSimaFilingAndMatchingRuleDto;
export type EditSimaFilingWithRuleParams = EditSimaFilingAndMatchingRuleDto &
  DetailParams & { ruleId: number };
export type EditSimaFilingWithRuleResponse = CreateOrEditSimaFilingAndMatchingRuleResponseDto;
//#endregion
