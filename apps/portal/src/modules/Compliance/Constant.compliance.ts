import { enumToSelectOptions, schemaToSelectOptions } from "utils";
import { ProductAttribute } from "../Product/Types.product";
import { complianceRuleTableSchema } from "./Schema.compliance";
import {
  CanadaGovernmentAgency,
  CFIAAirsType,
  ECCCEmissionProgram,
  ECCCSubType,
  ECCCWildlifeCompliance,
  GeneralImportPermit,
  HCEndUse,
  HCProductCategory,
  MatchingConditionOperator,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleOperator,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus,
  NRCANSubType,
  SimaIncoterms,
  SimaSubjectCode,
  TCTireClass,
  TCTireCompliance,
  TCTireEndUse,
  TCTireSize,
  TCTireType
} from "./Types.compliance";

const PRIORITY_VALUES = Array.from({ length: 11 }, (_, i) => i);
export const PRIORITY = enumToSelectOptions(PRIORITY_VALUES);

export const SOURCE_TABLE = enumToSelectOptions(MatchingRuleSourceDatabaseTable).filter(
  (value) =>
    ![
      MatchingRuleSourceDatabaseTable.CERTIFICATE_OF_ORIGIN,
      MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING,
      MatchingRuleSourceDatabaseTable.OGD_FILING,
      MatchingRuleSourceDatabaseTable.SIMA_FILING
    ].includes(value.value as MatchingRuleSourceDatabaseTable)
);
export const DESTINATION_TABLE = enumToSelectOptions(MatchingRuleDestinationDatabaseTable);
export const OPERATOR = enumToSelectOptions(MatchingConditionOperator);

export const SOURCE_TABLE_ARRAY = Object.values(MatchingRuleSourceDatabaseTable).filter(
  (value) => value !== MatchingRuleSourceDatabaseTable.CERTIFICATE_OF_ORIGIN
);

export const MATCHING_RULE_STATUS = enumToSelectOptions(MatchingRuleStatus);
export const MATCHING_RULE_OPERATOR = enumToSelectOptions(MatchingRuleOperator, { allCaps: true });
export const MATCHING_RULE_SORT_BY = schemaToSelectOptions(complianceRuleTableSchema);

export const GENERAL_IMPORT_PERMIT = enumToSelectOptions(GeneralImportPermit);

export const AGENCY = enumToSelectOptions(CanadaGovernmentAgency, {
  allCaps: true
});

export const SIMA_SUBJECT_CODE = enumToSelectOptions(SimaSubjectCode);

export const SIMA_INCOTERMS = enumToSelectOptions(SimaIncoterms, {
  allCaps: true
});

export const NEW_CONDITION = {
  attribute: ProductAttribute.PART_NUMBER,
  operator: MatchingConditionOperator.EQUALS,
  value: "",
  isOperationInverted: false,
  valueRecord: null,
  id: undefined,
  inValue: null
};

export const COMPLIANCE_TABLE_KEY = "complianceTable";

export const GAC_LINK = {
  [GeneralImportPermit.GIP80]:
    "https://www.international.gc.ca/controls-controles/report-rapports/list_liste/handbook-manuel/C2-liste-72xx.aspx?lang=eng",
  [GeneralImportPermit.GIP81]:
    "https://www.international.gc.ca/controls-controles/report-rapports/list_liste/handbook-manuel/C2-liste-73xx.aspx?lang=eng",
  [GeneralImportPermit.GIP83]:
    "https://www.international.gc.ca/trade-commerce/controls-controles/reports-rapports/C6-aluminum_aluminium.aspx?lang=eng"
};

export const CFIA_AIR_TYPE = enumToSelectOptions(CFIAAirsType, {
  renderLabel: "both",
  swapValue: true,
  allCaps: true
});
export const HC_END_USE = enumToSelectOptions(HCEndUse, {
  renderLabel: "both",
  swapValue: true,
  allCaps: true
});
export const HC_PRODUCT_CATEGORY = enumToSelectOptions(HCProductCategory, {
  renderLabel: "both",
  swapValue: true,
  allCaps: true
});
export const TC_TIRE_END_USE = enumToSelectOptions(TCTireEndUse, {
  renderLabel: "both",
  swapValue: true,
  allCaps: true
});
export const TC_TIRE_TYPE = enumToSelectOptions(TCTireType, {
  renderLabel: "both",
  swapValue: true,
  allCaps: true
});
export const TC_TIRE_SIZE = enumToSelectOptions(TCTireSize, {
  renderLabel: "both",
  swapValue: true,
  allCaps: true
});
export const TC_TIRE_COMPLIANCE = enumToSelectOptions(TCTireCompliance, {
  renderLabel: "both",
  swapValue: true,
  allCaps: true
});
export const TC_TIRE_CLASS = enumToSelectOptions(TCTireClass, {
  renderLabel: "both",
  swapValue: true,
  allCaps: true
});
export const ECCC_SUB_TYPE = enumToSelectOptions(ECCCSubType);
export const ECCC_WILDLIFE_COMPLIANCE = enumToSelectOptions(ECCCWildlifeCompliance);
export const ECCC_EMISSION_PROGRAM = enumToSelectOptions(ECCCEmissionProgram);
export const NRCAN_SUB_TYPE = enumToSelectOptions(NRCANSubType);
