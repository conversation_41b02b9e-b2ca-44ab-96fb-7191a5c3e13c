import { useStore } from "@/bootstrap/Store.bootstrap";
import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { useColumnVisibility } from "@/modules/Settings/Hooks.settings";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { Button, ColumnSelector, Header, Pagination, Table } from "ui";
import { SortOrder } from "utils";
import { CertificateFilter } from "../../components/CertificateFilter";
import { CERTIFICATE_TABLE_KEY, CertificatePages } from "../../Constant.certificate";
import { useGetCertificates } from "../../queries";
import { certificateTableSchema } from "../../Schema.certificate";
import { CertificateOfOrigin, CertificateOfOriginColumn, GetCertificateList } from "../../Types.certificate";

const CertificateList = observer(() => {
  const navigate = useNavigate();
  const { isSuperAdmin } = useStore().auth;
  const [page, setPage] = useState(1);
  const [filter, toggleFilter] = useReducer((r) => !r, false);
  const [filters, setFilters] = useState<GetCertificateList>();

  const { visibleColumns, handleToggleColumn } = useColumnVisibility({
    defaultColumns: certificateTableSchema,
    key: CERTIFICATE_TABLE_KEY
  });

  const { data, error, isFetching } = useGetCertificates({
    page,
    limit: DEFAULT_LIMIT,
    sortBy: CertificateOfOriginColumn.validTo,
    sortOrder: SortOrder.DESC,
    ...filters
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const handleFilter = useCallback((values: GetCertificateList) => {
    setFilters(values);
    setPage(1);
  }, []);

  return (
    <div className="flex flex-col h-full overflow-hidden">
      <Header title="Certificate List" containerStyle="border">
        <Button label="Filters" kind="filter" onClick={toggleFilter} />
        <ColumnSelector columns={visibleColumns} onToggleColumn={handleToggleColumn} />
      </Header>

      <section>
        <CertificateFilter filterValues={handleFilter} isOpen={filter} isSuperAdmin={isSuperAdmin} />
      </section>

      <section className="flex-1 min-h-0 text-sm">
        <Table
          data={data?.certificates}
          isLoading={isFetching}
          schema={visibleColumns}
          onClick={(_data: CertificateOfOrigin) => navigate(CertificatePages.Detail(_data.id.toString()))}
          onSort={(key?: string, direction?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key as CertificateOfOriginColumn,
              sortOrder: direction
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
          footer={
            <Pagination currentPage={page} total={data?.total} limit={DEFAULT_LIMIT} onPageChange={setPage} />
          }
          total={data?.total}
        />
      </section>
    </div>
  );
});

export default CertificateList;
