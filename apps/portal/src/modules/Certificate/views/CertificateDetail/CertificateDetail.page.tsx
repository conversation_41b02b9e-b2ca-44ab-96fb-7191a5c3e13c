import { Breadcrumbs } from "@/components";
import { useGetAggregationByTarget } from "@/modules/Document/queries/GetAggregationByTarget.query";
import { AggregationTargetType } from "@/modules/Document/Types.document";
import { Link, useParams } from "react-router-dom";
import { Card, Header } from "ui";

const CertificateDetail = () => {
  const { certificate_id } = useParams<{ certificate_id: string }>();

  const { data: aggregation, isLoading } = useGetAggregationByTarget({
    id: certificate_id,
    targetType: AggregationTargetType.CERTIFICATE_OF_ORIGIN
  });

  return (
    <div>
      <Header title="Certificate of Origin" />
      <Card>
        <Breadcrumbs />
      </Card>
      <Card containerStyle="m-4 p-4">
        <h3 className="text-lg font-semibold mb-2">Certificate</h3>
        {!isLoading &&
          (aggregation ? (
            <Link
              to={`/document/${aggregation.documents[0].id}`}
              className="text-primary hover:underline text-sm"
            >
              {aggregation.documents[0].name}
            </Link>
          ) : (
            <p className="text-gray-500">No documents found for this certificate.</p>
          ))}
        {isLoading && <p className="text-gray-500">Loading...</p>}
      </Card>
    </div>
  );
};

export default CertificateDetail;
