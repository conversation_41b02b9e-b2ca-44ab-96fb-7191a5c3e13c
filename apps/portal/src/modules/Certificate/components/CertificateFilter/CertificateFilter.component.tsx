import { ORDER_BY } from "@/common/Constant.common";
import { Organization } from "@/modules/Auth/Types.auth";
import { SelectOrganizationModal } from "@/modules/Auth/components";
import { useEffect, useReducer, useState } from "react";
import { twMerge } from "tailwind-merge";
import { Card, Input, Select } from "ui";
import { OrderBy } from "utils";
import {
  CERTIFICATE_SORT_BY,
  CERTIFICATE_STATUS_OPTIONS,
  CertificateStatus
} from "../../Constant.certificate";
import { GetCertificateList } from "../../Types.certificate";

type Props = {
  isOpen: boolean;
  isSuperAdmin?: boolean;
  filterValues(values?: GetCertificateList): void;
};

const CertificateFilter = ({ isOpen, isSuperAdmin, filterValues }: Props) => {
  const [values, setValues] = useState<GetCertificateList>({});
  const [organizationModal, toggleOrganizationModal] = useReducer((r) => !r, false);
  const [organization, setOrganization] = useState<Organization>();

  useEffect(() => {
    filterValues(values);
  }, [filterValues, values]);

  useEffect(() => {
    // Set default status to VALID when component mounts
    setValues((prev) => ({
      ...prev,
      isValid: true
    }));

    return () => setValues(() => ({}));
  }, []);

  return (
    <div
      className={twMerge(
        "overflow-hidden transition-max-height duration-300 ease-in-out",
        isOpen ? "max-h-96" : "max-h-0"
      )}
    >
      {organizationModal && (
        <SelectOrganizationModal
          show={organizationModal}
          onClose={toggleOrganizationModal}
          selected={!!organization}
          onSelect={(organization) => {
            setValues((prev) => ({
              ...prev,
              organizationId: organization?.id
            }));
            setOrganization(organization);
            toggleOrganizationModal();
          }}
        />
      )}
      {isOpen && (
        <Card containerStyle="flex flex-wrap items-center gap-x-3 gap-y-2 px-6 py-4 border">
          <div className="flex gap-3 flex-wrap gap-x-3 gap-y-2">
            <Select
              label="Certificate Status"
              onSelected={(status?: CertificateStatus) => {
                if (status === CertificateStatus.ALL) {
                  const { isValid, ...rest } = values;
                  setValues(rest);
                  return;
                }

                setValues((prev) => ({ ...prev, isValid: status === CertificateStatus.VALID }));
              }}
              options={CERTIFICATE_STATUS_OPTIONS}
              defaultValue={CertificateStatus.VALID}
            />
            {isSuperAdmin && (
              <Input
                label="Organization"
                placeholder="Select organization"
                value={organization?.name ?? ""}
                onClick={toggleOrganizationModal}
                readOnly
              />
            )}
            <Select
              label="Sort By"
              onSelected={(sortBy: string) =>
                setValues((prev) => ({ ...prev, sortBy }) as GetCertificateList)
              }
              options={CERTIFICATE_SORT_BY}
              defaultValue={"createdDate"}
              optional
            />
            <Select
              label="Sort Direction"
              onSelected={(sortOrder: OrderBy) =>
                setValues((prev) => ({ ...prev, sortOrder }) as GetCertificateList)
              }
              options={ORDER_BY}
              defaultValue={"desc"}
              optional
            />
          </div>
        </Card>
      )}
    </div>
  );
};

export default CertificateFilter;
