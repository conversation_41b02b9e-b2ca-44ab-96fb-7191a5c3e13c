export const CERTIFICATE_TABLE_KEY = "certificates";

export const CertificatePages = {
  List: "/certificates",
  Detail: (certificate_id?: string) => `${CertificatePages.List}/${certificate_id}`
};

export enum CertificateStatus {
  ALL = "all",
  VALID = "valid",
  EXPIRED = "expired"
}

export const CERTIFICATE_STATUS_OPTIONS = [
  { label: "All", value: CertificateStatus.ALL },
  { label: "Valid", value: CertificateStatus.VALID },
  { label: "Expired", value: CertificateStatus.EXPIRED }
];

export const CERTIFICATE_SORT_BY = [
  { label: "Valid From", value: "validFrom" },
  { label: "Valid To", value: "validTo" },
  { label: "Created Date", value: "createDate" },
  { label: "Last Edit Date", value: "lastEditDate" }
];
