import { RouterObject } from "@/common/Types.common";
import { LucideFileBadge } from "lucide-react";
import { Link } from "react-router-dom";
import { CertificatePages } from "./Constant.certificate";
import { CertificateDetail, CertificateList } from "./views";

const CertificateNavigation = {
  label: "Certificates",
  path: CertificatePages.List,
  icon: <LucideFileBadge className="size-6" />,
  pattern: [{ path: `${CertificatePages.List}/*` }]
};

const CertificateRoutes: RouterObject = [
  {
    path: "/certificates",
    handle: {
      crumb: () => <Link to="/certificates">Certificates</Link>
    },
    children: [
      {
        index: true,
        handle: {
          crumb: () => "Certificate List"
        },
        element: <CertificateList />
      },
      {
        path: ":certificate_id",
        handle: {
          crumb: () => "Certificate Details"
        },
        element: <CertificateDetail />
      }
    ]
  }
];

export { CertificateNavigation, CertificateRoutes };
