import Http from "@/bootstrap/Http.bootstrap";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import type {
  CertificateOfOrigin,
  GetCertificateList,
  GetCertificateListResponse
} from "../Types.certificate";

const CERTIFICATE_ROUTE = "certificates";

/**
 * Get all certificates
 *
 * @returns
 */
async function list(params?: GetCertificateList): Promise<GetCertificateListResponse> {
  try {
    return await Http.get(stringifyQueryParams(CERTIFICATE_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}

/**
 * Get certificate detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<CertificateOfOrigin> {
  try {
    return await Http.get(`${CERTIFICATE_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}

export { get, list };
