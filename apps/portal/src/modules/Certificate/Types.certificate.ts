import {
  CertificateOfOrigin as COO,
  Country,
  GetCertificatesOfOriginDto,
  GetCertificatesOfOriginResponseDto,
  Organization,
  TradePartner
} from "nest-modules";
import { ListParams, PaginationResponse } from "utils";

export type { COO as CertificateOfOrigin, Country, Organization, TradePartner };

export enum CertificateAttribute {
  CERTIFICATE_ID = "id",
  EXPORTER = "exporterId",
  IMPORTER = "importerId",
  COUNTRY_OF_ORIGIN = "countryOfOriginId",
  ISSUE_DATE = "issueDate",
  EXPIRY_DATE = "expiryDate"
}

export enum CertificateOfOriginColumn {
  id = "id",
  validFrom = "validFrom",
  validTo = "validTo",
  invoiceNumber = "invoiceNumber",

  countryOfOriginId = "countryOfOriginId",

  exporterId = "exporterId",
  producerId = "producerId",
  importerId = "importerId",

  createDate = "createDate",
  lastEditDate = "lastEditDate",

  organizationId = "organizationId"
}

export type CertificateTable = COO;

export type GetCertificateList = ListParams<GetCertificatesOfOriginDto>;

export type GetCertificateListResponse = PaginationResponse & GetCertificatesOfOriginResponseDto;
