import { useQuery } from "@tanstack/react-query";
import CertificateService from "../services";
import type { GetCertificateList, GetCertificateListResponse } from "../Types.certificate";

export const getCertificatesKey = "getCertificates";

export const useGetCertificates = <R = GetCertificateListResponse>(
  params: GetCertificateList,
  select?: (data: GetCertificateListResponse) => R,
  refetchOnMount = false
) =>
  useQuery({
    queryKey: [getCertificatesKey, params],
    queryFn: () => CertificateService.list(params),
    select,
    refetchOnMount
  });
