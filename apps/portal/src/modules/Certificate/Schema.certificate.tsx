import { StatusLabel } from "ui";
import { TableSchema } from "utils";
import { CertificateOfOrigin, Country, Organization, TradePartner } from "./Types.certificate";

export const certificateTableSchema: TableSchema<CertificateOfOrigin> = {
  isValid: {
    header: "Status",
    renderer: ({ value }: { value: boolean }) => (
      <StatusLabel text={value ? "Valid" : "Expired"} color={value ? "success" : "danger"} />
    ),
    visible: true,
    disableSort: true
  },
  validFrom: {
    header: "Valid From",
    renderer: "date",
    visible: true
  },
  validTo: {
    header: "Valid To",
    renderer: "date",
    visible: true
  },
  countryOfOrigin: {
    header: "Country of Origin",
    visible: true,
    renderer: ({ value }: { value: Country }) => <span>{value.name}</span>
  },
  producer: {
    header: "Producer",
    visible: true,
    renderer: ({ value }: { value: Organization }) => <span>{value?.name}</span>
  },
  exporter: {
    header: "Exporter",
    visible: true,
    renderer: ({ value }: { value: TradePartner }) => <span>{value?.name}</span>
  },
  importer: {
    header: "Importer",
    visible: true,
    renderer: ({ value }: { value: TradePartner }) => <span>{value?.name}</span>
  }
};
//#endregion
