import Http from "@/bootstrap/Http.bootstrap";
import { handleError } from "utils";
import { SearchParams, SearchResponse } from "../Types.search";

/**
 * Global Search
 *
 * @returns
 */
async function search(params: SearchParams): Promise<SearchResponse> {
  if (!params.term?.trim()) return [];
  try {
    const searchParams = new URLSearchParams({
      term: params.term
    });
    if (params.contexts) {
      searchParams.append("contexts", params.contexts.toString());
    }
    return await Http.get(`global-search?${searchParams.toString()}`);
  } catch (error) {
    throw await handleError(error);
  }
}

export default { search };
