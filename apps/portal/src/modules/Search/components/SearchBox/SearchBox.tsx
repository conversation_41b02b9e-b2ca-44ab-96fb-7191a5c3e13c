import { useDebounce } from "@uidotdev/usehooks";
import { GlobalSearchResponseDto } from "nest-modules";
import { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import { Icons, Input, Modal } from "ui";
import { useGlobalSearchList } from "../../queries";
import { contextToIcon } from "../../Utils.search";
import { useNavigate } from "react-router-dom";
import { ShipmentPages } from "@/modules/Shipment/Routes.shipment";
import { ProductPages } from "@/modules/Product/Constant.product";
import { MailPages } from "@/modules/Mail/Routes.mail";
import { DocumentPages } from "@/modules/Document/Routes.documents";
import { SearchContext } from "../../Types.search";

type Props = {
  show: boolean;
  onSelect?(item?: GlobalSearchResponseDto): void;
  onClose(): void;
};
function SearchBox({ show, onClose }: Props) {
  const navigate = useNavigate();
  const [_term, setTerm] = useState<string>();
  const term = useDebounce(_term, 500);
  const inputRef = useRef<HTMLInputElement>(null);

  const { data, isFetching, error } = useGlobalSearchList({
    term: term && term.length >= 3 ? term : undefined
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  useEffect(() => {
    if (show && inputRef.current) {
      inputRef.current.focus();
    }
  }, [show]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (show) {
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [show, onClose]);

  const handleSelect = (item: GlobalSearchResponseDto) => {
    onClose();
    switch (item.context) {
      case SearchContext.SHIPMENT:
        return navigate(ShipmentPages.Detail(item.id));
      case SearchContext.PRODUCT:
        return navigate(ProductPages.Detail(item.id));
      case SearchContext.EMAIL:
        return navigate(MailPages.Thread(item.threadId));
      case SearchContext.DOCUMENT:
        return navigate(DocumentPages.Detail(item.id));
    }
  };

  return (
    <Modal
      id="search-box"
      show={show}
      onClose={onClose}
      backgroundStyle="items-start backdrop-blur-sm !bg-opacity-30"
      containerStyle="rounded-lg"
      headerStyle="rounded-t-lg min-h-0 flex-none"
      title={
        <>
          <Input
            ref={inputRef}
            placeholder="Search shipments or products"
            kind="search"
            containerStyle="w-full bg-transparent"
            inputStyle="bg-transparent border-none focus-visible:outline-none"
            onTextChange={setTerm}
          />
        </>
      }
      size="xl"
      actions={<></>}
    >
      <div className="overflow-y-auto flex-1">
        {isFetching ? (
          <div className="py-2">
            <Icons.Loader />
          </div>
        ) : term && term.length < 3 ? (
          <div className="text-center">
            <small>Please enter at least 3 characters to search</small>
          </div>
        ) : data && data.length > 0 ? (
          <ul className="space-y-1 divide-neutral-200">
            {data.map((item, index) => (
              <li key={index} onClick={() => handleSelect(item)}>
                <div className="flex gap-4 p-2 rounded items-center cursor-pointer hover:bg-neutral-100">
                  <div className="text-neutral-700">{contextToIcon(item.context)}</div>
                  <div className="flex flex-col">
                    <span className="font-medium">{item.title}</span>
                    <small>{item.subtitle}</small>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        ) : (
          <div className="text-center">
            <small>No results found</small>
          </div>
        )}
      </div>
    </Modal>
  );
}
export default SearchBox;
