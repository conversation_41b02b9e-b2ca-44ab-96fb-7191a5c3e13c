import { useQuery } from "@tanstack/react-query";
import { SearchService } from "../services";
import { SearchParams, SearchResponse } from "../Types.search";

export const globalSearchKey = "globalSearch";

export const useGlobalSearch = <R = SearchResponse>(
  params: SearchParams,
  select?: (data: SearchResponse) => R
) =>
  useQuery({
    queryKey: [globalSearchKey, params],
    queryFn: () => SearchService.search(params),
    select,
    refetchOnMount: false,
    enabled: !!params.term
  });

export const useGlobalSearchList = (params: SearchParams) => useGlobalSearch(params, undefined);
