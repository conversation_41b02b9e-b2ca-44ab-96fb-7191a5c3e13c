import { File, Mail, Package, Ship } from "lucide-react";
import { SearchContext } from "./Types.search";

function contextToIcon(context: SearchContext) {
  switch (context) {
    case SearchContext.SHIPMENT:
      return <Ship className="size-6" />;
    case SearchContext.PRODUCT:
      return <Package className="size-6" />;
    case SearchContext.EMAIL:
      return <Mail className="size-6" />;
    case SearchContext.DOCUMENT:
      return <File className="size-6" />;
  }
}

export { contextToIcon };
