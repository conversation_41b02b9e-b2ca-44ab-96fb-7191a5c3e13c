import {
  BatchUpdateCommercialInvoiceLinesDto,
  CanadaAntiDumping,
  CanadaExciseTaxCode,
  CanadaGstExemptCode,
  CanadaOgd,
  CanadaSimaCode,
  CanadaTariff,
  CanadaTreatmentCode,
  CanadaVfdCode,
  CommercialInvoice as CommInvoice,
  CommercialInvoiceLine as CommInvoiceLine,
  EditCommercialInvoiceDto,
  EditCommercialInvoiceLineDto,
  GetCommercialInvoiceLinesDto,
  GetCommercialInvoiceLinesResponseDto,
  GetCommercialInvoicesDto,
  GetCommercialInvoicesResponseDto,
  NonCompliantRecordDto,
  SimplifiedCommercialInvoiceLineMeasurement,
  ValidateCommercialInvoiceLineComplianceResponseDto
} from "nest-modules";
import { ChangeProps, DetailParams, ListParams, PaginationResponse } from "utils";
import { MatchingRuleSourceDatabaseTable } from "../Compliance/Types.compliance";
import { Product, ProductType } from "../Product/Types.product";
import { UnitOfMeasure } from "../Shipment/Types.shipment";
import { TradePartner } from "../TradePartner/Types.partner";

export enum SurtaxSubjectCode {
  SUBJECT = "S",
  NON_SUBJECT = "N"
}
export enum SafeguardSubjectCode {
  SUBJECT = "S",
  NON_SUBJECT = "N"
}
export enum SpecialAuthorityTimeLimitType {
  AIRCRAFT_VESSEL_PARTS_15_YEARS = 1,
  BEER_AND_WINE_5_YEARS = 2,
  WAREHOUSE_MARKING_DISPLAY_90_DAYS = 3,
  OTHER_GOODS_4_YEARS = 4
}

export enum DestinationProvince {
  ALBERTA = "AB",
  BRITISH_COLUMBIA = "BC",
  MANITOBA = "MB",
  NEW_BRUNSWICK = "NB",
  NEWFOUNDLAND_AND_LABRADOR = "NL",
  NOVA_SCOTIA = "NS",
  NORTHWEST_TERRITORIES = "NT",
  NUNAVUT = "NU",
  ONTARIO = "ON",
  PRINCE_EDWARD_ISLAND = "PE",
  QUEBEC = "QC",
  SASKATCHEWAN = "SK",
  YUKON_TERRITORY = "YT"
}

export enum NonCompliantReason {
  MISSING_OGD_FILING = "missing-ogd-filing",
  MISSING_SIMA_FILING = "missing-sima-filing",
  MISSING_OR_INVALID_MEASUREMENTS = "missing-or-invalid-measurements",
  MISSING_CERTIFICATE_OF_ORIGIN = "missing-certificate-of-origin",
  /** @deprecated Please use missing-sima-filing instead. */
  MISSING_SIMA_CODE = "missing-sima-code"
}

export type InvoiceFormModal = {
  exporter?: TradePartner;
  vendor?: TradePartner;
  manufacturer?: TradePartner;
  purchaser?: TradePartner;
  shipTo?: TradePartner;
};
export type InvoiceLineFormModal = {
  [MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE]?: CanadaVfdCode;
  [MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE]?: CanadaTreatmentCode;
  [MatchingRuleSourceDatabaseTable.CANADA_TARIFF]?: CanadaTariff;
  product?: Product;
  temporaryProduct?: Product;
  [MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE]?: CanadaGstExemptCode;
  [MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE]?: CanadaExciseTaxCode;
};

export type CommercialInvoice = CommInvoice;
export type CommercialInvoiceTable = CommercialInvoice & {
  countryOfExportName?: string;
  exporterName?: string;
  purchaserName?: string;
  vendorName?: string;
  manufacturerName?: string;
  shipToName?: string;
  nonCompliantLines?: ValidateCommercialInvoiceLineComplianceResponseDto[];
  totalValue?: number;
};

export type GetCommercialInvoiceList = ListParams<GetCommercialInvoicesDto>;
export type GetCommercialInvoiceListResponse = PaginationResponse & GetCommercialInvoicesResponseDto;

export type SaveCommercialInvoiceParams = ChangeProps<EditCommercialInvoiceDto, "invoiceDate", string> &
  DetailParams & { stateRequired?: boolean };

export type CommercialInvoiceLine = CommInvoiceLine;
export type CommercialInvoiceLineTable = CommercialInvoiceLine & {
  sequence?: number;
  vfdName?: string;
  vfdCode?: number;
  ttName?: string;
  ttCode?: number;
  ogdAgencies?: string;
  simaCode?: number;
  productDescription?: string;
  partNumber?: string;
  originName?: string;
  nonCompliantRecords?: NonCompliantRecordDto[];
  currency?: string;
  productType?: ProductType;
  exchangeRate?: number;
};

export type GetCommercialInvoiceLineList = ListParams<GetCommercialInvoiceLinesDto> & {
  invoiceId: number;
};
export type GetCommercialInvoiceLineListResponse = PaginationResponse & GetCommercialInvoiceLinesResponseDto;
export type CommercialInvoiceLineParams = DetailParams & {
  invoiceId: number;
};
export type SaveCommercialInvoiceLineParams = ChangeProps<
  EditCommercialInvoiceLineDto,
  "timeLimitStartDate" | "timeLimitEndDate",
  string
> &
  DetailParams & { invoiceId: number; stateRequired?: boolean; unitOfMeasure?: UnitOfMeasure };

export type Measurement = SimplifiedCommercialInvoiceLineMeasurement;

export type MultipleInvoiceLineFormModal = InvoiceLineFormModal &
  Pick<SaveCommercialInvoiceLineParams, "originId" | "originStateId">;

export type BatchSaveCommercialInvoiceLineParams = BatchUpdateCommercialInvoiceLinesDto & DetailParams;

export type MultipleFilingParams = {
  sourceTable: MatchingRuleSourceDatabaseTable;
  sourceRecord?: CanadaOgd | CanadaSimaCode;
  measureInForce?: CanadaAntiDumping;
  lines: CommercialInvoiceLineTable[];
  products: Product[];
};
export type MultipleFilings = Record<string, MultipleFilingParams>;

export type OgdAgency = {
  agency: string;
  ogdFiled: boolean;
};
