import { CommercialInvoiceLine } from "../Types.commercial-invoice";

function getCommercialInvoiceLineDto(c: CommercialInvoiceLine) {
  return {
    ...c,
    vfdCode: c.vfd?.code,
    vfdName: c.vfd?.displayName,
    ttCode: c.tt?.code,
    ttName: c.tt?.displayName,
    productDescription: c.product?.description,
    originName: c.origin?.displayName,
    partNumber: c.product?.partNumber
  };
}
export { getCommercialInvoiceLineDto };
