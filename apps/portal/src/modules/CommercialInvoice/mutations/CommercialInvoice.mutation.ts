import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import CommercialInvoiceService from "../services";
import type { SaveCommercialInvoiceParams } from "../Types.commercial-invoice";

const useSaveCommercialInvoice = () =>
  useMutation({
    mutationFn: async (params: SaveCommercialInvoiceParams) => {
      if (params.id) {
        return await CommercialInvoiceService.edit(params);
      }
      return await CommercialInvoiceService.create(params);
    }
  });

const useDeleteCommercialInvoice = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await CommercialInvoiceService.remove(params)
  });

export { useDeleteCommercialInvoice, useSaveCommercialInvoice };
