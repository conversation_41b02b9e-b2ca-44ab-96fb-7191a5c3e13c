import { useMutation } from "@tanstack/react-query";
import CommercialInvoiceService from "../services";
import type {
  BatchSaveCommercialInvoiceLineParams,
  CommercialInvoiceLineParams,
  SaveCommercialInvoiceLineParams
} from "../Types.commercial-invoice";

const useSaveCommercialInvoiceLine = () =>
  useMutation({
    mutationFn: async (params: SaveCommercialInvoiceLineParams) => {
      if (params.id) {
        return await CommercialInvoiceService.Line.edit(params);
      }
      return await CommercialInvoiceService.Line.create(params);
    }
  });

const useDeleteCommercialInvoiceLine = () =>
  useMutation({
    mutationFn: async (params: CommercialInvoiceLineParams) =>
      await CommercialInvoiceService.Line.remove(params)
  });

const useBatchSaveCommercialInvoiceLine = () =>
  useMutation({
    mutationFn: async (params: BatchSaveCommercialInvoiceLineParams) =>
      await CommercialInvoiceService.Line.batchSave(params)
  });

export { useDeleteCommercialInvoiceLine, useSaveCommercialInvoiceLine, useBatchSaveCommercialInvoiceLine };
