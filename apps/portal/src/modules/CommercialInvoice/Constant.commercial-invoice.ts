import { enumToSelectOptions } from "utils";
import {
  DestinationProvince,
  SafeguardSubjectCode,
  SpecialAuthorityTimeLimitType,
  SurtaxSubjectCode
} from "./Types.commercial-invoice";

export const TIME_LIMIT_TYPE = enumToSelectOptions(SpecialAuthorityTimeLimitType).filter(({ label }) =>
  Number.isNaN(Number(label))
);

export const SURTAX_SUBJECT_CODE = enumToSelectOptions(SurtaxSubjectCode);

export const DESTINATION_PROVINCE = enumToSelectOptions(DestinationProvince);

export const SAFEGUARD_SUBJECT_CODE = enumToSelectOptions(SafeguardSubjectCode);
