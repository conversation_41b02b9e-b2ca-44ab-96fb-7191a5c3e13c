import { ShipmentCompliance } from "@/modules/Shipment/Types.shipment";
import { useQuery } from "@tanstack/react-query";
import CommercialInvoiceService from "../services";
import type {
  CommercialInvoice,
  GetCommercialInvoiceList,
  GetCommercialInvoiceListResponse
} from "../Types.commercial-invoice";

export const getCommercialInvoicesKey = "getCommercialInvoices";

export const useGetCommercialInvoices = <R = GetCommercialInvoiceListResponse>(
  params?: GetCommercialInvoiceList,
  select?: (data: GetCommercialInvoiceListResponse) => R,
  enabled = true
) =>
  useQuery({
    queryKey: [getCommercialInvoicesKey, params],
    queryFn: () => CommercialInvoiceService.list(params),
    select,
    enabled: !!params?.shipmentId && enabled
  });

export const useGetCommercialInvoiceList = (
  params?: GetCommercialInvoiceList,
  compliance?: ShipmentCompliance,
  enabled?: boolean
) =>
  useGetCommercialInvoices(
    params,
    (data: GetCommercialInvoiceListResponse) => {
      const commercialInvoices = data.commercialInvoices.map((c: CommercialInvoice) => {
        const issue = compliance?.nonCompliantInvoices?.find((i) => i.commercialInvoiceId === c.id);

        const totalValue =
          c.commercialInvoiceLines.length > 0
            ? c.commercialInvoiceLines.reduce((acc, line) => (acc || 0) + (line.totalLineValue || 0), 0)
            : 0;

        return {
          ...c,
          hasIssue: !!issue,
          nonCompliantLines: issue?.nonCompliantLines,
          countryOfExportName: c.countryOfExport?.name,
          exporterName: c.exporter?.name,
          purchaserName: c.purchaser?.name,
          vendorName: c.vendor?.name,
          manufacturerName: c.manufacturer?.name,
          shipToName: c.shipTo?.name,
          totalValue
        };
      });

      return { ...data, commercialInvoices };
    },
    enabled
  );
