import { useQuery } from "@tanstack/react-query";
import { DetailParams } from "utils";
import CommercialInvoiceService from "../services";

export const getCommercialInvoiceComplianceKey = "getCommercialInvoiceCompliance";

export const useGetCommercialInvoiceCompliance = ({ id }: DetailParams) =>
  useQuery({
    queryKey: [getCommercialInvoiceComplianceKey, id],
    queryFn: () => CommercialInvoiceService.getCompliance({ id }),
    enabled: !!id,
    gcTime: 0,
    retry: false
  });
