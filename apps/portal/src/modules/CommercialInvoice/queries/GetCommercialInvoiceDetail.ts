import { useQuery } from "@tanstack/react-query";
import { DetailParams } from "utils";
import CommercialInvoiceService from "../services";

export const getCommercialInvoiceDetailKey = "getCommercialInvoiceDetail";

export const useGetCommercialInvoiceDetail = ({ id }: DetailParams) =>
  useQuery({
    queryKey: [getCommercialInvoiceDetailKey, id],
    queryFn: () => CommercialInvoiceService.get({ id }),
    enabled: !!id
  });
