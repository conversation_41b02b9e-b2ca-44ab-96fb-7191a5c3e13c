import { CommercialInvoiceLineParams } from "@/modules/CommercialInvoice/Types.commercial-invoice";
import { useQuery } from "@tanstack/react-query";
import Line from "../services/CommercialInvoiceLine.service";

export const useGetCommercialInvoiceLineFallback = ({
  id,
  invoiceId,
  productId
}: CommercialInvoiceLineParams & { invoiceId?: number; productId?: number }) => {
  return useQuery({
    queryKey: ["commercial-invoice-line-fallback", id, invoiceId, productId],
    queryFn: () => Line.getFallback({ id, invoiceId, productId }),
    enabled: !!invoiceId && (!!id || !!productId)
  });
};
