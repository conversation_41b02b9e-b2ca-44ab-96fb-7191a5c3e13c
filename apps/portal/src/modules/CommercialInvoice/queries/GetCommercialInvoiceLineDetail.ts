import { useQuery } from "@tanstack/react-query";
import CommercialInvoiceService from "../services";
import { CommercialInvoiceLineParams } from "../Types.commercial-invoice";

export const getCommercialInvoiceLineDetailKey = "getCommercialInvoiceLineDetail";

export const useGetCommercialInvoiceLineDetail = (params: CommercialInvoiceLineParams) =>
  useQuery({
    queryKey: [getCommercialInvoiceLineDetailKey, params],
    queryFn: () => CommercialInvoiceService.Line.get(params),
    enabled: !!params.invoiceId && !!params.id
  });
