import { useQuery } from "@tanstack/react-query";
import { ValidateCommercialInvoiceLineComplianceResponseDto } from "nest-modules";
import { getCommercialInvoiceLineDto } from "../dto";
import CommercialInvoiceService from "../services";
import {
  type CommercialInvoiceLine,
  type GetCommercialInvoiceLineList,
  type GetCommercialInvoiceLineListResponse
} from "../Types.commercial-invoice";

export const getCommercialInvoiceLinesKey = "getCommercialInvoiceLines";

export const useGetCommercialInvoiceLines = <R = GetCommercialInvoiceLineListResponse>(
  params: GetCommercialInvoiceLineList,
  select?: (data: GetCommercialInvoiceLineListResponse) => R,
  enabled = true
) =>
  useQuery({
    queryKey: [getCommercialInvoiceLinesKey, params],
    queryFn: () => CommercialInvoiceService.Line.list(params),
    select,
    enabled: !!params?.invoiceId && enabled
  });

export const useGetCommercialInvoiceLineList = (
  params: GetCommercialInvoiceLineList,
  compliance?: ValidateCommercialInvoiceLineComplianceResponseDto[],
  enabled?: boolean
) =>
  useGetCommercialInvoiceLines(
    params,
    (data: GetCommercialInvoiceLineListResponse) => {
      const commercialInvoiceLines = data.commercialInvoiceLines.map((c: CommercialInvoiceLine) => {
        const issue = compliance?.find((i) => i.lineId === c.id);

        return {
          ...c,
          hasIssue: !!issue,
          nonCompliantRecords: issue?.nonCompliantRecords,
          productType: c.product?.productType,
          ...getCommercialInvoiceLineDto(c)
        };
      });

      const totals =
        commercialInvoiceLines.length > 0
          ? commercialInvoiceLines.reduce(
              (acc, line) => ({
                totalTotalLineValue: (acc.totalTotalLineValue || 0) + (line.totalLineValue || 0)
              }),
              {
                totalTotalLineValue: 0
              }
            )
          : undefined;

      return { ...data, commercialInvoiceLines, totals };
    },
    enabled
  );
