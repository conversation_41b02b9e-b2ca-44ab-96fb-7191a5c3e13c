import { useQuery } from "@tanstack/react-query";
import CommercialInvoiceService from "../services";
import { CommercialInvoiceLineParams } from "../Types.commercial-invoice";

export const getCommercialInvoiceLineComplianceKey = "getCommercialInvoiceLineCompliance";

export const useGetCommercialInvoiceLineCompliance = (params: CommercialInvoiceLineParams) =>
  useQuery({
    queryKey: [getCommercialInvoiceLineComplianceKey, params],
    queryFn: () => CommercialInvoiceService.Line.getCompliance(params),
    enabled: !!params.invoiceId && !!params.id,
    gcTime: 0,
    retry: false
  });
