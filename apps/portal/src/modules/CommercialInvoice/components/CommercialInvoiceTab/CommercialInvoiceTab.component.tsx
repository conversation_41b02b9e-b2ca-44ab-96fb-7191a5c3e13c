import { BaseError } from "@/common/Types.common";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";
import {
  useGetCommercialInvoiceCompliance,
  useGetCommercialInvoiceList
} from "@/modules/CommercialInvoice/queries";
import {
  commInvoiceTableSchema,
  issueInvoiceTableSchema
} from "@/modules/CommercialInvoice/Schema.commercial-invoice";
import { canCheckCompliance } from "@/modules/Compliance/Utils.compliance";
import { ShipmentContext } from "@/modules/Shipment/Types.shipment";
import { PlusIcon } from "lucide-react";
import { CommercialInvoiceColumn } from "nest-modules";
import { useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useOutletContext } from "react-router-dom";
import { twMerge } from "tailwind-merge";
import { <PERSON><PERSON>, Card, Popup, Table } from "ui";
import { SortOrder } from "utils";
import { useDeleteCommercialInvoice, useSaveCommercialInvoice } from "../../mutations";
import {
  CommercialInvoice,
  GetCommercialInvoiceList,
  SaveCommercialInvoiceParams
} from "../../Types.commercial-invoice";
import CommercialInvoiceLines from "../CommercialInvoiceLines";
import InvoiceForm from "../InvoiceForm";

function CommercialInvoiceTab() {
  const {
    shipment,
    compliance,
    loadingState,
    refetch: refetchShipment,
    refetchCompliance,
    isComplianceFetched
  } = useOutletContext<ShipmentContext>();
  const { showPopup, hidePopup } = Popup.usePopup();
  const [sidebar, setSidebar] = useState(false);
  const [invoice, setInvoice] = useState<CommercialInvoice>();
  const [isDirty, setIsDirty] = useState(false);
  const [initOpenDone, setInitOpenDone] = useState(false);
  const [isPending, setIsPending] = useState(false);
  const [filters, setFilters] = useState<GetCommercialInvoiceList>();
  const { canCrudBackoffice } = useBackofficePermission(shipment?.organization?.id);

  const saveCommercialInvoice = useSaveCommercialInvoice();
  const deleteCommercialInvoice = useDeleteCommercialInvoice();

  const { data, error, isFetching, refetch, isFetched } = useGetCommercialInvoiceList(
    {
      limit: 1000,
      shipmentId: shipment?.id,
      ...filters
    },
    compliance,
    canCheckCompliance(shipment) ? isComplianceFetched : true
  );
  const {
    data: invoiceCompliance,
    isFetching: isFetchingInvoiceCompliance,
    refetch: refetchInvoiceCompliance,
    isFetched: isFetchedInvoiceCompliance
  } = useGetCommercialInvoiceCompliance({
    id: canCheckCompliance(shipment) ? invoice?.id : undefined
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  // Reset states when shipment changes
  useEffect(() => {
    setInvoice(undefined);
    setIsDirty(false);
    setSidebar(false);
    setInitOpenDone(false);
  }, [shipment?.id]);

  useEffect(() => {
    if (
      !invoice &&
      data?.commercialInvoices &&
      data.commercialInvoices.length > 0 &&
      isFetched &&
      !initOpenDone
    ) {
      setInvoice(data?.commercialInvoices[0]);
      if (!sidebar) setSidebar(true);
      setInitOpenDone(true);
    }
  }, [data?.commercialInvoices, initOpenDone, invoice, isFetched, shipment?.customsStatus, sidebar]);

  const handleSubmit = useCallback(
    async (params: SaveCommercialInvoiceParams) => {
      try {
        setIsPending(true);
        await saveCommercialInvoice.mutateAsync(params);

        setTimeout(async () => {
          setIsDirty(false);

          await refetchShipment();
          await refetchCompliance();
          await refetch();

          const index = data?.commercialInvoices.findIndex((d) => d.id === params.id);
          if (index && index > -1) setInvoice(data?.commercialInvoices[index]);

          await refetchInvoiceCompliance();

          toast.success(`Commercial Invoice ${params.id ? "updated" : "created"} successfully`);
          setIsPending(false);
        }, 500);
      } catch (error) {
        toast.error((error as BaseError).message);
        setIsPending(false);
      }
    },
    [
      saveCommercialInvoice,
      refetchShipment,
      refetchCompliance,
      refetch,
      data?.commercialInvoices,
      refetchInvoiceCompliance
    ]
  );

  const handleDeleteInvoice = useCallback(async () => {
    setIsPending(true);
    try {
      await deleteCommercialInvoice.mutateAsync({ id: invoice?.id });

      setTimeout(async () => {
        if (sidebar) setSidebar(false);
        setInvoice(undefined);

        await refetchShipment();
        await refetchCompliance();
        await refetch();

        toast.success("Commercial Invoice deleted successfully");
        setIsPending(false);
      }, 500);
    } catch (error) {
      toast.error((error as BaseError).message);
      setIsPending(false);
    }
  }, [deleteCommercialInvoice, invoice?.id, refetch, refetchCompliance, refetchShipment, sidebar]);

  const deleteInvoicePrompt = () => {
    showPopup(
      {
        content: "Are you sure you want to delete this Commercial Invoice?",
        onProceed: () => {
          handleDeleteInvoice();
          hidePopup();
        }
      },
      Popup.PopupType.DELETE_POPUP
    );
  };

  return (
    <main className="flex flex-col gap2">
      <section>
        <Table
          data={data?.commercialInvoices}
          isLoading={isFetching || loadingState}
          schema={compliance?.nonCompliantInvoices?.length ? issueInvoiceTableSchema : commInvoiceTableSchema}
          getRowStyle={(d) => (d.id === invoice?.id ? "bg-neutral-100" : "")}
          onClick={(d: CommercialInvoice) => {
            if (!invoice) {
              setSidebar(true);
            }
            if (!isDirty) {
              setInvoice(d);
            }
          }}
          header={
            <div className="flex-1 flex items-center justify-between">
              <h5>Commercial Invoice</h5>
              {!sidebar && canCrudBackoffice && (
                <Button
                  label="Add Invoice"
                  icon={<PlusIcon size={16} />}
                  onClick={() => setSidebar(true)}
                  disabled={isFetching || loadingState}
                />
              )}
            </div>
          }
          onSort={(key?: string, direction?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key as CommercialInvoiceColumn,
              sortOrder: direction
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
        />
      </section>

      <section
        className={twMerge(
          "transition-all duration-150 ease-in-out flex flex-col gap-2 opacity-0",
          sidebar ? "opacity-100 max-h-full visible" : "opacity-0 max-h-0 invisible overflow-hidden"
        )}
      >
        <Card containerStyle="m-4 p-4 flex flex-col gap-4">
          {sidebar && (
            <InvoiceForm
              shipment={shipment}
              invoice={invoice}
              isLoading={saveCommercialInvoice.isPending || deleteCommercialInvoice.isPending || isPending}
              compliance={invoiceCompliance}
              isFetchingCompliance={isFetchingInvoiceCompliance}
              onSubmit={handleSubmit}
              onCancel={() => {
                setInvoice(undefined);
                setSidebar(false);
              }}
              onDelete={deleteInvoicePrompt}
              isDirty={setIsDirty}
            />
          )}

          {invoice && (
            <CommercialInvoiceLines
              invoice={invoice}
              nonCompliantLines={invoiceCompliance?.nonCompliantLines}
              isComplianceFetched={canCheckCompliance(shipment) ? isFetchedInvoiceCompliance : true}
            />
          )}
        </Card>
      </section>
    </main>
  );
}
export default CommercialInvoiceTab;
