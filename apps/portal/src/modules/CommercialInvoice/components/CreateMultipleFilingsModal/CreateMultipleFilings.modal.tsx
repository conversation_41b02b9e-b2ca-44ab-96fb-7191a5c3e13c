import { Organization } from "@/modules/Auth/Types.auth";
import { CompliancePages } from "@/modules/Compliance/Routes.compliance";
import { useCallback, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button, Modal, Table } from "ui";
import { snakeCaseToCapitalize } from "utils";
import { commercialInvoiceLineTableSchema } from "../../Schema.commercial-invoice";
import {
  CommercialInvoiceLineTable,
  MultipleFilingParams,
  MultipleFilings
} from "../../Types.commercial-invoice";

type Props = {
  show: boolean;
  filings: MultipleFilings;
  organization: Organization;
  onClose(): void;
};

function CreateMultipleFilingsModal({ show, filings, organization, onClose }: Props) {
  const navigate = useNavigate();
  const [selectedLines, setSelectedLines] = useState<Map<string, CommercialInvoiceLineTable[]>>(new Map());

  const handleCreateCompliance = useCallback(
    (line: MultipleFilingParams, key: string) => {
      navigate(CompliancePages.Detail("create"), {
        state: {
          ...line,
          products: selectedLines.get(key)?.map((item) => item.product),
          organization,
          from: location.pathname
        }
      });
      onClose();
    },
    [selectedLines, navigate, organization, onClose]
  );

  return (
    <Modal id="create-multiple-filings-modal" show={show} size="3xl" onClose={onClose} title="Create Filings">
      <div className="flex flex-col gap-3">
        {Object.entries(filings).map(([key, data]) => (
          <Table
            key={key}
            header={
              <>
                <span className="font-medium">{`Missing ${snakeCaseToCapitalize(key, true)} Filing`}</span>
                <Button
                  label="Create Filing"
                  onClick={() => handleCreateCompliance(data, key)}
                  disabled={!selectedLines.get(key) || selectedLines.get(key)?.length === 0}
                />
              </>
            }
            data={data?.lines}
            schema={commercialInvoiceLineTableSchema}
            checklist
            multiple
            selectAll
            selected={selectedLines.get(key)}
            onMultiSelect={(item) => {
              const map = new Map(selectedLines);
              map.set(key, item);
              setSelectedLines(map);
            }}
          />
        ))}
      </div>
    </Modal>
  );
}

export default CreateMultipleFilingsModal;
