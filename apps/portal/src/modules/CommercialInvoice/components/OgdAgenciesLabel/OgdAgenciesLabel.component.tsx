import { twMerge } from "tailwind-merge";
import { OgdAgency } from "../../Types.commercial-invoice";

const OgdAgenciesLabel = ({ value }: { value: OgdAgency[] }) => {
  if (value.length === 0) return "";

  return (
    <div>
      {value.map((agencies, index) => (
        <span key={agencies.agency}>
          <span className={twMerge("text-danger", agencies.ogdFiled && "text-success")}>
            {agencies.agency}
          </span>
          {index < value.length - 1 && <span>, </span>}
        </span>
      ))}
    </div>
  );
};

export default OgdAgenciesLabel;
