import { stateRequired } from "@/common/Utils.common";
import { SelectSourceModal } from "@/modules/Compliance/components";
import { MatchingRuleSourceDatabaseTable, SourceTable } from "@/modules/Compliance/Types.compliance";
import { SelectCountry, SelectState } from "@/modules/Location/components";
import { Country, CountryOption } from "@/modules/Location/Types.location";
import { useCallback, useMemo, useState } from "react";
import { Button, Input, Modal } from "ui";
import { MultipleInvoiceLineFormModal } from "../../Types.commercial-invoice";

type Props = {
  show: boolean;
  isLoading?: boolean;
  onSubmit(params: MultipleInvoiceLineFormModal): void;
  onClose(): void;
};
function EditMultipleInvoiceLineModal({ show, isLoading, onSubmit, onClose }: Props) {
  const [modalValues, setModalValues] = useState<MultipleInvoiceLineFormModal>();
  const [sourceModal, setSourceModal] = useState<MatchingRuleSourceDatabaseTable>();
  const [country, setCountry] = useState<Country>();

  const stateError = useMemo(
    () => stateRequired(country?.alpha2) && !modalValues?.originStateId,
    [country?.alpha2, modalValues?.originStateId]
  );
  const isEmpty = useMemo(() => Object.values(modalValues ?? {}).every((value) => !value), [modalValues]);

  const handleSourceModal = useCallback(
    (source: SourceTable, _sourceModal?: MatchingRuleSourceDatabaseTable) => {
      switch (_sourceModal) {
        case MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE:
          setModalValues((prev) => ({
            ...prev,
            [MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE]: source
          }));
          setModalValues((prev) => ({ ...prev, ttId: source?.id }));
          break;
        case MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE:
          setModalValues((prev) => ({ ...prev, [MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE]: source }));
          setModalValues((prev) => ({ ...prev, vfdId: source?.id }));
          break;
      }
    },
    []
  );
  const handleSelectSource = useCallback(
    (source: SourceTable) => {
      handleSourceModal(source, sourceModal);
      setSourceModal(undefined);
    },
    [handleSourceModal, sourceModal]
  );
  const handleCountrySelect = useCallback((value?: CountryOption) => {
    setModalValues((prev) => ({ ...prev, originId: value?.value as number }));
    if (!value || !stateRequired(value?.alpha2)) {
      setModalValues((prev) => ({ ...prev, originStateId: undefined }));
    }
    setCountry(value as unknown as Country);
  }, []);

  const handleSubmit = useCallback(() => {
    if (modalValues) onSubmit(modalValues);
  }, [modalValues, onSubmit]);

  return (
    <Modal
      id="edit-multiple-invoice-line-modal"
      show={show}
      onClose={onClose}
      title="Edit Multiple Invoice Line"
      actions={
        <Button
          label={"Update"}
          kind="update"
          loading={isLoading}
          onClick={handleSubmit}
          disabled={stateError || isEmpty}
        />
      }
    >
      <div className="flex flex-col gap-3">
        {sourceModal && (
          <SelectSourceModal
            show={!!sourceModal}
            source={sourceModal}
            record={modalValues?.[sourceModal as keyof typeof modalValues] as SourceTable | null | undefined}
            onClose={() => setSourceModal(undefined)}
            onSelect={handleSelectSource}
          />
        )}
        <Input
          label="VFD Code"
          name="vfdId"
          placeholder="Select VFD code"
          value={modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE]?.displayName ?? ""}
          onClick={() => {
            setSourceModal(MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE);
          }}
          readOnly
        />
        <Input
          label="Treatment Code"
          name="ttId"
          placeholder="Select treatment code"
          value={modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE]?.displayName ?? ""}
          onClick={() => {
            setSourceModal(MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE);
          }}
          readOnly
        />
        <SelectCountry name="originId" placeholder="Select Origin" onSelect={handleCountrySelect} />
        {modalValues?.originId && stateRequired(country?.alpha2) && (
          <SelectState
            label="Origin State"
            name="originStateId"
            placeholder="Select State of Export"
            countryId={modalValues?.originId}
            onSelect={(value) => setModalValues((prev) => ({ ...prev, originStateId: value as number }))}
            error={stateError ? "State is required" : ""}
          />
        )}
      </div>
    </Modal>
  );
}
export default EditMultipleInvoiceLineModal;
