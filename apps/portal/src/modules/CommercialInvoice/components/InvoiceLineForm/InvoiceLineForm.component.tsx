import InputFallbackWrapper from "@/common/components/InputFallbackWrapper/InputFallbackWrapper.component";
import { useFallback } from "@/common/hooks/useFallback";
import { BaseError } from "@/common/Types.common";
import { stateRequired } from "@/common/Utils.common";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";
import { ComplianceError, SelectSourceModal } from "@/modules/Compliance/components";
import { useProductMatchingRules, useSearchTariff } from "@/modules/Compliance/queries";
import { CompliancePages } from "@/modules/Compliance/Routes.compliance";
import { MatchingRuleSourceDatabaseTable, SourceTable } from "@/modules/Compliance/Types.compliance";
import { canCheckCompliance } from "@/modules/Compliance/Utils.compliance";
import { SelectCountry, SelectState } from "@/modules/Location/components";
import { Country, CountryOption, State } from "@/modules/Location/Types.location";
import {
  AddProductModal,
  ProductComplianceInformation,
  SelectProductModal
} from "@/modules/Product/components";
import { Product, ProductType } from "@/modules/Product/Types.product";
import { UNIT_OF_MEASURE, UNIT_OF_MEASURE_TYPE } from "@/modules/Shipment/Constant.shipment";
import { ShipmentDetailPages, ShipmentPages } from "@/modules/Shipment/Routes.shipment";
import { ShipmentContext, UNIT_OF_MEASURE_TYPE_MAP, UnitOfMeasure } from "@/modules/Shipment/Types.shipment";
import { FieldArray, FormikProvider, useFormik } from "formik";
import { PlusIcon, TrashIcon } from "lucide-react";
import { NonCompliantRecordDto, UnitOfMeasureType } from "nest-modules";
import { useCallback, useEffect, useMemo, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { useLocation, useNavigate, useOutletContext, useParams } from "react-router-dom";
import { Button, Card, Header, Icons, Input, InputHint, Popup, Select, Textarea, Tooltip } from "ui";
import {
  capitalizeLetter,
  emptyStringToNull,
  formikInputOpts,
  getFormikError,
  isoDateToString,
  localDateToUtc,
  Nullable
} from "utils";
import {
  DESTINATION_PROVINCE,
  SAFEGUARD_SUBJECT_CODE,
  SURTAX_SUBJECT_CODE,
  TIME_LIMIT_TYPE
} from "../../Constant.commercial-invoice";
import { useDeleteCommercialInvoiceLine, useSaveCommercialInvoiceLine } from "../../mutations";
import { useGetCommercialInvoiceLineCompliance, useGetCommercialInvoiceLineDetail } from "../../queries";
import { useGetCommercialInvoiceLineFallback } from "../../queries/GetCommercialInvoiceLineFallback";
import { saveCommercialInvoiceLineSchema } from "../../Schema.commercial-invoice";
import {
  InvoiceLineFormModal,
  Measurement,
  SaveCommercialInvoiceLineParams
} from "../../Types.commercial-invoice";

type TariffType = "4" | "10";
function InvoiceLineForm() {
  const navigate = useNavigate();
  const { invoice_id, line_id } = useParams();
  const { state } = useLocation();
  const { shipment, refetch: refetchShipment, refetchCompliance } = useOutletContext<ShipmentContext>();
  const { showPopup, hidePopup } = Popup.usePopup();
  const { canCrudBackoffice } = useBackofficePermission();

  const [modalValues, setModalValues] = useState<Nullable<InvoiceLineFormModal>>();
  const [productModal, toggleProductModal] = useReducer((r) => !r, false);
  const [temporaryProductModal, toggleTemporaryProductModal] = useReducer((r) => !r, false);
  const [sourceModal, setSourceModal] = useState<MatchingRuleSourceDatabaseTable>();
  const [tariffType, setTariffType] = useState<TariffType>();
  const [isPending, setIsPending] = useState(false);

  // const readOnly = useMemo(() => !canEditInvoice(shipment?.customsStatus), [shipment?.customsStatus]);

  const isCreate = !line_id || line_id === "undefined" || line_id === "create";

  //#region Init values
  const {
    data: info,
    error,
    isFetching,
    refetch
  } = useGetCommercialInvoiceLineDetail({
    invoiceId: Number(invoice_id),
    id: Number(line_id)
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const {
    data: lineCompliance,
    isFetching: isFetchingLineCompliance,
    refetch: refetchLineCompliance
  } = useGetCommercialInvoiceLineCompliance({
    id: canCheckCompliance(shipment) ? Number(line_id) : undefined,
    invoiceId: Number(invoice_id)
  });

  useEffect(() => {
    setModalValues((prev) => ({
      ...prev,
      product: info?.product,
      [MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE]: info?.vfd,
      [MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE]: info?.tt,
      [MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE]: info?.gstExemptCode,
      [MatchingRuleSourceDatabaseTable.CANADA_TARIFF]: info?.tariffCode,
      [MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE]: info?.exciseTaxCode
    }));
  }, [info]);

  useEffect(() => {
    setCountry(info?.origin as Country);
    setOriginState(info?.originState as State);
  }, [info?.origin, info?.originState]);

  useEffect(() => {
    return () => {
      setCountry(undefined);
      setOriginState(undefined);
    };
  }, []);

  const canEditHsCode = useMemo(
    () => modalValues?.product?.productType === ProductType.TEMPORARY,

    [modalValues?.product]
  );
  //#endregion

  //#region Submit handler
  const saveCommercialInvoiceLine = useSaveCommercialInvoiceLine();
  const deleteCommercialInvoiceLine = useDeleteCommercialInvoiceLine();

  const handleSubmit = useCallback(
    async (params: SaveCommercialInvoiceLineParams) => {
      const formatParams: SaveCommercialInvoiceLineParams = {
        ...emptyStringToNull(params),
        hsCode: canEditHsCode ? params.hsCode : undefined,
        quantity: params.quantity ? Number(params.quantity) : undefined,
        unitPrice: params.unitPrice ? Number(params.unitPrice) : undefined,
        totalLineValue: params.totalLineValue ? Number(params.totalLineValue) : undefined,
        provincialAlcoholTax: params.provincialAlcoholTax ? Number(params.provincialAlcoholTax) : undefined,
        provincialTobaccoTax: params.provincialTobaccoTax ? Number(params.provincialTobaccoTax) : undefined,
        provincialCannabisExciseDuty: params.provincialCannabisExciseDuty
          ? Number(params.provincialCannabisExciseDuty)
          : undefined,
        alcoholPercentage: params.alcoholPercentage ? Number(params.alcoholPercentage) : undefined,
        timeLimitType: params.timeLimitType ? Number(params.timeLimitType) : undefined,
        timeLimitStartDate: params.timeLimitStartDate ? localDateToUtc(params.timeLimitStartDate) : undefined,
        timeLimitEndDate: params.timeLimitEndDate ? localDateToUtc(params.timeLimitEndDate) : undefined,
        simaQuantity: params.simaQuantity ? Number(params.simaQuantity) : undefined,
        pstHst: params.pstHst ? Number(params.pstHst) : undefined,
        originId: params.originId ? Number(params.originId) : undefined,
        measurements:
          params.measurements && params.measurements.length > 0
            ? params.measurements?.map((m) => ({ ...m, value: Number(m.value) }))
            : undefined
      };

      setIsPending(true);

      try {
        const line = await saveCommercialInvoiceLine.mutateAsync(formatParams);

        setTimeout(async () => {
          toast.success(`Invoice Line ${isCreate ? "added" : "saved"}`);

          await refetchShipment();
          await refetchCompliance();

          if (isCreate) {
            navigate(
              `${ShipmentPages.Detail(shipment.id)}/${ShipmentDetailPages.CommercialInvoiceLine(
                invoice_id,
                line.id
              )}`,
              {
                replace: true
              }
            );
          } else {
            await refetch();
            await refetchLineCompliance();
          }

          setIsPending(false);
        }, 500);
      } catch (error) {
        toast.error((error as BaseError).message);
        setIsPending(false);
      }
    },
    [
      canEditHsCode,
      invoice_id,
      isCreate,
      navigate,
      refetch,
      refetchCompliance,
      refetchLineCompliance,
      refetchShipment,
      saveCommercialInvoiceLine,
      shipment?.id
    ]
  );

  const handleDeleteLine = useCallback(async () => {
    setIsPending(true);
    try {
      await deleteCommercialInvoiceLine.mutateAsync({
        id: info?.id,
        invoiceId: Number(invoice_id)
      });

      setTimeout(async () => {
        await refetchShipment();
        await refetchCompliance();

        setIsPending(false);
        toast.success("Commercial Invoice Line deleted successfully");
        navigate(-1);
      }, 500);
    } catch (error) {
      toast.error((error as BaseError).message);
      setIsPending(false);
    }
  }, [deleteCommercialInvoiceLine, info?.id, invoice_id, navigate, refetchCompliance, refetchShipment]);

  const deleteLinePrompt = () => {
    showPopup(
      {
        content: "Are you sure you want to delete this Commercial Invoice Line?",
        onProceed: () => {
          handleDeleteLine();
          hidePopup();
        }
      },
      Popup.PopupType.DELETE_POPUP
    );
  };
  //#endregion

  const formik = useFormik({
    initialValues: {
      id: info?.id ?? undefined,
      invoiceId: Number(invoice_id),
      goodsDescription: info?.goodsDescription ?? "",
      productId: info?.product?.id,
      vfdId: info?.vfd?.id,
      ttId: info?.tt?.id,
      hsCode: info?.hsCode ?? "",
      quantity: info?.quantity,
      unitOfMeasure: info?.unitOfMeasure,
      // TODO: after removing nullable, unitPrice will not need default value
      unitPrice: info?.unitPrice ?? 0,
      totalLineValue: info?.totalLineValue,
      additionalInfo: info?.additionalInfo ?? "",
      originId: info?.origin?.id,
      stateRequired: stateRequired(info?.origin?.alpha2),
      originStateId: info?.originState?.id,
      gstExemptCodeId: info?.gstExemptCode?.id,
      tariffCodeId: info?.tariffCode?.id,
      // Special Authorities
      orderInCouncil: info?.orderInCouncil ?? undefined,
      dutiesReliefLicence: info?.dutiesReliefLicence ?? undefined,
      authorityPermit: info?.authorityPermit ?? undefined,
      timeLimitType: info?.timeLimitType ?? undefined,
      timeLimitStartDate: info?.timeLimitStartDate ? isoDateToString(info.timeLimitStartDate) : undefined,
      timeLimitEndDate: info?.timeLimitEndDate ? isoDateToString(info.timeLimitEndDate) : undefined,
      // Duties & Taxes
      destinationProvince: info?.destinationProvince ?? undefined,
      pstHst: info?.pstHst ?? undefined,
      alcoholPercentage: info?.alcoholPercentage ?? undefined,
      provincialAlcoholTax: info?.provincialAlcoholTax ?? undefined,
      provincialTobaccoTax: info?.provincialTobaccoTax ?? undefined,
      provincialCannabisExciseDuty: info?.provincialCannabisExciseDuty ?? undefined,
      exciseTaxCodeId: info?.exciseTaxCode?.id,
      // SIMA
      simaQuantity: info?.simaQuantity ?? undefined,
      simaUnitOfMeasure: info?.simaUnitOfMeasure ?? undefined,
      // Surtax & Safeguard
      surtaxSubjectCode: info?.surtaxSubjectCode ?? undefined,
      surtaxCode: info?.surtaxCode ?? undefined,
      safeguardSubjectCode: info?.safeguardSubjectCode ?? undefined,
      safeguardCode: info?.safeguardCode ?? undefined,
      // additional measurements
      measurements: info?.measurements || [],
      temporaryProduct: undefined
    },
    validationSchema: saveCommercialInvoiceLineSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit: handleSubmit
  });

  const { setFieldValue } = formik;

  //#region Fallback (default value)
  const { data: fallback } = useGetCommercialInvoiceLineFallback({
    id: info?.id,
    invoiceId: Number(invoice_id),
    productId: formik.values.productId !== info?.product?.id ? formik.values.productId : undefined
  });

  const fallbackHelper = useFallback(fallback);
  //#endregion

  //#region Select and auto-populate handler

  // auto-populate origin
  const [country, setCountry] = useState<Country>();
  const [originState, setOriginState] = useState<State>();
  const [productId, setProductId] = useState<number>();

  // auto-populate other compliance
  const { data: compliance } = useProductMatchingRules(productId, [
    MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE,
    MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE,
    MatchingRuleSourceDatabaseTable.CANADA_TARIFF,
    MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE
  ]);

  // auto-populate UoM
  const [hsCode, setHsCode] = useState<string>();
  const { data: tariff, isFetching: isFetchingTariff } = useSearchTariff(hsCode);

  const handleSelectProduct = useCallback(
    (product?: Product) => {
      setModalValues((prev) => ({ ...prev, product }));
      setFieldValue("productId", product?.id);
      setFieldValue("hsCode", product?.hsCode);
      setFieldValue("goodsDescription", product?.description);
      setFieldValue("originId", product?.origin?.id);
      setFieldValue("originStateId", product?.originState?.id);

      const _stateRequired = stateRequired(product?.origin?.alpha2);
      setFieldValue("stateRequired", _stateRequired);
      if (!_stateRequired) setFieldValue("originStateId", undefined);

      setCountry(product?.origin);
      setOriginState(product?.originState as State);
      setProductId(product?.id);
      setHsCode(product?.hsCode);

      // reset tt and excise tax code
      handleSourceModal(undefined, MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE);
      handleSourceModal(undefined, MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE);
      handleSourceModal(undefined, MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE);

      if (temporaryProductModal) {
        setFieldValue("temporaryProduct", product);
        toggleTemporaryProductModal();
      } else {
        setFieldValue("temporaryProduct", undefined);
      }
      if (productModal) toggleProductModal();
    },
    [productModal, setFieldValue, temporaryProductModal]
  );
  // Set product after create product
  useEffect(() => {
    if (state?.product) {
      setTariffType("4");
      handleSelectProduct(state.product);
    }
  }, [handleSelectProduct, state]);

  const handleSourceModal = useCallback(
    (source?: SourceTable, _sourceModal?: MatchingRuleSourceDatabaseTable, _tariffType?: TariffType) => {
      switch (_sourceModal) {
        case MatchingRuleSourceDatabaseTable.CANADA_TARIFF:
          if (_tariffType === "10") {
            setFieldValue("hsCode", source?.hsCode);
            setFieldValue("unitOfMeasure", source?.uom);
            setModalValues((prev) => ({ ...prev, hsCode: source }));
            setHsCode(source?.hsCode);
          }
          if (_tariffType === "4") {
            setFieldValue("tariffCodeId", source?.id ?? null);
            setModalValues((prev) => ({ ...prev, [MatchingRuleSourceDatabaseTable.CANADA_TARIFF]: source }));
          }
          break;
        case MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE:
          setModalValues((prev) => ({
            ...prev,
            [MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE]: source
          }));
          setFieldValue("ttId", source?.id);
          break;
        case MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE:
          setModalValues((prev) => ({ ...prev, [MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE]: source }));
          setFieldValue("vfdId", source?.id);
          break;
        case MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE:
          setModalValues((prev) => ({
            ...prev,
            [MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE]: source
          }));
          setFieldValue("gstExemptCodeId", source?.id ?? null);
          break;
        case MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE:
          setModalValues((prev) => ({
            ...prev,
            [MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE]: source
          }));
          setFieldValue("exciseTaxCodeId", source?.id ?? null);
          break;
      }
    },
    [setFieldValue]
  );

  useEffect(() => {
    if (compliance && compliance.length > 0) {
      for (const item of compliance) {
        handleSourceModal(item.sourceRecords[0] as SourceTable, item.sourceTable, "4");
      }
    }
  }, [compliance, handleSourceModal]);

  useEffect(() => {
    if (tariff && tariff.data.length > 0) {
      const uom = tariff.data[0]?.uom ?? UnitOfMeasure.NUMBER;
      setFieldValue("unitOfMeasure", uom);

      if (formik.values.measurements && formik.values.measurements.length > 0) {
        const measurementsUom = formik.values.measurements.find((m) => m.unitOfMeasure === uom);
        if (measurementsUom) {
          setFieldValue("quantity", measurementsUom.value);
          setFieldValue(
            "unitPrice",
            formik.values.totalLineValue ? formik.values.totalLineValue / measurementsUom.value : 0
          );
        }
      }
    }
  }, [formik.values.measurements, formik.values.totalLineValue, setFieldValue, tariff]);

  useEffect(() => {
    if (formik.values.totalLineValue && formik.values.quantity) {
      setFieldValue("unitPrice", formik.values.totalLineValue / formik.values.quantity);
    }
  }, [formik.values.quantity, formik.values.totalLineValue, setFieldValue]);

  const handleSelectSource = useCallback(
    (source: SourceTable) => {
      handleSourceModal(source, sourceModal, tariffType);
      setSourceModal(undefined);
    },
    [handleSourceModal, sourceModal, tariffType]
  );

  const handleCountrySelect = useCallback(
    (value?: CountryOption) => {
      setFieldValue("originId", value?.value ?? "");
      setFieldValue("stateRequired", stateRequired(value?.alpha2));

      if (!value || !stateRequired(value?.alpha2)) {
        setFieldValue("originStateId", undefined);
      }
    },
    [setFieldValue]
  );

  const handleComplianceIssue = useCallback(
    (record: NonCompliantRecordDto) => {
      navigate(CompliancePages.Detail("create"), {
        state: {
          sourceTable:
            record.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING
              ? MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE
              : record.sourceTable,
          sourceRecord:
            record.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_OGD
              ? record.sourceRecord
              : undefined,
          measureInForce:
            record.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING
              ? record.sourceRecord
              : undefined,
          product: info?.product,
          from: location.pathname,
          organization: info?.organization
        }
      });
    },
    [info?.organization, info?.product, navigate]
  );

  const getUnselectedUOMTypeSelectOptions = useCallback(
    (type: UnitOfMeasureType) => {
      return UNIT_OF_MEASURE_TYPE.filter(
        (t) =>
          !(formik.values.measurements as Measurement[]).some((m) => m.type === t.value && t.value !== type)
      );
    },
    [formik.values.measurements]
  );

  const [hsCodeDebounceTimeout, setHsCodeDebounceTimeout] = useState<NodeJS.Timeout | null>(null);

  const handleHsCodeChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newHsCode = e.target.value;
      formik.handleChange(e);

      if (hsCodeDebounceTimeout) {
        clearTimeout(hsCodeDebounceTimeout);
      }

      const timeout = setTimeout(() => {
        if (newHsCode && newHsCode.length === 10) {
          setHsCode(newHsCode);
        }
      }, 1000);

      setHsCodeDebounceTimeout(timeout);
    },
    [formik, hsCodeDebounceTimeout]
  );

  useEffect(() => {
    return () => {
      if (hsCodeDebounceTimeout) {
        clearTimeout(hsCodeDebounceTimeout);
      }
    };
  }, [hsCodeDebounceTimeout]);
  //#endregion

  return (
    <div>
      <Header title={<h5>{info ? "Edit" : "Add"} Invoice Line</h5>} containerStyle="py-2 min-h-0">
        {canCrudBackoffice && (
          <>
            {info && (
              <Button
                label="Delete"
                kind="delete"
                loading={deleteCommercialInvoiceLine.isPending}
                disabled={
                  isFetching ||
                  isFetchingLineCompliance ||
                  saveCommercialInvoiceLine.isPending ||
                  isPending ||
                  isFetchingTariff
                }
                onClick={deleteLinePrompt}
              />
            )}
            <Button
              label={info ? "Update" : "Add"}
              kind="update"
              loading={saveCommercialInvoiceLine.isPending || isPending}
              disabled={
                isFetching ||
                isFetchingLineCompliance ||
                deleteCommercialInvoiceLine.isPending ||
                isFetchingTariff
              }
              onClick={formik.handleSubmit}
            />
          </>
        )}
      </Header>

      {productModal && (
        <SelectProductModal
          show={productModal}
          product={modalValues?.product}
          onClose={toggleProductModal}
          onSelect={handleSelectProduct}
          required
        />
      )}
      {temporaryProductModal && (
        <AddProductModal
          show={temporaryProductModal}
          info={formik.values.temporaryProduct as unknown as Product}
          origin={country}
          originState={originState}
          onClose={toggleTemporaryProductModal}
          onSubmit={(params) => handleSelectProduct(params as Product)}
        />
      )}
      {sourceModal && (
        <SelectSourceModal
          show={!!sourceModal}
          source={sourceModal}
          record={modalValues?.[sourceModal as keyof typeof modalValues] as SourceTable | null | undefined}
          onClose={() => setSourceModal(undefined)}
          onSelect={handleSelectSource}
          hsCodeLength={tariffType}
          canSelect={(item) => {
            if (sourceModal === MatchingRuleSourceDatabaseTable.CANADA_TARIFF)
              if (tariffType === "4") {
                return item.hsCode?.length === 4;
              } else {
                return item.hsCode?.length === 10;
              }

            return true;
          }}
        />
      )}

      {isFetching ? (
        <Icons.Loader />
      ) : (
        <div>
          <Card containerStyle="m-4">
            <ComplianceError
              title="Invoice Line"
              compliance={lineCompliance}
              onIssueClick={handleComplianceIssue}
            />
          </Card>

          <Card containerStyle="p-4 m-4">
            <div className="flex flex-col gap-2">
              <fieldset disabled={saveCommercialInvoiceLine.isPending || isPending}>
                <form className="flex flex-col gap-4 divide-y">
                  <div className="flex flex-col gap-2">
                    <h5>Details</h5>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-2">
                      <Input
                        label="Product Part Number"
                        name="productId"
                        placeholder="Select product"
                        value={modalValues?.product?.partNumber ?? ""}
                        onClick={toggleProductModal}
                        error={getFormikError(formik, "productId")}
                        readOnly
                        {...(canCrudBackoffice && {
                          hint: (
                            <InputHint
                              label={
                                formik.values.temporaryProduct ? "Edit product" : "Create temporary product"
                              }
                              onClick={
                                toggleTemporaryProductModal
                                // navigate(ProductPages.Detail("create"), {
                                //   state: {
                                //     from: location.pathname,
                                //     source: "commercial_invoice_line"
                                //   }
                                // })
                              }
                            />
                          )
                        })}
                      />

                      <div className="flex gap-2">
                        {modalValues?.product?.productType === ProductType.TEMPORARY && (
                          <Input
                            label="Product Type"
                            containerStyle="flex-1"
                            value={capitalizeLetter(modalValues?.product?.productType)}
                            disabled
                          />
                        )}
                        <Input
                          label="HS Code"
                          placeholder="Input HS code"
                          containerStyle="flex-1"
                          {...formikInputOpts(formik, "hsCode")}
                          onChange={handleHsCodeChange}
                          {...(canEditHsCode && {
                            hint: (
                              <InputHint
                                label="Search"
                                onClick={() => {
                                  setSourceModal(MatchingRuleSourceDatabaseTable.CANADA_TARIFF);
                                  setTariffType("10");
                                }}
                              />
                            )
                          })}
                          kind="search"
                          disabled={!canEditHsCode}
                        />
                      </div>
                      <Tooltip
                        text={
                          modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE]
                            ? modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE]?.displayName
                            : ""
                        }
                      >
                        <Input
                          label="VFD Code"
                          name="vfdId"
                          placeholder="Select VFD code"
                          value={
                            modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE]?.displayName ?? ""
                          }
                          onClick={() => {
                            setSourceModal(MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE);
                          }}
                          error={getFormikError(formik, "vfdId")}
                          readOnly
                        />
                      </Tooltip>
                      <Tooltip
                        text={
                          modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE]
                            ? modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE]
                                ?.displayName
                            : ""
                        }
                      >
                        <InputFallbackWrapper
                          fallback={fallbackHelper.getFallback("tt")}
                          value={modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE]}
                          valueTransformer={(value) => value?.displayName}
                          onReset={(value) => {
                            handleSourceModal(value, MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE);
                          }}
                          comparator={(value, fallbackValue) => value?.id === fallbackValue?.id}
                          materialized
                          withReset
                        >
                          <Input
                            label="Treatment Code"
                            name="ttId"
                            placeholder="Select treatment code"
                            value={
                              modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE]
                                ?.displayName ?? ""
                            }
                            onClick={() => {
                              setSourceModal(MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE);
                            }}
                            error={getFormikError(formik, "ttId")}
                            readOnly
                          />
                        </InputFallbackWrapper>
                      </Tooltip>
                      <Tooltip
                        text={
                          modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE]
                            ? modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE]
                                ?.displayName
                            : ""
                        }
                      >
                        <InputFallbackWrapper
                          fallback={fallbackHelper.getFallback("gstExemptCode")}
                          value={modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE]}
                          valueTransformer={(value) => value?.displayName}
                          onReset={(value) => {
                            handleSourceModal(value, MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE);
                          }}
                          comparator={(value, fallbackValue) => value?.id === fallbackValue?.id}
                          materialized
                          withReset
                        >
                          <Input
                            label="GST Code"
                            name="gstExemptCodeId"
                            placeholder="Select GST code"
                            value={
                              modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE]
                                ?.displayName ?? ""
                            }
                            onClick={() => {
                              setSourceModal(MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE);
                            }}
                            error={getFormikError(formik, "gstExemptCodeId")}
                            readOnly
                          />
                        </InputFallbackWrapper>
                      </Tooltip>
                      <Tooltip
                        text={
                          modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_TARIFF]
                            ? modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_TARIFF]?.displayName
                            : ""
                        }
                      >
                        <Input
                          label="Tariff Code"
                          name="tariffCodeId"
                          placeholder="Select tariff code"
                          value={
                            modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_TARIFF]?.displayName ?? ""
                          }
                          onClick={() => {
                            setSourceModal(MatchingRuleSourceDatabaseTable.CANADA_TARIFF);
                            setTariffType("4");
                          }}
                          error={getFormikError(formik, "tariffCodeId")}
                          readOnly
                        />
                      </Tooltip>

                      <div className="flex gap-2">
                        <Input
                          label="Quantity"
                          placeholder="Qty"
                          containerStyle="w-fit"
                          {...formikInputOpts(formik, "quantity")}
                          onNumberChange={formik.handleChange}
                        />
                        <Select
                          label="Unit"
                          options={UNIT_OF_MEASURE}
                          {...formikInputOpts(formik, "unitOfMeasure")}
                          disabled
                          optional
                        />
                      </div>

                      <Input
                        label="Unit Price"
                        placeholder="Input unit price"
                        {...formikInputOpts(formik, "unitPrice")}
                        disabled
                      />
                      <Input
                        label="Total Line Value"
                        placeholder="Input total line value"
                        {...formikInputOpts(formik, "totalLineValue")}
                        onNumberChange={formik.handleChange}
                      />
                      <SelectCountry
                        name="originId"
                        placeholder="Select Origin"
                        value={country}
                        onSelect={handleCountrySelect}
                        onBlur={formik.handleBlur}
                        error={getFormikError(formik, "originId")}
                      />
                      {formik.values.originId && formik.values.stateRequired && (
                        <SelectState
                          label="Origin State"
                          name="originStateId"
                          placeholder="Select State of Export"
                          value={originState}
                          countryId={formik.values.originId}
                          onSelect={(value) => setFieldValue("originStateId", value ?? "")}
                          onBlur={formik.handleBlur}
                          error={getFormikError(formik, "originStateId")}
                        />
                      )}
                      <Textarea
                        label="Goods Description"
                        placeholder="Input goods description"
                        {...formikInputOpts(formik, "goodsDescription")}
                      />
                      <Textarea
                        label="Additional Info"
                        placeholder="Input additional info"
                        {...formikInputOpts(formik, "additionalInfo")}
                      />
                    </div>
                  </div>

                  <div className="flex flex-col gap-2 pt-1">
                    <FormikProvider value={formik}>
                      <FieldArray
                        name="measurements"
                        render={(arrayHelpers) => (
                          <>
                            <div className="flex justify-between">
                              <h5>Additional Measurements</h5>
                              <Button
                                kind="outline"
                                label="Add"
                                icon={<PlusIcon size={16} />}
                                className="py-1 px-2"
                                onClick={() =>
                                  arrayHelpers.push({
                                    type: "",
                                    unitOfMeasure: "",
                                    value: ""
                                  })
                                }
                              />
                            </div>
                            <div className="flex flex-col gap-3">
                              {(formik.values.measurements as Measurement[]).map(
                                (measurement: Measurement, index: number) => (
                                  <div key={index} className="flex gap-2">
                                    <div className="grid grid-cols-3 gap-4 flex-1">
                                      <Select
                                        optional
                                        label="Type"
                                        {...formikInputOpts(formik, `measurements.${index}.type`)}
                                        options={getUnselectedUOMTypeSelectOptions(measurement.type)}
                                      />
                                      <Select
                                        optional
                                        disabled={!measurement.type}
                                        label="UoM"
                                        {...formikInputOpts(formik, `measurements.${index}.unitOfMeasure`)}
                                        options={
                                          measurement.type
                                            ? UNIT_OF_MEASURE.filter((uom) =>
                                                UNIT_OF_MEASURE_TYPE_MAP[measurement.type].includes(
                                                  uom.value as UnitOfMeasure
                                                )
                                              )
                                            : []
                                        }
                                      />
                                      <Input
                                        label="Value"
                                        placeholder="Input value"
                                        {...formikInputOpts(formik, `measurements.${index}.value`)}
                                        onNumberChange={formik.handleChange}
                                      />
                                    </div>
                                    <TrashIcon
                                      className="size-4 mt-7 cursor-pointer text-danger"
                                      onClick={() => arrayHelpers.remove(index)}
                                    />
                                  </div>
                                )
                              )}
                            </div>
                          </>
                        )}
                      />
                    </FormikProvider>
                  </div>

                  <div className="flex flex-col gap-2 pt-1">
                    <h5>Special Authorities</h5>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-2">
                      <Input
                        label="Order in Council"
                        placeholder="Input order in council"
                        {...formikInputOpts(formik, "orderInCouncil")}
                      />
                      <Input
                        label="Duties Relief Licence"
                        placeholder="Input duties relief licence"
                        {...formikInputOpts(formik, "dutiesReliefLicence")}
                      />
                      <Input
                        label="Authority Permit"
                        placeholder="Input authority permit"
                        {...formikInputOpts(formik, "authorityPermit")}
                      />
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-2">
                      <Select
                        label="Time Limit Type"
                        options={TIME_LIMIT_TYPE}
                        {...formikInputOpts(formik, "timeLimitType")}
                        optional
                      />
                      <Input
                        label="Time Limit From"
                        type="date"
                        placeholder="Input time limit from"
                        {...formikInputOpts(formik, "timeLimitStartDate")}
                      />
                      <Input
                        label="Time Limit To"
                        type="date"
                        placeholder="Input time limit to"
                        {...formikInputOpts(formik, "timeLimitEndDate")}
                      />
                    </div>
                  </div>

                  <div className="flex flex-col gap-2 pt-1">
                    <h5>Duties and Taxes</h5>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-2">
                      <Select
                        label="Destination Province"
                        options={DESTINATION_PROVINCE}
                        {...formikInputOpts(formik, "destinationProvince")}
                        optional
                      />
                      <Input
                        label="PST / HST / QST"
                        placeholder="Input pst/hst/qst"
                        {...formikInputOpts(formik, "pstHst")}
                        onNumberChange={formik.handleChange}
                      />
                      <Input
                        label="Alcohol Percentage"
                        placeholder="Input alcohol percentage"
                        hint="%"
                        {...formikInputOpts(formik, "alcoholPercentage")}
                        onNumberChange={formik.handleChange}
                      />
                      <Input
                        label="Provincial Alcohol Tax"
                        placeholder="Input provincial alcohol tax"
                        {...formikInputOpts(formik, "provincialAlcoholTax")}
                        onNumberChange={formik.handleChange}
                      />
                      <Input
                        label="Provincial Tobacco Tax"
                        placeholder="Input provincial tobacco tax"
                        {...formikInputOpts(formik, "provincialTobaccoTax")}
                        onNumberChange={formik.handleChange}
                      />
                      <Input
                        label="Provincial Cannabis Excise Duty"
                        placeholder="Input provincial cannabis excise duty"
                        {...formikInputOpts(formik, "provincialCannabisExciseDuty")}
                        onNumberChange={formik.handleChange}
                      />
                      <Tooltip
                        text={
                          modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE]
                            ? modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE]
                                ?.displayName
                            : ""
                        }
                      >
                        <InputFallbackWrapper
                          fallback={fallbackHelper.getFallback("exciseTaxCode")}
                          value={modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE]}
                          valueTransformer={(value) => value?.displayName}
                          onReset={(value) => {
                            handleSourceModal(value, MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE);
                          }}
                          comparator={(value, fallbackValue) => value?.id === fallbackValue?.id}
                          materialized
                          withReset
                        >
                          <Input
                            label="Excise Tax Code"
                            name="exciseTaxCodeId"
                            placeholder="Select excise tax code"
                            value={
                              modalValues?.[MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE]
                                ?.displayName ?? ""
                            }
                            onClick={() => {
                              setSourceModal(MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE);
                            }}
                            error={getFormikError(formik, "exciseTaxCodeId")}
                            readOnly
                          />
                        </InputFallbackWrapper>
                      </Tooltip>
                    </div>
                  </div>

                  <div className="flex flex-col gap-2 pt-1">
                    <h5>SIMA</h5>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-2">
                      <Input
                        label="SIMA Quantity"
                        placeholder="Input sima quantity"
                        {...formikInputOpts(formik, "simaQuantity")}
                        onNumberChange={formik.handleChange}
                      />
                      <Select
                        label="SIMA UoM"
                        options={UNIT_OF_MEASURE}
                        {...formikInputOpts(formik, "simaUnitOfMeasure")}
                        optional
                      />
                    </div>
                    {info?.product && (
                      <div className="bg-neutral-10 p-1">
                        <ProductComplianceInformation product={info.product} isSima={true} />
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col gap-2 pt-1">
                    <h5>Surtax & Safeguard</h5>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-2">
                      <Select
                        label="Surtax Subject"
                        options={SURTAX_SUBJECT_CODE}
                        {...formikInputOpts(formik, "surtaxSubjectCode")}
                        optional
                      />
                      <Input
                        label="Surtax Code"
                        placeholder="Input surtax code"
                        {...formikInputOpts(formik, "surtaxCode")}
                      />
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-x-4 gap-y-2">
                      <Select
                        label="Safeguard Subject"
                        options={SAFEGUARD_SUBJECT_CODE}
                        {...formikInputOpts(formik, "safeguardSubjectCode")}
                        optional
                      />
                      <Input
                        label="Safeguard Code"
                        placeholder="Input safeguard code"
                        {...formikInputOpts(formik, "safeguardCode")}
                      />
                    </div>
                  </div>
                </form>
              </fieldset>
            </div>
          </Card>

          {info?.product && (
            <Card containerStyle="p-4 m-4">
              <ProductComplianceInformation product={info.product} organization={info.organization} />
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
export default InvoiceLineForm;
