import { useMemo } from "react";
import { UnitOfMeasureType } from "../../../Shipment/Types.shipment";
import { CommercialInvoiceLineTable } from "../../Types.commercial-invoice";

const GoodsDescriptionWithMeasurements = ({
  value,
  row
}: {
  value: string;
  row: CommercialInvoiceLineTable;
}) => {
  const hasMeasurements = useMemo(() => row.measurements.length > 0, [row.measurements]);

  if (!hasMeasurements) return value;

  return (
    <div>
      <div className="truncate">{value}</div>
      {hasMeasurements && (
        <div className="leading-none">
          {row.measurements.map((measurement, i) => (
            <span key={measurement.type} className="text-xs text-gray-500 dark:text-gray-400">
              {i > 0 && " | "}
              {measurement.type !== UnitOfMeasureType.PER_UNIT
                ? `${measurement.type.toUpperCase()}: ${
                    measurement.value
                  } ${measurement.unitOfMeasure.toUpperCase()}`
                : `${measurement.value} ${measurement.unitOfMeasure.toUpperCase()}`}
            </span>
          ))}
        </div>
      )}
    </div>
  );
};

export default GoodsDescriptionWithMeasurements;
