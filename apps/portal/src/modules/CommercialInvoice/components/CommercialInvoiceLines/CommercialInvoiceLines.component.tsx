import { BaseError } from "@/common/Types.common";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";
import { MatchingRuleSourceDatabaseTable } from "@/modules/Compliance/Types.compliance";
import { PlusIcon } from "lucide-react";
import {
  CanadaAntiDumping,
  CanadaOgd,
  CommercialInvoiceLineColumn,
  EditCommercialInvoiceLineWithIdDto,
  ValidateCommercialInvoiceLineComplianceResponseDto
} from "nest-modules";
import { useCallback, useEffect, useMemo, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { Button, Table } from "ui";
import { formatCurrency, isEmptyObject, SortOrder, sortTable } from "utils";
import { useBatchSaveCommercialInvoiceLine } from "../../mutations";
import {
  commercialInvoiceLineTableSchema,
  issueInvoiceLineTableSchema
} from "../../Schema.commercial-invoice";
import {
  BatchSaveCommercialInvoiceLineParams,
  CommercialInvoiceLineTable,
  CommercialInvoiceTable,
  GetCommercialInvoiceLineList,
  MultipleFilings,
  MultipleInvoiceLineFormModal,
  NonCompliantReason
} from "../../Types.commercial-invoice";
import CreateMultipleFilingsModal from "../CreateMultipleFilingsModal";
import EditMultipleInvoiceLineModal from "../EditMultipleInvoiceLineModal";
import { useGetInvoiceLinesWithOgd } from "@/modules/Compliance/queries";

type Props = {
  invoice: CommercialInvoiceTable;
  nonCompliantLines?: ValidateCommercialInvoiceLineComplianceResponseDto[];
  isComplianceFetched?: boolean;
};
function CommercialInvoiceLines({ invoice, nonCompliantLines, isComplianceFetched }: Props) {
  const navigate = useNavigate();
  const { canCrudBackoffice } = useBackofficePermission();
  const [filters, setFilters] = useState<Omit<GetCommercialInvoiceLineList, "invoiceId">>();
  const [selectedLines, setSelectedLines] = useState<CommercialInvoiceLineTable[]>([]);
  const [multipleFilings, setMultipleFilings] = useState<MultipleFilings>();
  const [editMultipleModal, toggleEditMultipleModal] = useReducer((r) => !r, false);
  const [createMultipleFilingsModal, toggleCreateMultipleFilingsModal] = useReducer((r) => !r, false);

  const batchSave = useBatchSaveCommercialInvoiceLine();

  const { data, error, isFetching, refetchAll } = useGetInvoiceLinesWithOgd(
    {
      limit: 1000,
      invoiceId: invoice.id
    },
    nonCompliantLines,
    isComplianceFetched
  );

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const sortedEntries = useMemo(
    () => sortTable(data?.commercialInvoiceLines, filters?.sortBy, filters?.sortOrder),
    [data?.commercialInvoiceLines, filters?.sortBy, filters?.sortOrder]
  );

  const handleEditMultipleLine = useCallback(
    async (params: MultipleInvoiceLineFormModal) => {
      const formatBatchParams: BatchSaveCommercialInvoiceLineParams = {
        edit: selectedLines.map((line) => {
          return { ...(params as EditCommercialInvoiceLineWithIdDto), id: line.id };
        })
      };

      try {
        await batchSave.mutateAsync({ ...formatBatchParams, id: invoice.id });

        toast.success("Invoice lines updated");
        toggleEditMultipleModal();
        refetchAll();

        setTimeout(() => {
          setSelectedLines([]);
        }, 100);
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [batchSave, invoice.id, refetchAll, selectedLines]
  );

  // Handle create multiple filings
  useEffect(() => {
    const uniqueProducts = data?.commercialInvoiceLines?.filter(
      (line, index, self) =>
        line.hasIssue && index === self.findIndex((t) => t.product.id === line.product.id)
    );
    if (uniqueProducts?.length === 0) {
      setMultipleFilings(undefined);
      return;
    }

    const targetReasons = [NonCompliantReason.MISSING_OGD_FILING, NonCompliantReason.MISSING_SIMA_FILING];

    const groupedByCompliance = uniqueProducts?.reduce((acc, line) => {
      line.nonCompliantRecords?.forEach((record) => {
        if (targetReasons.includes(record.reason)) {
          // For OGD filing
          if (record.reason === NonCompliantReason.MISSING_OGD_FILING && record.sourceRecord) {
            const agencyName = `${(record.sourceRecord as CanadaOgd).agency.toUpperCase()}_${(
              record.sourceRecord as CanadaOgd
            ).program.replaceAll(" ", "_")}`;

            if (!acc[agencyName]) {
              acc[agencyName] = {
                sourceTable: MatchingRuleSourceDatabaseTable.CANADA_OGD,
                sourceRecord: record.sourceRecord as CanadaOgd,
                lines: [],
                products: []
              };
            }
            if (!acc[agencyName].lines.some((p) => p.product.id === line.product.id)) {
              acc[agencyName].lines.push(line);
              acc[agencyName].products.push(line.product);
            }
          } else {
            // For sima filing
            if (!acc["SIMA"]) {
              acc["SIMA"] = {
                sourceTable: MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE,
                measureInForce: record.sourceRecord as CanadaAntiDumping,
                lines: [],
                products: []
              };
            }
            if (!acc["SIMA"].lines.some((p) => p.product.id === line.product.id)) {
              acc["SIMA"].lines.push(line);
              acc["SIMA"].products.push(line.product);
            }
          }
        }
      });
      return acc;
    }, {} as MultipleFilings);

    setMultipleFilings(groupedByCompliance);
  }, [data?.commercialInvoiceLines]);

  return (
    <div className="flex flex-col gap-3">
      {editMultipleModal && (
        <EditMultipleInvoiceLineModal
          show={editMultipleModal}
          isLoading={batchSave.isPending}
          onSubmit={handleEditMultipleLine}
          onClose={toggleEditMultipleModal}
        />
      )}
      {createMultipleFilingsModal && multipleFilings && (
        <CreateMultipleFilingsModal
          show={createMultipleFilingsModal}
          filings={multipleFilings}
          organization={invoice.organization}
          onClose={toggleCreateMultipleFilingsModal}
        />
      )}

      <div className="flex items-center justify-between gap-2">
        <h5>Invoice Lines</h5>
        <div className="flex items-center gap-2">
          {selectedLines.length > 0 && (
            <Button
              label="Edit Selected"
              kind="outline"
              onClick={toggleEditMultipleModal}
              disabled={batchSave.isPending}
            />
          )}
          {!isEmptyObject(multipleFilings) && (
            <Button
              label="Create Filings"
              kind="outline"
              onClick={toggleCreateMultipleFilingsModal}
              loading={batchSave.isPending}
            />
          )}
          {canCrudBackoffice && (
            <Button
              label="Add Line"
              icon={<PlusIcon size={16} />}
              onClick={() =>
                navigate(`${location.pathname}/${invoice.id}/line/create`, {
                  state: {
                    invoice
                  }
                })
              }
            />
          )}
        </div>
      </div>

      <Table
        data={sortedEntries}
        isLoading={isFetching}
        schema={nonCompliantLines?.length ? issueInvoiceLineTableSchema : commercialInvoiceLineTableSchema}
        checklist
        multiple
        selectAll
        showTopScrollbar
        selected={selectedLines}
        onClick={(line: CommercialInvoiceLineTable) => {
          navigate(`${location.pathname}/${invoice.id}/line/${line.id}`, {
            state: {
              invoice
            }
          });
        }}
        onMultiSelect={setSelectedLines}
        onSort={(key?: string, sortOrder?: SortOrder) => {
          setFilters((prev) => ({
            ...prev,
            sortBy: key as CommercialInvoiceLineColumn,
            sortOrder
          }));
        }}
        sortKey={filters?.sortBy}
        sortDirection={filters?.sortOrder}
        summary={
          data?.totals && (
            <tr className="border-t border-gray-200 dark:border-neutral-700 font-semibold">
              <td></td>
              {Object.entries(
                nonCompliantLines?.length ? issueInvoiceLineTableSchema : commercialInvoiceLineTableSchema
              ).map(([key, column]) => {
                if (!column.visible) return null;
                const value =
                  data.totals?.[
                    `total${key.charAt(0).toUpperCase() + key.slice(1)}` as keyof typeof data.totals
                  ];
                return (
                  <td key={key} className="pl-4 pr-1 lg:pl-6 py-2 text-start text-sm">
                    {value !== undefined && typeof value === "number"
                      ? formatCurrency(value, invoice.currency)
                      : ""}
                  </td>
                );
              })}
            </tr>
          )
        }
      />
    </div>
  );
}

export default CommercialInvoiceLines;
