// import { SelectSourceModal } from "@/modules/Compliance/components";
// import { useProductMatchingRules, useSearchTariff } from "@/modules/Compliance/queries";
// import { CompliancePages } from "@/modules/Compliance/Routes.compliance";
// import { MatchingRuleSourceDatabaseTable, SourceTable } from "@/modules/Compliance/Types.compliance";
// import { SelectCountry } from "@/modules/Location/components";
// import { Country } from "@/modules/Location/Types.location";
// import { ProductComplianceInformation, SelectProductModal } from "@/modules/Product/components";
// import { Product } from "@/modules/Product/Types.product";
// import { UNIT_OF_MEASURE } from "@/modules/Shipment/Constant.shipment";
// import { useFormik } from "formik";
// import { CircleAlert } from "lucide-react";
// import { CanadaOgd, NonCompliantRecordDto } from "nest-modules";
// import { useCallback, useEffect, useReducer, useState } from "react";
// import toast from "react-hot-toast";
// import { useLocation, useNavigate } from "react-router-dom";
// import { Button, Icons, Input, Modal, Select, Textarea } from "ui";
// import { emptyStringToNull, formikInputOpts, getFormikError, kebabCaseToCapitalize, Nullable } from "utils";
// import { useGetCommercialInvoiceLineDetail } from "../../queries";
// import { saveCommercialInvoiceLineSchema } from "../../Schema.commercial-invoice";
// import {
//   CommercialInvoiceLineTable,
//   InvoiceLineFormModal,
//   SaveCommercialInvoiceLineParams
// } from "../../Types.commercial-invoice";

// type Props = {
//   show: boolean;
//   invoiceLine?: CommercialInvoiceLineTable;
//   invoiceId: number;
//   isLoading?: boolean;
//   readOnly?: boolean;
//   onSubmit(partner: SaveCommercialInvoiceLineParams): void;
//   onClose(): void;
//   onDelete(): void;
// };
// function AddInvoiceLineModal({
//   show,
//   invoiceLine,
//   invoiceId,
//   isLoading,
//   readOnly,
//   onSubmit,
//   onClose,
//   onDelete
// }: Props) {
//   const navigate = useNavigate();
//   const location = useLocation();
//   const [modalValues, setModalValues] = useState<Nullable<InvoiceLineFormModal>>();
//   const [productModal, toggleProductModal] = useReducer((r) => !r, false);
//   const [sourceModal, setSourceModal] = useState<MatchingRuleSourceDatabaseTable>();
//   // auto-populate origin, vfd & tt
//   const [country, setCountry] = useState<Country>();
//   const [productId, setProductId] = useState<number>();
//   const { data: compliance } = useProductMatchingRules(productId, [
//     MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE,
//     MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE
//   ]);
//   // auto-populate UoM
//   const [hsCode, setHsCode] = useState<string>();
//   const { data: tariff } = useSearchTariff(hsCode);

//   const {
//     data: info,
//     error,
//     isFetching
//   } = useGetCommercialInvoiceLineDetail({
//     invoiceId,
//     id: invoiceLine?.id
//   });

//   const handleSubmit = useCallback(
//     (params: SaveCommercialInvoiceLineParams) => {
//       const formatParams: SaveCommercialInvoiceLineParams = {
//         ...emptyStringToNull(params),
//         quantity: params.quantity ? Number(params.quantity) : undefined,
//         unitPrice: params.unitPrice ? Number(params.unitPrice) : undefined,
//         totalLineValue: params.totalLineValue ? Number(params.totalLineValue) : undefined,
//         originId: params.originId ? Number(params.originId) : undefined
//       };
//       onSubmit(formatParams);
//     },
//     [onSubmit]
//   );

//   const { setFieldValue, ...formik } = useFormik({
//     initialValues: {
//       id: info?.id ?? undefined,
//       invoiceId,
//       goodsDescription: info?.goodsDescription ?? "",
//       productId: info?.product?.id,
//       vfdId: info?.vfd?.id,
//       ttId: info?.tt?.id,
//       hsCode: info?.hsCode ?? "",
//       quantity: info?.quantity,
//       unitOfMeasure: info?.unitOfMeasure,
//       unitPrice: info?.unitPrice || undefined,
//       totalLineValue: info?.totalLineValue,
//       additionalInfo: info?.additionalInfo ?? "",
//       originId: info?.origin?.id
//     },
//     validationSchema: saveCommercialInvoiceLineSchema,
//     validateOnBlur: true,
//     enableReinitialize: true,
//     onSubmit: handleSubmit
//   });

//   //#region Modal handler
//   const handleClose = useCallback(() => {
//     formik.resetForm();
//     setModalValues(undefined);
//     onClose();
//   }, [formik, onClose]);

//   useEffect(() => {
//     if (error) {
//       toast.error(error.message);
//       handleClose();
//     }
//   }, [error, handleClose]);

//   useEffect(() => {
//     setModalValues((prev) => ({
//       ...prev,
//       product: info?.product,
//       vfd: info?.vfd,
//       tt: info?.tt
//     }));
//   }, [info]);

//   useEffect(() => {
//     setCountry(info?.origin as Country);
//   }, [info?.origin]);

//   useEffect(() => {
//     return () => setCountry(undefined);
//   }, []);

//   const handleSelectProduct = useCallback(
//     (product?: Product) => {
//       setModalValues((prev) => ({ ...prev, product }));
//       setFieldValue("productId", product?.id);
//       setFieldValue("hsCode", product?.hsCode);
//       setFieldValue("goodsDescription", product?.description);
//       setFieldValue("originId", product?.origin?.id);

//       setCountry(product?.origin);
//       setProductId(product?.id);
//       setHsCode(product?.hsCode);

//       toggleProductModal();
//     },
//     [setFieldValue]
//   );

//   const handleSourceModal = useCallback(
//     (source: SourceTable, _sourceModal?: MatchingRuleSourceDatabaseTable) => {
//       switch (_sourceModal) {
//         case MatchingRuleSourceDatabaseTable.CANADA_TARIFF:
//           setFieldValue("hsCode", source?.hsCode);
//           setFieldValue("unitOfMeasure", source?.uom);
//           break;
//         case MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE:
//           setModalValues((prev) => ({ ...prev, tt: source }));
//           setFieldValue("ttId", source?.id);
//           break;
//         case MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE:
//           setModalValues((prev) => ({ ...prev, vfd: source }));
//           setFieldValue("vfdId", source?.id);
//           break;
//       }
//     },
//     [setFieldValue]
//   );

//   useEffect(() => {
//     if (compliance && compliance.length > 0) {
//       for (const item of compliance) {
//         handleSourceModal(item.sourceRecords[0] as SourceTable, item.sourceTable);
//       }
//     }
//   }, [compliance, handleSourceModal]);

//   useEffect(() => {
//     if (tariff && tariff.data.length > 0) {
//       setFieldValue("unitOfMeasure", tariff.data[0]?.uom);
//     }
//   }, [setFieldValue, tariff]);

//   const handleSelectSource = useCallback(
//     (source: SourceTable) => {
//       handleSourceModal(source, sourceModal);
//       setSourceModal(undefined);
//     },
//     [handleSourceModal, sourceModal]
//   );

//   const handleComplianceIssue = useCallback(
//     (record: NonCompliantRecordDto) => {
//       navigate(CompliancePages.Detail("create"), {
//         state: {
//           sourceTable:
//             record.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING
//               ? MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE
//               : record.sourceTable,
//           sourceRecord:
//             record.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_OGD
//               ? record.sourceRecord
//               : undefined,
//           product: info?.product,
//           from: location.pathname
//         }
//       });
//     },
//     [info?.product, location.pathname, navigate]
//   );
//   //#endregion
//   return (
//     <Modal
//       id="add-invoice-line-modal"
//       show={show}
//       onClose={handleClose}
//       size="xl"
//       title={`${info ? "Edit" : "Add"} Invoice Line`}
//       {...(!readOnly && {
//         actions: (
//           <>
//             {info && (
//               <Button
//                 label="Delete"
//                 kind="delete"
//                 loading={isLoading}
//                 disabled={formik.dirty}
//                 onClick={onDelete}
//               />
//             )}
//             <Button
//               label={info ? "Update" : "Add"}
//               kind="create"
//               loading={isLoading}
//               onClick={formik.handleSubmit}
//             />
//           </>
//         )
//       })}
//     >
//       {productModal && (
//         <SelectProductModal
//           show={productModal}
//           onClose={toggleProductModal}
//           onSelect={handleSelectProduct}
//           required
//         />
//       )}
//       {sourceModal && (
//         <SelectSourceModal
//           show={!!sourceModal}
//           source={sourceModal}
//           onClose={() => setSourceModal(undefined)}
//           onSelect={handleSelectSource}
//           hsCodeLength="10"
//           canSelect={(item) => {
//             if (sourceModal === MatchingRuleSourceDatabaseTable.CANADA_TARIFF)
//               return item.hsCode?.length === 10;

//             return true;
//           }}
//         />
//       )}

//       {isFetching ? (
//         <Icons.Loader />
//       ) : (
//         <div className="flex flex-col gap-2">
//           {invoiceLine?.nonCompliantRecords && (
//             <div className="flex gap-2 p-2 bg-red-50 rounded-md border border-red-300">
//               <div className="shrink-0">
//                 <CircleAlert className="size-6 text-red-600" />
//               </div>
//               <div className="flex flex-col gap-2">
//                 <h5>There's an issue with this invoice line</h5>
//                 {invoiceLine.nonCompliantRecords?.map((r) => (
//                   <span
//                     key={r.sourceTable}
//                     className="hover:underline cursor-pointer"
//                     onClick={() => handleComplianceIssue(r)}
//                   >
//                     {r.sourceTable === MatchingRuleSourceDatabaseTable.CANADA_OGD
//                       ? `Missing ${(r.sourceRecord as CanadaOgd)?.agency?.toUpperCase()} ${
//                           (r.sourceRecord as CanadaOgd)?.program ?? ""
//                         } Filing`
//                       : kebabCaseToCapitalize(r.reason)}
//                   </span>
//                 ))}
//               </div>
//             </div>
//           )}

//           <fieldset disabled={readOnly}>
//             <form className="grid grid-cols-2 gap-x-4 gap-y-2">
//               <Input
//                 label="Product Part Number"
//                 name="productId"
//                 placeholder="Select product"
//                 value={modalValues?.product?.partNumber ?? ""}
//                 onClick={toggleProductModal}
//                 error={getFormikError(formik, "productId")}
//                 readOnly
//               />
//               <Input
//                 label="VFD"
//                 name="vfdId"
//                 placeholder="Select VFD"
//                 value={modalValues?.vfd?.displayName ?? ""}
//                 onClick={() => {
//                   setSourceModal(MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE);
//                 }}
//                 error={getFormikError(formik, "vfdId")}
//                 readOnly
//               />
//               <Input
//                 label="Treatment Code"
//                 name="ttId"
//                 placeholder="Select treatment code"
//                 value={modalValues?.tt?.displayName ?? ""}
//                 onClick={() => {
//                   setSourceModal(MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE);
//                 }}
//                 error={getFormikError(formik, "ttId")}
//                 readOnly
//               />
//               <Input
//                 label="HS Code"
//                 placeholder="Input HS Code"
//                 {...formikInputOpts(formik, "hsCode")}
//                 {...(!readOnly && {
//                   hint: (
//                     <div
//                       className="cursor-pointer underline text-xs"
//                       onClick={() => {
//                         setSourceModal(MatchingRuleSourceDatabaseTable.CANADA_TARIFF);
//                       }}
//                     >
//                       Search
//                     </div>
//                   )
//                 })}
//                 kind="search"
//               />
//               <div className="flex gap-2">
//                 <Input
//                   label="Quantity"
//                   placeholder="Qty"
//                   containerStyle="w-24"
//                   {...formikInputOpts(formik, "quantity")}
//                   onNumberChange={formik.handleChange}
//                 />
//                 <Select
//                   label="Unit"
//                   options={UNIT_OF_MEASURE}
//                   {...formikInputOpts(formik, "unitOfMeasure")}
//                   optional
//                 />
//               </div>
//               <SelectCountry
//                 name="originId"
//                 placeholder="Select Origin"
//                 value={country}
//                 onSelect={(value) => setFieldValue("originId", value?.value)}
//                 error={getFormikError(formik, "originId")}
//               />
//               <Input
//                 label="Unit Price"
//                 placeholder="Input unit price"
//                 {...formikInputOpts(formik, "unitPrice")}
//                 onNumberChange={formik.handleChange}
//               />
//               <Input
//                 label="Total Line Value"
//                 placeholder="Input total line value"
//                 {...formikInputOpts(formik, "totalLineValue")}
//                 onNumberChange={formik.handleChange}
//               />
//               <Textarea
//                 label="Goods Description"
//                 placeholder="Input goods description"
//                 {...formikInputOpts(formik, "goodsDescription")}
//               />
//               <Textarea
//                 label="Additional Info"
//                 placeholder="Input additional info"
//                 {...formikInputOpts(formik, "additionalInfo")}
//               />
//             </form>
//           </fieldset>

//           {info?.product && <ProductComplianceInformation product={info.product} />}
//         </div>
//       )}
//     </Modal>
//   );
// }
// export default AddInvoiceLineModal;
