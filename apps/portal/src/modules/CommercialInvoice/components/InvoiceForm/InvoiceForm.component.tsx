import { SelectCurrency } from "@/common/components";
import { BOOLEAN_OPTIONS } from "@/common/Constant.common";
import { useFallback } from "@/common/hooks/useFallback";
import { stateRequired } from "@/common/Utils.common";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";
import { ComplianceError } from "@/modules/Compliance/components";
import { objectToFilteredArray } from "@/modules/Compliance/Utils.compliance";
import { CreateFromAggregationMessage } from "@/modules/Document/components";
import { AggregationTargetType } from "@/modules/Document/Types.document";
import { SelectCountry, SelectState } from "@/modules/Location/components";
import { Country, CountryOption, State } from "@/modules/Location/Types.location";
import { PACKAGE, WEIGHT } from "@/modules/Shipment/Constant.shipment";
import { Shipment } from "@/modules/Shipment/Types.shipment";
import { SelectPartnerModal } from "@/modules/TradePartner/components";
import { <PERSON><PERSON><PERSON>, TradePartner } from "@/modules/TradePartner/Types.partner";
import { partnerTypeAggregator } from "@/modules/TradePartner/Utils.partner";
import { FormikConfig, useFormik } from "formik";
import { ValidateCommercialInvoiceComplianceResponseDto } from "nest-modules";
import { useCallback, useEffect, useMemo, useReducer, useState } from "react";
import { Button, FieldItem, Input, InputHint, Modal, Select, Textarea } from "ui";
import {
  Nullable,
  capitalizeLetter,
  emptyStringToNull,
  formikInputOpts,
  getFormikError,
  isoDateToString,
  kebabCaseToCapitalize,
  localDateToUtc,
  pascalToCapitalize
} from "utils";
import { useGetCommercialInvoiceFallback } from "../../queries";
import { saveCommercialInvoiceSchema } from "../../Schema.commercial-invoice";
import type {
  CommercialInvoice,
  InvoiceFormModal,
  SaveCommercialInvoiceParams
} from "../../Types.commercial-invoice";

type Props = {
  shipment: Shipment;
  invoice?: CommercialInvoice;
  isLoading?: boolean;
  compliance?: ValidateCommercialInvoiceComplianceResponseDto;
  isFetchingCompliance?: boolean;
  // readOnly?: boolean;
  isDirty?: (dirty: boolean) => void;
  onSubmit(params: SaveCommercialInvoiceParams): void;
  onCancel?(): void;
  onDelete?(): void;
};

function InvoiceForm({
  shipment,
  invoice,
  isLoading,
  compliance,
  isFetchingCompliance,
  // readOnly,
  isDirty,
  onSubmit,
  onCancel,
  onDelete
}: Props) {
  const [partnerModal, togglePartnerModal] = useReducer((r) => !r, false);
  const [partnerType, setPartnerType] = useState<PartnerField>();
  const [modalValues, setModalValues] = useState<Nullable<InvoiceFormModal>>();
  const { canCrudBackoffice } = useBackofficePermission();

  const handleSubmit = useCallback(
    (params: SaveCommercialInvoiceParams) => {
      const formatParams: SaveCommercialInvoiceParams = {
        ...emptyStringToNull(params),
        invoiceDate: params.invoiceDate ? localDateToUtc(params.invoiceDate) : undefined,
        grossWeight: params.grossWeight ? Number(params.grossWeight) : undefined,
        numberOfPackages: params.numberOfPackages ? Number(params.numberOfPackages) : undefined,
        includedTransCost: params.includedTransCost ? Number(params.includedTransCost) : undefined,
        includedPackCost: params.includedPackCost ? Number(params.includedPackCost) : undefined,
        includedMiscCost: params.includedMiscCost ? Number(params.includedMiscCost) : undefined,
        excludedTransCost: params.excludedTransCost ? Number(params.excludedTransCost) : undefined,
        excludedPackCost: params.excludedPackCost ? Number(params.excludedPackCost) : undefined,
        excludedMiscCost: params.excludedMiscCost ? Number(params.excludedMiscCost) : undefined,
        countryOfExportId: params.countryOfExportId ? Number(params.countryOfExportId) : undefined,
        valueIncludesDuty: params.valueIncludesDuty ? Boolean(params.valueIncludesDuty) : undefined
      };
      onSubmit(formatParams);
    },
    [onSubmit]
  );

  const formikConfig: FormikConfig<SaveCommercialInvoiceParams> = {
    initialValues: {
      id: invoice?.id,
      shipmentId: shipment?.id,
      invoiceNumber: invoice?.invoiceNumber ?? "",
      invoiceDate: invoice?.invoiceDate ? isoDateToString(invoice.invoiceDate) : "",
      poNumber: invoice?.poNumber ?? "",
      grossWeight: invoice?.grossWeight ?? undefined,
      weightUOM: invoice?.weightUOM ?? undefined,
      currency: invoice?.currency ?? undefined,
      numberOfPackages: invoice?.numberOfPackages ?? undefined,
      packageUOM: invoice?.packageUOM ?? undefined,
      includedTransCost: invoice?.includedTransCost ?? undefined,
      includedPackCost: invoice?.includedPackCost ?? undefined,
      includedMiscCost: invoice?.includedMiscCost ?? undefined,
      excludedTransCost: invoice?.excludedTransCost ?? undefined,
      excludedPackCost: invoice?.excludedPackCost ?? undefined,
      excludedMiscCost: invoice?.excludedMiscCost ?? undefined,
      valueIncludesDuty: invoice?.valueIncludesDuty ?? undefined,
      additionalInfo: invoice?.additionalInfo ?? "",
      countryOfExportId: invoice?.countryOfExport?.id,
      stateRequired: stateRequired(invoice?.countryOfExport?.alpha2),
      stateOfExportId: invoice?.stateOfExport?.id,
      exporterId: invoice?.exporter?.id,
      vendorId: invoice?.vendor?.id,
      manufacturerId: invoice?.manufacturer?.id,
      purchaserId: invoice?.purchaser?.id,
      shipToId: invoice?.shipTo?.id
    },
    validationSchema: saveCommercialInvoiceSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit: handleSubmit,
    validateOnChange: true
  };

  const formik = useFormik(formikConfig);

  useEffect(() => {
    isDirty?.(formik.dirty);
  }, [formik.dirty, isDirty]);

  //#region Modal handler
  useEffect(() => {
    setModalValues((prev) => ({
      ...prev,
      exporter: invoice?.exporter,
      vendor: invoice?.vendor,
      manufacturer: invoice?.manufacturer,
      purchaser: invoice?.purchaser,
      shipTo: invoice?.shipTo
    }));
  }, [invoice]);

  const handleSelectPartner = useCallback(
    (_partner?: TradePartner) => {
      switch (partnerType) {
        case PartnerField.EXPORTER:
          setModalValues((prev) => ({ ...prev, exporter: _partner }));
          formik.setFieldValue("exporterId", _partner?.id);
          break;
        case PartnerField.VENDOR:
          setModalValues((prev) => ({ ...prev, vendor: _partner }));
          formik.setFieldValue("vendorId", _partner?.id);
          break;
        case PartnerField.MANUFACTURER:
          setModalValues((prev) => ({ ...prev, manufacturer: _partner }));
          formik.setFieldValue("manufacturerId", _partner?.id);
          break;
        case PartnerField.PURCHASER:
          setModalValues((prev) => ({ ...prev, purchaser: _partner }));
          formik.setFieldValue("purchaserId", _partner?.id);
          break;
        case PartnerField.SHIP_TO:
          setModalValues((prev) => ({ ...prev, shipTo: _partner }));
          formik.setFieldValue("shipToId", _partner?.id);
          break;
      }
      setPartnerType(undefined);
      togglePartnerModal();
    },
    [formik, partnerType]
  );

  const [infoModal, setInfoModal] = useState<TradePartner | null>();
  const infoDetails = useMemo(() => (infoModal ? objectToFilteredArray(infoModal) : undefined), [infoModal]);
  //#endregion

  const handleCountrySelect = useCallback(
    (value?: CountryOption) => {
      formik.setFieldValue("countryOfExportId", value?.value ?? "");

      formik.setFieldValue("stateRequired", stateRequired(value?.alpha2));

      if (!value || !stateRequired(value?.alpha2)) {
        formik.setFieldValue("stateOfExportId", undefined);
      }
    },
    [formik]
  );

  //#region Fallback
  const { data: invoiceFallback } = useGetCommercialInvoiceFallback({
    id: invoice?.id
  });

  const { getFallbackPlaceholder, getFallbackValue } = useFallback(invoiceFallback);
  //#endregion

  return (
    <div>
      {partnerModal && (
        <SelectPartnerModal
          show={partnerModal}
          partnerType={partnerTypeAggregator(partnerType)}
          partner={modalValues?.[partnerType as keyof typeof modalValues] as TradePartner | null | undefined}
          onClose={() => {
            setPartnerType(undefined);
            togglePartnerModal();
          }}
          onSelect={handleSelectPartner}
        />
      )}
      <Modal
        id="info-modal"
        title="Details"
        show={!!infoModal}
        onClose={() => {
          setInfoModal(undefined);
        }}
      >
        <div className="grid gap-1">
          {infoDetails?.map(([key, value]) => (
            <FieldItem
              key={key}
              direction="row"
              label={`${capitalizeLetter(pascalToCapitalize(key))}:`}
              value={kebabCaseToCapitalize(value)}
            />
          ))}
        </div>
      </Modal>

      <div className="flex flex-col gap-3">
        <div className="flex items-center justify-between gap-2">
          <h5>Invoice Header</h5>
          <div className="flex items-center gap-3">
            {onCancel && <Button label="Cancel" kind="cancel" onClick={onCancel} />}
            {invoice && canCrudBackoffice && (
              <Button
                label="Delete"
                kind="delete"
                loading={isLoading}
                disabled={formik.dirty || isFetchingCompliance}
                onClick={onDelete}
              />
            )}
            {canCrudBackoffice && (
              <Button
                label={invoice ? "Update" : "Create"}
                kind="update"
                loading={isLoading}
                onClick={formik.handleSubmit}
                disabled={isFetchingCompliance}
              />
            )}
          </div>
        </div>

        <CreateFromAggregationMessage
          id={invoice?.id}
          targetType={AggregationTargetType.COMMERCIAL_INVOICE}
        />
        <ComplianceError title="Invoice" compliance={compliance} />

        <fieldset disabled={isFetchingCompliance} className="flex flex-col gap-3">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-2">
            <Input
              label="Invoice No."
              placeholder="Input Invoice No."
              {...formikInputOpts(formik, "invoiceNumber")}
            />
            <Input
              label="Invoice Date"
              placeholder="Input Invoice Date"
              type="date"
              {...formikInputOpts(formik, "invoiceDate")}
            />
            <Input label="PO No." placeholder="Input PO No." {...formikInputOpts(formik, "poNumber")} />
            <Input
              label="Gross Weight"
              placeholder="Input Gross Weight"
              {...formikInputOpts(formik, "grossWeight")}
              onNumberChange={formik.handleChange}
            />
            <Select label="Weight UOM" options={WEIGHT} {...formikInputOpts(formik, "weightUOM")} optional />
            <Input
              label="Number of Packages"
              placeholder="Input Number of Packages"
              {...formikInputOpts(formik, "numberOfPackages")}
              onNumberChange={formik.handleChange}
            />
            <Select
              label="Package UOM"
              options={PACKAGE}
              {...formikInputOpts(formik, "packageUOM")}
              optional
            />
            <SelectCurrency
              {...formikInputOpts(formik, "currency")}
              onSelect={(value) => formik.setFieldValue("currency", value)}
            />
            <Select
              label="Value Includes Duty"
              options={BOOLEAN_OPTIONS}
              {...formikInputOpts(formik, "valueIncludesDuty")}
              optional
            />
            <SelectCountry
              label="Country of Export"
              name="countryOfExportId"
              placeholder="Select Country of Export"
              value={invoice?.countryOfExport as Country}
              onSelect={handleCountrySelect}
              onBlur={formik.handleBlur}
              error={getFormikError(formik, "countryOfExportId")}
            />
            {formik.values.countryOfExportId && formik.values.stateRequired && (
              <SelectState
                label="State of Export"
                name="stateOfExportId"
                placeholder="Select State of Export"
                value={invoice?.stateOfExport as State}
                countryId={formik.values.countryOfExportId}
                onSelect={(value) => formik.setFieldValue("stateOfExportId", value ?? "")}
                onBlur={formik.handleBlur}
                error={getFormikError(formik, "stateOfExportId")}
              />
            )}
            <Textarea
              label="Additional Info"
              placeholder="Input Additional Info"
              {...formikInputOpts(formik, "additionalInfo")}
            />
          </div>
          <div className="flex gap-3">
            <div className="flex-1 flex flex-col gap-2">
              <h5>Included Costs</h5>
              <div className="flex gap-3 flex-wrap">
                <Input
                  label="Transportation Cost"
                  placeholder="Input Transportation Cost"
                  {...formikInputOpts(formik, "includedTransCost")}
                  onNumberChange={formik.handleChange}
                />
                <Input
                  label="Packing Cost"
                  placeholder="Input Packing Cost"
                  {...formikInputOpts(formik, "includedPackCost")}
                  onNumberChange={formik.handleChange}
                />
                <Input
                  label="Miscellaneous Cost"
                  placeholder="Input Miscellaneous Cost"
                  {...formikInputOpts(formik, "includedMiscCost")}
                  onNumberChange={formik.handleChange}
                />
              </div>
            </div>
            <div className="flex-1 flex flex-col gap-2">
              <h5>Excluded Costs</h5>
              <div className="flex gap-3 flex-wrap">
                <Input
                  label="Transportation Cost"
                  placeholder="Input Transportation Cost"
                  {...formikInputOpts(formik, "excludedTransCost")}
                  onNumberChange={formik.handleChange}
                />
                <Input
                  label="Packing Cost"
                  placeholder="Input Packing Cost"
                  {...formikInputOpts(formik, "excludedPackCost")}
                  onNumberChange={formik.handleChange}
                />
                <Input
                  label="Miscellaneous Cost"
                  placeholder="Input Miscellaneous Cost"
                  {...formikInputOpts(formik, "excludedMiscCost")}
                  onNumberChange={formik.handleChange}
                />
              </div>
            </div>
          </div>

          <h5>Trade Partners</h5>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-2">
            <Input
              label="Vendor"
              name="vendorId"
              placeholder="Select Vendor"
              value={modalValues?.vendor?.name ?? ""}
              onClick={() => {
                setPartnerType(PartnerField.VENDOR);
                togglePartnerModal();
              }}
              error={getFormikError(formik, "vendorId")}
              readOnly
              {...(modalValues?.vendor && {
                hint: <InputHint onClick={() => setInfoModal(modalValues.vendor)} />
              })}
            />
            <Input
              label="Exporter"
              name="exporterId"
              placeholder="Select Exporter"
              value={modalValues?.exporter?.name ?? ""}
              onClick={() => {
                setPartnerType(PartnerField.EXPORTER);
                togglePartnerModal();
              }}
              error={getFormikError(formik, "exporterId")}
              readOnly
              {...(modalValues?.exporter && {
                hint: <InputHint onClick={() => setInfoModal(modalValues.exporter)} />
              })}
            />
            <Input
              label="Manufacturer"
              name="manufacturerId"
              placeholder="Select Manufacturer"
              value={modalValues?.manufacturer?.name ?? ""}
              onClick={() => {
                setPartnerType(PartnerField.MANUFACTURER);
                togglePartnerModal();
              }}
              error={getFormikError(formik, "manufacturerId")}
              readOnly
              {...(modalValues?.manufacturer && {
                hint: <InputHint onClick={() => setInfoModal(modalValues.manufacturer)} />
              })}
            />
            <Input
              label="Purchaser"
              name="purchaserId"
              placeholder="Select Purchaser"
              value={modalValues?.purchaser?.name ?? ""}
              onClick={() => {
                setPartnerType(PartnerField.PURCHASER);
                togglePartnerModal();
              }}
              error={getFormikError(formik, "purchaserId")}
              readOnly
              {...(modalValues?.purchaser && {
                hint: <InputHint onClick={() => setInfoModal(modalValues.purchaser)} />
              })}
            />
            <Input
              label="Ship To"
              name="shipToId"
              placeholder={getFallbackPlaceholder("shipTo", "Select Ship To", (result) => result?.name)}
              value={modalValues?.shipTo?.name ?? ""}
              onClick={() => {
                setPartnerType(PartnerField.SHIP_TO);
                togglePartnerModal();
              }}
              error={getFormikError(formik, "shipToId")}
              readOnly
              {...(modalValues?.shipTo && {
                hint: <InputHint onClick={() => setInfoModal(modalValues.shipTo)} />
              })}
              {...(getFallbackValue("shipTo") && {
                hint: <InputHint onClick={() => setInfoModal(getFallbackValue("shipTo"))} />
              })}
            />
          </div>
        </fieldset>
      </div>
    </div>
  );
}

export default InvoiceForm;
