import { CreateProductDto } from "nest-modules";
import { Currency, TableSchema } from "utils";
import { array, boolean, mixed, number, object, ObjectSchema, string, TestContext } from "yup";
import { IssueLabel } from "../Compliance/components";
import { IssueComplianceTable } from "../Compliance/Types.compliance";
import { PackageUOM, UnitOfMeasure, UnitOfMeasureType, WeightUOM } from "../Shipment/Types.shipment";
import { GoodsDescriptionWithMeasurements, OgdAgenciesLabel } from "./components";
import {
  CommercialInvoiceLineTable,
  CommercialInvoiceTable,
  DestinationProvince,
  OgdAgency,
  SafeguardSubjectCode,
  SaveCommercialInvoiceLineParams,
  SaveCommercialInvoiceParams,
  SpecialAuthorityTimeLimitType,
  SurtaxSubjectCode
} from "./Types.commercial-invoice";

//#region Table schema
export const collapsedInvoiceTableSchema: TableSchema<CommercialInvoiceTable> = {
  invoiceNumber: {
    header: "Invoice No.",
    visible: true,
    readOnly: true
  }
};
export const commInvoiceTableSchema: TableSchema<CommercialInvoiceTable> = {
  invoiceNumber: {
    header: "Invoice No.",
    visible: true,
    readOnly: true
  },
  invoiceDate: {
    header: "Invoice Date",
    renderer: "date",
    visible: true
  },
  poNumber: {
    header: "PO No.",
    visible: true
  },
  grossWeight: {
    header: "Gross Weight",
    renderer: "number"
  },
  weightUOM: {
    header: "Weight UOM",
    style: "uppercase"
  },
  includedTransCost: {
    header: "Included Trans Cost",
    renderer: "numberDecimal"
  },
  includedPackCost: {
    header: "Included Pack Cost",
    renderer: "numberDecimal"
  },
  includedMiscCost: {
    header: "Included Misc Cost",
    renderer: "numberDecimal"
  },
  excludedTransCost: {
    header: "Excluded Trans Cost",
    renderer: "numberDecimal"
  },
  excludedPackCost: {
    header: "Excluded Pack Cost",
    renderer: "numberDecimal"
  },
  excludedMiscCost: {
    header: "Excluded Misc Cost",
    renderer: "numberDecimal"
  },
  valueIncludesDuty: {
    header: "Value Includes Duty",
    renderer: "boolean"
  },
  additionalInfo: {
    header: "Additional Info"
  },
  countryOfExportName: {
    header: "Country of Export",
    visible: true,
    sortKey: "countryOfExportId"
  },
  exporterName: {
    header: "Exporter"
  },
  purchaserName: {
    header: "Purchaser"
  },
  vendorName: {
    header: "Vendor",
    visible: true,
    sortKey: "vendorId"
  },
  shipToName: {
    header: "Ship To",
    visible: true,
    sortKey: "shipToId"
  },
  currency: {
    header: "Currency",
    style: "uppercase",
    visible: true
  },
  totalValue: {
    header: "Total Value",
    renderer: "numberDecimal",
    visible: true,
    disableSort: true
  }
};
export const issueInvoiceTableSchema: TableSchema<IssueComplianceTable<CommercialInvoiceTable>> = {
  hasIssue: {
    header: "Status",
    renderer: ({ value }: { value: boolean }) => <IssueLabel value={value} />,
    visible: true,
    disableSort: true
  },
  ...commInvoiceTableSchema
};
export const commercialInvoiceLineTableSchema: TableSchema<CommercialInvoiceLineTable> = {
  sequence: {
    header: "Line No.",
    visible: true
  },
  partNumber: {
    header: "Part Number",
    visible: true
  },
  productType: {
    header: "Product Type",
    renderer: "kebabCase",
    visible: true
  },
  goodsDescription: {
    header: "Goods Description",
    style: "max-w-80 truncate",
    renderer: ({ value, row }: { value: string; row: CommercialInvoiceLineTable }) => (
      <GoodsDescriptionWithMeasurements value={value} row={row} />
    ),
    visible: true
  },
  hsCode: {
    header: "HS Code",
    visible: true
  },
  vfdCode: {
    header: "VFD",
    visible: true
  },
  ttCode: {
    header: "TT",
    visible: true
  },
  originName: {
    header: "Origin",
    visible: true
  },
  quantity: {
    header: "Quantity",
    renderer: "number",
    visible: true
  },
  unitOfMeasure: {
    header: "UoM",
    style: "uppercase",
    visible: true
  },
  unitPrice: {
    header: "Unit Price",
    renderer: "numberDecimal",
    visible: true
  },
  totalLineValue: {
    header: "Total Line Value",
    renderer: "numberDecimal",
    visible: true
  },
  ogdAgencies: {
    header: "OGD",
    renderer: ({ value }: { value: OgdAgency[] }) => <OgdAgenciesLabel value={value} />,
    visible: true
  }
};
export const issueInvoiceLineTableSchema: TableSchema<IssueComplianceTable<CommercialInvoiceLineTable>> = {
  hasIssue: {
    header: "Status",
    renderer: ({ value }: { value: boolean }) => <IssueLabel value={value} />,
    visible: true
    // disableSort: true
  },
  ...commercialInvoiceLineTableSchema
};
//#endregion

//#region Form schema
export const saveCommercialInvoiceSchema: ObjectSchema<SaveCommercialInvoiceParams> = object({
  id: number().optional(),
  shipmentId: number().required("Shipment is required"),
  invoiceNumber: string().required("Invoice number is required"),
  invoiceDate: string().optional(),
  currency: mixed<Currency>().required("Currency is required"),
  poNumber: string().optional(),
  grossWeight: number().required("Gross weight is required"),
  weightUOM: mixed<WeightUOM>().required("Weight UOM is required"),
  numberOfPackages: number().required("Number of packages is required"),
  packageUOM: mixed<PackageUOM>().required("Package UOM is required"),
  includedTransCost: number().optional(),
  includedPackCost: number().optional(),
  includedMiscCost: number().optional(),
  excludedTransCost: number().optional(),
  excludedPackCost: number().optional(),
  excludedMiscCost: number().optional(),
  valueIncludesDuty: boolean().optional(),
  additionalInfo: string().optional(),
  countryOfExportId: number().required("Country of export is required"),
  stateRequired: boolean().optional(),
  stateOfExportId: number().when("$stateRequired", {
    is: true,
    then: (schema) => schema.required("State of export is required"),
    otherwise: (schema) => schema.optional()
  }),
  exporterId: number().optional(),
  purchaserId: number().optional(),
  shipToId: number().optional(),

  vendorId: number().required("Vendor is required"),
  manufacturerId: number().optional()
});

const ALLOWED_DIFFERENCE = 0.01;

const testQtyUnitPriceTotalLineValue = (_: number, context: TestContext) => {
  const { quantity, unitPrice, totalLineValue } = context.parent;
  if (!quantity || quantity === 0) {
    return quantity * unitPrice === totalLineValue;
  }
  return Math.abs(totalLineValue / quantity - unitPrice) < ALLOWED_DIFFERENCE;
};

export const saveCommercialInvoiceLineSchema: ObjectSchema<SaveCommercialInvoiceLineParams> = object({
  id: number().optional(),
  candataId: number().optional(),
  invoiceId: number().required("Invoice is required"),
  goodsDescription: string().required("Goods description is required"),
  productId: number().when("temporaryProduct", {
    is: undefined,
    then: (schema) => schema.required("Product is required"),
    otherwise: (schema) => schema.optional()
  }),
  vfdId: number().optional(),
  ttId: number().optional(),
  hsCode: string().required("HS code is required").length(10, "HS code must be 10 characters"),
  quantity: number()
    .required("Quantity is required")
    .test("totalLineValue", " ", testQtyUnitPriceTotalLineValue),
  unitOfMeasure: mixed<UnitOfMeasure>().optional(),
  unitPrice: number()
    .required("Unit price is required")
    .test("totalLineValue", "Unit Price x Quantity does not match", testQtyUnitPriceTotalLineValue),
  totalLineValue: number()
    .required("Total line value is required")
    .test("totalLineValue", " ", testQtyUnitPriceTotalLineValue),
  totalLineValueCad: number().optional(),
  additionalInfo: string().optional(),
  originId: number().required("Origin is required"),
  stateRequired: boolean().optional(),
  originStateId: number().when("$stateRequired", {
    is: true,
    then: (schema) => schema.required("Origin state is required"),
    otherwise: (schema) => schema.optional()
  }),
  tariffCodeId: number().optional().nullable(),
  gstExemptCodeId: number().optional().nullable(),
  orderInCouncil: string().optional().max(17, "Maximum 17 characters"),
  authorityPermit: string().optional().max(17, "Maximum 17 characters"),
  timeLimitType: mixed<SpecialAuthorityTimeLimitType>().optional(),
  timeLimitStartDate: string().optional(),
  timeLimitEndDate: string().optional(),
  provincialAlcoholTax: number().optional(),
  provincialTobaccoTax: number().optional(),
  provincialCannabisExciseDuty: number().optional(),
  alcoholPercentage: number().optional(),
  exciseTaxCodeId: number().optional().nullable(),
  surtaxSubjectCode: mixed<SurtaxSubjectCode>().optional(),
  surtaxCode: string().when("surtaxSubjectCode", {
    is: SurtaxSubjectCode.SUBJECT,
    then: () => string().required("Surtax code is required"),
    otherwise: () => string().optional()
  }),
  dutiesReliefLicence: string().optional(),
  safeguardSubjectCode: mixed<SafeguardSubjectCode>().optional(),
  safeguardCode: string().when("safeguardSubjectCode", {
    is: SafeguardSubjectCode.SUBJECT,
    then: () => string().required("Safeguard code is required"),
    otherwise: () => string().optional()
  }),
  simaQuantity: number().optional(),
  simaUnitOfMeasure: mixed<UnitOfMeasure>().optional(),
  pstHst: number().optional(),
  destinationProvince: mixed<DestinationProvince>().optional(),
  measurements: array(
    object({
      type: mixed<UnitOfMeasureType>().required("Measurement type is required"),
      unitOfMeasure: mixed<UnitOfMeasure>().required("Measurement unit is required"),
      value: number()
        .required("Measurement value is required")
        .moreThan(0, "Measurement must be greater than 0")
    })
  ).defined(),
  temporaryProduct: mixed<CreateProductDto>().optional()
});
//#endregion
