import Http from "@/bootstrap/Http.bootstrap";
import { FallbackEnricherResponse } from "@/common/Types.common";
import {
  BatchUpdateCommercialInvoiceLinesResponseDto,
  ValidateCommercialInvoiceLineComplianceResponseDto
} from "nest-modules";
import { handleError, stringifyQueryParams } from "utils";
import type {
  BatchSaveCommercialInvoiceLineParams,
  CommercialInvoiceLine,
  CommercialInvoiceLineParams,
  GetCommercialInvoiceLineList,
  GetCommercialInvoiceLineListResponse,
  SaveCommercialInvoiceLineParams
} from "../Types.commercial-invoice";

const COMMERCIAL_INVOICE_ROUTE = "commercial-invoices";
/**
 * Get all commercial invoice lines
 *
 * @returns
 */
async function list({
  invoiceId,
  ...params
}: GetCommercialInvoiceLineList): Promise<GetCommercialInvoiceLineListResponse> {
  try {
    return await Http.get(stringifyQueryParams(`${COMMERCIAL_INVOICE_ROUTE}/${invoiceId}/lines`, params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get commercial invoice line detail
 *
 * @returns
 */
async function get({ id, invoiceId }: CommercialInvoiceLineParams): Promise<CommercialInvoiceLine> {
  try {
    return await Http.get(`${COMMERCIAL_INVOICE_ROUTE}/${invoiceId}/lines/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create commercial invoice
 *
 * @returns
 */
async function create({
  invoiceId,
  ...params
}: SaveCommercialInvoiceLineParams): Promise<CommercialInvoiceLine> {
  try {
    return await Http.post(`${COMMERCIAL_INVOICE_ROUTE}/${invoiceId}/lines`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit commercial invoice
 *
 * @returns
 */
async function edit({
  id,
  invoiceId,
  ...params
}: SaveCommercialInvoiceLineParams): Promise<CommercialInvoiceLine> {
  try {
    return await Http.put(`${COMMERCIAL_INVOICE_ROUTE}/${invoiceId}/lines/${id}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Delete commercial invoice
 *
 * @returns
 */
async function remove({ id, invoiceId }: CommercialInvoiceLineParams): Promise<void> {
  try {
    return await Http.delete(`${COMMERCIAL_INVOICE_ROUTE}/${invoiceId}/lines/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Batch save commercial invoice line
 *
 * @returns
 */
async function batchSave({
  id,
  ...params
}: BatchSaveCommercialInvoiceLineParams): Promise<BatchUpdateCommercialInvoiceLinesResponseDto> {
  try {
    return await Http.post(`${COMMERCIAL_INVOICE_ROUTE}/${id}/lines/batch`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get commercial invoice line compliance
 *
 * @returns
 */
async function getCompliance({
  id,
  invoiceId
}: CommercialInvoiceLineParams): Promise<ValidateCommercialInvoiceLineComplianceResponseDto> {
  try {
    return await Http.post(
      `${COMMERCIAL_INVOICE_ROUTE}/${invoiceId}/lines/${id}/validate-compliance`,
      undefined,
      {
        timeout: 60000
      }
    );
  } catch (error) {
    throw await handleError(error);
  }
}

async function getFallback({
  id,
  invoiceId,
  productId
}: CommercialInvoiceLineParams & {
  invoiceId?: number;
  productId?: number;
}): Promise<FallbackEnricherResponse> {
  try {
    return await Http.get(`${COMMERCIAL_INVOICE_ROUTE}/${invoiceId}/lines${id ? `/${id}` : ""}/fallbacks`, {
      searchParams: productId ? { productId } : undefined
    });
  } catch (error) {
    throw await handleError(error);
  }
}

export default { create, edit, remove, get, list, getCompliance, batchSave, getFallback };
