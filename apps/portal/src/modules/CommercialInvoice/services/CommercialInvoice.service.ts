import Http from "@/bootstrap/Http.bootstrap";
import { FallbackEnricherResponse } from "@/common/Types.common";
import { ValidateCommercialInvoiceComplianceResponseDto } from "nest-modules";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import type {
  CommercialInvoice,
  GetCommercialInvoiceList,
  GetCommercialInvoiceListResponse,
  SaveCommercialInvoiceParams
} from "../Types.commercial-invoice";

const COMMERCIAL_INVOICE_ROUTE = "commercial-invoices";
/**
 * Get all commercial invoice
 *
 * @returns
 */
async function list(params?: GetCommercialInvoiceList): Promise<GetCommercialInvoiceListResponse> {
  try {
    return await Http.get(stringifyQueryParams(COMMERCIAL_INVOICE_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get commercial invoice detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<CommercialInvoice> {
  try {
    return await Http.get(`${COMMERCIAL_INVOICE_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create commercial invoice
 *
 * @returns
 */
async function create(params: SaveCommercialInvoiceParams): Promise<CommercialInvoice> {
  try {
    return await Http.post(COMMERCIAL_INVOICE_ROUTE, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit commercial invoice
 *
 * @returns
 */
async function edit({ id, ...params }: SaveCommercialInvoiceParams): Promise<CommercialInvoice> {
  try {
    return await Http.put(`${COMMERCIAL_INVOICE_ROUTE}/${id}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Delete commercial invoice
 *
 * @returns
 */
async function remove({ id }: DetailParams): Promise<void> {
  try {
    return await Http.delete(`${COMMERCIAL_INVOICE_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get commercial invoice compliance
 *
 * @returns
 */
async function getCompliance({ id }: DetailParams): Promise<ValidateCommercialInvoiceComplianceResponseDto> {
  try {
    return await Http.post(`${COMMERCIAL_INVOICE_ROUTE}/${id}/validate-compliance`, undefined, {
      timeout: 60000
    });
  } catch (error) {
    throw await handleError(error);
  }
}

async function getFallback({ id }: DetailParams): Promise<FallbackEnricherResponse> {
  try {
    return await Http.get(`${COMMERCIAL_INVOICE_ROUTE}/${id}/fallbacks`);
  } catch (error) {
    throw await handleError(error);
  }
}

export { create, edit, get, getCompliance, getFallback, list, remove };
