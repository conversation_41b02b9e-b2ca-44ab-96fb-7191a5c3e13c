import { Country } from "@/common/Types.common";
import { makeAutoObservable, runInAction } from "mobx";
import { clearPersistedStore, makePersistable } from "mobx-persist-store";
import { TableSchema } from "utils";

type ColumnVisibility = Record<string, boolean>;
type VisibilitySettings = Record<string, ColumnVisibility>;

export default class SettingsStore {
  columnSettings: VisibilitySettings = {};
  country: Country = Country.CA;

  constructor() {
    makeAutoObservable(this);

    makePersistable(this, {
      name: "ClaroSettingsStore",
      properties: ["columnSettings", "country"],
      storage: localStorage
    });
  }

  getColumnSettings = <T>(key: string, defaultSchema: TableSchema<T>): TableSchema<T> => {
    const savedVisibility = this.columnSettings[key];
    if (!savedVisibility) return defaultSchema;

    const result = {} as TableSchema<T>;
    for (const [columnKey, column] of Object.entries(defaultSchema)) {
      (result as Record<string, typeof column>)[columnKey] = {
        ...column,
        visible: savedVisibility[columnKey] ?? column.visible
      };
    }
    return result;
  };

  setColumnSettings = <T>(key: string, schema: TableSchema<T>) => {
    runInAction(() => {
      this.columnSettings[key] = Object.entries(schema).reduce((acc, [columnKey, column]) => {
        acc[columnKey] = column.visible ?? false;
        return acc;
      }, {} as ColumnVisibility);
    });
  };

  setCountry = (country: Country) => {
    this.country = country;
  };

  clearSettings = async (): Promise<void> => {
    await clearPersistedStore(this);
  };
}
