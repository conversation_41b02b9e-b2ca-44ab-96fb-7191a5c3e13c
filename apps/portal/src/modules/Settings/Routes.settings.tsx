import { RouterObject } from "@/common/Types.common";
import { Navigate } from "react-router-dom";
import { MyProfile } from "../Auth/components";
import { ImporterList } from "../Importers/components";
import { LocationList } from "../Location/components";
import { TradePartnerList } from "../TradePartner/components";
import { Settings } from "./views";

const SettingsPages = {
  List: "/settings",
  Profile: "profile",
  Importers: "importers",
  TradePartners: "trade-partners",
  Locations: "locations"
};

const SettingsTab = (isSuperAdmin?: boolean) => {
  const tabs = [
    {
      title: "Importers",
      path: SettingsPages.Importers
    },
    {
      title: "Trade Partners",
      path: SettingsPages.TradePartners
    },
    {
      title: "Locations",
      path: SettingsPages.Locations
    }
  ];
  if (!isSuperAdmin) {
    tabs.unshift({
      title: "Profile",
      path: SettingsPages.Profile
    });
  }

  return tabs;
};

const SettingsRoutes = (isSuperAdmin?: boolean): RouterObject => [
  {
    path: SettingsPages.List,
    element: <Settings />,
    children: [
      {
        path: "",
        element: <Navigate to={isSuperAdmin ? SettingsPages.Importers : SettingsPages.Profile} replace />
      },
      {
        path: SettingsPages.Profile,
        element: !isSuperAdmin ? <MyProfile /> : <Navigate to={SettingsPages.Importers} replace />
      },
      { path: SettingsPages.Importers, element: <ImporterList /> },
      { path: SettingsPages.TradePartners, element: <TradePartnerList /> },
      { path: SettingsPages.Locations, element: <LocationList /> }
    ]
  }
];

export { SettingsPages, SettingsRoutes, SettingsTab };
