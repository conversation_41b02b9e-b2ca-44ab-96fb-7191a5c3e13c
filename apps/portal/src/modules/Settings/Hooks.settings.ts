import { useStore } from "@/bootstrap/Store.bootstrap";
import { showOrganizationTableSchema } from "@/common/Utils.common";
import { useCallback, useEffect, useState } from "react";
import { TableSchema } from "utils";

interface UseColumnVisibilityProps<T> {
  defaultColumns: TableSchema<T>;
  key: string;
}
export function useColumnVisibility<T>({ defaultColumns, key }: UseColumnVisibilityProps<T>) {
  const { getColumnSettings, setColumnSettings } = useStore().settings;
  const { isSuperAdmin, isAuthenticated } = useStore().auth;

  const [visibleColumns, setVisibleColumns] = useState<TableSchema<T>>(() =>
    getColumnSettings(key, showOrganizationTableSchema(defaultColumns, key, isSuperAdmin))
  );

  // Reset to default columns when logout
  useEffect(() => {
    if (!isAuthenticated) {
      setVisibleColumns(defaultColumns);
    }
  }, [defaultColumns, isAuthenticated, isSuperAdmin]);

  const handleToggleColumn = useCallback(
    (columns: TableSchema<T>) => {
      setVisibleColumns(columns);
      setColumnSettings(key, columns);
    },
    [key, setColumnSettings]
  );

  return {
    visibleColumns,
    handleToggleColumn
  };
}
