import { RouterTabs } from "@/components";
import { observer } from "mobx-react-lite";
import { Card } from "ui";
import { SettingsTab } from "../../Routes.settings";
import { useStore } from "@/bootstrap/Store.bootstrap";

const SettingsPage = observer(() => {
  const { isSuperAdmin } = useStore().auth;
  return (
    <div className="flex flex-col">
      <Card>
        <RouterTabs items={SettingsTab(isSuperAdmin)} tabItemStyle="p-4 text-sm" contentStyle="p-4 h-full" />
      </Card>
    </div>
  );
});
export default SettingsPage;
