import { parseAddress } from "@/common/Utils.common";
import { useFormik } from "formik";
import { useCallback } from "react";
import { toast } from "react-hot-toast";
import { AddressAutoComplete, Button, Input, PhoneNumberInput } from "ui";
import { emptyStringToNull, formikInputOpts } from "utils";
import { useSaveOnboarding } from "../../mutations";
import { importerSchema } from "../../Schema.auth";
import { OnboardingDocument, OnboardingImporter, OnboardingSteps } from "../../Types.auth";
import { env } from "@/config/Environment.config";

type Props = {
  onboarding: OnboardingDocument;
  onNextStep: (step: OnboardingSteps) => void;
  onRefetch?: () => void;
};
function ImporterInfo({ onboarding, onNextStep, onRefetch }: Props) {
  const saveOnboarding = useSaveOnboarding();

  const handleSubmit = useCallback(
    async (params?: OnboardingImporter) => {
      if (onboarding.id) {
        try {
          await saveOnboarding.mutateAsync({
            id: onboarding.id,
            step: OnboardingSteps.BUSINESS_VALIDATION,
            importer: params ? emptyStringToNull(params) : undefined
          });
          onRefetch?.();
          onNextStep(OnboardingSteps.BUSINESS_VALIDATION);
        } catch (error) {
          toast.error((error as Error).message);
        }
      }
    },
    [onNextStep, onRefetch, onboarding.id, saveOnboarding]
  );

  const formik = useFormik({
    initialValues: {
      companyName: onboarding.importer?.companyName ?? "",
      email: onboarding.importer?.email ?? onboarding.contactEmail ?? "",
      phoneNumber: onboarding.importer?.phoneNumber ?? "",
      fax: onboarding.importer?.fax ?? "",
      address: onboarding.importer?.address ?? "",
      city: onboarding.importer?.city ?? "",
      state: onboarding.importer?.state ?? "",
      postalCode: onboarding.importer?.postalCode ?? "",
      countryName: onboarding.importer?.countryName ?? "",
      officerNameAndTitle: onboarding.importer?.officerNameAndTitle ?? onboarding.name ?? ""
    },
    validationSchema: importerSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit: handleSubmit
  });

  const handleAddress = useCallback(
    async (place: google.maps.places.PlaceResult) => {
      const address = await parseAddress(place, true);
      if (address) {
        formik.setFieldValue("address", address.street);
        formik.setFieldValue("city", address.city);
        formik.setFieldValue("state", address.state);
        formik.setFieldValue("postalCode", address.zipCode);
        formik.setFieldValue("countryName", address.countryName);
      }
    },
    [formik]
  );

  return (
    <div className="flex flex-col gap-6 max-w-3xl">
      <div className="flex flex-col items-center text-justify gap-2">
        <h4>Enter Your Importer Information</h4>
        <div className="bg-secondary-100 p-4 rounded-lg">
          <p className="leading-5">
            To get started with Claro Customs, we need a few official details about your company. This
            information will be used to generate your <b>Power of Attorney (POA)</b> — a required document
            that authorizes Claro Customs to act on your behalf for customs processing.
          </p>
        </div>
      </div>

      <form>
        <fieldset className="flex flex-col gap-2">
          <div className="grid grid-cols-2 gap-4">
            <Input
              label="Company Full Name"
              placeholder="Input company full name"
              {...formikInputOpts(formik, "companyName")}
            />
            <Input
              label="Company Officer Name"
              placeholder="Input company officer name"
              {...formikInputOpts(formik, "officerNameAndTitle")}
            />
          </div>

          <div className="grid grid-cols-4 gap-4">
            <Input
              label="Contact Email"
              type="email"
              placeholder="Input contact email"
              {...formikInputOpts(formik, "email")}
            />
            <PhoneNumberInput
              placeholder="Input contact phone number"
              {...formikInputOpts(formik, "phoneNumber")}
              onChange={(value) => formik.setFieldValue("phoneNumber", value)}
            />
            <Input label="Fax Number" placeholder="Input fax number" {...formikInputOpts(formik, "fax")} />
          </div>

          <h6 className="mt-2">Company Address</h6>
          <div className="grid grid-cols-4 gap-4">
            <AddressAutoComplete
              apiKey={env.mapsApiKey}
              label="Address"
              placeholder="Input address"
              containerStyle="col-span-2"
              {...formikInputOpts(formik, "address")}
              onSelected={handleAddress}
            />
            <Input label="City" placeholder="Input city" {...formikInputOpts(formik, "city")} />
            <Input label="State" placeholder="Input state" {...formikInputOpts(formik, "state")} />
            <Input
              label="Postal Code"
              placeholder="Input postal code"
              {...formikInputOpts(formik, "postalCode")}
            />
            <Input
              label="Country"
              placeholder="Input country"
              {...formikInputOpts(formik, "countryName")}
              info="Country should be auto filled"
              disabled
            />
          </div>
        </fieldset>
      </form>

      <Button
        label="Next Step"
        className="self-center w-fit"
        loading={saveOnboarding.isPending}
        onClick={formik.handleSubmit}
      />
    </div>
  );
}

export default ImporterInfo;
