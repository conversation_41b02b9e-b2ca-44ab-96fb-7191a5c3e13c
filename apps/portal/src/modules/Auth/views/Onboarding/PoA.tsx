import { useFormik } from "formik";
import { useCallback } from "react";
import toast from "react-hot-toast";
import { Button, Input } from "ui";
import { formikInputOpts } from "utils";
import { useResendPoA, useSaveOnboarding } from "../../mutations";
import { resendPoaSchema } from "../../Schema.auth";
import { OnboardingDocument } from "../../Types.auth";

type Props = {
  onboarding: OnboardingDocument;
  onNextStep?: (step: number) => void;
  onRefetch?: () => void;
};
function PoA({ onboarding, onNextStep: _onNextStep, onRefetch }: Props) {
  const saveOnboarding = useSaveOnboarding();
  const resendPoA = useResendPoA();

  const handleResendEmail = useCallback(
    async (params: { email?: string }) => {
      const isEmailChanged = params.email !== onboarding.importer?.email;

      try {
        const poa = await resendPoA.mutateAsync({
          envelopeId: onboarding.envelopeId,
          ...(isEmailChanged && { importer: { ...onboarding.importer, email: params.email } })
        });

        if (isEmailChanged)
          await saveOnboarding.mutateAsync({
            ...onboarding,
            importer: { ...onboarding.importer, email: params.email },
            envelopeId: poa.data?.envelopeId
          });

        toast.success("Email resent successfully!");
        onRefetch?.();
      } catch (error) {
        toast.error((error as Error).message);
      }
    },
    [onRefetch, onboarding, resendPoA, saveOnboarding]
  );

  const formik = useFormik({
    initialValues: {
      email: onboarding.importer?.email ?? ""
    },
    validationSchema: resendPoaSchema,
    validateOnBlur: true,
    onSubmit: handleResendEmail
  });

  return (
    <div className="flex flex-col gap-2 items-center max-w-2xl mx-auto">
      <div className="flex flex-col items-center text-justify gap-2">
        <h4>Final Steps to Complete Your Onboarding</h4>
        <div className="bg-secondary-100 p-4 rounded-lg flex flex-col gap-2">
          <h5>You're almost done! Here’s what happens next:</h5>
          <div>
            <h6>Sign the Power of Attorney (POA)</h6>
            <p className="leading-5">
              The company officer you listed will receive a POA request via <b>DocuSign</b>. Please ensure
              they review and sign the document promptly.
            </p>
          </div>

          <div>
            <h6>Approve the CARM Request</h6>
            <p className="leading-5">
              Log in to your <b>CBSA CARM Client Portal</b> and approve the business relationship request sent
              by Claro Customs. If you're unsure how to do this, follow the step-by-step instructions in the
              video below.
              {/* (I will provide the Youtube URL) */}
            </p>
          </div>

          <div>
            <h6>Receive Your Claro Login</h6>
            <p className="leading-5">
              Once both the POA is signed and the CARM request is approved, you’ll receive an email invitation
              to set your password and log in to Claro Customs.
            </p>
          </div>
        </div>
      </div>

      <div className="flex items-start justify-center gap-4">
        <Input
          label="Email"
          type="email"
          placeholder="Enter your email"
          {...formikInputOpts(formik, "email")}
        />
        <Button
          label="Resend PoA"
          onClick={formik.handleSubmit}
          loading={resendPoA.isPending || saveOnboarding.isPending}
          className="mt-4"
        />
      </div>

      <p className="text-xs text-gray-600 italic">
        Allow 5-10 minutes for delivery. Please check your spam folder.
      </p>

      <div className="text-sm mt-2">
        Need help? <a className="text-primary-800 hover:underline">Contact Support</a>
      </div>
    </div>
  );
}

export default PoA;
