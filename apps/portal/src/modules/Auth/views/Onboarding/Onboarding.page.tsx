import { PoaStatus } from "@/modules/Importers/Types.importer";
import { useCallback, useEffect, useMemo, useState } from "react";
import toast from "react-hot-toast";
import { useBlocker, useNavigate, useParams } from "react-router-dom";
import { Card, Icons, Stepper } from "ui";
import { useGetOnboardingDetail } from "../../queries";
import { AuthPages } from "../../Routes.auth";
import { OnboardingSteps } from "../../Types.auth";
import { isIneligibleForOnboarding } from "../../Utils.auth";
import ImporterInfo from "./ImporterInfo";
import IntroVideo from "./IntroVideo";
import PoA from "./PoA";
import VerifyBusiness from "./VerifyBusiness";

function Onboarding() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [step, setStep] = useState(OnboardingSteps.INTRODUCTION_VIDEO);
  const [canOnboard, setCanOnboard] = useState(true);

  const { data: onboarding, error, isFetching, refetch } = useGetOnboardingDetail(id);

  useEffect(() => {
    if (onboarding) {
      if (
        (onboarding.step === OnboardingSteps.SURVEY && isIneligibleForOnboarding(onboarding)) ||
        (onboarding.step === OnboardingSteps.POA && onboarding.poaStatus === PoaStatus.SIGNED) ||
        onboarding.step === OnboardingSteps.COMPLETED
      ) {
        setCanOnboard(false);
      } else if (onboarding.step === OnboardingSteps.SURVEY) {
        setStep(OnboardingSteps.INTRODUCTION_VIDEO);
      } else {
        setStep(onboarding.step);
      }
    }

    if (error) {
      toast.error((error as Error).message);
      navigate(AuthPages.Login);
    }
  }, [error, navigate, onboarding]);

  const handleStepChange = useCallback((newStep: OnboardingSteps) => {
    if (newStep >= OnboardingSteps.INTRODUCTION_VIDEO && newStep <= OnboardingSteps.POA) {
      setStep(newStep);
    }
  }, []);

  const renderStep = useMemo(() => {
    if (!canOnboard) {
      if (onboarding?.step === OnboardingSteps.COMPLETED) {
        return (
          <div className="text-center">
            <h4>You are all set!</h4>
            <p>
              Please check your email for the link to create your password. The link will expire in 24 hours.
              Please go to forgot password page if the link is expired.
            </p>
          </div>
        );
      } else if (onboarding?.poaStatus === PoaStatus.SIGNED)
        return (
          <div className="text-center">
            <h4>Thank you for your interest in Claro</h4>
            <p>
              We are processing your application. Once approved, you will receive an email to create your
              account.
            </p>
          </div>
        );
      return (
        <div className="text-center">
          <h4>Thank you for your interest in Claro</h4>
          <p>We will be contacting you soon!</p>
        </div>
      );
    }
    if (!onboarding?.id) return null;

    switch (step) {
      case OnboardingSteps.INTRODUCTION_VIDEO:
        return <IntroVideo id={onboarding.id} onNextStep={handleStepChange} />;
      case OnboardingSteps.IMPORTER_INFORMATION:
        return <ImporterInfo onboarding={onboarding} onNextStep={handleStepChange} onRefetch={refetch} />;
      case OnboardingSteps.BUSINESS_VALIDATION:
        return <VerifyBusiness onboarding={onboarding} onNextStep={handleStepChange} onRefetch={refetch} />;
      case OnboardingSteps.POA:
        return <PoA onboarding={onboarding} onNextStep={handleStepChange} onRefetch={refetch} />;
    }
  }, [canOnboard, handleStepChange, onboarding, refetch, step]);

  //#region Block user to previous page
  useBlocker(!canOnboard);

  useEffect(() => {
    if (!canOnboard) {
      navigate(location.pathname, { replace: true });

      const handleBack = () => {
        navigate(0); // Refresh the page to prevent navigation
      };

      window.addEventListener("popstate", handleBack);
      return () => window.removeEventListener("popstate", handleBack);
    }
  }, [canOnboard, navigate]);
  //#endregion

  return (
    <div className="flex items-center justify-center h-screen bg-gradient-to-b from-white via-neutral-100 to-white dark:from-neutral-800 dark:to-neutral-900">
      <header className="fixed top-0 left-0 flex items-center p-8 w-full">
        <div className="basis-1/4">
          <Icons.Logo className="w-30 fill-primary dark:fill-white" />
        </div>

        {canOnboard && (
          <div className="basis-1/2 text-center w-96">
            {/* TODO remove onStepChange */}
            <Stepper
              steps={4}
              step={step - 1}
              // onStepChange={(step) => handleStepChange(step + 1)}
            />
          </div>
        )}
      </header>
      {isFetching ? <Icons.Loader /> : <Card containerStyle="p-6 items-center">{renderStep}</Card>}
    </div>
  );
}

export default Onboarding;
