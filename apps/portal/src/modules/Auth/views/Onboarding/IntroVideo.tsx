import { useCallback, useState } from "react";
import toast from "react-hot-toast";
import ReactPlayer from "react-player";
import { OnProgressProps } from "react-player/base";
import { Button } from "ui";
import { useSaveOnboarding } from "../../mutations";
import { OnboardingSteps, OnboardingVideo } from "../../Types.auth";

type Props = {
  id: string;
  onNextStep: (step: OnboardingSteps) => void;
};
function IntroVideo({ id, onNextStep }: Props) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [playedSeconds, setPlayedSeconds] = useState(0);
  const saveOnboarding = useSaveOnboarding();

  const handleSaveOnboarding = useCallback(
    async (params?: OnboardingVideo, step?: OnboardingSteps) => {
      try {
        await saveOnboarding.mutateAsync({
          id,
          step: step || OnboardingSteps.INTRODUCTION_VIDEO,
          playedSeconds,
          ...params
        });
      } catch (error) {
        toast.error((error as Error).message);
      }
    },
    [saveOnboarding, id, playedSeconds]
  );

  const handlePlay = async () => {
    setIsPlaying(true);
    await handleSaveOnboarding({
      videoPlayed: true
    });
  };

  const handlePause = async () => {
    setIsPlaying(false);
    await handleSaveOnboarding();
  };

  const handleEnded = async () => {
    setIsPlaying(false);
    await handleSaveOnboarding({
      videoCompleted: true
    });
    // if (analytics) {
    //   logEvent(analytics, "video_completed");
    // }
    // analytics.then((analyticsInstance) => {
    //   if (analyticsInstance) {
    //     logEvent(analyticsInstance, "video_completed");
    //   }
    // });
  };

  const handleProgress = useCallback(
    (state: OnProgressProps) => {
      if (isPlaying) setPlayedSeconds(state.playedSeconds);
      // if (analytics) {
      //   logEvent(analytics, "video_progress", { time_watched: state.playedSeconds, debug: true });
      // }
      // analytics.then((analyticsInstance) => {
      //   if (analyticsInstance) {
      //     logEvent(analyticsInstance, "video_progress", { time_watched: state.playedSeconds, debug: true });
      //   }
      // });
    },
    [isPlaying]
  );

  const handleNextStep = async () => {
    await handleSaveOnboarding(undefined, OnboardingSteps.IMPORTER_INFORMATION);
    onNextStep(OnboardingSteps.IMPORTER_INFORMATION);
  };

  return (
    <div className="flex flex-col gap-6 items-center">
      <h4>Introduction Video</h4>
      <ReactPlayer
        url="https://www.youtube-nocookie.com/embed/d38e_NjXuhw?si=polYts-2D4cjzkot"
        controls
        onPlay={handlePlay}
        onProgress={handleProgress}
        onEnded={handleEnded}
        onPause={handlePause}
      />
      <Button
        label="Next Step"
        disabled={playedSeconds < 1 || isPlaying || saveOnboarding.isPending}
        onClick={handleNextStep}
      />
    </div>
  );
}

export default IntroVideo;
