import { env } from "@/config/Environment.config";
import { CarmStatus, PoaStatus } from "@/modules/Importers/Types.importer";
import { arrayUnion } from "firebase/firestore";
import { useFormik } from "formik";
import { useCallback, useState } from "react";
import { toast } from "react-hot-toast";
import { Button, Input, InvalidBusinessNumber, ValidBusinessNumber } from "ui";
import { formikInputOpts } from "utils";
import { useCarmRequest, useSaveOnboarding, useSendPoA, useVerifyBusinessNumber } from "../../mutations";
import { verifyBusinessSchema } from "../../Schema.auth";
import { BusinessNumberValidationParams, OnboardingDocument, OnboardingSteps } from "../../Types.auth";

type Props = {
  onboarding: OnboardingDocument;
  onNextStep: (step: number) => void;
  onRefetch?: () => void;
};
function VerifyBusiness({ onboarding, onNextStep, onRefetch }: Props) {
  const [isValid, setIsValid] = useState<boolean>();
  const saveOnboarding = useSaveOnboarding();
  const verifyBusiness = useVerifyBusinessNumber();
  const sendPoA = useSendPoA();
  const carmRequest = useCarmRequest();

  const formik = useFormik({
    initialValues: {
      business_number: onboarding.importer?.businessNumber ?? "",
      business_name: onboarding.importer?.companyName ?? ""
    },
    validationSchema: verifyBusinessSchema,
    validateOnBlur: true,
    onSubmit: async (params: BusinessNumberValidationParams, props) => {
      setIsValid(undefined);

      try {
        const response = await verifyBusiness.mutateAsync({
          business_number: params.business_number.slice(0, 9),
          business_name: params.business_name
        });

        const _isValid = response.some((item) => item.isValid);
        setIsValid(_isValid);

        if (!_isValid) {
          await saveOnboarding.mutateAsync({
            id: onboarding.id,
            step: OnboardingSteps.BUSINESS_VALIDATION,
            failedBusinessVerification: arrayUnion({
              businessNumber: params.business_number.slice(0, 9),
              businessName: params.business_name,
              timestamp: new Date()
            })
          });

          formik.setFieldError("business_number", "Invalid Business Number");
        } else {
          props.resetForm({ values: params });
        }
      } catch (error) {
        toast.error((error as Error).message);
      }
    }
  });

  const handleNext = useCallback(async () => {
    if (onboarding.id && formik.isValid && isValid) {
      let carm;

      if (env.mode !== "development") {
        try {
          carm = await carmRequest.mutateAsync({
            businessNo: formik.values.business_number.slice(0, 9)
          });
          // TODO send email to backoffice if status is invalid
        } catch (error) {
          console.error("CARM request failed:", error);
        }
      }

      try {
        const importer = {
          ...onboarding.importer,
          businessNumber: formik.values.business_number,
          companyName: formik.values.business_name
        };

        const poa = await sendPoA.mutateAsync(importer);

        await saveOnboarding.mutateAsync({
          id: onboarding.id,
          step: OnboardingSteps.POA,
          importer,
          envelopeId: poa.data?.envelopeId ?? "",
          poaStatus: PoaStatus.SENT,
          carmStatus: carm?.result ?? null,
          ...(carm?.result === CarmStatus.INVALID && {
            failedCarmRequest: arrayUnion({
              businessNumber: formik.values.business_number.slice(0, 9),
              businessName: formik.values.business_name,
              status: carm?.result,
              timestamp: new Date()
            })
          })
        });

        onRefetch?.();
        onNextStep(OnboardingSteps.POA);
      } catch (error) {
        toast.error((error as Error).message);
      }
    }
  }, [
    carmRequest,
    formik.isValid,
    formik.values.business_name,
    formik.values.business_number,
    isValid,
    onNextStep,
    onRefetch,
    onboarding.id,
    onboarding.importer,
    saveOnboarding,
    sendPoA
  ]);

  return (
    <div className="flex flex-col gap-6 max-w-2xl">
      <div className="flex flex-col items-center text-justify gap-2">
        <h4>Verify Your Business Information</h4>
        <div className="bg-secondary-100 p-4 rounded-lg flex flex-col gap-2">
          <p className="leading-5">
            To complete your setup with Claro Customs, we need to verify your business identity with the
            Canada Revenue Agency (CRA).
          </p>
          <p className="leading-5">
            Please enter your <b>15-digit Business Number (BN15)</b> or <b>Importer Account Number</b>, such
            as 123456789RM0001. This number will be used for all importation activities.
          </p>
          <p className="leading-5">
            Once submitted, our system will automatically verify that the number is valid and matches the
            company name you provided.
          </p>
        </div>
      </div>

      <form>
        <fieldset
          className="flex flex-col gap-3"
          disabled={
            saveOnboarding.isPending || verifyBusiness.isPending || sendPoA.isPending || carmRequest.isPending
          }
        >
          <InvalidBusinessNumber show={isValid === false} />

          <ValidBusinessNumber show={isValid === true} />

          <div className="flex gap-4 items-start">
            <Input
              label="Business Number"
              placeholder="Input business number"
              containerStyle="flex-1"
              {...formikInputOpts(formik, "business_number")}
              info="Business number format: 123456789RM0001"
            />
            <Input
              label="Business Name"
              placeholder="Input business full name"
              containerStyle="flex-1"
              {...formikInputOpts(formik, "business_name")}
            />
            {/* <Button
              label="Verify"
              kind="outline"
              className="text-xs px-3 py-1.5 w-fit justify-self-center mt-5 flex-0"
              loadingLabel="Verifying"
              loading={verifyBusiness.isPending}
              disabled={saveOnboarding.isPending || sendPoA.isPending || carmRequest.isPending}
              onClick={formik.handleSubmit}
            /> */}
          </div>
        </fieldset>
      </form>

      <div className="flex flex-col gap-1.5 items-center">
        {!isValid || formik.dirty ? (
          <Button
            label="Verify"
            // kind="outline"
            // className="text-xs px-3 py-1.5 w-fit justify-self-center mt-5 flex-0"
            loadingLabel="Verifying"
            loading={verifyBusiness.isPending}
            disabled={saveOnboarding.isPending || sendPoA.isPending || carmRequest.isPending}
            onClick={formik.handleSubmit}
          />
        ) : (
          <Button
            label="Next Step"
            className="self-center w-fit"
            disabled={verifyBusiness.isPending || !isValid || formik.dirty}
            loading={saveOnboarding.isPending || sendPoA.isPending || carmRequest.isPending}
            onClick={handleNext}
          />
        )}
      </div>
    </div>
  );
}

export default VerifyBusiness;
