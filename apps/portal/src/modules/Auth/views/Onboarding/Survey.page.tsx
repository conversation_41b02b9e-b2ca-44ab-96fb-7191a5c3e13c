import { useFormik } from "formik";
import { useCallback, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Button, Icons, Input, Select } from "ui";
import { formikInputOpts } from "utils";
import { COMMODITY, COMPANY_SIZE, INDUSTRY, MONTHLY_IMPORT_VOLUME } from "../../Constant.auth";
import { AuthPages } from "../../Routes.auth";
import { surveySchema } from "../../Schema.auth";
import { OnboardingDocument, OnboardingSteps } from "../../Types.auth";
import { useSaveOnboarding } from "../../mutations";
import toast from "react-hot-toast";

const Survey = () => {
  const navigate = useNavigate();
  const saveOnboarding = useSaveOnboarding();
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = useCallback(
    async (params: OnboardingDocument) => {
      try {
        setIsLoading(true);
        const docId = await saveOnboarding.mutateAsync(params);

        setTimeout(() => {
          setIsLoading(false);
          navigate(AuthPages.Onboarding(docId as string));
        }, 1500);
      } catch (error) {
        toast.error((error as Error).message);
        setIsLoading(false);
      }
    },
    [navigate, saveOnboarding]
  );

  const formik = useFormik({
    initialValues: {
      companySize: undefined,
      monthlyImportVolume: undefined,
      commodity: undefined,
      industry: undefined,
      name: "",
      contactEmail: "",
      step: OnboardingSteps.SURVEY
    },
    validationSchema: surveySchema,
    validateOnBlur: true,
    onSubmit: handleSubmit
  });

  return (
    <div className="flex items-center h-screen bg-gradient-to-b from-white via-neutral-100 to-white dark:from-neutral-800 dark:to-neutral-900">
      <header className="fixed top-0 left-0 flex items-center justify-between p-8 w-full">
        <Icons.Logo className="w-30 fill-primary dark:fill-white" />
      </header>

      <div className="lg:basis-3/5 basis-1/2 px-8 flex-1">
        <div className="flex flex-col gap-3 max-w-lg text-primary">
          <h1>
            Automate Customs <br /> Clearance Effortlessly with Claro
          </h1>
          <h4 className="!font-medium">
            Streamline your logistics with 24/7 shipment tracking, compliance automation, and cost-effective
            customs clearance.
          </h4>
        </div>
      </div>

      <div className="lg:basis-2/5 basis-1/2 h-full flex flex-col gap-8 py-8 px-16 items-center justify-center shadow overflow-y-auto bg-white dark:bg-dark dark:text-white">
        <div className="flex flex-col gap-2 items-center text-center">
          <h2>Tell us a little bit about your company</h2>
          <span className="text-neutral-600 dark:text-neutral-400">
            We need some information about your business to customize your experience. This helps us provide
            the most relevant customs clearance solutions for your needs.
          </span>
        </div>

        <div className="flex flex-col gap-3 w-full">
          <Select
            label="Company Size *"
            options={COMPANY_SIZE}
            {...formikInputOpts(formik, "companySize")}
            optional
          />
          <Select
            label="Monthly Import Volume * (shipments)"
            options={MONTHLY_IMPORT_VOLUME}
            {...formikInputOpts(formik, "monthlyImportVolume")}
            optional
          />
          <Select
            label="Commodity *"
            options={COMMODITY}
            {...formikInputOpts(formik, "commodity")}
            optional
          />
          <Select label="Industry *" options={INDUSTRY} {...formikInputOpts(formik, "industry")} optional />
          <div className="flex gap-3">
            <Input
              label="Name *"
              placeholder="Input your name"
              containerStyle="flex-1"
              {...formikInputOpts(formik, "name")}
            />
            <Input
              label="Contact Email *"
              type="email"
              placeholder="Input your email address"
              containerStyle="flex-1"
              {...formikInputOpts(formik, "contactEmail")}
            />
          </div>
        </div>

        <Button
          label="Continue"
          loading={saveOnboarding.isPending || isLoading}
          onClick={formik.handleSubmit}
          className="w-full"
        />

        <span className="inline-flex gap-1 text-sm text-neutral-900 dark:text-neutral-300">
          Already have an account?
          <Link to={AuthPages.Login} className="decoration-2 hover:underline font-medium cursor-pointer">
            Sign in
          </Link>
        </span>
      </div>
    </div>
  );
};
export default Survey;
