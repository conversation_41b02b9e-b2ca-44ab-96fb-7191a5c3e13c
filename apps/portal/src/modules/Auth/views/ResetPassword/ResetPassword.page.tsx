import { useStore } from "@/bootstrap/Store.bootstrap";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect } from "react";
import toast from "react-hot-toast";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Card } from "ui";
import { AuthHeader, ResetPasswordForm } from "../../components";
import { useResetPassword } from "../../mutations";
import { ResetPasswordParams } from "../../Types.auth";

const ResetPassword = observer(() => {
  const { isAuthenticated } = useStore().auth;
  const navigate = useNavigate();
  const [params] = useSearchParams();
  const token = params.get("token");

  const resetPassword = useResetPassword();

  useEffect(() => {
    if (isAuthenticated || !token) navigate("/");
  }, [navigate, isAuthenticated, token]);

  const handleResetPassword = useCallback(
    async (params: ResetPasswordParams) => {
      try {
        await resetPassword.mutateAsync(params);
        toast.success("Password reset successfully. Please login to continue.");
        navigate("/login");
      } catch (error) {
        toast.error((error as Error).message);
      }
    },
    [navigate, resetPassword]
  );

  return (
    <div className="flex items-center justify-center h-screen bg-gradient-to-b from-white via-neutral-100 to-white dark:from-neutral-800 dark:to-neutral-900">
      <AuthHeader />

      <Card containerStyle="flex flex-col gap-8 p-8 w-full max-w-md">
        <div className="flex flex-col gap-2 items-center">
          <h2>Reset Password</h2>
          <span>Reset your password.</span>
        </div>

        <ResetPasswordForm
          token={token}
          isLoading={resetPassword.isPending}
          onSubmit={handleResetPassword}
          isForgotPassword
        />
      </Card>
    </div>
  );
});
export default ResetPassword;
