import { useStore } from "@/bootstrap/Store.bootstrap";
import { BaseError } from "@/common/Types.common";
import { useFormik } from "formik";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect, useReducer } from "react";
import toast from "react-hot-toast";
import { Link, useNavigate } from "react-router-dom";
import { Button, Card, Input, InputHint } from "ui";
import { formikInputOpts } from "utils";
import { AuthHeader, ForgotPasswordModal } from "../../components";
import { useForgotPassword } from "../../mutations";
import { AuthPages } from "../../Routes.auth";
import { loginSchema } from "../../Schema.auth";
import { ForgotPasswordParams, LoginMethod } from "../../Types.auth";

const Login = observer(() => {
  // const { darkMode } = useStore().theme;
  const { login, isLoading, isAuthenticated, error } = useStore().auth;
  const navigate = useNavigate();
  const [forgotPasswordModal, toggleForgotPasswordModal] = useReducer((r) => !r, false);

  const forgotPassword = useForgotPassword();

  // const handleGoogleLogin = (res: CredentialResponse) => {
  //   if (res.credential) {
  //     // login({ idToken: res.credential, loginMethod: LoginMethod.GOOGLE_SSO });
  //   }
  // };

  const handleForgotPassword = useCallback(
    async (params: ForgotPasswordParams) => {
      try {
        await forgotPassword.mutateAsync(params);

        const message = `Reset password email sent.`;
        toast.success(message);

        toggleForgotPasswordModal();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [forgotPassword]
  );

  useEffect(() => {
    if (isAuthenticated) navigate("/");

    if (error) {
      toast.error(`Login failed: ${error.message}`);
    }
  }, [error, navigate, isAuthenticated]);

  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
      loginMethod: LoginMethod.EMAIL
    },
    validationSchema: loginSchema,
    validateOnBlur: true,
    onSubmit: login
  });

  return (
    <div className="flex items-center justify-center h-screen bg-gradient-to-b from-white via-neutral-100 to-white dark:from-neutral-800 dark:to-neutral-900">
      {forgotPasswordModal && (
        <ForgotPasswordModal
          show={forgotPasswordModal}
          isLoading={forgotPassword.isPending}
          onSubmit={handleForgotPassword}
          onClose={toggleForgotPasswordModal}
        />
      )}

      <AuthHeader />

      <Card containerStyle="flex flex-col gap-8 p-8 items-center w-full max-w-md">
        <div className="flex flex-col gap-2 items-center">
          <h2>Sign In</h2>
          <span>Sign in to your account.</span>
        </div>

        {/* <GoogleLogin
          onSuccess={(credentialResponse) => {
            handleGoogleLogin(credentialResponse);
          }}
          onError={() => {
            toast.error("Login failed!");
          }}
          text="signin_with"
          theme={darkMode ? "filled_black" : "outline"}
        /> */}

        {/* <div className="w-full py-3 flex items-center text-sm font-medium before:flex-1 before:border-t before:border-neutral-200 before:me-4 after:flex-1 after:border-t after:border-neutral-200 after:ms-4 dark:text-neutral-500 dark:before:border-neutral-600 dark:after:border-neutral-600">
          OR
        </div> */}

        <form onSubmit={formik.handleSubmit} className="flex flex-col gap-6 w-full">
          <Input
            label="Email"
            type="email"
            placeholder="<EMAIL>"
            inputStyle="bg-background border-none py-3 dark:border-solid"
            {...formikInputOpts(formik, "email")}
          />
          <Input
            label="Password"
            type="password"
            placeholder="******"
            inputStyle="bg-background border-none py-3 align-center dark:border-solid"
            hint={<InputHint label="Forgot password?" onClick={toggleForgotPasswordModal} />}
            {...formikInputOpts(formik, "password")}
          />

          <Button label="Sign In" loading={isLoading} type="submit" className="w-full mt-2" />
        </form>

        <div className="flex flex-col items-center gap-3">
          <span className="inline-flex gap-1 text-sm text-neutral-900 dark:text-neutral-300">
            Don't have an account?
            <Link to={AuthPages.Survey} className="decoration-2 hover:underline font-medium cursor-pointer">
              Sign up
            </Link>
          </span>
          {/* <DarkModeToggle /> */}
        </div>
      </Card>
    </div>
  );
});
export default Login;
