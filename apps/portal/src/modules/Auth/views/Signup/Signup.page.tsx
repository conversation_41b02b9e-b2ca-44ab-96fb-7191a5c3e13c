import { Link } from "react-router-dom";
import { Button, Icons, Input } from "ui";
import { AuthPages } from "../../Routes.auth";

const Signup = () => {
  // const { signup, isLoading, isAuthenticated, error } = useStore().auth;
  // const navigate = useNavigate();

  // const handleSignup = (params: SignupParams) => {
  //   signup(params);
  // };

  // useEffect(() => {
  //   if (isAuthenticated) navigate("/");

  //   if (error) {
  //     toast.error(`Signup failed: ${error.message}`);
  //   }
  // }, [error, navigate, isAuthenticated]);

  // const formik = useFormik({
  //   initialValues: {
  //     email: "",
  //     name: "",
  //     password: "",
  //     permission: UserPermission.BASIC,
  //     confirmPassword: "",
  //   },
  //   validationSchema: signupSchema,
  //   validateOnBlur: true,
  //   onSubmit: handleSignup,
  // });

  return (
    <div className="flex items-center h-screen bg-gradient-to-b from-white via-neutral-100 to-white dark:from-neutral-800 dark:to-neutral-900">
      <header className="fixed top-0 left-0 flex items-center justify-between p-8 w-full">
        <Icons.Logo className="w-30 fill-primary dark:fill-white" />
      </header>

      <div className="basis-3/5 px-8 flex-1">
        <div className="flex flex-col gap-3 max-w-lg text-primary">
          <h1 className="">
            Automate Customs <br /> Clearance Effortlessly with Claro
          </h1>
          <h4 className="!font-medium">
            Streamline your logistics with 24/7 shipment tracking, compliance automation, and cost-effective
            customs clearance.
          </h4>
        </div>
      </div>

      <div className="basis-2/5 h-full flex flex-col gap-8 py-8 px-16 items-center justify-center shadow overflow-y-auto bg-white dark:bg-dark dark:text-white">
        <div className="flex flex-col gap-2 items-center">
          <h2>Sign Up</h2>
          <span>Enter your personal data to create an account.</span>
        </div>

        {/* <GoogleLogin
          onSuccess={(credentialResponse) => {
            handleGoogleLogin(credentialResponse);
          }}
          onError={() => {
            alert("Login failed!");
            // toast.error(`Login failed`);
          }}
          text="signin_with"
        /> */}

        <div className="w-full py-3 flex items-center text-sm font-medium before:flex-1 before:border-t before:border-neutral-200 before:me-4 after:flex-1 after:border-t after:border-neutral-200 after:ms-4 dark:text-neutral-500 dark:before:border-neutral-600 dark:after:border-neutral-600">
          OR
        </div>

        <div className="flex flex-col gap-4 w-full">
          <Input
            label="Name"
            name="name"
            placeholder="Input your name"
            inputStyle="bg-background border-none py-3 dark:border-solid"
            // onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            // value={formik.values.name}
            // error={
            //   formik.errors.name && formik.touched.name
            //     ? formik.errors.name
            //     : undefined
            // }
          />
          <Input
            label="Email"
            type="email"
            name="email"
            placeholder="Input your email address"
            inputStyle="bg-background border-none py-3 dark:border-solid"
            // onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            // value={formik.values.email}
            // error={
            //   formik.errors.email && formik.touched.email
            //     ? formik.errors.email
            //     : undefined
            // }
          />
          <Input
            label="Password"
            type="password"
            name="password"
            placeholder="Input password"
            inputStyle="bg-background border-none py-3 dark:border-solid"
            // onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            // value={formik.values.password}
            // error={
            //   formik.errors.password && formik.touched.password
            //     ? formik.errors.password
            //     : undefined
            // }
          />
          <Input
            label="Confirm Password"
            type="password"
            name="confirmPassword"
            placeholder="Confirm password"
            inputStyle="bg-background border-none py-3 dark:border-solid"
            // onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            // value={formik.values.confirmPassword}
            // error={
            //   formik.errors.confirmPassword && formik.touched.confirmPassword
            //     ? formik.errors.confirmPassword
            //     : undefined
            // }
          />
        </div>

        <Button
          label="Sign Up"
          // loading={isLoading}
          // onClick={formik.handleSubmit}
          className="w-full"
        />

        <span className="inline-flex gap-1 text-sm text-neutral-900 dark:text-neutral-300">
          Already have an account?
          <Link to={AuthPages.Login} className="decoration-2 hover:underline font-medium cursor-pointer">
            Sign in
          </Link>
        </span>
      </div>
    </div>
  );
};
export default Signup;
