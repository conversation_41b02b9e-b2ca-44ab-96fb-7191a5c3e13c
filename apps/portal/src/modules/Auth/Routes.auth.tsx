import { RouterObject } from "@/common/Types.common";
import { CreatePassword, Login, Onboarding, ResetPassword, Signup, Survey } from "./views";

const AuthPages = {
  Login: "/login",
  Signup: "/register",
  Survey: "/signup",
  CreatePassword: "/create-password",
  ResetPassword: "/reset-password",
  Onboarding: (id?: string) => `/onboarding/${id}`
};
const AuthRoutes: RouterObject = [
  {
    // index: true,
    path: AuthPages.Login,
    element: <Login />
  },
  {
    path: AuthPages.Signup,
    element: <Signup />
  },
  {
    path: AuthPages.CreatePassword,
    element: <CreatePassword />
  },
  {
    path: AuthPages.ResetPassword,
    element: <ResetPassword />
  },
  {
    path: AuthPages.Survey,
    element: <Survey />
  },
  {
    path: AuthPages.Onboarding(":id"),
    element: <Onboarding />
  }
];

export { AuthPages, AuthRoutes };
