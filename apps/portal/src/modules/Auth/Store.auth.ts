import Http from "@/bootstrap/Http.bootstrap";
import Sse from "@/bootstrap/Sse.bootstrap";
import { makeAutoObservable, runInAction } from "mobx";
import AuthService from "./services";
import { LoginParams, Organization, Session, SignupParams, UserPermission } from "./Types.auth";

import { BaseError } from "@/common/Types.common";
import { clearPersistedStore, isHydrated, makePersistable } from "mobx-persist-store";
import ImporterService from "../Importers/services";
import { Importer, ImporterStatus } from "../Importers/Types.importer";

class AuthStore {
  session?: Session;
  isLoading = false;
  error?: BaseError;
  isReady = false;
  importerOnboarded = false;
  importer?: Importer;
  organization?: Organization;

  constructor() {
    makeAutoObservable(this);

    makePersistable(this, {
      name: "ClaroAuthStore",
      properties: ["session", "importerOnboarded", "organization"],
      storage: localStorage
    });
  }

  login = async (params: LoginParams) => {
    this.clear();
    this.isLoading = true;

    try {
      const _login = await AuthService.login(params);

      this.setSession(_login);
      this.setHeaders(_login.accessToken);
    } catch (error) {
      this.session = undefined;

      this.setError(error as BaseError);
    } finally {
      this.isLoading = false;
    }
  };

  signup = async (params: SignupParams) => {
    this.clear();
    this.isLoading = true;

    try {
      const _signup = await AuthService.signup(params);

      this.setSession(_signup);
      this.setHeaders(_signup.accessToken);
    } catch (error) {
      this.session = undefined;

      this.setError(error as BaseError);
    } finally {
      this.isLoading = false;
    }
  };

  refreshToken = async () => {
    this.clear();
    this.isLoading = true;

    const token = this.session?.refreshToken;

    try {
      if (token) {
        const refresh = await AuthService.refreshToken({
          refreshToken: token
        });

        this.setSession(refresh);
        this.setHeaders(refresh.accessToken);
      }
    } catch (error) {
      this.setError(error as BaseError);
    } finally {
      this.isLoading = false;
    }
  };

  checkImporter = async () => {
    try {
      const importers = await ImporterService.list();
      const hasActiveImporter = importers.importers.some(
        (importer) => importer.status === ImporterStatus.ACTIVE
      );
      const totalImporters = importers.importers.length;

      runInAction(() => {
        if (hasActiveImporter) {
          // Case 1: At least one active importer
          this.importerOnboarded = true;
        } else if (totalImporters > 1) {
          // Case 2: More than one importer but not active
          this.importerOnboarded = true;
        }
      });
    } catch (error) {
      this.setError(error as BaseError);
    } finally {
      runInAction(() => {
        this.isReady = true;
      });
    }
  };

  logout = async () => {
    const token = this.session?.refreshToken;
    this.isLoading = true;

    try {
      if (token) {
        await AuthService.logout({
          refreshToken: token
        });
      }
    } catch (error) {
      this.setError(error as BaseError);
    } finally {
      runInAction(() => {
        this.session = undefined;
        this.isReady = false;
        this.isLoading = false;
        this.importerOnboarded = false;
        this.organization = undefined;
      });
      this.clearHeaders();
      this.clear();
      this.clearPersistedData();
    }
  };

  initialize = () => {
    if (this.session && this.session.accessToken) {
      this.setHeaders(this.session.accessToken, this.organization?.id?.toString());
      if (!this.importerOnboarded) this.checkImporter();
      else
        runInAction(() => {
          this.isReady = true;
        });
    } else {
      this.logout();
    }
  };

  clear = () => {
    this.isLoading = false;
    this.error = undefined;
  };

  setError = (error: BaseError) => {
    runInAction(() => {
      this.error = error;
    });
  };
  setSession = (session: Session) => {
    runInAction(() => {
      this.session = session;
    });
  };
  updateOrganization = (organization?: Organization) => {
    runInAction(() => {
      this.organization = organization;
    });
    this.setHeaders(undefined, organization?.id?.toString());
    window.location.reload();
  };
  updateImporterOnboarded = (onboarded: boolean) => {
    runInAction(() => {
      this.importerOnboarded = onboarded;
    });
  };

  setHeaders = (token?: string, organizationId?: string) => {
    Http.updateConfig({
      headers: {
        Authorization: `Bearer ${token ?? this.session?.accessToken}`,
        "Claro-Organization-Id": organizationId ?? undefined
      }
    });
    Sse.updateConfig({
      headers: {
        Authorization: `Bearer ${token ?? this.session?.accessToken}`,
        "Claro-Organization-Id": organizationId ?? ""
      }
    });
  };
  clearHeaders = () => {
    Http.updateConfig({
      headers: {
        Authorization: "",
        "Claro-Organization-Id": undefined
      }
    });
    Sse.updateConfig({
      headers: {
        Authorization: "",
        "Claro-Organization-Id": ""
      }
    });
  };

  get isAuthenticated(): boolean {
    return this.session !== undefined && this.session.accessToken !== "";
  }

  get isSuperAdmin(): boolean {
    return this.session?.permission === UserPermission.BACKOFFICE_ADMIN;
  }

  get isAdmin(): boolean {
    return this.session?.permission === UserPermission.ORGANIZATION_ADMIN;
  }

  get isHydrated(): boolean {
    return isHydrated(this);
  }
  clearPersistedData = async (): Promise<void> => {
    await clearPersistedStore(this);
  };
}
export default AuthStore;
