import { useQuery } from "@tanstack/react-query";
import AuthService from "../services";
import type { GetUserList, GetUserListResponse } from "../Types.auth";

export const getUsersKey = "getUsers";

export const useGetUsers = <R = GetUserListResponse>(
  params?: GetUserList,
  // TODO make this options reusable
  options?: {
    select?: (data: GetUserListResponse) => R;
    refetchOnMount?: boolean;
    enabled?: boolean;
  }
) =>
  useQuery({
    queryKey: [getUsersKey, params],
    queryFn: () => AuthService.User.list(params),
    refetchOnMount: false,
    ...options
  });

export const useGetUserList = (params?: GetUserList) => useGetUsers(params, { refetchOnMount: true });
