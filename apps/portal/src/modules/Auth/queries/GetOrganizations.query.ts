import { useInfiniteQuery } from "@tanstack/react-query";
import { flattenQueryResults, infiniteQueryMapper } from "utils";
import { GetOrganizationList } from "../Types.auth";
import AuthService from "../services";

export const getOrganizationsKey = "getOrganizations";

export const useGetInfiniteOrganizations = (params?: GetOrganizationList, refetchOnMount = false) =>
  useInfiniteQuery({
    queryKey: [getOrganizationsKey, params],
    queryFn: ({ pageParam }) => AuthService.Organization.list({ ...params, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: infiniteQueryMapper,
    refetchOnMount,
    select: (data) => flattenQueryResults("organizations", data.pages)
  });
