import { enumToSelectOptions } from "utils";
import { Commodity, CompanySize, Industry, MonthlyImportVolume, UserPermission } from "./Types.auth";

export const USER_PERMISSION = enumToSelectOptions(UserPermission).filter(
  (value) => value.value !== UserPermission.BACKOFFICE_ADMIN
);

export const COMPANY_SIZE = enumToSelectOptions(CompanySize, {
  renderLabel: "value",
  ignoreHyphen: true
});
export const MONTHLY_IMPORT_VOLUME = enumToSelectOptions(MonthlyImportVolume, {
  renderLabel: "value",
  ignoreHyphen: true
});
export const COMMODITY = enumToSelectOptions(Commodity, {
  renderLabel: "value"
});
export const INDUSTRY = enumToSelectOptions(Industry, {
  renderLabel: "value"
});
