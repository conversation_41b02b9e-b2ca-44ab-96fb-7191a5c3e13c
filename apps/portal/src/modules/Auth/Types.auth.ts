import {
  CreateUserDto,
  OnboardingDocument as Doc,
  EditOrganizationDto,
  EditUserDto,
  GetOrganizationsDto,
  GetOrganizationsResponseDto,
  GetUsersDto,
  GetUsersResponseDto,
  OnboardingImporter as Imp,
  LoginUserDto,
  Organization as Org,
  RefreshAccessTokenResponseDto,
  ResetUserPasswordDto,
  OnboardingSurvey as Sur,
  User as Usr,
  OnboardingVideo as Vid
} from "nest-modules";
import { DetailParams, ListParams, PaginationResponse } from "utils";

export enum UserPermission {
  BACKOFFICE_ADMIN = "backoffice-admin",
  ORGANIZATION_ADMIN = "organization-admin",
  BASIC = "basic"
}
export enum LoginMethod {
  GOOGLE_SSO = "google-sso",
  EMAIL = "email"
}
export enum OrganizationCustomsBroker {
  USAV = "usav",
  CLARO = "claro"
}

//#region Auth
export type Session = RefreshAccessTokenResponseDto;

export type LoginParams = LoginUserDto;
export type SignupParams = CreateUserDto & {
  confirmPassword: string;
};
export type RefreshTokenParams = Pick<Session, "refreshToken">;
export type LogoutParams = RefreshTokenParams;

export type ResetPasswordParams = ResetUserPasswordDto & {
  confirmPassword: string;
};
export type ForgotPasswordParams = {
  email: string;
};
//#endregion

//#region User
export type User = Usr;

export type GetUserList = ListParams<GetUsersDto>;
export type GetUserListResponse = PaginationResponse & GetUsersResponseDto;

export type SaveUserParams = (CreateUserDto | EditUserDto) & DetailParams;
//#endregion

//#region Org
export type Organization = Org;

export type GetOrganizationList = ListParams<GetOrganizationsDto>;
export type GetOrganizationListResponse = PaginationResponse & GetOrganizationsResponseDto;

export type SaveOrganizationParams = EditOrganizationDto & DetailParams;
//#endregion

//#region Onboarding
export enum CompanySize {
  LESS_THAN_5 = "<5",
  FIVE_TO_TEN = "5-10",
  ELEVEN_TO_FIFTY = "11-50",
  FIFTY_ONE_TO_TWO_HUNDRED = "51-200",
  TWO_HUNDRED_ONE_TO_FIVE_HUNDRED = "201-500",
  FIVE_HUNDRED_PLUS = "500+"
}
export enum MonthlyImportVolume {
  ONE = "1",
  TWO_TO_FOUR = "2-4",
  FIVE_TO_TEN = "5-10",
  TEN_TO_TWENTY = "10-20",
  TWENTY_TO_FIFTY = "20-50",
  FIFTY_TO_HUNDRED = "50-100",
  HUNDRED_PLUS = "100+"
}
export enum Commodity {
  PERSONAL_GOODS = "Personal Goods Not for Commercial Use",
  ELECTRONICS = "Electronics & Electrical Equipment",
  MACHINERY = "Machinery & Industrial Equipment",
  TEXTILES = "Textiles & Apparel",
  RETAIL = "Retail & Consumer Goods",
  AUTOMOTIVE = "Automotive Parts & Vehicles",
  FOOD = "Food & Beverages",
  FURNITURE = "Commercial Furniture & Home Goods",
  METALS = "Metals & Minerals",
  PETROLEUM = "Petroleum & Energy Products",
  HAZARDOUS = "Hazardous Materials",
  AEROSPACE = "Aerospace & Defense",
  RENEWABLE = "Renewable Energy Components",
  PHARMACEUTICALS = "Pharmaceuticals & Medical Supplies",
  CHEMICALS = "Chemicals & Raw Materials"
}
export enum Industry {
  LOGISTICS = "Logistics & Transportation",
  MANUFACTURING = "Manufacturing",
  RETAIL = "Retail & Consumer Goods",
  HEALTHCARE = "Healthcare & Life Sciences",
  TECHNOLOGY = "Technology",
  CHEMICALS = "Chemicals & Materials",
  FOOD = "Food & Beverage",
  ENERGY = "Energy & Utilities",
  AEROSPACE = "Aerospace & Defense",
  CONSTRUCTION = "Construction & Engineering",
  TEXTILES = "Textiles & Apparel",
  PROFESSIONAL = "Professional Services",
  MINING = "Mining & Metals",
  MEDIA = "Media & Entertainment",
  GOVERNMENT = "Government & Public Sector",
  HOSPITALITY = "Hospitality & Tourism",
  EDUCATION = "Education & Research",
  NON_PROFIT = "Non-Profit & NGOs"
}
export enum OnboardingSteps {
  SURVEY = 1,
  INTRODUCTION_VIDEO,
  IMPORTER_INFORMATION,
  BUSINESS_VALIDATION,
  POA,
  COMPLETED
}

export type OnboardingSurvey = Sur;

export type OnboardingVideo = Vid;

export type OnboardingImporter = Imp & DetailParams;

export type BusinessNumberValidationParams = {
  business_number: string;
  business_name: string;
};
export type BusinessNumberValidationResponse = Array<{
  isValid: boolean;
}>;

export type SendPoaResponse = {
  envelopeId?: string;
};
export type ResendPoaParams = SendPoaResponse & {
  importer?: OnboardingImporter;
};

export type OnboardingDocument = Doc;
//#endregion
