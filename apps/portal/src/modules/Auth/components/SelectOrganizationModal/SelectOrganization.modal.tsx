import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { OrganizationColumn } from "nest-modules";
import { useState } from "react";
import { Button, Input, Modal, Table } from "ui";
import { SortOrder } from "utils";
import { organizationTableSchema } from "../../Schema.auth";
import { GetOrganizationList, Organization } from "../../Types.auth";
import { useGetInfiniteOrganizations } from "../../queries";

type Props = {
  show: boolean;
  selected?: boolean;
  onSelect(item?: Organization): void;
  onClose(): void;
};
function SelectOrganizationModal({ show, onSelect, onClose, selected }: Props) {
  const [name, setName] = useState<string>();
  const [filters, setFilters] = useState<GetOrganizationList>();

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } = useGetInfiniteOrganizations({
    limit: DEFAULT_LIMIT,
    sortBy: "createDate" as OrganizationColumn,
    sortOrder: SortOrder.DESC,
    name,
    ...filters
  });

  return (
    <Modal
      id="select-organization-modal"
      show={show}
      onClose={onClose}
      title="Select Organization"
      actions={<>{selected && <Button label="Clear" onClick={() => onSelect()} />}</>}
    >
      <div className="flex flex-col gap-4">
        <Input label="Name" placeholder="Search" onTextChange={setName} kind="search" />
        <Table
          data={data}
          schema={organizationTableSchema}
          onClick={onSelect}
          isLoading={isLoading}
          onEndReached={fetchNextPage}
          hasNextPage={hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
          onSort={(key?: string, sortOrder?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key as OrganizationColumn,
              sortOrder
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
        />
      </div>
    </Modal>
  );
}
export default SelectOrganizationModal;
