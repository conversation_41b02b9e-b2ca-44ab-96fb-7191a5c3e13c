import { useFormik } from "formik";
import { Button, Input } from "ui";
import { formikInputOpts } from "utils";
import { resetPasswordSchema } from "../../Schema.auth";
import { ResetPasswordParams } from "../../Types.auth";

type Props = {
  token: string | null;
  isLoading?: boolean;
  isForgotPassword?: boolean;
  onSubmit(params: ResetPasswordParams): void;
};
function ResetPasswordForm({ token, isLoading, isForgotPassword, onSubmit }: Props) {
  const formik = useFormik({
    initialValues: {
      token: token || "",
      password: "",
      confirmPassword: ""
    },
    validationSchema: resetPasswordSchema,
    validateOnBlur: true,
    onSubmit
  });

  return (
    <fieldset className="flex flex-col gap-6" disabled={isLoading}>
      <Input
        label="Password"
        type="password"
        placeholder="******"
        inputStyle="bg-background border-none py-3 align-center dark:border-solid"
        {...formikInputOpts(formik, "password")}
      />
      <Input
        label="Confirm Password"
        type="password"
        placeholder="Confirm password"
        inputStyle="bg-background border-none py-3 dark:border-solid"
        {...formikInputOpts(formik, "confirmPassword")}
      />
      <Button
        label={isForgotPassword ? "Reset Password" : "Create Password"}
        loading={isLoading}
        onClick={formik.handleSubmit}
        className="w-full mt-2"
      />
    </fieldset>
  );
}
export default ResetPasswordForm;
