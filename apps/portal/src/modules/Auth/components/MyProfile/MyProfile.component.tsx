import { useStore } from "@/bootstrap/Store.bootstrap";
import { BaseError } from "@/common/Types.common";
import { Building, PlusIcon } from "lucide-react";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect, useMemo, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { Button, Popup, Table } from "ui";
import { emptyStringToNull } from "utils";
import { AddUserModal, UserForm } from "..";
import { useDeleteUser, useSaveUser } from "../../mutations";
import { useGetUserDetail, useGetUsers } from "../../queries";
import { userTableSchema } from "../../Schema.auth";
import { SaveUserParams, User } from "../../Types.auth";

const MyProfile = observer(() => {
  const { session, isAdmin, isSuperAdmin } = useStore().auth;
  const [modal, toggleModal] = useReducer((r) => !r, false);
  const [user, setUser] = useState<User>();
  const { showPopup, hidePopup } = Popup.usePopup();

  const saveUser = useSaveUser();
  const deleteUser = useDeleteUser();

  const { data: me, error, isFetching, refetch } = useGetUserDetail();
  const {
    data: users,
    error: usersError,
    isFetching: fetchingUsers,
    refetch: usersRefetch
  } = useGetUsers({ limit: 50 });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
    if (usersError) {
      toast.error(usersError.message);
    }
  }, [error, usersError]);

  const canAddUser = useMemo(() => {
    return isAdmin && users?.users && users?.users?.length < 3;
  }, [isAdmin, users?.users]);

  const handleSaveUser = useCallback(
    async (params: SaveUserParams) => {
      if (!session?.organization?.id) return;

      try {
        await saveUser.mutateAsync({
          ...emptyStringToNull(params),
          organizationId: session?.organization?.id
        });

        const message = `User ${params.name} ${params.id ? "updated" : "created"} successfully`;
        toast.success(message);

        if (modal) {
          setUser(undefined);
          toggleModal();
        }
        refetch();
        usersRefetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [modal, refetch, saveUser, session?.organization?.id, usersRefetch]
  );

  const handleDeleteUser = () => {
    showPopup(
      {
        content: `Are you sure you want to delete ${user?.name}?`,
        onProceed: async () => {
          hidePopup();
          try {
            await deleteUser.mutateAsync({
              id: user?.id
            });

            toast.success(`User ${user?.name} deleted successfully`);

            if (modal) {
              setUser(undefined);
              toggleModal();
            }
            refetch();
            usersRefetch();
          } catch (error) {
            toast.error((error as BaseError).message);
          }
        }
      },
      Popup.PopupType.DELETE_POPUP
    );
  };

  return (
    <div className="grid grid-cols-3 gap-4">
      <AddUserModal
        show={modal}
        info={user}
        isLoading={saveUser.isPending}
        isDeleting={deleteUser.isPending}
        onClose={() => {
          setUser(undefined);
          toggleModal();
        }}
        onSubmit={handleSaveUser}
        onDelete={handleDeleteUser}
        isAdmin={isAdmin}
        currentUserId={me?.id}
      />

      <section className="flex flex-col gap-1">
        <h5>My Profile</h5>
        <UserForm
          user={me}
          isLoading={isFetching || saveUser.isPending}
          onSubmit={handleSaveUser}
          isSuperAdmin={isSuperAdmin}
        />
      </section>

      <section className="col-span-2">
        <div className="flex items-center mb-4">
          <div className="inline-flex gap-3 items-center">
            <Building />
            <h5>{session?.organization?.name}</h5>
          </div>
          {canAddUser && (
            <Button
              label="Add User"
              kind="outline"
              icon={<PlusIcon size={16} />}
              className="ml-auto py-1 px-2"
              disabled={isFetching || fetchingUsers}
              loading={saveUser.isPending}
              onClick={toggleModal}
            />
          )}
        </div>

        <Table
          data={users?.users}
          isLoading={fetchingUsers}
          schema={userTableSchema}
          {...(isAdmin && {
            onClick: (_data: User) => {
              if (_data.id === me?.id) return;
              setUser(_data);
              toggleModal();
            }
          })}
        />
      </section>
    </div>
  );
});
export default MyProfile;
