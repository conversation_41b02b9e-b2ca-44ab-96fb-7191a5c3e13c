import { useNavigate } from "react-router-dom";
import { Button, Icons } from "ui";

function AuthHeader() {
  const navigate = useNavigate();

  return (
    <header className="fixed top-0 left-0 flex items-center justify-between p-8 w-full">
      <Icons.Logo
        className="w-30 cursor-pointer fill-primary dark:fill-white"
        onClick={() => navigate("/")}
      />
      <Button
        label="Back to Home"
        // TODO change url on production
        onClick={() => (window.location.href = "https://claro-dev.clarocustoms.com")}
      />
    </header>
  );
}
export default AuthHeader;
