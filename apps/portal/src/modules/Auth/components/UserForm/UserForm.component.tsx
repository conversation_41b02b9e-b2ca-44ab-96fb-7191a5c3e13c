import { useFormik } from "formik";
import { Button, Input, Select } from "ui";
import { formikInputOpts } from "utils";
import { USER_PERMISSION } from "../../Constant.auth";
import { saveUserSchema } from "../../Schema.auth";
import { SaveUserParams, User } from "../../Types.auth";

type Props = {
  user?: User;
  isLoading?: boolean;
  isSuperAdmin?: boolean;
  onSubmit(params: SaveUserParams): void;
};
function UserForm({ user, isLoading, isSuperAdmin, onSubmit }: Props) {
  const formik = useFormik({
    initialValues: {
      id: user?.id,
      name: user?.name || "",
      email: user?.email || "",
      permission: user?.permission,
      password: ""
    },
    validationSchema: saveUserSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit
  });

  return (
    <fieldset className="flex flex-col gap-2" disabled={isSuperAdmin}>
      <Input label="Name" placeholder="Input name" {...formikInputOpts(formik, "name")} />
      <Input label="Email" placeholder="Input email" type="email" {...formikInputOpts(formik, "email")} />
      <Select
        label="Permission"
        options={USER_PERMISSION}
        {...formikInputOpts(formik, "permission")}
        optional
        disabled
      />
      <Input
        label="Password"
        placeholder="Input password"
        type="password"
        {...formikInputOpts(formik, "password")}
      />

      {!isSuperAdmin && (
        <Button label="Update" className="ml-auto" onClick={formik.handleSubmit} loading={isLoading} />
      )}
    </fieldset>
  );
}
export default UserForm;
