import { useFormik } from "formik";
import { Button, Input, Modal } from "ui";
import { formikInputOpts } from "utils";
import { forgotPasswordSchema } from "../../Schema.auth";
import { ForgotPasswordParams } from "../../Types.auth";

type Props = {
  show: boolean;
  isLoading?: boolean;
  onSubmit(params: ForgotPasswordParams): void;
  onClose(): void;
};
function ForgotPasswordModal({ show, isLoading, onSubmit, onClose }: Props) {
  const formik = useFormik({
    initialValues: {
      email: ""
    },
    validationSchema: forgotPasswordSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit
  });

  return (
    <Modal
      id="user-modal"
      show={show}
      onClose={() => {
        formik.resetForm();
        onClose();
      }}
      title="Forgot Password"
    >
      <div className="flex flex-col gap-6">
        <Input label="Email" type="email" placeholder="Input email" {...formikInputOpts(formik, "email")} />

        <Button label="Submit" loading={isLoading} onClick={formik.handleSubmit} />
      </div>
    </Modal>
  );
}
export default ForgotPasswordModal;
