import { useFormik } from "formik";
import { Button, Input, Modal, Select } from "ui";
import { formikInputOpts } from "utils";
import { USER_PERMISSION } from "../../Constant.auth";
import { saveUserSchema } from "../../Schema.auth";
import { SaveUserParams, User } from "../../Types.auth";

type Props = {
  show: boolean;
  info?: User;
  isLoading?: boolean;
  isDeleting?: boolean;
  isAdmin?: boolean;
  currentUserId?: number;
  onSubmit(params: SaveUserParams): void;
  onClose(): void;
  onDelete(): void;
};
function AddUserModal({
  show,
  info,
  isLoading,
  isDeleting,
  isAdmin,
  currentUserId,
  onSubmit,
  onClose,
  onDelete
}: Props) {
  const formik = useFormik({
    initialValues: {
      id: info?.id,
      name: info?.name || "",
      email: info?.email || "",
      permission: info?.permission,
      password: ""
    },
    validationSchema: saveUserSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit: onSubmit
  });

  return (
    <Modal
      id="user-modal"
      show={show}
      onClose={() => {
        formik.resetForm();
        onClose();
      }}
      title={`${info ? "Edit" : "Add New"} User`}
      actions={
        info &&
        isAdmin && (
          <Button
            label="Delete User"
            kind="delete"
            loading={isDeleting}
            disabled={isLoading}
            onClick={onDelete}
          />
        )
      }
    >
      <div className="flex flex-col gap-3 px-2">
        <Input label="Name" placeholder="Input name" {...formikInputOpts(formik, "name")} />
        <Input label="Email" type="email" placeholder="Input email" {...formikInputOpts(formik, "email")} />
        {isAdmin && (
          <Select
            label="Permission"
            options={USER_PERMISSION}
            {...formikInputOpts(formik, "permission")}
            optional
            disabled={currentUserId === info?.id}
          />
        )}
        <Input
          label="Password"
          type="password"
          placeholder="Input password"
          {...formikInputOpts(formik, "password")}
        />

        <Button
          label="Submit"
          loading={isLoading}
          disabled={isDeleting}
          onClick={formik.handleSubmit}
          className="mt-2"
        />
      </div>
    </Modal>
  );
}
export default AddUserModal;
