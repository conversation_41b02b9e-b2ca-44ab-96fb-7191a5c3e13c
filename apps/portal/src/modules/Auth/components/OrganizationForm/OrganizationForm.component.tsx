import { useFormik } from "formik";
import { Button, Input } from "ui";
import { formikInputOpts } from "utils";
import { saveOrganizationSchema } from "../../Schema.auth";
import { Organization, SaveOrganizationParams } from "../../Types.auth";

type Props = {
  organization?: Organization;
  isLoading?: boolean;
  onSubmit(params: SaveOrganizationParams): void;
};
function OrganizationForm({ organization, isLoading, onSubmit }: Props) {
  const formik = useFormik({
    initialValues: {
      id: organization?.id,
      name: organization?.name || ""
    },
    validationSchema: saveOrganizationSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit
  });

  if (!organization) return <></>;

  return (
    <>
      <h5>Organization</h5>

      <div className="flex items-end gap-3">
        <Input
          label="Organization Name"
          placeholder="Input name"
          containerStyle="flex-1"
          {...formikInputOpts(formik, "name")}
        />
        <Button label="Update" className="items-end" onClick={formik.handleSubmit} loading={isLoading} />
      </div>
    </>
  );
}
export default OrganizationForm;
