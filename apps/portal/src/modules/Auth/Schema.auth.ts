import { CarmStatus, LoginMethod, OrganizationType } from "nest-modules";
import { TableSchema } from "utils";
import { ObjectSchema, boolean, mixed, number, object, ref, string } from "yup";
import {
  Commodity,
  CompanySize,
  ForgotPasswordParams,
  Industry,
  LoginParams,
  MonthlyImportVolume,
  OnboardingImporter,
  OnboardingSurvey,
  Organization,
  OrganizationCustomsBroker,
  ResetPasswordParams,
  SaveOrganizationParams,
  SaveUserParams,
  User,
  UserPermission
} from "./Types.auth";
import { isValidPhoneNumber } from "react-phone-number-input";

export const userTableSchema: TableSchema<User> = {
  name: {
    header: "Name",
    style: "w-fit",
    visible: true
  },
  email: {
    header: "Email",
    visible: true
  },
  permission: {
    header: "Permission",
    renderer: "kebabCase",
    visible: true
  }
};
export const organizationTableSchema: TableSchema<Organization> = {
  name: {
    header: "Name",
    style: "w-fit font-medium",
    visible: true
  }
};

export const loginSchema: ObjectSchema<LoginParams> = object({
  email: string().email("Email is not valid").required("Email is required"),
  password: string().required("Password is required"),
  idToken: string().optional(),
  loginMethod: mixed<LoginMethod>().required()
});

export const forgotPasswordSchema: ObjectSchema<ForgotPasswordParams> = object({
  email: string().email("Email is not valid").required("Email is required")
});

export const resetPasswordSchema: ObjectSchema<ResetPasswordParams> = object({
  password: string().required("Password is required"),
  confirmPassword: string()
    .oneOf([ref("password")], "Password must match")
    .required("Confirm password is required"),
  token: string().required("Token is required")
});

// export const signupSchema: ObjectSchema<SignupParams> = object({
//   name: string().required("Name is required"),
//   email: string().email("Email is not valid").required("Email is required"),
//   password: string()
//     .required("Password is required")
//     .min(5, "Minimum 5 characters"),
//   confirmPassword: string()
//     .oneOf([ref("password")], "Password must match")
//     .required("Confirm password is required"),
//   permission: mixed<UserPermission>().required(),
// });

export const saveUserSchema: ObjectSchema<SaveUserParams> = object({
  id: number().optional(),
  name: string().required("Name is required"),
  email: string().required("Email is required").email("Email is invalid"),
  googleUserId: string().optional(),
  permission: mixed<UserPermission>().required("Permission is required"),
  password: string().when("id", {
    is: (value: number | undefined) => value !== undefined,
    then: () => string().optional(),
    otherwise: () => string().required("Password is required")
  }),
  organizationId: number().optional()
});

export const saveOrganizationSchema: ObjectSchema<SaveOrganizationParams> = object({
  id: number().optional(),
  name: string().required("Name is required"),
  organizationType: mixed<OrganizationType>().required("Organization type is required"),
  skipPoaCheck: boolean().optional(),
  customsBroker: mixed<OrganizationCustomsBroker>().optional()
});

//#region Onboarding
export const surveySchema: ObjectSchema<OnboardingSurvey> = object({
  companySize: mixed<CompanySize>().required("Company Size is required"),
  monthlyImportVolume: mixed<MonthlyImportVolume>().required("Monthly Import Volume is required"),
  commodity: mixed<Commodity>().required("Commodity is required"),
  industry: mixed<Industry>().required("Industry is required"),
  name: string().required("Name is required"),
  contactEmail: string().email("Email is not valid").required("Email is required")
});

export const importerSchema: ObjectSchema<OnboardingImporter> = object({
  companyName: string().required("Company name is required"),
  email: string().email("Email is not valid").required("Email is required"),
  phoneNumber: string()
    .required("Phone number is required")
    .test("isValid", "Phone number is not valid", (value) => isValidPhoneNumber(value)),
  fax: string().optional(),
  address: string().required("Address is required").max(70, "Address is limited to 70 characters"),
  city: string().required("City is required"),
  state: string().required("State is required"),
  postalCode: string().required("Postal code is required"),
  countryName: string().required("Country is required"),
  officerNameAndTitle: string().required("Company officer name is required"),
  // from importer object
  businessNumber: string().optional(),
  countryId: number().optional(),
  id: number().optional(),
  carmStatus: mixed<CarmStatus>().optional(),
  carmApiKey: string().optional(),
  candataCustomerNumber: string().optional()
});

export const verifyBusinessSchema = object({
  business_number: string()
    .required("Business number is required")
    .matches(
      /^\d{9}RM00\d{2}$/,
      "Business number must be in format: 9 digits + RM00 + 2 digits (e.g. 123456789RM0001)"
    ),
  business_name: string().required("Company name is required")
});

export const resendPoaSchema = object({
  email: string().email("Email is not valid").required("Email is required")
});
//#endregion
