import { useStore } from "@/bootstrap/Store.bootstrap";
import { useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";

/**
 * Check backoffice Permission
 */
export const useBackofficePermission = (organizationId?: number, returnRoute?: string) => {
  const { isSuperAdmin, organization } = useStore().auth;
  const navigate = useNavigate();

  useEffect(() => {
    if (isSuperAdmin && organization && organizationId && organization.id !== organizationId) {
      if (returnRoute) {
        navigate(returnRoute);
      } else {
        navigate(-1);
      }
    }
  }, [isSuperAdmin, navigate, organization, organizationId, returnRoute]);

  const canCrudBackoffice = useMemo(() => {
    // If not super admin, then can crud
    if (!isSuperAdmin) return true;
    else {
      // If super admin, then check if organization is the same
      if (isSuperAdmin && organization) {
        if (organizationId && organization.id !== organizationId) return false;
        return true;
        // If organization is not set, then return false
      } else return false;
    }
  }, [isSuperAdmin, organization, organizationId]);

  return {
    canCrudBackoffice
  };
};
