import http from "@/bootstrap/Http.bootstrap";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import type {
  GetOrganizationList,
  GetOrganizationListResponse,
  Organization,
  SaveOrganizationParams
} from "../Types.auth";

const ORGANIZATION_ROUTE = "organizations";
/**
 * Get organization list
 *
 * @returns
 */
async function list(params?: GetOrganizationList): Promise<GetOrganizationListResponse> {
  try {
    return await http.get(stringifyQueryParams(ORGANIZATION_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get organization detail
 *
 * @returns
 */
async function get(): Promise<Organization> {
  try {
    return await http.get(`${ORGANIZATION_ROUTE}/me`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create organization
 *
 * @returns
 */
async function create(params: SaveOrganizationParams): Promise<Organization> {
  try {
    return await http.post(ORGANIZATION_ROUTE, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit organization
 *
 * @returns
 */
async function edit({ id, ...params }: SaveOrganizationParams): Promise<Organization> {
  try {
    return await http.put(`${ORGANIZATION_ROUTE}/${id}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Delete organization
 *
 * @returns
 */
async function remove({ id }: DetailParams): Promise<void> {
  try {
    return await http.delete(`${ORGANIZATION_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}

export default { create, edit, get, list, remove };
