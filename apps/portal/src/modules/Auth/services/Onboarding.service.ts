import { env } from "@/config/Environment.config";
import { getAuth, signInAnonymously, UserCredential } from "firebase/auth";
import { doc, getDoc, serverTimestamp, setDoc, updateDoc } from "firebase/firestore";
import { httpsCallable, HttpsCallableResult } from "firebase/functions";
import { handleError, Http } from "utils";
import {
  BusinessNumberValidationParams,
  BusinessNumberValidationResponse,
  OnboardingDocument,
  OnboardingImporter,
  ResendPoaParams,
  SendPoaResponse
} from "../Types.auth";
import { handleFirebaseError } from "../Utils.auth";
import { db, functions } from "./firebase";
import { CarmRequestParams, CarmRequestResponse } from "@/modules/Importers/Types.importer";

const apify = new Http({
  prefixUrl: env.apifyApiUrl,
  headers: {
    "Content-Type": "application/json"
  },
  timeout: 60000
});
const carm = new Http({
  prefixUrl: env.carmApiUrl,
  headers: {
    "ngrok-skip-browser-warning": "true",
    "x-app-id": env.carmAppId
  },
  timeout: 120000
});

const COLLECTION_NAME = "onboarding";
/**
 * Authenticate firebase
 *
 * @returns
 */
async function authenticate(): Promise<UserCredential> {
  try {
    const auth = getAuth();
    return await signInAnonymously(auth);
  } catch (error) {
    throw handleFirebaseError(error);
  }
}
/**
 * Create document.
 *
 * @returns
 */
async function createDocument(params: Partial<OnboardingDocument>): Promise<string> {
  try {
    const auth = await authenticate();
    const userId = auth.user?.uid ?? "";

    const docRef = doc(db, COLLECTION_NAME, userId);
    await setDoc(docRef, { ...params, createdAt: serverTimestamp(), updatedAt: serverTimestamp() });
    return userId;
  } catch (error) {
    throw handleFirebaseError(error);
  }
}
/**
 * Edit document.
 *
 * @returns
 */
async function editDocument({ id, ...params }: OnboardingDocument): Promise<void> {
  if (!id) return;
  try {
    await authenticate();
    const docRef = doc(db, COLLECTION_NAME, id);
    return await updateDoc(docRef, { ...params, updatedAt: serverTimestamp() });
  } catch (error) {
    throw handleFirebaseError(error);
  }
}
/**
 * Get document.
 *
 * @returns
 */
async function getDocument(id: string): Promise<OnboardingDocument> {
  try {
    await authenticate();
    const docRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return { ...(docSnap.data() as OnboardingDocument), id: docSnap.id };
    } else {
      throw new Error("You are not authorized to access this page");
    }
  } catch (error) {
    throw handleFirebaseError(error);
  }
}
/**
 * Verify business number.
 *
 * @returns
 */
async function verifyBusinessNumber(
  params: BusinessNumberValidationParams
): Promise<BusinessNumberValidationResponse> {
  try {
    return await apify.post(
      `antek~cra-business-number-check/run-sync-get-dataset-items?token=${env.apifyToken}`,
      params
    );
  } catch (error) {
    throw new Error(error as string);
  }
}
/**
 * Send PoA.
 *
 * @returns
 */
async function sendPoA(params?: OnboardingImporter): Promise<HttpsCallableResult<SendPoaResponse>> {
  const callable = httpsCallable<OnboardingImporter, SendPoaResponse>(functions, "sendPoa");
  try {
    return await callable(params);
  } catch (error) {
    throw handleFirebaseError(error);
  }
}
/**
 * Send PoA.
 *
 * @returns
 */
async function resendPoA({
  envelopeId,
  importer
}: ResendPoaParams): Promise<HttpsCallableResult<SendPoaResponse>> {
  const callable = httpsCallable<ResendPoaParams, SendPoaResponse>(functions, "resendPoa");
  try {
    return await callable({ envelopeId, importer });
  } catch (error) {
    throw handleFirebaseError(error);
  }
}
/**
 * Carm request
 *
 * @returns
 */
async function carmRequest(params?: CarmRequestParams): Promise<CarmRequestResponse> {
  try {
    return await carm.get(`carmRequest?businessNo=${params?.businessNo}`);
  } catch (error) {
    throw await handleError(error);
  }
}

export default {
  createDocument,
  editDocument,
  getDocument,
  verifyBusinessNumber,
  sendPoA,
  resendPoA,
  carmRequest
};
