import http from "@/bootstrap/Http.bootstrap";
import { handleError } from "utils";
import {
  ForgotPasswordParams,
  LoginParams,
  LogoutParams,
  RefreshTokenParams,
  ResetPasswordParams,
  Session,
  SignupParams
} from "../Types.auth";

/**
 * Login.
 *
 * @returns
 */
async function login(params: LoginParams): Promise<Session> {
  try {
    return await http.post("auth/login", params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Signup.
 *
 * @returns
 */
async function signup(params: SignupParams): Promise<Session> {
  try {
    return await http.post("users", params);
  } catch (error) {
    throw await handleError(error);
  }
}

/**
 * Refresh Token.
 *
 * @returns
 */
async function refreshToken({ refreshToken }: RefreshTokenParams): Promise<Session> {
  try {
    return await http.post("auth/token", { refreshToken });
  } catch (error) {
    throw await handleError(error);
  }
}

/**
 * Logout.
 *
 * @returns
 */
async function logout(params: LogoutParams): Promise<void> {
  try {
    return await http.post("auth/logout", params);
  } catch (error) {
    throw await handleError(error);
  }
}

/**
 * Reset Password.
 *
 * @returns
 */
async function resetPassword(params: ResetPasswordParams): Promise<void> {
  try {
    return await http.post("password/reset", params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Forgot Password.
 *
 * @returns
 */
async function forgotPassword(params: ForgotPasswordParams): Promise<void> {
  try {
    return await http.post("password/send-reset-email", params);
  } catch (error) {
    throw await handleError(error);
  }
}

export default { forgotPassword, login, logout, refreshToken, resetPassword, signup };
