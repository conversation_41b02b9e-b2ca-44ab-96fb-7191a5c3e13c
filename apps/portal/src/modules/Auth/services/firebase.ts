import { env } from "@/config/Environment.config";
import { Analytics, getAnalytics, isSupported, setAnalyticsCollectionEnabled } from "firebase/analytics";
import { initializeApp } from "firebase/app";
import { getFirestore } from "firebase/firestore";
import { getFunctions } from "firebase/functions";

const app = initializeApp(env.firebaseConfig);
const db = getFirestore(app, "claro");
const functions = getFunctions(app, "northamerica-northeast2");

// Local testing
// connectFunctionsEmulator(functions, "127.0.0.1", 5001);

// Analytics
// const analytics = getAnalytics(app);
// const analytics = isSupported().then((supported) => (supported ? getAnalytics(app) : null));

let analytics: Analytics | null = null;
isSupported().then((supported) => {
  if (supported) {
    analytics = getAnalytics(app);
    setAnalyticsCollectionEnabled(analytics, true);
    // console.log("Firebase Analytics initialized");
  } else {
    console.warn("Firebase Analytics is not supported");
  }
});
// if (analytics) {
//   setAnalyticsCollectionEnabled(analytics, true);
//   console.log("Analytics debug mode enabled");
// }

export { analytics, db, functions };
