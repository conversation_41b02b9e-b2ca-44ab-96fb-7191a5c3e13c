import http from "@/bootstrap/Http.bootstrap";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import type { GetUserList, GetUserListResponse, SaveUserParams, User } from "../Types.auth";

const USER_ROUTE = "users";
/**
 * Get user list
 *
 * @returns
 */
async function list(params?: GetUserList): Promise<GetUserListResponse> {
  try {
    return await http.get(stringifyQueryParams(USER_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get user detail
 *
 * @returns
 */
async function get(): Promise<User> {
  try {
    return await http.get(`${USER_ROUTE}/me`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create user
 *
 * @returns
 */
async function create(params: SaveUserParams): Promise<User> {
  try {
    return await http.post(USER_ROUTE, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit user
 *
 * @returns
 */
async function edit({ id, ...params }: SaveUserParams): Promise<User> {
  try {
    return await http.put(`${USER_ROUTE}/${id}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Delete user
 *
 * @returns
 */
async function remove({ id }: DetailParams): Promise<void> {
  try {
    return await http.delete(`${USER_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}

export default { create, edit, get, list, remove };
