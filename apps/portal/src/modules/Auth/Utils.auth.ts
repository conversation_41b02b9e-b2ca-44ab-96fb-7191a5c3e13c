import { FirestoreError } from "firebase/firestore";
import { FunctionsError } from "firebase/functions";
import { Commodity, Industry, MonthlyImportVolume, OnboardingDocument } from "./Types.auth";

const handleFirebaseError = (error: unknown): Error => {
  const firebaseError = error as FirestoreError | FunctionsError;
  throw new Error(firebaseError.message || "An unknown error occurred");
};

function isIneligibleForOnboarding(onboarding: OnboardingDocument) {
  return (
    onboarding.monthlyImportVolume === MonthlyImportVolume.ONE ||
    onboarding.commodity === Commodity.PERSONAL_GOODS ||
    onboarding.industry === Industry.LOGISTICS
  );
}
export { handleFirebaseError, isIneligibleForOnboarding };
