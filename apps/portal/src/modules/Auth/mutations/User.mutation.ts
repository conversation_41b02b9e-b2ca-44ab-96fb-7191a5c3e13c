import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import AuthService from "../services";
import { ForgotPasswordParams, ResetPasswordParams, SaveUserParams } from "../Types.auth";

const useSaveUser = () =>
  useMutation({
    mutationFn: async (params: SaveUserParams) => {
      if (params.id) {
        return await AuthService.User.edit(params);
      }
      return await AuthService.User.create(params);
    }
  });

const useDeleteUser = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await AuthService.User.remove(params)
  });

const useResetPassword = () =>
  useMutation({
    mutationFn: async (params: ResetPasswordParams) => await AuthService.resetPassword(params)
  });

const useForgotPassword = () =>
  useMutation({
    mutationFn: async (params: ForgotPasswordParams) => await AuthService.forgotPassword(params)
  });

export { useDeleteUser, useResetPassword, useSaveUser, useForgotPassword };
