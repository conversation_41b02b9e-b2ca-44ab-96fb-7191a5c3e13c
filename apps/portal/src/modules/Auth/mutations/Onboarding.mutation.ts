import { useMutation } from "@tanstack/react-query";
import AuthService from "../services";
import {
  BusinessNumberValidationParams,
  OnboardingDocument,
  OnboardingImporter,
  ResendPoaParams
} from "../Types.auth";
import { CarmRequestParams } from "@/modules/Importers/Types.importer";

const useSaveOnboarding = () =>
  useMutation({
    mutationFn: async (params: OnboardingDocument) => {
      if (params.id) {
        return await AuthService.Onboarding.editDocument(params);
      }
      return await AuthService.Onboarding.createDocument(params);
    },
    retry: false
  });

const useVerifyBusinessNumber = () =>
  useMutation({
    mutationFn: async (params: BusinessNumberValidationParams) => {
      return await AuthService.Onboarding.verifyBusinessNumber(params);
    },
    retry: false
  });

const useSendPoA = () =>
  useMutation({
    mutationFn: async (params?: OnboardingImporter) => {
      return await AuthService.Onboarding.sendPoA(params);
    }
  });

const useResendPoA = () =>
  useMutation({
    mutationFn: async (params: ResendPoaParams) => {
      return await AuthService.Onboarding.resendPoA(params);
    }
  });

const useCarmRequest = () =>
  useMutation({
    mutationFn: async (params: CarmRequestParams) => {
      return await AuthService.Onboarding.carmRequest(params);
    },
    retry: false
  });

export { useSaveOnboarding, useVerifyBusinessNumber, useSendPoA, useResendPoA, useCarmRequest };
