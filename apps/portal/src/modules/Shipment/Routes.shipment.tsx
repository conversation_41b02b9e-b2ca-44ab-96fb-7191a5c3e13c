import { RouterObject } from "@/common/Types.common";
import { Ship } from "lucide-react";
import { Link } from "react-router-dom";
import { CommercialInvoiceTab, InvoiceLineForm } from "../CommercialInvoice/components";
import { DocumentList } from "../Document/components";
import { DocumentDetail } from "../Document/views";
import { BLTab, CustomsActivityTab, DutySummaryTab, MailTab, TrackingHistoryTab } from "./components";
import { CreateShipment, ShipmentDetail, ShipmentList } from "./views";

const ShipmentPages = {
  List: "/shipment",
  Detail: (shipment_id?: string | number) => `${ShipmentPages.List}/${shipment_id}`,
  Create: "create"
};

const ShipmentDetailPages = {
  BL: "",
  CommercialInvoice: "commercial-invoice",
  CommercialInvoiceLine: (invoice_id?: string | number, line_id?: string | number) =>
    `${ShipmentDetailPages.CommercialInvoice}/${invoice_id}/line/${line_id}`,
  DutySummary: "duty-summary",
  CustomsActivity: "customs-activity",
  Documents: "documents",
  Mail: "mail",
  TrackingHistory: "tracking-history"
};

const ShipmentNavigation = {
  label: "Shipment",
  path: ShipmentPages.List,
  icon: <Ship className="size-6" />,
  pattern: [{ path: `${ShipmentPages.List}/*` }]
};

const ShipmentRoutes: RouterObject = [
  {
    path: ShipmentPages.List,
    handle: {
      crumb: () => <Link to={ShipmentPages.List}>Shipment List</Link>
    },
    children: [
      { path: "", element: <ShipmentList /> },
      {
        path: ShipmentPages.Detail(":shipment_id"),
        element: <ShipmentDetail />,
        handle: {
          crumb: () => "Shipment Details"
        },
        children: [
          { path: ShipmentDetailPages.BL, element: <BLTab /> },
          {
            path: ShipmentDetailPages.CommercialInvoice,
            element: <CommercialInvoiceTab />
          },
          {
            path: ShipmentDetailPages.CommercialInvoiceLine(":invoice_id", ":line_id"),
            element: <InvoiceLineForm />
          },
          {
            path: ShipmentDetailPages.DutySummary,
            element: <DutySummaryTab />
          },
          {
            path: ShipmentDetailPages.CustomsActivity,
            element: <CustomsActivityTab />
          },
          {
            path: ShipmentDetailPages.Documents,
            element: <DocumentList />,
            children: [{ path: ":documentId", element: <DocumentDetail /> }]
          },
          {
            path: ShipmentDetailPages.Mail,
            element: <MailTab />
          },
          {
            path: ShipmentDetailPages.TrackingHistory,
            element: <TrackingHistoryTab />
          }
        ]
      },
      {
        path: ShipmentPages.Create,
        element: <CreateShipment />,
        handle: {
          crumb: () => "Create Shipment"
        }
      }
    ]
  }
];

export { ShipmentDetailPages, ShipmentNavigation, ShipmentPages, ShipmentRoutes };
