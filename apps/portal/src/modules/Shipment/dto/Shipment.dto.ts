import { getAuditNames } from "utils";
import { GetShipmentListResponse, Shipment } from "../Types.shipment";

function getShipmentDto(c: Shipment) {
  return {
    ...c,
    polName: c.portOfLoading?.name || "",
    podName: c.portOfDischarge?.name || "",
    deliveryName: c.placeOfDelivery?.name || "",
    carrierName: c.carrier?.name || "",
    manufacturerName: c.manufacturer?.name || "",
    shipperName: c.shipper?.name || "",
    importerName: c.importer?.companyName || "",
    consigneeName: c.consignee?.name || "",
    forwarderName: c.forwarder?.name || "",
    truckerName: c.trucker?.name || "",
    pickupLocationName: c.pickupLocation?.name || "",
    organizationName: c.organization?.name || "",
    ...getAuditNames(c)
  };
}
function getShipmentsDto(data: GetShipmentListResponse) {
  const shipments = data.shipments.map(getShipmentDto);

  return { ...data, shipments };
}

export { getShipmentDto, getShipmentsDto };
