import {
  CommercialInvoiceLineTable,
  CommercialInvoiceTable
} from "@/modules/CommercialInvoice/Types.commercial-invoice";
import { DutySummaryLineDto } from "nest-modules";
import { formatDate, formatNumber, isoDateToString } from "utils";
import { B3List, DutySummaryPdfParams, DutySummaryTotals, Shipment } from "../Types.shipment";
import { convertModeOfTransport } from "../Utils.shipment";

function downloadB3PdfDto(shipment: Shipment, dutySummaryLines: DutySummaryLineDto[]) {
  const b3List = dutySummaryLines.reduce(
    (result, item, index) => {
      const pageIndex = Math.floor(index / 5);
      const itemIndex = (index % 5) + 1;

      if (!result[pageIndex]) {
        result[pageIndex] = {
          page_index: pageIndex + 1
        };
      }

      result[pageIndex][`index${itemIndex}`] = item.sequence + 1;
      // result[pageIndex][`vfd_code${itemIndex}`] = item.vfd;
      // result[pageIndex][`gst_rate${itemIndex}`] = item.gstRate
      //   ? formatNumber(item.gstRate * 100)
      //   : 0;
      result[pageIndex][`commodity${itemIndex}`] = item.description;
      result[pageIndex][`hsCode${itemIndex}`] = item.hsCode;
      result[pageIndex][`quantity${itemIndex}`] = item.quantity;
      // result[pageIndex][`duty_rate${itemIndex}`] = item.customsDutyRate
      //   ? formatNumber(item.customsDutyRate * 100)
      //   : 0;
      // result[pageIndex][`usd_value${itemIndex}`] =
      //   item.valueForCurrencyConversion;
      result[pageIndex][`value_duty${itemIndex}`] = item.valueForDuty;
      result[pageIndex][`duty${itemIndex}`] = item.customsDuties;
      // result[pageIndex][`value_for_tax${itemIndex}`] = item.valueForTax;
      result[pageIndex][`gst${itemIndex}`] = item.gst;

      return result;
    },
    [] as Array<Record<string, string | number>>
  );

  const body: Partial<DutySummaryPdfParams> = {
    bn_no: shipment.importer?.businessNumber ?? "",
    importer: shipment.importer?.companyName ?? "",
    transaction_no: shipment.transactionNumber ?? "",
    file_no: "",
    shipper: shipment.shipper?.name ?? "",
    bl_number: shipment.hblNumber ?? "",
    container_no: shipment.containerNumber ?? "",
    exchange: "",
    currency: dutySummaryLines[0].currency ?? "",
    pod: shipment?.subLocation ?? "",
    B3List: b3List as B3List[]
  };
  return body;
}

function downloadCadPdfDto(
  shipment: Shipment,
  commercialInvoices?: CommercialInvoiceTable[],
  totals?: DutySummaryTotals,
  userName?: string
) {
  const body = {
    // row 1
    type: "AB", //
    wsType: "", //
    submissionDate: "", //
    transactionNumber: shipment.transactionNumber ?? "",
    portCode: shipment.portCode ?? "", // office no
    modeOfTransport: convertModeOfTransport(shipment.modeOfTransport),
    releaseDate: isoDateToString(shipment.releaseDate as Date) ?? "",
    weight: formatNumber(Number(shipment.weight), 2),
    carrierCode: shipment.carrierCode ?? "",
    preCarm: "", //
    rpp: "", //
    // row 2
    importerBusinessNumber: shipment.importer?.businessNumber ?? "",
    importerCompanyName: shipment.importer?.companyName ?? "",
    importerAddress: shipment.importer?.address ?? "",
    importerCity: shipment.importer?.city ?? "",
    importerPhoneNumber: shipment.importer?.phoneNumber ?? "",
    customsBroker: shipment.organization.customsBroker ?? "",
    cargoControlNumber: shipment.cargoControlNumber ?? "",
    intent: "",
    previousTransactionNumber: "",
    acceptedDate: "", //
    originalTransactionNumber: "",
    prevTransNoWhse: "",
    pou: "", //
    newBusinessNumber: "",
    // row 3
    reasonCode1: "",
    reasonCode2: "",
    reasonCode3: "",
    authorityCode1: "",
    authorityCode2: "",
    authorityCode3: "",
    remark1: "",
    remark2: "",
    remark3: "",
    // row 4
    notes: "",
    warehouseIn: "",
    warehouseOut: "",
    name: userName ?? "",
    today: formatDate(new Date()),
    // totals
    totalValueForDuty: formatNumber(totals?.totalValueForDuty, 2),
    totalPstHst: formatNumber(totals?.totalPstHst, 2),
    totalProvincialCannabisExciseDuty: formatNumber(totals?.totalProvincialCannabisExciseDuty, 2),
    totalProvincialAlcoholTax: formatNumber(totals?.totalProvincialAlcoholTax, 2),
    totalProvincialTobaccoTax: formatNumber(totals?.totalProvincialTobaccoTax, 2),
    totalRelieved: 0.0, //
    totalCustomsDuties: formatNumber(totals?.totalCustomsDuties, 2),
    totalExciseDuties: formatNumber(totals?.totalExciseDuties, 2),
    totalExciseTax: formatNumber(totals?.totalExciseTax, 2),
    totalGst: formatNumber(totals?.totalGst, 2),
    totalAntiDumping: formatNumber(totals?.totalAntiDumping, 2),
    totalCountervailing: formatNumber(totals?.totalCountervailing, 2),
    totalSurtax: formatNumber(totals?.totalSurtax, 2),
    totalSafeguard: formatNumber(totals?.totalSafeguard, 2),
    totalInterest: 0.0,
    totalTotalDutiesAndTaxes: formatNumber(totals?.totalTotalDutiesAndTaxes, 2),
    // page 2
    // commercialInvoices
    commercialInvoices: commercialInvoices?.map((invoice) => ({
      vendorName: invoice.vendor?.name ?? "",
      vendorAddress: `${invoice.vendor?.address ?? ""}, ${invoice.vendor?.city ?? ""}, ${
        invoice.vendor?.postalCode ?? ""
      }`,
      vendorPhoneNumber: invoice.vendor?.phoneNumber ?? "",
      purchaserName: invoice.purchaser?.name ?? "",
      purchaserAddress: `${invoice.purchaser?.address ?? ""}, ${invoice.purchaser?.city ?? ""}, ${
        invoice.purchaser?.postalCode ?? ""
      }`,
      purchaserPhoneNumber: invoice.purchaser?.phoneNumber ?? "",
      invoiceNumber: invoice.invoiceNumber,
      invoiceValue: formatNumber(invoice.totalValue ?? 0, 2),
      invoiceCurrency: invoice.currency.toUpperCase(),
      poNumber: invoice.poNumber,
      freightCharges: "", //
      portOfExit: "", //
      // remarks
      vendorReasonCode1: "",
      vendorReasonCode2: "",
      vendorReasonCode3: "",
      vendorAuthorityCode1: "",
      vendorAuthorityCode2: "",
      vendorAuthorityCode3: "",
      vendorRemark1: "",
      vendorRemark2: "",
      vendorRemark3: "",
      vendorTradeProgram1: "",
      vendorTradeProgram2: "",
      vendorTradeProgram3: "",
      // line items
      lines: invoice.commercialInvoiceLines.map((line: CommercialInvoiceLineTable) => ({
        sequence: line.sequence ?? 0,
        previousLineNo: "", //
        hsCode: line.hsCode ?? "",
        hsCodeDescription: "", //
        description: line.goodsDescription ?? "",
        quantity: line.quantity ?? "",
        quantityUOM: line.unitOfMeasure?.toUpperCase() ?? "",
        timeLimitType: line.timeLimitType ?? "",
        extensionDate: "", //
        countryOfOrigin: line.origin?.alpha2 ?? "",
        countryOfOriginState: line.origin?.alpha2 === "US" ? (line.originState?.alpha2 ?? "") : "", // - only for US
        countryOfExport: invoice.countryOfExport?.alpha2 ?? "",
        countryOfExportState:
          invoice.countryOfExport?.alpha2 === "US" ? (invoice.stateOfExport?.alpha2 ?? "") : "", // - only for US
        directShipmentDate: formatDate(shipment.etd || undefined),
        tt: line.tt?.code ?? "",
        tariffCode: line.tariffCode?.hsCode ?? "",
        timeLimitFrom: formatDate(line.timeLimitStartDate || undefined),
        timeLimitTo: formatDate(line.timeLimitEndDate || undefined),
        destinationProvince: line.destinationProvince ?? "",
        valueForCurrencyConversion: line.totalLineValue ?? "",
        currency: invoice.currency?.toUpperCase() ?? "",
        exchangeRate: line.exchangeRate ?? "",
        valueForDuty: line.valueForDuty ?? "",
        drpLicense: line.dutiesReliefLicence ?? "",
        orderInCouncil: line.orderInCouncil ?? "",
        authorityPermit: line.authorityPermit ?? "",
        customsDuties: line.customsDuties ?? "",
        exciseTax: line.exciseTax ?? "",
        exciseDuties: line.exciseDuties ?? "",
        surTax: line.surtax ?? "", // TODO
        antiDumping: line.antiDumping ?? "",
        safeguard: line.safeguard ?? "",
        countervailing: line.countervailing ?? "",
        valueForTax: line.valueForTax ?? "",
        gst: line.gst ?? "",
        pstHst: line.pstHst ?? "",
        provincialAlcoholTax: line.provincialAlcoholTax ?? "",
        provincialTobaccoTax: line.provincialTobaccoTax ?? "",
        alcoholPercentage: line.alcoholPercentage ?? "",
        provincialCannabisExciseDuty: line.provincialCannabisExciseDuty ?? "",
        cbsaCaseNo: "", //
        rulingNo: "", //
        appealCaseNo: "", //
        complCaseNo: "", //
        totalDutiesAndTaxes: line.totalDutiesAndTaxes ?? "",
        // remarks
        reasonCode1: "",
        reasonCode2: "",
        reasonCode3: "",
        authorityCode1: "",
        authorityCode2: "",
        authorityCode3: "",
        commodityRemark1: "",
        commodityRemark2: "",
        commodityRemark3: "",
        commodityAppealsProgram1: "",
        commodityAppealsProgram2: "",
        commodityAppealsProgram3: ""
      }))
    }))
  };
  return body;
}

export { downloadB3PdfDto, downloadCadPdfDto };
