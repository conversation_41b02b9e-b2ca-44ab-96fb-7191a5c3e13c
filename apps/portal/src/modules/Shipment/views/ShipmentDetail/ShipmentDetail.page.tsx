import { useStore } from "@/bootstrap/Store.bootstrap";
import { BaseError } from "@/common/Types.common";
import { Breadcrumbs, RouterTabs } from "@/components";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";
import { canCheckCompliance } from "@/modules/Compliance/Utils.compliance";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect, useMemo, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";
import { Button, Header, Icons, Popup } from "ui";
import { ShipmentHeader } from "../../components";
import { useSubmitEntry, useUpdateEntry } from "../../mutations";
import { useGetShipmentCompliance, useGetShipmentDetail } from "../../queries";
import { ShipmentDetailPages, ShipmentPages } from "../../Routes.shipment";
import { TrackingStatus } from "../../Types.shipment";
import { canSubmitEntry } from "../../Utils.shipment";

const ShipmentDetail = observer(() => {
  const { isSuperAdmin } = useStore().auth;
  const { shipment_id } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<number>();
  const { showPopup, hidePopup } = Popup.usePopup();
  const [isTracking, setIsTracking] = useState(false);

  const tabs = useMemo(
    () => [
      { title: "BL", path: ShipmentDetailPages.BL },
      {
        title: "Commercial Invoice",
        path: ShipmentDetailPages.CommercialInvoice
      },
      { title: "Duty Summary", path: ShipmentDetailPages.DutySummary },
      { title: "Customs Activity", path: ShipmentDetailPages.CustomsActivity },
      { title: "Documents", path: ShipmentDetailPages.Documents },
      { title: "Mail", path: ShipmentDetailPages.Mail },
      { title: "Tracking History", path: ShipmentDetailPages.TrackingHistory }
    ],
    []
  );

  //#region Fetch data
  const {
    data: shipment,
    error,
    isFetching,
    refetch,
    isFetchedAfterMount,
    isLoading
  } = useGetShipmentDetail(
    {
      id: shipment_id
    },
    isTracking ? 15000 : undefined
  );
  useBackofficePermission(shipment?.organization?.id, ShipmentPages.List);

  const {
    data: compliance,
    isFetching: isFetchingCompliance,
    refetch: refetchCompliance,
    isFetched
  } = useGetShipmentCompliance({
    id: canCheckCompliance(shipment) ? shipment?.id : undefined
  });

  const isComplianceFetched = useMemo(
    () => (canCheckCompliance(shipment) ? isFetched : true),
    [isFetched, shipment]
  );

  const loadingState = useMemo(
    () => (!isFetchedAfterMount ? isFetching : isLoading) || isFetchingCompliance,
    [isFetchedAfterMount, isFetching, isLoading, isFetchingCompliance]
  );

  useEffect(() => {
    if (error) {
      toast.error(error.message);
      navigate(ShipmentPages.List);
    }
  }, [error, navigate, shipment]);

  useEffect(() => {
    const isTracking = shipment?.containers.some((c) => c.trackingStatus === TrackingStatus.TRACKING);
    setIsTracking(!!isTracking);
  }, [shipment?.containers]);
  //#endregion

  //#region Submit entry
  const submitEntry = useSubmitEntry();
  const updateEntry = useUpdateEntry();

  const handleSubmitEntry = useCallback(async () => {
    try {
      if (shipment?.requiresReupload) await updateEntry.mutateAsync({ id: shipment_id });
      else await submitEntry.mutateAsync({ id: shipment_id });

      toast.success(`Shipment ${shipment?.requiresReupload ? "updated" : "submitted"} to CBSA`);
      refetch();
    } catch (error) {
      toast.error((error as BaseError).message);
    }
  }, [refetch, shipment?.requiresReupload, shipment_id, submitEntry, updateEntry]);

  const submitEntryPrompt = () => {
    showPopup({
      content: `Are you sure you want to ${
        shipment?.requiresReupload ? "update" : "submit"
      } this Shipment to CBSA?`,
      onProceed: () => {
        handleSubmitEntry();
        hidePopup();
      }
    });
  };
  //#endregion

  return (
    <div className="flex flex-col">
      <Header title="Shipment Detail">
        {activeTab === 0 && (canSubmitEntry(shipment?.customsStatus) || shipment?.requiresReupload) && (
          <Button
            label={shipment?.requiresReupload ? "Update Entry" : "Submit Entry"}
            kind="upload"
            onClick={submitEntryPrompt}
            loading={submitEntry.isPending || updateEntry.isPending}
            disabled={isFetching}
          />
        )}
      </Header>

      <Breadcrumbs containerStyle="bg-white dark:bg-transparent" />

      {shipment && <ShipmentHeader shipment={shipment} isSuperAdmin={isSuperAdmin} />}

      {submitEntry.isPending || updateEntry.isPending || isLoading ? (
        <div className="py-2">
          <Icons.Loader />
        </div>
      ) : (
        <RouterTabs
          items={tabs}
          kind="pills"
          context={{ shipment, refetch, refetchCompliance, loadingState, compliance, isComplianceFetched }}
          tabsStyle="bg-white dark:bg-transparent overflow-x-auto"
          onTabChange={setActiveTab}
          disabled={!isComplianceFetched}
        />
      )}
    </div>
  );
});

export default ShipmentDetail;
