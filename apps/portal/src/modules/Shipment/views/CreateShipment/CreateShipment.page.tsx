import { BaseError } from "@/common/Types.common";
import { Breadcrumbs } from "@/components";
import { AggregatableFileUpload } from "@/modules/Document/components";
import { useCallback, useRef, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, Card, Header } from "ui";
import { ShipmentForm, ShipmentFormRef } from "../../components";
import { useSaveShipment } from "../../mutations";
import { ShipmentPages } from "../../Routes.shipment";
import { SaveShipmentParams } from "../../Types.shipment";

const CreateShipment = () => {
  const navigate = useNavigate();
  const createShipment = useSaveShipment();
  const [isProcessing, setIsProcessing] = useState(false);
  const shipmentFormRef = useRef<ShipmentFormRef>(null);

  const handleSaveShipment = useCallback(
    async (params: SaveShipmentParams) => {
      try {
        const shipment = await createShipment.mutateAsync({
          ...params
        });

        toast.success(`Shipment ${params.hblNumber} created`);
        navigate(ShipmentPages.Detail(shipment.id));
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [createShipment, navigate]
  );

  const handleProcessing = useCallback((isProcessing: boolean) => {
    setIsProcessing(isProcessing);
  }, []);

  return (
    <div className="flex flex-col">
      <Header title="Create Shipment">
        <Button
          label="Create"
          kind="update"
          onClick={() => shipmentFormRef.current?.handleSubmit()}
          loading={isProcessing || createShipment.isPending}
        />
      </Header>
      <Breadcrumbs containerStyle="bg-white dark:bg-transparent" />

      <section className="px-6 py-3 bg-white dark:bg-transparent">
        <h5 className="mb-3">Upload file to create shipment</h5>
        <AggregatableFileUpload
          // onAggregate={aggregateShipmentDocument}
          onProcessing={handleProcessing}
          multiple
        />
      </section>

      <Card containerStyle="m-4 p-4">
        <ShipmentForm
          ref={shipmentFormRef}
          onSubmit={handleSaveShipment}
          isLoading={isProcessing || createShipment.isPending}
        />
      </Card>
    </div>
  );
};
export default CreateShipment;
