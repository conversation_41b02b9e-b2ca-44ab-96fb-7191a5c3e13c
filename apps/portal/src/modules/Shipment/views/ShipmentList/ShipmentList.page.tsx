import { useStore } from "@/bootstrap/Store.bootstrap";
import { useColumnVisibility } from "@/modules/Settings/Hooks.settings";
import { GroupIcon } from "lucide-react";
import { observer } from "mobx-react-lite";
import { useEffect, useMemo, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { Button, ColumnSelector, Header } from "ui";
import { SortOrder } from "utils";
import { ShipmentFilter, ShipmentTable } from "../../components";
import { SHIPMENT_TABLE_KEY } from "../../Constant.shipment";
import { useGetShipmentGroup } from "../../queries";
import { ShipmentPages } from "../../Routes.shipment";
import { shipmentTableSchema } from "../../Schema.shipment";
import { GetShipmentList, ShipmentColumn } from "../../Types.shipment";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";

const ShipmentList = observer(() => {
  const navigate = useNavigate();
  const { isSuperAdmin } = useStore().auth;
  const { canCrudBackoffice } = useBackofficePermission();
  const [filter, toggleFilter] = useReducer((r) => !r, false);
  const [filters, setFilters] = useState<GetShipmentList>();
  const [groupBy, setGroupBy] = useState<"status" | "customsStatus">("status");

  const { visibleColumns: _visibleColumns, handleToggleColumn } = useColumnVisibility({
    defaultColumns: shipmentTableSchema,
    key: SHIPMENT_TABLE_KEY
  });

  const visibleColumns = useMemo(() => {
    const schema = { ..._visibleColumns };

    const statusColumn = schema["status"];
    const customsStatusColumn = schema["customsStatus"];
    if (statusColumn && customsStatusColumn) {
      if (groupBy === "status") {
        statusColumn.visible = false;
        customsStatusColumn.visible = true;
      } else {
        statusColumn.visible = true;
        customsStatusColumn.visible = false;
      }
    }

    return schema;
  }, [_visibleColumns, groupBy]);

  const {
    data: shipments,
    error,
    isFetching,
    refetch
  } = useGetShipmentGroup(
    {
      limit: 1000,
      sortBy: ShipmentColumn.createDate,
      sortOrder: SortOrder.DESC,
      ...filters
    },
    groupBy
  );

  useEffect(() => {
    refetch();
  }, [groupBy, refetch]);

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  return (
    <div className="flex flex-col h-full overflow-hidden">
      <Header title="Shipment List" containerStyle="border">
        <Button
          label={`Group by: ${groupBy === "status" ? "Shipment Status" : "Customs Status"}`}
          kind="outline"
          icon={
            <GroupIcon className="size-4 fill-neutral-100 text-primary dark:fill-transparent dark:text-neutral-100" />
          }
          onClick={() => setGroupBy((prev) => (prev === "status" ? "customsStatus" : "status"))}
        />
        <Button label="Filters" kind="filter" onClick={toggleFilter} />
        <ColumnSelector columns={visibleColumns} onToggleColumn={handleToggleColumn} />
        {canCrudBackoffice && (
          <Button label="New Shipment" kind="create" onClick={() => navigate(ShipmentPages.Create)} />
        )}
      </Header>

      <section>
        <ShipmentFilter filterValues={setFilters} isOpen={filter} isSuperAdmin={isSuperAdmin} />
      </section>

      <section className="flex-1 min-h-0 text-sm">
        <ShipmentTable
          shipments={shipments}
          schema={visibleColumns}
          isLoading={isFetching}
          onRowClick={(shipmentId) => navigate(ShipmentPages.Detail(`${shipmentId}`))}
          groupBy={groupBy}
          onSort={(key?: string, sortOrder?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key as ShipmentColumn,
              sortOrder
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
        />
      </section>
    </div>
  );
});

export default ShipmentList;
