import { enumToSelectOptions, schemaToSelectOptions } from "utils";
import { shipmentTableSchema } from "./Schema.shipment";
import {
  ContainerType,
  PackageUOM,
  QuantityUOM,
  ShipmentMode,
  ShipmentStatus,
  UnitOfMeasure,
  UnitOfMeasureType,
  VolumeUOM,
  WeightUOM
} from "./Types.shipment";

export const SHIPMENT_MODE = enumToSelectOptions(ShipmentMode, {
  allCaps: true
});
export const SHIPMENT_STATUS = enumToSelectOptions(ShipmentStatus, {
  allCaps: true
});
export const CONTAINER_TYPE = enumToSelectOptions(ContainerType, {
  allCaps: true
});
export const VOLUME = enumToSelectOptions(VolumeUOM, { allCaps: true });
export const WEIGHT = enumToSelectOptions(WeightUOM, { renderLabel: "both" });
export const QUANTITY = enumToSelectOptions(QuantityUOM, {
  allCaps: true,
  renderLabel: "value"
});
export const PACKAGE = enumToSelectOptions(PackageUOM, { renderLabel: "both" });
export const UNIT_OF_MEASURE = enumToSelectOptions(UnitOfMeasure, {
  renderLabel: "both"
});
export const UNIT_OF_MEASURE_TYPE = enumToSelectOptions(UnitOfMeasureType, {
  renderLabel: "key"
});

export const SHIPMENT_SORT_BY = schemaToSelectOptions(shipmentTableSchema);

export const B3_PDF_TEMPLATE = "ece77b23a7eda6d4";
export const CAD_PDF_TEMPLATE = "1db77b2330119e4e";

export const SHIPMENT_TABLE_KEY = "shipmentTable";
