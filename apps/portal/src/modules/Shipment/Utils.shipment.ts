import { CustomsStatus, ShipmentMode, ShipmentStatus, TrackingStatus } from "./Types.shipment";

function shipmentLabelColor(status?: ShipmentStatus) {
  switch (status) {
    case ShipmentStatus.AT_PORT:
      return "bg-cyan-100 text-cyan-800 border-cyan-300";
    case ShipmentStatus.ON_RAIL:
      return "bg-teal-100 text-teal-800 border-teal-300";
    case ShipmentStatus.AT_TERMINAL:
      return "bg-indigo-100 text-indigo-800 border-indigo-300";
    case ShipmentStatus.IN_TRANSIT:
      return "warning";
    case ShipmentStatus.PICKED_UP:
      return "info";
    case ShipmentStatus.COMPLETED:
      return "success";
    default:
      return "gray";
  }
}
function shipmentLabelClassname(status: ShipmentStatus) {
  switch (status) {
    case ShipmentStatus.AT_PORT:
      return "bg-cyan-700 text-white";
    case ShipmentStatus.ON_RAIL:
      return "bg-teal-700 text-white";
    case ShipmentStatus.AT_TERMINAL:
      //   return "bg-blue-700 text-white";
      // case ShipmentStatus.TRANS_WHS:
      // case ShipmentStatus.DISPATCHED:
      return "bg-pink-700 text-white";
    case ShipmentStatus.IN_TRANSIT:
      // case ShipmentStatus.SCHEDULED:
      //   return "bg-indigo-700 text-white";
      // case ShipmentStatus.READY:
      return "bg-warning";
    case ShipmentStatus.PICKED_UP:
      return "bg-info";
    // case ShipmentStatus.IN_YARD:
    // case ShipmentStatus.IN_WAREHOUSE:
    //   return "bg-deep-orange-700 text-white";
    // case ShipmentStatus.CLOSED:
    //   return "bg-brown-600 text-white";
    case ShipmentStatus.COMPLETED:
      return "bg-success text-white";
    default:
      return "bg-gray-200";
  }
}

function customsLabelColor(status?: CustomsStatus) {
  switch (status) {
    case CustomsStatus.PENDING_CONFIRMATION:
      return "bg-amber-100 text-amber-800 border-amber-300";
    case CustomsStatus.PENDING_ARRIVAL:
      return "warning";
    case CustomsStatus.LIVE:
      return "info";
    case CustomsStatus.ENTRY_SUBMITTED:
      return "bg-cyan-100 text-cyan-800 border-cyan-300";
    case CustomsStatus.ENTRY_ACCEPTED:
      return "bg-teal-100 text-teal-800 border-teal-300";
    case CustomsStatus.EXAM:
      return "bg-rose-100 text-rose-800 border-rose-300";
    case CustomsStatus.RELEASED:
      return "success";
    case CustomsStatus.ACCOUNTING_COMPLETED:
      return "bg-green-100 text-green-800 border-green-300";
    default:
      return "gray";
  }
}

function trackingLabelColor(status?: TrackingStatus) {
  switch (status) {
    case TrackingStatus.ONLINE:
      return "success";
    case TrackingStatus.TRACKING:
      return "warning";
    case TrackingStatus.ERROR:
      return "danger";
  }
}

function canSubmitEntry(customsStatus?: CustomsStatus) {
  if (!customsStatus) return;
  return [
    CustomsStatus.PENDING_COMMERCIAL_INVOICE,
    CustomsStatus.PENDING_CONFIRMATION,
    CustomsStatus.PENDING_ARRIVAL
  ].includes(customsStatus);
}

function canEditBl(customsStatus?: CustomsStatus) {
  if (!customsStatus) return;
  return (
    customsStatus !== CustomsStatus.EXAM &&
    customsStatus !== CustomsStatus.RELEASED &&
    customsStatus !== CustomsStatus.ACCOUNTING_COMPLETED
  );
}

function convertModeOfTransport(modeOfTransport?: ShipmentMode | null) {
  switch (modeOfTransport) {
    case ShipmentMode.AIR:
      return "1";
    case ShipmentMode.LAND:
      return "2";
    case ShipmentMode.OCEAN_FCL:
    case ShipmentMode.OCEAN_LCL:
      return "9";
    default:
      return "";
  }
}

export {
  canEditBl,
  canSubmitEntry,
  convertModeOfTransport,
  customsLabelColor,
  shipmentLabelClassname,
  shipmentLabelColor,
  trackingLabelColor
};
