import {
  CandataExchangeRateDto,
  CandataExchangeRateResponseDto,
  CustomsActivityDto,
  DutySummaryLineDto,
  EditShipmentDto,
  GetShipmentCustomsActivitiesResponseDto,
  GetShipmentDutySummaryResponseDto,
  GetShipmentsDto,
  GetShipmentsResponseDto,
  GetTrackingHistoryDto,
  GetTrackingHistoryResponseDto,
  Shipment as Ship,
  TrackingHistory as TrackHistory,
  ValidateShipmentComplianceResponseDto
} from "nest-modules";
import { ChangeProps, DetailParams, ListParams, PaginationResponse } from "utils";
import { Importer } from "../Importers/Types.importer";
import { Location } from "../Location/Types.location";
import { TradePartner } from "../TradePartner/Types.partner";

//#region Column
export enum ShipmentColumn {
  id = "id",
  status = "status",
  trackingStatus = "trackingStatus",
  modeOfTransport = "modeOfTransport",
  etd = "etd",
  etaPort = "etaPort",
  etaPortString = "etaPortString",
  etaDestination = "etaDestination",
  etaDestinationString = "etaDestinationString",
  mblNumber = "mblNumber",
  hblNumber = "hblNumber",
  cargoControlNumber = "cargoControlNumber",
  carrierCode = "carrierCode",
  containerNumber = "containerNumber",
  containerType = "containerType",
  volume = "volume",
  volumeUOM = "volumeUOM",
  weight = "weight",
  weightUOM = "weightUOM",
  quantity = "quantity",
  quantityUOM = "quantityUOM",
  pickupLfd = "pickupLfd",
  pickupLfdString = "pickupLfdString",
  // progress = 'progress',
  pickupDate = "pickupDate",
  pickupDateString = "pickupDateString",
  pickupNumber = "pickupNumber",
  returnLfd = "returnLfd",
  returnLfdString = "returnLfdString",
  returnDate = "returnDate",
  returnDateString = "returnDateString",
  vessel = "vessel",
  voyageNumber = "voyageNumber",
  customsStatus = "customsStatus",
  transactionNumber = "transactionNumber",
  customsFileNumber = "customsFileNumber",
  portCode = "portCode",
  portOfExit = "portOfExit",
  // orderNumber = 'orderNumber',
  subLocation = "subLocation",
  //directShipmentMode = 'directShipmentMode',
  // directShipmentLocation = 'directShipmentLocation',
  // currency = 'currency',
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  portOfDischargeId = "portOfDischargeId",
  portOfLoadingId = "portOfLoadingId",
  placeOfDeliveryId = "placeOfDeliveryId",
  carrierId = "carrierId",
  manufacturerId = "manufacturerId",
  shipperId = "shipperId",
  importerId = "importerId",
  consigneeId = "consigneeId",
  forwarderId = "forwarderId",
  truckerId = "truckerId",
  pickupLocationId = "pickupLocationId",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}
export enum TrackingHistoryColumn {
  id = "id",
  shipmentId = "shipmentId",
  party = "party",
  timestamp = "timestamp",
  result = "result",
  organizationId = "organizationId"
}
//#endregion

//#region Status
export enum ShipmentStatus {
  NEW = "new",
  IN_TRANSIT = "in-transit",
  AT_PORT = "at-port",
  ON_RAIL = "on-rail",
  AT_TERMINAL = "at-terminal",
  PICKED_UP = "picked-up",
  COMPLETED = "completed"
}
export enum CustomsStatus {
  PENDING_COMMERCIAL_INVOICE = "pending-commercial-invoice",
  PENDING_CONFIRMATION = "pending-confirmation",
  PENDING_ARRIVAL = "pending-arrival",
  LIVE = "live",
  ENTRY_SUBMITTED = "entry-submitted",
  ENTRY_ACCEPTED = "entry-accepted",
  EXAM = "exam",
  RELEASED = "released",
  ACCOUNTING_COMPLETED = "accounting-completed"
}
export enum TrackingStatus {
  // Tracking is in progress
  TRACKING = "tracking",
  // Shipment is found online
  ONLINE = "online",
  // Shipment is not found online
  OFFLINE = "offline",
  // Error occurred while tracking
  ERROR = "error"
}
//#endregion

export enum ShipmentMode {
  AIR = "air",
  OCEAN_FCL = "ocean-fcl",
  OCEAN_LCL = "ocean-lcl",
  LAND = "land",
  SMALL_PACKAGE = "small-package"
}

export enum ContainerType {
  // general purpose
  FCL_20GP = "fcl-20gp",
  FCL_40GP = "fcl-40gp",

  // high cube
  FCL_40HQ = "fcl-40hq",
  FCL_45HQ = "fcl-45hq",

  // refrigerated
  FCL_20RF = "fcl-20rf",
  FCL_40RF = "fcl-40rf",

  // refrigerated high cube
  FCL_20RH = "fcl-20rh",
  FCL_40RH = "fcl-40rh",
  FCL_45RH = "fcl-45rh",

  // open top
  FCL_20OT = "fcl-20ot",
  FCL_40OT = "fcl-40ot",

  // flat rack
  FCL_20FR = "fcl-20fr",
  FCL_40FR = "fcl-40fr"
}

//#region Unit
export enum VolumeUOM {
  CBM = "cbm",
  CFT = "cft"
}
export enum WeightUOM {
  METRIC_CARAT = "ctm",
  DECITON = "dtn",
  GRAM = "grm",
  HECTOGRAM = "hgm",
  KILOGRAM = "kgm",
  KILOGRAM_NAMED_SUBSTANCE = "kns",
  KILOGRAM_AIR_DRY = "ksd",
  KILOTON = "ktn",
  POUND = "lbr",
  MILLIGRAM = "mgm",
  GRAMS_ODP_WEIGHTED = "odg",
  KILOGRAM_ODP_WEIGHTED = "odk",
  MILLIGRAM_ODP_WEIGHTED = "odm",
  METRIC_TON = "tne",
  TONNE_AIR_DRY = "tsd"
}
// @deprecated
export enum QuantityUOM {
  BOX = "box",
  PACK = "pk",
  PALLET = "pal",
  CARTON = "ctn",
  BAG = "bg",
  CASE = "cs",
  ROLL = "rl",
  BOTTLE = "btl",
  DRUM = "drm",
  PACKAGE = "pkg"
}
export enum UnitOfMeasure {
  MASS_IN_CARATS = "ctm",
  NUMBER_OF_DOZEN = "dzn",
  MASS_IN_GRAMS = "grm",
  GROSS_TWELVE_DOZEN = "gro",
  LIQUID_VOLUME_IN_HECTOLITRES = "hlt",
  MASS_IN_KILOGRAMS = "kgm",
  MASS_IN_KILOGRAMS_OF_NAMED_SUBSTANCE = "kns",
  MASS_IN_KILOGRAM_AIR_DRY = "ksd",
  LIQUID_VOLUME_IN_LITRES_OF_PURE_ALCOHOL = "lpa",
  LIQUID_VOLUME_IN_LITRES = "ltr",
  RADIOACTIVITY_IN_MEGABECQUERELS = "mbq",
  THOUSANDS = "mil",
  AREA_IN_SQUARE_METERS = "mtk",
  VOLUME_IN_CUBIC_METERS = "mtq",
  METERS = "mtr",
  ELECTRICAL_ENERGY_IN_MEGAWATT_HOURS = "mwh",
  NUMBER_OF_PACKAGES = "nap",
  NUMBER = "nmb",
  NUMBER_OF_PAIRS = "par",
  NUMBER_OF_SETS = "set",
  VOLUME_IN_THOUSANDS_OF_CUBIC_METERS = "tmq",
  MASS_IN_METRIC_TONNES = "tne",
  MASS_IN_METRIC_TONNES_AIR_DRY = "tsd"
}
export enum UnitOfMeasureType {
  VOLUME = "volume",
  WEIGHT = "weight",
  LENGTH = "len",
  AREA = "area",
  PER_UNIT = "per-unit"
}

export const UNIT_OF_MEASURE_TYPE_MAP = {
  [UnitOfMeasureType.VOLUME]: [
    UnitOfMeasure.LIQUID_VOLUME_IN_HECTOLITRES,
    UnitOfMeasure.LIQUID_VOLUME_IN_LITRES_OF_PURE_ALCOHOL,
    UnitOfMeasure.LIQUID_VOLUME_IN_LITRES,
    UnitOfMeasure.VOLUME_IN_CUBIC_METERS,
    UnitOfMeasure.VOLUME_IN_THOUSANDS_OF_CUBIC_METERS
  ],
  [UnitOfMeasureType.WEIGHT]: [
    UnitOfMeasure.MASS_IN_CARATS,
    UnitOfMeasure.MASS_IN_KILOGRAMS,
    UnitOfMeasure.MASS_IN_KILOGRAMS_OF_NAMED_SUBSTANCE,
    UnitOfMeasure.MASS_IN_KILOGRAM_AIR_DRY,
    UnitOfMeasure.MASS_IN_GRAMS,
    UnitOfMeasure.MASS_IN_METRIC_TONNES,
    UnitOfMeasure.MASS_IN_METRIC_TONNES_AIR_DRY
  ],
  [UnitOfMeasureType.LENGTH]: [UnitOfMeasure.METERS],
  [UnitOfMeasureType.AREA]: [UnitOfMeasure.AREA_IN_SQUARE_METERS],
  [UnitOfMeasureType.PER_UNIT]: [
    UnitOfMeasure.GROSS_TWELVE_DOZEN,
    UnitOfMeasure.NUMBER_OF_DOZEN,
    UnitOfMeasure.THOUSANDS,
    UnitOfMeasure.NUMBER_OF_PACKAGES,
    UnitOfMeasure.NUMBER,
    UnitOfMeasure.NUMBER_OF_PAIRS,
    UnitOfMeasure.NUMBER_OF_SETS
  ]
};
export enum PackageUOM {
  AEROSOL = "ae",
  AMPOULE_NON_PROTECTED = "am",
  AMPOULE_PROTECTED = "ap",
  ATOMIZER = "at",
  BAG = "bg",
  BALE_COMPRESSED = "bl",
  BALE_NON_COMPRESSED = "bn",
  BALLOON_NON_PROTECTED = "bf",
  BALLOON_PROTECTED = "bp",
  BAR = "br",
  BARREL = "ba",
  BARS_IN_BUNDLE = "bz",
  BASKET = "bk",
  BEER_CRATE = "cb",
  BIN = "bi",
  BOARD = "bd",
  BOARD_IN_BUNDLE = "by",
  BOBBIN = "bb",
  BOLT = "bt",
  BOTTLE_NON_PROTECTED_CYLINDRICAL = "bo",
  BOTTLE_NON_PROTECTED_BULBOUS = "bs",
  BOTTLE_PROTECTED_CYLINDRICAL = "bq",
  BOTTLE_PROTECTED_BULBOUS = "bv",
  BOTTLE_CRATE = "bc",
  BOX = "bx",
  BUCKET = "bj",
  BULK_LIQUEFIED_GAS = "vq",
  BULK_GAS = "vg",
  BULK_LIQUID = "vl",
  BULK_SOLID_FINE_PARTICLES = "vy",
  BULK_SOLID_GRANULAR_PARTICLES = "vr",
  BULK_SOLID_LARGE_PARTICLES = "vo",
  BUNCH = "bh",
  BUNDLE = "be",
  BUTT = "bu",
  CAGE = "cg",
  CAN_RECTANGULAR = "ca",
  CAN_CYLINDRICAL = "cx",
  CANISTER = "ci",
  CANVAS = "cz",
  CARBOY_NON_PROTECTED = "co",
  CARBOY_PROTECTED = "cp",
  CARTON = "ct",
  CASE = "cs",
  CASK = "ck",
  CHEST = "ch",
  CHURN = "cc",
  COFFER = "cf",
  COFFIN = "cj",
  COIL = "cl",
  COVER = "cv",
  CRATE = "cr",
  CREEL = "ce",
  CUP = "cu",
  CYLINDER = "cy",
  DEMIJOHN_NON_PROTECTED = "dj",
  DEMIJOHN_PROTECTED = "dp",
  DRUM = "dr",
  ENVELOPE = "en",
  FILMPACK = "fp",
  FIRKIN = "fi",
  FLASK = "fl",
  FOOTLOCKER = "fo",
  FRAME = "fr",
  FRAME_CRATE = "fd",
  FRUIT_CRATE = "fc",
  GAS_BOTTLE = "gb",
  GIRDER = "gi",
  GIRDERS_IN_BUNDLE = "gz",
  HAMPER = "hr",
  HOGSHEAD = "hg",
  INGOT = "in",
  INGOTS_IN_BUNDLE = "iz",
  JAR = "jr",
  JERRICAN_RECTANGULAR = "jc",
  JERRICAN_CYLINDRICAL = "jy",
  JUG = "jg",
  JUTE_BAG = "jt",
  KEG = "kg",
  LOG = "lg",
  LOGS_IN_BUNDLE = "lz",
  MILK_CRATE = "mc",
  MULTIPLY_BAG = "mb",
  MULTIWALL_SACK = "ms",
  MAT = "mt",
  MATCH_BOX = "mx",
  NEST = "ns",
  NET = "nt",
  PACKAGE = "pk",
  PACKET = "pa",
  PAIL = "pl",
  PARCEL = "pc",
  PIPE = "pi",
  PIPES_IN_BUNDLE = "pz",
  PITCHER = "ph",
  PLANK = "pn",
  PLATE = "pg",
  PLATES_IN_BUNDLE = "py",
  POT = "pt",
  POUCH = "po",
  PALLET = "pf",
  REDNET = "rt",
  REEL = "rl",
  RING = "rg",
  ROD = "rd",
  RODS_IN_BUNDLE = "rz",
  SACHET = "sh",
  SACK = "sa",
  SEA_CHEST = "se",
  SHALLOW_CRATE = "sc",
  SHEET = "st",
  SHEET_METAL = "sm",
  SHEETS_IN_BUNDLE = "sz",
  SHRINK_WRAPPED = "sw",
  SKELETON_CASE = "sk",
  SKID = "sv",
  SLIPSHEET = "sl",
  SPINDLE = "sd",
  SUITCASE = "su",
  TANK_RECTANGULAR = "tk",
  TANK_CYLINDRICAL = "ty",
  TEA_CHEST = "tc",
  TIN = "tn",
  TRAY = "pu",
  TRUNK = "tr",
  TRUSS = "ts",
  TUB = "tb",
  TUBE = "tu",
  TUBE_COLLAPSIBLE = "td",
  TUBES_IN_BUNDLE = "tz",
  TUN = "to",
  UNPACKED = "ne",
  VACUUM_PACKED = "vp",
  VAT = "va",
  VIAL = "vi",
  WICKER_BOTTLE = "wb"
}
//#endregion

export type ShipmentContext = {
  shipment: Shipment;
  loadingState?: boolean;
  compliance?: ShipmentCompliance;
  isComplianceFetched?: boolean;
  refetch: () => Promise<void>;
  refetchCompliance: () => Promise<void>;
};

export type Shipment = Ship;

export type ShipmentTable = Shipment & {
  polName?: string;
  podName?: string;
  carrierName?: string;
  manufacturerName?: string;
  shipperName?: string;
  importerName?: string;
  consigneeName?: string;
  forwarderName?: string;
  truckerName?: string;
  pickupLocationName?: string;
};

export type GetShipmentList = ListParams<GetShipmentsDto>;
export type GetShipmentListResponse = PaginationResponse & GetShipmentsResponseDto;

export type SaveShipmentParams = ChangeProps<
  Omit<EditShipmentDto, "containers">,
  "etd" | "etaDestination" | "etaPort" | "pickupDate" | "pickupLfd" | "returnLfd",
  string
> & {
  containers?: Array<{
    id?: number;
    status?: ShipmentStatus;
    trackingStatus?: TrackingStatus;
    containerNumber: string;
    containerType?: ContainerType;
    etaDestination?: string;
    pickupLfd?: string;
    pickupDate?: string;
    returnLfd?: string;
    returnDate?: string;
  }>;
} & DetailParams;

export type ShipmentFormModal = {
  pol?: Location;
  pod?: Location;
  delivery?: Location;
  carrier?: TradePartner;
  manufacturer?: TradePartner;
  shipper?: TradePartner;
  importer?: Importer;
  consignee?: TradePartner;
  forwarder?: TradePartner;
  trucker?: TradePartner;
  pickupLocation?: TradePartner;
};

export type ShipmentCompliance = ValidateShipmentComplianceResponseDto;

export type TrackingHistory = TrackHistory;
export type GetTrackingHistoryList = ListParams<GetTrackingHistoryDto>;
export type GetTrackingHistoryListResponse = PaginationResponse & GetTrackingHistoryResponseDto;

export type DutySummary = DutySummaryLineDto;
export type DutySummaryTotals = {
  // totalDuties?: number;
  // totalAssessments?: number;
  // totalExcise?: number;
  totalGst?: number;
  // grandTotal?: number;

  totalValueForDuty?: number;
  totalAntiDumping?: number;
  totalCountervailing?: number;
  totalCustomsDuties?: number;
  totalExciseDuties?: number;
  totalExciseTax?: number;
  totalPstHst?: number;
  totalProvincialAlcoholTax?: number;
  totalProvincialCannabisExciseDuty?: number;
  totalProvincialTobaccoTax?: number;
  totalSafeguard?: number;
  totalSurtax?: number;
  totalTotalDutiesAndTaxes?: number;
};
export type GetDutySummaryListResponse = GetShipmentDutySummaryResponseDto & {
  totals?: DutySummaryTotals;
};
export type B3List = {
  page_index: string; // 1;
  index1: string; // 1;
  vfd_code1: string; // "14";
  gst_rate1: string; // "5.0";
  commodity1: string; // "IRON FRAME";
  weight1: number; // 25.71;
  hsCode1: string; // "7323.99.00.90";
  quantity1: number; // 20;
  duty_rate1: number; // 6.5;
  duty_rate_number1: number; // 0.065;
  usd_value1: number; // 60;
  value_duty1: number; // 82.03;
  duty1: number; // 5.33;
  value_for_tax1: number; // 87.36;
  gst1: number; // 4.37;
  index2: number; // 2;
  vfd_code2: string; // "14";
  gst_rate2: string; // "5.0";
  commodity2: string; // "WEARBLE BLANKET";
  weight2: number; // 240.67;
  hsCode2: string; // "6301.90.00.00";
  quantity2: number; // 926;
  duty_rate2: number; // 17;
  duty_rate_number2: number; // 0.17;
  usd_value2: number; // 463;
  value_duty2: number; // 633.03;
  duty2: number; // 107.62;
  value_for_tax2: number; // 740.65;
  gst2: number; // 37.03;
  index3: number; // 3;
  vfd_code3: string; // "14";
  gst_rate3: string; // "5.0";
  commodity3: string; // "PAPER TOWEL HOLDER";
  weight3: number; // 73.2;
  hsCode3: string; // "8302.50.00.90";
  quantity3: number; // 320;
  duty_rate3: number; // 6.5;
  duty_rate_number3: number; // 0.065;
  usd_value3: number; // 320;
  value_duty3: number; // 437.52;
  duty3: number; // 28.44;
  value_for_tax3: number; // 465.96;
  gst3: number; // 23.3;
  index4: number; // 4;
  vfd_code4: string; // "14";
  gst_rate4: string; // "5.0";
  commodity4: string; // "PAPER TOWEL HOLDER";
  weight4: number; // 143.13;
  hsCode4: string; // "8302.50.00.90";
  quantity4: number; // 800;
  duty_rate4: number; // 6.5;
  duty_rate_number4: number; // 0.065;
  usd_value4: number; // 800;
  value_duty4: number; // 1093.79;
  duty4: number; // 71.1;
  value_for_tax4: number; // 1164.89;
  gst4: number; // 58.24;
  index5: number; // 5;
  vfd_code5: string; // "14";
  gst_rate5: string; // "5.0";
  commodity5: string; // "LED LAMP";
  weight5: number; // 480.15;
  hsCode5: string; // "8539.52.00.00";
  quantity5: number; // 99;
  duty_rate5: number; // 0;
  duty_rate_number5: number; // 0;
  usd_value5: number; // 495;
  value_duty5: number; // 676.78;
  duty5: number; // 0;
  value_for_tax5: number; // 676.78;
  gst5: number; // 33.84;
};
export type DutySummaryPdfParams = {
  bn_no: string; // "*********";
  importer: string; // "SHENZHEN HUARONG PHOTOELE";
  transaction_no: string; // "10005202099514";
  file_no: string; // "20209951";
  shipper: string; // "SHENZHEN VIDA NETWORK TECHNOLO";
  bl_number: string; // "8893767240";
  container_no: string; // "OOCU6514501";
  exchange: string; // "1";
  currency: string; // "CAD";
  pod: string; // "TORONTO";
  B3List: B3List[];
};

export type CustomsActivity = CustomsActivityDto;
export type GetCustomsActivityListResponse = GetShipmentCustomsActivitiesResponseDto;

export type GetShipmentTrackingParams = DetailParams & {
  containerId?: number;
};

export type GetExchangeRateParams = CandataExchangeRateDto;
export type GetExchangeRateResponse = CandataExchangeRateResponseDto;
