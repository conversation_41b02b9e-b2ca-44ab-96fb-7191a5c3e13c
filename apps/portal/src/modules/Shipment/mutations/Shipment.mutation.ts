import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import ShipmentService from "../services";
import type { SaveShipmentParams } from "../Types.shipment";

const useSaveShipment = () =>
  useMutation({
    mutationFn: async (params: SaveShipmentParams) => {
      if (params.id) {
        return await ShipmentService.edit(params);
      }
      return await ShipmentService.create(params);
    }
  });

const useDeleteShipment = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await ShipmentService.remove(params)
  });

const useSubmitEntry = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await ShipmentService.submitEntry(params)
  });

const useUpdateEntry = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await ShipmentService.updateEntry(params)
  });

const useRecalculateDutiesAndTaxes = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await ShipmentService.recalculateDutiesAndTaxes(params)
  });

export { useDeleteShipment, useRecalculateDutiesAndTaxes, useSaveShipment, useSubmitEntry, useUpdateEntry };
