import { useMutation } from "@tanstack/react-query";
import TrackingHistoryService from "../services/TrackingHistory.service";
import { GetShipmentTrackingParams } from "../Types.shipment";

const useSyncShipmentTracking = () =>
  useMutation({
    mutationFn: async (params: GetShipmentTrackingParams) => {
      return await TrackingHistoryService.sync(params);
    }
  });

export { useSyncShipmentTracking };
