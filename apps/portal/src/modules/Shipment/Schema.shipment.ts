import { Container, GetTrackingHistoryDto } from "nest-modules";
import { lazy } from "react";
import { auditTableSchema, TableSchema } from "utils";
import { array, date, mixed, number, object, string } from "yup";
import { CommercialInvoiceLineTable } from "../CommercialInvoice/Types.commercial-invoice";
import {
  ContainerType,
  CustomsActivity,
  QuantityUOM,
  SaveShipmentParams,
  ShipmentMode,
  ShipmentTable,
  VolumeUOM,
  WeightUOM
} from "./Types.shipment";
const CustomsLabel = lazy(() => import("./components/CustomsLabel"));
const ShipmentLabel = lazy(() => import("./components/ShipmentLabel"));
const TrackingLabel = lazy(() => import("./components/TrackingLabel"));

//#region Table schema
export const shipmentTableSchema: TableSchema<ShipmentTable> = {
  hblNumber: {
    header: "HBL",
    readOnly: true,
    visible: true,
    style: "font-medium"
  },
  cargoControlNumber: {
    header: "Cargo Control No.",
    visible: true
  },
  transactionNumber: {
    header: "Transaction No.",
    visible: true
  },
  // containerNumber: {
  //   header: "Container",
  //   visible: true
  // },
  mblNumber: {
    header: "MBL"
  },
  modeOfTransport: {
    header: "Mode",
    renderer: "kebabCase",
    style: "uppercase",
    visible: true
  },
  // containerType: {
  //   header: "Type",
  //   renderer: "kebabCase",
  //   style: "uppercase"
  // },
  status: {
    header: "Shipment Status",
    renderer: ShipmentLabel,
    readOnly: true,
    visible: false
  },
  customsStatus: {
    header: "Customs Status",
    renderer: CustomsLabel,
    readOnly: true,
    visible: true
  },
  // trackingStatus: {
  //   header: "Tracking Status",
  //   renderer: TrackingLabel,
  //   visible: true
  // },
  ...auditTableSchema,
  customsFileNumber: {
    header: "Customs File No.",
    visible: true
  },
  etd: {
    header: "ETD",
    renderer: "date"
  },
  etaPort: {
    header: "ETA Port",
    renderer: "date"
  },
  etaDestination: {
    header: "ETA Destination",
    renderer: "date"
  },
  polName: {
    header: "POL",
    sortKey: "portOfLoadingId"
  },
  podName: {
    header: "POD",
    sortKey: "portOfDischargeId"
  },
  importerName: {
    header: "Importer",
    sortKey: "importerId"
  },
  carrierName: {
    header: "Carrier",
    sortKey: "carrierId"
  },
  shipperName: {
    header: "Shipper",
    sortKey: "shipperId"
  },
  manufacturerName: {
    header: "Manufacturer",
    sortKey: "manufacturerId"
  },
  consigneeName: {
    header: "Consignee",
    sortKey: "consigneeId"
  },
  forwarderName: {
    header: "Forwarder",
    sortKey: "forwarderId"
  },
  truckerName: {
    header: "Trucker",
    sortKey: "truckerId"
  },
  pickupLocationName: {
    header: "Pickup Location",
    sortKey: "pickupLocationId"
  }
};
export const containerTableSchema: TableSchema<Container> = {
  containerNumber: {
    header: "Container",
    visible: true
  },
  containerType: {
    header: "Type",
    renderer: "kebabCase",
    style: "uppercase",
    visible: true
  },
  status: {
    header: "Container Status",
    renderer: ShipmentLabel,
    readOnly: true,
    visible: false
  },
  trackingStatus: {
    header: "Tracking Status",
    renderer: TrackingLabel,
    visible: true
  },
  etaDestination: {
    header: "ETA Destination",
    renderer: "date",
    visible: true
  },
  pickupDate: {
    header: "Pickup Date",
    renderer: "date",
    visible: true
  },
  returnDate: {
    header: "Return Date",
    renderer: "date",
    visible: true
  },
  pickupLfd: {
    header: "Pickup LFD",
    renderer: "date",
    visible: true
  },
  returnLfd: {
    header: "Return LFD",
    renderer: "date",
    visible: true
  }
};
export const trackingHistoryTableSchema: TableSchema<GetTrackingHistoryDto> = {
  timestamp: {
    header: "Datetime",
    renderer: "dateTime",
    readOnly: true,
    visible: true
  },
  party: {
    header: "Trade Party",
    style: "min-w-28",
    visible: true
  },
  result: {
    header: "Event",
    style: "whitespace-normal",
    visible: true
  }
};
export const dutySummaryTableSchema: TableSchema<CommercialInvoiceLineTable> = {
  sequence: {
    header: "Line No.",
    style: "text-center",
    renderer: "number",
    visible: true
  },
  productDescription: {
    header: "Description",
    style: "min-w-48 max-w-80 whitespace-normal",
    visible: true
  },
  hsCode: {
    header: "HS Code",
    visible: true
  },
  quantity: {
    header: "Quantity",
    renderer: "number",
    visible: true
  },
  unitOfMeasure: {
    header: "UoM",
    style: "uppercase",
    visible: true
  },
  vfdCode: {
    header: "VFD"
  },
  simaCode: {
    header: "Sima Code",
    visible: true
  },
  totalLineValue: {
    header: "Value for Currency Conversion",
    renderer: "numberDecimal",
    visible: true
  },
  currency: {
    header: "Currency",
    style: "uppercase",
    visible: true
  },
  exchangeRate: {
    header: "Exchange Rate",
    // renderer: "numberDecimal",
    visible: true
  },
  valueForDuty: {
    header: "Value for Duty",
    renderer: "numberDecimal",
    visible: true
  },
  customsDuties: {
    header: "Customs Duty",
    renderer: "numberDecimal",
    visible: true
  },
  exciseTax: {
    header: "Excise Tax",
    renderer: "numberDecimal",
    visible: true
  },
  exciseDuties: {
    header: "Excise Duty",
    renderer: "numberDecimal",
    visible: true
  },
  surtax: {
    header: "Surtax",
    renderer: "numberDecimal",
    visible: true
  },
  antiDumping: {
    header: "Anti Dumping",
    renderer: "numberDecimal",
    visible: true
  },
  safeguard: {
    header: "Safeguard",
    renderer: "numberDecimal",
    visible: true
  },
  countervailing: {
    header: "Countervailing",
    renderer: "numberDecimal",
    visible: true
  },
  valueForTax: {
    header: "Value for Tax",
    renderer: "numberDecimal",
    visible: true
  },
  gst: {
    header: "GST",
    renderer: "numberDecimal",
    visible: true
  },
  pstHst: {
    header: "PST/HST",
    renderer: "numberDecimal"
  },
  provincialAlcoholTax: {
    header: "Prov. Alcohol Tax",
    renderer: "numberDecimal",
    visible: true
  },
  provincialTobaccoTax: {
    header: "Prov. Tobacco Tax",
    renderer: "numberDecimal",
    visible: true
  },
  provincialCannabisExciseDuty: {
    header: "Cannabis PST",
    renderer: "numberDecimal",
    visible: true
  },
  totalDutiesAndTaxes: {
    header: "Total Duties & Taxes",
    renderer: "numberDecimal",
    visible: true
  }
};

export const customsActivityTableSchema: TableSchema<CustomsActivity> = {
  responseDate: {
    header: "Datetime",
    renderer: "dateTime",
    visible: true
  },
  transactionNumber: {
    header: "Transaction No.",
    visible: true
  },
  portCode: {
    header: "Port Code",
    visible: true
  },
  subLocationCode: {
    header: "Sub Location Code",
    visible: true
  },
  event: {
    header: "Event",
    style: "whitespace-normal",
    visible: true
  }
};
//#endregion

export const saveShipmentSchema = object<SaveShipmentParams>({
  id: number().optional(),
  modeOfTransport: mixed<ShipmentMode>().required("Shipment mode is required"),
  etd: string().optional(),
  etaPort: string().optional(),
  etaDestination: string().optional(),
  mblNumber: string().optional(),
  hblNumber: string().required("HBL is required"),
  cargoControlNumber: string().optional(),
  containerNumber: string().optional(),
  containerType: mixed<ContainerType>().optional(),
  containers: array(
    object({
      containerNumber: string().required("Container number is required"),
      containerType: mixed<ContainerType>().optional(),
      etaDestination: string().optional(),
      pickupLfd: string().optional(),
      pickupDate: string().optional(),
      returnLfd: string().optional(),
      returnDate: string().optional()
    })
  ).optional(),
  volume: number().optional().min(0).max(999),
  volumeUOM: mixed<VolumeUOM>().when("volume", {
    is: (value: number | undefined) => value !== undefined && value > 0,
    then: (schema) => schema.required("Volume unit is required"),
    otherwise: (schema) => schema.optional()
  }),
  weight: number().optional().min(0).max(99999999),

  // Temporarily disabled until https://github.com/jaredpalmer/formik/pull/3852 is merged
  // WeightUOM and QuantityUOM can be from fallback value, which will not pass to the validation function

  // weightUOM: mixed<WeightUOM>().when("weight", {
  //   is: (value: number | undefined) => value !== undefined && value > 0,
  //   then: (schema) => schema.required("Weight unit is required"),
  //   otherwise: (schema) => schema.optional()
  // }),

  weightUOM: mixed<WeightUOM>().optional(),
  quantity: number().optional().min(0).max(999999),
  // quantityUOM: mixed<QuantityUOM>().when("quantity", {
  //   is: (value: number | undefined) => value !== undefined && value > 0,
  //   then: (schema) => schema.required("Quantity unit is required"),
  //   otherwise: (schema) => schema.optional()
  // }),
  quantityUOM: mixed<QuantityUOM>().optional(),
  pickupLfd: string().optional(),
  // progress: string().optional(),
  pickupDate: string().optional(),
  pickupNumber: string().optional(),
  returnLfd: string().optional(),
  vessel: string().optional(),

  transactionNumber: string().optional(),
  customsFileNumber: string().optional(),
  carrierCode: string().optional(),
  voyageNumber: string().optional(),
  portCode: string().optional(),
  portOfExit: string().optional(),
  subLocation: string().optional(),
  releaseDate: date().optional(),
  surrenderDate: date().optional(),
  adviceNoteDate: date().optional(),

  portOfLoadingId: number().optional().nullable(),
  portOfDischargeId: number().optional().nullable(),
  placeOfDeliveryId: number().optional().nullable(),
  carrierId: number().optional().nullable(),
  manufacturerId: number().optional().nullable(),
  shipperId: number().optional().nullable(),
  importerId: number().optional().nullable(),
  consigneeId: number().optional().nullable(),
  forwarderId: number().optional().nullable(),
  truckerId: number().optional().nullable(),
  pickupLocationId: number().optional().nullable(),

  documentIds: array(number().defined()).optional(),
  etaPortString: string().optional(),
  etaDestinationString: string().optional(),
  pickupLfdString: string().optional(),
  pickupDateString: string().optional(),
  returnLfdString: string().optional(),
  returnDate: date().optional(),
  returnDateString: string().optional()
});
