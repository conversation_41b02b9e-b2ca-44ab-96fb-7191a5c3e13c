import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { useState } from "react";
import { Button, Modal, Table } from "ui";
import { SortOrder } from "utils";
import { useGetInfiniteShipments } from "../../queries";
import { shipmentTableSchema } from "../../Schema.shipment";
import { GetShipmentList, Shipment, ShipmentColumn } from "../../Types.shipment";
import ShipmentFilter from "../ShipmentFilter";

type Props = {
  show: boolean;
  required?: boolean;
  multiple?: boolean;
  onSelect(item?: Shipment): void;
  onClose(): void;
};
function SelectShipmentModal({ show, required, multiple, onSelect, onClose }: Props) {
  const [selected, setSelected] = useState<Shipment>();
  const [filters, setFilters] = useState<GetShipmentList>();

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } = useGetInfiniteShipments({
    limit: DEFAULT_LIMIT,
    sortBy: ShipmentColumn.createDate,
    sortOrder: SortOrder.DESC,
    ...filters
  });

  const handleSelect = () => {
    if (!multiple) onSelect(selected);
  };

  return (
    <Modal
      id="select-shipment-modal"
      show={show}
      onClose={onClose}
      title="Select Shipment"
      size="4xl"
      actions={
        <Button
          aria-label={required ? "Select shipment" : selected ? "Select shipment" : "Clear selection"}
          label={required ? "Select" : selected ? "Select" : "Clear"}
          onClick={handleSelect}
          disabled={!data?.length || (required && !selected)}
        />
      }
    >
      <div className="flex flex-col gap-4">
        <ShipmentFilter filterValues={setFilters} isOpen />

        <Table
          data={data}
          schema={shipmentTableSchema}
          checklist
          selected={selected ? [selected] : []}
          onClick={setSelected}
          isLoading={isLoading}
          onEndReached={fetchNextPage}
          hasNextPage={hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
        />
      </div>
    </Modal>
  );
}
export default SelectShipmentModal;
