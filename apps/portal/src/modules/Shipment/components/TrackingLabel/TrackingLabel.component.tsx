import { RefreshCwIcon } from "lucide-react";
import { Icons, StatusLabel } from "ui";
import { kebabCaseToCapitalize } from "utils";
import { TrackingStatus } from "../../Types.shipment";
import { trackingLabelColor } from "../../Utils.shipment";

type Props = { value: TrackingStatus; isSyncing?: boolean; onRefresh?(): void };
function TrackingLabel({ value, isSyncing, onRefresh }: Props) {
  const text = kebabCaseToCapitalize(value);
  const color = trackingLabelColor(value);

  return (
    <StatusLabel
      text={text}
      color={color}
      {...(onRefresh &&
        (isSyncing
          ? { icon: <Icons.Loader className="size-4" /> }
          : {
              icon: <RefreshCwIcon className="size-4 cursor-pointer" onClick={onRefresh} />
            }))}
      className="w-fit justify-center shadow"
    />
  );
}
export default TrackingLabel;
