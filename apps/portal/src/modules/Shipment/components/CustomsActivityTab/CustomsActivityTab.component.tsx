import { RefreshCwIcon } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import toast from "react-hot-toast";
import { useOutletContext } from "react-router-dom";
import { Button, Table } from "ui";
import { SortOrder, sortTable } from "utils";
import { useGetCustomsActivities } from "../../queries";
import { customsActivityTableSchema } from "../../Schema.shipment";
import { ShipmentContext } from "../../Types.shipment";

function CustomsActivityTab() {
  const { shipment } = useOutletContext<ShipmentContext>();
  const [filters, setFilters] = useState<{
    sortBy?: string;
    sortOrder?: SortOrder;
  }>({});

  const { data, error, isFetching, refetch } = useGetCustomsActivities({
    id: shipment?.id
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const sortedData = useMemo(
    () => sortTable(data?.customsActivities, filters.sortBy, filters.sortOrder),
    [data?.customsActivities, filters.sortBy, filters.sortOrder]
  );

  return (
    <div className="flex flex-col gap-3">
      <main>
        <Table
          data={sortedData}
          schema={customsActivityTableSchema}
          isLoading={isFetching}
          header={
            <div className="flex-1 flex items-center justify-between">
              <h5>Customs Activity</h5>
              <Button
                label="Refresh"
                icon={<RefreshCwIcon size={16} />}
                onClick={refetch}
                loading={isFetching}
              />
            </div>
          }
          onSort={(key?: string, sortOrder?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key,
              sortOrder
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
        />
      </main>
    </div>
  );
}
export default CustomsActivityTab;
