import { FieldItem } from "ui";
import { kebabCaseToCapitalize } from "utils";
import { type Shipment } from "../../Types.shipment";
import CustomsLabel from "../CustomsLabel";
import ShipmentLabel from "../ShipmentLabel";

type Props = {
  shipment: Shipment;
  isSuperAdmin?: boolean;
};
function ShipmentHeader({ shipment, isSuperAdmin }: Props) {
  return (
    <header className="flex gap-x-4 lg:gap-x-6 gap-y-2 flex-wrap px-6 py-3 bg-white dark:bg-transparent border-y dark:border-neutral-700">
      <FieldItem
        label="Shipment Status:"
        customElement={<ShipmentLabel value={shipment.status} />}
        direction="row"
      />
      <FieldItem
        label="Customs Status:"
        customElement={<CustomsLabel value={shipment.customsStatus} />}
        direction="row"
      />
      {/* <FieldItem
        label="Tracking Status:"
        customElement={
          <TrackingLabel value={shipment.trackingStatus} isSyncing={isSyncing} onRefresh={onRefresh} />
        }
        direction="row"
      /> */}
      <FieldItem
        label="Mode:"
        value={kebabCaseToCapitalize(shipment.modeOfTransport ?? "")}
        direction="row"
      />
      <FieldItem label="HBL:" value={shipment.hblNumber} direction="row" />
      {/* {shipment.containerNumber && (
        <FieldItem label="Container No:" value={shipment.containerNumber} direction="row" />
      )} */}
      {shipment.transactionNumber && (
        <FieldItem label="Transaction No:" value={shipment.transactionNumber} direction="row" />
      )}
      {shipment.customsFileNumber && (
        <FieldItem label="Customs File No:" value={shipment.customsFileNumber} direction="row" />
      )}
      {isSuperAdmin && <FieldItem label="Organization:" value={shipment.organization.name} direction="row" />}
    </header>
  );
}
export default ShipmentHeader;
