import { ORDER_BY } from "@/common/Constant.common";
import { Organization } from "@/modules/Auth/Types.auth";
import { SelectOrganizationModal } from "@/modules/Auth/components";
import { useDebounce } from "@uidotdev/usehooks";
import { useEffect, useReducer, useState } from "react";
import { twMerge } from "tailwind-merge";
import { Card, Input, Select } from "ui";
import { OrderBy } from "utils";
import { CONTAINER_TYPE, SHIPMENT_MODE, SHIPMENT_SORT_BY } from "../../Constant.shipment";
import { shipmentTableSchema } from "../../Schema.shipment";
import { ContainerType, GetShipmentList, ShipmentMode } from "../../Types.shipment";

type Props = {
  isOpen: boolean;
  isSuperAdmin?: boolean;
  filterValues(values?: GetShipmentList): void;
};
const ShipmentFilter = ({ isOpen, isSuperAdmin, filterValues }: Props) => {
  const [values, setValues] = useState<GetShipmentList>();
  const [organizationModal, toggleOrganizationModal] = useReducer((r) => !r, false);
  const [organization, setOrganization] = useState<Organization>();

  const [formValues, setFormValues] = useState<Partial<GetShipmentList>>({});
  const debouncedValues = useDebounce(formValues, 500);

  useEffect(() => {
    setValues((prev) => ({
      ...prev,
      ...debouncedValues
    }));
  }, [debouncedValues]);

  useEffect(() => {
    filterValues(values);
  }, [filterValues, values]);

  useEffect(() => () => setValues(undefined), []);

  return (
    <div
      className={twMerge(
        "overflow-hidden transition-max-height duration-300 ease-in-out",
        isOpen ? "max-h-96" : "max-h-0"
      )}
    >
      {organizationModal && (
        <SelectOrganizationModal
          show={organizationModal}
          onClose={toggleOrganizationModal}
          selected={!!organization}
          onSelect={(organization) => {
            setValues((prev) => ({
              ...prev,
              organizationId: organization?.id
            }));
            setOrganization(organization);
            toggleOrganizationModal();
          }}
        />
      )}
      {isOpen && (
        <Card containerStyle="flex flex-wrap items-center gap-x-3 gap-y-2 px-6 py-4 border">
          <div className="flex gap-3">
            <Input
              label="HBL"
              placeholder="Search"
              onTextChange={(hblNumber: string) => setFormValues((prev) => ({ ...prev, hblNumber }))}
              kind="search"
            />
            <Input
              label="Container"
              placeholder="Search"
              onTextChange={(containerNumber: string) =>
                setFormValues((prev) => ({ ...prev, containerNumber }))
              }
              kind="search"
            />
            <Input
              label="MBL"
              placeholder="Search"
              onTextChange={(mblNumber: string) => setFormValues((prev) => ({ ...prev, mblNumber }))}
              kind="search"
            />
            <Input
              label="Transaction No"
              placeholder="Search"
              onTextChange={(transactionNumber: string) =>
                setFormValues((prev) => ({ ...prev, transactionNumber }))
              }
              kind="search"
            />
            <Input
              label="Customs File No"
              placeholder="Search"
              onTextChange={(customsFileNumber: string) =>
                setFormValues((prev) => ({ ...prev, customsFileNumber }))
              }
              kind="search"
            />
            <Input
              label="Cargo Control No"
              placeholder="Search"
              onTextChange={(cargoControlNumber: string) =>
                setFormValues((prev) => ({ ...prev, cargoControlNumber }))
              }
              kind="search"
            />
            <Select
              label="Mode"
              onSelected={(modeOfTransport: ShipmentMode) =>
                setValues((prev) => ({ ...prev, modeOfTransport }))
              }
              options={SHIPMENT_MODE}
              optional
            />
            <Select
              label="Type"
              onSelected={(containerType: ContainerType) => setValues((prev) => ({ ...prev, containerType }))}
              options={CONTAINER_TYPE}
              optional
            />
            {isSuperAdmin && (
              <Input
                label="Organization"
                placeholder="Select organization"
                value={organization?.name ?? ""}
                onClick={toggleOrganizationModal}
                readOnly
              />
            )}
          </div>

          <div className="flex gap-3">
            <Select
              label="Sort By"
              onSelected={(sortBy: keyof typeof shipmentTableSchema) =>
                setValues((prev) => ({ ...prev, sortBy }) as GetShipmentList)
              }
              options={SHIPMENT_SORT_BY}
              defaultValue={"createDate"}
              optional
            />
            <Select
              label="Sort Direction"
              onSelected={(sortOrder: OrderBy) =>
                setValues((prev) => ({ ...prev, sortOrder }) as GetShipmentList)
              }
              options={ORDER_BY}
              defaultValue={"desc"}
              optional
            />
          </div>
        </Card>
      )}
    </div>
  );
};
export default ShipmentFilter;
