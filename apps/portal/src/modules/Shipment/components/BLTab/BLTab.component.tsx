import { BaseError } from "@/common/Types.common";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";
import { ComplianceError } from "@/modules/Compliance/components";
import { useCallback, useRef, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate, useOutletContext } from "react-router-dom";
import { <PERSON><PERSON>, Card, Header, Popup } from "ui";
import { useDeleteShipment, useSaveShipment, useSyncShipmentTracking } from "../../mutations";
import { useGetShipmentFieldFallback } from "../../queries";
import { ShipmentPages } from "../../Routes.shipment";
import { SaveShipmentParams, ShipmentContext } from "../../Types.shipment";
import ShipmentForm, { ShipmentFormRef } from "../ShipmentForm";
const ShipmentBL = () => {
  const { shipment, refetch, refetchCompliance, loadingState, compliance } =
    useOutletContext<ShipmentContext>();
  const { canCrudBackoffice } = useBackofficePermission(shipment?.organization?.id);

  const { showPopup, hidePopup } = Popup.usePopup();
  const shipmentFormRef = useRef<ShipmentFormRef>(null);
  const [isDirty, setIsDirty] = useState(false);
  const navigate = useNavigate();

  //#region Shipment
  const saveShipment = useSaveShipment();
  const deleteShipment = useDeleteShipment();

  const handleSaveShipment = useCallback(
    async (params: SaveShipmentParams) => {
      try {
        await saveShipment.mutateAsync(params);

        toast.success(`Shipment ${params.hblNumber} updated`);
        refetch();
        refetchCompliance();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [refetch, refetchCompliance, saveShipment]
  );

  const handleDeleteShipment = useCallback(async () => {
    try {
      await deleteShipment.mutateAsync({ id: shipment?.id });

      toast.success("Shipment deleted successfully");
      navigate(ShipmentPages.List);
    } catch (error) {
      toast.error((error as BaseError).message);
    }
  }, [deleteShipment, navigate, shipment?.id]);

  const deleteShipmentPrompt = () => {
    showPopup(
      {
        content: "Are you sure you want to delete this Shipment?",
        onProceed: () => {
          handleDeleteShipment();
          hidePopup();
        }
      },
      Popup.PopupType.DELETE_POPUP
    );
  };
  //#endregion

  //#region Tracking
  const syncTracking = useSyncShipmentTracking();

  const handleSyncTracking = useCallback(
    async (containerId?: number) => {
      try {
        await syncTracking.mutateAsync({ id: shipment?.id, containerId });
        refetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [refetch, shipment?.id, syncTracking]
  );
  //#endregion

  const { data: fallback } = useGetShipmentFieldFallback({ id: shipment?.id });

  return (
    <>
      <Header title={<h5>Bill of Lading</h5>} containerStyle="py-2 min-h-0">
        {canCrudBackoffice && (
          <>
            <Button
              label="Delete"
              kind="delete"
              loading={deleteShipment.isPending}
              disabled={isDirty || loadingState || saveShipment.isPending}
              onClick={deleteShipmentPrompt}
            />
            <Button
              label="Update"
              kind="update"
              onClick={() => shipmentFormRef.current?.handleSubmit()}
              loading={loadingState || saveShipment.isPending}
              disabled={deleteShipment.isPending}
            />
          </>
        )}
      </Header>

      <div className="flex">
        <div className="flex-1">
          <Card containerStyle="m-4">
            <ComplianceError title="Shipment" compliance={compliance} />
          </Card>

          <Card containerStyle="m-4 p-4">
            <ShipmentForm
              ref={shipmentFormRef}
              shipment={shipment}
              onSubmit={handleSaveShipment}
              isLoading={saveShipment.isPending}
              isDirty={setIsDirty}
              onSyncTracking={handleSyncTracking}
              fallback={fallback}
            />
          </Card>
        </div>
      </div>
    </>
  );
};
export default ShipmentBL;
