import { useStore } from "@/bootstrap/Store.bootstrap";
import { BaseError } from "@/common/Types.common";
import { useGetDutySimaCode } from "@/modules/Compliance/queries";
import { useDownloadPdf } from "@/modules/Document/mutations";
import { DownloadIcon } from "lucide-react";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect, useMemo, useState } from "react";
import toast from "react-hot-toast";
import { useOutletContext } from "react-router-dom";
import { Button, Table } from "ui";
import { Currency, downloadFromUrl, formatCurrency, formatNumber, SortOrder, sortTable } from "utils";
import { CAD_PDF_TEMPLATE } from "../../Constant.shipment";
import { downloadCadPdfDto } from "../../dto";
import { useRecalculateDutiesAndTaxes } from "../../mutations";
import { useGetDutySummary } from "../../queries";
import { dutySummaryTableSchema } from "../../Schema.shipment";
import { ShipmentContext } from "../../Types.shipment";
import { canSubmitEntry } from "../../Utils.shipment";

const DutySummaryTab = observer(() => {
  const { shipment } = useOutletContext<ShipmentContext>();
  const { session } = useStore().auth;
  const [filters, setFilters] = useState<{
    sortBy?: string;
    sortOrder?: SortOrder;
  }>({});

  const { data, error, isFetching, refetch } = useGetDutySummary({
    limit: 1000,
    shipmentId: shipment?.id,
    expandLines: true
  });

  const {
    data: dutySummary,
    error: dutySummaryError,
    isFetching: dutySummaryIsFetching
  } = useGetDutySimaCode(data?.dutySummaryLines);

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
    if (dutySummaryError) {
      toast.error(dutySummaryError.message);
    }
  }, [dutySummaryError, error]);

  const sortedData = useMemo(
    () => sortTable(dutySummary, filters.sortBy, filters.sortOrder),

    [dutySummary, filters.sortBy, filters.sortOrder]
  );

  const downloadPdf = useDownloadPdf();

  const handleDownload = useCallback(async () => {
    if (shipment && data?.commercialInvoices?.length) {
      const body = downloadCadPdfDto(shipment, data.commercialInvoices, data.totals, session?.name);

      try {
        const pdf = await downloadPdf.mutateAsync({
          templateId: CAD_PDF_TEMPLATE,
          fileName: `Duty Summary-${shipment.hblNumber}`,
          body
        });
        downloadFromUrl(pdf.download_url);
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    }
  }, [data?.commercialInvoices, data?.totals, downloadPdf, session?.name, shipment]);

  const recalculateDutiesAndTaxes = useRecalculateDutiesAndTaxes();

  const handleRecalculateDutiesAndTaxes = useCallback(async () => {
    if (shipment) {
      try {
        await recalculateDutiesAndTaxes.mutateAsync({ id: shipment.id });
        toast.success("Duties and taxes recalculated successfully");
        refetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    }
  }, [recalculateDutiesAndTaxes, refetch, shipment]);

  return (
    <div className="flex flex-col gap-3">
      <main className="mb-2">
        <Table
          data={sortedData}
          schema={dutySummaryTableSchema}
          isLoading={isFetching || dutySummaryIsFetching}
          showTopScrollbar
          header={
            <div className="flex-1 flex items-center justify-between">
              <h5>Duty Summary</h5>
              <div className="flex items-center gap-2">
                <Button
                  label="Download"
                  kind="outline"
                  icon={<DownloadIcon size={16} />}
                  onClick={handleDownload}
                  loading={downloadPdf.isPending}
                  disabled={
                    isFetching || dutySummaryIsFetching || !data || data.dutySummaryLines?.length === 0
                  }
                />
                {!canSubmitEntry(shipment.customsStatus) && (
                  <Button
                    label="Recalculate Duties and Taxes"
                    kind="refresh"
                    onClick={handleRecalculateDutiesAndTaxes}
                    loading={recalculateDutiesAndTaxes.isPending}
                    disabled={isFetching || dutySummaryIsFetching || downloadPdf.isPending}
                  />
                )}
                <Button
                  label="Refresh"
                  kind="refresh"
                  onClick={refetch}
                  loading={isFetching || dutySummaryIsFetching}
                  disabled={isFetching || dutySummaryIsFetching || downloadPdf.isPending}
                />
              </div>
            </div>
          }
          summary={
            data?.totals && (
              <tr className="border-t border-gray-200 dark:border-neutral-700 font-semibold">
                {Object.entries(dutySummaryTableSchema).map(([key, column]) => {
                  if (!column.visible) return null;
                  const value =
                    data.totals?.[
                      `total${key.charAt(0).toUpperCase() + key.slice(1)}` as keyof typeof data.totals
                    ];
                  return (
                    <td key={key} className="pl-4 pr-1 lg:pl-6 py-2 text-start text-sm">
                      {value !== undefined && typeof value === "number"
                        ? key === "totalDutiesAndTaxes"
                          ? formatCurrency(value, Currency.CAD, "code")
                          : formatNumber(value, 2)
                        : ""}
                    </td>
                  );
                })}
              </tr>
            )
          }
          total={data?.dutySummaryLines?.length}
          onSort={(key?: string, sortOrder?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key,
              sortOrder
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
        />
      </main>
    </div>
  );
});

export default DutySummaryTab;
