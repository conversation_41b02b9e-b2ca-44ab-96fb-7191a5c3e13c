import { ReactElement } from "react";
import { StatusLabel } from "ui";
import { kebabCaseToCapitalize } from "utils";
import type { ShipmentStatus } from "../../Types.shipment";
import { shipmentLabelColor } from "../../Utils.shipment";

type Props = { value: ShipmentStatus; icon?: ReactElement };
function ShipmentLabel({ value, icon }: Props) {
  const text = kebabCaseToCapitalize(value);
  const color = shipmentLabelColor(value);

  return <StatusLabel text={text} color={color} icon={icon} className="w-fit justify-center shadow" />;
}
export default ShipmentLabel;
