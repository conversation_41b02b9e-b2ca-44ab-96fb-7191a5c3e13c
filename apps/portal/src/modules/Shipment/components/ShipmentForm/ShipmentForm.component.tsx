import { useStore } from "@/bootstrap/Store.bootstrap";
import { InputFallbackWrapper } from "@/common/components";
import { useFallback } from "@/common/hooks/useFallback";
import { FallbackEnricherResponse } from "@/common/Types.common";
import { objectToFilteredArray } from "@/modules/Compliance/Utils.compliance";
import { SelectImporterModal } from "@/modules/Importers/components";
import { Importer } from "@/modules/Importers/Types.importer";
import { SelectLocationModal } from "@/modules/Location/components";
import { Location } from "@/modules/Location/Types.location";
import { SelectPartnerModal } from "@/modules/TradePartner/components";
import { PartnerField, TradePartner } from "@/modules/TradePartner/Types.partner";
import { partnerTypeAggregator } from "@/modules/TradePartner/Utils.partner";
import { FieldArray, FormikProvider, setNestedObjectValues, useFormik } from "formik";
import { PlusIcon, TrashIcon } from "lucide-react";
import {
  forwardRef,
  Ref,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useReducer,
  useRef,
  useState
} from "react";
import { twMerge } from "tailwind-merge";
import { Button, FieldItem, Input, InputHint, Modal, Select } from "ui";
import {
  capitalizeLetter,
  emptyStringToNull,
  formikInputOpts,
  getFormikError,
  isoDateToString,
  kebabCaseToCapitalize,
  localDateToUtc,
  Nullable,
  pascalToCapitalize
} from "utils";
import {
  CONTAINER_TYPE,
  QUANTITY,
  SHIPMENT_MODE,
  SHIPMENT_STATUS,
  VOLUME,
  WEIGHT
} from "../../Constant.shipment";
import { saveShipmentSchema } from "../../Schema.shipment";
import {
  ShipmentFormModal,
  ShipmentMode,
  TrackingStatus,
  type SaveShipmentParams,
  type Shipment
} from "../../Types.shipment";
import { canEditBl } from "../../Utils.shipment";
import ShipmentLabel from "../ShipmentLabel";
import TrackingLabel from "../TrackingLabel";

enum LocationType {
  POL = "pol",
  POD = "pod",
  DELIVERY = "delivery"
}
type Props = {
  shipment?: Shipment;
  fallback?: FallbackEnricherResponse;
  isLoading?: boolean;
  onSubmit(params: SaveShipmentParams): void;
  isDirty?: (dirty: boolean) => void;
  onSyncTracking?(containerId?: number): void;
};

type ShipmentFormRef = {
  setFormValues: (shipment?: Shipment) => void;
  handleSubmit: () => void;
  element: HTMLDivElement | null;
};

function ShipmentFormComponent(
  { shipment, isLoading, onSubmit, isDirty, onSyncTracking, fallback }: Props,
  ref: Ref<ShipmentFormRef>
) {
  const { isSuperAdmin } = useStore().auth;
  const [importerModal, toggleImporterModal] = useReducer((r) => !r, false);
  const [partnerModal, togglePartnerModal] = useReducer((r) => !r, false);
  const [locationModal, toggleLocationModal] = useReducer((r) => !r, false);
  const [partnerType, setPartner] = useState<PartnerField>();
  const [locationType, setLocation] = useState<LocationType>();
  const [modalValues, setModalValues] = useState<Nullable<ShipmentFormModal>>();

  const handleSubmit = useCallback(
    (params: SaveShipmentParams) => {
      const formatParams: object = {
        ...emptyStringToNull(params),
        volume: params.volume ? Number(params.volume) : undefined,
        weight: params.weight ? Number(params.weight) : undefined,
        quantity: params.quantity ? Number(params.quantity) : undefined,
        etd: params.etd ? localDateToUtc(params.etd) : undefined,
        etaPort: params.etaPort ? localDateToUtc(params.etaPort) : undefined,
        etaDestination: params.etaDestination ? localDateToUtc(params.etaDestination) : undefined,
        pickupLfd: params.pickupLfd ? localDateToUtc(params.pickupLfd) : undefined,
        pickupDate: params.pickupDate ? localDateToUtc(params.pickupDate) : undefined,
        returnLfd: params.returnLfd ? localDateToUtc(params.returnLfd) : undefined,
        containers:
          params.modeOfTransport === ShipmentMode.OCEAN_FCL ||
          params.modeOfTransport === ShipmentMode.OCEAN_LCL
            ? params.containers?.map((container) => ({
                id: container.id,
                containerNumber: container.containerNumber,
                containerType: container.containerType,
                etaDestination: params.etaDestination ? localDateToUtc(params.etaDestination) : undefined,
                pickupDate: container.pickupDate ? localDateToUtc(container.pickupDate) : undefined,
                returnDate: container.returnDate ? localDateToUtc(container.returnDate) : undefined,
                pickupLfd: container.pickupLfd ? localDateToUtc(container.pickupLfd) : undefined,
                returnLfd: container.returnLfd ? localDateToUtc(container.returnLfd) : undefined
              }))
            : undefined
      };
      onSubmit(formatParams);
    },
    [onSubmit]
  );

  const mapShipmentToFormValues = (shipment?: Shipment) => {
    return {
      id: shipment?.id,
      modeOfTransport: shipment?.modeOfTransport ?? undefined,
      status: shipment?.status ?? undefined,
      etd: shipment?.etd ? isoDateToString(shipment.etd) : undefined,
      etaPort: shipment?.etaPort ? isoDateToString(shipment.etaPort) : undefined,
      etaDestination: shipment?.etaDestination ? isoDateToString(shipment.etaDestination) : undefined,
      mblNumber: shipment?.mblNumber ?? "",
      hblNumber: shipment?.hblNumber ?? "",
      cargoControlNumber: shipment?.cargoControlNumber ?? "",
      containerNumber: shipment?.containerNumber ?? "",
      containerType: shipment?.containerType ?? undefined,
      containers:
        shipment?.containers?.map((container) => ({
          ...container,
          containerNumber: container.containerNumber,
          containerType: container.containerType ?? undefined,
          etaDestination: container.etaDestination ? isoDateToString(container.etaDestination) : undefined,
          pickupLfd: container.pickupLfd ? isoDateToString(container.pickupLfd) : undefined,
          pickupDate: container.pickupDate ? isoDateToString(container.pickupDate) : undefined,
          returnLfd: container.returnLfd ? isoDateToString(container.returnLfd) : undefined,
          returnDate: container.returnDate ? isoDateToString(container.returnDate) : undefined
        })) ?? [],
      volume: shipment?.volume ?? undefined,
      volumeUOM: shipment?.volumeUOM ?? undefined,
      weight: shipment?.weight ?? undefined,
      weightUOM: shipment?.weightUOM ?? undefined,
      quantity: shipment?.quantity ?? undefined,
      quantityUOM: shipment?.quantityUOM ?? undefined,
      pickupLfd: shipment?.pickupLfd ? isoDateToString(shipment.pickupLfd) : undefined,
      pickupDate: shipment?.pickupDate ? isoDateToString(shipment.pickupDate) : undefined,
      pickupNumber: shipment?.pickupNumber ?? "",
      returnLfd: shipment?.returnLfd ? isoDateToString(shipment.returnLfd) : undefined,
      vessel: shipment?.vessel ?? "",
      transactionNumber: shipment?.transactionNumber ?? "",
      customsFileNumber: shipment?.customsFileNumber ?? "",
      portOfLoadingId: shipment?.portOfLoading?.id ?? undefined,
      portOfDischargeId: shipment?.portOfDischarge?.id ?? undefined,
      placeOfDeliveryId: shipment?.placeOfDelivery?.id ?? undefined,
      carrierId: shipment?.carrier?.id ?? undefined,
      manufacturerId: shipment?.manufacturer?.id ?? undefined,
      shipperId: shipment?.shipper?.id ?? undefined,
      importerId: shipment?.importer?.id ?? undefined,
      consigneeId: shipment?.consignee?.id ?? undefined,
      forwarderId: shipment?.forwarder?.id ?? undefined,
      truckerId: shipment?.trucker?.id ?? undefined,
      pickupLocationId: shipment?.pickupLocation?.id ?? undefined,
      portCode: shipment?.portCode ?? "",
      portOfExit: shipment?.portOfExit ?? "",
      subLocation: shipment?.subLocation ?? "",
      carrierCode: shipment?.carrierCode ?? ""
    };
  };

  const formik = useFormik({
    initialValues: mapShipmentToFormValues(shipment),
    validationSchema: saveShipmentSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit: handleSubmit
  });

  const elementRef = useRef<HTMLDivElement>(null);
  useImperativeHandle(ref, () => {
    return {
      setFormValues: async (shipment?: Shipment) => {
        const mappedValues = mapShipmentToFormValues(shipment);
        await formik.setTouched(setNestedObjectValues(mappedValues, true));
        await formik.setValues(mappedValues);
      },
      handleSubmit: formik.handleSubmit,
      element: elementRef.current
    };
  }, [formik]);

  useEffect(() => {
    isDirty?.(formik.dirty);
  }, [formik.dirty, isDirty]);

  //#region Modal handler
  useEffect(() => {
    setModalValues((prev) => ({
      ...prev,
      pol: shipment?.portOfLoading,
      pod: shipment?.portOfDischarge,
      delivery: shipment?.placeOfDelivery,
      carrier: shipment?.carrier,
      manufacturer: shipment?.manufacturer,
      shipper: shipment?.shipper,
      importer: shipment?.importer,
      consignee: shipment?.consignee,
      forwarder: shipment?.forwarder,
      trucker: shipment?.trucker,
      pickupLocation: shipment?.pickupLocation
    }));
  }, [shipment]);

  const handleSelectPartner = useCallback(
    (_partner?: TradePartner) => {
      switch (partnerType) {
        case PartnerField.CARRIER:
          setModalValues((prev) => ({ ...prev, carrier: _partner }));
          formik.setFieldValue("carrierId", _partner?.id ?? null);
          break;
        case PartnerField.MANUFACTURER:
          setModalValues((prev) => ({ ...prev, manufacturer: _partner }));
          formik.setFieldValue("manufacturerId", _partner?.id ?? null);
          break;
        case PartnerField.SHIPPER:
          setModalValues((prev) => ({ ...prev, shipper: _partner }));
          formik.setFieldValue("shipperId", _partner?.id ?? null);
          break;
        case PartnerField.CONSIGNEE:
          setModalValues((prev) => ({ ...prev, consignee: _partner }));
          formik.setFieldValue("consigneeId", _partner?.id ?? null);
          break;
        case PartnerField.FORWARDER:
          setModalValues((prev) => ({ ...prev, forwarder: _partner }));
          formik.setFieldValue("forwarderId", _partner?.id ?? null);
          break;
        case PartnerField.TRUCKER:
          setModalValues((prev) => ({ ...prev, trucker: _partner }));
          formik.setFieldValue("truckerId", _partner?.id ?? null);
          break;
        case PartnerField.PICKUP:
          setModalValues((prev) => ({ ...prev, pickupLocation: _partner }));
          formik.setFieldValue("pickupLocationId", _partner?.id ?? null);
          break;
      }
      setPartner(undefined);
      togglePartnerModal();
    },
    [formik, partnerType]
  );

  const handleSelectImporter = useCallback(
    (_importer?: Importer) => {
      setModalValues((prev) => ({ ...prev, importer: _importer }));
      formik.setFieldValue("importerId", _importer?.id ?? null);

      toggleImporterModal();
    },
    [formik]
  );

  const handleSelectLocation = useCallback(
    (_location?: Location) => {
      switch (locationType) {
        case LocationType.POL:
          setModalValues((prev) => ({ ...prev, pol: _location }));
          formik.setFieldValue("portOfLoadingId", _location?.id ?? null);
          break;
        case LocationType.POD:
          setModalValues((prev) => ({ ...prev, pod: _location }));
          formik.setFieldValue("portOfDischargeId", _location?.id ?? null);
          break;
        case LocationType.DELIVERY:
          setModalValues((prev) => ({ ...prev, delivery: _location }));
          formik.setFieldValue("placeOfDeliveryId", _location?.id ?? null);
          break;
      }
      setLocation(undefined);
      toggleLocationModal();
    },
    [formik, locationType]
  );

  const [infoModal, setInfoModal] = useState<TradePartner | Importer | null>();
  const infoDetails = useMemo(() => (infoModal ? objectToFilteredArray(infoModal) : undefined), [infoModal]);
  //#endregion

  //#region Fallback
  const fallbackHelper = useFallback(fallback);
  //#endregion

  return (
    <div className="flex flex-col gap-3" ref={elementRef}>
      {partnerModal && (
        <SelectPartnerModal
          show={partnerModal}
          partner={modalValues?.[partnerType as keyof typeof modalValues] as TradePartner | null | undefined}
          onClose={() => {
            setPartner(undefined);
            togglePartnerModal();
          }}
          partnerType={partnerTypeAggregator(partnerType)}
          onSelect={handleSelectPartner}
        />
      )}
      {importerModal && (
        <SelectImporterModal
          show={importerModal}
          importer={modalValues?.importer}
          onClose={toggleImporterModal}
          onSelect={handleSelectImporter}
        />
      )}
      {locationModal && (
        <SelectLocationModal
          show={locationModal}
          location={modalValues?.[locationType as keyof typeof modalValues] as Location | null | undefined}
          onClose={() => {
            setLocation(undefined);
            toggleLocationModal();
          }}
          onSelect={handleSelectLocation}
        />
      )}
      <Modal
        id="info-modal"
        title="Details"
        show={!!infoModal}
        onClose={() => {
          setInfoModal(undefined);
        }}
      >
        <div className="grid gap-1">
          {infoDetails?.map(([key, value]) => (
            <FieldItem
              key={key}
              direction="row"
              label={`${capitalizeLetter(pascalToCapitalize(key))}:`}
              value={kebabCaseToCapitalize(value)}
            />
          ))}
        </div>
      </Modal>

      <fieldset className="flex flex-col gap-2" disabled={isLoading}>
        <h5>Cargo Information</h5>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-2">
          <Select
            label="Mode of Transport"
            options={SHIPMENT_MODE}
            {...formikInputOpts(formik, "modeOfTransport")}
            optional
          />
          <Select label="Status" options={SHIPMENT_STATUS} {...formikInputOpts(formik, "status")} optional />
          <Input
            label="Cargo Control No."
            placeholder="Input Cargo Control No."
            {...formikInputOpts(formik, "cargoControlNumber")}
            disabled={!isSuperAdmin && !canEditBl(shipment?.customsStatus)}
          />
          <Input label="MBL No." placeholder="Input MBL No." {...formikInputOpts(formik, "mblNumber")} />
          <Input label="HBL No." placeholder="Input HBL No." {...formikInputOpts(formik, "hblNumber")} />
          <Input label="ETD" placeholder="Input ETD" type="date" {...formikInputOpts(formik, "etd")} />
          <Input
            label="ETA Port"
            placeholder="Input Port ETA"
            type="date"
            {...formikInputOpts(formik, "etaPort")}
          />
          <Input
            label="ETA Destination"
            placeholder="Input Destination ETA"
            type="date"
            {...formikInputOpts(formik, "etaDestination")}
          />
          <Input
            label="Port of Loading"
            name="portOfLoadingId"
            placeholder="Select Port of Loading"
            value={modalValues?.pol?.name ?? ""}
            onClick={() => {
              setLocation(LocationType.POL);
              toggleLocationModal();
            }}
            error={getFormikError(formik, "portOfLoadingId")}
            readOnly
          />
          <Input
            label="Port of Discharge"
            name="portOfDischargeId"
            placeholder="Select Port of Discharge"
            value={modalValues?.pod?.name ?? ""}
            onClick={() => {
              setLocation(LocationType.POD);
              toggleLocationModal();
            }}
            error={getFormikError(formik, "portOfDischargeId")}
            readOnly
          />
          <Input
            label="Place of Delivery"
            name="placeOfDeliveryId"
            placeholder="Select Place of Delivery"
            value={modalValues?.delivery?.name ?? ""}
            onClick={() => {
              setLocation(LocationType.DELIVERY);
              toggleLocationModal();
            }}
            error={getFormikError(formik, "placeOfDeliveryId")}
            readOnly
          />
          <Input
            label="Port Code"
            placeholder="Input Port Code"
            {...formikInputOpts(formik, "portCode")}
            disabled={!isSuperAdmin && !canEditBl(shipment?.customsStatus)}
          />
          {formik.values.modeOfTransport === ShipmentMode.LAND && (
            <Input
              label="Port of Exit"
              placeholder="Input Port of Exit"
              {...formikInputOpts(formik, "portOfExit")}
            />
          )}
          <Input
            label="Sub Location"
            placeholder="Input Sub Location"
            {...formikInputOpts(formik, "subLocation")}
            disabled={!isSuperAdmin && !canEditBl(shipment?.customsStatus)}
          />
        </div>
        <div className="grid grid-cols-3 lg:grid-cols-4 gap-4">
          <div className="flex gap-2">
            <Input
              label="Volume"
              placeholder="Input Volume"
              {...formikInputOpts(formik, "volume")}
              onNumberChange={formik.handleChange}
            />
            <Select label="Unit" options={VOLUME} {...formikInputOpts(formik, "volumeUOM")} optional />
          </div>
          <div className="flex gap-2">
            <InputFallbackWrapper
              fallback={fallbackHelper.getFallback("weight")}
              value={formik.values.weight}
            >
              <Input
                label="Weight"
                placeholder={"Input Weight"}
                {...formikInputOpts(formik, "weight")}
                onNumberChange={formik.handleChange}
                onFocus={(e) => {
                  e.target.select();
                }}
              />
            </InputFallbackWrapper>
            <InputFallbackWrapper
              fallback={fallbackHelper.getFallback("weightUOM")}
              value={formik.values.weightUOM}
            >
              <Select label="Unit" options={WEIGHT} {...formikInputOpts(formik, "weightUOM")} optional />
            </InputFallbackWrapper>
          </div>
          <div className="flex gap-2">
            <InputFallbackWrapper
              fallback={fallbackHelper.getFallback("quantity")}
              value={formik.values.quantity}
            >
              <Input
                label="Quantity"
                placeholder={"Input Quantity"}
                {...formikInputOpts(formik, "quantity")}
                onNumberChange={formik.handleChange}
                onFocus={(e) => {
                  e.target.select();
                }}
                noDecimal
              />
            </InputFallbackWrapper>
            <InputFallbackWrapper
              fallback={fallbackHelper.getFallback("quantityUOM")}
              value={formik.values.quantityUOM}
            >
              <Select label="Unit" options={QUANTITY} {...formikInputOpts(formik, "quantityUOM")} optional />
            </InputFallbackWrapper>
          </div>
        </div>

        {(formik.values.modeOfTransport === ShipmentMode.OCEAN_FCL ||
          formik.values.modeOfTransport === ShipmentMode.OCEAN_LCL) && (
          <>
            <hr />
            <div className="flex flex-col gap-2">
              <FormikProvider value={formik}>
                <FieldArray
                  name="containers"
                  render={(arrayHelpers) => (
                    <>
                      <div className="flex justify-between items-center">
                        <h5>Containers</h5>
                        <Button
                          kind="outline"
                          label="Add"
                          icon={<PlusIcon size={16} />}
                          className="py-1 px-2"
                          onClick={() =>
                            arrayHelpers.push({
                              containerNumber: "",
                              containerType: undefined,
                              etaDestination: undefined,
                              pickupLfd: undefined,
                              pickupDate: undefined,
                              returnLfd: undefined,
                              returnDate: undefined
                            })
                          }
                        />
                      </div>
                      <div className="flex flex-col gap-3">
                        {formik.values.containers?.map((container, index: number) => (
                          <div key={index} className="flex flex-col border rounded-lg shadow">
                            <div className={twMerge("flex items-center gap-6 bg-neutral-10 p-2")}>
                              <h6>#{index + 1}</h6>
                              {container.status && (
                                <FieldItem
                                  label="Container Status:"
                                  customElement={<ShipmentLabel value={container.status} />}
                                  direction="row"
                                />
                              )}
                              {container.trackingStatus && (
                                <FieldItem
                                  label="Tracking Status:"
                                  customElement={
                                    <TrackingLabel
                                      value={container.trackingStatus}
                                      isSyncing={container.trackingStatus === TrackingStatus.TRACKING}
                                      onRefresh={() => onSyncTracking?.(container.id)}
                                    />
                                  }
                                  direction="row"
                                />
                              )}
                              <TrashIcon
                                className="size-4 ml-auto cursor-pointer text-danger"
                                onClick={() => arrayHelpers.remove(index)}
                              />
                            </div>
                            <div className="grid grid-cols-3 lg:grid-cols-4 gap-4 flex-1 p-2">
                              <Input
                                label="Container No."
                                placeholder="Input Container No."
                                inputStyle="bg-neutral-10"
                                {...formikInputOpts(formik, `containers.${index}.containerNumber`)}
                              />
                              <Select
                                label="Container Type"
                                inputStyle="bg-neutral-10"
                                options={CONTAINER_TYPE}
                                {...formikInputOpts(formik, `containers.${index}.containerType`)}
                                optional
                              />
                              <Input
                                label="ETA Destination"
                                placeholder="Input Destination ETA"
                                inputStyle="bg-neutral-10"
                                type="date"
                                {...formikInputOpts(formik, `containers.${index}.etaDestination`)}
                              />
                              <Input
                                label="Pickup LFD"
                                placeholder="Input Pickup LFD"
                                inputStyle="bg-neutral-10"
                                type="date"
                                {...formikInputOpts(formik, `containers.${index}.pickupLfd`)}
                              />
                              <Input
                                label="Pickup Date"
                                placeholder="Input Pickup Date"
                                inputStyle="bg-neutral-10"
                                type="date"
                                {...formikInputOpts(formik, `containers.${index}.pickupDate`)}
                              />
                              <Input
                                label="Return LFD"
                                placeholder="Input Return LFD"
                                inputStyle="bg-neutral-10"
                                type="date"
                                {...formikInputOpts(formik, `containers.${index}.returnLfd`)}
                              />
                              <Input
                                label="Return Date"
                                placeholder="Input Return Date"
                                inputStyle="bg-neutral-10"
                                type="date"
                                {...formikInputOpts(formik, `containers.${index}.returnDate`)}
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    </>
                  )}
                />
              </FormikProvider>
            </div>
          </>
        )}

        <hr />

        <h5>Trade Partner</h5>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-2">
          <Input
            label="Importer"
            name="importerId"
            placeholder="Select Importer"
            value={modalValues?.importer?.companyName ?? ""}
            onClick={toggleImporterModal}
            error={getFormikError(formik, "importerId")}
            readOnly
            {...(modalValues?.importer && {
              hint: <InputHint onClick={() => setInfoModal(modalValues.importer)} />
            })}
          />
          <Input
            label="Carrier"
            name="carrierId"
            placeholder="Select Carrier"
            value={modalValues?.carrier?.name ?? ""}
            onClick={() => {
              setPartner(PartnerField.CARRIER);
              togglePartnerModal();
            }}
            error={getFormikError(formik, "carrierId")}
            readOnly
            {...(modalValues?.carrier && {
              hint: (
                <InputHint
                  onClick={() => {
                    setInfoModal(modalValues.carrier);
                  }}
                />
              )
            })}
          />
          <Input
            label="Manufacturer"
            name="manufacturerId"
            placeholder="Select Manufacturer"
            value={modalValues?.manufacturer?.name ?? ""}
            onClick={() => {
              setPartner(PartnerField.MANUFACTURER);
              togglePartnerModal();
            }}
            error={getFormikError(formik, "manufacturerId")}
            readOnly
            {...(modalValues?.manufacturer && {
              hint: (
                <InputHint
                  onClick={() => {
                    setInfoModal(modalValues.manufacturer);
                  }}
                />
              )
            })}
          />
          <Input
            label="Shipper"
            name="shipperId"
            placeholder="Select Shipper"
            value={modalValues?.shipper?.name ?? ""}
            onClick={() => {
              setPartner(PartnerField.SHIPPER);
              togglePartnerModal();
            }}
            error={getFormikError(formik, "shipperId")}
            readOnly
            {...(modalValues?.shipper && {
              hint: <InputHint onClick={() => setInfoModal(modalValues.shipper)} />
            })}
          />
          <Input
            label="Consignee"
            name="consigneeId"
            placeholder="Select Consignee"
            value={modalValues?.consignee?.name ?? ""}
            onClick={() => {
              setPartner(PartnerField.CONSIGNEE);
              togglePartnerModal();
            }}
            error={getFormikError(formik, "consigneeId")}
            readOnly
            {...(modalValues?.consignee && {
              hint: <InputHint onClick={() => setInfoModal(modalValues.consignee)} />
            })}
          />
          <Input
            label="Forwarder"
            name="forwarderId"
            placeholder="Select Forwarder"
            value={modalValues?.forwarder?.name ?? ""}
            onClick={() => {
              setPartner(PartnerField.FORWARDER);
              togglePartnerModal();
            }}
            error={getFormikError(formik, "forwarderId")}
            readOnly
            {...(modalValues?.forwarder && {
              hint: <InputHint onClick={() => setInfoModal(modalValues.forwarder)} />
            })}
          />
          <Input
            label="Trucker"
            name="truckerId"
            placeholder="Select Trucker"
            value={modalValues?.trucker?.name ?? ""}
            onClick={() => {
              setPartner(PartnerField.TRUCKER);
              togglePartnerModal();
            }}
            error={getFormikError(formik, "truckerId")}
            readOnly
            {...(modalValues?.trucker && {
              hint: <InputHint onClick={() => setInfoModal(modalValues.trucker)} />
            })}
          />
        </div>

        <hr />

        <h5>Tracking Information</h5>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-2">
          <Input
            label="Pickup LFD"
            placeholder="Input Pickup LFD"
            type="date"
            {...formikInputOpts(formik, "pickupLfd")}
          />
          <Input
            label="Pickup Location"
            name="pickupLocationId"
            placeholder="Input Pickup Location"
            value={modalValues?.pickupLocation?.name ?? ""}
            onClick={() => {
              setPartner(PartnerField.PICKUP);
              togglePartnerModal();
            }}
            error={getFormikError(formik, "pickupLocationId")}
            readOnly
          />
          <Input
            label="Pickup Date"
            placeholder="Input Pickup Date"
            type="date"
            {...formikInputOpts(formik, "pickupDate")}
          />
          <Input
            label="Pickup No."
            placeholder="Input Pickup No."
            {...formikInputOpts(formik, "pickupNumber")}
          />
          <Input
            label="Return LFD"
            placeholder="Input Return LFD"
            type="date"
            {...formikInputOpts(formik, "returnLfd")}
          />
          <Input label="Vessel Name" placeholder="Input Vessel Name" {...formikInputOpts(formik, "vessel")} />
          <Input
            label="Transaction No."
            placeholder="Input Transaction No."
            {...formikInputOpts(formik, "transactionNumber")}
            disabled={!isSuperAdmin}
          />
          <Input
            label="Customs File No."
            placeholder="Input Customs File No."
            {...formikInputOpts(formik, "customsFileNumber")}
            disabled={!isSuperAdmin}
          />
          <Input
            label="Carrier Code"
            placeholder="Input Carrier Code"
            {...formikInputOpts(formik, "carrierCode")}
          />
        </div>
      </fieldset>
    </div>
  );
}

const ShipmentForm = forwardRef<ShipmentFormRef, Props>(ShipmentFormComponent);
export type { ShipmentFormRef };
export default ShipmentForm;
