import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { useDebounce } from "@uidotdev/usehooks";
import { RefreshCwIcon } from "lucide-react";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useOutletContext } from "react-router-dom";
import { Button, Table } from "ui";
import { SortOrder } from "utils";
import { useGetTrackingHistoryList } from "../../queries";
import { trackingHistoryTableSchema } from "../../Schema.shipment";
import { GetTrackingHistoryList, ShipmentContext, TrackingHistoryColumn } from "../../Types.shipment";

function TrackingHistoryTab() {
  const { shipment } = useOutletContext<ShipmentContext>();
  const [_result, setResult] = useState<string>();
  const result = useDebounce(_result, 500);
  const [filters, setFilters] = useState<GetTrackingHistoryList>();

  const { data, error, isFetching, refetch } = useGetTrackingHistoryList({
    limit: DEFAULT_LIMIT,
    shipmentId: shipment?.id,
    sortBy: TrackingHistoryColumn.timestamp,
    sortOrder: SortOrder.DESC,
    result,
    ...filters
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  return (
    <div className="flex flex-col gap-3">
      <main>
        <Table
          data={data?.trackingHistories}
          schema={trackingHistoryTableSchema}
          isLoading={isFetching}
          header={
            <div className="flex-1 flex items-center justify-between">
              <h5>Shipment Tracking</h5>
              <Button
                label="Refresh"
                icon={<RefreshCwIcon size={16} />}
                onClick={refetch}
                loading={isFetching}
              />
            </div>
          }
          onSearch={setResult}
          onSort={(key?: string, sortOrder?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key as TrackingHistoryColumn,
              sortOrder
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
        />
      </main>
    </div>
  );
}
export default TrackingHistoryTab;
