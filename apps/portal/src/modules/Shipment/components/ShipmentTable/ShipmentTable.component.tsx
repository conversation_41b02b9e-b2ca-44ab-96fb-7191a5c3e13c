import { ChevronDown, ChevronDownIcon, ChevronUp, LucideMinusSquare, LucidePlusSquare } from "lucide-react";
import React, { Fragment, useMemo, useState } from "react";
import { Icon<PERSON>, renderer<PERSON>and<PERSON>, Table } from "ui";
import { SortOrder, TableSchema } from "utils";
import { containerTableSchema } from "../../Schema.shipment";
import { CustomsStatus, Shipment, ShipmentStatus } from "../../Types.shipment";
import CustomsLabel from "../CustomsLabel";
import ShipmentLabel from "../ShipmentLabel";

interface Props {
  shipments?: Record<string, Shipment[] | undefined>;
  schema: TableSchema<Shipment>;
  isLoading?: boolean;
  groupBy: "status" | "customsStatus";
  onRowClick: (shipmentId: number) => void;
  onSort?: (key?: string, direction?: SortOrder) => void;
  sortKey?: string;
  sortDirection?: SortOrder;
}
const ShipmentTable: React.FC<Props> = ({
  shipments,
  schema,
  isLoading,
  groupBy = "status",
  onRowClick,
  onSort,
  sortKey,
  sortDirection
}) => {
  const columns = useMemo(() => Object.entries(schema).filter(([, column]) => column.visible), [schema]);
  const [collapsed, setCollapsed] = useState<Record<string, boolean>>({});
  const [expanded, setExpanded] = useState<Record<number, boolean>>({});

  const toggleCollapse = (status: string) => {
    setCollapsed((prev) => ({
      ...prev,
      [status]: !prev[status]
    }));
  };
  const toggleExpanded = (shipmentId: number) => {
    setExpanded((prev) => ({
      ...prev,
      [shipmentId]: !prev[shipmentId]
    }));
  };

  return (
    <div className="h-full overflow-auto">
      <div className="overflow-x-auto min-w-full inline-block align-middle">
        <table className="w-full table-auto border-collapse bg-white dark:bg-gray-900 dark:text-white">
          <thead className="sticky top-0 z-10 bg-neutral-10 dark:bg-gray-800">
            <tr className="text-xs tracking-wide">
              <th scope="col" className="pl-4 pr-1 lg:pl-6 py-3 text-start" />
              {columns.map(([key, col]) => (
                <th
                  key={key}
                  className={`border-y border-grey pl-4 pr-1 lg:pl-6 py-3 text-start dark:border-gray-600 whitespace-nowrap min-w-36 font-semibold cursor-pointer hover:underline hover:bg-neutral-50 ${col.style}`}
                  onClick={() => {
                    if (col.disableSort || !onSort) return;
                    const newDirection =
                      sortKey !== key && sortKey !== col.sortKey
                        ? SortOrder.ASC
                        : sortDirection === SortOrder.ASC
                          ? SortOrder.DESC
                          : sortDirection === SortOrder.DESC
                            ? undefined
                            : SortOrder.ASC;
                    onSort(!newDirection ? undefined : (col.sortKey ?? key), newDirection);
                  }}
                >
                  {col.header}
                  {onSort && !col.disableSort && (sortKey === key || sortKey === col.sortKey) && (
                    <span className="inline-flex flex-col">
                      {sortDirection === SortOrder.ASC && <ChevronUp className="size-3" />}
                      {sortDirection === SortOrder.DESC && <ChevronDown className="size-3" />}
                    </span>
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td colSpan={columns.length + 1} className="text-center p-2">
                  <Icons.Loader />
                </td>
              </tr>
            ) : shipments && Object.keys(shipments).length > 0 ? (
              Object.entries(shipments).map(([status, value]) => (
                <Fragment key={status}>
                  <tr>
                    <td
                      colSpan={columns.length + 1}
                      className="group-header border-y border-grey bg-neutral-50 dark:bg-neutral-800 dark:border-neutral-800"
                      onClick={() => toggleCollapse(status)}
                    >
                      <div className="flex items-center gap-2 px-4 lg:px-6 py-2 cursor-pointer">
                        {groupBy === "status" ? (
                          <ShipmentLabel
                            value={status as ShipmentStatus}
                            icon={
                              <ChevronDownIcon
                                size={20}
                                className={`transition-transform duration-200 ${
                                  collapsed[status] ? "-rotate-90 transform" : ""
                                }`}
                              />
                            }
                          />
                        ) : (
                          <CustomsLabel
                            value={status as CustomsStatus}
                            icon={
                              <ChevronDownIcon
                                size={20}
                                className={`transition-transform duration-200 ${
                                  collapsed[status] ? "-rotate-90 transform" : ""
                                }`}
                              />
                            }
                          />
                        )}
                        <div className="text-xs text-primary rounded-full bg-primary/5 size-5 flex items-center justify-center border dark:bg-grey-dark dark:text-white">
                          {value?.length}
                        </div>
                      </div>
                    </td>
                  </tr>
                  {!collapsed[status] &&
                    shipments[status]?.map((shipment) => (
                      <Fragment key={shipment.id}>
                        <tr
                          className="text-light cursor-pointer transition duration-100 ease-in-out dark:text-white hover:bg-neutral-100 dark:hover:bg-neutral-800"
                          onClick={() => onRowClick(shipment.id)}
                        >
                          <td className="whitespace-nowrap align-middle">
                            {shipment.containers.length > 0 &&
                              (expanded[shipment.id] ? (
                                <LucideMinusSquare
                                  className="size-4 m-4 hover:text-primary"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    toggleExpanded(shipment.id);
                                  }}
                                />
                              ) : (
                                <LucidePlusSquare
                                  className="size-4 m-4 hover:text-primary"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    toggleExpanded(shipment.id);
                                  }}
                                />
                              ))}
                          </td>
                          {columns.map(([key, col]) => (
                            <td
                              key={key}
                              className={`border-y border-grey pl-4 pr-1 lg:pl-6 py-3 dark:border-neutral-600 min-w-36 ${col.style}`}
                            >
                              {/* @ts-expect-error: Enable implicit any for dynamic table assign */}
                              {rendererHandler(col.renderer, shipment[key])}
                            </td>
                          ))}
                        </tr>
                        {expanded[shipment.id] && (
                          <tr>
                            <td
                              colSpan={columns.length + 1}
                              className="px-4 lg:px-6 py-3 bg-neutral-10 dark:bg-gray-800"
                            >
                              <Table
                                data={shipment.containers}
                                schema={containerTableSchema}
                                className="-mb-3 [&>div]:border-0 -mx-3 pl-8 lg:-mx-6 lg:pl-12"
                              />
                            </td>
                          </tr>
                        )}
                      </Fragment>
                    ))}
                </Fragment>
              ))
            ) : (
              <tr>
                <td colSpan={columns.length + 1} className="text-center p-2">
                  No data
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ShipmentTable;
