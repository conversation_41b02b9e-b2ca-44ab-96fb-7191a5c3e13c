import { MailPages } from "@/modules/Mail/Routes.mail";
import { mailTableSchema } from "@/modules/Mail/Schema.mail";
import { EmailColumn, Mail } from "@/modules/Mail/Types.mail";
import { ShipmentContext } from "@/modules/Shipment/Types.shipment";
import { useEffect, useMemo, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate, useOutletContext } from "react-router-dom";
import { Button, Table } from "ui";
import { SortOrder, sortTable } from "utils";
import { useGetMailList } from "../../../Mail/queries";

const MailTab = () => {
  const { shipment } = useOutletContext<ShipmentContext>();
  const navigate = useNavigate();
  const [filters, setFilters] = useState<{
    sortBy?: string;
    sortOrder?: SortOrder;
  }>({});

  const { data, error, isFetching, refetch } = useGetMailList({
    limit: 1000,
    shipmentId: shipment?.id,
    includeAttachments: true,
    sortBy: EmailColumn.receiveDate,
    sortOrder: SortOrder.DESC
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const sortedData = useMemo(
    () => sortTable(data?.data, filters.sortBy, filters.sortOrder),

    [data?.data, filters.sortBy, filters.sortOrder]
  );

  return (
    <div>
      <Table
        data={sortedData}
        schema={mailTableSchema}
        isLoading={isFetching}
        header={
          <div className="flex-1 flex items-center justify-between">
            <h5>Mail</h5>
            <div className="flex items-center gap-2">
              <Button label="Refresh" kind="refresh" onClick={refetch} loading={isFetching} />
            </div>
          </div>
        }
        onClick={(_data: Mail) => navigate(MailPages.Thread(_data.threadId))}
        total={data?.data?.length}
        onSort={(key?: string, sortOrder?: SortOrder) => {
          setFilters((prev) => ({
            ...prev,
            sortBy: key,
            sortOrder
          }));
        }}
        sortKey={filters?.sortBy}
        sortDirection={filters?.sortOrder}
      />
    </div>
  );
};

export default MailTab;
