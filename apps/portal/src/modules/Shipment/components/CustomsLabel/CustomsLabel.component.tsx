import { ReactElement } from "react";
import { StatusLabel } from "ui";
import { kebabCaseToCapitalize } from "utils";
import type { CustomsStatus } from "../../Types.shipment";
import { customsLabelColor } from "../../Utils.shipment";

type Props = { value: CustomsStatus; icon?: ReactElement };
function CustomsLabel({ value, icon }: Props) {
  const text = kebabCaseToCapitalize(value);
  const color = customsLabelColor(value);

  return <StatusLabel text={text} color={color} icon={icon} className="w-fit justify-center shadow" />;
}
export default CustomsLabel;
