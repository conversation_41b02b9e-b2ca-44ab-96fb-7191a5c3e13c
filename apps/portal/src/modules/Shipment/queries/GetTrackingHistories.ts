import { useQuery } from "@tanstack/react-query";
import ShipmentService from "../services";
import type { GetTrackingHistoryList, GetTrackingHistoryListResponse } from "../Types.shipment";

export const getTrackingHistoriesKey = "getTrackingHistories";

export const useGetTrackingHistoryList = <R = GetTrackingHistoryListResponse>(
  params?: GetTrackingHistoryList,
  select?: (data: GetTrackingHistoryListResponse) => R
) =>
  useQuery({
    queryKey: [getTrackingHistoriesKey, params],
    queryFn: () => ShipmentService.TrackingHistory.list(params),
    select,
    enabled: !!params?.shipmentId
  });
