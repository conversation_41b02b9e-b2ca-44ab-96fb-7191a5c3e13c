import { getCommercialInvoiceLineDto } from "@/modules/CommercialInvoice/dto";
import CommercialInvoiceService from "@/modules/CommercialInvoice/services";
import {
  CommercialInvoice,
  GetCommercialInvoiceList
} from "@/modules/CommercialInvoice/Types.commercial-invoice";
import { useQuery } from "@tanstack/react-query";
import { Currency, DetailParams, formatDate } from "utils";
import ShipmentService from "../services";
import type { DutySummaryTotals, GetDutySummaryListResponse } from "../Types.shipment";

export const getDutySummaryKey = "getDutySummary";

export const useDutySummary = <R = GetDutySummaryListResponse>(
  params: DetailParams,
  select?: (data: GetDutySummaryListResponse) => R
) =>
  useQuery({
    queryKey: [getDutySummaryKey, params],
    queryFn: () => ShipmentService.getDutySummary(params),
    select,
    enabled: !!params?.id
  });

export const useGetDutySummary = (params: GetCommercialInvoiceList) =>
  useQuery({
    queryKey: [getDutySummaryKey, params],
    queryFn: async () => {
      const data = await CommercialInvoiceService.list(params);
      const rates = new Map<string, number>();

      const commercialInvoicesWithTotals = await Promise.all(
        data.commercialInvoices.map(async (invoice: CommercialInvoice) => {
          if (!rates.has(invoice.currency) && invoice.currency !== Currency.CAD) {
            const exchangeRate = await ShipmentService.ExchangeRate.get({
              currencyCode: invoice.currency.toUpperCase(),
              date: invoice.shipment.etd ? formatDate(invoice.shipment.etd) : undefined
            });
            rates.set(invoice.currency, exchangeRate.rate);
          }

          const invoiceLinesWithDto = invoice.commercialInvoiceLines
            .map((line) => {
              return {
                ...line,
                ...getCommercialInvoiceLineDto(line),
                currency: invoice.currency,
                exchangeRate: rates.get(invoice.currency) || 1
              };
            })
            .sort((a, b) => (a.sequence || 0) - (b.sequence || 0));

          const totalValue = invoiceLinesWithDto.reduce((acc, line) => acc + (line.totalLineValue || 0), 0);

          return {
            ...invoice,
            totalValue,
            commercialInvoiceLines: invoiceLinesWithDto
          };
        })
      );

      const dutySummaryLines = commercialInvoicesWithTotals.flatMap(
        (invoice) => invoice.commercialInvoiceLines
      );

      const totals: DutySummaryTotals | undefined =
        dutySummaryLines.length > 0
          ? dutySummaryLines.reduce(
              (acc, line) => {
                const totalValueForDuty = (acc.totalValueForDuty || 0) + (line.valueForDuty || 0);
                const totalAntiDumping = (acc.totalAntiDumping || 0) + (line.antiDumping || 0);
                const totalCountervailing = (acc.totalCountervailing || 0) + (line.countervailing || 0);
                const totalCustomsDuties = (acc.totalCustomsDuties || 0) + (line.customsDuties || 0);
                const totalExciseDuties = (acc.totalExciseDuties || 0) + (line.exciseDuties || 0);
                const totalExciseTax = (acc.totalExciseTax || 0) + (line.exciseTax || 0);
                const totalGst = (acc.totalGst || 0) + (line.gst || 0);
                const totalPstHst = (acc.totalPstHst || 0) + (line.pstHst || 0);
                const totalProvincialAlcoholTax =
                  (acc.totalProvincialAlcoholTax || 0) + (line.provincialAlcoholTax || 0);
                const totalProvincialCannabisExciseDuty =
                  (acc.totalProvincialCannabisExciseDuty || 0) + (line.provincialCannabisExciseDuty || 0);
                const totalProvincialTobaccoTax =
                  (acc.totalProvincialTobaccoTax || 0) + (line.provincialTobaccoTax || 0);
                const totalSafeguard = (acc.totalSafeguard || 0) + (line.safeguard || 0);
                const totalSurtax = (acc.totalSurtax || 0) + (line.surtax || 0);
                const totalTotalDutiesAndTaxes =
                  (acc.totalTotalDutiesAndTaxes || 0) + (line.totalDutiesAndTaxes || 0);

                return {
                  totalValueForDuty,
                  totalAntiDumping,
                  totalCountervailing,
                  totalCustomsDuties,
                  totalExciseDuties,
                  totalExciseTax,
                  totalGst,
                  totalPstHst,
                  totalProvincialAlcoholTax,
                  totalProvincialCannabisExciseDuty,
                  totalProvincialTobaccoTax,
                  totalSafeguard,
                  totalSurtax,
                  totalTotalDutiesAndTaxes
                };
              },
              {
                totalValueForDuty: 0,
                totalAntiDumping: 0,
                totalCountervailing: 0,
                totalCustomsDuties: 0,
                totalExciseDuties: 0,
                totalExciseTax: 0,
                totalGst: 0,
                totalPstHst: 0,
                totalProvincialAlcoholTax: 0,
                totalProvincialCannabisExciseDuty: 0,
                totalProvincialTobaccoTax: 0,
                totalSafeguard: 0,
                totalSurtax: 0,
                totalTotalDutiesAndTaxes: 0
              }
            )
          : undefined;

      return {
        commercialInvoices: commercialInvoicesWithTotals,
        dutySummaryLines,
        totals
      };
    },
    enabled: !!params?.shipmentId
  });
