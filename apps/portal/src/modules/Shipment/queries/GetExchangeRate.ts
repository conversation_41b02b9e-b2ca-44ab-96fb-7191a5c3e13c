import { useQuery } from "@tanstack/react-query";
import ShipmentService from "../services";
import type { GetExchangeRateParams, GetExchangeRateResponse } from "../Types.shipment";

export const getExchangeRateKey = "getExchangeRate";

export const useGetExchangeRate = <R = GetExchangeRateResponse>(
  params: GetExchangeRateParams,
  select?: (data: GetExchangeRateResponse) => R
) =>
  useQuery({
    queryKey: [getExchangeRateKey, params],
    queryFn: () => ShipmentService.ExchangeRate.get(params),
    select,
    enabled: !!params?.currencyCode
  });
