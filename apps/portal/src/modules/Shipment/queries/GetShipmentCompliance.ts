import { useQuery } from "@tanstack/react-query";
import { DetailParams } from "utils";
import ShipmentService from "../services";

export const getShipmentComplianceKey = "getShipmentCompliance";

export const useGetShipmentCompliance = ({ id }: DetailParams) =>
  useQuery({
    queryKey: [getShipmentComplianceKey, id],
    queryFn: () => ShipmentService.getCompliance({ id }),
    enabled: !!id,
    gcTime: 0,
    retry: false
  });
