import { useQuery } from "@tanstack/react-query";
import { DetailParams } from "utils";
import TrackingHistoryService from "../services/TrackingHistory.service";

export const syncShipmentTrackingKey = "syncShipmentTracking";

export const useSyncShipmentTracking = ({ id }: DetailParams) =>
  useQuery({
    queryKey: [syncShipmentTrackingKey, id],
    queryFn: () => TrackingHistoryService.sync({ id }),
    enabled: false,
    retry: false,
    staleTime: 0,
    gcTime: 0
  });
