import { useQuery } from "@tanstack/react-query";
import type { DetailParams } from "utils";
import ShipmentService from "../services";
import type { GetCustomsActivityListResponse } from "../Types.shipment";

export const getCustomsActivitiesKey = "getCustomsActivities";

export const useGetCustomsActivities = <R = GetCustomsActivityListResponse>(
  params: DetailParams,
  select?: (data: GetCustomsActivityListResponse) => R
) =>
  useQuery({
    queryKey: [getCustomsActivitiesKey, params],
    queryFn: () => ShipmentService.getCustomsActivity(params),
    select,
    enabled: !!params?.id
  });
