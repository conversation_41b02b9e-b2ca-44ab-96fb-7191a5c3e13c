import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { flattenQueryResults, infiniteQueryMapper } from "utils";
import { getShipmentDto, getShipmentsDto } from "../dto";
import ShipmentService from "../services";
import type { GetShipmentList, GetShipmentListResponse, Shipment } from "../Types.shipment";

export const getShipmentsKey = "getShipments";

export const useGetShipments = <R = GetShipmentListResponse>(
  params?: GetShipmentList,
  select?: (data: GetShipmentListResponse) => R
) =>
  useQuery({
    queryKey: [getShipmentsKey, params],
    queryFn: () => ShipmentService.list(params),
    select
  });

export const useGetShipmentList = (params?: GetShipmentList) => useGetShipments(params, getShipmentsDto);

export const useGetInfiniteShipments = (params?: GetShipmentList, refetchOnMount = false) => {
  const result = useInfiniteQuery({
    queryKey: [getShipmentsKey, params],
    queryFn: ({ pageParam }) => ShipmentService.list({ ...params, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: infiniteQueryMapper,
    refetchOnMount,
    select: (data) => flattenQueryResults("shipments", data.pages).map(getShipmentDto)
  });

  return result;
};

export const useGetShipmentGroup = (
  params?: GetShipmentList,
  groupBy: "status" | "customsStatus" = "status"
) =>
  useGetShipments(params, (data: GetShipmentListResponse) => {
    const dto = getShipmentsDto(data);

    const shipments = Object.groupBy(dto.shipments, ({ [groupBy]: value }) => value);

    return shipments as Record<string, Shipment[] | undefined>;
  });
