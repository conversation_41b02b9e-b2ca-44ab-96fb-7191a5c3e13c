import { useQuery } from "@tanstack/react-query";
import { DetailParams } from "utils";
import ShipmentService from "../services";

export const getShipmentDetailKey = "getShipmentDetail";

export const useGetShipmentDetail = ({ id }: DetailParams, refetchInterval?: number) =>
  useQuery({
    queryKey: [getShipmentDetailKey, id],
    queryFn: () => ShipmentService.get({ id }),
    enabled: !!id,
    refetchInterval
  });
