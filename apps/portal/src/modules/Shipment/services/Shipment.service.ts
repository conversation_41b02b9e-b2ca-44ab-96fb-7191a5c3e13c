import Http from "@/bootstrap/Http.bootstrap";
import { FallbackEnricherResponse } from "@/common/Types.common";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import type {
  GetCustomsActivityListResponse,
  GetDutySummaryListResponse,
  GetShipmentList,
  GetShipmentListResponse,
  SaveShipmentParams,
  Shipment,
  ShipmentCompliance
} from "../Types.shipment";

const SHIPMENT_ROUTE = "shipments";
/**
 * Get all shipment
 *
 * @returns
 */
async function list(params?: GetShipmentList): Promise<GetShipmentListResponse> {
  try {
    return await Http.get(stringifyQueryParams(SHIPMENT_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get shipment detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<Shipment> {
  try {
    return await Http.get(`${SHIPMENT_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create shipment
 *
 * @returns
 */
async function create(params: SaveShipmentParams): Promise<Shipment> {
  try {
    return await Http.post(SHIPMENT_ROUTE, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit shipment
 *
 * @returns
 */
async function edit({ id, ...params }: SaveShipmentParams): Promise<Shipment> {
  try {
    return await Http.put(`${SHIPMENT_ROUTE}/${id}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Delete shipment
 *
 * @returns
 */
async function remove({ id }: DetailParams): Promise<void> {
  try {
    return await Http.delete(`${SHIPMENT_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get shipment compliance
 *
 * @returns
 */
async function getCompliance({ id }: DetailParams): Promise<ShipmentCompliance> {
  try {
    return await Http.post(`${SHIPMENT_ROUTE}/${id}/validate-compliance`, undefined, {
      timeout: 60000
    });
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get shipment duty summary
 *
 * @returns
 */
async function getDutySummary({ id }: DetailParams): Promise<GetDutySummaryListResponse> {
  try {
    return await Http.get(`${SHIPMENT_ROUTE}/${id}/duty-summary`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get shipment customs activity
 *
 * @returns
 */
async function getCustomsActivity({ id }: DetailParams): Promise<GetCustomsActivityListResponse> {
  try {
    return await Http.get(`${SHIPMENT_ROUTE}/${id}/customs-activities`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Submit entry to candata
 *
 * @returns
 */
async function submitEntry({ id }: DetailParams): Promise<Shipment> {
  try {
    return await Http.post(`${SHIPMENT_ROUTE}/${id}/submit-entry`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Update entry to candata
 *
 * @returns
 */
async function updateEntry({ id }: DetailParams): Promise<Shipment> {
  try {
    return await Http.post(`${SHIPMENT_ROUTE}/${id}/update-entry`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Recalculate duties and taxes
 *
 * @returns
 */
async function recalculateDutiesAndTaxes({ id }: DetailParams): Promise<Shipment> {
  try {
    return await Http.post(`${SHIPMENT_ROUTE}/${id}/recalculate-duties-and-taxes`);
  } catch (error) {
    throw await handleError(error);
  }
}

async function getFallback({ id }: DetailParams): Promise<FallbackEnricherResponse> {
  try {
    return await Http.get(`${SHIPMENT_ROUTE}/${id}/fallbacks`);
  } catch (error) {
    throw await handleError(error);
  }
}

export {
  create,
  edit,
  get,
  getCompliance,
  getCustomsActivity,
  getDutySummary,
  getFallback,
  list,
  recalculateDutiesAndTaxes,
  remove,
  submitEntry,
  updateEntry
};
