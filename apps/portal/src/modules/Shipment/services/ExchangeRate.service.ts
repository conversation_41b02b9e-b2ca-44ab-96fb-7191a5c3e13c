import Http from "@/bootstrap/Http.bootstrap";
import { GetExchangeRateParams, GetExchangeRateResponse } from "../Types.shipment";
import { handleError } from "utils";
/**
 * Get Exchange Rate
 *
 * @returns
 */
async function get({ currencyCode, date }: GetExchangeRateParams): Promise<GetExchangeRateResponse> {
  try {
    return await Http.get(`candata/exchange-rate?currencyCode=${currencyCode}${date ? `&date=${date}` : ""}`);
  } catch (error) {
    throw await handleError(error);
  }
}
export default { get };
