import Http from "@/bootstrap/Http.bootstrap";
import { Container } from "nest-modules";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import type {
  GetShipmentTrackingParams,
  GetTrackingHistoryList,
  GetTrackingHistoryListResponse,
  TrackingHistory
} from "../Types.shipment";

const TRACKING_HISTORY_ROUTE = "tracking-history";
/**
 * Get all tracking history
 *
 * @returns
 */
async function list(params?: GetTrackingHistoryList): Promise<GetTrackingHistoryListResponse> {
  try {
    return await Http.get(stringifyQueryParams(TRACKING_HISTORY_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get tracking history detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<TrackingHistory> {
  try {
    return await Http.get(`${TRACKING_HISTORY_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Delete tracking history
 *
 * @returns
 */
async function remove({ id }: DetailParams): Promise<void> {
  try {
    return await Http.delete(`${TRACKING_HISTORY_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Sync shipment tracking
 *
 * @returns
 */
async function sync({ id, containerId }: GetShipmentTrackingParams): Promise<Container> {
  try {
    return await Http.post(`shipment-tracking/${id}/containers/${containerId}`, undefined, {
      timeout: 90000
    });
  } catch (error) {
    throw await handleError(error);
  }
}

export default { get, list, remove, sync };
