import {
  Email,
  <PERSON>ailWithAttachments,
  GetEmailsByThreadIdResponseDto,
  GetEmailsDto,
  GetEmailsResponseDto
} from "nest-modules";
import { ListParams, PaginationResponse, WithAuditNames } from "utils";

export enum EmailStatus {
  // Get Gmail Message
  UPLOADING_ATTACHMENTS = "uploading-attachments",
  SAVED = "saved",
  FAILED_SAVING_EMAIL = "failed-saving-email",

  // Find Thread Shipment
  AWAIT_SHIPMENT_SEARCH = "await-shipment-search",
  COMPLETED_SHIPMENT_SEARCH = "completed-shipment-search",
  FAILED_SHIPMENT_SEARCH = "failed-shipment-search",

  // Extract User Intents
  ANALYZING_INTENTS = "analyzing-intents",
  INTENTS_ANALYZED = "intents-analyzed",
  FAILED_ANALYZING_INTENTS = "failed-analyzing-intents",

  // Process User Intents
  PROCESSING_INTENTS = "processing-intents",
  INTENTS_PROCESSED = "intents-processed",
  FAILED_PROCESSING_INTENTS = "failed-processing-intents",

  // Aggregate Email
  AGGREGATING_EMAIL = "aggregating-email",
  EMAIL_AGGREGATED = "email-aggregated",
  FAILED_AGGREGATING_EMAIL = "failed-aggregating-email",

  // Generate Email Response
  RESPONDING = "responding",
  RESPONDED = "responded",
  FAILED_RESPONDING = "failed-responding",

  // Requires manual review
  MANUAL_REVIEW = "manual-review",

  // Spam email
  SPAM = "spam",

  // Deleted on Gmail
  DELETED = "deleted",

  // System email skipped
  SYSTEM_EMAIL_SKIPPED = "system-email-skipped"
}

export enum MailOrigin {
  INBOX = "inbox",
  SENT = "sent"
}

export enum EmailColumn {
  id = "id",
  inboxEmail = "inboxEmail",
  origin = "origin",
  status = "status",
  gmailId = "gmailId",
  threadId = "threadId",
  historyId = "historyId",
  receiveDate = "receiveDate",
  from = "from",
  replyTo = "replyTo",
  to = "to",
  cc = "cc",
  subject = "subject",
  text = "text",
  html = "html",
  userIntents = "userIntents",
  requestSummary = "requestSummary",
  processingOutcome = "processingOutcome",
  error = "error",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export type Mail = Email;

export type MailTable = EmailWithAttachments & WithAuditNames & { fromAddresses: string };

export type GetMailList = ListParams<GetEmailsDto>;
export type GetMailListResponse = PaginationResponse & GetEmailsResponseDto;

export type GetMailThreadResponse = ListParams<GetEmailsByThreadIdResponseDto>;
