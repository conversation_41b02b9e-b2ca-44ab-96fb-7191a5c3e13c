import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { useGetActiveImporter } from "@/modules/Importers/queries";
import { useColumnVisibility } from "@/modules/Settings/Hooks.settings";
import { useCallback, useEffect, useReducer, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { Button, ColumnSelector, Header, Pagination, Table } from "ui";
import { SortOrder } from "utils";
import { MailFilter } from "../../components";
import { MAIL_TABLE_KEY } from "../../Constant.mail";
import { useGetMailList } from "../../queries";
import { MailPages } from "../../Routes.mail";
import { mailTableSchema } from "../../Schema.mail";
import { EmailColumn, GetMailList, Mail } from "../../Types.mail";
import { useStore } from "@/bootstrap/Store.bootstrap";
import { observer } from "mobx-react-lite";

const MailList = observer(() => {
  const { isSuperAdmin } = useStore().auth;
  const navigate = useNavigate();
  const [page, setPage] = useState(1);
  const [filter, toggleFilter] = useReducer((r) => !r, false);
  const [filters, setFilters] = useState<GetMailList>();

  const { visibleColumns, handleToggleColumn } = useColumnVisibility({
    defaultColumns: mailTableSchema,
    key: MAIL_TABLE_KEY
  });

  const { data, error, isFetching, refetch } = useGetMailList({
    page,
    includeAttachments: true,
    limit: DEFAULT_LIMIT,
    sortBy: EmailColumn.receiveDate,
    sortOrder: SortOrder.DESC,
    ...filters
  });

  const { data: importer } = useGetActiveImporter();

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const handleFilter = useCallback((values: GetMailList) => {
    setFilters(values);
    setPage(1);
  }, []);

  return (
    <div className="flex flex-col">
      <Header
        title={
          <div>
            <h4 className="leading-5">Mail Inbox</h4>
            <p className="leading-5">{importer?.receiveEmail}</p>
          </div>
        }
      >
        <Button label="Filters" kind="filter" onClick={toggleFilter} />
        <ColumnSelector columns={visibleColumns} onToggleColumn={handleToggleColumn} />
        <Button label="Refresh" loading={isFetching} kind="refresh" onClick={refetch} />
      </Header>

      <section>
        <MailFilter filterValues={handleFilter} isOpen={filter} isSuperAdmin={isSuperAdmin} />
      </section>

      <main>
        <Table
          data={data?.data}
          isLoading={isFetching}
          schema={visibleColumns}
          onClick={(_data: Mail) => navigate(MailPages.Thread(_data.threadId))}
          onSort={(key?: string, direction?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key as EmailColumn,
              sortOrder: direction
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
          footer={
            <Pagination currentPage={page} total={data?.total} limit={DEFAULT_LIMIT} onPageChange={setPage} />
          }
          total={data?.total}
        />
      </main>
    </div>
  );
});
export default MailList;
