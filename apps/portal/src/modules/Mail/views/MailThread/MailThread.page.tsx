import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";
import { ArrowLeftIcon } from "lucide-react";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate, useParams } from "react-router-dom";
import { <PERSON>ert, Card, FieldItem, Header, Icons } from "ui";
import { formatDateTime, getNameInitials } from "utils";
import { EmailBodyRenderer, MailLabel } from "../../components";
import { useGetMailThreads } from "../../queries";
import { MailPages } from "../../Routes.mail";

const MailThread = () => {
  const { thread_id } = useParams();
  const navigate = useNavigate();
  const [subject, setSubject] = useState<string | null>();

  const { data, error, isFetching, isFetched } = useGetMailThreads({
    id: thread_id
  });

  useBackofficePermission(data?.data?.[0]?.organization?.id, MailPages.List);

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }

    if (isFetched && data?.data?.length === 0) {
      navigate(-1);
    } else if (data?.data?.[0]) {
      setSubject(data.data[0].subject);
    }
  }, [data, error, isFetched, navigate]);

  return (
    <div>
      <Header
        title={
          <div className="flex items-center gap-4">
            <ArrowLeftIcon size={16} className="cursor-pointer" onClick={() => navigate(-1)} />
            <h4>{subject}</h4>
          </div>
        }
      />

      {isFetching ? (
        <Icons.Loader />
      ) : (
        <main className="flex flex-col gap-4 p-4">
          {data &&
            data.data &&
            data.data.length > 0 &&
            data.data.map((item) => (
              <Card key={item.id}>
                <header className="flex items-center gap-4 p-4 border-b">
                  <div className="flex size-8 items-center justify-center rounded-full bg-primary/20 dark:bg-neutral-800 font-medium">
                    {getNameInitials(item.from[0].name)}
                  </div>
                  <div className="flex flex-col">
                    <div className="flex gap-2 items-center">
                      <h6>{item.from[0].name}</h6>
                      <small>{item.from[0].address}</small>
                    </div>
                    <div className="inline">
                      <small className="font-medium">To: {item.to[0].name}</small>
                      <small> ({item.to[0].address})</small>
                    </div>
                  </div>

                  <div className="flex flex-col ml-auto items-end">
                    <MailLabel value={item.status} />
                    <small>{formatDateTime(item.receiveDate)}</small>
                  </div>
                </header>

                <div className="p-4 flex flex-col gap-4">
                  <EmailBodyRenderer text={item.text} html={item.html} />
                  {item.requestSummary && <FieldItem label="Request Summary" value={item.requestSummary} />}
                  {item.processingOutcome && (
                    <FieldItem label="Processing Outcome" value={item.processingOutcome} />
                  )}
                </div>

                {item.error && <Alert title="Document processing failed" type="error" message={item.error} />}
              </Card>
            ))}
        </main>
      )}
    </div>
  );
};
export default MailThread;
