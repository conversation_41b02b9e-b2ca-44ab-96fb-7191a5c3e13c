import { RouterObject } from "@/common/Types.common";
import { Mail } from "lucide-react";
import { Link } from "react-router-dom";
import { MailList, MailThread } from "./views";

const MailPages = {
  List: "/mail",
  Thread: (thread_id?: string) => `${MailPages.List}/${thread_id}`
};

const MailNavigation = {
  label: "Mail",
  path: MailPages.List,
  icon: <Mail className="size-6" />,
  pattern: [{ path: `${MailPages.List}/*` }]
};

const MailRoutes: RouterObject = [
  {
    path: MailPages.List,
    handle: {
      crumb: () => <Link to={MailPages.List}>Mail List</Link>
    },
    children: [
      { path: "", element: <MailList /> },
      {
        path: MailPages.Thread(":thread_id"),
        element: <MailThread />
      }
    ]
  }
];

export { MailNavigation, MailPages, MailRoutes };
