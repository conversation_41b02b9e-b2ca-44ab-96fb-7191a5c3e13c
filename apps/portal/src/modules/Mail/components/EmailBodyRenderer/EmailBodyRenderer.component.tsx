import DOMPurify from "dompurify";

function EmailBodyRenderer({ text, html }: { text?: string | null; html?: string | null }) {
  if (text) return <p className="whitespace-pre-wrap break-words">{text}</p>;
  else if (html) {
    const sanitizedHtml = DOMPurify.sanitize(html);
    return <div dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />;
  } else return <p className="italic leading-none">-- Email body is empty --</p>;
}

export default EmailBodyRenderer;
