import { ORDER_BY } from "@/common/Constant.common";
import { useDebounce } from "@uidotdev/usehooks";
import { useEffect, useReducer, useState } from "react";
import { twMerge } from "tailwind-merge";
import { Card, Input, Select } from "ui";
import { OrderBy, SortOrder } from "utils";
import { MAIL_SORT_BY, MAIL_STATUS } from "../../Constant.mail";
import { mailTableSchema } from "../../Schema.mail";
import { EmailColumn, EmailStatus, GetMailList } from "../../Types.mail";
import { Shipment } from "@/modules/Document/Types.document";
import { SelectShipmentModal } from "@/modules/Shipment/components";

type Props = {
  isOpen: boolean;
  isSuperAdmin?: boolean;
  filterValues(values?: GetMailList): void;
};

const MailFilter = ({ isOpen, filterValues, isSuperAdmin }: Props) => {
  const [values, setValues] = useState<GetMailList>();
  const [shipmentModal, toggleShipmentModal] = useReducer((r) => !r, false);
  const [shipment, setShipment] = useState<Shipment>();

  const [formValues, setFormValues] = useState<Partial<GetMailList>>({});
  const debouncedValues = useDebounce(formValues, 500);

  useEffect(() => {
    setValues((prev) => ({
      ...prev,
      ...debouncedValues
    }));
  }, [debouncedValues]);

  useEffect(() => {
    filterValues(values);
  }, [filterValues, values]);

  useEffect(() => () => setValues(undefined), []);

  return (
    <div
      className={twMerge(
        "overflow-hidden transition-max-height duration-300 ease-in-out",
        isOpen ? "max-h-96" : "max-h-0"
      )}
    >
      {shipmentModal && (
        <SelectShipmentModal
          show={shipmentModal}
          onClose={toggleShipmentModal}
          onSelect={(shipment) => {
            setValues((prev) => ({
              ...prev,
              shipmentId: shipment?.id
            }));
            setShipment(shipment);
            toggleShipmentModal();
          }}
        />
      )}
      {isOpen && (
        <Card containerStyle="flex flex-wrap items-center gap-x-3 gap-y-2 px-6 py-4 border">
          <div className="flex gap-3">
            <Input
              label="From"
              placeholder="Search"
              onTextChange={(from: string) => setFormValues((prev) => ({ ...prev, from }))}
              kind="search"
            />
            <Input
              label="Subject"
              placeholder="Search"
              onTextChange={(subject: string) => setFormValues((prev) => ({ ...prev, subject }))}
              kind="search"
            />
            <Input
              label="Content"
              placeholder="Search"
              onTextChange={(content: string) =>
                setFormValues((prev) => ({
                  ...prev,
                  content
                }))
              }
              kind="search"
            />
            <Input
              label="Shipment"
              placeholder="Select shipment"
              value={shipment?.hblNumber ?? ""}
              onClick={toggleShipmentModal}
              readOnly
            />
            <Select
              label="Status"
              onSelected={(status: EmailStatus) => setValues((prev) => ({ ...prev, status }))}
              options={MAIL_STATUS}
              optional
            />
          </div>

          <div className="flex gap-3">
            <Select
              label="Sort By"
              onSelected={(sortBy: keyof typeof mailTableSchema) =>
                setValues((prev) => ({ ...prev, sortBy }) as GetMailList)
              }
              options={MAIL_SORT_BY(isSuperAdmin)}
              defaultValue={EmailColumn.receiveDate}
              optional
            />
            <Select
              label="Sort Direction"
              onSelected={(sortOrder: OrderBy) =>
                setValues((prev) => ({ ...prev, sortOrder }) as GetMailList)
              }
              options={ORDER_BY}
              defaultValue={SortOrder.DESC}
              optional
            />
          </div>
        </Card>
      )}
    </div>
  );
};
export default MailFilter;
