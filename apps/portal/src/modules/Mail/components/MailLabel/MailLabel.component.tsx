import { StatusLabel } from "ui";
import { kebabCaseToCapitalize } from "utils";
import { EmailStatus } from "../../Types.mail";
import { mailLabelColor } from "../../Utils.mail";

type Props = { value: EmailStatus };
function MailLabel({ value }: Props) {
  const text = kebabCaseToCapitalize(value, true);
  const color = mailLabelColor(value);

  return <StatusLabel text={text} color={color} className="w-fit" />;
}
export default MailLabel;
