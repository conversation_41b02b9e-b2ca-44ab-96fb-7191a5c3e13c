import { EmailStatus } from "./Types.mail";

export function mailLabelColor(status?: EmailStatus) {
  switch (status) {
    case EmailStatus.UPLOADING_ATTACHMENTS:
    case EmailStatus.AWAIT_SHIPMENT_SEARCH:
    case EmailStatus.ANALYZING_INTENTS:
    case EmailStatus.PROCESSING_INTENTS:
    case EmailStatus.AGGREGATING_EMAIL:
      return "warning";
    case EmailStatus.SAVED:
    case EmailStatus.RESPONDED:
      return "info";
    case EmailStatus.COMPLETED_SHIPMENT_SEARCH:
    case EmailStatus.INTENTS_ANALYZED:
    case EmailStatus.INTENTS_PROCESSED:
    case EmailStatus.EMAIL_AGGREGATED:
      return "success";
    case EmailStatus.FAILED_SAVING_EMAIL:
    case EmailStatus.FAILED_SHIPMENT_SEARCH:
    case EmailStatus.FAILED_ANALYZING_INTENTS:
    case EmailStatus.FAILED_PROCESSING_INTENTS:
    case EmailStatus.FAILED_AGGREGATING_EMAIL:
    case EmailStatus.FAILED_RESPONDING:
      return "danger";
    case EmailStatus.MANUAL_REVIEW:
    case EmailStatus.SPAM:
      return "disabled";
    default:
      return "gray";
  }
}
