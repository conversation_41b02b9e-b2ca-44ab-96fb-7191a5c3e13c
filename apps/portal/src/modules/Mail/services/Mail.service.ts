import Http from "@/bootstrap/Http.bootstrap";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import { GetMailList, GetMailListResponse, GetMailThreadResponse, Mail } from "../Types.mail";

const MAIL_ROUTE = "emails";
/**
 * Get mail list
 *


 * @returns
 */
async function list(params?: GetMailList): Promise<GetMailListResponse> {
  try {
    return await Http.get(stringifyQueryParams(MAIL_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get mail detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<Mail> {
  try {
    return await Http.get(`${MAIL_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get mail thread
 *
 * @returns
 */
async function getThread({ id }: DetailParams): Promise<GetMailThreadResponse> {
  try {
    return await Http.get(`${MAIL_ROUTE}/thread/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}

export { get, getThread, list };
