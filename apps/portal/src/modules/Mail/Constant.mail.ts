import { enumToSelectOptions, schemaToSelectOptions } from "utils";
import { mailTableSchema } from "./Schema.mail";
import { EmailStatus } from "./Types.mail";

export const MAIL_STATUS = enumToSelectOptions(EmailStatus);
export const MAIL_SORT_BY = (isSuperAdmin?: boolean) => {
  return [
    ...schemaToSelectOptions(mailTableSchema),
    ...(isSuperAdmin
      ? [
          {
            label: "Organization",
            value: "organizationId"
          }
        ]
      : [])
  ];
};

export const MAIL_TABLE_KEY = "mailTable";
