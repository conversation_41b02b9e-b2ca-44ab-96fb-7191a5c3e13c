import { useQuery } from "@tanstack/react-query";
import { DetailParams } from "utils";
import MailService from "../services";
import { GetMailThreadResponse } from "../Types.mail";

export const getMailThreadsKey = "getMailThreads";

export const useGetMailThreads = <R = GetMailThreadResponse>(
  params: DetailParams,
  select?: (data: GetMailThreadResponse) => R
) =>
  useQuery({
    queryKey: [getMailThreadsKey, params],
    queryFn: () => MailService.getThread(params),
    select,
    enabled: !!params.id
  });
