import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { flattenQueryResults, getAuditNames, infiniteQueryMapper } from "utils";
import MailService from "../services";
import { GetMailList, GetMailListResponse, Mail } from "../Types.mail";

export const getMailDto = (mail: Mail) => ({
  ...mail,
  fromAddresses: mail.from?.map((f) => f.address).join(", ") || "",
  organizationName: mail.organization?.name || "",
  ...getAuditNames(mail)
});

export const getMailsKey = "getMails";

export const useGetMails = <R = GetMailListResponse>(
  params?: GetMailList,
  select?: (data: GetMailListResponse) => R,
  refetchOnMount?: boolean
) =>
  useQuery({
    queryKey: [getMailsKey, params],
    queryFn: () => MailService.list(params),
    select,
    refetchOnMount
  });

export const useGetMailList = (params?: GetMailList) =>
  useGetMails(params, (data: GetMailListResponse) => {
    const mails = data.data.map(getMailDto);

    return { ...data, data: mails };
  });

export const useGetInfiniteMails = (params?: GetMailList, refetchOnMount?: boolean) => {
  const result = useInfiniteQuery({
    queryKey: [getMailsKey, params],
    queryFn: ({ pageParam }) => MailService.list({ ...params, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: infiniteQueryMapper,
    refetchOnMount,
    select: (data) => flattenQueryResults("data", data.pages)
  });

  return result;
};
