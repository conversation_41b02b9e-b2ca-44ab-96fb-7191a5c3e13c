import { TableSchema } from "utils";
import { FileRenderer } from "../Document/components";
import { File } from "../Document/Types.document";
import { MailLabel } from "./components";
import { EmailStatus, MailTable } from "./Types.mail";

export const mailTableSchema: TableSchema<MailTable> = {
  fromAddresses: {
    header: "From",
    style: "font-medium",
    visible: true,
    sortKey: "from"
  },
  subject: {
    header: "Subject",
    style: "max-w-80 truncate",
    visible: true
  },
  status: {
    header: "Status",
    renderer: ({ value }: { value: EmailStatus }) => <MailLabel value={value} />,
    visible: true
  },
  files: {
    header: "Files",
    renderer: ({ value }: { value: File[] }) =>
      value.length > 0 ? (
        <div className="flex flex-col gap-1">
          {value.map((file) => (
            <FileRenderer value={file} key={file.id} />
          ))}
        </div>
      ) : (
        ""
      ),
    visible: true
  },
  text: {
    header: "Message",
    style: "max-w-80 truncate",
    visible: true
  },
  receiveDate: {
    header: "Received",
    renderer: "dateTime",
    visible: true
  }

  // ...auditTableSchema,
};
