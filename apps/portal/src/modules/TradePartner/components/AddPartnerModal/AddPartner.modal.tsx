import { parseAddress } from "@/common/Utils.common";
import { SelectCountry } from "@/modules/Location/components";
import { Country } from "@/modules/Location/Types.location";
import { useFormik } from "formik";
import { useCallback, useEffect, useState } from "react";
import { AddressAutoComplete, Button, Input, Modal, Select } from "ui";
import { formikInputOpts, getFormikError } from "utils";
import { PARTNER_TYPE } from "../../Constant.partner";
import { savePartnerSchema } from "../../Schema.partner";
import type { PartnerType, SavePartnerParams, TradePartner } from "../../Types.partner";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";
import { env } from "@/config/Environment.config";

type Props = {
  show: boolean;
  info?: TradePartner;
  isLoading?: boolean;
  partnerType?: PartnerType;
  isAdmin?: boolean;
  onSubmit(partner: SavePartnerParams): void;
  onDelete(): void;
  onClose(): void;
};
function AddPartnerModal({
  show,
  info,
  isLoading,
  partnerType,
  isAdmin,
  onSubmit,
  onDelete,
  onClose
}: Props) {
  const [country, setCountry] = useState<Country>();
  const { canCrudBackoffice } = useBackofficePermission();

  const { resetForm, ...formik } = useFormik({
    initialValues: {
      id: info?.id,
      name: info?.name || "",
      partnerType: info?.partnerType || partnerType,
      vendorCode: info?.vendorCode || "",
      email: info?.email || "",
      phoneNumber: info?.phoneNumber || "",
      address: info?.address || "",
      city: info?.city || "",
      state: info?.state || "",
      postalCode: info?.postalCode || "",
      countryId: info?.country?.id,
      eManifestCarrierCode: info?.eManifestCarrierCode || ""
    },
    validationSchema: savePartnerSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit
  });

  const handleAddress = useCallback(
    async (place: google.maps.places.PlaceResult) => {
      const address = await parseAddress(place);
      if (address) {
        formik.setFieldValue("address", address?.street);
        formik.setFieldValue("city", address?.city);
        formik.setFieldValue("postalCode", address?.zipCode);
        formik.setFieldValue("state", address?.state);
        formik.setFieldValue("countryId", address?.country?.id);
        setCountry(address.country);
      }
    },
    [formik]
  );

  useEffect(() => {
    if (info?.country) setCountry(info.country);
  }, [info?.country]);

  useEffect(() => {
    return () => setCountry(undefined);
  }, []);

  return (
    <Modal
      id="partner-modal"
      show={show}
      onClose={() => {
        resetForm();
        setCountry(undefined);
        onClose();
      }}
      title={`${info ? "Edit" : "Add New"} Partner`}
      actions={
        <>
          {info && (isAdmin || canCrudBackoffice) && (
            <Button
              label="Delete"
              kind="delete"
              loading={isLoading}
              disabled={formik.dirty}
              onClick={onDelete}
            />
          )}
          {canCrudBackoffice && (
            <Button
              label={info ? "Update" : "Add"}
              kind="update"
              loading={isLoading}
              onClick={formik.handleSubmit}
            />
          )}
        </>
      }
    >
      <form className="flex flex-col gap-2 px-2">
        <Input label="Name" placeholder="Input partner name" {...formikInputOpts(formik, "name")} />
        <Select
          label="Partner Type"
          options={PARTNER_TYPE}
          {...formikInputOpts(formik, "partnerType")}
          optional
        />
        <Input
          label="Vendor Code"
          placeholder="Input vendor code"
          {...formikInputOpts(formik, "vendorCode")}
        />
        <Input
          label="eManifest Carrier Code"
          placeholder="Input eManifest carrier code"
          {...formikInputOpts(formik, "eManifestCarrierCode")}
        />
        <Input label="Email" type="email" placeholder="Input email" {...formikInputOpts(formik, "email")} />
        <Input
          label="Phone Number"
          placeholder="Input phone number"
          {...formikInputOpts(formik, "phoneNumber")}
        />
        <AddressAutoComplete
          apiKey={env.mapsApiKey}
          label="Address"
          placeholder="Input address"
          {...formikInputOpts(formik, "address")}
          onSelected={handleAddress}
        />
        <Input label="City" placeholder="Input city" {...formikInputOpts(formik, "city")} />
        <Input label="State" placeholder="Input state" {...formikInputOpts(formik, "state")} />
        <Input
          label="Postal Code"
          placeholder="Input postal code"
          {...formikInputOpts(formik, "postalCode")}
        />
        <SelectCountry
          label="Country"
          placeholder="Select country"
          value={country}
          onSelect={(value) => formik.setFieldValue("countryId", value?.value)}
          error={getFormikError(formik, "countryId")}
        />
      </form>
    </Modal>
  );
}
export default AddPartnerModal;
