import { ORDER_BY } from "@/common/Constant.common";
import { SelectCountry } from "@/modules/Location/components";
import { useDebounce } from "@uidotdev/usehooks";
import { useEffect, useState } from "react";
import { Input, Select } from "ui";
import { OrderBy, SortOrder } from "utils";
import { PARTNER_SORT_BY, PARTNER_TYPE } from "../../Constant.partner";
import { tradePartnerTableSchema } from "../../Schema.partner";
import { GetPartnerList, PartnerType, TradePartnerColumn } from "../../Types.partner";

type Props = {
  type?: PartnerType;
  name?: string;
  filterValues(values?: GetPartnerList): void;
};
const TradePartnerFilter = ({ type, name: n, filterValues }: Props) => {
  const [values, setValues] = useState<GetPartnerList>();

  const [_name, setName] = useState<string | undefined>(n);
  const name = useDebounce(_name, 500);

  useEffect(() => {
    setValues((prev) => ({
      ...prev,
      name: name ?? undefined
    }));
  }, [name]);

  useEffect(() => {
    filterValues(values);
  }, [filterValues, values]);

  useEffect(() => () => setValues(undefined), []);

  return (
    <div className="flex flex-wrap items-center gap-x-3 gap-y-2">
      <div className="flex gap-3">
        <Input label="Name" placeholder="Search" onTextChange={setName} kind="search" value={_name ?? ""} />
        <Select
          label="Type"
          onSelected={(partnerType: PartnerType) => setValues((prev) => ({ ...prev, partnerType }))}
          options={PARTNER_TYPE}
          defaultValue={type}
          optional
        />
        <SelectCountry
          placeholder="Select Country"
          onSelect={(country) => {
            if (typeof country?.value === "number" || country?.value === undefined) {
              setValues((prev) => ({
                ...prev,
                countryId: country?.value as number
              }));
            }
          }}
        />
      </div>

      <div className="flex gap-3">
        <Select
          label="Sort By"
          onSelected={(sortBy: keyof typeof tradePartnerTableSchema) =>
            setValues((prev) => ({ ...prev, sortBy }) as GetPartnerList)
          }
          options={PARTNER_SORT_BY}
          defaultValue={TradePartnerColumn.createDate}
          optional
        />
        <Select
          label="Sort Direction"
          onSelected={(sortOrder: OrderBy) => setValues((prev) => ({ ...prev, sortOrder }) as GetPartnerList)}
          options={ORDER_BY}
          defaultValue={SortOrder.DESC}
          optional
        />
      </div>
    </div>
  );
};
export default TradePartnerFilter;
