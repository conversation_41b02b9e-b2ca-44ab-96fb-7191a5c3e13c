import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { SettingsPages } from "@/modules/Settings/Routes.settings";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { But<PERSON>, Modal, Table } from "ui";
import { SortOrder } from "utils";
import { tradePartnerTableSchema } from "../../Schema.partner";
import { GetPartnerList, PartnerType, TradePartner, TradePartnerColumn } from "../../Types.partner";
import { useGetInfinitePartners } from "../../queries/GetPartners.query";
import TradePartnerFilter from "../TradePartnerFilter";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";

type Props = {
  show: boolean;
  partnerType?: PartnerType;
  partner?: TradePartner | null;
  required?: boolean;
  multiple?: boolean;
  onSelect(item?: TradePartner): void;
  onClose(): void;
};

function SelectPartnerModal({ show, partnerType, partner, required, multiple, onSelect, onClose }: Props) {
  const navigate = useNavigate();
  const location = useLocation();
  const locationState = location.state;
  const [selected, setSelected] = useState<TradePartner>();
  const [filters, setFilters] = useState<GetPartnerList>();
  const { canCrudBackoffice } = useBackofficePermission();

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } = useGetInfinitePartners(
    {
      partnerType: partner?.partnerType ?? partnerType,
      limit: DEFAULT_LIMIT,
      sortBy: TradePartnerColumn.createDate,
      sortOrder: SortOrder.DESC,
      ...filters
    },
    true
  );

  // Select the default partner
  useEffect(() => {
    if (partner && data && data.length > 0) {
      const foundPartner = data.find((p) => p.id === partner.id);
      if (foundPartner) setSelected(foundPartner);
    }
  }, [partner, data]);

  const handleSelect = () => {
    if (!multiple) onSelect(selected ?? undefined);
  };

  return (
    <Modal
      id="select-partner-modal"
      show={show}
      onClose={onClose}
      title="Select Partner"
      size="4xl"
      bodyStyle="pt-0"
      actions={
        <>
          {!required && (
            <Button
              label="Clear"
              kind="cancel"
              onClick={() => {
                setSelected(undefined);
                onSelect(undefined);
              }}
            />
          )}
          {selected && canCrudBackoffice && (
            <Button
              label="Edit"
              kind="edit"
              onClick={() => {
                navigate(`${SettingsPages.List}/${SettingsPages.TradePartners}`, {
                  state: {
                    from: location.pathname,
                    partnerType,
                    partner: selected
                  }
                });
              }}
            />
          )}
          {canCrudBackoffice && (
            <Button
              label="Create"
              kind="create"
              onClick={() => {
                navigate(`${SettingsPages.List}/${SettingsPages.TradePartners}`, {
                  state: {
                    ...locationState,
                    from: location.pathname,
                    partnerType,
                    originalFrom: locationState?.originalFrom ?? locationState?.from,
                    partner: undefined
                  }
                });
              }}
            />
          )}

          <Button label="Select" onClick={handleSelect} disabled={!data?.length || !selected} />
        </>
      }
    >
      <div>
        <div className="sticky top-0 z-10 bg-white py-4">
          <TradePartnerFilter
            filterValues={setFilters}
            type={partner?.partnerType ?? partnerType}
            name={partner?.name}
          />
        </div>
        <div className="overflow-auto flex-1">
          <Table
            data={data}
            schema={tradePartnerTableSchema}
            checklist
            selected={selected ? [selected] : undefined}
            onClick={setSelected}
            isLoading={isLoading}
            onEndReached={fetchNextPage}
            hasNextPage={hasNextPage}
            isFetchingNextPage={isFetchingNextPage}
            onSort={(key?: string, direction?: SortOrder) => {
              setFilters((prev) => ({
                ...prev,
                sortBy: key as TradePartnerColumn,
                sortOrder: direction
              }));
            }}
            sortKey={filters?.sortBy}
            sortDirection={filters?.sortOrder}
          />
        </div>
      </div>
    </Modal>
  );
}
export default SelectPartnerModal;
