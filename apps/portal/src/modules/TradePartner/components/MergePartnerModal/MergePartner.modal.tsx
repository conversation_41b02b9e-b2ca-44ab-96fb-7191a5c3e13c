import { useInvalidateQuery } from "@/common/Utils.common";
import { LucideArrowLeftRight, LucideMerge } from "lucide-react";
import { EditTradePartnerDto } from "nest-modules";
import { useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { <PERSON><PERSON>, Modal } from "ui";
import { TradePartner } from "../../Types.partner";
import { useMergePartner } from "../../mutations";
import { getPartnersKey } from "../../queries/GetPartners.query";

interface MergePartnerModalProps {
  show: boolean;
  onClose: () => void;
  partners: TradePartner[];
}

interface FieldOption {
  label: string;
  key: string;
  selected: "source" | "target";
}

export const MergePartnerModal = ({ show, onClose, partners }: MergePartnerModalProps) => {
  const [sourceIndex, setSourceIndex] = useState(0);
  const [targetIndex, setTargetIndex] = useState(1);
  const [fieldOptions, setFieldOptions] = useState<FieldOption[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const invalidateQuery = useInvalidateQuery(getPartnersKey);
  const mergePartners = useMergePartner();

  // Initialize field options when partners change
  useEffect(() => {
    if (partners.length !== 2) return;

    const options: FieldOption[] = [
      { key: "partnerType", label: "Partner Type", selected: "target" },
      { key: "vendorCode", label: "Vendor Code", selected: "target" },
      { key: "eManifestCarrierCode", label: "eManifest Carrier Code", selected: "target" },
      { key: "name", label: "Name", selected: "target" },
      { key: "email", label: "Email", selected: "target" },
      { key: "phoneNumber", label: "Phone Number", selected: "target" },
      { key: "address", label: "Address", selected: "target" },
      { key: "city", label: "City", selected: "target" },
      { key: "state", label: "State", selected: "target" },
      { key: "postalCode", label: "Postal Code", selected: "target" },
      { key: "countryId", label: "Country", selected: "target" }
    ];

    setFieldOptions(options);
  }, [partners]);

  const handleSwapPartners = useCallback(() => {
    setSourceIndex(targetIndex);
    setTargetIndex(sourceIndex);
  }, [sourceIndex, targetIndex]);

  const handleFieldSelection = useCallback((field: string, value: "source" | "target") => {
    setFieldOptions((prev) =>
      prev.map((option) => (option.key === field ? { ...option, selected: value } : option))
    );
  }, []);

  const handleMerge = useCallback(async () => {
    if (partners.length !== 2) return;

    setIsLoading(true);

    const sourcePartner = partners[sourceIndex];
    const targetPartner = partners[targetIndex];

    // Create merged data object based on selected fields
    const mergedData: EditTradePartnerDto = {};

    fieldOptions.forEach((option) => {
      const sourceValue = sourcePartner[option.key as keyof TradePartner];
      const targetValue = targetPartner[option.key as keyof TradePartner];

      // Only add properties that are not undefined
      if (option.selected === "source" && sourceValue !== undefined) {
        // Need to cast to any because EditTradePartnerDto has different types
        (mergedData as any)[option.key] = sourceValue;
      } else if (option.selected === "target" && targetValue !== undefined) {
        (mergedData as any)[option.key] = targetValue;
      }
    });

    try {
      await mergePartners.mutateAsync({
        sourceTradePartnerId: sourcePartner.id,
        targetTradePartnerId: targetPartner.id,
        mergedData
      });

      toast.success("Trade partners merged successfully");
      invalidateQuery();
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to merge trade partners");
    } finally {
      setIsLoading(false);
    }
  }, [partners, sourceIndex, targetIndex, fieldOptions, mergePartners, invalidateQuery, onClose]);

  if (partners.length !== 2) {
    return null;
  }

  const sourcePartner = partners[sourceIndex];
  const targetPartner = partners[targetIndex];

  return (
    <Modal
      id="merge-partner-modal"
      show={show}
      onClose={onClose}
      title="Merge Trade Partners"
      footer={
        <div className="flex justify-end gap-2 px-4 py-3">
          <Button label="Cancel" kind="outline" onClick={onClose} disabled={isLoading} />
          <Button
            label="Merge Partners"
            kind="create"
            icon={<LucideMerge className="size-3" />}
            onClick={handleMerge}
            loading={isLoading}
          />
        </div>
      }
      size="4xl"
    >
      <div className="flex sticky top-0 z-10 bg-white px-4 py-3 shadow-sm rounded-md border">
        <div className="flex-1">
          <h6 className="font-medium">Source</h6>
          <div className="text-sm">{sourcePartner.name}</div>
        </div>

        <Button
          kind="outline"
          icon={<LucideArrowLeftRight className="size-3" />}
          className="mx-10"
          onClick={handleSwapPartners}
          aria-label="Swap source and target"
        />

        <div className="flex-1">
          <h6 className="font-medium">Target</h6>
          <div className="text-sm">{targetPartner.name}</div>
        </div>
      </div>

      <div className="mt-6 text-sm">
        {fieldOptions.map((option) => {
          const sourceValue = sourcePartner[option.key as keyof TradePartner];
          const targetValue = targetPartner[option.key as keyof TradePartner];

          return (
            <div key={option.key} className="grid grid-cols-3 gap-3 border-b py-2">
              <div className="font-medium">{option.label}</div>

              <div className="flex items-center">
                <input
                  type="radio"
                  id={`${option.key}-source`}
                  name={option.key}
                  checked={option.selected === "source"}
                  onChange={() => handleFieldSelection(option.key, "source")}
                  className="mr-2"
                />
                <div>
                  {sourceValue !== null && sourceValue !== undefined ? String(sourceValue) : "(empty)"}
                </div>
              </div>

              <div className="flex items-center">
                <input
                  type="radio"
                  id={`${option.key}-target`}
                  name={option.key}
                  checked={option.selected === "target"}
                  onChange={() => handleFieldSelection(option.key, "target")}
                  className="mr-2"
                />
                <div>
                  {targetValue !== null && targetValue !== undefined ? String(targetValue) : "(empty)"}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </Modal>
  );
};

export default MergePartnerModal;
