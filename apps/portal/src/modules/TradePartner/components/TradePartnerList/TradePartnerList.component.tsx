import { useStore } from "@/bootstrap/Store.bootstrap";
import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { BaseError } from "@/common/Types.common";
import { useInvalidateQuery } from "@/common/Utils.common";
import { useBackofficePermission } from "@/modules/Auth/Hooks.auth";
import { useColumnVisibility } from "@/modules/Settings/Hooks.settings";
import { LucideMerge } from "lucide-react";
import { observer } from "mobx-react-lite";
import { useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useLocation, useNavigate } from "react-router-dom";
import { Alert, Button, ColumnSelector, Pagination, Popup, Table } from "ui";
import { emptyStringToNull, SortOrder } from "utils";
import { PARTNER_TABLE_KEY } from "../../Constant.partner";
import { tradePartnerTableSchema } from "../../Schema.partner";
import {
  type GetPartnerList,
  type SavePartnerParams,
  type TradePartner,
  TradePartnerColumn
} from "../../Types.partner";
import { useDeletePartner, useSavePartner } from "../../mutations";
import { getPartnersKey, useGetPartnerList } from "../../queries/GetPartners.query";
import AddPartnerModal from "../AddPartnerModal";
import MergePartnerModal from "../MergePartnerModal";
import TradePartnerFilter from "../TradePartnerFilter";

const TradePartnerList = observer(() => {
  const { isAdmin } = useStore().auth;
  const { state } = useLocation();
  const navigate = useNavigate();
  const { showPopup, hidePopup } = Popup.usePopup();
  const { canCrudBackoffice } = useBackofficePermission();

  const { visibleColumns, handleToggleColumn } = useColumnVisibility({
    defaultColumns: tradePartnerTableSchema,
    key: PARTNER_TABLE_KEY
  });

  const [modal, setModal] = useState(false);
  const [partner, setPartner] = useState<TradePartner>();
  const [page, setPage] = useState(1);
  const [limit] = useState(DEFAULT_LIMIT);
  const [filters, setFilters] = useState<GetPartnerList>();

  const invalidateQuery = useInvalidateQuery(getPartnersKey);
  const savePartner = useSavePartner();
  const deletePartner = useDeletePartner();

  const { data, error, isFetching, refetch } = useGetPartnerList({
    page,
    limit,
    sortBy: TradePartnerColumn.createDate,
    sortOrder: SortOrder.DESC,
    ...filters
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  useEffect(() => {
    if (state?.from) {
      if (state?.partner) setPartner(state.partner);
      setModal(true);
    }
  }, [state?.from, state?.partner]);

  //#region Handler
  const handleSavePartner = useCallback(
    async (params: SavePartnerParams) => {
      const formatParams: SavePartnerParams = {
        ...emptyStringToNull(params),
        countryId: params.countryId ? Number(params.countryId) : undefined
      };
      try {
        const res = await savePartner.mutateAsync(formatParams);
        invalidateQuery();

        toast.success(`Trade partner ${formatParams.name} ${partner ? "saved" : "added"}`);

        setModal(false);
        setPartner(undefined);

        if (state?.from) {
          navigate(state.from, {
            state: {
              ...state,
              partner: res
            }
          });
        }

        refetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [invalidateQuery, navigate, partner, refetch, savePartner, state]
  );

  const handleDeletePartner = useCallback(async () => {
    try {
      await deletePartner.mutateAsync({ id: partner?.id });

      toast.success("Trade partner deleted successfully");

      setModal(false);
      setPartner(undefined);
      refetch();
    } catch (error) {
      toast.error((error as BaseError).message);
    }
  }, [deletePartner, partner?.id, refetch]);

  const handleDelete = () => {
    showPopup(
      {
        content: "Are you sure you want to delete this trade partner?",
        onProceed: () => {
          handleDeletePartner();
          hidePopup();
        }
      },
      Popup.PopupType.DELETE_POPUP
    );
  };

  const handleFilter = useCallback((values: GetPartnerList) => {
    setFilters(values);
    setPage(1);
  }, []);
  //#endregion

  const [selectedTradePartners, setSelectedTradePartners] = useState<TradePartner[]>([]);
  const [showMergeModal, setShowMergeModal] = useState(false);

  return (
    <div className="flex flex-col gap-3">
      <AddPartnerModal
        show={modal}
        info={partner}
        isAdmin={isAdmin}
        isLoading={savePartner.isPending || deletePartner.isPending}
        onClose={() => {
          setModal(false);
          setPartner(undefined);
        }}
        onSubmit={handleSavePartner}
        onDelete={handleDelete}
        partnerType={state?.partnerType}
      />

      <MergePartnerModal
        show={showMergeModal}
        onClose={() => {
          setShowMergeModal(false);
          setSelectedTradePartners([]);
        }}
        partners={selectedTradePartners}
      />

      <header className="flex gap-3 items-end">
        <TradePartnerFilter filterValues={handleFilter} />
        <ColumnSelector
          columns={visibleColumns}
          onToggleColumn={handleToggleColumn}
          containerStyle="ml-auto"
        />
        {canCrudBackoffice && (
          <Button
            label="New Partner"
            kind="create"
            onClick={() => setModal(true)}
            disabled={savePartner.isPending || deletePartner.isPending}
          />
        )}
      </header>

      <main>
        {selectedTradePartners.length > 0 && (
          <Alert
            type="info"
            className="mb-3 sticky top-3 z-10"
            compact
            icon={null}
            title={`${selectedTradePartners.length} trade partner(s) selected`}
            actionPosition="end"
            action={
              <>
                {selectedTradePartners.length === 2 && canCrudBackoffice && (
                  <Button
                    label="Merge"
                    onClick={() => setShowMergeModal(true)}
                    icon={<LucideMerge className="size-4" />}
                  />
                )}
                <Button label="Clear Selection" kind="outline" onClick={() => setSelectedTradePartners([])} />
              </>
            }
          />
        )}
        <Table
          data={data?.partners}
          isLoading={isFetching}
          schema={visibleColumns}
          checklist
          multiple
          selectAll
          selected={selectedTradePartners}
          onMultiSelect={setSelectedTradePartners}
          onClick={(_data: TradePartner) => {
            setPartner(_data);
            setModal(true);
          }}
          onSort={(key?: string, direction?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key as TradePartnerColumn,
              sortOrder: direction
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
          footer={
            <Pagination currentPage={page} total={data?.total} limit={DEFAULT_LIMIT} onPageChange={setPage} />
          }
          total={data?.total}
        />
      </main>
    </div>
  );
});
export default TradePartnerList;
