import Http from "@/bootstrap/Http.bootstrap";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import type {
  GetPartnerList,
  GetPartnerListResponse,
  MergeTradePartnerDto,
  SavePartnerParams,
  TradePartner
} from "../Types.partner";

const PARTNER_ROUTE = "trade-partners";
/**
 * Get all trade partner
 *
 * @returns
 */
async function list(params?: GetPartnerList): Promise<GetPartnerListResponse> {
  try {
    return await Http.get(stringifyQueryParams(PARTNER_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get partner detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<TradePartner> {
  try {
    return await Http.get(`${PARTNER_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create partner
 *
 * @returns
 */
async function create(params: SavePartnerParams): Promise<TradePartner> {
  try {
    return await Http.post(PARTNER_ROUTE, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit partner
 *
 * @returns
 */
async function edit({ id, ...params }: SavePartnerParams): Promise<TradePartner> {
  try {
    return await Http.put(`${PARTNER_ROUTE}/${id}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Delete partner
 *
 * @returns
 */
async function remove({ id }: DetailParams): Promise<void> {
  try {
    return await Http.delete(`${PARTNER_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}

/**
 * Merge two trade partners
 *
 * @returns
 */
async function merge(params: MergeTradePartnerDto): Promise<TradePartner> {
  try {
    return await Http.post(`${PARTNER_ROUTE}/merge`, params);
  } catch (error) {
    throw await handleError(error);
  }
}

export { create, edit, get, list, merge, remove };
