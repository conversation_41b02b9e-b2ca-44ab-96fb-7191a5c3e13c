import {
  EditTradePartnerDto,
  GetTradePartnersDto,
  GetTradePartnersResponseDto,
  MergeTradePartnerDto,
  TradePartner as TP
} from "nest-modules";
import { DetailParams, ListParams, PaginationResponse, WithAuditNames } from "utils";

export type { MergeTradePartnerDto };

export enum TradePartnerColumn {
  id = "id",
  partnerType = "partnerType",
  vendorCode = "vendorCode",
  name = "name",
  email = "email",
  phoneNumber = "phoneNumber",
  address = "address",
  city = "city",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  countryId = "countryId",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}
export enum PartnerType {
  VENDOR = "vendor",
  TRUCKER = "trucker",
  IMPORTER = "importer",
  SHIPPER = "shipper",
  AIR_CARRIER = "air-carrier",
  CUSTOMS_BROKER = "customs-broker",
  FORWARDER = "forwarder",
  OCEAN_CARRIER = "ocean-carrier",
  RAIL_COMPANY = "rail-company",
  TERMINAL = "terminal",
  WAREHOUSE = "warehouse",
  MANUFACTURER = "manufacturer"
}
export enum PartnerField {
  VENDOR = "vendor",
  TRUCKER = "trucker",
  IMPORTER = "importer",
  SHIPPER = "shipper",
  AIR_CARRIER = "air-carrier",
  CUSTOMS_BROKER = "customs-broker",
  FORWARDER = "forwarder",
  OCEAN_CARRIER = "ocean-carrier",
  RAIL_COMPANY = "rail-company",
  TERMINAL = "terminal",
  WAREHOUSE = "warehouse",
  // for shipment modal
  CARRIER = "carrier",
  MANUFACTURER = "manufacturer",
  CONSIGNEE = "consignee",
  PICKUP = "pickupLocation",
  // for commercial invoice modal
  EXPORTER = "exporter",
  PURCHASER = "purchaser",
  SHIP_TO = "shipTo"
}
export enum PaymentTerms {
  COD = "cod",
  SEMI_MONTHLY = "semi-monthly",
  MONTHLY = "monthly"
}
export type TradePartner = TP;

export type TradePartnerTable = TradePartner &
  WithAuditNames & {
    countryName?: string;
  };

export type GetPartnerList = ListParams<GetTradePartnersDto>;
export type GetPartnerListResponse = PaginationResponse & GetTradePartnersResponseDto;

export type SavePartnerParams = EditTradePartnerDto & DetailParams;

// Omit<
//   TradePartner,
//   | "id"
//   | "location"
//   | "expenseCategory"
//   | "expenseCode"
//   | "candataCustomerNumber"
//   | "opusCustomerCode"
//   | keyof Timestamp
// > & {
//   id?: number;
//   // locationId?: number | string;
// };
