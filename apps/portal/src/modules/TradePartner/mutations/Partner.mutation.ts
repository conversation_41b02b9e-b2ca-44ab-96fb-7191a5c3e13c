import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import PartnerService from "../services";
import type { SavePartnerParams } from "../Types.partner";

const useSavePartner = () =>
  useMutation({
    mutationFn: async (params: SavePartnerParams) => {
      if (params.id) {
        return await PartnerService.edit(params);
      }
      return await PartnerService.create(params);
    }
  });

const useDeletePartner = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await PartnerService.remove(params)
  });

export { useDeletePartner, useSavePartner };
