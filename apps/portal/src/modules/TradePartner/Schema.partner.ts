import { auditTableSchema, TableSchema } from "utils";
import { mixed, number, object, ObjectSchema, string } from "yup";
import { PartnerType, SavePartnerParams, TradePartnerTable } from "./Types.partner";

export const tradePartnerTableSchema: TableSchema<TradePartnerTable> = {
  name: {
    header: "Name",
    style: "max-w-80 whitespace-normal",
    visible: true
  },
  partnerType: {
    header: "Type",
    style: "uppercase",
    visible: true
  },
  email: {
    header: "Email",
    visible: true
  },
  phoneNumber: {
    header: "Phone",
    visible: true
  },
  address: {
    header: "Address",
    style: "max-w-80 whitespace-normal",
    visible: true
  },
  city: {
    header: "City",
    visible: true
  },
  countryName: {
    header: "Country",
    visible: true,
    sortKey: "countryId"
  },
  ...auditTableSchema
};

export const savePartnerSchema: ObjectSchema<SavePartnerParams> = object({
  id: number().optional(),
  name: string().required("Partner name is required"),
  partnerType: mixed<PartnerType>().required("Partner type is required"),
  email: string().email("Email is not valid").optional(),
  phoneNumber: string().optional(),
  address: string().optional().max(70, "Address is limited to 70 characters"),
  city: string().optional(),
  state: string().optional(),
  postalCode: string().optional(),
  countryId: number().optional(),
  vendorCode: string().optional(),
  eManifestCarrierCode: string().optional().length(4, "eManifest carrier code must be 4 characters")
});
