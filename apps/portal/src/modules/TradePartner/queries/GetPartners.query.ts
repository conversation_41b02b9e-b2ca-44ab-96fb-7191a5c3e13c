import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { flattenQueryResults, getAuditNames, infiniteQueryMapper, SelectOption } from "utils";
import PartnerService from "../services";
import type { GetPartnerList, GetPartnerListResponse, TradePartner } from "../Types.partner";

export const getPartnerDto = (partner: TradePartner) => ({
  ...partner,
  countryName: partner.country?.name || "",
  organizationName: partner.organization?.name || "",
  ...getAuditNames(partner),
  compute: partner.compute || null
});

export const getPartnersKey = "getPartners";

export const useGetPartners = <R = GetPartnerListResponse>(
  params?: GetPartnerList,
  select?: (data: GetPartnerListResponse) => R,
  refetchOnMount = false
) =>
  useQuery({
    queryKey: [getPartnersKey, params],
    queryFn: () => PartnerService.list(params),
    select,
    refetchOnMount
  });

export const useGetPartnerList = (params?: GetPartnerList) =>
  useGetPartners(
    params,
    (data: GetPartnerListResponse) => {
      const partners = data.partners.map(getPartnerDto);
      return { ...data, partners };
    },
    true
  );

export const useGetInfinitePartners = (params?: GetPartnerList, refetchOnMount = false) => {
  const result = useInfiniteQuery({
    queryKey: [getPartnersKey, params],
    queryFn: ({ pageParam }) => PartnerService.list({ ...params, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: infiniteQueryMapper,
    refetchOnMount,
    select: (data) => flattenQueryResults("partners", data.pages).map(getPartnerDto)
  });

  return result;
};

export const useGetPartnersOptions = () =>
  useGetPartners({ limit: 50 }, (data: GetPartnerListResponse): SelectOption[] =>
    data.partners.map((p) => ({ label: p.name, value: p.id }))
  );
