import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { BaseError } from "@/common/Types.common";
import { useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { Button, ColumnSelector, Pagination, Popup, Table } from "ui";

import { useStore } from "@/bootstrap/Store.bootstrap";
import { useInvalidateQuery } from "@/common/Utils.common";
import { useColumnVisibility } from "@/modules/Settings/Hooks.settings";
import { observer } from "mobx-react-lite";
import { useLocation, useNavigate } from "react-router-dom";
import { emptyStringToNull, SortOrder } from "utils";
import { LOCATION_TABLE_KEY } from "../../Constant.location";
import { useDeleteLocation, useSaveLocation } from "../../mutations";
import { getLocationsKey, useGetLocationList } from "../../queries/GetLocations.query";
import { locationTableSchema } from "../../Schema.location";
import {
  LocationColumn,
  type GetLocationList,
  type Location,
  type SaveLocationParams
} from "../../Types.location";
import AddLocationModal from "../AddLocationModal";
import LocationFilter from "../LocationFilter";

const LocationList = observer(() => {
  const { isAdmin, isSuperAdmin } = useStore().auth;
  const { state } = useLocation();
  const navigate = useNavigate();
  const { showPopup, hidePopup } = Popup.usePopup();

  const [modal, setModal] = useState(false);
  const [location, setLocation] = useState<Location>();
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState<GetLocationList>();

  const { visibleColumns, handleToggleColumn } = useColumnVisibility({
    defaultColumns: locationTableSchema,
    key: LOCATION_TABLE_KEY
  });

  const invalidateQuery = useInvalidateQuery(getLocationsKey);
  const saveLocation = useSaveLocation();
  const deleteLocation = useDeleteLocation();

  const { data, error, isFetching, refetch } = useGetLocationList({
    page,
    limit: DEFAULT_LIMIT,
    sortBy: LocationColumn.createDate,
    sortOrder: SortOrder.DESC,
    ...filters
  });

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  useEffect(() => {
    if (state?.from) {
      setModal(true);
    }
  }, [state?.from]);

  const handleSaveLocation = useCallback(
    async (params: SaveLocationParams) => {
      const formatParams: SaveLocationParams = {
        ...emptyStringToNull(params),
        countryId: params.countryId ? Number(params.countryId) : undefined
      };
      try {
        await saveLocation.mutateAsync(formatParams);
        invalidateQuery();

        toast.success(`Location ${formatParams.name} ${modal ? "added" : "saved"}`);

        setModal(false);
        setLocation(undefined);

        if (state?.from)
          navigate(state?.from, {
            replace: true
          });

        refetch();
      } catch (error) {
        toast.error((error as BaseError).message);
      }
    },
    [invalidateQuery, modal, navigate, refetch, saveLocation, state?.from]
  );

  const handleDeleteLocation = useCallback(async () => {
    try {
      await deleteLocation.mutateAsync({ id: location?.id });

      toast.success("Location deleted successfully");

      setModal(false);
      setLocation(undefined);
      refetch();
    } catch (error) {
      toast.error((error as BaseError).message);
    }
  }, [deleteLocation, location?.id, refetch]);

  const handleDelete = () => {
    showPopup(
      {
        content: "Are you sure you want to delete this location?",
        onProceed: () => {
          handleDeleteLocation();
          hidePopup();
        }
      },
      Popup.PopupType.DELETE_POPUP
    );
  };

  const handleFilter = useCallback((values: GetLocationList) => {
    setFilters(values);
    setPage(1);
  }, []);

  return (
    <div className="flex flex-col gap-3">
      <AddLocationModal
        show={modal}
        info={location}
        isAdmin={isAdmin}
        isLoading={saveLocation.isPending || deleteLocation.isPending}
        onClose={() => {
          setModal(false);
          setLocation(undefined);
        }}
        onSubmit={handleSaveLocation}
        onDelete={handleDelete}
      />

      <header className="flex gap-3 items-end">
        <LocationFilter filterValues={handleFilter} />
        <ColumnSelector
          columns={visibleColumns}
          onToggleColumn={handleToggleColumn}
          containerStyle="ml-auto"
        />
        {isSuperAdmin && (
          <Button
            label="New Location"
            kind="create"
            onClick={() => setModal(true)}
            disabled={saveLocation.isPending || deleteLocation.isPending}
          />
        )}
      </header>

      <main>
        <Table
          data={data?.locations}
          isLoading={isFetching}
          schema={visibleColumns}
          {...(isSuperAdmin && {
            onClick: (_data: Location) => {
              setLocation(_data);
              setModal(true);
            }
          })}
          onSort={(key?: string, direction?: SortOrder) => {
            setFilters((prev) => ({
              ...prev,
              sortBy: key as LocationColumn,
              sortOrder: direction
            }));
          }}
          sortKey={filters?.sortBy}
          sortDirection={filters?.sortOrder}
          footer={
            <Pagination currentPage={page} total={data?.total} limit={DEFAULT_LIMIT} onPageChange={setPage} />
          }
          total={data?.total}
        />
      </main>
    </div>
  );
});
export default LocationList;
