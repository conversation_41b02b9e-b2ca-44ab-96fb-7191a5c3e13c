import { parseAddress } from "@/common/Utils.common";
import { useFormik } from "formik";
import { useCallback, useEffect, useState } from "react";
import { AddressAutoComplete, Button, Input, Modal, Select, TimezoneSelect } from "ui";
import { formikInputOpts, getFormikError } from "utils";
import { LOCATION_TYPE } from "../../Constant.location";
import { saveLocationSchema } from "../../Schema.location";
import { Country, type Location, type SaveLocationParams } from "../../Types.location";
import SelectCountry from "../SelectCountry";
import { env } from "@/config/Environment.config";

type Props = {
  show: boolean;
  info?: Location;
  isLoading?: boolean;
  isAdmin?: boolean;
  onSubmit(partner: SaveLocationParams): void;
  onDelete(): void;
  onClose(): void;
};
function AddLocationModal({ show, info, isLoading, isAdmin, onSubmit, onDelete, onClose }: Props) {
  const [country, setCountry] = useState<Country>();

  const formik = useFormik({
    initialValues: {
      id: info?.id ?? undefined,
      locationType: info?.locationType || undefined,
      name: info?.name ?? "",
      streetAddress: info?.streetAddress ?? "",
      city: info?.city ?? "",
      state: info?.state ?? "",
      postalCode: info?.postalCode ?? "",
      countryId: info?.country?.id,
      timezone: info?.timezone ?? "America/Toronto"
    },
    validationSchema: saveLocationSchema,
    validateOnBlur: true,
    enableReinitialize: true,
    onSubmit
  });

  const handleAddress = useCallback(
    async (place: google.maps.places.PlaceResult) => {
      const address = await parseAddress(place);
      if (address) {
        formik.setFieldValue("streetAddress", address?.street);
        formik.setFieldValue("city", address?.city);
        formik.setFieldValue("state", address?.state);
        formik.setFieldValue("postalCode", address?.zipCode);
        formik.setFieldValue("countryId", address?.country?.id);
        setCountry(address.country);
      }
    },
    [formik]
  );

  useEffect(() => {
    setCountry(info?.country);
  }, [info?.country]);

  useEffect(() => {
    return () => setCountry(undefined);
  }, []);

  return (
    <Modal
      id="location-modal"
      show={show}
      onClose={() => {
        formik.resetForm();
        setCountry(undefined);
        onClose();
      }}
      title={`${info ? "Edit" : "Add New"} Location`}
      actions={
        <>
          {info && isAdmin && (
            <Button
              label="Delete"
              kind="delete"
              loading={isLoading}
              disabled={formik.dirty}
              onClick={onDelete}
            />
          )}
          <Button
            label={info ? "Update" : "Add"}
            kind="update"
            loading={isLoading}
            onClick={formik.handleSubmit}
          />
        </>
      }
    >
      <form className="flex flex-col gap-2 px-2">
        <Input label="Location Name" placeholder="Input location name" {...formikInputOpts(formik, "name")} />
        <Select
          label="Location Type"
          options={LOCATION_TYPE}
          {...formikInputOpts(formik, "locationType")}
          optional
        />
        <AddressAutoComplete
          apiKey={env.mapsApiKey}
          label="Street"
          placeholder="Input address"
          {...formikInputOpts(formik, "streetAddress")}
          onSelected={handleAddress}
        />
        <Input label="City" placeholder="Input City" {...formikInputOpts(formik, "city")} />
        <Input label="State" placeholder="Input State" {...formikInputOpts(formik, "state")} />
        <Input
          label="Postal Code"
          placeholder="Input Postal Code"
          {...formikInputOpts(formik, "postalCode")}
        />
        <SelectCountry
          label="Country"
          name="countryId"
          placeholder="Select country"
          value={country}
          onSelect={(value) => formik.setFieldValue("countryId", value?.value)}
          error={getFormikError(formik, "countryId")}
        />
        <TimezoneSelect
          label="Timezone"
          name="timezone"
          onSelected={({ value }) => {
            formik.setFieldValue("timezone", value);
          }}
          value={formik.values.timezone ?? ""}
          optional
        />
      </form>
    </Modal>
  );
}
export default AddLocationModal;
