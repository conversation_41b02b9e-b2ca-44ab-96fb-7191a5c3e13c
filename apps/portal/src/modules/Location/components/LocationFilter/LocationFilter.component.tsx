import { ORDER_BY } from "@/common/Constant.common";
import { SelectCountry } from "@/modules/Location/components";
import { useDebounce } from "@uidotdev/usehooks";
import { useEffect, useState } from "react";
import { Input, Select } from "ui";
import { OrderBy, SortOrder } from "utils";
import { LOCATION_SORT_BY, LOCATION_TYPE } from "../../Constant.location";
import { locationTableSchema } from "../../Schema.location";
import { GetLocationList, LocationColumn, LocationType } from "../../Types.location";

type Props = {
  type?: LocationType;
  name?: string;
  filterValues(values?: GetLocationList): void;
};
const LocationFilter = ({ type, name: n, filterValues }: Props) => {
  const [values, setValues] = useState<GetLocationList>();

  const [_name, setName] = useState<string | undefined>(n);
  const name = useDebounce(_name, 500);

  useEffect(() => {
    setValues((prev) => ({
      ...prev,
      name: name ?? undefined
    }));
  }, [name]);

  useEffect(() => {
    filterValues(values);
  }, [filterValues, values]);

  useEffect(() => () => setValues(undefined), []);

  return (
    <div className="flex flex-wrap items-center gap-x-3 gap-y-2">
      <div className="flex gap-3">
        <Input label="Name" placeholder="Search" onTextChange={setName} kind="search" value={_name ?? ""} />
        <Select
          label="Type"
          onSelected={(locationType: LocationType) => setValues((prev) => ({ ...prev, locationType }))}
          options={LOCATION_TYPE}
          defaultValue={type}
          optional
        />
        <SelectCountry
          placeholder="Select Country"
          onSelect={(country) => {
            if (typeof country?.value === "number" || country?.value === undefined) {
              setValues((prev) => ({
                ...prev,
                countryId: country?.value as number
              }));
            }
          }}
        />
      </div>

      <div className="flex gap-3">
        <Select
          label="Sort By"
          onSelected={(sortBy: keyof typeof locationTableSchema) =>
            setValues((prev) => ({ ...prev, sortBy }) as GetLocationList)
          }
          options={LOCATION_SORT_BY}
          defaultValue={LocationColumn.createDate}
          optional
        />
        <Select
          label="Sort Direction"
          onSelected={(sortOrder: OrderBy) =>
            setValues((prev) => ({ ...prev, sortOrder }) as GetLocationList)
          }
          options={ORDER_BY}
          defaultValue={SortOrder.DESC}
          optional
        />
      </div>
    </div>
  );
};
export default LocationFilter;
