import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { useDebounce } from "@uidotdev/usehooks";
import { InputHTMLAttributes, useEffect, useState } from "react";
import { AutoComplete } from "ui";
import { useGetInfiniteCountries } from "../../queries";
import { Country, CountryOption } from "../../Types.location";

type Props = Omit<InputHTMLAttributes<HTMLInputElement>, "value" | "onSelect"> & {
  value?: Country;
  label?: string;
  error?: string;
  containerStyle?: string;
  onSelect(value?: CountryOption): void;
  getCountryObject?(value?: Country): void;
};

function SelectCountry({
  value,
  onSelect,
  label = "Origin",
  error,
  containerStyle,
  getCountryObject,
  ...props
}: Props) {
  const [country, setCountry] = useState<Partial<Country>>();
  const [searchCountry, setSearchCountry] = useState<string>();
  const name = useDebounce(searchCountry, 300);

  const {
    data: countries,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage
  } = useGetInfiniteCountries({
    name: name || undefined,
    limit: DEFAULT_LIMIT
  });

  useEffect(() => {
    if (value)
      setCountry({
        ...value,
        name: `${value?.name} (${value?.alpha2})`
      });
  }, [value]);

  const handleCountrySelect = (option: CountryOption) => {
    setCountry({
      id: option.value,
      name: option.label,
      alpha2: option.alpha2
    } as Country);
    onSelect(option);
    getCountryObject?.({
      id: option.value,
      name: option.name,
      alpha2: option.alpha2
    } as Country);
  };

  const handleClear = () => {
    setCountry(undefined);
    setSearchCountry(undefined);
    onSelect(undefined);
  };

  return (
    <AutoComplete
      {...props}
      label={label}
      containerStyle={containerStyle}
      items={countries ?? []}
      value={
        country
          ? {
              label: country.name,
              value: country.id?.toString() ?? ""
            }
          : undefined
      }
      onSearch={setSearchCountry}
      onSelect={handleCountrySelect}
      onClear={handleClear}
      loading={isLoading || isFetchingNextPage}
      error={error}
      hasMore={hasNextPage}
      onLoadMore={fetchNextPage}
    />
  );
}

export default SelectCountry;
