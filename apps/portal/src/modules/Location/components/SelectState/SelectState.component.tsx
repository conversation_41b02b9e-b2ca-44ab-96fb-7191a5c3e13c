import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { useDebounce } from "@uidotdev/usehooks";
import { InputHTMLAttributes, useEffect, useState } from "react";
import { AutoComplete } from "ui";
import { SelectOption } from "utils";
import { useGetInfiniteStates } from "../../queries";
import { State } from "../../Types.location";

type Props = Omit<InputHTMLAttributes<HTMLInputElement>, "value"> & {
  countryId: number;
  value?: State;
  label?: string;
  error?: string;
  containerStyle?: string;
  onSelect(value?: number): void;
};
function SelectState({
  countryId,
  value,
  onSelect,
  label = "State",
  error,
  containerStyle,
  ...props
}: Props) {
  const [state, setState] = useState<Partial<State>>();
  const [searchState, setSearchState] = useState<string>();
  const name = useDebounce(searchState, 300);

  const {
    data: states,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage
  } = useGetInfiniteStates(
    {
      id: countryId,
      name: name || undefined,
      limit: DEFAULT_LIMIT
    },
    {
      enabled: !!countryId
    }
  );

  useEffect(() => {
    if (value)
      setState({
        ...value,
        name: `${value?.name} (${value?.alpha2})`
      });
  }, [value]);

  const handleSelect = (option: SelectOption) => {
    const newValue = { id: option.value, name: option.label };
    setState(newValue as State);
    onSelect(Number(option.value));
  };

  const handleClear = () => {
    setState(undefined);
    setSearchState(undefined);
    onSelect(undefined);
  };

  return (
    <AutoComplete
      {...props}
      label={label}
      containerStyle={containerStyle}
      items={states ?? []}
      value={
        state
          ? {
              label: state.name,
              value: state.id ?? ""
            }
          : undefined
      }
      onSearch={setSearchState}
      onSelect={handleSelect}
      onClear={handleClear}
      loading={isLoading || isFetchingNextPage}
      error={error}
      hasMore={hasNextPage}
      onLoadMore={fetchNextPage}
    />
  );
}

export default SelectState;
