import { DEFAULT_LIMIT } from "@/common/Constant.common";
import { useEffect, useState } from "react";
import { <PERSON>ton, Modal, Table } from "ui";
import { SortOrder } from "utils";
import { useGetInfiniteLocations } from "../../queries/GetLocations.query";
import { locationTableSchema } from "../../Schema.location";
import { GetLocationList, Location, LocationColumn } from "../../Types.location";
import LocationFilter from "../LocationFilter";

type Props = {
  show: boolean;
  location?: Location | null;
  required?: boolean;
  multiple?: boolean;
  onSelect(item?: Location): void;
  onClose(): void;
};
function SelectLocationModal({ show, location, required, multiple, onSelect, onClose }: Props) {
  const [selected, setSelected] = useState<Location>();
  const [filters, setFilters] = useState<GetLocationList>();

  const { data, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } = useGetInfiniteLocations({
    limit: DEFAULT_LIMIT,
    sortBy: LocationColumn.createDate,
    sortOrder: SortOrder.DESC,
    ...filters
  });

  useEffect(() => {
    if (location && data && data.length > 0) {
      const foundLocation = data.find((p) => p.id === location.id);
      if (foundLocation) setSelected(foundLocation);
    }
  }, [location, data]);

  const handleSelect = () => {
    if (!multiple) onSelect(selected ?? undefined);
  };

  return (
    <Modal
      id="select-location-modal"
      show={show}
      onClose={onClose}
      title="Select Location"
      size="4xl"
      bodyStyle="pt-0"
      actions={
        <>
          {/* <Button
            label="Create"
            kind="create"
            onClick={() => {
              navigate(`${SettingsPages.List}/${SettingsPages.Locations}`, {
                state: {
                  from: location.pathname
                }
              });
            }}
          /> */}
          {!required && (
            <Button
              label="Clear"
              kind="cancel"
              onClick={() => {
                setSelected(undefined);
                onSelect(undefined);
              }}
            />
          )}
          <Button label="Select" onClick={handleSelect} disabled={!data?.length || !selected} />
        </>
      }
    >
      <div>
        <div className="sticky top-0 z-10 bg-white py-4">
          <LocationFilter filterValues={setFilters} name={location?.name} />
        </div>
        <div className="overflow-auto flex-1">
          <Table
            data={data}
            schema={locationTableSchema}
            checklist
            selected={selected ? [selected] : undefined}
            onClick={setSelected}
            isLoading={isLoading}
            onEndReached={fetchNextPage}
            hasNextPage={hasNextPage}
            isFetchingNextPage={isFetchingNextPage}
            onSort={(key?: string, direction?: SortOrder) => {
              setFilters((prev) => ({
                ...prev,
                sortBy: key as LocationColumn,
                sortOrder: direction
              }));
            }}
            sortKey={filters?.sortBy}
            sortDirection={filters?.sortOrder}
          />
        </div>
      </div>
    </Modal>
  );
}
export default SelectLocationModal;
