import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { flattenQueryResults, infiniteQueryMapper, SelectOption } from "utils";
import type { GetStateList, GetStateListResponse } from "../Types.location";
import LocationService from "../services";

export const getStatesKey = "getStates";

export const useGetStates = <R = GetStateListResponse>(
  params: GetStateList,
  options?: {
    select?: (data: GetStateListResponse) => R;
    refetchOnMount?: boolean;
    enabled?: boolean;
  }
) =>
  useQuery({
    queryKey: [getStatesKey, params],
    queryFn: () => LocationService.Country.getStates(params),
    refetchOnMount: false,
    ...options
  });

export const useGetStatesOptions = (params: GetStateList, refetch = false) =>
  useGetStates(params, {
    select: (data: GetStateListResponse): SelectOption[] =>
      data.states.map((v) => ({ label: v.name, value: v.id })),
    refetchOnMount: refetch
  });

export const useSearchState = (params: GetStateList) =>
  useGetStates(params, {
    enabled: !!params?.name
  });

export const useGetInfiniteStates = (
  params?: GetStateList,
  options?: {
    refetchOnMount?: boolean;
    enabled?: boolean;
  }
) => {
  const result = useInfiniteQuery({
    queryKey: [getStatesKey, params],
    queryFn: ({ pageParam }) => LocationService.Country.getStates({ ...params, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: infiniteQueryMapper,
    refetchOnMount: false,
    ...options,
    select: (data) =>
      flattenQueryResults("states", data.pages).map((v) => ({
        label: `${v.name} (${v.alpha2})`,
        value: v.id
      }))
  });

  return result;
};
