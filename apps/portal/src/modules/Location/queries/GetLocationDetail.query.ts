import { useQuery } from "@tanstack/react-query";
import { DetailParams } from "utils";
import type { Location } from "../Types.location";
import LocationService from "../services";

export const getLocationDetailKey = "getLocationDetail";

export const useGetLocationDetail = <R = Location>({ id }: DetailParams, select?: (data: Location) => R) =>
  useQuery({
    queryKey: [getLocationDetailKey, { id }],
    queryFn: () => LocationService.get({ id }),
    enabled: !!id,
    select
  });
