import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { flattenQueryResults, getAuditNames, infiniteQueryMapper } from "utils";
import {
  LocationType,
  type GetLocationList,
  type GetLocationListResponse,
  type Location
} from "../Types.location";
import LocationService from "../services";

export const getLocationDto = (location: Location) => ({
  ...location,
  countryName: location.country?.name || "",
  ...getAuditNames(location)
});

export const getLocationsKey = "getLocations";

export const useGetLocations = <R = GetLocationListResponse>(
  params?: GetLocationList,
  select?: (data: GetLocationListResponse) => R,
  refetchOnMount = false
) =>
  useQuery({
    queryKey: [getLocationsKey, params],
    queryFn: () => LocationService.list(params),
    select,
    refetchOnMount
  });

export const useGetLocationList = (params?: GetLocationList) =>
  useGetLocations(
    params,
    (data: GetLocationListResponse) => {
      const locations = data.locations.map(getLocationDto);
      return { ...data, locations };
    },
    true
  );

export const useGetInfiniteLocations = (params?: GetLocationList, refetchOnMount = false) => {
  const result = useInfiniteQuery({
    queryKey: [getLocationsKey, params],
    queryFn: ({ pageParam }) => LocationService.list({ ...params, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: infiniteQueryMapper,
    refetchOnMount,
    select: (data) => flattenQueryResults("locations", data.pages).map(getLocationDto)
  });

  return result;
};

export const useGetPorts = (params?: GetLocationList) =>
  useGetLocations<GetLocationListResponse>({
    locationType: LocationType.OCEAN_AND_INLAND_PORT,
    limit: 100,
    ...params
  });
