import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { flattenQueryResults, infiniteQueryMapper, SelectOption } from "utils";
import type { GetCountries, GetCountriesResponse } from "../Types.location";
import LocationService from "../services";

export const getCountriesKey = "getCountries";

export const useGetCountries = <R = GetCountriesResponse>(
  params?: GetCountries,
  options?: {
    select?: (data: GetCountriesResponse) => R;
    refetchOnMount?: boolean;
    enabled?: boolean;
  }
) =>
  useQuery({
    queryKey: [getCountriesKey, params],
    queryFn: () => LocationService.Country.getCountries(params),
    refetchOnMount: false,
    ...options
  });

export const useGetCountriesOptions = (params?: GetCountries, refetch = false) =>
  useGetCountries(params, {
    select: (data: GetCountriesResponse): SelectOption[] =>
      data.countries.map((v) => ({ label: v.name, value: v.id })),
    refetchOnMount: refetch
  });

export const useSearchCountry = (params?: GetCountries) =>
  useGetCountries(params, {
    enabled: !!params?.name
  });

export const useGetInfiniteCountries = (
  params?: GetCountries,
  options?: {
    refetchOnMount?: boolean;
    enabled?: boolean;
  }
) => {
  const result = useInfiniteQuery({
    queryKey: [getCountriesKey, params],
    queryFn: ({ pageParam }) => LocationService.Country.getCountries({ ...params, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: infiniteQueryMapper,
    refetchOnMount: false,
    ...options,
    select: (data) =>
      flattenQueryResults("countries", data.pages).map((v) => ({
        label: `${v.name} (${v.alpha2})`,
        value: v.id,
        alpha2: v.alpha2,
        name: v.name
      }))
  });

  return result;
};
