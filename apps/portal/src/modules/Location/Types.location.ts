import {
  Country as Count,
  EditLocationDto,
  GetCountriesDto,
  GetCountriesResponseDto,
  GetLocationsDto,
  GetLocationsResponseDto,
  GetStatesDto,
  GetStatesResponseDto,
  Location as Loc,
  State as St
} from "nest-modules";
import { DetailParams, ListParams, SelectOption, WithAuditNames } from "utils";

export enum LocationColumn {
  id = "id",
  locationType = "locationType",
  name = "name",
  unit = "unit",
  streetAddress = "streetAddress",
  city = "city",
  state = "state",
  zipCode = "zipCode",
  timezone = "timezone",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  countryId = "countryId",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export enum LocationType {
  // PORT = 'port'
  OCEAN_PORT = "ocean-port",
  INLAND_PORT = "inland-port",
  OCEAN_AND_INLAND_PORT = "ocean-and-inland-port"
}

//#region Country
export type Country = Count;
export type GetCountries = ListParams<GetCountriesDto>;
export type GetCountriesResponse = GetCountriesResponseDto;

export type CountryOption = SelectOption & { alpha2?: string; name?: string };
//#endregion

//#region Location
export type Location = Loc;

export type LocationTable = Location &
  WithAuditNames & {
    countryName?: string;
  };
export type GetLocationList = ListParams<GetLocationsDto>;
export type GetLocationListResponse = GetLocationsResponseDto;

export type SaveLocationParams = EditLocationDto & DetailParams;
//#endregion

//#region State
export type State = St;

export type StateTable = State &
  WithAuditNames & {
    countryName?: string;
  };

export type GetStateList = ListParams<GetStatesDto> & DetailParams;
export type GetStateListResponse = GetStatesResponseDto;
//#endregion
