import { auditTableSchema, TableSchema } from "utils";
import { mixed, number, object, ObjectSchema, string } from "yup";
import type { LocationTable, LocationType, SaveLocationParams } from "./Types.location";

export const locationTableSchema: TableSchema<LocationTable> = {
  name: {
    header: "Name",
    visible: true
  },
  locationType: {
    header: "Type",
    renderer: "kebabCase",
    visible: true
  },
  streetAddress: {
    header: "Address",
    visible: true
  },
  city: {
    header: "City",
    visible: true
  },
  state: {
    header: "State",
    visible: true
  },
  postalCode: {
    header: "Postal Code",
    visible: true
  },
  countryName: {
    header: "Country",
    visible: true,
    sortKey: "countryId"
  },
  timezone: {
    header: "Timezone",
    visible: true
  },
  ...auditTableSchema
};

export const saveLocationSchema: ObjectSchema<SaveLocationParams> = object({
  id: number().optional(),
  name: string().required("Location name is required"),
  locationType: mixed<LocationType>().optional(),
  streetAddress: string().optional(),
  city: string().required("City is required"),
  state: string().optional(),
  zipCode: string().optional(),
  postalCode: string().optional(),
  countryId: number().required("Country is required"),
  timezone: string().optional()
});
