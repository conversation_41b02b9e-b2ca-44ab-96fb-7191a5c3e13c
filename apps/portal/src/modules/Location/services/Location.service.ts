import Http from "@/bootstrap/Http.bootstrap";
import { DetailParams, handleError, stringifyQueryParams } from "utils";
import type {
  GetLocationList,
  GetLocationListResponse,
  Location,
  SaveLocationParams
} from "../Types.location";

const LOCATION_ROUTE = "locations";

/**
 * Get all locations
 *
 * @returns
 */
async function list(params?: GetLocationList): Promise<GetLocationListResponse> {
  try {
    return await Http.get(stringifyQueryParams(LOCATION_ROUTE, params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get location detail
 *
 * @returns
 */
async function get({ id }: DetailParams): Promise<Location> {
  try {
    return await Http.get(`${LOCATION_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Create location
 *
 * @returns
 */
async function create(params: SaveLocationParams): Promise<Location> {
  try {
    return await Http.post(LOCATION_ROUTE, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Edit location
 *
 * @returns
 */
async function edit({ id, ...params }: SaveLocationParams): Promise<Location> {
  try {
    return await Http.put(`${LOCATION_ROUTE}/${id}`, params);
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Delete location
 *
 * @returns
 */
async function remove({ id }: DetailParams): Promise<void> {
  try {
    return await Http.delete(`${LOCATION_ROUTE}/${id}`);
  } catch (error) {
    throw await handleError(error);
  }
}

export { create, edit, get, list, remove };
