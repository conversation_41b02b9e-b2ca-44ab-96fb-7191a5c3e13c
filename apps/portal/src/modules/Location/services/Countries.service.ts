import Http from "@/bootstrap/Http.bootstrap";
import { handleError, stringifyQueryParams } from "utils";
import type {
  GetCountries,
  GetCountriesResponse,
  GetStateList,
  GetStateListResponse
} from "../Types.location";

/**
 * Get countries
 *
 * @returns
 */
async function getCountries(params?: GetCountries): Promise<GetCountriesResponse> {
  try {
    return await Http.get(stringifyQueryParams("countries", params));
  } catch (error) {
    throw await handleError(error);
  }
}
/**
 * Get states
 *
 * @returns
 */
async function getStates({ id, ...params }: GetStateList): Promise<GetStateListResponse> {
  try {
    return await Http.get(stringifyQueryParams(`countries/${id}/states`, params));
  } catch (error) {
    throw await handleError(error);
  }
}

export default { getCountries, getStates };
