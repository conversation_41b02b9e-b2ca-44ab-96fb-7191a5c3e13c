import { useMutation } from "@tanstack/react-query";
import { DetailParams } from "utils";
import LocationService from "../services";
import type { SaveLocationParams } from "../Types.location";

const useSaveLocation = () =>
  useMutation({
    mutationFn: async (params: SaveLocationParams) => {
      if (params.id) {
        return await LocationService.edit(params);
      }
      return await LocationService.create(params);
    }
  });

const useDeleteLocation = () =>
  useMutation({
    mutationFn: async (params: DetailParams) => await LocationService.remove(params)
  });

export { useDeleteLocation, useSaveLocation };
