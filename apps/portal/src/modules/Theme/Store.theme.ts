import { makeAutoObservable } from "mobx";
import { makePersistable } from "mobx-persist-store";

class ThemeStore {
  darkMode = false;
  // primary?: string;
  primary = "#002858";

  constructor() {
    makeAutoObservable(this);

    makePersistable(this, {
      name: "ClaroThemeStore",
      properties: ["darkMode"],
      storage: localStorage
    }).then(() => {
      // After the store is hydrated, apply the class based on the stored state
      this.applyDarkModeClass();
    });
  }

  toggleDarkMode = () => {
    this.darkMode = !this.darkMode;
    this.applyDarkModeClass();
  };

  applyDarkModeClass = () => {
    if (this.darkMode) {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  };

  changeTheme = (_primary: string) => {
    this.primary = _primary;
    document.documentElement.style.setProperty("--color-primary", _primary);

    // const color = { ...colors };
    // const shades = colors["red"];
    // console.log("shades", shades, color);
  };
}

export default ThemeStore;
