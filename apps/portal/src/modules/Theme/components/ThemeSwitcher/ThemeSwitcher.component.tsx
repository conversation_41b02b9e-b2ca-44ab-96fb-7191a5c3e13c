import { useStore } from "@/bootstrap/Store.bootstrap";
import { observer } from "mobx-react-lite";

const ThemeSwitcher = observer(() => {
  const { changeTheme } = useStore().theme;

  const colors = ["#404040", "#c2410c", "#4d7c0f", "#0f766e", "#1d4ed8", "#7e22ce", "#be123c", "#3490dc"];

  const onChange = () => {
    const random = Math.floor(Math.random() * colors.length);
    changeTheme(colors[random]);
  };
  return (
    <button onClick={onChange} className="text-sm inline-flex gap-3 items-center">
      <div className="size-4 bg-primary border" /> <span>Change Color</span>
    </button>
  );
});
export default ThemeSwitcher;
