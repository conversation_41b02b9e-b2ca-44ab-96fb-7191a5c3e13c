{"name": "portal", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "start": "vite --force"}, "dependencies": {"@microsoft/fetch-event-source": "~2.0.1", "@tanstack/react-query": "~5.59.0", "@uidotdev/usehooks": "~2.4.1", "dompurify": "~3.2.4", "firebase": "~11.4.0", "formik": "~2.4.6", "lucide-react": "~0.436.0", "mobx-persist-store": "~1.1.5", "mobx-react-lite": "~4.0.7", "mobx": "~6.13.1", "nest-modules": "workspace:^", "react-data-grid": "7.0.0-beta.47", "react-dom": "^18.3.1", "react-error-boundary": "~5.0.0", "react-hot-toast": "~2.4.1", "react-phone-number-input": "~3.4.12", "react-player": "~2.16.0", "react-router-dom": "~6.26.1", "react": "^18.3.1", "tailwind-merge": "~2.5.3", "ui": "workspace:^", "utils": "workspace:^", "xlsx": "~0.18.5", "yup": "~1.4.0", "react-markdown": "~10.1.0", "rehype-raw": "~7.0.0", "remark-gfm": "~4.0.1", "@dnd-kit/core": "~6.3.1", "@dnd-kit/sortable": "~10.0.0", "@dnd-kit/utilities": "~3.2.2", "flag-icons": "~7.5.0"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/forms": "~0.5.8", "@types/google.maps": "~3.58.0", "@types/react-dom": "^18.3.0", "@types/react": "^18.3.3", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "~10.4.20", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "eslint": "^9.9.0", "globals": "^15.9.0", "ky": "~1.7.2", "postcss": "~8.4.41", "tailwind": "workspace:^", "tailwindcss": "~3.4.10", "typescript-eslint": "^8.0.1", "typescript": "~5.5.3", "vite": "^5.4.1", "@tailwindcss/typography": "~0.5.16"}}