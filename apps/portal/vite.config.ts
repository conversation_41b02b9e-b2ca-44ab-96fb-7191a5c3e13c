import react from "@vitejs/plugin-react-swc";
import path from "path";
import { defineConfig } from "vite";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src")
    }
  },
  // optimizeDeps: {
  //   exclude: ["react-select"],
  // },
  build: {
    rollupOptions: {
      // external: ["@slam/domains"],
      output: {
        manualChunks: {
          // React core libraries
          "react-core": ["react", "react-dom", "react-router-dom"],

          // State management
          state: ["mobx", "mobx-persist-store", "mobx-react-lite"],

          // Form handling and validation
          forms: [
            "formik",
            "yup",
            "@uidotdev/usehooks",
            "@tanstack/react-query",
            "@microsoft/fetch-event-source",
            "dompurify"
          ],

          firebase: [
            // "firebase",
            "firebase/app",
            "firebase/auth",
            "firebase/firestore",
            // "firebase/storage",
            "firebase/analytics",
            "firebase/functions"
            // "firebase/messaging",
            // "firebase/performance",
            // "firebase/remote-config"
          ],

          // Core UI utilities
          "ui-core": [
            "lucide-react",
            "react-hot-toast",
            "tailwind-merge",
            "ui" // workspace package
          ],

          "ui-libs": ["react-phone-number-input", "react-player", "react-error-boundary"],

          // Data handling and business logic
          "data-utils": [
            "react-data-grid",
            "xlsx",
            "utils", // workspace package
            "nest-modules" // workspace package
          ]
        }
      }
    },
    sourcemap: process.env.NODE_ENV === "development" ? "inline" : false,
    emptyOutDir: true
    //     commonjsOptions: {
    //       esmExternals: true,
    //     },
  },
  server: {
    port: 5173,
    fs: {
      allow: [
        // Allow serving files from the workspace root
        path.resolve(__dirname, "../.."),
        // Allow serving files from pnpm store
        path.resolve(__dirname, "../../common/temp/node_modules")
      ]
    }
  }
});
