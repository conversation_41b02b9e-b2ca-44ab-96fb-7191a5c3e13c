# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Reference

### 🎯 Always Use These Sub-Agents First

| Task Type | Primary Agent | Use For |
|-----------|--------------|---------|
| **Email/AI/Template Questions** | `claro-docs-expert` | Consult BEFORE any code analysis |
| **File Analysis** | `code-file-analyzer` | Understanding specific code files (creates `<path>-doc.md`) |
| **Directory Analysis** | `folder-structure-analyzer` | Exploring folder structures (creates `<path>-folder.md`) |
| **Business Logic** | `business-purpose-analyzer` | Explaining domain concepts |
| **External APIs/Libraries** | `docs-lookup-agent` | **CRITICAL**: Use BEFORE writing any code with external APIs (OpenAI, Stripe, etc.) |
| **Documentation Creation** | `claro-docs-generator` | Creating new documentation |
| **Complex Research** | `general-purpose` | Multi-step investigations |

### ⚠️ Critical: External API/Library Usage

**ALWAYS use `docs-lookup-agent` BEFORE writing code that uses:**
- OpenAI SDK, Anthropic SDK, or any LLM APIs
- Payment processors (Stripe, PayPal, etc.)
- Cloud services (AWS, Google Cloud, Firebase)
- Email services (SendGrid, Gmail API)
- Any third-party library or external API

**How to use effectively:**
```
Task(
  description="Research OpenAI function calling",
  prompt="Show me the exact code for OpenAI SDK v4 function calling with streaming, including error handling and typescript types",
  subagent_type="docs-lookup-agent"
)
```

The agent will research and deliver exact code examples, API signatures, best practices, and implementation patterns. Be specific about versions and features needed.

### 📂 Code Analysis Documentation System

**Important**: Both analyzers save to `.ai-reference/code-file-analysis/` with mirrored paths:

**Save Patterns**:
- **Files**: `/.ai-reference/code-file-analysis/<mirrored-path>/<filename>-doc.md`
- **Folders**: `/.ai-reference/code-file-analysis/<mirrored-path>/<folder-name>-folder.md`

**Examples**:
```
# File Analysis
File: /apps/portal-api/src/services/auth.ts
Doc:  /.ai-reference/code-file-analysis/apps/portal-api/src/services/auth.ts-doc.md

# Folder Analysis  
Folder: /apps/portal-api/src/services/
Doc:    /.ai-reference/code-file-analysis/apps/portal-api/src/services-folder.md
```

**Two Key Implications**:

1. **Before Analysis**: Check if recent documentation already exists at the mirrored path
   - Look for `-doc.md` files for code files
   - Look for `-folder.md` files for directories
   
2. **After Major Changes**: Dispatch analyzers to update affected documentation

```bash
# Example: After modifying authentication service
Task(
  description="Update auth service documentation",
  prompt="Analyze and document the updated authentication service",
  subagent_type="code-file-analyzer",
  file_path="/apps/portal-api/src/services/auth.ts"
)

# Example: After restructuring services folder
Task(
  description="Update services folder structure documentation",
  prompt="Analyze the restructured services directory",
  subagent_type="folder-structure-analyzer",
  folder_path="/apps/portal-api/src/services"
)
```

### 📚 Core Documentation

All documentation is in `.ai-reference/`:
- `email-processing-docs/` - Email & AI system docs
- `agent-context-docs/` - Business logic docs
- `core-agent-docs/` - Core agent system docs
- `technical-analysis/` - Architecture guides
- `code-file-analysis/` - Mirrored file (`-doc.md`) and folder (`-folder.md`) analysis

**Key Files:**
1. `ClaroEmailProcessingSystem-doc.md` - System overview
2. `HandleRequestMessageProcessor-doc.md` - Email pipeline
3. `CoreAgentSystemComponents-doc.md` - AI services
4. `EmailModuleSystem-doc.md` - Gmail integration

**When to Use Docs vs Code:**
- **Use Docs**: Understanding architecture, workflows, patterns, dependencies
- **Use Code**: Bug fixes, runtime debugging, implementing features

**Using claro-docs-expert:**
```bash
# For any email/AI/template questions
Task(
  description="Understand email processing",
  prompt="How does intent refinement work in HandleRequestMessageProcessor?",
  subagent_type="claro-docs-expert"
)
```

## Development Commands

```bash
# Setup & Build
rush update              # Install dependencies
rush build              # Build all projects
rush services           # Start Redis, Postgres, etc.

# Development
rush dev                # All services
rush fe                 # Frontend only
rush start -t portal    # Specific project

# Testing (NEVER use Jest directly)
cd apps/portal-api && ./src/core-agent/testing/run-e2e-with-logs.sh

# Database
# Generate migration
cd apps/portal-api && npx typeorm migration:generate src/migrations/MigrationName -d src/data-source.ts

# Run migrations (use explicit env vars)
DB_HOST=localhost DB_PORT=5432 DB_NAME=claro_dev DB_USER=postgres DB_PASSWORD=superuser NODE_ENV=local npx typeorm migration:run -d libraries/nest-modules/dist/data-source.js

# Query database (never use psql)
cd apps/portal-api && node src/core-agent/testing/db-query.js orgs
```

## Project Structure

### 🏗️ Monorepo Overview

```
/home/<USER>/dev/Claro/
├── apps/                        # Applications (frontend/backend pairs)
├── libraries/                   # Shared code libraries
├── tools/                       # Development & infrastructure tools
├── services/                    # Infrastructure services (Docker)
├── common/                      # Rush build system configuration
├── docs/                        # Technical documentation
├── .ai-reference/              # AI-generated documentation
└── [Config files]              # rush.json, package.json, etc.
```

### 📱 Applications (`apps/`)

#### **Portal System** (Customer-facing)
- **`portal/`** - React frontend for customers
- **`portal-api/`** - NestJS backend API with comprehensive modules:
  - `core-agent/` - AI email processing system
  - `clean-agent-context/` - Context cleaning utilities
  - `email/` - Gmail integration & email processing
  - `llm/` - OpenAI/DeepSeek LLM services
  - `document/` - Document management & queues
  - `aggregation/` - Data aggregation workflows
  - `shipment/`, `product/`, `importer/` - Business entities
  - `ogd-filing/`, `sima-filing/` - Filing automation
  - `commercial-invoice/` - Invoice processing

#### **Backoffice System** (Admin-facing)
- **`backoffice/`** - React admin frontend with modules:
  - `Auth/`, `Compliance/`, `Document/`, `Importer/`, `Organization/`
- **`backoffice-api/`** - Admin backend with:
  - `docusign/`, `importer/`, `oauth/`, `trade-partner/`

#### **Supporting Applications**
- **`bullmq-board/`** - Queue monitoring dashboard
- **`cloud-functions/`** - Firebase cloud functions
- **`lambda-functions/`** - AWS Lambda functions:
  - `parse-xlsx/` - Excel document parsing
  - `pdf-to-png/` - PDF conversion service

### 📚 Libraries (`libraries/`)

#### **Nest Modules** (`libraries/nest-modules/`)
*Comprehensive shared NestJS backend code*

**Key Structure:**
```
src/
├── entities/                    # 50+ TypeORM database entities
├── migrations/                  # Database schema migrations
├── dto/                        # Data transfer objects
├── canada-*, us-tariff/        # Customs system modules
├── auth/, user/                # Authentication & user management
├── global-search/              # Search functionality
├── template-manager/           # Template system
├── guards/, decorators/        # Security & validation
└── types/                      # TypeScript definitions
```

#### **UI Library** (`libraries/ui/`)
*Shared React component library*

**Components:**
- **Form**: Input, Select, Checkbox, AutoComplete, FileUploader
- **Layout**: Header, Modal, Table, Tabs, Accordion
- **UI Elements**: Button, Icons, StatusLabel, Tooltip

### 🔧 Tools (`tools/`)

- **`infrastructure/`** - AWS CDK infrastructure as code
- **`utils/`** - Shared utility functions across projects
- **`tailwind/`** - Tailwind CSS configuration

### 🐳 Services (`services/`)

**Development Infrastructure:**
- `docker-compose.dev.yml` - Development services orchestration
- `redis/` - Redis caching configuration
- `minio/` - MinIO object storage setup
- `tika/` - Apache Tika document processing service

### ⚙️ Configuration & Build (`common/`)

**Rush Monorepo Management:**
- `config/rush/` - Rush.js configuration
  - `command-line.json` - Custom rush commands
  - `pnpm-config.json` - Package manager settings
- `docker/` - Dockerfiles for applications
- `scripts/` - Build and deployment scripts
- `git-hooks/` - Git hooks for code quality
- `temp/` - Build artifacts and package cache

### 📖 Documentation Structure

#### **AI Documentation** (`.ai-reference/`)
- `email-processing-docs/` - Email & AI system documentation
- `agent-context-docs/` - Business logic documentation
- `core-agent-docs/` - Core agent system documentation
- `technical-analysis/` - Architecture analysis
- `code-file-analysis/` - Auto-generated file analysis (mirrored paths)

#### **Technical Documentation** (`docs/`)
- `dependencies/` - Dependency analysis and documentation

### 🎯 Key Navigation Patterns

#### **Email Processing Pipeline:**
```
apps/portal-api/src/
├── email/                      # Gmail integration
├── core-agent/                 # AI processing
│   ├── processors/             # Message processors
│   ├── handlers/               # Intent handlers
│   ├── services/               # Core services
│   └── templates/              # Response templates
└── llm/                        # LLM integration
```

#### **Document Processing Flow:**
```
apps/portal-api/src/
├── document/                   # Document management
├── aggregation/                # Data aggregation
├── commercial-invoice/         # Invoice processing
└── [entity-modules]/           # Business logic
```

#### **Filing Systems:**
```
apps/portal-api/src/
├── ogd-filing/                 # OGD filing automation
└── sima-filing/                # SIMA filing automation
```

#### **Testing Infrastructure:**
```
apps/portal-api/src/core-agent/testing/
├── run-e2e-with-logs.sh       # E2E test runner
├── db-query.js                # Database query utility
└── [test-utilities]           # Testing helpers
```

## Efficiency Guidelines

### Parallel Sub-Agent Usage

**Always parallelize when possible:**
- Independent information gathering tasks
- Multi-component analysis
- Cross-service validation
- Performance testing across services

**Example Pattern:**
```
Task 1: Analyze component A
Task 2: Validate dependencies
Task 3: Check performance metrics
Task 4: Review security implications
→ Consolidate all results
```

### Common Parallel Workflows

1. **Feature Impact Analysis**
   - Analyze each affected service in parallel
   - Check dependencies and contracts
   - Validate database impacts
   - Test queue processing

2. **Migration Validation**
   - Validate syntax and dependencies
   - Check data impact
   - Verify rollback procedures
   - Test in multiple environments

3. **Performance Audit**
   - Monitor queue metrics
   - Analyze database queries
   - Check cache hit rates
   - Measure API response times

4. **Document Processing Analysis**
   - Validate handlers across document types
   - Test AI agent responses
   - Analyze processing pipelines
   - Check aggregation workflows

5. **Security & Code Quality**
   - Scan security across layers
   - Analyze code patterns
   - Check dependency vulnerabilities
   - Identify performance bottlenecks

## Key Guidelines

1. **Documentation First**: Always check `.ai-reference/` docs before diving into code
2. **Check Existing Analysis**: Look for `-doc.md` (files) or `-folder.md` (folders) at mirrored paths
3. **Update After Changes**: Dispatch analyzers to update docs after major code modifications
4. **Parallel by Default**: Use multiple sub-agents for complex tasks
5. **Never Direct SQL**: Use provided database query tools
6. **No Jest**: Use core-agent testing scripts
7. **Rush Only**: Never use npm install directly

## Documentation Creation Guidelines

When creating new documentation:

1. **Save to `.ai-reference/`** in appropriate subfolders:
   - `email-processing-docs/` - Email, AI, LLM, intent handling
   - `agent-context-docs/` - Business logic, compliance
   - `core-agent-docs/` - Core modules, processors
   - `technical-analysis/` - Architecture analysis, guides
   - Create new subfolders for distinct systems

2. **Naming Conventions**:
   - Main docs: `SystemName-doc.md`
   - Analysis: `Feature-analysis.md`
   - Guides: `Implementation-guide.md`

3. **Include**: Clear title, TOC for long docs, cross-references, code locations

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                 Claro Customs Automation                      │
├─────────────────────────────────────────────────────────────┤
│  Gmail API → Email Module → Core Agent System → LLM Services │
│      ↓            ↓              ↓                    ↓      │
│  OAuth Tokens  BullMQ Queues  Event System   Fragment Templates│
└─────────────────────────────────────────────────────────────┘
```

## Migration Troubleshooting

- Migrations location: `/home/<USER>/dev/Claro/libraries/nest-modules/src/migrations`
- "SASL: client password must be a string" = missing env vars, not bad credentials
- Always use explicit env vars when running migrations
- Check constraint names with `\d table_name` before writing migrations