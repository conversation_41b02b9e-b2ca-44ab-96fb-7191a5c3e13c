# Context Services Analysis: Scoping and Dependencies

## Executive Summary

The context building system in Claro uses a complex mix of SINGLETON and REQUEST-scoped services. The duplication between `agent-context` and `core-agent` exists partly due to **scoping constraints** - SINGLETON services cannot directly inject REQUEST-scoped services, requiring dynamic resolution through ModuleRef.

## Service Dependency Map

### Core Context Building Services

| Service | Module | Scope | Purpose | Key Dependencies |
|---------|--------|-------|---------|------------------|
| **ShipmentContextService** | agent-context | SINGLETON | Orchestrates context building | Interfaces only (DI tokens) |
| **ShipmentServicesAdapter** | agent-context | SINGLETON | Implements all interfaces | ModuleRef, DataSource |
| **BaseIntentHandler** | core-agent | SINGLETON | Base handler with context methods | TemplateManagerService |
| **Intent Handlers (11)** | core-agent | SINGLETON | Process specific intents | Various REQUEST services |

### Data Provider Services (REQUEST-scoped)

| Service | Module | Scope | Purpose | Why REQUEST? |
|---------|--------|-------|---------|--------------|
| **ShipmentService** | shipment | REQUEST | Shipment CRUD operations | Needs organization context |
| **EmailService** | email | REQUEST | Email operations | Organization-specific emails |
| **ImporterService** | importer | REQUEST | Importer management | Organization data isolation |
| **CommercialInvoiceService** | commercial-invoice | REQUEST | Invoice operations | Organization-specific data |
| **EntrySubmissionService** | shipment | REQUEST | Customs submission | Organization credentials |
| **RnsProofService** | email | REQUEST | RNS proof retrieval | Organization-specific |

### Supporting Services (SINGLETON)

| Service | Module | Scope | Purpose | Can be SINGLETON? |
|---------|--------|-------|---------|-------------------|
| **ComplianceValidationService** | shipment | SINGLETON | Validation logic | Yes - pure business logic |
| **CustomStatusService** | shipment | SINGLETON | Status processing | Yes - stateless operations |
| **RNSStatusChangeEmailSender** | shipment | SINGLETON | Email notifications | Yes - uses templates |
| **TemplateManagerService** | template-manager | SINGLETON | Template rendering | Yes - stateless |
| **ShipmentBusinessRulesService** | nest-modules | SINGLETON | Business rules | Yes - pure functions |
| **ShipmentFormatterService** | nest-modules | SINGLETON | Formatting logic | Yes - pure functions |

## The Scoping Problem

### 1. **Why REQUEST Scope is Required**

```typescript
@Injectable({ scope: Scope.REQUEST })
export class ShipmentService {
  constructor(
    @Inject(REQUEST) private request: RequestWithUser,
    // Uses request.user.organizationId for data filtering
  ) {}
}
```

REQUEST-scoped services need:
- Organization context for multi-tenancy
- User permissions for authorization
- Audit trail information

### 2. **The Injection Problem**

SINGLETON services **cannot** directly inject REQUEST-scoped services:

```typescript
// ❌ This would fail
@Injectable() // SINGLETON by default
export class SomeHandler {
  constructor(
    private shipmentService: ShipmentService // REQUEST-scoped
  ) {}
}
```

### 3. **Current Solution: Dynamic Resolution**

```typescript
// ✅ Current approach in ShipmentServicesAdapter
private async resolveRequestScopedServices(organization: Organization) {
  const contextId = ContextIdFactory.create();
  const request = generateRequest(null, organization);
  this.moduleRef.registerRequestByContextId(request, contextId);
  
  const shipmentService = await this.moduleRef.resolve(
    ShipmentService, 
    contextId, 
    { strict: false }
  );
  // ... resolve other services
}
```

## Context Building Flow

### Current Architecture (Duplicated)

```mermaid
graph TD
    A[Raw Data] --> B[agent-context/ShipmentContextService]
    B --> C[Initial Context]
    C --> D[core-agent/BaseIntentHandler]
    D --> E[Enhanced Context]
    E --> F[Intent Handlers]
    F --> G[Templates]
    
    H[REQUEST Services] -.-> B
    H -.-> D
    H -.-> F
```

### Why Consolidation is Challenging

1. **Service Resolution Complexity**
   - agent-context builds context early in the pipeline
   - core-agent handlers need REQUEST services for operations
   - Each handler may need different REQUEST services

2. **Context Enhancement Requirements**
   - Base context from agent-context is generic
   - Each intent handler needs specific context enhancements
   - Template-specific formatting happens late in the pipeline

3. **Circular Dependency Risk**
   ```
   agent-context → needs all handler logic → imports core-agent
   core-agent → needs context → imports agent-context
   ❌ Circular dependency!
   ```

## Complete Context Requirements

### Data Required for Templates

| Context Section | Source Service | Scope | When Needed |
|-----------------|----------------|-------|-------------|
| **Shipment Data** | ShipmentService | REQUEST | Always |
| **Compliance Status** | ComplianceValidationService | SINGLETON | Always |
| **Organization Settings** | Organization entity | Via REQUEST | Always |
| **Document Status** | Document relations | Via shipment | Always |
| **CAD Availability** | RNSStatusChangeEmailSender | SINGLETON | CAD requests |
| **RNS Proof Data** | RnsProofService | REQUEST | RNS requests |
| **Email Thread Info** | EmailService | REQUEST | Email responses |
| **Submission Status** | EntrySubmissionService | REQUEST | Rush/submit |
| **Validation Issues** | Multiple sources | Mixed | Status queries |
| **Smart Template Data** | Computed in handlers | N/A | Template rendering |

### Side Effects Management

| Side Effect | Required Service | Scope | Handler |
|-------------|------------------|-------|---------|
| **CAD Generation** | RNSStatusChangeEmailSender | SINGLETON | CAD/Process handlers |
| **Email Sending** | EmailService | REQUEST | All handlers |
| **Backoffice Alerts** | EmailService | REQUEST | Rush/Hold/Manual |
| **Shipment Updates** | ShipmentService | REQUEST | Update handler |
| **Entry Submission** | EntrySubmissionService | REQUEST | Rush/Process |

## Architectural Constraints

### 1. **Cannot Move All Logic to agent-context**
- Would create massive module with too many responsibilities
- Would need to import all handler-specific logic
- Violates single responsibility principle

### 2. **Cannot Make Everything SINGLETON**
- Loses multi-tenancy isolation
- Security risk with cross-organization data access
- Breaks existing authorization patterns

### 3. **Cannot Make Everything REQUEST-scoped**
- Performance impact from recreating services
- Complex dependency injection chains
- Loss of caching opportunities

## Recommendations

### 1. **Keep Current Architecture But Optimize**

The duplication exists for valid reasons:
- **agent-context**: Provides base context with core data
- **core-agent**: Enhances context for specific email templates

### 2. **Reduce Duplication Through Shared Services**

Already implemented:
- ✅ ShipmentBusinessRulesService (SINGLETON)
- ✅ ShipmentFormatterService (SINGLETON)
- ✅ Shared constants

### 3. **Optimize Service Resolution**

Create a **ContextServiceResolver** that:
- Caches resolved REQUEST services per request
- Provides typed access to all needed services
- Reduces boilerplate in handlers

```typescript
// Proposed pattern
const services = await this.contextServiceResolver.resolveAll(contextId);
// Returns typed object with all REQUEST services
```

### 4. **Document the Architecture**

The split between agent-context and core-agent is intentional:
- **agent-context**: Domain-agnostic context building
- **core-agent**: Email-specific context enhancement

## Conclusion

The duplication between agent-context and core-agent is **partially necessary** due to:

1. **Scoping constraints**: SINGLETON handlers need REQUEST services
2. **Module boundaries**: Avoiding circular dependencies
3. **Separation of concerns**: Generic vs. email-specific context
4. **Performance**: Caching and optimization opportunities

While complete consolidation isn't feasible, we can:
- ✅ Extract shared logic (done)
- 🔄 Optimize service resolution patterns
- 📚 Document the architecture clearly
- 🎯 Focus on reducing only unnecessary duplication

The current architecture is a reasonable trade-off between:
- Clean module separation
- Multi-tenancy support
- Performance optimization
- Maintainability