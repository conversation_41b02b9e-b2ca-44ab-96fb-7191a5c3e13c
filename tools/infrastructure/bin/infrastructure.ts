#!/usr/bin/env node
import * as cdk from "aws-cdk-lib";
import "source-map-support/register";
import { InfrastructureStack } from "../src/infrastructure-stack";

const app = new cdk.App();
const envName = process.env.ENVIRONMENT || "staging";

// Create environment-specific stack name
const stackName = envName === "staging" 
  ? "ClaroInfrastructureStack" 
  : `ClaroInfrastructureStack-${envName}`;
new InfrastructureStack(app, stackName, {
  /* If you don't specify 'env', this stack will be environment-agnostic.
   * Account/Region-dependent features and context lookups will not work,
   * but a single synthesized template can be deployed anywhere. */
  /* Uncomment the next line to specialize this stack for the AWS Account
   * and Region that are implied by the current CLI configuration. */
  // env: { account: process.env.CDK_DEFAULT_ACCOUNT, region: process.env.CDK_DEFAULT_REGION },
  env: { account: process.env.CDK_DEFAULT_ACCOUNT, region: "ca-central-1" }
  /* Uncomment the next line if you know exactly what Account and Region you
   * want to deploy the stack to. */
  // env: { account: '************', region: 'us-east-1' },
  /* For more information, see https://docs.aws.amazon.com/cdk/latest/guide/environments.html */
});


