# Welcome to your CDK TypeScript project

This is a blank project for CDK development with TypeScript.

The `cdk.json` file tells the CDK Toolkit how to execute your app.

## Pre-requisites

### Install AWS CLI

[](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html)

### Configure Profile

[](https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.html)

### Login on AWS SSO

`aws sso login`

### Install AWS CDK CLI

`npm i -g aws-cdk`

## To deploy the stack

- `cdk synth` -> Emits the synthesized CloudFormation template (test if the config is ok)
- `cdk bootstrap` -> Bootstrap the environment (only on first deploy)
- `cdk deploy` -> Deploy the stack

## Useful commands

- `npm run build` compile typescript to js
- `npm run watch` watch for changes and compile
- `npm run test` perform the jest unit tests
- `npx cdk deploy` deploy this stack to your default AWS account/region
- `npx cdk diff` compare deployed stack with current state
- `npx cdk synth` emits the synthesized CloudFormation template
