{"name": "infrastructure", "version": "0.1.0", "bin": {"infrastructure": "bin/infrastructure.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk"}, "dependencies": {"aws-cdk-lib": "2.206.0", "constructs": "^10.0.0", "source-map-support": "^0.5.21", "dotenv": "~16.4.5"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "22.5.4", "jest": "^29.7.0", "ts-jest": "^29.2.5", "aws-cdk": "2.1021.0", "ts-node": "^10.9.2", "typescript": "~5.6.2"}}