import { Duration, RemovalPolicy, Stack, StackProps } from "aws-cdk-lib";
import { AutoScalingGroup, SpotAllocationStrategy } from "aws-cdk-lib/aws-autoscaling";
import * as acm from "aws-cdk-lib/aws-certificatemanager";
import {
  AllowedMethods,
  CachePolicy,
  Distribution,
  PriceClass,
  S3OriginAccessControl,
  ViewerProtocolPolicy
} from "aws-cdk-lib/aws-cloudfront";
import { S3BucketOrigin } from "aws-cdk-lib/aws-cloudfront-origins";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import * as ecr_assets from "aws-cdk-lib/aws-ecr-assets";
import * as ecs from "aws-cdk-lib/aws-ecs";
import * as elasticache from "aws-cdk-lib/aws-elasticache";
import * as elbv2 from "aws-cdk-lib/aws-elasticloadbalancingv2";
import * as iam from "aws-cdk-lib/aws-iam";
import * as lambda from "aws-cdk-lib/aws-lambda";
import * as rds from "aws-cdk-lib/aws-rds";
import * as s3 from "aws-cdk-lib/aws-s3";
import * as secretsmanager from "aws-cdk-lib/aws-secretsmanager";
import * as ssm from "aws-cdk-lib/aws-ssm";
import * as wafv2 from "aws-cdk-lib/aws-wafv2";
import { Construct } from "constructs";

import { config } from "dotenv";

config(); // Load environment variables from .env file

//#region Environment config
interface EnvironmentConfig {
  envName: string;
  domainName: string;
  instanceSize: {
    portal: {
      instanceType: ec2.InstanceType;
      memoryLimit: number;
      memoryReservation: number;
    };
    backoffice: { instanceType: ec2.InstanceType; memoryLimit: number; memoryReservation: number };
    database: ec2.InstanceType;
    redis: string;
  };
  scaling: {
    portal: { min: number; max: number };
    backoffice: { min: number; max: number };
  };
  rds: {
    allocatedStorage: number;
    maxAllocatedStorage: number;
    backupRetention: number;
    multiAz: boolean;
    deletionProtection: boolean;
  };
  removalPolicy: RemovalPolicy;
}

const envConfigs: Record<string, EnvironmentConfig> = {
  staging: {
    envName: "staging",
    domainName: process.env.STAGING_DOMAIN_NAME || "dev.clarocustoms.com",
    instanceSize: {
      portal: {
        instanceType: ec2.InstanceType.of(ec2.InstanceClass.T3A, ec2.InstanceSize.SMALL),
        memoryLimit: 2048,
        memoryReservation: 512
      },
      backoffice: {
        instanceType: ec2.InstanceType.of(ec2.InstanceClass.T3A, ec2.InstanceSize.MICRO),
        memoryLimit: 384,
        memoryReservation: 256
      },
      database: ec2.InstanceType.of(ec2.InstanceClass.BURSTABLE4_GRAVITON, ec2.InstanceSize.MICRO),
      redis: "cache.t4g.micro"
    },
    scaling: {
      portal: { min: 1, max: 2 },
      backoffice: { min: 1, max: 2 }
    },
    rds: {
      allocatedStorage: 20,
      maxAllocatedStorage: 100,
      backupRetention: 7,
      multiAz: false,
      deletionProtection: true
    },
    removalPolicy: RemovalPolicy.RETAIN
  },
  production: {
    envName: "production",
    domainName: process.env.PROD_DOMAIN_NAME || "clarocustoms.com",
    instanceSize: {
      portal: {
        instanceType: ec2.InstanceType.of(ec2.InstanceClass.T3A, ec2.InstanceSize.SMALL),
        memoryLimit: 2048,
        memoryReservation: 512
      },
      backoffice: {
        instanceType: ec2.InstanceType.of(ec2.InstanceClass.T3A, ec2.InstanceSize.MICRO),
        memoryLimit: 384,
        memoryReservation: 256
      },
      database: ec2.InstanceType.of(ec2.InstanceClass.BURSTABLE4_GRAVITON, ec2.InstanceSize.MICRO),
      redis: "cache.t4g.micro"
    },
    scaling: {
      portal: { min: 1, max: 4 },
      backoffice: { min: 1, max: 2 }
    },
    rds: {
      allocatedStorage: 50,
      maxAllocatedStorage: 200,
      backupRetention: 14,
      multiAz: true,
      deletionProtection: true
    },
    removalPolicy: RemovalPolicy.RETAIN
  }
};
//#endregion
export class InfrastructureStack extends Stack {
  constructor(scope: Construct, id: string, props?: StackProps) {
    super(scope, id, props);

    // Get environment from context or environment variable
    const envName = process.env.ENVIRONMENT || "staging";
    const envConfig = envConfigs[envName];

    if (!envConfig) {
      throw new Error(
        `Environment '${envName}' not found. Available environments: ${Object.keys(envConfigs).join(", ")}`
      );
    }

    console.log(`Deploying to environment: ${envName}`);

    const isStaging = envConfig.envName === "staging";

    //#region IAM & Github action auth
    // Create or reference existing IAM OIDC provider for github actions
    const oidcProvider = isStaging
      ? new iam.OpenIdConnectProvider(this, "ClaroOIDCProvider", {
          url: "https://token.actions.githubusercontent.com",
          clientIds: ["sts.amazonaws.com"]
        })
      : iam.OpenIdConnectProvider.fromOpenIdConnectProviderArn(
          this,
          "ClaroOIDCProvider",
          "arn:aws:iam::695133745733:oidc-provider/token.actions.githubusercontent.com"
        );

    const conditions = {
      StringLike: {
        "token.actions.githubusercontent.com:sub": `repo:AIstribute/Claro:*`
      }
    };

    const githubActionsRole = new iam.Role(this, "GitHubActionsRole", {
      assumedBy: new iam.WebIdentityPrincipal(oidcProvider.openIdConnectProviderArn, conditions),
      description: "IAM Role for GitHub Actions",
      roleName: isStaging ? "GitHubActionsRole" : `GitHubActionsRole-${envConfig.envName}`
    });

    githubActionsRole.addToPolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          "ecs:*",
          "ssm:GetParameter",
          "cloudformation:*",
          "iam:PassRole",
          "cloudfront:*",
          // Migrations
          "ssm:StartSession",
          "ssm:DescribeSessions",
          "ssm:TerminateSession",
          "ssm:GetConnectionStatus"
        ],
        resources: [
          "arn:aws:ssm:ca-central-1:695133745733:parameter/cdk-bootstrap/hnb659fds/version", // SSM
          "arn:aws:ecs:ca-central-1:695133745733:cluster/ClaroInfrastructureStack*", // ECS cluster (wildcard for all env)
          "arn:aws:cloudformation:ca-central-1:695133745733:stack/ClaroInfrastructureStack*", // CloudFormation stack (wildcard for all env)
          "arn:aws:iam::695133745733:role/cdk-hnb659fds-cfn-exec-role-695133745733-ca-central-1", // CloudFormation execution role
          "arn:aws:cloudfront::695133745733:distribution/E2SOJ6ORUEJL0E", // CloudFront portal staging distribution
          "arn:aws:cloudfront::695133745733:distribution/E3N7ZW0W6V771H", // CloudFront backoffice staging distribution
          // Migrations
          "arn:aws:ec2:ca-central-1:695133745733:instance/i-0ca715bc5d6e2f1e9",
          "arn:aws:ssm:ca-central-1::document/AWS-StartPortForwardingSessionToRemoteHost"
        ]
      })
    );
    githubActionsRole.addToPolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: ["ecr:*", "s3:*"],
        resources: [
          "*" // Required to be "*" for ecr:GetAuthorizationToken
        ]
      })
    );

    // Create a task role for ECS services
    const ecsTaskRole = new iam.Role(this, "EcsTaskRole", {
      assumedBy: new iam.ServicePrincipal("ecs-tasks.amazonaws.com")
    });

    // Grant Textract permissions to the task role
    ecsTaskRole.addToPolicy(
      new iam.PolicyStatement({
        actions: ["textract:*"],
        resources: ["*"]
      })
    );

    // Grant ECS to run task
    ecsTaskRole.addToPolicy(
      new iam.PolicyStatement({
        actions: ["ecs:RunTask", "ecs:DescribeTasks", "iam:PassRole", "ssm:GetParameter"],
        resources: ["*"]
      })
    );
    //#endregion

    //#region Network & Security
    // Create a VPC
    const vpc = new ec2.Vpc(this, "ClaroVpc", {
      maxAzs: 2,
      subnetConfiguration: [
        {
          cidrMask: 24,
          name: "public-subnet",
          subnetType: ec2.SubnetType.PUBLIC
        },
        {
          cidrMask: 24,
          name: "private-subnet",
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS
        },
        {
          cidrMask: 24,
          name: "isolated-subnet",
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED
        }
      ]
    });

    const portalAsgSecurityGroup = new ec2.SecurityGroup(this, "PortalAsgSG", {
      vpc,
      allowAllOutbound: true,
      description: "Security group for Portal ASG instances"
    });
    const backofficeAsgSecurityGroup = new ec2.SecurityGroup(this, "BackofficeAsgSG", {
      vpc,
      allowAllOutbound: true,
      description: "Security group for Backoffice ASG instances"
    });

    const rdsSecurityGroup = new ec2.SecurityGroup(this, "RdsSecurityGroup", {
      vpc,
      description: "Allow ECS access to RDS",
      allowAllOutbound: true
    });

    const elastiCacheRedisSecurityGroup = new ec2.SecurityGroup(this, "ElastiCacheRedisSecurityGroup", {
      vpc,
      description: "Allow ECS access to ElastiCache Redis",
      allowAllOutbound: true
    });

    const elastiCacheRedisSubnetGroup = new elasticache.CfnSubnetGroup(this, "ElastiCacheRedisSubnetGroup", {
      cacheSubnetGroupName: isStaging
        ? "claro-redis-subnet-group"
        : `claro-${envConfig.envName}-redis-subnet-group`,
      subnetIds: vpc.privateSubnets.map((subnet) => subnet.subnetId),
      description: "Subnet group for ElastiCache Redis"
    });

    const elastiCacheRedisParameterGroup = new elasticache.CfnParameterGroup(
      this,
      "ElastiCacheRedisParameterGroup",
      {
        cacheParameterGroupFamily: "redis7",
        description: "Parameter group for ElastiCache Redis used by BullMQ",
        properties: {
          "maxmemory-policy": "noeviction"
        }
      }
    );

    const blockedIPSet = new wafv2.CfnIPSet(this, "BlockedIPSet", {
      name: isStaging ? "BlockedIPSet" : `BlockedIPSet-${envConfig.envName}`,
      scope: "REGIONAL",
      addresses: [
        // "**********/32",
        "***********/32",
        "*************/32"
      ], // Add blocked IPs here
      ipAddressVersion: "IPV4"
    });

    // Define a Web ACL
    const webAcl = new wafv2.CfnWebACL(this, "ClaroWebACL", {
      name: isStaging ? "ClaroWebACL" : `ClaroWebACL-${envConfig.envName}`,
      scope: "REGIONAL",
      defaultAction: {
        allow: {}
      },
      rules: [
        // AWS managed rule: PHP
        {
          name: "AWS-AWSManagedRulesPHPRuleSet",
          priority: 0,
          statement: {
            managedRuleGroupStatement: {
              vendorName: "AWS",
              name: "AWSManagedRulesPHPRuleSet"
            }
          },
          overrideAction: {
            none: {}
          },
          visibilityConfig: {
            sampledRequestsEnabled: true,
            cloudWatchMetricsEnabled: true,
            metricName: "AWS-AWSManagedRulesPHPRuleSet"
          }
        },
        // AWS managed rule: block req from services that allows anonymous IDs
        {
          name: "AWS-AWSManagedRulesAnonymousIpList",
          priority: 1,
          statement: {
            managedRuleGroupStatement: {
              vendorName: "AWS",
              name: "AWSManagedRulesAnonymousIpList"
            }
          },
          overrideAction: {
            none: {}
          },
          visibilityConfig: {
            sampledRequestsEnabled: true,
            cloudWatchMetricsEnabled: false,
            metricName: "AWS-AWSManagedRulesAnonymousIpList"
          }
        },
        // AWS managed rule: block sources associated with bots or other threats
        {
          name: "AWS-AWSManagedRulesAmazonIpReputationList",
          priority: 2,
          statement: {
            managedRuleGroupStatement: {
              vendorName: "AWS",
              name: "AWSManagedRulesAmazonIpReputationList"
            }
          },
          overrideAction: {
            none: {}
          },
          visibilityConfig: {
            sampledRequestsEnabled: true,
            cloudWatchMetricsEnabled: false,
            metricName: "AWS-AWSManagedRulesAmazonIpReputationList"
          }
        },
        // AWS managed rule: wordpress
        // {
        //   name: "AWS-AWSManagedRulesWordPressRuleSet",
        //   priority: 3,
        //   statement: {
        //     managedRuleGroupStatement: {
        //       vendorName: "AWS",
        //       name: "AWSManagedRulesWordPressRuleSet",
        //     },
        //   },
        //   overrideAction: {
        //     none: {},
        //   },
        //   visibilityConfig: {
        //     sampledRequestsEnabled: true,
        //     cloudWatchMetricsEnabled: false,
        //     metricName: "AWS-AWSManagedRulesWordPressRuleSet",
        //   },
        // },
        // Rule to block IPs in the IP set
        {
          name: "BlockSuspiciousIPs",
          priority: 4,
          action: {
            block: {}
          },
          statement: {
            ipSetReferenceStatement: {
              arn: blockedIPSet.attrArn
            }
          },
          visibilityConfig: {
            sampledRequestsEnabled: true,
            cloudWatchMetricsEnabled: false,
            metricName: "BlockedIPSet"
          }
        },
        {
          name: "RateLimitRule",
          priority: 5,
          action: {
            block: {}
          },
          statement: {
            rateBasedStatement: {
              limit: 1000, // Limit: 1000 requests per 5 minutes
              aggregateKeyType: "IP"
            }
          },
          visibilityConfig: {
            sampledRequestsEnabled: true,
            cloudWatchMetricsEnabled: false,
            metricName: "RateLimitRule"
          }
        }
      ],
      visibilityConfig: {
        cloudWatchMetricsEnabled: true,
        metricName: "ClaroWebACL",
        sampledRequestsEnabled: true
      }
    });

    // Create a secret for the RDS credentials
    const dbCredentialsSecret = new secretsmanager.Secret(this, "ClaroDbCredentialsSecret", {
      secretName: isStaging ? "claroDbCredentials" : `claro-${envConfig.envName}-db-credentials`,
      generateSecretString: {
        secretStringTemplate: JSON.stringify({
          username: process.env.DB_USER
        }),
        excludePunctuation: true,
        includeSpace: false,
        generateStringKey: "password"
      }
    });

    // Import existing ACM certificate
    const certificateCa = acm.Certificate.fromCertificateArn(
      this,
      "ClaroCertificate",
      process.env.AWS_ACM_CERTIFICATE_ARN || ""
    );
    const certificateUs = acm.Certificate.fromCertificateArn(
      this,
      "ClaroCertificateUs",
      process.env.AWS_ACM_CERTIFICATE_ARN_US || ""
    );
    //#endregion

    //#region Database & storage
    const bucketConfig = {
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      bucketKeyEnabled: true,
      enforceSSL: true,
      encryption: s3.BucketEncryption.S3_MANAGED,
      accessControl: s3.BucketAccessControl.PRIVATE,
      removalPolicy: envConfig.removalPolicy,
      objectOwnership: s3.ObjectOwnership.BUCKET_OWNER_ENFORCED
    };

    // Create a S3 bucket for document storage
    const documentBucket = s3.Bucket.fromBucketName(
      this,
      "ClaroDocumentBucket",
      isStaging ? "claro-document-storage" : `claro-${envConfig.envName}-document-storage`
    );

    // Grant S3 permissions to the task role
    documentBucket.grantReadWrite(ecsTaskRole);

    // Create S3 bucket for production frontend UI
    if (envConfig.envName === "production") {
      const portalBucket = new s3.Bucket(this, "ClaroPortalBucket", {
        bucketName: "portal.clarocustoms.com",
        ...bucketConfig
      });
      const backofficeBucket = new s3.Bucket(this, "ClaroBackofficeBucket", {
        bucketName: "backoffice.clarocustoms.com",
        ...bucketConfig
      });
      // const portalBucket = s3.Bucket.fromBucketName(this, "ClaroPortalBucket", "portal.clarocustoms.com");
      // const backofficeBucket = s3.Bucket.fromBucketName(
      //   this,
      //   "ClaroBackofficeBucket",
      //   "backoffice.clarocustoms.com"
      // );

      // Create Origin Access Control for CloudFront
      const portalOac = new S3OriginAccessControl(this, "ClaroPortalOAC", {
        description: "Claro portal prod control setting"
      });
      const backofficeOac = new S3OriginAccessControl(this, "ClaroBackofficeOAC", {
        description: "Claro backoffice prod control setting"
      });

      const errorResponses = [
        {
          httpStatus: 403,
          responseHttpStatus: 200,
          responsePagePath: "/index.html"
        },
        {
          httpStatus: 404,
          responseHttpStatus: 200,
          responsePagePath: "/index.html"
        }
      ];
      const behaviorConfig = {
        allowedMethods: AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
        viewerProtocolPolicy: ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        cachePolicy: CachePolicy.CACHING_OPTIMIZED
      };

      // Create CloudFront distribution for frontend
      const portalCloudfront = new Distribution(this, "ClaroPortalCloudfront", {
        comment: "Claro Portal Prod CDN",
        defaultBehavior: {
          origin: S3BucketOrigin.withOriginAccessControl(portalBucket, {
            originAccessControl: portalOac
          }),
          ...behaviorConfig
        },
        domainNames: ["portal.clarocustoms.com"],
        certificate: certificateUs,
        priceClass: PriceClass.PRICE_CLASS_100,
        errorResponses
      });
      const backofficeCloudfront = new Distribution(this, "ClaroBackofficeCloudfront", {
        comment: "Claro Backoffice Prod CDN",
        defaultBehavior: {
          origin: S3BucketOrigin.withOriginAccessControl(backofficeBucket, {
            originAccessControl: backofficeOac
          }),
          ...behaviorConfig
        },
        domainNames: ["backoffice.clarocustoms.com"],
        certificate: certificateUs,
        priceClass: PriceClass.PRICE_CLASS_100,
        errorResponses
      });

      // Add bucket policy to allow CloudFront access
      portalBucket.addToResourcePolicy(
        new iam.PolicyStatement({
          sid: "AllowCloudFrontServicePrincipal",
          effect: iam.Effect.ALLOW,
          principals: [new iam.ServicePrincipal("cloudfront.amazonaws.com")],
          actions: ["s3:GetObject"],
          resources: [`${portalBucket.bucketArn}/*`],
          conditions: {
            StringEquals: {
              "AWS:SourceArn": portalCloudfront.distributionArn
            }
          }
        })
      );
      backofficeBucket.addToResourcePolicy(
        new iam.PolicyStatement({
          sid: "AllowCloudFrontServicePrincipal",
          effect: iam.Effect.ALLOW,
          principals: [new iam.ServicePrincipal("cloudfront.amazonaws.com")],
          actions: ["s3:GetObject"],
          resources: [`${backofficeBucket.bucketArn}/*`],
          conditions: {
            StringEquals: {
              "AWS:SourceArn": backofficeCloudfront.distributionArn
            }
          }
        })
      );

      // Add CloudFront distribution ARN to GitHub Actions role
      githubActionsRole.addToPolicy(
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: ["cloudfront:*"],
          resources: [portalCloudfront.distributionArn, backofficeCloudfront.distributionArn]
        })
      );
    }

    const elastiCacheRedis = new elasticache.CfnCacheCluster(this, "ElastiCacheRedis", {
      engine: "redis",
      engineVersion: "7.1",
      cacheNodeType: envConfig.instanceSize.redis,
      numCacheNodes: 1,
      clusterName: isStaging ? "claro-redis" : `claro-${envConfig.envName}-redis`,
      vpcSecurityGroupIds: [elastiCacheRedisSecurityGroup.securityGroupId],
      azMode: "single-az",
      autoMinorVersionUpgrade: true,
      cacheSubnetGroupName: elastiCacheRedisSubnetGroup.ref,
      cacheParameterGroupName: elastiCacheRedisParameterGroup.ref
    });

    // Create or reference RDS instance for PostgreSQL
    const dbInstance = isStaging
      ? rds.DatabaseInstance.fromDatabaseInstanceAttributes(this, "ClaroDbInstance", {
          instanceIdentifier: "claro-db",
          instanceEndpointAddress: process.env.DB_HOST ?? "",
          port: 5432,
          securityGroups: [rdsSecurityGroup]
        })
      : new rds.DatabaseInstance(this, "ClaroDbInstance", {
          engine: rds.DatabaseInstanceEngine.postgres({
            version: rds.PostgresEngineVersion.VER_16_9
          }),
          instanceType: envConfig.instanceSize.database,
          instanceIdentifier: `claro-${envConfig.envName}-db`,
          vpc,
          credentials: rds.Credentials.fromSecret(dbCredentialsSecret),
          multiAz: envConfig.rds.multiAz,
          allocatedStorage: envConfig.rds.allocatedStorage,
          maxAllocatedStorage: envConfig.rds.maxAllocatedStorage,
          allowMajorVersionUpgrade: false,
          autoMinorVersionUpgrade: true,
          backupRetention: Duration.days(envConfig.rds.backupRetention),
          deleteAutomatedBackups: false,
          removalPolicy: envConfig.removalPolicy,
          deletionProtection: envConfig.rds.deletionProtection,
          databaseName: process.env.DB_NAME,
          publiclyAccessible: false,
          vpcSubnets: {
            subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS
          },
          securityGroups: [rdsSecurityGroup]
        });
    //#endregion

    //#region ECS services

    //#region Docker image
    // Define the Docker image for portal-api - manual repo creation
    // const portalApiImage = ecs.ContainerImage.fromEcrRepository(
    //   ecr.Repository.fromRepositoryName(this, "PortalApiImage", "portal-api"),
    //   "latest"
    // );
    // const backofficeApiImage = ecs.ContainerImage.fromEcrRepository(
    //   ecr.Repository.fromRepositoryName(
    //     this,
    //     "BackofficeApiImage",
    //     "backoffice-api"
    //   ),
    //   "latest"
    // );

    // Use managed docker by ecr assets - portal-api
    const portalApiDocker = new ecr_assets.DockerImageAsset(this, "PortalApiDocker", {
      directory: "../../common/deploy/portal-api"
    });

    // Use managed docker by ecr assets - backoffice-api
    const backofficeApiDocker = new ecr_assets.DockerImageAsset(this, "BackofficeApiDocker", {
      directory: "../../common/deploy/backoffice-api"
      // platform: ecr_assets.Platform.LINUX_ARM64
    });
    //#endregion

    // Create an ECS cluster
    const cluster = new ecs.Cluster(this, "ClaroCluster", {
      vpc
    });

    const environments = {
      ENVIRONMENT: envConfig.envName,
      NODE_ENV: "production",
      DB_HOST: dbInstance.dbInstanceEndpointAddress,
      DB_PORT: dbInstance.dbInstanceEndpointPort,
      DB_USER: process.env.DB_USER ?? "",
      DB_NAME: process.env.DB_NAME ?? "",
      ACCESS_TOKEN_EXPIRES_IN_SEC: process.env.ACCESS_TOKEN_EXPIRES_IN_SEC ?? "",
      REFRESH_TOKEN_EXPIRES_IN_SEC: process.env.REFRESH_TOKEN_EXPIRES_IN_SEC ?? "",
      REFRESH_TOKEN_GRACE_PERIOD_SEC: process.env.REFRESH_TOKEN_GRACE_PERIOD_SEC ?? "",
      GOOGLE_OAUTH_CLIENT_ID: process.env.GOOGLE_OAUTH_CLIENT_ID ?? "",
      GOOGLE_OAUTH_CLIENT_SECRET: process.env.GOOGLE_OAUTH_CLIENT_SECRET ?? "",
      DOCUSIGN_SECRET_KEY: process.env.DOCUSIGN_SECRET_KEY ?? "",
      DOCUSIGN_INTEGRATION_KEY: process.env.DOCUSIGN_INTEGRATION_KEY ?? "",
      OAUTH_STATE_SECRET_KEY: process.env.OAUTH_STATE_SECRET_KEY ?? "",
      DOCUSIGN_WEBHOOK_HMAC_KEY: process.env.DOCUSIGN_WEBHOOK_HMAC_KEY ?? "",
      DOCUSIGN_POA_TEMPLATE_ID: process.env.DOCUSIGN_POA_TEMPLATE_ID ?? "",
      DOCUSIGN_US_POA_TEMPLATE_ID: process.env.DOCUSIGN_US_POA_TEMPLATE_ID ?? "",
      DOCUSIGN_BASE_URL: process.env.DOCUSIGN_BASE_URL ?? "",
      CANDATA_REMOTE_SERVER_APP_ID: process.env.CANDATA_REMOTE_SERVER_APP_ID ?? "",
      USAV_CANDATA_BASE_URL: process.env.USAV_CANDATA_BASE_URL ?? "",
      USAV_CANDATA_TOKEN: process.env.USAV_CANDATA_TOKEN ?? "",
      CLARO_CANDATA_BASE_URL: process.env.CLARO_CANDATA_BASE_URL ?? "",
      CLARO_CANDATA_AUTH_URL: process.env.CLARO_CANDATA_AUTH_URL ?? "",
      CLARO_CANDATA_CLIENT_ID: process.env.CLARO_CANDATA_CLIENT_ID ?? "",
      CLARO_CANDATA_CLIENT_SECRET: process.env.CLARO_CANDATA_CLIENT_SECRET ?? "",
      APIFY_TOKEN: process.env.APIFY_TOKEN ?? "",
      FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID ?? "",
      FIREBASE_PRIVATE_KEY: process.env.FIREBASE_PRIVATE_KEY ?? "",
      FIREBASE_CLIENT_EMAIL: process.env.FIREBASE_CLIENT_EMAIL ?? "",
      CARM_APP_ID: process.env.CARM_APP_ID ?? ""
    };

    //#region Portal service
    const portalTemplate = new ec2.LaunchTemplate(this, "PortalLaunchTemplate", {
      instanceType: envConfig.instanceSize.portal.instanceType,
      machineImage: ecs.EcsOptimizedImage.amazonLinux2023(),
      securityGroup: portalAsgSecurityGroup,
      userData: ec2.UserData.forLinux(),
      role: new iam.Role(this, "PortalEC2Role", {
        assumedBy: new iam.ServicePrincipal("ec2.amazonaws.com"),
        managedPolicies: [
          iam.ManagedPolicy.fromAwsManagedPolicyName("service-role/AmazonEC2ContainerServiceforEC2Role"),
          iam.ManagedPolicy.fromAwsManagedPolicyName("AmazonSSMManagedInstanceCore")
        ]
      })
    });

    const portalAsg = new AutoScalingGroup(this, "PortalASG", {
      vpc,
      vpcSubnets: { subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS },
      minCapacity: envConfig.scaling.portal.min,
      maxCapacity: envConfig.scaling.portal.max,
      mixedInstancesPolicy: {
        instancesDistribution: {
          onDemandPercentageAboveBaseCapacity: 20,
          spotAllocationStrategy: SpotAllocationStrategy.PRICE_CAPACITY_OPTIMIZED
        },
        launchTemplate: portalTemplate
      }
    });

    portalAsg.addUserData(`echo ECS_CLUSTER=${cluster.clusterName} >> /etc/ecs/ecs.config`);

    const portalCapacityProvider = new ecs.AsgCapacityProvider(this, "PortalCapacityProvider", {
      autoScalingGroup: portalAsg,
      enableManagedScaling: true,
      enableManagedTerminationProtection: true,
      spotInstanceDraining: true
    });
    cluster.addAsgCapacityProvider(portalCapacityProvider);

    const portalTaskDef = new ecs.Ec2TaskDefinition(this, "PortalTaskDef", {
      taskRole: ecsTaskRole
    });

    portalTaskDef.addContainer("PortalContainer", {
      image: ecs.ContainerImage.fromDockerImageAsset(portalApiDocker),
      portMappings: [
        {
          containerPort: 5001,
          protocol: ecs.Protocol.TCP
        }
      ],
      logging: ecs.LogDrivers.awsLogs({ streamPrefix: "portal" }),
      memoryLimitMiB: envConfig.instanceSize.portal.memoryLimit,
      memoryReservationMiB: envConfig.instanceSize.portal.memoryReservation,
      environment: {
        ...environments,
        NO_COLOR: "true",
        DB_SSL_ENABLED: "true",
        ACCESS_TOKEN_SECRET: process.env.ACCESS_TOKEN_SECRET ?? "",
        REFRESH_TOKEN_SECRET: process.env.REFRESH_TOKEN_SECRET ?? "",
        OPENAI_API_KEY: process.env.OPENAI_API_KEY ?? "",
        AWS_S3_BUCKET: documentBucket.bucketName,
        AWS_REGION: process.env.AWS_REGION ?? "ca-central-1",
        REDIS_HOST: elastiCacheRedis.attrRedisEndpointAddress,
        REDIS_PORT: elastiCacheRedis.attrRedisEndpointPort,
        LLAMA_CLOUD_API_KEY: process.env.LLAMA_CLOUD_API_KEY ?? "",
        DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY ?? "",
        FILE_PROCESSOR_CONCURRENCY: "4",
        DOCUMENT_PROCESSOR_CONCURRENCY: "4",
        AGGREGATION_PROCESSOR_CONCURRENCY: "1",
        API_TEMPLATE_API_KEY: process.env.API_TEMPLATE_API_KEY ?? "",
        BACKOFFICE_EMAIL_ADDRESS: process.env.BACKOFFICE_EMAIL_ADDRESS ?? "",
        SYSTEM_FROM_EMAIL: process.env.SYSTEM_FROM_EMAIL ?? "",
        PORTAL_FRONTEND_URL: process.env.PORTAL_FRONTEND_URL ?? "",
        APIFY_US_TARIFF_STORE_NAME: process.env.APIFY_US_TARIFF_STORE_NAME ?? "",
        APIFY_US_TARIFF_ACTOR_NAME: process.env.APIFY_US_TARIFF_ACTOR_NAME ?? "",
        APIFY_US_TARIFF_ACTOR_ID: process.env.APIFY_US_TARIFF_ACTOR_ID ?? ""
      },
      secrets: {
        DB_PASSWORD: ecs.Secret.fromSecretsManager(dbCredentialsSecret, "password")
      }
    });

    const portalEc2Service = new ecs.Ec2Service(this, "PortalEc2Service", {
      cluster,
      taskDefinition: portalTaskDef,
      serviceName: isStaging ? "claro-portal" : `claro-${envConfig.envName}-portal`,
      desiredCount: 1,
      healthCheckGracePeriod: Duration.seconds(60),
      circuitBreaker: {
        enable: true,
        rollback: true
      },
      deploymentController: {
        type: ecs.DeploymentControllerType.ECS
      },
      capacityProviderStrategies: [
        {
          capacityProvider: portalCapacityProvider.capacityProviderName,
          weight: 1
        }
      ]
    });

    const portalAlb = new elbv2.ApplicationLoadBalancer(this, "PortalALB", {
      vpc,
      internetFacing: true,
      loadBalancerName: isStaging ? "PortalALB" : `claro-${envConfig.envName}-portal-alb`,
      vpcSubnets: { subnetType: ec2.SubnetType.PUBLIC },
      securityGroup: new ec2.SecurityGroup(this, "PortalALBSG", {
        vpc,
        allowAllOutbound: true,
        description: "Security group for Portal ALB"
      })
    });
    portalAlb.connections.allowFrom(ec2.Peer.anyIpv4(), ec2.Port.tcp(80), "Allow HTTP inbound");
    portalAlb.connections.allowFrom(ec2.Peer.anyIpv4(), ec2.Port.tcp(443), "Allow HTTPS inbound");

    portalAlb.addListener("HttpListener", {
      port: 80,
      protocol: elbv2.ApplicationProtocol.HTTP,
      defaultAction: elbv2.ListenerAction.redirect({
        port: "443",
        protocol: elbv2.Protocol.HTTPS,
        permanent: true
      })
    });
    const portalHttpsListener = portalAlb.addListener("HttpsListener", {
      port: 443,
      protocol: elbv2.ApplicationProtocol.HTTPS,
      certificates: [certificateCa],
      sslPolicy: elbv2.SslPolicy.TLS13_RES
    });

    const portalTargetGroup = portalHttpsListener.addTargets("ECS", {
      targetGroupName: isStaging ? "ClaroI-Porta-O8FLGSW63GLM" : `claro-${envConfig.envName}-portal-tg`,
      targets: [portalEc2Service],
      protocol: elbv2.ApplicationProtocol.HTTP
    });
    portalTargetGroup.configureHealthCheck({
      path: "/heartbeat",
      interval: Duration.seconds(60),
      timeout: Duration.seconds(5),
      healthyThresholdCount: 2,
      unhealthyThresholdCount: 3
    });

    portalAsgSecurityGroup.addIngressRule(
      ec2.Peer.securityGroupId(portalAlb.connections.securityGroups[0].securityGroupId),
      ec2.Port.allTcp(),
      "Allow ALB to reach ECS service on dynamic ports"
    );
    rdsSecurityGroup.addIngressRule(
      ec2.Peer.securityGroupId(portalAsgSecurityGroup.securityGroupId),
      ec2.Port.tcp(5432),
      "Allow Portal EC2 access to RDS"
    );
    elastiCacheRedisSecurityGroup.addIngressRule(
      ec2.Peer.securityGroupId(portalAsgSecurityGroup.securityGroupId),
      ec2.Port.tcp(6379),
      "Allow Portal EC2 access to ElastiCache Redis"
    );
    // Allow bastion host access to Redis (environment-specific)
    if (isStaging) {
      elastiCacheRedisSecurityGroup.addIngressRule(
        ec2.Peer.securityGroupId("sg-0fa58ee71fd6bb409"), // Staging bastion host
        ec2.Port.tcp(6379),
        "Allow EC2 bastion host access to ElastiCache Redis"
      );
    }
    // TODO: manually add bastion host to prod redis security group

    new wafv2.CfnWebACLAssociation(this, "PortalEc2WebACLAssociation", {
      resourceArn: portalAlb.loadBalancerArn,
      webAclArn: webAcl.attrArn
    });

    //#endregion

    //#region Backoffice service
    const backofficeTemplate = new ec2.LaunchTemplate(this, "BackofficeLaunchTemplate", {
      instanceType: envConfig.instanceSize.backoffice.instanceType,
      machineImage: ecs.EcsOptimizedImage.amazonLinux2023(),
      securityGroup: backofficeAsgSecurityGroup,
      userData: ec2.UserData.forLinux(),
      role: new iam.Role(this, "BackofficeEC2Role", {
        assumedBy: new iam.ServicePrincipal("ec2.amazonaws.com"),
        managedPolicies: [
          iam.ManagedPolicy.fromAwsManagedPolicyName("service-role/AmazonEC2ContainerServiceforEC2Role"),
          iam.ManagedPolicy.fromAwsManagedPolicyName("AmazonSSMManagedInstanceCore")
        ]
      })
    });

    const backofficeAsg = new AutoScalingGroup(this, "BackofficeASG", {
      vpc,
      vpcSubnets: { subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS },
      minCapacity: envConfig.scaling.backoffice.min,
      maxCapacity: envConfig.scaling.backoffice.max,
      // spotPrice: '0.0104',
      mixedInstancesPolicy: {
        instancesDistribution: {
          onDemandPercentageAboveBaseCapacity: 20,
          // onDemandBaseCapacity: 0,
          spotAllocationStrategy: SpotAllocationStrategy.PRICE_CAPACITY_OPTIMIZED
        },
        launchTemplate: backofficeTemplate
      }
    });

    // Add user data script to join the ECS cluster
    backofficeAsg.addUserData(`echo ECS_CLUSTER=${cluster.clusterName} >> /etc/ecs/ecs.config`);

    const backofficeCapacityProvider = new ecs.AsgCapacityProvider(this, "BackofficeCapacityProvider", {
      autoScalingGroup: backofficeAsg,
      enableManagedScaling: true,
      enableManagedTerminationProtection: true,
      spotInstanceDraining: true
    });
    cluster.addAsgCapacityProvider(backofficeCapacityProvider);

    const backofficeTaskDef = new ecs.Ec2TaskDefinition(this, "BackofficeTaskDef");

    backofficeTaskDef.addContainer("BackofficeContainer", {
      image: ecs.ContainerImage.fromDockerImageAsset(backofficeApiDocker),
      portMappings: [
        {
          containerPort: 5000,
          protocol: ecs.Protocol.TCP
        }
      ],
      logging: ecs.LogDrivers.awsLogs({ streamPrefix: "backoffice" }),
      memoryLimitMiB: envConfig.instanceSize.backoffice.memoryLimit,
      memoryReservationMiB: envConfig.instanceSize.backoffice.memoryReservation,
      environment: {
        ...environments,
        ACCESS_TOKEN_SECRET: process.env.ACCESS_TOKEN_SECRET_BO ?? "",
        REFRESH_TOKEN_SECRET: process.env.REFRESH_TOKEN_SECRET_BO ?? ""
      },
      secrets: {
        DB_PASSWORD: ecs.Secret.fromSecretsManager(dbCredentialsSecret, "password")
      }
    });

    const backofficeEc2Service = new ecs.Ec2Service(this, "BackofficeEc2Service", {
      cluster,
      taskDefinition: backofficeTaskDef,
      serviceName: isStaging ? "claro-backoffice" : `claro-${envConfig.envName}-backoffice`,
      desiredCount: 1,
      healthCheckGracePeriod: Duration.seconds(60),
      circuitBreaker: {
        enable: true,
        rollback: true
      },
      deploymentController: {
        type: ecs.DeploymentControllerType.ECS
      },
      capacityProviderStrategies: [
        {
          capacityProvider: backofficeCapacityProvider.capacityProviderName,
          weight: 1
        }
      ]
    });

    const backofficeAlb = new elbv2.ApplicationLoadBalancer(this, "BackofficeALB", {
      vpc,
      internetFacing: true,
      loadBalancerName: isStaging ? "BackofficeALB" : `claro-${envConfig.envName}-backoffice-alb`,
      vpcSubnets: { subnetType: ec2.SubnetType.PUBLIC },
      securityGroup: new ec2.SecurityGroup(this, "BackofficeALBSG", {
        vpc,
        allowAllOutbound: true,
        description: "Security group for Backoffice ALB"
      })
    });
    backofficeAlb.connections.allowFrom(ec2.Peer.anyIpv4(), ec2.Port.tcp(80), "Allow HTTP inbound");
    backofficeAlb.connections.allowFrom(ec2.Peer.anyIpv4(), ec2.Port.tcp(443), "Allow HTTPS inbound");

    backofficeAlb.addListener("HttpListener", {
      port: 80,
      protocol: elbv2.ApplicationProtocol.HTTP,
      defaultAction: elbv2.ListenerAction.redirect({
        port: "443",
        protocol: elbv2.Protocol.HTTPS,
        permanent: true
      })
    });
    const backofficeHttpsListener = backofficeAlb.addListener("HttpsListener", {
      port: 443,
      protocol: elbv2.ApplicationProtocol.HTTPS,
      certificates: [certificateCa],
      sslPolicy: elbv2.SslPolicy.TLS13_RES
    });

    const backofficeTargetGroup = backofficeHttpsListener.addTargets("ECS", {
      targetGroupName: isStaging ? "ClaroI-Backo-RQ0T3YOPRGSU" : `claro-${envConfig.envName}-backoffice-tg`,
      targets: [backofficeEc2Service],
      protocol: elbv2.ApplicationProtocol.HTTP
    });
    backofficeTargetGroup.configureHealthCheck({
      path: "/heartbeat",
      interval: Duration.seconds(60),
      timeout: Duration.seconds(5),
      healthyThresholdCount: 2,
      unhealthyThresholdCount: 3
    });

    backofficeAsgSecurityGroup.addIngressRule(
      ec2.Peer.securityGroupId(backofficeAlb.connections.securityGroups[0].securityGroupId),
      ec2.Port.allTcp(),
      "Allow ALB to reach ECS service on dynamic ports"
    );
    rdsSecurityGroup.addIngressRule(
      ec2.Peer.securityGroupId(backofficeAsgSecurityGroup.securityGroupId),
      ec2.Port.tcp(5432),
      "Allow Backoffice EC2 access to RDS"
    );

    new wafv2.CfnWebACLAssociation(this, "BackofficeWebACLAssociation", {
      resourceArn: backofficeAlb.loadBalancerArn,
      webAclArn: webAcl.attrArn
    });

    //#endregion

    //#endregion

    //#region lambda functions
    // 1. Lambda functions are deployed separately, therefore we need to get a reference of the role and functions.
    const lambdaRole = iam.Role.fromRoleArn(
      this,
      "LambdaRole",
      "arn:aws:iam::695133745733:role/service-role/XlsxParser_parseXlsx-role-sslapofi"
    );

    const parseXlsxLambda = lambda.Function.fromFunctionAttributes(this, "ParseXlsxLambda", {
      functionArn: "arn:aws:lambda:ca-central-1:695133745733:function:XlsxParser_parseXlsx",
      role: lambdaRole
    });

    const pdfToPngLambda = lambda.Function.fromFunctionAttributes(this, "PdfToPngLambda", {
      functionArn: "arn:aws:lambda:ca-central-1:695133745733:function:pdf-to-png",
      role: lambdaRole
    });

    // 2. After getting the reference, we would need to allow ecs to invoke the lambda functions.
    parseXlsxLambda.addPermission("ParseXlsxLambdaPermission", {
      action: "lambda:InvokeFunction",
      principal: ecsTaskRole
    });
    pdfToPngLambda.addPermission("PdfToPngLambdaPermission", {
      action: "lambda:InvokeFunction",
      principal: ecsTaskRole
    });

    // 3. Finally, we need to grant lambda functions to access the document bucket.
    documentBucket.grantReadWrite(parseXlsxLambda);
    documentBucket.grantReadWrite(pdfToPngLambda);
    //#endregion

    //#region Worker parameters
    new ssm.StringParameter(this, "ClusterNameParam", {
      parameterName: isStaging ? "/ecs/cluster-name" : `/ecs/cluster-name-${envConfig.envName}`,
      stringValue: cluster.clusterName
    });
    new ssm.StringParameter(this, "PortalServiceTaskDefinitionParam", {
      parameterName: isStaging
        ? "/ecs/portal-service-task-def"
        : `/ecs/portal-service-task-def-${envConfig.envName}`,
      stringValue: portalTaskDef.taskDefinitionArn
    });

    // new ssm.StringParameter(this, "SecurityGroupsParam", {
    //   parameterName: isStaging ? "/ecs/security-groups" : `/ecs/security-groups-${envConfig.envName}`,
    //   stringValue: portalAsgSecurityGroup.securityGroupId
    // });
    // new ssm.StringParameter(this, "SubnetsParam", {
    //   parameterName: isStaging ? "/ecs/subnets" : `/ecs/subnets-${envConfig.envName}`,
    //   stringValue: vpc.privateSubnets[0].subnetId
    // });
    //#endregion
  }
}
