{"name": "utils", "private": true, "version": "0.1.0", "description": "Utility package", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "files": ["dist"], "types": "dist/index.d.ts", "scripts": {"prebuild": "rm -rf dist", "build": "rollup -c", "dev": "rollup -c --watch", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "danny-aistribute <<EMAIL>>", "license": "ISC", "dependencies": {"date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "ky": "^1.2.4", "query-string": "~9.1.1", "formik": "~2.4.6"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.6", "@types/react": "^18.3.1", "rollup": "^4.17.1", "rollup-plugin-dts": "^6.1.0", "typescript": "~5.5.4", "tslib": "~2.7.0"}}