import { FormikProps, getIn } from "formik";
import queryString from "query-string";
import { kebabCaseToCapitalize, snakeCaseToCapitalize } from "./string.utils";
import {
  PaginationParams,
  PaginationResponse,
  SelectOption,
  SortOrder,
  TableSchema,
  WithAuditNames
} from "./types.utils";

type Options = {
  allCaps?: boolean;
  renderLabel?: "key" | "value" | "both";
  swapValue?: boolean;
  ignoreHyphen?: boolean;
};
/**
 * Converts enum to select options
 * @param {enum} _enum to be converted
 * @param {boolean} [allCaps=false] All capital label
 * @param {Options} [options] Label options
 * @returns Select option array with Capitalize letter
 */
export function enumToSelectOptions(
  _enum: object,
  options: Options = { allCaps: false, renderLabel: "key", swapValue: false, ignoreHyphen: false }
): SelectOption[] {
  const { allCaps = false, renderLabel = "key", swapValue = false, ignoreHyphen = false } = options;

  return Object.entries(_enum).map(([key, value]) => {
    let label = "";
    switch (renderLabel) {
      case "key":
        label = snakeCaseToCapitalize(key);
        break;
      case "value":
        label = !ignoreHyphen ? kebabCaseToCapitalize(value) : value;
        break;
      case "both":
        label = `${value.toUpperCase()} (${snakeCaseToCapitalize(key)})`;
        break;
    }
    if (allCaps) label = label.toUpperCase();

    return {
      label,
      value: swapValue ? key : value
    };
  });
}

/**
 * Converts table schema to select options
 * @param {TableSchema} schema to be converted
 * @returns Select options array
 */
export function schemaToSelectOptions<T>(schema: TableSchema<T>): SelectOption[] {
  return Object.entries(schema)
    .map(([key, value]) =>
      key !== "id" && !value.disableSort ? { label: value.header, value: value.sortKey ?? key } : undefined
    )
    .filter(Boolean);
}

/**
 * Clean form data and convert empty string to null
 * @param {T} params to be cleaned
 * @returns Clean object
 */
export function emptyStringToNull<T>(params: object) {
  type DTO = Record<string, T[keyof T]>;
  const obj = Object.entries(params).reduce<DTO>((acc, [key, val]) => {
    if (val === "") val = null;
    acc[key] = val;

    return acc;
  }, {});

  return obj as T;
}

/**
 * Map page and limit into skip and limit
 * @param {PaginationParams} [pagination] Pagination params
 * @returns Mapped pagination params
 */
type Params = { [key: string]: unknown };
export const paginationMapper = (pagination?: Partial<PaginationParams> & Params) => {
  if (!pagination) {
    return undefined;
  }

  const { page, limit, ...params } = pagination;
  return {
    ...params,
    skip: page && limit ? (page - 1) * limit : 0,
    limit
  };
};

/**
 * Stringify query params
 * @param {string} url Base url
 * @param {Params} [params] Query params
 * @returns stringified query params
 */
export const stringifyQueryParams = (url: string, params?: Params) => {
  return params ? `${url}?${queryString.stringify(paginationMapper(params))}` : url;
};

/**
 * Generate next query params for infinite query
 * @param {PaginationResponse} lastPage Last page response
 * @param {object[]} allPages All pages response
 * @returns Mapped pagination params
 */
export const infiniteQueryMapper = <T>(lastPage: PaginationResponse, allPages: T[]) => {
  const nextPage = allPages.length + 1;
  const totalPages = Math.ceil((lastPage.total ?? 1) / lastPage.limit);
  return nextPage <= totalPages ? nextPage : undefined;
};

/**
 * Flatten infinite query result
 * @param {string} key field props
 * @param {T[]} allPages All pages response
 * @returns Flat response array
 */
export const flattenQueryResults = <T, K extends keyof T>(
  key: K,
  allPages?: T[]
): Array<T[K] extends Array<infer U> ? U : never> => {
  return (
    allPages?.flatMap((page) => {
      const value = page[key];
      // Type guard to ensure the value is an array
      return Array.isArray(value) ? value : [];
    }) || []
  );
};

/**
 * Get error message from formik
 * @param {FormikProps} formik Formik props
 * @param {string} field Field name
 * @returns Error message
 */
export const getFormikError = <T>(formik: Partial<FormikProps<T>>, field: string) =>
  getIn(formik.touched, field) && getIn(formik.errors, field);

/**
 * Get formik input options
 * @param {FormikProps} formik Formik props
 * @param {string} field Field name
 * @param {function} [onChange] Custom on change handler
 * @returns Formik input options
 */
export const formikInputOpts = <T>(
  formik: Partial<FormikProps<T>>,
  field: string,
  onChange?: (e: unknown) => void
) => ({
  name: field,
  value: getIn(formik.values, field) ?? "",
  onBlur: formik.handleBlur,
  onChange: onChange ?? formik.handleChange,
  error: getFormikError(formik, field)
});

/**
 * Download file from url
 * @param {string} url
 */
export function downloadFromUrl(url: string) {
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("target", `_blank`);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * Sorts an array of objects based on a specified key and sort order.
 * @template T - Type of objects in the array (must be a record with string keys)
 * @param {T[] | undefined} data - Array of objects to sort
 * @param {string} [sortBy] - Key to sort by
 * @param {SortOrder} [sortOrder] - Sort direction ('asc' or 'desc')
 * @returns {T[] | undefined} - Sorted array or undefined if input is undefined
 */
export function sortTable<T>(data: T[] | undefined, sortBy?: string, sortOrder?: SortOrder): T[] | undefined {
  if (!data || !sortBy) return data;

  return [...data].sort((a, b) => {
    const aValue = a[sortBy as keyof T];
    const bValue = b[sortBy as keyof T];

    if (aValue === null || aValue === undefined) return 1;
    if (bValue === null || bValue === undefined) return -1;

    const comparison =
      typeof aValue === "string" ? aValue.localeCompare(String(bValue)) : Number(aValue) - Number(bValue);

    return sortOrder === "desc" ? -comparison : comparison;
  });
}

export const getAuditNames = (entity: WithAuditNames) => ({
  createdByName: entity.createdBy?.name || "",
  lastEditedByName: entity.lastEditedBy?.name || ""
});

export const auditTableSchema = {
  createDate: {
    header: "Created Date",
    renderer: "date",
    visible: true
  },
  createdByName: {
    header: "Created By",
    sortKey: "createdById"
  },
  lastEditDate: {
    header: "Last Updated",
    renderer: "date"
  },
  lastEditedByName: {
    header: "Last Updated By",
    sortKey: "lastEditedById"
  }
};
