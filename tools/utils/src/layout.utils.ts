/**
 * Round number value to the desired step
 * @param {number} value - value to round
 * @param {number} [step=2] - rounding step
 * @returns rounded number
 */
export function roundTo(value: number, step: number = 2) {
  return Math.round(value * step) / step;
}

/**
 * Round number value to the grid size
 * @param {number} value - value to round
 * @param {number} [grid=10] - grid size
 * @param {number} [max] - max value
 * @returns rounded values
 */
export const roundToGrid = (value: number, grid: number = 10, max?: number) =>
  value < 0 ? 0 : Math.round((max ? (value > max ? max : value) : value) / grid) * grid;
