import ky, { HTTPError, Options } from "ky";

export class Http {
  instance: typeof ky;
  config: Options;

  constructor(config: Options) {
    this.config = config;
    this.instance = ky.create(config);
  }

  /**
   * Updates the configuration of the Http instance, and recreates the http instance with the new config.
   * @param config ky config
   */
  updateConfig(config: Options): void {
    const newConfig = { ...this.config, ...config };

    this.config = newConfig;
    this.instance = ky.create(newConfig);
  }

  /**
   * GET blob
   * @param url URL to the endpoint
   * @param config Configuration object
   * @returns The response from endpoint
   */
  getBlob(url: string, config?: Options): Promise<Blob> {
    return this.instance.get(url, { ...this.config, ...config }).blob();
  }

  /**
   * GET operation
   * [Read more](https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods/GET)
   * @param url URL to the endpoint
   * @param config Configuration object
   * @returns The response from endpoint
   */
  get<R>(url: string, config?: Options): Promise<R> {
    return this.instance.get(url, { ...this.config, ...config }).json<R>();
  }

  /**
   * POST operation
   * [Read more](https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods/POST)
   * @param url URL to the endpoint
   * @param data Request params to be sent to the endpoint
   * @param config Configuration object
   * @returns The response from endpoint
   */

  post<P, R>(url: string, data?: P, config?: Options): Promise<R> {
    return this.instance
      .post(url, {
        ...this.config,
        ...config,
        json: data
      })
      .json<R>();
  }

  postFormData<R>(url: string, data: FormData, config?: Options): Promise<R> {
    return this.instance
      .post(url, {
        ...this.config,
        ...config,
        body: data
      })
      .json<R>();
  }

  putFormData<R>(url: string, data: FormData, config?: Options): Promise<R> {
    return this.instance
      .put(url, {
        ...this.config,
        ...config,
        body: data
      })
      .json<R>();
  }

  /**
   * PUT operation
   * [Read more](https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods/PUT)
   * @param url URL to the endpoint
   * @param config Configuration object
   * @returns The response from endpoint
   */
  put<P, R>(url: string, data?: P, config?: Options): Promise<R> {
    return this.instance.put(url, { ...this.config, ...config, json: data }).json<R>();
  }

  /**
   * DELETE operation
   * [Read more](https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods/DELETE)
   * @param url URL to the endpoint
   * @param config Configuration object
   * @returns The response from endpoint
   */
  delete<P, R>(url: string, data?: P, config?: Options): Promise<R> {
    return this.instance.delete(url, { ...this.config, ...config, json: data }).json<R>();
  }
}

/**
 * Common function for service error handling
 * @param error
 */
export async function handleError(error: unknown) {
  if (error instanceof HTTPError) {
    const errorData = await error.response.json();
    throw new Error(errorData.message);
  }
  throw error;
}
