/**
 * Get a value from an object using a dot notation path
 * @param obj - Object to get the value from
 * @param path - Dot notation path to the value
 * @param defaultValue - Default value to return if the value at the path is undefined
 * @returns The value at the path
 */
export function getValueByPath<T = any>(
  obj: Record<string, any>,
  path: string,
  defaultValue?: T
): T | undefined {
  const keys = path.split(".");
  let result: any = obj;
  for (const key of keys) {
    if (result == null || typeof result !== "object") {
      return defaultValue;
    }
    result = result[key];
  }

  return result === undefined ? defaultValue : result;
}

/**
 * Flatten an object into a single level of keys
 *
 * @param ob - Object to flatten
 * @returns Flattened object
 */
export function flattenObject(ob: object) {
  const toReturn: object = {};

  for (const i in ob) {
    if (!Object.hasOwn(ob, i)) continue;

    if (typeof ob[i] == "object" && ob[i] !== null) {
      const flatObject = flattenObject(ob[i]);
      for (const x in flatObject) {
        if (!Object.hasOwn(flatObject, x)) continue;

        toReturn[i + "." + x] = flatObject[x];
      }
    } else {
      toReturn[i] = ob[i];
    }
  }
  return toReturn;
}

/**
 * Check if an object is empty
 * @param obj - Object to check
 * @returns True if the object is empty, false otherwise
 */
export function isEmptyObject(obj?: object) {
  if (!obj) return true;
  for (const _ in obj) {
    return false;
  }
  return true;
}
