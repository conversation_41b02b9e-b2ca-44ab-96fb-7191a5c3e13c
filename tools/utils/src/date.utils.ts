import {
  differenceInMinutes,
  format,
  formatISO,
  fromUnixTime,
  isFuture,
  isToday,
  isValid,
  parse
} from "date-fns";
import { formatInTimeZone, toZonedTime } from "date-fns-tz";
// TODO set locale dynamically
import { enCA } from "date-fns/locale/en-CA";

const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

/**
 * Formats unix timestamp to locale date string
 * @param date Date to be formatted
 * @returns formatted date
 */
export function formatUnixTime(date: number): string {
  return format(fromUnixTime(date), "P", { locale: enCA });
}

export function formatDate(date?: Date | string, _format = "P", preserveTimezone = false): string {
  if (!date) return "";

  if (preserveTimezone) {
    return formatInTimeZone(date, "UTC", _format, { locale: enCA });
  }

  return format(date, _format, { locale: enCA });
}

export function formatDateTime(date?: Date | string, _format = "Pp", preserveTimezone = false) {
  if (!date) return "";

  if (preserveTimezone) {
    return formatInTimeZone(date, "UTC", _format, { locale: enCA });
  }

  return format(date, _format, { locale: enCA });
}

export function isoDateToString(date?: Date | string, representation: "complete" | "date" | "time" = "date") {
  return date ? formatISO(date, { representation }) : "";
}

export function localDateToUtc(date?: string, tz?: string) {
  return date ? toZonedTime(date, tz ?? timezone).toISOString() : "";
}

export function localDateToTimezone(date: Date, tz?: string, _format = "dd MMM yyyy HH:mm") {
  return date ? formatInTimeZone(date, tz ?? timezone, _format, { locale: enCA }) : "";
}

export function isValidDate(date: string) {
  return isValid(new Date(date));
}

export function dateInFuture(date: string) {
  return isFuture(new Date(date));
}

export function dateIsToday(date: string) {
  return isToday(new Date(date));
}

export function convertTimeToDate(time: string, _format = "HH:mm") {
  return parse(time, _format, new Date());
}

export function minuteDifference(date1: Date, date2: Date) {
  return differenceInMinutes(date1, date2);
}
