import { Currency } from "./types.utils";

/**
 * Formats currency value to locale string
 * @param value - value to format
 * @param {Currency} currency Currency to be formatted
 * @returns formatted Currency
 */
export function formatCurrency(
  value = 0,
  currency: Currency = Currency.CAD,
  currencyDisplay?: Intl.NumberFormatOptions["currencyDisplay"]
) {
  return value.toLocaleString("en-CA", {
    style: "currency",
    currency: currency?.toUpperCase(),
    maximumFractionDigits: 5,
    currencyDisplay
  });
}

/**
 * Formats number value to locale string
 * @param value - value to format
 * @param {number} [decimal] - Number of decimal places to display
 * @param {string} [style] - Style of the number to be formatted
 * @returns formatted number
 */
export function formatNumber(
  value = 0,
  decimal?: number,
  style: "decimal" | "percent" | "currency" = "decimal"
) {
  return value.toLocaleString("en-CA", {
    style,
    maximumFractionDigits: decimal ?? 2,
    ...(decimal && {
      minimumFractionDigits: decimal
    })
  });
}

/**
 * Gets the initials for a name
 * @param {string} name User Name to be extracted
 * @param {number} [maxLength=1] - Max length of initials to be generated
 * @returns Name Initials
 */
export function getNameInitials(name?: string, maxLength = 1) {
  if (!name || name === "") return;

  if (maxLength > 1) {
    const names = name.split(" ");
    const initials = names.map((name) => name[0]).join("");

    return initials;
  }

  return name.charAt(0).toUpperCase();
}
/**
 * Capitalize the first letter of a string
 * @param string Letter to be capitalized
 * @returns Capitalized letter
 */
export function capitalizeFirstLetter(string: string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}
/**
 * Converts a letter to CapitalCase
 * @param letter Letter to be converted
 * @returns CapitalCase Letter
 */
export function capitalizeLetter(letter?: string) {
  return letter
    ? letter
        .split(" ")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ")
    : "";
}
/**
 * Converts words to kebab-case
 * @param word Word to be converted
 * @returns Word converted to kebab-case
 */
export function convertToKebabCase(word: string) {
  return word.toLowerCase().split(" ").join("-");
}
/**
 * Converts words to snake-case
 * @param word Word to be converted
 * @returns Word converted to snake-case
 */
export function convertToSnakeCase(word: string) {
  return word.toUpperCase().split(" ").join("_");
}

/**
 * Converts a kebab-case string to CapitalCase
 * @param kebabCase Word to be converted
 * @returns CapitalCase Word
 */
export function kebabCaseToCapitalize(kebabCase?: string, allCaps?: boolean) {
  return kebabCase
    ? kebabCase
        .split("-")
        .map((word) => (allCaps ? word.toUpperCase() : capitalizeLetter(word)))
        .join(" ")
    : "";
}
/**
 * Converts a snake-case string to CapitalCase
 * @param snakeCase Word to be converted
 * @param {boolean} [preserveCase=false] - Preserve the case of the original string
 * @returns CapitalCase Word
 */
export function snakeCaseToCapitalize(snakeCase: string, preserveCase = false) {
  return snakeCase
    .split("_")
    .map((word) => (preserveCase ? word : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()))
    .join(" ");
}
/**
 * Converts a camelCase string to kebab-case
 * @param camelCase Word to be converted
 * @returns kebab-case Word
 */
export function camelToKebabCase(camelCase?: string) {
  return camelCase
    ? camelCase
        .replace(/([a-z])([A-Z])/g, "$1-$2")
        .replace(/([A-Z])([A-Z][a-z])/g, "$1-$2")
        .toLowerCase()
    : "";
}
/**
 * Converts a pascalCase string to CapitalCase
 * @param pascalCase Word to be converted
 * @returns CapitalCase Word
 */
export function pascalToCapitalize(pascalCase?: string) {
  return pascalCase ? pascalCase.replace(/([A-Z])/g, " $1").trim() : "";
}
/**
 * Converts a boolean value to a word
 * @param value Boolean value to be converted
 * @returns Word
 */
export function booleanToWord(value?: boolean) {
  return value ? "Yes" : "No";
}
