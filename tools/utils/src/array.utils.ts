type SortableItem<T extends string> = {
  [key in T | "name"]: string; // Allows accessing properties dynamically
};
/**
 * Sort array of objects based on custom sort property
 * @param {string[]} sortOrder Custom sort order
 * @param {SortableItem<T>[]} values Array to sort
 * @param {T} sortBy Field to sort
 * @returns Sorted array
 */
export function sortByOrder<T extends string>(sortOrder: string[], values: SortableItem<T>[], sortBy: T) {
  const ordering: Record<string, number> = {} as Record<string, number>; // map for efficient lookup of sortIndex

  for (let i = 0; i < sortOrder.length; i++) ordering[sortOrder[i]] = i;

  values.sort((a, b) => {
    const orderA = ordering[a[sortBy] as T] ?? Infinity;
    const orderB = ordering[b[sortBy] as T] ?? Infinity;

    return orderA - orderB || a.name.localeCompare(b.name);
  });
}
/**
 * Parse json array string to array string
 * @param {string} str String to parse
 * @returns Parsed array string
 */
export function parseArrayString(str?: string) {
  if (!str) return;
  try {
    const parsed = JSON.parse(str);
    return Array.isArray(parsed) ? JSON.stringify(parsed.map(String)) : str;
  } catch (_) {
    return str;
  }
}
