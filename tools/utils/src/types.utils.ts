import { ComponentType } from "react";

export type SelectOption = {
  label?: string;
  value: string | number;
  disabled?: boolean;
};

//#region Utility helpers
/**
 * Partially change the props to optional
 * @param type - original type
 * @param props - props to change
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
/**
 * Partially change the props to required
 * @param type - original type
 * @param props - props to change
 */
export type RequiredBy<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;
/**
 * Change all the props to nullable
 * @param type - original type
 */
export type Nullable<T> = { [K in keyof T]: T[K] | null };
/**
 * Partially change the props to new type
 * @param type - original type
 * @param props - props to change
 * @param newType - new type
 */
export type ChangeProps<T, K extends keyof T, NewType> = {
  [P in keyof T]: P extends K ? NewType : T[P];
};
//#endregion

//#region Table types
type CellValidator = (value: unknown, callback: (valid: boolean) => void) => void;

export type TableRenderer =
  | string
  | ComponentType<{ value: never; row: never }>
  | ComponentType<{ value: never }>;

export type TableSchema<T> = {
  [key in keyof Partial<
    T & {
      delete: T;
    }
  >]: {
    // [prop: string]: {
    header: string;
    style?: string;
    readOnly?: boolean;
    editor?: boolean;
    type?: string;
    source?: Array<string | number>;
    selectOptions?: Array<string | number>;
    width?: number;
    allowInvalid?: boolean;
    allowEmpty?: boolean;
    renderer?: TableRenderer;
    validator?: CellValidator;
    visibleRows?: number;
    sortKey?: string | null;
    disableSort?: boolean;
    visible?: boolean;
  };
};

export type WithAuditNames = {
  createdBy?: {
    name?: string;
  } | null;
  createdByName?: string;
  lastEditedBy?: {
    name?: string;
  } | null;
  lastEditedByName?: string;
};
//#endregion

//#region Common params
export type DetailParams = {
  id?: string | number;
};
export type PaginationParams = {
  page: number;
  limit: number;
};
export type PaginationResponse = {
  skip: number;
  limit: number;
  total?: number;
  // data: T[];
};
export type OrderBy = "asc" | "desc";
export enum SortOrder {
  ASC = "asc",
  DESC = "desc"
}
export type SortParams<T> = {
  sortBy?: keyof T;
  sortOrder?: OrderBy;
};
export type ListParams<T> = Partial<T> & Partial<PaginationParams>;
// & SortParams<T>;
//  & {
//   createdById?: number;
//   lastEditedById?: number;
//   createDateFrom?: string;
//   createDateTo?: string;
//   lastEditDateFrom?: string;
//   lastEditDateTo?: string;
// };
export type BatchParams<T> = {
  create?: T[];
  edit?: T[];
  delete?: number[];
};
//#endregion

export enum Currency {
  AED = "aed",
  AFN = "afn",
  ALL = "all",
  AMD = "amd",
  ANG = "ang",
  AOA = "aoa",
  ARS = "ars",
  AUD = "aud",
  AWG = "awg",
  AZN = "azn",
  BAM = "bam",
  BBD = "bbd",
  BDT = "bdt",
  BGN = "bgn",
  BHD = "bhd",
  BIF = "bif",
  BMD = "bmd",
  BND = "bnd",
  BOB = "bob",
  BOV = "bov",
  BRL = "brl",
  BSD = "bsd",
  BTN = "btn",
  BWP = "bwp",
  BYN = "byn",
  BZD = "bzd",
  CAD = "cad",
  CDF = "cdf",
  CHE = "che",
  CHF = "chf",
  CHW = "chw",
  CLF = "clf",
  CLP = "clp",
  CNY = "cny",
  COP = "cop",
  COU = "cou",
  CRC = "crc",
  CUC = "cuc",
  CUP = "cup",
  CVE = "cve",
  CZK = "czk",
  DJF = "djf",
  DKK = "dkk",
  DOP = "dop",
  DZD = "dzd",
  EGP = "egp",
  ERN = "ern",
  ETB = "etb",
  EUR = "eur",
  FJD = "fjd",
  FKP = "fkp",
  GBP = "gbp",
  GEL = "gel",
  GHS = "ghs",
  GIP = "gip",
  GMD = "gmd",
  GNF = "gnf",
  GTQ = "gtq",
  GYD = "gyd",
  HKD = "hkd",
  HNL = "hnl",
  HRK = "hrk",
  HTG = "htg",
  HUF = "huf",
  IDR = "idr",
  ILS = "ils",
  INR = "inr",
  IQD = "iqd",
  IRR = "irr",
  ISK = "isk",
  JMD = "jmd",
  JOD = "jod",
  JPY = "jpy",
  KES = "kes",
  KGS = "kgs",
  KHR = "khr",
  KMF = "kmf",
  KPW = "kpw",
  KRW = "krw",
  KWD = "kwd",
  KYD = "kyd",
  KZT = "kzt",
  LAK = "lak",
  LBP = "lbp",
  LKR = "lkr",
  LRD = "ldr",
  LSL = "lsl",
  LYD = "lyd",
  MAD = "mad",
  MDL = "mdl",
  MGA = "mga",
  MKD = "mkd",
  MMK = "mmk",
  MNT = "mnt",
  MOP = "mop",
  MRO = "mro",
  MUR = "mur",
  MVR = "mvr",
  MWK = "mwk",
  MXN = "mxn",
  MXV = "mxv",
  MYR = "myr",
  MZN = "mzn",
  NAD = "nad",
  NGN = "ngn",
  NIO = "nio",
  NOK = "nok",
  NPR = "npr",
  NZD = "nzd",
  OMR = "omr",
  PAB = "pab",
  PEN = "pen",
  PGK = "pgk",
  PHP = "php",
  PKR = "pkr",
  PLN = "pln",
  PYG = "pyg",
  QAR = "qar",
  RON = "ron",
  RSD = "rsd",
  RUB = "rub",
  RWF = "rwf",
  SAR = "sar",
  SBD = "sbd",
  SCR = "scr",
  SDG = "sdg",
  SEK = "sek",
  SGD = "sgd",
  SHP = "shp",
  SLL = "sll",
  SOS = "sos",
  SRD = "srd",
  SSP = "ssp",
  STN = "stn",
  SVC = "svc",
  SYP = "syp",
  SZL = "szl",
  THB = "thb",
  TJS = "tjs",
  TMT = "tmt",
  TND = "tnd",
  TOP = "top",
  TRY = "try",
  TTD = "ttd",
  TWD = "twd",
  TZS = "tzs",
  UAH = "uah",
  UGX = "ugx",
  USD = "usd",
  USN = "usn",
  UYI = "uyi",
  UYU = "uyu",
  UZS = "uzs",
  VEF = "vef",
  VND = "vnd",
  VUV = "vuv",
  WST = "wst",
  XAF = "xaf",
  XAG = "xag",
  XAU = "xau",
  XBA = "xba",
  XBB = "xbb",
  XBC = "xbc",
  XBD = "xbd",
  XCD = "xcd",
  XDR = "xdr",
  XOF = "xof",
  XPD = "xpd",
  XPF = "xpf",
  XPT = "xpt",
  XSU = "xsu",
  XTS = "xts",
  XUA = "xua",
  XXX = "xxx",
  YER = "yer",
  ZAR = "zar",
  ZMW = "zmw",
  ZWL = "zwl"
}
