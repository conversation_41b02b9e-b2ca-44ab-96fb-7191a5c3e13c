# Service Dependencies and Scope Analysis

## Executive Summary

This analysis examines 8 services for their scope configuration, constructor dependencies, and REQUEST injection patterns. The services show clear architectural patterns that affect ShipmentService dependency chains.

## Service Analysis Results

### 1. DocumentService
**File Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/document/services/document.service.ts`

**Scope**: `@Injectable()` (Default SINGLETON scope)

**Constructor Dependencies**:
- `ClsService` (via `@Inject(ClsService)`)
- `Repository<Document>` (via `@InjectRepository`)
- `Repository<DocumentValidationError>` (via `@InjectRepository`)
- `DocumentFieldService` (via `@Inject`)
- `Repository<FilePage>` (via `@InjectRepository`)
- `DocumentTypeService` (via `@Inject(forwardRef)`)
- `EventEmitter2` (direct injection)
- `TransactionalEventEmitterService` (via `@Inject`)

**REQUEST Patterns**: ❌ No REQUEST injection, uses ClsService for scoped data

### 2. ComplianceValidationService
**File Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/shipment/services/compliance-validation.service.ts`

**Scope**: `@Injectable()` (Default SINGLETON scope)

**Constructor Dependencies**:
- `Repository<CommercialInvoiceLine>` (via `@InjectRepository`)
- `Repository<CommercialInvoice>` (via `@InjectRepository`)
- `Repository<Shipment>` (via `@InjectRepository`)
- `Repository<Product>` (via `@InjectRepository`)
- `Repository<CanadaTariff>` (via `@InjectRepository`)
- `Repository<Importer>` (via `@InjectRepository`)
- `RuleQueryService` (via `@Inject(forwardRef)`)
- `CommercialInvoiceEnricher` (via `@Inject`)
- `CommercialInvoiceLineEnricher` (via `@Inject`)

**REQUEST Patterns**: ❌ No REQUEST injection

### 3. LocationService
**File Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/location/location.service.ts`

**Scope**: `@Injectable({ scope: Scope.REQUEST })` ⚠️ **REQUEST SCOPED**

**Constructor Dependencies**:
- `Repository<Location>` (via `@InjectRepository`)
- `CountryService` (via `@Inject`)
- `AuthenticatedRequest` (via `@Inject(REQUEST)`) ⚠️ **REQUEST TOKEN**
- `DataSource` (direct injection)

**REQUEST Patterns**: ✅ Uses `this.request?.user` throughout methods

### 4. TradePartnerService
**File Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/trade-partner/trade-partner.service.ts`

**Scope**: `@Injectable({ scope: Scope.REQUEST })` ⚠️ **REQUEST SCOPED**

**Constructor Dependencies**:
- `Repository<TradePartner>` (via `@InjectRepository`)
- `CountryService` (via `@Inject`)
- `AuthenticatedRequest` (via `@Inject(REQUEST)`) ⚠️ **REQUEST TOKEN**
- `DataSource` (direct injection)

**REQUEST Patterns**: ✅ Uses `this.request?.user` throughout methods

### 5. ImporterService
**File Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/importer/importer.service.ts`

**Scope**: `@Injectable({ scope: Scope.REQUEST })` ⚠️ **REQUEST SCOPED**

**Constructor Dependencies**:
- `Repository<Importer>` (via `@InjectRepository`)
- `EmailService` (via `@Inject(forwardRef)`)
- `CandataService` (via `@Inject`)
- `AuthenticatedRequest` (via `@Inject(REQUEST)`) ⚠️ **REQUEST TOKEN**
- `BaseImporterService` (via `@Inject`)

**REQUEST Patterns**: ✅ Uses `this.request?.user` throughout methods

### 6. CandataService
**File Location**: `/home/<USER>/dev/Claro/libraries/nest-modules/src/candata/candata.service.ts`

**Scope**: `@Injectable({ scope: Scope.REQUEST })` ⚠️ **REQUEST SCOPED**

**Constructor Dependencies**:
- `CandataModuleOptions` (via `@Inject(CANDATA_MODULE_OPTIONS)`)

**REQUEST Patterns**: ❌ No REQUEST injection despite REQUEST scope

### 7. ShipmentEnricher
**File Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/shipment/enrichers/shipment.enricher.ts`

**Scope**: `@Injectable()` (Default SINGLETON scope)

**Constructor Dependencies**:
- `ShipmentCommercialInvoiceService` (via `@Inject`)

**REQUEST Patterns**: ❌ No REQUEST injection

### 8. TransactionalEventEmitterService
**File Location**: `/home/<USER>/dev/Claro/libraries/nest-modules/src/transactional-event-emitter/services/transactional-event-emitter.service.ts`

**Scope**: `@Injectable()` (Default SINGLETON scope)

**Constructor Dependencies**:
- `Repository<TransactionalEvent>` (via `@InjectRepository`)
- `EventEmitter2` (direct injection)

**REQUEST Patterns**: ❌ No REQUEST injection

## Dependency Chain Analysis for ShipmentService

### Critical Scope Conflicts

**REQUEST-scoped services that ShipmentService likely depends on**:
1. **LocationService** - REQUEST scoped, uses REQUEST token
2. **TradePartnerService** - REQUEST scoped, uses REQUEST token  
3. **ImporterService** - REQUEST scoped, uses REQUEST token
4. **CandataService** - REQUEST scoped (but doesn't use REQUEST token)

**SINGLETON services**:
1. **DocumentService** - SINGLETON, uses ClsService for scoped data
2. **ComplianceValidationService** - SINGLETON
3. **ShipmentEnricher** - SINGLETON
4. **TransactionalEventEmitterService** - SINGLETON

### Architectural Patterns Observed

#### Pattern 1: REQUEST Scope + REQUEST Token
Services that need user context for authorization:
- LocationService
- TradePartnerService  
- ImporterService

These services use `this.request?.user?.organization?.id` for tenant isolation.

#### Pattern 2: SINGLETON + ClsService
DocumentService uses ClsService instead of REQUEST injection for scoped data access.

#### Pattern 3: REQUEST Scope Without REQUEST Token
CandataService is REQUEST scoped but doesn't inject REQUEST token - likely for other scoping reasons.

### Dependency Graph (Simplified)

```
ShipmentService (ASSUMED REQUEST SCOPED)
├── LocationService (REQUEST) ⚠️
├── TradePartnerService (REQUEST) ⚠️
├── ImporterService (REQUEST) ⚠️
├── CandataService (REQUEST) ⚠️
├── DocumentService (SINGLETON) ❌ CONFLICT
├── ComplianceValidationService (SINGLETON) ❌ CONFLICT  
├── ShipmentEnricher (SINGLETON) ✅
└── TransactionalEventEmitterService (SINGLETON) ✅
```

## Key Findings & Recommendations

### 🚨 Critical Issues

1. **Scope Mixing**: If ShipmentService is REQUEST-scoped and injects SINGLETON services like DocumentService and ComplianceValidationService, this creates scope conflicts.

2. **Inconsistent Patterns**: Some services use REQUEST injection while others use ClsService for the same purpose (user context).

### 💡 Recommendations

1. **Standardize Scoping Strategy**:
   - Either convert all user-context services to use ClsService (like DocumentService)
   - Or ensure consistent REQUEST scoping throughout the dependency chain

2. **Fix DocumentService Scope**:
   - If ShipmentService needs user context, DocumentService should also be REQUEST-scoped
   - Or ShipmentService should use ClsService instead of REQUEST injection

3. **Review CandataService Scope**:
   - Determine why it's REQUEST-scoped without using REQUEST token
   - Consider if it should be SINGLETON instead

### 📊 Scope Distribution

- **REQUEST Scoped**: 4 services (50%)
- **SINGLETON Scoped**: 4 services (50%)
- **REQUEST Token Users**: 3 services (37.5%)

This mixed scoping creates potential architectural issues that need resolution for proper dependency injection.