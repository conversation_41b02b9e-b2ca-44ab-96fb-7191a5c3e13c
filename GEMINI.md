# Gemini Code Assistant Project Guidelines

This document provides guidelines for using the Gemini Code Assistant in this project. Adhering to these guidelines will ensure that the AI-generated code is consistent with the project's standards and conventions.

## Project Overview

This project is a monorepo managed by **Rush**. It contains several applications and libraries, primarily focused on a web portal and its corresponding API. The project utilizes a modern tech stack with a clear separation between frontend and backend concerns.

### High-Level Architecture

-   **Monorepo:** The project is a Rush-based monorepo. All applications, libraries, and tools are located within this single repository. You can find the project definitions in `rush.json`.
-   **Backend:** The backend is built with **NestJS** (a Node.js framework) and uses **TypeScript**. It exposes a RESTful API for the frontend applications. Key dependencies include TypeORM for database interaction, BullMQ for queueing, and various AWS SDKs.
-   **Frontend:** The frontend applications are built with **React** and **TypeScript**. They use **Vite** for building and development. Key libraries include React Query for data fetching, MobX for state management, and Tailwind CSS for styling.
-   **Cursor Rules:** The `.cursor/rules` directory contains a set of markdown files (`.mdc`) that provide detailed documentation on the project's architecture, coding standards, and feature systems. When you need to understand a specific part of the codebase, you should consult these files. You can start by reading `.cursor/rules/index.mdc` to get an overview of the available rules. Use the `read_file` tool to access these files.

## Rush Monorepo

This is a Rush monorepo. Here are some key points to remember:

-   **Installation:** Use `rush install` to install all dependencies for all projects.
-   **Building:** Use `rush build` to build all projects. You can also build specific projects using `rush build -t <project-name>`.
-   **Running Scripts:** Use `rushx <script-name>` from within a project's directory to run a script from its `package.json`. For example, to start the portal API, navigate to `apps/portal-api` and run `rushx start`.
-   **Project Graph:** Rush manages the dependencies between projects. You can find the project definitions and dependencies in the `rush.json` file.

## Tech Stack

-   **Backend:** NestJS, TypeScript, TypeORM, PostgreSQL, BullMQ, AWS (S3, Lambda, ECS)
-   **Frontend:** React, TypeScript, Vite, React Query, MobX, Tailwind CSS
-   **Testing:** This project uses a set of custom testing scripts for the backend, located in `apps/portal-api/src/core-agent/testing/`. **Do not use Jest for backend testing.** For the frontend, it uses React Testing Library.

## Core-Agent Testing

The backend has a comprehensive set of testing scripts for the core-agent email processing system. These scripts are the primary way to test backend functionality. Before writing any new tests, you should familiarize yourself with these scripts and use them whenever possible.

-   **Location:** `apps/portal-api/src/core-agent/testing/`
-   **Documentation:** Refer to the `.cursor/rules/core-agent-testing.mdc` file for detailed information on how to use the testing scripts. Use the `read_file` tool to access this file.
-   **Key Scripts:**
    -   `test-intent-handlers.js`: Tests all 9 intent handlers with real shipment data.
    -   `test-processor-direct.js`: Tests the `handle-request-message.processor.ts` logic directly.
    -   `e2e-email-pipeline-nestjs.js`: Tests the complete email processing pipeline.
    -   `find-test-shipments.js`: Helps find suitable shipments for testing.

## Coding Standards

-   **General:** Follow the guidelines in `.cursor/rules/coding-standards.mdc`.
-   **Backend:** Adhere to NestJS patterns as described in `.cursor/rules/nestjs-patterns.mdc`.
-   **Frontend:** Follow the frontend architecture guidelines in `.cursor/rules/frontend-architecture.mdc`.

## Key Commands

-   **Install dependencies:** `rush install`
-   **Build all projects:** `rush build`
-   **Run backend (portal-api):** `cd apps/portal-api && rushx start`
-   **Run frontend (portal):** `cd apps/portal && rushx start`
-   **Run backend tests (intent handlers):** `cd apps/portal-api && node src/core-agent/testing/test-intent-handlers.js`
-   **Run backend tests (e2e):** `cd apps/portal-api && ./src/core-agent/testing/run-e2e-with-logs.sh`
-   **Run frontend tests:** `cd apps/portal && rushx lint` (as per package.json, no explicit test command)
