# Antek Logstics Customs Automation App

## Monorepo built with [<PERSON>ack](https://rushjs.io/pages/intro/get_started/).

## Here are some important commands

### Setup

`npm install -g @microsoft/rush`

### Install npm packages

To install an npm package, please go to the project directory and run:
`rush add -p {package_name} -p {package_name_2}`

> Don't install manually using npm i or yarn add as it will break the project symlink. If you accidentally run it, check this [link](https://rushjs.io/pages/developer/new_developer/)

### Update npm packages

After you update app package.json or adding new app, please run this script on root. It will update the repo package dependency
`rush update`

### Incremental build

To build all project, please run this from the root folder:
`rush build`

### Force all projects to be rebuilt

`rush rebuild`

### Add new project

- On root, open `rush.json`
- Find `projects`
- Put the new project

### Run the project

You can go to the project folder and run `rushx start`

-- OR --

Set the commands on `common/config/rush/command-line.json` and then run `rush {command_name}` from the root folder

I have created a script to run everything by `rush start`. You can also start a specific app by `rush start -t {app_name}`

> Rush check for `start` script on each app package.json

### Deploy API

Run from root:

- `rush update`
- `rush build`
- `rush deploy -s {app_name} -t common/deploy/{app_name} --overwrite`

> It will create a build ready for deploy on `common/deploy` folder.

> The deployment config is on `common/config/rush/deploy-{app_name}.json`

- Go to `tools/infrastructure` and run `cdk deploy`

> [!WARNING]  
> If docker auto deployment is not working, we need to do it manually.

- Go to `common/deploy/{app_name}`

- `aws ecr get-login-password --region ca-central-1 | docker login --username AWS --password-stdin 695133745733.dkr.ecr.ca-central-1.amazonaws.com`

- `aws ecr create-repository --repository-name {app_name} --region ca-central-1` (one time only)

- `docker build -t {app_name} .`

- `docker tag {app_name}:latest 695133745733.dkr.ecr.ca-central-1.amazonaws.com/{app_name}:latest`

- `docker push 695133745733.dkr.ecr.ca-central-1.amazonaws.com/{app_name}:latest`

- Go to `tools/infrastructure` and run `cdk deploy`
