# Context Consolidation Plan: Merging core-agent Context Building into agent-context

## Objective

Eliminate the duplicate context building layer by moving all context generation logic from `core-agent` into `agent-context`, creating a single source of truth for email template context.

## Current State Analysis

### Two-Layer Context Building
1. **Layer 1 (agent-context)**: Builds basic shipment context
2. **Layer 2 (core-agent)**: Adds template-specific context on top

### Code to Consolidate

From `BaseIntentHandler` and handlers:
- `buildValidationIssues()`
- `buildSmartTemplateContext()`
- `buildDocumentStatus()`
- `formatEtaDate()`
- `buildStatusMessage()`
- `buildCadMessage()`
- `buildRnsMessage()`
- `buildRushMessage()`
- All status message constants and mappings

## Step-by-Step Implementation Plan

### Phase 1: Extend agent-context Interfaces

1. **Update Context Type Definition**
   - Add all fields currently built in core-agent to the context interface
   - Include validationIssues, smartTemplateContext, etc.

2. **Extend IContextFormatter Interface**
   - Add methods for all context building currently in BaseIntentHandler
   - Define return types for template-specific context

### Phase 2: Move Context Building Logic

3. **Copy Status Message Constants**
   - Move all constants from core-agent to agent-context
   - Consolidate with existing constants

4. **Implement Context Building in ShipmentServicesAdapter**
   - Move all build methods from BaseIntentHandler
   - Adapt to work with existing context building flow
   - Ensure proper error handling with SafeEvaluationUtil

5. **Update ShipmentContextService**
   - Call new formatting methods in buildContext()
   - Ensure complete context is built in one pass

### Phase 3: Refactor Intent Handlers

6. **Update BaseIntentHandler**
   - Remove all context building methods
   - Update createConsolidatedFragments() to use pre-built context
   - Simplify to just fragment creation

7. **Update Individual Handlers**
   - Remove any context building logic
   - Use context fields directly
   - Update fragment creation to use new context structure

### Phase 4: Testing and Validation

8. **Update Tests**
   - Update context mocks to include all new fields
   - Ensure handlers work with pre-built context
   - Verify no context building happens in handlers

9. **Integration Testing**
   - Test complete email flow
   - Verify all templates render correctly
   - Check for any missing context fields

### Phase 5: Cleanup

10. **Remove Obsolete Code**
    - Delete unused imports
    - Remove old context building methods
    - Clean up any duplicate type definitions

11. **Update Documentation**
    - Update architecture docs
    - Document new context structure
    - Update handler documentation

## Risk Mitigation

1. **Parallel Implementation**: Keep old methods during transition
2. **Feature Flags**: Consider flag to switch between old/new context
3. **Incremental Rollout**: Test with one handler first
4. **Rollback Plan**: Keep backup of original code

## Success Criteria

- [ ] All context building happens in agent-context
- [ ] No context building logic in core-agent handlers
- [ ] All existing email templates work correctly
- [ ] No performance degradation
- [ ] Simplified handler code
- [ ] Single source of truth for context

---

# Implementation Prompt for Coding Agent

## Task: Consolidate Context Building from core-agent into agent-context

### Background

The Claro codebase currently has duplicate context building logic split between two modules:
- `agent-context`: Builds basic shipment context
- `core-agent`: Adds template-specific context on top

This creates unnecessary complexity and maintenance burden. Your task is to consolidate ALL context building into the agent-context module.

### Current Architecture

1. **agent-context/ShipmentContextService** builds initial context using:
   - IShipmentDataProvider (data fetching)
   - IBusinessRuleEvaluator (business rules)
   - IContextFormatter (formatting)

2. **core-agent/BaseIntentHandler** then adds more context:
   - validationIssues
   - smartTemplateContext
   - documentStatus
   - Various message formatting

### Your Mission

Move ALL context building logic from core-agent into agent-context so that handlers receive a complete, ready-to-use context.

### Specific Requirements

#### 1. Analyze Current Context Building

First, thoroughly analyze these files:
- `/apps/portal-api/src/core-agent/handlers/base-intent.handler.ts`
- `/apps/portal-api/src/core-agent/constants/response-messages.constants.ts`
- `/apps/portal-api/src/agent-context/interfaces/shipment-context.interface.ts`
- `/apps/portal-api/src/agent-context/services/shipment-services.adapter.ts`

Identify ALL context building logic that needs to be moved.

#### 2. Extend agent-context Interfaces

Update `/apps/portal-api/src/agent-context/interfaces/shipment-context.interface.ts`:
- Add validationIssues field to context type
- Add smartTemplateContext field
- Add any other fields currently built in handlers

Update `IContextFormatter` interface to include:
```typescript
buildValidationIssues(context: ShipmentContext): ValidationIssues;
buildSmartTemplateContext(context: ShipmentContext): SmartTemplateContext;
buildDocumentStatus(shipment: Shipment): DocumentStatusContext;
// ... other methods
```

#### 3. Move Constants

Copy these constants from core-agent to agent-context:
- Status response messages
- Rush action messages  
- Document blocker reasons
- Any other template-related constants

Place them in `/apps/portal-api/src/agent-context/constants/`

#### 4. Implement Context Building

In `ShipmentServicesAdapter`, implement all the new interface methods:
- Copy logic from BaseIntentHandler's build methods
- Wrap each in SafeEvaluationUtil for error handling
- Ensure all use the same patterns as existing methods

#### 5. Update ShipmentContextService

Modify the `buildContext` method to call all new formatting methods:
```typescript
// After existing context building...
context.validationIssues = this.formatter.buildValidationIssues(context);
context.smartTemplateContext = this.formatter.buildSmartTemplateContext(context);
// etc.
```

#### 6. Refactor BaseIntentHandler

Remove ALL context building methods:
- `buildValidationIssues()`
- `buildSmartTemplateContext()`
- `buildDocumentStatus()`
- etc.

Update `createConsolidatedFragments()` to use pre-built context:
```typescript
// Instead of:
const validationIssues = this.buildValidationIssues(context);

// Use:
const validationIssues = context.validationIssues;
```

#### 7. Update Individual Handlers

Go through each handler in `/apps/portal-api/src/core-agent/handlers/`:
- Remove any local context building
- Update to use context fields directly
- Ensure fragments use new context structure

#### 8. Handle Edge Cases

Pay special attention to:
- Methods that modify context (like refreshing after operations)
- Handlers that build unique context (like GetShipmentStatusHandler)
- Side effects collection
- Service injection into context

#### 9. Testing Approach

After each major change:
1. Run the build: `cd apps/portal-api && rushx build`
2. Check TypeScript: Look for any type errors
3. Test core flows if possible

#### 10. Important Patterns to Maintain

1. **SafeEvaluationUtil**: Wrap all evaluations for error safety
2. **Service Resolution**: Keep the existing pattern for REQUEST-scoped services
3. **Context Injection**: Maintain the _services property pattern
4. **Side Effects**: Ensure fragment context still works

### Expected Outcome

After your changes:
1. ALL context building happens in agent-context
2. Intent handlers just map context to fragments
3. No duplicate context building logic
4. Cleaner, more maintainable code
5. Single source of truth for email context

### Files You'll Primarily Work With

1. `/apps/portal-api/src/agent-context/interfaces/shipment-context.interface.ts`
2. `/apps/portal-api/src/agent-context/services/shipment-services.adapter.ts`
3. `/apps/portal-api/src/agent-context/services/shipment-context.service.ts`
4. `/apps/portal-api/src/core-agent/handlers/base-intent.handler.ts`
5. All handlers in `/apps/portal-api/src/core-agent/handlers/`
6. Constants files in both modules

### Constraints

- Maintain backward compatibility
- Don't break existing functionality
- Keep the same context field names where possible
- Preserve all error handling
- Maintain performance characteristics

### Start Here

1. Begin by reading and understanding the current context building in BaseIntentHandler
2. Map out all the context fields that need to be moved
3. Extend the interfaces in agent-context
4. Move the logic systematically
5. Test frequently

Good luck! This refactoring will significantly improve the codebase by eliminating a major source of duplication and complexity.