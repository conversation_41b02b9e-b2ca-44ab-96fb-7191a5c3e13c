name: Rush Incremental Build Check

on:
  pull_request:
    branches: [development]

jobs:
  rush-incremental-build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Git config user
        uses: snow-actions/git-config-user@v1.0.0
        with:
          name: cicd
          email: <EMAIL>

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Rush Install
        run: node common/scripts/install-run-rush.js install

      # - name: Get base branch commit
      #   id: base
      #   run: |
      #     echo "BASE_SHA=$(git merge-base origin/development HEAD)" >> $GITHUB_OUTPUT

      # - name: Build only changed projects
      #   run: |
      #     echo "Building changed projects from ${{ steps.base.outputs.BASE_SHA }}..."
      #     node common/scripts/install-run-rush.js build --from "${{ steps.base.outputs.BASE_SHA }}"

      - name: Rush Build
        run: node common/scripts/install-run-rush.js build -t tag:merge-check
