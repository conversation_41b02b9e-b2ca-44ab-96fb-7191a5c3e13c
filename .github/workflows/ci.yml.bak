name: CI
on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 2
      - name: Git config user
        uses: snow-actions/git-config-user@v1.0.0
        with:
          name: # Service Account's Name
          email: # Service Account's Email Address
      - uses: actions/setup-node@v3
        with:
          node-version: 16
      - name: Verify Change Logs
        run: node common/scripts/install-run-rush.js change --verify
      - name: Rush Install
        run: node common/scripts/install-run-rush.js install
      - name: Rush rebuild
        run: node common/scripts/install-run-rush.js rebuild --verbose --production
