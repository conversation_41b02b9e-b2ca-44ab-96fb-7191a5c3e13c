name: Deploy Frontend DEV

on:
  push:
    branches: ["staging"]
    paths:
      - apps/portal/**
      - apps/backoffice/**
      - tools/tailwind/**
      - tools/utils/**
      - libraries/ui/**
      - common/config/**
      - .github/workflows/fe-dev.yml
  pull_request:
    branches: ["staging"]
    paths:
      - apps/portal/**
      - apps/backoffice/**
      - tools/tailwind/**
      - tools/utils/**
      - libraries/ui/**
      - common/config/**
      - .github/workflows/fe-dev.yml

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: staging
    permissions:
      id-token: write
      contents: read

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2
      - name: Git config user
        uses: snow-actions/git-config-user@v1.0.0
        with:
          name: cicd
          email: <EMAIL>
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Rush Install
        run: node common/scripts/install-run-rush.js install

      - name: Rush build
        env:
          VITE_APP_PORTAL_API_URL: ${{ vars.VITE_APP_PORTAL_API_URL }}
          VITE_APP_BACKOFFICE_API_URL: ${{ vars.VITE_APP_BACKOFFICE_API_URL }}
          VITE_GOOGLE_ID: ${{ secrets.VITE_GOOGLE_ID }}
          VITE_MAPS_API_KEY: ${{ secrets.VITE_MAPS_API_KEY }}
          VITE_API_TEMPLATE: ${{ secrets.VITE_API_TEMPLATE }}
          VITE_APIFY_API_URL: ${{ vars.VITE_APIFY_API_URL }}
          VITE_APIFY_TOKEN: ${{ secrets.APIFY_TOKEN }}
          VITE_CARM_API_URL: ${{ vars.VITE_CARM_API_URL }}
          VITE_CARM_APP_ID: ${{ secrets.VITE_CARM_APP_ID }}
          VITE_FIREBASE_API_KEY: ${{ secrets.VITE_FIREBASE_API_KEY }}
          VITE_FIREBASE_AUTH_DOMAIN: ${{ vars.VITE_FIREBASE_AUTH_DOMAIN }}
          VITE_FIREBASE_PROJECT_ID: ${{ vars.VITE_FIREBASE_PROJECT_ID }}
          VITE_FIREBASE_STORAGE_BUCKET: ${{ vars.VITE_FIREBASE_STORAGE_BUCKET }}
          VITE_FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.VITE_FIREBASE_MESSAGING_SENDER_ID }}
          VITE_FIREBASE_APP_ID: ${{ secrets.VITE_FIREBASE_APP_ID }}
          VITE_FIREBASE_MEASUREMENT_ID: ${{ secrets.VITE_FIREBASE_MEASUREMENT_ID }}
        run: node common/scripts/install-run-rush.js build -t tag:frontend

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: ${{ secrets.AWS_ASSUME_ROLE }}
          role-session-name: ClaroDevDeploySession

      - name: Sync Portal to S3
        run: aws s3 sync apps/portal/dist s3://${{ vars.AWS_PORTAL_S3_BUCKET }} --acl private --follow-symlinks --delete

      - name: Sync Backoffice to S3
        run: aws s3 sync apps/backoffice/dist s3://${{ vars.AWS_ADMIN_S3_BUCKET }} --acl private --follow-symlinks --delete

      - name: Invalidate Portal CloudFront
        uses: chetan/invalidate-cloudfront-action@v2
        env:
          DISTRIBUTION: ${{ vars.AWS_PORTAL_CLOUDFRONT_DISTRIBUTION_ID }}
          PATHS: "/index.html"

      - name: Invalidate Backoffice CloudFront
        uses: chetan/invalidate-cloudfront-action@v2
        env:
          DISTRIBUTION: ${{ vars.AWS_ADMIN_CLOUDFRONT_DISTRIBUTION_ID }}
          PATHS: "/index.html"

      # - name: Upload storybook to S3
      #   uses: jakejarvis/s3-sync-action@master
      #   with:
      #     args: --acl private --follow-symlinks --delete
      #   env:
      #     SOURCE_DIR: erp-frontend/storybook-static
      #     AWS_S3_BUCKET: ${{ secrets.AWS_STORYBOOK_S3_BUCKET }}
