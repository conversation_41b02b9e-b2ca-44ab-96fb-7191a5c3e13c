name: Deploy Backend DEV

on:
  push:
    branches: ["staging"]
    paths:
      - apps/portal-api/**
      - apps/backoffice-api/**
      - tools/utils/**
      - tools/infrastructure/**
      - libraries/nest-modules/**
      - common/config/**
      - .github/workflows/be-dev.yml
  pull_request:
    branches: ["staging"]
    paths:
      - apps/portal-api/**
      - apps/backoffice-api/**
      - tools/utils/**
      - tools/infrastructure/**
      - libraries/nest-modules/**
      - common/config/**
      - .github/workflows/be-dev.yml
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to deploy to"
        required: true
        default: "staging"
        type: choice
        options:
          - staging

jobs:
  build:
    runs-on: ubuntu-latest
    environment: staging
    permissions:
      id-token: write
      contents: read
    # outputs:
    #   cache-key: ${{ steps.cache-key.outputs.key }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Git config user
        uses: snow-actions/git-config-user@v1.0.0
        with:
          name: cicd
          email: <EMAIL>

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      # - name: Generate cache key
      #   id: cache-key
      #   run: echo "key=deps-${{ hashFiles('common/config/rush/*.json') }}" >> $GITHUB_OUTPUT

      - name: Rush Install
        run: node common/scripts/install-run-rush.js install

      - name: Rush build
        run: node common/scripts/install-run-rush.js build -t tag:backend

      - name: Rush Deploy Portal API
        run: node common/scripts/install-run-rush.js deploy -s portal-api -t common/deploy/portal-api

      - name: Rush Deploy Backoffice API
        run: node common/scripts/install-run-rush.js deploy -s backoffice-api -t common/deploy/backoffice-api

      # - name: Cache build artifacts
      #   uses: actions/cache@v4
      #   with:
      #     path: |
      #       apps/portal-api/dist
      #       apps/backoffice-api/dist
      #       libraries/nest-modules/dist
      #       common/deploy/portal-api
      #       common/deploy/backoffice-api
      #     key: build-${{ github.sha }}

      # - name: Cache Rush dependencies
      #   id: cache-deps
      #   uses: actions/cache@v4
      #   with:
      #     path: |
      #       common/temp
      #       common/deploy
      #       **/.rush/temp
      #       **/node_modules
      #     key: ${{ steps.cache-key.outputs.key }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ vars.AWS_REGION }}
          role-to-assume: ${{ secrets.AWS_ASSUME_ROLE }}
          role-session-name: ClaroDevDeploySession

      - name: Setup and Run Migration with Tunnel
        run: |
          # Start the tunnel in the background
          aws ssm start-session \
            --region ${{ vars.AWS_REGION }} \
            --target ${{ secrets.AWS_BASTION_HOST_ID }} \
            --document-name AWS-StartPortForwardingSessionToRemoteHost \
            --parameters host=${{ secrets.DB_HOST }},portNumber=5432,localPortNumber=6432 &

          # Store the background process ID
          TUNNEL_PID=$!

          # Give the tunnel a moment to establish
          sleep 10

          # Navigate to the deployed portal-api directory
          cd libraries/nest-modules

          # Run the migration using the existing script
          timeout 600 node ../../common/scripts/install-run-rushx.js typeorm:migrate
          MIGRATION_EXIT_CODE=$?

          # Kill the tunnel process
          kill $TUNNEL_PID || true

          # Check migration exit code
          if [ $MIGRATION_EXIT_CODE -eq 124 ]; then
            echo "Migration timed out after 10 minutes"
            exit 1
          elif [ $MIGRATION_EXIT_CODE -ne 0 ]; then
            echo "Migration failed with exit code $MIGRATION_EXIT_CODE"
            exit $MIGRATION_EXIT_CODE
          fi

          echo "Migration completed successfully"
        env:
          NODE_ENV: production
          DB_HOST: localhost
          DB_PORT: 6432
          DB_NAME: ${{ secrets.DB_NAME }}
          DB_USER: ${{ secrets.DB_USER }}
          DB_PASSWORD: ${{ secrets.DB_PASSWORD }}

      - name: Install aws cdk
        run: npm install -g aws-cdk

      - name: Deploy stack with cdk deploy
        working-directory: ./tools/infrastructure
        env:
          ENVIRONMENT: staging
          NODE_ENV: production
          AWS_DEFAULT_REGION: ${{ vars.AWS_REGION }}
          DB_USER: ${{ secrets.DB_USER }}
          DB_NAME: ${{ secrets.DB_NAME }}
          DB_HOST: ${{ secrets.DB_HOST }}
          ACCESS_TOKEN_SECRET: ${{ secrets.ACCESS_TOKEN_SECRET }}
          REFRESH_TOKEN_SECRET: ${{ secrets.REFRESH_TOKEN_SECRET }}
          ACCESS_TOKEN_SECRET_BO: ${{ secrets.ACCESS_TOKEN_SECRET_BO }}
          REFRESH_TOKEN_SECRET_BO: ${{ secrets.REFRESH_TOKEN_SECRET_BO }}
          ACCESS_TOKEN_EXPIRES_IN_SEC: ${{ vars.ACCESS_TOKEN_EXPIRES_IN_SEC }}
          REFRESH_TOKEN_EXPIRES_IN_SEC: ${{ vars.REFRESH_TOKEN_EXPIRES_IN_SEC }}
          REFRESH_TOKEN_GRACE_PERIOD_SEC: ${{ vars.REFRESH_TOKEN_GRACE_PERIOD_SEC }}
          GOOGLE_OAUTH_CLIENT_ID: ${{ secrets.GOOGLE_OAUTH_CLIENT_ID }}
          GOOGLE_OAUTH_CLIENT_SECRET: ${{ secrets.GOOGLE_OAUTH_CLIENT_SECRET }}
          DOCUSIGN_SECRET_KEY: ${{ secrets.DOCUSIGN_SECRET_KEY }}
          DOCUSIGN_INTEGRATION_KEY: ${{ secrets.DOCUSIGN_INTEGRATION_KEY }}
          OAUTH_STATE_SECRET_KEY: ${{ secrets.OAUTH_STATE_SECRET_KEY }}
          DOCUSIGN_POA_TEMPLATE_ID: ${{ vars.DOCUSIGN_POA_TEMPLATE_ID }}
          DOCUSIGN_US_POA_TEMPLATE_ID: ${{ vars.DOCUSIGN_US_POA_TEMPLATE_ID }}
          DOCUSIGN_BASE_URL: ${{ vars.DOCUSIGN_BASE_URL }}
          DOCUSIGN_WEBHOOK_HMAC_KEY: ${{ secrets.DOCUSIGN_WEBHOOK_HMAC_KEY }}
          APIFY_TOKEN: ${{ secrets.APIFY_TOKEN }}
          APIFY_US_TARIFF_STORE_NAME: ${{ vars.APIFY_US_TARIFF_STORE_NAME }}
          APIFY_US_TARIFF_ACTOR_NAME: ${{ vars.APIFY_US_TARIFF_ACTOR_NAME }}
          APIFY_US_TARIFF_ACTOR_ID: ${{ secrets.APIFY_US_TARIFF_ACTOR_ID }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          AWS_S3_REGION: ${{ vars.AWS_REGION }}
          LLAMA_CLOUD_API_KEY: ${{ secrets.LLAMA_CLOUD_API_KEY }}
          DEEPSEEK_API_KEY: ${{ secrets.DEEPSEEK_API_KEY }}
          API_TEMPLATE_API_KEY: ${{ secrets.VITE_API_TEMPLATE }}
          FIREBASE_PROJECT_ID: ${{ vars.VITE_FIREBASE_PROJECT_ID }}
          FIREBASE_PRIVATE_KEY: ${{ secrets.FIREBASE_PRIVATE_KEY }}
          FIREBASE_CLIENT_EMAIL: ${{ secrets.FIREBASE_CLIENT_EMAIL }}
          CARM_APP_ID: ${{ secrets.VITE_CARM_APP_ID }}
          BACKOFFICE_EMAIL_ADDRESS: ${{ vars.BACKOFFICE_EMAIL_ADDRESS }}
          SYSTEM_FROM_EMAIL: ${{ vars.SYSTEM_FROM_EMAIL }}
          PORTAL_FRONTEND_URL: ${{ vars.PORTAL_FRONTEND_URL }}
          AWS_ACM_CERTIFICATE_ARN: ${{ secrets.AWS_ACM_CERTIFICATE_ARN }}
          AWS_ACM_CERTIFICATE_ARN_US: ${{ secrets.AWS_ACM_CERTIFICATE_ARN_US }}
          USAV_CANDATA_BASE_URL: ${{ vars.USAV_CANDATA_BASE_URL }}
          USAV_CANDATA_TOKEN: ${{ secrets.USAV_CANDATA_TOKEN }}
          CLARO_CANDATA_BASE_URL: ${{ vars.CLARO_CANDATA_BASE_URL }}
          CLARO_CANDATA_AUTH_URL: ${{ vars.CLARO_CANDATA_AUTH_URL }}
          CLARO_CANDATA_CLIENT_ID: ${{ secrets.CLARO_CANDATA_CLIENT_ID }}
          CLARO_CANDATA_CLIENT_SECRET: ${{ secrets.CLARO_CANDATA_CLIENT_SECRET }}
          CANDATA_REMOTE_SERVER_APP_ID: ${{ secrets.CANDATA_REMOTE_SERVER_APP_ID}}
        run: cdk deploy --require-approval never

  # migrations:
  #   needs: build
  #   runs-on: ubuntu-latest
  #   environment: staging
  #   permissions:
  #     id-token: write
  #     contents: read
  #   steps:
  #     - uses: actions/checkout@v4
  #       with:
  #         fetch-depth: 2

  #     - name: Set up Node.js
  #       uses: actions/setup-node@v4
  #       with:
  #         node-version: 20

  #     - name: Restore Rush dependencies
  #       uses: actions/cache@v4
  #       with:
  #         path: |
  #           common/temp
  #           common/deploy
  #           **/.rush/temp
  #           **/node_modules
  #         key: ${{ needs.build.outputs.cache-key }}
  #         restore-keys: deps-

  #     - name: Restore build artifacts
  #       uses: actions/cache@v4
  #       with:
  #         path: |
  #           apps/portal-api/dist
  #           apps/backoffice-api/dist
  #           libraries/nest-modules/dist
  #           common/deploy/portal-api
  #           common/deploy/backoffice-api
  #         key: build-${{ github.sha }}

  #     - name: Configure AWS Credentials
  #       uses: aws-actions/configure-aws-credentials@v4
  #       with:
  #         aws-region: ${{ vars.AWS_REGION }}
  #         role-to-assume: ${{ secrets.AWS_ASSUME_ROLE }}
  #         role-session-name: ClaroDevDeploySession

  #     - name: Setup and Run Migration with Tunnel
  #       run: |
  #         # Start the tunnel in the background
  #         aws ssm start-session \
  #           --region ${{ vars.AWS_REGION }} \
  #           --target ${{ secrets.AWS_BASTION_HOST_ID }} \
  #           --document-name AWS-StartPortForwardingSessionToRemoteHost \
  #           --parameters host=${{ secrets.DB_HOST }},portNumber=5432,localPortNumber=6432 &

  #         # Store the background process ID
  #         TUNNEL_PID=$!

  #         # Give the tunnel a moment to establish
  #         sleep 5

  #         # Navigate to the deployed portal-api directory
  #         cd libraries/nest-modules

  #         # Run the migration using the existing script
  #         timeout 600 node ../../common/scripts/install-run-rushx.js typeorm:migrate
  #         MIGRATION_EXIT_CODE=$?

  #         # Kill the tunnel process
  #         kill $TUNNEL_PID || true

  #         # Check migration exit code
  #         if [ $MIGRATION_EXIT_CODE -eq 124 ]; then
  #           echo "Migration timed out after 10 minutes"
  #           exit 1
  #         elif [ $MIGRATION_EXIT_CODE -ne 0 ]; then
  #           echo "Migration failed with exit code $MIGRATION_EXIT_CODE"
  #           exit $MIGRATION_EXIT_CODE
  #         fi

  #         echo "Migration completed successfully"
  #       env:
  #         NODE_ENV: production
  #         DB_HOST: localhost
  #         DB_PORT: 6432
  #         DB_NAME: ${{ secrets.DB_NAME }}
  #         DB_USER: ${{ secrets.DB_USER }}
  #         DB_PASSWORD: ${{ secrets.DB_PASSWORD }}

  # deploy:
  #   needs: [build, migrations]
  #   runs-on: ubuntu-latest
  #   environment: staging
  #   permissions:
  #     id-token: write
  #     contents: read
  #   steps:
  #     - uses: actions/checkout@v4
  #       with:
  #         fetch-depth: 2

  #     - name: Set up Node.js
  #       uses: actions/setup-node@v4
  #       with:
  #         node-version: 20

  #     - name: Restore Rush dependencies
  #       uses: actions/cache@v4
  #       with:
  #         path: |
  #           common/temp
  #           common/deploy
  #           **/.rush/temp
  #           **/node_modules
  #         key: ${{ needs.build.outputs.cache-key }}
  #         restore-keys: deps-

  #     - name: Restore build artifacts
  #       uses: actions/cache@v4
  #       with:
  #         path: |
  #           apps/portal-api/dist
  #           apps/backoffice-api/dist
  #           libraries/nest-modules/dist
  #           common/deploy/portal-api
  #           common/deploy/backoffice-api
  #         key: build-${{ github.sha }}

  #     - name: Install aws cdk
  #       run: npm install -g aws-cdk

  #     - name: Configure AWS Credentials
  #       uses: aws-actions/configure-aws-credentials@v4
  #       with:
  #         aws-region: ${{ vars.AWS_REGION }}
  #         role-to-assume: ${{ secrets.AWS_ASSUME_ROLE }}
  #         role-session-name: ClaroDevDeploySession

  #     - name: Deploy stack with cdk deploy
  #       working-directory: ./tools/infrastructure
  #       env:
  #         AWS_DEFAULT_REGION: ${{ vars.AWS_REGION }}
  #         NODE_ENV: production
  #         DB_USER: ${{ secrets.DB_USER }}
  #         DB_NAME: ${{ secrets.DB_NAME }}
  #         ACCESS_TOKEN_SECRET: ${{ secrets.ACCESS_TOKEN_SECRET }}
  #         REFRESH_TOKEN_SECRET: ${{ secrets.REFRESH_TOKEN_SECRET }}
  #         ACCESS_TOKEN_SECRET_BO: ${{ secrets.ACCESS_TOKEN_SECRET_BO }}
  #         REFRESH_TOKEN_SECRET_BO: ${{ secrets.REFRESH_TOKEN_SECRET_BO }}
  #         ACCESS_TOKEN_EXPIRES_IN_SEC: ${{ secrets.ACCESS_TOKEN_EXPIRES_IN_SEC }}
  #         REFRESH_TOKEN_EXPIRES_IN_SEC: ${{ secrets.REFRESH_TOKEN_EXPIRES_IN_SEC }}
  #         REFRESH_TOKEN_GRACE_PERIOD_SEC: ${{ secrets.REFRESH_TOKEN_GRACE_PERIOD_SEC }}
  #         GOOGLE_OAUTH_CLIENT_ID: ${{ secrets.GOOGLE_OAUTH_CLIENT_ID }}
  #         GOOGLE_OAUTH_CLIENT_SECRET: ${{ secrets.GOOGLE_OAUTH_CLIENT_SECRET }}
  #         DOCUSIGN_SECRET_KEY: ${{ secrets.DOCUSIGN_SECRET_KEY }}
  #         DOCUSIGN_INTEGRATION_KEY: ${{ secrets.DOCUSIGN_INTEGRATION_KEY }}
  #         OAUTH_STATE_SECRET_KEY: ${{ secrets.OAUTH_STATE_SECRET_KEY }}
  #         DOCUSIGN_POA_TEMPLATE_ID: ${{ vars.DOCUSIGN_POA_TEMPLATE_ID }}
  #         DOCUSIGN_WEBHOOK_HMAC_KEY: ${{ secrets.DOCUSIGN_WEBHOOK_HMAC_KEY }}
  #         APIFY_TOKEN: ${{ secrets.APIFY_TOKEN }}
  #         CANDATA_TOKEN: ${{ secrets.CANDATA_TOKEN }}
  #         OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
  #         AWS_S3_REGION: ${{ vars.AWS_REGION }}
  #         LLAMA_CLOUD_API_KEY: ${{ secrets.LLAMA_CLOUD_API_KEY }}
  #         DEEPSEEK_API_KEY: ${{ secrets.DEEPSEEK_API_KEY }}
  #         API_TEMPLATE_API_KEY: ${{ vars.VITE_API_TEMPLATE }}
  #       run: cdk deploy --require-approval never
