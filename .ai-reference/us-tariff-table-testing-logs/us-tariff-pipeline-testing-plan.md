# US Tariff Pipeline Testing Plan

## Overview

This testing plan covers the comprehensive US Tariff data pipeline with 4 key components:
1. **Database schema setup** - Migration scripts for US tariff tables
2. **Apify integration** - Downloads tariff data from Apify actor to XLSX
3. **XLSX processing** - Parse, validate, and prepare data for ingestion
4. **Database ingestion** - 3-step process: Parse → Upsert → Cross-populate

## Testing Phases

### Phase 1: Database Foundation (✅ COMPLETED)
- Migration scripts execution and verification
- Database schema validation
- Table relationships and constraints
- PGA requirement seed data verification

### Phase 2: Apify Integration & Data Flow (🔄 IN PROGRESS)
- Apify configuration and connection testing
- XLSX file download verification
- Standalone ingestion logic testing
- Database ingestion with cross-population
- Data quality validation

### Phase 3: Production Readiness (⏳ PENDING)
- Performance testing under load
- Error handling and recovery testing
- Monitoring and alerting setup
- Production deployment validation

## Critical Testing Priority

**⚠️ IMPORTANT**: Apify integration must be tested first in Phase 2, as all subsequent data processing depends on successfully retrieving XLSX files from the Apify actor.

## 1. Database Schema Setup (Phase 1)

**Purpose**: Verify database schema is correct and migrations run successfully.
**Status**: ✅ COMPLETED - All migrations successful, schema validated

### Check Current Migration Status
```bash
# Navigate to the migration directory
cd libraries/nest-modules

# Check what migrations exist
ls -la src/migrations/*us* | grep -E "(us-tariff|us-pga)"

# Run migrations (if needed)
npm run typeorm:migrate
```

### Key Migration Files to Review
- `1750800000000-CreateUsTariffAndPgaAgencyTables.ts` - Creates us_tariff table
- `1750950000000-CreateUsPgaRequirementTable.ts` - Creates us_pga_requirement table  
- `1751000000000-SeedUsTariffPgaRequirementPivotTable.ts` - Creates pivot table

### Verify Database Schema
```bash
# Check tables exist
psql -U postgres -h localhost -d claro_dev -c "
SELECT table_name, table_schema 
FROM information_schema.tables 
WHERE table_name IN ('us_tariff', 'us_pga_requirement', 'us_tariff_pga_requirement', 'tariff_sync_history');"
```

## 3. XLSX Processing Logic (Phase 2)

**Purpose**: Test the ingestion parsing logic without database dependencies.
**Prerequisite**: ⚠️ Requires XLSX files from successful Apify integration

### Test Data Structure Analysis
```bash
cd apps/portal-api

# Examine the Excel file structure
node scripts/us-tariff/examine-excel-file.js

# Test pure ingestion logic (no database)
node scripts/us-tariff/test-ingestion-logic.js
```

**What to Look For**:
- Proper HTS code validation (10 digits)
- Date parsing working correctly
- PGA codes being extracted
- Census quantities handled properly
- Duty flags (Y/N) converted to boolean

## 4. Database Ingestion Testing (Phase 2)

**Purpose**: Test the complete 3-step ingestion process with database persistence.
**Prerequisites**: ✅ Database schema ready, 📋 XLSX files available, 🔍 Parsing logic validated

### Prerequisites
```bash
# Make sure portal-api is running
cd apps/portal-api
rushx start

# Or build and run the modules
cd libraries/nest-modules
npm run build
```

### Test Direct Ingestion Service
```bash
cd apps/portal-api

# Test the service directly (recommended)
node scripts/us-tariff/test-us-tariff-direct.js
```

### Test HTTP Upload Endpoint
```bash
# Test via HTTP endpoint (requires auth)
node scripts/us-tariff/test-us-tariff-ingestion.js
```

**Expected Results**:
- Step 1: Parse XLSX → Should show "Parsed X rows from XLSX file"
- Step 2: Upsert tariff data → Should show insert/update statistics
- Step 3: Populate pivot table → Should show cross-population statistics

## 2. Apify Integration Testing (Phase 2)

**Purpose**: Test the complete Apify integration pipeline - the critical foundation for all data processing.
**Priority**: 🔥 CRITICAL - Must be tested first as all subsequent steps depend on XLSX retrieval

### Get Authentication Token
```bash
# Get auth token first
curl -X POST "http://localhost:5001/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "loginMethod": "email",
    "email": "<EMAIL>",
    "password": "password"
  }'
```

### Test Apify Sync Endpoint
```bash
# Test with force download
curl -X POST "http://localhost:5001/admin/us-tariffs/sync-from-apify" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"forceDownload": true}'

# Test with specific date
curl -X POST "http://localhost:5001/admin/us-tariffs/sync-from-apify" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"date": "070425", "forceDownload": false}'
```

### Monitor Background Jobs
```bash
# Check BullMQ queue status (if bullmq-board is running)
# Navigate to http://localhost:3000/admin/queues

# Or monitor logs
tail -f apps/portal-api/logs/application.log
```

## 5. Cross-Population Logic Testing (Phase 2)

**Purpose**: Verify the us-tariff to us-tariff-pga-requirement relationship creation.
**Prerequisites**: 🗄️ Database ingestion completed, 📊 US tariff records present

### Database Queries to Verify Cross-Population
```bash
# Check pivot table data
psql -U postgres -h localhost -d claro_dev -c "
SELECT COUNT(*) as total_relationships FROM us_tariff_pga_requirement;

SELECT 
  ut.\"hsCode\",
  ut.\"pgaCodes\",
  COUNT(utpr.id) as relationship_count
FROM us_tariff ut
LEFT JOIN us_tariff_pga_requirement utpr ON ut.id = utpr.\"usTariffId\"
WHERE ut.\"pgaCodes\" IS NOT NULL
GROUP BY ut.\"hsCode\", ut.\"pgaCodes\"
ORDER BY relationship_count DESC
LIMIT 10;
"
```

### Test Cross-Population Manually
```bash
# Query specific tariff relationships
psql -U postgres -h localhost -d claro_dev -c "
SELECT 
  ut.\"hsCode\",
  ut.description,
  ut.\"pgaCodes\",
  pr.\"tariffFlagCode\",
  pr.\"agencyCode\",
  pr.description as pga_description
FROM us_tariff ut
JOIN us_tariff_pga_requirement utpr ON ut.id = utpr.\"usTariffId\"
JOIN us_pga_requirement pr ON utpr.\"usPgaRequirementId\" = pr.id
WHERE ut.\"hsCode\" = '0101210090'
LIMIT 5;
"
```

## 6. End-to-End Pipeline Testing (Phase 2)

**Purpose**: Test the complete workflow from Apify API call to database update.
**Prerequisites**: 🔧 All individual components tested and working

### Complete Pipeline Test
```bash
# 1. Check initial state
psql -U postgres -h localhost -d claro_dev -c "
SELECT COUNT(*) FROM us_tariff;
SELECT COUNT(*) FROM us_tariff_pga_requirement;
SELECT COUNT(*) FROM tariff_sync_history;
"

# 2. Trigger Apify sync
curl -X POST "http://localhost:5001/admin/us-tariffs/sync-from-apify" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"forceDownload": true}'

# 3. Monitor progress
# Check TariffSyncHistory for job status
psql -U postgres -h localhost -d claro_dev -c "
SELECT id, status, \"syncDate\", \"finishDate\", \"errorMessage\" 
FROM tariff_sync_history 
ORDER BY \"syncDate\" DESC 
LIMIT 5;
"

# 4. Verify final state
psql -U postgres -h localhost -d claro_dev -c "
SELECT COUNT(*) FROM us_tariff;
SELECT COUNT(*) FROM us_tariff_pga_requirement;
"
```

## 7. Performance and Error Testing (Phase 3)

**Purpose**: Validate production readiness with load testing and error scenarios.
**Prerequisites**: 🚀 Phase 2 completed successfully, all components functional

### Test Error Scenarios
```bash
# Test with invalid date format
curl -X POST "http://localhost:5001/admin/us-tariffs/sync-from-apify" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"date": "invalid-date"}'

# Test without authentication
curl -X POST "http://localhost:5001/admin/us-tariffs/sync-from-apify" \
  -H "Content-Type: application/json" \
  -d '{"forceDownload": true}'
```

### Monitor Performance
```bash
# Check processing times in logs
grep "ingestion" apps/portal-api/logs/application.log | tail -10

# Check database performance
psql -U postgres -h localhost -d claro_dev -c "
SELECT 
  schemaname, 
  tablename, 
  n_tup_ins as inserts,
  n_tup_upd as updates,
  n_tup_del as deletes
FROM pg_stat_user_tables 
WHERE tablename LIKE '%us_tariff%' OR tablename LIKE '%us_pga%';
"
```

## Phase-by-Phase Verification Checklist

### Phase 1: Database Foundation ✅ COMPLETED
- [x] **Migration Scripts**: All US tariff tables exist with correct schema
- [x] **Schema Validation**: Tables, constraints, and relationships verified
- [x] **PGA Requirements**: Seed data properly loaded
- [x] **Database Operations**: Basic CRUD operations working

### Phase 2: Apify Integration & Data Flow 🔄 IN PROGRESS
- [ ] **Apify Configuration**: Environment variables and credentials verified
- [ ] **Apify Connection**: Actor connection successful
- [ ] **XLSX Download**: Files successfully retrieved from Apify
- [ ] **XLSX Parsing**: Data correctly parsed from Excel format
- [ ] **Database Ingestion**: Records properly inserted/updated in us_tariff table
- [ ] **Cross-Population**: Relationships created in us_tariff_pga_requirement table
- [ ] **Data Quality**: >95% valid HTS codes, >90% complete descriptions
- [ ] **End-to-End**: Complete pipeline from Apify → Database working

### Phase 3: Production Readiness ⏳ PENDING
- [ ] **Performance Testing**: Processing completes within reasonable time
- [ ] **Error Handling**: Proper error messages and rollback behavior
- [ ] **Background Jobs**: BullMQ processor handles jobs correctly
- [ ] **Load Testing**: System handles concurrent requests
- [ ] **Monitoring**: Logging and alerting systems functional
- [ ] **Data Integrity**: No orphaned records or missing relationships

## Troubleshooting Common Issues

### Phase 1 Issues (Database Foundation)
1. **Migration Fails**: Check if tables already exist, verify connection settings
2. **Schema Validation Fails**: Verify migration timestamps, check for constraint conflicts
3. **PGA Seed Data Missing**: Re-run specific seed migration, check for foreign key issues

### Phase 2 Issues (Apify Integration & Data Flow)
4. **Apify Download Fails**: Verify API keys, check actor status, validate credentials
5. **XLSX Files Missing**: Check download directory configuration, verify Apify actor output
6. **Ingestion Fails**: Check file format, verify Excel structure, validate data types
7. **Cross-Population Missing**: Verify PGA requirement seed data exists, check matching logic
8. **Data Quality Issues**: Review HTS code validation, check date parsing, verify PGA extraction

### Phase 3 Issues (Production Readiness)
9. **Performance Problems**: Check database indexes, optimize queries, review background job configuration
10. **Background Jobs Stuck**: Check BullMQ configuration, restart services, verify Redis connection
11. **Error Handling Issues**: Review exception handling, check rollback mechanisms, validate logging

## Current Status and Next Steps

**Phase 1**: ✅ **COMPLETED** - Database foundation successfully established
**Phase 2**: 🔄 **IN PROGRESS** - Focus on Apify integration as critical first step
**Phase 3**: ⏳ **PENDING** - Awaiting Phase 2 completion

**Next Action**: Execute Phase 2 testing with emphasis on Apify integration testing first, as all subsequent data processing depends on successful XLSX file retrieval.
