# US Tariff Pipeline Testing - Phase 2: Apify Integration & Data Ingestion

You are testing a comprehensive US Tariff data pipeline in a Rush monorepo. Phase 1 successfully established the database schema. Now Phase 2 focuses on the complete data flow from Apify retrieval through database ingestion and API functionality.

## Your Current Task: Phase 2 - Test Apify Integration & Data Flow

**Context**: The US Tariff system handles a 4-step pipeline:
1. **Apify downloads** → XLSX files from government data sources
2. **XLSX parsing** → Extract and validate tariff data
3. **Database ingestion** → 3-step process (parse → upsert → cross-populate)
4. **API endpoints** → HTTP interfaces for sync and upload operations

**Critical Priority**: Apify integration must be tested first since all subsequent steps depend on successfully retrieving XLSX files.

## Step 1: Check Current Environment

```bash
# Navigate to portal-api directory
cd /home/<USER>/dev/Claro/apps/portal-api

# Create Phase 2 log directory
mkdir -p /home/<USER>/dev/Claro/.ai-reference/us-tariff-table-testing-logs/prompt2
```

Verify service dependencies are running:
- Redis connection (redis-cli ping)
- PostgreSQL connection (psql test query)
- Build the portal-api application (rushx build)

**Expected Results**:
- <PERSON>is responds with "PONG"
- PostgreSQL connects successfully
- Build completes without errors

**If Dependencies Fail**:
- Redis: Try `redis-server` or `rush services`
- PostgreSQL: Check if running with `sudo systemctl status postgresql`
- Build: Check TypeScript errors or missing dependencies

## Step 2: Locate and Test Apify Configuration

```bash
# Check for Apify environment variables
env | grep -i apify

# Search for Apify references in source code
find src/ -name "*.ts" -o -name "*.js" | xargs grep -l -i "apify" 2>/dev/null

# Locate US Tariff service files
find src/ -name "*us-tariff*" -o -name "*tariff*" | grep -v ".d.ts"
```

**Expected Results**:
- Environment variables like `APIFY_TOKEN` or `APIFY_ACTOR_ID`
- Source files containing Apify integration code
- US Tariff service files in expected locations

**If Configuration Missing**:
- Check `.env.local` or `.env` files
- Look for configuration in environment setup scripts
- Verify Apify credentials are properly configured

## Step 3: Test Apify Actor Connection

Start the portal-api service and test the Apify sync endpoints:

```bash
# Start service for testing
timeout 120s rushx start &
SERVICE_PID=$!
sleep 45

# Test authentication first
curl -X POST "http://localhost:5001/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"loginMethod": "email", "email": "<EMAIL>", "password": "password"}'

# Test Apify sync endpoint (extract token from auth response)
curl -X POST "http://localhost:5001/admin/us-tariffs/sync-from-apify" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"forceDownload": false}'

# Stop service
kill $SERVICE_PID 2>/dev/null || true
```

**Expected Results**:
- Authentication succeeds with valid token
- Sync endpoint returns success response
- Background job is queued for processing

**If Sync Fails**:
- Check if endpoint exists (404 error)
- Verify Apify credentials are configured
- Check if Apify service is properly initialized

## Step 4: Verify XLSX File Download

Check for downloaded XLSX files in common locations:
- Current directory (find . -name "*.xlsx")
- Common download directories (/tmp, downloads, temp, uploads)
- Sample files that might exist for testing

**Expected Results**:
- XLSX files present in download directory
- Files have recent timestamps if sync was successful
- File names match expected pattern

**If No Files Found**:
- Apify download may have failed
- Check if download directory is configured correctly
- Verify Apify actor is actually producing files

## Step 5: Test XLSX Analysis and Standalone Ingestion

Look for and review Excel examination scripts to ensure they will test the data correctly, then run them:
```bash
# Test Excel file examination if script exists
if [ -f "scripts/us-tariff/examine-excel-file.js" ]; then
    node scripts/us-tariff/examine-excel-file.js
fi

# Test standalone ingestion logic (without database)
if [ -f "scripts/us-tariff/test-ingestion-logic.js" ]; then
    node scripts/us-tariff/test-ingestion-logic.js
fi
```

**Expected Results**:
- Excel examination shows file structure, worksheets, column headers
- Parsing logic validates HTS codes (10 digits)
- Date parsing works correctly
- PGA codes are extracted properly

**If Analysis/Ingestion Fails**:
- Script may not exist yet
- XLSX file may not be available
- Script may require specific file path
- Check data validation rules

## Step 6: Test Database Ingestion

Check database state before ingestion, then test direct ingestion:

```bash
# Check current database state
psql -U postgres -h localhost -d claro_dev -c "
SELECT 'us_tariff' as table_name, COUNT(*) as record_count FROM us_tariff
UNION ALL
SELECT 'us_tariff_pga_requirement' as table_name, COUNT(*) as record_count FROM us_tariff_pga_requirement
UNION ALL
SELECT 'tariff_sync_history' as table_name, COUNT(*) as record_count FROM tariff_sync_history
ORDER BY table_name;"

# Test direct ingestion service
if [ -f "scripts/us-tariff/test-us-tariff-direct.js" ]; then
    node scripts/us-tariff/test-us-tariff-direct.js
fi
```

**Expected Results**:
- Step 1: XLSX parsing succeeds
- Step 2: Data upsert to us_tariff table succeeds
- Step 3: Cross-population of pivot table succeeds
- No database constraint violations

**If Ingestion Fails**:
- Check constraint violations (HTS code format, etc.)
- Verify foreign key relationships
- Check if required data is present in XLSX

## Step 7: Verify Cross-Population and Data Quality

Test cross-population relationships and run data quality checks:

```bash
# Check PGA code distribution and relationships
psql -U postgres -h localhost -d claro_dev -c "
SELECT 
    CASE 
        WHEN \"pgaCodes\" IS NULL THEN 'No PGA Codes'
        ELSE 'Has PGA Codes'
    END as pga_status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as percentage
FROM us_tariff 
GROUP BY (\"pgaCodes\" IS NULL);"

# Data quality checks
psql -U postgres -h localhost -d claro_dev -c "
SELECT 
    'Valid HTS Codes (10 digits)' as check_type,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM us_tariff), 2) as percentage
FROM us_tariff 
WHERE LENGTH(\"hsCode\") = 10 AND \"hsCode\" ~ '^[0-9]+$'
UNION ALL
SELECT 
    'Records with Descriptions' as check_type,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM us_tariff), 2) as percentage
FROM us_tariff 
WHERE description IS NOT NULL AND LENGTH(description) > 0;"
```

**Expected Results**:
- Significant portion of records have PGA codes
- Cross-population relationships are created
- >95% of HTS codes are valid (10 digits)
- >90% of records have descriptions

**If Cross-Population Fails**:
- Check if PGA requirement seed data exists
- Verify PGA code matching logic
- Check if pivot table foreign keys are working

## Step 8: Final Verification and Summary

Generate final database counts and create summary:

```bash
# Final database state
psql -U postgres -h localhost -d claro_dev -c "
SELECT 
    'us_tariff' as table_name, 
    COUNT(*) as total_records,
    COUNT(CASE WHEN \"pgaCodes\" IS NOT NULL THEN 1 END) as with_pga_codes,
    COUNT(CASE WHEN \"adDuty\" IS NOT NULL THEN 1 END) as with_ad_duty
FROM us_tariff
UNION ALL
SELECT 
    'us_tariff_pga_requirement' as table_name, 
    COUNT(*) as total_records,
    COUNT(DISTINCT \"usTariffId\") as unique_tariffs,
    COUNT(DISTINCT \"usPgaRequirementId\") as unique_requirements
FROM us_tariff_pga_requirement;"
```

Show sample data to verify quality and create comprehensive summary of results.

## Success Criteria for Phase 2

✅ **Apify connection established** and functional  
✅ **XLSX files downloaded** from Apify actor  
✅ **Data parsing works** with proper validation  
✅ **Database ingestion succeeds** with 3-step process  
✅ **Cross-population logic** creates proper relationships  
✅ **Data quality checks pass** (HTS codes, descriptions, etc.)  
✅ **Performance metrics** within acceptable ranges  

## What to Do Next

**If Phase 2 Succeeds**: 
- Document successful pipeline operation
- Record performance metrics 
- Proceed to Phase 3 (Production Deployment Testing)

**If Phase 2 Fails**:
1. **Identify the failure point** (Apify, parsing, ingestion, etc.)
2. **Check specific error messages** in the logs
3. **Verify configuration** (API keys, database connections, etc.)
4. **Test components individually** before full pipeline
5. **Don't proceed** to Phase 3 until data flow is working

## Critical Notes

- **Apify integration is the foundation** - all other steps depend on it
- **DO NOT** drop existing tables without explicit confirmation
- **ALWAYS test** with transactions (BEGIN/ROLLBACK) when possible
- **BACKUP** database before major operations if this is production data
- **DOCUMENT** any deviations from expected results
- **STOP and report** if you encounter unfamiliar errors rather than guessing

## Environment Context

- **Database**: PostgreSQL (`claro_dev`)
- **API Server**: portal-api (localhost:5001)
- **Redis**: Required for background job processing
- **Apify**: External service for data retrieval
- **Target**: Development environment testing

Execute these steps methodically and report your findings after each major step. If everything passes, we'll move to testing production deployment next.

**NOTE**: Use parallel sub-agents where possible to improve efficiency, but with simpler, smaller steps of instructions and more stringent checks each.
Use 'tee' or '>' or other simple logging tool where necessary to generate logs and report all log filenames so the user can verify your results - this is most important.