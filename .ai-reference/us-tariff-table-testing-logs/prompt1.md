# US Tariff Pipeline Testing - Phase 1: Migration Scripts & Database Schema

  You are testing a comprehensive US Tariff data pipeline in a Rush monorepo. This pipeline handles:
  1. **Apify downloads** → XLSX files
  2. **XLSX ingestion** → Database (3-step: parse → upsert → cross-populate)  
  3. **Migration scripts** → Database schema
  4. **Cross-population** → us-tariff ↔ us-tariff-pga-requirement relationships

  ## Your Current Task: Phase 1 - Verify Database Foundation

  **Context**: Before testing data flow, ensure the database schema is correctly established. The US Tariff system uses 4 main tables:
  - `us_tariff` - Main tariff data (HTS codes, descriptions, dates, PGA codes)
  - `us_pga_requirement` - Partner Government Agency requirements lookup
  - `us_tariff_pga_requirement` - Many-to-many pivot table
  - `tariff_sync_history` - Processing status tracking

  ## Step 1: Check Current Environment

  ```bash
  # Verify you're in the correct directory
  pwd
  # Should show: /path/to/Claro

  # Check if services are running
  netstat -tulpn | grep -E ":5432|:3000|:5001" | head -3
  # Should show PostgreSQL (5432) and potentially API servers

  # Verify database connection
  psql -U postgres -h localhost -d claro_dev -c "SELECT current_database(), current_user;"
  ```

  **Expected Result**: Successfully connects to `claro_dev` database as `postgres` user.

  **If Connection Fails**:
  - Check if PostgreSQL is running: `sudo systemctl status postgresql` or `brew services list | grep postgres`
  - Verify credentials in `.env.local` or environment variables
  - Try: `rush services` to start Docker services if using containerized setup

  ## Step 2: Examine Migration Files

  ```bash
  # Navigate to migration directory
  cd libraries/nest-modules

  # List US Tariff related migrations
  ls -la src/migrations/ | grep -E "(us.?tariff|us.?pga|Create.*Us)" | sort
  ```

  **Expected Files**:
  - `1750800000000-CreateUsTariffAndPgaAgencyTables.ts`
  - `1750950000000-CreateUsPgaRequirementTable.ts` 
  - `1751000000000-SeedUsTariffPgaRequirementPivotTable.ts`

  **If Files Missing**: The migrations may have different timestamps. Look for files containing "tariff" or "pga" in the name.

  ## Step 3: Check Migration Status

  ```bash
  # Check which migrations have run
  psql -U postgres -h localhost -d claro_dev -c "
  SELECT id, timestamp, name 
  FROM migrations 
  WHERE name LIKE '%Us%' OR name LIKE '%us%tariff%' OR name LIKE '%pga%'
  ORDER BY timestamp DESC;"
  ```

  **Expected Result**: You should see migration entries for the US Tariff tables.

  **If No Migrations Found**: This means migrations haven't run yet. Proceed to Step 4.

  ## Step 4: Run Migrations (If Needed)

  ```bash
  # Ensure nest-modules is built first
  npm run build

  # Run all pending migrations
  npm run typeorm:migrate
  ```

  **Expected Output**: 
  ```
  query: SELECT * FROM current_schema()
  query: SELECT * FROM information_schema.tables WHERE table_schema = $1 AND table_name = $2
  Migration CreateUsTariffAndPgaAgencyTables1750800000000 has been executed successfully.
  Migration CreateUsPgaRequirementTable1750950000000 has been executed successfully.
  Migration SeedUsTariffPgaRequirementPivotTable1751000000000 has been executed successfully.
  ```

  **If Migration Fails**:
  1. **Check for existing tables**: Tables might already exist
     ```bash
     psql -U postgres -h localhost -d claro_dev -c "SELECT tablename FROM pg_tables WHERE tablename LIKE '%us_%' ORDER BY tablename;"
     ```
  2. **Check for dependency issues**: Ensure base tables exist
  3. **Manual rollback if needed**: `npm run typeorm:revert`
  4. **Re-run with verbose logging**: `VERBOSE=1 npm run typeorm:migrate`

  ## Step 5: Verify Database Schema

  ```bash
  # Check all US Tariff tables exist
  psql -U postgres -h localhost -d claro_dev -c "
  SELECT 
    table_name,
    table_schema,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count
  FROM information_schema.tables t
  WHERE table_name IN ('us_tariff', 'us_pga_requirement', 'us_tariff_pga_requirement', 'tariff_sync_history')
  ORDER BY table_name;"
  ```

  **Expected Result**: 4 tables with appropriate column counts:
  - `us_tariff` (~16 columns)
  - `us_pga_requirement` (~9 columns) 
  - `us_tariff_pga_requirement` (~7 columns)
  - `tariff_sync_history` (~8 columns)

  ## Step 6: Verify Table Structure & Constraints

  ```bash
  # Check us_tariff table structure
  psql -U postgres -h localhost -d claro_dev -c "
  \d us_tariff;
  SELECT constraint_name, constraint_type 
  FROM information_schema.table_constraints 
  WHERE table_name = 'us_tariff';"

  # Check if PGA requirements are seeded
  psql -U postgres -h localhost -d claro_dev -c "
  SELECT COUNT(*) as pga_requirement_count FROM us_pga_requirement;
  SELECT 'Sample PGA codes:' as label, string_agg(\"tariffFlagCode\", ', ') as codes
  FROM (SELECT \"tariffFlagCode\" FROM us_pga_requirement LIMIT 5) t;"
  ```

  **Expected Results**:
  - `us_tariff` has constraints: `VALID_HTS_CODE`, `UNIQUE_US_HTS_CODE`
  - PGA requirements table has > 0 records (should be ~100+ seeded requirements)
  - Sample PGA codes visible (like 'FD1', 'FD2', etc.)

  **If PGA Requirements Empty**: The seed migration may have failed. Check migration logs or re-run specific migration.

  ## Step 7: Test Basic Data Operations

  ```bash
  # Test inserting a sample tariff record
  psql -U postgres -h localhost -d claro_dev -c "
  BEGIN;
  INSERT INTO us_tariff (\"hsCode\", \"effectiveDate\", \"expiryDate\", description, \"pgaCodes\") 
  VALUES ('1234567890', '2024-01-01', '2024-12-31', 'Test tariff for validation', ARRAY['FD1', 'FD2']);

  SELECT \"hsCode\", description, \"pgaCodes\" FROM us_tariff WHERE \"hsCode\" = '1234567890';
  ROLLBACK;"
  ```

  **Expected Result**: Insert succeeds, data is retrieved correctly, then rolled back.

  **If Insert Fails**: Check constraint violations, data types, or required fields.

  ## Step 8: Verify Foreign Key Relationships

  ```bash
  # Check foreign key relationships work
  psql -U postgres -h localhost -d claro_dev -c "
  SELECT 
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
  FROM information_schema.table_constraints tc
  JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name
  JOIN information_schema.constraint_column_usage ccu ON ccu.constraint_name = tc.constraint_name
  WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name = 'us_tariff_pga_requirement';"
  ```

  **Expected Result**: Shows foreign keys to `us_tariff(id)` and `us_pga_requirement(id)`.

  ## Success Criteria for Phase 1

  ✅ **All tables exist** with correct schema  
  ✅ **Constraints are active** (HTS code validation, uniqueness)  
  ✅ **PGA requirements seeded** with lookup data  
  ✅ **Foreign key relationships** properly established  
  ✅ **Basic CRUD operations** work without errors  

  ## What to Do Next

  **If Phase 1 Succeeds**: Report success and proceed to Phase 2 (XLSX Ingestion Logic Testing)

  **If Phase 1 Fails**: 
  1. **Document the specific error** (copy full error message)
  2. **Check the failure type**:
     - Connection issues → Fix database connection
     - Migration failures → Check for conflicting tables
     - Constraint violations → Review table schema
     - Seed data missing → Re-run specific migrations
  3. **Provide logs** from migration attempts
  4. **Don't proceed** to subsequent phases until database foundation is solid

  ## Critical Notes

  - **DO NOT** drop existing tables without explicit confirmation
  - **ALWAYS test** with transactions (BEGIN/ROLLBACK) when possible
  - **BACKUP** database before major operations if this is production data
  - **DOCUMENT** any deviations from expected results
  - **STOP and report** if you encounter unfamiliar errors rather than guessing

  ## Environment Context

  - **Database**: PostgreSQL (localhost:5432)
  - **Schema**: `claro_dev` database  
  - **Rush Monorepo**: All commands assume you're in the root directory
  - **Migration Tool**: TypeORM with custom npm scripts
  - **Target**: Development environment (not production)

  Execute these steps methodically and report your findings after each major step. If everything passes, we'll move to testing the XLSX ingestion logic next.

  **NOTE**: Use parallel sub-agents where possible to improve efficiency, but with simpler, smaller steps of instructions and more stringent checks each.
  Use 'tee' or '>' or other simple logging tool where necessary to generate logs and report all log filenames so the user can verify your results - this is most important.