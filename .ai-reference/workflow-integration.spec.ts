import { Test, TestingModule } from "@nestjs/testing";
import { TypeOrmModule } from "@nestjs/typeorm";
import * as dotenv from "dotenv";
import * as fs from "fs";
import { LlmLog } from "nest-modules";
import * as path from "path";
import { LlmProviderFactory } from "../../llm/ask-llm/providers/llm-provider.factory";
import { OpenAILlmProvider } from "../../llm/ask-llm/providers/openai-llm-provider";
import { AskLLMService } from "../../llm/ask-llm/services/ask-llm.service";
import { OpenAIEnhancedService } from "../../llm/openai-enhanced.service";
import { AgentFlowEngineModule } from "../agent-flow-engine.module";
import { NodeType } from "../interfaces/types";
import { Edge } from "../models/edge.model";
import { Node } from "../models/node.model";
import { SerializableWorkflowGraph, WorkflowGraph } from "../models/workflow-graph.model";
import { JavaScriptNodeHandler } from "../nodes/javascript-node.handler";
import { LlmNodeHandler } from "../nodes/llm-node.handler";
import { NodeHandlerRegistry } from "../nodes/node-registry";
import { TemplateService } from "../services/template.service";
import { WorkflowExecutor } from "../services/workflow-executor.service";

// Load environment variables from .env.local in portal-api directory
const envPath = path.resolve(process.cwd(), ".env.local");
if (fs.existsSync(envPath)) {
  console.log(`Loading environment from: ${envPath}`);
  dotenv.config({ path: envPath });
} else {
  console.warn(`No .env.local file found at: ${envPath}`);
}

describe("Workflow Integration Test", () => {
  let module: TestingModule;
  let graph: WorkflowGraph;

  // Helper to check if we should run tests with real LLM
  const hasApiKey = !!process.env.OPENAI_API_KEY;

  // Increase timeout for API calls
  jest.setTimeout(30000);

  beforeAll(async () => {
    // Create the test module
    module = await Test.createTestingModule({
      imports: [
        // Minimal TypeORM setup for LlmLog
        TypeOrmModule.forRoot({
          type: "sqlite",
          database: ":memory:",
          entities: [LlmLog],
          synchronize: true
        }),
        // Import the full module with configuration
        AgentFlowEngineModule.register({
          openaiApiKey: process.env.OPENAI_API_KEY || "",
          defaultProvider: "openai",
          model: "gpt-4o-mini",
          temperature: 0.7,
          debug: true,
          store: true,
          system: "You are a helpful assistant for a customs management system."
        })
      ]
    }).compile();

    const intentDetectionNode = new Node({
      id: "intent-detection",
      type: "llm",
      description: "Intent detection node",
      inputMap: {
        instruction: "{{ input.instruction }}"
      },
      outputSchema: {
        intent: z.enum(["query","create", "update", "delete"])
      }
    });
    
    
    // create a new workflow graph
    graph = new WorkflowGraph().fromJSON({
      startNodeId: "start",
      nodes: [JSON.parse(JSON.stringify({
            model: "gpt-4o-mini",
            system:
              "You are a helpful assistant that analyzes instructions and determines their intent. Return only the intent as a single word or short phrase.",
            prompt: "Determine the intent of the following instruction: {{ instruction }}.",
            temperature: 0.1
          },
          outputSchemaMap: {})]
        }
      ],
      edges: []
    });

    console.log(
      hasApiKey
        ? "Found API key - running live integration tests with OpenAI"
        : "No API key found - some tests will be skipped"
    );

    // Create executor with tracker
    workflowExecutor = new WorkflowExecutor(executionTracker);

    // Create the real node registry
    nodeRegistry = new NodeHandlerRegistry();

    // Use real template service
    const templateService = new TemplateService();

    // Inject template service into Node class
    Node.templateServiceSingleton = templateService;

    // Create real JavaScriptNodeHandler
    javascriptNodeHandler = new JavaScriptNodeHandler();

    // Register the JavaScript handler
    nodeRegistry.registerHandler("javascript", (node, context) =>
      javascriptNodeHandler.handle(node, context)
    );

    // Only set up LLM handler if we have an API key
    if (hasApiKey) {
      // Create a TemplateManagerService with custom template loading for prompt templates
      const templateManagerService = {
        renderString: (template, variables) => {
          return templateService.renderTemplate(template, variables);
        },
        renderTemplate: (templateName, variables) => {
          if (templateName === "decision-prompt") {
            return `
              # system: 
              You are a customs declaration decision assistant. Make a decision based on the provided information.
              
              # user:
              Request ID: ${variables.requestId || "Unknown"}
              
              Please make a decision on this request. Respond with a JSON object that has:
              - decision: either "approve" or "reject"
              - confidence: a number between 0-1
              - reasoning: brief explanation
            `;
          }
          return templateService.renderTemplate(templateName, variables);
        }
      };

      const mockLlmLogService = {
        createLlmLog: jest.fn().mockResolvedValue({ id: 1 }),
        updateLlmLog: jest.fn().mockResolvedValue(true)
      };

      // Create real OpenAIService with API key
      const openaiService = new OpenAIEnhancedService(
        {
          openaiApiKey: process.env.OPENAI_API_KEY,
          deepseekApiKey: "",
          defaultProvider: "openai"
        },
        mockLlmLogService as any
      );

      // Create real OpenAIProvider
      const openaiProvider = new OpenAILlmProvider(openaiService);

      // Create real ProviderFactory
      const providerFactory = new LlmProviderFactory(
        {
          openaiApiKey: process.env.OPENAI_API_KEY,
          deepseekApiKey: "",
          defaultProvider: "openai"
        },
        [{ name: "openai", provider: openaiProvider }]
      );

      // Create the AskLLMService with real dependencies
      const askLlmService = new AskLLMService(providerFactory, templateManagerService as any, {
        openaiApiKey: process.env.OPENAI_API_KEY,
        deepseekApiKey: "",
        defaultProvider: "openai"
      });

      // Create real LlmNodeHandler
      llmNodeHandler = new LlmNodeHandler(askLlmService, {
        model: "gpt-4o-mini",
        temperature: 0.7,
        debug: true,
        store: true,
        system: "You are a helpful assistant for a customs management system."
      });

      // Register the LLM handler
      nodeRegistry.registerHandler("llm", (node, context) => llmNodeHandler.handle(node, context));
    } else {
      // If no API key, use a mock LLM handler for the tests that need to run
      const mockLlmHandler = jest.fn().mockImplementation(async () => {
        return {
          raw: "Mock LLM response (no API key available)",
          parsed: {
            decision: "approve",
            confidence: 0.95,
            reasoning: "Mock approval since no API key is available"
          }
        };
      });
      nodeRegistry.registerHandler("llm", mockLlmHandler);
    }

    // Set the registry to be used by Node instances
    Node.setHandlerRegistry(nodeRegistry);
  });

  beforeEach(() => {
    // Reset any state between tests
    jest.clearAllMocks();
  });

  afterAll(async () => {
    await module.close();
  });

  it("should create and execute a workflow with JavaScript and LLM nodes", async () => {
    // Create workflow directly
    const workflow = new WorkflowGraph();

    // Define nodes - JavaScript start node
    const initNode = new Node({
      id: "initialize",
      type: "javascript" as NodeType,
      description: "Initialize workflow",
      contents: {
        code: `
          return {
            requestId: "REQ-12345",
            timestamp: new Date().toISOString(),
            status: "pending"
          };
        `
      }
    });

    // LLM node to make a decision
    const decisionNode = new Node({
      id: "make-decision",
      type: "llm" as NodeType,
      description: "Make a decision",
      contents: {
        promptTemplate: "decision-prompt",
        model: "gpt-4o-mini",
        temperature: 0.2,
        outputSchema: {
          type: "object",
          properties: {
            decision: { type: "string", enum: ["approve", "reject"] },
            confidence: { type: "number", minimum: 0, maximum: 1 },
            reasoning: { type: "string" }
          },
          required: ["decision", "reasoning"]
        }
      },
      inputMap: {
        requestId: "{{ initialize.requestId }}"
      }
    });

    // JavaScript node for finalization
    const finalizeNode = new Node({
      id: "finalize",
      type: "javascript" as NodeType,
      description: "Finalize workflow",
      contents: {
        code: `
          const decision = context["make-decision"]?.parsed?.decision || "unknown";
          return {
            finalStatus: decision === "approve" ? "complete" : "rejected",
            timestamp: new Date().toISOString(),
            complete: decision === "approve",
            decision: decision
          };
        `
      }
    });

    // Define edges
    const initToDecisionEdge = new Edge({
      id: "init-to-decision",
      from: "initialize",
      to: "make-decision",
      condition: "true"
    });

    const decisionToFinalizeEdge = new Edge({
      id: "decision-to-finalize",
      from: "make-decision",
      to: "finalize",
      condition: "true"
    });

    // Build workflow
    workflow.addNode(initNode);
    workflow.addNode(decisionNode);
    workflow.addNode(finalizeNode);
    workflow.addEdge(initToDecisionEdge);
    workflow.addEdge(decisionToFinalizeEdge);
    workflow.setStartNode("initialize");

    // Execute workflow
    const result = await workflowExecutor.executeWorkflow(workflow, {});

    // Verify execution flow
    expect(executionFlow).toContainEqual({ nodeId: "initialize", status: "executing" });
    expect(executionFlow).toContainEqual({ nodeId: "make-decision", status: "executing" });
    expect(executionFlow).toContainEqual({ nodeId: "finalize", status: "executing" });

    // Verify results - using simpler property checks
    expect(result.initialize.requestId).toBe("REQ-12345");
    expect(result.finalize.finalStatus).toBeTruthy();
    expect(typeof result.finalize.complete).toBe("boolean");

    // The parsed data might be directly on the node result or wrapped in a parsed property
    if (result["make-decision"]?.parsed) {
      expect(result["make-decision"].parsed.decision).toBeDefined();
      expect(typeof result["make-decision"].parsed.reasoning).toBe("string");
    } else {
      console.log("make-decision result:", JSON.stringify(result["make-decision"]));
    }

    // Log execution time if using real LLM
    if (hasApiKey) {
      console.log(`LLM Response: ${JSON.stringify(result["make-decision"]?.parsed)}`);
    }
  });

  it("should deserialize and execute a workflow from JSON", async () => {
    // Define a serializable workflow
    const workflowJson: SerializableWorkflowGraph = {
      startNodeId: "initialize",
      nodes: [
        {
          id: "initialize",
          type: "javascript" as NodeType,
          description: "Initialize workflow",
          contents: {
            code: `
              return {
                requestId: "REQ-12345",
                timestamp: new Date().toISOString(),
                status: "pending"
              };
            `
          }
        },
        {
          id: "make-decision",
          type: "llm" as NodeType,
          description: "Make a decision",
          contents: {
            promptTemplate: "decision-prompt",
            model: "gpt-4o-mini",
            temperature: 0.2
          },
          inputMap: {
            requestId: "{{ initialize.requestId }}"
          }
        },
        {
          id: "finalize",
          type: "javascript" as NodeType,
          description: "Finalize workflow",
          contents: {
            code: `
              const decision = context["make-decision"]?.parsed?.decision || "unknown";
              return {
                finalStatus: decision === "approve" ? "complete" : "rejected",
                timestamp: new Date().toISOString(),
                complete: decision === "approve"
              };
            `
          }
        }
      ],
      edges: [
        {
          id: "init-to-decision",
          from: "initialize",
          to: "make-decision",
          condition: "true"
        },
        {
          id: "decision-to-finalize",
          from: "make-decision",
          to: "finalize",
          condition: "true"
        }
      ]
    };

    // Deserialize the workflow
    const workflow = WorkflowBuilderService.fromJSON(workflowJson);

    // Verify structure
    expect(workflow).toBeInstanceOf(WorkflowGraph);
    expect(workflow.getStartNodeId()).toBe("initialize");
    expect(workflow.nodes.size).toBe(3);
    expect(workflow.edges.size).toBe(2);

    // Execute workflow
    const result = await workflowExecutor.executeWorkflow(workflow, {});

    // Verify execution flow
    expect(executionFlow).toContainEqual({ nodeId: "initialize", status: "executing" });
    expect(executionFlow).toContainEqual({ nodeId: "make-decision", status: "executing" });
    expect(executionFlow).toContainEqual({ nodeId: "finalize", status: "executing" });

    // Verify results with more flexible assertions for real handlers
    expect(result.initialize.requestId).toBe("REQ-12345");
    expect(result.finalize.finalStatus).toBeTruthy();
    expect(typeof result.finalize.complete).toBe("boolean");

    // Test that we can serialize and deserialize again (round-trip)
    const serialized = workflow.toJSON();
    expect(serialized.nodes.length).toBe(3);
    expect(serialized.edges.length).toBe(2);

    const roundTripWorkflow = WorkflowBuilderService.fromJSON(serialized);
    expect(roundTripWorkflow.nodes.size).toBe(workflow.nodes.size);
    expect(roundTripWorkflow.edges.size).toBe(workflow.edges.size);
  });
});
