#!/usr/bin/env python3
import json
import subprocess
import sys
import re

def extract_with_strings(pdf_path):
    """Extract text using the strings command"""
    try:
        result = subprocess.run(['strings', pdf_path], capture_output=True, text=True)
        return result.stdout
    except Exception as e:
        print(f"Error using strings: {e}")
        return None

def parse_tariff_codes(text):
    """Parse tariff codes from the extracted text"""
    lines = text.split('\n')
    tariff_codes = []
    
    # Look for patterns that match our known codes
    known_codes = ['EP1', 'EP2', 'EP3', 'EP4', 'EP5', 'EP6', 'EP7', 'EP8', 'EH1', 'EH2',
                   'FS3', 'FS4', 'NM1', 'NM2', 'NM3', 'NM4', 'NM5', 'NM6', 'NM8',
                   'DT1', 'DT2', 'AL1', 'AL2', 'FD1', 'FD2', 'FD3', 'FD4',
                   'AM1', 'AM2', 'AM3', 'AM4', 'AM6', 'AM7', 'AM8',
                   'TB1', 'TB2', 'TB3', 'AQ1', 'AQ2', 'AQX', 'OM1', 'OM2',
                   'FW1', 'FW2', 'FW3', 'DE1']
    
    # Create a context window around each known code
    for code in known_codes:
        # Find lines containing this code
        matching_lines = []
        for i, line in enumerate(lines):
            if code in line:
                # Get context (previous and next few lines)
                context_start = max(0, i - 2)
                context_end = min(len(lines), i + 3)
                context = lines[context_start:context_end]
                matching_lines.append({
                    'code': code,
                    'line_number': i,
                    'context': context,
                    'main_line': line
                })
        
        if matching_lines:
            tariff_codes.extend(matching_lines)
    
    return tariff_codes

def main():
    pdf_path = '/home/<USER>/dev/Claro/.ai-reference/us-tariff-data/ACE Agency Tariff Codes_17October2023_508c.pdf'
    
    # Extract text using strings command
    text = extract_with_strings(pdf_path)
    if not text:
        print("Failed to extract text from PDF")
        return
    
    # Parse tariff codes
    tariff_codes = parse_tariff_codes(text)
    
    # Print results
    print(f"Found {len(tariff_codes)} potential tariff code matches:")
    for entry in tariff_codes:
        print(f"\nCode: {entry['code']}")
        print(f"Context around line {entry['line_number']}:")
        for line in entry['context']:
            if line.strip():
                print(f"  {line}")
        print("-" * 80)
    
    # Save raw text for manual inspection
    with open('/home/<USER>/dev/Claro/.ai-reference/extracted_raw_text.txt', 'w') as f:
        f.write(text)
    
    # Save parsed data
    with open('/home/<USER>/dev/Claro/.ai-reference/extracted_codes.json', 'w') as f:
        json.dump(tariff_codes, f, indent=2)
    
    print(f"\nRaw text saved to: /home/<USER>/dev/Claro/.ai-reference/extracted_raw_text.txt")
    print(f"Parsed codes saved to: /home/<USER>/dev/Claro/.ai-reference/extracted_codes.json")

if __name__ == "__main__":
    main()