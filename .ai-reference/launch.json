{"version": "0.2.0", "configurations": [{"name": "Jest: Current File", "type": "node", "request": "launch", "runtimeExecutable": "node", "runtimeArgs": ["--inspect-brk", "--require=ts-node/register"], "program": "${workspaceFolder}/apps/portal-api/node_modules/jest/bin/jest.js", "args": ["${fileBasenameNoExtension}", "--config=${workspaceFolder}/apps/portal-api/jest.config.js", "--runInBand", "--no-cache"], "envFile": "${workspaceFolder}/apps/portal-api/.env.local", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "cwd": "${workspaceFolder}/apps/portal-api", "skipFiles": ["<node_internals>/**"]}, {"name": "Jest CLI", "type": "node", "request": "launch", "runtimeExecutable": "node", "runtimeArgs": ["--inspect-brk"], "program": "${workspaceFolder}/apps/portal-api/node_modules/jest/bin/jest.js", "args": ["${fileBasenameNoExtension}", "--runInBand", "--no-cache", "--<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "--forceExit"], "env": {"NODE_PATH": "${workspaceFolder}/common/temp/node_modules;${workspaceFolder}/apps/portal-api/node_modules"}, "cwd": "${workspaceFolder}/apps/portal-api", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "stopOnEntry": true, "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"]}, {"name": "Debug Integration Tests", "type": "node", "request": "launch", "skipFiles": ["<node_internals>/**"], "runtimeExecutable": "node", "runtimeArgs": ["--inspect-brk", "--require=dotenv/config"], "program": "${workspaceFolder}/apps/portal-api/node_modules/jest/bin/jest.js", "args": ["integration", "--runInBand", "--forceExit"], "env": {"NODE_PATH": "${workspaceFolder}/common/temp/node_modules;${workspaceFolder}/apps/portal-api/node_modules", "DOTENV_CONFIG_PATH": "${workspaceFolder}/apps/portal-api/.env.debug"}, "console": "integratedTerminal", "cwd": "${workspaceFolder}/apps/portal-api", "autoAttachChildProcesses": false}]}