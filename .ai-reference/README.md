# AI Reference Documentation

This folder contains comprehensive documentation specifically created for AI assistants (<PERSON>, <PERSON>, etc.) to provide instant, accurate information about the Claro customs automation platform without requiring extensive code analysis.

## Purpose

The `.ai-reference` folder serves as a centralized knowledge base that:
- Reduces need for time-consuming code searches
- Provides consistent, accurate information about system architecture
- Enables faster response times for common queries
- Maintains single source of truth for AI assistants

## Documentation Organization

### 📁 email-processing-docs/
Core documentation for the email processing and AI system:

1. **ClaroEmailProcessingSystem-doc.md**
   - Overall system architecture and service dependencies
   - Template generation system with fragment-based rendering
   - LLM integration patterns and AI service architecture
   - Queue processing and BullMQ configuration
   - Performance, monitoring, and security considerations

2. **HandleRequestMessageProcessor-doc.md**
   - Central email processing orchestrator deep dive
   - Intent processing system with 11 specialized handlers
   - Fragment-based template rendering with Nunjucks
   - Double-pass intent refinement and priority system
   - Shipment discovery strategies and context building

3. **CoreAgentSystemComponents-doc.md**
   - Additional processors (IdentifyShipment, EventEmitter)
   - 6 specialized AI services (AgentTools, AnswerUserQuery, etc.)
   - Event-driven architecture with saga pattern
   - Complete testing infrastructure documentation
   - 9 Zod schemas for LLM integration

4. **EmailModuleSystem-doc.md**
   - Gmail API integration and OAuth management
   - Email CRUD operations and thread management
   - 16 email templates for automated responses
   - Advanced processors and event listeners
   - Email deletion with cascade handling

5. **ClaroDocumentationRAGAgent-prompt.md**
   - System prompt for the specialized `claro-docs-expert` sub-agent
   - Defines how to use this documentation effectively
   - Response format and synthesis rules

### 📁 agent-context-docs/
Documentation for the agent context system:

- **agent-context-comprehensive-summary.md** - Complete agent context system overview
- **shipment-context-service.md** - Business context building for shipments
- **shipment-services-adapter.md** - Service adaptation patterns
- **compliance-response-adapter.md** - Compliance validation documentation
- **customs-definitions-constants.md** - Customs terminology and definitions
- **customs-status-messages-constants.md** - Status message mappings
- **safe-evaluation-util.md** - Safe expression evaluation utility

### 📁 core-agent-docs/
Comprehensive core agent system documentation:

- **core-agent-comprehensive-summary.md** - System overview and architecture
- **core-agent-processors-comprehensive.md** - All processor documentation
- **core-agent-services-comprehensive.md** - Service layer documentation
- **core-agent-event-system-comprehensive.md** - Event-driven architecture
- **core-agent-testing-infrastructure-comprehensive.md** - Testing utilities
- **core-agent-schema-system-comprehensive.md** - Schema validation system

### 📁 technical-analysis/
Technical analysis and implementation guides:

- **NestJS-Provider-Scopes-Analysis.md** - Service scoping patterns
- **REQUEST-to-SINGLETON-CLS-Implementation-Guide.md** - CLS implementation
- **Service-Scope-Refactoring-Analysis.md** - Refactoring recommendations
- **CLS-Based-Scope-Conversion-Analysis.md** - Scope conversion strategies

## Usage Guidelines

### For AI Assistants

1. **Always check this documentation first** before searching codebase
2. Use the `claro-docs-expert` sub-agent for queries about documented systems
3. Reference specific sections and provide accurate citations
4. Indicate when information requires code analysis beyond documentation

### For Developers

1. Keep documentation updated when making significant changes
2. Add new documentation files here rather than scattered in codebase
3. Use consistent format following existing documentation patterns
4. Include cross-references between related documents

## Documentation Coverage

These documents comprehensively cover:
- ✅ Email processing pipeline (ingestion → analysis → response)
- ✅ All 11 intent handlers and their service dependencies
- ✅ Template system with 50+ Nunjucks templates
- ✅ LLM integration with multiple models and providers
- ✅ Event-driven architecture and saga pattern
- ✅ Testing infrastructure with 20+ utilities
- ✅ Gmail API integration and synchronization
- ✅ Schema validation system with Zod
- ✅ Performance optimization strategies
- ✅ Security and multi-tenant considerations

## Maintenance

- Documentation should be updated when significant changes are made
- New system components should be documented here
- Deprecated features should be marked but not removed immediately
- Version-specific changes should be clearly marked

## Benefits

- 🚀 **Faster onboarding** - Complete system understanding without code diving
- 📊 **Better architecture decisions** - Understand patterns and anti-patterns
- 🔧 **Efficient debugging** - Know where to look for specific functionality
- 🏗️ **Informed development** - Understand impacts before making changes
- 📈 **Performance insights** - Learn about optimization opportunities

---

*This documentation system was created to improve AI assistant effectiveness and reduce repetitive code analysis. For questions about the documentation system itself, see ClaroDocumentationRAGAgent-prompt.md*