// extract_100.js (Updated for { data: [...] } structure)
const { readFile, writeFile } = require("fs").promises; // Use CommonJS require
const { resolve } = require("path");
const { exit } = require("process");

// --- Configuration ---
const INPUT_FILE_ARG_INDEX = 2;
const OUTPUT_FILE_ARG_INDEX = 3;
const MAX_ENTRIES = 100;
// ---------------------

async function extractFirstEntries() {
  // 1. Get file paths from command line arguments
  const inputFile = process.argv[INPUT_FILE_ARG_INDEX];
  const outputFile = process.argv[OUTPUT_FILE_ARG_INDEX];

  if (!inputFile || !outputFile) {
    console.error(`Usage: node ${resolve(__filename)} <input.json> <output.json>`);
    exit(1); // Exit with an error code
  }

  const inputPath = resolve(inputFile); // Get absolute path
  const outputPath = resolve(outputFile); // Get absolute path

  console.log(`Reading from: ${inputPath}`);
  console.log(`Writing to:   ${outputPath}`);

  try {
    // 2. Read the input JSON file
    const rawData = await readFile(inputPath, "utf8");

    // 3. Parse the JSON content
    const fullData = JSON.parse(rawData); // Parsed object, e.g., { data: [...] }

    // 4. *** MODIFIED PART START ***
    // Check if the expected structure { data: [...] } exists
    if (typeof fullData === "object" && fullData !== null && Array.isArray(fullData.data)) {
      // Get the array from the 'data' key
      const itemsArray = fullData.data;

      // Slice the first N entries from the *itemsArray*
      const extractedItems = itemsArray.slice(0, MAX_ENTRIES);

      // Create the output structure, preserving the { data: [...] } format
      const outputData = { data: extractedItems };

      console.log(`Extracted the first ${extractedItems.length} elements from the 'data' array.`);

      // 5. Stringify the *new* output structure
      const outputJson = JSON.stringify(outputData, null, 2); // Pretty-print

      // 6. Write the new JSON string to the output file
      await writeFile(outputPath, outputJson, "utf8");

      console.log(`Successfully saved extracted data to ${outputPath}`);
    } else {
      // Handle cases where the input JSON doesn't match the expected structure
      console.error('Error: Input JSON does not have the expected { "data": [...] } structure.');
      exit(1);
    }
    // *** MODIFIED PART END ***
  } catch (error) {
    console.error("An error occurred:");
    if (error instanceof SyntaxError) {
      console.error(`Error parsing JSON in ${inputPath}: ${error.message}`);
    } else if (error.code === "ENOENT") {
      console.error(`Error: Input file not found at ${inputPath}`);
    } else {
      console.error(error); // Print other errors
    }
    exit(1); // Exit with an error code
  }
}

// --- Run the async function ---
extractFirstEntries();
// ----------------------------
