import { Modu<PERSON> } from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import { TypeOrmModule } from "@nestjs/typeorm";
import { LlmLog } from "nest-modules";
import { AgentFlowEngineModule } from "../apps/portal-api/src/agent-flow-engine/agent-flow-engine.module";
import { WorkflowExecutor } from "../apps/portal-api/src/agent-flow-engine/services/workflow-executor.service";
import { WorkflowBuilder } from "../apps/portal-api/src/agent-flow-engine/utils/workflow-builder";

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: "postgres",
      host: process.env.DB_HOST || "localhost",
      port: parseInt(process.env.DB_PORT || "5432"),
      username: process.env.DB_USER || "postgres",
      password: process.env.DB_PASSWORD || "postgres",
      database: process.env.DB_NAME || "claro",
      entities: [LlmLog],
      synchronize: true, // Be careful with this in production
      logging: true
    }),
    AgentFlowEngineModule.register({
      openaiApiKey: process.env.OPENAI_API_KEY || "",
      deepseekApiKey: process.env.DEEPSEEK_API_KEY || "",
      defaultProvider: "openai",
      model: "gpt-4",
      temperature: 0.7,
      debug: true,
      store: true,
      system: "You are a helpful assistant that gives very short answers."
    })
  ]
})
class AppModule {}

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);

  // Configure TypeOrmModule
  await app.select(TypeOrmModule).init();

  // Get the workflow executor service
  const workflowExecutor = app.get(WorkflowExecutor);

  // Configure the workflow executor with tracking
  workflowExecutor.configure({
    trackingCallback: (status) => {
      console.log("Execution status:", status);
    }
  });

  // Create a workflow builder
  const builder = new WorkflowBuilder();

  // Create a simple LLM node that answers a math question
  const llmNode = builder.addNode({
    id: "math-answer",
    type: "llm",
    description: "Answer a math question",
    contents: {
      prompt: "What is 2 + 2?",
      settings: {
        model: "gpt-4",
        temperature: 0.7
      }
    }
  });

  // Build and validate the workflow graph
  const graph = builder.build();

  // Execute the workflow
  await workflowExecutor.execute({});

  await app.close();
}

bootstrap().catch(console.error);
