/**
 * Main service for the Agent Flow Engine
 */
import { Injectable, Logger } from "@nestjs/common";
import {
  WorkflowExecutor,
  ExecutionTrackingCallback,
} from "./services/workflow-executor.service";
import { WorkflowGraph } from "./models/workflow-graph.model";
import { WorkflowBuilder } from "./utils/workflow-builder";

/**
 * Main service for working with the Agent Flow Engine
 */
@Injectable()
export class AgentFlowEngineService {
  private readonly logger = new Logger(AgentFlowEngineService.name);

  /**
   * Create a new WorkflowBuilder for building workflows
   * @param startNodeId Optional ID for the start node
   * @returns A new workflow builder
   */
  createWorkflowBuilder(startNodeId?: string): WorkflowBuilder {
    return new WorkflowBuilder(startNodeId);
  }

  /**
   * Create a WorkflowExecutor for executing a workflow
   * @param graph The workflow graph to execute
   * @param trackingCallback Optional callback for tracking execution
   * @returns A configured workflow executor
   */
  createExecutor(
    graph: WorkflowGraph,
    trackingCallback?: ExecutionTrackingCallback,
  ): WorkflowExecutor {
    this.logger.log("Creating workflow executor");
    return new WorkflowExecutor({
      graph,
      trackingCallback,
    });
  }

  /**
   * Execute a workflow with the given input data
   * @param graph The workflow graph to execute
   * @param initialData Input data for the workflow
   * @param trackingCallback Optional callback for tracking execution
   * @returns Result of the workflow execution
   */
  async executeWorkflow(
    graph: WorkflowGraph,
    initialData: any,
    trackingCallback?: ExecutionTrackingCallback,
  ): Promise<any> {
    this.logger.log("Executing workflow");
    const executor = this.createExecutor(graph, trackingCallback);
    return await executor.execute(initialData);
  }
}
