/**
 * Utilities for building workflow graphs
 */
import { NodeId, EdgeId } from "../interfaces/types";
import { BaseNode } from "../models/node.model";
import { BaseEdge, EdgeConfig } from "../models/edge.model";
import { WorkflowGraph } from "../models/workflow-graph.model";
import { Injectable, Logger } from "@nestjs/common";

/**
 * Helper class for building workflow graphs
 */
export class WorkflowBuilder {
  private readonly logger = new Logger(WorkflowBuilder.name);
  #graph: WorkflowGraph;
  #nextNodeId = 1;
  #nextEdgeId = 1;

  /**
   * Create a new workflow builder
   * @param startNodeId Optional ID for the start node (default: 'start')
   */
  constructor(startNodeId: NodeId = "start") {
    this.#graph = new WorkflowGraph(startNodeId);
    this.logger.log(
      `Created new workflow builder with start node: ${startNodeId}`,
    );
  }

  /**
   * Add a node to the workflow
   * @param node The node to add
   * @returns This builder for chaining
   */
  addNode(node: BaseNode): WorkflowBuilder {
    this.#graph.addNode(node);
    this.logger.log(`Added node to workflow: ${node.id} (type: ${node.type})`);
    return this;
  }

  /**
   * Add an edge to the workflow
   * @param edge The edge to add
   * @returns This builder for chaining
   */
  addEdge(edge: BaseEdge): WorkflowBuilder {
    this.#graph.addEdge(edge);
    this.logger.log(
      `Added edge to workflow: ${edge.id} (${edge.from} -> ${edge.to})`,
    );
    return this;
  }

  /**
   * Connect two nodes with an edge
   * @param from Source node ID
   * @param to Target node ID
   * @param config Optional edge configuration
   * @returns This builder for chaining
   */
  connect(
    from: NodeId,
    to: NodeId,
    config: Partial<EdgeConfig> = {},
  ): WorkflowBuilder {
    const edgeId = config.id || `edge_${this.#nextEdgeId++}`;
    this.logger.log(`Connecting nodes: ${from} -> ${to} (edge: ${edgeId})`);

    const edge = new BaseEdge({
      id: edgeId,
      from,
      to,
      conditionFn: config.conditionFn,
      description: config.description,
    });

    this.#graph.addEdge(edge);
    return this;
  }

  /**
   * Generate a unique node ID
   * @param prefix Optional prefix for the ID (default: 'node')
   * @returns A unique node ID
   */
  generateNodeId(prefix: string = "node"): NodeId {
    return `${prefix}_${this.#nextNodeId++}`;
  }

  setStart(startNodeId: string): WorkflowBuilder {
    (this.#graph as any)["#startNodeId"] = startNodeId;
    this.logger.log(`Start node updated to: ${startNodeId}`);
    return this;
  }

  /**
   * Get the built workflow graph
   * @returns The completed workflow graph
   */
  build(): WorkflowGraph {
    try {
      this.logger.log("Building and validating workflow");
      // Validate the graph before returning
      this.#graph.validate();

      // Log nodes and edges
      const nodes = this.#graph.nodes;
      const edges = this.#graph.edges;

      this.logger.log(
        `Workflow built with ${nodes.size} nodes and ${edges.size} edges`,
      );
      this.logger.log(`Nodes: ${Array.from(nodes.keys()).join(", ")}`);
      this.logger.log(
        `Edges: ${Array.from(edges.values())
          .map((e) => `${e.from}->${e.to}`)
          .join(", ")}`,
      );

      return this.#graph;
    } catch (error) {
      this.logger.error(
        `Error building workflow: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
