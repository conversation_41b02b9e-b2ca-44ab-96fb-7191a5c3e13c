import { Logger } from "@nestjs/common";
import { AgentContext } from "../models/context.model";

const logger = new Logger("EdgeConditionHelpers");

/**
 * Condition that always returns true, useful as a default
 */
export function alwaysTrue(): (context: AgentContext) => Promise<boolean> {
  return () => Promise.resolve(true);
}

/**
 * Safely get a value from context and parse it if it's a JSON string
 * @param context The agent context
 * @param key The key to get from context
 * @returns The parsed value or the original value if parsing fails
 */
export function getSafeValue(context: AgentContext, key: string): any {
  const rawValue = context.get(key);

  // Return as is if undefined, null or not a string
  if (
    rawValue === undefined ||
    rawValue === null ||
    typeof rawValue !== "string"
  ) {
    return rawValue;
  }

  // Try to parse as JSON if it's a string
  try {
    if (rawValue.trim().startsWith("{") || rawValue.trim().startsWith("[")) {
      return JSON.parse(rawValue);
    }
  } catch (e) {
    logger.warn(`Failed to parse ${key} as JSON: ${e.message}`);
  }

  // Return original value if parsing fails or if not valid JSON
  return rawValue;
}

/**
 * Create a condition function that safely checks for a property value
 * @param key The context key to check
 * @param property The property to check on the value
 * @param expectedValue The expected value of the property
 * @returns A condition function that returns a Promise<boolean>
 */
export function createPropertyCondition(
  key: string,
  property: string,
  expectedValue: any,
): (context: AgentContext) => Promise<boolean> {
  return (context: AgentContext) => {
    const value = getSafeValue(context, key);
    logger.debug(
      `Checking ${key}.${property} === ${expectedValue}: ${value?.[property] === expectedValue}`,
    );
    return Promise.resolve(value && value[property] === expectedValue);
  };
}

/**
 * Create a condition function for checking boolean properties with special handling for default values
 * @param key The context key to check
 * @param property The boolean property to check
 * @returns A condition function that returns a Promise<boolean>
 */
export function createBooleanCondition(
  key: string,
  property: string,
): (context: AgentContext) => Promise<boolean> {
  return (context: AgentContext) => {
    const value = getSafeValue(context, key);
    const propertyValue = value?.[property];

    // Handle explicit boolean values
    if (typeof propertyValue === "boolean") {
      logger.debug(
        `Boolean check ${key}.${property} === true: ${propertyValue === true}`,
      );
      return Promise.resolve(propertyValue === true);
    }

    // Handle string 'true'/'false' values
    if (typeof propertyValue === "string") {
      const normalizedValue = propertyValue.toLowerCase() === "true";
      logger.debug(
        `String boolean check ${key}.${property} === true: ${normalizedValue}`,
      );
      return Promise.resolve(normalizedValue);
    }

    // Handle undefined/missing property - treat as false
    logger.debug(
      `Missing/undefined boolean property ${key}.${property}, treating as false`,
    );
    return Promise.resolve(false);
  };
}
