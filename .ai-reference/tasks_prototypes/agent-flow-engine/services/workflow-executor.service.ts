/**
 * Workflow executor service for the Agent Flow Engine
 */
import { Injectable, Logger } from "@nestjs/common";
import { NodeId } from "../interfaces/types";
import { DataToContextFn, ContextToDataFn } from "../interfaces/functions";
import { AgentContext } from "../models/context.model";
import { WorkflowGraph } from "../models/workflow-graph.model";
import { ExecutionItem } from "../models/execution-item.model";
import { BaseEdge } from "../models/edge.model";

// Define a type for execution tracking callbacks
export type ExecutionTrackingCallback = (
  nodeId: string,
  edgeId?: string,
  status?: string,
) => void;

/**
 * Configuration for the workflow executor
 */
export interface ExecutorConfig {
  /**
   * The workflow graph to execute
   */
  graph: WorkflowGraph;

  /**
   * Optional function to convert input data to context
   */
  dataToContextFn?: DataToContextFn;

  /**
   * Optional function to convert final context to output data
   */
  contextToDataFn?: ContextToDataFn;

  /**
   * Optional callback for tracking execution
   */
  trackingCallback?: ExecutionTrackingCallback;
}

/**
 * Service responsible for executing a workflow
 */
@Injectable()
export class WorkflowExecutor {
  private readonly logger = new Logger(WorkflowExecutor.name);

  #graph: WorkflowGraph;
  #executionQueue: ExecutionItem[] = [];
  #dataToContextFn: DataToContextFn;
  #contextToDataFn: ContextToDataFn;
  #trackingCallback?: ExecutionTrackingCallback;

  /**
   * Create a new workflow executor
   * @param config Configuration for the executor
   */
  constructor(config: ExecutorConfig) {
    this.#graph = config.graph;
    this.#dataToContextFn =
      config.dataToContextFn || ((data) => new AgentContext(data));
    this.#contextToDataFn = config.contextToDataFn || ((context) => context);
    this.#trackingCallback = config.trackingCallback;
  }

  /**
   * Execute the workflow
   * @param initialData Initial data to start workflow with
   * @returns Result of the workflow execution
   */
  async execute(initialData: any): Promise<any> {
    try {
      // Validate the graph before execution
      this.#graph.validate();
      this.logger.log("Starting workflow execution");

      // Convert initial data to context
      const initialContext = this.#dataToContextFn(initialData);
      this.logger.log(`Enqueueing start node: ${this.#graph.getStartNodeId()}`);

      // Store the context separately to use for final result
      let finalContext = initialContext;

      // Enqueue start node
      this.#enqueueNode(this.#graph.getStartNodeId(), initialContext);

      // Process the execution queue
      this.logger.log("Starting queue processing");
      finalContext = await this.#processQueue(finalContext);
      this.logger.log("Queue processing completed");

      // Return the final result
      const result = this.#contextToDataFn(finalContext);
      this.logger.log("Workflow execution completed");
      return result;
    } catch (error) {
      this.logger.error(
        `Error during workflow execution: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process all items in the execution queue
   * @param currentContext The current context
   * @returns The final context after all nodes have been processed
   */
  async #processQueue(currentContext: AgentContext): Promise<AgentContext> {
    try {
      this.logger.log(
        `Processing queue with ${this.#executionQueue.length} items`,
      );

      let lastContext = currentContext;

      while (this.#executionQueue.length > 0) {
        const item = this.#executionQueue.shift();
        if (!item) {
          this.logger.warn("Found null item in queue, skipping");
          continue;
        }

        this.logger.debug(`Executing node: ${item.nodeId}`);

        // Execute the node
        const updatedContext = await this.#executeNode(
          item.nodeId,
          item.context,
        );
        this.logger.debug(`Node ${item.nodeId} executed successfully`);

        // Keep track of the most recent context
        lastContext = updatedContext;

        // Evaluate outgoing edges
        this.logger.debug(`Evaluating edges for node: ${item.nodeId}`);
        await this.#evaluateEdges(item.nodeId, updatedContext);
      }

      this.logger.log("Queue processing complete");
      return lastContext;
    } catch (error) {
      this.logger.error(
        `Error during queue processing: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Add a node to the execution queue
   * @param nodeId ID of the node to execute
   * @param context Context to use for execution
   */
  #enqueueNode(nodeId: NodeId, context: AgentContext): void {
    this.#executionQueue.push(new ExecutionItem(nodeId, context));
  }

  /**
   * Execute a node
   * @param nodeId ID of the node to execute
   * @param context Context to use for execution
   * @returns Updated context after execution
   */
  async #executeNode(
    nodeId: NodeId,
    context: AgentContext,
  ): Promise<AgentContext> {
    try {
      const node = this.#graph.getNode(nodeId);
      this.logger.debug(`Executing node ${nodeId} of type ${node.type}`);

      // Debug log the context before node execution
      this.logger.debug(
        `Context before executing node ${nodeId}: ${JSON.stringify(context)}`,
      );

      const result = await node.execute(context.clone());
      this.logger.debug(`Node ${nodeId} execution completed`);

      // Debug log the node's output that's stored in the context
      this.logger.debug(
        `Node ${nodeId} added to context: ${JSON.stringify(result.get(nodeId))}`,
      );

      // Notify about node execution if tracking callback is provided
      if (this.#trackingCallback) {
        this.#trackingCallback(nodeId, undefined, "success");
      }

      return result;
    } catch (error) {
      this.logger.error(
        `Error executing node ${nodeId}: ${error.message}`,
        error.stack,
      );
      context.metadata.recordError(nodeId, error);

      // Notify about node execution failure if tracking callback is provided
      if (this.#trackingCallback) {
        this.#trackingCallback(nodeId, undefined, "error");
      }

      throw error;
    }
  }

  /**
   * Logs detailed information about edge condition inputs and structure
   * @param edge The edge being evaluated
   * @param context The execution context
   * @private
   */
  #logEdgeConditionDetails(edge: BaseEdge, context: AgentContext): void {
    try {
      // Log the structure of the edge's source node result
      const fromNodeResult = context.get(edge.from);

      this.logger.debug(
        `EDGE CONDITION DETAIL for ${edge.id}:\n` +
          `- Source node: ${edge.from}\n` +
          `- Data path: ${edge.from}.parsed.operation\n` +
          `- Actual data: ${fromNodeResult ? JSON.stringify(fromNodeResult) : "undefined"}\n` +
          `- Parsed data: ${fromNodeResult?.parsed ? JSON.stringify(fromNodeResult.parsed) : "undefined"}`,
      );

      if (edge.from === "operation-detection") {
        const operationValue = fromNodeResult?.parsed?.operation;
        this.logger.debug(
          `Operation detection result: ${operationValue || "undefined"} (type: ${typeof operationValue})`,
        );
      }
    } catch (error) {
      this.logger.warn(
        `Error logging edge condition details: ${error.message}`,
      );
    }
  }

  /**
   * Evaluate outgoing edges from a node
   * @param nodeId ID of the node to evaluate edges from
   * @param context Context to use for evaluation
   */
  async #evaluateEdges(nodeId: NodeId, context: AgentContext): Promise<void> {
    try {
      const outgoingEdges = this.#graph.getOutgoingEdges(nodeId);
      this.logger.debug(
        `Found ${outgoingEdges.length} outgoing edges for node ${nodeId}`,
      );

      // Log the context keys and values for debugging
      const contextKeys = Object.keys(context).filter(
        (key) => key !== "metadata",
      );
      this.logger.debug(
        `Context for edge evaluation has keys: ${JSON.stringify(contextKeys)}`,
      );

      // Log the result of the last node execution
      this.logger.debug(
        `Last node result: ${JSON.stringify(context.get(nodeId))}`,
      );

      for (const edge of outgoingEdges) {
        this.logger.debug(
          `Evaluating edge ${edge.id} from ${edge.from} to ${edge.to}`,
        );

        // Debug the condition inputs more explicitly
        const fromNodeResult = context.get(edge.from);
        this.logger.debug(
          `Edge condition input from "${edge.from}": ${JSON.stringify(fromNodeResult)}`,
        );

        // Log detailed condition information
        this.#logEdgeConditionDetails(edge, context);

        const shouldFollow = await edge.evaluate(context);
        if (shouldFollow) {
          this.logger.debug(
            `Following edge ${edge.id} from ${edge.from} to ${edge.to}`,
          );
          this.#enqueueNode(edge.to, context.clone());

          // Notify about edge traversal if tracking callback is provided
          if (this.#trackingCallback) {
            this.#trackingCallback(edge.from, edge.id, "traversed");
          }
        } else {
          this.logger.debug(
            `Edge ${edge.id} condition evaluated to false, not following`,
          );

          // Notify about edge evaluation if tracking callback is provided
          if (this.#trackingCallback) {
            this.#trackingCallback(edge.from, edge.id, "skipped");
          }
        }
      }

      if (outgoingEdges.length === 0) {
        // Log warning: Node is a dead end
        this.logger.warn(`Node ${nodeId} has no outgoing edges`);
      }
    } catch (error) {
      this.logger.error(
        `Error evaluating edges for node ${nodeId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
