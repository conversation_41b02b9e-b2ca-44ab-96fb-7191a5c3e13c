/**
 * Workflow graph model for the Agent Flow Engine
 */
import { NodeId, EdgeId } from "../interfaces/types";
import { BaseNode } from "./node.model";
import { BaseEdge } from "./edge.model";
import { Logger } from "@nestjs/common";

/**
 * Represents a complete workflow as a graph of nodes and edges
 */
export class WorkflowGraph {
  private readonly logger = new Logger(WorkflowGraph.name);
  #nodes: Map<NodeId, BaseNode>;
  #edges: Map<EdgeId, BaseEdge>;
  #startNodeId: NodeId;

  /**
   * Create a new workflow graph
   * @param startNodeId ID of the node where execution begins
   */
  constructor(startNodeId: NodeId) {
    this.#nodes = new Map();
    this.#edges = new Map();
    this.#startNodeId = startNodeId;
    this.logger.log(`Created new workflow graph with start node ID: ${startNodeId}`);
  }

  /**
   * Add a node to the graph
   * @param node The node to add
   */
  addNode(node: BaseNode): void {
    this.#nodes.set(node.id, node);
    this.logger.log(`Added node to graph: ${node.id} (type: ${node.type})`);
  }

  /**
   * Add an edge to the graph
   * @param edge The edge to add
   */
  addEdge(edge: BaseEdge): void {
    this.#edges.set(edge.id, edge);
    this.logger.log(`Added edge to graph: ${edge.id} (${edge.from} -> ${edge.to})`);
  }

  /**
   * Get a node by ID
   * @param nodeId ID of the node to retrieve
   * @returns The requested node
   * @throws Error if node not found
   */
  getNode(nodeId: NodeId): BaseNode {
    const node = this.#nodes.get(nodeId);
    if (!node) {
      this.logger.error(`Node with ID ${nodeId} not found`);
      throw new Error(`Node with ID ${nodeId} not found`);
    }
    return node;
  }

  /**
   * Get all outgoing edges from a node
   * @param nodeId ID of the node to find edges from
   * @returns Array of edges originating from the node
   */
  getOutgoingEdges(nodeId: NodeId): BaseEdge[] {
    return Array.from(this.#edges.values()).filter((edge) => edge.from === nodeId);
  }

  /**
   * Get the ID of the starting node
   * @returns ID of the workflow's start node
   */
  getStartNodeId(): NodeId {
    return this.#startNodeId;
  }

  /**
   * Validate the graph structure
   * @returns True if graph is valid
   * @throws Error if graph is invalid
   */
  validate(): boolean {
    this.logger.log(`Validating workflow graph`);
    this.logger.log(`Start node ID: ${this.#startNodeId}`);
    this.logger.log(`Total nodes: ${this.#nodes.size}`);
    this.logger.log(`Total edges: ${this.#edges.size}`);

    // Check if start node exists
    if (!this.#nodes.has(this.#startNodeId)) {
      const error = `Start node with ID ${this.#startNodeId} not found`;
      this.logger.error(error);
      throw new Error(error);
    }
    this.logger.log(`Start node validation successful`);

    // Check if all edges reference existing nodes
    for (const edge of this.#edges.values()) {
      if (!this.#nodes.has(edge.from)) {
        const error = `Edge ${edge.id} references non-existent source node ${edge.from}`;
        this.logger.error(error);
        throw new Error(error);
      }
      if (!this.#nodes.has(edge.to)) {
        const error = `Edge ${edge.id} references non-existent target node ${edge.to}`;
        this.logger.error(error);
        throw new Error(error);
      }
    }
    this.logger.log(`Edge validation successful`);

    // Simple cycle detection could be added here
    // For a complete cycle detection, implement depth-first search

    this.logger.log(`Workflow graph validation successful`);
    return true;
  }

  /**
   * Get all nodes in the graph
   */
  get nodes(): Map<NodeId, BaseNode> {
    return new Map(this.#nodes);
  }

  /**
   * Get all edges in the graph
   */
  get edges(): Map<EdgeId, BaseEdge> {
    return new Map(this.#edges);
  }
}
