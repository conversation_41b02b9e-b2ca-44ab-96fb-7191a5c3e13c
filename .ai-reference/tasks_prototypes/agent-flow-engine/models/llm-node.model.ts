import { Injectable, Logger, Optional } from "@nestjs/common";
import { AskLLMService } from "../../llm/services/ask-llm.service";
import { AskLLMParams } from "../../llm/types/ask-llm.types";
import { ErrorHandlerFn } from "../interfaces/functions";
import { NodeId, NodeType } from "../interfaces/types";
import { AgentContext } from "./context.model";
import { BaseNode, NodeConfig } from "./node.model";

/**
 * Configuration for LLM nodes, combining NodeConfig with AskLLMParams
 */
export interface LLMNodeConfig extends Omit<NodeConfig, "processFn"> {
  /**
   * LLM parameters to use for the request
   */
  llmParams: Omit<AskLLMParams, "variables">;

  /**
   * Optional function to map context to node input
   */
  inputMapperFn?: (context: AgentContext) => Record<string, any>;
}

/**
 * Node that uses LLM to process input
 * Automatically configures processFn to use AskLLMService
 */
@Injectable()
export class LLMNode extends BaseNode {
  private readonly logger = new Logger(LLMNode.name);

  /**
   * LLM parameters to use for requests
   */
  private readonly llmParams: Omit<AskLLMParams, "variables">;

  /**
   * Create a new LLM node
   * @param askLLMService Service to use for LLM requests
   * @param config Configuration for the node
   */
  constructor(private readonly askLLMService: AskLLMService, @Optional() config?: LLMNodeConfig) {
    // Use default config if none provided
    const actualConfig = config || {
      id: `llm-${Date.now()}`,
      type: "llm" as NodeType,
      description: "Auto-generated LLM node",
      llmParams: {
        promptTemplate: "default",
        model: "gpt-4"
      }
    };

    // Set up the processFn for the LLM node
    const processFn = async (input: Record<string, any>) => {
      try {
        // Log only the keys of the input object instead of the full object
        this.logger.debug(`Processing LLM node with variables: ${Object.keys(input).join(", ")}`);

        // Create a copy of the LLM parameters to avoid mutating the original
        const llmParamsWithSchema = { ...actualConfig.llmParams };

        // Auto-pass outputSchema to zodSchema if outputSchema exists and zodSchema isn't already set
        if (this.outputSchema && !llmParamsWithSchema.zodSchema) {
          this.logger.debug(`Auto-passing outputSchema to zodSchema for node ${this.id}`);
          llmParamsWithSchema.zodSchema = this.outputSchema;

          // If schemaName isn't set, use the node ID as the schema name
          if (!llmParamsWithSchema.schemaName) {
            llmParamsWithSchema.schemaName = this.id;
          }
        }

        // Call LLM with the variables from input mapper and potentially the outputSchema
        const response = await this.askLLMService.ask({
          ...llmParamsWithSchema,
          variables: input
        });

        // Return only the most relevant part of the response
        // If we have parsed data (from a zodSchema), return that
        if (response.parsed) {
          // Use less verbose logging
          this.logger.debug(`Returning parsed schema data for ${actualConfig.id}`);
          return response.parsed;
        }

        // If no schema was used, return just the message content
        if (typeof response.message?.content === "string") {
          // Use less verbose logging
          this.logger.debug(`Returning message content for ${actualConfig.id}`);
          return response.message.content.trim();
        }

        // Fallback for any other cases (e.g., content is ContentPart[])
        this.logger.debug(`No parseable content found for ${actualConfig.id}`);
        return {};
      } catch (error) {
        this.logger.error(`Error in LLM node processFn: ${error.message}`);
        throw error;
      }
    };

    // Initialize the base node with the config
    super({
      ...actualConfig,
      processFn
    });

    // Store LLM parameters locally
    this.llmParams = actualConfig.llmParams;
  }

  /**
   * Factory method to create an LLM node
   * @param askLLMService Service to use for LLM requests
   * @param id Unique identifier for the node
   * @param type Type of the node
   * @param description Description of the node's purpose
   * @param llmParams LLM parameters to use for the request
   * @param inputMapperFn Function to map context to node input
   * @param errorHandlerFn Function to handle errors during execution
   * @returns A new LLM node
   */
  static create(
    askLLMService: AskLLMService,
    id: NodeId,
    type: NodeType,
    description: string,
    llmParams: Omit<AskLLMParams, "variables">,
    inputMapperFn?: (context: AgentContext) => Record<string, any>,
    errorHandlerFn?: ErrorHandlerFn
  ): LLMNode {
    return new LLMNode(askLLMService, {
      id,
      type,
      description,
      llmParams,
      inputMapperFn,
      errorHandlerFn
    });
  }
}
