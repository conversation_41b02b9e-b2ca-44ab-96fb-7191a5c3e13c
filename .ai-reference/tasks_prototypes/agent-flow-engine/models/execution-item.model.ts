/**
 * Execution item model for the Agent Flow Engine
 */
import { NodeId } from "../interfaces/types";
import { AgentContext } from "./context.model";

/**
 * Represents a node pending execution in the workflow queue
 */
export class ExecutionItem {
  /**
   * ID of the node to execute
   */
  nodeId: NodeId;

  /**
   * Context to use for execution
   */
  context: AgentContext;

  /**
   * Create a new execution item
   * @param nodeId ID of the node to execute
   * @param context Context to use for execution
   */
  constructor(nodeId: NodeId, context: AgentContext) {
    this.nodeId = nodeId;
    this.context = context;
  }
}
