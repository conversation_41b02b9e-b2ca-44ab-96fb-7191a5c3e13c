/**
 * Edge models for the Agent Flow Engine
 */
import { EdgeConditionFn } from "../interfaces/functions";
import { EdgeId, NodeId } from "../interfaces/types";
import { alwaysTrue } from "../utils/edge-condition-helpers";
import { AgentContext } from "./context.model";

/**
 * Configuration for an edge in the workflow
 */
export interface EdgeConfig {
  /**
   * Unique identifier for the edge
   */
  id: EdgeId;

  /**
   * ID of the source node
   */
  from: NodeId;

  /**
   * ID of the target node
   */
  to: NodeId;

  /**
   * Optional function to determine if edge should be followed
   */
  conditionFn?: EdgeConditionFn;

  /**
   * Optional description of the edge's purpose
   */
  description?: string;
}

/**
 * Represents a connection between nodes in a workflow
 */
export class BaseEdge {
  #id: EdgeId;
  #from: NodeId;
  #to: NodeId;
  #conditionFn: EdgeConditionFn;
  #description: string;

  /**
   * Create a new edge
   * @param config Configuration for the edge
   */
  constructor(config: EdgeConfig) {
    this.#id = config.id;
    this.#from = config.from;
    this.#to = config.to;
    this.#conditionFn = config.conditionFn || alwaysTrue();
    this.#description = config.description || "";
  }

  /**
   * Follow this edge if the condition is met
   * @param context The execution context
   * @returns The target node ID if the edge should be followed, null otherwise
   * @throws Error if evaluation fails with a contextualized error message
   */
  async follow(context: AgentContext): Promise<NodeId | null> {
    try {
      if (await this.evaluate(context)) {
        return this.#to;
      }
      return null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to follow edge ${this.#id} (${this.#from} → ${this.#to}): ${errorMessage}`);
    }
  }

  /**
   * Evaluate whether this edge should be followed
   * @param context The execution context
   * @returns True if edge should be followed, false otherwise
   * @throws Error if the condition function fails
   */
  async evaluate(context: AgentContext): Promise<boolean> {
    try {
      return await this.#conditionFn(context);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(
        `Edge condition evaluation failed for edge ${this.#id} (${this.#from} → ${this.#to}): ${errorMessage}`
      );
    }
  }

  /**
   * Get the edge ID
   */
  get id(): EdgeId {
    return this.#id;
  }

  /**
   * Get the source node ID
   */
  get from(): NodeId {
    return this.#from;
  }

  /**
   * Get the target node ID
   */
  get to(): NodeId {
    return this.#to;
  }

  /**
   * Get the edge description
   */
  get description(): string {
    return this.#description;
  }
}

export class Edge extends BaseEdge {
  constructor(config: EdgeConfig) {
    super(config);
  }
}
