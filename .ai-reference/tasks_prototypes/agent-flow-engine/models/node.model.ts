/**
 * Node models for the Agent Flow Engine
 */
import { NodeId, NodeType } from "../interfaces/types";
import {
  InputMapperFn,
  ProcessFn,
  ErrorHandlerFn,
} from "../interfaces/functions";
import { AgentContext } from "./context.model";

/**
 * Configuration for a node in the workflow
 */
export interface NodeConfig {
  /**
   * Unique identifier for the node
   */
  id: NodeId;

  /**
   * Type of the node
   */
  type: NodeType;

  /**
   * Description of the node's purpose
   */
  description: string;

  /**
   * Optional function to map context to node input
   */
  inputMapperFn?: InputMapperFn;

  /**
   * Function that performs the node's main processing
   */
  processFn: ProcessFn;

  /**
   * Optional function to handle errors during execution
   */
  errorHandlerFn?: ErrorHandlerFn;
}

/**
 * Base class for all workflow nodes
 */
export abstract class BaseNode {
  #id: NodeId;
  #type: NodeType;
  #description: string;

  // Change from private to protected for these fields
  protected inputMapperFn: InputMapperFn;
  protected processFn: ProcessFn;
  protected errorHandlerFn: ErrorHandlerFn;

  /**
   * Create a new node
   * @param config Configuration for the node
   */
  constructor(config: NodeConfig) {
    this.#id = config.id;
    this.#type = config.type;
    this.#description = config.description;
    this.inputMapperFn = config.inputMapperFn || ((ctx) => ctx);
    this.processFn = config.processFn;
    this.errorHandlerFn =
      config.errorHandlerFn || this.defaultErrorHandler.bind(this);
  }

  /**
   * Execute this node's processing on the context
   * @param context The execution context
   * @returns The updated context after processing
   * @throws Error if execution fails
   */
  async execute(context: AgentContext): Promise<AgentContext> {
    try {
      // Clone the context to avoid modifying the original
      const clonedContext = context.clone();

      // Map input using inputMapperFn if provided, or use cloned context directly
      const input = this.inputMapperFn
        ? this.inputMapperFn(clonedContext)
        : clonedContext;

      // Process the input
      const output = await this.processFn(input);

      // Store the output directly in the context
      clonedContext.set(this.#id, output);

      return clonedContext;
    } catch (error) {
      // Use errorHandlerFn if provided, otherwise rethrow
      if (this.errorHandlerFn) {
        return this.errorHandlerFn(context, error as Error);
      }
      throw new Error(
        `Error executing node ${this.#id}: ${(error as Error).message}`,
      );
    }
  }

  /**
   * Handle an error that occurred during execution
   * @param context The execution context
   * @param error The error that occurred
   * @returns Updated context after error handling
   */
  protected handleError(context: AgentContext, error: Error): AgentContext {
    return this.errorHandlerFn(context, error);
  }

  /**
   * Default error handler if none provided
   * @param context The execution context
   * @param error The error that occurred
   * @returns Updated context after error handling
   */
  protected defaultErrorHandler(
    context: AgentContext,
    error: Error,
  ): AgentContext {
    context.metadata.recordError(this.#id, error);
    return context;
  }

  /**
   * Get the node ID
   */
  get id(): NodeId {
    return this.#id;
  }

  /**
   * Get the node type
   */
  get type(): NodeType {
    return this.#type;
  }

  /**
   * Get the node description
   */
  get description(): string {
    return this.#description;
  }
}

/**
 * Standard implementation of BaseNode for common use cases
 * Provides a concrete implementation that can be instantiated directly
 */
export class Node extends BaseNode {
  /**
   * Create a new standard node
   * @param config Configuration for the node
   */
  constructor(config: NodeConfig) {
    super(config);
  }
}
