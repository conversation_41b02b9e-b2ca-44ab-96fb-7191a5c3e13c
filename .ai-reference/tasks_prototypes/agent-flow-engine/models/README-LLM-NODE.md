# LLM Node for Agent Flow Engine

This document describes the implementation of the LLM Node for the Agent Flow Engine, which allows easy integration of LLM capabilities into workflows.

## Overview

The LLM Node extends the BaseNode class and provides a convenient way to use the AskLLMService within the Agent Flow Engine. It automatically configures the `processFn` to use the AskLLMService with the provided parameters.

## Features

- Extends BaseNode to integrate with the Agent Flow Engine
- Automatically configures processFn to use AskLLMService
- Supports all AskLLMParams options
- Provides a factory method for easy creation
- Allows custom variable extraction from context

## Usage

### Basic Usage

```typescript
import { LLMNode } from "../models/llm-node.model";
import { AskLLMService } from "../../llm/services/ask-llm.service";
import { z } from "zod";

// Create an LLM node
const llmNode = LLMNode.create(
  askLLMService, // Injected AskLLMService
  "my-llm-node",
  "llm",
  "Node that answers questions using LLM",
  {
    model: "gpt-4o-mini",
    system: "You are a helpful assistant",
    prompt: "Answer the following question: {{question}}",
    temperature: 0.1,
    zodSchema: z.object({ answer: z.string() }),
    schemaName: "answer",
  },
);
```

### With Custom Variable Extraction

```typescript
const llmNode = LLMNode.create(
  askLLMService,
  "my-llm-node",
  "llm",
  "Node that answers questions using LLM",
  {
    model: "gpt-4o-mini",
    system: "You are a helpful assistant",
    prompt: "Answer the following question: {{question}}",
    temperature: 0.1,
  },
  // Custom variables extractor
  (context) => ({
    question: context.input?.question || "What is the meaning of life?",
    user: context.user,
  }),
);
```

### In a Workflow

```typescript
// Create a workflow builder
const builder = new WorkflowBuilder("start");

// Add the LLM node
builder.addNode(llmNode);

// Set the start node
builder.setStartNode("my-llm-node");

// Build the workflow
const workflow = builder.build();

// Create a workflow executor
const executor = new WorkflowExecutor(workflow);

// Execute the workflow
const result = await executor.execute({
  question: "What is the meaning of life?",
});

// Access the result
const llmResponse = result["my-llm-node"];
```

## Configuration

The LLMNodeConfig interface extends NodeConfig and includes the following additional properties:

- `llmParams`: LLM parameters to use for the request (all AskLLMParams except variables)
- `variablesExtractorFn`: Function to extract variables from context (optional)

## Implementation Details

The LLMNode class:

1. Extends BaseNode
2. Takes an AskLLMService and LLMNodeConfig in the constructor
3. Creates a processFn that:
   - Extracts variables from the context using variablesExtractorFn
   - Calls askLLMService.ask with the stored parameters and extracted variables
   - Returns the response
4. Provides a static factory method for easy creation

## Examples

See the `src/agent-flow-example/examples/llm-node-example.ts` file for complete examples of using the LLM node in workflows.
