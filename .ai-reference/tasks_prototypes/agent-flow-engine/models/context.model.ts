/**
 * Context models for the Agent Flow Engine
 */
import { NodeId } from "../interfaces/types";
import * as crypto from "crypto";

/**
 * Context object used throughout workflow execution
 */
export class AgentContext {
  executionId: string;
  [key: string]: any;

  /**
   * Create a new context, optionally with initial data
   * @param initialData Optional data to populate the context with
   */
  constructor(initialData?: any) {
    this.metadata = new ContextMetadata();
    if (initialData) {
      Object.assign(this, initialData);
    }
  }

  /**
   * Get a value from the context by key
   * @param key The key to retrieve
   * @returns The value, or undefined if not found
   */
  get(key: string): any {
    return this[key];
  }

  /**
   * Check if a key exists in the context
   * @param key The key to check
   * @returns True if the key exists, false otherwise
   */
  has(key: string): boolean {
    return this[key] !== undefined;
  }

  /**
   * Set a value in the context
   * @param key The key to set
   * @param value The value to set
   */
  set(key: string, value: any): void {
    this[key] = value;
  }

  /**
   * Create a clone of this context
   * @returns A new AgentContext with the same data (but shared execution ID)
   */
  clone(): AgentContext {
    const cloned = new AgentContext();

    // Clone all properties except metadata
    for (const key in this) {
      if (key !== "metadata" && Object.prototype.hasOwnProperty.call(this, key)) {
        cloned[key] = this[key];
      }
    }

    // Clone metadata but preserve the execution ID
    cloned.metadata = new ContextMetadata();
    cloned.metadata.executionId = this.metadata.executionId;
    cloned.metadata.startTime = this.metadata.startTime;

    // Clone errors
    this.metadata.errors.forEach((error, nodeId) => {
      cloned.metadata.errors.set(nodeId, error);
    });

    return cloned;
  }

  /**
   * Convert the context to a plain JavaScript object for serialization
   * @returns A plain object with all context properties except metadata
   */
  toJSON(): Record<string, any> {
    const result: Record<string, any> = {};

    // Copy all properties except metadata
    for (const key in this) {
      if (key !== "metadata" && Object.prototype.hasOwnProperty.call(this, key)) {
        result[key] = this[key];
      }
    }

    // Include a simplified version of metadata for debugging
    result.metadata = {
      executionId: this.metadata.executionId,
      startTime: this.metadata.startTime.toISOString(),
      errors: Object.fromEntries(this.metadata.errors)
    };

    return result;
  }
}
