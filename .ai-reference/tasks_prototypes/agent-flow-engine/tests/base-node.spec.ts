import { Test } from "@nestjs/testing";
import { BaseNode, Node, NodeConfig } from "../models/node.model";
import { AgentContext } from "../models/context.model";
import { NodeType } from "../interfaces/types";

describe("BaseNode", () => {
  describe("Node (concrete implementation of BaseNode)", () => {
    let node: Node;
    let mockProcessFn: jest.Mock;
    let mockInputMapperFn: jest.Mock;
    let mockErrorHandlerFn: jest.Mock;

    beforeEach(() => {
      // Create mocks for the functions
      mockProcessFn = jest.fn();
      mockInputMapperFn = jest.fn((ctx) => ctx);
      mockErrorHandlerFn = jest.fn((ctx, _) => ctx);

      // Create a basic node configuration
      const nodeConfig: NodeConfig = {
        id: "test-node",
        type: "standard" as NodeType,
        description: "Test node for unit tests",
        processFn: mockProcessFn,
        inputMapperFn: mockInputMapperFn,
        errorHandlerFn: mockErrorHandlerFn,
      };

      // Create the node instance
      node = new Node(nodeConfig);
    });

    it("should correctly initialize with the provided configuration", () => {
      expect(node.id).toBe("test-node");
      expect(node.type).toBe("standard");
      expect(node.description).toBe("Test node for unit tests");
    });

    it("should use the default input mapper if none provided", () => {
      const nodeWithDefaults = new Node({
        id: "default-node",
        type: "standard" as NodeType,
        description: "Node with defaults",
        processFn: jest.fn(),
      });

      // Create a context
      const context = new AgentContext({ data: "test" });

      // This should not throw since the default input mapper just returns the context
      nodeWithDefaults.execute(context);
    });

    it("should execute the process function with mapped input", async () => {
      // Set up the mock to return specific values
      mockInputMapperFn.mockReturnValue({ mappedData: "test-input" });
      mockProcessFn.mockResolvedValue({ result: "success" });

      // Create a context
      const context = new AgentContext({ originalData: "original" });

      // Execute the node
      const result = await node.execute(context);

      // Verify the input mapper was called with the context
      expect(mockInputMapperFn).toHaveBeenCalledWith(context);

      // Verify the process function was called with the mapped input
      expect(mockProcessFn).toHaveBeenCalledWith({ mappedData: "test-input" });

      // Verify the result has the original context data and new output
      expect(result.originalData).toBe("original");
      expect(result["test-node"]).toEqual({ result: "success" });
    });

    it("should use error handler when process function throws", async () => {
      // Make the process function throw an error
      const testError = new Error("Process function error");
      mockProcessFn.mockRejectedValue(testError);

      // Create a context
      const context = new AgentContext();

      // Execute the node
      await node.execute(context);

      // Verify the error handler was called with the context and error
      expect(mockErrorHandlerFn).toHaveBeenCalledWith(context, testError);
    });

    it("should use default error handler if none provided", async () => {
      // Create a node without custom error handler
      const nodeWithoutErrorHandler = new Node({
        id: "error-node",
        type: "standard" as NodeType,
        description: "Node without error handler",
        processFn: jest.fn().mockRejectedValue(new Error("Test error")),
      });

      // Create a context spy to verify the default error handler is called
      const context = new AgentContext();
      const recordErrorSpy = jest.spyOn(context.metadata, "recordError");

      // Execute the node
      await nodeWithoutErrorHandler.execute(context);

      // Verify the default error handler recorded the error
      expect(recordErrorSpy).toHaveBeenCalledWith(
        "error-node",
        expect.any(Error),
      );
    });

    it("should create a new context object during execution to avoid mutations", async () => {
      mockProcessFn.mockResolvedValue({ result: "success" });

      // Create a context
      const originalContext = new AgentContext({ data: "original" });

      // Execute the node
      const resultContext = await node.execute(originalContext);

      // Verify the result is a new object, not the same reference
      expect(resultContext).not.toBe(originalContext);

      // But it should have the same execution ID (cloned properly)
      expect(resultContext.metadata.executionId).toBe(
        originalContext.metadata.executionId,
      );

      // And it should have the original data
      expect(resultContext.data).toBe("original");
    });
  });
});
