import { Test } from "@nestjs/testing";
import { LLMNode, LLMNodeConfig } from "../models/llm-node.model";
import { AskLLMService } from "../../llm/services/ask-llm.service";
import { AgentContext } from "../models/context.model";
import { z } from "zod";

describe("LLMNode", () => {
  let llmNode: LLMNode;
  let mockAskLLMService: jest.Mocked<AskLLMService>;

  beforeEach(async () => {
    // Create a mock for AskLLMService
    mockAskLLMService = {
      ask: jest.fn(),
    } as any;

    // Create the module with the mocked service
    const moduleRef = await Test.createTestingModule({
      providers: [
        {
          provide: AskLLMService,
          useValue: mockAskLLMService,
        },
      ],
    }).compile();

    // Configure the mock to return a sample response that matches LlmResponse type
    mockAskLLMService.ask.mockResolvedValue({
      message: {
        role: "assistant",
        content: "This is a test response",
      },
      parsed: { result: "test" },
      raw: {
        /* Original provider response would go here */
      },
    });

    // Create a basic LLM node configuration
    const llmNodeConfig: LLMNodeConfig = {
      id: "test-llm-node",
      type: "llm",
      description: "Test LLM node for unit tests",
      llmParams: {
        model: "gpt-4o-mini",
        system: "You are a helpful assistant",
        prompt: "Answer the following question: {{question}}",
        temperature: 0.1,
        zodSchema: z.object({ result: z.string() }),
        schemaName: "testSchema",
      },
    };

    // Create the LLM node instance
    llmNode = new LLMNode(mockAskLLMService, llmNodeConfig);
  });

  it("should correctly initialize with the provided configuration", () => {
    expect(llmNode.id).toBe("test-llm-node");
    expect(llmNode.type).toBe("llm");
    expect(llmNode.description).toBe("Test LLM node for unit tests");
  });

  it("should call askLLMService.ask with the correct parameters", async () => {
    // Create a test context
    const context = new AgentContext({
      question: "What is the meaning of life?",
    });

    // Execute the node
    await llmNode.execute(context);

    // Verify that askLLMService.ask was called with the correct parameters
    expect(mockAskLLMService.ask).toHaveBeenCalledWith({
      model: "gpt-4o-mini",
      system: "You are a helpful assistant",
      prompt: "Answer the following question: {{question}}",
      temperature: 0.1,
      zodSchema: expect.any(Object),
      schemaName: "testSchema",
      variables: expect.objectContaining({
        question: "What is the meaning of life?",
      }),
    });
  });

  it("should store the LLM response in the context", async () => {
    // Create a test context
    const context = new AgentContext({
      question: "What is the meaning of life?",
    });

    // Execute the node
    const result = await llmNode.execute(context);

    // Verify that the result contains the node's output
    expect(result["test-llm-node"]).toEqual({
      message: {
        role: "assistant",
        content: "This is a test response",
      },
      parsed: { result: "test" },
      raw: expect.any(Object),
    });
  });

  it("should use the factory method to create an LLM node", () => {
    // Create a node using the factory method
    const factoryNode = LLMNode.create(
      mockAskLLMService,
      "factory-node",
      "llm",
      "Node created with factory method",
      {
        model: "gpt-4o-mini",
        system: "You are a helpful assistant",
        prompt: "Answer the following question: {{question}}",
      },
    );

    // Verify the node was created correctly
    expect(factoryNode).toBeInstanceOf(LLMNode);
    expect(factoryNode.id).toBe("factory-node");
    expect(factoryNode.type).toBe("llm");
    expect(factoryNode.description).toBe("Node created with factory method");
  });
});
