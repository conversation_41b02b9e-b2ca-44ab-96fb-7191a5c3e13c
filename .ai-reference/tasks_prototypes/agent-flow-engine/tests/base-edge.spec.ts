import { BaseEdge, EdgeConfig } from "../models/edge.model";
import { AgentContext } from "../models/context.model";

describe("BaseEdge", () => {
  let edge: BaseEdge;
  let mockConditionFn: jest.Mock;

  beforeEach(() => {
    // Create a mock condition function
    mockConditionFn = jest.fn();

    // Create basic edge configuration
    const edgeConfig: EdgeConfig = {
      id: "test-edge",
      from: "source-node",
      to: "target-node",
      conditionFn: mockConditionFn,
      description: "Test edge for unit tests",
    };

    // Create the edge instance
    edge = new BaseEdge(edgeConfig);
  });

  it("should correctly initialize with the provided configuration", () => {
    expect(edge.id).toBe("test-edge");
    expect(edge.from).toBe("source-node");
    expect(edge.to).toBe("target-node");
    expect(edge.description).toBe("Test edge for unit tests");
  });

  it("should use the default condition function if none provided", async () => {
    // Create an edge without a condition function
    const edgeWithDefaultCondition = new BaseEdge({
      id: "default-edge",
      from: "node-a",
      to: "node-b",
    });

    // Create a context
    const context = new AgentContext();

    // Default condition should always return true
    const result = await edgeWithDefaultCondition.evaluate(context);
    expect(result).toBe(true);
  });

  it("should use default empty string for description if none provided", () => {
    // Create an edge without a description
    const edgeWithoutDescription = new BaseEdge({
      id: "no-desc-edge",
      from: "node-a",
      to: "node-b",
    });

    // Description should be an empty string
    expect(edgeWithoutDescription.description).toBe("");
  });

  it("should call the condition function with the context during evaluation", async () => {
    // Set up the condition function to return true
    mockConditionFn.mockResolvedValue(true);

    // Create a context
    const context = new AgentContext({ testData: "value" });

    // Evaluate the edge
    const result = await edge.evaluate(context);

    // Verify the condition function was called with the context
    expect(mockConditionFn).toHaveBeenCalledWith(context);
    expect(result).toBe(true);
  });

  it("should return the target node ID when follow() is called and condition is true", async () => {
    // Set up the condition function to return true
    mockConditionFn.mockResolvedValue(true);

    // Create a context
    const context = new AgentContext();

    // Follow the edge
    const targetNodeId = await edge.follow(context);

    // Should return the target node ID
    expect(targetNodeId).toBe("target-node");
  });

  it("should return null when follow() is called and condition is false", async () => {
    // Set up the condition function to return false
    mockConditionFn.mockResolvedValue(false);

    // Create a context
    const context = new AgentContext();

    // Follow the edge
    const targetNodeId = await edge.follow(context);

    // Should return null
    expect(targetNodeId).toBeNull();
  });

  it("should throw error with edge information when condition function throws", async () => {
    // Make the condition function throw an error
    const testError = new Error("Condition function error");
    mockConditionFn.mockRejectedValue(testError);

    // Create a context
    const context = new AgentContext();

    // Evaluate the edge - should throw with enhanced error message
    await expect(edge.evaluate(context)).rejects.toThrow(
      "Edge condition evaluation failed for edge test-edge (source-node → target-node): Condition function error",
    );
  });

  it("should throw error with edge information when follow() encounters an error", async () => {
    // Make the condition function throw an error
    const testError = new Error("Follow error");
    mockConditionFn.mockRejectedValue(testError);

    // Create a context
    const context = new AgentContext();

    // Follow the edge - should throw with enhanced error message
    await expect(edge.follow(context)).rejects.toThrow(
      "Failed to follow edge test-edge (source-node → target-node): Edge condition evaluation failed for edge test-edge (source-node → target-node): Follow error",
    );
  });
});
