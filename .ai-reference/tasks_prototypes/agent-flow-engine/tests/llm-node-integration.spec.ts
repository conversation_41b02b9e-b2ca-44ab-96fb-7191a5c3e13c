import { Test } from "@nestjs/testing";
import { LLMNode } from "../models/llm-node.model";
import { AskLLMService } from "../../llm/services/ask-llm.service";
import { AgentContext } from "../models/context.model";
import { z } from "zod";
import * as dotenv from "dotenv";
import { LlmModule } from "../../llm/llm.module";

// Load environment variables
dotenv.config();

describe("LLMNode Integration Tests", () => {
  let llmNode: LLMNode;
  let askLLMService: AskLLMService;

  beforeAll(async () => {
    // Create a real module with the actual services
    const moduleRef = await Test.createTestingModule({
      imports: [
        LlmModule.register({
          openaiApiKey: process.env.OPENAI_API_KEY || "",
        }),
      ],
    }).compile();

    // Get the real AskLLMService
    askLLMService = moduleRef.get<AskLLMService>(AskLLMService);

    // Create the LLM node
    llmNode = LLMNode.create(
      askLLMService,
      "test-llm-node",
      "llm",
      "Test LLM node for integration tests",
      {
        model: "gpt-4o-mini",
        system: "You are a helpful assistant that answers succinctly",
        prompt: "Answer the following question in one sentence: {{question}}",
        temperature: 0.1,
        zodSchema: z.object({
          answer: z.string().describe("The answer to the question"),
        }),
        schemaName: "answer",
      },
      // Custom variables extractor
      (context) => {
        const newContext = context.clone();
        newContext.question =
          context.question || "What is the meaning of life?";
        return newContext;
      },
    );
  });

  it("should execute an LLM node using the OpenAI API", async () => {
    // Skip this test if no API key is available
    if (!process.env.OPENAI_API_KEY) {
      console.log("Skipping test: No OpenAI API key available");
      return;
    }

    // Create a test context
    const context = new AgentContext({
      question: "What is the capital of France?",
    });

    // Execute the node
    const result = await llmNode.execute(context);

    // Verify that the result contains the LLM output
    const nodeOutput = result["test-llm-node"];

    // Check that we got a valid response
    expect(nodeOutput).toBeDefined();
    expect(nodeOutput.message).toBeDefined();
    expect(nodeOutput.message.role).toBe("assistant");
    expect(typeof nodeOutput.message.content).toBe("string");

    // Check the parsed output
    expect(nodeOutput.parsed).toBeDefined();
    expect(nodeOutput.parsed.answer).toBeDefined();

    // The answer should contain "Paris" since that's the capital of France
    expect(nodeOutput.parsed.answer.toLowerCase()).toContain("paris");

    console.log("Answer:", nodeOutput.parsed.answer);
  }, 30000); // Allow up to 30 seconds for the API call
});
