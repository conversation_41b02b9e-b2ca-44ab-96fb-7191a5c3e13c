import { Node } from "../models/node.model";
import { BaseEdge } from "../models/edge.model";
import { AgentContext } from "../models/context.model";
import { WorkflowGraph } from "../models/workflow-graph.model";
import { WorkflowExecutor } from "../services/workflow-executor.service";

describe("Workflow Integration", () => {
  // Test a simple workflow: Start -> Process -> End
  describe("Linear workflow", () => {
    let startNode: Node;
    let processNode: Node;
    let endNode: Node;
    let startToProcessEdge: BaseEdge;
    let processToEndEdge: BaseEdge;
    let graph: WorkflowGraph;
    let executor: WorkflowExecutor;

    beforeEach(() => {
      // Create nodes
      startNode = new Node({
        id: "start",
        type: "standard",
        description: "Start node",
        processFn: jest.fn().mockResolvedValue({ status: "started" }),
      });

      processNode = new Node({
        id: "process",
        type: "standard",
        description: "Process node",
        processFn: jest.fn().mockImplementation(async (context) => {
          const input = context.input || "default";
          return { processed: input, timestamp: Date.now() };
        }),
      });

      endNode = new Node({
        id: "end",
        type: "standard",
        description: "End node",
        processFn: jest.fn().mockResolvedValue({ status: "completed" }),
      });

      // Create edges
      startToProcessEdge = new BaseEdge({
        id: "start-to-process",
        from: "start",
        to: "process",
      });

      processToEndEdge = new BaseEdge({
        id: "process-to-end",
        from: "process",
        to: "end",
      });

      // Create the graph - pass the start node ID in the constructor
      graph = new WorkflowGraph("start");
      graph.addNode(startNode);
      graph.addNode(processNode);
      graph.addNode(endNode);
      graph.addEdge(startToProcessEdge);
      graph.addEdge(processToEndEdge);

      // Create the executor
      executor = new WorkflowExecutor({
        graph,
      });
    });

    it("should execute the workflow in the expected order", async () => {
      // Create spies to check execution order
      const startSpy = jest.spyOn(startNode, "execute");
      const processSpy = jest.spyOn(processNode, "execute");
      const endSpy = jest.spyOn(endNode, "execute");

      // Execute the workflow
      const result = await executor.execute({ input: "test-data" });

      // Verify nodes were executed in the expected order
      expect(startSpy).toHaveBeenCalled();
      expect(processSpy).toHaveBeenCalled();
      expect(endSpy).toHaveBeenCalled();

      // Verify call order
      expect(startSpy.mock.invocationCallOrder[0]).toBeLessThan(
        processSpy.mock.invocationCallOrder[0],
      );
      expect(processSpy.mock.invocationCallOrder[0]).toBeLessThan(
        endSpy.mock.invocationCallOrder[0],
      );

      // Verify the final result contains all node outputs
      expect(result.start).toEqual({ status: "started" });
      expect(result.process).toEqual(
        expect.objectContaining({
          processed: "test-data",
          timestamp: expect.any(Number),
        }),
      );
      expect(result.end).toEqual({ status: "completed" });
    });
  });

  // Test a workflow with conditional branching
  describe("Conditional workflow", () => {
    let startNode: Node;
    let pathANode: Node;
    let pathBNode: Node;
    let endNode: Node;
    let graph: WorkflowGraph;
    let executor: WorkflowExecutor;

    beforeEach(() => {
      // Create nodes
      startNode = new Node({
        id: "start",
        type: "standard",
        description: "Start node",
        processFn: jest.fn().mockResolvedValue({ ready: true }),
      });

      pathANode = new Node({
        id: "path-a",
        type: "standard",
        description: "Path A node",
        processFn: jest.fn().mockResolvedValue({ path: "A" }),
      });

      pathBNode = new Node({
        id: "path-b",
        type: "standard",
        description: "Path B node",
        processFn: jest.fn().mockResolvedValue({ path: "B" }),
      });

      endNode = new Node({
        id: "end",
        type: "standard",
        description: "End node",
        processFn: jest.fn().mockResolvedValue({ completed: true }),
      });

      // Create the graph - pass the start node ID in the constructor
      graph = new WorkflowGraph("start");
      graph.addNode(startNode);
      graph.addNode(pathANode);
      graph.addNode(pathBNode);
      graph.addNode(endNode);
    });

    it("should follow path A when condition is true", async () => {
      // Add conditional edges
      graph.addEdge(
        new BaseEdge({
          id: "start-to-a",
          from: "start",
          to: "path-a",
          conditionFn: (context) => Promise.resolve(context.usePathA === true),
        }),
      );

      graph.addEdge(
        new BaseEdge({
          id: "start-to-b",
          from: "start",
          to: "path-b",
          conditionFn: (context) => Promise.resolve(context.usePathA !== true),
        }),
      );

      // Connect both paths to end
      graph.addEdge(
        new BaseEdge({
          id: "a-to-end",
          from: "path-a",
          to: "end",
        }),
      );

      graph.addEdge(
        new BaseEdge({
          id: "b-to-end",
          from: "path-b",
          to: "end",
        }),
      );

      // Create the executor
      executor = new WorkflowExecutor({
        graph,
      });

      // Create spies to check execution
      const pathASpy = jest.spyOn(pathANode, "execute");
      const pathBSpy = jest.spyOn(pathBNode, "execute");

      // Execute the workflow with path A condition
      const result = await executor.execute({ usePathA: true });

      // Verify only path A was executed
      expect(pathASpy).toHaveBeenCalled();
      expect(pathBSpy).not.toHaveBeenCalled();

      // Verify the result contains the expected outputs
      expect(result.start).toEqual({ ready: true });
      expect(result["path-a"]).toEqual({ path: "A" });
      expect(result["path-b"]).toBeUndefined();
      expect(result.end).toEqual({ completed: true });
    });

    it("should follow path B when condition is false", async () => {
      // Add conditional edges
      graph.addEdge(
        new BaseEdge({
          id: "start-to-a",
          from: "start",
          to: "path-a",
          conditionFn: (context) => Promise.resolve(context.usePathA === true),
        }),
      );

      graph.addEdge(
        new BaseEdge({
          id: "start-to-b",
          from: "start",
          to: "path-b",
          conditionFn: (context) => Promise.resolve(context.usePathA !== true),
        }),
      );

      // Connect both paths to end
      graph.addEdge(
        new BaseEdge({
          id: "a-to-end",
          from: "path-a",
          to: "end",
        }),
      );

      graph.addEdge(
        new BaseEdge({
          id: "b-to-end",
          from: "path-b",
          to: "end",
        }),
      );

      // Create the executor
      executor = new WorkflowExecutor({
        graph,
      });

      // Create spies to check execution
      const pathASpy = jest.spyOn(pathANode, "execute");
      const pathBSpy = jest.spyOn(pathBNode, "execute");

      // Execute the workflow with path B condition
      const result = await executor.execute({ usePathA: false });

      // Verify only path B was executed
      expect(pathASpy).not.toHaveBeenCalled();
      expect(pathBSpy).toHaveBeenCalled();

      // Verify the result contains the expected outputs
      expect(result.start).toEqual({ ready: true });
      expect(result["path-a"]).toBeUndefined();
      expect(result["path-b"]).toEqual({ path: "B" });
      expect(result.end).toEqual({ completed: true });
    });
  });
});
