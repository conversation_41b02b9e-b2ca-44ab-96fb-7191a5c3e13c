import { AgentContext, ContextMetadata } from "../models/context.model";

describe("AgentContext", () => {
  let context: AgentContext;

  beforeEach(() => {
    context = new AgentContext();
  });

  it("should create a new context with metadata", () => {
    expect(context.metadata).toBeInstanceOf(ContextMetadata);
    expect(context.metadata.executionId).toBeDefined();
    expect(context.metadata.startTime).toBeInstanceOf(Date);
    expect(context.metadata.errors).toBeInstanceOf(Map);
    expect(context.metadata.errors.size).toBe(0);
  });

  it("should initialize with provided data", () => {
    const contextWithData = new AgentContext({
      foo: "bar",
      nested: { value: 123 },
    });

    expect(contextWithData.foo).toBe("bar");
    expect(contextWithData.nested.value).toBe(123);
  });

  it("should allow setting and getting arbitrary properties", () => {
    context.testProperty = "test value";
    context.numberProp = 42;

    expect(context.testProperty).toBe("test value");
    expect(context.numberProp).toBe(42);
  });

  describe("clone()", () => {
    it("should create a shallow copy of the context", () => {
      // Set up original context
      context.testValue = "original";
      context.nested = { prop: "value" };

      // Clone the context
      const cloned = context.clone();

      // Should not be the same reference
      expect(cloned).not.toBe(context);

      // Should have the same values
      expect(cloned.testValue).toBe("original");
      expect(cloned.nested.prop).toBe("value");

      // Changing primitive values in cloned context should not affect original
      cloned.testValue = "changed";
      expect(context.testValue).toBe("original");

      // Note: The current implementation does a shallow clone, so nested objects
      // are shared by reference. This test acknowledges that behavior.
      cloned.nested.prop = "new value";
      expect(context.nested.prop).toBe("new value"); // Both reference the same object
    });

    it("should preserve execution ID when cloning", () => {
      const cloned = context.clone();
      expect(cloned.metadata.executionId).toBe(context.metadata.executionId);
    });

    it("should preserve start time when cloning", () => {
      const cloned = context.clone();
      expect(cloned.metadata.startTime).toEqual(context.metadata.startTime);
    });

    it("should clone error records", () => {
      // Record an error in the original context
      const testError = new Error("Test error");
      context.metadata.recordError("test-node", testError);

      // Clone the context
      const cloned = context.clone();

      // Check that the error was copied to the cloned context
      expect(cloned.metadata.errors.has("test-node")).toBe(true);

      // Get the error safely and check its message
      const error = cloned.metadata.errors.get("test-node");
      expect(error).toBeDefined();
      expect(error?.message).toBe("Test error");

      // But they should be different instances of Map
      expect(cloned.metadata.errors).not.toBe(context.metadata.errors);
    });
  });

  describe("ContextMetadata", () => {
    it("should record errors correctly", () => {
      const metadata = new ContextMetadata();
      const error1 = new Error("First error");
      const error2 = new Error("Second error");

      metadata.recordError("node1", error1);
      metadata.recordError("node2", error2);

      expect(metadata.errors.size).toBe(2);
      expect(metadata.errors.get("node1")).toBe(error1);
      expect(metadata.errors.get("node2")).toBe(error2);
    });

    it("should overwrite errors for the same node ID", () => {
      const metadata = new ContextMetadata();
      const error1 = new Error("First error");
      const error2 = new Error("Second error");

      metadata.recordError("node1", error1);
      metadata.recordError("node1", error2);

      expect(metadata.errors.size).toBe(1);
      expect(metadata.errors.get("node1")).toBe(error2);
    });
  });
});
