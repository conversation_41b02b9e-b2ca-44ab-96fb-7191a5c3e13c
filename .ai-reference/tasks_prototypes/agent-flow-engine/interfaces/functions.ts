/**
 * Core function interfaces for the Agent Flow Engine
 */
import { AgentContext } from "../models/context.model";

/**
 * Function to map context to input for a node
 */
export interface InputMapperFn<T = any> {
  (context: AgentContext): T;
}

/**
 * Function to process node input and produce output
 */
export interface ProcessFn<I = any, O = any> {
  (input: I): Promise<O>;
}

/**
 * Function to evaluate if an edge should be followed
 */
export interface EdgeConditionFn {
  (context: AgentContext): Promise<boolean>;
}

/**
 * Function to handle errors in a node's execution
 */
export interface ErrorHandlerFn {
  (context: AgentContext, error: Error): AgentContext;
}

/**
 * Function to convert raw data to an AgentContext
 */
export interface DataToContextFn {
  (data: any): AgentContext;
}

/**
 * Function to convert an AgentContext to output data
 */
export interface ContextToDataFn {
  (context: AgentContext): any;
}
