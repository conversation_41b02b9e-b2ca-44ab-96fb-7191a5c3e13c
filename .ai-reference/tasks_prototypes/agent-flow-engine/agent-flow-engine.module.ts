import { Module } from "@nestjs/common";
import { WorkflowExecutor } from "./services/workflow-executor.service";
import { AgentFlowEngineService } from "./agent-flow-engine.service";
import { LLMNode } from "./models/llm-node.model";

@Module({
  providers: [
    {
      provide: WorkflowExecutor,
      useFactory: () => {
        // This is just a placeholder. The actual executor will be created by AgentFlowEngineService
        // when needed with the appropriate configuration.
        return null;
      },
    },
    AgentFlowEngineService,
    LLMNode,
    // Other providers can be added here
  ],
  exports: [
    WorkflowExecutor,
    AgentFlowEngineService,
    LLMNode,
    // Other exports can be added here
  ],
})
export class AgentFlowEngineModule {}
