import { z } from "zod";

/**
 * Schema for validating field category selection output
 */
export const FieldCategorySchema = z.object({
  category: z
    .enum([
      "DateTime",
      "Location",
      "TradePartner",
      "Measurements",
      "Identifiers",
      "Documents",
      "Unsure",
    ])
    .describe("The selected field category from the instruction"),
  reasoning: z
    .string()
    .optional()
    .describe("Optional reasoning for the selected field category"),
});

export type FieldCategory = z.infer<typeof FieldCategorySchema>;
