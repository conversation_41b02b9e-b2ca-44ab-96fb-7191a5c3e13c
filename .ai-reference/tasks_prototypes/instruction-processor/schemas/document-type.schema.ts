import { z } from "zod";

/**
 * Schema for validating document type identification output
 */
export const DocumentTypeSchema = z.object({
  documentType: z
    .enum([
      "Shipment",
      "Commercial Invoice",
      "Certificate of Origin",
      "Unsure",
      "Other",
      "Wrong Operation",
    ])
    .describe("The identified document type from the instruction"),
  reasoning: z
    .string()
    .optional()
    .describe("Optional reasoning for the identified document type"),
});

export type DocumentType = z.infer<typeof DocumentTypeSchema>;
