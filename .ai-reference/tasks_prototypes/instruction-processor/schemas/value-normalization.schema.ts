import { z } from "zod";

/**
 * Schema for validating value normalization output
 */
export const ValueNormalizationSchema = z.object({
  originalValue: z
    .string()
    .describe("The original value extracted from the instruction"),
  normalizedValue: z
    .string()
    .describe("The normalized value in the appropriate format for the field"),
  success: z
    .boolean()
    .describe("Flag indicating if normalization was successful"),
  reasoning: z.string().describe("Reasoning for the normalization process"),
  needsHumanReview: z
    .boolean()
    .describe(
      "Flag indicating if human review is needed (set to true if needed, otherwise false)",
    ),
});

export type ValueNormalization = z.infer<typeof ValueNormalizationSchema>;
