import { z } from "zod";

/**
 * Schema for validating final review output
 */
export const FinalReviewSchema = z.object({
  isValid: z.boolean().describe("Flag indicating if the update is valid"),
  reasoning: z.string().describe("Reasoning for the validation result"),
  fixableIssue: z
    .boolean()
    .describe(
      "Flag indicating if there's a fixable issue (set to true if fixable, otherwise false)",
    ),
  fundamentallyWrong: z
    .boolean()
    .describe(
      "Flag indicating if the update is fundamentally wrong (set to true if wrong, otherwise false)",
    ),
  suggestedFix: z
    .string()
    .optional()
    .describe("Suggested fix for fixable issues"),
});

export type FinalReview = z.infer<typeof FinalReviewSchema>;
