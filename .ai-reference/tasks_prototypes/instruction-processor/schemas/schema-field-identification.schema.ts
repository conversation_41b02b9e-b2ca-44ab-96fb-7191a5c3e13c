import { z } from "zod";

/**
 * Schema for validating schema field identification output
 */
export const SchemaFieldIdentificationSchema = z.object({
  fieldName: z
    .string()
    .describe("The identified schema field name from the instruction"),
  confidence: z
    .number()
    .describe("Confidence level in the field identification (from 0 to 1)"),
  reasoning: z
    .string()
    .optional()
    .describe("Optional reasoning for the identified field"),
  wrongCategory: z
    .boolean()
    .describe(
      "Flag indicating if the wrong category was selected (set to true if this is the case, otherwise false)",
    ),
  noMatchingField: z
    .boolean()
    .describe(
      "Flag indicating if no matching field was found (set to true if this is the case, otherwise false)",
    ),
});

export type SchemaFieldIdentification = z.infer<
  typeof SchemaFieldIdentificationSchema
>;
