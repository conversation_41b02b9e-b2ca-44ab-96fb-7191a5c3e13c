import { z } from "zod";

/**
 * Schema for validating operation detection output
 */
export const OperationDetectionSchema = z.object({
  operation: z
    .enum(["Query", "Create", "Update", "SPAM", "Other"])
    .describe("The detected operation type from the instruction"),
  reasoning: z
    .string()
    .optional()
    .describe("Optional reasoning for the detected operation type"),
});

export type OperationDetection = z.infer<typeof OperationDetectionSchema>;
