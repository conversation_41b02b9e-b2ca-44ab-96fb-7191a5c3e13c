# Intent to Schema Workflow Plan

## Overview

This document outlines the plan to build the intent-to-schema processing workflow in the instruction-processor module. The goal is to transform a user instruction (from an email) into a validated update schema by sequentially processing it through several LLM-powered nodes. Each node is defined declaratively using the LLMNode interface, and responses are validated using Zod schemas. The ultimate update schemas are in the src/schemas/validation folder.

## Workflow Stages & Nodes

The workflow is broken into the following stages as per the mermaid flowchart:

### Stage 1: Operation & Spam Detection

- **Node:** OperationDetectionNode
- **Function:** Analyze the user instruction to determine the operation type (e.g., Query, Create, Update, SPAM, Other).
- **LLMNode Configuration:**
  - Model: gpt-4o-mini
  - System message prompt to return one of the operation types
  - Temperature: 0.1
  - Expected output validated via **OperationDetectionSchema** (Zod schema).

### Stage 2: Document Type Identification

- **Node:** DocumentTypeNode
- **Function:** Determine the document type based on the operation, e.g., Shipment, Commercial Invoice, Certificate of Origin, or Unsure.
- **LLMNode Configuration:**
  - Validate response via **DocumentTypeSchema**.
  - Handle branching (e.g., Wrong Operation leads back to Stage 1; Other leads to Human Review).

### Stage 3: Field Category Selection

- **Node:** FieldCategoryNode
- **Function:** Based on document type, present a set of field categories, such as DateTime, Location, TradePartner, Measurements, Identifiers, or Documents.
- **LLMNode Configuration:**
  - Validate using **FieldCategorySchema**.
  - The node input is dynamically generated to reduce the context window stress by including only relevant field options.

### Stage 4: Schema Field Identification

- **Node:** SchemaFieldIdentificationNode
- **Function:** Identify specific schema fields within the update schemas (from src/schemas/validation) based on the chosen category.
- **LLMNode Configuration:**
  - Uses **SchemaFieldIdentificationSchema** for validation.
  - Supports loop-back if the identified field is not matching (wrong category or no match).

### Stage 5: Value Normalization

- **Node:** ValueNormalizationNode
- **Function:** Normalize the raw value extracted from the instruction, e.g., standardizing date/time formats, currencies, or numerical units.
- **LLMNode Configuration:**
  - Validate using **ValueNormalizationSchema**.
  - If normalization fails, channel to human review for fix or loop back for reprocessing.

### Stage 6: Final Review & Validation

- **Node:** FinalReviewNode
- **Function:** Review and validate the final composed update against the ultimate update schemas (ShipmentRecordSchema or CommercialInvoiceRecordSchema from src/schemas/validation).
- **LLMNode Configuration:**
  - Validate using **FinalReviewSchema** (or directly validate using the final schemas from the validation folder).
  - Decisions for valid, fixable, or human review outcomes.

## Zod Schemas Requirements

For each node, the following Zod schemas need to be defined (these may either be new or adapt existing ones):

1. **OperationDetectionSchema**: Schema to validate output; e.g., { operation: string } where operation is one of the allowed types.
2. **DocumentTypeSchema**: Schema to validate the document type response.
3. **FieldCategorySchema**: Schema to ensure the category is one of the expected options.
4. **SchemaFieldIdentificationSchema**: Schema for validating the field name identified by the LLM.
5. **ValueNormalizationSchema**: Schema for validating the normalized value output.
6. **FinalReviewSchema**: Schema (or direct use of validation schemas) to confirm that the final update object is valid.

## Building the Workflow Graph

- **Graph Construction:** In the instruction-processor service, instantiate the above nodes using LLMNode.create.
- **Edge Connections:** Define edges according to the flowchart:
  - From OperationDetectionNode, branch to DocumentTypeNode on operations like "Create" or "Update".
  - Branch to Human Review for outcomes like "SPAM" or "Other".
  - Connect DocumentTypeNode to FieldCategoryNode, then to SchemaFieldIdentificationNode.
  - From SchemaFieldIdentificationNode, move to ValueNormalizationNode.
  - Connect ValueNormalizationNode to FinalReviewNode; include loop backs if errors occur.

## Dynamic Field Presentation Strategy

To mitigate context window strain:

- **InputMapperFn:** Each LLMNode can use inputMapperFn to extract only relevant data from the AgentContext.
- **Dynamic Options:** Nodes like FieldCategoryNode will dynamically load and present a limited set of options based on prior selections (e.g., only load categories relevant to "Shipment").
- **Modular Templates:** Prompt templates should be modular so that only necessary context is included per node.

## Next Steps & Testing

1. **Schema Definitions:** Finalize the Zod schemas for each node, possibly leveraging and extending the schemas in src/schemas/validation.
2. **Node Configuration:** Configure and test individual LLMNodes using unit tests.
3. **Graph Assembly:** Assemble nodes and edges into a complete workflow and test end-to-end processing.
4. **Error Handling:** Ensure robust error handling and branch routing to human review when needed.

## LLMNode Configuration Details

To ensure smooth integration and minimal friction when configuring LLMNodes, note the following key points:

1. **LLMNode Creation**

   - Use the static `LLMNode.create` factory method to define nodes declaratively. This method accepts the AskLLMService instance, a unique node `id`, a `type` (typically "llm"), a descriptive `description`, and an `llmParams` object.
   - Example:
     ```typescript
     const operationDetectionNode = LLMNode.create(
       askLLMService,
       "operation-detection",
       "llm",
       "Detects the operation type from the instruction",
       {
         model: "gpt-4o-mini",
         system:
           "You are a helpful assistant that determines the operation type. Return one of: Query, Create, Update, SPAM, Other.",
         prompt:
           "Analyze the following instruction: {{instruction}} to determine its operation type.",
         temperature: 0.1,
         schemaName: "operationDetection",
         zodSchema: OperationDetectionSchema, // A Zod schema validating the output, e.g., { operation: string }
       },
       // Optional input mapper function
       (context) => ({ instruction: context.instruction }),
     );
     ```

2. **LLM Parameters (llmParams) Requirements**

   - `model`: Specify the LLM model to use, e.g., "gpt-4o-mini".
   - `system`: Provide a clear system message to guide the LLM's behavior.
   - `prompt`: Define the prompt with placeholders (e.g., `{{instruction}}`), which the `inputMapperFn` will populate with data from the AgentContext.
   - `temperature`: Set the decoding randomness, typically low (e.g., 0.1) for deterministic responses in decision nodes.
   - `schemaName`: A label for the schema, assisting in validation and debugging.
   - `zodSchema`: A Zod schema used to validate and parse the LLM response; must be defined for each node based on expected output.

3. **Input Mapping**

   - Use `inputMapperFn` to extract and construct only the relevant context from the AgentContext. This reduces the payload sent to the LLM, thereby minimizing context window strain.
   - Example: For the FieldCategoryNode, the inputMapperFn could extract the document type and a short list of relevant field categories.

4. **Error Handling**

   - LLMNodes inherit error handling from the BaseNode; however, you can pass an optional `errorHandlerFn` to customize behavior in case of failures.
   - Ensure that any errors are logged and that the node either retries or routes the instruction to a human review branch as defined in your workflow.

5. **Workflow Graph Integration**

   - Once nodes are created, integrate them into a WorkflowGraph in the instruction-processor service. Connect nodes with edges that reflect branching logic from the mermaid diagram.
   - Example Edge: From OperationDetectionNode to DocumentTypeNode when the operation is "Create" or "Update".

6. **Testing & Iteration**
   - Test each node independently with unit tests before assembling the complete graph.
   - Validate responses against the defined Zod schemas to catch any discrepancies early.

By following these configuration details and best practices, you'll ensure that LLMNodes are set up correctly, dynamically process only necessary data, and seamlessly integrate into the overall intent-to-schema workflow.

## Conclusion

This document serves as the blueprint for implementing the intent-to-schema processing flow using LLMNodes. It outlines how to compose declarative nodes using structured Zod schemas and dynamic input mapping, all integrated into a workflow graph that mimics the mermaid diagram.

---

_End of Plan_
