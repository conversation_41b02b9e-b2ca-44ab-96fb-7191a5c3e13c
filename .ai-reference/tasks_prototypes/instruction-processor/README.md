# Instruction Processor Module

The Instruction Processor is a NestJS module that demonstrates how to process instructions using a workflow engine.

## Overview

This module uses the Agent Flow Engine to define and execute workflows based on instructions.
It provides a simple example of how to create conditional paths in a workflow and process them accordingly.

## Features

- Conditional workflow execution
- API endpoints for workflow execution
- Health check endpoint

## API Endpoints

### Run Workflow

- **URL**: `/instruction/run`
- **Method**: `GET`
- **Query Parameters**:
  - `usePathA` (boolean): Determines which path the workflow will take
- **Response**: JSON object containing input parameters and workflow execution results

### Health Check

- **URL**: `/instruction/health`
- **Method**: `GET`
- **Response**: Text indicating the service is healthy

## Usage

To use this module in a NestJS application:

```typescript
import { Module } from "@nestjs/common";
import { InstructionProcessorModule } from "./instruction-processor";

@Module({
  imports: [InstructionProcessorModule],
})
export class AppModule {}
```

## Running Standalone

To run this module as a standalone application:

```bash
# Navigate to the project root
cd your-project

# Install dependencies
npm install

# Run the application
npm run start:instruction-processor
```

## Example

The instruction processor currently supports a simple conditional workflow:

1. Start the workflow
2. Choose between path A or path B based on the `usePathA` parameter
3. Complete the workflow

## Development

To extend this module, you can:

1. Add new workflow definitions in the `InstructionProcessorService`
2. Create new controller endpoints to trigger different workflows
3. Implement custom node types to handle specific instruction processing tasks
