import { Injectable } from "@nestjs/common";
import { AskLLMService } from "../../llm/services/ask-llm.service";
import { LLMNode } from "../../agent-flow-engine/models/llm-node.model";
import { FinalReviewSchema } from "../schemas";
import { AgentContext } from "../../agent-flow-engine/models/context.model";

/**
 * Node for final review of the update
 */
@Injectable()
export class FinalReviewNode {
  /**
   * Create an LLM node for final review
   */
  static create(askLLMService: AskLLMService): LLMNode {
    return LLMNode.create(
      askLLMService,
      "final-review",
      "llm",
      "Performs final review of the update",
      {
        model: "gpt-4o-mini",
        system:
          "You are a helpful assistant that performs a final review of updates. " +
          "Validate that the normalized value is appropriate for the field and that the overall update makes sense. " +
          "CRITICAL: For date fields, ANY date is valid as long as it matches what the user intended in their instruction. " +
          "Never reject a date just because it's in the past, future, or seems unusual - if the user specified that date, it's valid. " +
          "If there are any issues, determine if they are fixable or if human review is needed.",
        prompt:
          "Review the following update:\n\n" +
          "Instruction: {{instruction}}\n" +
          "Document Type: {{documentType}}\n" +
          "Field Name: {{fieldName}}\n" +
          "Original Value: {{originalValue}}\n" +
          "Normalized Value: {{normalizedValue}}\n" +
          "Normalization Reasoning: {{normalizationReasoning}}\n" +
          "Current date/time (for reference): {{currentDateTime}}\n\n" +
          "Validate that the normalized value is appropriate for the field and that the overall update makes sense.\n\n" +
          "*** CRITICAL DATE VALIDATION RULE ***\n" +
          "For ALL date fields: ANY date is valid as long as it matches what the user specified in their instruction.\n" +
          "* Never reject a date just because it's in the past or future\n" +
          "* Never reject a date because it seems unusual\n" +
          "* If a user specifically mentioned March 15, then March 15 is the correct date\n" +
          "* Only flag date issues if there's a clear mismatch with what the user requested\n\n" +
          "You MUST include all of the following properties in your response:\n" +
          "- isValid: Whether the update is valid (required, boolean)\n" +
          "- reasoning: Your reasoning for the validation result (required, string)\n" +
          "- fixableIssue: Whether there's a fixable issue (required, boolean)\n" +
          "- fundamentallyWrong: Whether the update is fundamentally wrong (required, boolean)\n" +
          "- suggestedFix: A suggested fix if there's a fixable issue (optional, string)\n\n" +
          "If there are any issues, determine if they are fixable (set fixableIssue to true) or if the update is " +
          "fundamentally wrong (set fundamentallyWrong to true).",
        temperature: 0.1,
        schemaName: "finalReview",
        zodSchema: FinalReviewSchema,
      },
      // Input mapper function to extract context
      (context: AgentContext) => {
        const instruction = context.get("instruction") || "";
        const documentType = context.get("document-type")?.documentType || "";
        const fieldName =
          context.get("schema-field-identification")?.fieldName || "";

        // Get the current date/time in ISO format
        const currentDateTime = new Date().toISOString();

        // Handle either string or object result from value-normalization
        let normalization;
        const normalizationResult = context.get("value-normalization");

        try {
          // If result is a string, try to parse it as JSON
          normalization =
            typeof normalizationResult === "string"
              ? JSON.parse(normalizationResult)
              : normalizationResult || {};

          // Handle the case where normalizedValue is a JSON string
          if (
            normalization.normalizedValue &&
            typeof normalization.normalizedValue === "string" &&
            (normalization.normalizedValue.startsWith("{") ||
              normalization.normalizedValue.startsWith("["))
          ) {
            try {
              normalization.parsedValue = JSON.parse(
                normalization.normalizedValue,
              );
            } catch (e) {
              console.warn(
                "Failed to parse normalizedValue JSON string in final-review:",
                e.message,
              );
            }
          }
        } catch (e) {
          console.warn(
            "Failed to parse value-normalization result in final-review:",
            e.message,
          );
          normalization = {
            originalValue: "Error: Could not parse normalization result",
            normalizedValue: "Error: Could not parse normalization result",
            reasoning: `Error processing input: ${e.message}`,
          };
        }

        // Ensure we have a string representation of the normalized value for the prompt
        let normalizedValueStr = "";
        if (normalization.normalizedValue !== undefined) {
          if (normalization.parsedValue) {
            // We already parsed it successfully above
            normalizedValueStr = JSON.stringify(
              normalization.parsedValue,
              null,
              2,
            );
          } else {
            normalizedValueStr = String(normalization.normalizedValue);
          }
        }

        // Return a simple object with variables for the LLM prompt template
        return {
          instruction,
          documentType,
          fieldName,
          originalValue: normalization.originalValue || "",
          normalizedValue: normalizedValueStr,
          normalizationReasoning: normalization.reasoning || "",
          currentDateTime,
        };
      },
    );
  }
}
