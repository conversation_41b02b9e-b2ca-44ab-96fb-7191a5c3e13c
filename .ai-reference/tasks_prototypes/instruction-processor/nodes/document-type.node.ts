import { Injectable } from "@nestjs/common";
import { AskLLMService } from "../../llm/services/ask-llm.service";
import { LLMNode } from "../../agent-flow-engine/models/llm-node.model";
import { DocumentTypeSchema } from "../schemas";
import { AgentContext } from "../../agent-flow-engine/models/context.model";

/**
 * Node for identifying the document type from an instruction
 */
@Injectable()
export class DocumentTypeNode {
  /**
   * Create an LLM node for document type identification
   */
  static create(askLLMService: AskLLMService): LLMNode {
    return LLMNode.create(
      askLLMService,
      "document-type",
      "llm",
      "Identifies the document type from the instruction",
      {
        model: "gpt-4o-mini",
        system:
          "You are a helpful assistant that identifies the document type from user instructions. " +
          "Analyze the instruction carefully and determine if it's related to a Shipment, Commercial Invoice, " +
          "Certificate of Origin, or if you're Unsure. If it's a different document type, classify as Other. " +
          "If you believe the operation was misclassified, respond with Wrong Operation.",
        prompt:
          "Analyze the following instruction to identify the document type:\n\n" +
          "{{instruction}}\n\n" +
          "The operation was classified as: {{operation}}\n\n" +
          "Determine if this instruction is related to a Shipment, Commercial Invoice, Certificate of Origin, " +
          "or if you're Unsure. If it's a different document type, classify as Other. " +
          "If you believe the operation was misclassified, respond with Wrong Operation. " +
          "Provide your reasoning for the classification.",
        temperature: 0.1,
        schemaName: "documentType",
        zodSchema: DocumentTypeSchema,
      },
      // Input mapper function to extract instruction and operation from context
      (context: AgentContext) => {
        const instruction = context.get("instruction") || "";
        const operation = context.get("operation-detection")?.operation || "";

        // Return a simple object with variables for the LLM prompt template
        return {
          instruction,
          operation,
        };
      },
    );
  }
}
