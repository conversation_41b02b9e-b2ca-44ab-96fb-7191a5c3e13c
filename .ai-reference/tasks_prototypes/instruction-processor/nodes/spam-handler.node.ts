import { Injectable } from "@nestjs/common";
import { Node } from "../../agent-flow-engine";
import { AgentContext } from "../../agent-flow-engine/models/context.model";

/**
 * Node for handling spam operations
 */
@Injectable()
export class SpamHandlerNode {
  /**
   * Create a standard node for handling spam
   */
  static create(): Node {
    return new Node({
      id: "spam-handler",
      type: "standard",
      description: "Handles spam operations",
      processFn: async (context: AgentContext) => {
        // In a real implementation, this would handle the spam operation
        // For now, we'll just log the context and return a simple result
        console.log(
          "Spam operation detected for instruction:",
          context.get("instruction"),
        );

        return {
          status: "spam_handled",
          message: "This instruction has been marked as spam.",
          timestamp: new Date().toISOString(),
        };
      },
    });
  }
}
