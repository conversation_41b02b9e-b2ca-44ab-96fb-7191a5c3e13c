import { Injectable } from "@nestjs/common";
import { AskLLMService } from "../../llm/services/ask-llm.service";
import { LLMNode } from "../../agent-flow-engine/models/llm-node.model";
import { ValueNormalizationSchema } from "../schemas";
import { AgentContext } from "../../agent-flow-engine/models/context.model";

/**
 * Node for normalizing values from an instruction
 */
@Injectable()
export class ValueNormalizationNode {
  /**
   * Create an LLM node for value normalization
   */
  static create(askLLMService: AskLLMService): LLMNode {
    return LLMNode.create(
      askLLMService,
      "value-normalization",
      "llm",
      "Normalizes values from the instruction",
      {
        model: "gpt-4o-mini",
        system:
          "You are a helpful assistant that normalizes values from user instructions. " +
          "Extract the relevant value from the instruction and normalize it according to the field's requirements. " +
          "Dates and times are fine as long as they match what the user intended - don't make assumptions or change dates unless there's a clear format issue. " +
          "If you cannot normalize the value, set success to false and explain why.",
        prompt:
          "Extract and normalize the value for the identified field from this instruction:\n\n" +
          "{{instruction}}\n\n" +
          "Field name: {{fieldName}}\n" +
          "Field requirements: {{fieldRequirements}}\n" +
          "Current date/time (for reference): {{currentDateTime}}\n\n" +
          "Extract the relevant value and normalize it according to the field's requirements. " +
          "For dates and times, PRESERVE THE EXACT DATE THE USER INTENDED - only change the format, not the actual date values. " +
          "You MUST include all of the following properties in your response:\n" +
          "- originalValue: The original text from the instruction (required, string)\n" +
          "- normalizedValue: The normalized value as a string (required). For dates use ISO format, for structured data use a JSON-formatted string\n" +
          "- success: Whether normalization was successful (required, boolean)\n" +
          "- reasoning: Explanation of how you normalized the value (required, string)\n" +
          "- needsHumanReview: Whether human review is needed (required, boolean)\n\n" +
          "FORMATTING GUIDELINES:\n" +
          "- For dates, normalize to ISO 8601 format (YYYY-MM-DDTHH:MM:SS+00:00). IMPORTANT: Keep the exact date specified by the user, only change format.\n" +
          "- If a date is relative to today (e.g., 'next week', 'in 3 days'), use the current date as reference.\n" +
          "- For locations, format as a proper address string or coordinates.\n" +
          "- For measurements, include the unit (e.g., '20 kg', '5 m³').",
        temperature: 0.1,
        schemaName: "valueNormalization",
        zodSchema: ValueNormalizationSchema,
      },
      // Input mapper function to extract context and generate field requirements
      (context: AgentContext) => {
        const instruction = context.get("instruction") || "";
        const fieldName =
          context.get("schema-field-identification")?.fieldName || "";
        const documentType = context.get("document-type")?.documentType || "";
        const fieldCategory = context.get("field-category")?.category || "";

        // Get the current date/time in ISO format
        const currentDateTime = new Date().toISOString();

        // Generate field requirements based on field name and document type
        let fieldRequirements = "";

        // Date/time fields
        if (fieldCategory === "DateTime") {
          fieldRequirements =
            "Format as ISO 8601 date/time (YYYY-MM-DDTHH:MM:SS+00:00). " +
            "If only date is provided without time, use T00:00:00+00:00 for the time portion. " +
            "CRITICAL: Always preserve the EXACT date and time values specified by the user - " +
            "only change the FORMAT to ISO 8601, but keep the same date, month, year, and time values that the user intended. " +
            "Do not try to 'correct' dates that seem to be in the past or future - assume the user intended exactly what they specified.";
        }
        // Location fields
        else if (fieldCategory === "Location") {
          fieldRequirements =
            "Format as a structured address with street, city, state/province, postal code, and country code.";
        }
        // Measurement fields
        else if (fieldCategory === "Measurements") {
          fieldRequirements =
            "Include the numeric value and the unit of measurement (e.g., 'kg', 'm³').";
        }
        // Trade partner fields
        else if (fieldCategory === "TradePartner") {
          fieldRequirements =
            "Include the full company name and their role (shipper, consignee, etc.).";
        }
        // Identifier fields
        else if (fieldCategory === "Identifiers") {
          fieldRequirements =
            "Preserve exact format including special characters or spacing.";
        }
        // Document fields
        else if (fieldCategory === "Documents") {
          fieldRequirements =
            "Format as document type and any identifying numbers.";
        }
        // Default case
        else {
          fieldRequirements =
            "Extract the value as precisely as possible from the instruction.";
        }

        // Add specific field requirements based on field name
        if (
          fieldName === "etd" ||
          fieldName === "eta" ||
          fieldName === "etaDestination" ||
          fieldName === "releaseDate"
        ) {
          fieldRequirements +=
            " CRITICAL: For this date field, use ISO 8601 format but preserve the EXACT date specified by the user. " +
            "Include time portion as T00:00:00+00:00 if only date is provided. " +
            "Do NOT change the actual date, month, or year values - assume the user's specified date is exactly what they intended.";
        }

        // Return a simple object with variables for the LLM prompt template
        return {
          instruction,
          fieldName,
          fieldRequirements,
          currentDateTime,
        };
      },
    );
  }
}
