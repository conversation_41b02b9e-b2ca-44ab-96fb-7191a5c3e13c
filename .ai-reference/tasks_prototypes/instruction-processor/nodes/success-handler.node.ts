import { Injectable, Logger } from "@nestjs/common";
import { Node } from "../../agent-flow-engine";
import { AgentContext } from "../../agent-flow-engine/models/context.model";

/**
 * Node for handling successful updates
 */
@Injectable()
export class SuccessHandlerNode {
  private static readonly logger = new Logger(SuccessHandlerNode.name);

  /**
   * Create a standard node for handling successful updates
   */
  static create(): Node {
    return new Node({
      id: "success-handler",
      type: "standard",
      description: "Handles successful updates",
      processFn: async (context: AgentContext) => {
        SuccessHandlerNode.logger.log(
          "Processing successful update for instruction:",
          context.get("instruction"),
        );

        // Extract all the relevant information from the context
        const originalInstruction = context.get("instruction") || "";

        // Get operation detection results
        const operationDetection = SuccessHandlerNode.getValueFromContext(
          context,
          "operation-detection",
        );
        const operation = operationDetection?.operation || "Unknown";

        // Get document type results
        const documentTypeResult = SuccessHandlerNode.getValueFromContext(
          context,
          "document-type",
        );
        const documentType = documentTypeResult?.documentType || "Unknown";

        // Get field identification results
        const fieldIdResult = SuccessHandlerNode.getValueFromContext(
          context,
          "schema-field-identification",
        );
        const fieldName = fieldIdResult?.fieldName || "Unknown";
        const fieldConfidence = fieldIdResult?.confidence || 0;

        // Get value normalization results
        const valueNormResult = SuccessHandlerNode.getValueFromContext(
          context,
          "value-normalization",
        );
        const originalValue = valueNormResult?.originalValue || "";
        let normalizedValue = valueNormResult?.normalizedValue || "";

        // Try to parse the normalized value if it's a JSON string
        if (
          typeof normalizedValue === "string" &&
          (normalizedValue.startsWith("{") || normalizedValue.startsWith("["))
        ) {
          try {
            normalizedValue = JSON.parse(normalizedValue);
          } catch (e) {
            SuccessHandlerNode.logger.warn(
              `Failed to parse normalized value as JSON: ${e.message}`,
            );
          }
        }

        // Get final review results
        const finalReviewResult = SuccessHandlerNode.getValueFromContext(
          context,
          "final-review",
        );
        const isValid = finalReviewResult?.isValid || false;
        const reviewReasoning = finalReviewResult?.reasoning || "";

        // Construct a comprehensive result object
        const result = {
          status: "update_successful",
          operation: operation,
          documentType: documentType,
          field: {
            name: fieldName,
            confidence: fieldConfidence,
          },
          update: {
            [fieldName]: normalizedValue,
          },
          original: {
            instruction: originalInstruction,
            value: originalValue,
          },
          validation: {
            isValid: isValid,
            reasoning: reviewReasoning,
          },
          message: `Successfully processed ${operation} for ${documentType} field "${fieldName}" with value: ${JSON.stringify(normalizedValue)}`,
          timestamp: new Date().toISOString(),
        };

        SuccessHandlerNode.logger.log(
          `Generated successful update result: ${JSON.stringify(result)}`,
        );
        return result;
      },
    });
  }

  /**
   * Helper method to safely extract values from context
   */
  private static getValueFromContext(context: AgentContext, key: string): any {
    try {
      // Try to get the parsed value first
      const value = context.get(key);

      // If the value is a string that looks like JSON, try to parse it
      if (
        typeof value === "string" &&
        (value.startsWith("{") || value.startsWith("["))
      ) {
        try {
          return JSON.parse(value);
        } catch (e) {
          SuccessHandlerNode.logger.warn(
            `Failed to parse ${key} as JSON: ${e.message}`,
          );
          return value;
        }
      }

      // If it's not a JSON string, return as is
      return value;
    } catch (e) {
      SuccessHandlerNode.logger.warn(
        `Error getting ${key} from context: ${e.message}`,
      );
      return null;
    }
  }
}
