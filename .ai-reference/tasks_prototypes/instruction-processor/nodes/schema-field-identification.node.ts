import { Injectable, Logger } from "@nestjs/common";
import { AskLLMService } from "../../llm/services/ask-llm.service";
import { LLMNode } from "../../agent-flow-engine/models/llm-node.model";
import { SchemaFieldIdentificationSchema } from "../schemas";
import { AgentContext } from "../../agent-flow-engine/models/context.model";

/**
 * Interface for field definition
 */
interface FieldDefinition {
  name: string;
  description: string;
}

/**
 * Node for identifying the schema field from an instruction
 */
@Injectable()
export class SchemaFieldIdentificationNode {
  private static readonly logger = new Logger(
    SchemaFieldIdentificationNode.name,
  );

  /**
   * Create an LLM node for schema field identification
   */
  static create(askLLMService: AskLLMService): LLMNode {
    return LLMNode.create(
      askLLMService,
      "schema-field-identification",
      "llm",
      "Identifies the schema field from the instruction",
      {
        model: "gpt-4o-mini",
        system:
          "You are a helpful assistant that identifies the specific schema field from user instructions. " +
          "Analyze the instruction carefully and determine which field in the schema it relates to. " +
          "If you believe the wrong category was selected, set wrongCategory to true. " +
          "If you cannot find a matching field, set noMatchingField to true.",
        prompt:
          "Analyze the following instruction to identify the specific schema field:\n\n" +
          "{{instruction}}\n\n" +
          "The operation was classified as: {{operation}}\n" +
          "The document type was identified as: {{documentType}}\n" +
          "The field category was selected as: {{category}}\n\n" +
          "Available fields in this category:\n{{availableFields}}\n\n" +
          "Identify the specific schema field that this instruction relates to. " +
          "If you believe the wrong category was selected, set wrongCategory to true. " +
          "If you cannot find a matching field, set noMatchingField to true. " +
          "Provide your reasoning and a confidence level (0-1) for your identification.",
        temperature: 0.1,
        schemaName: "schemaFieldIdentification",
        zodSchema: SchemaFieldIdentificationSchema,
      },
      // Input mapper function to extract context and generate available fields
      (context: AgentContext) => {
        const instruction = context.get("instruction") || "";
        const operation = context.get("operation-detection")?.operation || "";
        const documentType = context.get("document-type")?.documentType || "";
        const category = context.get("field-category")?.category || "";

        this.logger.debug(
          `Preparing field options for: documentType=${documentType}, category=${category}`,
        );

        // This is a simplified example - in a real implementation, you would
        // dynamically load the appropriate fields based on document type and category
        let availableFields = "No fields available for this category.";
        const fieldsList: FieldDefinition[] = [];

        if (documentType === "Shipment") {
          if (category === "DateTime") {
            fieldsList.push(
              {
                name: "etd",
                description:
                  "Estimated Time of Departure (ETD). Also known as: departure date, estimated departure, sail date.",
              },
              {
                name: "etaPort",
                description:
                  "Estimated Time of Arrival at Port. Represents arrival at the port of discharge, typically before final destination.",
              },
              {
                name: "etaDestination",
                description:
                  "Estimated Time of Arrival at Final Destination. Also known as: delivery date, final delivery date. Represents arrival at the final delivery location after port processing.",
              },
              {
                name: "pickupLfd",
                description:
                  "Last Free Day for pickup. Also known as: pickup last free day, collection deadline. The final day to pick up cargo without incurring charges.",
              },
              {
                name: "pickupDate",
                description:
                  "Actual pickup date. Also known as: collection date, cargo ready date. The date when cargo was or will be collected.",
              },
              {
                name: "returnLfd",
                description:
                  "Last Free Day for container return. Also known as: return last free day, container return deadline. The final day to return container without incurring charges.",
              },
              {
                name: "returnDate",
                description:
                  "Actual container return date. Also known as: container return date, equipment return. The date when container was or will be returned.",
              },
              {
                name: "releaseDate",
                description:
                  "Cargo release date. Also known as: cargo release, customs release date. The date when cargo is cleared and available for pickup.",
              },
              {
                name: "surrenderDate",
                description:
                  "Document surrender date. Also known as: document surrender, surrender, docs surrender. The date when shipping documents were or will be surrendered.",
              },
              {
                name: "adviceNoteDate",
                description:
                  "Advice note date. Also known as: advice date, notification date, notice date. The date when the advice note was issued.",
              },
            );
          } else if (category === "Identifiers") {
            fieldsList.push(
              {
                name: "hblNumber",
                description:
                  "House Bill of Lading number. Also known as: house bill, hbl, house bl, house bill of lading, bill of lading.",
              },
              {
                name: "mblNumber",
                description:
                  "Master Bill of Lading number. Only used if explicitly stated as a master bill of lading number or MBL.",
              },
              {
                name: "cargoControlNumber",
                description:
                  "Cargo Control Number for customs tracking. Also known as: ccn, cargo control, customs control number.",
              },
              {
                name: "PARS",
                description:
                  "Pre-Arrival Review System number for road shipments. Also known as: pars number, pre-arrival number.",
              },
              {
                name: "carrierCode",
                description:
                  "Carrier's identification code. Also known as: scac, carrier id, carrier code.",
              },
              {
                name: "containerNumber",
                description:
                  "Container number(s) following ISO 6346 format. Also known as: container, container id, cntr number, cntr no.",
              },
              {
                name: "pickupNumber",
                description:
                  "Pickup reference number. Also known as: pickup reference, collection number, pickup ref.",
              },
              {
                name: "vessel",
                description:
                  "Name of the vessel carrying the shipment. Also known as: vessel name, ship, ship name.",
              },
              {
                name: "voyageNumber",
                description:
                  "Voyage or flight number. Also known as: voyage id, sailing number, flight number.",
              },
              {
                name: "transactionNumber",
                description:
                  "Internal transaction reference number. A unique identifier for tracking the shipment within internal systems.",
              },
              {
                name: "customsFileNumber",
                description:
                  "Customs file reference number. A unique identifier assigned by customs for tracking the clearance process.",
              },
              {
                name: "portCode",
                description:
                  "Customs office code where entry/clearance is processed. Must be a valid port identifier code.",
              },
              {
                name: "portOfExit",
                description:
                  "UNLOCODE for the physical port where cargo exits the customs territory.",
              },
              {
                name: "subLocation",
                description:
                  "Sub-location code or reference. Specific location within a larger facility or port area.",
              },
            );
          } else if (category === "Location") {
            fieldsList.push(
              {
                name: "portOfDischarge",
                description:
                  "Complete details of the physical port where cargo is unloaded.",
              },
              {
                name: "portOfLoading",
                description:
                  "Complete details of the physical port where cargo is loaded onto the vessel/aircraft.",
              },
              {
                name: "placeOfDelivery",
                description: "Final delivery location.",
              },
            );
          } else if (category === "Measurements") {
            fieldsList.push(
              {
                name: "volume",
                description:
                  "Volume of the shipment. Used to specify the total cubic volume of the cargo.",
              },
              {
                name: "volumeUOM",
                description:
                  "Unit of measure for volume (cbm for cubic meters, cft for cubic feet).",
              },
              {
                name: "weight",
                description:
                  "Weight of the shipment. For FCL shipments, must be within container type weight limits.",
              },
              {
                name: "weightUOM",
                description:
                  "Unit of measure for weight (e.g., kgm for kilograms, lbr for pounds).",
              },
              {
                name: "quantity",
                description:
                  "Quantity of items in the shipment. Represents the total count of shipping units.",
              },
              {
                name: "quantityUOM",
                description:
                  "Unit of measure for quantity (e.g., box, package, pallet, carton).",
              },
              {
                name: "containerType",
                description:
                  "Type of container for FCL shipments. Also known as: container size, equipment type, cntr type.",
              },
              {
                name: "modeOfTransport",
                description:
                  "The mode of transportation. Also known as: shipping method, transport type, service, mode.",
              },
            );
          } else if (category === "TradePartner") {
            fieldsList.push(
              {
                name: "carrier",
                description: "The carrier handling the shipment.",
              },
              {
                name: "manufacturer",
                description: "The manufacturer of the goods.",
              },
              {
                name: "shipper",
                description: "The party shipping the goods.",
              },
              {
                name: "consignee",
                description: "The party receiving the goods.",
              },
              {
                name: "forwarder",
                description: "The freight forwarder.",
              },
              {
                name: "trucker",
                description: "The trucking company.",
              },
              {
                name: "pickupLocation",
                description: "Location for pickup.",
              },
            );
          } else if (category === "Documents") {
            fieldsList.push({
              name: "specialHandlingInstructions",
              description:
                "Special handling instructions for the shipment. Also known as: special instructions, handling instructions.",
            });
          }
        } else if (documentType === "Commercial Invoice") {
          if (category === "Identifiers") {
            fieldsList.push(
              { name: "invoiceNumber", description: "Invoice number" },
              { name: "poNumber", description: "Purchase Order number" },
              {
                name: "shipmentReference",
                description: "Reference to related shipment",
              },
            );
          } else if (category === "DateTime") {
            fieldsList.push(
              { name: "invoiceDate", description: "Date of invoice issuance" },
              { name: "poDate", description: "Purchase Order date" },
              { name: "shipmentDate", description: "Date of shipment" },
            );
          } else if (category === "Measurements") {
            fieldsList.push(
              { name: "totalAmount", description: "Total invoice amount" },
              {
                name: "currency",
                description: "Currency code (e.g., USD, EUR)",
              },
              { name: "totalWeight", description: "Total weight of goods" },
              {
                name: "weightUOM",
                description: "Unit of measurement for weight",
              },
            );
          }
        }

        // Convert list to formatted string for prompt
        if (fieldsList.length > 0) {
          availableFields = fieldsList
            .map((field) => `${field.name}: ${field.description}`)
            .join("\n");

          this.logger.debug(
            `Presenting ${fieldsList.length} fields to the LLM:`,
          );
          fieldsList.forEach((field) => {
            this.logger.debug(`- ${field.name}: ${field.description}`);
          });
        } else {
          this.logger.debug(
            `No fields available for documentType=${documentType}, category=${category}`,
          );
        }

        // Return a simple object with variables for the LLM prompt template
        return {
          instruction,
          operation,
          documentType,
          category,
          availableFields,
        };
      },
    );
  }
}
