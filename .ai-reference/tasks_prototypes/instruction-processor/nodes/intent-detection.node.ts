import { AgentContext } from "../../agent-flow-engine/models/context.model";
import { AskLLMService } from "../../llm/services/ask-llm.service";
import { Injectable } from "@nestjs/common";
import { z } from "zod";

// Define the output schema for intent detection
const outputIntentSchema = z.object({
  intent: z.string().describe("The detected intent from the instruction"),
});

/**
 * Service that detects intent from user instructions
 */
@Injectable()
export class IntentDetectionNode {
  constructor(private readonly askLLMService: AskLLMService) {}

  /**
   * Detect intent from a given instruction
   * @param instruction The instruction to analyze
   * @returns The detected intent
   */
  async detectIntent(instruction: string): Promise<string> {
    // Use the askLLMService to detect intent using LLM
    const response = await this.askLLMService.ask({
      model: "gpt-4o-mini",
      system:
        "You are a helpful assistant that analyzes instructions and determines their intent. Return only the intent as a single word or short phrase.",
      prompt:
        "Determine the intent of the following instruction: {{instruction}}.",
      variables: {
        instruction,
      },
      temperature: 0.1,
      schemaName: "intent",
      zodSchema: outputIntentSchema,
    });

    // If parsed output is available, use it
    if (response.parsed?.intent) {
      return response.parsed.intent;
    }

    // Fall back to content from the message
    if (typeof response.message.content === "string") {
      return response.message.content.trim();
    }

    // If content is not a string (could be ContentPart[]), handle accordingly
    return "unknown_intent";
  }
}
