import { Injectable, Logger } from "@nestjs/common";
import { Node } from "../../agent-flow-engine";
import { AgentContext } from "../../agent-flow-engine/models/context.model";

/**
 * Node for handling operations that require human review
 */
@Injectable()
export class HumanReviewNode {
  private static readonly logger = new Logger(HumanReviewNode.name);

  /**
   * Create a standard node for handling human review
   */
  static create(): Node {
    return new Node({
      id: "human-review",
      type: "standard",
      description: "Routes update for human review when needed",
      processFn: async (input) => {
        HumanReviewNode.logger.log("Processing human review node");

        try {
          // Determine why human review is needed
          const reason = HumanReviewNode.determineReviewReason(input);

          return {
            reason,
            timestamp: new Date().toISOString(),
          };
        } catch (error) {
          HumanReviewNode.logger.error(
            `Error processing human review: ${error.message}`,
          );

          return {
            reason: "Error occurred during processing",
            error: error.message,
            timestamp: new Date().toISOString(),
          };
        }
      },
    });
  }

  /**
   * Determine the reason for human review based on context
   */
  private static determineReviewReason(context: any): string {
    // Check specific node outputs to determine the reason

    // If operation detection failed
    if (context["operation-detection"]) {
      const operation = context["operation-detection"].operation;
      if (operation === "Other") {
        return "Unclear operation type";
      }
    }

    // If document type is unsupported
    if (context["document-type"]) {
      const docType = context["document-type"].documentType;
      if (docType === "Other" || docType === "Unsure") {
        return "Unrecognized document type";
      }
    }

    // If field category is unclear
    if (
      context["field-category"] &&
      context["field-category"].category === "Unsure"
    ) {
      return "Unclear field category";
    }

    // If no matching field found
    if (context["schema-field-identification"]) {
      const fieldId = context["schema-field-identification"];
      if (fieldId.noMatchingField) {
        return "No matching field found";
      }
      if (fieldId.wrongCategory) {
        return "Wrong field category selected";
      }
    }

    // If value normalization failed
    if (context["value-normalization"]) {
      const valueNorm = context["value-normalization"];

      // Parse if it's a string
      let parsedValueNorm = valueNorm;
      if (typeof valueNorm === "string") {
        try {
          parsedValueNorm = JSON.parse(valueNorm);
        } catch (e) {
          // Keep the original if parsing fails
        }
      }

      if (parsedValueNorm.needsHumanReview) {
        return (
          parsedValueNorm.reasoning || "Value normalization needs human review"
        );
      }
      if (parsedValueNorm.success === false) {
        return "Value normalization failed";
      }
    }

    // If final review determined it's fundamentally wrong
    if (context["final-review"]) {
      const finalReview = context["final-review"];

      // Parse if it's a string
      let parsedReview = finalReview;
      if (typeof finalReview === "string") {
        try {
          parsedReview = JSON.parse(finalReview);
        } catch (e) {
          // Keep the original if parsing fails
        }
      }

      if (parsedReview.fundamentallyWrong) {
        return parsedReview.reasoning || "Update is fundamentally flawed";
      }
    }

    // Default fallback
    return "Unspecified reason for human review";
  }

  /**
   * Get information about the last processing stage
   */
  private static getLastProcessingStage(context: AgentContext): any {
    const stages = [
      "final-review",
      "value-normalization",
      "schema-field-identification",
      "field-category",
      "document-type",
      "operation-detection",
    ];

    for (const stage of stages) {
      if (context.has(stage)) {
        return {
          stage: stage,
          data: HumanReviewNode.getValueFromContext(context, stage),
        };
      }
    }

    return { stage: "unknown", data: null };
  }

  /**
   * Helper method to safely extract values from context
   */
  private static getValueFromContext(context: AgentContext, key: string): any {
    try {
      // Try to get the value
      const value = context.get(key);

      // If the value is a string that looks like JSON, try to parse it
      if (
        typeof value === "string" &&
        (value.startsWith("{") || value.startsWith("["))
      ) {
        try {
          return JSON.parse(value);
        } catch (e) {
          HumanReviewNode.logger.warn(
            `Failed to parse ${key} as JSON: ${e.message}`,
          );
          return value;
        }
      }

      // If it's not a JSON string, return as is
      return value;
    } catch (e) {
      HumanReviewNode.logger.warn(
        `Error getting ${key} from context: ${e.message}`,
      );
      return null;
    }
  }
}
