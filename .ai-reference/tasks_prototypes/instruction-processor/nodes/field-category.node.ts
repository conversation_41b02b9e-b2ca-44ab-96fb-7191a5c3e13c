import { Injectable } from "@nestjs/common";
import { AskLLMService } from "../../llm/services/ask-llm.service";
import { LLMNode } from "../../agent-flow-engine/models/llm-node.model";
import { FieldCategorySchema } from "../schemas";
import { AgentContext } from "../../agent-flow-engine/models/context.model";

/**
 * Node for selecting the field category from an instruction
 */
@Injectable()
export class FieldCategoryNode {
  /**
   * Create an LLM node for field category selection
   */
  static create(askLLMService: AskLLMService): LLMNode {
    return LLMNode.create(
      askLLMService,
      "field-category",
      "llm",
      "Selects the field category from the instruction",
      {
        model: "gpt-4o-mini",
        system:
          "You are a helpful assistant that selects the appropriate field category from user instructions. " +
          "Analyze the instruction carefully and determine which category of fields it relates to: " +
          "DateTime (dates and times), Location (places and addresses), TradePartner (companies and people), " +
          "Measurements (weights, volumes, quantities), Identifiers (reference numbers, codes), " +
          "Documents (document types and statuses), or Unsure if you cannot determine.",
        prompt:
          "Analyze the following instruction to select the appropriate field category:\n\n" +
          "{{instruction}}\n\n" +
          "The operation was classified as: {{operation}}\n" +
          "The document type was identified as: {{documentType}}\n\n" +
          "Select the most appropriate field category from: DateTime, Location, TradePartner, " +
          "Measurements, Identifiers, Documents, or Unsure. " +
          "Provide your reasoning for the selection.",
        temperature: 0.1,
        schemaName: "fieldCategory",
        zodSchema: FieldCategorySchema,
      },
      // Input mapper function to extract instruction, operation, and document type from context
      (context: AgentContext) => {
        const instruction = context.get("instruction") || "";
        const operation = context.get("operation-detection")?.operation || "";
        const documentType = context.get("document-type")?.documentType || "";

        // Return a simple object with variables for the LLM prompt template
        return {
          instruction,
          operation,
          documentType,
        };
      },
    );
  }
}
