import { Injectable, Logger } from "@nestjs/common";
import { AskLLMService } from "../../llm/services/ask-llm.service";
import { LLMNode } from "../../agent-flow-engine/models/llm-node.model";
import { OperationDetectionSchema } from "../schemas";
import { AgentContext } from "../../agent-flow-engine/models/context.model";

/**
 * Node for detecting the operation type from an instruction
 */
@Injectable()
export class OperationDetectionNode {
  private static readonly logger = new Logger(OperationDetectionNode.name);

  /**
   * Create an LLM node for operation detection
   */
  static create(askLLMService: AskLLMService): LLMNode {
    return LLMNode.create(
      askLLMService,
      "operation-detection",
      "llm",
      "Detects the operation type from the instruction",
      {
        model: "gpt-4o-mini",
        system:
          "You are a helpful assistant that determines the operation type from user instructions. " +
          "Analyze the instruction carefully and determine if it's a Query (asking for information), " +
          "Create (creating a new record), Update (modifying an existing record), SPAM (irrelevant or spam), " +
          "or Other (doesn't fit any category).",
        prompt:
          "Analyze the following instruction to determine its operation type:\n\n" +
          "{{instruction}}\n\n" +
          "Determine if this is a Query, Create, Update, SPAM, or Other operation. " +
          "Provide your reasoning for the classification.",
        temperature: 0.1,
        schemaName: "operationDetection",
        zodSchema: OperationDetectionSchema,
      },
      // Input mapper function to prepare context for processing
      (context: AgentContext) => {
        const instruction = context.get("instruction") || "";

        this.logger.debug(
          `Preparing context for operation detection: instruction="${instruction}"`,
        );

        // Return a simple object with variables for the LLM prompt template
        return { instruction };
      },
      // Optional error handler function
      (context: AgentContext, error: Error) => {
        this.logger.error(`Error in operation detection: ${error.message}`);
        // On error, set a default "Other" operation type to ensure workflow continues
        const updatedContext = context.clone();
        updatedContext.set("operation-detection", {
          operation: "Other",
          reasoning: "Error during processing",
        });
        return updatedContext;
      },
    );
  }
}
