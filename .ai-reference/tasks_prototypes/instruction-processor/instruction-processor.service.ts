import { Injectable, Logger } from "@nestjs/common";
import {
  AgentFlowEngineService,
  WorkflowGraph,
  Edge,
  BaseNode,
  Node,
  NodeConfig,
  AgentContext,
} from "../agent-flow-engine";
import { AskLLMService } from "../llm/services/ask-llm.service";
import {
  OperationDetectionNode,
  DocumentTypeNode,
  FieldCategoryNode,
  SchemaFieldIdentificationNode,
  ValueNormalizationNode,
  FinalReviewNode,
  HumanReviewNode,
  QueryHandlerNode,
  SpamHandlerNode,
  SuccessHandlerNode,
} from "./nodes";
import {
  createBooleanCondition,
  getSafeValue,
} from "../agent-flow-engine/utils/edge-condition-helpers";

@Injectable()
export class InstructionProcessorService {
  private readonly logger = new Logger(InstructionProcessorService.name);

  constructor(
    private readonly agentFlowEngineService: AgentFlowEngineService,
    private readonly askLLMService: AskLLMService,
  ) {}

  /**
   * Build the intent-to-schema workflow graph
   * @returns The workflow graph
   */
  buildIntentToSchemaWorkflow(): WorkflowGraph {
    // Create a new workflow graph with the start node
    const graph = new WorkflowGraph("operation-detection");

    // Create nodes
    const operationDetectionNode = OperationDetectionNode.create(
      this.askLLMService,
    );
    const documentTypeNode = DocumentTypeNode.create(this.askLLMService);
    const fieldCategoryNode = FieldCategoryNode.create(this.askLLMService);
    const schemaFieldIdentificationNode = SchemaFieldIdentificationNode.create(
      this.askLLMService,
    );
    const valueNormalizationNode = ValueNormalizationNode.create(
      this.askLLMService,
    );
    const finalReviewNode = FinalReviewNode.create(this.askLLMService);
    const humanReviewNode = HumanReviewNode.create();
    const queryHandlerNode = QueryHandlerNode.create();
    const spamHandlerNode = SpamHandlerNode.create();
    const successHandlerNode = SuccessHandlerNode.create();

    // Add nodes to the graph
    graph.addNode(operationDetectionNode);
    graph.addNode(documentTypeNode);
    graph.addNode(fieldCategoryNode);
    graph.addNode(schemaFieldIdentificationNode);
    graph.addNode(valueNormalizationNode);
    graph.addNode(finalReviewNode);
    graph.addNode(humanReviewNode);
    graph.addNode(queryHandlerNode);
    graph.addNode(spamHandlerNode);
    graph.addNode(successHandlerNode);

    // Add edges for Stage 1: Operation & Spam Detection
    graph.addEdge(
      new Edge({
        id: "operation-to-query",
        from: "operation-detection",
        to: "query-handler",
        description: "Route to query handler when operation is Query",
        conditionFn: (context) =>
          Promise.resolve(
            context.get("operation-detection")?.operation === "Query",
          ),
      }),
    );

    graph.addEdge(
      new Edge({
        id: "operation-to-document",
        from: "operation-detection",
        to: "document-type",
        description:
          "Route to document type when operation is Create or Update",
        conditionFn: (context) => {
          const operation = context.get("operation-detection")?.operation;
          return Promise.resolve(
            operation === "Create" || operation === "Update",
          );
        },
      }),
    );

    graph.addEdge(
      new Edge({
        id: "operation-to-spam",
        from: "operation-detection",
        to: "spam-handler",
        description: "Route to spam handler when operation is SPAM",
        conditionFn: (context) =>
          Promise.resolve(
            context.get("operation-detection")?.operation === "SPAM",
          ),
      }),
    );

    graph.addEdge(
      new Edge({
        id: "operation-to-human-review",
        from: "operation-detection",
        to: "human-review",
        description:
          "Route to human review when operation is Other or if no other edges match",
        conditionFn: (context) => {
          const operationResult = context.get("operation-detection");
          const operation = operationResult?.operation;

          // Match if:
          // 1. Operation is explicitly "Other"
          // 2. Operation result is undefined or missing (fallback case)
          // 3. Operation is not any of the valid types (fallback case)

          const isOtherExplicit = operation === "Other";
          const isMissingResult =
            !operationResult || !operationResult.operation || !operation;
          const isInvalidType =
            operation &&
            !["Query", "Create", "Update", "SPAM", "Other"].includes(operation);

          const shouldRoute =
            isOtherExplicit || isMissingResult || isInvalidType;

          if (shouldRoute) {
            this.logger.debug(
              `Routing to human review: ${
                isOtherExplicit
                  ? "Explicit Other"
                  : isMissingResult
                    ? "Missing result"
                    : "Invalid operation type"
              }`,
            );
          }

          return Promise.resolve(shouldRoute);
        },
      }),
    );

    // Add edges for Stage 2: Document Type Identification
    graph.addEdge(
      new Edge({
        id: "document-to-field-category",
        from: "document-type",
        to: "field-category",
        description: "Route to field category when document type is identified",
        conditionFn: (context) => {
          const documentType = context.get("document-type")?.documentType;
          return Promise.resolve(
            documentType === "Shipment" ||
              documentType === "Commercial Invoice",
          );
        },
      }),
    );

    graph.addEdge(
      new Edge({
        id: "document-to-human-review",
        from: "document-type",
        to: "human-review",
        description: "Route to human review when document type is Other",
        conditionFn: (context) =>
          Promise.resolve(
            context.get("document-type")?.documentType === "Other",
          ),
      }),
    );

    graph.addEdge(
      new Edge({
        id: "document-to-operation",
        from: "document-type",
        to: "operation-detection",
        description:
          "Route back to operation detection when document type is Wrong Operation",
        conditionFn: (context) =>
          Promise.resolve(
            context.get("document-type")?.documentType === "Wrong Operation",
          ),
      }),
    );

    // Add edges for Stage 3: Field Category Selection
    graph.addEdge(
      new Edge({
        id: "category-to-field",
        from: "field-category",
        to: "schema-field-identification",
        description:
          "Route to schema field identification when category is selected",
        conditionFn: (context) => {
          const category = context.get("field-category")?.category;
          return Promise.resolve(category !== "Unsure");
        },
      }),
    );

    graph.addEdge(
      new Edge({
        id: "category-to-human-review",
        from: "field-category",
        to: "human-review",
        description: "Route to human review when category is Unsure",
        conditionFn: (context) =>
          Promise.resolve(context.get("field-category")?.category === "Unsure"),
      }),
    );

    // Add edges for Stage 4: Schema Field Identification
    graph.addEdge(
      new Edge({
        id: "field-to-category",
        from: "schema-field-identification",
        to: "field-category",
        description:
          "Route back to field category when wrong category is selected",
        conditionFn: createBooleanCondition(
          "schema-field-identification",
          "wrongCategory",
        ),
      }),
    );

    graph.addEdge(
      new Edge({
        id: "field-to-human-review",
        from: "schema-field-identification",
        to: "human-review",
        description: "Route to human review when no matching field is found",
        conditionFn: createBooleanCondition(
          "schema-field-identification",
          "noMatchingField",
        ),
      }),
    );

    graph.addEdge(
      new Edge({
        id: "field-to-value",
        from: "schema-field-identification",
        to: "value-normalization",
        description: "Route to value normalization when field is identified",
        conditionFn: (context) => {
          const result = getSafeValue(context, "schema-field-identification");
          return Promise.resolve(
            result &&
              !result.wrongCategory &&
              !result.noMatchingField &&
              result.fieldName,
          );
        },
      }),
    );

    // Add edges for Stage 5: Value Normalization
    graph.addEdge(
      new Edge({
        id: "value-to-human-review",
        from: "value-normalization",
        to: "human-review",
        description:
          "Route to human review when normalization fails and needs human review",
        conditionFn: (context) => {
          const result = getSafeValue(context, "value-normalization");
          // Route to human review if needsHumanReview is true or if success is false
          return Promise.resolve(
            result &&
              (result.needsHumanReview === true || result.success === false),
          );
        },
      }),
    );

    graph.addEdge(
      new Edge({
        id: "value-to-final",
        from: "value-normalization",
        to: "final-review",
        description: "Route to final review when normalization succeeds",
        conditionFn: (context) => {
          const result = getSafeValue(context, "value-normalization");
          // Route to final review if success is true and needsHumanReview is not true
          return Promise.resolve(
            result &&
              result.success === true &&
              result.needsHumanReview !== true,
          );
        },
      }),
    );

    // Add edges for Stage 6: Final Review
    graph.addEdge(
      new Edge({
        id: "final-to-success",
        from: "final-review",
        to: "success-handler",
        description: "Route to success handler when validation succeeds",
        conditionFn: createBooleanCondition("final-review", "isValid"),
      }),
    );

    graph.addEdge(
      new Edge({
        id: "final-to-value",
        from: "final-review",
        to: "value-normalization",
        description: "Route back to value normalization when issue is fixable",
        conditionFn: createBooleanCondition("final-review", "fixableIssue"),
      }),
    );

    graph.addEdge(
      new Edge({
        id: "final-to-human-review",
        from: "final-review",
        to: "human-review",
        description: "Route to human review when update is fundamentally wrong",
        conditionFn: createBooleanCondition(
          "final-review",
          "fundamentallyWrong",
        ),
      }),
    );

    return graph;
  }

  /**
   * Run the intent-to-schema workflow with the given instruction
   * @param instruction The instruction to process
   * @returns The result of the workflow execution
   */
  async runIntentToSchemaWorkflow(instruction: string): Promise<any> {
    this.logger.log(`Processing instruction: ${instruction}`);

    // Validate the input
    if (!instruction) {
      throw new Error("Instruction is required");
    }

    // Create workflow graph
    const workflow = this.buildIntentToSchemaWorkflow();

    // Create a proper AgentContext instance with the instruction
    const context = new AgentContext({ instruction });

    // Debug log the context before execution
    this.logger.debug(
      `Initial context before execution: ${JSON.stringify(context)}`,
    );

    // Create a tracking callback that calls our trackWorkflowExecution method
    const trackingCallback = (
      nodeId: string,
      edgeId?: string,
      status?: string,
    ) => {
      this.trackWorkflowExecution(nodeId, edgeId, status);
    };

    // Execute workflow
    const executor = this.agentFlowEngineService.createExecutor(
      workflow,
      trackingCallback,
    );

    // Add a debug message to track execution
    this.logger.debug("About to execute workflow with AgentContext");

    const result = await executor.execute(context);

    // Log the result to help debug
    this.logger.debug(`Workflow execution result: ${JSON.stringify(result)}`);

    // Define the type for the final output
    type WorkflowOutput = {
      status: string;
      message: string;
      data: any; // Using 'any' to allow different data structures
      timestamp: string;
    };

    // Identify the final output based on which node was last executed
    let finalOutput: WorkflowOutput = {
      status: "incomplete",
      message: "Workflow did not reach a terminal node",
      data: null,
      timestamp: new Date().toISOString(),
    };

    // Check for success-handler output (successful update)
    if (result.has("success-handler")) {
      const successData = result.get("success-handler");

      finalOutput = {
        status: "success",
        message: "Update processed successfully",
        data: successData,
        timestamp: new Date().toISOString(),
      };
    }
    // Check for human-review output
    else if (result.has("human-review")) {
      // Get the human review data safely
      const humanReviewData = result.get("human-review");

      // Create a safe reason with fallback
      let reason = "Unspecified reason";
      if (humanReviewData && typeof humanReviewData === "object") {
        reason = humanReviewData.reason || reason;
      } else if (typeof humanReviewData === "string") {
        try {
          const parsed = JSON.parse(humanReviewData);
          reason = parsed.reason || reason;
        } catch (e) {
          this.logger.warn(`Failed to parse human-review data: ${e.message}`);
        }
      }

      finalOutput = {
        status: "human_review_needed",
        message: "This update requires human review",
        data: {
          reason,
          lastProcessedNode: result?.metadata?.lastNodeId || "unknown",
          context: this.extractRelevantContext(result),
        },
        timestamp: new Date().toISOString(),
      };
    }
    // Check for query-handler output
    else if (result.has("query-handler")) {
      const queryData = result.get("query-handler");

      finalOutput = {
        status: "query_completed",
        message: "Query processed successfully",
        data: queryData,
        timestamp: new Date().toISOString(),
      };
    }
    // Check for spam-handler output
    else if (result.has("spam-handler")) {
      const spamData = result.get("spam-handler");

      finalOutput = {
        status: "spam_detected",
        message: "Email marked as spam",
        data: spamData,
        timestamp: new Date().toISOString(),
      };
    }

    this.logger.log(`Workflow final output: ${JSON.stringify(finalOutput)}`);

    return finalOutput;
  }

  /**
   * Extract only the relevant context data to include in the final output
   * @param context The full agent context
   * @returns Object with only the relevant context data
   */
  private extractRelevantContext(context: AgentContext): Record<string, any> {
    const relevantKeys = [
      "instruction",
      "operation-detection",
      "document-type",
      "field-category",
      "schema-field-identification",
      "value-normalization",
      "final-review",
    ];

    const relevantContext: Record<string, any> = {};

    // Extract only the relevant context values for debugging
    for (const key of relevantKeys) {
      if (context.has(key)) {
        relevantContext[key] = context.get(key);
      }
    }

    return relevantContext;
  }

  buildConditionalWorkflow(): WorkflowGraph {
    const graph = new WorkflowGraph("start");

    // Create nodes
    const startNode = new Node({
      id: "start",
      type: "standard",
      description: "Start node that initializes the workflow",
      processFn: async () => ({ ready: true }),
    });

    const pathANode = new Node({
      id: "path-a",
      type: "standard",
      description: "Process path A",
      processFn: async () => ({ path: "A" }),
    });

    const pathBNode = new Node({
      id: "path-b",
      type: "standard",
      description: "Process path B",
      processFn: async () => ({ path: "B" }),
    });

    const endNode = new Node({
      id: "end",
      type: "standard",
      description: "End node that completes the workflow",
      processFn: async () => ({ completed: true }),
    });

    graph.addNode(startNode);
    graph.addNode(pathANode);
    graph.addNode(pathBNode);
    graph.addNode(endNode);

    graph.addEdge(
      new Edge({
        id: "start-to-a",
        from: "start",
        to: "path-a",
        description: "Path to take when usePathA is true",
        conditionFn: (context: any) =>
          Promise.resolve(context.get("usePathA") === true),
      }),
    );

    graph.addEdge(
      new Edge({
        id: "start-to-b",
        from: "start",
        to: "path-b",
        description: "Path to take when usePathA is not true",
        conditionFn: (context: any) =>
          Promise.resolve(context.get("usePathA") !== true),
      }),
    );

    graph.addEdge(
      new Edge({
        id: "a-to-end",
        from: "path-a",
        to: "end",
        description: "Connect path A to end",
      }),
    );

    graph.addEdge(
      new Edge({
        id: "b-to-end",
        from: "path-b",
        to: "end",
        description: "Connect path B to end",
      }),
    );

    return graph;
  }

  /**
   * Run the conditional workflow with the provided context
   * @param context The context to use for the workflow
   * @returns The result of the workflow execution
   */
  async runConditionalWorkflow(context: any): Promise<any> {
    this.logger.log(
      `Running conditional workflow with context: ${JSON.stringify(context)}`,
    );

    const workflow = this.buildConditionalWorkflow();
    const executor = this.agentFlowEngineService.createExecutor(workflow);

    // Create an AgentContext from the input context
    const agentContext = new AgentContext(context);

    // Debug log the AgentContext
    this.logger.debug(
      `AgentContext before execution: ${JSON.stringify(agentContext)}`,
    );

    // Execute and capture the result
    const result = await executor.execute(agentContext);

    // Log the result for debugging
    this.logger.debug(`Workflow execution result: ${JSON.stringify(result)}`);

    return result;
  }

  /**
   * Get the workflow graph structure in a format consumable by react-flow
   * @returns The workflow graph structure for visualization
   */
  getWorkflowGraphForVisualization(): any {
    // Create workflow graph
    const workflow = this.buildIntentToSchemaWorkflow();

    // Convert the workflow graph to a react-flow compatible format
    const nodes = Array.from(workflow.nodes.entries()).map(([id, node]) => ({
      id,
      type: node.type === "llm" ? "llmNode" : "standardNode",
      data: {
        label: id,
        description: node.description,
        type: node.type,
      },
      position: { x: 0, y: 0 }, // Position will be calculated by react-flow layout
    }));

    // Extract edges from the workflow graph
    const edges = Array.from(workflow.edges.entries()).map(([id, edge]) => ({
      id,
      source: edge.from,
      target: edge.to,
      label: edge.description || "",
      type: "smoothstep",
      animated: false,
    }));

    return { nodes, edges };
  }

  /**
   * Track workflow execution for visualization purposes
   * This method will be called by the workflow executor at each step
   * @param nodeId The ID of the node that was executed
   * @param edgeId The ID of the edge that was traversed (if any)
   * @param status The status of the execution (success, error, etc.)
   */
  trackWorkflowExecution(
    nodeId: string,
    edgeId?: string,
    status = "success",
  ): void {
    // In a real implementation, this would emit events to connected clients
    // For now, we'll just log the execution
    this.logger.debug(
      `Tracking workflow execution: node=${nodeId}, edge=${edgeId || "none"}, status=${status}`,
    );

    // This is where you would emit an event to connected clients via WebSockets
    // this.eventEmitter.emit('workflow-execution-update', { nodeId, edgeId, status });
  }

  // Placeholder for future workflow methods
}
