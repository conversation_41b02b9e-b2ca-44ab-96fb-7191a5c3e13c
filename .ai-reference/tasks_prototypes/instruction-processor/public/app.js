// React hooks
const { useState, useEffect, useCallback } = React;

// Debug log for initialization
console.log("App.js initializing...");
console.log("React loaded:", !!React);
console.log("ReactDOM loaded:", !!ReactDOM);
console.log("axios loaded:", !!axios);
console.log("ReactFlow loaded:", window.ReactFlow ? "Yes" : "No");

// Fallback UI component
function FallbackUI({ message }) {
  return (
    <div className="app-container">
      <div className="header">
        <h1>Workflow Visualizer</h1>
      </div>
      <div className="content" style={{ padding: "20px" }}>
        <div className="alert alert-danger">
          {message ||
            "ReactFlow library failed to load. Please check your internet connection or try a different browser."}
        </div>
      </div>
    </div>
  );
}

// Custom node components
const types = {
  standardNode: (props) => (
    <div>
      <div>
        <strong>{props.data.label}</strong>
      </div>
      <div style={{ fontSize: "10px" }}>{props.data.type}</div>
      {props.data.description && <div style={{ fontSize: "10px" }}>{props.data.description}</div>}
    </div>
  ),
  llmNode: (props) => (
    <div>
      <div>
        <strong>{props.data.label}</strong>
      </div>
      <div style={{ fontSize: "10px" }}>{props.data.type}</div>
      {props.data.description && <div style={{ fontSize: "10px" }}>{props.data.description}</div>}
    </div>
  )
};

// Calculate node positions in a grid layout
function getNodePosition(index) {
  const baseX = 50;
  const baseY = 100;
  const nodesPerRow = 3;
  const nodeGap = 250;

  const row = Math.floor(index / nodesPerRow);
  const col = index % nodesPerRow;

  return {
    x: baseX + col * nodeGap,
    y: baseY + row * nodeGap
  };
}

// Main application component
function App() {
  console.log("App component rendering...");

  // Check if ReactFlow is available
  if (!window.ReactFlow) {
    console.error("ReactFlow is not available on window object");
    return <FallbackUI />;
  }

  // Extract components from ReactFlow
  const { ReactFlow, Background, Controls, MiniMap, useNodesState, useEdgesState } = window.ReactFlow;

  console.log("ReactFlow components extracted:", {
    hasReactFlow: !!ReactFlow,
    hasBackground: !!Background,
    hasControls: !!Controls,
    hasMiniMap: !!MiniMap,
    hasNodesState: !!useNodesState,
    hasEdgesState: !!useEdgesState
  });

  // Component state
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [instruction, setInstruction] = useState("");
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);
  const [apiError, setApiError] = useState(null);

  // Load workflow graph on component mount
  useEffect(() => {
    console.log("Fetching workflow graph from API...");

    async function loadWorkflowGraph() {
      try {
        setLoading(true);
        setError(null);
        setApiError(null);

        console.log("Calling API endpoint: /api/instruction/workflow-graph");
        const response = await axios.get("/api/instruction/workflow-graph");
        console.log("Workflow graph loaded:", response.data);

        if (response.data && response.data.nodes && response.data.edges) {
          // Position nodes in a grid layout
          const positionedNodes = response.data.nodes.map((node, index) => ({
            ...node,
            position: getNodePosition(index)
          }));

          console.log("Setting nodes:", positionedNodes);
          console.log("Setting edges:", response.data.edges);

          setNodes(positionedNodes);
          setEdges(response.data.edges);
        } else {
          throw new Error("Invalid workflow graph data");
        }
      } catch (error) {
        console.error("Error loading workflow graph:", error);
        setError("Failed to load workflow graph");
        setApiError(error.toString());
        if (error.response) {
          console.error("API response error:", error.response.data);
          console.error("Status:", error.response.status);
        }
      } finally {
        setLoading(false);
      }
    }

    loadWorkflowGraph();
  }, []);

  // Handle instruction submission
  const handleSubmit = useCallback(async () => {
    if (!instruction.trim()) return;

    try {
      setLoading(true);
      setResult(null);
      setError(null);
      setApiError(null);

      // Reset node and edge styles
      setNodes((nodes) => nodes.map((node) => ({ ...node, className: "" })));
      setEdges((edges) => edges.map((edge) => ({ ...edge, className: "", animated: false })));

      console.log("Submitting instruction:", instruction);

      // Process the instruction
      const response = await axios.post("/api/instruction/process", {
        instruction
      });
      console.log("Instruction result:", response.data);

      if (response.data && response.data.result) {
        setResult(response.data.result);
      } else {
        throw new Error("Invalid response data");
      }
    } catch (error) {
      console.error("Error processing instruction:", error);
      setError("Failed to process instruction");
      setApiError(error.toString());
      if (error.response) {
        console.error("API response error:", error.response.data);
        console.error("Status:", error.response.status);
      }
    } finally {
      setLoading(false);
    }
  }, [instruction]);

  return (
    <div className="app-container">
      <div className="header">
        <h1>Workflow Visualizer</h1>
      </div>
      <div className="content">
        <div className="sidebar">
          <div className="mb-3">
            <label htmlFor="instruction" className="form-label">
              Instruction
            </label>
            <textarea
              id="instruction"
              className="form-control"
              value={instruction}
              onChange={(e) => setInstruction(e.target.value)}
              rows="3"
              placeholder="Enter your instruction..."
            ></textarea>
          </div>
          <button
            className="btn btn-primary mb-3"
            onClick={handleSubmit}
            disabled={loading || !instruction.trim()}
          >
            {loading ? "Processing..." : "Process Instruction"}
          </button>

          {error && (
            <div className="alert alert-danger mt-3">
              <p>{error}</p>
              {apiError && (
                <details>
                  <summary>Error details</summary>
                  <pre className="mt-2" style={{ fontSize: "10px" }}>
                    {apiError}
                  </pre>
                </details>
              )}
            </div>
          )}

          {result && (
            <div className="mt-3">
              <h5>Result</h5>
              <pre className="bg-light p-2 rounded" style={{ maxHeight: "300px", overflow: "auto" }}>
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>
        <div className="flow-container">
          {nodes.length > 0 ? (
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              types={types}
              fitView
            >
              <Background />
              <Controls />
              <MiniMap />
            </ReactFlow>
          ) : loading ? (
            <div className="d-flex justify-content-center align-items-center h-100">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : (
            <div className="d-flex justify-content-center align-items-center h-100">
              <div className="alert alert-info">
                <p>No workflow data available</p>
                {apiError && (
                  <details>
                    <summary>Error details</summary>
                    <pre className="mt-2" style={{ fontSize: "10px" }}>
                      {apiError}
                    </pre>
                  </details>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Main render logic
try {
  console.log("Starting app render...");
  console.log("ReactFlow availability:", !!window.ReactFlow);
  ReactDOM.render(<App />, document.getElementById("root"));
  console.log("App rendered successfully!");
} catch (error) {
  console.error("Error rendering application:", error);
  try {
    ReactDOM.render(
      <FallbackUI message={`Failed to render the application: ${error.message}`} />,
      document.getElementById("root")
    );
    console.log("Fallback UI rendered successfully!");
  } catch (fallbackError) {
    console.error("Critical error rendering fallback UI:", fallbackError);
    document.getElementById("root").innerHTML = `
      <div class="alert alert-danger m-3">
        Critical error: ${error.message}<br>
        Fallback error: ${fallbackError.message}
      </div>
    `;
  }
}
