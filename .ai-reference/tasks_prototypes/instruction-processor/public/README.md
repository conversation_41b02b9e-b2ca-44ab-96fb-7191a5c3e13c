# Workflow Visualizer

This is a visualization tool for the node-based workflow system used in the instruction processor. It allows you to see the structure of the workflow and track execution in real-time.

## Features

- Interactive visualization of the workflow graph
- Submit instructions through the UI
- View node and edge execution status
- See the final result of workflow execution

## Usage

1. Start the NestJS application
2. Open your browser to http://localhost:3000
3. The workflow graph will automatically load
4. Enter an instruction in the sidebar and click "Process Instruction"
5. Watch as the nodes and edges highlight to show execution progress
6. View the result in the sidebar when processing completes

## Implementation Details

This visualization uses:

- React for the UI
- React Flow for rendering the graph
- Axios for API calls

In a production implementation, WebSockets would be used to receive real-time updates about workflow execution. The current demo simulates this based on the final result structure.

## Example Instructions to Try

- "Update the delivery date to March 15th"
- "Change the container number to MSCU1234567"
- "Set the estimated arrival time to next Friday"
- "Change the weight to 1500 kg"

## Technical Notes

The visualization connects to the following API endpoints:

- GET /api/instruction/workflow-graph - Retrieves the workflow graph structure
- POST /api/instruction/process - Processes an instruction through the workflow

The workflow node execution is tracked via callback functions that update the UI in real-time.
