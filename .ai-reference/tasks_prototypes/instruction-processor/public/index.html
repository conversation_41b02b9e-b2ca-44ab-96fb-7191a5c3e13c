<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Workflow Visualizer</title>
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />
    <!-- ReactFlow CSS from jsDelivr -->
    <link
      href="https://cdn.jsdelivr.net/npm/reactflow@11.10.1/dist/style.min.css"
      rel="stylesheet"
    />
    <style>
      body,
      html,
      #root {
        height: 100%;
        margin: 0;
        font-family:
          -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
          "Helvetica Neue", Arial, sans-serif;
      }
      .app-container {
        display: flex;
        flex-direction: column;
        height: 100vh;
      }
      .header {
        padding: 1rem;
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
      }
      .content {
        display: flex;
        flex: 1;
      }
      .sidebar {
        width: 300px;
        padding: 1rem;
        background-color: #f8f9fa;
        border-right: 1px solid #dee2e6;
        overflow-y: auto;
      }
      .flow-container {
        flex: 1;
        height: 100%;
      }
      .react-flow__node {
        padding: 10px;
        border-radius: 3px;
        width: 150px;
        font-size: 12px;
        color: #222;
        text-align: center;
        border-width: 1px;
        border-style: solid;
      }
      .react-flow__node-standardNode {
        background-color: #f5f5f5;
        border-color: #ddd;
      }
      .react-flow__node-llmNode {
        background-color: #e6f7ff;
        border-color: #91d5ff;
      }
      .react-flow__node.executed {
        background-color: #d4edda;
        border-color: #c3e6cb;
      }
      .react-flow__node.executing {
        background-color: #fff3cd;
        border-color: #ffeeba;
        animation: pulse 1.5s infinite;
      }
      .react-flow__node.error {
        background-color: #f8d7da;
        border-color: #f5c6cb;
      }
      .react-flow__edge.traversed path {
        stroke: #28a745;
        stroke-width: 2;
      }
      .react-flow__edge.skipped path {
        stroke: #dc3545;
        stroke-width: 1;
        stroke-dasharray: 5, 5;
      }
      @keyframes pulse {
        0% {
          opacity: 1;
        }
        50% {
          opacity: 0.6;
        }
        100% {
          opacity: 1;
        }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- Loading indicator -->
      <div
        class="d-flex justify-content-center align-items-center"
        style="height: 100vh"
      >
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
      </div>
    </div>

    <!-- Dependencies -->
    <script
      src="https://cdn.jsdelivr.net/npm/react@17/umd/react.development.js"
      crossorigin
    ></script>
    <script
      src="https://cdn.jsdelivr.net/npm/react-dom@17/umd/react-dom.development.js"
      crossorigin
    ></script>
    <script
      src="https://cdn.jsdelivr.net/npm/axios@1.3.4/dist/axios.min.js"
      crossorigin
    ></script>

    <!-- ReactFlow from jsDelivr CDN -->
    <script
      src="https://cdn.jsdelivr.net/npm/reactflow@11.10.1/dist/umd/index.js"
      crossorigin
    ></script>

    <!-- Bootstrap JS -->
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
      crossorigin="anonymous"
    ></script>

    <!-- App Script -->
    <script src="/app.js"></script>
  </body>
</html>
