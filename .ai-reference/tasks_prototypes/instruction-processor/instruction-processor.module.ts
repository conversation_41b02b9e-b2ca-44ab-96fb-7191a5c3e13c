import { Modu<PERSON> } from "@nestjs/common";
import { InstructionProcessorService } from "./instruction-processor.service";
import { AgentFlowEngineModule } from "../agent-flow-engine/agent-flow-engine.module";
import { InstructionProcessorController } from "./controllers/instruction-processor.controller";
import { LlmModule } from "../llm/llm.module";
import {
  OperationDetectionNode,
  DocumentTypeNode,
  FieldCategoryNode,
  SchemaFieldIdentificationNode,
  ValueNormalizationNode,
  FinalReviewNode,
  HumanReviewNode,
  QueryHandlerNode,
  SpamHandlerNode,
  SuccessHandlerNode,
  IntentDetectionNode,
} from "./nodes";

@Module({
  imports: [AgentFlowEngineModule, LlmModule],
  controllers: [InstructionProcessorController],
  providers: [
    InstructionProcessorService,
    OperationDetectionNode,
    DocumentTypeNode,
    FieldCategoryNode,
    SchemaFieldIdentificationNode,
    ValueNormalizationNode,
    FinalReviewNode,
    HumanReviewNode,
    QueryHandlerNode,
    SpamHandlerNode,
    SuccessHandlerNode,
    IntentDetectionNode,
  ],
  exports: [InstructionProcessorService, IntentDetectionNode],
})
export class InstructionProcessorModule {}
