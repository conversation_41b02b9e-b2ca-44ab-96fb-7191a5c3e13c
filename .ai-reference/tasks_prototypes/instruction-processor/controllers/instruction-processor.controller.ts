import { <PERSON>, Get, Post, Query, Body, Logger } from "@nestjs/common";
import { InstructionProcessorService } from "../instruction-processor.service";

@Controller("instruction")
export class InstructionProcessorController {
  private readonly logger = new Logger(InstructionProcessorController.name);

  constructor(
    private readonly instructionService: InstructionProcessorService,
  ) {}

  @Get("run")
  async runWorkflow(@Query("usePathA") usePathA: string): Promise<any> {
    this.logger.log(`Running workflow with usePathA=${usePathA}`);

    // Convert string parameter to boolean
    const usePathABool = usePathA === "true";

    const result = await this.instructionService.runConditionalWorkflow({
      usePathA: usePathABool,
    });

    return {
      input: { usePathA: usePathABool },
      result,
    };
  }

  @Post("process")
  async processInstruction(
    @Body() body: { instruction: string },
  ): Promise<any> {
    this.logger.log(`Processing instruction: ${body.instruction}`);

    if (!body.instruction) {
      return {
        error: "Instruction is required",
      };
    }

    const result = await this.instructionService.runIntentToSchemaWorkflow(
      body.instruction,
    );

    return {
      input: { instruction: body.instruction },
      result,
    };
  }

  @Get("workflow-graph")
  getWorkflowGraph(): any {
    this.logger.log("Fetching workflow graph for visualization");
    const graph = this.instructionService.getWorkflowGraphForVisualization();
    return graph;
  }

  @Get("health")
  getHealth(): string {
    return "Instruction Processor is healthy!";
  }
}
