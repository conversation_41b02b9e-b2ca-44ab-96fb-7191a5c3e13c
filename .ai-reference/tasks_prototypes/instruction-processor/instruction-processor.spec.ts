import { Test, TestingModule } from "@nestjs/testing";
import { InstructionProcessorService } from "./instruction-processor.service";
import { AgentFlowEngineModule } from "../agent-flow-engine/agent-flow-engine.module";

describe("InstructionProcessorService", () => {
  let service: InstructionProcessorService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AgentFlowEngineModule],
      providers: [InstructionProcessorService],
    }).compile();

    service = module.get<InstructionProcessorService>(
      InstructionProcessorService,
    );
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
