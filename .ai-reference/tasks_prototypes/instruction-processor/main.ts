import { NestFactory } from "@nestjs/core";
import * as dotenv from "dotenv";
import { InstructionProcessorModule } from "./instruction-processor.module";
import { InstructionProcessorService } from "./instruction-processor.service";

// Load environment variables from .env file
dotenv.config();

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(
    InstructionProcessorModule,
  );

  const instructionService = app.get(InstructionProcessorService);

  const result = await instructionService.runConditionalWorkflow({
    usePathA: true,
  });
  console.log("Workflow result with usePathA=true:", result);

  const resultB = await instructionService.runConditionalWorkflow({
    usePathA: false,
  });
  console.log("Workflow result with usePathA=false:", resultB);

  await app.close();
}

bootstrap().catch((err) => {
  console.error("Bootstrap error:", err);
  process.exit(1);
});
