{"editor.tabSize": 2, "editor.wordWrap": "on", "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "never", "source.organizeImports": "explicit"}, "eslint.validate": ["javascript", "typescript"], "eslint.rules.customizations": [{"rule": "@typescript-eslint/no-unused-vars", "severity": "warn"}], "[xml]": {"editor.defaultFormatter": "redhat.vscode-xml"}, "files.eol": "\n", "jest.runMode": "deferred", "jest.jestCommandLine": "npm run jest", "liveServer.settings.multiRootWorkspaceName": "<PERSON><PERSON><PERSON>"}