---
Generated: 2025-01-08T22:32:00Z
Analyzer Version: 1.0
Source File: /home/<USER>/dev/Claro/apps/portal-api/src/clean-agent-context/document-data-gatherer.service.ts
Last Modified: Recent
File Hash: N/A
---

# DocumentDataGatherer Service Analysis

## File Overview

The **DocumentDataGatherer** is a DEFAULT-scoped service that acts as a processor for gathering document data specifically for email template generation. It integrates with both DocumentService and AggregationService to provide comprehensive document processing information, following the established processor pattern used in AggregationExecuter.

**Language/Framework**: TypeScript/NestJS  
**Project Location**: Clean agent context module for email processing system

## Dependencies Map

**Primary Service Dependencies**:
- `DocumentService` - Document management and retrieval operations (DEFAULT scope with ClsService)
- `AggregationService` - Document aggregation status and workflow management

**Entity/Type Dependencies**:
- `AggregationTargetType` from nest-modules - Enum for aggregation target typing
- `DocumentTemplateData` - Interface defining template data structure

**Framework Dependencies**:
- `Injectable` from @nestjs/common - Service decoration

## Exports

**Main Export**: `DocumentDataGatherer` class - Service for gathering document template data

## Key Method Signatures

### Primary Interface

```typescript
async gather(params: { shipmentId: number }): Promise<DocumentTemplateData>
```

**Purpose**: Main entry point for gathering document template data for a specific shipment  
**Parameters**: 
- `shipmentId: number` - The shipment ID to gather documents for
**Returns**: Complete document template data ready for email template rendering

## Core Implementation Analysis

### 1. Document Retrieval Strategy

**Pattern**: Parallel data fetching with aggregation correlation
```typescript
// Gets documents for shipment (limit: 100 to get all)
const documentsResponse = await this.documentService.getDocuments({
  shipmentId: shipmentId,
  limit: 100,
  skip: 0
});

// Gets aggregation status for correlation
const aggregation = await this.aggregationService.getAggregationByTarget(
  shipmentId,
  AggregationTargetType.SHIPMENT
);
```

**Implementation Notes**:
- Uses DocumentService.getDocuments() with GetDocumentsDto parameters
- Retrieves up to 100 documents per shipment (covers all documents)
- Integrates with AggregationService for real-time processing status

### 2. Aggregation Status Mapping Logic

**Pattern**: Two-tier status resolution with fallback strategy

**Primary Strategy**: Real aggregation status from AggregationService
```typescript
private getAggregationStatus(document: any, aggregationStatusMap: Map<number, string>): 
  "success" | "failed" | "processing" | "pending"
```

**Status Mappings**:
- `"completed"` | `"success"` → `"success"`
- `"failed"` | `"error"` → `"failed"`  
- `"processing"` | `"running"` | `"in_progress"` → `"processing"`
- `"pending"` | `"queued"` | default → `"pending"`

**Fallback Strategy**: Document status-based approximation
```typescript
private getDocumentStatusBasedAggregationStatus(document: any): 
  "success" | "failed" | "processing" | "pending"
```

**Document Status Mappings**:
- `"extracted"` → `"success"`
- `"extracting"` → `"processing"`
- `"failed"` → `"failed"`
- `"pending"` | default → `"pending"`

### 3. Document Processing Pipeline

**Pattern**: Transform raw document data to template-ready format
```typescript
const processedDocuments = documentsResponse.documents.map((doc) => ({
  filename: doc.name || "Unknown Document",
  contentType: doc.documentType?.name || "Unknown Type",
  aggregationStatus: this.getAggregationStatus(doc, aggregationStatusMap),
  claroUrl: this.generateClaroUrl(doc.id, shipmentId)
}));
```

**URL Generation Pattern**: `/portal/shipments/{shipmentId}/documents/{documentId}`

### 4. Document Type Detection & Validation

**Pattern**: String-based document type classification with partial matching

**Commercial Invoice/Packing List Detection**:
```typescript
private isMissingCommercialInvoiceOrPackingList(documentTypes: string[]): boolean {
  const hasCI = documentTypes.some(type => 
    type.includes("commercial invoice") || type.includes("ci"));
  const hasPL = documentTypes.some(type => 
    type.includes("packing list") || type.includes("pl"));
  return !hasCI || !hasPL;
}
```

**House Bill of Lading Detection**:
```typescript
private isMissingHouseBillOfLading(documentTypes: string[]): boolean {
  return !documentTypes.some(type =>
    type.includes("house bill of lading") || 
    type.includes("hbl") || 
    type.includes("bill of lading"));
}
```

**Arrival Notice/E-Manifest Detection**:
```typescript
private isMissingArrivalNoticeOrEManifest(documentTypes: string[]): boolean {
  const hasAN = documentTypes.some(type => 
    type.includes("arrival notice") || type.includes("an"));
  const hasEMF = documentTypes.some(type => 
    type.includes("e-manifest") || type.includes("emf") || type.includes("manifest"));
  return !hasAN && !hasEMF;
}
```

### 5. Validation Issues Detection

**Pattern**: Multi-criteria validation checking
```typescript
private hasValidationIssues(documents: any[]): boolean {
  const hasDocumentErrors = documents.some(doc => 
    doc.validationErrors && doc.validationErrors.length > 0);
  const hasFailedDocuments = documents.some(doc => doc.status === "failed");
  return hasDocumentErrors || hasFailedDocuments;
}
```

**Checks**:
- Document validation errors array
- Failed document status indicators

## DocumentTemplateData Return Structure

```typescript
interface DocumentTemplateData {
  PROCESSED_DOCUMENTS: Array<{
    filename: string;
    contentType: string;
    aggregationStatus: "success" | "failed" | "processing" | "pending";
    claroUrl: string;
  }>;
  SHOW_VALIDATION_ISSUES: boolean;
  CI_PL_MISSING: boolean;        // Commercial Invoice/Packing List missing
  HBL_MISSING: boolean;          // House Bill of Lading missing  
  AN_EMF_MISSING: boolean;       // Arrival Notice/E-Manifest missing
}
```

## Integration Patterns

### 1. Service Scope Integration

**Pattern**: Mixed-scope service integration
- **DocumentDataGatherer**: DEFAULT-scoped (can be injected directly)
- **DocumentService**: DEFAULT-scoped with ClsService for organization context
- **AggregationService**: DEFAULT-scoped with ClsService for organization context

### 2. CleanAgentContextService Integration

**Usage Pattern**: Direct injection (DEFAULT-scoped services)
```typescript
// In CleanAgentContextService
private readonly documentDataGatherer: DocumentDataGatherer

async gatherDocumentData(shipmentId: number, organizationId: number, _request: any): 
  Promise<DocumentTemplateData> {
  return await this.cls.run(async () => {
    this.cls.set("ORGANIZATION_ID", organizationId);
    const gatherer = this.documentDataGatherer; // Direct injection
    return await gatherer.gather({ shipmentId });
  });
}
```

### 3. Data Flow Integration

**Input**: `{ shipmentId: number }`  
**Processing**: DocumentService + AggregationService correlation  
**Output**: Template-ready document data with validation flags

## Key Design Points

### 1. Aggregation Status Priority

**Design Decision**: Real aggregation status takes precedence over document status
- Primary: AggregationService data (real-time processing status)
- Fallback: DocumentService status (document-level status approximation)

### 2. Document Type Flexibility

**Design Decision**: Partial string matching for document type classification
- Supports multiple naming conventions ("ci", "commercial invoice")
- Case-insensitive matching via `.toLowerCase()`
- Accommodates various document naming patterns

### 3. Validation Comprehensiveness

**Multi-layer validation approach**:
- Document-level validation errors
- Processing status validation  
- Required document type validation
- Business rule validation (CI/PL combination requirements)

### 4. Template Readiness

**Output Optimization**: All data pre-formatted for direct template consumption
- Boolean flags for missing document types
- Processed document arrays with display-ready properties
- URL generation for document access links

## Extension Points for Email-Specific Processing

### 1. Email Context Parameters

**Current Limitation**: Only accepts `shipmentId`
**Extension Opportunity**: Add email-specific context
```typescript
// Potential extension
async gather(params: { 
  shipmentId: number;
  emailContext?: {
    threadId?: string;
    messageId?: string;
    intent?: string;
  }
}): Promise<DocumentTemplateData>
```

### 2. Document Filtering

**Current Behavior**: Retrieves all documents (limit: 100)
**Extension Opportunity**: Email-relevant document filtering
- Filter by document status for email context
- Priority document identification
- Recent document emphasis

### 3. Template Customization

**Current Output**: Standard template data structure
**Extension Opportunity**: Email-specific template variables
- Email-specific validation messages
- Context-aware document recommendations
- Intent-based document highlighting

### 4. Status Context Enhancement

**Current Status**: Generic aggregation status mapping
**Extension Opportunity**: Email-aware status presentation
- Email-friendly status descriptions
- Context-sensitive status priorities
- Intent-based status emphasis

This analysis provides the foundation for understanding how to extend DocumentDataGatherer for email-specific document processing while maintaining compatibility with the existing template system.