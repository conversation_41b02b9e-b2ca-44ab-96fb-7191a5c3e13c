#!/usr/bin/env python3
import PyPDF2
import json
import sys
import re

def extract_pdf_text(pdf_path):
    """Extract text from PDF"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
            return text
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return None

def parse_tariff_codes(text):
    """Parse tariff codes from the extracted text"""
    # Look for patterns like "EP1", "EP2", etc. followed by agency codes and descriptions
    lines = text.split('\n')
    tariff_codes = []
    
    # Pattern to match tariff flag codes (2-3 letters + 1-2 digits)
    code_pattern = r'^([A-Z]{2,3}\d{1,2}[A-Z]?)\s+'
    
    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
            
        # Look for tariff flag codes
        match = re.match(code_pattern, line)
        if match:
            tariff_flag_code = match.group(1)
            
            # Try to extract the rest of the information from this line and potentially next lines
            remaining_text = line[match.end():].strip()
            
            # Look for agency code (3 letters)
            agency_match = re.search(r'\b([A-Z]{3})\b', remaining_text)
            agency_code = agency_match.group(1) if agency_match else ""
            
            # Look for requirement level (R or M)
            req_match = re.search(r'\b([RM])\b', remaining_text)
            requirement_level = req_match.group(1) if req_match else ""
            
            # Extract description (everything after the codes)
            description = remaining_text
            if agency_match:
                description = description[agency_match.end():].strip()
            
            # Clean up description
            description = re.sub(r'^[RM]\s+', '', description)
            description = re.sub(r'\s+', ' ', description)
            
            tariff_codes.append({
                'tariffFlagCode': tariff_flag_code,
                'agencyCode': agency_code,
                'requirementLevel': requirement_level,
                'description': description,
                'raw_line': line
            })
    
    return tariff_codes

def main():
    pdf_path = '/home/<USER>/dev/Claro/.ai-reference/us-tariff-data/ACE Agency Tariff Codes_17October2023_508c.pdf'
    
    # Extract text from PDF
    text = extract_pdf_text(pdf_path)
    if not text:
        print("Failed to extract text from PDF")
        return
    
    # Parse tariff codes
    tariff_codes = parse_tariff_codes(text)
    
    # Print results
    print(f"Found {len(tariff_codes)} tariff codes:")
    for code in tariff_codes:
        print(f"Code: {code['tariffFlagCode']}, Agency: {code['agencyCode']}, Level: {code['requirementLevel']}")
        print(f"Description: {code['description']}")
        print(f"Raw line: {code['raw_line']}")
        print("-" * 80)
    
    # Also save to JSON for easier comparison
    with open('/home/<USER>/dev/Claro/.ai-reference/extracted_tariff_codes.json', 'w') as f:
        json.dump(tariff_codes, f, indent=2)

if __name__ == "__main__":
    main()