# Core-Agent Fragment System Integration Prompt

## Context
You are working on the `feat/email-refactoring-separate-concerns` branch of a NestJS email processing system. We have just copied 30 files from a template branch that implements an advanced fragment-based response system for email generation. Your task is to integrate this system with the existing email processing pipeline.

## What Was Just Copied
We've successfully copied these files from `fix/SCRUM-530-7b-Send-copy-of-proof-of-release-RNS/response-templates-to-official`:

### Core System Files (6 files):
- `constants/templates.ts` - Template ordering and priority system
- `types/response-fragment.types.ts` - Fragment interfaces and types
- `types/shipment-context.types.ts` - Context interfaces
- `services/shipment-response.service.ts` - Fragment rendering engine
- `services/shipment-context.service.ts` - Context building service
- `services/intent-handler-registry.service.ts` - Handler registration system

### Intent Handler System (6 files):
- `interfaces/intent-handler.interface.ts` - Handler contract
- `handlers/base-intent-handler.ts` - Abstract base class
- `handlers/documentation-coming.handler.ts` - Documentation intent handler
- `handlers/get-shipment-status.handler.ts` - Status inquiry handler
- `handlers/request-cad-document.handler.ts` - CAD document handler
- `handlers/request-rush-processing.handler.ts` - Rush processing handler

### Advanced Templates (17 files):
- CAD templates: `cad-document-attached.njk`, `cad-document-not-ready.njk`
- RNS templates: `rns-proof-attached.njk`, `rns-proof-not-ready.njk`
- Processing templates: `rush-processing-success.njk`, `rush-processing-blocked.njk`, `manual-processing-requested.njk`
- Acknowledgment templates: `acknowledgement-all-docs-received.njk`, `acknowledgement-missing-docs.njk`
- Support templates: `contact-support.njk`, `system-unavailable.njk`, `submission-required-notice.njk`
- Shared components: `responses/customs-status.njk`, `responses/shipment-details.njk`, `responses/shipment-identifiers.njk`
- Other: `documentation-coming-acknowledged.njk`, `hold-shipment-confirmed.njk`

### Test Controller (1 file):
- `controllers/test/response-service.test.controller.ts` - Testing endpoints

## Current System Architecture
The existing system has:
- ✅ Email saga with `CoreAgentEmailSagaListener` and processors
- ✅ `HandleRequestMessageProcessor` that currently uses hardcoded HTML responses
- ✅ Basic templates (10 existing templates)
- ✅ Core-agent module with services and processors

## Integration Tasks

### 1. Module Integration
**File**: `apps/portal-api/src/core-agent/core-agent.module.ts`
- Add new services to providers: `ShipmentResponseService`, `ShipmentContextService`, `IntentHandlerRegistry`
- Ensure all intent handlers are included in providers
- Verify TemplateManagerModule configuration includes new templates

### 2. HandleRequestMessageProcessor Refactoring
**File**: `apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts`
- Replace hardcoded HTML responses with fragment-based system
- Integrate `ShipmentContextService` for building comprehensive context
- Use `IntentHandlerRegistry` to process intents and generate fragments
- Use `ShipmentResponseService` to render fragments into final response
- Maintain compatibility with existing email flow

### 3. Key Integration Points
- **Context Building**: Use `ShipmentContextService.buildContext()` instead of manual context creation
- **Intent Processing**: Use `IntentHandlerRegistry.processIntents()` instead of direct intent handling
- **Response Generation**: Use `ShipmentResponseService.renderFragments()` instead of hardcoded HTML
- **Side Effects**: Ensure handlers can generate attachments and send alerts through context

### 4. Template System
- Verify all 27 templates are accessible via TemplateManagerService
- Ensure template priority system works with `TEMPLATE_ORDER` constants
- Test fragment deduplication and sorting

## Expected Architecture After Integration

```
Email Saga → HandleRequestMessageProcessor → {
  1. Build Context (ShipmentContextService)
  2. Process Intents (IntentHandlerRegistry) → ResponseFragment[]
  3. Render Fragments (ShipmentResponseService) → HTML
  4. Send Email (existing flow)
}
```

## Key Files to Modify
1. `core-agent.module.ts` - Add new services and handlers
2. `handle-request-message.processor.ts` - Replace hardcoded responses with fragments
3. Potentially `template-registry.ts` - Ensure new templates are registered

## Testing Strategy
1. Use `response-service.test.controller.ts` to test fragment rendering
2. Run existing e2e email pipeline tests to ensure compatibility
3. Test all intent handlers produce correct fragments
4. Validate all templates render with real shipment data

## Success Criteria
- All hardcoded HTML responses replaced with fragment-based system
- All 27 templates render correctly
- Intent handlers produce appropriate fragments
- Email processing pipeline maintains existing functionality
- No breaking changes to email saga or existing processors

## Important Notes
- Preserve existing email saga functionality
- Maintain compatibility with existing email templates
- Ensure proper error handling and fallbacks
- Keep existing logging and monitoring
- Follow existing code patterns and conventions

Your goal is to seamlessly integrate the advanced fragment-based template system with the existing email processing pipeline while maintaining all current functionality.

## Technical Implementation Details

### Current HandleRequestMessageProcessor Pattern
The processor currently uses patterns like:
```typescript
// OLD: Hardcoded HTML
htmlResponseParts.push(`<p>We've received your request for rush processing...</p>`);

// NEW: Fragment-based
const fragments = await this.intentHandlerRegistry.processIntents(validatedIntents, context);
const response = await this.shipmentResponseService.renderFragments(fragments, context);
```

### Fragment System Flow
```typescript
// 1. Build comprehensive context
const context = await this.shipmentContextService.buildContext(shipmentId, organizationId);

// 2. Process intents to generate fragments
const fragments: ResponseFragment[] = [];
for (const intent of validatedIntents.intents) {
  const handler = this.intentHandlerRegistry.getHandler(intent.intent);
  const intentFragments = await handler.handle(intent, context);
  fragments.push(...intentFragments);
}

// 3. Render fragments to HTML
const responseHtml = await this.shipmentResponseService.renderFragments(fragments, context);
```

### Key Integration Points in HandleRequestMessageProcessor

1. **Replace generateAndSendResponse method**:
   - Use fragment system instead of hardcoded HTML
   - Maintain attachment handling for CAD/RNS documents

2. **Update intent processing loops**:
   - Replace direct intent handling with handler registry
   - Ensure side effects (attachments, alerts) are captured

3. **Preserve email template integration**:
   - Fragment-rendered content should work with existing `EmailTemplateName.DOCUMENT_SENT_IN_RESPONSE_WITH_QUERIES_EMAIL`

### Dependencies to Inject
Add to HandleRequestMessageProcessor constructor:
```typescript
private readonly shipmentResponseService: ShipmentResponseService,
private readonly shipmentContextService: ShipmentContextService,
private readonly intentHandlerRegistry: IntentHandlerRegistry
```

### Error Handling
- Fragment system has built-in fallbacks for template failures
- Maintain existing error handling patterns
- Log fragment rendering issues without breaking email flow

## Validation Commands
After integration, test with:
```bash
# Test fragment rendering
curl -X POST http://localhost:3000/debug/response-service/render-fragments/[shipmentId]

# Test intent handlers
curl -X GET http://localhost:3000/debug/core-agent/intent-handler-registry

# Run e2e email tests
./scripts/run-e2e-with-logs.sh
```

## Files Currently Staged
Run `git status` to see the 30 newly added files ready for integration. All files are staged and ready to be incorporated into the existing system.
