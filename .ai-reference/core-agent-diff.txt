diff --git a/apps/portal-api/src/core-agent/constants/templates.ts b/apps/portal-api/src/core-agent/constants/templates.ts
deleted file mode 100644
index 2725fee9..********
--- a/apps/portal-api/src/core-agent/constants/templates.ts
+++ /dev/null
@@ -1,59 +0,0 @@
-/**
- * Template ordering configuration for consistent response structure.
- * Templates are sorted by explicit priority first, then by this predefined order.
- */
-export const TEMPLATE_ORDER = [
-  // Critical alerts (priority 1-10)
-  "rush-processing-success",
-  "rush-processing-blocked",
-  "manual-processing-requested",
-  "hold-shipment-confirmed",
-  "cad-document-attached",
-  "rns-proof-attached",
-  "acknowledgement-all-docs-received",
-
-  // Status information (priority 11-20)
-  "customs-status",
-  "shipping-status",
-  "submission-status",
-
-  // Supporting information (priority 21-30)
-  "eta",
-  "shipment-identifiers",
-  "transaction-number",
-  "release-status",
-
-  // Compliance and errors (priority 31-40)
-  "compliance-errors",
-  "acknowledgement-missing-docs",
-  "submission-required-notice",
-
-  // Documentation not ready (priority 41-50)
-  "cad-document-not-ready",
-  "rns-proof-not-ready",
-
-  // Support and fallbacks (priority 51+)
-  "contact-support",
-  "system-unavailable",
-  "answer-question-template"
-] as const;
-
-/**
- * Template categories for organizational purposes
- */
-export const TEMPLATE_CATEGORIES = {
-  CRITICAL_ALERTS: [
-    "rush-processing-success",
-    "rush-processing-blocked",
-    "manual-processing-requested",
-    "hold-shipment-confirmed",
-    "cad-document-attached",
-    "rns-proof-attached",
-    "acknowledgement-all-docs-received"
-  ],
-  STATUS_INFO: ["customs-status", "shipping-status", "submission-status"],
-  SUPPORTING_INFO: ["eta", "shipment-identifiers", "transaction-number", "release-status"],
-  COMPLIANCE_ERRORS: ["compliance-errors", "acknowledgement-missing-docs", "submission-required-notice"],
-  NOT_READY: ["cad-document-not-ready", "rns-proof-not-ready"],
-  FALLBACKS: ["contact-support", "system-unavailable", "answer-question-template"]
-} as const;
diff --git a/apps/portal-api/src/core-agent/controllers/test/core-agent.test.controller.ts b/apps/portal-api/src/core-agent/controllers/test/core-agent.test.controller.ts
index 8ecc5e2b..17553134 100644
--- a/apps/portal-api/src/core-agent/controllers/test/core-agent.test.controller.ts
+++ b/apps/portal-api/src/core-agent/controllers/test/core-agent.test.controller.ts
@@ -3,23 +3,17 @@ import {
   Controller,
   Logger,
   Post,
-  Get,
-  Param,
   Req,
   UseGuards,
   ValidationPipe,
-  BadRequestException,
-  NotFoundException,
-  ParseIntPipe
+  BadRequestException
 } from "@nestjs/common";
-import { ApiOperation, ApiTags, ApiResponse, ApiBody, ApiParam } from "@nestjs/swagger";
+import { ApiOperation, ApiTags, ApiResponse, ApiBody } from "@nestjs/swagger";
 import { AccessTokenGuard, ApiAccessTokenAuthenticated, AuthenticatedRequest } from "nest-modules";
 import { TestAnalyzeEmailIntentsDto } from "../../dto/test/email-intent-analysis.test.dto";
 import { MessageContent } from "../../schemas/message-content.schema";
 import { CoreAgentService } from "../../services/core-agent.service";
 import { ShipmentIdentifierService } from "../../services/shipment-identifier.service";
-import { ShipmentContextService } from "../../services/shipment-context.service";
-import { IntentHandlerRegistry } from "../../services/intent-handler-registry.service";
 import { IdentifyShipmentOutput } from "../../schemas/identify-shipment.schema";
 
 @ApiTags("Core Agent Tests / Core Agent")
@@ -31,406 +25,9 @@ export class CoreAgentTestController {
 
   constructor(
     private readonly coreAgentService: CoreAgentService,
-    private readonly shipmentIdentifierService: ShipmentIdentifierService,
-    private readonly shipmentContextService: ShipmentContextService,
-    private readonly intentHandlerRegistry: IntentHandlerRegistry
+    private readonly shipmentIdentifierService: ShipmentIdentifierService
   ) {}
 
-  @Get("shipment-context/:shipmentId/:organizationId")
-  @ApiOperation({
-    summary: "Test the ShipmentContextService - builds complete context for a shipment",
-    description:
-      "Returns comprehensive shipment context including all business rules, formatted data, and performance metrics. Use this to test the centralized context building functionality."
-  })
-  @ApiParam({ name: "shipmentId", type: "number", description: "ID of the shipment to build context for" })
-  @ApiParam({
-    name: "organizationId",
-    type: "number",
-    description: "ID of the organization for service scoping"
-  })
-  @ApiResponse({
-    status: 200,
-    description: "Successfully built shipment context with all business rules and formatted data",
-    schema: {
-      type: "object",
-      properties: {
-        success: { type: "boolean" },
-        timing: {
-          type: "object",
-          properties: {
-            totalMs: { type: "number" },
-            contextBuildMs: { type: "number" }
-          }
-        },
-        context: {
-          type: "object",
-          properties: {
-            // Core data
-            shipmentId: { type: "number" },
-            organizationId: { type: "number" },
-            customsStatus: { type: "string" },
-
-            // Business rules
-            businessRules: {
-              type: "object",
-              properties: {
-                canRush: { type: "boolean" },
-                canGenerateCAD: { type: "boolean" },
-                canGenerateRNSProof: { type: "boolean" },
-                isCompliant: { type: "boolean" },
-                isReleased: { type: "boolean" },
-                isSubmitted: { type: "boolean" },
-                canBeModified: { type: "boolean" },
-                isEntryUploaded: { type: "boolean" },
-                canUpdateEntry: { type: "boolean" }
-              }
-            },
-
-            // Blocking reasons
-            blockingReasons: {
-              type: "object",
-              properties: {
-                rushBlockingReason: { type: "string" },
-                cadBlockingReason: { type: "string" }
-              }
-            },
-
-            // Compliance info
-            complianceInfo: {
-              type: "object",
-              properties: {
-                missingDocumentsCount: { type: "number" },
-                complianceErrorsCount: { type: "number" },
-                nonCompliantInvoicesCount: { type: "number" }
-              }
-            },
-
-            // Formatted display data
-            formattedData: {
-              type: "object",
-              properties: {
-                formattedCustomsStatus: { type: "string" },
-                shipmentIdentifiers: { type: "object" },
-                etaInformation: { type: "object" },
-                shippingInformation: { type: "object" }
-              }
-            }
-          }
-        }
-      }
-    }
-  })
-  @ApiResponse({ status: 400, description: "Invalid shipment ID or organization ID" })
-  @ApiResponse({ status: 404, description: "Shipment or organization not found" })
-  @ApiResponse({ status: 500, description: "Internal server error during context building" })
-  async testShipmentContext(
-    @Param("shipmentId", ParseIntPipe) shipmentId: number,
-    @Param("organizationId", ParseIntPipe) organizationId: number
-  ) {
-    const startTime = Date.now();
-    this.logger.log(
-      `Testing ShipmentContextService for shipment ${shipmentId}, organization ${organizationId}`
-    );
-
-    try {
-      // Build the context (this is what we're testing)
-      const contextStartTime = Date.now();
-      const context = await this.shipmentContextService.buildContext(shipmentId, organizationId);
-      const contextBuildTime = Date.now() - contextStartTime;
-
-      // Create a sanitized/readable version for debugging
-      const sanitizedContext = {
-        // Core identifiers
-        shipmentId: context.shipment.id,
-        organizationId: context.organization.id,
-        customsStatus: context.shipment.customsStatus,
-
-        // Business rules summary
-        businessRules: {
-          canRush: context.canRush,
-          canGenerateCAD: context.canGenerateCAD,
-          canGenerateRNSProof: context.canGenerateRNSProof,
-          isCompliant: context.isCompliant,
-          isReleased: context.isReleased,
-          isSubmitted: context.isSubmitted,
-          canBeModified: context.canBeModified,
-          isEntryUploaded: context.isEntryUploaded,
-          canUpdateEntry: context.canUpdateEntry
-        },
-
-        // Blocking reasons
-        blockingReasons: {
-          rushBlockingReason: context.rushBlockingReason,
-          cadBlockingReason: context.cadBlockingReason
-        },
-
-        // Compliance information
-        complianceInfo: {
-          missingDocumentsCount: context.missingDocuments.length,
-          complianceErrorsCount: context.complianceErrors.length,
-          nonCompliantInvoicesCount: context.nonCompliantInvoices.length,
-          missingDocuments: context.missingDocuments.slice(0, 5), // Show first 5 for debugging
-          complianceErrors: context.complianceErrors.slice(0, 5) // Show first 5 for debugging
-        },
-
-        // Formatted display data
-        formattedData: {
-          formattedCustomsStatus: context.formattedCustomsStatus,
-          shipmentIdentifiers: context.shipmentIdentifiers,
-          etaInformation: context.etaInformation,
-          shippingInformation: context.shippingInformation
-        },
-
-        // Service availability check
-        servicesAvailable: {
-          emailService: !!context._services.emailService,
-          rnsStatusChangeEmailSender: !!context._services.rnsStatusChangeEmailSender,
-          rnsProofService: !!context._services.rnsProofService,
-          customsStatusListener: !!context._services.customsStatusListener,
-          entrySubmissionService: !!context._services.entrySubmissionService,
-          importerService: !!context._services.importerService
-        }
-      };
-
-      const totalTime = Date.now() - startTime;
-
-      this.logger.log(`Successfully built context for shipment ${shipmentId} in ${contextBuildTime}ms`);
-
-      return {
-        success: true,
-        timing: {
-          totalMs: totalTime,
-          contextBuildMs: contextBuildTime
-        },
-        context: sanitizedContext
-      };
-    } catch (error) {
-      const totalTime = Date.now() - startTime;
-
-      this.logger.error(`Failed to build context for shipment ${shipmentId}: ${error.message}`, error.stack);
-
-      // Return detailed error information for debugging
-      return {
-        success: false,
-        timing: {
-          totalMs: totalTime,
-          contextBuildMs: 0
-        },
-        error: {
-          message: error.message,
-          type: error.constructor.name,
-          isNotFound: error instanceof NotFoundException,
-          isBadRequest: error instanceof BadRequestException,
-          stack: error.stack
-        },
-        context: null
-      };
-    }
-  }
-
-  @Get("intent-handler-registry")
-  @ApiOperation({
-    summary: "Test Intent Handler Registry - check which handlers are registered",
-    description:
-      "Returns diagnostics about intent handler registration, including registered handlers, missing handlers, and validation status"
-  })
-  @ApiResponse({
-    status: 200,
-    description: "Successfully retrieved intent handler registry diagnostics",
-    schema: {
-      type: "object",
-      properties: {
-        success: { type: "boolean" },
-        registrationStatus: {
-          type: "object",
-          properties: {
-            totalHandlers: { type: "number" },
-            registrationComplete: { type: "boolean" },
-            registrationTime: { type: "string", nullable: true }
-          }
-        },
-        registeredHandlers: {
-          type: "array",
-          items: {
-            type: "object",
-            properties: {
-              intentType: { type: "string" },
-              handlerClass: { type: "string" },
-              exampleCount: { type: "number" },
-              hasKeywords: { type: "boolean" }
-            }
-          }
-        },
-        missingHandlers: {
-          type: "array",
-          items: { type: "string" }
-        },
-        validation: {
-          type: "object",
-          properties: {
-            isValid: { type: "boolean" },
-            errors: {
-              type: "array",
-              items: { type: "string" }
-            }
-          }
-        },
-        supportedIntents: {
-          type: "array",
-          items: { type: "string" }
-        }
-      }
-    }
-  })
-  async testIntentHandlerRegistry() {
-    this.logger.log("Testing Intent Handler Registry");
-
-    try {
-      // Force re-registration to debug
-      this.logger.log("Forcing re-registration for debugging...");
-      this.intentHandlerRegistry.forceReregistration();
-
-      // Get system diagnostics
-      const diagnostics = this.intentHandlerRegistry.getSystemDiagnostics();
-
-      // Validate all handlers
-      const validation = this.intentHandlerRegistry.validateAllHandlers();
-
-      // Get supported intents
-      const supportedIntents = this.intentHandlerRegistry.getAllSupportedIntents();
-
-      // Test classification prompt generation
-      let classificationPrompt = "";
-      try {
-        classificationPrompt = this.intentHandlerRegistry.generateClassificationPrompt();
-      } catch (error) {
-        this.logger.warn(`Failed to generate classification prompt: ${error.message}`);
-      }
-
-      // Add debug information about handler injection
-      const debugInfo = {
-        hasRegistry: !!this.intentHandlerRegistry,
-        registryConstructorName: this.intentHandlerRegistry.constructor.name,
-        // Try to access the private allHandlers property via reflection for debugging
-        rawHandlersCount: (this.intentHandlerRegistry as any).allHandlers?.length || 0,
-        rawHandlersTypes:
-          (this.intentHandlerRegistry as any).allHandlers?.map((h: any) => h.constructor.name) || []
-      };
-
-      this.logger.log(`Registry has ${diagnostics.totalHandlers} handlers registered`);
-      this.logger.log(`Missing handlers: ${diagnostics.missingHandlers.join(", ") || "none"}`);
-      this.logger.log(`Debug info: ${JSON.stringify(debugInfo)}`);
-
-      return {
-        success: true,
-        registrationStatus: {
-          totalHandlers: diagnostics.totalHandlers,
-          registrationComplete: diagnostics.registrationComplete,
-          registrationTime: diagnostics.registrationTime?.toISOString() || null
-        },
-        registeredHandlers: diagnostics.registeredHandlers,
-        missingHandlers: diagnostics.missingHandlers,
-        validation,
-        supportedIntents,
-        hasClassificationPrompt: !!classificationPrompt.trim(),
-        classificationPromptLength: classificationPrompt.length,
-        debugInfo
-      };
-    } catch (error) {
-      this.logger.error(`Failed to test intent handler registry: ${error.message}`, error.stack);
-
-      return {
-        success: false,
-        error: {
-          message: error.message,
-          type: error.constructor.name,
-          stack: error.stack
-        }
-      };
-    }
-  }
-
-  @Get("debug-registry-state")
-  @ApiOperation({
-    summary: "Debug Registry State - show internal state without forcing re-registration",
-    description: "Returns raw internal state of the registry to debug multiple instance issues"
-  })
-  async debugRegistryState() {
-    this.logger.log("Debugging Registry State (no force re-registration)");
-
-    try {
-      const diagnostics = this.intentHandlerRegistry.getSystemDiagnostics();
-      const supportedIntents = this.intentHandlerRegistry.getAllSupportedIntents();
-
-      // Debug information about the registry instance
-      const debugInfo = {
-        hasRegistry: !!this.intentHandlerRegistry,
-        registryConstructorName: this.intentHandlerRegistry.constructor.name,
-        rawHandlersCount: (this.intentHandlerRegistry as any).allHandlers?.length || 0,
-        rawHandlersTypes:
-          (this.intentHandlerRegistry as any).allHandlers?.map((h: any) => h.constructor.name) || []
-      };
-
-      return {
-        success: true,
-        internalState: {
-          handlerMapSize: diagnostics.totalHandlers,
-          registrationComplete: diagnostics.registrationComplete,
-          registrationTime: diagnostics.registrationTime?.toISOString() || null,
-          rawHandlersCount: debugInfo.rawHandlersCount
-        },
-        registeredHandlers: diagnostics.registeredHandlers,
-        supportedIntents,
-        debugInfo
-      };
-    } catch (error) {
-      this.logger.error(`Failed to debug registry state: ${error.message}`, error.stack);
-      return {
-        success: false,
-        error: {
-          message: error.message,
-          type: error.constructor.name,
-          stack: error.stack
-        }
-      };
-    }
-  }
-
-  @Get("force-init-registry")
-  @ApiOperation({
-    summary: "Force Init Registry - manually trigger handler registration",
-    description: "Forces re-registration of all handlers to fix broken registry instances"
-  })
-  async forceInitRegistry() {
-    this.logger.log("Forcing registry initialization");
-
-    try {
-      // Force re-registration
-      this.intentHandlerRegistry.forceReregistration();
-
-      const diagnostics = this.intentHandlerRegistry.getSystemDiagnostics();
-      const supportedIntents = this.intentHandlerRegistry.getAllSupportedIntents();
-
-      return {
-        success: true,
-        message: "Registry re-initialized successfully",
-        totalHandlers: diagnostics.totalHandlers,
-        supportedIntents,
-        registrationTime: diagnostics.registrationTime?.toISOString() || null
-      };
-    } catch (error) {
-      this.logger.error(`Failed to force init registry: ${error.message}`, error.stack);
-      return {
-        success: false,
-        error: {
-          message: error.message,
-          type: error.constructor.name,
-          stack: error.stack
-        }
-      };
-    }
-  }
-
   @Post("identify-shipment")
   @ApiOperation({ summary: "Test the shipment identification service directly" })
   @ApiBody({ type: TestAnalyzeEmailIntentsDto })
@@ -480,173 +77,4 @@ export class CoreAgentTestController {
 
     return this.shipmentIdentifierService.identifyShipment(messageContent, organizationId);
   }
-
-  @Post("test-intent-handler/:shipmentId/:organizationId")
-  @ApiOperation({
-    summary: "Test a specific intent handler with mock validated intent",
-    description:
-      "Allows testing individual intent handlers by providing a mock ValidatedIntent and using real shipment context"
-  })
-  @ApiParam({ name: "shipmentId", type: "number", description: "ID of the shipment for context" })
-  @ApiParam({
-    name: "organizationId",
-    type: "number",
-    description: "ID of the organization for service scoping"
-  })
-  @ApiBody({
-    description: "Mock validated intent to test with",
-    schema: {
-      type: "object",
-      properties: {
-        intent: {
-          type: "string",
-          enum: [
-            "GET_SHIPMENT_STATUS",
-            "REQUEST_CAD_DOCUMENT",
-            "REQUEST_RUSH_PROCESSING",
-            "DOCUMENTATION_COMING"
-          ],
-          description: "The intent type to test"
-        },
-        instructions: {
-          type: "array",
-          items: { type: "string" },
-          description: "User instructions/questions"
-        },
-        attachments: {
-          type: "array",
-          items: {
-            type: "object",
-            properties: {
-              id: { type: "string" },
-              filename: { type: "string" },
-              documentType: { type: "string" },
-              reason: { type: "string" }
-            }
-          }
-        }
-      },
-      required: ["intent", "instructions"]
-    }
-  })
-  @ApiResponse({
-    status: 200,
-    description: "Successfully executed intent handler and returned response fragments",
-    schema: {
-      type: "object",
-      properties: {
-        success: { type: "boolean" },
-        intent: { type: "string" },
-        shipmentId: { type: "number" },
-        handlerClass: { type: "string" },
-        fragmentCount: { type: "number" },
-        fragments: {
-          type: "array",
-          items: {
-            type: "object",
-            properties: {
-              template: { type: "string" },
-              priority: { type: "number" },
-              fragmentContext: { type: "object" }
-            }
-          }
-        },
-        timing: {
-          type: "object",
-          properties: {
-            totalMs: { type: "number" },
-            contextBuildMs: { type: "number" },
-            handlerMs: { type: "number" }
-          }
-        }
-      }
-    }
-  })
-  async testIntentHandler(
-    @Param("shipmentId", ParseIntPipe) shipmentId: number,
-    @Param("organizationId", ParseIntPipe) organizationId: number,
-    @Body()
-    body: {
-      intent: string;
-      instructions: string[];
-      attachments?: Array<{ id: string; filename: string; documentType: string; reason: string }>;
-    }
-  ) {
-    const startTime = Date.now();
-    this.logger.log(`Testing intent handler '${body.intent}' for shipment ${shipmentId}`);
-
-    try {
-      // 1. Build context
-      const contextStartTime = Date.now();
-      const context = await this.shipmentContextService.buildContext(shipmentId, organizationId);
-      const contextBuildTime = Date.now() - contextStartTime;
-
-      // 2. Get the handler
-      const handler = this.intentHandlerRegistry.getHandler(body.intent as any);
-      if (!handler) {
-        return {
-          success: false,
-          error: {
-            message: `No handler found for intent: ${body.intent}`,
-            availableIntents: this.intentHandlerRegistry.getAllSupportedIntents()
-          }
-        };
-      }
-
-      // 3. Create mock ValidatedIntent
-      const validatedIntent = {
-        intent: body.intent as any, // Cast to avoid type mismatch with intent enum
-        instructions: body.instructions,
-        attachments: body.attachments || []
-      };
-
-      // 4. Execute the handler
-      const handlerStartTime = Date.now();
-      const fragments = await handler.handle(validatedIntent as any, context);
-      const handlerTime = Date.now() - handlerStartTime;
-
-      const totalTime = Date.now() - startTime;
-
-      this.logger.log(
-        `Handler '${body.intent}' completed in ${handlerTime}ms, returned ${fragments.length} fragments`
-      );
-
-      return {
-        success: true,
-        intent: body.intent,
-        shipmentId,
-        handlerClass: handler.constructor.name,
-        fragmentCount: fragments.length,
-        fragments,
-        timing: {
-          totalMs: totalTime,
-          contextBuildMs: contextBuildTime,
-          handlerMs: handlerTime
-        },
-        context: {
-          customsStatus: context.shipment.customsStatus,
-          businessRules: {
-            canRush: context.canRush,
-            canGenerateCAD: context.canGenerateCAD,
-            isCompliant: context.isCompliant
-          }
-        }
-      };
-    } catch (error) {
-      const totalTime = Date.now() - startTime;
-      this.logger.error(`Failed to test intent handler '${body.intent}': ${error.message}`, error.stack);
-
-      return {
-        success: false,
-        intent: body.intent,
-        shipmentId,
-        timing: { totalMs: totalTime },
-        error: {
-          message: error.message,
-          type: error.constructor.name,
-          stack: error.stack
-        }
-      };
-    }
-  }
 }
diff --git a/apps/portal-api/src/core-agent/controllers/test/response-service.test.controller.ts b/apps/portal-api/src/core-agent/controllers/test/response-service.test.controller.ts
deleted file mode 100644
index 108c0fa5..********
--- a/apps/portal-api/src/core-agent/controllers/test/response-service.test.controller.ts
+++ /dev/null
@@ -1,206 +0,0 @@
-import { Controller, Get, Post, Param, Body, Query } from "@nestjs/common";
-import { ApiOperation, ApiTags, ApiParam, ApiBody } from "@nestjs/swagger";
-import { ShipmentResponseService } from "../../services/shipment-response.service";
-import { ShipmentContextService } from "../../services/shipment-context.service";
-import { ResponseFragment } from "../../types/response-fragment.types";
-
-/**
- * Test controller for debugging and validating the ShipmentResponseService.
- * Only available in development/test environments.
- */
-@ApiTags("Debug - Response Service")
-@Controller("debug/response-service")
-export class ResponseServiceTestController {
-  constructor(
-    private readonly responseService: ShipmentResponseService,
-    private readonly contextService: ShipmentContextService
-  ) {}
-
-  @Post("render-fragments/:shipmentId")
-  @ApiOperation({ summary: "Test fragment rendering with mock fragments" })
-  @ApiParam({ name: "shipmentId", description: "Shipment ID for context" })
-  @ApiBody({
-    description: "Array of response fragments to render",
-    schema: {
-      type: "object",
-      properties: {
-        fragments: {
-          type: "array",
-          items: {
-            type: "object",
-            properties: {
-              template: { type: "string" },
-              priority: { type: "number" },
-              fragmentContext: { type: "object" }
-            }
-          }
-        },
-        organizationId: { type: "number" }
-      }
-    }
-  })
-  async testFragmentRendering(
-    @Param("shipmentId") shipmentId: number,
-    @Body() body: { fragments: ResponseFragment[]; organizationId: number }
-  ) {
-    try {
-      // Build context for the shipment
-      const context = await this.contextService.buildContext(shipmentId, body.organizationId);
-
-      // Render the provided fragments
-      const renderedResponse = await this.responseService.renderFragments(body.fragments, context);
-
-      return {
-        success: true,
-        shipmentId,
-        fragmentCount: body.fragments.length,
-        uniqueFragments: [...new Set(body.fragments.map((f) => f.template))],
-        renderedResponse,
-        context: {
-          shipmentStatus: context.shipment.customsStatus,
-          businessRules: {
-            canRush: context.canRush,
-            canGenerateCAD: context.canGenerateCAD,
-            isCompliant: context.isCompliant
-          }
-        }
-      };
-    } catch (error) {
-      return {
-        success: false,
-        error: error.message,
-        stack: error.stack
-      };
-    }
-  }
-
-  @Get("validate-template/:templateName")
-  @ApiOperation({ summary: "Test if a template exists and can be rendered" })
-  @ApiParam({ name: "templateName", description: "Name of template to validate" })
-  async validateTemplate(@Param("templateName") templateName: string) {
-    // Create sample context for validation
-    const sampleContext = {
-      shipment: { id: 1, customsStatus: "PENDING" },
-      isCompliant: false,
-      canRush: true,
-      rushBlockingReason: "Test reason",
-      missingDocuments: ["Commercial Invoice", "Packing List"],
-      sideEffects: {
-        backofficeAlerts: {
-          rushProcessingSent: true
-        }
-      }
-    };
-
-    const isValid = await this.responseService.validateTemplate(templateName, sampleContext);
-
-    return {
-      templateName,
-      isValid,
-      sampleContext
-    };
-  }
-
-  @Get("test-scenarios")
-  @ApiOperation({ summary: "Get predefined test scenarios for fragment rendering" })
-  getTestScenarios() {
-    return {
-      scenarios: [
-        {
-          name: "Rush Processing Success",
-          fragments: [
-            { template: "rush-processing-success", priority: 1 },
-            { template: "customs-status", priority: 2 }
-          ]
-        },
-        {
-          name: "Rush Processing Blocked",
-          fragments: [
-            { template: "rush-processing-blocked", priority: 1 },
-            { template: "compliance-errors", priority: 2 },
-            { template: "acknowledgement-missing-docs", priority: 3 }
-          ]
-        },
-        {
-          name: "CAD Document Request",
-          fragments: [
-            { template: "cad-document-attached", priority: 1 },
-            { template: "customs-status", priority: 2 }
-          ]
-        },
-        {
-          name: "CAD Document Not Ready",
-          fragments: [
-            { template: "cad-document-not-ready", priority: 1 },
-            { template: "submission-required-notice", priority: 2 }
-          ]
-        },
-        {
-          name: "Status Check with Compliance Issues",
-          fragments: [
-            { template: "customs-status", priority: 1 },
-            { template: "compliance-errors", priority: 2 },
-            { template: "acknowledgement-missing-docs", priority: 3 },
-            { template: "eta", priority: 4 }
-          ]
-        }
-      ]
-    };
-  }
-
-  @Get("deduplication-test")
-  @ApiOperation({ summary: "Test fragment deduplication logic" })
-  async testDeduplication(@Query("organizationId") organizationId: number = 1) {
-    const duplicateFragments: ResponseFragment[] = [
-      { template: "customs-status", priority: 1 },
-      { template: "customs-status", priority: 2 }, // Duplicate
-      { template: "eta", priority: 3 },
-      { template: "customs-status", priority: 4 }, // Another duplicate
-      { template: "acknowledgement-missing-docs", priority: 5 }
-    ];
-
-    // Use a test shipment ID (you may need to adjust this)
-    const context = await this.contextService.buildContext(1, organizationId);
-    const rendered = await this.responseService.renderFragments(duplicateFragments, context);
-
-    return {
-      input: duplicateFragments,
-      inputCount: duplicateFragments.length,
-      expectedUniqueCount: 3, // customs-status, eta, acknowledgement-missing-docs
-      rendered
-    };
-  }
-
-  @Get("priority-sorting-test")
-  @ApiOperation({ summary: "Test fragment priority sorting" })
-  async testPrioritySorting(@Query("organizationId") organizationId: number = 1) {
-    const unsortedFragments: ResponseFragment[] = [
-      { template: "eta", priority: 20 },
-      { template: "rush-processing-success", priority: 1 },
-      { template: "customs-status", priority: 10 },
-      { template: "acknowledgement-missing-docs" }, // No priority
-      { template: "compliance-errors", priority: 15 }
-    ];
-
-    const context = await this.contextService.buildContext(1, organizationId);
-    const rendered = await this.responseService.renderFragments(unsortedFragments, context);
-
-    return {
-      input: unsortedFragments,
-      expectedOrder: [
-        "rush-processing-success (priority: 1)",
-        "customs-status (priority: 10)",
-        "compliance-errors (priority: 15)",
-        "eta (priority: 20)",
-        "acknowledgement-missing-docs (no priority)"
-      ],
-      rendered
-    };
-  }
-
-  @Get("system-diagnostics")
-  @ApiOperation({ summary: "Get system diagnostics for response service" })
-  getSystemDiagnostics() {
-    return this.responseService.getSystemDiagnostics();
-  }
-}
diff --git a/apps/portal-api/src/core-agent/core-agent.module.ts b/apps/portal-api/src/core-agent/core-agent.module.ts
index ea0b7243..dae858e5 100644
--- a/apps/portal-api/src/core-agent/core-agent.module.ts
+++ b/apps/portal-api/src/core-agent/core-agent.module.ts
@@ -1,22 +1,21 @@
 import * as path from "path";
 import { Global, Module, forwardRef } from "@nestjs/common";
+import { BullModule } from "@nestjs/bullmq";
 import { TemplateManagerModule } from "nest-modules";
 import { CoreAgentService } from "./services/core-agent.service";
 import { ShipmentModule } from "@/shipment/shipment.module";
 import { AggregationModule } from "@/aggregation/aggregation.module";
-import { ImporterModule } from "@/importer/importer.module";
+import { EmailModule } from "@/email/email.module";
 import { EmailIntentAnalysisService } from "./services/email-intent-analysis.service";
 import { AnswerUserQueryService } from "./services/answer-user-query.service";
 import { ShipmentIdentifierService } from "./services/shipment-identifier.service";
-import { ShipmentContextService } from "./services/shipment-context.service";
-import { ShipmentResponseService } from "./services/shipment-response.service";
-import { IntentHandlerRegistry } from "./services/intent-handler-registry.service";
+import { EmailOrchestratorService } from "./services/email-orchestrator.service";
+import { CoreAgentEmailSagaListener } from "./listeners/email-saga.listener";
 import { CoreAgentTestController } from "./controllers/test/core-agent.test.controller";
-import { ResponseServiceTestController } from "./controllers/test/response-service.test.controller";
-import { GetShipmentStatusHandler } from "./handlers/get-shipment-status.handler";
-import { RequestCADDocumentHandler } from "./handlers/request-cad-document.handler";
-import { RequestRushProcessingHandler } from "./handlers/request-rush-processing.handler";
-import { DocumentationComingHandler } from "./handlers/documentation-coming.handler";
+import { EmailSavedProcessor } from "./processors/email-saved.processor";
+import { EventEmitterProcessor } from "./processors/event-emitter.processor";
+import { HandleRequestMessageProcessor } from "./processors/handle-request-message.processor";
+import { CORE_AGENT_QUEUES } from "./types/queue.types";
 
 /**
  * @module CoreAgentModule
@@ -29,60 +28,38 @@ import { DocumentationComingHandler } from "./handlers/documentation-coming.hand
 @Global()
 @Module({
   imports: [
-    forwardRef(() => ShipmentModule),
+    ShipmentModule,
     forwardRef(() => AggregationModule),
-    forwardRef(() => ImporterModule),
+    BullModule.registerQueue(...CORE_AGENT_QUEUES),
+    BullModule.registerFlowProducer({
+      name: "core-agent-flow"
+    }),
     TemplateManagerModule.forFeature({
       templatesPath: path.join(__dirname, "templates"),
       nunjucksConfig: {
         autoescape: true
       }
-    })
+    }),
+    forwardRef(() => EmailModule)
   ],
   providers: [
     CoreAgentService,
     EmailIntentAnalysisService,
     AnswerUserQueryService,
     ShipmentIdentifierService,
-    ShipmentContextService,
-    ShipmentResponseService,
-    IntentHandlerRegistry,
-    // Register individual handlers as providers
-    GetShipmentStatusHandler,
-    RequestCADDocumentHandler,
-    RequestRushProcessingHandler,
-    DocumentationComingHandler,
-    // Provide array of all handlers for injection
-    {
-      provide: "INTENT_HANDLERS",
-      useFactory: (
-        getShipmentStatusHandler: GetShipmentStatusHandler,
-        requestCADDocumentHandler: RequestCADDocumentHandler,
-        requestRushProcessingHandler: RequestRushProcessingHandler,
-        documentationComingHandler: DocumentationComingHandler
-      ) => [
-        getShipmentStatusHandler,
-        requestCADDocumentHandler,
-        requestRushProcessingHandler,
-        documentationComingHandler
-      ],
-      inject: [
-        GetShipmentStatusHandler,
-        RequestCADDocumentHandler,
-        RequestRushProcessingHandler,
-        DocumentationComingHandler
-      ]
-    }
+    EmailOrchestratorService,
+    CoreAgentEmailSagaListener,
+    EmailSavedProcessor,
+    EventEmitterProcessor,
+    HandleRequestMessageProcessor
   ],
   exports: [
     CoreAgentService,
     EmailIntentAnalysisService,
     AnswerUserQueryService,
     ShipmentIdentifierService,
-    ShipmentContextService,
-    ShipmentResponseService,
-    IntentHandlerRegistry
+    EmailOrchestratorService
   ],
-  controllers: [CoreAgentTestController, ResponseServiceTestController]
+  controllers: [CoreAgentTestController]
 })
 export class CoreAgentModule {}
diff --git a/apps/portal-api/src/core-agent/events/email-processing.events.ts b/apps/portal-api/src/core-agent/events/email-processing.events.ts
new file mode 100644
index ********..d597e576
--- /dev/null
+++ b/apps/portal-api/src/core-agent/events/email-processing.events.ts
@@ -0,0 +1,172 @@
+import { EmailStatus } from "nest-modules";
+
+/**
+ * Core-Agent Email Processing Events
+ *
+ * Events emitted by Core-Agent to communicate email processing status updates
+ * back to the Email module. This enables clean separation between modules
+ * while maintaining proper status tracking and error handling.
+ *
+ * Event Flow:
+ * Core-Agent → Status Update Events → Email Module Listeners → Database Updates
+ */
+
+/**
+ * Base class for all Core-Agent email processing events
+ */
+export abstract class BaseCoreAgentEmailEvent {
+  constructor({
+    emailId,
+    organizationId,
+    status
+  }: {
+    emailId: number;
+    organizationId: number;
+    status: EmailStatus;
+  }) {
+    this.emailId = emailId;
+    this.organizationId = organizationId;
+    this.status = status;
+  }
+
+  /** ID of the email being processed */
+  public readonly emailId: number;
+
+  /** Organization ID of the email */
+  public readonly organizationId: number;
+
+  /** Current email status */
+  public readonly status: EmailStatus;
+}
+
+/**
+ * Event emitted when Core-Agent starts processing an email
+ * Indicates that Core-Agent has taken control and begun business logic processing
+ */
+export class CoreAgentProcessingStartedEvent extends BaseCoreAgentEmailEvent {
+  constructor({
+    emailId,
+    organizationId,
+    status = EmailStatus.AWAIT_SHIPMENT_SEARCH
+  }: {
+    emailId: number;
+    organizationId: number;
+    status?: EmailStatus;
+  }) {
+    super({ emailId, organizationId, status });
+  }
+}
+
+/**
+ * Event emitted when Core-Agent successfully completes email processing
+ * Indicates that all business logic has been executed and email is ready for response
+ */
+export class CoreAgentProcessingCompletedEvent extends BaseCoreAgentEmailEvent {
+  constructor({
+    emailId,
+    organizationId,
+    status = EmailStatus.RESPONDED
+  }: {
+    emailId: number;
+    organizationId: number;
+    status?: EmailStatus;
+  }) {
+    super({ emailId, organizationId, status });
+  }
+}
+
+/**
+ * Event emitted when Core-Agent encounters an error during email processing
+ * Includes error details for debugging and recovery purposes
+ */
+export class CoreAgentProcessingFailedEvent extends BaseCoreAgentEmailEvent {
+  constructor({
+    emailId,
+    organizationId,
+    status = EmailStatus.FAILED_RESPONDING,
+    error
+  }: {
+    emailId: number;
+    organizationId: number;
+    status?: EmailStatus;
+    error?: string;
+  }) {
+    super({ emailId, organizationId, status });
+    this.error = error;
+  }
+
+  /** Error message describing the failure */
+  public readonly error?: string;
+}
+
+/**
+ * Event emitted when Core-Agent updates email status during processing
+ * Used for intermediate status updates during the processing pipeline
+ */
+export class CoreAgentStatusUpdateEvent extends BaseCoreAgentEmailEvent {
+  constructor({
+    emailId,
+    organizationId,
+    status,
+    previousStatus,
+    details
+  }: {
+    emailId: number;
+    organizationId: number;
+    status: EmailStatus;
+    previousStatus?: EmailStatus;
+    details?: string;
+  }) {
+    super({ emailId, organizationId, status });
+    this.previousStatus = previousStatus;
+    this.details = details;
+  }
+
+  /** Previous email status before the update */
+  public readonly previousStatus?: EmailStatus;
+
+  /** Additional details about the status update */
+  public readonly details?: string;
+}
+
+/**
+ * Event names for Core-Agent email processing events
+ * Used for event emission and listening
+ */
+export enum CoreAgentEmailEvent {
+  PROCESSING_STARTED = "core-agent.processing.started",
+  PROCESSING_COMPLETED = "core-agent.processing.completed",
+  PROCESSING_FAILED = "core-agent.processing.failed",
+  STATUS_UPDATE = "core-agent.status.update"
+}
+
+/**
+ * Type definitions for event payloads
+ * Used for type safety in event emission and handling
+ */
+export interface CoreAgentProcessingStartedPayload {
+  emailId: number;
+  organizationId: number;
+  status: EmailStatus;
+}
+
+export interface CoreAgentProcessingCompletedPayload {
+  emailId: number;
+  organizationId: number;
+  status: EmailStatus;
+}
+
+export interface CoreAgentProcessingFailedPayload {
+  emailId: number;
+  organizationId: number;
+  status: EmailStatus;
+  error?: string;
+}
+
+export interface CoreAgentStatusUpdatePayload {
+  emailId: number;
+  organizationId: number;
+  status: EmailStatus;
+  previousStatus?: EmailStatus;
+  details?: string;
+}
diff --git a/apps/portal-api/src/core-agent/handlers/base-intent-handler.ts b/apps/portal-api/src/core-agent/handlers/base-intent-handler.ts
deleted file mode 100644
index d8c42f75..********
--- a/apps/portal-api/src/core-agent/handlers/base-intent-handler.ts
+++ /dev/null
@@ -1,197 +0,0 @@
-import { Logger } from "@nestjs/common";
-import { IntentHandler, IntentClassificationMeta } from "../interfaces/intent-handler.interface";
-import { ShipmentContext } from "../types/shipment-context.types";
-import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
-
-/**
- * Abstract base class for all intent handlers providing common functionality.
- * Intent handlers should extend this class to inherit useful helper methods.
- */
-export abstract class BaseIntentHandler implements IntentHandler {
-  protected readonly logger = new Logger(this.constructor.name);
-
-  /**
-   * Classification metadata for dynamic LLM prompt generation.
-   * Each concrete handler must define this with their specific intent, description, and examples.
-   */
-  abstract readonly classificationMeta: IntentClassificationMeta;
-
-  /**
-   * Handle the validated intent and return response fragments.
-   * Each concrete handler must implement this method.
-   */
-  abstract handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]>;
-
-  // ===== HELPER METHODS =====
-
-  /**
-   * Mark that the user directly asked for a specific capability.
-   * This prevents duplicate responses and helps with context tracking.
-   */
-  protected markAsDirectlyAsked(context: ShipmentContext, key: string): void {
-    context.directlyAsked[key] = true;
-    this.logger.debug(`Marked '${key}' as directly asked for shipment ${context.shipment.id}`);
-  }
-
-  /**
-   * Add compliance-related fragments if the shipment has compliance issues.
-   * This is commonly needed across multiple intent handlers.
-   */
-  protected addComplianceFragmentsIfNeeded(
-    fragments: ResponseFragment[],
-    context: ShipmentContext,
-    basePriority: number = 10
-  ): void {
-    if (context.missingDocuments.length > 0) {
-      fragments.push({
-        template: "acknowledgement-missing-docs",
-        priority: basePriority
-      });
-    }
-
-    if (context.complianceErrors.length > 0) {
-      fragments.push({
-        template: "compliance-errors",
-        priority: basePriority + 1
-      });
-    }
-
-    if (context.nonCompliantInvoices.length > 0) {
-      fragments.push({
-        template: "compliance-errors",
-        priority: basePriority + 2
-      });
-    }
-  }
-
-  /**
-   * Add a submission required notice if the shipment isn't submitted yet.
-   * Commonly used when features require customs submission.
-   */
-  protected addSubmissionRequiredNoticeIfNeeded(
-    fragments: ResponseFragment[],
-    context: ShipmentContext,
-    priority: number = 20
-  ): void {
-    if (!context.isSubmitted) {
-      fragments.push({
-        template: "submission-required-notice",
-        priority
-      });
-    }
-  }
-
-  /**
-   * Send a backoffice alert email for manual processing requests.
-   * Returns true if alert was sent successfully.
-   */
-  protected async sendBackofficeAlert(
-    alertType: string,
-    shipmentId: number,
-    userInstructions: string[],
-    context: ShipmentContext
-  ): Promise<boolean> {
-    try {
-      const subject = `${alertType} - Shipment ID: ${shipmentId}`;
-      const body = [
-        `${alertType} requested for shipment ID: ${shipmentId}.`,
-        "",
-        "User Instructions:",
-        ...userInstructions.map((instruction) => `- ${instruction}`),
-        "",
-        "Please review and take appropriate action."
-      ].join("\n");
-
-      await context._services.emailService.sendBackofficeAlert(subject, body, context.organization.id);
-
-      this.logger.log(`Sent ${alertType} alert for shipment ${shipmentId}`);
-      return true;
-    } catch (error) {
-      this.logger.error(
-        `Failed to send ${alertType} alert for shipment ${shipmentId}: ${error.message}`,
-        error.stack
-      );
-      return false;
-    }
-  }
-
-  /**
-   * Create a standard error fragment when intent processing fails.
-   */
-  protected createErrorFragment(errorMessage?: string): ResponseFragment {
-    return {
-      template: "contact-support",
-      priority: 1000, // Low priority, appears at end
-      fragmentContext: {
-        errorMessage: errorMessage || "Unable to process this request at this time"
-      }
-    };
-  }
-
-  /**
-   * Safe wrapper for executing potentially failing operations.
-   * Returns success/failure and logs errors automatically.
-   */
-  protected async safeExecute<T>(
-    operation: () => Promise<T>,
-    errorContext: string
-  ): Promise<{ success: boolean; result?: T; error?: Error }> {
-    try {
-      const result = await operation();
-      return { success: true, result };
-    } catch (error) {
-      this.logger.error(`${errorContext}: ${error.message}`, error.stack);
-      return { success: false, error };
-    }
-  }
-
-  /**
-   * Extract instructions from a validated intent.
-   * Uses the existing ValidatedEmailAction.instructions field.
-   */
-  protected extractInstructions(validatedIntent: ValidatedIntent): string[] {
-    return validatedIntent.instructions || [];
-  }
-
-  /**
-   * Create a high-priority fragment that should appear near the top of the response.
-   */
-  protected createHighPriorityFragment(
-    template: string,
-    fragmentContext?: Record<string, any>
-  ): ResponseFragment {
-    return {
-      template,
-      priority: 1,
-      fragmentContext
-    };
-  }
-
-  /**
-   * Create a medium-priority fragment for standard responses.
-   */
-  protected createMediumPriorityFragment(
-    template: string,
-    fragmentContext?: Record<string, any>
-  ): ResponseFragment {
-    return {
-      template,
-      priority: 10,
-      fragmentContext
-    };
-  }
-
-  /**
-   * Create a low-priority fragment for supplementary information.
-   */
-  protected createLowPriorityFragment(
-    template: string,
-    fragmentContext?: Record<string, any>
-  ): ResponseFragment {
-    return {
-      template,
-      priority: 20,
-      fragmentContext
-    };
-  }
-}
diff --git a/apps/portal-api/src/core-agent/handlers/documentation-coming.handler.ts b/apps/portal-api/src/core-agent/handlers/documentation-coming.handler.ts
deleted file mode 100644
index ba85bd9a..********
--- a/apps/portal-api/src/core-agent/handlers/documentation-coming.handler.ts
+++ /dev/null
@@ -1,48 +0,0 @@
-import { Injectable } from "@nestjs/common";
-import { BaseIntentHandler } from "./base-intent-handler";
-import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
-import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
-import { ShipmentContext } from "../types/shipment-context.types";
-
-/**
- * Handles user notifications about incoming documentation.
- * Provides acknowledgment and sets expectations.
- */
-@Injectable()
-export class DocumentationComingHandler extends BaseIntentHandler {
-  readonly classificationMeta: IntentClassificationMeta = {
-    intent: "DOCUMENTATION_COMING" as const,
-    description: "User is notifying that additional documents are coming or will be sent soon",
-    examples: [
-      "I'll send you the missing documents shortly",
-      "Documents are coming",
-      "Paperwork will be forwarded tomorrow",
-      "Additional documentation incoming"
-    ],
-    keywords: ["coming", "sending", "forward", "incoming", "tomorrow", "shortly", "soon"]
-  };
-
-  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
-    const fragments: ResponseFragment[] = [];
-
-    this.logger.log(`Handling DOCUMENTATION_COMING for shipment ${context.shipment.id}`);
-
-    // Simple acknowledgment
-    fragments.push({
-      template: "documentation-coming-acknowledged",
-      priority: 1
-    });
-
-    // If shipment is not compliant, add helpful context about what's still needed
-    if (!context.isCompliant && context.missingDocuments.length > 0) {
-      fragments.push({
-        template: "acknowledgement-missing-docs",
-        priority: 2
-      });
-    }
-
-    this.markAsDirectlyAsked(context, "documentation_coming");
-
-    return fragments;
-  }
-}
diff --git a/apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts b/apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts
deleted file mode 100644
index 1b71e92b..********
--- a/apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts
+++ /dev/null
@@ -1,103 +0,0 @@
-import { Injectable } from "@nestjs/common";
-import { BaseIntentHandler } from "./base-intent-handler";
-import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
-import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
-import { ShipmentContext } from "../types/shipment-context.types";
-import { CoreAgentService } from "../services/core-agent.service";
-
-/**
- * Handles shipment status inquiries and questions.
- * This is the most complex handler that can generate multiple fragments
- * based on the current state of the shipment.
- */
-@Injectable()
-export class GetShipmentStatusHandler extends BaseIntentHandler {
-  constructor(private readonly coreAgentService: CoreAgentService) {
-    super();
-  }
-
-  readonly classificationMeta: IntentClassificationMeta = {
-    intent: "GET_SHIPMENT_STATUS" as const,
-    description: "Checks and inquiries on shipment status, including customs processing or clearance status",
-    examples: [
-      "Where's my load CCN123456789 at? Did it clear customs yet?",
-      "Any update on HBL987? What's the hold up?",
-      "What's the current status of my shipment?",
-      "Has my cargo been released?",
-      "When will customs clear this?"
-    ],
-    keywords: ["status", "update", "where", "customs", "clearance", "released", "cleared", "progress"]
-  };
-
-  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
-    const fragments: ResponseFragment[] = [];
-    const instructions = this.extractInstructions(validatedIntent);
-
-    this.logger.log(
-      `Handling GET_SHIPMENT_STATUS for shipment ${context.shipment.id} with ${instructions.length} questions`
-    );
-
-    // Try to answer specific questions using CoreAgentService
-    if (instructions.length > 0) {
-      try {
-        const answerString = await this.coreAgentService.answerShipmentQuestions(
-          context.shipment.id,
-          instructions
-        );
-
-        if (answerString) {
-          fragments.push({
-            template: "answer-question-template",
-            priority: 1,
-            fragmentContext: { answerString }
-          });
-        }
-      } catch (error) {
-        this.logger.error(
-          `CoreAgentService.answerShipmentQuestions failed for shipment ${context.shipment.id}: ${error.message}`,
-          error.stack
-        );
-
-        // Fall back to structured status response
-        this.addStructuredStatusFragments(fragments, context);
-      }
-    } else {
-      // No specific questions - provide structured status
-      this.addStructuredStatusFragments(fragments, context);
-    }
-
-    this.markAsDirectlyAsked(context, "shipment_status");
-
-    return fragments;
-  }
-
-  /**
-   * Add structured status fragments based on shipment state.
-   */
-  private addStructuredStatusFragments(fragments: ResponseFragment[], context: ShipmentContext): void {
-    // Core status - always included
-    fragments.push({ template: "customs-status", priority: 10 });
-
-    // Conditional fragments based on context
-    if (!context.isCompliant) {
-      this.addComplianceFragmentsIfNeeded(fragments, context);
-    }
-
-    if (context.isSubmitted) {
-      fragments.push({ template: "submission-status", priority: 15 });
-    }
-
-    if (context.isReleased) {
-      fragments.push({ template: "release-status", priority: 16 });
-    }
-
-    if (context.etaInformation.etaPortValue) {
-      fragments.push({ template: "eta", priority: 20 });
-    }
-
-    fragments.push({ template: "shipping-status", priority: 25 });
-
-    // Add shipment identifiers for reference
-    fragments.push({ template: "shipment-identifiers", priority: 30 });
-  }
-}
diff --git a/apps/portal-api/src/core-agent/handlers/request-cad-document.handler.ts b/apps/portal-api/src/core-agent/handlers/request-cad-document.handler.ts
deleted file mode 100644
index 438073f4..********
--- a/apps/portal-api/src/core-agent/handlers/request-cad-document.handler.ts
+++ /dev/null
@@ -1,141 +0,0 @@
-import { Injectable } from "@nestjs/common";
-import { BaseIntentHandler } from "./base-intent-handler";
-import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
-import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
-import { ShipmentContext } from "../types/shipment-context.types";
-import { CommercialInvoice } from "nest-modules";
-
-/**
- * Handles requests for CAD (Canada Entry) documents.
- * Evaluates business rules and generates CAD documents when possible.
- */
-@Injectable()
-export class RequestCADDocumentHandler extends BaseIntentHandler {
-  readonly classificationMeta: IntentClassificationMeta = {
-    intent: "REQUEST_CAD_DOCUMENT" as const,
-    description: "User is requesting CAD (Canada Entry) document or customs entry document",
-    examples: [
-      "Can you send me the CAD document?",
-      "I need the Canada Entry",
-      "Please provide the customs entry document",
-      "Send me the entry paperwork"
-    ],
-    keywords: ["cad", "canada entry", "customs entry", "entry document", "paperwork"]
-  };
-
-  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
-    const fragments: ResponseFragment[] = [];
-
-    this.logger.log(`Handling REQUEST_CAD_DOCUMENT for shipment ${context.shipment.id}`);
-
-    // Check if CAD can be generated
-    if (context.canGenerateCAD) {
-      try {
-        // Generate CAD attachment
-        const cadAttachment = await this.generateCADAttachment(context);
-
-        if (cadAttachment) {
-          // Store attachment in side effects
-          context.sideEffects.cadDocument = cadAttachment;
-
-          fragments.push({
-            template: "cad-document-attached",
-            priority: 1
-          });
-        } else {
-          // Generation failed
-          fragments.push({
-            template: "contact-support",
-            priority: 1
-          });
-        }
-      } catch (error) {
-        this.logger.error(
-          `Failed to generate CAD document for shipment ${context.shipment.id}: ${error.message}`,
-          error.stack
-        );
-
-        fragments.push({
-          template: "contact-support",
-          priority: 1
-        });
-      }
-    } else {
-      // Cannot generate CAD - explain why
-      fragments.push({
-        template: "cad-document-not-ready",
-        priority: 1
-      });
-    }
-
-    this.markAsDirectlyAsked(context, "cad_document");
-
-    return fragments;
-  }
-
-  /**
-   * Generate CAD attachment using existing RNS service.
-   */
-  private async generateCADAttachment(context: ShipmentContext) {
-    try {
-      // Get commercial invoices for the shipment
-      const commercialInvoices = await this.getCommercialInvoices(context);
-
-      if (commercialInvoices.length === 0) {
-        this.logger.warn(`No commercial invoices found for shipment ${context.shipment.id}`);
-        return null;
-      }
-
-      // Get organization importer
-      const importersResponse = await context._services.importerService.getImporters({
-        organizationId: context.organization.id,
-        limit: 1
-      });
-      const organizationImporter =
-        importersResponse.importers.length > 0 ? importersResponse.importers[0] : null;
-
-      // Generate CAD attachment using the clean API
-      const cadAttachment = await context._services.rnsStatusChangeEmailSender.createCADAttachment(
-        context.shipment,
-        commercialInvoices,
-        organizationImporter
-      );
-
-      // Clean the base64 string to remove any whitespace or formatting characters
-      return {
-        ...cadAttachment,
-        b64Data: cadAttachment.b64Data.replace(/[\s\r\n\t]/g, "")
-      };
-    } catch (error) {
-      this.logger.error(
-        `Error generating CAD attachment for shipment ${context.shipment.id}: ${error.message}`,
-        error.stack
-      );
-      return null;
-    }
-  }
-
-  /**
-   * Get commercial invoices for the shipment.
-   */
-  private async getCommercialInvoices(context: ShipmentContext): Promise<CommercialInvoice[]> {
-    try {
-      // Use the context services to get commercial invoices
-      const commercialInvoices = await context._services.commercialInvoiceService.getByShipment(
-        context.shipment.id,
-        context.organization.id
-      );
-
-      this.logger.log(
-        `Found ${commercialInvoices.length} commercial invoices for shipment ${context.shipment.id}`
-      );
-      return commercialInvoices;
-    } catch (error) {
-      this.logger.error(
-        `Failed to get commercial invoices for shipment ${context.shipment.id}: ${error.message}`,
-        error.stack
-      );
-      return [];
-    }
-  }
-}
diff --git a/apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts b/apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts
deleted file mode 100644
index a3516536..********
--- a/apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts
+++ /dev/null
@@ -1,92 +0,0 @@
-import { Injectable } from "@nestjs/common";
-import { BaseIntentHandler } from "./base-intent-handler";
-import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
-import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
-import { ShipmentContext } from "../types/shipment-context.types";
-
-/**
- * Handles requests for expedited/rush processing of shipments.
- * Evaluates business rules and sends backoffice alerts when appropriate.
- */
-@Injectable()
-export class RequestRushProcessingHandler extends BaseIntentHandler {
-  readonly classificationMeta: IntentClassificationMeta = {
-    intent: "REQUEST_RUSH_PROCESSING" as const,
-    description: "User is requesting expedited or rush processing for their shipment",
-    examples: [
-      "Can you rush this shipment?",
-      "We need this expedited",
-      "Please prioritize this load",
-      "This is urgent, can you speed it up?",
-      "Rush processing needed"
-    ],
-    keywords: ["rush", "urgent", "expedite", "prioritize", "speed", "asap", "quickly", "fast"]
-  };
-
-  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
-    const fragments: ResponseFragment[] = [];
-    const instructions = this.extractInstructions(validatedIntent);
-
-    this.logger.log(`Handling REQUEST_RUSH_PROCESSING for shipment ${context.shipment.id}`);
-
-    // Check if rush processing is possible
-    if (context.canRush) {
-      // Send backoffice alert
-      const alertSent = await this.sendBackofficeAlert(
-        "rush-processing",
-        context.shipment.id,
-        instructions,
-        context
-      );
-
-      // Mark alert as sent in side effects
-      context.sideEffects.backofficeAlerts.rushProcessingSent = alertSent;
-
-      fragments.push({
-        template: "rush-processing-success",
-        priority: 1
-      });
-    } else {
-      // Cannot rush - explain why
-      fragments.push({
-        template: "rush-processing-blocked",
-        priority: 1
-      });
-
-      // If not compliant, add details about what's missing
-      if (!context.isCompliant) {
-        this.addComplianceFragmentsIfNeeded(fragments, context);
-      }
-    }
-
-    this.markAsDirectlyAsked(context, "rush_processing");
-
-    return fragments;
-  }
-
-  /**
-   * Send backoffice alert for rush processing request.
-   */
-  protected async sendBackofficeAlert(
-    alertType: string,
-    shipmentId: number,
-    instructions: string[],
-    context: ShipmentContext
-  ): Promise<boolean> {
-    try {
-      const subject = `Rush Processing Request - Shipment ID: ${shipmentId}`;
-      const body = `Rush processing requested for shipment ID: ${shipmentId}.\nUser Instructions/Questions:\n${instructions.join("\n")}\n\nPlease review.`;
-
-      await context._services.emailService.sendBackofficeAlert(subject, body, context.organization.id);
-
-      this.logger.log(`Backoffice alert sent for rush processing request - shipment ${shipmentId}`);
-      return true;
-    } catch (error) {
-      this.logger.error(
-        `Failed to send backoffice alert for rush processing - shipment ${shipmentId}: ${error.message}`,
-        error.stack
-      );
-      return false;
-    }
-  }
-}
diff --git a/apps/portal-api/src/core-agent/interfaces/intent-handler.interface.ts b/apps/portal-api/src/core-agent/interfaces/intent-handler.interface.ts
deleted file mode 100644
index beab6f78..********
--- a/apps/portal-api/src/core-agent/interfaces/intent-handler.interface.ts
+++ /dev/null
@@ -1,39 +0,0 @@
-import { EMAIL_INTENTS } from "@/email/types/ai-agent.types";
-import { ShipmentContext } from "../types/shipment-context.types";
-import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
-
-/**
- * Classification metadata for dynamic LLM prompt generation.
- * Each intent handler provides this information for the LLM to classify user requests.
- */
-export interface IntentClassificationMeta {
-  /** The intent type this handler manages (from EMAIL_INTENTS) */
-  intent: (typeof EMAIL_INTENTS)[number];
-
-  /** Human-readable description of when this intent should be used */
-  description: string;
-
-  /** Example user requests that should trigger this intent */
-  examples: string[];
-
-  /** Optional additional context or keywords that help classification */
-  keywords?: string[];
-}
-
-/**
- * Interface that all intent handlers must implement.
- * Each handler corresponds to one EMAIL_INTENTS type and handles all user requests classified under that intent.
- */
-export interface IntentHandler {
-  /** Classification metadata for dynamic LLM prompt generation */
-  readonly classificationMeta: IntentClassificationMeta;
-
-  /**
-   * Handles validated intents and returns response fragments to be rendered.
-   *
-   * @param validatedIntent - The ValidatedEmailAction with intent type and instructions
-   * @param context - Complete shipment context with all business rules evaluated
-   * @returns Array of response fragments that should be included in the final response
-   */
-  handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]>;
-}
diff --git a/apps/portal-api/src/core-agent/listeners/email-saga.listener.ts b/apps/portal-api/src/core-agent/listeners/email-saga.listener.ts
new file mode 100644
index ********..430528ca
--- /dev/null
+++ b/apps/portal-api/src/core-agent/listeners/email-saga.listener.ts
@@ -0,0 +1,223 @@
+import { Injectable, Logger } from "@nestjs/common";
+import { OnEvent } from "@nestjs/event-emitter";
+import { InjectQueue, InjectFlowProducer } from "@nestjs/bullmq";
+import { InjectDataSource } from "@nestjs/typeorm";
+import { Email, EmailStatus, FileBatch, buildFlowProducer, JobConfig } from "nest-modules";
+import { DataSource } from "typeorm";
+import { FlowProducer } from "bullmq";
+import { EmailSavedEvent } from "../../email/dto/event.dto";
+import { EmailEvent } from "../../email/types/event.types";
+import {
+  BATCH_DOCUMENT_AGGREGATED_EVENT,
+  BatchDocumentAggregatedEvent
+} from "../../aggregation/events/batch-document-aggregated.event";
+import { CoreAgentQueueName, EmailSavedQueue, HandleRequestMessageQueue } from "../types/queue.types";
+
+/**
+ * Core-Agent Email Saga Listener
+ *
+ * Implements saga pattern for email processing with proper state management.
+ * Uses pessimistic locking to ensure only SAVED emails are processed and
+ * transitions emails to AGGREGATING status before queuing core-agent work.
+ *
+ * Event Flow:
+ * EMAIL_SAVED (Email Module) → Core-Agent Saga Listener → State Transition → BullMQ Queue → EmailSavedProcessor
+ *
+ * Responsibilities:
+ * - Listen for EMAIL_SAVED events with pessimistic locking
+ * - Guard state transitions (only process SAVED emails)
+ * - Transition email status to AGGREGATING_EMAIL
+ * - Queue core-agent processing jobs via BullMQ
+ * - Ensure idempotent processing and error handling
+ */
+@Injectable()
+export class CoreAgentEmailSagaListener {
+  private readonly logger = new Logger(CoreAgentEmailSagaListener.name);
+
+  constructor(
+    @InjectDataSource()
+    private readonly dataSource: DataSource,
+    @InjectQueue(CoreAgentQueueName.EMAIL_SAVED)
+    private readonly emailSavedQueue: EmailSavedQueue,
+    @InjectQueue(CoreAgentQueueName.HANDLE_REQUEST_MESSAGE)
+    private readonly handleRequestMessageQueue: HandleRequestMessageQueue,
+    @InjectFlowProducer("core-agent-flow")
+    private readonly coreAgentFlowProducer: FlowProducer
+  ) {}
+
+  /**
+   * Handle EMAIL_SAVED events with proper saga pattern
+   * Uses pessimistic locking to ensure only SAVED emails are processed
+   * Transitions email to AGGREGATING status and queues core-agent processing
+   *
+   * @param event - EmailSavedEvent containing email metadata
+   */
+  @OnEvent(EmailEvent.EMAIL_SAVED)
+  async onEmailSaved(event: EmailSavedEvent): Promise<void> {
+    const { emailId, organizationId, gmailId, documents } = event;
+
+    this.logger.log(
+      `[CORE_AGENT_SAGA] EMAIL_SAVED event received - emailId: ${emailId}, organizationId: ${organizationId}, ` +
+        `gmailId: ${gmailId}, documents: ${documents.length}`
+    );
+
+    // Use pessimistic locking for state machine safety
+    const queryRunner = this.dataSource.createQueryRunner();
+    await queryRunner.connect();
+    await queryRunner.startTransaction();
+
+    try {
+      // Find email with pessimistic lock
+      const email = await queryRunner.manager.findOne(Email, {
+        where: { id: emailId },
+        relations: ["organization"],
+        lock: { mode: "pessimistic_write" }
+      });
+
+      if (!email) {
+        this.logger.error(`[CORE_AGENT_SAGA] Email ${emailId} not found, stopped processing`);
+        await queryRunner.rollbackTransaction();
+        return;
+      }
+
+      // State machine guard: only process if SAVED
+      if (email.status !== EmailStatus.SAVED) {
+        this.logger.log(
+          `[CORE_AGENT_SAGA] Email ${emailId} is in state ${email.status}, expected ${EmailStatus.SAVED}. Already processed or processing. Skipping.`
+        );
+        await queryRunner.rollbackTransaction();
+        return;
+      }
+
+      this.logger.log(
+        `[CORE_AGENT_SAGA] Processing email ${emailId} - transitioning from ${email.status} to ${EmailStatus.AGGREGATING_EMAIL}`
+      );
+
+      // Transition state to AGGREGATING
+      await queryRunner.manager.update(Email, { id: emailId }, { status: EmailStatus.AGGREGATING_EMAIL });
+
+      await queryRunner.commitTransaction();
+
+      this.logger.log(
+        `[CORE_AGENT_SAGA] Updated email ${emailId} status to ${EmailStatus.AGGREGATING_EMAIL}`
+      );
+
+      // Determine if email has attachments
+      const hasAttachments = documents && documents.length > 0;
+
+      // Build conditional flow based on attachment presence
+      const flowId = `email-processing-${emailId}`;
+
+      if (hasAttachments) {
+        // Flow for emails WITH attachments: process → emit READY_FOR_AGGREGATION
+        this.logger.log(`[CORE_AGENT_SAGA] Building flow for email ${emailId} WITH attachments`);
+
+        const flow = buildFlowProducer([
+          {
+            name: "process-email-saved",
+            queueName: CoreAgentQueueName.EMAIL_SAVED,
+            data: { emailId, organizationId, hasAttachments },
+            opts: { failParentOnFailure: true }
+          },
+          {
+            name: "emit-ready-for-aggregation",
+            queueName: CoreAgentQueueName.EVENT_EMITTER,
+            data: {
+              eventType: "BATCH_READY_FOR_AGGREGATION",
+              eventData: { emailId, organizationId }
+            }
+          }
+        ]);
+        await this.coreAgentFlowProducer.add(flow);
+
+        this.logger.log(
+          `[CORE_AGENT_SAGA] Queued attachment flow ${flowId} for email ${emailId}: process-email-saved → emit-ready-for-aggregation → [aggregation] → handle-request-message`
+        );
+      } else {
+        // Flow for emails WITHOUT attachments: process → handle request message
+        this.logger.log(`[CORE_AGENT_SAGA] Building flow for email ${emailId} WITHOUT attachments`);
+
+        const flow = buildFlowProducer([
+          {
+            name: "process-email-saved",
+            queueName: CoreAgentQueueName.EMAIL_SAVED,
+            data: { emailId, organizationId, hasAttachments },
+            opts: { failParentOnFailure: true }
+          },
+          {
+            name: "handle-request-message",
+            queueName: CoreAgentQueueName.HANDLE_REQUEST_MESSAGE,
+            data: { emailId, organizationId }
+          }
+        ]);
+        await this.coreAgentFlowProducer.add(flow);
+
+        this.logger.log(
+          `[CORE_AGENT_SAGA] Queued direct flow ${flowId} for email ${emailId}: process-email-saved → handle-request-message`
+        );
+      }
+    } catch (error) {
+      this.logger.error(`[CORE_AGENT_SAGA] Error processing EMAIL_SAVED for email ${emailId}:`, error);
+      if (queryRunner.isTransactionActive) {
+        await queryRunner.rollbackTransaction();
+      }
+      throw error;
+    } finally {
+      await queryRunner.release();
+    }
+  }
+
+  /**
+   * Handle aggregation completion from external Aggregation module
+   * Queues handle-request-message jobs for emails after aggregation completes
+   *
+   * This listener bridges the gap between aggregation completion and user intent processing.
+   * When aggregation completes, we need to find the associated email and queue the final
+   * processing step to handle user intents and generate responses.
+   *
+   * @param event - BatchDocumentAggregatedEvent containing aggregation results
+   */
+  @OnEvent(BATCH_DOCUMENT_AGGREGATED_EVENT)
+  async onAggregationCompleted(event: BatchDocumentAggregatedEvent): Promise<void> {
+    const { batchId, organizationId, result } = event;
+
+    this.logger.log(`[CORE_AGENT_SAGA] Aggregation completed for batch ${batchId}`);
+
+    try {
+      // Find email associated with this batch via gmailId
+      // The batchId corresponds to the email's gmailId in the current architecture
+      const email = await this.dataSource.manager.findOne(Email, {
+        where: { gmailId: batchId },
+        relations: ["organization"]
+      });
+
+      if (!email) {
+        this.logger.warn(
+          `[CORE_AGENT_SAGA] No email found for batchId ${batchId} - aggregation completion cannot be processed`
+        );
+        return;
+      }
+
+      this.logger.log(
+        `[CORE_AGENT_SAGA] Found email ${email.id} for completed batch ${batchId}, queuing handle-request-message`
+      );
+
+      // Queue handle request message job for post-aggregation processing
+      const jobId = `handle-request-post-aggregation-${batchId}`;
+      await this.handleRequestMessageQueue.add(jobId, {
+        emailId: email.id,
+        organizationId: email.organization.id
+      });
+
+      this.logger.log(
+        `[CORE_AGENT_SAGA] Queued handle-request-message job ${jobId} for email ${email.id} after aggregation`
+      );
+    } catch (error) {
+      this.logger.error(
+        `[CORE_AGENT_SAGA] Error processing aggregation completion for batch ${batchId}:`,
+        error
+      );
+      throw error;
+    }
+  }
+}
diff --git a/apps/portal-api/src/core-agent/processors/email-saved.processor.ts b/apps/portal-api/src/core-agent/processors/email-saved.processor.ts
new file mode 100644
index ********..1b5dbae3
--- /dev/null
+++ b/apps/portal-api/src/core-agent/processors/email-saved.processor.ts
@@ -0,0 +1,244 @@
+import { Processor, WorkerHost, InjectQueue } from "@nestjs/bullmq";
+import { Logger } from "@nestjs/common";
+import { ContextIdFactory, ModuleRef } from "@nestjs/core";
+import { EventEmitter2 } from "@nestjs/event-emitter";
+import { InjectDataSource } from "@nestjs/typeorm";
+import {
+  Email,
+  EmailStatus,
+  File,
+  FIND_ORGANIZATION_RELATIONS,
+  Organization,
+  FileBatch,
+  FileBatchCreator,
+  FileBatchStatus
+} from "nest-modules";
+import { DataSource } from "typeorm";
+import { FileBatchService } from "../../document/services/file-batch.service";
+import { EmailService } from "../../email/services/email.service";
+import { CoreAgentService } from "../services/core-agent.service";
+import { EmailEvent } from "../../email/types/event.types";
+import { ThreadShipmentNotFoundEvent, ThreadTooManyShipmentsFoundEvent } from "../../email/dto/event.dto";
+import { FileBatchEvent } from "../../document/events";
+import { generateEmailContent } from "../../email/utils/generate-email-content";
+import { generateRequest } from "../../email/utils/generate-request";
+import { MessageContent } from "../schemas/message-content.schema";
+import { CoreAgentQueueName, EmailSavedJob, DEFAULT_WORKER_OPTIONS } from "../types/queue.types";
+
+/**
+ * Core Agent Email Saved Processor
+ *
+ * BullMQ processor that handles initial email processing after EMAIL_SAVED events.
+ * This processor focuses on shipment identification and filebatch processing setup.
+ *
+ * Responsibilities:
+ * - Gather EmailContent (leaner version of FindThreadShipment)
+ * - Identify shipments using CoreAgentService
+ * - Handle multiple shipment scenarios (emit events for manual review)
+ * - Set email status for next listener
+ * - Fire off extraction/aggregation for the filebatch
+ * - Hand off responsibility to next processor
+ * - Focus on filebatch processing, not direct email-shipment association
+ */
+@Processor(
+  {
+    name: CoreAgentQueueName.EMAIL_SAVED
+  },
+  DEFAULT_WORKER_OPTIONS
+)
+export class EmailSavedProcessor extends WorkerHost {
+  private readonly logger = new Logger(EmailSavedProcessor.name);
+
+  constructor(
+    @InjectDataSource()
+    private readonly dataSource: DataSource,
+    private readonly moduleRef: ModuleRef,
+    private readonly eventEmitter: EventEmitter2,
+    private readonly coreAgentService: CoreAgentService
+  ) {
+    super();
+  }
+
+  async process(job: EmailSavedJob): Promise<void> {
+    const { emailId, organizationId, hasAttachments = false } = job.data;
+
+    this.logger.log(
+      `[EMAIL_SAVED_PROCESSOR] Processing email ${emailId}, organization ${organizationId}, hasAttachments: ${hasAttachments}`
+    );
+
+    const queryRunner = this.dataSource.createQueryRunner();
+    await queryRunner.connect();
+    await queryRunner.startTransaction();
+
+    try {
+      // Load organization with relations
+      const organization = await queryRunner.manager.findOne(Organization, {
+        where: { id: organizationId },
+        relations: FIND_ORGANIZATION_RELATIONS
+      });
+      if (!organization) throw new Error("Organization not found");
+
+      // Create synthetic request context for request-scoped services
+      const contextId = ContextIdFactory.create();
+      const requestContext = generateRequest(null, organization);
+      this.moduleRef.registerRequestByContextId(requestContext, contextId);
+
+      const emailService = await this.moduleRef.resolve(EmailService, contextId);
+      const fileBatchService = await this.moduleRef.resolve(FileBatchService, contextId, {
+        strict: false
+      });
+      await new Promise((resolve) => process.nextTick(resolve)); // Wait for children dependencies
+
+      // Load email
+      const email = await emailService.getEmailById(emailId, queryRunner);
+      if (!email) throw new Error("Email not found");
+      if (email.organization?.id !== organizationId) throw new Error("Email organization mismatch");
+
+      // Validate email is in correct state for processing
+      if (email.status !== EmailStatus.AGGREGATING_EMAIL) {
+        this.logger.warn(
+          `Email ${emailId} is in state ${email.status}, expected ${EmailStatus.AGGREGATING_EMAIL}. Skipping processing.`
+        );
+        await queryRunner.rollbackTransaction();
+        return;
+      }
+
+      this.logger.log(`Processing email ${emailId} with threadId: ${email.threadId}`);
+
+      // Check if email already has a shipment from an existing thread
+      const existingThreadShipment = await emailService.getEmailThreadShipment(email.threadId, queryRunner);
+
+      let eventToEmit: EmailEvent | null = null;
+      let eventData: any = null;
+      let shipmentId: number | null = null;
+
+      if (existingThreadShipment) {
+        this.logger.log(
+          `[EMAIL_SAVED_PROCESSOR] Found existing thread shipment ${existingThreadShipment.id} for email ${emailId} - skipping shipment identification`
+        );
+        shipmentId = existingThreadShipment.id;
+      } else {
+        this.logger.log(
+          `[EMAIL_SAVED_PROCESSOR] No existing thread shipment found, proceeding with identification`
+        );
+
+        // Gather EmailContent (leaner version of FindThreadShipment approach)
+        const threadAttachments: Array<File> = [];
+        if (hasAttachments) {
+          try {
+            threadAttachments.push(
+              ...(await fileBatchService.getFiles(email.gmailId, {
+                documents: {
+                  documentType: true,
+                  fields: true
+                }
+              }))
+            );
+          } catch (error) {
+            this.logger.warn(`Error getting attachments for email ${email.gmailId}: ${error.message}`);
+          }
+        }
+
+        // Generate EmailContent for shipment identification
+        const emailContent = generateEmailContent(email, [], threadAttachments);
+
+        // Convert to MessageContent for CoreAgentService
+        const messageContent: MessageContent = {
+          subject: emailContent.subject,
+          text: emailContent.text,
+          messageHistory: emailContent.emailHistories,
+          attachments: emailContent.attachments?.map((attachment) => ({
+            filename: attachment.filename
+            // extractedData: attachment.extractedData
+          }))
+        };
+
+        // Try to identify shipment using CoreAgent
+        const identifyResult = await this.coreAgentService.identifyShipment(messageContent, organizationId);
+        const foundShipments = identifyResult.foundIdentifiers || [];
+
+        this.logger.log(
+          `Shipment identification result: ${foundShipments.length} shipments found - ${identifyResult.reason}`
+        );
+
+        // Handle shipment identification results
+        if (foundShipments.length === 0) {
+          // Only emit THREAD_SHIPMENT_NOT_FOUND if there are no attachments
+          if (!hasAttachments) {
+            this.logger.warn(
+              `No shipment found and no attachments, will emit thread shipment not found event`
+            );
+            eventToEmit = EmailEvent.THREAD_SHIPMENT_NOT_FOUND;
+            eventData = new ThreadShipmentNotFoundEvent({
+              threadId: email.threadId,
+              organizationId
+            });
+          } else {
+            this.logger.log(
+              `No shipment found but has attachments - ignoring THREAD_SHIPMENT_NOT_FOUND condition`
+            );
+          }
+        } else if (foundShipments.length > 1) {
+          this.logger.warn(
+            `Multiple shipments found (${foundShipments.length}), will emit too many shipments event for manual review`
+          );
+          eventToEmit = EmailEvent.THREAD_TOO_MANY_SHIPMENTS_FOUND;
+          eventData = new ThreadTooManyShipmentsFoundEvent({
+            threadId: email.threadId,
+            organizationId
+          });
+        } else {
+          // Single shipment found - proceed with filebatch processing
+          shipmentId = foundShipments[0].shipmentId;
+          this.logger.log(`Single shipment identified: ${shipmentId}, proceeding with filebatch processing`);
+        }
+      }
+
+      // Update email status to indicate shipment identification is complete
+      await queryRunner.manager.update(
+        Email,
+        { id: emailId },
+        { status: EmailStatus.COMPLETED_SHIPMENT_SEARCH }
+      );
+
+      this.logger.log(`Updated email ${emailId} status to ${EmailStatus.COMPLETED_SHIPMENT_SEARCH}`);
+
+      // If we identified a shipment and have attachments, associate shipment with filebatch
+      if (hasAttachments && shipmentId) {
+        try {
+          await queryRunner.manager.update(
+            FileBatch,
+            { id: email.gmailId, organization: { id: organizationId } },
+            { shipmentId }
+          );
+          this.logger.log(`Associated filebatch ${email.gmailId} with shipment ${shipmentId}`);
+        } catch (error) {
+          this.logger.warn(`Failed to associate filebatch with shipment: ${error.message}`);
+          // Don't fail the entire job if filebatch association fails
+        }
+      }
+      await queryRunner.commitTransaction();
+
+      // Emit events after successful transaction commit
+      if (eventToEmit && eventData) {
+        this.logger.log(`[EMAIL_SAVED_PROCESSOR] Emitting event ${eventToEmit} for email ${emailId}`);
+        this.eventEmitter.emit(eventToEmit, eventData);
+      }
+
+      this.logger.log(
+        `[EMAIL_SAVED_PROCESSOR] Successfully processed email ${emailId} - ` +
+          `${hasAttachments ? "with" : "no"} attachments`
+      );
+    } catch (error) {
+      this.logger.error(`[EMAIL_SAVED_PROCESSOR] Error processing email ${emailId}:`, error);
+
+      if (queryRunner.isTransactionActive) {
+        await queryRunner.rollbackTransaction();
+      }
+
+      throw error; // Let BullMQ handle retries
+    } finally {
+      await queryRunner.release();
+    }
+  }
+}
diff --git a/apps/portal-api/src/core-agent/processors/event-emitter.processor.ts b/apps/portal-api/src/core-agent/processors/event-emitter.processor.ts
new file mode 100644
index ********..ad3c6ffa
--- /dev/null
+++ b/apps/portal-api/src/core-agent/processors/event-emitter.processor.ts
@@ -0,0 +1,36 @@
+import { Processor, WorkerHost } from "@nestjs/bullmq";
+import { Logger } from "@nestjs/common";
+import { EventEmitter2 } from "@nestjs/event-emitter";
+import { CoreAgentQueueName, EventEmitterJob, DEFAULT_WORKER_OPTIONS } from "../types/queue.types";
+
+/**
+ * Event Emitter Processor
+ *
+ * Generic BullMQ processor that handles event emission jobs.
+ * Takes eventType and eventData from job payload and emits them via EventEmitter2.
+ *
+ * This processor enables decoupling of event emission from job processing,
+ * allowing FlowProducer sequences to emit events as part of their workflow.
+ *
+ * Responsibilities:
+ * - Process EVENT_EMITTER jobs from BullMQ queue
+ * - Extract eventType and eventData from job payload
+ * - Emit events via EventEmitter2 for other listeners to handle
+ * - Provide logging for event emission tracking
+ */
+@Processor({ name: CoreAgentQueueName.EVENT_EMITTER }, DEFAULT_WORKER_OPTIONS)
+export class EventEmitterProcessor extends WorkerHost {
+  private readonly logger = new Logger(EventEmitterProcessor.name);
+
+  constructor(private readonly eventEmitter: EventEmitter2) {
+    super();
+  }
+
+  async process(job: EventEmitterJob): Promise<void> {
+    const { eventType, eventData } = job.data;
+
+    this.logger.log(`[EVENT_EMITTER] Emitting ${eventType} event`);
+    this.eventEmitter.emit(eventType, eventData);
+    this.logger.log(`[EVENT_EMITTER] Successfully emitted ${eventType} event`);
+  }
+}
diff --git a/apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts b/apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts
new file mode 100644
index ********..b22083e0
--- /dev/null
+++ b/apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts
@@ -0,0 +1,1093 @@
+import { Processor, WorkerHost } from "@nestjs/bullmq";
+import { Logger } from "@nestjs/common";
+import { ContextIdFactory, ModuleRef } from "@nestjs/core";
+import { EventEmitter2 } from "@nestjs/event-emitter";
+import { InjectDataSource } from "@nestjs/typeorm";
+import { DataSource } from "typeorm";
+
+// Entity imports
+import { Email, EmailStatus, File, FIND_ORGANIZATION_RELATIONS, Organization } from "nest-modules";
+
+// Services
+import { EmailService } from "../../email/services/email.service";
+import { FileBatchService } from "../../document/services/file-batch.service";
+import { ShipmentService } from "../../shipment/services/shipment.service";
+import { CoreAgentService } from "../services/core-agent.service";
+import { EmailIntentAnalysisService } from "../services/email-intent-analysis.service";
+
+// Queue types
+import { CoreAgentQueueName, HandleRequestMessageJob, DEFAULT_WORKER_OPTIONS } from "../types/queue.types";
+
+// Utility functions
+import { generateEmailContent } from "../../email/utils/generate-email-content";
+import { generateRequest } from "../../email/utils/generate-request";
+
+// Schemas and types
+import { MessageContent } from "../schemas/message-content.schema";
+import { ValidatedEmailAction } from "../../email/schemas/validated-email-intents.schema";
+
+// Constants
+import {
+  isSendCADReady,
+  isSendRNSProofOfReleaseReady
+} from "@/core-agent/constants/customs-definitions.constants";
+
+/**
+ * Handle Request Message Processor
+ *
+ * BullMQ processor that consolidates email intent processing into a single core-agent processor.
+ * This processor handles the complete email intent processing pipeline:
+ *
+ * 1. Load email + attachments + aggregation results from database
+ * 2. Extract/classify intents using existing core-agent services
+ * 3. Combine all PROCESS_DOCUMENT intents into single object with instruction list
+ * 4. Handle each intent including GET_SHIPMENT_STATUS (generate side effects + answers)
+ * 5. Generate email response using existing response templates
+ * 6. Send email response
+ *
+ * This processor is called in two scenarios:
+ * 1. Directly for emails without attachments (no aggregation needed)
+ * 2. After aggregation completion for emails with attachments
+ */
+@Processor({ name: CoreAgentQueueName.HANDLE_REQUEST_MESSAGE }, DEFAULT_WORKER_OPTIONS)
+export class HandleRequestMessageProcessor extends WorkerHost {
+  private readonly logger = new Logger(HandleRequestMessageProcessor.name);
+
+  constructor(
+    @InjectDataSource() private readonly dataSource: DataSource,
+    private readonly moduleRef: ModuleRef,
+    private readonly eventEmitter: EventEmitter2,
+    private readonly coreAgentService: CoreAgentService,
+    private readonly emailIntentAnalysisService: EmailIntentAnalysisService
+  ) {
+    super();
+  }
+
+  async process(job: HandleRequestMessageJob): Promise<void> {
+    const { emailId, organizationId } = job.data;
+
+    this.logger.log(
+      `[HANDLE_REQUEST_MESSAGE] Processing email ${emailId} for organization ${organizationId}`
+    );
+
+    const queryRunner = this.dataSource.createQueryRunner();
+    await queryRunner.connect();
+    await queryRunner.startTransaction();
+
+    try {
+      // 1. Load organization with relations
+      const organization = await queryRunner.manager.findOne(Organization, {
+        where: { id: organizationId },
+        relations: FIND_ORGANIZATION_RELATIONS
+      });
+      if (!organization) {
+        throw new Error(`Organization not found: ${organizationId}`);
+      }
+
+      // 2. Create synthetic request context for request-scoped services
+      const contextId = ContextIdFactory.create();
+      const requestContext = generateRequest(null, organization);
+      this.moduleRef.registerRequestByContextId(requestContext, contextId);
+
+      // 3. Resolve request-scoped services
+      const emailService = await this.moduleRef.resolve(EmailService, contextId);
+      const fileBatchService = await this.moduleRef.resolve(FileBatchService, contextId, { strict: false });
+      await new Promise((resolve) => process.nextTick(resolve)); // Wait for dependencies
+
+      // 4. Load email with validation
+      const email = await emailService.getEmailById(emailId, queryRunner);
+      if (!email) {
+        throw new Error(`Email not found: ${emailId}`);
+      }
+      if (email.organization?.id !== organization.id) {
+        throw new Error(`Email organization mismatch: ${email.organization?.id} !== ${organizationId}`);
+      }
+
+      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Processing email ${email.id} with status: ${email.status}`);
+
+      // 5. Load attachments (gracefully handle missing FileBatch)
+      let attachments: Array<File> = [];
+      try {
+        attachments = await fileBatchService.getFiles(email.gmailId, {
+          documents: {
+            documentType: true,
+            fields: true
+          }
+        });
+        this.logger.log(
+          `[HANDLE_REQUEST_MESSAGE] Found ${attachments.length} attachments for email ${email.id}`
+        );
+      } catch (error) {
+        this.logger.warn(
+          `[HANDLE_REQUEST_MESSAGE] Could not load attachments for email ${email.gmailId}: ${error.message}`
+        );
+      }
+
+      // 6. Load previous emails for context
+      const previousEmails = await emailService.getPreviousEmails(emailId, queryRunner);
+      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Found ${previousEmails.length} previous emails in thread`);
+
+      // 7. Generate message content for intent analysis
+      const emailContent = generateEmailContent(email, previousEmails, attachments);
+      const messageContent: MessageContent = {
+        subject: emailContent.subject,
+        text: emailContent.text,
+        messageHistory: emailContent.emailHistories,
+        attachments: emailContent.attachments?.map((att) => ({
+          filename: att.filename,
+          extractedData: att.extractedData
+        }))
+      };
+
+      // 8. Extract and validate user intents
+      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Analyzing email intents for email ${email.id}`);
+      const validatedIntents = await this.emailIntentAnalysisService.analyzeEmailIntents(messageContent);
+
+      if (!validatedIntents.intents || validatedIntents.intents.length === 0) {
+        this.logger.log(`[HANDLE_REQUEST_MESSAGE] No intents found for email ${email.id}`);
+        await queryRunner.commitTransaction();
+        return;
+      }
+
+      this.logger.log(
+        `[HANDLE_REQUEST_MESSAGE] Found ${validatedIntents.intents.length} intents: ${validatedIntents.intents.map((i) => i.intent).join(", ")}`
+      );
+
+      // 9. Combine PROCESS_DOCUMENT intents into single object
+      const processedIntents = this.combineProcessDocumentIntents(validatedIntents.intents);
+      this.logger.log(
+        `[HANDLE_REQUEST_MESSAGE] After combining PROCESS_DOCUMENT intents: ${processedIntents.length} intents`
+      );
+
+      // 10. Ensure we have at least one PROCESS_DOCUMENT intent if we have aggregated documents
+      const finalIntents = await this.ensureProcessDocumentIntent(processedIntents, email, contextId);
+      this.logger.log(
+        `[HANDLE_REQUEST_MESSAGE] After ensuring PROCESS_DOCUMENT intent: ${finalIntents.length} intents`
+      );
+
+      // TODO: 11. Sort intents by priority (skip for now)
+
+      // 12. Process each intent to generate side effects and answers
+      const answerStubs: string[] = [];
+      for (const intent of finalIntents) {
+        this.logger.log(`[HANDLE_REQUEST_MESSAGE] Processing intent: ${intent.intent}`);
+
+        try {
+          const intentResult = await this.processIntent(intent, email, organization, contextId);
+          if (intentResult.answer) {
+            answerStubs.push(intentResult.answer);
+          }
+          this.logger.log(`[HANDLE_REQUEST_MESSAGE] Successfully processed intent: ${intent.intent}`);
+        } catch (error) {
+          this.logger.error(
+            `[HANDLE_REQUEST_MESSAGE] Failed to process intent ${intent.intent}: ${error.message}`,
+            error.stack
+          );
+          answerStubs.push(
+            `I encountered an error while processing your request for ${intent.intent}. Please try again or contact support.`
+          );
+        }
+      }
+
+      // 12. Generate and send email response
+      if (answerStubs.length > 0) {
+        this.logger.log(
+          `[HANDLE_REQUEST_MESSAGE] Generating email response with ${answerStubs.length} answer stubs`
+        );
+        await this.generateAndSendResponse(email, answerStubs, emailService, contextId);
+      }
+
+      // 13. Update email status
+      await emailService.editEmail(
+        email.id,
+        {
+          status: EmailStatus.RESPONDED,
+          userIntents: validatedIntents.intents
+        },
+        queryRunner
+      );
+
+      await queryRunner.commitTransaction();
+      this.logger.log(
+        `[HANDLE_REQUEST_MESSAGE] Successfully processed email ${emailId} with ${finalIntents.length} intents`
+      );
+    } catch (error) {
+      await queryRunner.rollbackTransaction();
+      this.logger.error(
+        `[HANDLE_REQUEST_MESSAGE] Failed to process email ${emailId}: ${error.message}`,
+        error.stack
+      );
+      throw error;
+    } finally {
+      await queryRunner.release();
+    }
+  }
+
+  /**
+   * Combines multiple PROCESS_DOCUMENT intents into a single intent with merged instructions
+   */
+  private combineProcessDocumentIntents(intents: ValidatedEmailAction[]): ValidatedEmailAction[] {
+    const processDocumentIntents = intents.filter((intent) => intent.intent === "PROCESS_DOCUMENT");
+    const otherIntents = intents.filter((intent) => intent.intent !== "PROCESS_DOCUMENT");
+
+    if (processDocumentIntents.length <= 1) {
+      return intents; // No combining needed
+    }
+
+    // Combine all PROCESS_DOCUMENT intents into one
+    const combinedIntent: ValidatedEmailAction = {
+      intent: "PROCESS_DOCUMENT",
+      instructions: processDocumentIntents.flatMap((i) => i.instructions || []),
+      attachments: processDocumentIntents.flatMap((i) => i.attachments || [])
+      // Note: shippingData is optional and will be undefined if not provided
+    };
+
+    // Only add shippingData if there's actual data to combine
+    const combinedShippingData = processDocumentIntents.reduce(
+      (acc, i) => ({ ...acc, ...(i.shippingData || {}) }),
+      {} as any
+    );
+    if (Object.keys(combinedShippingData).length > 0) {
+      (combinedIntent as any).shippingData = combinedShippingData;
+    }
+
+    this.logger.log(
+      `[HANDLE_REQUEST_MESSAGE] Combined ${processDocumentIntents.length} PROCESS_DOCUMENT intents into 1`
+    );
+
+    return [...otherIntents, combinedIntent];
+  }
+
+  /**
+   * Ensure we have at least one PROCESS_DOCUMENT intent if we have aggregated documents
+   */
+  private async ensureProcessDocumentIntent(
+    intents: ValidatedEmailAction[],
+    email: Email,
+    contextId: any
+  ): Promise<ValidatedEmailAction[]> {
+    // Check if we already have a PROCESS_DOCUMENT intent
+    const hasProcessDocumentIntent = intents.some((intent) => intent.intent === "PROCESS_DOCUMENT");
+    if (hasProcessDocumentIntent) {
+      this.logger.log(
+        `[HANDLE_REQUEST_MESSAGE] Email ${email.id} already has PROCESS_DOCUMENT intent, no need to add one`
+      );
+      return intents;
+    }
+
+    // Check if we have aggregated documents
+    const hasAggregatedDocuments = await this.hasProcessedDocuments(email, contextId);
+    if (!hasAggregatedDocuments) {
+      this.logger.log(
+        `[HANDLE_REQUEST_MESSAGE] Email ${email.id} has no aggregated documents, no need to add PROCESS_DOCUMENT intent`
+      );
+      return intents;
+    }
+
+    // Add a synthetic PROCESS_DOCUMENT intent
+    this.logger.log(
+      `[HANDLE_REQUEST_MESSAGE] Email ${email.id} has aggregated documents but no PROCESS_DOCUMENT intent, adding synthetic intent`
+    );
+
+    const syntheticIntent: ValidatedEmailAction = {
+      intent: "PROCESS_DOCUMENT",
+      instructions: ["Process aggregated documents"],
+      attachments: []
+    };
+
+    return [...intents, syntheticIntent];
+  }
+
+  /**
+   * Process a single intent to generate side effects and answer stubs
+   */
+  private async processIntent(
+    intent: ValidatedEmailAction,
+    email: Email,
+    organization: Organization,
+    contextId: any
+  ): Promise<{ answer?: string }> {
+    switch (intent.intent) {
+      case "GET_SHIPMENT_STATUS":
+        return await this.processGetShipmentStatus(intent, email, organization, contextId);
+
+      case "PROCESS_DOCUMENT":
+        return await this.processDocumentIntent(intent, email, organization, contextId);
+
+      case "REQUEST_CAD_DOCUMENT":
+        return await this.processRequestCADDocument(intent, email, organization, contextId);
+
+      case "REQUEST_RNS_PROOF":
+        return await this.processRequestRNSProof(intent, email, organization, contextId);
+
+      case "DOCUMENTATION_COMING":
+        return await this.processDocumentationComing(intent, email, organization, contextId);
+
+      case "REQUEST_RUSH_PROCESSING":
+        return await this.processRequestRushProcessing(intent, email, organization, contextId);
+
+      case "REQUEST_MANUAL_PROCESSING":
+        return await this.processRequestManualProcessing(intent, email, organization, contextId);
+
+      case "REQUEST_HOLD_SHIPMENT":
+        return await this.processRequestHoldShipment(intent, email, organization, contextId);
+
+      // Document creation/update intents - these would typically trigger aggregation/submission
+      case "CREATE_SHIPMENT":
+      case "UPDATE_SHIPMENT":
+      case "CREATE_COMMERCIAL_INVOICE":
+      case "UPDATE_COMMERCIAL_INVOICE":
+      case "CREATE_CERTIFICATE_OF_ORIGIN":
+      case "UPDATE_CERTIFICATE_OF_ORIGIN":
+        return await this.processDocumentCreationIntent(intent, email, organization, contextId);
+
+      case "UNSORTED":
+        return await this.processUnsortedIntent(intent, email, organization, contextId);
+
+      // Intents that don't require processing
+      case "UNKNOWN":
+      case "SPAM":
+        this.logger.log(`[HANDLE_REQUEST_MESSAGE] Skipping processing for intent: ${intent.intent}`);
+        return { answer: undefined };
+
+      default:
+        this.logger.warn(`[HANDLE_REQUEST_MESSAGE] Unhandled intent type: ${intent.intent}`);
+        return {
+          answer: `I received your request for ${intent.intent}, but this feature is not yet implemented. Please contact support for assistance.`
+        };
+    }
+  }
+
+  /**
+   * Handle GET_SHIPMENT_STATUS intent
+   */
+  private async processGetShipmentStatus(
+    intent: ValidatedEmailAction,
+    email: Email,
+    organization: Organization,
+    contextId: any
+  ): Promise<{ answer?: string }> {
+    try {
+      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Processing GET_SHIPMENT_STATUS for email ${email.id}`);
+
+      // TODO: Implement actual shipment status query logic using CoreAgentService
+      // Missing functionality to implement:
+      // 1. Get shipment for this email using getShipmentForEmail()
+      // 2. Use CoreAgentService.answerShipmentQuestions(shipmentId, questions, queryRunner)
+      // 3. Handle case where no shipment is found (return appropriate error message)
+      // 4. Handle case where shipment is found but CoreAgentService fails
+      // 5. Return the actual status answer from CoreAgentService
+      //
+      // This should mirror the logic in generate-email-response.processor.ts lines 458-505
+      // where shipment status questions are answered using CoreAgentService.
+
+      const statusAnswer =
+        "I received your shipment status request. This feature is currently being implemented.";
+
+      return { answer: statusAnswer };
+    } catch (error) {
+      this.logger.error(
+        `[HANDLE_REQUEST_MESSAGE] Error processing GET_SHIPMENT_STATUS: ${error.message}`,
+        error.stack
+      );
+      return {
+        answer: "I encountered an error while checking your shipment status. Please try again later."
+      };
+    }
+  }
+
+  /**
+   * Handle PROCESS_DOCUMENT intent
+   */
+  private async processDocumentIntent(
+    intent: ValidatedEmailAction,
+    email: Email,
+    organization: Organization,
+    contextId: any
+  ): Promise<{ answer?: string }> {
+    try {
+      this.logger.log(
+        `[HANDLE_REQUEST_MESSAGE] Processing PROCESS_DOCUMENT for email ${email.id} with ${intent.attachments?.length || 0} attachments`
+      );
+
+      // Get shipment for document processing and potential submission
+      const shipment = await this.getShipmentForEmail(email, contextId);
+
+      // Check if we have aggregated documents to process
+      const hasAggregatedDocuments = await this.hasProcessedDocuments(email, contextId);
+
+      // Attempt Candata submission if we have a shipment and aggregated documents
+      if (shipment && hasAggregatedDocuments) {
+        this.logger.log(
+          `[HANDLE_REQUEST_MESSAGE] Found shipment ${shipment.id} with aggregated documents for email ${email.id}, attempting Candata submission`
+        );
+
+        try {
+          await this.handleDocumentSubmissionFlow(shipment, email, organization, contextId);
+        } catch (submissionError) {
+          this.logger.error(
+            `[HANDLE_REQUEST_MESSAGE] Document submission failed for shipment ${shipment.id}: ${submissionError.message}`,
+            submissionError.stack
+          );
+          // Don't fail the entire response, just log the error and continue with document acknowledgment
+        }
+      } else {
+        if (!shipment) {
+          this.logger.warn(
+            `[HANDLE_REQUEST_MESSAGE] No shipment found for email ${email.id}, skipping Candata submission`
+          );
+        }
+        if (!hasAggregatedDocuments) {
+          this.logger.warn(
+            `[HANDLE_REQUEST_MESSAGE] No aggregated documents found for email ${email.id}, skipping Candata submission`
+          );
+        }
+      }
+
+      // TODO: Implement comprehensive document status display
+      // Missing functionality to implement:
+      // 1. Load EmailUpdates for this email to get document processing results
+      // 2. Extract document statuses from EmailUpdates.documents (extracted, processed, etc.)
+      // 3. Create documentsToShow array with document names, statuses, and Claro URLs
+      // 4. Generate document status summary (e.g., "3 extracted, 2 processed, 1 failed")
+      // 5. Use EmailService.renderEmailTemplate() with EmailTemplateName.DOCUMENT_SENT_IN_RESPONSE_WITH_QUERIES_EMAIL
+      // 6. Pass template data: { mainAnswerBody, receiveDate, documents: documentsToShow }
+      // 7. Handle cases where no documents were processed (fallback to manual review)
+      //
+      // Template data structure should match:
+      // {
+      //   mainAnswerBody: string (combined response text),
+      //   receiveDate: formatted date string,
+      //   documents: Array<{ name: string, status: string, claroUrl: string }>
+      // }
+      //
+      // This should mirror the logic in generate-email-response.processor.ts lines 649-714
+      // where documentsToShow is built and the email template is rendered.
+
+      const documentAnswer = `I received your documents and will process them according to your instructions: ${intent.instructions?.join(", ") || "standard processing"}`;
+
+      return { answer: documentAnswer };
+    } catch (error) {
+      this.logger.error(
+        `[HANDLE_REQUEST_MESSAGE] Error processing PROCESS_DOCUMENT: ${error.message}`,
+        error.stack
+      );
+      return { answer: "I encountered an error while processing your documents. Please try again later." };
+    }
+  }
+
+  /**
+   * Handle REQUEST_CAD_DOCUMENT intent
+   */
+  private async processRequestCADDocument(
+    intent: ValidatedEmailAction,
+    email: Email,
+    organization: Organization,
+    contextId: any
+  ): Promise<{ answer?: string }> {
+    try {
+      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Processing REQUEST_CAD_DOCUMENT for email ${email.id}`);
+
+      // Get shipment for CAD generation
+      const shipment = await this.getShipmentForEmail(email, contextId);
+      if (!shipment) {
+        return {
+          answer:
+            "I could not generate a CAD document as I couldn't find the associated shipment from this email thread."
+        };
+      }
+
+      // Check if shipment is ready for CAD document generation
+      if (!isSendCADReady(shipment.customsStatus)) {
+        return {
+          answer:
+            "The CAD document is not yet available for this shipment. CAD documents are generated once the customs entry has been accepted."
+        };
+      }
+
+      // TODO: Implement CAD generation logic
+      // Missing functionality to implement:
+      // 1. Use the same CAD generation logic as in generate-email-response.processor.ts
+      // 2. Call respondToRequestCADDocument() method or extract its logic
+      // 3. Generate actual CAD attachment using RNSStatusChangeEmailSender.createCADAttachment()
+      // 4. Return both responseText and attachment (need to modify return type)
+      // 5. Handle attachment validation and sanitization
+      // 6. Include attachment in the final email response
+      //
+      // This should mirror the logic in generate-email-response.processor.ts lines 537-569
+      // where CAD documents are actually generated and attached to emails.
+
+      return {
+        answer:
+          "I've received your request for a CAD document. The document will be generated and sent to you shortly."
+      };
+    } catch (error) {
+      this.logger.error(
+        `[HANDLE_REQUEST_MESSAGE] Error processing REQUEST_CAD_DOCUMENT: ${error.message}`,
+        error.stack
+      );
+      return {
+        answer: "I encountered an error while generating the CAD document. Our team will review this shortly."
+      };
+    }
+  }
+
+  /**
+   * Handle REQUEST_RNS_PROOF intent
+   */
+  private async processRequestRNSProof(
+    intent: ValidatedEmailAction,
+    email: Email,
+    organization: Organization,
+    contextId: any
+  ): Promise<{ answer?: string }> {
+    try {
+      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Processing REQUEST_RNS_PROOF for email ${email.id}`);
+
+      // Get shipment for RNS proof generation
+      const shipment = await this.getShipmentForEmail(email, contextId);
+      if (!shipment) {
+        return {
+          answer:
+            "I could not generate RNS proof of release as I am unsure which shipment this request is for."
+        };
+      }
+
+      // Check if shipment is ready for RNS proof of release
+      if (!isSendRNSProofOfReleaseReady(shipment.customsStatus)) {
+        return {
+          answer:
+            "RNS proof of release is not yet available for this shipment. Proof of release is only available once the goods have been released by customs."
+        };
+      }
+
+      // TODO: Implement RNS proof generation logic
+      // Missing functionality to implement:
+      // 1. Use the same RNS proof generation logic as in generate-email-response.processor.ts
+      // 2. Call respondToRequestRNSProof() method or extract its logic
+      // 3. Use RnsProofService to get RNS proof data
+      // 4. Format RNS proof response using RNSStatusChangeEmailSender.prepareRNSStatusEmail()
+      // 5. Return formatted RNS proof text as HTML
+      //
+      // This should mirror the logic in generate-email-response.processor.ts lines 586-598
+      // and the respondToRequestRNSProof method lines 1064-1150.
+
+      return {
+        answer:
+          "I've received your request for RNS proof of release. The document will be generated and sent to you shortly."
+      };
+    } catch (error) {
+      this.logger.error(
+        `[HANDLE_REQUEST_MESSAGE] Error processing REQUEST_RNS_PROOF: ${error.message}`,
+        error.stack
+      );
+      return {
+        answer:
+          "I encountered an error while generating the RNS proof of release. Our team will review this shortly."
+      };
+    }
+  }
+
+  /**
+   * Handle DOCUMENTATION_COMING intent
+   */
+  private async processDocumentationComing(
+    intent: ValidatedEmailAction,
+    email: Email,
+    organization: Organization,
+    contextId: any
+  ): Promise<{ answer?: string }> {
+    try {
+      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Processing DOCUMENTATION_COMING for email ${email.id}`);
+
+      return {
+        answer:
+          "We've received your note about additional documents coming and will await them. Thank you for the update."
+      };
+    } catch (error) {
+      this.logger.error(
+        `[HANDLE_REQUEST_MESSAGE] Error processing DOCUMENTATION_COMING: ${error.message}`,
+        error.stack
+      );
+      return {
+        answer: "I encountered an error while processing your documentation notice. Please try again later."
+      };
+    }
+  }
+
+  /**
+   * Handle REQUEST_RUSH_PROCESSING intent
+   */
+  private async processRequestRushProcessing(
+    intent: ValidatedEmailAction,
+    email: Email,
+    organization: Organization,
+    contextId: any
+  ): Promise<{ answer?: string }> {
+    try {
+      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Processing REQUEST_RUSH_PROCESSING for email ${email.id}`);
+
+      // Send backoffice alert
+      const emailService = await this.moduleRef.resolve(EmailService, contextId);
+      const shipment = await this.getShipmentForEmail(email, contextId);
+
+      const subject = `Rush Processing Request - Email ID: ${email.id}`;
+      const body = `Rush processing requested for shipment ID: ${shipment?.id || "N/A"}.\nUser Instructions/Questions:\n${intent.instructions?.join("\n") || "No specific instructions"}\n\nPlease review.`;
+
+      try {
+        await emailService.sendBackofficeAlert(subject, body, organization.id);
+        this.logger.log(`Backoffice email sent for rush processing request for email ID: ${email.id}`);
+      } catch (alertError) {
+        this.logger.error(
+          `Failed to send backoffice email for rush processing request for email ID: ${email.id}. Error: ${alertError.message}`,
+          alertError.stack
+        );
+      }
+
+      return {
+        answer:
+          "We've received your rush processing request and our team will prioritize accordingly. Thank you."
+      };
+    } catch (error) {
+      this.logger.error(
+        `[HANDLE_REQUEST_MESSAGE] Error processing REQUEST_RUSH_PROCESSING: ${error.message}`,
+        error.stack
+      );
+      return {
+        answer:
+          "I encountered an error while processing your rush request. Our team will review this shortly."
+      };
+    }
+  }
+
+  /**
+   * Handle REQUEST_MANUAL_PROCESSING intent
+   */
+  private async processRequestManualProcessing(
+    intent: ValidatedEmailAction,
+    email: Email,
+    organization: Organization,
+    contextId: any
+  ): Promise<{ answer?: string }> {
+    try {
+      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Processing REQUEST_MANUAL_PROCESSING for email ${email.id}`);
+
+      // Send backoffice alert
+      const emailService = await this.moduleRef.resolve(EmailService, contextId);
+      const shipment = await this.getShipmentForEmail(email, contextId);
+
+      const subject = `Manual Processing Request - Email ID: ${email.id}`;
+      const body = `Manual processing requested for shipment ID: ${shipment?.id || "N/A"}.\nUser Instructions/Questions:\n${intent.instructions?.join("\n") || "No specific instructions"}\n\nPlease review.`;
+
+      try {
+        await emailService.sendBackofficeAlert(subject, body, organization.id);
+        this.logger.log(`Backoffice email sent for manual processing request for email ID: ${email.id}`);
+      } catch (alertError) {
+        this.logger.error(
+          `Failed to send backoffice email for manual processing request for email ID: ${email.id}. Error: ${alertError.message}`,
+          alertError.stack
+        );
+      }
+
+      return {
+        answer:
+          "We've received your request for manual processing and our team will review this accordingly. Thank you."
+      };
+    } catch (error) {
+      this.logger.error(
+        `[HANDLE_REQUEST_MESSAGE] Error processing REQUEST_MANUAL_PROCESSING: ${error.message}`,
+        error.stack
+      );
+      return {
+        answer:
+          "I encountered an error while processing your manual processing request. Our team will review this shortly."
+      };
+    }
+  }
+
+  /**
+   * Handle REQUEST_HOLD_SHIPMENT intent
+   */
+  private async processRequestHoldShipment(
+    intent: ValidatedEmailAction,
+    email: Email,
+    organization: Organization,
+    contextId: any
+  ): Promise<{ answer?: string }> {
+    try {
+      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Processing REQUEST_HOLD_SHIPMENT for email ${email.id}`);
+
+      // Send backoffice alert
+      const emailService = await this.moduleRef.resolve(EmailService, contextId);
+      const shipment = await this.getShipmentForEmail(email, contextId);
+
+      const subject = `Hold Shipment Request - Email ID: ${email.id}`;
+      const body = `Hold shipment requested for shipment ID: ${shipment?.id || "N/A"}.\nUser Instructions/Questions:\n${intent.instructions?.join("\n") || "No specific instructions"}\n\nPlease review.`;
+
+      try {
+        await emailService.sendBackofficeAlert(subject, body, organization.id);
+        this.logger.log(`Backoffice email sent for hold shipment request for email ID: ${email.id}`);
+      } catch (alertError) {
+        this.logger.error(
+          `Failed to send backoffice email for hold shipment request for email ID: ${email.id}. Error: ${alertError.message}`,
+          alertError.stack
+        );
+      }
+
+      return {
+        answer:
+          "We've received your request to hold the shipment and our team will process this accordingly. Thank you."
+      };
+    } catch (error) {
+      this.logger.error(
+        `[HANDLE_REQUEST_MESSAGE] Error processing REQUEST_HOLD_SHIPMENT: ${error.message}`,
+        error.stack
+      );
+      return {
+        answer:
+          "I encountered an error while processing your hold shipment request. Our team will review this shortly."
+      };
+    }
+  }
+
+  /**
+   * Handle document creation/update intents (CREATE_SHIPMENT, UPDATE_SHIPMENT, etc.)
+   */
+  private async processDocumentCreationIntent(
+    intent: ValidatedEmailAction,
+    email: Email,
+    organization: Organization,
+    contextId: any
+  ): Promise<{ answer?: string }> {
+    try {
+      this.logger.log(
+        `[HANDLE_REQUEST_MESSAGE] Processing document creation intent ${intent.intent} for email ${email.id}`
+      );
+
+      // These intents typically trigger aggregation/submission processes
+      // For now, provide acknowledgment that documents will be processed
+      const intentDisplayName = intent.intent.replace(/_/g, " ").toLowerCase();
+
+      return {
+        answer: `I've received your request for ${intentDisplayName}. Your documents will be processed according to your instructions: ${intent.instructions?.join(", ") || "standard processing"}.`
+      };
+    } catch (error) {
+      this.logger.error(
+        `[HANDLE_REQUEST_MESSAGE] Error processing document creation intent ${intent.intent}: ${error.message}`,
+        error.stack
+      );
+      return {
+        answer: `I encountered an error while processing your ${intent.intent.replace(/_/g, " ").toLowerCase()} request. Our team will review this shortly.`
+      };
+    }
+  }
+
+  /**
+   * Handle UNSORTED intent
+   */
+  private async processUnsortedIntent(
+    intent: ValidatedEmailAction,
+    email: Email,
+    organization: Organization,
+    contextId: any
+  ): Promise<{ answer?: string }> {
+    try {
+      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Processing UNSORTED intent for email ${email.id}`);
+
+      // UNSORTED intents typically contain documents that need to be processed
+      return {
+        answer: `I've received your documents and will process them according to your instructions: ${intent.instructions?.join(", ") || "standard processing"}.`
+      };
+    } catch (error) {
+      this.logger.error(
+        `[HANDLE_REQUEST_MESSAGE] Error processing UNSORTED intent: ${error.message}`,
+        error.stack
+      );
+      return {
+        answer: "I encountered an error while processing your documents. Our team will review this shortly."
+      };
+    }
+  }
+
+  /**
+   * Check if email has processed/aggregated documents
+   */
+  private async hasProcessedDocuments(email: Email, contextId: any): Promise<boolean> {
+    try {
+      // Resolve EmailUpdateService to check for processed documents
+      const emailUpdateService = await this.moduleRef.resolve("EmailUpdateService", contextId, {
+        strict: false
+      });
+
+      // Get email updates for this email
+      const emailUpdates = await emailUpdateService.getEmailUpdatesByEmailId(email.id, null, false, {
+        documents: {
+          documentType: true
+        }
+      });
+
+      // Check if we have any email updates with documents
+      const hasDocuments = emailUpdates.some((update) => update.documents && update.documents.length > 0);
+
+      this.logger.log(
+        `[HANDLE_REQUEST_MESSAGE] Email ${email.id} has ${emailUpdates.length} updates, ${hasDocuments ? "with" : "without"} documents`
+      );
+
+      return hasDocuments;
+    } catch (error) {
+      this.logger.error(
+        `[HANDLE_REQUEST_MESSAGE] Error checking for processed documents for email ${email.id}: ${error.message}`,
+        error.stack
+      );
+      return false;
+    }
+  }
+
+  /**
+   * Handle document submission flow to Candata
+   * This mirrors the handlePostDocumentProcessingWithCustomsFlow logic from generate-email-response processor
+   */
+  private async handleDocumentSubmissionFlow(
+    shipment: any,
+    email: Email,
+    organization: Organization,
+    contextId: any
+  ): Promise<void> {
+    this.logger.log(
+      `[HANDLE_REQUEST_MESSAGE] 🔄 SUBMISSION FLOW START: Handling document submission for shipment ${shipment.id}, email ${email.id} (Org: ${organization.id})`
+    );
+
+    try {
+      // Resolve required services for submission
+      const complianceValidationService = await this.moduleRef.resolve(
+        "ComplianceValidationService", // Use string to avoid import issues
+        contextId,
+        { strict: false }
+      );
+      const customStatusService = await this.moduleRef.resolve(
+        "CustomStatusService", // Use string to avoid import issues
+        contextId,
+        { strict: false }
+      );
+      await new Promise((resolve) => process.nextTick(resolve));
+
+      const isDemoShipment = complianceValidationService.isDemoShipment(shipment);
+      this.logger.log(
+        `[HANDLE_REQUEST_MESSAGE] 📊 SUBMISSION FLOW: Shipment ${shipment.id} analysis: Status=${shipment.customsStatus}, Mode=${shipment.modeOfTransport}, isDemoShipment=${isDemoShipment}`
+      );
+
+      // Guard: Check if shipment is already submitted
+      if (complianceValidationService.isShipmentSubmitted(shipment)) {
+        this.logger.warn(
+          `[HANDLE_REQUEST_MESSAGE] 🛑 SUBMISSION FLOW STOPPED: Shipment ${shipment.id} is already submitted (status: ${shipment.customsStatus}), skipping submission. ${isDemoShipment ? "[DEMO ORG]" : ""}`
+        );
+        return;
+      }
+
+      this.logger.log(
+        `[HANDLE_REQUEST_MESSAGE] ✅ SUBMISSION FLOW: Shipment ${shipment.id} is not yet submitted, proceeding with validation. ${isDemoShipment ? "[DEMO ORG - Will skip filings validation]" : ""}`
+      );
+
+      // Get shipment compliance validation
+      const shipmentCompliances = await complianceValidationService.getShipmentCompliances([shipment]);
+      const validationResult = complianceValidationService.validateShipmentCompliances(
+        shipmentCompliances,
+        isDemoShipment // Skip filings validation for demo organizations
+      );
+
+      if (validationResult.length === 0) {
+        this.logger.warn(
+          `[HANDLE_REQUEST_MESSAGE] ⚠️ SUBMISSION FLOW: No validation results for shipment ${shipment.id}`
+        );
+        return;
+      }
+
+      // Use the existing processShipmentForCustomsStatus method
+      this.logger.log(
+        `[HANDLE_REQUEST_MESSAGE] 🎯 SUBMISSION FLOW: Calling processShipmentForCustomsStatus for shipment ${shipment.id}...`
+      );
+
+      const result = await customStatusService.processShipmentForCustomsStatus(
+        shipment,
+        validationResult[0],
+        null // No query runner needed here as we're not in a transaction
+      );
+
+      // Log the results for debugging
+      if (result.liveShipmentId) {
+        this.logger.log(
+          `[HANDLE_REQUEST_MESSAGE] 🎉 SUBMISSION FLOW SUCCESS: Successfully submitted shipment ${shipment.id} to Candata! ${isDemoShipment ? "[DEMO ORG]" : ""}`
+        );
+      } else if (result.liveEntryUploadFailedShipment) {
+        this.logger.error(
+          `[HANDLE_REQUEST_MESSAGE] 💥 SUBMISSION FLOW FAILED: Failed to submit shipment ${shipment.id} to Candata: ${result.liveEntryUploadFailedShipment.failedReason}. ${isDemoShipment ? "[DEMO ORG]" : ""}`
+        );
+      } else if (result.customsStatusCheckErrorShipment) {
+        this.logger.warn(
+          `[HANDLE_REQUEST_MESSAGE] ⏰ SUBMISSION FLOW BLOCKED: Timing/validation error for shipment ${shipment.id}: ${result.customsStatusCheckErrorShipment.errorMessage}. ${isDemoShipment ? "[DEMO ORG - Consider bypassing timing restrictions]" : ""}`
+        );
+      } else if (result.shipmentStatusUpdate) {
+        this.logger.log(
+          `[HANDLE_REQUEST_MESSAGE] 📋 SUBMISSION FLOW: Updated shipment ${shipment.id} status, not ready for submission yet. New status: ${result.shipmentStatusUpdate.newStatus}. ${isDemoShipment ? "[DEMO ORG]" : ""}`
+        );
+      } else {
+        this.logger.warn(
+          `[HANDLE_REQUEST_MESSAGE] ❓ SUBMISSION FLOW UNCLEAR: Unclear result for shipment ${shipment.id}. No submission, status update, or clear error. ${isDemoShipment ? "[DEMO ORG]" : ""}`
+        );
+      }
+    } catch (error) {
+      this.logger.error(
+        `[HANDLE_REQUEST_MESSAGE] 💥 SUBMISSION FLOW EXCEPTION: Error in handleDocumentSubmissionFlow for shipment ${shipment.id}: ${error.message}`,
+        error.stack
+      );
+      throw error;
+    }
+  }
+
+  /**
+   * Helper method to get shipment for email using the same logic as generate-email-response processor
+   */
+  private async getShipmentForEmail(email: Email, contextId: any): Promise<any> {
+    try {
+      // Resolve services
+      const emailService = await this.moduleRef.resolve(EmailService, contextId);
+      const fileBatchService = await this.moduleRef.resolve(FileBatchService, contextId, { strict: false });
+      const shipmentService = await this.moduleRef.resolve(ShipmentService, contextId);
+
+      // Strategy 1: Try EmailThread first (primary method)
+      let shipment = await emailService.getEmailThreadShipment(email.threadId);
+      if (shipment) {
+        this.logger.log(
+          `[HANDLE_REQUEST_MESSAGE] Found shipment ${shipment.id} via EmailThread for email ${email.id}`
+        );
+        return shipment;
+      }
+
+      // Strategy 2: Fallback to FileBatch if EmailThread doesn't have shipment
+      this.logger.log(
+        `[HANDLE_REQUEST_MESSAGE] EmailThread shipment not found, trying FileBatch fallback for gmailId: ${email.gmailId}`
+      );
+      try {
+        const fileBatch = await fileBatchService.getFileBatch(email.gmailId);
+        if (fileBatch?.shipmentId) {
+          this.logger.log(
+            `[HANDLE_REQUEST_MESSAGE] Found shipment ID ${fileBatch.shipmentId} in FileBatch ${email.gmailId}, fetching shipment details`
+          );
+          shipment = await shipmentService.getShipmentById(fileBatch.shipmentId);
+          if (shipment) {
+            this.logger.log(
+              `[HANDLE_REQUEST_MESSAGE] Found shipment ${shipment.id} via FileBatch fallback for email ${email.id}`
+            );
+            return shipment;
+          } else {
+            this.logger.warn(
+              `[HANDLE_REQUEST_MESSAGE] FileBatch ${email.gmailId} has shipmentId ${fileBatch.shipmentId} but shipment not found in database`
+            );
+          }
+        } else {
+          this.logger.log(`[HANDLE_REQUEST_MESSAGE] FileBatch ${email.gmailId} has no shipmentId set`);
+        }
+      } catch (fileBatchError) {
+        this.logger.warn(
+          `[HANDLE_REQUEST_MESSAGE] Could not get shipment from FileBatch ${email.gmailId}: ${fileBatchError.message}`
+        );
+      }
+
+      this.logger.log(`[HANDLE_REQUEST_MESSAGE] No shipment found via any method for email ${email.id}`);
+      return null;
+    } catch (error) {
+      this.logger.error(
+        `[HANDLE_REQUEST_MESSAGE] Error getting shipment for email ${email.id}: ${error.message}`,
+        error.stack
+      );
+      return null;
+    }
+  }
+
+  /**
+   * Generate and send email response
+   */
+  private async generateAndSendResponse(
+    email: Email,
+    answerStubs: string[],
+    emailService: EmailService,
+    _contextId: any
+  ): Promise<void> {
+    try {
+      // TODO: Consider using email templates instead of simple HTML formatting
+      // The generate-email-response processor uses EmailService.renderEmailTemplate()
+      // with EmailTemplateName.DOCUMENT_SENT_IN_RESPONSE_WITH_QUERIES_EMAIL
+      // This provides consistent formatting and includes document status information.
+      //
+      // Template data structure:
+      // {
+      //   mainAnswerBody: consolidatedAnswer,
+      //   receiveDate: formatted date string,
+      //   documents: Array<{ name: string, status: string, claroUrl: string }>
+      // }
+      //
+      // Consider refactoring to use the same template system for consistency.
+
+      // Combine all answer stubs into a cohesive response
+      const responseBody = this.formatResponseBody(answerStubs);
+
+      // Generate response subject
+      const responseSubject = email.subject?.startsWith("Re:")
+        ? email.subject
+        : `Re: ${email.subject || "Your Request"}`;
+
+      // Extract sender email from the original email
+      const recipientEmail = email.from?.[0]?.address;
+      if (!recipientEmail) {
+        throw new Error("No sender email found in original email");
+      }
+
+      // TODO: Handle attachments (CAD documents, etc.) in email response
+      // The generate-email-response processor includes attachment handling
+      // for CAD documents and other generated files.
+
+      // Send the response email
+      await emailService.sendEmail({
+        from: process.env.SYSTEM_FROM_EMAIL || "<EMAIL>", // TODO: Get from config
+        to: [recipientEmail],
+        subject: responseSubject,
+        html: responseBody,
+        threadId: email.threadId,
+        inReplyToMessageId: email.messageId
+      });
+
+      this.logger.log(`[HANDLE_REQUEST_MESSAGE] Successfully sent response email to ${recipientEmail}`);
+    } catch (error) {
+      this.logger.error(
+        `[HANDLE_REQUEST_MESSAGE] Failed to send response email: ${error.message}`,
+        error.stack
+      );
+      throw error;
+    }
+  }
+
+  /**
+   * Format answer stubs into a cohesive email response body
+   */
+  private formatResponseBody(answerStubs: string[]): string {
+    const greeting = "Thank you for your email.";
+    const body = answerStubs.join("\n\n");
+    const closing =
+      "If you have any questions or need further assistance, please don't hesitate to reach out.\n\nBest regards,\nClaro Customs Team";
+
+    return `
+      <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
+        <p>${greeting}</p>
+
+        <div style="margin: 20px 0;">
+          ${body
+            .split("\n\n")
+            .map((paragraph) => `<p>${paragraph}</p>`)
+            .join("")}
+        </div>
+
+        <p>${closing}</p>
+      </div>
+    `.trim();
+  }
+}
diff --git a/apps/portal-api/src/core-agent/services/agent-tools.service.ts b/apps/portal-api/src/core-agent/services/agent-tools.service.ts
index 16bd6b21..da9cb5c1 100644
--- a/apps/portal-api/src/core-agent/services/agent-tools.service.ts
+++ b/apps/portal-api/src/core-agent/services/agent-tools.service.ts
@@ -1,19 +1,17 @@
-import { Injectable } from "@nestjs/common";
-import { AskLLMService } from "@/llm/ask-llm/services/ask-llm.service";
+import { CommercialInvoiceService } from "@/commercial-invoice/commercial-invoice.service";
+import { DocumentService } from "@/document/services/document.service";
 import { LlmToolDefinition } from "@/llm/ask-llm/interfaces/llm-tool.interface";
+import { AskLLMService } from "@/llm/ask-llm/services/ask-llm.service";
+import { ShipmentService } from "@/shipment/services/shipment.service";
+import { Injectable } from "@nestjs/common";
 import { z, ZodSchema } from "zod";
 import { LookupShipmentDBSchema } from "../schemas/lookup-shipment-db.schema";
 import { ShipmentIdentifierService } from "./shipment-identifier.service";
-import { ShipmentService } from "@/shipment/services/shipment.service";
-import { CommercialInvoiceService } from "@/commercial-invoice/commercial-invoice.service";
-import { DocumentService } from "@/document/services/document.service";
-import { CustomsStatusListener } from "@/shipment/listeners/customs-status.listener";
 @Injectable()
 export class AgentToolsService {
   constructor(
     private readonly askLLMService: AskLLMService,
     private readonly shipmentIdentifierService: ShipmentIdentifierService,
-    private readonly customsStatusListener: CustomsStatusListener,
     private readonly shipmentService: ShipmentService,
     private readonly commercialInvoiceService: CommercialInvoiceService,
     private readonly documentService: DocumentService
@@ -38,7 +36,7 @@ export class AgentToolsService {
       description: "Retrieves a detailed compliance validation report for a specific shipment.",
       schema: z.object({ shipmentId: z.number() }) as ZodSchema<{ shipmentId: number }>,
       run: async ({ shipmentId }) => {
-        return this.customsStatusListener.getShipmentComplianceDetails(shipmentId);
+        return this.shipmentService.getShipmentComplianceDetails(shipmentId);
       }
     };
 
diff --git a/apps/portal-api/src/core-agent/services/answer-user-query.service.ts b/apps/portal-api/src/core-agent/services/answer-user-query.service.ts
index a92739a7..e178a50c 100644
--- a/apps/portal-api/src/core-agent/services/answer-user-query.service.ts
+++ b/apps/portal-api/src/core-agent/services/answer-user-query.service.ts
@@ -1,19 +1,13 @@
-import { Injectable, Logger, Inject, Scope, InternalServerErrorException } from "@nestjs/common";
-import { REQUEST } from "@nestjs/core";
+import { Injectable, Logger, InternalServerErrorException } from "@nestjs/common";
 import { AskLLMService } from "@/llm/ask-llm/services/ask-llm.service";
 import { PromptTemplateName } from "@/llm/prompt-template-manager";
 import {
   ClassifyQuestionCategorySchema,
   ClassifyQuestionCategoryOutput
 } from "../schemas/classify-question-category.schema";
-import {
-  Organization,
-  Shipment,
-  ValidateShipmentComplianceResponseDto,
-  TemplateManagerService
-} from "nest-modules";
+import { Shipment, ValidateShipmentComplianceResponseDto, TemplateManagerService } from "nest-modules";
 import { ANSWER_QUESTION_TEMPLATE_NAME } from "../constants/template-map.constants";
-import { CORE_AGENT_LLM_MODEL_MINI } from "../constants/llm.constants";
+import { CORE_AGENT_LLM_MODEL_FULL } from "../constants/llm.constants";
 import { getTemplateEntry } from "../template-registry";
 import type { AnswerFragment } from "../types/answer.types";
 import { QUESTION_CATEGORIES } from "../constants/question-categories.constants";
@@ -95,7 +89,7 @@ export class AnswerUserQueryService {
         ])
       );
       const llmResponse = await this.askLLMService.ask({
-        model: CORE_AGENT_LLM_MODEL_MINI,
+        model: CORE_AGENT_LLM_MODEL_FULL,
         promptTemplate: PromptTemplateName.CLASSIFY_QUESTION,
         variables: { userQuery, categories: categoriesForPrompt },
         zodSchema: ClassifyQuestionCategorySchema,
diff --git a/apps/portal-api/src/core-agent/services/core-agent.service.ts b/apps/portal-api/src/core-agent/services/core-agent.service.ts
index bd7dca7a..dc66196b 100644
--- a/apps/portal-api/src/core-agent/services/core-agent.service.ts
+++ b/apps/portal-api/src/core-agent/services/core-agent.service.ts
@@ -17,6 +17,7 @@ import { ShipmentService } from "@/shipment/services/shipment.service";
 import { EmailIntentAnalysisService } from "./email-intent-analysis.service";
 import { AnswerUserQueryService } from "./answer-user-query.service";
 import { ShipmentIdentifierService } from "./shipment-identifier.service";
+import { EmailService } from "@/email/services/email.service";
 import { ShipmentComplianceQueryService } from "@/shipment/services/shipment-compliance-query.service";
 
 @Injectable({ scope: Scope.REQUEST })
@@ -32,6 +33,7 @@ export class CoreAgentService {
     @Inject(forwardRef(() => AnswerUserQueryService))
     private readonly answerUserQueryService: AnswerUserQueryService,
     private readonly shipmentIdentifierService: ShipmentIdentifierService,
+    @Inject(EmailService) private readonly emailService: EmailService,
     private readonly shipmentComplianceQueryService: ShipmentComplianceQueryService
   ) {}
 
diff --git a/apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts b/apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts
index 6ac5b195..431c9fc2 100644
--- a/apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts
+++ b/apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts
@@ -42,7 +42,7 @@ export class EmailIntentAnalysisService {
   private async decomposeEmailToTasks(emailContent: MessageContent): Promise<TaskList> {
     try {
       const llmResponse = await this.askLLMService.ask({
-        model: CORE_AGENT_LLM_MODEL_MINI,
+        model: CORE_AGENT_LLM_MODEL_FULL,
         promptTemplate: PromptTemplateName.DECOMPOSE_TASK,
         variables: { emailContent },
         zodSchema: TaskListSchema,
diff --git a/apps/portal-api/src/core-agent/services/email-orchestrator.service.ts b/apps/portal-api/src/core-agent/services/email-orchestrator.service.ts
new file mode 100644
index ********..c588f6de
--- /dev/null
+++ b/apps/portal-api/src/core-agent/services/email-orchestrator.service.ts
@@ -0,0 +1,213 @@
+import { Injectable, Logger } from "@nestjs/common";
+import { EventEmitter2 } from "@nestjs/event-emitter";
+import { InjectDataSource } from "@nestjs/typeorm";
+import { Email, EmailStatus } from "nest-modules";
+import { DataSource } from "typeorm";
+import { CoreAgentEmailEvent } from "../events/email-processing.events";
+
+/**
+ * EmailOrchestratorService
+ *
+ * Core-Agent service responsible for orchestrating email processing after EMAIL_SAVED handoff.
+ * This service receives control from the Email module and coordinates all business logic processing.
+ *
+ * Phase 1 Implementation:
+ * - Receives EMAIL_SAVED events from Email module
+ * - Updates email status to indicate Core-Agent processing
+ * - Implements basic pass-through logic for initial testing
+ * - Emits status update events back to Email module
+ *
+ * Future Phases:
+ * - Will contain all business logic currently in EmailSagaService
+ * - Will coordinate with other Core-Agent services
+ * - Will handle complex email processing workflows
+ */
+@Injectable()
+export class EmailOrchestratorService {
+  private readonly logger = new Logger(EmailOrchestratorService.name);
+
+  constructor(
+    @InjectDataSource()
+    private readonly dataSource: DataSource,
+    private readonly eventEmitter: EventEmitter2
+  ) {}
+
+  /**
+   * Main entry point for email processing orchestration
+   * Called by Core-Agent email listener when EMAIL_SAVED event is received
+   *
+   * @param emailId - ID of the email to process
+   * @param organizationId - Organization ID for the email
+   * @param hasAttachments - Whether the email has attachments
+   */
+  async processEmail(
+    emailId: number,
+    organizationId: number,
+    hasAttachments: boolean = false
+  ): Promise<void> {
+    this.logger.log(
+      `[ORCHESTRATOR] Starting email processing for email ${emailId}, organization ${organizationId}, hasAttachments: ${hasAttachments}`
+    );
+
+    const queryRunner = this.dataSource.createQueryRunner();
+    await queryRunner.connect();
+    await queryRunner.startTransaction();
+
+    try {
+      // Load email with organization
+      const email = await queryRunner.manager.findOne(Email, {
+        where: { id: emailId },
+        relations: ["organization"]
+      });
+
+      if (!email) {
+        this.logger.error(`[ORCHESTRATOR] Email ${emailId} not found`);
+        await queryRunner.rollbackTransaction();
+        return;
+      }
+
+      // Validate email is in correct state for Core-Agent processing
+      if (email.status !== EmailStatus.SAVED) {
+        this.logger.warn(
+          `[ORCHESTRATOR] Email ${emailId} is in state ${email.status}, expected ${EmailStatus.SAVED}. ` +
+            `Cannot process - email may have already been processed or is in invalid state. Skipping.`
+        );
+        await queryRunner.rollbackTransaction();
+        return;
+      }
+
+      this.logger.log(
+        `[ORCHESTRATOR] Email ${emailId} validated for processing - gmailId: ${email.gmailId}, status: ${email.status}`
+      );
+
+      // Emit processing started event
+      await this.emitProcessingStarted(emailId, organizationId);
+
+      // Update email status to indicate Core-Agent has taken control
+      await queryRunner.manager.update(Email, { id: emailId }, { status: EmailStatus.AWAIT_SHIPMENT_SEARCH });
+
+      this.logger.log(
+        `[ORCHESTRATOR] Updated email ${emailId} status to ${EmailStatus.AWAIT_SHIPMENT_SEARCH} - Core-Agent processing initiated`
+      );
+
+      // Phase 1: Basic pass-through logic
+      // In future phases, this will contain the full business logic pipeline
+      await this.executeBasicProcessing(email, queryRunner);
+
+      await queryRunner.commitTransaction();
+
+      // Emit processing completed event
+      await this.emitProcessingCompleted(emailId, organizationId);
+
+      this.logger.log(`[ORCHESTRATOR] Email ${emailId} processing completed successfully`);
+    } catch (error) {
+      this.logger.error(`[ORCHESTRATOR] Error processing email ${emailId}:`, error);
+
+      if (queryRunner.isTransactionActive) {
+        await queryRunner.rollbackTransaction();
+      }
+
+      // Emit processing failed event
+      await this.emitProcessingFailed(emailId, organizationId, error.message);
+
+      throw error;
+    } finally {
+      await queryRunner.release();
+    }
+  }
+
+  /**
+   * Phase 1: Basic processing logic for testing
+   * This is a simplified implementation to verify the handoff works
+   * In future phases, this will be replaced with full business logic
+   */
+  private async executeBasicProcessing(email: Email, queryRunner: any): Promise<void> {
+    this.logger.log(`[ORCHESTRATOR] Executing basic processing for email ${email.id}`);
+
+    // Phase 1: Simple status progression to verify flow works
+    // Update to a completion status to indicate processing is done
+    await queryRunner.manager.update(Email, { id: email.id }, { status: EmailStatus.RESPONDED });
+
+    this.logger.log(
+      `[ORCHESTRATOR] Basic processing completed for email ${email.id} - status updated to ${EmailStatus.RESPONDED}`
+    );
+  }
+
+  /**
+   * Update email status and emit corresponding event
+   * Provides centralized status management with event emission
+   */
+  private async updateEmailStatus(
+    emailId: number,
+    organizationId: number,
+    status: EmailStatus,
+    error?: string
+  ): Promise<void> {
+    try {
+      await this.dataSource.manager.update(Email, { id: emailId }, { status });
+
+      this.logger.log(`[ORCHESTRATOR] Updated email ${emailId} status to ${status}`);
+
+      // Emit appropriate status event based on the status
+      switch (status) {
+        case EmailStatus.AWAIT_SHIPMENT_SEARCH:
+          await this.emitProcessingStarted(emailId, organizationId);
+          break;
+        case EmailStatus.RESPONDED:
+          await this.emitProcessingCompleted(emailId, organizationId);
+          break;
+        case EmailStatus.FAILED_SAVING_EMAIL:
+        case EmailStatus.FAILED_SHIPMENT_SEARCH:
+        case EmailStatus.FAILED_ANALYZING_INTENTS:
+        case EmailStatus.FAILED_PROCESSING_INTENTS:
+        case EmailStatus.FAILED_AGGREGATING_EMAIL:
+        case EmailStatus.FAILED_RESPONDING:
+          await this.emitProcessingFailed(emailId, organizationId, error);
+          break;
+      }
+    } catch (updateError) {
+      this.logger.error(`[ORCHESTRATOR] Failed to update email ${emailId} status to ${status}:`, updateError);
+      throw updateError;
+    }
+  }
+
+  /**
+   * Emit processing started event
+   */
+  private async emitProcessingStarted(emailId: number, organizationId: number): Promise<void> {
+    this.eventEmitter.emit(CoreAgentEmailEvent.PROCESSING_STARTED, {
+      emailId,
+      organizationId,
+      status: EmailStatus.AWAIT_SHIPMENT_SEARCH
+    });
+
+    this.logger.log(`[ORCHESTRATOR] Emitted processing started event for email ${emailId}`);
+  }
+
+  /**
+   * Emit processing completed event
+   */
+  private async emitProcessingCompleted(emailId: number, organizationId: number): Promise<void> {
+    this.eventEmitter.emit(CoreAgentEmailEvent.PROCESSING_COMPLETED, {
+      emailId,
+      organizationId,
+      status: EmailStatus.RESPONDED
+    });
+
+    this.logger.log(`[ORCHESTRATOR] Emitted processing completed event for email ${emailId}`);
+  }
+
+  /**
+   * Emit processing failed event
+   */
+  private async emitProcessingFailed(emailId: number, organizationId: number, error?: string): Promise<void> {
+    this.eventEmitter.emit(CoreAgentEmailEvent.PROCESSING_FAILED, {
+      emailId,
+      organizationId,
+      status: EmailStatus.FAILED_RESPONDING,
+      error
+    });
+
+    this.logger.log(`[ORCHESTRATOR] Emitted processing failed event for email ${emailId}: ${error}`);
+  }
+}
diff --git a/apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts b/apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts
deleted file mode 100644
index b1266ea8..********
--- a/apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts
+++ /dev/null
@@ -1,301 +0,0 @@
-import { Injectable, Inject, Logger, OnModuleInit } from "@nestjs/common";
-import { EMAIL_INTENTS } from "@/email/types/ai-agent.types";
-import { IntentHandler, IntentClassificationMeta } from "../interfaces/intent-handler.interface";
-
-/**
- * Registry service that auto-discovers and manages all intent handlers.
- * Provides mapping between EMAIL_INTENTS and their corresponding handlers.
- */
-@Injectable()
-export class IntentHandlerRegistry implements OnModuleInit {
-  private readonly logger = new Logger(IntentHandlerRegistry.name);
-  private readonly handlerMap = new Map<(typeof EMAIL_INTENTS)[number], IntentHandler>();
-  private registrationComplete = false;
-  private registrationTime?: Date;
-
-  constructor(
-    // NestJS will automatically inject ALL implementations of IntentHandler
-    @Inject("INTENT_HANDLERS") private readonly allHandlers: IntentHandler[]
-  ) {
-    this.logger.log(
-      `IntentHandlerRegistry constructor called with ${this.allHandlers?.length || 0} handlers`
-    );
-  }
-
-  async onModuleInit(): Promise<void> {
-    this.logger.log("IntentHandlerRegistry.onModuleInit() called - starting handler registration");
-    this.registerHandlers();
-    this.logger.log("IntentHandlerRegistry.onModuleInit() completed");
-  }
-
-  /**
-   * Register all injected intent handlers and validate the registry.
-   */
-  private registerHandlers(): void {
-    this.logger.log(`Registering ${this.allHandlers.length} intent handlers...`);
-    this.logger.debug(`Handler types: ${this.allHandlers.map((h) => h.constructor.name).join(", ")}`);
-
-    // Clear existing registrations
-    this.handlerMap.clear();
-
-    // Register each handler
-    for (const handler of this.allHandlers) {
-      try {
-        this.logger.debug(`Processing handler: ${handler.constructor.name}`);
-
-        // Check if handler has classification metadata
-        if (!handler.classificationMeta) {
-          this.logger.error(`Handler ${handler.constructor.name} missing classificationMeta`);
-          continue;
-        }
-
-        const { intent } = handler.classificationMeta;
-        this.logger.debug(`Handler ${handler.constructor.name} declares intent: ${intent}`);
-
-        // Check for duplicate registrations
-        if (this.handlerMap.has(intent)) {
-          const existingHandler = this.handlerMap.get(intent);
-          this.logger.warn(
-            `Duplicate handler registration for intent '${intent}': ` +
-              `${existingHandler?.constructor.name} will be replaced by ${handler.constructor.name}`
-          );
-        }
-
-        // Register the handler
-        this.handlerMap.set(intent, handler);
-
-        this.logger.log(
-          `✓ Registered handler: ${intent} -> ${handler.constructor.name} ` +
-            `(${handler.classificationMeta.examples.length} examples)`
-        );
-      } catch (error) {
-        this.logger.error(
-          `Failed to register handler ${handler.constructor.name}: ${error.message}`,
-          error.stack
-        );
-      }
-    }
-
-    this.registrationComplete = true;
-    this.registrationTime = new Date();
-
-    this.logger.log(
-      `Successfully registered ${this.handlerMap.size} intent handlers. ` +
-        `Missing: ${this.getMissingHandlers().join(", ") || "none"}`
-    );
-  }
-
-  /**
-   * Get the handler for a specific intent type.
-   */
-  getHandler(intentType: (typeof EMAIL_INTENTS)[number]): IntentHandler | undefined {
-    // Ensure handlers are registered if not already done
-    if (!this.registrationComplete && this.allHandlers.length > 0) {
-      this.logger.warn("Handler map empty but handlers available - forcing registration");
-      this.registerHandlers();
-    }
-    return this.handlerMap.get(intentType);
-  }
-
-  /**
-   * Check if a handler exists for the given intent type.
-   */
-  hasHandler(intentType: (typeof EMAIL_INTENTS)[number]): boolean {
-    return this.handlerMap.has(intentType);
-  }
-
-  /**
-   * Get all supported intent types (those that have registered handlers).
-   */
-  getAllSupportedIntents(): (typeof EMAIL_INTENTS)[number][] {
-    // Ensure handlers are registered if not already done
-    if (!this.registrationComplete && this.allHandlers.length > 0) {
-      this.logger.warn("Handler map empty but handlers available - forcing registration");
-      this.registerHandlers();
-    }
-    return Array.from(this.handlerMap.keys());
-  }
-
-  /**
-   * Get all registered handlers.
-   */
-  getAllHandlers(): IntentHandler[] {
-    return Array.from(this.handlerMap.values());
-  }
-
-  /**
-   * Get classification metadata for all registered handlers.
-   * This can be used to dynamically generate LLM classification prompts.
-   */
-  getAllClassificationMeta(): IntentClassificationMeta[] {
-    return this.getAllHandlers().map((handler) => handler.classificationMeta);
-  }
-
-  /**
-   * Get classification metadata for a specific intent type.
-   */
-  getClassificationMeta(intentType: (typeof EMAIL_INTENTS)[number]): IntentClassificationMeta | undefined {
-    const handler = this.getHandler(intentType);
-    return handler?.classificationMeta;
-  }
-
-  /**
-   * Generate a dynamic classification prompt using all registered handlers.
-   * This replaces the static classify-intent-only.njk template.
-   */
-  generateClassificationPrompt(): string {
-    const metadata = this.getAllClassificationMeta();
-
-    const intentDescriptions = metadata
-      .map((meta) => {
-        const examples = meta.examples.map((ex) => `"${ex}"`).join(" | ");
-        const keywords = meta.keywords ? ` Keywords: ${meta.keywords.join(", ")}` : "";
-
-        return `\`${meta.intent.toUpperCase()}\` - ${meta.description}\n  Examples: ${examples}${keywords}`;
-      })
-      .join("\n\n");
-
-    return `
-# system:
-You are an expert task classifier for shipping and logistics operations. Your role is to analyze individual task descriptions and classify the task into the most appropriate intent category.
-
-### Valid Intent Types
-
-${intentDescriptions}
-
-### Classification Rules
-- Analyze the task text carefully to determine the **primary intention**
-- Consider **key verbs**, context clues, and **specific terminology**
-- If torn between categories, **prefer the more specific one**
-- If the task contains multiple intents, pick the **primary action**
-- In this customs and logistics context:
-  - "CAD" always refers to "CAD Document" (Customs Accounting Document), not Canadian dollars or computer-aided design
-  - "RNS" always refers to "Release Notification System"
-- Be confident in your classification. If the intent is highly ambiguous or unclear after careful consideration, and you genuinely cannot determine the intent with reasonable confidence, use \`UNKNOWN\`
-- Use \`SPAM\` **only** if the message is clearly spam or phishing, not just unrelated business communication.
-- Ensure your output strictly adheres to the JSON structure provided.
-
-### Output Example (JSON Structure)
-\`\`\`json
-{
-  "intent": "CLASSIFIED_INTENT_TYPE"
-}
-\`\`\`
-
-# user:
-Classify the following task into the most appropriate intent type:
-
-{{ task }}
-    `.trim();
-  }
-
-  /**
-   * Get system diagnostics for monitoring and debugging.
-   */
-  getSystemDiagnostics(): {
-    registeredHandlers: Array<{
-      intentType: string;
-      handlerClass: string;
-      exampleCount: number;
-      hasKeywords: boolean;
-    }>;
-    totalHandlers: number;
-    registrationComplete: boolean;
-    registrationTime?: Date;
-    missingHandlers: string[];
-  } {
-    // Ensure handlers are registered if not already done
-    if (!this.registrationComplete && this.allHandlers.length > 0) {
-      this.logger.warn(
-        "Handler map empty but handlers available - forcing registration in getSystemDiagnostics"
-      );
-      this.registerHandlers();
-    }
-
-    return {
-      registeredHandlers: Array.from(this.handlerMap.entries()).map(([intentType, handler]) => ({
-        intentType,
-        handlerClass: handler.constructor.name,
-        exampleCount: handler.classificationMeta.examples.length,
-        hasKeywords: Boolean(handler.classificationMeta.keywords?.length)
-      })),
-      totalHandlers: this.handlerMap.size,
-      registrationComplete: this.registrationComplete,
-      registrationTime: this.registrationTime,
-      missingHandlers: this.getMissingHandlers()
-    };
-  }
-
-  /**
-   * Validate that all handlers are properly configured.
-   * Returns true if all handlers are valid, false otherwise.
-   */
-  validateAllHandlers(): { isValid: boolean; errors: string[] } {
-    const errors: string[] = [];
-
-    // Check if we have handlers for critical intents
-    const criticalIntents: (typeof EMAIL_INTENTS)[number][] = [
-      "GET_SHIPMENT_STATUS",
-      "REQUEST_CAD_DOCUMENT",
-      "REQUEST_RNS_PROOF",
-      "REQUEST_RUSH_PROCESSING",
-      "PROCESS_DOCUMENT"
-    ];
-
-    for (const intent of criticalIntents) {
-      if (!this.hasHandler(intent)) {
-        errors.push(`Missing handler for critical intent: ${intent}`);
-      }
-    }
-
-    // Validate each registered handler
-    for (const [intent, handler] of this.handlerMap.entries()) {
-      try {
-        const meta = handler.classificationMeta;
-
-        if (!meta.description || meta.description.trim().length === 0) {
-          errors.push(`Handler ${handler.constructor.name} has empty description`);
-        }
-
-        if (!meta.examples || meta.examples.length === 0) {
-          errors.push(`Handler ${handler.constructor.name} has no examples`);
-        }
-
-        if (meta.intent !== intent) {
-          errors.push(
-            `Handler ${handler.constructor.name} intent mismatch: ` +
-              `registered as ${intent} but declares ${meta.intent}`
-          );
-        }
-      } catch (error) {
-        errors.push(`Handler ${handler.constructor.name} validation failed: ${error.message}`);
-      }
-    }
-
-    const isValid = errors.length === 0;
-
-    if (!isValid) {
-      this.logger.warn(`Handler validation failed with ${errors.length} errors:`, errors);
-    }
-
-    return { isValid, errors };
-  }
-
-  /**
-   * Force re-registration of all handlers (useful for testing).
-   */
-  forceReregistration(): void {
-    this.logger.debug("Forcing re-registration of all intent handlers");
-    this.registerHandlers();
-  }
-
-  /**
-   * Get list of intent types that don't have handlers.
-   */
-  private getMissingHandlers(): string[] {
-    const allIntentTypes = [...EMAIL_INTENTS];
-    const registeredIntents = this.getAllSupportedIntents();
-
-    return allIntentTypes.filter((intent) => !registeredIntents.includes(intent));
-  }
-}
diff --git a/apps/portal-api/src/core-agent/services/shipment-context.service.ts b/apps/portal-api/src/core-agent/services/shipment-context.service.ts
deleted file mode 100644
index f6dbfbe5..********
--- a/apps/portal-api/src/core-agent/services/shipment-context.service.ts
+++ /dev/null
@@ -1,1383 +0,0 @@
-import {
-  Injectable,
-  Logger,
-  NotFoundException,
-  BadRequestException,
-  InternalServerErrorException
-} from "@nestjs/common";
-import { ContextIdFactory, ModuleRef } from "@nestjs/core";
-import { InjectDataSource } from "@nestjs/typeorm";
-import { DataSource, QueryRunner } from "typeorm";
-import {
-  Organization,
-  FIND_ORGANIZATION_RELATIONS,
-  Shipment,
-  ValidateShipmentComplianceResponseDto,
-  TemplateManagerService,
-  Document,
-  DocumentStatus,
-  NonCompliantReason
-} from "nest-modules";
-import { ShipmentContext } from "../types/shipment-context.types";
-import { generateRequest } from "@/email/utils/generate-request";
-import { CandataService } from "nest-modules";
-import { ShipmentService } from "@/shipment/services/shipment.service";
-import { ShipmentComplianceQueryService } from "@/shipment/services/shipment-compliance-query.service";
-import { ComplianceValidationService } from "@/shipment/services/compliance-validation.service";
-import { CustomsStatusListener } from "@/shipment/listeners/customs-status.listener";
-import { EntrySubmissionService } from "@/shipment/services/entry-submission.service";
-import { ImporterService } from "@/importer/importer.service";
-import { RNSStatusChangeEmailSender } from "@/shipment/senders/rns-status-change-email.sender";
-import { RnsProofService } from "@/email/services/rns-proof-service";
-import { EmailService } from "@/email/services/email.service";
-
-// Import business rule utilities and constants
-import {
-  isSubmittedCustomsStatus,
-  isReleasedCustomsStatus,
-  isSendCADReady,
-  isSendRNSProofOfReleaseReady,
-  isReadyToSubmit
-} from "../constants/customs-definitions.constants";
-import { CUSTOMS_STATUS_MESSAGES } from "../constants/customs-status-messages.constants";
-import { formatComplianceResponseToStrings } from "../adapters/compliance-response.adapter";
-
-/**
- * Interface for REQUEST-scoped services resolved via ModuleRef.
- * This ensures type safety for all resolved services.
- */
-interface ResolvedRequestScopedServices {
-  shipmentService: ShipmentService;
-  shipmentComplianceQueryService: ShipmentComplianceQueryService;
-  complianceValidationService: ComplianceValidationService;
-  customsStatusListener: CustomsStatusListener;
-  entrySubmissionService: EntrySubmissionService;
-  importerService: ImporterService;
-  rnsStatusChangeEmailSender: RNSStatusChangeEmailSender;
-  rnsProofService: RnsProofService;
-  emailService: EmailService;
-}
-
-/**
- * Enhanced document data status interface for template generation
- */
-interface DocumentDataStatus {
-  // Required for submission (compliance-based)
-  hasHBLDataForSubmission: boolean;
-  hasAnEmfDataForSubmission: boolean;
-
-  // Complete business assessment
-  hasCompleteHBLData: boolean;
-  hasCompleteAnEmfData: boolean;
-
-  // Commercial documents
-  ciReceived: boolean;
-  plReceived: boolean;
-
-  // Template display format
-  hblStatus: "Received" | "Missing";
-  anEmfStatus: "Received" | "Missing";
-  ciPlStatus: "Received" | "Missing";
-}
-
-/**
- * Enhanced missing fields analysis interface
- */
-interface MissingFieldsAnalysis {
-  missingIdentifiers: string[];
-  missingMeasurements: string[];
-  missingTiming: string[];
-  missingLocations: string[];
-  ogdFilingStatus: "pending" | "complete" | "not-required";
-  formattedMissingFields: string[]; // For template display like "CCN **missing**"
-}
-
-/**
- * Template-specific context interface
- */
-interface TemplateContext {
-  // Core identifiers for all templates
-  identifiers: {
-    ccn: string;
-    hbl: string;
-    containers: string[]; // Multiple containers
-    formattedContainers: string; // "TEMU8711844, ABCD1234567" or "TEMU8711844"
-  };
-
-  // Document status for templates
-  documentStatus: {
-    hblStatus: "Received" | "Missing";
-    anEmfStatus: "Received" | "Missing";
-    ciPlStatus: "Received" | "Missing";
-  };
-
-  // Missing fields for different template types
-  missingFields: {
-    forPendingCommercialInvoice: string[]; // Focus on CI/PL
-    forPendingConfirmation: string[]; // Focus on compliance issues
-    formatted: string[]; // "Weight **missing**", "OGD filing **Pending**"
-  };
-
-  // Timing information
-  timing: {
-    etaPort: string | null;
-    etaDestination: string | null;
-    releaseDate: string | null;
-    formattedEtaPort: string; // "January 15, 2024" or "TBD"
-    formattedEtaDestination: string;
-    formattedReleaseDate: string;
-  };
-
-  // Status-specific context
-  statusContext: {
-    primaryMessage: string; // Status-specific primary message
-    secondaryDetails: string[]; // Additional context based on status
-    actionRequired: boolean; // Whether action is needed from client
-  };
-}
-
-/**
- * Centralized service for building comprehensive shipment context containing all business rule evaluations.
- * This service operates in singleton scope and resolves REQUEST-scoped dependencies via ModuleRef.
- *
- * Key responsibilities:
- * - Single source of truth for all shipment business rule evaluation
- * - Fetches and consolidates all shipment-related data
- * - Provides rich context for response generation layers
- * - Handles REQUEST-scoped service resolution for background jobs
- */
-@Injectable() // Singleton scope
-export class ShipmentContextService {
-  private readonly logger = new Logger(ShipmentContextService.name);
-
-  constructor(
-    private readonly moduleRef: ModuleRef,
-    @InjectDataSource() private readonly dataSource: DataSource,
-    private readonly templateManagerService: TemplateManagerService, // Singleton - inject normally
-    private readonly candataService: CandataService // Singleton - inject normally
-  ) {}
-
-  /**
-   * Builds comprehensive context for a shipment containing all business rule evaluations,
-   * formatted display data, and service instances needed for response generation.
-   *
-   * @param shipmentId - The ID of the shipment to build context for
-   * @param organizationId - The organization ID for scoping REQUEST-scoped services
-   * @param queryRunner - Optional queryRunner for transaction consistency
-   * @returns Promise resolving to comprehensive ShipmentContext
-   * @throws {NotFoundException} If shipment or organization not found
-   * @throws {BadRequestException} If invalid parameters provided
-   * @throws {InternalServerErrorException} If critical services fail to resolve
-   */
-  async buildContext(
-    shipmentId: number,
-    organizationId: number,
-    queryRunner?: QueryRunner
-  ): Promise<ShipmentContext> {
-    this.logger.log(`Building context for shipment ${shipmentId}, organization ${organizationId}`);
-
-    try {
-      // Validate input parameters
-      if (!shipmentId || shipmentId <= 0) {
-        throw new BadRequestException(`Invalid shipmentId: ${shipmentId}`);
-      }
-      if (!organizationId || organizationId <= 0) {
-        throw new BadRequestException(`Invalid organizationId: ${organizationId}`);
-      }
-
-      // Critical organization lookup - fail fast
-      const organization = await this.fetchOrganization(organizationId, queryRunner);
-
-      // Resolve REQUEST-scoped services with proper error handling
-      const services = await this.resolveRequestScopedServices(organization);
-
-      // Fetch base data - ensure QueryRunner consistency for transactions
-      const { shipment, compliance } = await this.fetchShipmentData(
-        shipmentId,
-        services.shipmentService,
-        services.shipmentComplianceQueryService,
-        queryRunner
-      );
-
-      // Build comprehensive context with safe business rule evaluation
-      return {
-        // Raw data
-        shipment,
-        compliance,
-        organization,
-
-        // ALL business rule evaluations done ONCE with safe evaluation
-        canRush: this.safeEvaluate(
-          () => this.canShipmentBeRushed(shipment, compliance, services.complianceValidationService),
-          false,
-          "canRush"
-        ),
-        canGenerateCAD: this.safeEvaluate(
-          () => isSendCADReady(shipment.customsStatus),
-          false,
-          "canGenerateCAD"
-        ),
-        canGenerateRNSProof: this.safeEvaluate(
-          () => isSendRNSProofOfReleaseReady(shipment.customsStatus),
-          false,
-          "canGenerateRNSProof"
-        ),
-        isCompliant: this.safeEvaluate(() => isReadyToSubmit(compliance), false, "isCompliant"),
-        isReleased: this.safeEvaluate(
-          () => isReleasedCustomsStatus(shipment.customsStatus),
-          false,
-          "isReleased"
-        ),
-        isSubmitted: this.safeEvaluate(
-          () => services.complianceValidationService.isShipmentSubmitted(shipment),
-          false,
-          "isSubmitted"
-        ),
-        canBeModified: this.safeEvaluate(
-          () => !services.complianceValidationService.isShipmentSubmitted(shipment),
-          true,
-          "canBeModified"
-        ),
-        isEntryUploaded: this.safeEvaluate(
-          () => services.complianceValidationService.isShipmentEntryUploaded(shipment),
-          false,
-          "isEntryUploaded"
-        ),
-        canUpdateEntry: this.safeEvaluate(
-          () => services.complianceValidationService.canShipmentUpdateEntry(shipment),
-          false,
-          "canUpdateEntry"
-        ),
-        isAllDocsReceived: this.safeEvaluate(
-          () => this.isAllDocumentsReceived(shipment, compliance),
-          false,
-          "isAllDocsReceived"
-        ),
-
-        // Detailed breakdowns for templates with safe evaluation
-        missingDocuments: this.safeEvaluate(() => compliance.missingFields || [], [], "missingDocuments"),
-        complianceErrors: this.safeEvaluate(
-          () => formatComplianceResponseToStrings(compliance) || [],
-          [],
-          "complianceErrors"
-        ),
-        nonCompliantInvoices: this.safeEvaluate(
-          () => compliance.nonCompliantInvoices || [],
-          [],
-          "nonCompliantInvoices"
-        ),
-        rushBlockingReason: this.safeEvaluate(
-          () => this.getRushBlockingReason(shipment, compliance, services.complianceValidationService),
-          "Unable to determine blocking reason",
-          "rushBlockingReason"
-        ),
-        cadBlockingReason: this.safeEvaluate(
-          () => this.getCADBlockingReason(shipment),
-          "",
-          "cadBlockingReason"
-        ),
-        rnsBlockingReason: this.safeEvaluate(
-          () => this.getRNSBlockingReason(shipment),
-          "Proof of release is only available once the goods have been released by customs.",
-          "rnsBlockingReason"
-        ),
-
-        // Formatted display values (from existing context builders) with safe evaluation
-        formattedCustomsStatus: this.safeEvaluate(
-          () => this.getFormattedCustomsStatus(shipment.customsStatus),
-          "Status Unknown",
-          "formattedCustomsStatus"
-        ),
-        shipmentIdentifiers: this.safeEvaluate(
-          () => this.buildShipmentIdentifiers(shipment),
-          this.getEmptyShipmentIdentifiers(),
-          "shipmentIdentifiers"
-        ),
-        etaInformation: this.safeEvaluate(
-          () => this.buildEtaInformation(shipment),
-          this.getEmptyEtaInformation(),
-          "etaInformation"
-        ),
-        shippingInformation: this.safeEvaluate(
-          () => this.buildShippingInformation(shipment),
-          this.getEmptyShippingInformation(),
-          "shippingInformation"
-        ),
-
-        // Enhanced document and missing fields analysis
-        documentDataStatus: this.safeEvaluate(
-          () => this.buildDocumentDataStatus(shipment, compliance),
-          this.getEmptyDocumentDataStatus(),
-          "documentDataStatus"
-        ),
-        missingFieldsAnalysis: this.safeEvaluate(
-          () => this.buildMissingFieldsAnalysis(compliance),
-          this.getEmptyMissingFieldsAnalysis(),
-          "missingFieldsAnalysis"
-        ),
-        templateContext: this.safeEvaluate(
-          () => this.buildTemplateContext(shipment, compliance),
-          this.getEmptyTemplateContext(),
-          "templateContext"
-        ),
-
-        // Backward compatibility - maintain existing document receipt status
-        documentReceiptStatus: this.safeEvaluate(
-          () => this.buildDocumentReceiptStatus(shipment, compliance),
-          this.getEmptyDocumentReceiptStatus(),
-          "documentReceiptStatus"
-        ),
-        missingFieldsStatus: this.safeEvaluate(
-          () => this.buildMissingFieldsStatus(compliance),
-          [],
-          "missingFieldsStatus"
-        ),
-
-        // User interaction flags (set by intent handlers)
-        directlyAsked: {},
-
-        // Side effect results (populated by intent handlers)
-        sideEffects: {
-          cadDocument: null,
-          rnsProofData: null,
-          backofficeAlerts: {}
-        },
-
-        // Service instances for side effects (intent handlers need these)
-        _services: {
-          emailService: services.emailService,
-          rnsStatusChangeEmailSender: services.rnsStatusChangeEmailSender,
-          rnsProofService: services.rnsProofService,
-          customsStatusListener: services.customsStatusListener,
-          entrySubmissionService: services.entrySubmissionService,
-          importerService: services.importerService
-        }
-      };
-    } catch (error) {
-      this.logger.error(`Failed to build context for shipment ${shipmentId}: ${error.message}`, error.stack);
-
-      // Re-throw known errors with proper classification
-      if (error instanceof NotFoundException || error instanceof BadRequestException) {
-        throw error;
-      }
-
-      // Wrap unexpected errors
-      throw new InternalServerErrorException(
-        `Unable to retrieve shipment information for shipment ${shipmentId}: ${error.message}`
-      );
-    }
-  }
-
-  /**
-   * Fetches organization with proper error handling and fail-fast behavior.
-   */
-  private async fetchOrganization(organizationId: number, queryRunner?: QueryRunner): Promise<Organization> {
-    this.logger.debug(`Fetching organization ${organizationId}`);
-
-    const organization = await (queryRunner?.manager ?? this.dataSource.manager).findOne(Organization, {
-      where: { id: organizationId },
-      relations: FIND_ORGANIZATION_RELATIONS
-    });
-
-    if (!organization) {
-      throw new NotFoundException(`Organization ${organizationId} not found`);
-    }
-
-    this.logger.debug(`Organization ${organizationId} fetched successfully`);
-    return organization;
-  }
-
-  /**
-   * Resolves all REQUEST-scoped services via ModuleRef with proper error handling.
-   * This method encapsulates the complex service resolution pattern.
-   */
-  private async resolveRequestScopedServices(
-    organization: Organization
-  ): Promise<ResolvedRequestScopedServices> {
-    this.logger.debug(`Resolving REQUEST-scoped services for organization ${organization.id}`);
-
-    try {
-      // Create context for REQUEST-scoped services
-      const contextId = ContextIdFactory.create();
-
-      // Register mock request with organization context
-      this.moduleRef.registerRequestByContextId(generateRequest(null, organization), contextId);
-
-      // Resolve all REQUEST-scoped services in parallel for performance
-      // Using class references instead of string literals to avoid circular dependency issues
-      const [
-        shipmentService,
-        shipmentComplianceQueryService,
-        complianceValidationService,
-        customsStatusListener,
-        entrySubmissionService,
-        importerService,
-        rnsStatusChangeEmailSender,
-        rnsProofService,
-        emailService
-      ] = await Promise.all([
-        this.moduleRef.resolve(ShipmentService, contextId, { strict: false }),
-        this.moduleRef.resolve(ShipmentComplianceQueryService, contextId, { strict: false }),
-        this.moduleRef.resolve(ComplianceValidationService, contextId, { strict: false }),
-        this.moduleRef.resolve(CustomsStatusListener, contextId, { strict: false }),
-        this.moduleRef.resolve(EntrySubmissionService, contextId, { strict: false }),
-        this.moduleRef.resolve(ImporterService, contextId, { strict: false }),
-        this.moduleRef.resolve(RNSStatusChangeEmailSender, contextId, { strict: false }),
-        this.moduleRef.resolve(RnsProofService, contextId, { strict: false }),
-        this.moduleRef.resolve(EmailService, contextId, { strict: false })
-      ]);
-
-      // Wait for dependency resolution
-      await new Promise((resolve) => process.nextTick(resolve));
-
-      // Validate that all services were resolved successfully
-      const services = {
-        shipmentService,
-        shipmentComplianceQueryService,
-        complianceValidationService,
-        customsStatusListener,
-        entrySubmissionService,
-        importerService,
-        rnsStatusChangeEmailSender,
-        rnsProofService,
-        emailService
-      };
-
-      this.validateResolvedServices(services);
-
-      this.logger.debug(
-        `Successfully resolved all REQUEST-scoped services for organization ${organization.id}`
-      );
-      return services;
-    } catch (error) {
-      this.logger.error(`Failed to resolve REQUEST-scoped services: ${error.message}`, error.stack);
-      throw new InternalServerErrorException(
-        `Service resolution failed for organization ${organization.id}: ${error.message}`
-      );
-    }
-  }
-
-  /**
-   * Validates that all required services were successfully resolved.
-   */
-  private validateResolvedServices(services: ResolvedRequestScopedServices): void {
-    const serviceEntries = [
-      ["shipmentService", services.shipmentService],
-      ["shipmentComplianceQueryService", services.shipmentComplianceQueryService],
-      ["complianceValidationService", services.complianceValidationService],
-      ["customsStatusListener", services.customsStatusListener],
-      ["entrySubmissionService", services.entrySubmissionService],
-      ["importerService", services.importerService],
-      ["rnsStatusChangeEmailSender", services.rnsStatusChangeEmailSender],
-      ["rnsProofService", services.rnsProofService],
-      ["emailService", services.emailService]
-    ];
-
-    const missingServices = serviceEntries.filter(([name, service]) => !service).map(([name]) => name);
-
-    if (missingServices.length > 0) {
-      throw new InternalServerErrorException(
-        `Failed to resolve required services: ${missingServices.join(", ")}`
-      );
-    }
-  }
-
-  /**
-   * Fetches shipment and compliance data with proper error handling.
-   */
-  private async fetchShipmentData(
-    shipmentId: number,
-    shipmentService: ShipmentService,
-    shipmentComplianceQueryService: ShipmentComplianceQueryService,
-    queryRunner?: QueryRunner
-  ): Promise<{ shipment: Shipment; compliance: ValidateShipmentComplianceResponseDto }> {
-    this.logger.debug(`Fetching shipment data for shipment ${shipmentId}`);
-
-    try {
-      // Fetch shipment - critical data, fail fast
-      const shipment = await shipmentService.getShipmentById(shipmentId, queryRunner);
-      if (!shipment) {
-        throw new NotFoundException(`Shipment ${shipmentId} not found`);
-      }
-
-      // Fetch compliance data - critical data, fail fast
-      const compliance = await shipmentComplianceQueryService.getShipmentComplianceDetails(
-        shipmentId,
-        queryRunner
-      );
-
-      this.logger.debug(`Successfully fetched shipment data for shipment ${shipmentId}`);
-      return { shipment, compliance };
-    } catch (error) {
-      this.logger.error(
-        `Failed to fetch shipment data for shipment ${shipmentId}: ${error.message}`,
-        error.stack
-      );
-
-      // Re-throw NotFoundException as-is
-      if (error instanceof NotFoundException) {
-        throw error;
-      }
-
-      // Wrap other errors
-      throw new InternalServerErrorException(
-        `Failed to fetch shipment data for shipment ${shipmentId}: ${error.message}`
-      );
-    }
-  }
-
-  // ===== BUSINESS RULE METHODS (migrated from existing logic) =====
-
-  /**
-   * Determines if a shipment can be rushed based on submission and compliance status.
-   * Migrated from email processor logic.
-   */
-  private canShipmentBeRushed(
-    shipment: Shipment,
-    compliance: ValidateShipmentComplianceResponseDto,
-    complianceValidationService: ComplianceValidationService
-  ): boolean {
-    return !complianceValidationService.isShipmentSubmitted(shipment) && isReadyToSubmit(compliance);
-  }
-
-  /**
-   * Gets the reason why a shipment cannot be rushed.
-   * Provides user-friendly blocking reasons for rush processing.
-   */
-  private getRushBlockingReason(
-    shipment: Shipment,
-    compliance: ValidateShipmentComplianceResponseDto,
-    complianceValidationService: ComplianceValidationService
-  ): string {
-    if (complianceValidationService.isShipmentSubmitted(shipment)) {
-      return "Shipment already submitted to customs";
-    }
-    if (compliance.noCommercialInvoice) {
-      return "Missing commercial invoice";
-    }
-    if (compliance.missingFields?.length > 0) {
-      return "Missing required shipment fields";
-    }
-    if (compliance.nonCompliantInvoices?.length > 0) {
-      return "Commercial invoice compliance issues must be resolved";
-    }
-    return `Cannot rush shipments with status: ${shipment.customsStatus}`;
-  }
-
-  /**
-   * Gets the reason why CAD document cannot be generated.
-   */
-  private getCADBlockingReason(shipment: Shipment): string {
-    if (!isSendCADReady(shipment.customsStatus)) {
-      return "CAD document not available until customs entry is accepted";
-    }
-    return "";
-  }
-
-  /**
-   * Gets the reason why RNS proof cannot be generated.
-   */
-  private getRNSBlockingReason(shipment: Shipment): string {
-    if (!isSendRNSProofOfReleaseReady(shipment.customsStatus)) {
-      return "Proof of release is only available once the goods have been released by customs.";
-    }
-    return "";
-  }
-
-  /**
-   * Determines if all required documents have been received (i.e., all data they would provide is present).
-   * This is a comprehensive check across all document types and their required data fields.
-   */
-  private isAllDocumentsReceived(
-    shipment: Shipment,
-    compliance: ValidateShipmentComplianceResponseDto
-  ): boolean {
-    const documentDataStatus = this.buildDocumentDataStatus(shipment, compliance);
-
-    // All core documents must have data for submission
-    const allCoreDocsReceived =
-      documentDataStatus.hasHBLDataForSubmission &&
-      documentDataStatus.hasAnEmfDataForSubmission &&
-      documentDataStatus.ciReceived;
-
-    // No document-related missing fields (this covers additional fields beyond core docs)
-    const noDocumentFieldsMissing = !this.hasDocumentRelatedMissingFields(compliance.missingFields || []);
-
-    // Additional business rule: No non-compliant invoices that would require document updates
-    const noDocumentComplianceIssues = !this.hasDocumentComplianceIssues(compliance);
-
-    return allCoreDocsReceived && noDocumentFieldsMissing && noDocumentComplianceIssues;
-  }
-
-  /**
-   * Checks if there are missing fields that are typically provided by documents.
-   * These fields are essential for shipment processing and customs clearance.
-   */
-  private hasDocumentRelatedMissingFields(missingFields: string[]): boolean {
-    const documentRelatedFields = [
-      // HBL/AWB fields
-      "hblNumber",
-      "weight",
-      "weightUOM",
-      "quantity",
-      "quantityUOM",
-      "portCode",
-      "portOfLoadingId",
-
-      // AN/EMF fields
-      "cargoControlNumber",
-      "etd",
-      "etaPort",
-      "etaDestination",
-      "subLocation",
-
-      // Commercial Invoice fields (data that would come from CI)
-      "transactionNumber", // Often comes from CI processing
-
-      // Container/packaging information
-      "volume",
-      "volumeUOM",
-
-      // Timing information typically from shipping docs
-      "vessel",
-      "voyageNumber" // For ocean shipments
-    ];
-
-    return missingFields.some((field) => documentRelatedFields.includes(field));
-  }
-
-  /**
-   * Checks if there are compliance issues related to document content.
-   * This includes OGD filing issues, tariff classification problems, etc.
-   */
-  private hasDocumentComplianceIssues(compliance: ValidateShipmentComplianceResponseDto): boolean {
-    // Check for non-compliant invoices that would require document corrections
-    const hasInvoiceIssues = compliance.nonCompliantInvoices?.length > 0;
-
-    // Check for OGD filing issues (these typically require document updates)
-    const hasOGDIssues = this.getOGDFilingStatus(compliance) === "pending";
-
-    return hasInvoiceIssues || hasOGDIssues;
-  }
-
-  // ===== ENHANCED BUSINESS RULE METHODS =====
-
-  /**
-   * Builds enhanced document data status using business rules rather than just document processing status.
-   * Uses the ValidateShipmentComplianceResponseDto.missingFields array to infer what data we have.
-   */
-  private buildDocumentDataStatus(
-    shipment: Shipment,
-    compliance: ValidateShipmentComplianceResponseDto
-  ): DocumentDataStatus {
-    const missingFields = compliance.missingFields || [];
-
-    // Assess HBL data
-    const hasHBLDataForSubmission = this.hasHBLDataForSubmission(missingFields, shipment.modeOfTransport);
-    const hasCompleteHBLData = this.hasCompleteHBLData(shipment, missingFields);
-
-    // Assess AN/EMF data
-    const hasAnEmfDataForSubmission = this.hasAnEmfDataForSubmission(missingFields, shipment.modeOfTransport);
-    const hasCompleteAnEmfData = this.hasCompleteAnEmfData(shipment, missingFields);
-
-    // Commercial documents
-    const ciReceived = !compliance.noCommercialInvoice;
-    const plReceived = this.hasPackingListData(shipment);
-
-    return {
-      hasHBLDataForSubmission,
-      hasAnEmfDataForSubmission,
-      hasCompleteHBLData,
-      hasCompleteAnEmfData,
-      ciReceived,
-      plReceived,
-      hblStatus: this.formatDocumentStatus(hasCompleteHBLData),
-      anEmfStatus: this.formatDocumentStatus(hasCompleteAnEmfData),
-      ciPlStatus: this.formatCIPlStatus(compliance.noCommercialInvoice, plReceived)
-    };
-  }
-
-  /**
-   * Builds enhanced missing fields analysis with categorization and user-friendly formatting.
-   */
-  private buildMissingFieldsAnalysis(
-    compliance: ValidateShipmentComplianceResponseDto
-  ): MissingFieldsAnalysis {
-    const missingFields = compliance.missingFields || [];
-
-    // Categorize missing fields
-    const missingIdentifiers = missingFields.filter((field) =>
-      ["cargoControlNumber", "hblNumber", "transactionNumber"].includes(field)
-    );
-
-    const missingMeasurements = missingFields.filter((field) =>
-      ["weight", "weightUOM", "quantity", "quantityUOM"].includes(field)
-    );
-
-    const missingTiming = missingFields.filter((field) =>
-      ["etd", "etaPort", "etaDestination"].includes(field)
-    );
-
-    const missingLocations = missingFields.filter((field) =>
-      ["portCode", "subLocation", "portOfLoadingId", "placeOfDelivery"].includes(field)
-    );
-
-    // Assess OGD filing status
-    const ogdFilingStatus = this.getOGDFilingStatus(compliance);
-
-    // Format missing fields for template display
-    const formattedMissingFields = this.formatMissingFieldsForTemplate(missingFields, ogdFilingStatus);
-
-    return {
-      missingIdentifiers,
-      missingMeasurements,
-      missingTiming,
-      missingLocations,
-      ogdFilingStatus,
-      formattedMissingFields
-    };
-  }
-
-  /**
-   * Builds template-specific context for different customs statuses.
-   */
-  private buildTemplateContext(
-    shipment: Shipment,
-    compliance: ValidateShipmentComplianceResponseDto
-  ): TemplateContext {
-    const documentDataStatus = this.buildDocumentDataStatus(shipment, compliance);
-    const missingFieldsAnalysis = this.buildMissingFieldsAnalysis(compliance);
-
-    // Build enhanced identifiers
-    const containers = Array.isArray(shipment.containers)
-      ? shipment.containers.map((c) => c.containerNumber).filter(Boolean)
-      : [];
-
-    const identifiers = {
-      ccn: shipment.cargoControlNumber || "",
-      hbl: shipment.hblNumber || "",
-      containers,
-      formattedContainers: containers.length > 0 ? containers.join(", ") : ""
-    };
-
-    // Build timing information
-    const timing = {
-      etaPort: shipment.etaPort?.toISOString() || null,
-      etaDestination: shipment.etaDestination?.toISOString() || null,
-      releaseDate: shipment.releaseDate?.toISOString() || null,
-      formattedEtaPort: this.formatDateForTemplate(shipment.etaPort),
-      formattedEtaDestination: this.formatDateForTemplate(shipment.etaDestination),
-      formattedReleaseDate: this.formatDateForTemplate(shipment.releaseDate)
-    };
-
-    // Build missing fields for different template contexts
-    const missingFields = {
-      forPendingCommercialInvoice: this.getMissingFieldsForCIContext(missingFieldsAnalysis, compliance),
-      forPendingConfirmation: this.getMissingFieldsForComplianceContext(missingFieldsAnalysis, compliance),
-      formatted: missingFieldsAnalysis.formattedMissingFields
-    };
-
-    // Build status-specific context
-    const statusContext = this.buildStatusContext(shipment, compliance, documentDataStatus);
-
-    return {
-      identifiers,
-      documentStatus: {
-        hblStatus: documentDataStatus.hblStatus,
-        anEmfStatus: documentDataStatus.anEmfStatus,
-        ciPlStatus: documentDataStatus.ciPlStatus
-      },
-      missingFields,
-      timing,
-      statusContext
-    };
-  }
-
-  /**
-   * Checks if required HBL fields are present based on missing fields array.
-   */
-  private hasHBLDataForSubmission(missingFields: string[], modeOfTransport: string): boolean {
-    const requiredHBLFields = this.getRequiredHBLFields(modeOfTransport);
-    return !requiredHBLFields.some((field) => missingFields.includes(field));
-  }
-
-  /**
-   * Checks if required AN/EMF fields are present based on missing fields array.
-   */
-  private hasAnEmfDataForSubmission(missingFields: string[], modeOfTransport: string): boolean {
-    const requiredAnEmfFields = this.getRequiredAnEmfFields(modeOfTransport);
-    return !requiredAnEmfFields.some((field) => missingFields.includes(field));
-  }
-
-  /**
-   * Checks both required and optional HBL fields for completeness.
-   */
-  private hasCompleteHBLData(shipment: Shipment, missingFields: string[]): boolean {
-    const allHBLFields = [
-      ...this.getRequiredHBLFields(shipment.modeOfTransport),
-      "hblNumber",
-      ...(shipment.modeOfTransport?.toLowerCase().includes("ocean") ? ["vesselName", "voyageNumber"] : [])
-    ];
-    return !allHBLFields.some((field) => missingFields.includes(field));
-  }
-
-  /**
-   * Checks both required and optional AN/EMF fields for completeness.
-   */
-  private hasCompleteAnEmfData(shipment: Shipment, missingFields: string[]): boolean {
-    const allAnEmfFields = this.getRequiredAnEmfFields(shipment.modeOfTransport);
-    return !allAnEmfFields.some((field) => missingFields.includes(field));
-  }
-
-  /**
-   * Gets required HBL fields based on mode of transport.
-   */
-  private getRequiredHBLFields(modeOfTransport: string): string[] {
-    const baseFields = ["weight", "weightUOM"];
-
-    switch (modeOfTransport?.toUpperCase()) {
-      case "OCEAN_FCL":
-      case "OCEAN_LCL":
-        return [...baseFields, "portCode", "portOfLoadingId"];
-      case "AIR":
-        return [...baseFields, "portCode", "portOfLoadingId"];
-      case "LAND":
-        return [...baseFields];
-      default:
-        return [...baseFields, "portCode"];
-    }
-  }
-
-  /**
-   * Gets required AN/EMF fields based on mode of transport.
-   */
-  private getRequiredAnEmfFields(modeOfTransport: string): string[] {
-    const baseFields = ["cargoControlNumber", "etd", "etaDestination"];
-
-    switch (modeOfTransport?.toUpperCase()) {
-      case "OCEAN_FCL":
-      case "OCEAN_LCL":
-        return [...baseFields, "subLocation"];
-      case "AIR":
-        return [...baseFields, "subLocation"];
-      case "LAND":
-        return [...baseFields];
-      default:
-        return baseFields;
-    }
-  }
-
-  /**
-   * Checks if packing list data is available.
-   * Note: PLs are in skippedDocumentTypes, so we use shipment fields.
-   */
-  private hasPackingListData(shipment: Shipment): boolean {
-    // Packing list data is often inferred from quantity/packaging information
-    return !!(shipment.quantity && shipment.quantityUOM);
-  }
-
-  /**
-   * Formats document status for template display.
-   */
-  private formatDocumentStatus(hasData: boolean): "Received" | "Missing" {
-    return hasData ? "Received" : "Missing";
-  }
-
-  /**
-   * Formats CI & PL status combination for template display.
-   */
-  private formatCIPlStatus(noCommercialInvoice: boolean, hasPackingList: boolean): "Received" | "Missing" {
-    return !noCommercialInvoice && hasPackingList ? "Received" : "Missing";
-  }
-
-  /**
-   * Gets OGD filing status from compliance data.
-   */
-  private getOGDFilingStatus(
-    compliance: ValidateShipmentComplianceResponseDto
-  ): "pending" | "complete" | "not-required" {
-    const hasOGDFilingIssue = compliance.nonCompliantInvoices?.some((invoice) =>
-      invoice.nonCompliantLines?.some((line) =>
-        line.nonCompliantRecords?.some((record) => record.reason === NonCompliantReason.MISSING_OGD_FILING)
-      )
-    );
-
-    if (hasOGDFilingIssue) {
-      return "pending";
-    }
-
-    // Check if OGD filing is required but not needed (no controlled goods)
-    const hasControlledGoods = compliance.nonCompliantInvoices?.some((invoice) =>
-      invoice.nonCompliantLines?.some((line) =>
-        line.nonCompliantRecords?.some((record) => record.reason && record.reason.toString().includes("OGD"))
-      )
-    );
-
-    return hasControlledGoods ? "complete" : "not-required";
-  }
-
-  /**
-   * Formats missing fields for template display with business-friendly names.
-   */
-  private formatMissingFieldsForTemplate(
-    missingFields: string[],
-    ogdFilingStatus: "pending" | "complete" | "not-required"
-  ): string[] {
-    const formatted: string[] = [];
-
-    // Map technical field names to user-friendly template format
-    const fieldMapping: Record<string, string> = {
-      cargoControlNumber: "CCN **missing**",
-      hblNumber: "HBL **missing**",
-      weight: "Weight **missing**",
-      weightUOM: "Weight unit **missing**",
-      quantity: "Quantity **missing**",
-      quantityUOM: "Quantity unit **missing**",
-      etd: "ETD **missing**",
-      etaPort: "ETA Port **missing**",
-      etaDestination: "ETA Destination **missing**",
-      portCode: "Port code **missing**",
-      subLocation: "Sub-location **missing**",
-      portOfLoadingId: "Port of loading **missing**",
-      transactionNumber: "Transaction number **missing**"
-    };
-
-    // Add formatted missing fields
-    missingFields.forEach((field) => {
-      if (fieldMapping[field]) {
-        formatted.push(fieldMapping[field]);
-      }
-    });
-
-    // Add OGD filing status if pending
-    if (ogdFilingStatus === "pending") {
-      formatted.push("OGD filing **Pending**");
-    }
-
-    return formatted;
-  }
-
-  /**
-   * Gets missing fields relevant for commercial invoice template context.
-   */
-  private getMissingFieldsForCIContext(
-    missingFieldsAnalysis: MissingFieldsAnalysis,
-    compliance: ValidateShipmentComplianceResponseDto
-  ): string[] {
-    const ciRelatedFields: string[] = [];
-
-    if (compliance.noCommercialInvoice) {
-      ciRelatedFields.push("Commercial invoice required");
-    }
-
-    if (missingFieldsAnalysis.ogdFilingStatus === "pending") {
-      ciRelatedFields.push("OGD filing pending");
-    }
-
-    // Add measurement-related missing fields that affect CI processing
-    ciRelatedFields.push(
-      ...missingFieldsAnalysis.missingMeasurements.map((field) => this.formatMissingFieldForTemplate(field))
-    );
-
-    return ciRelatedFields;
-  }
-
-  /**
-   * Gets missing fields relevant for compliance confirmation template context.
-   */
-  private getMissingFieldsForComplianceContext(
-    missingFieldsAnalysis: MissingFieldsAnalysis,
-    compliance: ValidateShipmentComplianceResponseDto
-  ): string[] {
-    const complianceFields: string[] = [];
-
-    // Add all missing identifiers
-    complianceFields.push(
-      ...missingFieldsAnalysis.missingIdentifiers.map((field) => this.formatMissingFieldForTemplate(field))
-    );
-
-    // Add missing measurements
-    complianceFields.push(
-      ...missingFieldsAnalysis.missingMeasurements.map((field) => this.formatMissingFieldForTemplate(field))
-    );
-
-    // Add missing timing
-    complianceFields.push(
-      ...missingFieldsAnalysis.missingTiming.map((field) => this.formatMissingFieldForTemplate(field))
-    );
-
-    // Add missing locations
-    complianceFields.push(
-      ...missingFieldsAnalysis.missingLocations.map((field) => this.formatMissingFieldForTemplate(field))
-    );
-
-    return complianceFields;
-  }
-
-  /**
-   * Formats a date for template display.
-   */
-  private formatDateForTemplate(date: Date | null | undefined): string {
-    if (!date) return "TBD";
-
-    try {
-      return date.toLocaleDateString("en-US", {
-        year: "numeric",
-        month: "long",
-        day: "numeric"
-      });
-    } catch {
-      return "TBD";
-    }
-  }
-
-  /**
-   * Converts technical field names to user-friendly template format.
-   */
-  private formatMissingFieldForTemplate(fieldName: string): string {
-    const fieldMapping: Record<string, string> = {
-      cargoControlNumber: "CCN missing",
-      hblNumber: "HBL missing",
-      weight: "Weight missing",
-      weightUOM: "Weight unit missing",
-      quantity: "Quantity missing",
-      quantityUOM: "Quantity unit missing",
-      etd: "ETD missing",
-      etaPort: "ETA Port missing",
-      etaDestination: "ETA Destination missing",
-      portCode: "Port code missing",
-      subLocation: "Sub-location missing",
-      portOfLoadingId: "Port of loading missing",
-      transactionNumber: "Transaction number missing"
-    };
-
-    return fieldMapping[fieldName] || `${fieldName} missing`;
-  }
-
-  /**
-   * Builds status-specific context for templates.
-   */
-  private buildStatusContext(
-    shipment: Shipment,
-    compliance: ValidateShipmentComplianceResponseDto,
-    documentDataStatus: DocumentDataStatus
-  ): { primaryMessage: string; secondaryDetails: string[]; actionRequired: boolean } {
-    const status = shipment.customsStatus?.toLowerCase() || "unknown";
-    const secondaryDetails: string[] = [];
-    let primaryMessage = "";
-    let actionRequired = false;
-
-    // Build status-specific context based on customs status
-    if (status.includes("pending")) {
-      primaryMessage = "Shipment awaiting processing";
-      actionRequired = true;
-
-      if (!documentDataStatus.ciReceived) {
-        secondaryDetails.push("Commercial invoice required");
-      }
-      if (compliance.missingFields?.length > 0) {
-        secondaryDetails.push("Additional shipment details needed");
-      }
-    } else if (status.includes("accepted") || status.includes("released")) {
-      primaryMessage = "Shipment cleared for release";
-      actionRequired = false;
-
-      if (shipment.releaseDate) {
-        secondaryDetails.push(`Released on ${this.formatDateForTemplate(shipment.releaseDate)}`);
-      }
-    } else if (status.includes("exam")) {
-      primaryMessage = "Shipment selected for examination";
-      actionRequired = false;
-      secondaryDetails.push("Please await further instructions");
-    } else {
-      primaryMessage = `Status: ${this.getFormattedCustomsStatus(shipment.customsStatus)}`;
-      actionRequired = compliance.missingFields?.length > 0 || compliance.noCommercialInvoice;
-    }
-
-    return {
-      primaryMessage,
-      secondaryDetails,
-      actionRequired
-    };
-  }
-
-  // ===== FORMATTED DISPLAY VALUE METHODS (migrated from context builders) =====
-
-  /**
-   * Formats customs status for display.
-   * Migrated from customs-status.context.ts
-   */
-  private getFormattedCustomsStatus(customsStatus: string): string {
-    if (customsStatus && CUSTOMS_STATUS_MESSAGES[customsStatus]) {
-      return CUSTOMS_STATUS_MESSAGES[customsStatus];
-    }
-    return customsStatus ? customsStatus.toLowerCase().replace(/-/g, " ") : "Status Unknown";
-  }
-
-  /**
-   * Builds shipment identifiers object.
-   * Migrated from shipment-identifiers.context.ts
-   */
-  private buildShipmentIdentifiers(shipment: Shipment) {
-    const containers = Array.isArray(shipment.containers)
-      ? shipment.containers.map((c) => c.containerNumber).filter(Boolean)
-      : [];
-
-    return {
-      hblNumber: shipment.hblNumber || null,
-      cargoControlNumber: shipment.cargoControlNumber || null,
-      transactionNumber: shipment.transactionNumber || null,
-      containerNumbers: containers,
-      // Enhanced: Add formatted containers for template display
-      formattedContainers: containers.length > 0 ? containers.join(", ") : "",
-      // Enhanced: Add multiple container handling
-      hasMultipleContainers: containers.length > 1,
-      primaryContainer: containers.length > 0 ? containers[0] : null
-    };
-  }
-
-  /**
-   * Builds ETA information object.
-   * Migrated from eta.context.ts
-   */
-  private buildEtaInformation(shipment: Shipment) {
-    return {
-      etaPortValue: shipment.etaPort?.toLocaleDateString(),
-      portName: shipment.portOfDischarge?.name,
-      etaDestinationValue: shipment.etaDestination?.toLocaleDateString(),
-      destinationName: shipment.placeOfDelivery?.name
-    };
-  }
-
-  /**
-   * Builds shipping information object.
-   * Migrated from shipping-status.context.ts
-   */
-  private buildShippingInformation(shipment: Shipment) {
-    const isTrackerOnline =
-      shipment.trackingStatus && ["online", "tracking"].includes(shipment.trackingStatus);
-    const isAir =
-      shipment.modeOfTransport && ["AIR", "air", "Air"].includes(shipment.modeOfTransport as string);
-
-    return {
-      isTrackerOnline,
-      shipmentStatus: shipment.status,
-      isAir,
-      trackingStatus: shipment.trackingStatus
-    };
-  }
-
-  /**
-   * Safely evaluates a business rule with fallback and logging.
-   * This prevents context build failures from individual rule evaluation errors.
-   */
-  private safeEvaluate<T>(evaluation: () => T, fallback: T, ruleName: string): T {
-    try {
-      return evaluation();
-    } catch (error) {
-      this.logger.warn(`Business rule evaluation failed for ${ruleName}, using fallback: ${error.message}`);
-      return fallback;
-    }
-  }
-
-  // ===== DOCUMENT RECEIPT STATUS METHODS =====
-
-  /**
-   * Builds document receipt status using existing shipment.documents and compliance data.
-   * Uses existing business logic instead of manual repository queries.
-   */
-  private buildDocumentReceiptStatus(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto) {
-    const documents = shipment.documents || [];
-
-    return {
-      hblReceived: this.hasDocumentReceived(documents, this.getHBLDocumentTypes(shipment.modeOfTransport)),
-      anEmfReceived: this.hasDocumentReceived(
-        documents,
-        this.getANEMFDocumentTypes(shipment.modeOfTransport)
-      ),
-      ciReceived: !compliance.noCommercialInvoice, // Use existing compliance check
-      plReceived: this.hasDocumentReceived(documents, ["PACKING_LIST"])
-    };
-  }
-
-  /**
-   * Builds missing fields status using existing compliance validation.
-   * Leverages formatComplianceResponseToStrings and existing missing field descriptions.
-   */
-  private buildMissingFieldsStatus(compliance: ValidateShipmentComplianceResponseDto): string[] {
-    const missing: string[] = [];
-
-    // Use existing missing fields validation
-    if (compliance.missingFields?.includes("cargoControlNumber" as any)) {
-      missing.push("CCN **missing**");
-    }
-    if (compliance.missingFields?.includes("weight" as any)) {
-      missing.push("Weight **missing**");
-    }
-    if (compliance.missingFields?.includes("hblNumber" as any)) {
-      missing.push("HBL **missing**");
-    }
-
-    // Check for OGD filing requirements using existing compliance logic
-    if (this.getOGDFilingStatus(compliance) === "pending") {
-      missing.push("OGD filing **Pending**");
-    }
-
-    return missing;
-  }
-
-  /**
-   * Determines HBL document types based on mode of transport.
-   */
-  private getHBLDocumentTypes(modeOfTransport: string): string[] {
-    switch (modeOfTransport?.toUpperCase()) {
-      case "OCEAN_FCL":
-      case "OCEAN_LCL":
-        return ["HOUSE_OCEAN_BILL_OF_LADING"];
-      case "AIR":
-        return ["AIR_WAYBILL"];
-      case "LAND":
-        return ["ROAD_BILL_OF_LADING"];
-      default:
-        return ["HOUSE_OCEAN_BILL_OF_LADING", "AIR_WAYBILL", "ROAD_BILL_OF_LADING"];
-    }
-  }
-
-  /**
-   * Determines AN/EMF document types based on mode of transport.
-   */
-  private getANEMFDocumentTypes(modeOfTransport: string): string[] {
-    switch (modeOfTransport?.toUpperCase()) {
-      case "OCEAN_FCL":
-      case "OCEAN_LCL":
-        return ["OCEAN_ARRIVAL_NOTICE", "OCEAN_E_MANIFEST"];
-      case "AIR":
-        return ["AIR_ARRIVAL_NOTICE", "AIR_E_MANIFEST"];
-      case "LAND":
-        return ["ROAD_ARRIVAL_NOTICE"];
-      default:
-        return [
-          "OCEAN_ARRIVAL_NOTICE",
-          "OCEAN_E_MANIFEST",
-          "AIR_ARRIVAL_NOTICE",
-          "AIR_E_MANIFEST",
-          "ROAD_ARRIVAL_NOTICE"
-        ];
-    }
-  }
-
-  /**
-   * Checks if document type has been received using existing document associations.
-   * Uses shipment.documents relation that's already loaded.
-   */
-  private hasDocumentReceived(documents: Document[], documentTypes: string[]): boolean {
-    return documents.some(
-      (doc) =>
-        documentTypes.includes(doc.name) &&
-        [DocumentStatus.EXTRACTED, DocumentStatus.AGGREGATED].includes(doc.status as DocumentStatus) &&
-        !doc.isShipmentMismatch // Exclude mismatched documents
-    );
-  }
-
-  // ===== FALLBACK METHODS =====
-
-  private getEmptyShipmentIdentifiers() {
-    return {
-      hblNumber: null,
-      cargoControlNumber: null,
-      transactionNumber: null,
-      containerNumbers: [],
-      formattedContainers: "",
-      hasMultipleContainers: false,
-      primaryContainer: null
-    };
-  }
-
-  private getEmptyShippingInformation() {
-    return {
-      isTrackerOnline: false,
-      shipmentStatus: "Unknown",
-      isAir: false,
-      trackingStatus: undefined
-    };
-  }
-
-  private getEmptyEtaInformation() {
-    return {
-      etaPortValue: undefined,
-      portName: undefined,
-      etaDestinationValue: undefined,
-      destinationName: undefined
-    };
-  }
-
-  private getEmptyDocumentReceiptStatus() {
-    return {
-      hblReceived: false,
-      anEmfReceived: false,
-      ciReceived: false,
-      plReceived: false
-    };
-  }
-
-  // ===== ENHANCED FEATURE FALLBACK METHODS =====
-
-  private getEmptyDocumentDataStatus(): DocumentDataStatus {
-    return {
-      hasHBLDataForSubmission: false,
-      hasAnEmfDataForSubmission: false,
-      hasCompleteHBLData: false,
-      hasCompleteAnEmfData: false,
-      ciReceived: false,
-      plReceived: false,
-      hblStatus: "Missing",
-      anEmfStatus: "Missing",
-      ciPlStatus: "Missing"
-    };
-  }
-
-  private getEmptyMissingFieldsAnalysis(): MissingFieldsAnalysis {
-    return {
-      missingIdentifiers: [],
-      missingMeasurements: [],
-      missingTiming: [],
-      missingLocations: [],
-      ogdFilingStatus: "not-required",
-      formattedMissingFields: []
-    };
-  }
-
-  private getEmptyTemplateContext(): TemplateContext {
-    return {
-      identifiers: {
-        ccn: "",
-        hbl: "",
-        containers: [],
-        formattedContainers: ""
-      },
-      documentStatus: {
-        hblStatus: "Missing",
-        anEmfStatus: "Missing",
-        ciPlStatus: "Missing"
-      },
-      missingFields: {
-        forPendingCommercialInvoice: [],
-        forPendingConfirmation: [],
-        formatted: []
-      },
-      timing: {
-        etaPort: null,
-        etaDestination: null,
-        releaseDate: null,
-        formattedEtaPort: "TBD",
-        formattedEtaDestination: "TBD",
-        formattedReleaseDate: "TBD"
-      },
-      statusContext: {
-        primaryMessage: "Status unknown",
-        secondaryDetails: [],
-        actionRequired: false
-      }
-    };
-  }
-}
diff --git a/apps/portal-api/src/core-agent/services/shipment-response.service.ts b/apps/portal-api/src/core-agent/services/shipment-response.service.ts
deleted file mode 100644
index e71ac71c..********
--- a/apps/portal-api/src/core-agent/services/shipment-response.service.ts
+++ /dev/null
@@ -1,240 +0,0 @@
-import { Injectable, Logger } from "@nestjs/common";
-import { TemplateManagerService } from "nest-modules";
-import { ResponseFragment } from "../types/response-fragment.types";
-import { ShipmentContext } from "../types/shipment-context.types";
-import { TEMPLATE_ORDER } from "../constants/templates";
-
-/**
- * Layer 3: Response Service
- *
- * Provides pure rendering of response fragments using templates.
- * Handles deduplication, sorting, and consolidation of fragments
- * into final shipment responses.
- */
-@Injectable()
-export class ShipmentResponseService {
-  private readonly logger = new Logger(ShipmentResponseService.name);
-
-  constructor(private readonly templateManagerService: TemplateManagerService) {}
-
-  /**
-   * Render fragments into a consolidated response.
-   *
-   * @param fragments - Array of response fragments to render
-   * @param context - Full shipment context for template rendering
-   * @returns Consolidated HTML response
-   */
-  async renderFragments(fragments: ResponseFragment[], context: ShipmentContext): Promise<string> {
-    const startTime = Date.now();
-
-    this.logger.log(`Rendering ${fragments.length} fragments for shipment ${context.shipment.id}`);
-    this.logger.debug(`Fragment types: ${fragments.map((f) => f.template).join(", ")}`);
-
-    try {
-      // 1. Deduplicate by template name
-      const uniqueFragments = this.deduplicateFragments(fragments);
-      this.logger.debug(`After deduplication: ${uniqueFragments.length} fragments`);
-
-      // 2. Sort by priority and predefined order
-      const sortedFragments = this.sortFragments(uniqueFragments);
-
-      // 3. Render each template with merged context
-      const renderedParts: string[] = [];
-
-      for (const fragment of sortedFragments) {
-        try {
-          const fragmentContext = this.mergeContexts(context, fragment.fragmentContext);
-          const rendered = await this.renderSingleFragment(fragment, fragmentContext);
-
-          if (rendered.trim()) {
-            renderedParts.push(rendered);
-          }
-        } catch (error) {
-          this.logger.error(
-            `Failed to render fragment '${fragment.template}': ${error.message}`,
-            error.stack
-          );
-
-          // Add error fragment instead of failing entire response
-          renderedParts.push(this.renderErrorFragment(fragment.template, error.message));
-        }
-      }
-
-      const consolidatedResponse = renderedParts.join("\n\n");
-      const duration = Date.now() - startTime;
-
-      this.logger.log(`Rendered ${renderedParts.length} fragments in ${duration}ms`);
-      return consolidatedResponse;
-    } catch (error) {
-      this.logger.error(
-        `Critical error in fragment rendering for shipment ${context.shipment.id}: ${error.message}`,
-        error.stack
-      );
-
-      return this.renderFallbackResponse(error.message);
-    }
-  }
-
-  /**
-   * Remove duplicate fragments by template name.
-   * Later fragments with the same template name override earlier ones.
-   */
-  private deduplicateFragments(fragments: ResponseFragment[]): ResponseFragment[] {
-    const templateMap = new Map<string, ResponseFragment>();
-
-    // Later fragments override earlier ones
-    for (const fragment of fragments) {
-      templateMap.set(fragment.template, fragment);
-    }
-
-    return Array.from(templateMap.values());
-  }
-
-  /**
-   * Sort fragments by priority and predefined template order.
-   */
-  private sortFragments(fragments: ResponseFragment[]): ResponseFragment[] {
-    return fragments.sort((a, b) => {
-      // First sort by explicit priority if provided
-      if (a.priority !== undefined || b.priority !== undefined) {
-        const aPriority = a.priority ?? 999;
-        const bPriority = b.priority ?? 999;
-
-        if (aPriority !== bPriority) {
-          return aPriority - bPriority;
-        }
-      }
-
-      // Then sort by predefined template order
-      const indexA = TEMPLATE_ORDER.indexOf(a.template as any);
-      const indexB = TEMPLATE_ORDER.indexOf(b.template as any);
-
-      // Templates not in the order list go to the end
-      const orderA = indexA === -1 ? 999 + TEMPLATE_ORDER.length : indexA;
-      const orderB = indexB === -1 ? 999 + TEMPLATE_ORDER.length : indexB;
-
-      return orderA - orderB;
-    });
-  }
-
-  /**
-   * Merge fragment-specific context with main shipment context.
-   */
-  private mergeContexts(
-    mainContext: ShipmentContext,
-    fragmentContext?: Record<string, any>
-  ): ShipmentContext {
-    if (!fragmentContext) {
-      return mainContext;
-    }
-
-    // Merge fragment context while preserving main context structure
-    return {
-      ...mainContext,
-      ...fragmentContext,
-      // Ensure core properties aren't overridden
-      shipment: mainContext.shipment,
-      compliance: mainContext.compliance,
-      organization: mainContext.organization
-    };
-  }
-
-  /**
-   * Render a single fragment using the template manager.
-   */
-  private async renderSingleFragment(fragment: ResponseFragment, context: ShipmentContext): Promise<string> {
-    try {
-      // Remove sensitive service instances from template context
-      const sanitizedContext = this.sanitizeContextForTemplate(context);
-
-      return this.templateManagerService.renderTemplate(fragment.template, sanitizedContext);
-    } catch (error) {
-      throw new Error(`Template rendering failed for '${fragment.template}': ${error.message}`);
-    }
-  }
-
-  /**
-   * Remove sensitive data and service instances from context before template rendering.
-   */
-  private sanitizeContextForTemplate(context: ShipmentContext): any {
-    const { _services, ...sanitizedContext } = context;
-
-    return {
-      ...sanitizedContext,
-      // Escape any HTML in user-provided content
-      shipment: {
-        ...sanitizedContext.shipment,
-        hblNumber: this.escapeHtml(sanitizedContext.shipment.hblNumber),
-        cargoControlNumber: this.escapeHtml(sanitizedContext.shipment.cargoControlNumber)
-      }
-    };
-  }
-
-  /**
-   * Escape HTML characters to prevent injection attacks.
-   */
-  private escapeHtml(text: string | null | undefined): string | null {
-    if (!text) return text as null;
-
-    return text
-      .replace(/&/g, "&amp;")
-      .replace(/</g, "&lt;")
-      .replace(/>/g, "&gt;")
-      .replace(/"/g, "&quot;")
-      .replace(/'/g, "&#x27;");
-  }
-
-  /**
-   * Render an error fragment when template rendering fails.
-   */
-  private renderErrorFragment(templateName: string, errorMessage: string): string {
-    this.logger.error(`Template '${templateName}' failed to render: ${errorMessage}`);
-
-    return `
-      <div class="error-fragment">
-        <p><strong>Note:</strong> Some information could not be displayed at this time.</p>
-        <p>For complete details, please contact our support team.</p>
-      </div>
-    `;
-  }
-
-  /**
-   * Render a fallback response when critical errors occur.
-   */
-  private renderFallbackResponse(errorMessage: string): string {
-    this.logger.error(`Using fallback response due to critical error: ${errorMessage}`);
-
-    return `
-      <p>Thank you for your inquiry. We've received your message and will get back to you shortly.</p>
-      <p>For immediate assistance, please contact our support team.</p>
-    `;
-  }
-
-  /**
-   * Get system diagnostics for monitoring and debugging.
-   */
-  getSystemDiagnostics(): {
-    templateManagerAvailable: boolean;
-    lastRenderTime?: Date;
-    totalRendersToday: number;
-  } {
-    return {
-      templateManagerAvailable: Boolean(this.templateManagerService),
-      // TODO: Add metrics tracking
-      totalRendersToday: 0
-    };
-  }
-
-  /**
-   * Validate that a template exists and can be rendered.
-   */
-  async validateTemplate(templateName: string, sampleContext: any): Promise<boolean> {
-    try {
-      await this.templateManagerService.renderTemplate(templateName, sampleContext);
-      return true;
-    } catch (error) {
-      this.logger.warn(`Template validation failed for '${templateName}': ${error.message}`);
-      return false;
-    }
-  }
-}
diff --git a/apps/portal-api/src/core-agent/templates/acknowledgement-all-docs-received.njk b/apps/portal-api/src/core-agent/templates/acknowledgement-all-docs-received.njk
deleted file mode 100644
index 5cc1e7f2..********
--- a/apps/portal-api/src/core-agent/templates/acknowledgement-all-docs-received.njk
+++ /dev/null
@@ -1,4 +0,0 @@
-{#- Acknowledgement - All docs received -#}
-{% set isLandTransport = shipment.modeOfTransport and shipment.modeOfTransport.toLowerCase() == 'land' %}
-
-<p>We have received the required documents for your shipment and are currently reviewing and processing them.{% if isLandTransport %} Transaction# will be sent to you shortly.{% endif %}</p>
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/templates/acknowledgement-missing-docs.njk b/apps/portal-api/src/core-agent/templates/acknowledgement-missing-docs.njk
deleted file mode 100644
index 03fe9f48..********
--- a/apps/portal-api/src/core-agent/templates/acknowledgement-missing-docs.njk
+++ /dev/null
@@ -1,11 +0,0 @@
-{#- When importer is missing docs for Ocean, Air and Truck. -#}
-{% if missingDocuments and missingDocuments.length > 0 %}
-<div>
-  <p>Please send the missing document shown below at your earliest convenience for the subject shipment, so we can file customs without delay.</p>
-  <ul>
-    {% for doc in missingDocuments %}
-      <li>{{ doc }}</li>
-    {% endfor %}
-  </ul>
-</div>
-{% endif %} 
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/templates/cad-document-attached.njk b/apps/portal-api/src/core-agent/templates/cad-document-attached.njk
deleted file mode 100644
index 85e79bbb..********
--- a/apps/portal-api/src/core-agent/templates/cad-document-attached.njk
+++ /dev/null
@@ -1,22 +0,0 @@
-{#- CAD Document Request - Status-specific responses -#}
-{%- if customsStatus == 'pending-commercial-invoice' -%}
-<p>We can't provide the CAD yet as we're missing the following document(s). Please send them at your earliest convenience so we can proceed with filing.</p>
-
-{%- elif customsStatus == 'pending-confirmation' -%}
-<p>We're currently unable to provide the CAD as there are compliance issues or missing required fields. Please respond to the email and provide additional information for the missing fields.</p>
-
-{%- elif customsStatus == 'pending-arrival' -%}
-<p>We've received your request for the CAD document. Our team has been notified and will provide it to you shortly.</p>
-
-{%- elif customsStatus == 'live' or customsStatus == 'entry-submitted' or customsStatus == 'entry-accepted' or customsStatus == 'exam' or customsStatus == 'released' or customsStatus == 'accounting-completed' -%}
-<p>Please see CAD document attached.</p>
-
-{%- else -%}
-<p>Your CAD (Customs Accounting Document) has been generated and is attached to this email.</p>
-{% if sideEffects.cadDocument %}
-<p><strong>Document:</strong> {{ sideEffects.cadDocument.fileName }}</p>
-{% endif %}
-<p>Please review the document and contact us if you have any questions.</p>
-{%- endif -%}
-
-{% include "responses/shipment-details.njk" %} 
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/templates/cad-document-not-ready.njk b/apps/portal-api/src/core-agent/templates/cad-document-not-ready.njk
deleted file mode 100644
index bd9f53ce..********
--- a/apps/portal-api/src/core-agent/templates/cad-document-not-ready.njk
+++ /dev/null
@@ -1,5 +0,0 @@
-<p>We've received your request for the CAD (Customs Accounting Document), however it cannot be generated at this time.</p>
-<p><strong>Reason:</strong> {{ cadBlockingReason }}</p>
-{% if not isSubmitted %}
-<p>Your shipment must be submitted to customs before the CAD document can be generated.</p>
-{% endif %} 
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/templates/contact-support.njk b/apps/portal-api/src/core-agent/templates/contact-support.njk
deleted file mode 100644
index 7315c9c8..********
--- a/apps/portal-api/src/core-agent/templates/contact-support.njk
+++ /dev/null
@@ -1,6 +0,0 @@
-<p>We encountered an issue processing your request. Please contact our support team for assistance.</p>
-{% if shipment and shipment.cargoControlNumber %}
-<p>Please reference shipment CCN: {{ shipment.cargoControlNumber }} when contacting support.</p>
-{% elif shipment and shipment.hblNumber %}
-<p>Please reference shipment HBL: {{ shipment.hblNumber }} when contacting support.</p>
-{% endif %} 
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/templates/documentation-coming-acknowledged.njk b/apps/portal-api/src/core-agent/templates/documentation-coming-acknowledged.njk
deleted file mode 100644
index d10737b7..********
--- a/apps/portal-api/src/core-agent/templates/documentation-coming-acknowledged.njk
+++ /dev/null
@@ -1,4 +0,0 @@
-{#- Acknowledgment when user says they'll send docs later -#}
-<p>Thank you for the update. We'll be waiting for the additional or updated documents.</p>
-
-{% include "responses/shipment-details.njk" %} 
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/templates/hold-shipment-confirmed.njk b/apps/portal-api/src/core-agent/templates/hold-shipment-confirmed.njk
deleted file mode 100644
index e769c668..********
--- a/apps/portal-api/src/core-agent/templates/hold-shipment-confirmed.njk
+++ /dev/null
@@ -1,7 +0,0 @@
-{#- Acknowledgement - Cancel/Hold shipment request -#}
-<p>We've received your request to cancel/hold the entry. Our team has been notified and will take the necessary action.</p>
-{% if sideEffects.backofficeAlerts.holdShipmentSent %}
-<p>Your hold request has been forwarded to our team and processing has been paused pending further instructions.</p>
-{% else %}
-<p>Our team will review this hold request and ensure processing is managed accordingly.</p>
-{% endif %} 
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/templates/manual-processing-requested.njk b/apps/portal-api/src/core-agent/templates/manual-processing-requested.njk
deleted file mode 100644
index 54924ef9..********
--- a/apps/portal-api/src/core-agent/templates/manual-processing-requested.njk
+++ /dev/null
@@ -1,6 +0,0 @@
-<p>We've received your request for manual processing and have noted your comments.</p>
-{% if sideEffects.backofficeAlerts.manualProcessingSent %}
-<p>Your request has been forwarded to our team for review and you should receive an update soon.</p>
-{% else %}
-<p>Our team will review this request and get back to you.</p>
-{% endif %} 
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/templates/responses/customs-status.njk b/apps/portal-api/src/core-agent/templates/responses/customs-status.njk
deleted file mode 100644
index 6468ebd4..********
--- a/apps/portal-api/src/core-agent/templates/responses/customs-status.njk
+++ /dev/null
@@ -1,74 +0,0 @@
-{#- --- Display Current Customs Status with Detailed Information --- -#}
-{%- if customsStatus == 'pending-commercial-invoice' -%}
-Please send the missing document shown below at your earliest convenience for the subject shipment, so we can file customs without delay.
-{%- if templateContext.missingFields.forPendingCommercialInvoice and templateContext.missingFields.forPendingCommercialInvoice.length > 0 -%}
-{%- for field in templateContext.missingFields.forPendingCommercialInvoice %}
-{{ field }}
-{%- endfor %}
-{%- endif %}
-{%- if templateContext.documentStatus.ciPlStatus == 'Missing' %}
-CI & PL: **Missing**
-{%- endif %}
-{% include "shipment-details.njk" %}
-{%- if templateContext.documentStatus %}
-HBL: {{ templateContext.documentStatus.hblStatus }} AN/EMF: {{ templateContext.documentStatus.anEmfStatus }}
-{%- endif %}
-{%- if templateContext.missingFields.formatted and templateContext.missingFields.formatted.length > 0 %}
-{% for field in templateContext.missingFields.formatted %}{{ field }} {% endfor %}
-{%- endif %}
-
-{%- elif customsStatus == 'pending-confirmation' -%}
-There are compliance issues or missing required fields preventing submission of the subject shipment. Please respond to the email and provide additional information for the missing fields.
-{%- if templateContext.missingFields.forPendingConfirmation and templateContext.missingFields.forPendingConfirmation.length > 0 -%}
-{%- for field in templateContext.missingFields.forPendingConfirmation %}
-{{ field }}
-{%- endfor %}
-{%- endif %}
-{%- if templateContext.missingFields.formatted and templateContext.missingFields.formatted.length > 0 %}
-{% for field in templateContext.missingFields.formatted %}{{ field }} {% endfor %}
-{%- endif %}
-{% include "shipment-details.njk" %}
-{%- if templateContext.documentStatus %}
-HBL: {{ templateContext.documentStatus.hblStatus }} AN/EMF: {{ templateContext.documentStatus.anEmfStatus }}  CI & PL: {{ templateContext.documentStatus.ciPlStatus }}
-{%- endif %}
-
-{%- elif customsStatus == 'pending-arrival' -%}
-{%- if templateContext.timing.formattedEtaPort and templateContext.timing.formattedEtaPort != 'TBD' -%}
-The estimated time of arrival (ETA) at the port for the subject shipment is {{ templateContext.timing.formattedEtaPort }}. We expect to submit the customs entry as the arrival date approaches.
-{%- else -%}
-The submission is pending the shipment's arrival.
-{%- endif %}
-{% include "shipment-details.njk" %}
-
-{%- elif customsStatus == 'live' -%}
-The submission for the subject shipment has been initiated. We will let you know once released by customs.
-{% include "shipment-details.njk" %}
-
-{%- elif customsStatus == 'entry-submitted' -%}
-The entry for the subject shipment has been submitted. We will let you know once released by customs.
-{% include "shipment-details.njk" %}
-
-{%- elif customsStatus == 'entry-accepted' -%}
-The subject shipment entry has been accepted by Customs and is awaiting arrival of goods.
-{% include "shipment-details.njk" %}
-
-{%- elif customsStatus == 'exam' -%}
-The subject shipment has been selected by customs for examination. We are contacting you for further information.
-{%- if templateContext.timing.formattedEtaDestination and templateContext.timing.formattedEtaDestination != 'TBD' -%}
-The ETA of the cargo at destination is {{ templateContext.timing.formattedEtaDestination }}.
-{%- endif %}
-{% include "shipment-details.njk" %}
-
-{%- elif customsStatus == 'released' or customsStatus == 'accounting-complete' -%}
-{%- if templateContext.timing.formattedReleaseDate and templateContext.timing.formattedReleaseDate != 'TBD' -%}
-The subject shipment has been released by CBSA on {{ templateContext.timing.formattedReleaseDate }}.
-{%- else -%}
-The subject shipment has been released by CBSA.
-{%- endif %}
-{% include "shipment-details.njk" %}
-
-{%- else -%}
-{#- FALLBACK case in case new status is added -#}
-The shipment's current customs status is {{ formattedCustomsStatus }}.
-{% include "shipment-details.njk" %}
-{%- endif %}
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/templates/responses/shipment-details.njk b/apps/portal-api/src/core-agent/templates/responses/shipment-details.njk
deleted file mode 100644
index 496c687c..********
--- a/apps/portal-api/src/core-agent/templates/responses/shipment-details.njk
+++ /dev/null
@@ -1,13 +0,0 @@
-**Details:**
-{%- if templateContext.identifiers.ccn %}
-CCN#: {{ templateContext.identifiers.ccn }}
-{%- endif -%}
-{%- if templateContext.identifiers.formattedContainers %}, Container#: {{ templateContext.identifiers.formattedContainers }}
-{%- endif -%}
-{%- if templateContext.identifiers.hbl %}, HBL#: {{ templateContext.identifiers.hbl }}
-{%- endif %}
-{%- if statusMessage %}
-Status: {{ statusMessage }}
-{%- else %}
-Status: {{ templateContext.statusContext.primaryMessage or formattedCustomsStatus }}
-{%- endif %} 
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/templates/responses/shipment-identifiers.njk b/apps/portal-api/src/core-agent/templates/responses/shipment-identifiers.njk
deleted file mode 100644
index 11138994..********
--- a/apps/portal-api/src/core-agent/templates/responses/shipment-identifiers.njk
+++ /dev/null
@@ -1,5 +0,0 @@
-{#-
-  Dynamically displays available shipment identifiers for a customer-facing message.
--#}
-{%- if hblNumber or cargoControlNumber or transactionNumber or (containerNumbers and containerNumbers.length > 0) -%}
-Regarding your Shipment{%- if hblNumber %} HBL# {{ hblNumber }}{% endif -%}{%- if cargoControlNumber %}, CCN# {{ cargoControlNumber }}{% endif -%}{%- if transactionNumber %}, Transaction# {{ transactionNumber }}{% endif -%}{%- if containerNumbers and containerNumbers.length > 0 %}, Container# {% for containerNum in containerNumbers -%}{{containerNum -}}{%- if not loop.last %}, {% endif -%}{%- endfor -%}{%- endif -%}: {%- endif -%}
diff --git a/apps/portal-api/src/core-agent/templates/rns-proof-attached.njk b/apps/portal-api/src/core-agent/templates/rns-proof-attached.njk
deleted file mode 100644
index 62451f22..********
--- a/apps/portal-api/src/core-agent/templates/rns-proof-attached.njk
+++ /dev/null
@@ -1,34 +0,0 @@
-{#- RNS Proof Request - Status-specific responses -#}
-{%- if customsStatus == 'pending-commercial-invoice' -%}
-<p>We can't provide the RNS yet as we're missing the following document(s). Please send them at your earliest convenience so we can proceed with filing.</p>
-
-{%- elif customsStatus == 'pending-confirmation' -%}
-<p>We're currently unable to provide the RNS as there are compliance issues or missing required fields. Please respond to the email and provide additional information for the missing fields.</p>
-
-{%- elif customsStatus == 'pending-arrival' or customsStatus == 'live' -%}
-<p>The RNS can only be provided after the shipment has been submitted to customs.</p>
-
-{%- elif customsStatus == 'entry-submitted' -%}
-<p>The RNS can only be provided after the shipment has been accepted by customs.</p>
-
-{%- elif customsStatus == 'entry-accepted' or customsStatus == 'exam' -%}
-<p>RNS information:</p>
-
-{%- elif customsStatus == 'released' or customsStatus == 'accounting-completed' -%}
-<p>RNS Proof of Release information:</p>
-
-{%- else -%}
-<p>RNS Proof of Release information:</p>
-{%- endif -%}
-
-{% if sideEffects.rnsProofData and sideEffects.rnsProofData.content %}
-<div style="font-family: monospace; white-space: pre-line; background-color: #f5f5f5; padding: 15px; border: 1px solid #ddd; border-radius: 4px;">
-  <strong>RNS PROOF OF RELEASE</strong>
-  
-  {{ sideEffects.rnsProofData.content | safe }}
-</div>
-{% else %}
-<p>RNS proof of release has been generated and is available for your shipment.</p>
-{% endif %}
-
-{% include "responses/shipment-details.njk" %} 
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/templates/rns-proof-not-ready.njk b/apps/portal-api/src/core-agent/templates/rns-proof-not-ready.njk
deleted file mode 100644
index 42bfac11..********
--- a/apps/portal-api/src/core-agent/templates/rns-proof-not-ready.njk
+++ /dev/null
@@ -1,5 +0,0 @@
-<p>RNS proof of release is not yet available for this shipment.</p>
-<p><strong>Reason:</strong> {{ rnsBlockingReason | default("Proof of release is only available once the goods have been released by customs.") }}</p>
-{% if isSubmitted and not isReleased %}
-<p>Your shipment has been submitted to customs and is currently being processed. You will be notified once it has been released.</p>
-{% endif %} 
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/templates/rush-processing-blocked.njk b/apps/portal-api/src/core-agent/templates/rush-processing-blocked.njk
deleted file mode 100644
index 52810e13..********
--- a/apps/portal-api/src/core-agent/templates/rush-processing-blocked.njk
+++ /dev/null
@@ -1,5 +0,0 @@
-<p>We've received your rush processing request, however this shipment cannot currently be expedited.</p>
-<p><strong>Reason:</strong> {{ rushBlockingReason }}</p>
-{% if not isCompliant %}
-<p>Please resolve any compliance issues first, then submit a new rush request.</p>
-{% endif %} 
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/templates/rush-processing-success.njk b/apps/portal-api/src/core-agent/templates/rush-processing-success.njk
deleted file mode 100644
index 858637ef..********
--- a/apps/portal-api/src/core-agent/templates/rush-processing-success.njk
+++ /dev/null
@@ -1,34 +0,0 @@
-{#- Rush Processing Success - Status-specific messages -#}
-{%- if customsStatus == 'pending-commercial-invoice' -%}
-<p>Please send the missing document shown below at your earliest convenience for the subject shipment, so we can file customs without delay.</p>
-
-{%- elif customsStatus == 'pending-confirmation' -%}
-<p>There are compliance issues or missing required fields preventing submission of the subject shipment. Please respond to the email and provide additional information for the missing fields.</p>
-
-{%- elif customsStatus == 'pending-arrival' or customsStatus == 'live' -%}
-<p>We've received your rush request and will be submitting the entry right away.</p>
-
-{%- elif customsStatus == 'entry-submitted' -%}
-<p>The entry for the subject shipment has already been submitted. We will let you know once released by customs.</p>
-
-{%- elif customsStatus == 'entry-accepted' -%}
-<p>The subject shipment entry has already been submitted and accepted by Customs and is awaiting arrival of goods.</p>
-
-{%- elif customsStatus == 'exam' -%}
-<p>Please note the shipment has been selected for exam and will be released once the examination is complete.</p>
-
-{%- elif customsStatus == 'released' or customsStatus == 'accounting-completed' -%}
-{%- if templateContext.timing.formattedReleaseDate and templateContext.timing.formattedReleaseDate != 'TBD' -%}
-<p>The subject shipment has been released by CBSA on {{ templateContext.timing.formattedReleaseDate }}.</p>
-{%- else -%}
-<p>The subject shipment has been released by CBSA.</p>
-{%- endif -%}
-
-{%- else -%}
-<p>We've received your rush processing request and have forwarded it to our team for immediate attention.</p>
-{% if sideEffects.backofficeAlerts.rushProcessingSent %}
-<p>Your request has been escalated and you should receive an update within 2 business hours.</p>
-{% endif %}
-{%- endif -%}
-
-{% include "responses/shipment-details.njk" %} 
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/templates/submission-required-notice.njk b/apps/portal-api/src/core-agent/templates/submission-required-notice.njk
deleted file mode 100644
index 7a9a6fa8..********
--- a/apps/portal-api/src/core-agent/templates/submission-required-notice.njk
+++ /dev/null
@@ -1,8 +0,0 @@
-<div class="notice">
-  <p><strong>Notice:</strong> This shipment must be submitted to customs before this action can be completed.</p>
-  {% if not isCompliant %}
-  <p>Please resolve all compliance issues first, then submit your shipment.</p>
-  {% else %}
-  <p>Your shipment appears to be compliant and ready for submission.</p>
-  {% endif %}
-</div> 
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/templates/system-unavailable.njk b/apps/portal-api/src/core-agent/templates/system-unavailable.njk
deleted file mode 100644
index b41e2ffd..********
--- a/apps/portal-api/src/core-agent/templates/system-unavailable.njk
+++ /dev/null
@@ -1,2 +0,0 @@
-<p>Our system is temporarily unavailable. We are working to resolve this issue and will process your request as soon as possible.</p>
-<p>If this is urgent, please contact our support team directly.</p> 
\ No newline at end of file
diff --git a/apps/portal-api/src/core-agent/types/queue.types.ts b/apps/portal-api/src/core-agent/types/queue.types.ts
new file mode 100644
index ********..45167e30
--- /dev/null
+++ b/apps/portal-api/src/core-agent/types/queue.types.ts
@@ -0,0 +1,73 @@
+import { RegisterQueueOptions } from "@nestjs/bullmq";
+import { NestWorkerOptions } from "@nestjs/bullmq/dist/interfaces/worker-options.interface";
+import { Job, JobsOptions, Queue } from "bullmq";
+
+export const DEFAULT_JOB_OPTIONS: JobsOptions = {
+  attempts: 1,
+  // backoff: {
+  //   type: "exponential",
+  //   delay: 1000,
+  // },
+  removeOnComplete: {
+    age: 3600,
+    count: 1000
+  },
+  removeOnFail: {
+    age: 24 * 3600
+  }
+};
+
+export const DEFAULT_WORKER_OPTIONS: NestWorkerOptions = {
+  concurrency: 1,
+  lockDuration: 1000 * 60 * 2 // setting 2 minutes for processing to complete
+};
+
+export enum CoreAgentQueueName {
+  EMAIL_SAVED = "core-agent-email-saved",
+  EVENT_EMITTER = "core-agent-event-emitter",
+  HANDLE_REQUEST_MESSAGE = "core-agent-handle-request-message"
+}
+
+export const CORE_AGENT_QUEUES: Array<RegisterQueueOptions> = [
+  {
+    name: CoreAgentQueueName.EMAIL_SAVED,
+    defaultJobOptions: DEFAULT_JOB_OPTIONS
+  },
+  {
+    name: CoreAgentQueueName.EVENT_EMITTER,
+    defaultJobOptions: DEFAULT_JOB_OPTIONS
+  },
+  {
+    name: CoreAgentQueueName.HANDLE_REQUEST_MESSAGE,
+    defaultJobOptions: DEFAULT_JOB_OPTIONS
+  }
+];
+
+// Email Saved Queue
+export interface EmailSavedJobData {
+  emailId: number;
+  organizationId: number;
+  hasAttachments?: boolean;
+}
+export type EmailSavedJob = Job<EmailSavedJobData, null, string>;
+export type EmailSavedQueue = Queue<EmailSavedJobData, null, string>;
+
+// Event Emitter Queue
+export interface EventEmitterJobData {
+  eventType: string;
+  eventData: any;
+}
+export type EventEmitterJob = Job<EventEmitterJobData, null, string>;
+export type EventEmitterQueue = Queue<EventEmitterJobData, null, string>;
+
+// Handle Request Message Queue
+export interface HandleRequestMessageJobData {
+  emailId: number;
+  organizationId: number;
+}
+export type HandleRequestMessageJob = Job<HandleRequestMessageJobData, null, string>;
+export type HandleRequestMessageQueue = Queue<HandleRequestMessageJobData, null, string>;
+
+// Generic Core Agent Job
+export type CoreAgentJobData = EmailSavedJobData | EventEmitterJobData | HandleRequestMessageJobData;
+export type CoreAgentJob = Job<CoreAgentJobData, null, string>;
diff --git a/apps/portal-api/src/core-agent/types/response-fragment.types.ts b/apps/portal-api/src/core-agent/types/response-fragment.types.ts
deleted file mode 100644
index 4d5dd96f..********
--- a/apps/portal-api/src/core-agent/types/response-fragment.types.ts
+++ /dev/null
@@ -1,22 +0,0 @@
-import { ValidatedEmailAction } from "@/email/schemas/validated-email-intents.schema";
-
-/**
- * Response fragment returned by intent handlers.
- * Each fragment represents a piece of the final response that should be rendered.
- */
-export interface ResponseFragment {
-  /** Template name to render (e.g., 'customs-status', 'cad-document-attached') */
-  template: string;
-
-  /** Priority for ordering fragments in final response (lower number = higher priority) */
-  priority?: number;
-
-  /** Additional context data specific to this fragment (merged with main context) */
-  fragmentContext?: Record<string, any>;
-}
-
-/**
- * Re-export existing ValidatedEmailAction as ValidatedIntent for consistency.
- * This aligns our intent handlers with the existing email processing system.
- */
-export type ValidatedIntent = ValidatedEmailAction;
diff --git a/apps/portal-api/src/core-agent/types/shipment-context.types.ts b/apps/portal-api/src/core-agent/types/shipment-context.types.ts
deleted file mode 100644
index 291a7405..********
--- a/apps/portal-api/src/core-agent/types/shipment-context.types.ts
+++ /dev/null
@@ -1,159 +0,0 @@
-import { Shipment, ValidateShipmentComplianceResponseDto, Organization } from "nest-modules";
-import { EmailService } from "@/email/services/email.service";
-import { RNSStatusChangeEmailSender } from "@/shipment/senders/rns-status-change-email.sender";
-import { RnsProofService } from "@/email/services/rns-proof-service";
-import { CustomsStatusListener } from "@/shipment/listeners/customs-status.listener";
-import { EntrySubmissionService } from "@/shipment/services/entry-submission.service";
-import { ImporterService } from "@/importer/importer.service";
-
-/**
- * Comprehensive context interface containing all shipment-related business rule evaluations,
- * formatted display data, and service instances. This serves as the single source of truth
- * for all shipment response generation.
- */
-export interface ShipmentContext {
-  // Raw data
-  shipment: Shipment;
-  compliance: ValidateShipmentComplianceResponseDto;
-  organization: Organization;
-
-  // Business rule evaluations (evaluated once, used everywhere)
-  canRush: boolean;
-  canGenerateCAD: boolean;
-  canGenerateRNSProof: boolean;
-  isCompliant: boolean;
-  isReleased: boolean;
-  isSubmitted: boolean;
-  canBeModified: boolean;
-  isEntryUploaded: boolean;
-  canUpdateEntry: boolean;
-  isAllDocsReceived: boolean;
-
-  // Detailed data for templates
-  missingDocuments: string[];
-  complianceErrors: string[];
-  nonCompliantInvoices: any[];
-  rushBlockingReason: string;
-  cadBlockingReason: string;
-  rnsBlockingReason: string;
-
-  // Formatted display values (from existing context builders)
-  formattedCustomsStatus: string;
-  shipmentIdentifiers: {
-    hblNumber: string | null;
-    cargoControlNumber: string | null;
-    transactionNumber: string | null;
-    containerNumbers: string[];
-    formattedContainers: string;
-    hasMultipleContainers: boolean;
-    primaryContainer: string | null;
-  };
-  etaInformation: {
-    etaPortValue?: string;
-    portName?: string;
-    etaDestinationValue?: string;
-    destinationName?: string;
-  };
-  shippingInformation: {
-    isTrackerOnline: boolean;
-    shipmentStatus: string;
-    isAir: boolean;
-    trackingStatus?: string;
-  };
-
-  // Enhanced document and missing fields analysis
-  documentDataStatus: {
-    hasHBLDataForSubmission: boolean;
-    hasAnEmfDataForSubmission: boolean;
-    hasCompleteHBLData: boolean;
-    hasCompleteAnEmfData: boolean;
-    ciReceived: boolean;
-    plReceived: boolean;
-    hblStatus: "Received" | "Missing";
-    anEmfStatus: "Received" | "Missing";
-    ciPlStatus: "Received" | "Missing";
-  };
-
-  missingFieldsAnalysis: {
-    missingIdentifiers: string[];
-    missingMeasurements: string[];
-    missingTiming: string[];
-    missingLocations: string[];
-    ogdFilingStatus: "pending" | "complete" | "not-required";
-    formattedMissingFields: string[];
-  };
-
-  templateContext: {
-    identifiers: {
-      ccn: string;
-      hbl: string;
-      containers: string[];
-      formattedContainers: string;
-    };
-    documentStatus: {
-      hblStatus: "Received" | "Missing";
-      anEmfStatus: "Received" | "Missing";
-      ciPlStatus: "Received" | "Missing";
-    };
-    missingFields: {
-      forPendingCommercialInvoice: string[];
-      forPendingConfirmation: string[];
-      formatted: string[];
-    };
-    timing: {
-      etaPort: string | null;
-      etaDestination: string | null;
-      releaseDate: string | null;
-      formattedEtaPort: string;
-      formattedEtaDestination: string;
-      formattedReleaseDate: string;
-    };
-    statusContext: {
-      primaryMessage: string;
-      secondaryDetails: string[];
-      actionRequired: boolean;
-    };
-  };
-
-  // Backward compatibility - maintain existing document receipt status
-  documentReceiptStatus: {
-    hblReceived: boolean;
-    anEmfReceived: boolean;
-    ciReceived: boolean;
-    plReceived: boolean;
-  };
-  missingFieldsStatus: string[];
-
-  // User interaction flags (modified by intent handlers)
-  directlyAsked: {
-    [key: string]: boolean;
-  };
-
-  // Side effect results (populated by intent handlers)
-  sideEffects: {
-    cadDocument?: {
-      fileName: string;
-      mimeType: string;
-      b64Data: string;
-    };
-    rnsProofData?: {
-      content: string;
-      releaseDate: string | null;
-    };
-    backofficeAlerts: {
-      rushProcessingSent?: boolean;
-      manualProcessingSent?: boolean;
-      holdShipmentSent?: boolean;
-    };
-  };
-
-  // Service instances for intent handlers (internal use only)
-  _services: {
-    emailService: EmailService;
-    rnsStatusChangeEmailSender: RNSStatusChangeEmailSender;
-    rnsProofService: RnsProofService;
-    customsStatusListener: CustomsStatusListener;
-    entrySubmissionService: EntrySubmissionService;
-    importerService: ImporterService;
-  };
-}
