# Frontend Integration Ticket: ChatAgentModule Support

## Summary
Update the Portal frontend to optionally connect to the new ChatAgentModule endpoints instead of the existing ChatbotModule endpoints.

## Description
We have implemented a new ChatAgentModule in the backend with enhanced intent processing capabilities. This ticket involves updating the frontend to optionally use these new endpoints based on a configuration setting.

## Technical Details

### Current Implementation
- The frontend currently connects to `/chat` endpoints using the `Chatbot.service.ts`
- It uses SSE for streaming responses from the AI chatbot
- The main connection is established through the `subscribeToChatbot` function

### Required Changes
1. Create a new service file `ChatAgent.service.ts` that connects to the new endpoints:
   - `/chat-communication/stream` for SSE streaming
   - `/chat-communication/session` for creating sessions
   - `/chat-communication/history/:conversationId` for chat history
   
2. Update the existing service or create a factory/provider that can switch between:
   - Using the old `Chatbot.service.ts` (connecting to `/chat`)
   - Using the new `ChatAgent.service.ts` (connecting to `/chat-communication`)

3. Add a configuration toggle in the application settings to enable/disable the new ChatAgentModule

4. Ensure that response handling is compatible with both implementations

## Acceptance Criteria
- [ ] The frontend can connect to both the existing ChatbotModule and the new ChatAgentModule
- [ ] A configuration option allows switching between implementations
- [ ] The UI behavior is identical regardless of which backend implementation is used
- [ ] All existing functionality continues to work as expected
- [ ] SSE streaming works with both implementations

## Implementation Notes
- No changes to the UI components should be necessary
- The payload format is compatible between both implementations
- When connecting to the new endpoints, ensure all proper authentication headers are included

## Related Resources
- Backend PR with ChatAgentModule implementation: [Link to PR] 