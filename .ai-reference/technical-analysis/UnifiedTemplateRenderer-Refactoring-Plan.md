# UnifiedTemplateRenderer Refactoring Plan

## Overview

This document outlines the plan to refactor the `UnifiedTemplateRendererService` by moving business logic, data formatting, and template variable construction to the `CleanAgentContextService`. The goal is to achieve proper separation of concerns and simplify the template rendering service.

## Current State Analysis

### Problems with Current Implementation

1. **Violation of Single Responsibility Principle**
   - Template service handles data gathering, business logic, formatting, and rendering
   - Business rules scattered across formatting methods
   - Hard-coded status mappings and compliance scoring

2. **Code Duplication**
   - Business logic likely exists elsewhere in the system
   - Formatting logic should be centralized for consistency

3. **Testing Complexity**
   - Multiple responsibilities make unit testing difficult
   - Mocking requirements are extensive

4. **Maintenance Issues**
   - Business rule changes require updates in template service
   - Tight coupling to entity structures

## Target Architecture

### Simplified Responsibilities

**CleanAgentContextService (Enhanced)**
- Gather all raw data from various services
- Apply all business logic and formatting rules
- Construct complete template variable set
- Return unified context object ready for template rendering

**UnifiedTemplateRendererService (Simplified)**
- Coordinate data gathering via CleanAgentContextService
- Select relevant messages from email-response-messages.ts
- Apply section visibility flags
- Render template with unified context

## Implementation Plan

### Phase 1: Enhance CleanAgentContextService

#### 1.1 Add New Interface for Unified Context

```typescript
export interface UnifiedTemplateContext {
  // Raw data (existing)
  shipmentData: any;
  documentData: any;
  rnsData: any;
  importerData: any;
  validationData: any;
  
  // Formatted template variables (new)
  CCN_PLACEHOLDER: string;
  CONTAINER_PLACEHOLDER: string;
  HBL_PLACEHOLDER: string;
  HBL_STATUS_PLACEHOLDER: string;
  AN_EMF_STATUS_PLACEHOLDER: string;
  CI_PL_STATUS_PLACEHOLDER: string;
  CUSTOMS_STATUS_LINE_PLACEHOLDER: string;
  MISSING_DOCUMENTS_PLACEHOLDER: string;
  MISSING_FIELDS_PLACEHOLDER: string;
  COMPLIANCE_ERRORS_PLACEHOLDER: string;
  
  // Status and compliance data (new)
  COMPLIANCE_STATUS: string;
  COMPLIANCE_SCORE: number;
  STATUS_CATEGORY: string;
  STATUS_PRIORITY: number;
  STATUS_UPDATED: string;
  
  // Processed documents (existing, possibly enhanced)
  PROCESSED_DOCUMENTS?: ProcessedDocument[];
}
```

#### 1.2 Add getUnifiedTemplateContext Method

**File:** `apps/portal-api/src/clean-agent-context/clean-agent-context.service.ts`

```typescript
async getUnifiedTemplateContext(
  shipmentId: number,
  organizationId: number,
  request: any
): Promise<UnifiedTemplateContext> {
  // 1. Gather all raw data (existing functionality)
  const rawContext = await this.gatherAllData(shipmentId, organizationId, request);
  
  // 2. Apply formatting and business logic (moved from UnifiedTemplateRendererService)
  const formattedContext = this.buildFormattedContext(rawContext);
  
  return {
    ...rawContext,
    ...formattedContext
  };
}
```

#### 1.3 Move Formatting Methods to CleanAgentContextService

**Methods to Move from UnifiedTemplateRendererService:**

```typescript
// Move these methods with business logic intact
private formatContainers(containers: any[]): string
private formatCustomsStatus(status: string): string
private formatMissingDocuments(documentData: any): string
private formatMissingFields(validationData: any): string
private formatComplianceErrors(validationData: any): string
private buildComplianceData(validationData: any): any
private buildStatusData(shipment: Shipment, shipmentData: any): any
private buildPlaceholderData(shipment: Shipment, contextData: TemplateContextData): any
private calculateComplianceScore(validationData: any): number
private getStatusCategory(status: string): string
private getStatusPriority(status: string): number
```

**Implementation in CleanAgentContextService:**

```typescript
private buildFormattedContext(rawContext: any): Partial<UnifiedTemplateContext> {
  const shipment = rawContext.shipment;
  
  return {
    // Placeholder variables
    CCN_PLACEHOLDER: shipment.cargoControlNumber || 'N/A',
    CONTAINER_PLACEHOLDER: this.formatContainers(shipment.containers || []),
    HBL_PLACEHOLDER: shipment.hblNumber || 'N/A',
    HBL_STATUS_PLACEHOLDER: rawContext.documentData?.HBL_MISSING ? 'Missing' : 'Received',
    AN_EMF_STATUS_PLACEHOLDER: rawContext.documentData?.AN_EMF_MISSING ? 'Missing' : 'Received',
    CI_PL_STATUS_PLACEHOLDER: rawContext.documentData?.CI_PL_MISSING ? 'Missing' : 'Received',
    CUSTOMS_STATUS_LINE_PLACEHOLDER: this.formatCustomsStatus(shipment.customsStatus),
    MISSING_DOCUMENTS_PLACEHOLDER: this.formatMissingDocuments(rawContext.documentData),
    MISSING_FIELDS_PLACEHOLDER: this.formatMissingFields(rawContext.validationData),
    COMPLIANCE_ERRORS_PLACEHOLDER: this.formatComplianceErrors(rawContext.validationData),
    
    // Compliance and status data
    COMPLIANCE_STATUS: rawContext.validationData?.COMPLIANCE_ERRORS?.length > 0 ? 'ISSUES_FOUND' : 'COMPLIANT',
    COMPLIANCE_SCORE: this.calculateComplianceScore(rawContext.validationData),
    STATUS_CATEGORY: this.getStatusCategory(shipment.customsStatus),
    STATUS_PRIORITY: this.getStatusPriority(shipment.customsStatus),
    STATUS_UPDATED: new Date().toISOString()
  };
}
```

### Phase 2: Simplify UnifiedTemplateRendererService

#### 2.1 Remove Business Logic Methods

**Methods to Delete:**
- `buildPlaceholderData()`
- `buildComplianceData()`
- `buildStatusData()`
- `formatContainers()`
- `formatCustomsStatus()`
- `formatMissingDocuments()`
- `formatMissingFields()`
- `formatComplianceErrors()`
- `calculateComplianceScore()`
- `getStatusCategory()`
- `getStatusPriority()`

#### 2.2 Simplify renderEmailResponse Method

```typescript
async renderEmailResponse(
  shipment: Shipment,
  organizationId: number,
  request: any,
  sectionFlags: TemplateSectionFlags,
  messageData: TemplateMessageData,
  cadRequest: CADDocumentRequest,
  renderOptions: RenderOptions = {}
): Promise<EmailRenderResult> {
  
  const renderTimer = this.startRenderTimer();
  
  try {
    this.logger.log(`Starting template render for shipment ${shipment.id}`);
    
    // 1. Get unified context with all template variables pre-formatted
    const unifiedContext = await this.cleanAgentContextService.getUnifiedTemplateContext(
      shipment.id,
      organizationId,
      request
    );
    
    // 2. Generate dynamic content if requested (keep this for now)
    const dynamicContent = await this.generateDynamicContent(
      shipment, unifiedContext, renderOptions
    );
    
    // 3. Build final template context
    const templateContext = {
      // Pre-formatted context from CleanAgentContextService
      ...unifiedContext,
      
      // Section visibility flags
      ...sectionFlags,
      
      // Message content
      ...messageData,
      
      // Dynamic content if available
      ...(dynamicContent && {
        DYNAMIC_EXPLANATION: dynamicContent.explanation,
        DYNAMIC_RECOMMENDATIONS: dynamicContent.recommendations,
        DYNAMIC_NEXT_STEPS: dynamicContent.nextSteps
      })
    };
    
    // 4. Render template
    const content = await this.templateManagerService.renderTemplate(
      'email-response-complete-template',
      templateContext
    );
    
    // 5. Generate attachments (consider moving to separate service)
    const attachments = await this.generateAttachments(shipment, cadRequest, unifiedContext);
    
    const duration = renderTimer.end();
    
    // 6. Emit success event
    this.eventEmitter.emit('template.render.success', {
      shipmentId: shipment.id,
      templateType: 'unified-email-response',
      duration,
      hasAttachments: attachments.length > 0
    });
    
    return {
      content,
      attachments,
      renderData: this.sanitizeRenderData(templateContext),
      metadata: {
        renderDuration: duration,
        dynamicContentGenerated: !!dynamicContent
      }
    };
    
  } catch (error) {
    this.handleRenderError(error, shipment, renderTimer);
    throw new TemplateRenderError(
      `Failed to render email response for shipment ${shipment.id}`,
      error,
      { shipmentId: shipment.id, sectionFlags }
    );
  }
}
```

#### 2.3 Remove Unused Methods and Interfaces

**Remove these unused components:**
- `gatherTemplateData()` method
- `buildUnifiedContext()` method
- `TemplateContextData` interface (if no longer needed elsewhere)

### Phase 3: Update Dependencies and Tests

#### 3.1 Update Import Statements

**CleanAgentContextService:**
- Import `Shipment` entity if not already imported
- Add any utility functions needed for formatting

**UnifiedTemplateRendererService:**
- Remove unused imports
- Update interface imports if needed

#### 3.2 Update Tests

**CleanAgentContextService Tests:**
- Add tests for `getUnifiedTemplateContext()` method
- Add tests for all moved formatting methods
- Test business logic scenarios (status mappings, compliance scoring)

**UnifiedTemplateRendererService Tests:**
- Simplify tests to focus on template rendering coordination
- Mock `cleanAgentContextService.getUnifiedTemplateContext()`
- Remove tests for moved business logic methods

### Phase 4: Consider Future Enhancements

#### 4.1 Message Selection Logic

Add method to select only relevant messages based on intent:

```typescript
private selectRelevantMessages(
  messageData: TemplateMessageData,
  intent: string
): Partial<TemplateMessageData> {
  // Return only the messages that should appear for this specific intent
  // This prevents showing all possible message variables in every email
}
```

#### 4.2 Attachment Service

Consider creating a dedicated `EmailAttachmentService`:

```typescript
@Injectable()
export class EmailAttachmentService {
  async generateAttachments(
    shipment: Shipment,
    cadRequest: CADDocumentRequest,
    context: UnifiedTemplateContext
  ): Promise<DocumentAttachment[]> {
    // Move attachment generation logic here
  }
}
```

#### 4.3 Dynamic Content Service

Consider separating dynamic content generation:

```typescript
@Injectable()
export class DynamicContentService {
  async generateDynamicContent(
    shipment: Shipment,
    context: UnifiedTemplateContext,
    options: RenderOptions
  ): Promise<DynamicContent | null> {
    // Move LLM integration logic here
  }
}
```

## Implementation Steps

### Step 1: Prepare CleanAgentContextService
1. Add `UnifiedTemplateContext` interface
2. Add `getUnifiedTemplateContext()` method stub
3. Copy formatting methods from UnifiedTemplateRendererService
4. Implement `buildFormattedContext()` method
5. Write tests for new functionality

### Step 2: Update UnifiedTemplateRendererService
1. Update `renderEmailResponse()` to use new CleanAgentContextService method
2. Remove old business logic methods
3. Update tests to mock new dependencies
4. Verify template rendering still works

### Step 3: Clean Up
1. Remove unused interfaces and methods
2. Update documentation
3. Run full test suite
4. Performance testing to ensure no regression

### Step 4: Validate
1. Test with various shipment scenarios
2. Verify all template variables are populated correctly
3. Check that section visibility still works
4. Validate error handling

## Benefits of This Refactoring

1. **Single Responsibility**: Each service has a clear, focused purpose
2. **Reusability**: Business logic in CleanAgentContextService can be used by other services
3. **Testability**: Easier to unit test isolated functionality
4. **Maintainability**: Business rule changes happen in one place
5. **Consistency**: All formatting follows same patterns
6. **Performance**: Potential for better caching of formatted data

## Risks and Mitigation

### Risk: Breaking Existing Functionality
**Mitigation**: 
- Comprehensive testing before and after refactoring
- Gradual implementation with feature flags if needed
- Maintain backward compatibility during transition

### Risk: Performance Impact
**Mitigation**:
- Profile performance before and after changes
- Consider caching formatted context data
- Monitor production performance metrics

### Risk: Integration Issues
**Mitigation**:
- Coordinate with teams using CleanAgentContextService
- Update all consumers of UnifiedTemplateRendererService
- Thorough integration testing

## Timeline Estimate

- **Phase 1 (CleanAgentContextService Enhancement)**: 2-3 days
- **Phase 2 (UnifiedTemplateRendererService Simplification)**: 1-2 days  
- **Phase 3 (Dependencies and Tests)**: 2-3 days
- **Phase 4 (Validation and Documentation)**: 1 day

**Total: 6-9 days**

## Success Criteria

1. All existing functionality preserved
2. Template rendering produces identical output
3. Business logic centralized in CleanAgentContextService
4. UnifiedTemplateRendererService has single responsibility
5. Test coverage maintained or improved
6. Performance characteristics unchanged or improved
7. Code maintainability significantly improved