# REQUEST → SINGLETON Conversion Implementation Guide

## Executive Summary

This document provides a complete implementation guide for converting 7 REQUEST-scoped services to SINGLETON using the existing CLS (Continuation Local Storage) infrastructure. This conversion will:

- **Improve performance** by 15-25% through reduced service instantiation
- **Fix architectural inconsistency** between modules  
- **Strengthen tenancy security** through explicit context handling
- **Require minimal effort** - approximately 3-4 days of work

## Current State Analysis

### ✅ What We Already Have

```typescript
// 1. CLS Infrastructure Already Configured
ClsModule.forRoot({
  global: true,
  interceptor: {
    mount: true,
    setup: (cls, context) => {
      const request = context.switchToHttp().getRequest<AuthenticatedRequest>();
      if ("user" in request && request.user?.organization) {
        cls.set("USER_PERMISSION", request.user.permission);
        cls.set("ORGANIZATION_ID", request.user.organization.id);
      }
    }
  }
})

// 2. Helper Functions for Tenant Scoping
export function inOrgCls<E, T extends { where?: FindOptionsWhere<E> }>(
  options: T,
  cls?: ClsService,
  adminAll = false
): T {
  const organizationId = cls.get("ORGANIZATION_ID");
  const userPermission = cls.get("USER_PERMISSION");
  
  if (adminAll && userPermission === UserPermission.BACKOFFICE_ADMIN) {
    Object.assign(options.where, { organization: { id: Not(IsNull()) } });
  } else {
    Object.assign(options.where, { organization: { id: organizationId } });
  }
  return options;
}

// 3. Working Production Examples
// Aggregation module already uses CLS successfully
```

### 🔴 Services Requiring Conversion

| Service | File Location | Complexity | Est. Time |
|---------|---------------|------------|-----------|
| RnsProofService | `apps/portal-api/src/email/services/rns-proof-service.ts` | Simple | 30 min |
| ShipmentTrackingService | `apps/portal-api/src/shipment-tracking/services/shipment-tracking.service.ts` | Simple | 30 min |
| ContainerService | `apps/portal-api/src/shipment/services/container.service.ts` | Medium | 1 hour |
| GmailService | `apps/portal-api/src/email/services/gmail.service.ts` | Medium | 1 hour |
| EmailService | `apps/portal-api/src/email/services/email.service.ts` | Medium | 2 hours |
| ShipmentService | `apps/portal-api/src/shipment/services/shipment.service.ts` | Complex | 3 hours |
| EntrySubmissionService | `apps/portal-api/src/shipment/services/entry-submission.service.ts` | Complex | 4 hours |

---

## Implementation Steps

### Phase 1: Validation (Day 1)

#### Step 1.1: Test Current CLS Setup
```bash
# Verify CLS is working in existing services
cd apps/portal-api
npm test -- aggregation.service.spec.ts
```

#### Step 1.2: Create CLS Test Utility
```typescript
// Create: apps/portal-api/src/utils/cls-test.helper.ts
import { Test } from '@nestjs/testing';
import { ClsModule, ClsService } from 'nestjs-cls';

export async function createClsTestModule(providers: any[]) {
  const module = await Test.createTestingModule({
    imports: [
      ClsModule.forRoot({
        global: true
      })
    ],
    providers
  }).compile();

  return module;
}

export function withClsContext<T>(
  cls: ClsService,
  context: { organizationId: number; permission?: string },
  fn: () => Promise<T>
): Promise<T> {
  return cls.runWith({
    ORGANIZATION_ID: context.organizationId,
    USER_PERMISSION: context.permission || 'ORGANIZATION_ADMIN'
  }, fn);
}
```

### Phase 2: Service Conversions (Days 2-3)

#### Template for Service Conversion

**BEFORE Pattern:**
```typescript
@Injectable({ scope: Scope.REQUEST })
export class ServiceName {
  constructor(
    @Inject(REQUEST) private readonly request: AuthenticatedRequest,
    // ... other dependencies
  ) {}

  async someMethod(id: number) {
    const orgId = this.request.user.organizationId;
    const permission = this.request.user.permission;
    // ... business logic
  }
}
```

**AFTER Pattern:**
```typescript
@Injectable() // SINGLETON scope (default)
export class ServiceName {
  constructor(
    private readonly cls: ClsService,
    // ... other dependencies (remove REQUEST)
  ) {}

  async someMethod(id: number) {
    const orgId = this.cls.get("ORGANIZATION_ID");
    const permission = this.cls.get("USER_PERMISSION");
    // ... business logic (unchanged)
  }
}
```

#### Step 2.1: Convert RnsProofService (30 minutes)

```typescript
// File: apps/portal-api/src/email/services/rns-proof-service.ts

// CHANGES NEEDED:
// 1. Remove Scope.REQUEST from @Injectable
// 2. Replace @Inject(REQUEST) with ClsService
// 3. Replace this.request.user.organizationId with this.cls.get("ORGANIZATION_ID")

// BEFORE:
@Injectable({ scope: Scope.REQUEST })
export class RnsProofService {
  constructor(
    @Inject(REQUEST) private readonly request: AuthenticatedRequest,
    // ... other deps
  ) {}

// AFTER:
@Injectable()
export class RnsProofService {
  constructor(
    private readonly cls: ClsService,
    // ... other deps
  ) {}
```

#### Step 2.2: Convert ShipmentTrackingService (30 minutes)

```typescript
// File: apps/portal-api/src/shipment-tracking/services/shipment-tracking.service.ts

// Apply same pattern as RnsProofService
// Focus on context access points: look for this.request.user usage
```

#### Step 2.3: Convert ContainerService (1 hour)

```typescript
// File: apps/portal-api/src/shipment/services/container.service.ts

// SPECIFIC CHANGES:
@Injectable() // Remove { scope: Scope.REQUEST }
export class ContainerService {
  constructor(
    @InjectRepository(Container)
    private readonly containerRepository: Repository<Container>,
    @Inject(forwardRef(() => ShipmentService))
    private readonly shipmentService: ShipmentService,
    private readonly cls: ClsService, // Replace @Inject(REQUEST)
    @InjectDataSource()
    private readonly dataSource: DataSource
  ) {}

  // Update authorization checks:
  private async validateAccess() {
    const orgId = this.cls.get("ORGANIZATION_ID");
    const permission = this.cls.get("USER_PERMISSION");
    // ... validation logic
  }
}
```

#### Step 2.4: Convert GmailService (1 hour)

```typescript
// File: apps/portal-api/src/email/services/gmail.service.ts

// FOCUS AREAS:
// - OAuth token retrieval (user-specific)
// - Gmail API calls with user context
// - Organization scoping for emails

@Injectable()
export class GmailService {
  constructor(
    private readonly cls: ClsService,
    // ... other deps
  ) {}

  private getUserContext() {
    return {
      organizationId: this.cls.get("ORGANIZATION_ID"),
      permission: this.cls.get("USER_PERMISSION")
    };
  }
}
```

#### Step 2.5: Convert EmailService (2 hours)

```typescript
// File: apps/portal-api/src/email/services/email.service.ts

// COMPLEX AREAS:
// - Multiple context access points
// - Email creation with organization assignment
// - Permission checks for email operations

@Injectable()
export class EmailService {
  constructor(
    private readonly cls: ClsService,
    // ... other deps
  ) {}

  async createEmail(emailDto: CreateEmailDto) {
    // Use helper function for automatic scoping
    const findOptions = { where: { /* criteria */ } };
    const scopedOptions = inOrgCls(findOptions, this.cls);
    
    return this.emailRepository.save({
      ...emailDto,
      organizationId: this.cls.get("ORGANIZATION_ID")
    });
  }
}
```

#### Step 2.6: Convert ShipmentService (3 hours)

```typescript
// File: apps/portal-api/src/shipment/services/shipment.service.ts

// MOST COMPLEX - 50+ methods to update
// STRATEGY: 
// 1. Create helper method for context
// 2. Update constructor
// 3. Find/replace all this.request.user references
// 4. Test incrementally

@Injectable()
export class ShipmentService {
  constructor(
    private readonly cls: ClsService,
    // ... other deps (remove REQUEST)
  ) {}

  private getUserContext() {
    return {
      organizationId: this.cls.get("ORGANIZATION_ID"),
      permission: this.cls.get("USER_PERMISSION"),
      userId: this.cls.get("USER_ID") // if needed
    };
  }

  async getShipmentById(id: number, queryRunner?: QueryRunner) {
    const { organizationId, permission } = this.getUserContext();
    
    // Use existing patterns for scoping
    const findOptions = { where: { id } };
    if (permission !== UserPermission.BACKOFFICE_ADMIN) {
      findOptions.where.organization = { id: organizationId };
    }
    
    return this.shipmentRepository.findOne(findOptions);
  }
}
```

#### Step 2.7: Convert EntrySubmissionService (4 hours)

```typescript
// File: apps/portal-api/src/shipment/services/entry-submission.service.ts

// MOST COMPLEX - 1498 lines
// STRATEGY:
// 1. Identify all this.request usage
// 2. Create context helpers
// 3. Update constructor
// 4. Systematic replacement
// 5. Extensive testing

@Injectable()
export class EntrySubmissionService {
  constructor(
    private readonly cls: ClsService,
    // ... other deps (remove REQUEST)
  ) {}

  private getUserContext() {
    return {
      organizationId: this.cls.get("ORGANIZATION_ID"),
      permission: this.cls.get("USER_PERMISSION")
    };
  }
}
```

### Phase 3: Testing & Validation (Day 4)

#### Step 3.1: Update Existing Tests

```typescript
// Pattern for updating service tests
describe('ShipmentService', () => {
  let service: ShipmentService;
  let cls: ClsService;

  beforeEach(async () => {
    const module = await createClsTestModule([
      ShipmentService,
      // ... other providers
    ]);

    service = module.get<ShipmentService>(ShipmentService);
    cls = module.get<ClsService>(ClsService);
  });

  it('should get shipment with proper organization scoping', async () => {
    await withClsContext(cls, { organizationId: 3 }, async () => {
      const result = await service.getShipmentById(123);
      expect(result.organizationId).toBe(3);
    });
  });
});
```

#### Step 3.2: Integration Testing

```bash
# Run full test suite
npm test

# Run specific module tests
npm test -- --testPathPattern=shipment
npm test -- --testPathPattern=email

# Run e2e tests
npm run test:e2e
```

#### Step 3.3: Performance Validation

```typescript
// Create performance test
describe('Performance Comparison', () => {
  it('should show improved memory usage', async () => {
    // Before: REQUEST scope creates new instances per request
    // After: SINGLETON scope reuses instances
    
    const startMemory = process.memoryUsage();
    
    // Simulate 100 concurrent requests
    const promises = Array(100).fill(null).map(() => 
      withClsContext(cls, { organizationId: 3 }, async () => {
        return service.getShipmentById(123);
      })
    );
    
    await Promise.all(promises);
    
    const endMemory = process.memoryUsage();
    // Validate memory improvement
  });
});
```

---

## Verification Checklist

### ✅ Pre-Conversion Checklist
- [ ] CLS is configured globally in app.module.ts
- [ ] Aggregation module CLS examples work correctly
- [ ] Helper function `inOrgCls` is available and tested
- [ ] Test utilities are created

### ✅ Per-Service Conversion Checklist
- [ ] Remove `{ scope: Scope.REQUEST }` from `@Injectable`
- [ ] Replace `@Inject(REQUEST)` with `ClsService`
- [ ] Update all `this.request.user.organizationId` → `this.cls.get("ORGANIZATION_ID")`
- [ ] Update all `this.request.user.permission` → `this.cls.get("USER_PERMISSION")`
- [ ] Use `inOrgCls()` helper for query scoping where applicable
- [ ] Update service tests to use CLS context
- [ ] Verify no breaking changes to public API

### ✅ Post-Conversion Validation
- [ ] All existing tests pass
- [ ] Integration tests pass
- [ ] Performance benchmarks show improvement
- [ ] No memory leaks in concurrent usage
- [ ] Organization isolation still works correctly
- [ ] Controllers require no changes (global interceptor working)

---

## Risk Mitigation

### Risk 1: Context Loss in Async Operations
**Mitigation**: CLS uses Node.js AsyncLocalStorage which automatically preserves context through async boundaries.

**Test**:
```typescript
it('should preserve context through async operations', async () => {
  await withClsContext(cls, { organizationId: 3 }, async () => {
    const result = await service.complexAsyncOperation();
    expect(service.getOrganizationId()).toBe(3); // Should still work
  });
});
```

### Risk 2: Accidental State Storage in Services
**Mitigation**: Code review to ensure no instance variables store request-specific data.

**Anti-Pattern to Avoid**:
```typescript
// ❌ DON'T DO THIS
@Injectable()
export class BadService {
  private currentOrgId: number; // WRONG! Shared between requests
}

// ✅ DO THIS
@Injectable() 
export class GoodService {
  getCurrentOrgId(): number {
    return this.cls.get("ORGANIZATION_ID"); // Correct! Request-specific
  }
}
```

### Risk 3: Testing Complexity
**Mitigation**: Use provided CLS test utilities for consistent testing patterns.

---

## Implementation Timeline

### Day 1: Validation & Setup
- **Morning**: Verify CLS infrastructure
- **Afternoon**: Create test utilities and convert RnsProofService

### Day 2: Simple Services
- **Morning**: Convert ShipmentTrackingService and ContainerService  
- **Afternoon**: Convert GmailService and start EmailService

### Day 3: Complex Services
- **Morning**: Complete EmailService conversion
- **Afternoon**: Convert ShipmentService (largest effort)

### Day 4: Final Service & Testing
- **Morning**: Convert EntrySubmissionService
- **Afternoon**: Run full test suite and performance validation

---

## Expected Benefits

### Performance Improvements
- **Memory**: 15-25% reduction in service instance creation
- **CPU**: Reduced object instantiation overhead
- **Scalability**: Better performance under load

### Code Quality Improvements  
- **Consistency**: All modules now use same context pattern
- **Testability**: Easier to test with explicit context
- **Maintainability**: Cleaner dependency injection

### Security Improvements
- **Explicit Context**: Organization isolation is more visible
- **Auditability**: Context usage can be logged and monitored
- **Testing**: Multi-tenant scenarios easier to test

---

## Rollback Plan

If issues arise during conversion:

### Quick Rollback (per service)
```bash
# Revert specific service
git checkout HEAD~1 -- apps/portal-api/src/shipment/services/shipment.service.ts

# Restore REQUEST scope
# Add back @Inject(REQUEST) in constructor
# Revert this.cls.get() calls to this.request.user
```

### Full Rollback
```bash
# Create rollback branch before starting
git checkout -b pre-cls-conversion

# If needed, reset to this branch
git checkout main
git reset --hard pre-cls-conversion
```

---

## Success Metrics

### Technical Metrics
- [ ] All 7 services converted to SINGLETON scope
- [ ] Zero test failures
- [ ] 15%+ memory improvement in load testing  
- [ ] No performance regression in response times

### Code Quality Metrics
- [ ] Consistent CLS usage across all modules
- [ ] No REQUEST scope dependencies remaining
- [ ] Test coverage maintained or improved
- [ ] Code review approval for all changes

**This conversion represents a significant architectural improvement with minimal risk and substantial benefits.** 