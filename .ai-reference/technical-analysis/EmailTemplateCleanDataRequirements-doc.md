# Email Template Clean Data Requirements

## Overview

This document provides a clean analysis of the actual data requirements for the email template, focusing on:
1. What data the template actually needs
2. Where that data originally comes from (skipping LLM-contaminated agent-context/core-agent)
3. NestJS service scopes and related gotchas for coding agents

## Template Data Requirements

### 1. RNS (Release Notification System) Data

**Template Variables:**
- `SHOW_RNS_PROOF` - Boolean flag to show/hide RNS section
- `RNS_RESPONSE_MESSAGE` - RNS response text
- `IMPORTER_NAME` - Importer company name
- `PROCESS_DATE` - Date RNS was processed
- `RESPONSE_DATE` - Date of RNS response
- `TRANSACTION_NUMBER` - 14-digit transaction number
- `CARGO_CONTROL_NUMBER` - Cargo control number
- `PORT_CODE` - 4-digit port code
- `SUBLOCATION_CODE` - Sublocation code
- `CONTAINER_NUMBERS` - Container numbers (comma-separated)
- `PROCESSING_INDICATOR` - RNS processing indicator code

**Original Data Sources:**
- **Database**: `shipment` table (transaction_number, cargo_control_number, port_code)
- **Database**: `importer` table (company_name)  
- **Database**: `container` table (container_number)
- **External API**: Candata RNS API (process_date, response_date, processing_indicator, sublocation_code)

### 2. Document Processing Data

**Template Variables:**
- `PROCESSED_DOCUMENTS` - Array of document objects with:
  - `filename` - Document name
  - `contentType` - Document type
  - `aggregationStatus` - Status (success/failed/processing/pending)
  - `claroUrl` - URL to view document

**Original Data Sources:**
- **Database**: `document` table (id, name, status, display_status, document_type_id)
- **Database**: `document_aggregation` table (status)
- **Database**: `file` table (id, name, status)
- **URL Generation**: Frontend portal base URL + entity IDs

### 3. Validation Flags

**Template Variables:**
- `SHOW_VALIDATION_ISSUES` - Show validation section
- `CI_PL_MISSING` - Commercial Invoice/Packing List missing
- `HBL_MISSING` - House Bill of Lading missing
- `AN_EMF_MISSING` - Arrival Notice/E-Manifest missing
- `WEIGHT_MISSING` - Weight field missing
- `PORT_CODE_MISSING` - Port code missing
- `CCN_MISSING` - Cargo control number missing
- `OGD_FILING_PENDING` - Other Government Department filing pending
- `SHOW_DETAILS` - Show details section
- `SHOW_COMPLIANCE_ERRORS` - Show compliance errors

**Original Data Sources:**
- **Database**: `shipment` table (customs_status, weight, port_code, cargo_control_number)
- **Database**: `commercial_invoice` table (existence check)
- **Database**: `document` table (checking for specific document types)
- **Business Logic**: Required fields validation based on transport mode
- **Database**: `ogd_filing` table (filing status)

### 4. Message Content Variables

**Template Variables:**
- `ACKNOWLEDGE_DOCUMENTS_MESSAGE`
- `ACKNOWLEDGE_MISSING_DOCUMENTS_MESSAGE`
- `DOCUMENTATION_COMING_MESSAGE`
- `PROCESS_DOCUMENT_MESSAGE`
- `REQUEST_CAD_DOCUMENT_MESSAGE`
- `REQUEST_RNS_PROOF_MESSAGE`
- `REQUEST_RUSH_PROCESSING_MESSAGE`
- `REQUEST_MANUAL_PROCESSING_MESSAGE`
- `REQUEST_HOLD_SHIPMENT_MESSAGE`
- `UPDATE_SHIPMENT_MESSAGE`
- `ETA_RESPONSE_MESSAGE`
- `TRANSACTION_NUMBER_RESPONSE_MESSAGE`
- `RELEASE_STATUS_RESPONSE_MESSAGE`
- `SHIPPING_STATUS_RESPONSE_MESSAGE`
- `SYSTEM_ERROR_MESSAGE`
- `CONTACT_SUPPORT_MESSAGE`
- `GET_SHIPMENT_STATUS_MESSAGE`

**Original Data Sources:**
- **Static Constants**: `email-response-messages.ts` file
- **Selection Logic**: Based on customs_status and transport_mode
- **Dynamic Content**: Some messages contain Nunjucks variables (see below)

### 5. Placeholder Variables (Legacy/Unused?)

**Template Variables:**
- `CCN_PLACEHOLDER`
- `CONTAINER_PLACEHOLDER`
- `HBL_PLACEHOLDER`
- `HBL_STATUS_PLACEHOLDER`
- `AN_EMF_STATUS_PLACEHOLDER`
- `CI_PL_STATUS_PLACEHOLDER`
- `CUSTOMS_STATUS_LINE_PLACEHOLDER`
- `MISSING_DOCUMENTS_PLACEHOLDER`
- `MISSING_FIELDS_PLACEHOLDER`
- `COMPLIANCE_ERRORS_PLACEHOLDER`

**Note**: These appear to be unused placeholders - actual data should come from proper sources above.

## Nunjucks Variables in Message Constants

The `email-response-messages.ts` file contains messages with embedded Nunjucks variables:

### Date Variables
- `{{ eta | date('F j, Y') }}` - Estimated Time of Arrival formatted
- `{{ shipment.releaseDate | date('d-m-Y') }}` - Release date formatted
- `{{ shipment.releaseDate | date('F j, Y') }}` - Release date (alternative format)

### Data Variables
- `{{ TRANSACTION_NUMBER }}` - Transaction number
- `{{ SHIPPING_STATUS }}` - Shipping status text

**Original Data Sources for Nunjucks Variables:**
- **Database**: `shipment` table (eta_destination, release_date)
- **Database**: `shipment` table (transaction_number)
- **Business Logic**: Shipping status calculation

## Existing Services & Replacement Methods

### ⚠️ REQUEST Scope Services (Need Organization Context)

These services require proper request context and organization scoping:

1. **ShipmentService** (`/apps/portal-api/src/shipment/services/shipment.service.ts`)
   - **Scope**: `@Injectable({ scope: Scope.REQUEST })`
   - **Key Methods**: 
     - `getShipmentById(shipmentId, queryRunner?, relations?)` - Complete shipment with relations
     - `getShipmentByIdentifier({ hblNumber?, cargoControlNumber? })` - Find by identifier
     - `getShipmentCustomsActivities(shipmentId)` - RNS/customs events from Candata
   - **Replaces**: Direct queries to `shipment`, `container`, `location` tables
   - **Gotcha**: Uses `this.request?.user?.organization?.id` for auto-scoping

2. **ImporterService** (`/apps/portal-api/src/importer/importer.service.ts`)
   - **Scope**: `@Injectable({ scope: Scope.REQUEST })`
   - **Key Methods**: 
     - `getImporterById(importerId, queryRunner?)` - Importer with relations
     - `getImporterByReceiveEmail(receiveEmails, queryRunner?, skipOrgCheck?)` - Find by email
   - **Replaces**: Direct queries to `importer` table
   - **Gotcha**: Delegates to BaseImporterService which also needs context

3. **CandataService** (`/libraries/nest-modules/src/candata/candata.service.ts`)
   - **Scope**: `@Injectable({ scope: Scope.REQUEST })`
   - **Key Methods**: 
     - `findRnsResponseByTransactionNumbers(transactionNumbers, account?)` - RNS by transaction
     - `findRnsResponseByCargoControlNumbers(cargoControlNumbers, account?)` - RNS by CCN
     - `getCandataShipment(shipmentId, account?)` - Full Candata shipment data
   - **Replaces**: External API calls to Candata RNS system
   - **Gotcha**: Needs organization's customs broker for API credentials

### ✅ DEFAULT Scope Services (Use ClsService for Context)

These use ClsService for organization context but can be injected normally:

4. **DocumentService** (`/apps/portal-api/src/document/services/document.service.ts`)
   - **Scope**: `@Injectable()` (DEFAULT - uses ClsService)
   - **Key Methods**: 
     - `getDocuments(getDocumentsDto)` - Paginated documents with shipmentId filter
     - `getDocument(id, relations?)` - Single document with relations
   - **Replaces**: Direct queries to `document`, `file` tables
   - **Gotcha**: Uses CLS (Continuation Local Storage) for organization context

5. **AggregationService** (`/apps/portal-api/src/aggregation/aggregation.service.ts`)
   - **Scope**: `@Injectable()` (DEFAULT - uses ClsService)
   - **Key Methods**: 
     - `getAggregationByTarget(targetId, targetType)` - Aggregations by shipment
     - `getDocumentAggregation(id)` - Aggregation with full relations
   - **Replaces**: Direct queries to `document_aggregation` table
   - **Gotcha**: Complex relations including documents, batches, steps

6. **ComplianceValidationService** (`/apps/portal-api/src/shipment/services/compliance-validation.service.ts`)
   - **Scope**: `@Injectable()` (default)
   - **Key Methods**: 
     - `getShipmentCompliances(shipmentsOrIds, queryRunner?)` - Detailed compliance analysis
     - `validateShipmentCompliances(shipmentCompliances, skipFilingsValidation?)` - Validation results
   - **Replaces**: Complex compliance queries across multiple tables
   - **Note**: Pure business logic, no request dependencies

## Critical Scope-Related Issues for Coding Agents

### 1. REQUEST Scope Service Resolution

**Problem**: LLMs often try to inject REQUEST-scoped services directly or use them in DEFAULT-scoped services.

**Correct Pattern**:
```typescript
// For REQUEST-scoped services, must use ModuleRef
const contextId = ContextIdFactory.create();
this.moduleRef.registerRequestByContextId(request, contextId);
const service = await this.moduleRef.resolve(ShipmentService, contextId);
```

### 2. Organization Context

**Problem**: LLMs forget that REQUEST-scoped services auto-filter by organization.

**Key Points**:
- Services automatically use `this.request?.user?.organization?.id`
- No need to manually filter by organization in queries
- BACKOFFICE_ADMIN users bypass organization filtering

### 3. Transaction Support

**Problem**: LLMs mix transaction-capable and non-capable services.

**Transaction-Capable** (accept QueryRunner):
- ShipmentService
- ImporterService  
- DocumentService
- ComplianceValidationService

**NOT Transaction-Capable** (external APIs):
- CandataService (external API)

## Service-Based Data Retrieval Strategy

### Template Variable to Service Method Mapping

**1. RNS (Release Notification System) Data**
```typescript
// Replace: Direct database queries to shipment, importer, container tables
// Use: Service method calls with proper organization scoping

const shipment = await this.shipmentService.getShipmentById(shipmentId, queryRunner, {
  importer: true,
  containers: true,
  portOfEntry: true
});

const rnsData = await this.candataService.findRnsResponseByTransactionNumbers([
  shipment.transactionNumber
]);

// Template variables populated from:
// - IMPORTER_NAME: shipment.importer.companyName
// - TRANSACTION_NUMBER: shipment.transactionNumber
// - CARGO_CONTROL_NUMBER: shipment.cargoControlNumber
// - PORT_CODE: shipment.portOfEntry.code
// - CONTAINER_NUMBERS: shipment.containers.map(c => c.containerNumber).join(', ')
// - RNS data: rnsData[0] fields (processDate, responseDate, etc.)
```

**2. Document Processing Data**
```typescript
// Replace: Direct queries to document, document_aggregation, file tables
// Use: DocumentService and AggregationService with shipment filtering

const documents = await this.documentService.getDocuments({
  shipmentId: shipmentId,
  // Additional filters as needed
});

const aggregations = await this.aggregationService.getAggregationByTarget(
  shipmentId, 
  AggregationTargetType.SHIPMENT
);

// Template variables:
// - PROCESSED_DOCUMENTS: documents mapped with aggregation status
// - Each document: { filename, contentType, aggregationStatus, claroUrl }
```

**3. Validation and Compliance Data**
```typescript
// Replace: Complex queries across multiple tables for compliance checks
// Use: ComplianceValidationService for unified validation

const shipmentCompliances = await this.complianceValidationService.getShipmentCompliances([shipmentId]);
const validationResults = await this.complianceValidationService.validateShipmentCompliances(shipmentCompliances);

// Template variables:
// - SHOW_VALIDATION_ISSUES: boolean based on validation results
// - CI_PL_MISSING, HBL_MISSING, etc.: derived from compliance analysis
// - Missing field flags: computed from shipment compliance data
```

### What Agent-Context Should Actually Do

Based on this service-based analysis, a clean agent-context should:

1. **Orchestrate Service Calls**: Call multiple existing services to gather template data
2. **Handle Service Scoping**: Properly resolve REQUEST-scoped services using ModuleRef
3. **Transform Service Data**: Convert service responses into template-ready format
4. **Select Messages**: Choose appropriate messages based on shipment context
5. **Maintain Organization Security**: Leverage existing service organization scoping

**Key Responsibilities:**
```typescript
class CleanAgentContextService {
  // 1. Service orchestration
  async gatherTemplateData(shipmentId: number): Promise<EmailTemplateData> {
    const shipment = await this.getShipmentWithRelations(shipmentId);
    const documents = await this.getDocumentsForShipment(shipmentId);
    const rnsData = await this.getRnsDataForShipment(shipment);
    const compliance = await this.getComplianceDataForShipment(shipmentId);
    
    return this.transformToTemplateFormat(shipment, documents, rnsData, compliance);
  }

  // 2. Service scoping (for REQUEST-scoped services)
  private async resolveRequestScopedService<T>(serviceClass: Type<T>): Promise<T> {
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(this.request, contextId);
    return await this.moduleRef.resolve(serviceClass, contextId);
  }

  // 3. Data transformation only
  private transformToTemplateFormat(...): EmailTemplateData {
    // Simple transformations: dates, status enums, etc.
  }
}
```

**It should NOT:**
- Create abstract "adapters" or "providers" 
- Add layers of indirection that hide service calls
- Mix concerns (data fetching vs. message generation vs. LLM interaction)
- Duplicate business logic that exists in services
- Create its own database queries

## Clean Implementation Benefits

1. **Service Reuse**: Leverage existing, tested service methods with proper scoping
2. **Security Built-in**: Organization scoping and permission checks from services
3. **Maintainability**: Changes to business logic happen in one place (the service)
4. **Testability**: Mock services instead of database queries
5. **Transaction Support**: Services already handle QueryRunner for consistency
6. **Clear Dependencies**: Explicit service dependencies instead of hidden database access

This approach removes LLM-generated complexity and makes the data flow transparent and maintainable while properly utilizing the existing service architecture.

## ✅ PROVEN Implementation Pattern (Successfully Tested)

### Working Service Resolution Pattern for BullMQ Processors

**CRITICAL: Use the Processor Pattern (Not Direct Service Resolution)**

After extensive testing and debugging, the correct pattern for a clean agent-context service is:

```typescript
// ✅ WORKING PATTERN: Processor-based approach
// Step 1: Create REQUEST-scoped processors for each service integration

// shipment-data-gatherer.service.ts - REQUEST-scoped processor
@Injectable({ scope: Scope.REQUEST })
export class ShipmentDataGatherer {
  constructor(
    @Inject(REQUEST) private readonly request: AuthenticatedRequest,
    private readonly shipmentService: ShipmentService
  ) {}

  async gather(params: { shipmentId: number }): Promise<ShipmentTemplateData> {
    const shipment = await this.shipmentService.getShipmentById(params.shipmentId, undefined, {
      importer: true,
      containers: true,
      portOfLoading: true,
      placeOfDelivery: true
    });

    // Transform to template format
    return {
      shipment,
      IMPORTER_NAME: shipment.importer?.companyName || 'Unknown Importer',
      TRANSACTION_NUMBER: shipment.transactionNumber || '',
      CARGO_CONTROL_NUMBER: shipment.cargoControlNumber || '',
      PORT_CODE: shipment.portCode || '',
      CONTAINER_NUMBERS: this.formatContainerNumbers(shipment),
      HBL_NUMBER: shipment.hblNumber || '',
      CUSTOMS_STATUS: shipment.customsStatus || 'unknown',
      HAS_TRANSACTION_NUMBER: Boolean(shipment.transactionNumber),
      HAS_CONTAINERS: Boolean(shipment.containers && shipment.containers.length > 0)
    };
  }
}

// Step 2: Create DEFAULT-scoped orchestrator service
@Injectable() // DEFAULT scope - singleton for BullMQ processors
export class CleanAgentContextService {
  constructor(
    private readonly moduleRef: ModuleRef,
    private readonly cls: ClsService
  ) {}

  async gatherShipmentData(
    shipmentId: number,
    organizationId: number,
    request: any // Pass from BullMQ job context
  ): Promise<ShipmentTemplateData> {
    // Use ClsService for proper context isolation (like AggregationExecuter)
    return await this.cls.run(async () => {
      this.cls.set("ORGANIZATION_ID", organizationId);
      
      // Resolve the processor (not the service directly)
      const gatherer = await this.resolveRequestScopedService(ShipmentDataGatherer, request);
      
      // Use processor to gather data
      return await gatherer.gather({ shipmentId });
    });
  }

  private async resolveRequestScopedService<T>(serviceClass: Type<T>, request: any): Promise<T> {
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(request, contextId);
    const service = await this.moduleRef.resolve(serviceClass, contextId);
    await new Promise((resolve) => process.nextTick(resolve)); // Important: wait for resolution
    return service;
  }
}
```

### Module Configuration Requirements

```typescript
@Module({
  imports: [
    TypeOrmModule.forFeature([Shipment]),
    ShipmentModule.forFeature(), // Use forFeature() not forRoot()
  ],
  providers: [
    CleanAgentContextService,      // DEFAULT scope orchestrator
    ShipmentDataGatherer,          // REQUEST scope processor
  ],
  exports: [CleanAgentContextService]
})
export class CleanAgentContextModule {}
```

### Why This Pattern Works

1. **Module Boundaries**: Processors are defined in the same module, avoiding cross-module resolution issues
2. **Proper Scoping**: REQUEST-scoped processors get proper dependency injection
3. **Context Isolation**: ClsService ensures organization scoping works correctly
4. **Mirrors AggregationExecuter**: Uses the exact same pattern as proven working code
5. **Testable**: Easy to test processors independently

### ❌ What DOESN'T Work (Learned Through Testing)

```typescript
// ❌ BROKEN: Direct service resolution across module boundaries
const shipmentService = await this.moduleRef.resolve(ShipmentService, contextId);
// Error: "Nest could not find ShipmentService element"

// ❌ BROKEN: Factory providers with moduleRef.get()
{
  provide: ShipmentService,
  useFactory: (moduleRef: ModuleRef) => moduleRef.get(ShipmentService),
  // Error: "Request and transient-scoped providers can't be used with get()"
}

// ❌ BROKEN: REQUEST scope orchestrator
@Injectable({ scope: Scope.REQUEST })
export class CleanAgentContextService {
  // This creates circular dependency issues in BullMQ processors
}
```

### Key Implementation Notes

1. **Service Resolution Pattern**: Use processor pattern, not direct service resolution
2. **Organization Scoping**: ClsService + proper request context ensures organization filtering
3. **Error Handling**: Services have built-in error handling and fallbacks
4. **Performance**: Use service relations/includes to avoid N+1 queries
5. **Security**: Leverage existing service permission checks and organization boundaries
6. **BullMQ Integration**: Works perfectly in BullMQ job processors with mock request objects

This pattern has been **successfully tested and proven to work** with actual ShipmentService integration.