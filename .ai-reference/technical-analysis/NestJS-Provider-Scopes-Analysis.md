# NestJS Provider Scopes Analysis

## Overview

This document analyzes the **Provider Scope** (injection scope) of all services in the Shipment and Email modules. In NestJS, providers can have three scopes:

- **Singleton** (DEFAULT) - One instance shared across the entire application
- **Request** - New instance created for each HTTP request
- **Transient** - New instance created each time the provider is injected

## Analysis Methodology

Each service is examined for:
1. `@Injectable({ scope: Scope.XXX })` decorator configuration
2. Dependencies that might force scope inheritance
3. Default behavior (Singleton if no scope specified)

---

## Shipment Module Services

### Core Services

| Service | Scope | Configuration | Notes |
|---------|-------|---------------|-------|
| ShipmentService | **Request** | `@Injectable({ scope: Scope.REQUEST })` | Requires user context for authorization |
| ContainerService | **Request** | `@Injectable({ scope: Scope.REQUEST })` | Requires user context for authorization |
| EntrySubmissionService | **Request** | `@Injectable({ scope: Scope.REQUEST })` | Requires user context for operations |
| ComplianceValidationService | **Singleton** | `@Injectable()` | Stateless validation logic |
| ShipmentComplianceQueryService | **Singleton** | `@Injectable()` | Query service, no state |
| ShipmentDeletionService | **Singleton** | `@Injectable()` | Utility service for deletion |
| CustomStatusService | **Singleton** | `@Injectable()` | Status checking logic |
| ShipmentCommercialInvoiceService | **Singleton** | `@Injectable()` | Data aggregation service |

### Email Senders

| Service | Scope | Configuration | Notes |
|---------|-------|---------------|-------|
| LiveShipmentEmailSender | **Singleton** | `@Injectable()` | Email notification service |
| LiveEntryUploadFailedEmailSender | **Singleton** | `@Injectable()` | Error notification service |
| RNSStatusChangeEmailSender | **Singleton** | `@Injectable()` | Status change notifications |
| EntryNotAcceptedWarningEmailSender | **Singleton** | `@Injectable()` | Warning notifications |
| CustomsStatusCheckErrorEmailSender | **Singleton** | `@Injectable()` | Error notifications |
| AccountingNotCompletedWarningEmailSender | **Singleton** | `@Injectable()` | Warning notifications |

### Enrichers & Processors

| Service | Scope | Configuration | Notes |
|---------|-------|---------------|-------|
| ShipmentEnricher | **Singleton** | `@Injectable()` | Data enrichment logic |
| UpdateEntryProcessor | **Singleton** | `@Injectable()` | Queue processor |

---

## Email Module Services

### Core Services

| Service | Scope | Configuration | Notes |
|---------|-------|---------------|-------|
| EmailService | **Request** | `@Injectable({ scope: Scope.REQUEST })` | Requires user context for authorization |
| GmailService | **Request** | `@Injectable({ scope: Scope.REQUEST })` | User-specific Gmail operations |
| RnsProofService | **Request** | `@Injectable({ scope: Scope.REQUEST })` | User context required |
| EmailUpdateService | **Singleton** | `@Injectable()` | Data update operations |
| EmailDeletionService | **Singleton** | `@Injectable()` | Utility service for deletion |
| TestEmailService | **Singleton** | `@Injectable()` | Testing utilities |

### Processors

| Service | Scope | Configuration | Notes |
|---------|-------|---------------|-------|
| GetGmailMessageProcessor | **Singleton** | `@Injectable()` | Queue processor |

---

## Shipment Tracking Module Services

| Service | Scope | Configuration | Notes |
|---------|-------|---------------|-------|
| ShipmentTrackingService | **Request** | `@Injectable({ scope: Scope.REQUEST })` | User context required for tracking |

---

## Analysis Details

### Scope Distribution Summary

| Scope | Total Services | Percentage |
|-------|----------------|------------|
| **Request** | 7 | 35% |
| **Singleton** | 13 | 65% |
| **Transient** | 0 | 0% |

### Request-Scoped Services Pattern

Services with Request scope follow a clear pattern - they all require user authentication context:

```typescript
@Injectable({ scope: Scope.REQUEST })
export class ServiceName {
  constructor(
    @Inject(REQUEST) private readonly request: AuthenticatedRequest,
    // ... other dependencies
  ) {}
}
```

**Request-Scoped Services:**
- **ShipmentService** - Core CRUD operations requiring user permissions
- **ContainerService** - Container operations with authorization checks  
- **EntrySubmissionService** - Entry submission requiring user context
- **EmailService** - Email operations requiring user context
- **GmailService** - User-specific Gmail token access
- **RnsProofService** - User context for proof generation
- **ShipmentTrackingService** - User-specific tracking operations

### Singleton-Scoped Services Pattern

Most services use the default Singleton scope, suitable for:
- Stateless business logic
- Utility functions
- Email notifications
- Data enrichment
- Queue processors

### Key Architectural Insights

1. **Authorization Pattern**: Services requiring user context are consistently REQUEST-scoped
2. **Performance Optimization**: Stateless services remain Singleton for efficiency
3. **Queue Processing**: All queue processors are Singleton (stateless background jobs)
4. **Email Notifications**: All email senders are Singleton (no user-specific state needed)

### Scope Inheritance Considerations

When a Request-scoped service injects a Singleton service, the injection works normally. However, if a Singleton service tries to inject a Request-scoped service, it creates a circular dependency issue unless properly handled with forwardRef() or lazy injection patterns.

**Example from codebase:**
```typescript
// ContainerService (REQUEST) → ShipmentService (REQUEST) ✅
@Inject(forwardRef(() => ShipmentService))
private readonly shipmentService: ShipmentService
```

### Memory and Performance Impact

- **Request-scoped services**: New instance per HTTP request (7 services × request volume)
- **Singleton services**: One instance shared across all requests (13 services)
- **Total memory footprint**: Moderate, with Request scope limited to user-facing operations

### Recommendations

1. **Current scope allocation is appropriate** - follows NestJS best practices
2. **Request scope is correctly used** for user-context dependent services
3. **Singleton scope is efficient** for stateless operations
4. **No changes needed** - the architecture is well-designed for the use case 