# Service Scope Refactoring Analysis: REQUEST → SINGLETON

## Overview

This document analyzes the feasibility, benefits, and risks of converting REQUEST-scoped services to SINGLETON by introducing an explicit context-passing layer while maintaining authentication at the controller level.

## Current Architecture Pattern

### REQUEST-Scoped Services (Current)
```typescript
@Injectable({ scope: Scope.REQUEST })
export class ShipmentService {
  constructor(
    @Inject(REQUEST) private readonly request: AuthenticatedRequest,
    // ... other dependencies
  ) {}

  async getShipmentById(id: number) {
    // Implicit access to user context
    const orgId = this.request.user.organizationId;
    const permissions = this.request.user.permissions;
    
    // Use context for authorization checks
    return this.findShipment(id, orgId, permissions);
  }
}
```

## Proposed Architecture Pattern

### SINGLETON Services with Explicit Context

```typescript
// 1. Define explicit context interface
interface UserContext {
  userId: number;
  organizationId: number;
  permissions: UserPermission[];
  email: string;
}

// 2. Convert service to SINGLETON with explicit context
@Injectable() // Default SINGLETON scope
export class ShipmentService {
  constructor(
    // Remove REQUEST injection
    private readonly shipmentRepository: Repository<Shipment>,
    // ... other dependencies (no REQUEST)
  ) {}

  async getShipmentById(id: number, context: UserContext) {
    // Explicit context usage
    const { organizationId, permissions } = context;
    
    // Same authorization logic, but explicit
    return this.findShipment(id, organizationId, permissions);
  }
}

// 3. Create context extraction layer
@Injectable()
export class UserContextExtractor {
  extractContext(request: AuthenticatedRequest): UserContext {
    return {
      userId: request.user.id,
      organizationId: request.user.organizationId,
      permissions: request.user.permissions,
      email: request.user.email
    };
  }
}

// 4. Update controller to pass context
@Controller('shipments')
export class ShipmentController {
  constructor(
    private readonly shipmentService: ShipmentService,
    private readonly contextExtractor: UserContextExtractor
  ) {}

  @Get(':id')
  async getShipment(@Param('id') id: number, @Req() request: AuthenticatedRequest) {
    const context = this.contextExtractor.extractContext(request);
    return this.shipmentService.getShipmentById(id, context);
  }
}
```

---

## Feasibility Analysis

### Implementation Complexity: **Medium-High**

| Service | Refactoring Effort | Risk Level | Notes |
|---------|-------------------|------------|-------|
| ShipmentService | High | Medium | 50+ methods need context parameter |
| ContainerService | Medium | Low | Fewer methods, simpler logic |
| EntrySubmissionService | High | High | Complex workflows, multiple context uses |
| EmailService | Medium | Medium | Email context needs careful handling |
| GmailService | High | High | OAuth tokens tied to user context |
| RnsProofService | Low | Low | Simple service, few methods |
| ShipmentTrackingService | Medium | Low | Straightforward tracking operations |

### Breaking Changes Required

```typescript
// BEFORE: All these method signatures would change
await shipmentService.getShipmentById(123);
await containerService.getContainerById(456, 789);
await emailService.sendNotification(email);

// AFTER: Context must be passed explicitly
await shipmentService.getShipmentById(123, context);
await containerService.getContainerById(456, 789, context);
await emailService.sendNotification(email, context);
```

---

## Benefits Analysis

### 1. Performance Improvements

```typescript
// Current: New instances per request
// 7 REQUEST-scoped services × 1000 requests = 7000 service instances

// Proposed: Shared instances
// 7 SINGLETON services × 1000 requests = 7 service instances
// Estimated 15-25% reduction in memory usage
```

### 2. Enhanced Testability

```typescript
// BEFORE: Hard to test - requires REQUEST mock
const mockRequest = { user: { organizationId: 3 } };
// Complex setup...

// AFTER: Easy to test - pure function
const context = { organizationId: 3, permissions: [...] };
const result = await shipmentService.getShipmentById(123, context);
// Simple, explicit testing
```

### 3. Better Tenancy Security

```typescript
// EXPLICIT context makes tenancy violations obvious
class ShipmentService {
  async getShipmentById(id: number, context: UserContext) {
    // ✅ VISIBLE: We can see exactly what context is being used
    // ✅ AUDITABLE: Easy to log context for security audits
    // ✅ TESTABLE: Can test with different org contexts
    
    if (!this.hasPermission(context.permissions, 'READ_SHIPMENT')) {
      throw new ForbiddenException();
    }
    
    return this.findShipmentByIdAndOrg(id, context.organizationId);
  }
}
```

---

## Risks and Mitigation Strategies

### 1. Context Leakage Risk
**Risk**: Accidentally passing wrong organization context

**Mitigation**:
```typescript
// Type-safe context with validation
interface ValidatedUserContext extends UserContext {
  readonly _validated: unique symbol;
}

@Injectable()
export class ContextValidator {
  validate(request: AuthenticatedRequest): ValidatedUserContext {
    const context = this.extractContext(request);
    
    // Validation logic
    if (!context.organizationId) {
      throw new UnauthorizedException('Invalid organization context');
    }
    
    return context as ValidatedUserContext;
  }
}
```

### 2. Method Signature Explosion
**Risk**: Every method needs context parameter

**Mitigation**:
```typescript
// Create base service with context handling
abstract class ContextualService {
  protected validateOrgAccess(resourceOrgId: number, context: UserContext) {
    if (resourceOrgId !== context.organizationId) {
      throw new ForbiddenException('Cross-organization access denied');
    }
  }
}

class ShipmentService extends ContextualService {
  async getShipmentById(id: number, context: UserContext) {
    const shipment = await this.shipmentRepository.findOne({ where: { id } });
    this.validateOrgAccess(shipment.organizationId, context);
    return shipment;
  }
}
```

### 3. Thread Safety
**Risk**: Singleton services accidentally storing state

**Mitigation**:
```typescript
// ✅ GOOD: Stateless service
@Injectable()
export class ShipmentService {
  async getShipmentById(id: number, context: UserContext) {
    // No instance variables modified
    return this.repository.findOne(...);
  }
}

// ❌ BAD: Storing context as instance variable
@Injectable()
export class ShipmentService {
  private currentContext: UserContext; // DON'T DO THIS!
}
```

---

## Implementation Strategy

### Phase 1: Foundation (1-2 weeks)
1. Create `UserContext` interface and related types
2. Build `UserContextExtractor` service
3. Create base classes for contextual services
4. Add comprehensive type safety

### Phase 2: Service Migration (4-6 weeks)
1. Start with low-risk services (RnsProofService, ShipmentTrackingService)
2. Migrate medium-complexity services (ContainerService, EmailService)
3. Handle high-complexity services last (ShipmentService, EntrySubmissionService)

### Phase 3: Testing & Validation (2-3 weeks)
1. Comprehensive integration testing
2. Performance benchmarking
3. Security audit of context handling
4. Load testing

---

## Recommendation: **PROCEED WITH CAUTION**

### ✅ Proceed If:
- You have 6-8 weeks for careful implementation
- Strong TypeScript/testing discipline on team
- Performance is a significant concern
- You want better testability

### ❌ Avoid If:
- Under time pressure for delivery
- Team lacks TypeScript expertise
- Current performance is acceptable
- Risk tolerance is low

### Alternative: Hybrid Approach
Consider keeping high-risk services (EmailService, GmailService) as REQUEST-scoped while converting safer ones (ShipmentService, ContainerService) to SINGLETON first.

---

## Tenancy Impact: **POSITIVE**

This refactoring would actually **strengthen** tenancy security by:
1. Making context usage explicit and auditable
2. Enabling better testing of cross-tenant scenarios
3. Reducing accidental context sharing between requests
4. Making security boundaries more visible in code

The key is disciplined implementation with strong type safety and validation. 