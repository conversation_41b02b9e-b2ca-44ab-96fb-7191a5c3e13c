# Clean Agent Context - Next Service Implementation Prompt

## Mission Overview

You are continuing the implementation of a **Clean Agent Context Service** that gathers template data for email generation in the Claro customs automation platform. The first phase (ShipmentService integration) has been **successfully completed and tested**. Your task is to implement the next service integration following the proven pattern.

## 🎯 Current Status - What's Already Working

### ✅ Successfully Implemented & Tested:
- **ShipmentDataGatherer**: REQUEST-scoped processor that integrates with ShipmentService
- **CleanAgentContextService**: DEFAULT-scoped orchestrator that uses ModuleRef pattern
- **Test Infrastructure**: Working test harness with database queries and mock request creation
- **Module Configuration**: Proper imports using `ShipmentModule.forFeature()`

### 📊 Test Results (ShipmentService):
- **Status**: ✅ SUCCESS
- **Duration**: ~15ms  
- **Data Retrieved**: Complete shipment with importer, containers, port info
- **Template Variables**: 10+ variables successfully populated (IMPORTER_NAME, TRANSACTION_NUMBER, etc.)

## 📋 Your Task - Implement Next Service Integration

Choose **ONE** of the following services to implement next (in order of priority):

### Priority 1: DocumentService Integration
**Purpose**: Gather document processing data for email templates
**Service**: `/apps/portal-api/src/document/services/document.service.ts`
**Scope**: `@Injectable()` (DEFAULT - uses ClsService)
**Key Methods**: 
- `getDocuments(getDocumentsDto)` - Get documents filtered by shipmentId
- `getDocument(id, relations?)` - Single document with relations

**Template Variables to Populate**:
```typescript
interface DocumentTemplateData {
  PROCESSED_DOCUMENTS: Array<{
    filename: string;
    contentType: string;
    aggregationStatus: 'success' | 'failed' | 'processing' | 'pending';
    claroUrl: string;
  }>;
  SHOW_VALIDATION_ISSUES: boolean;
  CI_PL_MISSING: boolean;  // Commercial Invoice/Packing List missing
  HBL_MISSING: boolean;    // House Bill of Lading missing  
  AN_EMF_MISSING: boolean; // Arrival Notice/E-Manifest missing
}
```

### Priority 2: CandataService Integration (RNS Data)
**Purpose**: Gather RNS (Release Notification System) data from external API
**Service**: `/libraries/nest-modules/src/candata/candata.service.ts` 
**Scope**: `@Injectable({ scope: Scope.REQUEST })` (REQUEST-scoped)
**Key Methods**:
- `findRnsResponseByTransactionNumbers(transactionNumbers, account?)`
- `getCandataShipment(shipmentId, account?)`

**Template Variables to Populate**:
```typescript
interface RnsTemplateData {
  SHOW_RNS_PROOF: boolean;
  RNS_RESPONSE_MESSAGE: string;
  PROCESS_DATE: string;
  RESPONSE_DATE: string;
  PROCESSING_INDICATOR: string;
  SUBLOCATION_CODE: string;
}
```

### Priority 3: ComplianceValidationService Integration
**Purpose**: Gather validation and compliance data
**Service**: `/apps/portal-api/src/shipment/services/compliance-validation.service.ts`
**Scope**: `@Injectable()` (DEFAULT)
**Key Methods**:
- `getShipmentCompliances(shipmentsOrIds, queryRunner?)`
- `validateShipmentCompliances(shipmentCompliances, skipFilingsValidation?)`

## 🏗️ CRITICAL - Use the Proven Implementation Pattern

**⚠️ MUST READ FIRST**: `/home/<USER>/dev/Claro/.ai-reference/technical-analysis/EmailTemplateCleanDataRequirements-doc.md`

The document contains the **proven implementation pattern** that was successfully tested. Follow this pattern exactly:

### Step 1: Create REQUEST-Scoped Processor (if needed)
```typescript
// For REQUEST-scoped services like CandataService
@Injectable({ scope: Scope.REQUEST })
export class CandataDataGatherer {
  constructor(
    @Inject(REQUEST) private readonly request: AuthenticatedRequest,
    private readonly candataService: CandataService
  ) {}

  async gather(params: { shipment: Shipment }): Promise<RnsTemplateData> {
    // Implementation here
  }
}
```

### Step 2: Create DEFAULT-Scoped Processor (if service is DEFAULT)
```typescript
// For DEFAULT-scoped services like DocumentService
@Injectable()
export class DocumentDataGatherer {
  constructor(
    private readonly documentService: DocumentService,
    private readonly aggregationService: AggregationService
  ) {}

  async gather(params: { shipmentId: number }): Promise<DocumentTemplateData> {
    // Implementation here
  }
}
```

### Step 3: Update Module Configuration
Add your new processor to `CleanAgentContextModule`:
```typescript
@Module({
  imports: [
    TypeOrmModule.forFeature([Shipment]),
    ShipmentModule.forFeature(),
    DocumentModule,        // If using DocumentService
    // Add other modules as needed
  ],
  providers: [
    CleanAgentContextService,
    ShipmentDataGatherer,    // Existing
    DocumentDataGatherer,    // New
    // Add your new processor
  ],
  exports: [CleanAgentContextService]
})
```

### Step 4: Update CleanAgentContextService
Add a new method following the existing pattern:
```typescript
async gatherDocumentData(
  shipmentId: number,
  organizationId: number,
  request: any
): Promise<DocumentTemplateData> {
  return await this.cls.run(async () => {
    this.cls.set("ORGANIZATION_ID", organizationId);
    
    // For DEFAULT-scoped services - inject directly
    const gatherer = this.documentDataGatherer;
    
    // For REQUEST-scoped services - resolve via ModuleRef
    // const gatherer = await this.resolveRequestScopedService(CandataDataGatherer, request);
    
    return await gatherer.gather({ shipmentId });
  });
}
```

### Step 5: Update Types File
Add your new interface to `clean-agent-context.types.ts`:
```typescript
export interface DocumentTemplateData {
  // Your interface definition
}
```

### Step 6: Update Test Script
Extend the existing test to include your new service:
```typescript
// Test both services
const shipmentData = await cleanAgentContext.gatherShipmentData(shipment.id, config.organizationId, mockRequest);
const documentData = await cleanAgentContext.gatherDocumentData(shipment.id, config.organizationId, mockRequest);
```

## 🚨 Critical Requirements

### ❌ What NOT to Do:
1. **Don't create abstract layers** - Use services directly
2. **Don't try to resolve services across module boundaries** - Use the processor pattern
3. **Don't use `moduleRef.get()` with REQUEST-scoped services** - Use `moduleRef.resolve()`
4. **Don't make the orchestrator REQUEST-scoped** - Keep it DEFAULT for BullMQ
5. **Don't create new database queries** - Use existing service methods

### ✅ What TO Do:
1. **Follow the exact pattern** from the proven implementation
2. **Use ClsService for context isolation** like AggregationExecuter
3. **Create processors in the same module** to avoid resolution issues
4. **Use service relations/includes** to avoid N+1 queries
5. **Test your implementation** using the existing test harness

## 📂 Key Files to Work With

### Implementation Files:
- `/home/<USER>/dev/Claro/apps/portal-api/src/clean-agent-context/clean-agent-context.service.ts`
- `/home/<USER>/dev/Claro/apps/portal-api/src/clean-agent-context/clean-agent-context.module.ts`
- `/home/<USER>/dev/Claro/apps/portal-api/src/clean-agent-context/clean-agent-context.types.ts`
- `/home/<USER>/dev/Claro/apps/portal-api/src/clean-agent-context/[new-service]-data-gatherer.service.ts` (create new)

### Test Files:
- `/home/<USER>/dev/Claro/apps/portal-api/src/clean-agent-context/testing/test-clean-agent-context.js`

### Reference Files:
- `/home/<USER>/dev/Claro/apps/portal-api/src/clean-agent-context/shipment-data-gatherer.service.ts` (working example)
- `/home/<USER>/dev/Claro/apps/portal-api/src/aggregation/executer/aggregation.executer.ts` (reference pattern)

## 🎯 Success Criteria

1. **Service Integration**: Successfully integrate chosen service using processor pattern
2. **Template Data**: Populate appropriate template variables with real data
3. **Testing**: Test passes with ✅ SUCCESS status and shows populated data
4. **Pattern Consistency**: Follow the exact same pattern as ShipmentDataGatherer
5. **No Errors**: No module resolution or dependency injection errors
6. **Performance**: Reasonable response time (< 50ms for simple data)

## 📝 Implementation Steps

1. **Choose Priority Service**: Start with DocumentService (DEFAULT scope - easier)
2. **Read Documentation**: Study the requirements doc and existing implementation
3. **Create Processor**: Following the proven pattern exactly
4. **Update Module**: Add imports and providers
5. **Update Service**: Add new method to orchestrator
6. **Update Types**: Add interface definitions
7. **Test Implementation**: Run tests and verify success
8. **Document Results**: Update test results and verify data population

## 🔍 Testing Your Implementation

Use the existing test harness:
```bash
cd /home/<USER>/dev/Claro/apps/portal-api
npm run build
node src/clean-agent-context/testing/test-clean-agent-context.js --verbose
```

Look for:
- ✅ SUCCESS status
- Populated template variables in test results
- No dependency injection errors
- Reasonable performance metrics

## 📞 Support References

- **Requirements Document**: `/home/<USER>/dev/Claro/.ai-reference/technical-analysis/EmailTemplateCleanDataRequirements-doc.md`
- **Working Example**: `ShipmentDataGatherer` service and `CleanAgentContextService.gatherShipmentData()` method
- **Service Documentation**: Comprehensive documentation exists for all target services
- **Testing Infrastructure**: Complete test harness with database access ready to use

**Remember**: This is an incremental implementation. Focus on ONE service integration, test it thoroughly, then move to the next. The pattern is proven to work - just follow it exactly!