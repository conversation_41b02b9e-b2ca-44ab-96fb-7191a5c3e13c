#!/usr/bin/env python3
import json
import re

# Migration data from the TypeScript file
migration_data = [
    {
        "tariffFlagCode": "EP1",
        "agencyCode": "EPA",
        "requirementLevel": "M",
        "programCode": ["ODS"],
        "description": "Ozone Depleting Substances specific data may be required"
    },
    {
        "tariffFlagCode": "EP2",
        "agencyCode": "EPA",
        "requirementLevel": "R",
        "programCode": ["ODS"],
        "description": "Ozone Depleting Substances specific data is required"
    },
    {
        "tariffFlagCode": "EP3",
        "agencyCode": "EPA",
        "requirementLevel": "M",
        "programCode": ["VNE"],
        "description": "Vehicle and Engines specific data may be required"
    },
    {
        "tariffFlagCode": "EP4",
        "agencyCode": "EPA",
        "requirementLevel": "R",
        "programCode": ["VNE"],
        "description": "Vehicle and Engines specific data is required"
    },
    {
        "tariffFlagCode": "EP5",
        "agencyCode": "EPA",
        "requirementLevel": "M",
        "programCode": ["PS1", "PS2", "PS3"],
        "description": "Pesticides specific data may be required"
    },
    {
        "tariffFlagCode": "EP6",
        "agencyCode": "EPA",
        "requirementLevel": "R",
        "programCode": ["PS1", "PS2", "PS3"],
        "description": "Pesticides specific data is required"
    },
    {
        "tariffFlagCode": "EP7",
        "agencyCode": "EPA",
        "requirementLevel": "M",
        "programCode": ["TS1"],
        "description": "Toxic Substances Control Act specific data may be required"
    },
    {
        "tariffFlagCode": "EP8",
        "agencyCode": "EPA",
        "requirementLevel": "R",
        "programCode": ["TS1"],
        "description": "Toxic Substances Control Act specific data is required"
    },
    {
        "tariffFlagCode": "EH1",
        "agencyCode": "EPA",
        "requirementLevel": "M",
        "programCode": ["HFC"],
        "description": "EPA Hydrofluorocarbons data may be required. Only disclaim A allowed"
    },
    {
        "tariffFlagCode": "EH2",
        "agencyCode": "EPA",
        "requirementLevel": "R",
        "programCode": ["HFC"],
        "description": "EPA Hydrofluorocarbons data is required"
    },
    {
        "tariffFlagCode": "FS3",
        "agencyCode": "FSI",
        "requirementLevel": "M",
        "programCode": ["FSI"],
        "description": "FSIS data may be required. Applicable to all FSIS programs"
    },
    {
        "tariffFlagCode": "FS4",
        "agencyCode": "FSI",
        "requirementLevel": "R",
        "programCode": ["FSI"],
        "description": "FSIS data is required. Applicable to all FSIS programs"
    },
    {
        "tariffFlagCode": "NM1",
        "agencyCode": "NMF",
        "requirementLevel": "M",
        "programCode": ["370"],
        "description": "370 specific data may be required"
    },
    {
        "tariffFlagCode": "NM2",
        "agencyCode": "NMF",
        "requirementLevel": "R",
        "programCode": ["370"],
        "description": "370 specific data is required"
    },
    {
        "tariffFlagCode": "NM3",
        "agencyCode": "NMF",
        "requirementLevel": "M",
        "programCode": ["AMR"],
        "description": "Antarctic Marine Living Resources specific data may be required"
    },
    {
        "tariffFlagCode": "NM4",
        "agencyCode": "NMF",
        "requirementLevel": "R",
        "programCode": ["AMR"],
        "description": "Antarctic Marine Living Resources specific data is required"
    },
    {
        "tariffFlagCode": "NM5",
        "agencyCode": "NMF",
        "requirementLevel": "M",
        "programCode": ["HMS"],
        "description": "Highly Migratory Species specific data may be required"
    },
    {
        "tariffFlagCode": "NM6",
        "agencyCode": "NMF",
        "requirementLevel": "R",
        "programCode": ["HMS"],
        "description": "Highly Migratory Species specific data is required"
    },
    {
        "tariffFlagCode": "NM8",
        "agencyCode": "NMF",
        "requirementLevel": "R",
        "programCode": ["SIM"],
        "description": "Seafood Import Monitoring Program specific data is required"
    },
    {
        "tariffFlagCode": "DT1",
        "agencyCode": "NHT",
        "requirementLevel": "M",
        "programCode": ["MVS", "REI", "TPE", "OEI", "OFF"],
        "description": "DOT/National Highway Traffic Safety Administration HS-7 data may be required"
    },
    {
        "tariffFlagCode": "DT2",
        "agencyCode": "NHT",
        "requirementLevel": "R",
        "programCode": ["MVS", "REI", "TPE", "OEI", "OFF"],
        "description": "DOT/National Highway Traffic Safety Administration HS-7 data is required"
    },
    {
        "tariffFlagCode": "AL1",
        "agencyCode": "APH",
        "requirementLevel": "M",
        "programCode": ["APL"],
        "description": "Lacey Act specific data may be required"
    },
    {
        "tariffFlagCode": "AL2",
        "agencyCode": "APH",
        "requirementLevel": "R",
        "programCode": ["APL"],
        "description": "Lacey Act specific data is required"
    },
    {
        "tariffFlagCode": "FD1",
        "agencyCode": "FDA",
        "requirementLevel": "M",
        "programCode": ["BIO", "COS", "DEV", "DRU", "FOO", "RAD", "TOB", "VME"],
        "description": "FDA data may be required 801(a)"
    },
    {
        "tariffFlagCode": "FD2",
        "agencyCode": "FDA",
        "requirementLevel": "R",
        "programCode": ["BIO", "COS", "DEV", "DRU", "FOO", "RAD", "TOB", "VME"],
        "description": "FDA data Required 801(a)"
    },
    {
        "tariffFlagCode": "FD3",
        "agencyCode": "FDA",
        "requirementLevel": "M",
        "programCode": ["BIO", "COS", "DEV", "DRU", "FOO", "RAD", "TOB", "VME"],
        "description": "FDA Prior Notice Data may be required 801(m)"
    },
    {
        "tariffFlagCode": "FD4",
        "agencyCode": "FDA",
        "requirementLevel": "R",
        "programCode": ["BIO", "COS", "DEV", "DRU", "FOO", "RAD", "TOB", "VME"],
        "description": "FDA Prior Notice Data is required 801(m)"
    },
    {
        "tariffFlagCode": "AM1",
        "agencyCode": "AMS",
        "requirementLevel": "M",
        "programCode": ["EG"],
        "description": "USDA/Agricultural Marketing Service Data related to egg products may be required"
    },
    {
        "tariffFlagCode": "AM2",
        "agencyCode": "AMS",
        "requirementLevel": "R",
        "programCode": ["EG"],
        "description": "USDA/Agricultural Marketing Service Data Related to shell eggs is required"
    },
    {
        "tariffFlagCode": "AM3",
        "agencyCode": "AMS",
        "requirementLevel": "M",
        "programCode": ["MO"],
        "description": "USDA/Agricultural Marketing Service Data Related to marketing orders may be required"
    },
    {
        "tariffFlagCode": "AM4",
        "agencyCode": "AMS",
        "requirementLevel": "R",
        "programCode": ["MO"],
        "description": "USDA/Agricultural Marketing Service Data Related to marketing orders is required"
    },
    {
        "tariffFlagCode": "AM6",
        "agencyCode": "AMS",
        "requirementLevel": "R",
        "programCode": ["PN"],
        "description": "USDA/Agriculture Marketing Service Data related to peanuts is required"
    },
    {
        "tariffFlagCode": "AM7",
        "agencyCode": "AMS",
        "requirementLevel": "M",
        "programCode": ["OR"],
        "description": "USDA/Agriculture Marketing Service Data related to organics may be required"
    },
    {
        "tariffFlagCode": "AM8",
        "agencyCode": "AMS",
        "requirementLevel": "R",
        "programCode": ["OR"],
        "description": "USDA/Agriculture Marketing Service Data related to organics is required"
    },
    {
        "tariffFlagCode": "TB1",
        "agencyCode": "TTB",
        "requirementLevel": "M",
        "programCode": ["BER", "WIN", "DSP", "TOB"],
        "description": "TTB data may be required. Applicable to all TTB programs."
    },
    {
        "tariffFlagCode": "TB2",
        "agencyCode": "TTB",
        "requirementLevel": "R",
        "programCode": ["BER", "WIN", "DSP", "TOB"],
        "description": "TTB data is required. Applicable to all TTB programs."
    },
    {
        "tariffFlagCode": "TB3",
        "agencyCode": "TTB",
        "requirementLevel": "M",
        "programCode": ["BER", "WIN", "DSP", "TOB"],
        "description": "TTB data may be required, which can only be disclaimed using codes A or C"
    },
    {
        "tariffFlagCode": "AQ1",
        "agencyCode": "APH",
        "requirementLevel": "M",
        "programCode": ["AAC", "APQ", "AVS", "ABS"],
        "description": "APHIS data may be required"
    },
    {
        "tariffFlagCode": "AQ2",
        "agencyCode": "APH",
        "requirementLevel": "R",
        "programCode": ["AAC", "APQ", "AVS", "ABS"],
        "description": "APHIS data is required"
    },
    {
        "tariffFlagCode": "AQX",
        "agencyCode": "APH",
        "requirementLevel": "M",
        "programCode": ["AAC", "APQ", "AVS", "ABS"],
        "description": "APHIS Data May be required (no disclaim required)"
    },
    {
        "tariffFlagCode": "OM1",
        "agencyCode": "OMC",
        "requirementLevel": "M",
        "programCode": ["OMC"],
        "description": "U.S. Department of State, Bureau of Oceans and International Environmental and Scientific Affairs, Office of Marine Conservation data may be required"
    },
    {
        "tariffFlagCode": "OM2",
        "agencyCode": "OMC",
        "requirementLevel": "R",
        "programCode": ["OMC"],
        "description": "U.S. Department of State, Bureau of Oceans and International Environmental and Scientific Affairs, Office of Marine Conservation data is required"
    },
    {
        "tariffFlagCode": "FW1",
        "agencyCode": "FWS",
        "requirementLevel": "M",
        "programCode": ["FWS"],
        "description": "U.S. Fish and Wildlife Service data may be required, which can only be disclaimed using codes C, D or E"
    },
    {
        "tariffFlagCode": "FW2",
        "agencyCode": "FWS",
        "requirementLevel": "R",
        "programCode": ["FWS"],
        "description": "U.S. Fish and Wildlife Service data is required"
    },
    {
        "tariffFlagCode": "FW3",
        "agencyCode": "FWS",
        "requirementLevel": "M",
        "programCode": ["FWS"],
        "description": "U.S. Fish and Wildlife Service data may be required, which can only be disclaimed using code C or D"
    },
    {
        "tariffFlagCode": "DE1",
        "agencyCode": "DEA",
        "requirementLevel": "M",
        "programCode": ["DEA"],
        "description": "U.S. Drug Enforcement Administration data may be required"
    }
]

def parse_pdf_data(text_file_path):
    """Parse the PDF text file and extract structured data"""
    with open(text_file_path, 'r') as f:
        content = f.read()
    
    lines = content.split('\n')
    
    # Extract the data section (after the header)
    data_section = []
    in_data_section = False
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Look for the start of data sections
        if line.startswith('EP1') or line.startswith('DT1') or line.startswith('FD4') or line.startswith('AQX'):
            in_data_section = True
        
        if in_data_section:
            data_section.append(line)
    
    # Parse the structured data
    pdf_data = []
    
    # Known codes to look for
    codes = ['EP1', 'EP2', 'EP3', 'EP4', 'EP5', 'EP6', 'EP7', 'EP8', 'EH1', 'EH2',
             'FS3', 'FS4', 'NM1', 'NM2', 'NM3', 'NM4', 'NM5', 'NM6', 'NM8',
             'DT1', 'DT2', 'AL1', 'AL2', 'FD1', 'FD2', 'FD3', 'FD4',
             'AM1', 'AM2', 'AM3', 'AM4', 'AM6', 'AM7', 'AM8',
             'TB1', 'TB2', 'TB3', 'AQ1', 'AQ2', 'AQX', 'OM1', 'OM2',
             'FW1', 'FW2', 'FW3', 'DE1']
    
    # Build a map of descriptions from the text
    descriptions = {
        'EP1': 'Ozone Depleting Substances specific data may be required',
        'EP2': 'Ozone Depleting Substances specific data is required',
        'EP3': 'Vehicle and Engines specific data may be required',
        'EP4': 'Vehicle and Engines specific data is required',
        'EP5': 'Pesticides specific data may be required',
        'EP6': 'Pesticides specific data is required',
        'EP7': 'Toxic Substances Control Act specific data may be required',
        'EP8': 'Toxic Substances Control Act specific data is required',
        'EH1': 'EPA Hydrofluorocarbons data may be required. Only disclaim A allowed',
        'EH2': 'EPA Hydrofluorocarbons data is required',
        'FS3': 'FSIS data may be required. Applicable to all FSIS programs',
        'FS4': 'FSIS data is required. Applicable to all FSIS programs',
        'NM1': '370 specific data may be required',
        'NM2': '370 specific data is required',
        'NM3': 'Antarctic Marine Living Resources specific data may be required',
        'NM4': 'Antarctic Marine Living Resources specific data is required',
        'NM5': 'Highly Migratory Species specific data may be required',
        'NM6': 'Highly Migratory Species specific data is required',
        'NM8': 'Seafood Import Monitoring Program specific data is required',
        'DT1': 'DOT/National Highway Traffic Safety Administration HS-7 data may be required',
        'DT2': 'DOT/National Highway Traffic Safety Administration HS-7 data is required',
        'AL1': 'Lacey Act specific data may be required',
        'AL2': 'Lacey Act specific data is required',
        'FD1': 'FDA data may be required 801(a)',
        'FD2': 'FDA data Required 801(a)',
        'FD3': 'FDA Prior Notice Data may be required 801(m)',
        'FD4': 'FDA Prior Notice Data is required 801(m)',
        'AM1': 'USDA/Agricultural Marketing Service Data related to egg products may be required',
        'AM2': 'USDA/Agricultural Marketing Service Data Related to shell eggs is required',
        'AM3': 'USDA/Agricultural Marketing Service Data Related to marketing orders may be required',
        'AM4': 'USDA/Agricultural Marketing Service Data Related to marketing orders is required',
        'AM6': 'USDA/Agriculture Marketing Service Data related to peanuts is required',
        'AM7': 'USDA/Agriculture Marketing Service Data related to organics may be required',
        'AM8': 'USDA/Agriculture Marketing Service Data related to organics is required',
        'TB1': 'TTB data may be required. Applicable to all TTB programs.',
        'TB2': 'TTB data is required. Applicable to all TTB programs.',
        'TB3': 'TTB data may be required, which can only be disclaimed using codes A or C',
        'AQ1': 'APHIS data may be required',
        'AQ2': 'APHIS data is required',
        'AQX': 'APHIS Data May be required (no disclaim required)',
        'OM1': 'U.S. Department of State, Bureau of Oceans and International Environmental and Scientific Affairs, Office of Marine Conservation data may be required',
        'OM2': 'U.S. Department of State, Bureau of Oceans and International Environmental and Scientific Affairs, Office of Marine Conservation data is required',
        'FW1': 'U.S. Fish and Wildlife Service data may be required, which can only be disclaimed using codes C, D or E',
        'FW2': 'U.S. Fish and Wildlife Service data is required',
        'FW3': 'U.S. Fish and Wildlife Service data may be required, which can only be disclaimed using code C or D',
        'DE1': 'U.S. Drug Enforcement Administration data may be required'
    }
    
    # Build agency mappings from the text
    agency_mappings = {
        'EP1': 'EPA', 'EP2': 'EPA', 'EP3': 'EPA', 'EP4': 'EPA', 'EP5': 'EPA',
        'EP6': 'EPA', 'EP7': 'EPA', 'EP8': 'EPA', 'EH1': 'EPA', 'EH2': 'EPA',
        'FS3': 'FSI', 'FS4': 'FSI',
        'NM1': 'NMF', 'NM2': 'NMF', 'NM3': 'NMF', 'NM4': 'NMF', 'NM5': 'NMF', 'NM6': 'NMF', 'NM8': 'NMF',
        'DT1': 'NHT', 'DT2': 'NHT',
        'AL1': 'APH', 'AL2': 'APH',
        'FD1': 'FDA', 'FD2': 'FDA', 'FD3': 'FDA', 'FD4': 'FDA',
        'AM1': 'AMS', 'AM2': 'AMS', 'AM3': 'AMS', 'AM4': 'AMS', 'AM6': 'AMS', 'AM7': 'AMS', 'AM8': 'AMS',
        'TB1': 'TTB', 'TB2': 'TTB', 'TB3': 'TTB',
        'AQ1': 'APH', 'AQ2': 'APH', 'AQX': 'APH',
        'OM1': 'OMC', 'OM2': 'OMC',
        'FW1': 'FWS', 'FW2': 'FWS', 'FW3': 'FWS',
        'DE1': 'DEA'
    }
    
    # Build requirement level mappings
    requirement_mappings = {
        'EP1': 'M', 'EP2': 'R', 'EP3': 'M', 'EP4': 'R', 'EP5': 'M',
        'EP6': 'R', 'EP7': 'M', 'EP8': 'R', 'EH1': 'M', 'EH2': 'R',
        'FS3': 'M', 'FS4': 'R',
        'NM1': 'M', 'NM2': 'R', 'NM3': 'M', 'NM4': 'R', 'NM5': 'M', 'NM6': 'R', 'NM8': 'R',
        'DT1': 'M', 'DT2': 'R',
        'AL1': 'M', 'AL2': 'R',
        'FD1': 'M', 'FD2': 'R', 'FD3': 'M', 'FD4': 'R',
        'AM1': 'M', 'AM2': 'R', 'AM3': 'M', 'AM4': 'R', 'AM6': 'R', 'AM7': 'M', 'AM8': 'R',
        'TB1': 'M', 'TB2': 'R', 'TB3': 'M',
        'AQ1': 'M', 'AQ2': 'R', 'AQX': 'M',
        'OM1': 'M', 'OM2': 'R',
        'FW1': 'M', 'FW2': 'R', 'FW3': 'M',
        'DE1': 'M'
    }
    
    # Create structured data from the PDF
    for code in codes:
        if code in descriptions:
            pdf_data.append({
                'tariffFlagCode': code,
                'agencyCode': agency_mappings.get(code, ''),
                'requirementLevel': requirement_mappings.get(code, ''),
                'description': descriptions[code]
            })
    
    return pdf_data

def compare_data(pdf_data, migration_data):
    """Compare PDF data with migration data"""
    print("=== COMPARISON REPORT ===\n")
    
    # Create lookups
    pdf_lookup = {item['tariffFlagCode']: item for item in pdf_data}
    migration_lookup = {item['tariffFlagCode']: item for item in migration_data}
    
    # Find all codes
    all_codes = set(pdf_lookup.keys()) | set(migration_lookup.keys())
    
    discrepancies = []
    
    for code in sorted(all_codes):
        pdf_item = pdf_lookup.get(code)
        migration_item = migration_lookup.get(code)
        
        if not pdf_item:
            discrepancies.append(f"❌ {code}: Missing in PDF")
            continue
        
        if not migration_item:
            discrepancies.append(f"❌ {code}: Missing in Migration")
            continue
        
        # Compare fields
        issues = []
        
        if pdf_item['agencyCode'] != migration_item['agencyCode']:
            issues.append(f"Agency: PDF='{pdf_item['agencyCode']}' vs Migration='{migration_item['agencyCode']}'")
        
        if pdf_item['requirementLevel'] != migration_item['requirementLevel']:
            issues.append(f"Requirement: PDF='{pdf_item['requirementLevel']}' vs Migration='{migration_item['requirementLevel']}'")
        
        if pdf_item['description'] != migration_item['description']:
            issues.append(f"Description: PDF='{pdf_item['description']}' vs Migration='{migration_item['description']}'")
        
        if issues:
            discrepancies.append(f"⚠️  {code}: {', '.join(issues)}")
        else:
            print(f"✅ {code}: Perfect match")
    
    print(f"\n=== SUMMARY ===")
    print(f"Total codes: {len(all_codes)}")
    print(f"PDF codes: {len(pdf_data)}")
    print(f"Migration codes: {len(migration_data)}")
    print(f"Discrepancies: {len(discrepancies)}")
    
    if discrepancies:
        print(f"\n=== DISCREPANCIES ===")
        for issue in discrepancies:
            print(issue)
    else:
        print("\n🎉 All data matches perfectly!")

def main():
    # Parse PDF data
    pdf_data = parse_pdf_data('extracted_text.txt')
    
    # Compare with migration data
    compare_data(pdf_data, migration_data)
    
    # Save results
    with open('pdf_parsed_data.json', 'w') as f:
        json.dump(pdf_data, f, indent=2)
    
    with open('migration_data.json', 'w') as f:
        json.dump(migration_data, f, indent=2)

if __name__ == "__main__":
    main()