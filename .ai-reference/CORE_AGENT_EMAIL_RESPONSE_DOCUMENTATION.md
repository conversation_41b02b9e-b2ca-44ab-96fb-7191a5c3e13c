# Core-Agent Email Response System Documentation

## Executive Summary

The Core-Agent email response system is a sophisticated, template-based architecture that processes customer emails, identifies intents, and generates contextual responses. The system uses a fragment-based approach with prioritized templates that are dynamically assembled based on shipment status, user requests, and business rules.

### Key Components

1. **HandleRequestMessageProcessor**: Main orchestrator that processes emails through the intent pipeline
2. **Intent Handlers**: 11 specialized handlers that process specific user intents
3. **Template System**: Consolidated Nunjucks templates with fragment-based composition
4. **Context Building**: ShipmentContextService that builds comprehensive data context
5. **Response Rendering**: ShipmentResponseService that assembles fragments into final HTML

### System Flow

```
Email Received → Intent Analysis → Handler Selection → Context Building → Template Rendering → Email Response
```

## Response Tree Hierarchy

### Intent Processing Pipeline

```
HandleRequestMessageProcessor
├── Load Email & Attachments
├── Extract User Intents (EmailIntentAnalysisService)
├── Double-Pass Classification (for UNSORTED/SPAM/UNKNOWN)
├── Combine PROCESS_DOCUMENT Intents
├── Sort Intents by Priority
│   ├── Priority 1: PROCESS_DOCUMENT (must run first)
│   ├── Priority 2: Status-dependent handlers
│   │   ├── GET_SHIPMENT_STATUS
│   │   ├── REQUEST_RUSH_PROCESSING
│   │   ├── REQUEST_CAD_DOCUMENT
│   │   ├── REQUEST_RNS_PROOF
│   │   └── UPDATE_SHIPMENT
│   └── Priority 3: Acknowledgment/Administrative handlers
│       ├── DOCUMENTATION_COMING
│       ├── ACKNOWLEDGE_DOCUMENTS
│       ├── REQUEST_MANUAL_PROCESSING
│       └── REQUEST_HOLD_SHIPMENT
└── Process Each Intent
    ├── Find Intent Handler
    ├── Build Shipment Context
    ├── Generate Response Fragments
    ├── Collect Side Effects
    └── Send Consolidated Response
```

### Handler → Template → Context Flow

```
Intent Handler
├── createConsolidatedFragments()
│   ├── Main Messages Fragment
│   │   ├── Template: consolidated/main-messages
│   │   └── Context: mainMessages array with type-specific content
│   ├── Validation Issues Fragment
│   │   ├── Template: consolidated/validation-issues
│   │   └── Context: validationIssues (missing docs/fields)
│   ├── Details Header Fragment
│   │   ├── Template: consolidated/details-header
│   │   └── Context: Static header text
│   ├── Shipment Identifiers Fragment
│   │   ├── Template: consolidated/shipment-identifiers
│   │   └── Context: shipmentIdentifiers object
│   ├── Document Status Fragment
│   │   ├── Template: consolidated/document-status
│   │   └── Context: Document receipt status
│   └── Status Line Fragment
│       ├── Template: consolidated/status-line
│       └── Context: formattedCustomsStatus, validation flags
└── Side Effects
    ├── CAD Document Generation
    ├── RNS Proof Content
    ├── Backoffice Alert Emails
    └── Shipment Submission Attempts
```

## Intent Handlers Documentation

### 1. AcknowledgeDocumentsHandler

**Intent**: `ACKNOWLEDGE_DOCUMENTS`  
**Purpose**: Acknowledges receipt of all documents from the importer

**Templates Used**:
- Main: `acknowledge-documents-messages`
- Consolidated: `main-messages`, `validation-issues`, `details-header`, `shipment-identifiers`, `document-status`, `status-line`

**Context Variables**:
```javascript
{
  allDocsReceived: true,
  transportMode: "OCEAN" | "AIR" | "TRUCK",
  shipment: {...},
  shipmentIdentifiers: {...},
  smartTemplateContext: {...},
  directlyAsked: { document_acknowledgment: true }
}
```

**Special Behavior**: Shows complete document status and indicates all documents received

---

### 2. AcknowledgeMissingDocumentsHandler

**Intent**: `ACKNOWLEDGE_MISSING_DOCUMENTS`  
**Purpose**: Proactive notification about missing documents

**Templates Used**:
- Main: `acknowledge-missing-documents-messages`
- Shows document status with missing indicators

**Context Variables**:
```javascript
{
  hasMissingDocs: true,
  shipment: {...},
  shipmentIdentifiers: {...},
  smartTemplateContext: {...},
  directlyAsked: { missing_documents_acknowledgment: true }
}
```

---

### 3. DocumentationComingHandler

**Intent**: `DOCUMENTATION_COMING`  
**Purpose**: User notification that additional documents are coming

**Templates Used**:
- Main: `documentation-coming-messages`
- Shows current document status

**Context Variables**:
```javascript
{
  acknowledgment: "Thank you for letting us know. We'll keep an eye out for the additional documentation.",
  directlyAsked: { documentation_coming: true }
}
```

---

### 4. GetShipmentStatusHandler

**Intent**: `GET_SHIPMENT_STATUS`  
**Purpose**: Handles various shipment status inquiries and specific questions

**Templates Used**: Dynamic based on question type
- ETA questions → `eta.njk`
- Transaction number → `transaction-number.njk`
- Release status → `release-status.njk`
- Shipping status → `shipping-status.njk`
- General status → Status messages

**Context Variables**: Dynamic based on question categories

**Special Features**:
- Uses LLM to classify question types
- Can generate multiple answer fragments
- Shows validation issues for pending statuses
- Handles composite questions (e.g., "What's the status and ETA?")

---

### 5. ProcessDocumentHandler

**Intent**: `PROCESS_DOCUMENT`  
**Purpose**: Process uploaded documents and attempt customs submission

**Templates Used**:
- Main: `document-processing-messages`
- Optional: `cad-messages` (for demo organizations)

**Context Variables**:
```javascript
{
  documents: [
    {
      filename: "invoice.pdf",
      contentType: "commercial-invoice",
      aggregationStatus: "success",
      claroUrl: "https://..."
    }
  ],
  hasAllRequiredDocuments: true,
  acknowledgment: "Custom message if CAD generated"
}
```

**Side Effects**:
- Attempts shipment submission to customs
- May generate CAD document for demo organizations
- Updates shipment status based on submission result

---

### 6. RequestCADDocumentHandler

**Intent**: `REQUEST_CAD_DOCUMENT`  
**Purpose**: Generate and send Customs Accounting Document

**Templates Used**:
- Main: `cad-messages` (via buildCadMessage)

**Context Variables**:
```javascript
{
  type: "cad",
  attachments: {
    cadDocument: {
      fileName: "CAD_HBL123456.pdf",
      mimeType: "application/pdf",
      b64Data: "base64_encoded_pdf_content"
    }
  }
}
```

**Side Effects**: Generates CAD PDF attachment

---

### 7. RequestHoldShipmentHandler

**Intent**: `REQUEST_HOLD_SHIPMENT`  
**Purpose**: Hold or cancel shipment processing

**Templates Used**:
- Main: `hold-shipment-messages`
- Shows document status

**Context Variables**:
```javascript
{
  acknowledgment: "We've received your request to cancel/hold the entry...",
  instructions: ["User instructions"],
  backofficeAlerts: { holdShipment: true },
  directlyAsked: { hold_shipment: true }
}
```

**Side Effects**: Sends backoffice alert email

---

### 8. RequestManualProcessingHandler

**Intent**: `REQUEST_MANUAL_PROCESSING`  
**Purpose**: Request manual intervention for shipment

**Templates Used**:
- Main: `manual-processing-messages`

**Context Variables**:
```javascript
{
  acknowledgment: "We've received your request for manual processing...",
  instructions: ["User instructions"],
  backofficeAlerts: { manualProcessing: true },
  directlyAsked: { manual_processing: true }
}
```

**Side Effects**: Sends backoffice alert email

---

### 9. RequestRNSProofHandler

**Intent**: `REQUEST_RNS_PROOF`  
**Purpose**: Generate RNS proof of release document

**Templates Used**:
- Main: `rns-messages` (via buildRnsMessage)

**Context Variables**:
```javascript
{
  type: "rns",
  attachments: {
    rnsData: {
      content: "Release notification content",
      releaseDate: "2024-03-15"
    }
  }
}
```

**Side Effects**: Generates RNS proof content

---

### 10. RequestRushProcessingHandler

**Intent**: `REQUEST_RUSH_PROCESSING`  
**Purpose**: Expedite shipment processing

**Templates Used**:
- Main: `rush-messages` (via buildRushMessage)

**Context Variables**:
```javascript
{
  backofficeAlerts: { rushProcessing: true },
  submissionResult: "success" | "error",
  submissionError: "Error message if failed",
  directlyAsked: { rush_processing: true }
}
```

**Side Effects**:
- Sends backoffice alert email
- Attempts immediate shipment submission

---

### 11. UpdateShipmentHandler

**Intent**: `UPDATE_SHIPMENT`  
**Purpose**: Update shipment fields (port code, sublocation, CCN)

**Templates Used**:
- Success: `shipment-update-messages`
- Error: `shipment-update-error-messages`

**Context Variables**:
```javascript
{
  acknowledgment: "Shipment updated successfully",
  shipmentId: "123456",
  updatedFields: ["portCode", "cargoControlNumber"],
  updateData: { portCode: "0401", cargoControlNumber: "12345" },
  directlyAsked: { shipment_update: true }
}
```

**Side Effects**: Updates shipment via editShipment API

## Template System Architecture

### Consolidated Template System

The modern template system uses a fragment-based approach with reusable components:

```
/apps/portal-api/src/core-agent/templates/consolidated/
├── main-messages.njk              # Main message router
├── details-header.njk             # "Details:" header
├── shipment-identifiers.njk       # HBL, CCN, containers
├── document-status.njk            # Document receipt status
├── status-line.njk                # Status and signature
├── validation-issues.njk          # Missing docs/fields
└── messages/                      # Intent-specific templates
    ├── cad-messages.njk
    ├── rns-messages.njk
    ├── rush-messages.njk
    ├── document-processing-messages.njk
    ├── acknowledge-documents-messages.njk
    ├── acknowledge-missing-documents-messages.njk
    ├── documentation-coming-messages.njk
    ├── manual-processing-messages.njk
    ├── hold-shipment-messages.njk
    ├── shipment-update-messages.njk
    └── shipment-update-error-messages.njk
```

### Template Rendering Flow

1. **Fragment Creation**: Handler calls `createConsolidatedFragments()`
2. **Context Building**: Full shipment context merged with fragment-specific data
3. **Fragment Processing**:
   - Deduplication by template name
   - Merging of mainMessages for consolidated templates
   - Sorting by priority and predefined order
4. **Template Rendering**: Each fragment rendered with Nunjucks
5. **Assembly**: Fragments concatenated with greeting prepended

### Priority System

Templates are rendered in priority order:

| Priority | Template Type | Purpose |
|----------|--------------|---------|
| 1 | Main Messages | Primary response content |
| 2 | Validation Issues | Missing documents/fields |
| 3 | Details Header | Section separator |
| 4 | Shipment Identifiers | HBL, CCN, containers |
| 5 | Document Status | Document receipt status |
| 6 | Status Line | Current status + signature |

## Context Building Process

### ShipmentContextService Architecture

The context building follows a three-phase approach:

```
ShipmentContextService
├── Data Fetching (IShipmentDataProvider)
│   ├── fetchOrganization()
│   ├── fetchShipment()
│   └── fetchCompliance()
├── Business Rule Evaluation (IBusinessRuleEvaluator)
│   ├── canRush()
│   ├── canGenerateCAD()
│   ├── canGenerateRNSProof()
│   ├── isCompliant()
│   ├── isReleased()
│   └── isSubmitted()
└── Context Formatting (IContextFormatter)
    ├── formatCustomsStatus()
    ├── formatShipmentIdentifiers()
    ├── formatMissingFields()
    └── buildSmartTemplateContext()
```

### Data Flow

1. **Raw Data Fetch**:
   ```javascript
   {
     organization: { id, name, isDemo, settings },
     shipment: { id, status, customsStatus, documents, ... },
     compliance: { missingFields, nonCompliantInvoices, ... }
   }
   ```

2. **Business Rules**:
   ```javascript
   {
     canRush: boolean,
     rushBlockingReason: string,
     canGenerateCAD: boolean,
     cadBlockingReason: string,
     // ... other evaluations
   }
   ```

3. **Formatted Context**:
   ```javascript
   {
     formattedCustomsStatus: "Entry accepted by customs",
     shipmentIdentifiers: { hblNumber, ccn, containers },
     missingFields: ["Weight **missing**", "CCN **missing**"],
     // ... other formatted data
   }
   ```

### Smart Template Context

Provides status-aware messages and availability flags:

```javascript
smartTemplateContext: {
  // Status-specific messages
  statusResponseMessage: "Your shipment is pending...",
  rushActionMessage: "We'll prioritize your shipment...",
  documentBlockerReason: "Commercial invoice required...",
  
  // Transport mode
  transportMode: "OCEAN" | "AIR" | "TRUCK",
  
  // Dates
  etaDate: "March 15, 2024",
  formattedReleaseDate: "March 14, 2024",
  
  // Document availability
  cadDocumentAvailable: true,
  rnsDocumentAvailable: false,
  
  // Document status
  documentStatus: {
    hbl: "Received",
    anEmf: "Missing",
    ciPl: "Received"
  },
  
  // Missing items
  missingItems: ["CCN", "Weight"],
  
  // OGD status
  ogdFilingStatus: "Pending"
}
```

## Template Variable Reference

### Core Variables

| Variable | Type | Description | Source |
|----------|------|-------------|--------|
| `shipment` | Object | Complete shipment entity | Database |
| `organization` | Object | Organization entity | Database |
| `formattedCustomsStatus` | String | User-friendly status | Status formatter |
| `shipmentIdentifiers` | Object | HBL, CCN, containers | Context formatter |
| `smartTemplateContext` | Object | Status-aware helpers | Smart context builder |

### Shipment Identifiers Object

```javascript
{
  hblNumber: "HBL123456",
  cargoControlNumber: "8000-12345678-1",
  transactionNumber: "1234567",
  hasMultipleContainers: false,
  primaryContainer: "ABCD1234567",
  formattedContainers: "ABCD1234567",
  containerNumbers: ["ABCD1234567"]
}
```

### Validation Issues Object

```javascript
{
  showValidationIssues: true,
  missingDocuments: ["Commercial Invoice", "Packing List"],
  missingFields: ["CCN **missing**", "Weight **missing**"]
}
```

### Document Processing Results

```javascript
{
  documents: [
    {
      filename: "invoice.pdf",
      contentType: "commercial-invoice",
      aggregationStatus: "success",
      claroUrl: "https://app.goclaro.com/documents/123"
    }
  ],
  hasAllRequiredDocuments: true,
  acknowledgment: "Documents processed successfully"
}
```

### Side Effects Collection

```javascript
{
  cadDocument: {
    fileName: "CAD_HBL123456.pdf",
    mimeType: "application/pdf",
    b64Data: "base64_content"
  },
  rnsProofData: {
    content: "Release notification content",
    releaseDate: "2024-03-15"
  },
  backofficeAlerts: {
    rushProcessing: true,
    manualProcessing: false,
    holdShipment: false
  },
  documentProcessing: [
    { filename: "invoice.pdf", processed: true, status: "success" }
  ]
}
```

## Troubleshooting Guide

### Common Context Errors

1. **Missing Shipment Context**
   - **Symptom**: "No shipment found for email thread"
   - **Cause**: Email thread not associated with shipment
   - **Solution**: Check FileBatch for shipment association

2. **Template Variable Undefined**
   - **Symptom**: Nunjucks error "variable X is undefined"
   - **Cause**: Context building failed or incomplete
   - **Solution**: Check SafeEvaluationUtil wrapped calls

3. **Empty Response Generated**
   - **Symptom**: Email contains only greeting
   - **Cause**: All handlers failed or no meaningful content
   - **Solution**: Check handler errors in logs

### Debug Logging Points

Key log entries to monitor:

```
[HANDLE_REQUEST_MESSAGE] Processing email {emailId}
[HANDLE_REQUEST_MESSAGE] Found {count} intents: {intent_list}
[HANDLE_REQUEST_MESSAGE] Building context for shipment {shipmentId}
[HANDLE_REQUEST_MESSAGE] Processing intent: {intent}
[HANDLE_REQUEST_MESSAGE] Document processing results: {statuses}
[PDF_ATTACHMENT_TRACKING] Side effects collected - CAD: {bool}, RNS: {bool}
```

### Missing Data Scenarios

1. **No Compliance Data**
   - Falls back to empty arrays for missing fields
   - Shows generic "pending" status messages

2. **No Shipment Status**
   - Uses "unknown" status
   - Shows contact support message

3. **No Organization Settings**
   - Disables demo-specific features (CAD generation)
   - Uses default behavior

### Template Debugging

To debug template rendering:

1. Check fragment generation in handler
2. Verify context variables in `fragmentContext`
3. Review merged context in ShipmentResponseService
4. Check template syntax in .njk files
5. Monitor SafeEvaluationUtil for wrapped errors

## Appendix: Status Response Messages

### Customs Status Messages

| Status | Message |
|--------|---------|
| `pending-commercial-invoice` | "Commercial invoice is missing for this shipment." |
| `pending-confirmation` | "There are compliance issues or missing required fields..." |
| `entry-accepted` | "The shipment entry has been accepted by customs..." |
| `entry-on-hold` | "The entry is currently on hold by customs..." |
| `released` | "Your shipment has been released by customs!" |

### Rush Action Messages

| Status | Message |
|--------|---------|
| `pending-*` | "We need the required documents before we can rush..." |
| `entry-accepted` | "Your shipment is already in queue for release..." |
| `released` | "Your shipment has already been released!" |
| Default | "We'll prioritize your shipment for immediate processing." |

### Document Blocker Reasons

| Status | Reason |
|--------|---------|
| `pending-commercial-invoice` | "We need the commercial invoice first..." |
| `pending-confirmation` | "We need to resolve compliance issues first..." |
| `released` | "Your shipment has already been released." |

---

*This documentation reflects the current state of the Core-Agent email response system as of the latest codebase analysis.*