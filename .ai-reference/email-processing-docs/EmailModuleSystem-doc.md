# Email Module System Documentation

## Overview

The Email Module System provides the foundational email infrastructure for the Claro customs automation platform, handling Gmail integration, email management, template processing, and automated notifications. This system works in conjunction with the core-agent system to provide comprehensive email-based customs automation.

**System Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/`

**Key Responsibilities**:
- Gmail API integration and OAuth management
- Email ingestion and processing pipeline
- Template-based email generation and sending
- Email lifecycle management (CRUD operations)
- Event-driven email automation
- Error handling and recovery mechanisms

## Controllers

### EmailController

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/controllers/email.controller.ts`

**Purpose**: Provides RESTful API endpoints for email management and administration.

**API Endpoints**:

#### Email Management
```typescript
// Email listing with pagination and filtering
GET /emails?page=1&limit=10&status=RESPONDED&organizationId=123
// Response: { emails: Email[], total: number, page: number, limit: number }

// Individual email retrieval with relations
GET /emails/:id
// Response: Email with shipment, organization, and thread data

// Thread-based email retrieval
GET /emails/thread/:threadId
// Response: Email[] - All emails in the specified thread

// Email reprocessing trigger (admin only)
POST /emails/:id/process
// Triggers reprocessing of email through core-agent pipeline

// Email organization reassignment (admin only)
POST /emails/:id/reassign-organization/:organizationId
// Reassigns email to different organization for error correction
```

#### Testing and Development
```typescript
// Test event emission (marked for removal)
POST /emails/test-event/:event
// Emits test events for development and debugging
```

**Security Features**:
- Organization-scoped data access
- Role-based endpoint restrictions
- Request validation and sanitization
- Rate limiting for administrative operations

**Integration Points**:
- Uses `EmailService` for business logic
- Integrates with core-agent for email reprocessing
- Emits events for downstream processing

### GmailController

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/controllers/gmail.controller.ts`

**Purpose**: Manages Gmail OAuth integration and token management.

**API Endpoints**:

#### OAuth Management
```typescript
// OAuth callback handling (marked for backoffice move)
GET /gmail/oauth/callback?code=...&state=...
// Handles OAuth callback and token exchange

// Authorization URI generation
GET /gmail/oauth
// Response: { authUrl: string } - Gmail OAuth authorization URL
```

#### Token Management
```typescript
// Token listing with pagination
GET /gmail/tokens?page=1&limit=10
// Response: { tokens: GmailToken[], total: number }

// Individual token retrieval
GET /gmail/tokens/:email
// Response: GmailToken with refresh status and expiration

// Default token setting
PUT /gmail/tokens/:email/default
// Sets specified token as default for organization

// Manual sync trigger (admin-only)
POST /gmail/trigger-sync
// Triggers manual Gmail sync for testing and troubleshooting
```

**Security Considerations**:
- OAuth state parameter validation
- Token encryption and secure storage
- Admin-only access for sensitive operations
- Rate limiting for sync operations

## Core Services

### EmailService

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/services/email.service.ts`
**Scope**: `@Injectable({ scope: Scope.REQUEST })` - Request-scoped for user context

**Purpose**: Core email business logic with CRUD operations, thread management, and email composition.

**Key Methods**:

#### Email CRUD Operations
```typescript
class EmailService {
  // Email retrieval with organization scoping
  async getEmailById(emailId: number, queryRunner?: QueryRunner): Promise<Email>;
  async getEmails(options: GetEmailsOptions): Promise<PaginatedResult<Email>>;
  
  // Email modification with audit trail
  async editEmail(emailId: number, editDto: EditEmailDto, queryRunner?: QueryRunner): Promise<Email>;
  async updateEmailStatus(emailId: number, status: EmailStatus, queryRunner?: QueryRunner): Promise<void>;
  
  // Email deletion with cascade handling
  async deleteEmail(emailId: number, queryRunner?: QueryRunner): Promise<void>;
}
```

#### Thread Management
```typescript
// Thread-shipment associations
async getEmailThreadShipment(threadId: string): Promise<Shipment | null>;
async setEmailThreadShipment(threadId: string, shipment: Shipment, queryRunner?: QueryRunner): Promise<void>;

// Thread email retrieval
async getPreviousEmails(emailId: number, queryRunner?: QueryRunner): Promise<Array<Email>>;
async getThreadEmails(threadId: string): Promise<Array<Email>>;
```

#### Email Composition and Sending
```typescript
// Email reply with template support
async replyEmail(emailId: number, replyDto: ReplyEmailDto, queryRunner?: QueryRunner): Promise<void>;

// Email sending with attachment support
async sendEmail(sendDto: SendEmailDto, queryRunner?: QueryRunner): Promise<void>;

// Template-based email generation
async sendTemplateEmail(templateName: string, context: any, recipient: string): Promise<void>;
```

**Integration Features**:
- Gmail API integration through `GmailService`
- Template rendering through `TemplateManagerService`
- Organization scoping for multi-tenant security
- Transaction support for data consistency

### GmailService

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/services/gmail.service.ts`

**Purpose**: Gmail API integration with comprehensive error handling and rate limit management.

**Core Capabilities**:

#### Gmail API Integration
```typescript
class GmailService {
  // OAuth flow management
  async getAuthUrl(organizationId: number): Promise<string>;
  async handleOAuthCallback(code: string, state: string): Promise<GmailToken>;
  async refreshToken(token: GmailToken): Promise<GmailToken>;
  
  // Message retrieval with different formats
  async getMessage(messageId: string, format: 'FULL' | 'MINIMAL' | 'METADATA'): Promise<gmail_v1.Schema$Message>;
  async getMessages(query: string, maxResults?: number): Promise<gmail_v1.Schema$Message[]>;
  
  // History API integration for incremental sync
  async getHistory(startHistoryId: string): Promise<gmail_v1.Schema$History[]>;
  async getLatestHistoryId(): Promise<string>;
}
```

#### Message Processing
```typescript
// Gmail message parsing
async parseGmailMessage(message: gmail_v1.Schema$Message): Promise<ParsedGmailMessage>;

// Attachment handling
async getAttachment(messageId: string, attachmentId: string): Promise<Buffer>;
async downloadAttachments(message: gmail_v1.Schema$Message): Promise<EmailAttachment[]>;

// Address parsing and validation
parseEmailAddresses(addressString: string): EmailAddressDto[];
```

**Advanced Features**:
- **Rate Limit Handling**: Exponential backoff and retry mechanisms
- **Batch Operations**: Efficient processing of multiple messages
- **Error Recovery**: Graceful handling of API failures and network issues
- **Token Management**: Automatic token refresh and validation
- **Message Parsing**: Comprehensive parsing of Gmail message structure

### EmailUpdateService

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/services/email-update.service.ts`

**Purpose**: Manages EmailUpdate entities for document association and batch operations.

**Key Features**:

#### EmailUpdate CRUD Operations
```typescript
class EmailUpdateService {
  // Creation with validation
  async createEmailUpdate(createDto: CreateEmailUpdateDto): Promise<EmailUpdate>;
  
  // Retrieval with various filters
  async getEmailUpdateById(id: number): Promise<EmailUpdate>;
  async getEmailUpdatesByEmail(emailId: number): Promise<EmailUpdate[]>;
  async getEmailUpdatesByThread(threadId: string): Promise<EmailUpdate[]>;
  
  // Batch operations
  async createMultipleEmailUpdates(createDtos: CreateEmailUpdateDto[]): Promise<EmailUpdate[]>;
  async updateMultipleEmailUpdates(updates: UpdateEmailUpdateDto[]): Promise<EmailUpdate[]>;
}
```

#### Document Association Management
```typescript
// Document relationship handling
async associateWithDocuments(emailUpdateId: number, documentIds: number[]): Promise<void>;
async getAssociatedDocuments(emailUpdateId: number): Promise<Document[]>;

// Batch correlation via batchId
async getEmailUpdatesByBatchId(batchId: string): Promise<EmailUpdate[]>;
async associateWithFileBatch(emailUpdateId: number, batchId: string): Promise<void>;
```

**Business Logic Features**:
- Organization scoping for multi-tenancy
- Required field validation pipeline
- Transaction handling for consistency
- Audit trail for changes

### EmailDeletionService

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/services/email-deletion.service.ts`

**Purpose**: Provides safe email deletion with comprehensive cascade handling and preview functionality.

**Critical Functionality**:

#### Email Deletion Pipeline
```typescript
class EmailDeletionService {
  // Complete email deletion with cascade handling
  async deleteEmailCompletely(emailId: number): Promise<DeletionResult>;
  
  // Preview deletion impact
  async getEmailDeletionSummary(emailId: number): Promise<DeletionSummary>;
  
  // Batch deletion operations
  async deleteMultipleEmails(emailIds: number[]): Promise<DeletionResult[]>;
}
```

#### Cascade Management
```typescript
// Foreign key constraint order management
private async deleteCascadingEntities(emailId: number): Promise<CascadeDeletionResult> {
  // 1. EmailThread cleanup and dependency management
  // 2. EmailUpdate.id → batchId correlation handling
  // 3. FileBatch and Document cleanup procedures
  // 4. Shipment deletion correlation
  // 5. Attachment and file cleanup
}
```

**Safety Features**:
- **Preview Mode**: Shows deletion impact before execution
- **Dependency Analysis**: Identifies all related entities
- **Transaction Safety**: Ensures atomic deletion operations
- **Rollback Capability**: Can undo deletions in case of errors
- **REPL Integration**: Debugging utilities for complex deletion scenarios

### RNSProofService

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/services/rns-proof-service.ts`

**Purpose**: Specialized service for RNS (Release Notification System) proof of release determination.

**Core Functionality**:
```typescript
class RNSProofService {
  // RNS proof determination
  async determineRNSProofOfRelease(shipment: Shipment): Promise<RNSProofResult>;
  
  // Candata service integration
  async getRNSResponseFromCandata(shipment: Shipment): Promise<CandataRNSResponseDto>;
  
  // Response categorization and filtering
  async categorizeRNSResponse(response: CandataRNSResponseDto): Promise<RNSResponseCategory>;
  
  // Release date extraction and validation
  async extractReleaseDate(rnsResponse: CandataRNSResponseDto): Promise<Date | null>;
}
```

**Integration Points**:
- Integrates with Candata customs system
- Used by `RequestRNSProofHandler` for document generation
- Provides structured data for email templates

## Processors

### GetGmailMessageProcessor

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/processors/get-gmail-message.processor.ts`

**Purpose**: Processes Gmail messages through a comprehensive 9-step pipeline from Gmail API retrieval to core-agent processing.

**Processing Pipeline**:

#### Step 1-3: Message Retrieval and Validation
```typescript
async process(job: GetGmailMessageJob): Promise<void> {
  // 1. Gmail API message retrieval with error handling
  const gmailMessage = await this.gmailService.getMessage(messageId, 'FULL');
  
  // 2. System email detection and filtering
  if (this.isSystemEmail(gmailMessage)) {
    await this.markEmailAsSystemSkipped(gmailMessage);
    return;
  }
  
  // 3. Organization context creation and validation
  const organization = await this.resolveOrganization(gmailMessage);
}
```

#### Step 4-6: Content Processing and Association
```typescript
// 4. Email parsing and attachment extraction
const parsedEmail = await this.parseGmailMessage(gmailMessage);
const attachments = await this.extractAttachments(gmailMessage);

// 5. Importer resolution and organization assignment
const importer = await this.resolveImporter(parsedEmail, organization);

// 6. Database persistence with transaction handling
const email = await this.persistEmail(parsedEmail, organization, importer);
```

#### Step 7-9: Integration and Downstream Processing
```typescript
// 7. Context creation and dependency injection
const contextId = ContextIdFactory.create();
await this.createRequestContext(organization, contextId);

// 8. FileBatch association with existing shipments
await this.associateWithExistingShipments(email, attachments);

// 9. Event emission for downstream processing
await this.emitProcessingEvents(email, organization);
```

**Advanced Features**:
- **Public Testing Method**: `processEmailFromGmailMessage()` for integration testing
- **Error Recovery**: Comprehensive error handling with fallback mechanisms
- **Rate Limit Handling**: Gmail API rate limit compliance
- **Context Injection**: Proper NestJS service scoping

**Integration Points**:
- Triggers `IdentifyShipmentProcessor` for shipment association
- Emits events for `EmailSagaListener` coordination
- Creates FileBatch associations for document processing

## Event System

### Email Listener

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/listeners/email.listener.ts`

**Purpose**: Orchestrates email synchronization and automated processing through advanced event handling.

**Core Event Handlers**:

#### Email Synchronization
```typescript
@OnEvent(EmailEvent.SYNC_EMAILS)
async handleEmailSync(event: EmailSyncEvent): Promise<void> {
  // Full vs partial sync determination
  const syncType = await this.determineSyncType(event.organizationId);
  
  if (syncType === 'FULL') {
    await this.performFullSync(event.organizationId);
  } else {
    await this.performIncrementalSync(event.organizationId, event.lastHistoryId);
  }
}
```

#### Gmail History API Integration
```typescript
// Incremental sync using Gmail History API
private async performIncrementalSync(organizationId: number, startHistoryId: string): Promise<void> {
  // 1. Retrieve history changes since last sync
  const historyChanges = await this.gmailService.getHistory(startHistoryId);
  
  // 2. Process message additions and deletions
  await this.processHistoryChanges(historyChanges, organizationId);
  
  // 3. Update history ID for next sync
  await this.updateLastSyncHistoryId(organizationId);
}
```

#### Error Handling and Recovery
```typescript
// Rate limit handling during sync
private async handleRateLimitDuringSync(error: GmailApiError): Promise<void> {
  // Exponential backoff implementation
  const backoffDelay = this.calculateBackoffDelay(error.retryAfter);
  await this.scheduleRetrySyncAfterDelay(backoffDelay);
}
```

**Advanced Features**:
- **History ID Management**: Prevents race conditions and duplicate processing
- **Scoped Service Resolution**: Per-organization service context
- **Manual Review Automation**: Automated responses for manual review emails
- **Integration Coordination**: Works with EmailSagaCoordinator for state management

### Queue Listener

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/listeners/queue.listener.ts`

**Purpose**: Handles queue processing failures and automated error responses.

**Error Handling Capabilities**:
```typescript
@OnEvent(QueueEvent.GMAIL_MESSAGE_PROCESSING_FAILED)
async handleGmailProcessingFailure(event: ProcessingFailureEvent): Promise<void> {
  // 1. Update email status to indicate failure
  await this.emailService.updateEmailStatus(event.emailId, EmailStatus.PROCESSING_FAILED);
  
  // 2. Send automated failure notification
  await this.sendProcessingFailureEmail(event.emailId, event.error);
  
  // 3. Log failure for monitoring and analysis
  this.logger.error(`Gmail message processing failed: ${event.error.message}`, {
    emailId: event.emailId,
    organizationId: event.organizationId,
    error: event.error
  });
}
```

**Features**:
- Organization-scoped error handling
- Automated user notification for failures
- Comprehensive error logging and monitoring
- Integration with manual review escalation

## Template System

### Email Template Infrastructure

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/templates/`

**Template Categories**:

#### Status and Processing Templates
```
├── manual-review-email.njk - Generic manual review notification
├── processing-failed-email.njk - Processing failure notification
├── customs-status-check-error-email.njk - Customs status check failures
└── entry-submission-error-email.njk - Entry submission error notifications
```

#### Document and Workflow Templates
```
├── document-sent-in-response-email.njk - Document submission confirmations
├── document-sent-in-response-with-queries-email.njk - Document with queries
├── live-entry-upload-failed-email.njk - Live entry upload failures
└── live-shipment-email.njk - Live shipment notifications
```

#### User Management Templates
```
├── importer-onboarded-email.njk - Importer onboarding notifications
├── reset-password-email.njk - Password reset emails
└── onboarding-create-user-error-email.njk - User creation error notifications
```

#### Workflow and Compliance Templates
```
├── accounting-not-completed-warning-email.njk - Accounting workflow warnings
├── entry-not-accepted-warning-email.njk - Entry rejection notifications
├── live-shipment-mandatory-fields-update-email.njk - Field update requirements
├── manual-entry-submission-request-email.njk - Manual processing requests
├── rns-status-change-email.njk - RNS status change notifications
└── update-entry-error-email.njk - Entry update error notifications
```

### Template Context and Usage

**Template Variable Structures**:
Each template expects specific context variables for rendering:

```typescript
// Example: manual-review-email.njk context
interface ManualReviewEmailContext {
  recipientName: string;
  emailSubject: string;
  reviewReason: string;
  estimatedResolutionTime: string;
  supportContactInfo: ContactInfo;
  emailId: number;
  organizationName: string;
}

// Example: rns-status-change-email.njk context
interface RNSStatusChangeEmailContext {
  shipment: Shipment;
  rnsResponse: CandataRNSResponseDto;
  statusCategory: RNSResponseCategory;
  releaseDate?: string;
  cadDocument?: CADDocumentData;
  nextSteps: string[];
}
```

**Template Integration Points**:
- Used by `EmailService.sendTemplateEmail()` for automated responses
- Integrated with `RNSStatusChangeEmailSender` for customs notifications
- Context provided by business services and event handlers
- Rendered using shared `TemplateManagerService`

## Schema System

### Validation Schemas

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/schemas/`

**Core Validation Schemas**:

#### Email Processing Schemas
```typescript
// email-intent.schema.ts - Intent extraction and validation
export const EmailIntentSchema = z.object({
  intent: z.enum(EMAIL_INTENTS),
  confidence: z.number().min(0).max(1),
  instructions: z.array(z.string()).optional(),
  extractedData: z.record(z.unknown()).optional()
});

// validated-email-intents.schema.ts - Post-processing validation
export const ValidatedEmailIntentsSchema = z.object({
  intents: z.array(ValidatedEmailActionSchema),
  metadata: z.object({
    processingTime: z.number(),
    modelUsed: z.string(),
    confidenceScore: z.number()
  })
});
```

#### Business Entity Schemas
```typescript
// shipment.schema.ts - Shipping data structure validation
export const ShipmentSchema = z.object({
  hblNumber: z.string().optional(),
  mblNumber: z.string().optional(),
  cargoControlNumber: z.string().optional(),
  customsStatus: z.enum(CUSTOMS_STATUS_VALUES),
  etaPort: z.date().optional(),
  etaDestination: z.date().optional()
});

// email-content.schema.ts - Email content validation
export const EmailContentSchema = z.object({
  subject: z.string().max(255).optional(),
  text: z.string().max(50000),
  attachments: z.array(EmailAttachmentSchema).optional(),
  messageHistory: z.array(z.string()).optional()
});
```

#### Reference Data Schemas
```typescript
// country.schema.ts - Country reference validation
export const CountrySchema = z.object({
  code: z.string().length(2),
  name: z.string().min(1).max(100),
  alpha3Code: z.string().length(3).optional()
});

// location.schema.ts - Location reference validation
export const LocationSchema = z.object({
  portCode: z.string().max(10),
  portName: z.string().max(100),
  country: CountrySchema,
  sublocation: z.string().max(50).optional()
});

// trade-partner.schema.ts - Trade partner validation
export const TradePartnerSchema = z.object({
  name: z.string().min(1).max(200),
  address: z.string().max(500),
  contactInfo: z.object({
    email: z.string().email().optional(),
    phone: z.string().max(20).optional()
  }).optional()
});
```

**Schema Features**:
- **Input Sanitization**: Prevents malicious input and injection attacks
- **Business Rule Validation**: Enforces business logic constraints
- **Size Limits**: Prevents oversized inputs and DoS attacks
- **Type Safety**: Ensures data consistency across system boundaries
- **Error Handling**: Provides detailed validation error messages

### Schema Integration Patterns

**LLM Integration**:
- Schemas define expected structure for LLM responses
- Validation occurs before business logic processing
- Malformed responses trigger fallback mechanisms
- Schema evolution supports backward compatibility

**API Validation**:
- Controller endpoints validate inputs using schemas
- Response validation ensures API contract compliance
- Error responses include detailed validation failures
- Schema documentation supports API documentation

## Testing Infrastructure

### Test Controllers and Services

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/test/`

**Testing Utilities**:

#### Test Controllers
```typescript
// test-email.controller.ts - Email testing endpoints
@Controller('test/email')
export class TestEmailController {
  // Email template testing
  @Post('template/:templateName')
  async testTemplate(@Param('templateName') template: string, @Body() context: any): Promise<string>;
  
  // Email sending testing
  @Post('send')
  async testEmailSending(@Body() emailData: TestEmailDto): Promise<void>;
  
  // Gmail integration testing
  @Post('gmail/parse')
  async testGmailParsing(@Body() gmailMessage: any): Promise<ParsedGmailMessage>;
}
```

#### Test Services
```typescript
// test-email.service.ts - Email testing business logic
export class TestEmailService {
  // Generate test email data
  async generateTestEmail(organizationId: number): Promise<Email>;
  
  // Create test Gmail messages
  async createTestGmailMessage(options: TestGmailOptions): Promise<gmail_v1.Schema$Message>;
  
  // Simulate email processing pipeline
  async simulateEmailProcessing(emailId: number): Promise<ProcessingResult>;
}
```

#### Test Data Generators
```typescript
// test-data-generators.ts - Test data creation utilities
export class TestDataGenerators {
  static generateEmailAddresses(count: number): EmailAddressDto[];
  static generateAttachments(types: string[]): EmailAttachment[];
  static generateShipmentData(): Partial<Shipment>;
  static generateOrganizationContext(): Organization;
}
```

**Testing Approach**:
- **Unit Testing**: Individual service and controller testing
- **Integration Testing**: Cross-service interaction validation
- **Template Testing**: Email template rendering verification
- **Gmail API Testing**: Gmail integration simulation
- **Data Generation**: Realistic test data creation

## Integration Points

### Cross-Module Integration

**With Core-Agent System**:
- Provides email ingestion pipeline for core-agent processing
- Supplies EmailService for core-agent email operations
- Handles email template rendering for core-agent responses
- Manages email status updates throughout processing lifecycle

**With Document System**:
- Processes email attachments through document extraction pipeline
- Associates emails with FileBatch for document correlation
- Provides email context for document processing workflows

**With Shipment System**:
- Associates emails with shipments through thread management
- Provides shipment context for email template rendering
- Updates shipment status based on email processing results

### External System Integration

**Gmail API**:
- OAuth 2.0 authentication and token management
- Message retrieval with comprehensive parsing
- History API for incremental synchronization
- Rate limit compliance and error handling

**Template Engine**:
- Nunjucks template rendering through TemplateManagerService
- Context injection and variable substitution
- Template caching and performance optimization

## Performance and Monitoring

### Performance Optimizations

**Gmail API Optimization**:
- Batch message retrieval for efficiency
- History API for incremental sync
- Rate limit compliance with exponential backoff
- Connection pooling and request optimization

**Database Optimization**:
- Organization-scoped queries with proper indexing
- Bulk operations for batch processing
- Transaction optimization for consistency
- Query result caching where appropriate

**Template Rendering Optimization**:
- Template compilation caching
- Context preparation optimization
- Async rendering for large templates
- Memory management for large email volumes

### Monitoring and Observability

**Key Metrics**:
```typescript
// Email processing metrics
interface EmailProcessingMetrics {
  emailsProcessedPerHour: number;
  gmailApiResponseTime: number;
  templateRenderingTime: number;
  emailDeliverySuccessRate: number;
  errorRateByType: Record<string, number>;
}

// Gmail API metrics
interface GmailApiMetrics {
  rateLimitHits: number;
  tokenRefreshRate: number;
  apiErrorRate: number;
  syncLatency: number;
  messageRetrievalTime: number;
}
```

**Logging Strategy**:
- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Performance Tracking**: Response times and throughput measurement
- **Error Aggregation**: Centralized error collection and analysis
- **Business Metrics**: Email processing success rates and user satisfaction

**Health Checks**:
- Gmail API connectivity and token validity
- Database connection and query performance
- Template rendering system health
- Queue processing status and backlog monitoring

## Security and Compliance

### Security Measures

**Data Protection**:
- Organization-scoped data access enforcement
- Input sanitization and validation
- Secure token storage and encryption
- PII data handling compliance

**API Security**:
- Rate limiting on all endpoints
- Authentication and authorization validation
- Request/response logging for audit
- Error message sanitization

**Gmail Integration Security**:
- OAuth 2.0 implementation best practices
- Secure token refresh and storage
- API key protection and rotation
- Access scope limitation

### Compliance Features

**Audit Trail**:
- Comprehensive logging of all email operations
- User action tracking and attribution
- Data modification history
- Regulatory compliance reporting

**Data Retention**:
- Email retention policy enforcement
- Automated cleanup of expired data
- Backup and recovery procedures
- Data export capabilities for compliance

This comprehensive documentation covers all aspects of the Email Module System, providing complete coverage of Gmail integration, email management, template processing, and automated notifications that work in conjunction with the core-agent system to deliver sophisticated email-based customs automation.