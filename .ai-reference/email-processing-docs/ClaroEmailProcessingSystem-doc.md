# Claro Email Processing System Documentation

## Overview

The Claro Email Processing System is a sophisticated customs automation platform that processes trade documents, manages customs filings, and provides automated compliance checking for import/export operations through email-based interactions. The system uses advanced LLM integration and a fragment-based template rendering system to provide intelligent responses to customer inquiries.

**Core Architecture**: Rush monorepo with NestJS backend, React frontends, and shared libraries
**Primary Processor**: `HandleRequestMessageProcessor` serving as the central email processing orchestrator
**Template System**: Fragment-based Nunjucks templating with priority-ordered response generation

## Architecture Overview

### System Components

```mermaid
graph TD
    A[Email Received] --> B[HandleRequestMessageProcessor]
    B --> C[Intent Analysis Service]
    C --> D[Intent Handler Registry]
    D --> E[11 Specialized Handlers]
    E --> F[Fragment Generation]
    F --> G[Template Rendering Service]
    G --> H[Email Response]
    
    I[Shipment Context Service] --> F
    J[23 Service Dependencies] --> E
    K[Fragment Templates] --> G
    L[Business Rules Engine] --> I
```

### Main Applications
- **portal** - Customer-facing React frontend
- **portal-api** - Main NestJS backend API with LLM integration (primary focus)
- **backoffice** - Admin React frontend  
- **backoffice-api** - Admin NestJS backend
- **bullmq-board** - Queue monitoring dashboard
- **cloud-functions** - Firebase cloud functions

### Shared Libraries
- **libraries/nest-modules** - Shared NestJS modules, entities, DTOs, and services
- **libraries/ui** - Shared React UI components
- **tools/utils** - Shared utilities

## Service Architecture Analysis

### Service Dependency Classification

**🔴 High-Risk Dependencies (High Coupling):**
- **EmailService** - 11 dependencies (VERY HIGH coupling, God Object pattern)
- **HandleRequestMessageProcessor** - 11 dependencies (HIGH coupling)
- **ShipmentContextService** - 5 dependencies (HIGH coupling)

**🟡 Medium-Risk Dependencies:**
- **EmailIntentAnalysisService** - 2 dependencies
- **FileBatchService** - 4 dependencies

**🟢 Low-Risk Dependencies:**
- **ShipmentResponseService** - 1 dependency
- **TemplateManagerService** - 1 dependency

### Critical Architectural Issues

1. **Circular Dependency Risk**: EmailService ↔ FileBatchService creates tight coupling
2. **Scope Mismatch**: Singleton processor managing request-scoped services through complex ModuleRef.resolve() patterns
3. **God Object Pattern**: EmailService violates Single Responsibility Principle with 11 dependencies
4. **Single Points of Failure**: TemplateManagerService shared across multiple critical paths

### Service Scoping Patterns

**Request-Scoped Services (6):**
- `EmailService`, `FileBatchService`, `RNSStatusChangeEmailSender`, `FileService`, `DocumentService`, `SseEventService`

**Singleton Services (17):**
- `HandleRequestMessageProcessor`, `EmailIntentAnalysisService`, `ShipmentResponseService`, `ShipmentContextService`, `IntentHandlerRegistry`, `TemplateManagerService`, and 11 others

## Intent Handler System

### Handler Classification by Complexity

**Complex Handlers (High Service Usage):**

#### ProcessDocumentHandler
- **Dependencies**: `DocumentProcessorService`, `ShipmentContextService`, `DataSource` (TypeORM)
- **Scope**: Processes document attachments and attempts automatic entry submission
- **Features**: Complex submission workflow, CAD document generation, direct database queries

#### GetShipmentStatusHandler
- **Dependencies**: `AnswerUserQueryService`, `ShipmentResponseService`
- **Scope**: Handles status inquiries and specific questions (ETA, transaction number, release status)
- **Features**: Complex question classification and multi-answer support via LLM

#### RequestCADDocumentHandler
- **Dependencies**: `RNSStatusChangeEmailSender`, `ImporterService`, `DataSource`, `ShipmentResponseService`
- **Scope**: Generates and provides CAD (Canada Entry) documents
- **Features**: Direct TypeORM queries, PDF attachment handling

#### RequestRNSProofHandler
- **Dependencies**: `RnsProofService`, `RNSStatusChangeEmailSender`, `ImporterService`, `ShipmentResponseService`
- **Scope**: Generates RNS (Release Notification System) proof of release documents
- **Features**: Complex RNS data processing and email content generation

**Simple Handlers (Low Service Usage):**

#### AcknowledgeDocumentsHandler
- **Dependencies**: None (uses BaseIntentHandler helpers)
- **Scope**: Acknowledges receipt of documents from importers
- **Template**: `acknowledge-documents-messages`

#### AcknowledgeMissingDocumentsHandler
- **Dependencies**: None (uses BaseIntentHandler helpers)
- **Scope**: Proactive notification about missing documents
- **Template**: `acknowledge-missing-documents-messages`

#### DocumentationComingHandler
- **Dependencies**: None (uses BaseIntentHandler helpers)
- **Scope**: Acknowledges user notifications about incoming documentation
- **Template**: `documentation-coming-messages`

### Handler Service Patterns

#### Shared Services Pattern
- **EmailService** - Used by multiple handlers for backoffice alerts
- **ShipmentResponseService** - Common utility service across handlers
- **RNSStatusChangeEmailSender** - Used for CAD and RNS document generation
- **ImporterService** - Required for document generation handlers

#### Agent-Context Services Pattern
- Many handlers use `ShipmentContextWithServices` for enhanced service access
- Services accessed via `context._services.serviceName`
- Examples: `entrySubmissionService`, `emailService`

#### Template-Based Response Pattern
- All handlers use consolidated template system
- `buildMainMessages()` → `buildConsolidatedOptions()` → `createConsolidatedFragments()`
- Consistent priority-based message ordering

## Template Generation System

### Template Architecture

**Template Categories:**

#### Answer Templates (Simple Responses)
```
/apps/portal-api/src/core-agent/templates/
├── answer-eta-template.njk - ETA information responses
├── answer-question-template.njk - General question answers
├── answer-release-status-template.njk - Release status responses
├── answer-shipping-status-template.njk - Shipping status responses
└── answer-transaction-number-template.njk - Transaction number responses
```

#### Status Templates
```
├── customs-status.njk - Customs processing status
├── shipping-status.njk - Shipment tracking status
├── submission-status.njk - Entry submission status
├── eta.njk - Estimated time of arrival
├── release-status.njk - Customs release information
└── transaction-number.njk - Transaction number display
```

#### Fragment System
```
/core-agent/fragments/
├── status-messages/ - Status-specific responses (pending-commercial-invoice, etc.)
├── details/ - Common information blocks (shipment-identifiers, document-status)
├── document-requests/ - Consolidated document responses (rush-processing, CAD, RNS)
└── system/ - Error messages and system notifications
```

#### Consolidated Templates
```
/consolidated/
└── main-messages.njk - Master template for complex multi-intent responses
```

### Template Context System

**Core Context Structure (`ShipmentContext`):**
```typescript
interface ShipmentContext {
  // Raw data
  shipment: Shipment;
  compliance: ValidateShipmentComplianceResponseDto;
  organization: Organization;
  
  // Business rule evaluations
  canRush: boolean;
  canGenerateCAD: boolean;
  canGenerateRNSProof: boolean;
  isCompliant: boolean;
  isReleased: boolean;
  
  // Template-ready variables
  smartTemplateContext: {
    statusResponseMessage: string;
    cadDocumentAvailable: boolean;
    rnsDocumentAvailable: boolean;
    documentStatus: { hbl: string; anEmf: string; ciPl: string };
    transportMode: "OCEAN" | "AIR" | "TRUCK";
    hasETA: boolean;
    etaDate?: string;
    formattedReleaseDate?: string;
  };
  
  // Formatted display values
  shipmentIdentifiers: {
    hblNumber: string;
    cargoControlNumber: string;
    containerNumbers: string[];
    formattedContainers: string;
  };
  
  // Document and compliance analysis
  missingFieldsAnalysis: {
    formattedMissingFields: string[];
    ogdFilingStatus: string;
  };
}
```

### Template Rendering Pipeline

**ShipmentResponseService** (Core Rendering Engine):
```typescript
class ShipmentResponseService {
  async renderFragments(fragments: ResponseFragment[], context: ShipmentContext): Promise<string> {
    // 1. Fragment Deduplication - Removes duplicate templates, merges main-messages arrays
    // 2. Priority Sorting - Orders fragments by explicit priority and predefined template order
    // 3. Context Merging - Combines main shipment context with fragment-specific context
    // 4. Template Rendering - Uses TemplateManagerService (Nunjucks) to render each fragment
    // 5. HTML Sanitization - Escapes user content to prevent XSS attacks
    // 6. Fragment Joining - Combines rendered fragments with appropriate spacing
    // 7. Greeting Addition - Prepends "Hello," to every email response
  }
}
```

**Context Data Providers:**

#### Primary Context Builder: ShipmentContextService
- **Location**: `/apps/portal-api/src/agent-context/services/shipment-context.service.ts`
- **Dependencies**: `IShipmentDataProvider`, `IBusinessRuleEvaluator`, `IContextFormatter`, `IEmailService`, `IRnsProofService`, `IEntrySubmissionService`, `ICommercialInvoiceService`

**Context Building Pipeline:**
1. Load raw shipment data with full relations
2. Evaluate business rules (can rush, can generate documents, compliance status)
3. Format display values (dates, identifiers, status messages)
4. Analyze missing documents and fields
5. Build template-ready context variables
6. Inject service instances for handler operations

#### Fragment-Specific Context Providers
- **Intent Handlers**: Add fragment-specific context (error messages, document results, missing fields)
- **RNSStatusChangeEmailSender**: CAD document context
- **RnsProofService**: RNS proof document context

### Template Processing Flow

**End-to-End Template Generation:**

1. **Email Processing** (`HandleRequestMessageProcessor`)
   - Receives email through BullMQ queue
   - Loads email, attachments, and organization data
   - Finds associated shipment using multiple strategies

2. **Context Building** (`ShipmentContextService`)
   - Builds comprehensive `ShipmentContext` with business rules and formatted data
   - Injects service instances for handler operations

3. **Intent Processing** (`IntentHandlerRegistry`)
   - Routes validated intents to specific handlers
   - Each handler generates `ResponseFragment[]` arrays

4. **Fragment Generation** (Intent Handlers)
   - Build main messages with priority ordering
   - Create consolidated fragments with enhanced context
   - Include side effects (CAD documents, RNS data, alerts)

5. **Response Rendering** (`ShipmentResponseService`)
   - Deduplicate and sort fragments by priority
   - Render each fragment using Nunjucks templates
   - Sanitize HTML output for security
   - Join fragments with proper spacing
   - Add greeting prefix

6. **Email Delivery** (`EmailService`)
   - Attach generated documents (CAD, RNS)
   - Send HTML email response
   - Update email status to RESPONDED

## LLM Integration Patterns

### Model Usage Strategy

**Model Constants**: `/apps/portal-api/src/core-agent/constants/llm.constants.ts`

- **Full Model** (`CORE_AGENT_LLM_MODEL_FULL`): Complex reasoning, intent analysis, document processing
- **Mini Model** (`CORE_AGENT_LLM_MODEL_MINI`): Simple classification, validation tasks

### LLM Service Integration

**Primary LLM Service**: `AskLLMService`
- **Location**: `/apps/portal-api/src/llm/ask-llm/services/ask-llm.service.ts`
- **Prompt Templates**: `/apps/portal-api/src/llm/prompt-templates/`
- **Concurrency Management**: `limitConcurrency()` utility

### Intent Processing Flow

1. **Task Decomposition**: Break email into discrete task strings (`EmailIntentAnalysisService.decomposeEmailToTasks()`)
2. **Intent Classification**: Classify each task using handler registry metadata
3. **Validation**: Convert to structured ValidatedEmailAction format
4. **Refinement**: Double-pass classification for problematic intents
5. **Combination**: Merge related document processing intents
6. **Prioritization**: Sort by business priority rules
7. **Execution**: Process through fragment-based system

### Double-Pass Intent Refinement

**Implementation**: Sophisticated intent refinement to improve classification accuracy

```typescript
private async refineProblematicIntents(
  intents: ValidatedEmailAction[],
  messageContent: MessageContent
): Promise<ValidatedEmailAction[]> {
  // Re-classify problematic intents (UNSORTED, SPAM, UNKNOWN) 
  // using individual instruction analysis for better accuracy
  // Filters improved classifications and keeps originals if no improvement
}
```

## Queue and Processing System

### Queue Configuration

**Queue Types**: `/apps/portal-api/src/core-agent/types/queue.types.ts`
```typescript
enum CoreAgentQueueName {
  IDENTIFY_SHIPMENT = "core-agent.identify-shipment",
  HANDLE_REQUEST_MESSAGE = "core-agent.handle-request-message", 
  EVENT_EMITTER = "core-agent.event-emitter"
}

const DEFAULT_WORKER_OPTIONS: NestWorkerOptions = {
  concurrency: 3,
  limiter: { max: 100, duration: 1000 }
};
```

### Processing Pipeline

**Main Processing Method** (`HandleRequestMessageProcessor.process()`):

1. **Transaction Setup**: Create database transaction for consistency
2. **Context Creation**: Generate request context for scoped services
3. **Service Resolution**: Resolve request-scoped services using NestJS ModuleRef
4. **Email Loading**: Load email with validation and previous email context
5. **Attachment Processing**: Load and process email attachments
6. **Intent Analysis**: Analyze email content using LLM services
7. **Fragment Processing**: Convert intents to response fragments
8. **Response Generation**: Render fragments to HTML using template system
9. **Email Delivery**: Send response with attachments
10. **Status Update**: Update email status and commit transaction

## Error Handling and Fallback Mechanisms

### Error Categories

1. **Intent Analysis Failure**: Falls back to no-intents response
2. **Shipment Not Found**: Escalates to manual review
3. **Fragment Rendering Failure**: Skips failed fragments, continues with others
4. **Email Sending Failure**: Falls back to manual review
5. **Empty Response Detection**: Escalates to manual review

### Manual Review System

**Fallback Response Implementation**:
```typescript
private async sendFallbackResponse(
  email: Email,
  emailService: EmailService, 
  queryRunner: QueryRunner
): Promise<void> {
  await emailService.editEmail(email.id, {
    status: EmailStatus.MANUAL_REVIEW,
    error: "No shipment found for email thread"
  }, queryRunner);

  this.eventEmitter.emit(EmailEvent.EMAIL_MANUAL_REVIEW_REQUIRED, 
    new EmailManualReviewRequiredEvent({
      emailId: email.id,
      organizationId: email.organization.id,
      reviewReason: EmailManualReviewReason.NO_INTENTS
    })
  );
}
```

### Transaction Management
- All processing occurs within database transactions
- Rollback on any critical failure
- Proper resource cleanup in finally blocks

## Performance and Monitoring

### Concurrency Controls
- **Worker concurrency**: 3 concurrent jobs
- **Rate limiting**: 100 requests per second
- **LLM API concurrency limiting**: Prevents API rate limit violations

### Database Optimization
- Strategic use of query runners for transaction consistency
- Selective field loading to minimize data transfer
- Proper indexing on search fields (HBL, MBL, etc.) for shipment discovery

### Key Performance Metrics
- Processing time per email
- Intent classification accuracy (double-pass refinement statistics)
- Fragment rendering success rates
- Manual review escalation rates
- Email response delivery success

### Monitoring Strategy
- Structured logging with correlation IDs
- Performance metrics for processing stages
- Document processing status tracking
- Intent classification success rates

## Security Considerations

### Input Validation
- Email content sanitization before LLM processing
- Attachment type and size validation
- Organization-scoped data access enforcement

### Data Protection
- No sensitive data in logs (PII, financial information)
- Proper access control through request context
- Secure template rendering with input escaping

### External API Security
- Rate limiting for LLM API calls
- Secure credential management
- Error message sanitization

## System Integration Points

### External Systems
- **Gmail API**: Email operations through GmailService
- **LLM APIs**: Intent analysis and document processing
- **Customs Systems**: CAD document generation, RNS proof retrieval
- **Document Processing**: File parsing and data extraction

### Internal Integration
- **Database**: TypeORM with PostgreSQL for data persistence
- **Queue System**: BullMQ for asynchronous job processing
- **Event System**: EventEmitter2 for loose coupling
- **Template System**: Nunjucks for HTML generation

## Testing Infrastructure

### Testing Utilities
- **E2E Testing Script**: `/apps/portal-api/src/core-agent/testing/run-e2e-with-logs.sh`
- **Database Query Helper**: `/apps/portal-api/src/core-agent/testing/db-query.js`

### Key Test Scenarios
1. **Single Intent Processing**: Each intent type with valid data
2. **Multiple Intent Combinations**: Document processing + status requests
3. **Error Scenarios**: No shipment found, malformed intents, LLM failures
4. **Edge Cases**: Empty emails, spam detection, circular processing
5. **Fragment Rendering**: Template errors, missing context, deduplication
6. **Transaction Handling**: Rollback scenarios, connection failures

### Mock Requirements
- LLM service responses for consistent intent classification
- Database entities with proper relations loaded
- Gmail API responses for email operations
- Template rendering for response generation

## Development Recommendations

### Immediate Architecture Improvements

1. **Break EmailService into smaller services**:
   - `EmailReadService` - Email retrieval and thread management
   - `EmailWriteService` - Email sending and composition
   - `EmailThreadService` - Thread association management

2. **Eliminate circular dependencies**:
   - Introduce coordinator service between EmailService and FileBatchService
   - Use event-driven patterns for loose coupling

3. **Standardize service scopes**:
   - Reduce complexity of mixed singleton/request-scoped patterns
   - Implement proper error boundaries for external dependencies

4. **Template System Optimization**:
   - Implement template caching for performance
   - Add template validation during development
   - Create template testing utilities

### Long-term Architectural Goals

1. **Microservices Migration**: Break monolith into focused services
2. **Event Sourcing**: Implement event sourcing for audit trails
3. **CQRS Pattern**: Separate read/write operations for scalability
4. **Circuit Breaker**: Add resilience patterns for external APIs

## Cross-Reference Index

### Primary Implementation Files
- **Main Processor**: `/apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts`
- **Intent Analysis**: `/apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts`
- **Fragment Rendering**: `/apps/portal-api/src/core-agent/services/shipment-response.service.ts`
- **Context Building**: `/apps/portal-api/src/agent-context/services/shipment-context.service.ts`
- **Handler Registry**: `/apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts`

### Entity Definitions
- **Email**: `/libraries/nest-modules/src/entities/email.entity.ts`
- **Shipment**: `/libraries/nest-modules/src/entities/shipment.entity.ts`
- **Organization**: `/libraries/nest-modules/src/entities/organization.entity.ts`
- **File**: `/libraries/nest-modules/src/entities/file.entity.ts`

### Intent Handlers
- **All Handlers**: `/apps/portal-api/src/core-agent/handlers/`
- **Templates**: `/apps/portal-api/src/core-agent/templates/`

### Schema and Type Definitions
- **Email Intents**: `/libraries/nest-modules/src/types/ai-agent.types.ts`
- **Message Content**: `/apps/portal-api/src/core-agent/schemas/message-content.schema.ts`
- **Response Fragments**: `/apps/portal-api/src/core-agent/types/response-fragment.types.ts`
- **Queue Types**: `/apps/portal-api/src/core-agent/types/queue.types.ts`

### Utility Functions
- **Email Content Generation**: `/apps/portal-api/src/email/utils/generate-email-content.ts`
- **Request Context**: `/apps/portal-api/src/email/utils/generate-request.ts`
- **Concurrency Control**: `/apps/portal-api/src/core-agent/utils/concurrency-limit.util.ts`

This comprehensive documentation provides a complete technical reference for the Claro Email Processing System, covering architecture, service dependencies, template generation, intent handling, and all critical system components for customs automation email processing.