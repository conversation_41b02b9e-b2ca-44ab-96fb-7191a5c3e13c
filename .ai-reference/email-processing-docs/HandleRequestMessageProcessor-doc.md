# HandleRequestMessageProcessor Documentation

## Overview

The `HandleRequestMessageProcessor` is a comprehensive BullMQ processor that serves as the central orchestrator for email intent processing in the Claro customs automation platform. It consolidates the complete email processing pipeline from attachment handling through intent extraction to response generation using a sophisticated fragment-based rendering system.

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts`

**Key Methods**:
- `process()` - Main processing method (line 137)
- `processIntentsToFragments()` - Fragment generation pipeline (line 557)
- `handleSingleIntent()` - Individual intent processing (line 594)
- `sortIntentsByPriority()` - Intent prioritization (line 129)
- `getIntentPriority()` - Priority lookup (line 119)

## Architecture Overview

### Processing Pipeline

```mermaid
graph TD
    A[Email Received] --> B[Load Organization & Email]
    B --> C[Load Attachments & Context]
    C --> D[Generate Message Content]
    D --> E[Extract & Validate Intents]
    E --> F[Double-Pass Intent Refinement]
    F --> G[Combine PROCESS_DOCUMENT Intents]
    G --> H[Sort Intents by Priority]
    H --> I[Find Shipment for Context]
    I --> J[Build Shipment Context]
    J --> K[Process Intents to Fragments]
    K --> L[Render Fragments to HTML]
    L --> M[Send Email Response]
    M --> N[Update Email Status]
```

## Core Dependencies Analysis

### 1. Entity Layer (nest-modules)

#### Email Entity
**Location**: `/home/<USER>/dev/Claro/libraries/nest-modules/src/entities/email.entity.ts`

```typescript
interface Email {
  id: number;
  gmailId: string;
  threadId: string;
  status: EmailStatus;
  from: Array<EmailAddressDto>;
  to: Array<EmailAddressDto>;
  subject: string | null;
  text: string | null;
  html: string | null;
  userIntents: Array<Record<string, any>> | null;
  organization: Organization;
  receiveDate: Date;
}
```

**Key Features**:
- Gmail integration with `gmailId` and `threadId`
- Status tracking through `EmailStatus` enum (see enum definition below)
- Stores extracted `userIntents` from LLM processing (see [Intent Processing Flow](#intent-processing-flow))
- Full-text search capabilities with `search_vector`

#### EmailStatus Enum
```typescript
enum EmailStatus {
  UPLOADING_ATTACHMENTS = "uploading-attachments",
  SAVED = "saved",
  ANALYZING_INTENTS = "analyzing-intents", 
  PROCESSING_INTENTS = "processing-intents",
  AGGREGATING_EMAIL = "aggregating-email",
  RESPONDING = "responding",
  RESPONDED = "responded",
  MANUAL_REVIEW = "manual-review",
  SPAM = "spam",
  SYSTEM_EMAIL_SKIPPED = "system-email-skipped"
}
```

#### Organization Entity
**Location**: `/home/<USER>/dev/Claro/libraries/nest-modules/src/entities/organization.entity.ts`

```typescript
interface Organization {
  id: number;
  name: string;
  organizationType: OrganizationType;
  customsBroker: OrganizationCustomsBroker;
  skipPoaCheck: boolean;
  // Relations
  emails: Promise<Array<Email>>;
  shipments: Promise<Array<Shipment>>;
  users: Promise<Array<User>>;
}
```

#### Shipment Entity
**Location**: `/home/<USER>/dev/Claro/libraries/nest-modules/src/entities/shipment.entity.ts`

```typescript
interface Shipment {
  id: number;
  status: ShipmentStatus;
  trackingStatus: TrackingStatus;
  customsStatus: CustomsStatus;
  hblNumber: string | null;
  mblNumber: string | null;
  cargoControlNumber: string | null;
  // Dates
  etd: Date | null;
  etaPort: Date | null;
  etaDestination: Date | null;
  releaseDate: Date | null;
  // Weight/Volume
  weight: number | null;
  volume: number | null;
  quantity: number | null;
  // Relations  
  organization: Organization;
  documents: Array<Document>;
  containers: Array<Container>;
}
```

#### File Entity
**Location**: `/home/<USER>/dev/Claro/libraries/nest-modules/src/entities/file.entity.ts`

```typescript
interface File {
  id: number;
  path: string;
  name: string;
  parseResult: string;
  parser: string;
  status: FileStatus;
  mimeType: string;
  hash: string;
  batchId: string | null;
  // Relations
  organization: Organization;
  shipment: Shipment | null;
  documents: Array<Document>;
  batch: FileBatch | null;
}
```

#### EMAIL_INTENTS Constant
**Location**: `/home/<USER>/dev/Claro/libraries/nest-modules/src/types/ai-agent.types.ts`

```typescript
const EMAIL_INTENTS = [
  "CREATE_SHIPMENT",
  "UPDATE_SHIPMENT", 
  "CREATE_COMMERCIAL_INVOICE",
  "UPDATE_COMMERCIAL_INVOICE",
  "CREATE_CERTIFICATE_OF_ORIGIN",
  "UPDATE_CERTIFICATE_OF_ORIGIN",
  "GET_SHIPMENT_STATUS",
  "PROCESS_DOCUMENT",
  "REQUEST_RUSH_PROCESSING",
  "REQUEST_MANUAL_PROCESSING", 
  "REQUEST_HOLD_SHIPMENT",
  "REQUEST_CAD_DOCUMENT",
  "REQUEST_RNS_PROOF",
  "DOCUMENTATION_COMING",
  "ACKNOWLEDGE_DOCUMENTS",
  "ACKNOWLEDGE_MISSING_DOCUMENTS",
  "UNSORTED",
  "UNKNOWN", 
  "SPAM"
] as const;
```

### 2. Service Layer

#### EmailService
**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/services/email.service.ts`
**NestJS Scope**: `@Injectable({ scope: Scope.REQUEST })` - **Request Scoped**

**Key Methods**:
```typescript
class EmailService {
  // Core CRUD operations
  async getEmailById(emailId: number, queryRunner?: QueryRunner): Promise<Email>;
  async editEmail(emailId: number, editDto: EditEmailDto, queryRunner?: QueryRunner): Promise<Email>;
  
  // Thread management
  async getEmailThreadShipment(threadId: string): Promise<Shipment | null>;
  async setEmailThreadShipment(threadId: string, shipment: Shipment, queryRunner?: QueryRunner): Promise<void>;
  async getPreviousEmails(emailId: number, queryRunner?: QueryRunner): Promise<Array<Email>>;
  
  // Email sending
  async replyEmail(emailId: number, replyDto: ReplyEmailDto, queryRunner?: QueryRunner): Promise<void>;
  async sendEmail(sendDto: SendEmailDto, queryRunner?: QueryRunner): Promise<void>;
}
```

**Implementation Notes**:
- Request-scoped for user context (see [NestJS Scope Summary](#nestjs-scope-summary))
- Integrates with Gmail API through GmailService
- Supports HTML/text email composition with attachments
- Manages email thread associations to shipments (see [Shipment Discovery Strategy](#shipment-discovery-strategy))

#### FileBatchService
**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/document/services/file-batch.service.ts`
**NestJS Scope**: `@Injectable()` - **Singleton (default)**

**Key Methods**:
```typescript
class FileBatchService {
  async getFileBatch(id: string): Promise<FileBatch>;
  async getFiles(batchId: string, relations?: FindOptionsRelations<File>): Promise<Array<File>>;
  async getFileBatchStatus(id: string, queryRunner?: QueryRunner): Promise<FileBatchStatus>;
  async aggregateFileBatch(id: string): Promise<void>;
  async lockForUpdate(id: string, queryRunner?: QueryRunner): Promise<FileBatch>;
}
```

**Implementation Notes**:
- Manages file batch lifecycle and aggregation
- Coordinates document processing across multiple files (see [Fragment Processing Pipeline](#fragment-processing-pipeline))
- Emits events for aggregation completion
- Provides transaction-safe file access

#### EmailIntentAnalysisService
**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts`
**NestJS Scope**: `@Injectable()` - **Singleton (default)**

**Key Methods**:
```typescript
class EmailIntentAnalysisService {
  // Main analysis pipeline
  async analyzeEmailIntents(emailContent: MessageContent): Promise<ValidatedEmailIntents>; // line 72
  
  // Individual classification  
  async classifyTaskIntent(task: string): Promise<TaskIntent>; // line 234
  
  // Internal pipeline methods
  private async decomposeEmailToTasks(emailContent: MessageContent): Promise<TaskList>; // line 37
  private async classifyTaskIntents(decomposedTasks: TaskList): Promise<ClassifiedTasks>; // line 94
  private async adaptClassifiedTasksToValidatedIntents(classifiedTasks: ClassifiedTasks, emailContent: MessageContent): Promise<ValidatedEmailIntents>; // line 167
}
```

**LLM Integration**:
- Uses `CORE_AGENT_LLM_MODEL_FULL` for complex reasoning (see [LLM Usage](#llm-usage))
- Uses `CORE_AGENT_LLM_MODEL_MINI` for simple classification (see [LLM Usage](#llm-usage))
- Integrates with `AskLLMService` using Zod schemas
- Supports concurrency limiting for API rate limits
- Used by double-pass refinement (see [Double-Pass Intent Refinement](#double-pass-intent-refinement))

#### RNSStatusChangeEmailSender
**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/shipment/senders/rns-status-change-email.sender.ts`
**NestJS Scope**: `@Injectable()` - **Singleton (default)**

**Key Methods**:
```typescript
class RNSStatusChangeEmailSender extends BaseEmailSender {
  async sendRNSStatusChangeEmail(shipment: Shipment, response: CandataRNSResponseDto): Promise<void>;
  async generateCADB64(shipment: Shipment, commercialInvoices: Array<CommercialInvoice>, importer: Importer): Promise<string>;
  
  private async searchForPreviousEmail(shipment: Shipment, queryRunner?: QueryRunner): Promise<Email | null>;
  private async categorizeRNSResponse(response: CandataRNSResponseDto): Promise<RNSResponseCategory>;
}
```

**Implementation Notes**:
- Extends BaseEmailSender for common functionality
- Generates CAD documents using API templates (see CAD document handling in [Response Generation](#response-generation))
- Categorizes RNS responses for appropriate messaging
- Searches for previous status change emails to maintain context

### 3. Fragment Processing System

#### ShipmentResponseService
**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/services/shipment-response.service.ts`
**NestJS Scope**: `@Injectable()` - **Singleton (default)**

**Key Methods**:
```typescript
class ShipmentResponseService {
  // Main rendering pipeline
  async renderFragments(fragments: ResponseFragment[], context: ShipmentContext): Promise<string>;
  
  // Fragment processing
  private deduplicateFragments(fragments: ResponseFragment[]): ResponseFragment[];
  private sortFragments(fragments: ResponseFragment[]): ResponseFragment[];
  private mergeContexts(baseContext: ShipmentContext, fragmentContext?: FragmentContext): any;
  private renderSingleFragment(fragment: ResponseFragment, context: any): Promise<string>;
  private joinFragments(renderedParts: string[]): string;
}
```

**Template Processing**:
- Deduplicates fragments by template name (see `deduplicateFragments()` method)
- Special handling for stackable templates (e.g., `main-messages`)
- Sorts by priority and predefined template order (see `sortFragments()` method)
- Merges fragment-specific context with base shipment context (see `mergeContexts()` method)
- Renders using TemplateManagerService (see [TemplateManagerService](#templatemanagerservice))

#### ShipmentContextService
**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/agent-context/services/shipment-context.service.ts`
**NestJS Scope**: `@Injectable()` - **Singleton (default)**

**Key Methods**:
```typescript
class ShipmentContextService {
  // Context building
  async buildContext(shipmentId: number, organizationId: number, queryRunner?: QueryRunner): Promise<ShipmentContextWithServices>;
  
  // Service injection for intent handlers
  injectServices(context: ShipmentContextWithServices, services: {
    emailService?: IEmailService;
    rnsStatusChangeEmailSender?: IRNSStatusChangeEmailSender;
    // ... other services
  }): void;
}
```

**Context Structure**:
```typescript
interface ShipmentContext {
  // Raw data
  shipment: Shipment;
  compliance: ValidateShipmentComplianceResponseDto;
  organization: Organization;
  
  // Business rules (evaluated safely)
  canRush: boolean;
  isReleased: boolean;
  hasValidationIssues: boolean;
  missingDocuments: string[];
  missingFields: string[];
  
  // Formatted data
  formattedStatus: string;
  formattedETA: string;
  documentStatus: Array<DocumentStatusInfo>;
  
  // Service access (internal)
  _services: {
    emailService?: IEmailService;
    rnsStatusChangeEmailSender?: IRNSStatusChangeEmailSender;
  };
}
```

#### IntentHandlerRegistry
**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts`
**NestJS Scope**: `@Injectable()` - **Singleton (default)**

**Key Methods**:
```typescript
class IntentHandlerRegistry implements OnModuleInit {
  // Handler management
  getHandler(intentType: (typeof EMAIL_INTENTS)[number]): IntentHandler | undefined;
  hasHandler(intentType: (typeof EMAIL_INTENTS)[number]): boolean;
  getAllClassificationMeta(): IntentClassificationMeta[];
  
  // Registry status
  getRegistrationStatus(): RegistrationStatus;
  getMissingHandlers(): string[];
  
  private registerHandlers(): void;
}
```

**Handler Interface**:
```typescript
interface IntentHandler {
  classificationMeta: IntentClassificationMeta;
  handle(intent: ValidatedEmailAction, context: ShipmentContextWithServices): Promise<ResponseFragment[]>;
}

interface IntentClassificationMeta {
  intent: (typeof EMAIL_INTENTS)[number]; // See EMAIL_INTENTS constant above
  description: string;
  examples: string[];
  priority: number; // Used by Intent Priority System
}
```

**See Also**: [Intent Priority System](#intent-priority-system) for priority handling details
```

#### TemplateManagerService
**Location**: `/home/<USER>/dev/Claro/libraries/nest-modules/src/template-manager/template-manager.service.ts`
**NestJS Scope**: `@Injectable()` - **Singleton (default)**

**Key Methods**:
```typescript
class TemplateManagerService {
  async renderTemplate(templateName: string, context: any): Promise<string>;
  private loadTemplate(templateName: string): string;
  private configureNunjucks(): Environment;
}
```

**Implementation Notes**:
- Manages Nunjucks template rendering for email responses
- Provides template loading and caching functionality
- Supports dynamic context injection for personalized content
- Used for fallback responses and fragment rendering (see [Fragment System](#fragment-system))
- Template locations: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/templates/`

#### NestJS Scope Summary

The HandleRequestMessageProcessor's dependency injection follows these scope patterns:

**Request Scoped Services (1):**
- `EmailService` - Requires authenticated request context for user/organization-specific operations

**Singleton Services (7):**
- `FileBatchService` - File batch management (stateless)
- `EmailIntentAnalysisService` - LLM-based intent classification (stateless)
- `RNSStatusChangeEmailSender` - Email sending service (stateless)
- `ShipmentResponseService` - Fragment rendering (stateless)
- `ShipmentContextService` - Context building (stateless)
- `IntentHandlerRegistry` - Intent handler registry (shared singleton)
- `TemplateManagerService` - Template rendering (stateless with caching)

**Scope Rationale:**
- **Request Scoped**: Only used where user authentication context is required (see `generateRequest()` utility)
- **Singleton**: Efficient for stateless business logic services and shared registries
- **No Transient**: All services can be safely shared (no per-injection instance needed)

This design ensures optimal performance while maintaining proper security boundaries through request-scoped user context where needed.

**Service Resolution**: See `resolveServices()` method at line 749 in the main processor for how these scoped services are resolved during processing.

**Context Management**: The processor uses NestJS's `ContextIdFactory` and `ModuleRef` to manage request contexts for proper service scoping across the processing pipeline.

### 4. Utility Functions

#### generateEmailContent
**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/utils/generate-email-content.ts`

```typescript
function generateEmailContent(
  currentEmail: Email,
  previousEmails: Array<Email>, 
  attachments: Array<File>
): EmailContent {
  return {
    fromAddresses: currentEmail.from.map(formatEmailAddress),
    subject: currentEmail.subject || null,
    text: currentEmail.text || currentEmail.html || null,
    emailHistories: previousEmails.map(formatEmailHistory),
    attachments: attachments.flatMap(extractDocumentData)
  };
}
```

**Document Data Extraction**:
- Converts document fields to appropriate data types (boolean, number, datetime, etc.)
- Handles JSON parsing for complex field types
- Provides structured data for LLM analysis (see [EmailIntentAnalysisService](#emailintentanalysisservice))

#### generateRequest
**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/utils/generate-request.ts`

```typescript
function generateRequest(
  currentRequest: AuthenticatedRequest | null,
  organization: Organization,
  permission = UserPermission.BACKOFFICE_ADMIN
): AuthenticatedRequest {
  // Creates synthetic request context for request-scoped services
  return currentRequest?.user?.organization?.id ? 
    currentRequest : 
    { user: { permission, organization } };
}
```

### 5. Schema & Type Definitions

#### MessageContent Schema
**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/schemas/message-content.schema.ts`

```typescript
interface MessageContent {
  subject?: string | null;
  text: string;
  attachments?: MessageAttachment[];
  messageHistory?: string[];
}

interface MessageAttachment {
  filename: string;
  contentType?: string;
  extractedData?: Record<string, string | number | boolean>;
}
```

#### ValidatedEmailAction Schema
**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/schemas/validated-email-intents.schema.ts`

```typescript
interface ValidatedEmailAction {
  intent: (typeof EMAIL_INTENTS)[number];
  instructions?: string[];
  shipmentReference?: {
    hblNumber?: string;
    mblNumber?: string;
    cargoControlNumber?: string;
  };
  shippingData?: ShippingData;
  attachments?: Array<{
    filename: string;
    documentType?: string;
    extractedData?: Record<string, any>;
  }>;
}
```

#### ResponseFragment Types
**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/types/response-fragment.types.ts`

```typescript
interface ResponseFragment {
  template: string;
  priority?: number;
  fragmentContext?: FragmentContext;
}

interface FragmentContext {
  errorMessage?: string;
  documentResults?: DocumentProcessingResult[];
  missingFieldsFormatted?: string;
  success?: boolean;
  error?: string | boolean;
  [key: string]: unknown;
}

interface ContextSideEffects {
  documentProcessing?: DocumentProcessingResult[];
  cadDocument?: {
    fileName: string;
    mimeType: string; 
    b64Data: string;
  };
  rnsProofData?: {
    content: string;
    releaseDate: string | null;
  };
  backofficeAlerts?: {
    rushProcessingSent?: boolean;
    manualProcessingSent?: boolean;
    holdShipmentSent?: boolean;
  };
}
```

#### Queue Types
**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/types/queue.types.ts`

```typescript
enum CoreAgentQueueName {
  IDENTIFY_SHIPMENT = "core-agent.identify-shipment",
  HANDLE_REQUEST_MESSAGE = "core-agent.handle-request-message", 
  EVENT_EMITTER = "core-agent.event-emitter"
}

interface HandleRequestMessageJobData {
  emailId: number;
  organizationId: number;
}

const DEFAULT_WORKER_OPTIONS: NestWorkerOptions = {
  concurrency: 3,
  limiter: { max: 100, duration: 1000 }
};
```

## HandleRequestMessageProcessor Implementation

### Class Structure

```typescript
@Processor({ name: CoreAgentQueueName.HANDLE_REQUEST_MESSAGE }, DEFAULT_WORKER_OPTIONS)
export class HandleRequestMessageProcessor extends WorkerHost {
  constructor(
    @InjectDataSource() private readonly dataSource: DataSource,
    private readonly moduleRef: ModuleRef,
    private readonly eventEmitter: EventEmitter2,
    private readonly emailIntentAnalysisService: EmailIntentAnalysisService,
    private readonly shipmentResponseService: ShipmentResponseService,
    private readonly shipmentContextService: ShipmentContextService,
    private readonly intentHandlerRegistry: IntentHandlerRegistry,
    private readonly templateManagerService: TemplateManagerService
  ) {
    super();
  }
}
```

### Intent Priority System

**Implementation**: See `INTENT_PRIORITIES` property at line 92 in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts`

The processor implements a sophisticated priority system to ensure intents are executed in the correct order:

```typescript
private readonly INTENT_PRIORITIES = {
  PROCESS_DOCUMENT: 1,        // Must run first to update status
  GET_SHIPMENT_STATUS: 2,     // Uses current status
  REQUEST_RUSH_PROCESSING: 2, // Uses current status
  REQUEST_CAD_DOCUMENT: 2,    // Uses current status
  REQUEST_RNS_PROOF: 2,       // Uses current status
  UPDATE_SHIPMENT: 2,         // May depend on processed documents
  CREATE_SHIPMENT: 2,         // May depend on processed documents
  UPDATE_COMMERCIAL_INVOICE: 2, // May depend on processed documents
  CREATE_COMMERCIAL_INVOICE: 2, // May depend on processed documents
  UPDATE_CERTIFICATE_OF_ORIGIN: 2, // May depend on processed documents
  CREATE_CERTIFICATE_OF_ORIGIN: 2, // May depend on processed documents
  DOCUMENTATION_COMING: 3,    // Acknowledgment handlers
  ACKNOWLEDGE_DOCUMENTS: 3,   // Acknowledgment handlers
  ACKNOWLEDGE_MISSING_DOCUMENTS: 3,
  REQUEST_MANUAL_PROCESSING: 3, // Fallback handlers
  REQUEST_HOLD_SHIPMENT: 3,   // Fallback handlers
  UNSORTED: 99,               // Lowest priority
  UNKNOWN: 99,                // Lowest priority
  SPAM: 99                    // Lowest priority
} as const;
```

**Related Methods**:
- `getIntentPriority()` - Priority lookup logic (line 119)
- `sortIntentsByPriority()` - Sorting implementation (line 129)

### Main Processing Method

**Implementation**: See `process()` method at line 137 in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts`

```typescript
async process(job: HandleRequestMessageJob): Promise<void> {
  const { emailId, organizationId } = job.data;
  
  const queryRunner = this.dataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // 1. Load organization and create request context
    const organization = await this.loadOrganization(organizationId, queryRunner);
    const contextId = ContextIdFactory.create();
    const requestContext = generateRequest(null, organization);
    this.moduleRef.registerRequestByContextId(requestContext, contextId);

    // 2. Resolve request-scoped services
    const services = await this.resolveServices(contextId);

    // 3. Load email with validation
    const email = await this.loadEmailWithValidation(emailId, services.emailService, queryRunner);

    // 4. Load attachments and previous emails
    const attachments = await this.loadAttachments(email, services.fileBatchService);
    const previousEmails = await services.emailService.getPreviousEmails(emailId, queryRunner);

    // 5. Generate message content and analyze intents
    const messageContent = this.generateMessageContent(email, previousEmails, attachments);
    const validatedIntents = await this.analyzeEmailIntents(messageContent);

    // 6. Process intents through fragment system
    await this.processIntentsWithFragmentSystem(
      validatedIntents.intents,
      email,
      organization,
      contextId,
      services,
      queryRunner
    );

    // 7. Update email status and commit transaction
    await services.emailService.editEmail(emailId, { 
      status: EmailStatus.RESPONDED,
      userIntents: validatedIntents.intents 
    }, queryRunner);

    await queryRunner.commitTransaction();
    
  } catch (error) {
    await queryRunner.rollbackTransaction();
    this.logger.error(`Failed to process email ${emailId}: ${error.message}`, error.stack);
    throw error;
  } finally {
    await queryRunner.release();
  }
}
```

**Helper Methods Referenced**:
- `loadOrganization()` - Organization loading with relations (line 722)
- `resolveServices()` - Request-scoped service resolution (line 749)
- `loadEmailWithValidation()` - Email loading with status checks (line 775)
- `loadAttachments()` - File batch attachment loading (line 806)
- `generateMessageContent()` - Content preparation for LLM (line 833)
- `analyzeEmailIntents()` - Intent analysis pipeline (line 846)
- `processIntentsWithFragmentSystem()` - Fragment processing (line 385)

### Fragment Processing Pipeline

**Implementation**: See `processIntentsWithFragmentSystem()` method at line 385 in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts`

```typescript
private async processIntentsWithFragmentSystem(
  intents: ValidatedEmailAction[],
  email: Email,
  organization: Organization,
  contextId: any,
  services: ResolvedServices,
  queryRunner: QueryRunner
): Promise<void> {
  // 1. Find shipment for context building
  const shipment = await this.findShipmentForEmail(email, contextId, services.emailService, queryRunner);
  if (!shipment) {
    await this.sendFallbackResponse(email, services.emailService, queryRunner);
    return;
  }

  // 2. Build comprehensive shipment context
  const context = await this.shipmentContextService.buildContext(shipment.id, organization.id);
  this.shipmentContextService.injectServices(context, {
    emailService: services.emailService,
    rnsStatusChangeEmailSender: services.rnsStatusChangeEmailSender
  });

  // 3. Filter meaningful intents and process to fragments
  const meaningfulIntents = intents.filter(intent => 
    !["UNSORTED", "SPAM", "UNKNOWN"].includes(intent.intent)
  );
  
  const allFragments = await this.processIntentsToFragments(meaningfulIntents, context, email);

  // 4. Render fragments into consolidated response
  const responseHtml = await this.shipmentResponseService.renderFragments(allFragments, context);

  // 5. Check for empty response and send
  if (this.isEmptyResponse(responseHtml)) {
    await this.sendFallbackResponse(email, services.emailService, queryRunner);
    return;
  }

  await this.sendFragmentResponse(email, responseHtml, allFragments, services.emailService, queryRunner);
}
```

**Helper Methods Referenced**:
- `findShipmentForEmail()` - Shipment discovery strategy (line 447)
- `sendFallbackResponse()` - Manual review escalation (line 701)
- `processIntentsToFragments()` - Intent to fragment conversion (line 557)
- `isEmptyResponse()` - Response validation (line 666)
- `sendFragmentResponse()` - Email response delivery (line 679)

**External Service References**:
- `ShipmentContextService.buildContext()` - Context building in `/home/<USER>/dev/Claro/apps/portal-api/src/agent-context/services/shipment-context.service.ts`
- `ShipmentResponseService.renderFragments()` - Fragment rendering in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/services/shipment-response.service.ts`

### Intent Processing

**Primary Method**: `processIntentsToFragments()` at line 557
**Handler Resolution**: `handleSingleIntent()` at line 594

```typescript
private async processIntentsToFragments(
  intents: ValidatedEmailAction[],
  context: ShipmentContextWithServices,
  email: Email
): Promise<ResponseFragment[]> {
  const allFragments: ResponseFragment[] = [];

  for (const intent of intents) {
    this.logger.log(`Processing intent: ${intent.intent}`);
    
    try {
      const fragments = await this.handleSingleIntent(intent, context);
      allFragments.push(...fragments);
      
      // Log document processing results for tracking
      this.logDocumentProcessingResults(intent, fragments);
      
    } catch (error) {
      this.logger.error(`Error handling intent ${intent.intent}: ${error.message}`, error.stack);
      // Skip failed intents - other intents can still succeed
    }
  }

  return allFragments;
}

private async handleSingleIntent(intent: ValidatedEmailAction, context: ShipmentContextWithServices): Promise<ResponseFragment[]> {
  const handler = this.intentHandlerRegistry.getHandler(intent.intent);
  
  if (!handler) {
    this.logger.warn(`No handler found for intent: ${intent.intent}`);
    return [this.createFallbackFragment(intent)];
  }

  return await handler.handle(intent, context);
}
```

**Intent Handler Implementations**:
All handlers implement the `IntentHandler` interface and are located in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/handlers/`:
- `ProcessDocumentHandler` - `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/handlers/process-document.handler.ts`
- `GetShipmentStatusHandler` - `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts`
- `RequestRushProcessingHandler` - `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts`
- `RequestCADDocumentHandler` - `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/handlers/request-cad-document.handler.ts`
- `RequestRNSProofHandler` - `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/handlers/request-rns-proof.handler.ts`
- And others...

**Registry Location**: Intent handler registration occurs in `IntentHandlerRegistry` at `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts`

**Logging Methods**:
- `logIntentDetails()` - Detailed intent logging (line 608)
- `logDocumentProcessingResults()` - Document processing status tracking (line 641)

### Double-Pass Intent Refinement

**Implementation**: See `refineProblematicIntents()` method at line 889 in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts`

The processor includes sophisticated intent refinement to improve classification accuracy:

```typescript
private async refineProblematicIntents(
  intents: ValidatedEmailAction[],
  messageContent: MessageContent
): Promise<ValidatedEmailAction[]> {
  const refinedIntents: ValidatedEmailAction[] = [];
  const problematicIntentTypes = ["UNSORTED", "SPAM", "UNKNOWN"];

  for (const intent of intents) {
    if (problematicIntentTypes.includes(intent.intent)) {
      // Re-classify each instruction separately for better accuracy
      const reClassifiedActions: ValidatedEmailAction[] = [];
      
      for (const instruction of intent.instructions || []) {
        const taskIntent = await this.emailIntentAnalysisService.classifyTaskIntent(instruction);
        const reClassifiedAction = this.mapTaskIntentToEmailIntent(taskIntent, instruction, intent);
        reClassifiedActions.push(reClassifiedAction);
      }

      // Filter improved classifications
      const improvedClassifications = reClassifiedActions.filter(
        action => !problematicIntentTypes.includes(action.intent)
      );

      if (improvedClassifications.length > 0) {
        this.logger.log(`Double-pass SUCCESS: Reclassified ${improvedClassifications.length} instructions`);
        refinedIntents.push(...improvedClassifications);
      } else {
        refinedIntents.push(intent); // Keep original if no improvement
      }
    } else {
      refinedIntents.push(intent); // Keep non-problematic intents
    }
  }

  return refinedIntents;
}
```

**Related Methods**:
- `mapTaskIntentToEmailIntent()` - Task intent to email action mapping (line 935)
- `combineProcessDocumentIntents()` - Document intent consolidation (line 863)

**External Service Integration**:
- `EmailIntentAnalysisService.classifyTaskIntent()` - Individual task classification at line 234 in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts`

### Shipment Discovery Strategy

**Implementation**: See `findShipmentForEmail()` method at line 447 in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts`

```typescript
private async findShipmentForEmail(
  email: Email,
  contextId: any,
  emailService: EmailService,
  queryRunner: QueryRunner
): Promise<Shipment | null> {
  // Strategy 1: Try to get shipment from email thread
  const threadShipment = await this.tryGetThreadShipment(email, emailService);
  if (threadShipment) return threadShipment;

  // Strategy 2: Check FileBatch for shipment and associate with thread
  const fileBatchShipment = await this.tryGetFileBatchShipment(email, contextId, emailService, queryRunner);
  if (fileBatchShipment) return fileBatchShipment;

  this.logger.warn(`No shipment found for email ${email.id}`);
  return null;
}
```

**Strategy Implementation Methods**:
- `tryGetThreadShipment()` - Thread-based shipment lookup (line 460)
- `tryGetFileBatchShipment()` - File batch shipment discovery (line 470)

**External Service Integration**:
- `EmailService.getEmailThreadShipment()` - Thread shipment retrieval in `/home/<USER>/dev/Claro/apps/portal-api/src/email/services/email.service.ts`
- `EmailService.setEmailThreadShipment()` - Thread shipment association in `/home/<USER>/dev/Claro/apps/portal-api/src/email/services/email.service.ts`

### Response Generation

**Implementation**: See `sendFragmentResponse()` method at line 679 in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts`

```typescript
private async sendFragmentResponse(
  email: Email,
  responseHtml: string,
  fragments: ResponseFragment[],
  emailService: EmailService,
  queryRunner: QueryRunner
): Promise<void> {
  try {
    // Collect side effects from fragments (CAD documents, RNS proofs, etc.)
    const sideEffects = this.collectSideEffectsFromFragments(fragments);

    // Handle attachments from side effects
    const attachments = [];
    if (sideEffects?.cadDocument) {
      attachments.push({
        fileName: sideEffects.cadDocument.fileName || "CAD.pdf",
        mimeType: sideEffects.cadDocument.mimeType || "application/pdf",
        b64Data: sideEffects.cadDocument.b64Data
      });
    }

    // Send email response with attachments
    await emailService.replyEmail(email.id, {
      html: responseHtml,
      attachments
    }, queryRunner);

    this.logger.log(`Sent response for email ${email.id} with ${attachments.length} attachments`);
    
  } catch (error) {
    this.logger.error(`Failed to send response for email ${email.id}: ${error.message}`);
    // Fall back to manual review when email sending fails
    await this.sendFallbackResponse(email, emailService, queryRunner);
  }
}
```

**Helper Methods**:
- `collectSideEffectsFromFragments()` - Side effect extraction (line 709)
- `sendFallbackResponse()` - Fallback handling (line 701)
- `isEmptyResponse()` - Response validation (line 666)

**External Service Integration**:
- `EmailService.replyEmail()` - Email sending in `/home/<USER>/dev/Claro/apps/portal-api/src/email/services/email.service.ts`

**Template Processing**:
Response templates are located in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/templates/`:
- `eta.njk` - ETA information template
- `shipping-status.njk` - Shipping status template
- `customs-status.njk` - Customs status template
- `compliance-errors.njk` - Validation errors template
- `contact-support.njk` - Support contact template
- And others...

### Error Handling and Fallbacks

The processor implements comprehensive error handling with multiple fallback mechanisms:

1. **Intent Analysis Failure**: Falls back to no-intents response
2. **Shipment Not Found**: Escalates to manual review
3. **Fragment Rendering Failure**: Skips failed fragments, continues with others
4. **Email Sending Failure**: Falls back to manual review
5. **Empty Response Detection**: Escalates to manual review

**Implementation**: See `sendFallbackResponse()` method at line 701 in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts`

```typescript
private async sendFallbackResponse(
  email: Email,
  emailService: EmailService, 
  queryRunner: QueryRunner
): Promise<void> {
  await emailService.editEmail(email.id, {
    status: EmailStatus.MANUAL_REVIEW,
    error: "No shipment found for email thread"
  }, queryRunner);

  this.eventEmitter.emit(EmailEvent.EMAIL_MANUAL_REVIEW_REQUIRED, 
    new EmailManualReviewRequiredEvent({
      emailId: email.id,
      organizationId: email.organization.id,
      reviewReason: EmailManualReviewReason.NO_INTENTS
    })
  );
}
```

**Event System Integration**:
- `EmailEvent` types defined in `/home/<USER>/dev/Claro/apps/portal-api/src/email/types/event.types.ts`
- `EmailManualReviewRequiredEvent` defined in `/home/<USER>/dev/Claro/apps/portal-api/src/email/dto/event.dto.ts`
- `EmailManualReviewReason` enum in `/home/<USER>/dev/Claro/apps/portal-api/src/email/types/email.types.ts`
```

## Agent Integration Patterns

### LLM Usage

**Model Constants**: Defined in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/constants/llm.constants.ts`

The processor leverages multiple LLM models for different tasks:

- **Full Model** (`CORE_AGENT_LLM_MODEL_FULL`): Complex reasoning, intent analysis, document processing
- **Mini Model** (`CORE_AGENT_LLM_MODEL_MINI`): Simple classification, validation tasks

**LLM Service Integration**:
- Primary service: `AskLLMService` in `/home/<USER>/dev/Claro/apps/portal-api/src/llm/ask-llm/services/ask-llm.service.ts`
- Prompt templates: Located in `/home/<USER>/dev/Claro/apps/portal-api/src/llm/prompt-templates/`
- Concurrency management: `limitConcurrency()` utility in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/utils/concurrency-limit.util.ts`

### Intent Processing Flow

1. **Task Decomposition**: Break email into discrete task strings (see `decomposeEmailToTasks()` in [EmailIntentAnalysisService](#emailintentanalysisservice))
2. **Intent Classification**: Classify each task using handler registry metadata (see [IntentHandlerRegistry](#intenthandlerregistry))
3. **Validation**: Convert to structured ValidatedEmailAction format
4. **Refinement**: Double-pass classification for problematic intents (see [Double-Pass Intent Refinement](#double-pass-intent-refinement))
5. **Combination**: Merge related document processing intents (see `combineProcessDocumentIntents()` line 863)
6. **Prioritization**: Sort by business priority rules (see [Intent Priority System](#intent-priority-system))
7. **Execution**: Process through fragment-based system (see [Fragment Processing Pipeline](#fragment-processing-pipeline))

### Fragment System

**Core Implementation**: `ShipmentResponseService` in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/services/shipment-response.service.ts`

The fragment system provides modular, composable response generation:

- **Fragments**: Independent response pieces with templates and context
  - Type definition: `ResponseFragment` in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/types/response-fragment.types.ts`
- **Templates**: Nunjucks templates for rendering HTML responses
  - Template location: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/templates/`
  - Template manager: `TemplateManagerService` in `/home/<USER>/dev/Claro/libraries/nest-modules/src/template-manager/template-manager.service.ts`
- **Context Merging**: Combines shipment context with fragment-specific data
  - Context building: `ShipmentContextService` in `/home/<USER>/dev/Claro/apps/portal-api/src/agent-context/services/shipment-context.service.ts`
- **Deduplication**: Handles overlapping fragments intelligently
  - Logic in `ShipmentResponseService.deduplicateFragments()`
- **Priority Ordering**: Ensures logical response flow
  - Implementation in `ShipmentResponseService.sortFragments()`

**Fragment Template Examples**:
- Status templates: `shipping-status.njk`, `customs-status.njk`, `release-status.njk`
- Information templates: `eta.njk`, `transaction-number.njk`
- Error templates: `compliance-errors.njk`, `system-unavailable.njk`
- Support templates: `contact-support.njk`, `manual-processing-requested.njk`

## Error Handling and Fallback Mechanisms

### Transaction Management
- All processing occurs within database transactions
- Rollback on any critical failure
- Proper resource cleanup in finally blocks

### Intent Processing Errors
- Individual intent failures don't stop processing
- Failed intents are logged and skipped
- Other intents continue processing

### Response Generation Errors
- Fragment rendering failures are logged and skipped
- Empty responses trigger manual review escalation
- Email sending failures fall back to manual review

### Manual Review Triggers
- No meaningful intents found
- No shipment association possible (see [Shipment Discovery Strategy](#shipment-discovery-strategy))
- Empty or greeting-only responses (see `isEmptyResponse()` method line 666)
- Critical processing failures

**Implementation**: Manual review is triggered through `sendFallbackResponse()` method (line 701) which:
- Sets email status to `MANUAL_REVIEW`
- Emits `EmailManualReviewRequiredEvent`
- Logs the escalation reason

## Performance Considerations

### Concurrency Controls
- Worker concurrency: 3 concurrent jobs (see `DEFAULT_WORKER_OPTIONS` in [Queue Types](#queue-types))
- Rate limiting: 100 requests per second (see `DEFAULT_WORKER_OPTIONS`)
- Concurrency limiting for LLM API calls (see `limitConcurrency` utility)

**Configuration**: Concurrency settings are defined in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/types/queue.types.ts`

### Database Optimization
- Strategic use of query runners for transaction consistency (see transaction handling in `process()` method)
- Selective field loading to minimize data transfer (see `FIND_*_RELATIONS` constants)
- Proper indexing on search fields (HBL, MBL, etc.) for shipment discovery

**Related**: See [Shipment Discovery Strategy](#shipment-discovery-strategy) for database query optimization patterns

### Memory Management
- Request-scoped services for proper cleanup (see [NestJS Scope Summary](#nestjs-scope-summary))
- Streaming for large document processing (see [FileBatchService](#filebatchservice))
- Proper connection pooling (see TypeORM DataSource injection)

## Testing Considerations

### Key Test Scenarios
1. **Single Intent Processing**: Each intent type with valid data
   - Test all handlers in `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/handlers/`
2. **Multiple Intent Combinations**: Document processing + status requests
   - Test priority ordering in `sortIntentsByPriority()` (line 129)
3. **Error Scenarios**: No shipment found, malformed intents, LLM failures  
   - Test fallback handling in `sendFallbackResponse()` (line 701)
4. **Edge Cases**: Empty emails, spam detection, circular processing
   - Test double-pass refinement in `refineProblematicIntents()` (line 889)
5. **Fragment Rendering**: Template errors, missing context, deduplication
   - Test `ShipmentResponseService.renderFragments()` integration
6. **Transaction Handling**: Rollback scenarios, connection failures
   - Test database transaction logic in `process()` method (line 137)

**Testing Utilities**:
- E2E testing script: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/testing/run-e2e-with-logs.sh`
- Database query helper: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/testing/db-query.js`

### Mock Requirements
- LLM service responses for consistent intent classification (see [EmailIntentAnalysisService](#emailintentanalysisservice))
- Database entities with proper relations loaded (see entity definitions in [Core Dependencies Analysis](#core-dependencies-analysis))
- Gmail API responses for email operations (see [EmailService](#emailservice))
- Template rendering for response generation (see [Fragment System](#fragment-system))

### Integration Points
- Queue system for job processing (see [Queue Types](#queue-types))
- Database transactions for consistency (see transaction handling in `process()` method line 137)
- External LLM APIs for intent analysis (see [EmailIntentAnalysisService](#emailintentanalysisservice))
- Gmail API for email operations (see [EmailService](#emailservice))
- Template system for response rendering (see [Fragment System](#fragment-system))

## Security Considerations

### Input Validation
- Email content sanitization before LLM processing (see `generateMessageContent()` utility)
- Attachment type and size validation (see [FileBatchService](#filebatchservice))
- Organization-scoped data access enforcement (see request context generation)

### Data Protection
- No sensitive data in logs (PII, financial information) - see logging methods for sanitization
- Proper access control through request context (see `generateRequest()` utility)
- Secure template rendering with input escaping (see [TemplateManagerService](#templatemanagerservice))

### External API Security
- Rate limiting for LLM API calls (see concurrency limiting utilities)
- Secure credential management (see LLM service configuration)
- Error message sanitization (see error handling in [Error Handling and Fallback Mechanisms](#error-handling-and-fallback-mechanisms))

## Monitoring and Observability

### Logging Strategy
- Structured logging with correlation IDs (see job ID tracking in `process()` method)
- Performance metrics for processing stages (see timing logs throughout pipeline)
- Document processing status tracking (see `logDocumentProcessingResults()` method)
- Intent classification success rates (see double-pass refinement logging)

### Key Metrics
- Processing time per email (tracked in `process()` method)
- Intent classification accuracy (see double-pass refinement statistics)
- Fragment rendering success rates (see [Fragment Processing Pipeline](#fragment-processing-pipeline))
- Manual review escalation rates (see [Manual Review Triggers](#manual-review-triggers))
- Email response delivery success (see [Response Generation](#response-generation))

### Debug Information
- Intent priority assignments (see `logIntentDetails()` method line 608)
- Fragment deduplication results (logged in `ShipmentResponseService`)
- Shipment discovery strategy outcomes (see [Shipment Discovery Strategy](#shipment-discovery-strategy))
- LLM response parsing results (see `EmailIntentAnalysisService` debug logging)
- Document processing status (see `logDocumentProcessingResults()` line 641)

This comprehensive documentation provides a complete technical reference for the HandleRequestMessageProcessor and its entire dependency ecosystem, covering all aspects from entity relationships through business logic to error handling and performance considerations.

## Cross-Reference Index

### Primary Implementation Files
- **Main Processor**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts`
- **Intent Analysis**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts`
- **Fragment Rendering**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/services/shipment-response.service.ts`
- **Context Building**: `/home/<USER>/dev/Claro/apps/portal-api/src/agent-context/services/shipment-context.service.ts`
- **Handler Registry**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts`

### Entity Definitions
- **Email**: `/home/<USER>/dev/Claro/libraries/nest-modules/src/entities/email.entity.ts`
- **Shipment**: `/home/<USER>/dev/Claro/libraries/nest-modules/src/entities/shipment.entity.ts`
- **Organization**: `/home/<USER>/dev/Claro/libraries/nest-modules/src/entities/organization.entity.ts`
- **File**: `/home/<USER>/dev/Claro/libraries/nest-modules/src/entities/file.entity.ts`

### Intent Handlers Directory
- **All Handlers**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/handlers/`
- **Templates**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/templates/`

### Schema and Type Definitions
- **Email Intents**: `/home/<USER>/dev/Claro/libraries/nest-modules/src/types/ai-agent.types.ts`
- **Message Content**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/schemas/message-content.schema.ts`
- **Validated Actions**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/schemas/validated-email-intents.schema.ts`
- **Response Fragments**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/types/response-fragment.types.ts`
- **Queue Types**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/types/queue.types.ts`

### Utility Functions
- **Email Content Generation**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/utils/generate-email-content.ts`
- **Request Context**: `/home/<USER>/dev/Claro/apps/portal-api/src/email/utils/generate-request.ts`
- **Concurrency Control**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/utils/concurrency-limit.util.ts`

### Testing Infrastructure
- **E2E Tests**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/testing/run-e2e-with-logs.sh`
- **Database Queries**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/testing/db-query.js`