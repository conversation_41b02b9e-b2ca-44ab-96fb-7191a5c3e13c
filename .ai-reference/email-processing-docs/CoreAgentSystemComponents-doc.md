# Core-Agent System Components Documentation

## Overview

This document provides comprehensive coverage of the core-agent system components that were not fully covered in the HandleRequestMessageProcessor and ClaroEmailProcessingSystem documentation. The core-agent system represents a sophisticated AI-driven email processing platform with multiple processors, specialized services, event-driven architecture, and extensive testing infrastructure.

**System Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/`

## Additional Processors

### IdentifyShipmentProcessor

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/processors/identify-shipment.processor.ts`

**Purpose**: Processes incoming emails to identify and associate them with existing shipments using LLM-based analysis and database lookup.

**Queue Configuration**:
```typescript
@Processor({ name: CoreAgentQueueName.IDENTIFY_SHIPMENT }, DEFAULT_WORKER_OPTIONS)
export class IdentifyShipmentProcessor extends WorkerHost {
  concurrency: 3,
  limiter: { max: 100, duration: 1000 }
}
```

**Key Methods**:
- `process(job)` - Main processing method for shipment identification
- `processEmailForShipmentIdentification()` - Core identification logic
- `findShipmentFromEmail()` - Multi-strategy shipment discovery
- `associateEmailWithShipment()` - Thread association management

**Processing Pipeline**:
1. **Email Loading**: Retrieves email with organization context
2. **Content Analysis**: Uses LLM to extract shipment identifiers (HBL, MBL, CCN)
3. **Database Lookup**: Searches for matching shipments using extracted identifiers
4. **Thread Association**: Associates email thread with identified shipment
5. **Downstream Triggering**: Queues HandleRequestMessage job for intent processing

**Integration Points**:
- Uses `ShipmentIdentifierService` for LLM-based identifier extraction
- Integrates with `EmailService` for thread management
- Emits events for downstream processing coordination

### EventEmitterProcessor

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/processors/event-emitter.processor.ts`

**Purpose**: Handles asynchronous event emission for cross-module communication and state synchronization.

**Key Features**:
- **Event Coordination**: Manages event emission timing and ordering
- **Error Handling**: Provides fallback mechanisms for failed event processing
- **State Synchronization**: Ensures consistent state across distributed components
- **Queue Integration**: Processes events asynchronously to prevent blocking

**Event Types Handled**:
- Email processing status changes
- Document processing completion
- Shipment state updates
- Manual review escalations

## Specialized Services

### AgentToolsService

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/services/agent-tools.service.ts`

**Purpose**: Provides specialized LLM tools for database operations and business logic execution.

**Available Tools**:
1. **Database Query Tool** - Executes safe SQL queries with organization scoping
2. **Shipment Lookup Tool** - Finds shipments by various identifiers
3. **Document Retrieval Tool** - Accesses document metadata and content
4. **Status Check Tool** - Retrieves current shipment and customs status
5. **Compliance Validation Tool** - Checks shipment compliance rules
6. **Field Extraction Tool** - Extracts structured data from unstructured text
7. **Reference Lookup Tool** - Accesses reference data (ports, countries, etc.)

**Security Features**:
- Organization-scoped data access
- SQL injection prevention
- Query result sanitization
- Rate limiting per tool usage

### AnswerUserQueryService

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/services/answer-user-query.service.ts`

**Purpose**: Classifies and routes user queries to appropriate response handlers using AI.

**Key Methods**:
```typescript
class AnswerUserQueryService {
  async classifyUserQuestion(question: string, shipmentContext: ShipmentContext): Promise<QuestionCategory>;
  async generateAnswer(question: string, category: QuestionCategory, context: ShipmentContext): Promise<AnswerResponse>;
  async handleMultipleQuestions(questions: string[], context: ShipmentContext): Promise<AnswerResponse[]>;
}
```

**Question Categories Supported**:
- **ETA_QUESTION** - Estimated time of arrival inquiries
- **RELEASE_STATUS_QUESTION** - Customs release status
- **SHIPPING_STATUS_QUESTION** - Transportation status
- **TRANSACTION_NUMBER_QUESTION** - Reference number requests
- **GENERAL_QUESTION** - Fallback for unclassified queries

**Integration Pattern**:
- Used by `GetShipmentStatusHandler` for complex query processing
- Integrates with `ShipmentContext` for business rule evaluation
- Supports concurrent processing of multiple questions

### DocumentProcessorService

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/services/document-processor.service.ts`

**Purpose**: Handles document validation, processing, and entry submission workflows.

**Key Features**:
- **Document Validation**: Checks document completeness and compliance
- **Field Extraction**: Extracts structured data from document content
- **Entry Submission**: Attempts automated customs entry submission
- **Error Handling**: Provides detailed validation error messages
- **Status Tracking**: Monitors document processing status

**Processing Pipeline**:
1. Document content analysis and validation
2. Missing field identification and reporting
3. Compliance rule checking
4. Automated submission attempt (if conditions met)
5. Result reporting and error handling

### ShipmentFieldExtractionService

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/services/shipment-field-extraction.service.ts`

**Purpose**: Extracts structured shipment data from unstructured email content using LLM.

**Extraction Capabilities**:
- **Port Codes**: Origin and destination port identification
- **Sublocation**: Specific location within ports
- **Cargo Control Number (CCN)**: Customs tracking numbers
- **Container Information**: Container numbers and types
- **Dates**: ETD, ETA, release dates with format standardization
- **Weights/Measures**: Volume, weight, quantity with unit conversion

**LLM Integration**:
- Uses structured schemas for reliable data extraction
- Implements validation and sanitization of extracted data
- Provides confidence scoring for extracted values
- Handles multiple extraction attempts for accuracy

### ShipmentIdentifierService

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/services/shipment-identifier.service.ts`

**Purpose**: Sophisticated LLM-based shipment identification with database lookup capabilities.

**Identification Methods**:
```typescript
class ShipmentIdentifierService {
  async identifyShipmentFromContent(content: MessageContent): Promise<ShipmentIdentifiers>;
  async lookupShipmentInDatabase(identifiers: ShipmentIdentifiers, organizationId: number): Promise<Shipment[]>;
  async validateShipmentMatch(email: Email, shipment: Shipment): Promise<boolean>;
}
```

**Multi-Strategy Approach**:
1. **Primary Identifiers**: HBL, MBL, CCN extraction from email content
2. **Secondary Identifiers**: Container numbers, booking references
3. **Contextual Analysis**: Uses email history and attachment analysis
4. **Database Correlation**: Matches extracted identifiers against database
5. **Confidence Scoring**: Ranks potential matches by confidence level

## Event System Architecture

### Email Processing Events

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/events/email-processing.events.ts`

**Event Types**:
```typescript
enum EmailProcessingEventType {
  EMAIL_RECEIVED = 'email.received',
  SHIPMENT_IDENTIFIED = 'shipment.identified',
  INTENTS_ANALYZED = 'intents.analyzed',
  FRAGMENTS_GENERATED = 'fragments.generated',
  RESPONSE_SENT = 'response.sent',
  MANUAL_REVIEW_REQUIRED = 'manual-review.required',
  PROCESSING_FAILED = 'processing.failed'
}
```

**Event Data Structures**:
- Organization ID for proper scoping
- Email ID and thread ID for tracking
- Processing stage information
- Error details for failed events
- Timestamp and correlation data

### Email Saga Listener

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/listeners/email-saga.listener.ts`

**Purpose**: Implements saga pattern for reliable email processing coordination with pessimistic locking.

**Saga Coordination Features**:
- **State Management**: Tracks email processing state across services
- **Pessimistic Locking**: Prevents concurrent processing of same email
- **Compensation Actions**: Handles rollback scenarios for failed processing
- **Event Ordering**: Ensures proper sequence of processing events
- **Timeout Handling**: Manages stuck or long-running processes

**Processing Stages Coordinated**:
1. Email ingestion and validation
2. Shipment identification
3. Intent analysis
4. Fragment generation
5. Response composition and delivery
6. Status updates and cleanup

## Schema System

### LLM Integration Schemas

The core-agent system uses 9 Zod schemas for type-safe LLM interactions:

**Classification Schemas**:
- `ClassifiedTaskSchema` - Task intent classification results
- `InquiryClassificationSchema` - User query categorization
- `ClassifyQuestionCategorySchema` - Question type identification

**Extraction Schemas**:
- `ExtractShipmentFieldsSchema` - Structured shipment data extraction
- `ExtractShipmentIdentifiersSchema` - Shipment identifier extraction
- `IdentifyShipmentSchema` - Shipment identification results

**Processing Schemas**:
- `TaskDecompositionSchema` - Email task breakdown
- `MessageContentSchema` - Email content validation
- `LookupShipmentDbSchema` - Database query result validation

**Schema Features**:
- **Type Safety**: Ensures LLM responses match expected structures
- **Validation**: Input sanitization and business rule checking
- **Size Limits**: Prevents oversized responses and DoS attacks
- **Error Handling**: Graceful degradation for malformed responses

## Testing Infrastructure

### E2E Testing Framework

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/testing/`

**Testing Utilities**:

#### Core Testing Scripts
- `run-e2e-with-logs.sh` - Full end-to-end pipeline testing
- `run-e2e-interactive.sh` - Interactive testing with user input
- `run-e2e-debug.sh` - Debug mode with detailed logging
- `run-processor-test-with-logs.sh` - Direct processor testing

#### Specialized Test Scripts
- `test-intent-handlers.js` - Individual handler testing with real data
- `test-fragment-deduplication-fix.js` - Fragment system validation
- `test-request-rush-processing-integration.js` - Rush processing workflow
- `test-document-processor-status.js` - Document processing validation

#### Database Testing Utilities
- `db-query.js` - Database query helper with organization scoping
- `find-test-shipments.js` - Test shipment discovery and selection
- `comprehensive-email-cleanup.js` - Test data cleanup and reset

#### Email Management Tools
- `cleanup-emails-by-subject.js` - Subject-based email cleanup
- `e2e-email-pipeline-nestjs.js` - Full email pipeline simulation
- `check-e2e-test-status.js` - Test execution status monitoring

### Testing Approach

**Multi-Level Testing Strategy**:
1. **Unit Testing**: Individual component testing with mocks
2. **Integration Testing**: Service interaction testing
3. **E2E Testing**: Full pipeline testing with real data
4. **Performance Testing**: Load testing with concurrent requests
5. **Regression Testing**: Automated testing for bug prevention

**Test Data Management**:
- Real shipment data integration for realistic testing
- Organization-scoped test data isolation
- Automated cleanup and reset capabilities
- Test data generation utilities

## Constants and Configuration

### Message and Error Constants

**Compliance Messages**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/constants/compliance-messages.constants.ts`
- Standardized compliance error messages
- Field validation error descriptions
- Missing document notifications

**Customs Definitions**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/constants/customs-definitions.constants.ts`
- Customs status definitions and descriptions
- Trade terminology and explanations
- Workflow stage descriptions

**Error Formatting**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/constants/error-formatting.constants.ts`
- User-friendly error message formatting
- Error categorization and prioritization
- Localization support structures

**Template Constants**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/constants/templates.ts`
- Template name constants for type safety
- Template ordering and priority definitions
- Fragment template relationships

## Utility Functions

### Concurrency Management

**Location**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/utils/concurrency-limit.util.ts`

**Purpose**: Manages concurrent LLM API calls to prevent rate limit violations.

```typescript
export async function limitConcurrency<T>(
  tasks: Array<() => Promise<T>>,
  concurrencyLimit: number = 3
): Promise<T[]> {
  // Processes tasks with controlled concurrency
  // Implements exponential backoff for rate limit handling
  // Provides error aggregation and partial success handling
}
```

### Error Handling Utilities

**Retry Decorator**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/utils/retry.decorator.ts`
- Automatic retry logic for transient failures
- Exponential backoff implementation
- Configurable retry attempts and conditions

**Try-Catch Utility**: `/home/<USER>/dev/Claro/apps/portal-api/src/core-agent/utils/try-catch.util.ts`
- Standardized error handling patterns
- Error logging and telemetry integration
- Graceful degradation helpers

## Integration with Main System

### Service Dependency Integration

The core-agent system integrates with the main email processing system through:

1. **Queue Integration**: BullMQ-based asynchronous processing
2. **Event System**: EventEmitter2 for cross-module communication  
3. **Service Injection**: NestJS dependency injection with proper scoping
4. **Database Integration**: TypeORM with transaction management
5. **Context Sharing**: ShipmentContext propagation across services

### Processing Flow Integration

```mermaid
graph TD
    A[Email Received] --> B[IdentifyShipmentProcessor]
    B --> C[HandleRequestMessageProcessor]
    C --> D[Intent Handler Registry]
    D --> E[Specialized Services]
    E --> F[AgentToolsService]
    E --> G[DocumentProcessorService] 
    E --> H[AnswerUserQueryService]
    F --> I[Fragment Generation]
    G --> I
    H --> I
    I --> J[Email Response]
    
    K[EventEmitterProcessor] --> L[Email Saga Listener]
    L --> M[State Coordination]
```

## Performance Considerations

### Concurrency Controls
- **LLM API Rate Limiting**: Prevents quota exhaustion
- **Database Connection Pooling**: Optimizes database resource usage
- **Queue Worker Scaling**: Handles processing load spikes
- **Memory Management**: Proper cleanup of large objects

### Optimization Strategies
- **Schema Validation Caching**: Reduces validation overhead
- **Template Compilation Caching**: Improves rendering performance
- **Database Query Optimization**: Indexes and query planning
- **Async Processing**: Non-blocking operations where possible

## Security Considerations

### Data Protection
- **Organization Scoping**: All data access is organization-scoped
- **Input Sanitization**: LLM inputs are sanitized to prevent injection
- **Schema Validation**: All external inputs are validated against schemas
- **Error Message Sanitization**: Prevents information leakage

### Access Control
- **Service-Level Authorization**: Services enforce proper access controls
- **Database-Level Security**: Row-level security where applicable
- **API Rate Limiting**: Prevents abuse and DoS attacks
- **Audit Logging**: Comprehensive logging for security monitoring

## Monitoring and Observability

### Logging Strategy
- **Structured Logging**: JSON-formatted logs with consistent fields
- **Correlation IDs**: Request tracking across service boundaries
- **Performance Metrics**: Response times and throughput measurement
- **Error Aggregation**: Centralized error collection and analysis

### Key Metrics
- **Processing Success Rate**: Percentage of successful email processing
- **LLM API Response Times**: Monitor external API performance
- **Database Query Performance**: Track slow queries and optimization needs
- **Queue Processing Delays**: Monitor queue depth and processing times

This comprehensive documentation covers all the major core-agent system components that were not fully documented in the existing HandleRequestMessageProcessor and ClaroEmailProcessingSystem documentation, providing complete coverage of this sophisticated AI-driven email processing platform.