# Multi-Tenancy Security Analysis Report

## Executive Summary

The multi-tenancy security concern is **LEGITIMATE**. The current implementation uses REQUEST-scoped services that rely on the request context to filter data by organization. Converting these to SINGLETON services without proper safeguards would create significant security risks including data leakage between tenants.

## Current Multi-Tenancy Implementation

### 1. REQUEST-Scoped Services Pattern

The system currently uses REQUEST-scoped services (24 services identified) that inject the authenticated request to access organization context:

```typescript
@Injectable({ scope: Scope.REQUEST })
export class ShipmentService {
  constructor(
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    // ... other dependencies
  ) {}
```

### 2. Organization Filtering Patterns

Services filter data by organization in several ways:

**Direct filtering in queries:**
```typescript
// From ShipmentService.getShipments()
if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
  getShipmentsDto.organizationId = this.request?.user?.organization?.id || -1;
```

**Where clause filtering:**
```typescript
// From ShipmentService.hblNumberExists()
if (this.request?.user?.organization?.id) {
  where.organization = { id: this.request?.user?.organization?.id };
}
```

**Validation checks:**
```typescript
// From ShipmentService.validateAndAssignImporter()
if (shipment[propertyName].organization?.id !== this.request?.user?.organization?.id)
  throw new BadRequestException(
    `${convertFromCamelCase(propertyName)} does not belong to the organization of current user`
  );
```

### 3. Alternative Pattern: CLS (Continuation Local Storage)

Some SINGLETON services use CLS for organization context:

```typescript
// From DocumentService (SINGLETON)
if (this.cls.get("USER_PERMISSION") !== UserPermission.BACKOFFICE_ADMIN)
  getDocumentsDto.organizationId = this.cls.get("ORGANIZATION_ID") || -1;
```

The CLS is configured in app.module.ts to extract organization context from requests:
```typescript
ClsModule.forRoot({
  interceptor: {
    setup: (cls, context) => {
      const request = context.switchToHttp().getRequest<AuthenticatedRequest>();
      if ("user" in request && request.user?.organization) {
        cls.set("USER_PERMISSION", request.user.permission);
        cls.set("ORGANIZATION_ID", request.user.organization.id);
      }
    }
  }
})
```

## Specific Risks of Converting to SINGLETON

### 1. Race Conditions
If REQUEST services become SINGLETON without proper context management:
- Concurrent requests could overwrite shared state
- Organization context could leak between requests
- User A could see/modify User B's data

### 2. Missing Organization Filters
Without request context, critical queries would lose organization filtering:
- `getShipmentById()` - Would return shipments from any organization
- `hblNumberExists()` - Would check across all organizations
- `getEmailById()` - Would expose emails from other organizations

### 3. Background Jobs and System Operations
The `generateRequest()` utility creates fake request contexts for system operations:
```typescript
export function generateRequest(
  currentRequest: AuthenticatedRequest | null,
  organization: Organization,
  permission = UserPermission.BACKOFFICE_ADMIN
) {
  if (currentRequest?.user?.organization?.id) return currentRequest;
  else return {
    user: { permission, organization }
  };
}
```

This pattern would break with SINGLETON services unless refactored.

## Safe Patterns for SINGLETON Services

### 1. Use CLS Consistently
- All organization filtering should use CLS instead of request injection
- CLS provides async context that works with SINGLETON services
- Example: DocumentService, AggregationService

### 2. Explicit Organization Parameters
- Pass organization ID explicitly to all methods
- Never rely on implicit context
- Validate organization ownership at every layer

### 3. Repository Pattern with Organization Scoping
Create a scoped repository pattern:
```typescript
@Injectable()
export class OrganizationScopedRepository<T> {
  constructor(
    private readonly cls: ClsService,
    private readonly repository: Repository<T>
  ) {}
  
  async findOne(where: FindOptionsWhere<T>) {
    const orgId = this.cls.get("ORGANIZATION_ID");
    return this.repository.findOne({
      where: { ...where, organization: { id: orgId } }
    });
  }
}
```

## Recommendations

1. **DO NOT** convert REQUEST-scoped services to SINGLETON without refactoring
2. **ADOPT** CLS pattern consistently across all services that need organization context
3. **CREATE** integration tests that verify multi-tenancy isolation
4. **IMPLEMENT** organization validation at the controller level as an additional safeguard
5. **AUDIT** all database queries to ensure organization filtering is applied

## Critical Services Requiring Attention

These services have the highest risk if converted to SINGLETON:
1. ShipmentService - 940+ lines, complex organization filtering
2. EmailService - Complex thread/organization logic
3. ImporterService - Organization-specific business rules
4. TradePartnerService - Shared and org-specific partners
5. CoreAgentService - Processes sensitive customer data

## Conclusion

The multi-tenancy implementation relies heavily on REQUEST scope for security. Converting to SINGLETON services requires a comprehensive refactoring to use CLS or explicit organization parameters. The risk of data leakage is HIGH without proper safeguards.