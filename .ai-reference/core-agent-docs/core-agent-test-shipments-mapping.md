# Core Agent Email Response Testing - Revised Shipment Mapping Table

Based on the email scenarios and intent mapping table, here are the ideal shipments to use for testing core-agent email responses. This table provides specific scenarios, customer questions, and expected shipment attributes needed for comprehensive testing.

## Revised Testing Scenarios with Shipment Requirements

### General Status Updates

| Scenario | Customer Question | Shipment ID | CCN | HBL | Container | Additional Attributes | Expected Response Pattern |
|----------|-------------------|-------------|-----|-----|-----------|----------------------|---------------------------|
| Status: Pending Commercial Invoice | "What's the status of my shipment?" | **[FIND: pending-commercial-invoice]** | 8F5LCQD25031326 | CQD25031326 | TEMU8711844 | missing_docs: ["CI", "PL"] | "Please send the missing document shown below... CI & PL: Missing" |
| Status: Pending Confirmation | "What's the status of my shipment?" | **[FIND: pending-confirmation]** | 8F5LCQD25031327 | CQD25031327 | TEMU8711845 | missing_fields: ["weight"] | "There are compliance issues or missing required fields... Weight missing" |
| Status: Pending Arrival | "What's the status of my shipment?" | **[FIND: pending-arrival]** | 8F5LCQD25031328 | CQD25031328 | TEMU8711846 | ETA: "2025-07-01" | "The estimated time of arrival (ETA) at the port... We expect to submit the customs entry as the arrival date approaches" |
| Status: Live | "What's the status of my shipment?" | **[FIND: live]** | 8F5LCQD25031329 | CQD25031329 | TEMU8711847 | - | "The submission for the subject shipment has been initiated. We will let you know once released by customs" |
| Status: Entry Submitted | "What's the status of my shipment?" | **[FIND: entry-submitted]** | 8F5LCQD25031330 | CQD25031330 | TEMU8711848 | - | "The entry for the subject shipment has been submitted. We will let you know once released by customs" |
| Status: Entry Accepted | "What's the status of my shipment?" | **[FIND: entry-accepted]** | 8F5LCQD25031331 | CQD25031331 | TEMU8711849 | - | "The subject shipment entry has been accepted by Customs and is awaiting arrival of goods" |
| Status: Exam | "What's the status of my shipment?" | **[FIND: exam]** | 8F5LCQD25031332 | CQD25031332 | TEMU8711850 | ETA: "2025-07-01" | "The subject shipment has been selected by customs for examination. We are contacting you for further information" |
| Status: Released | "What's the status of my shipment?" | **[FIND: released]** | 8F5LCQD25031333 | CQD25031333 | TEMU8711851 | release_date: "2025-06-20" | "The subject shipment has been released by CBSA on [date]" |

### Rush/Urgent Requests

| Rush - Pending Commercial Invoice | "This is urgent, please expedite my shipment" | **[USE SAME AS: pending-commercial-invoice]** | - | - | - | urgent: true + missing_docs | "Please send the missing document shown below at your earliest convenience... CI & PL: Missing" |
| Rush - Pending Confirmation | "This is urgent, please expedite my shipment" | **[USE SAME AS: pending-confirmation]** | - | - | - | urgent: true + missing_fields | "There are compliance issues or missing required fields... Weight: missing" |
| Rush - Pending Arrival | "This is urgent, please expedite my shipment" | **[USE SAME AS: pending-arrival]** | - | - | - | urgent: true | "We've received your rush request and will be submitting the entry right away" |
| Rush - Live | "This is urgent, please expedite my shipment" | **[USE SAME AS: live]** | - | - | - | urgent: true | "We've received your rush request and will be submitting the entry right away" |
| Rush - Entry Submitted | "This is urgent, please expedite my shipment" | **[USE SAME AS: entry-submitted]** | - | - | - | urgent: true | "The entry for the subject shipment has already been submitted. We will let you know once released by customs" |
| Rush - Entry Accepted | "This is urgent, please expedite my shipment" | **[USE SAME AS: entry-accepted]** | - | - | - | urgent: true | "The subject shipment entry has already been submitted and accepted by Customs and is awaiting arrival of goods" |
| Rush - Exam | "This is urgent, please expedite my shipment" | **[USE SAME AS: exam]** | - | - | - | urgent: true | "Please note the shipment has been selected for exam and will be released once the examination is complete" |
| Rush - Released | "This is urgent, please expedite my shipment" | **[USE SAME AS: released]** | - | - | - | urgent: true | "The subject shipment has been released by CBSA on [date]" |

### Document Requests - CAD

| CAD - Pending Commercial Invoice | "Can you send me the CAD?" | **[USE SAME AS: pending-commercial-invoice]** | - | - | - | missing_docs | "We can't provide the CAD yet as we're missing the following document(s)... CI & PL: Missing" |
| CAD - Pending Confirmation | "Can you send me the CAD?" | **[USE SAME AS: pending-confirmation]** | - | - | - | missing_fields | "We're currently unable to provide the CAD as there are compliance issues... Weight: missing" |
| CAD - Pending Arrival | "Can you send me the CAD?" | **[USE SAME AS: pending-arrival]** | - | - | - | - | "Send email to backoffice to send them the CAD" |
| CAD - Available | "Can you send me the CAD?" | **[USE SAME AS: live/entry-submitted/entry-accepted/exam/released]** | - | - | - | - | "Please see CAD document attached" |

### Document Requests - RNS

| RNS - Pending Commercial Invoice | "Can you send me the RNS?" | **[USE SAME AS: pending-commercial-invoice]** | - | - | - | missing_docs | "We can't provide the RNS yet as we're missing the following document(s)... CI & PL: Missing" |
| RNS - Pending Confirmation | "Can you send me the RNS?" | **[USE SAME AS: pending-confirmation]** | - | - | - | missing_fields | "We're currently unable to provide the RNS as there are compliance issues... Weight: missing" |
| RNS - Not Available | "Can you send me the RNS?" | **[USE SAME AS: pending-arrival/live/entry-submitted]** | - | - | - | - | "The RNS can only be provided after the shipment has been submitted/accepted by customs" |
| RNS - Available | "Can you send me the RNS?" | **[USE SAME AS: entry-accepted/exam]** | - | - | - | - | "RNS information: [Details]" |
| RNS - Proof of Release | "Can you send me the RNS?" | **[USE SAME AS: released]** | - | - | - | - | "RNS Proof of Release information: [Details]" |

### Document Provision

| Promise to Send Later | "I will send the documents later" | **[USE SAME AS: pending-commercial-invoice]** | - | - | - | promised_docs: true | "Thank you for the update. We'll be waiting for the additional or updated documents" |
| All Documents Received - Ocean/Air | "Here are all the documents" | **[FIND: ocean/air + all_docs_received]** | - | - | - | shipment_type: "ocean/air" | "We have received the required documents for your shipment and are currently reviewing and processing them" |
| All Documents Received - Truck | "Here are all the documents" | **[FIND: land + all_docs_received]** | - | - | - | shipment_type: "truck" | "We have received the required documents... Transaction# will be sent to you shortly" |
| Missing Documents | "Here are some documents" | **[USE SAME AS: pending-commercial-invoice]** | - | - | - | missing_docs | "Please send the missing document shown below... CI & PL: Missing" |

### Cancellation

| Cancel Shipment | "Please cancel my shipment" | **[FIND: pending-arrival/live/entry-submitted]** | - | - | - | cancel_requested: true | "We've received your request to cancel/hold the entry. Our team has been notified and will take the necessary action" |

## Database Query Instructions

To find actual shipment IDs for testing, use these SQL queries:

```sql
-- Find shipments by customs status
SELECT s.id, s."cargoControlNumber", s."hblNumber", s."customsStatus", s."modeOfTransport", c."containerNumber"
FROM shipment s 
LEFT JOIN container c ON c."shipmentId" = s.id 
WHERE s."customsStatus" = '[STATUS]' 
  AND s."cargoControlNumber" IS NOT NULL 
  AND s."hblNumber" IS NOT NULL 
  AND s."deletedAt" IS NULL 
ORDER BY s."updatedAt" DESC 
LIMIT 3;

-- Replace [STATUS] with: pending-commercial-invoice, pending-confirmation, pending-arrival, live, entry-submitted, entry-accepted, exam, released
```

## Testing Notes

1. **Use Real Shipment IDs**: Replace the **[FIND: status]** placeholders with actual shipment IDs from your database
2. **Rush Scenarios**: Use the same shipment IDs as the corresponding status scenarios, just add `urgent: true` flag in testing
3. **Document Scenarios**: Use the same shipment IDs as status scenarios, test different document request types
4. **Transport Modes**: Ensure you have examples of ocean, air, and land shipments for comprehensive testing
5. **Test Data Integrity**: Verify that the shipments you choose have the expected attributes (CCN, HBL, container numbers, etc.)

## Expected Response Validation

For each test scenario:
1. Send the customer question to the core-agent
2. Verify the response matches the expected response pattern
3. Check that the response includes relevant shipment details (CCN, HBL, container numbers)
4. Validate that the tone and urgency level match the scenario (normal vs rush)
5. Ensure document availability logic is correctly applied

## Database Queries to Find Real Shipment IDs

Use these SQL queries to find actual shipment IDs for each scenario:

### 1. Find Pending Commercial Invoice Shipments
```sql
SELECT s.id, s."cargoControlNumber", s."hblNumber", s."customsStatus", s."modeOfTransport", c."containerNumber"
FROM shipment s
LEFT JOIN container c ON c."shipmentId" = s.id
WHERE s."customsStatus" = 'pending-commercial-invoice'
  AND s."cargoControlNumber" IS NOT NULL
  AND s."hblNumber" IS NOT NULL
  AND s."deletedAt" IS NULL
ORDER BY s."updatedAt" DESC
LIMIT 3;
```

### 2. Find Pending Confirmation Shipments
```sql
SELECT s.id, s."cargoControlNumber", s."hblNumber", s."customsStatus", s."modeOfTransport", c."containerNumber"
FROM shipment s
LEFT JOIN container c ON c."shipmentId" = s.id
WHERE s."customsStatus" = 'pending-confirmation'
  AND s."cargoControlNumber" IS NOT NULL
  AND s."hblNumber" IS NOT NULL
  AND s."deletedAt" IS NULL
ORDER BY s."updatedAt" DESC
LIMIT 3;
```

### 3. Find All Status Types
```sql
SELECT s."customsStatus", COUNT(*) as count
FROM shipment s
WHERE s."cargoControlNumber" IS NOT NULL
  AND s."hblNumber" IS NOT NULL
  AND s."deletedAt" IS NULL
GROUP BY s."customsStatus"
ORDER BY count DESC;
```

### 4. Find Shipments by Transport Mode
```sql
SELECT s.id, s."cargoControlNumber", s."hblNumber", s."customsStatus", s."modeOfTransport", c."containerNumber"
FROM shipment s
LEFT JOIN container c ON c."shipmentId" = s.id
WHERE s."modeOfTransport" IN ('ocean-fcl', 'ocean-lcl', 'air', 'land')
  AND s."cargoControlNumber" IS NOT NULL
  AND s."hblNumber" IS NOT NULL
  AND s."deletedAt" IS NULL
ORDER BY s."modeOfTransport", s."updatedAt" DESC
LIMIT 20;
```

## How to Execute Database Queries

1. **Using the db-query script:**
   ```bash
   cd apps/portal-api
   node src/core-agent/testing/db-query.js sql "YOUR_SQL_QUERY_HERE"
   ```

2. **Using direct database connection:**
   - Connect to your PostgreSQL database
   - Run the queries above to find suitable shipment IDs
   - Note down the IDs, CCNs, HBLs, and container numbers

## Next Steps

1. **Execute the database queries** to find actual shipment IDs for each status
2. **Update this table** by replacing **[FIND: status]** with real shipment IDs
3. **Create test cases** using the identified shipments
4. **Execute core-agent email response tests** with the customer questions
5. **Validate responses** against expected patterns
6. **Document any discrepancies** between expected and actual responses

## Example Updated Entry

Once you find real shipment IDs, update the table like this:

| Status: Pending Commercial Invoice | "What's the status of my shipment?" | **12345** | 8F5LCQD25031326 | CQD25031326 | TEMU8711844 | missing_docs: ["CI", "PL"] | "Please send the missing document shown below... CI & PL: Missing" |

## Testing Execution

For each scenario:
1. **Send the customer question** to the core-agent system
2. **Include the shipment ID** in the context
3. **Verify the response** matches the expected pattern
4. **Check shipment details** (CCN, HBL, container) are included correctly
5. **Validate tone and urgency** match the scenario type
