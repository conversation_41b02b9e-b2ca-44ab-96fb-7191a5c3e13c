# Core-Agent Event System - Comprehensive Documentation

## Overview

The Core-Agent event system implements an event-driven architecture for email processing coordination using the saga pattern. It provides reliable, decoupled communication between the Core-Agent module and other system components, particularly the Email module.

## Architecture

### Event-Driven Design Principles
- **Saga Pattern**: Implements long-running business transactions with proper compensation
- **Event Sourcing**: Uses events to track state changes and processing stages
- **Loose Coupling**: Modules communicate through events rather than direct dependencies
- **Reliability**: Events are processed with transaction safety and retry mechanisms
- **State Management**: Pessimistic locking ensures consistent state transitions

### Event Flow Architecture
```
Email Module → Events → Core-Agent Saga Listener → BullMQ Flows → Event Emitter → Events → Other Modules

Core Components:
1. Email Processing Events (core-agent/events/)
2. Email Saga Listener (core-agent/listeners/)
3. Event Emitter Processor (core-agent/processors/)
```

## Event System Components

### 1. Email Processing Events

**File**: `/apps/portal-api/src/core-agent/events/email-processing.events.ts`

**Purpose**: Defines event classes and types for Core-Agent email processing status communication.

#### Event Class Hierarchy

##### BaseCoreAgentEmailEvent
Base class for all Core-Agent email processing events:

```typescript
abstract class BaseCoreAgentEmailEvent {
  public readonly emailId: number;
  public readonly organizationId: number;
  public readonly status: EmailStatus;
}
```

##### Specific Event Classes

1. **CoreAgentProcessingStartedEvent**
   - **Purpose**: Signals Core-Agent has begun processing an email
   - **Default Status**: `EmailStatus.AWAIT_SHIPMENT_SEARCH`
   - **Usage**: Indicates email has entered Core-Agent pipeline

2. **CoreAgentProcessingCompletedEvent**
   - **Purpose**: Signals successful completion of email processing
   - **Default Status**: `EmailStatus.RESPONDED`
   - **Usage**: Indicates email processing is complete and response ready

3. **CoreAgentProcessingFailedEvent**
   - **Purpose**: Signals processing failure with error details  
   - **Default Status**: `EmailStatus.FAILED_RESPONDING`
   - **Additional Fields**: `error?: string` for failure context
   - **Usage**: Enables error handling and recovery workflows

4. **CoreAgentStatusUpdateEvent**
   - **Purpose**: Intermediate status updates during processing
   - **Additional Fields**: 
     - `previousStatus?: EmailStatus`
     - `details?: string`
   - **Usage**: Provides granular processing visibility

#### Event Naming Convention
```typescript
enum CoreAgentEmailEvent {
  PROCESSING_STARTED = "core-agent.processing.started",
  PROCESSING_COMPLETED = "core-agent.processing.completed", 
  PROCESSING_FAILED = "core-agent.processing.failed",
  STATUS_UPDATE = "core-agent.status.update"
}
```

#### Type Safety
Includes comprehensive TypeScript interfaces for type-safe event handling:

```typescript
interface CoreAgentProcessingStartedPayload {
  emailId: number;
  organizationId: number;
  status: EmailStatus;
}
// ... additional payload interfaces for each event type
```

### 2. Email Saga Listener

**File**: `/apps/portal-api/src/core-agent/listeners/email-saga.listener.ts`

**Purpose**: Implements the saga pattern for email processing with proper state management and workflow coordination.

#### Saga Pattern Implementation
The listener implements a sophisticated saga pattern:

1. **Pessimistic Locking**: Uses database-level locking for state consistency
2. **State Guards**: Only processes emails in expected states
3. **Transaction Safety**: All operations wrapped in database transactions
4. **Flow Coordination**: Orchestrates complex multi-step workflows
5. **Error Recovery**: Proper rollback and error handling

#### Event Handlers

##### onEmailSaved() - EMAIL_SAVED Event Handler

**Responsibilities**:
- Listen for EMAIL_SAVED events from the Email module
- Validate email state and organization context
- Determine processing flow based on attachments
- Queue appropriate BullMQ flows for processing
- Update email status atomically

**Processing Flow**:
```typescript
@OnEvent(EMAIL_SAVED)
async onEmailSaved(event: EmailSavedEvent): Promise<void> {
  // 1. Pessimistic locking for state safety
  const queryRunner = this.dataSource.createQueryRunner();
  await queryRunner.startTransaction();
  
  // 2. Load email with pessimistic lock
  const email = await queryRunner.manager.findOne(Email, {
    where: { id: emailId },
    lock: { mode: "pessimistic_write" }
  });
  
  // 3. State machine guard
  if (email.status !== EmailStatus.SAVED) {
    // Skip processing - already handled
    return;
  }
  
  // 4. Determine flow based on attachments
  const { hasAttachments, fileIds } = await this.getFileBatchInfo(gmailId, queryRunner);
  
  // 5. Build and queue appropriate flow
  if (hasAttachments) {
    // Flow: identify-shipment → emit-file-batch-created
  } else {
    // Flow: identify-shipment → handle-request-message  
  }
  
  // 6. Update email status
  await queryRunner.manager.update(Email, { id: emailId }, { 
    status: EmailStatus.AWAIT_SHIPMENT_SEARCH 
  });
  
  await queryRunner.commitTransaction();
}
```

**Flow Decision Logic**:
- **With Attachments**: `identify-shipment` → `emit-file-batch-created` → (waits for aggregation)
- **Without Attachments**: `identify-shipment` → `handle-request-message`

##### onAggregationCompleted() - BATCH_DOCUMENT_AGGREGATED_EVENT Handler

**Responsibilities**:
- Handle completion of document aggregation from external Aggregation module
- Bridge aggregation completion with user intent processing
- Validate email state and queue final processing step
- Update email status to reflect aggregation completion

**Processing Flow**:
```typescript
@OnEvent(BATCH_DOCUMENT_AGGREGATED_EVENT)
async onAggregationCompleted(event: BatchDocumentAggregatedEvent): Promise<void> {
  // 1. Find email by batchId with pessimistic lock
  const email = await queryRunner.manager.findOne(Email, {
    where: { gmailId: batchId },
    lock: { mode: "pessimistic_write" }
  });
  
  // 2. State machine guard
  if (email.status !== EmailStatus.AGGREGATING_EMAIL) {
    // Not waiting for aggregation - skip
    return;
  }
  
  // 3. Queue handle-request-message for post-aggregation processing
  await this.handleRequestMessageQueue.add(jobId, {
    emailId: email.id,
    organizationId: organizationId
  });
  
  // 4. Update status to EMAIL_AGGREGATED
  await queryRunner.manager.update(Email, { id: email.id }, { 
    status: EmailStatus.EMAIL_AGGREGATED 
  });
}
```

#### State Management

##### State Transition Guards
The saga listener implements strict state machine guards:

```typescript
// Only process SAVED emails
if (email.status !== EmailStatus.SAVED) {
  this.logger.warn(`Email ${emailId} already processed or processing. Skipping.`);
  return;
}

// Only process emails waiting for aggregation
if (email.status !== EmailStatus.AGGREGATING_EMAIL) {
  this.logger.error(`Email not waiting for aggregation. Skipping.`);
  return;
}
```

##### Transaction Safety
All state changes are wrapped in database transactions:

```typescript
const queryRunner = this.dataSource.createQueryRunner();
await queryRunner.connect();
await queryRunner.startTransaction();

try {
  // State changes here
  await queryRunner.commitTransaction();
} catch (error) {
  if (queryRunner.isTransactionActive) {
    await queryRunner.rollbackTransaction();
  }
  throw error;
} finally {
  await queryRunner.release();
}
```

#### Flow Coordination

##### BullMQ Flow Integration
The saga listener uses BullMQ FlowProducer for complex workflow coordination:

```typescript
// Build flow with dependent jobs
const flow = buildFlowProducer([
  {
    name: "identify-shipment",
    queueName: CoreAgentQueueName.IDENTIFY_SHIPMENT,
    data: { emailId, organizationId, hasAttachments },
    opts: { failParentOnFailure: true }
  },
  {
    name: "handle-request-message", 
    queueName: CoreAgentQueueName.HANDLE_REQUEST_MESSAGE,
    data: { emailId, organizationId }
  }
]);

await this.flowProducer.add(flow);
```

##### Event Bridge Pattern
Uses EventEmitterProcessor to bridge BullMQ flows with NestJS events:

```typescript
{
  name: "emit-file-batch-created",
  queueName: CoreAgentQueueName.EVENT_EMITTER,
  data: {
    eventType: FILE_BATCH_CREATED,
    eventData: new FileBatchCreatedEvent(gmailId, organizationId, fileIds)
  }
}
```

## Event Integration Patterns

### Module Communication
Events enable clean communication between modules:

```
Email Module:
- Emits EMAIL_SAVED when email is persisted
- Listens for Core-Agent status events for UI updates

Core-Agent Module:  
- Listens for EMAIL_SAVED to start processing
- Emits status events for processing updates
- Listens for BATCH_DOCUMENT_AGGREGATED from Aggregation module

Aggregation Module:
- Emits BATCH_DOCUMENT_AGGREGATED when processing completes
```

### Event-Driven State Machine
The system implements a distributed state machine through events:

```
SAVED → (EMAIL_SAVED) → AWAIT_SHIPMENT_SEARCH → (processing) → 
COMPLETED_SHIPMENT_SEARCH → (flow decision) →
[AGGREGATING_EMAIL → EMAIL_AGGREGATED] → RESPONDED
```

### Error Handling Integration
Events support comprehensive error handling:

```typescript
// Emit error events for downstream handling
this.eventEmitter.emit(CoreAgentEmailEvent.PROCESSING_FAILED, {
  emailId,
  organizationId,
  status: EmailStatus.FAILED_RESPONDING,
  error: error.message
});
```

## Performance Considerations

### Optimization Strategies
- **Pessimistic Locking**: Minimizes lock duration through focused transactions
- **Flow Batching**: BullMQ flows enable efficient job batching and coordination
- **Event Queuing**: Events are queued for reliable async processing
- **Connection Pooling**: Efficient database connection management

### Scalability Features
- **Horizontal Scaling**: Multiple saga listener instances can process events
- **Queue Distribution**: BullMQ enables distributed job processing
- **Event Partitioning**: Events can be partitioned by organization for scaling
- **Resource Isolation**: Proper resource cleanup prevents memory leaks

## Monitoring and Debugging

### Event Tracking
Comprehensive logging for event processing:

```typescript
this.logger.log(`[CORE_AGENT_SAGA] EMAIL_SAVED event received - emailId: ${emailId}`);
this.logger.log(`[CORE_AGENT_SAGA] Building flow for email WITH/WITHOUT attachments`);
this.logger.log(`[CORE_AGENT_SAGA] Successfully queued flow for email ${emailId}`);
```

### Error Diagnostics
Rich error context for debugging:

```typescript
this.logger.error(`[CORE_AGENT_SAGA] Failed to process EMAIL_SAVED event`, {
  emailId,
  error: error.message,
  stack: error.stack
});
```

### State Visibility
Clear state transition logging:

```typescript
this.logger.log(`[CORE_AGENT_SAGA] Updated email ${emailId} status to ${EmailStatus.AWAIT_SHIPMENT_SEARCH}`);
```

## Security Considerations

### Multi-Tenant Security
- **Organization Scoping**: All events include organization context
- **Data Isolation**: Events are scoped to organization boundaries
- **Access Control**: Event handlers validate organization access

### Transaction Safety
- **ACID Properties**: All state changes follow ACID transaction properties  
- **Rollback Safety**: Proper transaction rollback on failures
- **Concurrency Control**: Pessimistic locking prevents race conditions

## Testing and Validation

### Event Testing
The event system supports comprehensive testing:

```bash
# Test end-to-end email pipeline with events
./src/core-agent/testing/run-e2e-with-logs.sh

# Test specific event scenarios
node src/core-agent/testing/e2e-email-pipeline-nestjs.js
```

### Integration Testing
- **Event Emission**: Tests that events are emitted correctly
- **Event Handling**: Validates event handlers process events properly
- **State Transitions**: Verifies state machine behavior
- **Error Scenarios**: Tests error handling and recovery

## Future Enhancements

### Extensibility Points
- **New Event Types**: Framework supports additional event types
- **Additional Listeners**: Easy addition of new event listeners
- **Complex Flows**: Support for more sophisticated workflow patterns
- **External Integration**: Events can trigger external system actions

### Performance Optimization
- **Event Batching**: Potential for event batching optimizations
- **Streaming**: Event streaming for high-throughput scenarios
- **Caching**: Event result caching for performance improvement
- **Monitoring**: Enhanced monitoring and alerting capabilities

This event system provides a robust, scalable foundation for distributed email processing with proper state management, error handling, and module coordination capabilities.