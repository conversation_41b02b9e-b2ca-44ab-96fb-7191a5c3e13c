# Core-Agent Schema System - Comprehensive Documentation

## Overview

The Core-Agent schema system provides structured data validation and type safety for LLM interactions, email processing, and business logic operations. Built on Zod, it ensures reliable data flow between components and validates AI agent outputs.

## Architecture

### Schema Design Principles
- **Type Safety**: Comprehensive TypeScript type inference from schemas
- **LLM Integration**: Structured schemas for AI agent input/output validation
- **Business Logic Validation**: Schemas enforce business rules and constraints
- **Size Limits**: Realistic size constraints for production use
- **Error Prevention**: Prevents malformed data from propagating through system

### Schema Categories
```
core-agent/schemas/
├── message-content.schema.ts           # Email message structure
├── identify-shipment.schema.ts         # Shipment identification output
├── lookup-shipment-db.schema.ts        # Database lookup parameters
├── extract-shipment-fields.schema.ts   # Shipment field extraction
├── extract-shipment-identifiers.schema.ts # Identifier extraction
├── classify-question-category.schema.ts # Question classification
├── inquiry-classification.schema.ts    # Inquiry type classification
├── task-decomposition.schema.ts        # Task breakdown structure
└── classified-task.schema.ts          # Classified task output
```

## Core Schemas

### 1. MessageContent Schema

**File**: `message-content.schema.ts`

**Purpose**: Defines the structure for email message content used throughout the Core-Agent system.

#### Schema Structure
```typescript
export const MessageContentSchema = z.object({
  subject: z.string().nullable().optional().describe("Subject of the message"),
  text: z.string().describe("Text content of the message"),
  attachments: z.array(MessageAttachmentSchema).optional().describe("List of attachments"),
  emailHistory: z.array(z.string()).optional().describe("Previous exchanges (deprecated)"),
  messageHistory: z.array(z.string()).optional().describe("Previous message exchanges")
}).strict();
```

#### Size Limit Constants
```typescript
export const MESSAGE_SIZE_LIMITS = {
  SUBJECT_MAX_LENGTH: 500,
  TEXT_MAX_LENGTH: 100000,
  MESSAGE_HISTORY_MAX_LENGTH: 100000,
  FILENAME_MAX_LENGTH: 255,
  CONTENT_TYPE_MAX_LENGTH: 100
};
```

#### MessageAttachment Schema
```typescript
export const MessageAttachmentSchema = z.object({
  filename: z.string().describe("The filename of the attachment"),
  contentType: z.string().optional().describe("The content type of the attachment"),
  extractedData: z.record(z.string().or(z.number()).or(z.boolean()))
    .optional()
    .describe("Extracted data from the attachment")
});
```

#### Usage Context
- **Email Processing**: Validates incoming email content structure
- **LLM Analysis**: Provides structured input for AI agent analysis
- **Intent Handlers**: Ensures consistent message format across handlers
- **Template Rendering**: Provides validated data for response templates

### 2. IdentifyShipment Schema

**File**: `identify-shipment.schema.ts`

**Purpose**: Defines the output structure for shipment identification operations using LLM analysis.

#### Schema Structure
```typescript
export const IdentifyShipmentOutputSchema = z.object({
  foundIdentifiers: z.array(FoundIdentifierSchema)
    .describe("List of identified shipments, each with ID and context."),
  reason: z.string()
    .describe("Explanation summarizing the identification result and process.")
});
```

#### FoundIdentifier Schema
```typescript
const FoundIdentifierSchema = z.object({
  shipmentId: z.number().describe("The database ID of the identified shipment."),
  identifierType: IdentifierTypeEnum.describe("The type of identifier that was matched."),
  identifierValue: z.string().describe("The value of the identifier that was matched."),
  context: z.string().describe("Context or evidence supporting the identification.")
});
```

#### Business Logic Integration
- **Multiple Shipments**: Handles scenarios where multiple shipments are identified
- **Context Tracking**: Maintains evidence trail for identification decisions
- **Reason Explanation**: Provides human-readable explanation of identification logic
- **Type Safety**: Ensures identifier types match database schema

### 3. LookupShipmentDB Schema

**File**: `lookup-shipment-db.schema.ts`

**Purpose**: Defines parameters for database shipment lookup operations and LLM tool integration.

#### Identifier Type Enumeration
```typescript
export const IdentifierTypeEnum = z.enum([
  "hblNumber",
  "cargoControlNumber", 
  "transactionNumber",  // Also used for PARS
  "containerNumber"
]);
```

#### Database Lookup Schema
```typescript
export const LookupShipmentDBSchema = z.object({
  identifierType: IdentifierTypeEnum.describe("The type of identifier to search for"),
  identifierValue: z.string()
    .min(1)
    .describe("The value of the identifier to search for")
});
```

#### LLM Tool Integration Schema
```typescript
export const LlmLookupShipmentDBInputSchema = z.object({
  identifierValue: z.string()
    .min(1)
    .describe("The identifier value to search for in the database"),
  initialGuessedType: IdentifierTypeEnum.optional()
    .describe("Optional initial guess for the identifier type")
});

export const LlmLookupShipmentDBOutputSchema = z.object({
  shipmentId: z.number().nullable()
    .describe("The shipment ID if found uniquely, null otherwise"),
  matchedIdentifierType: IdentifierTypeEnum.nullable()
    .describe("The actual identifier type that matched"),
  identifierValue: z.string()
    .describe("The original identifier value that was searched"),
  reason: z.string()
    .describe("Explanation of the search result")
});
```

#### Security Features
- **Input Validation**: Prevents empty or malformed identifier values
- **Type Constraints**: Limits identifier types to supported database columns
- **Output Validation**: Ensures consistent response structure from LLM tools

### 4. ExtractShipmentFields Schema

**File**: `extract-shipment-fields.schema.ts`

**Purpose**: Defines the structure for extracting structured shipment update fields from unstructured text.

#### Schema Structure
```typescript
export const ExtractShipmentFieldsSchema = z.object({
  cargoControlNumber: z.string()
    .optional()
    .describe("The cargo control number if mentioned"),
  portCode: z.string()
    .regex(/^0\d{3}$/)
    .optional() 
    .describe("4-digit port code starting with 0"),
  subLocation: z.string()
    .regex(/^\d{4}$/)
    .optional()
    .describe("4-digit sub-location code")
});
```

#### Validation Rules
- **Cargo Control Number**: Alphanumeric with hyphens and underscores allowed
- **Port Code**: Exactly 4 digits starting with '0' (e.g., "0401")
- **Sub Location**: Exactly 4 digits (e.g., "1234")

#### Business Logic Integration
- **Field Cleaning**: Supports automatic cleaning of extracted data
- **Pattern Validation**: Enforces Canadian customs field format requirements
- **Optional Fields**: All fields are optional to handle partial extractions

### 5. ClassifyQuestionCategory Schema

**File**: `classify-question-category.schema.ts`

**Purpose**: Defines the output structure for user query classification operations.

#### Schema Structure
```typescript
export const ClassifyQuestionCategorySchema = z.object({
  category: z.string()
    .min(1)
    .describe("The identified category of the user's question"),
  confidence: z.number()
    .min(0)
    .max(1)
    .optional()
    .describe("Confidence score for the classification (0-1)"),
  reasoning: z.string()
    .optional()
    .describe("Brief explanation for why this category was chosen")
});
```

#### Category Integration
Works with `QUESTION_CATEGORIES` constant to classify queries into:
- **Shipment Status Inquiries**
- **Document Requests**
- **Rush Processing Requests** 
- **Compliance Questions**
- **General Inquiries**

#### LLM Integration
- **Confidence Scoring**: Optional confidence scores for classification decisions
- **Reasoning**: Provides explanation for classification choices
- **Category Validation**: Ensures classified categories match predefined types

### 6. ExtractShipmentIdentifiers Schema

**File**: `extract-shipment-identifiers.schema.ts`

**Purpose**: Defines the structure for extracting shipment identifiers from email content.

#### Schema Structure
```typescript
export const ExtractShipmentIdentifiersSchema = z.object({
  identifiers: z.array(z.object({
    type: IdentifierTypeEnum.describe("The type of identifier found"),
    value: z.string().describe("The identifier value extracted from text"),
    context: z.string().describe("Surrounding text that provides context"),
    confidence: z.number().min(0).max(1).optional().describe("Confidence in extraction")
  })).describe("List of potential shipment identifiers found in the text"),
  
  reasoning: z.string()
    .describe("Explanation of the extraction process and findings")
});
```

#### Extraction Features
- **Multiple Identifiers**: Supports extraction of multiple identifier types
- **Context Preservation**: Maintains surrounding text for validation
- **Confidence Scoring**: Optional confidence assessment for each extraction
- **Type Classification**: Automatically classifies identifier types

### 7. TaskDecomposition Schema

**File**: `task-decomposition.schema.ts`

**Purpose**: Defines the structure for breaking down complex tasks into manageable subtasks.

#### Schema Structure
```typescript
export const TaskDecompositionSchema = z.object({
  mainTask: z.string().describe("The primary task to be accomplished"),
  subtasks: z.array(z.object({
    id: z.string().describe("Unique identifier for the subtask"),
    description: z.string().describe("Description of what needs to be done"),
    dependencies: z.array(z.string()).optional().describe("IDs of prerequisite subtasks"),
    estimatedComplexity: z.enum(["low", "medium", "high"]).optional(),
    requiredData: z.array(z.string()).optional().describe("Data needed to complete subtask")
  })).describe("List of subtasks needed to complete the main task"),
  
  executionOrder: z.array(z.string())
    .describe("Recommended order for executing subtasks")
});
```

#### Task Management Features
- **Dependency Tracking**: Manages subtask dependencies
- **Complexity Assessment**: Estimates relative complexity of subtasks
- **Resource Planning**: Identifies required data for each subtask
- **Execution Planning**: Provides optimal execution sequencing

### 8. InquiryClassification Schema

**File**: `inquiry-classification.schema.ts`

**Purpose**: Defines the structure for classifying different types of customer inquiries.

#### Schema Structure
```typescript
export const InquiryClassificationSchema = z.object({
  inquiryType: z.enum([
    "status_inquiry",
    "document_request", 
    "compliance_question",
    "process_update",
    "general_support"
  ]).describe("The primary type of customer inquiry"),
  
  urgency: z.enum(["low", "medium", "high", "critical"])
    .describe("Assessed urgency level of the inquiry"),
    
  requiredActions: z.array(z.string())
    .describe("List of actions needed to address the inquiry"),
    
  estimatedResolutionTime: z.string()
    .optional()
    .describe("Estimated time to resolve the inquiry")
});
```

#### Classification Features
- **Inquiry Types**: Standardized inquiry type classification
- **Urgency Assessment**: Automatic urgency level determination
- **Action Planning**: Identifies required actions for resolution
- **Time Estimation**: Provides resolution time estimates

## Schema Integration Patterns

### LLM Tool Integration
Schemas are used extensively in LLM tool definitions:

```typescript
const getShipmentByIdentifierTool: LlmToolDefinition<typeof LookupShipmentDBSchema._input> = {
  name: "getShipmentByIdentifier",
  description: "Queries the database to find a shipment ID based on an identifier",
  schema: LookupShipmentDBSchema,
  run: async (input) => {
    return this.shipmentIdentifierService.lookupShipmentInDB(input, organizationId);
  },
  strict: true
};
```

### Validation Chain Pattern
Schemas are used in validation chains throughout the system:

```typescript
// 1. Input validation
const validatedInput = MessageContentSchema.parse(emailContent);

// 2. LLM processing with output validation
const llmResponse = await this.askLLMService.ask({
  zodSchema: IdentifyShipmentOutputSchema,
  variables: { emailContent: validatedInput }
});

// 3. Business logic validation
const processedResult = await this.processIdentificationResult(llmResponse.parsed);
```

### Type Safety Integration
Schemas provide comprehensive TypeScript integration:

```typescript
// Automatic type inference
type MessageContent = z.infer<typeof MessageContentSchema>;
type IdentifyShipmentOutput = z.infer<typeof IdentifyShipmentOutputSchema>;

// Type-safe function signatures
async function processMessage(content: MessageContent): Promise<IdentifyShipmentOutput> {
  // Implementation with full type safety
}
```

## Performance Considerations

### Validation Optimization
- **Schema Compilation**: Zod schemas are compiled once for reuse
- **Selective Validation**: Only validates necessary fields in performance-critical paths
- **Error Short-Circuiting**: Fails fast on validation errors
- **Memory Efficiency**: Minimal memory overhead for schema validation

### Size Constraints
All schemas include realistic size constraints:

- **Text Content**: 100KB maximum for email text
- **Subject Lines**: 500 characters maximum
- **Filenames**: 255 characters maximum
- **Arrays**: Reasonable limits on array sizes

## Error Handling

### Validation Error Handling
Schemas provide detailed error information:

```typescript
try {
  const validated = MessageContentSchema.parse(input);
} catch (error) {
  if (error instanceof z.ZodError) {
    // Detailed field-level error information
    const fieldErrors = error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code
    }));
  }
}
```

### Business Rule Validation
Schemas enforce business rules at the data layer:

- **Required Fields**: Ensures critical fields are present
- **Format Validation**: Validates field formats (port codes, etc.)
- **Cross-Field Validation**: Validates relationships between fields
- **Enum Constraints**: Limits values to valid business options

## Security Considerations

### Input Sanitization
Schemas provide the first line of defense against malformed input:

- **Size Limits**: Prevents excessively large inputs
- **Type Validation**: Ensures data types match expectations
- **Pattern Matching**: Validates format constraints
- **Injection Prevention**: Prevents malformed data injection

### Data Integrity
- **Strict Validation**: Uses `.strict()` to prevent additional properties
- **Type Coercion Control**: Explicit control over type coercion
- **Business Rule Enforcement**: Validates business logic constraints
- **Error Information Limiting**: Prevents information leakage in error messages

## Testing and Debugging

### Schema Testing
Schemas can be tested independently:

```typescript
// Valid input testing
const validInput = { subject: "Test", text: "Content" };
expect(() => MessageContentSchema.parse(validInput)).not.toThrow();

// Invalid input testing  
const invalidInput = { text: "" }; // Missing required field
expect(() => MessageContentSchema.parse(invalidInput)).toThrow();
```

### Development Tools
- **Type Generation**: Automatic TypeScript type generation
- **Error Messages**: Detailed validation error messages
- **Schema Introspection**: Runtime schema analysis capabilities
- **Documentation Generation**: Automatic API documentation from schemas

## Future Enhancements

### Schema Evolution
- **Version Management**: Schema versioning for backward compatibility
- **Migration Support**: Automated data migration between schema versions
- **Conditional Schemas**: More sophisticated conditional validation
- **Performance Optimization**: Enhanced validation performance

### Integration Expansion
- **OpenAPI Integration**: Automatic OpenAPI specification generation
- **Database Schema**: Integration with database schema validation
- **External Validation**: Integration with external validation services
- **Real-time Validation**: WebSocket-based real-time validation

This schema system provides a robust foundation for data validation, type safety, and business rule enforcement throughout the Core-Agent system, ensuring reliable and predictable data flow across all components.