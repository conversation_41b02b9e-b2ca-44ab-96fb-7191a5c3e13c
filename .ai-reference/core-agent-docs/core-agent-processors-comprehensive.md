# Core-Agent Processors - Comprehensive Documentation

## Overview

The Core-Agent processors are specialized BullMQ workers that handle different stages of the email processing pipeline. They implement the queue-based architecture pattern for reliable, scalable email processing with proper error handling and retry mechanisms.

## Architecture

### Processor Design Pattern
- **BullMQ Integration**: All processors extend `WorkerHost` from `@nestjs/bullmq`
- **Transaction Safety**: Database operations wrapped in transactions with proper rollback
- **Event Coordination**: Processors emit events for saga pattern coordination
- **Error Handling**: Comprehensive error handling with logging and retry capabilities

### Queue Flow Architecture
```
Email Saved → Email Saga Listener → BullMQ Flows:

With Attachments:
EmailSaved → IdentifyShipment → EventEmitter(FileBatchCreated) → Aggregation → HandleRequestMessage

Without Attachments:  
EmailSaved → IdentifyShipment → HandleRequestMessage
```

## Core Processors

### 1. IdentifyShipmentProcessor

**File**: `/apps/portal-api/src/core-agent/processors/identify-shipment.processor.ts`

**Purpose**: Identifies shipments associated with incoming emails and sets up the processing pipeline.

#### Key Responsibilities
- **Email Content Gathering**: Collects email text, subject, history, and attachments
- **Shipment Identification**: Uses CoreAgentService to identify shipments via LLM analysis
- **Thread Association**: Associates identified shipments with email threads for future emails
- **Status Management**: Updates email status based on identification results
- **Pipeline Coordination**: Determines next steps based on attachments and identification results

#### Process Flow
1. **Load Email Data**: Retrieves email with organization context and thread information
2. **Check Existing Associations**: Checks if thread already has an associated shipment
3. **Content Aggregation**: Gathers email content and attachment metadata
4. **LLM Analysis**: Uses CoreAgentService to identify shipments from content
5. **Result Processing**: Handles multiple scenarios (no shipment, single shipment, multiple shipments)
6. **Thread Association**: Associates found shipment with email thread
7. **Status Updates**: Updates email status for next processing stage

#### Shipment Identification Logic
```typescript
// Helper function to validate shipment uniqueness
function isSameShipment(foundShipments: Array<{ shipmentId?: number }>): boolean {
  // Validates that all found identifiers point to the same unique shipment
  // Returns true if all identifiers reference the same shipment ID
}
```

#### Status Transitions
- **No Shipment Found (No Attachments)**: `SAVED` → `FAILED_SHIPMENT_SEARCH`
- **No Shipment Found (Has Attachments)**: `SAVED` → `AGGREGATING_EMAIL`
- **Multiple Different Shipments**: `SAVED` → `FAILED_SHIPMENT_SEARCH`
- **Single Shipment Identified**: `SAVED` → `COMPLETED_SHIPMENT_SEARCH`

#### Event Emissions
- **THREAD_SHIPMENT_NOT_FOUND**: When no shipment identified and no attachments
- **THREAD_TOO_MANY_SHIPMENTS_FOUND**: When multiple different shipments identified

#### Error Handling
- **Transaction Rollback**: Automatic rollback on processing failures
- **Connection Management**: Proper cleanup of database connections
- **Retry Strategy**: BullMQ handles retries for transient failures
- **Logging**: Comprehensive logging for debugging and monitoring

### 2. EventEmitterProcessor

**File**: `/apps/portal-api/src/core-agent/processors/event-emitter.processor.ts`

**Purpose**: Generic event emission processor for decoupling event emission from business logic processing.

#### Key Responsibilities
- **Event Decoupling**: Separates event emission from core business logic
- **Flow Integration**: Enables FlowProducer sequences to emit events as part of workflows
- **Event Broadcasting**: Uses EventEmitter2 for reliable event distribution
- **Logging**: Provides event emission tracking and debugging

#### Design Benefits
- **Modularity**: Keeps event emission separate from business processing
- **Reliability**: Events are queued and processed reliably through BullMQ
- **Flow Integration**: Enables events as part of complex workflow sequences
- **Debugging**: Centralized event emission logging

#### Usage Pattern
```typescript
// Events are queued as jobs with eventType and eventData
{
  name: "emit-file-batch-created",
  queueName: CoreAgentQueueName.EVENT_EMITTER,
  data: {
    eventType: FILE_BATCH_CREATED,
    eventData: new FileBatchCreatedEvent(gmailId, organizationId, fileIds)
  }
}
```

#### Integration Points
- **FlowProducer Sequences**: Used within BullMQ flows for saga coordination
- **Event System**: Bridges BullMQ queues with NestJS EventEmitter2
- **Saga Pattern**: Enables event-driven saga orchestration

## Integration with Email Pipeline

### Saga Pattern Implementation
The processors implement the saga pattern for reliable email processing:

1. **Email Saga Listener**: Listens for EMAIL_SAVED events and orchestrates processing
2. **Flow Coordination**: Uses BullMQ FlowProducer for sequential job execution
3. **State Management**: Pessimistic locking ensures proper state transitions
4. **Error Recovery**: Transaction rollback and retry mechanisms for failure recovery

### Request-Scoped Services
Both processors handle REQUEST-scoped service resolution:

```typescript
// Create synthetic request context for request-scoped services
const contextId = ContextIdFactory.create();
const requestContext = generateRequest(null, organization);
this.moduleRef.registerRequestByContextId(requestContext, contextId);

// Resolve request-scoped services
const fileBatchService = await this.moduleRef.resolve(FileBatchService, contextId, {
  strict: false
});
```

### Transaction Management
All processors implement robust transaction handling:

```typescript
const queryRunner = this.dataSource.createQueryRunner();
await queryRunner.connect();
await queryRunner.startTransaction();

try {
  // Processing logic here
  await queryRunner.commitTransaction();
} catch (error) {
  if (queryRunner.isTransactionActive) {
    await queryRunner.rollbackTransaction();
  }
  throw error;
} finally {
  await queryRunner.release();
}
```

## Performance Considerations

### Optimization Strategies
- **Lazy Loading**: Email content and attachments loaded only when needed  
- **Pessimistic Locking**: Prevents race conditions in concurrent processing
- **Connection Pooling**: Efficient database connection management
- **Memory Management**: Proper cleanup of resources and connections

### Scalability Features
- **Queue-Based Processing**: Horizontal scaling through multiple workers
- **Flow Coordination**: Complex workflows broken into manageable steps
- **Event-Driven Architecture**: Loose coupling enables independent scaling
- **Transaction Isolation**: Safe concurrent processing of different emails

## Testing and Debugging

### Processor Testing
The processors can be tested independently:

```bash
# Test processor logic directly
./src/core-agent/testing/run-processor-test-with-logs.sh

# Test specific intent processing
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS
```

### Debugging Features
- **Comprehensive Logging**: Detailed logs at each processing stage
- **Transaction Tracking**: Database operation logging for debugging
- **Error Context**: Rich error messages with processing context
- **Performance Metrics**: Processing time tracking for performance analysis

## Security Considerations

### Multi-Tenant Security
- **Organization Scoping**: All operations scoped to specific organizations
- **Request Context**: Maintains proper security context through service resolution
- **Data Isolation**: Transaction-level isolation between organizations

### Input Validation
- **Email Validation**: Comprehensive validation of email content and metadata
- **Attachment Safety**: Safe handling of email attachments and file processing
- **SQL Injection Prevention**: Parameterized queries and input sanitization

## Future Enhancements

### Extensibility Points
- **New Processors**: Framework supports additional specialized processors
- **Event Types**: Easy addition of new event types for workflow coordination
- **Flow Patterns**: Support for more complex workflow patterns
- **Integration Points**: Clean interfaces for external system integration

### Performance Optimization
- **Caching Strategies**: Potential for caching frequently accessed data
- **Parallel Processing**: Enhanced parallel processing capabilities
- **Resource Optimization**: More sophisticated resource management
- **Monitoring Integration**: Enhanced monitoring and alerting capabilities

This processor architecture provides a robust, scalable foundation for email processing with proper error handling, transaction safety, and event coordination capabilities.