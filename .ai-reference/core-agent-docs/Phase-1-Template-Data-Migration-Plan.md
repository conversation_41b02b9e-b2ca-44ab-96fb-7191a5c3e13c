# Phase 1: Template Data Migration Plan

**Date**: July 31, 2025  
**Scope**: Replace fragment-based template system with unified template + clean-agent-context  
**Duration**: 1-2 weeks  
**Goal**: Migrate from 61 template files to single consolidated template system

## Phase 1 Overview

This phase focuses on migrating the template data layer while maintaining all existing business logic. We will:

1. **Use clean-agent-context exclusively** for data capture (even if agent-context could provide same data)
2. **Replace fragment system** with section flags (`SHOW_X` approach)
3. **Preserve CAD document attachment** functionality with cleaner patterns
4. **Create new template render system** in core-agent module

## Current vs Target Architecture

### Current System
```
Intent Handler → ShipmentContextService → Fragment Selection → Multiple Templates → Email
                     ↓
               Template Context + Smart Context
```

### Target System  
```
Intent Handler → CleanAgentContextService + EmailMessageService → Single Template → Email
                     ↓                           ↓
            Template Data (SHOW_X flags)    Message Constants
```

## Implementation Steps

### Step 1.1: Extend CleanAgentContextService

**Goal**: Provide all template variables and section flags needed by the unified template

#### 1.1.1: Add Missing Template Variable Gatherers

Create new data gatherers to complete template variable coverage:

```typescript
// File: apps/portal-api/src/clean-agent-context/data-gatherers/template-variables.data-gatherer.ts

@Injectable()
export class TemplateVariablesDataGatherer {
  async gatherTemplateVariables(shipment: Shipment): Promise<TemplateVariableData> {
    return {
      // Placeholder variables for details section
      CCN_PLACEHOLDER: shipment.cargoControlNumber || 'N/A',
      CONTAINER_PLACEHOLDER: this.formatContainerNumbers(shipment.containers),
      HBL_PLACEHOLDER: shipment.houseBladeNumber || 'N/A',
      
      // Status placeholders  
      HBL_STATUS_PLACEHOLDER: await this.getHBLStatus(shipment),
      AN_EMF_STATUS_PLACEHOLDER: await this.getAnEmfStatus(shipment),
      CI_PL_STATUS_PLACEHOLDER: await this.getCIPLStatus(shipment),
      CUSTOMS_STATUS_LINE_PLACEHOLDER: this.formatCustomsStatusLine(shipment),
      
      // Missing items placeholders
      MISSING_DOCUMENTS_PLACEHOLDER: await this.formatMissingDocuments(shipment),
      MISSING_FIELDS_PLACEHOLDER: await this.formatMissingFields(shipment),
      COMPLIANCE_ERRORS_PLACEHOLDER: await this.formatComplianceErrors(shipment)
    };
  }

  private formatContainerNumbers(containers: Container[]): string {
    if (!containers?.length) return 'N/A';
    return containers.map(c => c.containerNumber).join(', ');
  }

  private async getHBLStatus(shipment: Shipment): Promise<string> {
    // Logic to determine HBL document status
    const hblDoc = await this.findHBLDocument(shipment);
    return hblDoc ? 'Received' : 'Missing';
  }

  // ... similar methods for other status determinations
}
```

#### 1.1.2: Add Section Flag Gatherer

Replace fragment selection with section visibility flags:

```typescript
// File: apps/portal-api/src/clean-agent-context/data-gatherers/section-flags.data-gatherer.ts

@Injectable() 
export class SectionFlagsDataGatherer {
  async gatherSectionFlags(
    shipment: Shipment,
    intent: IntentType,
    documentData: DocumentTemplateData,
    validationData: ValidationTemplateData
  ): Promise<SectionFlagsData> {
    return {
      // RNS section
      SHOW_RNS_PROOF: await this.shouldShowRNSProof(shipment, intent),
      
      // Document processing section
      SHOW_PROCESSED_DOCUMENTS: documentData.PROCESSED_DOCUMENTS?.length > 0,
      
      // Validation sections
      SHOW_VALIDATION_ISSUES: this.hasValidationIssues(validationData),
      SHOW_DETAILS: await this.shouldShowDetails(shipment, intent),
      SHOW_COMPLIANCE_ERRORS: validationData.COMPLIANCE_ERRORS?.length > 0,
    };
  }

  private async shouldShowRNSProof(shipment: Shipment, intent: IntentType): boolean {
    // Logic equivalent to old fragment selection for RNS proof
    return intent === 'request-rns-proof' && 
           ['entry-accepted', 'exam', 'released'].includes(shipment.customsStatus);
  }

  private hasValidationIssues(validationData: ValidationTemplateData): boolean {
    return validationData.CI_PL_MISSING || 
           validationData.HBL_MISSING || 
           validationData.AN_EMF_MISSING ||
           validationData.WEIGHT_MISSING ||
           validationData.PORT_CODE_MISSING ||
           validationData.CCN_MISSING ||
           validationData.OGD_FILING_PENDING;
  }

  private async shouldShowDetails(shipment: Shipment, intent: IntentType): boolean {
    // Logic to determine when to show details section
    const detailIntents = [
      'get-shipment-status', 
      'request-cad-document', 
      'request-rns-proof'
    ];
    return detailIntents.includes(intent);
  }
}
```

#### 1.1.3: Update CleanAgentContextService Interface

Extend the main service to provide all template data:

```typescript
// File: apps/portal-api/src/clean-agent-context/clean-agent-context.service.ts

interface CompleteTemplateData extends 
  ShipmentTemplateData,
  DocumentTemplateData, 
  RnsTemplateData,
  ImporterTemplateData,
  ValidationTemplateData {
  
  // Template variables
  CCN_PLACEHOLDER: string;
  CONTAINER_PLACEHOLDER: string;
  HBL_PLACEHOLDER: string;
  HBL_STATUS_PLACEHOLDER: string;
  AN_EMF_STATUS_PLACEHOLDER: string;
  CI_PL_STATUS_PLACEHOLDER: string;
  CUSTOMS_STATUS_LINE_PLACEHOLDER: string;
  MISSING_DOCUMENTS_PLACEHOLDER: string;
  MISSING_FIELDS_PLACEHOLDER: string;
  COMPLIANCE_ERRORS_PLACEHOLDER: string;
  
  // Section flags
  SHOW_RNS_PROOF: boolean;
  SHOW_PROCESSED_DOCUMENTS: boolean;
  SHOW_VALIDATION_ISSUES: boolean;
  SHOW_DETAILS: boolean;
  SHOW_COMPLIANCE_ERRORS: boolean;
}

@Injectable()
export class CleanAgentContextService {
  constructor(
    // ... existing gatherers
    private readonly templateVariablesGatherer: TemplateVariablesDataGatherer,
    private readonly sectionFlagsGatherer: SectionFlagsDataGatherer,
  ) {}

  async gatherCompleteTemplateData(
    shipment: Shipment, 
    intent: IntentType
  ): Promise<CompleteTemplateData> {
    // Gather all data types in parallel
    const [
      shipmentData,
      documentData, 
      rnsData,
      importerData,
      validationData,
      templateVariables
    ] = await Promise.all([
      this.gatherShipmentData(shipment),
      this.gatherDocumentData(shipment),
      this.gatherRnsData(shipment),
      this.gatherImporterData(shipment),
      this.gatherValidationData(shipment),
      this.templateVariablesGatherer.gatherTemplateVariables(shipment)
    ]);

    // Generate section flags based on gathered data and intent
    const sectionFlags = await this.sectionFlagsGatherer.gatherSectionFlags(
      shipment, intent, documentData, validationData
    );

    return {
      ...shipmentData,
      ...documentData,
      ...rnsData, 
      ...importerData,
      ...validationData,
      ...templateVariables,
      ...sectionFlags
    };
  }
}
```

### Step 1.2: Create EmailMessageService

**Goal**: Map business state and intent to appropriate message constants

#### 1.2.1: Message Selection Logic

```typescript
// File: apps/portal-api/src/core-agent/services/email-message.service.ts

@Injectable()
export class EmailMessageService {
  getMessagesForIntent(
    intent: IntentType, 
    templateData: CompleteTemplateData
  ): EmailMessageData {
    const customsStatus = templateData.CUSTOMS_STATUS;
    const transportMode = this.determineTransportMode(templateData);
    
    return {
      // Document acknowledgment messages
      ACKNOWLEDGE_DOCUMENTS_MESSAGE: this.getAcknowledgeDocumentsMessage(customsStatus, transportMode),
      ACKNOWLEDGE_MISSING_DOCUMENTS_MESSAGE: emailResponseMessages.acknowledgeMissingDocuments.default,
      DOCUMENTATION_COMING_MESSAGE: emailResponseMessages.documentationComing.default,
      PROCESS_DOCUMENT_MESSAGE: this.getProcessDocumentMessage(templateData),
      
      // Document request messages  
      REQUEST_CAD_DOCUMENT_MESSAGE: this.getCADDocumentMessage(customsStatus),
      REQUEST_RNS_PROOF_MESSAGE: this.getRNSProofMessage(customsStatus),
      
      // Processing request messages
      REQUEST_RUSH_PROCESSING_MESSAGE: this.getRushProcessingMessage(customsStatus, templateData),
      REQUEST_MANUAL_PROCESSING_MESSAGE: emailResponseMessages.requestManualProcessing.default,
      REQUEST_HOLD_SHIPMENT_MESSAGE: emailResponseMessages.requestHoldShipment.default,
      
      // Data update messages
      UPDATE_SHIPMENT_MESSAGE: this.getUpdateShipmentMessage(templateData),
      
      // Dynamic content messages
      ETA_RESPONSE_MESSAGE: this.getETAResponseMessage(templateData),
      TRANSACTION_NUMBER_RESPONSE_MESSAGE: this.getTransactionNumberMessage(templateData),
      RELEASE_STATUS_RESPONSE_MESSAGE: this.getReleaseStatusMessage(templateData),
      SHIPPING_STATUS_RESPONSE_MESSAGE: this.getShippingStatusMessage(templateData),
      
      // Status message
      GET_SHIPMENT_STATUS_MESSAGE: this.getCustomsStatusMessage(customsStatus, templateData),
      
      // Error/fallback messages
      SYSTEM_ERROR_MESSAGE: emailResponseMessages.systemError.default,
      CONTACT_SUPPORT_MESSAGE: emailResponseMessages.contactSupport.default
    };
  }

  private getAcknowledgeDocumentsMessage(customsStatus: string, transportMode: string): string {
    if (transportMode === 'truck') {
      return emailResponseMessages.acknowledgeDocuments.truck;
    }
    return emailResponseMessages.acknowledgeDocuments.ocean_air;
  }

  private getCADDocumentMessage(customsStatus: string): string {
    return emailResponseMessages.requestCADDocument[customsStatus] || 
           emailResponseMessages.requestCADDocument.default;
  }

  private getRNSProofMessage(customsStatus: string): string {
    return emailResponseMessages.requestRNSProof[customsStatus] || 
           emailResponseMessages.requestRNSProof.default;
  }

  private getRushProcessingMessage(customsStatus: string, templateData: CompleteTemplateData): string {
    let message = emailResponseMessages.requestRushProcessing[customsStatus] || 
                  emailResponseMessages.requestRushProcessing.default;
    
    // Handle dynamic date substitution for released status
    if (customsStatus === 'released' && templateData.shipment?.releaseDate) {
      message = message.replace('{{ shipment.releaseDate | date(\'d-m-Y\') }}', 
                               this.formatDate(templateData.shipment.releaseDate));
    }
    
    return message;
  }

  private getCustomsStatusMessage(customsStatus: string, templateData: CompleteTemplateData): string {
    let message = emailResponseMessages.getCustomsStatus[customsStatus] || 
                  emailResponseMessages.getCustomsStatus.default;
    
    // Handle dynamic ETA substitution
    if (message.includes('{{ eta | date(\'F j, Y\') }}') && templateData.ETA_DATE) {
      message = message.replace('{{ eta | date(\'F j, Y\') }}', 
                               this.formatDate(templateData.ETA_DATE, 'F j, Y'));
    }
    
    return message;
  }

  // ... additional message methods
}
```

### Step 1.3: Create New Template Render System

**Goal**: Replace fragment-based rendering with unified template rendering

#### 1.3.1: Unified Template Renderer

```typescript
// File: apps/portal-api/src/core-agent/services/unified-template-renderer.service.ts

@Injectable()
export class UnifiedTemplateRendererService {
  constructor(
    private readonly cleanAgentContextService: CleanAgentContextService,
    private readonly emailMessageService: EmailMessageService,
    private readonly cadDocumentService: CADDocumentService, // Preserve existing CAD functionality
    @Inject('TEMPLATE_ENGINE') private readonly templateEngine: any
  ) {}

  async renderEmailResponse(
    shipment: Shipment,
    intent: IntentType,
    validatedIntent: ValidatedIntent
  ): Promise<EmailRenderResult> {
    // 1. Gather complete template data using clean-agent-context
    const templateData = await this.cleanAgentContextService.gatherCompleteTemplateData(
      shipment, 
      intent
    );

    // 2. Get messages for this intent
    const messages = this.emailMessageService.getMessagesForIntent(intent, templateData);

    // 3. Handle CAD document attachment (preserve existing pattern)
    const cadAttachment = await this.handleCADDocumentAttachment(
      shipment, 
      intent, 
      templateData
    );

    // 4. Combine all data for template
    const completeTemplateContext = {
      ...templateData,
      ...messages
    };

    // 5. Render unified template
    const emailContent = await this.templateEngine.render(
      'email-response-complete-template.njk',
      completeTemplateContext
    );

    return {
      content: emailContent,
      attachments: cadAttachment ? [cadAttachment] : [],
      templateData: completeTemplateContext
    };
  }

  private async handleCADDocumentAttachment(
    shipment: Shipment,
    intent: IntentType,
    templateData: CompleteTemplateData
  ): Promise<DocumentAttachment | null> {
    // Preserve existing CAD document logic with cleaner pattern
    if (intent !== 'request-cad-document') {
      return null;
    }

    // Check if CAD should be generated based on customs status
    const canGenerateCAD = this.shouldGenerateCAD(shipment.customsStatus);
    if (!canGenerateCAD) {
      return null;
    }

    try {
      // Use existing CAD generation service
      const cadDocument = await this.cadDocumentService.generateCADDocument(shipment);
      return {
        filename: `CAD_${shipment.cargoControlNumber}.pdf`,
        content: cadDocument.buffer,
        contentType: 'application/pdf'
      };
    } catch (error) {
      console.error('CAD document generation failed:', error);
      return null;
    }
  }

  private shouldGenerateCAD(customsStatus: string): boolean {
    // Clean pattern for CAD generation logic
    const cadReadyStatuses = ['pending-arrival', 'live', 'entry-submitted', 'entry-accepted', 'exam', 'released'];
    return cadReadyStatuses.includes(customsStatus);
  }
}

interface EmailRenderResult {
  content: string;
  attachments: DocumentAttachment[];
  templateData: CompleteTemplateData & EmailMessageData;
}

interface DocumentAttachment {
  filename: string;
  content: Buffer;
  contentType: string;
}
```

#### 1.3.2: Template Engine Configuration

```typescript
// File: apps/portal-api/src/core-agent/config/template-engine.config.ts

import * as nunjucks from 'nunjucks';

export const createTemplateEngine = () => {
  const env = nunjucks.configure('src/core-agent', {
    autoescape: true,
    throwOnUndefined: false
  });

  // Add custom filters for date formatting
  env.addFilter('date', (date: Date | string, format: string = 'Y-m-d') => {
    if (!date) return '';
    const d = new Date(date);
    return this.formatDate(d, format);
  });

  env.addFilter('capitalize', (str: string) => {
    return str ? str.charAt(0).toUpperCase() + str.slice(1) : '';
  });

  return env;
};

// Module configuration
export const TemplateEngineProvider = {
  provide: 'TEMPLATE_ENGINE',
  useFactory: createTemplateEngine
};
```

### Step 1.4: Update Intent Handlers

**Goal**: Replace fragment-based rendering with unified template system

#### 1.4.1: Handler Interface Update

```typescript
// File: apps/portal-api/src/core-agent/handlers/base-intent-handler.ts

export abstract class BaseIntentHandler implements IntentHandler {
  constructor(
    protected readonly unifiedTemplateRenderer: UnifiedTemplateRendererService
  ) {}

  abstract handle(
    validatedIntent: ValidatedIntent, 
    shipment: Shipment
  ): Promise<HandlerResult>;

  protected async renderResponse(
    shipment: Shipment,
    intent: IntentType,
    validatedIntent: ValidatedIntent
  ): Promise<HandlerResult> {
    const renderResult = await this.unifiedTemplateRenderer.renderEmailResponse(
      shipment,
      intent, 
      validatedIntent
    );

    return {
      emailContent: renderResult.content,
      attachments: renderResult.attachments,
      success: true
    };
  }
}
```

#### 1.4.2: Example Handler Migration

```typescript
// File: apps/portal-api/src/core-agent/handlers/acknowledge-documents.handler.ts

@Injectable()  
export class AcknowledgeDocumentsHandler extends BaseIntentHandler {
  async handle(
    validatedIntent: ValidatedIntent,
    shipment: Shipment
  ): Promise<HandlerResult> {
    // Business logic operations (preserved from existing handler)
    await this.updateDocumentStatus(shipment, 'acknowledged');
    
    // Simple template rendering (replaces complex fragment logic)
    return this.renderResponse(shipment, 'acknowledge-documents', validatedIntent);
  }

  private async updateDocumentStatus(shipment: Shipment, status: string): Promise<void> {
    // Preserve existing business logic operations
    // This stays exactly the same as current implementation
  }
}

// OLD APPROACH (to be removed):
// const fragments: ResponseFragment[] = [];
// if (context.missingDocuments.length > 0) {
//   fragments.push({ template: "acknowledgement-missing-docs", priority: 1 });
// }
// return this.shipmentResponseService.renderFragments(fragments, context);

// NEW APPROACH (unified):
// return this.renderResponse(shipment, 'acknowledge-documents', validatedIntent);
```

### Step 1.5: Module Integration

**Goal**: Wire up all new services in the core-agent module

#### 1.5.1: Core Agent Module Updates

```typescript
// File: apps/portal-api/src/core-agent/core-agent.module.ts

import { CleanAgentContextModule } from '../clean-agent-context/clean-agent-context.module';
import { TemplateVariablesDataGatherer } from '../clean-agent-context/data-gatherers/template-variables.data-gatherer';
import { SectionFlagsDataGatherer } from '../clean-agent-context/data-gatherers/section-flags.data-gatherer';
import { EmailMessageService } from './services/email-message.service';
import { UnifiedTemplateRendererService } from './services/unified-template-renderer.service';
import { TemplateEngineProvider } from './config/template-engine.config';

@Module({
  imports: [
    CleanAgentContextModule, // Use clean-agent-context exclusively
    // Note: Keep AgentContextModule for now (business logic operations)
    // but use clean-agent-context for all template data
  ],
  providers: [
    TemplateVariablesDataGatherer,
    SectionFlagsDataGatherer,
    EmailMessageService,
    UnifiedTemplateRendererService,
    TemplateEngineProvider,
    
    // All existing intent handlers (updated to use new system)
    AcknowledgeDocumentsHandler,
    ProcessDocumentHandler,
    RequestCADDocumentHandler,
    // ... all other handlers
  ],
  exports: [
    UnifiedTemplateRendererService,
    EmailMessageService
  ]
})
export class CoreAgentModule {}
```

## Testing Strategy

### Step 1.6: Comprehensive Testing

#### 1.6.1: Template Data Testing

```typescript
// File: apps/portal-api/src/core-agent/testing/template-data.test.ts

describe('CleanAgentContextService Template Data', () => {
  it('should provide all required template variables', async () => {
    const templateData = await cleanAgentContextService.gatherCompleteTemplateData(
      mockShipment, 
      'get-shipment-status'
    );
    
    // Verify all placeholder variables are present
    expect(templateData.CCN_PLACEHOLDER).toBeDefined();
    expect(templateData.CONTAINER_PLACEHOLDER).toBeDefined();
    expect(templateData.HBL_PLACEHOLDER).toBeDefined();
    
    // Verify section flags are set correctly
    expect(typeof templateData.SHOW_DETAILS).toBe('boolean');
    expect(typeof templateData.SHOW_VALIDATION_ISSUES).toBe('boolean');
  });
  
  it('should generate appropriate section flags for each intent', async () => {
    const statusTemplateData = await cleanAgentContextService.gatherCompleteTemplateData(
      mockShipment, 
      'get-shipment-status'
    );
    expect(statusTemplateData.SHOW_DETAILS).toBe(true);
    
    const cadTemplateData = await cleanAgentContextService.gatherCompleteTemplateData(
      mockShipment,
      'request-cad-document'  
    );
    expect(cadTemplateData.SHOW_RNS_PROOF).toBe(false);
  });
});
```

#### 1.6.2: Message Service Testing

```typescript
describe('EmailMessageService', () => {
  it('should select correct messages based on customs status', () => {
    const messages = emailMessageService.getMessagesForIntent(
      'request-rush-processing',
      { CUSTOMS_STATUS: 'pending-arrival', ...mockTemplateData }
    );
    
    expect(messages.REQUEST_RUSH_PROCESSING_MESSAGE).toContain(
      'We\'ve received your rush request'
    );
  });
  
  it('should handle dynamic message substitution', () => {
    const messages = emailMessageService.getMessagesForIntent(
      'get-shipment-status',
      { 
        CUSTOMS_STATUS: 'pending-arrival',
        ETA_DATE: new Date('2025-08-15'),
        ...mockTemplateData 
      }
    );
    
    expect(messages.GET_SHIPMENT_STATUS_MESSAGE).toContain('August 15, 2025');
  });
});
```

#### 1.6.3: End-to-End Template Testing

```typescript
describe('Unified Template Rendering', () => {
  it('should render complete email for all intent types', async () => {
    const intents = [
      'acknowledge-documents',
      'process-document', 
      'request-cad-document',
      'request-rns-proof',
      'request-rush-processing',
      'get-shipment-status'
    ];
    
    for (const intent of intents) {
      const result = await unifiedTemplateRenderer.renderEmailResponse(
        mockShipment,
        intent,
        mockValidatedIntent
      );
      
      expect(result.content).toBeDefined();
      expect(result.content).toContain('Hello,');
      expect(result.content).toContain('Best regards,');
      expect(result.content.length).toBeGreaterThan(100);
    }
  });
  
  it('should include CAD attachment for CAD requests when appropriate', async () => {
    const result = await unifiedTemplateRenderer.renderEmailResponse(
      { ...mockShipment, customsStatus: 'live' },
      'request-cad-document',
      mockValidatedIntent
    );
    
    expect(result.attachments).toHaveLength(1);
    expect(result.attachments[0].filename).toMatch(/CAD_.*\.pdf/);
  });
});
```

## Migration Timeline

### Week 1: Foundation
- **Days 1-2**: Implement TemplateVariablesDataGatherer and SectionFlagsDataGatherer
- **Days 3-4**: Create EmailMessageService with complete message mapping
- **Days 5**: Update CleanAgentContextService to provide CompleteTemplateData

### Week 2: Integration & Testing  
- **Days 1-2**: Implement UnifiedTemplateRendererService with CAD attachment support
- **Days 3**: Update BaseIntentHandler and migrate 2-3 simple handlers
- **Days 4-5**: Comprehensive testing and validation

## Success Criteria

### Functional Requirements
- [ ] All template variables populated correctly
- [ ] All section flags generate appropriate visibility  
- [ ] All message constants mapped to correct business states
- [ ] CAD document attachment preserved and working
- [ ] All intent handlers produce correct email content

### Technical Requirements
- [ ] Clean-agent-context used exclusively for template data
- [ ] Single template file handles all email scenarios
- [ ] Fragment system completely replaced with section flags
- [ ] Template rendering performance maintained or improved

### Quality Assurance
- [ ] All E2E tests pass with new template system
- [ ] Email content matches existing output for all scenarios
- [ ] CAD document generation works correctly
- [ ] No regression in email generation functionality

This phase sets the foundation for complete template system migration while preserving all business logic and maintaining system functionality.