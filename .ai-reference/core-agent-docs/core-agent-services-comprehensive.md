# Core-Agent Services - Comprehensive Documentation

## Overview

The Core-Agent services provide the business logic layer for AI-powered email processing and shipment management. These services integrate Large Language Models (LLMs) with domain-specific business rules to automate customs processing workflows.

## Architecture

### Service Design Principles
- **Single Responsibility**: Each service handles a specific aspect of the core-agent functionality
- **REQUEST Scope**: Services are REQUEST-scoped for proper multi-tenant isolation
- **LLM Integration**: Sophisticated integration with multiple LLM providers (OpenAI, DeepSeek)
- **Tool-Based Architecture**: LLM agents use structured tools for database operations
- **Error Resilience**: Comprehensive error handling with graceful degradation

### Service Dependencies
```
CoreAgentService (orchestrator)
├── EmailIntentAnalysisService
├── AnswerUserQueryService  
├── ShipmentIdentifierService
├── AgentToolsService (provides LLM tools)
├── ShipmentFieldExtractionService
└── DocumentProcessorService
```

## Core Services

### 1. CoreAgentService

**File**: `/apps/portal-api/src/core-agent/services/core-agent.service.ts`

**Purpose**: Main orchestration service that coordinates all core-agent operations and provides high-level API for email processing.

#### Key Responsibilities
- **Service Orchestration**: Coordinates between specialized services
- **Shipment Operations**: Retrieves and validates shipment data
- **Intent Analysis**: Delegates email intent analysis to specialized services
- **Shipment Identification**: Orchestrates shipment identification from email content

#### Service Scope
```typescript
@Injectable({ scope: Scope.REQUEST })
export class CoreAgentService {
  // REQUEST-scoped for proper organization isolation
}
```

#### Core Methods
- **`getShipmentById()`**: Retrieves shipments with organization isolation
- **`identifyShipment()`**: Delegates to ShipmentIdentifierService for LLM-based identification
- **`analyzeEmailIntents()`**: Delegates to EmailIntentAnalysisService for intent extraction

#### Error Handling
- **NotFoundException**: When shipments are not found or unauthorized
- **InternalServerErrorException**: For system-level failures
- **Comprehensive Logging**: Detailed error context for debugging

### 2. AgentToolsService

**File**: `/apps/portal-api/src/core-agent/services/agent-tools.service.ts`

**Purpose**: Provides structured LLM tools that enable AI agents to interact with the Claro database and business logic.

#### Tool Architecture
The service defines LLM tools using the `LlmToolDefinition` interface:

```typescript
interface LlmToolDefinition<T> {
  name: string;
  description: string;
  schema: ZodSchema<T>;
  run: (input: T) => Promise<any>;
  strict?: boolean;
}
```

#### Available Tools

##### Database Query Tools
1. **`getShipmentByIdentifier`**
   - **Purpose**: Queries database to find shipment ID by identifier
   - **Schema**: `LookupShipmentDBSchema`
   - **Implementation**: Uses ShipmentIdentifierService
   - **Organization Isolation**: Automatically scoped to organization

2. **`getShipmentById`**
   - **Purpose**: Fetches complete shipment data by database ID
   - **Schema**: `{ shipmentId: number }`
   - **Implementation**: Uses ShipmentService with full relations

##### Business Logic Tools
3. **`getShipmentComplianceDetails`**
   - **Purpose**: Retrieves detailed compliance validation report
   - **Schema**: `{ shipmentId: number }`
   - **Returns**: Comprehensive compliance analysis

4. **`getShipmentCustomsActivities`**
   - **Purpose**: Retrieves customs filing and activity history
   - **Schema**: `{ shipmentId: number }`
   - **Returns**: Complete customs processing timeline

5. **`getShipmentDutySummary`**
   - **Purpose**: Gets total duties and taxes summary
   - **Schema**: `{ shipmentId: number }`
   - **Returns**: Financial summary for shipment

##### Document Tools
6. **`getCommercialInvoices`**
   - **Purpose**: Lists commercial invoices linked to shipment
   - **Schema**: Supports pagination and line expansion
   - **Parameters**: `shipmentId`, `expandLines`, `skip`, `limit`

7. **`getDocuments`**
   - **Purpose**: Lists documents attached to shipment
   - **Schema**: Supports pagination
   - **Parameters**: `shipmentId`, `skip`, `limit`

#### Tool Usage Pattern
```typescript
const tools = agentToolsService.getTools(organizationId);
const llmResponse = await askLLMService.agent({
  promptTemplate: PromptTemplateName.SOME_TEMPLATE,
  variables: { /* variables */ },
  tools: tools,
  zodSchema: SomeSchema
});
```

### 3. ShipmentIdentifierService

**File**: `/apps/portal-api/src/core-agent/services/shipment-identifier.service.ts`

**Purpose**: Sophisticated shipment identification service that uses LLM agents with database lookup tools to identify shipments from unstructured email content.

#### Identification Strategy
1. **LLM Analysis**: Analyzes email content to extract potential identifiers
2. **Database Lookup**: Uses structured database queries to find matching shipments
3. **Comprehensive Search**: Falls back to searching all identifier types
4. **Validation**: Ensures unique shipment identification

#### Identifier Types Supported
```typescript
enum IdentifierType {
  hblNumber = "hblNumber",
  cargoControlNumber = "cargoControlNumber", 
  transactionNumber = "transactionNumber", // Also used for PARS
  containerNumber = "containerNumber"
}
```

#### Security Features
- **SQL Injection Prevention**: Whitelisted column names for database queries
- **Organization Isolation**: All queries scoped to organization context
- **Input Validation**: Comprehensive validation of identifier values

#### Search Algorithm
1. **Primary Search**: If LLM provides initial guess for identifier type
2. **Comprehensive Fallback**: Searches across all identifier types using OR queries
3. **Case-Insensitive Matching**: Uses SQL UPPER() for reliable matching
4. **Uniqueness Validation**: Ensures only one shipment matches before returning

#### Performance Optimizations
- **Single Query Strategy**: Uses OR conditions instead of multiple queries
- **Limited Results**: Uses `TAKE(2)` to efficiently determine uniqueness
- **Indexed Searches**: Leverages database indexes on identifier columns

### 4. AnswerUserQueryService

**File**: `/apps/portal-api/src/core-agent/services/answer-user-query.service.ts`

**Purpose**: Classifies user queries about shipments into predefined categories using LLM analysis.

#### Classification Categories
Defined in `QUESTION_CATEGORIES` constant:
- **Shipment Status Inquiries**
- **Document Requests**  
- **Rush Processing Requests**
- **Compliance Questions**
- **General Inquiries**

#### LLM Integration
- **Model**: Uses `CORE_AGENT_LLM_MODEL_FULL` for accurate classification
- **Template**: `PromptTemplateName.CLASSIFY_QUESTION`
- **Schema**: `ClassifyQuestionCategorySchema` for structured output
- **Validation**: Multiple layers of validation for response integrity

#### Error Handling
- **Input Validation**: Validates non-empty query strings
- **Response Validation**: Validates LLM response structure and content
- **Fallback Handling**: Graceful handling of classification failures

### 5. DocumentProcessorService

**File**: `/apps/portal-api/src/core-agent/services/document-processor.service.ts`

**Purpose**: Processes documents and attachments for intent handlers, providing validation, status lookup, and formatting utilities.

#### Key Capabilities

##### Attachment Validation
```typescript
validateCADAttachment(attachment: any): attachment is {
  fileName: string;
  mimeType: string;
  b64Data: string;
}
```

##### Document Processing
- **Status Lookup**: Queries database for actual document/file status
- **Display Formatting**: Formats document types and statuses for user display
- **URL Generation**: Creates Claro portal URLs for document access
- **Fallback Handling**: Provides sensible defaults for missing data

##### Database Integration
- **Multi-Table Queries**: Searches both Document and File tables
- **Aggregation Status**: Includes document aggregation status information
- **Organization Scoping**: All queries scoped to organization context

#### Document Status Management
- **Actual Status**: Uses `displayStatus` field when available (considers shipment mismatches)
- **Fallback Status**: Falls back to `status` field for basic status info
- **Aggregation Tracking**: Includes aggregation processing status

### 6. ShipmentFieldExtractionService

**File**: `/apps/portal-api/src/core-agent/services/shipment-field-extraction.service.ts`

**Purpose**: Extracts structured shipment update fields from unstructured text instructions using LLM processing.

#### Extraction Capabilities
Extracts fields for shipment updates:
- **Cargo Control Number**: Alphanumeric identifiers
- **Port Code**: 4-digit codes starting with '0'
- **Sub Location**: 4-digit location codes

#### LLM Processing
- **Model**: Uses `CORE_AGENT_LLM_MODEL_FULL`
- **Template**: `PromptTemplateName.EXTRACT_SHIPMENT_FIELDS`
- **Schema**: `ExtractShipmentFieldsSchema`
- **Temperature**: Low temperature (0.1) for consistent extraction

#### Data Cleaning
Implements sophisticated data cleaning:

```typescript
private cleanExtractedFields(fields: ExtractShipmentFieldsOutput): ExtractShipmentFieldsOutput {
  // Removes invalid characters and validates field formats
  // Ensures extracted data meets business rules
}
```

##### Cleaning Rules
- **Cargo Control Number**: Only alphanumeric, hyphens, underscores
- **Port Code**: Exactly 4 digits starting with '0'
- **Sub Location**: Exactly 4 digits
- **Invalid Data Removal**: Removes fields that don't match patterns

## Service Integration Patterns

### REQUEST-Scoped Resolution
All services are REQUEST-scoped for proper multi-tenant isolation:

```typescript
// Services automatically receive organization context from REQUEST
@Injectable({ scope: Scope.REQUEST })
export class SomeService {
  // Organization context available through request
}
```

### LLM Integration Pattern
Services follow consistent LLM integration patterns:

```typescript
const llmResponse = await this.askLLMService.ask({
  model: MODEL_CONSTANT,
  promptTemplate: PromptTemplateName.TEMPLATE_NAME,
  variables: { /* template variables */ },
  zodSchema: ValidationSchema,
  schemaName: "OutputTypeName",
  debug: true
});
```

### Error Handling Pattern
All services implement comprehensive error handling:

```typescript
try {
  // Service logic
} catch (error) {
  this.logger.error(`Service operation failed: ${error.message}`, error.stack);
  throw new InternalServerErrorException(`Operation failed: ${error.message}`);
}
```

## Performance Considerations

### Optimization Strategies
- **Request Scoping**: Efficient service resolution per request
- **Database Optimization**: Optimized queries with proper indexing
- **LLM Efficiency**: Uses appropriate model sizes for different tasks
- **Caching**: Leverages TypeORM second-level caching where appropriate

### Scalability Features
- **Stateless Design**: Services are stateless for horizontal scaling
- **Connection Pooling**: Efficient database connection management
- **Async Processing**: Non-blocking async operations throughout
- **Resource Management**: Proper cleanup and resource management

## Security Considerations

### Multi-Tenant Security
- **Organization Isolation**: All operations scoped to organization context
- **Request Context**: Security context maintained through REQUEST scope
- **Data Validation**: Comprehensive input validation and sanitization

### LLM Security
- **Prompt Injection Prevention**: Structured prompts with validation
- **Output Validation**: Zod schema validation for all LLM outputs
- **Rate Limiting**: Appropriate rate limiting for LLM API calls

## Testing Strategies

### Service Testing
Services can be tested independently:

```bash
# Test intent handlers with real data
node src/core-agent/testing/test-intent-handlers.js

# Test document processing
node src/core-agent/testing/test-processor-direct.js --intent=PROCESS_DOCUMENT
```

### Integration Testing
- **Database Integration**: Tests with real database connections
- **LLM Integration**: Tests with actual LLM providers
- **Error Scenarios**: Comprehensive error condition testing

## Future Enhancements

### Extensibility Points
- **New Tool Types**: Framework supports additional LLM tools
- **Additional Services**: Clean interfaces for new specialized services
- **LLM Providers**: Support for additional LLM providers
- **Integration Points**: APIs for external system integration

### Performance Optimization
- **Caching Strategies**: Enhanced caching for frequently accessed data
- **Parallel Processing**: Enhanced parallel processing capabilities
- **Resource Optimization**: More sophisticated resource management
- **Monitoring Integration**: Enhanced monitoring and alerting

This service architecture provides a robust, scalable foundation for AI-powered customs processing with proper error handling, security, and multi-tenant isolation.