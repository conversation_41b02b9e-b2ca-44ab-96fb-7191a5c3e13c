# Core-Agent Testing Infrastructure - Comprehensive Documentation

## Overview

The Core-Agent testing infrastructure provides comprehensive testing capabilities for the AI-powered email processing system. It includes specialized scripts for testing individual components, end-to-end workflows, and integration scenarios with real shipment data.

## Architecture

### Testing Strategy
- **Multi-Layer Testing**: Unit, integration, and end-to-end testing capabilities
- **Real Data Integration**: Tests with actual shipment and organization data
- **Component Isolation**: Individual testing of processors, handlers, and services
- **Workflow Validation**: End-to-end pipeline testing with comprehensive logging
- **Performance Analysis**: Processing time tracking and optimization insights

### Testing Components Structure
```
apps/portal-api/src/core-agent/testing/
├── README.md                           # Comprehensive testing documentation
├── test-intent-handlers.js             # Main intent handler testing
├── test-processor-direct.js            # Direct processor logic testing
├── find-test-shipments.js             # Shipment discovery and analysis
├── e2e-email-pipeline-nestjs.js        # Full pipeline end-to-end testing
├── db-query.js                        # Database query utilities
├── run-e2e-with-logs.sh               # E2E testing with logging
├── run-processor-test-with-logs.sh     # Processor testing with logging
└── [utility scripts...]               # Additional testing utilities
```

## Core Testing Scripts

### 1. Intent Handler Testing

**File**: `test-intent-handlers.js`

**Purpose**: Comprehensive testing of all 9 intent handlers with real shipment data and detailed output analysis.

#### Testing Capabilities
- **All Intent Types**: Tests all 9 supported intent handlers
- **Real Data Integration**: Uses actual shipment data from database
- **Side Effect Testing**: Validates side effects like email generation and backoffice alerts
- **Fragment Analysis**: Detailed analysis of response fragments
- **Performance Metrics**: Processing time tracking per intent

#### Supported Intent Handlers
| Intent | Description | Side Effects | Requirements |
|--------|-------------|--------------|--------------|
| `GET_SHIPMENT_STATUS` | Status inquiries | CoreAgent answers | Shipment |
| `PROCESS_DOCUMENT` | Document processing | Entry submission | Shipment |
| `REQUEST_RUSH_PROCESSING` | Rush requests | Backoffice alerts | None |
| `DOCUMENTATION_COMING` | Acknowledge incoming docs | None | None |
| `UPDATE_SHIPMENT` | Update shipment info | Field updates | Shipment |
| `REQUEST_CAD_DOCUMENT` | CAD document requests | Document generation | Shipment |
| `REQUEST_RNS_PROOF` | RNS proof requests | Document generation | Shipment |
| `REQUEST_MANUAL_PROCESSING` | Manual processing | Backoffice alerts | None |
| `REQUEST_HOLD_SHIPMENT` | Hold processing | Backoffice alerts | None |

#### Usage Examples
```bash
# Test all intents with demo organization
node src/core-agent/testing/test-intent-handlers.js

# Test specific intent
node src/core-agent/testing/test-intent-handlers.js --intent=REQUEST_CAD_DOCUMENT

# Test with specific shipment
node src/core-agent/testing/test-intent-handlers.js --shipment=123 --verbose

# Test without side effects (development mode)
node src/core-agent/testing/test-intent-handlers.js --no-side-effects
```

#### Command Line Options
- `--org=ID` - Organization ID (default: 3 - demo org)
- `--shipment=ID` - Specific shipment ID to test with
- `--intent=INTENT_NAME` - Test only specific intent
- `--no-side-effects` - Skip side effects like backoffice emails
- `--verbose` - Show detailed logging and fragment details

#### Test Output Analysis
```bash
🧪 Testing Intent: REQUEST_CAD_DOCUMENT
📝 Description: Request CAD document
📋 Instructions: Can you send me the CAD document?, Please provide the customs...
⚙️  Processing intent with handler...
✅ Handler completed in 245ms
📊 Generated 1 response fragments
🎨 Rendering fragments to HTML...
🔧 Side Effects Generated:
   - cadDocument: object
📧 Response Preview: Please see CAD document attached...
```

### 2. Direct Processor Testing

**File**: `test-processor-direct.js`

**Purpose**: Tests the `handle-request-message.processor.ts` logic directly, bypassing the full email pipeline to focus on processor-specific behavior.

#### Key Features
- **Direct Processor Testing**: Tests processor logic without email pipeline overhead
- **Intent Simulation**: Simulates different intent scenarios with custom instructions
- **Template Rendering**: Shows full HTML template rendering output
- **Fragment Analysis**: Detailed fragment generation and processing analysis
- **Custom Instructions**: Supports custom instruction text for testing

#### Usage Examples
```bash
# Run processor test with logging
./src/core-agent/testing/run-processor-test-with-logs.sh

# Test specific intent with auto-selected shipment
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --verbose

# Test with custom instructions
node src/core-agent/testing/test-processor-direct.js --intent=REQUEST_CAD_DOCUMENT --instructions="Please send me the CAD document for my shipment"

# Test all intents for specific shipment with HTML output
node src/core-agent/testing/test-processor-direct.js --shipment=123 --verbose --show-html
```

#### Command Line Options
- `--org=ID` - Organization ID (default: 3 - demo org)
- `--shipment=ID` - Specific shipment ID to test with
- `--intent=INTENT_NAME` - Test only specific intent
- `--instructions="text"` - Custom instructions for the intent
- `--verbose` - Show detailed logging including fragment details
- `--show-html` - Show the full rendered HTML output
- `--no-side-effects` - Skip side effects like backoffice emails

#### Testing Benefits
- **Focused Testing**: Isolates processor logic from email pipeline complexity
- **Rapid Iteration**: Faster testing cycles for processor development
- **Custom Scenarios**: Easy testing of different instruction scenarios
- **Template Validation**: Validates HTML template rendering

### 3. Shipment Discovery and Analysis

**File**: `find-test-shipments.js`

**Purpose**: Discovers suitable shipments for testing different scenarios and provides shipment analysis capabilities.

#### Discovery Capabilities
- **Diverse Shipment Selection**: Finds shipments across different customs statuses
- **Status Analysis**: Analyzes shipment distribution by customs status
- **Testing Recommendations**: Suggests optimal shipments for different test scenarios
- **Filtering Options**: Filters shipments by status, organization, and other criteria

#### Usage Examples
```bash
# Find diverse shipments for testing
node src/core-agent/testing/find-test-shipments.js

# Show analysis of all customs statuses
node src/core-agent/testing/find-test-shipments.js --analysis

# Find shipments with specific status
node src/core-agent/testing/find-test-shipments.js --status=released

# Limit results and specify organization
node src/core-agent/testing/find-test-shipments.js --org=1 --limit=5
```

#### Command Line Options
- `--org=ID` - Organization ID (default: 3)
- `--status=STATUS` - Filter by customs status
- `--limit=N` - Number of shipments to show (default: 10)
- `--analysis` - Show detailed analysis by status

#### Status-Based Testing Recommendations
- **`released`** - Best for all document requests (CAD, RNS)
- **`live`** - Good for document processing and CAD requests
- **`pending-confirmation`** - Good for status inquiries and updates
- **`pending-commercial-invoice`** - Good for status inquiries and rush requests

### 4. End-to-End Pipeline Testing

**File**: `e2e-email-pipeline-nestjs.js`

**Purpose**: Comprehensive end-to-end testing of the complete email processing pipeline from email creation to response generation.

#### E2E Testing Capabilities
- **Full Pipeline**: Tests complete email processing workflow
- **Real Integration**: Tests with actual email creation and processing
- **Database State**: Validates database state changes throughout pipeline
- **Queue Processing**: Tests BullMQ queue processing and coordination
- **Event System**: Validates event emission and handling

#### Pipeline Stages Tested
1. **Email Creation**: Creates test emails with attachments
2. **Saga Coordination**: Tests email saga listener processing
3. **Shipment Identification**: Validates shipment identification logic
4. **Intent Analysis**: Tests intent analysis and handler selection
5. **Response Generation**: Validates response template rendering
6. **Status Updates**: Tests email status progression

#### Usage Examples
```bash
# Run full e2e pipeline test
./src/core-agent/testing/run-e2e-with-logs.sh

# Run with custom parameters
node src/core-agent/testing/e2e-email-pipeline-nestjs.js --org=3 --verbose
```

### 5. Database Query Utilities

**File**: `db-query.js`

**Purpose**: Provides database query utilities for testing data discovery and validation.

#### Query Capabilities
- **Organization Listing**: Lists available organizations for testing
- **Custom SQL Execution**: Executes custom SQL queries for data analysis
- **Shipment Discovery**: Finds shipments for testing scenarios
- **Data Validation**: Validates database state after tests

#### Usage Examples
```bash
# List organizations
node src/core-agent/testing/db-query.js orgs

# Execute custom SQL
node src/core-agent/testing/db-query.js sql "SELECT COUNT(*) FROM shipment WHERE \"organizationId\" = 3"

# Find emails by status
node src/core-agent/testing/db-query.js sql "SELECT id, status FROM email WHERE \"organizationId\" = 3 LIMIT 10"
```

## Testing Workflows

### 1. Development Testing Workflow
```bash
# Step 1: Find suitable shipments
node src/core-agent/testing/find-test-shipments.js --analysis

# Step 2: Test specific intent with found shipment
node src/core-agent/testing/test-intent-handlers.js --shipment=456 --intent=GET_SHIPMENT_STATUS --verbose

# Step 3: Test processor logic directly
node src/core-agent/testing/test-processor-direct.js --shipment=456 --intent=GET_SHIPMENT_STATUS --verbose

# Step 4: Validate with end-to-end test
./src/core-agent/testing/run-e2e-with-logs.sh
```

### 2. Comprehensive Testing Workflow
```bash
# Test all handlers with auto-selected shipment
node src/core-agent/testing/test-intent-handlers.js --verbose

# Test with specific shipment across all intents
node src/core-agent/testing/test-intent-handlers.js --shipment=789 --verbose

# Test side effects (emails and backoffice alerts)
node src/core-agent/testing/test-intent-handlers.js --verbose

# Validate with full pipeline test
node src/core-agent/testing/e2e-email-pipeline-nestjs.js --verbose
```

### 3. Performance Testing Workflow
```bash
# Test processing times for all intents
node src/core-agent/testing/test-intent-handlers.js --verbose

# Test processor performance directly
node src/core-agent/testing/test-processor-direct.js --verbose

# Analyze performance metrics in output
grep "completed in" test-output.log
```

## Test Output Analysis

### Success Indicators
- **✅ Handler Success**: Intent handler completed successfully
- **📊 Fragment Count**: Number of response fragments generated
- **⏱️ Processing Time**: Time taken for intent processing
- **🔧 Side Effects**: Generated side effects (documents, emails, alerts)
- **📧 Response Preview**: Preview of generated response content

### Failure Analysis
- **❌ Handler Errors**: Intent handler failures with error messages
- **🚫 Validation Failures**: Schema validation or business rule failures
- **⚠️ Warning Conditions**: Non-critical issues that need attention
- **📊 Summary Statistics**: Overall test results and failure rates

### Performance Metrics
```bash
📈 Overall Results:
   Tests Passed: 8/9
   Total Fragments: 12
   Total Processing Time: 1,234ms
   Average Time per Intent: 154ms
```

## Error Handling and Troubleshooting

### Common Issues and Solutions

#### 1. No Shipments Found
```bash
# Check if organization has shipments
node src/core-agent/testing/db-query.js sql "SELECT COUNT(*) FROM shipment WHERE \"organizationId\" = 3"

# Try different organization
node src/core-agent/testing/find-test-shipments.js --org=1
```

#### 2. Handler Not Found
```bash
# Check available intents
node src/core-agent/testing/test-intent-handlers.js
```

#### 3. Side Effects Not Working
```bash
# Test without side effects first
node src/core-agent/testing/test-intent-handlers.js --no-side-effects

# Check logs for specific errors
node src/core-agent/testing/test-intent-handlers.js --verbose
```

#### 4. CAD/RNS Generation Fails
```bash
# Find shipments with released status
node src/core-agent/testing/find-test-shipments.js --status=released

# Test with released shipment
node src/core-agent/testing/test-intent-handlers.js --shipment=ID --intent=REQUEST_CAD_DOCUMENT
```

## Integration with Development Workflow

### Continuous Integration
The testing infrastructure integrates with development workflows:

```bash
# Quick smoke test during development
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --no-side-effects

# Comprehensive testing before commits
./src/core-agent/testing/run-e2e-with-logs.sh

# Performance regression testing
node src/core-agent/testing/test-intent-handlers.js --verbose > performance-results.log
```

### Debugging Support
- **Verbose Logging**: Detailed logs for debugging processor logic
- **HTML Output**: Full template rendering output for UI debugging
- **Fragment Analysis**: Detailed fragment generation analysis
- **Side Effect Tracking**: Comprehensive side effect validation

## Performance Optimization Insights

### Metrics Collection
The testing infrastructure provides performance insights:

- **Processing Time Tracking**: Per-intent processing time measurement
- **Fragment Generation Analysis**: Fragment count and complexity analysis
- **Database Query Performance**: Database operation timing
- **Template Rendering Performance**: HTML rendering time analysis

### Optimization Opportunities
- **Intent Handler Performance**: Identifies slow intent handlers
- **Database Query Optimization**: Highlights slow database operations
- **Template Rendering**: Identifies template rendering bottlenecks
- **Side Effect Processing**: Analyzes side effect generation performance

## Future Enhancements

### Testing Capabilities
- **Load Testing**: Performance testing under load conditions
- **Concurrent Testing**: Testing concurrent email processing
- **Error Scenario Testing**: Comprehensive error condition testing
- **Integration Testing**: Enhanced external system integration testing

### Automation Features
- **Automated Test Suites**: Scheduled comprehensive testing
- **Regression Testing**: Automated regression test detection
- **Performance Monitoring**: Continuous performance monitoring
- **Alert Integration**: Integration with monitoring and alerting systems

This testing infrastructure provides comprehensive validation capabilities for the Core-Agent system, ensuring reliability, performance, and correctness across all components and workflows.