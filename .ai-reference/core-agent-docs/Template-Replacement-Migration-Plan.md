# Template Replacement Migration Plan

**Date**: July 31, 2025  
**Scope**: Replace fragmented email template system with consolidated template + clean-agent-context  
**Target**: Migrate from 61+ template files to single unified template system

## Executive Summary

This migration plan outlines how to replace the current fragmented email template system (61+ `.njk` files) with the new consolidated template system consisting of:

- **Single Template**: `email-response-complete-template.njk`
- **Centralized Messages**: `email-response-messages.ts`
- **Data Provider**: `clean-agent-context` system

The analysis shows **this migration is viable** and can provide **complete functional coverage** while significantly improving maintainability.

## Current System Analysis

### Template System Coverage

**Old System**: 61 template files across multiple categories:
- **Core Templates**: 9 files (answer templates, status templates, error templates)
- **Status-Specific Templates**: 8 files (eta, transaction-number, release-status, etc.)
- **Fragment Templates**: 44 files (document processing, acknowledgments, details)

**New System**: 1 template file + 1 constants file:
- **Unified Template**: `email-response-complete-template.njk` (130 lines)
- **Message Constants**: `email-response-messages.ts` (143 lines)

### Functional Coverage Assessment

| Old Template Category | New System Coverage | Status |
|----------------------|---------------------|--------|
| Document Acknowledgment | `ACKNOWLEDGE_*_MESSAGE` variables | ✅ Complete |
| Status & Information | `emailResponseMessages.getCustomsStatus` | ✅ Complete |
| Document Requests | `REQUEST_CAD_DOCUMENT_MESSAGE`, `REQUEST_RNS_PROOF_MESSAGE` | ✅ Complete |
| Processing Requests | `REQUEST_RUSH_PROCESSING_MESSAGE`, etc. | ✅ Complete |
| Dynamic Content | `ETA_RESPONSE_MESSAGE`, `TRANSACTION_NUMBER_RESPONSE_MESSAGE` | ✅ Complete |
| Error/Fallback | `SYSTEM_ERROR_MESSAGE`, `CONTACT_SUPPORT_MESSAGE` | ✅ Complete |

**Result**: **100% functional coverage** - all email scenarios can be handled by the new system.

## Agent-Context Usage Analysis

### Template-Only Components (Can be Replaced)

**60-70% of agent-context system** is used exclusively for template data preparation:

#### Template Data Properties:
- `templateContext` (entire object) - Nunjucks template variables
- `smartTemplateContext` (entire object) - Template-ready display variables
- `formattedCustomsStatus` - Display formatting
- `shipmentIdentifiers.formattedContainers` - Template display helpers
- All formatting methods (`formatDateForTemplate`, `buildStatusContext`)

#### Template-Specific Services:
- `STATUS_RESPONSE_MESSAGES` constants
- `RUSH_ACTION_MESSAGES` constants
- `DOCUMENT_BLOCKER_REASONS` constants
- Template variable assembly methods

### Business Logic Components (Must be Preserved)

**30-40% of agent-context system** handles critical business operations:

#### Business Rule Flags:
```typescript
canRush: boolean;           // Controls submission workflow
canGenerateCAD: boolean;    // Controls document generation
isSubmitted: boolean;       // Controls handler flow logic
canBeModified: boolean;     // Controls edit permissions
```

#### Service Integration:
```typescript
_services: {
  entrySubmissionService,   // Customs submission operations
  emailService,            // Backoffice alert system
  shipmentService          // Database operations
}
```

#### Workflow Operations:
- Database transaction management
- External service calls (customs API, Candata)
- Business rule evaluation logic
- Side effect coordination

## Migration Strategy

### Phase 1: Template Data Migration

**Goal**: Replace template-specific data preparation with clean-agent-context + messages

#### Step 1.1: Handler Template Integration
```typescript
// Replace existing template rendering:
const fragments = await this.shipmentResponseService.renderFragments(
  templateNames,
  context  // Old agent-context
);

// With new consolidated template:
const templateData = await this.cleanAgentContextService.gatherAllData(shipment);
const messages = this.getMessagesForIntent(intent, templateData);
const emailContent = await this.renderTemplate('email-response-complete-template.njk', {
  ...templateData,
  ...messages
});
```

#### Step 1.2: Message Integration Service
Create `EmailMessageService` to map business state to message constants:

```typescript
class EmailMessageService {
  getMessagesForIntent(intent: IntentType, templateData: AllTemplateData): MessageData {
    const customsStatus = templateData.shipment.customsStatus;
    
    return {
      ACKNOWLEDGE_DOCUMENTS_MESSAGE: emailResponseMessages.acknowledgeDocuments[customsStatus] || emailResponseMessages.acknowledgeDocuments.default,
      REQUEST_CAD_DOCUMENT_MESSAGE: emailResponseMessages.requestCADDocument[customsStatus],
      ETA_RESPONSE_MESSAGE: this.getETAMessage(templateData),
      // ... map all message types
    };
  }
}
```

#### Step 1.3: Template Data Enhancement
Extend `clean-agent-context` to provide missing template variables:

```typescript
// Add to CleanAgentContextService:
async gatherTemplateVariables(shipment: Shipment): Promise<TemplateVariableData> {
  return {
    // Placeholder variables
    CCN_PLACEHOLDER: shipment.cargoControlNumber || 'N/A',
    CONTAINER_PLACEHOLDER: this.formatContainers(shipment.containers),
    HBL_PLACEHOLDER: shipment.houseBladeNumber || 'N/A',
    
    // Status placeholders
    HBL_STATUS_PLACEHOLDER: this.getHBLStatus(shipment),
    CI_PL_STATUS_PLACEHOLDER: this.getCIPLStatus(shipment),
    CUSTOMS_STATUS_LINE_PLACEHOLDER: this.formatCustomsStatus(shipment),
    
    // Missing items
    MISSING_DOCUMENTS_PLACEHOLDER: this.formatMissingDocuments(shipment),
    MISSING_FIELDS_PLACEHOLDER: this.formatMissingFields(shipment),
    COMPLIANCE_ERRORS_PLACEHOLDER: this.formatComplianceErrors(shipment)
  };
}
```

### Phase 2: Handler System Cleanup

**Goal**: Simplify intent handlers while preserving business logic

#### Step 2.1: Template Logic Extraction
Move template-specific logic from handlers to template/message layer:

```typescript
// OLD: Complex template fragment logic in handlers
const fragments: ResponseFragment[] = [];
if (context.missingDocuments.length > 0) {
  fragments.push({ template: "acknowledgement-missing-docs", priority: 1 });
}
if (context.complianceErrors.length > 0) {
  fragments.push({ template: "compliance-errors", priority: 2 });
}

// NEW: Simple message selection based on state
const messageType = this.determineMessageType(shipment, intent);
const templateData = await this.cleanAgentContextService.gatherAllData(shipment);
const messages = this.emailMessageService.getMessagesForIntent(messageType, templateData);
```

#### Step 2.2: Business Logic Preservation
Maintain business operations while simplifying template concerns:

```typescript
// Keep business logic operations:
if (canAttemptSubmission) {
  await context._services.entrySubmissionService.attemptShipmentSubmission(
    shipment, compliance, organizationId
  );
}

// Simplify response generation:
return this.generateUnifiedResponse(shipment, intent);
```

### Phase 3: System Integration

**Goal**: Integrate new template system with existing business logic

#### Step 3.1: Service Bridge
Create bridge service to maintain business logic while using new template system:

```typescript
class BusinessLogicContextService {
  async evaluateBusinessRules(shipment: Shipment): Promise<BusinessRuleContext> {
    return {
      canRush: this.evaluateCanRush(shipment),
      canGenerateCAD: this.evaluateCanGenerateCAD(shipment),
      isSubmitted: this.isSubmittedStatus(shipment.customsStatus),
      // ... all business rule evaluations
    };
  }
  
  async executeBusinessOperations(
    shipment: Shipment, 
    intent: IntentType, 
    businessRules: BusinessRuleContext
  ): Promise<OperationResult> {
    // Handle database operations, external API calls, etc.
  }
}
```

#### Step 3.2: Dual Context Usage
Use both clean-agent-context (templates) and business-logic-context (operations):

```typescript
async handle(validatedIntent: ValidatedIntent, shipment: Shipment): Promise<string> {
  // Business logic evaluation
  const businessRules = await this.businessLogicService.evaluateBusinessRules(shipment);
  
  // Execute business operations based on rules
  const operationResult = await this.businessLogicService.executeBusinessOperations(
    shipment, validatedIntent.intent, businessRules
  );
  
  // Generate template response
  const templateData = await this.cleanAgentContextService.gatherAllData(shipment);
  const messages = this.emailMessageService.getMessagesForIntent(
    validatedIntent.intent, templateData
  );
  
  return this.renderUnifiedTemplate(templateData, messages);
}
```

## Implementation Plan

### Phase 1: Foundation (1-2 weeks)
1. **Complete CleanAgentContextService**: Add missing template variable gatherers
2. **Create EmailMessageService**: Implement message selection logic
3. **Extend template data interfaces**: Add all placeholder variables
4. **Test consolidated template**: Verify all sections render correctly

### Phase 2: Handler Migration (2-3 weeks)
1. **Migrate 1-2 simple handlers**: Start with acknowledge-documents, documentation-coming
2. **Create business logic service**: Extract non-template operations
3. **Migrate complex handlers**: Process-document, request-rush-processing
4. **Update all intent handlers**: Replace fragment system with unified template

### Phase 3: Cleanup & Testing (1-2 weeks)
1. **Remove old template files**: Delete 61 obsolete .njk files
2. **Remove template-specific agent-context code**: Clean up formatting methods
3. **Comprehensive testing**: E2E tests for all email scenarios
4. **Performance validation**: Ensure no regression in response times

## Success Criteria

### Functional Requirements
- [ ] All email scenarios produce correct content
- [ ] All message types are properly populated
- [ ] All template variables are correctly substituted
- [ ] All business operations continue to function

### Technical Requirements
- [ ] Single template file handles all email generation
- [ ] Template complexity reduced by 95% (61 files → 1 file)
- [ ] Handler code simplified and focused on business logic
- [ ] Clean separation between template data and business operations

### Quality Requirements
- [ ] All E2E tests pass
- [ ] No performance regression
- [ ] Code maintainability improved
- [ ] Template consistency across all email types

## Risk Analysis

### Low Risk
- **Template Coverage**: Analysis shows 100% functional coverage possible
- **Message Completeness**: All message types identified and mappable
- **Data Availability**: Clean-agent-context can provide all required data

### Medium Risk
- **Business Logic Complexity**: Complex business rules need careful preservation
- **Service Integration**: Existing service dependencies must be maintained
- **Testing Coverage**: Need comprehensive E2E testing of all scenarios

### Mitigation Strategies
1. **Gradual Migration**: Migrate handlers one by one with thorough testing
2. **Dual System Support**: Run old and new systems in parallel during transition
3. **Comprehensive Testing**: Test all combinations of customs status, intent types, and document states
4. **Rollback Plan**: Maintain old system until new system is fully validated

## Conclusion

This migration is **highly recommended** and **technically feasible**. The analysis shows:

1. **Complete Template Coverage**: New system can handle all email scenarios
2. **Significant Simplification**: 61 template files → 1 unified template
3. **Business Logic Preservation**: Critical operations can be maintained
4. **Improved Maintainability**: Centralized messages and single template structure

The migration will result in a more maintainable, consistent, and robust email generation system while preserving all existing business logic and operations.