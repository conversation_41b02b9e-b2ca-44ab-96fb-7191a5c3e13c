# UnifiedTemplateRenderer Build & Test Plan

**Date**: July 31, 2025  
**Scope**: Build and test UnifiedTemplateRendererService with real database shipment data  
**Duration**: 2-3 days  
**Goal**: Validate unified template system works with all possible sections activated

## Overview

Build the `UnifiedTemplateRendererService` first to validate the unified template approach works with real data. This service will:

1. **Accept section flags object** from handlers (not determine flags itself)
2. **Use clean-agent-context** for data only (handlers handle conversion)  
3. **Render the single unified template** at `apps/portal-api/src/core-agent/email-response-complete-template.njk`
4. **Test with real DB data** to ensure everything works before handler migration

**Goal**: The primary goal is to successfully render the `email-response-complete-template.njk` template with real shipment data and all possible sections activated.

## Architecture Design

### Input/Output Flow
```
Handler → SectionFlags + TemplateData → UnifiedTemplateRenderer → Email HTML + Attachments
```

### Service Interface Design
```typescript
interface TemplateSectionFlags {
  SHOW_RNS_PROOF: boolean;
  SHOW_PROCESSED_DOCUMENTS: boolean;
  SHOW_VALIDATION_ISSUES: boolean;
  SHOW_DETAILS: boolean;
  SHOW_COMPLIANCE_ERRORS: boolean;
}

interface TemplateMessageData {
  ACKNOWLEDGE_DOCUMENTS_MESSAGE?: string;
  ACKNOWLEDGE_MISSING_DOCUMENTS_MESSAGE?: string;
  DOCUMENTATION_COMING_MESSAGE?: string;
  PROCESS_DOCUMENT_MESSAGE?: string;
  REQUEST_CAD_DOCUMENT_MESSAGE?: string;
  REQUEST_RNS_PROOF_MESSAGE?: string;
  REQUEST_RUSH_PROCESSING_MESSAGE?: string;
  REQUEST_MANUAL_PROCESSING_MESSAGE?: string;
  REQUEST_HOLD_SHIPMENT_MESSAGE?: string;
  UPDATE_SHIPMENT_MESSAGE?: string;
  ETA_RESPONSE_MESSAGE?: string;
  TRANSACTION_NUMBER_RESPONSE_MESSAGE?: string;
  RELEASE_STATUS_RESPONSE_MESSAGE?: string;
  SHIPPING_STATUS_RESPONSE_MESSAGE?: string;
  SYSTEM_ERROR_MESSAGE?: string;
  CONTACT_SUPPORT_MESSAGE?: string;
  GET_SHIPMENT_STATUS_MESSAGE?: string;
}

interface CADDocumentRequest {
  shouldGenerateCAD: boolean;
  cadReadyStatus: boolean;
}
```

## Step-by-Step Implementation

### Step 1: Create Base Service Structure

**File**: `apps/portal-api/src/core-agent/services/unified-template-renderer.service.ts`

```typescript
import { Injectable, Inject } from '@nestjs/common';
import { CleanAgentContextService } from '../../clean-agent-context/clean-agent-context.service';

@Injectable()
export class UnifiedTemplateRendererService {
  constructor(
    private readonly cleanAgentContextService: CleanAgentContextService,
    @Inject('TEMPLATE_ENGINE') private readonly templateEngine: any
  ) {}

  async renderEmailResponse(
    shipment: Shipment,
    sectionFlags: TemplateSectionFlags,
    messageData: TemplateMessageData,
    cadRequest: CADDocumentRequest
  ): Promise<EmailRenderResult> {
    // Implementation will be built step by step
  }
}

interface EmailRenderResult {
  content: string;
  attachments: DocumentAttachment[];
  renderData: any; // For debugging/testing
}

interface DocumentAttachment {
  filename: string;
  content: Buffer;
  contentType: string;
}
```

### Step 2: Use Existing Template Engine

We already have templating systems in node_modules (Nunjucks) and the existing core-agent template infrastructure. We can import and use the existing template engine from `TemplateManagerService` or similar existing systems.

If needed, we can add the `capitalize` filter to the existing template configuration:

**Enhancement to existing template system:**
```typescript
// Add to existing template engine configuration
env.addFilter('capitalize', (str: string) => {
  return str ? str.charAt(0).toUpperCase() + str.slice(1) : '';
});
```

**Target Template**: This unified renderer is specifically designed to power the single template at `apps/portal-api/src/core-agent/email-response-complete-template.njk`.

### Step 3: Implement Core Rendering Logic

```typescript
// In UnifiedTemplateRendererService

async renderEmailResponse(
  shipment: Shipment,
  sectionFlags: TemplateSectionFlags,
  messageData: TemplateMessageData,
  cadRequest: CADDocumentRequest
): Promise<EmailRenderResult> {
  
  // 1. Gather template data from clean-agent-context
  const [
    shipmentData,
    documentData,
    rnsData,
    importerData,
    validationData
  ] = await Promise.all([
    this.cleanAgentContextService.gatherShipmentData(shipment),
    this.cleanAgentContextService.gatherDocumentData(shipment),
    this.cleanAgentContextService.gatherRnsData(shipment),
    this.cleanAgentContextService.gatherImporterData(shipment),
    this.cleanAgentContextService.gatherValidationData(shipment)
  ]);

  // 2. Build placeholder variables (handlers will eventually do this)
  const placeholderData = this.buildPlaceholderData(shipment, {
    shipmentData,
    documentData, 
    validationData
  });

  // 3. Handle CAD document attachment
  const cadAttachment = await this.handleCADAttachment(shipment, cadRequest);

  // 4. Combine all template context
  const templateContext = {
    // Clean agent context data
    ...shipmentData,
    ...documentData,
    ...rnsData,
    ...importerData,
    ...validationData,
    
    // Section visibility flags
    ...sectionFlags,
    
    // Message content
    ...messageData,
    
    // Placeholder variables
    ...placeholderData
  };

  // 5. Render the single unified template
  const content = await this.templateEngine.render(
    'email-response-complete-template.njk',
    templateContext
  );

  return {
    content,
    attachments: cadAttachment ? [cadAttachment] : [],
    renderData: templateContext // For testing/debugging
  };
}

private buildPlaceholderData(shipment: Shipment, contextData: any): any {
  // Temporary implementation - handlers will eventually provide this
  return {
    CCN_PLACEHOLDER: shipment.cargoControlNumber || 'N/A',
    CONTAINER_PLACEHOLDER: this.formatContainers(shipment.containers || []),
    HBL_PLACEHOLDER: shipment.houseBladeNumber || 'N/A',
    HBL_STATUS_PLACEHOLDER: contextData.documentData?.HBL_MISSING ? 'Missing' : 'Received',
    AN_EMF_STATUS_PLACEHOLDER: contextData.documentData?.AN_EMF_MISSING ? 'Missing' : 'Received',  
    CI_PL_STATUS_PLACEHOLDER: contextData.documentData?.CI_PL_MISSING ? 'Missing' : 'Received',
    CUSTOMS_STATUS_LINE_PLACEHOLDER: this.formatCustomsStatus(shipment.customsStatus),
    MISSING_DOCUMENTS_PLACEHOLDER: this.formatMissingDocuments(contextData.documentData),
    MISSING_FIELDS_PLACEHOLDER: this.formatMissingFields(contextData.validationData),
    COMPLIANCE_ERRORS_PLACEHOLDER: this.formatComplianceErrors(contextData.validationData)
  };
}

private async handleCADAttachment(
  shipment: Shipment, 
  cadRequest: CADDocumentRequest
): Promise<DocumentAttachment | null> {
  if (!cadRequest.shouldGenerateCAD || !cadRequest.cadReadyStatus) {
    return null;
  }

  try {
    // For now, return mock CAD document - will integrate with real service later
    return {
      filename: `CAD_${shipment.cargoControlNumber || 'UNKNOWN'}.pdf`,
      content: Buffer.from('Mock CAD Document Content'),
      contentType: 'application/pdf'
    };
  } catch (error) {
    console.error('CAD generation failed:', error);
    return null;
  }
}
```

### Step 4: Add Helper Methods

```typescript
// Add these helper methods to UnifiedTemplateRendererService

private formatContainers(containers: any[]): string {
  if (!containers?.length) return 'N/A';
  return containers.map(c => c.containerNumber || c.number || 'Unknown').join(', ');
}

private formatCustomsStatus(status: string): string {
  if (!status) return 'Unknown';
  
  // Convert technical status to user-friendly display
  const statusMap = {
    'pending-commercial-invoice': 'Pending Commercial Invoice',
    'pending-confirmation': 'Pending Confirmation',
    'pending-arrival': 'Pending Arrival',
    'live': 'Live/In Progress',
    'entry-submitted': 'Entry Submitted',
    'entry-accepted': 'Entry Accepted',
    'exam': 'Selected for Examination',
    'released': 'Released'
  };
  
  return statusMap[status] || status.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

private formatMissingDocuments(documentData: any): string {
  const missing = [];
  if (documentData?.CI_PL_MISSING) missing.push('Commercial Invoice & Packing List');
  if (documentData?.HBL_MISSING) missing.push('House Bill of Lading');
  if (documentData?.AN_EMF_MISSING) missing.push('Arrival Notice/e-Manifest');
  
  return missing.length > 0 ? missing.join(', ') : 'None';
}

private formatMissingFields(validationData: any): string {
  const missing = [];
  if (validationData?.WEIGHT_MISSING) missing.push('Shipment Weight');
  if (validationData?.PORT_CODE_MISSING) missing.push('Port Code');
  if (validationData?.CCN_MISSING) missing.push('Cargo Control Number');
  
  return missing.length > 0 ? missing.join(', ') : 'None';
}

private formatComplianceErrors(validationData: any): string {
  // This will be populated from actual compliance validation data
  if (validationData?.COMPLIANCE_ERRORS?.length > 0) {
    return validationData.COMPLIANCE_ERRORS.join(', ');
  }
  return 'None';
}
```

### Step 5: Create Test Service

**File**: `apps/portal-api/src/core-agent/testing/template-renderer-test.service.ts`

```typescript
import { Injectable } from '@nestjs/common';
import { UnifiedTemplateRendererService } from '../services/unified-template-renderer.service';
import { emailResponseMessages } from '../constants/email-response-messages';

@Injectable()
export class TemplateRendererTestService {
  constructor(
    private readonly unifiedRenderer: UnifiedTemplateRendererService
  ) {}

  async testAllSectionsActive(shipment: Shipment): Promise<EmailRenderResult> {
    // Activate ALL possible sections to test complete template
    const sectionFlags: TemplateSectionFlags = {
      SHOW_RNS_PROOF: true,
      SHOW_PROCESSED_DOCUMENTS: true,
      SHOW_VALIDATION_ISSUES: true,
      SHOW_DETAILS: true,
      SHOW_COMPLIANCE_ERRORS: true
    };

    // Include ALL possible messages
    const messageData: TemplateMessageData = {
      ACKNOWLEDGE_DOCUMENTS_MESSAGE: emailResponseMessages.acknowledgeDocuments.default,
      ACKNOWLEDGE_MISSING_DOCUMENTS_MESSAGE: emailResponseMessages.acknowledgeMissingDocuments.default,
      DOCUMENTATION_COMING_MESSAGE: emailResponseMessages.documentationComing.default,
      PROCESS_DOCUMENT_MESSAGE: emailResponseMessages.processDocument.success,
      REQUEST_CAD_DOCUMENT_MESSAGE: this.getCADMessage(shipment.customsStatus),
      REQUEST_RNS_PROOF_MESSAGE: this.getRNSMessage(shipment.customsStatus),
      REQUEST_RUSH_PROCESSING_MESSAGE: this.getRushMessage(shipment.customsStatus),
      REQUEST_MANUAL_PROCESSING_MESSAGE: emailResponseMessages.requestManualProcessing.default,
      REQUEST_HOLD_SHIPMENT_MESSAGE: emailResponseMessages.requestHoldShipment.default,
      UPDATE_SHIPMENT_MESSAGE: emailResponseMessages.updateShipment.success,
      ETA_RESPONSE_MESSAGE: this.getETAMessage(shipment),
      TRANSACTION_NUMBER_RESPONSE_MESSAGE: this.getTransactionMessage(shipment),
      RELEASE_STATUS_RESPONSE_MESSAGE: this.getReleaseMessage(shipment),
      SHIPPING_STATUS_RESPONSE_MESSAGE: emailResponseMessages.shippingStatusResponse.default,
      SYSTEM_ERROR_MESSAGE: emailResponseMessages.systemError.default,
      CONTACT_SUPPORT_MESSAGE: emailResponseMessages.contactSupport.default,
      GET_SHIPMENT_STATUS_MESSAGE: this.getStatusMessage(shipment.customsStatus)
    };

    const cadRequest: CADDocumentRequest = {
      shouldGenerateCAD: true,
      cadReadyStatus: this.isCADReady(shipment.customsStatus)
    };

    return this.unifiedRenderer.renderEmailResponse(
      shipment,
      sectionFlags,
      messageData,
      cadRequest
    );
  }

  async testByIntent(shipment: Shipment, intent: string): Promise<EmailRenderResult> {
    // Test specific intent scenarios
    const sectionFlags = this.getSectionFlagsForIntent(intent, shipment);
    const messageData = this.getMessageDataForIntent(intent, shipment);
    const cadRequest = this.getCADRequestForIntent(intent, shipment);

    return this.unifiedRenderer.renderEmailResponse(
      shipment,
      sectionFlags,
      messageData,
      cadRequest
    );
  }

  private getCADMessage(customsStatus: string): string {
    return emailResponseMessages.requestCADDocument[customsStatus] || 
           emailResponseMessages.requestCADDocument.default;
  }

  private getRNSMessage(customsStatus: string): string {
    return emailResponseMessages.requestRNSProof[customsStatus] ||
           emailResponseMessages.requestRNSProof.default;
  }

  private getRushMessage(customsStatus: string): string {
    return emailResponseMessages.requestRushProcessing[customsStatus] ||
           emailResponseMessages.requestRushProcessing.default;
  }

  private getStatusMessage(customsStatus: string): string {
    return emailResponseMessages.getCustomsStatus[customsStatus] ||
           emailResponseMessages.getCustomsStatus.default;
  }

  // ... additional helper methods for testing different scenarios
}
```

### Step 6: Create Test Controller

**File**: `apps/portal-api/src/core-agent/controllers/template-test.controller.ts`

```typescript
import { Controller, Get, Param, Query } from '@nestjs/common';
import { TemplateRendererTestService } from '../testing/template-renderer-test.service';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Shipment } from '../../../libraries/nest-modules/src/entities/shipment.entity';

@Controller('template-test')
export class TemplateTestController {
  constructor(
    private readonly testService: TemplateRendererTestService,
    @InjectRepository(Shipment)
    private readonly shipmentRepository: Repository<Shipment>
  ) {}

  @Get('all-sections/:shipmentId')
  async testAllSections(@Param('shipmentId') shipmentId: string) {
    const shipment = await this.shipmentRepository.findOne({
      where: { id: parseInt(shipmentId) },
      relations: ['containers', 'documents', 'organization']
    });

    if (!shipment) {
      return { error: 'Shipment not found' };
    }

    try {
      const result = await this.testService.testAllSectionsActive(shipment);
      
      return {
        success: true,
        html: result.content,
        attachments: result.attachments.map(a => ({
          filename: a.filename,
          contentType: a.contentType,
          size: a.content.length
        })),
        renderData: result.renderData
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        stack: error.stack
      };
    }
  }

  @Get('by-intent/:shipmentId')
  async testByIntent(
    @Param('shipmentId') shipmentId: string,
    @Query('intent') intent: string
  ) {
    const shipment = await this.shipmentRepository.findOne({
      where: { id: parseInt(shipmentId) },
      relations: ['containers', 'documents', 'organization']
    });

    if (!shipment) {
      return { error: 'Shipment not found' };
    }

    try {
      const result = await this.testService.testByIntent(shipment, intent);
      
      return {
        success: true,
        intent,
        customsStatus: shipment.customsStatus,
        html: result.content,
        attachments: result.attachments.map(a => ({
          filename: a.filename,
          contentType: a.contentType,
          size: a.content.length
        })),
        renderData: result.renderData
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        stack: error.stack
      };
    }
  }

  @Get('shipments')
  async listShipments(@Query('limit') limit = '10') {
    const shipments = await this.shipmentRepository.find({
      take: parseInt(limit),
      order: { id: 'DESC' },
      select: ['id', 'cargoControlNumber', 'customsStatus', 'createdAt']
    });

    return shipments;
  }
}
```

### Step 7: Module Integration

**File**: Update `apps/portal-api/src/core-agent/core-agent.module.ts`

```typescript
import { Module } from '@nestjs/common';
import { CleanAgentContextModule } from '../clean-agent-context/clean-agent-context.module';
import { UnifiedTemplateRendererService } from './services/unified-template-renderer.service';
import { TemplateRendererTestService } from './testing/template-renderer-test.service';
import { TemplateTestController } from './controllers/template-test.controller';
import { TemplateEngineProvider } from './config/template-engine.config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Shipment } from '../../libraries/nest-modules/src/entities/shipment.entity';

@Module({
  imports: [
    CleanAgentContextModule,
    TypeOrmModule.forFeature([Shipment])
  ],
  providers: [
    TemplateEngineProvider,
    UnifiedTemplateRendererService,
    TemplateRendererTestService
  ],
  controllers: [
    TemplateTestController
  ],
  exports: [
    UnifiedTemplateRendererService
  ]
})
export class CoreAgentModule {}
```

## Testing Strategy

### Test Scenarios

1. **All Sections Active**: Test with all possible template sections enabled
2. **By Intent Type**: Test each intent type with appropriate sections  
3. **By Customs Status**: Test each customs status with relevant content
4. **CAD Attachment**: Test CAD generation for eligible shipments
5. **Missing Data**: Test graceful handling of missing shipment data

### Test Approaches

**Primary Testing Method**: Use the E2E testing infrastructure from `apps/portal-api/src/core-agent/testing/run-e2e-with-logs.sh` and its associated JavaScript files for comprehensive testing.

**Test Controller**: Keep the test controller for quick manual testing and debugging during development.

### Test Endpoints

```bash
# List available shipments for testing
GET /template-test/shipments

# Test all sections active for a shipment
GET /template-test/all-sections/123

# Test specific intent scenarios
GET /template-test/by-intent/123?intent=acknowledge-documents
GET /template-test/by-intent/123?intent=request-cad-document
GET /template-test/by-intent/123?intent=get-shipment-status
```

### E2E Testing Integration

Use the existing E2E testing methodology from `run-e2e-with-logs.sh` to validate the unified template renderer with real database scenarios.

### Validation Checklist

- [ ] Template renders without errors for all shipments
- [ ] All sections display when flags are enabled
- [ ] All message variables are populated correctly
- [ ] Placeholder variables show appropriate data or fallbacks
- [ ] CAD attachments generate for eligible statuses
- [ ] HTML output is well-formed and readable
- [ ] Template handles missing data gracefully
- [ ] Performance is acceptable (< 500ms per render)

## Comprehensive Testing Strategy

### Unit Testing Framework

**Template Unit Tests:**
```typescript
describe('UnifiedTemplateRendererService', () => {
  let service: UnifiedTemplateRendererService;
  let mockCleanAgentContext: jest.Mocked<CleanAgentContextService>;
  let mockTemplateManager: jest.Mocked<TemplateManagerService>;
  
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UnifiedTemplateRendererService,
        { provide: CleanAgentContextService, useValue: mockCleanAgentContext },
        { provide: TemplateManagerService, useValue: mockTemplateManager },
        { provide: 'TEMPLATE_ENGINE', useValue: mockNunjucksEngine }
      ]
    }).compile();
    
    service = module.get<UnifiedTemplateRendererService>(UnifiedTemplateRendererService);
  });
  
  describe('renderEmailResponse', () => {
    it('should render all sections when flags are enabled', async () => {
      const mockShipment = createMockShipment({
        customsStatus: 'pending-commercial-invoice',
        cargoControlNumber: 'CCN123456789'
      });
      
      const sectionFlags: TemplateSectionFlags = {
        SHOW_RNS_PROOF: true,
        SHOW_PROCESSED_DOCUMENTS: true,
        SHOW_VALIDATION_ISSUES: true,
        SHOW_DETAILS: true,
        SHOW_COMPLIANCE_ERRORS: true
      };
      
      mockCleanAgentContext.gatherShipmentData.mockResolvedValue({
        CUSTOMS_STATUS: 'pending-commercial-invoice',
        CCN: 'CCN123456789'
      });
      
      const result = await service.renderEmailResponse(
        mockShipment, sectionFlags, {}, { shouldGenerateCAD: false, cadReadyStatus: false }
      );
      
      expect(result.content).toContain('Hello,');
      expect(result.content).toContain('CCN123456789');
      expect(result.metadata.fragmentsUsed).toHaveLength(6); // All sections + greeting + footer
    });
    
    it('should handle missing data gracefully', async () => {
      const mockShipment = createMockShipment({ customsStatus: null });
      
      mockCleanAgentContext.gatherShipmentData.mockResolvedValue({});
      
      const result = await service.renderEmailResponse(
        mockShipment, 
        { SHOW_DETAILS: true }, 
        {}, 
        { shouldGenerateCAD: false, cadReadyStatus: false }
      );
      
      expect(result.content).toContain('Hello,');
      expect(result.content).not.toContain('undefined');
      expect(result.content).not.toContain('null');
    });
    
    it('should generate CAD attachment when requested', async () => {
      const mockShipment = createMockShipment({
        customsStatus: 'entry-submitted',
        cargoControlNumber: 'CCN123456789'
      });
      
      const result = await service.renderEmailResponse(
        mockShipment,
        {},
        {},
        { shouldGenerateCAD: true, cadReadyStatus: true }
      );
      
      expect(result.attachments).toHaveLength(1);
      expect(result.attachments[0].filename).toContain('CAD_CCN123456789');
      expect(result.attachments[0].contentType).toBe('application/pdf');
    });
  });
  
  describe('Fragment System Integration', () => {
    it('should maintain fragment priority ordering', async () => {
      const fragments = service.buildFragmentList(
        { SHOW_DETAILS: true, SHOW_RNS_PROOF: true },
        {}
      );
      
      const priorities = fragments.map(f => f.priority).filter(p => p !== undefined);
      const sortedPriorities = [...priorities].sort((a, b) => a - b);
      
      expect(priorities).toEqual(sortedPriorities);
    });
    
    it('should deduplicate fragments correctly', async () => {
      const fragments = [
        { template: 'fragment-a', priority: 1 },
        { template: 'fragment-b', priority: 2 },
        { template: 'fragment-a', priority: 1 } // Duplicate
      ];
      
      const deduplicated = service.deduplicateFragments(fragments);
      
      expect(deduplicated).toHaveLength(2);
      expect(deduplicated.map(f => f.template)).toEqual(['fragment-a', 'fragment-b']);
    });
  });
});
```

### Integration Testing Infrastructure

**E2E Template Testing:**
```typescript
describe('Template System E2E Tests', () => {
  let app: INestApplication;
  let shipmentRepository: Repository<Shipment>;
  
  beforeEach(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [CoreAgentModule, TypeOrmModule.forRoot(testDbConfig)]
    }).compile();
    
    app = moduleFixture.createNestApplication();
    shipmentRepository = moduleFixture.get(getRepositoryToken(Shipment));
    await app.init();
  });
  
  describe('Real Database Integration', () => {
    it('should render templates for all customs statuses', async () => {
      const customsStatuses = [
        'pending-commercial-invoice',
        'pending-confirmation', 
        'live',
        'entry-submitted',
        'released'
      ];
      
      const results = await Promise.all(
        customsStatuses.map(async status => {
          const shipment = await shipmentRepository.findOne({ 
            where: { customsStatus: status },
            relations: ['containers', 'documents']
          });
          
          if (!shipment) {
            throw new Error(`No shipment found with status: ${status}`);
          }
          
          const response = await request(app.getHttpServer())
            .get(`/template-test/all-sections/${shipment.id}`)
            .expect(200);
          
          return { status, result: response.body };
        })
      );
      
      results.forEach(({ status, result }) => {
        expect(result.success).toBe(true);
        expect(result.html).toContain('Hello,');
        expect(result.html.length).toBeGreaterThan(100);
        console.log(`✅ ${status} - Rendered ${result.html.length} chars`);
      });
    });
    
    it('should handle performance requirements', async () => {
      const shipment = await shipmentRepository.findOne({
        relations: ['containers', 'documents', 'organization']
      });
      
      const startTime = Date.now();
      
      const response = await request(app.getHttpServer())
        .get(`/template-test/all-sections/${shipment.id}`)
        .expect(200);
      
      const duration = Date.now() - startTime;
      
      expect(response.body.success).toBe(true);
      expect(duration).toBeLessThan(500); // Must render in under 500ms
      
      console.log(`⚡ Template rendered in ${duration}ms`);
    });
  });
});
```

### Load Testing Framework

**Performance Benchmarking:**
```typescript
describe('Template Performance Tests', () => {
  let service: UnifiedTemplateRendererService;
  
  it('should handle concurrent renders efficiently', async () => {
    const concurrentRenders = 10;
    const mockShipment = createMockShipment();
    
    const renderPromises = Array.from({ length: concurrentRenders }, () => 
      service.renderEmailResponse(
        mockShipment,
        { SHOW_DETAILS: true },
        {},
        { shouldGenerateCAD: false, cadReadyStatus: false }
      )
    );
    
    const startTime = Date.now();
    const results = await Promise.all(renderPromises);
    const totalDuration = Date.now() - startTime;
    
    expect(results).toHaveLength(concurrentRenders);
    expect(results.every(r => r.content.length > 0)).toBe(true);
    expect(totalDuration).toBeLessThan(2000); // All renders in under 2 seconds
    
    console.log(`🚀 ${concurrentRenders} concurrent renders in ${totalDuration}ms`);
  });
  
  it('should maintain memory efficiency', async () => {
    const initialMemory = process.memoryUsage().heapUsed;
    
    // Render 100 templates
    for (let i = 0; i < 100; i++) {
      await service.renderEmailResponse(
        createMockShipment({ id: i }),
        { SHOW_DETAILS: true },
        {},
        { shouldGenerateCAD: false, cadReadyStatus: false }
      );
    }
    
    // Force garbage collection
    if (global.gc) global.gc();
    
    const finalMemory = process.memoryUsage().heapUsed;
    const memoryIncrease = finalMemory - initialMemory;
    const memoryIncreaseMB = memoryIncrease / 1024 / 1024;
    
    expect(memoryIncreaseMB).toBeLessThan(50); // Less than 50MB increase
    
    console.log(`💾 Memory increase: ${memoryIncreaseMB.toFixed(2)}MB`);
  });
});
```

### Visual Regression Testing

**HTML Output Validation:**
```typescript
describe('Visual Regression Tests', () => {
  it('should generate consistent HTML structure', async () => {
    const mockShipment = createMockShipment({
      cargoControlNumber: 'CCN123456789',
      customsStatus: 'pending-commercial-invoice'
    });
    
    const result = await service.renderEmailResponse(
      mockShipment, 
      { SHOW_DETAILS: true, SHOW_PROCESSED_DOCUMENTS: true },
      {
        ACKNOWLEDGE_DOCUMENTS_MESSAGE: 'Test acknowledgment message'
      },
      { shouldGenerateCAD: false, cadReadyStatus: false }
    );
    
    // Validate HTML structure
    expect(result.content).toMatch(/<p>Hello,<\/p>/);
    expect(result.content).toContain('CCN123456789');
    expect(result.content).toContain('Test acknowledgment message');
    
    // Check for well-formed HTML
    const $ = cheerio.load(result.content);
    expect($('p').length).toBeGreaterThan(0);
    expect($('*').length).toBeGreaterThan(0); // Has HTML elements
    
    // Validate no broken placeholders
    expect(result.content).not.toMatch(/\{\{.*\}\}/); // No unrendered variables
    expect(result.content).not.toContain('undefined');
    expect(result.content).not.toContain('null');
  });
  
  it('should generate email-safe HTML', async () => {
    const result = await service.renderEmailResponse(
      createMockShipment(),
      { SHOW_DETAILS: true },
      {},
      { shouldGenerateCAD: false, cadReadyStatus: false }
    );
    
    // Check for email client compatibility
    expect(result.content).not.toContain('<script>'); // No scripts
    expect(result.content).not.toContain('javascript:'); // No JS protocols
    expect(result.content).not.toMatch(/<style[^>]*>/); // Inline styles only
    
    // Validate proper escaping
    const $ = cheerio.load(result.content);
    $('*').each((i, elem) => {
      const text = $(elem).text();
      expect(text).not.toContain('<'); // Properly escaped
      expect(text).not.toContain('>'); // Properly escaped
    });
  });
});
```

### Mock Data Generators

**Comprehensive Test Data:**
```typescript
class TemplateTestDataGenerator {
  static createMockShipment(overrides: Partial<Shipment> = {}): Shipment {
    return {
      id: 12345,
      cargoControlNumber: 'CCN123456789',
      houseBladeNumber: 'HBL987654321',
      customsStatus: 'pending-commercial-invoice',
      containers: [
        { containerNumber: 'CONT123456789', type: '40HC' },
        { containerNumber: 'CONT987654321', type: '20GP' }
      ],
      organization: {
        id: 3,
        name: 'Test Organization',
        email: '<EMAIL>'
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides
    } as Shipment;
  }
  
  static createMockSectionFlags(overrides: Partial<TemplateSectionFlags> = {}): TemplateSectionFlags {
    return {
      SHOW_RNS_PROOF: false,
      SHOW_PROCESSED_DOCUMENTS: false,
      SHOW_VALIDATION_ISSUES: false,
      SHOW_DETAILS: false,
      SHOW_COMPLIANCE_ERRORS: false,
      ...overrides
    };
  }
  
  static createMockMessageData(intent: string): TemplateMessageData {
    const baseMessages = {
      'acknowledge-documents': {
        ACKNOWLEDGE_DOCUMENTS_MESSAGE: 'Thank you for uploading the requested documents.'
      },
      'request-cad-document': {
        REQUEST_CAD_DOCUMENT_MESSAGE: 'Please find the CAD document attached.'
      },
      'get-shipment-status': {
        GET_SHIPMENT_STATUS_MESSAGE: 'Here is the current status of your shipment.'
      }
    };
    
    return baseMessages[intent] || {};
  }
  
  static createMockContextData(): TemplateContextData {
    return {
      shipmentData: {
        CUSTOMS_STATUS: 'pending-commercial-invoice',
        CCN: 'CCN123456789',
        HBL: 'HBL987654321',
        CONTAINERS: 'CONT123456789, CONT987654321'
      },
      documentData: {
        CI_PL_MISSING: true,
        HBL_MISSING: false,
        AN_EMF_MISSING: false
      },
      validationData: {
        COMPLIANCE_ERRORS: [],
        MISSING_FIELDS: ['weight', 'portCode']
      },
      rnsData: {
        RNS_REQUIRED: false,
        RNS_STATUS: 'not-applicable'
      },
      importerData: {
        IMPORTER_NAME: 'Test Importer Inc.',
        IMPORTER_CONTACT: '<EMAIL>'
      }
    };
  }
}
```

## Timeline

### Day 1: Enhanced Foundation
- ✅ Create UnifiedTemplateRendererService with advanced architecture
- ✅ Implement template engine configuration with caching
- ✅ Build core rendering logic with fragment system integration
- ✅ Add performance monitoring and error handling

### Day 2: Comprehensive Testing Infrastructure  
- ✅ Create test service with all intent scenarios
- ✅ Implement unit tests with mocking framework
- ✅ Set up integration tests with real database
- ✅ Add performance and load testing
- ✅ Build visual regression testing

### Day 3: Validation & Production Readiness
- ✅ Test with real database shipments across all customs statuses
- ✅ Validate performance requirements (< 500ms per render)
- ✅ Fix any template rendering issues and edge cases
- ✅ Document all sections and message types functionality
- ✅ Prepare production deployment configuration

### Success Criteria

**Functional Requirements:**
- [ ] ✅ Template renders without errors for all shipment types
- [ ] ✅ All sections display correctly when flags are enabled
- [ ] ✅ Message variables populate with appropriate fallbacks
- [ ] ✅ CAD attachments generate for eligible customs statuses
- [ ] ✅ HTML output is well-formed and email-client compatible
- [ ] ✅ Graceful handling of missing or invalid data

**Performance Requirements:**
- [ ] ✅ Individual renders complete in < 500ms
- [ ] ✅ Concurrent renders handle 10+ simultaneous requests
- [ ] ✅ Memory usage remains stable under load
- [ ] ✅ Fragment caching improves performance measurably

**Quality Requirements:**
- [ ] ✅ 95%+ unit test coverage for core rendering logic
- [ ] ✅ Integration tests pass for all customs statuses
- [ ] ✅ No security vulnerabilities (XSS, injection)
- [ ] ✅ Comprehensive error handling and logging
- [ ] ✅ Production-ready monitoring and metrics

This comprehensive approach validates the unified template concept works completely with real data, proper error handling, and production-grade performance before proceeding with handler migration.

## Deployment and Monitoring Strategy

### Production Configuration

**Environment Variables:**
```bash
# Template system configuration
TEMPLATE_ENGINE_CACHE_ENABLED=true
TEMPLATE_ENGINE_CACHE_TTL=3600
TEMPLATE_RENDER_TIMEOUT=5000
TEMPLATE_MAX_CONCURRENT_RENDERS=50

# Performance monitoring
TEMPLATE_PERFORMANCE_MONITORING=true
TEMPLATE_SLOW_RENDER_THRESHOLD=500
TEMPLATE_ERROR_REPORTING=true

# Security settings
TEMPLATE_SANITIZATION_STRICT=true
TEMPLATE_ALLOW_UNSAFE_HTML=false
TEMPLATE_XSS_PROTECTION=true
```

**Module Registration:**
```typescript
// In portal-api app.module.ts
@Module({
  imports: [
    CoreAgentModule.forRoot({
      templateEngine: {
        cacheEnabled: process.env.TEMPLATE_ENGINE_CACHE_ENABLED === 'true',
        cacheTTL: parseInt(process.env.TEMPLATE_ENGINE_CACHE_TTL || '3600'),
        renderTimeout: parseInt(process.env.TEMPLATE_RENDER_TIMEOUT || '5000'),
        maxConcurrentRenders: parseInt(process.env.TEMPLATE_MAX_CONCURRENT_RENDERS || '50')
      },
      monitoring: {
        enabled: process.env.TEMPLATE_PERFORMANCE_MONITORING === 'true',
        slowThreshold: parseInt(process.env.TEMPLATE_SLOW_RENDER_THRESHOLD || '500'),
        errorReporting: process.env.TEMPLATE_ERROR_REPORTING === 'true'
      },
      security: {
        strictSanitization: process.env.TEMPLATE_SANITIZATION_STRICT === 'true',
        allowUnsafeHTML: process.env.TEMPLATE_ALLOW_UNSAFE_HTML === 'true',
        xssProtection: process.env.TEMPLATE_XSS_PROTECTION === 'true'
      }
    })
  ]
})
export class AppModule {}
```

### Monitoring and Observability

**Performance Metrics:**
```typescript
export class TemplateMetricsService {
  private readonly prometheus = require('prom-client');
  
  private readonly renderDurationHistogram = new this.prometheus.Histogram({
    name: 'template_render_duration_seconds',
    help: 'Duration of template rendering operations',
    labelNames: ['template_type', 'customs_status', 'fragments_count'],
    buckets: [0.1, 0.5, 1.0, 2.0, 5.0]
  });
  
  private readonly renderErrorCounter = new this.prometheus.Counter({
    name: 'template_render_errors_total',
    help: 'Total number of template rendering errors',
    labelNames: ['template_type', 'error_type', 'customs_status']
  });
  
  private readonly cacheHitRatio = new this.prometheus.Gauge({
    name: 'template_cache_hit_ratio',
    help: 'Template cache hit ratio',
    labelNames: ['cache_type']
  });
  
  recordRenderDuration(
    templateType: string,
    customsStatus: string,
    fragmentsCount: number,
    duration: number
  ): void {
    this.renderDurationHistogram
      .labels(templateType, customsStatus, fragmentsCount.toString())
      .observe(duration / 1000);
  }
  
  recordRenderError(
    templateType: string,
    errorType: string,
    customsStatus: string
  ): void {
    this.renderErrorCounter
      .labels(templateType, errorType, customsStatus)
      .inc();
  }
  
  updateCacheHitRatio(cacheType: string, ratio: number): void {
    this.cacheHitRatio.labels(cacheType).set(ratio);
  }
}
```

**Health Checks:**
```typescript
@Injectable()
export class TemplateHealthIndicator extends HealthIndicator {
  constructor(
    private readonly templateRenderer: UnifiedTemplateRendererService,
    private readonly templateMetrics: TemplateMetricsService
  ) {
    super();
  }
  
  @HealthCheck('template-system')
  async isHealthy(): Promise<HealthIndicatorResult> {
    try {
      // Test template rendering with minimal data
      const testShipment = this.createTestShipment();
      const startTime = Date.now();
      
      await this.templateRenderer.renderEmailResponse(
        testShipment,
        { SHOW_DETAILS: true },
        {},
        { shouldGenerateCAD: false, cadReadyStatus: false }
      );
      
      const duration = Date.now() - startTime;
      
      if (duration > 1000) {
        throw new Error(`Template rendering too slow: ${duration}ms`);
      }
      
      return this.getStatus('template-system', true, {
        renderTime: `${duration}ms`,
        status: 'operational'
      });
      
    } catch (error) {
      return this.getStatus('template-system', false, {
        error: error.message,
        status: 'failing'
      });
    }
  }
}
```

### Error Recovery and Circuit Breaker

**Circuit Breaker Implementation:**
```typescript
export class TemplateCircuitBreaker {
  private readonly circuitStates = new Map<string, CircuitState>();
  private readonly failureThreshold = 5;
  private readonly recoveryTimeout = 30000; // 30 seconds
  
  async executeWithCircuitBreaker<T>(
    templateId: string,
    operation: () => Promise<T>
  ): Promise<T> {
    const state = this.getCircuitState(templateId);
    
    if (state.state === 'OPEN') {
      if (Date.now() - state.lastFailureTime < this.recoveryTimeout) {
        throw new Error(`Circuit breaker OPEN for template: ${templateId}`);
      }
      // Try to recover
      state.state = 'HALF_OPEN';
    }
    
    try {
      const result = await operation();
      
      if (state.state === 'HALF_OPEN') {
        state.state = 'CLOSED';
        state.failureCount = 0;
      }
      
      return result;
      
    } catch (error) {
      state.failureCount++;
      state.lastFailureTime = Date.now();
      
      if (state.failureCount >= this.failureThreshold) {
        state.state = 'OPEN';
      }
      
      throw error;
    }
  }
}
```

## Security and Compliance

### Input Validation and Sanitization

**Template Context Validation:**
```typescript
export class TemplateSecurityValidator {
  private readonly allowedTemplateIds = new Set([
    'email-response-complete-template',
    'core-agent/fragments/greeting',
    'core-agent/fragments/details/shipment-identifiers',
    'core-agent/fragments/status-messages/pending-commercial-invoice'
    // ... other approved templates
  ]);
  
  validateTemplateRequest(
    templateId: string,
    sectionFlags: TemplateSectionFlags,
    messageData: TemplateMessageData
  ): ValidationResult {
    const errors: string[] = [];
    
    // Validate template ID
    if (!this.allowedTemplateIds.has(templateId)) {
      errors.push(`Unauthorized template ID: ${templateId}`);
    }
    
    // Sanitize message data
    for (const [key, value] of Object.entries(messageData)) {
      if (typeof value === 'string') {
        messageData[key] = this.sanitizeString(value);
      }
    }
    
    // Validate section flags
    this.validateSectionFlags(sectionFlags, errors);
    
    return {
      isValid: errors.length === 0,
      errors,
      sanitizedData: messageData
    };
  }
  
  private sanitizeString(input: string): string {
    return input
      .replace(/<script[^>]*>.*?<\/script>/gi, '') // Remove scripts
      .replace(/javascript:/gi, '') // Remove JS protocols
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .replace(/expression\s*\(/gi, '') // Remove CSS expressions
      .trim();
  }
}
```

### Audit Logging

**Template Rendering Audit:**
```typescript
export class TemplateAuditLogger {
  constructor(private readonly logger: Logger) {}
  
  logTemplateRender(auditEvent: TemplateRenderAuditEvent): void {
    this.logger.log({
      event: 'template_render',
      timestamp: new Date().toISOString(),
      shipmentId: auditEvent.shipmentId,
      templateType: auditEvent.templateType,
      userEmail: auditEvent.userEmail,
      organizationId: auditEvent.organizationId,
      sectionsRendered: auditEvent.sectionsRendered,
      renderDuration: auditEvent.renderDuration,
      success: auditEvent.success,
      errorMessage: auditEvent.errorMessage,
      ipAddress: auditEvent.ipAddress,
      userAgent: auditEvent.userAgent
    });
  }
  
  logSecurityViolation(violation: SecurityViolationEvent): void {
    this.logger.error({
      event: 'template_security_violation',
      timestamp: new Date().toISOString(),
      violationType: violation.type,
      details: violation.details,
      userEmail: violation.userEmail,
      ipAddress: violation.ipAddress,
      blocked: violation.blocked
    });
  }
}
```

## Migration Strategy from Current System

### Phase 1: Parallel Testing (Week 1-2)
1. **Deploy UnifiedTemplateRenderer alongside existing system**
2. **Shadow mode testing** - render both old and new templates, compare results
3. **Performance baseline establishment** - measure current system performance
4. **Feature parity validation** - ensure all current functionality works

### Phase 2: Gradual Rollout (Week 3-4)
1. **Low-traffic organizations first** (< 100 emails/day)
2. **A/B testing** - 10% traffic to new system, monitor metrics
3. **Error rate monitoring** - rollback if error rate > 0.1%
4. **Performance comparison** - ensure new system meets SLA

### Phase 3: Full Migration (Week 5-6)
1. **Increase traffic percentage** - 25%, 50%, 75%, 100%
2. **Intent handler migration** - update handlers to use new service
3. **Legacy system deprecation** - remove old template code
4. **Documentation updates** - update all technical documentation

### Rollback Plan
```typescript
// Feature flag-based rollback capability
export class TemplateSystemController {
  constructor(
    private readonly unifiedRenderer: UnifiedTemplateRendererService,
    private readonly legacyRenderer: ShipmentResponseService,
    private readonly featureFlags: FeatureFlagService
  ) {}
  
  async renderEmail(request: EmailRenderRequest): Promise<EmailRenderResult> {
    const useUnifiedRenderer = await this.featureFlags.isEnabled(
      'unified-template-renderer',
      request.organizationId
    );
    
    if (useUnifiedRenderer) {
      try {
        return await this.unifiedRenderer.renderEmailResponse(
          request.shipment,
          request.sectionFlags,
          request.messageData,
          request.cadRequest
        );
      } catch (error) {
        // Automatic fallback on error
        this.logger.error('Unified renderer failed, falling back to legacy', error);
        return await this.legacyRenderer.renderResponse(request);
      }
    } else {
      return await this.legacyRenderer.renderResponse(request);
    }
  }
}
```

## Success Metrics and KPIs

### Performance Metrics
- **Template Render Time**: < 500ms (95th percentile)
- **Concurrent Render Capacity**: 50+ simultaneous renders
- **Memory Usage**: < 100MB per worker process
- **Cache Hit Rate**: > 80% for frequently used templates

### Quality Metrics
- **Error Rate**: < 0.1% of all template renders
- **Availability**: > 99.9% uptime for template service
- **Data Accuracy**: 100% of shipment data correctly rendered
- **Security Compliance**: 0 XSS vulnerabilities, 0 data leaks

### Business Metrics
- **Email Response Time**: 50% reduction in email processing time
- **Template Maintenance**: 75% reduction in template update time
- **Developer Productivity**: 60% faster feature development
- **System Reliability**: 90% reduction in template-related incidents

### Monitoring Dashboard

**Key Metrics to Track:**
```typescript
interface TemplateSystemDashboard {
  realTimeMetrics: {
    activeRenders: number;
    avgRenderTime: number;
    errorRate: number;
    cacheHitRate: number;
  };
  
  dailyMetrics: {
    totalRenders: number;
    uniqueTemplates: number;
    errorCount: number;
    performanceP95: number;
  };
  
  systemHealth: {
    circuitBreakerStatus: Map<string, 'OPEN' | 'CLOSED' | 'HALF_OPEN'>;
    memoryUsage: number;
    cpuUsage: number;
    diskUsage: number;
  };
}
```

This comprehensive build and test plan provides a production-ready foundation for implementing the UnifiedTemplateRenderer with proper testing, monitoring, security, and migration strategies.

## Advanced Implementation Strategy

### Architecture Integration Points

Based on comprehensive analysis of the existing template system, the UnifiedTemplateRenderer must integrate with:

**Current Template Infrastructure:**
- **50+ Nunjucks templates** in fragment-based architecture
- **TemplateManagerService** for basic template rendering
- **ShipmentResponseService** for fragment composition and deduplication
- **Fragment priority system** with predefined template ordering
- **Security sanitization** with HTML escaping and context cleaning

**Core-Agent System Integration:**
- **HandleRequestMessageProcessor** - main email processing pipeline
- **11 Intent Handlers** - provide structured data for templates
- **BullMQ integration** - async processing support
- **Event system** - template rendering monitoring
- **LLM Services** - dynamic content generation

### Enhanced Service Architecture

```typescript
export class UnifiedTemplateRendererService {
  constructor(
    private readonly cleanAgentContextService: CleanAgentContextService,
    private readonly templateManagerService: TemplateManagerService,
    private readonly shipmentResponseService: ShipmentResponseService,
    private readonly llmService: LLMService,
    private readonly eventEmitter: EventEmitter2,
    @Inject('TEMPLATE_ENGINE') private readonly templateEngine: any
  ) {}

  async renderEmailResponse(
    shipment: Shipment,
    sectionFlags: TemplateSectionFlags,
    messageData: TemplateMessageData,
    cadRequest: CADDocumentRequest,
    renderOptions: RenderOptions = {}
  ): Promise<EmailRenderResult> {
    
    const renderTimer = this.startRenderTimer(shipment.id);
    
    try {
      // 1. Validate inputs and create render context
      const renderContext = await this.createRenderContext(
        shipment, sectionFlags, messageData, cadRequest, renderOptions
      );
      
      // 2. Gather all required data in parallel
      const contextData = await this.gatherTemplateData(shipment, renderContext);
      
      // 3. Generate dynamic content via LLM if requested
      const dynamicContent = await this.generateDynamicContent(
        shipment, contextData, renderOptions
      );
      
      // 4. Build unified template context
      const templateContext = this.buildUnifiedContext(
        shipment, contextData, dynamicContent, sectionFlags, messageData
      );
      
      // 5. Fragment-based rendering with priority system
      const fragments = this.buildFragmentList(sectionFlags, renderOptions);
      const renderedFragments = await this.renderFragments(fragments, templateContext);
      
      // 6. Assemble final email content
      const content = this.assembleEmailContent(renderedFragments, templateContext);
      
      // 7. Generate attachments
      const attachments = await this.generateAttachments(shipment, cadRequest, contextData);
      
      // 8. Emit success event for monitoring
      this.eventEmitter.emit('template.render.success', {
        shipmentId: shipment.id,
        templateType: 'unified-email-response',
        duration: renderTimer.end(),
        fragmentCount: fragments.length,
        hasAttachments: attachments.length > 0
      });
      
      return {
        content,
        attachments,
        renderData: this.sanitizeRenderData(templateContext),
        metadata: {
          fragmentsUsed: fragments.map(f => f.template),
          renderDuration: renderTimer.getDuration(),
          dynamicContentGenerated: !!dynamicContent
        }
      };
      
    } catch (error) {
      this.handleRenderError(error, shipment, renderTimer);
      throw new TemplateRenderError(
        `Failed to render email response for shipment ${shipment.id}`,
        error,
        { shipmentId: shipment.id, sectionFlags }
      );
    }
  }

  private async gatherTemplateData(
    shipment: Shipment, 
    renderContext: RenderContext
  ): Promise<TemplateContextData> {
    // Parallel data gathering for optimal performance
    const [shipmentData, documentData, rnsData, importerData, validationData] = 
      await Promise.all([
        this.cleanAgentContextService.gatherShipmentData(shipment),
        this.cleanAgentContextService.gatherDocumentData(shipment),
        this.cleanAgentContextService.gatherRnsData(shipment),
        this.cleanAgentContextService.gatherImporterData(shipment),
        this.cleanAgentContextService.gatherValidationData(shipment)
      ]);
    
    return {
      shipmentData,
      documentData,
      rnsData,
      importerData,
      validationData,
      complianceData: this.buildComplianceData(validationData),
      statusData: this.buildStatusData(shipment, shipmentData)
    };
  }

  private async generateDynamicContent(
    shipment: Shipment,
    contextData: TemplateContextData,
    options: RenderOptions
  ): Promise<DynamicContent | null> {
    if (!options.includeDynamicContent) {
      return null;
    }
    
    try {
      const llmPrompt = this.buildLLMPrompt(shipment, contextData);
      const response = await this.llmService.generateResponse(llmPrompt, {
        model: 'deepseek-chat',
        maxTokens: 500,
        temperature: 0.1
      });
      
      return {
        explanation: response.explanation,
        recommendations: response.recommendations,
        nextSteps: response.nextSteps
      };
    } catch (error) {
      console.warn('Dynamic content generation failed:', error);
      return null;
    }
  }

  private buildFragmentList(
    sectionFlags: TemplateSectionFlags,
    options: RenderOptions
  ): ResponseFragment[] {
    const fragments: ResponseFragment[] = [];
    
    // Core message fragments (always included)
    fragments.push({
      template: 'core-agent/fragments/greeting',
      priority: 1
    });
    
    // Conditional sections based on flags
    if (sectionFlags.SHOW_PROCESSED_DOCUMENTS) {
      fragments.push({
        template: 'core-agent/fragments/document-status',
        priority: 2
      });
    }
    
    if (sectionFlags.SHOW_VALIDATION_ISSUES) {
      fragments.push({
        template: 'core-agent/fragments/validation-issues',
        priority: 3
      });
    }
    
    if (sectionFlags.SHOW_DETAILS) {
      fragments.push({
        template: 'core-agent/fragments/details/shipment-identifiers',
        priority: 4
      });
    }
    
    if (sectionFlags.SHOW_COMPLIANCE_ERRORS) {
      fragments.push({
        template: 'core-agent/fragments/compliance-errors',
        priority: 5
      });
    }
    
    if (sectionFlags.SHOW_RNS_PROOF) {
      fragments.push({
        template: 'core-agent/fragments/rns-proof-request',
        priority: 6
      });
    }
    
    // Footer fragment (always included)
    fragments.push({
      template: 'core-agent/fragments/footer',
      priority: 10
    });
    
    return fragments.sort((a, b) => (a.priority || 0) - (b.priority || 0));
  }

  private async renderFragments(
    fragments: ResponseFragment[],
    templateContext: any
  ): Promise<RenderedFragment[]> {
    const renderedFragments: RenderedFragment[] = [];
    
    for (const fragment of fragments) {
      try {
        const rendered = await this.templateManagerService.renderTemplate(
          fragment.template + '.njk',
          { ...templateContext, ...fragment.fragmentContext }
        );
        
        if (rendered.trim()) {
          renderedFragments.push({
            template: fragment.template,
            content: rendered,
            priority: fragment.priority
          });
        }
      } catch (error) {
        console.error(`Failed to render fragment ${fragment.template}:`, error);
        // Continue with other fragments - don't fail entire render
      }
    }
    
    return renderedFragments;
  }

  private assembleEmailContent(
    fragments: RenderedFragment[],
    templateContext: any
  ): string {
    const fragmentsContent = fragments
      .map(f => f.content)
      .join('\n\n');
    
    // Add greeting and standard structure
    return `<p>Hello,</p>\n\n${fragmentsContent}`;
  }
}
```

### Integration with Existing Fragment System

**Fragment Compatibility:**
- **Reuse existing fragments** from `templates/core-agent/fragments/`
- **Maintain priority system** with predefined template ordering
- **Support fragment deduplication** to prevent duplicate content
- **Preserve context merging** patterns for data injection

**Template Path Resolution:**
```typescript
interface FragmentTemplate {
  // Existing fragment paths
  'core-agent/fragments/status-messages/pending-commercial-invoice': StatusFragment;
  'core-agent/fragments/details/shipment-identifiers': DetailFragment;
  'core-agent/fragments/document-requests/rush-processing-response': RequestFragment;
  'core-agent/fragments/system/error-message': SystemFragment;
}
```

### Performance Optimization Strategy

**Caching Layers:**
1. **Template Compilation Cache** - Compiled Nunjucks templates
2. **Fragment Render Cache** - Rendered fragment content by context hash
3. **Context Data Cache** - Clean-agent-context results by shipment ID
4. **LLM Response Cache** - Dynamic content by prompt hash

**Parallel Processing:**
```typescript
// Parallel data gathering
const contextData = await Promise.all([
  this.cleanAgentContextService.gatherShipmentData(shipment),
  this.cleanAgentContextService.gatherDocumentData(shipment),
  this.cleanAgentContextService.gatherRnsData(shipment),
  this.cleanAgentContextService.gatherImporterData(shipment),
  this.cleanAgentContextService.gatherValidationData(shipment)
]);

// Parallel fragment rendering
const fragmentPromises = fragments.map(fragment => 
  this.renderFragment(fragment, templateContext)
);
const renderedFragments = await Promise.all(fragmentPromises);
```

**Memory Management:**
- **Streaming for large templates** to reduce memory usage
- **Garbage collection hints** after large renders
- **Context cleanup** to prevent memory leaks
- **Fragment pooling** for frequently used templates