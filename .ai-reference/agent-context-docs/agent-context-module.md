# agent-context.module.ts Documentation

## Overview
- **Purpose**: NestJS module that provides clean, reusable context building services for agent systems
- **Type**: NestJS Module
- **Location**: `/apps/portal-api/src/agent-context/agent-context.module.ts`

## Dependencies
### External Dependencies
- `@nestjs/common`: Provides `@Module` decorator for NestJS module definition

### Internal Dependencies
- `./services/shipment-context.service` → ShipmentContextService: Core orchestration service
- `./services/shipment-services.adapter` → ShipmentServicesAdapter: Business logic adapter
- `./utils/safe-evaluation.util` → SafeEvaluationUtil: Utility for safe evaluation operations

## Implementation

### Module: AgentContextModule
NestJS module that follows KISS (Keep It Simple, Stupid) principles for context building in agent systems.

**Architecture Philosophy**:
- **Single responsibility**: Context building only
- **Minimal dependencies**: Uses interfaces instead of concrete services  
- **Clean separation**: Adapter contains complexity, service orchestrates

#### Providers Configuration
The module uses a sophisticated dependency injection pattern where multiple interface tokens point to the same adapter implementation:

**Core Services**:
- `ShipmentContextService`: Main orchestration service (minimal, focused)
- `SafeEvaluationUtil`: Utility service for safe operations
- `ShipmentServicesAdapter`: Contains all business logic

**Interface Token Mappings**:
All interface tokens are mapped to `ShipmentServicesAdapter` using `useExisting`:
- `"SHIPMENT_DATA_PROVIDER"` → ShipmentServicesAdapter
- `"BUSINESS_RULE_EVALUATOR"` → ShipmentServicesAdapter  
- `"CONTEXT_FORMATTER"` → ShipmentServicesAdapter
- `"ENTRY_SUBMISSION_SERVICE"` → ShipmentServicesAdapter

**Benefits of this pattern**:
1. **Interface Segregation**: Different consumers can depend on specific interfaces
2. **Single Implementation**: One adapter implements all interfaces, reducing complexity
3. **Testability**: Easy to mock specific interfaces during testing
4. **Flexibility**: Can switch implementations without changing consumers

#### Exports
- `ShipmentContextService`: Main service for external consumers
- `ShipmentServicesAdapter`: Exported for ProcessDocumentHandler dependency injection

## Design Patterns
- **Adapter Pattern**: ShipmentServicesAdapter adapts complex business logic to simple interfaces
- **Dependency Injection**: Uses NestJS DI container with interface tokens
- **Facade Pattern**: ShipmentContextService acts as a facade over the adapter complexity

## Security Considerations
- Clean separation of concerns reduces attack surface
- Interface-based design limits exposure of internal implementation details
- SafeEvaluationUtil suggests secure evaluation practices

## Testing Considerations
- Mock individual interface tokens for focused unit testing
- Test module configuration with different adapter implementations
- Verify proper provider resolution through NestJS DI system