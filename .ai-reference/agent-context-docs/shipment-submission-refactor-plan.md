# Shipment Submission Refactor - Complete Plan

## Overview
Replace the bloated `ShipmentServicesAdapter` (1000+ lines, 5 interfaces, complex service resolution) with clean, focused services that integrate directly with `CleanAgentContextService`.

## Current Problems
- **God Object**: `ShipmentServicesAdapter` implements 5 interfaces with 30+ methods
- **Duplicate Logic**: Custom service resolution when `CleanAgentContextService` exists
- **Poor Separation**: Transaction management mixed with business logic
- **Hard to Test**: Complex dependencies and massive methods
- **Unnecessary Abstraction**: Adapter layer adds complexity without value

## Target Architecture

### **New Focused Services**
```typescript
ShipmentSubmissionService     // Handles submission orchestration (~20 lines)
TransactionManager           // Reusable transaction management utility
ShipmentBusinessRulesService  // Business rule evaluations (future)
ShipmentFormattingService     // Template formatting (future) 
ShipmentEditService          // Shipment editing (future)
```

### **Direct Integration Pattern**
```
Email Handler → ShipmentSubmissionService → CleanAgentContext → CustomStatusService
```

## Migration Plan

### **Phase 1: Create New Clean Services**
1. **TransactionManager** - Extract flexible transaction pattern
2. **ShipmentSubmissionService** - Clean submission orchestration
3. **Unit Tests** - Comprehensive test coverage
4. **Integration Tests** - Verify with existing systems

### **Phase 2: Update Consumers** 
1. Update email handlers to use `ShipmentSubmissionService` directly
2. Update any other code using `attemptShipmentSubmission`
3. Integration testing with email flow

### **Phase 3: Remove Adapter**
1. Delete `ShipmentServicesAdapter` entirely
2. Delete the 5 over-engineered interfaces
3. Clean up imports and dependencies
4. Final verification testing

---

## Phase 1 Implementation Details

### **1. TransactionManager Service**

**Purpose**: Extract the flexible transaction pattern for reuse across services

**Key Features**:
- Can create own transaction OR use provided QueryRunner
- Prevents nested transaction issues
- Proper connection lifecycle management
- Error handling with rollback

**Location**: `/apps/portal-api/src/common/services/transaction-manager.service.ts`

```typescript
@Injectable()
export class TransactionManager {
  constructor(@InjectDataSource() private readonly dataSource: DataSource) {}

  async executeInTransaction<T>(
    queryRunner: QueryRunner | undefined,
    operation: (runner: QueryRunner) => Promise<T>
  ): Promise<T> {
    const shouldManageTransaction = !queryRunner;
    let runner: QueryRunner;

    if (shouldManageTransaction) {
      runner = this.dataSource.createQueryRunner();
      await runner.connect();
      await runner.startTransaction();
    } else {
      runner = queryRunner;
    }

    try {
      const result = await operation(runner);
      
      if (shouldManageTransaction) {
        await runner.commitTransaction();
      }
      
      return result;
    } catch (error) {
      if (shouldManageTransaction) {
        await runner.rollbackTransaction();
      }
      throw error;
    } finally {
      if (shouldManageTransaction) {
        await runner.release();
      }
    }
  }
}
```

### **2. ShipmentSubmissionService**

**Purpose**: Clean submission orchestration using CleanAgentContext

**Key Features**:
- Uses existing `CleanAgentContextService.getUnifiedTemplateContext()`
- Delegates to `CustomStatusService` for business logic
- Handles transaction management via `TransactionManager`
- Simple validation and early exits
- Synchronizes shipment status after submission

**Location**: `/apps/portal-api/src/agent-context/services/shipment-submission.service.ts`

```typescript
export interface SubmissionResult {
  submissionResult?: {
    liveShipmentId: number | null;
    liveEntryUploadFailedShipment: { shipmentId: number; failedReason: string } | null;
    customsStatusCheckErrorShipment: ICustomsStatusCheckError | null;
    shipmentStatusUpdate: { shipmentId: number; newStatus: string } | null;
  };
  submissionError?: string;
  alreadySubmitted?: boolean;
}

@Injectable()
export class ShipmentSubmissionService {
  private readonly logger = new Logger(ShipmentSubmissionService.name);

  constructor(
    private readonly cleanContext: CleanAgentContextService,
    private readonly transactionManager: TransactionManager,
    private readonly moduleRef: ModuleRef
  ) {}

  async attemptSubmission(
    shipmentId: number,
    organizationId: number,
    queryRunner?: QueryRunner
  ): Promise<SubmissionResult> {
    this.logger.log(`Attempting submission for shipment ${shipmentId}`);

    try {
      // Get unified context data
      const context = await this.cleanContext.getUnifiedTemplateContext(shipmentId, organizationId);

      // Early exit checks
      if (this.isAlreadySubmitted(context.shipment)) {
        this.logger.log(`Shipment ${shipmentId} already submitted - status: ${context.shipment.customsStatus}`);
        return { alreadySubmitted: true };
      }

      // Resolve CustomStatusService with organization context
      const customStatusService = await this.resolveCustomStatusService(context.organization);

      // Execute submission with transaction management
      return await this.transactionManager.executeInTransaction(queryRunner, async (runner) => {
        const result = await customStatusService.processShipmentForCustomsStatus(
          context.shipment,
          context.compliance,
          runner
        );

        // Sync in-memory status if updated
        if (result.shipmentStatusUpdate) {
          context.shipment.customsStatus = result.shipmentStatusUpdate.newStatus;
          this.logger.log(`Updated shipment ${shipmentId} status to ${result.shipmentStatusUpdate.newStatus}`);
        }

        this.logger.log(`Submission completed for shipment ${shipmentId}`);
        return { submissionResult: result };
      });

    } catch (error) {
      this.logger.error(`Submission failed for shipment ${shipmentId}: ${error.message}`, error.stack);
      return { submissionError: error.message };
    }
  }

  private isAlreadySubmitted(shipment: Shipment): boolean {
    const finalStatuses = ["entry-submitted", "entry-accepted", "exam", "released"];
    return finalStatuses.includes(shipment.customsStatus);
  }

  private async resolveCustomStatusService(organization: Organization): Promise<CustomStatusService> {
    const contextId = ContextIdFactory.create();
    this.moduleRef.registerRequestByContextId(generateRequest(null, organization), contextId);
    return await this.moduleRef.resolve(CustomStatusService, contextId, { strict: false });
  }
}
```

### **3. Module Integration**

**Update AgentContextModule**:
```typescript
@Module({
  imports: [
    // ... existing imports
    CommonModule, // For TransactionManager
  ],
  providers: [
    // ... existing providers
    ShipmentSubmissionService,
  ],
  exports: [
    // ... existing exports
    ShipmentSubmissionService,
  ],
})
export class AgentContextModule {}
```

**Create/Update CommonModule**:
```typescript
@Module({
  providers: [TransactionManager],
  exports: [TransactionManager],
})
export class CommonModule {}
```

### **4. Testing Strategy**

#### **Unit Tests**
- **TransactionManager**: Test transaction creation, commit, rollback, connection management
- **ShipmentSubmissionService**: Test orchestration logic, early exits, error handling

#### **Integration Tests**
- Test with real `CleanAgentContextService`
- Test with real `CustomStatusService` 
- Test transaction behavior with real database
- Test error scenarios and rollback behavior

#### **Mock Strategy**
```typescript
// Clean mocking - only what's actually needed
const mockCleanContext = {
  getUnifiedTemplateContext: jest.fn()
};

const mockTransactionManager = {
  executeInTransaction: jest.fn()
};

const mockCustomStatusService = {
  processShipmentForCustomsStatus: jest.fn()
};
```

---

## Success Criteria

### **Phase 1 Complete When:**
1. ✅ `TransactionManager` service created and tested
2. ✅ `ShipmentSubmissionService` service created and tested  
3. ✅ All unit tests passing (>90% coverage)
4. ✅ Integration tests passing with existing systems
5. ✅ Services properly exported from modules
6. ✅ No breaking changes to existing functionality

### **What to Check After Phase 1:**
1. **Service Registration**: Verify services are properly injectable
2. **CleanAgentContext Integration**: Verify `getUnifiedTemplateContext()` works correctly
3. **Transaction Behavior**: Verify transactions commit/rollback properly
4. **Error Handling**: Verify errors are caught and logged appropriately
5. **Performance**: Verify no significant performance regression
6. **Memory**: Verify no connection leaks or memory issues

### **Manual Testing Checklist:**
- [ ] Can resolve `ShipmentSubmissionService` from DI container
- [ ] Can call `attemptSubmission()` with valid shipment ID
- [ ] Early exit works for already-submitted shipments
- [ ] Transaction manager creates/commits transactions properly
- [ ] Error scenarios are handled gracefully
- [ ] Logging output is appropriate and helpful

---

## Future Phases (After Phase 1 Approval)

### **Phase 2: Update Consumers**
- Update email handlers to inject `ShipmentSubmissionService`
- Replace calls to `adapter.attemptShipmentSubmission()` 
- Update any other consumers of the submission functionality

### **Phase 3: Remove Adapter**
- Delete `ShipmentServicesAdapter` 
- Delete over-engineered interfaces
- Clean up imports and dependencies
- Extract remaining useful functionality to focused services

### **Benefits After Complete Migration**
- **~1000 lines removed** from codebase
- **5 over-engineered interfaces** eliminated
- **Simplified testing** with focused responsibilities
- **Better performance** with direct dependencies
- **Cleaner architecture** with single-responsibility services
- **Easier maintenance** with clear service boundaries