# index.ts Documentation

## Overview
- **Purpose**: Main export file for the agent-context module, providing clean public API
- **Type**: Module Index/Barrel Export
- **Location**: `/apps/portal-api/src/agent-context/index.ts`

## Dependencies
### Internal Dependencies
- `./agent-context.module` → AgentContextModule: Main NestJS module
- `./services/shipment-context.service` → ShipmentContextService, ShipmentContextWithServices: Core services
- `./types/shipment-context.types` → ShipmentContext: Type definitions
- `./interfaces/shipment-context-services.interface` → Multiple interfaces: Service contracts
- `./utils/safe-evaluation.util` → SafeEvaluationUtil: Utility functions

## Implementation

### Export Categories

#### Main Module
```typescript
export { AgentContextModule } from "./agent-context.module";
```
- **Purpose**: Exports the main NestJS module for dependency injection
- **Usage**: Import this in other modules to use agent context services

#### Core Service
```typescript
export { ShipmentContextService, ShipmentContextWithServices } from "./services/shipment-context.service";
```
- **ShipmentContextService**: Main orchestration service for building shipment context
- **ShipmentContextWithServices**: Extended type/interface that includes service dependencies
- **Usage**: Primary service consumers will use for context building operations

#### Types
```typescript
export { ShipmentContext } from "./types/shipment-context.types";
```
- **ShipmentContext**: Core type definition for shipment context data structure
- **Usage**: TypeScript consumers need this for type safety when working with shipment contexts

#### Interfaces (for testing and advanced usage)
```typescript
export {
  IShipmentDataProvider,
  IBusinessRuleEvaluator,
  IContextFormatter,
  IEmailService,
  IRNSStatusChangeEmailSender,
  IRnsProofService,
  IImporterService,
  ICustomsStatusListener,
  IEntrySubmissionService,
  ICommercialInvoiceService
} from "./interfaces/shipment-context-services.interface";
```

**Interface Categories**:
- **Data Providers**: `IShipmentDataProvider` - Retrieves shipment data
- **Business Logic**: `IBusinessRuleEvaluator` - Evaluates business rules and compliance
- **Formatting**: `IContextFormatter` - Formats context for consumption
- **Communication**: `IEmailService`, `IRNSStatusChangeEmailSender` - Email operations
- **External Services**: `IRnsProofService`, `IImporterService` - Third-party integrations  
- **Event Handling**: `ICustomsStatusListener` - Status change notifications
- **Submission**: `IEntrySubmissionService` - Entry submission operations
- **Document Processing**: `ICommercialInvoiceService` - Invoice processing

#### Utilities
```typescript
export { SafeEvaluationUtil } from "./utils/safe-evaluation.util";
```
- **SafeEvaluationUtil**: Utility for safe evaluation operations
- **Usage**: Advanced consumers may need direct access to evaluation utilities

## Design Patterns
- **Barrel Export Pattern**: Single entry point for all module exports
- **Interface Segregation**: Exports specific interfaces for different use cases
- **Layered Architecture**: Separates module, services, types, interfaces, and utilities

## Usage Guidelines
- **Standard Usage**: Import `AgentContextModule` and `ShipmentContextService`
- **Testing**: Use individual interfaces for mocking specific functionality
- **Advanced Usage**: Access utilities and types directly when needed
- **Type Safety**: Always import `ShipmentContext` type when working with context data

## Security Considerations
- Clean API surface reduces accidental exposure of internal implementation
- Interface exports allow for secure mocking and testing patterns
- Utilities export enables secure evaluation practices

## Testing Considerations
- All interfaces exported for comprehensive mocking capabilities
- Core types available for test data construction
- Utilities accessible for testing evaluation logic
- Module export enables integration testing