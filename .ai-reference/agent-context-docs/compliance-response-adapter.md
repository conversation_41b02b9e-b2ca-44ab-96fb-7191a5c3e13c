# compliance-response.adapter.ts Documentation  

## Overview
- **Purpose**: Simplified formatter for compliance response that converts compliance issues to user-friendly strings
- **Type**: Pure Function Utility
- **Location**: `/apps/portal-api/src/agent-context/adapters/compliance-response.adapter.ts`

## Dependencies
### External Dependencies
- `nest-modules`: Provides ValidateShipmentComplianceResponseDto type

## Implementation

### formatComplianceResponseToStrings Function
**Signature**: `formatComplianceResponseToStrings(compliance: ValidateShipmentComplianceResponseDto): string[]`
**Purpose**: Converts complex compliance validation results into user-friendly error messages
**Design Philosophy**: Minimal version focusing on most common compliance issues

#### Implementation Logic

##### Missing Fields Processing
```typescript
if (compliance.missingFields?.length > 0) {
  compliance.missingFields.forEach((field) => {
    errors.push(`Missing required field: ${field}`);
  });
}
```
- **Purpose**: Identifies missing required shipment fields
- **Output Format**: "Missing required field: {fieldName}"
- **Example**: "Missing required field: cargoControlNumber"

##### Missing Commercial Invoice
```typescript
if (compliance.noCommercialInvoice) {
  errors.push("Commercial invoice required for customs clearance");
}
```
- **Purpose**: Identifies when commercial invoice is entirely missing
- **Output**: Single descriptive message about invoice requirement

##### Non-Compliant Invoices Processing
**Structure**: Nested processing of invoice → lines → records hierarchy

**Invoice Level Processing**:
1. **Missing Invoice Fields**: Maps invoice-level missing fields
2. **Missing Ship-To Fields**: Identifies shipping address issues  
3. **Missing Vendor Fields**: Identifies vendor information gaps
4. **Non-Compliant Lines**: Processes line-item issues

**Line Level Processing**:
```typescript
invoice.nonCompliantLines.forEach((line) => {
  const linePrefix = `${invoicePrefix} Line ${line.lineId}`;
  
  if (line.isHsCodeInvalid) {
    errors.push(`${linePrefix}: Invalid HS code`);
  }
  
  if (line.isQuantityInvalid) {
    errors.push(`${linePrefix}: Invalid quantity`);
  }
  
  if (line.nonCompliantRecords?.length > 0) {
    line.nonCompliantRecords.forEach((record) => {
      errors.push(`${linePrefix}: ${record.reason || "Compliance issue"}`);
    });
  }
});
```

**Error Message Hierarchy**:
- **Invoice Level**: "Invoice {index}: Missing {field}"
- **Line Level**: "Invoice {index} Line {lineId}: {issue}"
- **Record Level**: "Invoice {index} Line {lineId}: {reason}"

#### Error Categories Processed

##### Shipment Level Errors
1. **Missing Required Fields**: Core shipment data gaps
2. **Missing Commercial Invoice**: Complete invoice absence

##### Invoice Level Errors  
1. **Missing Invoice Fields**: Invoice metadata issues
2. **Missing Ship-To Fields**: Shipping address problems
3. **Missing Vendor Fields**: Vendor information gaps

##### Line Item Errors
1. **Invalid HS Code**: Harmonized System classification issues
2. **Invalid Quantity**: Quantity validation failures  
3. **Compliance Records**: Specific regulatory compliance issues

## Design Patterns
- **Pure Function**: No side effects, predictable input/output
- **Hierarchical Processing**: Processes nested compliance structure systematically
- **Error Aggregation**: Collects all errors into single array
- **Descriptive Messaging**: Converts technical errors to user-friendly descriptions

## Message Format Standards
- **Consistent Prefixing**: Uses invoice/line prefixes for context
- **Descriptive Language**: User-friendly rather than technical terminology
- **Hierarchical Context**: Maintains context through message structure
- **Fallback Messaging**: Provides default messages for unknown issues

## Error Processing Flow
1. **Initialize Error Array**: Start with empty error collection
2. **Process Shipment Fields**: Add missing field errors
3. **Process Invoice Status**: Add missing invoice error
4. **Process Each Invoice**: Iterate through non-compliant invoices
   - Add invoice-level field errors
   - Process each non-compliant line
   - Add line-level validation errors
   - Process compliance records
5. **Return Aggregated Errors**: Return complete error list

## Use Cases
- **User Interface Display**: Convert technical compliance data for UI
- **Email Notifications**: Format compliance issues for email alerts
- **Report Generation**: Create readable compliance reports
- **Debug Information**: Provide clear error descriptions for troubleshooting

## Performance Considerations
- **Linear Complexity**: O(n) where n is total compliance issues
- **Memory Efficient**: Builds error array incrementally
- **No External Dependencies**: Pure function with no I/O operations
- **Minimal Processing**: Focuses only on essential error formatting

## Security Considerations
- **No User Input Processing**: Only processes validated compliance objects
- **No Data Modification**: Pure function that doesn't modify inputs
- **Safe Error Handling**: Uses optional chaining for safe property access
- **No Sensitive Data Exposure**: Only formats compliance metadata

## Testing Considerations
- **Pure Function Testing**: Easy to test with predictable inputs/outputs
- **Edge Case Coverage**: Test with empty, null, and malformed compliance data
- **Message Format Verification**: Verify consistent error message formatting
- **Hierarchical Testing**: Test nested invoice/line/record error processing
- **Error Aggregation Testing**: Verify all error types are properly collected

## Error Message Examples

### Shipment Level
- "Missing required field: cargoControlNumber"
- "Commercial invoice required for customs clearance"

### Invoice Level  
- "Invoice 1: Missing invoiceNumber"
- "Invoice 2: Missing ship-to address"
- "Invoice 1: Missing vendor name"

### Line Level
- "Invoice 1 Line 5: Invalid HS code"
- "Invoice 2 Line 3: Invalid quantity"
- "Invoice 1 Line 7: MISSING_OGD_FILING"

## Integration Points
- **Used by**: ShipmentServicesAdapter.formatComplianceErrors method
- **Called from**: Context formatting operations
- **Returns to**: Template rendering systems and user interfaces