# shipment-context.types.ts Documentation

## Overview
- **Purpose**: Defines the comprehensive ShipmentContext interface for agent systems
- **Type**: TypeScript Type Definitions
- **Location**: `/apps/portal-api/src/agent-context/types/shipment-context.types.ts`

## Dependencies
### External Dependencies
- `nest-modules`: Provides core entity types (Shipment, ValidateShipmentComplianceResponseDto, Organization, ValidateCommercialInvoiceComplianceResponseDto, ShipmentColumn)

## Implementation

### ShipmentContext Interface
**Purpose**: Comprehensive context interface containing all shipment-related business rule evaluations, formatted display data, and service instances. Serves as the single source of truth for all shipment response generation.

#### Raw Data Properties
```typescript
shipment: Shipment;
compliance: ValidateShipmentComplianceResponseDto;
organization: Organization;
```
- **shipment**: Core shipment entity with all base data
- **compliance**: Validation results for the shipment
- **organization**: Organization entity associated with the shipment

#### Business Rule Evaluations
**Purpose**: Evaluated once, used everywhere to avoid redundant calculations
```typescript
canRush: boolean;
canGenerateCAD: boolean;
canGenerateRNSProof: boolean;
isCompliant: boolean;
isReleased: boolean;
isSubmitted: boolean;
canBeModified: boolean;
isEntryUploaded: boolean;
canUpdateEntry: boolean;
isAllDocsReceived: boolean;
```

**Rule Categories**:
- **Processing Flags**: `canRush`, `canBeModified`, `canUpdateEntry`
- **Document Generation**: `canGenerateCAD`, `canGenerateRNSProof`
- **Status Checks**: `isCompliant`, `isReleased`, `isSubmitted`, `isEntryUploaded`, `isAllDocsReceived`

#### Detailed Data for Templates
```typescript
missingDocuments: string[];
complianceErrors: string[];
nonCompliantInvoices: ValidateCommercialInvoiceComplianceResponseDto[];
rushBlockingReason: string;
cadBlockingReason: string;
rnsBlockingReason: string;
```
- **Error Arrays**: Lists of missing documents and compliance errors
- **Blocking Reasons**: Detailed explanations for why certain actions are blocked
- **Non-compliant Data**: Specific invoice compliance issues

#### Formatted Display Values
**ShipmentIdentifiers**:
```typescript
shipmentIdentifiers: {
  hblNumber: string | null;
  cargoControlNumber: string | null;
  transactionNumber: string | null;
  containerNumbers: string[];
  formattedContainers: string;
  hasMultipleContainers: boolean;
  primaryContainer: string | null;
};
```

**ETA Information**:
```typescript
etaInformation: {
  etaPortValue?: string;
  portName?: string;
  etaDestinationValue?: string;
  destinationName?: string;
};
```

**Shipping Information**:
```typescript
shippingInformation: {
  isTrackerOnline: boolean;
  shipmentStatus: string;
  isAir: boolean;
  trackingStatus?: string;
};
```

#### Enhanced Document and Missing Fields Analysis
**DocumentDataStatus**:
```typescript
documentDataStatus: {
  hasHBLDataForSubmission: boolean;
  hasAnEmfDataForSubmission: boolean;
  hasCompleteHBLData: boolean;
  hasCompleteAnEmfData: boolean;
  ciReceived: boolean;
  plReceived: boolean;
  hblStatus: "Received" | "Missing";
  anEmfStatus: "Received" | "Missing";
  ciPlStatus: "Received" | "Missing";
};
```

**MissingFieldsAnalysis**:
```typescript
missingFieldsAnalysis: {
  missingIdentifiers: string[];
  missingMeasurements: string[];
  missingTiming: string[];
  missingLocations: string[];
  ogdFilingStatus: "pending" | "complete" | "not-required";
  formattedMissingFields: string[];
};
```

#### Template Context
**Purpose**: Comprehensive context for template rendering
```typescript
templateContext: {
  identifiers: {
    ccn: string;
    hbl: string;
    containers: string[];
    formattedContainers: string;
  };
  documentStatus: {
    hblStatus: "Received" | "Missing";
    anEmfStatus: "Received" | "Missing";
    ciPlStatus: "Received" | "Missing";
  };
  missingFields: {
    forPendingCommercialInvoice: string[];
    forPendingConfirmation: string[];
    formatted: string[];
  };
  timing: {
    etaPort: string | null;
    etaDestination: string | null;
    releaseDate: string | null;
    formattedEtaPort: string;
    formattedEtaDestination: string;
    formattedReleaseDate: string;
  };
  statusContext: {
    primaryMessage: string;
    secondaryDetails: string[];
    actionRequired: boolean;
  };
};
```

#### Smart Template Context
**Purpose**: Template-ready variables optimized for smart templates
```typescript
smartTemplateContext: {
  statusResponseMessage: string;
  rushActionMessage?: string;
  documentBlockerReason?: string;
  transportMode: "OCEAN" | "AIR" | "TRUCK";
  hasETA: boolean;
  etaDate?: string;
  formattedReleaseDate?: string;
  transactionNumber?: string;
  cadDocumentAvailable: boolean;
  rnsDocumentAvailable: boolean;
  documentStatus: {
    hbl: "Received" | "Missing";
    anEmf: "Received" | "Missing";
    ciPl: "Received" | "Missing";
  };
  missingItems: string[];
  ogdFilingStatus: string;
};
```

#### User Interaction Tracking
```typescript
directlyAsked: {
  [key: string]: boolean;
};
```
**Purpose**: Tracks which information the user has directly requested, modified by intent handlers

#### Side Effect Results
```typescript
sideEffects: {
  cadDocument?: {
    fileName: string;
    mimeType: string;
    b64Data: string;
  };
  rnsProofData?: {
    content: string;
    releaseDate: string | null;
  };
  backofficeAlerts: {
    rushProcessingSent?: boolean;
    manualProcessingSent?: boolean;
    holdShipmentSent?: boolean;
  };
};
```
**Purpose**: Populated by intent handlers to track performed actions and generated documents

#### Backward Compatibility
```typescript
documentReceiptStatus: {
  hblReceived: boolean;
  anEmfReceived: boolean;
  ciReceived: boolean;
  plReceived: boolean;
};
missingFieldsStatus: string[];
```
**Purpose**: Maintains existing document receipt status interface for legacy consumers

## Design Patterns
- **Single Source of Truth**: Comprehensive interface eliminates data duplication
- **Pre-computed Values**: Business rules evaluated once, cached for reuse
- **Layered Data Structure**: Raw data, computed values, formatted data, and side effects
- **Template Optimization**: Multiple context objects optimized for different template types

## Performance Considerations
- **One-time Evaluation**: Business rules computed once during context building
- **Formatted Values**: Pre-formatted strings eliminate runtime formatting
- **Structured Data**: Nested objects provide direct access without computation

## Security Considerations
- **Immutable Interface**: TypeScript interface prevents accidental modification
- **Structured Side Effects**: Clear tracking of performed actions
- **Safe Document Handling**: Base64 encoding for document attachments

## Testing Considerations
- **Comprehensive Coverage**: All business rules exposed for testing
- **Mock-friendly Structure**: Clear separation between raw data and computed values
- **Side Effect Tracking**: Easy to verify performed actions in tests