# attemptShipmentSubmission Method - Complete Analysis

## Overview
- **Location**: `/apps/portal-api/src/agent-context/services/shipment-services.adapter.ts` (Lines 289-437)
- **Purpose**: Orchestrates the complete shipment submission workflow to Canadian customs
- **Pattern**: Transaction-aware orchestrator that delegates to shipment module business logic
- **Complexity**: 150+ lines with complex transaction management and service resolution

## Method Signature

```typescript
async attemptShipmentSubmission(
  shipment: Shipment,                                    // Target shipment for submission
  compliance: ValidateShipmentComplianceResponseDto,     // Pre-validated compliance data
  organizationId: number,                                // Organization context for service resolution
  queryRunner?: QueryRunner                              // Optional - for transaction management
): Promise<{
  submissionResult?: {
    liveShipmentId: number | null;                       // Success indicator - shipment went live
    liveEntryUploadFailedShipment: {                     // Submission failure details
      shipmentId: number; 
      failedReason: string;
    } | null;      
    customsStatusCheckErrorShipment: ICustomsStatusCheckError | null;  // Timing/validation errors
    shipmentStatusUpdate: {                              // Status change notification
      shipmentId: number; 
      newStatus: string;
    } | null;               
  };
  submissionError?: string;                              // Top-level orchestration errors
}>
```

## Complete Step-by-Step Flow

### **Phase 1: Input Validation & Early Exits (Lines 304-318)**

1. **Null Shipment Check**
   ```typescript
   if (!shipment) {
     return { submissionError: "No shipment found for submission" };
   }
   ```

2. **Final Status Prevention**
   ```typescript
   const finalStatuses = ["entry-submitted", "entry-accepted", "exam", "released"];
   if (finalStatuses.includes(shipment.customsStatus)) {
     return {}; // Empty object - template handles status message
   }
   ```
   - **Purpose**: Prevents reprocessing of already-submitted shipments
   - **Return**: Empty object lets template show current status

### **Phase 2: Service Resolution (Lines 322-323)**

3. **Organization Context Setup**
   ```typescript
   const organization = await this.fetchOrganization(organizationId);
   ```
   - **Database Query**: Uses `FIND_ORGANIZATION_RELATIONS` for complete org data
   - **Error Handling**: Throws `NotFoundException` if org not found

4. **REQUEST-Scoped Service Resolution**
   ```typescript
   const services = await this.resolveRequestScopedServices(organization);
   ```
   - **Pattern**: Creates ContextId → registers request → resolves 10 services in parallel
   - **Services Resolved**: ShipmentService, ComplianceValidationService, CustomStatusService, etc.
   - **Purpose**: Ensures all services have proper organization context

### **Phase 3: Fresh Compliance Validation (Lines 326-340)**

5. **Get Current Shipment Compliances**
   ```typescript
   const shipmentCompliances = await services.complianceValidationService.getShipmentCompliances([shipment]);
   ```

6. **Validate Against Current Rules**
   ```typescript
   const validationResults = services.complianceValidationService.validateShipmentCompliances(
     shipmentCompliances,
     services.complianceValidationService.isDemoShipment(shipment)
   );
   ```
   - **Fresh Validation**: Ignores passed `compliance` parameter, gets current state
   - **Demo Mode**: Different validation rules for demo organizations

7. **Validation Result Check**
   ```typescript
   if (validationResults.length === 0) {
     return { submissionError: "Unable to validate shipment for submission" };
   }
   ```

### **Phase 4: Transaction Management Setup (Lines 351-365)**

8. **Transaction Strategy Decision**
   ```typescript
   const shouldManageTransaction = !queryRunner;
   let runner: QueryRunner;
   ```

9. **Transaction Initialization**
   ```typescript
   if (shouldManageTransaction) {
     runner = this.dataSource.createQueryRunner();
     await runner.connect();
     await runner.startTransaction();
   } else {
     runner = queryRunner; // Use caller's transaction
   }
   ```
   - **Pattern**: Flexible transaction management
   - **Prevents**: Nested transaction issues
   - **Allows**: Integration with larger transaction contexts

### **Phase 5: Core Submission Orchestration (Lines 369-375)**

10. **The Critical Delegation**
    ```typescript
    const result = await services.customStatusService.processShipmentForCustomsStatus(
      shipment,
      validationResult,
      runner
    );
    ```

## Critical Call Chain Analysis

### **CustomStatusService.processShipmentForCustomsStatus**
**Location**: `/apps/portal-api/src/shipment/services/custom-status.service.ts` (Lines 648-726)

#### **Step 1: Business Readiness Validation**
```typescript
const isReady = isShipmentReadyToSubmit(validationResult);
```
**Checks:**
- Commercial invoice exists (`!noCommercialInvoice`)
- No missing fields (`missingFields.length === 0`) 
- No non-compliant invoices (`nonCompliantInvoices.length === 0`)

**If Not Ready:**
- Determines new status via `getNewCustomsStatus()`:
  - `PENDING_COMMERCIAL_INVOICE` if no commercial invoice
  - `PENDING_CONFIRMATION` if missing fields/compliance issues
- Returns status update object, exits early

#### **Step 2: Timing Window Validation**
```typescript
const canSubmit = canSubmitImmediately(shipment);
```

**By Transport Mode:**
- **DEMO Organizations**: Always allowed
- **LAND**: Always allowed immediately  
- **OCEAN_FCL**: Complex ETA-based logic
  - **Inland Delivery**: Requires ETA port + destination
    - Submits when: current ≥ ETA port OR current ≥ (ETA destination - 3 days)
  - **Port Delivery**: Requires ETA port
    - Submits when: current ≥ (ETA port - 3 days)
- **AIR/OCEAN_LCL**: Requires ETA destination
  - Submits when: current ≥ ETA destination

**If Timing Failed:**
- Updates status to `PENDING_ARRIVAL`
- May include timing error details for email notifications

#### **Step 3: Entry Submission Attempt**
```typescript
const submissionResult = await trySubmitShipmentEntry(shipment, queryRunner);
```

### **EntrySubmissionService.submitShipmentEntry**
**Location**: `/apps/portal-api/src/shipment/services/entry-submission.service.ts` (Lines 1120-1415)

#### **Major Operations:**
1. **Shipment Enrichment**: `enricher.enrich(shipment)` - adds calculated/derived data
2. **Final Compliance Check**: Re-validates shipment compliance  
3. **Data Mapping**: Maps trade partners and CCIs for external API format
4. **🚨 EXTERNAL API SUBMISSION**: `candataService.createCandataShipment()`
   - **Target**: Candata Canadian Customs API (`http://***************/api`)
   - **Purpose**: Submit customs entry to Canadian government systems
5. **Duty/Tax Calculation**: `candataService.calculateCad()` - external tax calculation
6. **Database Updates**: Updates multiple entities with submission results

#### **Database Operations:**
- **Shipment**: Sets status to `LIVE`, adds `transactionNumber` and `customsFileNumber`
- **CommercialInvoice**: Sets `candataId` for external system tracking
- **CommercialInvoiceLine**: Sets `candataId` plus calculated duty/tax amounts

### **Phase 6: Result Processing (Lines 378-410)**

11. **Critical Status Synchronization**
    ```typescript
    if (result.shipmentStatusUpdate) {
      const shipmentRepository = runner.manager.getRepository(Shipment);
      await shipmentRepository.update(
        { id: result.shipmentStatusUpdate.shipmentId },
        { customsStatus: result.shipmentStatusUpdate.newStatus }
      );
      shipment.customsStatus = result.shipmentStatusUpdate.newStatus; // Sync in-memory object
    }
    ```
    - **Critical**: Updates both database AND in-memory object
    - **Prevents**: Stale data issues in subsequent processing

12. **Transaction Commit (If Managing)**
    ```typescript
    if (shouldManageTransaction) {
      await runner.commitTransaction();
    }
    ```

13. **Outcome Logging**
    ```typescript
    if (result.liveShipmentId) {
      // Success - shipment submitted to customs
    } else if (result.liveEntryUploadFailedShipment) {
      // Submission failed - log error reason
    } else if (result.customsStatusCheckErrorShipment) {
      // Timing/validation error - may retry later
    }
    ```

### **Phase 7: Error Handling (Lines 411-431)**

14. **Transaction Rollback**
    ```typescript
    if (shouldManageTransaction) {
      await runner.rollbackTransaction();
    } else {
      // Log error but don't rollback - external transaction management
    }
    ```

15. **Connection Cleanup**
    ```typescript
    if (shouldManageTransaction) {
      await runner.release();
    }
    ```

## Transaction Management Pattern

### **The Flexible Pattern**
```typescript
// Decision point
const shouldManageTransaction = !queryRunner;

// Setup
if (shouldManageTransaction) {
  runner = this.dataSource.createQueryRunner();
  await runner.connect();
  await runner.startTransaction();
} else {
  runner = queryRunner; // Use caller's transaction
}

// Work with transaction
// ...

// Commit/rollback only if we own the transaction
if (shouldManageTransaction) {
  await runner.commitTransaction(); // or rollbackTransaction()
}

// Cleanup only if we own the connection
if (shouldManageTransaction) {
  await runner.release();
}
```

### **Benefits**
- **Composability**: Can participate in larger transactions
- **Safety**: Avoids nested transaction issues
- **Flexibility**: Works standalone or as part of larger workflow

## What Gets Modified

### **Database Tables**
1. **shipments**: `customsStatus`, `transactionNumber`, `customsFileNumber`
2. **commercial_invoices**: `candataId` (for external system tracking)
3. **commercial_invoice_lines**: `candataId`, duty amounts, tax amounts

### **In-Memory Objects** 
- `shipment.customsStatus` synchronized with database to prevent stale data

### **External Systems**
- **Candata API**: Canadian customs entry submission + duty calculation
- **PostgreSQL**: All database updates via TypeORM transactions

## External System Boundaries

### **Where Processing Leaves Agent-Context/Core-Agent**

1. **CustomStatusService** → **ShipmentModule** (`/apps/portal-api/src/shipment/`)
2. **EntrySubmissionService** → **CandataModule** (`/libraries/nest-modules/src/candata/`)
3. **CandataService** → **External Candata API** (Canadian customs broker)
4. **Database Operations** → **PostgreSQL** (via TypeORM)

### **Final Destinations**
- **Canadian Government Customs Systems** (via Candata API)
- **Claro Database** (submission state tracking)
- **Email Notification Systems** (status change notifications)

## Design Patterns Used

### **1. Orchestrator Pattern**
- Method coordinates between systems but doesn't implement business logic
- Delegates actual submission to specialized services

### **2. Flexible Transaction Management**
- Can create own transaction OR participate in caller's transaction
- Prevents nested transaction issues

### **3. Service Locator Pattern**
- Dynamic resolution of REQUEST-scoped services based on organization context
- Ensures proper multi-tenant service scoping

### **4. Command Pattern**
- Complex submission workflow encapsulated in single method call
- Clear success/failure result structure

## Error Scenarios & Handling

### **Input Validation Errors**
- **Null shipment**: Return `submissionError`
- **Already submitted**: Return empty object (let template handle)

### **Service Resolution Errors**
- **Organization not found**: `NotFoundException` bubbles up
- **Service resolution failure**: Error logged, exception thrown

### **Business Logic Errors**
- **Not ready for submission**: Status update to pending state
- **Timing window not met**: Status update to `PENDING_ARRIVAL` with timing error

### **External API Errors**
- **Candata submission failure**: `liveEntryUploadFailedShipment` with error reason
- **Network/timeout errors**: Captured in error reason for email notifications

### **Transaction Errors**
- **Database errors**: Automatic rollback if managing own transaction
- **Nested transaction issues**: Avoided by flexible transaction pattern

## Performance Considerations

### **Service Resolution Overhead**
- Resolves 10 services in parallel using `Promise.all`
- Creates new ContextId for each submission (REQUEST scope requirement)

### **Database Connections**
- Uses single QueryRunner for all operations in transaction
- Proper connection lifecycle management (connect → work → release)

### **External API Calls**
- Candata API calls are synchronous and blocking
- No retry logic at this level (handled by CandataService)

## Rewrite Requirements

To accurately rewrite this method, you need:

1. **Input Validation Pattern**: Null checks + final status prevention
2. **Service Resolution Pattern**: ContextId creation + parallel service resolution  
3. **Flexible Transaction Pattern**: Conditional transaction management
4. **Delegation Pattern**: Pass control to CustomStatusService with proper error handling
5. **Synchronization Pattern**: Database + in-memory object updates
6. **Comprehensive Logging**: Debug info at every major step
7. **Error Handling Pattern**: Different strategies for different error types

## Code Quality Issues

### **Problems Identified**
- **God Method**: 150+ lines doing too many things
- **Tight Coupling**: Directly manages transactions + service resolution + business logic
- **Duplicate Logic**: Service resolution pattern exists elsewhere (CleanAgentContextService)
- **Mixed Abstractions**: High-level orchestration mixed with low-level transaction management
- **Hard to Test**: Complex dependencies and transaction management

### **Refactoring Opportunities**
- Extract transaction management to utility service
- Use existing CleanAgentContextService instead of custom service resolution
- Split into focused methods (validate → submit → process results)
- Create submission command object to encapsulate parameters
- Add retry logic and circuit breaker patterns for external API calls

## Summary

The `attemptShipmentSubmission` method is a **transaction-aware orchestrator** that:

1. **Validates** input and prevents duplicate processing
2. **Resolves** REQUEST-scoped services with organization context  
3. **Manages** database transactions flexibly (own or caller's)
4. **Delegates** to CustomStatusService for business logic execution
5. **Processes** results and synchronizes state (database + in-memory)
6. **Handles** errors comprehensively with proper cleanup

**Ultimate Purpose**: Bridge between agent-context system and actual Canadian customs submission infrastructure while maintaining proper transaction boundaries and error handling.

**Key Innovation**: Flexible transaction management that allows the method to work standalone or as part of larger transactional workflows.

**Major Weakness**: Violates single responsibility principle by combining orchestration, transaction management, service resolution, and result processing in one massive method.