# customs-status-messages.constants.ts Documentation

## Overview
- **Purpose**: Constants mapping customs status keys to user-friendly display messages
- **Type**: TypeScript Constants and Utility Functions
- **Location**: `/apps/portal-api/src/agent-context/constants/customs-status-messages.constants.ts`

## Dependencies
### External Dependencies
- `nest-modules/types`: Provides CustomsStatus enum

## Implementation

### CUSTOMS_STATUS_MESSAGES Constant
**Type**: `Readonly<Record<CustomsStatus, string>>`
**Purpose**: Maps CustomsStatus enum values to user-facing display strings

#### Status Mappings
```typescript
{
  [CustomsStatus.PENDING_COMMERCIAL_INVOICE]: "Pending Commercial Invoice",
  [CustomsStatus.PENDING_CONFIRMATION]: "Pending Confirmation",
  [CustomsStatus.PENDING_ARRIVAL]: "Pending Arrival",
  [CustomsStatus.LIVE]: "Live",
  [CustomsStatus.ENTRY_SUBMITTED]: "Entry Submitted",
  [CustomsStatus.ENTRY_ACCEPTED]: "Entry Accepted",
  [CustomsStatus.EXAM]: "Under Examination",
  [CustomsStatus.RELEASED]: "Released",
  [CustomsStatus.ACCOUNTING_COMPLETED]: "Accounting Completed"
}
```

**Message Categories**:
- **Pending States**: User-friendly descriptions of what's being waited for
- **Processing States**: Clear indication of current processing phase
- **Final States**: Status indicating completion or resolution

**Design Decisions**:
- Uses enum keys to ensure type safety
- `as const` assertion for immutable object
- Readonly type prevents runtime modification
- Comment suggests "Live" could be "Ready for Submission"

### getCustomsStatusMessage Function
**Signature**: `getCustomsStatusMessage(status: CustomsStatus | string | null | undefined): string`
**Purpose**: Safely retrieves customs status message with fallback for unknown statuses

#### Implementation Logic
1. **Null/Undefined Check**: Returns "Unknown" for falsy values
2. **Direct Mapping**: If status exists in CUSTOMS_STATUS_MESSAGES, returns mapped message
3. **String Fallback**: For valid strings not in mapping:
   - Capitalizes first letter
   - Converts rest to lowercase
   - Example: "CUSTOM_STATUS" → "Custom_status"
4. **Final Fallback**: Returns "Unknown" for non-string values

#### Error Handling Strategy
- **Graceful Degradation**: Never throws errors, always returns a string
- **Safe Type Casting**: Uses `status as CustomsStatus` with validation
- **Fallback Chain**: Multiple fallback levels ensure robust operation

#### Use Cases
- **Known Statuses**: Returns professional display messages
- **New/Unknown Statuses**: Provides reasonable formatting
- **Null Safety**: Handles database null values gracefully
- **API Safety**: Prevents errors in response formatting

## Design Patterns
- **Constants Pattern**: Centralized status message definitions
- **Safe Accessor Pattern**: Function with multiple fallback levels
- **Type Safety**: Leverages TypeScript enum system
- **Immutability**: Readonly constant prevents accidental modification

## Status Message Design Philosophy
- **User-Centric**: Messages written for end users, not developers
- **Professional Language**: Avoids technical jargon
- **Descriptive**: Each message clearly indicates what's happening
- **Consistent**: Uniform capitalization and formatting

## Localization Considerations
- **Centralized Messages**: Easy to replace with localized versions
- **String Constants**: All user-facing text in one location
- **Function-based Access**: Can be extended for multi-language support

## Error Handling Patterns
- **No Exceptions**: Function never throws, always returns valid string
- **Defensive Programming**: Handles all possible input types
- **Graceful Fallbacks**: Multiple levels of fallback behavior

## Security Considerations
- **No User Input Processing**: Only processes enum values and constants
- **Immutable Constants**: Readonly object prevents tampering
- **Safe String Handling**: No eval or dynamic code execution

## Testing Considerations
- **Enum Coverage**: Test all CustomsStatus enum values
- **Edge Cases**: Test null, undefined, and invalid inputs  
- **Fallback Behavior**: Verify string formatting for unknown statuses
- **Type Safety**: Ensure proper TypeScript compilation
- **Message Consistency**: Verify all messages follow same format standards