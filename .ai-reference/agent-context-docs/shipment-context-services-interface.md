# shipment-context-services.interface.ts Documentation

## Overview
- **Purpose**: Defines service interfaces for agent context operations in customs automation system
- **Type**: TypeScript Interface Definitions
- **Location**: `/apps/portal-api/src/agent-context/interfaces/shipment-context-services.interface.ts`

## Dependencies
### External Dependencies
- `nest-modules`: Provides core entity types (Shipment, Organization, Email, CommercialInvoice, Importer, ValidateShipmentComplianceResponseDto)
- `typeorm`: Provides QueryRunner for transaction management

## Implementation

### Service Interfaces for _services Property

#### IEmailService
**Purpose**: Interface for email operations used in intent handlers

**Method: sendBackofficeAlert**
- **Signature**: `sendBackofficeAlert(subject: string, body: string, organization: number | Organization, queryRunner?: QueryRunner): Promise<Email | null>`
- **Purpose**: Sends alert emails to backoffice address
- **Parameters**:
  - `subject`: Email subject line
  - `body`: Email content body
  - `organization`: Organization ID or entity
  - `queryRunner`: Optional transaction runner
- **Returns**: Promise resolving to Email entity or null

#### IRNSStatusChangeEmailSender
**Purpose**: Interface for RNS-specific email operations

**Method: createCADAttachment**
- **Signature**: `createCADAttachment(shipment: Shipment, commercialInvoices: Array<CommercialInvoice>, organizationImporter: Importer, queryRunner?: QueryRunner): Promise<{ fileName: string; mimeType: string; b64Data: string }>`
- **Purpose**: Creates CAD (Customs Automated Declaration) attachment for emails
- **Parameters**:
  - `shipment`: Shipment entity
  - `commercialInvoices`: Array of commercial invoice entities
  - `organizationImporter`: Importer entity
  - `queryRunner`: Optional transaction runner
- **Returns**: Promise resolving to attachment object with file details

#### IRnsProofService
**Purpose**: Interface for RNS (Release Notification System) proof operations

**Method: getRNSProofOfRelease**
- **Signature**: `getRNSProofOfRelease(shipment: Shipment): Promise<{ isReleased: boolean; rnsResponse: unknown | null; releaseDate: string | null }>`
- **Purpose**: Retrieves RNS proof of release data for shipments
- **Parameters**:
  - `shipment`: Shipment entity
- **Returns**: Promise resolving to release status information
- **Note**: Uses `unknown` type for rnsResponse to avoid circular dependencies

#### IImporterService
**Purpose**: Interface for importer management operations

**Method: getImporters**
- **Signature**: `getImporters(params: { organizationId: number; limit?: number }, queryRunner?: QueryRunner): Promise<{ importers: Importer[]; total: number; skip: number; limit: number }>`
- **Purpose**: Retrieves importers for an organization with pagination
- **Parameters**:
  - `params.organizationId`: Organization ID
  - `params.limit`: Optional result limit
  - `queryRunner`: Optional transaction runner
- **Returns**: Promise resolving to paginated importer results

#### ICustomsStatusListener
**Purpose**: Interface for customs status event handling
- **Current Status**: Empty interface, methods to be added as needed

#### ICustomsStatusCheckError
**Purpose**: Interface representing customs status check errors during submission workflow

**Properties**:
- `shipmentId: number`: ID of shipment with error
- `errorType: string`: Categorized error type (e.g., "Invalid CCN", "Missing ETA Port")
- `errorMessage: string`: Human-readable error message
- `stackTrace?: string`: Optional debug stack trace

#### IEntrySubmissionService
**Purpose**: Interface for customs entry submission operations

**Method: attemptShipmentSubmission**
- **Signature**: Complex submission method with comprehensive error handling
- **Purpose**: Submits shipments for customs processing following established workflow
- **Parameters**:
  - `shipment`: Shipment entity to submit
  - `compliance`: Pre-validated compliance data
  - `organizationId`: Organization ID for service resolution
  - `queryRunner`: Optional transaction runner
- **Returns**: Promise with submission results or error information
- **Features**:
  - Handles validation, timing checks, and submission
  - Provides transaction management flexibility
  - Returns detailed submission results including errors

#### ICommercialInvoiceService
**Purpose**: Interface for commercial invoice operations
- **Current Status**: Empty interface, methods to be added as needed

#### IShipmentService
**Purpose**: Interface for shipment management operations

**Method: editShipment**
- **Signature**: `editShipment(shipmentId: number, updateData: Record<string, any>, queryRunner?: QueryRunner): Promise<Shipment>`
- **Purpose**: Edits shipment with provided update data
- **Parameters**:
  - `shipmentId`: ID of shipment to edit
  - `updateData`: Object containing update fields
  - `queryRunner`: Optional transaction runner
- **Returns**: Promise resolving to updated shipment entity

### Context Building Interfaces

#### IShipmentDataProvider
**Purpose**: Interface for fetching shipment-related data

**Methods**:
1. **fetchOrganization**: Retrieves organization data
   - **Signature**: `fetchOrganization(organizationId: number, queryRunner?: QueryRunner): Promise<Organization>`

2. **fetchShipment**: Retrieves shipment data
   - **Signature**: `fetchShipment(shipmentId: number, queryRunner?: QueryRunner): Promise<Shipment>`

3. **fetchCompliance**: Retrieves compliance data
   - **Signature**: `fetchCompliance(shipmentId: number, queryRunner?: QueryRunner): Promise<ValidateShipmentComplianceResponseDto>`

#### IBusinessRuleEvaluator
**Purpose**: Interface for evaluating business rules and compliance checks

**Business Logic Methods**:
1. **canShipmentBeRushed**: Determines rush eligibility
2. **canGenerateCAD**: Checks CAD generation capability
3. **canGenerateRNSProof**: Checks RNS proof generation capability
4. **isCompliant**: Validates shipment compliance
5. **isReleased**: Checks release status
6. **isSubmitted**: Checks submission status
7. **canBeModified**: Checks modification eligibility
8. **isEntryUploaded**: Checks entry upload status (async)
9. **canUpdateEntry**: Checks entry update capability (async)
10. **isAllDocsReceived**: Validates document receipt status
11. **determineDocumentCompleteness**: Analyzes document completeness

**Reason Analysis Methods**:
1. **getRushBlockingReason**: Explains why rush is blocked
2. **getCADBlockingReason**: Explains why CAD generation is blocked
3. **getRNSBlockingReason**: Explains why RNS proof is blocked

#### IContextFormatter
**Purpose**: Interface for formatting context data for display and consumption

**Core Formatting Methods**:
1. **formatCustomsStatus**: Formats customs status for display
2. **buildShipmentIdentifiers**: Creates shipment identifier object
3. **buildEtaInformation**: Creates ETA information object
4. **buildShippingInformation**: Creates shipping status object
5. **buildDocumentDataStatus**: Creates document status object
6. **buildMissingFieldsAnalysis**: Analyzes missing field requirements
7. **buildTemplateContext**: Creates comprehensive template context
8. **buildDocumentReceiptStatus**: Creates document receipt status
9. **buildMissingFieldsStatus**: Creates missing fields status
10. **formatComplianceErrors**: Formats compliance errors to strings

**Complex Return Types**:
- All methods return detailed objects with specific field structures
- Supports nested object composition for complex data relationships
- Provides formatted strings alongside raw data

## Design Patterns
- **Interface Segregation Principle**: Separate interfaces for different service concerns
- **Dependency Inversion**: Services depend on interfaces, not concrete implementations
- **Single Responsibility**: Each interface focuses on one service domain
- **Transaction Support**: Consistent QueryRunner parameter pattern for transaction management

## Error Handling Patterns
- **ICustomsStatusCheckError**: Structured error representation for customs operations
- **Optional Returns**: Methods return null/undefined for non-critical failures
- **Detailed Error Information**: Includes error types, messages, and optional stack traces

## Security Considerations
- Interface-based design limits exposure of implementation details
- Transaction runner pattern ensures secure database operations
- Structured error handling prevents information leakage

## Testing Considerations
- All interfaces designed for easy mocking
- Complex return types provide comprehensive test assertions
- Async methods properly typed for Promise-based testing
- Optional parameters support flexible test scenarios