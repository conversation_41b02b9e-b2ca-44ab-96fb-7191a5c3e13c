# shipment-context.service.ts Documentation

## Overview
- **Purpose**: Simplified, focused service that orchestrates context building using three core interfaces
- **Type**: NestJS Injectable Service  
- **Location**: `/apps/portal-api/src/agent-context/services/shipment-context.service.ts`
- **NestJS Scope**: Singleton (default scope)

## Dependencies
### External Dependencies
- `@nestjs/common`: Provides Injectable decorator, Logger, and exception classes
- `typeorm`: Provides QueryRunner for transaction management
- `nest-modules`: Provides Shipment, ValidateShipmentComplianceResponseDto, ShipmentColumn types
- `moment`: Date formatting library

### Internal Dependencies
- `../types/shipment-context.types` → ShipmentContext: Core context interface
- `../utils/safe-evaluation.util` → SafeEvaluationUtil: Safe operation evaluation
- `../interfaces/shipment-context-services.interface` → Multiple service interfaces
- `../../core-agent/constants/response-messages.constants` → Message constants

## Implementation

### ShipmentContextWithServices Interface
**Purpose**: Extended context interface for internal use that includes service instances
**Design**: Keeps main ShipmentContext interface clean while providing service access

```typescript
export interface ShipmentContextWithServices extends ShipmentContext {
  _services: {
    emailService?: IEmailService;
    rnsStatusChangeEmailSender?: IRNSStatusChangeEmailSender;
    rnsProofService?: IRnsProofService;
    customsStatusListener?: ICustomsStatusListener;
    entrySubmissionService?: IEntrySubmissionService;
    importerService?: IImporterService;
    commercialInvoiceService?: ICommercialInvoiceService;
    shipmentService?: IShipmentService;
  };
}
```

### Class: ShipmentContextService
**Design Philosophy**: Delegates all business logic to interfaces for clean separation of concerns

#### Dependency Injection
Uses interface tokens for loose coupling:
- `@Inject("SHIPMENT_DATA_PROVIDER")` → IShipmentDataProvider
- `@Inject("BUSINESS_RULE_EVALUATOR")` → IBusinessRuleEvaluator  
- `@Inject("CONTEXT_FORMATTER")` → IContextFormatter
- `@Inject("ENTRY_SUBMISSION_SERVICE")` → IEntrySubmissionService
- `SafeEvaluationUtil` → Direct class injection

#### Method: buildContext
**Signature**: `buildContext(shipmentId: number, organizationId: number, queryRunner?: QueryRunner): Promise<ShipmentContextWithServices>`
**Purpose**: Builds comprehensive context for a shipment

**Implementation Steps**:
1. **Input Validation**: Validates shipmentId and organizationId are positive numbers
2. **Data Fetching**: Fetches organization, shipment, and compliance data in parallel
3. **Business Rule Evaluation**: Evaluates all business rules using safe evaluation:
   - `canRush`, `canGenerateCAD`, `canGenerateRNSProof`
   - `isCompliant`, `isReleased`, `isSubmitted`
   - `canBeModified`, `isEntryUploaded`, `canUpdateEntry`, `isAllDocsReceived`
4. **Blocking Reason Analysis**: Determines why certain actions are blocked
5. **Data Formatting**: Formats all display values using formatter interface
6. **Context Assembly**: Builds comprehensive context with all computed values

**Error Handling**:
- Validates input parameters with BadRequestException
- Catches and re-throws NotFoundException and BadRequestException
- Wraps other errors in BadRequestException with context
- Logs all errors with stack traces

**Side Effects**: 
- Logs context building operations
- Uses safe evaluation to prevent context build failures

#### Method: refreshShipmentInContext
**Signature**: `refreshShipmentInContext(context: ShipmentContextWithServices, queryRunner?: QueryRunner): Promise<void>`
**Purpose**: Refreshes shipment context to get latest data after operations that modify shipment

**Implementation**:
1. **Safety Check**: Validates context has shipment ID and organization ID
2. **Data Refresh**: Fetches fresh shipment data from database
3. **Status-Dependent Updates**: Re-evaluates business rules that depend on customs status:
   - `canGenerateCAD`, `canGenerateRNSProof`
   - `isReleased`, `isSubmitted`
4. **Display Updates**: Updates formatted status and smart template context
5. **Change Logging**: Logs status changes for debugging

**Critical Use Case**: Called after shipment submission to reflect new customs status

**Error Handling**: Catches errors but doesn't throw - continues with stale context

#### Method: injectServices
**Signature**: `injectServices(context: ShipmentContextWithServices, services: Partial<ShipmentContextWithServices["_services"]>): void`
**Purpose**: Injects services into context for intent handlers
**Usage**: Called by higher-level services that have access to REQUEST-scoped services

### Private Helper Methods

#### Fallback Methods
All fallback methods provide empty/default objects when safe evaluation fails:
- `getEmptyShipmentIdentifiers()`: Empty identifier object
- `getEmptyShippingInformation()`: Default shipping info with unknown status
- `getEmptyEtaInformation()`: Empty ETA information
- `getEmptyDocumentReceiptStatus()`: All documents marked as not received
- `getEmptyDocumentDataStatus()`: All document statuses marked as missing
- `getEmptyMissingFieldsAnalysis()`: Empty missing fields analysis
- `getEmptyTemplateContext()`: Complete empty template context
- `getEmptySmartTemplateContext()`: Empty smart template context

#### Smart Template Context Building
**Method: buildSmartTemplateContext**
**Purpose**: Builds template-ready context optimized for smart templates

**Implementation**:
- Maps customs status to response messages
- Determines transport mode from mode of transport
- Formats dates using moment.js
- Evaluates document availability
- Builds missing items list

**Helper Methods**:
- `getTransportMode()`: Maps transport mode to enum ("OCEAN", "AIR", "TRUCK")
- `formatEtaDate()`: Formats dates as "Month DD, YYYY"
- `buildDocumentStatus()`: Determines document receipt status from compliance
- `buildMissingItemsList()`: Builds list of missing items from compliance

## Design Patterns
- **Facade Pattern**: Provides simple interface over complex business logic
- **Dependency Injection**: Uses interface tokens for loose coupling
- **Safe Evaluation Pattern**: All operations wrapped in safe evaluation
- **Builder Pattern**: Constructs complex context objects step by step
- **Template Method Pattern**: Consistent fallback patterns across methods

## NestJS Integration
- **Injectable Service**: Available for dependency injection
- **Interface Token Resolution**: Uses custom injection tokens
- **Exception Handling**: Uses NestJS exception classes
- **Logging**: Integrated with NestJS Logger

## Error Handling Patterns
- **Input Validation**: Early validation with descriptive exceptions
- **Safe Operations**: All business logic wrapped in safe evaluation
- **Graceful Degradation**: Provides fallback values when operations fail
- **Error Context**: Includes shipment/organization IDs in error messages
- **Non-Breaking Refresh**: Refresh failures don't stop execution

## Performance Considerations
- **One-Time Evaluation**: Business rules computed once during context building
- **Parallel Data Fetching**: Fetches base data concurrently where possible
- **Cached Computations**: Context caches all computed values
- **Safe Async Operations**: Properly handles async business rule evaluations

## Security Considerations
- **Input Validation**: Validates all input parameters
- **Transaction Support**: Supports QueryRunner for transactional consistency
- **Safe Evaluation**: Prevents exceptions from leaking sensitive information
- **Service Injection**: Controlled service injection prevents unauthorized access

## Testing Considerations
- **Interface Mocking**: All dependencies are interfaces, easy to mock
- **Safe Evaluation Testing**: Can test fallback behavior by making operations fail
- **Context Validation**: Rich context objects provide comprehensive assertions
- **Service Injection Testing**: Can test service injection and usage patterns
- **Refresh Logic Testing**: Can test context refresh behavior after modifications