# customs-definitions.constants.ts Documentation

## Overview
- **Purpose**: Provides pure functions for customs status validation and business logic evaluation
- **Type**: TypeScript Constants and Utility Functions
- **Location**: `/apps/portal-api/src/agent-context/constants/customs-definitions.constants.ts`

## Dependencies
### External Dependencies
- `nest-modules`: Provides CustomsStatus enum and ValidateShipmentComplianceResponseDto type

## Implementation

### isSubmittedCustomsStatus Function
**Signature**: `isSubmittedCustomsStatus(status: string | null | undefined): boolean`
**Purpose**: Checks if a given customs status is considered 'submitted'
**Implementation**:
- Returns false for null/undefined status
- Checks if status matches any submitted status:
  - `CustomsStatus.ENTRY_SUBMITTED`
  - `CustomsStatus.ENTRY_ACCEPTED`
  - `CustomsStatus.EXAM`
  - `CustomsStatus.RELEASED`
  - `CustomsStatus.ACCOUNTING_COMPLETED`
**Side Effects**: None (pure function)

### isPendingCustomsStatus Function
**Signature**: `isPendingCustomsStatus(status: string | null | undefined): boolean`
**Purpose**: Checks if a customs status is in pending state
**Implementation**:
- Returns false for null/undefined status
- Checks if status matches any pending status:
  - `CustomsStatus.PENDING_COMMERCIAL_INVOICE`
  - `CustomsStatus.PENDING_CONFIRMATION`
  - `CustomsStatus.PENDING_ARRIVAL`
**Side Effects**: None (pure function)

### isLiveCustomsStatus Function
**Signature**: `isLiveCustomsStatus(status: string | null | undefined): boolean`
**Purpose**: Checks if a customs status is 'live' (ready for submission)
**Implementation**:
- Returns false for null/undefined status
- Returns true only if status equals `CustomsStatus.LIVE`
**Side Effects**: None (pure function)

### isReleasedCustomsStatus Function
**Signature**: `isReleasedCustomsStatus(status: string | null | undefined): boolean`
**Purpose**: Checks if a customs status indicates released goods
**Implementation**:
- Returns false for null/undefined status
- Checks if status matches any released status:
  - `CustomsStatus.RELEASED`
  - `CustomsStatus.ACCOUNTING_COMPLETED`
**Side Effects**: None (pure function)

### isSendCADReady Function
**Signature**: `isSendCADReady(status: string | null | undefined): boolean`
**Purpose**: Checks if a shipment is ready to have a CAD document sent
**Business Logic**: CAD documents are sent when the customs entry is accepted and beyond
**Implementation**:
- Returns false for null/undefined status
- Checks if status matches CAD-ready statuses:
  - `CustomsStatus.ENTRY_ACCEPTED`
  - `CustomsStatus.EXAM`
  - `CustomsStatus.RELEASED`
  - `CustomsStatus.ACCOUNTING_COMPLETED`
**Side Effects**: None (pure function)

### isSendRNSProofOfReleaseReady Function
**Signature**: `isSendRNSProofOfReleaseReady(status: string | null | undefined): boolean`
**Purpose**: Checks if a shipment is ready to have RNS proof of release sent
**Business Logic**: RNS proof of release is only available when goods are released
**Implementation**:
- Returns false for null/undefined status
- Checks if status matches release-ready statuses:
  - `CustomsStatus.RELEASED`
  - `CustomsStatus.ACCOUNTING_COMPLETED`
**Side Effects**: None (pure function)

### isReadyToSubmit Function
**Signature**: `isReadyToSubmit(compliance: ValidateShipmentComplianceResponseDto): boolean`
**Purpose**: Checks if a shipment is ready to submit based on compliance validation
**Implementation**:
- Validates compliance object has:
  - No missing commercial invoice (`!compliance.noCommercialInvoice`)
  - No missing fields (`(compliance.missingFields?.length ?? 0) === 0`)
  - No non-compliant invoices (`(compliance.nonCompliantInvoices?.length ?? 0) === 0`)
- Returns true only if all conditions are met
**Side Effects**: None (pure function)

## Design Patterns
- **Pure Functions**: All functions are side-effect free, enhancing testability and predictability
- **Null Safety**: Consistent null/undefined handling across all functions
- **Business Logic Encapsulation**: Complex status logic centralized in reusable functions
- **Type Safety**: Strong TypeScript typing with enum usage

## Status Categories

### Submission Lifecycle Statuses
1. **Pending**: PENDING_COMMERCIAL_INVOICE, PENDING_CONFIRMATION, PENDING_ARRIVAL
2. **Live**: LIVE (ready for submission)
3. **Submitted**: ENTRY_SUBMITTED, ENTRY_ACCEPTED, EXAM, RELEASED, ACCOUNTING_COMPLETED
4. **Released**: RELEASED, ACCOUNTING_COMPLETED

### Document Generation Readiness
1. **CAD Ready**: ENTRY_ACCEPTED, EXAM, RELEASED, ACCOUNTING_COMPLETED
2. **RNS Proof Ready**: RELEASED, ACCOUNTING_COMPLETED

## Business Rules
- **CAD Documents**: Available after entry acceptance
- **RNS Proof**: Only available after goods release
- **Submission Readiness**: Requires complete compliance (no missing documents/fields/invoices)
- **Status Progression**: Clear hierarchy from pending → live → submitted → released

## Error Handling Patterns
- **Graceful Degradation**: Returns false for invalid/null inputs rather than throwing
- **Safe Type Casting**: Uses type assertions with enum validation
- **Optional Chaining**: Safely handles undefined properties in compliance objects

## Security Considerations
- **Input Validation**: All functions validate input parameters
- **Type Safety**: Strong typing prevents invalid status comparisons
- **No Side Effects**: Pure functions prevent unintended state changes

## Testing Considerations
- **Unit Test Friendly**: Pure functions with predictable inputs/outputs
- **Edge Case Coverage**: Handles null, undefined, and invalid status values
- **Business Logic Verification**: Each function tests specific business rules
- **Compliance Testing**: isReadyToSubmit allows testing of complex compliance scenarios