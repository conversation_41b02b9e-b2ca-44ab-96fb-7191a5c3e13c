# shipment-services.adapter.ts Documentation

## Overview
- **Purpose**: Adapter that implements all core interfaces and contains existing business logic from original ShipmentContextService
- **Type**: NestJS Injectable Service
- **Location**: `/apps/portal-api/src/agent-context/services/shipment-services.adapter.ts`
- **NestJS Scope**: Singleton (default scope)

## Dependencies
### External Dependencies
- `@nestjs/common`: Injectable decorator, Logger, NotFoundException
- `@nestjs/core`: ContextIdFactory, ModuleRef for REQUEST-scoped service resolution
- `@nestjs/typeorm`: InjectDataSource decorator
- `typeorm`: DataSource, QueryRunner for database operations
- `nest-modules`: Core entity types and DTOs

### Internal Dependencies
- Multiple service dependencies for REQUEST-scoped service resolution
- Business rule utilities from constants
- Email request generation utilities

## Implementation

### ResolvedRequestScopedServices Interface
**Purpose**: Defines interface for REQUEST-scoped services resolved via ModuleRef
**Services Included**:
- ShipmentService, ShipmentComplianceQueryService, ComplianceValidationService
- EntrySubmissionService, CustomStatusService, ImporterService
- RNSStatusChangeEmailSender, RnsProofService, EmailService, CommercialInvoiceService

### Class: ShipmentServicesAdapter
**Design Philosophy**: Contains all complexity in one place while implementing multiple interfaces
**Implemented Interfaces**: IShipmentDataProvider, IBusinessRuleEvaluator, IContextFormatter, IEntrySubmissionService, IShipmentService

#### Constructor Dependencies
- `ModuleRef`: For resolving REQUEST-scoped services
- `@InjectDataSource() DataSource`: For direct database operations

### IShipmentDataProvider Implementation

#### Method: fetchOrganization
**Signature**: `fetchOrganization(organizationId: number, queryRunner?: QueryRunner): Promise<Organization>`
**Implementation**:
- Uses queryRunner manager or dataSource manager
- Finds organization with FIND_ORGANIZATION_RELATIONS
- Throws NotFoundException if not found
**Side Effects**: Logs debug information

#### Method: fetchShipment  
**Signature**: `fetchShipment(shipmentId: number, queryRunner?: QueryRunner): Promise<Shipment>`
**Implementation**:
1. Finds basic shipment to get organization ID
2. Fetches organization for service resolution
3. Resolves REQUEST-scoped services
4. Uses ShipmentService to get full shipment data
**Side Effects**: Logs debug information, resolves services

#### Method: fetchCompliance
**Signature**: `fetchCompliance(shipmentId: number, queryRunner?: QueryRunner): Promise<ValidateShipmentComplianceResponseDto>`
**Implementation**:
1. Finds shipment to get organization
2. Resolves REQUEST-scoped services  
3. Uses ShipmentComplianceQueryService for compliance data
**Side Effects**: Logs debug information, resolves services

### IBusinessRuleEvaluator Implementation

#### Business Logic Methods
All methods implement customs automation business rules:

1. **canShipmentBeRushed**: Checks if shipment not submitted and compliance ready
2. **canGenerateCAD**: Uses isSendCADReady utility function
3. **canGenerateRNSProof**: Uses isSendRNSProofOfReleaseReady utility
4. **isCompliant**: Uses isReadyToSubmit utility function
5. **isReleased**: Uses isReleasedCustomsStatus utility
6. **isSubmitted**: Uses isShipmentSubmittedDirect helper
7. **canBeModified**: Inverse of isSubmitted
8. **isEntryUploaded**: Async method using ComplianceValidationService
9. **canUpdateEntry**: Async method using ComplianceValidationService  
10. **isAllDocsReceived**: Complex logic checking document status and compliance
11. **determineDocumentCompleteness**: Status-based document completeness check

#### Blocking Reason Methods
Provide detailed explanations for why actions are blocked:
- **getRushBlockingReason**: Explains rush blocking (submitted, missing CI, fields, compliance)
- **getCADBlockingReason**: Explains CAD unavailability
- **getRNSBlockingReason**: Explains RNS proof unavailability

### IEntrySubmissionService Implementation  

#### Method: attemptShipmentSubmission
**Signature**: Complex method with comprehensive submission workflow
**Purpose**: Attempts to submit shipment for customs processing following established workflow

**Implementation Steps**:
1. **Input Validation**: Validates shipment exists
2. **Status Check**: Prevents submission if already in final status
3. **Service Resolution**: Resolves REQUEST-scoped services
4. **Compliance Validation**: Gets and validates shipment compliance
5. **Transaction Management**: Creates or uses provided QueryRunner
6. **Submission Process**: Calls CustomStatusService.processShipmentForCustomsStatus
7. **Status Updates**: Updates shipment status both in database and memory
8. **Transaction Handling**: Commits/rollbacks based on success
9. **Logging**: Comprehensive logging throughout process

**Transaction Pattern**:
- If QueryRunner provided: Uses existing transaction (caller manages)
- If QueryRunner not provided: Creates and manages own transaction
- Prevents nested transaction issues

**Error Handling**: Comprehensive error handling with transaction rollback

### IContextFormatter Implementation

#### Core Formatting Methods
1. **formatCustomsStatus**: Maps status to user-friendly messages
2. **buildShipmentIdentifiers**: Extracts and formats shipment identifiers
3. **buildEtaInformation**: Formats ETA and port information
4. **buildShippingInformation**: Builds shipping status information
5. **buildDocumentDataStatus**: Complex document status analysis
6. **buildMissingFieldsAnalysis**: Categorizes missing fields by type
7. **buildTemplateContext**: Comprehensive context for template rendering
8. **buildDocumentReceiptStatus**: Document receipt status analysis
9. **buildMissingFieldsStatus**: Formatted missing fields list
10. **formatComplianceErrors**: Formats compliance errors using adapter

#### Complex Business Logic
**Document Status Analysis**: Sophisticated logic for determining document completeness based on:
- Transport mode (Ocean, Air, Land)
- Required fields per document type
- Missing field analysis
- Compliance validation results

**Missing Fields Categorization**: Groups missing fields into:
- Identifiers (CCN, HBL, Transaction Number)
- Measurements (Weight, Quantity with UOMs)  
- Timing (ETD, ETA Port, ETA Destination)
- Locations (Port codes, sub-locations)

### IShipmentService Implementation

#### Method: editShipment
**Signature**: `editShipment(shipmentId: number, updateData: Record<string, any>, queryRunner?: QueryRunner): Promise<Shipment>`
**Implementation**:
1. Fetches shipment and organization
2. Resolves REQUEST-scoped services
3. Delegates to ShipmentService.editShipment
**Error Handling**: Comprehensive logging and error propagation

### Private Helper Methods

#### SERVICE RESOLUTION
**resolveRequestScopedServices**: Critical method for resolving REQUEST-scoped services
- Creates context ID for request scoping
- Registers request with organization context
- Resolves all required services in parallel
- Returns ResolvedRequestScopedServices interface

#### BUSINESS LOGIC HELPERS
- **isShipmentSubmittedDirect**: Direct status-based submission check
- **hasDocumentRelatedMissingFields**: Checks for document-related missing fields
- **hasDocumentComplianceIssues**: Identifies compliance issues
- **Document validation methods**: Transport-mode-specific document validation

#### FORMATTING HELPERS
- **Date formatting**: Consistent date formatting for templates
- **Field mapping**: Maps internal field names to user-friendly descriptions
- **Status context building**: Builds context-aware status messages
- **Document type mapping**: Maps transport modes to required document types

## Design Patterns
- **Adapter Pattern**: Adapts complex business logic to simple interfaces
- **Facade Pattern**: Provides unified interface over multiple services
- **Template Method Pattern**: Consistent patterns for data formatting
- **Strategy Pattern**: Different logic based on transport mode
- **Service Locator Pattern**: Dynamic service resolution based on organization

## NestJS Integration
- **REQUEST Scope Handling**: Sophisticated handling of REQUEST-scoped services
- **Transaction Management**: Flexible transaction handling with QueryRunner
- **Dependency Injection**: Uses ModuleRef for dynamic service resolution
- **Context Management**: Proper request context management for multi-tenant scenarios

## Error Handling Patterns
- **Graceful Degradation**: Async methods return false on errors rather than throwing
- **Comprehensive Logging**: Detailed logging throughout all operations
- **Transaction Safety**: Proper transaction rollback on errors
- **Context Preservation**: Maintains request context through service resolution

## Performance Considerations
- **Service Caching**: Resolves services once per operation where possible
- **Parallel Operations**: Uses Promise.all for concurrent service resolution
- **Database Efficiency**: Uses QueryRunner for transaction consistency
- **Memory Management**: Proper cleanup of QueryRunner connections

## Security Considerations
- **Organization Scoping**: All operations scoped to specific organizations
- **Request Context**: Maintains proper security context through service resolution
- **Transaction Isolation**: Uses transactions for data consistency
- **Input Validation**: Validates inputs before processing

## Testing Considerations
- **Interface Implementation**: Each interface can be tested independently
- **Service Mocking**: Can mock individual resolved services
- **Transaction Testing**: Can test with and without QueryRunner
- **Error Scenario Testing**: Comprehensive error handling enables thorough testing
- **Business Logic Testing**: Complex business rules are well-encapsulated for testing