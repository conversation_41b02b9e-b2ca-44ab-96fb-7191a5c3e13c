# safe-evaluation.util.ts Documentation

## Overview
- **Purpose**: Utility for safely evaluating operations with fallback values and logging
- **Type**: NestJS Injectable Service
- **Location**: `/apps/portal-api/src/agent-context/utils/safe-evaluation.util.ts`
- **NestJS Scope**: Singleton (default scope)

## Dependencies
### External Dependencies
- `@nestjs/common`: Provides `@Injectable` decorator and `Logger` class

## Implementation

### Class: SafeEvaluationUtil
**Purpose**: Prevents context build failures from individual rule evaluation errors
**Design Philosophy**: Fail-safe pattern that ensures system resilience by providing fallback values when operations fail

#### Properties
- `logger: Logger`: Private readonly logger instance for error reporting

#### Method: evaluate<T>
**Signature**: `evaluate<T>(operation: () => T, defaultValue: T, context: string): T`
**Purpose**: Safely evaluates a synchronous operation with fallback and logging

**Parameters**:
- `operation: () => T`: Function to execute safely
- `defaultValue: T`: Fallback value if operation fails
- `context: string`: Context description for logging purposes

**Implementation**:
1. Executes the provided operation in a try-catch block
2. Returns operation result if successful
3. On failure:
   - Logs warning with context and error message
   - Returns the provided default value

**Side Effects**: 
- Logs warnings when operations fail
- Prevents exceptions from propagating

**Use Cases**:
- Business rule evaluation that shouldn't break context building
- Safe property access on potentially undefined objects
- Calculations that might fail due to missing data

#### Method: evaluateAsync<T>
**Signature**: `evaluateAsync<T>(operation: () => Promise<T>, defaultValue: T, context: string): Promise<T>`
**Purpose**: Safely evaluates an asynchronous operation with fallback and logging

**Parameters**:
- `operation: () => Promise<T>`: Async function to execute safely
- `defaultValue: T`: Fallback value if operation fails
- `context: string`: Context description for logging purposes

**Implementation**:
1. Awaits the provided async operation in a try-catch block
2. Returns operation result if successful
3. On failure:
   - Logs warning with context and error message
   - Returns the provided default value

**Side Effects**:
- Logs warnings when async operations fail
- Prevents promise rejections from propagating

**Use Cases**:
- Safe database queries during context building
- External API calls that might fail
- Async business rule evaluations

## Design Patterns
- **Fail-Safe Pattern**: Ensures operations never fail catastrophically
- **Template Method Pattern**: Common error handling logic for sync/async operations
- **Decorator Pattern**: Wraps operations with safety mechanisms
- **Logging Pattern**: Consistent logging of failures for debugging

## NestJS Integration
- **Injectable Service**: Can be injected into other services via dependency injection
- **Singleton Scope**: Single instance shared across the application
- **Logger Integration**: Uses NestJS Logger for consistent log formatting

## Dependency Injection Token
- **Class Token**: Injected by class name `SafeEvaluationUtil`
- **Usage**: `constructor(private readonly safeEval: SafeEvaluationUtil) {}`

## Error Handling Strategy
- **Non-Breaking Errors**: Converts exceptions to logged warnings
- **Graceful Degradation**: Provides sensible defaults when operations fail
- **Debugging Support**: Logs original error messages for troubleshooting
- **Context Preservation**: Includes operation context in log messages

## Logging Format
**Warning Format**: `"Business rule evaluation failed for {context}, using fallback: {error.message}"`
**Async Warning Format**: `"Async business rule evaluation failed for {context}, using fallback: {error.message}"`

## Performance Considerations
- **Minimal Overhead**: Try-catch blocks have negligible performance impact
- **No Memory Leaks**: Proper error handling prevents uncaught exceptions
- **Efficient Logging**: Only logs on failure, not success

## Security Considerations
- **Safe Execution**: Prevents malicious operations from crashing the system
- **Information Leakage**: Only logs error messages, not sensitive data
- **Controlled Failures**: Converts uncontrolled exceptions to controlled defaults

## Testing Considerations
- **Mock Operations**: Easy to test with mock functions that throw errors
- **Fallback Verification**: Can verify default values are returned on failure
- **Logging Verification**: Can test that appropriate warnings are logged
- **Generic Testing**: Type-safe testing with different return types

## Usage Examples

### Business Rule Evaluation
```typescript
const canRush = this.safeEval.evaluate(
  () => this.businessRuleEvaluator.canShipmentBeRushed(shipment, compliance),
  false,
  'canShipmentBeRushed'
);
```

### Async Database Operation
```typescript
const organization = await this.safeEval.evaluateAsync(
  () => this.organizationService.findById(orgId),
  null,
  'fetchOrganization'
);
```

### Safe Property Access
```typescript
const containerCount = this.safeEval.evaluate(
  () => shipment.containers.length,
  0,
  'containerCount'
);
```