#!/usr/bin/env node

const readline = require("readline");
const fs = require("fs");
const path = require("path");
const http = require("http");

// Configuration
const API_BASE_URL = "http://localhost:5001";
const DEFAULT_LOGIN_EMAIL = "<EMAIL>";
const DEFAULT_LOGIN_PASSWORD = "password";

// Default email values
const DEFAULTS = {
  fromEmail: "<EMAIL>",
  fromName: "Sylvester Test Client",
  toEmail: "<EMAIL>",
  subject: "Test Email - Document Processing Request",
  messageBody: "Please process customs documentation for our shipment. All required documents are attached."
};

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Utility function to prompt user input
function prompt(question, defaultValue = "") {
  return new Promise((resolve) => {
    const displayDefault = defaultValue ? ` (default: ${defaultValue})` : "";
    rl.question(`${question}${displayDefault}: `, (answer) => {
      resolve(answer.trim() || defaultValue);
    });
  });
}

// Utility function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = "";
      res.on("data", (chunk) => (body += chunk));
      res.on("end", () => {
        try {
          const response = {
            statusCode: res.statusCode,
            data: body ? JSON.parse(body) : null,
            headers: res.headers
          };
          resolve(response);
        } catch (e) {
          reject(new Error(`Failed to parse response: ${e.message}`));
        }
      });
    });

    req.on("error", reject);

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// Function to read file and convert to base64
function fileToBase64(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    const fileBuffer = fs.readFileSync(filePath);
    const base64String = fileBuffer.toString("base64");
    const fileName = path.basename(filePath);

    return {
      fileName: fileName,
      mimeType: "application/pdf",
      b64Data: base64String
    };
  } catch (error) {
    throw new Error(`Failed to process file ${filePath}: ${error.message}`);
  }
}

// Function to authenticate and get access token
async function authenticate() {
  console.log("\n🔐 Authenticating with portal-api...");

  const loginData = {
    loginMethod: "email",
    email: DEFAULT_LOGIN_EMAIL,
    password: DEFAULT_LOGIN_PASSWORD
  };

  const options = {
    hostname: "localhost",
    port: 5001,
    path: "/auth/login",
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Content-Length": Buffer.byteLength(JSON.stringify(loginData))
    }
  };

  try {
    const response = await makeRequest(options, loginData);

    if (response.statusCode !== 200 && response.statusCode !== 201) {
      throw new Error(`Authentication failed: ${response.statusCode} - ${JSON.stringify(response.data)}`);
    }

    if (!response.data || !response.data.accessToken) {
      throw new Error("No access token received from authentication");
    }

    console.log("✅ Authentication successful");
    return response.data.accessToken;
  } catch (error) {
    throw new Error(`Authentication failed: ${error.message}`);
  }
}

// Function to send test email
async function sendTestEmail(accessToken, emailData) {
  console.log("\n📧 Sending test email...");

  const options = {
    hostname: "localhost",
    port: 5001,
    path: "/emails/test/simulate-incoming-test-email",
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
      "Content-Length": Buffer.byteLength(JSON.stringify(emailData))
    }
  };

  try {
    const response = await makeRequest(options, emailData);

    if (response.statusCode !== 200 && response.statusCode !== 201) {
      throw new Error(`Failed to send test email: ${response.statusCode} - ${JSON.stringify(response.data)}`);
    }

    console.log("✅ Test email sent successfully");
    console.log(`📧 Email ID: ${response.data.id}`);
    console.log(`🏢 Organization: ${response.data.organization?.name || "Unknown"}`);
    console.log(`📊 Status: ${response.data.status}`);

    return response.data;
  } catch (error) {
    throw new Error(`Failed to send test email: ${error.message}`);
  }
}

// Main function
async function main() {
  console.log("🚀 Portal-API Test Email Sender");
  console.log("=====================================\n");

  try {
    // Collect email details
    console.log("📝 Email Details:");
    const fromEmail = await prompt("From email address", DEFAULTS.fromEmail);
    const fromName = await prompt("From name", DEFAULTS.fromName);
    const toEmail = await prompt("To email address (inbox email)", DEFAULTS.toEmail);
    const subject = await prompt("Subject", DEFAULTS.subject);
    const messageBody = await prompt("Message body", DEFAULTS.messageBody);

    // Collect PDF attachments
    console.log("\n📎 PDF Attachments:");
    console.log("Enter file paths for PDF attachments (press Enter on blank line to finish):");

    const attachments = [];
    let fileIndex = 1;

    while (true) {
      const filePath = await prompt(`PDF file ${fileIndex} path`);

      if (!filePath) {
        break; // Empty input, stop collecting files
      }

      try {
        const attachment = fileToBase64(filePath);
        attachments.push(attachment);
        console.log(
          `✅ Added: ${attachment.fileName} (${Math.round((attachment.b64Data.length * 0.75) / 1024)} KB)`
        );
        fileIndex++;
      } catch (error) {
        console.log(`❌ Error: ${error.message}`);
        console.log("Please try again or press Enter to skip.");
      }
    }

    console.log(`\n📊 Summary: ${attachments.length} attachment(s) added`);

    // Prepare email data
    const emailData = {
      scenario: "shipment-inquiry",
      from: [
        {
          address: fromEmail,
          name: fromName
        }
      ],
      inboxEmail: toEmail,
      subject: subject,
      messageBody: messageBody,
      attachments: attachments.length > 0 ? attachments : undefined // Use scenario defaults if no attachments
    };

    console.log("\n📋 Email Data Preview:");
    console.log(`From: ${fromName} <${fromEmail}>`);
    console.log(`To: ${toEmail}`);
    console.log(`Subject: ${subject}`);
    console.log(`Message: ${messageBody.substring(0, 100)}${messageBody.length > 100 ? "..." : ""}`);
    console.log(`Attachments: ${attachments.length} file(s)`);

    // Confirm before sending
    const confirm = await prompt("\n❓ Send this test email? (y/N)", "n");
    if (confirm.toLowerCase() !== "y" && confirm.toLowerCase() !== "yes") {
      console.log("❌ Cancelled by user");
      return;
    }

    // Authenticate
    const accessToken = await authenticate();

    // Send test email
    const result = await sendTestEmail(accessToken, emailData);

    console.log("\n🎉 Test email processing initiated successfully!");
    console.log("\n💡 Tips:");
    console.log("- Check the portal-api logs for processing details");
    console.log("- Response emails will be sent to the sender address");
    console.log("- You can monitor the email status via the API");
  } catch (error) {
    console.error(`\n❌ Error: ${error.message}`);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Handle process termination
process.on("SIGINT", () => {
  console.log("\n\n👋 Goodbye!");
  rl.close();
  process.exit(0);
});

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error(`\n💥 Unexpected error: ${error.message}`);
    process.exit(1);
  });
}
