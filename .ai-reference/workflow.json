{"workflow": {"id": "linear_debug", "label": "Linear Debug Workflow", "description": "A simple 2-node linear workflow for debugging.", "nodes": [{"id": "node_a", "type": "javascript", "label": "Start Node (A)", "config": {"code": "console.log('Executing Node A'); return { message: 'Hello from Node A', value: 123 };"}}, {"id": "node_b", "type": "javascript", "label": "End Node (B)", "inputMapSpec": {"message": "node_a.message", "value": "node_a.value"}, "config": {"code": "console.log('Executing Node B with input:', input); const finalMessage = `Received from A: ${input.message}, Value: ${input.value}`; console.log('Final message:', finalMessage); return { finalResult: finalMessage };"}}], "edges": [{"id": "edge_a_to_b", "from": "node_a", "to": "node_b"}], "startNode": "node_a"}, "context": {"initialInput": "Some initial context if needed"}, "waitForCompletion": true, "config": {"timeout": 15000}}