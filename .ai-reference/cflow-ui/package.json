{"name": "cflow-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@codemirror/lang-javascript": "^6.2.3", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "^6.4.4", "@mui/material": "^6.4.4", "@uiw/react-codemirror": "^4.23.8", "react": "^19.0.0", "react-dom": "^19.0.0", "reactflow": "^11.11.4"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}