import React, { useState, use<PERSON><PERSON>back, useRef, useEffect } from "react";
import <PERSON>act<PERSON>low, {
  Node,
  Edge,
  Connection,
  addEdge,
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  Panel,
  NodeTypes,
  ReactFlowProvider,
  EdgeMouseHandler,
  NodeMouseHandler,
  getConnectedEdges,
  getOutgoers,
} from "reactflow";
import "reactflow/dist/style.css";
import NodeConfigPanel from "./NodeConfigPanel";
import CustomNode from "./CustomNode";

// Define node types with their specific data structure
interface NodeData {
  label: string;
  code?: string;
  type?: "start" | "process" | "end";
  isExecuting?: boolean;
}

const nodeTypes: NodeTypes = {
  custom: CustomNode,
};

const initialNodes: Node<NodeData>[] = [
  {
    id: "1",
    type: "custom",
    data: {
      label: "Start",
      code: "// Start node code here",
      type: "start",
    },
    position: { x: 250, y: 5 },
  },
  {
    id: "2",
    type: "custom",
    data: {
      label: "Process",
      code: "// Process node code here",
      type: "process",
    },
    position: { x: 100, y: 100 },
  },
  {
    id: "3",
    type: "custom",
    data: {
      label: "End",
      code: "// End node code here",
      type: "end",
    },
    position: { x: 250, y: 250 },
  },
];

const initialEdges: Edge[] = [
  { id: "e1-2", source: "1", target: "2", animated: true, type: "smoothstep" },
  { id: "e2-3", source: "2", target: "3", type: "smoothstep" },
];

const WorkflowEditor = () => {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState<Node<NodeData> | null>(null);
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    show: boolean;
  }>({ x: 0, y: 0, show: false });
  const [nodeContextMenu, setNodeContextMenu] = useState<{
    x: number;
    y: number;
    show: boolean;
    nodeId: string | null;
  }>({ x: 0, y: 0, show: false, nodeId: null });
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isRunning, setIsRunning] = useState(false);

  const onConnect = useCallback(
    (params: Connection) => {
      setEdges((eds) => addEdge({ ...params, type: "smoothstep" }, eds));
    },
    [setEdges],
  );

  const handleNodeUpdate = (updatedNode: Node<NodeData>) => {
    setNodes((nds) =>
      nds.map((node) => (node.id === updatedNode.id ? updatedNode : node)),
    );
    setSelectedNode(null);
  };

  const handleSave = () => {
    const workflowJSON = JSON.stringify({ nodes, edges }, null, 2);
    const blob = new Blob([workflowJSON], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "workflow.json";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleLoadWorkflowClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files && event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const text = e.target?.result as string;
          const { nodes: loadedNodes, edges: loadedEdges } = JSON.parse(text);
          setNodes(loadedNodes);
          setEdges(loadedEdges);
        } catch (err) {
          console.error(err);
          alert("Invalid JSON file");
        }
      };
      reader.readAsText(file);
    }
  };

  const handleAddNode = () => {
    const newId = Date.now().toString();
    const newNode: Node<NodeData> = {
      id: newId,
      type: "custom",
      data: {
        label: `Node ${newId}`,
        code: "// New node code here",
        type: "process",
      },
      position: { x: Math.random() * 500, y: Math.random() * 500 },
    };
    setNodes((nds) => [...nds, newNode]);
  };

  // Handle edge deletion
  const onEdgeDelete = useCallback(
    (edge: Edge) => {
      setEdges((eds) => eds.filter((e) => e.id !== edge.id));
      setSelectedEdge(null);
      setContextMenu({ x: 0, y: 0, show: false });
    },
    [setEdges],
  );

  // Handle edge selection
  const onEdgeClick: EdgeMouseHandler = useCallback((event, edge) => {
    event.preventDefault();
    setSelectedEdge(edge);
  }, []);

  // Handle edge context menu
  const onEdgeContextMenu: EdgeMouseHandler = useCallback((event, edge) => {
    event.preventDefault();
    setSelectedEdge(edge);
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      show: true,
    });
  }, []);

  // Handle node deletion
  const onNodeDelete = useCallback(
    (nodeId: string) => {
      const nodeToDelete = nodes.find((n) => n.id === nodeId);

      // Prevent deletion of Start and End nodes
      if (
        nodeToDelete?.data.type === "start" ||
        nodeToDelete?.data.type === "end"
      ) {
        return;
      }

      // Remove connected edges first
      const connectedEdges = getConnectedEdges([nodeToDelete!], edges);
      setEdges((eds) =>
        eds.filter((e) => !connectedEdges.find((ce) => ce.id === e.id)),
      );

      // Remove the node
      setNodes((nds) => nds.filter((n) => n.id !== nodeId));
      setNodeContextMenu({ x: 0, y: 0, show: false, nodeId: null });
    },
    [nodes, edges, setNodes, setEdges],
  );

  // Handle node context menu
  const onNodeContextMenu: NodeMouseHandler = useCallback((event, node) => {
    event.preventDefault();
    setNodeContextMenu({
      x: event.clientX,
      y: event.clientY,
      show: true,
      nodeId: node.id,
    });
  }, []);

  // Update keyboard event handler to include node deletion
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Delete") {
        if (selectedEdge) {
          onEdgeDelete(selectedEdge);
        } else if (selectedNode) {
          onNodeDelete(selectedNode.id);
        }
      } else if (event.key === "Escape") {
        setContextMenu({ x: 0, y: 0, show: false });
        setNodeContextMenu({ x: 0, y: 0, show: false, nodeId: null });
        setSelectedEdge(null);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [selectedEdge, selectedNode, onEdgeDelete, onNodeDelete]);

  // Update click outside handler to include node context menu
  useEffect(() => {
    const handleClick = () => {
      setContextMenu({ x: 0, y: 0, show: false });
      setNodeContextMenu({ x: 0, y: 0, show: false, nodeId: null });
    };

    if (contextMenu.show || nodeContextMenu.show) {
      window.addEventListener("click", handleClick);
      return () => window.removeEventListener("click", handleClick);
    }
  }, [contextMenu.show, nodeContextMenu.show]);

  // Function to get all execution paths
  const getAllExecutionPaths = useCallback(() => {
    const startNode = nodes.find((node) => node.data.type === "start");
    if (!startNode) return [];

    const paths: Node<NodeData>[][] = [];

    const traverse = (
      currentNode: Node<NodeData>,
      currentPath: Node<NodeData>[],
    ) => {
      // Add current node to path
      currentPath.push(currentNode);

      // Get all outgoing nodes
      const outgoers = getOutgoers(currentNode, nodes, edges);

      if (outgoers.length === 0) {
        // End of path reached, save the path
        paths.push([...currentPath]);
      } else {
        // Continue traversing each outgoing path
        for (const nextNode of outgoers) {
          // Prevent infinite loops by checking if we've visited this node in the current path
          if (!currentPath.find((n) => n.id === nextNode.id)) {
            traverse(nextNode, [...currentPath]);
          }
        }
      }
    };

    // Start traversal from the start node
    traverse(startNode, []);
    return paths;
  }, [nodes, edges]);

  // Function to handle workflow execution
  const handleRun = useCallback(async () => {
    if (isRunning) return;
    setIsRunning(true);

    // Get all possible execution paths
    const executionPaths = getAllExecutionPaths();

    // Reset all nodes' execution state
    setNodes((nodes) =>
      nodes.map((node) => ({
        ...node,
        data: { ...node.data, isExecuting: false },
      })),
    );

    // Track which nodes are currently executing
    const executingNodes = new Set<string>();

    // Execute all paths concurrently
    await Promise.all(
      executionPaths.map(async (path) => {
        for (const node of path) {
          // Add node to currently executing set
          executingNodes.add(node.id);

          // Update all currently executing nodes
          setNodes((nodes) =>
            nodes.map((n) => ({
              ...n,
              data: {
                ...n.data,
                isExecuting: executingNodes.has(n.id),
              },
            })),
          );

          // Wait for 1 second
          await new Promise((resolve) => setTimeout(resolve, 1000));

          // Remove node from currently executing set
          executingNodes.delete(node.id);
        }
      }),
    );

    // Reset execution state
    setNodes((nodes) =>
      nodes.map((node) => ({
        ...node,
        data: { ...node.data, isExecuting: false },
      })),
    );

    setIsRunning(false);
  }, [isRunning, setNodes, getAllExecutionPaths]);

  return (
    <ReactFlowProvider>
      <div style={{ width: "100%", height: "100%" }}>
        <input
          type="file"
          accept=".json"
          ref={fileInputRef}
          onChange={handleFileChange}
          style={{ display: "none" }}
        />
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onNodeDoubleClick={(_, node) => setSelectedNode(node)}
          onConnect={onConnect}
          onEdgeClick={onEdgeClick}
          onEdgeContextMenu={onEdgeContextMenu}
          onNodeContextMenu={onNodeContextMenu}
          nodeTypes={nodeTypes}
          fitView
          style={{ background: "#f8f9fa" }}
          defaultEdgeOptions={{ type: "smoothstep" }}
        >
          <Panel position="top-left" className="workflow-buttons">
            <button className="workflow-button" onClick={handleSave}>
              Save Workflow
            </button>
            <button
              className="workflow-button"
              onClick={handleLoadWorkflowClick}
            >
              Load Workflow
            </button>
            <button className="workflow-button" onClick={handleAddNode}>
              Add Node
            </button>
          </Panel>
          <MiniMap />
          <Controls />
          <Background color="#aaa" gap={16} />
        </ReactFlow>
        {contextMenu.show && (
          <div
            style={{
              position: "fixed",
              left: contextMenu.x,
              top: contextMenu.y,
              background: "white",
              padding: "8px",
              borderRadius: "4px",
              boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
              zIndex: 1000,
            }}
          >
            <button
              className="workflow-button"
              onClick={() => selectedEdge && onEdgeDelete(selectedEdge)}
              style={{ padding: "4px 8px", fontSize: "12px" }}
            >
              Delete Connection
            </button>
          </div>
        )}
        {nodeContextMenu.show && nodeContextMenu.nodeId && (
          <div
            style={{
              position: "fixed",
              left: nodeContextMenu.x,
              top: nodeContextMenu.y,
              background: "white",
              padding: "8px",
              borderRadius: "4px",
              boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
              zIndex: 1000,
            }}
          >
            <button
              className="workflow-button"
              onClick={() => onNodeDelete(nodeContextMenu.nodeId!)}
              style={{
                padding: "4px 8px",
                fontSize: "12px",
                opacity:
                  nodes.find((n) => n.id === nodeContextMenu.nodeId)?.data
                    .type === "start" ||
                  nodes.find((n) => n.id === nodeContextMenu.nodeId)?.data
                    .type === "end"
                    ? "0.5"
                    : "1",
                cursor:
                  nodes.find((n) => n.id === nodeContextMenu.nodeId)?.data
                    .type === "start" ||
                  nodes.find((n) => n.id === nodeContextMenu.nodeId)?.data
                    .type === "end"
                    ? "not-allowed"
                    : "pointer",
              }}
            >
              Delete Node
            </button>
          </div>
        )}
        {selectedNode && (
          <NodeConfigPanel
            node={selectedNode}
            onUpdate={handleNodeUpdate}
            onClose={() => setSelectedNode(null)}
          />
        )}

        {/* Run button panel */}
        <Panel
          position="bottom-center"
          style={{
            marginBottom: "20px",
            background: "white",
            padding: "8px",
            borderRadius: "8px",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          }}
        >
          <button
            className="workflow-button"
            onClick={handleRun}
            disabled={isRunning}
            style={{
              background: isRunning ? "#9CA3AF" : "#10B981",
              color: "white",
              minWidth: "120px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              gap: "8px",
            }}
          >
            {isRunning ? "Running..." : "Run"}
          </button>
        </Panel>
      </div>
    </ReactFlowProvider>
  );
};

export default WorkflowEditor;
