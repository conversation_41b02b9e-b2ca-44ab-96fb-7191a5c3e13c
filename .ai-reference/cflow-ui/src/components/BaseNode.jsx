import React from "react";
import { NODE_COLORS, SPACING } from "../constants";

const BaseNode = ({ nodeType, children, style, ...props }) => {
  const colors = NODE_COLORS[nodeType] || NODE_COLORS.process;
  const nodeStyle = {
    backgroundColor: colors.background,
    border: `2px solid ${colors.border}`,
    padding: SPACING.padding,
    borderRadius: "4px",
    ...style,
  };

  return (
    <div style={nodeStyle} {...props}>
      {children}
    </div>
  );
};

export default BaseNode;
