import React, { memo } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "reactflow";
import BaseNode from "./BaseNode";

interface CustomNodeData {
  label: string;
  code?: string;
  type?: "start" | "process" | "end";
  isExecuting?: boolean;
}

const CustomNode = ({
  data,
  isConnectable,
  selected,
}: NodeProps<CustomNodeData>) => {
  return (
    <BaseNode
      nodeType={data.type || "process"}
      className={`custom-node ${selected ? "selected" : ""} ${
        data.isExecuting ? "executing" : ""
      }`}
      data-type={data.type || "process"}
    >
      {/* Left handle */}
      <Handle
        type="target"
        position={Position.Left}
        isConnectable={isConnectable}
        className="custom-handle target"
      />

      {/* Node content */}
      <div className="custom-node-content">
        <div className="node-label">{data.label}</div>
        {data.code && (
          <div className="node-code-indicator">
            <span>⚡</span>
          </div>
        )}
      </div>

      {/* Right handle */}
      <Handle
        type="source"
        position={Position.Right}
        isConnectable={isConnectable}
        className="custom-handle source"
      />
    </BaseNode>
  );
};

export default memo(CustomNode);
