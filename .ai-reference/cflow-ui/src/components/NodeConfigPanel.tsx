import React, { useState } from "react";
import { Node } from "reactflow";
import Code<PERSON>irror from "@uiw/react-codemirror";
import { javascript } from "@codemirror/lang-javascript";

interface NodeData {
  label: string;
  code?: string;
}

interface NodeConfigPanelProps {
  node: Node<NodeData>;
  onUpdate: (updatedNode: Node<NodeData>) => void;
  onClose: () => void;
}

const NodeConfigPanel: React.FC<NodeConfigPanelProps> = ({
  node,
  onUpdate,
  onClose,
}) => {
  const [label, setLabel] = useState(node.data.label || "");
  const [code, setCode] = useState(node.data.code || "");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdate({
      ...node,
      data: {
        ...node.data,
        label,
        code,
      },
    });
  };

  return (
    <div className="node-config-panel">
      <div className="panel-header">
        <h3>Configure Node</h3>
        <button className="close-button" onClick={onClose}>
          ×
        </button>
      </div>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="node-label">Label:</label>
          <input
            id="node-label"
            type="text"
            value={label}
            onChange={(e) => setLabel(e.target.value)}
            className="input-field"
          />
        </div>
        <div className="form-group">
          <label htmlFor="node-code">Code:</label>
          <CodeMirror
            value={code}
            height="200px"
            theme="dark"
            extensions={[javascript()]}
            onChange={(value) => setCode(value)}
            className="code-editor"
          />
        </div>
        <div className="button-group">
          <button type="submit" className="save-button">
            Save
          </button>
          <button type="button" onClick={onClose} className="cancel-button">
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default NodeConfigPanel;
