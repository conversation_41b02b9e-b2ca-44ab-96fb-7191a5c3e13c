import { Node, Edge } from "reactflow";

export interface Workflow {
  nodes: Node<unknown>[];
  edges: Edge[];
}

export function saveWorkflowToFile(
  workflow: Workflow,
  fileName: string = "workflow.json",
): void {
  const workflowJSON = JSON.stringify(workflow, null, 2);
  const blob = new Blob([workflowJSON], { type: "application/json" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

export function loadWorkflowFromFile(file: File): Promise<Workflow> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target?.result as string;
        const parsedData = JSON.parse(text);
        resolve(parsedData);
      } catch (error) {
        reject(error);
      }
    };
    reader.onerror = (error) => reject(error);
    reader.readAsText(file);
  });
}
