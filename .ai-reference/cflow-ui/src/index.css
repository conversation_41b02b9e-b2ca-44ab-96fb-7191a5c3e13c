/* Material Icons */
@import url("https://fonts.googleapis.com/icon?family=Material+Icons");

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: #213547;
  background-color: #ffffff;
}

body {
  margin: 0;
  padding: 0;
  width: 100vw;
  height: 100vh;
}

#root {
  width: 100%;
  height: 100%;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* ReactFlow Styles */
.react-flow__node {
  border: none !important;
  padding: 0 !important;
  border-radius: 0 !important;
  background: transparent !important;
  box-shadow: none !important;
}

.react-flow__node-input {
  background-color: #e6f3ff;
  border-color: #0077ff;
}

.react-flow__node-output {
  background-color: #f3ffe6;
  border-color: #00ff77;
}

.react-flow__node-default {
  background-color: #fff6e6;
  border-color: #ffa500;
}

.react-flow__handle {
  width: 8px;
  height: 8px;
  background-color: #1a192b;
  border-radius: 50%;
}

.react-flow__handle-top {
  top: -4px;
}

.react-flow__handle-bottom {
  bottom: -4px;
}

.workflow-buttons {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 10;
  display: flex;
  gap: 10px;
}

.workflow-button {
  background-color: #1a192b;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.workflow-button:hover {
  background-color: #2a293b;
}

/* Make the canvas fill the viewport */
.react-flow-wrapper {
  width: 100%;
  height: 100vh;
  background-color: #f8f9fa;
}

/* Node Config Panel Styles */
.node-config-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  padding: 20px;
  width: 400px;
  z-index: 1000;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.panel-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1a192b;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.close-button:hover {
  color: #333;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #1a192b;
  font-weight: 500;
}

.input-field {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.input-field:focus {
  outline: none;
  border-color: #1a192b;
}

.code-editor {
  border-radius: 4px;
  overflow: hidden;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.save-button,
.cancel-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: none;
}

.save-button {
  background-color: #1a192b;
  color: white;
}

.save-button:hover {
  background-color: #2a293b;
}

.cancel-button {
  background-color: #f0f0f0;
  color: #333;
}

.cancel-button:hover {
  background-color: #e0e0e0;
}

/* Custom Node Styles */
.custom-node {
  padding: 15px 20px;
  border-radius: 8px;
  min-width: 180px;
  font-size: 13px;
  text-align: left;
  border: 2px solid transparent;
  background-color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
  position: relative;
}

.custom-node:hover {
  box-shadow: 0 8px 12px -1px rgba(0, 0, 0, 0.15),
    0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-color: #6366f1;
}

.custom-node.selected {
  border-color: #6366f1;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.custom-node-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.node-label {
  font-weight: 600;
  color: #1a192b;
  font-size: 13px;
  letter-spacing: -0.01em;
}

.node-code-indicator {
  font-size: 14px;
  color: #6366f1;
  background-color: rgba(99, 102, 241, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

/* n8n-style connection points */
.custom-handle {
  width: 12px;
  height: 12px;
  background-color: white;
  border: 2px solid #6366f1;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.custom-handle.source {
  right: -6px;
}

.custom-handle.target {
  left: -6px;
}

/* Connection point hover effect */
.custom-handle:hover {
  background-color: #6366f1;
  border-color: #4f46e5;
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.2);
}

/* Edge styles */
.react-flow__edge-path {
  stroke: #6366f1;
  stroke-width: 2;
  stroke-dasharray: none;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #4f46e5;
  stroke-width: 3;
}

.react-flow__edge.animated .react-flow__edge-path {
  stroke-dasharray: 5, 5;
  animation: dashdraw 1s linear infinite;
}

@keyframes dashdraw {
  from {
    stroke-dashoffset: 10;
  }
}

/* Node type-specific styles */
.custom-node[data-type="start"] {
  background-color: #f0f9ff;
  border-color: #0ea5e9;
}

.custom-node[data-type="start"]:hover {
  border-color: #0284c7;
}

.custom-node[data-type="end"] {
  background-color: #f0fdf4;
  border-color: #22c55e;
}

.custom-node[data-type="end"]:hover {
  border-color: #16a34a;
}

.custom-node[data-type="process"] {
  background-color: #faf5ff;
  border-color: #a855f7;
}

.custom-node[data-type="process"]:hover {
  border-color: #9333ea;
}

/* Node execution state */
.custom-node.executing {
  border-color: #10b981 !important;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}
