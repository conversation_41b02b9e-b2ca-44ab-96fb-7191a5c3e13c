import { Injectable, Logger } from "@nestjs/common";
import * as nunjucks from "nunjucks";
import * as fs from "fs";
import * as nodePath from "path";

@Injectable()
export class PromptTemplateManager {
  private readonly logger = new Logger(PromptTemplateManager.name);
  private env: nunjucks.Environment;

  constructor() {
    try {
      this.logger.log("Initializing PromptTemplateManager");

      // Get the current working directory
      const cwd = process.cwd();
      this.logger.log(`Current working directory: ${cwd}`);

      // Setup Nunjucks with default templates path
      const templatesPath = nodePath.join(
        cwd,
        "src",
        "llm",
        "prompt-templates",
      );
      this.logger.log(`Templates path: ${templatesPath}`);

      // Ensure the templates directory exists
      try {
        if (!fs.existsSync(templatesPath)) {
          this.logger.log(`Creating templates directory: ${templatesPath}`);
          fs.mkdirSync(templatesPath, { recursive: true });
        }
      } catch (e) {
        this.logger.warn(
          `Could not check/create templates directory: ${e.message}`,
        );
        // Continue anyway, we'll use the fallback
      }

      try {
        this.env = nunjucks.configure(templatesPath, {
          autoescape: false,
          throwOnUndefined: false,
        });

        // Add JSON filter
        this.env.addFilter("tojson", function (obj: any, indent: number = 2) {
          return JSON.stringify(obj, null, indent);
        });

        this.logger.log("PromptTemplateManager initialized successfully");
      } catch (e) {
        this.logger.warn(
          `Failed to initialize Nunjucks environment: ${e.message}`,
        );
        throw e; // Propagate to fallback
      }
    } catch (error) {
      this.logger.error(
        `Error initializing PromptTemplateManager: ${error.message}`,
      );

      // Create a basic environment with no loader as fallback
      try {
        this.env = new nunjucks.Environment(null, {
          autoescape: false,
          throwOnUndefined: false,
        });

        // Add JSON filter to fallback env too
        this.env.addFilter("tojson", function (obj: any, indent: number = 2) {
          return JSON.stringify(obj, null, indent);
        });

        this.logger.log("Created fallback Nunjucks environment");
      } catch (envError) {
        this.logger.error(
          `Failed to create fallback environment: ${envError.message}`,
        );
        // As a last resort, create a minimal mock to prevent null references
        this.env = {
          renderString: (str: string, _vars?: any) => str,
          addFilter: () => {},
        } as unknown as nunjucks.Environment;
        this.logger.log("Created mock environment object");
      }
    }
  }

  /**
   * Get a template by name
   *
   * @param templateName Name of the template file
   * @returns Template content as string
   */
  getTemplate(templateName: string): string {
    try {
      const cwd = process.cwd();
      const templatePath = nodePath.join(
        cwd,
        "src",
        "llm",
        "prompt-templates",
        templateName,
      );
      this.logger.log(`Looking for template at: ${templatePath}`);

      try {
        if (!fs.existsSync(templatePath)) {
          this.logger.warn(`Template not found: ${templateName}`);
          throw new Error(`Template not found: ${templateName}`);
        }

        return fs.readFileSync(templatePath, "utf-8");
      } catch (e) {
        // Return a basic template if we can't find or read the file
        this.logger.warn(
          `Could not read template, using fallback: ${e.message}`,
        );
        return `# Fallback template for ${templateName}\n\nPlease provide your input.`;
      }
    } catch (error) {
      this.logger.error(
        `Error loading template ${templateName}: ${error.message}`,
      );
      return `# Fallback template for ${templateName}\n\nPlease provide your input.`;
    }
  }

  /**
   * Render a template with variables
   *
   * @param template Template string or name
   * @param variables Variables to inject into the template
   * @returns Rendered template
   */
  renderTemplate(
    template: string,
    variables: Record<string, any> = {},
  ): string {
    try {
      // Check if it's a template name rather than a template string
      if (
        template.endsWith(".njk") &&
        !template.includes("\n") &&
        template.length < 100
      ) {
        try {
          const templateContent = this.getTemplate(template);
          template = templateContent;
        } catch (error) {
          this.logger.warn(
            `Failed to load template file, treating as string: ${error.message}`,
          );
          // Continue with the original string
        }
      }

      if (!this.env) {
        this.logger.warn(
          "Nunjucks environment not initialized, returning original template",
        );
        return template;
      }

      return this.env.renderString(template, variables);
    } catch (error) {
      this.logger.error(`Template rendering error: ${error.message}`);
      // Fallback behavior: return the template as-is
      this.logger.log(`Fallback: returning original template`);
      return template;
    }
  }
}
