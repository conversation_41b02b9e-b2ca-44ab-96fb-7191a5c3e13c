import { Inject, Injectable, Logger } from "@nestjs/common";
import { OpenAI } from "openai";
import { LlmModuleOptions } from "../llm.module";
import { z } from "zod";
import { zodToJsonSchema } from "zod-to-json-schema";

@Injectable()
export class OpenAIService {
  protected readonly logger = new Logger(this.constructor.name);
  private client: OpenAI;

  constructor(
    @Inject("LLM_MODULE_OPTIONS")
    private readonly options: LlmModuleOptions,
  ) {
    if (!options.openaiApiKey) {
      throw new Error("OpenAI API key is not set");
    }

    this.client = new OpenAI({
      apiKey: options.openaiApiKey,
    });
  }

  /**
   * Get the OpenAI client
   *
   * @returns OpenAI
   */
  getClient(): OpenAI {
    return this.client;
  }

  /**
   * Create a chat completion
   *
   * @param params
   * @returns OpenAI.Chat.Completions.ChatCompletion
   */
  async createCompletion(
    params: OpenAI.Chat.Completions.ChatCompletionCreateParamsNonStreaming,
    caller?: string,
  ) {
    // Simple logging for development
    if (caller) {
      this.logger.log(`Chat completion requested by: ${caller}`);
    }

    try {
      const response = await this.client.chat.completions.create(params);
      return response;
    } catch (error) {
      this.logger.error(`Chat completion error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Execute a structured chat completion with schema validation
   *
   * @param params
   * @returns OpenAI.Chat.Completions.ChatCompletion
   */
  async createStructuredCompletion<T>(
    params: OpenAI.Chat.Completions.ChatCompletionCreateParamsNonStreaming & {
      zodSchema: z.ZodType<T>;
      schemaName: string;
    },
    caller?: string,
  ) {
    // Simple logging for development
    if (caller) {
      this.logger.log(`Structured chat completion requested by: ${caller}`);
    }

    try {
      const { zodSchema, schemaName, ...completionParams } = params;

      // If we have a Zod schema, use beta parse API with schema
      if (zodSchema && schemaName) {
        try {
          // Set up basic params with JSON response format
          const parseParams = {
            ...completionParams,
            response_format: { type: "json_object" },
          };

          // We need to access the beta API which has a different signature
          // Use any to bypass TypeScript checking since the OpenAI SDK types don't fully match the API
          const schema = this.zodToJsonSchema(zodSchema, schemaName);
          const client = this.client as any;

          // Pass the schema directly to the beta API
          const response = await client.beta.chat.completions.parse({
            ...parseParams,
            schema,
          });

          return response;
        } catch (error) {
          this.logger.error(`Beta parse API error: ${error.message}`);
          // Fall back to standard completion with JSON response format
          const response = await this.client.chat.completions.create({
            ...completionParams,
            response_format: { type: "json_object" },
          });
          return response;
        }
      } else {
        // No schema provided, use standard completion with JSON format
        const response = await this.client.chat.completions.create({
          ...completionParams,
          response_format: { type: "json_object" },
        });
        return response;
      }
    } catch (error) {
      this.logger.error(`Structured chat completion error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a streaming chat completion
   *
   * @param params Streaming parameters
   * @param caller Optional identifier for logging
   * @returns OpenAI streaming chat completion
   */
  async createCompletionStream(
    params: OpenAI.Chat.Completions.ChatCompletionCreateParamsStreaming,
    caller?: string,
  ) {
    if (caller) {
      this.logger.log(`Streaming chat completion requested by: ${caller}`);
    }

    try {
      // Return a properly typed streaming completion
      return await this.client.chat.completions.create({
        ...params,
        stream: true,
      });
    } catch (error) {
      this.logger.error(`Streaming chat completion error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a structured streaming chat completion using Zod schema validation
   *
   * @param params Streaming parameters with schema
   * @param caller Optional identifier for logging
   * @returns OpenAI streaming chat completion
   */
  async createStructuredCompletionStream<T>(
    params: OpenAI.Chat.Completions.ChatCompletionCreateParamsStreaming & {
      zodSchema?: z.ZodType<T>;
      schemaName?: string;
    },
    caller?: string,
  ) {
    if (caller) {
      this.logger.log(
        `Structured streaming chat completion requested by: ${caller}`,
      );
    }

    try {
      this.logger.warn("Structured streaming completion not implemented yet");
      throw new Error("Method not implemented");
    } catch (error) {
      this.logger.error(
        `Structured streaming completion error: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Convert a Zod schema to a JSON schema
   * @param schema Zod schema
   * @param schemaName Name for the schema
   * @returns JSON schema
   */
  private zodToJsonSchema(schema: z.ZodType, schemaName: string): any {
    try {
      return zodToJsonSchema(schema, {
        name: schemaName,
      });
    } catch (error) {
      this.logger.error(
        `Failed to convert Zod schema to JSON schema: ${error.message}`,
      );
      throw error;
    }
  }
}
