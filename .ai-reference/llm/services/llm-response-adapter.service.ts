import { Injectable, Logger } from "@nestjs/common";
import { z } from "zod";
import type { ChatCompletion } from "openai/resources";
import {
  LlmResponse,
  LlmResponseSchema,
  LlmToolCall,
} from "../types/llm-response.types";

/**
 * Service to adapt responses from different LLM providers to a unified format
 */
@Injectable()
export class LlmResponseAdapter {
  private readonly logger = new Logger(this.constructor.name);

  /**
   * Adapts an OpenAI chat completion to our generic LlmResponse format
   */
  adaptOpenAIResponse(completion: ChatCompletion): LlmResponse {
    const message = completion.choices[0].message;
    const toolCalls = message.tool_calls?.map((toolCall) => {
      try {
        return {
          id: toolCall.id,
          type: toolCall.type,
          name: toolCall.function.name,
          args: JSON.parse(toolCall.function.arguments || "{}"),
        } as LlmToolCall;
      } catch (error) {
        this.logger.warn(
          `Failed to parse tool call arguments: ${error.message}`,
        );
        return {
          id: toolCall.id,
          type: toolCall.type,
          name: toolCall.function.name,
          args: {},
        } as LlmToolCall;
      }
    });

    return LlmResponseSchema.parse({
      content: message.content,
      tool_calls: toolCalls,
      metadata: {
        model: completion.model,
        usage: completion.usage,
        finish_reason: completion.choices[0].finish_reason,
      },
    });
  }

  /**
   * Adapts an Anthropic/Claude response to our generic LlmResponse format
   */
  adaptAnthropicResponse(completion: any): LlmResponse {
    // Example implementation for Anthropic's Claude
    // Adjust based on actual Anthropic API response format
    return LlmResponseSchema.parse({
      content: completion.content?.[0]?.text || null,
      metadata: {
        model: completion.model,
        usage: {
          prompt_tokens: completion.usage?.input_tokens,
          completion_tokens: completion.usage?.output_tokens,
          total_tokens:
            (completion.usage?.input_tokens || 0) +
            (completion.usage?.output_tokens || 0),
        },
      },
    });
  }

  /**
   * Adapts a Google/Gemini response to our generic LlmResponse format
   */
  adaptGeminiResponse(response: any): LlmResponse {
    // Example implementation for Google's Gemini
    // Adjust based on actual Gemini API response format
    return LlmResponseSchema.parse({
      content: response.candidates?.[0]?.content?.parts?.[0]?.text || null,
      metadata: {
        model: response.candidates?.[0]?.model || "gemini",
        usage: {
          prompt_tokens: response.usageMetadata?.promptTokenCount,
          completion_tokens: response.usageMetadata?.candidatesTokenCount,
          total_tokens: response.usageMetadata?.totalTokenCount,
        },
        finish_reason: this.mapGeminiFinishReason(
          response.candidates?.[0]?.finishReason,
        ),
      },
    });
  }

  /**
   * Maps Gemini finish reasons to our standardized enum values
   */
  private mapGeminiFinishReason(
    reason: string,
  ): "stop" | "length" | "content_filter" | undefined {
    const mappings: Record<string, "stop" | "length" | "content_filter"> = {
      STOP: "stop",
      MAX_TOKENS: "length",
      SAFETY: "content_filter",
      RECITATION: "content_filter",
      OTHER: "stop",
    };

    return mappings[reason];
  }

  /**
   * Generic adapter that tries to convert any LLM completion to our standard format
   */
  adaptGenericResponse(completion: any): LlmResponse {
    try {
      // Try to detect the provider based on the response structure
      if (completion.choices && completion.choices[0]?.message) {
        return this.adaptOpenAIResponse(completion as ChatCompletion);
      } else if (completion.content && Array.isArray(completion.content)) {
        return this.adaptAnthropicResponse(completion);
      } else if (completion.candidates) {
        return this.adaptGeminiResponse(completion);
      } else {
        // Fallback for unknown providers - just extract whatever we can
        return LlmResponseSchema.parse({
          content: this.extractContentFromUnknown(completion),
          metadata: {
            model: completion.model || "unknown",
          },
        });
      }
    } catch (error) {
      this.logger.error("Failed to adapt LLM response", error);
      // Return a minimal valid response
      return {
        content:
          typeof completion === "string"
            ? completion
            : "Failed to parse LLM response",
        metadata: {
          model: "unknown",
        },
      };
    }
  }

  /**
   * Attempts to extract content from an unknown response format
   */
  private extractContentFromUnknown(response: any): string | null {
    if (typeof response === "string") {
      return response;
    } else if (response?.text) {
      return response.text;
    } else if (response?.content) {
      if (typeof response.content === "string") {
        return response.content;
      } else if (Array.isArray(response.content)) {
        return response.content
          .map((item: any) =>
            typeof item === "string" ? item : item?.text || "",
          )
          .join("\n");
      }
    }
    return null;
  }
}
