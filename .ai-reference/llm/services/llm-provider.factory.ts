import { Inject, Injectable, Logger } from "@nestjs/common";
import { LlmProvider } from "../interfaces/llm-provider.interface";
import { OpenAIAskService } from "./openai-ask.service";
import { LlmModuleOptions } from "../llm.module";
import { LlmResponse } from "../types/ask-llm.types";

/**
 * Factory service that manages LLM provider selection and instantiation
 */
@Injectable()
export class LlmProviderFactory {
  private readonly logger = new Logger(LlmProviderFactory.name);
  private providers: Map<string, LlmProvider> = new Map();

  constructor(
    @Inject("LLM_MODULE_OPTIONS") private readonly options: LlmModuleOptions,
    private readonly openAiService: OpenAIAskService,
  ) {
    // Initialize providers map
    this.providers.set("openai", this.openAiService);

    // Log available providers
    this.logger.log(
      `Initialized LLM providers: ${Array.from(this.providers.keys()).join(", ")}`,
    );
  }

  /**
   * Get LLM provider by name
   * @param name Provider name (defaults to the default provider from options)
   * @returns LLM provider instance
   */
  getProvider(name?: string): LlmProvider {
    const providerName = name || this.options.defaultProvider || "openai";

    const provider = this.providers.get(providerName);
    if (!provider) {
      this.logger.warn(
        `Provider "${providerName}" not found, falling back to OpenAI`,
      );
      return this.providers.get("openai")!;
    }

    return provider;
  }

  /**
   * Register a new provider
   * @param name Provider name
   * @param provider Provider instance
   */
  registerProvider(name: string, provider: LlmProvider): void {
    if (this.providers.has(name)) {
      this.logger.warn(`Provider "${name}" already registered, overwriting`);
    }

    this.providers.set(name, provider);
    this.logger.log(`Registered provider: ${name}`);
  }

  /**
   * Get the appropriate provider for the given model
   * @param model Model name
   * @returns LLM provider instance
   */
  getProviderForModel(model?: string): LlmProvider {
    if (!model) {
      return this.getProvider();
    }

    // Check model prefixes to determine provider
    if (model.startsWith("gpt-") || model.includes("gpt")) {
      return this.getProvider("openai");
    }

    // Add more model detection logic here as needed

    // Default to configured provider
    return this.getProvider();
  }
}
