import { Inject, Injectable, Logger } from "@nestjs/common";
import * as fs from "fs";
import { z } from "zod";
import * as path from "path";
import { LlmModuleOptions } from "../llm.module";
import { PromptTemplateManager } from "./prompt-template-manager.service";
import {
  AskLLMParams,
  AskLLMStructuredParams,
  LlmMessage,
  LlmResponse,
  ContentPart,
} from "../types/ask-llm.types";
import { LlmProviderFactory } from "./llm-provider.factory";

@Injectable()
export class AskLLMService {
  protected readonly logger = new Logger(this.constructor.name);

  constructor(
    private readonly llmProviderFactory: LlmProviderFactory,
    private readonly promptTemplateManager: PromptTemplateManager,
    @Inject("LLM_MODULE_OPTIONS") private readonly options: LlmModuleOptions,
  ) {}

  /**
   * A versatile method to interact with LLM models with support for:
   * - Direct prompts
   * - Message arrays
   * - Template-based prompts
   * - Image inputs
   * - Zod schema validation
   * - <PERSON><PERSON> calling
   *
   * @param params Configuration for the LLM request
   * @returns Complete LLM response object
   */
  async ask(params: AskLLMParams): Promise<LlmResponse> {
    // Extract parameters we need to process ourselves
    const {
      model,
      prompt,
      variables,
      system,
      images,
      zodSchema,
      schemaName,
      messages: inputMessages,
      debug = false,
      // Parameters below are directly passed to LLM
      ...passthroughParams
    } = params;

    // Get the appropriate provider based on the model
    const llmProvider = this.llmProviderFactory.getProviderForModel(model);

    // Determine if we need to create a structured output
    const isZodSchema = zodSchema && schemaName;

    // Initialize messages array
    const processedMessages = this.buildMessages(
      system,
      prompt,
      images,
      variables,
      inputMessages,
    );

    // Debug logging
    if (debug) {
      this.logger.debug({
        msg: "LLM Request",
        request: JSON.stringify(
          {
            messages: processedMessages,
            model: model || "gpt-4o-mini",
            temperature: passthroughParams.temperature,
            max_tokens: passthroughParams.max_completion_tokens,
            tools: passthroughParams.tools,
          },
          null,
          2,
        ),
      });
    }

    // Create a structured completion if schema is provided
    if (isZodSchema) {
      try {
        // Pass the Zod schema to the provider
        return await llmProvider.createStructuredCompletion({
          ...passthroughParams,
          model,
          messages: processedMessages,
          zodSchema,
          schemaName,
        });
      } catch (error) {
        this.logger.error(`Structured output error: ${error.message}`);
        throw error;
      }
    } else {
      // Call provider for standard completion
      return await llmProvider.createCompletion({
        ...passthroughParams,
        model,
        messages: processedMessages,
      });
    }
  }

  /**
   * Prepare messages based on the input params type
   */
  private buildMessages(
    system?: string,
    prompt?: string,
    images?: string[],
    variables?: Record<string, any>,
    existingMessages?: LlmMessage[],
  ): LlmMessage[] {
    // Initialize with existing messages if provided
    let resultMessages: LlmMessage[] = existingMessages || [];
    const isOngoingConversation =
      existingMessages && existingMessages.length > 0;

    // Handle user prompt if provided
    if (prompt) {
      // Get messages from the prompt, splitTemplateByRoles always returns at least one message
      let promptMessages = this.splitTemplateByRoles(prompt);

      // In an ongoing conversation, filter out system messages from the prompt
      if (isOngoingConversation) {
        promptMessages = promptMessages.filter((msg) => msg.role !== "system");
      }

      // Apply variable substitution to each message content
      if (variables && Object.keys(variables).length > 0) {
        for (const message of promptMessages) {
          if (typeof message.content === "string") {
            try {
              message.content = this.promptTemplateManager.renderTemplate(
                message.content,
                variables,
              );
            } catch (error) {
              this.logger.error(
                "Template rendering error for role content:",
                error,
              );
              // Keep original content
            }
          }
        }
      }

      // Add the processed messages
      resultMessages = [...resultMessages, ...promptMessages];
    }

    // If no messages were generated, throw an error
    if (resultMessages.length === 0) {
      throw new Error(
        "No messages were provided. Please provide either messages, system+prompt, or a template with role markers.",
      );
    }

    // Handle system message if provided directly
    if (system) {
      // Apply variable substitution to system message
      let processedSystem = system;
      if (variables && Object.keys(variables).length > 0) {
        try {
          processedSystem = this.promptTemplateManager.renderTemplate(
            system,
            variables,
          );
        } catch (error) {
          this.logger.error(
            "Template rendering error for system message:",
            error,
          );
          // Fall back to original system message
        }
      }

      // Filter out any existing system messages
      resultMessages = resultMessages.filter((msg) => msg.role !== "system");

      // Add the new system message at the beginning
      resultMessages.unshift({
        role: "system",
        content: processedSystem,
      });
    }

    // Add images to the latest user message if provided
    if (images && images.length > 0) {
      resultMessages = this.addImagesToMessages(resultMessages, images);
    }

    return resultMessages;
  }

  /**
   * Split a template string by role markers
   * @param template The template string to split
   * @returns Array of {role, content} objects, always including at least a user message
   */
  private splitTemplateByRoles(template: string): LlmMessage[] {
    // Split by role markers that are on their own line with a newline after the colon
    const sections = template.split(
      /^# (system|user|assistant|developer|tool):\n/m,
    );

    // If no role markers were found, return the template as a user message
    if (sections.length === 1) {
      return [
        {
          role: "user",
          content: template.trim(),
        },
      ];
    }

    // Process the sections into messages
    const messages: LlmMessage[] = [];
    for (let i = 1; i < sections.length; i += 2) {
      const roleString = sections[i].trim();
      let content = sections[i + 1]?.trim() || "";

      // Map 'developer' role to 'system' for compatibility
      const role =
        roleString === "developer"
          ? "system"
          : (roleString as "system" | "user" | "assistant" | "tool");

      // Create base message
      const message: Partial<LlmMessage> = {
        role,
        content,
      };

      // Add tool_call_id for tool messages
      if (role === "tool") {
        (message as any).tool_call_id = ""; // Default empty ID, should be filled in by caller
      }

      messages.push(message as LlmMessage);
    }

    return messages;
  }

  /**
   * Add images to the last user message
   */
  private addImagesToMessages(
    messages: LlmMessage[],
    images: string[],
  ): LlmMessage[] {
    if (images.length === 0 || messages.length === 0) {
      return messages;
    }

    // Find the last user message
    const lastUserMessageIndex = messages
      .map((msg, index) => ({ role: msg.role, index }))
      .filter((item) => item.role === "user")
      .pop()?.index;

    if (lastUserMessageIndex === undefined) {
      return messages;
    }

    const updatedMessages = [...messages];
    const userMessage = updatedMessages[lastUserMessageIndex];

    // Create content parts array
    const contentParts: ContentPart[] = [];

    // Add text content if it exists
    if (typeof userMessage.content === "string" && userMessage.content) {
      contentParts.push({
        type: "text",
        text: userMessage.content,
      });
    }
    // If content is already an array, preserve it
    else if (Array.isArray(userMessage.content)) {
      contentParts.push(...(userMessage.content as ContentPart[]));
    }

    // Add each image to the content array
    for (const imagePath of images) {
      try {
        const base64Image = fs.readFileSync(imagePath, { encoding: "base64" });
        const mimeType = this.getMimeTypeFromPath(imagePath);
        contentParts.push({
          type: "image",
          url: `data:${mimeType};base64,${base64Image}`,
        });
      } catch (error) {
        this.logger.error(`Failed to read image at ${imagePath}`, error);
      }
    }

    // Update the message with the new content parts
    updatedMessages[lastUserMessageIndex] = {
      ...userMessage,
      content: contentParts,
    };

    return updatedMessages;
  }

  /**
   * Get MIME type from file extension
   */
  private getMimeTypeFromPath(filePath: string): string {
    const extension = path.extname(filePath).toLowerCase();
    const mimeTypes: Record<string, string> = {
      ".jpg": "image/jpeg",
      ".jpeg": "image/jpeg",
      ".png": "image/png",
      ".gif": "image/gif",
      ".webp": "image/webp",
      ".svg": "image/svg+xml",
      ".bmp": "image/bmp",
    };

    return mimeTypes[extension] || "image/jpeg"; // Default to jpeg if unknown
  }
}
