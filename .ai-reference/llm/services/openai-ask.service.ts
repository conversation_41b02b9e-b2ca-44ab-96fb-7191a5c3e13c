import { Inject, Injectable, Logger } from "@nestjs/common";
import { OpenAI } from "openai";
import { zodResponseFormat } from "openai/helpers/zod";
import type {
  ChatCompletionContentPartImage,
  ChatCompletionContentPartText,
  ChatCompletionCreateParams,
  ChatCompletionCreateParamsNonStreaming,
  ChatCompletionMessageParam,
  ChatCompletionTool
} from "openai/resources";
import { z } from "zod";
import { zodToJsonSchema } from "zod-to-json-schema";
import { LlmModuleOptions } from "../llm.module";
import {
  AskLLMParams,
  LlmMessage,
  LlmToolCall,
  LlmUsage,
  LlmResponse,
  AskLLMStructuredParams
} from "../types/ask-llm.types";
import { LlmProvider } from "../interfaces/llm-provider.interface";

@Injectable()
export class OpenAIAskService implements LlmProvider {
  protected readonly logger = new Logger(this.constructor.name);
  private client: OpenAI;

  constructor(
    @Inject("LLM_MODULE_OPTIONS")
    private readonly options: LlmModuleOptions
  ) {
    if (!options.openaiApiKey) {
      throw new Error("OpenAI API key is not set");
    }

    this.client = new OpenAI({
      apiKey: options.openaiApiKey
    });
  }

  /**
   * Convert our generic LlmMessages to OpenAI's message format
   */
  adaptMessages(messages: LlmMessage[]): ChatCompletionMessageParam[] {
    return messages.map((message) => {
      // Handle content correctly based on type
      let content: string | null | undefined = null;
      if (typeof message.content === "string") {
        content = message.content;
      } else if (message.content === null) {
        content = null;
      }
      // Note: We're ignoring ContentPart[] for now as it requires custom mapping

      // Create a base message structure using 'any' to bypass strict typechecking
      // We have to use any because the OpenAI types are too restrictive
      const openaiMessage: any = {
        role: message.role === "developer" ? "system" : message.role,
        content: content
      };

      // Add name if present
      if (message.name) {
        openaiMessage.name = message.name;
      }

      // Handle tool calls for assistant messages
      if (message.role === "assistant" && message.tool_calls) {
        openaiMessage.tool_calls = message.tool_calls.map((toolCall) => ({
          id: toolCall.id || `tool_${Date.now()}`,
          type: "function",
          function: {
            name: toolCall.function.name,
            arguments: JSON.stringify(toolCall.function.arguments)
          }
        }));
      }

      // Handle tool messages (responses to tool calls)
      if (message.role === "tool") {
        openaiMessage.tool_call_id = (message as any).tool_call_id || "";
      }

      return openaiMessage;
    });
  }

  /**
   * Convert OpenAI's response to our unified format
   */
  private adaptOpenAIResponse(response: any, parsed?: any): LlmResponse {
    const choices = response.choices || [];
    const firstChoice = choices[0] || {};
    const message = firstChoice.message || {};

    // Extract content
    const content = message.content || null;

    // Extract tool calls if present
    const toolCalls: LlmToolCall[] = [];
    if (message.tool_calls && message.tool_calls.length > 0) {
      for (const toolCall of message.tool_calls) {
        try {
          toolCalls.push({
            id: toolCall.id,
            type: toolCall.type,
            function: {
              name: toolCall.function.name,
              arguments: toolCall.function.arguments
            }
          });
        } catch (error) {
          this.logger.warn(`Failed to parse tool call arguments: ${error.message}`);
        }
      }
    }

    // Extract usage information
    const usage: LlmUsage = {
      promptTokens: response.usage?.prompt_tokens || 0,
      completionTokens: response.usage?.completion_tokens || 0,
      totalTokens: response.usage?.total_tokens || 0,
      raw: response.usage || {}
    };

    // Build the unified response
    const unifiedResponse: LlmResponse = {
      id: response.id,
      model: response.model,
      created: response.created,
      message: {
        role: "assistant",
        content,
        tool_calls: toolCalls.length > 0 ? toolCalls : undefined
      },
      usage,
      finishReason: firstChoice.finish_reason,
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
      raw: response
    };

    // Add parsed data if available
    if (parsed !== undefined) {
      unifiedResponse.parsed = parsed;
    }

    return unifiedResponse;
  }

  /**
   * Convert generic params to OpenAI-specific params
   */
  private adaptParams(params: AskLLMParams): ChatCompletionCreateParamsNonStreaming {
    const {
      zodSchema,
      schemaName,
      response_format,
      messages,
      max_completion_tokens,
      store,
      modalities,
      debug,
      stream,
      ...openaiParams
    } = params;

    // Create base params
    const completionParams: ChatCompletionCreateParamsNonStreaming = {
      model: params.model || "gpt-4o-mini",
      messages: messages ? this.adaptMessages(messages) : [],
      ...openaiParams,
      stream: false
    };

    // Add max_tokens if specified
    if (max_completion_tokens !== undefined) {
      completionParams.max_tokens = max_completion_tokens;
    }

    // Add response_format if specified
    if (response_format) {
      completionParams.response_format = { type: "json_object" };
    }

    return completionParams;
  }

  /**
   * Execute a standard chat completion
   */
  async createCompletion(params: AskLLMParams): Promise<LlmResponse> {
    try {
      const openaiParams = this.adaptParams(params);
      const response = await this.client.chat.completions.create(openaiParams);
      return this.adaptOpenAIResponse(response);
    } catch (error) {
      this.logger.error(`Chat completion error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Creates a chat completion with structured output validation using a Zod schema
   * @param params Parameters for creating a structured completion
   * @returns The LLM response with parsed data
   */
  async createStructuredCompletion<T>(
    params: AskLLMStructuredParams<T>
  ): Promise<LlmResponse & { parsed?: T }> {
    try {
      const { zodSchema, schemaName, ...rest } = params;

      const openaiParams = this.adaptParams(rest);

      // Use the beta parse API
      try {
        const client = this.client as OpenAI;

        if (client.beta?.chat?.completions?.parse) {
          const parseParams = {
            ...openaiParams,
            response_format: zodResponseFormat(zodSchema, schemaName)
          };

          const response = await client.beta.chat.completions.parse(parseParams);
          let parsed: T | undefined;

          // Extract the parsed result from the response
          if (response.choices[0]?.message?.parsed) {
            parsed = response.choices[0].message.parsed as T;
          }

          return this.adaptOpenAIResponse(response, parsed);
        }
      } catch (error) {
        this.logger.warn(`Beta parse API error: ${error.message}, falling back to standard completion`);
      }

      // Fallback to standard completion with manual parsing
      const response = await this.client.chat.completions.create({
        ...openaiParams
      });

      let parsed: T | undefined;

      try {
        if (response.choices[0]?.message?.content) {
          const content = response.choices[0].message.content;
          const jsonData = this.extractJsonFromResponse(content);
          parsed = zodSchema.parse(jsonData);
        }
      } catch (error) {
        this.logger.error(`Failed to parse response as JSON: ${error.message}`);
      }

      return this.adaptOpenAIResponse(response, parsed);
    } catch (error) {
      this.logger.error(`Structured chat completion error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Helper method to extract JSON from response content
   * that might be wrapped in markdown code blocks
   */
  private extractJsonFromResponse(content: string): any {
    try {
      // First try direct parsing
      return JSON.parse(content);
    } catch (e) {
      // Look for JSON in code blocks
      const jsonRegex = /```(?:json)?\s*([\s\S]*?)\s*```/;
      const match = content.match(jsonRegex);

      if (match && match[1]) {
        try {
          return JSON.parse(match[1]);
        } catch (innerError) {
          this.logger.error(`Failed to parse JSON from code block: ${innerError.message}`);
        }
      }

      // Try to find anything that looks like a JSON object
      const objectRegex = /(\{[\s\S]*\})/;
      const objectMatch = content.match(objectRegex);

      if (objectMatch && objectMatch[1]) {
        try {
          return JSON.parse(objectMatch[1]);
        } catch (innerError) {
          this.logger.error(`Failed to parse JSON object: ${innerError.message}`);
        }
      }

      throw new Error("Could not extract JSON from response");
    }
  }

  /**
   * Create a streaming chat completion
   */
  async createCompletionStream(params: AskLLMParams): Promise<any> {
    try {
      const openaiParams = this.adaptParams(params) as any;
      openaiParams.stream = true;

      return this.client.chat.completions.create(openaiParams);
    } catch (error) {
      this.logger.error(`Streaming chat completion error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a structured streaming chat completion with schema validation
   */
  async createStructuredCompletionStream<T>(params: AskLLMStructuredParams<T>): Promise<any> {
    try {
      this.logger.log("Create structured streaming completion not implemented yet");
      throw new Error("Method not implemented");
    } catch (error) {
      this.logger.error(`Structured streaming completion error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Convert a Zod schema to a JSON schema
   */
  private zodToJsonSchema(schema: z.ZodType, schemaName: string): any {
    try {
      return zodToJsonSchema(schema, {
        name: schemaName
      });
    } catch (error) {
      this.logger.error(`Failed to convert Zod schema to JSON schema: ${error.message}`);
      throw error;
    }
  }
}
