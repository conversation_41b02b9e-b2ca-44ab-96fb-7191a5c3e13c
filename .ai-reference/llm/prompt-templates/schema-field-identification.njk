# system:
You are an assistant helping to identify the most appropriate field name from a schema based on a user instruction.

# user:
Instruction: {{ instruction }}
Document Type: {{ documentType }}
Field Category: {{ fieldCategory }}

{% if documentType == "SHIPMENT" %}
{% if fieldCategory == "DATETIME" %}
Available fields for SHIPMENT documents with DATETIME category:
- etd: Estimated Time of Departure
- eta: Estimated Time of Arrival (at port)
- etaDestination: Estimated Time of Arrival at final destination
- cargoReady: Cargo Ready Date
- actualDeparture: Actual Departure Date
- actualArrival: Actual Arrival Date
- pickupDate: Container Pickup Date
- returnDate: Container Return Date
{% endif %}

{% if fieldCategory == "LOCATION" %}
Available fields for SHIPMENT documents with LOCATION category:
- originLocation: Origin Location
- destinationLocation: Destination Location
- portOfLoading: Port of Loading
- portOfDischarge: Port of Discharge
{% endif %}

{% if fieldCategory == "TRADEPARTNER" %}
Available fields for SHIPMENT documents with TRADEPARTNER category:
- shipper: Shipper Information
- consignee: Consignee Information
- notifyParty: Notify Party Information
- forwarder: Forwarder Information
{% endif %}

{% if fieldCategory == "IDENTIFIER" %}
Available fields for SHIPMENT documents with IDENTIFIER category:
- shipmentId: Shipment ID
- bookingNumber: Booking Number
- containerNumbers: Container Numbers
- billOfLadingNumber: Bill of Lading Number
- customsReferenceNumber: Customs Reference Number
{% endif %}
{% endif %}

{% if documentType == "COMMERCIAL_INVOICE" %}
{% if fieldCategory == "DATETIME" %}
Available fields for COMMERCIAL_INVOICE documents with DATETIME category:
- invoiceDate: Invoice Date
- shipmentDate: Shipment Date
- dueDate: Payment Due Date
{% endif %}

{% if fieldCategory == "AMOUNT" %}
Available fields for COMMERCIAL_INVOICE documents with AMOUNT category:
- totalAmount: Total Invoice Amount
- itemAmount: Individual Item Amount
- taxAmount: Tax Amount
- freightAmount: Freight Amount
{% endif %}
{% endif %}

Based on the instruction and field category, determine the most appropriate field name from the available options.
Respond with ONLY the field name that best matches the instruction and category.
If none of the fields match, respond with "NO_MATCH".

Example response:
etd 