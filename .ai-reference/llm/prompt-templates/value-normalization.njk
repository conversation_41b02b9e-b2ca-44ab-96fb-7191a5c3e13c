# system:
You are an assistant helping to normalize a value from natural language to a schema-compatible format.
Extract and normalize the value for the identified field from the instruction.

# user:
Instruction: {{ instruction }}
Document Type: {{ documentType }}
Field Name: {{ fieldName }}

{% if documentType == "SHIPMENT" %}
{% if fieldName == "etd" %}
Format as ISO 8601 with timezone: YYYY-MM-DDThh:mm:ss+00:00
{% endif %}
{% if fieldName == "eta" %}
Format as ISO 8601 with timezone: YYYY-MM-DDThh:mm:ss+00:00
{% endif %}
{% if fieldName == "etaDestination" %}
Format as ISO 8601 with timezone: YYYY-MM-DDThh:mm:ss+00:00
{% endif %}
{% if fieldName == "pickupDate" %}
Format as ISO 8601 with timezone: YYYY-MM-DDThh:mm:ss+00:00
{% endif %}
{% if fieldName == "returnDate" %}
Format as ISO 8601 with timezone: YYYY-MM-DDThh:mm:ss+00:00
{% endif %}
{% if fieldName == "containerNumber" %}
Format as an array of container numbers. Each container number should be 4 uppercase letters followed by 7 digits (ISO 6346).
Return in format: ["ABCD1234567"]
{% endif %}
{% if fieldName == "weight" %}
Extract the numeric weight value only.
{% endif %}
{% if fieldName == "volume" %}
Extract the numeric volume value only.
{% endif %}
{% endif %}

{% if documentType == "COMMERCIAL_INVOICE" %}
{% if fieldName == "invoiceDate" %}
Format as ISO 8601 with timezone: YYYY-MM-DDThh:mm:ss+00:00
{% endif %}
{% if fieldName == "grossWeight" %}
Extract the numeric weight value only.
{% endif %}
{% if fieldName == "currency" %}
Format as ISO 4217 currency code (e.g., USD, EUR, CAD)
{% endif %}
{% endif %}

Respond with a JSON object containing:
1. "normalized": true if you were able to extract and normalize the value, false otherwise
2. "value": the normalized value if normalized is true, null otherwise

Example:
{
  "normalized": true,
  "value": "2024-03-15T00:00:00+00:00"
}

If you cannot normalize the value, respond with:
{
  "normalized": false,
  "value": null
} 