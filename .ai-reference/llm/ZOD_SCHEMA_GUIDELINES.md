# Zod Schema Guidelines for LLM Structured Outputs

This document outlines best practices and limitations for using Zod schemas with the LLM service's structured output capabilities.

## Overview

When using the `askLLMService.ask()` method with a `zodSchema` parameter, the service will attempt to validate the LLM's response against the schema. For this to work properly, the Zod schema must be compatible with OpenAI's JSON schema capabilities.

## Supported Schema Features

### Basic Types

- `z.string()` - String values
- `z.number()` - Numeric values
- `z.boolean()` - Boolean values
- `z.null()` - Null values
- `z.array(...)` - Arrays of values
- `z.object({...})` - Object structures
- `z.enum([...])` - Enumerated values

### Nesting

- Nested objects are supported via `z.object()`
- Arrays of objects are supported via `z.array(z.object({...}))`

## Limitations and Restrictions

### Avoid Using

These Zod features are NOT compatible with OpenAI's structured output capabilities:

1. **Validation Methods**

   - `.min()`, `.max()`, `.length()` - String/array length constraints
   - `.email()`, `.url()`, `.uuid()` - String format validation
   - `.regex()` - Regular expression patterns
   - `.int()`, `.positive()`, `.nonnegative()` - Number constraints
   - `.min()`, `.max()` - Number range constraints

2. **Custom Logic**

   - `.refine()` - Custom validation logic
   - `.transform()` - Value transformations

3. **Optionality Handling**

   - Nested `.optional()` fields may cause issues

4. **Schema Composition**
   - Avoid complex unions with `.or()`

## Example of a Valid Schema

```typescript
// GOOD - Compatible with OpenAI structured output
const goodSchema = z.object({
  name: z.string(),
  age: z.number(),
  email: z.string(), // No .email() validation
  tags: z.array(z.string()),
  status: z.enum(["active", "inactive", "pending"]),
  address: z.object({
    street: z.string(),
    city: z.string(),
    country: z.string(),
  }),
});
```

## Examples of Problematic Schemas

```typescript
// BAD - Not compatible with OpenAI structured output
const badSchema = z.object({
  name: z.string().min(3).max(50), // ❌ min/max not supported
  age: z.number().int().positive(), // ❌ int/positive not supported
  email: z.string().email(), // ❌ email validation not supported
  tags: z.array(z.string()).min(1), // ❌ min length not supported
  score: z.number().refine((n) => n > 0, {
    // ❌ refine not supported
    message: "Score must be positive",
  }),
  formattedValue: z.string().transform((val) => val.toUpperCase()), // ❌ transform not supported
});
```

## Best Practices

1. **Keep Schemas Simple**

   - Use only the basic type definitions
   - Avoid custom validation methods

2. **Validation in Application Logic**

   - Perform additional validation after receiving the LLM response
   - Apply transformations afterward if needed

3. **Prompt Engineering**

   - Include schema requirements in your prompt
   - For example: "Please provide a user with name (string), age (positive number), and interests (array of strings)"

4. **Error Handling**
   - Expect that schema validation might fail despite these precautions
   - Implement graceful fallbacks in your application code

## Technical Details

When a Zod schema is provided to the `askLLMService.ask()` method, the service:

1. First tries to use OpenAI's beta parse API with the schema
2. If that fails, falls back to standard completion and tries to extract JSON from the response
3. Validates the extracted JSON against the provided Zod schema

For the best experience, follow these guidelines to ensure your schemas are compatible with both the primary and fallback paths.
