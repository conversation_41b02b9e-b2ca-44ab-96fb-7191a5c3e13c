import {
  Ask<PERSON><PERSON>arams,
  AskLLMStructuredParams,
  LlmMessage,
  LlmResponse,
} from "../types/ask-llm.types";
import { z } from "zod";

/**
 * Base interface for LLM provider adapters
 * All provider implementations must follow this contract
 */
export interface LlmProvider {
  /**
   * Convert generic LlmMessages to provider-specific format
   * @param messages Our standardized message format
   * @returns Provider-specific message format
   */
  adaptMessages(messages: LlmMessage[]): any;

  /**
   * Execute a chat completion request with the provider
   * @param params Generic parameters that will be adapted to the provider
   * @returns A unified response object
   */
  createCompletion(params: AskLLMParams): Promise<LlmResponse>;

  /**
   * Create a structured chat completion with schema validation
   * @param params Generic parameters with schema for validation
   * @returns A unified response with validated data
   */
  createStructuredCompletion<T>(
    params: AskLLMStructuredParams<T>,
  ): Promise<LlmResponse & { parsed?: T }>;

  /**
   * Create a streaming chat completion
   * @param params Generic parameters
   * @returns Provider-specific stream that should be adapted by the caller
   */
  createCompletionStream(params: Ask<PERSON>MParams): Promise<any>;

  /**
   * Create a structured streaming chat completion with schema validation
   * @param params Generic parameters with schema for validation
   * @returns Provider-specific stream with validated data that should be adapted by the caller
   */
  createStructuredCompletionStream<T>(
    params: AskLLMStructuredParams<T>,
  ): Promise<any>;
}
