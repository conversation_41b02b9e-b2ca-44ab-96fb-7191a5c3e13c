import { z } from "zod";
import type {
  ChatCompletionMessageParam,
  ChatCompletionTool,
  ChatCompletionToolChoiceOption
} from "openai/resources";

export interface AskLLMParams {
  temperature?: number;
  model?: string;
  zodSchema?: z.ZodType;
  schemaName?: string;
  response_format?: Record<string, any>;
  tools?: ChatCompletionTool[];
  toolChoice?: ChatCompletionToolChoiceOption;
  debug?: boolean;
  store?: boolean;
  reasoning_effort?: "low" | "medium" | "high";
  metadata?: Record<string, string>;
  frequency_penalty?: number;
  logit_bias?: Record<string, number>;
  logprobs?: boolean;
  top_logprobs?: number;
  max_completion_tokens?: number;
  n?: number;
  modalities?: string[];
  prediction?: object;
  audio?: object;
  presence_penalty?: number;
  seed?: number;
  service_tier?: "auto" | "default";
  stop?: string | string[];
  stream?: boolean;
  stream_options?: {
    include_usage?: boolean;
  };
  top_p?: number;
  parallel_tool_calls?: boolean;
  user?: string;

  system?: string;
  messages?: LlmMessage[];
  prompt?: string;
  images?: string[];
  variables?: Record<string, any>;
}

/**
 * Extended params for structured output with required schema parameters
 */
export interface AskLLMStructuredParams<T = any> extends AskLLMParams {
  zodSchema: z.ZodType<T>;
  schemaName: string;
}

// Content part types
interface TextContent {
  type: "text";
  text: string;
}

interface ImageContent {
  type: "image";
  url: string;
  detail?: "low" | "medium" | "high" | "auto";
}

interface AudioContent {
  type: "audio";
  url: string;
}

interface VideoContent {
  type: "video";
  url: string;
}

// Input-specific content types
interface InputAudioContent {
  type: "input_audio";
  input_audio: {
    // Audio input properties would go here
    data: string; // Base64 encoded audio data
  };
}

// Union type for all content parts
export type ContentPart = TextContent | ImageContent | AudioContent | VideoContent | InputAudioContent;

/**
 * Tool call information for LLM function calling
 */
export interface LlmToolCall {
  id: string;
  type: "function";
  function: {
    name: string;
    arguments: string; // JSON string to match OpenAI's implementation
  };
}

/**
 * Usage information for LLM responses
 */
export interface LlmUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  completionTokensDetails?: {
    reasoningTokens: number;
    acceptedPredictionTokens: number;
    rejectedPredictionTokens: number;
  };
  raw?: Record<string, any>;
}

/**
 * Audio response configuration
 */
export interface AudioResponse {
  // Audio response properties would go here
  voice?: string;
  speed?: number;
}

/**
 * Base message interface that all message types extend
 */
export interface BaseLlmMessage {
  role: string;
  content: string | ContentPart[] | null;
  name?: string;
}

/**
 * Developer message (replacement for system message in newer models)
 */
export interface DeveloperMessage extends BaseLlmMessage {
  role: "developer";
}

/**
 * System message
 */
export interface SystemMessage extends BaseLlmMessage {
  role: "system";
}

/**
 * User message
 */
export interface UserMessage extends BaseLlmMessage {
  role: "user";
}

/**
 * Assistant message
 */
export interface AssistantMessage extends BaseLlmMessage {
  role: "assistant";
  refusal?: string | null;
  audio?: AudioResponse | null;
  tool_calls?: LlmToolCall[];
  function_call?: {
    // Deprecated
    name: string;
    arguments: string;
  } | null;
}

/**
 * Tool message
 */
export interface ToolMessage extends BaseLlmMessage {
  role: "tool";
  tool_call_id: string;
}

/**
 * Union type for all message types
 */
export type LlmMessage = DeveloperMessage | SystemMessage | UserMessage | AssistantMessage | ToolMessage;

/**
 * A unified response from an LLM interaction
 * This represents the standardized response format across all providers
 */
export interface LlmResponse {
  // Identifiers from the provider
  id?: string;
  model?: string;
  created?: number;

  // The response message from the LLM
  message: LlmMessage;

  // Usage information for tracking token consumption
  usage?: LlmUsage;

  // Convenience access to tool calls if present
  toolCalls?: LlmToolCall[];

  // Reason the LLM stopped generating
  finishReason?: "stop" | "length" | "content_filter" | "tool_calls" | string;

  // For structured outputs - parsed result
  parsed?: any;

  // Original provider-specific response
  raw: Record<string, any>;
}
