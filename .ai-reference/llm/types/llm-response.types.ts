import { z } from "zod";

/**
 * A generic chat message that works across different LLM providers
 */
export const LlmMessageSchema = z.object({
  role: z.enum(["system", "user", "assistant", "function", "tool"]),
  content: z.string().nullable(),
  name: z.string().optional(),
});

export type LlmMessage = z.infer<typeof LlmMessageSchema>;

/**
 * Base schema for tool calls made by the LLM
 * Designed to be easily extended for specific tool types
 */
export const LlmToolCallSchema = z.object({
  id: z.string().optional(),
  type: z.string(),
  name: z.string(),
  args: z.record(z.any()).describe("Arguments for the tool call"),
});

export type LlmToolCall = z.infer<typeof LlmToolCallSchema>;

/**
 * A generic, provider-agnostic response from an LLM
 * Designed to work well with OpenAI and other providers
 */
export const LlmResponseSchema = z.object({
  // Primary content as string (may be null if using tool_calls instead)
  content: z.string().nullable(),

  // Optional structured thinking/reasoning
  thinking: z
    .string()
    .optional()
    .describe("The LLM's step-by-step reasoning process"),

  // Optional reflection on the response
  reflection: z
    .string()
    .optional()
    .describe(
      "The LLM's reflection on its response quality and potential issues",
    ),

  // Tool calls made by the LLM
  tool_calls: z.array(LlmToolCallSchema).optional(),

  // Metadata about the response
  metadata: z
    .object({
      model: z.string().optional(),
      usage: z
        .object({
          prompt_tokens: z.number().optional(),
          completion_tokens: z.number().optional(),
          total_tokens: z.number().optional(),
        })
        .optional(),
      finish_reason: z
        .enum([
          "stop",
          "length",
          "content_filter",
          "tool_calls",
          "function_call",
        ])
        .optional(),
    })
    .optional(),
});

export type LlmResponse = z.infer<typeof LlmResponseSchema>;

/**
 * A more specific response type for structured outputs with tool calls
 * This is useful when you want to ensure the LLM produces specific tool calls
 */
export const StructuredLlmResponseSchema = z.object({
  thinking: z
    .string()
    .describe("Detailed reasoning about the input and how to respond"),
  reflection: z
    .string()
    .describe("Critical assessment of the reasoning and response"),
  tool_calls: z
    .array(LlmToolCallSchema)
    .min(1)
    .describe("The actions to take based on the input"),
});

export type StructuredLlmResponse = z.infer<typeof StructuredLlmResponseSchema>;

// Base content part types
interface TextContent {
  type: "text";
  text: string;
}

interface ImageContent {
  type: "image";
  url: string;
  detail?: "low" | "medium" | "high" | "auto";
}

// Optional additional content types for future expansion
interface AudioContent {
  type: "audio";
  url: string;
}

interface VideoContent {
  type: "video";
  url: string;
}

interface ToolResponse {
  tool_call_id: string;
  content: string;
}

// Union type for all content parts
type ContentPart = TextContent | ImageContent | AudioContent | VideoContent;

/**
 * Usage information for LLM responses
 * Standardized across different providers
 */
export interface LlmUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  raw: Record<string, any>;
}

/**
 * A tool call result from an LLM
 * Standardized across different providers
 */
export interface LlmToolCallInfo {
  id?: string;
  type: string;
  name: string;
  arguments: Record<string, any>;
}

// Main message interface - renamed to avoid conflict
export interface LlmMessageExtended {
  // Core properties
  role: "system" | "user" | "assistant" | "tool";

  // Content can be string (for backward compatibility),
  // array of content parts, or null
  content: string | ContentPart[] | null;

  // Tool/function calling
  toolCalls?: LlmToolCallInfo[];

  // Optional metadata that might be useful across platforms
  id?: string;
  name?: string;
  created?: number;
}

/**
 * A unified response interface for LLM interactions
 * Designed to be vendor-agnostic while providing direct access to important fields
 */
export interface LlmResponseExtended {
  // Primary content (typed for flexibility)
  message: LlmMessageExtended;

  id?: string;
  model?: string;
  created?: number;

  usage?: LlmUsage;

  toolCalls?: LlmToolCallInfo[];

  finishReason?: "stop" | "length" | "content_filter" | "tool_calls";

  raw: Record<string, any>;
}
