import { Test } from "@nestjs/testing";
import { AskLLMService } from "../services/ask-llm.service";
import { PromptTemplateManager } from "../services/prompt-template-manager.service";
import { LlmProviderFactory } from "../services/llm-provider.factory";
import { z } from "zod";

describe("AskLLMService", () => {
  let service: AskLLMService;
  let llmProviderFactory: LlmProviderFactory;
  let promptTemplateManager: PromptTemplateManager;
  let mockProvider: any;

  beforeEach(async () => {
    // Create a mock provider that implements the LlmProvider interface
    mockProvider = {
      adaptMessages: jest.fn(),
      createCompletion: jest.fn(),
      createStructuredCompletion: jest.fn(),
      createCompletionStream: jest.fn(),
      createStructuredCompletionStream: jest.fn(),
    };

    const moduleRef = await Test.createTestingModule({
      providers: [
        AskLLMService,
        {
          provide: LlmProviderFactory,
          useValue: {
            getProviderForModel: jest.fn(() => mockProvider),
          },
        },
        {
          provide: PromptTemplateManager,
          useValue: {
            renderTemplate: jest.fn((template) => template),
          },
        },
        {
          provide: "LLM_MODULE_OPTIONS",
          useValue: { openaiApiKey: "test-key" },
        },
      ],
    }).compile();

    service = moduleRef.get<AskLLMService>(AskLLMService);
    llmProviderFactory = moduleRef.get<LlmProviderFactory>(LlmProviderFactory);
    promptTemplateManager = moduleRef.get<PromptTemplateManager>(
      PromptTemplateManager,
    );
  });

  describe("ask", () => {
    it("should call provider with direct prompt", async () => {
      // Mock response from provider
      const mockResponse = {
        id: "test-id",
        model: "gpt-4o-mini",
        message: {
          role: "assistant",
          content: "Test response",
        },
        raw: {},
      };

      mockProvider.createCompletion.mockResolvedValue(mockResponse);

      // Call the service with a direct prompt
      const result = await service.ask({
        system: "You are a helpful assistant",
        prompt: "Hello, how are you?",
        model: "gpt-4o-mini",
      });

      // Verify the result and that the correct service was called
      expect(result).toBe(mockResponse);
      expect(mockProvider.createCompletion).toHaveBeenCalled();
      expect(llmProviderFactory.getProviderForModel).toHaveBeenCalledWith(
        "gpt-4o-mini",
      );
    });

    it("should handle structured output with Zod schema", async () => {
      // Create a simple schema for testing
      const zodSchema = z.object({
        summary: z.string(),
        sentiment: z.string(),
      });

      // Mock response with parsed data
      const mockParsedResult = {
        summary: "This is a summary",
        sentiment: "positive",
      };

      const mockResponse = {
        id: "test-id",
        model: "gpt-4o-mini",
        message: {
          role: "assistant",
          content: JSON.stringify(mockParsedResult),
        },
        parsed: mockParsedResult,
        raw: {},
      };

      mockProvider.createStructuredCompletion.mockResolvedValue(mockResponse);

      // Call the service with a schema
      const result = await service.ask({
        prompt: "Analyze this text",
        zodSchema: zodSchema,
        schemaName: "TestSchema",
        model: "gpt-4o-mini",
      });

      // Verify the structured output was returned
      expect(result).toEqual(mockResponse);
      expect(mockProvider.createStructuredCompletion).toHaveBeenCalledWith({
        messages: expect.any(Array),
        model: "gpt-4o-mini",
        zodSchema: zodSchema,
        schemaName: "TestSchema",
      });
    });

    it("should process messages with image attachments", async () => {
      // Mock response from provider
      const mockResponse = {
        id: "test-id",
        model: "gpt-4o-mini",
        message: {
          role: "assistant",
          content: "I see an image",
        },
        raw: {},
      };

      mockProvider.createCompletion.mockResolvedValue(mockResponse);

      // Call the service with a direct prompt and images
      const result = await service.ask({
        prompt: "What's in this image?",
        model: "gpt-4o-mini",
        images: ["test-image.jpg"],
      });

      // Verify the result and that images were processed
      expect(result).toBe(mockResponse);
      expect(mockProvider.createCompletion).toHaveBeenCalled();

      // Get the call arguments
      const callArgs = mockProvider.createCompletion.mock.calls[0][0];

      // At least one message should be passed
      expect(callArgs.messages.length).toBeGreaterThan(0);
    });
  });
});
