import { Test } from "@nestjs/testing";
import { AskLLMService } from "../services/ask-llm.service";
import { OpenAIService } from "../services/openai.service";
import { LlmModule } from "../llm.module";
import { z } from "zod";
import * as dotenv from "dotenv";
import {
  ChatCompletionSystemMessageParam,
  ChatCompletionUserMessageParam,
} from "openai/resources/chat";

// Load environment variables
dotenv.config();

/**
 * REAL API TESTS FOR STRUCTURED OUTPUT
 *
 * These tests use the actual OpenAI API with schema validation.
 * They will incur costs and should be run sparingly.
 */
describe("Structured Output - Real API Tests", () => {
  let askLLMService: AskLLMService;
  let openAIService: OpenAIService;

  // Timeout set to 30 seconds as API calls can take time
  jest.setTimeout(30000);

  beforeAll(async () => {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error(
        "OPENAI_API_KEY environment variable is not set. These tests require a valid API key.",
      );
    }

    const moduleRef = await Test.createTestingModule({
      imports: [
        LlmModule.register({
          openaiApiKey: process.env.OPENAI_API_KEY,
          defaultProvider: "openai",
        }),
      ],
    }).compile();

    askLLMService = moduleRef.get<AskLLMService>(AskLLMService);
    openAIService = moduleRef.get<OpenAIService>(OpenAIService);
  });

  describe("Simple Schema Validation", () => {
    it("should return properly validated data with a basic schema", async () => {
      // Define a simple schema - simplified based on zodschema guidelines
      const userProfileSchema = z.object({
        name: z.string(),
        age: z.number(),
        interests: z.array(z.string()),
      });

      const response = await askLLMService.ask({
        system:
          "You are a helpful assistant. Always respond with valid JSON that matches the requested schema.",
        prompt:
          "Generate a profile for a fictional user named Alex who is 29 years old and loves hiking and photography. Respond with JSON.",
        model: "gpt-4o-mini",
        temperature: 0.1,
        zodSchema: userProfileSchema,
        schemaName: "UserProfile",
      });

      // Validate response
      expect(response).toBeDefined();
      expect(response.parsed).toBeDefined();

      // Type the parsed data
      const profile = response.parsed as z.infer<typeof userProfileSchema>;

      // Check the profile data
      expect(profile.name).toBe("Alex");
      expect(profile.age).toBe(29);
      expect(profile.interests).toContain("hiking");
      expect(profile.interests).toContain("photography");
    });
  });

  describe("Complex Nested Schema", () => {
    // Define address schema - simplified based on zodschema guidelines
    const addressSchema = z.object({
      street: z.string(),
      city: z.string(),
      state: z.string(),
      zip: z.string(),
      country: z.string(),
    });

    // Define order item schema - simplified
    const orderItemSchema = z.object({
      productId: z.string(),
      name: z.string(),
      quantity: z.number(),
      price: z.number(),
      subtotal: z.number(),
    });

    // Define the complete order schema - simplified
    const orderSchema = z.object({
      orderId: z.string(),
      customer: z.object({
        id: z.string(),
        name: z.string(),
        email: z.string(),
        address: addressSchema,
      }),
      items: z.array(orderItemSchema),
      totals: z.object({
        subtotal: z.number(),
        tax: z.number(),
        shipping: z.number(),
        total: z.number(),
      }),
      status: z.enum(["pending", "processing", "shipped", "delivered"]),
      orderDate: z.string(),
    });

    it("should correctly validate a complex nested schema", async () => {
      const response = await askLLMService.ask({
        system:
          "You are a helpful assistant. Always respond with valid JSON that matches the requested schema.",
        prompt: `Generate a fictional e-commerce order with the following requirements:
        - Order ID starts with ORD- followed by 6 digits
        - Include customer info with address (2-letter state code and valid ZIP)
        - At least 2 order items with product IDs in format PROD-###
        - Calculate proper subtotals and totals
        - Status should be one of: pending, processing, shipped, delivered
        - Order date in YYYY-MM-DD format
        
        Provide your response as valid JSON.`,
        model: "gpt-4o-mini",
        temperature: 0.1,
        zodSchema: orderSchema,
        schemaName: "Order",
      });

      // Validate response
      expect(response).toBeDefined();
      expect(response.parsed).toBeDefined();

      // Type the parsed data
      const order = response.parsed as z.infer<typeof orderSchema>;

      // Check order structure
      expect(order.orderId).toMatch(/^ORD-\d{6}$/);
      expect(order.customer.email).toContain("@");
      expect(order.customer.address.state.length).toBe(2);
      expect(order.customer.address.zip).toMatch(/^\d{5}(-\d{4})?$/);

      // Check items
      expect(order.items.length).toBeGreaterThanOrEqual(2);
      order.items.forEach((item) => {
        expect(item.productId).toMatch(/^PROD-\d+$/);
        expect(item.price * item.quantity).toBeCloseTo(item.subtotal);
      });

      // Check totals
      const calculatedSubtotal = order.items.reduce(
        (sum, item) => sum + item.subtotal,
        0,
      );
      expect(order.totals.subtotal).toBeCloseTo(calculatedSubtotal);
      expect(order.totals.total).toBeCloseTo(
        order.totals.subtotal + order.totals.tax + order.totals.shipping,
      );

      // Check other fields
      expect(["pending", "processing", "shipped", "delivered"]).toContain(
        order.status,
      );
      expect(order.orderDate).toMatch(/^\d{4}-\d{2}-\d{2}$/);
    });
  });

  describe("Schema with Transformations", () => {
    // Define a schema without transformations - simplified based on zodschema guidelines
    const userInputSchema = z.object({
      name: z.string(),
      email: z.string(),
      age: z.number(), // Changed from string with transform to plain number
      tags: z.array(z.string()), // Changed from string with transform to array
      isPremium: z.boolean(),
    });

    it("should correctly validate basic schema without transformations", async () => {
      const response = await askLLMService.ask({
        system: `You are a helpful assistant that creates user profiles.
                When given information about a user, you'll format it according to the required schema.
                Always respond with valid JSON.`,
        prompt: `Create a user profile with the following information:
                Name: John Smith
                Email: <EMAIL>
                Age: 30
                Tags: web development, JavaScript, React
                isPremium: true
                
                Format this as a JSON object.`,
        model: "gpt-4o-mini",
        temperature: 0.1,
        zodSchema: userInputSchema,
        schemaName: "UserInput",
      });

      // Validate response
      expect(response).toBeDefined();
      expect(response.parsed).toBeDefined();

      // Type the parsed data
      const userProfile = response.parsed as z.infer<typeof userInputSchema>;

      // Check data is correct
      expect(userProfile.name).toBe("John Smith");
      expect(userProfile.email).toBe("<EMAIL>");
      expect(userProfile.age).toBe(30);
      expect(Array.isArray(userProfile.tags)).toBe(true);
      expect(userProfile.isPremium).toBe(true);
    });
  });

  describe("Direct OpenAI Client Usage", () => {
    it("should use askLLMService with schema validation", async () => {
      // Define a simple schema - simplified based on zodschema guidelines
      const recipeSchema = z.object({
        title: z.string(),
        ingredients: z.array(z.string()),
        steps: z.array(z.string()),
        prepTime: z.number(),
        cookTime: z.number(),
        servings: z.number(),
      });

      // Use askLLMService instead of directly accessing OpenAI
      const response = await askLLMService.ask({
        system:
          "You are a helpful chef assistant that provides recipes. Always respond with valid JSON.",
        prompt:
          "Give me a simple recipe for chocolate chip cookies. Provide your response as JSON.",
        model: "gpt-4o-mini",
        temperature: 0.1,
        zodSchema: recipeSchema,
        schemaName: "Recipe",
      });

      // Check if we got a response
      expect(response).toBeDefined();
      expect(response.parsed).toBeDefined();

      // Type the parsed data
      const recipe = response.parsed as z.infer<typeof recipeSchema>;

      // Check structure
      expect(recipe.title.toLowerCase()).toContain("cookie");
      expect(recipe.ingredients.length).toBeGreaterThanOrEqual(3);
      expect(recipe.steps.length).toBeGreaterThanOrEqual(2);
      expect(recipe.prepTime).toBeGreaterThan(0);
      expect(recipe.cookTime).toBeGreaterThan(0);
      expect(recipe.servings).toBeGreaterThan(0);
    });
  });
});
