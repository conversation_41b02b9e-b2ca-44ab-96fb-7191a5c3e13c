import { Test } from "@nestjs/testing";
import { z } from "zod";
import { AskLLMService } from "../services/ask-llm.service";
import { OpenAIAskService } from "../services/openai-ask.service";
import { LlmProviderFactory } from "../services/llm-provider.factory";
import { PromptTemplateManager } from "../services/prompt-template-manager.service";
import {
  AskLLMParams,
  LlmMessage,
  LlmResponse,
  LlmToolCall,
  AssistantMessage,
  ToolMessage,
} from "../types/ask-llm.types";

describe("Tool Calling Tests", () => {
  let askLLMService: AskLLMService;
  let openAIProvider: OpenAIAskService;
  let mockProvider: any;

  // Define test tools
  const weatherTool = {
    type: "function" as const,
    function: {
      name: "get_weather",
      description: "Get the current weather in a location",
      parameters: {
        type: "object",
        properties: {
          location: {
            type: "string",
            description: "The city and state, e.g. San Francisco, CA",
          },
          unit: {
            type: "string",
            enum: ["celsius", "fahrenheit"],
            description: "The unit of temperature to use",
          },
        },
        required: ["location"],
      },
    },
  };

  const searchTool = {
    type: "function" as const,
    function: {
      name: "search_database",
      description: "Search for information in the database",
      parameters: {
        type: "object",
        properties: {
          query: {
            type: "string",
            description: "The search query",
          },
          limit: {
            type: "number",
            description: "Maximum number of results to return",
          },
        },
        required: ["query"],
      },
    },
  };

  beforeEach(async () => {
    // Create a mock provider that implements the LlmProvider interface
    mockProvider = {
      adaptMessages: jest.fn((messages) => messages),
      createCompletion: jest.fn(),
      createStructuredCompletion: jest.fn(),
      createCompletionStream: jest.fn(),
      createStructuredCompletionStream: jest.fn(),
    };

    const moduleRef = await Test.createTestingModule({
      providers: [
        AskLLMService,
        OpenAIAskService,
        {
          provide: LlmProviderFactory,
          useValue: {
            getProviderForModel: jest.fn(() => mockProvider),
          },
        },
        {
          provide: PromptTemplateManager,
          useValue: {
            renderTemplate: jest.fn((template, variables) => {
              // Simple template rendering implementation for tests
              let result = template;
              if (variables) {
                for (const [key, value] of Object.entries(variables)) {
                  result = result.replace(
                    new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, "g"),
                    value as string,
                  );
                }
              }
              return result;
            }),
          },
        },
        {
          provide: "LLM_MODULE_OPTIONS",
          useValue: { openaiApiKey: "mock-key" },
        },
      ],
    }).compile();

    askLLMService = moduleRef.get<AskLLMService>(AskLLMService);
    openAIProvider = moduleRef.get<OpenAIAskService>(OpenAIAskService);

    // Replace the openAIProvider's adaptMessages method with a spy
    jest.spyOn(openAIProvider, "adaptMessages");
  });

  describe("OpenAIAskService tool call adaptation", () => {
    it("should correctly adapt messages with tool calls to OpenAI format", () => {
      // Arrange
      const messages: LlmMessage[] = [
        { role: "system", content: "You are a helpful assistant." },
        { role: "user", content: "What's the weather in New York?" },
        {
          role: "assistant",
          content: null,
          tool_calls: [
            {
              id: "call_123",
              type: "function",
              function: {
                name: "get_weather",
                arguments: JSON.stringify({ location: "New York" }),
              },
            },
          ],
        },
        {
          role: "tool",
          content: "The weather in New York is 75°F and sunny.",
          name: "get_weather",
          tool_call_id: "call_123",
        },
      ];

      // Act
      const adapted = openAIProvider.adaptMessages(messages);

      // Assert
      expect(adapted).toHaveLength(4);

      // Check system message
      expect(adapted[0].role).toBe("system");
      expect(adapted[0].content).toBe("You are a helpful assistant.");

      // Check user message
      expect(adapted[1].role).toBe("user");
      expect(adapted[1].content).toBe("What's the weather in New York?");

      // Check assistant message with tool calls
      expect(adapted[2].role).toBe("assistant");
      expect(adapted[2].content).toBe(null);

      // The OpenAI SDK expects tool_calls in a specific format
      const assistantMessage = adapted[2] as any;
      expect(assistantMessage.tool_calls).toBeDefined();
      expect(assistantMessage.tool_calls).toHaveLength(1);
      expect(assistantMessage.tool_calls[0].id).toBe("call_123");
      expect(assistantMessage.tool_calls[0].type).toBe("function");
      expect(assistantMessage.tool_calls[0].function.name).toBe("get_weather");
      expect(assistantMessage.tool_calls[0].function.arguments).toBe(
        JSON.stringify(JSON.stringify({ location: "New York" })),
      );

      // Check tool response message
      const toolMessage = adapted[3] as any;
      expect(toolMessage.role).toBe("tool");
      expect(toolMessage.content).toBe(
        "The weather in New York is 75°F and sunny.",
      );
      expect(toolMessage.tool_call_id).toBe("call_123");
    });
  });

  describe("OpenAI response adaptation with tool calls", () => {
    it("should correctly extract tool calls from OpenAI response", () => {
      // Create a mock OpenAI response with tool calls
      const mockOpenAIResponse = {
        id: "chatcmpl-123",
        model: "gpt-4o-mini",
        created: **********,
        choices: [
          {
            message: {
              role: "assistant",
              content: null,
              tool_calls: [
                {
                  id: "call_abc123",
                  type: "function",
                  function: {
                    name: "get_weather",
                    arguments: '{"location":"San Francisco","unit":"celsius"}',
                  },
                },
              ],
            },
            finish_reason: "tool_calls",
          },
        ],
        usage: {
          prompt_tokens: 100,
          completion_tokens: 50,
          total_tokens: 150,
        },
      };

      // Set up the mock to return our prepared response
      mockProvider.createCompletion.mockResolvedValue(
        openAIProvider["adaptOpenAIResponse"](mockOpenAIResponse),
      );

      // Act - call the service through the ask method
      return askLLMService
        .ask({
          system: "You are a helpful assistant that can check the weather.",
          prompt: "What's the weather like in San Francisco?",
          model: "gpt-4o-mini",
          tools: [weatherTool],
        })
        .then((result) => {
          // Assert
          expect(result).toBeDefined();
          expect(result.id).toBe("chatcmpl-123");
          expect(result.model).toBe("gpt-4o-mini");

          // Check that tool calls are correctly extracted
          expect(result.toolCalls).toBeDefined();
          expect(result.toolCalls).toHaveLength(1);
          expect(result.toolCalls![0].function.name).toBe("get_weather");

          // Verify the arguments are parsed correctly
          const args = JSON.parse(result.toolCalls![0].function.arguments);
          expect(args.location).toBe("San Francisco");
          expect(args.unit).toBe("celsius");

          // Check the message format
          expect(result.message.role).toBe("assistant");
          expect(result.message.content).toBeNull();
          expect((result.message as AssistantMessage).tool_calls).toEqual(
            result.toolCalls,
          );

          // Check that finish reason is set correctly
          expect(result.finishReason).toBe("tool_calls");
        });
    });
  });

  describe("AskLLMService with tools", () => {
    it("should properly pass tools to the provider", () => {
      // Set up the mock to return a basic response
      mockProvider.createCompletion.mockResolvedValue({
        id: "chatcmpl-123",
        model: "gpt-4o-mini",
        message: {
          role: "assistant",
          content: "I'll check the weather for you.",
        },
      });

      // Act
      return askLLMService
        .ask({
          system: "You are a helpful assistant that can check the weather.",
          prompt: "What's the weather like in San Francisco?",
          model: "gpt-4o-mini",
          tools: [weatherTool, searchTool],
        })
        .then(() => {
          // Assert that createCompletion was called with the tools
          expect(mockProvider.createCompletion).toHaveBeenCalledWith(
            expect.objectContaining({
              tools: [weatherTool, searchTool],
            }),
          );
        });
    });

    it("should handle a complete tool conversation flow", async () => {
      // Mock the first response with tool calls
      const mockToolCallResponse = {
        id: "chatcmpl-tool-call",
        model: "gpt-4o-mini",
        message: {
          role: "assistant",
          content: null,
          tool_calls: [
            {
              id: "call_weather_123",
              type: "function",
              function: {
                name: "get_weather",
                arguments: '{"location":"London","unit":"celsius"}',
              },
            },
          ],
        },
        finishReason: "tool_calls",
        toolCalls: [
          {
            id: "call_weather_123",
            type: "function",
            function: {
              name: "get_weather",
              arguments: '{"location":"London","unit":"celsius"}',
            },
          },
        ],
      };

      // Mock the final response after tool results
      const mockFinalResponse = {
        id: "chatcmpl-final",
        model: "gpt-4o-mini",
        message: {
          role: "assistant",
          content: "The weather in London is currently 15°C and cloudy.",
        },
        finishReason: "stop",
      };

      // Set up the mock to return our tool call response first, then the final response
      mockProvider.createCompletion
        .mockResolvedValueOnce(mockToolCallResponse)
        .mockResolvedValueOnce(mockFinalResponse);

      // First request - user asks about weather
      const initialResponse = await askLLMService.ask({
        system: "You are a helpful assistant that can check the weather.",
        prompt: "What's the weather like in London?",
        model: "gpt-4o-mini",
        tools: [weatherTool],
      });

      // Verify tool call response
      expect(initialResponse.toolCalls).toBeDefined();
      expect(initialResponse.toolCalls![0].function.name).toBe("get_weather");

      // Extract the tool call details
      const toolCall = initialResponse.toolCalls![0];
      const toolCallId = toolCall.id;
      const toolName = toolCall.function.name;
      const toolArgs = JSON.parse(toolCall.function.arguments);

      expect(toolArgs.location).toBe("London");
      expect(toolArgs.unit).toBe("celsius");

      // Simulate getting the weather data (in a real app, this would call an actual API)
      const weatherData = "15°C and cloudy";

      // Create a follow-up request with the tool response
      const messages: LlmMessage[] = [
        {
          role: "system",
          content: "You are a helpful assistant that can check the weather.",
        },
        { role: "user", content: "What's the weather like in London?" },
        {
          role: "assistant" as const,
          content: null,
          tool_calls: initialResponse.toolCalls,
        } as AssistantMessage,
        {
          role: "tool" as const,
          name: toolName,
          tool_call_id: toolCallId,
          content: weatherData,
        } as ToolMessage,
      ];

      // Send the follow-up with the tool results
      const finalResponse = await askLLMService.ask({
        messages,
        model: "gpt-4o-mini",
        tools: [weatherTool],
      });

      // Verify the final response
      expect(finalResponse.message.content).toBe(
        "The weather in London is currently 15°C and cloudy.",
      );
      expect(finalResponse.finishReason).toBe("stop");

      // Verify the correct messages were passed to the provider
      expect(mockProvider.createCompletion).toHaveBeenCalledTimes(2);

      // Check the second call included our tool response
      const secondCallArgs = mockProvider.createCompletion.mock.calls[1][0];
      expect(secondCallArgs.messages).toHaveLength(4);
      expect(secondCallArgs.messages[3].role).toBe("tool");
      expect(secondCallArgs.messages[3].content).toBe(weatherData);
    });
  });

  describe("Zod schema validation with tool calls", () => {
    it("should validate tool call parameters with Zod schemas", async () => {
      // Define a schema for the tool parameters
      const WeatherParamsSchema = z.object({
        location: z.string(),
        unit: z.enum(["celsius", "fahrenheit"]).optional(),
      });

      // Mock the OpenAI response with a tool call
      const mockResponse = {
        id: "chatcmpl-123",
        model: "gpt-4o-mini",
        created: **********,
        choices: [
          {
            message: {
              role: "assistant",
              content: null,
              tool_calls: [
                {
                  id: "call_abc123",
                  type: "function",
                  function: {
                    name: "get_weather",
                    arguments: '{"location":"Paris","unit":"celsius"}',
                  },
                },
              ],
            },
            finish_reason: "tool_calls",
          },
        ],
        usage: {
          prompt_tokens: 100,
          completion_tokens: 50,
          total_tokens: 150,
        },
      };

      // Adapt the mock response
      const adaptedResponse =
        openAIProvider["adaptOpenAIResponse"](mockResponse);
      mockProvider.createCompletion.mockResolvedValue(adaptedResponse);

      // Make the request with a tool that has a Zod schema
      const weatherToolWithSchema = {
        ...weatherTool,
        // Add a function to validate parameters with Zod
        validateParameters: (params: any) => {
          return WeatherParamsSchema.parse(params);
        },
      };

      const result = await askLLMService.ask({
        system: "You are a helpful assistant that can check the weather.",
        prompt: "What's the weather like in Paris?",
        model: "gpt-4o-mini",
        tools: [weatherToolWithSchema],
      });

      // Verify we got tool calls in the response
      expect(result.toolCalls).toBeDefined();
      expect(result.toolCalls).toHaveLength(1);

      // Extract and parse the tool call arguments
      const toolCall = result.toolCalls![0];
      const args = JSON.parse(toolCall.function.arguments);

      // Validate the arguments with our schema
      const validatedArgs = WeatherParamsSchema.parse(args);
      expect(validatedArgs.location).toBe("Paris");
      expect(validatedArgs.unit).toBe("celsius");
    });
  });
});
