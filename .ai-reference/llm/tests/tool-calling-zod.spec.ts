import { Test } from "@nestjs/testing";
import { z } from "zod";
import { AskLLMService } from "../services/ask-llm.service";
import { OpenAIAskService } from "../services/openai-ask.service";
import { LlmProviderFactory } from "../services/llm-provider.factory";
import { PromptTemplateManager } from "../services/prompt-template-manager.service";
import {
  AskLLMParams,
  LlmMessage,
  LlmResponse,
  LlmToolCall,
  AssistantMessage,
  ToolMessage,
} from "../types/ask-llm.types";

describe("Tool Calling with Zod Schema Tests", () => {
  let askLLMService: AskLLMService;
  let openAIProvider: OpenAIAskService;
  let mockProvider: any;

  // Define weather schema with validation
  const WeatherParamsSchema = z.object({
    location: z.string().min(2).max(100),
    unit: z.enum(["celsius", "fahrenheit"]).optional().default("celsius"),
    detailed: z.boolean().optional().default(false),
  });

  // Type for the validated parameters
  type WeatherParams = z.infer<typeof WeatherParamsSchema>;

  // Define a tool with schema validation
  const weatherTool = {
    type: "function" as const,
    function: {
      name: "get_weather",
      description: "Get the current weather in a location",
      parameters: {
        type: "object",
        properties: {
          location: {
            type: "string",
            description: "The city and state, e.g. San Francisco, CA",
          },
          unit: {
            type: "string",
            enum: ["celsius", "fahrenheit"],
            description: "The unit of temperature to use",
          },
          detailed: {
            type: "boolean",
            description: "Whether to include detailed weather information",
          },
        },
        required: ["location"],
      },
    },
    validateParameters: (params: any): WeatherParams => {
      return WeatherParamsSchema.parse(params);
    },
  };

  beforeEach(async () => {
    // Create a mock provider
    mockProvider = {
      adaptMessages: jest.fn((messages) => messages),
      createCompletion: jest.fn(),
      createStructuredCompletion: jest.fn(),
      createCompletionStream: jest.fn(),
      createStructuredCompletionStream: jest.fn(),
    };

    const moduleRef = await Test.createTestingModule({
      providers: [
        AskLLMService,
        OpenAIAskService,
        {
          provide: LlmProviderFactory,
          useValue: {
            getProviderForModel: jest.fn(() => mockProvider),
          },
        },
        {
          provide: PromptTemplateManager,
          useValue: {
            renderTemplate: jest.fn((template, variables) => {
              // Simple template rendering implementation for tests
              let result = template;
              if (variables) {
                for (const [key, value] of Object.entries(variables)) {
                  result = result.replace(
                    new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, "g"),
                    value as string,
                  );
                }
              }
              return result;
            }),
          },
        },
        {
          provide: "LLM_MODULE_OPTIONS",
          useValue: { openaiApiKey: "mock-key" },
        },
      ],
    }).compile();

    askLLMService = moduleRef.get<AskLLMService>(AskLLMService);
    openAIProvider = moduleRef.get<OpenAIAskService>(OpenAIAskService);
  });

  describe("Tool parameter validation", () => {
    it("should validate tool parameters with Zod schema", async () => {
      // Mock a response with tool calls
      const mockOpenAIResponse = {
        id: "chatcmpl-123",
        model: "gpt-4o-mini",
        created: **********,
        choices: [
          {
            message: {
              role: "assistant",
              content: null,
              tool_calls: [
                {
                  id: "call_123",
                  type: "function",
                  function: {
                    name: "get_weather",
                    arguments: '{"location":"Berlin","unit":"celsius"}',
                  },
                },
              ],
            },
            finish_reason: "tool_calls",
          },
        ],
        usage: {
          prompt_tokens: 100,
          completion_tokens: 50,
          total_tokens: 150,
        },
      };

      // Set up the mock to return our prepared response
      mockProvider.createCompletion.mockResolvedValue(
        openAIProvider["adaptOpenAIResponse"](mockOpenAIResponse),
      );

      // Call the service
      const result = await askLLMService.ask({
        system: "You are a helpful assistant that can check the weather.",
        prompt: "What's the weather like in Berlin?",
        model: "gpt-4o-mini",
        tools: [weatherTool],
      });

      // Verify we got tool calls in the response
      expect(result.toolCalls).toBeDefined();
      expect(result.toolCalls).toHaveLength(1);

      // Extract and parse the arguments
      const args = JSON.parse(result.toolCalls![0].function.arguments);

      // Apply schema validation
      const validatedArgs = weatherTool.validateParameters(args);

      // Verify arguments match our schema expectations
      expect(validatedArgs.location).toBe("Berlin");
      expect(validatedArgs.unit).toBe("celsius");
      expect(validatedArgs.detailed).toBe(false); // Default from the schema
    });

    it("should handle invalid parameters with proper error messages", async () => {
      // Mock a response with tool calls that have invalid arguments
      const mockOpenAIResponse = {
        id: "chatcmpl-123",
        model: "gpt-4o-mini",
        created: **********,
        choices: [
          {
            message: {
              role: "assistant",
              content: null,
              tool_calls: [
                {
                  id: "call_123",
                  type: "function",
                  function: {
                    name: "get_weather",
                    arguments: '{"location":"B","unit":"kelvin"}', // Invalid location & unit
                  },
                },
              ],
            },
            finish_reason: "tool_calls",
          },
        ],
        usage: {
          prompt_tokens: 100,
          completion_tokens: 50,
          total_tokens: 150,
        },
      };

      // Set up the mock to return our prepared response
      mockProvider.createCompletion.mockResolvedValue(
        openAIProvider["adaptOpenAIResponse"](mockOpenAIResponse),
      );

      // Call the service
      const result = await askLLMService.ask({
        system: "You are a helpful assistant that can check the weather.",
        prompt: "What's the weather like in Berlin?",
        model: "gpt-4o-mini",
        tools: [weatherTool],
      });

      // Verify we got tool calls in the response
      expect(result.toolCalls).toBeDefined();

      // Extract the arguments to validate manually
      const args = JSON.parse(result.toolCalls![0].function.arguments);

      // Verify validation fails appropriately
      try {
        weatherTool.validateParameters(args);
        fail("Validation should have failed");
      } catch (error) {
        expect(error).toBeDefined();

        // Check for specific validation errors
        const zodError = error as z.ZodError;
        expect(zodError.errors).toHaveLength(2);

        // Check location error (too short)
        const locationError = zodError.errors.find((e) =>
          e.path.includes("location"),
        );
        expect(locationError).toBeDefined();
        expect(locationError!.message).toContain("at least 2");

        // Check unit error (invalid enum value)
        const unitError = zodError.errors.find((e) => e.path.includes("unit"));
        expect(unitError).toBeDefined();
        expect(unitError!.message).toContain("Invalid enum value");
      }
    });

    it("should handle and transform arguments according to the schema", async () => {
      // Mock a response with tool calls that need transformation
      const mockOpenAIResponse = {
        id: "chatcmpl-123",
        model: "gpt-4o-mini",
        created: **********,
        choices: [
          {
            message: {
              role: "assistant",
              content: null,
              tool_calls: [
                {
                  id: "call_123",
                  type: "function",
                  function: {
                    name: "get_weather",
                    arguments: '{"location":"Tokyo","detailed":1}', // detailed as number instead of boolean
                  },
                },
              ],
            },
            finish_reason: "tool_calls",
          },
        ],
        usage: {
          prompt_tokens: 100,
          completion_tokens: 50,
          total_tokens: 150,
        },
      };

      // Set up the mock to return our prepared response
      mockProvider.createCompletion.mockResolvedValue(
        openAIProvider["adaptOpenAIResponse"](mockOpenAIResponse),
      );

      // Extend the schema to handle the transformation
      const EnhancedWeatherSchema = WeatherParamsSchema.extend({
        detailed: z
          .union([
            z.boolean(),
            z.number().transform((n) => Boolean(n)), // Transform numbers to boolean
            z.string().transform((s) => s.toLowerCase() === "true"), // Transform strings to boolean
          ])
          .optional()
          .default(false),
      });

      // Create enhanced tool with the new schema
      const enhancedWeatherTool = {
        ...weatherTool,
        validateParameters: (params: any) => {
          return EnhancedWeatherSchema.parse(params);
        },
      };

      // Call the service with the enhanced tool
      const result = await askLLMService.ask({
        system: "You are a helpful assistant that can check the weather.",
        prompt: "What's the detailed weather like in Tokyo?",
        model: "gpt-4o-mini",
        tools: [enhancedWeatherTool],
      });

      // Verify we got tool calls in the response
      expect(result.toolCalls).toBeDefined();

      // Extract the arguments to validate with the enhanced schema
      const args = JSON.parse(result.toolCalls![0].function.arguments);

      // Apply enhanced schema validation with transformations
      const validatedArgs = enhancedWeatherTool.validateParameters(args);

      // Verify transformations were applied correctly
      expect(validatedArgs.location).toBe("Tokyo");
      expect(validatedArgs.unit).toBe("celsius"); // Default value
      expect(validatedArgs.detailed).toBe(true); // Transformed from 1 to true
      expect(typeof validatedArgs.detailed).toBe("boolean"); // Ensure it's actually a boolean
    });
  });

  describe("Integration with tool execution flow", () => {
    it("should validate parameters before executing tool functions", async () => {
      // Mock the initial response with tool calls
      const mockToolCallResponse = {
        id: "chatcmpl-tool-call",
        model: "gpt-4o-mini",
        message: {
          role: "assistant",
          content: null,
          tool_calls: [
            {
              id: "call_weather_123",
              type: "function",
              function: {
                name: "get_weather",
                arguments:
                  '{"location":"Rome","unit":"celsius","detailed":true}',
              },
            },
          ],
        },
        finishReason: "tool_calls",
        toolCalls: [
          {
            id: "call_weather_123",
            type: "function",
            function: {
              name: "get_weather",
              arguments: '{"location":"Rome","unit":"celsius","detailed":true}',
            },
          },
        ],
      };

      // Mock the final response after tool results
      const mockFinalResponse = {
        id: "chatcmpl-final",
        model: "gpt-4o-mini",
        message: {
          role: "assistant",
          content:
            "The detailed weather in Rome is 25°C, 45% humidity, with clear skies.",
        },
        finishReason: "stop",
      };

      // Set up the mock to return our tool call response first, then the final response
      mockProvider.createCompletion
        .mockResolvedValueOnce(mockToolCallResponse)
        .mockResolvedValueOnce(mockFinalResponse);

      // Create a mock weather function that uses the validated parameters
      const getWeather = jest.fn((params: WeatherParams) => {
        // Return different responses based on whether detailed is true
        if (params.detailed) {
          return `${params.unit === "celsius" ? "25°C" : "77°F"}, 45% humidity, with clear skies`;
        } else {
          return `${params.unit === "celsius" ? "25°C" : "77°F"} and sunny`;
        }
      });

      // First request - user asks about weather
      const initialResponse = await askLLMService.ask({
        system: "You are a helpful assistant that can check the weather.",
        prompt: "What's the detailed weather like in Rome?",
        model: "gpt-4o-mini",
        tools: [weatherTool],
      });

      // Verify tool call response
      expect(initialResponse.toolCalls).toBeDefined();
      expect(initialResponse.toolCalls![0].function.name).toBe("get_weather");

      // Extract the tool call details
      const toolCall = initialResponse.toolCalls![0];
      const toolCallId = toolCall.id;
      const toolName = toolCall.function.name;
      const toolArgsRaw = JSON.parse(toolCall.function.arguments);

      // Validate parameters before executing the function
      try {
        const validatedParams = weatherTool.validateParameters(toolArgsRaw);

        // Verify parameters were validated correctly
        expect(validatedParams.location).toBe("Rome");
        expect(validatedParams.unit).toBe("celsius");
        expect(validatedParams.detailed).toBe(true);

        // "Execute" the weather function with validated parameters
        const weatherData = getWeather(validatedParams);

        // Create follow-up request with tool response
        const messages: LlmMessage[] = [
          {
            role: "system",
            content: "You are a helpful assistant that can check the weather.",
          },
          {
            role: "user",
            content: "What's the detailed weather forecast for Berlin?",
          },
          {
            role: "assistant" as const,
            content: null,
            tool_calls: initialResponse.toolCalls,
          } as AssistantMessage,
          {
            role: "tool" as const,
            name: toolName,
            tool_call_id: toolCallId,
            content: weatherData,
          } as ToolMessage,
        ];

        // Send the follow-up with the tool results
        const finalResponse = await askLLMService.ask({
          messages,
          model: "gpt-4o-mini",
        });

        // Verify the final response contains the detailed weather information
        expect(finalResponse.message.content).toBe(
          "The detailed weather in Rome is 25°C, 45% humidity, with clear skies.",
        );

        // Verify the weather function was called with the right parameters
        expect(getWeather).toHaveBeenCalledWith({
          location: "Rome",
          unit: "celsius",
          detailed: true,
        });
      } catch (error) {
        fail(`Parameter validation should not have failed: ${error}`);
      }
    });
  });
});
