import { Test } from "@nestjs/testing";
import { AskLLMService } from "../services/ask-llm.service";
import { LlmModule } from "../llm.module";
import { z } from "zod";
import * as dotenv from "dotenv";
import {
  AssistantMessage,
  ToolMessage,
  LlmToolCall,
} from "../types/ask-llm.types";

// Load environment variables
dotenv.config();

/**
 * REAL API TESTS FOR TOOL CALLING WITH ZOD VALIDATION
 *
 * These tests use the actual OpenAI API with tool calling features and Zod validation.
 * They will incur costs and should be run sparingly.
 */
describe("Tool Calling with Zod Schema - Real API Tests", () => {
  let askLLMService: AskLLMService;

  // Timeout set to 60 seconds as complex API calls can take time
  jest.setTimeout(60000);

  beforeAll(async () => {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error(
        "OPENAI_API_KEY environment variable is not set. These tests require a valid API key.",
      );
    }

    const moduleRef = await Test.createTestingModule({
      imports: [
        LlmModule.register({
          openaiApiKey: process.env.OPENAI_API_KEY,
          defaultProvider: "openai",
        }),
      ],
    }).compile();

    askLLMService = moduleRef.get<AskLLMService>(AskLLMService);
  });

  describe("Weather Tool with Validation", () => {
    // Define the Zod schema for validating weather parameters
    const WeatherParamsSchema = z.object({
      location: z
        .string()
        .min(2)
        .max(100)
        .transform((val) => val.trim()),
      unit: z.enum(["celsius", "fahrenheit"]).optional().default("celsius"),
      detailed: z.boolean().optional().default(false),
    });

    // Type for validated parameters
    type WeatherParams = z.infer<typeof WeatherParamsSchema>;

    // Define the weather tool with schema validation
    const weatherTool = {
      type: "function" as const,
      function: {
        name: "get_weather",
        description: "Get the current weather in a location",
        parameters: {
          type: "object",
          properties: {
            location: {
              type: "string",
              description: "The city and state, e.g. San Francisco, CA",
            },
            unit: {
              type: "string",
              enum: ["celsius", "fahrenheit"],
              description: "The unit of temperature to use",
            },
            detailed: {
              type: "boolean",
              description: "Whether to include detailed weather information",
            },
          },
          required: ["location"],
        },
      },
      // Add validation function
      validateParameters: (params: any): WeatherParams => {
        return WeatherParamsSchema.parse(params);
      },
    };

    it("should call the weather tool with valid parameters", async () => {
      const response = await askLLMService.ask({
        system:
          "You are a helpful assistant that checks weather for users. Always use the get_weather tool when a user asks about weather.",
        prompt: "What's the current temperature in Berlin, Germany?",
        model: "gpt-4o-mini",
        temperature: 0.1,
        tools: [weatherTool],
      });

      // Check if we got a response
      expect(response).toBeDefined();
      expect(response.message).toBeDefined();

      // Cast to correct type
      const message = response.message as AssistantMessage;

      // Check if tool calls were made
      expect(message.tool_calls).toBeDefined();
      expect(message.tool_calls?.length).toBeGreaterThan(0);

      // Get the first tool call
      const toolCall = message.tool_calls?.[0] as LlmToolCall;

      // Check tool call structure
      expect(toolCall.function).toBeDefined();
      expect(toolCall.function.name).toBe("get_weather");

      // Parse and validate the arguments
      const args = JSON.parse(toolCall.function.arguments);

      // Should be valid according to our schema
      const validatedArgs = WeatherParamsSchema.safeParse(args);
      expect(validatedArgs.success).toBe(true);

      if (validatedArgs.success) {
        // Check specific parameters
        expect(validatedArgs.data.location.toLowerCase()).toContain("berlin");
        expect(["celsius", "fahrenheit"]).toContain(validatedArgs.data.unit);
      }
    });

    it("should respond to weather tool calls with a custom message", async () => {
      // First, get a tool call
      const initialResponse = await askLLMService.ask({
        system:
          "You are a helpful assistant that checks weather for users. Always use the get_weather tool when a user asks about weather.",
        prompt: "How hot is it in Tokyo today?",
        model: "gpt-4o-mini",
        temperature: 0.1,
        tools: [weatherTool],
      });

      // Verify we got a tool call
      const message = initialResponse.message as AssistantMessage;
      expect(message.tool_calls).toBeDefined();
      expect(message.tool_calls?.length).toBeGreaterThan(0);

      // Get the first tool call
      const toolCall = message.tool_calls?.[0] as LlmToolCall;

      // Now create a messages array to simulate a tool response
      const messages = [
        {
          role: "system" as const,
          content: "You are a helpful assistant that checks weather for users.",
        },
        {
          role: "user" as const,
          content: "How hot is it in Tokyo today?",
        },
        {
          role: "assistant" as const,
          content: null,
          tool_calls: [
            {
              id: toolCall.id,
              type: "function" as const,
              function: {
                name: "get_weather",
                arguments: toolCall.function.arguments,
              },
            },
          ],
        },
        {
          role: "tool" as const,
          tool_call_id: toolCall.id,
          content: JSON.stringify({
            temperature: 28,
            conditions: "Sunny with some clouds",
            humidity: "65%",
          }),
        },
      ];

      // Now send this conversation back to the model
      const followUpResponse = await askLLMService.ask({
        messages,
        model: "gpt-4o-mini",
        temperature: 0.1,
      });

      // Check that we got a proper response using the tool results
      expect(followUpResponse).toBeDefined();
      expect(followUpResponse.message).toBeDefined();
      expect(followUpResponse.message.content).toBeDefined();

      // The response should mention the weather details we provided
      const content = followUpResponse.message.content || "";
      expect(content).toContain("28");
      // Check if content is a string before using toLowerCase
      if (typeof content === "string") {
        expect(content.toLowerCase()).toContain("tokyo");
      } else {
        // If it's a ContentPart[], look for "tokyo" in any part
        const contentParts = content as any[];
        expect(
          contentParts.some(
            (part) =>
              typeof part.text === "string" &&
              part.text.toLowerCase().includes("tokyo"),
          ),
        ).toBeTruthy();
      }
    });
  });

  describe("Multiple Tools with Zod Validation", () => {
    // Search database tool
    const SearchParamsSchema = z.object({
      query: z.string().min(1).max(100),
      limit: z.number().int().min(1).max(100).optional().default(10),
    });

    // User info tool
    const UserInfoParamsSchema = z.object({
      userId: z.string().regex(/^user_\d+$/),
    });

    // Define the tools
    const searchTool = {
      type: "function" as const,
      function: {
        name: "search_database",
        description: "Search for information in the database",
        parameters: {
          type: "object",
          properties: {
            query: {
              type: "string",
              description: "The search query",
            },
            limit: {
              type: "number",
              description: "Maximum number of results to return",
            },
          },
          required: ["query"],
        },
      },
      validateParameters: (params: any) => {
        return SearchParamsSchema.parse(params);
      },
    };

    const userInfoTool = {
      type: "function" as const,
      function: {
        name: "get_user_info",
        description: "Get information about a user",
        parameters: {
          type: "object",
          properties: {
            userId: {
              type: "string",
              description: "The user ID in format user_123",
            },
          },
          required: ["userId"],
        },
      },
      validateParameters: (params: any) => {
        return UserInfoParamsSchema.parse(params);
      },
    };

    it("should select and call the appropriate tool", async () => {
      const response = await askLLMService.ask({
        system:
          "You are a helpful assistant with access to a database and user information. Use the appropriate tool when needed.",
        prompt: "Find information about artificial intelligence",
        model: "gpt-4o-mini",
        temperature: 0.1,
        tools: [searchTool, userInfoTool],
      });

      // Verify response
      expect(response).toBeDefined();

      // Cast to correct type
      const message = response.message as AssistantMessage;

      // Check if tool calls were made
      if (message.tool_calls) {
        expect(message.tool_calls.length).toBeGreaterThan(0);

        // Get the first tool call
        const toolCall = message.tool_calls[0];

        // For this query, search_database should be called
        expect(toolCall.function.name).toBe("search_database");

        // Parse arguments
        const args = JSON.parse(toolCall.function.arguments);

        // Validate against our schema
        const validatedArgs = SearchParamsSchema.safeParse(args);
        expect(validatedArgs.success).toBe(true);

        if (validatedArgs.success) {
          // Query should be about AI
          expect(validatedArgs.data.query.toLowerCase()).toContain(
            "artificial intelligence",
          );
        }
      }
    });

    it("should validate tool parameters and reject invalid ones", async () => {
      // We need to create a test function to manually validate parameters
      // since the LLM won't intentionally generate invalid parameters

      // This is a helper function to test our parameter validation logic
      const testValidation = (params: any, schema: z.ZodType<any>) => {
        try {
          const result = schema.parse(params);
          return { success: true, result };
        } catch (error) {
          return { success: false, error };
        }
      };

      // Test valid parameters
      const validSearch = testValidation(
        { query: "artificial intelligence", limit: 20 },
        SearchParamsSchema,
      );
      expect(validSearch.success).toBe(true);

      // Test invalid parameters (limit too high)
      const invalidLimit = testValidation(
        { query: "AI", limit: 500 },
        SearchParamsSchema,
      );
      expect(invalidLimit.success).toBe(false);

      // Test valid user ID format
      const validUserId = testValidation(
        { userId: "user_123" },
        UserInfoParamsSchema,
      );
      expect(validUserId.success).toBe(true);

      // Test invalid user ID format
      const invalidUserId = testValidation(
        { userId: "invalid-format" },
        UserInfoParamsSchema,
      );
      expect(invalidUserId.success).toBe(false);
    });
  });
});
