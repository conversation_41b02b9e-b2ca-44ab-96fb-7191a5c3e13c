import { z } from "zod";
import {
  validateSchemaForStructuredOutput,
  IncompatibleSchemaError,
} from "../utils/schema-validator";

describe("Schema Validator", () => {
  describe("validateSchemaForStructuredOutput", () => {
    it("should pass valid simple schemas", () => {
      // Simple valid schema
      const validSchema = z.object({
        name: z.string(),
        age: z.number(),
        isActive: z.boolean(),
        tags: z.array(z.string()),
      });

      const result = validateSchemaForStructuredOutput(validSchema);
      expect(result.isCompatible).toBe(true);
      expect(result.issues.length).toBe(0);
    });

    it("should pass valid nested schemas", () => {
      // Nested valid schema
      const validNestedSchema = z.object({
        user: z.object({
          name: z.string(),
          address: z.object({
            street: z.string(),
            city: z.string(),
            country: z.string(),
          }),
        }),
        orders: z.array(
          z.object({
            id: z.string(),
            amount: z.number(),
          }),
        ),
      });

      const result = validateSchemaForStructuredOutput(validNestedSchema);
      expect(result.isCompatible).toBe(true);
      expect(result.issues.length).toBe(0);
    });

    it("should fail on string validation methods", () => {
      // Schema with string validation methods
      const invalidStringSchema = z.object({
        name: z.string().min(3).max(50),
        email: z.string().email(),
        username: z.string().regex(/^[a-z0-9]+$/),
      });

      const result = validateSchemaForStructuredOutput(invalidStringSchema);
      expect(result.isCompatible).toBe(false);
      expect(result.issues.length).toBeGreaterThan(0);
      // Check if issues include string validation mentions
      expect(
        result.issues.some((issue) =>
          issue.includes("string validation methods"),
        ),
      ).toBe(true);
    });

    it("should fail on number validation methods", () => {
      // Schema with number validation methods
      const invalidNumberSchema = z.object({
        age: z.number().int().positive(),
        score: z.number().min(0).max(100),
      });

      const result = validateSchemaForStructuredOutput(invalidNumberSchema);
      expect(result.isCompatible).toBe(false);
      expect(result.issues.length).toBeGreaterThan(0);
      // Check if issues include number validation mentions
      expect(
        result.issues.some((issue) =>
          issue.includes("number validation methods"),
        ),
      ).toBe(true);
    });

    it("should fail on array validation methods", () => {
      // Schema with array validation methods
      const invalidArraySchema = z.object({
        tags: z.array(z.string()).min(1).max(5),
        items: z.array(z.number()).nonempty(),
      });

      const result = validateSchemaForStructuredOutput(invalidArraySchema);
      expect(result.isCompatible).toBe(false);
      expect(result.issues.length).toBeGreaterThan(0);
      // Check if issues include array validation mentions
      expect(
        result.issues.some((issue) =>
          issue.includes("array validation methods"),
        ),
      ).toBe(true);
    });

    it("should fail on refinements", () => {
      // Schema with refinements
      const invalidRefinementSchema = z.object({
        value: z.number().refine((n) => n > 0, {
          message: "Value must be positive",
        }),
      });

      const result = validateSchemaForStructuredOutput(invalidRefinementSchema);
      expect(result.isCompatible).toBe(false);
      expect(result.issues.length).toBeGreaterThan(0);
      // Check if issues include refinement mentions
      expect(result.issues.some((issue) => issue.includes("refinements"))).toBe(
        true,
      );
    });

    it("should fail on transformations", () => {
      // Schema with transformations
      const invalidTransformSchema = z.object({
        name: z.string().transform((s) => s.trim()),
        age: z.number().transform((n) => n.toString()),
      });

      // Use any to bypass TypeScript's type checking for transformed schemas
      const result = validateSchemaForStructuredOutput(
        invalidTransformSchema as any,
      );
      expect(result.isCompatible).toBe(false);
      expect(result.issues.length).toBeGreaterThan(0);
      // Check if issues include transformation mentions
      expect(
        result.issues.some((issue) => issue.includes("transformations")),
      ).toBe(true);
    });

    it("should handle nested issues", () => {
      // Schema with deeply nested issues
      const complexInvalidSchema = z.object({
        user: z.object({
          name: z.string(),
          age: z.number().int().positive(),
          address: z.object({
            street: z.string(),
            city: z.string(),
            zipCode: z.string().regex(/^\d{5}$/),
          }),
        }),
        orders: z.array(
          z.object({
            id: z.string(),
            items: z.array(z.string()).min(1),
          }),
        ),
      });

      const result = validateSchemaForStructuredOutput(complexInvalidSchema);
      expect(result.isCompatible).toBe(false);
      expect(result.issues.length).toBeGreaterThan(0);

      // There should be issues in both the user and orders parts
      const hasUserPathIssue = result.issues.some((issue) =>
        issue.includes("user.age"),
      );
      const hasAddressPathIssue = result.issues.some((issue) =>
        issue.includes("user.address.zipCode"),
      );
      const hasOrdersPathIssue = result.issues.some((issue) =>
        issue.includes("orders[].items"),
      );

      expect(
        hasUserPathIssue || hasAddressPathIssue || hasOrdersPathIssue,
      ).toBe(true);
    });

    it("should throw when configured to do so", () => {
      // Schema with issues
      const invalidSchema = z.object({
        name: z.string().min(3),
      });

      // Should throw with the throwOnIncompatible option
      expect(() => {
        validateSchemaForStructuredOutput(invalidSchema, {
          throwOnIncompatible: true,
        });
      }).toThrow();
    });
  });
});
