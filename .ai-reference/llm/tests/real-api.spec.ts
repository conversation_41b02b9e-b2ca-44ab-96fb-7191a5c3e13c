import { Test } from "@nestjs/testing";
import { AskLLMService } from "../services/ask-llm.service";
import { OpenAIService } from "../services/openai.service";
import { OpenAIAskService } from "../services/openai-ask.service";
import { LlmProviderFactory } from "../services/llm-provider.factory";
import { PromptTemplateManager } from "../services/prompt-template-manager.service";
import { LlmModule } from "../llm.module";
import { z } from "zod";
import * as dotenv from "dotenv";

// Load environment variables
dotenv.config();

/**
 * REAL API TESTS
 *
 * These tests use the actual OpenAI API and will incur costs.
 * They should be run sparingly and with consideration.
 */
describe("LLM Service - Real API Tests", () => {
  let askLLMService: AskLLMService;
  let openAIService: OpenAIService;

  // Timeout set to 30 seconds as API calls can take time
  jest.setTimeout(30000);

  beforeAll(async () => {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error(
        "OPENAI_API_KEY environment variable is not set. These tests require a valid API key.",
      );
    }

    const moduleRef = await Test.createTestingModule({
      imports: [
        LlmModule.register({
          openaiApiKey: process.env.OPENAI_API_KEY,
          defaultProvider: "openai",
        }),
      ],
    }).compile();

    askLLMService = moduleRef.get<AskLLMService>(AskLLMService);
    openAIService = moduleRef.get<OpenAIService>(OpenAIService);
  });

  describe("Basic Completion", () => {
    it("should generate a response with a simple prompt", async () => {
      // Use a deterministic prompt that should yield a consistent response pattern
      const response = await askLLMService.ask({
        prompt: "What is 2+2? Answer with just the number.",
        model: "gpt-3.5-turbo",
        temperature: 0.1, // Low temperature for deterministic output
      });

      // Verify the response structure
      expect(response).toBeDefined();
      expect(response.message).toBeDefined();
      expect(response.message.content).toBeDefined();

      // The content should contain "4" somewhere in the response
      expect(response.message.content).toContain("4");
    });

    it("should handle system prompts correctly", async () => {
      const response = await askLLMService.ask({
        system: "You are a mathematician who only responds with numbers.",
        prompt: "What is the result of 3×7?",
        model: "gpt-3.5-turbo",
        temperature: 0.1,
      });

      expect(response).toBeDefined();
      expect(response.message.content).toBeDefined();

      // The response should contain "21" somewhere
      expect(response.message.content).toContain("21");
    });
  });

  describe("Structured Output", () => {
    it("should return properly structured data with a Zod schema", async () => {
      // Define a schema for the response
      const weatherSchema = z.object({
        temperature: z.number(),
        conditions: z.string(),
        forecast: z.array(z.string()),
      });

      const response = await askLLMService.ask({
        prompt:
          "Provide a fake weather report for New York with temperature in celsius, current conditions, and a 3-day forecast.",
        model: "gpt-4o-mini",
        temperature: 0.1,
        zodSchema: weatherSchema,
        schemaName: "WeatherReport",
      });

      // Validate response
      expect(response).toBeDefined();
      expect(response.parsed).toBeDefined();

      const weather = response.parsed as z.infer<typeof weatherSchema>;

      // Check the structure of the response
      expect(typeof weather.temperature).toBe("number");
      expect(typeof weather.conditions).toBe("string");
      expect(Array.isArray(weather.forecast)).toBe(true);
      expect(weather.forecast).toHaveLength(3);
    });
  });

  describe("Tool Calling", () => {
    it("should properly call a tool when prompted", async () => {
      // Define a tool
      const weatherTool = {
        type: "function" as const,
        function: {
          name: "get_weather",
          description: "Get the current weather in a location",
          parameters: {
            type: "object",
            properties: {
              location: {
                type: "string",
                description: "The city and state, e.g. San Francisco, CA",
              },
              unit: {
                type: "string",
                enum: ["celsius", "fahrenheit"],
                description: "The unit of temperature to use",
              },
            },
            required: ["location"],
          },
        },
      };

      const response = await askLLMService.ask({
        prompt: "What's the weather like in Miami, Florida?",
        model: "gpt-4o-mini",
        temperature: 0.1,
        tools: [weatherTool],
      });

      // Validate response
      expect(response).toBeDefined();

      // Check if it's an assistant message with tool calls
      const message = response.message as any;

      if (message.tool_calls) {
        // If the model decided to use tool calls
        expect(message.tool_calls).toBeDefined();
        expect(message.tool_calls.length).toBeGreaterThan(0);

        const toolCall = message.tool_calls[0];
        expect(toolCall.function).toBeDefined();
        expect(toolCall.function.name).toBe("get_weather");

        // Parse the function arguments
        const args = JSON.parse(toolCall.function.arguments);
        expect(args.location.toLowerCase()).toContain("miami");
      } else if (message.content) {
        // Sometimes the model might just respond directly
        console.log(
          "Model chose to respond directly instead of calling a tool.",
        );
        expect(message.content).toBeDefined();
        expect(typeof message.content).toBe("string");
        // Content should mention Miami or weather
        expect(message.content.toLowerCase()).toMatch(/miami|weather|florida/);
      }
    });
  });

  describe("Error Handling", () => {
    it("should properly handle invalid model names", async () => {
      await expect(
        askLLMService.ask({
          system: "You are a helpful AI assistant.",
          prompt: "Tell me a short joke.",
          model: "non-existent-model",
        }),
      ).rejects.toThrow();
    });
  });
});
