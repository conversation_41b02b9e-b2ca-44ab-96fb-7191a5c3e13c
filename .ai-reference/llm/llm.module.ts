import { DynamicModule, Global, Module } from "@nestjs/common";
import { AskLLMService } from "./services/ask-llm.service";
import { OpenAIService } from "./services/openai.service";
import { OpenAIAskService } from "./services/openai-ask.service";
import { PromptTemplateManager } from "./services/prompt-template-manager.service";
import { LlmProviderFactory } from "./services/llm-provider.factory";

export interface LlmModuleOptions {
  openaiApiKey: string;
  defaultProvider?: string;
}

/**
 * A simplified LLM module for development and testing
 * This module is designed to be compatible with the structure used in the main Claro codebase
 * but without all the additional dependencies
 */
@Global()
@Module({
  providers: [
    {
      provide: "LLM_MODULE_OPTIONS",
      useValue: {
        openaiApiKey: process.env.OPENAI_API_KEY || "",
        defaultProvider: "openai",
      },
    },
    OpenAIService,
    OpenAIAskService,
    PromptTemplateManager,
    LlmProviderFactory,
    AskLLMService,
  ],
  exports: [
    OpenAIService,
    OpenAIAskService,
    PromptTemplateManager,
    LlmProviderFactory,
    AskLLMService,
  ],
})
export class LlmModule {
  /**
   * Register the module with configuration options
   */
  static register(options: LlmModuleOptions): DynamicModule {
    // Set default provider if not specified
    const moduleOptions = {
      ...options,
      defaultProvider: options.defaultProvider || "openai",
    };

    return {
      module: LlmModule,
      providers: [
        {
          provide: "LLM_MODULE_OPTIONS",
          useValue: moduleOptions,
        },
        OpenAIService,
        OpenAIAskService,
        PromptTemplateManager,
        LlmProviderFactory,
        AskLLMService,
      ],
      exports: [
        OpenAIService,
        OpenAIAskService,
        PromptTemplateManager,
        LlmProviderFactory,
        AskLLMService,
      ],
    };
  }
}
