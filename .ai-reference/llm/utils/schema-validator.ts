import { z } from "zod";

/**
 * Error message for an incompatible Zod schema
 */
export class IncompatibleSchemaError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "IncompatibleSchemaError";
  }
}

/**
 * Options for schema validation
 */
export interface SchemaValidationOptions {
  /** Whether to throw an error for incompatible schemas */
  throwOnIncompatible?: boolean;
  /** Whether to log warnings for potential issues */
  warnOnPotentialIssues?: boolean;
}

/**
 * Result of schema validation
 */
export interface SchemaValidationResult {
  /** Whether the schema is compatible with OpenAI's structured output */
  isCompatible: boolean;
  /** Issues found in the schema */
  issues: string[];
}

/**
 * Validates a Zod schema for compatibility with OpenAI's structured output
 * @param schema The schema to validate
 * @param options Validation options
 * @returns Validation result
 */
export function validateSchemaForStructuredOutput<T>(
  schema: z.ZodType<T>,
  options: SchemaValidationOptions = {},
): SchemaValidationResult {
  const { throwOnIncompatible = false, warnOnPotentialIssues = true } = options;
  const issues: string[] = [];

  // Check the schema recursively
  checkZodSchema(schema, "", issues);

  const isCompatible = issues.length === 0;

  // Log warnings if needed
  if (!isCompatible && warnOnPotentialIssues) {
    console.warn(
      "Schema may not be compatible with OpenAI's structured output capabilities:",
      issues,
    );
  }

  // Throw if needed
  if (!isCompatible && throwOnIncompatible) {
    throw new IncompatibleSchemaError(
      `Schema is not compatible with OpenAI's structured output capabilities: ${issues.join(
        ", ",
      )}`,
    );
  }

  return { isCompatible, issues };
}

/**
 * Inspects a schema's _def property to check for validation operations
 * @param schema The Zod schema to inspect
 * @returns Whether the schema has validation methods
 */
function hasValidationMethods(schema: any): boolean {
  if (!schema || !schema._def) return false;

  // Check for refinements
  if (schema._def.refinement) return true;

  // Check for string validations - not all checks are problematic
  if (schema._def.checks && schema._def.checks.length > 0) {
    // Look for specific validation types that are incompatible
    const incompatibleChecks = schema._def.checks.filter((check: any) => {
      const type = check.kind || check.type;
      return [
        "min",
        "max",
        "length",
        "email",
        "url",
        "uuid",
        "regex",
        "includes",
      ].includes(type);
    });
    return incompatibleChecks.length > 0;
  }

  // Check for transformations
  if (schema._def.effect && schema._def.effect.type === "transform")
    return true;

  // Check for array validations - only care about specific ones
  if (
    (schema._def.minLength !== null && schema._def.minLength !== undefined) ||
    (schema._def.maxLength !== null && schema._def.maxLength !== undefined)
  )
    return true;

  // Check for number validations - only care about specific ones
  if (
    (schema._def.minimum !== null && schema._def.minimum !== undefined) ||
    (schema._def.maximum !== null && schema._def.maximum !== undefined) ||
    schema._def.isInt ||
    schema._def.isPositive ||
    schema._def.isNonNegative
  )
    return true;

  return false;
}

/**
 * Recursively checks a Zod schema for compatibility issues
 * @param schema The schema to check
 * @param path The current path in the schema
 * @param issues Array to collect issues
 */
function checkZodSchema(
  schema: z.ZodType<any>,
  path: string,
  issues: string[],
): void {
  // Check schema type and special operations
  if (!schema || typeof schema !== "object") return;

  // Check for ZodString with validations
  if (schema instanceof z.ZodString && hasValidationMethods(schema)) {
    issues.push(
      `${path || "Schema"} includes string validation methods which are not supported`,
    );
  }

  // Check for ZodNumber with validations
  if (schema instanceof z.ZodNumber && hasValidationMethods(schema)) {
    issues.push(
      `${path || "Schema"} includes number validation methods which are not supported`,
    );
  }

  // Check for ZodArray with validations
  if (schema instanceof z.ZodArray && hasValidationMethods(schema)) {
    issues.push(
      `${path || "Schema"} includes array validation methods which are not supported`,
    );
  }

  // Check for refinements and transformations directly
  if (schema instanceof z.ZodEffects) {
    if (schema._def.effect.type === "refinement") {
      issues.push(
        `${path || "Schema"} includes refinements which are not supported`,
      );
    } else if (schema._def.effect.type === "transform") {
      issues.push(
        `${path || "Schema"} includes transformations which are not supported`,
      );
    }
  }

  // Handle object types recursively
  if (schema instanceof z.ZodObject) {
    const shape = schema._def.shape();
    Object.entries(shape).forEach(([key, value]) => {
      const newPath = path ? `${path}.${key}` : key;
      checkZodSchema(value as z.ZodType<any>, newPath, issues);
    });
  }

  // Handle array types recursively
  if (schema instanceof z.ZodArray) {
    const elementType = schema._def.type;
    checkZodSchema(elementType, `${path}[]`, issues);
  }

  // Handle union types recursively
  if (schema instanceof z.ZodUnion) {
    const options = schema._def.options;
    if (options.length > 2) {
      issues.push(
        `${path || "Schema"} includes a complex union with more than 2 types, which may cause issues`,
      );
    }
    options.forEach((option: z.ZodType<any>, index: number) => {
      checkZodSchema(option, `${path || "Union"}[${index}]`, issues);
    });
  }
}

/**
 * Creates a simplified version of a Zod schema that is compatible with OpenAI's structured output
 * This is a best-effort attempt and may not work for all schemas
 * @param schema The schema to simplify
 * @returns A simplified schema
 */
export function createSimplifiedSchema<T>(
  schema: z.ZodType<T>,
): z.ZodType<any> {
  // Implementation of schema simplification would go here
  // This is more complex and would require a full translation mechanism
  // For now, we just warn users to manually simplify their schemas
  console.warn(
    "Schema simplification is not fully implemented. Please manually simplify your schema following the guidelines in ZOD_SCHEMA_GUIDELINES.md",
  );

  return schema;
}
