// filter_by_index.js
const { readFile, writeFile } = require("fs").promises;
const { resolve } = require("path");
const { exit } = require("process");

// --- Configuration ---
const INPUT_FILE_ARG_INDEX = 2;
const OUTPUT_FILE_ARG_INDEX = 3;

// *** NEW: Define the indices (0-based) of the emails you want to keep ***
const INDICES_TO_KEEP = [
  0, 1, 2, 3, 5, 6, 7, 10, 11, 15, 16, 18, 21, 25, 26, 27, 28, 30, 31, 32, 35, 37, 40, 43, 44, 47, 50, 52, 57,
  58, 59, 60, 64, 65, 66, 68, 69, 71, 74, 77, 79, 80, 91, 92, 97, 98, 99
]; // Indices based on filtering criteria
// ---------------------

async function filterEntriesByIndex() {
  // 1. Get file paths from command line arguments
  const inputFile = process.argv[INPUT_FILE_ARG_INDEX];
  const outputFile = process.argv[OUTPUT_FILE_ARG_INDEX];

  if (!inputFile || !outputFile) {
    console.error(`Usage: node ${resolve(__filename)} <input.json> <output.json>`);
    exit(1); // Exit with an error code
  }

  const inputPath = resolve(inputFile); // Get absolute path
  const outputPath = resolve(outputFile); // Get absolute path

  console.log(`Reading from: ${inputPath}`);
  console.log(`Filtering based on ${INDICES_TO_KEEP.length} specified indices.`);
  console.log(`Writing to:   ${outputPath}`);

  try {
    // 2. Read the input JSON file
    const rawData = await readFile(inputPath, "utf8");

    // 3. Parse the JSON content
    const fullData = JSON.parse(rawData); // Parsed object, e.g., { data: [...] }

    // 4. *** MODIFIED PART START ***
    // Check if the expected structure { data: [...] } exists
    if (typeof fullData === "object" && fullData !== null && Array.isArray(fullData.data)) {
      // Get the array from the 'data' key
      const itemsArray = fullData.data;

      // --- FILTERING LOGIC ---
      // Filter the itemsArray based on whether their index is in INDICES_TO_KEEP
      const filteredItems = itemsArray.filter((item, index) => {
        return INDICES_TO_KEEP.includes(index);
      });
      // -------------------------

      // Create the output structure, preserving the { data: [...] } format
      const outputData = { data: filteredItems };

      console.log(`Filtered down to ${filteredItems.length} elements based on the specified indices.`);

      // 5. Stringify the *new* output structure
      const outputJson = JSON.stringify(outputData, null, 2); // Pretty-print

      // 6. Write the new JSON string to the output file
      await writeFile(outputPath, outputJson, "utf8");

      console.log(`Successfully saved filtered data to ${outputPath}`);
    } else {
      // Handle cases where the input JSON doesn't match the expected structure
      console.error('Error: Input JSON does not have the expected { "data": [...] } structure.');
      exit(1);
    }
    // *** MODIFIED PART END ***
  } catch (error) {
    console.error("An error occurred:");
    if (error instanceof SyntaxError) {
      console.error(`Error parsing JSON in ${inputPath}: ${error.message}`);
    } else if (error.code === "ENOENT") {
      console.error(`Error: Input file not found at ${inputPath}`);
    } else {
      console.error(error); // Print other errors
    }
    exit(1); // Exit with an error code
  }
}

// --- Run the async function ---
filterEntriesByIndex();
// ----------------------------
