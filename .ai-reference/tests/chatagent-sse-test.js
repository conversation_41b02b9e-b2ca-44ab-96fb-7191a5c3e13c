// ChatAgentModule SSE Integration Test
// Run with: node chatagent-sse-test.js

const EventSource = require("eventsource");
const fetch = require("node-fetch");

// Configuration
const API_URL = "http://localhost:3000"; // Adjust to your API URL
const ACCESS_TOKEN = "your_access_token_here"; // Replace with a valid access token
const ORGANIZATION_ID = "your_organization_id_here"; // Replace with a valid organization ID

// Test both implementations
async function testChatbotSSE() {
  console.log("Testing ChatbotModule SSE...");

  const eventSource = new EventSource(`${API_URL}/chat`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${ACCESS_TOKEN}`,
      "Organization-Id": ORGANIZATION_ID
    },
    body: JSON.stringify({
      message: "Hello, this is a test message for the ChatbotModule."
    })
  });

  eventSource.onmessage = (event) => {
    console.log("ChatbotModule SSE event:", JSON.parse(event.data));

    // Close connection after receiving a few messages
    setTimeout(() => {
      eventSource.close();
      console.log("ChatbotModule SSE connection closed");
    }, 5000);
  };

  eventSource.onerror = (error) => {
    console.error("ChatbotModule SSE error:", error);
    eventSource.close();
  };
}

async function testChatAgentSSE() {
  console.log("Testing ChatAgentModule SSE...");

  const eventSource = new EventSource(`${API_URL}/chat-communication/stream`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${ACCESS_TOKEN}`,
      "Organization-Id": ORGANIZATION_ID
    },
    body: JSON.stringify({
      message: "Hello, this is a test message for the ChatAgentModule."
    })
  });

  eventSource.onmessage = (event) => {
    console.log("ChatAgentModule SSE event:", JSON.parse(event.data));

    // Close connection after receiving a few messages
    setTimeout(() => {
      eventSource.close();
      console.log("ChatAgentModule SSE connection closed");
    }, 5000);
  };

  eventSource.onerror = (error) => {
    console.error("ChatAgentModule SSE error:", error);
    eventSource.close();
  };
}

// Create a simple chat session first (optional)
async function createChatSession() {
  try {
    const response = await fetch(`${API_URL}/chat-communication/session`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${ACCESS_TOKEN}`,
        "Organization-Id": ORGANIZATION_ID
      },
      body: JSON.stringify({
        initialMessage: "Start a new conversation"
      })
    });

    const data = await response.json();
    console.log("Created chat session:", data);
    return data.conversationId;
  } catch (error) {
    console.error("Error creating chat session:", error);
    return null;
  }
}

// Run tests sequentially
async function runTests() {
  try {
    // Test original ChatbotModule
    await testChatbotSSE();

    // Wait a bit between tests
    await new Promise((resolve) => setTimeout(resolve, 6000));

    // Create a session and test ChatAgentModule
    const conversationId = await createChatSession();
    await testChatAgentSSE();

    console.log("All tests completed");
  } catch (error) {
    console.error("Test error:", error);
  }
}

// Start the tests
runTests();
