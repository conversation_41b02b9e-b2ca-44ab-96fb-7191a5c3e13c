import { Test, TestingModule } from "@nestjs/testing";
import * as dotenv from "dotenv";
import * as fs from "fs";
import * as path from "path";
import { LlmProviderFactory } from "../../llm/ask-llm/providers/llm-provider.factory";
import { OpenAILlmProvider } from "../../llm/ask-llm/providers/openai-llm-provider";
import { AskLLMService } from "../../llm/ask-llm/services/ask-llm.service";
import { OpenAIEnhancedService } from "../../llm/openai-enhanced.service";
import { AgentFlowEngineService } from "../agent-flow-engine.service";
import { WorkflowGraph } from "../models/workflow-graph.model";
import { DEFAULT_LLM_PARAMS, LlmNodeHandler } from "../nodes/llm-node.handler";
import { NodeHandlerRegistry } from "../nodes/node-registry";
import { EdgeFactory } from "../services/edge-factory.service";
import { NodeFactory } from "../services/node-factory.service";
import { TemplateService } from "../services/template.service";
import { SerializedWorkflowGraph, WorkflowBuilder } from "../services/workflow-builder.service";
import { WorkflowExecutor } from "../services/workflow-executor.service";

// Load environment variables from .env.local in portal-api directory
const envPath = path.resolve(process.cwd(), ".env.local");
if (fs.existsSync(envPath)) {
  console.log(`Loading environment from: ${envPath}`);
  dotenv.config({ path: envPath });
} else {
  console.warn(`No .env.local file found at: ${envPath}`);
}

// Debug module resolution
console.log("NODE_PATH:", process.env.NODE_PATH);
console.log("Module paths:", module.paths);

// Check .env file existence (for informational purposes)
if (fs.existsSync(envPath)) {
  console.log(`Found .env.local file at: ${envPath}`);
} else {
  console.warn(`No .env.local file found at: ${envPath}`);
}

// Log if API key is available
console.log("OPENAI_API_KEY available:", !!process.env.OPENAI_API_KEY);

describe("WorkflowExecutor Integration Tests", () => {
  let graph: WorkflowGraph;
  let agentFlowEngineService: AgentFlowEngineService;
  let workflowExecutor: WorkflowExecutor;
  let nodeHandlerRegistry: NodeHandlerRegistry;
  let llmNodeHandler: LlmNodeHandler;
  let templateService: TemplateService;
  let nodeFactory: NodeFactory;
  let edgeFactory: EdgeFactory;
  let workflowBuilder: WorkflowBuilder;
  let moduleRef: TestingModule;

  // Workflow definition as a JSON string
  const workflowJson = `
  {
    "startNodeId": "intent-node",
    "nodes": [
      {
        "id": "intent-node",
        "type": "llm",
        "description": "Test the intent of the instruction",
        "contents": {
          "prompt": "Please determine the intent of this instruction: \\"{{ instruction }}\\". Respond with only one of these words: QUERY, CREATE, or UPDATE.",
          "temperature": 0.1,
          "model": "gpt-4o-mini"
        },
        "inputMap": {
          "instruction": "{{ input.instruction }}"
        },
        "outputSchemaMap": {
          "intent": "enum:QUERY|CREATE|UPDATE"
        }
      },
      {
        "id": "query-node",
        "type": "llm",
        "description": "Handles query operations",
        "contents": {
          "prompt": "You are a query handler for a customs management system. Process the following query: {{ instruction }}",
          "temperature": 0.2,
          "model": "gpt-4o-mini",
          "system": "You are a helpful assistant for a customs management system. Your role is to process query operations."
        },
        "inputMap": {
          "instruction": "{{ input.instruction }}"
        },
        "outputSchemaMap": {
          "status": "string",
          "response": "string",
          "timestamp": "string"
        }
      },
      {
        "id": "create-node",
        "type": "llm",
        "description": "Handles create operations",
        "contents": {
          "prompt": "You are a create operation handler for a customs management system. Process the following create request: {{ instruction }}",
          "temperature": 0.2,
          "model": "gpt-4o-mini",
          "system": "You are a helpful assistant for a customs management system. Your role is to process create operations."
        },
        "inputMap": {
          "instruction": "{{ input.instruction }}"
        },
        "outputSchemaMap": {
          "status": "string",
          "entityId": "string",
          "message": "string",
          "timestamp": "string"
        }
      },
      {
        "id": "update-node",
        "type": "llm",
        "description": "Handles update operations",
        "contents": {
          "prompt": "You are an update operation handler for a customs management system. Process the following update request: {{ instruction }}",
          "temperature": 0.2,
          "model": "gpt-4o-mini",
          "system": "You are a helpful assistant for a customs management system. Your role is to process update operations."
        },
        "inputMap": {
          "instruction": "{{ input.instruction }}"
        },
        "outputSchemaMap": {
          "status": "string",
          "entityId": "string",
          "changes": "string[]",
          "timestamp": "string"
        }
      }
    ],
    "edges": [
      {
        "id": "query-edge",
        "from": "intent-node",
        "to": "query-node",
        "condition": "{{ intent-node.intent == 'QUERY' }}"
      },
      {
        "id": "create-edge",
        "from": "intent-node",
        "to": "create-node",
        "condition": "{{ intent-node.intent == 'CREATE' }}"
      },
      {
        "id": "update-edge",
        "from": "intent-node",
        "to": "update-node",
        "condition": "{{ intent-node.intent == 'UPDATE' }}"
      }
    ],
    "inputSchemaMap": {
      "instruction": "string",
      "timestamp?": "string"
    },
    "outputSchemaMap": {
      "intent-node?": {
        "intent": "enum:QUERY|CREATE|UPDATE"
      },
      "query-node?": {
        "status": "string",
        "response": "string",
        "timestamp": "string"
      },
      "create-node?": {
        "status": "string",
        "entityId": "string",
        "message": "string",
        "timestamp": "string"
      },
      "update-node?": {
        "status": "string",
        "entityId": "string",
        "changes": "string[]",
        "timestamp": "string"
      }
    }
  }
  `;

  // Helper to check if we should run tests
  const hasApiKey = !!process.env.OPENAI_API_KEY;

  // Timeout for all tests - API calls can take time
  jest.setTimeout(30000);

  beforeAll(async () => {
    if (!hasApiKey) {
      console.warn("Skipping workflow tests - no OPENAI_API_KEY found in .env.local");
      return;
    }

    console.log("Found API key - running live workflow tests with OpenAI");

    // Create mock for LlmLogService to avoid database dependency
    const mockLlmLogService = {
      createLlmLog: jest.fn().mockResolvedValue({ id: 1 }),
      updateLlmLog: jest.fn().mockResolvedValue(true)
    };

    // Set up the NestJS test module with all required dependencies
    moduleRef = await Test.createTestingModule({
      providers: [
        // Core services
        TemplateService,
        WorkflowExecutor,
        NodeHandlerRegistry,
        AgentFlowEngineService,
        NodeFactory,
        EdgeFactory,

        // LLM-related services
        {
          provide: "LLM_CONFIG", // Configuration token
          useValue: {
            openaiApiKey: process.env.OPENAI_API_KEY,
            deepseekApiKey: "",
            defaultProvider: "openai"
          }
        },
        {
          provide: "LLM_LOG_SERVICE",
          useValue: mockLlmLogService
        },
        {
          provide: OpenAIEnhancedService,
          useFactory: (config, logService) => new OpenAIEnhancedService(config, logService),
          inject: ["LLM_CONFIG", "LLM_LOG_SERVICE"]
        },
        {
          provide: "OPENAI_PROVIDER",
          useFactory: (openaiService) => new OpenAILlmProvider(openaiService),
          inject: [OpenAIEnhancedService]
        },
        {
          provide: LlmProviderFactory,
          useFactory: (config, openaiProvider) =>
            new LlmProviderFactory(config, [{ name: "openai", provider: openaiProvider }]),
          inject: ["LLM_CONFIG", "OPENAI_PROVIDER"]
        },
        {
          provide: AskLLMService,
          useFactory: (providerFactory, templateService, config) => {
            console.log("Creating AskLLMService with TemplateService");
            return new AskLLMService(
              providerFactory,
              {
                renderString: (template, variables) => {
                  console.log(`Rendering template: ${template.substring(0, 30)}...`);
                  return templateService.renderTemplate(template, variables);
                },
                renderTemplate: (templateName, variables) => {
                  console.log(`Rendering named template: ${templateName}`);
                  return templateService.renderTemplate(templateName, variables);
                }
              } as any,
              config
            );
          },
          inject: [LlmProviderFactory, TemplateService, "LLM_CONFIG"]
        },
        {
          provide: DEFAULT_LLM_PARAMS,
          useValue: {
            model: "gpt-4o-mini",
            temperature: 0.7,
            debug: true,
            store: true,
            system: "You are a helpful assistant for a customs management system."
          }
        },
        {
          provide: LlmNodeHandler,
          useFactory: (askLlmService, defaultParams) => {
            console.log("Creating LlmNodeHandler with default params:", defaultParams);
            return new LlmNodeHandler(askLlmService, defaultParams);
          },
          inject: [AskLLMService, DEFAULT_LLM_PARAMS]
        },
        {
          provide: WorkflowBuilder,
          useFactory: (templateService, nodeFactory, edgeFactory) => {
            return new WorkflowBuilder(templateService, nodeFactory, edgeFactory);
          },
          inject: [TemplateService, NodeFactory, EdgeFactory]
        }
      ]
    }).compile();

    // Get the service instances from the module
    templateService = moduleRef.get<TemplateService>(TemplateService);
    workflowExecutor = moduleRef.get<WorkflowExecutor>(WorkflowExecutor);
    nodeHandlerRegistry = moduleRef.get<NodeHandlerRegistry>(NodeHandlerRegistry);
    llmNodeHandler = moduleRef.get<LlmNodeHandler>(LlmNodeHandler);
    agentFlowEngineService = moduleRef.get<AgentFlowEngineService>(AgentFlowEngineService);
    nodeFactory = moduleRef.get<NodeFactory>(NodeFactory);
    edgeFactory = moduleRef.get<EdgeFactory>(EdgeFactory);
    workflowBuilder = moduleRef.get<WorkflowBuilder>(WorkflowBuilder);

    // Register the LLM handler with the node registry
    console.log("Registering 'llm' node handler");
    nodeHandlerRegistry.registerHandler("llm", (node, context) => {
      console.log(`Handling node with type: ${node.type}, id: ${node.id}`);
      return llmNodeHandler.handle(node, context);
    });

    console.log("Registered handlers:", nodeHandlerRegistry.getRegisteredNodeTypes());

    // Parse the JSON string to get the workflow configuration
    const workflowConfig: SerializedWorkflowGraph = JSON.parse(workflowJson);

    // Create a workflow graph from the configuration using the builder
    graph = workflowBuilder.build(workflowConfig);

    console.log("Workflow graph created and validated");
  });

  afterAll(async () => {
    if (moduleRef) {
      await moduleRef.close();
    }
  });

  describe("Graph Validation", () => {
    it("should load the workflow configuration correctly", () => {
      if (!hasApiKey) {
        console.log("Skipping test - no API key");
        return;
      }

      // Verify the graph structure
      expect(graph).toBeDefined();
      expect(graph.nodes.size).toBe(4);
      expect(graph.edges.size).toBe(3);
      expect(graph.getStartNodeId()).toBe("intent-node");
    });
  });

  describe("Workflow Execution", () => {
    it("should execute the workflow with a query instruction", async () => {
      if (!hasApiKey) {
        console.log("Skipping test - no API key");
        return;
      }

      // Set up a tracker to debug node execution
      const executionLog = [];
      workflowExecutor.setExecutionTracker((nodeId, status, edgeId) => {
        executionLog.push({ nodeId, edgeId, status });
        console.log(`Execution: ${nodeId} - ${status} ${edgeId ? `(edge: ${edgeId})` : ""}`);
      });

      // Create test input data for a query operation
      const inputData = {
        instruction: "Show me all pending shipments",
        timestamp: new Date().toISOString()
      };

      // Execute the workflow
      const result = await workflowExecutor.executeWorkflow(graph, inputData);

      // Debug the execution
      console.log("Execution log:", executionLog);
      console.log("Result:", JSON.stringify(result, null, 2));
      console.log("Intent node output:", JSON.stringify(result["intent-node"], null, 2));

      // Verify the output structure
      expect(result).toBeDefined();
      expect(result["intent-node"]).toBeDefined();
      expect(result["intent-node"].intent).toBe("QUERY");
      expect(result["query-node"]).toBeDefined();
      expect(result["query-node"].status).toBeDefined();
      expect(result["query-node"].response).toBeDefined();
      expect(result["query-node"].timestamp).toBeDefined();

      // Verify the correct edge was followed
      const queryEdgeExecuted = executionLog.some(
        (entry) => entry.edgeId === "query-edge" && entry.status === "following"
      );
      expect(queryEdgeExecuted).toBe(true);

      console.log("Query response:", result["query-node"].response);
    });

    it("should execute the workflow with a create instruction", async () => {
      if (!hasApiKey) {
        console.log("Skipping test - no API key");
        return;
      }

      // Set up a tracker to debug node execution
      const executionLog = [];
      workflowExecutor.setExecutionTracker((nodeId, status, edgeId) => {
        executionLog.push({ nodeId, edgeId, status });
        console.log(`Execution: ${nodeId} - ${status} ${edgeId ? `(edge: ${edgeId})` : ""}`);
      });

      // Create test input data for a create operation
      const inputData = {
        instruction: "Create a new shipment from China with cargo type electronics",
        timestamp: new Date().toISOString()
      };

      // Execute the workflow
      const result = await workflowExecutor.executeWorkflow(graph, inputData);

      // Debug the execution
      console.log("Execution log:", executionLog);
      console.log("Result:", JSON.stringify(result, null, 2));
      console.log("Intent node output:", JSON.stringify(result["intent-node"], null, 2));

      // Verify the output structure
      expect(result).toBeDefined();
      expect(result["intent-node"]).toBeDefined();
      expect(result["intent-node"].intent).toBe("CREATE");

      // Verify create node was executed
      expect(result["create-node"]).toBeDefined();
      expect(result["create-node"].status).toBeDefined();
      expect(result["create-node"].entityId).toBeDefined();
      expect(result["create-node"].message).toBeDefined();

      // Verify the correct edge was followed
      const createEdgeExecuted = executionLog.some(
        (entry) => entry.edgeId === "create-edge" && entry.status === "following"
      );
      expect(createEdgeExecuted).toBe(true);

      console.log("Create response:", result["create-node"].message);
    });

    it("should execute the workflow with an update instruction", async () => {
      if (!hasApiKey) {
        console.log("Skipping test - no API key");
        return;
      }

      // Set up a tracker to debug node execution
      const executionLog = [];
      workflowExecutor.setExecutionTracker((nodeId, status, edgeId) => {
        executionLog.push({ nodeId, edgeId, status });
        console.log(`Execution: ${nodeId} - ${status} ${edgeId ? `(edge: ${edgeId})` : ""}`);
      });

      // Create test input data for an update operation
      const inputData = {
        instruction: "Update the shipment with ID 12345 to change the destination to Los Angeles",
        timestamp: new Date().toISOString()
      };

      // Execute the workflow
      const result = await workflowExecutor.executeWorkflow(graph, inputData);

      // Debug the execution
      console.log("Execution log:", executionLog);
      console.log("Result:", JSON.stringify(result, null, 2));
      console.log("Intent node output:", JSON.stringify(result["intent-node"], null, 2));

      // Verify the output structure
      expect(result).toBeDefined();
      expect(result["intent-node"]).toBeDefined();
      expect(result["intent-node"].intent).toBe("UPDATE");

      // Verify update node was executed
      expect(result["update-node"]).toBeDefined();
      expect(result["update-node"].status).toBeDefined();
      expect(result["update-node"].entityId).toBeDefined();
      expect(result["update-node"].changes).toBeDefined();

      // Verify the correct edge was followed
      const updateEdgeExecuted = executionLog.some(
        (entry) => entry.edgeId === "update-edge" && entry.status === "following"
      );
      expect(updateEdgeExecuted).toBe(true);

      console.log("Update changes:", result["update-node"].changes);
    });
  });
});
