{# Template for OTHER_DIRECT_FIELD inquiries #}
{# Input: shipment object, requestedField<PERSON><PERSON> (e.g., 'mblNumber'), requestedField<PERSON>abel (e.g., 'MBL Number') #}

{# WARNING: This is a basic template. You might need more sophisticated logic #}
{# to extract the specific field requested from the user query BEFORE calling this template. #}
{# For now, it assumes 'requestedFieldKey' and 'requestedFieldLabel' are provided. #}

{%- set value = shipment[requestedFieldKey] %}

{%- if value is defined and value is not none and value != '' %}
  The {{ requestedFieldLabel | default('requested information') }} is {{ value }}.
{%- else %}
  The {{ requestedFieldLabel | default('requested information') }} is not available for this shipment.
{%- endif %} 