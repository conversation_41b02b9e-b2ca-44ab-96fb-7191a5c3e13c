{# Template for GENERAL_STATUS inquiries #}
{# Input: shipment object #}

{# Core status #}
{%- set customs_status_desc = {
  "PENDING_CONFIRMATION": "Pending Confirmation",
  "PENDING_ARRIVAL": "Pending Arrival",
  "LIVE": "Live",
  "ENTRY_SUBMITTED": "Entry Submitted",
  "ENTRY_ACCEPTED": "Entry Accepted",
  "EXAM": "Under Exam",
  "RELEASED": "Released",
  "PENDING_COMMERCIAL_INVOICE": "Pending Commercial Invoice"
} %}
{%- set final_answer = "The current customs status is " + (customs_status_desc[shipment.customsStatus] | default(shipment.customsStatus, true)) + "." %}

{# Transaction Number (Conditional) #}
{%- if shipment.transactionNumber and shipment.customsStatus in ['LIVE', 'ENTRY_SUBMITTED', 'ENTRY_ACCEPTED', 'EXAM', 'RELEASED'] %}
  {%- set final_answer = final_answer + " The transaction number is " + shipment.transactionNumber + "." %}
{%- endif %}

{# ETAs (Conditional) #}
{%- set is_air_ocean = shipment.modeOfTransport in ['AIR', 'OCEAN'] %}
{%- set eta_parts_to_add = [] %} {# Temporary array #}
{%- if is_air_ocean %}
    {%- if shipment.etaPort %}
        {# Use standard set with array concatenation #}
        {%- set eta_parts_to_add = eta_parts_to_add + ["ETA to port is " + shipment.etaPort] %}
    {%- endif %}
    {%- if shipment.etaDestination %}
        {# Use standard set with array concatenation #}
        {%- set eta_parts_to_add = eta_parts_to_add + ["final ETA is " + shipment.etaDestination] %}
    {%- endif %}
{%- endif %}

{%- if eta_parts_to_add | length > 0 %}
    {%- set final_answer = final_answer + " " + eta_parts_to_add | join(' and ') + "." %}
{%- endif %}

{# Shipment Status (Conditional) #}
{%- set tracking_status_desc = {
  "ONLINE": "Online",
  "OFFLINE": "Offline",
  "UNKNOWN": "Unknown"
} %}
{%- set shipment_status_desc = {
  "PENDING": "Pending",
  "PRE_TRANSIT": "Pre-Transit",
  "IN_TRANSIT": "In Transit",
  "ARRIVED_PORT": "Arrived at Port",
  "ARRIVED_DESTINATION": "Arrived at Destination",
  "OUT_FOR_DELIVERY": "Out for Delivery",
  "DELIVERED": "Delivered",
  "EXCEPTION": "Exception",
  "UNKNOWN": "Unknown"
} %}
{%- if shipment.trackingStatus == 'ONLINE' and shipment.status %}
  {%- set final_answer = final_answer + " The shipment tracking shows it is " + (shipment_status_desc[shipment.status] | default(shipment.status, true)) + " (Tracking: Online)." %}
{%- elif shipment.trackingStatus %}
  {%- set final_answer = final_answer + " The tracking status is " + (tracking_status_desc[shipment.trackingStatus] | default(shipment.trackingStatus, true)) + "." %}
{%- endif %}

{{ final_answer | trim }} 