{# Template for SUBMISSION_STATUS inquiries #}
{# Input: shipment object #}

{%- set submitted_statuses = ['ENTRY_SUBMITTED', 'ENTRY_ACCEPTED', 'EXAM', 'RELEASED'] %}
{%- set customs_status_desc = {
  "PENDING_CONFIRMATION": "Pending Confirmation",
  "PENDING_ARRIVAL": "Pending Arrival",
  "LIVE": "Live",
  "ENTRY_SUBMITTED": "Entry Submitted",
  "ENTRY_ACCEPTED": "Entry Accepted",
  "EXAM": "Under Exam",
  "RELEASED": "Released",
  "PENDING_COMMERCIAL_INVOICE": "Pending Commercial Invoice"
} %}

{%- if shipment.customsStatus in submitted_statuses %}
  Yes, the customs entry has been submitted. The current status is {{ customs_status_desc[shipment.customsStatus] | default(shipment.customsStatus, true) }}.
{%- elif shipment.customsStatus %}
  No, the customs entry has not been submitted yet. The current status is {{ customs_status_desc[shipment.customsStatus] | default(shipment.customsStatus, true) }}.
{%- else %}
  The customs submission status is not available.
{%- endif %} 