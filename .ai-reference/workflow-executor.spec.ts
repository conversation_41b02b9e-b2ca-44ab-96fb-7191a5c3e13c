import { NodeType } from "../interfaces/types";
import { AgentContext } from "../models/context.model";
import { Edge } from "../models/edge.model";
import { Node } from "../models/node.model";
import { WorkflowGraph } from "../models/workflow-graph.model";
import { WorkflowExecutor } from "../services/workflow-executor.service";

/**
 * Helper function to verify properties in a nested structure
 */
function expectNestedProperty(obj: any, path: string, expectedValue: any): void {
  const [nodeId, propPath] = path.split(".");

  // First check if the top-level object exists
  expect(obj).toBeDefined();

  // Check if the node object exists at the top level
  expect(obj[nodeId]).toBeDefined();

  // For our specific data structure, check if there's a nested object with the same key
  if (obj[nodeId][nodeId]) {
    // In this case we have a structure like result.start.start.output
    expect(obj[nodeId][nodeId][propPath]).toEqual(expectedValue);
  } else {
    // In simpler cases like result.custom-error-handler.handled
    expect(obj[nodeId][propPath]).toEqual(expectedValue);
  }
}

describe("WorkflowExecutor Tests", () => {
  let workflowExecutor: WorkflowExecutor;
  let workflow: WorkflowGraph;

  // Track execution flow for assertions
  const executionFlow: Array<{ nodeId: string; edgeId?: string; status?: string }> = [];
  const executionTracker = (nodeId: string, edgeId?: string, status?: string) => {
    executionFlow.push({ nodeId, edgeId, status });
  };

  beforeEach(() => {
    // Reset execution flow tracking
    executionFlow.length = 0;

    // Create real executor with tracker
    workflowExecutor = new WorkflowExecutor(executionTracker);

    // Create a fresh workflow for each test
    workflow = new WorkflowGraph();
  });

  describe("Basic workflow execution", () => {
    beforeEach(() => {
      // Define nodes
      const startNode = new Node({
        id: "start",
        type: "standard" as NodeType,
        description: "Start node",
        processFn: async () => {
          return { output: "start-data" };
        }
      });

      const middleNode = new Node({
        id: "middle",
        type: "standard" as NodeType,
        description: "Middle node",
        processFn: async () => {
          return { output: "middle-data" };
        }
      });

      const endNode = new Node({
        id: "end",
        type: "standard" as NodeType,
        description: "End node",
        processFn: async () => {
          return { output: "end-data", complete: true };
        }
      });

      // Define edges
      const startToMiddleEdge = new Edge({
        id: "start-to-middle",
        from: "start",
        to: "middle",
        condition: "true"
      });

      const middleToEndEdge = new Edge({
        id: "middle-to-end",
        from: "middle",
        to: "end",
        condition: "true"
      });

      // Build workflow
      workflow.addNode(startNode);
      workflow.addNode(middleNode);
      workflow.addNode(endNode);
      workflow.addEdge(startToMiddleEdge);
      workflow.addEdge(middleToEndEdge);
      workflow.setStartNode("start");
    });

    it("should execute a simple linear workflow", async () => {
      // Execute workflow with default configuration
      const result = await workflowExecutor.executeWorkflow(workflow, {});

      // Verify the result
      expectNestedProperty(result, "start.output", "start-data");
      expectNestedProperty(result, "middle.output", "middle-data");
      expectNestedProperty(result, "end.output", "end-data");
      expectNestedProperty(result, "end.complete", true);

      // Verify execution flow
      expect(executionFlow).toContainEqual({ nodeId: "start", status: "executing" });
      expect(executionFlow).toContainEqual({ nodeId: "middle", status: "executing" });
      expect(executionFlow).toContainEqual({ nodeId: "end", status: "executing" });
    });
  });

  describe("Conditional workflow execution", () => {
    beforeEach(() => {
      // Define nodes
      const startNode = new Node({
        id: "start",
        type: "standard" as NodeType,
        description: "Start node",
        processFn: async (_context) => {
          return new AgentContext({
            start: {
              output: "start-data",
              decision: _context.get("initialDecision") || "path-a"
            }
          });
        }
      });

      const pathANode = new Node({
        id: "path-a",
        type: "standard" as NodeType,
        description: "Path A node",
        processFn: async () => {
          return { output: "path-a-data" };
        }
      });

      const pathBNode = new Node({
        id: "path-b",
        type: "standard" as NodeType,
        description: "Path B node",
        processFn: async () => {
          return { output: "path-b-data" };
        }
      });

      const endNode = new Node({
        id: "end",
        type: "standard" as NodeType,
        description: "End node",
        processFn: async (context) => {
          return new AgentContext({
            end: {
              path: context.has("path-a") ? "A" : "B",
              output: "end-data"
            }
          });
        }
      });

      // Define edges with conditions
      const startToPathAEdge = new Edge({
        id: "start-to-path-a",
        from: "start",
        to: "path-a",
        condition: "{{ start.start.decision === 'path-a' }}"
      });

      const startToPathBEdge = new Edge({
        id: "start-to-path-b",
        from: "start",
        to: "path-b",
        condition: "{{ start.start.decision === 'path-b' }}"
      });

      const pathAToEndEdge = new Edge({
        id: "path-a-to-end",
        from: "path-a",
        to: "end",
        condition: "true"
      });

      const pathBToEndEdge = new Edge({
        id: "path-b-to-end",
        from: "path-b",
        to: "end",
        condition: "true"
      });

      // Build workflow
      workflow.addNode(startNode);
      workflow.addNode(pathANode);
      workflow.addNode(pathBNode);
      workflow.addNode(endNode);
      workflow.addEdge(startToPathAEdge);
      workflow.addEdge(startToPathBEdge);
      workflow.addEdge(pathAToEndEdge);
      workflow.addEdge(pathBToEndEdge);
      workflow.setStartNode("start");
    });

    it("should follow path-a when initialDecision is path-a", async () => {
      // Execute workflow with path-a configuration
      const result = await workflowExecutor.executeWorkflow(workflow, {
        initialDecision: "path-a"
      });

      // Verify the result and flow
      expectNestedProperty(result, "start.decision", "path-a");
      expectNestedProperty(result, "path-a.output", "path-a-data");
      expectNestedProperty(result, "end.path", "A");
      expectNestedProperty(result, "end.output", "end-data");

      // Verify execution flow
      expect(executionFlow).toContainEqual({ nodeId: "start", status: "executing" });
      expect(executionFlow).toContainEqual({ nodeId: "path-a", status: "executing" });
      expect(executionFlow).toContainEqual({ nodeId: "end", status: "executing" });
      expect(executionFlow).not.toContainEqual({ nodeId: "path-b", status: "executing" });
    });

    it("should follow path-b when initialDecision is path-b", async () => {
      // Execute workflow with path-b configuration
      const result = await workflowExecutor.executeWorkflow(workflow, {
        initialDecision: "path-b"
      });

      // Verify the result
      expectNestedProperty(result, "start.decision", "path-b");
      expectNestedProperty(result, "path-b.output", "path-b-data");
      expectNestedProperty(result, "end.path", "B");
      expectNestedProperty(result, "end.output", "end-data");

      // Verify execution flow
      expect(executionFlow).toContainEqual({ nodeId: "start", status: "executing" });
      expect(executionFlow).toContainEqual({ nodeId: "path-b", status: "executing" });
      expect(executionFlow).toContainEqual({ nodeId: "end", status: "executing" });
      expect(executionFlow).not.toContainEqual({ nodeId: "path-a", status: "executing" });
    });
  });

  describe("Error handling", () => {
    beforeEach(() => {
      // Define nodes
      const startNode = new Node({
        id: "start",
        type: "standard" as NodeType,
        description: "Start node",
        processFn: async () => {
          return { output: "start-data" };
        }
      });

      const errorNode = new Node({
        id: "error-node",
        type: "standard" as NodeType,
        description: "Error node",
        processFn: async () => {
          const error = new Error("Intentional test error");
          (error as any).isTestError = true;
          throw error;
        }
      });

      const customErrorHandler = new Node({
        id: "custom-error-handler",
        type: "standard" as NodeType,
        description: "Custom error handler",
        processFn: async () => {
          return {
            handled: true,
            errorMessage: "Custom handled error"
          };
        }
      });

      // Define edges
      const startToErrorNode = new Edge({
        id: "start-to-error",
        from: "start",
        to: "error-node",
        condition: "{{ throwError }}"
      });

      const startToCustomHandler = new Edge({
        id: "start-to-custom",
        from: "start",
        to: "custom-error-handler",
        condition: "{{ useCustomHandler }}"
      });

      // Build workflow
      workflow.addNode(startNode);
      workflow.addNode(errorNode);
      workflow.addNode(customErrorHandler);
      workflow.addEdge(startToErrorNode);
      workflow.addEdge(startToCustomHandler);
      workflow.setStartNode("start");
    });

    it("should throw execution error when node throws an error", async () => {
      // Execute workflow with error path
      await expect(workflowExecutor.executeWorkflow(workflow, { throwError: true })).rejects.toThrow(
        "Intentional test error"
      );

      // Verify execution flow
      expect(executionFlow).toContainEqual({ nodeId: "start", status: "executing" });
      expect(executionFlow).toContainEqual({ nodeId: "start", status: "completed" });
      expect(executionFlow).toContainEqual({ nodeId: "error-node", status: "executing" });
      expect(executionFlow).toContainEqual({ nodeId: "error-node", status: "error" });
    });

    it("should use custom error handler when provided", async () => {
      // Execute workflow with custom error handling
      const result = await workflowExecutor.executeWorkflow(workflow, {
        useCustomHandler: true
      });

      // Verify custom error handling
      expectNestedProperty(result, "custom-error-handler.handled", true);
      expectNestedProperty(result, "custom-error-handler.errorMessage", "Custom handled error");
    });
  });
});
