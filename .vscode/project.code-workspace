{"folders": [{"name": "ROOT", "path": "../"}, {"name": "libraries/nest-modules", "path": "../libraries/nest-modules"}, {"name": "libraries/ui", "path": "../libraries/ui"}, {"name": "apps/portal-api", "path": "../apps/portal-api"}, {"name": "apps/portal", "path": "../apps/portal"}, {"name": "apps/backoffice", "path": "../apps/backoffice"}, {"name": "apps/backoffice-api", "path": "../apps/backoffice-api"}, {"name": "apps/cloud-functions", "path": "../apps/cloud-functions"}, {"name": "apps/lambda-functions", "path": "../apps/lambda-functions"}, {"name": "apps/bullmq-board", "path": "../apps/bullmq-board"}, {"name": "common/config", "path": "../common/config"}, {"name": "common/docker", "path": "../common/docker"}, {"name": "common/scripts", "path": "../common/scripts"}, {"name": "tools/infrastructure", "path": "../tools/infrastructure"}, {"name": "tools/tailwind", "path": "../tools/tailwind"}, {"name": "tools/utils", "path": "../tools/utils"}, {"name": "services", "path": "../services"}], "settings": {"editor.tabSize": 2, "editor.wordWrap": "on", "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "never", "source.organizeImports": "explicit"}, "[xml]": {"editor.defaultFormatter": "redhat.vscode-xml"}, "files.eol": "\n", "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/.hg/store/**": true, "**/dist/**": true, "**/temp/**": true}, "jest.runMode": "deferred", "jest.jestCommandLine": "npm run jest --", "jest.outputConfig": "test-results-based", "testing.automaticallyOpenTestResults": "neverOpen", "jest.disabledWorkspaceFolders": ["ROOT", "libraries/nest-modules", "libraries/ui", "apps/portal", "apps/backoffice", "apps/bullmq-board", "common/config", "common/docker", "common/scripts", "tools/tailwind", "tools/utils", "services"], "typescript.tsdk": "apps/portal-api/node_modules/typescript/lib"}, "extensions": {"recommendations": ["esbenp.prettier-vscode"]}}