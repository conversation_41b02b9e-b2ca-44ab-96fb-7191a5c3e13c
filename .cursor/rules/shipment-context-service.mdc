---
description: 
globs: apps/portal-api/src/core-agent/services/shipment-context.service.ts,apps/portal-api/src/core-agent/types/shipment-context.types.ts,**/*context*.ts
alwaysApply: false
---
# ShipmentContextService Implementation Guide

## Service Architecture

### Scope Pattern
The [ShipmentContextService](mdc:apps/portal-api/src/core-agent/services/shipment-context.service.ts) uses **Singleton scope** with **ModuleRef resolution** for REQUEST-scoped dependencies.

```typescript
@Injectable() // Singleton scope
export class ShipmentContextService {
  constructor(
    private readonly moduleRef: ModuleRef,
    @InjectDataSource() private readonly dataSource: DataSource,
    private readonly templateManagerService: TemplateManagerService, // Singleton
    private readonly candataService: CandataService // Singleton
  ) {}
}
```

### Why This Pattern?
- **Background Jobs**: BullMQ processors don't have REQUEST scope
- **Service Resolution**: Resolves REQUEST-scoped services dynamically
- **Context Isolation**: Each call gets proper organization context
- **Error Resilience**: Graceful handling of service resolution failures

## Business Rule Evaluation

### Core Principle
**ALL business logic evaluation happens in ShipmentContextService**
- Evaluated ONCE per context build
- Cached in ShipmentContext object
- Used by intent handlers and templates
- No business logic in response layers

### Safe Evaluation Pattern
```typescript
// ALWAYS use safeEvaluate for business rules
private safeEvaluate<T>(evaluation: () => T, fallback: T, ruleName: string): T {
  try {
    return evaluation();
  } catch (error) {
    this.logger.warn(`Business rule evaluation failed for ${ruleName}, using fallback: ${error.message}`);
    return fallback;
  }
}
```

### Business Rule Categories
```typescript
interface ShipmentContext {
  // Core capabilities
  canRush: boolean;
  canGenerateCAD: boolean;
  canGenerateRNSProof: boolean;
  canBeModified: boolean;
  
  // Status assessments
  isCompliant: boolean;
  isReleased: boolean;
  isSubmitted: boolean;
  isEntryUploaded: boolean;
  
  // Blocking reasons (user-friendly)
  rushBlockingReason: string;
  cadBlockingReason: string;
  
  // Data availability
  missingDocuments: string[];
  complianceErrors: string[];
  nonCompliantInvoices: any[];
}
```

## REQUEST-Scoped Service Resolution

### Resolution Pattern
```typescript
private async resolveRequestScopedServices(organization: Organization): Promise<ResolvedRequestScopedServices> {
  try {
    // Create context for REQUEST-scoped services
    const contextId = ContextIdFactory.create();
    
    // Register mock request with organization context
    this.moduleRef.registerRequestByContextId(generateRequest(null, organization), contextId);
    
    // Resolve all REQUEST-scoped services in parallel
    const [shipmentService, complianceValidationService, ...] = await Promise.all([
      this.moduleRef.resolve(ShipmentService, contextId, { strict: false }),
      this.moduleRef.resolve(ComplianceValidationService, contextId, { strict: false }),
      // ... other services
    ]);
    
    // Wait for dependency resolution
    await new Promise((resolve) => process.nextTick(resolve));
    
    return { shipmentService, complianceValidationService, ... };
  } catch (error) {
    throw new InternalServerErrorException(`Service resolution failed: ${error.message}`);
  }
}
```

### Service Validation
```typescript
private validateResolvedServices(services: ResolvedRequestScopedServices): void {
  const serviceEntries = [
    ["shipmentService", services.shipmentService],
    ["complianceValidationService", services.complianceValidationService],
    // ... other required services
  ];

  const missingServices = serviceEntries.filter(([name, service]) => !service).map(([name]) => name);

  if (missingServices.length > 0) {
    throw new InternalServerErrorException(`Failed to resolve required services: ${missingServices.join(", ")}`);
  }
}
```

## Context Building Process

### 1. Input Validation
```typescript
// Validate input parameters early
if (!shipmentId || shipmentId <= 0) {
  throw new BadRequestException(`Invalid shipmentId: ${shipmentId}`);
}
if (!organizationId || organizationId <= 0) {
  throw new BadRequestException(`Invalid organizationId: ${organizationId}`);
}
```

### 2. Critical Data Fetching
```typescript
// Critical organization lookup - fail fast
const organization = await this.fetchOrganization(organizationId, queryRunner);

// Fetch shipment and compliance data - critical data, fail fast
const { shipment, compliance } = await this.fetchShipmentData(
  shipmentId,
  services.shipmentService,
  services.shipmentComplianceQueryService,
  queryRunner
);
```

### 3. Business Rule Evaluation
```typescript
// ALL business rules evaluated with safe evaluation
return {
  shipment,
  compliance,
  organization,
  
  // Core capabilities
  canRush: this.safeEvaluate(() => this.canShipmentBeRushed(...), false, "canRush"),
  canGenerateCAD: this.safeEvaluate(() => isSendCADReady(shipment.customsStatus), false, "canGenerateCAD"),
  
  // Status assessments
  isCompliant: this.safeEvaluate(() => isReadyToSubmit(compliance), false, "isCompliant"),
  isSubmitted: this.safeEvaluate(() => services.complianceValidationService.isShipmentSubmitted(shipment), false, "isSubmitted"),
  
  // Enhanced analysis
  documentDataStatus: this.safeEvaluate(() => this.buildDocumentDataStatus(shipment, compliance), this.getEmptyDocumentDataStatus(), "documentDataStatus"),
  missingFieldsAnalysis: this.safeEvaluate(() => this.buildMissingFieldsAnalysis(compliance), this.getEmptyMissingFieldsAnalysis(), "missingFieldsAnalysis"),
  
  // Service instances for intent handlers
  _services: { emailService, rnsStatusChangeEmailSender, ... }
};
```

## Error Handling Strategy

### Multi-Level Error Handling
1. **Input Validation**: BadRequestException for invalid parameters
2. **Data Fetching**: NotFoundException for missing data
3. **Service Resolution**: InternalServerErrorException for service failures
4. **Business Rules**: Safe evaluation with fallbacks

### Error Classification
```typescript
// Re-throw known errors with proper classification
if (error instanceof NotFoundException || error instanceof BadRequestException) {
  throw error;
}

// Wrap unexpected errors
throw new InternalServerErrorException(
  `Unable to retrieve shipment information for shipment ${shipmentId}: ${error.message}`
);
```

## Business Rule Implementation Patterns

### Rule Evaluation Methods
```typescript
// Simple boolean evaluation
private canShipmentBeRushed(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto, complianceValidationService: ComplianceValidationService): boolean {
  return !complianceValidationService.isShipmentSubmitted(shipment) && isReadyToSubmit(compliance);
}

// User-friendly blocking reasons
private getRushBlockingReason(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto, complianceValidationService: ComplianceValidationService): string {
  if (complianceValidationService.isShipmentSubmitted(shipment)) {
    return "Shipment already submitted to customs";
  }
  if (compliance.noCommercialInvoice) {
    return "Missing commercial invoice";
  }
  if (compliance.missingFields?.length > 0) {
    return "Missing required shipment fields";
  }
  return `Cannot rush shipments with status: ${shipment.customsStatus}`;
}
```

### Document Data Analysis
```typescript
// Business rule based on compliance validation
private buildDocumentDataStatus(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto): DocumentDataStatus {
  const missingFields = compliance.missingFields || [];
  
  return {
    hasHBLDataForSubmission: this.hasHBLDataForSubmission(missingFields, shipment.modeOfTransport),
    hasAnEmfDataForSubmission: this.hasAnEmfDataForSubmission(missingFields, shipment.modeOfTransport),
    ciReceived: !compliance.noCommercialInvoice,
    plReceived: this.hasPackingListData(shipment),
    
    // Template-friendly status
    hblStatus: this.formatDocumentStatus(hasCompleteHBLData),
    anEmfStatus: this.formatDocumentStatus(hasCompleteAnEmfData),
    ciPlStatus: this.formatCIPlStatus(compliance.noCommercialInvoice, plReceived)
  };
}
```

## Query Runner Support

### Transaction Consistency
```typescript
// Support QueryRunner for transaction consistency
async buildContext(shipmentId: number, organizationId: number, queryRunner?: QueryRunner): Promise<ShipmentContext> {
  // Use QueryRunner when provided
  const organization = await this.fetchOrganization(organizationId, queryRunner);
  const { shipment, compliance } = await this.fetchShipmentData(shipmentId, services.shipmentService, services.shipmentComplianceQueryService, queryRunner);
}

private async fetchOrganization(organizationId: number, queryRunner?: QueryRunner): Promise<Organization> {
  return await (queryRunner?.manager ?? this.dataSource.manager).findOne(Organization, {
    where: { id: organizationId },
    relations: FIND_ORGANIZATION_RELATIONS
  });
}
```

## Fallback Methods

### Safe Defaults
```typescript
// Provide safe defaults for all context properties
private getEmptyShipmentIdentifiers() {
  return {
    hblNumber: null,
    cargoControlNumber: null,
    containerNumbers: [],
    formattedContainers: "",
    hasMultipleContainers: false
  };
}

private getEmptyDocumentDataStatus(): DocumentDataStatus {
  return {
    hasHBLDataForSubmission: false,
    hasAnEmfDataForSubmission: false,
    ciReceived: false,
    plReceived: false,
    hblStatus: "Missing",
    anEmfStatus: "Missing",
    ciPlStatus: "Missing"
  };
}
```

## Performance Optimization

### Service Resolution Caching
- Services resolved once per context build
- Parallel resolution for better performance
- Validation ensures all required services available

### Business Rule Efficiency
- All rules evaluated once in context service
- Results cached in context object
- Safe evaluation prevents cascading failures

### Memory Management
- Context objects are request-scoped
- Service instances shared where appropriate
- QueryRunner support for transaction efficiency

# Shipment Context Service - Business Rules Layer

## Overview
The [ShipmentContextService](mdc:apps/portal-api/src/core-agent/services/shipment-context.service.ts) is the **single source of truth** for all shipment-related business rule evaluation. It builds comprehensive context objects used throughout the response system.

## Core Responsibility
**Evaluate ALL business rules once** → Provide rich context to intent handlers → Avoid duplicate business logic

## Key Features

### NestJS Scope Strategy
- **Singleton scope** with **ModuleRef resolution** pattern
- Handles REQUEST-scoped dependencies in BullMQ processors
- Resolves services dynamically via `ContextIdFactory`

### Context Building Process
```typescript
// Primary method signature:
async buildContext(
  shipmentId: number, 
  organizationId: number,
  queryRunner?: QueryRunner
): Promise<ShipmentContext>
```

## ShipmentContext Interface Structure

### Raw Data
- `shipment: Shipment` - Core shipment entity
- `compliance: ValidateShipmentComplianceResponseDto` - Compliance validation
- `organization: Organization` - Organization details

### Business Rule Evaluations (Pre-computed)
- `canRush: boolean` - Rush processing eligibility
- `canGenerateCAD: boolean` - CAD document generation readiness
- `canGenerateRNSProof: boolean` - RNS proof availability  
- `isCompliant: boolean` - Overall compliance status
- `isReleased: boolean` - Release status
- `isSubmitted: boolean` - Customs submission status
- `canBeModified: boolean` - Modification eligibility
- `isEntryUploaded: boolean` - Entry upload status

### Detailed Information
- `missingDocuments: string[]` - List of missing required documents
- `complianceErrors: string[]` - Compliance validation errors
- `nonCompliantInvoices: any[]` - Non-compliant invoice details
- `rushBlockingReason: string` - Why rush processing is blocked
- `cadBlockingReason: string` - Why CAD generation is blocked

### Display Values (Formatted)
- `formattedCustomsStatus: string` - Human-readable customs status
- `shipmentIdentifiers: object` - HBL, CCN, etc.
- `etaInformation: object` - ETA details with port info
- `shippingInformation: object` - Tracking and status info

### User Interaction Tracking
- `directlyAsked: { [key: string]: boolean }` - Track what user explicitly requested

### Side Effects Storage
- `sideEffects: object` - Results from handler actions (CAD docs, alerts, etc.)

### Service Instances  
- `_services: object` - Resolved service instances for handlers to use

## Usage Patterns

### In Intent Handlers
```typescript
export class SomeIntentHandler extends BaseIntentHandler {
  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
    const fragments: ResponseFragment[] = [];
    
    // Use pre-computed business rules
    if (!context.isCompliant) {
      fragments.push({ template: 'compliance-errors', priority: 1 });
    }
    
    // Use detailed information
    if (context.missingDocuments.length > 0) {
      fragments.push({ template: 'missing-documents-list', priority: 2 });
    }
    
    // Use service instances for side effects
    if (context.canRush) {
      await context._services.emailService.sendRushAlert();
    }
    
    return fragments;
  }
}
```

### Error Handling Pattern
The service uses `safeEvaluate()` wrapper for all business rule evaluations:
```typescript
canRush: this.safeEvaluate(() => this.canShipmentBeRushed(...), false, 'canRush')
```
This ensures individual rule failures don't break entire context building.

## Development Guidelines

### Adding New Business Rules
1. Add evaluation logic in `buildContext()` method
2. Add property to `ShipmentContext` interface in [types file](mdc:apps/portal-api/src/core-agent/types/shipment-context.types.ts)
3. Use `safeEvaluate()` wrapper for error resilience
4. Provide meaningful default values

### Working with REQUEST-scoped Services
```typescript
// Services are resolved dynamically:
const contextId = ContextIdFactory.create();
this.moduleRef.registerRequestByContextId(
  generateRequest(null, organization), 
  contextId
);
const services = await this.resolveRequestScopedServices(organization);
```

### Performance Considerations
- Business rules evaluated **once per request**
- Results cached in context object
- Heavy computations done upfront, not in templates
- Service resolution optimized with parallel loading

## Integration Points

### Used By
- [Intent Handlers](mdc:apps/portal-api/src/core-agent/handlers) - All handlers receive ShipmentContext
- [Response Service](mdc:apps/portal-api/src/core-agent/services/shipment-response.service.ts) - For template rendering
- Email processors - When integrated

### Dependencies
- [ShipmentService](mdc:apps/portal-api/src/shipment) - Core shipment operations
- [ComplianceService](mdc:apps/portal-api/src/compliance) - Validation logic  
- [OrganizationService](mdc:apps/portal-api/src/organization) - Organization context
- [EmailService](mdc:apps/portal-api/src/email) - Communication services

## Testing
Use [ResponseServiceTestController](mdc:apps/portal-api/src/core-agent/controllers/test/response-service.test.controller.ts) to test context building with real shipment data.

## Best Practices

1. **Never duplicate business logic** - Always use context properties
2. **Add comprehensive error handling** - Use safeEvaluate pattern
3. **Pre-compute expensive operations** - Don't lazy-load in templates
4. **Provide meaningful defaults** - Ensure context is always usable
5. **Document new business rules** - Update interface and this rule
