---
description: 
globs: 
alwaysApply: false
---
# Portal API Architecture

The [portal-api](mdc:apps/portal-api/src) is built with NestJS and follows these architectural patterns:

## Core Features

- **core-agent**: LLM integration and agent services in [src/core-agent](mdc:apps/portal-api/src/core-agent)
- **document**: Document processing in [src/document](mdc:apps/portal-api/src/document)
- **email**: Email handling in [src/email](mdc:apps/portal-api/src/email)
- **llm**: LLM service integration in [src/llm](mdc:apps/portal-api/src/llm)
- **aggregation**: Data aggregation logic in [src/aggregation](mdc:apps/portal-api/src/aggregation)

## NestJS Pattern

- NestJS modules with controllers, services, and DTOs
- BullMQ for task queues
- Event-driven architecture with EventEmitter2
- TypeORM for database operations

## Key Files

- Entry point: [src/main.ts](mdc:apps/portal-api/src/main.ts)
- App module: [src/app.module.ts](mdc:apps/portal-api/src/app.module.ts)
