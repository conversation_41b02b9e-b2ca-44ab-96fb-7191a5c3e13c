---
description:
globs:
alwaysApply: false
---

---

description: PostgreSQL command line usage guidelines with proper quoting for shell commands, specifically for the Claro development database
globs: ["*.sh", "*.bash", "apps/portal-api/**/*.ts", "libraries/nest-modules/**/*.ts"]
alwaysApply: false

---

# PostgreSQL Command Line (PSQL) Rules

## ⚠️ **For Portal-API Testing: Use db-query.js Script Instead**

For portal-api core-agent testing, prefer the db-query.js helper script:

```bash
cd apps/portal-api
node src/core-agent/testing/db-query.js orgs
node src/core-agent/testing/db-query.js sql "SELECT * FROM shipment WHERE \"organizationId\" = 3 LIMIT 5;"
```

See `@core-agent-testing` and `@portal-api-database-testing` rules for details.

## Environment and Connection

Use the standard connection pattern for the Claro development database:

```bash
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "..." | cat
```

## ⚠️ **CRITICAL: Prevent PSQL Hanging**

**ALWAYS append `| cat` to PSQL commands to prevent hanging:**

```bash
# Correct - with | cat
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "SELECT * FROM users;" | cat

# Wrong - will hang in automated environments
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "SELECT * FROM users;"
```

### Why `| cat` is Required

- **PSQL uses a pager by default** which can cause commands to hang in non-interactive environments
- **The pager waits for user input** to navigate through results
- **`| cat` forces all output to be displayed immediately** without pagination
- **Essential for scripts, CI/CD, and automated processes**

### 🎯 **Memory Rule**

> **Every PSQL command MUST end with `| cat`** to prevent hanging.

## Quoting Rules for Shell Commands

### 🎯 **Core Quoting Rule**

When writing SQL inside a shell command:

- **Wrap the entire SQL statement in double quotes** `"like this"`
- **Use single quotes for string literals** `'like_this'` inside the SQL
- **NEVER use double quotes for string literals** — they're for identifiers in PostgreSQL
- **Escape double quotes if needed inside SQL**: `\"identifier\"`

### ✅ **Memory Rule**

> `'SQL literals in single quotes'` and `"wrap the whole thing in double quotes"` for the shell.

## Examples

### ✅ Correct Usage

```bash
# Simple query
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "SELECT * FROM users WHERE name = 'John Doe';" | cat

# Insert with string values
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "INSERT INTO users (name, email) VALUES ('Jane Smith', '<EMAIL>');" | cat

# Update with conditions
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "UPDATE users SET status = 'active' WHERE id = 123;" | cat

# Complex query with multiple string literals
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "SELECT * FROM shipments WHERE status IN ('pending', 'processing') AND created_at > '2024-01-01';" | cat

# Using escaped identifiers (if needed)
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "SELECT \"user_id\", name FROM users WHERE \"user_type\" = 'admin';" | cat
```

### ❌ Common Mistakes

```bash
# Wrong - using double quotes for string literals
psql -c "SELECT * FROM users WHERE name = "John Doe";" | cat

# Wrong - not wrapping SQL in quotes for shell
psql -c SELECT * FROM users WHERE name = 'John Doe'; | cat

# Wrong - mixing quote types incorrectly
psql -c 'SELECT * FROM users WHERE name = "John Doe";' | cat

# Wrong - missing | cat (will hang!)
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "SELECT * FROM users;"
```

## Common PSQL Commands

### Database Operations

```bash
# List databases
PGPASSWORD=superuser psql -U postgres -h localhost -c "\l" | cat

# List tables in current database
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "\dt" | cat

# Describe table structure
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "\d table_name" | cat

# Show table size
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "SELECT pg_size_pretty(pg_total_relation_size('table_name'));" | cat
```

### Migration and Schema Operations

```bash
# Run migration file
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -f migration.sql | cat

# Create database
PGPASSWORD=superuser psql -U postgres -h localhost -c "CREATE DATABASE new_db_name;" | cat

# Drop database (careful!)
PGPASSWORD=superuser psql -U postgres -h localhost -c "DROP DATABASE db_name;" | cat
```

## Shell Variables in SQL

When using shell variables in SQL commands, be extra careful with quoting:

```bash
# Correct - variable outside the SQL quotes
TABLE_NAME="users"
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "SELECT * FROM $TABLE_NAME WHERE status = 'active';" | cat

# Correct - using printf for complex interpolation
USER_ID=123
STATUS="pending"
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "$(printf "UPDATE shipments SET status = '%s' WHERE user_id = %d;" "$STATUS" "$USER_ID")" | cat
```

## Troubleshooting Quotes

If you get syntax errors:

1. **Check if string literals use single quotes** `'value'`
2. **Ensure the entire SQL is wrapped in double quotes** for the shell
3. **Verify no unescaped double quotes** inside the SQL
4. **Test the SQL directly in psql** before putting it in a shell command
5. **Verify `| cat` is appended** to prevent hanging

## Best Practices

- **Always append `| cat` to prevent hanging**
- Always test complex SQL queries directly in `psql` first
- Use `\q` to exit psql interactive mode
- Use `-t` flag to get tuple-only output (no headers)
- Use `-A` flag for unaligned output
- Use `-c` for single commands, `-f` for SQL files
- Consider using heredoc syntax for very complex multi-line SQL

```bash
# Heredoc example for complex SQL
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev <<EOF | cat
SELECT
    s.id,
    s.tracking_number,
    s.status
FROM shipments s
WHERE s.created_at > '2024-01-01'
    AND s.status IN ('pending', 'processing');
EOF
```
