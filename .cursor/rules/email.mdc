---
description:
globs: apps/portal-api/src/email/**/* apps/portal-api/src/email/* **/email/**/*.ts **/email/**/*.dto.ts **/email/**/*.service.ts **/email/**/*.processor.ts **/email/**/*.listener.ts **/email/**/*.controller.ts
alwaysApply: false
---

# Email Module Architecture Guide

The email module handles Gmail integration, email processing, and automated email responses. It has been simplified and now integrates closely with the core-agent module for AI-powered email processing.

## Module Structure

### Core Files

- **Module Definition**: [email.module.ts](mdc:apps/portal-api/src/email/email.module.ts) - Main module with dependencies and providers
- **Queue Types**: [queue.types.ts](mdc:apps/portal-api/src/email/types/queue.types.ts) - Queue definitions and job data interfaces (simplified)

### Directory Organization

```
email/
├── controllers/     # REST API endpoints
├── services/        # Business logic and external integrations
├── processors/      # BullMQ queue processors (simplified)
├── listeners/       # Event listeners for async operations
├── dto/            # Data transfer objects and validation
├── types/          # TypeScript interfaces and enums
├── templates/      # Nunjucks email templates
├── utils/          # Utility functions
├── test/           # Test services and controllers
└── decorators/     # Custom decorators
```

## Simplified Queue Architecture

### Current Queue Structure

The email processing has been significantly simplified with only one main queue:

- **GET_GMAIL_MESSAGE** - Fetches and processes emails from Gmail API

### Queue Configuration

```typescript
export const DEFAULT_JOB_OPTIONS: JobsOptions = {
  attempts: 1,
  removeOnComplete: { age: 3600, count: 1000 },
  removeOnFail: { age: 24 * 3600 }
};

export const DEFAULT_WORKER_OPTIONS: NestWorkerOptions = {
  concurrency: 1,
  lockDuration: 1000 * 60 * 2 // 2 minutes for LLM calls
};
```

### Integration with Core-Agent

The complex email processing pipeline has been moved to the core-agent module:

- Email saga processing is handled by `CoreAgentEmailSagaListener`
- Intent analysis and response generation moved to core-agent
- Email module focuses on Gmail integration and basic email management

## Core Services

### Email Service

The main [EmailService](mdc:apps/portal-api/src/email/services/email.service.ts) is request-scoped and handles:

- CRUD operations for emails
- Email template rendering
- Gmail API integration
- Email sending with proper RFC2822 formatting
- Thread management and Message-ID handling

### Gmail Service

The [GmailService](mdc:apps/portal-api/src/email/services/gmail.service.ts) handles:

- OAuth token management
- Message fetching with different formats (FULL, MINIMAL)
- Attachment processing
- History ID tracking for incremental sync
- Gmail API interactions

### Email Update Service

The [EmailUpdateService](mdc:apps/portal-api/src/email/services/email-update.service.ts) handles:

- Email status updates
- Batch operations
- Email metadata management

### Email Deletion Service

The [EmailDeletionService](mdc:apps/portal-api/src/email/services/email-deletion.service.ts) handles:

- Safe email deletion
- Cleanup operations
- Related data management

### RNS Proof Service

The [RnsProofService](mdc:apps/portal-api/src/email/services/rns-proof-service.ts) handles:

- RNS proof generation
- Specialized email attachments

## Processor Architecture

### Get Gmail Message Processor

The [GetGmailMessageProcessor](mdc:apps/portal-api/src/email/processors/get-gmail-message.processor.ts) handles:

- Gmail message fetching and parsing
- System email detection and filtering
- Organization context resolution
- Email record creation/updates
- File attachment processing
- Event emission for downstream processing

### Key Processor Features

- **Multi-tenancy Support**: Uses `ContextIdFactory` for organization scoping
- **System Email Filtering**: Prevents infinite loops from system emails
- **Transaction-based Processing**: Ensures data consistency
- **Attachment Handling**: Processes email attachments through FileService
- **Event Emission**: Triggers downstream processing via EventEmitter

## Event-Driven Integration

### Email Events

Events are defined in [event.types.ts](mdc:apps/portal-api/src/email/types/event.types.ts):

```typescript
export enum EmailEvent {
  EMAIL_SAVED = "email.saved"
  // ... other events
}
```

### Event Listeners

- **EmailListener** - Core email processing events
- **QueueListener** - Queue status and error handling
- **UserListener** - User-related email events

### Integration with Core-Agent

- Email events trigger core-agent processing
- Core-agent handles intent analysis and response generation
- Email module receives status updates from core-agent

## Test System

### Test Email Service

The [TestEmailService](mdc:apps/portal-api/src/email/test/services/test-email.service.ts) provides:

- Manual email testing capabilities
- Automated test scenarios
- Email pipeline testing
- Mock Gmail message generation
- Test data generation utilities

### Test Features

- **Multiple Test Scenarios**: Manual, auto, flexible testing
- **Pipeline Testing**: End-to-end email processing validation
- **Mock Gmail Integration**: Simulates Gmail API responses
- **Test Data Generation**: Creates realistic test emails
- **Cleanup Capabilities**: Manages test data lifecycle

## DTO and Validation

### Core DTOs

- **CreateEmailDto** - For new email creation
- **EditEmailDto** - For email updates
- **SendEmailDto** - For outgoing emails
- **ReplyEmailDto** - For email replies
- **GetEmailsDto** - For email queries

### Validation Configuration

```typescript
export const DEFAULT_VALIDATION_OPTIONS = {
  whitelist: true,
  forbidNonWhitelisted: true,
  transform: true
};
```

## Gmail Integration Patterns

### Message Processing

- **Format Strategy**: Use `FULL` for new emails, `MINIMAL` for existing
- **Threading**: Proper Message-ID and In-Reply-To handling
- **Attachment Processing**: Secure attachment handling with virus scanning
- **Rate Limiting**: Graceful handling of Gmail API limits

### Email Composition

- **RFC2822 Compliance**: Proper email formatting
- **Multipart Messages**: Support for text/HTML with attachments
- **Character Encoding**: UTF-8 support with proper encoding
- **Threading Headers**: Maintains conversation threads

## Error Handling

### System Email Detection

```typescript
const systemEmails = [
  this.configService.get<string>("SYSTEM_FROM_EMAIL"),
  this.configService.get<string>("BACKOFFICE_EMAIL_ADDRESS")
].filter(Boolean);

if (isSystemEmail) {
  createOrEditEmailDto.status = EmailStatus.SYSTEM_EMAIL_SKIPPED;
}
```

### Error Status Management

- **Specific Error Types**: `EmailStatus.FAILED_*` for different failure types
- **Retry Logic**: Configurable retry mechanisms
- **Error Logging**: Comprehensive error tracking
- **Graceful Degradation**: Fallback behavior for API failures

## Module Integration

### Dependencies

- **CoreAgentModule** - For AI-powered email processing
- **AggregationModule** - For data aggregation
- **DocumentModule** - For attachment handling
- **ShipmentModule** - For shipment associations
- **ImporterModule** - For importer management
- **TemplateManagerModule** - For email template rendering

### Service Patterns

```typescript
@Injectable({ scope: Scope.REQUEST })
export class EmailService {
  constructor(
    @InjectRepository(Email) private readonly emailRepository: Repository<Email>,
    @Inject(REQUEST) private readonly request: AuthenticatedRequest,
    @InjectDataSource() private readonly dataSource: DataSource,
    private readonly eventEmitter: EventEmitter2
  ) {}
}
```

## Development Guidelines

### Best Practices

1. **Request Scoping**: Use `@Scope(Scope.REQUEST)` for multi-tenant services
2. **Transaction Management**: Use QueryRunner for multi-step operations
3. **Event Emission**: Emit events at key processing stages
4. **Error Handling**: Use proper NestJS exception patterns
5. **System Email Filtering**: Always check for system emails to prevent loops
6. **Test Coverage**: Use TestEmailService for development and validation

### Testing Strategy

- **Unit Tests**: Focus on service logic and business rules
- **Integration Tests**: Test Gmail API interactions
- **End-to-End Tests**: Validate complete email processing flow
- **Mock Testing**: Use TestEmailService for controlled testing

### Performance Considerations

- **Concurrency Control**: Limited concurrency for Gmail API calls
- **Caching**: Smart caching of Gmail tokens and metadata
- **Batch Processing**: Efficient handling of multiple emails
- **Resource Management**: Proper cleanup of resources and connections

## Security Considerations

### Gmail API Security

- **OAuth Token Management**: Secure token storage and refresh
- **Rate Limiting**: Respect Gmail API quotas
- **Data Validation**: Validate all incoming email data
- **Attachment Security**: Scan attachments for malware

### Multi-tenancy Security

- **Organization Scoping**: Proper isolation between organizations
- **Access Control**: Role-based email access
- **Data Encryption**: Secure handling of email content
- **Audit Logging**: Track all email operations

## Monitoring and Observability

### Logging Strategy

- **Structured Logging**: Use NestJS Logger with proper context
- **Error Tracking**: Comprehensive error logging and alerting
- **Performance Metrics**: Track processing times and success rates
- **Queue Monitoring**: Monitor queue health and job failures

### Key Metrics

- **Email Processing Rate**: Emails processed per minute
- **Error Rate**: Percentage of failed email processing
- **Gmail API Usage**: API call counts and rate limiting
- **Queue Depth**: Number of pending jobs in queues
