---
description: 
globs: 
alwaysApply: false
---
# Email Processing System

The email processing system is located in [portal-api/src/email](mdc:apps/portal-api/src/email) and handles email receiving, parsing, and sending.

## Key Components

- **Controllers**: [email/controllers](mdc:apps/portal-api/src/email/controllers) - API endpoints for email actions
- **Services**: [email/services](mdc:apps/portal-api/src/email/services) - Email processing services
- **Processors**: [email/processors](mdc:apps/portal-api/src/email/processors) - Email content processors
- **Templates**: [email/templates](mdc:apps/portal-api/src/email/templates) - Email templates
- **Types**: [email/types](mdc:apps/portal-api/src/email/types) - Type definitions

## Processing Flow

1. Email receipt via controllers
2. Processing with appropriate processors
3. Template rendering for outgoing emails
4. Email sending through services

## Related Components

- **Mail Module in Portal**: [portal/src/modules/Mail](mdc:apps/portal/src/modules/Mail)
- **Email-related LLM integration**: For processing email content with LLMs

When implementing email features, follow the established patterns for processing, templating, and sending.
