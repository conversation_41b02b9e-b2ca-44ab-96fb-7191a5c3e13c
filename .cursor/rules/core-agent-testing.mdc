---
description: Core-agent email processing testing scripts, database access, and testing workflows
globs: apps/portal-api/src/core-agent/**/*,apps/portal-api/src/email/**/*,**/*test*.js,**/*test*.sh
---

# Core-Agent Email Processing Testing

This rule provides comprehensive guidance for testing the core-agent email processing system, including intent handlers, E2E pipeline testing, and database access patterns.

## Available Testing Scripts

### Directory Structure

```
apps/portal-api/src/core-agent/testing/
├── README.md                           # Comprehensive testing documentation
├── test-intent-handlers.js             # Main testing script for all 9 intent handlers
├── test-processor-direct.js            # Direct processor testing (bypasses email pipeline)
├── run-processor-test-with-logs.sh     # Processor test with automatic logging
├── find-test-shipments.js             # Shipment discovery helper
├── e2e-email-pipeline-nestjs.js        # End-to-end email pipeline testing
├── run-e2e-with-logs.sh               # E2E test with automatic logging
├── db-query.js                         # Database query helper
├── cleanup-emails-by-subject.js        # Email cleanup utilities
├── comprehensive-email-cleanup.js      # Advanced email cleanup
└── check-e2e-test-status.js           # E2E test status monitoring
```

### Primary Testing Scripts

#### 1. Intent Handler Testing

**File:** [test-intent-handlers.js](mdc:apps/portal-api/src/core-agent/testing/test-intent-handlers.js)
Tests all 9 intent handlers with real shipment data.

**Basic Usage:**

```bash
# Navigate to portal-api directory first
cd apps/portal-api

# Test all intents with demo organization
node src/core-agent/testing/test-intent-handlers.js

# Test specific intent
node src/core-agent/testing/test-intent-handlers.js --intent=REQUEST_CAD_DOCUMENT

# Test with specific shipment
node src/core-agent/testing/test-intent-handlers.js --shipment=123 --verbose

# Test without side effects (no emails sent)
node src/core-agent/testing/test-intent-handlers.js --no-side-effects
```

**Options:**

- `--org=ID` - Organization ID (default: 3 - demo org)
- `--shipment=ID` - Specific shipment ID to test with
- `--intent=INTENT_NAME` - Test only specific intent
- `--no-side-effects` - Skip side effects like backoffice emails
- `--verbose` - Show detailed logging and fragment details

#### 2. Direct Processor Testing

**File:** [test-processor-direct.js](mdc:apps/portal-api/src/core-agent/testing/test-processor-direct.js)
Tests the handle-request-message.processor.ts logic directly, bypassing the email pipeline.

**Basic Usage:**

```bash
# Run with automatic logging (preferred)
./src/core-agent/testing/run-processor-test-with-logs.sh

# Test specific intent with auto-selected shipment
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --verbose

# Test with custom instructions
node src/core-agent/testing/test-processor-direct.js --intent=REQUEST_CAD_DOCUMENT --instructions="Please send me the CAD document for my shipment"

# Test all intents for a specific shipment with HTML output
node src/core-agent/testing/test-processor-direct.js --shipment=123 --verbose --show-html
```

**Options:**

- `--org=ID` - Organization ID (default: 3 - demo org)
- `--shipment=ID` - Specific shipment ID to test with
- `--intent=INTENT_NAME` - Test only specific intent
- `--instructions="text"` - Custom instructions for the intent
- `--verbose` - Show detailed logging including fragment details
- `--show-html` - Show the full rendered HTML output
- `--no-side-effects` - Skip side effects like backoffice emails

#### 3. End-to-End Email Pipeline Testing

**File:** [e2e-email-pipeline-nestjs.js](mdc:apps/portal-api/src/core-agent/testing/e2e-email-pipeline-nestjs.js)
Tests the complete email processing pipeline from email creation to response.

**Basic Usage:**

```bash
# Run with automatic logging (preferred)
./src/core-agent/testing/run-e2e-with-logs.sh

# Quick test with all defaults (includes startup cleanup)
node src/core-agent/testing/e2e-email-pipeline-nestjs.js

# Custom configuration
node src/core-agent/testing/e2e-email-pipeline-nestjs.js --inbox-email=<EMAIL> --no-attachments
```

**Options:**

- `--org=ID` - Organization ID (default: 3 - demo org)
- `--inbox-email=EMAIL` - Inbox email address (default: <EMAIL>)
- `--from-email=EMAIL` - From email address (default: <EMAIL>)
- `--subject=SUBJECT` - Email subject (default: auto-generated with unique HBL)
- `--body=BODY` - Email body message (default: "Please process customs")
- `--no-attachments` - Send email without attachments
- `--no-startup-cleanup` - Skip checking for and cleaning up leftover test data

#### 4. Shipment Discovery

**File:** [find-test-shipments.js](mdc:apps/portal-api/src/core-agent/testing/find-test-shipments.js)
Helps find suitable shipments for testing different scenarios.

**Basic Usage:**

```bash
# Find diverse shipments for testing
node src/core-agent/testing/find-test-shipments.js

# Show analysis of all customs statuses
node src/core-agent/testing/find-test-shipments.js --analysis

# Find shipments with specific status
node src/core-agent/testing/find-test-shipments.js --status=released
```

### Shell Scripts with Logging

#### 1. Processor Test with Logs

**File:** [run-processor-test-with-logs.sh](mdc:apps/portal-api/src/core-agent/testing/run-processor-test-with-logs.sh)

- Runs processor test and saves output to `.ai-reference/processor-test-{timestamp}.log`
- Automatically creates log directory and provides log file path
- Passes through all command line arguments

#### 2. E2E Test with Logs

**File:** [run-e2e-with-logs.sh](mdc:apps/portal-api/src/core-agent/testing/run-e2e-with-logs.sh)

- Runs E2E test and saves output to `/tmp/e2e-email-test-{timestamp}.log`
- Automatically provides log file path for analysis
- Passes through all command line arguments

## Database Access Patterns

### Database Access

```bash
# Use db-query.js script (preferred method)
cd apps/portal-api

# Query organizations
node src/core-agent/testing/db-query.js orgs

# Query shipments for specific organization
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"customsStatus\", \"transactionNumber\", \"etaPort\", \"releaseDate\", \"trackingStatus\" FROM shipment WHERE \"organizationId\" = 3 ORDER BY \"createDate\" DESC LIMIT 5;"

# Execute custom SQL queries
node src/core-agent/testing/db-query.js sql "SELECT COUNT(*) FROM shipment WHERE \"organizationId\" = 3"
```

### Database Query Helper

**File:** [db-query.js](mdc:apps/portal-api/src/core-agent/testing/db-query.js)

```bash
# List organizations
node src/core-agent/testing/db-query.js orgs

# Find shipments with all key fields for testing (recommended)
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"customsStatus\", \"transactionNumber\", \"etaPort\", \"releaseDate\", \"trackingStatus\" FROM shipment WHERE \"organizationId\" = 3 ORDER BY \"createDate\" DESC LIMIT 5;"

# Execute custom SQL
node src/core-agent/testing/db-query.js sql "SELECT COUNT(*) FROM shipment WHERE \"organizationId\" = 3"
```

## Organization Information

### Key Organizations

- **Organization 1**: Antek (Trading type) - Production organization
- **Organization 3**: Demo organization (<EMAIL>)

### Test Email Addresses

- **Development Account**: <EMAIL> (can receive emails)
- **Demo Organization**: <EMAIL>
- **Default Inbox**: <EMAIL>

## Intent Handlers Reference

### Available Intent Types

| Intent                      | Description               | Side Effects        | Requirements |
| --------------------------- | ------------------------- | ------------------- | ------------ |
| `GET_SHIPMENT_STATUS`       | Status inquiries          | CoreAgent answers   | Shipment     |
| `PROCESS_DOCUMENT`          | Document processing       | Entry submission    | Shipment     |
| `REQUEST_RUSH_PROCESSING`   | Rush requests             | Backoffice alerts   | None         |
| `DOCUMENTATION_COMING`      | Acknowledge incoming docs | None                | None         |
| `UPDATE_SHIPMENT`           | Update shipment info      | Field updates       | Shipment     |
| `REQUEST_CAD_DOCUMENT`      | CAD document requests     | Document generation | Shipment     |
| `REQUEST_RNS_PROOF`         | RNS proof requests        | Document generation | Shipment     |
| `REQUEST_MANUAL_PROCESSING` | Manual processing         | Backoffice alerts   | None         |
| `REQUEST_HOLD_SHIPMENT`     | Hold processing           | Backoffice alerts   | None         |

## Testing Workflow Best Practices

### 1. Start with Shipment Discovery

```bash
# Find suitable shipments
node src/core-agent/testing/find-test-shipments.js --analysis
```

### 2. Test Specific Scenarios

```bash
# Test CAD document generation (requires released shipment)
node src/core-agent/testing/find-test-shipments.js --status=released
node src/core-agent/testing/test-intent-handlers.js --shipment=789 --intent=REQUEST_CAD_DOCUMENT

# Test document processing (requires shipment with pending status)
node src/core-agent/testing/find-test-shipments.js --status=live
node src/core-agent/testing/test-intent-handlers.js --shipment=123 --intent=PROCESS_DOCUMENT
```

### 3. Use Logging Scripts for Analysis

```bash
# Always prefer logging scripts for detailed analysis
./src/core-agent/testing/run-processor-test-with-logs.sh --intent=GET_SHIPMENT_STATUS --verbose
./src/core-agent/testing/run-e2e-with-logs.sh --org=3
```

### 4. Development vs Production Testing

```bash
# Use --no-side-effects for development testing
node src/core-agent/testing/test-intent-handlers.js --no-side-effects --verbose

# Use demo organization (3) for safe testing
node src/core-agent/testing/test-intent-handlers.js --org=3
```

## Log Analysis and Monitoring

### Log Locations

- **Processor Tests**: `.ai-reference/processor-test-{timestamp}.log`
- **E2E Tests**: `/tmp/e2e-email-test-{timestamp}.log`
- **Application Logs**: Use CloudWatch logs for production analysis

### E2E Test Status Monitoring

**File:** [check-e2e-test-status.js](mdc:apps/portal-api/src/core-agent/testing/check-e2e-test-status.js)

```bash
# Monitor E2E test progress
node src/core-agent/testing/check-e2e-test-status.js --test-id=e2e-test-{timestamp}
```

## Email Cleanup and Maintenance

### Cleanup Test Emails

```bash
# Clean up emails by subject pattern
node src/core-agent/testing/cleanup-emails-by-subject.js --pattern="E2E Test" --dry-run

# Comprehensive cleanup (use with caution)
node src/core-agent/testing/comprehensive-email-cleanup.js --dry-run
```

### Safety Patterns

- Always use `--dry-run` first to preview changes
- Use demo organization (3) for testing
- Use `--no-side-effects` to prevent actual email sending
- Check log files for detailed analysis

## Common Testing Commands

### Quick Testing

```bash
# Test specific intent with logging
./src/core-agent/testing/run-processor-test-with-logs.sh --intent=GET_SHIPMENT_STATUS --verbose

# Run full E2E test
./src/core-agent/testing/run-e2e-with-logs.sh

# Find suitable shipments
node src/core-agent/testing/find-test-shipments.js --analysis
```

### Database Queries

```bash
# Check organizations
node src/core-agent/testing/db-query.js orgs

# Check recent emails
node src/core-agent/testing/db-query.js sql "SELECT id, subject, status, \"organizationId\" FROM email WHERE \"createDate\" > NOW() - INTERVAL '1 hour' ORDER BY \"createDate\" DESC;"
```

### Environment Setup

- Always navigate to `apps/portal-api` directory before running scripts
- Use `db-query.js` script for database queries instead of direct psql commands
- Prefer shell scripts with logging for detailed analysis
- Use demo organization (3) for safe testing

## Troubleshooting

### Common Issues

1. **Database Connection**: Ensure PostgreSQL is running and credentials are correct
2. **Permission Errors**: Navigate to `apps/portal-api` directory first
3. **Missing Shipments**: Use `find-test-shipments.js` to discover available data
4. **Side Effects**: Use `--no-side-effects` to prevent actual email sending during development

### Log Analysis

- Check processor logs in `.ai-reference/` directory
- Check E2E logs in `/tmp/` directory
- Use `--verbose` flag for detailed output
- Monitor CloudWatch logs for production issues

## Critical Testing Learnings

### Template Compilation Requirements

**CRITICAL**: New templates must be compiled before testing:

```bash
# Always run build after creating new templates
cd apps/portal-api
rushx build

# Or from project root
rush build --to portal-api

# Verify templates exist in dist directory
ls -la dist/core-agent/templates/
```

**Issue**: Templates created in `src/` are not automatically available until compiled to `dist/`.
**Solution**: Always run `rushx build` or `rush build --to portal-api` after creating new template files.

### GET_SHIPMENT_STATUS Specific Answer Templates

The GET_SHIPMENT_STATUS handler uses LLM-based question classification with specific templates:

```bash
# Test ETA questions - should use answer-eta-template
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="What is the ETA?" --verbose

# Test transaction number - should use answer-transaction-number-template
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="What is the transaction number?" --verbose

# Test release status - should use answer-release-status-template
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="Has the shipment been released?" --verbose

# Test shipping status - should use answer-shipping-status-template
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="What is the shipping status?" --verbose
```

**Expected Template Usage**:

- ETA questions → `answer-eta-template` (Priority 1)
- Transaction number → `answer-transaction-number-template` (Priority 2)
- Release status → `answer-release-status-template` (Priority 3)
- Shipping status → `answer-shipping-status-template` (Priority 4)
- General status → `core-agent/fragments/status-message` + `core-agent/fragments/details` (Priority 10-11)

### Template Context Structure for Specific Answers

All specific answer templates use this context structure:

```typescript
{
  template: "answer-eta-template",
  priority: 1,
  fragmentContext: {
    answer: "The estimated time of arrival (ETA) is June 25, 2025."
  }
}
```

**Template Pattern**:

```nunjucks
{# ETA-specific answer template #}
{# Context: { answer: string } #}
{{ answer }}
```

### Priority Ordering Validation

Specific answers get higher priority than general status:

```bash
# Test priority ordering - specific answer should appear first
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="Has the shipment been released?" --verbose
```

**Expected Fragment Order**:

1. `answer-release-status-template` (Priority 1) - Specific answer
2. `core-agent/fragments/status-message` (Priority 10) - General status
3. `core-agent/fragments/details` (Priority 11) - Supporting details

### Fallback Handling Testing

Test fallback behavior when data is missing:

```bash
# Test with shipment missing transaction number
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="What is the transaction number?" --verbose

# Expected: "The transaction number is not yet available. It will be provided once the entry is submitted to customs."
```

**Fallback Patterns**:

- Missing transaction number → "not yet available" message
- Missing release date → "not yet been released" message
- Missing ETA → Uses available `etaPort` data
- Missing shipping status → Shows actual `trackingStatus` value

### Performance Benchmarks

Based on GET_SHIPMENT_STATUS testing:

| Test Type          | Average Time | Status     |
| ------------------ | ------------ | ---------- |
| ETA Question       | 996ms        | ✅ Good    |
| Transaction Number | 783ms        | ✅ Good    |
| Release Status     | 768ms        | ✅ Good    |
| Shipping Status    | 1141ms       | ⚠️ Monitor |
| Mixed Questions    | 714ms        | ✅ Good    |

**Performance Thresholds**:

- ✅ Good: < 1000ms
- ⚠️ Monitor: 1000-1500ms
- ❌ Investigate: > 1500ms

### Mixed Question Behavior

Multi-question strings are processed as single queries:

```bash
# This gets classified as single category (ETA in this case)
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="What is the status? When will it arrive? What is the transaction number?" --verbose
```

**Expected**: LLM picks dominant question type, not multiple classifications.
**Design**: Current implementation processes entire instruction as one query.
