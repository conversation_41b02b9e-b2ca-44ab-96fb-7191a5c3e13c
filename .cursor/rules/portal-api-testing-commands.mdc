---
description: Portal-API testing commands, scripts, and execution best practices
---

# Portal-API Testing Commands

## Essential Test Commands

### Always Navigate to portal-api Directory First

```bash
cd apps/portal-api
```

### Intent Handler Testing

```bash
# Test all intent handlers
node src/core-agent/testing/test-intent-handlers.js --verbose

# Test specific intent
node src/core-agent/testing/test-intent-handlers.js --intent=GET_SHIPMENT_STATUS --verbose

# Test with specific shipment
node src/core-agent/testing/test-intent-handlers.js --shipment=123 --verbose

# Test without side effects (no emails sent)
node src/core-agent/testing/test-intent-handlers.js --no-side-effects --verbose
```

### Direct Processor Testing

```bash
# Use logging script (preferred)
./src/core-agent/testing/run-processor-test-with-logs.sh --intent=GET_SHIPMENT_STATUS --verbose

# Direct execution with custom instructions
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="What is the ETA?" --verbose
```

### E2E Email Pipeline Testing

```bash
# Use logging script (preferred)
./src/core-agent/testing/run-e2e-with-logs.sh

# Direct execution
node src/core-agent/testing/e2e-email-pipeline-nestjs.js --org=3
```

### Database Queries

```bash
# Use db-query.js script (preferred method)
node src/core-agent/testing/db-query.js orgs
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"customsStatus\", \"transactionNumber\", \"etaPort\", \"releaseDate\", \"trackingStatus\" FROM shipment WHERE \"organizationId\" = 3 ORDER BY \"createDate\" DESC LIMIT 5;"
node src/core-agent/testing/db-query.js sql "SELECT COUNT(*) FROM shipment WHERE \"organizationId\" = 3"

# Get recent emails
node src/core-agent/testing/db-query.js recent 10
```

### Shipment Discovery

```bash
# Find test shipments
node src/core-agent/testing/find-test-shipments.js --analysis

# Find shipments with specific status
node src/core-agent/testing/find-test-shipments.js --status=released
```

## Best Practices

### Use Demo Organization (ID 3)

Always test with the demo organization for safety:

```bash
--org=3
```

### Use Logging Scripts

Prefer shell scripts with logging over direct node execution:

```bash
# Preferred
./src/core-agent/testing/run-processor-test-with-logs.sh --intent=GET_SHIPMENT_STATUS --verbose

# Instead of
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --verbose
```

### Prevent Side Effects During Development

Use `--no-side-effects` to prevent actual email sending:

```bash
node src/core-agent/testing/test-intent-handlers.js --no-side-effects --verbose
```

## Log File Locations

- **Processor Tests**: `.ai-reference/processor-test-{timestamp}.log`
- **E2E Tests**: `/tmp/e2e-email-test-{timestamp}.log`
- **Application Logs**: Check CloudWatch for production analysis
