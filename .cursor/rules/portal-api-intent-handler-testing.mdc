---
description: Intent handler testing patterns, validation, and troubleshooting for portal-api core-agent
---

# Portal-API Intent Handler Testing

## Available Intent Handlers

### 9 Core Intent Types

1. **GET_SHIPMENT_STATUS** - Status inquiries and specific questions
2. **PROCESS_DOCUMENT** - Document processing and entry submission
3. **REQUEST_RUSH_PROCESSING** - Rush processing requests
4. **DOCUMENTATION_COMING** - Acknowledge incoming documentation
5. **UPDATE_SHIPMENT** - Update shipment information
6. **REQUEST_CAD_DOCUMENT** - CAD document requests
7. **REQUEST_RNS_PROOF** - RNS proof of release requests
8. **REQUEST_MANUAL_PROCESSING** - Manual processing requests
9. **REQUEST_HOLD_SHIPMENT** - Hold processing requests

## Intent Handler Testing Commands

### Test All Intent Handlers

```bash
cd apps/portal-api
node src/core-agent/testing/test-intent-handlers.js --org=3 --verbose
```

### Test Specific Intent Handler

```bash
# Test GET_SHIPMENT_STATUS handler
node src/core-agent/testing/test-intent-handlers.js --intent=GET_SHIPMENT_STATUS --verbose

# Test with specific shipment
node src/core-agent/testing/test-intent-handlers.js --intent=GET_SHIPMENT_STATUS --shipment=123 --verbose
```

### Test Without Side Effects

```bash
# Prevent actual email sending during development
node src/core-agent/testing/test-intent-handlers.js --intent=GET_SHIPMENT_STATUS --no-side-effects --verbose
```

## Intent Handler Structure

### Handler Requirements

- Extends `BaseIntentHandler`
- Implements `IntentClassificationMeta` interface
- Has `handle()` method returning `ResponseFragment[]`
- Uses dependency injection for services

### Fragment Generation Pattern

```typescript
// Example fragment structure
{
  template: "answer-eta-template",
  priority: 1,
  fragmentContext: { answer: "The ETA is January 15, 2024." }
}
```

## GET_SHIPMENT_STATUS Handler Testing

### Question Types and Expected Templates

- **ETA Questions**: Uses `answer-eta-template`
- **Transaction Number**: Uses `answer-transaction-number-template`
- **Release Status**: Uses `answer-release-status-template`
- **Shipping Status**: Uses `answer-shipping-status-template`
- **General Status**: Uses `core-agent/fragments/status-message` + `core-agent/fragments/details`

### Test Scenarios

```bash
# Test ETA questions
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="When will my shipment arrive?" --verbose

# Test transaction number questions
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="What is the transaction number?" --verbose

# Test release status questions
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="Has the shipment been released?" --verbose

# Test mixed questions
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="What is the status? When will it arrive?" --verbose
```

## Template System

### Template Registration

Templates must be registered in [constants/templates.ts](mdc:apps/portal-api/src/core-agent/constants/templates.ts):

```typescript
export const TEMPLATE_ORDER = [
  // Specific answer templates (priority 31-35)
  "answer-eta-template",
  "answer-transaction-number-template",
  "answer-release-status-template",
  "answer-shipping-status-template"
  // ...
];
```

### Template Structure

All new templates follow this pattern:

```nunjucks
{# Description template #}
{# Context: { answer: string } #}
{{ answer }}
```

## Validation and Testing

### What to Look For

- **Handler Execution**: No errors during handler execution
- **Fragment Generation**: Correct templates with proper priorities
- **Context Passing**: Fragment context contains expected data
- **Template Rendering**: Templates render to valid HTML
- **Question Classification**: LLM correctly categorizes questions

### Common Issues

- **Handler Not Registered**: Check `core-agent.module.ts` for proper registration
- **Template Not Found**: Verify template file exists and is in `TEMPLATE_ORDER`
- **Context Errors**: Check `fragmentContext` structure matches template expectations
- **Classification Errors**: Verify `AnswerUserQueryService` is properly injected

## Fragment Priority System

### Priority Levels

- **1-4**: Specific answer templates (highest priority)
- **10-11**: General status fragments
- **31-35**: Template fallback order from constants
- **36+**: Compliance errors and support templates

### Priority Testing

```bash
# Test priority ordering with mixed questions
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --instructions="What is the status? When will it arrive? What is the transaction number?" --verbose --show-html
```

## Performance Testing

### Metrics to Monitor

- Handler execution time
- Fragment generation count
- Template rendering time
- Response size
- Classification accuracy

### Performance Commands

```bash
# Use verbose flag to see timing information
node src/core-agent/testing/test-intent-handlers.js --intent=GET_SHIPMENT_STATUS --verbose
```

## Troubleshooting

### Debug Steps

1. **Check Handler Registration**: Verify handler is in `INTENT_HANDLERS` array
2. **Validate Templates**: Ensure templates exist and are registered
3. **Test Classification**: Verify question classification is working
4. **Check Context**: Ensure fragment context matches template expectations
5. **Review Logs**: Check `.ai-reference/` logs for detailed errors

### Debug Commands

```bash
# Get detailed fragment information
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --verbose --show-html

# Test with specific shipment data
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --shipment=123 --verbose
```

## Best Practices

### Development Testing

- Always use `--org=3` (demo organization)
- Use `--no-side-effects` to prevent email sending
- Test with `--verbose` for detailed output
- Use logging scripts for analysis

### Production Validation

- Test with real shipment data
- Verify all question types work correctly
- Check template rendering with various data states
- Monitor performance metrics
