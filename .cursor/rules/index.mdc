---
description: 
globs: 
alwaysApply: false
---
# Claro Codebase Rules

This index provides quick access to rules for navigating and understanding the Claro codebase.

## Project Structure

- [Project Overview](mdc:.cursor/rules/project-overview.mdc) - Overview of the monorepo structure
- [Rush Monorepo](mdc:.cursor/rules/rush-monorepo.mdc) - Rush monorepo configuration and conventions

## Architecture Rules

- [Portal API Architecture](mdc:.cursor/rules/portal-api-architecture.mdc) - Structure of the portal-api
- [Frontend Architecture](mdc:.cursor/rules/frontend-architecture.mdc) - Structure of the frontend applications
- [NestJS Patterns](mdc:.cursor/rules/nestjs-patterns.mdc) - Common NestJS patterns used in the backend

## Feature Systems

- [Document Processing](mdc:.cursor/rules/document-processing.mdc) - Document processing system
- [Aggregation System](mdc:.cursor/rules/aggregation-system.mdc) - Data aggregation system
- [Email Processing](mdc:.cursor/rules/email-processing.mdc) - Email processing system
- [LLM Integration](mdc:.cursor/rules/llm-integration.mdc) - LLM integration patterns
- [Queue Processing](mdc:.cursor/rules/queue-processing.mdc) - Queue processing with BullMQ

## UI and Components

- [UI Component Library](mdc:.cursor/rules/ui-components.mdc) - Shared UI component library

## Best Practices

- [Coding Standards](mdc:.cursor/rules/coding-standards.mdc) - Coding standards and best practices

Use these rules as a reference when working with the codebase to ensure consistency and follow established patterns.
