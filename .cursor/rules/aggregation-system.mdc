---
description: 
globs: 
alwaysApply: false
---
# Aggregation System

The data aggregation system is located in [portal-api/src/aggregation](mdc:apps/portal-api/src/aggregation) and provides a framework for combining and processing data from various sources.

## Key Components

- **Adapters**: [aggregation/adapter](mdc:apps/portal-api/src/aggregation/adapter) - Convert data between formats
- **Generators**: [aggregation/generator](mdc:apps/portal-api/src/aggregation/generator) - Generate aggregated data
- **Intents**: [aggregation/intents](mdc:apps/portal-api/src/aggregation/intents) - Define data processing intents
- **Executers**: [aggregation/executer](mdc:apps/portal-api/src/aggregation/executer) - Execute aggregation processes
- **Transformers**: [aggregation/transformers](mdc:apps/portal-api/src/aggregation/transformers) - Transform data formats
- **Workflows**: [aggregation/workflows](mdc:apps/portal-api/src/aggregation/workflows) - Define aggregation workflows

## Processing Flow

1. Define intent for data aggregation
2. Generate data using appropriate generators
3. Transform data as needed
4. Execute workflow for the aggregation
5. Process results through appropriate adapters

## Related Components

- **Unit Conversion**: [aggregation/unit-conversion](mdc:apps/portal-api/src/aggregation/unit-conversion)
- **Field Fallback**: [aggregation/field-fallback](mdc:apps/portal-api/src/aggregation/field-fallback)

When implementing aggregation features, follow the established patterns for intents, generators, and workflows.
