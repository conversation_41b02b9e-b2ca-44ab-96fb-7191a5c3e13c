---
alwaysApply: true
---

# Portal-API PSQL CLI Database Access and Testing

## Database Connection

### Preferred Method: db-query.js Script

```bash
# Always navigate to portal-api directory first
cd apps/portal-api

# Use db-query.js script for all database operations
node src/core-agent/testing/db-query.js [command] [parameters]
```

### Database Configuration

- **Database**: `claro_dev`
- **User**: `postgres`
- **Password**: `superuser`
- **Host**: `localhost`

### Legacy Method (Not Recommended)

```bash
# When psql must be used, always use PGPASSWORD to avoid interactive prompts
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "SQL_COMMAND"
```

## Key Organizations

### Organization Data

```bash
# List all organizations (preferred method)
node src/core-agent/testing/db-query.js orgs

# Demo organization (safe for testing)
node src/core-agent/testing/db-query.js sql "SELECT * FROM organization WHERE id = 3;"
```

### Organization Details

- **Organization 1**: Antek (Trading type) - Production organization
- **Organization 3**: Demo organization - Safe for testing
  - Email: `<EMAIL>`
  - Use for all testing scenarios

## Shipment Queries

### Find Test Shipments

```bash
# Find shipments for demo organization (preferred method)
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"customsStatus\", \"transactionNumber\", \"etaPort\", \"releaseDate\", \"trackingStatus\" FROM shipment WHERE \"organizationId\" = 3 ORDER BY \"createDate\" DESC LIMIT 5;"

# Find shipments by status
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"customsStatus\", \"createDate\" FROM shipment WHERE \"organizationId\" = 3 AND \"customsStatus\" = 'released' ORDER BY \"createDate\" DESC;"

# Count shipments by status
node src/core-agent/testing/db-query.js sql "SELECT \"customsStatus\", COUNT(*) as count FROM shipment WHERE \"organizationId\" = 3 GROUP BY \"customsStatus\" ORDER BY count DESC;"
```

### Shipment Fields for Testing

- **id**: Shipment identifier
- **hblNumber**: House Bill of Lading number
- **customsStatus**: live, released, pending, etc.
- **transactionNumber**: For transaction number queries
- **etaPort**: For ETA queries
- **releaseDate**: For release status queries
- **trackingStatus**: For shipping status queries

## Email Testing Data

### Email Queries

```bash
# Recent emails for organization (preferred method)
node src/core-agent/testing/db-query.js sql "SELECT id, subject, status, \"organizationId\", \"createDate\" FROM email WHERE \"organizationId\" = 3 AND \"createDate\" > NOW() - INTERVAL '1 hour' ORDER BY \"createDate\" DESC;"

# Get recent emails using helper command
node src/core-agent/testing/db-query.js recent 10

# Clean up test emails (use cleanup scripts instead)
node src/core-agent/testing/cleanup-emails-by-subject.js --pattern="E2E Test" --dry-run
```

### Test Email Addresses

- **Development**: `<EMAIL>`
- **Demo Organization**: `<EMAIL>`
- **Default Inbox**: `<EMAIL>`

## Database Helper Scripts

### Using db-query.js

```bash
# List organizations
node src/core-agent/testing/db-query.js orgs

# Find shipments with all key fields for testing (recommended)
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"customsStatus\", \"transactionNumber\", \"etaPort\", \"releaseDate\", \"trackingStatus\" FROM shipment WHERE \"organizationId\" = 3 ORDER BY \"createDate\" DESC LIMIT 5;"

# Get recent emails
node src/core-agent/testing/db-query.js recent 10

# Execute custom SQL
node src/core-agent/testing/db-query.js sql "SELECT COUNT(*) FROM shipment WHERE \"organizationId\" = 3"
```

### Using find-test-shipments.js

```bash
# Find diverse shipments for testing
node src/core-agent/testing/find-test-shipments.js --analysis

# Find shipments with specific status
node src/core-agent/testing/find-test-shipments.js --status=released
```

## Safety Practices

### Always Use Demo Organization

- Use `--org=3` for all testing
- Never test with production organization (ID 1)

### Verify Data Before Testing

```bash
# Check if demo organization exists
node src/core-agent/testing/db-query.js sql "SELECT id, name FROM organization WHERE id = 3;"

# Check if shipments exist
node src/core-agent/testing/db-query.js sql "SELECT COUNT(*) FROM shipment WHERE \"organizationId\" = 3;"
```

### Clean Up Test Data

```bash
# Use cleanup scripts with --dry-run first
node src/core-agent/testing/cleanup-emails-by-subject.js --pattern="E2E Test" --dry-run
```

## Common Database Patterns

### Shipment Discovery for Intent Testing

```bash
# Find shipments good for GET_SHIPMENT_STATUS testing
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"customsStatus\", \"transactionNumber\", \"etaPort\", \"releaseDate\" FROM shipment WHERE \"organizationId\" = 3 AND \"customsStatus\" IS NOT NULL ORDER BY \"createDate\" DESC;"

# Use the dedicated shipment discovery script (recommended)
node src/core-agent/testing/find-test-shipments.js --analysis
```

### Find Shipments by Scenario

```bash
# For ETA testing (shipments with ETA data)
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"etaPort\" FROM shipment WHERE \"organizationId\" = 3 AND \"etaPort\" IS NOT NULL;"

# For transaction number testing
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"transactionNumber\" FROM shipment WHERE \"organizationId\" = 3 AND \"transactionNumber\" IS NOT NULL;"

# For release status testing
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"customsStatus\", \"releaseDate\" FROM shipment WHERE \"organizationId\" = 3 AND \"customsStatus\" = 'released';"

# Use the dedicated shipment discovery script for specific scenarios
node src/core-agent/testing/find-test-shipments.js --status=released
```

node src/core-agent/testing/find-test-shipments.js --status=released

```

```

# Portal-API Database Testing

## Database Connection

### Preferred Method: db-query.js Script

```bash
# Always navigate to portal-api directory first
cd apps/portal-api

# Use db-query.js script for all database operations
node src/core-agent/testing/db-query.js [command] [parameters]
```

### Database Configuration

- **Database**: `claro_dev`
- **User**: `postgres`
- **Password**: `superuser`
- **Host**: `localhost`

### Legacy Method (Not Recommended)

```bash
# When psql must be used, always use PGPASSWORD to avoid interactive prompts
PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c "SQL_COMMAND"
```

## Key Organizations

### Organization Data

```bash
# List all organizations (preferred method)
node src/core-agent/testing/db-query.js orgs

# Demo organization (safe for testing)
node src/core-agent/testing/db-query.js sql "SELECT * FROM organization WHERE id = 3;"
```

### Organization Details

- **Organization 1**: Antek (Trading type) - Production organization
- **Organization 3**: Demo organization - Safe for testing
  - Email: `<EMAIL>`
  - Use for all testing scenarios

## Shipment Queries

### Find Test Shipments

```bash
# Find shipments for demo organization (preferred method)
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"customsStatus\", \"transactionNumber\", \"etaPort\", \"releaseDate\", \"trackingStatus\" FROM shipment WHERE \"organizationId\" = 3 ORDER BY \"createDate\" DESC LIMIT 5;"

# Find shipments by status
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"customsStatus\", \"createDate\" FROM shipment WHERE \"organizationId\" = 3 AND \"customsStatus\" = 'released' ORDER BY \"createDate\" DESC;"

# Count shipments by status
node src/core-agent/testing/db-query.js sql "SELECT \"customsStatus\", COUNT(*) as count FROM shipment WHERE \"organizationId\" = 3 GROUP BY \"customsStatus\" ORDER BY count DESC;"
```

### Shipment Fields for Testing

- **id**: Shipment identifier
- **hblNumber**: House Bill of Lading number
- **customsStatus**: live, released, pending, etc.
- **transactionNumber**: For transaction number queries
- **etaPort**: For ETA queries
- **releaseDate**: For release status queries
- **trackingStatus**: For shipping status queries

## Email Testing Data

### Email Queries

```bash
# Recent emails for organization (preferred method)
node src/core-agent/testing/db-query.js sql "SELECT id, subject, status, \"organizationId\", \"createDate\" FROM email WHERE \"organizationId\" = 3 AND \"createDate\" > NOW() - INTERVAL '1 hour' ORDER BY \"createDate\" DESC;"

# Get recent emails using helper command
node src/core-agent/testing/db-query.js recent 10

# Clean up test emails (use cleanup scripts instead)
node src/core-agent/testing/cleanup-emails-by-subject.js --pattern="E2E Test" --dry-run
```

### Test Email Addresses

- **Development**: `<EMAIL>`
- **Demo Organization**: `<EMAIL>`
- **Default Inbox**: `<EMAIL>`

## Database Helper Scripts

### Using db-query.js

```bash
# List organizations
node src/core-agent/testing/db-query.js orgs

# Find shipments with all key fields for testing (recommended)
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"customsStatus\", \"transactionNumber\", \"etaPort\", \"releaseDate\", \"trackingStatus\" FROM shipment WHERE \"organizationId\" = 3 ORDER BY \"createDate\" DESC LIMIT 5;"

# Get recent emails
node src/core-agent/testing/db-query.js recent 10

# Execute custom SQL
node src/core-agent/testing/db-query.js sql "SELECT COUNT(*) FROM shipment WHERE \"organizationId\" = 3"
```

### Using find-test-shipments.js

```bash
# Find diverse shipments for testing
node src/core-agent/testing/find-test-shipments.js --analysis

# Find shipments with specific status
node src/core-agent/testing/find-test-shipments.js --status=released
```

## Safety Practices

### Always Use Demo Organization

- Use `--org=3` for all testing
- Never test with production organization (ID 1)

### Verify Data Before Testing

```bash
# Check if demo organization exists
node src/core-agent/testing/db-query.js sql "SELECT id, name FROM organization WHERE id = 3;"

# Check if shipments exist
node src/core-agent/testing/db-query.js sql "SELECT COUNT(*) FROM shipment WHERE \"organizationId\" = 3;"
```

### Clean Up Test Data

```bash
# Use cleanup scripts with --dry-run first
node src/core-agent/testing/cleanup-emails-by-subject.js --pattern="E2E Test" --dry-run
```

## Common Database Patterns

### Shipment Discovery for Intent Testing

```bash
# Find shipments good for GET_SHIPMENT_STATUS testing
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"customsStatus\", \"transactionNumber\", \"etaPort\", \"releaseDate\" FROM shipment WHERE \"organizationId\" = 3 AND \"customsStatus\" IS NOT NULL ORDER BY \"createDate\" DESC;"

# Use the dedicated shipment discovery script (recommended)
node src/core-agent/testing/find-test-shipments.js --analysis
```

### Find Shipments by Scenario

```bash
# For ETA testing (shipments with ETA data)
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"etaPort\" FROM shipment WHERE \"organizationId\" = 3 AND \"etaPort\" IS NOT NULL;"

# For transaction number testing
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"transactionNumber\" FROM shipment WHERE \"organizationId\" = 3 AND \"transactionNumber\" IS NOT NULL;"

# For release status testing
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"customsStatus\", \"releaseDate\" FROM shipment WHERE \"organizationId\" = 3 AND \"customsStatus\" = 'released';"

# Use the dedicated shipment discovery script for specific scenarios
node src/core-agent/testing/find-test-shipments.js --status=released
```

node src/core-agent/testing/find-test-shipments.js --status=released

```

```
