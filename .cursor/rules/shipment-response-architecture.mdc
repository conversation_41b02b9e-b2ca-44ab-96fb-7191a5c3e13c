---
description: 
globs: apps/portal-api/src/core-agent/**/*.ts,apps/portal-api/src/core-agent/**/*.njk
alwaysApply: false
---
# Shipment Response Service - 3-Layer Architecture

## Overview
The shipment response service implements a 3-layer architecture for centralized response generation:

**Layer 1: Context Service** → **Layer 2: Intent Handlers** → **Layer 3: Response Service**

## Core Files

### Layer 1: Business Rules & Context
- **Main Service**: [apps/portal-api/src/core-agent/services/shipment-context.service.ts](mdc:apps/portal-api/src/core-agent/services/shipment-context.service.ts) (1,303 lines)
- **Context Types**: [apps/portal-api/src/core-agent/types/shipment-context.types.ts](mdc:apps/portal-api/src/core-agent/types/shipment-context.types.ts)

### Layer 2: Intent Processing  
- **Registry**: [apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts](mdc:apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts) (268 lines)
- **Base Handler**: [apps/portal-api/src/core-agent/handlers/base-intent-handler.ts](mdc:apps/portal-api/src/core-agent/handlers/base-intent-handler.ts) (198 lines)
- **Handler Interface**: [apps/portal-api/src/core-agent/interfaces/intent-handler.interface.ts](mdc:apps/portal-api/src/core-agent/interfaces/intent-handler.interface.ts)

### Layer 3: Response Rendering
- **Response Service**: [apps/portal-api/src/core-agent/services/shipment-response.service.ts](mdc:apps/portal-api/src/core-agent/services/shipment-response.service.ts) (241 lines)
- **Template Constants**: [apps/portal-api/src/core-agent/constants/templates.ts](mdc:apps/portal-api/src/core-agent/constants/templates.ts)

## Implementation Status

### ✅ **FULLY IMPLEMENTED**
- **Layer 1 (Context Service)**: Complete business rule evaluation (100%)
- **Layer 3 (Response Service)**: Complete fragment rendering (100%)
- **Template Library**: All 24 templates implemented
- **Testing Infrastructure**: Complete debug controllers

### 🔄 **PARTIAL IMPLEMENTATION** 
- **Layer 2 (Intent Handlers)**: 4/17 handlers implemented, **registration broken**

### ❌ **CRITICAL ISSUE**
Handler registration is broken in [apps/portal-api/src/core-agent/core-agent.module.ts](mdc:apps/portal-api/src/core-agent/core-agent.module.ts):
```typescript
{
  provide: "INTENT_HANDLERS",
  useValue: []  // ❌ Empty array - no handlers work!
}
```

## Architecture Flow

```typescript
// 1. Build context with all business rules
const context = await contextService.buildContext(shipmentId, organizationId);

// 2. Process intents → generate fragments  
const fragments = await intentHandler.handle(validatedIntent, context);

// 3. Render fragments → final response
const response = await responseService.renderFragments(fragments, context);
```

## Key Principles

1. **Single Source of Truth**: All business rule evaluation happens in ShipmentContextService
2. **Fragment-Based**: Responses are composed of reusable template fragments
3. **Priority-Based Ordering**: Templates render in predefined priority order
4. **Error Resilient**: Individual fragment failures don't break entire response
5. **Security First**: HTML escaping and context sanitization

## EMAIL_INTENTS Mapping

Available intents from [apps/portal-api/src/email/types/ai-agent.types.ts](mdc:apps/portal-api/src/email/types/ai-agent.types.ts):
- `GET_SHIPMENT_STATUS` ✅ [Implemented](mdc:apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts)
- `REQUEST_CAD_DOCUMENT` ✅ [Implemented](mdc:apps/portal-api/src/core-agent/handlers/request-cad-document.handler.ts) 
- `REQUEST_RUSH_PROCESSING` ✅ [Implemented](mdc:apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts)
- `DOCUMENTATION_COMING` ✅ [Implemented](mdc:apps/portal-api/src/core-agent/handlers/documentation-coming.handler.ts)
- 13 more intents need handlers

## Development Guidelines

### Creating New Intent Handlers
1. Extend [BaseIntentHandler](mdc:apps/portal-api/src/core-agent/handlers/base-intent-handler.ts)
2. Define `classificationMeta` with intent, description, examples
3. Implement `handle()` method returning `ResponseFragment[]`
4. Register in `INTENT_HANDLERS` provider

### Working with Templates
- Templates located in [apps/portal-api/src/core-agent/templates/](mdc:apps/portal-api/src/core-agent/templates)
- Use priority system from [TEMPLATE_ORDER](mdc:apps/portal-api/src/core-agent/constants/templates.ts)
- All templates support context-aware conditional rendering

### Testing
Use debug controllers:
- [CoreAgentTestController](mdc:apps/portal-api/src/core-agent/controllers/test/core-agent.test.controller.ts)
- [ResponseServiceTestController](mdc:apps/portal-api/src/core-agent/controllers/test/response-service.test.controller.ts)

## Next Steps Priority

1. **P0**: Fix handler registration in CoreAgentModule
2. **P1**: Implement remaining 13 intent handlers
3. **P2**: Integrate with email processors
