---
description: 
globs: 
alwaysApply: false
---
# <PERSON><PERSON><PERSON> Monorepo Overview

This is a Rush monorepo with multiple applications and libraries:

## Applications

- **backoffice**: Admin interface located in [apps/backoffice](mdc:apps/backoffice)
- **backoffice-api**: API for backoffice in [apps/backoffice-api](mdc:apps/backoffice-api)
- **portal**: Customer-facing portal in [apps/portal](mdc:apps/portal)
- **portal-api**: API for portal in [apps/portal-api](mdc:apps/portal-api)
- **bullmq-board**: Dashboard for monitoring BullMQ jobs in [apps/bullmq-board](mdc:apps/bullmq-board)
- **cloud-functions**: Cloud functions in [apps/cloud-functions](mdc:apps/cloud-functions)

## Libraries

- **nest-modules**: Shared NestJS modules in [libraries/nest-modules](mdc:libraries/nest-modules)
- **ui**: Shared UI components in [libraries/ui](mdc:libraries/ui)

## Configuration

- Rush configuration in [rush.json](mdc:rush.json)
- Common configuration in [common/config](mdc:common/config)

See [README.md](mdc:README.md) for additional project information.
