---
description: Guidelines for API authentication, Bearer token usage, and testing portal-api endpoints and controllers. Used for curl commands.
globs: 
alwaysApply: false
---
# Authentication Testing Guide

## Overview
The portal-api requires Bearer token authentication for most endpoints. All controllers use the `@UseGuards(AccessTokenGuard)` and `@ApiAccessTokenAuthenticated()` decorators to enforce authentication.

## Base URL Configuration
- **Local Development**: `http://localhost:5001`
- **Port**: 5001 (configured in [main.ts](mdc:apps/portal-api/src/main.ts))

## Authentication Flow

### 1. Login Endpoint
**Endpoint**: `POST {{base_url}}/auth/login`

**Required Headers**:
```
Content-Type: application/json
```

**Request Body**:
```json
{
  "loginMethod": "email",
  "email": "<EMAIL>", 
  "password": "password"
}
```

**Response** (LoginUserResponseDto):
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "accessTokenExpiryDate": "2024-01-01T12:00:00.000Z",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshTokenExpiryDate": "2024-01-01T12:00:00.000Z", 
  "name": "Sophie",
  "email": "<EMAIL>",
  "organization": {
    "id": 1,
    "name": "Organization Name"
  },
  "permission": "ADMIN"
}
```

### 2. Using Access Token
For all subsequent API requests, include the Bearer token:

**Required Headers**:
```
Authorization: Bearer {{accessToken}}
Content-Type: application/json
```

**Optional Headers** (for backoffice admin users):
```
claro-organization-id: {{organizationId}}
```

## Authentication Implementation Details

### Guard Structure
- **Guard Class**: [AccessTokenGuard](mdc:libraries/nest-modules/src/guards/access-token.guard.ts)
- **Auth Service**: [AuthService](mdc:libraries/nest-modules/src/auth/auth.service.ts) 
- **Controller**: [AuthController](mdc:libraries/nest-modules/src/auth/auth.controller.ts)

### Login Methods
Supported login methods (from [LoginMethod enum](mdc:libraries/nest-modules/src/types/auth.types.ts)):
- `"email"` - Email/password authentication
- `"google-sso"` - Google SSO (requires `idToken`)

### User Permissions
Available permissions (affects API access):
- `BASIC` - Standard user access
- `ADMIN` - Administrative access
- `SUPER_ADMIN` - Full system access
- `BACKOFFICE_ADMIN` - Can impersonate organizations via `claro-organization-id` header

## Testing Workflow

### Step 1: Get Authentication Token
```bash
curl -X POST "http://localhost:5001/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "loginMethod": "email",
    "email": "<EMAIL>",
    "password": "password"
  }'
```

### Step 2: Extract Access Token
Save the `accessToken` from the response for subsequent requests.

### Step 3: Test Authenticated Endpoints
```bash
curl -X GET "http://localhost:5001/shipments" \
  -H "Authorization: Bearer {{accessToken}}" \
  -H "Content-Type: application/json"
```

## Protected Controller Examples

### Core Agent Test Controllers
- [CoreAgentTestController](mdc:apps/portal-api/src/core-agent/controllers/test/core-agent.test.controller.ts)
  - `GET /test/core-agent/shipment-context/:shipmentId/:organizationId`
  - `GET /test/core-agent/intent-handler-registry`
  - `POST /test/core-agent/identify-shipment`

### Business Controllers  
- [ShipmentController](mdc:apps/portal-api/src/shipment/shipment.controller.ts) - `GET|POST /shipments`
- [LocationController](mdc:apps/portal-api/src/location/location.controller.ts) - `GET|POST /locations`
- [OgdFilingController](mdc:apps/portal-api/src/ogd-filing/ogd-filing.controller.ts) - `GET|POST /ogd-filings`

### User Management
- [UserController](mdc:libraries/nest-modules/src/user/controllers/user.controller.ts) - `GET /users`
- [OrganizationController](mdc:libraries/nest-modules/src/user/controllers/organization.controller.ts) - `GET /organizations`

## Token Management

### Refresh Token
**Endpoint**: `POST {{base_url}}/auth/token`
```json
{
  "refreshToken": "{{refreshToken}}"
}
```

### Logout
**Endpoint**: `POST {{base_url}}/auth/logout`
**Headers**: `Authorization: Bearer {{accessToken}}`
```json
{
  "refreshToken": "{{refreshToken}}"
}
```

## Security Features

### Multi-Tenancy
- All business data is scoped by `organizationId`
- User access is automatically scoped to their organization
- Backoffice admins can override organization via `claro-organization-id` header

### Request Context
The [AccessTokenGuard](mdc:libraries/nest-modules/src/guards/access-token.guard.ts) automatically:
1. Validates the Bearer token
2. Loads user information
3. Sets `request.user` with authenticated user data
4. Handles organization scoping for backoffice admins

## Error Handling
Common authentication errors:
- `401 Unauthorized` - Invalid or missing token
- `403 Forbidden` - Valid token but insufficient permissions
- `404 Not Found` - User not found

## Development Tips

### Swagger Documentation
- Local Swagger UI: `http://localhost:5001/docs`
- Includes Bearer token authentication in UI
- All protected endpoints marked with `@ApiAccessTokenAuthenticated()`

### Testing Authenticated Endpoints
1. **ALWAYS** authenticate first via `/auth/login`
2. Extract and store the `accessToken` 
3. Include `Authorization: Bearer {{accessToken}}` header
4. For testing organization-specific endpoints, note the user's `organization.id`
5. For backoffice testing, use `claro-organization-id` header

### Debug Controllers
Use test controllers for debugging without UI:
- Core Agent: `GET /test/core-agent/shipment-context/:shipmentId/:organizationId`
- Response Service: Available in [ResponseServiceTestController](mdc:apps/portal-api/src/core-agent/controllers/test/response-service.test.controller.ts)
