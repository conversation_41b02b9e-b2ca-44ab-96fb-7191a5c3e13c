---
description: Core-agent module architecture for AI-powered email processing and intent handling
globs: apps/portal-api/src/core-agent/**/* apps/portal-api/src/core-agent/* **/core-agent/**/*.ts **/core-agent/**/*.dto.ts **/core-agent/**/*.service.ts **/core-agent/**/*.processor.ts **/core-agent/**/*.listener.ts **/core-agent/**/*.controller.ts **/core-agent/**/*.handler.ts
alwaysApply: false
---

# Core Agent Module Guide

The Core Agent module is a global NestJS module that provides AI-powered agent capabilities for analyzing emails, processing user intents, identifying shipments, and generating intelligent responses. It serves as the central AI orchestration layer for email processing.

## Module Architecture

### Entry Point

- [core-agent.module.ts](mdc:apps/portal-api/src/core-agent/core-agent.module.ts) - Main module definition with global scope and comprehensive intent handler registry

### Core Services

#### Orchestration Services

- [core-agent.service.ts](mdc:apps/portal-api/src/core-agent/services/core-agent.service.ts) - Main orchestration service for email processing
- [email-intent-analysis.service.ts](mdc:apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts) - Analyzes email intents using LLM
- [answer-user-query.service.ts](mdc:apps/portal-api/src/core-agent/services/answer-user-query.service.ts) - Handles user query processing and response generation
- [shipment-identifier.service.ts](mdc:apps/portal-api/src/core-agent/services/shipment-identifier.service.ts) - Identifies shipments from various input formats using AI

#### Support Services

- [intent-handler-registry.service.ts](mdc:apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts) - Auto-discovers and manages all intent handlers
- [shipment-response.service.ts](mdc:apps/portal-api/src/core-agent/services/shipment-response.service.ts) - Generates structured responses for shipments
- [document-processor.service.ts](mdc:apps/portal-api/src/core-agent/services/document-processor.service.ts) - Processes documents and extracts data
- [shipment-field-extraction.service.ts](mdc:apps/portal-api/src/core-agent/services/shipment-field-extraction.service.ts) - Extracts specific fields from shipment data
- [agent-tools.service.ts](mdc:apps/portal-api/src/core-agent/services/agent-tools.service.ts) - Utility tools for LLM agent operations

## Intent Handler System

### Intent Handler Registry

The [IntentHandlerRegistry](mdc:apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts) provides:

- Auto-discovery of all intent handlers
- Mapping between EMAIL_INTENTS and their handlers
- Dynamic classification prompt generation
- Validation and diagnostics for handler completeness

### Intent Handler Architecture

Each intent handler extends the base handler and implements:

- **Classification Metadata**: Intent type, description, examples, and keywords
- **Processing Logic**: Business logic for handling the specific intent
- **Response Generation**: Structured responses for the intent

### Available Intent Handlers

- [get-shipment-status.handler.ts](mdc:apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts) - Handles shipment status queries
- [request-cad-document.handler.ts](mdc:apps/portal-api/src/core-agent/handlers/request-cad-document.handler.ts) - Processes CAD document requests
- [request-rush-processing.handler.ts](mdc:apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts) - Handles rush processing requests
- [documentation-coming.handler.ts](mdc:apps/portal-api/src/core-agent/handlers/documentation-coming.handler.ts) - Manages documentation notifications
- [request-rns-proof.handler.ts](mdc:apps/portal-api/src/core-agent/handlers/request-rns-proof.handler.ts) - Handles RNS proof requests
- [request-manual-processing.handler.ts](mdc:apps/portal-api/src/core-agent/handlers/request-manual-processing.handler.ts) - Processes manual processing requests
- [request-hold-shipment.handler.ts](mdc:apps/portal-api/src/core-agent/handlers/request-hold-shipment.handler.ts) - Handles shipment hold requests
- [process-document.handler.ts](mdc:apps/portal-api/src/core-agent/handlers/process-document.handler.ts) - Processes document-related intents
- [update-shipment.handler.ts](mdc:apps/portal-api/src/core-agent/handlers/update-shipment.handler.ts) - Handles shipment updates
- [acknowledge-documents.handler.ts](mdc:apps/portal-api/src/core-agent/handlers/acknowledge-documents.handler.ts) - Acknowledges document receipt
- [acknowledge-missing-documents.handler.ts](mdc:apps/portal-api/src/core-agent/handlers/acknowledge-missing-documents.handler.ts) - Handles missing document acknowledgments

### Base Intent Handler

The [base-intent-handler.ts](mdc:apps/portal-api/src/core-agent/handlers/base-intent-handler.ts) provides:

- Common patterns for intent processing
- Shipment context building
- Response template management
- Error handling utilities

## Queue-Based Processing

### Queue Architecture

Core-agent uses BullMQ for orchestrating email processing:

#### Queue Types

- **IDENTIFY_SHIPMENT** - Identifies shipments from email content
- **HANDLE_REQUEST_MESSAGE** - Processes user intents and generates responses
- **EVENT_EMITTER** - Handles event emission for downstream processing

### Processor Implementation

#### Identify Shipment Processor

[identify-shipment.processor.ts](mdc:apps/portal-api/src/core-agent/processors/identify-shipment.processor.ts) handles:

- Email content gathering and analysis
- Shipment identification using AI/LLM
- Multiple shipment scenario handling
- Event emission for next processing stage

#### Handle Request Message Processor

[handle-request-message.processor.ts](mdc:apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts) handles:

- Complete email intent processing pipeline
- Intent extraction and classification
- Intent handler execution
- Response generation and email sending

#### Event Emitter Processor

[event-emitter.processor.ts](mdc:apps/portal-api/src/core-agent/processors/event-emitter.processor.ts) handles:

- Event emission for external systems
- Status updates and notifications
- Integration with other modules

## Event-Driven Architecture

### Email Saga Listener

[email-saga.listener.ts](mdc:apps/portal-api/src/core-agent/listeners/email-saga.listener.ts) implements:

- Saga pattern for email processing
- Pessimistic locking for state management
- Email status transitions
- Integration with aggregation completion events

### Core-Agent Events

[email-processing.events.ts](mdc:apps/portal-api/src/core-agent/events/email-processing.events.ts) defines:

- **CoreAgentProcessingStartedEvent** - Processing initiation
- **CoreAgentProcessingCompletedEvent** - Successful completion
- **CoreAgentProcessingFailedEvent** - Processing failures
- **CoreAgentStatusUpdateEvent** - Status updates

### Event Flow

```
Email Module → EMAIL_SAVED → Core-Agent Saga → Status Transition → Queue Processing → Handler Execution → Response Generation
```

## Template System

### Template Architecture

Templates are managed through the TemplateManagerModule and stored in the `templates/` directory:

- Nunjucks-based template rendering
- Context-aware template selection
- Dynamic content generation
- Multi-format support (HTML/text)

### Template Usage

Templates are used by:

- Intent handlers for response generation
- Shipment response service for status updates
- Document processor for formatted output
- Email composition for final responses

## Schema Definitions

### Core Schemas

- [message-content.schema.ts](mdc:apps/portal-api/src/core-agent/schemas/message-content.schema.ts) - Email content structure
- [identify-shipment.schema.ts](mdc:apps/portal-api/src/core-agent/schemas/identify-shipment.schema.ts) - Shipment identification results
- [lookup-shipment-db.schema.ts](mdc:apps/portal-api/src/core-agent/schemas/lookup-shipment-db.schema.ts) - Database lookup schemas

### Integration Schemas

- Integration with email module schemas for validated intents
- LLM response schemas for structured outputs
- Event schemas for inter-module communication

## Configuration & Constants

### LLM Configuration

[llm.constants.ts](mdc:apps/portal-api/src/core-agent/constants/llm.constants.ts) defines:

- Model selection for different operations
- Default model: `gpt-4.1-mini` for performance-critical operations
- Configuration for various AI processing tasks

### Queue Configuration

[queue.types.ts](mdc:apps/portal-api/src/core-agent/types/queue.types.ts) defines:

- Queue names and configuration
- Job data interfaces
- Flow producer configurations
- Default job options and worker settings

## Integration Patterns

### Email Module Integration

- Listens for EMAIL_SAVED events
- Processes emails through AI pipeline
- Updates email status throughout processing
- Generates and sends responses

### Shipment Module Integration

- Queries shipment data for context
- Updates shipment status based on intents
- Generates shipment-specific responses
- Handles shipment-related operations

### Document Module Integration

- Processes email attachments
- Extracts document data
- Handles document-related intents
- Manages document workflows

### Aggregation Module Integration

- Receives aggregation completion events
- Processes aggregated data
- Incorporates aggregation results into responses
- Handles post-aggregation workflows

## Development Patterns

### Adding New Intent Handlers

1. Create handler class extending base handler
2. Define classification metadata with examples
3. Implement processing logic
4. Add to module providers
5. Register in intent handler registry

### Service Extension

- Follow NestJS dependency injection patterns
- Use request scoping for multi-tenancy
- Implement proper error handling
- Add comprehensive logging

### Testing Strategy

- Use test controllers for development
- Mock external dependencies
- Test intent classification accuracy
- Validate response generation
- Test error scenarios

### Error Handling

- Structured error responses
- Proper exception propagation
- Comprehensive logging
- Graceful degradation

## Performance Considerations

### Queue Management

- Concurrency control for LLM calls
- Efficient job processing
- Resource cleanup
- Dead letter queue handling

### LLM Usage Optimization

- Model selection based on task complexity
- Prompt optimization for accuracy
- Response caching where appropriate
- Rate limiting and quota management

### Memory Management

- Proper context cleanup
- Efficient data structures
- Resource pooling
- Garbage collection optimization

## Security Considerations

### Multi-tenancy

- Organization-scoped processing
- Secure context switching
- Data isolation
- Access control validation

### Data Protection

- Secure handling of email content
- PII protection in logging
- Encrypted data transmission
- Audit trail maintenance

### AI Security

- Prompt injection protection
- Output validation
- Model access control
- Response sanitization

## Monitoring and Observability

### Logging Strategy

- Structured logging with context
- Performance metrics tracking
- Error rate monitoring
- Intent classification accuracy tracking

### Key Metrics

- Email processing throughput
- Intent classification accuracy
- Response generation time
- Queue depth and processing lag
- Error rates by intent type

### Diagnostics

- Intent handler registry validation
- Queue health monitoring
- LLM usage tracking
- Performance bottleneck identification

## Future Extensibility

### Plugin Architecture

- Intent handlers as plugins
- Extensible processing pipeline
- Configurable workflows
- Dynamic handler registration

### AI Model Evolution

- Model versioning support
- A/B testing capabilities
- Performance comparison tools
- Graceful model transitions

### Integration Expansion

- Additional module integrations
- External service connections
- Webhook support
- API extensibility
