---
description: Accessing portal-api CloudWatch logs via MCP: how to find the correct (dynamic) log group and log, view configuration, and use common query patterns.
globs: 
alwaysApply: false
---
# CloudWatch Logs Configuration for Portal-API

## Default Log Group and Discovery

When "CloudWatch logs" or "the logs" are mentioned, this refers to the **portal-api** logs.

**Primary Log Group:** `ClaroInfrastructureStack-PortalApiServiceTaskDefwebLogGroup603D6AA7-A8hmDo5Mgulz`

**How to Find the Current Log:**
1. List logs in the group
2. Identify the latest active one (check `creationTime` and log activity).
3. Use that as the primary log for subsequent operations.

## Guiding Principles for Log Analysis

These principles are critical for efficiently analyzing large log volumes without overwhelming the context.

- **Start Broad, Then Narrow**: First, get a high-level overview with `summarize_log_activity`. Then, look for specific issues with `find_error_patterns`. Only after that, dive into targeted searches with `search_logs`.
- **Use The Right Tool for the Job**:
    - 🔥 **Troubleshooting/Issues** → `find_error_patterns` (NOT `search_logs` with error filters).
    - 🔥 **Understanding Activity** → `summarize_log_activity` (NOT broad `search_logs`).
    - 🔥 **Cross-Service Tracing** → `correlate_logs` or `search_logs_multi`.
    - ⚠️ **Simple Pattern Matching** → `filter_log_events`.
    - 📊 **Complex Queries** → `search_logs` (as a last resort, be very specific).
- **Parallelize Queries**: Always run independent operations in parallel. For example, run `summarize_log_activity()` and `find_error_patterns()` at the same time. Use `search_logs_multi()` instead of multiple sequential `search_logs()` calls.
- **Use Optimized Time Windows**:
    - **Recent Issues**: Start with 1-2 hours.
    - **Investigation**: Expand to 6-12 hours if needed.
    - **Trend Analysis**: Only use 24+ hours for high-level patterns.
    - **Default**: Start with a 2-6 hour window, not 12 or 24.
- **Write Efficient Queries**:
    - **❌ AVOID**: Generic, broad searches like `filter @message like /email|Email|CAD|demo|Demo/`.
    - **✅ PREFER**: Targeted tools like `find_error_patterns()`, `correlate_logs(search_term="specific_id")`, or precise filters like `filter @message like /specific_event_name/`.

## Tool Reference

- `summarize_log_activity`: **Always start here for large logs.** Use it to get an overview of volume and activity patterns. Default time window: 6-12 hours.
- `find_error_patterns`: Best for troubleshooting. Identifies common errors and failure patterns more efficiently than manual searching.
- `correlate_logs`: Use for tracing requests across services using a common ID (e.g., transaction ID, user ID).
- `search_logs_multi`: Use when you need to search multiple log groups at once for cross-service investigations.
- `filter_log_events`: Best for simple, fast pattern matching. This is an excellent starting point for targeted searches on a specific module or class. Use flexible filter patterns (e.g., `"[ClassName]" "entity" "1234"`).
- `search_logs`: Use sparingly for complex CloudWatch Insights queries when other tools are insufficient. It is expensive on large logs, so use targeted time windows.

## Standard Workflows

### General Troubleshooting Workflow
1. Run `summarize_log_activity(hours=6)` and `find_error_patterns(hours=6)` in parallel.
2. If specific IDs are found, use `correlate_logs(search_term="<ID>")`.
3. If the issue may span services, use `search_logs_multi([portal-api, ...])`.
4. Only as a final step, use `search_logs` with a very targeted query for specific details.

### NestJS Debugging Workflow
This workflow is for debugging issues within the NestJS backend.

**1. Identify the Entry Point**
- Start in the code to identify the primary NestJS module, processor, or service for the feature (e.g., `AggregateEmailProcessor`).

**2. Targeted Log Filtering**
- Use `filter_log_events` to search for logs from that specific class. NestJS logs are helpfully prefixed with the class name (e.g., `[AggregateEmailProcessor]`).
- **Use flexible filter patterns.** A search for `"[ClassName]" "entity" "1234"` is more robust than `"ClassName processed entity ID 1234"`.

**3. Analyze the Results**
- **If you find errors:** You've likely found the source of the problem.
- **If the process completes successfully:** The issue is likely not a technical failure but a business logic condition or a problem in a *downstream* process. Proceed to the next step.
- **If the process starts but logs stop:** Your filter might be too specific, or the process may be crashing silently.

**4. Trace Control Flow for Logic Issues**
- **When to use**: When targeted searches show a component is executing without errors, but the overall feature is failing. This points to issues in business logic or inter-component communication.
- **How to do it**: Follow the execution path from one logged component to the next. Look for logs indicating decisions (e.g., `'Intent classified as UNKNOWN'`), emitted events, or calls to other services.
- **Example from CAD failure**: The `AggregateEmailProcessor` ran successfully, but no CAD document was generated. Tracing logs revealed that the upstream `ProcessUserIntentsProcessor` had classified the user's intent as `UNKNOWN`, so it never triggered the `EMAIL_AGGREGATED` event required by the downstream processor. The problem wasn't an *error*, but an unexpected *logic path*.

**5. Broaden the Search if Necessary**
- If targeted searches don't reveal the root cause, switch to `search_logs` on the *entire log stream* (`@logStream`) where the job was running. This provides a complete chronological view of all activity in the container.

**6. Example: Debugging the CAD Generation Failure**
```
// 1. Start with the code to identify the main module (e.g., AggregateEmailProcessor).
// 2. Run a targeted, flexible filter to see if it's running correctly.
filter_log_events(
  log_group_name: "...", // Use current portal-api log group
  pattern: `"[AggregateEmailProcessor]" "email" "3162"`, // Use flexible pattern
  hours: 6
)

// 3. If the module runs but the feature still fails, the problem is likely upstream or downstream.
//    Search the entire log stream from that time to see what happened before and after.
search_logs(
  log_group_name: "...",
  query: `fields @timestamp, @message | filter @logStream = "the_log_stream_from_step_2"`,
  start_time: "...",
  end_time: "..."
)

// 4. In parallel, check for any unexpected errors.
find_error_patterns(log_group_name: "...", hours=6)
```

## Advanced Techniques

### CloudWatch Logs Insights Query Techniques
- Use `fields` to select only key columns: `fields @timestamp, @logStream, @requestId`
- Truncate long messages: `fields @timestamp, substr(@message, 0, 100) as preview`
- Use `stats` for aggregation: `stats count() by bin(5m), @logStream`
- Combine filters for precision: `filter @message like /ERROR/ and @message like /timeout/`

### Pagination Strategy
1. Start with a very small `limit` (10-20).
2. Review the structure of the results.
3. Refine your query to be more selective.
4. Gradually increase the limit only if necessary.

## Other Information

### Other Available Log Groups
- **Backoffice API:** `ClaroInfrastructureStack-BackofficeApiServiceTaskDefwebLogGroup25CBF0A5-*`
- **Tika Service:** `ClaroInfrastructureStack-TikaServiceTaskDefwebLogGroupF0DDBEE5-*`

### Default Behavior Summary
- **Always start with** `summarize_log_activity`.
- **Use specialized tools** before falling back to `search_logs`.
- **Run operations in parallel** whenever possible.
- **Start with 2-6 hour windows** and expand only if needed.
- **Default to the portal-api log group** unless specified otherwise.
- **Auto-discover the current log group** if the primary one fails.
