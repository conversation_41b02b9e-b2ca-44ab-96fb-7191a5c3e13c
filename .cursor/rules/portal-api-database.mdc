---
description: 
globs: apps/portal-api/**/*.ts,libraries/nest-modules/src/entities/**/*.ts,libraries/nest-modules/src/migrations/**/*.ts,libraries/nest-modules/src/dto/**/*.ts,apps/portal-api/src/**/*.service.ts,apps/portal-api/src/**/*.controller.ts,apps/portal-api/src/**/*.dto.ts
alwaysApply: false
---
# Portal-API Database Structure

## Overview
The portal-api uses PostgreSQL as its database with TypeORM as the ORM. All database entities are defined in the shared nest-modules library and follow a consistent architectural pattern.

## Database Configuration
- **Database Type**: PostgreSQL
- **ORM**: TypeORM
- **Configuration**: [data-source.ts](mdc:libraries/nest-modules/src/data-source.ts)
- **Connection**: Uses environment variables (DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD)
- **SSL**: Enabled for non-local environments

## Entity Architecture

### Base Entity Pattern
All entities extend from [SimplifiedBaseEntity](mdc:libraries/nest-modules/src/entities/base.entity.ts) which provides:
- `id`: Primary key (auto-generated integer)
- `createDate`: Timestamp with timezone for creation
- `lastEditDate`: Timestamp with timezone for last update

### Entity Location
All database entities are located in [libraries/nest-modules/src/entities/](mdc:libraries/nest-modules/src/entities) and exported through [index.ts](mdc:libraries/nest-modules/src/entities/index.ts).

## Database Table Schemas

### Core Tables

#### `organization` - Root tenant entity
```sql
CREATE TABLE organization (
  id SERIAL PRIMARY KEY,
  name VARCHAR NOT NULL,
  organizationType organization_type DEFAULT 'TRADING',
  skipPoaCheck BOOLEAN DEFAULT false,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  createdById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  lastEditedById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  CONSTRAINT UNIQUE_ORG_NAME UNIQUE (name)
);
```

#### `user` - User accounts and authentication
```sql
CREATE TABLE "user" (
  id SERIAL PRIMARY KEY,
  email VARCHAR NOT NULL,
  name VARCHAR NOT NULL,
  googleUserId VARCHAR NULL,
  permission user_permission DEFAULT 'BASIC',
  organizationId INTEGER NOT NULL REFERENCES organization(id) ON DELETE CASCADE,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  createdById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  lastEditedById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  CONSTRAINT UNIQUE_USER_ORGANIZATION_EMAIL UNIQUE (email, organizationId)
);
```

#### `shipment` - Core shipment tracking
```sql
CREATE TABLE shipment (
  id SERIAL PRIMARY KEY,
  requiresReupload BOOLEAN DEFAULT false,
  status shipment_status DEFAULT 'NEW',
  trackingStatus tracking_status DEFAULT 'OFFLINE',
  modeOfTransport shipment_mode NULL,
  etd TIMESTAMP WITH TIME ZONE NULL,
  etaPort TIMESTAMP WITH TIME ZONE NULL,
  etaPortString VARCHAR NULL,
  etaDestination TIMESTAMP WITH TIME ZONE NULL,
  etaDestinationString VARCHAR NULL,
  mblNumber VARCHAR NULL,
  hblNumber VARCHAR NULL,
  cargoControlNumber VARCHAR NULL,
  carrierCode VARCHAR NULL,
  containerNumber VARCHAR NULL,          -- deprecated
  containerType container_type NULL,     -- deprecated
  volume REAL NULL,
  volumeUOM volume_uom NULL,
  weight REAL NULL,
  weightUOM weight_uom NULL,
  quantity INTEGER NULL,
  quantityUOM quantity_uom NULL,
  pickupLfd TIMESTAMP WITH TIME ZONE NULL,
  pickupLfdString VARCHAR NULL,
  pickupDate TIMESTAMP WITH TIME ZONE NULL,
  pickupDateString VARCHAR NULL,
  pickupNumber VARCHAR NULL,
  returnLfd TIMESTAMP WITH TIME ZONE NULL,
  returnLfdString VARCHAR NULL,
  returnDate TIMESTAMP WITH TIME ZONE NULL,
  returnDateString VARCHAR NULL,
  vessel VARCHAR NULL,
  voyageNumber VARCHAR NULL,
  customsStatus customs_status DEFAULT 'PENDING_COMMERCIAL_INVOICE',
  transactionNumber VARCHAR NULL,
  customsFileNumber VARCHAR NULL,
  releaseDate TIMESTAMP WITH TIME ZONE NULL,
  adviceNoteDate TIMESTAMP WITH TIME ZONE NULL,
  portCode VARCHAR NULL,
  portOfExit VARCHAR NULL,
  subLocation VARCHAR NULL,
  organizationId INTEGER NOT NULL REFERENCES organization(id) ON DELETE CASCADE,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  -- Foreign Key References
  portOfDischargeId INTEGER REFERENCES location(id) ON DELETE SET NULL,
  portOfLoadingId INTEGER REFERENCES location(id) ON DELETE SET NULL,
  placeOfDeliveryId INTEGER REFERENCES location(id) ON DELETE SET NULL,
  carrierId INTEGER REFERENCES trade_partner(id) ON DELETE SET NULL,
  manufacturerId INTEGER REFERENCES trade_partner(id) ON DELETE SET NULL,
  shipperId INTEGER REFERENCES trade_partner(id) ON DELETE SET NULL,
  importerId INTEGER REFERENCES importer(id) ON DELETE SET NULL,
  consigneeId INTEGER REFERENCES trade_partner(id) ON DELETE SET NULL,
  forwarderId INTEGER REFERENCES trade_partner(id) ON DELETE SET NULL,
  truckerId INTEGER REFERENCES trade_partner(id) ON DELETE SET NULL,
  pickupLocationId INTEGER REFERENCES trade_partner(id) ON DELETE SET NULL,
  createdById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  lastEditedById INTEGER REFERENCES "user"(id) ON DELETE SET NULL
);
CREATE INDEX IDX_SHIPMENT_ORG_ID ON shipment(organizationId);
```

#### `container` - Container tracking per shipment
```sql
CREATE TABLE container (
  id SERIAL PRIMARY KEY,
  containerNumber VARCHAR NOT NULL,
  containerType container_type NOT NULL,
  trackingStatus tracking_status DEFAULT 'OFFLINE',
  shipmentId INTEGER NOT NULL REFERENCES shipment(id) ON DELETE CASCADE,
  organizationId INTEGER NOT NULL REFERENCES organization(id) ON DELETE CASCADE,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  createdById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  lastEditedById INTEGER REFERENCES "user"(id) ON DELETE SET NULL
);
CREATE INDEX IDX_CONTAINER_SHIPMENT_ID ON container(shipmentId);
```

#### `commercial_invoice` - Invoice headers
```sql
CREATE TABLE commercial_invoice (
  id SERIAL PRIMARY KEY,
  candataId INTEGER NULL,
  invoiceNumber VARCHAR NOT NULL,
  currency currency DEFAULT 'CAD',
  invoiceDate TIMESTAMP WITH TIME ZONE NULL,
  poNumber VARCHAR NULL,
  grossWeight REAL DEFAULT 0,
  weightUOM weight_uom DEFAULT 'KILOGRAM',
  numberOfPackages INTEGER DEFAULT 0,
  packageUOM package_uom DEFAULT 'BOX',
  includedTransCost REAL NULL,
  includedPackCost REAL NULL,
  includedMiscCost REAL NULL,
  excludedTransCost REAL NULL,
  excludedPackCost REAL NULL,
  excludedMiscCost REAL NULL,
  valueIncludesDuty BOOLEAN NULL,
  additionalInfo TEXT NULL,
  shipmentId INTEGER NOT NULL REFERENCES shipment(id) ON DELETE NO ACTION,
  countryOfExportId INTEGER REFERENCES country(id) ON DELETE RESTRICT,
  stateOfExportId INTEGER REFERENCES state(id) ON DELETE SET NULL,
  exporterId INTEGER REFERENCES trade_partner(id) ON DELETE SET NULL,
  purchaserId INTEGER REFERENCES trade_partner(id) ON DELETE SET NULL,
  shipToId INTEGER REFERENCES trade_partner(id) ON DELETE SET NULL,
  vendorId INTEGER NOT NULL REFERENCES trade_partner(id) ON DELETE RESTRICT,
  manufacturerId INTEGER REFERENCES trade_partner(id) ON DELETE SET NULL,
  organizationId INTEGER NOT NULL REFERENCES organization(id) ON DELETE CASCADE,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  createdById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  lastEditedById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  CONSTRAINT UNIQUE_CI_INVOICE_NUMBER UNIQUE (organizationId, invoiceNumber)
);
CREATE INDEX IDX_CI_SHIPMENT_ID ON commercial_invoice(shipmentId);
```

#### `commercial_invoice_line` - Invoice line items
```sql
CREATE TABLE commercial_invoice_line (
  id SERIAL PRIMARY KEY,
  candataId INTEGER NULL,
  sequence INTEGER DEFAULT 1,
  lineNumber VARCHAR NULL,
  description TEXT NULL,
  productDescription VARCHAR NULL,
  quantity REAL DEFAULT 0,
  quantityUOM quantity_uom DEFAULT 'UNIT',
  unitPrice REAL DEFAULT 0,
  totalValue REAL DEFAULT 0,
  countryOfOriginId INTEGER REFERENCES country(id) ON DELETE SET NULL,
  hsCode VARCHAR NULL,
  partNumber VARCHAR NULL,
  skuNumber VARCHAR NULL,
  weight REAL NULL,
  weightUOM weight_uom NULL,
  volume REAL NULL,
  volumeUOM volume_uom NULL,
  commercialInvoiceId INTEGER NOT NULL REFERENCES commercial_invoice(id) ON DELETE CASCADE,
  productId INTEGER REFERENCES product(id) ON DELETE SET NULL,
  organizationId INTEGER NOT NULL REFERENCES organization(id) ON DELETE CASCADE,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  createdById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  lastEditedById INTEGER REFERENCES "user"(id) ON DELETE SET NULL
);
CREATE INDEX IDX_CIL_CI_ID ON commercial_invoice_line(commercialInvoiceId);
CREATE INDEX IDX_CIL_ORG_ID ON commercial_invoice_line(organizationId);
```

### Trade and Logistics Tables

#### `trade_partner` - Companies in supply chain
```sql
CREATE TABLE trade_partner (
  id SERIAL PRIMARY KEY,
  name VARCHAR NOT NULL,
  taxId VARCHAR NULL,
  email VARCHAR NULL,
  phone VARCHAR NULL,
  address TEXT NULL,
  city VARCHAR NULL,
  postalCode VARCHAR NULL,
  countryId INTEGER REFERENCES country(id) ON DELETE SET NULL,
  stateId INTEGER REFERENCES state(id) ON DELETE SET NULL,
  organizationId INTEGER NOT NULL REFERENCES organization(id) ON DELETE CASCADE,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  createdById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  lastEditedById INTEGER REFERENCES "user"(id) ON DELETE SET NULL
);
CREATE INDEX IDX_TP_ORG_ID ON trade_partner(organizationId);
```

#### `importer` - Import license holders
```sql
CREATE TABLE importer (
  id SERIAL PRIMARY KEY,
  name VARCHAR NOT NULL,
  businessNumber VARCHAR NULL,
  email VARCHAR NULL,
  phone VARCHAR NULL,
  address TEXT NULL,
  city VARCHAR NULL,
  postalCode VARCHAR NULL,
  countryId INTEGER REFERENCES country(id) ON DELETE SET NULL,
  stateId INTEGER REFERENCES state(id) ON DELETE SET NULL,
  organizationId INTEGER NOT NULL REFERENCES organization(id) ON DELETE CASCADE,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  createdById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  lastEditedById INTEGER REFERENCES "user"(id) ON DELETE SET NULL
);
```

#### `location` - Ports and delivery locations
```sql
CREATE TABLE location (
  id SERIAL PRIMARY KEY,
  name VARCHAR NOT NULL,
  code VARCHAR NULL,
  city VARCHAR NULL,
  countryId INTEGER REFERENCES country(id) ON DELETE SET NULL,
  stateId INTEGER REFERENCES state(id) ON DELETE SET NULL,
  organizationId INTEGER NOT NULL REFERENCES organization(id) ON DELETE CASCADE,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  createdById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  lastEditedById INTEGER REFERENCES "user"(id) ON DELETE SET NULL
);
```

### Document Management Tables

#### `document` - Document metadata and OCR results
```sql
CREATE TABLE document (
  id SERIAL PRIMARY KEY,
  name VARCHAR NOT NULL,
  extractedText TEXT NULL,
  ocrData JSONB NULL,
  processingStatus document_processing_status DEFAULT 'PENDING',
  documentTypeId INTEGER REFERENCES document_type(id) ON DELETE SET NULL,
  shipmentId INTEGER REFERENCES shipment(id) ON DELETE SET NULL,
  organizationId INTEGER NOT NULL REFERENCES organization(id) ON DELETE CASCADE,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  createdById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  lastEditedById INTEGER REFERENCES "user"(id) ON DELETE SET NULL
);
```

#### `file` - File storage references
```sql
CREATE TABLE file (
  id SERIAL PRIMARY KEY,
  name VARCHAR NOT NULL,
  mimeType VARCHAR NOT NULL,
  size INTEGER NOT NULL,
  path VARCHAR NOT NULL,
  checksum VARCHAR NULL,
  shipmentId INTEGER REFERENCES shipment(id) ON DELETE SET NULL,
  organizationId INTEGER NOT NULL REFERENCES organization(id) ON DELETE CASCADE,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  createdById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  lastEditedById INTEGER REFERENCES "user"(id) ON DELETE SET NULL
);
```

### Compliance and Filing Tables

#### `ogd_filing` - Canadian OGD filings
```sql
CREATE TABLE ogd_filing (
  id SERIAL PRIMARY KEY,
  filingType ogd_filing_type NOT NULL,
  status ogd_filing_status DEFAULT 'DRAFT',
  filingData JSONB NULL,
  submissionDate TIMESTAMP WITH TIME ZONE NULL,
  responseData JSONB NULL,
  organizationId INTEGER NOT NULL REFERENCES organization(id) ON DELETE CASCADE,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  createdById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  lastEditedById INTEGER REFERENCES "user"(id) ON DELETE SET NULL
);
```

#### `sima_filing` - SIMA compliance filings
```sql
CREATE TABLE sima_filing (
  id SERIAL PRIMARY KEY,
  filingType sima_filing_type NOT NULL,
  status sima_filing_status DEFAULT 'DRAFT',
  filingData JSONB NULL,
  submissionDate TIMESTAMP WITH TIME ZONE NULL,
  organizationId INTEGER NOT NULL REFERENCES organization(id) ON DELETE CASCADE,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Canadian Trade Reference Tables

#### `canada_tariff` - Canadian tariff codes
```sql
CREATE TABLE canada_tariff (
  id SERIAL PRIMARY KEY,
  tariffCode VARCHAR NOT NULL,
  description TEXT NOT NULL,
  unit VARCHAR NULL,
  specialUnit VARCHAR NULL,
  mfnRate VARCHAR NULL,
  applicablePreferentialTariffs JSONB NULL,
  supplementaryUnits VARCHAR NULL,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  createdById INTEGER REFERENCES "user"(id) ON DELETE SET NULL,
  lastEditedById INTEGER REFERENCES "user"(id) ON DELETE SET NULL
);
CREATE INDEX IDX_CANADA_TARIFF_CODE ON canada_tariff(tariffCode);
```

#### `canada_treatment_code` - Trade treatment codes
```sql
CREATE TABLE canada_treatment_code (
  id SERIAL PRIMARY KEY,
  code VARCHAR NOT NULL UNIQUE,
  description TEXT NOT NULL,
  rate VARCHAR NULL,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Email System Tables

#### `email` - Email tracking and processing
```sql
CREATE TABLE email (
  id SERIAL PRIMARY KEY,
  messageId VARCHAR UNIQUE,
  threadId VARCHAR NULL,
  subject VARCHAR NULL,
  fromAddress VARCHAR NOT NULL,
  toAddresses TEXT[] NULL,
  ccAddresses TEXT[] NULL,
  bccAddresses TEXT[] NULL,
  body TEXT NULL,
  htmlBody TEXT NULL,
  receivedDate TIMESTAMP WITH TIME ZONE NULL,
  status email_status DEFAULT 'UNPROCESSED',
  organizationId INTEGER NOT NULL REFERENCES organization(id) ON DELETE CASCADE,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  lastEditDate TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
CREATE INDEX IDX_EMAIL_ORG_ID ON email(organizationId);
CREATE INDEX IDX_EMAIL_MESSAGE_ID ON email(messageId);
```

### System and Tracking Tables

#### `tracking_history` - Audit trail
```sql
CREATE TABLE tracking_history (
  id SERIAL PRIMARY KEY,
  entityType VARCHAR NOT NULL,
  entityId INTEGER NOT NULL,
  action VARCHAR NOT NULL,
  oldValue JSONB NULL,
  newValue JSONB NULL,
  organizationId INTEGER NOT NULL REFERENCES organization(id) ON DELETE CASCADE,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  userId INTEGER REFERENCES "user"(id) ON DELETE SET NULL
);
CREATE INDEX IDX_TH_ENTITY ON tracking_history(entityType, entityId);
CREATE INDEX IDX_TH_ORG_ID ON tracking_history(organizationId);
```

#### `llm_log` - LLM interaction logging
```sql
CREATE TABLE llm_log (
  id SERIAL PRIMARY KEY,
  model VARCHAR NOT NULL,
  prompt TEXT NOT NULL,
  response TEXT NOT NULL,
  tokens INTEGER NULL,
  cost DECIMAL(10,6) NULL,
  createDate TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Key Database Enums

### Shipment Related
```sql
CREATE TYPE shipment_status AS ENUM (
  'NEW', 'IN_TRANSIT', 'ARRIVED', 'CLEARED', 'DELIVERED', 'CANCELLED'
);

CREATE TYPE tracking_status AS ENUM (
  'OFFLINE', 'DEPARTED', 'IN_TRANSIT', 'ARRIVED', 'AVAILABLE_FOR_PICKUP', 'DELIVERED'
);

CREATE TYPE customs_status AS ENUM (
  'PENDING_COMMERCIAL_INVOICE', 'PENDING_DOCUMENTS', 'SUBMITTED', 'RELEASED', 'EXAMINED'
);

CREATE TYPE shipment_mode AS ENUM (
  'OCEAN', 'AIR', 'TRUCK', 'RAIL', 'COURIER'
);

CREATE TYPE container_type AS ENUM (
  'DRY_20', 'DRY_40', 'DRY_40_HC', 'REEFER_20', 'REEFER_40', 'OPEN_TOP', 'FLAT_RACK'
);
```

### Currency and Units
```sql
CREATE TYPE currency AS ENUM ('CAD', 'USD', 'EUR', 'GBP', 'JPY', 'CNY');

CREATE TYPE weight_uom AS ENUM ('KILOGRAM', 'POUND', 'GRAM', 'OUNCE', 'TON');

CREATE TYPE volume_uom AS ENUM ('CUBIC_METER', 'CUBIC_FOOT', 'LITER', 'GALLON');

CREATE TYPE quantity_uom AS ENUM ('UNIT', 'PIECE', 'SET', 'PAIR', 'DOZEN', 'CASE');

CREATE TYPE package_uom AS ENUM ('BOX', 'PALLET', 'CRATE', 'BAG', 'BUNDLE', 'CONTAINER');
```

### User and Organization
```sql
CREATE TYPE user_permission AS ENUM ('BASIC', 'ADMIN', 'SUPER_ADMIN');

CREATE TYPE organization_type AS ENUM ('TRADING', 'LOGISTICS', 'CUSTOMS_BROKER');
```

## Core Business Entities

### Organization Structure
- **[Organization](mdc:libraries/nest-modules/src/entities/organization.entity.ts)**: Root tenant entity
  - Multi-tenant architecture with organization-based scoping
  - Contains `organizationType` enum (TRADING, etc.)
  - Has `skipPoaCheck` flag for compliance
  - One-to-many relationships with all major business entities

### User Management
- **[User](mdc:libraries/nest-modules/src/entities/user.entity.ts)**: User accounts and authentication
- **[Password](mdc:libraries/nest-modules/src/entities/password.entity.ts)**: Password storage
- **[RefreshToken](mdc:libraries/nest-modules/src/entities/refresh-token.entity.ts)**: JWT refresh tokens
- **[ResetPasswordToken](mdc:libraries/nest-modules/src/entities/reset-password-token.entity.ts)**: Password reset tokens

### Trade and Logistics Entities
- **[Shipment](mdc:libraries/nest-modules/src/entities/shipment.entity.ts)**: Core shipment tracking entity
  - Status tracking (`ShipmentStatus`, `TrackingStatus`, `CustomsStatus`)
  - Transport details (mode, dates, numbers)
  - Container relationships
  - Trade partner relationships (carrier, shipper, consignee, etc.)
  
- **[Container](mdc:libraries/nest-modules/src/entities/container.entity.ts)**: Container tracking per shipment
- **[TradePartner](mdc:libraries/nest-modules/src/entities/trade-partner.entity.ts)**: Companies in the supply chain
- **[Importer](mdc:libraries/nest-modules/src/entities/importer.entity.ts)**: Import license holders
- **[Location](mdc:libraries/nest-modules/src/entities/location.entity.ts)**: Ports and delivery locations

### Commercial Invoice System
- **[CommercialInvoice](mdc:libraries/nest-modules/src/entities/commercial-invoice.entity.ts)**: Invoice headers
- **[CommercialInvoiceLine](mdc:libraries/nest-modules/src/entities/commercial-invoice-line.entity.ts)**: Individual line items
- **[CommercialInvoiceLineMeasurement](mdc:libraries/nest-modules/src/entities/commercial-invoice-line-measurement.entity.ts)**: Measurement data
- **[Product](mdc:libraries/nest-modules/src/entities/product.entity.ts)**: Product catalog

### Document Management
- **[Document](mdc:libraries/nest-modules/src/entities/document.entity.ts)**: Document metadata and OCR results
- **[File](mdc:libraries/nest-modules/src/entities/file.entity.ts)**: File storage references
- **[FileBatch](mdc:libraries/nest-modules/src/entities/file-batch.entity.ts)**: Batch upload tracking
- **[DocumentType](mdc:libraries/nest-modules/src/entities/document-type.entity.ts)**: Document classification
- **[DocumentField](mdc:libraries/nest-modules/src/entities/document-field.entity.ts)**: Extracted field data

### Document Processing Pipeline
- **[DocumentAggregation](mdc:libraries/nest-modules/src/entities/document-aggregation.entity.ts)**: Document processing workflows
- **[DocumentAggregationStep](mdc:libraries/nest-modules/src/entities/document-aggregation-step.entity.ts)**: Processing step tracking
- **[DocumentAggregationTradePartner](mdc:libraries/nest-modules/src/entities/document-aggregation-trade-partner.entity.ts)**: Trade partner linkage

### Compliance and Filing
- **[OgdFiling](mdc:libraries/nest-modules/src/entities/ogd-filing.entity.ts)**: Canadian OGD filings
- **[SimaFiling](mdc:libraries/nest-modules/src/entities/sima-filing.entity.ts)**: SIMA compliance filings
- **[CertificateOfOrigin](mdc:libraries/nest-modules/src/entities/certificate-of-origin.entity.ts)**: COO documents

### Canadian Tariff and Trade Data
- **[CanadaTariff](mdc:libraries/nest-modules/src/entities/canada-tariff.entity.ts)**: Canadian tariff codes
- **[CanadaTreatmentCode](mdc:libraries/nest-modules/src/entities/canada-treatment-code.entity.ts)**: Trade treatment codes
- **[CanadaOgd](mdc:libraries/nest-modules/src/entities/canada-ogd.entity.ts)**: OGD (Other Government Department) codes
- **[CanadaSimaCode](mdc:libraries/nest-modules/src/entities/canada-sima-code.entity.ts)**: SIMA-related codes
- **[CanadaVfdCode](mdc:libraries/nest-modules/src/entities/canada-vfd-code.entity.ts)**: VFD codes
- **[CanadaAntiDumping](mdc:libraries/nest-modules/src/entities/canada-anti-dumping.entity.ts)**: Anti-dumping measures
- **[CanadaExciseTaxCode](mdc:libraries/nest-modules/src/entities/canada-excise-tax-code.entity.ts)**: Excise tax codes
- **[CanadaGstExemptCode](mdc:libraries/nest-modules/src/entities/canada-gst-exempt-code.entity.ts)**: GST exemption codes
- **[CanadaSubLocation](mdc:libraries/nest-modules/src/entities/canada-sub-location.entity.ts)**: Sub-location codes

### Geographic Data
- **[Country](mdc:libraries/nest-modules/src/entities/country.entity.ts)**: Country reference data
- **[State](mdc:libraries/nest-modules/src/entities/state.entity.ts)**: State/province data
- **[Port](mdc:libraries/nest-modules/src/entities/port.entity.ts)**: Port codes and data

### Email System
- **[Email](mdc:libraries/nest-modules/src/entities/email.entity.ts)**: Email tracking and processing
- **[EmailUpdate](mdc:libraries/nest-modules/src/entities/email-update.entity.ts)**: Email processing updates
- **[EmailThread](mdc:libraries/nest-modules/src/entities/email-thread.entity.ts)**: Email conversation threading
- **[GmailToken](mdc:libraries/nest-modules/src/entities/gmail-token.entity.ts)**: Gmail API integration

### Matching and Rules Engine
- **[MatchingRule](mdc:libraries/nest-modules/src/entities/matching-rule.entity.ts)**: Business rule definitions
- **[MatchingCondition](mdc:libraries/nest-modules/src/entities/matching-condition.entity.ts)**: Rule condition logic
- **[MatchingHistory](mdc:libraries/nest-modules/src/entities/matching-history.entity.ts)**: Rule execution history

### System and Tracking
- **[TrackingHistory](mdc:libraries/nest-modules/src/entities/tracking-history.entity.ts)**: Audit trail for changes
- **[TransactionalEvent](mdc:libraries/nest-modules/src/entities/transactional-event.entity.ts)**: Event sourcing
- **[LlmLog](mdc:libraries/nest-modules/src/entities/llm-log.entity.ts)**: LLM interaction logging
- **[TariffSyncHistory](mdc:libraries/nest-modules/src/entities/tariff-sync-history.entity.ts)**: Tariff data sync tracking

### Integration Tokens
- **[DocusignToken](mdc:libraries/nest-modules/src/entities/docusign-token.entity.ts)**: DocuSign API integration
- **[ParsDocOverlay](mdc:libraries/nest-modules/src/entities/pars-doc-overlay.entity.ts)**: Document overlay data

## Database Patterns

### Multi-Tenancy
- All business entities have `organizationId` foreign key
- Organization-based row-level security
- Use scoping helpers (`inOrg`, `inOrgCls`) for queries

### Relationships
- Extensive use of TypeORM relations (`@ManyToOne`, `@OneToMany`)
- Cascade deletes configured appropriately
- Nullable foreign keys with `SET NULL` on delete where needed

### Enums
- Heavy use of TypeScript enums for status tracking
- Stored as PostgreSQL enum types in database
- Examples: `ShipmentStatus`, `CustomsStatus`, `TrackingStatus`

### Timestamps
- All entities have `createDate` and `lastEditDate`
- Uses PostgreSQL `timestamp with time zone` type
- Automatic handling via TypeORM decorators

### Computed Fields
- Many entities have computed display fields
- Use `@AfterInsert`, `@AfterLoad`, `@AfterUpdate` hooks
- Example: `displayName` computed from `name` or `id`

## Migration Management
- Migrations located in [libraries/nest-modules/src/migrations/](mdc:libraries/nest-modules/src/migrations)
- Uses TypeORM migration system
- Timestamp-based naming convention
- Recent migrations show container system and organizational changes

## Usage in Portal-API
- Import entities from `@claro/nest-modules`
- Use TypeORM repositories for data access
- Implement scoping through ClsService or REQUEST injection
- Follow NestJS dependency injection patterns for repositories
