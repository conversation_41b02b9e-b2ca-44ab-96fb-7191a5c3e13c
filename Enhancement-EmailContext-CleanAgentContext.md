# Enhancement Task: Add Email Context Support to CleanAgentContext

## Objective
Enhance the CleanAgentContextService to support dual-context template generation:
1. **Email-specific context**: Show documents and processing status from current email attachments only
2. **Shipment-wide context**: Show all shipment documents and missing document analysis (existing functionality)

## Background
Currently, CleanAgentContextService only provides shipment-wide document context via `DocumentDataGatherer.gather()`. The core-agent system needs the ability to distinguish between:
- Documents that came from the current email being processed (for "here's what we processed from your email" messages)
- All shipment documents (for "here's what's missing overall" analysis)

## Technical Requirements

### 1. Enhance `getUnifiedTemplateContext` Method Signature

**Current:**
```typescript
async getUnifiedTemplateContext(
  shipmentId: number,
  organizationId: number,
  request: any
): Promise<UnifiedTemplateContext>
```

**Enhanced:**
```typescript
async getUnifiedTemplateContext(
  shipmentId: number,
  organizationId: number,
  request: any,
  options?: {
    emailId?: number;           // Optional email ID for email-specific context
    gmailId?: string;           // Optional Gmail ID for FileBatch lookup
    includeEmailAttachments?: boolean; // Flag to control email-specific processing
  }
): Promise<UnifiedTemplateContext>
```

### 2. Enhance `DocumentDataGatherer.gather` Method

**Current:**
```typescript
async gather(request: { shipmentId: number }): Promise<DocumentTemplateData>
```

**Enhanced:**
```typescript
async gather(request: {
  shipmentId: number;
  emailContext?: {
    emailId: number;
    gmailId: string;
    includeEmailAttachments: boolean;
  };
}): Promise<DocumentTemplateData>
```

### 3. Email-Specific Document Processing Logic

**Add to DocumentDataGatherer:**
```typescript
private async gatherEmailSpecificDocuments(
  shipmentId: number,
  gmailId: string
): Promise<{
  emailAttachmentDocuments: ProcessedDocument[];
  emailProcessingSummary: {
    totalAttachments: number;
    processedSuccessfully: number;
    processingFailed: number;
    stillProcessing: number;
  };
}> {
  // 1. Get documents from FileBatch using Gmail ID
  const fileBatch = await this.fileBatchService.getFiles(gmailId, {
    documents: {
      documentType: true,
      status: true,
      fields: true
    }
  });

  // 2. Get aggregation status for these specific documents
  const documentIds = fileBatch
    .filter(file => file.documents?.length > 0)
    .flatMap(file => file.documents.map(doc => doc.id));

  const aggregationStatusMap = await this.aggregationService.getAggregationByTarget(
    AggregationTargetType.DOCUMENT,
    documentIds
  );

  // 3. Format documents for template with email-specific context
  const emailAttachmentDocuments = fileBatch.map(file => {
    const document = file.documents?.[0];
    if (!document) return null;

    const aggregationStatus = this.getAggregationStatus(document, aggregationStatusMap);
    
    return {
      filename: file.name || document.name || "Unknown Document",
      contentType: document.documentType?.name || "Unknown Type",
      aggregationStatus: aggregationStatus,
      claroUrl: this.generateClaroUrl(document.id, shipmentId),
      sourceContext: "email_attachment", // Mark as email attachment
      processedAt: document.updatedAt || document.createdAt,
      fileSize: file.size || null
    };
  }).filter(Boolean);

  // 4. Generate processing summary
  const emailProcessingSummary = {
    totalAttachments: fileBatch.length,
    processedSuccessfully: emailAttachmentDocuments.filter(doc => doc.aggregationStatus === "success").length,
    processingFailed: emailAttachmentDocuments.filter(doc => doc.aggregationStatus === "failed").length,
    stillProcessing: emailAttachmentDocuments.filter(doc => doc.aggregationStatus === "processing").length
  };

  return { emailAttachmentDocuments, emailProcessingSummary };
}
```

### 4. Enhanced PROCESSED_DOCUMENTS Context

**Modify the PROCESSED_DOCUMENTS generation to support both contexts:**

```typescript
// In DocumentDataGatherer.gather()
if (request.emailContext?.includeEmailAttachments) {
  // Email-specific context: only show documents from current email
  const { emailAttachmentDocuments, emailProcessingSummary } = 
    await this.gatherEmailSpecificDocuments(
      request.shipmentId, 
      request.emailContext.gmailId
    );

  return {
    ...existingDocumentData,
    PROCESSED_DOCUMENTS: emailAttachmentDocuments,
    EMAIL_PROCESSING_SUMMARY: emailProcessingSummary,
    CONTEXT_TYPE: "EMAIL_SPECIFIC"
  };
} else {
  // Shipment-wide context: show all documents + missing analysis (existing logic)
  return {
    ...existingDocumentData,
    PROCESSED_DOCUMENTS: allShipmentDocuments, // existing logic
    CONTEXT_TYPE: "SHIPMENT_WIDE"
  };
}
```

### 5. Core-Agent Integration Pattern

**Update HandleRequestMessageProcessor to use email context:**

```typescript
// In HandleRequestMessageProcessor.process()
const unifiedContext = await this.cleanAgentContextService.getUnifiedTemplateContext(
  shipmentId,
  organizationId,
  request,
  {
    emailId: email.id,
    gmailId: email.gmailId,
    includeEmailAttachments: true  // Enable email-specific processing
  }
);
```

### 6. Preserve Existing Functionality

**Ensure backward compatibility:**
- When no email context is provided, use existing shipment-wide logic
- Keep all existing missing document analysis functionality
- Maintain current template variable names and structure
- Preserve existing aggregation status mapping logic

## Implementation Steps

1. **Enhance method signatures** in `CleanAgentContextService` and `DocumentDataGatherer`

2. **Add email-specific document gathering logic** to `DocumentDataGatherer`

3. **Implement FileBatch integration** for email attachment lookup

4. **Add email processing summary generation**

5. **Update template context building** to support both modes

6. **Modify core-agent integration** to pass email context

7. **Add comprehensive logging** for email context processing

8. **Write tests** for both email-specific and shipment-wide contexts

## Testing Requirements

### Unit Tests
- Test email-specific document gathering with mock FileBatch data
- Test shipment-wide document gathering (existing functionality)
- Test aggregation status mapping for email attachments
- Test backward compatibility when no email context provided

### Integration Tests
- Test complete flow from HandleRequestMessageProcessor → CleanAgentContext → template rendering
- Test email attachment processing status accuracy
- Test missing document analysis remains functional
- Test template context generation for both modes

### Edge Cases
- Email with no attachments
- Email with attachments but no processed documents
- Email with failed document processing
- Shipment with no email context
- Invalid email ID or Gmail ID

## Expected Outcomes

After implementation:

1. **Email responses will show email-specific context**: "Here are the documents we processed from your email and their status"

2. **Shipment-wide analysis remains available**: "Here's what's missing for your shipment overall"  

3. **Core-agent can choose context scope** based on the type of response being generated

4. **Clean separation of concerns**: CleanAgentContext handles all template data generation, core-agent focuses on email processing workflow

5. **Backward compatibility maintained**: Existing functionality continues to work without email context

## Files to Modify

- `/apps/portal-api/src/clean-agent-context/clean-agent-context.service.ts`
- `/apps/portal-api/src/clean-agent-context/services/document-data-gatherer.service.ts`
- `/apps/portal-api/src/clean-agent-context/clean-agent-context.types.ts`
- `/apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts`

## Success Criteria

- [ ] CleanAgentContext supports email context parameters
- [ ] Email-specific document processing works correctly
- [ ] Shipment-wide missing document analysis preserved
- [ ] Core-agent integration updated to use email context
- [ ] All existing functionality remains backward compatible
- [ ] Comprehensive test coverage for both context modes
- [ ] Template rendering works for both email-specific and shipment-wide contexts