FROM node:20-slim

WORKDIR /usr/src/app
# WORKDIR /app

# # COPY dist ./dist

# # COPY package*.json ./
COPY . .

# # RUN npm install --production
# RUN npm install -g pnpm
# RUN pnpm install --prod
RUN node create-links.js create

# # RUN npm install
# # RUN npm run build

EXPOSE 5001

# CMD ["node", "dist/main.js"]
CMD ["node", "apps/backoffice-api/dist/main.js"]

# Stage 1: Install Rush and prepare the deployment
# FROM node:20-slim

# Install Rush globally in the container
# RUN npm install -g @microsoft/rush

# Set the working directory inside the container

# Copy the rush.json and the common/config/rush folder, needed for rush to run
# COPY rush.json ./
# COPY common/config/rush common/config/rush
# COPY common/scripts common/scripts

# Copy your specific project's deployable folder (from rush deploy)
# Ensure this deployable folder has all the necessary files (package.json, dist, etc.)
# COPY common/deploy ./

# Run `rush install` to resolve workspace dependencies
# RUN node common/scripts/install-run-rush.js install --bypass-policy

# Stage 2: Create the production image
# FROM builder AS production

# Set the working directory inside the container
# WORKDIR /usr/src/app

# Copy the built app and dependencies from the builder stage
# COPY --from=builder /usr/src/app/ ./


# Expose the port your app will run on
# EXPOSE 5001

# Start the app (assume main entry point is dist/main.js)
# CMD ["node", "dist/main.js"]
