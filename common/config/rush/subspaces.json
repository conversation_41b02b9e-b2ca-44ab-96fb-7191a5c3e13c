/**
 * This configuration file manages the experimental "subspaces" feature for <PERSON>,
 * which allows multiple PNPM lockfiles to be used in a single Rush workspace.
 * For full documentation, please see https://rushjs.io
 */
{
  "$schema": "https://developer.microsoft.com/json-schemas/rush/v5/subspaces.schema.json",

  /**
   * Set this flag to "true" to enable usage of subspaces.
   */
  "subspacesEnabled": false,

  /**
   * (DEPRECATED) This is a temporary workaround for migrating from an earlier prototype
   * of this feature: https://github.com/microsoft/rushstack/pull/3481
   * It allows subspaces with only one project to store their config files in the project folder.
   */
  "splitWorkspaceCompatibility": false,

  /**
   * When a command such as "rush update" is invoked without the "--subspace" or "--to"
   * parameters, <PERSON> will install all subspaces.  In a huge monorepo with numerous subspaces,
   * this would be extremely slow.  Set "preventSelectingAllSubspaces" to true to avoid this
   * mistake by always requiring selection parameters for commands such as "rush update".
   */
  "preventSelectingAllSubspaces": false,

  /**
   * The list of subspace names, which should be lowercase alphanumeric words separated by
   * hyphens, for example "my-subspace".  The corresponding config files will have paths
   * such as "common/config/subspaces/my-subspace/package-lock.yaml".
   */
  "subspaceNames": []
}
