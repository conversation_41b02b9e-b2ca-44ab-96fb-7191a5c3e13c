# <PERSON> uses this file to configure the NPM package registry during installation.  It is applicable
# to PNPM, NPM, and Yarn package managers.  It is used by operations such as "rush install",
# "rush update", and the "install-run.js" scripts.
#
# NOTE: The "rush publish" command uses .npmrc-publish instead.
#
# Before invoking the package manager, <PERSON> will generate an .npmrc in the folder where installation
# is performed.  This generated file will omit any config lines that reference environment variables
# that are undefined in that session; this avoids problems that would otherwise result due to
# a missing variable being replaced by an empty string.
#
# If "subspacesEnabled" is true in subspaces.json, the generated file will merge settings from
# "common/config/rush/.npmrc" and "common/config/subspaces/<name>/.npmrc", with the latter taking
# precedence.
#
# * * * SECURITY WARNING * * *
#
# It is NOT recommended to store authentication tokens in a text file on a lab machine, because
# other unrelated processes may be able to read that file.  Also, the file may persist indefinitely,
# for example if the machine loses power.  A safer practice is to pass the token via an
# environment variable, which can be referenced from .npmrc using ${} expansion.  For example:
#
#   //registry.npmjs.org/:_authToken=${NPM_AUTH_TOKEN}
#
registry=https://registry.npmjs.org/
always-auth=false
