{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(rg:*)", "Bash(rg:*)", "Bash(grep:*)", "mcp__cw-mcp-server__list_log_groups", "Bash(node:*)", "Bash(rush build)", "Bash(nvm use:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(rushx build:*)", "Bash(rushx --to portal-api lint)", "Bash(./src/core-agent/testing/run-processor-test-with-logs.sh:*)", "Bash(./src/core-agent/testing/run-e2e-with-logs.sh:*)", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(chmod:*)", "Bash(./src/core-agent/testing/run-rush-processing-tests.sh:*)", "<PERSON><PERSON>(timeout:*)", "Bash(git checkout:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(cat:*)", "WebFetch(domain:github.com)", "Bash(./pr-actionable-comments.sh:*)", "<PERSON><PERSON>(jq:*)", "mcp__ide__executeCode", "<PERSON><PERSON>(echo:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(APIFY_TOKEN=********************************************** node src/us-tariff/testing/test-kv-store-direct.js)", "Bash(APIFY_TOKEN=********************************************** node src/us-tariff/testing/test-apify-access.js)", "Bash(rush build:*)", "Bash(npx typeorm migration:generate:*)", "Bash(npx typeorm migration:run:*)", "<PERSON><PERSON>(rush list:*)", "Bash(DB_HOST=localhost DB_PORT=5432 DB_NAME=claro_dev DB_USER=postgres DB_PASSWORD=superuser NODE_ENV=local npx typeorm migration:run -d libraries/nest-modules/dist/data-source.js)", "<PERSON><PERSON>(curl:*)", "Bash(rm:*)", "Bash(npm run build:*)", "Bash(npm run typeorm:migrate:*)", "Bash(PGPASSWORD=superuser psql -U postgres -h localhost -d claro_dev -c \"SELECT COUNT(*) as tariff_count FROM us_tariff; SELECT COUNT(*) as pivot_count FROM us_tariff_pga_requirement;\")", "<PERSON><PERSON>(mv:*)", "Bash(rushx lint:*)", "Bash(cd /home/<USER>/dev/Claro)", "Bash(rg -A 2 -B 2 \"scope:\\s*Scope\\.REQUEST\" --type ts)", "Bash(rg -B 5 -A 5 \"ClsModule\\.forRoot\" apps/portal-api/src/app.module.ts)", "Bash(rg -B 5 -A 15 \"ClsModule\\.forRoot\" apps/portal-api/src/app.module.ts)", "WebFetch(domain:claudelog.com)", "WebFetch(domain:cuong.io)", "WebFetch(domain:www.anthropic.com)", "Bash(rushx:*)", "<PERSON><PERSON>(claude task \"Get template system overview from docs expert\" \"I need to understand the current template system in the core-agent. Please explain: 1) How the current template system works 2) What templates exist and their purposes 3) The new unified template approach 4) Any migration considerations or coverage concerns between old and new templates\" --subagent-type claro-docs-expert)", "<PERSON><PERSON>(claude task --help)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(npm test:*)", "Bash(npx typescript:*)", "Bash(npm run:*)", "Bash(npx tsc:*)", "Bash(./src/core-agent/testing/run-template-status-test.sh:*)", "Bash(cp:*)"], "deny": []}}