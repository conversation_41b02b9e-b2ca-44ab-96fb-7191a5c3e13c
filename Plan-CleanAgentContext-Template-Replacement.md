# Plan: Remove Legacy Template/Response System and Replace One Handler with UnifiedTemplateRendererService

This plan removes the existing legacy template and response system (including its dedicated data retrieval), and migrates a single, easy handler to the new system that uses CleanAgentContextService for data and UnifiedTemplateRendererService for HTML output. The goal is to land a minimal, correct end-to-end path and keep BullMQ/NestJS scoping safe.

Scope of removal (legacy):
- ShipmentResponseService fragment/response model and any legacy fragment assemblers used by the processor path
- TemplateManagerService usage from the core-agent processor path
- Any legacy template-specific data retrieval/services that were only needed for the old renderer

New system components to use:
- CleanAgentContextService (apps/portal-api/src/clean-agent-context/clean-agent-context.service.ts)
  - Provides getUnifiedTemplateContext(...), which includes the business logic migrated from UnifiedTemplateRendererService
- UnifiedTemplateRendererService
  - Use this service to produce the final HTML from the unified context and the new fragment inputs
  - If the class is not currently present as a concrete service, create a thin service that wraps CleanAgentContextService’s unified context and renders the new clean fragments (the renderer name and provider token should be UnifiedTemplateRendererService)

Migration target (first handler):
- GET_SHIPMENT_STATUS handler (apps/portal-api/src/core-agent/handlers/...)
  - Replace its legacy fragment/response output with the new clean fragment(s)
  - Route final HTML through UnifiedTemplateRendererService

---

## Step-by-step Plan

### Step 1 — Define minimal Clean Fragments for the new system
Create a small, explicit fragment model that represents the output from handlers under the new system.

Files to add:
- apps/portal-api/src/core-agent/clean-fragments/clean-fragment.types.ts

Content (initial):
```ts
export type CleanFragment =
  | { kind: "shipment-status"; data: { shipmentId: number; status?: string | null } }
  | { kind: "tracking-info"; data: { trackingNumbers: string[] } }
  | { kind: "free-text"; data: { html: string } }
  | { kind: "cta"; data: { label: string; href: string } };

export interface CleanFragmentBundle {
  fragments: CleanFragment[];
}
```

Notes:
- Keep kinds small and semantic. Payloads are serializable and not ORM entities.
- Expand as more handlers migrate.

### Step 2 — Clean Fragment Builder service
Create a helper service for handlers to build CleanFragments.

File:
- apps/portal-api/src/core-agent/services/clean-fragment-builder.service.ts

Content (scaffold):
```ts
import { Injectable } from "@nestjs/common";
import { CleanFragment, CleanFragmentBundle } from "../clean-fragments/clean-fragment.types";

@Injectable()
export class CleanFragmentBuilderService {
  bundle(...fragments: CleanFragment[]): CleanFragmentBundle {
    return { fragments };
  }

  shipmentStatus(opts: { shipmentId: number; status?: string | null }): CleanFragment {
    return { kind: "shipment-status", data: { shipmentId: opts.shipmentId, status: opts.status ?? null } };
  }

  trackingInfo(trackingNumbers: string[]): CleanFragment {
    return { kind: "tracking-info", data: { trackingNumbers } };
  }

  freeTextHtml(html: string): CleanFragment {
    return { kind: "free-text", data: { html } };
  }

  cta(label: string, href: string): CleanFragment {
    return { kind: "cta", data: { label, href } };
  }
}
```

### Step 3 — UnifiedTemplateRendererService (final HTML)
Use UnifiedTemplateRendererService to render final HTML. If not present, implement a thin service with this name that:
- Calls CleanAgentContextService.getUnifiedTemplateContext(shipmentId, organizationId, requestContext, emailMeta)
- Accepts CleanFragment[]
- Produces safe HTML using the unified context plus the fragments

File (if needed):
- apps/portal-api/src/core-agent/services/unified-template-renderer.service.ts

Content (scaffold):
```ts
import { Injectable, Logger } from "@nestjs/common";
import { CleanAgentContextService } from "@/clean-agent-context/clean-agent-context.service";
import { CleanFragment } from "../clean-fragments/clean-fragment.types";

@Injectable()
export class UnifiedTemplateRendererService {
  private readonly logger = new Logger(UnifiedTemplateRendererService.name);

  constructor(private readonly cleanAgentContextService: CleanAgentContextService) {}

  async render(options: {
    shipmentId: number;
    organizationId: number;
    requestContext: any; // generateRequest(null, organization)
    fragments: CleanFragment[];
    emailMeta?: { emailId: number; gmailId: string | null };
  }): Promise<string> {
    const { shipmentId, organizationId, requestContext, fragments, emailMeta } = options;

    const unifiedContext = await this.cleanAgentContextService.getUnifiedTemplateContext(
      shipmentId,
      organizationId,
      requestContext,
      {
        emailId: emailMeta?.emailId,
        gmailId: emailMeta?.gmailId ?? null,
        includeEmailAttachments: true
      }
    );

    const parts: string[] = [];
    parts.push("<div>");
    parts.push("<p>Hello,</p>");

    for (const f of fragments) {
      switch (f.kind) {
        case "shipment-status":
          parts.push(`<h3>Shipment Status</h3>`);
          parts.push(`<p>Status: ${unifiedContext.STATUS_CATEGORY ?? f.data.status ?? "Unknown"}</p>`);
          break;
        case "tracking-info":
          parts.push(`<h3>Tracking</h3>`);
          parts.push(`<ul>`);
          for (const tn of f.data.trackingNumbers) parts.push(`<li>${tn}</li>`);
          parts.push(`</ul>`);
          break;
        case "free-text":
          parts.push(f.data.html);
          break;
        case "cta":
          parts.push(`<p><a href="${f.data.href}">${f.data.label}</a></p>`);
          break;
      }
    }

    parts.push("</div>");
    return parts.join("\n");
  }
}
```

### Step 4 — Wire module providers and remove legacy providers
Update apps/portal-api/src/core-agent/core-agent.module.ts:
- Add providers/exports: CleanFragmentBuilderService, UnifiedTemplateRendererService
- Remove TemplateManagerService from this module (and any legacy response/fragment services used only by the old path)
- Keep CleanAgentContextService as-is (provided in its own module)

### Step 5 — Update the processor to drop legacy response system
File: apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts
- Remove usages of ShipmentResponseService and TemplateManagerService (and legacy fragment model) from the request processing path
- Inject CleanFragmentBuilderService and UnifiedTemplateRendererService
- Keep BullMQ scoping patterns (ContextIdFactory + moduleRef.registerRequestByContextId)
- For “no intents” fallback: return a minimal literal HTML or a helper in UnifiedTemplateRendererService (simple paragraph is fine)

Pseudocode:
```ts
// Execute intents and invoke ONE migrated handler (GET_SHIPMENT_STATUS) only.
const fragments: CleanFragment[] = [];
if (intent === "GET_SHIPMENT_STATUS") {
  const cleanBundle = await this.getShipmentStatusHandler(...).handleWithCleanFragments(...);
  fragments.push(...cleanBundle.fragments);
}

const html = await this.unifiedTemplateRenderer.render({
  shipmentId: shipment.id,
  organizationId: organization.id,
  requestContext: cleanAgentRequest,
  fragments,
  emailMeta: { emailId: email.id, gmailId: email.gmailId }
});
```

### Step 6 — Migrate the single easy handler (GET_SHIPMENT_STATUS)
- Update the GET_SHIPMENT_STATUS handler to depend on CleanFragmentBuilderService
- Return CleanFragmentBundle containing shipment-status and any simple supporting fragments
- Remove any use of ShipmentResponseService or legacy fragment/response code

Example handler return:
```ts
return this.cleanFragmentBuilder.bundle(
  this.cleanFragmentBuilder.shipmentStatus({ shipmentId: shipment.id, status: computedStatus })
);
```

---

## Clean-up and guardrails
- Delete or isolate legacy response/fragment code paths from the processor
- Ensure tests reference only the new path for the migrated handler
- Maintain request-scoped resolution via ContextIdFactory and synthetic request contexts for worker safety

---

## Deliverables Checklist
- New: clean-fragment.types.ts
- New: clean-fragment-builder.service.ts
- New or confirmed: UnifiedTemplateRendererService (using CleanAgentContextService internally)
- Updated: core-agent.module.ts — add new providers/exports, remove legacy template/response providers
- Updated: handle-request-message.processor.ts — remove legacy response system, use UnifiedTemplateRendererService for final HTML
- Updated: GET_SHIPMENT_STATUS handler — emits CleanFragmentBundle
- Tests: minimal validation that the email HTML renders via UnifiedTemplateRendererService for GET_SHIPMENT_STATUS
