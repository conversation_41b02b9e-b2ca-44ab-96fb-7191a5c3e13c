{"name": "nest-modules", "version": "1.0.0", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": "./dist/index.js", "./user": "./dist/user/index.js", "./types": "./dist/types/index.js", "./middleware": "./dist/middleware/index.js", "./guards": "./dist/guards/index.js", "./entities": "./dist/entities/index.js", "./dto": "./dist/dto/index.js", "./decorators": "./dist/decorators/index.js", "./auth": "./dist/auth/index.js"}, "scripts": {"prebuild": "rm -rf dist", "build": "tsc --build", "start": "tsc --watch", "typeorm:create": "typeorm-ts-node-commonjs migration:create", "typeorm:generate": "typeorm-ts-node-commonjs migration:generate -d src/data-source.ts", "typeorm:migrate": "typeorm migration:run -d dist/data-source.js", "typeorm:revert": "typeorm migration:revert -d dist/data-source.js"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/swagger": "^7.3.1", "@nestjs/typeorm": "^10.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "express": "^4.19.2", "google-auth-library": "^9.9.0", "moment-timezone": "~0.5.45", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.20", "cheerio": "~1.0.0", "axios": "~1.7.7", "adm-zip": "~0.5.16", "mdb-reader": "~3.0.0", "@nestjs/config": "~3.3.0", "@nestjs/event-emitter": "~2.1.1", "fast-xml-parser": "~4.5.0", "dotenv": "~16.4.7", "googleapis": "~144.0.0", "nunjucks": "~3.2.4", "openai": "~4.83.0", "tiktoken": "~1.0.20", "firebase-admin": "~13.2.0", "xlsx": "~0.18.5", "@nestjs/platform-express": "^10.0.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.12.7", "ts-node": "^10.9.2", "typescript": "^5.4.5", "@types/adm-zip": "~0.5.5", "@types/multer": "~1.4.12", "@types/nunjucks": "~3.2.6"}}