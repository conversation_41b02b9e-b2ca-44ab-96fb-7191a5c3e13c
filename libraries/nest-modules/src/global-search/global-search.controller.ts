import { BadRequestException, Controller, Get, Query, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { ApiAccessTokenAuthenticated, ApiGetManyResponses } from "../decorators";
import { AccessTokenGuard } from "../guards";
import { searchRegistry } from "../types/global-search.types";
import { GlobalSearchService } from "./global-search.service";
import { GlobalSearchParamsDto, GlobalSearchResponseDto } from "../dto/global-search.dto";

@ApiTags("Global Search API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("global-search")
export class GlobalSearchController {
  constructor(private readonly globalSearchService: GlobalSearchService) {}

  @Get()
  @ApiOperation({ summary: "Global search with context" })
  @ApiGetManyResponses({ type: GlobalSearchResponseDto })
  async search(@Query() { term, contexts }: GlobalSearchParamsDto) {
    if (!term?.trim()) throw new BadRequestException("Query is empty");

    const _contexts = contexts ? contexts : Object.keys(searchRegistry);

    const results = await this.globalSearchService.search(term.trim(), _contexts);
    return results;
  }
}
