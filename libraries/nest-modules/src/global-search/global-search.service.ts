import { BadRequestException, Inject, Injectable, InternalServerErrorException, Scope } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import moment from "moment-timezone";
import { Brackets, DataSource } from "typeorm";
import { GlobalSearchResponseDto } from "../dto/global-search.dto";
import { AuthenticatedRequest } from "../types/auth.types";
import { SearchContext, SearchField, searchRegistry } from "../types/global-search.types";

@Injectable({ scope: Scope.REQUEST })
export class GlobalSearchService {
  constructor(
    private readonly dataSource: DataSource,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest
  ) {}

  private getColumn(context: string, field: SearchField) {
    if (field.relation) {
      const relationParts = field.relation.split(".");
      const lastRelation = relationParts[relationParts.length - 1];
      return `"${lastRelation}"."${field.field}"`;
    }
    return `"${context}"."${field.field}"`;
  }

  private buildQueryForContext(context: string, term: string, organizationId: number) {
    const fields: SearchField[] = searchRegistry[context];
    if (!fields) throw new BadRequestException(`No search registry for context: ${context}`);

    const titleField = fields.find((f) => f.display === "title");
    const subtitleFields = fields.filter((f) => f.display === "subtitle");

    const qb = this.dataSource
      .createQueryBuilder()
      .select(`${context}.id`, "id")
      .distinct(true)
      .addSelect(`'${context}'`, "context")
      .addSelect(`${context}.createDate`, "createDate")
      .from(context, context);

    // Add threadId selection for email context
    if (context === SearchContext.EMAIL) {
      qb.addSelect(`${context}.threadId`, "threadId");
    }

    qb.orderBy(`${context}.createDate`, "DESC");

    const joinedRelations = new Set<string>();

    for (const field of fields) {
      if (field.relation && !joinedRelations.has(field.relation)) {
        const relationParts = field.relation.split(".");
        // Email specific join logic
        if (context === SearchContext.EMAIL && relationParts[0] === "emailThread") {
          // Special case for email -> emailThread join
          if (!joinedRelations.has("emailThread")) {
            qb.leftJoin("email_thread", "emailThread", `"emailThread"."threadId" = ${context}."threadId"`);
            joinedRelations.add("emailThread");
          }

          // Join subsequent relations if any
          let currentEntity = "emailThread";
          for (let i = 1; i < relationParts.length; i++) {
            const relationPath = relationParts.slice(0, i + 1).join(".");
            if (!joinedRelations.has(relationPath)) {
              qb.leftJoin(`${currentEntity}.${relationParts[i]}`, relationParts[i]);
              joinedRelations.add(relationPath);
            }
            currentEntity = relationParts[i];
          }
        } else {
          // Normal case for other relations
          let currentEntity = context;
          for (let i = 0; i < relationParts.length; i++) {
            const relationPath = relationParts.slice(0, i + 1).join(".");
            if (!joinedRelations.has(relationPath)) {
              qb.leftJoin(`${currentEntity}.${relationParts[i]}`, relationParts[i]);
              joinedRelations.add(relationPath);
            }
            currentEntity = relationParts[i];
          }
        }
      }
    }

    const col = this.getColumn(context, titleField);
    qb.addSelect(col, "title");
    subtitleFields.forEach((field, index) => {
      const col = this.getColumn(context, field);
      qb.addSelect(col, `subtitle_${index}`);
    });

    qb.where(
      new Brackets((qbWhere) => {
        let ftsAdded = false;
        for (const field of fields) {
          const column = this.getColumn(context, field);

          if (field.fts && !ftsAdded) {
            // Split the search term into words and create a prefix match for each
            const searchTerms = term
              .toLowerCase()
              .split(/\s+/)
              .filter(Boolean)
              .map((word) => `${word}:*`)
              .join(" & ");

            qbWhere.orWhere(`${context}.search_vector @@ to_tsquery('english', :ftsTerm)`, {
              ftsTerm: searchTerms
            });
            ftsAdded = true;
          } else {
            switch (field.dataType) {
              case "enum":
                qbWhere.orWhere(`${column}::text ILIKE :likeTerm`, {
                  likeTerm: `%${term}%`
                });
                break;
              case "string":
              default:
                qbWhere.orWhere(`${column} ILIKE :likeTerm`, {
                  likeTerm: `%${term}%`
                });
                break;
            }
          }
        }
      })
    );

    qb.andWhere(`${context}.organizationId = :organizationId`, { organizationId });

    return qb;
  }

  async search(term: string, contexts: string[]): Promise<GlobalSearchResponseDto[]> {
    const organizationId = this.request?.user?.organization?.id || -1;

    try {
      const queries = await Promise.all(
        contexts.map((context) => this.buildQueryForContext(context, term, organizationId))
      );

      const results = await Promise.all(queries.map((qb) => qb.getRawMany()));

      const uniqueResults = results.map((result) => {
        const uniqueItem = new Set<string>();
        return result.filter((res) => {
          const key = `${res.context}_${res.id}`;
          if (uniqueItem.has(key)) {
            return false;
          }
          uniqueItem.add(key);
          return true;
        });
      });

      const flattenedResults = uniqueResults.flat().map((res) => {
        const subtitle = [
          ...Object.keys(res)
            .filter((k) => k.startsWith("subtitle_"))
            .map((k) => res[k])
            .filter(Boolean),
          moment(res.createDate).tz("America/Toronto").format("YYYY-MM-DD HH:mm")
        ].join(" · ");

        return {
          id: res.id,
          title: res.title,
          subtitle,
          context: res.context,
          createDate: res.createDate,
          threadId: res.threadId
        };
      });

      return flattenedResults;
    } catch (error: any) {
      throw new InternalServerErrorException(`Failed to search: ${error?.message}`);
    }
  }
}
