import { Module } from "@nestjs/common";
import { TransactionSubscriber } from "./subscribers";
import { TransactionalEventEmitterService } from "./services";
import { TypeOrmModule } from "@nestjs/typeorm";
import { TransactionalEvent } from "../entities";

@Module({
  imports: [TypeOrmModule.forFeature([TransactionalEvent])],
  providers: [
    // Services
    TransactionalEventEmitterService,

    // Subscribers
    TransactionSubscriber
  ],
  controllers: [],
  exports: [TransactionalEventEmitterService]
})
export class TransactionalEventEmitterModule {}
