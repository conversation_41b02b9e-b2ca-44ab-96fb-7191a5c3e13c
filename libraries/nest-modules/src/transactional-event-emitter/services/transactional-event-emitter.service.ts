import { Injectable, Logger } from "@nestjs/common";
import { EventEmitter2, OnEvent } from "@nestjs/event-emitter";
import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import { DataSource, QueryRunner, Repository } from "typeorm";
import { TransactionalEvent } from "../../entities";
import { DeserializedValue } from "../types";
import { randomUUID } from "crypto";
import { deserializeValue, serializeValue } from "../utils";
import { SortOrder } from "../../types";

@Injectable()
export class TransactionalEventEmitterService {
  constructor(
    @InjectRepository(TransactionalEvent)
    private readonly transactionalEventRepository: Repository<TransactionalEvent>,
    private readonly eventEmitter: EventEmitter2
  ) {}
  private readonly logger = new Logger(TransactionalEventEmitterService.name);

  /**
   * Enqueues an event to be emitted when the transaction is committed.
   * @param event - The event name
   * @param queryRunner - The query runner instance. If not provided or no active transaction is attached to the query runner, the event will be emitted immediately.
   * @param values - The values to be provided as arguments to the event handlers. The supported types of values are: string, number, bigint, boolean, null, undefined, object, array, date
   */
  async enqueueEvent(
    event: string | Array<string>,
    queryRunner?: QueryRunner | null,
    ...values: Array<any>
  ): Promise<void> {
    const serializedValues = serializeValue(values);
    if (queryRunner && !queryRunner.isReleased && queryRunner.isTransactionActive) {
      if (
        typeof queryRunner.data?.transactionId !== "string" ||
        queryRunner.data?.transactionId?.length <= 0
      ) {
        this.logger.warn(`Transaction ID is not set for the query runner, generating a new one...`);
        queryRunner.data.transactionId = randomUUID();
      }

      this.logger.log(`Enqueuing event ${event} with transaction ID: ${queryRunner.data?.transactionId}`);
      // Enqueue the event
      let newTransactionalEvent = new TransactionalEvent();
      newTransactionalEvent.event = JSON.stringify(event);
      newTransactionalEvent.valuesJson = JSON.stringify(serializedValues);
      newTransactionalEvent.transactionId = queryRunner.data?.transactionId;
      newTransactionalEvent = await this.transactionalEventRepository.save(newTransactionalEvent);
      this.logger.log(
        `Enqueued event ${newTransactionalEvent.id}. Transaction ID: ${newTransactionalEvent.transactionId}, Event: ${newTransactionalEvent.event}`
      );
    } else {
      this.logger.log(
        `Either query runner is not provided or no active transaction attached to it, emitting event immediately`
      );
      // Emit the event immediately
      this.eventEmitter.emit(event, ...(deserializeValue(serializedValues) as Array<DeserializedValue>));
    }
    return;
  }
}
