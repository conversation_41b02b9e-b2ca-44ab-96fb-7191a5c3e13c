export type SerializedBigInt = {
  type: "bigint";
  // Stringify value of the bigint
  value: string;
};

export type SerializedUndefined = {
  type: "undefined";
};

export type SerializedDate = {
  type: "date";
  // ISO string of the date
  value: string;
};

export type SerializedObject = {
  type: "object";
  // Object that contains other serialized values
  value: Object;
};

export type SerializedValue =
  | string
  | number
  | boolean
  | null
  | SerializedBigInt
  | SerializedUndefined
  | SerializedDate
  | SerializedObject
  | Array<SerializedValue>;

export type DeserializedValue =
  | string
  | number
  | boolean
  | null
  | bigint
  | undefined
  | Date
  | Object
  | Array<DeserializedValue>;
