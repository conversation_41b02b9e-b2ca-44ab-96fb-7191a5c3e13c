import { BadRequestException } from "@nestjs/common";
import { SerializedValue, DeserializedValue } from "../types";

/**
 * Serializes a value to the format that can be stored in the database. Only the following types are supported: string, number, boolean, null, undefined, bigint, date, object, array.
 * @param value - The value to serialize
 * @returns The serialized value
 */
export function serializeValue(value: any): SerializedValue {
  switch (typeof value) {
    // For string, number and boolean, we can directly assign them to the serialized values
    case "string":
    case "number":
    case "boolean":
      return value;
    // For bigint, we need to convert it to string
    case "bigint":
      return {
        type: "bigint",
        value: value.toString()
      };
    // For undefined, we can just create a new object with the type undefined
    case "undefined":
      return {
        type: "undefined"
      };
    case "object":
      // For null, we can directly assign it to the serialized values
      if (value === null) return value;
      // For date, we need to convert it to ISO string
      else if (value instanceof Date)
        return {
          type: "date",
          value: value.toISOString()
        };
      // For array, we need to serialize each element
      else if (Array.isArray(value)) return value.map(serializeValue);
      // For object, we need to serialize each property
      else {
        const serializedObject: Record<string, SerializedValue> = {};
        for (const [subKey, subValue] of Object.entries(value))
          serializedObject[subKey] = serializeValue(subValue);
        return {
          type: "object",
          value: serializedObject
        };
      }
    default:
      throw new BadRequestException(`Cannot serialize value of type ${typeof value}`);
  }
}

/**
 * Deserializes a value from the format that can be stored in the database.
 * @param value - The value to deserialize
 * @returns The deserialized value
 */
export function deserializeValue(value: SerializedValue): DeserializedValue {
  switch (typeof value) {
    case "string":
    case "number":
    case "boolean":
      return value;
    case "object":
      if (value === null) return value;
      else if (Array.isArray(value)) return value.map(deserializeValue);
      else {
        switch (value?.type) {
          case "undefined":
            return undefined;
          case "bigint":
            if (typeof value.value !== "string" || !/^\d+$/.test(value.value))
              throw new BadRequestException(`Value provided is not a valid BigInt`);
            return BigInt(value.value);
          case "date":
            if (
              typeof value.value !== "string" ||
              !/^(\d{4}|[+-]\d{6})-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/.test(value.value)
            )
              throw new BadRequestException(`Value provided is not a valid Date`);
            return new Date(value.value);
          case "object":
            const deserializedObject: Record<string, DeserializedValue> = {};
            for (const [subKey, subValue] of Object.entries(value.value))
              deserializedObject[subKey] = deserializeValue(subValue);
            return deserializedObject;
          default:
            throw new BadRequestException(`Invalid serialized value provided`);
        }
      }
    default:
      throw new BadRequestException(`Cannot deserialize value of type ${typeof value}`);
  }
}
