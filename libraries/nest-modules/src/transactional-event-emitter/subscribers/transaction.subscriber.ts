import { Injectable, Logger } from "@nestjs/common";
import { InjectDataSource, InjectRepository } from "@nestjs/typeorm";
import {
  EntitySubscriberInterface,
  TransactionCommitEvent,
  TransactionRollbackEvent,
  TransactionStartEvent,
  DataSource,
  Repository
} from "typeorm";
import { randomUUID } from "crypto";
import { TransactionalEvent } from "../../entities";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { SortOrder } from "../../types";
import { DeserializedValue } from "../types";
import { deserializeValue } from "../utils";

@Injectable()
export class TransactionSubscriber implements EntitySubscriberInterface {
  constructor(
    @InjectRepository(TransactionalEvent)
    private readonly transactionalEventRepository: Repository<TransactionalEvent>,
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly eventEmitter: EventEmitter2
  ) {
    dataSource.subscribers.push(this);
  }
  private readonly logger = new Logger(TransactionSubscriber.name);

  async afterTransactionStart(event: TransactionStartEvent) {
    if (
      typeof event.queryRunner.data?.transactionId !== "string" ||
      event.queryRunner.data?.transactionId?.length <= 0
    ) {
      // this.logger.debug(`No transaction ID found, generating a new one...`);
      event.queryRunner.data.transactionId = randomUUID();
    }
    // this.logger.debug(`Transaction started, Transaction ID: ${event.queryRunner.data?.transactionId}`);
  }

  async afterTransactionCommit(event: TransactionCommitEvent) {
    // this.logger.debug(`Transaction committed, Transaction ID: ${event.queryRunner.data?.transactionId}`);
    if (
      typeof event.queryRunner.data?.transactionId !== "string" ||
      event.queryRunner.data?.transactionId?.length <= 0
    ) {
      this.logger.warn(`No transaction ID found, skipping event emission...`);
      return;
    }

    // Emit the events that are associated with the transaction
    const transactionalEvents = await this.transactionalEventRepository.find({
      where: { transactionId: event.queryRunner.data?.transactionId },
      order: { createDate: SortOrder.ASC }
    });
    // this.logger.debug(`Found ${transactionalEvents.length} events to emit...`);
    for (const transactionalEvent of transactionalEvents) {
      try {
        // this.logger.debug(
        //   `Emitting event ${transactionalEvent.event}, ID: ${transactionalEvent.id}, value JSON: ${transactionalEvent.valuesJson}`
        // );
        const result = this.eventEmitter.emit(
          JSON.parse(transactionalEvent.event),
          ...(deserializeValue(JSON.parse(transactionalEvent.valuesJson) || []) as Array<DeserializedValue>)
        );
        // this.logger.debug(`Event ${transactionalEvent.event} emitted with result: ${result}`);
      } catch (error) {
        this.logger.error(
          `Error emitting event "${transactionalEvent.event}" ID: ${transactionalEvent.id}: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }

    // this.logger.debug(`Deleting events for transaction ID: ${event.queryRunner.data?.transactionId}`);
    const deleteResult = await this.transactionalEventRepository.delete({
      transactionId: event.queryRunner.data?.transactionId
    });
    // this.logger.debug(`Deleted ${deleteResult.affected} events`);
  }

  async afterTransactionRollback(event: TransactionRollbackEvent) {
    // this.logger.debug(`Transaction rolled back, Transaction ID: ${event.queryRunner.data?.transactionId}`);

    if (
      typeof event.queryRunner.data?.transactionId !== "string" ||
      event.queryRunner.data?.transactionId?.length <= 0
    ) {
      this.logger.warn(`No transaction ID found, skipping event deletion...`);
      return;
    }

    // this.logger.debug(`Deleting events for transaction ID: ${event.queryRunner.data?.transactionId}`);
    const deleteResult = await this.transactionalEventRepository.delete({
      transactionId: event.queryRunner.data?.transactionId
    });
    // this.logger.debug(`Deleted ${deleteResult.affected} events`);
  }
}
