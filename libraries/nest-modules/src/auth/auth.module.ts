import { DynamicModule, Module } from "@nestjs/common";
import { JwtModule } from "@nestjs/jwt";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AUTH_MODULE_OPTIONS, AuthModuleOptions } from "../types/auth.types";
import { AuthService } from "./auth.service";
import { AuthController } from "./auth.controller";
import { RefreshToken } from "../entities/refresh-token.entity";
import { User } from "../entities/user.entity";
import { Organization } from "../entities/organization.entity";
import { Password } from "../entities/password.entity";

@Module({})
export class AuthModule {
  static register(options: AuthModuleOptions): DynamicModule {
    return {
      global: true,
      module: AuthModule,
      imports: [
        JwtModule.register({ global: true }),
        TypeOrmModule.forFeature([RefreshToken, Password, User, Organization])
      ],
      controllers: [AuthController],
      providers: [
        {
          provide: AUTH_MODULE_OPTIONS,
          useValue: options
        },
        AuthService
      ],
      exports: [AuthService]
    };
  }
}
