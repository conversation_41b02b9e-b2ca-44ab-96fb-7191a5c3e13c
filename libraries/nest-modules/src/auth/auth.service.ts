import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException
} from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { InjectRepository } from "@nestjs/typeorm";
import { randomBytes } from "crypto";
import { OAuth2Client } from "google-auth-library";
import { MoreThan, Repository } from "typeorm";
import {
  LoginUserDto,
  LoginUserResponseDto,
  LogoutUserDto,
  LogoutUserResponseDto,
  RefreshAccessTokenDto,
  RefreshAccessTokenResponseDto
} from "../dto/auth.dto";
import { Organization } from "../entities/organization.entity";
import { Password } from "../entities/password.entity";
import { RefreshToken } from "../entities/refresh-token.entity";
import { User } from "../entities/user.entity";
import { getHashPassword } from "../helper-functions";
import { AUTH_MODULE_OPTIONS, AuthModuleOptions, DecodedIdToken, LoginMethod } from "../types/auth.types";
import { PredefinedOrganizationName } from "../types/organization.types";
import { FIND_USER_RELATIONS, UserPermission } from "../types/user.types";

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(RefreshToken)
    private readonly refreshTokenRepository: Repository<RefreshToken>,
    @InjectRepository(Password)
    private readonly passwordRepository: Repository<Password>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Organization)
    private readonly organizationRepository: Repository<Organization>,
    @Inject(AUTH_MODULE_OPTIONS)
    private readonly authModuleOptions: AuthModuleOptions,
    @Inject(JwtService)
    private readonly jwtService: JwtService // private readonly dataSource: DataSource
  ) {}

  async verifyGoogleIdToken(idToken: string): Promise<DecodedIdToken> {
    const { googleOAuthClientId } = this.authModuleOptions;
    const googleClient = new OAuth2Client();
    const ticket = await googleClient.verifyIdToken({ idToken, audience: googleOAuthClientId });
    const googleUserId = ticket.getUserId();
    const { hd: domain, email, name } = ticket.getPayload();
    return {
      googleUserId,
      domain,
      email,
      name: name || email.split("@")[0]
    };
  }

  async validateToken(token: string, secret: string, ignoreExpiration = false) {
    const payload = this.jwtService.verify<{ uid: number }>(token, {
      secret,
      ignoreExpiration
    });
    return { uid: payload.uid };
  }

  async validateAccessToken(accessToken: string, ignoreExpiration = false) {
    return await this.validateToken(accessToken, this.authModuleOptions.accessTokenSecret, ignoreExpiration);
  }

  async validateRefreshToken(refreshToken: string, ignoreExpiration = false) {
    return await this.validateToken(
      refreshToken,
      this.authModuleOptions.refreshTokenSecret,
      ignoreExpiration
    );
  }

  // async validateApiKey(key: string) {
  //     if (typeof(key) !== 'string' || key.length !== 32) throw new BadRequestException('Invalid API key');
  //     const apiKeyRecord = await this.apiKeyRepository.createQueryBuilder('ak')
  //         .where(`ak.hashKey = encode(sha256((:key || ak.salt)::bytea), 'hex')`, { key })
  //         .leftJoinAndSelect('ak.user', 'user')
  //         .getOne();
  //     if (!apiKeyRecord) throw new NotFoundException('API Key not found');
  //     return apiKeyRecord;
  // }

  async isRefreshTokenExpired(refreshToken: string) {
    const { refreshTokenGracePeriodSec } = this.authModuleOptions;
    const refreshTokenRecord = await this.refreshTokenRepository.findOneBy({ refreshToken });
    if (!refreshTokenRecord) return true;
    const expiryDateWithGracePeriod = new Date(
      refreshTokenRecord.expiryDate.getTime() + refreshTokenGracePeriodSec * 1000
    );
    return expiryDateWithGracePeriod <= new Date();
  }

  async createTokens(user: User) {
    const currentSec = Math.floor(Date.now() / 1000);
    const { accessTokenExpiresInSec, refreshTokenExpiresInSec, accessTokenSecret, refreshTokenSecret } =
      this.authModuleOptions;
    const accessToken = this.jwtService.sign(
      {
        uid: user.id,
        exp: currentSec + accessTokenExpiresInSec,
        iat: currentSec
      },
      {
        secret: accessTokenSecret,
        algorithm: "HS256"
      }
    );
    const refreshToken = this.jwtService.sign(
      {
        uid: user.id,
        ran: randomBytes(8).toString("hex"),
        iat: currentSec
      },
      {
        secret: refreshTokenSecret,
        algorithm: "HS256"
      }
    );

    const newRefreshTokenRecord = new RefreshToken();
    newRefreshTokenRecord.refreshToken = refreshToken;
    newRefreshTokenRecord.expiryDate = new Date((currentSec + refreshTokenExpiresInSec) * 1000);
    newRefreshTokenRecord.issueDate = new Date(currentSec * 1000);
    newRefreshTokenRecord.user = user;
    await this.refreshTokenRepository.save(newRefreshTokenRecord);

    return {
      accessToken,
      accessTokenExpiryDate: new Date((currentSec + accessTokenExpiresInSec) * 1000),
      refreshToken,
      refreshTokenExpiryDate: new Date((currentSec + refreshTokenExpiresInSec) * 1000)
    };
  }

  async revokeRefreshToken(refreshToken: string) {
    await this.refreshTokenRepository.update(
      { refreshToken },
      {
        expiryDate: new Date(Date.now() - 1)
      }
    );
    return;
  }

  async revokeAllRefreshTokens(user: User) {
    const currentDate = new Date();
    await this.refreshTokenRepository.update(
      {
        user: { id: user.id },
        expiryDate: MoreThan(currentDate)
      },
      {
        expiryDate: new Date(currentDate.getTime() - 1)
      }
    );
    return;
  }

  async signInWithGoogleSSO(idToken: string, createBackofficeAdmin = false) {
    let decodedToken: DecodedIdToken | null = null;
    try {
      decodedToken = await this.verifyGoogleIdToken(idToken);
    } catch (error) {
      // console.error('verifyGoogleIdToken Error: ', error);
      throw new UnauthorizedException("Invalid ID Token");
    }

    let user = await this.userRepository.findOne({
      where: { email: decodedToken.email },
      relations: FIND_USER_RELATIONS
    });
    if (!user) {
      if (createBackofficeAdmin) {
        if (!["anteklogistics.com", "aistribute.com", "clarocustoms.com"].includes(decodedToken.domain))
          throw new UnauthorizedException("User not found");
        let antekOrg = await this.organizationRepository.findOneBy({
          name: PredefinedOrganizationName.ANTEK_LOGISTICS
        });
        if (!antekOrg)
          antekOrg = await this.organizationRepository.save(
            this.organizationRepository.create({ name: PredefinedOrganizationName.ANTEK_LOGISTICS })
          );
        user = new User();
        for (const [key, value] of Object.entries({
          name: decodedToken.name,
          email: decodedToken.email,
          googleUserId: decodedToken.googleUserId,
          permission: UserPermission.BACKOFFICE_ADMIN,
          organization: antekOrg
        }))
          user[key] = value;
        user = await this.userRepository.save(user);
      } else throw new UnauthorizedException("User not found");
    } else if (user.googleUserId !== decodedToken.googleUserId) {
      await this.userRepository.update({ id: user.id }, { googleUserId: decodedToken.googleUserId });
      user = await this.userRepository.findOne({
        where: { id: user.id },
        relations: FIND_USER_RELATIONS
      });
    }

    return {
      user,
      decodedToken
    };
  }

  async signInWithEmail(email: string, password: string) {
    const GENERIC_ERROR_MSG = "Invalid email or password";

    const user = await this.userRepository.findOne({
      where: { email },
      relations: FIND_USER_RELATIONS
    });
    if (!user) throw new UnauthorizedException(GENERIC_ERROR_MSG);

    const passwordRecord = await this.passwordRepository.findOneBy({ user: { id: user.id } });
    if (!passwordRecord) throw new UnauthorizedException(GENERIC_ERROR_MSG);

    const currentHashPassword = getHashPassword(password, passwordRecord.salt);
    if (currentHashPassword !== passwordRecord.hashPassword)
      throw new UnauthorizedException(GENERIC_ERROR_MSG);

    return user;
  }

  async loginUser(loginUserDto: LoginUserDto): Promise<LoginUserResponseDto> {
    const { allowedLoginMethods } = this.authModuleOptions;
    const { loginMethod, idToken, email, password } = loginUserDto;
    if (!allowedLoginMethods.includes(loginMethod))
      throw new UnauthorizedException(`Login Method ${loginMethod} is not allowed`);
    let user: User = null;
    switch (loginMethod) {
      case LoginMethod.GOOGLE_SSO:
        if (typeof idToken !== "string" || idToken.length <= 0)
          throw new UnauthorizedException("ID token is required for Google SSO");
        const { user: googleSsoUser } = await this.signInWithGoogleSSO(idToken, true);
        user = googleSsoUser;
        break;
      case LoginMethod.EMAIL:
        if (typeof email !== "string" || email.length <= 0)
          throw new UnauthorizedException("Email is required for email and password sign-in");
        if (typeof password !== "string" || password.length <= 0)
          throw new UnauthorizedException("Password is required for email and password sign-in");
        user = await this.signInWithEmail(email, password);
        break;
    }
    if (!user) throw new UnauthorizedException("User not found");
    const tokens = await this.createTokens(user);
    return {
      ...tokens,
      name: user.name,
      email: user.email,
      organization: user.organization,
      permission: user.permission
    };
  }

  async logoutUser(
    logoutUserDto: LogoutUserDto,
    authorizationHeader: string
  ): Promise<LogoutUserResponseDto> {
    const [bearer, accessToken] = authorizationHeader?.split(" ") ?? [];
    if (bearer !== "Bearer") throw new UnauthorizedException("Token type not bearer");

    let accessTokenUid: number | null = null,
      refreshTokenUid: number | null = null;
    try {
      const { uid: auid } = await this.validateAccessToken(accessToken, true);
      const { uid: ruid } = await this.validateRefreshToken(logoutUserDto.refreshToken, true);
      accessTokenUid = auid;
      refreshTokenUid = ruid;
    } catch (error) {
      throw new ForbiddenException("Invalid access token or refresh token");
    }

    const user = await this.userRepository.findOneBy({ id: refreshTokenUid });
    if (!user) throw new NotFoundException("User not found");
    if (accessTokenUid !== refreshTokenUid)
      throw new BadRequestException("Refresh token does not belong to current user");
    await this.revokeRefreshToken(logoutUserDto.refreshToken);

    return { userId: user.id };
  }

  async refreshAccessToken(
    refreshAccessTokenDto: RefreshAccessTokenDto
  ): Promise<RefreshAccessTokenResponseDto> {
    let payload: { uid: number } = null;
    try {
      payload = await this.validateRefreshToken(refreshAccessTokenDto.refreshToken);
    } catch (error) {
      throw new ForbiddenException("Invalid refresh token");
    }

    const user = await this.userRepository.findOne({
      where: { id: payload.uid },
      relations: FIND_USER_RELATIONS
    });
    if (!user) throw new NotFoundException("User not found");

    if (await this.isRefreshTokenExpired(refreshAccessTokenDto.refreshToken)) {
      await this.revokeAllRefreshTokens(user);
      throw new ForbiddenException("Refresh token expired");
    }

    const tokens = await this.createTokens(user);
    return {
      ...tokens,
      name: user.name,
      email: user.email,
      organization: user.organization,
      permission: user.permission
    };
  }
}
