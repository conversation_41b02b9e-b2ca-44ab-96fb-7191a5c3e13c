import { Body, Controller, HttpCode, Post, Req } from "@nestjs/common";
import {
  ApiBadRequestResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiUnauthorizedResponse
} from "@nestjs/swagger";
import { AuthService } from "./auth.service";
import {
  BadRequestResponseDto,
  ForbiddenResponseDto,
  NotFoundResponseDto,
  UnauthorizedResponseDto
} from "../dto/exception.dto";
import { UserService } from "../user/services/user.service";
import { OrganizationService } from "../user/services/organization.service";
import {
  LoginUserDto,
  LoginUserResponseDto,
  LogoutUserDto,
  LogoutUserResponseDto,
  RefreshAccessTokenDto,
  RefreshAccessTokenResponseDto
} from "../dto/auth.dto";
import { ApiAccessTokenAuthenticated } from "../decorators/api-docs";
import { Request } from "express";

@ApiTags("Auth API")
@Controller("auth")
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly userService: UserService,
    private readonly organizationService: OrganizationService
  ) {}

  @Post("login")
  @HttpCode(200)
  @ApiOperation({ summary: "Login User" })
  @ApiUnauthorizedResponse({ type: UnauthorizedResponseDto })
  @ApiOkResponse({ type: LoginUserResponseDto })
  async login(@Body() loginUserDto: LoginUserDto) {
    return await this.authService.loginUser(loginUserDto);
  }

  @ApiAccessTokenAuthenticated()
  @Post("logout")
  @HttpCode(200)
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiOkResponse({ type: LogoutUserResponseDto })
  async logout(@Req() req: Request, @Body() logoutUserDto: LogoutUserDto) {
    return await this.authService.logoutUser(logoutUserDto, req.headers?.authorization);
  }

  @Post("token")
  @HttpCode(200)
  @ApiOperation({ summary: "Refresh Access Token" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiOkResponse({ type: RefreshAccessTokenResponseDto })
  async refreshAccessToken(@Body() refreshAccessTokenDto: RefreshAccessTokenDto) {
    return await this.authService.refreshAccessToken(refreshAccessTokenDto);
  }
}
