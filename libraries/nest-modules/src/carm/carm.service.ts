import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  Scope
} from "@nestjs/common";
import axios, { AxiosError } from "axios";
import https from "https";
import moment from "moment-timezone";
import {
  CarmErrorResponseDto,
  DutiesAndTaxesCalculatorDto,
  DutiesAndTaxesCalculatorResponseDto,
  GetIndividualTariffNumberCustomsDutiesDto,
  GetIndividualTariffNumberCustomsDutiesResponseDto,
  GetIndividualTariffNumberDto,
  GetIndividualTariffNumberResponseDto,
  GetListOfCustomsDutyRecordsDto,
  GetListOfCustomsDutyRecordsResponseDto,
  GetListOfTariffNumbersDto,
  GetListOfTariffNumbersResponseDto
} from "../dto";
import { TooManyRequestsException } from "../exceptions";
import { parseXml, toXml } from "../helper-functions";
import {
  CARM_MODULE_OPTIONS,
  CarmErrorCode,
  CarmErrorResponse,
  CarmModuleOptions,
  CarmRequestResponse,
  CarmStatusResponse
} from "../types";

@Injectable({ scope: Scope.REQUEST })
export class CarmService {
  constructor(
    @Inject(CARM_MODULE_OPTIONS)
    private readonly options: CarmModuleOptions
  ) {}
  private readonly logger = new Logger(CarmService.name);
  static readonly CARM_BASE_URL = `https://ccapi-ipacc.cbsa-asfc.cloud-nuage.canada.ca/v1`;
  static readonly CARM_REQUEST_BASE_URL = "https://claro.ngrok.io/api";

  static getCarmDeclarationDateString(date: Date) {
    return moment(date).tz("America/Toronto").format("YYYYMMDD");
  }

  static getCarmTariffDateString(date: Date) {
    return moment(date).tz("America/Toronto").format("YYYY-MM-DDTHH:mm:ss");
  }

  static toCarmDeclarationDate(dateString: string) {
    return moment.tz(dateString, "YYYYMMDD", "America/Toronto").toDate();
  }

  static toCarmTariffDate(dateString: string) {
    return moment.tz(dateString, "YYYY-MM-DDTHH:mm:ss", "America/Toronto").toDate();
  }

  private parseCarmXml<T>(xml: string, withAttributes = false) {
    return parseXml<T>(xml, withAttributes, (tagName, tagValue) =>
      ["TariffNumber", "TariffItemNumber"].includes(tagName) ? null : tagValue
    );
  }

  private handleCarmError(error: any, defaultErrorMessage: string) {
    if (typeof error.response?.status === "number" && error.response?.data) {
      switch (error.response.status) {
        case 409:
          if (
            parseXml<CarmErrorResponseDto>(error.response.data)?.error?.code ===
            CarmErrorCode.NO_DATA_SELECTED
          )
            throw new NotFoundException("No data selected");
          break;
        case 429:
          throw new TooManyRequestsException("Rate limit exceeded");
      }
    }
    throw new InternalServerErrorException(defaultErrorMessage);
  }

  private handleCarmRequestError(error: AxiosError) {
    if (error.response) {
      const { response } = error;

      this.logger.error("CARM Request error", {
        status: response.status,
        statusText: response.statusText,
        data: response.data
      });

      if (response.status === 400) {
        throw new BadRequestException((response.data as CarmErrorResponse).message);
      } else if (response.status === 404) {
        throw new NotFoundException((response.data as CarmErrorResponse).message);
      } else {
        throw new InternalServerErrorException((response.data as CarmErrorResponse).message);
      }
    } else if (!error.response) {
      throw new InternalServerErrorException(
        `Unable to reach CARM service – ${error.message ?? "network failure"}`
      );
    }
    throw new InternalServerErrorException("Unknown CARM request error");
  }

  private async carmRequest(url: string, headers?: any, timeout = 150000) {
    this.logger.log(`CARM app id: ${this.options.appId}`);
    try {
      return await axios.get(url, {
        headers: {
          "ngrok-skip-browser-warning": "true",
          "x-app-id": this.options.appId,
          ...headers
        },
        timeout
      });
    } catch (error) {
      this.handleCarmRequestError(error as AxiosError);
    }
  }

  async getRequestStatus(): Promise<CarmStatusResponse[]> {
    const result = await this.carmRequest(`${CarmService.CARM_REQUEST_BASE_URL}/requestStatus`);
    return result?.data;
  }

  async sendRequest(businessNo: string): Promise<CarmRequestResponse> {
    const result = await this.carmRequest(
      `${CarmService.CARM_REQUEST_BASE_URL}/carmRequest?businessNo=${businessNo}`
    );
    return result?.data;
  }

  // async checkCarmStatus() {
  //   try {
  //     // Get onboarding doc that's poa signed and carm status is pending or sent
  //     const docs = await this.firebaseService.getOnboarding([
  //       ["poaStatus", "==", PoaStatus.SIGNED],
  //       ["carmStatus", "in", [CarmStatus.PENDING, CarmStatus.SENT]]
  //     ]);
  //     console.log("docs", docs);

  //     const requestStatus = await this.getRequestStatus();
  //     console.log("carmRequest", requestStatus);

  //     // Filter onboarding docs that have a matching business number and carm status is approved
  //     const approved = docs.filter((item) =>
  //       requestStatus.some(
  //         (request) =>
  //           request.business_number === item.importer.businessNumber.slice(0, 9) &&
  //           request.status === CarmStatus.APPROVED
  //       )
  //     );
  //     console.log("approved", approved);
  //     // Update onboarding doc with carm status active
  //     if (approved.length > 0) {
  //       for (const doc of approved) {
  //         await this.firebaseService.updateOnboarding(doc.id, {
  //           carmStatus: CarmStatus.ACTIVE
  //         });
  //       }
  //     }
  //   } catch (error) {
  //     this.logger.error("Error in checkCarmStatus", error);
  //     throw new InternalServerErrorException(error);
  //   }
  // }

  // private toXmlRequest<T>(dto: T) {
  //   const xml = new XMLBuilder({
  //     textNodeName: 'TextValue',
  //     attributeNamePrefix: '$'
  //   }).build(dto);
  //   return xml as string;
  // }

  // private parseXmlResponse<T>(resXml: string, withAttributes = false): T {
  //   function convertEmptyStringToNull(currentValue: any) {
  //     if (typeof (currentValue) === 'string' && currentValue === '') return null;
  //     else if (typeof (currentValue) === 'object') {
  //       if (Array.isArray(currentValue)) return currentValue.map(convertEmptyStringToNull);
  //       else return Object.entries(currentValue).reduce((obj, [key, value]) => {
  //         obj[key] = convertEmptyStringToNull(value);
  //         return obj;
  //       }, {});
  //     }
  //     else return currentValue;
  //   }

  //   const resJson = new XMLParser({
  //     removeNSPrefix: true,
  //     ignoreAttributes: !withAttributes,
  //     textNodeName: 'TextValue',
  //     attributeNamePrefix: '$',
  //     tagValueProcessor(tagName, tagValue, jPath, hasAttributes, isLeafNode) {
  //       if (['TariffNumber', 'TariffItemNumber'].includes(tagName)) return null;
  //       else return tagValue;
  //     },
  //   }).parse(resXml);
  //   return convertEmptyStringToNull(resJson) as T;
  // }

  async getListOfTariffNumbers(getListOfTariffNumbersDto: GetListOfTariffNumbersDto) {
    try {
      const res = await axios.get(`${CarmService.CARM_BASE_URL}/tariff-srv/tariffClassifications`, {
        params: getListOfTariffNumbersDto,
        headers: {
          Accept: "application/xml",
          "Accept-Language": "EN"
        },
        // TODO: remove this after testing
        httpsAgent: new https.Agent({
          rejectUnauthorized: false
        })
      });
      return this.parseCarmXml<GetListOfTariffNumbersResponseDto>(res.data);
    } catch (error: any) {
      this.logger.error(`Got error while getting list of tariff numbers:`);
      this.logger.error(error);
      this.handleCarmError(error, `Error while getting list of tariff numbers`);
    }
  }

  async getIndividualTariffNumber(getIndividualTariffNumberDto: GetIndividualTariffNumberDto) {
    try {
      const { TariffNumber, AsOfDate, ...queryParams } = getIndividualTariffNumberDto;
      const res = await axios.get(
        `${CarmService.CARM_BASE_URL}/tariff-srv/tariffClassifications(TariffNumber='${TariffNumber}',AsOfDate=datetime'${AsOfDate}')`,
        {
          params: queryParams,
          headers: {
            Accept: "application/xml",
            "Accept-Language": "EN"
          },
          // TODO: remove this after testing
          httpsAgent: new https.Agent({
            rejectUnauthorized: false
          })
        }
      );
      return this.parseCarmXml<GetIndividualTariffNumberResponseDto>(res.data);
    } catch (error: any) {
      this.logger.error(`Got error while getting individual tariff number:`);
      this.logger.error(error);
      this.handleCarmError(error, `Error while getting individual tariff number`);
    }
  }

  async getIndividualTariffNumberCustomsDuties(
    getIndividualTariffNumberCustomsDutiesDto: GetIndividualTariffNumberCustomsDutiesDto
  ) {
    try {
      const { TariffNumber, AsOfDate, ...queryParams } = getIndividualTariffNumberCustomsDutiesDto;
      const res = await axios.get(
        `${CarmService.CARM_BASE_URL}/tariff-srv/tariffClassifications(TariffNumber='${TariffNumber}',AsOfDate=datetime'${AsOfDate}')/to_customsDuties`,
        {
          params: queryParams,
          headers: {
            Accept: "application/xml",
            "Accept-Language": "EN"
          },
          // TODO: remove this after testing
          httpsAgent: new https.Agent({
            rejectUnauthorized: false
          })
        }
      );
      return this.parseCarmXml<GetIndividualTariffNumberCustomsDutiesResponseDto>(res.data);
    } catch (error) {
      this.logger.error(`Got error while getting individual tariff number customs duties:`);
      this.logger.error(error);
      this.handleCarmError(error, `Error while getting individual tariff number customs duties`);
    }
  }

  async getListOfCustomsDutyRecords(
    getListOfCustomsDutyRecordsDto: GetListOfCustomsDutyRecordsDto
  ): Promise<GetListOfCustomsDutyRecordsResponseDto> {
    try {
      const res = await axios.get(`${CarmService.CARM_BASE_URL}/tariff-srv/customsDuties`, {
        params: getListOfCustomsDutyRecordsDto,
        headers: {
          Accept: "application/xml",
          "Accept-Language": "EN"
        },
        // TODO: remove this after testing
        httpsAgent: new https.Agent({
          rejectUnauthorized: false
        })
      });
      return this.parseCarmXml<GetListOfCustomsDutyRecordsResponseDto>(res.data);
    } catch (error) {
      this.logger.error(`Got error while getting list of customs duty records:`);
      this.logger.error(error);
      this.handleCarmError(error, `Error while getting list of customs duty records`);
    }
  }

  async dutiesAndTaxesCalculator(dutiesAndTaxesCalculatorDto: DutiesAndTaxesCalculatorDto) {
    try {
      const res = await axios.post(
        `${CarmService.CARM_BASE_URL}/duty-srv/dutyTaxCalculator`,
        toXml(dutiesAndTaxesCalculatorDto),
        {
          headers: {
            Accept: "application/xml",
            "Content-Type": "application/xml"
          },
          // TODO: remove this after testing
          httpsAgent: new https.Agent({
            rejectUnauthorized: false
          })
        }
      );
      return this.parseCarmXml<DutiesAndTaxesCalculatorResponseDto>(res.data, true);
    } catch (error: any) {
      this.logger.error(`Got error while calculating duties and taxes:`);
      this.logger.error(error);
      if (error.response) {
        this.logger.error(`Status code: ${error.response?.status}`);
        this.logger.error(`Response body: ${JSON.stringify(error.response?.data)}`);
      }
      this.handleCarmError(error, `Error while calculating duties and taxes`);
    }
  }
}
