import { DynamicModule, Global, Module } from "@nestjs/common";
import { CARM_MODULE_OPTIONS, CarmModuleOptions } from "../types/carm.types";
import { CarmService } from "./carm.service";
import { CarmController } from "./carm.controller";

@Module({})
export class CarmModule {
  static register(options?: CarmModuleOptions): DynamicModule {
    return {
      global: true,
      module: CarmModule,
      providers: [
        CarmService,
        {
          provide: CARM_MODULE_OPTIONS,
          useValue: options
        }
      ],
      controllers: [CarmController],
      exports: [CarmService]
    };
  }
}
