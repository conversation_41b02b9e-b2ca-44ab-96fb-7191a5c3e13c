import { Body, Controller, Post, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiTags } from "@nestjs/swagger";
import { ApiAccessTokenAuthenticated, ApiCreateResponses } from "../decorators";
import { CarmRequestParamsDto, CarmRequestResponseDto } from "../dto";
import { AccessTokenGuard } from "../guards";
import { CarmService } from "./carm.service";

@ApiTags("Carm API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("carm")
export class CarmController {
  constructor(private readonly carmService: CarmService) {}

  @Post("/request")
  @ApiOperation({ summary: "Send Request" })
  @ApiCreateResponses({ type: CarmRequestResponseDto })
  async sendRequest(@Body() { businessNo }: CarmRequestParamsDto) {
    return await this.carmService.sendRequest(businessNo);
  }
}
