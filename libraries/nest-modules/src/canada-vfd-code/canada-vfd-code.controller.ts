import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseGuards,
  NotFoundException,
  Post,
  Body,
  Put,
  Delete,
  HttpCode
} from "@nestjs/common";
import { AccessTokenGuard } from "../guards";
import {
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses
} from "../decorators";
import { ApiForbiddenResponse, ApiOperation, ApiParam } from "@nestjs/swagger";
import { ApiTags } from "@nestjs/swagger";
import {
  BatchUpdateCanadaVfdCodesDto,
  BatchUpdateCanadaVfdCodesResponseDto,
  CreateCanadaVfdCodeDto,
  EditCanadaVfdCodeDto,
  ForbiddenResponseDto,
  GetCanadaVfdCodesDto,
  GetCanadaVfdCodesResponseDto
} from "../dto";
import { CanadaVfdCodeService } from "./canada-vfd-code.service";
import { CanadaVfdCode } from "../entities";

@ApiTags("Canada VFD Code API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("canada-vfd-codes")
export class CanadaVfdCodeController {
  constructor(private readonly canadaVfdCodeService: CanadaVfdCodeService) {}

  @Get()
  @ApiOperation({ summary: "Get Canada VFD Codes" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: GetCanadaVfdCodesResponseDto })
  async getCanadaVfdCodes(@Query() getCanadaVfdCodesDto: GetCanadaVfdCodesDto) {
    return await this.canadaVfdCodeService.getCanadaVfdCodes(getCanadaVfdCodesDto);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get Canada VFD Code by ID" })
  @ApiParam({ name: "id", type: "integer", description: "Canada VFD Code ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: CanadaVfdCode })
  async getCanadaVfdCodeById(@Param("id", ParseIntPipe) id: number) {
    const code = await this.canadaVfdCodeService.getCanadaVfdCodeById(id);
    if (!code) throw new NotFoundException("Canada VFD Code not found");
    return code;
  }

  @Post()
  @ApiOperation({ summary: "Create Canada VFD Code" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiCreateResponses({ type: CanadaVfdCode })
  async createCanadaVfdCode(@Body() createCanadaVfdCodeDto: CreateCanadaVfdCodeDto) {
    return await this.canadaVfdCodeService.createCanadaVfdCode(createCanadaVfdCodeDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update Canada VFD Code" })
  @ApiParam({ name: "id", type: "integer", description: "Canada VFD Code ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: CanadaVfdCode })
  async editCanadaVfdCode(
    @Param("id", ParseIntPipe) id: number,
    @Body() editCanadaVfdCodeDto: EditCanadaVfdCodeDto
  ) {
    return await this.canadaVfdCodeService.editCanadaVfdCode(id, editCanadaVfdCodeDto);
  }

  @HttpCode(204)
  @Delete(":id")
  @ApiOperation({ summary: "Delete Canada VFD Code" })
  @ApiParam({ name: "id", type: "integer", description: "Canada VFD Code ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiDeleteResponses()
  async deleteCanadaVfdCode(@Param("id", ParseIntPipe) id: number) {
    await this.canadaVfdCodeService.deleteCanadaVfdCode(id);
    return;
  }

  @HttpCode(200)
  @Post("batch")
  @ApiOperation({ summary: "Batch update Canada VFD Codes" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: BatchUpdateCanadaVfdCodesResponseDto })
  async batchUpdateCanadaVfdCodes(@Body() batchUpdateCanadaVfdCodesDto: BatchUpdateCanadaVfdCodesDto) {
    return await this.canadaVfdCodeService.batchUpdateCanadaVfdCodes(batchUpdateCanadaVfdCodesDto);
  }
}
