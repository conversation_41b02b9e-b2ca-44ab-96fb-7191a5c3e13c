import { DynamicModule, Module } from "@nestjs/common";
import { CANADA_VFD_CODE_MODULE_OPTIONS, CrudOptions } from "../types";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CanadaVfdCodeController } from "./canada-vfd-code.controller";
import { CanadaVfdCode } from "../entities";
import { CanadaVfdCodeService } from "./canada-vfd-code.service";

@Module({})
export class CanadaVfdCodeModule {
  static register(options: CrudOptions): DynamicModule {
    return {
      global: true,
      module: CanadaVfdCodeModule,
      imports: [TypeOrmModule.forFeature([CanadaVfdCode])],
      controllers: [CanadaVfdCodeController],
      providers: [
        {
          provide: CANADA_VFD_CODE_MODULE_OPTIONS,
          useValue: options
        },
        CanadaVfdCodeService
      ],
      exports: [CanadaVfdCodeService]
    };
  }
}
