import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import { DataSource, In, Not, QueryRunner, Repository } from "typeorm";
import {
  BatchUpdateCanadaVfdCodesDto,
  BatchUpdateCanadaVfdCodesResponseDto,
  CreateCanadaVfdCodeDto,
  EditCanadaVfdCodeDto,
  GetCanadaVfdCodesDto,
  GetCanadaVfdCodesResponseDto
} from "../dto";
import { CanadaVfdCode } from "../entities";
import { convertFromCamelCase, getFindOptions } from "../helper-functions";
import { MatchingRuleService } from "../matching-rule";
import {
  AuthenticatedRequest,
  CANADA_VFD_CODE_MODULE_OPTIONS,
  CANADA_VFD_CODE_REQUIRED_KEYS,
  CanadaVfdCodeColumn,
  CrudOptions,
  FIND_CANADA_VFD_CODE_RELATIONS,
  MatchingRuleSourceDatabaseTable,
  UserPermission
} from "../types";

@Injectable({ scope: Scope.REQUEST })
export class CanadaVfdCodeService {
  constructor(
    @InjectRepository(CanadaVfdCode)
    private readonly canadaVfdCodeRepository: Repository<CanadaVfdCode>,
    @Inject(MatchingRuleService)
    private readonly matchingRuleService: MatchingRuleService,
    @Inject(CANADA_VFD_CODE_MODULE_OPTIONS)
    private readonly options: CrudOptions,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}

  async getCanadaVfdCodes(getCanadaVfdCodesDto: GetCanadaVfdCodesDto): Promise<GetCanadaVfdCodesResponseDto> {
    if (!this.options.readMany) throw new ForbiddenException("Get Canada VFD Codes is disabled");
    const { where, order, skip, take } = getFindOptions(getCanadaVfdCodesDto, [], [], CanadaVfdCodeColumn.id);
    const [data, total] = await this.canadaVfdCodeRepository.findAndCount({
      where,
      order,
      relations: FIND_CANADA_VFD_CODE_RELATIONS,
      skip,
      take
    });
    return {
      data,
      total,
      skip,
      limit: take
    };
  }

  async getCanadaVfdCodeById(codeId: number, queryRunner?: QueryRunner) {
    if (!this.options.readOne) throw new ForbiddenException("Get Canada VFD Code is disabled");
    const canadaVfdCodeRepository = queryRunner
      ? queryRunner.manager.getRepository(CanadaVfdCode)
      : this.canadaVfdCodeRepository;
    const canadaVfdCode = await canadaVfdCodeRepository.findOne({
      where: { id: codeId },
      relations: FIND_CANADA_VFD_CODE_RELATIONS
    });
    return canadaVfdCode;
  }

  async createCanadaVfdCode(createCanadaVfdCodeDto: CreateCanadaVfdCodeDto) {
    if (!this.options.create) throw new ForbiddenException("Create Canada VFD Code is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to create Canada VFD Codes");
    let newCanadaVfdCode = new CanadaVfdCode();
    newCanadaVfdCode.createdBy = this.request?.user || null;
    newCanadaVfdCode.lastEditedBy = this.request?.user || null;
    for (const [key, value] of Object.entries(createCanadaVfdCodeDto)) {
      if (value === undefined) continue;
      if (CANADA_VFD_CODE_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      newCanadaVfdCode[key] = value;
    }
    if (await this.canadaVfdCodeRepository.existsBy({ code: newCanadaVfdCode.code }))
      throw new BadRequestException("Canada VFD Code with the same code already exists");

    newCanadaVfdCode = await this.canadaVfdCodeRepository.save(newCanadaVfdCode);

    return await this.getCanadaVfdCodeById(newCanadaVfdCode.id);
  }

  async editCanadaVfdCode(codeId: number, editCanadaVfdCodeDto: EditCanadaVfdCodeDto) {
    if (!this.options.update) throw new ForbiddenException("Edit Canada VFD Code is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to edit Canada VFD Codes");
    const canadaVfdCode = await this.getCanadaVfdCodeById(codeId);
    if (!canadaVfdCode) throw new NotFoundException("Canada VFD Code not found");
    canadaVfdCode.lastEditedBy = this.request?.user || null;
    for (const [key, value] of Object.entries(editCanadaVfdCodeDto)) {
      if (value === undefined) continue;
      if (CANADA_VFD_CODE_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      canadaVfdCode[key] = value;
    }
    if (
      await this.canadaVfdCodeRepository.existsBy({
        id: Not(codeId),
        code: canadaVfdCode.code
      })
    )
      throw new BadRequestException("Canada VFD Code with the same code already exists");
    await this.canadaVfdCodeRepository.save(canadaVfdCode);
    return await this.getCanadaVfdCodeById(codeId);
  }

  async deleteCanadaVfdCode(codeId: number) {
    if (!this.options.delete) throw new ForbiddenException("Delete Canada VFD Code is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to delete Canada VFD Codes");
    if (!(await this.canadaVfdCodeRepository.existsBy({ id: codeId })))
      throw new NotFoundException("Canada VFD Code not found");
    if (
      await this.matchingRuleService.isMatchingRuleForSourceRecordExists(
        MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE,
        codeId
      )
    )
      throw new BadRequestException("Canada VFD Code is being used in a matching rule");
    await this.canadaVfdCodeRepository.delete({ id: codeId });
    return;
  }

  async batchUpdateCanadaVfdCodes(
    batchUpdateCanadaVfdCodesDto: BatchUpdateCanadaVfdCodesDto
  ): Promise<BatchUpdateCanadaVfdCodesResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const savedCanadaVfdCodeIds: Array<number> = [];
    try {
      const { create: createList, edit: editList, delete: deleteList } = batchUpdateCanadaVfdCodesDto;

      if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
        throw new ForbiddenException("You are not authorized to batch update Canada VFD Codes");
      if (Array.isArray(createList) && createList.length > 0 && !this.options.create)
        throw new ForbiddenException("Create Canada VFD Code is disabled");
      if (Array.isArray(editList) && editList.length > 0 && !this.options.update)
        throw new ForbiddenException("Edit Canada VFD Code is disabled");
      if (Array.isArray(deleteList) && deleteList.length > 0 && !this.options.delete)
        throw new ForbiddenException("Delete Canada VFD Code is disabled");

      const canadaVfdCodeRepository = queryRunner.manager.getRepository(CanadaVfdCode);
      const toBeSavedCanadaVfdCodes: Array<CanadaVfdCode> = [];

      for (let i = 0; i < (createList || []).length; i++) {
        const errorMsgPrefix = `Error on create list index ${i}: `;
        const createCanadaVfdCodeDto = createList[i];
        let newCanadaVfdCode = new CanadaVfdCode();
        newCanadaVfdCode.createdBy = this.request?.user || null;
        newCanadaVfdCode.lastEditedBy = this.request?.user || null;
        for (const [key, value] of Object.entries(createCanadaVfdCodeDto)) {
          if (value === undefined) continue;
          if (CANADA_VFD_CODE_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
          newCanadaVfdCode[key] = value;
        }
        if (
          toBeSavedCanadaVfdCodes.some((c) => c.code === newCanadaVfdCode.code) ||
          (await canadaVfdCodeRepository.existsBy({
            code: newCanadaVfdCode.code
          }))
        )
          throw new BadRequestException(`${errorMsgPrefix}Canada VFD Code with the same code already exists`);

        toBeSavedCanadaVfdCodes.push(newCanadaVfdCode);
      }

      for (let i = 0; i < (editList || []).length; i++) {
        const errorMsgPrefix = `Error on edit list index ${i}: `;
        const { id: codeId, ...editCanadaVfdCodeDto } = editList[i];
        const canadaVfdCode = await this.getCanadaVfdCodeById(codeId, queryRunner);
        if (!canadaVfdCode) throw new NotFoundException(`${errorMsgPrefix}Canada VFD Code not found`);
        canadaVfdCode.lastEditedBy = this.request?.user || null;
        for (const [key, value] of Object.entries(editCanadaVfdCodeDto)) {
          if (value === undefined) continue;
          if (CANADA_VFD_CODE_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
          canadaVfdCode[key] = value;
        }
        if (
          toBeSavedCanadaVfdCodes.some((c) => c.code === canadaVfdCode.code && c.id !== canadaVfdCode.id) ||
          (await canadaVfdCodeRepository.existsBy({
            id: Not(
              In(
                toBeSavedCanadaVfdCodes
                  .filter((c) => typeof c.id === "number")
                  .map((c) => c.id)
                  .concat([codeId])
              )
            ),
            code: canadaVfdCode.code
          }))
        )
          throw new BadRequestException(`${errorMsgPrefix}Canada VFD Code with the same code already exists`);
        toBeSavedCanadaVfdCodes.push(canadaVfdCode);
      }

      for (let i = 0; i < (deleteList || []).length; i++) {
        const codeId = deleteList[i];
        const errorMsgPrefix = `Error on delete list index ${i}: `;
        if (!(await canadaVfdCodeRepository.existsBy({ id: codeId })))
          throw new NotFoundException(`${errorMsgPrefix}Canada VFD Code not found`);
        if (toBeSavedCanadaVfdCodes.some((c) => c.id === codeId))
          throw new BadRequestException(`${errorMsgPrefix}Canada VFD Code is being edited`);
        if (
          await this.matchingRuleService.isMatchingRuleForSourceRecordExists(
            MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE,
            codeId,
            queryRunner
          )
        )
          throw new BadRequestException(`${errorMsgPrefix}Canada VFD Code is being used in a matching rule`);
      }

      savedCanadaVfdCodeIds.push(
        ...(await canadaVfdCodeRepository.save(toBeSavedCanadaVfdCodes)).map((c) => c.id)
      );
      if (Array.isArray(deleteList) && deleteList.length > 0)
        await canadaVfdCodeRepository.delete({ id: In(deleteList) });

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner.isReleased) queryRunner.release();
    }

    return {
      data: await this.canadaVfdCodeRepository.find({
        where: { id: In(savedCanadaVfdCodeIds) },
        relations: FIND_CANADA_VFD_CODE_RELATIONS
      })
    };
  }
}
