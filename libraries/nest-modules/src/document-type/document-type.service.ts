import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { DataSource, FindOptionsRelations, Repository } from "typeorm";
import { Query<PERSON>un<PERSON> } from "typeorm/browser";
import {
  CreateDocumentTypeDto,
  EditDocumentTypeDto,
  GetDocumentTypesDto,
  GetDocumentTypesResponseDto
} from "../dto";
import { DocumentType } from "../entities";
import { convertFromCamelCase, getFindOptions } from "../helper-functions";
import {
  CrudOptions,
  DOCUMENT_TYPE_ENUM_KEYS,
  DOCUMENT_TYPE_MODULE_OPTIONS,
  DOCUMENT_TYPE_REQUIRED_KEYS,
  DocumentTypeColumn,
  FIND_DOCUMENT_TYPE_RELATIONS
} from "../types";

@Injectable()
export class DocumentTypeService {
  constructor(
    @InjectRepository(DocumentType)
    private readonly documentTypeRepository: Repository<DocumentType>,
    @Inject(DOCUMENT_TYPE_MODULE_OPTIONS)
    private readonly options: CrudOptions,
    // @Inject(REQUEST)
    // private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}

  async getDocumentTypes(getDocumentTypesDto: GetDocumentTypesDto): Promise<GetDocumentTypesResponseDto> {
    if (!this.options.readMany) throw new ForbiddenException("Get Document Types is disabled");
    const { where, order, take, skip } = getFindOptions(
      getDocumentTypesDto,
      DOCUMENT_TYPE_ENUM_KEYS,
      [],
      DocumentTypeColumn.id
    );
    const [documentTypes, total] = await this.documentTypeRepository.findAndCount({
      where,
      order,
      relations: FIND_DOCUMENT_TYPE_RELATIONS,
      take,
      skip
    });
    return { documentTypes, total, skip, limit: take };
  }

  async getDocumentTypeById(documentTypeId: number, queryRunner?: QueryRunner) {
    if (!this.options.readOne) throw new ForbiddenException("Get Document Type by ID is disabled");
    return await (
      queryRunner ? queryRunner.manager.getRepository(DocumentType) : this.documentTypeRepository
    ).findOne({
      where: { id: documentTypeId },
      relations: FIND_DOCUMENT_TYPE_RELATIONS
    });
  }

  async createDocumentType(createDocumentTypeDto: CreateDocumentTypeDto) {
    if (!this.options.create) throw new ForbiddenException("Create Document Type is disabled");
    if (
      await this.documentTypeRepository.existsBy({
        name: createDocumentTypeDto.name
      })
    )
      throw new BadRequestException("Document Type with the same name already exists");
    let newDocType = new DocumentType();
    for (const [key, value] of Object.entries({
      ...createDocumentTypeDto,
      createdBy: null,
      lastEditedBy: null
    })) {
      if (value === undefined) continue;
      if (DOCUMENT_TYPE_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      newDocType[key] = value;
    }
    newDocType = await this.documentTypeRepository.save(newDocType);
    return await this.getDocumentTypeById(newDocType.id);
  }

  async editDocumentType(documentTypeId: number, editDocumentTypeDto: EditDocumentTypeDto) {
    if (!this.options.update) throw new ForbiddenException("Edit Document Type is disabled");
    let docType = await this.getDocumentTypeById(documentTypeId);
    if (!docType) throw new NotFoundException("Document Type not found");
    for (const [key, value] of Object.entries({
      ...editDocumentTypeDto,
      lastEditedBy: null
    })) {
      if (value === undefined) continue;
      if (DOCUMENT_TYPE_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      docType[key] = value;
    }
    docType = await this.documentTypeRepository.save(docType);
    return await this.getDocumentTypeById(docType.id);
  }

  async deleteDocumentType(documentTypeId: number) {
    if (!this.options.delete) throw new ForbiddenException("Delete Document Type is disabled");
    if (!(await this.documentTypeRepository.existsBy({ id: documentTypeId })))
      throw new NotFoundException("Document Type not found");
    await this.documentTypeRepository.delete({ id: documentTypeId });
    return;
  }

  async getAllDocumentTypes(relations?: FindOptionsRelations<DocumentType>): Promise<DocumentType[]> {
    return await this.documentTypeRepository.find({
      relations
    });
  }
}
