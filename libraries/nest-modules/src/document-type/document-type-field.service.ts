import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { DataSource, In, Not, QueryRunner, Repository } from "typeorm";
import { DocumentTypeField } from "../entities";
import {
  AuthenticatedRequest,
  DOCUMENT_TYPE_MODULE_OPTIONS,
  DOCUMENT_TYPE_FIELD_REQUIRED_KEYS,
  DocumentTypeFieldColumn,
  FIND_DOCUMENT_TYPE_FIELD_RELATIONS,
  UserPermission,
  DOCUMENT_TYPE_FIELD_ENUM_KEYS
} from "../types";
import { CrudOptions } from "../types";
import { REQUEST } from "@nestjs/core";
import {
  BatchUpdateDocumentTypeFieldsDto,
  BatchUpdateDocumentTypeFieldsResponseDto,
  CreateDocumentTypeFieldDto,
  EditDocumentType<PERSON>ieldDto,
  GetDocumentTypeFieldsDto,
  GetDocumentTypeFieldsResponseDto
} from "../dto";
import { convertFromCamelCase, getFindOptions } from "../helper-functions";
import { DocumentTypeService } from "./document-type.service";

@Injectable({ scope: Scope.REQUEST })
export class DocumentTypeFieldService {
  constructor(
    @InjectRepository(DocumentTypeField)
    private readonly documentTypeFieldRepository: Repository<DocumentTypeField>,
    @Inject(DocumentTypeService)
    private readonly documentTypeService: DocumentTypeService,
    @Inject(DOCUMENT_TYPE_MODULE_OPTIONS)
    private readonly options: CrudOptions,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}

  async getDocumentTypeFields(
    documentTypeId: number,
    getDocumentTypeFieldsDto: GetDocumentTypeFieldsDto
  ): Promise<GetDocumentTypeFieldsResponseDto> {
    if (!this.options.readMany) throw new ForbiddenException("Read Document Type Fields is disabled");
    if (!(await this.documentTypeService.getDocumentTypeById(documentTypeId)))
      throw new NotFoundException("Document Type not found");
    const { where, order, take, skip } = getFindOptions(
      { ...getDocumentTypeFieldsDto, documentTypeId },
      DOCUMENT_TYPE_FIELD_ENUM_KEYS,
      [],
      DocumentTypeFieldColumn.id
    );
    const [documentTypeFields, total] = await this.documentTypeFieldRepository.findAndCount({
      where,
      order,
      relations: FIND_DOCUMENT_TYPE_FIELD_RELATIONS,
      take,
      skip
    });
    return { documentTypeFields, total, skip, limit: take };
  }

  async getDocumentTypeFieldById(documentTypeId: number, fieldId: number, queryRunner?: QueryRunner) {
    if (!this.options.readOne) throw new ForbiddenException("Read Document Type Field is disabled");
    if (!(await this.documentTypeService.getDocumentTypeById(documentTypeId, queryRunner)))
      throw new NotFoundException("Document Type not found");
    return await (
      queryRunner ? queryRunner.manager.getRepository(DocumentTypeField) : this.documentTypeFieldRepository
    ).findOne({
      where: { id: fieldId, documentType: { id: documentTypeId } },
      relations: FIND_DOCUMENT_TYPE_FIELD_RELATIONS
    });
  }

  async createDocumentTypeField(
    documentTypeId: number,
    createDocumentTypeFieldDto: CreateDocumentTypeFieldDto
  ) {
    if (!this.options.create) throw new ForbiddenException("Create Document Type Field is disabled");
    const documentType = await this.documentTypeService.getDocumentTypeById(documentTypeId);
    if (!documentType) throw new NotFoundException("Document Type not found");
    if (
      await this.documentTypeFieldRepository.existsBy({
        name: createDocumentTypeFieldDto.name,
        documentType: { id: documentTypeId }
      })
    )
      throw new BadRequestException("Document Type Field with the same name already exists");
    let newDocumentTypeField = new DocumentTypeField();
    for (const [key, value] of Object.entries({
      ...createDocumentTypeFieldDto,
      createdBy: this.request?.user || null,
      lastEditedBy: this.request?.user || null,
      documentType
    })) {
      if (value === undefined) continue;
      if (DOCUMENT_TYPE_FIELD_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      newDocumentTypeField[key] = value;
    }
    newDocumentTypeField = await this.documentTypeFieldRepository.save(newDocumentTypeField);
    return await this.getDocumentTypeFieldById(documentTypeId, newDocumentTypeField.id);
  }

  async editDocumentTypeField(
    documentTypeId: number,
    fieldId: number,
    editDocumentTypeFieldDto: EditDocumentTypeFieldDto
  ) {
    if (!this.options.update) throw new ForbiddenException("Edit Document Type Field is disabled");
    const documentType = await this.documentTypeService.getDocumentTypeById(documentTypeId);
    if (!documentType) throw new NotFoundException("Document Type not found");
    let documentTypeField = await this.getDocumentTypeFieldById(documentTypeId, fieldId);
    if (!documentTypeField) throw new NotFoundException("Document Type Field not found");
    for (const [key, value] of Object.entries(editDocumentTypeFieldDto)) {
      if (value === undefined) continue;
      if (DOCUMENT_TYPE_FIELD_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      documentTypeField[key] = value;
    }
    if (
      await this.documentTypeFieldRepository.existsBy({
        id: Not(documentTypeField.id),
        name: documentTypeField.name,
        documentType: { id: documentTypeId }
      })
    )
      throw new BadRequestException("Document Type Field with the same name already exists");
    documentTypeField = await this.documentTypeFieldRepository.save(documentTypeField);
    return await this.getDocumentTypeFieldById(documentTypeId, fieldId);
  }

  async deleteDocumentTypeField(documentTypeId: number, fieldId: number) {
    if (!this.options.delete) throw new ForbiddenException("Delete Document Type Field is disabled");
    const documentType = await this.documentTypeService.getDocumentTypeById(documentTypeId);
    if (!documentType) throw new NotFoundException("Document Type not found");
    const documentTypeField = await this.getDocumentTypeFieldById(documentTypeId, fieldId);
    if (!documentTypeField) throw new NotFoundException("Document Type Field not found");
    await this.documentTypeFieldRepository.delete({ id: fieldId, documentType: { id: documentTypeId } });
    return;
  }

  async batchUpdateDocumentTypeFields(
    documentTypeId: number,
    batchUpdateDocumentTypeFieldsDto: BatchUpdateDocumentTypeFieldsDto
  ): Promise<BatchUpdateDocumentTypeFieldsResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const savedDocumentTypeFieldIds: Array<number> = [];
    try {
      const documentTypeFieldRepository = queryRunner.manager.getRepository(DocumentTypeField);
      const { create: createList, edit: editList, delete: deleteList } = batchUpdateDocumentTypeFieldsDto;
      const toBeSavedDocumentTypeFields: Array<DocumentTypeField> = [];

      const documentType = await this.documentTypeService.getDocumentTypeById(documentTypeId, queryRunner);
      if (!documentType) throw new NotFoundException("Document Type not found");

      if (Array.isArray(createList) && createList.length > 0 && !this.options.create)
        throw new ForbiddenException("Create Document Type Field is disabled");
      if (Array.isArray(editList) && editList.length > 0 && !this.options.update)
        throw new ForbiddenException("Edit Document Type Field is disabled");
      if (Array.isArray(deleteList) && deleteList.length > 0 && !this.options.delete)
        throw new ForbiddenException("Delete Document Type Field is disabled");

      for (let i = 0; i < (createList || []).length; i++) {
        const createDocumentTypeFieldDto = createList[i];
        const errorMsgPrefix = `Error on create list index ${i}: `;
        if (
          toBeSavedDocumentTypeFields.some(
            (f) => f.name === createDocumentTypeFieldDto.name && f.documentType?.id === documentType.id
          ) ||
          (await documentTypeFieldRepository.existsBy({
            name: createDocumentTypeFieldDto.name,
            documentType: { id: documentTypeId }
          }))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Document Type Field with the same name already exists`
          );
        let newDocumentTypeField = new DocumentTypeField();
        for (const [key, value] of Object.entries({
          ...createDocumentTypeFieldDto,
          createdBy: this.request?.user || null,
          lastEditedBy: this.request?.user || null,
          documentType
        })) {
          if (value === undefined) continue;
          if (DOCUMENT_TYPE_FIELD_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
          newDocumentTypeField[key] = value;
        }
        toBeSavedDocumentTypeFields.push(newDocumentTypeField);
      }

      for (let i = 0; i < (editList || []).length; i++) {
        const editDocumentTypeFieldDto = editList[i];
        const errorMsgPrefix = `Error on edit list index ${i}: `;
        const documentTypeField = await this.getDocumentTypeFieldById(
          documentTypeId,
          editDocumentTypeFieldDto.id,
          queryRunner
        );
        if (!documentTypeField) throw new NotFoundException(`${errorMsgPrefix}Document Type Field not found`);
        for (const [key, value] of Object.entries(editDocumentTypeFieldDto)) {
          if (value === undefined) continue;
          if (DOCUMENT_TYPE_FIELD_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
          documentTypeField[key] = value;
        }
        if (
          toBeSavedDocumentTypeFields.some(
            (f) => f.name === documentTypeField.name && f.documentType?.id === documentType?.id
          ) ||
          (await documentTypeFieldRepository.existsBy({
            id: Not(
              In(
                toBeSavedDocumentTypeFields
                  .filter((f) => typeof f.id === "number")
                  .map((f) => f.id)
                  .concat([documentTypeField.id])
              )
            ),
            name: documentTypeField.name,
            documentType: { id: documentTypeId }
          }))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Document Type Field with the same name already exists`
          );
        toBeSavedDocumentTypeFields.push(documentTypeField);
      }

      for (let i = 0; i < (deleteList || []).length; i++) {
        const fieldId = deleteList[i];
        const errorMsgPrefix = `Error on delete list index ${i}: `;
        if (
          !(await documentTypeFieldRepository.existsBy({ id: fieldId, documentType: { id: documentTypeId } }))
        )
          throw new NotFoundException(`${errorMsgPrefix}Document Type Field not found`);
        if (toBeSavedDocumentTypeFields.some((f) => f.id === fieldId))
          throw new BadRequestException(`${errorMsgPrefix}Document Type Field is being edited`);
      }

      savedDocumentTypeFieldIds.push(
        ...(await documentTypeFieldRepository.save(toBeSavedDocumentTypeFields)).map((f) => f.id)
      );
      if (Array.isArray(deleteList) && deleteList.length > 0)
        await documentTypeFieldRepository.delete({ id: In(deleteList) });

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner.isReleased) queryRunner.release();
    }

    return {
      documentTypeFields: await this.documentTypeFieldRepository.find({
        where: { id: In(savedDocumentTypeFieldIds) },
        relations: FIND_DOCUMENT_TYPE_FIELD_RELATIONS
      })
    };
  }
}
