import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Delete,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  SerializeOptions,
  UseGuards,
  UseInterceptors
} from "@nestjs/common";
import { ApiForbiddenResponse, ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import {
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses
} from "../decorators";
import {
  BatchUpdateDocumentTypeFieldsDto,
  BatchUpdateDocumentTypeFieldsResponseDto,
  CreateDocumentTypeDto,
  CreateDocumentTypeFieldDto,
  EditDocumentTypeDto,
  EditDocumentTypeFieldDto,
  ForbiddenResponseDto,
  GetDocumentTypeFieldsDto,
  GetDocumentTypeFieldsResponseDto,
  GetDocumentTypesDto,
  GetDocumentTypesResponseDto
} from "../dto";
import { DocumentType, DocumentTypeField } from "../entities";
import { AccessTokenGuard } from "../guards";
import { DocumentTypeFieldService } from "./document-type-field.service";
import { DocumentTypeService } from "./document-type.service";

@ApiTags("Document Type API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@UseInterceptors(ClassSerializerInterceptor)
@Controller("document-types")
@SerializeOptions({ groups: ["backoffice"] })
export class DocumentTypeController {
  constructor(
    private readonly documentTypeService: DocumentTypeService,
    private readonly documentTypeFieldService: DocumentTypeFieldService
  ) {}

  // DocumentType endpoints

  @Get()
  @ApiOperation({ summary: "Get Document Types" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: GetDocumentTypesResponseDto })
  async getDocumentTypes(@Query() getDocumentTypesDto: GetDocumentTypesDto) {
    return await this.documentTypeService.getDocumentTypes(getDocumentTypesDto);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get Document Type by ID" })
  @ApiParam({ name: "id", type: "integer", description: "Document Type ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: DocumentType })
  async getDocumentTypeById(@Param("id", ParseIntPipe) id: number) {
    const documentType = await this.documentTypeService.getDocumentTypeById(id);
    if (!documentType) throw new NotFoundException("Document Type not found");
    return documentType;
  }

  @Post()
  @ApiOperation({ summary: "Create Document Type" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiCreateResponses({ type: DocumentType })
  async createDocumentType(@Body() createDocumentTypeDto: CreateDocumentTypeDto) {
    return await this.documentTypeService.createDocumentType(createDocumentTypeDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update Document Type" })
  @ApiParam({ name: "id", type: "integer", description: "Document Type ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: DocumentType })
  async editDocumentType(
    @Param("id", ParseIntPipe) id: number,
    @Body() editDocumentTypeDto: EditDocumentTypeDto
  ) {
    return await this.documentTypeService.editDocumentType(id, editDocumentTypeDto);
  }

  @HttpCode(204)
  @Delete(":id")
  @ApiOperation({ summary: "Delete Document Type" })
  @ApiParam({ name: "id", type: "integer", description: "Document Type ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiDeleteResponses()
  async deleteDocumentType(@Param("id", ParseIntPipe) id: number) {
    await this.documentTypeService.deleteDocumentType(id);
    return;
  }

  // DocumentTypeField endpoints

  @Get(":documentTypeId/fields")
  @ApiOperation({ summary: "Get Document Type Fields" })
  @ApiParam({ name: "documentTypeId", type: "integer", description: "Document Type ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: GetDocumentTypeFieldsResponseDto })
  async getDocumentTypeFields(
    @Param("documentTypeId", ParseIntPipe) documentTypeId: number,
    @Query() getDocumentTypeFieldsDto: GetDocumentTypeFieldsDto
  ) {
    return await this.documentTypeFieldService.getDocumentTypeFields(
      documentTypeId,
      getDocumentTypeFieldsDto
    );
  }

  @Get(":documentTypeId/fields/:fieldId")
  @ApiOperation({ summary: "Get Document Type Field by ID" })
  @ApiParam({ name: "documentTypeId", type: "integer", description: "Document Type ID" })
  @ApiParam({ name: "fieldId", type: "integer", description: "Document Type Field ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: DocumentTypeField })
  async getDocumentTypeFieldById(
    @Param("documentTypeId", ParseIntPipe) documentTypeId: number,
    @Param("fieldId", ParseIntPipe) fieldId: number
  ) {
    const field = await this.documentTypeFieldService.getDocumentTypeFieldById(documentTypeId, fieldId);
    if (!field) throw new NotFoundException("Document Type Field not found");
    return field;
  }

  @Post(":documentTypeId/fields")
  @ApiOperation({ summary: "Create Document Type Field" })
  @ApiParam({ name: "documentTypeId", type: "integer", description: "Document Type ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiCreateResponses({ type: DocumentTypeField })
  async createDocumentTypeField(
    @Param("documentTypeId", ParseIntPipe) documentTypeId: number,
    @Body() createDocumentTypeFieldDto: CreateDocumentTypeFieldDto
  ) {
    return await this.documentTypeFieldService.createDocumentTypeField(
      documentTypeId,
      createDocumentTypeFieldDto
    );
  }

  @Put(":documentTypeId/fields/:fieldId")
  @ApiOperation({ summary: "Update Document Type Field" })
  @ApiParam({ name: "documentTypeId", type: "integer", description: "Document Type ID" })
  @ApiParam({ name: "fieldId", type: "integer", description: "Document Type Field ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: DocumentTypeField })
  async editDocumentTypeField(
    @Param("documentTypeId", ParseIntPipe) documentTypeId: number,
    @Param("fieldId", ParseIntPipe) fieldId: number,
    @Body() editDocumentTypeFieldDto: EditDocumentTypeFieldDto
  ) {
    return await this.documentTypeFieldService.editDocumentTypeField(
      documentTypeId,
      fieldId,
      editDocumentTypeFieldDto
    );
  }

  @HttpCode(204)
  @Delete(":documentTypeId/fields/:fieldId")
  @ApiOperation({ summary: "Delete Document Type Field" })
  @ApiParam({ name: "documentTypeId", type: "integer", description: "Document Type ID" })
  @ApiParam({ name: "fieldId", type: "integer", description: "Document Type Field ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiDeleteResponses()
  async deleteDocumentTypeField(
    @Param("documentTypeId", ParseIntPipe) documentTypeId: number,
    @Param("fieldId", ParseIntPipe) fieldId: number
  ) {
    await this.documentTypeFieldService.deleteDocumentTypeField(documentTypeId, fieldId);
    return;
  }

  @HttpCode(200)
  @Post(":documentTypeId/fields/batch")
  @ApiOperation({ summary: "Batch update Document Type Fields" })
  @ApiParam({ name: "documentTypeId", type: "integer", description: "Document Type ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: BatchUpdateDocumentTypeFieldsResponseDto })
  async batchUpdateDocumentTypeFields(
    @Param("documentTypeId", ParseIntPipe) documentTypeId: number,
    @Body() batchUpdateDocumentTypeFieldsDto: BatchUpdateDocumentTypeFieldsDto
  ) {
    return await this.documentTypeFieldService.batchUpdateDocumentTypeFields(
      documentTypeId,
      batchUpdateDocumentTypeFieldsDto
    );
  }
}
