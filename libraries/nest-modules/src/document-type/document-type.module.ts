import { DynamicModule, Module } from "@nestjs/common";
import { DOCUMENT_TYPE_MODULE_OPTIONS, CrudOptions } from "../types";
import { TypeOrmModule } from "@nestjs/typeorm";
import { DocumentTypeController } from "./document-type.controller";
import { DocumentType, DocumentTypeField } from "../entities";
import { DocumentTypeService } from "./document-type.service";
import { DocumentTypeFieldService } from "./document-type-field.service";

@Module({})
export class DocumentTypeModule {
  static register(options: CrudOptions): DynamicModule {
    return {
      global: true,
      module: DocumentTypeModule,
      imports: [TypeOrmModule.forFeature([DocumentType, DocumentTypeField])],
      controllers: [DocumentTypeController],
      providers: [
        {
          provide: DOCUMENT_TYPE_MODULE_OPTIONS,
          useValue: options
        },
        DocumentTypeService,
        DocumentTypeFieldService
      ],
      exports: [DocumentTypeService, DocumentTypeFieldService]
    };
  }
}
