import { CustomsStatus } from "../types";
import { ValidateShipmentComplianceResponseDto } from "../dto";

/**
 * Checks if a given customs status is considered 'submitted'.
 * Pure function, no side effects.
 * @param status - The customs status to check.
 * @returns {boolean} True if the status is a submitted status, false otherwise.
 */
export function isSubmittedCustomsStatus(status: string | null | undefined): boolean {
  if (!status) return false;
  return [
    CustomsStatus.ENTRY_SUBMITTED,
    CustomsStatus.ENTRY_ACCEPTED,
    CustomsStatus.EXAM,
    CustomsStatus.RELEASED,
    CustomsStatus.ACCOUNTING_COMPLETED
  ].includes(status as CustomsStatus);
}

export function isPendingCustomsStatus(status: string | null | undefined): boolean {
  if (!status) return false;
  return [
    CustomsStatus.PENDING_COMMERCIAL_INVOICE,
    CustomsStatus.PENDING_CONFIRMATION,
    CustomsStatus.PENDING_ARRIVAL
  ].includes(status as CustomsStatus);
}

export function isLiveCustomsStatus(status: string | null | undefined): boolean {
  if (!status) return false;
  return status === CustomsStatus.LIVE;
}

export function isReleasedCustomsStatus(status: string | null | undefined): boolean {
  if (!status) return false;
  return [CustomsStatus.RELEASED, CustomsStatus.ACCOUNTING_COMPLETED].includes(status as CustomsStatus);
}

/**
 * Checks if a shipment is ready to have a CAD document sent.
 * CAD documents are sent when the customs entry is accepted and beyond.
 * Pure function, no side effects.
 * @param status - The customs status to check.
 * @returns {boolean} True if the status allows CAD document sending, false otherwise.
 */
export function isSendCADReady(status: string | null | undefined): boolean {
  if (!status) return false;
  return [
    CustomsStatus.ENTRY_ACCEPTED,
    CustomsStatus.EXAM,
    CustomsStatus.RELEASED,
    CustomsStatus.ACCOUNTING_COMPLETED
  ].includes(status as CustomsStatus);
}

/**
 * Checks if a shipment is ready to have RNS proof of release sent.
 * RNS proof of release is only available when goods are released.
 * Pure function, no side effects.
 * @param status - The customs status to check.
 * @returns {boolean} True if the status allows RNS proof of release sending, false otherwise.
 */
export function isSendRNSProofOfReleaseReady(status: string | null | undefined): boolean {
  if (!status) return false;
  return [CustomsStatus.RELEASED, CustomsStatus.ACCOUNTING_COMPLETED].includes(status as CustomsStatus);
}

/**
 * Checks if a shipment is ready to submit based on compliance validation result.
 * @param compliance - The compliance validation result
 * @returns True if ready, false otherwise
 */
export function isReadyToSubmit(compliance: ValidateShipmentComplianceResponseDto): boolean {
  return (
    !compliance.noCommercialInvoice &&
    (compliance.missingFields?.length ?? 0) === 0 &&
    (compliance.nonCompliantInvoices?.length ?? 0) === 0
  );
}