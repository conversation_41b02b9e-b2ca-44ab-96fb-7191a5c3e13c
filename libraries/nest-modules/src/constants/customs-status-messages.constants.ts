/**
 * @fileoverview Constants mapping customs status keys to user-friendly display messages.
 */

import { CustomsStatus } from "../types";

/**
 * A map where keys are members of the CustomsStatus enum
 * and values are the corresponding user-facing display strings.
 */
export const CUSTOMS_STATUS_MESSAGES: Readonly<Record<CustomsStatus, string>> = {
  // Statuses from CustomsStatus enum
  [CustomsStatus.PENDING_COMMERCIAL_INVOICE]: "Pending Commercial Invoice",
  [CustomsStatus.PENDING_CONFIRMATION]: "Pending Confirmation",
  [CustomsStatus.PENDING_ARRIVAL]: "Pending Arrival",
  [CustomsStatus.LIVE]: "Live", // Or perhaps "Ready for Submission"?
  [CustomsStatus.ENTRY_SUBMITTED]: "Entry Submitted",
  [CustomsStatus.ENTRY_ACCEPTED]: "Entry Accepted",
  [CustomsStatus.EXAM]: "Under Examination",
  [CustomsStatus.RELEASED]: "Released",
  [CustomsStatus.ACCOUNTING_COMPLETED]: "Accounting Completed"
  // Add other customs statuses and their display names as needed
} as const;

/**
 * Function to safely get a customs status message, with fallback for unknown statuses
 */
export function getCustomsStatusMessage(status: CustomsStatus | string | null | undefined): string {
  if (!status) return "Unknown";

  // If status exists in the map, return the mapped message
  if (status in CUSTOMS_STATUS_MESSAGES) {
    return CUSTOMS_STATUS_MESSAGES[status as CustomsStatus];
  }

  // Fallback: use the status string itself, with first letter capitalized
  if (typeof status === "string") {
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  }

  return "Unknown";
}