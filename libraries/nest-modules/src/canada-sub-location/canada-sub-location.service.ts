import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { GetCanadaSubLocationsDto, GetCanadaSubLocationsResponseDto } from "../dto/canada-sub-location.dto";
import { CanadaSubLocation } from "../entities";
import { getFindOptions } from "../helper-functions";
import {
  CanadaSubLocationColumn,
  FIND_CANADA_SUB_LOCATION_RELATIONS
} from "../types/canada-sub-location.types";

@Injectable()
export class CanadaSubLocationService {
  constructor(
    @InjectRepository(CanadaSubLocation)
    private readonly repository: Repository<CanadaSubLocation>
  ) {}

  async getCanadaSubLocations(query: GetCanadaSubLocationsDto): Promise<GetCanadaSubLocationsResponseDto> {
    const { where, order, skip, take } = getFindOptions<CanadaSubLocation>(
      query,
      [],
      ["code", "portCode"],
      CanadaSubLocationColumn.id
    );

    const [data, total] = await this.repository.findAndCount({
      where,
      order,
      skip,
      take,
      relations: FIND_CANADA_SUB_LOCATION_RELATIONS
    });

    return {
      data,
      skip,
      limit: take,
      total
    };
  }
}
