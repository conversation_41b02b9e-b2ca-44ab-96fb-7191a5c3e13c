import { Controller, Get, Inject, Query, UseGuards } from "@nestjs/common";
import { ApiForbiddenResponse, ApiOperation, ApiTags } from "@nestjs/swagger";
import { ApiAccessTokenAuthenticated, ApiGetManyResponses } from "../decorators";
import { GetCanadaSubLocationsDto, GetCanadaSubLocationsResponseDto } from "../dto/canada-sub-location.dto";
import { ForbiddenResponseDto } from "../dto/exception.dto";
import { AccessTokenGuard } from "../guards";
import { CanadaSubLocationService } from "./canada-sub-location.service";

@ApiTags("Canada Sub Location API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("canada-sub-locations")
export class CanadaSubLocationController {
  constructor(
    @Inject(CanadaSubLocationService)
    private readonly service: CanadaSubLocationService
  ) {}

  @ApiOperation({ summary: "Get Canada sub locations" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: GetCanadaSubLocationsResponseDto })
  @Get()
  async getCanadaSubLocations(
    @Query() query: GetCanadaSubLocationsDto
  ): Promise<GetCanadaSubLocationsResponseDto> {
    return this.service.getCanadaSubLocations(query);
  }
}
