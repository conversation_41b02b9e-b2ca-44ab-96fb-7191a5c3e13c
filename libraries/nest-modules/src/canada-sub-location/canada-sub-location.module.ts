import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CanadaSubLocation, Port } from "../entities";
import { CanadaSubLocationController } from "./canada-sub-location.controller";
import { CanadaSubLocationService } from "./canada-sub-location.service";
import { PortController } from "./port.controller";
import { PortService } from "./port.service";

@Module({
  imports: [TypeOrmModule.forFeature([CanadaSubLocation, Port])],
  providers: [CanadaSubLocationService, PortService],
  controllers: [CanadaSubLocationController, PortController]
})
export class CanadaSubLocationModule {}
