import { Controller, Get, Inject, Query, UseGuards } from "@nestjs/common";
import { ApiForbiddenResponse, ApiOperation, ApiTags } from "@nestjs/swagger";
import { ApiAccessTokenAuthenticated, ApiGetManyResponses } from "../decorators/api-docs";
import { ForbiddenResponseDto } from "../dto/exception.dto";
import { GetPortsDto, GetPortsResponseDto } from "../dto/port.dto";
import { AccessTokenGuard } from "../guards";
import { PortService } from "./port.service";

@ApiTags("Port API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("ports")
export class PortController {
  constructor(
    @Inject(PortService)
    private readonly portService: PortService
  ) {}

  @ApiOperation({ summary: "Get port codes" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: GetPortsResponseDto })
  @Get()
  async getPortCodes(@Query() query: GetPortsDto): Promise<GetPortsResponseDto> {
    return this.portService.getPortCodes(query);
  }
}
