import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { GetPortsDto, GetPortsResponseDto } from "../dto/port.dto";
import { Port } from "../entities/port.entity";
import { getFindOptions } from "../helper-functions";
import { PortColumn } from "../types";

@Injectable()
export class PortService {
  constructor(
    @InjectRepository(Port)
    private readonly portRepository: Repository<Port>
  ) {}

  async getPortCodes(query: GetPortsDto): Promise<GetPortsResponseDto> {
    const { where, order, skip, take } = getFindOptions<Port>(query, [], ["code"], PortColumn.id);

    const [data, total] = await this.portRepository.findAndCount({
      where,
      order,
      skip,
      take
    });

    return {
      data,
      skip,
      limit: take,
      total
    };
  }

  async getPortByCode(code: string): Promise<Port> {
    const port = await this.portRepository.findOne({ where: { code } });
    if (!port) throw new NotFoundException("Port not found");
    return port;
  }
}
