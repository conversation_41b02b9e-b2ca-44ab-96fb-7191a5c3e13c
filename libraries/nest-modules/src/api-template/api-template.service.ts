import { Inject, Injectable, InternalServerErrorException, Logger } from "@nestjs/common";
import axios from "axios";
import { ApiTemplateCreatePdfDto, ApiTemplateCreatePdfResponseDto } from "../dto";
import { API_TEMPLATE_MODULE_OPTIONS, ApiTemplateModuleOptions } from "../types";

@Injectable()
export class ApiTemplateService {
  constructor(
    @Inject(API_TEMPLATE_MODULE_OPTIONS)
    private readonly options: ApiTemplateModuleOptions
  ) {}
  private readonly logger = new Logger(ApiTemplateService.name);
  private readonly API_TEMPLATE_BASE_URL = "https://rest-us.apitemplate.io/v2";

  async createPdf(dto: ApiTemplateCreatePdfDto, payload: Record<string, any>) {
    if (!this.options.apiKey) throw new InternalServerErrorException("API Template API Key is not set");

    const responseType =
      dto.export_type === "file" ? (dto.export_in_base64 === "1" ? "text" : "arraybuffer") : "json";

    try {
      const res = await axios.post(`${this.API_TEMPLATE_BASE_URL}/create-pdf`, payload, {
        headers: {
          "X-API-KEY": this.options.apiKey
        },
        params: dto,
        responseType
      });
      if (responseType === "json") return res.data as ApiTemplateCreatePdfResponseDto;
      else if (responseType === "arraybuffer") return res.data as ArrayBuffer;
      else return res.data as string;
    } catch (error) {
      this.logger.error(
        `Error while creating PDF: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
      throw error;
    }
  }
}
