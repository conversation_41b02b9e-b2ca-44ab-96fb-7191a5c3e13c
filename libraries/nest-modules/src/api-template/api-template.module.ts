import { DynamicModule, Module } from "@nestjs/common";
import { API_TEMPLATE_MODULE_OPTIONS, ApiTemplateModuleOptions } from "../types";
import { ApiTemplateService } from "./api-template.service";

@Module({})
export class ApiTemplateModule {
  static register(options: ApiTemplateModuleOptions): DynamicModule {
    return {
      global: true,
      module: ApiTemplateModule,
      providers: [
        ApiTemplateService,
        {
          provide: API_TEMPLATE_MODULE_OPTIONS,
          useValue: options
        }
      ],
      exports: [ApiTemplateService]
    };
  }
}
