import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import { Not, Query<PERSON>unner, Repository } from "typeorm";
import {
  CreateOrganizationDto,
  EditOrganizationDto,
  GetOrganizationsDto,
  GetOrganizationsResponseDto
} from "../../dto/organization.dto";
import { Organization } from "../../entities/organization.entity";
import { getFindOptions } from "../../helper-functions";
import { AuthenticatedRequest } from "../../types/auth.types";
import { CrudOptions } from "../../types/base.types";
import {
  FIND_ORGANIZATION_RELATIONS,
  ORGANIZATION_ENUM_KEYS,
  ORGANIZATION_REQUIRED_KEYS,
  OrganizationColumn
} from "../../types/organization.types";
import { ORGANIZATION_CRUD_OPTIONS, UserPermission } from "../../types/user.types";

@Injectable({ scope: Scope.REQUEST })
export class OrganizationService {
  constructor(
    @InjectRepository(Organization)
    private readonly organizationRepository: Repository<Organization>,
    @Inject(ORGANIZATION_CRUD_OPTIONS)
    private readonly crudOptions: CrudOptions,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest
  ) {}

  async getOrganizations(getOrganizationsDto: GetOrganizationsDto): Promise<GetOrganizationsResponseDto> {
    if (!this.crudOptions.readMany) throw new ForbiddenException("Get organizations is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("User is not backoffice admin");
    const { where, order, skip, take } = getFindOptions<Organization>(
      getOrganizationsDto,
      ORGANIZATION_ENUM_KEYS,
      [],
      OrganizationColumn.id
    );
    const [organizations, total] = await this.organizationRepository.findAndCount({
      where,
      order,
      skip,
      take,
      relations: FIND_ORGANIZATION_RELATIONS
    });

    return {
      organizations,
      total,
      skip,
      limit: take
    };
  }

  async getOrganizationById(orgId: number, skipCheckingOrganization = false, queryRunner?: QueryRunner) {
    if (!this.crudOptions.readOne) throw new ForbiddenException("Get Organization is disabled");
    if (
      this.request.user?.permission !== UserPermission.BACKOFFICE_ADMIN &&
      this.request?.user?.organization?.id !== orgId &&
      !skipCheckingOrganization
    )
      return null;
    return await (
      queryRunner ? queryRunner.manager.getRepository(Organization) : this.organizationRepository
    ).findOne({
      where: { id: orgId },
      relations: FIND_ORGANIZATION_RELATIONS
    });
  }

  /**
   * Create a new organization
   * @param createOrganizationDto - Create organization DTO.
   * @param fromOnboarding - Whether the organization is being created from onboarding. If true, the create flag in CRUD options will be ignored.
   * @param queryRunner - Query runner used for transaction.
   * @returns Newly created organization
   */
  async createOrganization(
    createOrganizationDto: CreateOrganizationDto,
    fromOnboarding = false,
    queryRunner?: QueryRunner
  ) {
    const organizationRepository = queryRunner
      ? queryRunner.manager.getRepository(Organization)
      : this.organizationRepository;

    if (!fromOnboarding && !this.crudOptions.create)
      throw new ForbiddenException("Create organization is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user is not backoffice admin");
    if (await organizationRepository.existsBy({ name: createOrganizationDto.name }))
      throw new BadRequestException("Organization with the same name already exists");

    let newOrg = new Organization();
    newOrg.name = createOrganizationDto.name;
    newOrg.organizationType = createOrganizationDto.organizationType;
    newOrg.customsBroker = createOrganizationDto.customsBroker;
    newOrg.skipPoaCheck = createOrganizationDto.skipPoaCheck ?? false;
    newOrg.createdBy = this.request?.user ?? null;
    newOrg.lastEditedBy = this.request?.user ?? null;

    const missingRequiredKeys = ORGANIZATION_REQUIRED_KEYS.filter((key) =>
      [null, undefined].includes(newOrg[key])
    );
    if (missingRequiredKeys.length > 0)
      throw new BadRequestException(
        `The following fields are required for organization: ${missingRequiredKeys.join(", ")}`
      );
    newOrg = await organizationRepository.save(newOrg);

    return await this.getOrganizationById(newOrg.id, false, queryRunner);
  }

  async editOrganization(orgId: number, editOrganizationDto: EditOrganizationDto) {
    if (!this.crudOptions.update) throw new ForbiddenException("Edit organization is disabled");

    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user is not backoffice admin");
    const organization = await this.getOrganizationById(orgId);
    if (!organization) throw new NotFoundException("Organization not found");
    if (
      await this.organizationRepository.existsBy({
        id: Not(organization.id),
        name: editOrganizationDto.name || organization.name
      })
    )
      throw new BadRequestException("Organization with the same name already exists");

    if (typeof editOrganizationDto.name === "string" && editOrganizationDto.name.length > 0)
      organization.name = editOrganizationDto.name;
    if (editOrganizationDto.organizationType)
      organization.organizationType = editOrganizationDto.organizationType;

    if (editOrganizationDto.skipPoaCheck !== undefined) {
      organization.skipPoaCheck = editOrganizationDto.skipPoaCheck;
    }
    organization.lastEditedBy = this.request?.user;

    const missingRequiredKeys = ORGANIZATION_REQUIRED_KEYS.filter((key) =>
      [null, undefined].includes(organization[key])
    );
    if (missingRequiredKeys.length > 0)
      throw new BadRequestException(
        `The following fields are required for organization: ${missingRequiredKeys.join(", ")}`
      );

    await this.organizationRepository.save(organization);

    return await this.getOrganizationById(organization.id);
  }

  async deleteOrganization(orgId: number) {
    if (!this.crudOptions.delete) throw new ForbiddenException("Delete organization is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user is not backoffice admin");
    if (!(await this.organizationRepository.existsBy({ id: orgId })))
      throw new NotFoundException("Organization not found");
    await this.organizationRepository.delete({ id: orgId });
    return;
  }
}
