import { BadRequestException, Inject, Injectable, Logger, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Password, ResetPasswordToken, SimplifiedUser, User } from "../../entities";
import { <PERSON>Null, <PERSON><PERSON>han, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>qua<PERSON>, Not, QueryRunner, Repository } from "typeorm";
import { createHash, randomBytes } from "crypto";
import { getHashPassword } from "../../helper-functions";
import { FIND_RESET_PASSWORD_TOKEN_RELATIONS, FIND_USER_RELATIONS } from "../../types";

@Injectable()
export class PasswordService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Password)
    private readonly passwordRepository: Repository<Password>,
    @InjectRepository(ResetPasswordToken)
    private readonly resetPasswordTokenRepository: Repository<ResetPasswordToken>
  ) {}
  private readonly logger = new Logger(PasswordService.name);

  private hashToken(token: string) {
    return createHash("sha256").update(token).digest("hex");
  }

  private async getResetPasswordTokenById(id: number, includeExpired = false, queryRunner?: QueryRunner) {
    return await (
      queryRunner ? queryRunner.manager.getRepository(ResetPasswordToken) : this.resetPasswordTokenRepository
    ).findOne({
      where: { id, expiryDate: includeExpired ? Not(IsNull()) : MoreThan(new Date()) },
      relations: FIND_RESET_PASSWORD_TOKEN_RELATIONS
    });
  }

  private async getResetPasswordTokenByToken(
    token: string,
    includeExpired = false,
    queryRunner?: QueryRunner
  ) {
    return await (
      queryRunner ? queryRunner.manager.getRepository(ResetPasswordToken) : this.resetPasswordTokenRepository
    ).findOne({
      where: {
        hashedToken: this.hashToken(token),
        expiryDate: includeExpired ? Not(IsNull()) : MoreThan(new Date())
      },
      relations: FIND_RESET_PASSWORD_TOKEN_RELATIONS
    });
  }

  async getResetPasswordTokensByUser(
    userOrUserId: User | number,
    includeExpired = false,
    queryRunner?: QueryRunner
  ) {
    return await (
      queryRunner ? queryRunner.manager.getRepository(ResetPasswordToken) : this.resetPasswordTokenRepository
    ).find({
      where: {
        user: { id: typeof userOrUserId === "number" ? userOrUserId : userOrUserId.id },
        expiryDate: includeExpired ? Not(IsNull()) : MoreThan(new Date())
      },
      relations: FIND_RESET_PASSWORD_TOKEN_RELATIONS
    });
  }

  async getResetPasswordUser(token: string, includeExpired = false, queryRunner?: QueryRunner) {
    const resetPasswordToken = await this.getResetPasswordTokenByToken(token, includeExpired, queryRunner);
    if (!resetPasswordToken) return null;
    return await (queryRunner ? queryRunner.manager.getRepository(User) : this.userRepository).findOne({
      where: { id: resetPasswordToken.user?.id },
      relations: FIND_USER_RELATIONS
    });
  }

  async invalidateUserResetPasswordTokens(userOrUserId: User | number, queryRunner?: QueryRunner) {
    const userRepository = queryRunner ? queryRunner.manager.getRepository(User) : this.userRepository;
    const resetPasswordTokenRepository = queryRunner
      ? queryRunner.manager.getRepository(ResetPasswordToken)
      : this.resetPasswordTokenRepository;

    const user =
      typeof userOrUserId === "number" ? await userRepository.findOneBy({ id: userOrUserId }) : userOrUserId;
    if (!user) throw new NotFoundException("User not found");

    await resetPasswordTokenRepository.update({ user: { id: user.id } }, { expiryDate: new Date() });

    return await this.getResetPasswordTokensByUser(user, true, queryRunner);
  }

  async invalidateResetPasswordToken(token: string, queryRunner?: QueryRunner) {
    const resetPasswordTokenRepository = queryRunner
      ? queryRunner.manager.getRepository(ResetPasswordToken)
      : this.resetPasswordTokenRepository;

    if (!(await resetPasswordTokenRepository.existsBy({ hashedToken: this.hashToken(token) })))
      throw new NotFoundException("Reset password token not found");

    await resetPasswordTokenRepository.update(
      { hashedToken: this.hashToken(token) },
      { expiryDate: new Date() }
    );

    return await this.getResetPasswordTokenByToken(token, true, queryRunner);
  }

  async generateResetPasswordToken(userOrUserId: User | number, queryRunner?: QueryRunner) {
    const userRepository = queryRunner ? queryRunner.manager.getRepository(User) : this.userRepository;
    const resetPasswordTokenRepository = queryRunner
      ? queryRunner.manager.getRepository(ResetPasswordToken)
      : this.resetPasswordTokenRepository;

    const user =
      typeof userOrUserId === "number" ? await userRepository.findOneBy({ id: userOrUserId }) : userOrUserId;
    if (!user) throw new NotFoundException("User not found");

    // Generate a unique token
    let token: string | null = null,
      hashedToken: string | null = null;
    do {
      token = randomBytes(32).toString("hex"); // 256-bit hex string
      hashedToken = this.hashToken(token);
    } while (await resetPasswordTokenRepository.existsBy({ hashedToken }));

    let resetPasswordToken = new ResetPasswordToken();
    resetPasswordToken.user = user;
    resetPasswordToken.hashedToken = hashedToken;
    resetPasswordToken.expiryDate = new Date(Date.now() + 24 * 60 * 60 * 1000); // 1 day
    resetPasswordToken = await resetPasswordTokenRepository.save(resetPasswordToken);

    return {
      token,
      resetPasswordTokenRecord: await this.getResetPasswordTokenById(
        resetPasswordToken.id,
        false,
        queryRunner
      )
    };
  }

  async setUserPassword(userOrUserId: User | number, password: string, queryRunner?: QueryRunner) {
    const userRepository = queryRunner ? queryRunner.manager.getRepository(User) : this.userRepository;
    const passwordRepository = queryRunner
      ? queryRunner.manager.getRepository(Password)
      : this.passwordRepository;

    const user =
      typeof userOrUserId === "number" ? await userRepository.findOneBy({ id: userOrUserId }) : userOrUserId;
    if (!user) throw new NotFoundException("User not found");

    if (typeof password !== "string" || password.length <= 0)
      throw new BadRequestException("Password must be a non-empty string");

    let passwordRecord = await passwordRepository.findOneBy({ user: { id: user.id } });
    if (!passwordRecord) {
      passwordRecord = new Password();
      passwordRecord.user = user;
    }
    passwordRecord.salt = randomBytes(32).toString("hex");
    passwordRecord.hashPassword = getHashPassword(password, passwordRecord.salt);

    return await passwordRepository.save(passwordRecord);
  }
}
