import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { <PERSON><PERSON><PERSON>, Not, Query<PERSON><PERSON>ner, Repository } from "typeorm";
import { User } from "../../entities/user.entity";
import { OrganizationService } from "./organization.service";
import {
  FIND_USER_RELATIONS,
  USER_CRUD_OPTIONS,
  USER_ENUM_KEYS,
  UserColumn,
  UserEvent,
  UserPermission
} from "../../types/user.types";
import { CrudOptions } from "../../types/base.types";
import { REQUEST } from "@nestjs/core";
import { AuthenticatedRequest } from "../../types/auth.types";
import {
  CreateUserDto,
  EditUserDto,
  GetUsersDto,
  GetUsersResponseDto,
  ResetUserPasswordDto,
  ResetUserPasswordResponseDto,
  SendResetPasswordEmailDto,
  SendResetPasswordEmailResponseDto
} from "../../dto/user.dto";
import { getFindOptions, getHashPassword } from "../../helper-functions";
import { Password } from "../../entities/password.entity";
import { randomBytes } from "crypto";
import { PasswordService } from "./password.service";
import { TransactionalEventEmitterService } from "../../transactional-event-emitter";

@Injectable({ scope: Scope.REQUEST })
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Password)
    private readonly passwordRepository: Repository<Password>,
    @Inject(USER_CRUD_OPTIONS)
    private readonly crudOptions: CrudOptions,
    @Inject(OrganizationService)
    private readonly organizationService: OrganizationService,
    @Inject(PasswordService)
    private readonly passwordService: PasswordService,
    @Inject(TransactionalEventEmitterService)
    private readonly transactionalEventEmitterService: TransactionalEventEmitterService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest
  ) {}

  async getUsers(getUsersDto: GetUsersDto): Promise<GetUsersResponseDto> {
    if (!this.crudOptions.readMany) throw new ForbiddenException("Get users is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      getUsersDto.organizationId = this.request?.user?.organization?.id || -1;
    const { where, order, skip, take } = getFindOptions<User>(
      getUsersDto,
      USER_ENUM_KEYS,
      ["googleUserId"],
      UserColumn.id
    );
    const [users, total] = await this.userRepository.findAndCount({
      where,
      order,
      skip,
      take,
      relations: FIND_USER_RELATIONS
    });
    return {
      users,
      total,
      skip,
      limit: take
    };
  }

  async getUserById(userId: number, skipCheckingOrganization = false, queryRunner?: QueryRunner) {
    if (!this.crudOptions.readOne) throw new ForbiddenException("Get user is disabled");
    return await (queryRunner ? queryRunner.manager.getRepository(User) : this.userRepository).findOne({
      where: {
        id: userId,
        organization: {
          id:
            skipCheckingOrganization || this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
              ? Not(IsNull())
              : this.request?.user?.organization?.id || -1
        }
      },
      relations: FIND_USER_RELATIONS
    });
  }

  async createUser(createUserDto: CreateUserDto, queryRunner?: QueryRunner) {
    const userRepository = queryRunner ? queryRunner.manager.getRepository(User) : this.userRepository;

    if (!this.crudOptions.create) throw new ForbiddenException("Create user is disabled");
    // TODO: Add action rule
    if (this.request?.user?.permission === UserPermission.BASIC)
      throw new ForbiddenException("User is not admin");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN) {
      createUserDto.organizationId = this.request?.user?.organization?.id || -1;
      if (createUserDto.permission === UserPermission.BACKOFFICE_ADMIN)
        createUserDto.permission = UserPermission.ORGANIZATION_ADMIN;
    }

    const { organizationId, password, email, ...createUserProps } = createUserDto;
    const organization = await this.organizationService.getOrganizationById(
      organizationId,
      false,
      queryRunner
    );
    if (!organization) throw new NotFoundException("Organization not found");
    // TODO: Do not show error message (refer to phase 2 story)
    if (await userRepository.existsBy({ email }))
      throw new BadRequestException("User with the same email already exists");
    let newUser = new User();
    for (const [key, value] of Object.entries(createUserProps)) newUser[key] = value;
    newUser.organization = organization;
    newUser.email = email;
    newUser.createdBy = this.request?.user ?? null;
    newUser.lastEditedBy = this.request?.user ?? null;
    newUser = await userRepository.save(newUser);

    if (typeof password === "string" && password.length > 0)
      await this.passwordService.setUserPassword(newUser, password, queryRunner);

    return await this.getUserById(newUser.id, false, queryRunner);
  }

  async editUser(userId: number, editUserDto: EditUserDto, queryRunner?: QueryRunner) {
    const userRepository = queryRunner ? queryRunner.manager.getRepository(User) : this.userRepository;

    if (!this.crudOptions.update) throw new ForbiddenException("Edit user is disabled");
    // TODO: Add action rule
    if (this.request?.user?.permission === UserPermission.BASIC) {
      if (this.request?.user?.id !== userId) throw new ForbiddenException("User is not admin");
      delete editUserDto.permission;
    } else if (
      this.request?.user?.permission === UserPermission.ORGANIZATION_ADMIN &&
      editUserDto.permission === UserPermission.BACKOFFICE_ADMIN
    )
      editUserDto.permission = UserPermission.ORGANIZATION_ADMIN;
    const user = await this.getUserById(userId, false, queryRunner);
    if (!user) throw new NotFoundException("User not found");

    const { password, email, ...editUserProps } = editUserDto;
    if (typeof email === "string" && email.length > 0) {
      // TODO: Do not show error message (refer to phase 2 story)
      if (await userRepository.existsBy({ id: Not(user.id), email }))
        throw new BadRequestException("User with the same email already exists in organization");
      user.email = email;
    }
    for (const [key, value] of Object.entries(editUserProps)) user[key] = value;
    user.lastEditedBy = this.request?.user ?? null;
    await userRepository.save(user);

    if (typeof password === "string" && password.length > 0)
      await this.passwordService.setUserPassword(user, password, queryRunner);

    return await this.getUserById(user.id, false, queryRunner);
  }

  async deleteUser(userId: number) {
    if (!this.crudOptions.delete) throw new ForbiddenException("Delete user is disabled");
    // TODO: Add action rule
    if (this.request?.user?.permission === UserPermission.BASIC)
      throw new ForbiddenException("User is not admin");
    const user = await this.getUserById(userId);
    if (!user) throw new NotFoundException("User not found");
    await this.userRepository.delete({ id: user.id });
    return;
  }

  async sendResetPasswordEmail(
    sendResetPasswordEmailDto: SendResetPasswordEmailDto
  ): Promise<SendResetPasswordEmailResponseDto> {
    const { email } = sendResetPasswordEmailDto;
    if (
      await this.userRepository.existsBy({
        email
      })
    )
      await this.transactionalEventEmitterService.enqueueEvent(UserEvent.SEND_RESET_PASSWORD_EMAIL, null, {
        email
      });

    return { message: "If the email exists, a reset link has been sent." };
  }

  async resetUserPassword(
    resetUserPasswordDto: ResetUserPasswordDto,
    queryRunner?: QueryRunner
  ): Promise<ResetUserPasswordResponseDto> {
    const { token, password } = resetUserPasswordDto;

    const tokenUser = await this.passwordService.getResetPasswordUser(token, false, queryRunner);
    if (!tokenUser) throw new BadRequestException("Invalid token");

    await this.passwordService.setUserPassword(tokenUser, password, queryRunner);
    await this.passwordService.invalidateResetPasswordToken(token, queryRunner);

    return { message: "Password reset successfully" };
  }
}
