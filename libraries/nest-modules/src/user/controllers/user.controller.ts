import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards
} from "@nestjs/common";
import { ApiForbiddenResponse, ApiOkResponse, ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import {
  ApiAccessTokenAuthenticated,
  ApiGetByIdResponses,
  ApiCreateResponses,
  ApiEditResponses,
  ApiDeleteResponses
} from "../../decorators";
import {
  ForbiddenResponseDto,
  GetUsersResponseDto,
  GetUsersDto,
  CreateUserDto,
  EditUserDto
} from "../../dto";
import { User } from "../../entities";
import { AccessTokenGuard } from "../../guards";
import { AuthenticatedRequest } from "../../types";
import { UserService } from "../services";

@UseGuards(AccessTokenGuard)
@ApiAccessTokenAuthenticated()
@ApiTags("User API")
@Controller("users")
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @ApiOperation({ summary: "Get users" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiOkResponse({ type: GetUsersResponseDto })
  async getUsers(@Query() getUsersDto: GetUsersDto) {
    return await this.userService.getUsers(getUsersDto);
  }

  @Get("me")
  @ApiOperation({ summary: "Get current user" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiOkResponse({ type: User })
  async getCurrentUser(@Req() request: AuthenticatedRequest) {
    const user = await this.userService.getUserById(request.user?.id);
    if (!user) throw new NotFoundException("User not found");
    return user;
  }

  @Get(":id")
  @ApiOperation({ summary: "Get User" })
  @ApiParam({ name: "id", type: "integer", description: "User ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: User })
  async getUser(@Req() req: AuthenticatedRequest, @Param("id", ParseIntPipe) id: number) {
    const user = await this.userService.getUserById(id);
    if (!user || user.organization.id !== req.user.organization.id)
      throw new NotFoundException("User not found");
    return user;
  }

  @Post()
  @ApiOperation({ summary: "Create User" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiCreateResponses({ type: User })
  async createUser(@Req() request: AuthenticatedRequest, @Body() createUserDto: CreateUserDto) {
    return await this.userService.createUser(createUserDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Edit User" })
  @ApiParam({ name: "id", type: "integer", description: "User ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: User })
  async editUser(@Param("id", ParseIntPipe) id: number, @Body() editUserDto: EditUserDto) {
    return await this.userService.editUser(id, editUserDto);
  }

  @Delete(":id")
  @HttpCode(204)
  @ApiOperation({ summary: "Delete User" })
  @ApiParam({ name: "id", type: "integer", description: "User ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiDeleteResponses()
  async deleteUser(@Param("id", ParseIntPipe) id: number) {
    await this.userService.deleteUser(id);
    return;
  }
}
