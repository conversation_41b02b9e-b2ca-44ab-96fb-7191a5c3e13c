import { Body, Controller, HttpCode, Post } from "@nestjs/common";
import {
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags
} from "@nestjs/swagger";
import { BadRequestResponseDto, NotFoundResponseDto } from "../../dto/exception.dto";
import {
  ResetUserPasswordDto,
  ResetUserPasswordResponseDto,
  SendResetPasswordEmailDto,
  SendResetPasswordEmailResponseDto
} from "../../dto";
import { UserService } from "../services";

@ApiTags("Password API")
@Controller("password")
export class PasswordController {
  constructor(private readonly userService: UserService) {}

  @Post("send-reset-email")
  @HttpCode(200)
  @ApiOperation({ summary: "Send Reset Password Email" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiOkResponse({ type: SendResetPasswordEmailResponseDto })
  async sendResetPasswordEmail(@Body() sendResetPasswordEmailDto: SendResetPasswordEmailDto) {
    return await this.userService.sendResetPasswordEmail(sendResetPasswordEmailDto);
  }

  @Post("reset")
  @HttpCode(200)
  @ApiOperation({ summary: "Reset User Password" })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiOkResponse({ type: ResetUserPasswordResponseDto })
  async resetUserPassword(@Body() resetUserPasswordDto: ResetUserPasswordDto) {
    return await this.userService.resetUserPassword(resetUserPasswordDto);
  }
}
