import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards
} from "@nestjs/common";
import { ApiForbiddenResponse, ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import {
  ApiAccessTokenAuthenticated,
  ApiGetByIdResponses,
  ApiGetManyResponses,
  ApiCreateResponses,
  ApiEditResponses,
  ApiDeleteResponses
} from "../../decorators";
import {
  ForbiddenResponseDto,
  GetOrganizationsResponseDto,
  GetOrganizationsDto,
  CreateOrganizationDto,
  EditOrganizationDto
} from "../../dto";
import { Organization } from "../../entities";
import { AccessTokenGuard } from "../../guards";
import { AuthenticatedRequest } from "../../types";
import { OrganizationService } from "../services";

@UseGuards(AccessTokenGuard)
@ApiAccessTokenAuthenticated()
@ApiTags("Organization API")
@Controller("organizations")
export class OrganizationController {
  constructor(private readonly organizationService: OrganizationService) {}

  @Get("me")
  @ApiOperation({ summary: "Get Current Organization" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: Organization })
  async getCurrentOrganization(@Req() request: AuthenticatedRequest) {
    const organization = await this.organizationService.getOrganizationById(
      request?.user?.organization?.id || 0
    );
    if (!organization) throw new NotFoundException("Organization not found");
    return organization;
  }

  @Get()
  @ApiOperation({ summary: "Get Organizations" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: GetOrganizationsResponseDto })
  async getOrganizations(@Query() getOrganizationsDto: GetOrganizationsDto) {
    return await this.organizationService.getOrganizations(getOrganizationsDto);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get Organization" })
  @ApiParam({ name: "id", type: "integer", description: "Organization ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: Organization })
  async getOrganizationById(@Param("id", ParseIntPipe) id: number) {
    const org = await this.organizationService.getOrganizationById(id);
    if (!org) throw new NotFoundException("Organization not found");
    return org;
  }

  @Post()
  @ApiOperation({ summary: "Create Organization" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiCreateResponses({ type: Organization })
  async createOrganization(@Body() createOrganizationDto: CreateOrganizationDto) {
    return await this.organizationService.createOrganization(createOrganizationDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Edit Organization" })
  @ApiParam({ name: "id", type: "integer", description: "Organization ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: Organization })
  async editOrganization(
    @Param("id", ParseIntPipe) id: number,
    @Body() editOrganizationDto: EditOrganizationDto
  ) {
    return await this.organizationService.editOrganization(id, editOrganizationDto);
  }

  @Delete(":id")
  @HttpCode(204)
  @ApiOperation({ summary: "Delete Organization" })
  @ApiParam({ name: "id", type: "integer", description: "Organization ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiDeleteResponses()
  async deleteOrganization(@Param("id", ParseIntPipe) id: number) {
    await this.organizationService.deleteOrganization(id);
    return;
  }
}
