import { DynamicModule, Global, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Organization } from "../entities/organization.entity";
import { Password } from "../entities/password.entity";
import { User } from "../entities/user.entity";
import { AccessTokenGuard } from "../guards/access-token.guard";
import { ORGANIZATION_CRUD_OPTIONS, USER_CRUD_OPTIONS, UserModuleOptions } from "../types/user.types";
import { UserController, OrganizationController, PasswordController } from "./controllers";
import { UserService, OrganizationService } from "./services";
import { ResetPasswordToken } from "../entities";
import { PasswordService } from "./services/password.service";
import { TransactionalEventEmitterModule } from "../transactional-event-emitter";

@Global()
@Module({})
export class UserModule {
  static register(options: UserModuleOptions): DynamicModule {
    return {
      global: true,
      module: UserModule,
      imports: [
        TypeOrmModule.forFeature([User, Organization, Password, ResetPasswordToken]),
        TransactionalEventEmitterModule
      ],
      controllers: [UserController, OrganizationController, PasswordController],
      providers: [
        {
          provide: USER_CRUD_OPTIONS,
          useValue: options.user
        },
        {
          provide: ORGANIZATION_CRUD_OPTIONS,
          useValue: options.organization
        },
        UserService,
        OrganizationService,
        PasswordService,
        AccessTokenGuard
      ],
      exports: [UserService, OrganizationService, PasswordService, AccessTokenGuard]
    };
  }
}
