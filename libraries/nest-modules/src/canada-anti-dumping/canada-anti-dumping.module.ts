import { DynamicModule, Module } from "@nestjs/common";
import { CANADA_ANTI_DUMPING_MODULE_OPTIONS, CrudOptions } from "../types";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CanadaAntiDumpingController } from "./canada-anti-dumping.controller";
import { CanadaAntiDumping } from "../entities";
import { CanadaAntiDumpingService } from "./canada-anti-dumping.service";

@Module({})
export class CanadaAntiDumpingModule {
  static register(options: CrudOptions): DynamicModule {
    return {
      global: true,
      module: CanadaAntiDumpingModule,
      imports: [TypeOrmModule.forFeature([CanadaAntiDumping])],
      controllers: [CanadaAntiDumpingController],
      providers: [
        {
          provide: CANADA_ANTI_DUMPING_MODULE_OPTIONS,
          useValue: options
        },
        CanadaAntiDumpingService
      ],
      exports: [CanadaAntiDumpingService]
    };
  }
}
