import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import { DataSource, In, Not, QueryRunner, Repository } from "typeorm";
import {
  BatchUpdateCanadaAntiDumpingsDto,
  BatchUpdateCanadaAntiDumpingsResponseDto,
  CreateCanadaAntiDumpingDto,
  EditCanadaAntiDumpingDto,
  GetCanadaAntiDumpingsDto,
  GetCanadaAntiDumpingsResponseDto
} from "../dto";
import { CanadaAntiDumping } from "../entities";
import { convertFromCamelCase, getFindOptions } from "../helper-functions";
import { MatchingRuleService } from "../matching-rule";
import {
  AuthenticatedRequest,
  CANADA_ANTI_DUMPING_MODULE_OPTIONS,
  CANADA_ANTI_DUMPING_REQUIRED_KEYS,
  CanadaAntiDumpingColumn,
  CrudOptions,
  FIND_CANADA_ANTI_DUMPING_RELATIONS,
  MatchingRuleSourceDatabaseTable,
  UserPermission
} from "../types";

@Injectable({ scope: Scope.REQUEST })
export class CanadaAntiDumpingService {
  constructor(
    @InjectRepository(CanadaAntiDumping)
    private readonly canadaAntiDumpingRepository: Repository<CanadaAntiDumping>,
    @Inject(MatchingRuleService)
    private readonly matchingRuleService: MatchingRuleService,
    @Inject(CANADA_ANTI_DUMPING_MODULE_OPTIONS)
    private readonly options: CrudOptions,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}

  async getCanadaAntiDumpings(
    getCanadaAntiDumpingsDto: GetCanadaAntiDumpingsDto
  ): Promise<GetCanadaAntiDumpingsResponseDto> {
    if (!this.options.readMany) throw new ForbiddenException("Get Canada Anti-Dumping is disabled");
    const { where, order, skip, take } = getFindOptions(
      getCanadaAntiDumpingsDto,
      [],
      [],
      CanadaAntiDumpingColumn.id
    );
    const [data, total] = await this.canadaAntiDumpingRepository.findAndCount({
      where,
      order,
      relations: FIND_CANADA_ANTI_DUMPING_RELATIONS,
      skip,
      take
    });
    return {
      data,
      total,
      skip,
      limit: take
    };
  }

  async getCanadaAntiDumpingById(antiDumpingId: number, queryRunner?: QueryRunner) {
    if (!this.options.readOne) throw new ForbiddenException("Get Canada Anti-Dumping is disabled");
    const canadaAntiDumpingRepository = queryRunner
      ? queryRunner.manager.getRepository(CanadaAntiDumping)
      : this.canadaAntiDumpingRepository;
    const canadaAntiDumping = await canadaAntiDumpingRepository.findOne({
      where: { id: antiDumpingId },
      relations: FIND_CANADA_ANTI_DUMPING_RELATIONS
    });
    return canadaAntiDumping;
  }

  async createCanadaAntiDumping(createCanadaAntiDumpingDto: CreateCanadaAntiDumpingDto) {
    if (!this.options.create) throw new ForbiddenException("Create Canada Anti-Dumping is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to create Canada Anti-Dumping");
    let newCanadaAntiDumping = new CanadaAntiDumping();
    newCanadaAntiDumping.createdBy = this.request?.user || null;
    newCanadaAntiDumping.lastEditedBy = this.request?.user || null;
    for (const [key, value] of Object.entries(createCanadaAntiDumpingDto)) {
      if (value === undefined) continue;
      if (CANADA_ANTI_DUMPING_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      newCanadaAntiDumping[key] = value;
    }
    if (await this.canadaAntiDumpingRepository.existsBy({ code: newCanadaAntiDumping.code }))
      throw new BadRequestException("Canada Anti-Dumping with the same code already exists");
    if (await this.canadaAntiDumpingRepository.existsBy({ case: newCanadaAntiDumping.case }))
      throw new BadRequestException("Canada Anti-Dumping with the same case already exists");

    newCanadaAntiDumping = await this.canadaAntiDumpingRepository.save(newCanadaAntiDumping);

    return await this.getCanadaAntiDumpingById(newCanadaAntiDumping.id);
  }

  async editCanadaAntiDumping(antiDumpingId: number, editCanadaAntiDumpingDto: EditCanadaAntiDumpingDto) {
    if (!this.options.update) throw new ForbiddenException("Edit Canada Anti-Dumping is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to edit Canada Anti-Dumping");
    const canadaAntiDumping = await this.getCanadaAntiDumpingById(antiDumpingId);
    if (!canadaAntiDumping) throw new NotFoundException("Canada Anti-Dumping not found");
    canadaAntiDumping.lastEditedBy = this.request?.user || null;
    for (const [key, value] of Object.entries(editCanadaAntiDumpingDto)) {
      if (value === undefined) continue;
      if (CANADA_ANTI_DUMPING_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      canadaAntiDumping[key] = value;
    }
    if (
      await this.canadaAntiDumpingRepository.existsBy({
        id: Not(antiDumpingId),
        code: canadaAntiDumping.code
      })
    )
      throw new BadRequestException("Canada Anti-Dumping with the same code already exists");
    if (
      await this.canadaAntiDumpingRepository.existsBy({
        id: Not(antiDumpingId),
        case: canadaAntiDumping.case
      })
    )
      throw new BadRequestException("Canada Anti-Dumping with the same case already exists");
    await this.canadaAntiDumpingRepository.save(canadaAntiDumping);
    return await this.getCanadaAntiDumpingById(antiDumpingId);
  }

  async deleteCanadaAntiDumping(antiDumpingId: number) {
    if (!this.options.delete) throw new ForbiddenException("Delete Canada Anti-Dumping is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to delete Canada Anti-Dumping");
    if (!(await this.canadaAntiDumpingRepository.existsBy({ id: antiDumpingId })))
      throw new NotFoundException("Canada Anti-Dumping not found");
    if (
      await this.matchingRuleService.isMatchingRuleForSourceRecordExists(
        MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING,
        antiDumpingId
      )
    )
      throw new BadRequestException("Canada Anti-Dumping is being used in a matching rule");
    await this.canadaAntiDumpingRepository.delete({ id: antiDumpingId });
    return;
  }

  async batchUpdateCanadaAntiDumpings(
    batchUpdateCanadaAntiDumpingsDto: BatchUpdateCanadaAntiDumpingsDto
  ): Promise<BatchUpdateCanadaAntiDumpingsResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const savedCanadaAntiDumpingIds: Array<number> = [];
    try {
      const { create: createList, edit: editList, delete: deleteList } = batchUpdateCanadaAntiDumpingsDto;

      if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
        throw new ForbiddenException("You are not authorized to batch update Canada Anti-Dumping");
      if (Array.isArray(createList) && createList.length > 0 && !this.options.create)
        throw new ForbiddenException("Create Canada Anti-Dumping is disabled");
      if (Array.isArray(editList) && editList.length > 0 && !this.options.update)
        throw new ForbiddenException("Edit Canada Anti-Dumping is disabled");
      if (Array.isArray(deleteList) && deleteList.length > 0 && !this.options.delete)
        throw new ForbiddenException("Delete Canada Anti-Dumping is disabled");

      const canadaAntiDumpingRepository = queryRunner.manager.getRepository(CanadaAntiDumping);
      const toBeSavedCanadaAntiDumpings: Array<CanadaAntiDumping> = [];

      for (let i = 0; i < (createList || []).length; i++) {
        const errorMsgPrefix = `Error on create list index ${i}: `;
        const createCanadaAntiDumpingDto = createList[i];
        let newCanadaAntiDumping = new CanadaAntiDumping();
        newCanadaAntiDumping.createdBy = this.request?.user || null;
        newCanadaAntiDumping.lastEditedBy = this.request?.user || null;
        for (const [key, value] of Object.entries(createCanadaAntiDumpingDto)) {
          if (value === undefined) continue;
          if (CANADA_ANTI_DUMPING_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
          newCanadaAntiDumping[key] = value;
        }
        if (
          toBeSavedCanadaAntiDumpings.some((c) => c.code === newCanadaAntiDumping.code) ||
          (await canadaAntiDumpingRepository.existsBy({ code: newCanadaAntiDumping.code }))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada Anti-Dumping with the same code already exists`
          );

        if (
          toBeSavedCanadaAntiDumpings.some((c) => c.case === newCanadaAntiDumping.case) ||
          (await canadaAntiDumpingRepository.existsBy({
            case: newCanadaAntiDumping.case
          }))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada Anti-Dumping with the same case already exists`
          );

        toBeSavedCanadaAntiDumpings.push(newCanadaAntiDumping);
      }

      for (let i = 0; i < (editList || []).length; i++) {
        const errorMsgPrefix = `Error on edit list index ${i}: `;
        const { id: antiDumpingId, ...editCanadaAntiDumpingDto } = editList[i];
        const canadaAntiDumping = await this.getCanadaAntiDumpingById(antiDumpingId, queryRunner);
        if (!canadaAntiDumping) throw new NotFoundException(`${errorMsgPrefix}Canada Anti-Dumping not found`);
        canadaAntiDumping.lastEditedBy = this.request?.user || null;
        for (const [key, value] of Object.entries(editCanadaAntiDumpingDto)) {
          if (value === undefined) continue;
          if (CANADA_ANTI_DUMPING_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
          canadaAntiDumping[key] = value;
        }
        if (
          toBeSavedCanadaAntiDumpings.some(
            (c) => c.code === canadaAntiDumping.code && c.id !== canadaAntiDumping.id
          ) ||
          (await canadaAntiDumpingRepository.existsBy({
            id: Not(
              In(
                toBeSavedCanadaAntiDumpings
                  .filter((c) => typeof c.id === "number")
                  .map((c) => c.id)
                  .concat([antiDumpingId])
              )
            ),
            code: canadaAntiDumping.code
          }))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada Anti-Dumping with the same code already exists`
          );
        if (
          toBeSavedCanadaAntiDumpings.some(
            (c) => c.case === canadaAntiDumping.case && c.id !== canadaAntiDumping.id
          ) ||
          (await canadaAntiDumpingRepository.existsBy({
            id: Not(
              In(
                toBeSavedCanadaAntiDumpings
                  .filter((c) => typeof c.id === "number")
                  .map((c) => c.id)
                  .concat([antiDumpingId])
              )
            ),
            case: canadaAntiDumping.case
          }))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada Anti-Dumping with the same case already exists`
          );
        toBeSavedCanadaAntiDumpings.push(canadaAntiDumping);
      }

      for (let i = 0; i < (deleteList || []).length; i++) {
        const antiDumpingId = deleteList[i];
        const errorMsgPrefix = `Error on delete list index ${i}: `;
        if (!(await canadaAntiDumpingRepository.existsBy({ id: antiDumpingId })))
          throw new NotFoundException(`${errorMsgPrefix}Canada Anti-Dumping not found`);
        if (toBeSavedCanadaAntiDumpings.some((c) => c.id === antiDumpingId))
          throw new BadRequestException(`${errorMsgPrefix}Canada Anti-Dumping is being edited`);
        if (
          await this.matchingRuleService.isMatchingRuleForSourceRecordExists(
            MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING,
            antiDumpingId,
            queryRunner
          )
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada Anti-Dumping is being used in a matching rule`
          );
      }

      savedCanadaAntiDumpingIds.push(
        ...(await canadaAntiDumpingRepository.save(toBeSavedCanadaAntiDumpings)).map((c) => c.id)
      );
      if (Array.isArray(deleteList) && deleteList.length > 0)
        await canadaAntiDumpingRepository.delete({ id: In(deleteList) });

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner.isReleased) queryRunner.release();
    }

    return {
      data: await this.canadaAntiDumpingRepository.find({
        where: { id: In(savedCanadaAntiDumpingIds) },
        relations: FIND_CANADA_ANTI_DUMPING_RELATIONS
      })
    };
  }
}
