import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseGuards,
  NotFoundException,
  Post,
  Body,
  Put,
  Delete,
  HttpCode
} from "@nestjs/common";
import { AccessTokenGuard } from "../guards";
import {
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses
} from "../decorators";
import { ApiForbiddenResponse, ApiOperation, ApiParam } from "@nestjs/swagger";
import { ApiTags } from "@nestjs/swagger";
import {
  BatchUpdateCanadaAntiDumpingsDto,
  BatchUpdateCanadaAntiDumpingsResponseDto,
  CreateCanadaAntiDumpingDto,
  EditCanadaAntiDumpingDto,
  ForbiddenResponseDto,
  GetCanadaAntiDumpingsDto,
  GetCanadaAntiDumpingsResponseDto
} from "../dto";
import { CanadaAntiDumpingService } from "./canada-anti-dumping.service";
import { CanadaAntiDumping } from "../entities";

@ApiTags("Canada Anti-Dumping API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("canada-anti-dumpings")
export class CanadaAntiDumpingController {
  constructor(private readonly canadaAntiDumpingService: CanadaAntiDumpingService) {}

  @Get()
  @ApiOperation({ summary: "Get Canada Anti-Dumpings" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: GetCanadaAntiDumpingsResponseDto })
  async getCanadaAntiDumpings(@Query() getCanadaAntiDumpingsDto: GetCanadaAntiDumpingsDto) {
    return await this.canadaAntiDumpingService.getCanadaAntiDumpings(getCanadaAntiDumpingsDto);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get Canada Anti-Dumping by ID" })
  @ApiParam({ name: "id", type: "integer", description: "Canada Anti-Dumping ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: CanadaAntiDumping })
  async getCanadaAntiDumpingById(@Param("id", ParseIntPipe) id: number) {
    const antiDumping = await this.canadaAntiDumpingService.getCanadaAntiDumpingById(id);
    if (!antiDumping) throw new NotFoundException("Canada Anti-Dumping not found");
    return antiDumping;
  }

  @Post()
  @ApiOperation({ summary: "Create Canada Anti-Dumping" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiCreateResponses({ type: CanadaAntiDumping })
  async createCanadaAntiDumping(@Body() createCanadaAntiDumpingDto: CreateCanadaAntiDumpingDto) {
    return await this.canadaAntiDumpingService.createCanadaAntiDumping(createCanadaAntiDumpingDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update Canada Anti-Dumping" })
  @ApiParam({ name: "id", type: "integer", description: "Canada Anti-Dumping ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: CanadaAntiDumping })
  async editCanadaAntiDumping(
    @Param("id", ParseIntPipe) id: number,
    @Body() editCanadaAntiDumpingDto: EditCanadaAntiDumpingDto
  ) {
    return await this.canadaAntiDumpingService.editCanadaAntiDumping(id, editCanadaAntiDumpingDto);
  }

  @HttpCode(204)
  @Delete(":id")
  @ApiOperation({ summary: "Delete Canada Anti-Dumping" })
  @ApiParam({ name: "id", type: "integer", description: "Canada Anti-Dumping ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiDeleteResponses()
  async deleteCanadaAntiDumping(@Param("id", ParseIntPipe) id: number) {
    await this.canadaAntiDumpingService.deleteCanadaAntiDumping(id);
    return;
  }

  @HttpCode(200)
  @Post("batch")
  @ApiOperation({ summary: "Batch update Canada Anti-Dumpings" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: BatchUpdateCanadaAntiDumpingsResponseDto })
  async batchUpdateCanadaAntiDumpings(
    @Body() batchUpdateCanadaAntiDumpingsDto: BatchUpdateCanadaAntiDumpingsDto
  ) {
    return await this.canadaAntiDumpingService.batchUpdateCanadaAntiDumpings(
      batchUpdateCanadaAntiDumpingsDto
    );
  }
}
