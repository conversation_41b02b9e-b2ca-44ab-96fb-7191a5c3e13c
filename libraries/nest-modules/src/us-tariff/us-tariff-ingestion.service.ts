import { Injectable, Logger } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { DataSource, Repository } from "typeorm";
import * as XLSX from "xlsx";
import { TariffSyncHistory, UsTariff, UsPgaRequirement, UsTariffPgaRequirement } from "../entities";
import { SyncStatus } from "../types";
import { HTSPGAFileRow, ProcessedTariffData, TariffImportStats } from "../types/us-tariff.types";

@Injectable()
export class UsTariffIngestionService {
  private readonly logger = new Logger(UsTariffIngestionService.name);

  constructor(
    private readonly dataSource: DataSource,
    @InjectRepository(TariffSyncHistory)
    private readonly historyRepository: Repository<TariffSyncHistory>,
    @InjectRepository(UsTariff)
    private readonly usTariffRepository: Repository<UsTariff>,
    @InjectRepository(UsPgaRequirement)
    private readonly usPgaRequirementRepository: Repository<UsPgaRequirement>,
    @InjectRepository(UsTariffPgaRequirement)
    private readonly usTariffPgaRequirementRepository: Repository<UsTariffPgaRequirement>
  ) {}

  /**
   * Ingest US tariff data from XLSX file buffer
   */
  async ingestFromXlsx(fileBuffer: Buffer, initiatedBy: string): Promise<TariffSyncHistory> {
    // Create sync history record
    const history = this.historyRepository.create({
      status: SyncStatus.RUNNING
    });
    await this.historyRepository.save(history);

    try {
      this.logger.log(`Starting US tariff ingestion (3-step process), sync ID: ${history.id}`);

      // Step 1: Parse XLSX file (Apify to XLSX equivalent)
      const rows = await this.parseWorkbook(fileBuffer);
      this.logger.log(`Step 1 completed: Parsed ${rows.length} rows from XLSX file`);

      // Step 2: Load up the DB (main tariff data)
      const stats = await this.upsertRows(rows);
      this.logger.log(`Step 2 completed: Upserted tariff data - ${JSON.stringify(stats)}`);

      // Step 3: Populate pivot table (tariff-PGA relationships)
      const pivotStats = await this.upsertPivotTable(rows);
      this.logger.log(`Step 3 completed: Populated pivot table - ${JSON.stringify(pivotStats)}`);

      // Check if there were significant errors in pivot table processing
      const pivotErrorRate = pivotStats.errors / (pivotStats.inserted + pivotStats.errors || 1);
      const hasSignificantPivotErrors = pivotStats.errors > 0 && pivotErrorRate > 0.1; // More than 10% error rate

      if (hasSignificantPivotErrors) {
        this.logger.warn(
          `High error rate in pivot table processing: ${pivotStats.errors} errors out of ${
            pivotStats.inserted + pivotStats.errors
          } attempts (${(pivotErrorRate * 100).toFixed(1)}%)`
        );
      }

      // Update history - mark as success if main tariff data succeeded, but warn about pivot errors
      Object.assign(history, {
        finishDate: new Date(),
        status: SyncStatus.SUCCESS,
        errorMessage: hasSignificantPivotErrors
          ? `Pivot table processing had ${pivotStats.errors} errors`
          : null
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(`Ingestion failed: ${errorMessage}`, errorStack);

      // Update history with failure
      Object.assign(history, {
        finishDate: new Date(),
        status: SyncStatus.FAILED,
        errorMessage
      });

      throw error;
    } finally {
      await this.historyRepository.save(history);
    }

    return history;
  }

  /**
   * Parse XLSX workbook from buffer and extract tariff data
   */
  private async parseWorkbook(fileBuffer: Buffer): Promise<ProcessedTariffData[]> {
    try {
      // Parse XLSX file using SheetJS
      const workbook = XLSX.read(fileBuffer, { type: "buffer" });

      if (!workbook.SheetNames.length) {
        throw new Error("No worksheets found in XLSX file");
      }

      // Use first worksheet
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      this.logger.log(`Using worksheet: ${workbook.SheetNames[0]}`);

      // Convert to array of arrays (preserves original structure)
      const rawRows = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        raw: false,
        defval: ""
      }) as any[][];

      this.logger.log(`Extracted ${rawRows.length} raw rows from worksheet`);

      // 🔍 DEBUG: Log the first 10 rows to see structure
      this.logger.log("=== XLSX STRUCTURE DEBUG ===");
      this.logger.log(`Total sheets: ${workbook.SheetNames.join(", ")}`);
      this.logger.log(`Worksheet range: ${worksheet["!ref"]}`);
      this.logger.log("First 10 raw rows:");
      rawRows.slice(0, 10).forEach((row, index) => {
        this.logger.log(`Row ${index}: [${row.length} cols] ${JSON.stringify(row)}`);
      });

      // 🔍 DEBUG: Check header rows specifically
      this.logger.log("=== HEADER ANALYSIS ===");
      this.logger.log(`Row 0 (headers?): ${JSON.stringify(rawRows[0] || [])}`);
      this.logger.log(`Row 1: ${JSON.stringify(rawRows[1] || [])}`);
      this.logger.log(`Row 2: ${JSON.stringify(rawRows[2] || [])}`);
      this.logger.log(`Row 3 (first data?): ${JSON.stringify(rawRows[3] || [])}`);

      // 🔍 DEBUG: Column count analysis
      const columnCounts = rawRows.slice(0, 20).map((row) => row?.length || 0);
      this.logger.log(`Column counts in first 20 rows: ${JSON.stringify(columnCounts)}`);
      this.logger.log("=== END DEBUG ===");

      // Process raw rows into structured data
      return this.processRawRows(rawRows);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to parse XLSX file: ${errorMessage}`);
      throw new Error(`XLSX parsing failed: ${errorMessage}`);
    }
  }

  /**
   * Process raw XLSX rows into structured tariff data
   */
  private processRawRows(rawRows: any[][]): ProcessedTariffData[] {
    const processedData: ProcessedTariffData[] = [];

    // Skip header rows (first 3 rows based on sample CSV)
    const dataRows = rawRows.slice(3);

    // 🔍 DEBUG: Log processing stats
    this.logger.log("=== ROW PROCESSING DEBUG ===");
    this.logger.log(`Total raw rows: ${rawRows.length}`);
    this.logger.log(`Data rows (after skipping 3): ${dataRows.length}`);

    let validRows = 0;
    let invalidRows = 0;
    let shortRows = 0;

    for (let i = 0; i < dataRows.length; i++) {
      const row = dataRows[i];
      const rowIndex = i + 3; // Actual row number in Excel (0-based + 3 skipped)

      if (!row || row.length < 19) {
        shortRows++;
        if (i < 5) {
          // Log first few short rows for debugging
          this.logger.warn(`Row ${rowIndex}: TOO SHORT [${row?.length || 0} cols] ${JSON.stringify(row)}`);
        }
        continue;
      }

      try {
        const processed = this.processRow(row);
        if (processed) {
          processedData.push(processed);
          validRows++;

          // 🔍 DEBUG: Log first few successful rows
          if (validRows <= 3) {
            this.logger.log(
              `Row ${rowIndex}: SUCCESS → hsCode: ${
                processed.hsCode
              }, description: "${processed.description.substring(0, 50)}..."`
            );
          }
        } else {
          invalidRows++;
          if (invalidRows <= 5) {
            // Log first few invalid rows
            this.logger.warn(
              `Row ${rowIndex}: INVALID (processRow returned null) - tariff: "${row[0]}", desc: "${row[1]}"`
            );
          }
        }
      } catch (error) {
        invalidRows++;
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (invalidRows <= 5) {
          // Log first few error rows
          this.logger.warn(`Row ${rowIndex}: ERROR ${errorMessage}`, { row });
        }
      }
    }

    this.logger.log(`=== PROCESSING SUMMARY ===`);
    this.logger.log(`Valid rows: ${validRows}`);
    this.logger.log(`Invalid rows: ${invalidRows}`);
    this.logger.log(`Short rows (< 19 cols): ${shortRows}`);
    this.logger.log(`Success rate: ${((validRows / dataRows.length) * 100).toFixed(1)}%`);
    this.logger.log("=== END ROW PROCESSING DEBUG ===");

    return processedData;
  }

  /**
   * Process a single row into structured tariff data
   */
  private processRow(row: any[]): ProcessedTariffData | null {
    const [
      tariffNumber,
      description,
      effectiveDate,
      expirationDate,
      censusQty1,
      censusQty2,
      censusQty3,
      adDuty,
      cvDuty,
      pga1,
      pga2,
      pga3,
      pga4,
      pga5,
      pga6,
      pga7,
      pga8,
      pga9,
      pga10
    ] = row;

    // Validate required fields
    if (!tariffNumber || !description) {
      return null;
    }

    // Clean and validate HTS code (must be 8-10 digits)
    const hsCode = String(tariffNumber).replace(/\./g, "");
    if (!/^\d{8,10}$/.test(hsCode)) {
      this.logger.warn(`Invalid HTS code: ${tariffNumber} -> ${hsCode}`);
      return null;
    }

    // Parse dates
    const effectiveDateParsed = this.parseDate(effectiveDate);
    const expiryDateParsed = this.parseDate(expirationDate);

    if (!effectiveDateParsed || !expiryDateParsed) {
      this.logger.warn(
        `Invalid dates for HTS ${hsCode}: effectiveDate="${effectiveDate}" -> ${effectiveDateParsed}, expiryDate="${expirationDate}" -> ${expiryDateParsed}`
      );
      return null;
    }

    // Process census quantities (preserve "NO" as string, only convert empty to null)
    const processCensusQty = (qty: any): string | null => {
      const str = String(qty || "").trim();
      return str === "" ? null : str;
    };

    // Process duty flags (N -> false, Y -> true)
    const processDutyFlag = (flag: any): boolean | null => {
      const str = String(flag || "")
        .trim()
        .toUpperCase();
      if (str === "N") return false;
      if (str === "Y") return true;
      return null;
    };

    // Collect PGA codes (filter out empty values)
    const pgaCodes = [pga1, pga2, pga3, pga4, pga5, pga6, pga7, pga8, pga9, pga10]
      .map((code) => String(code || "").trim())
      .filter((code) => code && code !== "")
      .filter((code) => code.length > 0);

    return {
      hsCode,
      description: String(description).trim(),
      effectiveDate: effectiveDateParsed,
      expiryDate: expiryDateParsed,
      censusQty1: processCensusQty(censusQty1),
      censusQty2: processCensusQty(censusQty2),
      censusQty3: processCensusQty(censusQty3),
      adDuty: processDutyFlag(adDuty),
      cvDuty: processDutyFlag(cvDuty),
      pgaCodes: pgaCodes.length > 0 ? pgaCodes : null
    };
  }

  /**
   * Parse date from various formats
   */
  private parseDate(dateValue: any): Date | null {
    if (!dateValue) return null;

    if (dateValue instanceof Date) {
      return dateValue;
    }

    const dateStr = String(dateValue).trim();
    if (!dateStr) return null;

    // Handle M/D/YY format (like "1/1/02", "12/31/30")
    const shortDateMatch = dateStr.match(/^(\d{1,2})\/(\d{1,2})\/(\d{2})$/);
    if (shortDateMatch) {
      const [, month, day, year] = shortDateMatch;
      // Convert 2-digit year to 4-digit year
      // Years 00-30 are 2000-2030, years 31-99 are 1931-1999
      const fullYear = parseInt(year) <= 30 ? 2000 + parseInt(year) : 1900 + parseInt(year);

      const parsed = new Date(fullYear, parseInt(month) - 1, parseInt(day));
      return isNaN(parsed.getTime()) ? null : parsed;
    }

    // Handle M/D/YYYY format (like "1/1/2002", "12/31/2030")
    const longDateMatch = dateStr.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
    if (longDateMatch) {
      const [, month, day, year] = longDateMatch;
      const parsed = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      return isNaN(parsed.getTime()) ? null : parsed;
    }

    // Fallback to standard Date parsing
    const parsed = new Date(dateValue);
    return isNaN(parsed.getTime()) ? null : parsed;
  }

  /**
   * Upsert processed tariff data to database with differential sync
   * This implements a complete daily replacement strategy:
   * 1. Delete tariffs not present in current file (expired/removed tariffs)
   * 2. Upsert tariffs from current file (new/updated tariffs)
   */
  private async upsertRows(processedData: ProcessedTariffData[]): Promise<TariffImportStats> {
    const stats: TariffImportStats = {
      inserted: 0,
      updated: 0,
      deleted: 0,
      processed: processedData.length,
      errors: 0
    };

    const BATCH_SIZE = 1000;
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();

    try {
      await queryRunner.startTransaction();

      // Step 1: Delete tariffs not present in current file
      // This ensures database only contains currently active tariffs from CBP
      const currentHsCodes = processedData.map((data) => data.hsCode);
      this.logger.log(`Current file contains ${currentHsCodes.length} tariffs`);

      if (currentHsCodes.length > 0) {
        // Delete tariffs (and their PGA relationships via cascade) not in current file
        const deleteResult = await queryRunner.manager
          .createQueryBuilder()
          .delete()
          .from(UsTariff)
          .where("hsCode NOT IN (:...codes)", { codes: currentHsCodes })
          .execute();

        stats.deleted = deleteResult.affected || 0;
        this.logger.log(`Deleted ${stats.deleted} tariffs no longer in source data`);
      }

      // Step 2: Upsert current tariffs in batches
      for (let i = 0; i < processedData.length; i += BATCH_SIZE) {
        const batch = processedData.slice(i, i + BATCH_SIZE);
        const batchStats = await this.upsertBatch(queryRunner, batch);

        stats.inserted += batchStats.inserted;
        stats.updated += batchStats.updated;
        stats.errors += batchStats.errors;

        this.logger.log(`Processed batch ${Math.floor(i / BATCH_SIZE) + 1}: ${JSON.stringify(batchStats)}`);
      }

      await queryRunner.commitTransaction();
      this.logger.log(`Transaction committed successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Transaction rolled back: ${errorMessage}`);
      throw error;
    } finally {
      await queryRunner.release();
    }

    return stats;
  }

  /**
   * Upsert a batch of tariff records
   */
  private async upsertBatch(
    queryRunner: any,
    batch: ProcessedTariffData[]
  ): Promise<Pick<TariffImportStats, "inserted" | "updated" | "errors">> {
    const stats = { inserted: 0, updated: 0, errors: 0 };

    for (const data of batch) {
      try {
        const existing = await queryRunner.manager.findOne(UsTariff, {
          where: { hsCode: data.hsCode }
        });

        if (existing) {
          // Update existing record
          await queryRunner.manager.update(
            UsTariff,
            { hsCode: data.hsCode },
            {
              description: data.description,
              effectiveDate: data.effectiveDate,
              expiryDate: data.expiryDate,
              censusQty1: data.censusQty1,
              censusQty2: data.censusQty2,
              censusQty3: data.censusQty3,
              adDuty: data.adDuty,
              cvDuty: data.cvDuty,
              pgaCodes: data.pgaCodes,
              lastEditDate: new Date()
            }
          );
          stats.updated++;
        } else {
          // Insert new record
          const newTariff = queryRunner.manager.create(UsTariff, {
            ...data,
            createDate: new Date(),
            lastEditDate: new Date()
          });
          await queryRunner.manager.save(newTariff);
          stats.inserted++;
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.logger.error(`Error upserting tariff ${data.hsCode}: ${errorMessage}`);
        stats.errors++;
      }
    }

    return stats;
  }

  /**
   * Step 3: Populate pivot table with tariff-PGA relationships
   */
  private async upsertPivotTable(processedData: ProcessedTariffData[]): Promise<{
    inserted: number;
    deleted: number;
    errors: number;
  }> {
    const stats = { inserted: 0, deleted: 0, errors: 0 };

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();

    try {
      await queryRunner.startTransaction();

      // Get all PGA requirements for lookup
      const pgaRequirements = await queryRunner.manager.find(UsPgaRequirement);
      const pgaRequirementsByCode = new Map(pgaRequirements.map((req) => [req.tariffFlagCode, req]));

      // Process each tariff's PGA relationships
      for (const data of processedData) {
        if (!data.pgaCodes || data.pgaCodes.length === 0) {
          continue;
        }

        try {
          // Delete existing relationships for this hsCode (no need to lookup UsTariff entity)
          const deleteResult = await queryRunner.manager.delete(UsTariffPgaRequirement, {
            hsCode: data.hsCode
          });
          stats.deleted += deleteResult.affected || 0;

          // Create new relationships - deduplicate PGA codes first
          const uniquePgaCodes = [...new Set(data.pgaCodes)];
          for (const pgaCode of uniquePgaCodes) {
            const pgaRequirement = pgaRequirementsByCode.get(pgaCode);
            if (!pgaRequirement) {
              this.logger.warn(`PGA requirement not found for code: ${pgaCode}`);
              continue;
            }

            const pivotRecord = queryRunner.manager.create(UsTariffPgaRequirement, {
              hsCode: data.hsCode,
              tariffFlagCode: pgaCode
            });

            await queryRunner.manager.save(pivotRecord);
            stats.inserted++;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          this.logger.error(`Error processing pivot relationships for ${data.hsCode}: ${errorMessage}`);
          stats.errors++;
        }
      }

      await queryRunner.commitTransaction();
      this.logger.log(`Pivot table transaction committed successfully`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Pivot table transaction rolled back: ${errorMessage}`);
      throw error;
    } finally {
      await queryRunner.release();
    }

    return stats;
  }
}
