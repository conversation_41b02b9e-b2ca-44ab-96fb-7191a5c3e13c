import { DynamicModule, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { TariffSyncHistory, UsTariff, UsPgaRequirement, UsTariffPgaRequirement } from "../entities";
import { ApifyModule } from "../apify";
import { UsTariffIngestionService } from "./us-tariff-ingestion.service";
import { UsTariffApifyService } from "./us-tariff-apify.service";
import { UsTariffService } from "./us-tariff.service";

@Module({})
export class UsTariffModule {
  static forRoot(): DynamicModule {
    return {
      module: UsTariffModule,
      imports: [
        TypeOrmModule.forFeature([UsTariff, TariffSyncHistory, UsPgaRequirement, UsTariffPgaRequirement]),
        ApifyModule
      ],
      providers: [UsTariffIngestionService, UsTariffApifyService, UsTariffService],
      exports: [UsTariffIngestionService, UsTariffApifyService, UsTariffService, ApifyModule]
    };
  }
}
