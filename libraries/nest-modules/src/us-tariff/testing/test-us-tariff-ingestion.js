#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const FormData = require("form-data");
const fetch = require("node-fetch");

/**
 * Test script for US Tariff Ingestion Service
 * Tests the upload endpoint with the provided Excel file
 */

const EXCEL_FILE_PATH = "../../../../../.ai-reference/us-tariff-data/hts_pga_report_June29.xls";
const API_BASE_URL = "http://localhost:3000";
const UPLOAD_ENDPOINT = "/admin/us-tariffs/upload";

async function testTariffIngestion() {
  console.log("🧪 Testing US Tariff Ingestion Service");
  console.log("=====================================");

  try {
    // Check if Excel file exists
    if (!fs.existsSync(EXCEL_FILE_PATH)) {
      throw new Error(`Excel file not found at: ${EXCEL_FILE_PATH}`);
    }

    const fileStats = fs.statSync(EXCEL_FILE_PATH);
    console.log(`📁 File: ${EXCEL_FILE_PATH}`);
    console.log(`📊 Size: ${(fileStats.size / 1024 / 1024).toFixed(2)} MB`);

    // Create form data
    const form = new FormData();
    const fileStream = fs.createReadStream(EXCEL_FILE_PATH);
    form.append("file", fileStream, {
      filename: "hts_pga_report_June29.xls",
      contentType: "application/vnd.ms-excel"
    });

    console.log("\n🚀 Uploading file to ingestion service...");
    console.log(`📡 URL: ${API_BASE_URL}${UPLOAD_ENDPOINT}`);

    const startTime = Date.now();

    // Make the upload request
    const response = await fetch(`${API_BASE_URL}${UPLOAD_ENDPOINT}`, {
      method: "POST",
      body: form,
      headers: {
        // Note: We're not including auth headers for testing
        // In production, this would require proper authentication
        ...form.getHeaders()
      }
    });

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log(`⏱️  Request completed in ${duration}s`);
    console.log(`📊 Status: ${response.status} ${response.statusText}`);

    const responseText = await response.text();

    if (response.ok) {
      console.log("✅ Upload successful!");

      try {
        const result = JSON.parse(responseText);
        console.log("\n📋 Response:");
        console.log(JSON.stringify(result, null, 2));

        // Check sync history
        if (result.id) {
          console.log(`\n🔍 Sync History ID: ${result.id}`);
          console.log(`📅 Sync Date: ${result.syncDate}`);
          console.log(`🔄 Status: ${result.status}`);

          if (result.finishDate) {
            console.log(`✅ Finish Date: ${result.finishDate}`);
          }

          if (result.errorMessage) {
            console.log(`❌ Error: ${result.errorMessage}`);
          }
        }
      } catch (parseError) {
        console.log("📄 Raw response:", responseText);
      }
    } else {
      console.log("❌ Upload failed!");
      console.log("📄 Response:", responseText);

      try {
        const errorResult = JSON.parse(responseText);
        if (errorResult.message) {
          console.log(`💬 Error message: ${errorResult.message}`);
        }
      } catch (parseError) {
        // Response is not JSON
      }
    }
  } catch (error) {
    console.error("💥 Test failed:", error.message);
    console.error(error.stack);
  }
}

async function checkDatabaseState() {
  console.log("\n🗄️  Checking database state...");

  const { spawn } = require("child_process");

  return new Promise((resolve, reject) => {
    const psql = spawn(
      "psql",
      [
        "-U",
        "postgres",
        "-h",
        "localhost",
        "-d",
        "claro_dev",
        "-c",
        "SELECT COUNT(*) as tariff_count FROM us_tariff; SELECT COUNT(*) as history_count FROM tariff_sync_history;"
      ],
      {
        env: { ...process.env, PGPASSWORD: "superuser" }
      }
    );

    let output = "";
    let error = "";

    psql.stdout.on("data", (data) => {
      output += data.toString();
    });

    psql.stderr.on("data", (data) => {
      error += data.toString();
    });

    psql.on("close", (code) => {
      if (code === 0) {
        console.log("📊 Database state:");
        console.log(output);
        resolve();
      } else {
        console.error("❌ Database check failed:", error);
        reject(new Error(error));
      }
    });
  });
}

// Main execution
async function main() {
  try {
    console.log("🔍 Checking initial database state...");
    await checkDatabaseState();

    await testTariffIngestion();

    console.log("\n🔍 Checking final database state...");
    await checkDatabaseState();

    console.log("\n✅ Test completed!");
  } catch (error) {
    console.error("💥 Main execution failed:", error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
