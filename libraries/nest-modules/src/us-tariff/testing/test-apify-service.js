#!/usr/bin/env node

/**
 * Standalone test script for UsTariffApifyService
 *
 * This script tests the UsTariffApifyService methods without requiring external dependencies.
 * It uses Node.js built-in fetch and handles the correct path to .env.local from the module location.
 *
 * Usage:
 *   node test-apify-service.js
 *   node test-apify-service.js --date=123024  # Test with specific date (MMDDYY format)
 *   node test-apify-service.js --test=download  # Test specific method
 *   node test-apify-service.js --dry-run  # Test without making actual API calls
 *
 * Location: libraries/nest-modules/src/us-tariff/testing/
 */

const fs = require("fs");
const path = require("path");

// Load environment variables from .env.local (at project root)
function loadEnvVars() {
  // From libraries/nest-modules/src/us-tariff/testing/ to project root
  const envPath = path.resolve(__dirname, "../../../../../.env.local");
  const envVars = {};

  console.log(`📍 Looking for .env.local at: ${envPath}`);

  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, "utf8");
    envContent.split("\n").forEach((line) => {
      if (line.startsWith("#") || !line.includes("=")) return;
      const [key, ...valueParts] = line.split("=");
      envVars[key.trim()] = valueParts.join("=").trim();
    });
    console.log(`✅ Loaded environment variables from .env.local`);
  } else {
    console.log(`⚠️  .env.local not found at ${envPath}`);
  }

  return {
    APIFY_TOKEN: envVars.APIFY_TOKEN || process.env.APIFY_TOKEN,
    APIFY_US_TARIFF_ACTOR_NAME: envVars.APIFY_US_TARIFF_ACTOR_NAME || process.env.APIFY_US_TARIFF_ACTOR_NAME,
    APIFY_US_TARIFF_ACTOR_ID: envVars.APIFY_US_TARIFF_ACTOR_ID || process.env.APIFY_US_TARIFF_ACTOR_ID,
    APIFY_US_TARIFF_STORE_NAME: envVars.APIFY_US_TARIFF_STORE_NAME || process.env.APIFY_US_TARIFF_STORE_NAME
  };
}

// Mock ConfigService that mimics NestJS ConfigService
class MockConfigService {
  constructor(envVars) {
    this.envVars = envVars;
  }

  get(key) {
    return this.envVars[key];
  }
}

// Mock ApifyService that mimics the actual ApifyService using Node.js built-in fetch
class MockApifyService {
  constructor(configService, isDryRun = false) {
    this.configService = configService;
    this.isDryRun = isDryRun;
    this.APIFY_BASE_URL = "https://api.apify.com/v2";
  }

  getApifyHeaders() {
    const token = this.configService.get("APIFY_TOKEN");
    if (!token) throw new Error("No Apify token set");

    return {
      "Content-Type": "application/json",
      Accept: "application/json",
      Authorization: `Bearer ${token}`
    };
  }

  async runActor(actorId, payload, options = {}) {
    console.log(`🚀 ApifyService.runActor(${actorId})`);
    console.log(`   Payload: ${JSON.stringify(payload)}`);
    console.log(`   Options: ${JSON.stringify(options)}`);

    if (this.isDryRun) {
      console.log("  🔄 DRY RUN: Would run actor with payload:", payload);
      return {
        data: {
          id: `dry-run-${Date.now()}`,
          status: "RUNNING",
          startedAt: new Date().toISOString(),
          finishedAt: null
        }
      };
    }

    try {
      const response = await fetch(`${this.APIFY_BASE_URL}/acts/${actorId}/runs`, {
        method: "POST",
        headers: this.getApifyHeaders(),
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log(`  ✅ Actor run started successfully: ${result.data?.id}`);
      return result;
    } catch (error) {
      console.error(`  ❌ Error running actor: ${error.message}`);
      throw error;
    }
  }

  async getRun(runId) {
    console.log(`📊 ApifyService.getRun(${runId})`);

    if (this.isDryRun) {
      console.log("  🔄 DRY RUN: Would get run status for:", runId);
      return {
        data: {
          id: runId,
          status: "SUCCEEDED",
          startedAt: new Date(Date.now() - 300000).toISOString(),
          finishedAt: new Date().toISOString()
        }
      };
    }

    try {
      const response = await fetch(`${this.APIFY_BASE_URL}/actor-runs/${runId}`, {
        headers: this.getApifyHeaders()
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log(`  ✅ Run status: ${result.data?.status}`);
      return result;
    } catch (error) {
      console.error(`  ❌ Error getting run: ${error.message}`);
      throw error;
    }
  }

  async getKeyValueStoreRecord(storeId, recordKey, isBinary = false) {
    console.log(`📁 ApifyService.getKeyValueStoreRecord(${storeId}, ${recordKey}, ${isBinary})`);

    if (this.isDryRun) {
      console.log("  🔄 DRY RUN: Would get key-value store record:", recordKey);
      if (isBinary) {
        return Buffer.from("Mock binary data for testing");
      }
      return { mockData: "This is mock metadata", success: true };
    }

    try {
      const response = await fetch(
        `${this.APIFY_BASE_URL}/key-value-stores/${storeId}/records/${recordKey}`,
        {
          headers: this.getApifyHeaders()
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (isBinary) {
        const arrayBuffer = await response.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        console.log(`  ✅ Retrieved binary data: ${buffer.length} bytes`);
        return buffer;
      }

      const result = await response.json();
      console.log(`  ✅ Retrieved data:`, result);
      return result;
    } catch (error) {
      console.error(`  ❌ Error getting key-value store record: ${error.message}`);
      throw error;
    }
  }
}

// Mock Logger that mimics NestJS Logger
class MockLogger {
  log(message, context) {
    console.log(`[LOG] ${message}`, context || "");
  }

  error(message, stack) {
    console.error(`[ERROR] ${message}`, stack || "");
  }

  warn(message, context) {
    console.warn(`[WARN] ${message}`, context || "");
  }
}

// Simplified version of UsTariffApifyService for testing
class UsTariffApifyService {
  constructor(apifyService, configService) {
    this.apifyService = apifyService;
    this.configService = configService;
    this.logger = new MockLogger();
  }

  getActorName() {
    const actorName = this.configService.get("APIFY_US_TARIFF_ACTOR_NAME");
    if (!actorName) {
      throw new Error("APIFY_US_TARIFF_ACTOR_NAME environment variable is required");
    }
    return actorName;
  }

  getActorId() {
    const actorId = this.configService.get("APIFY_US_TARIFF_ACTOR_ID");
    if (!actorId) {
      throw new Error("APIFY_US_TARIFF_ACTOR_ID environment variable is required");
    }
    return actorId;
  }

  getStoreName() {
    const storeName = this.configService.get("APIFY_US_TARIFF_STORE_NAME");
    if (!storeName) {
      throw new Error("APIFY_US_TARIFF_STORE_NAME environment variable is required");
    }
    return storeName;
  }

  formatDate(date) {
    if (date) {
      if (!/^\d{6}$/.test(date)) {
        throw new Error(`Invalid date format. Expected MMDDYY, got: ${date}`);
      }
      return date;
    }
    // Use vanilla JavaScript instead of moment-timezone
    const now = new Date();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const year = String(now.getFullYear()).slice(-2);
    return `${month}${day}${year}`;
  }

  async runTariffDownload(date, forceDownload = false) {
    const targetDate = this.formatDate(date);
    this.logger.log(`Running US Tariff download actor for date: ${targetDate}, force: ${forceDownload}`);

    const payload = {
      date: targetDate,
      forceDownload
    };

    try {
      const result = await this.apifyService.runActor(this.getActorId(), payload, {
        build: "latest"
      });

      this.logger.log(`Actor run started successfully: ${result.data?.id}`);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to run US Tariff actor: ${errorMessage}`);
      throw error;
    }
  }

  async downloadTariffFile(date) {
    const targetDate = this.formatDate(date);
    const key = `US_TARIFF_${targetDate}`;

    this.logger.log(`Downloading US Tariff file for key: ${key}`);

    try {
      const fileBuffer = await this.apifyService.getKeyValueStoreRecord(this.getStoreName(), key, true);

      if (!fileBuffer) {
        throw new Error(`No file found for key: ${key}`);
      }

      this.logger.log(`Successfully downloaded file for key: ${key}, size: ${fileBuffer.length} bytes`);
      return fileBuffer;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to download tariff file for key ${key}: ${errorMessage}`);
      throw error;
    }
  }

  async getTariffMetadata(date) {
    const targetDate = this.formatDate(date);
    const metadataKey = `US_TARIFF_${targetDate}_metadata`;

    this.logger.log(`Getting US Tariff metadata for key: ${metadataKey}`);

    try {
      const metadata = await this.apifyService.getKeyValueStoreRecord(this.getStoreName(), metadataKey);

      this.logger.log(`Retrieved metadata for key: ${metadataKey}`, metadata);
      return metadata;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.warn(`Failed to get tariff metadata for key ${metadataKey}: ${errorMessage}`);
      return null;
    }
  }

  async tariffFileExists(date) {
    try {
      const metadata = await this.getTariffMetadata(date);
      return metadata !== null && metadata.success === true;
    } catch (error) {
      return false;
    }
  }

  async getRun(runId) {
    this.logger.log(`Getting status for actor run: ${runId}`);

    try {
      const result = await this.apifyService.getRun(runId);
      this.logger.log(`Actor run ${runId} status: ${result.data?.status}`);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to get run status for ${runId}: ${errorMessage}`);
      throw error;
    }
  }
}

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    date: null,
    test: null,
    dryRun: false,
    help: false
  };

  args.forEach((arg) => {
    if (arg.startsWith("--date=")) {
      options.date = arg.split("=")[1];
    } else if (arg.startsWith("--test=")) {
      options.test = arg.split("=")[1];
    } else if (arg === "--dry-run") {
      options.dryRun = true;
    } else if (arg === "--help" || arg === "-h") {
      options.help = true;
    }
  });

  return options;
}

// Show help
function showHelp() {
  console.log(`
🧪 UsTariffApifyService Test Script
==================================

Location: libraries/nest-modules/src/us-tariff/testing/

Usage:
  node test-apify-service.js [options]

Options:
  --date=MMDDYY     Test with specific date (e.g., --date=123024)
  --test=METHOD     Test specific method (run, download, metadata, exists, status)
  --dry-run         Test without making actual API calls
  --help, -h        Show this help message

Examples:
  node test-apify-service.js
  node test-apify-service.js --date=123024
  node test-apify-service.js --test=download --date=123024
  node test-apify-service.js --dry-run

Available test methods:
  run      - Test runTariffDownload()
  download - Test downloadTariffFile()
  metadata - Test getTariffMetadata()
  exists   - Test tariffFileExists()
  status   - Test getRun() with dummy run ID
  all      - Test all methods (default)

Dependencies:
  - Uses Node.js built-in fetch (Node.js 18+)
  - Loads .env.local from project root
  - No external dependencies required
`);
}

// Main test function
async function runTests() {
  const options = parseArgs();

  if (options.help) {
    showHelp();
    return;
  }

  console.log("🧪 Testing UsTariffApifyService");
  console.log("===============================");
  console.log(`📂 Script location: ${__dirname}`);

  // Load environment variables
  const envVars = loadEnvVars();

  console.log("\n📋 Environment Configuration:");
  console.log(
    `  APIFY_TOKEN: ${envVars.APIFY_TOKEN ? envVars.APIFY_TOKEN.substring(0, 20) + "..." : "NOT SET"}`
  );
  console.log(`  APIFY_US_TARIFF_ACTOR_NAME: ${envVars.APIFY_US_TARIFF_ACTOR_NAME || "NOT SET"}`);
  console.log(`  APIFY_US_TARIFF_ACTOR_ID: ${envVars.APIFY_US_TARIFF_ACTOR_ID || "NOT SET"}`);
  console.log(`  APIFY_US_TARIFF_STORE_NAME: ${envVars.APIFY_US_TARIFF_STORE_NAME || "NOT SET"}`);

  console.log("\n🎯 Actor Testing Configuration:");
  console.log(`  🎬 Actor Name to Test: ${envVars.APIFY_US_TARIFF_ACTOR_NAME || "NOT SET"}`);
  console.log(`  🆔 Actor ID to Test: ${envVars.APIFY_US_TARIFF_ACTOR_ID || "NOT SET"}`);
  console.log(`  📦 Store Name to Test: ${envVars.APIFY_US_TARIFF_STORE_NAME || "NOT SET"}`);
  console.log(
    `  🔗 Actor URL: https://console.apify.com/actors/${envVars.APIFY_US_TARIFF_ACTOR_ID || "NOT_SET"}`
  );
  console.log(
    `  🔗 Store URL: https://console.apify.com/storage/key-value-stores/${
      envVars.APIFY_US_TARIFF_STORE_NAME || "NOT_SET"
    }`
  );

  if (options.dryRun) {
    console.log("\n🔄 DRY RUN MODE: No actual API calls will be made");
  }

  // Validate required environment variables
  if (!envVars.APIFY_TOKEN) {
    console.error("❌ APIFY_TOKEN not found in environment");
    process.exit(1);
  }

  if (!envVars.APIFY_US_TARIFF_ACTOR_NAME && !envVars.APIFY_US_TARIFF_ACTOR_ID) {
    console.error("❌ Either APIFY_US_TARIFF_ACTOR_NAME or APIFY_US_TARIFF_ACTOR_ID must be set");
    process.exit(1);
  }

  if (!envVars.APIFY_US_TARIFF_STORE_NAME) {
    console.error("❌ APIFY_US_TARIFF_STORE_NAME not found in environment");
    process.exit(1);
  }

  // Create service instances
  const configService = new MockConfigService(envVars);
  const apifyService = new MockApifyService(configService, options.dryRun);
  const usTariffApifyService = new UsTariffApifyService(apifyService, configService);

  const testDate = options.date || usTariffApifyService.formatDate();
  console.log(`\n🎯 Test date: ${testDate}`);

  try {
    // Test specific method or all methods
    const testMethod = options.test || "all";

    if (testMethod === "exists" || testMethod === "all") {
      console.log("\n📋 Test 1: Check if tariff file exists");
      console.log("====================================");
      const exists = await usTariffApifyService.tariffFileExists(testDate);
      console.log(`✅ File exists: ${exists}`);
    }

    if (testMethod === "metadata" || testMethod === "all") {
      console.log("\n📋 Test 2: Get tariff metadata");
      console.log("==============================");
      const metadata = await usTariffApifyService.getTariffMetadata(testDate);
      console.log(`✅ Metadata:`, metadata);
    }

    if (testMethod === "download" || testMethod === "all") {
      console.log("\n📋 Test 3: Download tariff file");
      console.log("===============================");
      try {
        const fileBuffer = await usTariffApifyService.downloadTariffFile(testDate);
        console.log(`✅ Downloaded file: ${fileBuffer.length} bytes`);
      } catch (error) {
        console.log(`⚠️  Download failed (expected if file doesn't exist): ${error.message}`);
      }
    }

    if (testMethod === "run" || testMethod === "all") {
      console.log("\n📋 Test 4: Run tariff download actor");
      console.log("====================================");
      console.log(
        `🎯 About to test actor: ${envVars.APIFY_US_TARIFF_ACTOR_NAME} (ID: ${envVars.APIFY_US_TARIFF_ACTOR_ID})`
      );
      console.log(
        `🔗 If this fails, check: https://console.apify.com/actors/${envVars.APIFY_US_TARIFF_ACTOR_ID}`
      );
      const runResult = await usTariffApifyService.runTariffDownload(testDate, false);
      console.log(`✅ Actor run result:`, runResult.data);

      if (runResult.data?.id && (testMethod === "status" || testMethod === "all")) {
        console.log("\n📋 Test 5: Get run status");
        console.log("=========================");

        // Wait a moment before checking status
        await new Promise((resolve) => setTimeout(resolve, 2000));

        const statusResult = await usTariffApifyService.getRun(runResult.data.id);
        console.log(`✅ Run status:`, statusResult.data);
      }
    }

    if (testMethod === "status" && testMethod !== "all") {
      console.log("\n📋 Test: Get run status (with dummy ID)");
      console.log("========================================");
      try {
        const dummyRunId = "dummy-run-id-for-testing";
        const statusResult = await usTariffApifyService.getRun(dummyRunId);
        console.log(`✅ Run status:`, statusResult.data);
      } catch (error) {
        console.log(`⚠️  Status check failed (expected for dummy ID): ${error.message}`);
      }
    }

    console.log("\n✅ All tests completed successfully!");
    console.log("\n📊 Summary:");
    console.log("  - Service instantiated correctly");
    console.log("  - Environment variables loaded from project root");
    console.log("  - All methods executed without critical errors");
    console.log("  - Uses Node.js built-in fetch (no external dependencies)");
    console.log("  - Ready for integration with full application");
  } catch (error) {
    console.error("\n💥 Test failed:", error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Check Node.js version
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split(".")[0]);

if (majorVersion < 18) {
  console.error("❌ This script requires Node.js 18+ for built-in fetch support");
  console.error(`   Current version: ${nodeVersion}`);
  process.exit(1);
}

// Run the tests
if (require.main === module) {
  runTests().catch((error) => {
    console.error("💥 Unhandled error:", error);
    process.exit(1);
  });
}
