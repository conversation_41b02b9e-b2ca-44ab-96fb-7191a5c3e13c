#!/usr/bin/env node

/**
 * Simple US Tariff Pipeline Test
 * Usage: node libraries/nest-modules/src/us-tariff/testing/test-pipeline-simple.js [--date=MMDDYY] [--force]
 */

const fs = require("fs");

async function runTariffPipeline() {
  console.log("🚀 Running US Tariff Pipeline Test");
  console.log("==================================");

  const args = process.argv.slice(2);
  const dateArg = args.find((arg) => arg.startsWith("--date="));
  const force = args.includes("--force");

  // Parse date or use today
  const targetDate = dateArg
    ? dateArg.split("=")[1]
    : new Date()
        .toLocaleDateString("en-US", {
          month: "2-digit",
          day: "2-digit",
          year: "2-digit"
        })
        .replace(/\//g, "");

  console.log(`📅 Target date: ${targetDate}`);
  console.log(`🔄 Force download: ${force}`);

  try {
    // Setup NestJS context
    const { NestFactory, ContextIdFactory } = require("@nestjs/core");
    const { AppModule } = require("../../../../../apps/portal-api/dist/app.module");
    const { UsTariffIngestionService, UsTariffApifyService } = require("../../..");

    console.log("\n🔧 Initializing services...");
    const contextId = ContextIdFactory.create();
    const app = await NestFactory.createApplicationContext(AppModule);

    // Setup demo organization context
    app.registerRequestByContextId(
      {
        user: {
          permission: "backoffice-admin",
          organization: { id: 3 }
        }
      },
      contextId
    );

    const apifyService = await app.resolve(UsTariffApifyService, contextId);
    const ingestionService = await app.resolve(UsTariffIngestionService, contextId);

    console.log("✅ Services initialized");

    // Step 1: Download or check file
    console.log(`\n📥 Step 1: Getting tariff file (${targetDate})...`);
    let fileBuffer;

    const fileExists = await apifyService.tariffFileExists(targetDate);

    if (!fileExists || force) {
      console.log("🚀 Running Apify actor to download fresh data...");
      const actorRun = await apifyService.runTariffDownload(targetDate, force);
      console.log(`⏳ Actor started: ${actorRun.data?.id}`);

      // Wait for download to complete
      console.log("⏳ Waiting for actor to complete...");
      await new Promise((resolve) => setTimeout(resolve, 30000));
    }

    // Download the file
    fileBuffer = await apifyService.downloadTariffFile(targetDate);
    console.log(`✅ Downloaded: ${(fileBuffer.length / 1024 / 1024).toFixed(2)} MB`);

    // Step 2: Process and ingest
    console.log("\n📊 Step 2: Processing XLSX and ingesting to database...");
    const startTime = Date.now();

    const result = await ingestionService.ingestFromXlsx(fileBuffer, "pipeline-test");

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    console.log(`✅ Ingestion completed in ${duration}s`);

    // Step 3: Verify results
    console.log("\n🔍 Step 3: Verifying results...");
    console.log(`📋 Sync Status: ${result.status}`);
    console.log(`📅 Sync Date: ${result.syncDate}`);
    console.log(`⏱️  Finish Date: ${result.finishDate}`);

    if (result.status === "SUCCESS") {
      console.log("\n🎉 Pipeline completed successfully!");
      console.log("💾 Data has been stored in the database");
      console.log("🔗 Ready for use in the application");
    } else {
      console.log("❌ Pipeline failed!");
      console.log(`📝 Error: ${result.errorMessage}`);
    }

    await app.close();
  } catch (error) {
    console.error("\n💥 Pipeline failed:", error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Show usage help
if (process.argv.includes("--help") || process.argv.includes("-h")) {
  console.log(`
Usage: node libraries/nest-modules/src/us-tariff/testing/test-pipeline-simple.js [options]

Options:
  --date=MMDDYY    Target date (e.g., --date=071425)
  --force          Force download even if file exists
  --help, -h       Show this help message

Examples:
  node libraries/nest-modules/src/us-tariff/testing/test-pipeline-simple.js
  node libraries/nest-modules/src/us-tariff/testing/test-pipeline-simple.js --date=071425
  node libraries/nest-modules/src/us-tariff/testing/test-pipeline-simple.js --force
  node libraries/nest-modules/src/us-tariff/testing/test-pipeline-simple.js --date=071425 --force
  `);
  process.exit(0);
}

// Run the pipeline
if (require.main === module) {
  runTariffPipeline();
}
