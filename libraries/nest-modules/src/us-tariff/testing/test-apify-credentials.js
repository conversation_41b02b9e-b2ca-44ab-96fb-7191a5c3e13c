#!/usr/bin/env node

/**
 * Simple test to verify Apify credentials and configured resources
 */

const axios = require("axios");

// Load from .env.local manually
const fs = require("fs");
const envPath = "../../../../../.env.local";
const envVars = {};

if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, "utf8");
  envContent.split("\n").forEach((line) => {
    if (line.startsWith("#") || !line.includes("=")) return;
    const [key, ...valueParts] = line.split("=");
    envVars[key.trim()] = valueParts.join("=").trim();
  });
}

const APIFY_TOKEN = envVars.APIFY_TOKEN || process.env.APIFY_TOKEN;
const APIFY_US_TARIFF_STORE_NAME =
  envVars.APIFY_US_TARIFF_STORE_NAME || process.env.APIFY_US_TARIFF_STORE_NAME;
const APIFY_US_TARIFF_ACTOR_NAME =
  envVars.APIFY_US_TARIFF_ACTOR_NAME || process.env.APIFY_US_TARIFF_ACTOR_NAME;
const APIFY_US_TARIFF_ACTOR_ID = envVars.APIFY_US_TARIFF_ACTOR_ID || process.env.APIFY_US_TARIFF_ACTOR_ID;

console.log("🔍 Testing Apify Configuration");
console.log("===============================");
console.log(`Token: ${APIFY_TOKEN ? APIFY_TOKEN.substring(0, 20) + "..." : "NOT SET"}`);
console.log(`Store Name: ${APIFY_US_TARIFF_STORE_NAME}`);
console.log(`Actor Name: ${APIFY_US_TARIFF_ACTOR_NAME}`);
console.log(`Actor ID: ${APIFY_US_TARIFF_ACTOR_ID}`);

if (!APIFY_TOKEN) {
  console.error("❌ APIFY_TOKEN not found in environment");
  process.exit(1);
}

async function testApifyCredentials() {
  try {
    // Test 1: Check if token is valid by listing user info
    console.log("\n🧪 Test 1: Checking token validity...");
    const userResponse = await axios.get("https://api.apify.com/v2/users/me", {
      headers: { Authorization: `Bearer ${APIFY_TOKEN}` }
    });
    console.log(`✅ Token valid - User: ${userResponse.data.data.username}`);

    // Test 2: Check if the configured actor exists
    console.log("\n🧪 Test 2: Checking actor existence...");
    try {
      const actorResponse = await axios.get(
        `https://api.apify.com/v2/acts/${APIFY_US_TARIFF_ACTOR_ID || APIFY_US_TARIFF_ACTOR_NAME}`,
        {
          headers: { Authorization: `Bearer ${APIFY_TOKEN}` }
        }
      );
      console.log(`✅ Actor exists - Name: ${actorResponse.data.data.name}`);
    } catch (actorError) {
      console.log(`❌ Actor not found: ${actorError.response?.status} - ${actorError.response?.statusText}`);

      // List user's actors to see what's available
      console.log("\n📋 Available actors:");
      try {
        const actorsResponse = await axios.get("https://api.apify.com/v2/acts", {
          headers: { Authorization: `Bearer ${APIFY_TOKEN}` }
        });
        actorsResponse.data.data.items.forEach((actor) => {
          console.log(`  - ${actor.name} (ID: ${actor.id})`);
        });
      } catch (listError) {
        console.log(`❌ Failed to list actors: ${listError.message}`);
      }
    }

    // Test 3: Check if the configured store exists
    console.log("\n🧪 Test 3: Checking key-value store existence...");
    try {
      const storeResponse = await axios.get(
        `https://api.apify.com/v2/key-value-stores/${APIFY_US_TARIFF_STORE_NAME}`,
        {
          headers: { Authorization: `Bearer ${APIFY_TOKEN}` }
        }
      );
      console.log(`✅ Store exists - Name: ${storeResponse.data.data.name}`);
    } catch (storeError) {
      console.log(`❌ Store not found: ${storeError.response?.status} - ${storeError.response?.statusText}`);

      // List user's stores to see what's available
      console.log("\n📋 Available key-value stores:");
      try {
        const storesResponse = await axios.get("https://api.apify.com/v2/key-value-stores", {
          headers: { Authorization: `Bearer ${APIFY_TOKEN}` }
        });
        storesResponse.data.data.items.forEach((store) => {
          console.log(`  - ${store.name} (ID: ${store.id})`);
        });
      } catch (listError) {
        console.log(`❌ Failed to list stores: ${listError.message}`);
      }
    }

    console.log("\n✅ Apify credential test completed!");
  } catch (error) {
    console.error("💥 Test failed:", error.message);
    if (error.response) {
      console.error(`Response: ${error.response.status} - ${error.response.statusText}`);
    }
    process.exit(1);
  }
}

if (require.main === module) {
  testApifyCredentials();
}
