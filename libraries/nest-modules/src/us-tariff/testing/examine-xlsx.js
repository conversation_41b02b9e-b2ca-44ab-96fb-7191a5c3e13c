const XLSX = require("xlsx");
const fs = require("fs");

const filePath = "../../../../../.ai-reference/us-tariff-test/test-download-071125.xlsx";
if (!fs.existsSync(filePath)) {
  console.log("File not found:", filePath);
  process.exit(1);
}

const workbook = XLSX.readFile(filePath);
const sheetName = workbook.SheetNames[0];
const worksheet = workbook.Sheets[sheetName];
const data = XLSX.utils.sheet_to_json(worksheet);

console.log("Sheet name:", sheetName);
console.log("Total rows:", data.length);
console.log("Sample column names:", Object.keys(data[0] || {}));

// Look for a specific tariff that we know has duplicates
const testRow = data.find((row) => row["HTS Number"] === "0306360020");
if (testRow) {
  console.log("\nFound tariff 0306360020 in XLSX:");
  console.log(JSON.stringify(testRow, null, 2));
}

// Look for PGA-related columns
const sampleRow = data[0] || {};
const pgaColumns = Object.keys(sampleRow).filter(
  (key) =>
    key.toLowerCase().includes("pga") ||
    key.toLowerCase().includes("flag") ||
    key.toLowerCase().includes("partner")
);
console.log("\nPGA-related columns:", pgaColumns);

// Look for any rows with multiple PGA flags in a single cell
const rowsWithPGA = data.filter((row) => {
  return pgaColumns.some((col) => {
    const value = row[col];
    return (
      value &&
      typeof value === "string" &&
      (value.includes(",") || value.includes(";") || value.includes(" "))
    );
  });
});

console.log("\nRows with multiple PGA flags in cells:", rowsWithPGA.length);
if (rowsWithPGA.length > 0) {
  console.log("Sample multi-PGA row:");
  console.log(JSON.stringify(rowsWithPGA[0], null, 2));
}
