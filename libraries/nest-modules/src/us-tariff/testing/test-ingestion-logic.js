#!/usr/bin/env node

const fs = require("fs");
// Use xlsx from nest-modules dependencies
const XLSX = require("xlsx");

/**
 * Test the US Tariff ingestion logic without NestJS
 * This tests the parsing and processing logic directly
 */

const EXCEL_FILE_PATH = "../../../../../.ai-reference/us-tariff-data/hts_pga_report_June29.xls";

function testIngestionLogic() {
  console.log("🧪 Testing US Tariff Ingestion Logic");
  console.log("====================================");

  try {
    // Check if Excel file exists
    if (!fs.existsSync(EXCEL_FILE_PATH)) {
      throw new Error(`Excel file not found at: ${EXCEL_FILE_PATH}`);
    }

    const fileStats = fs.statSync(EXCEL_FILE_PATH);
    console.log(`📁 File: ${EXCEL_FILE_PATH}`);
    console.log(`📊 Size: ${(fileStats.size / 1024 / 1024).toFixed(2)} MB`);

    // Parse XLSX file
    console.log("\n📖 Parsing XLSX file...");
    const workbook = XLSX.readFile(EXCEL_FILE_PATH);
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];

    // Convert to array of arrays
    const rawRows = XLSX.utils.sheet_to_json(worksheet, {
      header: 1,
      raw: false,
      defval: ""
    });

    console.log(`📊 Total raw rows: ${rawRows.length}`);

    // Process raw rows (skip first 3 rows as per service logic)
    const dataRows = rawRows.slice(3);
    console.log(`📊 Data rows to process: ${dataRows.length}`);

    // Process each row using the same logic as the service
    const processedData = [];
    let validRows = 0;
    let invalidRows = 0;

    for (let i = 0; i < dataRows.length; i++) {
      // Process all rows
      const row = dataRows[i];

      if (!row || row.length < 19) {
        console.log(`⚠️  Skipping invalid row ${i + 4}: insufficient columns (${row?.length || 0})`);
        invalidRows++;
        continue;
      }

      const processed = processRow(row, i + 4);
      if (processed) {
        processedData.push(processed);
        validRows++;

        // Show first few processed rows
        if (validRows <= 5) {
          console.log(`✅ Row ${i + 4} processed:`, {
            hsCode: processed.hsCode,
            description: processed.description.substring(0, 50) + "...",
            effectiveDate: processed.effectiveDate,
            expiryDate: processed.expiryDate,
            censusQty1: processed.censusQty1,
            adDuty: processed.adDuty,
            cvDuty: processed.cvDuty,
            pgaCodes: processed.pgaCodes
          });
        }
      } else {
        invalidRows++;
      }
    }

    console.log(`\n📊 Processing Summary:`);
    console.log(`✅ Valid rows processed: ${validRows}`);
    console.log(`❌ Invalid rows skipped: ${invalidRows}`);
    console.log(`📋 Total processed data: ${processedData.length}`);

    // Show some statistics
    if (processedData.length > 0) {
      const withPgaCodes = processedData.filter((d) => d.pgaCodes && d.pgaCodes.length > 0);
      const withAdDuty = processedData.filter((d) => d.adDuty === true);
      const withCvDuty = processedData.filter((d) => d.cvDuty === true);
      const withCensusQty = processedData.filter((d) => d.censusQty1 || d.censusQty2 || d.censusQty3);

      console.log(`\n📈 Data Statistics:`);
      console.log(`🏛️  Records with PGA codes: ${withPgaCodes.length}`);
      console.log(`💰 Records with AD duty: ${withAdDuty.length}`);
      console.log(`💰 Records with CV duty: ${withCvDuty.length}`);
      console.log(`📊 Records with census quantities: ${withCensusQty.length}`);

      // Show sample PGA codes
      if (withPgaCodes.length > 0) {
        console.log(`\n🏛️  Sample PGA codes:`);
        withPgaCodes.slice(0, 5).forEach((record) => {
          console.log(`  ${record.hsCode}: [${record.pgaCodes.join(", ")}]`);
        });
      }
    }

    console.log("\n✅ Ingestion logic test completed!");
  } catch (error) {
    console.error("💥 Test failed:", error.message);
    console.error(error.stack);
  }
}

/**
 * Process a single row (same logic as the service)
 */
function processRow(row, rowNumber) {
  const [
    tariffNumber,
    description,
    effectiveDate,
    expirationDate,
    censusQty1,
    censusQty2,
    censusQty3,
    adDuty,
    cvDuty,
    pga1,
    pga2,
    pga3,
    pga4,
    pga5,
    pga6,
    pga7,
    pga8,
    pga9,
    pga10
  ] = row;

  // Validate required fields
  if (!tariffNumber || !description) {
    console.log(`⚠️  Row ${rowNumber}: Missing tariff number or description`);
    return null;
  }

  // Clean and validate HTS code (must be exactly 10 digits)
  const hsCode = String(tariffNumber).replace(/\./g, "");
  if (!/^\d{10}$/.test(hsCode)) {
    console.log(`⚠️  Row ${rowNumber}: Invalid HTS code: ${tariffNumber} -> ${hsCode}`);
    return null;
  }

  // Parse dates
  const effectiveDateParsed = parseDate(effectiveDate);
  const expiryDateParsed = parseDate(expirationDate);

  if (!effectiveDateParsed || !expiryDateParsed) {
    console.log(`⚠️  Row ${rowNumber}: Invalid dates for HTS ${hsCode}: ${effectiveDate}, ${expirationDate}`);
    return null;
  }

  // Process census quantities (convert "NO" to null)
  const processCensusQty = (qty) => {
    const str = String(qty || "").trim();
    return str === "NO" || str === "" ? null : str;
  };

  // Process duty flags (N -> false, Y -> true)
  const processDutyFlag = (flag) => {
    const str = String(flag || "")
      .trim()
      .toUpperCase();
    if (str === "N") return false;
    if (str === "Y") return true;
    return null;
  };

  // Collect PGA codes (filter out empty values)
  const pgaCodes = [pga1, pga2, pga3, pga4, pga5, pga6, pga7, pga8, pga9, pga10]
    .map((code) => String(code || "").trim())
    .filter((code) => code && code !== "")
    .filter((code) => code.length > 0);

  return {
    hsCode,
    description: String(description).trim(),
    effectiveDate: effectiveDateParsed,
    expiryDate: expiryDateParsed,
    censusQty1: processCensusQty(censusQty1),
    censusQty2: processCensusQty(censusQty2),
    censusQty3: processCensusQty(censusQty3),
    adDuty: processDutyFlag(adDuty),
    cvDuty: processDutyFlag(cvDuty),
    pgaCodes: pgaCodes.length > 0 ? pgaCodes : null
  };
}

/**
 * Parse date from various formats
 */
function parseDate(dateValue) {
  if (!dateValue) return null;

  if (dateValue instanceof Date) {
    return dateValue;
  }

  const parsed = new Date(dateValue);
  return isNaN(parsed.getTime()) ? null : parsed;
}

// Main execution
if (require.main === module) {
  testIngestionLogic();
}
