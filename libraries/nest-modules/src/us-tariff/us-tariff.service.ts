import {
  Injectable,
  Logger,
  NotFoundException,
  Scope,
  ConflictException,
  BadRequestException
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import {
  Repository,
  QueryRunner,
  FindManyOptions,
  Like,
  Equal,
  Raw,
  And,
  LessThanOrEqual,
  MoreThanOrEqual
} from "typeorm";
import { UsTariff, UsTariffPgaRequirement, UsPgaRequirement } from "../entities";

@Injectable({ scope: Scope.REQUEST })
export class UsTariffService {
  private readonly logger = new Logger(UsTariffService.name);

  constructor(
    @InjectRepository(UsTariff)
    private readonly usTariffRepository: Repository<UsTariff>,
    @InjectRepository(UsTariffPgaRequirement)
    private readonly usTariffPgaRequirementRepository: Repository<UsTariffPgaRequirement>,
    @InjectRepository(UsPgaRequirement)
    private readonly usPgaRequirementRepository: Repository<UsPgaRequirement>
  ) {}

  /**
   * Format HS code for display purposes
   * Example: "12345678" -> "1234.56.78" or "1234567890" -> "1234.56.78.90"
   */
  static formatHsCode(hsCode: string): string {
    if (!hsCode || (hsCode.length !== 8 && hsCode.length !== 10)) return hsCode;

    const hsCodeFirstPart = hsCode.substring(0, 4);
    const hsCodeSecondPart = hsCode.substring(4, 6);
    const hsCodeThirdPart = hsCode.substring(6, 8);
    const hsCodeFourthPart = hsCode.substring(8, 10);

    if (hsCode.length === 8) {
      // 8-digit format: 1234.56.78
      return [hsCodeFirstPart, hsCodeSecondPart, hsCodeThirdPart].filter((s) => s.length > 0).join(".");
    } else {
      // 10-digit format: 1234.56.78.90
      return [hsCodeFirstPart, hsCodeSecondPart, hsCodeThirdPart, hsCodeFourthPart]
        .filter((s) => s.length > 0)
        .join(".");
    }
  }

  /**
   * Validate HS code format
   */
  static validateHsCode(hsCode: string): boolean {
    return /^\d{2,10}$/.test(hsCode);
  }

  /**
   * Get US tariffs with pagination and filtering
   */
  async getUsTariffs(
    options: {
      hsCode?: string;
      description?: string;
      effectiveDate?: Date;
      expiryDate?: Date;
      skip?: number;
      take?: number;
      sortBy?: string;
      sortOrder?: "ASC" | "DESC";
    } = {}
  ) {
    const {
      hsCode,
      description,
      effectiveDate,
      expiryDate,
      skip = 0,
      take = 50,
      sortBy = "hsCode",
      sortOrder = "ASC"
    } = options;

    const where: any = {};

    if (hsCode) {
      where.hsCode = Like(`${hsCode}%`);
    }

    if (description) {
      where.description = Like(`%${description}%`);
    }

    if (effectiveDate) {
      where.effectiveDate = MoreThanOrEqual(effectiveDate);
    }

    if (expiryDate) {
      where.expiryDate = LessThanOrEqual(expiryDate);
    }

    const findOptions: FindManyOptions<UsTariff> = {
      where,
      order: { [sortBy]: sortOrder },
      skip,
      take,
      relations: ["createdBy", "lastEditedBy"]
    };

    const [data, total] = await this.usTariffRepository.findAndCount(findOptions);

    return {
      data,
      skip,
      limit: take,
      total
    };
  }

  /**
   * Get US tariff by ID
   */
  async getUsTariffById(tariffId: number, queryRunner?: QueryRunner): Promise<UsTariff | null> {
    const repository = queryRunner ? queryRunner.manager.getRepository(UsTariff) : this.usTariffRepository;

    return await repository.findOne({
      where: { id: tariffId },
      relations: ["createdBy", "lastEditedBy"]
    });
  }

  /**
   * Get US tariff by HS code
   */
  async getUsTariffByHsCode(hsCode: string, queryRunner?: QueryRunner): Promise<UsTariff | null> {
    if (!UsTariffService.validateHsCode(hsCode)) {
      throw new BadRequestException(`Invalid HS code format: ${hsCode}`);
    }

    const repository = queryRunner ? queryRunner.manager.getRepository(UsTariff) : this.usTariffRepository;

    return await repository.findOne({
      where: { hsCode: Equal(hsCode) },
      relations: ["createdBy", "lastEditedBy"]
    });
  }

  /**
   * Create new US tariff
   */
  async createUsTariff(
    tariffData: {
      hsCode: string;
      effectiveDate: Date;
      expiryDate: Date;
      description: string;
      censusQty1?: string | null;
      censusQty2?: string | null;
      censusQty3?: string | null;
      adDuty?: boolean | null;
      cvDuty?: boolean | null;
      pgaCodes?: string[] | null;
    },
    queryRunner?: QueryRunner
  ): Promise<UsTariff> {
    if (!UsTariffService.validateHsCode(tariffData.hsCode)) {
      throw new BadRequestException(`Invalid HS code format: ${tariffData.hsCode}`);
    }

    const repository = queryRunner ? queryRunner.manager.getRepository(UsTariff) : this.usTariffRepository;

    // Check if tariff with this HS code already exists
    const existingTariff = await repository.findOne({
      where: { hsCode: tariffData.hsCode }
    });

    if (existingTariff) {
      throw new ConflictException(`US tariff with HS code ${tariffData.hsCode} already exists`);
    }

    const newTariff = repository.create(tariffData);
    return await repository.save(newTariff);
  }

  /**
   * Update existing US tariff
   */
  async editUsTariff(
    tariffId: number,
    updateData: Partial<{
      effectiveDate: Date;
      expiryDate: Date;
      description: string;
      censusQty1: string | null;
      censusQty2: string | null;
      censusQty3: string | null;
      adDuty: boolean | null;
      cvDuty: boolean | null;
      pgaCodes: string[] | null;
    }>,
    queryRunner?: QueryRunner
  ): Promise<UsTariff> {
    const repository = queryRunner ? queryRunner.manager.getRepository(UsTariff) : this.usTariffRepository;

    const existingTariff = await repository.findOne({
      where: { id: tariffId }
    });

    if (!existingTariff) {
      throw new NotFoundException(`US tariff with ID ${tariffId} not found`);
    }

    Object.assign(existingTariff, updateData);
    return await repository.save(existingTariff);
  }

  /**
   * Delete US tariff
   */
  async deleteUsTariff(tariffId: number, queryRunner?: QueryRunner): Promise<void> {
    const repository = queryRunner ? queryRunner.manager.getRepository(UsTariff) : this.usTariffRepository;

    const existingTariff = await repository.findOne({
      where: { id: tariffId }
    });

    if (!existingTariff) {
      throw new NotFoundException(`US tariff with ID ${tariffId} not found`);
    }

    // TODO: Consider whether to also delete related PGA requirements
    await repository.remove(existingTariff);
    this.logger.log(`Deleted US tariff with ID ${tariffId} (HS code: ${existingTariff.hsCode})`);
  }

  // PGA Requirements Pivot Table Methods

  /**
   * Get PGA requirements for a specific HS code
   */
  async getPgaRequirements(hsCode: string, queryRunner?: QueryRunner): Promise<UsPgaRequirement[]> {
    if (!UsTariffService.validateHsCode(hsCode)) {
      throw new BadRequestException(`Invalid HS code format: ${hsCode}`);
    }

    const pgaRequirementRepository = queryRunner
      ? queryRunner.manager.getRepository(UsTariffPgaRequirement)
      : this.usTariffPgaRequirementRepository;

    const usPgaRequirementRepository = queryRunner
      ? queryRunner.manager.getRepository(UsPgaRequirement)
      : this.usPgaRequirementRepository;

    // Get pivot records for this HS code
    const pivotRecords = await pgaRequirementRepository.find({
      where: { hsCode }
    });

    if (pivotRecords.length === 0) {
      return [];
    }

    // Get the actual PGA requirement objects
    const tariffFlagCodes = pivotRecords.map((pivot) => pivot.tariffFlagCode);
    return await usPgaRequirementRepository.find({
      where: tariffFlagCodes.map((code) => ({ tariffFlagCode: code }))
    });
  }

  /**
   * Get PGA requirements with pivot table details for a specific HS code
   */
  async getPgaRequirementsWithPivotData(
    hsCode: string,
    queryRunner?: QueryRunner
  ): Promise<UsTariffPgaRequirement[]> {
    if (!UsTariffService.validateHsCode(hsCode)) {
      throw new BadRequestException(`Invalid HS code format: ${hsCode}`);
    }

    const repository = queryRunner
      ? queryRunner.manager.getRepository(UsTariffPgaRequirement)
      : this.usTariffPgaRequirementRepository;

    return await repository.find({
      where: { hsCode }
    });
  }

  /**
   * Add PGA requirement relationship for a specific HS code
   */
  async addPgaRequirement(
    hsCode: string,
    tariffFlagCode: string,
    queryRunner?: QueryRunner
  ): Promise<UsTariffPgaRequirement> {
    if (!UsTariffService.validateHsCode(hsCode)) {
      throw new BadRequestException(`Invalid HS code format: ${hsCode}`);
    }

    const pgaRequirementRepository = queryRunner
      ? queryRunner.manager.getRepository(UsTariffPgaRequirement)
      : this.usTariffPgaRequirementRepository;

    const usPgaRequirementRepository = queryRunner
      ? queryRunner.manager.getRepository(UsPgaRequirement)
      : this.usPgaRequirementRepository;

    // Verify the PGA requirement exists
    const pgaRequirement = await usPgaRequirementRepository.findOne({
      where: { tariffFlagCode }
    });

    if (!pgaRequirement) {
      throw new NotFoundException(`PGA requirement with tariff flag code ${tariffFlagCode} not found`);
    }

    // Check if relationship already exists
    const existingRelation = await pgaRequirementRepository.findOne({
      where: { hsCode, tariffFlagCode }
    });

    if (existingRelation) {
      throw new ConflictException(
        `PGA requirement ${tariffFlagCode} already associated with HS code ${hsCode}`
      );
    }

    // Create new relationship
    const newRelation = pgaRequirementRepository.create({
      hsCode,
      tariffFlagCode
    });

    const savedRelation = await pgaRequirementRepository.save(newRelation);
    this.logger.log(`Added PGA requirement ${tariffFlagCode} to HS code ${hsCode}`);

    return savedRelation;
  }

  /**
   * Remove PGA requirement relationship for a specific HS code
   */
  async removePgaRequirement(
    hsCode: string,
    tariffFlagCode: string,
    queryRunner?: QueryRunner
  ): Promise<void> {
    if (!UsTariffService.validateHsCode(hsCode)) {
      throw new BadRequestException(`Invalid HS code format: ${hsCode}`);
    }

    const repository = queryRunner
      ? queryRunner.manager.getRepository(UsTariffPgaRequirement)
      : this.usTariffPgaRequirementRepository;

    const existingRelation = await repository.findOne({
      where: { hsCode, tariffFlagCode }
    });

    if (!existingRelation) {
      throw new NotFoundException(`PGA requirement ${tariffFlagCode} not associated with HS code ${hsCode}`);
    }

    await repository.remove(existingRelation);
    this.logger.log(`Removed PGA requirement ${tariffFlagCode} from HS code ${hsCode}`);
  }

  /**
   * Sync PGA requirements for a specific HS code (replace all existing with new list)
   */
  async syncPgaRequirements(
    hsCode: string,
    tariffFlagCodes: string[],
    queryRunner?: QueryRunner
  ): Promise<UsTariffPgaRequirement[]> {
    if (!UsTariffService.validateHsCode(hsCode)) {
      throw new BadRequestException(`Invalid HS code format: ${hsCode}`);
    }

    const repository = queryRunner
      ? queryRunner.manager.getRepository(UsTariffPgaRequirement)
      : this.usTariffPgaRequirementRepository;

    const usPgaRequirementRepository = queryRunner
      ? queryRunner.manager.getRepository(UsPgaRequirement)
      : this.usPgaRequirementRepository;

    // Verify all PGA requirements exist
    const pgaRequirements = await usPgaRequirementRepository.find({
      where: tariffFlagCodes.map((code) => ({ tariffFlagCode: code }))
    });

    const foundCodes = pgaRequirements.map((req) => req.tariffFlagCode);
    const missingCodes = tariffFlagCodes.filter((code) => !foundCodes.includes(code));

    if (missingCodes.length > 0) {
      throw new NotFoundException(`PGA requirements not found for codes: ${missingCodes.join(", ")}`);
    }

    // Remove existing relationships
    await repository.delete({ hsCode });

    // Create new relationships
    const newRelations = tariffFlagCodes.map((tariffFlagCode) =>
      repository.create({ hsCode, tariffFlagCode })
    );

    const savedRelations = await repository.save(newRelations);
    this.logger.log(`Synced ${savedRelations.length} PGA requirements for HS code ${hsCode}`);

    return savedRelations;
  }

  // Search and Utility Methods

  /**
   * Search tariffs by HS code prefix (useful for autocomplete)
   */
  async searchByHsCodePrefix(
    prefix: string,
    limit: number = 20,
    queryRunner?: QueryRunner
  ): Promise<UsTariff[]> {
    if (!prefix || !/^\d{1,10}$/.test(prefix)) {
      throw new BadRequestException(`Invalid HS code prefix format: ${prefix}`);
    }

    const repository = queryRunner ? queryRunner.manager.getRepository(UsTariff) : this.usTariffRepository;

    return await repository.find({
      where: { hsCode: Like(`${prefix}%`) },
      order: { hsCode: "ASC" },
      take: limit,
      relations: ["createdBy", "lastEditedBy"]
    });
  }

  /**
   * Get active tariffs (based on effective and expiry dates)
   */
  async getActiveTariffs(
    date: Date = new Date(),
    options: {
      hsCodePrefix?: string;
      skip?: number;
      take?: number;
    } = {},
    queryRunner?: QueryRunner
  ): Promise<{ data: UsTariff[]; total: number }> {
    const { hsCodePrefix, skip = 0, take = 50 } = options;

    const repository = queryRunner ? queryRunner.manager.getRepository(UsTariff) : this.usTariffRepository;

    const where: any = {
      effectiveDate: LessThanOrEqual(date),
      expiryDate: MoreThanOrEqual(date)
    };

    if (hsCodePrefix) {
      where.hsCode = Like(`${hsCodePrefix}%`);
    }

    const [data, total] = await repository.findAndCount({
      where,
      order: { hsCode: "ASC" },
      skip,
      take,
      relations: ["createdBy", "lastEditedBy"]
    });

    return { data, total };
  }

  /**
   * Get tariff with its PGA requirements in a single query
   */
  async getTariffWithPgaRequirements(
    hsCode: string,
    queryRunner?: QueryRunner
  ): Promise<{ tariff: UsTariff | null; pgaRequirements: UsPgaRequirement[] }> {
    if (!UsTariffService.validateHsCode(hsCode)) {
      throw new BadRequestException(`Invalid HS code format: ${hsCode}`);
    }

    // Get tariff and PGA requirements in parallel
    const [tariff, pgaRequirements] = await Promise.all([
      this.getUsTariffByHsCode(hsCode, queryRunner),
      this.getPgaRequirements(hsCode, queryRunner)
    ]);

    return { tariff, pgaRequirements };
  }

  /**
   * Check if tariff requires census reporting
   */
  async requiresCensusReporting(hsCode: string, queryRunner?: QueryRunner): Promise<boolean> {
    const tariff = await this.getUsTariffByHsCode(hsCode, queryRunner);

    if (!tariff) {
      return false;
    }

    // Check if any census quantities are defined (NO means NUMBER - a valid unit)
    const quantities = [tariff.censusQty1, tariff.censusQty2, tariff.censusQty3];
    return quantities.some((qty) => qty !== null && qty !== undefined);
  }

  /**
   * Get census quantities for a tariff (NO means NUMBER - a valid unit)
   */
  async getCensusQuantities(hsCode: string, queryRunner?: QueryRunner): Promise<string[]> {
    const tariff = await this.getUsTariffByHsCode(hsCode, queryRunner);

    if (!tariff) {
      return [];
    }

    return [tariff.censusQty1, tariff.censusQty2, tariff.censusQty3].filter(
      (qty): qty is string => qty !== null && qty !== undefined
    );
  }

  /**
   * Search tariffs by description
   */
  async searchByDescription(
    searchTerm: string,
    options: {
      skip?: number;
      take?: number;
      exactMatch?: boolean;
    } = {},
    queryRunner?: QueryRunner
  ): Promise<{ data: UsTariff[]; total: number }> {
    const { skip = 0, take = 50, exactMatch = false } = options;

    if (!searchTerm || searchTerm.length < 2) {
      throw new BadRequestException("Search term must be at least 2 characters long");
    }

    const repository = queryRunner ? queryRunner.manager.getRepository(UsTariff) : this.usTariffRepository;

    const where = exactMatch ? { description: Equal(searchTerm) } : { description: Like(`%${searchTerm}%`) };

    const [data, total] = await repository.findAndCount({
      where,
      order: { hsCode: "ASC" },
      skip,
      take,
      relations: ["createdBy", "lastEditedBy"]
    });

    return { data, total };
  }

  /**
   * Get tariffs that have specific PGA requirements
   */
  async getTariffsByPgaRequirement(
    tariffFlagCode: string,
    options: {
      skip?: number;
      take?: number;
    } = {},
    queryRunner?: QueryRunner
  ): Promise<{ data: UsTariff[]; total: number }> {
    const { skip = 0, take = 50 } = options;

    const pivotRepository = queryRunner
      ? queryRunner.manager.getRepository(UsTariffPgaRequirement)
      : this.usTariffPgaRequirementRepository;

    const tariffRepository = queryRunner
      ? queryRunner.manager.getRepository(UsTariff)
      : this.usTariffRepository;

    // Get HS codes that have this PGA requirement
    const pivotRecords = await pivotRepository.find({
      where: { tariffFlagCode },
      skip,
      take
    });

    const hsCodes = pivotRecords.map((record) => record.hsCode);

    if (hsCodes.length === 0) {
      return { data: [], total: 0 };
    }

    const [data, total] = await tariffRepository.findAndCount({
      where: { hsCode: Raw((alias) => `${alias} IN (${hsCodes.map((code) => `'${code}'`).join(",")})`) },
      order: { hsCode: "ASC" },
      relations: ["createdBy", "lastEditedBy"]
    });

    return { data, total };
  }
}
