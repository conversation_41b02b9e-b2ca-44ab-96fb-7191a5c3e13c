import { Inject, Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import moment from "moment-timezone";
import { ApifyService, ApifyActorRunDto } from "../apify";
import { ApifyUsTariffResult } from "./types/apify.types";

@Injectable()
export class UsTariffApifyService {
  private readonly logger = new Logger(UsTariffApifyService.name);

  constructor(
    @Inject(ApifyService)
    private readonly apifyService: ApifyService,
    @Inject(ConfigService)
    private readonly configService: ConfigService
  ) {}

  /**
   * Get US Tariff actor name from environment
   */
  private getActorName(): string {
    const actorName = this.configService.get<string>("APIFY_US_TARIFF_ACTOR_NAME");
    if (!actorName) {
      throw new Error("APIFY_US_TARIFF_ACTOR_NAME environment variable is required");
    }
    return actorName;
  }

  /**
   * Get US Tariff actor ID from environment
   */
  private getActorId(): string {
    const actorId = this.configService.get<string>("APIFY_US_TARIFF_ACTOR_ID");
    if (!actorId) {
      throw new Error("APIFY_US_TARIFF_ACTOR_ID environment variable is required");
    }
    return actorId;
  }

  /**
   * Get US Tariff store name/ID from environment
   */
  private getStoreName(): string {
    const storeName = this.configService.get<string>("APIFY_US_TARIFF_STORE_NAME");
    if (!storeName) {
      throw new Error("APIFY_US_TARIFF_STORE_NAME environment variable is required");
    }
    return storeName;
  }

  /**
   * Run the US Tariff download actor
   */
  async runTariffDownload(date?: string, forceDownload: boolean = false): Promise<ApifyActorRunDto> {
    const targetDate = this.formatDate(date);

    this.logger.log(`Running US Tariff download actor for date: ${targetDate}, force: ${forceDownload}`);

    const payload = {
      date: targetDate,
      forceDownload
    };

    try {
      const result = await this.apifyService.runActor(this.getActorId(), payload, {
        build: "latest"
      });

      this.logger.log(`Actor run started successfully: ${result.data?.id}`);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to run US Tariff actor: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Download XLSX file from key-value store
   */
  async downloadTariffFile(date?: string): Promise<Buffer> {
    const targetDate = this.formatDate(date);
    const key = `US_TARIFF_${targetDate}`;

    this.logger.log(`Downloading US Tariff file for key: ${key}`);

    try {
      const fileBuffer = await this.apifyService.getKeyValueStoreRecord<Buffer>(
        this.getStoreName(),
        key,
        true
      );

      if (!fileBuffer) {
        throw new Error(`No file found for key: ${key}`);
      }

      this.logger.log(`Successfully downloaded file for key: ${key}, size: ${fileBuffer.length} bytes`);
      return fileBuffer;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to download tariff file for key ${key}: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get file metadata from key-value store
   */
  async getTariffMetadata(date?: string): Promise<ApifyUsTariffResult | null> {
    const targetDate = this.formatDate(date);
    const metadataKey = `US_TARIFF_${targetDate}_metadata`;

    this.logger.log(`Getting US Tariff metadata for key: ${metadataKey}`);

    try {
      const metadata = await this.apifyService.getKeyValueStoreRecord<ApifyUsTariffResult>(
        this.getStoreName(),
        metadataKey
      );

      this.logger.log(`Retrieved metadata for key: ${metadataKey}`, metadata);
      return metadata;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.warn(`Failed to get tariff metadata for key ${metadataKey}: ${errorMessage}`);
      return null;
    }
  }

  /**
   * Format date to MMDDYY format used by the actor
   */
  private formatDate(date?: string): string {
    if (date) {
      // Validate the provided date is in MMDDYY format
      if (!/^\d{6}$/.test(date)) {
        throw new Error(`Invalid date format. Expected MMDDYY, got: ${date}`);
      }
      return date;
    }

    // Default to today's date in MMDDYY format
    return moment().format("MMDDYY");
  }

  /**
   * Check if tariff file exists for a given date
   */
  async tariffFileExists(date?: string): Promise<boolean> {
    try {
      const metadata = await this.getTariffMetadata(date);
      return metadata !== null && metadata.success === true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get the status of an Apify actor run
   */
  async getRun(runId: string): Promise<ApifyActorRunDto> {
    this.logger.log(`Getting status for actor run: ${runId}`);

    try {
      const result = await this.apifyService.getRun(runId);
      this.logger.log(`Actor run ${runId} status: ${result.data?.status}`);
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to get run status for ${runId}: ${errorMessage}`);
      throw error;
    }
  }
}
