import { BadRequestException, ConflictException, Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Not, IsNull, QueryRunner, Repository } from "typeorm";
import { EmailThread } from "../entities/email-thread.entity";
import { Shipment } from "../entities/shipment.entity";
import { Email } from "../entities/email.entity";
import { FIND_SHIPMENT_RELATIONS } from "../types/shipment.types";

export interface IThreadShipmentRepository {
  findShipmentByThread(threadId: string, queryRunner?: QueryRunner): Promise<Shipment | null>;
  associateThreadWithShipment(
    threadId: string,
    shipment: Shipment,
    queryRunner?: QueryRunner,
    skipOrganizationCheck?: boolean
  ): Promise<Shipment>;
  isThreadAlreadyAssociated(threadId: string, queryRunner?: QueryRunner): Promise<boolean>;
  getThreadsForShipment(shipmentId: string, queryRunner?: QueryRunner): Promise<EmailThread[]>;
}

@Injectable()
export class ThreadShipmentRepository implements IThreadShipmentRepository {
  constructor(
    @InjectRepository(EmailThread)
    private readonly emailThreadRepository: Repository<EmailThread>,
    @InjectRepository(Email)
    private readonly emailRepository: Repository<Email>
  ) {}

  /**
   * Find shipment associated with a thread ID
   * @param threadId Gmail thread ID
   * @param queryRunner Optional query runner for transactions
   * @returns Shipment if found, null otherwise
   */
  async findShipmentByThread(threadId: string, queryRunner?: QueryRunner): Promise<Shipment | null> {
    const emailThread = await (
      queryRunner ? queryRunner.manager.getRepository(EmailThread) : this.emailThreadRepository
    ).findOne({
      where: {
        threadId
      },
      relations: { shipment: FIND_SHIPMENT_RELATIONS }
    });
    return emailThread?.shipment || null;
  }

  /**
   * Associate a thread with a shipment
   * @param threadId Gmail thread ID
   * @param shipment Shipment to associate
   * @param queryRunner Optional query runner for transactions
   * @param skipOrganizationCheck Skip organization validation
   * @returns Associated shipment
   * @throws ConflictException if thread already associated
   * @throws NotFoundException if no emails found for thread
   * @throws BadRequestException if organizations don't match
   */
  async associateThreadWithShipment(
    threadId: string,
    shipment: Shipment,
    queryRunner?: QueryRunner,
    skipOrganizationCheck = false
  ): Promise<Shipment> {
    const emailThreadRepository = queryRunner
      ? queryRunner.manager.getRepository(EmailThread)
      : this.emailThreadRepository;

    if (await emailThreadRepository.existsBy({ threadId }))
      throw new ConflictException("Email thread is already associated with a shipment");

    const emails = await this.getEmailsByThreadId(threadId, null, null, queryRunner, skipOrganizationCheck);
    if (emails?.length <= 0) throw new NotFoundException("No emails found for this thread");
    if (emails[0].organization?.id !== shipment?.organization?.id)
      throw new BadRequestException("Shipment and thread belong to different organization");

    let emailThread = new EmailThread();
    emailThread.threadId = threadId;
    emailThread.shipment = shipment;
    emailThread = await emailThreadRepository.save(emailThread);
    return await this.findShipmentByThread(threadId, queryRunner);
  }

  /**
   * Check if thread is already associated with a shipment
   * @param threadId Gmail thread ID
   * @param queryRunner Optional query runner for transactions
   * @returns True if association exists
   */
  async isThreadAlreadyAssociated(threadId: string, queryRunner?: QueryRunner): Promise<boolean> {
    const emailThreadRepository = queryRunner
      ? queryRunner.manager.getRepository(EmailThread)
      : this.emailThreadRepository;

    return await emailThreadRepository.existsBy({ threadId });
  }

  /**
   * Get all threads associated with a shipment
   * @param shipmentId Shipment ID
   * @param queryRunner Optional query runner for transactions
   * @returns Array of EmailThread entities
   */
  async getThreadsForShipment(shipmentId: string, queryRunner?: QueryRunner): Promise<EmailThread[]> {
    const emailThreadRepository = queryRunner
      ? queryRunner.manager.getRepository(EmailThread)
      : this.emailThreadRepository;

    return await emailThreadRepository.find({
      where: {
        shipment: { id: parseInt(shipmentId) }
      },
      relations: {
        shipment: true
      }
    });
  }

  /**
   * Get emails by thread ID - extracted from EmailService
   * @param threadId Gmail thread ID
   * @param earlierThan Filter emails earlier than date
   * @param status Filter by email status
   * @param queryRunner Optional query runner for transactions
   * @param skipOrganizationCheck Skip organization validation
   * @returns Array of Email entities
   */
  private async getEmailsByThreadId(
    threadId: string,
    earlierThan?: Date,
    status?: any,
    queryRunner?: QueryRunner,
    skipOrganizationCheck = false
  ): Promise<Email[]> {
    return await (queryRunner ? queryRunner.manager.getRepository(Email) : this.emailRepository).find({
      where: {
        threadId,
        status: status || Not(IsNull()),
        ...(earlierThan && { receiveDate: Not(IsNull()) })
      },
      relations: {
        organization: true,
        updates: true
      },
      order: {
        receiveDate: "ASC"
      }
    });
  }
}
