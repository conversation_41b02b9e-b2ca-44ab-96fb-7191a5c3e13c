# ThreadShipmentRepository

## Overview

The `ThreadShipmentRepository` is a dedicated repository for managing thread-shipment associations, extracted from the `EmailService` to improve separation of concerns and maintainability.

## Purpose

This repository handles the association between Gmail thread IDs and shipments through the `EmailThread` entity, providing:

- Thread-to-shipment lookups
- Creating new thread-shipment associations
- Validation of thread associations
- Organization-level security checks

## Interface

```typescript
interface IThreadShipmentRepository {
  findShipmentByThread(threadId: string, queryRunner?: QueryRunner): Promise<Shipment | null>;
  associateThreadWithShipment(threadId: string, shipment: Shipment, queryRunner?: QueryRunner, skipOrganizationCheck?: boolean): Promise<Shipment>;
  isThreadAlreadyAssociated(threadId: string, queryRunner?: QueryRunner): Promise<boolean>;
  getThreadsForShipment(shipmentId: string, queryRunner?: QueryRunner): Promise<EmailThread[]>;
}
```

## Methods

### `findShipmentByThread(threadId, queryRunner?)`
- **Purpose**: Find shipment associated with a Gmail thread ID
- **Returns**: Shipment with full relations or null if not found
- **Transaction Support**: Yes, via optional queryRunner

### `associateThreadWithShipment(threadId, shipment, queryRunner?, skipOrganizationCheck?)`
- **Purpose**: Create new thread-shipment association
- **Validation**: 
  - Thread uniqueness (throws ConflictException if exists)
  - Email existence (throws NotFoundException if no emails)
  - Organization matching (throws BadRequestException if mismatch)
- **Transaction Support**: Yes, via optional queryRunner
- **Security**: Organization check can be skipped for system operations

### `isThreadAlreadyAssociated(threadId, queryRunner?)`
- **Purpose**: Check if thread already has a shipment association
- **Returns**: Boolean indicating association existence
- **Transaction Support**: Yes, via optional queryRunner

### `getThreadsForShipment(shipmentId, queryRunner?)`
- **Purpose**: Get all thread associations for a shipment
- **Returns**: Array of EmailThread entities
- **Transaction Support**: Yes, via optional queryRunner

## Integration

### Module Registration

Add to your module providers:

```typescript
import { ThreadShipmentRepository } from 'nest-modules';

@Module({
  imports: [
    TypeOrmModule.forFeature([EmailThread, Email])
  ],
  providers: [ThreadShipmentRepository],
  exports: [ThreadShipmentRepository]
})
```

### Usage Example

```typescript
import { ThreadShipmentRepository } from 'nest-modules';

@Injectable()
export class SomeService {
  constructor(
    private threadShipmentRepository: ThreadShipmentRepository
  ) {}

  async processEmail(email: Email, queryRunner?: QueryRunner) {
    // Check existing association
    const existingShipment = await this.threadShipmentRepository
      .findShipmentByThread(email.threadId, queryRunner);
    
    if (!existingShipment) {
      // Create new association
      const newShipment = await this.createShipment(email);
      await this.threadShipmentRepository
        .associateThreadWithShipment(email.threadId, newShipment, queryRunner);
    }
  }
}
```

## Migration from EmailService

To migrate from EmailService methods:

**Before:**
```typescript
const shipment = await emailService.getEmailThreadShipment(threadId, queryRunner);
await emailService.setEmailThreadShipment(threadId, shipment, queryRunner, skipOrgCheck);
```

**After:**
```typescript
const shipment = await threadShipmentRepository.findShipmentByThread(threadId, queryRunner);
await threadShipmentRepository.associateThreadWithShipment(threadId, shipment, queryRunner, skipOrgCheck);
```

## Error Handling

The repository maintains the same error behavior as the original EmailService methods:

- `ConflictException`: Thread already associated with a shipment
- `NotFoundException`: No emails found for thread
- `BadRequestException`: Shipment and thread belong to different organizations

## Transaction Support

All methods support TypeORM QueryRunner for transaction consistency:

```typescript
await dataSource.transaction(async (queryRunner) => {
  const shipment = await threadShipmentRepository
    .findShipmentByThread(threadId, queryRunner);
  
  if (!shipment) {
    const newShipment = await shipmentRepository
      .save(createShipmentDto, { queryRunner });
    
    await threadShipmentRepository
      .associateThreadWithShipment(threadId, newShipment, queryRunner);
  }
});
```

## Testing

Tests should be implemented using the portal-api testing infrastructure. See the template in `thread-shipment.repository.spec.ts` for test case structure.

The repository follows the same patterns as other NestJS repositories and can be mocked for unit testing using standard Jest mocking patterns.

## Dependencies

- `@nestjs/typeorm` - Repository decorators and TypeORM integration
- `typeorm` - Database operations and query runner support
- `nest-modules/entities` - EmailThread, Email, Shipment entities
- `nest-modules/types` - FIND_SHIPMENT_RELATIONS constant