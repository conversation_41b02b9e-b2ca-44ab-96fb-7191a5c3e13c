/**
 * BullMQ Helper Functions
 *
 * Utility functions for working with BullMQ FlowProducer and job management.
 * These are helper functions for the BullMQ library, not part of BullMQ itself.
 */

/**
 * Interface for FlowProducer job configuration
 */
export interface FlowJob {
  name: string;
  queueName: string;
  data?: any;
  prefix?: string;
  opts?: any;
  children?: FlowJob[];
}

/**
 * Interface for job configuration when building flows
 */
export interface JobConfig {
  name: string;
  queueName: string;
  data?: any;
  opts?: any;
}

/**
 * Helper function that builds a FlowProducer chain from an execution sequence array.
 *
 * Takes an array representing the desired execution order and converts it
 * into the nested structure that BullMQ's FlowProducer expects.
 *
 * @example
 * // To execute jobs in order: extract-user-intents → process-user-intents
 * const flowProducerStructure = buildFlowProducer([
 *   {
 *     name: 'extract-user-intents', // Runs FIRST, but is most deeply nested child
 *     queueName: EmailQueueName.EXTRACT,
 *     data: { emailId, organizationId }
 *   },
 *   {
 *     name: 'process-user-intents', // Runs SECOND
 *     queueName: EmailQueueName.PROCESS,
 *     data: { emailId, organizationId }
 *   }
 * ]);
 *
 * @param sequence - Array of job configurations in execution order (first executes first)
 * @returns FlowJob structure with proper nesting for FlowProducer
 * @throws Error if sequence is empty or invalid
 */
export function buildFlowProducer(sequence: JobConfig[]): FlowJob {
  // Validate input
  if (!Array.isArray(sequence)) {
    throw new Error("Sequence must be an array");
  }

  if (sequence.length === 0) {
    throw new Error("Sequence cannot be empty");
  }

  // Validate all jobs have required properties
  for (const job of sequence) {
    if (!job.name || typeof job.name !== "string") {
      throw new Error("Each job must have a valid name string");
    }
    if (!job.queueName || typeof job.queueName !== "string") {
      throw new Error("Each job must have a valid queueName string");
    }
  }

  // Build the nested structure from the inside out
  // Start with the first job (innermost/deepest child)
  let currentJob: FlowJob = {
    name: sequence[0].name,
    queueName: sequence[0].queueName,
    ...(sequence[0].data && { data: sequence[0].data }),
    ...(sequence[0].opts && { opts: sequence[0].opts })
  };

  // Wrap each subsequent job around the previous one
  for (let i = 1; i < sequence.length; i++) {
    const job = sequence[i];

    currentJob = {
      name: job.name,
      queueName: job.queueName,
      ...(job.data && { data: job.data }),
      ...(job.opts && { opts: job.opts }),
      children: [currentJob]
    };
  }

  return currentJob;
}

/**
 * Helper function to build a simple chain with same queue name for all jobs
 *
 * @example
 * const flow = buildSimpleJobChain(['A', 'B', 'C', 'D'], 'my-queue');
 *
 * @param jobNames - Array of job names in execution order
 * @param queueName - Queue name to use for all jobs
 * @param commonData - Optional data to apply to all jobs
 * @param commonOpts - Optional options to apply to all jobs
 * @returns FlowJob structure with proper nesting for FlowProducer
 */
export function buildSimpleJobChain(
  jobNames: string[],
  queueName: string,
  commonData?: any,
  commonOpts?: any
): FlowJob {
  const sequence: JobConfig[] = jobNames.map((name) => ({
    name,
    queueName,
    ...(commonData && { data: commonData }),
    ...(commonOpts && { opts: commonOpts })
  }));

  return buildFlowProducer(sequence);
}

/**
 * Helper function that extracts the execution order from a FlowProducer structure
 * (useful for debugging or validation)
 *
 * @param flow - FlowJob structure
 * @returns Array of job names in execution order
 */
export function extractJobExecutionOrder(flow: FlowJob): string[] {
  const order: string[] = [];

  function traverse(job: FlowJob): void {
    if (job.children && job.children.length > 0) {
      // Recursively traverse children first (they execute first)
      for (const child of job.children) {
        traverse(child);
      }
    }

    // Add current job after its children
    order.push(job.name);
  }

  traverse(flow);
  return order;
}
