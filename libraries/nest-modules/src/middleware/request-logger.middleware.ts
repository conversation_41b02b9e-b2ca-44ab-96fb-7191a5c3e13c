import { Injectable, NestMiddleware } from "@nestjs/common";
import { NextFunction, Request, Response } from "express";
import moment from "moment-timezone";

@Injectable()
export class RequestLoggerMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const { method, originalUrl, ip } = req;
    const userAgent = req.get("User-Agent");
    const requestDate = moment.tz("America/Toronto").format("YYYY-MM-DDTHH:mm:ss.SSSZZ");
    let body = JSON.stringify(req.body);
    const passwordKeyMatches = body.matchAll(/"password":("[^"]*"|[^"]*)(,|})/gi);
    for (const passwordKeyMatch of passwordKeyMatches) {
      const startIdx = passwordKeyMatch.index + 11;
      const endIdx = passwordKeyMatch.index + passwordKeyMatch[0].length - 1;
      body = body.substring(0, startIdx) + "*".repeat(endIdx - startIdx) + body.substring(endIdx);
    }
    // if (passwordKeyMatch) {
    //     const startIdx = passwordKeyMatch.index + 11;
    //     const endIdx = passwordKeyMatch.index + passwordKeyMatch[0].length-1;
    // }

    res.on("finish", () => {
      const { statusCode } = res;
      console.log(`${requestDate} ${method} ${originalUrl} ${statusCode} - ${ip} ${userAgent} ${body}`);
    });

    next();
  }
}
