import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsDate, IsEnum, IsInt, IsNotEmpty, IsOptional, IsString, Min } from "class-validator";
import { TrackingHistoryColumn } from "../types/tracking-history.types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class CreateTrackingHistoryDto {
  @ApiProperty({ type: "string", format: "date-time" })
  @Type(() => Date)
  @IsDate()
  @IsNotEmpty()
  timestamp: Date;

  @ApiProperty({ type: "string" })
  @IsString()
  @IsNotEmpty()
  result: string;

  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  shipmentId: number;

  @ApiProperty({ type: "string" })
  @IsString()
  @IsNotEmpty()
  party: string;
}

export class GetTrackingHistoryDto extends GetManyDto {
  @ApiPropertyOptional({ enum: TrackingHistoryColumn, default: TrackingHistoryColumn.id })
  @IsOptional()
  @IsEnum(TrackingHistoryColumn)
  sortBy?: TrackingHistoryColumn;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  timestamp?: Date;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  result?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  party?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  shipmentId?: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "For basic user and organization admin, Organization ID will be auto-set according to current user's organization"
  })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  organizationId?: number;
}

export class GetTrackingHistoryResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: [GetTrackingHistoryDto] })
  trackingHistories: Array<GetTrackingHistoryDto>;
}
