import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  ArrayUnique,
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
  Min,
  MinLength,
  ValidateNested
} from "class-validator";
import { CanadaExciseTaxCode } from "../entities";
import { CanadaExciseTaxCodeColumn } from "../types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class GetCanadaExciseTaxCodesDto extends GetManyDto {
  @ApiPropertyOptional({ enum: CanadaExciseTaxCodeColumn })
  @IsOptional()
  @IsEnum(CanadaExciseTaxCodeColumn)
  sortBy?: CanadaExciseTaxCodeColumn;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  category?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  code?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  explanation?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  rateType?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetCanadaExciseTaxCodesResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: [CanadaExciseTaxCode] })
  data: Array<CanadaExciseTaxCode>;
}

export class CreateCanadaExciseTaxCodeDto {
  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  category: string;

  @ApiProperty({ minLength: 3, maxLength: 3, pattern: "^[A-Z]\d{2}$" })
  @IsNotEmpty()
  @IsString()
  @Matches(/^[A-Z]\d{2}$/)
  code: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  explanation: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  rateType?: string | null;
}

export class EditCanadaExciseTaxCodeDto {
  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  category?: string;

  @ApiPropertyOptional({ minLength: 3, maxLength: 3, pattern: "^[A-Z]\d{2}$" })
  @IsOptional()
  @IsString()
  @Matches(/^[A-Z]\d{2}$/)
  code?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  explanation?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  rateType?: string | null;
}

export class EditCanadaExciseTaxCodeWithIdDto extends EditCanadaExciseTaxCodeDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  id: number;
}

export class BatchUpdateCanadaExciseTaxCodesDto {
  @ApiProperty({ type: [CreateCanadaExciseTaxCodeDto] })
  @Type(() => CreateCanadaExciseTaxCodeDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  create?: Array<CreateCanadaExciseTaxCodeDto>;

  @ApiProperty({ type: [EditCanadaExciseTaxCodeWithIdDto] })
  @Type(() => EditCanadaExciseTaxCodeWithIdDto)
  @IsOptional()
  @IsArray()
  @ArrayUnique((o) => o.id)
  @ValidateNested({ each: true })
  edit?: Array<EditCanadaExciseTaxCodeWithIdDto>;

  @ApiProperty({ type: "integer", minimum: 1, isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  delete?: Array<number>;
}

export class BatchUpdateCanadaExciseTaxCodesResponseDto {
  @ApiProperty({ type: [CanadaExciseTaxCode] })
  data: Array<CanadaExciseTaxCode>;
}
