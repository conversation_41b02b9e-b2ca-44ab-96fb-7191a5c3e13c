import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  IsArray,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  Max,
  <PERSON>,
  ValidateNested
} from "class-validator";

export class ApiTemplateCreatePdfDto {
  @ApiProperty({ type: "string", description: "Your template id, it can be obtained in the web console" })
  @IsString()
  @IsNotEmpty()
  template_id: string;

  @ApiPropertyOptional({ type: "enum", enum: ["file", "json"], default: "json" })
  @IsIn(["file", "json"])
  @IsOptional()
  export_type?: "file" | "json";

  @ApiPropertyOptional({
    type: "enum",
    enum: ["0", "1"],
    default: "0",
    description:
      "If export_type = file, the PDF can be downloaded in binary or base64 format. The value is either 1 or 0(Default). The export_in_base64 is set 0 is to download the PDF in binary. The export_in_base64 is set 1 is to download the PDF in base64 format"
  })
  @IsIn(["0", "1"])
  @IsOptional()
  export_in_base64?: "0" | "1";

  @ApiPropertyOptional({
    type: "integer",
    minimum: 0,
    maximum: 10080,
    description: "Expiration of the generated PDF in minutes(default to 0, store permanently)"
  })
  @IsInt()
  @IsOptional()
  @Min(0)
  @Max(10080)
  expiration?: number;

  @ApiPropertyOptional({
    type: "enum",
    enum: ["0", "1"],
    default: "0",
    description:
      "To enable output of html content, set the value to 1 and it will return in the JSON response as html_url field (as a URL)"
  })
  @IsIn(["0", "1"])
  @IsOptional()
  output_html?: "0" | "1";

  @ApiPropertyOptional({
    type: "enum",
    enum: ["pdf", "html"],
    description:
      "It's generating PDF by default. However, you can specify output_format=html to generate only HTML(It will return in the JSON response as download_url field as a URL)."
  })
  @IsIn(["pdf", "html"])
  @IsOptional()
  output_format?: "pdf" | "html";

  @ApiPropertyOptional({
    type: "string",
    description:
      "Default to UUID (e.g 0c93bd9e-9ebb-4634-a70f-de9131848416.pdf). Use this to specify custom file name, it should end with .pdf"
  })
  @IsOptional()
  @IsString()
  filename?: string;

  @ApiPropertyOptional({
    type: "enum",
    enum: ["0", "1"],
    default: "0",
    description: "ContentDisposition set to attachment. 1=true, 0=false. Default to '0'"
  })
  @IsIn(["0", "1"])
  @IsOptional()
  direct_download?: "0" | "1";

  // TODO: Add the complete list of query parameters from API Template
}

export class ApiTemplatePostActionDto {
  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  action?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  bucket?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  file?: string;
}

export class ApiTemplateCreatePdfResponseDto {
  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional({ type: "string", format: "uri" })
  @IsOptional()
  @IsUrl()
  download_url?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  template_id?: string;

  @ApiPropertyOptional({ type: "integer" })
  @IsOptional()
  @IsInt()
  @IsInt()
  total_pages?: number;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  transaction_ref?: string;

  @ApiPropertyOptional({ type: () => [ApiTemplatePostActionDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ApiTemplatePostActionDto)
  post_actions?: Array<ApiTemplatePostActionDto>;
}
