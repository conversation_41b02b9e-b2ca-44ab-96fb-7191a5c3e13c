import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  Min,
  <PERSON><PERSON>ength,
  ValidateNested
} from "class-validator";
import { File as FileEntity } from "../entities/file.entity";
import { FileColumn, FileStatus } from "../types/file.types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class GetFilesDto extends GetManyDto {
  @ApiPropertyOptional({ enum: FileColumn, default: FileColumn.id })
  @IsOptional()
  @IsEnum(FileColumn)
  sortBy?: FileColumn;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  path?: string;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "For basic user and organization admin, Organization ID will be auto-set according to current user's organization"
  })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  organizationId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;

  @ApiPropertyOptional({ enum: FileStatus })
  @IsOptional()
  @IsEnum(FileStatus)
  status?: FileStatus;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  shipmentId?: number;

  @ApiPropertyOptional({ type: "boolean" })
  @IsOptional()
  @Transform(({ value }) => value === "true")
  @IsBoolean()
  hasOrphanDocuments?: boolean;
}

export class GetFilesResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [FileEntity] })
  files: Array<FileEntity>;
}

export class CreateFileDto {
  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  name: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @IsUrl()
  path: string;
}

export class EditFileDto {
  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @IsUrl()
  path?: string;
}

export class UploadFilesDto {
  @ApiProperty({ type: "array", items: { type: "string", format: "binary" } })
  files: Array<File | Express.Multer.File>;
}

export class UploadFilesResponseDto {
  @ApiProperty({ type: () => [FileEntity] })
  files: Array<FileEntity>;

  @ApiProperty({
    description: "The file upload session id"
  })
  /* @deprecated use batchId instead */
  session: string;

  @ApiProperty({
    description: "The batch id for the file upload session"
  })
  batchId: string;

  @ApiPropertyOptional({
    type: "integer",
    description: "The shipment id file uploaded to"
  })
  shipmentId?: number;
}

/**
 * DTO for saving documents for a file
 */
export class BatchSaveFileDocumentItemDto {
  @IsInt()
  @IsOptional()
  @ApiPropertyOptional()
  id?: number;

  @IsNotEmpty()
  @IsInt()
  @ApiProperty()
  documentTypeId: number;

  @IsNotEmpty()
  @IsInt()
  @ApiProperty()
  startPage: number;

  @IsNotEmpty()
  @IsInt({ each: true })
  @ApiProperty()
  pageIds: number[];
}

export class BatchSaveFileDocumentsDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BatchSaveFileDocumentItemDto)
  @ApiProperty({ type: () => [BatchSaveFileDocumentItemDto] })
  created: Array<BatchSaveFileDocumentItemDto>;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BatchSaveFileDocumentItemDto)
  @ApiProperty({ type: () => [BatchSaveFileDocumentItemDto] })
  updated: Array<BatchSaveFileDocumentItemDto>;

  @IsArray()
  @IsInt({ each: true })
  @ApiProperty({ type: () => [Number] })
  deleted: Array<number>;
}
