import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type, Transform } from "class-transformer";
import { IsDate, IsEnum, IsInt, IsOptional, Min } from "class-validator";
import { SortOrder } from "../types/base.types";

export class GetManyDto {
  @ApiPropertyOptional({ type: "integer", minimum: 0, default: 0 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(0)
  skip?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 0, default: 10 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(0)
  limit?: number;

  @ApiPropertyOptional({ enum: SortOrder, default: SortOrder.ASC })
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Transform(({ value }) => (typeof value === "string" && value.length > 0 ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  createDateFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Transform(({ value }) => (typeof value === "string" && value.length > 0 ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  createDateTo?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Transform(({ value }) => (typeof value === "string" && value.length > 0 ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  lastEditDateFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Transform(({ value }) => (typeof value === "string" && value.length > 0 ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  lastEditDateTo?: Date;
}

export class GetManyResponseDto {
  @ApiProperty({ type: "integer", minimum: 0 })
  total: number;

  @ApiProperty({ type: "integer", minimum: 0, default: 0 })
  skip: number;

  @ApiProperty({ type: "integer", minimum: 0, default: 10 })
  limit: number;
}
