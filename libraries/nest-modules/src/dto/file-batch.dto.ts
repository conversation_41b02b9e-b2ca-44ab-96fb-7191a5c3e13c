import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsEnum, IsInt, IsOptional } from "class-validator";
import { FileBatch } from "../entities/file-batch.entity";
import { FileBatchStatus } from "../types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class GetFileBatchesDto extends GetManyDto {
  @ApiProperty({ type: "integer", required: false })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  organizationId?: number;

  @ApiProperty({ type: "integer", required: false })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  shipmentId?: number;

  @ApiProperty({ type: "string", required: false })
  @IsOptional()
  @IsEnum(FileBatchStatus)
  status?: FileBatchStatus;
}

export class GetFileBatchesResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [FileBatch] })
  fileBatches: Array<FileBatch>;
}
