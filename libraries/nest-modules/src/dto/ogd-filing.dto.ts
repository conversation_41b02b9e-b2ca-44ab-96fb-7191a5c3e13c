import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
  IsArray,
  IsBoolean,
  isBooleanString,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
  <PERSON><PERSON>ength,
  ValidateNested
} from "class-validator";
import { IsNumberStringBetween } from "../decorators";
import { MatchingRule } from "../entities";
import { OgdFiling } from "../entities/ogd-filing.entity";
import {
  ECCCEmissionProgram,
  ECCCSubType,
  ECCCWildlifeCompliance,
  GeneralImportPermit,
  NRCANSubType,
  OgdFilingColumn,
  TCTireClass,
  TCTireCompliance,
  TCTireSize,
  TCTireType
} from "../types/ogd-filing.types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import { BatchUpdateMatchingConditionsDto, CreateMatchingConditionDto } from "./matching-condition.dto";
import { CreateMatchingRuleWithoutSourceDto, EditMatchingRuleWithoutSourceDto } from "./matching-rule.dto";

export class GetOgdFilingsDto extends GetManyDto {
  @ApiPropertyOptional({ enum: OgdFilingColumn, default: OgdFilingColumn.id })
  @IsOptional()
  @IsEnum(OgdFilingColumn)
  sortBy?: OgdFilingColumn;

  @ApiPropertyOptional({ type: "boolean" })
  @Transform(({ value }) =>
    typeof value === "string" && isBooleanString(value.trim().toLowerCase())
      ? value.trim().toLowerCase() === "true"
      : value
  )
  @IsOptional()
  @IsBoolean()
  isExcluded?: boolean;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  extensionCode?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  endUse?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  @MinLength(1)
  airsType?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  airsReferenceNumber?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  productCategory?: string;

  @ApiPropertyOptional({ enum: GeneralImportPermit })
  @IsOptional()
  @IsEnum(GeneralImportPermit)
  generalImportPermit?: GeneralImportPermit;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  brandName?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  manufacturerId?: number;

  @ApiPropertyOptional({ enum: TCTireType })
  @IsOptional()
  @IsEnum(TCTireType)
  tireType?: TCTireType;

  @ApiPropertyOptional({ enum: TCTireSize })
  @IsOptional()
  @IsEnum(TCTireSize)
  tireSize?: TCTireSize;

  @ApiPropertyOptional({ enum: TCTireCompliance })
  @IsOptional()
  @IsEnum(TCTireCompliance)
  tireCompliance?: TCTireCompliance;

  @ApiPropertyOptional({ enum: TCTireClass })
  @IsOptional()
  @IsEnum(TCTireClass)
  tireClass?: TCTireClass;

  @ApiPropertyOptional({ enum: ECCCSubType })
  @IsOptional()
  @IsEnum(ECCCSubType)
  ecccSubType?: ECCCSubType;

  @ApiPropertyOptional({ enum: ECCCWildlifeCompliance })
  @IsOptional()
  @IsEnum(ECCCWildlifeCompliance)
  wildlifeCompliance?: ECCCWildlifeCompliance;

  @ApiPropertyOptional({ enum: ECCCEmissionProgram })
  @IsOptional()
  @IsEnum(ECCCEmissionProgram)
  emissionProgram?: ECCCEmissionProgram;

  @ApiPropertyOptional({ enum: NRCANSubType })
  @IsOptional()
  @IsEnum(NRCANSubType)
  nrcanSubType?: NRCANSubType;

  @ApiPropertyOptional({ type: "boolean" })
  @Transform(({ value }) =>
    typeof value === "string" && isBooleanString(value.trim().toLowerCase())
      ? value.trim().toLowerCase() === "true"
      : value
  )
  @IsOptional()
  @IsBoolean()
  isRegulated?: boolean;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  ogdId?: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "For basic user and organization admin, Organization ID will be auto-set according to current user's organization"
  })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  organizationId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetOgdFilingsResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [OgdFiling] })
  data: Array<OgdFiling>;
}

export class CreateOgdFilingDto {
  @ApiPropertyOptional({
    type: "boolean",
    default: false,
    description: "If true, all other fields will be ignored"
  })
  @IsOptional()
  @IsBoolean()
  isExcluded?: boolean;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  extensionCode?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  endUse?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1, maxLength: 3, description: "Code between 1-999" })
  @IsOptional()
  @IsNumberStringBetween(1, 999)
  airsType?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  airsReferenceNumber?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  productCategory?: string;

  @ApiPropertyOptional({ enum: GeneralImportPermit })
  @IsOptional()
  @IsEnum(GeneralImportPermit)
  generalImportPermit?: GeneralImportPermit;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  brandName?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  manufacturerId?: number;

  @ApiPropertyOptional({ enum: TCTireType })
  @IsOptional()
  @IsEnum(TCTireType)
  tireType?: TCTireType;

  @ApiPropertyOptional({ enum: TCTireSize })
  @IsOptional()
  @IsEnum(TCTireSize)
  tireSize?: TCTireSize;

  @ApiPropertyOptional({ enum: TCTireCompliance })
  @IsOptional()
  @IsEnum(TCTireCompliance)
  tireCompliance?: TCTireCompliance;

  @ApiPropertyOptional({ enum: TCTireClass })
  @IsOptional()
  @IsEnum(TCTireClass)
  tireClass?: TCTireClass;

  @ApiPropertyOptional({ enum: ECCCSubType })
  @IsOptional()
  @IsEnum(ECCCSubType)
  ecccSubType?: ECCCSubType;

  @ApiPropertyOptional({ enum: ECCCWildlifeCompliance })
  @IsOptional()
  @IsEnum(ECCCWildlifeCompliance)
  wildlifeCompliance?: ECCCWildlifeCompliance;

  @ApiPropertyOptional({ enum: ECCCEmissionProgram })
  @IsOptional()
  @IsEnum(ECCCEmissionProgram)
  emissionProgram?: ECCCEmissionProgram;

  @ApiPropertyOptional({ enum: NRCANSubType })
  @IsOptional()
  @IsEnum(NRCANSubType)
  nrcanSubType?: NRCANSubType;

  @ApiPropertyOptional({ type: "boolean" })
  @IsOptional()
  @IsBoolean()
  isRegulated?: boolean;

  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  ogdId: number;
}

export class EditOgdFilingDto {
  @ApiPropertyOptional({
    type: "boolean",
    default: false,
    description: "If true, all other fields will be ignored"
  })
  @IsOptional()
  @IsBoolean()
  isExcluded?: boolean;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  extensionCode?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  endUse?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1, maxLength: 3, description: "Code between 1-999" })
  @IsOptional()
  @IsNumberStringBetween(1, 999)
  airsType?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  airsReferenceNumber?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  productCategory?: string;

  @ApiPropertyOptional({ enum: GeneralImportPermit })
  @IsOptional()
  @IsEnum(GeneralImportPermit)
  generalImportPermit?: GeneralImportPermit;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  brandName?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  manufacturerId?: number;

  @ApiPropertyOptional({ enum: TCTireType })
  @IsOptional()
  @IsEnum(TCTireType)
  tireType?: TCTireType;

  @ApiPropertyOptional({ enum: TCTireSize })
  @IsOptional()
  @IsEnum(TCTireSize)
  tireSize?: TCTireSize;

  @ApiPropertyOptional({ enum: TCTireCompliance })
  @IsOptional()
  @IsEnum(TCTireCompliance)
  tireCompliance?: TCTireCompliance;

  @ApiPropertyOptional({ enum: TCTireClass })
  @IsOptional()
  @IsEnum(TCTireClass)
  tireClass?: TCTireClass;

  @ApiPropertyOptional({ enum: ECCCSubType })
  @IsOptional()
  @IsEnum(ECCCSubType)
  ecccSubType?: ECCCSubType;

  @ApiPropertyOptional({ enum: ECCCWildlifeCompliance })
  @IsOptional()
  @IsEnum(ECCCWildlifeCompliance)
  wildlifeCompliance?: ECCCWildlifeCompliance;

  @ApiPropertyOptional({ enum: ECCCEmissionProgram })
  @IsOptional()
  @IsEnum(ECCCEmissionProgram)
  emissionProgram?: ECCCEmissionProgram;

  @ApiPropertyOptional({ enum: NRCANSubType })
  @IsOptional()
  @IsEnum(NRCANSubType)
  nrcanSubType?: NRCANSubType;

  @ApiPropertyOptional({ type: "boolean" })
  @IsOptional()
  @IsBoolean()
  isRegulated?: boolean;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  ogdId?: number;
}

export class CreateOgdFilingAndMatchingRuleDto {
  @ApiProperty({ type: CreateOgdFilingDto })
  @Type(() => CreateOgdFilingDto)
  @ValidateNested()
  ogdFiling: CreateOgdFilingDto;

  @ApiProperty({ type: CreateMatchingRuleWithoutSourceDto })
  @Type(() => CreateMatchingRuleWithoutSourceDto)
  @ValidateNested()
  matchingRule: CreateMatchingRuleWithoutSourceDto;

  @ApiPropertyOptional({ type: [CreateMatchingConditionDto] })
  @Type(() => CreateMatchingConditionDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  matchingConditions?: Array<CreateMatchingConditionDto>;
}

export class EditOgdFilingAndMatchingRuleDto {
  @ApiPropertyOptional({ type: EditOgdFilingDto })
  @Type(() => EditOgdFilingDto)
  @IsOptional()
  @ValidateNested()
  ogdFiling?: EditOgdFilingDto;

  @ApiPropertyOptional({ type: EditMatchingRuleWithoutSourceDto })
  @Type(() => EditMatchingRuleWithoutSourceDto)
  @IsOptional()
  @ValidateNested()
  matchingRule?: EditMatchingRuleWithoutSourceDto;

  @ApiPropertyOptional({ type: BatchUpdateMatchingConditionsDto })
  @Type(() => BatchUpdateMatchingConditionsDto)
  @IsOptional()
  @ValidateNested()
  matchingConditions?: BatchUpdateMatchingConditionsDto;
}

export class CreateOrEditOgdFilingAndMatchingRuleResponseDto {
  @ApiProperty({ type: OgdFiling })
  ogdFiling: OgdFiling;

  @ApiProperty({ type: MatchingRule })
  matchingRule: MatchingRule;
}
