import { ApiProperty } from "@nestjs/swagger";

export class BaseGetAuthorizationUriResponseDto {
  @ApiProperty({ type: "string", format: "uri" })
  authorizationUri: string;
}

export class GetDocusignAuthorizationUriResponseDto extends BaseGetAuthorizationUriResponseDto {}

export class AuthorizationCallbackResponseDto {
  @ApiProperty({ example: "Docusign Authorization Successful" })
  message: string;
}
