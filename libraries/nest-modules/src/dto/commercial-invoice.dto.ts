import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
  MinLength,
  ValidateIf
} from "class-validator";
import { IsExist } from "../database-validator/decorators";
import { CommercialInvoice } from "../entities/commercial-invoice.entity";
import { Country } from "../entities/country.entity";
import { State } from "../entities/state.entity";
import { Currency, TradePartnerColumn, WeightUOM } from "../types";
import { CommercialInvoiceColumn, PackageUOM } from "../types/commercial-invoice.types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import { ValidateCommercialInvoiceLineComplianceResponseDto } from "./commercial-invoice-line.dto";

export class CreateCommercialInvoiceDto {
  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  invoiceNumber: string;

  @ApiProperty({ enum: Currency, default: Currency.CAD })
  @IsNotEmpty()
  @IsEnum(Currency)
  currency: Currency;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @IsOptional()
  @Type((type) => Date)
  @IsDate()
  invoiceDate?: Date;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  poNumber?: string;

  @ApiProperty({ type: "number", format: "float", minimum: 0 })
  @IsNumber()
  @Min(0)
  grossWeight: number;

  @ApiProperty({ enum: WeightUOM, default: WeightUOM.KILOGRAM })
  @IsEnum(WeightUOM)
  @IsOptional()
  weightUOM?: WeightUOM;

  @ApiProperty({ type: "integer", minimum: 0, default: 0 })
  @IsInt()
  @Min(0)
  numberOfPackages: number;

  @ApiProperty({ enum: PackageUOM })
  @IsEnum(PackageUOM)
  @IsOptional()
  packageUOM?: PackageUOM;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  includedTransCost?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  includedPackCost?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  includedMiscCost?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  excludedTransCost?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  excludedPackCost?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  excludedMiscCost?: number;

  @ApiPropertyOptional({ type: "boolean" })
  @IsOptional()
  @IsBoolean()
  valueIncludesDuty?: boolean;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  additionalInfo?: string;

  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  shipmentId: number;

  @ApiProperty({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @IsExist(Country)
  countryOfExportId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  // only validate if countryOfExportId is provided
  @ValidateIf((entity: CreateCommercialInvoiceDto) => !!entity.countryOfExportId)
  @IsExist(State, "id", (entity: CreateCommercialInvoiceDto) => ({
    countryId: entity.countryOfExportId
  }))
  stateOfExportId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  exporterId?: number;

  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  vendorId: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  manufacturerId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  purchaserId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  shipToId?: number;
}

export class EditCommercialInvoiceDto {
  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  invoiceNumber?: string;

  @ApiPropertyOptional({ enum: Currency })
  @IsOptional()
  @IsEnum(Currency)
  currency?: Currency;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @IsOptional()
  @Type(() => Date)
  @IsDate()
  invoiceDate?: Date;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  poNumber?: string;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  grossWeight?: number;

  @ApiPropertyOptional({ enum: WeightUOM })
  @IsOptional()
  @IsEnum(WeightUOM)
  weightUOM?: WeightUOM;

  @ApiPropertyOptional({ type: "integer", minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  numberOfPackages?: number;

  @ApiPropertyOptional({ enum: PackageUOM })
  @IsOptional()
  @IsEnum(PackageUOM)
  packageUOM?: PackageUOM;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  includedTransCost?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  includedPackCost?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  includedMiscCost?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  excludedTransCost?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  excludedPackCost?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  excludedMiscCost?: number;

  @ApiPropertyOptional({ type: "boolean" })
  @IsOptional()
  @IsBoolean()
  valueIncludesDuty?: boolean;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  additionalInfo?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  shipmentId?: number;

  // @ApiPropertyOptional({ type: 'integer', minimum: 1 })
  // @IsOptional()
  // @IsNumber()
  // @Min(1)
  // originId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @IsExist(Country)
  countryOfExportId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @IsExist(State, "id", (entity: EditCommercialInvoiceDto) => ({
    countryId: entity.countryOfExportId
  }))
  stateOfExportId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  exporterId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  vendorId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  manufacturerId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  purchaserId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  shipToId?: number;
}

export class GetCommercialInvoicesDto extends GetManyDto {
  @ApiPropertyOptional({
    enum: CommercialInvoiceColumn,
    default: CommercialInvoiceColumn.id
  })
  @IsOptional()
  @IsEnum(CommercialInvoiceColumn)
  sortBy?: CommercialInvoiceColumn;

  @ApiPropertyOptional({
    type: "boolean",
    default: false,
    description: "If true, the CI lines would include all nested records"
  })
  @Transform(({ value }) =>
    typeof value === "string" && ["true", "false"].includes(value.trim().toLowerCase())
      ? value.trim().toLowerCase() === "true"
      : value
  )
  @IsOptional()
  @IsBoolean()
  expandLines?: boolean;

  @ApiPropertyOptional({ type: "integer" })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  candataId?: number;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  invoiceNumber?: string;

  @ApiPropertyOptional({ enum: Currency })
  @IsOptional()
  @IsEnum(Currency)
  currency?: Currency;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @IsOptional()
  @Type((type) => Date)
  @IsDate()
  invoiceDate?: Date;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  poNumber?: string;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @Type((type) => Number)
  @IsOptional()
  @IsNumber()
  @Min(0)
  grossWeight?: number;

  @ApiPropertyOptional({ enum: WeightUOM })
  @IsOptional()
  @IsEnum(WeightUOM)
  weightUOM?: WeightUOM;

  @ApiPropertyOptional({ type: "integer", minimum: 0 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(0)
  numberOfPackages?: number;

  @ApiPropertyOptional({ enum: PackageUOM })
  @IsOptional()
  @IsEnum(PackageUOM)
  packageUOM?: PackageUOM;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  @Type((type) => Number)
  @Min(0)
  includedTransCost?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  @Type((type) => Number)
  @Min(0)
  includedPackCost?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  @Type((type) => Number)
  @Min(0)
  includedMiscCost?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  @Type((type) => Number)
  @Min(0)
  excludedTransCost?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  @Type((type) => Number)
  @Min(0)
  excludedPackCost?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  @Type((type) => Number)
  @Min(0)
  excludedMiscCost?: number;

  @ApiPropertyOptional({ type: "boolean" })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === "true" || value === true)
  valueIncludesDuty?: boolean;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  additionalInfo?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type((type) => Number)
  shipmentId?: number;

  // TODO: Remove this field
  /**
   * @deprecated This field is deprecated. Please use the `origin` field in commercial invoice line instead.
   */
  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type((type) => Number)
  originId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type((type) => Number)
  countryOfExportId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type((type) => Number)
  stateOfExportId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type((type) => Number)
  exporterId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type((type) => Number)
  vendorId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type((type) => Number)
  manufacturerId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type((type) => Number)
  organizationId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type((type) => Number)
  shipToId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type((type) => Number)
  lastEditedById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type((type) => Number)
  createdById?: number;
}

export class GetCommercialInvoicesResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [CommercialInvoice] })
  commercialInvoices: Array<CommercialInvoice>;
}

export class ValidateCommercialInvoiceComplianceResponseDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  commercialInvoiceId: number;

  @ApiProperty({
    type: "enum",
    enum: CommercialInvoiceColumn,
    isArray: true,
    description: "List of missing fields required for entry submission"
  })
  missingFields: Array<CommercialInvoiceColumn>;

  @ApiProperty({
    type: "enum",
    enum: TradePartnerColumn,
    isArray: true,
    description: "List of missing fields in the `shipTo` trade partner"
  })
  shipToMissingFields: Array<TradePartnerColumn>;

  @ApiProperty({
    type: "enum",
    enum: TradePartnerColumn,
    isArray: true,
    description: "List of missing fields in the `vendor` trade partner"
  })
  vendorMissingFields: Array<TradePartnerColumn>;

  @ApiProperty({
    type: () => [ValidateCommercialInvoiceLineComplianceResponseDto],
    description: "List of non-compliant commercial invoice lines"
  })
  nonCompliantLines: Array<ValidateCommercialInvoiceLineComplianceResponseDto>;
}
