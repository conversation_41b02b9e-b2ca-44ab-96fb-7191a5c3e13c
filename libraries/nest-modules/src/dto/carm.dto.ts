import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDateString,
  IsDecimal,
  IsEnum,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  Length,
  Matches,
  Max,
  <PERSON>Length,
  Min,
  MinLength,
  ValidateIf,
  ValidateNested
} from "class-validator";
import {
  CarmLanguageCode,
  CarmStatus,
  GetIndividualTariffNumberCustomsDutiesSelect,
  GetListOfCustomsDutyRecordsOrderBy,
  GetListOfCustomsDutyRecordsSelect,
  GetListOfTariffNumbersExpand,
  GetListOfTariffNumbersOrderBy,
  GetListOfTariffNumbersSelect
} from "../types";

export class CarmErrorResponseDto {
  @ApiProperty({
    type: "object",
    properties: {
      code: { type: "string", example: "Z_TRM_TARIFF_QUERY/003" },
      message: { type: "string", example: "No Data Selected" }
    }
  })
  @IsObject()
  error: {
    code: string;
    message: string;
  };
}

class BaseCarmGetListOfRecordsDto {
  @ApiPropertyOptional({
    type: "integer",
    minimum: 0,
    description: "Show only the first n items"
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  $top?: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 0,
    description: "Skip the first n items"
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  $skip?: number;

  @ApiPropertyOptional({
    type: "string",
    description: "Filter items by property values"
  })
  @IsOptional()
  @IsString()
  $filter?: string;

  @ApiPropertyOptional({
    type: "enum",
    enum: ["allpages"],
    description: "Include count of items"
  })
  @IsOptional()
  @IsEnum(["allpages"])
  $inlinecount?: "allpages";
}

export class GetListOfTariffNumbersDto extends BaseCarmGetListOfRecordsDto {
  @ApiPropertyOptional({
    type: "string",
    description: "Search Tariff Numbers and Descriptions by search phrases"
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    type: "enum",
    enum: GetListOfTariffNumbersOrderBy,
    description: "Order items by property values"
  })
  @IsOptional()
  @IsEnum(GetListOfTariffNumbersOrderBy)
  $orderby?: GetListOfTariffNumbersOrderBy;

  @ApiPropertyOptional({
    type: "enum",
    enum: GetListOfTariffNumbersSelect,
    description: "Select properties to be returned"
  })
  @IsOptional()
  @IsEnum(GetListOfTariffNumbersSelect)
  $select?: GetListOfTariffNumbersSelect;

  @ApiPropertyOptional({
    type: "enum",
    enum: GetListOfTariffNumbersExpand,
    description: "Expand related entities"
  })
  @IsOptional()
  @IsEnum(GetListOfTariffNumbersExpand)
  $expand?: GetListOfTariffNumbersExpand;
}

export class CarmTariffNumberContentPropertiesDto {
  @ApiProperty({ type: "string", example: "0101.21.00.00" })
  @IsNotEmpty()
  @IsString()
  TariffNumber: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @IsNotEmpty()
  @IsDateString()
  AsOfDate: string;

  @ApiProperty({ type: "enum", enum: ["EN", "FR"] })
  @IsEnum(["EN", "FR"])
  Language: "EN" | "FR";

  @ApiPropertyOptional({ type: "integer" })
  @IsInt()
  SpecialClassificationIndicator: number;

  @ApiProperty({ type: "string", format: "date-time" })
  @IsDateString()
  TariffNumberValidStartDate: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @IsDateString()
  TariffNumberValidEndDate: string;

  @ApiProperty({ type: "string", example: "T_INITIAL" })
  @IsString()
  TariffNumberChangeReason: string;

  @ApiProperty({ type: "string", example: "0101.21.00" })
  @IsString()
  TariffItemNumber: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @IsDateString()
  TariffItemEffectiveDate: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @IsDateString()
  TariffItemExpiryDate: string;

  @ApiProperty({ type: "string", example: "T_INITIAL" })
  @IsString()
  TariffItemChangeReasonCode: string;

  @ApiProperty({ type: "string" })
  @IsString()
  AreaCode1: string;

  @ApiProperty({ type: "string" })
  @IsString()
  AreaCode2: string;

  @ApiProperty({ type: "string" })
  @IsString()
  AreaCode3: string;

  @ApiPropertyOptional({ type: "string", example: "NMB" })
  @IsOptional()
  @IsString()
  UnitOfMeasureCode?: string;

  @ApiProperty({ type: "boolean" })
  @IsBoolean()
  ExchangeDateFlag: boolean;

  @ApiProperty({ type: "boolean" })
  @IsBoolean()
  PermitRequiredIndicator: boolean;

  @ApiProperty({ type: "boolean" })
  @IsBoolean()
  QuotaIndicator: boolean;

  @ApiProperty({ type: "boolean" })
  @IsBoolean()
  GSTIndicator: boolean;

  @ApiProperty({ type: "boolean" })
  @IsBoolean()
  InactiveIndicator: boolean;

  @ApiProperty({
    type: "string",
    example: "Live horses, asses, mules and hinnies. - Horses: - Pure-bred breeding animals"
  })
  @IsString()
  Description: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @IsDateString()
  UpdateOn: string;
}

export class CarmTariffNumberContentDto {
  @ApiProperty({ type: () => CarmTariffNumberContentPropertiesDto })
  @ValidateNested()
  @Type(() => CarmTariffNumberContentPropertiesDto)
  properties: CarmTariffNumberContentPropertiesDto;
}

export class CarmTariffNumberEntryDto {
  @ApiProperty({
    type: "string",
    example: `https://IPORTAL-IPORTAIL.CBSA-ASFC.CA:443/sap/opu/odata/sap/TARIFF/tariffClassifications(TariffNumber='0101.21.00.00',AsOfDate=datetime'2024-12-02T00%3A00%3A00')`
  })
  @IsString()
  @IsUrl()
  id: string;

  @ApiProperty({
    type: "string",
    example: `tariffClassifications(TariffNumber='0101.21.00.00',AsOfDate=datetime'2024-12-02T00%3A00%3A00')`
  })
  @IsString()
  title: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @IsDateString()
  updated: string;

  @ApiPropertyOptional({ type: "string", isArray: true })
  @IsOptional()
  @IsString({ each: true })
  link?: Array<string>;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({ type: () => CarmTariffNumberContentDto })
  @ValidateNested()
  @Type(() => CarmTariffNumberContentDto)
  content: CarmTariffNumberContentDto;
}

export class BaseCarmFeedDto {
  @ApiProperty({
    type: "string",
    example: "https://ccapi-ipacc.cbsa-asfc.cloud-nuage.canada.ca/v1/tariff-srv/tariffClassifications"
  })
  @IsString()
  @IsUrl()
  id: string;

  @ApiProperty({ type: "string", example: "tariffClassifications" })
  @IsString()
  title: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @IsDateString()
  updated: string;

  @ApiPropertyOptional({
    type: "object",
    properties: {
      name: { type: "string" }
    }
  })
  @IsOptional()
  @IsObject()
  author?: { name: string };

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  link?: string;
}

export class GetListOfTariffNumbersFeedDto extends BaseCarmFeedDto {
  @ApiProperty({ type: () => [CarmTariffNumberEntryDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CarmTariffNumberEntryDto)
  entry: Array<CarmTariffNumberEntryDto>;
}

export class GetListOfTariffNumbersResponseDto {
  @ApiProperty({ type: () => GetListOfTariffNumbersFeedDto })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => GetListOfTariffNumbersFeedDto)
  feed: GetListOfTariffNumbersFeedDto;
}

export class GetIndividualTariffNumberDto {
  @ApiProperty({
    type: "string",
    pattern: `^[0-9]{4}\\.[0-9]{2}\\.[0-9]{2}\\.[0-9]{2}$`
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[0-9]{4}\.[0-9]{2}\.[0-9]{2}\.[0-9]{2}$/, {
    message: "Invalid tariff number format"
  })
  TariffNumber: string;

  @ApiProperty({
    type: "string",
    pattern: `^\\d{4}-\\d{2}-\\\d{2}T\\d{2}:\\d{2}:\\d{2}$`
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/, {
    message: "Invalid AsOfDate format"
  })
  AsOfDate: string;

  @ApiPropertyOptional({
    type: "enum",
    enum: GetListOfTariffNumbersSelect,
    description: "Select properties to be returned"
  })
  @IsOptional()
  @IsEnum(GetListOfTariffNumbersSelect)
  $select?: GetListOfTariffNumbersSelect;

  @ApiPropertyOptional({
    type: "enum",
    enum: GetListOfTariffNumbersExpand,
    description: "Expand related entities"
  })
  @IsOptional()
  @IsEnum(GetListOfTariffNumbersExpand)
  $expand?: GetListOfTariffNumbersExpand;
}

export class GetIndividualTariffNumberResponseDto {
  @ApiProperty({ type: () => CarmTariffNumberEntryDto })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CarmTariffNumberEntryDto)
  entry: CarmTariffNumberEntryDto;
}

export class GetIndividualTariffNumberCustomsDutiesDto {
  @ApiProperty({
    type: "string",
    pattern: `^[0-9]{4}\\.[0-9]{2}\\.[0-9]{2}\\.[0-9]{2}$`
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[0-9]{4}\.[0-9]{2}\.[0-9]{2}\.[0-9]{2}$/, {
    message: "Invalid tariff number format"
  })
  TariffNumber: string;

  @ApiProperty({
    type: "string",
    pattern: `^\\d{4}-\\d{2}-\\\d{2}T\\d{2}:\\d{2}:\\d{2}$`
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/, {
    message: "Invalid AsOfDate format"
  })
  AsOfDate: string;

  @ApiPropertyOptional({
    type: "enum",
    enum: GetIndividualTariffNumberCustomsDutiesSelect,
    description: "Select properties to be returned"
  })
  @IsOptional()
  @IsEnum(GetIndividualTariffNumberCustomsDutiesSelect)
  $select?: GetIndividualTariffNumberCustomsDutiesSelect;
}

export class CarmCustomsDutyContentPropertiesDto {
  @ApiProperty({ type: "string", example: "0101.21.00" })
  @IsString()
  TariffItemNumber: string;

  @ApiProperty({ type: "integer", example: 3 })
  @IsInt()
  TariffTreatmentCode: number;

  @ApiProperty({ type: "string", format: "date-time" })
  @IsDateString()
  AsOfDate: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @IsDateString()
  CustomsDutyValidStartDate: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @IsDateString()
  CustomsDutyValidEndDate: string;

  @ApiProperty({ type: "enum", enum: ["", "X"] })
  @IsEnum(["", "X"])
  FreeQualifierIndicator: "X" | "";

  @ApiProperty({ type: "number", format: "float" })
  @IsNumber()
  SpecificRateRegValue: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    maximum: 3,
    description: "1-Dollar, 2-Cents, 3-Percentage"
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(3)
  SpecificRateRegQualifierCode?: number;

  @ApiProperty({ type: "number", format: "float" })
  @IsNumber()
  SpecificRateMinValue: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    maximum: 3,
    description: "1-Dollar, 2-Cents, 3-Percentage"
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(3)
  SpecificRateMinQualifierCode?: number;

  @ApiProperty({ type: "number", format: "float" })
  @IsNumber()
  SpecificRateMaxValue: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    maximum: 3,
    description: "1-Dollar, 2-Cents, 3-Percentage"
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(3)
  SpecificRateMaxQualifierCode?: number;

  @ApiProperty({ type: "number", format: "float" })
  @IsNumber()
  AdValoremRateRegValue: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    maximum: 3,
    description: "1-Dollar, 2-Cents, 3-Percentage"
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(3)
  AdValoremRateRegValueQualifierCode?: number;

  @ApiProperty({ type: "number", format: "float" })
  @IsNumber()
  AdValoremRateMinValue: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    maximum: 3,
    description: "1-Dollar, 2-Cents, 3-Percentage"
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(3)
  AdValoremRateMinQualifierCode?: number;

  @ApiProperty({ type: "number", format: "float" })
  @IsNumber()
  AdValoremRateMaxValue: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    maximum: 3,
    description: "1-Dollar, 2-Cents, 3-Percentage"
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(3)
  AdValoremRateMaxQualifierCode?: number;

  @ApiPropertyOptional({ type: "boolean" })
  @IsOptional()
  @IsBoolean()
  InactiveIndicator?: boolean;

  @ApiProperty({ type: "string", format: "date-time" })
  @IsDateString()
  UpdateOn: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  UnitOfMeasureCode?: string;
}

export class CarmCustomsDutyContentDto {
  @ApiProperty({ type: () => CarmCustomsDutyContentPropertiesDto })
  @ValidateNested()
  @Type(() => CarmCustomsDutyContentPropertiesDto)
  properties: CarmCustomsDutyContentPropertiesDto;
}

export class CarmCustomsDutyEntryDto {
  @ApiProperty({
    type: "string",
    example: `https://IPORTAL-IPORTAIL.CBSA-ASFC.CA:443/sap/opu/odata/sap/TARIFF/customsDuties(TariffItemNumber='0101.21.00',TariffTreatmentCode='003',AsOfDate=datetime'2024-12-02T00%3A00%3A00')`
  })
  @IsString()
  @IsUrl()
  id: string;

  @ApiProperty({
    type: "string",
    example: `customsDuties(TariffItemNumber='0101.21.00',TariffTreatmentCode='003',AsOfDate=datetime'2024-12-02T00%3A00%3A00')`
  })
  @IsString()
  title: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @IsDateString()
  updated: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  link?: string;

  @ApiProperty({ type: () => CarmCustomsDutyContentDto })
  @ValidateNested()
  @Type(() => CarmCustomsDutyContentDto)
  content: CarmCustomsDutyContentDto;
}

export class GetIndividualTariffNumberCustomsDutiesFeedDto extends BaseCarmFeedDto {
  @ApiProperty({ type: () => [CarmCustomsDutyEntryDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CarmCustomsDutyEntryDto)
  entry: Array<CarmCustomsDutyEntryDto>;
}

export class GetIndividualTariffNumberCustomsDutiesResponseDto {
  @ApiProperty({ type: () => GetIndividualTariffNumberCustomsDutiesFeedDto })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => GetIndividualTariffNumberCustomsDutiesFeedDto)
  feed: GetIndividualTariffNumberCustomsDutiesFeedDto;
}

export class GetListOfCustomsDutyRecordsDto extends BaseCarmGetListOfRecordsDto {
  @ApiPropertyOptional({
    type: "string",
    description: "Search Tariff Item Numbers and Tariff Treatment Codes by search phrases"
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    type: "enum",
    enum: GetListOfCustomsDutyRecordsOrderBy,
    description: "Order items by property values"
  })
  @IsOptional()
  @IsEnum(GetListOfCustomsDutyRecordsOrderBy)
  $orderby?: GetListOfCustomsDutyRecordsOrderBy;

  @ApiPropertyOptional({
    type: "enum",
    enum: GetListOfCustomsDutyRecordsSelect,
    description: "Select properties to be returned"
  })
  @IsOptional()
  @IsEnum(GetListOfCustomsDutyRecordsSelect)
  $select?: GetListOfCustomsDutyRecordsSelect;
}

export class GetListOfCustomsDutyRecordsFeedDto extends BaseCarmFeedDto {
  @ApiProperty({ type: () => [CarmCustomsDutyEntryDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CarmCustomsDutyEntryDto)
  entry: Array<CarmCustomsDutyEntryDto>;
}

// Duties and Taxes Calculator
export class GetListOfCustomsDutyRecordsResponseDto {
  @ApiProperty({ type: () => GetListOfCustomsDutyRecordsFeedDto })
  @ValidateNested()
  @Type(() => GetListOfCustomsDutyRecordsFeedDto)
  feed: GetListOfCustomsDutyRecordsFeedDto;
}

export class DutiesAndTaxesCalculatorDateTimeDto {
  @ApiProperty({
    type: "string",
    pattern: `^\\d{4}(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])$`
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{4}(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])$/, {
    message: "Invalid date format"
  })
  DateTimeString: string;
}

export class DutiesAndTaxesCalculatorAmountDto {
  @ApiProperty({ type: "string", format: "decimal" })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{1,11}\.\d{2}$/, {
    message: "Invalid decimal format"
  })
  @IsDecimal()
  TextValue: string;

  @ApiProperty({ type: "string", minLength: 3, maxLength: 3 })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(3)
  $currencyID: string;
}

export class DutiesAndTaxesCalculatorQuantityDto {
  @ApiProperty({
    type: "string",
    format: "decimal",
    pattern: "^\\d{1,10}\\.\\d{3}$",
    description: "Total digits: 13, fractional digits: 3"
  })
  @IsDecimal()
  @IsNotEmpty()
  TextValue: string;

  @ApiProperty({
    type: "string",
    minLength: 3,
    maxLength: 3
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(3)
  $unitCode: string;
}

export class DutiesAndTaxesCalculatorClassificationDto {
  @ApiProperty({
    type: "string",
    minLength: 4,
    maxLength: 10,
    description: `Numeric code used to identify and categorize goods, which can be a 10 digit classification number or a 4 digit chapter 99 Tariff Code. Enter the classification number of the commodity as indicated in the customs tariff. Complete this field with Classification type '10' to declare the unique Classification number, formatted XXXXXXXX. Complete this field with Classification type '06' to declare the unique Chapter 99 Tariff Code, formatted XXXX.`
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(4)
  @MaxLength(10)
  ID: string;

  @ApiProperty({
    type: "enum",
    enum: ["10", "06", "6"],
    description:
      "Coded value which identifies the type of the segment instance as a 10 digit classification number or 4 digit Chapter 99 Tariff Code. Enter the coded value to identify the segment instance as a 10 digit classification number or 4 digit Chapter 99 Tariff Code."
  })
  @IsIn(["10", "06", "6"])
  @IsNotEmpty()
  BindingTariffReferenceID: "10" | "06" | "6";
}

export class DutiesAndTaxesCalculatorCountryDto {
  @ApiProperty({
    type: "string",
    minLength: 2,
    maxLength: 2,
    description: `Value that identifies a location from where the goods were shipped directly to the receiving location Enter the country code the goods were exported from. Available country codes can be found in List 2 of D17-1-10 Coding of Customs Accounting Documents.`
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(2)
  @MaxLength(2)
  CountryCode: string;
}

export class DutiesAndTaxesCalculatorStatusDto {
  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorDateTimeDto,
    description:
      "Date of release of the goods declared for the transaction being submitted. Enter the date the goods were released into the country. Show the date formatted as CCYYMMDD in the EST/EDT time zone."
  })
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorDateTimeDto)
  @IsNotEmpty()
  ReleaseDateTime: DutiesAndTaxesCalculatorDateTimeDto;
}

export class DutiesAndTaxesCalculatorAdditionalDocumentDto {
  @ApiProperty({
    type: "string",
    minLength: 1,
    maxLength: 25,
    description: `Unique number identifying the additional document: Complete with unique Duty Relief Program number applicable for the commodity line. Statement Type Code 'DRL' must be transmitted as the instance type; Complete with unique Order in Council number applicable for the commodity line. Statement Type Code 'OIC' must be transmitted as the instance type.`
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(25)
  ID: string;

  @ApiProperty({
    type: "enum",
    enum: ["DRL", "OIC"],
    description:
      "Coded value which identifies the type of the segment instance. Enter a coded value to identify the type of document being declared within this instance of the segment."
  })
  @IsIn(["DRL", "OIC"])
  @IsNotEmpty()
  TypeCode: "DRL" | "OIC";
}

export class DutiesAndTaxesCalculatorAdditionalInformationDto {
  @ApiPropertyOptional({
    type: "string",
    minLength: 1,
    maxLength: 17,
    description: `
    Unique code that indicates an additional information regarding the commodity related to the additional statement type

    - Must complete with the Value for Duty Code for the commodity line. Statement Type Code 'VDC' must be transmitted as the instance type.
    - Must complete with the SIMA Subject Code value to identify the commodity as SIMA Subject, Non-Subject or Undertaking. Statement Type Code 'ASJ' must be transmitted as the instance type.
    - Must complete with the Surtax Measure Subject Code value to identify the commodity as Surtax Measure Subject or Non-Subject. Statement Type Code 'BSJ' must be transmitted as the instance type.
    - Must complete with the Safeguard Measure Subject Code value to identify the commodity as Safeguard Measure Subject or Non-Subject. Statement Type Code 'CSJ' must be transmitted as the instance type.
    - Complete with the Measure in Force code value when SIMA is Subject or Undertaking. Statement Type Code 'MIF' must be transmitted as the instance type.

    Available codes can be found in the Measures in force`
  })
  @IsString()
  @MinLength(1)
  @MaxLength(17)
  @ValidateIf((o) => ["ASJ", "BSJ", "CSJ", "MIF", "VDC"].includes(o.TypeCode))
  StatementCode?: string;

  @ApiPropertyOptional({
    type: () => DutiesAndTaxesCalculatorDateTimeDto,
    description: `
    Date of which the commodity will enter/exit a Customs Bonded Warehouse or economy

    - Complete for any commodity line, when a time control is applicable, to declare the estimated dates the goods will enter the economy. Statement Type Code 'TLS' must be transmitted as the instance type.
    - Complete for any commodity line, when a time control is applicable, to declare the estimated dates the goods will exit the economy. Statement Type Code 'TLE' must be transmitted as the instance type.
    `
  })
  @Type(() => DutiesAndTaxesCalculatorDateTimeDto)
  @ValidateNested()
  @ValidateIf((o) => o.TypeCode === "TLS" || o.TypeCode === "TLE")
  LimitDateTime?: DutiesAndTaxesCalculatorDateTimeDto;

  @ApiPropertyOptional({
    type: "enum",
    enum: ["ASJ", "BSJ", "CSJ", "MIF", "TLE", "TLS", "VDC"],
    description: `
    Coded value which identifies the type of the segment instance. Complete the Type Code to indicate the type of information being relayed in the segment instance.
    `
  })
  @IsIn(["ASJ", "BSJ", "CSJ", "MIF", "TLE", "TLS", "VDC"])
  @IsNotEmpty()
  StatementTypeCode: "ASJ" | "BSJ" | "CSJ" | "MIF" | "TLE" | "TLS" | "VDC";
}

export class DutiesAndTaxesCalculatorConstituentDto {
  @ApiProperty({
    type: "number",
    format: "float",
    minLength: 1,
    maxLength: 5,
    description:
      "Percentage of alcohol contained in the good. Enter the alcohol percentage of the commodity being declared. Show to a maximum of two decimal points."
  })
  @IsNumber()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(5)
  ElementPercentNumeric: number;
}

export class DutiesAndTaxesCalculatorInvoiceLineDto {
  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorAmountDto,
    description:
      "Total price on invoice of subject goods being imported for this item/model. Enter the total price on the invoice of subject goods being imported for this item/model to a maximum of 2 decimal places."
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorAmountDto)
  ItemChargeAmount: DutiesAndTaxesCalculatorAmountDto;
}

export class DutiesAndTaxesCalculatorDutyTaxFeeAdditionalInformationDto {
  @ApiProperty({
    type: "enum",
    enum: ["X"],
    description: `Indicator to identify the amount of SIMA, Surtax, or Safeguard will be self-declared. Transmit this field within the duty tax fee loop for the associated Duty type to self-declare a SIMA Anti-dumping, Countervailing, Surtax or Safeguard Surtax amount.`
  })
  @IsIn(["X"])
  @IsNotEmpty()
  RequestOverrideCode: "X";
}

export class DutiesAndTaxesCalculatorDutyTaxAssessmentBasisDto {
  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorAmountDto,
    description: `Value of the goods on which a duty or tax or fee will be assessed. Complete this field with type code 'TOT' with the Value for Currency conversion. Show this amount in the currency specified on the invoice to a maximum of two decimal points.`
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorAmountDto)
  AdValoremTaxBaseAmount: DutiesAndTaxesCalculatorAmountDto;
}

export class DutiesAndTaxesCalculatorPaymentDto {
  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorAmountDto,
    description: `
    Total net duties and taxes amounts for the commodity line Show this amount to a maximum of two decimal points.

    - Complete this field with type code "CUD" to declare the Total Customs Duty net amount.
    - Complete this field with type code "EXC" to declare the Total Excise Duty net amount.
    - Complete this field with type code "FET" to declare the Total Excise Tax net amount.
    - Complete this field with type code "GST" to declare the Total GST net amount.
    - Complete this field with type code "SUR" and with RequestOverrideCode "X" to declare the Total Surtax net amount.
    - Complete this field with type code "OTH" and with RequestOverrideCode "X" to declare the Total Safeguard Surtax net amount.
    - Complete this field with type code "ADD" and with RequestOverrideCode. "X" to declare the Total Antidumping net amount.
    - Complete this field with type code "CVD" and with RequestOverrideCode "X" to declare the Total Countervailing net amount. Self Declared amounts must be in Canadian dollars to a maximum of two decimal points. If the payment amount is equal to ‘0.00’ or not applicable, the field should be left blank.`
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorAmountDto)
  PaymentAmount: DutiesAndTaxesCalculatorAmountDto;
}

export class DutiesAndTaxesCalculatorDutyTaxFeeDto {
  @ApiPropertyOptional({
    type: "string",
    minLength: 1,
    maxLength: 10,
    description: `
    Coded value which identifies the rate to be used in the duty/tax calculation. Examples: Tariff Treatment Code, Excise Tax Code, GST Code, Surtax Measure Code, Safeguard Measure Code

    - Complete this field with a 2 digit Tariff Treatment code per appendix G12. Must be completed with duty tax fee type code 'CUD'

    - This field may be completed with a 2 digit GST code per appendix G10, if applicable. Must be completed with duty tax fee type code 'GST'.

    - Complete this field with a 3 character Excise Tax code per appendix G9, if applicable. Must be completed with duty tax fee type code 'FET'.

    - Complete this field with up to 7 character Surtax Measure Code per appendix G36, if applicable. Must be completed with duty tax fee type code 'SUR'.

    - Complete this field with up to 7 character Safeguard Measure Code per appendix G37, if applicable. Must be completed with duty tax fee type code 'OTH'.`
  })
  @IsString()
  @MinLength(1)
  @MaxLength(10)
  @ValidateIf((o) => ["CUD", "GST", "FET", "SUR", "OTH"].includes(o.TypeCode))
  DutyRegimeCode?: string;

  @ApiPropertyOptional({
    type: () => DutiesAndTaxesCalculatorQuantityDto,
    description: `
    otal price on invoice of subject goods being imported for this item/model. Enter the total price on the invoice of subject goods being imported for this item/model to a maximum of three decimal places. Quantity of the goods to be declared in the dimension of the unit of measure as described in policy is different than the unit of measure assigned to the commodity in the Customs Tariff

    - Complete this field with the SIMA quantity if SIMA Subject is declared and the unit of measure of the SIMA Measure is in a different dimension than the unit of measure of the Classification code in the Customs Tariff. Must complete this field with type code 'ADD' or 'CVD'.
    `
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorQuantityDto)
  @ValidateIf((o) => o.TypeCode === "ADD" || o.TypeCode === "CVD")
  SpecificTaxBaseQuantity?: DutiesAndTaxesCalculatorQuantityDto;

  @ApiProperty({
    type: "enum",
    enum: ["ADD", "CUD", "CVD", "FET", "EXC", "GST", "OTH", "SUR", "TOT"],
    description: `Type of duty tax being applied to the commodity. Enter the coded value to which the duty/tax the Duty Tax Fee information applies.`
  })
  @IsIn(["ADD", "CUD", "CVD", "FET", "EXC", "GST", "OTH", "SUR", "TOT"])
  @IsNotEmpty()
  TypeCode: "ADD" | "CUD" | "CVD" | "FET" | "EXC" | "GST" | "OTH" | "SUR" | "TOT";

  @ApiPropertyOptional({
    type: () => DutiesAndTaxesCalculatorDutyTaxFeeAdditionalInformationDto,
    description: `Additional Information relating to the commodity duty and taxes Complete this segment to identify additional information related to the commodity.`
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorDutyTaxFeeAdditionalInformationDto)
  AdditionalInformation?: DutiesAndTaxesCalculatorDutyTaxFeeAdditionalInformationDto;

  @ApiPropertyOptional({
    type: () => DutiesAndTaxesCalculatorDutyTaxAssessmentBasisDto,
    description: `Base amount used for duties and taxes calculation. Complete this to declare the Value for Currency Conversion.Must be completed with type code "TOT".`
  })
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorDutyTaxAssessmentBasisDto)
  @ValidateIf((o) => o.TypeCode === "TOT")
  DutyTaxFeeAssessmentBasis?: DutiesAndTaxesCalculatorDutyTaxAssessmentBasisDto;

  @ApiPropertyOptional({
    type: () => DutiesAndTaxesCalculatorPaymentDto,
    description: `Information about payment details Complete this segment on form types if self-declaring the net duty tax amount if allowed by policy.`
  })
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorPaymentDto)
  Payment?: DutiesAndTaxesCalculatorPaymentDto;
}

export class DutiesAndTaxesCalculatorCommodityDto {
  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorDateTimeDto,
    description:
      "Date the shipment began their continuous and uninterrupted journey to Canada. Enter the date of direct shipment, show the date formatted as CCYYMMDD in the Time EST/EDT time zone."
  })
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorDateTimeDto)
  @IsNotEmpty()
  ExitDateTime: DutiesAndTaxesCalculatorDateTimeDto;

  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorQuantityDto,
    description: `
    Quantity of the goods being declared in the commodity line. Complete the quantity, to a maximum of three decimal points, of the commodity being declared. Must be declared in the dimension of the classification number unit of measure as it appears in the Customs Tariff. Show to a maximum of three decimal points.
    `
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorQuantityDto)
  CountQuantity: DutiesAndTaxesCalculatorQuantityDto;

  @ApiPropertyOptional({
    type: () => DutiesAndTaxesCalculatorDateTimeDto,
    description:
      "Date of sale on which terms of sale are finalized for subject goods Enter the date on which terms of sale are finalized for SIMA subject goods. This field may be declared if SIMA is subject. If no date of sale is provided the direct shipment date will be used. Show the date formatted as CCYYMMDD in the Time zone of CBSA headquarters (EST/EDT)."
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorDateTimeDto)
  AcquisitionDateTime?: DutiesAndTaxesCalculatorDateTimeDto;

  @ApiPropertyOptional({
    type: () => [DutiesAndTaxesCalculatorAdditionalDocumentDto],
    minItems: 0,
    maxItems: 2,
    description:
      "Document details relating to the commodity line. Complete this segment once per applicable Order in Council or Duty Relief Program."
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(0)
  @ArrayMaxSize(2)
  @ValidateNested({ each: true })
  @Type(() => DutiesAndTaxesCalculatorAdditionalDocumentDto)
  AdditionalDocument?: Array<DutiesAndTaxesCalculatorAdditionalDocumentDto>;

  @ApiProperty({
    type: () => [DutiesAndTaxesCalculatorAdditionalInformationDto],
    minItems: 4,
    maxItems: 8,
    description: `
    Additional information relating to the commodity line Complete this segment to declare any additional information as required.

    - Must be completed once with a Value for Duty Code.
    - Must be completed once with a SIMA Subject Code if a SIMA measure may apply.
    - Must be completed once with a Surtax Subject Code if a Surtax measure may apply.
    - Must be completed once with a Safeguard Subject Code if a Safeguard measure may apply.
    - Must complete, when a time control is applicable, with the estimated dates the goods will enter the economy.
    - Must be completed, when a time control is applicable, with the estimated dates the goods will exit the economy.
    - Must be completed when SIMA Subject or Undertaking is declared with the Measure in Force Code.
    `
  })
  @IsArray()
  @ArrayMinSize(4)
  @ArrayMaxSize(8)
  @ValidateNested({ each: true })
  @Type(() => DutiesAndTaxesCalculatorAdditionalInformationDto)
  AdditionalInformation: Array<DutiesAndTaxesCalculatorAdditionalInformationDto>;

  @ApiPropertyOptional({
    type: () => DutiesAndTaxesCalculatorConstituentDto,
    description:
      "Details regarding the element or component element in a commodity. Complete this segment if the commodity line is alcohol as required by policy."
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorConstituentDto)
  Constituent?: DutiesAndTaxesCalculatorConstituentDto;

  @ApiProperty({
    type: () => [DutiesAndTaxesCalculatorClassificationDto],
    minItems: 1,
    maxItems: 2,
    description: `Details about the non-commercial categorization of a commodity by a standard-setting organization. Complete once to identify a 10 digit classification number. Optional to identify a 4 digit Chapter 99 Tariff Code.`
  })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(2)
  @ValidateNested({ each: true })
  @Type(() => DutiesAndTaxesCalculatorClassificationDto)
  Classification: Array<DutiesAndTaxesCalculatorClassificationDto>;

  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorCountryDto,
    description: "Details related to the export country. Complete to declare your Country of Export."
  })
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorCountryDto)
  @IsNotEmpty()
  ExportCountry: DutiesAndTaxesCalculatorCountryDto;

  @ApiPropertyOptional({
    type: () => DutiesAndTaxesCalculatorInvoiceLineDto,
    description: `Details relating to the line item of an invoice Complete this segment if SIMA is subject and the invoice price is required by the Measure in Force.`
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorInvoiceLineDto)
  InvoiceLine?: DutiesAndTaxesCalculatorInvoiceLineDto;

  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorCountryDto,
    description:
      "Details about the origin of the goods. Complete this segment to declare the country of origin."
  })
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorCountryDto)
  @IsNotEmpty()
  Origin: DutiesAndTaxesCalculatorCountryDto;

  @ApiProperty({
    type: () => [DutiesAndTaxesCalculatorDutyTaxFeeDto],
    minItems: 3,
    maxItems: 9,
    description: `
    Details relating to Duty Tax Fees Complete this segment to declare Duty/Tax specific information. This segment must be completed:

    - To declare a Tariff Treatment code in the DutyRegime Field with type code 'CUD' (Mandatory)
    - To declare a GST code in the DutyRegime Field with type code 'GST' (Mandatory)
    - To declare the value for currency conversion amount and currency as the DutyTaxFeeAssessmentBasis with the type code 'TOT' (Mandatory). This segment may be completed:
    - To self declare any net amount. To self Declare SIMA, Surtax, Safegaurd, Excise Tax, or Excise Duty the applicable self-declare indicator must also be transmitted. If the payment amount is ‘0.00’ or not applicable, the specific duty tax fee instance should be left blank.
    `
  })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(3)
  @ArrayMaxSize(9)
  @ValidateNested({ each: true })
  @Type(() => DutiesAndTaxesCalculatorDutyTaxFeeDto)
  DutyTaxFee: Array<DutiesAndTaxesCalculatorDutyTaxFeeDto>;
}

export class DutiesAndTaxesCalculatorGovernmentAgencyGoodsItemDto {
  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorCommodityDto,
    description: "Details regarding a commodity line. Complete this segment with the commodity line details."
  })
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorCommodityDto)
  @IsNotEmpty()
  Commodity: DutiesAndTaxesCalculatorCommodityDto;
}

export class DutiesAndTaxesCalculatorGoodsShipmentDto {
  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorGovernmentAgencyGoodsItemDto
  })
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorGovernmentAgencyGoodsItemDto)
  @IsNotEmpty()
  GovernmentAgencyGoodsItem: DutiesAndTaxesCalculatorGovernmentAgencyGoodsItemDto;
}

export class DutiesAndTaxesCalculatorDeclarationDto {
  @ApiProperty({
    enum: CarmLanguageCode,
    description:
      "Identifies the language of the transmission. Complete this segment with a language code. English and French are accepted."
  })
  @IsEnum(CarmLanguageCode)
  @IsNotEmpty()
  LanguageCode: CarmLanguageCode;

  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorStatusDto,
    description: "Details to identify the Release Date. Complete this segment to declare the release date."
  })
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorStatusDto)
  @IsNotEmpty()
  Status: DutiesAndTaxesCalculatorStatusDto;

  @ApiProperty({ type: () => DutiesAndTaxesCalculatorGoodsShipmentDto })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorGoodsShipmentDto)
  GoodsShipment: DutiesAndTaxesCalculatorGoodsShipmentDto;
}

export class DutiesAndTaxesCalculatorDto {
  @ApiProperty({ type: () => DutiesAndTaxesCalculatorDeclarationDto })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorDeclarationDto)
  Declaration: DutiesAndTaxesCalculatorDeclarationDto;
}

// Duties and Taxes Calculator Response
export class DutiesAndTaxesCalculatorResponseCountQuantityDto {
  @ApiProperty({
    type: "number",
    format: "float",
    minLength: 1,
    maxLength: 13,
    description: "Quantity of the goods being declared in the commodity line"
  })
  @IsNumber()
  @IsNotEmpty()
  TextValue: number;

  @ApiProperty({
    type: "string",
    minLength: 3,
    maxLength: 3,
    description:
      "Unit of measure used to declare the quantity of goods being imported. Available unit of measure codes can be found in the Administrative Guidelines of the Customs Tariff."
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(3)
  unitCode: string;
}

export class DutiesAndTaxesCalculatorResponseAdditionalInformationDto {
  @ApiProperty({
    type: "string",
    minLength: 1,
    maxLength: 17,
    description: `
    Code that indicates additional information regarding the commodity related to the additional statement type. In addition to any additional information declared on the inbound, the CARM system may return:

    - An instance with type code 'APP' with the Statement Code 'X - True' representing that SIMA may be applicable
    - An instance with type code 'BPP' with the Statement Code 'X - True' representing that a Surtax Measure may be applicable
    - An instance with type code 'CAP' with the Statement Code 'X - True' representing that a Safeguard Measure may be applicable

    If SIMA is Subject or Undertaking, the CARM system will return:

    - An instance with type code 'RLT' with the Statement Code representing the Measure Type Code
    - An instance with type code 'SIM' with the Statement Code representing the SIMA Code

    Available codes can be found in Measures in force.
  `
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(17)
  StatementCode: string;

  @ApiProperty({
    type: "enum",
    enum: ["APP", "BPP", "CAP", "RLT", "SIM"],
    description: "Coded value which identifies the type of the segment instance"
  })
  @IsIn(["APP", "BPP", "CAP", "RLT", "SIM"])
  @IsNotEmpty()
  StatementTypeCode: "APP" | "BPP" | "CAP" | "RLT" | "SIM";
}

export class DutiesAndTaxesCalculatorResponseAmountDto {
  @ApiProperty({ type: "number", format: "float", minLength: 1, maxLength: 13 })
  @IsNumber()
  @IsNotEmpty()
  TextValue: number;

  @ApiProperty({ type: "string", minLength: 3, maxLength: 3 })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(3)
  currencyID: string;
}

export class DutiesAndTaxesCalculatorResponseQuantityDto {
  @ApiProperty({ type: "number", format: "float", minLength: 1, maxLength: 13 })
  @IsNumber()
  @IsNotEmpty()
  TextValue: number;

  @ApiProperty({ type: "string", minLength: 3, maxLength: 3 })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(3)
  unitCode: string;
}

export class DutiesAndTaxesCalculatorResponseDutyTaxFeeAdditionalInformationDto {
  @ApiProperty({
    type: "string",
    minLength: 1,
    maxLength: 155,
    description: "Rate in plain text that was used in the duty and taxes calculation"
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(155)
  StatementDescription: string;

  @ApiProperty({
    type: "enum",
    enum: ["AAF"],
    description: "Coded value indicating the type of information in the particular segment"
  })
  @IsIn(["AAF"])
  @IsNotEmpty()
  StatementTypeCode: "AAF";
}

export class DutiesAndTaxesCalculatorResponseDutyTaxFeeAssessmentBasisDto {
  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorResponseAmountDto,
    description: "Value of the goods on which a duty or tax or fee will be assessed."
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponseAmountDto)
  AdValoremTaxBaseAmount: DutiesAndTaxesCalculatorResponseAmountDto;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minLength: 1,
    maxLength: 12,
    description: "Rate of exchange used in the currency conversion calculation"
  })
  @IsOptional()
  @IsNumber()
  RateNumeric?: number;
}

export class DutiesAndTaxesCalculatorResponsePaymentDto {
  @ApiPropertyOptional({
    type: () => DutiesAndTaxesCalculatorResponseAmountDto,
    description: `Total Duty tax fee amount calculated excluding any relief amounts The system will return the amount of duties and taxes calculated per the duty tax fee type (excluding relief amounts). If the calculated amount is ‘0.00’ or not applicable, the field will not be returned.`
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponseAmountDto)
  TaxAssessedAmount?: DutiesAndTaxesCalculatorResponseAmountDto;

  @ApiPropertyOptional({
    type: () => DutiesAndTaxesCalculatorResponseAmountDto,
    description: `Total net amount of duties and taxes for the commodity line The system will return the net amount of duties and taxes per duty tax type. If the payment amount is equal to ‘0.00’ or not applicable, the field will not be returned.`
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponseAmountDto)
  PaymentAmount?: DutiesAndTaxesCalculatorResponseAmountDto;
}

export class DutiesAndTaxesCalculatorResponseSpecificTaxRateDto {
  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorResponseQuantityDto,
    description:
      "Specific duty rate as a specific amount per unit, to determine the rate of duty, such as cents per kilogram"
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponseQuantityDto)
  SpecificTaxBaseQuantity: DutiesAndTaxesCalculatorResponseQuantityDto;

  @ApiProperty({
    type: "string",
    minLength: 3,
    maxLength: 3,
    description:
      "Coded value identifying the currency of the specific rate. Available currency codes can be found in List 2 of D17-1-10 Coding of Customs Accounting Documents."
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(3)
  CurrencyTypeCode: string;
}

export class DutiesAndTaxesCalculatorResponseRateDto {
  @ApiPropertyOptional({
    type: "enum",
    enum: ["M", "N", "X"],
    description: "Coded value identifying the rate type"
  })
  @IsOptional()
  @IsIn(["M", "N", "X"])
  TariffClassSpecificationCode?: "M" | "N" | "X";

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minLength: 1,
    maxLength: 15,
    description: "Ad valorem duty rate as a fixed percentage of value to determine rate of duty"
  })
  @IsOptional()
  @IsNumber()
  AdvaloremTaxBaseRateNumeric?: number;

  @ApiPropertyOptional({
    type: () => DutiesAndTaxesCalculatorResponseSpecificTaxRateDto,
    description: "Details related to a specific tax rate"
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponseSpecificTaxRateDto)
  SpecificTaxRate?: DutiesAndTaxesCalculatorResponseSpecificTaxRateDto;
}

export class DutiesAndTaxesCalculatorResponseDutyTaxFeeDto {
  @ApiPropertyOptional({
    type: () => DutiesAndTaxesCalculatorResponseAmountDto,
    description: "Total duties and taxes relieved amount"
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponseAmountDto)
  DeductAmount?: DutiesAndTaxesCalculatorResponseAmountDto;

  @ApiPropertyOptional({
    type: () => DutiesAndTaxesCalculatorResponseQuantityDto,
    description:
      "Quantity of the goods to be declared in the dimension of the unit of measure as described in policy is different than the unit of measure assigned to the commodity in the Customs Tariff."
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponseQuantityDto)
  SpecificTaxBaseQuantity?: DutiesAndTaxesCalculatorResponseQuantityDto;

  @ApiProperty({
    type: "enum",
    enum: ["ADD", "CVD", "EXC", "FET", "SUR", "OTH", "GST", "CUD", "TOT"],
    description: "Type of duty Tax being applied to the commodity"
  })
  @IsIn(["ADD", "CVD", "EXC", "FET", "SUR", "OTH", "GST", "CUD", "TOT"])
  @IsNotEmpty()
  TypeCode: "ADD" | "CVD" | "EXC" | "FET" | "SUR" | "OTH" | "GST" | "CUD" | "TOT";

  @ApiPropertyOptional({
    type: () => DutiesAndTaxesCalculatorResponseDutyTaxFeeAdditionalInformationDto,
    description: `Additional Information relating to the commodity duty and taxes. If the rate used in the calculation is ‘0.00000’ or not applicable, the field will not be returned.`
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponseDutyTaxFeeAdditionalInformationDto)
  AdditionalInformation?: DutiesAndTaxesCalculatorResponseDutyTaxFeeAdditionalInformationDto;

  @ApiPropertyOptional({
    type: () => [DutiesAndTaxesCalculatorResponseDutyTaxFeeAssessmentBasisDto],
    description: "Base amount used for duties and taxes calculation"
  })
  @IsOptional()
  @IsArray()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponseDutyTaxFeeAssessmentBasisDto)
  DutyTaxFeeAssessmentBasis?: DutiesAndTaxesCalculatorResponseDutyTaxFeeAssessmentBasisDto;

  @ApiPropertyOptional({
    type: () => DutiesAndTaxesCalculatorResponsePaymentDto,
    description: "Information about payment details"
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponsePaymentDto)
  Payment?: DutiesAndTaxesCalculatorResponsePaymentDto;

  @ApiPropertyOptional({
    type: () => [DutiesAndTaxesCalculatorResponseRateDto],
    minItems: 0,
    maxItems: 3,
    description: `Details related to the tax rate. If the rate is equal to ‘0.00000’ or not applicable, the rate instance will not be returned.`
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(0)
  @ArrayMaxSize(3)
  @ValidateNested({ each: true })
  @Type(() => DutiesAndTaxesCalculatorResponseRateDto)
  Rate?: Array<DutiesAndTaxesCalculatorResponseRateDto>;
}

export class DutiesAndTaxesCalculatorResponseCommodityDto {
  @ApiProperty({ type: () => DutiesAndTaxesCalculatorResponseCountQuantityDto })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponseCountQuantityDto)
  CountQuantity: DutiesAndTaxesCalculatorResponseCountQuantityDto;

  @ApiPropertyOptional({
    type: () => [DutiesAndTaxesCalculatorResponseAdditionalInformationDto],
    minItems: 0,
    maxItems: 5,
    description: `
    Additional information relating to the commodity line The CARM system may return:

    - An instance representing that SIMA may be applicable
    - An instance representing that a Surtax Measure may be applicable
    - An instance representing that a Safeguard Measure may be applicable
    
    If SIMA is Subject or Undertaking, the CARM system will return:

    - An instance representing the Measure Type Code
    - An instance representing the SIMA Code`
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(0)
  @ArrayMaxSize(5)
  @ValidateNested({ each: true })
  @Type(() => DutiesAndTaxesCalculatorResponseAdditionalInformationDto)
  AdditionalInformation?: Array<DutiesAndTaxesCalculatorResponseAdditionalInformationDto>;

  @ApiProperty({
    type: () => [DutiesAndTaxesCalculatorResponseDutyTaxFeeDto],
    minItems: 1,
    maxItems: 10,
    description: "Details relating to Duty Tax Fees"
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(10)
  @ValidateNested({ each: true })
  @Type(() => DutiesAndTaxesCalculatorResponseDutyTaxFeeDto)
  DutyTaxFee: Array<DutiesAndTaxesCalculatorResponseDutyTaxFeeDto>;
}

export class DutiesAndTaxesCalculatorResponseGovernmentAgencyGoodsItemDto {
  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorResponseCommodityDto,
    description: "Details regarding a commodity line"
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponseCommodityDto)
  Commodity: DutiesAndTaxesCalculatorResponseCommodityDto;
}

export class DutiesAndTaxesCalculatorResponseGoodsShipmentDto {
  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorResponseGovernmentAgencyGoodsItemDto,
    description: "Details regarding a goods item"
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponseGovernmentAgencyGoodsItemDto)
  GovernmentAgencyGoodsItem: DutiesAndTaxesCalculatorResponseGovernmentAgencyGoodsItemDto;
}

export class DutiesAndTaxesCalculatorResponseDeclarationDto {
  @ApiProperty({
    enum: CarmLanguageCode,
    description: "Identifies if the transmission is in English or French."
  })
  @IsEnum(CarmLanguageCode)
  @IsNotEmpty()
  LanguageCode: CarmLanguageCode;

  @ApiProperty({
    type: () => DutiesAndTaxesCalculatorResponseGoodsShipmentDto,
    description: "Details related to the invoice and vendor of the goods"
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponseGoodsShipmentDto)
  GoodsShipment: DutiesAndTaxesCalculatorResponseGoodsShipmentDto;
}

export class DutiesAndTaxesCalculatorResponseErrorPointerDto {
  @ApiProperty({
    type: "string",
    minLength: 1,
    maxLength: 512,
    description: "Location of the error in the document using XPath syntax"
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(512)
  Location: string;
}

export class DutiesAndTaxesCalculatorResponseErrorDto {
  @ApiProperty({
    type: "string",
    minLength: 1,
    maxLength: 255,
    description: "Plain Text description detailing the validation error"
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(255)
  Description: string;

  @ApiProperty({
    type: "string",
    minLength: 1,
    maxLength: 8,
    description: "Code specifying a data validation error"
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(1)
  @MaxLength(8)
  ValidationCode: string;

  @ApiProperty({
    type: () => [DutiesAndTaxesCalculatorResponseErrorPointerDto],
    minItems: 1,
    description: "Details related to the location of the error"
  })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => DutiesAndTaxesCalculatorResponseErrorPointerDto)
  Pointer: Array<DutiesAndTaxesCalculatorResponseErrorPointerDto>;
}

export class DutiesAndTaxesCalculatorResponseObjectDto {
  @ApiProperty({ type: () => DutiesAndTaxesCalculatorResponseDeclarationDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponseDeclarationDto)
  Declaration?: DutiesAndTaxesCalculatorResponseDeclarationDto;

  @ApiProperty({
    type: () => [DutiesAndTaxesCalculatorResponseErrorDto],
    minItems: 0,
    description: "Details related to errors in the message"
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(0)
  @ValidateNested({ each: true })
  @Type(() => DutiesAndTaxesCalculatorResponseErrorDto)
  Error?: Array<DutiesAndTaxesCalculatorResponseErrorDto>;
}

export class DutiesAndTaxesCalculatorResponseDto {
  @ApiProperty({ type: () => DutiesAndTaxesCalculatorResponseObjectDto })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => DutiesAndTaxesCalculatorResponseObjectDto)
  Response: DutiesAndTaxesCalculatorResponseObjectDto;
}

export class CarmRequestParamsDto {
  @ApiProperty({ type: "string" })
  @IsString()
  @IsNotEmpty()
  @Length(9, 9, {
    message: "Business number must be exactly 9 characters"
  })
  businessNo: string;
}
export class CarmRequestResponseDto {
  @ApiProperty({ type: "string" })
  @IsString()
  @IsNotEmpty()
  businessNo: string;

  @ApiProperty({ enum: CarmStatus })
  @IsEnum(CarmStatus)
  @IsNotEmpty()
  result: CarmStatus;
}
