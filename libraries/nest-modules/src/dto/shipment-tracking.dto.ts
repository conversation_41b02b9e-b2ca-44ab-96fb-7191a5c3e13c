import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional, IsString } from "class-validator";

export class BaseContainerTrackingDto {
  @ApiProperty({ type: "string" })
  @IsNotEmpty()
  @IsString()
  ids: string;
}

export class CarrierShipmentTrackingDto extends BaseContainerTrackingDto {}

export class PortShipmentTrackingDto extends BaseContainerTrackingDto {}

export class RailShipmentTrackingDto extends BaseContainerTrackingDto {}

export class ShipmentPickupReturnTrackingDto extends BaseContainerTrackingDto {}

export class BaseFindShipmentResponseDto {
  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  containerNumber: string;
}

export class FindShipmentLineResponseDto extends BaseFindShipmentResponseDto {
  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  shippingLine: string;
}

export class FindShipmentPortResponseDto extends BaseFindShipmentResponseDto {
  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  port: string;
}

export class FindShipmentRailwayResponseDto extends BaseFindShipmentResponseDto {
  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  railway: string;
}

export class FindShipmentLinesResponseDto {
  @ApiProperty({ type: () => [FindShipmentLineResponseDto] })
  lines: FindShipmentLineResponseDto[];
}

export class FindShipmentPortsResponseDto {
  @ApiProperty({ type: () => [FindShipmentPortResponseDto] })
  ports: FindShipmentPortResponseDto[];
}

export class FindShipmentRailwaysResponseDto {
  @ApiProperty({ type: () => [FindShipmentRailwayResponseDto] })
  railways: FindShipmentRailwayResponseDto[];
}
