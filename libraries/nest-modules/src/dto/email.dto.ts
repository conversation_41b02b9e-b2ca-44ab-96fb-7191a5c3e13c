import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
  IsBoolean,
  IsDate,
  IsEmail,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
  MinLength
} from "class-validator";
import { Email, EmailWithAttachments } from "../entities";
import { EmailColumn, EmailOrigin, EmailStatus } from "../types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class EmailAddressDto {
  @ApiProperty({ type: "string", format: "email" })
  @IsNotEmpty()
  @IsEmail()
  address: string;

  @ApiProperty({ type: "string", minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  name: string;
}

export class GetEmailsDto extends GetManyDto {
  @ApiPropertyOptional({ enum: EmailColumn, default: EmailColumn.id })
  @IsOptional()
  @IsEnum(EmailColumn)
  sortBy?: EmailColumn;

  @ApiPropertyOptional({
    type: "boolean",
    default: false,
    description: "If true, each email will include a list of attachments"
  })
  @Transform(({ value }) =>
    typeof value === "string" && ["true", "false"].includes(value.trim().toLowerCase())
      ? value.trim().toLowerCase() === "true"
      : value
  )
  @IsOptional()
  @IsBoolean()
  includeAttachments?: boolean;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  shipmentId?: number;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  inboxEmail?: string;

  @ApiPropertyOptional({ enum: EmailOrigin })
  @IsOptional()
  @IsEnum(EmailOrigin)
  origin?: EmailOrigin;

  @ApiPropertyOptional({ enum: EmailStatus })
  @IsOptional()
  @IsEnum(EmailStatus)
  status?: EmailStatus;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  gmailId?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  threadId?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  historyId?: string;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type(() => Date)
  @IsOptional()
  @IsDate()
  receiveDateFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type(() => Date)
  @IsOptional()
  @IsDate()
  receiveDateTo?: Date;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  from?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  replyTo?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  to?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  cc?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  subject?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  text?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  html?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  content?: string;

  // Don't filter by userIntents
  // @ApiPropertyOptional({ type: "object" })
  // @IsOptional()
  // userIntents?: Array<Record<string, any>>;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  requestSummary?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  processingOutcome?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  error?: string;

  @ApiPropertyOptional({ type: "number", minimum: 1 })
  @IsOptional()
  @Min(1)
  organizationId?: number;

  @ApiPropertyOptional({ type: "number", minimum: 1 })
  @IsOptional()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "number", minimum: 1 })
  @IsOptional()
  @Min(1)
  lastEditedById?: number;
}

export class GetEmailsResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [EmailWithAttachments] })
  data: Array<EmailWithAttachments>;
}

export class GetEmailsByThreadIdResponseDto {
  @ApiProperty({ type: () => [Email] })
  data: Array<Email>;
}
