import { ApiProperty, ApiPropertyOptional, getSchemaPath } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  ArrayUnique,
  IsArray,
  IsDate,
  IsEnum,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
  Min,
  MinLength,
  registerDecorator,
  ValidateIf,
  ValidateNested,
  ValidationArguments,
  ValidationOptions
} from "class-validator";
import { IsExist } from "../database-validator/decorators";
import { IsNestedFieldSame } from "../decorators";
import {
  CanadaAntiDumping,
  CanadaExciseTaxCode,
  CanadaGstExemptCode,
  CanadaOgd,
  CanadaSimaCode,
  CanadaTariff,
  CanadaTreatmentCode,
  CanadaVfdCode,
  CertificateOfOrigin,
  Country,
  OgdFiling,
  SimaFiling,
  State
} from "../entities";
import { CommercialInvoiceLine } from "../entities/commercial-invoice-line.entity";
import {
  MatchingRuleSourceDatabaseTable,
  ProductColumn,
  SafeguardSubjectCode,
  SpecialAuthorityTimeLimitType,
  SurtaxSubjectCode,
  UnitOfMeasure
} from "../types";
import {
  CommercialInvoiceLineColumn,
  DestinationProvince,
  NonCompliantReason
} from "../types/commercial-invoice-line.types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import { CommercialInvoiceLineMeasurementDto } from "./commercial-invoice-line-measurement.dto";
import { CreateProductDto } from "./product.dto";

function TotalLineValue(validationOptions?: ValidationOptions) {
  const ALLOWED_DIFFERENCE = 0.01;

  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: "totalLineValue",
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(_: any, args: ValidationArguments) {
          const { quantity, unitPrice, totalLineValue } = args.object as any;

          if (quantity === 0 || unitPrice === 0 || totalLineValue === 0) {
            return quantity * unitPrice === totalLineValue;
          }

          return Math.abs(totalLineValue / quantity - unitPrice) < ALLOWED_DIFFERENCE;
        }
      }
    });
  };
}

export class CreateCommercialInvoiceLineDto {
  @ApiPropertyOptional({ type: "number", format: "integer" })
  @IsOptional()
  @IsInt()
  sequence?: number;

  @ApiPropertyOptional({ type: "number", format: "integer" })
  @IsOptional()
  @IsInt()
  candataId?: number | null;

  @ApiProperty({ type: "string", minLength: 1 })
  @IsString()
  @MinLength(1)
  @IsNotEmpty()
  goodsDescription: string;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "Product ID of an existing product. Use temporaryProduct if a new temporary product should be created. If both are provided, productId will be used."
  })
  @ValidateIf((obj: CreateCommercialInvoiceLineDto) => [null, undefined].includes(obj.temporaryProduct))
  @IsInt()
  @Min(1)
  productId?: number;

  @ApiPropertyOptional({
    type: () => CreateProductDto,
    description:
      "The new temporary product to be created. Use productId if an existing product should be used. If both are provided, productId will be used."
  })
  @Type(() => CreateProductDto)
  @ValidateIf((obj: CreateCommercialInvoiceLineDto) => [null, undefined].includes(obj.productId))
  @ValidateNested()
  temporaryProduct?: CreateProductDto;

  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  @IsExist(Country)
  originId: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @IsExist(State, "id", (entity: CreateCommercialInvoiceLineDto) => ({
    countryId: entity.originId
  }))
  originStateId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  vfdId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  ttId?: number | null;

  @ApiProperty({ type: "number", format: "float", minimum: 0 })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @TotalLineValue({
    message: "quantity, unitPrice and totalLineValue does not match"
  })
  quantity: number;

  // @ApiProperty({
  //   type: "enum",
  //   enum: UnitOfMeasure,
  //   default: UnitOfMeasure.NUMBER
  // })
  // @IsNotEmpty()
  // @IsEnum(UnitOfMeasure)
  // unitOfMeasure: UnitOfMeasure;

  @ApiProperty({ type: "number", format: "float", minimum: 0 })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @TotalLineValue({
    message: "quantity, unitPrice and totalLineValue does not match"
  })
  unitPrice: number;

  @ApiProperty({ type: "number", format: "float", minimum: 0, default: 0 })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @TotalLineValue({
    message: "quantity, unitPrice and totalLineValue does not match"
  })
  totalLineValue: number;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    default: 0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  totalLineValueCad?: number | null;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  additionalInfo?: string | null;

  @ApiPropertyOptional({ type: "string", maxLength: 17 })
  @IsOptional()
  @IsString()
  @MaxLength(17)
  orderInCouncil?: string | null;

  @ApiPropertyOptional({ enum: SpecialAuthorityTimeLimitType })
  @IsOptional()
  @IsEnum(SpecialAuthorityTimeLimitType)
  timeLimitType?: SpecialAuthorityTimeLimitType | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type(() => Date)
  @IsOptional()
  @IsDate()
  timeLimitStartDate?: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type(() => Date)
  @IsOptional()
  @IsDate()
  timeLimitEndDate?: Date | null;

  @ApiPropertyOptional({ type: "string", maxLength: 17 })
  @IsOptional()
  @IsString()
  @MaxLength(17)
  authorityPermit?: string | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  dutiesReliefLicence?: string | null;

  @ApiPropertyOptional({
    enum: SurtaxSubjectCode,
    default: SurtaxSubjectCode.NON_SUBJECT,
    description: "N-Non-subject, S-Subject"
  })
  @IsOptional()
  @IsEnum(SurtaxSubjectCode)
  surtaxSubjectCode?: SurtaxSubjectCode | null;

  @ApiPropertyOptional({
    type: "string",
    description: "Required if surtax is subjected"
  })
  @ValidateIf((object) => object.surtaxSubjectCode === SurtaxSubjectCode.SUBJECT)
  @IsNotEmpty()
  @IsString()
  surtaxCode?: string | null;

  @ApiPropertyOptional({
    enum: SafeguardSubjectCode,
    default: SafeguardSubjectCode.NON_SUBJECT,
    description: "N-Non-subject, S-Subject"
  })
  @IsOptional()
  @IsEnum(SafeguardSubjectCode)
  safeguardSubjectCode?: SafeguardSubjectCode | null;

  @ApiPropertyOptional({
    type: "string",
    description: "Required if safeguard is subjected"
  })
  @ValidateIf((object) => object.safeguardSubjectCode === SafeguardSubjectCode.SUBJECT)
  @IsNotEmpty()
  @IsString()
  safeguardCode?: string | null;

  @ApiPropertyOptional({ type: "integer", minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  simaQuantity?: number | null;

  @ApiPropertyOptional({ enum: UnitOfMeasure })
  @IsOptional()
  @IsEnum(UnitOfMeasure)
  simaUnitOfMeasure?: UnitOfMeasure | null;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  provincialAlcoholTax?: number | null;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  provincialTobaccoTax?: number | null;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  provincialCannabisExciseDuty?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  alcoholPercentage?: number | null;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    description: "PST/HST/QST"
  })
  @IsOptional()
  @IsNumber()
  pstHst?: number | null;

  @ApiPropertyOptional({ enum: DestinationProvince })
  @IsOptional()
  @IsEnum(DestinationProvince)
  destinationProvince?: DestinationProvince | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  gstExemptCodeId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  tariffCodeId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  exciseTaxCodeId?: number | null;

  @ApiPropertyOptional({ type: () => [CommercialInvoiceLineMeasurementDto] })
  @Type(() => CommercialInvoiceLineMeasurementDto)
  @IsOptional()
  @IsArray()
  @ArrayUnique((measurement) => measurement?.type, { message: "Measurement types must be unique" })
  @ValidateNested({ each: true })
  measurements?: CommercialInvoiceLineMeasurementDto[];
}

export class GetCommercialInvoiceLinesDto extends GetManyDto {
  @ApiPropertyOptional({
    type: "enum",
    enum: (Object.values(CommercialInvoiceLineColumn) as Array<string>).concat(ProductColumn.partNumber),
    default: CommercialInvoiceLineColumn.id
  })
  @IsOptional()
  @IsIn((Object.values(CommercialInvoiceLineColumn) as Array<string>).concat(ProductColumn.partNumber))
  sortBy?: CommercialInvoiceLineColumn | ProductColumn.partNumber;

  @ApiPropertyOptional({ type: "number", format: "integer" })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  candataId?: number;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  goodsDescription?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  productId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  originId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  originStateId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  vfdId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  ttId?: number;

  @ApiPropertyOptional({ minLength: 10, maxLength: 10 })
  @IsOptional()
  @IsString()
  @MinLength(10)
  @MaxLength(10)
  hsCode?: string;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Type((type) => Number)
  @Min(0)
  totalLineValue?: number;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Type((type) => Number)
  @Min(0)
  totalLineValueCad?: number;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Type((type) => Number)
  @Min(0)
  quantity?: number;

  @ApiPropertyOptional({
    type: "enum",
    enum: UnitOfMeasure,
    default: UnitOfMeasure.NUMBER
  })
  @IsOptional()
  @IsEnum(UnitOfMeasure)
  unitOfMeasure?: UnitOfMeasure;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Type((type) => Number)
  @Min(0)
  unitPrice?: number;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  additionalInfo?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type((type) => Number)
  ciId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type((type) => Number)
  organizationId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type((type) => Number)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type((type) => Number)
  lastEditedById?: number;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  orderInCouncil?: string;

  @ApiPropertyOptional({ enum: SpecialAuthorityTimeLimitType })
  @IsOptional()
  @IsEnum(SpecialAuthorityTimeLimitType)
  timeLimitType?: SpecialAuthorityTimeLimitType;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  timeLimitStartDate?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  timeLimitEndDate?: Date;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  authorityPermit?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  dutiesReliefLicence?: string;

  @ApiPropertyOptional({ enum: SurtaxSubjectCode })
  @IsOptional()
  @IsEnum(SurtaxSubjectCode)
  surtaxSubjectCode?: SurtaxSubjectCode;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  surtaxCode?: string;

  @ApiPropertyOptional({ enum: SafeguardSubjectCode })
  @IsOptional()
  @IsEnum(SafeguardSubjectCode)
  safeguardSubjectCode?: SafeguardSubjectCode;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  safeguardCode?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 0 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(0)
  simaQuantity?: number | null;

  @ApiPropertyOptional({ enum: UnitOfMeasure })
  @IsOptional()
  @IsEnum(UnitOfMeasure)
  simaUnitOfMeasure?: UnitOfMeasure | null;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0)
  provincialAlcoholTax?: number;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0)
  provincialTobaccoTax?: number;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0)
  provincialCannabisExciseDuty?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 0 })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(0)
  alcoholPercentage?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  pstHst?: number;

  @ApiPropertyOptional({ enum: DestinationProvince })
  @IsOptional()
  @IsEnum(DestinationProvince)
  destinationProvince?: DestinationProvince;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  gstExemptCodeId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  tariffCodeId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  exciseTaxCodeId?: number;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type(() => Date)
  @IsOptional()
  @IsDate()
  cadCalculationDateFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type(() => Date)
  @IsOptional()
  @IsDate()
  cadCalculationDateTo?: Date;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  valueForDuty?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  valueForTax?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  antiDumping?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  countervailing?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  customsDuties?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  exciseDuties?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  exciseTax?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  gst?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  safeguard?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  surtax?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  totalDutiesAndTaxes?: number;
}

export class GetCommercialInvoiceLinesResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [CommercialInvoiceLine] })
  commercialInvoiceLines: Array<CommercialInvoiceLine>;
}

export class EditCommercialInvoiceLineDto {
  @ApiPropertyOptional({ type: "number", format: "integer" })
  @IsOptional()
  @IsInt()
  candataId?: number | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  goodsDescription?: string;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "Product ID of an existing product. Use temporaryProduct if a new temporary product should be created. If both are provided, productId will be used."
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  productId?: number;

  @ApiPropertyOptional({
    type: () => CreateProductDto,
    description:
      "The new temporary product to be created. Use productId if an existing product should be used. If both are provided, productId will be used."
  })
  @Type(() => CreateProductDto)
  @IsOptional()
  @ValidateNested()
  @IsNestedFieldSame({
    nestedFieldName: "hsCode",
    parentFieldName: "hsCode",
    allowNull: false,
    allowUndefined: true
  })
  temporaryProduct?: CreateProductDto;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @IsExist(Country)
  originId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @IsExist(State, "id", (entity: EditCommercialInvoiceLineDto) => ({
    countryId: entity.originId
  }))
  originStateId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  vfdId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  ttId?: number | null;

  @ApiPropertyOptional({
    minLength: 10,
    maxLength: 10,
    pattern: "^[0-9]{10}$",
    description:
      "This field can only be used if the product is temporary, and setting this field will update both the line's and the product's HS code."
  })
  @IsOptional()
  @IsString()
  @MinLength(10)
  @MaxLength(10)
  @Matches(/^[0-9]{10}$/, { message: "hsCode must be a 10-digit number" })
  hsCode?: string;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @TotalLineValue({
    message: "quantity, unitPrice and totalLineValue does not match"
  })
  quantity?: number;

  // @ApiPropertyOptional({
  //   type: "enum",
  //   enum: UnitOfMeasure,
  //   default: UnitOfMeasure.NUMBER
  // })
  // @IsOptional()
  // @IsEnum(UnitOfMeasure)
  // unitOfMeasure?: UnitOfMeasure;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @TotalLineValue({
    message: "quantity, unitPrice and totalLineValue does not match"
  })
  unitPrice?: number;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @TotalLineValue({
    message: "quantity, unitPrice and totalLineValue does not match"
  })
  totalLineValue?: number;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  totalLineValueCad?: number | null;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  additionalInfo?: string | null;

  @ApiPropertyOptional({ type: "string", maxLength: 17 })
  @IsOptional()
  @IsString()
  @MaxLength(17)
  orderInCouncil?: string | null;

  @ApiPropertyOptional({ enum: SpecialAuthorityTimeLimitType })
  @IsOptional()
  @IsEnum(SpecialAuthorityTimeLimitType)
  timeLimitType?: SpecialAuthorityTimeLimitType | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  timeLimitStartDate?: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  timeLimitEndDate?: Date | null;

  @ApiPropertyOptional({ type: "string", maxLength: 17 })
  @IsOptional()
  @IsString()
  @MaxLength(17)
  authorityPermit?: string | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  dutiesReliefLicence?: string | null;

  @ApiPropertyOptional({
    enum: SurtaxSubjectCode,
    default: SurtaxSubjectCode.NON_SUBJECT,
    description: "N-Non-subject, S-Subject"
  })
  @IsOptional()
  @IsEnum(SurtaxSubjectCode)
  surtaxSubjectCode?: SurtaxSubjectCode | null;

  @ApiPropertyOptional({
    type: "string",
    description: "Required if surtax is subjected"
  })
  @ValidateIf((object) => object.surtaxSubjectCode === SurtaxSubjectCode.SUBJECT)
  @IsNotEmpty()
  @IsString()
  surtaxCode?: string | null;

  @ApiPropertyOptional({
    enum: SafeguardSubjectCode,
    default: SafeguardSubjectCode.NON_SUBJECT,
    description: "N-Non-subject, S-Subject"
  })
  @IsOptional()
  @IsEnum(SafeguardSubjectCode)
  safeguardSubjectCode?: SafeguardSubjectCode | null;

  @ApiPropertyOptional({
    type: "string",
    description: "Required if safeguard is subjected"
  })
  @ValidateIf((object) => object.safeguardSubjectCode === SafeguardSubjectCode.SUBJECT)
  @IsNotEmpty()
  @IsString()
  safeguardCode?: string | null;

  @ApiPropertyOptional({ type: "integer", minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  simaQuantity?: number | null;

  @ApiPropertyOptional({ type: "enum", enum: UnitOfMeasure })
  @IsOptional()
  @IsEnum(UnitOfMeasure)
  simaUnitOfMeasure?: UnitOfMeasure | null;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  provincialAlcoholTax?: number | null;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  provincialTobaccoTax?: number | null;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  provincialCannabisExciseDuty?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  alcoholPercentage?: number | null;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    description: "PST/HST/QST"
  })
  @IsOptional()
  @IsNumber()
  pstHst?: number | null;

  @ApiPropertyOptional({ enum: DestinationProvince })
  @IsOptional()
  @IsEnum(DestinationProvince)
  destinationProvince?: DestinationProvince | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  gstExemptCodeId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  tariffCodeId?: number | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  exciseTaxCodeId?: number | null;

  @ApiPropertyOptional({ type: () => [CommercialInvoiceLineMeasurementDto] })
  @Type(() => CommercialInvoiceLineMeasurementDto)
  @IsOptional()
  @IsArray()
  @ArrayUnique((measurement) => measurement?.type, { message: "Measurement types must be unique" })
  @ValidateNested({ each: true })
  measurements?: CommercialInvoiceLineMeasurementDto[];
}

export class EditCommercialInvoiceLineWithIdDto extends EditCommercialInvoiceLineDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  id: number;
}

export class BatchUpdateCommercialInvoiceLinesDto {
  @ApiProperty({ type: [CreateCommercialInvoiceLineDto] })
  @Type(() => CreateCommercialInvoiceLineDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  create?: Array<CreateCommercialInvoiceLineDto>;

  @ApiProperty({ type: [EditCommercialInvoiceLineWithIdDto] })
  @Type(() => EditCommercialInvoiceLineWithIdDto)
  @IsOptional()
  @IsArray()
  @ArrayUnique((o) => o.id)
  @ValidateNested({ each: true })
  edit?: Array<EditCommercialInvoiceLineWithIdDto>;

  @ApiProperty({ type: "integer", minimum: 1, isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  delete?: Array<number>;
}

export class BatchUpdateCommercialInvoiceLinesResponseDto {
  @ApiProperty({ type: [CommercialInvoiceLine] })
  commercialInvoiceLines: Array<CommercialInvoiceLine>;
}

export class NonCompliantRecordDto {
  @ApiProperty({ enum: MatchingRuleSourceDatabaseTable })
  sourceTable: MatchingRuleSourceDatabaseTable;

  @ApiProperty({ type: "integer", minimum: 1 })
  sourceId: number;

  @ApiProperty({
    oneOf: [
      { $ref: getSchemaPath(CanadaAntiDumping) },
      { $ref: getSchemaPath(CanadaOgd) },
      { $ref: getSchemaPath(CanadaSimaCode) },
      { $ref: getSchemaPath(CanadaTariff) },
      { $ref: getSchemaPath(CanadaTreatmentCode) },
      { $ref: getSchemaPath(CanadaVfdCode) },
      { $ref: getSchemaPath(CanadaGstExemptCode) },
      { $ref: getSchemaPath(CanadaExciseTaxCode) },
      { $ref: getSchemaPath(OgdFiling) },
      { $ref: getSchemaPath(SimaFiling) },
      { $ref: getSchemaPath(CertificateOfOrigin) }
    ]
  })
  sourceRecord:
    | CanadaAntiDumping
    | CanadaOgd
    | CanadaSimaCode
    | CanadaTariff
    | CanadaTreatmentCode
    | CanadaVfdCode
    | CanadaGstExemptCode
    | OgdFiling
    | SimaFiling
    | CertificateOfOrigin
    | CanadaExciseTaxCode;

  @ApiProperty({ enum: NonCompliantReason })
  reason: NonCompliantReason;
}

export class ValidateCommercialInvoiceLineComplianceResponseDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  lineId: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description: "Product ID of the commercial invoice line. `null` if no product is set"
  })
  productId?: number | null;

  @ApiProperty({
    type: "boolean",
    description:
      "True if HS code is not 10-digit string or no related Canada Tariff record with the same HS code is found"
  })
  isHsCodeInvalid: boolean;

  @ApiProperty({
    type: "boolean",
    description: "True if Canada Tariff record is found but the quantity is is zero"
  })
  isQuantityInvalid: boolean;

  @ApiProperty({
    type: "boolean",
    description: "True if the country of origin is US and the state of origin is not set"
  })
  isUsStateOfOriginNotSet: boolean;

  @ApiProperty({
    type: () => [NonCompliantRecordDto],
    description: "List of non-compliant records from the compliance rules"
  })
  nonCompliantRecords: Array<NonCompliantRecordDto>;
}
