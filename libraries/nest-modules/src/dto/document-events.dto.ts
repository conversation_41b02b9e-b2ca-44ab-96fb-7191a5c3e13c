/**
 * Cross-module document event DTOs
 *
 * These events are used for communication between modules and should be imported
 * from nest-modules to avoid tight coupling between individual app modules.
 */

/**
 * Event emitted when a file batch is created and ready for processing
 * Used for cross-module communication between Core-Agent and Document modules
 */
export class FileBatchCreatedEvent {
  constructor(
    public readonly fileBatchId: string,
    public readonly organizationId: number,
    public readonly fileIds: number[],
    public readonly shipmentId?: number
  ) {}
}
