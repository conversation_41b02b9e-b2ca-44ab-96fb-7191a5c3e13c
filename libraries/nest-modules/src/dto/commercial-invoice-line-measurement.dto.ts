import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty, IsN<PERSON>ber, <PERSON>, <PERSON> } from "class-validator";
import { IsValidUnitOfMeasure } from "../decorators";
import { UnitOfMeasure, UnitOfMeasureType } from "../types";

export class CommercialInvoiceLineMeasurementDto {
  @ApiProperty({ type: "enum", enum: UnitOfMeasureType })
  @IsNotEmpty()
  @IsEnum(UnitOfMeasureType)
  type: UnitOfMeasureType;

  @ApiProperty({ type: "enum", enum: UnitOfMeasure })
  @IsNotEmpty()
  @IsEnum(UnitOfMeasure)
  @IsValidUnitOfMeasure({
    message: "$property doesn't match the type"
  })
  unitOfMeasure: UnitOfMeasure;

  @ApiProperty({ type: "number", format: "float", minimum: 0, maximum: 99999999 })
  @IsNotEmpty()
  @Min(0)
  @Max(99999999)
  @IsNumber()
  // TODO: add validation for the value based on the type
  // For example, if the type is per_unit, the value must be an integer
  value: number;
}
