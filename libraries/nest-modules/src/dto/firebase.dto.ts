import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsOptional, IsString } from "class-validator";
import { WhereQuery } from "../types";
import { GetManyDto } from "./base.dto";

export class WhereQueryDto extends GetManyDto {
  @ApiPropertyOptional({
    type: "array",
    description: "Array of where query conditions in format [[field, operator, value], ...]",
    example: [
      ["fieldName", "==", "value"],
      ["status", "in", ["PENDING", "ACTIVE"]]
    ]
  })
  @IsOptional()
  @IsArray()
  whereQueries?: WhereQuery[];

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  sortBy?: string;
}
