import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsDate, IsEnum, IsNotEmpty, IsOptional, IsString } from "class-validator";
import { ContainerType } from "../types/shipment.types";
import { Type } from "class-transformer";

export class ShipmentContainerDto {
  @ApiProperty({ type: "string" })
  @IsNotEmpty()
  @IsString()
  containerNumber: string;

  @ApiPropertyOptional({ enum: ContainerType })
  @IsOptional()
  @IsEnum(ContainerType)
  containerType?: ContainerType | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type(() => Date)
  @IsOptional()
  @IsDate()
  etaDestination?: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type(() => Date)
  @IsOptional()
  @IsDate()
  pickupLfd?: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type(() => Date)
  @IsOptional()
  @IsDate()
  pickupDate?: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type(() => Date)
  @IsOptional()
  @IsDate()
  returnLfd?: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type(() => Date)
  @IsOptional()
  @IsDate()
  returnDate?: Date | null;
}
