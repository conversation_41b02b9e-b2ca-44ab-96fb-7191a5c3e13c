import { ApiProperty, ApiPropertyOptional, getSchemaPath } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDateString,
  IsDecimal,
  IsEmail,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
  Min,
  MinLength,
  ValidateNested
} from "class-validator";
import { IsTrue } from "../decorators";
import {
  CandataDeclarationStatus,
  CandataDeclarationType,
  CandataModeOfTransport,
  CandataProvince,
  SafeguardSubjectCode,
  SimaCode,
  SimaIncoterms,
  SimaSubjectCode,
  SpecialAuthorityTimeLimitType,
  SurtaxSubjectCode,
  TariffTreatmentCode,
  VfdCode
} from "../types";

export class CandataPartyDto {
  @ApiPropertyOptional({ type: "string", maxLength: 35 })
  @IsString()
  @MaxLength(35)
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({ type: "string", maxLength: 35 })
  @IsString()
  @MaxLength(35)
  @IsOptional()
  name2?: string;

  @ApiPropertyOptional({ type: "string", maxLength: 3 })
  @IsString()
  @MaxLength(3)
  @IsOptional()
  type?: string;

  @ApiPropertyOptional({ type: "string", pattern: "U?[A-Z][A-Z]" })
  @IsString()
  @Matches(/^U?[A-Z][A-Z]$/)
  @IsOptional()
  country?: string;

  @ApiPropertyOptional({ type: "string", maxLength: 35 })
  @IsString()
  @MaxLength(35)
  @IsOptional()
  address1?: string;

  @ApiPropertyOptional({ type: "string", maxLength: 35 })
  @IsString()
  @MaxLength(35)
  @IsOptional()
  address2?: string;

  @ApiPropertyOptional({ type: "string", maxLength: 100 })
  @IsString()
  @MaxLength(100)
  @IsOptional()
  city?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  postalCode?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  businessNumber?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  @IsEmail()
  emailAddress?: string;

  @ApiPropertyOptional({ enum: CandataProvince })
  @IsEnum(CandataProvince)
  @IsOptional()
  province?: CandataProvince;

  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  contact?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  state?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  fax?: string;
}

export class InvoiceSummaryDto {
  @ApiPropertyOptional({ type: () => [InvoiceDetailsDto] })
  @ValidateNested({ each: true })
  @Type(() => InvoiceDetailsDto)
  @IsArray()
  details?: InvoiceDetailsDto[];

  @ApiPropertyOptional({ type: "number" })
  @IsNumber()
  @IsOptional()
  transportationCharges?: number;

  @ApiPropertyOptional({ type: "number" })
  @IsNumber()
  @IsOptional()
  miscellaneousCost?: number;

  @ApiPropertyOptional({ type: "number" })
  @IsNumber()
  @IsOptional()
  exportPacking?: number;

  @ApiPropertyOptional({ type: "number" })
  @IsNumber()
  @IsOptional()
  cashDiscount?: number;

  @ApiPropertyOptional({ type: "number" })
  @IsNumber()
  @IsOptional()
  tradeDiscount?: number;

  @ApiPropertyOptional({ type: "number" })
  @IsNumber()
  @IsOptional()
  sampleDiscount?: number;

  @ApiPropertyOptional({ type: "number" })
  @IsNumber()
  @IsOptional()
  volumeDiscount?: number;

  @ApiPropertyOptional({ type: "number" })
  @IsNumber()
  @IsOptional()
  invoiceAmount?: number;

  @ApiPropertyOptional({ type: "boolean" })
  @IsBoolean()
  @IsOptional()
  isDutyIncluded?: boolean;

  @ApiPropertyOptional({ type: "string", maxLength: 30 })
  @IsString()
  @MaxLength(30)
  @IsOptional()
  vendorName?: string;

  @ApiPropertyOptional({ type: "number" })
  @IsNumber()
  @IsOptional()
  weight?: number;

  @ApiPropertyOptional({ type: "string", maxLength: 10 })
  @IsString()
  @MaxLength(10)
  @IsOptional()
  invoiceNumber?: string;

  @ApiPropertyOptional({ type: "string", maxLength: 3 })
  @IsString()
  @MaxLength(3)
  @IsOptional()
  currency?: string;

  @ApiPropertyOptional({ type: "string", maxLength: 3 })
  @IsString()
  @MaxLength(3)
  @IsOptional()
  placeOfExport?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1, maxLength: 7 })
  @IsString()
  @MinLength(1)
  @MaxLength(7)
  @IsOptional()
  vendorNumber?: string;

  @ApiPropertyOptional({ type: "string", maxLength: 3 })
  @IsString()
  @MaxLength(3)
  @IsOptional()
  vendorStateCode?: string;

  @ApiPropertyOptional({ type: "integer" })
  @IsInt()
  @IsOptional()
  sequence?: number;
}

export class PgaDetailDto {
  @ApiProperty({ type: "string" })
  @IsString()
  @IsNotEmpty()
  detailType: string;

  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  descriptionCode?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  description?: string;
}

export class LpcoDto {
  @ApiPropertyOptional({ type: "number", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  sourceId?: number;

  @ApiPropertyOptional({ type: "string", pattern: "U?[A-Z][A-Z]" })
  @IsOptional()
  @Matches(/^U?[A-Z][A-Z]$/)
  countryOfIssue?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  alternativeQuantityUnit?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  documentType?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  documentDesc?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  secondaryReference?: string;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  referenceNumber?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  imageURN?: string;

  @ApiPropertyOptional({ type: "number", format: "integer" })
  @IsOptional()
  @IsNumber()
  alternativeQuantity?: number;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ type: () => CandataPartyDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => CandataPartyDto)
  parties?: CandataPartyDto;

  @ApiPropertyOptional({ type: "string", pattern: "U?[A-Z][A-Z]" })
  @IsOptional()
  @Matches(/^U?[A-Z][A-Z]$/)
  countryOfAuth?: string;

  @ApiPropertyOptional({ type: "string", pattern: "U?[A-Z][A-Z]" })
  @IsOptional()
  @Matches(/^U?[A-Z][A-Z]$/)
  countryOfOrigin?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @IsOptional()
  @IsDateString()
  issueDate?: string;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @IsOptional()
  @IsDateString()
  effectiveDate?: string;
}

export class PgaDto {
  @ApiProperty({ type: "number", format: "integer" })
  @IsInt()
  @IsNotEmpty()
  type: number;

  @ApiProperty({ type: "string" })
  @IsString()
  @IsNotEmpty()
  subType: string;

  @ApiPropertyOptional({ type: "integer" })
  @IsOptional()
  @IsInt()
  unitCount?: number;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  unitType?: string;

  @ApiProperty({ type: "boolean" })
  @IsBoolean()
  @IsNotEmpty()
  excluded: boolean;

  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  selection?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  endUse?: string;

  @ApiPropertyOptional({ type: [LpcoDto] })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => LpcoDto)
  @IsArray()
  lpcos?: LpcoDto[];

  @ApiPropertyOptional({ type: [PgaDetailDto] })
  @ValidateNested({ each: true })
  @Type(() => PgaDetailDto)
  @IsArray()
  @IsOptional()
  pgadetails?: PgaDetailDto[];

  @ApiPropertyOptional({ type: () => [CandataPartyDto] })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CandataPartyDto)
  parties?: CandataPartyDto[];

  @ApiPropertyOptional({ type: () => [PgaDetailDto] })
  @ValidateNested({ each: true })
  @Type(() => PgaDetailDto)
  @IsArray()
  @IsOptional()
  exceptions?: PgaDetailDto[];

  @ApiPropertyOptional({ type: () => [PgaDetailDto] })
  @ValidateNested({ each: true })
  @Type(() => PgaDetailDto)
  @IsArray()
  @IsOptional()
  compliances?: PgaDetailDto[];
}

export class InvoiceDetailsDto {
  @ApiPropertyOptional({
    type: "integer",
    description: "Auto-generated by Candata"
  })
  @IsOptional()
  @IsInt()
  id?: number;

  @ApiPropertyOptional({ type: [PgaDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PgaDto)
  pgas?: PgaDto[];

  @ApiPropertyOptional({ type: "string", minLength: 1, maxLength: 256 })
  @IsString()
  @MinLength(1)
  @MaxLength(256)
  @IsOptional()
  description: string;

  @ApiProperty({ type: "string", minLength: 1 })
  @IsString()
  @MinLength(1)
  @IsNotEmpty()
  productNumber: string;

  @ApiProperty({ type: "number", format: "integer" })
  @IsNumber()
  @IsNotEmpty()
  quantity: number;

  @ApiProperty({ type: "number", format: "float" })
  @IsNumber()
  @IsNotEmpty()
  unitPrice: number;

  @ApiProperty({ type: "number", format: "float" })
  @IsNumber()
  @IsNotEmpty()
  valueForConversion: number;

  @ApiProperty({ type: "number", format: "float" })
  @IsNumber()
  @IsNotEmpty()
  discount: number;

  @ApiProperty({ type: "number", format: "float" })
  @IsNumber()
  @IsNotEmpty()
  netValueForConversion: number;

  @ApiPropertyOptional({ enum: VfdCode })
  @IsOptional()
  @IsEnum(VfdCode)
  vfdCode?: VfdCode;

  @ApiPropertyOptional({ type: "string", pattern: "[0-9]{10}" })
  @IsString()
  @Matches(/^[0-9]{10}$/)
  @IsOptional()
  classification?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1, maxLength: 3 })
  @IsString()
  @MinLength(1)
  @MaxLength(3)
  @IsOptional()
  unitOfMeasure?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1, maxLength: 3 })
  @IsString()
  @MinLength(1)
  @MaxLength(3)
  @IsOptional()
  gstCode?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1, maxLength: 4 })
  @IsString()
  @MinLength(1)
  @MaxLength(4)
  @IsOptional()
  countryOfOrigin?: string;

  @ApiPropertyOptional({
    enum: TariffTreatmentCode,
    description:
      "0/blank-None, 2-Most Favoured, 3-General, 4-Australia, 5-New Zealand, 7-Common Wealth, 8-Least Developed, 9-General Preference, 10-US, 11-Mexico, 12-Mexico/US, 13-Israel, 14-Chile, 21-Costa Rica, 22-Iceland, 23-Norway, 24-Switzerland, 25-Peru, 26-Colombia, 27-Jordan, 28-Panama, 29-Honduras, 30-South Korea, 31-Europe, 32-Ukraine, 33-TransPacific, 34-United Kingdom"
  })
  @IsOptional()
  @IsEnum(TariffTreatmentCode)
  tariffTreatment?: TariffTreatmentCode;

  @ApiPropertyOptional({ type: "number", format: "integer" })
  @IsOptional()
  @IsNumber()
  cciQuantity?: number;

  @ApiProperty({ type: "number", format: "float" })
  @IsNumber()
  @IsNotEmpty()
  invoiceDiscountAmount: number;

  @ApiPropertyOptional({ type: "number", format: "integer" })
  @IsNumber()
  @IsOptional()
  pageNumber?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  gstAmount?: number;

  @ApiPropertyOptional({ type: "string", maxLength: 4 })
  @IsOptional()
  @IsString()
  @MaxLength(4)
  annexCode?: string;

  @ApiPropertyOptional({ type: "string", pattern: "^[1-9][0-9]*$" })
  @IsOptional()
  @IsString()
  @Matches(/^[1-9][0-9]*$/)
  cadLineNumber?: string;

  // CAD Special Authority
  @ApiPropertyOptional({ type: "string", maxLength: 17 })
  @IsOptional()
  @IsString()
  @MaxLength(17)
  orderInCouncil?: string;

  @ApiPropertyOptional({ enum: SpecialAuthorityTimeLimitType })
  @IsOptional()
  @IsEnum(SpecialAuthorityTimeLimitType)
  warehouseTimeLimitCode?: SpecialAuthorityTimeLimitType;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @IsOptional()
  @IsDateString()
  warehouseTimeLimitStart?: string;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @IsOptional()
  @IsDateString()
  warehouseTimeLimitEnd?: string;

  @ApiPropertyOptional({
    type: "string",
    maxLength: 17,
    description: "Authority permit"
  })
  @IsOptional()
  @IsString()
  @MaxLength(17)
  remission?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  dutiesReliefLicence?: string;

  // CAD Duties and Taxes
  @ApiPropertyOptional({ type: "string", maxLength: 3 })
  @IsOptional()
  @IsString()
  @MaxLength(3)
  exciseTaxCode?: string;

  @ApiPropertyOptional({ type: "integer" })
  @IsOptional()
  @IsInt()
  alcoholPercentage?: number;

  @ApiPropertyOptional({
    type: "string",
    description: "Province of Destination"
  })
  @IsOptional()
  @IsString()
  province?: string;

  // CAD SIMA
  @ApiPropertyOptional({ enum: SimaSubjectCode })
  @IsOptional()
  @IsEnum(SimaSubjectCode)
  simaSubjectCode?: SimaSubjectCode;

  @ApiPropertyOptional({
    type: "string",
    maxLength: 3,
    description: "SIMA Code"
  })
  @IsOptional()
  @IsString()
  @MaxLength(3)
  sima?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  measureInForce?: string;

  @ApiPropertyOptional({ enum: SimaIncoterms, description: "(CAD)Trade Terms" })
  @IsOptional()
  @IsEnum(SimaIncoterms)
  simaIncoterms?: SimaIncoterms;

  @ApiPropertyOptional({
    type: "boolean",
    description:
      "(CAD)The security flag will indicate that there is a security bond in hand that could theoretically be used to cover the SIMA charges"
  })
  @IsOptional()
  @IsBoolean()
  simaSecurity?: boolean;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  simaUnitOfMeasure?: string;

  @ApiPropertyOptional({ type: "integer" })
  @IsOptional()
  @IsInt()
  simaQuantity?: number;

  // CAD Surtax and Safeguard
  @ApiPropertyOptional({ enum: SurtaxSubjectCode })
  @IsOptional()
  @IsEnum(SurtaxSubjectCode)
  surtaxSubjectCode?: SurtaxSubjectCode;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  surtaxCode?: string;

  @ApiPropertyOptional({ enum: SafeguardSubjectCode })
  @IsOptional()
  @IsEnum(SafeguardSubjectCode)
  safeguardSubjectCode?: SafeguardSubjectCode;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  safeguardCode?: string;

  // CAD amounts
  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  valueForDuty?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  valueForTax?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  antiDumpingDuty?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  countervailingDutyAmount?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  duty?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  exciseDutyAmount?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  exciseTaxAmount?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  gstTax?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  pstCannabis?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  pstTobaccoAmount?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  safeguardSurtax?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  surtax?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  totalDuty?: number;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  dutyTaxCalculationError?: string;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @IsOptional()
  @IsDateString()
  calculatedDateTime?: string;
}

export class CciDto {
  @ApiPropertyOptional({ type: () => CandataPartyDto })
  @ValidateNested()
  @Type(() => CandataPartyDto)
  @IsOptional()
  vendor?: CandataPartyDto;

  @ApiPropertyOptional({ type: () => CandataPartyDto })
  @ValidateNested()
  @Type(() => CandataPartyDto)
  @IsOptional()
  consignee?: CandataPartyDto;

  @ApiPropertyOptional({ type: () => InvoiceSummaryDto })
  @ValidateNested()
  @Type(() => InvoiceSummaryDto)
  invoiceSummary?: InvoiceSummaryDto;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsString()
  @MinLength(1)
  @IsOptional()
  purchaseOrder?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1, maxLength: 25 })
  @IsString()
  @MinLength(1)
  @MaxLength(25)
  @IsOptional()
  directShipmentMode?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsString()
  @MinLength(1)
  @IsOptional()
  directShipmentLocation?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1, maxLength: 17 })
  @IsString()
  @MinLength(1)
  @MaxLength(17)
  @IsOptional()
  container?: string;

  @ApiPropertyOptional({ type: "string", maxLength: 3 })
  @IsString()
  @MaxLength(3)
  @IsOptional()
  currency?: string;

  @ApiPropertyOptional({ type: "number" })
  @IsNumber()
  @IsOptional()
  netWeight?: number;

  @ApiPropertyOptional({ type: "number" })
  @IsNumber()
  @IsOptional()
  grossWeight?: number;

  @ApiPropertyOptional({ type: "number" })
  @IsNumber()
  @IsOptional()
  measurement?: number;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsString()
  @MinLength(1)
  @IsOptional()
  measurementUOM?: string;

  @ApiPropertyOptional({ type: "number", format: "integer" })
  @IsInt()
  @IsOptional()
  numberOfPackages?: number;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsString()
  @MinLength(1)
  @IsOptional()
  packageUOM?: string;

  @ApiPropertyOptional({ type: "string", maxLength: 4 })
  @IsString()
  @MaxLength(4)
  @IsOptional()
  countryOfExport?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  countryDirectShipment?: string;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  transportationChargesIncluded?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  transportationChargesExcluded?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  constructionChargesIncluded?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  commissionsExcluded?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  exportPackingIncluded?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  exportPackingExcluded?: number;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @IsDateString()
  @IsOptional()
  purchaseDate?: string;

  @ApiPropertyOptional({ type: "integer" })
  @IsOptional()
  @IsInt()
  id?: number;
}

export class B3DetailsDto {
  @ApiPropertyOptional({
    type: "integer",
    description: "Auto-generated by Candata"
  })
  @IsOptional()
  @IsInt()
  id?: number;

  @ApiPropertyOptional({ type: "integer" })
  @IsOptional()
  @IsInt()
  sequence?: number;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  description1?: string;

  @ApiPropertyOptional({ type: "string", pattern: "^[0-9]{10}$" })
  @IsOptional()
  @Matches(/^[0-9]{10}$/)
  classification?: string;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  quantity?: number;

  @ApiPropertyOptional({ type: "string", maxLength: 3 })
  @IsOptional()
  @IsString()
  @MaxLength(3)
  unitOfMeasure?: string;

  @ApiPropertyOptional({ enum: VfdCode })
  @IsOptional()
  @IsEnum(VfdCode)
  vfdCode?: VfdCode;

  @ApiPropertyOptional({ enum: SimaCode })
  @IsOptional()
  @IsEnum(SimaCode)
  simaCode?: SimaCode;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  dutyRate?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  valueForCurrencyConversion?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  valueForDuty?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  duty?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  taxableAmount?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  gstAmount?: number;

  @ApiPropertyOptional({ type: () => [InvoiceDetailsDto] })
  @IsOptional()
  @Type(() => InvoiceDetailsDto)
  @IsArray()
  @ValidateNested({ each: true })
  cciInvoiceDetails?: Array<InvoiceDetailsDto>;
}

export class B3SubheaderDto {
  @ApiPropertyOptional({ type: () => [B3DetailsDto] })
  @IsOptional()
  @Type(() => B3DetailsDto)
  @IsArray()
  @ValidateNested({ each: true })
  b3details?: Array<B3DetailsDto>;

  @ApiPropertyOptional({ type: "string", maxLength: 3 })
  @IsString()
  @IsOptional()
  @MaxLength(3)
  currency?: string;
}

export class B3Dto {
  @ApiPropertyOptional({ type: () => [B3SubheaderDto] })
  @IsOptional()
  @Type(() => B3SubheaderDto)
  @IsArray()
  @ValidateNested({ each: true })
  subheaders?: Array<B3SubheaderDto>;
}

export class CandataDeclarationDto {
  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalExciseTaxes?: number;

  @ApiPropertyOptional({
    enum: CandataModeOfTransport,
    description:
      "1-Air, 2-Highway, 5-Postal , 6-Rail, 7-Pipeline, 8-HandCarriedGoods, 9-Marine, 10-ElectricalGrid"
  })
  @IsOptional()
  @IsEnum(CandataModeOfTransport)
  modeOfTransport?: CandataModeOfTransport;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalDutiesAndTaxesWithInterest?: number;

  @ApiPropertyOptional({ type: "integer" })
  @IsOptional()
  @IsInt()
  cadVersion?: number;

  @ApiPropertyOptional({
    enum: CandataDeclarationType,
    description:
      "AB-StandardAccounting, F-LVS, TT-CustomsSelfAssessment, 10-WarehouseIn, 13-ReWarehouse, 20-ExWarehouseForConsumption, 21-ExWarehouseForExport, 30-TransferOfGoods"
  })
  @IsOptional()
  @IsEnum(CandataDeclarationType)
  type?: CandataDeclarationType;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  totalExciseDuties?: number;

  @ApiPropertyOptional({ type: "string", maxLength: 15 })
  @IsOptional()
  @IsString()
  @MaxLength(15)
  importerNumber?: string;

  @ApiPropertyOptional({ type: "boolean" })
  @IsOptional()
  @IsBoolean()
  isPreCARM?: boolean;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  total?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsOptional()
  @IsNumber()
  totalRelieved?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalValueForDuty?: number;

  @ApiPropertyOptional({ type: "integer" })
  @IsInt()
  @IsOptional()
  id?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalAntiDumping?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalPSTCannabisAmount?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalInterest?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalSurtaxes?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalPSTTobaccoAmount?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalCustomsDuties?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalCustomsDutiesRelieved?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  taxAssessedAmount?: number;

  @ApiPropertyOptional({ type: "integer" })
  @IsInt()
  @IsOptional()
  version?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalProvAlcoholTaxAmount?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalSafeguards?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalGST?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalGSTRelieved?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalPST?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  totalCountervailing?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @IsNumber()
  @IsOptional()
  freightCharges?: number;

  @ApiPropertyOptional({
    enum: CandataDeclarationStatus,
    description: "-1-Draft, 39-Approved, 41-Rejected, 42-ApprovalPending, 200-Acknowledged, 400-InvalidSyntax"
  })
  @IsEnum(CandataDeclarationStatus)
  @IsOptional()
  status?: CandataDeclarationStatus;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @IsOptional()
  @IsDateString()
  lastCalculatedAt?: string;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @IsOptional()
  @IsDateString()
  responseIssueDateTime?: string;
}

export class CandataShipmentDto {
  @ApiPropertyOptional({ type: "string", minLength: 4, maxLength: 10 })
  @IsOptional()
  @IsString()
  @MinLength(4)
  @MaxLength(10)
  fileNumber?: string;

  @ApiPropertyOptional({ type: "string", pattern: "^[0-9]{14}$" })
  @IsOptional()
  @IsString()
  @Matches(/^[0-9]{14}$/)
  transactionNumber?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1, maxLength: 17 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(17)
  container?: string;

  @ApiPropertyOptional({
    oneOf: [
      { type: "string", pattern: "^[^,]{1,17}(,[^,]{1,17})*$" },
      { type: "array", items: { type: "string", minLength: 1, maxLength: 17 }, minItems: 1 }
    ]
  })
  @IsOptional()
  @IsTrue(
    (value) => {
      const containerArray = Array.isArray(value) ? value : typeof value === "string" ? value.split(",") : [];
      return (
        containerArray.length > 0 &&
        containerArray.every(
          (container) => typeof container === "string" && container.length >= 1 && container.length <= 17
        )
      );
    },
    {
      message: "$property must either be a comma-separated string or an array of container numbers"
    }
  )
  containers?: string | Array<string>;

  @ApiProperty({ type: "string", minLength: 1, maxLength: 20 })
  @IsString()
  @MinLength(1)
  @MaxLength(20)
  @IsNotEmpty()
  billOfLading: string;

  @ApiProperty({ type: "string", minLength: 1, maxLength: 10 })
  @IsString()
  @MinLength(1)
  @MaxLength(10)
  @IsNotEmpty()
  customerNumber: string;

  @ApiPropertyOptional({
    type: "string",
    pattern: "[0-9A-Z]{3}(-|[0-9A-Z])[0-9A-Z]{1,21}"
  })
  @IsString()
  @Matches(/^[0-9A-Z]{3}(-|[0-9A-Z])[0-9A-Z]{1,21}$/)
  @IsOptional()
  cargoControlNumber?: string;

  @ApiPropertyOptional({
    oneOf: [
      {
        type: "string",
        pattern: "^[0-9A-Z]{3}(-|[0-9A-Z])[0-9A-Z]{1,21}(,[0-9A-Z]{3}(-|[0-9A-Z])[0-9A-Z]{1,21})*$"
      },
      {
        type: "array",
        items: { type: "string", pattern: "^[0-9A-Z]{3}(-|[0-9A-Z])[0-9A-Z]{1,21}$" },
        minItems: 1
      }
    ]
  })
  @IsOptional()
  @IsTrue(
    (value) => {
      const CCN_REGEX = /^[0-9A-Z]{3}(-|[0-9A-Z])[0-9A-Z]{1,21}$/;
      const ccnArray = Array.isArray(value) ? value : typeof value === "string" ? value.split(",") : [];
      return (
        ccnArray.length > 0 &&
        ccnArray.every((ccn) => typeof ccn === "string" && ccn.length > 0 && CCN_REGEX.test(ccn))
      );
    },
    {
      message: "$property must either be a comma-separated string or an array of cargo control numbers"
    }
  )
  cargoControlNumbers?: string | Array<string>;

  @ApiProperty({
    type: "string",
    minLength: 4,
    maxLength: 4,
    pattern: "[0][0-9]{3}"
  })
  @IsString()
  @MinLength(4)
  @MaxLength(4)
  @Matches(/^[0][0-9]{3}$/)
  @IsNotEmpty()
  port: string;

  @ApiProperty({
    type: "string",
    minLength: 1,
    maxLength: 7,
    description: "The package count of the whole shipment"
  })
  @IsString()
  @MinLength(1)
  @MaxLength(7)
  @IsNotEmpty()
  numberOfContainers: string;

  @ApiProperty({ type: "number", description: "The weight of the whole shipment in kg" })
  @IsNumber()
  @IsNotEmpty()
  weight: number;

  @ApiPropertyOptional({ type: "number", description: "The volume of the whole shipment in m3" })
  @MaxLength(10, {
    message: "Volume cannot be longer than 10 characters"
  })
  @IsDecimal()
  @Matches(/^\d{1,10}(\.\d{1,2})?$/, {
    message: "Volume must be a number with up to 2 decimal places"
  })
  @IsOptional()
  volume?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  orderNumber?: string;

  @ApiProperty({ type: "string", format: "date" })
  @IsNotEmpty()
  @IsDateString()
  etaDate: string;

  @ApiProperty({ type: "string", format: "date" })
  @IsNotEmpty()
  @IsDateString()
  directShipmentDate: string;

  @ApiProperty({ type: "string", maxLength: 4 })
  @IsString()
  @MaxLength(4)
  @IsNotEmpty()
  sublocationCode: string;

  @ApiProperty({ type: () => [CciDto] })
  @Type(() => CciDto)
  @ValidateNested({ each: true })
  @IsArray()
  @ArrayMinSize(1)
  ccis: CciDto[];

  @ApiPropertyOptional({ type: () => B3Dto })
  @IsOptional()
  @Type(() => B3Dto)
  @ValidateNested()
  b3?: B3Dto;

  @ApiPropertyOptional({ type: () => CandataDeclarationDto })
  @IsOptional()
  @Type(() => CandataDeclarationDto)
  @ValidateNested()
  declaration?: CandataDeclarationDto;

  @ApiPropertyOptional({ type: () => CandataPartyDto })
  @IsOptional()
  @Type(() => CandataPartyDto)
  @ValidateNested()
  vendor?: CandataPartyDto;
}

export class CandataIidResponseErrorMessageDto {
  @ApiProperty({ type: "string" })
  @IsString()
  @IsNotEmpty()
  $message: string;
}

export class CandataIidResponseErrorsDto {
  @ApiProperty({
    oneOf: [
      { $ref: getSchemaPath(CandataIidResponseErrorMessageDto) },
      {
        type: "array",
        items: { $ref: getSchemaPath(CandataIidResponseErrorMessageDto) }
      }
    ]
  })
  @Type(() => CandataIidResponseErrorMessageDto)
  @ValidateNested({ each: true })
  error: Array<CandataIidResponseErrorMessageDto> | CandataIidResponseErrorMessageDto;
}

export class CandataIidResponseResultDto {
  @ApiProperty({
    type: "string",
    minLength: 4,
    maxLength: 10,
    description: "Unique identifier for a shipment"
  })
  @IsString()
  @MinLength(4)
  @MaxLength(10)
  @IsNotEmpty()
  $fileNumber: string;

  @ApiPropertyOptional({ type: "string" })
  @IsString()
  @IsOptional()
  $interchange?: string;

  @ApiPropertyOptional({
    type: "string",
    maxLength: 10,
    description: "Unique identifier for a customer"
  })
  @IsString()
  @MaxLength(10)
  @IsOptional()
  $customer?: string;

  @ApiPropertyOptional({ type: () => CandataIidResponseErrorsDto })
  @IsOptional()
  @Type(() => CandataIidResponseErrorsDto)
  @ValidateNested()
  errors?: CandataIidResponseErrorsDto;
}

export class CandataIidResponseDto {
  @ApiProperty({ type: () => CandataIidResponseResultDto })
  @Type(() => CandataIidResponseResultDto)
  @ValidateNested()
  result: CandataIidResponseResultDto;
}
