import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  Min,
  ValidateNested
} from "class-validator";
import { MatchingRule } from "../entities";
import { SimaFiling } from "../entities/sima-filing.entity";
import { SimaFilingColumn, SimaIncoterms, SimaSubjectCode } from "../types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import { BatchUpdateMatchingConditionsDto, CreateMatchingConditionDto } from "./matching-condition.dto";
import { CreateMatchingRuleWithoutSourceDto, EditMatchingRuleWithoutSourceDto } from "./matching-rule.dto";

export class GetSimaFilingsDto extends GetManyDto {
  @ApiPropertyOptional({ enum: SimaFilingColumn, default: SimaFilingColumn.id })
  @IsOptional()
  @IsEnum(SimaFilingColumn)
  sortBy?: SimaFilingColumn;

  @ApiPropertyOptional({ enum: SimaSubjectCode })
  @IsOptional()
  @IsEnum(SimaSubjectCode)
  subjectCode?: SimaSubjectCode;

  @ApiPropertyOptional({ enum: SimaIncoterms })
  @IsOptional()
  @IsEnum(SimaIncoterms)
  incoterms?: SimaIncoterms;

  @ApiPropertyOptional({ type: "boolean" })
  @IsOptional()
  @IsBoolean()
  security?: boolean;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  measureInForceId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  simaCodeId?: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "For basic user and organization admin, Organization ID will be auto-set according to current user's organization"
  })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  organizationId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetSimaFilingsResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [SimaFiling] })
  data: Array<SimaFiling>;
}

export class CreateSimaFilingDto {
  @ApiProperty({
    enum: SimaSubjectCode,
    description: "N-Non-subject, S-Subject, U-Undertaking"
  })
  @IsNotEmpty()
  @IsEnum(SimaSubjectCode)
  subjectCode: SimaSubjectCode;

  @ApiPropertyOptional({
    enum: SimaIncoterms,
    description: "If SIMA is not subjected, this field is set to null"
  })
  @IsOptional()
  @IsEnum(SimaIncoterms)
  incoterms?: SimaIncoterms | null;

  @ApiPropertyOptional({
    type: "boolean",
    default: false,
    description: "If SIMA is not subjected, this field is set to false"
  })
  @IsOptional()
  @IsBoolean()
  security?: boolean;

  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  measureInForceId: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description: "Required if SIMA is subjected or undertaking. Set to null if SIMA is not subjected"
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  simaCodeId?: number | null;
}

export class EditSimaFilingDto {
  @ApiPropertyOptional({
    enum: SimaSubjectCode,
    description: "N-Non-subject, S-Subject, U-Undertaking"
  })
  @IsOptional()
  @IsEnum(SimaSubjectCode)
  subjectCode?: SimaSubjectCode;

  @ApiPropertyOptional({
    enum: SimaIncoterms,
    description: "If SIMA is not subject, this field is set to null"
  })
  @IsOptional()
  @IsEnum(SimaIncoterms)
  incoterms?: SimaIncoterms | null;

  @ApiPropertyOptional({
    type: "boolean",
    description: "If SIMA is not subjected, this field is set to false"
  })
  @IsOptional()
  @IsBoolean()
  security?: boolean;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  measureInForceId?: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description: "Required if SIMA is subjected or undertaking. Set to null if SIMA is not subjected"
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  simaCodeId?: number | null;
}

export class CreateSimaFilingAndMatchingRuleDto {
  @ApiProperty({ type: CreateSimaFilingDto })
  @Type(() => CreateSimaFilingDto)
  @ValidateNested()
  simaFiling: CreateSimaFilingDto;

  @ApiProperty({ type: CreateMatchingRuleWithoutSourceDto })
  @Type(() => CreateMatchingRuleWithoutSourceDto)
  @ValidateNested()
  matchingRule: CreateMatchingRuleWithoutSourceDto;

  @ApiPropertyOptional({ type: [CreateMatchingConditionDto] })
  @Type(() => CreateMatchingConditionDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  matchingConditions?: Array<CreateMatchingConditionDto>;
}

export class EditSimaFilingAndMatchingRuleDto {
  @ApiPropertyOptional({ type: EditSimaFilingDto })
  @Type(() => EditSimaFilingDto)
  @IsOptional()
  @ValidateNested()
  simaFiling?: EditSimaFilingDto;

  @ApiPropertyOptional({ type: EditMatchingRuleWithoutSourceDto })
  @Type(() => EditMatchingRuleWithoutSourceDto)
  @IsOptional()
  @ValidateNested()
  matchingRule?: EditMatchingRuleWithoutSourceDto;

  @ApiPropertyOptional({ type: BatchUpdateMatchingConditionsDto })
  @Type(() => BatchUpdateMatchingConditionsDto)
  @IsOptional()
  @ValidateNested()
  matchingConditions?: BatchUpdateMatchingConditionsDto;
}

export class CreateOrEditSimaFilingAndMatchingRuleResponseDto {
  @ApiProperty({ type: SimaFiling })
  simaFiling: SimaFiling;

  @ApiProperty({ type: MatchingRule })
  matchingRule: MatchingRule;
}
