import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsEnum, IsInt, IsNotEmpty, IsOptional, IsString, Length, Min, MinLength } from "class-validator";
import { Country } from "../entities/country.entity";
import { State } from "../entities/state.entity";
import { CountryColumn } from "../types/country.types";
import { StateColumn } from "../types/state.types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class GetCountriesDto extends GetManyDto {
  @ApiPropertyOptional({ enum: CountryColumn, default: CountryColumn.id })
  @IsOptional()
  @IsEnum(CountryColumn)
  sortBy?: CountryColumn;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ minLength: 3, maxLength: 3 })
  @IsOptional()
  @IsString()
  @Length(3)
  alpha3?: string;

  @ApiPropertyOptional({ minLength: 2, maxLength: 2 })
  @IsOptional()
  @IsString()
  @Length(2)
  alpha2?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetCountriesResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [Country] })
  countries: Array<Country>;
}

export class GetStatesDto extends GetManyDto {
  @ApiPropertyOptional({ enum: StateColumn, default: StateColumn.id })
  @IsOptional()
  @IsEnum(StateColumn)
  sortBy?: StateColumn;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ minLength: 2, maxLength: 2 })
  @IsOptional()
  @IsString()
  @Length(2)
  alpha2?: string;
}

export class GetStatesResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [State] })
  states: Array<State>;
}

export class CreateCountryDto {
  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ minLength: 3, maxLength: 3 })
  @IsNotEmpty()
  @IsString()
  @Length(3)
  alpha3: string;

  @ApiPropertyOptional({ minLength: 2, maxLength: 2 })
  @IsOptional()
  @IsString()
  @Length(2)
  alpha2?: string;
}

export class EditCountryDto {
  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ minLength: 3, maxLength: 3 })
  @IsOptional()
  @IsString()
  @Length(3)
  alpha3?: string;

  @ApiPropertyOptional({ minLength: 2, maxLength: 2 })
  @IsOptional()
  @IsString()
  @Length(2)
  alpha2?: string;
}
