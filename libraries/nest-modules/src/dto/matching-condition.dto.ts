import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import { MatchingCondition } from "../entities";
import {
  IsOptional,
  IsEnum,
  IsString,
  MinLength,
  IsInt,
  Min,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  ArrayUnique,
  IsBoolean,
  IsNumber,
  IsDateString,
  isBooleanString,
  isDateString,
  IsDate
} from "class-validator";
import { MatchingConditionAttributeType, MatchingConditionColumn, MatchingConditionOperator } from "../types";
import { Transform, Type } from "class-transformer";

export class GetMatchingConditionsDto extends GetManyDto {
  @ApiPropertyOptional({ enum: MatchingConditionColumn })
  @IsOptional()
  @IsEnum(MatchingConditionColumn)
  sortBy?: MatchingConditionColumn;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  attribute?: string;

  @ApiPropertyOptional({ enum: MatchingConditionOperator })
  @IsOptional()
  @IsEnum(MatchingConditionOperator)
  operator?: MatchingConditionOperator;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  value?: string;

  @ApiPropertyOptional()
  @Transform(({ value }) =>
    typeof value === "string" && ["true", "false"].includes(value.trim().toLowerCase())
      ? value.trim().toLowerCase() === "true"
      : value
  )
  @IsOptional()
  @IsBoolean()
  isOperationInverted?: boolean;

  @ApiPropertyOptional({ type: "enum", enum: MatchingConditionAttributeType })
  @IsOptional()
  @IsEnum(MatchingConditionAttributeType)
  attributeType?: MatchingConditionAttributeType;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  valueString?: string;

  @ApiPropertyOptional({ type: "integer" })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  valueInteger?: number;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Type(() => Number)
  @IsOptional()
  @IsNumber()
  valueFloat?: number;

  @ApiPropertyOptional({ type: "boolean" })
  @Transform(({ value }) =>
    typeof value === "string" && isBooleanString(value.trim().toLowerCase())
      ? value.trim().toLowerCase() === "true"
      : value
  )
  @IsOptional()
  @IsBoolean()
  valueBoolean?: boolean;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Transform(({ value }) => (typeof value === "string" && isDateString(value) ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  valueDateTime?: Date;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetMatchingConditionsResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: [MatchingCondition] })
  matchingConditions: Array<MatchingCondition>;
}

export class CreateMatchingConditionDto {
  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  attribute: string;

  @ApiProperty({ enum: MatchingConditionOperator })
  @IsNotEmpty()
  @IsEnum(MatchingConditionOperator)
  operator: MatchingConditionOperator;

  @ApiPropertyOptional({
    description:
      'The value of the condition. The type of the value depends on the attribute and operator. For operator "in", this field should be a non-empty array of values. For operator "between-inclusive" or "between-exclusive", this field should be a 2-element array of values.',
    examples: [
      null,
      "string",
      "123",
      "123.45",
      "true",
      "2024-01-01",
      "2024-01-01T00:00:00Z",
      `["value1","value2","value3"]`,
      `[1, 2, 3]`
    ]
  })
  @IsOptional()
  @IsString()
  value?: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isOperationInverted: boolean;
}

export class EditMatchingConditionDto {
  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  attribute?: string;

  @ApiPropertyOptional({ enum: MatchingConditionOperator })
  @IsOptional()
  @IsEnum(MatchingConditionOperator)
  operator?: MatchingConditionOperator;

  @ApiPropertyOptional({
    description:
      'The value of the condition. The type of the value depends on the attribute and operator. For operator "in", this field should be an array of values.',
    examples: [
      null,
      "string",
      "123",
      "123.45",
      "true",
      "2024-01-01",
      "2024-01-01T00:00:00Z",
      `["value1","value2","value3"]`,
      `[1, 2, 3]`
    ]
  })
  @IsOptional()
  @IsString()
  value?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isOperationInverted?: boolean;
}

export class EditMatchingConditionWithIdDto extends EditMatchingConditionDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  id: number;
}

export class BatchUpdateMatchingConditionsDto {
  @ApiProperty({ type: [CreateMatchingConditionDto] })
  @Type(() => CreateMatchingConditionDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  create?: Array<CreateMatchingConditionDto>;

  @ApiProperty({ type: [EditMatchingConditionWithIdDto] })
  @Type(() => EditMatchingConditionWithIdDto)
  @IsOptional()
  @IsArray()
  @ArrayUnique((o) => o.id)
  @ValidateNested({ each: true })
  edit?: Array<EditMatchingConditionWithIdDto>;

  @ApiProperty({ type: "integer", minimum: 1, isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  delete?: Array<number>;
}

export class BatchUpdateMatchingConditionsResponseDto {
  @ApiProperty({ type: [MatchingCondition] })
  matchingConditions: Array<MatchingCondition>;
}
