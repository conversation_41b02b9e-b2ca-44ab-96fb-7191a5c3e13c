import { AggregationTargetType, DocumentAggregationAction } from "../types";

/**
 * Cross-module aggregation event DTOs
 *
 * These events are used for communication between modules and should be imported
 * from nest-modules to avoid tight coupling between individual app modules.
 */

interface DocumentAggregationSuccess {
  success: true;
  targetId: number;
  targetType: AggregationTargetType;
  // FIXME: remove this in the future
  error?: string;
}

interface DocumentAggregationFailed {
  success: false;
  error: string;
}

type DocumentAggregationResult = {
  id: number;

  /** @deprecated prefer to use targetId and targetType */
  shipmentId: number;

  /** @deprecated please use documents instead */
  documentId: number;

  /** @deprecated please use documents instead */
  documentName: string;

  documents: {
    id: number;
    name: string;
  }[];

  action: DocumentAggregationAction;
} & (DocumentAggregationSuccess | DocumentAggregationFailed);

/**
 * Event emitted when batch document aggregation is completed
 * Used for cross-module communication between Aggregation and Core-Agent modules
 */
export class BatchDocumentAggregatedEvent {
  public readonly batchId: string;
  public readonly organizationId: number;
  public readonly result: DocumentAggregationResult[];

  constructor(payload: BatchDocumentAggregatedEvent) {
    Object.assign(this, payload);
  }
}
