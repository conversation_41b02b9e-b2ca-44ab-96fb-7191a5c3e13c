import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import { CanadaSimaCode } from "../entities";
import {
  IsOptional,
  IsEnum,
  IsString,
  MinLength,
  IsInt,
  Min,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  ArrayUnique
} from "class-validator";
import { CanadaSimaCodeColumn } from "../types";
import { Type } from "class-transformer";

export class GetCanadaSimaCodesDto extends GetManyDto {
  @ApiPropertyOptional({ enum: CanadaSimaCodeColumn })
  @IsOptional()
  @IsEnum(CanadaSimaCodeColumn)
  sortBy?: CanadaSimaCodeColumn;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  code?: number;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  explanation?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetCanadaSimaCodesResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: [CanadaSimaCode] })
  data: Array<CanadaSimaCode>;
}

export class CreateCanadaSimaCodeDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  code: number;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  explanation: string;
}

export class EditCanadaSimaCodeDto {
  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  code?: number;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  explanation?: string;
}

export class EditCanadaSimaCodeWithIdDto extends EditCanadaSimaCodeDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  id: number;
}

export class BatchUpdateCanadaSimaCodesDto {
  @ApiProperty({ type: [CreateCanadaSimaCodeDto] })
  @Type(() => CreateCanadaSimaCodeDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  create?: Array<CreateCanadaSimaCodeDto>;

  @ApiProperty({ type: [EditCanadaSimaCodeWithIdDto] })
  @Type(() => EditCanadaSimaCodeWithIdDto)
  @IsOptional()
  @IsArray()
  @ArrayUnique((o) => o.id)
  @ValidateNested({ each: true })
  edit?: Array<EditCanadaSimaCodeWithIdDto>;

  @ApiProperty({ type: "integer", minimum: 1, isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  delete?: Array<number>;
}

export class BatchUpdateCanadaSimaCodesResponseDto {
  @ApiProperty({ type: [CanadaSimaCode] })
  data: Array<CanadaSimaCode>;
}
