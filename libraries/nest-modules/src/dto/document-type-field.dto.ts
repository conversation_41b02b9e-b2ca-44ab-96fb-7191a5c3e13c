import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
  ArrayUnique,
  IsArray,
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
  <PERSON><PERSON>ength,
  ValidateNested
} from "class-validator";
import { DocumentTypeField } from "../entities/document-type-field.entity";
import { DocumentFieldDataType, DocumentTypeFieldColumn } from "../types/document-type-field.types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class GetDocumentTypeFieldsDto extends GetManyDto {
  @ApiPropertyOptional({ enum: DocumentTypeFieldColumn, default: DocumentTypeFieldColumn.id })
  @IsOptional()
  @IsEnum(DocumentTypeFieldColumn)
  sortBy?: DocumentTypeFieldColumn;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ enum: DocumentFieldDataType })
  @IsOptional()
  @IsEnum(DocumentFieldDataType)
  dataType?: DocumentFieldDataType;

  @ApiPropertyOptional()
  @Transform(({ value }) =>
    typeof value === "string" && ["true", "false"].includes(value.trim().toLowerCase())
      ? value.trim().toLowerCase() === "true"
      : value
  )
  @IsOptional()
  @IsBoolean()
  isMandatory?: boolean;

  // @ApiPropertyOptional({ type: 'integer', minimum: 1 })
  // @Type(() => Number)
  // @IsOptional()
  // @IsInt()
  // @Min(1)
  // documentTypeId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description?: string;
}

export class GetDocumentTypeFieldsResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [DocumentTypeField] })
  documentTypeFields: Array<DocumentTypeField>;
}

export class CreateDocumentTypeFieldDto {
  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  name: string;

  @ApiProperty({ enum: DocumentFieldDataType })
  @IsNotEmpty()
  @IsEnum(DocumentFieldDataType)
  dataType: DocumentFieldDataType;

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isMandatory: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description?: string;
}

export class EditDocumentTypeFieldDto {
  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ enum: DocumentFieldDataType })
  @IsOptional()
  @IsEnum(DocumentFieldDataType)
  dataType?: DocumentFieldDataType;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isMandatory?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description?: string;
}

export class EditDocumentTypeFieldWithIdDto extends EditDocumentTypeFieldDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  id: number;
}

export class BatchUpdateDocumentTypeFieldsDto {
  @ApiProperty({ type: [CreateDocumentTypeFieldDto] })
  @Type(() => CreateDocumentTypeFieldDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  create?: Array<CreateDocumentTypeFieldDto>;

  @ApiProperty({ type: [EditDocumentTypeFieldWithIdDto] })
  @Type(() => EditDocumentTypeFieldWithIdDto)
  @IsOptional()
  @IsArray()
  @ArrayUnique((o) => o.id)
  @ValidateNested({ each: true })
  edit?: Array<EditDocumentTypeFieldWithIdDto>;

  @ApiProperty({ type: "integer", minimum: 1, isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  delete?: Array<number>;
}

export class BatchUpdateDocumentTypeFieldsResponseDto {
  @ApiProperty({ type: [DocumentTypeField] })
  documentTypeFields: Array<DocumentTypeField>;
}
