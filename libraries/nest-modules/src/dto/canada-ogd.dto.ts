import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import { CanadaOgd } from "../entities";
import {
  IsOptional,
  IsEnum,
  IsString,
  MinLength,
  IsInt,
  Min,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  ArrayUnique
} from "class-validator";
import { CanadaGovernmentAgency, CanadaOgdColumn } from "../types";
import { Type } from "class-transformer";

export class GetCanadaOgdsDto extends GetManyDto {
  @ApiPropertyOptional({ enum: CanadaOgdColumn })
  @IsOptional()
  @IsEnum(CanadaOgdColumn)
  sortBy?: CanadaOgdColumn;

  @ApiPropertyOptional({ enum: CanadaGovernmentAgency })
  @IsOptional()
  @IsEnum(CanadaGovernmentAgency)
  agency?: CanadaGovernmentAgency;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  program?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  commodityType?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetCanadaOgdsResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: [CanadaOgd] })
  data: Array<CanadaOgd>;
}

export class CreateCanadaOgdDto {
  @ApiProperty({ enum: CanadaGovernmentAgency })
  @IsNotEmpty()
  @IsEnum(CanadaGovernmentAgency)
  agency: CanadaGovernmentAgency;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  program: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  commodityType?: string;
}

export class EditCanadaOgdDto {
  @ApiPropertyOptional({ enum: CanadaGovernmentAgency })
  @IsOptional()
  @IsEnum(CanadaGovernmentAgency)
  agency?: CanadaGovernmentAgency;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  program?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  commodityType?: string;
}

export class EditCanadaOgdWithIdDto extends EditCanadaOgdDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  id: number;
}

export class BatchUpdateCanadaOgdsDto {
  @ApiProperty({ type: [CreateCanadaOgdDto] })
  @Type((type) => CreateCanadaOgdDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  create?: Array<CreateCanadaOgdDto>;

  @ApiProperty({ type: [EditCanadaOgdWithIdDto] })
  @Type((type) => EditCanadaOgdWithIdDto)
  @IsOptional()
  @IsArray()
  @ArrayUnique((o) => o.id)
  @ValidateNested({ each: true })
  edit?: Array<EditCanadaOgdWithIdDto>;

  @ApiProperty({ type: "integer", minimum: 1, isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  delete?: Array<number>;
}

export class BatchUpdateCanadaOgdsResponseDto {
  @ApiProperty({ type: [CanadaOgd] })
  data: Array<CanadaOgd>;
}
