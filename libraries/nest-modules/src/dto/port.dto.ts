import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsString, <PERSON>Length, MinLength } from "class-validator";
import { Port } from "../entities";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class GetPortsDto extends GetManyDto {
  @ApiPropertyOptional({ type: "string", description: "Port code" })
  @MinLength(1)
  @MaxLength(10)
  @IsOptional()
  @IsString()
  code?: string;
}

export class GetPortsResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [Port] })
  data: Array<Port>;
}
