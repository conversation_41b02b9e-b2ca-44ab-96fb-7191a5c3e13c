import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
  Min,
  <PERSON>Length,
  ValidateNested
} from "class-validator";
import { CandataCustomerProfileStatus, CandataProvince } from "../types/candata.types";

export class CandataAddressDto {
  @ApiPropertyOptional({ type: "string", minLength: 0, maxLength: 35 })
  @IsOptional()
  @IsString()
  @MinLength(0)
  @MaxLength(35)
  address1?: string;

  @ApiPropertyOptional({ type: "string", minLength: 0, maxLength: 35 })
  @IsOptional()
  @IsString()
  @MinLength(0)
  @MaxLength(35)
  address2?: string;

  @ApiPropertyOptional({ type: "string", minLength: 0, maxLength: 100 })
  @IsOptional()
  @IsString()
  @MinLength(0)
  @MaxLength(100)
  city?: string;

  @ApiPropertyOptional({ enum: CandataProvince })
  @IsOptional()
  @IsEnum(CandataProvince)
  province?: CandataProvince;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  postalCode?: string;

  @ApiPropertyOptional({ type: "string", pattern: "^U?[A-Z][A-Z]$" })
  @IsOptional()
  @IsString()
  @Matches(/^U?[A-Z][A-Z]$/)
  country?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  businessNumber?: string;

  @ApiPropertyOptional({ type: "string", format: "email" })
  @IsOptional()
  @IsEmail()
  emailAddress?: string;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  contact?: string;
}

export class CandataCustomerProfileDto {
  @ApiPropertyOptional({ enum: CandataCustomerProfileStatus })
  @IsOptional()
  @IsEnum(CandataCustomerProfileStatus)
  accountStatus?: CandataCustomerProfileStatus;

  @ApiPropertyOptional({ type: "boolean" })
  @IsOptional()
  @IsBoolean()
  temporary?: boolean;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ enum: ["English", "French"] })
  @IsOptional()
  @IsEnum(["English", "French"])
  language?: "English" | "French";

  @ApiPropertyOptional({ type: "string", maxLength: 13 })
  @IsOptional()
  @IsString()
  @MaxLength(13)
  phone?: string;

  @ApiPropertyOptional({ type: "string", maxLength: 13 })
  @IsOptional()
  @IsString()
  @MaxLength(13)
  fax?: string;

  @ApiPropertyOptional({ type: "string", format: "email" })
  @IsOptional()
  @IsEmail()
  emailAddress?: string;

  @ApiPropertyOptional({ type: "string", maxLength: 30 })
  @IsOptional()
  @IsString()
  @MaxLength(30)
  contact?: string;
}

export class CandataCustomsDto {
  @ApiPropertyOptional({ type: "boolean" })
  @IsOptional()
  @IsBoolean()
  recapEntry?: boolean;

  @ApiPropertyOptional({ type: "boolean" })
  @IsOptional()
  @IsBoolean()
  provincialTaxApplies?: boolean;

  @ApiPropertyOptional({ type: "boolean" })
  @IsOptional()
  @IsBoolean()
  autoCreateB3?: boolean;

  @ApiPropertyOptional({ type: "boolean" })
  @IsOptional()
  @IsBoolean()
  consigneeRequired?: boolean;

  @ApiPropertyOptional({ type: "string", maxLength: 15 })
  @IsOptional()
  @IsString()
  @MaxLength(15)
  businessNumber?: string;
}

export class CandataCustomerDto {
  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  id?: number;

  @ApiProperty({ type: "string", maxLength: 10 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(10)
  clientNumber: string;

  @ApiPropertyOptional({ type: () => CandataAddressDto })
  @IsOptional()
  @Type(() => CandataAddressDto)
  @ValidateNested()
  address?: CandataAddressDto;

  @ApiPropertyOptional({ type: () => CandataCustomerProfileDto })
  @IsOptional()
  @Type(() => CandataCustomerProfileDto)
  @ValidateNested()
  profile?: CandataCustomerProfileDto;

  @ApiPropertyOptional({ type: () => CandataCustomsDto })
  @IsOptional()
  @Type(() => CandataCustomsDto)
  @ValidateNested()
  customs?: CandataCustomsDto;
}
