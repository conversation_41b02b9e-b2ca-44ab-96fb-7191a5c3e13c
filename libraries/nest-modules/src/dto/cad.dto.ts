import { Type } from "class-transformer";
import { Is<PERSON>rray, IsEnum, IsN<PERSON>ber, IsOptional, IsString, ValidateNested } from "class-validator";
import { OrganizationCustomsBroker } from "../types";

export class CadCommercialInvoiceLineDto {
  @IsNumber()
  sequence: number;

  @IsString()
  previousLineNo: string;

  @IsString()
  hsCode: string;

  @IsString()
  hsCodeDescription: string;

  @IsString()
  description: string;

  @IsString()
  quantity: string;

  @IsString()
  quantityUOM: string;

  @IsString()
  timeLimitType: string;

  @IsString()
  extensionDate: string;

  @IsString()
  countryOfOrigin: string;

  @IsString()
  countryOfOriginState: string;

  @IsString()
  countryOfExport: string;

  @IsString()
  countryOfExportState: string;

  @IsString()
  directShipmentDate: string;

  @IsString()
  tt: string;

  @IsString()
  tariffCode: string;

  @IsString()
  timeLimitFrom: string;

  @IsString()
  timeLimitTo: string;

  @IsString()
  destinationProvince: string;

  @IsString()
  valueForCurrencyConversion: string;

  @IsString()
  currency: string;

  @IsString()
  exchangeRate: string;

  @IsString()
  valueForDuty: string;

  @IsString()
  drpLicense: string;

  @IsString()
  orderInCouncil: string;

  @IsString()
  authorityPermit: string;

  @IsString()
  customsDuties: string;

  @IsString()
  exciseTax: string;

  @IsString()
  exciseDuties: string;

  @IsString()
  surTax: string;

  @IsString()
  antiDumping: string;

  @IsString()
  safeguard: string;

  @IsString()
  countervailing: string;

  @IsString()
  valueForTax: string;

  @IsString()
  gst: string;

  @IsString()
  pstHst: string;

  @IsString()
  provincialAlcoholTax: string;

  @IsString()
  provincialTobaccoTax: string;

  @IsString()
  alcoholPercentage: string;

  @IsString()
  provincialCannabisExciseDuty: string;

  @IsString()
  cbsaCaseNo: string;

  @IsString()
  rulingNo: string;

  @IsString()
  appealCaseNo: string;

  @IsString()
  complCaseNo: string;

  @IsString()
  totalDutiesAndTaxes: string;

  // Remarks
  @IsString()
  reasonCode1: string;

  @IsString()
  reasonCode2: string;

  @IsString()
  reasonCode3: string;

  @IsString()
  authorityCode1: string;

  @IsString()
  authorityCode2: string;

  @IsString()
  authorityCode3: string;

  @IsString()
  commodityRemark1: string;

  @IsString()
  commodityRemark2: string;

  @IsString()
  commodityRemark3: string;

  @IsString()
  commodityAppealsProgram1: string;

  @IsString()
  commodityAppealsProgram2: string;

  @IsString()
  commodityAppealsProgram3: string;
}

export class CadCommercialInvoiceDto {
  @IsString()
  vendorName: string;

  @IsString()
  vendorAddress: string;

  @IsString()
  @IsOptional()
  vendorPhoneNumber: string;

  @IsString()
  purchaserName: string;

  @IsString()
  purchaserAddress: string;

  @IsString()
  purchaserPhoneNumber: string;

  @IsString()
  invoiceNumber: string;

  @IsString()
  @IsOptional()
  invoiceValue: string;

  @IsString()
  invoiceCurrency: string;

  @IsString()
  poNumber: string;

  @IsString()
  freightCharges: string;

  @IsString()
  portOfExit: string;

  // Remarks
  @IsString()
  vendorReasonCode1: string;

  @IsString()
  vendorReasonCode2: string;

  @IsString()
  vendorReasonCode3: string;

  @IsString()
  vendorAuthorityCode1: string;

  @IsString()
  vendorAuthorityCode2: string;

  @IsString()
  vendorAuthorityCode3: string;

  @IsString()
  vendorRemark1: string;

  @IsString()
  vendorRemark2: string;

  @IsString()
  vendorRemark3: string;

  @IsString()
  vendorTradeProgram1: string;

  @IsString()
  vendorTradeProgram2: string;

  @IsString()
  vendorTradeProgram3: string;

  // Line items
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CadCommercialInvoiceLineDto)
  lines: CadCommercialInvoiceLineDto[];
}

export class CadDto {
  // Row 1
  @IsString()
  type: string;

  @IsString()
  wsType: string;

  @IsString()
  submissionDate: string;

  @IsString()
  transactionNumber: string;

  @IsString()
  portCode: string;

  @IsString()
  modeOfTransport: string;

  @IsString()
  releaseDate: string;

  @IsString()
  weight: string;

  @IsString()
  carrierCode: string;

  @IsString()
  preCarm: string;

  @IsString()
  rpp: string;

  // Row 2
  @IsString()
  importerBusinessNumber: string;

  @IsString()
  importerCompanyName: string;

  @IsString()
  importerAddress: string;

  @IsString()
  importerCity: string;

  @IsString()
  importerPhoneNumber: string;

  @IsEnum(OrganizationCustomsBroker)
  customsBroker: OrganizationCustomsBroker;

  @IsString()
  cargoControlNumber: string;

  @IsString()
  intent: string;

  @IsString()
  previousTransactionNumber: string;

  @IsString()
  acceptedDate: string;

  @IsString()
  originalTransactionNumber: string;

  @IsString()
  prevTransNoWhse: string;

  @IsString()
  pou: string;

  @IsString()
  newBusinessNumber: string;

  // Row 3
  @IsString()
  reasonCode1: string;

  @IsString()
  reasonCode2: string;

  @IsString()
  reasonCode3: string;

  @IsString()
  authorityCode1: string;

  @IsString()
  authorityCode2: string;

  @IsString()
  authorityCode3: string;

  @IsString()
  remark1: string;

  @IsString()
  remark2: string;

  @IsString()
  remark3: string;

  // Row 4
  @IsString()
  notes: string;

  @IsString()
  warehouseIn: string;

  @IsString()
  warehouseOut: string;

  @IsString()
  name: string;

  @IsString()
  today: string;

  // Totals
  @IsString()
  totalValueForDuty: string;

  @IsString()
  totalPstHst: string;

  @IsString()
  totalProvincialCannabisExciseDuty: string;

  @IsString()
  totalProvincialAlcoholTax: string;

  @IsString()
  totalProvincialTobaccoTax: string;

  @IsString()
  totalRelieved: string;

  @IsString()
  totalCustomsDuties: string;

  @IsString()
  totalExciseDuties: string;

  @IsString()
  totalExciseTax: string;

  @IsString()
  totalGst: string;

  @IsString()
  totalAntiDumping: string;

  @IsString()
  totalCountervailing: string;

  @IsString()
  totalSurtax: string;

  @IsString()
  totalSafeguard: string;

  @IsString()
  totalInterest: string;

  @IsString()
  totalTotalDutiesAndTaxes: string;

  // Commercial Invoices
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CadCommercialInvoiceDto)
  commercialInvoices: CadCommercialInvoiceDto[];
}
