import { Document } from "../entities";

/**
 * Cross-module email event DTOs
 *
 * These events are used for communication between modules and should be imported
 * from nest-modules to avoid tight coupling between individual app modules.
 */

class BaseEmailEvent {
  constructor({ emailId, organizationId }: { emailId: number; organizationId: number }) {
    this.emailId = emailId;
    this.organizationId = organizationId;
  }

  /** ID of the email saved in database */
  public readonly emailId: number;

  /** Organization ID of the email */
  public readonly organizationId: number;
}

/**
 * Event emitted when an email is saved and ready for processing
 * Used for cross-module communication between Email and Core-Agent modules
 */
export class EmailSavedEvent extends BaseEmailEvent {
  constructor({
    emailId,
    organizationId,
    gmailId,
    documents
  }: {
    emailId: number;
    organizationId: number;
    gmailId: string;
    documents: Array<Document>;
  }) {
    super({ emailId, organizationId });
    this.gmailId = gmailId;
    this.documents = documents;
  }

  /** Gmail ID of the email */
  public readonly gmailId: string;

  /** Extracted documents from the saved email */
  public readonly documents: Array<Document>;
}
