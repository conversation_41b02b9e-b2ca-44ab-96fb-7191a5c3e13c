import { ApiProperty } from "@nestjs/swagger";

class ExceptionMessage {
  @ApiProperty({
    oneOf: [{ type: "string" }, { type: "array", items: { type: "string" } }]
  })
  message: string[] | string;
}

export class BadRequestResponseDto extends ExceptionMessage {
  @ApiProperty({ default: "Bad Request" })
  error: string;

  @ApiProperty({ type: "integer", default: 400 })
  statusCode: number;
}

export class UnauthorizedResponseDto extends ExceptionMessage {
  @ApiProperty({ default: "Unauthorized" })
  error: string;

  @ApiProperty({ type: "integer", default: 401 })
  statusCode: number;
}

export class ForbiddenResponseDto extends ExceptionMessage {
  @ApiProperty({ default: "Forbidden" })
  error: string;

  @ApiProperty({ type: "integer", default: 403 })
  statusCode: number;
}

export class NotFoundResponseDto extends ExceptionMessage {
  @ApiProperty({ default: "Not Found" })
  error: string;

  @ApiProperty({ type: "integer", default: 404 })
  statusCode: number;
}

export class TooManyRequestsResponseDto extends ExceptionMessage {
  @ApiProperty({ default: "Too Many Requests" })
  error: string;

  @ApiProperty({ type: "integer", default: 429 })
  statusCode: number;
}

export class InternalServerErrorResponseDto extends ExceptionMessage {
  @ApiProperty({ default: "Internal Server Error" })
  error: string;

  @ApiProperty({ type: "integer", default: 500 })
  statusCode: number;
}

export class ConflictResponseDto extends ExceptionMessage {
  @ApiProperty({ default: "Conflict" })
  error: string;

  @ApiProperty({ type: "integer", default: 409 })
  statusCode: number;
}
