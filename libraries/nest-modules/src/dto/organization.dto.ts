import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import { OrganizationColumn, OrganizationCustomsBroker, OrganizationType } from "../types/organization.types";
import { IsBoolean, IsEnum, IsInt, IsNotEmpty, IsOptional, IsString, Min, MinLength } from "class-validator";
import { Transform, Type } from "class-transformer";
import { Organization } from "../entities/organization.entity";

export class GetOrganizationsDto extends GetManyDto {
  @ApiPropertyOptional({ enum: OrganizationColumn, default: OrganizationColumn.id })
  @IsOptional()
  @IsEnum(OrganizationColumn)
  sortBy?: OrganizationColumn;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ enum: OrganizationType })
  @IsOptional()
  @IsEnum(OrganizationType)
  organizationType?: OrganizationType;

  @ApiPropertyOptional({ enum: OrganizationCustomsBroker })
  @IsOptional()
  @IsEnum(OrganizationCustomsBroker)
  customsBroker?: OrganizationCustomsBroker;

  @ApiPropertyOptional({ type: "boolean" })
  @Transform(({ value }) =>
    typeof value === "string" && ["true", "false"].includes(value.trim().toLowerCase())
      ? value.trim().toLowerCase() === "true"
      : value
  )
  @IsOptional()
  @IsBoolean()
  skipPoaCheck?: boolean;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetOrganizationsResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [Organization] })
  organizations: Array<Organization>;
}

export class CreateOrganizationDto {
  @ApiProperty({ type: "string", minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  name: string;

  @ApiProperty({ enum: OrganizationType, default: OrganizationType.TRADING })
  @IsNotEmpty()
  @IsEnum(OrganizationType)
  organizationType: OrganizationType;

  @ApiProperty({ enum: OrganizationCustomsBroker, default: OrganizationCustomsBroker.CLARO })
  @IsNotEmpty()
  @IsEnum(OrganizationCustomsBroker)
  customsBroker: OrganizationCustomsBroker;

  @ApiPropertyOptional({ type: "boolean", default: false })
  @IsOptional()
  @IsBoolean()
  skipPoaCheck?: boolean;
}

export class EditOrganizationDto {
  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ enum: OrganizationType, default: OrganizationType.TRADING })
  @IsOptional()
  @IsEnum(OrganizationType)
  organizationType?: OrganizationType;

  @ApiPropertyOptional({ enum: OrganizationCustomsBroker, default: OrganizationCustomsBroker.CLARO })
  @IsOptional()
  @IsEnum(OrganizationCustomsBroker)
  customsBroker?: OrganizationCustomsBroker;

  @ApiPropertyOptional({ type: "boolean", default: false })
  @IsOptional()
  @IsBoolean()
  skipPoaCheck?: boolean;
}
