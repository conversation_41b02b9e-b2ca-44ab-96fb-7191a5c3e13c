import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { LoginMethod } from "../types/auth.types";
import { IsEmail, IsEnum, IsJWT, IsNotEmpty, IsString, ValidateIf } from "class-validator";
import { Organization, SimplifiedOrganization } from "../entities/organization.entity";
import { UserPermission } from "../types/user.types";

export class LoginUserDto {
  @ApiProperty({ enum: LoginMethod })
  @IsNotEmpty()
  @IsEnum(LoginMethod)
  loginMethod: LoginMethod;

  @ApiPropertyOptional({
    type: "string",
    format: "jwt",
    description: "Google ID Token Required if login method is Google SSO"
  })
  @ValidateIf((o) => o.loginMethod === LoginMethod.GOOGLE_SSO)
  @IsJWT()
  idToken?: string;

  @ApiPropertyOptional({ type: "string", format: "email", description: "Required if login method is email" })
  @ValidateIf((o) => o.loginMethod === LoginMethod.EMAIL)
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ type: "string", format: "email", description: "Required if login method is email" })
  @ValidateIf((o) => o.loginMethod === LoginMethod.EMAIL)
  @IsString()
  password?: string;
}

export class LogoutUserDto {
  @ApiProperty({ type: "string", format: "jwt", example: "refresh.token.jwt" })
  @IsNotEmpty()
  @IsJWT()
  refreshToken: string;
}

export class RefreshAccessTokenDto {
  @ApiProperty({ type: "string", format: "jwt", example: "refresh.token.jwt" })
  @IsNotEmpty()
  @IsJWT()
  refreshToken: string;
}

export class RefreshAccessTokenResponseDto {
  @ApiProperty({ type: "string", format: "jwt", example: "access.token.jwt" })
  accessToken: string;

  @ApiProperty({ type: "string", format: "date-time" })
  accessTokenExpiryDate: Date;

  @ApiProperty({ type: "string", format: "jwt", example: "refresh.token.jwt" })
  refreshToken: string;

  @ApiProperty({ type: "string", format: "date-time" })
  refreshTokenExpiryDate: Date;

  @ApiProperty()
  name: string;

  @ApiProperty({ type: "string", format: "email" })
  email: string;

  @ApiProperty({ type: () => SimplifiedOrganization })
  organization: Organization;

  @ApiProperty({ enum: UserPermission })
  permission: UserPermission;
}

export class LoginUserResponseDto extends RefreshAccessTokenResponseDto {}

export class LogoutUserResponseDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  userId: number;
}
