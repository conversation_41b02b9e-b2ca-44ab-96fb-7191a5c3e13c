import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  Matches,
  Min,
  MinLength
} from "class-validator";
import { CanadaTariff } from "../entities";
import { CanadaTariffColumn, CanadaTariffType, UnitOfMeasure } from "../types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class GetCanadaTariffsDto extends GetManyDto {
  @ApiPropertyOptional({
    enum: CanadaTariffColumn,
    default: CanadaTariffColumn.hsCode
  })
  @IsOptional()
  @IsEnum(CanadaTariffColumn)
  sortBy?: CanadaTariffColumn;

  @ApiPropertyOptional({ enum: CanadaTariffType, description: "Number of digits in HS code" })
  @IsOptional()
  @IsEnum(CanadaTariffType)
  type?: CanadaTariffType;

  @ApiPropertyOptional({
    type: "string",
    minLength: 1,
    description: "Tariff Treatments separated by comma",
    example: "aut,nzt,ccct"
  })
  @Transform(({ value }) => (typeof value === "string" ? value.split(",").map((s) => s.trim()) : value))
  @IsOptional()
  @IsString({ each: true })
  @MinLength(1, { each: true })
  treatments?: Array<string>;

  @ApiPropertyOptional({ minLength: 1, pattern: `^\\d+$`, description: "HS code prefix" })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @Matches(/^\d+$/, { message: "HS code must be numerical string" })
  hsCode?: string;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @Transform(({ value }) => (typeof value === "string" && value.length > 0 ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  effectiveDateFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @Transform(({ value }) => (typeof value === "string" && value.length > 0 ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  effectiveDateTo?: Date;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @Transform(({ value }) => (typeof value === "string" && value.length > 0 ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  expiryDateFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @Transform(({ value }) => (typeof value === "string" && value.length > 0 ? new Date(value) : value))
  @IsOptional()
  @IsDate()
  expiryDateTo?: Date;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  description?: string;

  @ApiPropertyOptional({ enum: UnitOfMeasure })
  @IsOptional()
  @IsEnum(UnitOfMeasure)
  uom?: UnitOfMeasure;

  // /**
  //  * @deprecated MFN treatment should be stored in `treatments` object field
  //  */
  // @ApiPropertyOptional({ minLength: 1, deprecated: true })
  // @IsOptional()
  // @IsString()
  // @MinLength(1)
  // mfn?: string;

  // /**
  //  * @deprecated General tariff should be stored in `treatments` object field
  //  */
  // @ApiPropertyOptional({ minLength: 1, deprecated: true })
  // @IsOptional()
  // @IsString()
  // @MinLength(1)
  // generalTariff?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetCanadaTariffsResponseDto extends GetManyResponseDto {
  // @ApiProperty({ type: 'integer', minimum: 0, default: 0 })
  // skip: number;

  // @ApiProperty({ type: 'integer', minimum: 0, default: 10 })
  // limit: number;

  @ApiProperty({ type: () => [CanadaTariff] })
  data: Array<CanadaTariff>;
}

export class SyncCanadaTariffsDto {
  @ApiPropertyOptional({
    type: "boolean",
    default: false,
    description: "If true, will perform full sync for all tariffs and removing any tariffs that are not found"
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) =>
    typeof value === "string" && ["true", "false"].includes(value.trim().toLowerCase())
      ? value.trim().toLowerCase() === "true"
      : value
  )
  fullSync?: boolean;
}
