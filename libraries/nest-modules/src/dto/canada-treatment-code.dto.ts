import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  ArrayUnique,
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
  MinLength,
  ValidateNested
} from "class-validator";
import { CanadaTreatmentCode } from "../entities";
import { CanadaTreatmentCodeColumn } from "../types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class GetCanadaTreatmentCodesDto extends GetManyDto {
  @ApiPropertyOptional({ enum: CanadaTreatmentCodeColumn })
  @IsOptional()
  @IsEnum(CanadaTreatmentCodeColumn)
  sortBy?: CanadaTreatmentCodeColumn;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  code?: number;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  abbreviation?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetCanadaTreatmentCodesResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: [CanadaTreatmentCode] })
  data: Array<CanadaTreatmentCode>;
}

export class CreateCanadaTreatmentCodeDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  code: number;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  name: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  abbreviation: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1, isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  countryIds?: Array<number>;
}

export class EditCanadaTreatmentCodeDto {
  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  code?: number;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  abbreviation?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1, isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  countryIds?: Array<number>;
}

export class EditCanadaTreatmentCodeWithIdDto extends EditCanadaTreatmentCodeDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  id: number;
}

export class BatchUpdateCanadaTreatmentCodesDto {
  @ApiProperty({ type: [CreateCanadaTreatmentCodeDto] })
  @Type(() => CreateCanadaTreatmentCodeDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  create?: Array<CreateCanadaTreatmentCodeDto>;

  @ApiProperty({ type: [EditCanadaTreatmentCodeWithIdDto] })
  @Type(() => EditCanadaTreatmentCodeWithIdDto)
  @IsOptional()
  @IsArray()
  @ArrayUnique((o) => o.id)
  @ValidateNested({ each: true })
  edit?: Array<EditCanadaTreatmentCodeWithIdDto>;

  @ApiProperty({ type: "integer", minimum: 1, isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  delete?: Array<number>;
}

export class BatchUpdateCanadaTreatmentCodesResponseDto {
  @ApiProperty({ type: [CanadaTreatmentCode] })
  data: Array<CanadaTreatmentCode>;
}
