import { ApiProperty } from "@nestjs/swagger";
import { IsDateString, IsN<PERSON>ber, IsOptional, IsString } from "class-validator";

export class CandataExchangeRateDto {
  @ApiProperty({ type: "string", description: "Currency Code", example: "USD" })
  @IsString()
  currencyCode: string;

  @ApiProperty({ type: "string", description: "Exchange Date", example: "2025-12-30" })
  @IsString()
  @IsOptional()
  date?: string;
}

export class CandataExchangeRateResponseDto {
  @ApiProperty({ type: "number", description: "Rate", example: 1.23 })
  @IsNumber()
  rate: number;

  @ApiProperty({ type: "number", description: "ID", example: 1032169666 })
  @IsNumber()
  id: number;

  @ApiProperty({ type: "string", description: "As Of", example: "2025-01-01" })
  @IsDateString()
  asOf: Date;

  @ApiProperty({ type: "string", description: "Expires", example: "2025-01-01" })
  @IsDateString()
  expires: Date;

  @ApiProperty({ type: "string", description: "Currency Code", example: "USD" })
  @IsString()
  currencyCode: string;
}
