import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";
import { IsArray, IsBoolean, IsInt, IsNotEmpty, IsOptional, IsString, Min } from "class-validator";
import { FieldValidationError } from "../types";

export class AggregateDocumentDto {
  @ApiProperty({ type: [Number] })
  @IsInt({ each: true })
  @Min(1, { each: true })
  @IsArray()
  @Transform(({ value }) => (typeof value === "string" ? [value] : value).map((v) => parseInt(v, 10)))
  ids: number[];

  @ApiPropertyOptional({ type: [Bo<PERSON>an] })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === "true")
  dryRun?: boolean;
}

export class UpdateDocumentShipmentDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  shipmentId: number;
}

export enum ProcessDocumentModel {
  GPT_4O = "gpt-4o",
  O3_MINI = "o3-mini"
}

export class ProcessDocumentDto {
  @ApiProperty({ enum: ProcessDocumentModel })
  @IsNotEmpty()
  @IsString()
  model: ProcessDocumentModel;
}

export class AggregateDocumentValidationErrorResponseDto {
  @ApiProperty({
    description: "The validation errors",
    type: [FieldValidationError]
  })
  validationErrors: FieldValidationError[];
}
