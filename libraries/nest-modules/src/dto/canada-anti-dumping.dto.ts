import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  ArrayUnique,
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
  MinLength,
  ValidateNested
} from "class-validator";
import { CanadaAntiDumping } from "../entities";
import { CanadaAntiDumpingColumn } from "../types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class GetCanadaAntiDumpingsDto extends GetManyDto {
  @ApiPropertyOptional({ enum: CanadaAntiDumpingColumn })
  @IsOptional()
  @IsEnum(CanadaAntiDumpingColumn)
  sortBy?: CanadaAntiDumpingColumn;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  code?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  case?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  dumpingCountry?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  subsidyCountry?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  caseType?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  productDefinition?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  exclusion?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  hsCodes?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  antiDumpingDutyLiability?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  countervailingDutyLiability?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetCanadaAntiDumpingsResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: [CanadaAntiDumping] })
  data: Array<CanadaAntiDumping>;
}

export class CreateCanadaAntiDumpingDto {
  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  code: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  case: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  dumpingCountry: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  subsidyCountry: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  caseType: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  productDefinition: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  exclusion: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  hsCodes: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  antiDumpingDutyLiability: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  countervailingDutyLiability: string;
}

export class EditCanadaAntiDumpingDto {
  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  code?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  case?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  dumpingCountry?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  subsidyCountry?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  caseType?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  productDefinition?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  exclusion?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  hsCodes?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  antiDumpingDutyLiability?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  countervailingDutyLiability?: string;
}

export class EditCanadaAntiDumpingWithIdDto extends EditCanadaAntiDumpingDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  id: number;
}

export class BatchUpdateCanadaAntiDumpingsDto {
  @ApiProperty({ type: [CreateCanadaAntiDumpingDto] })
  @Type(() => CreateCanadaAntiDumpingDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  create?: Array<CreateCanadaAntiDumpingDto>;

  @ApiProperty({ type: [EditCanadaAntiDumpingWithIdDto] })
  @Type(() => EditCanadaAntiDumpingWithIdDto)
  @IsOptional()
  @IsArray()
  @ArrayUnique((o) => o.id)
  @ValidateNested({ each: true })
  edit?: Array<EditCanadaAntiDumpingWithIdDto>;

  @ApiProperty({ type: "integer", minimum: 1, isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  delete?: Array<number>;
}

export class BatchUpdateCanadaAntiDumpingsResponseDto {
  @ApiProperty({ type: [CanadaAntiDumping] })
  data: Array<CanadaAntiDumping>;
}
