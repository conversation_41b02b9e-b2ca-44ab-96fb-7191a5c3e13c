import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsString, MaxLength, MinLength } from "class-validator";
import { CanadaSubLocation } from "../entities";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class GetCanadaSubLocationsDto extends GetManyDto {
  @ApiPropertyOptional({
    type: "string",
    description: "Canada sub location code"
  })
  @MinLength(1)
  @MaxLength(10)
  @IsOptional()
  @IsString()
  code?: string;

  @ApiPropertyOptional({ type: "string", description: "Port code" })
  @IsOptional()
  @IsString()
  portCode?: string;
}

export class GetCanadaSubLocationsResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [CanadaSubLocation] })
  data: Array<CanadaSubLocation>;
}
