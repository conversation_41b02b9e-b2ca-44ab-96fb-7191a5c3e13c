import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsBoolean, IsEnum, IsInt, IsOptional, Min } from "class-validator";
import { CertificateOfOrigin } from "../entities";
import { CertificateOfOriginColumn } from "../types/certificate-of-origin.types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class GetCertificatesOfOriginDto extends GetManyDto {
  @ApiPropertyOptional({
    enum: CertificateOfOriginColumn,
    default: CertificateOfOriginColumn.id
  })
  @IsOptional()
  @IsEnum(CertificateOfOriginColumn)
  sortBy?: CertificateOfOriginColumn;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  exporterId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  importerId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  producerId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  countryOfOriginId?: number;

  @ApiPropertyOptional({ type: "boolean", description: "Filter by valid or invalid certificates" })
  @Transform(({ value }) =>
    typeof value === "string" && ["true", "false"].includes(value.trim().toLowerCase())
      ? value.trim().toLowerCase() === "true"
      : value
  )
  @IsOptional()
  @IsBoolean()
  isValid?: boolean;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "For basic user and organization admin, Organization ID will be auto-set according to current user's organization"
  })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  organizationId?: number;
}

export class GetCertificatesOfOriginResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [CertificateOfOrigin] })
  certificates: Array<CertificateOfOrigin>;
}
