import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import { CanadaVfdCode } from "../entities";
import {
  IsOptional,
  IsEnum,
  IsString,
  MinLength,
  IsInt,
  Min,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  ArrayUnique
} from "class-validator";
import { CanadaVfdCodeColumn } from "../types";
import { Type } from "class-transformer";

export class GetCanadaVfdCodesDto extends GetManyDto {
  @ApiPropertyOptional({ enum: CanadaVfdCodeColumn })
  @IsOptional()
  @IsEnum(CanadaVfdCodeColumn)
  sortBy?: CanadaVfdCodeColumn;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  code?: number;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  explanation?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetCanadaVfdCodesResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: [CanadaVfdCode] })
  data: Array<CanadaVfdCode>;
}

export class CreateCanadaVfdCodeDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  code: number;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  explanation: string;
}

export class EditCanadaVfdCodeDto {
  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  code?: number;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  explanation?: string;
}

export class EditCanadaVfdCodeWithIdDto extends EditCanadaVfdCodeDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  id: number;
}

export class BatchUpdateCanadaVfdCodesDto {
  @ApiProperty({ type: [CreateCanadaVfdCodeDto] })
  @Type(() => CreateCanadaVfdCodeDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  create?: Array<CreateCanadaVfdCodeDto>;

  @ApiProperty({ type: [EditCanadaVfdCodeWithIdDto] })
  @Type(() => EditCanadaVfdCodeWithIdDto)
  @IsOptional()
  @IsArray()
  @ArrayUnique((o) => o.id)
  @ValidateNested({ each: true })
  edit?: Array<EditCanadaVfdCodeWithIdDto>;

  @ApiProperty({ type: "integer", minimum: 1, isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  delete?: Array<number>;
}

export class BatchUpdateCanadaVfdCodesResponseDto {
  @ApiProperty({ type: [CanadaVfdCode] })
  data: Array<CanadaVfdCode>;
}
