import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import { CanadaGstExemptCode } from "../entities";
import {
  IsOptional,
  IsEnum,
  IsString,
  MinLength,
  IsInt,
  Min,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  ArrayUnique
} from "class-validator";
import { CanadaGstExemptCodeColumn } from "../types";
import { Type } from "class-transformer";

export class GetCanadaGstExemptCodesDto extends GetManyDto {
  @ApiPropertyOptional({ enum: CanadaGstExemptCodeColumn })
  @IsOptional()
  @IsEnum(CanadaGstExemptCodeColumn)
  sortBy?: CanadaGstExemptCodeColumn;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  code?: number;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  explanation?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetCanadaGstExemptCodesResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: [CanadaGstExemptCode] })
  data: Array<CanadaGstExemptCode>;
}

export class CreateCanadaGstExemptCodeDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  code: number;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  explanation: string;
}

export class EditCanadaGstExemptCodeDto {
  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  code?: number;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  explanation?: string;
}

export class EditCanadaGstExemptCodeWithIdDto extends EditCanadaGstExemptCodeDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  id: number;
}

export class BatchUpdateCanadaGstExemptCodesDto {
  @ApiProperty({ type: [CreateCanadaGstExemptCodeDto] })
  @Type(() => CreateCanadaGstExemptCodeDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  create?: Array<CreateCanadaGstExemptCodeDto>;

  @ApiProperty({ type: [EditCanadaGstExemptCodeWithIdDto] })
  @Type(() => EditCanadaGstExemptCodeWithIdDto)
  @IsOptional()
  @IsArray()
  @ArrayUnique((o) => o.id)
  @ValidateNested({ each: true })
  edit?: Array<EditCanadaGstExemptCodeWithIdDto>;

  @ApiProperty({ type: "integer", minimum: 1, isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  delete?: Array<number>;
}

export class BatchUpdateCanadaGstExemptCodesResponseDto {
  @ApiProperty({ type: [CanadaGstExemptCode] })
  data: Array<CanadaGstExemptCode>;
}
