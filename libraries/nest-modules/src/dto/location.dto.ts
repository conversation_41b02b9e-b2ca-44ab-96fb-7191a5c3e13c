import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsEnum, IsInt, IsNotEmpty, IsOptional, IsString, IsTimeZone, Min, MinLength } from "class-validator";
import { Location } from "../entities/location.entity";
import { LocationColumn, LocationType } from "../types/location.types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class GetLocationsDto extends GetManyDto {
  @ApiPropertyOptional({ enum: LocationColumn, default: LocationColumn.id })
  @IsOptional()
  @IsEnum(LocationColumn)
  sortBy?: LocationColumn;

  @ApiPropertyOptional({ enum: LocationType })
  @IsOptional()
  @IsEnum(LocationType)
  locationType?: LocationType;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  unit?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  streetAddress?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  city?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  state?: string;

  /** @deprecated Use `postalCode` instead */
  @ApiPropertyOptional({ deprecated: true, minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  zipCode?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  postalCode?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  timezone?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  countryId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetLocationsResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [Location] })
  locations: Array<Location>;
}

export class CreateLocationDto {
  @ApiPropertyOptional({ enum: LocationType })
  @IsOptional()
  @IsEnum(LocationType)
  locationType?: LocationType;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  streetAddress?: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  city?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  state?: string;

  /** @deprecated Use `postalCode` instead */
  @ApiPropertyOptional({ deprecated: true, minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  zipCode?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  postalCode?: string;

  @ApiPropertyOptional({ minLength: 1, default: "America/Toronto" })
  @IsOptional()
  @IsTimeZone()
  @MinLength(1)
  timezone?: string;

  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  countryId: number;
}

export class EditLocationDto {
  @ApiPropertyOptional({ enum: LocationType })
  @IsOptional()
  @IsEnum(LocationType)
  locationType?: LocationType;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  streetAddress?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  city?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  state?: string;

  /** @deprecated Use `postalCode` instead */
  @ApiPropertyOptional({ deprecated: true, minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  zipCode?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  postalCode?: string;

  @ApiPropertyOptional({ minLength: 1, default: "America/Toronto" })
  @IsOptional()
  @IsTimeZone()
  @MinLength(1)
  timezone?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  countryId?: number;
}
