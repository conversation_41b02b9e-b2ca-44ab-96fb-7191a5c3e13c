import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  ArrayUnique,
  IsArray,
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
  <PERSON><PERSON>ength,
  ValidateNested
} from "class-validator";
import { DocumentField } from "../entities/document-field.entity";
import { DocumentFieldDataType } from "../types";
import { DocumentFieldColumn } from "../types/document-field.types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class GetDocumentFieldsDto extends GetManyDto {
  @ApiPropertyOptional({ enum: DocumentFieldColumn, default: DocumentFieldColumn.id })
  @IsOptional()
  @IsEnum(DocumentFieldColumn)
  sortBy?: DocumentFieldColumn;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ enum: DocumentFieldDataType })
  @IsOptional()
  @IsEnum(DocumentFieldDataType)
  dataType?: DocumentFieldDataType;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  documentId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetDocumentFieldsResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [DocumentField] })
  documentFields: Array<DocumentField>;
}

export class CreateDocumentFieldDto {
  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  name: string;

  @ApiProperty({ enum: DocumentFieldDataType })
  @IsNotEmpty()
  @IsEnum(DocumentFieldDataType)
  dataType: DocumentFieldDataType;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  value: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  locked?: boolean;
}

export class EditDocumentFieldDto {
  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ enum: DocumentFieldDataType })
  @IsOptional()
  @IsEnum(DocumentFieldDataType)
  dataType?: DocumentFieldDataType;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  value?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  locked?: boolean;
}

export class EditDocumentFieldWithIdDto extends EditDocumentFieldDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  id: number;
}

export class BatchUpdateDocumentFieldsDto {
  @ApiProperty({ type: [CreateDocumentFieldDto] })
  @Type(() => CreateDocumentFieldDto)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  create?: Array<CreateDocumentFieldDto>;

  @ApiProperty({ type: [EditDocumentFieldWithIdDto] })
  @Type(() => EditDocumentFieldWithIdDto)
  @IsOptional()
  @IsArray()
  @ArrayUnique((o) => o.id)
  @ValidateNested({ each: true })
  edit?: Array<EditDocumentFieldWithIdDto>;

  @ApiProperty({ type: "integer", minimum: 1, isArray: true })
  @IsOptional()
  @IsArray()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  delete?: Array<number>;
}

export class BatchUpdateDocumentFieldsResponseDto {
  @ApiProperty({ type: [DocumentField] })
  documentFields: Array<DocumentField>;
}
