import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import {
  IsEmail,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  Min,
  Min<PERSON>ength
} from "class-validator";
import { UserColumn, UserPermission } from "../types/user.types";
import { Type } from "class-transformer";
import { User } from "../entities/user.entity";

export class GetUsersDto extends GetManyDto {
  @ApiPropertyOptional({ enum: UserColumn, default: UserColumn.id })
  @IsOptional()
  @IsEnum(UserColumn)
  sortBy?: UserColumn;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  email?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  googleUserId?: string;

  @ApiPropertyOptional({ enum: UserPermission })
  @IsOptional()
  @IsEnum(UserPermission)
  permission?: UserPermission;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "For basic user and organization admin, Organization ID will be auto-set according to current user's organization"
  })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  organizationId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetUsersResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [User] })
  users: Array<User>;
}

export class CreateUserDto {
  @ApiProperty({ type: "string", format: "email", minLength: 1 })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({ type: "string", minLength: 1 })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  googleUserId?: string;

  @ApiPropertyOptional({ enum: UserPermission, default: UserPermission.BASIC })
  @IsOptional()
  @IsEnum(UserPermission)
  permission?: UserPermission;

  @ApiProperty({
    type: "integer",
    minimum: 1,
    description:
      "For organization admin, Organization ID will be auto-set according to current user's organization"
  })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  organizationId: number;

  @ApiPropertyOptional({ type: "string", format: "password", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  password?: string;
}

export class EditUserDto {
  @ApiPropertyOptional({ type: "string", format: "email", minLength: 1 })
  @IsOptional()
  @IsEmail()
  @MinLength(1)
  email?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  googleUserId?: string;

  @ApiPropertyOptional({ enum: UserPermission, default: UserPermission.BASIC })
  @IsOptional()
  @IsEnum(UserPermission)
  permission?: UserPermission;

  @ApiPropertyOptional({ type: "string", format: "password", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  password?: string;
}

export class SendResetPasswordEmailDto {
  @ApiProperty({ type: "string", format: "email", minLength: 1 })
  @IsNotEmpty()
  @IsEmail()
  @MinLength(1)
  email: string;
}

export class SendResetPasswordEmailResponseDto {
  @ApiProperty({ type: "string", example: "If the email exists, a reset link has been sent." })
  message: string;
}

export class ResetUserPasswordDto {
  @ApiProperty({
    type: "string",
    minLength: 64,
    maxLength: 64,
    example: "f3a9d8e5b6c4a2f1e0d9c8b7a6f5e4d3c2b1a09f8e7d6c5b4a39281716151413"
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(64)
  @MaxLength(64)
  token: string;

  @ApiProperty({ type: "string", format: "password", minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  password: string;
}

export class ResetUserPasswordResponseDto {
  @ApiProperty({ type: "string", example: "Password reset successfully" })
  message: string;
}
