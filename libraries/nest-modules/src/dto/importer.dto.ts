import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
  IsArray,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
  MinLength,
  ValidateIf
} from "class-validator";
import { Importer } from "../entities/importer.entity";
import { ImporterColumn, ImporterStatus } from "../types/importer.types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import { CarmStatus } from "../types/carm.types";

export class GetImportersDto extends GetManyDto {
  @ApiPropertyOptional({ enum: ImporterColumn, default: ImporterColumn.id })
  @IsOptional()
  @IsEnum(ImporterColumn)
  sortBy?: ImporterColumn;

  @ApiPropertyOptional({ enum: ImporterStatus })
  @IsOptional()
  @IsEnum(ImporterStatus)
  status?: ImporterStatus;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  companyName?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  businessNumber?: string;

  @ApiPropertyOptional({
    minLength: 1
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  address?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  city?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  postalCode?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  state?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  phoneNumber?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  fax?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  email?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  officerNameAndTitle?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  docusignEnvelopeId?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  rejectReason?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  candataCustomerNumber?: string;

  @ApiPropertyOptional({
    type: "string",
    minLength: 1,
    description: "Email address to receive customer emails from"
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  receiveEmail?: string;

  // TODO: Add whitelist emails

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  countryId?: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "For basic user and organization admin, Organization ID will be auto-set according to current user's organization"
  })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  organizationId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetImportersResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [Importer] })
  importers: Array<Importer>;
}

export class UpdateImporterWhitelistEmailsDto {
  @ApiProperty({ type: "string", format: "email", isArray: true })
  @IsNotEmpty()
  @IsArray()
  @IsEmail({}, { each: true })
  whitelistEmails: Array<string>;
}

export class CreateImporterDto {
  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  companyName: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  businessNumber: string;

  @ApiProperty({
    minLength: 1,
    description: "Address is limited to 70 characters, any additional characters will be truncated"
  })
  @IsNotEmpty()
  @IsString()
  @Transform(({ value }) => value?.trim().substring(0, 70) || null)
  address: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  city: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  postalCode: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  state: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  phoneNumber: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  fax?: string;

  @ApiProperty({ type: "string", format: "email", minLength: 1 })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  officerNameAndTitle: string;

  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  countryId: number;

  @ApiPropertyOptional({ enum: CarmStatus })
  @IsOptional()
  @IsEnum(CarmStatus)
  carmStatus?: CarmStatus;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  carmApiKey?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  candataCustomerNumber?: string;
}

export class EditImporterDto {
  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  companyName?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  businessNumber?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  address?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  city?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  postalCode?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  state?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  phoneNumber?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  fax?: string;

  @ApiPropertyOptional({ type: "string", format: "email", minLength: 1 })
  @IsOptional()
  @IsEmail()
  @MinLength(1)
  email?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  officerNameAndTitle?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  countryId?: number;

  @ApiPropertyOptional({ enum: CarmStatus })
  @IsOptional()
  @IsEnum(CarmStatus)
  carmStatus?: CarmStatus;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  carmApiKey?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  candataCustomerNumber?: string;
}

export class SendPOASignatureRequestEmailResponseDto {
  @ApiProperty()
  envelopeId: string;

  @ApiProperty({ type: () => Importer })
  importer: Importer;
}

export class VerifyImporterDto {
  @ApiProperty({ type: "boolean" })
  @IsNotEmpty()
  @IsBoolean()
  isVerified: boolean;

  @ApiPropertyOptional({
    minLength: 1,
    description: "Required if isVerified is false"
  })
  @ValidateIf((o) => !o.isVerified)
  @IsString()
  @MinLength(1)
  rejectReason?: string;
}

export class UpdateImporterStatusDto {
  @ApiProperty({ enum: ImporterStatus })
  @IsNotEmpty()
  @IsEnum(ImporterStatus)
  status: ImporterStatus;
}
