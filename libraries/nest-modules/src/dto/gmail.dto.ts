import { ApiProperty, ApiPropertyOptional, OmitType } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import { IsBoolean, IsDate, IsEnum, IsInt, IsOptional, IsString, Matches, MinLength } from "class-validator";
import { SimplifiedGmailToken } from "../entities";
import { GmailTokenSortBy } from "../types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import { BaseGetAuthorizationUriResponseDto } from "./oauth.dto";

export class GetGmailAuthorizationUriResponseDto extends BaseGetAuthorizationUriResponseDto {}

export class GetGmailTokensDto extends OmitType(GetManyDto, [
  "createDateFrom",
  "createDateTo",
  "lastEditDateFrom",
  "lastEditDateTo"
]) {
  @ApiPropertyOptional({
    enum: GmailTokenSortBy,
    default: GmailTokenSortBy.email
  })
  @IsOptional()
  @IsEnum(GmailTokenSortBy)
  sortBy?: GmailTokenSortBy;

  @ApiPropertyOptional({ type: "boolean" })
  @Transform(({ value }) =>
    typeof value === "string" && ["true", "false"].includes(value.trim().toLowerCase())
      ? value.trim().toLowerCase() === "true"
      : value
  )
  @IsOptional()
  @IsBoolean()
  isDefaultMailbox?: boolean;

  @ApiPropertyOptional({ type: "string", minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  email?: string;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type(() => Date)
  @IsOptional()
  @IsDate()
  accessTokenExpiryDateFrom?: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Type(() => Date)
  @IsOptional()
  @IsDate()
  accessTokenExpiryDateTo?: Date;

  @ApiPropertyOptional({ type: "string", pattern: "^\\d+$" })
  @IsOptional()
  @IsString()
  @Matches(/^\d+$/)
  lastSyncedHistoryId?: string;
}

export class GetGmailTokensResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [SimplifiedGmailToken] })
  data: SimplifiedGmailToken[];
}
