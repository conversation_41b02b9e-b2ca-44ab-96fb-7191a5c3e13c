import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsArray, IsEnum, IsOptional, IsString } from "class-validator";
import { SearchContext } from "../types/global-search.types";
import { Transform } from "class-transformer";

export class GlobalSearchParamsDto {
  @ApiProperty({ type: "string", description: "Search term", example: "abc123" })
  @IsString()
  term: string;

  @ApiPropertyOptional({
    enum: SearchContext,
    isArray: true,
    description: "List of contexts to search",
    example: ["product", "shipment"]
  })
  @IsOptional()
  @IsArray()
  @IsEnum(SearchContext, { each: true })
  @Transform(({ value }) => {
    if (typeof value === "string") {
      return value
        .split(",")
        .map((v) => v.trim())
        .filter(Boolean);
    }
    return value;
  })
  contexts?: SearchContext[];
}

export class GlobalSearchResponseDto {
  @ApiProperty({ enum: SearchContext, description: "Context", example: "product" })
  @IsEnum(SearchContext)
  context: SearchContext;

  @ApiProperty({ type: "string", description: "Item ID", example: "123" })
  @IsString()
  id: string;

  @ApiProperty({ type: "string", description: "Title", example: "abc123" })
  @IsString()
  title: string;

  @ApiPropertyOptional({ type: "string", description: "Subtitle", example: "abc123 - 1234567890" })
  @IsOptional()
  @IsString()
  subtitle?: string;

  @ApiPropertyOptional({ type: "string", description: "Create Date", example: "2025-01-01" })
  @IsOptional()
  @IsString()
  createDate?: string;

  @ApiPropertyOptional()
  threadId?: string;
}
