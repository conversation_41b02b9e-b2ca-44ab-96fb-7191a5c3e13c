import { ApiProperty, ApiPropertyOptional, getSchemaPath } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
  ArrayNotEmpty,
  ArrayUnique,
  IsArray,
  IsBoolean,
  IsDate,
  isDateString,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Max,
  Min,
  MinLength,
  ValidateNested
} from "class-validator";
import {
  CanadaAntiDumping,
  CanadaExciseTaxCode,
  CanadaGstExemptCode,
  CanadaOgd,
  CanadaSimaCode,
  CanadaTariff,
  CanadaTreatmentCode,
  CanadaVfdCode,
  CertificateOfOrigin,
  MatchingRule,
  OgdFiling,
  Product,
  SimaFiling
} from "../entities";
import {
  MatchingRuleColumn,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleOperator,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus
} from "../types/matching-rule.types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import { CreateMatchingConditionDto } from "./matching-condition.dto";
import moment from "moment-timezone";
import { IsDateOnlyString } from "../decorators";

export class GetMatchingRulesDto extends GetManyDto {
  @ApiPropertyOptional({
    enum: MatchingRuleColumn,
    default: MatchingRuleColumn.id
  })
  @IsOptional()
  @IsEnum(MatchingRuleColumn)
  sortBy?: MatchingRuleColumn;

  @ApiPropertyOptional({ type: "boolean" })
  @Transform(({ value }) =>
    typeof value === "string" && ["true", "false"].includes(value.trim().toLowerCase())
      ? value.trim().toLowerCase() === "true"
      : value
  )
  @IsOptional()
  @IsBoolean()
  isGlobal?: boolean;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description: "Destination table must also be specified if this is provided."
  })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  destinationId?: number;

  @ApiPropertyOptional({ enum: MatchingRuleStatus })
  @IsOptional()
  @IsEnum(MatchingRuleStatus)
  status?: MatchingRuleStatus;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 0,
    maximum: 10,
    description: "Larger number means higher priority"
  })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(10)
  priority?: number;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ enum: MatchingRuleSourceDatabaseTable })
  @IsOptional()
  @IsEnum(MatchingRuleSourceDatabaseTable)
  sourceTable?: MatchingRuleSourceDatabaseTable;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  sourceId?: number;

  @ApiPropertyOptional({ enum: MatchingRuleDestinationDatabaseTable })
  @IsOptional()
  @IsEnum(MatchingRuleDestinationDatabaseTable)
  destinationTable?: MatchingRuleDestinationDatabaseTable;

  @ApiPropertyOptional({ enum: MatchingRuleOperator })
  @IsOptional()
  @IsEnum(MatchingRuleOperator)
  operator?: MatchingRuleOperator;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @IsOptional()
  @IsDateOnlyString()
  expiryDateFrom?: string | null;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @IsOptional()
  @IsDateOnlyString()
  expiryDateTo?: string | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "Show only matching rules applied for this organization. If isGlobal is true, this will be ignored."
  })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  organizationId?: number;
}

export class GetMatchingRulesResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: [MatchingRule] })
  matchingRules: Array<MatchingRule>;
}

export class CreateMatchingRuleWithoutSourceDto {
  @ApiPropertyOptional({
    type: "integer",
    minimum: 0,
    maximum: 10,
    default: 1,
    description: "Larger number means higher priority"
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(10)
  priority?: number;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  name: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ enum: MatchingRuleDestinationDatabaseTable })
  @IsNotEmpty()
  @IsEnum(MatchingRuleDestinationDatabaseTable)
  destinationTable: MatchingRuleDestinationDatabaseTable;

  @ApiPropertyOptional({ enum: MatchingRuleOperator, default: MatchingRuleOperator.AND })
  @IsOptional()
  @IsEnum(MatchingRuleOperator)
  operator?: MatchingRuleOperator;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @IsOptional()
  @IsDateOnlyString()
  expiryDate?: string | null;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "This option is only available for backoffice admin. If provided, the rule will be created for the specified organization."
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  organizationId?: number | null;
}

export class CreateMatchingRuleDto extends CreateMatchingRuleWithoutSourceDto {
  @ApiProperty({ enum: MatchingRuleSourceDatabaseTable })
  @IsNotEmpty()
  @IsEnum(MatchingRuleSourceDatabaseTable)
  sourceTable: MatchingRuleSourceDatabaseTable;

  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  sourceId: number;
}

export class EditMatchingRuleWithoutSourceDto {
  @ApiPropertyOptional({
    type: "integer",
    minimum: 0,
    maximum: 10,
    default: 1,
    description: "Larger number means higher priority"
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(10)
  priority?: number;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ enum: MatchingRuleDestinationDatabaseTable })
  @IsOptional()
  @IsEnum(MatchingRuleDestinationDatabaseTable)
  destinationTable?: MatchingRuleDestinationDatabaseTable;

  @ApiPropertyOptional({ enum: MatchingRuleOperator, default: MatchingRuleOperator.AND })
  @IsOptional()
  @IsEnum(MatchingRuleOperator)
  operator?: MatchingRuleOperator;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @IsOptional()
  @IsDateOnlyString()
  expiryDate?: string | null;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "This option is only available for backoffice admin. If provided, the rule will be created for the specified organization."
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  organizationId?: number | null;
}

export class EditMatchingRuleDto extends EditMatchingRuleWithoutSourceDto {
  @ApiPropertyOptional({ enum: MatchingRuleSourceDatabaseTable })
  @IsOptional()
  @IsEnum(MatchingRuleSourceDatabaseTable)
  sourceTable?: MatchingRuleSourceDatabaseTable;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  sourceId?: number;
}

export class UpdateMatchingRuleStatusDto {
  @ApiProperty({ enum: MatchingRuleStatus })
  @IsNotEmpty()
  @IsEnum(MatchingRuleStatus)
  status: MatchingRuleStatus;
}

class BaseQueryMatchingResultsDto {
  @ApiProperty({ enum: MatchingRuleDestinationDatabaseTable })
  @IsNotEmpty()
  @IsEnum(MatchingRuleDestinationDatabaseTable)
  destinationTable: MatchingRuleDestinationDatabaseTable;

  @ApiProperty({ enum: MatchingRuleSourceDatabaseTable, isArray: true })
  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @IsEnum(MatchingRuleSourceDatabaseTable, { each: true })
  sourceTables: Array<MatchingRuleSourceDatabaseTable>;
}

export class QueryMultipleMatchingResultsDto extends BaseQueryMatchingResultsDto {
  @ApiProperty({ type: "integer", minimum: 1, isArray: true, minItems: 1 })
  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @IsInt({ each: true })
  @Min(1, { each: true })
  destinationIds: Array<number>;
}

export class QueryMatchingResultsDto extends BaseQueryMatchingResultsDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  destinationId: number;
}

export class MatchingResult {
  @ApiProperty({ enum: MatchingRuleSourceDatabaseTable })
  sourceTable: MatchingRuleSourceDatabaseTable;

  // @ApiProperty({ type: 'integer', minimum: 1, isArray: true })
  // sourceIds: Array<number>;
  @ApiProperty({
    oneOf: [
      { $ref: getSchemaPath(CanadaAntiDumping) },
      { $ref: getSchemaPath(CanadaOgd) },
      { $ref: getSchemaPath(CanadaSimaCode) },
      { $ref: getSchemaPath(CanadaTariff) },
      { $ref: getSchemaPath(CanadaTreatmentCode) },
      { $ref: getSchemaPath(CanadaVfdCode) },
      { $ref: getSchemaPath(CanadaGstExemptCode) },
      { $ref: getSchemaPath(CanadaExciseTaxCode) },
      { $ref: getSchemaPath(OgdFiling) },
      { $ref: getSchemaPath(SimaFiling) },
      { $ref: getSchemaPath(CertificateOfOrigin) }
    ],
    isArray: true
  })
  sourceRecords: Array<
    | CanadaAntiDumping
    | CanadaOgd
    | CanadaSimaCode
    | CanadaTariff
    | CanadaTreatmentCode
    | CanadaVfdCode
    | CanadaGstExemptCode
    | CanadaExciseTaxCode
    | OgdFiling
    | SimaFiling
    | CertificateOfOrigin
  >;
}

export class QueryMatchingResultsResponseDto {
  @ApiProperty({ type: [MatchingResult] })
  matchingResults: Array<MatchingResult>;
}

export class QueryMatchingResultsWithDestinationIdResponseDto extends QueryMatchingResultsResponseDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  destinationId: number;
}

export class ReverseQueryMatchingResultsDto {
  @ApiProperty({ enum: MatchingRuleDestinationDatabaseTable })
  @IsNotEmpty()
  @IsEnum(MatchingRuleDestinationDatabaseTable)
  destinationTable: MatchingRuleDestinationDatabaseTable;

  @ApiPropertyOptional({
    enum: MatchingRuleOperator,
    default: MatchingRuleOperator.AND,
    description: "Operator used for chaining conditions within a rule"
  })
  @IsOptional()
  @IsEnum(MatchingRuleOperator)
  operator?: MatchingRuleOperator;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "This option is only available for backoffice admin. If provided, the results will be filtered by the specific organization."
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  organizationId?: number;

  @ApiProperty({ type: [CreateMatchingConditionDto] })
  @Type((type) => CreateMatchingConditionDto)
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  conditions: Array<CreateMatchingConditionDto>;
}

export class ReverseQueryMatchingResultsResponseDto {
  @ApiProperty({
    oneOf: [{ $ref: getSchemaPath(Product) }]
  })
  destinationRecords: Array<Product>;
}
