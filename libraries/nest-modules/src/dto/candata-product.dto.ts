import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsArray, IsEnum, IsOptional, IsString, Matches, MaxLength, ValidateNested } from "class-validator";
import { CandataGstStatusCode, SimaCode, TariffTreatmentCode, UnitOfMeasure, VfdCode } from "../types";
import { PgaDto } from "./candata-shipment.dto";

export class CandataProductDto {
  @ApiProperty({ type: "string" })
  @IsString()
  code: string;

  @ApiPropertyOptional({ type: "string", pattern: "^[0-9]{4}$" })
  @IsOptional()
  @IsString()
  @Matches(/^[0-9]{4}$/, { message: "Invalid Tariff Code" })
  tariffCode?: string | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  exciseCode?: string | null;

  @ApiProperty({ type: "string" })
  @IsString()
  description: string;

  @ApiProperty({ enum: TariffTreatmentCode })
  @IsEnum(TariffTreatmentCode)
  tariffTreatment: TariffTreatmentCode;

  @ApiProperty({ enum: VfdCode })
  @IsEnum(VfdCode)
  vfdCode: VfdCode;

  @ApiProperty({ enum: UnitOfMeasure })
  @IsEnum(UnitOfMeasure)
  unitOfMeasure: UnitOfMeasure;

  @ApiPropertyOptional({ type: "string", maxLength: 16 })
  @IsOptional()
  @IsString()
  @MaxLength(16)
  orderInCouncil?: string | null;

  @ApiProperty({ type: "string", pattern: "^[0-9]{10}$" })
  @IsString()
  @Matches(/^[0-9]{10}$/, { message: "Invalid HS code" })
  classification: string;

  @ApiPropertyOptional({ enum: CandataGstStatusCode })
  @IsOptional()
  @IsEnum(CandataGstStatusCode)
  gstStatusCode?: CandataGstStatusCode | null;

  @ApiProperty({ type: "string" })
  @IsString()
  countryOfOrigin: string;

  @ApiPropertyOptional({ enum: SimaCode })
  @IsOptional()
  @IsEnum(SimaCode)
  simaCode?: SimaCode | null;

  @ApiPropertyOptional({ type: PgaDto, isArray: true })
  @IsOptional()
  @Type((type) => PgaDto)
  @ValidateNested({ each: true })
  @IsArray()
  pgas?: Array<PgaDto> | null;
}

export class GetCandataProductsResponseDto {
  @ApiProperty({ type: () => [CandataProductDto] })
  @IsArray()
  @Type((type) => CandataProductDto)
  @ValidateNested({ each: true })
  products: Array<CandataProductDto>;
}
