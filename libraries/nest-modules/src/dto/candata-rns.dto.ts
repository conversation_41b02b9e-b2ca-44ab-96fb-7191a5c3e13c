import { ApiPropertyOptional } from "@nestjs/swagger";
import {
  IsDateString,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  Matches,
  MaxLength,
  MinLength
} from "class-validator";
import { RNSProcessingIndicator } from "../types";

export class CandataRNSResponseDto {
  @ApiPropertyOptional({ enum: RNSProcessingIndicator })
  @IsOptional()
  @IsEnum(RNSProcessingIndicator)
  processingIndicator?: RNSProcessingIndicator | null;

  @ApiPropertyOptional({ type: "string", pattern: "^[0-9]{14}$" })
  @IsOptional()
  @IsString()
  @Matches(/^[0-9]{14}$/)
  transactionNumber?: string | null;

  @ApiPropertyOptional({ type: "string", pattern: "^[0-9A-Z]{3}(-|[0-9A-Z])[0-9A-Z]{1,21}$" })
  @IsOptional()
  @IsString()
  @Matches(/^[0-9A-Z]{3}(-|[0-9A-Z])[0-9A-Z]{1,21}$/)
  cargoControlNumber?: string | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  containers?: string | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  documentMessage?: string | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  message?: string | null;

  @ApiPropertyOptional({ type: "string" })
  @IsOptional()
  @IsString()
  rejectReasons?: string | null;

  @ApiPropertyOptional({ type: "string", maxLength: 2 })
  @IsOptional()
  @IsString()
  @MaxLength(2)
  rejectType?: string | null;

  @ApiPropertyOptional({ type: "string", minLength: 4, maxLength: 4, pattern: "^[0][0-9]{3}$" })
  @IsOptional()
  @IsString()
  @MinLength(4)
  @MaxLength(4)
  @Matches(/^[0][0-9]{3}$/)
  port?: string | null;

  @ApiPropertyOptional({ type: "string", maxLength: 4 })
  @IsOptional()
  @IsString()
  @MaxLength(4)
  sublocation?: string | null;

  @ApiPropertyOptional({ type: "integer" })
  @IsOptional()
  @IsInt()
  dataType?: number | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @IsOptional()
  @IsDateString()
  responseDate?: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @IsOptional()
  @IsDateString()
  processDate?: string | null;
}
