import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsInt, IsNotEmpty, IsOptional, IsString, ValidateNested } from "class-validator";

export class DocusignEnvelopeSummarySender {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  userName: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  userId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  accountId: string;

  @ApiProperty({ type: "string", format: "email" })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiProperty({ type: "string", format: "ipv4" })
  @IsNotEmpty()
  @IsString()
  ipAddress: string;
}

export class DocusignEnvelopeSummarySigner {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ type: "string", format: "email" })
  @IsNotEmpty()
  @IsString()
  email: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  recipientId: string;
}

export class DocusignEnvelopeSummaryRecipients {
  @ApiProperty({ type: () => [DocusignEnvelopeSummarySigner] })
  @Type((type) => DocusignEnvelopeSummarySigner)
  @IsNotEmpty()
  @ValidateNested({ each: true })
  signers: Array<DocusignEnvelopeSummarySigner>;
}

export class DocusignEnvelopeSummary {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  status: string;

  @ApiProperty({ type: () => DocusignEnvelopeSummarySender })
  @Type((type) => DocusignEnvelopeSummarySender)
  @IsNotEmpty()
  @ValidateNested()
  sender: DocusignEnvelopeSummarySender;

  @ApiProperty({ type: () => DocusignEnvelopeSummaryRecipients })
  @Type((type) => DocusignEnvelopeSummaryRecipients)
  @IsNotEmpty()
  @ValidateNested()
  recipients: DocusignEnvelopeSummaryRecipients;
}

export class DocusignWebhookData {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  accountId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  userId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  envelopeId: string;

  @ApiPropertyOptional({ type: () => DocusignEnvelopeSummary })
  @Type((type) => DocusignEnvelopeSummary)
  @IsOptional()
  @ValidateNested()
  envelopeSummary?: DocusignEnvelopeSummary;
}

export class OnImporterPOASignedDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  event: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  apiVersion: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  uri: string;

  @ApiProperty({ type: "integer" })
  @IsNotEmpty()
  @IsInt()
  retryCount: number;

  @ApiProperty({ type: "integer" })
  @IsNotEmpty()
  @IsInt()
  configurationId: number;

  @ApiProperty({ type: "string", format: "date-time" })
  @IsNotEmpty()
  @IsString()
  generatedDateTime: string;

  @ApiProperty({ type: () => DocusignWebhookData })
  @Type((type) => DocusignWebhookData)
  @IsNotEmpty()
  @ValidateNested()
  data: DocusignWebhookData;
}
