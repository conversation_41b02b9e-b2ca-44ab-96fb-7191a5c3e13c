import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform, Type } from "class-transformer";
import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  <PERSON><PERSON>ength,
  Min,
  MinLength
} from "class-validator";
import { TradePartner } from "../entities/trade-partner.entity";
import { PartnerType, TradePartnerColumn } from "../types/trade-partner.types";
import { GetManyDto, GetManyResponseDto } from "./base.dto";

export class GetTradePartnersDto extends GetManyDto {
  @ApiPropertyOptional({
    enum: TradePartnerColumn,
    default: TradePartnerColumn.id
  })
  @IsOptional()
  @IsEnum(TradePartnerColumn)
  sortBy?: TradePartnerColumn;

  @ApiPropertyOptional({ enum: PartnerType })
  @IsOptional()
  @IsEnum(PartnerType)
  partnerType?: PartnerType;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  vendorCode?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  eManifestCarrierCode?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  email?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  phoneNumber?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  address?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  city?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  state?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  postalCode?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  countryId?: number;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 1,
    description:
      "For basic user and organization admin, Organization ID will be auto-set according to current user's organization"
  })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  organizationId?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type((type) => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetTradePartnersResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [TradePartner] })
  partners: Array<TradePartner>;
}

export class CreateTradePartnerDto {
  @ApiPropertyOptional({
    type: "boolean",
    default: false,
    description:
      "Whether the trade partner is global (i.e. not associated with any organization). Only available for backoffice admins."
  })
  @IsOptional()
  @IsBoolean()
  isGlobal?: boolean;

  @ApiProperty({ enum: PartnerType })
  @IsNotEmpty()
  @IsEnum(PartnerType)
  partnerType: PartnerType;

  @ApiPropertyOptional({
    minLength: 1
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  vendorCode?: string | null;

  @ApiPropertyOptional({
    minLength: 4,
    maxLength: 4,
    description: "CBSA-issued eManifest carrier code"
  })
  @Transform(({ value }) => (typeof value === "string" ? value?.trim()?.toUpperCase() : value))
  @IsOptional()
  @IsString()
  @MinLength(4)
  @MaxLength(4)
  eManifestCarrierCode?: string | null;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  name: string;

  @ApiPropertyOptional({ type: "string", format: "email", minLength: 1 })
  @IsOptional()
  @IsEmail()
  @MinLength(1)
  email?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  phoneNumber?: string | null;

  @ApiPropertyOptional({
    minLength: 1,
    description: "Address is limited to 70 characters, any additional characters will be truncated"
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @Transform(({ value }) => value?.trim().substring(0, 70) || null)
  address?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  city?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  state?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  postalCode?: string | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  countryId?: number | null;
}

export class EditTradePartnerDto {
  @ApiPropertyOptional({ enum: PartnerType })
  @IsOptional()
  @IsEnum(PartnerType)
  partnerType?: PartnerType;

  @ApiPropertyOptional({
    minLength: 1
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  vendorCode?: string | null;

  @ApiPropertyOptional({
    minLength: 4,
    maxLength: 4,
    description: "CBSA-issued eManifest carrier code"
  })
  @Transform(({ value }) => (typeof value === "string" ? value?.trim()?.toUpperCase() : value))
  @IsOptional()
  @IsString()
  @MinLength(4)
  @MaxLength(4)
  eManifestCarrierCode?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ type: "string", format: "email", minLength: 1 })
  @IsOptional()
  @IsEmail()
  @MinLength(1)
  email?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  phoneNumber?: string | null;

  @ApiPropertyOptional({
    minLength: 1,
    description: "Address is limited to 70 characters, any additional characters will be truncated"
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @Transform(({ value }) => value?.trim().substring(0, 70) || null)
  address?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  city?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  state?: string | null;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  postalCode?: string | null;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  countryId?: number | null;
}

export class MergeTradePartnerDto {
  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  sourceTradePartnerId: number;

  @ApiProperty({ type: "integer", minimum: 1 })
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  targetTradePartnerId: number;

  @ApiProperty({
    type: EditTradePartnerDto,
    description: "Merged data will be used to update the target trade partner"
  })
  @IsNotEmpty()
  mergedData: EditTradePartnerDto;
}
