import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { GetManyDto, GetManyResponseDto } from "./base.dto";
import { DocumentTypeColumn } from "../types/document-type.types";
import { IsEnum, IsInt, IsNotEmpty, IsOptional, IsString, Min, MinLength } from "class-validator";
import { Type } from "class-transformer";
import { DocumentType } from "../entities/document-type.entity";

export class GetDocumentTypesDto extends GetManyDto {
  @ApiPropertyOptional({ enum: DocumentTypeColumn, default: DocumentTypeColumn.id })
  @IsOptional()
  @IsEnum(DocumentTypeColumn)
  sortBy?: DocumentTypeColumn;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  promptTemplate?: string;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  createdById?: number;

  @ApiPropertyOptional({ type: "integer", minimum: 1 })
  @Type(() => Number)
  @IsOptional()
  @IsInt()
  @Min(1)
  lastEditedById?: number;
}

export class GetDocumentTypesResponseDto extends GetManyResponseDto {
  @ApiProperty({ type: () => [DocumentType] })
  documentTypes: Array<DocumentType>;
}

export class CreateDocumentTypeDto {
  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  name: string;

  @ApiProperty({ minLength: 1 })
  @IsNotEmpty()
  @IsString()
  @MinLength(1)
  promptTemplate: string;
}

export class EditDocumentTypeDto {
  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  name?: string;

  @ApiPropertyOptional({ minLength: 1 })
  @IsOptional()
  @IsString()
  @MinLength(1)
  promptTemplate?: string;
}
