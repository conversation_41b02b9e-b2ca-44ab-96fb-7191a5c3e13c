import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards
} from "@nestjs/common";
import {
  ApiBadRequestResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags
} from "@nestjs/swagger";
import {
  ApiAccessTokenAuthenticated,
  ApiDeleteResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses
} from "../decorators";
import {
  BadRequestResponseDto,
  GetImportersDto,
  GetImportersResponseDto,
  NotFoundResponseDto,
  UpdateImporterWhitelistEmailsDto
} from "../dto";
import { Importer } from "../entities";
import { AccessTokenGuard } from "../guards";
import { BaseImporterService } from "./importer.service";

@ApiTags("Importer API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("importers")
export class BaseImporterController {
  constructor(private readonly importerService: BaseImporterService) {}

  @ApiOperation({ summary: "Get Importers" })
  @ApiGetManyResponses({ type: GetImportersResponseDto })
  @Get()
  async getImporters(@Query() getImportersDto: GetImportersDto) {
    return await this.importerService.getImporters(getImportersDto);
  }

  @ApiOperation({ summary: "Get Importer" })
  @ApiParam({ name: "id", type: "integer", description: "Importer ID" })
  @ApiGetByIdResponses({ type: Importer })
  @Get(":id")
  async getImporterById(@Param("id", ParseIntPipe) id: number) {
    const importer = await this.importerService.getImporterById(id);
    if (!importer) throw new NotFoundException("Importer not found");
    return importer;
  }

  @ApiOperation({ summary: "Disable Importer" })
  @ApiParam({ name: "id", type: "integer", description: "Importer ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiOkResponse({ type: Importer })
  @HttpCode(200)
  @Post(":id/disable")
  async disableImporter(@Param("id", ParseIntPipe) id: number) {
    return await this.importerService.disableImporter(id);
  }

  @ApiOperation({ summary: "Enable Importer" })
  @ApiParam({ name: "id", type: "integer", description: "Importer ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiOkResponse({ type: Importer })
  @HttpCode(200)
  @Post(":id/enable")
  async enableImporter(@Param("id", ParseIntPipe) id: number) {
    return await this.importerService.enableImporter(id);
  }

  @ApiOperation({ summary: "Delete Importer" })
  @ApiParam({ name: "id", type: "integer", description: "Importer ID" })
  @ApiDeleteResponses()
  @HttpCode(204)
  @Delete(":id")
  async deleteImporter(@Param("id", ParseIntPipe) id: number) {
    return await this.importerService.deleteImporter(id);
  }

  @ApiOperation({ summary: "Generate Importer Receive Email" })
  @ApiParam({ name: "id", type: "integer", description: "Importer ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiBadRequestResponse({ type: BadRequestResponseDto })
  @ApiOkResponse({ type: Importer })
  @HttpCode(200)
  @Post(":id/receive-email")
  async generateReceiveEmail(@Param("id", ParseIntPipe) id: number) {
    return await this.importerService.generateImporterReceiveEmail(id);
  }

  @ApiOperation({ summary: "Update Importer Whitelist Emails" })
  @ApiParam({ name: "id", type: "integer", description: "Importer ID" })
  @ApiNotFoundResponse({ type: NotFoundResponseDto })
  @ApiOkResponse({ type: Importer })
  @HttpCode(200)
  @Put(":id/whitelist-emails")
  async updateImporterWhitelistEmails(
    @Param("id", ParseIntPipe) id: number,
    @Body() updateImporterWhitelistEmailsDto: UpdateImporterWhitelistEmailsDto
  ) {
    return await this.importerService.updateImporterWhitelistEmails(id, updateImporterWhitelistEmailsDto);
  }
}
