import { Global, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CountryModule } from "../country";
import { Importer } from "../entities";
import { BaseImporterController } from "./importer.controller";
import { BaseImporterService } from "./importer.service";

@Global()
@Module({
  imports: [TypeOrmModule.forFeature([Importer]), CountryModule],
  providers: [BaseImporterService],
  controllers: [BaseImporterController],
  exports: [BaseImporterService]
})
export class BaseImporterModule {}
