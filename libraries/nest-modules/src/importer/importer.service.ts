import { BadRequestException, Inject, Injectable, Logger, NotFoundException, Scope } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import { <PERSON>N<PERSON>, Not, Query<PERSON><PERSON><PERSON>, Repository } from "typeorm";
import { NodeEnv } from "..";
import { CarmService } from "../carm";
import { CountryService } from "../country";
import { BaseDocusignService } from "../docusign";
import {
  CreateImporterDto,
  EditImporterDto,
  GetImportersDto,
  GetImportersResponseDto,
  SendPOASignatureRequestEmailResponseDto,
  UpdateImporterWhitelistEmailsDto
} from "../dto";
import { Importer } from "../entities";
import { getFindOptions } from "../helper-functions";
import {
  AuthenticatedRequest,
  DocusignCreateEnvelopeRequestBody,
  FIND_IMPORTER_RELATIONS,
  IMPORTER_ENUM_KEYS,
  IMPORTER_REQUIRED_KEYS,
  ImporterColumn,
  ImporterStatus,
  UserPermission
} from "../types";

@Injectable({ scope: Scope.REQUEST })
export class BaseImporterService {
  constructor(
    @InjectRepository(Importer)
    private readonly importerRepository: Repository<Importer>,
    @Inject(CountryService)
    private readonly countryService: CountryService,
    @Inject(CarmService)
    private readonly carmService: CarmService,
    @Inject(BaseDocusignService)
    private readonly docusignService: BaseDocusignService,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest
  ) {}
  private readonly logger = new Logger(BaseImporterService.name);

  async getImporters(
    getImportersDto: GetImportersDto,
    queryRunner?: QueryRunner
  ): Promise<GetImportersResponseDto> {
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      getImportersDto.organizationId = this.request?.user?.organization?.id || -1;
    const { where, order, skip, take } = getFindOptions(
      getImportersDto,
      IMPORTER_ENUM_KEYS,
      ["docusignEnvelopeId"],
      ImporterColumn.id
    );
    const [importers, total] = await (
      queryRunner ? queryRunner.manager.getRepository(Importer) : this.importerRepository
    ).findAndCount({
      where,
      relations: FIND_IMPORTER_RELATIONS,
      order,
      skip,
      take
    });

    return {
      importers,
      total,
      skip,
      limit: take
    };
  }

  async getImporterById(importerId: number, queryRunner?: QueryRunner) {
    return await (
      queryRunner ? queryRunner.manager.getRepository(Importer) : this.importerRepository
    ).findOne({
      where: {
        id: importerId,
        organization: {
          id:
            this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
              ? Not(IsNull())
              : this.request?.user?.organization?.id || -1
        }
      },
      relations: FIND_IMPORTER_RELATIONS
    });
  }

  async getImporterByEnvelopeId(envelopeId: string) {
    return await this.importerRepository.findOne({
      where: { docusignEnvelopeId: envelopeId },
      relations: FIND_IMPORTER_RELATIONS
    });
  }

  async createImporter(
    createImporterDto: CreateImporterDto,
    queryRunner?: QueryRunner,
    skipCarmStatusCheck = false
  ) {
    const importerRepository = queryRunner
      ? queryRunner.manager.getRepository(Importer)
      : this.importerRepository;

    const { companyName, countryId, ...createImporterProps } = createImporterDto;
    if (
      await importerRepository.existsBy({
        companyName,
        organization: { id: this.request?.user?.organization?.id }
      })
    )
      throw new BadRequestException("Importer with the same company name already exists");

    const skipPoa = this.request?.user?.organization?.skipPoaCheck;

    let importer = new Importer();
    importer.status = skipPoa ? ImporterStatus.ACTIVE : ImporterStatus.PENDING_POA;
    importer.createdBy = this.request?.user || null;
    importer.lastEditedBy = this.request?.user || null;
    importer.organization = this.request?.user?.organization;
    importer.companyName = companyName;

    importer.country = await this.countryService.getCountryById(countryId, queryRunner);
    if (!importer.country) throw new NotFoundException("Country not found");

    if (this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN) {
      importer.candataCustomerNumber = createImporterDto.candataCustomerNumber;
    }

    for (const [key, value] of Object.entries(createImporterProps)) {
      if (value === undefined) continue;
      importer[key] = value;
    }
    const missingRequiredKeys = IMPORTER_REQUIRED_KEYS.filter((key) =>
      [null, undefined].includes(importer[key])
    );
    if (missingRequiredKeys.length > 0)
      throw new BadRequestException(`The following fields are required: ${missingRequiredKeys.join(", ")}`);

    if (process.env.NODE_ENV === NodeEnv.PRODUCTION && !skipCarmStatusCheck) {
      try {
        const request = await this.carmService.sendRequest(importer.businessNumber.slice(0, 9));
        importer.carmStatus = request?.result;
        // TODO send email to backoffice if status is invalid
      } catch (error) {
        this.logger.error("CARM request failed, but continuing the process:", error);
      }
    }

    importer = await importerRepository.save(importer);

    if (!skipPoa) await this.sendPOASignatureRequestEmail(importer, undefined, queryRunner);

    await this.generateImporterReceiveEmail(importer.id, queryRunner);

    // const whitelist = await this.updateImporterWhitelistEmails(importer.id, { whitelistEmails });
    // await this.emailService.sendOnboardingEmail(importer);

    return await this.getImporterById(importer.id, queryRunner);
  }

  async editImporter(importerId: number, editImporterDto: EditImporterDto, queryRunner?: QueryRunner) {
    const importerRepository = queryRunner
      ? queryRunner.manager.getRepository(Importer)
      : this.importerRepository;

    const importer = await this.getImporterById(importerId, queryRunner);
    if (!importer) throw new NotFoundException("Importer not found");

    if (importer.organization?.id !== this.request?.user?.organization?.id)
      throw new BadRequestException("Importer does not belong to the organization of current user");

    if (
      ![ImporterStatus.PENDING_POA, ImporterStatus.REJECTED].includes(importer.status) &&
      this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN
    )
      throw new BadRequestException("Importer is not Pending POA or Rejected");

    const { companyName, countryId, ...editImporterProps } = editImporterDto;

    importer.lastEditedBy = this.request?.user || null;
    if (typeof companyName === "string") {
      if (
        await importerRepository.existsBy({
          id: Not(importerId),
          companyName,
          organization: { id: this.request?.user?.organization?.id }
        })
      )
        throw new BadRequestException("Importer with the same company name already exists");
      importer.companyName = companyName;
    }
    if (typeof countryId === "number") {
      importer.country = await this.countryService.getCountryById(countryId, queryRunner);
      if (!importer.country) throw new NotFoundException("Country not found");
    }
    for (const [key, value] of Object.entries(editImporterProps)) {
      if (value === undefined) continue;
      importer[key] = value;
    }
    const missingRequiredKeys = IMPORTER_REQUIRED_KEYS.filter((key) =>
      [null, undefined].includes(importer[key])
    );
    if (missingRequiredKeys.length > 0)
      throw new BadRequestException(`The following fields are required: ${missingRequiredKeys.join(", ")}`);

    if (importer.status === ImporterStatus.REJECTED) {
      importer.status = ImporterStatus.PENDING_POA;
      importer.rejectReason = null;
      importer.docusignEnvelopeId = null;
    }

    await importerRepository.save(importer);

    if (importer.status === ImporterStatus.PENDING_POA)
      await this.sendPOASignatureRequestEmail(importer, !!importer.docusignEnvelopeId, queryRunner);

    // const whitelist = await this.updateImporterWhitelistEmails(importer.id, {
    //   whitelistEmails: editImporterDto.whitelistEmails
    // });
    // await this.emailService.sendOnboardingEmail(importer);

    return await this.getImporterById(importer.id, queryRunner);
  }

  async disableImporter(importerId: number, queryRunner?: QueryRunner) {
    const importer = await this.getImporterById(importerId, queryRunner);
    if (!importer) throw new NotFoundException("Importer not found");

    // if (importer.organization?.id !== this.request?.user?.organization?.id)
    //   throw new BadRequestException("Importer does not belong to the organization of current user");
    if (importer.status !== ImporterStatus.ACTIVE) throw new BadRequestException("Importer is not Active");

    importer.lastEditedBy = this.request?.user || null;
    importer.status = ImporterStatus.DISABLED;
    await (queryRunner ? queryRunner.manager.getRepository(Importer) : this.importerRepository).save(
      importer
    );

    return await this.getImporterById(importerId, queryRunner);
  }

  async enableImporter(importerId: number, queryRunner?: QueryRunner) {
    const importer = await this.getImporterById(importerId, queryRunner);
    if (!importer) throw new NotFoundException("Importer not found");
    if (importer.status !== ImporterStatus.DISABLED)
      throw new BadRequestException("Importer is not Disabled");

    importer.lastEditedBy = this.request?.user || null;
    importer.status = ImporterStatus.ACTIVE;
    await (queryRunner ? queryRunner.manager.getRepository(Importer) : this.importerRepository).save(
      importer
    );

    return await this.getImporterById(importerId, queryRunner);
  }

  async deleteImporter(importerId: number, queryRunner?: QueryRunner) {
    const importer = await this.getImporterById(importerId, queryRunner);
    if (!importer) throw new NotFoundException("Importer not found");
    // if (importer.organization?.id !== this.request?.user?.organization?.id)
    //   throw new BadRequestException("Importer does not belong to the organization of current user");
    if (
      ![ImporterStatus.PENDING_POA, ImporterStatus.REJECTED].includes(importer.status) &&
      this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN
    )
      throw new BadRequestException("Importer is not Pending POA or Rejected");
    await (queryRunner ? queryRunner.manager.getRepository(Importer) : this.importerRepository).delete({
      id: importerId
    });
    return;
  }

  async generateImporterReceiveEmail(importerId: number, queryRunner?: QueryRunner) {
    const importerRepository = queryRunner
      ? queryRunner.manager.getRepository(Importer)
      : this.importerRepository;

    const importer = await this.getImporterById(importerId, queryRunner);
    if (!importer) throw new NotFoundException("Importer not found");
    if (importer.receiveEmail) throw new BadRequestException("Importer already has a receive email");
    const RECEIVE_EMAIL_DOMAIN = "doc.clarocustoms.com";
    const parsedImporterName = importer.companyName.toLowerCase().replaceAll(/[^a-z0-9]/g, "-");
    let newReceiveEmailName = parsedImporterName,
      duplicateCount = 0;
    while (
      await importerRepository.existsBy({
        id: Not(importer.id),
        receiveEmail: `${newReceiveEmailName}@${RECEIVE_EMAIL_DOMAIN}`
      })
    )
      newReceiveEmailName = `${parsedImporterName}-${++duplicateCount}`;
    importer.receiveEmail = `${newReceiveEmailName}@${RECEIVE_EMAIL_DOMAIN}`;
    await importerRepository.save(importer);
    return importer;
  }

  async updateImporterWhitelistEmails(
    importerId: number,
    updateImporterWhitelistEmailsDto: UpdateImporterWhitelistEmailsDto
  ) {
    const importer = await this.getImporterById(importerId);
    if (!importer) throw new NotFoundException("Importer not found");

    importer.whitelistEmails = updateImporterWhitelistEmailsDto.whitelistEmails;
    await this.importerRepository.save(importer);

    return await this.getImporterById(importerId);
  }

  private generatePOAPayload(importer: Importer): DocusignCreateEnvelopeRequestBody {
    const emailSubject = `Signature Required: Importer ${importer.companyName} POA`;

    const emailContent = [
      `Hi ${importer.officerNameAndTitle},`,
      "",
      `Please sign the following Power of Attorney for the company ${importer.companyName}.`,
      "",
      "Thanks,",
      "Claro Customs"
    ].join("\n");

    const payload: DocusignCreateEnvelopeRequestBody = {
      emailSubject,
      emailBlurb: emailContent,
      templateRoles: [
        {
          email: importer.email,
          name: importer.officerNameAndTitle,
          roleName: "Signer",
          tabs: {
            textTabs: [
              {
                tabLabel: "company_name",
                value: importer.companyName
              },
              {
                tabLabel: "business_number",
                value: importer.businessNumber
              },
              {
                tabLabel: "company_address",
                value: `${importer.address},\n${importer.city}, ${importer.state} ${importer.postalCode},\n${importer.country?.name}`
              },
              {
                tabLabel: "phone_number",
                value: importer.phoneNumber
              },
              {
                tabLabel: "email",
                value: importer.email
              },
              {
                tabLabel: "officer",
                value: importer.officerNameAndTitle
              },
              {
                tabLabel: "place",
                value: `${importer.city}, ${importer.state} ${importer.postalCode}, ${importer.country?.name}`
              },
              {
                tabLabel: "fax",
                value: importer.fax || ""
              }
            ],
            signHereTabs: [
              {
                tabLabel: "sign_poa"
              },
              {
                tabLabel: "sign_gst"
              }
            ],
            dateSignedTabs: [
              {
                tabLabel: "date_signed"
              }
            ]
          }
        }
      ],
      status: "sent"
    };
    return payload;
  }

  async sendPOASignatureRequestEmail(
    importer: Importer,
    resend?: boolean,
    queryRunner?: QueryRunner
  ): Promise<SendPOASignatureRequestEmailResponseDto> {
    const payload = this.generatePOAPayload(importer);
    let envelopeId: string;

    if (resend) {
      const { envelopeId: newEnvelopeId } = await this.docusignService.resendEnvelope(
        importer.docusignEnvelopeId,
        payload
      );
      envelopeId = newEnvelopeId;
    } else {
      const request = await this.docusignService.sendSignatureRequest(payload);
      envelopeId = request.envelopeId;
    }

    await (queryRunner ? queryRunner.manager.getRepository(Importer) : this.importerRepository).update(
      { id: importer.id },
      {
        docusignEnvelopeId: envelopeId,
        lastEditedBy: this.request?.user || null
      }
    );
    importer = await this.getImporterById(importer.id, queryRunner);

    return { envelopeId, importer };
  }
}
