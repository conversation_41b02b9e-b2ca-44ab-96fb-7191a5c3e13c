import { Controller, Get, Post, Body, Param, Delete, HttpCode, Query, UseGuards } from "@nestjs/common";
import { TrackingHistoryService } from "./tracking-history.service";
import {
  CreateTrackingHistoryDto,
  GetTrackingHistoryDto,
  GetTrackingHistoryResponseDto
} from "../dto/tracking-history.dto";
import { TrackingHistory } from "../entities/tracking-history.entity";
import { ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import {
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses
} from "../decorators/api-docs";
import { AccessTokenGuard } from "../guards";

@ApiTags("Tracking History")
@Controller("tracking-history")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
export class TrackingHistoryController {
  constructor(private readonly trackingHistoryService: TrackingHistoryService) {}

  @ApiOperation({ summary: "Get tracking history by shipment id" })
  @ApiParam({ name: "trackingHistoryId", type: "integer", description: "Tracking History ID" })
  @ApiGetByIdResponses({ type: TrackingHistory })
  @Get(":trackingHistoryId")
  async getTrackingHistoryById(
    @Param("trackingHistoryId") trackingHistoryId: number
  ): Promise<TrackingHistory> {
    return await this.trackingHistoryService.getTrackingHistoryById(trackingHistoryId);
  }

  @ApiOperation({ summary: "Get tracking histories" })
  @ApiGetManyResponses({ type: GetTrackingHistoryResponseDto })
  @Get()
  async getTrackingHistories(
    @Query() getTrackingHistoryDto: GetTrackingHistoryDto
  ): Promise<GetTrackingHistoryResponseDto> {
    return await this.trackingHistoryService.getTrackingHistories(getTrackingHistoryDto);
  }

  @ApiOperation({ summary: "Delete tracking history by id" })
  @ApiParam({ name: "trackingHistoryId", type: "integer", description: "Tracking History ID" })
  @ApiDeleteResponses()
  @HttpCode(204)
  @Delete(":trackingHistoryId")
  async deleteTrackingHistoryById(@Param("trackingHistoryId") trackingHistoryId: number) {
    return await this.trackingHistoryService.deleteTrackingHistoryById(trackingHistoryId);
  }
}
