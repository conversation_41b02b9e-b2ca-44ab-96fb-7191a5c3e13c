import { DynamicModule, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Organization, Shipment } from "../entities";
import { TrackingHistory } from "../entities/tracking-history.entity";
import { TrackingHistoryController } from "./tracking-history.controller";
import { TrackingHistoryService } from "./tracking-history.service";

@Module({})
export class TrackingHistoryModule {
  static register(): DynamicModule {
    return {
      module: TrackingHistoryModule,
      global: true,
      imports: [TypeOrmModule.forFeature([TrackingHistory, Shipment, Organization])],
      controllers: [TrackingHistoryController],
      providers: [TrackingHistoryService],
      exports: [TrackingHistoryService]
    };
  }
}
