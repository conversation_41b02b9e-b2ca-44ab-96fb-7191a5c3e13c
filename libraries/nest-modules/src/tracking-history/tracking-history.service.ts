import { BadRequestException, Inject, Injectable, NotFoundException, Scope } from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import { IsNull, Not, QueryRunner, Repository } from "typeorm";
import { GetTrackingHistoryDto } from "../dto";
import { CreateTrackingHistoryDto, GetTrackingHistoryResponseDto } from "../dto/tracking-history.dto";
import { Organization, Shipment, TrackingHistory } from "../entities";
import { getFindOptions } from "../helper-functions";
import { AuthenticatedRequest, UserPermission } from "../types";
import { FIND_SHIPMENT_RELATIONS } from "../types/shipment.types";
import {
  FIND_TRACKING_HISTORY_RELATIONS,
  TRACKING_HISTORY_REQUIRED_KEYS,
  TrackingHistoryColumn
} from "../types/tracking-history.types";

@Injectable({ scope: Scope.REQUEST })
export class TrackingHistoryService {
  constructor(
    @InjectRepository(TrackingHistory)
    private readonly trackingHistoryRepository: Repository<TrackingHistory>,
    @InjectRepository(Shipment)
    private readonly shipmentRepository: Repository<Shipment>,
    @InjectRepository(Organization)
    private readonly organizationRepository: Repository<Organization>,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest
  ) {}

  async createTrackingHistory(createTrackingHistoryDto: CreateTrackingHistoryDto, queryRunner?: QueryRunner) {
    const newTrackingHistory = new TrackingHistory();
    for (const [key, value] of Object.entries(createTrackingHistoryDto)) {
      if (value === undefined) continue;
      const propertyName = key.replace(/Id$/, "");
      switch (propertyName) {
        case "shipment":
          if (typeof value === "number") {
            newTrackingHistory[propertyName] = await (
              queryRunner ? queryRunner.manager.getRepository(Shipment) : this.shipmentRepository
            ).findOne({
              where: { id: value },
              relations: FIND_SHIPMENT_RELATIONS
            });
            newTrackingHistory.organization = newTrackingHistory[propertyName]?.organization || null;
            if (!newTrackingHistory[propertyName]) throw new NotFoundException(`${propertyName} not found`);
          } else newTrackingHistory[propertyName] = null;
          break;
        default:
          newTrackingHistory[propertyName] = value;
      }
    }

    const missingRequiredKeys = TRACKING_HISTORY_REQUIRED_KEYS.filter((key) =>
      [null, undefined].includes(newTrackingHistory[key])
    );
    if (missingRequiredKeys.length > 0)
      throw new BadRequestException(
        `The following keys are required for tracking history: ${missingRequiredKeys.join(", ")}`
      );

    await (
      queryRunner ? queryRunner.manager.getRepository(TrackingHistory) : this.trackingHistoryRepository
    ).save(newTrackingHistory);
    return await this.getTrackingHistoryById(newTrackingHistory.id);
  }

  async getTrackingHistoryById(trackingHistoryId: number, queryRunner?: QueryRunner) {
    return await (
      queryRunner ? queryRunner.manager.getRepository(TrackingHistory) : this.trackingHistoryRepository
    ).findOne({
      where: {
        id: trackingHistoryId,
        organization: {
          id:
            this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
              ? Not(IsNull())
              : this.request?.user?.organization?.id || -1
        }
      },
      relations: FIND_TRACKING_HISTORY_RELATIONS
    });
  }

  async getTrackingHistories(
    getTrackingHistoryDto: GetTrackingHistoryDto
  ): Promise<GetTrackingHistoryResponseDto> {
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      getTrackingHistoryDto.organizationId = this.request?.user?.organization?.id || -1;
    const { where, order, skip, take } = getFindOptions<TrackingHistory>(
      getTrackingHistoryDto,
      [],
      [],
      TrackingHistoryColumn.id
    );

    const [trackingHistories, total] = await this.trackingHistoryRepository.findAndCount({
      where,
      relations: FIND_TRACKING_HISTORY_RELATIONS,
      order,
      skip,
      take
    });

    return {
      trackingHistories,
      total,
      skip,
      limit: take
    };
  }

  async deleteTrackingHistoryById(trackingHistoryId: number) {
    const trackingHistory = await this.getTrackingHistoryById(trackingHistoryId);
    if (!trackingHistory) throw new NotFoundException("Tracking history not found");
    if (
      this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN &&
      trackingHistory.shipment?.organization?.id !== this.request?.user?.organization?.id
    ) {
      throw new BadRequestException("You can only delete tracking history for your organization");
    }
    await this.trackingHistoryRepository.delete({ id: trackingHistoryId });
    return;
  }
}
