import { Inject, Injectable, Logger } from "@nestjs/common";
import { ModuleRef } from "@nestjs/core";
import { OnEvent } from "@nestjs/event-emitter";
import { InjectDataSource } from "@nestjs/typeorm";
import { MatchingRuleEvent } from "../../types";
import { DataSource } from "typeorm";
import { MatchingRuleService } from "../services";

@Injectable()
export class MatchingRuleListener {
  constructor(
    @Inject(MatchingRuleService)
    private readonly matchingRuleService: MatchingRuleService
  ) {}
  private readonly logger = new Logger(MatchingRuleListener.name);

  @OnEvent(MatchingRuleEvent.MATCHING_RULE_CLEAN_UP)
  async onCleanUpMatchingRules() {
    this.logger.log("Cleaning up expired matching rules...");
    await this.matchingRuleService.cleanUpExpiredMatchingRules();

    this.logger.log("Cleaning up dangling matching rules...");
    await this.matchingRuleService.cleanUpDanglingMatchingRules();
  }
}
