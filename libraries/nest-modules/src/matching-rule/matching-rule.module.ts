import { DynamicModule, Module } from "@nestjs/common";
import { MATCHING_RULE_MODULE_OPTIONS, CrudOptions, MatchingRuleModuleOptions } from "../types";
import { TypeOrmModule } from "@nestjs/typeorm";
import { MatchingRuleController } from "./controllers";
import {
  MatchingRule,
  MatchingCondition,
  MatchingHistory,
  TradePartner,
  Country,
  Organization,
  OgdFiling
} from "../entities";
import { MatchingRuleService, MatchingConditionService, RuleQueryService } from "./services";
import { MatchingRuleListener } from "./listeners";

@Module({})
export class MatchingRuleModule {
  static register(options: MatchingRuleModuleOptions): DynamicModule {
    return {
      global: true,
      module: MatchingRuleModule,
      imports: [
        TypeOrmModule.forFeature([
          MatchingRule,
          MatchingCondition,
          MatchingHistory,
          OgdFiling,
          TradePartner,
          Country,
          Organization
        ])
      ],
      controllers: [MatchingRuleController],
      providers: [
        {
          provide: MATCHING_RULE_MODULE_OPTIONS,
          useValue: options
        },
        MatchingRuleService,
        MatchingConditionService,
        RuleQueryService,
        MatchingRuleListener
      ],
      exports: [MatchingRuleService, MatchingConditionService, RuleQueryService]
    };
  }
}
