import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseGuards,
  NotFoundException,
  Post,
  Body,
  Put,
  Delete,
  HttpCode
} from "@nestjs/common";
import { AccessTokenGuard } from "../../guards";
import {
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses
} from "../../decorators";
import { ApiForbiddenResponse, ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import {
  BatchUpdateMatchingConditionsDto,
  BatchUpdateMatchingConditionsResponseDto,
  CreateMatchingRuleDto,
  CreateMatchingConditionDto,
  EditMatchingRuleDto,
  EditMatchingConditionDto,
  ForbiddenResponseDto,
  GetMatchingConditionsDto,
  GetMatchingConditionsResponseDto,
  GetMatchingRulesDto,
  GetMatchingRulesResponseDto,
  UpdateMatchingRuleStatusDto,
  QueryMatchingResultsResponseDto,
  QueryMatchingResultsDto,
  ReverseQueryMatchingResultsResponseDto,
  ReverseQueryMatchingResultsDto,
  QueryMatchingResultsWithDestinationIdResponseDto,
  QueryMultipleMatchingResultsDto
} from "../../dto";
import { MatchingRuleService, MatchingConditionService, RuleQueryService } from "../services";
import { MatchingRule, MatchingCondition } from "../../entities";

@ApiTags("Matching Rule API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("matching-rules")
export class MatchingRuleController {
  constructor(
    private readonly matchingRuleService: MatchingRuleService,
    private readonly matchingConditionService: MatchingConditionService,
    private readonly ruleQueryService: RuleQueryService
  ) {}

  // MatchingRule endpoints

  @Get()
  @ApiOperation({ summary: "Get Matching Rules" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: GetMatchingRulesResponseDto })
  async getMatchingRules(@Query() getMatchingRulesDto: GetMatchingRulesDto) {
    return await this.matchingRuleService.getMatchingRules(getMatchingRulesDto);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get Matching Rule by ID" })
  @ApiParam({ name: "id", type: "integer", description: "Matching Rule ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: MatchingRule })
  async getMatchingRuleById(@Param("id", ParseIntPipe) id: number) {
    const matchingRule = await this.matchingRuleService.getMatchingRuleById(id);
    if (!matchingRule) throw new NotFoundException("Matching Rule not found");
    return matchingRule;
  }

  @Post()
  @ApiOperation({ summary: "Create Matching Rule" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiCreateResponses({ type: MatchingRule })
  async createMatchingRule(@Body() createMatchingRuleDto: CreateMatchingRuleDto) {
    return await this.matchingRuleService.createMatchingRule(createMatchingRuleDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update Matching Rule" })
  @ApiParam({ name: "id", type: "integer", description: "Matching Rule ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: MatchingRule })
  async editMatchingRule(
    @Param("id", ParseIntPipe) id: number,
    @Body() editMatchingRuleDto: EditMatchingRuleDto
  ) {
    return await this.matchingRuleService.editMatchingRule(id, editMatchingRuleDto);
  }

  @Post(":id/status")
  @ApiOperation({ summary: "Update Matching Rule Status" })
  @ApiParam({ name: "id", type: "integer", description: "Matching Rule ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: MatchingRule })
  async updateMatchingRuleStatus(
    @Param("id", ParseIntPipe) id: number,
    @Body() updateMatchingRuleStatusDto: UpdateMatchingRuleStatusDto
  ) {
    return await this.matchingRuleService.updateMatchingRuleStatus(id, updateMatchingRuleStatusDto);
  }

  @HttpCode(204)
  @Delete(":id")
  @ApiOperation({ summary: "Delete Matching Rule" })
  @ApiParam({ name: "id", type: "integer", description: "Matching Rule ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiDeleteResponses()
  async deleteMatchingRule(@Param("id", ParseIntPipe) id: number) {
    await this.matchingRuleService.deleteMatchingRule(id);
    return;
  }

  // MatchingCondition endpoints

  @Get(":ruleId/conditions")
  @ApiOperation({ summary: "Get Matching Conditions" })
  @ApiParam({ name: "ruleId", type: "integer", description: "Matching Rule ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: GetMatchingConditionsResponseDto })
  async getMatchingConditions(
    @Param("ruleId", ParseIntPipe) ruleId: number,
    @Query() getMatchingConditionsDto: GetMatchingConditionsDto
  ) {
    return await this.matchingConditionService.getMatchingConditions(ruleId, getMatchingConditionsDto);
  }

  @Get(":ruleId/conditions/:conditionId")
  @ApiOperation({ summary: "Get Matching Condition by ID" })
  @ApiParam({ name: "ruleId", type: "integer", description: "Matching Rule ID" })
  @ApiParam({ name: "conditionId", type: "integer", description: "Matching Condition ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: MatchingCondition })
  async getMatchingConditionById(
    @Param("ruleId", ParseIntPipe) ruleId: number,
    @Param("conditionId", ParseIntPipe) conditionId: number
  ) {
    const condition = await this.matchingConditionService.getMatchingConditionById(ruleId, conditionId);
    if (!condition) throw new NotFoundException("Matching Condition not found");
    return condition;
  }

  @Post(":ruleId/conditions")
  @ApiOperation({ summary: "Create Matching Condition" })
  @ApiParam({ name: "ruleId", type: "integer", description: "Matching Rule ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiCreateResponses({ type: MatchingCondition })
  async createMatchingCondition(
    @Param("ruleId", ParseIntPipe) ruleId: number,
    @Body() createMatchingConditionDto: CreateMatchingConditionDto
  ) {
    return await this.matchingConditionService.createMatchingCondition(ruleId, createMatchingConditionDto);
  }

  @Put(":ruleId/conditions/:conditionId")
  @ApiOperation({ summary: "Update Matching Condition" })
  @ApiParam({ name: "ruleId", type: "integer", description: "Matching Rule ID" })
  @ApiParam({ name: "conditionId", type: "integer", description: "Matching Condition ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: MatchingCondition })
  async editMatchingCondition(
    @Param("ruleId", ParseIntPipe) ruleId: number,
    @Param("conditionId", ParseIntPipe) conditionId: number,
    @Body() editMatchingConditionDto: EditMatchingConditionDto
  ) {
    return await this.matchingConditionService.editMatchingCondition(
      ruleId,
      conditionId,
      editMatchingConditionDto
    );
  }

  @HttpCode(204)
  @Delete(":ruleId/conditions/:conditionId")
  @ApiOperation({ summary: "Delete Matching Condition" })
  @ApiParam({ name: "ruleId", type: "integer", description: "Matching Rule ID" })
  @ApiParam({ name: "conditionId", type: "integer", description: "Matching Condition ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiDeleteResponses()
  async deleteMatchingCondition(
    @Param("ruleId", ParseIntPipe) ruleId: number,
    @Param("conditionId", ParseIntPipe) conditionId: number
  ) {
    await this.matchingConditionService.deleteMatchingCondition(ruleId, conditionId);
    return;
  }

  @HttpCode(200)
  @Post(":ruleId/conditions/batch")
  @ApiOperation({ summary: "Batch update Matching Conditions" })
  @ApiParam({ name: "ruleId", type: "integer", description: "Matching Rule ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: BatchUpdateMatchingConditionsResponseDto })
  async batchUpdateMatchingConditions(
    @Param("ruleId", ParseIntPipe) ruleId: number,
    @Body() batchUpdateMatchingConditionsDto: BatchUpdateMatchingConditionsDto
  ) {
    return await this.matchingConditionService.batchUpdateMatchingConditions(
      ruleId,
      batchUpdateMatchingConditionsDto
    );
  }

  @HttpCode(200)
  @Post("query-results")
  @ApiOperation({
    summary: "Query Matching Results",
    description: "Query source records given a destination record"
  })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: QueryMatchingResultsResponseDto })
  async queryMatchingResults(@Body() queryMatchingResultsDto: QueryMatchingResultsDto) {
    return await this.ruleQueryService.queryMatchingResults(queryMatchingResultsDto);
  }

  @HttpCode(200)
  @Post("reverse-query-results")
  @ApiOperation({
    summary: "Reverse-query Matching Results",
    description: "Query destination records given conditions"
  })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: ReverseQueryMatchingResultsResponseDto })
  async reverseQueryMatchingResults(@Body() reverseQueryMatchingResultsDto: ReverseQueryMatchingResultsDto) {
    return await this.ruleQueryService.reverseQueryMatchingResults(reverseQueryMatchingResultsDto);
  }

  @HttpCode(200)
  @Post("query-multiple-results")
  @ApiOperation({
    summary: "Query Multiple Matching Results",
    description: "Query source records given multiple destination records"
  })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: QueryMatchingResultsWithDestinationIdResponseDto, isArray: true })
  async queryMultipleMatchingResults(
    @Body() queryMultipleMatchingResultsDto: QueryMultipleMatchingResultsDto
  ) {
    return await this.ruleQueryService.queryMultipleMatchingResults(queryMultipleMatchingResultsDto);
  }
}
