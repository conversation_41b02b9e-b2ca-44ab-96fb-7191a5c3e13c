import {
  BadRequestException,
  ForbiddenException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import assert from "assert/strict";
import { isDateString } from "class-validator";
import { ArrayContains, DataSource, In, Repository } from "typeorm";
import { QueryRunner } from "typeorm/browser";
import {
  BatchUpdateMatchingConditionsDto,
  BatchUpdateMatchingConditionsResponseDto,
  CreateMatchingConditionDto,
  EditMatchingConditionDto,
  GetMatchingConditionsDto,
  GetMatchingConditionsResponseDto
} from "../../dto";
import { Country, MatchingCondition, MatchingRule, Organization, TradePartner } from "../../entities";
import { getFindOptions } from "../../helper-functions";
import {
  ATTRIBUTE_TYPE_DB_COLUMN_MAPPING,
  ATTRIBUTE_TYPE_SUPPORT_OPERATORS,
  AuthenticatedRequest,
  COUNTRY_ID_ATTRIBUTES,
  DESTINATION_TABLE_ATTRIBUTES,
  DESTINATION_TABLE_CASE_INSENSITIVE_ATTRIBUTES,
  FIND_COUNTRY_RELATIONS,
  FIND_MATCHING_CONDITION_RELATIONS,
  FIND_ORGANIZATION_RELATIONS,
  FIND_TRADE_PARTNER_RELATIONS,
  MATCHING_CONDITION_ENUM_KEYS,
  MATCHING_CONDITION_REQUIRED_KEYS,
  MATCHING_RULE_MODULE_OPTIONS,
  MatchingConditionAttributeType,
  MatchingConditionColumn,
  MatchingConditionOperator,
  MatchingRuleModuleOptions,
  MatchingRuleStatus,
  ORGANIZATION_ID_ATTRIBUTES,
  TRADE_PARTNER_ID_ATTRIBUTES,
  TYPED_VALUE_DB_COLUMNS,
  UserPermission
} from "../../types";
import { MatchingRuleService } from "./matching-rule.service";

@Injectable({ scope: Scope.REQUEST })
export class MatchingConditionService {
  constructor(
    @InjectRepository(MatchingCondition)
    private readonly matchingConditionRepository: Repository<MatchingCondition>,
    @Inject(forwardRef(() => MatchingRuleService))
    private readonly matchingRuleService: MatchingRuleService,
    @Inject(MATCHING_RULE_MODULE_OPTIONS)
    private readonly options: MatchingRuleModuleOptions,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}

  validateConditionValues(
    value: string | null,
    attributeType: MatchingConditionAttributeType,
    operator: MatchingConditionOperator
  ): { isValid: boolean; errorMessage?: string; parsedValueList?: Array<string | number | boolean | Date> } {
    const INTEGER_REGEX = /^(0|-?[1-9]\d*)$/;
    const FLOAT_REGEX = /^(0|-?[1-9]\d*)(\.\d+)?$/;
    if (!ATTRIBUTE_TYPE_SUPPORT_OPERATORS[attributeType].includes(operator))
      return {
        isValid: false,
        errorMessage: `Operator ${operator} is not supported for attribute type ${attributeType}`
      };
    const valueList: Array<any> = [];
    let parsedValueList: Array<string | number | boolean | Date> | null | undefined = undefined;
    if (
      [
        MatchingConditionOperator.IN,
        MatchingConditionOperator.BETWEEN_INCLUSIVE,
        MatchingConditionOperator.BETWEEN_EXCLUSIVE
      ].includes(operator)
    ) {
      try {
        const tmpValueList = JSON.parse(value);
        assert(
          Array.isArray(tmpValueList) &&
            ([
              MatchingConditionOperator.BETWEEN_INCLUSIVE,
              MatchingConditionOperator.BETWEEN_EXCLUSIVE
            ].includes(operator)
              ? tmpValueList.length === 2
              : tmpValueList.length > 0)
        );
        valueList.push(...tmpValueList);
      } catch {
        return {
          isValid: false,
          errorMessage: [
            MatchingConditionOperator.BETWEEN_INCLUSIVE,
            MatchingConditionOperator.BETWEEN_EXCLUSIVE
          ].includes(operator)
            ? `Value is not a valid 2-element array`
            : `Value is not a valid and non-empty array`
        };
      }
    } else valueList.push(value);
    if (valueList.some((v) => v === null)) {
      if (operator !== MatchingConditionOperator.EQUALS)
        return { isValid: false, errorMessage: "Null value is only supported for equals operator" };
      parsedValueList = null;
    } else {
      switch (attributeType) {
        case MatchingConditionAttributeType.ID:
          if (
            [
              MatchingConditionOperator.IN,
              MatchingConditionOperator.BETWEEN_INCLUSIVE,
              MatchingConditionOperator.BETWEEN_EXCLUSIVE
            ].includes(operator)
              ? !valueList.every((v) => typeof v === "number" && Number.isSafeInteger(v) && v > 0)
              : !(
                  typeof valueList[0] === "string" &&
                  INTEGER_REGEX.test(valueList[0]) &&
                  Number.isSafeInteger(Number(valueList[0])) &&
                  Number(valueList[0]) > 0
                )
          )
            return {
              isValid: false,
              errorMessage: valueList.length > 1 ? "Values are not all IDs" : "Value is not an ID"
            };
          parsedValueList = valueList.map((v) => (typeof v === "number" ? v : Number(v)));
          break;
        case MatchingConditionAttributeType.STRING:
          if (!valueList.every((v) => typeof v === "string"))
            return {
              isValid: false,
              errorMessage: valueList.length > 1 ? "Values are not all strings" : "Value is not a string"
            };
          parsedValueList = [...valueList];
          break;
        case MatchingConditionAttributeType.INTEGER:
          if (
            [
              MatchingConditionOperator.BETWEEN_INCLUSIVE,
              MatchingConditionOperator.BETWEEN_EXCLUSIVE,
              MatchingConditionOperator.IN
            ].includes(operator)
              ? !valueList.every((v) => typeof v === "number" && Number.isSafeInteger(v))
              : !(
                  typeof valueList[0] === "string" &&
                  INTEGER_REGEX.test(valueList[0]) &&
                  Number.isSafeInteger(Number(valueList[0]))
                )
          )
            return {
              isValid: false,
              errorMessage: valueList.length > 1 ? "Values are not all integers" : "Value is not an integer"
            };
          parsedValueList = valueList.map((v) => (typeof v === "number" ? v : Number(v)));
          break;
        case MatchingConditionAttributeType.FLOAT:
          if (
            [
              MatchingConditionOperator.BETWEEN_INCLUSIVE,
              MatchingConditionOperator.BETWEEN_EXCLUSIVE,
              MatchingConditionOperator.IN
            ].includes(operator)
              ? !valueList.every((v) => typeof v === "number" && Number.isFinite(v))
              : !(
                  typeof valueList[0] === "string" &&
                  FLOAT_REGEX.test(valueList[0]) &&
                  Number.isFinite(Number(valueList[0]))
                )
          )
            return {
              isValid: false,
              errorMessage: valueList.length > 1 ? "Values are not all numbers" : "Value is not a number"
            };
          parsedValueList = valueList.map((v) => (typeof v === "number" ? v : Number(v)));
          break;
        case MatchingConditionAttributeType.BOOLEAN:
          if (
            [
              MatchingConditionOperator.IN,
              MatchingConditionOperator.BETWEEN_INCLUSIVE,
              MatchingConditionOperator.BETWEEN_EXCLUSIVE
            ].includes(operator)
              ? !valueList.every((v) => typeof v === "boolean")
              : !(
                  typeof valueList[0] === "string" &&
                  ["true", "false"].includes(valueList[0].trim().toLowerCase())
                )
          )
            return {
              isValid: false,
              errorMessage: valueList.length > 1 ? "Values are not all booleans" : "Value is not a boolean"
            };
          parsedValueList = valueList.map((v) =>
            typeof v === "boolean" ? v : v.trim().toLowerCase() === "true"
          );
          break;
        case MatchingConditionAttributeType.DATE_TIME:
          if (!valueList.every((v) => typeof v === "string" && isDateString(v, { strict: true })))
            return {
              isValid: false,
              errorMessage: valueList.length > 1 ? "Values are not all datetimes" : "Value is not a datetime"
            };
          parsedValueList = valueList.map((v) => new Date(v));
          break;
        default:
          return { isValid: false, errorMessage: `Attribute type ${attributeType} is not supported` };
      }
      if (
        [MatchingConditionOperator.BETWEEN_INCLUSIVE, MatchingConditionOperator.BETWEEN_EXCLUSIVE].includes(
          operator
        ) &&
        parsedValueList[0] > parsedValueList[1]
      )
        return {
          isValid: false,
          errorMessage: "First value must be less than or equal to the second value"
        };
    }
    return { isValid: true, parsedValueList };
  }

  async setConditionValueRecords(
    conditions: Array<MatchingCondition>,
    destinationTableAttributes: Record<string, MatchingConditionAttributeType>,
    queryRunner?: QueryRunner
  ) {
    function validateId(valueInteger: Array<number> | null) {
      if (valueInteger === null) return false;
      return (
        Array.isArray(valueInteger) &&
        valueInteger.length > 0 &&
        !Number.isNaN(valueInteger[0]) &&
        Number.isSafeInteger(valueInteger[0]) &&
        valueInteger[0] > 0
      );
    }

    const conditionsWithIdAttribute = conditions.filter(
      (c) => c.attributeType === MatchingConditionAttributeType.ID
    );
    const tradePartnerConditions = conditionsWithIdAttribute.filter((c) =>
      TRADE_PARTNER_ID_ATTRIBUTES.includes(c.attribute)
    );
    const countryConditions = conditionsWithIdAttribute.filter((c) =>
      COUNTRY_ID_ATTRIBUTES.includes(c.attribute)
    );
    const organizationConditions = conditionsWithIdAttribute.filter((c) =>
      ORGANIZATION_ID_ATTRIBUTES.includes(c.attribute)
    );

    const tradePartnerIds: Array<number> = [];
    for (const c of tradePartnerConditions) {
      if (validateId(c.valueInteger) && !tradePartnerIds.includes(c.valueInteger[0]))
        tradePartnerIds.push(c.valueInteger[0]);
    }

    const countryIds: Array<number> = [];
    for (const c of countryConditions) {
      if (validateId(c.valueInteger) && !countryIds.includes(c.valueInteger[0]))
        countryIds.push(c.valueInteger[0]);
    }

    const organizationIds: Array<number> = [];
    for (const c of organizationConditions) {
      if (validateId(c.valueInteger) && !organizationIds.includes(c.valueInteger[0]))
        organizationIds.push(c.valueInteger[0]);
    }

    const relatedTradePartners =
      tradePartnerIds.length > 0
        ? await (queryRunner ? queryRunner.manager : this.dataSource.manager).find(TradePartner, {
            where: { id: In(tradePartnerIds) },
            relations: FIND_TRADE_PARTNER_RELATIONS
          })
        : [];
    const relatedCountries =
      countryIds.length > 0
        ? await (queryRunner ? queryRunner.manager : this.dataSource.manager).find(Country, {
            where: { id: In(countryIds) },
            relations: FIND_COUNTRY_RELATIONS
          })
        : [];
    const relatedOrganizations =
      organizationIds.length > 0
        ? await (queryRunner ? queryRunner.manager : this.dataSource.manager).find(Organization, {
            where: { id: In(organizationIds) },
            relations: FIND_ORGANIZATION_RELATIONS
          })
        : [];

    const tradePartnerMap = new Map(relatedTradePartners.map((tp) => [tp.id, tp]));
    const countryMap = new Map(relatedCountries.map((c) => [c.id, c]));
    const organizationMap = new Map(relatedOrganizations.map((o) => [o.id, o]));

    const mappedConditions: Array<MatchingCondition> = [];
    for (const condition of conditions) {
      if (tradePartnerConditions.find((tpc) => tpc.id === condition.id))
        mappedConditions.push({
          ...condition,
          valueRecord: validateId(condition.valueInteger)
            ? tradePartnerMap.get(condition.valueInteger[0]) || null
            : null
        });
      else if (countryConditions.find((cc) => cc.id === condition.id))
        mappedConditions.push({
          ...condition,
          valueRecord: validateId(condition.valueInteger)
            ? countryMap.get(condition.valueInteger[0]) || null
            : null
        });
      else if (organizationConditions.find((oc) => oc.id === condition.id))
        mappedConditions.push({
          ...condition,
          valueRecord: validateId(condition.valueInteger)
            ? organizationMap.get(condition.valueInteger[0]) || null
            : null
        });
      else
        mappedConditions.push({
          ...condition,
          valueRecord: null
        });
    }

    return mappedConditions;
  }

  async getMatchingConditions(
    ruleId: number,
    getMatchingConditionsDto: GetMatchingConditionsDto
  ): Promise<GetMatchingConditionsResponseDto> {
    if (!this.options.readMany) throw new ForbiddenException("Get matching conditions is disabled");
    const matchingRule = await this.matchingRuleService.getMatchingRuleById(ruleId);
    if (!matchingRule) throw new NotFoundException("Matching rule not found");
    const { valueString, valueInteger, valueFloat, valueBoolean, valueDateTime, ...restOfDto } =
      getMatchingConditionsDto;
    const { where, order, take, skip } = getFindOptions<MatchingCondition>(
      { ...restOfDto, ruleId },
      MATCHING_CONDITION_ENUM_KEYS,
      [],
      MatchingConditionColumn.id
    );
    if (typeof valueString === "string") where.valueString = ArrayContains([valueString]);
    if (typeof valueInteger === "number") where.valueInteger = ArrayContains([valueInteger]);
    if (typeof valueFloat === "number") where.valueFloat = ArrayContains([valueFloat]);
    if (typeof valueBoolean === "boolean") where.valueBoolean = ArrayContains([valueBoolean]);
    if (valueDateTime instanceof Date) where.valueDateTime = ArrayContains([valueDateTime]);

    const [matchingConditions, total] = await this.matchingConditionRepository.findAndCount({
      where,
      order,
      relations: FIND_MATCHING_CONDITION_RELATIONS,
      take,
      skip
    });
    const mappedConditions = await this.setConditionValueRecords(
      matchingConditions,
      DESTINATION_TABLE_ATTRIBUTES[matchingRule.destinationTable] || {}
    );
    return { matchingConditions: mappedConditions, total, skip, limit: take };
  }

  async getMatchingConditionById(ruleId: number, conditionId: number, queryRunner?: QueryRunner) {
    if (!this.options.readOne) throw new ForbiddenException("Get matching condition is disabled");
    const matchingRule = await this.matchingRuleService.getMatchingRuleById(ruleId, queryRunner);
    if (!matchingRule) throw new NotFoundException("Matching rule not found");
    const condition = await (
      queryRunner ? queryRunner.manager.getRepository(MatchingCondition) : this.matchingConditionRepository
    ).findOne({
      where: { id: conditionId, rule: { id: matchingRule.id } },
      relations: FIND_MATCHING_CONDITION_RELATIONS
    });
    if (!condition) return null;

    const [mappedCondition] = await this.setConditionValueRecords(
      [condition],
      DESTINATION_TABLE_ATTRIBUTES[matchingRule.destinationTable] || {}
    );
    return mappedCondition;
  }

  async createMatchingCondition(ruleId: number, createMatchingConditionDto: CreateMatchingConditionDto) {
    if (!this.options.create) throw new ForbiddenException("Create matching condition is disabled");
    const matchingRule = await this.matchingRuleService.getMatchingRuleById(ruleId);
    if (!matchingRule) throw new NotFoundException("Matching rule not found");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN) {
      if (matchingRule.organization === null)
        throw new ForbiddenException(
          "Current user is not allowed to create conditions for global matching rules"
        );
      if (createMatchingConditionDto.attribute === "organizationId")
        throw new ForbiddenException("Current user is not allowed to create conditions with organizationId");
    }
    if (matchingRule.status === MatchingRuleStatus.ACTIVE)
      throw new BadRequestException("Active matching rule cannot be edited");
    const attributeType =
      DESTINATION_TABLE_ATTRIBUTES[matchingRule.destinationTable][createMatchingConditionDto.attribute];
    if (!attributeType)
      throw new BadRequestException(
        `Attribute must be one of the following: ${Object.keys(
          DESTINATION_TABLE_ATTRIBUTES[matchingRule.destinationTable]
        ).join(", ")}`
      );
    const { isValid, errorMessage, parsedValueList } = this.validateConditionValues(
      createMatchingConditionDto.value === undefined ? null : createMatchingConditionDto.value,
      attributeType,
      createMatchingConditionDto.operator
    );
    if (!isValid) throw new BadRequestException(errorMessage);
    if (parsedValueList === undefined) throw new BadRequestException("Cannot parse condition value");

    const dbColumnName = ATTRIBUTE_TYPE_DB_COLUMN_MAPPING[attributeType];
    if (!dbColumnName) throw new BadRequestException(`Unsupported attribute type ${attributeType}`);

    const caseInsensitiveAttributes =
      DESTINATION_TABLE_CASE_INSENSITIVE_ATTRIBUTES[matchingRule.destinationTable] ?? [];
    if (
      caseInsensitiveAttributes.includes(createMatchingConditionDto.attribute) &&
      Array.isArray(parsedValueList) &&
      parsedValueList.every((v) => typeof v === "string")
    ) {
      createMatchingConditionDto.value = createMatchingConditionDto.value.toUpperCase();
      parsedValueList.forEach((v, i) => (parsedValueList[i] = v.toUpperCase()));
    }

    let newCondition = new MatchingCondition();
    for (const [key, value] of Object.entries({
      ...createMatchingConditionDto,
      rule: matchingRule,
      attributeType,
      [dbColumnName]: parsedValueList,
      createdBy: this.request?.user || null,
      lastEditedBy: this.request?.user || null
    })) {
      if (value === undefined) continue;
      if (MATCHING_CONDITION_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${key} is required`);
      newCondition[key] = value;
    }
    newCondition = await this.matchingConditionRepository.save(newCondition);
    return await this.getMatchingConditionById(ruleId, newCondition.id);
  }

  async editMatchingCondition(
    ruleId: number,
    conditionId: number,
    editMatchingConditionDto: EditMatchingConditionDto
  ) {
    if (!this.options.update) throw new ForbiddenException("Edit matching condition is disabled");
    const matchingRule = await this.matchingRuleService.getMatchingRuleById(ruleId);
    if (!matchingRule) throw new NotFoundException("Matching rule not found");
    if (
      this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN &&
      matchingRule.organization === null
    )
      throw new ForbiddenException(
        "Current user is not allowed to edit conditions for global matching rules"
      );
    if (matchingRule.status === MatchingRuleStatus.ACTIVE)
      throw new BadRequestException("Active matching rule cannot be edited");
    let condition = await this.getMatchingConditionById(ruleId, conditionId);
    if (!condition) throw new NotFoundException("Matching condition not found");
    if (
      this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN &&
      (editMatchingConditionDto.attribute || condition.attribute) === "organizationId"
    )
      throw new ForbiddenException("Current user is not allowed to edit conditions with organizationId");
    for (const [key, value] of Object.entries({
      ...editMatchingConditionDto,
      lastEditedBy: this.request?.user || null
    })) {
      if (value === undefined) continue;
      if (MATCHING_CONDITION_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${key} is required`);
      condition[key] = value;
    }

    condition.attributeType =
      DESTINATION_TABLE_ATTRIBUTES[matchingRule.destinationTable][condition.attribute];
    if (!condition.attributeType)
      throw new BadRequestException(
        `Attribute must be one of the following: ${Object.keys(
          DESTINATION_TABLE_ATTRIBUTES[matchingRule.destinationTable]
        ).join(", ")}`
      );
    const { isValid, errorMessage, parsedValueList } = this.validateConditionValues(
      condition.value === undefined ? null : condition.value,
      condition.attributeType,
      condition.operator
    );
    if (!isValid) throw new BadRequestException(errorMessage);

    const targetColumnName = ATTRIBUTE_TYPE_DB_COLUMN_MAPPING[condition.attributeType];
    if (!targetColumnName)
      throw new BadRequestException(`Unsupported attribute type ${condition.attributeType}`);

    const caseInsensitiveAttributes =
      DESTINATION_TABLE_CASE_INSENSITIVE_ATTRIBUTES[matchingRule.destinationTable] ?? [];
    if (
      caseInsensitiveAttributes.includes(condition.attribute) &&
      Array.isArray(parsedValueList) &&
      parsedValueList.every((v) => typeof v === "string")
    ) {
      condition.value = condition.value.toUpperCase();
      parsedValueList.forEach((v, i) => (parsedValueList[i] = v.toUpperCase()));
    }

    for (const column of TYPED_VALUE_DB_COLUMNS)
      condition[column] = column === targetColumnName ? parsedValueList : null;

    condition = await this.matchingConditionRepository.save(condition);
    return await this.getMatchingConditionById(ruleId, condition.id);
  }

  async deleteMatchingCondition(ruleId: number, conditionId: number) {
    if (!this.options.delete) throw new ForbiddenException("Delete matching condition is disabled");
    const matchingRule = await this.matchingRuleService.getMatchingRuleById(ruleId);
    if (!matchingRule) throw new NotFoundException("Matching rule not found");
    if (
      this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN &&
      matchingRule.organization === null
    )
      throw new ForbiddenException(
        "Current user is not allowed to delete conditions for global matching rules"
      );
    if (matchingRule.status === MatchingRuleStatus.ACTIVE)
      throw new BadRequestException("Active matching rule cannot be edited");
    const condition = await this.getMatchingConditionById(ruleId, conditionId);
    if (!condition) throw new NotFoundException("Matching condition not found");
    if (
      this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN &&
      condition.attribute === "organizationId"
    )
      throw new ForbiddenException("Current user is not allowed to delete conditions with organizationId");
    await this.matchingConditionRepository.delete({ id: condition.id });
    return;
  }

  async batchUpdateMatchingConditions(
    ruleId: number,
    batchUpdateMatchingConditionsDto: BatchUpdateMatchingConditionsDto,
    queryRunner?: QueryRunner
  ): Promise<BatchUpdateMatchingConditionsResponseDto> {
    // const queryRunner = this.dataSource.createQueryRunner();
    // await queryRunner.connect();
    // await queryRunner.startTransaction();
    let tQueryRunner = queryRunner;
    if (!tQueryRunner) {
      tQueryRunner = this.dataSource.createQueryRunner();
      await tQueryRunner.connect();
      await tQueryRunner.startTransaction();
    }

    const savedConditionIds: Array<number> = [];
    let matchingRule: MatchingRule | null = null;
    try {
      matchingRule = await this.matchingRuleService.getMatchingRuleById(ruleId, tQueryRunner);
      if (!matchingRule) throw new NotFoundException("Matching rule not found");
      if (
        this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN &&
        matchingRule.organization === null
      )
        throw new ForbiddenException(
          "Current user is not allowed to create/edit/delete conditions for global matching rules"
        );
      if (matchingRule.status === MatchingRuleStatus.ACTIVE)
        throw new BadRequestException("Active matching rule cannot be edited");

      const matchingConditionRepository = tQueryRunner.manager.getRepository(MatchingCondition);
      const { create: createList, edit: editList, delete: deleteList } = batchUpdateMatchingConditionsDto;
      const toBeSavedConditions: Array<MatchingCondition> = [];
      if (Array.isArray(createList) && createList.length > 0 && !this.options.create)
        throw new ForbiddenException("Create matching condition is disabled");
      if (Array.isArray(editList) && editList.length > 0 && !this.options.update)
        throw new ForbiddenException("Edit matching condition is disabled");
      if (Array.isArray(deleteList) && deleteList.length > 0 && !this.options.delete)
        throw new ForbiddenException("Delete matching condition is disabled");

      for (let i = 0; i < (createList || []).length; i++) {
        const createConditionDto = createList[i];
        const errorMsgPrefix = `Error on create list index ${i}: `;
        if (
          this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN &&
          createConditionDto.attribute === "organizationId"
        )
          throw new ForbiddenException(
            `${errorMsgPrefix}Current user is not allowed to create conditions with organizationId`
          );
        const attributeType =
          DESTINATION_TABLE_ATTRIBUTES[matchingRule.destinationTable][createConditionDto.attribute];
        if (!attributeType)
          throw new BadRequestException(
            `${errorMsgPrefix}Attribute must be one of the following: ${Object.keys(
              DESTINATION_TABLE_ATTRIBUTES[matchingRule.destinationTable]
            ).join(", ")}`
          );
        const { isValid, errorMessage, parsedValueList } = this.validateConditionValues(
          createConditionDto.value === undefined ? null : createConditionDto.value,
          attributeType,
          createConditionDto.operator
        );
        if (!isValid) throw new BadRequestException(`${errorMsgPrefix}${errorMessage}`);

        const dbColumnName = ATTRIBUTE_TYPE_DB_COLUMN_MAPPING[attributeType];
        if (!dbColumnName)
          throw new BadRequestException(`${errorMsgPrefix}Unsupported attribute type ${attributeType}`);

        const caseInsensitiveAttributes =
          DESTINATION_TABLE_CASE_INSENSITIVE_ATTRIBUTES[matchingRule.destinationTable] ?? [];
        if (
          caseInsensitiveAttributes.includes(createConditionDto.attribute) &&
          Array.isArray(parsedValueList) &&
          parsedValueList.every((v) => typeof v === "string")
        ) {
          createConditionDto.value = createConditionDto.value.toUpperCase();
          parsedValueList.forEach((v, i) => (parsedValueList[i] = v.toUpperCase()));
        }

        const newCondition = new MatchingCondition();
        for (const [key, value] of Object.entries({
          ...createConditionDto,
          rule: matchingRule,
          attributeType,
          [dbColumnName]: parsedValueList,
          createdBy: this.request?.user || null,
          lastEditedBy: this.request?.user || null
        })) {
          if (value === undefined) continue;
          if (MATCHING_CONDITION_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${key} is required`);
          newCondition[key] = value;
        }
        toBeSavedConditions.push(newCondition);
      }

      for (let i = 0; i < (editList || []).length; i++) {
        const editConditionDto = editList[i];
        const errorMsgPrefix = `Error on edit list index ${i}: `;
        const condition = await this.getMatchingConditionById(ruleId, editConditionDto.id, tQueryRunner);
        if (!condition) throw new NotFoundException(`${errorMsgPrefix}Matching condition not found`);
        if (
          this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN &&
          (editConditionDto.attribute || condition.attribute) === "organizationId"
        )
          throw new ForbiddenException(
            `${errorMsgPrefix}Current user is not allowed to edit conditions with organizationId`
          );
        for (const [key, value] of Object.entries({
          ...editConditionDto,
          lastEditedBy: this.request?.user || null
        })) {
          if (value === undefined) continue;
          if (MATCHING_CONDITION_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${key} is required`);
          condition[key] = value;
        }

        condition.attributeType =
          DESTINATION_TABLE_ATTRIBUTES[matchingRule.destinationTable][condition.attribute];
        if (!condition.attributeType)
          throw new BadRequestException(
            `${errorMsgPrefix}Attribute must be one of the following: ${Object.keys(
              DESTINATION_TABLE_ATTRIBUTES[matchingRule.destinationTable]
            ).join(", ")}`
          );
        const { isValid, errorMessage, parsedValueList } = this.validateConditionValues(
          condition.value === undefined ? null : condition.value,
          condition.attributeType,
          condition.operator
        );
        if (!isValid) throw new BadRequestException(`${errorMsgPrefix}${errorMessage}`);

        const targetColumnName = ATTRIBUTE_TYPE_DB_COLUMN_MAPPING[condition.attributeType];
        if (!targetColumnName)
          throw new BadRequestException(
            `${errorMsgPrefix}Unsupported attribute type ${condition.attributeType}`
          );

        const caseInsensitiveAttributes =
          DESTINATION_TABLE_CASE_INSENSITIVE_ATTRIBUTES[matchingRule.destinationTable] ?? [];
        if (
          caseInsensitiveAttributes.includes(condition.attribute) &&
          Array.isArray(parsedValueList) &&
          parsedValueList.every((v) => typeof v === "string")
        ) {
          condition.value = condition.value.toUpperCase();
          parsedValueList.forEach((v, i) => (parsedValueList[i] = v.toUpperCase()));
        }

        for (const column of TYPED_VALUE_DB_COLUMNS)
          condition[column] = column === targetColumnName ? parsedValueList : null;

        toBeSavedConditions.push(condition);
      }

      for (let i = 0; i < (deleteList || []).length; i++) {
        const conditionId = deleteList[i];
        const errorMsgPrefix = `Error on delete list index ${i}: `;
        const condition = await this.getMatchingConditionById(ruleId, conditionId, tQueryRunner);
        if (!condition) throw new NotFoundException(`${errorMsgPrefix}Matching condition not found`);
        if (
          this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN &&
          condition.attribute === "organizationId"
        )
          throw new ForbiddenException(
            `${errorMsgPrefix}Current user is not allowed to delete conditions with organizationId`
          );
      }

      savedConditionIds.push(
        ...(await matchingConditionRepository.save(toBeSavedConditions)).map((c) => c.id)
      );
      if (Array.isArray(deleteList) && deleteList.length > 0)
        await matchingConditionRepository.delete({ id: In(deleteList) });

      if (!queryRunner) await tQueryRunner.commitTransaction();
    } catch (error) {
      if (!queryRunner && tQueryRunner.isTransactionActive) await tQueryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner && !tQueryRunner.isReleased) await tQueryRunner.release();
    }

    const savedConditions = await (
      queryRunner ? queryRunner.manager.getRepository(MatchingCondition) : this.matchingConditionRepository
    ).find({ where: { id: In(savedConditionIds) }, relations: FIND_MATCHING_CONDITION_RELATIONS });
    const mappedConditions = await this.setConditionValueRecords(
      savedConditions,
      DESTINATION_TABLE_ATTRIBUTES[matchingRule?.destinationTable] || {}
    );
    return { matchingConditions: mappedConditions };
  }
}
