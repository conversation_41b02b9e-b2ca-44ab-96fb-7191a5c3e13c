import {
  BadRequestException,
  ForbiddenException,
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import assert from "assert";
import { DataSource, FindOptionsRelations, In, IsNull, LessThanOrEqual, Not, Raw, Repository } from "typeorm";
import { QueryRunner } from "typeorm/browser";
import {
  CreateMatchingConditionDto,
  CreateMatchingRuleDto,
  EditMatchingRuleDto,
  GetMatchingRulesDto,
  GetMatchingRulesResponseDto,
  UpdateMatchingRuleStatusDto
} from "../../dto";
import {
  CanadaAntiDumping,
  CanadaExciseTaxCode,
  CanadaGstExemptCode,
  CanadaOgd,
  CanadaSimaCode,
  CanadaTariff,
  CanadaTreatmentCode,
  CanadaVfdCode,
  CertificateOfOrigin,
  MatchingCondition,
  MatchingHistory,
  MatchingRule,
  OgdFiling,
  Organization,
  Product,
  SimaFiling
} from "../../entities";
import { convertFromCamelCase, getFindOptions } from "../../helper-functions";
import {
  AuthenticatedRequest,
  DESTINATION_TABLE_ATTRIBUTES,
  DESTINATION_TABLE_CASE_INSENSITIVE_ATTRIBUTES,
  FIND_CANADA_ANTI_DUMPING_RELATIONS,
  FIND_CANADA_EXCISE_TAX_CODE_RELATIONS,
  FIND_CANADA_GST_EXEMPT_CODE_RELATIONS,
  FIND_CANADA_OGD_RELATIONS,
  FIND_CANADA_SIMA_CODE_RELATIONS,
  FIND_CANADA_TARIFF_RELATIONS,
  FIND_CANADA_TREATMENT_CODE_RELATIONS,
  FIND_CANADA_VFD_CODE_RELATIONS,
  FIND_CERTIFICATE_OF_ORIGIN_RELATIONS,
  FIND_MATCHING_RULE_RELATIONS,
  FIND_OGD_FILING_RELATIONS,
  FIND_PRODUCT_RELATIONS,
  FIND_SIMA_FILING_RELATIONS,
  MATCHING_RULE_ENUM_KEYS,
  MATCHING_RULE_MODULE_OPTIONS,
  MATCHING_RULE_NON_ID_KEYS,
  MATCHING_RULE_REQUIRED_KEYS,
  MatchingConditionOperator,
  MatchingRuleColumn,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleEvent,
  MatchingRuleModuleOptions,
  MatchingRuleOperator,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus,
  UserPermission
} from "../../types";
import { MatchingConditionService } from "./matching-condition.service";
import { RuleQueryService } from "./rule-query.service";
import moment from "moment-timezone";

@Injectable({ scope: Scope.REQUEST })
export class MatchingRuleService {
  constructor(
    @InjectRepository(MatchingRule)
    private readonly matchingRuleRepository: Repository<MatchingRule>,
    @InjectRepository(MatchingHistory)
    private readonly matchingHistoryRepository: Repository<MatchingHistory>,
    @Inject(forwardRef(() => MatchingConditionService))
    private readonly matchingConditionService: MatchingConditionService,
    @Inject(forwardRef(() => RuleQueryService))
    private readonly ruleQueryService: RuleQueryService,
    @Inject(MATCHING_RULE_MODULE_OPTIONS)
    private readonly options: MatchingRuleModuleOptions,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}
  private readonly logger = new Logger(MatchingRuleService.name);

  static getSourceTableEntityAndRelations(sourceTable: MatchingRuleSourceDatabaseTable): {
    entity: Function;
    relations: FindOptionsRelations<any>;
    isOrganizationSpecific: boolean;
  } {
    switch (sourceTable) {
      case MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING:
        return {
          entity: CanadaAntiDumping,
          relations: FIND_CANADA_ANTI_DUMPING_RELATIONS,
          isOrganizationSpecific: false
        };
      case MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE:
        return {
          entity: CanadaGstExemptCode,
          relations: FIND_CANADA_GST_EXEMPT_CODE_RELATIONS,
          isOrganizationSpecific: false
        };
      case MatchingRuleSourceDatabaseTable.CANADA_OGD:
        return {
          entity: CanadaOgd,
          relations: FIND_CANADA_OGD_RELATIONS,
          isOrganizationSpecific: false
        };
      case MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE:
        return {
          entity: CanadaSimaCode,
          relations: FIND_CANADA_SIMA_CODE_RELATIONS,
          isOrganizationSpecific: false
        };
      case MatchingRuleSourceDatabaseTable.CANADA_TARIFF:
        return {
          entity: CanadaTariff,
          relations: FIND_CANADA_TARIFF_RELATIONS,
          isOrganizationSpecific: false
        };
      case MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE:
        return {
          entity: CanadaVfdCode,
          relations: FIND_CANADA_VFD_CODE_RELATIONS,
          isOrganizationSpecific: false
        };
      case MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE:
        return {
          entity: CanadaTreatmentCode,
          relations: FIND_CANADA_TREATMENT_CODE_RELATIONS,
          isOrganizationSpecific: false
        };
      case MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE:
        return {
          entity: CanadaExciseTaxCode,
          relations: FIND_CANADA_EXCISE_TAX_CODE_RELATIONS,
          isOrganizationSpecific: false
        };
      case MatchingRuleSourceDatabaseTable.OGD_FILING:
        return {
          entity: OgdFiling,
          relations: FIND_OGD_FILING_RELATIONS,
          isOrganizationSpecific: true
        };
      case MatchingRuleSourceDatabaseTable.SIMA_FILING:
        return {
          entity: SimaFiling,
          relations: FIND_SIMA_FILING_RELATIONS,
          isOrganizationSpecific: true
        };
      case MatchingRuleSourceDatabaseTable.CERTIFICATE_OF_ORIGIN:
        return {
          entity: CertificateOfOrigin,
          relations: FIND_CERTIFICATE_OF_ORIGIN_RELATIONS,
          isOrganizationSpecific: true
        };
      default:
        throw new BadRequestException("Invalid source table");
    }
  }

  static getDestinationTableEntityAndRelations(destinationTable: MatchingRuleDestinationDatabaseTable): {
    entity: Function;
    relations: FindOptionsRelations<any>;
  } {
    switch (destinationTable) {
      case MatchingRuleDestinationDatabaseTable.PRODUCT:
        return {
          entity: Product,
          relations: FIND_PRODUCT_RELATIONS
        };
      default:
        throw new BadRequestException("Invalid destination table");
    }
  }

  static getDatabaseTableAlias(
    table: MatchingRuleDestinationDatabaseTable | MatchingRuleSourceDatabaseTable
  ) {
    return table.replaceAll(/[^A-Z]/g, "").toLowerCase();
  }

  isMatchingRuleExpired(matchingRule: MatchingRule) {
    return (
      typeof matchingRule.expiryDate === "string" &&
      moment
        .tz(matchingRule.expiryDate, "YYYY-MM-DD", "America/Toronto")
        .isSameOrBefore(moment.tz("America/Toronto"), "day")
    );
  }

  private async isSourceRecordExists(
    sourceTable: MatchingRuleSourceDatabaseTable,
    sourceId: number,
    queryRunner?: QueryRunner
  ) {
    const { entity: sourceTableEntity, isOrganizationSpecific } =
      MatchingRuleService.getSourceTableEntityAndRelations(sourceTable);
    return await (queryRunner ? queryRunner.manager : this.dataSource.manager).existsBy(sourceTableEntity, {
      id: sourceId,
      ...(isOrganizationSpecific && this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN
        ? { organization: { id: this.request?.user?.organization?.id || -1 } }
        : {})
    });
  }

  async isMatchingRuleForSourceRecordExists(
    sourceTable: MatchingRuleSourceDatabaseTable,
    sourceId: number,
    queryRunner?: QueryRunner
  ) {
    return await (
      queryRunner ? queryRunner.manager.getRepository(MatchingRule) : this.matchingRuleRepository
    ).existsBy({
      sourceTable,
      sourceId
    });
  }

  async invalidateMatchingHistories(
    destinationTable: MatchingRuleDestinationDatabaseTable,
    sourceTable?: MatchingRuleSourceDatabaseTable,
    destinationIds?: Array<number>,
    queryRunner?: QueryRunner
  ) {
    console.log(
      `Invalidating histories for source table: ${sourceTable}, destination table: ${destinationTable}, destination IDs: ${destinationIds}...`
    );
    await (
      queryRunner ? queryRunner.manager.getRepository(MatchingHistory) : this.matchingHistoryRepository
    ).update(
      {
        sourceTable: sourceTable || Not(IsNull()),
        destinationTable,
        destinationId: Array.isArray(destinationIds) ? In(destinationIds) : Not(IsNull()),
        invalidDate: IsNull()
      },
      { invalidDate: new Date() }
    );
  }

  // TODO: Fix potential issue with exceeding SQL parameters count limit
  async getMatchingRules(
    getMatchingRulesDto: GetMatchingRulesDto,
    queryRunner?: QueryRunner
  ): Promise<GetMatchingRulesResponseDto> {
    const { destinationId, isGlobal, organizationId, expiryDateFrom, expiryDateTo, ...getManyDto } =
      getMatchingRulesDto;

    if (!this.options.readMany) {
      throw new ForbiddenException("Get matching rules is disabled");
    }

    const isBackofficeAdmin = this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN;

    let isDestinationMatchedRuleIdsSet = false;
    const destinationMatchedRuleIds: Array<number> = [];
    if (typeof destinationId === "number") {
      if (!getManyDto.destinationTable)
        throw new BadRequestException("Destination table is required when destination ID is specified");
      const { entity: destinationTableEntity } = MatchingRuleService.getDestinationTableEntityAndRelations(
        getManyDto.destinationTable
      );
      const destinationTableAlias = MatchingRuleService.getDatabaseTableAlias(getManyDto.destinationTable);
      const destinationTableAttributes = DESTINATION_TABLE_ATTRIBUTES[getManyDto.destinationTable];
      const destinationTableCaseInsensitiveAttributes =
        DESTINATION_TABLE_CASE_INSENSITIVE_ATTRIBUTES[getManyDto.destinationTable] ?? [];
      const conditionMatchingResults = await this.ruleQueryService.getConditionMatchingResults(
        false,
        getManyDto.destinationTable,
        destinationTableEntity,
        destinationTableAttributes,
        destinationTableCaseInsensitiveAttributes,
        destinationTableAlias,
        [{ destinationId }],
        queryRunner
      );
      const matchingRuleResults: Record<
        number,
        { ruleOperator: MatchingRuleOperator; totalCount: number; satisfiedCount: number }
      > = {};
      for (const conditionMatchingResult of conditionMatchingResults) {
        if (!matchingRuleResults[conditionMatchingResult.ruleId])
          matchingRuleResults[conditionMatchingResult.ruleId] = {
            ruleOperator: conditionMatchingResult.ruleOperator,
            totalCount: 0,
            satisfiedCount: 0
          };
        matchingRuleResults[conditionMatchingResult.ruleId].totalCount++;
        matchingRuleResults[conditionMatchingResult.ruleId].satisfiedCount +=
          typeof conditionMatchingResult.rawResult === "boolean" &&
          conditionMatchingResult.rawResult !== conditionMatchingResult.isOperationInverted
            ? 1
            : 0;
      }
      isDestinationMatchedRuleIdsSet = true;
      destinationMatchedRuleIds.push(
        ...Object.entries(matchingRuleResults)
          .filter(([_, { ruleOperator, totalCount, satisfiedCount }]) =>
            ruleOperator === MatchingRuleOperator.OR ? satisfiedCount > 0 : satisfiedCount === totalCount
          )
          .map(([ruleId]) => parseInt(ruleId))
      );
    }

    const { where, order, skip, take } = getFindOptions<MatchingRule>(
      {
        ...getManyDto,
        expiryDateFrom:
          typeof expiryDateFrom === "string"
            ? moment.tz(expiryDateFrom, "YYYY-MM-DD", "America/Toronto").toDate()
            : undefined,
        expiryDateTo:
          typeof expiryDateTo === "string"
            ? moment.tz(expiryDateTo, "YYYY-MM-DD", "America/Toronto").toDate()
            : undefined
      },
      MATCHING_RULE_ENUM_KEYS,
      MATCHING_RULE_NON_ID_KEYS,
      MatchingRuleColumn.id
    );
    const [matchingRules, total] = await (
      queryRunner ? queryRunner.manager.getRepository(MatchingRule) : this.matchingRuleRepository
    ).findAndCount({
      where: {
        ...where,
        id: Raw(
          (alias) => {
            const sql = [
              destinationMatchedRuleIds.length > 0
                ? `${alias} IN (:...destinationMatchedRuleIds)`
                : isDestinationMatchedRuleIdsSet
                  ? `${alias} IS NULL`
                  : ""
            ]
              .filter((sql) => sql.length > 0)
              .join(" AND ");
            return sql.length > 0 ? sql : `${alias} IS NOT NULL`;
          },
          {
            destinationMatchedRuleIds
          }
        ),
        organization: {
          id: Raw(
            (alias) => {
              const globalQuery = `${alias} IS NULL`;
              const orgQuery =
                !isBackofficeAdmin || (isBackofficeAdmin && organizationId)
                  ? `${alias} = :organizationId`
                  : `${alias} IS NOT NULL`;

              return isGlobal === true
                ? globalQuery
                : isGlobal === false
                  ? orgQuery
                  : `${globalQuery} OR ${orgQuery}`;
            },
            {
              organizationId:
                isBackofficeAdmin && organizationId
                  ? organizationId
                  : this.request?.user?.organization?.id || -1
            }
          )
        }
      },
      order,
      relations: FIND_MATCHING_RULE_RELATIONS,
      skip,
      take
    });

    const sourceIds: Record<MatchingRuleSourceDatabaseTable, Array<number>> = {
      [MatchingRuleSourceDatabaseTable.CANADA_ANTI_DUMPING]: [],
      [MatchingRuleSourceDatabaseTable.CANADA_OGD]: [],
      [MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE]: [],
      [MatchingRuleSourceDatabaseTable.CANADA_TARIFF]: [],
      [MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE]: [],
      [MatchingRuleSourceDatabaseTable.CANADA_VFD_CODE]: [],
      [MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE]: [],
      [MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE]: [],
      [MatchingRuleSourceDatabaseTable.OGD_FILING]: [],
      [MatchingRuleSourceDatabaseTable.SIMA_FILING]: [],
      [MatchingRuleSourceDatabaseTable.CERTIFICATE_OF_ORIGIN]: []
    };
    for (const rule of matchingRules) {
      if (!sourceIds[rule.sourceTable].includes(rule.sourceId))
        sourceIds[rule.sourceTable].push(rule.sourceId);
      rule.conditions = await this.matchingConditionService.setConditionValueRecords(
        rule.conditions,
        DESTINATION_TABLE_ATTRIBUTES[rule.destinationTable] || {},
        queryRunner
      );
    }

    for (const [sourceTable, ids] of Object.entries(sourceIds)) {
      if (ids.length <= 0) continue;
      const { entity: sourceTableEntity, relations: sourceTableRelations } =
        MatchingRuleService.getSourceTableEntityAndRelations(sourceTable as MatchingRuleSourceDatabaseTable);
      const sourceRecords = await (queryRunner ?? this.dataSource).manager.find(sourceTableEntity, {
        where: { id: In(ids) },
        relations: sourceTableRelations
      });
      for (let i = 0; i < matchingRules.length; i++)
        if (matchingRules[i].sourceTable === sourceTable)
          matchingRules[i].sourceRecord = sourceRecords.find((r) => r.id === matchingRules[i].sourceId);
    }

    return {
      matchingRules,
      total,
      skip,
      limit: take
    };
  }

  async getMatchingRuleById(matchingRuleId: number, queryRunner?: QueryRunner) {
    if (!this.options.readOne) throw new ForbiddenException("Get matching rule by ID is disabled");
    const rule = await (
      queryRunner ? queryRunner.manager.getRepository(MatchingRule) : this.matchingRuleRepository
    ).findOne({
      where: {
        id: matchingRuleId,
        organization: {
          id: Raw(
            (alias) =>
              [
                `${alias} IS NULL`,
                this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
                  ? `${alias} IS NOT NULL`
                  : `${alias} = :organizationId`
              ].join(" OR "),
            {
              organizationId: this.request?.user?.organization?.id || -1
            }
          )
        }
      },
      relations: FIND_MATCHING_RULE_RELATIONS
    });

    if (rule) {
      const { entity: sourceTableEntity, relations: sourceTableRelations } =
        MatchingRuleService.getSourceTableEntityAndRelations(rule.sourceTable);
      rule.sourceRecord = await this.dataSource.manager.findOne(sourceTableEntity, {
        where: { id: rule.sourceId },
        relations: sourceTableRelations
      });
      rule.conditions = await this.matchingConditionService.setConditionValueRecords(
        rule.conditions,
        DESTINATION_TABLE_ATTRIBUTES[rule.destinationTable] || {}
      );
    } else return null;

    return rule;
  }

  async createMatchingRule(createMatchingRuleDto: CreateMatchingRuleDto, queryRunner?: QueryRunner) {
    let tQueryRunner = queryRunner;
    if (!tQueryRunner) {
      tQueryRunner = this.dataSource.createQueryRunner();
      await tQueryRunner.connect();
      await tQueryRunner.startTransaction();
    }

    try {
      const organizationRepository = tQueryRunner.manager.getRepository(Organization);
      const matchingRuleRepository = tQueryRunner.manager.getRepository(MatchingRule);

      if (!this.options.create) throw new ForbiddenException("Create matching rule is disabled");
      if ([null, undefined].includes(createMatchingRuleDto.priority)) createMatchingRuleDto.priority = 1;
      if ([null, undefined].includes(createMatchingRuleDto.operator))
        createMatchingRuleDto.operator = MatchingRuleOperator.AND;
      const { organizationId, ...dto } = createMatchingRuleDto;

      if (
        await matchingRuleRepository.existsBy({
          name: createMatchingRuleDto.name,
          organization: {
            id:
              this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
                ? organizationId || IsNull()
                : this.request?.user?.organization?.id || -1
          }
        })
      )
        throw new BadRequestException("Matching rule with this name already exists");

      let newMatchingRule = new MatchingRule();
      if (
        this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN ||
        typeof organizationId === "number"
      ) {
        const targetOrgId =
          this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
            ? organizationId
            : this.request?.user?.organization?.id || -1;
        newMatchingRule.organization = await organizationRepository.findOneBy({
          id: targetOrgId
        });
        if (!newMatchingRule.organization)
          throw new NotFoundException(`Organization ${targetOrgId} not found`);
      }
      for (const [key, value] of Object.entries({
        ...dto,
        createdBy: this.request?.user || null,
        lastEditedBy: this.request?.user || null
      })) {
        if (value === undefined) continue;
        if (MATCHING_RULE_REQUIRED_KEYS.includes(key) && value === null)
          throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
        newMatchingRule[key] = value;
      }

      if (this.isMatchingRuleExpired(newMatchingRule))
        throw new BadRequestException("Expiry date cannot be in the past or today");

      // const { entity: sourceTableEntity, isOrganizationSpecific } =
      //   MatchingRuleService.getSourceTableEntityAndRelations(newMatchingRule.sourceTable);
      // if (
      //   !(await tQueryRunner.manager.existsBy(sourceTableEntity, {
      //     id: newMatchingRule.sourceId,
      //     ...(isOrganizationSpecific && this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN
      //       ? {
      //           organization: {
      //             id: this.request?.user?.organization?.id || -1
      //           }
      //         }
      //       : {})
      //   }))
      // )
      if (
        !(await this.isSourceRecordExists(
          newMatchingRule.sourceTable,
          newMatchingRule.sourceId,
          tQueryRunner
        ))
      )
        throw new NotFoundException(`${newMatchingRule.sourceTable} ${newMatchingRule.sourceId} not found`);
      newMatchingRule = await matchingRuleRepository.save(newMatchingRule);

      // No need to invalidate histories as the matching rule is not active by default
      if (!queryRunner) await tQueryRunner.commitTransaction();
      return await this.getMatchingRuleById(newMatchingRule.id, queryRunner);
    } catch (error) {
      if (!queryRunner && tQueryRunner.isTransactionActive) await tQueryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner && !tQueryRunner.isReleased) await tQueryRunner.release();
    }
  }

  async editMatchingRule(
    matchingRuleId: number,
    editMatchingRuleDto: EditMatchingRuleDto,
    queryRunner?: QueryRunner
  ) {
    const matchingRuleRepository = queryRunner
      ? queryRunner.manager.getRepository(MatchingRule)
      : this.matchingRuleRepository;
    const organizationRepository = queryRunner
      ? queryRunner.manager.getRepository(Organization)
      : this.dataSource.manager.getRepository(Organization);

    if (!this.options.update) throw new ForbiddenException("Edit matching rule is disabled");
    let matchingRule = await this.getMatchingRuleById(matchingRuleId, queryRunner);
    if (!matchingRule) throw new NotFoundException("Matching rule not found");
    if (
      this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN &&
      matchingRule.organization === null
    )
      throw new ForbiddenException("Current user is not allowed to edit global rules");
    if (matchingRule.status === MatchingRuleStatus.ACTIVE)
      throw new BadRequestException("Active matching rule cannot be edited");
    if (
      typeof editMatchingRuleDto.destinationTable === "string" &&
      matchingRule.destinationTable !== editMatchingRuleDto.destinationTable &&
      matchingRule.conditions.length > 0
    )
      throw new BadRequestException(
        "Cannot change destination table when there are already conditions in the matching rule"
      );
    const { organizationId, ...dto } = editMatchingRuleDto;
    if (this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN && organizationId !== undefined) {
      if (typeof organizationId === "number") {
        matchingRule.organization = await organizationRepository.findOneBy({ id: organizationId });
        if (!matchingRule.organization)
          throw new NotFoundException(`Organization ${organizationId} not found`);
      } else matchingRule.organization = null;
    }
    for (const [key, value] of Object.entries({
      ...dto,
      lastEditedBy: this.request?.user || null
    })) {
      if (value === undefined) continue;
      if (MATCHING_RULE_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      matchingRule[key] = value;
    }

    if (
      await matchingRuleRepository.existsBy({
        id: Not(matchingRuleId),
        name: matchingRule.name,
        organization: {
          id: matchingRule?.organization?.id || IsNull()
        }
      })
    )
      throw new BadRequestException("Matching rule with this name already exists");
    if (this.isMatchingRuleExpired(matchingRule))
      throw new BadRequestException("Expiry date cannot be in the past or today");
    // const { entity: sourceTableEntity, isOrganizationSpecific } =
    //   MatchingRuleService.getSourceTableEntityAndRelations(matchingRule.sourceTable);
    // if (
    //   !(await (queryRunner ? queryRunner.manager : this.dataSource.manager).existsBy(sourceTableEntity, {
    //     id: matchingRule.sourceId,
    //     ...(isOrganizationSpecific && this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN
    //       ? { organization: { id: this.request?.user?.organization?.id || -1 } }
    //       : {})
    //   }))
    // )
    if (!(await this.isSourceRecordExists(matchingRule.sourceTable, matchingRule.sourceId, queryRunner)))
      throw new NotFoundException(`${matchingRule.sourceTable} ${matchingRule.sourceId} not found`);
    // No need to invalidate histories as active matching rules cannot be edited
    matchingRule = await matchingRuleRepository.save(matchingRule);
    return await this.getMatchingRuleById(matchingRule.id, queryRunner);
  }

  async updateMatchingRuleStatus(
    matchingRuleId: number,
    updateMatchingRuleStatusDto: UpdateMatchingRuleStatusDto,
    queryRunner?: QueryRunner
  ) {
    if (!this.options.update) throw new ForbiddenException("Update matching rule status is disabled");
    const matchingRule = await this.getMatchingRuleById(matchingRuleId, queryRunner);
    if (!matchingRule) throw new NotFoundException("Matching rule not found");
    if (
      this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN &&
      matchingRule.organization === null
    )
      throw new ForbiddenException("Current user is not allowed to update global rules status");
    if (matchingRule.status === updateMatchingRuleStatusDto.status)
      throw new BadRequestException(
        `Matching rule status is already set to the status ${updateMatchingRuleStatusDto.status}`
      );

    if (updateMatchingRuleStatusDto.status === MatchingRuleStatus.ACTIVE) {
      if (this.isMatchingRuleExpired(matchingRule))
        throw new BadRequestException("Expired matching rule cannot be set to active");
      if (!(await this.isSourceRecordExists(matchingRule.sourceTable, matchingRule.sourceId, queryRunner)))
        throw new BadRequestException(`Cannot set matching rule to active when source record does not exist`);
    }

    await (
      queryRunner ? queryRunner.manager.getRepository(MatchingRule) : this.matchingRuleRepository
    ).update({ id: matchingRuleId }, { status: updateMatchingRuleStatusDto.status });
    if (
      matchingRule.status === MatchingRuleStatus.ACTIVE ||
      updateMatchingRuleStatusDto.status === MatchingRuleStatus.ACTIVE
    )
      await this.invalidateMatchingHistories(
        matchingRule.destinationTable,
        matchingRule.sourceTable,
        undefined,
        queryRunner
      );
    return await this.getMatchingRuleById(matchingRuleId, queryRunner);
  }

  async deleteMatchingRule(matchingRuleId: number, queryRunner?: QueryRunner) {
    if (!this.options.delete) throw new ForbiddenException("Delete matching rule is disabled");
    const matchingRule = await this.getMatchingRuleById(matchingRuleId, queryRunner);
    if (!matchingRule) throw new NotFoundException("Matching rule not found");
    if (
      this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN &&
      matchingRule.organization === null
    )
      throw new ForbiddenException("Current user is not allowed to delete global rules");
    if (matchingRule.status === MatchingRuleStatus.ACTIVE)
      throw new BadRequestException("Active matching rule cannot be deleted");
    await (
      queryRunner ? queryRunner.manager.getRepository(MatchingRule) : this.matchingRuleRepository
    ).delete({ id: matchingRuleId });
    // No need to invalidate histories as active matching rules cannot be deleted
    return;
  }

  async cleanUpExpiredMatchingRules(queryRunner?: QueryRunner) {
    const tQueryRunner = queryRunner ?? this.dataSource.createQueryRunner();
    if (!queryRunner) {
      await tQueryRunner.connect();
      await tQueryRunner.startTransaction();
    }

    try {
      const matchingRuleRepository = tQueryRunner.manager.getRepository(MatchingRule);

      // Get all expired matching rules
      const expiredMatchingRules = await matchingRuleRepository.find({
        where: {
          status: Not(MatchingRuleStatus.DISABLED),
          expiryDate: LessThanOrEqual(moment.tz("America/Toronto").format("YYYY-MM-DD"))
        },
        select: {
          id: true,
          destinationTable: true,
          sourceTable: true
        }
      });
      this.logger.log(`Found ${expiredMatchingRules.length} expired matching rules`);

      // Set the status to disabled
      if (expiredMatchingRules.length > 0) {
        const updateStatusResult = await matchingRuleRepository.update(
          expiredMatchingRules.map((rule) => rule.id),
          { status: MatchingRuleStatus.DISABLED }
        );
        this.logger.log(`Updated status of ${updateStatusResult.affected} expired matching rules`);

        // Invalidate the related histories
        const sourceAndDestinationTablePairs = new Set<string>();
        for (const rule of expiredMatchingRules) {
          if (!sourceAndDestinationTablePairs.has(`${rule.sourceTable}-${rule.destinationTable}`)) {
            this.logger.log(
              `Invalidating histories for source table ${rule.sourceTable} and destination table ${rule.destinationTable}...`
            );
            sourceAndDestinationTablePairs.add(`${rule.sourceTable}-${rule.destinationTable}`);
            await this.invalidateMatchingHistories(
              rule.destinationTable,
              rule.sourceTable,
              undefined,
              tQueryRunner
            );
          } else
            this.logger.log(
              `Source table ${rule.sourceTable} and destination table ${rule.destinationTable} already invalidated`
            );
        }
      }

      if (!queryRunner) await tQueryRunner.commitTransaction();
    } catch (error) {
      if (!queryRunner && tQueryRunner.isTransactionActive) await tQueryRunner.rollbackTransaction();
      throw new InternalServerErrorException(
        `Failed to clean up expired matching rules: ${
          error instanceof Error ? error.message : String(error)
        }`,
        error instanceof Error ? error.stack : undefined
      );
    } finally {
      if (!queryRunner) await tQueryRunner.release();
    }
  }

  async cleanUpDanglingMatchingRules(queryRunner?: QueryRunner) {
    // Disable matching rules with no source record attached to it
    try {
      const ruleIdsToDisable = new Set<number>();
      const sourceAndDestinationTablePairs = new Map<
        string,
        {
          sourceTable: MatchingRuleSourceDatabaseTable;
          destinationTable: MatchingRuleDestinationDatabaseTable;
        }
      >();
      for (const sourceTable of Object.values(MatchingRuleSourceDatabaseTable)) {
        const { entity: sourceTableEntity } =
          MatchingRuleService.getSourceTableEntityAndRelations(sourceTable);
        const rulesWithNoSourceRecord = await (queryRunner ? queryRunner.manager : this.dataSource.manager)
          .createQueryBuilder(queryRunner)
          .select("mr.id", "ruleId")
          .addSelect("mr.sourceTable", "sourceTable")
          .addSelect("mr.destinationTable", "destinationTable")
          .from(MatchingRule, "mr")
          .leftJoin(sourceTableEntity, "s", `mr.sourceTable = :sourceTable AND mr.sourceId = s.id`)
          .where(`mr.sourceTable = :sourceTable AND s.id IS NULL`)
          .setParameters({ sourceTable })
          .getRawMany<{
            ruleId: number;
            sourceTable: MatchingRuleSourceDatabaseTable;
            destinationTable: MatchingRuleDestinationDatabaseTable;
          }>();
        this.logger.log(
          `Found ${rulesWithNoSourceRecord.length} ${sourceTable} matching rules with no source record`
        );
        rulesWithNoSourceRecord.forEach(({ ruleId, sourceTable, destinationTable }) => {
          ruleIdsToDisable.add(ruleId);
          sourceAndDestinationTablePairs.set(`${sourceTable}-${destinationTable}`, {
            sourceTable,
            destinationTable
          });
        });
      }
      if (ruleIdsToDisable.size > 0) {
        const updateResult = await (queryRunner ? queryRunner.manager : this.dataSource.manager).update(
          MatchingRule,
          Array.from(ruleIdsToDisable),
          { status: MatchingRuleStatus.DISABLED }
        );
        this.logger.log(`Disabled ${updateResult.affected} matching rules with no source record`);

        for (const { sourceTable, destinationTable } of sourceAndDestinationTablePairs.values()) {
          await this.invalidateMatchingHistories(destinationTable, sourceTable, undefined, queryRunner);
        }
      }
    } catch (error) {
      this.logger.error(
        `Failed to delete rules with no source record: ${
          error instanceof Error ? error.message : String(error)
        }`,
        error instanceof Error ? error.stack : undefined
      );
    }

    const historyIdsToInvalidate = new Set<number>();
    // Invalidate histories with the same (sourceTable, destinationTable, destinationId) set but with older queryDate
    try {
      const duplicatedHistories = await (queryRunner ? queryRunner.manager : this.dataSource.manager)
        .createQueryBuilder(queryRunner)
        .select("mh.id", "historyId")
        .from(MatchingHistory, "mh")
        .leftJoin(
          MatchingHistory,
          "mh2",
          `mh.sourceTable = mh2.sourceTable AND mh.destinationTable = mh2.destinationTable AND mh.destinationId = mh2.destinationId AND mh2.invalidDate IS NULL AND (mh.queryDate < mh2.queryDate OR (mh.queryDate = mh2.queryDate AND mh.id < mh2.id))`
        )
        .where(`mh.invalidDate IS NULL AND mh2.id IS NOT NULL`)
        .distinctOn(["mh.id"])
        .getRawMany<{ historyId: number }>();
      this.logger.log(
        `Found ${duplicatedHistories.length} duplicated matching histories with older query date`
      );
      duplicatedHistories.forEach(({ historyId }) => historyIdsToInvalidate.add(historyId));
    } catch (error) {
      this.logger.error(
        `Failed to find duplicated histories: ${error instanceof Error ? error.message : String(error)}`,
        error instanceof Error ? error.stack : undefined
      );
    }

    // Invalidate histories with missing source records (i.e. one of the sourceIds is not found)
    try {
      for (const sourceTable of Object.values(MatchingRuleSourceDatabaseTable)) {
        const { entity: sourceTableEntity } =
          MatchingRuleService.getSourceTableEntityAndRelations(sourceTable);
        const historiesWithMissingSourceRecords = await (
          queryRunner ? queryRunner.manager : this.dataSource.manager
        )
          .createQueryBuilder(queryRunner)
          .select("mh.id", "historyId")
          .from(
            (qb) =>
              qb
                .select("mh.id", "id")
                .addSelect("unnest(mh.sourceIds)", "sourceId")
                .from(MatchingHistory, "mh")
                .where(`mh.sourceTable = :sourceTable AND mh.invalidDate IS NULL`, { sourceTable }),
            "mh"
          )
          .leftJoin(sourceTableEntity, "s", `mh."sourceId" = s.id`)
          .where(`s.id IS NULL`)
          .distinctOn(["mh.id"])
          .getRawMany<{ historyId: number }>();
        this.logger.log(
          `Found ${historiesWithMissingSourceRecords.length} ${sourceTable} matching histories with missing source records`
        );
        historiesWithMissingSourceRecords.forEach(({ historyId }) => historyIdsToInvalidate.add(historyId));
      }
    } catch (error) {
      this.logger.error(
        `Failed to find histories with missing source records: ${
          error instanceof Error ? error.message : String(error)
        }`,
        error instanceof Error ? error.stack : undefined
      );
    }

    // Invalidate histories with missing destination record
    try {
      for (const destinationTable of Object.values(MatchingRuleDestinationDatabaseTable)) {
        const { entity: destinationTableEntity } =
          MatchingRuleService.getDestinationTableEntityAndRelations(destinationTable);
        const historiesWithMissingDestinationRecords = await (
          queryRunner ? queryRunner.manager : this.dataSource.manager
        )
          .createQueryBuilder(queryRunner)
          .select("mh.id", "historyId")
          .from(MatchingHistory, "mh")
          .leftJoin(
            destinationTableEntity,
            "d",
            "mh.destinationTable = :destinationTable AND mh.destinationId = d.id"
          )
          .where("mh.destinationTable = :destinationTable AND mh.invalidDate IS NULL AND d.id IS NULL")
          .setParameters({ destinationTable })
          .getRawMany<{ historyId: number }>();
        this.logger.log(
          `Found ${historiesWithMissingDestinationRecords.length} ${destinationTable} matching histories with missing destination records`
        );
        historiesWithMissingDestinationRecords.forEach(({ historyId }) =>
          historyIdsToInvalidate.add(historyId)
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to find histories with missing destination records: ${
          error instanceof Error ? error.message : String(error)
        }`,
        error instanceof Error ? error.stack : undefined
      );
    }

    // Invalidate histories
    if (historyIdsToInvalidate.size > 0) {
      try {
        const updateResult = await (queryRunner ? queryRunner.manager : this.dataSource.manager).update(
          MatchingHistory,
          Array.from(historyIdsToInvalidate),
          { invalidDate: new Date() }
        );
        this.logger.log(`Invalidated ${updateResult.affected} matching histories`);
      } catch (error) {
        this.logger.error(
          `Failed to invalidate histories: ${error instanceof Error ? error.message : String(error)}`,
          error instanceof Error ? error.stack : undefined
        );
      }
    }
  }
}
