import {
  BadRequestException,
  ForbiddenException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import { DataSource, In, Is<PERSON><PERSON>, Not, QueryRunner, Repository } from "typeorm";
import {
  MatchingResult,
  QueryMatchingResultsDto,
  QueryMatchingResultsResponseDto,
  QueryMatchingResultsWithDestinationIdResponseDto,
  QueryMultipleMatchingResultsDto,
  ReverseQueryMatchingResultsDto,
  ReverseQueryMatchingResultsResponseDto
} from "../../dto";
import {
  MatchingCondition,
  MatchingHistory,
  MatchingRule,
  Organization,
  Product,
  SimplifiedBaseEntity
} from "../../entities";
import {
  ATTRIBUTE_TYPE_DB_COLUMN_MAPPING,
  AuthenticatedRequest,
  DESTINATION_TABLE_ATTRIBUTES,
  DESTINATION_TABLE_CASE_INSENSITIVE_ATTRIBUTES,
  MATCHING_RULE_MODULE_OPTIONS,
  MatchingConditionAttributeType,
  MatchingConditionOperator,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleModuleOptions,
  MatchingRuleOperator,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus,
  UserPermission
} from "../../types";
import { MatchingConditionService } from "./matching-condition.service";
import { MatchingRuleService } from "./matching-rule.service";
import moment from "moment-timezone";

@Injectable({ scope: Scope.REQUEST })
export class RuleQueryService {
  constructor(
    @InjectRepository(MatchingRule)
    private readonly matchingRuleRepository: Repository<MatchingRule>,
    @InjectRepository(MatchingHistory)
    private readonly matchingHistoryRepository: Repository<MatchingHistory>,
    @Inject(forwardRef(() => MatchingConditionService))
    private readonly matchingConditionService: MatchingConditionService,
    @Inject(MATCHING_RULE_MODULE_OPTIONS)
    private readonly options: MatchingRuleModuleOptions,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}
  private readonly logger = new Logger(RuleQueryService.name);

  /**
   * Evaluate the expression from the given matching condition operator and value.
   * @param recordValue - The value from the database record (i.e. left hand side of the expression).
   * @param operator - The operator of the expression.
   * @param conditionValue - The value from the matching condition (i.e. right hand side of the expression)
   * @returns The boolean result of the expression evaluation. If expression is invalid, null will be returned.
   */
  private evaluateCondition(
    recordValue: string | number | boolean | Date | null,
    operator: MatchingConditionOperator,
    conditionValue: Array<string> | Array<number> | Array<boolean> | Array<Date> | null
  ) {
    const lhsValue = recordValue instanceof Date ? recordValue.toISOString() : recordValue;
    const rhsValue = (
      conditionValue === null
        ? [null]
        : conditionValue.map((v: string | number | boolean | Date) =>
            v instanceof Date ? v.toISOString() : v
          )
    ) as Array<string> | Array<number> | Array<boolean> | Array<null>;
    switch (operator) {
      case MatchingConditionOperator.EQUALS:
        return lhsValue === rhsValue[0];
      case MatchingConditionOperator.CONTAINS:
        return typeof lhsValue === "string" && rhsValue.every((v) => typeof v === "string")
          ? lhsValue.includes(rhsValue[0])
          : null;
      case MatchingConditionOperator.STARTS_WITH:
        return typeof lhsValue === "string" && rhsValue.every((v) => typeof v === "string")
          ? lhsValue.startsWith(rhsValue[0])
          : null;
      case MatchingConditionOperator.ENDS_WITH:
        return typeof lhsValue === "string" && rhsValue.every((v) => typeof v === "string")
          ? lhsValue.endsWith(rhsValue[0])
          : null;
      case MatchingConditionOperator.GREATER_THAN:
        return lhsValue !== null &&
          typeof lhsValue !== "boolean" &&
          rhsValue.every((v) => v !== null && typeof v !== "boolean")
          ? lhsValue > rhsValue[0]
          : null;
      case MatchingConditionOperator.LESS_THAN:
        return lhsValue !== null &&
          typeof lhsValue !== "boolean" &&
          rhsValue.every((v) => v !== null && typeof v !== "boolean")
          ? lhsValue < rhsValue[0]
          : null;
      case MatchingConditionOperator.GREATER_THAN_OR_EQUAL_TO:
        return lhsValue !== null &&
          typeof lhsValue !== "boolean" &&
          rhsValue.every((v) => v !== null && typeof v !== "boolean")
          ? lhsValue >= rhsValue[0]
          : null;
      case MatchingConditionOperator.LESS_THAN_OR_EQUAL_TO:
        return lhsValue !== null &&
          typeof lhsValue !== "boolean" &&
          rhsValue.every((v) => v !== null && typeof v !== "boolean")
          ? lhsValue <= rhsValue[0]
          : null;
      case MatchingConditionOperator.IN:
        return rhsValue.length > 0 ? rhsValue.some((v) => lhsValue === v) : null;
      case MatchingConditionOperator.BETWEEN_EXCLUSIVE:
      case MatchingConditionOperator.BETWEEN_INCLUSIVE:
        return lhsValue !== null &&
          typeof lhsValue !== "boolean" &&
          rhsValue.length === 2 &&
          rhsValue.every((v) => v !== null && typeof v !== "boolean")
          ? operator === MatchingConditionOperator.BETWEEN_EXCLUSIVE
            ? lhsValue > rhsValue[0] && lhsValue < rhsValue[1]
            : lhsValue >= rhsValue[0] && lhsValue <= rhsValue[1]
          : null;
      default:
        this.logger.warn(`Got invalid operator when evaluation condition: ${operator}`);
        return null;
    }
  }

  /**
   * Validate destination table and records.
   * @param destinationTable - The destination table
   * @param destinationIds - The destination IDs
   * @param queryRunner - Query Runner instance handling the transaction. If provided, the transaction will not be managed by the service
   * @param skipOrganizationCheck - If true, the organization check for destination records will be skipped
   * @returns The validated destination table and records
   */
  private async validateDestinationTableAndRecords(
    destinationTable: MatchingRuleDestinationDatabaseTable,
    destinationIds: Array<number>,
    queryRunner: QueryRunner,
    skipOrganizationCheck = false
  ) {
    const { entity: destinationTableEntity } =
      MatchingRuleService.getDestinationTableEntityAndRelations(destinationTable);
    const destinationTableAlias = MatchingRuleService.getDatabaseTableAlias(destinationTable);
    const destinationTableAttributes = DESTINATION_TABLE_ATTRIBUTES[destinationTable];
    const destinationTableCaseInsensitiveAttributes =
      DESTINATION_TABLE_CASE_INSENSITIVE_ATTRIBUTES[destinationTable] ?? [];

    const destinationRecords = await queryRunner.manager.findBy<
      SimplifiedBaseEntity & { organization: Organization }
    >(destinationTableEntity, {
      id: In(destinationIds),
      organization: {
        id:
          skipOrganizationCheck || this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
            ? Not(IsNull())
            : this.request?.user?.organization?.id || -1
      }
    });
    const notFoundDestinationIds = destinationIds.filter(
      (id) => !destinationRecords.some((record) => record.id === id)
    );
    if (notFoundDestinationIds.length > 0)
      throw new NotFoundException(
        `Destination record${
          notFoundDestinationIds.length > 1 ? "s" : ""
        } not found: ${notFoundDestinationIds.join(", ")}`
      );

    return {
      destinationTableEntity,
      destinationTableAlias,
      destinationTableAttributes,
      destinationTableCaseInsensitiveAttributes,
      destinationRecords
    };
  }

  private async getPreviousMatchingHistoryList(
    destinationTable: MatchingRuleDestinationDatabaseTable,
    destinationTableAlias: string,
    destinationTableEntity: Function,
    destinationIds: Array<number>,
    sourceTables: Array<MatchingRuleSourceDatabaseTable>,
    queryRunner: QueryRunner
  ) {
    const startTime = process.hrtime();
    const previousMatchingHistoryList = await queryRunner.manager
      .createQueryBuilder(MatchingHistory, "mh", queryRunner)
      .leftJoin(
        destinationTableEntity,
        destinationTableAlias,
        `${destinationTableAlias}.id = mh."destinationId"`
      )
      .where('mh."invalidDate" IS NULL')
      .andWhere('mh."destinationTable" = :destinationTable', { destinationTable })
      .andWhere('mh."destinationId" IN (:...destinationIds)', { destinationIds })
      .andWhere(
        sourceTables.length > 0 ? 'mh."sourceTable" IN (:...sourceTables)' : 'mh."sourceTable" IS NULL',
        { sourceTables }
      )
      .andWhere(`mh."queryDate" > ${destinationTableAlias}."lastEditDate"`)
      .distinctOn(['mh."sourceTable"', 'mh."destinationTable"', 'mh."destinationId"'])
      .orderBy('mh."sourceTable"', "DESC")
      .addOrderBy('mh."destinationTable"', "DESC")
      .addOrderBy('mh."destinationId"', "DESC")
      .addOrderBy('mh."queryDate"', "DESC")
      .getMany();
    this.logger.log(
      `Previous matching histories: ${JSON.stringify(previousMatchingHistoryList.map((h) => h.id))}`
    );

    const endTime = process.hrtime(startTime);
    this.logger.debug(`getPreviousMatchingHistoryList took ${endTime[0]}s ${endTime[1] / 1000000}ms`);
    return previousMatchingHistoryList;
  }

  /**
   * Perform matching condition evaluation for the given destination record and return the result.
   *
   * @param activeRulesOnly Whether to only consider active rules or not.
   * @param destinationTable Destination table.
   * @param destinationTableEntity Destination table entity.
   * @param destinationTableAttributes Destination table attributes.
   * @param destinationTableCaseInsensitiveAttributes Destination table case-insensitive attributes.
   * @param destinationTableAlias Destination table alias.
   * @param destinationIdAndSourceTablesList List of destination IDs and the target source tables. If source tables are not specified, it will match all source tables for the destination ID.
   * @param queryRunner Query runner.
   * @returns Matching condition evaluation results.
   */
  async getConditionMatchingResults(
    activeRulesOnly: boolean,
    destinationTable: MatchingRuleDestinationDatabaseTable,
    destinationTableEntity: Function,
    destinationTableAttributes: Record<string, MatchingConditionAttributeType>,
    destinationTableCaseInsensitiveAttributes: Array<string>,
    destinationTableAlias: string,
    destinationIdAndSourceTablesList: Array<{
      destinationId: number;
      sourceTables?: Array<MatchingRuleSourceDatabaseTable>;
    }>,
    queryRunner?: QueryRunner
  ) {
    const startTime = process.hrtime();

    // Get destination records and their attributes
    let destinationRecordQueryBuilder = this.dataSource
      .createQueryBuilder(destinationTableEntity, destinationTableAlias, queryRunner)
      .select(`${destinationTableAlias}.id`, "id");
    for (const attribute of Object.keys(destinationTableAttributes)) {
      if (attribute === "id") continue;
      destinationRecordQueryBuilder = destinationRecordQueryBuilder.addSelect(
        destinationTableCaseInsensitiveAttributes.includes(attribute)
          ? `upper(${destinationTableAlias}."${attribute}")`
          : `${destinationTableAlias}."${attribute}"`,
        attribute
      );
    }
    destinationRecordQueryBuilder = destinationRecordQueryBuilder.where(
      `${destinationTableAlias}.id in (:...destinationIds)`,
      {
        destinationIds: destinationIdAndSourceTablesList.map(({ destinationId }) => destinationId)
      }
    );
    const destinationRecords = await destinationRecordQueryBuilder.getRawMany<{
      [attribute: string]: any;
    }>();

    // Get list of unique source tables required
    const sourceTableList = destinationIdAndSourceTablesList.reduce((acc, { sourceTables }) => {
      if (Array.isArray(sourceTables)) {
        for (const table of sourceTables) if (!acc.includes(table)) acc.push(table);
      }
      return acc;
    }, [] as Array<MatchingRuleSourceDatabaseTable>);

    // Get list of matching conditions that are required
    const conditionList = await this.dataSource
      .createQueryBuilder(queryRunner)
      .select("mr.id", "ruleId")
      .addSelect("mr.priority", "priority")
      .addSelect(`mr."lastEditDate"`, "lastEditDate")
      .addSelect(`mr."sourceTable"`, "sourceTable")
      .addSelect(`mr."sourceId"`, "sourceId")
      .addSelect(`mr."operator"`, "ruleOperator")
      .addSelect(`mr."organizationId"`, "organizationId")
      .addSelect(`mc."attribute"`, "attribute")
      .addSelect(`mc."attributeType"`, "attributeType")
      .addSelect(`mc."operator"`, "conditionOperator")
      .addSelect(`mc."valueString"`, "valueString")
      .addSelect(`mc."valueInteger"`, "valueInteger")
      .addSelect(`mc."valueFloat"`, "valueFloat")
      .addSelect(`mc."valueBoolean"`, "valueBoolean")
      .addSelect(`mc."valueDateTime"`, "valueDateTime")
      .addSelect(`mc."isOperationInverted"`, "isOperationInverted")
      .from(MatchingCondition, "mc")
      .leftJoin(MatchingRule, "mr", `mr.id = mc."ruleId"`)
      .where(activeRulesOnly ? "mr.status = :activeStatus" : "mr.status is not null", {
        activeStatus: MatchingRuleStatus.ACTIVE
      })
      .andWhere(`(mr."expiryDate" is null or mr."expiryDate" > :currentDate)`, {
        currentDate: moment.tz("America/Toronto").format("YYYY-MM-DD")
      })
      .andWhere(`mr."destinationTable" = :destinationTable`, { destinationTable })
      .andWhere(
        sourceTableList.length > 0
          ? `mr."sourceTable" in (:...sourceTableList)`
          : `mr."sourceTable" is not null`,
        { sourceTableList }
      )
      .getRawMany<{
        ruleId: number;
        priority: number;
        lastEditDate: Date;
        sourceTable: MatchingRuleSourceDatabaseTable;
        sourceId: number;
        ruleOperator: MatchingRuleOperator;
        organizationId: number | null;
        attribute: string;
        attributeType: MatchingConditionAttributeType;
        conditionOperator: MatchingConditionOperator;
        value: string | null;
        valueString: Array<string> | null;
        valueInteger: Array<number> | null;
        valueFloat: Array<number> | null;
        valueBoolean: Array<boolean> | null;
        valueDateTime: Array<Date> | null;
        isOperationInverted: boolean;
      }>();

    // Combine conditions with destination records and evaluate if the conditions are satisfied
    const resultantConditionList: Array<{
      ruleId: number;
      priority: number;
      lastEditDate: Date;
      sourceTable: MatchingRuleSourceDatabaseTable;
      sourceId: number;
      ruleOperator: MatchingRuleOperator;
      organizationId: number | null;
      destinationId: number;
      attribute: string;
      attributeType: MatchingConditionAttributeType;
      conditionOperator: MatchingConditionOperator;
      value: string | null;
      isOperationInverted: boolean;
      targetValue: string | number | boolean | Date | null;
      rawResult: boolean | null;
    }> = [];
    for (const { destinationId, sourceTables } of destinationIdAndSourceTablesList) {
      const destinationRecord = destinationRecords.find((dr) => dr?.id === destinationId);
      if (!destinationRecord) continue;

      resultantConditionList.push(
        ...conditionList
          .filter(
            (c) =>
              (c.organizationId === null || c.organizationId === destinationRecord?.organizationId) &&
              (Array.isArray(sourceTables) ? sourceTables.includes(c.sourceTable) : true)
          )
          .map(
            ({ valueString, valueInteger, valueFloat, valueBoolean, valueDateTime, ...restOfCondition }) => {
              const targetValue = destinationRecord[restOfCondition.attribute];
              const conditionTypedValue = {
                valueString,
                valueInteger,
                valueFloat,
                valueDateTime,
                valueBoolean
              }[ATTRIBUTE_TYPE_DB_COLUMN_MAPPING[restOfCondition.attributeType]] as
                | Array<string>
                | Array<number>
                | Array<boolean>
                | Array<Date>
                | null;
              const rawResult = this.evaluateCondition(
                targetValue,
                restOfCondition.conditionOperator,
                conditionTypedValue
              );

              return {
                ...restOfCondition,
                destinationId,
                targetValue,
                rawResult
              };
            }
          )
      );
    }

    const endTime = process.hrtime(startTime);
    this.logger.debug(`getConditionMatchingResults took ${endTime[0]}s ${endTime[1] / 1000000}ms`);
    return resultantConditionList;
  }

  /**
   * Query matching results for multiple destination records.
   * @param queryMatchingResultsDto - The query matching results DTO
   * @param queryRunner - Query Runner instance handling the transaction. If provided, the transaction will not be managed by the service
   * @param skipOrganizationCheck - If true, the organization check for destination records will be skipped
   * @returns The query matching results
   */
  async queryMultipleMatchingResults(
    queryMatchingResultsDto: QueryMultipleMatchingResultsDto,
    queryRunner?: QueryRunner,
    skipOrganizationCheck = false
  ): Promise<Array<QueryMatchingResultsWithDestinationIdResponseDto>> {
    if (!this.options.query) throw new ForbiddenException("Query multiple matching results is disabled");
    const tQueryRunner = queryRunner || this.dataSource.createQueryRunner();
    if (!queryRunner) {
      await tQueryRunner.connect();
      await tQueryRunner.startTransaction();
    }

    let queryMatchingResultsList: Array<QueryMatchingResultsWithDestinationIdResponseDto> = [];

    try {
      const matchingHistoryRepository = tQueryRunner.manager.getRepository(MatchingHistory);
      const { destinationTable, destinationIds, sourceTables } = queryMatchingResultsDto;

      const {
        destinationTableEntity,
        destinationTableAlias,
        destinationTableAttributes,
        destinationTableCaseInsensitiveAttributes
      } = await this.validateDestinationTableAndRecords(
        destinationTable,
        destinationIds,
        tQueryRunner,
        skipOrganizationCheck
      );

      const previousMatchingHistoryList = await this.getPreviousMatchingHistoryList(
        destinationTable,
        destinationTableAlias,
        destinationTableEntity,
        destinationIds,
        sourceTables,
        tQueryRunner
      );

      const filteredSourceTablesByDestinationId: Record<number, Array<MatchingRuleSourceDatabaseTable>> = {};
      for (const destinationId of destinationIds)
        filteredSourceTablesByDestinationId[destinationId] = [...sourceTables];
      for (const mh of previousMatchingHistoryList) {
        const targetSourceTableIdx = filteredSourceTablesByDestinationId[mh.destinationId].findIndex(
          (st) => st === mh.sourceTable
        );
        if (targetSourceTableIdx >= 0)
          filteredSourceTablesByDestinationId[mh.destinationId].splice(targetSourceTableIdx, 1);
      }

      const savedHistoryList: Array<MatchingHistory> = [];
      if (
        Object.values(filteredSourceTablesByDestinationId).some(
          (destIdSourceTables) => destIdSourceTables.length > 0
        )
      ) {
        const filteredDestinationIds = Object.entries(filteredSourceTablesByDestinationId)
          .filter(([_, sourceTables]) => sourceTables.length > 0)
          .map(([destinationId]) => Number(destinationId));

        const conditionMatchingResultList = await this.getConditionMatchingResults(
          true,
          destinationTable,
          destinationTableEntity,
          destinationTableAttributes,
          destinationTableCaseInsensitiveAttributes,
          destinationTableAlias,
          filteredDestinationIds.map((destinationId) => ({
            destinationId,
            sourceTables: filteredSourceTablesByDestinationId[destinationId]
          })),
          tQueryRunner
        );

        const matchingResultListMapping = new Map<
          number,
          Array<{
            ruleId: number;
            priority: number;
            lastEditDate: Date;
            sourceTable: MatchingRuleSourceDatabaseTable;
            sourceId: number;
            ruleOperator: MatchingRuleOperator;
            satisfiedCount: number;
            totalCount: number;
          }>
        >();
        for (const {
          ruleId,
          priority,
          lastEditDate,
          sourceTable,
          sourceId,
          destinationId,
          ruleOperator,
          rawResult,
          isOperationInverted
        } of conditionMatchingResultList) {
          let destIdMatchingResultList = matchingResultListMapping.get(destinationId);
          if (!destIdMatchingResultList) destIdMatchingResultList = [];

          let matchingResultIdx = destIdMatchingResultList.findIndex((mr) => mr.ruleId === ruleId);
          if (matchingResultIdx === -1) {
            destIdMatchingResultList.push({
              ruleId,
              priority,
              lastEditDate,
              sourceTable,
              sourceId,
              ruleOperator,
              satisfiedCount: 0,
              totalCount: 0
            });
            matchingResultIdx = destIdMatchingResultList.length - 1;
          }
          destIdMatchingResultList[matchingResultIdx].totalCount++;
          destIdMatchingResultList[matchingResultIdx].satisfiedCount +=
            typeof rawResult === "boolean" && rawResult !== isOperationInverted ? 1 : 0;

          matchingResultListMapping.set(destinationId, destIdMatchingResultList);
        }

        const toBeSavedHistoryList: Array<MatchingHistory> = [];
        for (const destinationId of filteredDestinationIds) {
          const destIdMatchingResultList = matchingResultListMapping.get(destinationId) || [];
          const filteredSourceTables = filteredSourceTablesByDestinationId[destinationId];
          for (const sourceTable of filteredSourceTables) {
            const newHistory = new MatchingHistory();
            newHistory.sourceTable = sourceTable;
            newHistory.sourceIds = destIdMatchingResultList
              .filter(
                (mr) =>
                  mr.sourceTable === sourceTable &&
                  (mr.ruleOperator === MatchingRuleOperator.OR
                    ? mr.satisfiedCount > 0 // For OR operator, we only need to save records that have at least one satisfied condition.
                    : mr.satisfiedCount === mr.totalCount) // For AND operator, we need to save records that have all conditions satisfied.
              )
              .sort((a, b) =>
                a.priority !== b.priority
                  ? b.priority - a.priority // Larger number means higher priority, so we sort in descending order
                  : a.lastEditDate < b.lastEditDate
                    ? -1
                    : a.lastEditDate > b.lastEditDate
                      ? 1
                      : 0
              )
              .reduce((sourceIds, mr) => {
                if (!sourceIds.includes(mr.sourceId)) sourceIds.push(mr.sourceId);
                return sourceIds;
              }, [] as Array<number>);
            newHistory.destinationTable = destinationTable;
            newHistory.destinationId = destinationId;
            toBeSavedHistoryList.push(newHistory);
          }
        }
        savedHistoryList.push(...(await matchingHistoryRepository.save(toBeSavedHistoryList)));
        this.logger.log(`Saved histories: ${JSON.stringify(savedHistoryList.map((h) => h.id))}`);
      }

      for (const destinationId of destinationIds) {
        const destinationIdMatchingResults: Array<MatchingResult> = [];
        for (const sourceTable of sourceTables) {
          const sourceIds =
            (filteredSourceTablesByDestinationId[destinationId].includes(sourceTable)
              ? savedHistoryList.find(
                  (h) => h.destinationId === destinationId && h.sourceTable === sourceTable
                )?.sourceIds
              : previousMatchingHistoryList.find(
                  (mh) => mh.destinationId === destinationId && mh.sourceTable === sourceTable
                )?.sourceIds) || [];
          const { entity: sourceEntity, relations: sourceRelations } =
            MatchingRuleService.getSourceTableEntityAndRelations(sourceTable);
          const unsortedSourceRecords = await tQueryRunner.manager.find(sourceEntity, {
            where: { id: In(sourceIds) },
            relations: sourceRelations
          });
          destinationIdMatchingResults.push({
            sourceTable,
            sourceRecords: sourceIds.map((sid) => unsortedSourceRecords.find((s) => s.id === sid))
          });
        }
        queryMatchingResultsList.push({
          destinationId,
          matchingResults: destinationIdMatchingResults
        });
      }

      if (!queryRunner) await tQueryRunner.commitTransaction();
    } catch (error) {
      if (!queryRunner) await tQueryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner) await tQueryRunner.release();
    }

    return queryMatchingResultsList;
  }

  /**
   * Query matching result for a single destination record
   * @param queryMatchingResultsDto - The query matching results DTO
   * @param queryRunner - Query Runner instance handling the transaction. If provided, the transaction will not be managed by the service
   * @param skipOrganizationCheck - If true, the organization check for destination records will be skipped
   * @returns The query matching results
   */
  async queryMatchingResults(
    queryMatchingResultsDto: QueryMatchingResultsDto,
    queryRunner?: QueryRunner,
    skipOrganizationCheck = false
  ): Promise<QueryMatchingResultsResponseDto> {
    if (!this.options.query) throw new ForbiddenException("Query matching results is disabled");
    const tQueryRunner = queryRunner || this.dataSource.createQueryRunner();
    if (!queryRunner) {
      await tQueryRunner.connect();
      await tQueryRunner.startTransaction();
    }

    let sourceTableMatchingResultList: Array<MatchingResult> = [];

    try {
      const matchingHistoryRepository = tQueryRunner.manager.getRepository(MatchingHistory);

      const { destinationTable, destinationId, sourceTables } = queryMatchingResultsDto;

      const {
        destinationTableEntity,
        destinationTableAlias,
        destinationTableAttributes,
        destinationTableCaseInsensitiveAttributes,
        destinationRecords: [destinationRecord]
      } = await this.validateDestinationTableAndRecords(
        destinationTable,
        [destinationId],
        tQueryRunner,
        skipOrganizationCheck
      );

      const previousMatchingHistoryList = await this.getPreviousMatchingHistoryList(
        destinationTable,
        destinationTableAlias,
        destinationTableEntity,
        [destinationId],
        sourceTables,
        tQueryRunner
      );

      const filteredSourceTables = sourceTables.filter(
        (st) => !previousMatchingHistoryList.some((mh) => mh.sourceTable === st)
      );
      this.logger.log(`Filtered source tables: ${JSON.stringify(filteredSourceTables)}`);

      const matchingResultList: Array<{
        ruleId: number;
        priority: number;
        lastEditDate: Date;
        sourceTable: MatchingRuleSourceDatabaseTable;
        sourceId: number;
        ruleOperator: MatchingRuleOperator;
        satisfiedCount: number;
        totalCount: number;
      }> = [];
      const savedHistoryList: Array<MatchingHistory> = [];
      if (filteredSourceTables.length > 0) {
        const conditionMatchingResultList = await this.getConditionMatchingResults(
          true,
          destinationTable,
          destinationTableEntity,
          destinationTableAttributes,
          destinationTableCaseInsensitiveAttributes,
          destinationTableAlias,
          [{ destinationId, sourceTables: filteredSourceTables }],
          tQueryRunner
        );

        for (const {
          ruleId,
          priority,
          lastEditDate,
          sourceTable,
          sourceId,
          ruleOperator,
          rawResult,
          isOperationInverted
        } of conditionMatchingResultList) {
          let matchingResultIdx = matchingResultList.findIndex(
            (matchingResult) => matchingResult.ruleId === ruleId
          );
          if (matchingResultIdx === -1) {
            matchingResultList.push({
              ruleId,
              priority,
              lastEditDate,
              sourceTable,
              sourceId,
              ruleOperator,
              satisfiedCount: 0,
              totalCount: 0
            });
            matchingResultIdx = matchingResultList.length - 1;
          }
          matchingResultList[matchingResultIdx].totalCount++;
          matchingResultList[matchingResultIdx].satisfiedCount +=
            typeof rawResult === "boolean" && rawResult !== isOperationInverted ? 1 : 0;
        }

        const toBeSavedHistoryList: Array<MatchingHistory> = [];
        for (const sourceTable of filteredSourceTables) {
          const newHistory = new MatchingHistory();
          newHistory.sourceTable = sourceTable;
          newHistory.sourceIds = matchingResultList
            .filter(
              (mr) =>
                mr.sourceTable === sourceTable &&
                (mr.ruleOperator === MatchingRuleOperator.OR
                  ? mr.satisfiedCount > 0 // For OR operator, we only need to save records that have at least one satisfied condition.
                  : mr.satisfiedCount === mr.totalCount) // For AND operator, we need to save records that have all conditions satisfied.
            )
            .sort((a, b) =>
              a.priority !== b.priority
                ? b.priority - a.priority // Larger number means higher priority, so we sort in descending order
                : a.lastEditDate < b.lastEditDate
                  ? -1
                  : a.lastEditDate > b.lastEditDate
                    ? 1
                    : 0
            )
            .reduce((sourceIds, mr) => {
              if (!sourceIds.includes(mr.sourceId)) sourceIds.push(mr.sourceId);
              return sourceIds;
            }, [] as Array<number>);
          newHistory.destinationTable = destinationTable;
          newHistory.destinationId = destinationId;
          toBeSavedHistoryList.push(newHistory);
        }
        savedHistoryList.push(...(await matchingHistoryRepository.save(toBeSavedHistoryList)));
        this.logger.log(`Saved histories: ${JSON.stringify(savedHistoryList.map((h) => h.id))}`);
      }

      for (const sourceTable of sourceTables) {
        const sourceIds =
          (filteredSourceTables.includes(sourceTable)
            ? savedHistoryList.find((h) => h.sourceTable === sourceTable)?.sourceIds
            : previousMatchingHistoryList.find((mh) => mh.sourceTable === sourceTable)?.sourceIds) || [];
        const { entity: sourceEntity, relations: sourceRelations } =
          MatchingRuleService.getSourceTableEntityAndRelations(sourceTable);
        const unsortedSourceRecords = await tQueryRunner.manager.find(sourceEntity, {
          where: { id: In(sourceIds) },
          relations: sourceRelations
        });
        sourceTableMatchingResultList.push({
          sourceTable,
          sourceRecords: sourceIds.map((sid) => unsortedSourceRecords.find((s) => s.id === sid))
        });
      }

      if (!queryRunner) await tQueryRunner.commitTransaction();
    } catch (error) {
      if (!queryRunner && tQueryRunner.isTransactionActive) await tQueryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner) await tQueryRunner.release();
    }

    return { matchingResults: sourceTableMatchingResultList };
  }

  async reverseQueryMatchingResults(
    reverseQueryMatchingResultsDto: ReverseQueryMatchingResultsDto
  ): Promise<ReverseQueryMatchingResultsResponseDto> {
    if (!this.options.query) throw new ForbiddenException("Reverse-query matching results is disabled");
    if (!reverseQueryMatchingResultsDto.operator)
      reverseQueryMatchingResultsDto.operator = MatchingRuleOperator.AND;
    const {
      destinationTable,
      conditions,
      operator: ruleOperator,
      organizationId
    } = reverseQueryMatchingResultsDto;

    const { entity: destinationTableEntity, relations: destinationTableRelations } =
      MatchingRuleService.getDestinationTableEntityAndRelations(destinationTable);
    const destinationTableAlias = MatchingRuleService.getDatabaseTableAlias(destinationTable);
    const destinationTableAttributes = DESTINATION_TABLE_ATTRIBUTES[destinationTable];
    const destinationTableCaseInsensitiveAttributes =
      DESTINATION_TABLE_CASE_INSENSITIVE_ATTRIBUTES[destinationTable] ?? [];

    const whereStrings: Array<string> = [];
    const whereValues: Record<string, any> = {};
    for (let i = 0; i < conditions.length; i++) {
      let { attribute, operator, value, isOperationInverted } = conditions[i];
      const attributeType = destinationTableAttributes[attribute];
      if (!attributeType)
        throw new BadRequestException(
          `Attribute must be one of the following: ${Object.keys(
            DESTINATION_TABLE_ATTRIBUTES[destinationTable]
          ).join(", ")}`
        );
      const { isValid, errorMessage, parsedValueList } =
        this.matchingConditionService.validateConditionValues(
          value === undefined ? null : value,
          attributeType,
          operator
        );
      if (!isValid) throw new BadRequestException(errorMessage);
      if (
        destinationTableCaseInsensitiveAttributes.includes(attribute) &&
        Array.isArray(parsedValueList) &&
        parsedValueList.every((v) => typeof v === "string")
      ) {
        value = value.toUpperCase();
        parsedValueList.forEach((v, i) => (parsedValueList[i] = v.toUpperCase()));
      }

      if (
        [MatchingConditionOperator.BETWEEN_EXCLUSIVE, MatchingConditionOperator.BETWEEN_INCLUSIVE].includes(
          operator
        )
      ) {
        whereValues[`value${i}lower`] = parsedValueList[0];
        whereValues[`value${i}upper`] = parsedValueList[1];
      } else {
        whereValues[`value${i}`] =
          operator === MatchingConditionOperator.IN || parsedValueList === null
            ? parsedValueList
            : parsedValueList[0];
      }

      let currentWhereString: string = null;
      const attributeSql = destinationTableCaseInsensitiveAttributes.includes(attribute)
        ? `upper(${destinationTableAlias}."${attribute}")`
        : `${destinationTableAlias}."${attribute}"`;
      switch (operator) {
        case MatchingConditionOperator.EQUALS:
          currentWhereString =
            `${attributeSql} ` + ([null, undefined].includes(parsedValueList) ? "IS NULL" : `= :value${i}`);
          break;
        case MatchingConditionOperator.CONTAINS:
          currentWhereString = `${attributeSql} LIKE '%' || :value${i} || '%'`;
          break;
        case MatchingConditionOperator.STARTS_WITH:
          currentWhereString = `${attributeSql} LIKE :value${i} || '%'`;
          break;
        case MatchingConditionOperator.ENDS_WITH:
          currentWhereString = `${attributeSql} LIKE '%' || :value${i}`;
          break;
        case MatchingConditionOperator.GREATER_THAN:
          currentWhereString = `${attributeSql} > :value${i}`;
          break;
        case MatchingConditionOperator.GREATER_THAN_OR_EQUAL_TO:
          currentWhereString = `${attributeSql} >= :value${i}`;
          break;
        case MatchingConditionOperator.LESS_THAN:
          currentWhereString = `${attributeSql} < :value${i}`;
          break;
        case MatchingConditionOperator.LESS_THAN_OR_EQUAL_TO:
          currentWhereString = `${attributeSql} <= :value${i}`;
          break;
        case MatchingConditionOperator.IN:
          currentWhereString = `${attributeSql} IN (:...value${i})`;
          break;
        case MatchingConditionOperator.BETWEEN_INCLUSIVE:
          currentWhereString = `${attributeSql} BETWEEN :value${i}lower AND :value${i}upper`;
          break;
        case MatchingConditionOperator.BETWEEN_EXCLUSIVE:
          currentWhereString = `(${attributeSql} > :value${i}lower AND ${attributeSql} < :value${i}upper)`;
          break;
        default:
          throw new BadRequestException(`Operator ${operator} is not supported`);
      }
      whereStrings.push(isOperationInverted ? `NOT (${currentWhereString})` : currentWhereString);
    }

    if (
      this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN ||
      typeof organizationId === "number"
    ) {
      whereStrings.push(`${destinationTableAlias}.organizationId = :organizationId`);
      whereValues.organizationId =
        this.request?.user?.permission === UserPermission.BACKOFFICE_ADMIN
          ? organizationId
          : this.request?.user?.organization?.id || -1;
    }

    let queryBuilder = this.dataSource.createQueryBuilder<Product>(
      destinationTableEntity,
      destinationTableAlias
    );
    for (const property of Object.keys(destinationTableRelations))
      queryBuilder = queryBuilder.leftJoinAndSelect(`${destinationTableAlias}.${property}`, property);
    const destinationRecords = (await queryBuilder
      .where(whereStrings.join(ruleOperator === MatchingRuleOperator.OR ? " OR " : " AND "), whereValues)
      .getMany()) as Array<Product>;
    return { destinationRecords };
  }
}
