import { DynamicModule, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CanadaTreatmentCode, Country } from "../entities";
import { CANADA_TREATMENT_CODE_MODULE_OPTIONS, CrudOptions } from "../types";
import { CanadaTreatmentCodeController } from "./canada-treatment-code.controller";
import { CanadaTreatmentCodeService } from "./canada-treatment-code.service";

@Module({})
export class CanadaTreatmentCodeModule {
  static register(options: CrudOptions): DynamicModule {
    return {
      global: true,
      module: CanadaTreatmentCodeModule,
      imports: [TypeOrmModule.forFeature([CanadaTreatmentCode, Country])],
      controllers: [CanadaTreatmentCodeController],
      providers: [
        {
          provide: CANADA_TREATMENT_CODE_MODULE_OPTIONS,
          useValue: options
        },
        CanadaTreatmentCodeService
      ],
      exports: [CanadaTreatmentCodeService]
    };
  }
}
