import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseGuards,
  NotFoundException,
  Post,
  Body,
  Put,
  Delete,
  HttpCode
} from "@nestjs/common";
import { AccessTokenGuard } from "../guards";
import {
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses
} from "../decorators";
import { ApiForbiddenResponse, ApiOperation, ApiParam } from "@nestjs/swagger";
import { ApiTags } from "@nestjs/swagger";
import {
  BatchUpdateCanadaTreatmentCodesDto,
  BatchUpdateCanadaTreatmentCodesResponseDto,
  CreateCanadaTreatmentCodeDto,
  EditCanadaTreatmentCodeDto,
  ForbiddenResponseDto,
  GetCanadaTreatmentCodesDto,
  GetCanadaTreatmentCodesResponseDto
} from "../dto";
import { CanadaTreatmentCodeService } from "./canada-treatment-code.service";
import { CanadaTreatmentCode } from "../entities";

@ApiTags("Canada Treatment Code API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("canada-treatment-codes")
export class CanadaTreatmentCodeController {
  constructor(private readonly canadaTreatmentCodeService: CanadaTreatmentCodeService) {}

  @Get()
  @ApiOperation({ summary: "Get Canada Treatment Codes" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: GetCanadaTreatmentCodesResponseDto })
  async getCanadaTreatmentCodes(@Query() getCanadaTreatmentCodesDto: GetCanadaTreatmentCodesDto) {
    return await this.canadaTreatmentCodeService.getCanadaTreatmentCodes(getCanadaTreatmentCodesDto);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get Canada Treatment Code by ID" })
  @ApiParam({ name: "id", type: "integer", description: "Canada Treatment Code ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: CanadaTreatmentCode })
  async getCanadaTreatmentCodeById(@Param("id", ParseIntPipe) id: number) {
    const code = await this.canadaTreatmentCodeService.getCanadaTreatmentCodeById(id);
    if (!code) throw new NotFoundException("Canada Treatment Code not found");
    return code;
  }

  @Post()
  @ApiOperation({ summary: "Create Canada Treatment Code" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiCreateResponses({ type: CanadaTreatmentCode })
  async createCanadaTreatmentCode(@Body() createCanadaTreatmentCodeDto: CreateCanadaTreatmentCodeDto) {
    return await this.canadaTreatmentCodeService.createCanadaTreatmentCode(createCanadaTreatmentCodeDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update Canada Treatment Code" })
  @ApiParam({ name: "id", type: "integer", description: "Canada Treatment Code ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: CanadaTreatmentCode })
  async editCanadaTreatmentCode(
    @Param("id", ParseIntPipe) id: number,
    @Body() editCanadaTreatmentCodeDto: EditCanadaTreatmentCodeDto
  ) {
    return await this.canadaTreatmentCodeService.editCanadaTreatmentCode(id, editCanadaTreatmentCodeDto);
  }

  @HttpCode(204)
  @Delete(":id")
  @ApiOperation({ summary: "Delete Canada Treatment Code" })
  @ApiParam({ name: "id", type: "integer", description: "Canada Treatment Code ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiDeleteResponses()
  async deleteCanadaTreatmentCode(@Param("id", ParseIntPipe) id: number) {
    await this.canadaTreatmentCodeService.deleteCanadaTreatmentCode(id);
    return;
  }

  @HttpCode(200)
  @Post("batch")
  @ApiOperation({ summary: "Batch update Canada Treatment Codes" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: BatchUpdateCanadaTreatmentCodesResponseDto })
  async batchUpdateCanadaTreatmentCodes(
    @Body() batchUpdateCanadaTreatmentCodesDto: BatchUpdateCanadaTreatmentCodesDto
  ) {
    return await this.canadaTreatmentCodeService.batchUpdateCanadaTreatmentCodes(
      batchUpdateCanadaTreatmentCodesDto
    );
  }
}
