import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import { DataSource, In, Not, QueryRunner, Repository } from "typeorm";
import {
  BatchUpdateCanadaTreatmentCodesDto,
  BatchUpdateCanadaTreatmentCodesResponseDto,
  CreateCanadaTreatmentCodeDto,
  EditCanadaTreatmentCodeDto,
  GetCanadaTreatmentCodesDto,
  GetCanadaTreatmentCodesResponseDto
} from "../dto";
import { CanadaTreatmentCode, Country } from "../entities";
import { convertFromCamelCase, getFindOptions } from "../helper-functions";
import { MatchingRuleService } from "../matching-rule";
import {
  AuthenticatedRequest,
  CANADA_TREATMENT_CODE_MODULE_OPTIONS,
  CANADA_TREATMENT_CODE_REQUIRED_KEYS,
  CanadaTreatmentCodeColumn,
  CrudOptions,
  FIND_CANADA_TREATMENT_CODE_RELATIONS,
  MatchingRuleSourceDatabaseTable,
  UserPermission
} from "../types";

@Injectable({ scope: Scope.REQUEST })
export class CanadaTreatmentCodeService {
  constructor(
    @InjectRepository(CanadaTreatmentCode)
    private readonly canadaTreatmentCodeRepository: Repository<CanadaTreatmentCode>,
    @InjectRepository(Country)
    private readonly countryRepository: Repository<Country>,
    @Inject(MatchingRuleService)
    private readonly matchingRuleService: MatchingRuleService,
    @Inject(CANADA_TREATMENT_CODE_MODULE_OPTIONS)
    private readonly options: CrudOptions,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}

  async getCanadaTreatmentCodes(
    getCanadaTreatmentCodesDto: GetCanadaTreatmentCodesDto
  ): Promise<GetCanadaTreatmentCodesResponseDto> {
    if (!this.options.readMany) throw new ForbiddenException("Get Canada Treatment Codes is disabled");
    const { where, order, skip, take } = getFindOptions(
      getCanadaTreatmentCodesDto,
      [],
      [],
      CanadaTreatmentCodeColumn.id
    );
    const [data, total] = await this.canadaTreatmentCodeRepository.findAndCount({
      where,
      order,
      relations: FIND_CANADA_TREATMENT_CODE_RELATIONS,
      skip,
      take
    });
    return {
      data,
      total,
      skip,
      limit: take
    };
  }

  async getCanadaTreatmentCodeById(codeId: number, queryRunner?: QueryRunner) {
    if (!this.options.readOne) throw new ForbiddenException("Get Canada Treatment Code is disabled");
    const canadaTreatmentCodeRepository = queryRunner
      ? queryRunner.manager.getRepository(CanadaTreatmentCode)
      : this.canadaTreatmentCodeRepository;
    const canadaTreatmentCode = await canadaTreatmentCodeRepository.findOne({
      where: { id: codeId },
      relations: FIND_CANADA_TREATMENT_CODE_RELATIONS
    });
    return canadaTreatmentCode;
  }

  async createCanadaTreatmentCode(createCanadaTreatmentCodeDto: CreateCanadaTreatmentCodeDto) {
    if (!this.options.create) throw new ForbiddenException("Create Canada Treatment Code is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to create Canada Treatment Codes");
    let newCanadaTreatmentCode = new CanadaTreatmentCode();
    newCanadaTreatmentCode.createdBy = this.request?.user || null;
    newCanadaTreatmentCode.lastEditedBy = this.request?.user || null;
    const { countryIds, ...createProps } = createCanadaTreatmentCodeDto;
    if (Array.isArray(countryIds)) {
      newCanadaTreatmentCode.countries =
        countryIds.length > 0 ? await this.countryRepository.findBy({ id: In(countryIds) }) : [];
      if (newCanadaTreatmentCode.countries.length !== countryIds.length) {
        const missingCountryIds = countryIds.filter(
          (id) => !newCanadaTreatmentCode.countries.some((c) => c.id === id)
        );
        throw new NotFoundException(
          `Countr${missingCountryIds.length > 1 ? "ies" : "y"} ${missingCountryIds.join(", ")} not found`
        );
      }
    }
    for (const [key, value] of Object.entries(createProps)) {
      if (CANADA_TREATMENT_CODE_REQUIRED_KEYS.includes(key)) {
        if (value === undefined) continue;
        if (value === null) throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
        newCanadaTreatmentCode[key] = value;
      } else newCanadaTreatmentCode[key] = [null, undefined].includes(value) ? null : value;
    }
    if (
      await this.canadaTreatmentCodeRepository.existsBy([
        { code: newCanadaTreatmentCode.code },
        { name: newCanadaTreatmentCode.name },
        { abbreviation: newCanadaTreatmentCode.abbreviation }
      ])
    )
      throw new BadRequestException(
        "Canada Treatment Code with the same code, name or abbreviation already exists"
      );

    newCanadaTreatmentCode = await this.canadaTreatmentCodeRepository.save(newCanadaTreatmentCode);

    return await this.getCanadaTreatmentCodeById(newCanadaTreatmentCode.id);
  }

  async editCanadaTreatmentCode(codeId: number, editCanadaTreatmentCodeDto: EditCanadaTreatmentCodeDto) {
    if (!this.options.update) throw new ForbiddenException("Edit Canada Treatment Code is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to edit Canada Treatment Codes");
    const canadaTreatmentCode = await this.getCanadaTreatmentCodeById(codeId);
    if (!canadaTreatmentCode) throw new NotFoundException("Canada Treatment Code not found");
    canadaTreatmentCode.lastEditedBy = this.request?.user || null;
    const { countryIds, ...editProps } = editCanadaTreatmentCodeDto;
    if (Array.isArray(countryIds)) {
      canadaTreatmentCode.countries =
        countryIds.length > 0 ? await this.countryRepository.findBy({ id: In(countryIds) }) : [];
      if (canadaTreatmentCode.countries.length !== countryIds.length) {
        const missingCountryIds = countryIds.filter(
          (id) => !canadaTreatmentCode.countries.some((c) => c.id === id)
        );
        throw new NotFoundException(
          `Countr${missingCountryIds.length > 1 ? "ies" : "y"} ${missingCountryIds.join(", ")} not found`
        );
      }
    }
    for (const [key, value] of Object.entries(editProps)) {
      if (CANADA_TREATMENT_CODE_REQUIRED_KEYS.includes(key)) {
        if (value === undefined) continue;
        if (value === null) throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
        canadaTreatmentCode[key] = value;
      } else canadaTreatmentCode[key] = [null, undefined].includes(value) ? null : value;
    }
    if (
      await this.canadaTreatmentCodeRepository.existsBy([
        { code: canadaTreatmentCode.code, id: Not(codeId) },
        { name: canadaTreatmentCode.name, id: Not(codeId) },
        { abbreviation: canadaTreatmentCode.abbreviation, id: Not(codeId) }
      ])
    )
      throw new BadRequestException(
        "Canada Treatment Code with the same code, name or abbreviation already exists"
      );
    await this.canadaTreatmentCodeRepository.save(canadaTreatmentCode);
    return await this.getCanadaTreatmentCodeById(codeId);
  }

  async deleteCanadaTreatmentCode(codeId: number) {
    if (!this.options.delete) throw new ForbiddenException("Delete Canada Treatment Code is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to delete Canada Treatment Codes");
    if (!(await this.canadaTreatmentCodeRepository.existsBy({ id: codeId })))
      throw new NotFoundException("Canada Treatment Code not found");
    if (
      await this.matchingRuleService.isMatchingRuleForSourceRecordExists(
        MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE,
        codeId
      )
    )
      throw new BadRequestException("Canada Treatment Code is being used in a matching rule");
    await this.canadaTreatmentCodeRepository.delete({ id: codeId });
    return;
  }

  async batchUpdateCanadaTreatmentCodes(
    batchUpdateCanadaTreatmentCodesDto: BatchUpdateCanadaTreatmentCodesDto
  ): Promise<BatchUpdateCanadaTreatmentCodesResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const savedCanadaTreatmentCodeIds: Array<number> = [];
    try {
      const { create: createList, edit: editList, delete: deleteList } = batchUpdateCanadaTreatmentCodesDto;

      if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
        throw new ForbiddenException("You are not authorized to batch update Canada Treatment Codes");
      if (Array.isArray(createList) && createList.length > 0 && !this.options.create)
        throw new ForbiddenException("Create Canada Treatment Code is disabled");
      if (Array.isArray(editList) && editList.length > 0 && !this.options.update)
        throw new ForbiddenException("Edit Canada Treatment Code is disabled");
      if (Array.isArray(deleteList) && deleteList.length > 0 && !this.options.delete)
        throw new ForbiddenException("Delete Canada Treatment Code is disabled");

      const canadaTreatmentCodeRepository = queryRunner.manager.getRepository(CanadaTreatmentCode);
      const toBeSavedCanadaTreatmentCodes: Array<CanadaTreatmentCode> = [];

      for (let i = 0; i < (createList || []).length; i++) {
        const errorMsgPrefix = `Error on create list index ${i}: `;
        const { countryIds, ...createCanadaTreatmentCodeDto } = createList[i];
        let newCanadaTreatmentCode = new CanadaTreatmentCode();
        newCanadaTreatmentCode.createdBy = this.request?.user || null;
        newCanadaTreatmentCode.lastEditedBy = this.request?.user || null;
        if (Array.isArray(countryIds)) {
          newCanadaTreatmentCode.countries =
            countryIds.length > 0 ? await this.countryRepository.findBy({ id: In(countryIds) }) : [];
          if (newCanadaTreatmentCode.countries.length !== countryIds.length) {
            const missingCountryIds = countryIds.filter(
              (id) => !newCanadaTreatmentCode.countries.some((c) => c.id === id)
            );
            throw new NotFoundException(
              `${errorMsgPrefix}Countr${missingCountryIds.length > 1 ? "ies" : "y"} ${missingCountryIds.join(", ")} not found`
            );
          }
        }
        for (const [key, value] of Object.entries(createCanadaTreatmentCodeDto)) {
          if (CANADA_TREATMENT_CODE_REQUIRED_KEYS.includes(key)) {
            if (value === undefined) continue;
            if (value === null)
              throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
            newCanadaTreatmentCode[key] = value;
          } else newCanadaTreatmentCode[key] = [null, undefined].includes(value) ? null : value;
        }
        if (
          toBeSavedCanadaTreatmentCodes.some(
            (c) =>
              c.code === newCanadaTreatmentCode.code ||
              c.name === newCanadaTreatmentCode.name ||
              c.abbreviation === newCanadaTreatmentCode.abbreviation
          ) ||
          (await canadaTreatmentCodeRepository.existsBy([
            { code: newCanadaTreatmentCode.code },
            { name: newCanadaTreatmentCode.name },
            { abbreviation: newCanadaTreatmentCode.abbreviation }
          ]))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada Treatment Code with the same code, name or abbreviation already exists`
          );

        toBeSavedCanadaTreatmentCodes.push(newCanadaTreatmentCode);
      }

      for (let i = 0; i < (editList || []).length; i++) {
        const errorMsgPrefix = `Error on edit list index ${i}: `;
        const { id: codeId, ...editCanadaTreatmentCodeDto } = editList[i];
        const canadaTreatmentCode = await this.getCanadaTreatmentCodeById(codeId, queryRunner);
        if (!canadaTreatmentCode)
          throw new NotFoundException(`${errorMsgPrefix}Canada Treatment Code not found`);
        canadaTreatmentCode.lastEditedBy = this.request?.user || null;
        const { countryIds, ...editProps } = editCanadaTreatmentCodeDto;
        if (Array.isArray(countryIds)) {
          canadaTreatmentCode.countries =
            countryIds.length > 0 ? await this.countryRepository.findBy({ id: In(countryIds) }) : [];
          if (canadaTreatmentCode.countries.length !== countryIds.length) {
            const missingCountryIds = countryIds.filter(
              (id) => !canadaTreatmentCode.countries.some((c) => c.id === id)
            );
            throw new NotFoundException(
              `${errorMsgPrefix}Countr${missingCountryIds.length > 1 ? "ies" : "y"} ${missingCountryIds.join(", ")} not found`
            );
          }
        }
        for (const [key, value] of Object.entries(editProps)) {
          if (CANADA_TREATMENT_CODE_REQUIRED_KEYS.includes(key)) {
            if (value === undefined) continue;
            if (value === null)
              throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
            canadaTreatmentCode[key] = value;
          } else canadaTreatmentCode[key] = [null, undefined].includes(value) ? null : value;
        }
        if (
          toBeSavedCanadaTreatmentCodes.some(
            (c) =>
              c.code === canadaTreatmentCode.code ||
              c.name === canadaTreatmentCode.name ||
              c.abbreviation === canadaTreatmentCode.abbreviation
          ) ||
          (await canadaTreatmentCodeRepository.existsBy([
            {
              code: canadaTreatmentCode.code,
              id: Not(
                In(
                  toBeSavedCanadaTreatmentCodes
                    .filter((c) => typeof c.id === "number")
                    .map((c) => c.id)
                    .concat([codeId])
                )
              )
            },
            {
              name: canadaTreatmentCode.name,
              id: Not(
                In(
                  toBeSavedCanadaTreatmentCodes
                    .filter((c) => typeof c.id === "number")
                    .map((c) => c.id)
                    .concat([codeId])
                )
              )
            },
            {
              abbreviation: canadaTreatmentCode.abbreviation,
              id: Not(
                In(
                  toBeSavedCanadaTreatmentCodes
                    .filter((c) => typeof c.id === "number")
                    .map((c) => c.id)
                    .concat([codeId])
                )
              )
            }
          ]))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada Treatment Code with the same code, name or abbreviation already exists`
          );
        toBeSavedCanadaTreatmentCodes.push(canadaTreatmentCode);
      }

      for (let i = 0; i < (deleteList || []).length; i++) {
        const codeId = deleteList[i];
        const errorMsgPrefix = `Error on delete list index ${i}: `;
        if (!(await canadaTreatmentCodeRepository.existsBy({ id: codeId })))
          throw new NotFoundException(`${errorMsgPrefix}Canada Treatment Code not found`);
        if (toBeSavedCanadaTreatmentCodes.some((c) => c.id === codeId))
          throw new BadRequestException(`${errorMsgPrefix}Canada Treatment Code is being edited`);
        if (
          await this.matchingRuleService.isMatchingRuleForSourceRecordExists(
            MatchingRuleSourceDatabaseTable.CANADA_TREATMENT_CODE,
            codeId,
            queryRunner
          )
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada Treatment Code is being used in a matching rule`
          );
      }

      savedCanadaTreatmentCodeIds.push(
        ...(await canadaTreatmentCodeRepository.save(toBeSavedCanadaTreatmentCodes)).map((c) => c.id)
      );
      if (Array.isArray(deleteList) && deleteList.length > 0)
        await canadaTreatmentCodeRepository.delete({ id: In(deleteList) });

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner.isReleased) queryRunner.release();
    }

    return {
      data: await this.canadaTreatmentCodeRepository.find({
        where: { id: In(savedCanadaTreatmentCodeIds) },
        relations: FIND_CANADA_TREATMENT_CODE_RELATIONS
      })
    };
  }
}
