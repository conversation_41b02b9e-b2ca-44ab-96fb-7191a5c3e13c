import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { DataSource, In, Not, QueryRunner, Repository } from "typeorm";
import { CanadaOgd, OgdFiling } from "../entities";
import {
  AuthenticatedRequest,
  CANADA_OGD_ENUM_KEYS,
  CANADA_OGD_MODULE_OPTIONS,
  CANADA_OGD_REQUIRED_KEYS,
  CanadaOgdColumn,
  FIND_CANADA_OGD_RELATIONS,
  MatchingRuleSourceDatabaseTable,
  UserPermission
} from "../types";
import { CrudOptions } from "../types";
import { REQUEST } from "@nestjs/core";
import {
  BatchUpdateCanadaOgdsDto,
  BatchUpdateCanadaOgdsResponseDto,
  CreateCanadaOgdDto,
  EditCanadaOgdDto,
  GetCanadaOgdsDto,
  GetCanadaOgdsResponseDto
} from "../dto";
import { convertFromCamelCase, getFindOptions } from "../helper-functions";
import { MatchingRuleService } from "../matching-rule";

@Injectable({ scope: Scope.REQUEST })
export class CanadaOgdService {
  constructor(
    @InjectRepository(CanadaOgd)
    private readonly canadaOgdRepository: Repository<CanadaOgd>,
    @InjectRepository(OgdFiling)
    private readonly ogdFilingRepository: Repository<OgdFiling>,
    @Inject(MatchingRuleService)
    private readonly matchingRuleService: MatchingRuleService,
    @Inject(CANADA_OGD_MODULE_OPTIONS)
    private readonly options: CrudOptions,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}

  async getCanadaOgds(getCanadaOgdsDto: GetCanadaOgdsDto): Promise<GetCanadaOgdsResponseDto> {
    if (!this.options.readMany) throw new ForbiddenException("Get Canada OGDs is disabled");
    const { where, order, skip, take } = getFindOptions(
      getCanadaOgdsDto,
      CANADA_OGD_ENUM_KEYS,
      [],
      CanadaOgdColumn.id
    );
    const [data, total] = await this.canadaOgdRepository.findAndCount({
      where,
      order,
      relations: FIND_CANADA_OGD_RELATIONS,
      skip,
      take
    });
    return {
      data,
      total,
      skip,
      limit: take
    };
  }

  async getCanadaOgdById(ogdId: number, queryRunner?: QueryRunner) {
    if (!this.options.readOne) throw new ForbiddenException("Get Canada OGD is disabled");
    const canadaOgdRepository = queryRunner
      ? queryRunner.manager.getRepository(CanadaOgd)
      : this.canadaOgdRepository;
    const canadaOgd = await canadaOgdRepository.findOne({
      where: { id: ogdId },
      relations: FIND_CANADA_OGD_RELATIONS
    });
    return canadaOgd;
  }

  async createCanadaOgd(createCanadaOgdDto: CreateCanadaOgdDto) {
    if (!this.options.create) throw new ForbiddenException("Create Canada OGD is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to create Canada OGDs");
    let newCanadaOgd = new CanadaOgd();
    newCanadaOgd.createdBy = this.request?.user || null;
    newCanadaOgd.lastEditedBy = this.request?.user || null;
    for (const [key, value] of Object.entries(createCanadaOgdDto)) {
      if (value === undefined) continue;
      if (CANADA_OGD_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      newCanadaOgd[key] = value;
    }
    if (
      await this.canadaOgdRepository.existsBy({
        agency: newCanadaOgd.agency,
        program: newCanadaOgd.program,
        commodityType: newCanadaOgd.commodityType || null
      })
    )
      throw new BadRequestException(
        "Canada OGD with the same agency, program, and commodity type already exists"
      );

    newCanadaOgd = await this.canadaOgdRepository.save(newCanadaOgd);

    return await this.getCanadaOgdById(newCanadaOgd.id);
  }

  async editCanadaOgd(ogdId: number, editCanadaOgdDto: EditCanadaOgdDto) {
    if (!this.options.update) throw new ForbiddenException("Edit Canada OGD is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to edit Canada OGDs");
    const canadaOgd = await this.getCanadaOgdById(ogdId);
    if (!canadaOgd) throw new NotFoundException("Canada OGD not found");
    canadaOgd.lastEditedBy = this.request?.user || null;
    for (const [key, value] of Object.entries(editCanadaOgdDto)) {
      if (value === undefined) continue;
      if (CANADA_OGD_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      canadaOgd[key] = value;
    }
    if (
      await this.canadaOgdRepository.existsBy({
        id: Not(ogdId),
        agency: canadaOgd.agency,
        program: canadaOgd.program,
        commodityType: canadaOgd.commodityType || null
      })
    )
      throw new BadRequestException(
        "Canada OGD with the same agency, program, and commodity type already exists"
      );
    await this.canadaOgdRepository.save(canadaOgd);
    return await this.getCanadaOgdById(ogdId);
  }

  async deleteCanadaOgd(ogdId: number) {
    if (!this.options.delete) throw new ForbiddenException("Delete Canada OGD is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to delete Canada OGDs");
    if (!(await this.canadaOgdRepository.existsBy({ id: ogdId })))
      throw new NotFoundException("Canada OGD not found");
    if (
      await this.matchingRuleService.isMatchingRuleForSourceRecordExists(
        MatchingRuleSourceDatabaseTable.CANADA_OGD,
        ogdId
      )
    )
      throw new BadRequestException("Canada OGD is being used in a matching rule");
    if (await this.ogdFilingRepository.existsBy({ ogd: { id: ogdId } }))
      throw new BadRequestException("Canada OGD is being used in an OGD Filing");
    await this.canadaOgdRepository.delete({ id: ogdId });
    return;
  }

  async batchUpdateCanadaOgds(
    batchUpdateCanadaOgdsDto: BatchUpdateCanadaOgdsDto
  ): Promise<BatchUpdateCanadaOgdsResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const savedCanadaOgdIds: Array<number> = [];
    try {
      const { create: createList, edit: editList, delete: deleteList } = batchUpdateCanadaOgdsDto;

      if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
        throw new ForbiddenException("You are not authorized to batch update Canada OGDs");
      if (Array.isArray(createList) && createList.length > 0 && !this.options.create)
        throw new ForbiddenException("Create Canada OGD is disabled");
      if (Array.isArray(editList) && editList.length > 0 && !this.options.update)
        throw new ForbiddenException("Edit Canada OGD is disabled");
      if (Array.isArray(deleteList) && deleteList.length > 0 && !this.options.delete)
        throw new ForbiddenException("Delete Canada OGD is disabled");

      const canadaOgdRepository = queryRunner.manager.getRepository(CanadaOgd);
      const ogdFilingRepository = queryRunner.manager.getRepository(OgdFiling);
      const toBeSavedCanadaOgds: Array<CanadaOgd> = [];

      for (let i = 0; i < (createList || []).length; i++) {
        const errorMsgPrefix = `Error on create list index ${i}: `;
        const createCanadaOgdDto = createList[i];
        let newCanadaOgd = new CanadaOgd();
        newCanadaOgd.createdBy = this.request?.user || null;
        newCanadaOgd.lastEditedBy = this.request?.user || null;
        for (const [key, value] of Object.entries(createCanadaOgdDto)) {
          if (value === undefined) continue;
          if (CANADA_OGD_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
          newCanadaOgd[key] = value;
        }
        if (
          toBeSavedCanadaOgds.some(
            (o) =>
              o.agency === newCanadaOgd.agency &&
              o.program === newCanadaOgd.program &&
              (o.commodityType || null) === (newCanadaOgd.commodityType || null)
          ) ||
          (await canadaOgdRepository.existsBy({
            agency: newCanadaOgd.agency,
            program: newCanadaOgd.program,
            commodityType: newCanadaOgd.commodityType || null
          }))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada OGD with the same agency, program, and commodity type already exists`
          );

        toBeSavedCanadaOgds.push(newCanadaOgd);
      }

      for (let i = 0; i < (editList || []).length; i++) {
        const errorMsgPrefix = `Error on edit list index ${i}: `;
        const { id: ogdId, ...editCanadaOgdDto } = editList[i];
        const canadaOgd = await this.getCanadaOgdById(ogdId, queryRunner);
        if (!canadaOgd) throw new NotFoundException(`${errorMsgPrefix}Canada OGD not found`);
        canadaOgd.lastEditedBy = this.request?.user || null;
        for (const [key, value] of Object.entries(editCanadaOgdDto)) {
          if (value === undefined) continue;
          if (CANADA_OGD_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
          canadaOgd[key] = value;
        }
        if (
          toBeSavedCanadaOgds.some(
            (o) =>
              o.agency === canadaOgd.agency &&
              o.program === canadaOgd.program &&
              (o.commodityType || null) === (canadaOgd.commodityType || null)
          ) ||
          (await canadaOgdRepository.existsBy({
            id: Not(
              In(
                toBeSavedCanadaOgds
                  .filter((o) => typeof o.id === "number")
                  .map((o) => o.id)
                  .concat([ogdId])
              )
            ),
            agency: canadaOgd.agency,
            program: canadaOgd.program,
            commodityType: canadaOgd.commodityType || null
          }))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada OGD with the same agency, program, and commodity type already exists`
          );
        toBeSavedCanadaOgds.push(canadaOgd);
      }

      for (let i = 0; i < (deleteList || []).length; i++) {
        const ogdId = deleteList[i];
        const errorMsgPrefix = `Error on delete list index ${i}: `;
        if (!(await canadaOgdRepository.existsBy({ id: ogdId })))
          throw new NotFoundException(`${errorMsgPrefix}Canada OGD not found`);
        if (toBeSavedCanadaOgds.some((o) => o.id === ogdId))
          throw new BadRequestException(`${errorMsgPrefix}Canada OGD is being edited`);
        if (
          await this.matchingRuleService.isMatchingRuleForSourceRecordExists(
            MatchingRuleSourceDatabaseTable.CANADA_OGD,
            ogdId,
            queryRunner
          )
        )
          throw new BadRequestException(`${errorMsgPrefix}Canada OGD is being used in a matching rule`);
        if (await ogdFilingRepository.existsBy({ ogd: { id: ogdId } }))
          throw new BadRequestException(`${errorMsgPrefix}Canada OGD is being used in an OGD Filing`);
      }

      savedCanadaOgdIds.push(...(await canadaOgdRepository.save(toBeSavedCanadaOgds)).map((o) => o.id));
      if (Array.isArray(deleteList) && deleteList.length > 0)
        await canadaOgdRepository.delete({ id: In(deleteList) });

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner.isReleased) queryRunner.release();
    }

    return {
      data: await this.canadaOgdRepository.find({
        where: { id: In(savedCanadaOgdIds) },
        relations: FIND_CANADA_OGD_RELATIONS
      })
    };
  }
}
