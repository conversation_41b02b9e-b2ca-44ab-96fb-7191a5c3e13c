import { DynamicModule, Module } from "@nestjs/common";
import { CANADA_OGD_MODULE_OPTIONS, CrudOptions } from "../types";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CanadaOgdController } from "./canada-ogd.controller";
import { CanadaOgd, OgdFiling } from "../entities";
import { CanadaOgdService } from "./canada-ogd.service";

@Module({})
export class CanadaOgdModule {
  static register(options: CrudOptions): DynamicModule {
    return {
      global: true,
      module: CanadaOgdModule,
      imports: [TypeOrmModule.forFeature([CanadaOgd, OgdFiling])],
      controllers: [CanadaOgdController],
      providers: [
        {
          provide: CANADA_OGD_MODULE_OPTIONS,
          useValue: options
        },
        CanadaOgdService
      ],
      exports: [CanadaOgdService]
    };
  }
}
