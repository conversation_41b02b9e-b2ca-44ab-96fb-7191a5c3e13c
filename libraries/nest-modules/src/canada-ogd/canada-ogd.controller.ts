import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseGuards,
  NotFoundException,
  Post,
  Body,
  Put,
  Delete,
  HttpCode
} from "@nestjs/common";
import { AccessTokenGuard } from "../guards";
import {
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses
} from "../decorators";
import { ApiForbiddenResponse, ApiOperation, ApiParam } from "@nestjs/swagger";
import { ApiTags } from "@nestjs/swagger";
import {
  BatchUpdateCanadaOgdsDto,
  BatchUpdateCanadaOgdsResponseDto,
  CreateCanadaOgdDto,
  EditCanadaOgdDto,
  ForbiddenResponseDto,
  GetCanadaOgdsDto,
  GetCanadaOgdsResponseDto
} from "../dto";
import { CanadaOgdService } from "./canada-ogd.service";
import { CanadaOgd } from "../entities";

@ApiTags("Canada OGD API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("canada-ogds")
export class CanadaOgdController {
  constructor(private readonly canadaOgdService: CanadaOgdService) {}

  @Get()
  @ApiOperation({ summary: "Get Canada OGDs" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: GetCanadaOgdsResponseDto })
  async getCanadaOgds(@Query() getCanadaOgdsDto: GetCanadaOgdsDto) {
    return await this.canadaOgdService.getCanadaOgds(getCanadaOgdsDto);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get Canada OGD by ID" })
  @ApiParam({ name: "id", type: "integer", description: "Canada OGD ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: CanadaOgd })
  async getCanadaOgdById(@Param("id", ParseIntPipe) id: number) {
    const ogd = await this.canadaOgdService.getCanadaOgdById(id);
    if (!ogd) throw new NotFoundException("Canada OGD not found");
    return ogd;
  }

  @Post()
  @ApiOperation({ summary: "Create Canada OGD" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiCreateResponses({ type: CanadaOgd })
  async createCanadaOgd(@Body() createCanadaOgdDto: CreateCanadaOgdDto) {
    return await this.canadaOgdService.createCanadaOgd(createCanadaOgdDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update Canada OGD" })
  @ApiParam({ name: "id", type: "integer", description: "Canada OGD ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: CanadaOgd })
  async editCanadaOgd(@Param("id", ParseIntPipe) id: number, @Body() editCanadaOgdDto: EditCanadaOgdDto) {
    return await this.canadaOgdService.editCanadaOgd(id, editCanadaOgdDto);
  }

  @HttpCode(204)
  @Delete(":id")
  @ApiOperation({ summary: "Delete Canada OGD" })
  @ApiParam({ name: "id", type: "integer", description: "Canada OGD ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiDeleteResponses()
  async deleteCanadaOgd(@Param("id", ParseIntPipe) id: number) {
    await this.canadaOgdService.deleteCanadaOgd(id);
    return;
  }

  @HttpCode(200)
  @Post("batch")
  @ApiOperation({ summary: "Batch update Canada OGDs" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: BatchUpdateCanadaOgdsResponseDto })
  async batchUpdateCanadaOgds(@Body() batchUpdateCanadaOgdsDto: BatchUpdateCanadaOgdsDto) {
    return await this.canadaOgdService.batchUpdateCanadaOgds(batchUpdateCanadaOgdsDto);
  }
}
