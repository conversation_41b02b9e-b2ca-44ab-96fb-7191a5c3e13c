import {
  BadRequestException,
  HttpException,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException
} from "@nestjs/common";
import { cert, getApps, initializeApp } from "firebase-admin/app";
import { getFirestore, Query, FieldValue } from "firebase-admin/firestore";
import { PoaStatus } from "../types/docusign.types";
import {
  FailedBusinessVerification,
  FIREBASE_MODULE_OPTIONS,
  FirebaseModuleOptions,
  OnboardingDocument,
  WhereQuery
} from "../types/firebase.types";
import { SortOrder } from "../types/base.types";

@Injectable()
export class FirebaseService {
  constructor(
    @Inject(FIREBASE_MODULE_OPTIONS)
    private readonly options: FirebaseModuleOptions
  ) {}
  private readonly logger = new Logger(FirebaseService.name);
  private readonly env = process.env.ENVIRONMENT ?? "staging";

  private initializeFirebase() {
    const creds = {
      projectId: this.options.projectId,
      privateKey: this.options.privateKey?.replace(/\\n/g, "\n"),
      clientEmail: this.options.clientEmail
    };
    getApps().length === 0 &&
      initializeApp({
        credential: cert(creds)
      });
    const db = getFirestore("claro");
    const ref = db.collection(this.env === "staging" ? "onboarding" : "onboarding-prod");
    return ref;
  }

  private convertFirebaseTimestamp(obj: OnboardingDocument) {
    return {
      ...obj,
      createdAt: obj.createdAt ? new Date(obj.createdAt._seconds * 1000).toISOString() : null,
      updatedAt: obj.updatedAt ? new Date(obj.updatedAt._seconds * 1000).toISOString() : null,
      poaSignedAt: obj.poaSignedAt ? new Date(obj.poaSignedAt._seconds * 1000).toISOString() : null,
      ...(obj.failedBusinessVerification && {
        failedBusinessVerification: (obj.failedBusinessVerification as FailedBusinessVerification[]).map(
          (item) => ({
            ...item,
            timestamp: item.timestamp ? new Date(item.timestamp._seconds * 1000).toISOString() : null
          })
        )
      }),
      ...(obj.failedCarmRequest && {
        failedCarmRequest: (obj.failedCarmRequest as FailedBusinessVerification[]).map((item) => ({
          ...item,
          timestamp: item.timestamp ? new Date(item.timestamp._seconds * 1000).toISOString() : null
        }))
      })
    };
  }

  async getOnboarding(queries?: WhereQuery[], sortBy?: string, sortOrder?: SortOrder) {
    const ref = this.initializeFirebase();
    let query: Query = ref;

    if (queries)
      for (const [field, op, value] of queries) {
        query = query.where(field, op, value);
      }

    if (sortBy && sortOrder) query = query.orderBy(sortBy, sortOrder);

    try {
      const snapshot = await query.get();
      if (snapshot.empty) {
        throw new NotFoundException("No onboarding documents found");
      }

      const docs = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...this.convertFirebaseTimestamp(doc.data() as OnboardingDocument)
      }));
      return docs;
    } catch (error: any) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException(`Failed to fetch documents: ${error?.message}`);
    }
  }

  async getOnboardingById(id: string) {
    const ref = this.initializeFirebase();
    const doc = ref.doc(id);

    try {
      const snapshot = await doc.get();
      if (!snapshot.exists) {
        throw new NotFoundException("Document not found");
      }
      return {
        ...this.convertFirebaseTimestamp(snapshot.data() as OnboardingDocument),
        id: snapshot.id
      };
    } catch (error: any) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException(`Failed to fetch document: ${error?.message}`);
    }
  }

  async updateOnboarding(id: string, params: Partial<OnboardingDocument>) {
    const ref = this.initializeFirebase();
    const doc = ref.doc(id);

    if (!id || !params) throw new BadRequestException("ID and params are required");

    try {
      await doc.update(params);
    } catch (error: any) {
      if (error instanceof HttpException) {
        throw error;
      }
      if (error.code === 5) {
        throw new NotFoundException(`Document with ID ${id} not found`);
      }
      throw new InternalServerErrorException(`Failed to update document: ${error?.message}`);
    }
  }

  async updatePoaStatus(envelopeId: string) {
    if (!envelopeId) throw new BadRequestException("Envelope ID is required");

    const ref = this.initializeFirebase();

    try {
      const snapshot = await ref.where("envelopeId", "==", envelopeId).limit(1).get();

      if (snapshot.empty) {
        this.logger.warn(`Document with envelope id: ${envelopeId} not found, skipping...`);
        return;
      }

      const doc = snapshot.docs[0];
      await doc.ref.update({
        poaStatus: PoaStatus.SIGNED,
        poaSignedAt: FieldValue.serverTimestamp()
      });

      return true;
    } catch (error: any) {
      this.logger.error(JSON.stringify(error));

      throw new InternalServerErrorException(`Failed to update onboarding: ${error?.message}`);
    }
  }
}
