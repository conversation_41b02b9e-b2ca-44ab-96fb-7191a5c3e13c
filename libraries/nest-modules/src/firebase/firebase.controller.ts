import { Body, Controller, Get, HttpCode, Param, Post, Put, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import { ApiAccessTokenAuthenticated } from "../decorators";
import { WhereQueryDto } from "../dto";
import { AccessTokenGuard } from "../guards";
import { OnboardingDocument } from "../types";
import { FirebaseService } from "./firebase.service";

@ApiTags("Firebase API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("firebase/onboarding")
export class FirebaseController {
  constructor(private readonly firebaseService: FirebaseService) {}

  @Post()
  @HttpCode(200)
  @ApiOperation({ summary: "Get Onboarding List" })
  async sendRequest(@Body() { whereQueries, sortBy, sortOrder }: WhereQueryDto) {
    return await this.firebaseService.getOnboarding(whereQueries, sortBy, sortOrder);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get Onboarding Detail" })
  @ApiParam({ name: "id", type: "integer", description: "Onboarding ID" })
  async getOnboardingById(@Param("id") id: string) {
    return await this.firebaseService.getOnboardingById(id);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update Onboarding" })
  @ApiParam({ name: "id", type: "integer", description: "Onboarding ID" })
  async updateOnboarding(@Param("id") id: string, @Body() body: OnboardingDocument) {
    return await this.firebaseService.updateOnboarding(id, body);
  }
}
