import { DynamicModule, Module } from "@nestjs/common";
import { FIREBASE_MODULE_OPTIONS, FirebaseModuleOptions } from "../types";
import { FirebaseService } from "./firebase.service";
import { FirebaseController } from "./firebase.controller";

@Module({})
export class FirebaseModule {
  static register(options: FirebaseModuleOptions): DynamicModule {
    return {
      global: true,
      module: FirebaseModule,
      providers: [
        FirebaseService,
        {
          provide: FIREBASE_MODULE_OPTIONS,
          useValue: options
        }
      ],
      controllers: [FirebaseController],
      exports: [FirebaseService]
    };
  }
}
