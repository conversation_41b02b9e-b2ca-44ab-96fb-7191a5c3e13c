import { Injectable } from "@nestjs/common";
import { CustomsStatus } from "../types";
import { ValidateShipmentComplianceResponseDto } from "../dto";
import { Shipment } from "../entities";
import {
  isSubmittedCustomsStatus,
  isPendingCustomsStatus,
  isLiveCustomsStatus,
  isReleasedCustomsStatus,
  isSendCADReady,
  isSendRNSProofOfReleaseReady,
  isReadyToSubmit
} from "../constants";

/**
 * Service containing all shipment-related business rules in one place.
 * This consolidates duplicate logic from various parts of the codebase.
 */
@Injectable()
export class ShipmentBusinessRulesService {
  /**
   * Check if a shipment can be rushed based on its status and compliance
   */
  canShipmentBeRushed(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto): boolean {
    return !this.isShipmentSubmitted(shipment) && isReadyToSubmit(compliance);
  }

  /**
   * Check if a shipment has been submitted to customs
   */
  isShipmentSubmitted(shipment: Shipment): boolean {
    return isSubmittedCustomsStatus(shipment.customsStatus);
  }

  /**
   * Check if a shipment can be modified (inverse of isSubmitted)
   */
  canShipmentBeModified(shipment: Shipment): boolean {
    return !this.isShipmentSubmitted(shipment);
  }

  /**
   * Check if a shipment is in a pending status
   */
  isShipmentPending(shipment: Shipment): boolean {
    return isPendingCustomsStatus(shipment.customsStatus);
  }

  /**
   * Check if a shipment is in live status
   */
  isShipmentLive(shipment: Shipment): boolean {
    return isLiveCustomsStatus(shipment.customsStatus);
  }

  /**
   * Check if a shipment has been released
   */
  isShipmentReleased(shipment: Shipment): boolean {
    return isReleasedCustomsStatus(shipment.customsStatus);
  }

  /**
   * Check if a shipment is ready for CAD document generation
   */
  canGenerateCAD(shipment: Shipment): boolean {
    return isSendCADReady(shipment.customsStatus);
  }

  /**
   * Check if a shipment is ready for RNS proof generation
   */
  canGenerateRNSProof(shipment: Shipment): boolean {
    return isSendRNSProofOfReleaseReady(shipment.customsStatus);
  }

  /**
   * Check if a shipment is compliant based on validation response
   */
  isShipmentCompliant(compliance: ValidateShipmentComplianceResponseDto): boolean {
    return isReadyToSubmit(compliance);
  }

  /**
   * Check if entry has been uploaded to customs
   */
  isEntryUploaded(shipment: Shipment): boolean {
    // Entry is uploaded if it's in LIVE status or beyond
    return (
      this.isShipmentLive(shipment) ||
      this.isShipmentSubmitted(shipment) ||
      this.isShipmentReleased(shipment)
    );
  }

  /**
   * Check if entry can be updated after upload
   */
  canUpdateEntry(shipment: Shipment): boolean {
    // Can update if in LIVE status (not yet submitted)
    return this.isShipmentLive(shipment);
  }

  /**
   * Get blocking reason for rush processing
   */
  getRushBlockingReason(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto): string | null {
    if (this.isShipmentSubmitted(shipment)) {
      return "Shipment has already been submitted to customs";
    }
    if (!isReadyToSubmit(compliance)) {
      return "Shipment has compliance issues that must be resolved first";
    }
    return null;
  }

  /**
   * Get blocking reason for CAD generation
   */
  getCADBlockingReason(shipment: Shipment): string | null {
    if (!this.canGenerateCAD(shipment)) {
      const status = shipment.customsStatus;
      if (isPendingCustomsStatus(status)) {
        return "CAD document is not available until the shipment entry is accepted by customs";
      }
      if (status === CustomsStatus.LIVE || status === CustomsStatus.ENTRY_SUBMITTED) {
        return "CAD document will be available once the entry is accepted";
      }
      return "CAD document is not available for this shipment status";
    }
    return null;
  }

  /**
   * Get blocking reason for RNS proof generation
   */
  getRNSBlockingReason(shipment: Shipment): string | null {
    if (!this.canGenerateRNSProof(shipment)) {
      if (this.isShipmentReleased(shipment)) {
        return null; // Already released, should be able to generate
      }
      return "RNS proof of release is only available after the shipment has been released by customs";
    }
    return null;
  }

  /**
   * Check if all required documents have been received
   */
  areAllDocumentsReceived(
    hasHBLData: boolean,
    hasAnEmfData: boolean,
    hasCIData: boolean,
    transportMode: string
  ): boolean {
    // For air shipments, we need AN/EMF instead of HBL
    const hasTransportDoc = transportMode === "AIR" ? hasAnEmfData : hasHBLData;
    return hasTransportDoc && hasCIData;
  }

  /**
   * Determine if a shipment requires OGD filing
   */
  requiresOGDFiling(shipment: Shipment): boolean {
    // This would typically check for controlled goods, special commodities, etc.
    // For now, returning false as default
    return false;
  }

  /**
   * Check if shipment needs manual review
   */
  needsManualReview(shipment: Shipment, compliance: ValidateShipmentComplianceResponseDto): boolean {
    // Manual review needed if there are non-compliant invoices or specific error conditions
    return (compliance.nonCompliantInvoices?.length ?? 0) > 0;
  }
}