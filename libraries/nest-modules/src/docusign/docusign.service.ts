import {
  ForbiddenException,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import axios, { isAxiosError } from "axios";
import moment from "moment-timezone";
import { IsNull, Not, Repository } from "typeorm";
import { DocusignToken } from "../entities/docusign-token.entity";
import { AuthenticatedRequest } from "../types/auth.types";
import {
  DOCUSIGN_MODULE_OPTIONS,
  DocusignCreateEnvelopeRequestBody,
  DocusignModuleOptions,
  FIND_DOCUSIGN_TOKEN_RELATIONS
} from "../types/docusign.types";
import { DocusignAccessTokenResponse } from "../types/oauth.types";

@Injectable({ scope: Scope.REQUEST })
export class BaseDocusignService {
  constructor(
    @InjectRepository(DocusignToken)
    private readonly docusignTokenRepository: Repository<DocusignToken>,
    @Inject(DOCUSIGN_MODULE_OPTIONS)
    private readonly options: DocusignModuleOptions,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest
  ) {}

  private readonly logger = new Logger(BaseDocusignService.name);

  private async refreshAccessToken(docusignToken: DocusignToken) {
    const THIRTY_MINUTES_MS = 30 * 60 * 1000;
    const THIRTY_DAYS_MS = 30 * 24 * 60 * 60 * 1000;
    const currentMs = Date.now();

    if (currentMs < docusignToken.accessTokenExpiresIn.getTime() - THIRTY_MINUTES_MS) {
      this.logger.log("Access Token not expired, skip refreshing...");
      return docusignToken;
    }

    if (currentMs >= docusignToken.refreshTokenExpiresIn.getTime())
      throw new ForbiddenException("Docusign refresh token already expired");

    const refreshRes = await axios.post(
      `${this.options.baseUrl}/oauth/token`,
      {
        grant_type: "refresh_token",
        refresh_token: docusignToken.refreshToken
      },
      {
        auth: {
          username: this.options.integrationKey,
          password: this.options.secretKey
        },
        headers: { "Content-Type": "application/x-www-form-urlencoded" }
      }
    );
    const { access_token, refresh_token, expires_in } = refreshRes.data as DocusignAccessTokenResponse;
    await this.docusignTokenRepository.update(
      { accountId: docusignToken.accountId },
      {
        accessToken: access_token,
        refreshToken: refresh_token,
        accessTokenExpiresIn: moment.tz(currentMs + expires_in, "America/Toronto").toDate(),
        refreshTokenExpiresIn: moment.tz(currentMs + THIRTY_DAYS_MS, "America/Toronto").toDate(),
        lastEditedBy: this.request?.user || null
      }
    );
    return await this.docusignTokenRepository.findOne({
      where: { accountId: docusignToken.accountId },
      relations: FIND_DOCUSIGN_TOKEN_RELATIONS
    });
  }

  async sendSignatureRequest(payload: DocusignCreateEnvelopeRequestBody) {
    let docusignToken = await this.docusignTokenRepository.findOne({
      where: { accountId: Not(IsNull()) },
      relations: FIND_DOCUSIGN_TOKEN_RELATIONS
    });
    if (!docusignToken) throw new ForbiddenException("Docusign Access Token not found");
    docusignToken = await this.refreshAccessToken(docusignToken);

    const templateId = this.options.poaTemplateId;

    const requestBody: DocusignCreateEnvelopeRequestBody = {
      ...payload,
      templateId
    };

    try {
      const createEnvelopeRes = await axios.post(
        `${docusignToken.baseUri}/restapi/v2.1/accounts/${docusignToken.accountId}/envelopes`,
        requestBody,
        {
          headers: { Authorization: `Bearer ${docusignToken.accessToken}` }
        }
      );

      const { envelopeId, statusDateTime } = createEnvelopeRes.data;
      return { envelopeId, statusDateTime };
    } catch (error) {
      this.logger.error("Failed to send signature request", error);
      if (isAxiosError(error) && error.response?.data?.message) {
        throw new Error(`Failed to send signature request. ${error.response.data.message}`);
      }
      throw new InternalServerErrorException("Failed to send signature request");
    }
  }

  async resendEnvelope(envelopeId: string, newPayload?: DocusignCreateEnvelopeRequestBody) {
    let docusignToken = await this.docusignTokenRepository.findOne({
      where: { accountId: Not(IsNull()) },
      relations: FIND_DOCUSIGN_TOKEN_RELATIONS
    });
    if (!docusignToken) throw new ForbiddenException("Docusign Access Token not found");
    docusignToken = await this.refreshAccessToken(docusignToken);

    try {
      if (!newPayload) {
        // Simply resend the existing envelope
        const resendRes = await axios.put(
          `${docusignToken.baseUri}/restapi/v2.1/accounts/${docusignToken.accountId}/envelopes/${envelopeId}`,
          { resendEnvelope: "true" },
          {
            headers: { Authorization: `Bearer ${docusignToken.accessToken}` }
          }
        );
        return { envelopeId: resendRes.data.envelopeId };
      } else {
        // Void the existing envelope
        await axios.put(
          `${docusignToken.baseUri}/restapi/v2.1/accounts/${docusignToken.accountId}/envelopes/${envelopeId}`,
          {
            status: "voided",
            voidedReason: "Changed recipient information"
          },
          {
            headers: { Authorization: `Bearer ${docusignToken.accessToken}` }
          }
        );

        // Create a new envelope with updated information
        return this.sendSignatureRequest(newPayload);
      }
    } catch (error) {
      this.logger.error("Failed to resend envelope", error);
      if (isAxiosError(error) && error.response?.data?.message) {
        throw new Error(`Failed to resend envelope. ${error.response.data.message}`);
      }
      throw new InternalServerErrorException("Failed to resend envelope");
    }
  }
}
