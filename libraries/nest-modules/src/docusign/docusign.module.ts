import { DynamicModule, Module } from "@nestjs/common";
import { DOCUSIGN_MODULE_OPTIONS, DocusignModuleOptions } from "../types/docusign.types";
import { BaseDocusignService } from "./docusign.service";
import { TypeOrmModule } from "@nestjs/typeorm";
import { DocusignToken } from "../entities/docusign-token.entity";

@Module({})
export class BaseDocusignModule {
  static register(options?: DocusignModuleOptions): DynamicModule {
    return {
      global: true,
      module: BaseDocusignModule,
      imports: [TypeOrmModule.forFeature([DocusignToken])],
      providers: [
        BaseDocusignService,
        {
          provide: DOCUSIGN_MODULE_OPTIONS,
          useValue: options
        }
      ],
      exports: [BaseDocusignService]
    };
  }
}
