import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  Scope
} from "@nestjs/common";
import axios from "axios";
import {
  CandataExchangeRateDto,
  CandataExchangeRateResponseDto,
  CandataProductDto,
  CandataRNSResponseDto,
  GetCandataProductsResponseDto
} from "../dto";
import { CandataCustomerDto } from "../dto/candata-customer.dto";
import { CandataIidResponseDto, CandataShipmentDto } from "../dto/candata-shipment.dto";
import { parseXml } from "../helper-functions";
import {
  CANDATA_MODULE_OPTIONS,
  CandataAccountAuthType,
  CandataErrorHandling,
  CandataModuleOptions,
  CandataSendIidMessageFunction
} from "../types/candata.types";
import { OrganizationCustomsBroker } from "../types";

@Injectable({ scope: Scope.REQUEST })
export class CandataService {
  constructor(
    @Inject(CANDATA_MODULE_OPTIONS)
    private readonly options: CandataModuleOptions
  ) {}
  private readonly accessTokenMap = new Map<
    OrganizationCustomsBroker,
    {
      accessToken: string;
      expiresAt: Date;
    }
  >();
  /** Base URL for performing actions on the Candata UI */
  static readonly CANDATA_UI_BASE_URL = "http://***************/api";
  private readonly logger = new Logger(CandataService.name);

  /**
   * Converts a date to a Candata date format
   * @param date - The date to convert
   * @returns The date in Candata date format
   * @deprecated Use `MapperFacade.dateToCandataDate` instead
   */
  static toCandataDate(date: Date) {
    return date.toISOString().split("T")[0];
  }

  async clearCandataCci(transactionNumber: string, account = OrganizationCustomsBroker.CLARO) {
    if (this.options.appId === undefined) throw new InternalServerErrorException("App ID is not defined");
    if (!transactionNumber) throw new BadRequestException("Invalid transaction number");

    try {
      const response = await axios.get(`${CandataService.CANDATA_UI_BASE_URL}/clearCci`, {
        params: { transactionNumber },
        headers: {
          "x-app-id": this.options.appId
        }
      });
      return true;
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || String(error);
      throw new InternalServerErrorException(`Failed to clear Candata CCI: ${message}`);
    }
  }

  async deleteCandataCci(transactionNumber: string, account = OrganizationCustomsBroker.CLARO) {
    if (this.options.appId === undefined) throw new InternalServerErrorException("App ID is not defined");
    if (!transactionNumber) throw new BadRequestException("Invalid transaction number");

    try {
      const response = await axios.get(`${CandataService.CANDATA_UI_BASE_URL}/deleteCci`, {
        params: { transactionNumber },
        headers: {
          "x-app-id": this.options.appId
        }
      });
      return true;
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || String(error);
      throw new InternalServerErrorException(`Failed to delete Candata CCI: ${message}`);
    }
  }

  /**
   * Returns the authentication header for the given account
   * @param account - The customs broker represented by the Candata account
   * @returns The authentication header used to authenticate requests to the Candata API and the base URL of the Candata API
   */
  private async getAuthenticationHeadersAndBaseUrl(account: OrganizationCustomsBroker): Promise<{
    baseUrl: string;
    authHeaders: { "X-Candata-Token": string } | { Authorization: string };
  }> {
    const accountCred = (this.options.accounts || [])?.find((acc) => acc.account === account);
    if (!accountCred)
      throw new InternalServerErrorException(`Candata API credentials for account ${account} not found`);
    if (!accountCred.baseUrl)
      throw new InternalServerErrorException(`Candata API base URL for account ${account} not found`);
    switch (accountCred.authType) {
      case CandataAccountAuthType.API_KEY: {
        this.logger.debug(
          `Candata account: ${account}, API key type: ${typeof accountCred.apiKey}, API key length: ${
            accountCred?.apiKey?.length
          }`
        );
        if (!accountCred.apiKey)
          throw new InternalServerErrorException(`Candata API key for account ${account} not found`);
        return {
          baseUrl: accountCred.baseUrl,
          authHeaders: { "X-Candata-Token": accountCred.apiKey }
        };
      }
      case CandataAccountAuthType.CLIENT_ID_AND_SECRET: {
        const { accessToken: existingAccessToken, expiresAt: existingExpiresAt } =
          this.accessTokenMap.get(account) || {};
        if (existingAccessToken && existingExpiresAt && existingExpiresAt > new Date()) {
          this.logger.debug(
            `Using existing access token for account ${account}, expires at ${existingExpiresAt}...`
          );
          return {
            baseUrl: accountCred.baseUrl,
            authHeaders: { Authorization: `Bearer ${existingAccessToken}` }
          };
        }
        if (!accountCred.clientId || !accountCred.clientSecret)
          throw new InternalServerErrorException(
            `Candata client ID and secret for account ${account} not found`
          );
        if (!accountCred.authUrl)
          throw new InternalServerErrorException(`Candata auth URL for account ${account} not found`);
        try {
          const res = await axios.post(
            accountCred.authUrl,
            {
              client_id: accountCred.clientId,
              client_secret: accountCred.clientSecret,
              grant_type: "client_credentials"
            },
            { headers: { "Content-Type": "application/x-www-form-urlencoded" } }
          );
          if (!res.data?.access_token) throw new Error(`No access token returned`);
          if (typeof res.data?.expires_in !== "number" || res.data.expires_in <= 0)
            throw new Error("Invalid expires in value");
          this.accessTokenMap.set(account, {
            accessToken: res.data.access_token,
            expiresAt: new Date(Date.now() + res.data.expires_in * 1000)
          });
          return {
            baseUrl: accountCred.baseUrl,
            authHeaders: { Authorization: `Bearer ${res.data.access_token}` }
          };
        } catch (error) {
          this.logger.error(
            `Failed to get Candata access token for account ${account}: ${
              error instanceof Error ? error.message : String(error)
            }`,
            error instanceof Error ? error.stack : undefined
          );
          throw new InternalServerErrorException(`Failed to get Candata access token for account ${account}`);
        }
      }
      default:
        throw new InternalServerErrorException(`Invalid Candata authentication type for account ${account}`);
    }
  }

  async getCandataShipment(shipmentId: string, account = OrganizationCustomsBroker.CLARO) {
    if (!shipmentId) {
      throw new BadRequestException("Invalid shipment ID");
    }

    const { baseUrl, authHeaders } = await this.getAuthenticationHeadersAndBaseUrl(account);

    try {
      const response = await axios.get(`${baseUrl}/shipments/${shipmentId}`, {
        headers: {
          ...authHeaders,
          "Content-Type": "application/json"
        }
      });
      if (!response.data) throw new Error("No shipment data returned");
      return response.data as CandataShipmentDto;
    } catch (error: any) {
      const message = error.response?.data?.message || error.message;
      throw new InternalServerErrorException(`Failed to fetch shipment data: ${message}`);
    }
  }

  async getMultipleCandataShipments(
    shipmentIds: Array<string>,
    errorHandling = CandataErrorHandling.ERROR_ON_FIRST_ERROR,
    account = OrganizationCustomsBroker.CLARO
  ) {
    if (shipmentIds.length <= 0) throw new BadRequestException("No shipment IDs provided");
    // This function is called before the requests are made to save the access token first
    await this.getAuthenticationHeadersAndBaseUrl(account);
    try {
      const promiseResults = await Promise.allSettled(
        shipmentIds.map((shipmentId) => this.getCandataShipment(shipmentId, account))
      );
      switch (errorHandling) {
        case CandataErrorHandling.IGNORE_ERRORS:
          return promiseResults.map((result) => (result.status === "fulfilled" ? result.value : null));
        case CandataErrorHandling.ERROR_ON_ALL_ERRORS:
          if (promiseResults.every((result) => result.status === "rejected"))
            throw new Error(
              `All requests failed: ${JSON.stringify(promiseResults.map((result) => result.reason))}`
            );
          return promiseResults.map((result) => (result.status === "fulfilled" ? result.value : null));
        case CandataErrorHandling.ERROR_ON_FIRST_ERROR:
        default:
          if (promiseResults.some((result) => result.status === "rejected"))
            throw new Error(
              `Some requests failed: ${JSON.stringify(
                promiseResults.filter((result) => result.status === "rejected").map((result) => result.reason)
              )}`
            );
          return promiseResults.map((result) => (result as PromiseFulfilledResult<CandataShipmentDto>).value);
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      throw new InternalServerErrorException(`Failed to get multiple Candata shipments: ${message}`);
    }
  }

  async createCandataShipment(
    createCandataShipmentDto: CandataShipmentDto,
    account = OrganizationCustomsBroker.CLARO
  ) {
    const { baseUrl, authHeaders } = await this.getAuthenticationHeadersAndBaseUrl(account);
    try {
      const response = await axios.post(`${baseUrl}/shipments`, createCandataShipmentDto, {
        headers: {
          ...authHeaders,
          "Content-Type": "application/json"
        }
      });
      return response.data as CandataShipmentDto;
    } catch (error: any) {
      const message = error.response?.data?.["error message"] || error.message;
      this.logger.error(JSON.stringify(error.response?.data));
      throw new InternalServerErrorException(`Failed to create shipment: ${message}`);
    }
  }

  async updateCandataShipment(
    shipmentId: string,
    updateCandataShipmentDto: Omit<Partial<CandataShipmentDto>, "b3" | "declaration">,
    account = OrganizationCustomsBroker.CLARO
  ) {
    const { baseUrl, authHeaders } = await this.getAuthenticationHeadersAndBaseUrl(account);
    try {
      const response = await axios.patch(`${baseUrl}/shipments/${shipmentId}`, updateCandataShipmentDto, {
        headers: {
          ...authHeaders,
          "Content-Type": "application/json"
        }
      });
      return response.data as CandataShipmentDto;
    } catch (error: any) {
      const message = error.response?.data?.["error message"] || error.message;
      this.logger.error(JSON.stringify(error.response?.data));
      throw new InternalServerErrorException(`Failed to update shipment: ${message}`);
    }
  }

  async deleteCandataShipment(
    customerNumber: string,
    shipmentId: string,
    account = OrganizationCustomsBroker.CLARO
  ) {
    const { baseUrl, authHeaders } = await this.getAuthenticationHeadersAndBaseUrl(account);
    try {
      const response = await axios.delete(`${baseUrl}/customers/${customerNumber}/shipments/${shipmentId}`, {
        headers: {
          ...authHeaders,
          "Content-Type": "application/json",
          Accept: "application/json"
        }
      });
      return response.data as CandataShipmentDto;
    } catch (error: any) {
      const message = error.response?.data?.["error message"] || error.message;
      this.logger.error(JSON.stringify(error.response?.data));
      throw new InternalServerErrorException(`Failed to delete shipment: ${message}`);
    }
  }

  async findRnsResponseByTransactionNumbers(
    transactionNumbers: Array<string>,
    account = OrganizationCustomsBroker.CLARO
  ) {
    const { baseUrl, authHeaders } = await this.getAuthenticationHeadersAndBaseUrl(account);
    if (transactionNumbers.some((tn) => !/^[0-9]{14}$/.test(tn))) {
      throw new BadRequestException("Invalid transaction numbers");
    }

    try {
      const response = await axios.post(
        `${baseUrl}/cbsa/responses/rns/transaction`,
        transactionNumbers.join("\n"),
        {
          headers: {
            ...authHeaders,
            "Content-Type": "text/plain",
            Accept: "application/json"
          }
        }
      );
      return (response.data as Array<CandataRNSResponseDto>) ?? [];
    } catch (error: any) {
      const message = error.response?.data?.message || error.message;
      this.logger.error(error);
      throw new InternalServerErrorException(
        `Failed to find RNS response by transaction numbers: ${message}`
      );
    }
  }

  async findRnsResponseByCargoControlNumbers(
    cargoControlNumbers: Array<string>,
    account = OrganizationCustomsBroker.CLARO
  ) {
    const { baseUrl, authHeaders } = await this.getAuthenticationHeadersAndBaseUrl(account);
    if (cargoControlNumbers.length <= 0) throw new BadRequestException("No cargo control number provided");
    if (cargoControlNumbers.some((ccn) => !/^[0-9A-Z]{3}(-|[0-9A-Z])[0-9A-Z]{1,21}$/.test(ccn)))
      throw new BadRequestException("Invalid cargo control numbers");

    try {
      const response = await axios.post(
        `${baseUrl}/cbsa/responses/rns/cargo`,
        cargoControlNumbers.join("\n"),
        {
          headers: {
            ...authHeaders,
            "Content-Type": "text/plain",
            Accept: "application/json"
          }
        }
      );
      return Array.isArray(response.data) ? (response.data as Array<CandataRNSResponseDto>) : [];
    } catch (error: any) {
      const message = error.response?.data?.message || error.message;
      this.logger.error(error);
      throw new InternalServerErrorException(
        `Failed to find RNS response by cargo control numbers: ${message}`
      );
    }
  }

  async getCandataCustomer(customerNumber: string, account = OrganizationCustomsBroker.CLARO) {
    const { baseUrl, authHeaders } = await this.getAuthenticationHeadersAndBaseUrl(account);
    if (!customerNumber?.trim()) {
      throw new BadRequestException("Invalid customer number");
    }
    try {
      const response = await axios.get(`${baseUrl}/customers/${customerNumber}`, {
        headers: {
          ...authHeaders,
          "Content-Type": "application/json"
        }
      });
      return response.data as CandataCustomerDto;
    } catch (error: any) {
      const message = error.response?.data?.message || error.message;
      this.logger.error(error);
      throw new InternalServerErrorException(`Failed to fetch customer: ${message}`);
    }
  }

  async findCandataCustomers(searchString: string, account = OrganizationCustomsBroker.CLARO) {
    const { baseUrl, authHeaders } = await this.getAuthenticationHeadersAndBaseUrl(account);
    if (!searchString?.trim()) {
      throw new BadRequestException("Invalid search string");
    }

    try {
      const response = await axios.get(`${baseUrl}/customers`, {
        headers: {
          ...authHeaders,
          "Content-Type": "application/json"
        },
        params: {
          search: searchString
        }
      });
      return response.data as Array<CandataCustomerDto>;
    } catch (error: any) {
      const message = error.response?.data?.message || error.message;
      this.logger.error(error);
      throw new InternalServerErrorException(`Failed to fetch customers: ${message}`);
    }
  }

  async createCandataCustomer(
    createCandataCustomerDto: CandataCustomerDto,
    account = OrganizationCustomsBroker.CLARO
  ) {
    const { baseUrl, authHeaders } = await this.getAuthenticationHeadersAndBaseUrl(account);
    try {
      const response = await axios.post(`${baseUrl}/customers`, createCandataCustomerDto, {
        headers: {
          ...authHeaders,
          "Content-Type": "application/json"
        }
      });
      return response.data as CandataCustomerDto;
    } catch (error: any) {
      const message = error.response?.data?.message || error.message;
      throw new InternalServerErrorException(`Failed to create customer: ${message}`);
    }
  }

  async recapShipment(fileNumber: string, account = OrganizationCustomsBroker.CLARO) {
    const { baseUrl, authHeaders } = await this.getAuthenticationHeadersAndBaseUrl(account);
    try {
      const response = await axios.get(`${baseUrl}/b3s/${fileNumber}/recap`, {
        headers: {
          ...authHeaders,
          "Content-Type": "application/json"
        }
      });
      return response.data as CandataShipmentDto;
    } catch (error: any) {
      const message = error.response?.data?.message || error.message;
      this.logger.error(error);
      throw new InternalServerErrorException(`Failed to recap shipment: ${message}`);
    }
  }

  async createCandataProduct(
    createCandataProductDto: CandataProductDto,
    account = OrganizationCustomsBroker.CLARO
  ) {
    const { baseUrl, authHeaders } = await this.getAuthenticationHeadersAndBaseUrl(account);
    try {
      const response = await axios.post(`${baseUrl}/products`, createCandataProductDto, {
        headers: {
          ...authHeaders,
          "Content-Type": "application/json"
        }
      });
      return response.data as CandataProductDto;
    } catch (error: any) {
      const message = error.response?.data?.message || error.message;
      this.logger.error(error);
      throw new InternalServerErrorException(`Failed to create product: ${message}`);
    }
  }

  async getCandataProducts(productCode: string, account = OrganizationCustomsBroker.CLARO) {
    const { baseUrl, authHeaders } = await this.getAuthenticationHeadersAndBaseUrl(account);
    try {
      const response = await axios.get(`${baseUrl}/products`, {
        params: {
          code: productCode
        },
        headers: {
          ...authHeaders,
          "Content-Type": "application/json"
        }
      });
      return response.data as GetCandataProductsResponseDto;
    } catch (error: any) {
      const message = error.response?.data?.message || error.message;
      this.logger.error(error);
      throw new InternalServerErrorException(`Failed to get products: ${message}`);
    }
  }

  async calculateCad(shipmentId: string, account = OrganizationCustomsBroker.CLARO) {
    const { baseUrl, authHeaders } = await this.getAuthenticationHeadersAndBaseUrl(account);
    try {
      const response = await axios.get(`${baseUrl}/shipments/${shipmentId}/carm/calculate`, {
        headers: {
          ...authHeaders,
          "Content-Type": "application/json"
        }
      });
      return response.data as CandataShipmentDto;
    } catch (error: any) {
      const message = error.response?.data?.message || error.message;
      this.logger.error(error);
      throw new InternalServerErrorException(`Failed to calculate CAD: ${message}`);
    }
  }

  async sendIid(
    shipmentId: string,
    messageFunction: CandataSendIidMessageFunction,
    account = OrganizationCustomsBroker.CLARO
  ) {
    const { baseUrl, authHeaders } = await this.getAuthenticationHeadersAndBaseUrl(account);
    try {
      const response = await axios.get(`${baseUrl}/shipments/${shipmentId}/iid/send/${messageFunction}`, {
        headers: {
          ...authHeaders,
          Accept: "application/xml"
        }
      });
      return parseXml<CandataIidResponseDto>(response.data, true);
    } catch (error: any) {
      const message = error.response?.data?.message || error.message;
      this.logger.error(error);
      throw new InternalServerErrorException(`Failed to send IID message: ${message}`);
    }
  }

  async getExchangeRate(
    params: CandataExchangeRateDto,
    account = OrganizationCustomsBroker.CLARO
  ): Promise<CandataExchangeRateResponseDto> {
    const { baseUrl, authHeaders } = await this.getAuthenticationHeadersAndBaseUrl(account);
    try {
      const url = `${baseUrl}/currency/${params.currencyCode}/exchange${
        params.date ? `?date=${params.date}` : ""
      }`;
      const res = await axios.get(url, {
        headers: {
          ...authHeaders,
          Accept: "application/json"
        }
      });
      return res.data;
    } catch (error: any) {
      const message = error.response?.data?.message || error.message;
      this.logger.error(error);
      throw new InternalServerErrorException(`Failed to get exchange rate: ${message}`);
    }
  }
}
