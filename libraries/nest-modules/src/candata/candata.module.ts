import { DynamicModule, Module } from "@nestjs/common";
import { CandataService } from "./candata.service";
import { CANDATA_MODULE_OPTIONS, CandataModuleOptions } from "../types";
import { CandataController } from "./candata.controller";

@Module({})
export class CandataModule {
  static register(options: CandataModuleOptions): DynamicModule {
    return {
      global: true,
      module: CandataModule,
      providers: [
        CandataService,
        {
          provide: CANDATA_MODULE_OPTIONS,
          useValue: options
        }
      ],
      controllers: [CandataController],
      exports: [CandataService]
    };
  }
}
