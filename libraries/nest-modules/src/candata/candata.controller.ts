import { UseGuards, Controller, Get, Query } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiOkResponse, ApiExcludeEndpoint } from "@nestjs/swagger";
import { ApiAccessTokenAuthenticated } from "../decorators";
import { AccessTokenGuard } from "../guards";
import { CandataService } from "./candata.service";
import { CandataExchangeRateDto, CandataExchangeRateResponseDto } from "../dto";

@ApiTags("Candata API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("candata")
export class CandataController {
  constructor(private readonly candataService: CandataService) {}
  @Get("exchange-rate")
  @ApiOperation({ summary: "Get Exchange Rate" })
  @ApiOkResponse({ type: CandataExchangeRateResponseDto })
  async getExchangeRate(@Query() params: CandataExchangeRateDto) {
    return await this.candataService.getExchangeRate(params);
  }

  @ApiExcludeEndpoint()
  @Get("test-sync")
  async testSync() {
    for (let i = 0; i < 10; i++) {
      console.log(`Getting exchange rate ${i + 1} out of 10 times...`);
      const res = await this.candataService.getExchangeRate({
        currencyCode: "USD",
        date: "2025-06-26"
      });
      console.log(res);
    }
  }

  @ApiExcludeEndpoint()
  @Get("test-async")
  async testAsync() {
    for (let i = 0; i < 10; i++) {
      console.log(`Getting exchange rate ${i + 1} out of 10 times...`);
      const res = await Promise.all(
        new Array(100).fill(0).map(() =>
          this.candataService.getExchangeRate({
            currencyCode: "USD",
            date: "2025-06-26"
          })
        )
      );
      console.log(res);
    }
  }
}
