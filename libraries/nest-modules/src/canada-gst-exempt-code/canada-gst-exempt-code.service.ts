import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { DataSource, In, Not, QueryRunner, Repository } from "typeorm";
import { CanadaGstExemptCode } from "../entities";
import {
  AuthenticatedRequest,
  CANADA_GST_EXEMPT_CODE_MODULE_OPTIONS,
  CANADA_GST_EXEMPT_CODE_REQUIRED_KEYS,
  CanadaGstExemptCodeColumn,
  FIND_CANADA_GST_EXEMPT_CODE_RELATIONS,
  MatchingRuleSourceDatabaseTable,
  UserPermission
} from "../types";
import { CrudOptions } from "../types";
import { REQUEST } from "@nestjs/core";
import {
  BatchUpdateCanadaGstExemptCodesDto,
  BatchUpdateCanadaGstExemptCodesResponseDto,
  CreateCanadaGstExemptCodeDto,
  EditCanadaGstExemptCodeDto,
  GetCanadaGstExemptCodesDto,
  GetCanadaGstExemptCodesResponseDto
} from "../dto";
import { convertFromCamelCase, getFindOptions } from "../helper-functions";
import { MatchingRuleService } from "../matching-rule";

@Injectable({ scope: Scope.REQUEST })
export class CanadaGstExemptCodeService {
  constructor(
    @InjectRepository(CanadaGstExemptCode)
    private readonly canadaGstExemptCodeRepository: Repository<CanadaGstExemptCode>,
    @Inject(MatchingRuleService)
    private readonly matchingRuleService: MatchingRuleService,
    @Inject(CANADA_GST_EXEMPT_CODE_MODULE_OPTIONS)
    private readonly options: CrudOptions,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}

  async getCanadaGstExemptCodes(
    getCanadaGstExemptCodesDto: GetCanadaGstExemptCodesDto
  ): Promise<GetCanadaGstExemptCodesResponseDto> {
    if (!this.options.readMany) throw new ForbiddenException("Get Canada GST Exempt Codes is disabled");
    const { where, order, skip, take } = getFindOptions(
      getCanadaGstExemptCodesDto,
      [],
      [],
      CanadaGstExemptCodeColumn.id
    );
    const [data, total] = await this.canadaGstExemptCodeRepository.findAndCount({
      where,
      order,
      relations: FIND_CANADA_GST_EXEMPT_CODE_RELATIONS,
      skip,
      take
    });
    return {
      data,
      total,
      skip,
      limit: take
    };
  }

  async getCanadaGstExemptCodeById(codeId: number, queryRunner?: QueryRunner) {
    if (!this.options.readOne) throw new ForbiddenException("Get Canada GST Exempt Code is disabled");
    const canadaGstExemptCodeRepository = queryRunner
      ? queryRunner.manager.getRepository(CanadaGstExemptCode)
      : this.canadaGstExemptCodeRepository;
    const canadaGstExemptCode = await canadaGstExemptCodeRepository.findOne({
      where: { id: codeId },
      relations: FIND_CANADA_GST_EXEMPT_CODE_RELATIONS
    });
    return canadaGstExemptCode;
  }

  async createCanadaGstExemptCode(createCanadaGstExemptCodeDto: CreateCanadaGstExemptCodeDto) {
    if (!this.options.create) throw new ForbiddenException("Create Canada GST Exempt Code is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to create Canada GST Exempt Codes");
    let newCanadaGstExemptCode = new CanadaGstExemptCode();
    newCanadaGstExemptCode.createdBy = this.request?.user || null;
    newCanadaGstExemptCode.lastEditedBy = this.request?.user || null;
    for (const [key, value] of Object.entries(createCanadaGstExemptCodeDto)) {
      if (value === undefined) continue;
      if (CANADA_GST_EXEMPT_CODE_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      newCanadaGstExemptCode[key] = value;
    }
    if (await this.canadaGstExemptCodeRepository.existsBy({ code: newCanadaGstExemptCode.code }))
      throw new BadRequestException("Canada GST Exempt Code with the same code already exists");

    newCanadaGstExemptCode = await this.canadaGstExemptCodeRepository.save(newCanadaGstExemptCode);

    return await this.getCanadaGstExemptCodeById(newCanadaGstExemptCode.id);
  }

  async editCanadaGstExemptCode(codeId: number, editCanadaGstExemptCodeDto: EditCanadaGstExemptCodeDto) {
    if (!this.options.update) throw new ForbiddenException("Edit Canada GST Exempt Code is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to edit Canada GST Exempt Codes");
    const canadaGstExemptCode = await this.getCanadaGstExemptCodeById(codeId);
    if (!canadaGstExemptCode) throw new NotFoundException("Canada GST Exempt Code not found");
    canadaGstExemptCode.lastEditedBy = this.request?.user || null;
    for (const [key, value] of Object.entries(editCanadaGstExemptCodeDto)) {
      if (value === undefined) continue;
      if (CANADA_GST_EXEMPT_CODE_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      canadaGstExemptCode[key] = value;
    }
    if (
      await this.canadaGstExemptCodeRepository.existsBy({
        id: Not(codeId),
        code: canadaGstExemptCode.code
      })
    )
      throw new BadRequestException("Canada GST Exempt Code with the same code already exists");
    await this.canadaGstExemptCodeRepository.save(canadaGstExemptCode);
    return await this.getCanadaGstExemptCodeById(codeId);
  }

  async deleteCanadaGstExemptCode(codeId: number) {
    if (!this.options.delete) throw new ForbiddenException("Delete Canada GST Exempt Code is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to delete Canada GST Exempt Codes");
    if (!(await this.canadaGstExemptCodeRepository.existsBy({ id: codeId })))
      throw new NotFoundException("Canada GST Exempt Code not found");
    if (
      await this.matchingRuleService.isMatchingRuleForSourceRecordExists(
        MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE,
        codeId
      )
    )
      throw new BadRequestException("Canada GST Exempt Code is being used in a matching rule");
    await this.canadaGstExemptCodeRepository.delete({ id: codeId });
    return;
  }

  async batchUpdateCanadaGstExemptCodes(
    batchUpdateCanadaGstExemptCodesDto: BatchUpdateCanadaGstExemptCodesDto
  ): Promise<BatchUpdateCanadaGstExemptCodesResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const savedCanadaGstExemptCodeIds: Array<number> = [];
    try {
      const { create: createList, edit: editList, delete: deleteList } = batchUpdateCanadaGstExemptCodesDto;

      if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
        throw new ForbiddenException("You are not authorized to batch update Canada GST Exempt Codes");
      if (Array.isArray(createList) && createList.length > 0 && !this.options.create)
        throw new ForbiddenException("Create Canada GST Exempt Code is disabled");
      if (Array.isArray(editList) && editList.length > 0 && !this.options.update)
        throw new ForbiddenException("Edit Canada GST Exempt Code is disabled");
      if (Array.isArray(deleteList) && deleteList.length > 0 && !this.options.delete)
        throw new ForbiddenException("Delete Canada GST Exempt Code is disabled");

      const canadaGstExemptCodeRepository = queryRunner.manager.getRepository(CanadaGstExemptCode);
      const toBeSavedCanadaGstExemptCodes: Array<CanadaGstExemptCode> = [];

      for (let i = 0; i < (createList || []).length; i++) {
        const errorMsgPrefix = `Error on create list index ${i}: `;
        const createCanadaGstExemptCodeDto = createList[i];
        let newCanadaGstExemptCode = new CanadaGstExemptCode();
        newCanadaGstExemptCode.createdBy = this.request?.user || null;
        newCanadaGstExemptCode.lastEditedBy = this.request?.user || null;
        for (const [key, value] of Object.entries(createCanadaGstExemptCodeDto)) {
          if (value === undefined) continue;
          if (CANADA_GST_EXEMPT_CODE_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
          newCanadaGstExemptCode[key] = value;
        }
        if (
          toBeSavedCanadaGstExemptCodes.some((c) => c.code === newCanadaGstExemptCode.code) ||
          (await canadaGstExemptCodeRepository.existsBy({
            code: newCanadaGstExemptCode.code
          }))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada GST Exempt Code with the same code already exists`
          );

        toBeSavedCanadaGstExemptCodes.push(newCanadaGstExemptCode);
      }

      for (let i = 0; i < (editList || []).length; i++) {
        const errorMsgPrefix = `Error on edit list index ${i}: `;
        const { id: codeId, ...editCanadaGstExemptCodeDto } = editList[i];
        const canadaGstExemptCode = await this.getCanadaGstExemptCodeById(codeId, queryRunner);
        if (!canadaGstExemptCode)
          throw new NotFoundException(`${errorMsgPrefix}Canada GST Exempt Code not found`);
        canadaGstExemptCode.lastEditedBy = this.request?.user || null;
        for (const [key, value] of Object.entries(editCanadaGstExemptCodeDto)) {
          if (value === undefined) continue;
          if (CANADA_GST_EXEMPT_CODE_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
          canadaGstExemptCode[key] = value;
        }
        if (
          toBeSavedCanadaGstExemptCodes.some(
            (c) => c.code === canadaGstExemptCode.code && c.id !== canadaGstExemptCode.id
          ) ||
          (await canadaGstExemptCodeRepository.existsBy({
            id: Not(
              In(
                toBeSavedCanadaGstExemptCodes
                  .filter((c) => typeof c.id === "number")
                  .map((c) => c.id)
                  .concat([codeId])
              )
            ),
            code: canadaGstExemptCode.code
          }))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada GST Exempt Code with the same code already exists`
          );
        toBeSavedCanadaGstExemptCodes.push(canadaGstExemptCode);
      }

      for (let i = 0; i < (deleteList || []).length; i++) {
        const codeId = deleteList[i];
        const errorMsgPrefix = `Error on delete list index ${i}: `;
        if (!(await canadaGstExemptCodeRepository.existsBy({ id: codeId })))
          throw new NotFoundException(`${errorMsgPrefix}Canada GST Exempt Code not found`);
        if (toBeSavedCanadaGstExemptCodes.some((c) => c.id === codeId))
          throw new BadRequestException(`${errorMsgPrefix}Canada GST Exempt Code is being edited`);
        if (
          await this.matchingRuleService.isMatchingRuleForSourceRecordExists(
            MatchingRuleSourceDatabaseTable.CANADA_GST_EXEMPT_CODE,
            codeId,
            queryRunner
          )
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada GST Exempt Code is being used in a matching rule`
          );
      }

      savedCanadaGstExemptCodeIds.push(
        ...(await canadaGstExemptCodeRepository.save(toBeSavedCanadaGstExemptCodes)).map((c) => c.id)
      );
      if (Array.isArray(deleteList) && deleteList.length > 0)
        await canadaGstExemptCodeRepository.delete({ id: In(deleteList) });

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner.isReleased) queryRunner.release();
    }

    return {
      data: await this.canadaGstExemptCodeRepository.find({
        where: { id: In(savedCanadaGstExemptCodeIds) },
        relations: FIND_CANADA_GST_EXEMPT_CODE_RELATIONS
      })
    };
  }
}
