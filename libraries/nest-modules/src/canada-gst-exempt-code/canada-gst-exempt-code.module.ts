import { DynamicModule, Module } from "@nestjs/common";
import { CANADA_GST_EXEMPT_CODE_MODULE_OPTIONS, CrudOptions } from "../types";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CanadaGstExemptCodeController } from "./canada-gst-exempt-code.controller";
import { CanadaGstExemptCode } from "../entities";
import { CanadaGstExemptCodeService } from "./canada-gst-exempt-code.service";

@Module({})
export class CanadaGstExemptCodeModule {
  static register(options: CrudOptions): DynamicModule {
    return {
      global: true,
      module: CanadaGstExemptCodeModule,
      imports: [TypeOrmModule.forFeature([CanadaGstExemptCode])],
      controllers: [CanadaGstExemptCodeController],
      providers: [
        {
          provide: CANADA_GST_EXEMPT_CODE_MODULE_OPTIONS,
          useValue: options
        },
        CanadaGstExemptCodeService
      ],
      exports: [CanadaGstExemptCodeService]
    };
  }
}
