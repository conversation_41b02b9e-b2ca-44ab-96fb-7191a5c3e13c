import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseGuards,
  NotFoundException,
  Post,
  Body,
  Put,
  Delete,
  HttpCode
} from "@nestjs/common";
import { AccessTokenGuard } from "../guards";
import {
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses
} from "../decorators";
import { ApiForbiddenResponse, ApiOperation, ApiParam } from "@nestjs/swagger";
import { ApiTags } from "@nestjs/swagger";
import {
  BatchUpdateCanadaGstExemptCodesDto,
  BatchUpdateCanadaGstExemptCodesResponseDto,
  CreateCanadaGstExemptCodeDto,
  EditCanadaGstExemptCodeDto,
  ForbiddenResponseDto,
  GetCanadaGstExemptCodesDto,
  GetCanadaGstExemptCodesResponseDto
} from "../dto";
import { CanadaGstExemptCodeService } from "./canada-gst-exempt-code.service";
import { CanadaGstExemptCode } from "../entities";

@ApiTags("Canada GST Exempt Code API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("canada-gst-exempt-codes")
export class CanadaGstExemptCodeController {
  constructor(private readonly canadaGstExemptCodeService: CanadaGstExemptCodeService) {}

  @Get()
  @ApiOperation({ summary: "Get Canada GST Exempt Codes" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: GetCanadaGstExemptCodesResponseDto })
  async getCanadaGstExemptCodes(@Query() getCanadaGstExemptCodesDto: GetCanadaGstExemptCodesDto) {
    return await this.canadaGstExemptCodeService.getCanadaGstExemptCodes(getCanadaGstExemptCodesDto);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get Canada GST Exempt Code by ID" })
  @ApiParam({ name: "id", type: "integer", description: "Canada GST Exempt Code ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: CanadaGstExemptCode })
  async getCanadaGstExemptCodeById(@Param("id", ParseIntPipe) id: number) {
    const code = await this.canadaGstExemptCodeService.getCanadaGstExemptCodeById(id);
    if (!code) throw new NotFoundException("Canada GST Exempt Code not found");
    return code;
  }

  @Post()
  @ApiOperation({ summary: "Create Canada GST Exempt Code" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiCreateResponses({ type: CanadaGstExemptCode })
  async createCanadaGstExemptCode(@Body() createCanadaGstExemptCodeDto: CreateCanadaGstExemptCodeDto) {
    return await this.canadaGstExemptCodeService.createCanadaGstExemptCode(createCanadaGstExemptCodeDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update Canada GST Exempt Code" })
  @ApiParam({ name: "id", type: "integer", description: "Canada GST Exempt Code ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: CanadaGstExemptCode })
  async editCanadaGstExemptCode(
    @Param("id", ParseIntPipe) id: number,
    @Body() editCanadaGstExemptCodeDto: EditCanadaGstExemptCodeDto
  ) {
    return await this.canadaGstExemptCodeService.editCanadaGstExemptCode(id, editCanadaGstExemptCodeDto);
  }

  @HttpCode(204)
  @Delete(":id")
  @ApiOperation({ summary: "Delete Canada GST Exempt Code" })
  @ApiParam({ name: "id", type: "integer", description: "Canada GST Exempt Code ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiDeleteResponses()
  async deleteCanadaGstExemptCode(@Param("id", ParseIntPipe) id: number) {
    await this.canadaGstExemptCodeService.deleteCanadaGstExemptCode(id);
    return;
  }

  @HttpCode(200)
  @Post("batch")
  @ApiOperation({ summary: "Batch update Canada GST Exempt Codes" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: BatchUpdateCanadaGstExemptCodesResponseDto })
  async batchUpdateCanadaGstExemptCodes(
    @Body() batchUpdateCanadaGstExemptCodesDto: BatchUpdateCanadaGstExemptCodesDto
  ) {
    return await this.canadaGstExemptCodeService.batchUpdateCanadaGstExemptCodes(
      batchUpdateCanadaGstExemptCodesDto
    );
  }
}
