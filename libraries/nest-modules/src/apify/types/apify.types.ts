import { CarrierAbbreviation } from "./carrier.types";

export type ApifyActorNameAndId = {
  /** Name of the Apify actor */
  name: string;
  /** ID of the Apify actor */
  id: string;
};

export enum ActorRunStatus {
  READY = "READY",
  RUNNING = "RUNNING",
  SUCCEEDED = "SUCCEEDED",
  FAILED = "FAILED",
  TIMING_OUT = "TIMING-OUT",
  TIMED_OUT = "TIMED-OUT",
  ABORTING = "ABORTING",
  ABORTED = "ABORTED"
}

export const INITIAL_RUN_STATUSES = [ActorRunStatus.READY];
export const TRANSITIONAL_RUN_STATUSES = [
  ActorRunStatus.RUNNING,
  ActorRunStatus.TIMING_OUT,
  ActorRunStatus.ABORTING
];
export const TERMINAL_RUN_STATUSES = [
  ActorRunStatus.SUCCEEDED,
  ActorRunStatus.FAILED,
  ActorRunStatus.TIMED_OUT,
  ActorRunStatus.ABORTED
];

export enum ApifyActorRunOrigin {
  DEVELOPMENT = "DEVELOPMENT",
  WEB = "WEB",
  API = "API",
  SCHEDULER = "SCHEDULER",
  TEST = "TEST",
  WEBHOOK = "WEBHOOK",
  ACTOR = "ACTOR",
  CLI = "CLI",
  STANDBY = "STANDBY"
}

// Time (in millliseconds) between each request to get the Apify actor run status
export const APIFY_GET_RUN_STATUS_INTERVAL_MS = 30000;
// Maximum number of attempts to get the Apify actor run status
export const APIFY_GET_RUN_STATUS_MAX_ATTEMPTS = 5;

export const APIFY_RESULT_STORE_NAMES = {
  CARRIER: "antek~Container-Tracking",
  PORT: "antek~claro-port-tracking",
  RAIL: "antek~claro-railway-tracking",
  PICKUP_RETURN: "antek~Container-Return"
};

export const APIFY_CONTAINER_STATUS_CHECK_ACTOR_NAMES_AND_IDS: Record<string, ApifyActorNameAndId> = {
  [CarrierAbbreviation.EGL]: {
    name: "antek~container-status-check-egl",
    id: "jl2dLAxR9B2zIkF9f"
  },
  [CarrierAbbreviation.COSCO]: {
    name: "antek~container-status-check-cosco",
    id: "Rak5O0nc19oKLKri2"
  },
  [CarrierAbbreviation.MSC]: {
    name: "antek~container-status-check-msc",
    id: "1Nw2lZsYK30WPWKAa"
  },
  [CarrierAbbreviation.ONE]: {
    name: "antek~container-status-check-one",
    id: "gtiAnFxHLGU9DzFkK"
  },
  [CarrierAbbreviation.CMA]: {
    name: "antek~container-status-check-cma",
    id: "FDNNKJlTBuf8wLNYA"
  },
  [CarrierAbbreviation.SML]: {
    name: "antek~container-status-check-sml",
    id: "0pFxi5lpqGQXUJ5EE"
  },
  [CarrierAbbreviation.MAERSK]: {
    name: "antek~container-status-check-maersk",
    id: "wgn4EuhSFD8Uyr0yJ"
  }
};
export interface ApifyContainerStatusCheckResult {
  container: string;
  isDischarged?: boolean;
  eta?: string;
  mark?: string;
}

export const APIFY_PORT_ACTOR_NAMES_AND_IDS: Record<string, ApifyActorNameAndId> = {
  CENTERM: {
    name: "antek~vancouver-centerm-port-check",
    id: "2aQIHVnS65NquNe0t"
  },
  FRASER: {
    name: "antek~vancouver-fraser-port-check",
    id: "o6JT6fXs0WLH3IdKB"
  },
  GCT: {
    name: "antek~vancouver-gct-port-check",
    id: "0sb6x6fQUVDbYRd1D"
  }
};
export interface ApifyPortCheckResult {
  containerNumber: string;
  category?: string | null;
  lastFreeDay?: string | null;
  mark?: string | null;
}

export const APIFY_RAILWAY_ACTOR_NAMES_AND_IDS: Record<string, ApifyActorNameAndId> = {
  CP: {
    name: "antek~cp-container-status-check",
    id: "vKbhXgNEQFKYUBQ8h"
  },
  CN: {
    name: "antek~cn-container-status-check",
    id: "rnqR6bRNhWlpdJ4iP"
  }
};
export interface APifyRailwayCheckResult {
  containerNumber: string;
  eta?: string | null;
  lastFreeDay?: string | null;
  mark?: string | null;
}

export const APIFY_PICKUP_RETURN_ACTOR_NAMES_AND_IDS: Record<string, ApifyActorNameAndId> = {
  [CarrierAbbreviation.EGL]: {
    name: "antek~container-return-egl",
    id: "vgeF3BLC0WyKDFVuP"
  },
  [CarrierAbbreviation.COSCO]: {
    name: "antek~container-return-cosco",
    id: "z3DkwfkLM0jz3202i"
  },
  [CarrierAbbreviation.MSC]: {
    name: "antek~container-return-msc",
    id: "yo6U5TSfv7N4LACKo"
  },
  [CarrierAbbreviation.ONE]: {
    name: "antek~container-return-one",
    id: "Et2YS21ejxoKLC85y"
  },
  [CarrierAbbreviation.CMA]: {
    name: "antek~container-return-cma",
    id: "XuU3eNEFb1gHAja1g"
  },
  [CarrierAbbreviation.SML]: {
    name: "antek~container-return-sml",
    id: "Wslg97JhDKSkw6uoy"
  },
  [CarrierAbbreviation.MAERSK]: {
    name: "antek~container-return-maersk",
    id: "T1iMRgy0Zl2OaLQ22"
  }
};
export interface ApifyPickupReturnCheckResult {
  container: string;
  pickupTime?: string | null;
  returnTime?: string | null;
  mark?: string | null;
}
