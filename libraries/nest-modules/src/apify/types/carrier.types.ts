export enum CarrierAbbreviation {
  EGL = "EGL",
  MSC = "MSC",
  OOCL = "OOCL",
  ZIM = "ZIM",
  MAERSK = "Maersk",
  HMM = "HMM",
  COSCO = "COSCO",
  HPL = "HPL",
  CMA = "CMA CGM",
  YML = "YML",
  ONE = "ONE",
  SML = "SML"
}

export const CARRIER_MAPPINGS: Record<
  CarrierAbbreviation,
  { name: string; eManifestCode: string; prefixes: Array<string> }
> = {
  [CarrierAbbreviation.EGL]: {
    name: "Evergreen Line",
    eManifestCode: "9476",
    prefixes: ["EGHU", "EGSU", "EISU", "EITU", "EMCU", "HMCU", "IMTU", "LTIU", "UGMU"]
  },
  [CarrierAbbreviation.MSC]: {
    name: "Mediterranean Shipping Company",
    eManifestCode: "9066",
    prefixes: [
      "GTIU",
      "MADU",
      "MEDU",
      "MSBU",
      "MS<PERSON>",
      "MSDU",
      "MSGU",
      "MSMU",
      "MSNU",
      "MSPU",
      "MSRU",
      "MSTU",
      "MSVU",
      "MSYU",
      "MSZU"
    ]
  },
  [CarrierAbbreviation.OOCL]: {
    name: "Orient Overseas Container Line Ltd.",
    eManifestCode: "9082",
    prefixes: ["OOCU", "OOLU"]
  },
  [CarrierAbbreviation.ZIM]: {
    name: "ZIM Integrated Shipping Services Ltd",
    eManifestCode: "9312",
    prefixes: ["ZCLU", "ZCSU", "ZIMU", "ZMOU", "ZWFU"]
  },
  [CarrierAbbreviation.MAERSK]: {
    name: "Maersk Line",
    eManifestCode: "9381",
    prefixes: [
      "APMU",
      "CADU",
      "CNIU",
      "COZU",
      "ENAU",
      "FAAU",
      "FRLU",
      "GRIU",
      "HASU",
      "KHJU",
      "KHLU",
      "KNLU",
      "LOTU",
      "MAEU",
      "MALU",
      "MCAU",
      "MCHU",
      "MCRU",
      "MHHU",
      "MIEU",
      "MMAU",
      "MNBU",
      "MRFU",
      "MRKU",
      "MRSU",
      "MSAU",
      "MSFU",
      "MSKU",
      "MSWU",
      "MVIU",
      "MWCU",
      "MWMU",
      "OCLU",
      "POCU",
      "PONU",
      "SCMU",
      "SEAU",
      "SUDU",
      "TORU",
      "MCIU"
    ]
  },
  [CarrierAbbreviation.HMM]: {
    name: "Hyundai Merchant Marine Co., Ltd.",
    eManifestCode: "9463",
    prefixes: ["HDMU", "HMMU", "GHMU", "HDGU", "HMRU"]
  },
  [CarrierAbbreviation.COSCO]: {
    name: "COSCO Container Lines",
    eManifestCode: "9502",
    prefixes: ["CBHU", "CCLU", "CSLU", "CSNU"]
  },
  [CarrierAbbreviation.HPL]: {
    name: "Hapag Lloyd Container Line",
    eManifestCode: "9529",
    prefixes: [
      "CASU",
      "CMUU",
      "CPSU",
      "CSQU",
      "CSVU",
      "DAYU",
      "FANU",
      "HAMU",
      "HLBU",
      "HLCU",
      "HLXU",
      "ITAU",
      "IVLU",
      "LNXU",
      "MOMU",
      "NDSU",
      "NIDU",
      "QIBU",
      "QNNU",
      "TLEU",
      "TMMU",
      "UACU",
      "UAEU",
      "UASU"
    ]
  },
  [CarrierAbbreviation.CMA]: {
    name: "Compagnie Maritime d Affretement Compagnie Generale Maritime",
    eManifestCode: "9558",
    prefixes: [
      "AMCU",
      "ANNU",
      "APHU",
      "APLU",
      "APRU",
      "APZU",
      "CGMU",
      "CMAU",
      "CMNU",
      "CNCU",
      "CSFU",
      "CSOU",
      "CZLU",
      "DVRU",
      "ECMU",
      "KLCU",
      "MMCU",
      "NOLU",
      "NOSU",
      "OPDU",
      "OTAU",
      "SMUU",
      "SOFU",
      "STMU"
    ]
  },
  [CarrierAbbreviation.YML]: {
    name: "Yang Ming Line",
    eManifestCode: "90K6",
    prefixes: ["YMLU", "YMLU"]
  },
  [CarrierAbbreviation.ONE]: {
    name: "Ocean Network Express",
    eManifestCode: "919J",
    prefixes: [
      "AKLU",
      "EKLU",
      "ESSU",
      "KKFU",
      "KKLU",
      "KKTU",
      "KLFU",
      "KLTU",
      "KXTU",
      "MOAU",
      "MOEU",
      "MOFU",
      "MOGU",
      "MORU",
      "MOSU",
      "MOTU",
      "NYKU",
      "ONEU",
      "PXCU"
    ]
  },
  [CarrierAbbreviation.SML]: {
    name: "Seaboard Marine",
    eManifestCode: "918P",
    prefixes: ["SMCU", "SMLU"]
  }
};
