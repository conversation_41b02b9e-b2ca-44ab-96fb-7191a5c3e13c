import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  ValidateNested
} from "class-validator";
import { ActorRunStatus, ApifyActorRunOrigin } from "../types/apify.types";

export class ApifyActorRunMetadataDto {
  @ApiProperty({
    type: "enum",
    enum: ApifyActorRunOrigin,
    example: ApifyActorRunOrigin.DEVELOPMENT
  })
  @IsEnum(ApifyActorRunOrigin)
  @IsNotEmpty()
  origin: ApifyActorRunOrigin;
}

export class ApifyActorRunStatisticsDto {
  @ApiProperty({ type: "number", example: 240 })
  @IsNumber()
  @IsNotEmpty()
  inputBodyLen: number;

  @ApiProperty({ type: "number", example: 0 })
  @IsNumber()
  @IsNotEmpty()
  restartCount: number;

  @ApiProperty({ type: "number", example: 2 })
  @IsNumber()
  @IsNotEmpty()
  resurrectCount: number;

  @ApiPropertyOptional({ type: "number", example: 267874071.9 })
  @IsNumber()
  @IsOptional()
  memAvgBytes?: number;

  @ApiPropertyOptional({ type: "number", example: 404713472 })
  @IsNumber()
  @IsOptional()
  memMaxBytes?: number;

  @ApiPropertyOptional({ type: "number", example: 0 })
  @IsNumber()
  @IsOptional()
  memCurrentBytes?: number;

  @ApiPropertyOptional({ type: "number", example: 33.7532101107538 })
  @IsNumber()
  @IsOptional()
  cpuAvgUsage?: number;

  @ApiPropertyOptional({ type: "number", example: 169.650735534941 })
  @IsNumber()
  @IsOptional()
  cpuMaxUsage?: number;

  @ApiPropertyOptional({ type: "number", example: 0 })
  @IsNumber()
  @IsOptional()
  cpuCurrentUsage?: number;

  @ApiPropertyOptional({ type: "number", example: 103508042 })
  @IsNumber()
  @IsOptional()
  netRxBytes?: number;

  @ApiPropertyOptional({ type: "number", example: 4854600 })
  @IsNumber()
  @IsOptional()
  netTxBytes?: number;

  @ApiPropertyOptional({ type: "number", example: 248472 })
  @IsNumber()
  @IsOptional()
  durationMillis?: number;

  @ApiPropertyOptional({ type: "number", example: 248.472 })
  @IsNumber()
  @IsOptional()
  runTimeSecs?: number;

  @ApiPropertyOptional({ type: "number", example: 0 })
  @IsNumber()
  @IsOptional()
  metamorph?: number;

  @ApiProperty({ type: "number", example: 0.13804 })
  @IsNumber()
  @IsNotEmpty()
  computeUnits: number;
}

export class ApifyActorRunOptionsDto {
  @ApiProperty({ type: "string", example: "latest" })
  @IsString()
  @IsNotEmpty()
  build: string;

  @ApiProperty({ type: "number", example: 300 })
  @IsNumber()
  @IsNotEmpty()
  timeoutSecs: number;

  @ApiProperty({ type: "number", example: 1024 })
  @IsNumber()
  @IsNotEmpty()
  memoryMbytes: number;

  @ApiProperty({ type: "number", example: 2048 })
  @IsNumber()
  @IsNotEmpty()
  diskMbytes: number;
}

export class ApifyActorRunDataDto {
  @ApiProperty({ type: "string", example: "HG7ML7M8z78YcAPEB" })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ type: "string", example: "HDSasDasz78YcAPEB" })
  @IsString()
  @IsNotEmpty()
  actId: string;

  @ApiProperty({ type: "string", example: "7sT5jcggjjA9fNcxF" })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiPropertyOptional({ type: "string", example: "KJHSKHausidyaJKHs" })
  @IsString()
  @IsOptional()
  actorTaskId?: string | null;

  @ApiProperty({ type: "string", format: "date-time", example: "2019-11-30T07:34:24.202Z" })
  @IsDateString()
  @IsNotEmpty()
  startedAt: string;

  @ApiProperty({ type: "string", format: "date-time", example: "2019-12-12T09:30:12.202Z" })
  @IsDateString()
  @IsNotEmpty()
  finishedAt: string;

  @ApiProperty({ type: "enum", enum: ActorRunStatus, example: ActorRunStatus.RUNNING })
  @IsEnum(ActorRunStatus)
  @IsNotEmpty()
  status: ActorRunStatus;

  @ApiPropertyOptional({ type: "string", example: "Actor is running" })
  @IsString()
  @IsOptional()
  statusMessage?: string | null;

  @ApiPropertyOptional({ type: "boolean", example: false })
  @IsBoolean()
  @IsOptional()
  isStatusMessageTerminal?: boolean | null;

  @ApiProperty({ type: () => ApifyActorRunMetadataDto })
  @Type(() => ApifyActorRunMetadataDto)
  @ValidateNested()
  @IsNotEmpty()
  meta: ApifyActorRunMetadataDto;

  @ApiProperty({ type: () => ApifyActorRunStatisticsDto })
  @Type(() => ApifyActorRunStatisticsDto)
  @ValidateNested()
  @IsNotEmpty()
  stats: ApifyActorRunStatisticsDto;

  @ApiProperty({ type: () => ApifyActorRunOptionsDto })
  @Type(() => ApifyActorRunOptionsDto)
  @ValidateNested()
  @IsNotEmpty()
  options: ApifyActorRunOptionsDto;

  @ApiProperty({ type: "string", example: "7sT5jcggjjA9fNcxF" })
  @IsString()
  @IsNotEmpty()
  buildId: string;

  @ApiPropertyOptional({ type: "number", example: 0 })
  @IsNumber()
  @IsOptional()
  exitCode?: number | null;

  @ApiProperty({ type: "string", example: "eJNzqsbPiopwJcgGQ" })
  @IsString()
  @IsNotEmpty()
  defaultKeyValueStoreId: string;

  @ApiProperty({ type: "string", example: "wmKPijuyDnPZAPRMk" })
  @IsString()
  @IsNotEmpty()
  defaultDatasetId: string;

  @ApiProperty({ type: "string", example: "FL35cSF7jrxr3BY39" })
  @IsString()
  @IsNotEmpty()
  defaultRequestQueueId: string;

  @ApiProperty({ type: "string", example: "0.0.36" })
  @IsString()
  @IsNotEmpty()
  buildNumber: string;

  @ApiProperty({ type: "string", format: "uri", example: "https://g8kd8kbc5ge8.runs.apify.net" })
  @IsUrl()
  @IsNotEmpty()
  containerUrl: string;

  @ApiPropertyOptional({ type: "boolean", example: true })
  @IsBoolean()
  @IsOptional()
  isContainerServerReady?: boolean | null;

  @ApiPropertyOptional({ type: "string", example: "master" })
  @IsString()
  @IsOptional()
  gitBranchName?: string | null;

  @ApiPropertyOptional({ type: "object" })
  @IsObject()
  @IsOptional()
  usage?: Record<string, any> | null;

  @ApiPropertyOptional({ type: "number", example: 0.2654 })
  @IsNumber()
  @IsOptional()
  usageTotalUsd?: number | null;

  @ApiPropertyOptional({ type: "object" })
  @IsObject()
  @IsOptional()
  usageUsd?: Record<string, any> | null;
}

export class ApifyActorRunDto {
  @ApiProperty({ type: () => ApifyActorRunDataDto })
  @Type(() => ApifyActorRunDataDto)
  @ValidateNested()
  @IsNotEmpty()
  data: ApifyActorRunDataDto;
}

export class ApifyGetRunDto {
  @ApiPropertyOptional({
    type: "number",
    format: "double",
    description:
      "The maximum number of seconds the server waits for the run to finish. By default, it is `0`, the maximum value is `60`. If the build finishes in time then the returned run object will have a terminal status (e.g. `SUCCEEDED`), otherwise it will have a transitional status (e.g. `RUNNING`)."
  })
  @IsNumber()
  @IsOptional()
  waitForFinish?: number;
}

export class ApifyRunActorDto extends ApifyGetRunDto {
  @ApiPropertyOptional({
    type: "number",
    format: "double",
    description:
      "Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor."
  })
  @IsNumber()
  @IsOptional()
  timeout?: number;

  @ApiPropertyOptional({
    type: "number",
    format: "double",
    description:
      "Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor."
  })
  @IsNumber()
  @IsOptional()
  memory?: number;

  @ApiPropertyOptional({
    type: "number",
    format: "double",
    description:
      "The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the `ACTOR_MAX_PAID_DATASET_ITEMS` environment variable."
  })
  @IsNumber()
  @IsOptional()
  maxItems?: number;

  @ApiPropertyOptional({
    type: "string",
    description:
      "Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically `latest`)."
  })
  @IsString()
  @IsOptional()
  build?: string;

  @ApiPropertyOptional({
    type: "string",
    description:
      "Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks."
  })
  @IsString()
  @IsOptional()
  webhooks?: string;
}
