import { DynamicModule, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CanadaTariff, TariffSyncHistory } from "../entities";
import { CANADA_TARIFF_MODULE_OPTIONS, CanadaTariffModuleOptions } from "../types";
import { CanadaTariffController } from "./canada-tariff.controller";
import { CanadaTariffService } from "./canada-tariff.service";

@Module({})
export class CanadaTariffModule {
  static register(options: CanadaTariffModuleOptions): DynamicModule {
    return {
      module: CanadaTariffModule,
      global: true,
      imports: [TypeOrmModule.forFeature([CanadaTariff, TariffSyncHistory])],
      controllers: [CanadaTariffController],
      providers: [
        {
          provide: CANADA_TARIFF_MODULE_OPTIONS,
          useValue: options
        },
        CanadaTariffService
      ],
      exports: [CanadaTariffService]
    };
  }
}
