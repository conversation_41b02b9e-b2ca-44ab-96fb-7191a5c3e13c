import {
  ConflictException,
  ForbiddenException,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { EventEmitter2, OnEvent } from "@nestjs/event-emitter";
import { InjectRepository } from "@nestjs/typeorm";
import AdmZip from "adm-zip";
import axios from "axios";
import * as cheerio from "cheerio";
import moment from "moment-timezone";
import {
  And,
  DataSource,
  Equal,
  FindOperator,
  In,
  LessThanOrEqual,
  Like,
  MoreThan,
  Not,
  QueryRunner,
  Raw,
  Repository
} from "typeorm";
import { CarmService } from "../carm";
import { GetCanadaTariffsDto, GetCanadaTariffsResponseDto, SyncCanadaTariffsDto } from "../dto";
import { CanadaTariff, TariffSyncHistory, User } from "../entities";
import { getFindOptions } from "../helper-functions";
import {
  AuthenticatedRequest,
  CANADA_TARIFF_ENUM_KEYS,
  CANADA_TARIFF_MODULE_OPTIONS,
  CANADA_TARIFF_SYNC_EVENT,
  CanadaTariffColumn,
  CanadaTariffModuleOptions,
  CBSATariffFileRow,
  FIND_CANADA_TARIFF_RELATIONS,
  SyncStatus,
  TariffTreeNode,
  UnitOfMeasure
} from "../types";

@Injectable({ scope: Scope.REQUEST })
export class CanadaTariffService {
  constructor(
    @InjectRepository(CanadaTariff)
    private readonly canadaTariffRepository: Repository<CanadaTariff>,
    @InjectRepository(TariffSyncHistory)
    private readonly tariffSyncHistoryRepository: Repository<TariffSyncHistory>,
    @Inject(CarmService)
    private readonly carmService: CarmService,
    @Inject(CANADA_TARIFF_MODULE_OPTIONS)
    private readonly canadaTariffModuleOptions: CanadaTariffModuleOptions,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource,
    private readonly eventEmitter: EventEmitter2
  ) {}
  private readonly logger = new Logger(CanadaTariffService.name);

  static formatHsCode(hsCode: string) {
    const hsCodeFirstPart = hsCode.substring(0, 4);
    const hsCodeSecondPart = hsCode.substring(4, 6);
    const hsCodeThirdPart = hsCode.substring(6, 8);
    const hsCodeFourthPart = hsCode.substring(8, 10);
    return [hsCodeFirstPart, hsCodeSecondPart, hsCodeThirdPart, hsCodeFourthPart]
      .filter((s) => s.length > 0)
      .join(".");
  }

  async getCanadaTariffs(getCanadaTariffsDto: GetCanadaTariffsDto): Promise<GetCanadaTariffsResponseDto> {
    if (!this.canadaTariffModuleOptions.readMany)
      throw new ForbiddenException("Get canada tariffs is disabled");

    const { treatments, type: tariffType, ...getManyDto } = getCanadaTariffsDto;
    const { where, order, skip, take } = getFindOptions<CanadaTariff>(
      getManyDto,
      CANADA_TARIFF_ENUM_KEYS,
      [],
      CanadaTariffColumn.id
    );
    if (Array.isArray(treatments) && treatments.length > 0)
      where.treatments = Raw((alias) => `${alias} ?& array[:...treatments]`, {
        treatments
      });
    if (tariffType) {
      const hsCodeLengthCondition = Raw((alias) => `LENGTH(${alias}) = ${tariffType}`);
      where.hsCode = where.hsCode
        ? And(where.hsCode as FindOperator<string>, hsCodeLengthCondition)
        : hsCodeLengthCondition;
    }

    const [data, total] = await this.canadaTariffRepository.findAndCount({
      where,
      relations: FIND_CANADA_TARIFF_RELATIONS,
      order,
      skip,
      take
    });
    return {
      data,
      skip,
      limit: take,
      total
    };
  }

  async getCanadaTariffById(tariffId: number, queryRunner?: QueryRunner) {
    if (!this.canadaTariffModuleOptions.readOne)
      throw new ForbiddenException("Get Canada Tariff is disabled");
    const canadaTariffRepository = queryRunner
      ? queryRunner.manager.getRepository(CanadaTariff)
      : this.canadaTariffRepository;
    return await canadaTariffRepository.findOne({
      where: { id: tariffId },
      relations: FIND_CANADA_TARIFF_RELATIONS
    });
  }

  async getCanadaTariffByHsCode(hsCode: string, fullCodesOnly = false, queryRunner?: QueryRunner) {
    if (!this.canadaTariffModuleOptions.readOne)
      throw new ForbiddenException("Get Canada Tariff is disabled");

    const canadaTariffRepository = queryRunner
      ? queryRunner.manager.getRepository(CanadaTariff)
      : this.canadaTariffRepository;
    return await canadaTariffRepository.findOne({
      where: {
        hsCode: And(
          Equal(hsCode),
          Raw((alias) => `LENGTH(${alias}) ${fullCodesOnly ? "= 10" : "> 0"}`)
        )
      },
      relations: FIND_CANADA_TARIFF_RELATIONS
    });
  }

  async createCanadaTariffSyncEvent(syncCanadaTariffsDto: SyncCanadaTariffsDto) {
    if (!this.canadaTariffModuleOptions.sync)
      throw new ForbiddenException("Sync Candata Tariffs is disabled");
    if (
      await this.tariffSyncHistoryRepository.existsBy({
        status: SyncStatus.RUNNING,
        syncDate: MoreThan(new Date(Date.now() - 60 * 60 * 1000))
      })
    )
      throw new ConflictException("Another sync is already running");
    const newSyncHistory = await this.tariffSyncHistoryRepository.save(new TariffSyncHistory());
    this.eventEmitter.emit(CANADA_TARIFF_SYNC_EVENT, newSyncHistory, syncCanadaTariffsDto);
    return newSyncHistory;
  }

  async getCarmTariffList(lastUpdateDate: Date) {
    const MAX_ENTRY_COUNT_PER_BATCH = 250;
    const carmTariffList: Array<{
      hsCode: string;
      effectiveDate: Date;
      expiryDate: Date;
      description: string;
      uom: UnitOfMeasure | null;
    }> = [];
    this.logger.log(`Getting CARM tariff list...`);
    const filter = lastUpdateDate
      ? `UpdateOn ge datetime'${moment.tz(lastUpdateDate, "America/Toronto").format("YYYY-MM-DDTHH:mm:ss")}'`
      : null;
    let isLastPage = false;
    do {
      try {
        this.logger.log(
          `Top: ${MAX_ENTRY_COUNT_PER_BATCH}, Skip: ${carmTariffList.length}, filter: ${filter}`
        );
        const {
          feed: { entry }
        } = await this.carmService.getListOfTariffNumbers({
          $top: MAX_ENTRY_COUNT_PER_BATCH,
          $skip: carmTariffList.length,
          $filter: filter
        });
        const batchTariffList = entry.map(({ content: { properties } }) => ({
          hsCode: properties.TariffNumber.replaceAll(".", ""),
          effectiveDate: CarmService.toCarmTariffDate(properties.TariffNumberValidStartDate),
          expiryDate: CarmService.toCarmTariffDate(properties.TariffNumberValidEndDate),
          description: properties.Description,
          uom:
            typeof properties.UnitOfMeasureCode === "string" &&
            Object.values(UnitOfMeasure).includes(properties.UnitOfMeasureCode.toLowerCase() as UnitOfMeasure)
              ? (properties.UnitOfMeasureCode.toLowerCase() as UnitOfMeasure)
              : null
        }));
        carmTariffList.push(...batchTariffList);
        this.logger.log(
          `-- Current Batch Count: ${batchTariffList.length}, Total Count: ${carmTariffList.length}`
        );
      } catch (error: any) {
        this.logger.warn(`Error while getting CARM tariff list: ${error.message}`);
        if (error instanceof NotFoundException) {
          this.logger.warn(`No more tariff records found with prefix, stop getting CARM tariff list...`);
          isLastPage = true;
        } else {
          this.logger.error(`Unknown error, stop getting CARM tariff list entirely...`);
          throw error;
        }
      }
    } while (!isLastPage);

    // TODO: Get tariff treatments from CARM API

    return carmTariffList;
  }

  @OnEvent(CANADA_TARIFF_SYNC_EVENT)
  async syncCanadaTariffFromCarm(
    tariffSyncHistory: TariffSyncHistory,
    syncCanadaTariffsDto: SyncCanadaTariffsDto
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      if (syncCanadaTariffsDto.fullSync) this.logger.warn(`Full sync is enabled`);

      this.logger.log(`Getting last successful sync history...`);
      const lastSuccessfulSyncHistory = await queryRunner.manager.findOneBy(TariffSyncHistory, {
        status: SyncStatus.SUCCESS
      });
      this.logger.log(
        `Last successful sync history: ${lastSuccessfulSyncHistory?.id}, sync date: ${lastSuccessfulSyncHistory?.syncDate}`
      );
      const carmTariffList = await this.getCarmTariffList(
        (!syncCanadaTariffsDto.fullSync && lastSuccessfulSyncHistory?.syncDate) || null
      );
      this.logger.log(`CARM Tariff List Count: ${carmTariffList.length}`);

      this.logger.log(`Mapping CARM tariff list by first 4 digits...`);
      const carmTariffMap = new Map<
        string,
        Array<{
          hsCode: string;
          effectiveDate: Date;
          expiryDate: Date;
          description: string;
          uom: UnitOfMeasure | null;
        }>
      >();
      for (const carmTariff of carmTariffList) {
        const hsCodePrefix = carmTariff.hsCode.substring(0, 4);
        let categoryTariffList = carmTariffMap.get(hsCodePrefix);
        if (!categoryTariffList) {
          categoryTariffList = [];
          carmTariffMap.set(hsCodePrefix, categoryTariffList);
        }
        categoryTariffList.push(carmTariff);
      }

      const toBeSavedTariffList: Array<CanadaTariff> = [];
      for (const [hsCodePrefix, categoryTariffList] of carmTariffMap) {
        this.logger.log(`Updating or creating tariff records for HS code prefix ${hsCodePrefix}...`);
        const existingTariffList = await queryRunner.manager.find(CanadaTariff, {
          where: { hsCode: Like(`${hsCodePrefix}%`) },
          relations: FIND_CANADA_TARIFF_RELATIONS
        });
        this.logger.log(`-- Existing tariff record count: ${existingTariffList.length}`);
        for (const carmTariff of categoryTariffList) {
          this.logger.log(`Updating tariff record for ${carmTariff.hsCode}...`);
          let existingTariff = existingTariffList.find((t) => t.hsCode === carmTariff.hsCode);
          if (!existingTariff) {
            this.logger.log(`-- Existing tariff record for ${carmTariff.hsCode} not found, creating new...`);
            existingTariff = new CanadaTariff();
          }
          Object.assign(existingTariff, carmTariff);
          existingTariff.treatments = {};
          toBeSavedTariffList.push(existingTariff);
        }
      }

      this.logger.log(`Saving tariff records...`);
      const BATCH_SAVE_RECORD_LIMIT = 1000;
      const savedTariffList: Array<CanadaTariff> = [];
      for (let i = 0; i < Math.ceil(toBeSavedTariffList.length / BATCH_SAVE_RECORD_LIMIT); i++) {
        const batchToBeSavedTariffList = toBeSavedTariffList.slice(
          i * BATCH_SAVE_RECORD_LIMIT,
          (i + 1) * BATCH_SAVE_RECORD_LIMIT
        );
        savedTariffList.push(...(await queryRunner.manager.save(batchToBeSavedTariffList)));
        this.logger.log(`-- Batch ${i + 1}, record count: ${batchToBeSavedTariffList.length}`);
      }
      this.logger.log(`-- Saved tariff record count: ${savedTariffList.length}`);

      this.logger.log(`Removing expired tariff records...`);
      const result = await queryRunner.manager.delete(CanadaTariff, {
        expiryDate: LessThanOrEqual(new Date())
      });
      this.logger.log(`-- Removed expired tariff record count: ${result.affected}`);

      if (syncCanadaTariffsDto.fullSync) {
        this.logger.log(`Removing orphan tariff records for full sync...`);
        const hsCodePrefixMap = new Map<string, Array<number>>();
        for (const tariff of savedTariffList) {
          const hsCodePrefix = tariff.hsCode.substring(0, 2);
          let hsCodePrefixList = hsCodePrefixMap.get(hsCodePrefix);
          if (!hsCodePrefixList) {
            hsCodePrefixList = [];
            hsCodePrefixMap.set(hsCodePrefix, hsCodePrefixList);
          }
          hsCodePrefixList.push(tariff.id);
        }
        for (const [hsCodePrefix, tariffIdList] of hsCodePrefixMap) {
          this.logger.log(`-- Removing orphan tariff records for prefix ${hsCodePrefix}...`);
          const result = await queryRunner.manager.delete(CanadaTariff, {
            hsCode: Like(`${hsCodePrefix}%`),
            id: Not(In(tariffIdList))
          });
          this.logger.log(`-- Removed orphan tariff record count: ${result.affected}`);
        }
      }

      await queryRunner.commitTransaction();

      this.logger.log(`Updating sync history...`);
      await this.tariffSyncHistoryRepository.update(
        { id: tariffSyncHistory.id },
        { status: SyncStatus.SUCCESS, finishDate: new Date() }
      );
    } catch (error: any) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error while syncing Canada Tariffs from CARM: ${error.message}`,
        error instanceof Error ? error.stack : null
      );
      await this.tariffSyncHistoryRepository.update(
        { id: tariffSyncHistory.id },
        {
          status: SyncStatus.FAILED,
          finishDate: new Date(),
          errorMessage: error?.message || null
        }
      );
    } finally {
      await queryRunner.release();
    }
  }

  async syncCanadaTariffsFromCbsaWebsite(tariffSyncHistory: TariffSyncHistory) {
    // if (!this.canadaTariffModuleOptions.sync) throw new ForbiddenException('Sync Candata Tariffs is disabled');
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    function findParentNode(hsCode: string, currentNode: TariffTreeNode, logger: Logger) {
      logger.log(
        `Finding parent node for ${hsCode}... Current Node: ${currentNode.hsCode}, children: ${currentNode.children.length}`
      );
      for (const childNode of currentNode.children) {
        if (hsCode.startsWith(childNode.hsCode)) return findParentNode(hsCode, childNode, logger);
      }
      return currentNode;
    }

    function createOrUpdateTariffRecords(
      currentNode: TariffTreeNode,
      categoryTariffRecords: Array<CanadaTariff>,
      logger: Logger,
      currentUser?: User
    ) {
      logger.log(`Creating or updating tariff record for ${currentNode.hsCode}...`);
      let tariffRecord = categoryTariffRecords.find((t) => t.hsCode === currentNode.hsCode);
      if (!tariffRecord) {
        logger.log(`-- Existing tariff record not found, creating...`);
        tariffRecord = new CanadaTariff();
        tariffRecord.hsCode = currentNode.hsCode;
        tariffRecord.createdBy = currentUser || null;
      }
      tariffRecord.lastEditedBy = currentUser || null;
      tariffRecord.effectiveDate = currentNode.effectiveDate;
      tariffRecord.description = currentNode.description;
      tariffRecord.uom = currentNode.uom;
      // tariffRecord.mfn = null;
      // tariffRecord.generalTariff = null;
      tariffRecord.treatments = currentNode.treatments;
      const childrenTariffRecords: Array<CanadaTariff> = [tariffRecord];
      for (const childNode of currentNode.children) {
        childrenTariffRecords.push(
          ...createOrUpdateTariffRecords(
            childNode,
            categoryTariffRecords.filter((t) => t.hsCode.startsWith(currentNode.hsCode)),
            logger,
            currentUser
          )
        );
      }
      return childrenTariffRecords;
    }

    try {
      const canadaTariffRepository = queryRunner.manager.getRepository(CanadaTariff);
      const BATCH_SAVE_RECORD_LIMIT = 1000;
      const CBSA_BASE_URL = `https://www.cbsa-asfc.gc.ca`;
      const CBSA_TARIFF_MAIN_URL = `${CBSA_BASE_URL}/trade-commerce/tariff-tarif/menu-eng.html`;

      const mainPageRes = await axios.get(CBSA_TARIFF_MAIN_URL);
      this.logger.log(`Main Page Res: ${mainPageRes.status}`);
      const $mainPage = cheerio.load(mainPageRes.data);
      const $currentTariffAnchor = $mainPage(`a:contains('Current Customs Tariff')`);
      this.logger.log(`Current Tariff Anchor: ${$currentTariffAnchor}`);
      const currentTariffUrl = $currentTariffAnchor.attr("href");
      this.logger.log(`Current Tariff URL: ${currentTariffUrl}`);

      const currentTariffPageRes = await axios.get(`${CBSA_BASE_URL}${currentTariffUrl}`);
      this.logger.log(`Current Tariff Page Res: ${currentTariffPageRes.status}`);
      const $currentTariffPage = cheerio.load(currentTariffPageRes.data);
      const $zipAnchors = $currentTariffPage('a[href$=".zip"]');
      this.logger.log(`Zip Anchors: ${$zipAnchors}`);
      const zipUrls = $zipAnchors.map((_, el) => $currentTariffPage(el).attr("href")).get();
      this.logger.log(`ZIP Urls: ${JSON.stringify(zipUrls)}`);

      if (zipUrls.length > 0) {
        const zipRes = await axios.get(`${CBSA_BASE_URL}${zipUrls[0]}`, {
          responseType: "arraybuffer"
        });
        const zipFileBuffer = Buffer.from(zipRes.data as ArrayBuffer);
        const zipFile = new AdmZip(zipFileBuffer);
        const tariffDbBuffer = zipFile.getEntries()[0].getData();

        const MDBReader = (await import("mdb-reader")).default;
        const tariffDbReader = new MDBReader(tariffDbBuffer);
        const tariffTable = tariffDbReader.getTable("TPHS");
        const rawTariffList = tariffTable
          .getData<CBSATariffFileRow>()
          .map(
            ({
              TARIFF,
              EFF_DATE,
              CHANGE,
              SUB_CHAP,
              DESC1,
              DESC2,
              DESC3,
              FOOTNOTE,
              UOM,
              MFN,
              "General Tariff": GEN,
              row_id,
              ...row
            }) => ({
              hsCode: TARIFF.replaceAll(".", ""),
              effectiveDate: EFF_DATE,
              description: [DESC1, DESC2, DESC3]
                .filter((s) => typeof s === "string" && s.length > 0)
                .join("")
                .replace(/(\.|:|;)$/, ""),
              uom:
                typeof UOM === "string" && Object.values(UnitOfMeasure).includes(UOM as UnitOfMeasure)
                  ? (UOM as UnitOfMeasure)
                  : null,
              MFN,
              GEN,
              ...row
            })
          )
          .sort((a, b) => {
            const lengthDiff = a.hsCode.length - b.hsCode.length;
            if (lengthDiff !== 0) return lengthDiff;
            return a.hsCode < b.hsCode ? -1 : a.hsCode > b.hsCode ? 1 : 0;
          });
        // fs.writeFileSync('canada-tariff.json', JSON.stringify(rawTariffList));

        const rootNode: TariffTreeNode = {
          hsCode: "",
          description: null,
          effectiveDate: null,
          uom: null,
          treatments: {},
          children: []
        };
        for (const { hsCode, effectiveDate, description, uom, ...treatments } of rawTariffList) {
          const currentNode: TariffTreeNode = {
            hsCode,
            effectiveDate,
            description,
            uom,
            treatments: {},
            children: []
          };
          for (const [treatment, rate] of Object.entries(treatments)) {
            if (!/^[A-Z]+$/.test(treatment.trim())) continue;
            if (typeof rate !== "string" || rate.length <= 0) continue;
            currentNode.treatments[treatment.trim()] = rate;
          }
          const parentNode = findParentNode(hsCode, rootNode, this.logger);
          if (typeof parentNode.description === "string" && parentNode.description.length > 0)
            currentNode.description = `${parentNode.description}; ${description}`;
          for (const treatment of Object.keys(parentNode.treatments)) {
            if (currentNode.treatments[treatment]) continue;
            currentNode.treatments[treatment] = parentNode.treatments[treatment];
          }
          parentNode.children.push(currentNode);
        }

        const toBeSavedTariffRecordList: Array<CanadaTariff> = [];
        for (const topCategoryNode of rootNode.children) {
          const categoryTariffRecords = await canadaTariffRepository.find({
            where: { hsCode: Like(`${topCategoryNode.hsCode}%`) },
            relations: FIND_CANADA_TARIFF_RELATIONS
          });
          toBeSavedTariffRecordList.push(
            ...createOrUpdateTariffRecords(
              topCategoryNode,
              categoryTariffRecords,
              this.logger,
              this.request?.user
            )
          );
        }
        const filteredToBeSavedTariffRecordList = toBeSavedTariffRecordList.filter(
          (t) => t.hsCode.length === 10
        );

        this.logger.log(`Start saving tariff...`);
        const savedTariffRecordList: Array<CanadaTariff> = [];
        for (
          let i = 0;
          i < Math.ceil(filteredToBeSavedTariffRecordList.length / BATCH_SAVE_RECORD_LIMIT);
          i++
        ) {
          const batchToBeSavedTariffRecordList = filteredToBeSavedTariffRecordList.slice(
            i * BATCH_SAVE_RECORD_LIMIT,
            (i + 1) * BATCH_SAVE_RECORD_LIMIT
          );
          this.logger.log(`-- Batch ${i + 1}, record count: ${batchToBeSavedTariffRecordList.length}`);
          savedTariffRecordList.push(...(await canadaTariffRepository.save(batchToBeSavedTariffRecordList)));
        }
        this.logger.log(`Saved Tariff Count: ${savedTariffRecordList.length}`);

        this.logger.log(`Start deleting unused tariff records...`);
        for (let i = 1; i < 100; i++) {
          const hsCodePrefix = i.toString().padStart(2, "0");
          this.logger.log(`-- Deleting unused tariff records with prefix ${hsCodePrefix}...`);
          const deleteResult = await canadaTariffRepository.delete({
            hsCode: Like(`${hsCodePrefix}%`),
            id: Not(
              In(savedTariffRecordList.filter((t) => t.hsCode.startsWith(hsCodePrefix)).map((t) => t.id))
            )
          });
          this.logger.log(`-- Deleted Tariff Count: ${deleteResult.affected}`);
        }
      }

      await queryRunner.commitTransaction();

      await this.tariffSyncHistoryRepository.update(
        { id: tariffSyncHistory.id },
        { status: SyncStatus.SUCCESS, finishDate: new Date() }
      );
    } catch (error: any) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      this.logger.error(`Error while syncing Canada Tariffs: ${error.message}`);
      await this.tariffSyncHistoryRepository.update(
        { id: tariffSyncHistory.id },
        {
          status: SyncStatus.FAILED,
          finishDate: new Date(),
          errorMessage: error?.message || null
        }
      );
    } finally {
      if (!queryRunner.isReleased) await queryRunner.release();
    }
  }
}
