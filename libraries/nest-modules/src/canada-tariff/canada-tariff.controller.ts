import {
  Controller,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Query,
  UseGuards
} from "@nestjs/common";
import {
  ApiConflictResponse,
  ApiForbiddenResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags
} from "@nestjs/swagger";
import { ApiAccessTokenAuthenticated, ApiGetByIdResponses, ApiGetManyResponses } from "../decorators";
import {
  ConflictResponseDto,
  ForbiddenResponseDto,
  GetCanadaTariffsDto,
  GetCanadaTariffsResponseDto,
  SyncCanadaTariffsDto
} from "../dto";
import { CanadaTariff } from "../entities";
import { AccessTokenGuard } from "../guards";
import { CanadaTariffService } from "./canada-tariff.service";

@ApiTags("Canada Tariff API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("canada-tariffs")
export class CanadaTariffController {
  constructor(private readonly canadaTariffService: CanadaTariffService) {}

  @ApiOperation({ summary: "Get Canada Tariffs" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: GetCanadaTariffsResponseDto })
  @Get()
  async getCanadaTariffs(@Query() getCanadaTariffsDto: GetCanadaTariffsDto) {
    return await this.canadaTariffService.getCanadaTariffs(getCanadaTariffsDto);
  }

  @ApiOperation({ summary: "Get Canada Tariff" })
  @ApiParam({ name: "id", type: "integer", description: "Canada Tariff ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: CanadaTariff })
  @Get(":id")
  async getCanadaTariffById(@Param("id", ParseIntPipe) id: number) {
    const tariff = await this.canadaTariffService.getCanadaTariffById(id);
    if (!tariff) throw new NotFoundException("Canada Tariff not found");
    return tariff;
  }

  @ApiOperation({ summary: "Sync Canada Tariffs" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiConflictResponse({ type: ConflictResponseDto })
  @ApiOkResponse()
  @HttpCode(200)
  @Post("sync")
  async syncCanadaTariffs(@Query() syncCanadaTariffsDto: SyncCanadaTariffsDto) {
    return await this.canadaTariffService.createCanadaTariffSyncEvent(syncCanadaTariffsDto);
  }
}
