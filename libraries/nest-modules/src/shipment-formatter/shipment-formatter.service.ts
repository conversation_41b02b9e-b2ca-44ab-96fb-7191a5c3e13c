import { Injectable } from "@nestjs/common";
import { Shipment, Container } from "../entities";
import { CustomsStatus } from "../types";
import { CUSTOMS_STATUS_MESSAGES, getCustomsStatusMessage } from "../constants";
import moment from "moment-timezone";

/**
 * Service for formatting shipment data for display.
 * Consolidates all formatting logic in one place.
 */
@Injectable()
export class ShipmentFormatterService {
  /**
   * Format customs status for user display
   */
  formatCustomsStatus(customsStatus: string | null | undefined): string {
    return getCustomsStatusMessage(customsStatus);
  }

  /**
   * Format date for display (e.g., "March 15, 2024")
   */
  formatDate(date: Date | string | null | undefined): string | null {
    if (!date) return null;
    return moment(date).format("MMMM DD, YYYY");
  }

  /**
   * Format ETA date with fallback text
   */
  formatEtaDate(date: Date | string | null | undefined): string {
    if (!date) return "TBD";
    return this.formatDate(date) || "TBD";
  }

  /**
   * Format shipment identifiers for display
   */
  formatShipmentIdentifiers(shipment: Shipment) {
    const containers = shipment.containers || [];
    const hasMultipleContainers = containers.length > 1;
    
    return {
      hblNumber: shipment.hblNumber || null,
      cargoControlNumber: shipment.cargoControlNumber || null,
      transactionNumber: shipment.transactionNumber || null,
      hasMultipleContainers,
      primaryContainer: containers.length > 0 ? containers[0].containerNumber : null,
      formattedContainers: this.formatContainerNumbers(containers),
      containerNumbers: containers.map(c => c.containerNumber).filter(Boolean)
    };
  }

  /**
   * Format container numbers for display
   */
  formatContainerNumbers(containers: Container[]): string {
    if (!containers || containers.length === 0) return "";
    
    const numbers = containers.map(c => c.containerNumber).filter(Boolean);
    
    if (numbers.length === 0) return "";
    if (numbers.length === 1) return numbers[0];
    if (numbers.length === 2) return `${numbers[0]} and ${numbers[1]}`;
    
    // For 3+ containers: "ABC123, DEF456, and 2 others"
    const displayed = numbers.slice(0, 2).join(", ");
    const remaining = numbers.length - 2;
    return `${displayed}, and ${remaining} other${remaining > 1 ? "s" : ""}`;
  }

  /**
   * Format missing fields for display with ** emphasis
   */
  formatMissingFields(missingFields: string[]): string[] {
    const fieldNameMap: Record<string, string> = {
      cargoControlNumber: "CCN",
      hblNumber: "HBL",
      weight: "Weight",
      weightUOM: "Weight unit",
      quantity: "Quantity",
      quantityUOM: "Quantity unit",
      etd: "ETD",
      etaPort: "ETA Port",
      etaDestination: "ETA Destination",
      portCode: "Port code",
      subLocation: "Sub-location",
      portOfLoadingId: "Port of loading",
      placeOfDelivery: "Place of delivery",
      vendorName: "Vendor name",
      vendorAddress: "Vendor address",
      consigneeName: "Consignee name",
      consigneeAddress: "Consignee address"
    };

    return missingFields.map(field => {
      const displayName = fieldNameMap[field] || field;
      return `${displayName} **missing**`;
    });
  }

  /**
   * Format transport mode for display
   */
  formatTransportMode(shipmentMode: string | null | undefined): string {
    if (!shipmentMode) return "UNKNOWN";
    
    const modeMap: Record<string, string> = {
      "ocean-fcl": "OCEAN",
      "ocean-lcl": "OCEAN",
      "air": "AIR",
      "land": "TRUCK",
      "small-package": "SMALL PACKAGE"
    };
    
    return modeMap[shipmentMode.toLowerCase()] || shipmentMode.toUpperCase();
  }

  /**
   * Format document status (Received/Missing)
   */
  formatDocumentStatus(hasDocument: boolean): string {
    return hasDocument ? "Received" : "Missing";
  }

  /**
   * Format validation issues for display
   */
  formatValidationIssues(missingDocuments: string[], missingFields: string[]) {
    return {
      showValidationIssues: missingDocuments.length > 0 || missingFields.length > 0,
      missingDocuments: this.formatMissingDocumentTypes(missingDocuments),
      missingFields: this.formatMissingFields(missingFields)
    };
  }

  /**
   * Format missing document types for display
   */
  private formatMissingDocumentTypes(documentTypes: string[]): string[] {
    const typeMap: Record<string, string> = {
      "commercial-invoice": "Commercial Invoice",
      "packing-list": "Packing List",
      "bill-of-lading": "Bill of Lading",
      "air-waybill": "Air Waybill",
      "certificate-of-origin": "Certificate of Origin"
    };

    return documentTypes.map(type => typeMap[type] || type);
  }

  /**
   * Get user-friendly customs status response message
   */
  getStatusResponseMessage(customsStatus: string): string {
    const messages: Record<string, string> = {
      [CustomsStatus.PENDING_COMMERCIAL_INVOICE]: "Commercial invoice is missing for this shipment.",
      [CustomsStatus.PENDING_CONFIRMATION]: "There are compliance issues or missing required fields that need to be resolved.",
      [CustomsStatus.PENDING_ARRIVAL]: "Your shipment is pending arrival at the port.",
      [CustomsStatus.LIVE]: "Your shipment entry has been uploaded and is ready for submission.",
      [CustomsStatus.ENTRY_SUBMITTED]: "Your shipment entry has been submitted to customs.",
      [CustomsStatus.ENTRY_ACCEPTED]: "The shipment entry has been accepted by customs and is in queue for release.",
      [CustomsStatus.EXAM]: "Your shipment has been selected for examination by customs.",
      [CustomsStatus.RELEASED]: "Your shipment has been released by customs!",
      [CustomsStatus.ACCOUNTING_COMPLETED]: "Your shipment has been released and accounting is complete."
    };

    return messages[customsStatus] || "Your shipment is being processed.";
  }

  /**
   * Get rush action message based on customs status
   */
  getRushActionMessage(customsStatus: string): string {
    if ([CustomsStatus.PENDING_COMMERCIAL_INVOICE, CustomsStatus.PENDING_CONFIRMATION].includes(customsStatus as CustomsStatus)) {
      return "We need the required documents and information before we can rush your shipment.";
    }
    
    if ([CustomsStatus.ENTRY_ACCEPTED, CustomsStatus.EXAM].includes(customsStatus as CustomsStatus)) {
      return "Your shipment is already in queue for release. We'll monitor it closely.";
    }
    
    if ([CustomsStatus.RELEASED, CustomsStatus.ACCOUNTING_COMPLETED].includes(customsStatus as CustomsStatus)) {
      return "Your shipment has already been released!";
    }
    
    return "We'll prioritize your shipment for immediate processing.";
  }

  /**
   * Get document blocker reason based on customs status
   */
  getDocumentBlockerReason(customsStatus: string): string | null {
    if (customsStatus === CustomsStatus.PENDING_COMMERCIAL_INVOICE) {
      return "We need the commercial invoice and packing list first.";
    }
    
    if (customsStatus === CustomsStatus.PENDING_CONFIRMATION) {
      return "We need to resolve compliance issues before we can generate documents.";
    }
    
    if ([CustomsStatus.RELEASED, CustomsStatus.ACCOUNTING_COMPLETED].includes(customsStatus as CustomsStatus)) {
      return "Your shipment has already been released.";
    }
    
    return null;
  }
}