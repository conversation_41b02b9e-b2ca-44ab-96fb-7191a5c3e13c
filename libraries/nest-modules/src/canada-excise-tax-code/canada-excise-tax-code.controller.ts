import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards
} from "@nestjs/common";
import { ApiForbiddenResponse, ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import {
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses
} from "../decorators";
import {
  BatchUpdateCanadaExciseTaxCodesDto,
  BatchUpdateCanadaExciseTaxCodesResponseDto,
  CreateCanadaExciseTaxCodeDto,
  EditCanadaExciseTaxCodeDto,
  ForbiddenResponseDto,
  GetCanadaExciseTaxCodesDto,
  GetCanadaExciseTaxCodesResponseDto
} from "../dto";
import { CanadaExciseTaxCode } from "../entities";
import { AccessTokenGuard } from "../guards";
import { CanadaExciseTaxCodeService } from "./canada-excise-tax-code.service";

@ApiTags("Canada Excise Tax Code API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("canada-excise-tax-codes")
export class CanadaExciseTaxCodeController {
  constructor(private readonly canadaExciseTaxCodeService: CanadaExciseTaxCodeService) {}

  @Get()
  @ApiOperation({ summary: "Get Canada Excise Tax Codes" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: GetCanadaExciseTaxCodesResponseDto })
  async getCanadaExciseTaxCodes(@Query() getCanadaExciseTaxCodesDto: GetCanadaExciseTaxCodesDto) {
    return await this.canadaExciseTaxCodeService.getCanadaExciseTaxCodes(getCanadaExciseTaxCodesDto);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get Canada Excise Tax Code by ID" })
  @ApiParam({ name: "id", type: "integer", description: "Canada Excise Tax Code ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: CanadaExciseTaxCode })
  async getCanadaExciseTaxCodeById(@Param("id", ParseIntPipe) id: number) {
    const code = await this.canadaExciseTaxCodeService.getCanadaExciseTaxCodeById(id);
    if (!code) throw new NotFoundException("Canada Excise Tax Code not found");
    return code;
  }

  @Post()
  @ApiOperation({ summary: "Create Canada Excise Tax Code" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiCreateResponses({ type: CanadaExciseTaxCode })
  async createCanadaExciseTaxCode(@Body() createCanadaExciseTaxCodeDto: CreateCanadaExciseTaxCodeDto) {
    return await this.canadaExciseTaxCodeService.createCanadaExciseTaxCode(createCanadaExciseTaxCodeDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update Canada Excise Tax Code" })
  @ApiParam({ name: "id", type: "integer", description: "Canada Excise Tax Code ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: CanadaExciseTaxCode })
  async editCanadaExciseTaxCode(
    @Param("id", ParseIntPipe) id: number,
    @Body() editCanadaExciseTaxCodeDto: EditCanadaExciseTaxCodeDto
  ) {
    return await this.canadaExciseTaxCodeService.editCanadaExciseTaxCode(id, editCanadaExciseTaxCodeDto);
  }

  @HttpCode(204)
  @Delete(":id")
  @ApiOperation({ summary: "Delete Canada Excise Tax Code" })
  @ApiParam({ name: "id", type: "integer", description: "Canada Excise Tax Code ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiDeleteResponses()
  async deleteCanadaExciseTaxCode(@Param("id", ParseIntPipe) id: number) {
    await this.canadaExciseTaxCodeService.deleteCanadaExciseTaxCode(id);
    return;
  }

  @HttpCode(200)
  @Post("batch")
  @ApiOperation({ summary: "Batch update Canada Excise Tax Codes" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: BatchUpdateCanadaExciseTaxCodesResponseDto })
  async batchUpdateCanadaExciseTaxCodes(
    @Body() batchUpdateCanadaExciseTaxCodesDto: BatchUpdateCanadaExciseTaxCodesDto
  ) {
    return await this.canadaExciseTaxCodeService.batchUpdateCanadaExciseTaxCodes(
      batchUpdateCanadaExciseTaxCodesDto
    );
  }
}
