import { DynamicModule, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CanadaExciseTaxCode } from "../entities";
import { CANADA_EXCISE_TAX_CODE_MODULE_OPTIONS, CrudOptions } from "../types";
import { CanadaExciseTaxCodeController } from "./canada-excise-tax-code.controller";
import { CanadaExciseTaxCodeService } from "./canada-excise-tax-code.service";

@Module({})
export class CanadaExciseTaxCodeModule {
  static register(options: CrudOptions): DynamicModule {
    return {
      global: true,
      module: CanadaExciseTaxCodeModule,
      imports: [TypeOrmModule.forFeature([CanadaExciseTaxCode])],
      controllers: [CanadaExciseTaxCodeController],
      providers: [
        {
          provide: CANADA_EXCISE_TAX_CODE_MODULE_OPTIONS,
          useValue: options
        },
        CanadaExciseTaxCodeService
      ],
      exports: [CanadaExciseTaxCodeService]
    };
  }
}
