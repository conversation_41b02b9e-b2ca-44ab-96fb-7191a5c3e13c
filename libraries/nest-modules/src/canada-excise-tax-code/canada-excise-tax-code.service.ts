import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import { DataSource, In, Not, QueryRunner, Repository } from "typeorm";
import {
  BatchUpdateCanadaExciseTaxCodesDto,
  BatchUpdateCanadaExciseTaxCodesResponseDto,
  CreateCanadaExciseTaxCodeDto,
  EditCanadaExciseTaxCodeDto,
  GetCanadaExciseTaxCodesDto,
  GetCanadaExciseTaxCodesResponseDto
} from "../dto";
import { CanadaExciseTaxCode } from "../entities";
import { convertFromCamelCase, getFindOptions } from "../helper-functions";
import { MatchingRuleService } from "../matching-rule";
import {
  AuthenticatedRequest,
  CANADA_EXCISE_TAX_CODE_MODULE_OPTIONS,
  CANADA_EXCISE_TAX_CODE_REQUIRED_KEYS,
  CanadaExciseTaxCodeColumn,
  CrudOptions,
  FIND_CANADA_EXCISE_TAX_CODE_RELATIONS,
  MatchingRuleSourceDatabaseTable,
  UserPermission
} from "../types";

@Injectable({ scope: Scope.REQUEST })
export class CanadaExciseTaxCodeService {
  constructor(
    @InjectRepository(CanadaExciseTaxCode)
    private readonly canadaExciseTaxCodeRepository: Repository<CanadaExciseTaxCode>,
    @Inject(MatchingRuleService)
    private readonly matchingRuleService: MatchingRuleService,
    @Inject(CANADA_EXCISE_TAX_CODE_MODULE_OPTIONS)
    private readonly options: CrudOptions,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}

  async getCanadaExciseTaxCodes(
    getCanadaExciseTaxCodesDto: GetCanadaExciseTaxCodesDto
  ): Promise<GetCanadaExciseTaxCodesResponseDto> {
    if (!this.options.readMany) throw new ForbiddenException("Get Canada Excise Tax Codes is disabled");
    const { where, order, skip, take } = getFindOptions(
      getCanadaExciseTaxCodesDto,
      [],
      [],
      CanadaExciseTaxCodeColumn.id
    );
    const [data, total] = await this.canadaExciseTaxCodeRepository.findAndCount({
      where,
      order,
      relations: FIND_CANADA_EXCISE_TAX_CODE_RELATIONS,
      skip,
      take
    });
    return {
      data,
      total,
      skip,
      limit: take
    };
  }

  async getCanadaExciseTaxCodeById(codeId: number, queryRunner?: QueryRunner) {
    if (!this.options.readOne) throw new ForbiddenException("Get Canada Excise Tax Code is disabled");
    const canadaExciseTaxCodeRepository = queryRunner
      ? queryRunner.manager.getRepository(CanadaExciseTaxCode)
      : this.canadaExciseTaxCodeRepository;
    const canadaExciseTaxCode = await canadaExciseTaxCodeRepository.findOne({
      where: { id: codeId },
      relations: FIND_CANADA_EXCISE_TAX_CODE_RELATIONS
    });
    return canadaExciseTaxCode;
  }

  async createCanadaExciseTaxCode(createCanadaExciseTaxCodeDto: CreateCanadaExciseTaxCodeDto) {
    if (!this.options.create) throw new ForbiddenException("Create Canada Excise Tax Code is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to create Canada Excise Tax Codes");
    let newCanadaExciseTaxCode = new CanadaExciseTaxCode();
    newCanadaExciseTaxCode.createdBy = this.request?.user || null;
    newCanadaExciseTaxCode.lastEditedBy = this.request?.user || null;
    for (const [key, value] of Object.entries(createCanadaExciseTaxCodeDto)) {
      if (value === undefined) continue;
      if (CANADA_EXCISE_TAX_CODE_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      newCanadaExciseTaxCode[key] = value;
    }
    if (await this.canadaExciseTaxCodeRepository.existsBy({ code: newCanadaExciseTaxCode.code }))
      throw new BadRequestException("Canada Excise Tax Code with the same code already exists");

    newCanadaExciseTaxCode = await this.canadaExciseTaxCodeRepository.save(newCanadaExciseTaxCode);

    return await this.getCanadaExciseTaxCodeById(newCanadaExciseTaxCode.id);
  }

  async editCanadaExciseTaxCode(codeId: number, editCanadaExciseTaxCodeDto: EditCanadaExciseTaxCodeDto) {
    if (!this.options.update) throw new ForbiddenException("Edit Canada Excise Tax Code is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to edit Canada Excise Tax Codes");
    const canadaExciseTaxCode = await this.getCanadaExciseTaxCodeById(codeId);
    if (!canadaExciseTaxCode) throw new NotFoundException("Canada Excise Tax Code not found");
    canadaExciseTaxCode.lastEditedBy = this.request?.user || null;
    for (const [key, value] of Object.entries(editCanadaExciseTaxCodeDto)) {
      if (value === undefined) continue;
      if (CANADA_EXCISE_TAX_CODE_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      canadaExciseTaxCode[key] = value;
    }
    if (
      await this.canadaExciseTaxCodeRepository.existsBy({
        id: Not(codeId),
        code: canadaExciseTaxCode.code
      })
    )
      throw new BadRequestException("Canada Excise Tax Code with the same code already exists");
    await this.canadaExciseTaxCodeRepository.save(canadaExciseTaxCode);
    return await this.getCanadaExciseTaxCodeById(codeId);
  }

  async deleteCanadaExciseTaxCode(codeId: number) {
    if (!this.options.delete) throw new ForbiddenException("Delete Canada Excise Tax Code is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to delete Canada Excise Tax Codes");
    if (!(await this.canadaExciseTaxCodeRepository.existsBy({ id: codeId })))
      throw new NotFoundException("Canada Excise Tax Code not found");
    if (
      await this.matchingRuleService.isMatchingRuleForSourceRecordExists(
        MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE,
        codeId
      )
    )
      throw new BadRequestException("Canada Excise Tax Code is being used in a matching rule");
    await this.canadaExciseTaxCodeRepository.delete({ id: codeId });
    return;
  }

  async batchUpdateCanadaExciseTaxCodes(
    batchUpdateCanadaExciseTaxCodesDto: BatchUpdateCanadaExciseTaxCodesDto
  ): Promise<BatchUpdateCanadaExciseTaxCodesResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const savedCanadaExciseTaxCodeIds: Array<number> = [];
    try {
      const { create: createList, edit: editList, delete: deleteList } = batchUpdateCanadaExciseTaxCodesDto;

      if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
        throw new ForbiddenException("You are not authorized to batch update Canada Excise Tax Codes");
      if (Array.isArray(createList) && createList.length > 0 && !this.options.create)
        throw new ForbiddenException("Create Canada Excise Tax Code is disabled");
      if (Array.isArray(editList) && editList.length > 0 && !this.options.update)
        throw new ForbiddenException("Edit Canada Excise Tax Code is disabled");
      if (Array.isArray(deleteList) && deleteList.length > 0 && !this.options.delete)
        throw new ForbiddenException("Delete Canada Excise Tax Code is disabled");

      const canadaExciseTaxCodeRepository = queryRunner.manager.getRepository(CanadaExciseTaxCode);
      const toBeSavedCanadaExciseTaxCodes: Array<CanadaExciseTaxCode> = [];

      for (let i = 0; i < (createList || []).length; i++) {
        const errorMsgPrefix = `Error on create list index ${i}: `;
        const createCanadaExciseTaxCodeDto = createList[i];
        let newCanadaExciseTaxCode = new CanadaExciseTaxCode();
        newCanadaExciseTaxCode.createdBy = this.request?.user || null;
        newCanadaExciseTaxCode.lastEditedBy = this.request?.user || null;
        for (const [key, value] of Object.entries(createCanadaExciseTaxCodeDto)) {
          if (value === undefined) continue;
          if (CANADA_EXCISE_TAX_CODE_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
          newCanadaExciseTaxCode[key] = value;
        }
        if (
          toBeSavedCanadaExciseTaxCodes.some((c) => c.code === newCanadaExciseTaxCode.code) ||
          (await canadaExciseTaxCodeRepository.existsBy({
            code: newCanadaExciseTaxCode.code
          }))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada Excise Tax Code with the same code already exists`
          );

        toBeSavedCanadaExciseTaxCodes.push(newCanadaExciseTaxCode);
      }

      for (let i = 0; i < (editList || []).length; i++) {
        const errorMsgPrefix = `Error on edit list index ${i}: `;
        const { id: codeId, ...editCanadaExciseTaxCodeDto } = editList[i];
        const canadaExciseTaxCode = await this.getCanadaExciseTaxCodeById(codeId, queryRunner);
        if (!canadaExciseTaxCode)
          throw new NotFoundException(`${errorMsgPrefix}Canada Excise Tax Code not found`);
        canadaExciseTaxCode.lastEditedBy = this.request?.user || null;
        for (const [key, value] of Object.entries(editCanadaExciseTaxCodeDto)) {
          if (value === undefined) continue;
          if (CANADA_EXCISE_TAX_CODE_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
          canadaExciseTaxCode[key] = value;
        }
        if (
          toBeSavedCanadaExciseTaxCodes.some(
            (c) => c.code === canadaExciseTaxCode.code && c.id !== canadaExciseTaxCode.id
          ) ||
          (await canadaExciseTaxCodeRepository.existsBy({
            id: Not(
              In(
                toBeSavedCanadaExciseTaxCodes
                  .filter((c) => typeof c.id === "number")
                  .map((c) => c.id)
                  .concat([codeId])
              )
            ),
            code: canadaExciseTaxCode.code
          }))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada Excise Tax Code with the same code already exists`
          );
        toBeSavedCanadaExciseTaxCodes.push(canadaExciseTaxCode);
      }

      for (let i = 0; i < (deleteList || []).length; i++) {
        const codeId = deleteList[i];
        const errorMsgPrefix = `Error on delete list index ${i}: `;
        if (!(await canadaExciseTaxCodeRepository.existsBy({ id: codeId })))
          throw new NotFoundException(`${errorMsgPrefix}Canada Excise Tax Code not found`);
        if (toBeSavedCanadaExciseTaxCodes.some((c) => c.id === codeId))
          throw new BadRequestException(`${errorMsgPrefix}Canada Excise Tax Code is being edited`);
        if (
          await this.matchingRuleService.isMatchingRuleForSourceRecordExists(
            MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE,
            codeId,
            queryRunner
          )
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada Excise Tax Code is being used in a matching rule`
          );
      }

      savedCanadaExciseTaxCodeIds.push(
        ...(await canadaExciseTaxCodeRepository.save(toBeSavedCanadaExciseTaxCodes)).map((c) => c.id)
      );
      if (Array.isArray(deleteList) && deleteList.length > 0)
        await canadaExciseTaxCodeRepository.delete({ id: In(deleteList) });

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner.isReleased) queryRunner.release();
    }

    return {
      data: await this.canadaExciseTaxCodeRepository.find({
        where: { id: In(savedCanadaExciseTaxCodeIds) },
        relations: FIND_CANADA_EXCISE_TAX_CODE_RELATIONS
      })
    };
  }
}
