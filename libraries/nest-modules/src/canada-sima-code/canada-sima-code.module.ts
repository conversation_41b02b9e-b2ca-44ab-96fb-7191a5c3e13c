import { DynamicModule, Module } from "@nestjs/common";
import { CANADA_SIMA_CODE_MODULE_OPTIONS, CrudOptions } from "../types";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CanadaSimaCodeController } from "./canada-sima-code.controller";
import { CanadaSimaCode } from "../entities";
import { CanadaSimaCodeService } from "./canada-sima-code.service";

@Module({})
export class CanadaSimaCodeModule {
  static register(options: CrudOptions): DynamicModule {
    return {
      global: true,
      module: CanadaSimaCodeModule,
      imports: [TypeOrmModule.forFeature([CanadaSimaCode])],
      controllers: [CanadaSimaCodeController],
      providers: [
        {
          provide: CANADA_SIMA_CODE_MODULE_OPTIONS,
          useValue: options
        },
        CanadaSimaCodeService
      ],
      exports: [CanadaSimaCodeService]
    };
  }
}
