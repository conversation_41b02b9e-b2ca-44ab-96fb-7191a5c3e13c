import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Query,
  UseGuards,
  NotFoundException,
  Post,
  Body,
  Put,
  Delete,
  HttpCode
} from "@nestjs/common";
import { AccessTokenGuard } from "../guards";
import {
  ApiAccessTokenAuthenticated,
  ApiCreateResponses,
  ApiDeleteResponses,
  ApiEditResponses,
  ApiGetByIdResponses,
  ApiGetManyResponses
} from "../decorators";
import { ApiForbiddenResponse, ApiOperation, ApiParam } from "@nestjs/swagger";
import { ApiTags } from "@nestjs/swagger";
import {
  BatchUpdateCanadaSimaCodesDto,
  BatchUpdateCanadaSimaCodesResponseDto,
  CreateCanadaSimaCodeDto,
  EditCanadaSimaCodeDto,
  ForbiddenResponseDto,
  GetCanadaSimaCodesDto,
  GetCanadaSimaCodesResponseDto
} from "../dto";
import { CanadaSimaCodeService } from "./canada-sima-code.service";
import { CanadaSimaCode } from "../entities";

@ApiTags("Canada SIMA Code API")
@ApiAccessTokenAuthenticated()
@UseGuards(AccessTokenGuard)
@Controller("canada-sima-codes")
export class CanadaSimaCodeController {
  constructor(private readonly canadaSimaCodeService: CanadaSimaCodeService) {}

  @Get()
  @ApiOperation({ summary: "Get Canada SIMA Codes" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetManyResponses({ type: GetCanadaSimaCodesResponseDto })
  async getCanadaSimaCodes(@Query() getCanadaSimaCodesDto: GetCanadaSimaCodesDto) {
    return await this.canadaSimaCodeService.getCanadaSimaCodes(getCanadaSimaCodesDto);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get Canada SIMA Code by ID" })
  @ApiParam({ name: "id", type: "integer", description: "Canada SIMA Code ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiGetByIdResponses({ type: CanadaSimaCode })
  async getCanadaSimaCodeById(@Param("id", ParseIntPipe) id: number) {
    const code = await this.canadaSimaCodeService.getCanadaSimaCodeById(id);
    if (!code) throw new NotFoundException("Canada SIMA Code not found");
    return code;
  }

  @Post()
  @ApiOperation({ summary: "Create Canada SIMA Code" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiCreateResponses({ type: CanadaSimaCode })
  async createCanadaSimaCode(@Body() createCanadaSimaCodeDto: CreateCanadaSimaCodeDto) {
    return await this.canadaSimaCodeService.createCanadaSimaCode(createCanadaSimaCodeDto);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update Canada SIMA Code" })
  @ApiParam({ name: "id", type: "integer", description: "Canada SIMA Code ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: CanadaSimaCode })
  async editCanadaSimaCode(
    @Param("id", ParseIntPipe) id: number,
    @Body() editCanadaSimaCodeDto: EditCanadaSimaCodeDto
  ) {
    return await this.canadaSimaCodeService.editCanadaSimaCode(id, editCanadaSimaCodeDto);
  }

  @HttpCode(204)
  @Delete(":id")
  @ApiOperation({ summary: "Delete Canada SIMA Code" })
  @ApiParam({ name: "id", type: "integer", description: "Canada SIMA Code ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiDeleteResponses()
  async deleteCanadaSimaCode(@Param("id", ParseIntPipe) id: number) {
    await this.canadaSimaCodeService.deleteCanadaSimaCode(id);
    return;
  }

  @HttpCode(200)
  @Post("batch")
  @ApiOperation({ summary: "Batch update Canada SIMA Codes" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: BatchUpdateCanadaSimaCodesResponseDto })
  async batchUpdateCanadaSimaCodes(@Body() batchUpdateCanadaSimaCodesDto: BatchUpdateCanadaSimaCodesDto) {
    return await this.canadaSimaCodeService.batchUpdateCanadaSimaCodes(batchUpdateCanadaSimaCodesDto);
  }
}
