import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { DataSource, In, Not, QueryRunner, Repository } from "typeorm";
import { CanadaSimaCode } from "../entities";
import {
  AuthenticatedRequest,
  CANADA_SIMA_CODE_MODULE_OPTIONS,
  CANADA_SIMA_CODE_REQUIRED_KEYS,
  CanadaSimaCodeColumn,
  FIND_CANADA_SIMA_CODE_RELATIONS,
  MatchingRuleSourceDatabaseTable,
  UserPermission
} from "../types";
import { CrudOptions } from "../types";
import { REQUEST } from "@nestjs/core";
import {
  BatchUpdateCanadaSimaCodesDto,
  BatchUpdateCanadaSimaCodesResponseDto,
  CreateCanadaSimaCodeDto,
  EditCanadaSimaCodeDto,
  GetCanadaSimaCodesDto,
  GetCanadaSimaCodesResponseDto
} from "../dto";
import { convertFromCamelCase, getFindOptions } from "../helper-functions";
import { MatchingRuleService } from "../matching-rule";

@Injectable({ scope: Scope.REQUEST })
export class CanadaSimaCodeService {
  constructor(
    @InjectRepository(CanadaSimaCode)
    private readonly canadaSimaCodeRepository: Repository<CanadaSimaCode>,
    @Inject(MatchingRuleService)
    private readonly matchingRuleService: MatchingRuleService,
    @Inject(CANADA_SIMA_CODE_MODULE_OPTIONS)
    private readonly options: CrudOptions,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest,
    private readonly dataSource: DataSource
  ) {}

  async getCanadaSimaCodes(
    getCanadaSimaCodesDto: GetCanadaSimaCodesDto
  ): Promise<GetCanadaSimaCodesResponseDto> {
    if (!this.options.readMany) throw new ForbiddenException("Get Canada SIMA Codes is disabled");
    const { where, order, skip, take } = getFindOptions(
      getCanadaSimaCodesDto,
      [],
      [],
      CanadaSimaCodeColumn.id
    );
    const [data, total] = await this.canadaSimaCodeRepository.findAndCount({
      where,
      order,
      relations: FIND_CANADA_SIMA_CODE_RELATIONS,
      skip,
      take
    });
    return {
      data,
      total,
      skip,
      limit: take
    };
  }

  async getCanadaSimaCodeById(codeId: number, queryRunner?: QueryRunner) {
    if (!this.options.readOne) throw new ForbiddenException("Get Canada SIMA Code is disabled");
    const canadaSimaCodeRepository = queryRunner
      ? queryRunner.manager.getRepository(CanadaSimaCode)
      : this.canadaSimaCodeRepository;
    const canadaSimaCode = await canadaSimaCodeRepository.findOne({
      where: { id: codeId },
      relations: FIND_CANADA_SIMA_CODE_RELATIONS
    });
    return canadaSimaCode;
  }

  async createCanadaSimaCode(createCanadaSimaCodeDto: CreateCanadaSimaCodeDto) {
    if (!this.options.create) throw new ForbiddenException("Create Canada SIMA Code is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to create Canada SIMA Codes");
    let newCanadaSimaCode = new CanadaSimaCode();
    newCanadaSimaCode.createdBy = this.request?.user || null;
    newCanadaSimaCode.lastEditedBy = this.request?.user || null;
    for (const [key, value] of Object.entries(createCanadaSimaCodeDto)) {
      if (value === undefined) continue;
      if (CANADA_SIMA_CODE_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      newCanadaSimaCode[key] = value;
    }
    if (await this.canadaSimaCodeRepository.existsBy({ code: newCanadaSimaCode.code }))
      throw new BadRequestException("Canada SIMA Code with the same code already exists");

    newCanadaSimaCode = await this.canadaSimaCodeRepository.save(newCanadaSimaCode);

    return await this.getCanadaSimaCodeById(newCanadaSimaCode.id);
  }

  async editCanadaSimaCode(codeId: number, editCanadaSimaCodeDto: EditCanadaSimaCodeDto) {
    if (!this.options.update) throw new ForbiddenException("Edit Canada SIMA Code is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to edit Canada SIMA Codes");
    const canadaSimaCode = await this.getCanadaSimaCodeById(codeId);
    if (!canadaSimaCode) throw new NotFoundException("Canada SIMA Code not found");
    canadaSimaCode.lastEditedBy = this.request?.user || null;
    for (const [key, value] of Object.entries(editCanadaSimaCodeDto)) {
      if (value === undefined) continue;
      if (CANADA_SIMA_CODE_REQUIRED_KEYS.includes(key) && value === null)
        throw new BadRequestException(`${convertFromCamelCase(key)} is required`);
      canadaSimaCode[key] = value;
    }
    if (
      await this.canadaSimaCodeRepository.existsBy({
        id: Not(codeId),
        code: canadaSimaCode.code
      })
    )
      throw new BadRequestException("Canada SIMA Code with the same code already exists");
    await this.canadaSimaCodeRepository.save(canadaSimaCode);
    return await this.getCanadaSimaCodeById(codeId);
  }

  async deleteCanadaSimaCode(codeId: number) {
    if (!this.options.delete) throw new ForbiddenException("Delete Canada SIMA Code is disabled");
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("You are not authorized to delete Canada SIMA Codes");
    if (!(await this.canadaSimaCodeRepository.existsBy({ id: codeId })))
      throw new NotFoundException("Canada SIMA Code not found");
    if (
      await this.matchingRuleService.isMatchingRuleForSourceRecordExists(
        MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE,
        codeId
      )
    )
      throw new BadRequestException("Canada SIMA Code is being used in a matching rule");
    await this.canadaSimaCodeRepository.delete({ id: codeId });
    return;
  }

  async batchUpdateCanadaSimaCodes(
    batchUpdateCanadaSimaCodesDto: BatchUpdateCanadaSimaCodesDto
  ): Promise<BatchUpdateCanadaSimaCodesResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    const savedCanadaSimaCodeIds: Array<number> = [];
    try {
      const { create: createList, edit: editList, delete: deleteList } = batchUpdateCanadaSimaCodesDto;

      if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
        throw new ForbiddenException("You are not authorized to batch update Canada SIMA Codes");
      if (Array.isArray(createList) && createList.length > 0 && !this.options.create)
        throw new ForbiddenException("Create Canada SIMA Code is disabled");
      if (Array.isArray(editList) && editList.length > 0 && !this.options.update)
        throw new ForbiddenException("Edit Canada SIMA Code is disabled");
      if (Array.isArray(deleteList) && deleteList.length > 0 && !this.options.delete)
        throw new ForbiddenException("Delete Canada SIMA Code is disabled");

      const canadaSimaCodeRepository = queryRunner.manager.getRepository(CanadaSimaCode);
      const toBeSavedCanadaSimaCodes: Array<CanadaSimaCode> = [];

      for (let i = 0; i < (createList || []).length; i++) {
        const errorMsgPrefix = `Error on create list index ${i}: `;
        const createCanadaSimaCodeDto = createList[i];
        let newCanadaSimaCode = new CanadaSimaCode();
        newCanadaSimaCode.createdBy = this.request?.user || null;
        newCanadaSimaCode.lastEditedBy = this.request?.user || null;
        for (const [key, value] of Object.entries(createCanadaSimaCodeDto)) {
          if (value === undefined) continue;
          if (CANADA_SIMA_CODE_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
          newCanadaSimaCode[key] = value;
        }
        if (
          toBeSavedCanadaSimaCodes.some((c) => c.code === newCanadaSimaCode.code) ||
          (await canadaSimaCodeRepository.existsBy({
            code: newCanadaSimaCode.code
          }))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada SIMA Code with the same code already exists`
          );

        toBeSavedCanadaSimaCodes.push(newCanadaSimaCode);
      }

      for (let i = 0; i < (editList || []).length; i++) {
        const errorMsgPrefix = `Error on edit list index ${i}: `;
        const { id: codeId, ...editCanadaSimaCodeDto } = editList[i];
        const canadaSimaCode = await this.getCanadaSimaCodeById(codeId, queryRunner);
        if (!canadaSimaCode) throw new NotFoundException(`${errorMsgPrefix}Canada SIMA Code not found`);
        canadaSimaCode.lastEditedBy = this.request?.user || null;
        for (const [key, value] of Object.entries(editCanadaSimaCodeDto)) {
          if (value === undefined) continue;
          if (CANADA_SIMA_CODE_REQUIRED_KEYS.includes(key) && value === null)
            throw new BadRequestException(`${errorMsgPrefix}${convertFromCamelCase(key)} is required`);
          canadaSimaCode[key] = value;
        }
        if (
          toBeSavedCanadaSimaCodes.some(
            (c) => c.code === canadaSimaCode.code && c.id !== canadaSimaCode.id
          ) ||
          (await canadaSimaCodeRepository.existsBy({
            id: Not(
              In(
                toBeSavedCanadaSimaCodes
                  .filter((c) => typeof c.id === "number")
                  .map((c) => c.id)
                  .concat([codeId])
              )
            ),
            code: canadaSimaCode.code
          }))
        )
          throw new BadRequestException(
            `${errorMsgPrefix}Canada SIMA Code with the same code already exists`
          );
        toBeSavedCanadaSimaCodes.push(canadaSimaCode);
      }

      for (let i = 0; i < (deleteList || []).length; i++) {
        const codeId = deleteList[i];
        const errorMsgPrefix = `Error on delete list index ${i}: `;
        if (!(await canadaSimaCodeRepository.existsBy({ id: codeId })))
          throw new NotFoundException(`${errorMsgPrefix}Canada SIMA Code not found`);
        if (toBeSavedCanadaSimaCodes.some((c) => c.id === codeId))
          throw new BadRequestException(`${errorMsgPrefix}Canada SIMA Code is being edited`);
        if (
          await this.matchingRuleService.isMatchingRuleForSourceRecordExists(
            MatchingRuleSourceDatabaseTable.CANADA_SIMA_CODE,
            codeId,
            queryRunner
          )
        )
          throw new BadRequestException(`${errorMsgPrefix}Canada SIMA Code is being used in a matching rule`);
      }

      savedCanadaSimaCodeIds.push(
        ...(await canadaSimaCodeRepository.save(toBeSavedCanadaSimaCodes)).map((c) => c.id)
      );
      if (Array.isArray(deleteList) && deleteList.length > 0)
        await canadaSimaCodeRepository.delete({ id: In(deleteList) });

      await queryRunner.commitTransaction();
    } catch (error) {
      if (queryRunner.isTransactionActive) await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      if (!queryRunner.isReleased) queryRunner.release();
    }

    return {
      data: await this.canadaSimaCodeRepository.find({
        where: { id: In(savedCanadaSimaCodeIds) },
        relations: FIND_CANADA_SIMA_CODE_RELATIONS
      })
    };
  }
}
