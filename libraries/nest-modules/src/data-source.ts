import { config } from "dotenv";
import { DataSource } from "typeorm";
import { ENTITY_LIST, NodeEnv } from ".";

config();

export const NODE_ENV = process.env.NODE_ENV || NodeEnv.LOCAL;
export default new DataSource({
  type: "postgres",
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  database: process.env.DB_NAME,
  username: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  synchronize: false,
  logging: process.env.VERBOSE !== undefined,
  ssl: NODE_ENV !== NodeEnv.LOCAL,
  extra: NODE_ENV !== NodeEnv.LOCAL ? { ssl: { rejectUnauthorized: false } } : undefined,
  entities: ENTITY_LIST,
  migrations: [__dirname + "/migrations/*{.ts,.js}"]
});
