import { registerDecorator, ValidationArguments, ValidationOptions } from "class-validator";

export function IsNumberStringBetween(min: number, max: number, validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: "isNumberStringBetween",
      target: object.constructor,
      propertyName,
      constraints: [min, max],
      options: {
        message: `${propertyName} must be a number string between ${min} and ${max}`,
        ...validationOptions
      },
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [min, max] = args.constraints;
          if (typeof min !== "number" || typeof max !== "number" || min > max) return false;
          if (typeof value !== "string") return false;
          const parsedValue = Number(value);
          return (
            !Number.isNaN(parsedValue) &&
            Number.isFinite(parsedValue) &&
            parsedValue >= min &&
            parsedValue <= max
          );
        }
      }
    });
  };
}
