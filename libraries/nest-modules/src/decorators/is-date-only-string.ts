import { registerDecorator, ValidationArguments, ValidationOptions } from "class-validator";
import moment from "moment-timezone";

/**
 * Validate if the value is a date-only string in the format of YYYY-MM-DD and is a valid date.
 */
export function IsDateOnlyString(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: "isDateOnlyString",
      target: object.constructor,
      propertyName,
      constraints: [],
      options: {
        message: `${propertyName} must be a valid date only string`,
        ...validationOptions
      },
      validator: {
        validate(value: any, args: ValidationArguments) {
          // Check if value is a string
          if (typeof value !== "string") return false;
          const dateComponents = value.split("-");
          // Check if the date string has 3 parts (year, month, day)
          if (dateComponents.length !== 3) return false;
          // Check if all date components are valid integers
          const parsedDateComponents = dateComponents.map(Number);
          if (parsedDateComponents.some((c) => Number.isNaN(c) || !Number.isInteger(c))) return false;
          // Check if the month and day are valid
          const [year, month, day] = parsedDateComponents;
          if (month < 1 || month > 12) return false;
          if (day < 1 || day > 31) return false;
          // Check if the date is valid
          const dateObj = moment.utc([year, month - 1, day]);
          return dateObj.isValid();
        }
      }
    });
  };
}
