import { ForbiddenException, applyDecorators } from "@nestjs/common";
import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiForbiddenResponse,
  ApiNoContentResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiResponseOptions,
  ApiSecurity,
  ApiUnauthorizedResponse
} from "@nestjs/swagger";
import {
  BadRequestResponseDto,
  ForbiddenResponseDto,
  NotFoundResponseDto,
  UnauthorizedResponseDto
} from "../dto/exception.dto";

export function ApiAccessTokenAuthenticated() {
  return applyDecorators(
    ApiBearerAuth("accessToken"),
    ApiUnauthorizedResponse({ type: UnauthorizedResponseDto })
  );
}

export function ApiKeyAuthenticated() {
  return applyDecorators(ApiSecurity("apiKey"), ApiUnauthorizedResponse({ type: UnauthorizedResponseDto }));
}

export function ApiActionRestricted() {
  return applyDecorators(ApiForbiddenResponse({ type: ForbiddenResponseDto }));
}

export function ApiGetManyResponses(options?: ApiResponseOptions) {
  return applyDecorators(ApiOkResponse(options));
}

export function ApiGetByIdResponses(options?: ApiResponseOptions) {
  return applyDecorators(ApiOkResponse(options), ApiNotFoundResponse({ type: NotFoundResponseDto }));
}

export function ApiCreateResponses(options?: ApiResponseOptions) {
  return applyDecorators(ApiCreatedResponse(options), ApiBadRequestResponse({ type: BadRequestResponseDto }));
}

export function ApiEditResponses(options?: ApiResponseOptions) {
  return applyDecorators(
    ApiOkResponse(options),
    ApiNotFoundResponse({ type: NotFoundResponseDto }),
    ApiBadRequestResponse({ type: BadRequestResponseDto })
  );
}

export function ApiDeleteResponses(options?: ApiResponseOptions) {
  return applyDecorators(ApiNoContentResponse(options), ApiNotFoundResponse({ type: NotFoundResponseDto }));
}

export function ApiCreateSubrecordResponses(options?: ApiResponseOptions) {
  return applyDecorators(
    ApiCreatedResponse(options),
    ApiNotFoundResponse({ type: NotFoundResponseDto }),
    ApiBadRequestResponse({ type: BadRequestResponseDto })
  );
}
