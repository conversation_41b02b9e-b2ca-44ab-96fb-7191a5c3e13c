import { registerDecorator, ValidationArguments, ValidationOptions } from "class-validator";
import { UNIT_OF_MEASURE_TYPE_MAP, UnitOfMeasureType } from "../types";

export function IsValidUnitOfMeasure(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: "isValidUnitOfMeasure",
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const { type } = args.object as { type: UnitOfMeasureType };
          const validTypes = UNIT_OF_MEASURE_TYPE_MAP[type] ?? [];
          return validTypes.includes(value);
        }
      }
    });
  };
}
