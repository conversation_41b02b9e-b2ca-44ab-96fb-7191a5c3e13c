import { registerDecorator, ValidationArguments, ValidationOptions } from "class-validator";

export function IsTrue(conditionalFunc: (value: any) => boolean, validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: "isTrue",
      target: object.constructor,
      propertyName,
      constraints: [conditionalFunc],
      options: validationOptions,
      validator: {
        validate(value: any, validationArguments?: ValidationArguments) {
          const [conditionalFunc] = validationArguments.constraints;
          return typeof conditionalFunc === "function" ? conditionalFunc(value) || false : false;
        },
        defaultMessage(validationArguments?: ValidationArguments) {
          return `Condition for ${validationArguments.property} is not true`;
        }
      }
    });
  };
}
