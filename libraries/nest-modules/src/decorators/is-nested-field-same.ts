import { registerDecorator, ValidationArguments, ValidationOptions } from "class-validator";

export interface IsNestedFieldSameOptions {
  /** The name of the field in the nested object */
  nestedFieldName: string;
  /** The name of the field in the parent object */
  parentFieldName: string;
  /** Whether to allow the nested field or parent field to be null. If true, the validation will pass even if one of the fields is null. */
  allowNull?: boolean;
  /** Whether to allow the nested field or parent field to be undefined. If true, the validation will pass even if one of the fields is undefined. */
  allowUndefined?: boolean;
}

/**
 * Use this decorator on a nested field. Validate if the field in the nested object is the same as the field in the parent object.
 * @param options - Options required for the validation
 * @param validationOptions - Validation options
 */
export function IsNestedFieldSame(options: IsNestedFieldSameOptions, validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: "isNestedFieldSame",
      target: object.constructor,
      propertyName,
      constraints: [options],
      options: {
        message: `${propertyName}.${options.nestedFieldName} has different value with ${options.parentFieldName}`,
        ...validationOptions
      },
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [options] = args.constraints as [IsNestedFieldSameOptions];
          // If allow null, return true when either parent or nested field is null
          if (
            options.allowNull &&
            (args.object[options.parentFieldName] === null ||
              (typeof value === "object" && value[options.nestedFieldName] === null))
          )
            return true;

          // If allow undefined, return true when either parent or nested field is undefined
          if (
            options.allowUndefined &&
            (args.object[options.parentFieldName] === undefined ||
              value === undefined ||
              value[options.nestedFieldName] === undefined)
          )
            return true;

          // When both allow null and allow undefined are false, return true only when both parent and nested field are of the same type and value
          if (!options.allowNull && !options.allowUndefined)
            return (
              typeof value === "object" &&
              typeof args.object[options.parentFieldName] === typeof value[options.nestedFieldName] &&
              args.object[options.parentFieldName] === value[options.nestedFieldName]
            );
          return false;
        }
      }
    });
  };
}
