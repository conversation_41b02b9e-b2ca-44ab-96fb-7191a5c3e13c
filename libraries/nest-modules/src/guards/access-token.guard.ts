import { CanActivate, ExecutionContext, Inject, Injectable, UnauthorizedException } from "@nestjs/common";
import { AuthService } from "../auth/auth.service";
import { UserPermission } from "../types";
import { OrganizationService } from "../user/services/organization.service";
import { UserService } from "../user/services/user.service";

@Injectable()
export class AccessTokenGuard implements CanActivate {
  constructor(
    @Inject(AuthService)
    private readonly authService: AuthService,
    @Inject(UserService)
    private readonly userService: UserService,
    @Inject(OrganizationService)
    private readonly organizationService: OrganizationService
  ) {}

  async canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();
    const [bearer, token] = request.headers.authorization?.split(" ") ?? [];
    if (bearer !== "Bearer") throw new UnauthorizedException("Token type not bearer");

    let payload: { uid: number } | null = null;
    try {
      payload = await this.authService.validateAccessToken(token);
    } catch (error) {
      throw new UnauthorizedException("Invalid access token");
    }
    // try{
    //     payload = this.jwtService.verify<{uid: number}>(token, {
    //         secret: accessTokenSecret,
    //         algorithms: ['HS256']
    //     })
    // }catch(error){
    //     throw new UnauthorizedException('Invalid access token');
    // }

    const user = await this.userService.getUserById(payload.uid, true);
    if (!user) throw new UnauthorizedException("User not found");

    request.user = user;

    const organizationId = request.headers["claro-organization-id"];
    // Backoffice admin act as a particular organization
    if (user.permission === UserPermission.BACKOFFICE_ADMIN && organizationId) {
      const organization = await this.organizationService.getOrganizationById(organizationId, true);

      if (organization) {
        request.user.organization = organization;
        if (request.method === "GET") request.query.organizationId = Number(organizationId);
      }
    }

    return true;
  }
}
