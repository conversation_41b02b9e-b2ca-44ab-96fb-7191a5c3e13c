import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Country, State } from "../entities";
import { CountryController } from "./country.controller";
import { CountryService } from "./country.service";
import { StateController } from "./state.controller";
import { StateService } from "./state.service";

@Module({
  imports: [TypeOrmModule.forFeature([Country, State])],
  providers: [CountryService, StateService],
  controllers: [CountryController, StateController],
  exports: [CountryService, StateService]
})
export class CountryModule {}
