import {
  BadRequestException,
  ForbiddenException,
  Inject,
  Injectable,
  NotFoundException,
  Scope
} from "@nestjs/common";
import { REQUEST } from "@nestjs/core";
import { InjectRepository } from "@nestjs/typeorm";
import { Not, QueryRunner, Repository } from "typeorm";
import { CreateCountryDto, EditCountryDto, GetCountriesDto, GetCountriesResponseDto } from "../dto";
import { Country } from "../entities";
import { getFindOptions } from "../helper-functions";
import { AuthenticatedRequest, CountryColumn, FIND_COUNTRY_RELATIONS, UserPermission } from "../types";

@Injectable({ scope: Scope.REQUEST })
export class CountryService {
  constructor(
    @InjectRepository(Country)
    private readonly countryRepository: Repository<Country>,
    @Inject(REQUEST)
    private readonly request: AuthenticatedRequest
  ) {}

  async getCountries(getCountriesDto: GetCountriesDto): Promise<GetCountriesResponseDto> {
    const { where, order, skip, take } = getFindOptions(getCountriesDto, [], [], CountryColumn.id);
    const [countries, total] = await this.countryRepository.findAndCount({
      where,
      relations: FIND_COUNTRY_RELATIONS,
      order,
      skip,
      take
    });

    return {
      countries,
      total,
      skip,
      limit: take
    };
  }

  async getCountryById(countryId: number, queryRunner?: QueryRunner) {
    return await (queryRunner ? queryRunner.manager.getRepository(Country) : this.countryRepository).findOne({
      where: { id: countryId },
      relations: FIND_COUNTRY_RELATIONS
    });
  }

  async createCountry(createCountryDto: CreateCountryDto) {
    // TODO: Add action rule
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user is not backoffice admin");
    const { name, alpha3, alpha2 } = createCountryDto;
    if (await this.countryRepository.existsBy({ name }))
      throw new BadRequestException("Country with the same name already exists");
    if (await this.countryRepository.existsBy({ alpha3 }))
      throw new BadRequestException("Country with the same alpha3 already exists");
    if (await this.countryRepository.existsBy({ alpha2 }))
      throw new BadRequestException("Country with the same alpha2 already exists");

    let newCountry = new Country();
    newCountry.name = name;
    newCountry.alpha3 = alpha3;
    newCountry.alpha2 = alpha2;
    newCountry.createdBy = this.request?.user;
    newCountry.lastEditedBy = this.request?.user;
    newCountry = await this.countryRepository.save(newCountry);

    return await this.getCountryById(newCountry.id);
  }

  async editCountry(countryId: number, editCountryDto: EditCountryDto) {
    // TODO: Add action rule
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user is not backoffice admin");
    const { name, alpha3, alpha2 } = editCountryDto;
    const country = await this.getCountryById(countryId);
    if (!country) throw new NotFoundException("Country not found");

    if (typeof name === "string" && name.length > 0) {
      if (await this.countryRepository.existsBy({ id: Not(countryId), name }))
        throw new BadRequestException("Country with the same name already exists");
      country.name = name;
    }
    if (typeof alpha3 === "string" && alpha3.length > 0) {
      if (await this.countryRepository.existsBy({ id: Not(countryId), alpha3 }))
        throw new BadRequestException("Country with the same alpha3 already exists");
      country.alpha3 = alpha3;
    }
    if (typeof alpha2 === "string" && alpha2.length > 0) {
      if (await this.countryRepository.existsBy({ id: Not(countryId), alpha2 }))
        throw new BadRequestException("Country with the same alpha2 already exists");
      country.alpha2 = alpha2;
    }
    country.lastEditedBy = this.request?.user;
    await this.countryRepository.save(country);

    return await this.getCountryById(countryId);
  }

  async deleteCountry(countryId: number) {
    // TODO: Add action rule
    if (this.request?.user?.permission !== UserPermission.BACKOFFICE_ADMIN)
      throw new ForbiddenException("Current user is not backoffice admin");
    if (!(await this.countryRepository.existsBy({ id: countryId })))
      throw new NotFoundException("Country not found");
    await this.countryRepository.delete({ id: countryId });
    return;
  }
}
