import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  NotFoundException,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards
} from "@nestjs/common";
import { ApiForbiddenResponse, ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import { CountryService } from "./country.service";
import {
  ApiAccessTokenAuthenticated,
  ApiGetManyResponses,
  ApiGetByIdResponses,
  ApiCreateResponses,
  ApiEditResponses,
  ApiDeleteResponses
} from "../decorators";
import {
  GetCountriesResponseDto,
  GetCountriesDto,
  ForbiddenResponseDto,
  CreateCountryDto,
  EditCountryDto
} from "../dto";
import { Country } from "../entities";
import { AccessTokenGuard } from "../guards";

@ApiAccessTokenAuthenticated()
@ApiTags("Country API")
@UseGuards(AccessTokenGuard)
@Controller("countries")
export class CountryController {
  constructor(private readonly countryService: CountryService) {}

  @ApiOperation({ summary: "Get Countries" })
  @ApiGetManyResponses({ type: GetCountriesResponseDto })
  @Get()
  async getCountries(@Query() getCountriesDto: GetCountriesDto) {
    return await this.countryService.getCountries(getCountriesDto);
  }

  @ApiOperation({ summary: "Get Country" })
  @ApiParam({ name: "id", type: "integer", description: "Country ID" })
  @ApiGetByIdResponses({ type: Country })
  @Get(":id")
  async getCountryById(@Param("id", ParseIntPipe) id: number) {
    const country = await this.countryService.getCountryById(id);
    if (!country) throw new NotFoundException("Country not found");
    return country;
  }

  @ApiOperation({ summary: "Create Country" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiCreateResponses({ type: Country })
  @Post()
  async createCountry(@Body() createCountryDto: CreateCountryDto) {
    return await this.countryService.createCountry(createCountryDto);
  }

  @ApiOperation({ summary: "Edit Country" })
  @ApiParam({ name: "id", type: "integer", description: "Country ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiEditResponses({ type: Country })
  @Put(":id")
  async editCountry(@Param("id", ParseIntPipe) id: number, @Body() editCountryDto: EditCountryDto) {
    return await this.countryService.editCountry(id, editCountryDto);
  }

  @ApiOperation({ summary: "Delete Country" })
  @ApiParam({ name: "id", type: "integer", description: "Country ID" })
  @ApiForbiddenResponse({ type: ForbiddenResponseDto })
  @ApiDeleteResponses()
  @Delete(":id")
  @HttpCode(204)
  async deleteCountry(@Param("id", ParseIntPipe) id: number) {
    await this.countryService.deleteCountry(id);
    return;
  }
}
