import { Controller, Get, NotFoundException, Param, ParseIntPipe, Query, UseGuards } from "@nestjs/common";
import { ApiOperation, ApiParam, ApiTags } from "@nestjs/swagger";
import { CountryService } from "./country.service";
import { StateService } from "./state.service";
import { ApiAccessTokenAuthenticated, ApiGetManyResponses } from "../decorators";
import { GetStatesResponseDto, GetStatesDto } from "../dto";
import { AccessTokenGuard } from "../guards";

@ApiAccessTokenAuthenticated()
@ApiTags("State API")
@UseGuards(AccessTokenGuard)
@Controller("countries/:countryId")
export class StateController {
  constructor(
    private readonly stateService: StateService,
    private readonly countryService: CountryService
  ) {}

  /**
   * Get all states by country id
   *
   * @param countryId - Country id
   * @param getStatesDto - Get states dto
   * @returns States
   */
  @ApiOperation({ summary: "Get States" })
  @ApiParam({ name: "countryId", type: "integer", description: "Country ID" })
  @ApiGetManyResponses({ type: GetStatesResponseDto })
  @Get("states")
  async getStates(@Param("countryId", ParseIntPipe) countryId: number, @Query() getStatesDto: GetStatesDto) {
    const country = await this.countryService.getCountryById(countryId);
    if (!country) {
      throw new NotFoundException("Country not found");
    }
    return this.stateService.getStatesByCountryId(countryId, getStatesDto);
  }
}
