import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { GetStatesDto } from "../dto";
import { State } from "../entities";
import { getFindOptions } from "../helper-functions";
import { StateColumn } from "../types";

@Injectable()
export class StateService {
  constructor(
    @InjectRepository(State)
    private readonly stateRepository: Repository<State>
  ) {}

  /**
   * Get states by country id
   *
   * @param countryId - Country id
   * @param getStatesDto - Get states dto
   * @returns States
   */
  async getStatesByCountryId(countryId: number, getStatesDto: GetStatesDto) {
    const { where, order, skip, take } = getFindOptions(getStatesDto, [], [], StateColumn.id);

    const [states, total] = await this.stateRepository.findAndCount({
      where: { country: { id: countryId }, ...where },
      order,
      skip,
      take
    });

    return { states, total, skip, limit: take };
  }

  /**
   * Get state by id
   *
   * @param stateId - State id
   * @returns State
   */
  async getStateById(stateId: number) {
    const state = await this.stateRepository.findOne({ where: { id: stateId } });
    if (!state) throw new NotFoundException("State not found");
    return state;
  }
}
