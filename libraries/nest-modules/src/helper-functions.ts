import { createHash } from "crypto";
import { X2jOptions, XMLBuilder, XMLParser } from "fast-xml-parser";
import {
  Between,
  FindOptionsOrder,
  FindOptionsRelations,
  FindOptionsWhere,
  LessThanOrEqual,
  MoreThanOrEqual,
  Raw,
  SelectQueryBuilder
} from "typeorm";
import { GetManyDto } from "./dto/base.dto";
import { SortOrder } from "./types/base.types";

export type GetManyDtoWithFilters = GetManyDto & { sortBy?: string } & Record<string, any>;

/**
 * Get TypeORM Find Options
 * @param getManyDto - Get many DTO with filters
 * @param enumKeys - Keys of enum fields for the entity
 * @param nonIdKeys - Keys of the fields that ends with "Id" but are not relational fields
 * @param defaultSortBy - Default field to sort by if not provided in DTO
 * @returns Find Options
 */
export function getFindOptions<T>(
  getManyDto: GetManyDtoWithFilters,
  enumKeys: Array<string>,
  nonIdKeys: Array<string>,
  defaultSortBy = "id"
): {
  where: FindOptionsWhere<T>;
  order: FindOptionsOrder<T>;
  skip: number;
  take: number;
} {
  const where: FindOptionsWhere<T> = {};
  const { skip, limit, sortOrder, sortBy, ...filters } = getManyDto;
  for (const [key, value] of Object.entries(filters)) {
    if ([null, undefined].includes(value)) continue;

    if (key.endsWith("Id") && !nonIdKeys.includes(key)) {
      const propertyName = key.replace(/Id$/, "");
      where[propertyName] = { id: value };
    } else if (value instanceof Date) {
      const propertyName = key.replace(/(From|To)$/, "");
      if (where[propertyName]) continue;
      const fromDate = key.endsWith("From") ? value : (filters[propertyName + "From"] as Date);
      const toDate = key.endsWith("To") ? value : (filters[propertyName + "To"] as Date);
      where[propertyName] =
        fromDate && toDate
          ? Between(fromDate.toISOString(), toDate.toISOString())
          : fromDate
            ? MoreThanOrEqual(fromDate.toISOString())
            : LessThanOrEqual(toDate.toISOString());
    } else if (enumKeys.includes(key) || ["boolean", "number"].includes(typeof value)) where[key] = value;
    else
      where[key] = Raw(
        (alias) =>
          `LOWER(${alias}) LIKE LOWER('%' || replace(replace(replace(:${key}, '\\', '\\\\'), '%', '\\%'), '_', '\\_') || '%')`,
        { [key]: value }
      );
  }

  const order: FindOptionsOrder<T> = {};
  if (sortBy) {
    if (sortBy.endsWith("Id") && !nonIdKeys.includes(sortBy))
      order[sortBy.replace(/Id$/, "")] = { id: sortOrder || SortOrder.ASC };
    else order[sortBy] = sortOrder || SortOrder.ASC;
  } else order[defaultSortBy] = sortOrder || SortOrder.ASC;

  return {
    where,
    order,
    skip: typeof skip === "number" ? skip : 0,
    take: typeof limit === "number" ? limit : 10
  };
}

/**
 * Get Query Conditions. This function is intented to be used with TypeORM Query Builder.
 * @param getManyDto - Get many DTO with filters
 * @param enumKeys - Keys of enum fields for the entity
 * @param nonIdKeys - Keys of the fields that ends with "Id" but are not relational fields
 * @param defaultSortBy - Default field to sort by if not provided in DTO
 * @param alias - Alias of the table
 * @returns Query Conditions
 */
export function getQueryConditions(
  getManyDto: GetManyDtoWithFilters,
  enumKeys: Array<string>,
  nonIdKeys: Array<string>,
  defaultSortBy = "id",
  alias = "entity"
): {
  conditions: Array<string>;
  values: Record<string, any>;
  sortBy: string;
  sortOrder: "ASC" | "DESC";
  skip: number;
  take: number;
} {
  const whereStrings: Array<string> = [];
  const whereValues: Record<string, any> = {};
  const { skip, limit, sortOrder, sortBy, ...filters } = getManyDto;
  for (const [key, value] of Object.entries(filters)) {
    if (value === undefined) continue;

    if (value instanceof Date) {
      const propertyName = key.replace(/(From|To)$/, "");
      whereStrings.push(
        `${alias}."${propertyName}" ${key.endsWith("From") ? ">=" : key.endsWith("To") ? "<=" : "="} :${key}`
      );
      whereValues[key] = value.toISOString();
    } else if (enumKeys.includes(key) || ["boolean", "number"].includes(typeof value)) {
      whereStrings.push(`${alias}."${key}" = :${key}`);
      whereValues[key] = value;
    } else if (value === null) {
      whereStrings.push(`${alias}."${key}" IS NULL`);
    } else {
      whereStrings.push(
        `LOWER(${alias}."${key}") LIKE LOWER('%' || replace(replace(replace(:${key}, '\\', '\\\\'), '%', '\\%'), '_', '\\_') || '%')`
      );
      whereValues[key] = value;
    }
  }

  return {
    conditions: whereStrings,
    values: whereValues,
    sortBy: !nonIdKeys.includes(sortBy || defaultSortBy)
      ? (sortBy || defaultSortBy).replace(/Id$/, "")
      : sortBy || defaultSortBy,
    sortOrder: (sortOrder || SortOrder.ASC).toUpperCase() as "ASC" | "DESC",
    skip: typeof skip === "number" ? skip : 0,
    take: typeof limit === "number" ? limit : 10
  };
}

/**
 * Recursively left join relations to TypeORM Query Builder
 * @param queryBuilder - TypeORM Query Builder
 * @param alias - Alias of the table
 * @param relations - Relations to join
 * @returns TypeORM Query Builder with left joins
 */
export function queryBuilderRecursiveLeftJoin<E>(
  queryBuilder: SelectQueryBuilder<E>,
  alias: string,
  relations: FindOptionsRelations<E>
): SelectQueryBuilder<E> {
  for (const [key, value] of Object.entries(relations)) {
    queryBuilder = queryBuilder.leftJoinAndSelect(`${alias}.${key}`, `${alias}_${key}`);
    if (typeof value === "object") {
      queryBuilder = queryBuilderRecursiveLeftJoin(queryBuilder, `${alias}_${key}`, value);
    }
  }
  return queryBuilder;
}

export function getHashPassword(password: string, salt: string) {
  return createHash("sha256").update(`pwd:${password};slt:${salt}`).digest("hex");
}

export function convertFromCamelCase(s: string) {
  return s.replace(/([A-Z])/g, " $1").replace(/^./, (c) => c.toUpperCase());
}

export function toUpperSnakeCase(s: string) {
  return s.toUpperCase().replaceAll(/\s+/g, "_");
}

export function toXml<T>(json: T) {
  const xml = new XMLBuilder({
    ignoreAttributes: false,
    textNodeName: "TextValue",
    attributeNamePrefix: "$"
  }).build(json);
  return xml as string;
}

export function parseXml<T>(
  xml: string,
  withAttributes = false,
  tagValueProcessor?: (
    tagName: string,
    tagValue: string,
    jPath: string,
    hasAttributes: boolean,
    isLeafNode: boolean
  ) => unknown
) {
  function convertEmptyStringToNull(currentValue: any) {
    if (typeof currentValue === "string" && currentValue === "") return null;
    else if (typeof currentValue === "object") {
      if (Array.isArray(currentValue)) return currentValue.map(convertEmptyStringToNull);
      else
        return Object.entries(currentValue).reduce((obj, [key, value]) => {
          obj[key] = convertEmptyStringToNull(value);
          return obj;
        }, {});
    } else return currentValue;
  }

  const options: X2jOptions = {
    removeNSPrefix: true,
    ignoreAttributes: !withAttributes,
    textNodeName: "TextValue",
    attributeNamePrefix: "$"
  };
  if (tagValueProcessor) options.tagValueProcessor = tagValueProcessor;

  const json = new XMLParser(options).parse(xml);
  return convertEmptyStringToNull(json) as T;
}
