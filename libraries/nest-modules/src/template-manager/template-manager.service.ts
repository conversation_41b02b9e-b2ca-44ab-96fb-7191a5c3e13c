import { Inject, Injectable, Logger } from "@nestjs/common";
import nunjucks from "nunjucks";
import {
  DEFAULT_NUNJUCKS_CONFIG,
  TEMPLATE_MANAGER_MODULE_OPTIONS,
  TemplateManagerOptions
} from "../types/template-manager.types";

@Injectable()
export class TemplateManagerService {
  private readonly logger = new Logger(TemplateManagerService.name);
  private nunjucks: nunjucks.Environment;

  constructor(
    @Inject(TEMPLATE_MANAGER_MODULE_OPTIONS)
    private readonly options?: TemplateManagerOptions
  ) {
    const templatesPath = options?.templatesPath ?? __dirname;
    this.logger.debug(`Templates path: ${templatesPath}`);
    this.nunjucks = new nunjucks.Environment(
      new nunjucks.FileSystemLoader(templatesPath),
      options?.nunjucksConfig || DEFAULT_NUNJUCKS_CONFIG
    );
  }

  renderTemplate(templateName: string, data: Record<string, any>): string {
    try {
      return this.nunjucks.render(`${templateName}.njk`, data);
    } catch (error) {
      this.logger.error(
        `Error rendering template ${templateName}: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  renderString(templateString: string, data: Record<string, any>): string {
    try {
      return this.nunjucks.renderString(templateString, data);
    } catch (error) {
      this.logger.error(`Error rendering string: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }
}
