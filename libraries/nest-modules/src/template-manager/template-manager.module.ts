import { DynamicModule, Module } from "@nestjs/common";
import { TEMPLATE_MANAGER_MODULE_OPTIONS, TemplateManagerOptions } from "../types/template-manager.types";
import { TemplateManagerService } from "./template-manager.service";

@Module({})
export class TemplateManagerModule {
  static forRoot(): DynamicModule {
    return {
      module: TemplateManagerModule
    };
  }

  static forFeature(options?: TemplateManagerOptions): DynamicModule {
    return {
      module: TemplateManagerModule,
      imports: [],
      providers: [
        {
          provide: TEMPLATE_MANAGER_MODULE_OPTIONS,
          useValue: options
        },
        TemplateManagerService
      ],
      exports: [TemplateManagerService]
    };
  }
}
