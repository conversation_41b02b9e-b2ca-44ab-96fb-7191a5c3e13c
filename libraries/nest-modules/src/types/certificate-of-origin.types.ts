import { FindOptionsRelations } from "typeorm";
import { CertificateOfOrigin } from "../entities";

export enum CertificateOfOriginColumn {
  id = "id",
  validFrom = "validFrom",
  validTo = "validTo",
  invoiceNumber = "invoiceNumber",

  countryOfOriginId = "countryOfOriginId",

  exporterId = "exporterId",
  producerId = "producerId",
  importerId = "importerId",

  createDate = "createDate",
  lastEditDate = "lastEditDate",

  organizationId = "organizationId"
}

export const FIND_CERTIFICATE_OF_ORIGIN_RELATIONS: FindOptionsRelations<CertificateOfOrigin> = {
  exporter: true,
  producer: true,
  importer: true,
  countryOfOrigin: true,
  organization: true
};

export const CERTIFICATE_OF_ORIGIN_REQUIRED_KEYS = ["validFrom", "validTo"];
