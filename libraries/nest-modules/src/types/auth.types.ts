import { Request } from "express";
import { User } from "../entities/user.entity";

export enum LoginMethod {
  GOOGLE_SSO = "google-sso",
  EMAIL = "email"
}

export interface AuthModuleOptions {
  accessTokenSecret: string;
  refreshTokenSecret: string;
  accessTokenExpiresInSec: number;
  refreshTokenExpiresInSec: number;
  refreshTokenGracePeriodSec: number;
  googleOAuthClientId: string;
  allowedLoginMethods: Array<LoginMethod>;
}

export const AUTH_MODULE_OPTIONS = "AUTH_MODULE_OPTIONS";

export type AuthenticatedRequest = Request & {
  user: User | null;
};

export interface DecodedIdToken {
  name?: string;
  email: string;
  googleUserId: string;
  domain: string;
}
