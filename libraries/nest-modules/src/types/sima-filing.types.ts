import { FindOptionsRelations } from "typeorm";
import { SimaFiling } from "../entities";

export enum SimaFilingColumn {
  id = "id",
  subjectCode = "subjectCode",
  incoterms = "incoterms",
  security = "security",
  createDate = "createDate",
  createById = "createById",
  lastEditDate = "lastEditDate",
  lastEditById = "lastEditById",
  organizationId = "organizationId",
  measureInForceId = "measureInForceId",
  simaCodeId = "simaCodeId"
}

export const SIMA_FILING_REQUIRED_KEYS = ["subjectCode", "security", "measureInForce"];

export const SIMA_FILING_ENUM_KEYS = ["subjectCode", "incoterms"];

export const FIND_SIMA_FILING_RELATIONS: FindOptionsRelations<SimaFiling> = {
  measureInForce: true,
  simaCode: true,
  organization: true,
  createdBy: true,
  lastEditedBy: true
};
