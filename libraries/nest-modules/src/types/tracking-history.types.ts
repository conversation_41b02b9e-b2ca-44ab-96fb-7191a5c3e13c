import { FindOptionsRelations } from "typeorm";
import { TrackingHistory } from "../entities";

export enum TrackingHistoryColumn {
  id = "id",
  party = "party",
  timestamp = "timestamp",
  result = "result",
  shipmentId = "shipmentId",
  organizationId = "organizationId"
}

export const FIND_TRACKING_HISTORY_RELATIONS: FindOptionsRelations<TrackingHistory> = {
  shipment: true,
  organization: true
};

export const TRACKING_HISTORY_REQUIRED_KEYS = ["party", "timestamp", "result", "shipment", "organization"];
