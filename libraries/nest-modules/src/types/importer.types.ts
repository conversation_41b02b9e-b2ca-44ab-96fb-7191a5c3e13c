import { FindOptionsRelations } from "typeorm";
import { Importer } from "../entities";

export enum ImporterStatus {
  PENDING_POA = "pending-poa",
  PENDING_VERIFICATION = "pending-verification",
  REJECTED = "rejected",
  ACTIVE = "active",
  DISABLED = "disabled"
}

export enum ImporterColumn {
  id = "id",
  status = "status",
  companyName = "companyName",
  businessNumber = "businessNumber",
  address = "address",
  city = "city",
  postalCode = "postalCode",
  state = "state",
  phoneNumber = "phoneNumber",
  fax = "fax",
  email = "email",
  officerNameAndTitle = "officerNameAndTitle",
  docusignEnvelopeId = "docusignEnvelopeId",
  rejectReason = "rejectReason",
  candataCustomerNumber = "candataCustomerNumber",
  receiveEmail = "receiveEmail",
  whitelistEmails = "whitelistEmails",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  countryId = "countryId",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export const FIND_IMPORTER_RELATIONS: FindOptionsRelations<Importer> = {
  country: true,
  organization: true,
  createdBy: true,
  lastEditedBy: true
};

export const IMPORTER_ENUM_KEYS = ["status"];
export const IMPORTER_REQUIRED_KEYS = [
  "status",
  "companyName",
  "businessNumber",
  "address",
  "city",
  "postalCode",
  "state",
  "phoneNumber",
  "email",
  "officerNameAndTitle",
  "country",
  "organization"
];
