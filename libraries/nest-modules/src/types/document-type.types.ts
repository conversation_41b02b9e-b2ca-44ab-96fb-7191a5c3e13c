import { FindOptionsRelations } from "typeorm";
import { DocumentType } from "../entities";

export enum DocumentTypeColumn {
  id = "id",
  name = "name",
  promptTemplate = "promptTemplate",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export const FIND_DOCUMENT_TYPE_RELATIONS: FindOptionsRelations<DocumentType> = {
  createdBy: true,
  lastEditedBy: true,
  fields: true
};

export const DOCUMENT_TYPE_ENUM_KEYS: string[] = [];

export const DOCUMENT_TYPE_REQUIRED_KEYS = ["name", "promptTemplate"];

export const DOCUMENT_TYPE_MODULE_OPTIONS = "DOCUMENT_TYPE_MODULE_OPTIONS";
