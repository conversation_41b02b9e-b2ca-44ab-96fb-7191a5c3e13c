import { FindOptionsRelations } from "typeorm";
import { Document } from "../entities";

export enum DocumentColumn {
  id = "id",
  name = "name",
  startPage = "startPage",
  endPage = "endPage",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  fileId = "fileId",
  documentTypeId = "documentTypeId",
  createdById = "createdById",
  lastEditedById = "lastEditedById",
  status = "status",
  shipmentId = "shipmentId",
  organizationId = "organizationId",
  aggregationId = "aggregationId",
  referenceId = "referenceId"
}

export enum DocumentStatus {
  // default
  PENDING = "pending",
  EXTRACTING = "extracting",
  EXTRACTED = "extracted",

  // validating
  VALIDATING = "validating",

  // aggregation
  AGGREGATED = "aggregated",

  // error
  EXTRACTION_FAILED = "extraction_failed",
  AGGREGATION_FAILED = "aggregation_failed",

  // shipment mismatch
  SHIPMENT_MISMATCH = "shipment_mismatch"
}

export const FIND_DOCUMENT_RELATIONS: FindOptionsRelations<Document> = {
  file: true,
  documentType: true,
  createdBy: true,
  lastEditedBy: true,
  fields: true,
  shipment: true,
  organization: true,
  aggregation: true,
  reference: {
    file: true
  },
  referencedBy: {
    file: true
  }
};

export const DOCUMENT_ENUM_KEYS: string[] = ["status"];

export const DOCUMENT_REQUIRED_KEYS = ["name", "file", "organization"];
