import { FindOptionsRelations } from "typeorm";
import { CanadaOgd } from "../entities";

export enum CanadaGovernmentAgency {
  CFIA = "cfia",
  CNSC = "cnsc",
  DFO = "dfo",
  ECCC = "eccc",
  GAC = "gac",
  HC = "hc",
  PHAC = "phac",
  NRCAN = "nrcan",
  TC = "tc"
}

export enum CanadaOgdColumn {
  id = "id",
  agency = "agency",
  program = "program",
  commodityType = "commodityType",
  createdById = "createdById",
  createdDate = "createdDate",
  lastEditedById = "lastEditedById",
  lastEditedDate = "lastEditedDate"
}

export const FIND_CANADA_OGD_RELATIONS: FindOptionsRelations<CanadaOgd> = {
  createdBy: true,
  lastEditedBy: true
};

export const CANADA_OGD_ENUM_KEYS = ["agency"];

export const CANADA_OGD_REQUIRED_KEYS = ["agency", "program"];

export const CANADA_OGD_MODULE_OPTIONS = "CANADA_OGD_MODULE_OPTIONS";

export enum OGD_HEALTH_CANADA_PROGRAM {
  ACTIVE_PHARMACEUTICAL_INGREDIENTS = "Active Pharmaceutical Ingredients",
  BLOOD_AND_BLOOD_COMPONENTS = "Blood and Blood Components",
  CELL_TISSUES_AND_ORGAN = "Cell, Tissues and Organs",
  CONSUMER_PRODUCT_SAFETY = "Consumer Product Safety",
  DONOR_SEMEN_AND_OVA_PROGRAM = "Donor Semen and Ova Program",
  HUMAN_DRUGS = "Human Drugs",
  OFFICE_OF_CONTROLLED_SUBSTANCES = "Office of Controlled substances",
  MEDICAL_DEVICES = "Medical Devices",
  NATURAL_HEALTH_PRODUCTS = "Natural Health Products",
  PESTICIDES_PEST_MANAGEMENT_REGULATORY_AGENCY = "Pesticides (Pest Management Regulatory Agency)",
  RADIATION_EMITTING_DEVICES = "Radiation Emitting Devices",
  VETERINARY_DRUGS = "Veterinary Drugs"
}

export enum OGD_ECCC_PROGRAM {
  WASTE_MANAGEMENT_AND_REDUCTION_DIVISION = "Waste Management and Reduction Division",
  OZONE_DEPLETING_SUBSTANCES = "Ozone Depleting Sustances",

  WILDLIFE_ENFORCEMENT = "Wildlife Enforcement",
  TRANSPORT_PROGRAM = "Transport Program",
  WILDLIFE_ENFORCEMENT_SOMETIMES_REGULATED = "Wildlife Enforcement Sometimes Regulated",

  // needs to check data integrity
  WILDLIFE_ENFORCEMENT_A = "Wildlife Enforcement A",
  VEHICLES_AND_ENGINE_EMISSIONS_PROGRAM = "Vehicles and Engine Emissions Program"
}

export enum OGD_GAC_PROGRAM {
  AGRICULTURE = "Agriculture",
  CLOTHING_AND_TEXTILES = "Clothing and Textiles",
  STEEL = "Steel",
  ALUMINUM_IMPORT_MONITORING = "Aluminum Import Monitoring Program"
}

export enum OGD_GAC_COMMODITY_TYPE {
  BARLEY_PRODUCTS_HIGH_TARIFF = "Barley Products - High Tariff",
  ALUMINUM = "Aluminum",
  BARLEY_LOW_TARIFF = "Barley - Low Tariff",
  CARBON_STEEL = "Carbon Steel",
  BARLEY_PRODUCTS_LOW_TARIFF = "Barley Products - Low Tariff",
  WHEAT_PRODUCTS_HIGH_TARIFF = "Wheat Products - High Tariff",
  WHEAT_HIGH_TARIFF = "Wheat - High Tariff",
  SPECIALTY_STEEL = "Specialty Steel",
  BEEF_AND_VEAL_LOW_TARIFF = "Beef and Veal - Low Tariff",
  AGRICULTURE_PRODUCTS_LOW_TARIFF = "Agriculture Products - Low Tariff",
  CLOTHING_AND_TEXTILES = "Clothing and Textiles",
  WHEAT_PRODUCTS_LOW_TARIFF = "Wheat Products- Low Tariff",
  WHEAT_LOW_TARIFF = "Wheat - Low Tariff",
  BARLEY_HIGH_TARIFF = "Barley - High Tariff",
  AGRICULTURE_PRODUCTS_HIGH_TARIFF = "Agriculture Products - High Tariff",
  BEEF_AND_VEAL_HIGH_TARIFF = "Beef and Veal - High Tariff",

  // needs to check data integrity
  BEEF_VEAL_HIGH_TARIFF = "Beef & Veal - High Tariff",
  BEEF_VEAL_LOW_TARIFF = "Beef & Veal - Low Tariff"
}

export enum OGD_NRCAN_PROGRAM {
  OFFICE_OF_ENERGY_EFFICIENCY = "Office of Energy Efficiency",
  EXPLOSIVE = "Explosive",
  ROUGE_DIAMONDS = "Rouge Diamonds",

  // needs to check data integrity
  EXPLOSIVE_PROGRAM = "Explosive Program"
}
