import { FindOptionsRelations } from "typeorm";
import { CommercialInvoiceLine } from "../entities/commercial-invoice-line.entity";

export enum NonCompliantReason {
  MISSING_OGD_FILING = "missing-ogd-filing",
  MISSING_SIMA_FILING = "missing-sima-filing",
  MISSING_OR_INVALID_MEASUREMENTS = "missing-or-invalid-measurements",
  MISSING_CERTIFICATE_OF_ORIGIN = "missing-certificate-of-origin",
  /** @deprecated Please use missing-sima-filing instead. */
  MISSING_SIMA_CODE = "missing-sima-code"
}

export enum DestinationProvince {
  ALBERTA = "AB",
  BRITISH_COLUMBIA = "BC",
  MANITOBA = "MB",
  NEW_BRUNSWICK = "NB",
  NEWFOUNDLAND_AND_LABRADOR = "NL",
  NOVA_SCOTIA = "NS",
  NORTHWEST_TERRITORIES = "NT",
  NUNAVUT = "NU",
  ONTARIO = "ON",
  PRINCE_EDWARD_ISLAND = "PE",
  QUEBEC = "QC",
  SASKATCHEWAN = "SK",
  YUKON_TERRITORY = "YT"
}

export const FIND_COMMERCIAL_INVOICE_LINE_RELATIONS: FindOptionsRelations<CommercialInvoiceLine> = {
  product: true,
  vfd: true,
  tt: true,
  gstExemptCode: true,
  tariffCode: true,
  exciseTaxCode: true,
  origin: true,
  originState: true,
  commercialInvoice: true,
  organization: true,
  createdBy: true,
  lastEditedBy: true,
  measurements: true
};

export const COMMERCIAL_INVOICE_LINE_NON_ID_KEYS = ["candataId", "sequence"];
export const COMMERCIAL_INVOICE_LINE_REQUIRED_KEYS = [
  "commercialInvoice",
  "product",
  "origin",
  "hsCode",
  "goodsDescription",
  "quantity",
  "unitOfMeasure",
  "unitPrice",
  "totalLineValue",
  "surtaxSubjectCode",
  "organization",
  // "tt",
  "vfd"
];
export const COMMERCIAL_INVOICE_LINE_ENUM_KEYS = [
  "unitOfMeasure",
  "timeLimitType",
  "surtaxSubjectCode",
  "safeguardSubjectCode",
  "simaUnitOfMeasure",
  "destinationProvince"
];

export enum CommercialInvoiceLineColumn {
  id = "id",
  sequence = "sequence",
  candataId = "candataId",
  hsCode = "hsCode",
  goodsDescription = "goodsDescription",
  quantity = "quantity",
  unitOfMeasure = "unitOfMeasure",
  unitPrice = "unitPrice",
  totalLineValue = "totalLineValue",
  totalLineValueCad = "totalLineValueCad",
  timeLimitType = "timeLimitType",
  timeLimitStartDate = "timeLimitStartDate",
  timeLimitEndDate = "timeLimitEndDate",
  authorityPermit = "authorityPermit",
  dutiesReliefLicence = "dutiesReliefLicence",
  surtaxSubjectCode = "surtaxSubjectCode",
  surtaxCode = "surtaxCode",
  safeguardSubjectCode = "safeguardSubjectCode",
  safeguardCode = "safeguardCode",
  simaQuantity = "simaQuantity",
  simaUnitOfMeasure = "simaUnitOfMeasure",
  provincialAlcoholTax = "provincialAlcoholTax",
  provincialSalesTax = "provincialSalesTax",
  provincialTobaccoTax = "provincialTobaccoTax",
  provincialCannabisExciseDuty = "provincialCannabisExciseDuty",
  alcoholPercentage = "alcoholPercentage",
  pstHst = "pstHst",
  destinationProvince = "destinationProvince",
  cadCalculationDate = "cadCalculationDate",
  valueForDuty = "valueForDuty",
  valueForTax = "valueForTax",
  antiDumping = "antiDumping",
  countervailing = "countervailing",
  customsDuties = "customsDuties",
  exciseDuties = "exciseDuties",
  exciseTax = "exciseTax",
  gst = "gst",
  safeguard = "safeguard",
  surtax = "surtax",
  totalDutiesAndTaxes = "totalDutiesAndTaxes",
  /** @deprecated This field will be removed in future versions. */
  dutyCad = "dutyCad",
  /** @deprecated This field will be removed in future versions. */
  dutyAndTax = "dutyAndTax",
  additionalInfo = "additionalInfo",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  productId = "productId",
  vfdId = "vfdId",
  ttId = "ttId",
  gstExemptCodeId = "gstExemptCodeId",
  tariffCodeId = "tariffCodeId",
  exciseTaxCodeId = "exciseTaxCodeId",
  originId = "originId",
  commercialInvoiceId = "commercialInvoiceId",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}
