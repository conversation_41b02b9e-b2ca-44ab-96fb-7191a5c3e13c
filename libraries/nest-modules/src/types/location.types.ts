import { FindOptionsRelations } from "typeorm";
import { Location } from "../entities";

export enum LocationType {
  // PORT = 'port'
  OCEAN_PORT = "ocean-port",
  INLAND_PORT = "inland-port",
  OCEAN_AND_INLAND_PORT = "ocean-and-inland-port"
}

export enum LocationColumn {
  id = "id",
  locationType = "locationType",
  name = "name",
  unit = "unit",
  streetAddress = "streetAddress",
  city = "city",
  state = "state",
  /** @deprecated Use `postalCode` instead */
  zipCode = "zipCode",
  postalCode = "postalCode",
  timezone = "timezone",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  countryId = "countryId",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export const FIND_LOCATION_RELATIONS: FindOptionsRelations<Location> = {
  country: true,
  createdBy: true,
  lastEditedBy: true
};

export const LOCATION_ENUM_KEYS = ["locationType"];
export const LOCATION_REQUIRED_KEYS = ["name", "city", "timezone", "country"];
