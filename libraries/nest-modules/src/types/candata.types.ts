import { OrganizationCustomsBroker } from "./organization.types";

export enum CandataAccountAuthType {
  API_KEY = "api-key",
  CLIENT_ID_AND_SECRET = "client-id-and-secret"
}

export interface ApiKeyCandataAccount {
  /** The customs broker represented by this Candata account */
  account: OrganizationCustomsBroker;
  /** The type of authentication used for this Candata account */
  authType: CandataAccountAuthType.API_KEY;
  /** The base URL of the Candata API */
  baseUrl: string;
  /** The API key used for authentication */
  apiKey: string;
}

export interface ClientIdAndSecretCandataAccount {
  /** The customs broker represented by this Candata account */
  account: OrganizationCustomsBroker;
  /** The type of authentication used for this Candata account */
  authType: CandataAccountAuthType.CLIENT_ID_AND_SECRET;
  /** The base URL of the Candata API */
  baseUrl: string;
  /** The URL for obtaining the Candata API access token */
  authUrl: string;
  /** The client ID used for authentication */
  clientId: string;
  /** The client secret used for authentication */
  clientSecret: string;
}

export type CandataAccount = ApiKeyCandataAccount | ClientIdAndSecretCandataAccount;

export interface CandataModuleOptions {
  /** The Candata accounts to use for the module */
  accounts: Array<CandataAccount>;
  /** App ID required for performing actions on the Candata UI through remote server */
  appId: string;
}

export enum CandataErrorHandling {
  /** Throws an error if any of the requests fail */
  ERROR_ON_FIRST_ERROR = "error-on-first-error",
  /** Throws an error if all of the requests fail. If only some of the requests failed, the results of the requests that succeeded will be returned and null values for the ones that failed */
  ERROR_ON_ALL_ERRORS = "error-on-all-errors",
  /** Ignores errors and returns the results of the requests that succeeded and null for the ones that failed*/
  IGNORE_ERRORS = "ignore-errors"
}

export const CANDATA_MODULE_OPTIONS = "CANDATA_MODULE_OPTIONS";

export enum VfdCode {
  CODE_13 = "13",
  CODE_14 = "14",
  CODE_15 = "15",
  CODE_16 = "16",
  CODE_17 = "17",
  CODE_18 = "18",
  CODE_19 = "19",
  CODE_23 = "23",
  CODE_24 = "24",
  CODE_25 = "25",
  CODE_26 = "26",
  CODE_27 = "27",
  CODE_28 = "28",
  CODE_29 = "29"
}

export enum TariffTreatmentCode {
  NONE = 0,
  MOST_FAVOURED = 2,
  GENERAL = 3,
  AUSTRALIA = 4,
  NEW_ZEALAND = 5,
  COMMON_WEALTH = 7,
  LEAST_DEVELOPED = 8,
  GENERAL_PREFERENCE = 9,
  US = 10,
  MEXICO = 11,
  MEXICO_OR_US = 12,
  ISRAEL = 13,
  CHILE = 14,
  COSTA_RICA = 21,
  ICELAND = 22,
  NORWAY = 23,
  SWITZERLAND = 24,
  PERU = 25,
  COLOMBIA = 26,
  JORDAN = 27,
  PANAMA = 28,
  HONDURAS = 29,
  SOUTH_KOREA = 30,
  EUROPE = 31,
  UKRAINE = 32,
  TRANS_PACIFIC = 33,
  UNITED_KINGDOM = 34
}

export enum SimaCode {
  SEPARATE_NON_SUBJECT_FROM_GOODS_WITH_CITT = "10",
  PRICE_UNDERTAKING = "20",
  SIMA_BOND_COVERED_PROVISIONAL_DUTY = "32",
  GOODS_WITH_NIL_SIMA_DUTY = "40",
  SIMA_DUTY_COVERED_BY_OIC = "50",
  CASH_COVERED_SIMA_DUTY_SURTAX_AMOUNT = "51",
  SIMA_BOND_COVERED_EXPEDITED_GOODS = "52"
}

export enum SimaSubjectCode {
  SUBJECT = "S",
  NON_SUBJECT = "N",
  UNDERTAKING = "U"
}

export enum SimaIncoterms {
  CFR = "CFR",
  CIF = "CIF",
  CIP = "CIP",
  CPT = "CPT",
  DAP = "DAP",
  DAT = "DAT",
  DDP = "DDP",
  DPU = "DPU",
  EWX = "EWX",
  FAS = "FAS",
  FCA = "FCA",
  FOB = "FOB"
}

export enum SurtaxSubjectCode {
  SUBJECT = "S",
  NON_SUBJECT = "N"
}

export enum SafeguardSubjectCode {
  SUBJECT = "S",
  NON_SUBJECT = "N"
}

export enum SpecialAuthorityTimeLimitType {
  AIRCRAFT_VESSEL_PARTS_15_YEARS = 1,
  BEER_AND_WINE_5_YEARS = 2,
  WAREHOUSE_MARKING_DISPLAY_90_DAYS = 3,
  OTHER_GOODS_4_YEARS = 4
}

export enum CandataProvince {
  // Canadian Provinces and Territories
  ALBERTA = "AB",
  BRITISH_COLUMBIA = "BC",
  MANITOBA = "MB",
  NEW_BRUNSWICK = "NB",
  NEWFOUNDLAND_AND_LABRADOR = "NL",
  NORTHWEST_TERRITORIES = "NT",
  NOVA_SCOTIA = "NS",
  NUNAVUT = "NU",
  ONTARIO = "ON",
  PRINCE_EDWARD_ISLAND = "PE",
  QUEBEC = "QC",
  SASKATCHEWAN = "SK",
  YUKON = "YT",

  // US States and Territories
  ALASKA = "AK",
  ALABAMA = "AL",
  ARKANSAS = "AR",
  AMERICAN_SAMOA = "AS",
  ARIZONA = "AZ",
  CALIFORNIA = "CA",
  COLORADO = "CO",
  CONNECTICUT = "CT",
  DISTRICT_OF_COLUMBIA = "DC",
  DELAWARE = "DE",
  FLORIDA = "FL",
  GEORGIA = "GA",
  GUAM = "GU",
  HAWAII = "HI",
  IOWA = "IA",
  IDAHO = "ID",
  ILLINOIS = "IL",
  INDIANA = "IN",
  KANSAS = "KS",
  KENTUCKY = "KY",
  LOUISIANA = "LA",
  MASSACHUSETTS = "MA",
  MARYLAND = "MD",
  MAINE = "ME",
  MICHIGAN = "MI",
  MINNESOTA = "MN",
  MISSOURI = "MO",
  NORTHERN_MARIANA_ISLANDS = "MP",
  MISSISSIPPI = "MS",
  MONTANA = "MT",
  NORTH_CAROLINA = "NC",
  NORTH_DAKOTA = "ND",
  NEBRASKA = "NE",
  NEW_HAMPSHIRE = "NH",
  NEW_JERSEY = "NJ",
  NEW_MEXICO = "NM",
  NEVADA = "NV",
  NEW_YORK = "NY",
  OHIO = "OH",
  OKLAHOMA = "OK",
  OREGON = "OR",
  PENNSYLVANIA = "PA",
  PUERTO_RICO = "PR",
  RHODE_ISLAND = "RI",
  SOUTH_CAROLINA = "SC",
  SOUTH_DAKOTA = "SD",
  TENNESSEE = "TN",
  TEXAS = "TX",
  UTAH = "UT",
  VIRGINIA = "VA",
  VIRGIN_ISLANDS = "VI",
  VERMONT = "VT",
  WASHINGTON = "WA",
  WISCONSIN = "WI",
  WEST_VIRGINIA = "WV",
  WYOMING = "WY"
}

export enum CandataCustomerProfileStatus {
  ACTIVE = "A",
  CREDIT_WATCH = "W",
  HOLD = "H",
  INACTIVE = "I"
}

export enum CandataModeOfTransport {
  AIR = 1,
  HIGHWAY = 2,
  POSTAL = 5,
  RAIL = 6,
  PIPELINE = 7,
  HAND_CARRIED_GOODS = 8,
  MARINE = 9,
  ELECTRICAL_GRID = 10
}

export enum CandataDeclarationType {
  STANDARD_ACCOUNTING = "AB",
  LVS = "F",
  CUSTOMS_SELF_ASSESSMENT = "TT",
  WAREHOUSE_IN = "10",
  RE_WAREHOUSE = "13",
  EX_WAREHOUSE_FOR_CONSUMPTION = "20",
  EX_WAREHOUSE_FOR_EXPORT = "21",
  TRANSFER_OF_GOODS = "30"
}

export enum CandataDeclarationStatus {
  DRAFT = -1,
  APPROVED = 39,
  REJECTED = 41,
  APPROVAL_PENDING = 42,
  ACKNOWLEDGED = 200,
  INVALID_SYNTAX = 400
}

export enum CandataSendIidMessageFunction {
  CANCEL = "Cancel",
  CHANGE = "Change",
  ORIGINAL = "Original"
}

export enum CandataGstStatusCode {
  CODE_001 = "001",
  CODE_002 = "002",
  CODE_017 = "017",
  CODE_048 = "048",
  CODE_049 = "049",
  CODE_050 = "050",
  CODE_051 = "051",
  CODE_052 = "052",
  CODE_053 = "053",
  CODE_054 = "054",
  CODE_055 = "055",
  CODE_056 = "056",
  CODE_057 = "057",
  CODE_059 = "059",
  CODE_060 = "060",
  CODE_061 = "061",
  CODE_062 = "062",
  CODE_063 = "063",
  CODE_064 = "064",
  CODE_065 = "065",
  CODE_066 = "066",
  CODE_067 = "067",
  CODE_068 = "068",
  CODE_069 = "069",
  CODE_070 = "070",
  CODE_071 = "071",
  CODE_072 = "072",
  CODE_073 = "073",
  CODE_074 = "074",
  CODE_075 = "075",
  CODE_076 = "076",
  CODE_077 = "077",
  CODE_078 = "078",
  CODE_079 = "079",
  CODE_080 = "080",
  CODE_081 = "081",
  CODE_082 = "082",
  CODE_083 = "083",
  CODE_084 = "084",
  CODE_085 = "085",
  CODE_086 = "086",
  CODE_092 = "092",
  CODE_093 = "093",
  CODE_096 = "096",
  CODE_099 = "099"
}

export enum RNSProcessingIndicator {
  UNKNOWN = 0,
  ACCEPTED = 1,
  REJECTED = 2,
  RELEASED = 4,
  EXAM_REQUIRED = 5,
  Y51_RELEASE = 6,
  RELEASED_INSTRUCTIONS = 7,
  DETAIN_TO_DESTINATION = 8,
  ACCEPTED_WAITING = 9,
  ERROR = 14,
  AUTHORIZED_TO_DELIVERY = 23,
  EXAM_REQUIRED_INSTRUCTIONS = 24,
  ACCEPTED_AWAITING_CUSTOMS = 34
}
