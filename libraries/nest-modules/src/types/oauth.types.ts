export interface DocusignAccessTokenResponse {
  access_token: string;
  token_type: string;
  refresh_token: string;
  expires_in: number;
}

export interface DocusignAccount {
  account_id: string;
  is_default: boolean;
  account_name: string;
  base_uri: string;
}

export interface DocusignGetUserInfoResponse {
  sub: string;
  name: string;
  given_name: string;
  family_name: string;
  created: string;
  email: string;
  accounts: Array<DocusignAccount>;
}
