export enum UnitOfMeasure {
  // weight
  MASS_IN_CARATS = "ctm",
  MASS_IN_KILOGRAMS = "kgm",
  MASS_IN_KILOGRAMS_OF_NAMED_SUBSTANCE = "kns",
  MASS_IN_KILOGRAM_AIR_DRY = "ksd",
  MASS_IN_GRAMS = "grm",
  MASS_IN_METRIC_TONNES = "tne",
  MASS_IN_METRIC_TONNES_AIR_DRY = "tsd",

  // per unit
  GROSS_TWELVE_DOZEN = "gro",
  NUMBER_OF_DOZEN = "dzn",
  THOUSANDS = "mil",
  NUMBER_OF_PACKAGES = "nap",
  NUMBER = "nmb",
  NUMBER_OF_PAIRS = "par",
  NUMBER_OF_SETS = "set",

  // volume
  LIQUID_VOLUME_IN_HECTOLITRES = "hlt",
  LIQUID_VOLUME_IN_LITRES_OF_PURE_ALCOHOL = "lpa",
  LIQUID_VOLUME_IN_LITRES = "ltr",
  VOLUME_IN_CUBIC_METERS = "mtq",
  VOLUME_IN_THOUSANDS_OF_CUBIC_METERS = "tmq",

  // area
  AREA_IN_SQUARE_METERS = "mtk",

  // length
  METERS = "mtr",

  // other
  RADIOACTIVITY_IN_MEGABECQUERELS = "mbq",
  ELECTRICAL_ENERGY_IN_MEGAWATT_HOURS = "mwh"
}

export enum UnitOfMeasureType {
  VOLUME = "volume",
  WEIGHT = "weight",
  LENGTH = "len",
  AREA = "area",
  PER_UNIT = "per-unit"
}

export const UNIT_OF_MEASURE_TYPE_MAP = {
  [UnitOfMeasureType.VOLUME]: [
    UnitOfMeasure.LIQUID_VOLUME_IN_HECTOLITRES,
    UnitOfMeasure.LIQUID_VOLUME_IN_LITRES_OF_PURE_ALCOHOL,
    UnitOfMeasure.LIQUID_VOLUME_IN_LITRES,
    UnitOfMeasure.VOLUME_IN_CUBIC_METERS,
    UnitOfMeasure.VOLUME_IN_THOUSANDS_OF_CUBIC_METERS
  ],
  [UnitOfMeasureType.WEIGHT]: [
    UnitOfMeasure.MASS_IN_CARATS,
    UnitOfMeasure.MASS_IN_KILOGRAMS,
    UnitOfMeasure.MASS_IN_KILOGRAMS_OF_NAMED_SUBSTANCE,
    UnitOfMeasure.MASS_IN_KILOGRAM_AIR_DRY,
    UnitOfMeasure.MASS_IN_GRAMS,
    UnitOfMeasure.MASS_IN_METRIC_TONNES,
    UnitOfMeasure.MASS_IN_METRIC_TONNES_AIR_DRY
  ],
  [UnitOfMeasureType.LENGTH]: [UnitOfMeasure.METERS],
  [UnitOfMeasureType.AREA]: [UnitOfMeasure.AREA_IN_SQUARE_METERS],
  [UnitOfMeasureType.PER_UNIT]: [
    UnitOfMeasure.GROSS_TWELVE_DOZEN,
    UnitOfMeasure.NUMBER_OF_DOZEN,
    UnitOfMeasure.THOUSANDS,
    UnitOfMeasure.NUMBER_OF_PACKAGES,
    UnitOfMeasure.NUMBER,
    UnitOfMeasure.NUMBER_OF_PAIRS,
    UnitOfMeasure.NUMBER_OF_SETS
  ]
};
