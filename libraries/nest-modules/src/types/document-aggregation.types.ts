import { FindOptionsRelations } from "typeorm";
import { DocumentAggregation } from "../entities/document-aggregation.entity";

export enum DocumentAggregationStatus {
  PENDING = "pending",
  FAILED = "failed",
  PROCESSING = "processing",
  SUCCESS = "success"
}

export enum DocumentAggregationAction {
  CREATE_SHIPMENT = "create-shipment",
  CREATE_COMMERCIAL_INVOICE = "create-commercial-invoice",
  CREATE_CERTIFICATE_OF_ORIGIN = "create-certificate-of-origin",
  UPDATE_SHIPMENT = "update-shipment"
}

export enum EntityOperation {
  CREATE = "create",
  UPDATE = "update"
}

export enum AggregationTargetType {
  COMMERCIAL_INVOICE = "commercial-invoice",
  CERTIFICATE_OF_ORIGIN = "certificate-of-origin",
  SHIPMENT = "shipment"
}

export enum DocumentAggregationColumn {
  id = "id",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  createdById = "createdById",
  lastEditedById = "lastEditedById",
  organizationId = "organizationId",
  status = "status",
  data = "data",
  shipmentId = "shipmentId",
  type = "type"
}

export const FIND_DOCUMENT_AGGREGATION_RELATIONS: FindOptionsRelations<DocumentAggregation> = {
  createdBy: true,
  lastEditedBy: true,
  organization: true,
  documents: true,
  steps: true
};

export class FieldValidationError {
  sourceField: string | string[] | null;
  targetField: string | string[] | null;
  message: string;
}

export type FieldValidationErrors = FieldValidationError[];
