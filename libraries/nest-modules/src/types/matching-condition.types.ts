import { FindOptionsRelations } from "typeorm";
import { MatchingCondition } from "../entities";
import { MatchingRuleDestinationDatabaseTable } from "./matching-rule.types";
import { PRODUCT_CASE_INSENSITIVE_KEYS, ProductColumn } from "./product.types";

export enum MatchingConditionColumn {
  id = "id",
  attribute = "attribute",
  operator = "operator",
  value = "value",
  isOperationInverted = "isOperationInverted",
  attributeType = "attributeType",
  valueString = "valueString",
  valueInteger = "valueInteger",
  valueFloat = "valueFloat",
  valueBoolean = "valueBoolean",
  valueDateTime = "valueDateTime",
  createdById = "createdById",
  createdDate = "createdDate",
  lastEditedById = "lastEditedById",
  lastEditedDate = "lastEditedDate",
  ruleId = "ruleId"
}

export const FIND_MATCHING_CONDITION_RELATIONS: FindOptionsRelations<MatchingCondition> = {
  createdBy: true,
  lastEditedBy: true,
  rule: true
};

export const MATCHING_CONDITION_REQUIRED_KEYS = [
  "attribute",
  "operator",
  "isOperationInverted",
  "attributeType"
];

export const MATCHING_CONDITION_ENUM_KEYS = ["operator", "attributeType"];

export enum MatchingConditionOperator {
  EQUALS = "equals",
  CONTAINS = "contains",
  STARTS_WITH = "starts-with",
  ENDS_WITH = "ends-with",
  GREATER_THAN = "greater-than",
  LESS_THAN = "less-than",
  GREATER_THAN_OR_EQUAL_TO = "greater-than-or-equal-to",
  LESS_THAN_OR_EQUAL_TO = "less-than-or-equal-to",
  IN = "in",
  BETWEEN_INCLUSIVE = "between-inclusive",
  BETWEEN_EXCLUSIVE = "between-exclusive"
}

export enum MatchingConditionAttributeType {
  ID = "id",
  STRING = "string",
  INTEGER = "integer",
  FLOAT = "float",
  BOOLEAN = "boolean",
  DATE_TIME = "date-time"
}

export const TYPED_VALUE_DB_COLUMNS = [
  MatchingConditionColumn.valueString,
  MatchingConditionColumn.valueInteger,
  MatchingConditionColumn.valueFloat,
  MatchingConditionColumn.valueBoolean,
  MatchingConditionColumn.valueDateTime
];

export const ATTRIBUTE_TYPE_SUPPORT_OPERATORS = {
  [MatchingConditionAttributeType.ID]: [MatchingConditionOperator.EQUALS],
  [MatchingConditionAttributeType.STRING]: [
    MatchingConditionOperator.EQUALS,
    MatchingConditionOperator.CONTAINS,
    MatchingConditionOperator.STARTS_WITH,
    MatchingConditionOperator.ENDS_WITH,
    MatchingConditionOperator.GREATER_THAN,
    MatchingConditionOperator.GREATER_THAN_OR_EQUAL_TO,
    MatchingConditionOperator.LESS_THAN,
    MatchingConditionOperator.LESS_THAN_OR_EQUAL_TO,
    MatchingConditionOperator.IN,
    MatchingConditionOperator.BETWEEN_INCLUSIVE,
    MatchingConditionOperator.BETWEEN_EXCLUSIVE
  ],
  [MatchingConditionAttributeType.INTEGER]: [
    MatchingConditionOperator.EQUALS,
    MatchingConditionOperator.GREATER_THAN,
    MatchingConditionOperator.GREATER_THAN_OR_EQUAL_TO,
    MatchingConditionOperator.LESS_THAN,
    MatchingConditionOperator.LESS_THAN_OR_EQUAL_TO,
    MatchingConditionOperator.IN,
    MatchingConditionOperator.BETWEEN_INCLUSIVE,
    MatchingConditionOperator.BETWEEN_EXCLUSIVE
  ],
  [MatchingConditionAttributeType.FLOAT]: [
    MatchingConditionOperator.EQUALS,
    MatchingConditionOperator.GREATER_THAN,
    MatchingConditionOperator.GREATER_THAN_OR_EQUAL_TO,
    MatchingConditionOperator.LESS_THAN,
    MatchingConditionOperator.LESS_THAN_OR_EQUAL_TO,
    MatchingConditionOperator.IN,
    MatchingConditionOperator.BETWEEN_INCLUSIVE,
    MatchingConditionOperator.BETWEEN_EXCLUSIVE
  ],
  [MatchingConditionAttributeType.BOOLEAN]: [MatchingConditionOperator.EQUALS, MatchingConditionOperator.IN],
  [MatchingConditionAttributeType.DATE_TIME]: [
    MatchingConditionOperator.EQUALS,
    MatchingConditionOperator.GREATER_THAN,
    MatchingConditionOperator.GREATER_THAN_OR_EQUAL_TO,
    MatchingConditionOperator.LESS_THAN,
    MatchingConditionOperator.LESS_THAN_OR_EQUAL_TO,
    MatchingConditionOperator.IN,
    MatchingConditionOperator.BETWEEN_INCLUSIVE,
    MatchingConditionOperator.BETWEEN_EXCLUSIVE
  ]
};
export const ATTRIBUTE_TYPE_DB_COLUMN_MAPPING: Record<
  MatchingConditionAttributeType,
  MatchingConditionColumn
> = {
  [MatchingConditionAttributeType.ID]: MatchingConditionColumn.valueInteger,
  [MatchingConditionAttributeType.STRING]: MatchingConditionColumn.valueString,
  [MatchingConditionAttributeType.INTEGER]: MatchingConditionColumn.valueInteger,
  [MatchingConditionAttributeType.FLOAT]: MatchingConditionColumn.valueFloat,
  [MatchingConditionAttributeType.BOOLEAN]: MatchingConditionColumn.valueBoolean,
  [MatchingConditionAttributeType.DATE_TIME]: MatchingConditionColumn.valueDateTime
};

export const DESTINATION_TABLE_ATTRIBUTES: Record<
  MatchingRuleDestinationDatabaseTable,
  Record<string, MatchingConditionAttributeType>
> = {
  [MatchingRuleDestinationDatabaseTable.PRODUCT]: {
    [ProductColumn.id]: MatchingConditionAttributeType.ID,
    [ProductColumn.partNumber]: MatchingConditionAttributeType.STRING,
    [ProductColumn.sku]: MatchingConditionAttributeType.STRING,
    [ProductColumn.upc]: MatchingConditionAttributeType.STRING,
    [ProductColumn.vendorPartNumber]: MatchingConditionAttributeType.STRING,
    [ProductColumn.description]: MatchingConditionAttributeType.STRING,
    [ProductColumn.hsCode]: MatchingConditionAttributeType.STRING,
    [ProductColumn.vendorId]: MatchingConditionAttributeType.ID,
    [ProductColumn.originId]: MatchingConditionAttributeType.ID,
    [ProductColumn.organizationId]: MatchingConditionAttributeType.ID
  }
};

export const DESTINATION_TABLE_CASE_INSENSITIVE_ATTRIBUTES: Record<
  MatchingRuleDestinationDatabaseTable,
  Array<string>
> = {
  [MatchingRuleDestinationDatabaseTable.PRODUCT]: PRODUCT_CASE_INSENSITIVE_KEYS
};

export const TRADE_PARTNER_ID_ATTRIBUTES: Array<string> = [ProductColumn.vendorId];
export const COUNTRY_ID_ATTRIBUTES: Array<string> = [ProductColumn.originId];
export const ORGANIZATION_ID_ATTRIBUTES: Array<string> = [ProductColumn.organizationId];
