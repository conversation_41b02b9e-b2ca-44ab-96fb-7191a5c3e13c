import { FindOptionsRelations } from "typeorm";
import { CanadaGstExemptCode } from "../entities";
import { CrudOptions } from "./base.types";

export enum CanadaGstExemptCodeColumn {
  id = "id",
  code = "code",
  explanation = "explanation",
  createdById = "createdById",
  createdDate = "createdDate",
  lastEditedById = "lastEditedById",
  lastEditedDate = "lastEditedDate"
}

export const FIND_CANADA_GST_EXEMPT_CODE_RELATIONS: FindOptionsRelations<CanadaGstExemptCode> = {
  createdBy: true,
  lastEditedBy: true
};

export const CANADA_GST_EXEMPT_CODE_REQUIRED_KEYS = ["code", "explanation"];

export const CANADA_GST_EXEMPT_CODE_MODULE_OPTIONS = "CANADA_GST_EXEMPT_CODE_MODULE_OPTIONS";
