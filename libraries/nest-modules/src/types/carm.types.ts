export enum CarmErrorCode {
  NO_DATA_SELECTED = "Z_TRM_TARIFF_QUERY/003"
}

export enum GetListOfTariffNumbersOrderBy {
  TARIFF_NUMBER = "TariffNumber",
  TARIFF_NUMBER_DESC = "TariffNumber desc",
  AS_OF_DATE = "AsOfDate",
  AS_OF_DATE_DESC = "AsOfDate desc",
  SPECIAL_CLASSIFICATION_INDICATOR = "SpecialClassificationIndicator",
  SPECIAL_CLASSIFICATION_INDICATOR_DESC = "SpecialClassificationIndicator desc",
  TARIFF_NUMBER_VALID_START_DATE = "TariffNumberValidStartDate",
  TARIFF_NUMBER_VALID_START_DATE_DESC = "TariffNumberValidStartDate desc",
  TARIFF_NUMBER_VALID_END_DATE = "TariffNumberValidEndDate",
  TARIFF_NUMBER_VALID_END_DATE_DESC = "TariffNumberValidEndDate desc",
  TARIFF_NUMBER_CHANGE_REASON = "TariffNumberChangeReason",
  TARIFF_NUMBER_CHANGE_REASON_DESC = "TariffNumberChangeReason desc",
  TARIFF_ITEM_NUMBER = "TariffItemNumber",
  TARIFF_ITEM_NUMBER_DESC = "TariffItemNumber desc",
  TARIFF_ITEM_EFFECTIVE_DATE = "TariffItemEffectiveDate",
  TARIFF_ITEM_EFFECTIVE_DATE_DESC = "TariffItemEffectiveDate desc",
  TARIFF_ITEM_EXPIRY_DATE = "TariffItemExpiryDate",
  TARIFF_ITEM_EXPIRY_DATE_DESC = "TariffItemExpiryDate desc",
  TARIFF_ITEM_CHANGE_REASON_CODE = "TariffItemChangeReasonCode",
  TARIFF_ITEM_CHANGE_REASON_CODE_DESC = "TariffItemChangeReasonCode desc",
  AREA_CODE_1 = "AreaCode1",
  AREA_CODE_1_DESC = "AreaCode1 desc",
  AREA_CODE_2 = "AreaCode2",
  AREA_CODE_2_DESC = "AreaCode2 desc",
  AREA_CODE_3 = "AreaCode3",
  AREA_CODE_3_DESC = "AreaCode3 desc",
  UNIT_OF_MEASURE_CODE = "UnitOfMeasureCode",
  UNIT_OF_MEASURE_CODE_DESC = "UnitOfMeasureCode desc",
  EXCHANGE_DATE_FLAG = "ExchangeDateFlag",
  EXCHANGE_DATE_FLAG_DESC = "ExchangeDateFlag desc",
  PERMIT_REQUIRED_INDICATOR = "PermitRequiredIndicator",
  PERMIT_REQUIRED_INDICATOR_DESC = "PermitRequiredIndicator desc",
  QUOTA_INDICATOR = "QuotaIndicator",
  QUOTA_INDICATOR_DESC = "QuotaIndicator desc",
  GST_INDICATOR = "GSTIndicator",
  GST_INDICATOR_DESC = "GSTIndicator desc",
  INACTIVE_INDICATOR = "InactiveIndicator",
  INACTIVE_INDICATOR_DESC = "InactiveIndicator desc",
  UPDATE_ON = "UpdateOn",
  UPDATE_ON_DESC = "UpdateOn desc"
}

export enum GetListOfTariffNumbersSelect {
  TARIFF_NUMBER = "TariffNumber",
  AS_OF_DATE = "AsOfDate",
  SPECIAL_CLASSIFICATION_INDICATOR = "SpecialClassificationIndicator",
  TARIFF_NUMBER_VALID_START_DATE = "TariffNumberValidStartDate",
  TARIFF_NUMBER_VALID_END_DATE = "TariffNumberValidEndDate",
  TARIFF_NUMBER_CHANGE_REASON = "TariffNumberChangeReason",
  TARIFF_ITEM_NUMBER = "TariffItemNumber",
  TARIFF_ITEM_EFFECTIVE_DATE = "TariffItemEffectiveDate",
  TARIFF_ITEM_EXPIRY_DATE = "TariffItemExpiryDate",
  TARIFF_ITEM_CHANGE_REASON_CODE = "TariffItemChangeReasonCode",
  AREA_CODE_1 = "AreaCode1",
  AREA_CODE_2 = "AreaCode2",
  AREA_CODE_3 = "AreaCode3",
  UNIT_OF_MEASURE_CODE = "UnitOfMeasureCode",
  EXCHANGE_DATE_FLAG = "ExchangeDateFlag",
  PERMIT_REQUIRED_INDICATOR = "PermitRequiredIndicator",
  QUOTA_INDICATOR = "QuotaIndicator",
  GST_INDICATOR = "GSTIndicator",
  INACTIVE_INDICATOR = "InactiveIndicator",
  DESCRIPTION = "Description",
  UPDATE_ON = "UpdateOn"
}

export enum GetListOfTariffNumbersExpand {
  TO_CUSTOMS_DUTIES = "to_customsDuties",
  TO_EXCISE_DUTIES = "to_exciseDuties",
  TO_EXCISE_TAXES = "to_exciseTaxes"
}

export enum GetIndividualTariffNumberCustomsDutiesSelect {
  TARIFF_ITEM_NUMBER = "TariffItemNumber",
  TARIFF_TREATMENT_CODE = "TariffTreatmentCode",
  AS_OF_DATE = "AsOfDate",
  CUSTOMS_DUTY_VALID_START_DATE = "CustomsDutyValidStartDate",
  CUSTOMS_DUTY_VALID_END_DATE = "CustomsDutyValidEndDate",
  FREE_QUALIFIER_INDICATOR = "FreeQualifierIndicator",
  SPECIFIC_RATE_REG_VALUE = "SpecificRateRegValue",
  SPECIFIC_RATE_REG_QUALIFIER_CODE = "SpecificRateRegQualifierCode",
  AD_VALOREM_RATE_MIN_VALUE = "AdValoremRateMinValue",
  AD_VALOREM_RATE_MIN_QUALIFIER_CODE = "AdValoremRateMinQualifierCode",
  AD_VALOREM_RATE_MAX_VALUE = "AdValoremRateMaxValue",
  AD_VALOREM_RATE_MAX_QUALIFIER_CODE = "AdValoremRateMaxQualifierCode",
  AD_VALOREM_RATE_REG_VALUE = "AdValoremRateRegValue",
  AD_VALOREM_RATE_REG_QUALIFIER_CODE = "AdValoremRateRegQualifierCode",
  SPECIFIC_RATE_MIN_VALUE = "SpecificRateMinValue",
  SPECIFIC_RATE_MIN_QUALIFIER_CODE = "SpecificRateMinQualifierCode",
  SPECIFIC_RATE_MAX_VALUE = "SpecificRateMaxValue",
  SPECIFIC_RATE_MAX_QUALIFIER_CODE = "SpecificRateMaxQualifierCode",
  INACTIVE_INDICATOR = "InactiveIndicator",
  UPDATE_ON = "UpdateOn",
  UNIT_OF_MEASURE_CODE = "UnitOfMeasureCode"
}

export enum GetListOfCustomsDutyRecordsOrderBy {
  TARIFF_ITEM_NUMBER = "TariffItemNumber",
  TARIFF_ITEM_NUMBER_DESC = "TariffItemNumber desc",
  TARIFF_TREATMENT_CODE = "TariffTreatmentCode",
  TARIFF_TREATMENT_CODE_DESC = "TariffTreatmentCode desc",
  AS_OF_DATE = "AsOfDate",
  AS_OF_DATE_DESC = "AsOfDate desc",
  CUSTOMS_DUTY_VALID_START_DATE = "CustomsDutyValidStartDate",
  CUSTOMS_DUTY_VALID_START_DATE_DESC = "CustomsDutyValidStartDate desc",
  CUSTOMS_DUTY_VALID_END_DATE = "CustomsDutyValidEndDate",
  CUSTOMS_DUTY_VALID_END_DATE_DESC = "CustomsDutyValidEndDate desc",
  FREE_QUALIFIER_INDICATOR = "FreeQualifierIndicator",
  FREE_QUALIFIER_INDICATOR_DESC = "FreeQualifierIndicator desc",
  SPECIFIC_RATE_REG_VALUE = "SpecificRateRegValue",
  SPECIFIC_RATE_REG_VALUE_DESC = "SpecificRateRegValue desc",
  SPECIFIC_RATE_REG_QUALIFIER_CODE = "SpecificRateRegQualifierCode",
  SPECIFIC_RATE_REG_QUALIFIER_CODE_DESC = "SpecificRateRegQualifierCode desc",
  AD_VALOREM_RATE_MIN_VALUE = "AdValoremRateMinValue",
  AD_VALOREM_RATE_MIN_VALUE_DESC = "AdValoremRateMinValue desc",
  AD_VALOREM_RATE_MIN_QUALIFIER_CODE = "AdValoremRateMinQualifierCode",
  AD_VALOREM_RATE_MIN_QUALIFIER_CODE_DESC = "AdValoremRateMinQualifierCode desc",
  AD_VALOREM_RATE_MAX_VALUE = "AdValoremRateMaxValue",
  AD_VALOREM_RATE_MAX_VALUE_DESC = "AdValoremRateMaxValue desc",
  AD_VALOREM_RATE_MAX_QUALIFIER_CODE = "AdValoremRateMaxQualifierCode",
  AD_VALOREM_RATE_MAX_QUALIFIER_CODE_DESC = "AdValoremRateMaxQualifierCode desc",
  AD_VALOREM_RATE_REG_VALUE = "AdValoremRateRegValue",
  AD_VALOREM_RATE_REG_VALUE_DESC = "AdValoremRateRegValue desc",
  AD_VALOREM_RATE_REG_QUALIFIER_CODE = "AdValoremRateRegQualifierCode",
  AD_VALOREM_RATE_REG_QUALIFIER_CODE_DESC = "AdValoremRateRegQualifierCode desc",
  SPECIFIC_RATE_MIN_VALUE = "SpecificRateMinValue",
  SPECIFIC_RATE_MIN_VALUE_DESC = "SpecificRateMinValue desc",
  SPECIFIC_RATE_MIN_QUALIFIER_CODE = "SpecificRateMinQualifierCode",
  SPECIFIC_RATE_MIN_QUALIFIER_CODE_DESC = "SpecificRateMinQualifierCode desc",
  SPECIFIC_RATE_MAX_VALUE = "SpecificRateMaxValue",
  SPECIFIC_RATE_MAX_VALUE_DESC = "SpecificRateMaxValue desc",
  SPECIFIC_RATE_MAX_QUALIFIER_CODE = "SpecificRateMaxQualifierCode",
  SPECIFIC_RATE_MAX_QUALIFIER_CODE_DESC = "SpecificRateMaxQualifierCode desc",
  INACTIVE_INDICATOR = "InactiveIndicator",
  INACTIVE_INDICATOR_DESC = "InactiveIndicator desc",
  UPDATE_ON = "UpdateOn",
  UPDATE_ON_DESC = "UpdateOn desc",
  UNIT_OF_MEASURE_CODE = "UnitOfMeasureCode",
  UNIT_OF_MEASURE_CODE_DESC = "UnitOfMeasureCode desc"
}

export enum GetListOfCustomsDutyRecordsSelect {
  TARIFF_ITEM_NUMBER = "TariffItemNumber",
  TARIFF_TREATMENT_CODE = "TariffTreatmentCode",
  AS_OF_DATE = "AsOfDate",
  CUSTOMS_DUTY_VALID_START_DATE = "CustomsDutyValidStartDate",
  CUSTOMS_DUTY_VALID_END_DATE = "CustomsDutyValidEndDate",
  FREE_QUALIFIER_INDICATOR = "FreeQualifierIndicator",
  SPECIFIC_RATE_REG_VALUE = "SpecificRateRegValue",
  SPECIFIC_RATE_REG_QUALIFIER_CODE = "SpecificRateRegQualifierCode",
  AD_VALOREM_RATE_MIN_VALUE = "AdValoremRateMinValue",
  AD_VALOREM_RATE_MIN_QUALIFIER_CODE = "AdValoremRateMinQualifierCode",
  AD_VALOREM_RATE_MAX_VALUE = "AdValoremRateMaxValue",
  AD_VALOREM_RATE_MAX_QUALIFIER_CODE = "AdValoremRateMaxQualifierCode",
  AD_VALOREM_RATE_REG_VALUE = "AdValoremRateRegValue",
  AD_VALOREM_RATE_REG_QUALIFIER_CODE = "AdValoremRateRegQualifierCode",
  SPECIFIC_RATE_MIN_VALUE = "SpecificRateMinValue",
  SPECIFIC_RATE_MIN_QUALIFIER_CODE = "SpecificRateMinQualifierCode",
  SPECIFIC_RATE_MAX_VALUE = "SpecificRateMaxValue",
  SPECIFIC_RATE_MAX_QUALIFIER_CODE = "SpecificRateMaxQualifierCode",
  INACTIVE_INDICATOR = "InactiveIndicator",
  UPDATE_ON = "UpdateOn",
  UNIT_OF_MEASURE_CODE = "UnitOfMeasureCode"
}

export enum CarmLanguageCode {
  EN = "EN",
  FR = "FR",
  en = "en",
  fr = "fr"
}

export enum CarmStatus {
  SENT = "sent",
  PENDING = "pending",
  ACTIVE = "active",
  INVALID = "invalid",
  APPROVED = "Approved"
}

export interface CarmModuleOptions {
  appId: string;
}
export type CarmStatusResponse = {
  business_name: string;
  business_number: string;
  status: CarmStatus;
  Request_date: string;
};
export type CarmRequestParams = {
  businessNo: string;
};
export type CarmRequestResponse = {
  result: CarmStatus;
  businessNo: string;
};
export type CarmErrorResponse = {
  success: boolean;
  message: string;
};

export const CARM_MODULE_OPTIONS = "CARM_MODULE_OPTIONS";
