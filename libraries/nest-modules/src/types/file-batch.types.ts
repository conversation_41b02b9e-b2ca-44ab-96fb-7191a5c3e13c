export enum FileBatchColumn {
  id = "id",
  name = "name",
  shipmentId = "shipmentId",
  createdById = "createdById",
  lastEditedById = "lastEditedById",
  organizationId = "organizationId",
  creator = "creator",
  status = "status"
}

export enum FileBatchCreator {
  API = "api",
  EMAIL = "email"
}

export enum FileBatchStatus {
  UPLOADED = "uploaded",
  EXTRACTING = "extracting",
  EXTRACTED = "extracted",
  AGGREGATING = "aggregating",
  AGGREGATED = "aggregated"
}

export const FILE_BATCH_ENUM_KEYS = [FileBatchColumn.status];
