import { FindOptionsRelations } from "typeorm";
import { DocumentTypeField } from "../entities";

export enum DocumentFieldDataType {
  STRING = "string",
  NUMBER = "number",
  BOOLEAN = "boolean",
  DATETIME = "datetime",
  ARRAY = "array",
  OBJECT = "object"
}

export enum DocumentTypeFieldColumn {
  id = "id",
  name = "name",
  dataType = "dataType",
  isMandatory = "isMandatory",
  description = "description",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  documentTypeId = "documentTypeId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export const FIND_DOCUMENT_TYPE_FIELD_RELATIONS: FindOptionsRelations<DocumentTypeField> = {
  documentType: true,
  createdBy: true,
  lastEditedBy: true
};

export const DOCUMENT_TYPE_FIELD_ENUM_KEYS = ["dataType"];

export const DOCUMENT_TYPE_FIELD_REQUIRED_KEYS = [
  "name",
  "dataType",
  "isMandatory",
  "documentType",
  "description"
];
