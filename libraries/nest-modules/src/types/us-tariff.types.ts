/**
 * Raw row data from HTS PGA report XLSX file
 */
export interface HTSPGAFileRow {
  "Tariff Number": string;
  Description: string;
  "Effective Date": string | Date;
  "Expiration Date": string | Date;
  "Census Qty 1": string;
  "Census Qty 2": string;
  "Census Qty 3": string;
  "AD Duty": string;
  "CV Duty": string;
  "PGA 1": string;
  "PGA 2": string;
  "PGA 3": string;
  "PGA 4": string;
  "PGA 5": string;
  "PGA 6": string;
  "PGA 7": string;
  "PGA 8": string;
  "PGA 9": string;
  "PGA 10": string;
}

/**
 * Statistics from tariff import operation
 */
export interface TariffImportStats {
  inserted: number;
  updated: number;
  deleted: number;
  processed: number;
  errors: number;
}

/**
 * Processed tariff data ready for database insertion
 */
export interface ProcessedTariffData {
  hsCode: string;
  description: string;
  effectiveDate: Date;
  expiryDate: Date;
  censusQty1: string | null;
  censusQty2: string | null;
  censusQty3: string | null;
  adDuty: boolean | null;
  cvDuty: boolean | null;
  pgaCodes: string[] | null;
}
