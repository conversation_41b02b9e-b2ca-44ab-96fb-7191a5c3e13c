import { FindOptionsRelations } from "typeorm";
import { SimplifiedTradePartner, TradePartner } from "../entities";

export enum PartnerType {
  VENDOR = "vendor",
  TRUCKER = "trucker",
  IMPORTER = "importer",
  SHIPPER = "shipper",
  MANUFACTURER = "manufacturer",

  AIR_CARRIER = "air-carrier",
  CUSTOMS_BROKER = "customs-broker",
  FORWARDER = "forwarder",
  OCEAN_CARRIER = "ocean-carrier",
  RAIL_COMPANY = "rail-company",
  TERMINAL = "terminal",
  WAREHOUSE = "warehouse"
}

export enum TradePartnerColumn {
  id = "id",
  partnerType = "partnerType",
  vendorCode = "vendorCode",
  eManifestCarrierCode = "eManifestCarrierCode",
  name = "name",
  email = "email",
  phoneNumber = "phoneNumber",
  address = "address",
  city = "city",
  state = "state",
  postalCode = "postalCode",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  countryId = "countryId",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export const FIND_TRADE_PARTNER_RELATIONS: FindOptionsRelations<TradePartner> = {
  country: true,
  organization: true,
  createdBy: true,
  lastEditedBy: true
};

export type TradePartnerLinkedRelationKey = keyof Omit<
  TradePartner,
  keyof SimplifiedTradePartner | "country" | "organization" | "createdBy" | "lastEditedBy"
>;

export class TradePartnerUsageCount implements Record<TradePartnerLinkedRelationKey, number> {
  purchaserCommercialInvoice: number;
  exporterCommercialInvoice: number;
  vendorCommercialInvoice: number;
  manufacturerCommercialInvoice: number;
  shipToCommercialInvoice: number;
  carrierShipments: number;
  manufacturerShipments: number;
  shipperShipments: number;
  consigneeShipments: number;
  forwarderShipments: number;
  truckerShipments: number;
  pickupLocationShipments: number;
  vendorProducts: number;
  manufacturerProducts: number;
  manufacturerOgdFilings: number;
}

export const TRADE_PARTNER_ENUM_KEYS = ["partnerType"];
export const TRADE_PARTNER_REQUIRED_KEYS = ["partnerType", "name"];
