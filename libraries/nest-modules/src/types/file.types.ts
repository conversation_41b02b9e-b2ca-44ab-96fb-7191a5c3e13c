import { FindOptionsRelations } from "typeorm";
import { File } from "../entities";

export enum FileColumn {
  id = "id",
  path = "path",
  name = "name",
  parseResult = "parseResult",
  parser = "parser",
  numPages = "numPages",
  status = "status",
  mimeType = "mimeType",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById",
  batchId = "batchId",
  hash = "hash"
}

export const FIND_FILE_RELATIONS: FindOptionsRelations<File> = {
  organization: true,
  createdBy: true,
  lastEditedBy: true,
  documents: {
    shipment: true
  }
};

export enum FileStatus {
  // default
  PENDING = "pending", // The file is uploaded, and is pending to be processed

  // normal
  PARSING = "parsing", // The file is being parsed
  SPLITTING = "splitting", // Splitting the documents in the file
  PROCESSED = "processed", // The file is processed

  NO_DOCUMENT_DETECTED = "no_document_detected", // No document detected in the file

  // error
  FILE_CORRUPTED = "file_corrupted", // specific error for file corrupted
  PARSE_FAILED = "parse_failed", // specific error for parsing failed
  SPLIT_FAILED = "split_failed", // specific error for split failed

  ERROR = "error" // general error
}

export const FILE_ENUM_KEYS: string[] = ["status"];

export const FILE_REQUIRED_KEYS = ["path", "name", "organization"];
