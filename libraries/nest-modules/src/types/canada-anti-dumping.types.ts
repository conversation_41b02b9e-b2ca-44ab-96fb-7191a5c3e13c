import { FindOptionsRelations } from "typeorm";
import { CanadaAntiDumping } from "../entities";

export enum CanadaAntiDumpingColumn {
  id = "id",
  code = "code",
  case = "case",
  dumpingCountry = "dumpingCountry",
  subsidyCountry = "subsidyCountry",
  caseType = "caseType",
  productDefinition = "productDefinition",
  exclusion = "exclusion",
  hsCodes = "hsCodes",
  antiDumpingDutyLiability = "antiDumpingDutyLiability",
  countervailingDutyLiability = "countervailingDutyLiability",
  createdById = "createdById",
  createdDate = "createdDate",
  lastEditedById = "lastEditedById",
  lastEditedDate = "lastEditedDate"
}

export const FIND_CANADA_ANTI_DUMPING_RELATIONS: FindOptionsRelations<CanadaAntiDumping> = {
  createdBy: true,
  lastEditedBy: true
};

export const CANADA_ANTI_DUMPING_REQUIRED_KEYS = [
  "code",
  "case",
  "dumpingCountry",
  "subsidyCountry",
  "caseType",
  "productDefinition",
  "exclusion",
  "hsCodes",
  "antiDumpingDutyLiability",
  "countervailingDutyLiability"
];

export const CANADA_ANTI_DUMPING_MODULE_OPTIONS = "CANADA_ANTI_DUMPING_MODULE_OPTIONS";
