import { FindOptionsRelations } from "typeorm";
import { DocusignToken } from "../entities";

export enum PoaStatus {
  SENT = "sent",
  SIGNED = "signed"
}

export interface DocusignModuleOptions {
  integrationKey: string;
  secretKey: string;
  poaTemplateId: string;
  baseUrl: string;
}
export const DOCUSIGN_MODULE_OPTIONS = "DOCUSIGN_MODULE_OPTIONS";

export const FIND_DOCUSIGN_TOKEN_RELATIONS: FindOptionsRelations<DocusignToken> = {
  createdBy: true,
  lastEditedBy: true
};

type DocusignTab = {
  tabLabel: string;
  documentId?: string;
  pageNumber?: string;
  value?: string;
  width?: string;
  height?: string;
  xPosition?: string;
  yPosition?: string;
  scaleValue?: string;
};
export interface DocusignDocument {
  name: string;
  templateId?: string;
  documentBase64?: string;
  documentId?: string;
  fileExtension: string;
  tabs?: {
    prefillTabs?: {
      textTabs?: Array<DocusignTab>;
    };
  };
}

export interface DocusignSigner {
  email: string;
  name: string;
  recipientId: string;
  tabs?: {
    signHereTabs?: Array<DocusignTab>;
    dateSignedTabs?: Array<DocusignTab>;
  };
}

type DocusignTemplateRole = {
  email: string;
  name: string;
  roleName: string;
  tabs?: {
    textTabs?: Array<DocusignTab>;
    signHereTabs?: Array<DocusignTab>;
    dateSignedTabs?: Array<DocusignTab>;
  };
};

export interface DocusignCreateEnvelopeRequestBody {
  status: "created" | "sent";
  templateId?: string;
  emailSubject: string;
  emailBlurb: string;
  documents?: Array<DocusignDocument>;
  recipients?: {
    signers: Array<DocusignSigner>;
  };
  templateRoles?: Array<DocusignTemplateRole>;
}

export interface DocusignCreateEnvelopeResponse {
  envelopeId: string;
  status: "created" | "sent";
  statusDateTime: string;
  uri: string;
}

export type SendPoaResponse = {
  envelopeId?: string;
};
