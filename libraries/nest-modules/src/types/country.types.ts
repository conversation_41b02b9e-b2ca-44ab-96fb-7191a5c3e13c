import { FindOptionsRelations } from "typeorm";
import { Country } from "../entities";

export enum CountryColumn {
  id = "id",
  name = "name",
  alpha3 = "alpha3",
  alpha2 = "alpha2",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export const FIND_COUNTRY_RELATIONS: FindOptionsRelations<Country> = {
  createdBy: true,
  lastEditedBy: true
};
