import { FindOptionsRelations } from "typeorm";
import { OgdFiling } from "../entities";
import { CanadaGovernmentAgency } from "./canada-ogd.types";

export enum OgdFilingType {
  DEFAULT = "default",
  GENERATED = "generated",
  MANUAL = "manual"
}

/** GAC options */
export enum GeneralImportPermit {
  GIP80 = "gip80",
  GIP81 = "gip81",
  GIP83 = "gip83"
}

/** TC options */
export enum TCTireEndUse {
  TC01 = "TC01",
  TC02 = "TC02",
  TC03 = "TC03",
  TC04 = "TC04",
  TC05 = "TC05"
}
export enum TCTireClass {
  TC01 = "TC01",
  TC02 = "TC02",
  TC03 = "TC03"
}
export enum TCTireType {
  TC04 = "TC04",
  TC05 = "TC05"
}
export enum TCTireSize {
  TC06 = "TC06",
  TC07 = "TC07",
  TC08 = "TC08",
  TC09 = "TC09",
  TC10 = "TC10",
  TC11 = "TC11",
  TC12 = "TC12",
  TC13 = "TC13"
}
export enum TCTireCompliance {
  TC01 = "TC01",
  TC02 = "TC02",
  TC07 = "TC07",
  TC08 = "TC08"
}

/** ECCC options */
export enum ECCCSubType {
  EMISSIONS = "Emissions",
  WILDLIFE = "Wildlife"
}
export enum ECCCWildlifeCompliance {
  ENDANGERED = "EC17",
  NOT_ENDANGERED = "EC18"
}
export enum ECCCEmissionProgram {
  EXEMPT = "XE99"
}

/** NRCAN options */
export enum NRCANSubType {
  ENERGY_EFFICIENCY = "EnergyEfficiency",
  EXPLOSIVES = "Explosives"
}

export enum OgdFilingColumn {
  id = "id",
  isExcluded = "isExcluded",
  extensionCode = "extensionCode",
  endUse = "endUse",
  airsType = "airsType",
  airsReferenceNumber = "airsReferenceNumber",
  productCategory = "productCategory",
  generalImportPermit = "generalImportPermit",
  brandName = "brandName",
  tireType = "tireType",
  tireSize = "tireSize",
  tireCompliance = "tireCompliance",
  tireClass = "tireClass",
  ecccSubType = "ecccSubType",
  wildlifeCompliance = "wildlifeCompliance",
  emissionProgram = "emissionProgram",
  nrcanSubType = "nrcanSubType",
  isRegulated = "isRegulated",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  ogdId = "ogdId",
  organizationId = "organizationId",
  manufacturerId = "manufacturerId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

/**
 * Table of the required and optional properties for each agency filing.
 */
export const AGENCY_PROPS: Record<
  CanadaGovernmentAgency,
  { required: Array<OgdFilingColumn>; optional: Array<OgdFilingColumn> }
> = {
  [CanadaGovernmentAgency.CFIA]: {
    required: [],
    optional: [
      OgdFilingColumn.extensionCode,
      OgdFilingColumn.endUse,
      OgdFilingColumn.airsType,
      OgdFilingColumn.airsReferenceNumber
    ]
  },
  [CanadaGovernmentAgency.GAC]: {
    required: [OgdFilingColumn.generalImportPermit],
    optional: []
  },
  [CanadaGovernmentAgency.HC]: {
    required: [OgdFilingColumn.endUse, OgdFilingColumn.productCategory],
    optional: []
  },
  [CanadaGovernmentAgency.ECCC]: {
    required: [OgdFilingColumn.ecccSubType],
    optional: [OgdFilingColumn.wildlifeCompliance, OgdFilingColumn.emissionProgram]
  },
  [CanadaGovernmentAgency.CNSC]: {
    required: [],
    optional: []
  },
  [CanadaGovernmentAgency.DFO]: {
    required: [],
    optional: []
  },
  [CanadaGovernmentAgency.PHAC]: {
    required: [],
    optional: []
  },
  [CanadaGovernmentAgency.NRCAN]: {
    required: [OgdFilingColumn.nrcanSubType, OgdFilingColumn.isRegulated],
    optional: []
  },
  [CanadaGovernmentAgency.TC]: {
    required: [],
    optional: [
      OgdFilingColumn.endUse,
      OgdFilingColumn.brandName,
      OgdFilingColumn.manufacturerId,
      OgdFilingColumn.tireType,
      OgdFilingColumn.tireSize,
      OgdFilingColumn.tireCompliance,
      OgdFilingColumn.tireClass
    ]
  }
};

/**
 * Table of the required and optional properties for ECCC filing.
 */
export const ECCC_PROPS: Record<
  ECCCSubType,
  {
    required: Array<OgdFilingColumn>;
    optional: Array<OgdFilingColumn>;
  }
> = {
  [ECCCSubType.EMISSIONS]: {
    required: [OgdFilingColumn.emissionProgram],
    optional: []
  },
  [ECCCSubType.WILDLIFE]: {
    required: [OgdFilingColumn.wildlifeCompliance],
    optional: []
  }
};

export const FIND_OGD_FILING_RELATIONS: FindOptionsRelations<OgdFiling> = {
  ogd: true,
  organization: true,
  manufacturer: true,
  createdBy: true,
  lastEditedBy: true
};

export const NON_OGD_FILING_KEYS = [
  OgdFilingColumn.id,
  OgdFilingColumn.isExcluded,
  OgdFilingColumn.createDate,
  OgdFilingColumn.lastEditDate,
  OgdFilingColumn.ogdId,
  OgdFilingColumn.organizationId,
  OgdFilingColumn.createdById,
  OgdFilingColumn.lastEditedById
];
export const OGD_FILING_ENUM_KEYS = [
  "generalImportPermit",
  "tireType",
  "tireSize",
  "tireCompliance",
  "tireClass",
  "ecccSubType",
  "wildlifeCompliance",
  "emissionProgram",
  "nrcanSubType"
];
export const OGD_FILING_REQUIRED_KEYS = ["isExcluded", "ogd", "organization"];
