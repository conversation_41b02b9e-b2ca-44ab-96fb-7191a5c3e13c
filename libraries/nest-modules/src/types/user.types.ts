import { FindOptionsRelations } from "typeorm";
import { CrudOptions } from "./base.types";
import { User } from "../entities/user.entity";

export enum UserPermission {
  BACKOFFICE_ADMIN = "backoffice-admin",
  ORGANIZATION_ADMIN = "organization-admin",
  BASIC = "basic"
}

export interface UserModuleOptions {
  user: CrudOptions;
  organization: CrudOptions;
}
export const USER_CRUD_OPTIONS = "USER_CRUD_OPTIONS";
export const ORGANIZATION_CRUD_OPTIONS = "ORGANIZATION_CRUD_OPTIONS";

export enum UserColumn {
  id = "id",
  email = "email",
  name = "name",
  googleUserId = "googleUserId",
  permission = "permission",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export const FIND_USER_RELATIONS: FindOptionsRelations<User> = {
  organization: true,
  createdBy: true,
  lastEditedBy: true
};

export const USER_ENUM_KEYS = ["permission"];

export enum UserEvent {
  SEND_RESET_PASSWORD_EMAIL = "user.send-reset-password-email"
}
