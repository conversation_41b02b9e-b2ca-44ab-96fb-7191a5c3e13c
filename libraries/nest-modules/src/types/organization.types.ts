import { FindOptionsRelations } from "typeorm";
import { Organization } from "../entities/organization.entity";

export enum OrganizationColumn {
  id = "id",
  name = "name",
  organizationType = "organizationType",
  customsBroker = "customsBroker",
  skipPoaCheck = "skipPoaCheck",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export const FIND_ORGANIZATION_RELATIONS: FindOptionsRelations<Organization> = {
  createdBy: true,
  lastEditedBy: true
};

export enum PredefinedOrganizationName {
  ANTEK_LOGISTICS = "Antek Logistics"
}

export enum OrganizationType {
  BACKOFFICE = "Backoffice",
  TRADING = "Trading",
  AGENT = "Agent",
  DEMO = "Demo"
}

export enum OrganizationCustomsBroker {
  USAV = "usav",
  CLARO = "claro"
}

export const ORGANIZATION_REQUIRED_KEYS = ["name", "organizationType", "skipPoaCheck"];
export const ORGANIZATION_ENUM_KEYS = ["organizationType", "customsBroker"];
