import { FindOptionsRelations } from "typeorm";

import { CanadaSubLocation } from "../entities";

export enum CanadaSubLocationColumn {
  id = "id",
  name = "name",
  code = "code",
  portCode = "portCode",
  address = "address",
  createDate = "createDate",
  lastEditDate = "lastEditDate"
}

export const FIND_CANADA_SUB_LOCATION_RELATIONS: FindOptionsRelations<CanadaSubLocation> = {
  port: true
};
