export const SHIPPING_LINE_MAPPINGS = {
  ZIM: "ZIMU",
  YML: "YMLU",
  WWL: "WWL",
  WLS: "WLS",
  WHL: "WHL",
  Westwood: "WWSU",
  WEC: "WECU",
  USL: "USLU",
  UAL: "UALC",
  Turkon: "TRKU",
  TOT: "TOTE",
  TSC: "TOHO",
  TS: "THZS",
  Swire: "CHVW",
  Sinokor: "SKLU",
  "Siem Car": "SCYE",
  Seago: "SEJJ",
  SeaFreight: "SEFN",
  SML: "SMLU",
  SCI: "SCIU",
  SAM: "SKII",
  Sallaum: "SLAQ",
  Safmarine: "SAFM",
  "Rickmers Line": "RCKI",
  PL: "PLLU",
  PMO: "PMOL",
  PIL: "PCIU",
  PHT: "PSHI",
  OOCL: "OOLU",
  ONE: "ONEY",
  Nordana: "NODA",
  Neptune: "NOSU",
  NDS: "NIDU",
  MSC: "MSCU",
  MISC: "MISC",
  MCC: "MCCQ",
  <PERSON><PERSON>: "MATS",
  <PERSON><PERSON>: "MRUB",
  Marfret: "MFTU",
  MAERSK: "MAEU",
  "Linea Messina": "LMCU",
  LGL: "LGLT",
  Libra: "CLIB",
  KMTC: "KMTU",
  "King Ocean": "KOSL",
  "K Line": "KKLU",
  "Interocean Lines": "INOC",
  IMC: "IDMC",
  ICL: "IILU",
  Horizon: "HRZU",
  Hoegh: "HUAU",
  HMM: "HDMU",
  "Hapag Lloyd": "HLCU",
  "HAMBURG SUD": "SUDU",
  GES: "GESC",
  GWF: "UBCU",
  GSL: "GSLU",
  Grimaldi: "GRIU",
  "Grieg Star": "ACSU",
  GAL: "GFAL",
  FESCO: "FESO",
  EGL: "EGLV",
  Eukor: "EUKO",
  Emirates: "ESPU",
  Eimskip: "EIMU",
  "Ecuadorian Line": "EQLI",
  Dole: "DOLQ",
  Delmas: "DAAE",
  CSCL: "CHNJ",
  "CSAV Norasia": "NSLU",
  CSAV: "CHIW",
  Crowley: "CAMN",
  COSCO: "COSU",
  "CNC Line": "11DX",
  "CMA CGM": "CMDU",
  CHIPOLBROK: "CPJQ",
  CCNI: "CNIU",
  CGL: "CEGL",
  BCL: "BCLU",
  Bahri: "NSAU",
  ARRC: "AROF",
  Arkas: "ARKU",
  APL: "APLU",
  ANL: "ANNU",
  AML: "AKMR",
  Alianca: "ANRM",
  ACL: "ACLU"
};

export const SHIPPING_LINE_FREE_DAYS = {
  COSCO: 5,
  MSC: 5,
  ONE: 4,
  CMA: 3,
  SML: 5,
  EGL: 4,
  OOCL: 4,
  ZIM: 3,
  YML: 2,
  HMM: 2,
  MAERSK: 0,
  HPL: 0
  // missing MAERSK and HPL
};
export const PORT_NAME_MAPPINGS = {
  CENTERM: "CENTERM",
  GCT: "GCT",
  FRASER: "FRASER"
};

export const RAILWAY_NAME_MAPPINGS = {
  CN: "CN",
  CP: "CP"
};

export const APIFY_RETURN_SHIPPING_LINE_ACTOR_MAPPINGS = {
  EGL: "antek~container-return-egl",
  COSCO: "antek~container-return-cosco",
  MSC: "antek~container-return-msc",
  ONE: "antek~container-return-one",
  "CMA CGM": "antek~container-return-cma",
  SML: "antek~container-return-sml",
  MAERSK: "antek~container-return-maersk"
};

export const APIFY_NEW_SHIPPING_LINE_ACTOR_MAPPINGS = {
  EGL: "antek~container-status-check-egl",
  COSCO: "antek~container-status-check-cosco",
  MSC: "antek~container-status-check-msc",
  ONE: "antek~container-status-check-one",
  "CMA CGM": "antek~container-status-check-cma",
  SML: "antek~container-status-check-sml",
  MAERSK: "antek~container-status-check-maersk"
};

export const APIFY_PORT_ACTOR_MAPPINGS = {
  CENTERM: "antek~vancouver-centerm-port-check",
  FRASER: "antek~vancouver-fraser-port-check",
  GCT: "antek~vancouver-gct-port-check"
};

export const APIFY_RAILWAY_ACTOR_MAPPINGS = {
  CP: "antek~cp-container-status-check",
  CN: "antek~cn-container-status-check"
};

export const APIFY_NEW_SHIPPING_STORE_NAME = "antek~Container-Tracking";
export const APIFY_RAILWAY_STORE_NAME = "antek~carlo-railway-tracking";
export const APIFY_PORT_STORE_NAME = "antek~claro-port-tracking";
export const APIFY_RETURN_SHIPPING_STORE_NAME = "antek~Container-Return";

export const PORT_INPUT_MAPPINGS = {
  CENTERM: {
    url: "https://login.cargoes.com/auth/realms/DPWorld/protocol/openid-connect/auth?client_id=CA&redirect_uri=http://ca-community.cargoes.com/login&response_type=code&state=ijW2Ae"
  },
  GCT: {
    url: "https://webservices.globalterminals.com/tsiWebServiceClient/importContainerStatus.jsp"
  }
};
