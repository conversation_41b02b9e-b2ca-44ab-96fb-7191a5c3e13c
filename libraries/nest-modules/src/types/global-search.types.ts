export type SearchField = {
  field: string;
  relation?: string;
  fts?: boolean;
  alias?: string;
  dataType?: "string" | "enum";
  display?: "title" | "subtitle";
};
export enum SearchContext {
  SHIPMENT = "shipment",
  PRODUCT = "product",
  EMAIL = "email",
  DOCUMENT = "document"
}

export const searchRegistry: Record<SearchContext, SearchField[]> = {
  [SearchContext.SHIPMENT]: [
    { field: "hblNumber", display: "title" },
    { field: "mblNumber" },
    { field: "transactionNumber" },
    { field: "customsFileNumber" },
    { field: "cargoControlNumber" },
    { field: "modeOfTransport", dataType: "enum", display: "subtitle" },
    { field: "containerNumber", relation: "containers", display: "subtitle" },
    { field: "containerType", relation: "containers", dataType: "enum", display: "subtitle" },
    { field: "name", relation: "shipper", fts: true },
    { field: "name", relation: "consignee", fts: true }
  ],
  [SearchContext.PRODUCT]: [
    { field: "partNumber", display: "title" },
    { field: "hsCode", display: "subtitle" },
    { field: "sku" },
    { field: "upc" },
    { field: "vendorPartNumber" },
    { field: "description", fts: true, display: "subtitle" },
    { field: "name", relation: "origin", fts: true, display: "subtitle" }
  ],
  [SearchContext.EMAIL]: [
    { field: "subject", fts: true, display: "title" },
    { field: "text", fts: true },
    { field: "html", fts: true },
    { field: "hblNumber", relation: "emailThread.shipment", display: "subtitle" },
    { field: "mblNumber", relation: "emailThread.shipment" },
    { field: "transactionNumber", relation: "emailThread.shipment" },
    { field: "customsFileNumber", relation: "emailThread.shipment" },
    { field: "cargoControlNumber", relation: "emailThread.shipment" }
  ],
  [SearchContext.DOCUMENT]: [
    { field: "name", display: "title" },
    { field: "hblNumber", relation: "shipment", display: "subtitle" },
    { field: "mblNumber", relation: "shipment" },
    { field: "transactionNumber", relation: "shipment" },
    { field: "customsFileNumber", relation: "shipment" },
    { field: "cargoControlNumber", relation: "shipment" }
  ]
};
