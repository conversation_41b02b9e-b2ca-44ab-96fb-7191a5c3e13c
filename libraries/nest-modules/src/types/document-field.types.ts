import { FindOptionsRelations } from "typeorm";
import { DocumentField } from "../entities";

export enum DocumentFieldColumn {
  id = "id",
  name = "name",
  dataType = "dataType",
  value = "value",
  originalValue = "originalValue",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  documentId = "documentId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export const FIND_DOCUMENT_FIELD_RELATIONS: FindOptionsRelations<DocumentField> = {
  document: true,
  createdBy: true,
  lastEditedBy: true
};

export const DOCUMENT_FIELD_ENUM_KEYS = ["dataType"];

export const DOCUMENT_FIELD_REQUIRED_KEYS = ["name", "dataType", "value", "document"];
