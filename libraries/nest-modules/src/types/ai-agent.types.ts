/**
 * AI Agent types and constants for email processing
 *
 * These types are used across multiple modules for AI/LLM processing of emails.
 * They use UPPER_CASE format to distinguish from database-level EmailIntentType
 * which uses kebab-case format.
 */

export const FILTERED_EMAIL_INTENTS = [
  "CREATE_SHIPMENT",
  "UPDATE_SHIPMENT",
  "CREATE_COMMERCIAL_INVOICE",
  "UPDATE_COMMERCIAL_INVOICE",
  "CREATE_CERTIFICATE_OF_ORIGIN",
  "UPDATE_CERTIFICATE_OF_ORIGIN",
  "GET_SHIPMENT_STATUS",
  "PROCESS_DOCUMENT",
  "REQUEST_RUSH_PROCESSING",
  "REQUEST_MANUAL_PROCESSING",
  "REQUEST_HOLD_SHIPMENT",
  "REQUEST_CAD_DOCUMENT",
  "REQUEST_RNS_PROOF",
  "DOCUMENTATION_COMING",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>DGE_DOCUMENTS",
  "<PERSON>K<PERSON><PERSON><PERSON>DGE_MISSING_DOCUMENTS",
  "UNSORTED"
] as const;

export const EMAIL_INTENTS = [...FILTERED_EMAIL_INTENTS, "UNKNOWN", "SPAM"] as const;

export const EMAIL_ACTION_TYPES = ["FIELD_UPDATE", "SUBMIT_DOCUMENT"] as const;

export const DOCUMENT_TYPES = [
  "BILL_OF_LADING",
  "COMMERCIAL_INVOICE",
  "PACKING_LIST",
  "CERTIFICATE_OF_ORIGIN",
  "ARRIVAL_NOTICE",
  "SHIPMENT"
] as const;

export interface FieldConfig {
  description: string;
  type: string;
  values?: string[];
  aliases?: string[];
  rules?: string[];
}
