import { FindOptionsRelations } from "typeorm";
import { CanadaTreatmentCode } from "../entities";

export enum CanadaTreatmentCodeColumn {
  id = "id",
  code = "code",
  name = "name",
  abbreviation = "abbreviation",
  createdById = "createdById",
  createdDate = "createdDate",
  lastEditedById = "lastEditedById",
  lastEditedDate = "lastEditedDate"
}

export const FIND_CANADA_TREATMENT_CODE_RELATIONS: FindOptionsRelations<CanadaTreatmentCode> = {
  createdBy: true,
  lastEditedBy: true,
  countries: true
};

export const CANADA_TREATMENT_CODE_REQUIRED_KEYS = ["code", "name", "abbreviation"];

export const CANADA_TREATMENT_CODE_MODULE_OPTIONS = "CANADA_TREATMENT_CODE_MODULE_OPTIONS";
