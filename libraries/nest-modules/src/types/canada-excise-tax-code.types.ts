import { FindOptionsRelations } from "typeorm";
import { CanadaExciseTaxCode } from "../entities";

export enum CanadaExciseTaxCodeColumn {
  id = "id",
  category = "category",
  code = "code",
  explanation = "explanation",
  rateType = "rateType",
  createdById = "createdById",
  createdDate = "createdDate",
  lastEditedById = "lastEditedById",
  lastEditedDate = "lastEditedDate"
}

export const FIND_CANADA_EXCISE_TAX_CODE_RELATIONS: FindOptionsRelations<CanadaExciseTaxCode> = {
  createdBy: true,
  lastEditedBy: true
};

export const CANADA_EXCISE_TAX_CODE_REQUIRED_KEYS = ["category", "code", "explanation"];

export const CANADA_EXCISE_TAX_CODE_MODULE_OPTIONS = "CANADA_EXCISE_TAX_CODE_MODULE_OPTIONS";
