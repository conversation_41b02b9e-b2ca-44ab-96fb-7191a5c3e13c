import { FindOptionsRelations } from "typeorm";
import { Email, EmailUpdate } from "../entities";

export enum EmailStatus {
  // Get Gmail Message
  UPLOADING_ATTACHMENTS = "uploading-attachments",
  SAVED = "saved",
  FAILED_SAVING_EMAIL = "failed-saving-email",

  // Find Thread Shipment
  AWAIT_SHIPMENT_SEARCH = "await-shipment-search",
  COMPLETED_SHIPMENT_SEARCH = "completed-shipment-search",
  FAILED_SHIPMENT_SEARCH = "failed-shipment-search",

  // Extract User Intents
  ANALYZING_INTENTS = "analyzing-intents",
  INTENTS_ANALYZED = "intents-analyzed",
  FAILED_ANALYZING_INTENTS = "failed-analyzing-intents",

  // Process User Intents
  PROCESSING_INTENTS = "processing-intents",
  INTENTS_PROCESSED = "intents-processed",
  FAILED_PROCESSING_INTENTS = "failed-processing-intents",

  // Aggregate Email
  AGGREGATING_EMAIL = "aggregating-email",
  EMAIL_AGGREGATED = "email-aggregated",
  FAILED_AGGREGATING_EMAIL = "failed-aggregating-email",

  // Generate Email Response
  RESPONDING = "responding",
  RESPONDED = "responded",
  FAILED_RESPONDING = "failed-responding",

  // Requires manual review
  MANUAL_REVIEW = "manual-review",

  // Spam email
  SPAM = "spam",

  // Deleted on Gmail
  DELETED = "deleted",

  // System email skipped
  SYSTEM_EMAIL_SKIPPED = "system-email-skipped"
}

export enum EmailOrigin {
  INBOX = "inbox",
  SENT = "sent"
}

export enum EmailColumn {
  id = "id",
  inboxEmail = "inboxEmail",
  origin = "origin",
  status = "status",
  gmailId = "gmailId",
  threadId = "threadId",
  historyId = "historyId",
  receiveDate = "receiveDate",
  from = "from",
  replyTo = "replyTo",
  to = "to",
  cc = "cc",
  subject = "subject",
  text = "text",
  html = "html",
  userIntents = "userIntents",
  requestSummary = "requestSummary",
  processingOutcome = "processingOutcome",
  error = "error",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}
export const FIND_EMAIL_RELATIONS: FindOptionsRelations<Email> = {
  organization: true,
  createdBy: true,
  lastEditedBy: true,
  updates: {
    documents: true
  }
};
export const EMAIL_ENUM_KEYS = ["origin", "status"];
export const EMAIL_NON_ID_KEYS = ["gmailId", "threadId", "historyId"];
export const EMAIL_REQUIRED_KEYS = [
  "inboxEmail",
  "origin",
  "status",
  "gmailId",
  "threadId",
  "receiveDate",
  "from"
];
export enum EmailIntentType {
  CREATE_SHIPMENT = "create-shipment",
  UPDATE_SHIPMENT = "update-shipment",
  CREATE_COMMERCIAL_INVOICE = "create-commercial-invoice",
  UPDATE_COMMERCIAL_INVOICE = "update-commercial-invoice",
  CREATE_CERTIFICATE_OF_ORIGIN = "create-certificate-of-origin",
  UPDATE_CERTIFICATE_OF_ORIGIN = "update-certificate-of-origin",
  GET_SHIPMENT_STATUS = "get-shipment-status",
  PROCESS_DOCUMENT = "process-document",
  DOCUMENTATION_COMING = "documentation-coming",
  REQUEST_RUSH_PROCESSING = "request-rush-processing",
  REQUEST_MANUAL_PROCESSING = "request-manual-processing",
  REQUEST_HOLD_SHIPMENT = "request-hold-shipment",
  REQUEST_CAD_DOCUMENT = "request-cad-document",
  REQUEST_RNS_PROOF = "request-rns-proof",
  SPAM = "spam",
  UNSORTED = "unsorted"
}

export enum EmailUpdateStatus {
  // Waiting to be applied
  PENDING = "pending",
  // Successfully applied
  SUCCESS = "success",
  // Failed to apply
  FAILED = "failed"
}

export enum EmailUpdateColumn {
  status = "status",
  intent = "intent",
  updateJson = "updateJson",
  failedReasons = "failedReasons",
  emailId = "emailId"
}
export const FIND_EMAIL_UPDATE_RELATIONS: FindOptionsRelations<EmailUpdate> = {
  email: true,
  documents: true
};
export const EMAIL_UPDATE_ENUM_KEYS = ["status", "intent"];
export const EMAIL_UPDATE_REQUIRED_KEYS = ["status", "intent", "email"];
