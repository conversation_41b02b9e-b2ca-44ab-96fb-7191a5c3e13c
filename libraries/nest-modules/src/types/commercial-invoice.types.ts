import { FindOptionsRelations } from "typeorm";
import { CommercialInvoice } from "../entities/commercial-invoice.entity";

export enum PackageUOM {
  AEROSOL = "ae",
  AMPOULE_NON_PROTECTED = "am",
  AMPOULE_PROTECTED = "ap",
  ATOMIZER = "at",
  BAG = "bg",
  BALE_COMPRESSED = "bl",
  BALE_NON_COMPRESSED = "bn",
  BALLOON_NON_PROTECTED = "bf",
  BALLOON_PROTECTED = "bp",
  BAR = "br",
  BARREL = "ba",
  BARS_IN_BUNDLE = "bz",
  BASKET = "bk",
  BEER_CRATE = "cb",
  BIN = "bi",
  BOARD = "bd",
  BOARD_IN_BUNDLE = "by",
  BOBBIN = "bb",
  BOLT = "bt",
  BOTTLE_NON_PROTECTED_CYLINDRICAL = "bo",
  BOTTLE_NON_PROTECTED_BULBOUS = "bs",
  BOTTLE_PROTECTED_CYLINDRICAL = "bq",
  BOTTLE_PROTECTED_BULBOUS = "bv",
  BOTTLE_CRATE = "bc",
  BOX = "bx",
  BUCKET = "bj",
  BULK_LIQUEFIED_GAS = "vq",
  BULK_GAS = "vg",
  BULK_LIQUID = "vl",
  BULK_SOLID_FINE_PARTICLES = "vy",
  BULK_SOLID_GRANULAR_PARTICLES = "vr",
  BULK_SOLID_LARGE_PARTICLES = "vo",
  BUNCH = "bh",
  BUNDLE = "be",
  BUTT = "bu",
  CAGE = "cg",
  CAN_RECTANGULAR = "ca",
  CAN_CYLINDRICAL = "cx",
  CANISTER = "ci",
  CANVAS = "cz",
  CARBOY_NON_PROTECTED = "co",
  CARBOY_PROTECTED = "cp",
  CARTON = "ct",
  CASE = "cs",
  CASK = "ck",
  CHEST = "ch",
  CHURN = "cc",
  COFFER = "cf",
  COFFIN = "cj",
  COIL = "cl",
  COVER = "cv",
  CRATE = "cr",
  CREEL = "ce",
  CUP = "cu",
  CYLINDER = "cy",
  DEMIJOHN_NON_PROTECTED = "dj",
  DEMIJOHN_PROTECTED = "dp",
  DRUM = "dr",
  ENVELOPE = "en",
  FILMPACK = "fp",
  FIRKIN = "fi",
  FLASK = "fl",
  FOOTLOCKER = "fo",
  FRAME = "fr",
  FRAME_CRATE = "fd",
  FRUIT_CRATE = "fc",
  GAS_BOTTLE = "gb",
  GIRDER = "gi",
  GIRDERS_IN_BUNDLE = "gz",
  HAMPER = "hr",
  HOGSHEAD = "hg",
  INGOT = "in",
  INGOTS_IN_BUNDLE = "iz",
  JAR = "jr",
  JERRICAN_RECTANGULAR = "jc",
  JERRICAN_CYLINDRICAL = "jy",
  JUG = "jg",
  JUTE_BAG = "jt",
  KEG = "kg",
  LOG = "lg",
  LOGS_IN_BUNDLE = "lz",
  MILK_CRATE = "mc",
  MULTIPLY_BAG = "mb",
  MULTIWALL_SACK = "ms",
  MAT = "mt",
  MATCH_BOX = "mx",
  NEST = "ns",
  NET = "nt",
  PACKAGE = "pk",
  PACKET = "pa",
  PAIL = "pl",
  PARCEL = "pc",
  PIPE = "pi",
  PIPES_IN_BUNDLE = "pz",
  PITCHER = "ph",
  PLANK = "pn",
  PLATE = "pg",
  PLATES_IN_BUNDLE = "py",
  POT = "pt",
  POUCH = "po",
  PALLET = "pf",
  REDNET = "rt",
  REEL = "rl",
  RING = "rg",
  ROD = "rd",
  RODS_IN_BUNDLE = "rz",
  SACHET = "sh",
  SACK = "sa",
  SEA_CHEST = "se",
  SHALLOW_CRATE = "sc",
  SHEET = "st",
  SHEET_METAL = "sm",
  SHEETS_IN_BUNDLE = "sz",
  SHRINK_WRAPPED = "sw",
  SKELETON_CASE = "sk",
  SKID = "sv",
  SLIPSHEET = "sl",
  SPINDLE = "sd",
  SUITCASE = "su",
  TANK_RECTANGULAR = "tk",
  TANK_CYLINDRICAL = "ty",
  TEA_CHEST = "tc",
  TIN = "tn",
  TRAY = "pu",
  TRUNK = "tr",
  TRUSS = "ts",
  TUB = "tb",
  TUBE = "tu",
  TUBE_COLLAPSIBLE = "td",
  TUBES_IN_BUNDLE = "tz",
  TUN = "to",
  UNPACKED = "ne",
  VACUUM_PACKED = "vp",
  VAT = "va",
  VIAL = "vi",
  WICKER_BOTTLE = "wb"
}

export const FIND_COMMERCIAL_INVOICE_RELATIONS: FindOptionsRelations<CommercialInvoice> = {
  commercialInvoiceLines: true,
  organization: true,
  shipment: true,
  countryOfExport: true,
  stateOfExport: true,
  exporter: true,
  purchaser: true,
  manufacturer: true,
  shipTo: true,
  vendor: true,
  createdBy: true,
  lastEditedBy: true
};

export const COMMERCIAL_INVOICE_REQUIRED_KEYS = [
  "invoiceNumber",
  // "currency",
  // "grossWeight",
  // "weightUOM",
  // "numberOfPackages",
  // "packageUOM",
  "organization",
  "shipment",
  "vendor"
];
export const COMMERCIAL_INVOICE_ENUM_KEYS = ["currency", "weightUOM", "packageUOM"];
export const COMMERCIAL_INVOICE_NON_ID_KEYS = ["candataId"];

export enum CommercialInvoiceColumn {
  id = "id",
  candataId = "candataId",
  invoiceNumber = "invoiceNumber",
  currency = "currency",
  invoiceDate = "invoiceDate",
  poNumber = "poNumber",
  grossWeight = "grossWeight",
  weightUOM = "weightUOM",
  numberOfPackages = "numberOfPackages",
  packageUOM = "packageUOM",
  includedTransCost = "includedTransCost",
  includedPackCost = "includedPackCost",
  includedMiscCost = "includedMiscCost",
  excludedTransCost = "excludedTransCost",
  excludedPackCost = "excludedPackCost",
  excludedMiscCost = "excludedMiscCost",
  valueIncludesDuty = "valueIncludesDuty",
  additionalInfo = "additionalInfo",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  shipmentId = "shipmentId",
  countryOfExportId = "countryOfExportId",
  stateOfExportId = "stateOfExportId",
  exporterId = "exporterId",
  vendorId = "vendorId",
  manufacturerId = "manufacturerId",
  shipToId = "shipToId",
  purchaserId = "purchaserId",
  organizationId = "organizationId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}
