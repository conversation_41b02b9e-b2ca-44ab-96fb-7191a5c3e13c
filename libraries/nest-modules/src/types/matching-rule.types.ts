import { FindOptionsRelations } from "typeorm";
import { MatchingRule } from "../entities";
import { CrudOptions } from "./base.types";

export enum MatchingRuleColumn {
  id = "id",
  status = "status",
  priority = "priority",
  name = "name",
  description = "description",
  sourceTable = "sourceTable",
  sourceId = "sourceId",
  destinationTable = "destinationTable",
  operator = "operator",
  expiryDate = "expiryDate",
  organizationId = "organizationId",
  createdById = "createdById",
  createDate = "createDate",
  lastEditedById = "lastEditedById",
  lastEditDate = "lastEditDate"
}

export const FIND_MATCHING_RULE_RELATIONS: FindOptionsRelations<MatchingRule> = {
  organization: true,
  createdBy: true,
  lastEditedBy: true,
  conditions: true
};

export const MATCHING_RULE_NON_ID_KEYS = ["sourceId"];

export const MATCHING_RULE_REQUIRED_KEYS = [
  "status",
  "priority",
  "name",
  "sourceTable",
  "sourceId",
  "destinationTable",
  "operator"
];

export const MATCHING_RULE_ENUM_KEYS = ["status", "sourceTable", "destinationTable", "operator"];

export const MATCHING_RULE_MODULE_OPTIONS = "MATCHING_RULE_MODULE_OPTIONS";

export type MatchingRuleModuleOptions = CrudOptions & {
  query: boolean;
};

export enum MatchingRuleStatus {
  PENDING = "pending",
  ACTIVE = "active",
  DISABLED = "disabled"
}

export enum MatchingRuleSourceDatabaseTable {
  CANADA_ANTI_DUMPING = "CanadaAntiDumping",
  CANADA_OGD = "CanadaOgd",
  CANADA_SIMA_CODE = "CanadaSimaCode",
  CANADA_TARIFF = "CanadaTariff",
  CANADA_TREATMENT_CODE = "CanadaTreatmentCode",
  CANADA_VFD_CODE = "CanadaVfdCode",
  CANADA_GST_EXEMPT_CODE = "CanadaGstExemptCode",
  CANADA_EXCISE_TAX_CODE = "CanadaExciseTaxCode",
  OGD_FILING = "OgdFiling",
  SIMA_FILING = "SimaFiling",
  CERTIFICATE_OF_ORIGIN = "CertificateOfOrigin"
}

export enum MatchingRuleDestinationDatabaseTable {
  PRODUCT = "Product"
}

/**
 * Operator used for chaining conditions within a rule
 */
export enum MatchingRuleOperator {
  AND = "and",
  OR = "or"
}

export enum MatchingRuleEvent {
  MATCHING_RULE_CLEAN_UP = "matching-rule.clean-up"
}
