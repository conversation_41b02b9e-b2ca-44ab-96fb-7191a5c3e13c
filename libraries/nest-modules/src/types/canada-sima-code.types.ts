import { FindOptionsRelations } from "typeorm";
import { CanadaSimaCode } from "../entities";

export enum CanadaSimaCodeColumn {
  id = "id",
  code = "code",
  explanation = "explanation",
  createdById = "createdById",
  createdDate = "createdDate",
  lastEditedById = "lastEditedById",
  lastEditedDate = "lastEditedDate"
}

export const FIND_CANADA_SIMA_CODE_RELATIONS: FindOptionsRelations<CanadaSimaCode> = {
  createdBy: true,
  lastEditedBy: true
};

export const CANADA_SIMA_CODE_REQUIRED_KEYS = ["code", "explanation"];

export const CANADA_SIMA_CODE_MODULE_OPTIONS = "CANADA_SIMA_CODE_MODULE_OPTIONS";
