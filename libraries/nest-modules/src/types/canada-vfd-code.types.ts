import { FindOptionsRelations } from "typeorm";
import { CanadaVfdCode } from "../entities";

export enum CanadaVfdCodeColumn {
  id = "id",
  code = "code",
  explanation = "explanation",
  createdById = "createdById",
  createdDate = "createdDate",
  lastEditedById = "lastEditedById",
  lastEditedDate = "lastEditedDate"
}

export const FIND_CANADA_VFD_CODE_RELATIONS: FindOptionsRelations<CanadaVfdCode> = {
  createdBy: true,
  lastEditedBy: true
};

export const CANADA_VFD_CODE_REQUIRED_KEYS = ["code", "explanation"];

export const CANADA_VFD_CODE_MODULE_OPTIONS = "CANADA_VFD_CODE_MODULE_OPTIONS";
