import { FieldValue, WhereFilterOp } from "firebase-admin/firestore";
import { EditImporterDto } from "../dto";
import { CarmStatus } from "./carm.types";
import { PoaStatus, SendPoaResponse } from "./docusign.types";

export interface FirebaseModuleOptions {
  projectId: string;
  privateKey: string;
  clientEmail: string;
}

export const FIREBASE_MODULE_OPTIONS = "FIREBASE_MODULE_OPTIONS";

export type WhereQuery = [string, WhereFilterOp, any];

//#region Onboarding
export enum CompanySize {
  LESS_THAN_5 = "<5",
  FIVE_TO_TEN = "5-10",
  ELEVEN_TO_FIFTY = "11-50",
  FIFTY_ONE_TO_TWO_HUNDRED = "51-200",
  TWO_HUNDRED_ONE_TO_FIVE_HUNDRED = "201-500",
  FIVE_HUNDRED_PLUS = "500+"
}
export enum MonthlyImportVolume {
  ONE = "1",
  TWO_TO_FOUR = "2-4",
  FIVE_TO_TEN = "5-10",
  TEN_TO_TWENTY = "10-20",
  TWENTY_TO_FIFTY = "20-50",
  FIFTY_TO_HUNDRED = "50-100",
  HUNDRED_PLUS = "100+"
}
export enum Commodity {
  PERSONAL_GOODS = "Personal Goods Not for Commercial Use",
  ELECTRONICS = "Electronics & Electrical Equipment",
  MACHINERY = "Machinery & Industrial Equipment",
  TEXTILES = "Textiles & Apparel",
  RETAIL = "Retail & Consumer Goods",
  AUTOMOTIVE = "Automotive Parts & Vehicles",
  FOOD = "Food & Beverages",
  FURNITURE = "Commercial Furniture & Home Goods",
  METALS = "Metals & Minerals",
  PETROLEUM = "Petroleum & Energy Products",
  HAZARDOUS = "Hazardous Materials",
  AEROSPACE = "Aerospace & Defense",
  RENEWABLE = "Renewable Energy Components",
  PHARMACEUTICALS = "Pharmaceuticals & Medical Supplies",
  CHEMICALS = "Chemicals & Raw Materials"
}
export enum Industry {
  LOGISTICS = "Logistics & Transportation",
  MANUFACTURING = "Manufacturing",
  RETAIL = "Retail & Consumer Goods",
  HEALTHCARE = "Healthcare & Life Sciences",
  TECHNOLOGY = "Technology",
  CHEMICALS = "Chemicals & Materials",
  FOOD = "Food & Beverage",
  ENERGY = "Energy & Utilities",
  AEROSPACE = "Aerospace & Defense",
  CONSTRUCTION = "Construction & Engineering",
  TEXTILES = "Textiles & Apparel",
  PROFESSIONAL = "Professional Services",
  MINING = "Mining & Metals",
  MEDIA = "Media & Entertainment",
  GOVERNMENT = "Government & Public Sector",
  HOSPITALITY = "Hospitality & Tourism",
  EDUCATION = "Education & Research",
  NON_PROFIT = "Non-Profit & NGOs"
}
export enum OnboardingSteps {
  SURVEY = 1,
  INTRODUCTION_VIDEO,
  IMPORTER_INFORMATION,
  BUSINESS_VALIDATION,
  POA,
  COMPLETED
}
export type OnboardingSurvey = {
  companySize?: CompanySize;
  monthlyImportVolume?: MonthlyImportVolume;
  commodity?: Commodity;
  industry?: Industry;
  name: string;
  contactEmail: string;
};
export type OnboardingVideo = {
  videoPlayed?: boolean;
  videoCompleted?: boolean;
  playedSeconds?: number;
};
export type OnboardingImporter = Partial<EditImporterDto> & {
  countryName?: string;
};

type SerializedTimestamp = {
  _seconds: number;
  _nanoseconds: number;
};

export type FailedBusinessVerification = {
  businessNumber?: string;
  businessName?: string;
  status?: CarmStatus;
  timestamp?: SerializedTimestamp;
};

export type OnboardingDocument = Partial<OnboardingSurvey> &
  OnboardingVideo &
  SendPoaResponse & {
    id?: string;
    step: OnboardingSteps;
    importer?: OnboardingImporter;
    poaStatus?: PoaStatus;
    poaSignedAt?: SerializedTimestamp;
    carmStatus?: CarmStatus | null;
    failedBusinessVerification?: FailedBusinessVerification[] | FieldValue;
    failedCarmRequest?: FailedBusinessVerification[] | FieldValue;
    createdAt?: SerializedTimestamp;
    updatedAt?: SerializedTimestamp;
  };
//#endregion
