import { FindOptionsRelations } from "typeorm";
import { CanadaTariff } from "../entities";
import { UnitOfMeasure } from "./enums";

export enum CanadaTariffType {
  TEN_DIGITS = "10",
  FOUR_DIGITS = "4"
}

export enum SyncStatus {
  RUNNING = "running",
  SUCCESS = "success",
  FAILED = "failed"
}

export const CANADA_TARIFF_SYNC_EVENT = "canada-tariff.sync";
export const CANADA_TARIFF_EMBEDDING_EVENT = "canada-tariff.embedding";

export const CANADA_TARIFF_MODULE_OPTIONS = "CANADA_TARIFF_MODULE_OPTIONS";

export interface CanadaTariffModuleOptions {
  readMany: boolean;
  readOne: boolean;
  sync: boolean;
}

export enum CanadaTariffColumn {
  id = "id",
  hsCode = "hsCode",
  effectiveDate = "effectiveDate",
  expiryDate = "expiryDate",
  description = "description",
  uom = "uom",
  // mfn = 'mfn',
  // generalTariff = 'generalTariff',
  treatments = "treatments",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export const FIND_CANADA_TARIFF_RELATIONS: FindOptionsRelations<CanadaTariff> = {
  createdBy: true,
  lastEditedBy: true
};

export const CANADA_TARIFF_ENUM_KEYS = ["uom"];

export type CBSATariffFileRow = {
  row_id: number;

  TARIFF: string;
  EFF_DATE: Date;
  CHANGE: 0 | 1;
  SUB_CHAP: string | null;
  DESC1: string;
  DESC2: string | null;
  DESC3: string | null;
  FOOTNOTE: string | null;
  UOM: string | null;
  MFN: string | null;
  "General Tariff": string | null;
} & { [x: string]: string | null };

export type TariffTreeNode = {
  hsCode: string;
  description: string;
  effectiveDate: Date;
  uom: UnitOfMeasure;
  treatments: Record<string, string>;
  children: Array<TariffTreeNode>;
};
