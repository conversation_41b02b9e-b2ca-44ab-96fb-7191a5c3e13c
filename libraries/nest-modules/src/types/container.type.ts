import { FindOptionsRelations } from "typeorm";
import { Container } from "../entities/container.entity";

export enum ContainerColumn {
  id = "id",
  status = "status",
  trackingStatus = "trackingStatus",
  containerNumber = "containerNumber",
  containerType = "containerType",
  etaDestination = "etaDestination",
  etaDestinationString = "etaDestinationString",
  pickupLfd = "pickupLfd",
  pickupLfdString = "pickupLfdString",
  pickupDate = "pickupDate",
  pickupDateString = "pickupDateString",
  returnLfd = "returnLfd",
  returnLfdString = "returnLfdString",
  returnDate = "returnDate",
  returnDateString = "returnDateString",
  createDate = "createDate",
  lastEditDate = "lastEditDate",
  shipmentId = "shipmentId",
  createdById = "createdById",
  lastEditedById = "lastEditedById"
}

export const CONTAINER_ENUM_KEYS = ["status", "trackingStatus", "containerType"];
export const CONTAINER_REQUIRED_KEYS = ["status", "trackingStatus", "containerNumber", "shipment"];

export const FIND_CONTAINER_RELATIONS: FindOptionsRelations<Container> = {
  shipment: true,
  createdBy: true,
  lastEditedBy: true
};
