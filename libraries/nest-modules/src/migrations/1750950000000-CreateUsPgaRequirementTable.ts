import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateUsPgaRequirementTable1750950000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop existing us_pga_requirement table if it exists
    await queryRunner.query(`DROP TABLE IF EXISTS "us_pga_requirement"`);

    // Create us_pga_requirement table only
    await queryRunner.query(`
      CREATE TABLE "us_pga_requirement" (
        "id" SERIAL NOT NULL,
        "createDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "lastEditDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "tariffFlagCode" character varying(3) NOT NULL,
        "agencyCode" character varying(3) NOT NULL,
        "requirementLevel" character varying(1) NOT NULL,
        "programCode" text array,
        "description" text NOT NULL,
        CONSTRAINT "PK_us_pga_requirement" PRIMARY KEY ("id"),
        CONSTRAINT "CHK_requirement_level" CHECK ("requirementLevel" IN ('R', 'M')),
        CONSTRAINT "UNIQUE_TARIFF_FLAG_CODE" UNIQUE ("tariffFlagCode")
      )
    `);

    // Create indexes for performance
    await queryRunner.query(`
      CREATE INDEX "IDX_US_PGA_REQUIREMENT_TARIFF_FLAG_CODE" ON "us_pga_requirement" ("tariffFlagCode")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_US_PGA_REQUIREMENT_AGENCY_CODE" ON "us_pga_requirement" ("agencyCode")
    `);

    // Insert seed data
    const aceTariffCodes = {
      data: [
        {
          tariffFlagCode: "EP1",
          agencyCode: "EPA",
          requirementLevel: "M",
          programCode: ["ODS"],
          description: "Ozone Depleting Substances specific data may be required"
        },
        {
          tariffFlagCode: "EP2",
          agencyCode: "EPA",
          requirementLevel: "R",
          programCode: ["ODS"],
          description: "Ozone Depleting Substances specific data is required"
        },
        {
          tariffFlagCode: "EP3",
          agencyCode: "EPA",
          requirementLevel: "M",
          programCode: ["VNE"],
          description: "Vehicle and Engines specific data may be required"
        },
        {
          tariffFlagCode: "EP4",
          agencyCode: "EPA",
          requirementLevel: "R",
          programCode: ["VNE"],
          description: "Vehicle and Engines specific data is required"
        },
        {
          tariffFlagCode: "EP5",
          agencyCode: "EPA",
          requirementLevel: "M",
          programCode: ["PS1", "PS2", "PS3"],
          description: "Pesticides specific data may be required"
        },
        {
          tariffFlagCode: "EP6",
          agencyCode: "EPA",
          requirementLevel: "R",
          programCode: ["PS1", "PS2", "PS3"],
          description: "Pesticides specific data is required"
        },
        {
          tariffFlagCode: "EP7",
          agencyCode: "EPA",
          requirementLevel: "M",
          programCode: ["TS1"],
          description: "Toxic Substances Control Act specific data may be required"
        },
        {
          tariffFlagCode: "EP8",
          agencyCode: "EPA",
          requirementLevel: "R",
          programCode: ["TS1"],
          description: "Toxic Substances Control Act specific data is required"
        },
        {
          tariffFlagCode: "EH1",
          agencyCode: "EPA",
          requirementLevel: "M",
          programCode: ["HFC"],
          description: "EPA Hydrofluorocarbons data may be required. Only disclaim A allowed"
        },
        {
          tariffFlagCode: "EH2",
          agencyCode: "EPA",
          requirementLevel: "R",
          programCode: ["HFC"],
          description: "EPA Hydrofluorocarbons data is required"
        },
        {
          tariffFlagCode: "FS3",
          agencyCode: "FSI",
          requirementLevel: "M",
          programCode: ["FSI"],
          description: "FSIS data may be required. Applicable to all FSIS programs"
        },
        {
          tariffFlagCode: "FS4",
          agencyCode: "FSI",
          requirementLevel: "R",
          programCode: ["FSI"],
          description: "FSIS data is required. Applicable to all FSIS programs"
        },
        {
          tariffFlagCode: "NM1",
          agencyCode: "NMF",
          requirementLevel: "M",
          programCode: ["370"],
          description: "370 specific data may be required"
        },
        {
          tariffFlagCode: "NM2",
          agencyCode: "NMF",
          requirementLevel: "R",
          programCode: ["370"],
          description: "370 specific data is required"
        },
        {
          tariffFlagCode: "NM3",
          agencyCode: "NMF",
          requirementLevel: "M",
          programCode: ["AMR"],
          description: "Antarctic Marine Living Resources specific data may be required"
        },
        {
          tariffFlagCode: "NM4",
          agencyCode: "NMF",
          requirementLevel: "R",
          programCode: ["AMR"],
          description: "Antarctic Marine Living Resources specific data is required"
        },
        {
          tariffFlagCode: "NM5",
          agencyCode: "NMF",
          requirementLevel: "M",
          programCode: ["HMS"],
          description: "Highly Migratory Species specific data may be required"
        },
        {
          tariffFlagCode: "NM6",
          agencyCode: "NMF",
          requirementLevel: "R",
          programCode: ["HMS"],
          description: "Highly Migratory Species specific data is required"
        },
        {
          tariffFlagCode: "NM8",
          agencyCode: "NMF",
          requirementLevel: "R",
          programCode: ["SIM"],
          description: "Seafood Import Monitoring Program specific data is required"
        },
        {
          tariffFlagCode: "DT1",
          agencyCode: "NHT",
          requirementLevel: "M",
          programCode: ["MVS", "REI", "TPE", "OEI", "OFF"],
          description: "DOT/National Highway Traffic Safety Administration HS-7 data may be required"
        },
        {
          tariffFlagCode: "DT2",
          agencyCode: "NHT",
          requirementLevel: "R",
          programCode: ["MVS", "REI", "TPE", "OEI", "OFF"],
          description: "DOT/National Highway Traffic Safety Administration HS-7 data is required"
        },
        {
          tariffFlagCode: "AL1",
          agencyCode: "APH",
          requirementLevel: "M",
          programCode: ["APL"],
          description: "Lacey Act specific data may be required"
        },
        {
          tariffFlagCode: "AL2",
          agencyCode: "APH",
          requirementLevel: "R",
          programCode: ["APL"],
          description: "Lacey Act specific data is required"
        },
        {
          tariffFlagCode: "FD1",
          agencyCode: "FDA",
          requirementLevel: "M",
          programCode: ["BIO", "COS", "DEV", "DRU", "FOO", "RAD", "TOB", "VME"],
          description: "FDA data may be required 801(a)"
        },
        {
          tariffFlagCode: "FD2",
          agencyCode: "FDA",
          requirementLevel: "R",
          programCode: ["BIO", "COS", "DEV", "DRU", "FOO", "RAD", "TOB", "VME"],
          description: "FDA data Required 801(a)"
        },
        {
          tariffFlagCode: "FD3",
          agencyCode: "FDA",
          requirementLevel: "M",
          programCode: ["BIO", "COS", "DEV", "DRU", "FOO", "RAD", "TOB", "VME"],
          description: "FDA Prior Notice Data may be required 801(m)"
        },
        {
          tariffFlagCode: "FD4",
          agencyCode: "FDA",
          requirementLevel: "R",
          programCode: ["BIO", "COS", "DEV", "DRU", "FOO", "RAD", "TOB", "VME"],
          description: "FDA Prior Notice Data is required 801(m)"
        },
        {
          tariffFlagCode: "AM1",
          agencyCode: "AMS",
          requirementLevel: "M",
          programCode: ["EG"],
          description: "USDA/Agricultural Marketing Service Data related to egg products may be required"
        },
        {
          tariffFlagCode: "AM2",
          agencyCode: "AMS",
          requirementLevel: "R",
          programCode: ["EG"],
          description: "USDA/Agricultural Marketing Service Data Related to shell eggs is required"
        },
        {
          tariffFlagCode: "AM3",
          agencyCode: "AMS",
          requirementLevel: "M",
          programCode: ["MO"],
          description: "USDA/Agricultural Marketing Service Data Related to marketing orders may be required"
        },
        {
          tariffFlagCode: "AM4",
          agencyCode: "AMS",
          requirementLevel: "R",
          programCode: ["MO"],
          description: "USDA/Agricultural Marketing Service Data Related to marketing orders is required"
        },
        {
          tariffFlagCode: "AM6",
          agencyCode: "AMS",
          requirementLevel: "R",
          programCode: ["PN"],
          description: "USDA/Agriculture Marketing Service Data related to peanuts is required"
        },
        {
          tariffFlagCode: "AM7",
          agencyCode: "AMS",
          requirementLevel: "M",
          programCode: ["OR"],
          description: "USDA/Agriculture Marketing Service Data related to organics may be required"
        },
        {
          tariffFlagCode: "AM8",
          agencyCode: "AMS",
          requirementLevel: "R",
          programCode: ["OR"],
          description: "USDA/Agriculture Marketing Service Data related to organics is required"
        },
        {
          tariffFlagCode: "TB1",
          agencyCode: "TTB",
          requirementLevel: "M",
          programCode: ["BER", "WIN", "DSP", "TOB"],
          description: "TTB data may be required. Applicable to all TTB programs."
        },
        {
          tariffFlagCode: "TB2",
          agencyCode: "TTB",
          requirementLevel: "R",
          programCode: ["BER", "WIN", "DSP", "TOB"],
          description: "TTB data is required. Applicable to all TTB programs."
        },
        {
          tariffFlagCode: "TB3",
          agencyCode: "TTB",
          requirementLevel: "M",
          programCode: ["BER", "WIN", "DSP", "TOB"],
          description: "TTB data may be required, which can only be disclaimed using codes A or C"
        },
        {
          tariffFlagCode: "AQ1",
          agencyCode: "APH",
          requirementLevel: "M",
          programCode: ["AAC", "APQ", "AVS", "ABS"],
          description: "APHIS data may be required"
        },
        {
          tariffFlagCode: "AQ2",
          agencyCode: "APH",
          requirementLevel: "R",
          programCode: ["AAC", "APQ", "AVS", "ABS"],
          description: "APHIS data is required"
        },
        {
          tariffFlagCode: "AQX",
          agencyCode: "APH",
          requirementLevel: "M",
          programCode: ["AAC", "APQ", "AVS", "ABS"],
          description: "APHIS Data May be required (no disclaim required)"
        },
        {
          tariffFlagCode: "OM1",
          agencyCode: "OMC",
          requirementLevel: "M",
          programCode: ["OMC"],
          description:
            "U.S. Department of State, Bureau of Oceans and International Environmental and Scientific Affairs, Office of Marine Conservation data may be required"
        },
        {
          tariffFlagCode: "OM2",
          agencyCode: "OMC",
          requirementLevel: "R",
          programCode: ["OMC"],
          description:
            "U.S. Department of State, Bureau of Oceans and International Environmental and Scientific Affairs, Office of Marine Conservation data is required"
        },
        {
          tariffFlagCode: "FW1",
          agencyCode: "FWS",
          requirementLevel: "M",
          programCode: ["FWS"],
          description:
            "U.S. Fish and Wildlife Service data may be required, which can only be disclaimed using codes C, D or E"
        },
        {
          tariffFlagCode: "FW2",
          agencyCode: "FWS",
          requirementLevel: "R",
          programCode: ["FWS"],
          description: "U.S. Fish and Wildlife Service data is required"
        },
        {
          tariffFlagCode: "FW3",
          agencyCode: "FWS",
          requirementLevel: "M",
          programCode: ["FWS"],
          description:
            "U.S. Fish and Wildlife Service data may be required, which can only be disclaimed using code C or D"
        },
        {
          tariffFlagCode: "DE1",
          agencyCode: "DEA",
          requirementLevel: "M",
          programCode: ["DEA"],
          description: "U.S. Drug Enforcement Administration data may be required"
        }
      ]
    };
    const seedData = aceTariffCodes.data;

    // Insert all seed data
    for (const record of seedData) {
      await queryRunner.query(
        `
        INSERT INTO "us_pga_requirement" (
          "tariffFlagCode", 
          "agencyCode", 
          "requirementLevel", 
          "programCode", 
          "description"
        ) VALUES ($1, $2, $3, $4, $5)
      `,
        [
          record.tariffFlagCode,
          record.agencyCode,
          record.requirementLevel,
          record.programCode,
          record.description
        ]
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_US_PGA_REQUIREMENT_AGENCY_CODE"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_US_PGA_REQUIREMENT_TARIFF_FLAG_CODE"`);

    // Drop the table
    await queryRunner.query(`DROP TABLE IF EXISTS "us_pga_requirement"`);
  }
}
