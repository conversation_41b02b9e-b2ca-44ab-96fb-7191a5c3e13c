import { MigrationInterface, QueryRunner } from "typeorm";

export class FlipMatchingRulePriority1747339514323 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE IF EXISTS "matching_rule"
            ALTER COLUMN "priority" SET DEFAULT 1
        `);

    await queryRunner.query(`
            UPDATE "matching_rule"
            SET "priority" = 10 - LEAST(10, "priority")
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            UPDATE "matching_rule"
            SET "priority" = 10 - LEAST(10, "priority")
        `);

    await queryRunner.query(`
            ALTER TABLE IF EXISTS "matching_rule"
            ALTER COLUMN "priority" SET DEFAULT 0
        `);
  }
}
