import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";
import { ContainerType } from "../types";

export class MoveShipmentContainerFields1742566631911 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create container type enum
    await queryRunner.query(`
        DO $$ BEGIN
        CREATE TYPE container_containertype_enum AS ENUM (${Object.values(ContainerType)
          .map((ct) => `'${ct}'`)
          .join(",")});
        EXCEPTION
        WHEN duplicate_object THEN null;
        END $$;
    `);

    // Add new columns to container table
    await queryRunner.query(
      `ALTER TABLE IF EXISTS container ADD COLUMN IF NOT EXISTS "containerType" container_containertype_enum;`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS container ADD COLUMN IF NOT EXISTS "etaDestination" timestamptz;`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS container ADD COLUMN IF NOT EXISTS "etaDestinationString" varchar;`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS container ADD COLUMN IF NOT EXISTS "pickupLfd" timestamptz;`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS container ADD COLUMN IF NOT EXISTS "pickupLfdString" varchar;`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS container ADD COLUMN IF NOT EXISTS "pickupDate" timestamptz;`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS container ADD COLUMN IF NOT EXISTS "pickupDateString" varchar;`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS container ADD COLUMN IF NOT EXISTS "returnLfd" timestamptz;`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS container ADD COLUMN IF NOT EXISTS "returnLfdString" varchar;`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS container ADD COLUMN IF NOT EXISTS "returnDate" timestamptz;`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS container ADD COLUMN IF NOT EXISTS "returnDateString" varchar;`
    );

    // Copy values from shipment table to container table
    await queryRunner.query(`
        UPDATE container
        SET
            "containerType" = s."containerType"::varchar::container_containertype_enum,
            "etaDestination" = s."etaDestination",
            "etaDestinationString" = s."etaDestinationString",
            "pickupLfd" = s."pickupLfd",
            "pickupLfdString" = s."pickupLfdString",
            "pickupDate" = s."pickupDate",
            "pickupDateString" = s."pickupDateString",
            "returnLfd" = s."returnLfd",
            "returnLfdString" = s."returnLfdString",
            "returnDate" = s."returnDate",
            "returnDateString" = s."returnDateString"
        FROM shipment s
        WHERE s.id = container."shipmentId";
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Copy values from container table to shipment table
    await queryRunner.query(`
        UPDATE shipment s
        SET
            "containerType" = c."containerType"::varchar::shipment_containertype_enum,
            "etaDestination" = c."etaDestination",
            "etaDestinationString" = c."etaDestinationString",
            "pickupLfd" = c."pickupLfd",
            "pickupLfdString" = c."pickupLfdString",
            "pickupDate" = c."pickupDate",
            "pickupDateString" = c."pickupDateString",
            "returnLfd" = c."returnLfd",
            "returnLfdString" = c."returnLfdString",
            "returnDate" = c."returnDate",
            "returnDateString" = c."returnDateString"
        FROM (
            SELECT DISTINCT ON (c."shipmentId") *
            FROM container c
            ORDER BY c."shipmentId" ASC, c."createDate" DESC
        ) AS c
        WHERE c."shipmentId" = s.id;
    `);

    // Drop container columns
    await queryRunner.query(`ALTER TABLE IF EXISTS container DROP COLUMN IF EXISTS "containerType";`);
    await queryRunner.query(`ALTER TABLE IF EXISTS container DROP COLUMN IF EXISTS "etaDestination";`);
    await queryRunner.query(`ALTER TABLE IF EXISTS container DROP COLUMN IF EXISTS "etaDestinationString";`);
    await queryRunner.query(`ALTER TABLE IF EXISTS container DROP COLUMN IF EXISTS "pickupLfd";`);
    await queryRunner.query(`ALTER TABLE IF EXISTS container DROP COLUMN IF EXISTS "pickupLfdString";`);
    await queryRunner.query(`ALTER TABLE IF EXISTS container DROP COLUMN IF EXISTS "pickupDate";`);
    await queryRunner.query(`ALTER TABLE IF EXISTS container DROP COLUMN IF EXISTS "pickupDateString";`);
    await queryRunner.query(`ALTER TABLE IF EXISTS container DROP COLUMN IF EXISTS "returnLfd";`);
    await queryRunner.query(`ALTER TABLE IF EXISTS container DROP COLUMN IF EXISTS "returnLfdString";`);
    await queryRunner.query(`ALTER TABLE IF EXISTS container DROP COLUMN IF EXISTS "returnDate";`);
    await queryRunner.query(`ALTER TABLE IF EXISTS container DROP COLUMN IF EXISTS "returnDateString";`);

    // Drop container type enum
    await queryRunner.query(`DROP TYPE IF EXISTS container_containertype_enum;`);
  }
}
