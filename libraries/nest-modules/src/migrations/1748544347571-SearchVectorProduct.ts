import { MigrationInterface, QueryRunner } from "typeorm";

export class SearchVectorProduct1748544347571 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE IF EXISTS "product" ADD COLUMN IF NOT EXISTS "search_vector" tsvector;
          `);

    await queryRunner.query(`
            CREATE OR REPLACE FUNCTION update_product_search_vector() RETURNS trigger AS $$
            DECLARE
              origin_name TEXT;
            BEGIN
              SELECT name INTO origin_name FROM country WHERE id = NEW."originId";

              -- Handle case where country is not found
              IF origin_name IS NULL THEN
                origin_name := '';
              END IF;
      
              NEW.search_vector := to_tsvector('english',
                coalesce(NEW.description, '') || ' ' || coalesce(origin_name, '')
              );
              RETURN NEW;
              EXCEPTION
              WHEN OTHERS THEN
                -- Log error but don't fail the operation
                RAISE WARNING 'Failed to update search vector for product %: %', NEW.id, SQLERRM;
                NEW.search_vector := to_tsvector('english', coalesce(NEW.description, ''));
                RETURN NEW;
            END
            $$ LANGUAGE plpgsql;
          `);

    await queryRunner.query(`
            CREATE OR REPLACE TRIGGER product_search_vector_trigger
            BEFORE INSERT OR UPDATE ON product
            FOR EACH ROW EXECUTE FUNCTION update_product_search_vector();
          `);

    await queryRunner.query(`
            CREATE INDEX idx_product_search_vector ON product USING GIN(search_vector);
          `);

    // Populate existing records
    await queryRunner.query(`
            UPDATE product p
            SET search_vector = to_tsvector('english',
              coalesce(p.description, '') || ' ' || coalesce(c.name, '')
            )
            FROM country c
            WHERE p."originId" = c.id;
          `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX IF EXISTS idx_product_search_vector;`);
    await queryRunner.query(`DROP TRIGGER IF EXISTS product_search_vector_trigger ON product;`);
    await queryRunner.query(`DROP FUNCTION IF EXISTS update_product_search_vector;`);
    await queryRunner.query(`ALTER TABLE product DROP COLUMN IF EXISTS search_vector;`);
  }
}
