import { MigrationInterface, QueryRunner } from "typeorm";

export class SearchVectorProductRevOne1750171230505 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop existing trigger and function to recreate them
    await queryRunner.query(`
            DROP TRIGGER IF EXISTS product_search_vector_trigger ON product;
            DROP FUNCTION IF EXISTS update_product_search_vector();
        `);

    // Create improved trigger function with proper NULL handling
    await queryRunner.query(`
            CREATE OR REPLACE FUNCTION update_product_search_vector() RETURNS trigger AS $$
            DECLARE
                origin_name TEXT;
            BEGIN
                -- Use LEFT JOIN logic in a correlated subquery to handle NULLs
                SELECT name INTO origin_name FROM country WHERE id = NEW."originId";

                NEW.search_vector := to_tsvector('english',
                    coalesce(NEW.description, '') || ' ' || coalesce(origin_name, '')
                );
                RETURN NEW;
            END
            $$ LANGUAGE plpgsql;
        `);

    // Recreate trigger
    await queryRunner.query(`
            CREATE TRIGGER product_search_vector_trigger
            BEFORE INSERT OR UPDATE ON product
            FOR EACH ROW
            EXECUTE FUNCTION update_product_search_vector();
        `);

    // Reindex existing records with proper NULL handling
    await queryRunner.query(`
            UPDATE product p
            SET search_vector = to_tsvector(
                'english',
                coalesce(p.description, '') || ' ' || coalesce(c.name, '')
            )
            FROM country c
            WHERE c.id = p."originId";
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the new trigger and function
    await queryRunner.query(`DROP TRIGGER IF EXISTS product_search_vector_trigger ON product;`);
    await queryRunner.query(`DROP FUNCTION IF EXISTS update_product_search_vector();`);

    // Restore the original trigger function
    await queryRunner.query(`
            CREATE OR REPLACE FUNCTION update_product_search_vector() RETURNS trigger AS $$
            DECLARE
                origin_name TEXT;
            BEGIN
                SELECT name INTO origin_name FROM country WHERE id = NEW."originId";
    
                NEW.search_vector := to_tsvector('english',
                    coalesce(NEW.description, '') || ' ' || coalesce(origin_name, '')
                );
                RETURN NEW;
            END
            $$ LANGUAGE plpgsql;
        `);

    // Restore the original trigger
    await queryRunner.query(`
            CREATE TRIGGER product_search_vector_trigger
            BEFORE INSERT OR UPDATE ON product
            FOR EACH ROW
            EXECUTE FUNCTION update_product_search_vector();
        `);

    // Restore the original data
    await queryRunner.query(`
            UPDATE product p
            SET search_vector = to_tsvector('english',
                coalesce(p.description, '') || ' ' || coalesce(c.name, '')
            )
            FROM country c
            WHERE p."originId" = c.id;
        `);
  }
}
