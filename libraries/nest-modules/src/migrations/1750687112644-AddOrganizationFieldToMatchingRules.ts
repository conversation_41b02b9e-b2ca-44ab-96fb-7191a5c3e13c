import { MigrationInterface, QueryRunner } from "typeorm";
import { MatchingConditionOperator } from "../types";

export class AddOrganizationFieldToMatchingRules1750687112644 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new organizationId field to matching rule table
    await queryRunner.query(`
        ALTER TABLE IF EXISTS "matching_rule" 
        ADD COLUMN IF NOT EXISTS "organizationId" integer
        REFERENCES "organization"(id) ON DELETE SET NULL;
    `);

    // Update existing organization rules to have organizationId
    await queryRunner.query(
      `
        UPDATE "matching_rule" mr1
        SET "organizationId" = (
            SELECT mc."valueInteger"[1]
            FROM matching_condition mc
            WHERE
                mc."ruleId" = mr1.id AND 
                mc."attribute" = 'organizationId' AND 
                (
                    (mc."operator" = $1 AND mc."valueInteger" IS NOT NULL) OR 
                    (mc."operator" = $2 AND array_length(mc."valueInteger", 1) = 1)
                )
            LIMIT 1
        )
        FROM (
            SELECT
                mc."ruleId" as "id",
                SUM(
                    CASE 
                        WHEN mc."attribute" = 'organizationId' AND ((mc."operator" = $1 AND mc."valueInteger" IS NOT NULL) OR (mc."operator" = $2 AND array_length(mc."valueInteger", 1) = 1)) THEN 1 
                        ELSE 0 
                    END
                ) as "orgTotal"
            FROM matching_condition mc
            GROUP BY mc."ruleId"
        ) mr2
        WHERE mr1.id = mr2.id AND mr2."orgTotal" = 1;
    `,
      [MatchingConditionOperator.EQUALS, MatchingConditionOperator.IN]
    );

    // Remove the organizationId condition for rules with organizationId set
    await queryRunner.query(
      `
        DELETE FROM matching_condition as mc
        USING (
            SELECT
                mc."ruleId" as "id",
                SUM(
                    CASE 
                        WHEN mc."attribute" = 'organizationId' AND ((mc."operator" = $1 AND mc."valueInteger" IS NOT NULL) OR (mc."operator" = $2 AND array_length(mc."valueInteger", 1) = 1)) THEN 1 
                        ELSE 0 
                    END
                ) as "orgTotal"
            FROM matching_condition mc
            GROUP BY mc."ruleId"
        ) as mr
        WHERE 
            mc."ruleId" = mr.id AND 
            mr."orgTotal" = 1 AND 
            mc."attribute" = 'organizationId' AND
            ((mc."operator" = $1 AND mc."valueInteger" IS NOT NULL) OR (mc."operator" = $2 AND array_length(mc."valueInteger", 1) = 1));
    `,
      [MatchingConditionOperator.EQUALS, MatchingConditionOperator.IN]
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Create new organizationId condition for those rules with organizationId set
    await queryRunner.query(`
        INSERT INTO matching_condition ("ruleId", "attribute", "operator", "value", "isOperationInverted", "attributeType", "valueInteger")
        SELECT
            mr.id as "ruleId",
            'organizationId' as "attribute",
            'equals' as "operator",
            mr."organizationId"::varchar as "value",
            false as "isOperationInverted",
            'id' as "attributeType",
            ARRAY[mr."organizationId"] as "valueInteger"
        FROM matching_rule mr
        WHERE mr."organizationId" IS NOT NULL;
    `);

    // Drop organizationId column from matching rule table
    await queryRunner.query(`
        ALTER TABLE IF EXISTS "matching_rule" 
        DROP COLUMN IF EXISTS "organizationId";
    `);
  }
}
