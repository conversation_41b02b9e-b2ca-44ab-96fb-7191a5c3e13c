import { In, MigrationInterface, QueryRunner } from "typeorm";
import { CanadaOgd, MatchingCondition, MatchingRule, OgdFiling } from "../entities";
import {
  ECCCEmissionProgram,
  ECCCSubType,
  ECCCWildlifeCompliance,
  GeneralImportPermit,
  HCIntendedUse,
  HCProductCategory,
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus,
  NRCANSubType,
  OGD_ECCC_PROGRAM,
  OGD_GAC_COMMODITY_TYPE,
  OGD_GAC_PROGRAM,
  OGD_HEALTH_CANADA_PROGRAM,
  OGD_NRCAN_PROGRAM,
  OgdFilingType
} from "../types";

export class DefaultOgdFilingRule1748269963219 implements MigrationInterface {
  private readonly ecccExcludedHSCodePrefixes = [
    "0101",
    "0102",
    "0103",
    "0104",
    "0105",
    "0106",
    "0201",
    "0202",
    "0204",
    "0205",
    "0206",
    "020724",
    "020725",
    "020726",
    "020727",
    "020741",
    "020742",
    "020743",
    "020744",
    "020745",
    "020751",
    "020752",
    "020753",
    "020754",
    "020755",
    "02076",
    "0208",
    "0210",
    "0301",
    "0302",
    "0303",
    "0304",
    "0305",
    "0306",
    "0307",
    "0308",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "**********",
    "04101",
    "04109",
    "05021",
    "0504",
    "0505",
    "0506",
    "0507",
    "0508",
    "0510",
    "0511",
    "0601101",
    "0601102",
    "06012",
    "0602",
    "060313",
    "060319",
    "080111",
    "080112",
    "080119",
    "1211",
    "130190",
    "130219",
    "1504101",
    "1504109",
    "15042",
    "15043",
    "1602901",
    "1602909",
    "1603",
    "1604",
    "1605",
    "160556",
    "160557",
    "160558"
  ];

  private readonly defaultOgdFilingRule: Record<
    string,
    {
      agency: string;
      program: string;
      commodityType?: string;
      ogd: Partial<OgdFiling & { ogdId: number }>;
    }
  > = {
    HC_CONSUMER_PRODUCT_SAFETY: {
      agency: "hc",
      program: OGD_HEALTH_CANADA_PROGRAM.CONSUMER_PRODUCT_SAFETY,
      ogd: {
        id: 1,
        endUse: HCIntendedUse.CONSUMER_PRODUCTS_SALE_DISTRIBUTION,
        productCategory: HCProductCategory.CONSUMER_PRODUCTS_CONSUMER_PRODUCT_FOR_ALL_AGES,
        isExcluded: false,
        isRegulated: false
      }
    },
    ECCC_WILDLIFE: {
      agency: "eccc",
      program: OGD_ECCC_PROGRAM.WILDLIFE_ENFORCEMENT_SOMETIMES_REGULATED,
      ogd: {
        id: 2,
        ecccSubType: ECCCSubType.WILDLIFE,
        wildlifeCompliance: ECCCWildlifeCompliance.NOT_ENDANGERED
      }
    },
    ECCC_VEHICLE_ENGINE_EMISSIONS: {
      agency: "eccc",
      program: OGD_ECCC_PROGRAM.TRANSPORT_PROGRAM,
      ogd: {
        id: 3,
        ecccSubType: ECCCSubType.EMISSIONS,
        emissionProgram: ECCCEmissionProgram.EXEMPT
      }
    },
    GAC_CARBON_STEEL: {
      agency: "gac",
      program: OGD_GAC_PROGRAM.STEEL,
      commodityType: OGD_GAC_COMMODITY_TYPE.CARBON_STEEL,
      ogd: {
        id: 4,
        generalImportPermit: GeneralImportPermit.GIP80,
        isExcluded: false,
        isRegulated: false
      }
    },
    GAC_SPECIALTY_STEEL: {
      agency: "gac",
      program: OGD_GAC_PROGRAM.STEEL,
      commodityType: OGD_GAC_COMMODITY_TYPE.SPECIALTY_STEEL,
      ogd: {
        id: 5,
        generalImportPermit: GeneralImportPermit.GIP81,
        isExcluded: false,
        isRegulated: false
      }
    },
    GAC_ALUMINUM: {
      agency: "gac",
      program: OGD_GAC_PROGRAM.ALUMINUM_IMPORT_MONITORING,
      commodityType: OGD_GAC_COMMODITY_TYPE.ALUMINUM,
      ogd: {
        id: 6,
        generalImportPermit: GeneralImportPermit.GIP83,
        isExcluded: false,
        isRegulated: false
      }
    },
    NRCAN_ENERGY_EFFICIENCY: {
      agency: "nrcan",
      program: OGD_NRCAN_PROGRAM.OFFICE_OF_ENERGY_EFFICIENCY,
      ogd: {
        id: 7,
        nrcanSubType: NRCANSubType.ENERGY_EFFICIENCY,
        isExcluded: false,
        isRegulated: false
      }
    }
  };

  private async prepareDefaultOgdFilingRule(queryRunner: QueryRunner): Promise<void> {
    for (const [key, value] of Object.entries(this.defaultOgdFilingRule)) {
      const ogdId = await this.getOgdId(value.agency, value.program, queryRunner);
      value.ogd.ogdId = ogdId;
    }
  }

  private async getOgdId(agency: string, program: string, queryRunner: QueryRunner): Promise<number> {
    const repository = queryRunner.manager.getRepository(CanadaOgd);

    const result = await repository.findOne({
      where: {
        agency: agency as any,
        program: program as any
      }
    });

    if (!result) {
      throw new Error(`OGD program ${program} not found`);
    }

    return result.id;
  }

  private async insertOgdFilingRule(queryRunner: QueryRunner): Promise<void> {
    const repository = queryRunner.manager.getRepository(OgdFiling);
    const data = Object.values(this.defaultOgdFilingRule).map((value) => {
      const { ogdId, ...ogd } = value.ogd;
      return {
        type: OgdFilingType.DEFAULT,
        ...ogd,
        ogd: {
          id: ogdId
        }
      };
    });

    for (const ogdFiling of data) {
      const newOgdFiling = await repository.save({ ...ogdFiling });
      // update the id to the original id
      // https://github.com/typeorm/typeorm/issues/2215
      await repository.update(newOgdFiling.id, { id: ogdFiling.id });
    }
  }

  private async createMatchingRules(queryRunner: QueryRunner): Promise<void> {
    const repository = queryRunner.manager.getRepository(MatchingRule);
    const matchingConditionRepository = queryRunner.manager.getRepository(MatchingCondition);

    for (const value of Object.values(this.defaultOgdFilingRule)) {
      console.log(`Creating matching rules for ${value.agency} - ${value.program}`);
      const rules = await repository.find({
        where: {
          sourceId: value.ogd.ogdId,
          sourceTable: MatchingRuleSourceDatabaseTable.CANADA_OGD
        },
        relations: {
          conditions: true
        }
      });

      console.log(`Found ${rules.length} matching rules`);

      const defaultRules = rules
        .map((rule) => {
          const isExcludedHsCodePrefix = rule.conditions.find((condition) =>
            this.ecccExcludedHSCodePrefixes.includes(condition.value)
          );
          if (value.agency === "eccc" && isExcludedHsCodePrefix) {
            return null;
          }

          const name =
            value.agency !== "gac"
              ? `DefaultCanadaOgdFiling - ${value.agency} - ${value.program} - ${rule.id}`
              : `DefaultCanadaOgdFiling - ${value.agency} - ${value.program} - ${value.commodityType} - ${rule.id}`;

          // batch create matching rules
          return {
            status: MatchingRuleStatus.ACTIVE,
            name,
            sourceId: value.ogd.id,
            sourceTable: MatchingRuleSourceDatabaseTable.OGD_FILING,
            destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT,
            priority: 0,
            conditions: rule.conditions.map((condition) => ({
              attribute: condition.attribute,
              operator: condition.operator,
              value: condition.value
            }))
          };
        })
        .filter((a) => a !== null);

      for (const rule of defaultRules) {
        const result = await repository.save(rule);
        // save matching conditions as well
        await matchingConditionRepository.save(
          rule.conditions.map((condition) => ({
            ...condition,
            rule: {
              id: result.id
            }
          }))
        );
      }
    }
  }

  public async up(queryRunner: QueryRunner): Promise<void> {
    // set ogd filing org id nullable
    await queryRunner.query(`
            ALTER TABLE IF EXISTS ogd_filing
            ALTER COLUMN "organizationId" DROP NOT NULL
        `);

    // add enum value to ogd_filing
    await queryRunner.query(`
            DO $$ BEGIN
            CREATE TYPE "ogd_filing_type_enum" AS ENUM ('default', 'generated', 'manual');
            EXCEPTION
            WHEN duplicate_object THEN null;
            END $$;
        `);

    // add column to ogd_filing
    await queryRunner.query(`
            ALTER TABLE IF EXISTS ogd_filing
            ADD COLUMN IF NOT EXISTS "type" "ogd_filing_type_enum" NOT NULL DEFAULT 'manual'
        `);

    // populate the ogdId for the defaultOgdFilingRule
    await this.prepareDefaultOgdFilingRule(queryRunner);
    await this.insertOgdFilingRule(queryRunner);
    await this.createMatchingRules(queryRunner);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // remove all matching rules
    const repository = queryRunner.manager.getRepository(MatchingRule);
    await repository.delete({
      sourceTable: MatchingRuleSourceDatabaseTable.OGD_FILING,
      sourceId: In(Object.values(this.defaultOgdFilingRule).map((value) => value.ogd.id))
    });

    // remove all default ogd filing rules
    const ogdFilingRepository = queryRunner.manager.getRepository(OgdFiling);
    await ogdFilingRepository.delete({
      id: In(Object.values(this.defaultOgdFilingRule).map((value) => value.ogd.id))
    });

    // remove column from ogd_filing
    await queryRunner.query(`
            ALTER TABLE IF EXISTS ogd_filing
            DROP COLUMN IF EXISTS "type"
        `);

    // remove enum value from ogd_filing
    await queryRunner.query(`
            DROP TYPE IF EXISTS "ogd_filing_type_enum"
        `);

    // Set OGD filing org ID to not null
    await queryRunner.query(`
      ALTER TABLE IF EXISTS ogd_filing
      ALTER COLUMN "organizationId" SET NOT NULL
    `);
  }
}
