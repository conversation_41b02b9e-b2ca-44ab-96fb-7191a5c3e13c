import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSequenceToCommercialInvoiceLine1745248413414 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First add the column if it doesn't exist
    await queryRunner.query(`
            ALTER TABLE commercial_invoice_line 
            ADD COLUMN IF NOT EXISTS "sequence" integer default 0
        `);

    // Then update the column with sequence values
    await queryRunner.query(`
            WITH numbered_lines AS (
                SELECT 
                    id,
                    ROW_NUMBER() OVER (PARTITION BY "commercialInvoiceId" ORDER BY id) AS seq
                FROM commercial_invoice_line
            )
            UPDATE commercial_invoice_line cil
            SET "sequence" = nl.seq
            FROM numbered_lines nl
            WHERE cil.id = nl.id
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the line_sequence column
    await queryRunner.query(`
            ALTER TABLE commercial_invoice_line 
            DROP COLUMN IF EXISTS "sequence"
        `);
  }
}
