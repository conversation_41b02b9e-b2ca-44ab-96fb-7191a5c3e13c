import { Logger, MigrationInterface, QueryRunner } from "typeorm";

class SilentLogger implements Logger {
  logQueryError(error: string | Error, query: string, parameters?: any[], queryRunner?: QueryRunner) {}
  logQuerySlow(time: number, query: string, parameters?: any[], queryRunner?: QueryRunner) {}
  logSchemaBuild(message: string, queryRunner?: QueryRunner) {}
  logMigration(message: string, queryRunner?: QueryRunner) {}
  log(level: "log" | "info" | "warn", message: any, queryRunner?: QueryRunner) {}
  logQuery(query: string, parameters?: any[]): void {}
}

export class CopyFileParseResultToSeparateTable1749236124337 implements MigrationInterface {
  private async getFiles(
    queryRunner: QueryRunner,
    lastId: number,
    chunkSize: number
  ): Promise<{
    pages: {
      fileId: number;
      page: number;
      md: string;
      documentId: number;
      documentPage: number;
      documentStartPage: number;
      documentEndPage: number;
      documentName: string;
    }[];
    lastId: number;
  }> {
    const files = await queryRunner.query(`
SELECT
    f.id AS "fileId",
    (page_elem->>'page')::int AS "page",
    (page_elem->>'md')::text AS "md",
    d.id AS "documentId",
    ((page_elem->>'page')::int - d."startPage" + 1) as "documentPage",
    d."startPage" AS "documentStartPage",
    d."endPage" AS "documentEndPage",
    d."name" AS "documentName"
FROM
    file f
JOIN
    LATERAL jsonb_array_elements(f."parseResult"::jsonb->'pages') AS page_elem ON TRUE
LEFT JOIN
    document d
    ON d."fileId" = f.id
    AND d."startPage" <= (page_elem->>'page')::int
    AND d."endPage" >= (page_elem->>'page')::int
WHERE
    f."parseResult" IS NOT null and d."id" is not null and f."id" > ${lastId} and f."id" <= ${lastId + chunkSize}
order by "fileId", (page_elem->>'page')::int asc
        `);
    return {
      pages: files,
      lastId: lastId + chunkSize
    };
  }

  public async up(queryRunner: QueryRunner): Promise<void> {
    const conn = queryRunner.connection;
    const prevLogger = conn.logger;
    conn.logger = new SilentLogger();

    try {
      const chunkSize = 20;
      const minId = await queryRunner.query(`select min("id") from "file"`);
      const maxId = await queryRunner.query(`select max("id") from "file"`);

      if (minId[0].min === null || maxId[0].max === null) {
        console.log("No files to process");
        return;
      }

      let lastId = minId[0].min - 1;

      let pagesInserted = 0;

      while (true) {
        console.log(`Processing files from ${lastId} to ${lastId + chunkSize}`);
        const files = await this.getFiles(queryRunner, lastId, chunkSize);

        if (files.lastId > maxId[0].max) {
          console.log(`No more files to process`);
          break;
        }

        console.log(`Inserting ${files.pages.length} pages`);

        await queryRunner.manager
          .createQueryBuilder()
          .insert()
          .into("file_page")
          .values(
            files.pages.map((page) => {
              const isParsOverlay = page.documentName === "PARS_OVERLAY";
              return {
                fileId: page.fileId,
                page: isParsOverlay ? 0 : page.page,
                md: page.md,
                documentId: page.documentId,
                documentPage: isParsOverlay ? 1 : page.documentPage
              };
            })
          )
          .orIgnore(`("fileId", "page")`)
          .execute();

        lastId = files.lastId;
        pagesInserted += files.pages.length;
        console.log(`Pages inserted: ${pagesInserted}`);
      }
    } catch (error) {
      throw error;
    } finally {
      conn.logger = prevLogger;
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // pass
  }
}
