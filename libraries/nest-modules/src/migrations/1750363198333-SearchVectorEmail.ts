import { MigrationInterface, QueryRunner } from "typeorm";

export class SearchVectorEmail1750363198333 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add search_vector column
    await queryRunner.query(`
            ALTER TABLE IF EXISTS "email" ADD COLUMN IF NOT EXISTS "search_vector" tsvector;
        `);

    // Create GIN index on search_vector
    await queryRunner.query(`
            CREATE INDEX IF NOT EXISTS "idx_email_search_vector" ON "email" USING gin("search_vector");
        `);

    // Create function to generate search vector
    await queryRunner.query(`
            CREATE OR REPLACE FUNCTION email_search_vector_trigger() RETURNS trigger AS $$
            BEGIN
                NEW.search_vector :=
                    setweight(to_tsvector('english', COALESCE(NEW.subject, '')), 'A') || 
                    setweight(to_tsvector('english', COALESCE(NEW.text, '')), 'B') ||
                    setweight(to_tsvector('english', COALESCE(NEW.html, '')), 'C');
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        `);

    // Create trigger to automatically update search vector
    await queryRunner.query(`
            DROP TRIGGER IF EXISTS email_search_vector_update ON "email";
            CREATE TRIGGER email_search_vector_update
                BEFORE INSERT OR UPDATE OF subject, text, html
                ON "email"
                FOR EACH ROW
                EXECUTE FUNCTION email_search_vector_trigger();
        `);

    // Update existing records
    await queryRunner.query(`
            UPDATE "email"
            SET search_vector =
                setweight(to_tsvector('english', COALESCE(subject, '')), 'A') ||
                setweight(to_tsvector('english', COALESCE(text, '')), 'B') ||
                setweight(to_tsvector('english', COALESCE(html, '')), 'C');
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop trigger
    await queryRunner.query(`
            DROP TRIGGER IF EXISTS email_search_vector_update ON "email";
        `);

    // Drop function
    await queryRunner.query(`
            DROP FUNCTION IF EXISTS email_search_vector_trigger;
        `);

    // Drop index
    await queryRunner.query(`
            DROP INDEX IF EXISTS "idx_email_search_vector";
        `);

    // Drop column
    await queryRunner.query(`
            ALTER TABLE "email" DROP COLUMN IF EXISTS "search_vector";
        `);
  }
}
