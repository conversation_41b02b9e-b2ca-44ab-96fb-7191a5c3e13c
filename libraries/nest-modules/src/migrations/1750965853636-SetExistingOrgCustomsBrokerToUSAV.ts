import { MigrationInterface, QueryRunner } from "typeorm";
import { OrganizationCustomsBroker } from "../types";

export class SetExistingOrgCustomsBrokerToUSAV1750965853636 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create organization customs broker enum
    await queryRunner.query(`
            DO $$ BEGIN
            CREATE TYPE organization_customsbroker_enum AS ENUM (${Object.values(OrganizationCustomsBroker)
              .map((broker) => `'${broker}'`)
              .join(",")});
            EXCEPTION
            WHEN duplicate_object THEN null;
            END $$; 
        `);

    // Add new column to organization table
    await queryRunner.query(
      `ALTER TABLE IF EXISTS organization ADD COLUMN IF NOT EXISTS "customsBroker" organization_customsbroker_enum NOT NULL DEFAULT '${OrganizationCustomsBroker.CLARO}';`
    );

    // Set existing organizations' broker to USAV
    await queryRunner.query(`
       UPDATE organization SET "customsBroker" = '${OrganizationCustomsBroker.USAV}';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop new column from organization table
    await queryRunner.query(`ALTER TABLE IF EXISTS organization DROP COLUMN IF EXISTS "customsBroker";`);

    // Drop organization customs broker enum
    await queryRunner.query(`DROP TYPE IF EXISTS organization_customsbroker_enum;`);
  }
}
