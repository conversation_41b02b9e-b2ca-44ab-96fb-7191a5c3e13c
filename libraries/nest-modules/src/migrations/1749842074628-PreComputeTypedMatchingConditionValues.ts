import { MigrationInterface, QueryRunner } from "typeorm";
import {
  DESTINATION_TABLE_ATTRIBUTES,
  DESTINATION_TABLE_CASE_INSENSITIVE_ATTRIBUTES,
  MatchingConditionAttributeType,
  MatchingConditionOperator,
  MatchingRuleDestinationDatabaseTable
} from "../types";
import { MatchingCondition } from "../entities";

export class PreComputeTypedMatchingConditionValues1749842074628 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create attribute type enum
    await queryRunner.query(`
        DO $$ BEGIN
        CREATE TYPE matching_condition_attributetype_enum AS ENUM (${Object.values(
          MatchingConditionAttributeType
        )
          .map((at) => `'${at}'`)
          .join(",")});
        EXCEPTION
        WHEN duplicate_object THEN null;
        END $$;
    `);

    // Create new columns
    await queryRunner.query(
      `ALTER TABLE IF EXISTS "matching_condition" ADD COLUMN IF NOT EXISTS "attributeType" matching_condition_attributetype_enum`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS "matching_condition" ADD COLUMN IF NOT EXISTS "valueString" varchar ARRAY`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS "matching_condition" ADD COLUMN IF NOT EXISTS "valueInteger" integer ARRAY`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS "matching_condition" ADD COLUMN IF NOT EXISTS "valueFloat" double precision ARRAY`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS "matching_condition" ADD COLUMN IF NOT EXISTS "valueBoolean" boolean ARRAY`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS "matching_condition" ADD COLUMN IF NOT EXISTS "valueDateTime" timestamp with time zone ARRAY`
    );

    // Convert existing values to the new types
    for (const [destinationTable, attributes] of Object.entries(DESTINATION_TABLE_ATTRIBUTES)) {
      const caseInsensitiveAttributes = DESTINATION_TABLE_CASE_INSENSITIVE_ATTRIBUTES[destinationTable];
      for (const [attribute, attributeType] of Object.entries(attributes)) {
        // Set attribute type
        await queryRunner.query(`
            UPDATE matching_condition mc
            SET "attributeType" = '${attributeType}'::matching_condition_attributetype_enum
            FROM matching_rule mr
            WHERE mc."ruleId" = mr.id AND mr."destinationTable" = '${destinationTable}' AND mc."attribute" = '${attribute}';
        `);

        // Set typed column if needed
        let typedColumnName: string | null = null,
          dbType: string | null = null;
        switch (attributeType) {
          case MatchingConditionAttributeType.STRING:
            typedColumnName = "valueString";
            dbType = "varchar";
            break;
          case MatchingConditionAttributeType.ID:
          case MatchingConditionAttributeType.INTEGER:
            typedColumnName = "valueInteger";
            dbType = "integer";
            break;
          case MatchingConditionAttributeType.FLOAT:
            typedColumnName = "valueFloat";
            dbType = "double precision";
            break;
          case MatchingConditionAttributeType.BOOLEAN:
            typedColumnName = "valueBoolean";
            dbType = "boolean";
            break;
          case MatchingConditionAttributeType.DATE_TIME:
            typedColumnName = "valueDateTime";
            dbType = "timestamptz";
            break;
          default:
            typedColumnName = null;
            dbType = null;
        }
        if (!typedColumnName) continue;

        // First update non-array rows
        await queryRunner.query(
          `
          UPDATE matching_condition mc
          SET "${typedColumnName}" = ARRAY[CASE WHEN mc."operator" = '${
            MatchingConditionOperator.EQUALS
          }' AND mc."value" IS NULL THEN NULL ELSE ${
            caseInsensitiveAttributes.includes(attribute)
              ? `UPPER(mc.value::${dbType})`
              : `mc.value::${dbType}`
          } END] 
          FROM matching_rule mr
          WHERE mc."ruleId" = mr.id AND mr."destinationTable" = $1 AND mc."attribute" = $2 AND mc."operator" != $3;
        `,
          [destinationTable, attribute, MatchingConditionOperator.IN]
        );

        // Then update array rows
        await queryRunner.query(
          `
          UPDATE matching_condition mc
          SET "${typedColumnName}" = ARRAY(
            SELECT json_array_elements_text(mc.value::json)
          )::${dbType}[]
          FROM matching_rule mr
          WHERE mc."ruleId" = mr.id AND mr."destinationTable" = $1 AND mc."attribute" = $2 AND mc."operator" = $3
        `,
          [destinationTable, attribute, MatchingConditionOperator.IN]
        );
        // const inMatchingConditions = await queryRunner.manager.findBy(MatchingCondition, {
        //   attribute,
        //   operator: MatchingConditionOperator.IN,
        //   rule: {
        //     destinationTable: destinationTable as MatchingRuleDestinationDatabaseTable
        //   }
        // });

        // for (const condition of inMatchingConditions) {
        //   const { id, value } = condition;
        //   const valueArray = JSON.parse(value) as Array<string | number | boolean>;
        //   await queryRunner.query(
        //     `
        //     UPDATE matching_condition mc
        //     SET "${typedColumnName}" = $2::${dbType}[]
        //     WHERE mc."id" = $1
        //   `,
        //     [
        //       id,
        //       caseInsensitiveAttributes.includes(attribute)
        //         ? valueArray.map((v) => v.toString().toUpperCase())
        //         : valueArray
        //     ]
        //   );
        // }
      }
    }

    // Add not null constraint to attribute type column
    await queryRunner.query(`
        ALTER TABLE IF EXISTS "matching_condition" ALTER COLUMN "attributeType" SET NOT NULL;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop new columns
    await queryRunner.query(
      `ALTER TABLE IF EXISTS "matching_condition" DROP COLUMN IF EXISTS "attributeType"`
    );
    await queryRunner.query(`ALTER TABLE IF EXISTS "matching_condition" DROP COLUMN IF EXISTS "valueString"`);
    await queryRunner.query(
      `ALTER TABLE IF EXISTS "matching_condition" DROP COLUMN IF EXISTS "valueInteger"`
    );
    await queryRunner.query(`ALTER TABLE IF EXISTS "matching_condition" DROP COLUMN IF EXISTS "valueFloat"`);
    await queryRunner.query(
      `ALTER TABLE IF EXISTS "matching_condition" DROP COLUMN IF EXISTS "valueBoolean"`
    );
    await queryRunner.query(
      `ALTER TABLE IF EXISTS "matching_condition" DROP COLUMN IF EXISTS "valueDateTime"`
    );

    // Drop enum
    await queryRunner.query(`DROP TYPE IF EXISTS matching_condition_attributetype_enum;`);
  }
}
