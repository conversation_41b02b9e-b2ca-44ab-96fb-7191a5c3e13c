import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddMessageIdToEmail1748465681000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // await queryRunner.addColumn(
    //   "email",
    //   new TableColumn({
    //     name: "messageId",
    //     type: "varchar",
    //     isNullable: true,
    //     comment: "RFC 2822 Message-ID header for email threading"
    //   })
    // );
    await queryRunner.query(`
      ALTER TABLE IF EXISTS "email" ADD COLUMN IF NOT EXISTS "messageId" varchar;
    `);
    await queryRunner.query(`
      DO $$
      BEGIN
        IF EXISTS (SELECT column_name FROM information_schema.columns WHERE table_name = 'email' AND column_name = 'messageId') THEN
          COMMENT ON COLUMN "email"."messageId" IS 'RFC 2822 Message-ID header for email threading';
        END IF;
      END
      $$
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // await queryRunner.dropColumn("email", "messageId");
    await queryRunner.query(`
      ALTER TABLE IF EXISTS "email" DROP COLUMN IF EXISTS "messageId";
    `);
  }
}
