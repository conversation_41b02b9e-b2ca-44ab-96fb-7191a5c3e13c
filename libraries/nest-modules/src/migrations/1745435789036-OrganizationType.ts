import { MigrationInterface, QueryRunner } from "typeorm";
import { OrganizationType, PredefinedOrganizationName } from "../types/organization.types";

export class OrganizationType1745435789036 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TYPE enum_organization_organizationtype AS ENUM (${Object.values(OrganizationType)
        .map((ot) => `'${ot}'`)
        .join(",")});
    `);

    await queryRunner.query(`
        ALTER TABLE "organization" 
        ADD COLUMN IF NOT EXISTS "organizationType" enum_organization_organizationtype 
        DEFAULT '${OrganizationType.TRADING}'
      `);

    await queryRunner.query(`
        UPDATE "organization"
        SET "organizationType" = '${OrganizationType.TRADING}'
        WHERE "organizationType" IS NULL
      `);

    await queryRunner.query(`
        UPDATE "organization"
        SET "organizationType" = '${OrganizationType.BACKOFFICE}'
        WHERE "name" = '${PredefinedOrganizationName.ANTEK_LOGISTICS}'
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE "organization" 
        DROP COLUMN IF EXISTS "organizationType"
      `);
  }
}
