import { MigrationInterface, QueryRunner, Table } from "typeorm";
import { ShipmentStatus, TrackingStatus } from "../types";

export class ShipmentContainer1742492840634 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create container status enum
    await queryRunner.query(`DO $$ BEGIN
      CREATE TYPE container_status_enum AS ENUM (${Object.values(ShipmentStatus)
        .map((s) => `'${s}'`)
        .join(",")}); 
      EXCEPTION 
      WHEN duplicate_object THEN null; 
      END $$;`);

    // Create container tracking status enum
    await queryRunner.query(`DO $$ BEGIN 
      CREATE TYPE container_trackingstatus_enum AS ENUM (${Object.values(TrackingStatus)
        .map((ts) => `'${ts}'`)
        .join(",")});
      EXCEPTION
      WHEN duplicate_object THEN null;
      END $$;`);

    // Create container table
    await queryRunner.query(`CREATE TABLE IF NOT EXISTS container (
      id serial4 PRIMARY KEY,
      "createDate" timestamptz NOT NULL DEFAULT now(),
      "lastEditedDate" timestamptz NOT NULL DEFAULT now(),
      status container_status_enum NOT NULL DEFAULT 'new',
      "trackingStatus" container_trackingstatus_enum NOT NULL DEFAULT 'offline',
      "containerNumber" varchar NOT NULL,
      "shipmentId" int4 NOT NULL REFERENCES shipment(id) ON DELETE CASCADE,
      "createdById" int4 REFERENCES "user"(id) ON DELETE SET NULL,
      "lastEditedById" int4 REFERENCES "user"(id) ON DELETE SET NULL
    );`);

    // Insert container data from shipment table
    await queryRunner.query(`INSERT INTO container (status, "trackingStatus", "containerNumber", "shipmentId") 
      SELECT status::varchar::container_status_enum as status, "trackingStatus"::varchar::container_trackingstatus_enum as "trackingStatus", "containerNumber", id as "shipmentId" 
      FROM shipment
      WHERE "containerNumber" IS NOT NULL;`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Copy container records back to shipment table
    await queryRunner.query(
      `UPDATE shipment
      SET "containerNumber" = c."containerNumber", status = c.status::varchar::shipment_status_enum, "trackingStatus" = c."trackingStatus"::varchar::shipment_trackingstatus_enum
      FROM (
        SELECT DISTINCT ON (c."shipmentId") *
        FROM container c 
        ORDER BY c."shipmentId" ASC, c."createDate" DESC
      ) AS c
      WHERE c."shipmentId" = shipment.id;`
    );

    // Drop container table
    await queryRunner.query(`DROP TABLE IF EXISTS container;`);

    // Drop container status enum
    await queryRunner.query(`DROP TYPE IF EXISTS container_status_enum;`);

    // Drop container tracking status enum
    await queryRunner.query(`DROP TYPE IF EXISTS container_trackingstatus_enum;`);
  }
}
