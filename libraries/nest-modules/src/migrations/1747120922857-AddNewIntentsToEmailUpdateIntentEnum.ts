import { MigrationInterface, QueryRunner } from "typeorm";

export class AddNewIntentsToEmailUpdateIntentEnum1747120922857 implements MigrationInterface {
  name = "AddNewIntentsToEmailUpdateIntentEnum1747120922857";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DO $$ 
      BEGIN
        ALTER TYPE "public"."email_update_intent_enum" ADD VALUE IF NOT EXISTS 'process-document';
        ALTER TYPE "public"."email_update_intent_enum" ADD VALUE IF NOT EXISTS 'documentation-coming';
        ALTER TYPE "public"."email_update_intent_enum" ADD VALUE IF NOT EXISTS 'request-rush-processing';
        ALTER TYPE "public"."email_update_intent_enum" ADD VALUE IF NOT EXISTS 'request-manual-processing';
        ALTER TYPE "public"."email_update_intent_enum" ADD VALUE IF NOT EXISTS 'request-hold-shipment';
        ALTER TYPE "public"."email_update_intent_enum" ADD VALUE IF NOT EXISTS 'request-cad-document';
        ALTER TYPE "public"."email_update_intent_enum" ADD VALUE IF NOT EXISTS 'request-rns-proof';
        ALTER TYPE "public"."email_update_intent_enum" ADD VALUE IF NOT EXISTS 'spam';
      END $$;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log(
      `INFO: The 'down' migration for 'AddNewIntentsToEmailUpdateIntentEnum1747120922857' does not remove enum values from 'email_update_intent_enum' for data integrity reasons.`
    );
  }
}
