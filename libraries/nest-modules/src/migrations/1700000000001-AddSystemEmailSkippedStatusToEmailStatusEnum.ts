import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSystemEmailSkippedStatusToEmailStatusEnum1700000000001 implements MigrationInterface {
  name = "AddSystemEmailSkippedStatusToEmailStatusEnum1700000000001";

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TYPE "public"."email_status_enum" ADD VALUE IF NOT EXISTS 'system-email-skipped';`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Removing enum values can be problematic if data exists.
    // This down migration is intentionally left simple.
    // Consider manual steps or more complex logic if rollback of this value is truly needed while preserving data.
    console.log(
      `INFO: The 'down' migration for 'AddSystemEmailSkippedStatusToEmailStatusEnum1700000000001' does not remove 'system-email-skipped' from 'email_status_enum' for data integrity reasons.`
    );
  }
}
