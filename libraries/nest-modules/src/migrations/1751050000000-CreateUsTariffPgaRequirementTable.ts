import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateUsTariffPgaRequirementTable1751050000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop existing table if it exists (from previous migration)
    await queryRunner.query(`DROP TABLE IF EXISTS "us_tariff_pga_requirement"`);

    // Create simplified pivot table for HS code to tariff flag code mappings
    await queryRunner.query(`
      CREATE TABLE "us_tariff_pga_requirement" (
        "id" SERIAL NOT NULL,
        "createDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "lastEditDate" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "hsCode" character varying(10) NOT NULL,
        "tariffFlagCode" character varying(3) NOT NULL,
        CONSTRAINT "PK_us_tariff_pga_requirement" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for performance
    await queryRunner.query(`
      CREATE INDEX "IDX_US_TARIFF_PGA_REQUIREMENT_HSCODE" ON "us_tariff_pga_requirement" ("hsCode")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_US_TARIFF_PGA_REQUIREMENT_TARIFF_FLAG_CODE" ON "us_tariff_pga_requirement" ("tariffFlagCode")
    `);

    // Create unique constraint to prevent duplicate hsCode + tariffFlagCode combinations
    await queryRunner.query(`
      ALTER TABLE "us_tariff_pga_requirement" 
      ADD CONSTRAINT "UQ_US_TARIFF_PGA_BUSINESS_KEY" UNIQUE ("hsCode", "tariffFlagCode")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS "us_tariff_pga_requirement"`);
  }
}
