import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddRateLimitReadUntilToGmailToken1748009199015 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // await queryRunner.addColumn(
    //   "gmail_token",
    //   new TableColumn({
    //     name: "rateLimitReadUntil",
    //     type: "timestamp with time zone",
    //     isNullable: true,
    //     comment: "Timestamp until which Gmail API read operations for this mailbox are rate limited"
    //   })
    // );
    await queryRunner.query(`
      ALTER TABLE IF EXISTS "gmail_token" ADD COLUMN IF NOT EXISTS "rateLimitReadUntil" timestamptz;  
    `);
    await queryRunner.query(`
      DO $$
      BEGIN
        IF EXISTS (SELECT column_name FROM information_schema.columns WHERE table_name = 'gmail_token' AND column_name = 'rateLimitReadUntil') THEN
          COMMENT ON COLUMN "gmail_token"."rateLimitReadUntil" IS 'Timestamp until which Gmail API read operations for this mailbox are rate limited';
        END IF;
      END
      $$
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // await queryRunner.dropColumn("gmail_token", "rateLimitReadUntil");
    await queryRunner.query(`
      ALTER TABLE IF EXISTS "gmail_token" DROP COLUMN IF EXISTS "rateLimitReadUntil";
    `);
  }
}
