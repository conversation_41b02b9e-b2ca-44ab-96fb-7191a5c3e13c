import { MigrationInterface, QueryRunner } from "typeorm";

export class SearchVectorShipment1749576722600 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add search_vector column if it doesn't exist
    await queryRunner.query(`
      ALTER TABLE IF EXISTS shipment ADD COLUMN IF NOT EXISTS search_vector tsvector;
    `);

    // Drop any existing triggers that might be using our function
    await queryRunner.query(`
      DROP TRIGGER IF EXISTS shipment_search_vector_trade_partner_trigger ON shipment;
      DROP TRIGGER IF EXISTS trade_partner_shipment_search_vector_trigger ON trade_partner;
      DROP FUNCTION IF EXISTS update_shipment_search_vector_on_trade_partner_change();
      DROP FUNCTION IF EXISTS update_shipment_search_vector_on_trade_partner_name_change();
    `);

    // Create trigger function for shipment changes
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION update_shipment_search_vector_on_trade_partner_change() R<PERSON><PERSON><PERSON> trigger AS $$
      DECLARE
        shipper_name TEXT;
        consignee_name TEXT;
      BEGIN
        -- Use LEFT JOIN logic in a correlated subquery to handle NULLs
        SELECT name INTO shipper_name FROM trade_partner WHERE id = NEW."shipperId";
        SELECT name INTO consignee_name FROM trade_partner WHERE id = NEW."consigneeId";

        NEW.search_vector := to_tsvector('english',
          coalesce(shipper_name, '') || ' ' || coalesce(consignee_name, '')
        );
        
        RETURN NEW;
      END
      $$ LANGUAGE plpgsql;
    `);

    // Create trigger function for trade partner name changes
    await queryRunner.query(`
      CREATE OR REPLACE FUNCTION update_shipment_search_vector_on_trade_partner_name_change() RETURNS trigger AS $$
      BEGIN
        -- Update search vectors for shipments where this trade partner is either shipper or consignee
        UPDATE shipment s
        SET search_vector = (
          SELECT to_tsvector('english',
            coalesce(shipper.name, '') || ' ' || coalesce(consignee.name, '')
          )
          FROM trade_partner shipper, trade_partner consignee
          WHERE (s."shipperId" = shipper.id AND s."consigneeId" = consignee.id)
            AND (s."shipperId" = NEW.id OR s."consigneeId" = NEW.id)
        );
        
        RETURN NEW;
      END
      $$ LANGUAGE plpgsql;
    `);

    // Create triggers
    await queryRunner.query(`
      CREATE TRIGGER shipment_search_vector_trade_partner_trigger
      BEFORE INSERT OR UPDATE ON shipment
      FOR EACH ROW
      EXECUTE FUNCTION update_shipment_search_vector_on_trade_partner_change();
    `);

    await queryRunner.query(`
      CREATE TRIGGER trade_partner_shipment_search_vector_trigger
      AFTER UPDATE OF name ON trade_partner
      FOR EACH ROW
      EXECUTE FUNCTION update_shipment_search_vector_on_trade_partner_name_change();
    `);

    // Create GIN index if it doesn't exist
    await queryRunner.query(`
      CREATE INDEX IF NOT EXISTS idx_shipment_search_vector ON shipment USING GIN(search_vector);
    `);

    // Populate existing records with proper NULL handling
    await queryRunner.query(`
      UPDATE shipment s
      SET search_vector = (
        SELECT to_tsvector('english',
          coalesce(shipper.name, '') || ' ' || coalesce(consignee.name, '')
        )
        FROM trade_partner shipper
        LEFT JOIN trade_partner consignee ON consignee.id = s."consigneeId"
        WHERE shipper.id = s."shipperId"
      );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX IF EXISTS idx_shipment_search_vector;`);
    await queryRunner.query(
      `DROP TRIGGER IF EXISTS shipment_search_vector_trade_partner_trigger ON shipment;`
    );
    await queryRunner.query(
      `DROP TRIGGER IF EXISTS trade_partner_shipment_search_vector_trigger ON trade_partner;`
    );
    await queryRunner.query(
      `DROP FUNCTION IF EXISTS update_shipment_search_vector_on_trade_partner_change() CASCADE;`
    );
    await queryRunner.query(
      `DROP FUNCTION IF EXISTS update_shipment_search_vector_on_trade_partner_name_change();`
    );
    await queryRunner.query(`ALTER TABLE shipment DROP COLUMN IF EXISTS search_vector;`);
  }
}
