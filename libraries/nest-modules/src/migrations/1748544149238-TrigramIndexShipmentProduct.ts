import { MigrationInterface, QueryRunner } from "typeorm";

export class TrigramIndexShipmentProduct1748544149238 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Enable pg_trgm extension
    await queryRunner.query(`CREATE EXTENSION IF NOT EXISTS pg_trgm;`);

    // === Shipment ===
    await queryRunner.query(
      `CREATE INDEX idx_shipment_hbl_trgm ON shipment USING GIN ("hblNumber" gin_trgm_ops);`
    );
    await queryRunner.query(
      `CREATE INDEX idx_shipment_mbl_trgm ON shipment USING GIN ("mblNumber" gin_trgm_ops);`
    );
    await queryRunner.query(
      `CREATE INDEX idx_shipment_transaction_number_trgm ON shipment USING GIN ("transactionNumber" gin_trgm_ops);`
    );
    await queryRunner.query(
      `CREATE INDEX idx_shipment_customs_file_number_trgm ON shipment USING GIN ("customsFileNumber" gin_trgm_ops);`
    );
    await queryRunner.query(
      `CREATE INDEX idx_shipment_cargo_control_number_trgm ON shipment USING GIN ("cargoControlNumber" gin_trgm_ops);`
    );
    await queryRunner.query(
      `CREATE INDEX idx_container_number_trgm ON container USING GIN ("containerNumber" gin_trgm_ops);`
    );

    // === Product ===
    await queryRunner.query(
      `CREATE INDEX idx_product_part_number_trgm ON product USING GIN ("partNumber" gin_trgm_ops);`
    );
    await queryRunner.query(
      `CREATE INDEX idx_product_hscode_trgm ON product USING GIN ("hsCode" gin_trgm_ops);`
    );
    await queryRunner.query(`CREATE INDEX idx_product_sku_trgm ON product USING GIN (sku gin_trgm_ops);`);
    await queryRunner.query(`CREATE INDEX idx_product_upc_trgm ON product USING GIN (upc gin_trgm_ops);`);
    await queryRunner.query(
      `CREATE INDEX idx_product_vendor_part_number_trgm ON product USING GIN ("vendorPartNumber" gin_trgm_ops);`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // === Shipment ===
    await queryRunner.query(`DROP INDEX IF EXISTS idx_shipment_cargo_control_number_trgm;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_shipment_customs_file_number_trgm;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_shipment_transaction_number_trgm;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_container_number_trgm;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_shipment_mbl_trgm;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_shipment_hbl_trgm;`);

    // === Product ===
    await queryRunner.query(`DROP INDEX IF EXISTS idx_product_vendor_part_number_trgm;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_product_upc_trgm;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_product_sku_trgm;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_product_hscode_trgm;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_product_part_number_trgm;`);

    // Optionally: Remove extension (if not used elsewhere)
    await queryRunner.query(`DROP EXTENSION IF EXISTS pg_trgm;`);
  }
}
