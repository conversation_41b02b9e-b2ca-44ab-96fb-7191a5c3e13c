import { MigrationInterface, QueryRunner } from "typeorm";
import { CanadaExciseTaxCode, MatchingCondition } from "../entities";
import { MatchingRule } from "../entities/matching-rule.entity";
import { MatchingConditionOperator } from "../types/matching-condition.types";
import {
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus
} from "../types/matching-rule.types";

export class AddDefaultRuleForExciseTaxCodeCannabis1749576722518 implements MigrationInterface {
  private readonly MATCHED_HS_CODES = [
    "0409000010",
    "0803900090",
    "0804100020",
    "0804200020",
    "0804300020",
    "0804500020",
    "0805100090",
    "0805290000",
    "0805500030",
    "0806200000",
    "0813100000",
    "0813200000",
    "0813300000",
    "0813400010",
    "0813400090",
    "1515900090",
    "1704100010",
    "1704100090",
    "1704902000",
    "1704909020",
    "1704909050",
    "1704909090",
    "1806109000",
    "1806209090",
    "1806310000",
    "1806320010",
    "1806320090",
    "1806909011",
    "1806909012",
    "1806909013",
    "1806909019",
    "1806909091",
    "1806909092",
    "1806909099",
    "1901201111",
    "1901201119",
    "1901201120",
    "1901201211",
    "1901201219",
    "1901201220",
    "1901201310",
    "1901201320",
    "1901201410",
    "1901201420",
    "1901201511",
    "1901201519",
    "1901201520",
    "1901201910",
    "1901201920",
    "1901202111",
    "1901202119",
    "1901202120",
    "1901202211",
    "1901202219",
    "1901202220",
    "1901202300",
    "1901202400",
    "1901202910",
    "1901202920",
    "1901903310",
    "1901903390",
    "1901903410",
    "1901903490",
    "1901903910",
    "1901903990",
    "1901904010",
    "1901904090",
    "1901905300",
    "1901905400",
    "1901905900",
    "1905200000",
    "1905311000",
    "1905312100",
    "1905312200",
    "1905312300",
    "1905312900",
    "1905319100",
    "1905319200",
    "1905319300",
    "1905319900",
    "1905321000",
    "1905329100",
    "1905329200",
    "1905329300",
    "1905329900",
    "1905905910",
    "1905905991",
    "1905905998",
    "1905905999",
    "2007100000",
    "2007910011",
    "2007910012",
    "2007910019",
    "2007910090",
    "2007991000",
    "2007992000",
    "2007999041",
    "2007999042",
    "2007999043",
    "2007999044",
    "2007999045",
    "2007999049",
    "2007999092",
    "2007999093",
    "2007999099",
    "2008111000",
    "2008119000",
    "2009110010",
    "2009110020",
    "2009120010",
    "2009120090",
    "2009190000",
    "2009210000",
    "2009290000",
    "2009310010",
    "2009310090",
    "2009390011",
    "2009390019",
    "2009390090",
    "2009410000",
    "2009490000",
    "2009500000",
    "2009611000",
    "2009619000",
    "2009691010",
    "2009691020",
    "2009699000",
    "2009711000",
    "2009719000",
    "2009791110",
    "2009791190",
    "2009791900",
    "2009799000",
    "2009810000",
    "2009891010",
    "2009891020",
    "2009891081",
    "2009891089",
    "2009891090",
    "2009892000",
    "2009901000",
    "2009902000",
    "2009903010",
    "2009903020",
    "2009903090",
    "2009904000",
    "2101200090",
    "2105001000",
    "2105009190",
    "2105009290",
    "2106901040",
    "2106901050",
    "2106901090",
    "2106902100",
    "2106902910",
    "2106902990",
    "2106903200",
    "2106903400",
    "2106903500",
    "2106903910",
    "2106903920",
    "2106903990",
    "2106904110",
    "2106904120",
    "2106909100",
    "2106909200",
    "2106909700",
    "2106909800",
    "2106909910",
    "2106909931",
    "2106909939",
    "2106909940",
    "2106909991",
    "2106909992",
    "2106909999",
    "2202100011",
    "2202100019",
    "2202100090",
    "2202910000",
    "2202991000",
    "2202992110",
    "2202992190",
    "2202992200",
    "2202993910",
    "2202993920",
    "2202999090",
    "3004900021",
    "3301290000",
    "3301900000",
    "3302100000",
    "3302900010",
    "3302900090",
    "3303000010",
    "3304100000",
    "3304999020",
    "3305100000",
    "3307300000",
    "3401119000",
    "3401190010",
    "3401190090"
  ];

  public async up(queryRunner: QueryRunner): Promise<void> {
    const source = await queryRunner.manager.getRepository(CanadaExciseTaxCode).findOne({
      where: {
        code: "C00"
      },
      select: ["id"]
    });

    if (!source) {
      throw new Error("Canada Excise Tax Code with code C00 not found");
    }

    // foreach, create rules for each hs code
    const ruleRepository = queryRunner.manager.getRepository(MatchingRule);
    const conditionRepository = queryRunner.manager.getRepository(MatchingCondition);
    const rule = await ruleRepository.save({
      status: MatchingRuleStatus.ACTIVE,
      name: `DefaultCanadaExciseTaxCode - Cannabis`,
      priority: 0,
      sourceTable: MatchingRuleSourceDatabaseTable.CANADA_EXCISE_TAX_CODE,
      sourceId: source.id,
      destinationTable: MatchingRuleDestinationDatabaseTable.PRODUCT
    });

    await conditionRepository.save({
      rule: {
        id: rule.id
      },
      attribute: "hsCode",
      operator: MatchingConditionOperator.IN,
      value: JSON.stringify(this.MATCHED_HS_CODES)
    });
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    const ruleRepository = queryRunner.manager.getRepository(MatchingRule);
    const conditionRepository = queryRunner.manager.getRepository(MatchingCondition);
    const rule = await ruleRepository.findOne({
      where: {
        name: `DefaultCanadaExciseTaxCode - Cannabis`
      }
    });
    if (rule) {
      await ruleRepository.delete(rule.id);
      await conditionRepository.delete({
        rule: {
          id: rule.id
        }
      });
    }
  }
}
