import { Injectable } from "@nestjs/common";
import { ValidationArguments, ValidatorConstraint } from "class-validator";
import { AbstractDatabaseValidator } from "./abstract-database.validator";

@Injectable()
@ValidatorConstraint({ async: true })
export class IsExistConstraint extends AbstractDatabaseValidator {
  async validate(value: string, args: ValidationArguments): Promise<boolean> {
    const [entity, columnName, where] = args.constraints;
    return this.dataSource.getRepository(entity).existsBy({
      [columnName]: value,
      ...(typeof where === "function" ? where(args.object) : where)
    });
  }

  defaultMessage(args: ValidationArguments) {
    const [entity, columnName] = args.constraints;
    return `no record found for the ${columnName} in ${entity.name}`;
  }
}
