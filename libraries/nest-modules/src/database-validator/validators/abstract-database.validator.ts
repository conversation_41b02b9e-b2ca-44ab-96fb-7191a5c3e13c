import { Injectable } from "@nestjs/common";
import { InjectDataSource } from "@nestjs/typeorm";
import { ValidationArguments, ValidatorConstraintInterface } from "class-validator";
import { DataSource } from "typeorm";

@Injectable()
export abstract class AbstractDatabaseValidator implements ValidatorConstraintInterface {
  constructor(
    @InjectDataSource()
    protected readonly dataSource: DataSource
  ) {
    if (!this.dataSource) {
      throw new Error("DataSource is not initialized");
    }
  }

  abstract validate(value: string, args: ValidationArguments): Promise<boolean>;

  abstract defaultMessage(args: ValidationArguments): string;
}
