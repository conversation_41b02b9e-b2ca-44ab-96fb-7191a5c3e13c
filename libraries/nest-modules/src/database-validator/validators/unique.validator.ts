import { Injectable } from "@nestjs/common";
import { ValidationArguments, ValidatorConstraint } from "class-validator";
import { Not } from "typeorm";
import { AbstractDatabaseValidator } from "./abstract-database.validator";

@Injectable()
@ValidatorConstraint({ async: true })
export class IsUniqueConstraint extends AbstractDatabaseValidator {
  async validate(value: string, args: ValidationArguments): Promise<boolean> {
    const [entity, columnName, options] = args.constraints;

    const where = options?.where || {};

    if (options?.except) {
      const exceptValue = args.object[options.except];
      where[options.except] = Not(exceptValue);
    }

    where[columnName] = value;

    const exists = await this.dataSource.getRepository(entity).existsBy(where);

    return !exists;
  }

  defaultMessage(args: ValidationArguments) {
    const [entity, columnName] = args.constraints;
    return `The ${columnName} must be unique in ${entity.name}`;
  }
}
