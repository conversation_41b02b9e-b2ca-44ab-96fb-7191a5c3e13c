import { ValidationOptions, registerDecorator } from "class-validator";
import { FindOptionsWhere } from "typeorm";
import { SimplifiedBaseEntity } from "../entities/base.entity";
import { IsExistConstraint, IsUniqueConstraint } from "./validators";

export function IsExist<T extends SimplifiedBaseEntity, O extends object>(
  entity: new () => T,
  columnName: keyof T = "id",
  where?: FindOptionsWhere<T> | ((entity: O) => FindOptionsWhere<T>),
  validationOptions?: ValidationOptions
) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      constraints: [entity, columnName, where],
      options: validationOptions,
      validator: IsExistConstraint
    });
  };
}

export function IsUnique<T extends SimplifiedBaseEntity>(
  entity: new () => T,
  columnName: keyof T = "id",
  options?: {
    except?: keyof T;
    where?: FindOptionsWhere<T>;
  },
  validationOptions?: ValidationOptions
) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      constraints: [entity, columnName, options],
      options: validationOptions,
      validator: IsUniqueConstraint
    });
  };
}
