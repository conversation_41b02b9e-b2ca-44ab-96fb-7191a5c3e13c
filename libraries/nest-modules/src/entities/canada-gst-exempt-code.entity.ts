import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { AfterInsert, AfterLoad, AfterUpdate, Column, Entity, ManyToOne, OneToMany, Unique } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { CommercialInvoiceLine } from "./commercial-invoice-line.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedCanadaGstExemptCode extends SimplifiedBaseEntity {
  @ApiProperty({ type: "integer", minimum: 1 })
  @Column({ type: "integer" })
  code: number;

  @ApiProperty({ minLength: 1 })
  @Column()
  explanation: string;

  // Compute fields
  @ApiProperty({ type: "string", minLength: 1 })
  displayName: string;

  @AfterInsert()
  @AfterLoad()
  @AfterUpdate()
  compute() {
    this.displayName = `${this.code} - ${this.explanation}`;
  }
}

@Entity()
@Unique("UNIQUE_CANADA_GST_EXEMPT_CODE", ["code"])
export class CanadaGstExemptCode extends SimplifiedCanadaGstExemptCode {
  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdCanadaGstExemptCodes, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedCanadaGstExemptCodes, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  // Hidden fields
  @OneToMany((type) => CommercialInvoiceLine, (line) => line.gstExemptCode)
  commercialInvoiceLines: Promise<Array<CommercialInvoiceLine>>;
}
