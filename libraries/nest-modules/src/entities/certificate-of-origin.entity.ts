import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import moment from "moment-timezone";
import { AfterInsert, AfterLoad, AfterUpdate, Column, Entity, ManyToOne } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { Country, SimplifiedCountry } from "./country.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { SimplifiedTradePartner, TradePartner } from "./trade-partner.entity";

export class SimplifiedCertificateOfOrigin extends SimplifiedBaseEntity {
  @ApiPropertyOptional({ type: "string", format: "date" })
  @Column({ type: "timestamp with time zone", nullable: true })
  validFrom: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @Column({ type: "timestamp with time zone", nullable: true })
  validTo: Date | null;

  @ApiProperty({ type: "boolean" })
  isValid: boolean;

  @AfterLoad()
  @AfterInsert()
  @AfterUpdate()
  compute() {
    const now = moment();

    if (!this.validFrom || !this.validTo) {
      this.isValid = false;
      return;
    }

    this.isValid = moment(this.validFrom).isBefore(now) && moment(this.validTo).isAfter(now);
  }
}

@Entity()
export class CertificateOfOrigin extends SimplifiedCertificateOfOrigin {
  @ApiPropertyOptional({ type: () => SimplifiedTradePartner })
  @ManyToOne(() => TradePartner, { nullable: true, onDelete: "SET NULL" })
  exporter?: TradePartner;

  @ApiPropertyOptional({ type: () => SimplifiedTradePartner })
  @ManyToOne(() => TradePartner, { nullable: true, onDelete: "SET NULL" })
  producer?: TradePartner;

  @ApiPropertyOptional({ type: () => SimplifiedTradePartner })
  @ManyToOne(() => TradePartner, { nullable: true, onDelete: "SET NULL" })
  importer?: TradePartner;

  @ApiPropertyOptional({ type: () => SimplifiedCountry })
  @ManyToOne(() => Country, { nullable: true })
  countryOfOrigin?: Country;

  @ApiPropertyOptional({ type: () => SimplifiedOrganization })
  @ManyToOne(() => Organization, { nullable: true })
  organization?: Organization;
}
