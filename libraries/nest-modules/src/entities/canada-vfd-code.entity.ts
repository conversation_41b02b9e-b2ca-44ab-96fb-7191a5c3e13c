import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { SimplifiedUser, User } from "./user.entity";
import { SimplifiedBaseEntity } from "./base.entity";
import { Column, Entity, Unique, ManyToOne, OneToMany, AfterInsert, AfterLoad, AfterUpdate } from "typeorm";
import { CommercialInvoiceLine, SimplifiedCommercialInvoiceLine } from "./commercial-invoice-line.entity";
export class SimplifiedCanadaVfdCode extends SimplifiedBaseEntity {
  @ApiProperty({ type: "integer", minimum: 1 })
  @Column({ type: "integer" })
  code: number;

  @ApiProperty({ minLength: 1 })
  @Column()
  explanation: string;

  // Compute fields
  @ApiProperty({ type: "string", minLength: 1 })
  displayName: string;

  @AfterInsert()
  @AfterLoad()
  @AfterUpdate()
  compute() {
    this.displayName = `${this.code} - ${this.explanation}`;
  }
}

@Entity()
@Unique("UNIQUE_CANADA_VFD_CODE", ["code"])
export class CanadaVfdCode extends SimplifiedCanadaVfdCode {
  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdCanadaVfdCodes, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedCanadaVfdCodes, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  // hidden fields
  @OneToMany((type) => CommercialInvoiceLine, (invoiceLine) => invoiceLine.vfd)
  commercialInvoiceLines: Array<CommercialInvoiceLine> | null;
}
