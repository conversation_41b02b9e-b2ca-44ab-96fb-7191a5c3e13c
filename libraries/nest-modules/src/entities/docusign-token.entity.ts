import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, CreateDateColumn, Entity, ManyToOne, PrimaryColumn, UpdateDateColumn } from "typeorm";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedDocusignToken {
  @ApiProperty()
  @PrimaryColumn()
  accountId: string;

  @ApiProperty({ type: "string", format: "uri" })
  @Column()
  baseUri: string;

  @ApiProperty({ type: "string", format: "jwt" })
  @Column()
  accessToken: string;

  @ApiProperty({ type: "string", format: "jwt" })
  @Column()
  refreshToken: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone" })
  accessTokenExpiresIn: Date;

  @ApiProperty({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone" })
  refreshTokenExpiresIn: Date;

  @ApiProperty({ type: "string", format: "date-time" })
  @CreateDateColumn({ type: "timestamp with time zone" })
  createDate: Date;

  @ApiProperty({ type: "string", format: "date-time" })
  @UpdateDateColumn({ type: "timestamp with time zone" })
  lastEditDate: Date;
}

@Entity()
export class DocusignToken extends SimplifiedDocusignToken {
  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdDocusignTokens, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedDocusignTokens, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;
}
