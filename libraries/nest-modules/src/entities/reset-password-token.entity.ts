import { Column, Entity, <PERSON>T<PERSON><PERSON>ne, Unique } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { ApiProperty } from "@nestjs/swagger";
import { SimplifiedUser, User } from "./user.entity";

@Unique("UNIQUE_RESET_PWD_HASHED_TOKEN", ["hashedToken"])
@Entity()
export class ResetPasswordToken extends SimplifiedBaseEntity {
  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, {
    onDelete: "CASCADE",
    nullable: false
  })
  user: User;

  // @ApiProperty({
  //   type: "string",
  //   minLength: 64,
  //   maxLength: 64,
  //   example: "f3a9d8e5b6c4a2f1e0d9c8b7a6f5e4d3c2b1a09f8e7d6c5b4a39281716151413"
  // })
  // @Column({ type: "varchar", length: 64, nullable: false })
  // token: string;

  @ApiProperty({
    type: "string",
    description: "Token hashed with SHA-256",
    example: "f3a9d8e5b6c4a2f1e0d9c8b7a6f5e4d3c2b1a09f8e7d6c5b4a39281716151413"
  })
  @Column({ type: "varchar", length: 64, nullable: false })
  hashedToken: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: false })
  expiryDate: Date;
}
