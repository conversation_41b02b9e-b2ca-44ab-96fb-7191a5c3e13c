import { ApiProperty } from "@nestjs/swagger";
import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
  UpdateDateColumn
} from "typeorm";
import { FileBatchCreator, FileBatchStatus } from "../types";
import { DocumentAggregation } from "./document-aggregation.entity";
import { File } from "./file.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { Shipment, SimplifiedShipment } from "./shipment.entity";

export class SimplifiedFileBatch {
  @PrimaryColumn()
  id: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @CreateDateColumn({ type: "timestamp with time zone" })
  createDate: Date;

  @ApiProperty({ type: "string", format: "date-time" })
  @UpdateDateColumn({ type: "timestamp with time zone" })
  lastEditDate: Date;

  @ApiProperty({ type: "string", enum: FileBatchCreator })
  @Column({
    type: "enum",
    enum: FileBatchCreator,
    default: FileBatchCreator.API
  })
  creator: FileBatchCreator;

  @ApiProperty({ type: "number" })
  @Column({ type: "integer", nullable: true })
  shipmentId: number | null;

  @ApiProperty({ type: "number" })
  @Column({ type: "integer", nullable: true })
  organizationId: number | null;

  @ApiProperty({ type: "string", enum: FileBatchStatus })
  @Column({ type: "enum", enum: FileBatchStatus, default: FileBatchStatus.UPLOADED })
  status: FileBatchStatus;
}

@Entity()
export class FileBatch extends SimplifiedFileBatch {
  @ApiProperty({ type: () => SimplifiedOrganization })
  @ManyToOne((type) => Organization, {
    nullable: false,
    onDelete: "SET NULL"
  })
  organization: Organization | null;

  @ApiProperty({ type: () => File })
  @OneToMany(() => File, (file) => file.batch, {
    nullable: true,
    onDelete: "SET NULL"
  })
  files: File[];

  @ApiProperty({ type: () => SimplifiedShipment })
  @ManyToOne((type) => Shipment, (shipment) => shipment.files, {
    nullable: true,
    onDelete: "SET NULL"
  })
  shipment: Shipment | null;

  @ApiProperty({ type: () => DocumentAggregation })
  @OneToMany(() => DocumentAggregation, (aggregation) => aggregation.batch)
  documentAggregations: DocumentAggregation[];
}
