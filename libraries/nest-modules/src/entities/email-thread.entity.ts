import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity, ManyToOne, Unique } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { Shipment, SimplifiedShipment } from "./shipment.entity";

@Entity()
@Unique("EMAIL_THREAD_UNIQUE_THREAD_ID", ["threadId"])
export class EmailThread extends SimplifiedBaseEntity {
  @ApiProperty({ type: "string", minLength: 1, description: "Gmail Thread ID" })
  @Column({ type: "varchar", nullable: false })
  threadId: string;

  @ApiProperty({ type: () => SimplifiedShipment })
  @ManyToOne((type) => Shipment, { nullable: false, onDelete: "CASCADE" })
  shipment: Shipment;
}
