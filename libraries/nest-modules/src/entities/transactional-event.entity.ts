import { Column, Entity } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

@Entity()
export class TransactionalEvent extends SimplifiedBaseEntity {
  @ApiProperty({ type: "string", format: "uuid" })
  @Column({ type: "varchar", nullable: false })
  transactionId: string;

  @ApiProperty({ type: "string" })
  @Column({ type: "varchar", nullable: false })
  event: string;

  @ApiPropertyOptional({
    type: "string",
    description: "Stringified JSON array of arguments to be provided to the event handler"
  })
  @Column({ type: "varchar", nullable: true })
  valuesJson: string | null;
}
