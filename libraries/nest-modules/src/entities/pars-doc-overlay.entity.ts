import { Column, <PERSON><PERSON>ty, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import { File } from "./file.entity";

@Entity()
export class ParsDocOverlay {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: "integer" })
  page: number;

  @Column({ nullable: true })
  portOfEntry: string;

  @Column({ nullable: true })
  etaPort: string;

  @Column({ nullable: true })
  etd: string;

  @Column({ nullable: true })
  ccn: string;

  @Column({ type: "integer" })
  fileId: number;

  @ManyToOne(() => File, (file) => file.id)
  file: File;
}
