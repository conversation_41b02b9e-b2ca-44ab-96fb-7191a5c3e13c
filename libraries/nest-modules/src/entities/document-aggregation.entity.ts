import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn
} from "typeorm";
import {
  AggregationTargetType,
  DocumentAggregationAction,
  DocumentAggregationStatus
} from "../types/document-aggregation.types";
import { DocumentAggregationStep } from "./document-aggregation-step.entity";
import { Document } from "./document.entity";
import { FileBatch, SimplifiedFileBatch } from "./file-batch.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { Shipment, SimplifiedShipment } from "./shipment.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedDocumentAggregation {
  @ApiProperty({ type: "integer", minimum: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    enum: DocumentAggregationStatus,
    description: "The status of the document aggregation"
  })
  @Column({ type: "enum", enum: DocumentAggregationStatus })
  status: DocumentAggregationStatus;

  @ApiProperty({
    enum: DocumentAggregationAction,
    description: "The action to perform on the document aggregation"
  })
  @Column({ type: "enum", enum: DocumentAggregationAction })
  action: DocumentAggregationAction;

  @ApiProperty({
    type: Number,
    description: "The id of the organization"
  })
  @Column({ type: "integer" })
  organizationId: number;

  @ApiProperty({
    type: Number,
    description: "The id of the document aggregation that this one depends on"
  })
  @Column({ type: "integer", nullable: true })
  dependsOnId: number | null;

  @ApiProperty({
    type: String,
    description: "The batch id of the document aggregation"
  })
  // TODO: change to relationId, or remove this field
  @Column({ nullable: true })
  batchId: string | null;

  @ApiProperty({
    type: String,
    description: "The job id of the document aggregation"
  })
  @Column({ type: "varchar", nullable: true })
  jobId: string | null;

  @ApiProperty({ type: "string", format: "date-time" })
  @CreateDateColumn({ type: "timestamp with time zone" })
  createDate: Date;

  @ApiProperty({ type: "string", format: "date-time" })
  @UpdateDateColumn({ type: "timestamp with time zone" })
  lastEditDate: Date;

  @ApiProperty({
    type: String,
    description: "The target type of the document aggregation"
  })
  @Column({ type: "enum", enum: AggregationTargetType, nullable: true })
  targetType: AggregationTargetType | null;

  @ApiProperty({
    type: String,
    description: "The target id of the document aggregation"
  })
  @Column({ type: "integer", nullable: true })
  targetId: number | null;
}

@Entity()
export class DocumentAggregation extends SimplifiedDocumentAggregation {
  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdDocumentAggregations, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedDocumentAggregations, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  @ApiProperty({ type: () => SimplifiedOrganization })
  @ManyToOne((type) => Organization, (org) => org.documentAggregations, {
    nullable: false,
    onDelete: "CASCADE"
  })
  organization: Organization;

  @ApiProperty({ type: () => [Document] })
  @OneToMany((type) => Document, (document) => document.aggregation)
  documents: Array<Document>;

  @ApiPropertyOptional({ type: () => SimplifiedShipment })
  @JoinColumn({ name: "shipmentId" })
  @ManyToOne((type) => Shipment, {
    nullable: true,
    onDelete: "SET NULL"
  })
  shipment: Shipment | null;

  @ApiProperty({ type: () => [DocumentAggregationStep] })
  @OneToMany(() => DocumentAggregationStep, (step) => step.documentAggregation)
  steps: DocumentAggregationStep[];

  @ApiProperty({ type: () => DocumentAggregation })
  @ManyToOne(() => DocumentAggregation, (aggregation) => aggregation.dependencies)
  dependsOn: DocumentAggregation | null;

  @ApiProperty({ type: () => [DocumentAggregation] })
  @OneToMany(() => DocumentAggregation, (aggregation) => aggregation.dependsOn)
  dependencies: DocumentAggregation[];

  @ApiProperty({ type: () => SimplifiedFileBatch })
  @ManyToOne(() => FileBatch, (batch) => batch.documentAggregations, {
    nullable: true,
    onDelete: "SET NULL"
  })
  batch: FileBatch | null;
}
