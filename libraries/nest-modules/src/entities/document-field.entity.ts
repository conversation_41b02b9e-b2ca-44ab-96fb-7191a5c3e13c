import { ApiProperty } from "@nestjs/swagger";
import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, Unique } from "typeorm";
import { DocumentFieldDataType } from "../types";
import { SimplifiedBaseEntity } from "./base.entity";
import { Document, SimplifiedDocument } from "./document.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedDocumentField extends SimplifiedBaseEntity {
  @ApiProperty({ description: "Name of the document field" })
  @Column()
  name: string;

  @ApiProperty({
    description: "Data type of the document field",
    enum: DocumentFieldDataType
  })
  @Column({ type: "enum", enum: DocumentFieldDataType })
  dataType: DocumentFieldDataType;

  @ApiProperty({ description: "Value of the document field" })
  @Column({ type: "text", nullable: true })
  value: string;

  @ApiProperty({ description: "Original value of the document field" })
  @Column({ type: "text", nullable: true })
  originalValue: string;

  @ApiProperty({ description: "Locked field will not be overridden by the AI" })
  @Column({ type: "boolean", default: false })
  locked: boolean;
}

@Entity()
@Unique("UQ_FIELD_NAME_DOCUMENT", ["name", "document"])
export class DocumentField extends SimplifiedDocumentField {
  @ApiProperty({ type: () => SimplifiedDocument })
  @ManyToOne(() => Document, (document) => document.fields, {
    nullable: false,
    onDelete: "CASCADE",
    orphanedRowAction: "delete"
  })
  @JoinColumn({ name: "documentId" })
  document: Document;

  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne(() => User, (user) => user.createdDocumentFields, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne(() => User, (user) => user.lastEditedDocumentFields, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;
}
