import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn } from "typeorm";
import { MatchingRuleDestinationDatabaseTable, MatchingRuleSourceDatabaseTable } from "../types";

@Entity()
@Index("IDX_MATCHING_HISTORY", [
  "destinationId",
  "sourceTable",
  "queryDate",
  "invalidDate",
  "destinationTable"
])
export class MatchingHistory {
  @ApiProperty({ type: "integer", minimum: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ enum: MatchingRuleSourceDatabaseTable })
  @Column({ type: "enum", enum: MatchingRuleSourceDatabaseTable })
  sourceTable: MatchingRuleSourceDatabaseTable;

  @ApiProperty({ type: "integer", minimum: 1, isArray: true })
  @Column({ type: "integer", array: true })
  sourceIds: Array<number>;

  @ApiProperty({ enum: MatchingRuleDestinationDatabaseTable })
  @Column({ type: "enum", enum: MatchingRuleDestinationDatabaseTable })
  destinationTable: MatchingRuleDestinationDatabaseTable;

  @ApiProperty({ type: "integer", minimum: 1 })
  @Column({ type: "integer" })
  destinationId: number;

  @ApiProperty({ type: "string", format: "date-time" })
  @CreateDateColumn({ type: "timestamp with time zone" })
  queryDate: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ nullable: true, type: "timestamp with time zone" })
  invalidDate: Date | null;
}
