import { ApiProperty, ApiPropertyOptional, getSchemaPath } from "@nestjs/swagger";
import assert from "assert";
import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  Column,
  Entity,
  Index,
  ManyToOne,
  OneToMany,
  Unique
} from "typeorm";
import { MatchingConditionOperator } from "../types/matching-condition.types";
import {
  MatchingRuleDestinationDatabaseTable,
  MatchingRuleOperator,
  MatchingRuleSourceDatabaseTable,
  MatchingRuleStatus
} from "../types/matching-rule.types";
import { SimplifiedBaseEntity } from "./base.entity";
import { CanadaAntiDumping } from "./canada-anti-dumping.entity";
import { CanadaGstExemptCode } from "./canada-gst-exempt-code.entity";
import { CanadaOgd } from "./canada-ogd.entity";
import { CanadaSimaCode } from "./canada-sima-code.entity";
import { CanadaTariff } from "./canada-tariff.entity";
import { CanadaTreatmentCode } from "./canada-treatment-code.entity";
import { CanadaVfdCode } from "./canada-vfd-code.entity";
import { MatchingCondition } from "./matching-condition.entity";
import { OgdFiling } from "./ogd-filing.entity";
import { SimaFiling } from "./sima-filing.entity";
import { SimplifiedUser, User } from "./user.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";

export class SimplifiedMatchingRule extends SimplifiedBaseEntity {
  @ApiProperty({
    enum: MatchingRuleStatus,
    default: MatchingRuleStatus.PENDING
  })
  @Column({
    type: "enum",
    enum: MatchingRuleStatus,
    default: MatchingRuleStatus.PENDING
  })
  status: MatchingRuleStatus;

  @ApiProperty({
    type: "integer",
    minimum: 0,
    maximum: 10,
    default: 1,
    description: "Larger number means higher priority"
  })
  @Column({ type: "integer", default: 1 })
  priority: number;

  @ApiProperty({ minLength: 1 })
  @Column()
  name: string;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  description: string | null;

  @ApiProperty({ enum: MatchingRuleSourceDatabaseTable })
  @Column({ type: "enum", enum: MatchingRuleSourceDatabaseTable })
  sourceTable: MatchingRuleSourceDatabaseTable;

  @ApiProperty({ type: "integer", minimum: 1 })
  @Column({ type: "integer" })
  sourceId: number;

  @ApiProperty({ enum: MatchingRuleDestinationDatabaseTable })
  @Column({ type: "enum", enum: MatchingRuleDestinationDatabaseTable })
  destinationTable: MatchingRuleDestinationDatabaseTable;

  @ApiProperty({
    enum: MatchingRuleOperator,
    default: MatchingRuleOperator.AND,
    description: "Operator used for chaining conditions within a rule"
  })
  @Column({ type: "enum", enum: MatchingRuleOperator, default: MatchingRuleOperator.AND })
  operator: MatchingRuleOperator;

  @ApiPropertyOptional({ type: "string", format: "date" })
  @Column({
    type: "date",
    nullable: true
  })
  expiryDate: string | null;

  // Compute fields
  @ApiPropertyOptional({ type: "boolean" })
  isGlobal?: boolean;

  @ApiPropertyOptional({
    oneOf: [
      { $ref: getSchemaPath(CanadaAntiDumping) },
      { $ref: getSchemaPath(CanadaOgd) },
      { $ref: getSchemaPath(CanadaSimaCode) },
      { $ref: getSchemaPath(CanadaTariff) },
      { $ref: getSchemaPath(CanadaTreatmentCode) },
      { $ref: getSchemaPath(CanadaVfdCode) },
      { $ref: getSchemaPath(CanadaGstExemptCode) },
      { $ref: getSchemaPath(OgdFiling) }
    ]
  })
  sourceRecord?:
    | CanadaAntiDumping
    | CanadaOgd
    | CanadaSimaCode
    | CanadaTariff
    | CanadaTreatmentCode
    | CanadaVfdCode
    | CanadaGstExemptCode
    | OgdFiling
    | SimaFiling
    | null;
}

@Entity()
@Unique("UNIQUE_MATCHING_RULE_NAME_ORGANIZATION", ["name", "organization"])
@Index("IDX_MATCHING_RULE_DESTINATION", ["destinationTable"])
@Index("IDX_MATCHING_RULE_SOURCE", ["sourceTable", "sourceId"])
export class MatchingRule extends SimplifiedMatchingRule {
  @ApiPropertyOptional({ type: () => SimplifiedOrganization })
  @ManyToOne((type) => Organization, (org) => org.matchingRules, {
    nullable: true,
    onDelete: "SET NULL"
  })
  organization: Organization | null;

  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdMatchingRules, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedMatchingRules, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  @ApiProperty({ type: () => [MatchingCondition] })
  @OneToMany((type) => MatchingCondition, (condition) => condition.rule)
  conditions: MatchingCondition[];

  @AfterLoad()
  @AfterInsert()
  @AfterUpdate()
  compute() {
    if (this.organization !== undefined) this.isGlobal = this.organization === null;
  }
}
