import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn } from "typeorm";
import { SyncStatus } from "../types";

@Entity()
export class TariffSyncHistory {
  @ApiProperty({ type: "integer", minimum: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ enum: SyncStatus, default: SyncStatus.RUNNING })
  @Column({ type: "enum", enum: SyncStatus, default: SyncStatus.RUNNING })
  status: SyncStatus;

  @ApiProperty({ type: "string", format: "date-time" })
  @CreateDateColumn({ type: "timestamp with time zone" })
  syncDate: Date;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  finishDate: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ type: "text", nullable: true })
  errorMessage: string | null;
}
