import { ApiProperty } from "@nestjs/swagger";
import { Entity, Index, Unique, Column } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";

@Entity("us_tariff_pga_requirement")
@Index("IDX_US_TARIFF_PGA_REQUIREMENT_HSCODE", ["hsCode"])
@Index("IDX_US_TARIFF_PGA_REQUIREMENT_TARIFF_FLAG_CODE", ["tariffFlagCode"])
@Unique("UQ_US_TARIFF_PGA_BUSINESS_KEY", ["hsCode", "tariffFlagCode"])
export class UsTariffPgaRequirement extends SimplifiedBaseEntity {
  @ApiProperty({ type: "string", minLength: 8, maxLength: 10 })
  @Column({ length: 10 })
  hsCode: string;

  @ApiProperty({ type: "string", minLength: 3, maxLength: 3 })
  @Column({ length: 3 })
  tariffFlagCode: string;
}
