import { ApiProperty } from "@nestjs/swagger";
import { CreateDateColumn, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

export class SimplifiedBaseEntity {
  @ApiProperty({ type: "integer", minimum: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ type: "string", format: "date-time" })
  @CreateDateColumn({ type: "timestamp with time zone" })
  createDate: Date;

  @ApiProperty({ type: "string", format: "date-time" })
  @UpdateDateColumn({ type: "timestamp with time zone" })
  lastEditDate: Date;
}
