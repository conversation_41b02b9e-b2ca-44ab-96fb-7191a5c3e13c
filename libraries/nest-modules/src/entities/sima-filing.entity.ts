import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, ManyToOne } from "typeorm";
import { SimaIncoterms, SimaSubjectCode } from "../types";
import { SimplifiedBaseEntity } from "./base.entity";
import { CanadaAntiDumping, SimplifiedCanadaAntiDumping } from "./canada-anti-dumping.entity";
import { CanadaSimaCode, SimplifiedCanadaSimaCode } from "./canada-sima-code.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedSimaFiling extends SimplifiedBaseEntity {
  @ApiProperty({
    enum: SimaSubjectCode,
    description: "N-Non-subject, S-Subject, U-Undertaking"
  })
  @Column({ type: "enum", enum: SimaSubjectCode, nullable: false })
  subjectCode: SimaSubjectCode;

  @ApiPropertyOptional({
    enum: SimaIncoterms,
    description: "If SIMA is not subjected, this field is set to null"
  })
  @Column({ type: "enum", enum: SimaIncoterms, nullable: true })
  incoterms: SimaIncoterms | null;

  @ApiProperty({
    type: "boolean",
    default: false,
    description: "If SIMA is not subjected, this field is set to false"
  })
  @Column({ type: "boolean", default: false })
  security: boolean;
}

@Entity()
export class SimaFiling extends SimplifiedSimaFiling {
  @ApiProperty({ type: () => SimplifiedCanadaAntiDumping })
  @ManyToOne((type) => CanadaAntiDumping, (antiDumping) => antiDumping.simaFilings, {
    nullable: false,
    onDelete: "RESTRICT"
  })
  measureInForce: CanadaAntiDumping;

  @ApiPropertyOptional({
    type: () => SimplifiedCanadaSimaCode,
    description: "Required if SIMA is subjected or undertaking. Set to null if SIMA is not subjected"
  })
  @ManyToOne((type) => CanadaSimaCode, (simaCode) => simaCode.simaFilings, {
    nullable: true,
    onDelete: "SET NULL"
  })
  simaCode: CanadaSimaCode | null;

  @ApiProperty({ type: () => SimplifiedOrganization })
  @ManyToOne((type) => Organization, (organization) => organization.simaFilings, {
    nullable: false,
    onDelete: "CASCADE"
  })
  organization: Organization;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdSimaFilings, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedSimaFilings, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;
}
