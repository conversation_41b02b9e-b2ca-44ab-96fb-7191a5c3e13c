import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, JoinTable, ManyToMany, ManyToOne } from "typeorm";
import { EmailIntentType, EmailUpdateStatus } from "../types/email.types";
import { SimplifiedBaseEntity } from "./base.entity";
import { Document, SimplifiedDocument } from "./document.entity";
import { Email, SimplifiedEmail } from "./email.entity";

export class SimplifiedEmailUpdate extends SimplifiedBaseEntity {
  @ApiProperty({ enum: EmailUpdateStatus, default: EmailUpdateStatus.PENDING })
  @Column({
    type: "enum",
    enum: EmailUpdateStatus,
    default: EmailUpdateStatus.PENDING
  })
  status: EmailUpdateStatus;

  @ApiProperty({
    enum: EmailIntentType,
    description: "Type of intent that the update is related to"
  })
  @Column({ type: "enum", enum: EmailIntentType, nullable: false })
  intent: EmailIntentType;

  @ApiPropertyOptional({
    type: "object",
    description: "JSON containing the updates to apply"
  })
  @Column({ type: "jsonb", nullable: true })
  updateJson: Record<string, any> | null;

  @ApiPropertyOptional({
    type: "string",
    description: "Failed reasons of the update"
  })
  @Column({ type: "varchar", nullable: true })
  failedReasons: string | null;
}

@Entity()
export class EmailUpdate extends SimplifiedEmailUpdate {
  @ApiProperty({
    type: () => SimplifiedEmail,
    description: "Email that the update is related to"
  })
  @ManyToOne((type) => Email, (email) => email.updates, {
    nullable: false,
    onDelete: "CASCADE"
  })
  email: Email;

  @ApiPropertyOptional({
    type: () => SimplifiedDocument,
    description: "Documents to be used in the update"
  })
  @JoinTable({ name: "email_update_documents" })
  @ManyToMany((type) => Document, { nullable: true })
  documents: Array<Document> | null;
}
