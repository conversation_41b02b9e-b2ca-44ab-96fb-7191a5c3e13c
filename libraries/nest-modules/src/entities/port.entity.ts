import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity, Index, ManyToOne, OneToMany } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { CanadaSubLocation } from "./canada-sub-location.entity";
import { Location, SimplifiedLocation } from "./location.entity";

export class SimplifiedPort extends SimplifiedBaseEntity {
  @ApiProperty({ minLength: 1 })
  @Column()
  name: string;

  @ApiProperty({ minLength: 1 })
  @Index({ unique: true })
  @Column()
  code: string;

  @ApiProperty({ minLength: 1 })
  @Index({ unique: true })
  @Column({ nullable: true })
  iataCode: string;

  @ApiProperty({ minLength: 1 })
  @Column()
  state: string;

  @ApiProperty({ minLength: 1 })
  @Column()
  city: string;

  @ApiProperty({ minLength: 1 })
  @Column()
  road: boolean;

  @ApiProperty({ minLength: 1 })
  @Column()
  air: boolean;

  @ApiProperty({ minLength: 1 })
  @Column()
  rail: boolean;

  @ApiProperty({ minLength: 1 })
  @Column()
  marine: boolean;

  @ApiProperty({ minLength: 1 })
  @Column()
  inland: boolean;
}

@Entity()
export class Port extends SimplifiedPort {
  @ApiProperty({ type: () => SimplifiedLocation })
  @ManyToOne(() => Location, (location) => location.ports)
  location: Location;

  @ApiProperty({ type: () => CanadaSubLocation })
  @OneToMany(() => CanadaSubLocation, (subLocation) => subLocation.port)
  subLocations: CanadaSubLocation[];
}
