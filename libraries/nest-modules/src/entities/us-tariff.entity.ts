import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  Check,
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  Unique
} from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { SimplifiedUser, User } from "./user.entity";
import { UsTariffPgaRequirement } from "./us-tariff-pga-requirement.entity";

export class SimplifiedUsTariff extends SimplifiedBaseEntity {
  @ApiProperty({ type: "string", minLength: 2, maxLength: 10 })
  @Column()
  hsCode: string;

  @ApiProperty({ type: "string", format: "date" })
  @Column({ type: "date" })
  effectiveDate: Date;

  @ApiProperty({ type: "string", format: "date" })
  @Column({ type: "date" })
  expiryDate: Date;

  @ApiProperty()
  @Column({ type: "text" })
  description: string;

  @ApiPropertyOptional({
    description: "Census quantity 1 for statistical reporting"
  })
  @Column({ nullable: true })
  censusQty1: string | null;

  @ApiPropertyOptional({
    description: "Census quantity 2 for statistical reporting"
  })
  @Column({ nullable: true })
  censusQty2: string | null;

  @ApiPropertyOptional({
    description: "Census quantity 3 for statistical reporting"
  })
  @Column({ nullable: true })
  censusQty3: string | null;

  @ApiPropertyOptional({
    description: "Anti-dumping duty applicable"
  })
  @Column({ type: "boolean", nullable: true })
  adDuty: boolean | null;

  @ApiPropertyOptional({
    description: "Countervailing duty applicable"
  })
  @Column({ type: "boolean", nullable: true })
  cvDuty: boolean | null;

  @ApiPropertyOptional({
    type: [String],
    description: "Partner Government Agency codes"
  })
  @Column({ type: "text", array: true, nullable: true })
  pgaCodes: string[] | null;

  // Computed Fields
  @ApiProperty({ type: "string", example: "0000.00.00.00" })
  formattedHsCode: string;

  @ApiProperty({ type: "string", minLength: 1 })
  displayName: string;

  @ApiPropertyOptional({
    description: "Unit of measure for commercial invoice aggregation"
  })
  uom: string | null;

  @AfterLoad()
  @AfterInsert()
  @AfterUpdate()
  compute() {
    // Format HS code with dots for better readability
    // Support both 8-digit and 10-digit codes
    const hsCodeFirstPart = this.hsCode.substring(0, 4);
    const hsCodeSecondPart = this.hsCode.substring(4, 6);
    const hsCodeThirdPart = this.hsCode.substring(6, 8);
    const hsCodeFourthPart = this.hsCode.substring(8, 10);

    if (this.hsCode.length === 8) {
      // 8-digit format: 1234.56.78
      this.formattedHsCode = [hsCodeFirstPart, hsCodeSecondPart, hsCodeThirdPart]
        .filter((s) => s.length > 0)
        .join(".");
    } else {
      // 10-digit format: 1234.56.78.90
      this.formattedHsCode = [hsCodeFirstPart, hsCodeSecondPart, hsCodeThirdPart, hsCodeFourthPart]
        .filter((s) => s.length > 0)
        .join(".");
    }

    this.displayName = `${this.hsCode} - ${this.description}`;

    // Find primary UOM from census quantities (first valid value - NO means NUMBER)
    this.uom =
      [this.censusQty1, this.censusQty2, this.censusQty3].find((qty) => qty !== null && qty !== undefined) ||
      null;
  }

  /**
   * Get all census quantities as array (NO means NUMBER - a valid unit)
   */
  getCensusQuantities(): string[] {
    return [this.censusQty1, this.censusQty2, this.censusQty3].filter(
      (qty): qty is string => qty !== null && qty !== undefined
    );
  }

  /**
   * Check if this tariff requires census quantity reporting
   */
  requiresCensusReporting(): boolean {
    return this.getCensusQuantities().length > 0;
  }
}

@Entity("us_tariff")
@Check("VALID_HTS_CODE", 'char_length("hsCode") BETWEEN 2 AND 10')
@Unique("UNIQUE_US_HTS_CODE", ["hsCode"])
export class UsTariff extends SimplifiedUsTariff {
  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdUsTariffs, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedUsTariffs, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  // Note: No direct TypeORM relationship since UsTariffPgaRequirement uses hsCode strings
  // Use UsTariffService.getPgaRequirements(hsCode) to get related PGA requirements
  // pgaRequirements: Promise<Array<UsTariffPgaRequirement>>;
}
