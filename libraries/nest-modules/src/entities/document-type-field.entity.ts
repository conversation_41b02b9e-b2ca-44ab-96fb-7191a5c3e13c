import { ApiProperty } from "@nestjs/swagger";
import { Colum<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, Unique } from "typeorm";
import { DocumentFieldDataType } from "../types";
import { SimplifiedBaseEntity } from "./base.entity";
import { DocumentType, SimplifiedDocumentType } from "./document-type.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedDocumentTypeField extends SimplifiedBaseEntity {
  @ApiProperty({ description: "Name of the document type field" })
  @Column()
  name: string;

  @ApiProperty({ enum: DocumentFieldDataType })
  @Column({ type: "enum", enum: DocumentFieldDataType })
  dataType: DocumentFieldDataType;

  @ApiProperty({ description: "Whether the field is mandatory" })
  @Column()
  isMandatory: boolean;

  @ApiProperty({ description: "Description of the field" })
  @Column({ default: "" })
  description: string;
}

@Entity()
@Unique("UNIQUE_DOCUMENT_TYPE_FIELD_NAME", ["name", "documentType"])
export class DocumentTypeField extends SimplifiedDocumentTypeField {
  @ApiProperty({ type: () => SimplifiedDocumentType })
  @ManyToOne(() => DocumentType, (documentType) => documentType.fields)
  @JoinColumn({ name: "documentTypeId" })
  documentType: DocumentType;

  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne(() => User, (user) => user.createdDocumentTypeFields, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne(() => User, (user) => user.lastEditedDocumentTypeFields, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;
}
