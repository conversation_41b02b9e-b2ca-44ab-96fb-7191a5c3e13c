import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  Relation,
  Unique
} from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { CommercialInvoiceLine } from "./commercial-invoice-line.entity";
import { CommercialInvoice } from "./commercial-invoice.entity";
import { DocumentAggregation } from "./document-aggregation.entity";
import { Document } from "./document.entity";
import { Email } from "./email.entity";
import { File } from "./file.entity";
import { Importer } from "./importer.entity";
import { OgdFiling } from "./ogd-filing.entity";
import { Product } from "./product.entity";
import { Shipment } from "./shipment.entity";
import { SimaFiling } from "./sima-filing.entity";
import { TrackingHistory } from "./tracking-history.entity";
import { TradePartner } from "./trade-partner.entity";
import { SimplifiedUser, User } from "./user.entity";
import { OrganizationCustomsBroker, OrganizationType } from "../types/organization.types";
import { MatchingRule } from "./matching-rule.entity";

export class SimplifiedOrganization extends SimplifiedBaseEntity {
  @ApiProperty({ minLength: 1 })
  @Column()
  name: string;

  @ApiProperty({ enum: OrganizationType, default: OrganizationType.TRADING })
  @Column({ type: "enum", enum: OrganizationType, default: OrganizationType.TRADING })
  organizationType: OrganizationType;

  @ApiProperty({ enum: OrganizationCustomsBroker, default: OrganizationCustomsBroker.CLARO })
  @Column({ type: "enum", enum: OrganizationCustomsBroker, default: OrganizationCustomsBroker.CLARO })
  customsBroker: OrganizationCustomsBroker;

  @ApiProperty({ type: "boolean", default: false })
  @Column({ type: "boolean", default: false })
  skipPoaCheck: boolean;

  // Compute fields
  @ApiProperty({ type: "string", minLength: 1 })
  displayName: string;

  @AfterInsert()
  @AfterLoad()
  @AfterUpdate()
  compute() {
    this.displayName = this.name ?? this.id.toString();
  }
}

@Entity()
@Unique("UNIQUE_ORG_NAME", ["name"])
export class Organization extends SimplifiedOrganization {
  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdOrganizations, {
    onDelete: "SET NULL",
    nullable: true
  })
  createdBy: Relation<User> | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedOrganizations, {
    onDelete: "SET NULL",
    nullable: true
  })
  lastEditedBy: Relation<User> | null;

  // Hidden fields
  @OneToMany((type) => TrackingHistory, (trackingHistory) => trackingHistory.organization)
  trackingHistories: Promise<Array<TrackingHistory>>;

  @OneToMany((type) => CommercialInvoiceLine, (invoiceLine) => invoiceLine.organization)
  commercialInvoiceLines: Promise<Array<CommercialInvoiceLine>>;

  @OneToMany((type) => CommercialInvoice, (invoice) => invoice.organization)
  commercialInvoices: Promise<Array<CommercialInvoice>>;

  @OneToMany((type) => User, (user) => user.organization)
  users: Promise<Array<User>>;

  @OneToMany((type) => TradePartner, (partner) => partner.organization)
  tradePartners: Promise<Array<TradePartner>>;

  @OneToMany((type) => Importer, (importer) => importer.organization)
  importers: Promise<Array<Importer>>;

  @OneToMany((type) => Shipment, (shipment) => shipment.organization)
  shipments: Promise<Array<Shipment>>;

  @OneToMany((type) => Product, (product) => product.organization)
  products: Promise<Array<Product>>;

  @OneToMany((type) => File, (file) => file.organization)
  files: Promise<Array<File>>;

  @OneToMany((type) => OgdFiling, (filing) => filing.organization)
  ogdFilings: Promise<Array<OgdFiling>>;

  @OneToMany((type) => Document, (document) => document.organization)
  documents: Promise<Array<Document>>;

  @OneToMany((type) => DocumentAggregation, (aggregation) => aggregation.organization)
  documentAggregations: Promise<Array<DocumentAggregation>>;

  @OneToMany((type) => SimaFiling, (filing) => filing.organization)
  simaFilings: Promise<Array<SimaFiling>>;

  @OneToMany((type) => Email, (email) => email.organization)
  emails: Promise<Array<Email>>;

  @OneToMany((type) => MatchingRule, (rule) => rule.organization)
  matchingRules: Promise<Array<MatchingRule>>;
}
