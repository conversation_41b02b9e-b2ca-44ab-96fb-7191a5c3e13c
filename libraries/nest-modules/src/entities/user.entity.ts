import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, ManyToOne, OneToMany, Relation, Unique } from "typeorm";
import { UserPermission } from "../types/user.types";
import { SimplifiedBaseEntity } from "./base.entity";
import { CanadaAntiDumping } from "./canada-anti-dumping.entity";
import { CanadaExciseTaxCode } from "./canada-excise-tax-code.entity";
import { CanadaGstExemptCode } from "./canada-gst-exempt-code.entity";
import { CanadaOgd } from "./canada-ogd.entity";
import { CanadaSimaCode } from "./canada-sima-code.entity";
import { CanadaTariff } from "./canada-tariff.entity";
import { CanadaTreatmentCode } from "./canada-treatment-code.entity";
import { CanadaVfdCode } from "./canada-vfd-code.entity";
import { CommercialInvoiceLine } from "./commercial-invoice-line.entity";
import { CommercialInvoice } from "./commercial-invoice.entity";
import { Country } from "./country.entity";
import { DocumentAggregation } from "./document-aggregation.entity";
import { DocumentField } from "./document-field.entity";
import { DocumentTypeField } from "./document-type-field.entity";
import { DocumentType } from "./document-type.entity";
import { Document } from "./document.entity";
import { DocusignToken } from "./docusign-token.entity";
import { Email } from "./email.entity";
import { File } from "./file.entity";
import { Importer } from "./importer.entity";
import { Location } from "./location.entity";
import { MatchingCondition } from "./matching-condition.entity";
import { MatchingRule } from "./matching-rule.entity";
import { OgdFiling } from "./ogd-filing.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { Product } from "./product.entity";
import { RefreshToken } from "./refresh-token.entity";
import { Shipment } from "./shipment.entity";
import { SimaFiling } from "./sima-filing.entity";
import { TradePartner } from "./trade-partner.entity";
import { UsTariff } from "./us-tariff.entity";

export class SimplifiedUser extends SimplifiedBaseEntity {
  @ApiProperty({ type: "string", format: "email" })
  @Column()
  email: string;

  @ApiProperty()
  @Column()
  name: string;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  googleUserId: string | null;

  @ApiProperty({ enum: UserPermission, default: UserPermission.BASIC })
  @Column({ type: "enum", enum: UserPermission, default: UserPermission.BASIC })
  permission: UserPermission;
}

@Entity()
@Unique("UNIQUE_USER_ORGANIZATION_EMAIL", ["email", "organization"])
export class User extends SimplifiedUser {
  @ApiProperty({ type: () => SimplifiedOrganization })
  @ManyToOne("Organization", "users", {
    onDelete: "CASCADE",
    nullable: false
  })
  organization: Relation<Organization>;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdUsers, {
    onDelete: "SET NULL",
    nullable: true
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedUsers, {
    onDelete: "SET NULL",
    nullable: true
  })
  lastEditedBy: User | null;

  // Hidden fields
  @OneToMany((type) => CommercialInvoiceLine, (invoiceLine) => invoiceLine.createdBy)
  createdCommercialInvoiceLines: Promise<Array<CommercialInvoiceLine>>;

  @OneToMany((type) => CommercialInvoiceLine, (invoiceLine) => invoiceLine.lastEditedBy)
  lastEditedCommercialInvoiceLines: Promise<Array<CommercialInvoiceLine>>;

  @OneToMany((type) => CommercialInvoice, (invoice) => invoice.createdBy)
  createdCommercialInvoices: Promise<Array<CommercialInvoice>>;

  @OneToMany((type) => CommercialInvoice, (invoice) => invoice.lastEditedBy)
  lastEditedCommercialInvoices: Promise<Array<CommercialInvoice>>;

  @OneToMany((type) => User, (user) => user.createdBy)
  createdUsers: Promise<Array<User>>;

  @OneToMany((type) => User, (user) => user.lastEditedBy)
  lastEditedUsers: Promise<Array<User>>;

  @OneToMany((type) => Organization, (org) => org.createdBy)
  createdOrganizations: Promise<Array<Organization>>;

  @OneToMany((type) => Organization, (org) => org.lastEditedBy)
  lastEditedOrganizations: Promise<Array<Organization>>;

  @OneToMany((type) => RefreshToken, (token) => token.user)
  refreshTokens: Promise<Array<RefreshToken>>;

  @OneToMany((type) => Country, (country) => country.createdBy)
  createdCountries: Promise<Array<Country>>;

  @OneToMany((type) => Country, (country) => country.lastEditedBy)
  lastEditedCountries: Promise<Array<Country>>;

  @OneToMany((type) => Location, (location) => location.createdBy)
  createdLocations: Promise<Array<Location>>;

  @OneToMany((type) => Location, (location) => location.lastEditedBy)
  lastEditedLocations: Promise<Array<Location>>;

  @OneToMany((type) => TradePartner, (partner) => partner.createdBy)
  createdTradePartners: Promise<Array<TradePartner>>;

  @OneToMany((type) => TradePartner, (partner) => partner.lastEditedBy)
  lastEditedTradePartners: Promise<Array<TradePartner>>;

  @OneToMany((type) => Importer, (importer) => importer.createdBy)
  createdImporters: Promise<Array<Importer>>;

  @OneToMany((type) => Importer, (importer) => importer.lastEditedBy)
  lastEditedImporters: Promise<Array<Importer>>;

  @OneToMany((type) => Shipment, (shipment) => shipment.createdBy)
  createdShipments: Promise<Array<Shipment>>;

  @OneToMany((type) => Shipment, (shipment) => shipment.lastEditedBy)
  lastEditedShipments: Promise<Array<Shipment>>;

  @OneToMany((type) => DocusignToken, (token) => token.createdBy)
  createdDocusignTokens: Promise<Array<DocusignToken>>;

  @OneToMany((type) => DocusignToken, (token) => token.lastEditedBy)
  lastEditedDocusignTokens: Promise<Array<DocusignToken>>;

  @OneToMany((type) => CanadaTariff, (tariff) => tariff.createdBy)
  createdCanadaTariffs: Promise<Array<CanadaTariff>>;

  @OneToMany((type) => CanadaTariff, (tariff) => tariff.lastEditedBy)
  lastEditedCanadaTariffs: Promise<Array<CanadaTariff>>;

  @OneToMany((type) => UsTariff, (tariff) => tariff.createdBy)
  createdUsTariffs: Promise<Array<UsTariff>>;

  @OneToMany((type) => UsTariff, (tariff) => tariff.lastEditedBy)
  lastEditedUsTariffs: Promise<Array<UsTariff>>;

  // @OneToMany(type => CanadaTariffTreatment, treatment => treatment.createdBy)
  // createdCanadaTariffTreatments: Promise<Array<CanadaTariffTreatment>>;

  // @OneToMany(type => CanadaTariffTreatment, treatment => treatment.lastEditedBy)
  // lastEditedCanadaTariffTreatments: Promise<Array<CanadaTariffTreatment>>;

  @OneToMany((type) => Product, (product) => product.createdBy)
  createdProducts: Promise<Array<Product>>;

  @OneToMany((type) => Product, (product) => product.lastEditedBy)
  lastEditedProducts: Promise<Array<Product>>;

  @OneToMany((type) => CanadaOgd, (ogd) => ogd.createdBy)
  createdCanadaOgds: Promise<Array<CanadaOgd>>;

  @OneToMany((type) => CanadaOgd, (ogd) => ogd.lastEditedBy)
  lastEditedCanadaOgds: Promise<Array<CanadaOgd>>;

  @OneToMany((type) => CanadaTreatmentCode, (treatmentCode) => treatmentCode.createdBy)
  createdCanadaTreatmentCodes: Promise<Array<CanadaTreatmentCode>>;

  @OneToMany((type) => CanadaTreatmentCode, (treatmentCode) => treatmentCode.lastEditedBy)
  lastEditedCanadaTreatmentCodes: Promise<Array<CanadaTreatmentCode>>;

  @OneToMany((type) => CanadaGstExemptCode, (code) => code.createdBy)
  createdCanadaGstExemptCodes: Promise<Array<CanadaGstExemptCode>>;

  @OneToMany((type) => CanadaGstExemptCode, (code) => code.lastEditedBy)
  lastEditedCanadaGstExemptCodes: Promise<Array<CanadaGstExemptCode>>;

  @OneToMany((type) => CanadaSimaCode, (code) => code.createdBy)
  createdCanadaSimaCodes: Promise<Array<CanadaSimaCode>>;

  @OneToMany((type) => CanadaSimaCode, (code) => code.lastEditedBy)
  lastEditedCanadaSimaCodes: Promise<Array<CanadaSimaCode>>;

  @OneToMany((type) => CanadaVfdCode, (code) => code.createdBy)
  createdCanadaVfdCodes: Promise<Array<CanadaVfdCode>>;

  @OneToMany((type) => CanadaVfdCode, (code) => code.lastEditedBy)
  lastEditedCanadaVfdCodes: Promise<Array<CanadaVfdCode>>;

  @OneToMany((type) => CanadaAntiDumping, (antiDumping) => antiDumping.createdBy)
  createdCanadaAntiDumpings: Promise<Array<CanadaAntiDumping>>;

  @OneToMany((type) => CanadaAntiDumping, (antiDumping) => antiDumping.lastEditedBy)
  lastEditedCanadaAntiDumpings: Promise<Array<CanadaAntiDumping>>;

  @OneToMany((type) => File, (file) => file.createdBy)
  createdFiles: Promise<Array<File>>;

  @OneToMany((type) => File, (file) => file.lastEditedBy)
  lastEditedFiles: Promise<Array<File>>;

  @OneToMany((type) => Document, (document) => document.createdBy)
  createdDocuments: Promise<Array<Document>>;

  @OneToMany((type) => Document, (document) => document.lastEditedBy)
  lastEditedDocuments: Promise<Array<Document>>;

  @OneToMany((type) => DocumentField, (field) => field.createdBy)
  createdDocumentFields: Promise<Array<DocumentField>>;

  @OneToMany((type) => DocumentField, (field) => field.lastEditedBy)
  lastEditedDocumentFields: Promise<Array<DocumentField>>;

  @OneToMany((type) => DocumentType, (type) => type.createdBy)
  createdDocumentTypes: Promise<Array<DocumentType>>;

  @OneToMany((type) => DocumentType, (type) => type.lastEditedBy)
  lastEditedDocumentTypes: Promise<Array<DocumentType>>;

  @OneToMany((type) => DocumentTypeField, (field) => field.createdBy)
  createdDocumentTypeFields: Promise<Array<DocumentTypeField>>;

  @OneToMany((type) => DocumentTypeField, (field) => field.lastEditedBy)
  lastEditedDocumentTypeFields: Promise<Array<DocumentTypeField>>;

  @OneToMany((type) => MatchingRule, (rule) => rule.createdBy)
  createdMatchingRules: Promise<Array<MatchingRule>>;

  @OneToMany((type) => MatchingRule, (rule) => rule.lastEditedBy)
  lastEditedMatchingRules: Promise<Array<MatchingRule>>;

  @OneToMany((type) => MatchingCondition, (condition) => condition.createdBy)
  createdMatchingConditions: Promise<Array<MatchingCondition>>;

  @OneToMany((type) => MatchingCondition, (condition) => condition.lastEditedBy)
  lastEditedMatchingConditions: Promise<Array<MatchingCondition>>;

  @OneToMany((type) => OgdFiling, (filing) => filing.createdBy)
  createdOgdFilings: Promise<Array<OgdFiling>>;

  @OneToMany((type) => OgdFiling, (filing) => filing.lastEditedBy)
  lastEditedOgdFilings: Promise<Array<OgdFiling>>;

  @OneToMany((type) => CanadaExciseTaxCode, (code) => code.createdBy)
  createdCanadaExciseTaxCodes: Promise<Array<CanadaExciseTaxCode>>;

  @OneToMany((type) => CanadaExciseTaxCode, (code) => code.lastEditedBy)
  lastEditedCanadaExciseTaxCodes: Promise<Array<CanadaExciseTaxCode>>;

  @OneToMany((type) => DocumentAggregation, (aggregation) => aggregation.createdBy)
  createdDocumentAggregations: Promise<Array<DocumentAggregation>>;

  @OneToMany((type) => DocumentAggregation, (aggregation) => aggregation.lastEditedBy)
  lastEditedDocumentAggregations: Promise<Array<DocumentAggregation>>;

  @OneToMany((type) => SimaFiling, (filing) => filing.createdBy)
  createdSimaFilings: Promise<Array<SimaFiling>>;

  @OneToMany((type) => SimaFiling, (filing) => filing.lastEditedBy)
  lastEditedSimaFilings: Promise<Array<SimaFiling>>;

  @OneToMany((type) => Email, (email) => email.createdBy)
  createdEmails: Promise<Array<Email>>;

  @OneToMany((type) => Email, (email) => email.lastEditedBy)
  lastEditedEmails: Promise<Array<Email>>;
}
