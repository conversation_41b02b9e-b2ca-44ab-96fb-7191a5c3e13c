import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { AfterInsert, AfterLoad, AfterUpdate, Column, Entity, ManyToOne, OneToMany, Unique } from "typeorm";
import { PartnerType } from "../types/trade-partner.types";
import { SimplifiedBaseEntity } from "./base.entity";
import { CommercialInvoice } from "./commercial-invoice.entity";
import { Country, SimplifiedCountry } from "./country.entity";
import { OgdFiling } from "./ogd-filing.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { Product } from "./product.entity";
import { Shipment } from "./shipment.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedTradePartner extends SimplifiedBaseEntity {
  @ApiProperty({ enum: PartnerType })
  @Column({ type: "enum", enum: PartnerType })
  partnerType: PartnerType;

  @ApiPropertyOptional({
    type: "string",
    minLength: 1
  })
  @Column({ nullable: true })
  vendorCode: string | null;

  @ApiPropertyOptional({
    type: "string",
    minLength: 4,
    maxLength: 4,
    description: "CBSA-issued eManifest carrier code"
  })
  @Column({ nullable: true })
  eManifestCarrierCode: string | null;

  @ApiProperty()
  @Column()
  name: string;

  @ApiPropertyOptional({ type: "string", format: "email", minLength: 1 })
  @Column({ nullable: true })
  email: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  phoneNumber: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  address: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  city: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  state: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  postalCode: string | null;

  // Compute fields
  @ApiProperty({ type: "string", minLength: 1 })
  displayName: string;

  @AfterInsert()
  @AfterLoad()
  @AfterUpdate()
  compute() {
    this.displayName = this.name ?? this.id.toString();
  }
}

@Entity()
@Unique("UNIQUE_TRADE_PARTNER", ["name", "city", "state", "country", "organization"])
// TODO: Uncomment this check
// @Check('VALID_VENDOR', `"partnerType" != 'vendor' OR ("vendorCode" IS NOT NULL AND "vendorCode" != '')`)
export class TradePartner extends SimplifiedTradePartner {
  @ApiPropertyOptional({ type: () => SimplifiedCountry })
  @ManyToOne((type) => Country, (country) => country.tradePartners, {
    nullable: true,
    onDelete: "SET NULL"
  })
  country: Country | null;

  @ApiPropertyOptional({ type: () => SimplifiedOrganization })
  @ManyToOne((type) => Organization, (org) => org.tradePartners, {
    nullable: true,
    onDelete: "CASCADE"
  })
  organization: Organization | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdTradePartners, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedTradePartners, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  // Hidden fields
  @OneToMany((type) => CommercialInvoice, (invoice) => invoice.purchaser)
  purchaserCommercialInvoice: Promise<Array<CommercialInvoice>>;

  @OneToMany((type) => CommercialInvoice, (invoice) => invoice.exporter)
  exporterCommercialInvoice: Promise<Array<CommercialInvoice>>;

  @OneToMany((type) => CommercialInvoice, (invoice) => invoice.vendor)
  vendorCommercialInvoice: Promise<Array<CommercialInvoice>>;

  @OneToMany((type) => CommercialInvoice, (invoice) => invoice.manufacturer)
  manufacturerCommercialInvoice: Promise<Array<CommercialInvoice>>;

  @OneToMany((type) => CommercialInvoice, (invoice) => invoice.shipTo)
  shipToCommercialInvoice: Promise<Array<CommercialInvoice>>;

  @OneToMany((type) => Shipment, (shipment) => shipment.carrier)
  carrierShipments: Promise<Array<Shipment>>;

  @OneToMany((type) => Shipment, (shipment) => shipment.manufacturer)
  manufacturerShipments: Promise<Array<Shipment>>;

  @OneToMany((type) => Shipment, (shipment) => shipment.shipper)
  shipperShipments: Promise<Array<Shipment>>;

  @OneToMany((type) => Shipment, (shipment) => shipment.consignee)
  consigneeShipments: Promise<Array<Shipment>>;

  @OneToMany((type) => Shipment, (shipment) => shipment.forwarder)
  forwarderShipments: Promise<Array<Shipment>>;

  @OneToMany((type) => Shipment, (shipment) => shipment.trucker)
  truckerShipments: Promise<Array<Shipment>>;

  @OneToMany((type) => Shipment, (shipment) => shipment.pickupLocation)
  pickupLocationShipments: Promise<Array<Shipment>>;

  @OneToMany((type) => Product, (product) => product.vendor)
  vendorProducts: Promise<Array<Product>>;

  @OneToMany((type) => Product, (product) => product.manufacturer)
  manufacturerProducts: Promise<Array<Product>>;

  @OneToMany(() => OgdFiling, (filing) => filing.manufacturer)
  manufacturerOgdFilings: Promise<Array<OgdFiling>>;
}
