import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, Index, ManyToOne, Unique } from "typeorm";
import { ContainerType, ShipmentStatus, TrackingStatus } from "../types";
import { SimplifiedBaseEntity } from "./base.entity";
import { Shipment, SimplifiedShipment } from "./shipment.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedContainer extends SimplifiedBaseEntity {
  @ApiProperty({
    enum: ShipmentStatus,
    default: ShipmentStatus.NEW
  })
  @Column({ nullable: false, type: "enum", enum: ShipmentStatus, default: ShipmentStatus.NEW })
  status: ShipmentStatus;

  @ApiProperty({
    enum: TrackingStatus,
    default: TrackingStatus.OFFLINE
  })
  @Column({
    nullable: false,
    type: "enum",
    enum: TrackingStatus,
    default: TrackingStatus.OFFLINE
  })
  trackingStatus: TrackingStatus;

  @ApiProperty({ type: "string", minLength: 1, maxLength: 17 })
  @Column({ nullable: false, type: "varchar" })
  @Index("idx_container_number_trgm", { synchronize: false })
  containerNumber: string;

  @ApiPropertyOptional({ enum: ContainerType })
  @Column({ nullable: true, type: "enum", enum: ContainerType })
  containerType: ContainerType | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ nullable: true, type: "timestamp with time zone" })
  etaDestination: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  etaDestinationString: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  pickupLfd: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  pickupLfdString: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  pickupDate: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  pickupDateString: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  returnLfd: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  returnLfdString: string | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  returnDate: Date | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  returnDateString: string | null;
}

@Entity()
@Unique(["containerNumber", "shipment"])
export class Container extends SimplifiedContainer {
  @ApiProperty({ type: () => SimplifiedShipment })
  @ManyToOne((type) => Shipment, (shipment) => shipment.containers, {
    nullable: false,
    onDelete: "CASCADE"
  })
  shipment: Shipment;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;
}
