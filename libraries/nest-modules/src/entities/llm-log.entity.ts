import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, CreateDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

@Entity()
export class LlmLog {
  @ApiProperty({ type: "integer", minimum: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiPropertyOptional({ description: "The model used in the request" })
  @Column({ type: "text", nullable: true })
  model: string | null;

  @ApiProperty({ description: "The request sent to the LLM" })
  @Column("text")
  request: string;

  @ApiPropertyOptional({ description: "The response from the LLM" })
  @Column("text", { nullable: true })
  response: string | null;

  @ApiProperty({ description: "The caller of the LLM" })
  @Column("text", { nullable: true })
  caller: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @CreateDateColumn({ type: "timestamp with time zone" })
  createDate: Date;

  @ApiProperty({ type: "string", format: "date-time" })
  @UpdateDateColumn({ type: "timestamp with time zone" })
  completeDate: Date;
}
