import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  Check,
  Column,
  Entity,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  Unique
} from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { CommercialInvoiceLine } from "./commercial-invoice-line.entity";
import { Country, SimplifiedCountry } from "./country.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedCanadaTreatmentCode extends SimplifiedBaseEntity {
  @ApiProperty({ type: "integer", minimum: 1 })
  @Column({ type: "integer" })
  code: number;

  @ApiProperty({ minLength: 1 })
  @Column()
  name: string;

  @ApiProperty({ minLength: 1 })
  @Column()
  abbreviation: string;

  // Compute fields
  @ApiProperty({ type: "string", minLength: 1 })
  displayName: string;

  @AfterInsert()
  @AfterLoad()
  @AfterUpdate()
  compute() {
    this.displayName = `${this.code} - ${this.name}`;
  }
}

@Entity()
@Check("VALID_CANADA_TREATMENT_CODE", `"code" >= 1`)
@Unique("UNIQUE_CANADA_TREATMENT_CODE", ["code"])
@Unique("UNIQUE_CANADA_TREATMENT_CODE_NAME", ["name"])
@Unique("UNIQUE_CANADA_TREATMENT_CODE_ABBREV", ["abbreviation"])
export class CanadaTreatmentCode extends SimplifiedCanadaTreatmentCode {
  @ApiProperty({ type: () => [SimplifiedCountry] })
  @ManyToMany((type) => Country, (country) => country.canadaTreatmentCodes)
  @JoinTable({ name: "canada_treatment_code_countries" })
  countries: Array<Country>;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdCanadaTreatmentCodes, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedCanadaTreatmentCodes, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  // hidden fields
  @OneToMany((type) => CommercialInvoiceLine, (invoiceLine) => invoiceLine.tt)
  commercialInvoiceLines: Array<CommercialInvoiceLine>;
}
