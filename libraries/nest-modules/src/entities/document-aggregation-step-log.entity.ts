import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { DocumentAggregationStep } from "./document-aggregation-step.entity";

export class SimplifiedDocumentAggregationStepLog extends SimplifiedBaseEntity {
  @Column({ type: "text" })
  message: string;

  @Column({ type: "text" })
  field: string;

  @Column({ type: "text" })
  level: "info" | "error" | "warning";
}

@Entity()
export class DocumentAggregationStepLog extends SimplifiedDocumentAggregationStepLog {
  @ManyToOne(() => DocumentAggregationStep, (step) => step.logs, {
    nullable: false,
    onDelete: "CASCADE"
  })
  @JoinColumn({ name: "documentAggregationStepId" })
  step: DocumentAggregationStep;
}
