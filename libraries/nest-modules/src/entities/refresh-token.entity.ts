import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity, ManyToOne, PrimaryColumn, Relation } from "typeorm";
import { SimplifiedUser, User } from "./user.entity";

@Entity()
export class RefreshToken {
  @ApiProperty({ type: "string", format: "jwt" })
  @PrimaryColumn()
  refreshToken: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone" })
  expiryDate: Date;

  @ApiProperty({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone" })
  issueDate: Date;

  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.refreshTokens, {
    onDelete: "CASCADE",
    nullable: false
  })
  user: Relation<User>;
}
