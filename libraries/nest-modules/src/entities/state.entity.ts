import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, ManyToOne, Unique } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { Country } from "./country.entity";
import { SimplifiedUser } from "./user.entity";

export class SimplifiedState extends SimplifiedBaseEntity {
  @ApiProperty()
  @Column()
  name: string;

  @ApiProperty({ type: "string", minLength: 1 })
  @Column()
  // TODO: alpha2 should not be nullable
  alpha2: string;

  @ApiProperty({ type: "number" })
  @Column()
  countryId: number;
}

@Entity()
@Unique("UNIQUE_STATE_NAME", ["name", "country"])
@Unique("UNIQUE_STATE_ALPHA2", ["alpha2", "country"])
export class State extends SimplifiedState {
  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne(() => Country, (country) => country.states)
  country: Country;
}
