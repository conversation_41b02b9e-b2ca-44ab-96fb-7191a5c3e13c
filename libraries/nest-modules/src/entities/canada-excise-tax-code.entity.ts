import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { AfterInsert, AfterLoad, AfterUpdate, Column, Entity, ManyToOne, OneToMany, Unique } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { CommercialInvoiceLine } from "./commercial-invoice-line.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedCanadaExciseTaxCode extends SimplifiedBaseEntity {
  @ApiProperty({ minLength: 1 })
  @Column()
  category: string;

  @ApiProperty({ minLength: 3, maxLength: 3, pattern: "^[A-Z]\d{2}$" })
  @Column()
  code: string;

  @ApiProperty({ minLength: 1 })
  @Column()
  explanation: string;

  @ApiPropertyOptional({ minLength: 1 })
  @Column({ nullable: true })
  rateType: string | null;

  // Compute fields
  @ApiProperty({ type: "string", minLength: 1 })
  displayName: string;

  @AfterInsert()
  @AfterLoad()
  @AfterUpdate()
  compute() {
    this.displayName = `${this.code} - ${this.explanation}`;
  }
}

@Entity()
@Unique("UNIQUE_CANADA_EXCISE_TAX_CODE", ["code"])
export class CanadaExciseTaxCode extends SimplifiedCanadaExciseTaxCode {
  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdCanadaExciseTaxCodes, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedCanadaExciseTaxCodes, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  // Hidden fields
  @OneToMany((type) => CommercialInvoiceLine, (line) => line.exciseTaxCode)
  commercialInvoiceLines: Promise<Array<CommercialInvoiceLine>>;
}
