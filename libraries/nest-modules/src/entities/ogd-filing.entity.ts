import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, ManyToOne } from "typeorm";
import {
  ECCCEmissionProgram,
  ECCCSubType,
  ECCCWildlifeCompliance,
  GeneralImportPermit,
  NRCANSubType,
  OgdFilingType,
  TCTireClass,
  TCTireCompliance,
  TCTireSize,
  TCTireType
} from "../types";
import { SimplifiedBaseEntity } from "./base.entity";
import { CanadaOgd, SimplifiedCanadaOgd } from "./canada-ogd.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { SimplifiedTradePartner, TradePartner } from "./trade-partner.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedOgdFiling extends SimplifiedBaseEntity {
  /** Common fields */
  @ApiProperty({ type: "boolean", default: false })
  @Column({ type: "boolean", default: false })
  isExcluded: boolean;

  @ApiProperty({ type: "string", enum: OgdFilingType })
  @Column({ type: "enum", enum: OgdFilingType, default: OgdFilingType.MANUAL })
  type: OgdFilingType;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  endUse: string | null;

  /** CFIA fields */
  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  extensionCode: string | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  airsType: string | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  airsReferenceNumber: string | null;

  /** HC fields */
  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  productCategory: string | null;

  /** GAC fields */
  @ApiPropertyOptional({ enum: GeneralImportPermit })
  @Column({ nullable: true, type: "enum", enum: GeneralImportPermit })
  generalImportPermit: GeneralImportPermit | null;

  /** TC fields */
  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  brandName: string | null;

  @ApiPropertyOptional({ enum: TCTireType })
  @Column({ nullable: true, type: "enum", enum: TCTireType })
  tireType: TCTireType | null;

  @ApiPropertyOptional({ enum: TCTireSize })
  @Column({ nullable: true, type: "enum", enum: TCTireSize })
  tireSize: TCTireSize | null;

  @ApiPropertyOptional({ enum: TCTireCompliance })
  @Column({ nullable: true, type: "enum", enum: TCTireCompliance })
  tireCompliance: TCTireCompliance | null;

  @ApiPropertyOptional({ enum: TCTireClass })
  @Column({ nullable: true, type: "enum", enum: TCTireClass })
  tireClass: TCTireClass | null;

  /** ECCC fields */
  @ApiPropertyOptional({ enum: ECCCSubType })
  @Column({ nullable: true, type: "enum", enum: ECCCSubType })
  ecccSubType: ECCCSubType | null;

  @ApiPropertyOptional({ enum: ECCCWildlifeCompliance })
  @Column({ nullable: true, type: "enum", enum: ECCCWildlifeCompliance })
  wildlifeCompliance: ECCCWildlifeCompliance | null;

  @ApiPropertyOptional({ enum: ECCCEmissionProgram })
  @Column({ nullable: true, type: "enum", enum: ECCCEmissionProgram })
  emissionProgram: ECCCEmissionProgram | null;

  /** NRCAN fields */
  @ApiPropertyOptional({ enum: NRCANSubType })
  @Column({ nullable: true, type: "enum", enum: NRCANSubType })
  nrcanSubType: NRCANSubType | null;

  @ApiPropertyOptional({ type: "boolean" })
  @Column({ nullable: true, type: "boolean" })
  isRegulated: boolean | null;
}

@Entity()
export class OgdFiling extends SimplifiedOgdFiling {
  @ApiProperty({ type: () => SimplifiedCanadaOgd })
  @ManyToOne((type) => CanadaOgd, (ogd) => ogd.filings, {
    nullable: false,
    onDelete: "RESTRICT"
  })
  ogd: CanadaOgd;

  @ApiProperty({ type: () => SimplifiedOrganization })
  @ManyToOne((type) => Organization, (organization) => organization.ogdFilings, {
    nullable: true,
    onDelete: "CASCADE"
  })
  organization?: Organization | null;

  @ApiProperty({ type: () => SimplifiedTradePartner })
  @ManyToOne(() => TradePartner, (partner) => partner.manufacturerOgdFilings, {
    nullable: true,
    onDelete: "SET NULL"
  })
  manufacturer: TradePartner | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdOgdFilings, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedOgdFilings, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;
}
