import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity, ManyToOne, PrimaryColumn } from "typeorm";
import { UnitOfMeasure, UnitOfMeasureType } from "../types";
import { CommercialInvoiceLine } from "./commercial-invoice-line.entity";

export class SimplifiedCommercialInvoiceLineMeasurement {
  @PrimaryColumn({ type: "integer", nullable: false })
  commercialInvoiceLineId: number;

  /**
   * The type of measurement.
   */
  @ApiProperty({ type: "enum", enum: UnitOfMeasureType })
  @PrimaryColumn({ type: "enum", enum: UnitOfMeasureType, nullable: false })
  type: UnitOfMeasureType;

  /**
   * The unit of measure.
   */
  @ApiProperty({
    type: "enum",
    enum: UnitOfMeasure
  })
  @Column({
    type: "enum",
    enum: UnitOfMeasure,
    nullable: false
  })
  unitOfMeasure: UnitOfMeasure;

  /**
   * The value of the measurement.
   */
  @ApiProperty({ type: "number", format: "float", minimum: 0 })
  @Column({ type: "real", nullable: false })
  value: number;
}

@Entity()
export class CommercialInvoiceLineMeasurement extends SimplifiedCommercialInvoiceLineMeasurement {
  @ApiProperty({ type: () => CommercialInvoiceLine })
  @ManyToOne((type) => CommercialInvoiceLine, (commercialInvoiceLine) => commercialInvoiceLine.measurements, {
    nullable: false,
    onDelete: "CASCADE",
    orphanedRowAction: "delete"
  })
  commercialInvoiceLine: CommercialInvoiceLine;
}
