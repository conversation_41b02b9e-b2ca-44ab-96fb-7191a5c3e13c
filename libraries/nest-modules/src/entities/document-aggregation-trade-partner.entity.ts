import { Column, Entity, ManyToOne } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { FileBatch } from "./file-batch.entity";
import { Shipment } from "./shipment.entity";
import { TradePartner } from "./trade-partner.entity";

@Entity()
export class SimplifiedDocumentAggregationTradePartner extends SimplifiedBaseEntity {
  @Column({ type: "text", array: true, default: [] })
  partnerTypes: string[];

  @Column({ type: "json" })
  aggregatedData: string;

  @Column({ type: "text", array: true, default: [] })
  source: string[];
}

@Entity()
export class DocumentAggregationTradePartner extends SimplifiedDocumentAggregationTradePartner {
  @ManyToOne((type) => TradePartner, { nullable: true, onDelete: "SET NULL" })
  tradePartner: TradePartner | null;

  @ManyToOne((type) => FileBatch, { nullable: true })
  batch: FileBatch | null;

  @ManyToOne((type) => Shipment, { nullable: true, onDelete: "CASCADE" })
  shipment: Shipment | null;
}
