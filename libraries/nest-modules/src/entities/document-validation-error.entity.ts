import { Column, Entity, ManyToOne } from "typeorm";
import { DocumentValidationErrorCode } from "../types";
import { SimplifiedBaseEntity } from "./base.entity";
import { Document } from "./document.entity";

export class SimplifiedDocumentValidationError extends SimplifiedBaseEntity {
  @Column({ type: "varchar", length: 64 })
  code: DocumentValidationErrorCode;

  @Column({ type: "jsonb", default: [] })
  path: string[];
}

@Entity()
export class DocumentValidationError extends SimplifiedDocumentValidationError {
  @ManyToOne(() => Document, (document) => document.validationErrors, {
    onDelete: "CASCADE"
  })
  document: Document;
}
