import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  Column,
  Entity,
  ManyToMany,
  ManyToOne,
  OneToMany,
  Unique
} from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { CanadaTreatmentCode } from "./canada-treatment-code.entity";
import { CommercialInvoiceLine } from "./commercial-invoice-line.entity";
import { CommercialInvoice } from "./commercial-invoice.entity";
import { Importer } from "./importer.entity";
import { Location } from "./location.entity";
import { Product } from "./product.entity";
import { State } from "./state.entity";
import { TradePartner } from "./trade-partner.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedCountry extends SimplifiedBaseEntity {
  @ApiProperty()
  @Column()
  name: string;

  // Compute fields
  @ApiProperty({ type: "string", minLength: 1 })
  displayName: string;

  @ApiProperty({ type: "string", minLength: 1 })
  @Column({ nullable: true })
  // TODO: alpha3 should not be nullable
  alpha3: string;

  @ApiProperty({ type: "string", minLength: 1 })
  @Column({ nullable: true })
  // TODO: alpha2 should not be nullable
  alpha2: string;

  @AfterInsert()
  @AfterLoad()
  @AfterUpdate()
  compute() {
    this.displayName = this.name ?? this.id.toString();
  }
}

@Entity()
@Unique("UNIQUE_COUNTRY_NAME", ["name"])
@Unique("UNIQUE_COUNTRY_ALPHA2", ["alpha2"])
export class Country extends SimplifiedCountry {
  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdCountries, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedCountries, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  // Hidden fields
  @OneToMany((type) => CommercialInvoice, (invoice) => invoice.countryOfExport)
  exportedCommercialInvoices: Promise<Array<CommercialInvoice>>;

  @OneToMany((type) => CommercialInvoiceLine, (line) => line.origin)
  commercialInvoiceLines: Promise<Array<CommercialInvoiceLine>>;

  @OneToMany((type) => Location, (location) => location.country)
  locations: Promise<Array<Location>>;

  @OneToMany((type) => TradePartner, (partner) => partner.country)
  tradePartners: Promise<Array<TradePartner>>;

  @OneToMany((type) => Importer, (importer) => importer.country)
  importers: Promise<Array<Importer>>;

  @OneToMany((type) => Product, (product) => product.origin)
  products: Promise<Array<Product>>;

  @ManyToMany((type) => CanadaTreatmentCode, (canadaTreatmentCode) => canadaTreatmentCode.countries)
  canadaTreatmentCodes: Promise<Array<CanadaTreatmentCode>>;

  @OneToMany((type) => State, (state) => state.country)
  states: Promise<Array<State>>;
}
