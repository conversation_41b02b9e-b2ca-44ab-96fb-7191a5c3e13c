import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, PrimaryColumn } from "typeorm";

export class SimplifiedGmailToken {
  @ApiProperty({ type: "boolean", default: false })
  @Column({ type: "boolean", default: false })
  isDefaultMailbox: boolean;

  @ApiProperty({ type: "string", format: "email" })
  @PrimaryColumn({ nullable: false })
  email: string;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  accessTokenExpiryDate: Date | null;

  @ApiPropertyOptional({ type: "string", pattern: "^\\d+$" })
  @Column({ type: "varchar", nullable: true })
  lastSyncedHistoryId: string | null;
}

@Entity()
export class GmailToken extends SimplifiedGmailToken {
  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  accessToken: string | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  refreshToken: string | null;

  @ApiPropertyOptional({
    description: "Timestamp until which Gmail API read operations for this mailbox are rate limited",
    type: "string",
    format: "date-time",
    nullable: true
  })
  @Column({
    type: "timestamp with time zone",
    nullable: true,
    comment: "Timestamp until which Gmail API read operations for this mailbox are rate limited"
  })
  rateLimitReadUntil: Date | null;
}
