import { ApiProperty } from "@nestjs/swagger";
import { AfterInsert, AfterLoad, AfterUpdate, Column, Entity, ManyToOne, OneToMany } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { SimaFiling } from "./sima-filing.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedCanadaAntiDumping extends SimplifiedBaseEntity {
  @ApiProperty({ type: "string", minLength: 1 })
  // TODO: Remove nullable once the code is added to the database
  @Column({ nullable: true })
  code: string | null;

  @ApiProperty({ minLength: 1 })
  @Column()
  case: string;

  @ApiProperty({ minLength: 1 })
  @Column()
  dumpingCountry: string;

  @ApiProperty({ minLength: 1 })
  @Column()
  subsidyCountry: string;

  @ApiProperty({ minLength: 1 })
  @Column()
  caseType: string;

  @ApiProperty({ minLength: 1 })
  @Column()
  productDefinition: string;

  @ApiProperty({ minLength: 1 })
  @Column()
  exclusion: string;

  @ApiProperty({ minLength: 1 })
  @Column()
  hsCodes: string;

  @ApiProperty({ minLength: 1 })
  @Column()
  antiDumpingDutyLiability: string;

  @ApiProperty({ minLength: 1 })
  @Column()
  countervailingDutyLiability: string;

  // Compute fields
  @ApiProperty({ type: "string", minLength: 1 })
  displayName: string;

  @AfterInsert()
  @AfterLoad()
  @AfterUpdate()
  compute() {
    this.displayName = `${this.case} - ${this.caseType} - ${this.productDefinition ?? ""}`;
  }
}

@Entity()
export class CanadaAntiDumping extends SimplifiedCanadaAntiDumping {
  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdCanadaAntiDumpings, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedCanadaAntiDumpings, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  // Hidden fields
  @OneToMany((type) => SimaFiling, (simaFiling) => simaFiling.measureInForce)
  simaFilings: Promise<Array<SimaFiling>>;
}
