import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { ApiProperty } from "@nestjs/swagger";
import { SimplifiedUser, User } from "./user.entity";

@Entity()
export class Password extends SimplifiedBaseEntity {
  @ApiProperty({ type: "string", format: "password" })
  @Column()
  salt: string;

  @ApiProperty({ type: "string", format: "password" })
  @Column()
  hashPassword: string;

  @ApiProperty({ type: () => SimplifiedUser })
  @OneToOne("User", {
    onDelete: "CASCADE",
    nullable: false
  })
  @JoinColumn()
  user: User;
}
