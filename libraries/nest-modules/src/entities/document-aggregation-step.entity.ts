import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from "typeorm";
import { DocumentAggregationStepStatus } from "../types/document-aggregation-step.types";
import { SimplifiedBaseEntity } from "./base.entity";
import { DocumentAggregationStepLog } from "./document-aggregation-step-log.entity";
import { DocumentAggregation } from "./document-aggregation.entity";

export class SimplifiedDocumentAggregationStep<I = any, O = any> extends SimplifiedBaseEntity {
  @ApiProperty({
    enum: DocumentAggregationStepStatus,
    description: "The status of the document aggregation step"
  })
  @Column({ type: "enum", enum: DocumentAggregationStepStatus })
  status: DocumentAggregationStepStatus;

  @ApiProperty({
    description: "Name of the aggregation step"
  })
  @Column({ type: "varchar", length: 255 })
  name: string;

  @ApiPropertyOptional({
    type: () => Object,
    description: "Input data for the step"
  })
  @Column({ type: "json", nullable: true })
  inputData: I | null;

  @ApiPropertyOptional({
    type: () => Object,
    description: "Output data from the step"
  })
  @Column({ type: "jsonb", nullable: true })
  outputData: O | null;

  @ApiPropertyOptional({
    type: () => String,
    description: "Error message if the step failed"
  })
  @Column({ type: "text", nullable: true })
  errorMessage: string | null;
}

@Entity()
export class DocumentAggregationStep<I = any, O = any> extends SimplifiedDocumentAggregationStep<I, O> {
  @ApiProperty({ type: () => DocumentAggregation })
  @ManyToOne(() => DocumentAggregation, (aggregation) => aggregation.steps, {
    nullable: false,
    onDelete: "CASCADE"
  })
  @JoinColumn({ name: "documentAggregationId" })
  documentAggregation: DocumentAggregation;

  @ApiProperty({ type: () => [DocumentAggregationStepLog] })
  @OneToMany(() => DocumentAggregationStepLog, (log) => log.step, {
    onDelete: "CASCADE"
  })
  logs: DocumentAggregationStepLog[];
}
