import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn } from "typeorm";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { Shipment, SimplifiedShipment } from "./shipment.entity";

export class SimplifiedTrackingHistory {
  @ApiProperty({ type: "integer", minimum: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone" })
  timestamp: Date;

  @ApiProperty({ type: "string" })
  @Column({ type: "text" })
  party: string;

  @ApiProperty({ type: "string" })
  @Column({ type: "text" })
  result: string;
}

@Entity()
export class TrackingHistory extends SimplifiedTrackingHistory {
  @ApiProperty({ type: () => SimplifiedShipment })
  @ManyToOne((type) => Shipment, (shipment) => shipment.trackingHistories, {
    nullable: false,
    onDelete: "CASCADE"
  })
  shipment: Shipment;

  @ApiProperty({ type: () => SimplifiedOrganization })
  @ManyToOne((type) => Organization, (organization) => organization.trackingHistories, {
    nullable: false,
    onDelete: "CASCADE"
  })
  organization: Organization;
}
