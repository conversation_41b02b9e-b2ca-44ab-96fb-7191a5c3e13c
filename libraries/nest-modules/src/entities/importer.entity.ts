import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, ManyToOne, OneToMany, Unique } from "typeorm";
import { ImporterStatus } from "../types/importer.types";
import { SimplifiedBaseEntity } from "./base.entity";
import { Country, SimplifiedCountry } from "./country.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { Shipment } from "./shipment.entity";
import { SimplifiedUser, User } from "./user.entity";
import { CarmStatus } from "../types/carm.types";

export class SimplifiedImporter extends SimplifiedBaseEntity {
  @ApiProperty({ enum: ImporterStatus, default: ImporterStatus.PENDING_POA })
  @Column({
    type: "enum",
    enum: ImporterStatus,
    default: ImporterStatus.PENDING_POA
  })
  status: ImporterStatus;

  @ApiProperty()
  @Column()
  companyName: string;

  @ApiProperty()
  @Column()
  businessNumber: string;

  @ApiProperty()
  @Column()
  address: string;

  @ApiProperty()
  @Column()
  city: string;

  @ApiProperty()
  @Column({ default: "" })
  postalCode: string;

  @ApiProperty()
  @Column()
  state: string;

  @ApiProperty()
  @Column()
  phoneNumber: string;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  fax: string | null;

  @ApiProperty({ type: "string", format: "email" })
  @Column()
  email: string;

  @ApiProperty()
  @Column()
  officerNameAndTitle: string;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  docusignEnvelopeId: string | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  rejectReason: string | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  candataCustomerNumber: string | null;

  @ApiPropertyOptional({
    type: "string",
    format: "email",
    description: "Email address to receive customer emails from"
  })
  @Column({ nullable: true })
  receiveEmail: string | null;

  @ApiPropertyOptional({
    type: "string",
    format: "email",
    isArray: true,
    description: "List of whitelisted sender emails"
  })
  @Column({ type: "varchar", array: true, nullable: true })
  whitelistEmails: Array<string> | null;

  @ApiPropertyOptional({ enum: CarmStatus })
  @Column({ type: "enum", enum: CarmStatus, nullable: true })
  carmStatus: CarmStatus | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ type: "varchar", nullable: true })
  carmApiKey: string | null;
}

@Entity()
@Unique("UNIQUE_IMPORTER_NAME_ORG", ["companyName", "organization"])
@Unique("UNIQUE_IMPORTER_RECEIVE_EMAIL", ["receiveEmail"])
export class Importer extends SimplifiedImporter {
  @ApiPropertyOptional({ type: () => SimplifiedCountry })
  @ManyToOne((type) => Country, (country) => country.importers, {
    nullable: false,
    onDelete: "CASCADE"
  })
  country: Country;

  @ApiProperty({ type: () => SimplifiedOrganization })
  @ManyToOne((type) => Organization, (org) => org.importers, {
    nullable: false,
    onDelete: "CASCADE"
  })
  organization: Organization;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdImporters, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedImporters, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  // Hidden fields
  @OneToMany((type) => Shipment, (shipment) => shipment.importer)
  shipments: Promise<Array<Shipment>>;
}
