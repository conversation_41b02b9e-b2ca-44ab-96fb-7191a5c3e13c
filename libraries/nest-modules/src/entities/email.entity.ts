import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, Index, ManyToOne, OneToMany, Unique } from "typeorm";
import { EmailAddressDto } from "../dto/email.dto";
import { EmailOrigin, EmailStatus } from "../types/email.types";
import { SimplifiedBaseEntity } from "./base.entity";
import { EmailUpdate, SimplifiedEmailUpdate } from "./email-update.entity";
import { File, SimplifiedFile } from "./file.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedEmail extends SimplifiedBaseEntity {
  @ApiProperty({
    type: "string",
    format: "email",
    description: "Gmail Inbox Email Address"
  })
  @Column({ type: "varchar", nullable: false })
  inboxEmail: string;

  @ApiProperty({ enum: EmailOrigin, description: "Origin of the email" })
  @Column({ type: "enum", enum: EmailOrigin, nullable: false })
  origin: EmailOrigin;

  @ApiProperty({ enum: EmailStatus })
  @Column({ type: "enum", enum: EmailStatus })
  status: EmailStatus;

  @ApiProperty({
    type: "string",
    minLength: 1,
    description: "Email ID from Gmail"
  })
  @Column({ type: "varchar", nullable: false })
  gmailId: string;

  @ApiProperty({
    type: "string",
    minLength: 1,
    description: "Thread ID from Gmail"
  })
  @Column({ type: "varchar", nullable: false })
  threadId: string;

  @ApiProperty({
    type: "string",
    minLength: 1,
    description: "History ID from Gmail"
  })
  @Column({ type: "varchar", nullable: false })
  historyId: string;

  @ApiProperty({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: false })
  receiveDate: Date;

  @ApiProperty({
    type: () => [EmailAddressDto],
    minItems: 1,
    description: "List of author email addresses and names"
  })
  @Column({ type: "jsonb", nullable: false })
  from: Array<EmailAddressDto>;

  @ApiPropertyOptional({
    type: () => [EmailAddressDto],
    minItems: 0,
    description: "List of reply-to email addresses and names"
  })
  @Column({ type: "jsonb", nullable: true })
  replyTo: Array<EmailAddressDto> | null;

  @ApiProperty({
    type: () => [EmailAddressDto],
    minItems: 1,
    description: "List of recipient email addresses and names"
  })
  @Column({ type: "jsonb", nullable: false })
  to: Array<EmailAddressDto>;

  @ApiPropertyOptional({
    type: () => [EmailAddressDto],
    minItems: 0,
    description: "List of carbon-copy recipient email addresses and names"
  })
  @Column({ type: "jsonb", nullable: true })
  cc: Array<EmailAddressDto> | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ type: "varchar", nullable: true })
  subject: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "Text content of the email"
  })
  @Column({ type: "varchar", nullable: true })
  text: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "HTML content of the email"
  })
  @Column({ type: "varchar", nullable: true })
  html: string | null;

  @ApiPropertyOptional({
    type: "object",
    description: "Extracted user intents from the email"
  })
  @Column({ type: "jsonb", nullable: true })
  userIntents: Array<Record<string, any>> | null;

  @ApiPropertyOptional({
    type: "string",
    description: "One line summary of the email request"
  })
  @Column({ type: "varchar", nullable: true })
  requestSummary: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "One line summary of the process outcome of the email request"
  })
  @Column({ type: "varchar", nullable: true })
  processingOutcome: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "Error message if the email processing failed"
  })
  @Column({ type: "varchar", nullable: true })
  error: string | null;

  @ApiPropertyOptional({
    type: "string",
    description: "RFC 2822 Message-ID header"
  })
  @Column({ type: "varchar", nullable: true })
  messageId: string | null;
}

@Entity()
@Unique("EMAIL_UNIQUE_GMAIL_ID", ["gmailId"])
export class Email extends SimplifiedEmail {
  @ApiProperty({ type: () => SimplifiedOrganization })
  @ManyToOne(() => Organization, (org) => org.emails, {
    nullable: false,
    onDelete: "CASCADE"
  })
  organization: Organization;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne(() => User, (user) => user.createdEmails, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne(() => User, (user) => user.lastEditedEmails, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  @ApiProperty({ type: () => [SimplifiedEmailUpdate] })
  @OneToMany(() => EmailUpdate, (update) => update.email)
  updates: Array<EmailUpdate>;

  @Column({ type: "tsvector", select: false, nullable: true })
  @Index("idx_email_search_vector", { synchronize: false })
  search_vector: string;
}

export class EmailWithAttachments extends Email {
  @ApiPropertyOptional({
    type: () => [SimplifiedFile],
    description: "Attachments for the email"
  })
  files?: Array<File>;
}
