import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  Check,
  Column,
  Entity,
  ManyToOne,
  OneToMany,
  Unique
} from "typeorm";
import { UnitOfMeasure } from "../types/enums";
import { SimplifiedBaseEntity } from "./base.entity";
import { CommercialInvoiceLine } from "./commercial-invoice-line.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedCanadaTariff extends SimplifiedBaseEntity {
  @ApiProperty({ type: "string", minLength: 2, maxLength: 10 })
  @Column()
  hsCode: string;

  @ApiProperty({ type: "string", format: "date" })
  @Column({ type: "date" })
  effectiveDate: Date;

  @ApiProperty({ type: "string", format: "date", default: "9999-12-31" })
  @Column({ type: "date", default: () => `TO_DATE('9999-12-31', 'YYYY-MM-DD')` })
  expiryDate: Date;

  @ApiProperty()
  @Column()
  description: string;

  @ApiPropertyOptional({ enum: UnitOfMeasure })
  @Column({ type: "enum", enum: UnitOfMeasure, nullable: true })
  uom: UnitOfMeasure | null;

  // /**
  //  * @deprecated MFN treatment should be stored in `treatments` object field
  //  */
  // @ApiPropertyOptional({ deprecated: true })
  // @Column({ nullable: true })
  // mfn: string | null;

  // /**
  //  * @deprecated General tariff should be stored in `treatments` object field
  //  */
  // @ApiPropertyOptional({ deprecated: true })
  // @Column({ nullable: true })
  // generalTariff: string | null;

  // Computed Field
  @ApiProperty({ type: "string", example: "0000.00.00.00" })
  formattedHsCode: string;

  @ApiProperty({ type: "string", minLength: 1 })
  displayName: string;

  @AfterLoad()
  @AfterInsert()
  @AfterUpdate()
  compute() {
    const hsCodeFirstPart = this.hsCode.substring(0, 4);
    const hsCodeSecondPart = this.hsCode.substring(4, 6);
    const hsCodeThirdPart = this.hsCode.substring(6, 8);
    const hsCodeFourthPart = this.hsCode.substring(8, 10);
    this.formattedHsCode = [hsCodeFirstPart, hsCodeSecondPart, hsCodeThirdPart, hsCodeFourthPart]
      .filter((s) => s.length > 0)
      .join(".");
    this.displayName = `${this.hsCode} - ${this.description}`;
  }
}

@Entity()
@Check("VALID_HS_CODE", 'char_length("hsCode") BETWEEN 2 AND 10')
@Unique("UNIQUE_HS_CODE", ["hsCode"])
export class CanadaTariff extends SimplifiedCanadaTariff {
  // @ApiProperty({ type: () => [SimplifiedCanadaTariffTreatment] })
  // // @OneToMany(type => CanadaTariffTreatment, treatment => treatment.canadaTariff)
  // @ManyToMany(type => CanadaTariffTreatment, treatment => treatment.canadaTariffs)
  // @JoinTable({ name: 'canada_tariff_tariff_treatment' })
  // treatments: Array<CanadaTariffTreatment>;

  @ApiPropertyOptional({
    type: "object",
    additionalProperties: { type: "string", nullable: false },
    description: "Tariff treatments and the corresponding tariff rates"
  })
  @Column({ nullable: true, type: "jsonb" })
  treatments: Record<string, string> | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdCanadaTariffs, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedCanadaTariffs, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  // Hidden fields
  @OneToMany((type) => CommercialInvoiceLine, (line) => line.tariffCode)
  commercialInvoiceLines: Promise<Array<CommercialInvoiceLine>>;
}
