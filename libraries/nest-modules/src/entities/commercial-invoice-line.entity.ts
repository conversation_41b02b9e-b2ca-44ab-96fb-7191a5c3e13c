import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, ManyToOne, OneToMany, RelationId } from "typeorm";
import {
  DestinationProvince,
  SafeguardSubjectCode,
  SpecialAuthorityTimeLimitType,
  SurtaxSubjectCode,
  UnitOfMeasure
} from "../types";
import { SimplifiedBaseEntity } from "./base.entity";
import { CanadaExciseTaxCode, SimplifiedCanadaExciseTaxCode } from "./canada-excise-tax-code.entity";
import { CanadaGstExemptCode, SimplifiedCanadaGstExemptCode } from "./canada-gst-exempt-code.entity";
import { CanadaTariff, SimplifiedCanadaTariff } from "./canada-tariff.entity";
import { CanadaTreatmentCode, SimplifiedCanadaTreatmentCode } from "./canada-treatment-code.entity";
import { CanadaVfdCode, SimplifiedCanadaVfdCode } from "./canada-vfd-code.entity";
import { CommercialInvoiceLineMeasurement } from "./commercial-invoice-line-measurement.entity";
import { CommercialInvoice, SimplifiedCommercialInvoice } from "./commercial-invoice.entity";
import { Country, SimplifiedCountry } from "./country.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { Product, SimplifiedProduct } from "./product.entity";
import { SimplifiedState, State } from "./state.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedCommercialInvoiceLine extends SimplifiedBaseEntity {
  /**
   * @description The sequence number of the commercial invoice line.
   * @example 1
   * @default 0
   */
  @ApiProperty({ type: "number", format: "integer" })
  @Column({ type: "integer", nullable: false, default: 0 })
  sequence: number = 0;

  @RelationId((line: CommercialInvoiceLine) => line.commercialInvoice)
  commercialInvoiceId: number;

  @ApiPropertyOptional({ type: "number", format: "integer" })
  @Column({ type: "integer", nullable: true })
  candataId: number | null;

  @ApiProperty({ type: "string" })
  @Column({ nullable: false })
  hsCode: string;

  @ApiProperty({ type: "string" })
  // TODO: Remove nullable
  @Column({ nullable: true })
  goodsDescription: string | null;

  @ApiProperty({ type: "number", format: "float", minimum: 0 })
  @Column({ type: "real", nullable: false, default: 0 })
  quantity: number;

  @ApiProperty({
    type: "enum",
    enum: UnitOfMeasure,
    default: UnitOfMeasure.NUMBER
  })
  @Column({
    type: "enum",
    enum: UnitOfMeasure,
    nullable: false,
    default: UnitOfMeasure.NUMBER
  })
  unitOfMeasure: UnitOfMeasure;

  @ApiProperty({ type: "number", format: "float", minimum: 0 })
  // TODO: Remove nullable
  @Column({ type: "real", nullable: true })
  unitPrice: number | null;

  @ApiProperty({ type: "number", format: "float", minimum: 0, default: 0 })
  @Column({ type: "real", nullable: false, default: 0 })
  totalLineValue: number;

  @ApiProperty({ type: "number", format: "float", minimum: 0, default: 0 })
  @Column({ type: "real", nullable: false, default: 0 })
  totalLineValueCad: number;

  @ApiPropertyOptional()
  @Column({ type: "text", nullable: true })
  additionalInfo: string | null;

  // Special Authorities
  @ApiPropertyOptional({ type: "string", maxLength: 17 })
  @Column({ nullable: true })
  orderInCouncil: string | null;

  @ApiPropertyOptional({
    enum: SpecialAuthorityTimeLimitType,
    description:
      "4 - Any other goods 4 years\n3 - Goods placed in a bonded warehouse for marking in accordance with the Marking of Imported Goods Regulations or for display at conventions, exhibitions or trade shows 90 days\n2 - Beer and wine 5 years\n1 - Spare parts for aircraft or vessels, oceanic cable, oil-drilling supplies and related parts and equipment, not intended for domestic consumption 15 years"
  })
  @Column({ type: "enum", enum: SpecialAuthorityTimeLimitType, nullable: true })
  timeLimitType: SpecialAuthorityTimeLimitType | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  timeLimitStartDate: Date | null;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  timeLimitEndDate: Date | null;

  @ApiPropertyOptional({ type: "string", maxLength: 17 })
  @Column({ nullable: true })
  authorityPermit: string | null;

  @ApiPropertyOptional({ type: "string" })
  @Column({ nullable: true })
  dutiesReliefLicence: string | null;

  // Surtax and Safeguard
  @ApiProperty({
    enum: SurtaxSubjectCode,
    default: SurtaxSubjectCode.NON_SUBJECT,
    description: "N-Non-subject, S-Subject"
  })
  @Column({
    type: "enum",
    enum: SurtaxSubjectCode,
    default: SurtaxSubjectCode.NON_SUBJECT
  })
  surtaxSubjectCode: SurtaxSubjectCode;

  @ApiPropertyOptional({
    type: "string",
    description: "Required if surtax is subjected"
  })
  @Column({ nullable: true })
  surtaxCode: string | null;

  @ApiPropertyOptional({
    enum: SafeguardSubjectCode,
    default: SafeguardSubjectCode.NON_SUBJECT,
    description: "N-Non-subject, S-Subject"
  })
  @Column({
    type: "enum",
    enum: SafeguardSubjectCode,
    nullable: false,
    default: SafeguardSubjectCode.NON_SUBJECT
  })
  safeguardSubjectCode: SafeguardSubjectCode;

  @ApiPropertyOptional({
    type: "string",
    description: "Required if safeguard is subjected"
  })
  @Column({ nullable: true })
  safeguardCode: string | null;

  // SIMA
  @ApiPropertyOptional({ type: "integer", minimum: 0 })
  @Column({ type: "integer", nullable: true })
  simaQuantity: number | null;

  @ApiPropertyOptional({ enum: UnitOfMeasure })
  @Column({ type: "enum", enum: UnitOfMeasure, nullable: true })
  simaUnitOfMeasure: UnitOfMeasure | null;

  // Duties and Taxes
  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @Column({ type: "real", nullable: true })
  provincialAlcoholTax: number | null;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @Column({ type: "real", nullable: true })
  provincialTobaccoTax: number | null;

  @ApiPropertyOptional({ type: "number", format: "float", minimum: 0 })
  @Column({ type: "real", nullable: true })
  provincialCannabisExciseDuty: number | null;

  @ApiPropertyOptional({
    type: "integer",
    minimum: 0,
    description: "Percentage of alcohol contained in the goods"
  })
  @Column({ type: "integer", nullable: true })
  alcoholPercentage: number | null;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    description: "PST/HST/QST"
  })
  @Column({ type: "real", nullable: true })
  pstHst: number | null;

  @ApiPropertyOptional({ enum: DestinationProvince })
  @Column({ type: "enum", enum: DestinationProvince, nullable: true })
  destinationProvince: DestinationProvince | null;

  // CAD Amounts
  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  cadCalculationDate: Date | null;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Column({ type: "real", nullable: true })
  valueForDuty: number | null;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Column({ type: "real", nullable: true })
  valueForTax: number | null;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Column({ type: "real", nullable: true })
  antiDumping: number | null;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Column({ type: "real", nullable: true })
  countervailing: number | null;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Column({ type: "real", nullable: true })
  customsDuties: number | null;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Column({ type: "real", nullable: true })
  exciseDuties: number | null;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Column({ type: "real", nullable: true })
  exciseTax: number | null;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Column({ type: "real", nullable: true })
  gst: number | null;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Column({ type: "real", nullable: true })
  safeguard: number | null;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Column({ type: "real", nullable: true })
  surtax: number | null;

  @ApiPropertyOptional({ type: "number", format: "float" })
  @Column({ type: "real", nullable: true })
  totalDutiesAndTaxes: number | null;

  // TODO: Remove these properties
  /** @deprecated This property will be removed in future versions. */
  @ApiProperty({
    type: "number",
    format: "float",
    minimum: 0,
    default: 0,
    deprecated: true
  })
  @Column({ type: "real", nullable: false, default: 0 })
  dutyCad: number;

  /** @deprecated This property will be removed in future versions. */
  @ApiProperty({
    type: "number",
    format: "float",
    minimum: 0,
    default: 0,
    deprecated: true
  })
  @Column({ type: "real", nullable: false, default: 0 })
  dutyAndTax: number;
}

@Entity()
export class CommercialInvoiceLine extends SimplifiedCommercialInvoiceLine {
  @ApiProperty({
    type: () => SimplifiedCanadaVfdCode,
    description: "Value for Duty code"
  })
  // TODO: Remove nullable
  @ManyToOne((type) => CanadaVfdCode, (vfd) => vfd.commercialInvoiceLines, {
    nullable: true,
    onDelete: "SET NULL"
  })
  vfd: CanadaVfdCode | null;

  @ApiProperty({
    type: () => SimplifiedCanadaTreatmentCode,
    description: "Tariff Treatment code"
  })
  // TODO: Remove nullable
  @ManyToOne((type) => CanadaTreatmentCode, (tt) => tt.commercialInvoiceLines, {
    nullable: true,
    onDelete: "SET NULL"
  })
  tt: CanadaTreatmentCode | null;

  @ApiPropertyOptional({
    type: () => SimplifiedCanadaGstExemptCode,
    description: "GST Exempt code. If not set, the default GST code 10 will be used during entry submission"
  })
  @ManyToOne((type) => CanadaGstExemptCode, (code) => code.commercialInvoiceLines, {
    nullable: true,
    onDelete: "SET NULL"
  })
  gstExemptCode: CanadaGstExemptCode | null;

  @ApiPropertyOptional({
    type: () => SimplifiedCanadaTariff,
    description: "4-digit Chapter 99 code"
  })
  @ManyToOne((type) => CanadaTariff, (tariff) => tariff.commercialInvoiceLines, {
    nullable: true,
    onDelete: "SET NULL"
  })
  tariffCode: CanadaTariff | null;

  @ApiPropertyOptional({ type: () => SimplifiedCanadaExciseTaxCode })
  @ManyToOne((type) => CanadaExciseTaxCode, (code) => code.commercialInvoiceLines, {
    nullable: true,
    onDelete: "SET NULL"
  })
  exciseTaxCode: CanadaExciseTaxCode | null;

  @ApiProperty({ type: () => SimplifiedCountry })
  // TODO: Remove nullable
  @ManyToOne((type) => Country, (country) => country.commercialInvoiceLines, {
    nullable: true,
    onDelete: "SET NULL"
  })
  origin: Country | null;

  @ApiPropertyOptional({ type: () => SimplifiedState })
  @ManyToOne((type) => State, null, {
    nullable: true,
    onDelete: "SET NULL"
  })
  originState: State | null;

  @ApiProperty({ type: () => SimplifiedProduct })
  @ManyToOne((type) => Product, (product) => product.commercialInvoiceLines, {
    nullable: false,
    onDelete: "RESTRICT"
  })
  product: Product;

  @ApiProperty({ type: () => SimplifiedCommercialInvoice })
  @ManyToOne((type) => CommercialInvoice, (commercialInvoice) => commercialInvoice.commercialInvoiceLines, {
    nullable: false,
    onDelete: "CASCADE"
  })
  commercialInvoice: CommercialInvoice;

  @ApiProperty({ type: () => SimplifiedOrganization })
  @ManyToOne((type) => Organization, (organization) => organization.commercialInvoiceLines, {
    nullable: false,
    onDelete: "CASCADE"
  })
  organization: Organization;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdCommercialInvoiceLines, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedCommercialInvoiceLines, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  @ApiProperty({ type: () => CommercialInvoiceLineMeasurement })
  @OneToMany((type) => CommercialInvoiceLineMeasurement, (measurement) => measurement.commercialInvoiceLine, {
    cascade: true
  })
  measurements: CommercialInvoiceLineMeasurement[];
}
