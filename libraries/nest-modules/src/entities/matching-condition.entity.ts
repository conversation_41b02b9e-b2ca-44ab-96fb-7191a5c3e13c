import { ApiHideProperty, ApiProperty, ApiPropertyOptional, getSchemaPath } from "@nestjs/swagger";
import { Column, Entity, Index, ManyToOne, RelationId } from "typeorm";
import { MatchingConditionAttributeType, MatchingConditionOperator } from "../types";
import { SimplifiedBaseEntity } from "./base.entity";
import { Country } from "./country.entity";
import { MatchingRule } from "./matching-rule.entity";
import { Organization } from "./organization.entity";
import { TradePartner } from "./trade-partner.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedMatchingCondition extends SimplifiedBaseEntity {
  @ApiHideProperty()
  @RelationId((condition: MatchingCondition) => condition.rule)
  ruleId: number;

  @ApiProperty({ minLength: 1 })
  @Column()
  attribute: string;

  @ApiProperty({ enum: MatchingConditionOperator })
  @Column({ type: "enum", enum: MatchingConditionOperator })
  operator: MatchingConditionOperator;

  @ApiPropertyOptional({
    description:
      'The value of the condition. The type of the value depends on the attribute and operator. For operator "in", this field should be an array of values.',
    examples: [
      null,
      "string",
      "123",
      "123.45",
      "true",
      "2024-01-01",
      "2024-01-01T00:00:00Z",
      `["value1","value2","value3"]`,
      `[1, 2, 3]`
    ]
  })
  @Column({ nullable: true })
  value: string | null;

  @ApiProperty({ type: "boolean", default: false })
  @Column({ type: "boolean", default: false })
  isOperationInverted: boolean;

  @ApiProperty({ type: "enum", enum: MatchingConditionAttributeType })
  @Column({ type: "enum", enum: MatchingConditionAttributeType, nullable: false })
  attributeType: MatchingConditionAttributeType;

  @ApiPropertyOptional({
    type: "array",
    items: { type: "string" },
    description:
      "Array of string values of the condition. This field is only used for string attributes. If operator is 'in', this field may contain more than 1 item, otherwise it must contain exactly 1 item."
  })
  @Column({ type: "varchar", array: true, nullable: true })
  valueString: Array<string> | null;

  @ApiPropertyOptional({
    type: "array",
    items: { type: "integer" },
    description:
      "Array of integer values of the condition. This field is only used for integer attributes. If operator is 'in', this field may contain more than 1 item, otherwise it must contain exactly 1 item."
  })
  @Column({ type: "integer", array: true, nullable: true })
  valueInteger: Array<number> | null;

  @ApiPropertyOptional({
    type: "array",
    items: { type: "number", format: "float" },
    description:
      "Floating point value of the condition. This field is only used for float attributes. If operator is 'in', this field may contain more than 1 item, otherwise it must contain exactly 1 item."
  })
  @Column({ type: "double precision", array: true, nullable: true })
  valueFloat: Array<number> | null;

  @ApiPropertyOptional({
    type: "array",
    items: { type: "boolean" },
    description:
      "Array of boolean values of the condition. This field is only used for boolean attributes. If operator is 'in', this field may contain more than 1 item, otherwise it must contain exactly 1 item."
  })
  @Column({ type: "boolean", array: true, nullable: true })
  valueBoolean: Array<boolean> | null;

  @ApiPropertyOptional({
    type: "array",
    items: { type: "string", format: "date-time" },
    description:
      "Array of datetime values of the condition. This field is only used for datetime attributes. If operator is 'in', this field may contain more than 1 item, otherwise it must contain exactly 1 item."
  })
  @Column({ type: "timestamp with time zone", array: true, nullable: true })
  valueDateTime: Array<Date> | null;

  // Compute field
  @ApiPropertyOptional({
    oneOf: [
      { $ref: getSchemaPath(TradePartner) },
      { $ref: getSchemaPath(Country) },
      { $ref: getSchemaPath(Organization) }
    ],
    description: "The record that the condition value refers to."
  })
  valueRecord?: TradePartner | Country | Organization | null;
}

@Entity()
@Index("IDX_MATCHING_CONDITION_RULE", ["attribute", "operator", "value"])
export class MatchingCondition extends SimplifiedMatchingCondition {
  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdMatchingConditions, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedMatchingConditions, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  @ApiProperty({ type: () => MatchingRule })
  @ManyToOne((type) => MatchingRule, (rule) => rule.conditions, {
    nullable: false,
    onDelete: "CASCADE"
  })
  rule: MatchingRule;
}
