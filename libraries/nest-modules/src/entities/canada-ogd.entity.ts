import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { AfterUpdate, AfterLoad, AfterInsert, Column, Entity, ManyToOne, OneToMany, Unique } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { CanadaGovernmentAgency } from "../types";
import { SimplifiedUser, User } from "./user.entity";
import { OgdFiling } from "./ogd-filing.entity";

export class SimplifiedCanadaOgd extends SimplifiedBaseEntity {
  @ApiProperty({ enum: CanadaGovernmentAgency })
  @Column({ type: "enum", enum: CanadaGovernmentAgency })
  agency: CanadaGovernmentAgency;

  @ApiProperty({ minLength: 1 })
  @Column()
  program: string;

  @ApiPropertyOptional({ minLength: 1 })
  @Column({ nullable: true })
  commodityType: string | null;

  // Compute fields
  @ApiProperty({ type: "string", minLength: 1 })
  displayName: string;

  @AfterInsert()
  @AfterLoad()
  @AfterUpdate()
  compute() {
    this.displayName = `${this.agency} - ${this.program} - ${this.commodityType ?? ""}`;
  }
}

@Entity()
@Unique("UNIQUE_CANADA_OGD_AGENCY_PROGRAM_COMMODITY_TYPE", ["agency", "program", "commodityType"])
export class CanadaOgd extends SimplifiedCanadaOgd {
  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdCanadaOgds, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedCanadaOgds, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  // Hidden fields
  @OneToMany((type) => OgdFiling, (filing) => filing.ogd)
  filings: Promise<Array<OgdFiling>>;
}
