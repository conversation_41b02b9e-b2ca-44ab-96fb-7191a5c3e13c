import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity, Index, JoinColumn, ManyToOne } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { Port, SimplifiedPort } from "./port.entity";

export class SimplifiedCanadaSubLocation extends SimplifiedBaseEntity {
  @ApiProperty()
  @Column()
  name: string;

  @ApiProperty()
  @Index({ unique: true })
  @Column()
  code: string;

  @ApiProperty()
  @Column()
  address: string;
}

@Entity()
export class CanadaSubLocation extends SimplifiedCanadaSubLocation {
  @ApiProperty({ type: () => SimplifiedPort })
  @ManyToOne((type) => Port, (port) => port.subLocations, {
    onDelete: "RESTRICT",
    onUpdate: "CASCADE"
  })
  @JoinColumn({ name: "portCode", referencedColumnName: "code" })
  port: Port;
}
