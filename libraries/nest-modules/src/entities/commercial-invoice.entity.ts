import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, Index, ManyToOne, OneToMany, Unique } from "typeorm";
import { Currency, PackageUOM, WeightUOM } from "../types";
import { SimplifiedBaseEntity } from "./base.entity";
import { CommercialInvoiceLine, SimplifiedCommercialInvoiceLine } from "./commercial-invoice-line.entity";
import { Country, SimplifiedCountry } from "./country.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { Shipment, SimplifiedShipment } from "./shipment.entity";
import { SimplifiedState, State } from "./state.entity";
import { SimplifiedTradePartner, TradePartner } from "./trade-partner.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedCommercialInvoice extends SimplifiedBaseEntity {
  @ApiPropertyOptional({ type: "integer" })
  @Column({ type: "integer", nullable: true })
  candataId: number | null;

  @ApiProperty({ type: "string" })
  @Column({ nullable: false })
  invoiceNumber: string;

  @ApiProperty({ enum: Currency, default: Currency.CAD })
  @Column({
    type: "enum",
    enum: Currency,
    nullable: false,
    default: Currency.CAD
  })
  currency: Currency;

  @ApiPropertyOptional({ type: "string", format: "date-time" })
  @Column({ type: "timestamp with time zone", nullable: true })
  invoiceDate: Date | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  poNumber: string | null;

  @ApiProperty({ type: "number", format: "float", minimum: 0, default: 0 })
  @Column({ type: "real", nullable: false, default: 0 })
  grossWeight: number;

  @ApiProperty({ enum: WeightUOM, default: WeightUOM.KILOGRAM })
  @Column({
    type: "enum",
    enum: WeightUOM,
    nullable: false,
    default: WeightUOM.KILOGRAM
  })
  weightUOM: WeightUOM;

  @ApiProperty({ type: "integer", minimum: 0, default: 0 })
  @Column({ type: "integer", nullable: false, default: 0 })
  numberOfPackages: number;

  @ApiProperty({ enum: PackageUOM, default: PackageUOM.BOX })
  @Column({
    type: "enum",
    enum: PackageUOM,
    nullable: false,
    default: PackageUOM.BOX
  })
  packageUOM: PackageUOM;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    maximum: 9999
  })
  @Column({ type: "real", nullable: true })
  includedTransCost: number | null;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    maximum: 9999
  })
  @Column({ type: "real", nullable: true })
  includedPackCost: number | null;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    maximum: 9999
  })
  @Column({ type: "real", nullable: true })
  includedMiscCost: number | null;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    maximum: 9999
  })
  @Column({ type: "real", nullable: true })
  excludedTransCost: number | null;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    maximum: 9999
  })
  @Column({ type: "real", nullable: true })
  excludedPackCost: number | null;

  @ApiPropertyOptional({
    type: "number",
    format: "float",
    minimum: 0,
    maximum: 9999
  })
  @Column({ type: "real", nullable: true })
  excludedMiscCost: number | null;

  @ApiPropertyOptional({ type: "boolean" })
  @Column({ type: "boolean", nullable: true })
  valueIncludesDuty: boolean | null;

  @ApiPropertyOptional()
  @Column({ type: "text", nullable: true })
  additionalInfo: string | null;
}

@Entity()
@Index("IDX_CI_SHIPMENT_ID", ["shipment"])
@Unique("UNIQUE_CI_INVOICE_NUMBER", ["invoiceNumber", "shipment"])
export class CommercialInvoice extends SimplifiedCommercialInvoice {
  @ApiProperty({ type: () => SimplifiedShipment })
  @ManyToOne((type) => Shipment, (shipment) => shipment.commercialInvoices, {
    nullable: false,
    onDelete: "NO ACTION",
    createForeignKeyConstraints: false
  })
  shipment: Shipment;

  @ApiProperty({ type: () => SimplifiedCountry })
  @ManyToOne((type) => Country, (country) => country.exportedCommercialInvoices, {
    nullable: true,
    onDelete: "RESTRICT"
  })
  countryOfExport: Country | null;

  @ApiPropertyOptional({ type: () => SimplifiedState })
  @ManyToOne((type) => State, null, {
    nullable: true,
    onDelete: "SET NULL"
  })
  stateOfExport: State | null;

  @ApiPropertyOptional({
    type: () => SimplifiedTradePartner,
    description: "The exporter of the commercial invoice"
  })
  @ManyToOne((type) => TradePartner, (partner) => partner.exporterCommercialInvoice, {
    nullable: true,
    onDelete: "SET NULL"
  })
  exporter: TradePartner | null;

  @ApiPropertyOptional({
    type: () => SimplifiedTradePartner,
    description: "The purchaser of the commercial invoice"
  })
  @ManyToOne((type) => TradePartner, (partner) => partner.purchaserCommercialInvoice, {
    nullable: true,
    onDelete: "SET NULL"
  })
  purchaser: TradePartner | null;

  @ApiPropertyOptional({
    type: () => SimplifiedTradePartner,
    description: "The ship to of the commercial invoice"
  })
  @ManyToOne((type) => TradePartner, (partner) => partner.shipToCommercialInvoice, {
    nullable: true,
    onDelete: "SET NULL"
  })
  shipTo: TradePartner | null;

  @ApiProperty({
    type: () => SimplifiedTradePartner,
    description: "The vendor of the commercial invoice"
  })
  @ManyToOne((type) => TradePartner, (partner) => partner.vendorCommercialInvoice, {
    nullable: false,
    onDelete: "RESTRICT"
  })
  vendor: TradePartner;

  @ApiPropertyOptional({
    type: () => SimplifiedTradePartner,
    description: "The manufacturer of the commercial invoice"
  })
  @ManyToOne((type) => TradePartner, (partner) => partner.manufacturerCommercialInvoice, {
    nullable: true,
    onDelete: "SET NULL"
  })
  manufacturer: TradePartner | null;

  @ApiProperty({ type: () => SimplifiedOrganization })
  @ManyToOne((type) => Organization, (organization) => organization.commercialInvoices, {
    nullable: false,
    onDelete: "CASCADE"
  })
  organization: Organization;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdCommercialInvoices, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedCommercialInvoices, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  @ApiProperty({ type: () => [SimplifiedCommercialInvoiceLine] })
  @OneToMany((type) => CommercialInvoiceLine, (invoiceLine) => invoiceLine.commercialInvoice)
  commercialInvoiceLines: Array<CommercialInvoiceLine>;
}
