import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity, ManyToOne, OneToMany } from "typeorm";
import { FileStatus } from "../types/file.types";
import { SimplifiedBaseEntity } from "./base.entity";
import { Document, SimplifiedDocument } from "./document.entity";
import { FileBatch, SimplifiedFileBatch } from "./file-batch.entity";
import { FilePage, SimplifiedFilePage } from "./file-page.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { Shipment, SimplifiedShipment } from "./shipment.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedFile extends SimplifiedBaseEntity {
  @ApiProperty({ description: "Path to the file" })
  @Column()
  path: string;

  @ApiProperty({ description: "Name of the file" })
  @Column()
  name: string;

  @ApiProperty({ description: "The parse result of the file" })
  @Column({ nullable: true, type: "text" })
  parseResult: string;

  @ApiProperty({ description: "The parser used to parse the file" })
  @Column({ nullable: true })
  parser: string;

  @ApiProperty({ description: "Number of pages in the file" })
  @Column({ nullable: true })
  numPages: number;

  @ApiProperty({ enum: FileStatus, default: FileStatus.PENDING })
  @Column({ type: "enum", enum: FileStatus, default: FileStatus.PENDING })
  status: FileStatus;

  @ApiProperty({ description: "The mime type of the file" })
  @Column({ nullable: true })
  mimeType: string;

  @ApiProperty({ description: "The SHA-1 hash of the file" })
  @Column({ nullable: true })
  hash: string;

  @ApiProperty({ type: "string" })
  @Column({ nullable: true })
  batchId: string | null;

  @ApiProperty({ type: "number" })
  @Column()
  organizationId: number;

  @ApiProperty({ type: "number", nullable: true })
  @Column({ nullable: true })
  shipmentId: number | null;

  isProcessing?() {
    return [FileStatus.PENDING, FileStatus.PARSING, FileStatus.SPLITTING].includes(this.status);
  }
}

@Entity()
export class File extends SimplifiedFile {
  @ApiProperty({ type: () => SimplifiedOrganization })
  @ManyToOne((type) => Organization, (org) => org.files, {
    nullable: false,
    onDelete: "CASCADE"
  })
  organization: Organization;

  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne(() => User, (user) => user.createdFiles, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne(() => User, (user) => user.lastEditedFiles, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  @ApiProperty({ type: () => [SimplifiedDocument] })
  @OneToMany((type) => Document, (doc) => doc.file)
  documents: Array<Document>;

  @ApiProperty({ type: () => SimplifiedShipment })
  @ManyToOne((type) => Shipment, (shipment) => shipment.files, {
    nullable: true,
    onDelete: "SET NULL"
  })
  shipment: Shipment | null;

  @ApiProperty({ type: () => SimplifiedFileBatch })
  @ManyToOne((type) => FileBatch, (batch) => batch.files, {
    nullable: true,
    onDelete: "SET NULL"
  })
  batch: FileBatch | null;

  @ApiProperty({ type: () => [SimplifiedFilePage] })
  @OneToMany((type) => FilePage, (page) => page.file, {
    cascade: true,
    onDelete: "CASCADE"
  })
  pages: Array<FilePage>;
}
