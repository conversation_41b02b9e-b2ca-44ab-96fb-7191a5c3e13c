import { ApiProperty } from "@nestjs/swagger";
import { Expose } from "class-transformer";
import { Column, Entity, ManyToOne, OneToMany, Unique } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { DocumentTypeField, SimplifiedDocumentType<PERSON>ield } from "./document-type-field.entity";
import { Document } from "./document.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedDocumentType extends SimplifiedBaseEntity {
  @ApiProperty({ description: "Name of the document type" })
  @Column()
  name: string;

  @ApiProperty({ description: "Prompt template for the document type" })
  @Column({ type: "text" })
  @Expose({ groups: ["backoffice"] })
  promptTemplate: string;
}

@Entity()
@Unique("UNIQUE_DOCUMENT_TYPE_NAME", ["name"])
export class DocumentType extends SimplifiedDocumentType {
  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne(() => User, (user) => user.createdDocumentTypes, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne(() => User, (user) => user.lastEditedDocumentTypes, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  @ApiProperty({ type: () => [SimplifiedDocumentTypeField] })
  @OneToMany((type) => DocumentTypeField, (field) => field.documentType)
  fields: Array<DocumentTypeField>;

  // Hidden fields
  @OneToMany((type) => Document, (doc) => doc.documentType)
  documents: Array<Document>;
}
