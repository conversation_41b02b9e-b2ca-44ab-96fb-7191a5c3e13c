import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne
} from "typeorm";
import { DocumentStatus } from "../types/document.types";
import { SimplifiedBaseEntity } from "./base.entity";
import { DocumentAggregation, SimplifiedDocumentAggregation } from "./document-aggregation.entity";
import { DocumentField, SimplifiedDocumentField } from "./document-field.entity";
import { DocumentType, SimplifiedDocumentType } from "./document-type.entity";
import { DocumentValidationError } from "./document-validation-error.entity";
import { FilePage } from "./file-page.entity";
import { File, SimplifiedFile } from "./file.entity";
import { Organization, SimplifiedOrganization } from "./organization.entity";
import { Shipment, SimplifiedShipment } from "./shipment.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedDocument extends SimplifiedBaseEntity {
  @ApiProperty({ description: "Name of the document" })
  @Column()
  name: string;

  @ApiProperty({ description: "Start page of the document" })
  @Column({ type: "integer" })
  startPage: number;

  @ApiProperty({ description: "End page of the document" })
  /** deprecated */
  @Column({ type: "integer", nullable: true })
  endPage: number;

  @ApiProperty({ enum: DocumentStatus, default: DocumentStatus.PENDING })
  @Column({
    type: "enum",
    enum: DocumentStatus,
    default: DocumentStatus.PENDING
  })
  status: DocumentStatus;

  @ApiProperty({ type: "number" })
  @Column()
  organizationId: number;

  @ApiProperty({ type: "number" })
  @Column()
  fileId: number;

  @ApiProperty({ type: "number", nullable: true })
  @Column({ nullable: true })
  shipmentId: number | null;

  isProcessing?() {
    return [DocumentStatus.EXTRACTING, DocumentStatus.PENDING].includes(this.status as DocumentStatus);
  }

  @ApiProperty({ type: "string" })
  displayStatus: string;

  @AfterLoad()
  @AfterInsert()
  @AfterUpdate()
  compute?() {
    this.displayStatus = this.isShipmentMismatch ? DocumentStatus.SHIPMENT_MISMATCH : this.status;
  }

  @ApiProperty({ type: "string" })
  @Column({ type: "text", nullable: true })
  jobId: string | null;

  @ApiProperty({ type: "boolean" })
  @Column({ type: "boolean", default: false })
  isShipmentMismatch: boolean;

  @ApiProperty({ type: "boolean" })
  @Column({ type: "boolean", default: false })
  isHidden: boolean;
}

@Entity()
export class Document extends SimplifiedDocument {
  @ApiProperty({ type: () => SimplifiedFile })
  @ManyToOne((type) => File, (file) => file.documents, {
    nullable: false,
    onDelete: "CASCADE"
  })
  file: File;

  @ApiProperty({ type: () => SimplifiedOrganization })
  @ManyToOne((type) => Organization, (org) => org.documents, {
    nullable: false,
    onDelete: "CASCADE"
  })
  organization: Organization;

  @ApiProperty({ type: () => SimplifiedDocumentType })
  @ManyToOne((type) => DocumentType, (type) => type.documents, {
    nullable: true,
    onDelete: "SET NULL"
  })
  documentType: DocumentType | null;

  @ApiProperty({ type: () => SimplifiedShipment })
  @ManyToOne((type) => Shipment, (shipment) => shipment.documents, {
    nullable: true,
    onDelete: "SET NULL"
  })
  shipment: Shipment | null;

  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne(() => User, (user) => user.createdDocuments, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiProperty({ type: () => SimplifiedUser })
  @ManyToOne(() => User, (user) => user.lastEditedDocuments, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  @ApiProperty({ type: () => [SimplifiedDocumentField] })
  @OneToMany((type) => DocumentField, (field) => field.document, {
    cascade: true
  })
  fields: Array<DocumentField>;

  @ApiPropertyOptional({ type: () => SimplifiedDocumentAggregation })
  @ManyToOne(() => DocumentAggregation, (aggregation) => aggregation.documents, {
    nullable: true,
    onDelete: "SET NULL"
  })
  aggregation: DocumentAggregation | null;

  @OneToOne(() => Document, {
    nullable: true,
    onDelete: "SET NULL"
  })
  @JoinColumn({ name: "referenceId" })
  reference: Document | null;

  @OneToOne(() => Document, (document) => document.reference, {
    nullable: true,
    onDelete: "SET NULL"
  })
  referencedBy: Document | null;

  @OneToMany(() => FilePage, (page) => page.document, {
    onDelete: "SET NULL",
    cascade: ["update"]
  })
  pages: Array<FilePage>;

  @OneToMany(() => DocumentValidationError, (validationError) => validationError.document, {
    onDelete: "CASCADE"
  })
  validationErrors: Array<DocumentValidationError>;
}
