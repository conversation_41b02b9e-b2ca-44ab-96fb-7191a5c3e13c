import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { AfterInsert, AfterLoad, AfterUpdate, Column, Entity, ManyToOne, OneToMany, Unique } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { SimaFiling } from "./sima-filing.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedCanadaSimaCode extends SimplifiedBaseEntity {
  @ApiProperty({ type: "integer", minimum: 1 })
  @Column({ type: "integer" })
  code: number;

  @ApiProperty({ minLength: 1 })
  @Column()
  explanation: string;

  // Compute fields
  @ApiProperty({ type: "string", minLength: 1 })
  displayName: string;

  @AfterInsert()
  @AfterLoad()
  @AfterUpdate()
  compute() {
    this.displayName = `${this.code} - ${this.explanation}`;
  }
}

@Entity()
@Unique("UNIQUE_CANADA_SIMA_CODE", ["code"])
export class CanadaSimaCode extends SimplifiedCanadaSimaCode {
  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdCanadaSimaCodes, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedCanadaSimaCodes, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  // Hidden fields
  @OneToMany((type) => SimaFiling, (simaFiling) => simaFiling.simaCode)
  simaFilings: Promise<Array<SimaFiling>>;
}
