import { ApiProperty } from "@nestjs/swagger";
import { Column, Entity, Index, JoinColumn, ManyToOne, Unique } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { Document, SimplifiedDocument } from "./document.entity";
import { File, SimplifiedFile } from "./file.entity";

export class SimplifiedFilePage extends SimplifiedBaseEntity {
  @ApiProperty({ description: "Page number" })
  @Column()
  page: number;

  @ApiProperty({ description: "Sequence of the page in the document" })
  @Column({ nullable: true })
  documentPage: number | null;

  @ApiProperty({ description: "Markdown of the page" })
  @Column({ type: "text" })
  md: string;

  @ApiProperty({ description: "File ID" })
  @Column()
  fileId: number;

  @ApiProperty({ description: "Document ID" })
  @Column({ nullable: true })
  documentId: number | null;
}

// This is the map of the document pages to the file pages
@Entity()
@Unique(["page", "file"]) // unique page per file
@Unique(["documentPage", "document"]) // unique document page per document
@Index(["page", "file"]) // index for page and document page
@Index(["documentPage", "document"]) // index for document page and document
export class FilePage extends SimplifiedFilePage {
  @ApiProperty({ type: () => SimplifiedDocument })
  @ManyToOne(() => Document, (document) => document.pages, {
    onDelete: "SET NULL",
    nullable: true
  })
  @JoinColumn({ name: "documentId", referencedColumnName: "id" })
  document: Document | null;

  @ApiProperty({ type: () => SimplifiedFile })
  @ManyToOne(() => File, (file) => file.pages, {
    onDelete: "CASCADE"
  })
  @JoinColumn({ name: "fileId", referencedColumnName: "id" })
  file: File;
}
