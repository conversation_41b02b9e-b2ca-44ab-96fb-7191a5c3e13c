import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, ManyToOne, OneToMany, Unique } from "typeorm";
import { LocationType } from "../types/location.types";
import { SimplifiedBaseEntity } from "./base.entity";
import { Country, SimplifiedCountry } from "./country.entity";
import { Port } from "./port.entity";
import { Shipment } from "./shipment.entity";
import { SimplifiedUser, User } from "./user.entity";

export class SimplifiedLocation extends SimplifiedBaseEntity {
  @ApiPropertyOptional({ enum: LocationType })
  @Column({ type: "enum", enum: LocationType, nullable: true })
  locationType: LocationType | null;

  @ApiProperty()
  @Column()
  name: string;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  unit: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  streetAddress: string | null;

  @ApiProperty()
  @Column()
  city: string;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  state: string | null;

  /** @deprecated Use `postalCode` instead */
  @ApiPropertyOptional({ deprecated: true })
  @Column({ nullable: true })
  zipCode: string | null;

  @ApiPropertyOptional()
  @Column({ nullable: true })
  postalCode: string | null;

  @ApiProperty({ type: "string", default: "America/Toronto" })
  @Column({ default: "America/Toronto" })
  timezone: string;
}

@Entity()
@Unique("UNIQUE_LOCATION_NAME_COUNTRY", ["name", "state", "country"])
export class Location extends SimplifiedLocation {
  @ApiProperty({ type: () => SimplifiedCountry })
  @ManyToOne((type) => Country, (country) => country.locations, {
    nullable: false,
    onDelete: "CASCADE"
  })
  country: Country;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.createdLocations, {
    nullable: true,
    onDelete: "SET NULL"
  })
  createdBy: User | null;

  @ApiPropertyOptional({ type: () => SimplifiedUser })
  @ManyToOne((type) => User, (user) => user.lastEditedLocations, {
    nullable: true,
    onDelete: "SET NULL"
  })
  lastEditedBy: User | null;

  // Hidden fields
  @OneToMany((type) => Shipment, (shipment) => shipment.portOfDischarge)
  portOfDischargeShipments: Promise<Array<Shipment>>;

  @OneToMany((type) => Shipment, (shipment) => shipment.portOfLoading)
  portOfLoadingShipments: Promise<Array<Shipment>>;

  @OneToMany((type) => Shipment, (shipment) => shipment.placeOfDelivery)
  placeOfDeliveryShipments: Promise<Array<Shipment>>;

  @OneToMany((type) => Port, (port) => port.location)
  ports: Promise<Array<Port>>;
}
