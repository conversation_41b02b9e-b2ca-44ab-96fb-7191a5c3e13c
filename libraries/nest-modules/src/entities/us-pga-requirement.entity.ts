import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, OneToMany, Index, Unique } from "typeorm";
import { SimplifiedBaseEntity } from "./base.entity";
import { UsTariffPgaRequirement } from "./us-tariff-pga-requirement.entity";

@Entity()
@Index("IDX_US_PGA_REQUIREMENT_TARIFF_FLAG_CODE", ["tariffFlagCode"])
@Unique("UNIQUE_TARIFF_FLAG_CODE", ["tariffFlagCode"])
export class UsPgaRequirement extends SimplifiedBaseEntity {
  @ApiProperty({ type: "string", maxLength: 3 })
  @Column({ length: 3 })
  tariffFlagCode: string; // e.g., "FD1"

  @ApiProperty({ type: "string", maxLength: 3 })
  @Column({ length: 3 })
  agencyCode: string; // e.g., "FDA"

  @ApiProperty({ type: "string", enum: ["R", "M"] })
  @Column({ length: 1 })
  requirementLevel: "R" | "M";

  @ApiPropertyOptional({ type: [String], description: "Program codes applicable to this requirement" })
  @Column({ type: "text", array: true, nullable: true })
  programCode: string[] | null; // e.g., ["BIO"] or ["PS1", "PS2", "PS3"]

  @ApiProperty({ type: "string" })
  @Column({ type: "text" })
  description: string; // e.g., "FDA data may be required 801(a)"

  // Note: No direct TypeORM relationship since UsTariffPgaRequirement uses tariffFlagCode strings
  // Use UsTariffService.getTariffsByPgaRequirement(tariffFlagCode) to get related tariffs
  // tariffRelations: Promise<Array<UsTariffPgaRequirement>>;
}
