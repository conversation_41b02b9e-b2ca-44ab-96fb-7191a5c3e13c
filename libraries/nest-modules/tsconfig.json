{"compilerOptions": {"experimentalDecorators": true, "target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "noImplicitAny": false, "strictNullChecks": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "emitDecoratorMetadata": true, "strictBindCallApply": false, "skipLibCheck": true}, "include": ["src"], "exclude": ["node_modules", "dist"], "watchOptions": {"excludeDirectories": ["dist", "node_modules"]}}