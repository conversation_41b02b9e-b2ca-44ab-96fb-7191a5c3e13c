import react from "@vitejs/plugin-react";
import { resolve } from "path";
import tailwindcss from "tailwindcss";
import { defineConfig } from "vite";
import dts from "vite-plugin-dts";
import { libInjectCss } from "vite-plugin-lib-inject-css";

// https://vitejs.dev/config/
export default defineConfig({
  optimizeDeps: {
    include: ["utils"]
  },
  build: {
    lib: {
      entry: resolve(__dirname, "./src/index.ts"),
      name: "lib",
      // fileName: (format) => `index.${format}.js`,
      formats: ["es"]
    },
    rollupOptions: {
      // Packages that also used in the frontend app
      external: [
        "react",
        "react/jsx-runtime",
        "react-dom",
        "tailwindcss",
        "tailwind-merge",
        "react-select",
        "lucide-react",
        "react-phone-number-input",
        "utils"
      ],
      output: {
        globals: {
          react: "React",
          "react-dom": "ReactDOM",
          tailwindcss: "tailwindcss"
        }
      }
    },
    sourcemap: process.env.NODE_ENV === "development" ? true : false,
    // sourcemap: true,
    emptyOutDir: true,
    copyPublicDir: false
  },
  plugins: [react(), libInjectCss(), dts({ include: ["src"], rollupTypes: true })],
  css: {
    postcss: {
      plugins: [tailwindcss]
    }
  }
});
