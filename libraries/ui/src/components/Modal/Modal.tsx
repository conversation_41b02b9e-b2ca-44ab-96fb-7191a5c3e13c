import { PropsWithChildren, ReactNode, useEffect, useMemo, useRef } from "react";
import { twMerge } from "tailwind-merge";
import { Icons } from "..";

interface Props extends PropsWithChildren {
  id: string;
  show?: boolean;
  title?: ReactNode;
  footer?: ReactNode;
  actions?: ReactNode;
  size?: "xs" | "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "5xl" | "6xl" | "7xl";
  backgroundStyle?: string;
  containerStyle?: string;
  headerStyle?: string;
  bodyStyle?: string;
  actionStyle?: string;
  fullscreen?: boolean;
  onClose(): void;
}
function Modal({
  id,
  show,
  title,
  footer,
  actions,
  size,
  backgroundStyle,
  containerStyle,
  headerStyle,
  actionStyle,
  bodyStyle,
  fullscreen = false,
  onClose,
  children
}: Props) {
  const modalRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (modalRef.current && show) {
      modalRef.current.focus();
    }
  }, [show]);

  const maxWidth = useMemo(
    () =>
      ({
        xs: "max-w-xs",
        sm: "max-w-sm",
        md: "max-w-md",
        lg: "max-w-lg",
        xl: "max-w-xl",
        "2xl": "max-w-2xl",
        "3xl": "max-w-3xl",
        "4xl": "max-w-4xl",
        "5xl": "max-w-5xl",
        "6xl": "max-w-6xl",
        "7xl": "max-w-7xl"
      })[size ?? "sm"],
    [size]
  );

  return (
    show && (
      <div
        className={twMerge(
          "flex py-10 overflow-hidden animate-fade-in fixed items-center justify-center inset-0 z-[70] outline-none focus:outline-none !bg-opacity-20 bg-neutral-900 dark:bg-neutral-100",
          backgroundStyle,
          fullscreen && "py-0"
        )}
        onClick={onClose}
      >
        <div
          id={id}
          ref={modalRef}
          className={twMerge(
            `flex flex-col max-h-[80vh] w-full animate-slide-down z-[70] rounded-md bg-white border-e shadow-md dark:bg-gray-900 dark:border-neutral-700 focus-visible:outline-none`,
            !fullscreen && maxWidth,
            fullscreen && "max-h-full h-full",
            containerStyle
          )}
          tabIndex={-1}
          onClick={(e) => {
            // do not close modal if anything inside modal content is clicked
            e.stopPropagation();
          }}
        >
          <header
            className={twMerge(
              "flex items-center py-3 px-4 bg-neutral-10 border-b min-h-16 dark:border-neutral-700 dark:bg-grey-dark",
              headerStyle
            )}
          >
            {title && typeof title === "string" ? <h5>{title}</h5> : title}
            {actions ? (
              <div className={twMerge("flex items-center gap-3 ml-auto", actionStyle)}>{actions}</div>
            ) : (
              <button
                type="button"
                className="flex justify-center items-center size-7 ml-auto text-sm font-semibold rounded-full text-neutral-800 hover:bg-neutral-100 dark:text-white dark:hover:bg-neutral-700 dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-neutral-600"
                onClick={onClose}
              >
                <span className="sr-only">Close modal</span>
                <Icons.Close className="size-4" />
              </button>
            )}
          </header>

          <main className={twMerge("overflow-y-auto pt-3 pb-4 px-4", bodyStyle)}>{children}</main>

          {footer && <footer>{footer}</footer>}
        </div>
      </div>
    )
  );
}
export default Modal;
