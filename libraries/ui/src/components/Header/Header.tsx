import { PropsWithChildren, ReactNode } from "react";
import { twMerge } from "tailwind-merge";

interface Props extends PropsWithChildren {
  title: ReactNode;
  containerStyle?: string;
  childStyle?: string;
}
function Header({ title, containerStyle, children, childStyle }: Props) {
  return (
    <header
      role="banner"
      aria-label={typeof title === "string" ? title : ""}
      className={twMerge("flex items-center px-6 py-3 min-h-16 bg-white dark:bg-transparent", containerStyle)}
    >
      {typeof title === "string" ? <h4>{title}</h4> : title}
      <div className={twMerge("flex items-center gap-3 ml-auto", childStyle)}>{children}</div>
    </header>
  );
}
export default Header;
