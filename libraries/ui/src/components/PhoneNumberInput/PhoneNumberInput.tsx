import { forwardRef } from "react";
import PhoneInput, { Country } from "react-phone-number-input";
import Input from "../Input";
import { InputProps } from "../Input/Input";
import "./PhoneNumber.css";
import "react-phone-number-input/style.css";

type PhoneNumberInputProps = InputProps & {
  defaultCountry?: Country;
  value?: string;
  onChange: (value?: string) => void;
};

const InputContainer = forwardRef<HTMLInputElement | null, InputProps>(({ className: _, ...props }, ref) => (
  <Input ref={ref} label={props.label || "Phone Number"} {...props} />
));

function PhoneNumberInput({ defaultCountry = "CA", ...props }: PhoneNumberInputProps) {
  return (
    <PhoneInput
      defaultCountry={defaultCountry}
      international
      countryCallingCodeEditable={false}
      inputComponent={InputContainer}
      {...props}
    />
  );
}

export default PhoneNumberInput;
