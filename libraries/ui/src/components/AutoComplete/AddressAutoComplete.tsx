import { InputHTMLAttributes } from "react";
import { usePlacesWidget } from "react-google-autocomplete";
import Input from "../Input";

type Props = InputHTMLAttributes<HTMLInputElement> & {
  apiKey: string;
  label?: string;
  error?: string;
  containerStyle?: string;
  onSelected(address: google.maps.places.PlaceResult): void;
};
function AddressAutoComplete({ label, error, onSelected, containerStyle, apiKey, ...props }: Props) {
  const { ref } = usePlacesWidget({
    apiKey,
    onPlaceSelected: (place) => {
      onSelected(place);
    },
    options: {
      fields: [
        "name",
        "address_components"
        // address = route
        // city =  "locality"
        // state = "administrative_area_level_1"
        // country
        // zip = "postal_code"
        // "geometry.location",
        // "formatted_address",
      ],
      types: ["address"]
    }
  });

  return <Input ref={ref} {...props} label={label} error={error} containerStyle={containerStyle} />;
}
export default AddressAutoComplete;
