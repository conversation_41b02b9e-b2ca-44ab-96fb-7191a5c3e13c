import { forwardRef, HTMLProps, useId } from "react";
import { twMerge } from "tailwind-merge";

interface ItemProps {
  children: React.ReactNode;
  active: boolean;
}
const AutoCompleteItem = forwardRef<HTMLDivElement, ItemProps & HTMLProps<HTMLDivElement>>(
  ({ children, active, ...rest }, ref) => {
    const id = useId();
    return (
      <div
        ref={ref}
        role="option"
        id={id}
        aria-selected={active}
        className={twMerge(
          "text-xs ps-2 pe-2 py-2 hover:cursor-pointer hover:bg-primary-800 hover:text-white",
          active && "bg-primary-800 text-white"
        )}
        {...rest}
      >
        {children}
      </div>
    );
  }
);
export default AutoCompleteItem;
