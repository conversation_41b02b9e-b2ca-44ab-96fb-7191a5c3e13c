import {
  FloatingFocusManager,
  FloatingPortal,
  autoUpdate,
  flip,
  size,
  useDismiss,
  useFloating,
  useInteractions,
  useListNavigation,
  useRole
} from "@floating-ui/react";
import { ChangeEvent, InputHTMLAttributes, useEffect, useRef, useState } from "react";
import { SelectOption } from "utils";
import { AutoCompleteItem, Icons, Input } from "..";
import { Chip } from "../ChipInput";

type Props<T extends SelectOption = SelectOption> = Omit<
  InputHTMLAttributes<HTMLInputElement>,
  "value" | "onSelect"
> & {
  label?: string;
  error?: string;
  containerStyle?: string;
  items: T[];
  values?: T[];
  onSearch: (search: string) => void;
  onSelect: (options: T[]) => void;
  loading?: boolean;
  hasMore?: boolean;
  onLoadMore?: () => void;
};

function MultiValueAutoComplete<T extends SelectOption = SelectOption>({
  label,
  name,
  placeholder,
  containerStyle,
  items,
  values = [],
  onSearch,
  onSelect,
  loading,
  error,
  hasMore,
  onLoadMore,
  ...props
}: Props<T>) {
  const [open, setOpen] = useState(false);
  const [localInputValue, setLocalInputValue] = useState("");
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const [selectedValues, setSelectedValues] = useState<T[]>(values);

  const listRef = useRef<Array<HTMLElement | null>>([]);

  const { refs, floatingStyles, context } = useFloating<HTMLInputElement>({
    whileElementsMounted: autoUpdate,
    open,
    onOpenChange: setOpen,
    middleware: [
      flip({ padding: 10 }),
      size({
        apply({ rects, availableHeight, elements }) {
          Object.assign(elements.floating.style, {
            width: `${rects.reference.width}px`,
            maxHeight: `${availableHeight}px`
          });
        },
        padding: 10
      })
    ]
  });

  const role = useRole(context, { role: "listbox" });
  const dismiss = useDismiss(context);
  const listNav = useListNavigation(context, {
    listRef,
    activeIndex,
    onNavigate: setActiveIndex,
    virtual: true,
    loop: true
  });

  const { getReferenceProps, getFloatingProps, getItemProps } = useInteractions([role, dismiss, listNav]);

  useEffect(() => {
    setSelectedValues(values);
  }, [values]);

  function onChange(event: ChangeEvent<HTMLInputElement>) {
    const newValue = event.target.value;
    setLocalInputValue(newValue);
    onSearch(newValue);
    setOpen(true);
    setActiveIndex(0);
  }

  const handleInputClick = () => {
    setOpen(true);
    if (!localInputValue) {
      onSearch("");
    }
  };

  const handleSelect = (item: T) => {
    const isAlreadySelected = selectedValues.some((v) => v.value === item.value);
    if (!isAlreadySelected) {
      const newValues = [...selectedValues, item];
      setSelectedValues(newValues);
      onSelect(newValues);
    }
    setLocalInputValue("");
    onSearch("");
    setOpen(false);
    refs.domReference.current?.focus();
  };

  const handleRemoveValue = (valueToRemove: T) => {
    const newValues = selectedValues.filter((v) => v.value !== valueToRemove.value);
    setSelectedValues(newValues);
    onSelect(newValues);
  };

  // Handle scroll for infinite loading
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = Math.round(e.currentTarget.scrollTop);
    const scrollHeight = e.currentTarget.scrollHeight;
    const clientHeight = e.currentTarget.clientHeight;

    const bottom = scrollHeight - scrollTop - clientHeight <= 1;
    if (bottom && hasMore && !loading && onLoadMore) {
      onLoadMore();
    }
  };

  return (
    <div className={containerStyle}>
      <div className="flex flex-col gap-1">
        {label && (
          <label htmlFor={name} className="block text-xs font-medium dark:text-white">
            {label}
          </label>
        )}
        <div className="relative">
          <div className="flex flex-wrap items-center min-h-[2.5rem] p-1 bg-grey-light rounded-lg dark:bg-transparent dark:border dark:border-neutral-600">
            {selectedValues.map((value) => (
              <Chip key={value.value} label={value.label} onRemove={() => handleRemoveValue(value)} />
            ))}
            <Input
              {...props}
              containerStyle="flex-1 min-w-[8rem]"
              inputStyle="!border-none !shadow-none !ring-0 !bg-transparent"
              {...getReferenceProps({
                ref: refs.setReference,
                onChange,
                value: localInputValue,
                placeholder: selectedValues.length === 0 ? placeholder : "",
                name,
                "aria-autocomplete": "list",
                onClick: handleInputClick,
                onKeyDown(event) {
                  if (event.key === "Enter" && activeIndex != null && items[activeIndex]) {
                    handleSelect(items[activeIndex]);
                  }
                }
              })}
            />
          </div>
          {error && <div className="mt-1 text-xs text-red-600">{error}</div>}
        </div>
      </div>

      <FloatingPortal>
        {open && (
          <FloatingFocusManager context={context} initialFocus={-1} visuallyHiddenDismiss>
            <div
              {...getFloatingProps({
                ref: refs.setFloating,
                style: {
                  ...floatingStyles,
                  background: "white",
                  color: "black",
                  overflowY: "auto",
                  maxHeight: "300px",
                  border: "1px solid #e2e8f0",
                  borderRadius: "0.375rem",
                  boxShadow: "0 4px 6px -1px rgb(0 0 0 / 0.1)",
                  zIndex: 1000
                },
                onScroll: handleScroll
              })}
            >
              {items
                .filter((item) => !selectedValues.some((v) => v.value === item.value))
                .map((item, index) => (
                  <AutoCompleteItem
                    key={item.value}
                    {...getItemProps({
                      ref(node) {
                        listRef.current[index] = node;
                      },
                      onClick() {
                        handleSelect(item);
                      }
                    })}
                    active={activeIndex === index}
                  >
                    {item.label}
                  </AutoCompleteItem>
                ))}
              {loading && (
                <div className="py-1">
                  <Icons.Loader />
                </div>
              )}
            </div>
          </FloatingFocusManager>
        )}
      </FloatingPortal>
    </div>
  );
}

export default MultiValueAutoComplete;
