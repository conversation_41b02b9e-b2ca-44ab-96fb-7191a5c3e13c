import {
  FloatingFocusManager,
  FloatingPortal,
  autoUpdate,
  flip,
  size,
  useDismiss,
  useFloating,
  useInteractions,
  useListNavigation,
  useRole
} from "@floating-ui/react";
import { XCircleIcon } from "lucide-react";
import { ChangeEvent, InputHTMLAttributes, useEffect, useRef, useState } from "react";
import { SelectOption } from "utils";
import { AutoCompleteItem, Icons, Input } from "../";

type Props<T extends SelectOption = SelectOption> = Omit<
  InputHTMLAttributes<HTMLInputElement>,
  "value" | "onSelect"
> & {
  label?: string;
  error?: string;
  containerStyle?: string;
  items: T[];
  value?: T;
  onSearch: (search: string) => void;
  onSelect: (option: T) => void;
  onClear?: () => void;
  loading?: boolean;
  hasMore?: boolean;
  onLoadMore?: () => void;
};

function AutoComplete<T extends SelectOption = SelectOption>({
  label,
  name,
  placeholder,
  containerStyle,
  items,
  value,
  onSearch,
  onSelect,
  onClear,
  loading,
  error,
  hasMore,
  onLoadMore
}: Props<T>) {
  const [open, setOpen] = useState(false);
  const [localInputValue, setLocalInputValue] = useState("");
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const listRef = useRef<Array<HTMLElement | null>>([]);

  const { refs, floatingStyles, context } = useFloating<HTMLInputElement>({
    whileElementsMounted: autoUpdate,
    open,
    onOpenChange: setOpen,
    middleware: [
      flip({ padding: 10 }),
      size({
        apply({ rects, availableHeight, elements }) {
          Object.assign(elements.floating.style, {
            width: `${rects.reference.width}px`,
            maxHeight: `${availableHeight}px`
          });
        },
        padding: 10
      })
    ]
  });

  const role = useRole(context, { role: "listbox" });
  const dismiss = useDismiss(context);
  const listNav = useListNavigation(context, {
    listRef,
    activeIndex,
    onNavigate: setActiveIndex,
    virtual: true,
    loop: true
  });

  const { getReferenceProps, getFloatingProps, getItemProps } = useInteractions([role, dismiss, listNav]);

  // Update input value when controlled value changes
  useEffect(() => {
    if (value?.label) {
      setLocalInputValue(value.label);
    }
  }, [value?.label]);

  function onChange(event: ChangeEvent<HTMLInputElement>) {
    const newValue = event.target.value;
    setLocalInputValue(newValue);
    onSearch(newValue);
    setOpen(true);
    setActiveIndex(0);
  }

  const handleInputClick = () => {
    setOpen(true);
    if (!localInputValue) {
      onSearch("");
    }
  };

  const handleSelect = (item: T) => {
    onSelect(item);
    setOpen(false);
    refs.domReference.current?.focus();
  };

  // Handle scroll for infinite loading
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = Math.round(e.currentTarget.scrollTop);
    const scrollHeight = e.currentTarget.scrollHeight;
    const clientHeight = e.currentTarget.clientHeight;

    const bottom = scrollHeight - scrollTop - clientHeight <= 1;
    if (bottom && hasMore && !loading && onLoadMore) {
      onLoadMore();
    }
  };

  const handleClear = () => {
    setLocalInputValue("");
    onSearch("");
    if (onClear) {
      onClear();
    }
  };

  return (
    <div className={containerStyle}>
      <Input
        autoComplete="off"
        error={error}
        icon={
          localInputValue && onClear ? (
            <button type="button" onClick={handleClear} className="text-gray-400 hover:text-gray-600">
              <XCircleIcon className="size-4" />
            </button>
          ) : undefined
        }
        {...getReferenceProps({
          ref: refs.setReference,
          onChange,
          value: localInputValue,
          placeholder,
          name,
          label,
          "aria-autocomplete": "list",
          onClick: handleInputClick,
          onKeyDown(event) {
            if (event.key === "Enter" && activeIndex != null && items[activeIndex]) {
              handleSelect(items[activeIndex]);
            }
          }
        })}
      />

      <FloatingPortal>
        {open && (
          <FloatingFocusManager context={context} initialFocus={-1} visuallyHiddenDismiss>
            <div
              {...getFloatingProps({
                ref: refs.setFloating,
                style: {
                  ...floatingStyles,
                  background: "white",
                  color: "black",
                  overflowY: "auto",
                  maxHeight: "300px",
                  border: "1px solid #e2e8f0",
                  borderRadius: "0.375rem",
                  boxShadow: "0 4px 6px -1px rgb(0 0 0 / 0.1)",
                  zIndex: 1000
                },
                onScroll: handleScroll
              })}
            >
              {items.map((item, index) => (
                <AutoCompleteItem
                  key={item.value}
                  {...getItemProps({
                    ref(node) {
                      listRef.current[index] = node;
                    },
                    onClick() {
                      handleSelect(item);
                    }
                  })}
                  active={activeIndex === index}
                >
                  {item.label}
                </AutoCompleteItem>
              ))}
              {loading && (
                <div className="py-1">
                  <Icons.Loader />
                </div>
              )}
            </div>
          </FloatingFocusManager>
        )}
      </FloatingPortal>
    </div>
  );
}
export default AutoComplete;
