import { Check, X } from "lucide-react";
import { ComponentType, isValidElement, ReactNode } from "react";
import {
  Currency,
  formatCurrency,
  formatDate,
  formatNumber,
  kebabCaseToCapitalize,
  pascalToCapitalize,
  TableRenderer
} from "utils";

//#region Predefined renderers
function numberRenderer(value?: number) {
  if (value !== undefined && value !== null && !isNaN(value)) {
    return formatNumber(value);
  }
}
function numberDecimalRenderer(value?: number) {
  if (value !== undefined && value !== null && !isNaN(value)) {
    return formatNumber(value, 2);
  }
}
function currencyRenderer(value?: number, currency?: Currency) {
  if (value !== undefined && value !== null && !isNaN(value)) {
    return formatCurrency(value, currency);
  }
}
function percentRenderer(value?: number) {
  if (value !== undefined && value !== null && !isNaN(value)) {
    return formatNumber(value, undefined, "percent");
  }
}
function dateRenderer(value: string) {
  if (value) {
    return formatDate(new Date(value), "P");
  }
}
function dateLocalRenderer(value: string) {
  if (value) {
    return formatDate(new Date(value), "P", true);
  }
}
function dateTimeRenderer(value: string) {
  if (value) {
    return formatDate(new Date(value), "Pp");
  }
}
function dateTimeLocalRenderer(value: string) {
  if (value) {
    return formatDate(new Date(value), "Pp", true);
  }
}
function kebabCaseRenderer(value: string) {
  if (value) {
    return kebabCaseToCapitalize(value);
  }
}
function pascalCaseRenderer(value: string) {
  if (value) {
    return pascalToCapitalize(value);
  }
}
function booleanRenderer(value: unknown) {
  if (typeof value === "boolean") {
    if (value) return <Check className="size-5 text-success" />;
    else return <X className="size-5 text-danger" />;
  }
}

type Renderer = Record<string, (val: never) => ReactNode>;
const renderers: Renderer = {
  number: numberRenderer,
  numberDecimal: numberDecimalRenderer,
  currency: currencyRenderer,
  percent: percentRenderer,
  date: dateRenderer,
  dateLocal: dateLocalRenderer,
  dateTime: dateTimeRenderer,
  dateTimeLocal: dateTimeLocalRenderer,
  kebabCase: kebabCaseRenderer,
  pascalCase: pascalCaseRenderer,
  boolean: booleanRenderer
};
//#endregion

const rendererHandler = <T extends object, V extends ReactNode = ReactNode>(
  renderer?: TableRenderer,
  value?: V,
  row?: T
): ReactNode => {
  if (renderer && value !== undefined) {
    // If renderer is a string, look for predefined renderers
    if (typeof renderer === "string") {
      const selector = renderers[renderer];
      if (selector) return selector(value as never);
    }
    // If renderer is a react component, return it for React to render
    if (isValidElement(renderer) || typeof renderer === "object" || typeof renderer === "function") {
      const CustomComponent = renderer as ComponentType<{
        value: V;
        row?: T;
      }>;
      return <CustomComponent value={value} row={row} />;
    }

    // Return raw value if no renderer found
    return value;
  }
  // Fallback if renderer or value is undefined
  return value;
};

export default rendererHandler;
