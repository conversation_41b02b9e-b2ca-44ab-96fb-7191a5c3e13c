import { LoaderCircle } from "lucide-react";
import { useEffect, useRef } from "react";

type Props = {
  isFetchingNextPage?: boolean;
  hasNextPage: boolean;
  onEndReached?(): void;
};
function InfiniteTable({ isFetchingNextPage, hasNextPage, onEndReached }: Props) {
  const loadMoreRef = useRef<HTMLTableRowElement>(null);
  useEffect(() => {
    const ref = loadMoreRef.current;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasNextPage) {
          onEndReached && onEndReached();
        }
      },
      { threshold: 1.0 }
    );

    if (ref) {
      observer.observe(ref);
    }

    return () => {
      if (ref) {
        observer.unobserve(ref);
      }
    };
  }, [hasNextPage, onEndReached]);

  return (
    <>
      {isFetchingNextPage && (
        <tr>
          <td colSpan={3}>
            <LoaderCircle className="m-auto size-8 animate-spin text-primary" />
          </td>
        </tr>
      )}
      {hasNextPage && (
        <tr>
          <td>
            <span ref={loadMoreRef} />
          </td>
        </tr>
      )}
    </>
  );
}
export default InfiniteTable;
