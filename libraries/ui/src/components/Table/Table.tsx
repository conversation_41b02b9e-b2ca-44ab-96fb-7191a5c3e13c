import { ChevronDown, ChevronUp } from "lucide-react";
import { ReactNode, useMemo, useRef, useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";
import { SortOrder, TableSchema } from "utils";
import Checkbox from "../Checkbox";
import Icons from "../Icons";
import Input from "../Input";
import InfiniteTable from "./Table.infinite";
import TableRow from "./TableRow";

interface RowProps {
  rowStyle?: string;
}
type Props<T extends object> = {
  data?: (T & RowProps)[];
  schema: TableSchema<T>;
  className?: string;
  isLoading?: boolean;
  checklist?: boolean;
  selected?: T[];
  multiple?: boolean;
  selectAll?: boolean;
  header?: ReactNode;
  footer?: ReactNode; // footer for pagination
  summary?: ReactNode; // footer for each column
  total?: number; // total number of rows
  emptyMessage?: string;
  isFetchingNextPage?: boolean;
  hasNextPage?: boolean;
  sortKey?: string;
  sortDirection?: SortOrder;
  showTopScrollbar?: boolean; // show horizontal scrollbar at top
  onClick?(value: unknown): void;
  onSearch?(value: string): void;
  getRowStyle?: (row: T) => string;
  onEndReached?(): void;
  disableRow?: (row: T) => boolean;
  expandableRow?: (row: T) => ReactNode;
  onSort?: (key?: string, direction?: SortOrder) => void;
  onMultiSelect?: (selectedItems: T[]) => void;
};

function Table<T extends object>({
  data,
  schema,
  isLoading,
  checklist,
  multiple,
  selectAll,
  header,
  footer,
  summary,
  total,
  emptyMessage = "No Data",
  onClick,
  onSearch,
  isFetchingNextPage,
  hasNextPage,
  onEndReached,
  getRowStyle,
  disableRow,
  selected,
  expandableRow,
  className,
  onSort,
  sortKey,
  sortDirection,
  showTopScrollbar = false,
  onMultiSelect
}: Props<T>) {
  const columns = useMemo(() => Object.entries(schema).filter(([, column]) => column.visible), [schema]);
  const topScrollRef = useRef<HTMLDivElement>(null);
  const tableScrollRef = useRef<HTMLDivElement>(null);
  const [scrollbarWidth, setScrollbarWidth] = useState(0);

  // Sync scroll positions between top scrollbar and table
  useEffect(() => {
    const topScroll = topScrollRef.current;
    const tableScroll = tableScrollRef.current;

    if (!topScroll || !tableScroll || !showTopScrollbar) return;

    // Update scrollbar width to match table's scrollable width
    const updateScrollbarWidth = () => {
      const table = tableScroll.querySelector("table");
      if (table) {
        setScrollbarWidth(table.scrollWidth);
      }
    };

    // Initial width calculation
    updateScrollbarWidth();

    const handleTopScroll = () => {
      if (tableScroll.scrollLeft !== topScroll.scrollLeft) {
        tableScroll.scrollLeft = topScroll.scrollLeft;
      }
    };

    const handleTableScroll = () => {
      if (topScroll.scrollLeft !== tableScroll.scrollLeft) {
        topScroll.scrollLeft = tableScroll.scrollLeft;
      }
    };

    // Use ResizeObserver to watch for table size changes
    const resizeObserver = new ResizeObserver(updateScrollbarWidth);
    const table = tableScroll.querySelector("table");
    if (table) {
      resizeObserver.observe(table);
    }

    topScroll.addEventListener("scroll", handleTopScroll);
    tableScroll.addEventListener("scroll", handleTableScroll);

    return () => {
      topScroll.removeEventListener("scroll", handleTopScroll);
      tableScroll.removeEventListener("scroll", handleTableScroll);
      resizeObserver.disconnect();
    };
  }, [showTopScrollbar, data]);

  const handleSelection = (index: number) => {
    if (!data) return;
    if (multiple) {
      const item = data[index];
      const isSelected = selected?.includes(item);
      const newSelected = isSelected
        ? (selected || []).filter((i) => i !== item)
        : [...(selected || []), item];
      onMultiSelect?.(newSelected);
    } else {
      onMultiSelect?.([data[index]]);
    }
  };

  const handleSelectAll = () => {
    if (!data || data.length === 0) return;
    if (selected?.length === data.length) {
      onMultiSelect?.([]);
    } else {
      onMultiSelect?.(data);
    }
  };

  return (
    <div className={twMerge("flex flex-col", className)}>
      <div className="bg-white border border-gray-200 shadow-sm overflow-hidden dark:!bg-transparent dark:border-neutral-700">
        {/* <!-- Header --> */}
        {(header || onSearch) && (
          <div className="px-6 py-2 flex gap-3 items-center justify-between border-b border-gray-200 dark:border-neutral-700">
            {typeof header === "string" ? <span>{header}</span> : header}
            {onSearch && <Input name="search" placeholder="Search" kind="search" onTextChange={onSearch} />}
          </div>
        )}
        {/* <!-- End Header --> */}

        {/* <!-- Top Scrollbar --> */}
        {showTopScrollbar && (
          <div
            ref={topScrollRef}
            className="overflow-x-auto overflow-y-hidden h-auto border-b border-gray-200 dark:border-neutral-700"
          >
            <div style={{ width: scrollbarWidth || "100%", height: "1px" }}></div>
          </div>
        )}
        {/* <!-- End Top Scrollbar --> */}

        {/* <!-- Table --> */}
        <div ref={tableScrollRef} className={twMerge("overflow-x-auto", hasNextPage && "max-h-[31rem]")}>
          <table className="min-w-full text-light dark:bg-transparent dark:text-white">
            <colgroup>
              {checklist && <col />}
              {expandableRow && <col />}
              {columns.map(([key, col]) =>
                col.width ? <col key={key} style={{ width: `${col.width}px` }} /> : <col key={key} />
              )}
            </colgroup>

            <thead className="bg-neutral-10 dark:bg-gray-800 border-b border-neutral-200 dark:border-neutral-700">
              <tr>
                {checklist && (
                  <th scope="col" className="pl-4 pr-1 lg:pl-6 py-3 text-start">
                    {selectAll && data && data.length > 0 && (
                      <Checkbox
                        name="select-all-checkbox"
                        onChange={handleSelectAll}
                        checked={selected?.length === data.length || false}
                      />
                    )}
                  </th>
                )}
                {expandableRow && <th scope="col" className="pl-4 pr-1 lg:pl-6 py-3 text-start"></th>}
                {columns.map(([key, col]) => (
                  <th
                    scope="col"
                    className={twMerge(
                      "pl-4 pr-1 lg:pl-6 py-3 text-start",
                      onSort &&
                        !col.disableSort &&
                        "cursor-pointer hover:underline hover:bg-neutral-50 dark:hover:bg-gray-700"
                    )}
                    key={key}
                    onClick={() => {
                      if (col.disableSort || !onSort) return;
                      const newDirection =
                        sortKey !== key && sortKey !== col.sortKey
                          ? SortOrder.ASC
                          : sortDirection === SortOrder.ASC
                            ? SortOrder.DESC
                            : sortDirection === SortOrder.DESC
                              ? undefined
                              : SortOrder.ASC;
                      onSort(!newDirection ? undefined : (col.sortKey ?? key), newDirection);
                    }}
                  >
                    <div className="text-xs font-semibold tracking-wide flex items-center gap-1">
                      {col.header}
                      {onSort && !col.disableSort && (sortKey === key || sortKey === col.sortKey) && (
                        <span className="inline-flex flex-col">
                          {sortDirection === SortOrder.ASC && <ChevronUp className="size-3" />}
                          {sortDirection === SortOrder.DESC && <ChevronDown className="size-3" />}
                        </span>
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>

            <tbody className="divide-y divide-neutral-100 dark:divide-neutral-700">
              {!isLoading ? (
                data && data.length > 0 ? (
                  <>
                    {data.map((row, i) => {
                      const isDisabled = disableRow ? disableRow(row) : false;
                      const isSelected = selected ? selected.includes(row) : false;
                      return (
                        <TableRow
                          key={i}
                          row={row}
                          rowStyle={getRowStyle?.(row)}
                          index={i}
                          columns={columns}
                          checklist={checklist}
                          multiple={multiple}
                          isSelected={isSelected}
                          isDisabled={isDisabled}
                          onClick={onClick}
                          setInternalSelected={handleSelection}
                          expandableRow={expandableRow}
                        />
                      );
                    })}
                    {hasNextPage && (
                      <InfiniteTable
                        hasNextPage={hasNextPage}
                        isFetchingNextPage={isFetchingNextPage}
                        onEndReached={onEndReached}
                      />
                    )}
                  </>
                ) : (
                  <tr className="text-center">
                    <td className="p-2" colSpan={columns.length + (checklist ? 1 : 0)}>
                      {emptyMessage}
                    </td>
                  </tr>
                )
              ) : (
                <tr className="text-center">
                  <td className="p-2" colSpan={columns.length + (checklist ? 1 : 0)}>
                    <Icons.Loader />
                  </td>
                </tr>
              )}
            </tbody>
            {/* Summary row footer */}
            {summary && (
              <tfoot className="bg-neutral-10 dark:bg-gray-800">
                {typeof summary === "string" ? (
                  <tr>
                    <td colSpan={columns.length + (checklist ? 1 : 0)} className="px-6 py-2">
                      {summary}
                    </td>
                  </tr>
                ) : (
                  summary
                )}
              </tfoot>
            )}
          </table>
        </div>
        {/* <!-- End Table --> */}
        {/* <!-- Footer --> */}
        {footer && total !== undefined && total > 0 && (
          <div className="px-6 py-2 flex gap-3 items-center justify-between bg-neutral-10 dark:bg-gray-800 border-t border-gray-200 dark:border-neutral-700">
            {total !== undefined && (
              <span>
                {total} Record{total > 1 ? "s" : ""}
              </span>
            )}
            {footer}
          </div>
        )}
        {/* <!-- End Footer --> */}
      </div>
    </div>
  );
}
export default Table;
