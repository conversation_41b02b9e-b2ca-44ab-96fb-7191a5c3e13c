import { LucideMinusSquare, LucidePlusSquare } from "lucide-react";
import { ReactNode, Suspense, useMemo, useState } from "react";
import { twMerge } from "tailwind-merge";
import { TableSchema } from "utils";
import Checkbox from "../Checkbox";
import rendererHandler from "./Table.renderer";

interface TableRowProps<T extends object> {
  row: T & { rowStyle?: string };
  rowStyle?: string;
  index: number;
  columns: [string, TableSchema<T>[keyof TableSchema<T>]][];
  checklist?: boolean;
  multiple?: boolean;
  isSelected: boolean;
  isDisabled: boolean;
  onClick?: (value: unknown) => void;
  setInternalSelected: (index: number) => void;
  expandableRow?: (row: T) => ReactNode;
}

function TableRow<T extends object>({
  row,
  rowStyle,
  index,
  columns,
  checklist,
  multiple,
  isSelected,
  isDisabled,
  onClick,
  setInternalSelected,
  expandableRow
}: TableRowProps<T>) {
  const [expanded, setExpanded] = useState(false);

  const columnCount = useMemo(
    () => columns.length + (checklist ? 1 : 0) + (expandableRow ? 1 : 0),
    [columns, checklist, expandableRow]
  );

  const ToggleIcon = useMemo(() => (expanded ? LucideMinusSquare : LucidePlusSquare), [expanded]);

  return (
    <>
      <tr
        className={twMerge(
          `text-sm hover:bg-neutral-100 dark:hover:!bg-neutral-700`,
          row?.rowStyle,
          rowStyle,
          isDisabled && "opacity-60 cursor-not-allowed",
          onClick && "cursor-pointer"
        )}
        key={index}
        {...(onClick &&
          !isDisabled && {
            onClick: () => {
              onClick(row);
              if (checklist && !multiple) setInternalSelected(index);
            }
          })}
      >
        {checklist && (
          <td
            className="size-px whitespace-nowrap align-top pl-4 pr-1 lg:pl-6 py-3"
            onClick={(e) => {
              if (multiple) {
                e.stopPropagation();
                setInternalSelected(index);
              }
            }}
          >
            <Checkbox
              name={`checkbox-${index}`}
              checked={isSelected}
              onChange={() => null}
              disabled={isDisabled}
            />
          </td>
        )}
        {expandableRow && (
          <td className="size-px whitespace-nowrap align-top">
            <div className="pl-4 pr-1 lg:pl-6 py-3">
              <button
                onClick={() => setExpanded(!expanded)}
                className="w-4 h-4 text-neutral-800 dark:text-neutral-200 hover:text-primary"
              >
                <ToggleIcon className="size-4" />
              </button>
            </div>
          </td>
        )}
        {columns.map(([key, col], j) => (
          <td
            className={twMerge(
              "whitespace-nowrap pl-4 pr-1 lg:pl-6 py-3 align-top text-light dark:text-white",
              j == 0 && "font-medium",
              col?.style
            )}
            key={j}
          >
            <Suspense>
              {rendererHandler<T>(
                col?.renderer,
                (key in row ? row[key as keyof T] : undefined) as never,
                row
              )}
            </Suspense>
          </td>
        ))}
      </tr>
      {expandableRow && expanded && (
        <tr>
          <td colSpan={columnCount} className="px-4 lg:px-6 py-3 bg-neutral-10 dark:bg-gray-800">
            {expandableRow(row)}
          </td>
        </tr>
      )}
    </>
  );
}

export default TableRow;
