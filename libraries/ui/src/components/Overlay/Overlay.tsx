import { PropsWithChildren, ReactElement, useRef } from "react";
import { twMerge } from "tailwind-merge";
import { Icons } from "..";

type Props = PropsWithChildren & {
  id: string;
  title?: string;
  show?: boolean;
  containerStyle?: string;
  header?: ReactElement;
  closeButton?: boolean;
  onClose(): void;
};
function Overlay({ show, id, title, containerStyle, header, closeButton, children, onClose }: Props) {
  const modalRef = useRef<HTMLDivElement>(null);

  return (
    show && (
      <div
        className="flex justify-end overflow-x-hidden overflow-y-auto p-4 fixed inset-0 z-50 outline-none focus:outline-none !bg-opacity-20 bg-neutral-900 dark:bg-neutral-100"
        onClick={onClose}
      >
        <aside
          id={id}
          ref={modalRef}
          className={twMerge(
            "flex flex-col h-full max-w-sm w-full z-[60] animate-sheet-in bg-white border-e shadow-md rounded-md dark:bg-neutral-900 dark:border-neutral-700 focus-visible:outline-none",
            containerStyle
          )}
          tabIndex={-1}
          onClick={(e) => {
            // do not close modal if anything inside modal content is clicked
            e.stopPropagation();
          }}
        >
          <header className="flex gap-3 items-center p-4 border-b bg-neutral-50 dark:bg-neutral-800 dark:border-neutral-700">
            {title && <h4 className="font-bold">{title}</h4>}
            {header}
            {closeButton && (
              <button
                type="button"
                className="flex justify-center items-center size-7 text-sm font-semibold rounded-full ml-auto text-neutral-800 hover:bg-neutral-100 dark:text-white dark:hover:!bg-neutral-800 dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-neutral-600"
                onClick={onClose}
              >
                <span className="sr-only">Close side menu</span>
                <Icons.Close className="size-4" />
              </button>
            )}
          </header>
          <div className="h-full overflow-y-auto relative">{children}</div>
        </aside>
      </div>
    )
  );
}
export default Overlay;
