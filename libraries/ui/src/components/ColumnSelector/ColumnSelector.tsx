import { useCallback } from "react";
import { TableSchema } from "utils";
import Button from "../Button";
import Checkbox from "../Checkbox";
import Dropdown from "../Dropdown";

interface ColumnSelectorProps<T> {
  columns: TableSchema<T>;
  containerStyle?: string;
  onToggleColumn: (columns: TableSchema<T>) => void;
}

function ColumnSelector<T>({ columns, containerStyle, onToggleColumn }: ColumnSelectorProps<T>) {
  const toggleColumn = useCallback(
    (columnKey: string) => {
      const updatedColumns = {
        ...columns,
        [columnKey]: {
          ...columns[columnKey as keyof TableSchema<T>],
          visible: !columns[columnKey as keyof TableSchema<T>]?.visible
        }
      } as TableSchema<T>;
      onToggleColumn(updatedColumns);
    },
    [onToggleColumn, columns]
  );

  return (
    <Dropdown
      triggerElement={<Button label="Fields" kind="fields" />}
      placement="bottom-end"
      dropdownStyle="min-w-max"
      className={containerStyle}
    >
      <div className="p-3 flex flex-col gap-1 rounded dark:bg-neutral-900">
        {Object.entries(columns).map(([key, column]) => (
          <Checkbox
            key={key}
            label={column.header}
            name={key}
            checked={column.visible}
            onChange={() => toggleColumn(key)}
            disabled={column.readOnly}
          />
        ))}
      </div>
    </Dropdown>
  );
}
export default ColumnSelector;
