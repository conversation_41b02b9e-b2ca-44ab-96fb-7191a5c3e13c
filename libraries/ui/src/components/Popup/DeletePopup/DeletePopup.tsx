import { <PERSON>Alert } from "lucide-react";
import { ReactNode } from "react";
import Button from "../../Button";
import Modal from "../../Modal";

interface Props {
  show: boolean;
  content?: ReactNode;
  proceedText?: string;
  cancelText?: string;
  onProceed(): void;
  onCancel(): void;
}
function DeletePopup({
  show,
  content,
  proceedText = "Yes, I'm sure",
  cancelText = "No, cancel",
  onProceed,
  onCancel
}: Props) {
  return (
    <Modal
      id="delete-popup"
      show={show}
      onClose={onCancel}
      containerStyle="z-[9999]"
      backgroundStyle="z-[9999]"
      footer={
        <div className="flex items-center justify-center gap-6 pb-4">
          <Button kind="cancel" label={cancelText} onClick={onCancel} />
          <Button kind="delete" label={proceedText} onClick={onProceed} />
        </div>
      }
    >
      <div className="flex flex-col gap-4 text-center">
        <TriangleAlert className="mx-auto size-14 text-danger" />
        <div>{content}</div>
      </div>
    </Modal>
  );
}
export default DeletePopup;
