import { PropsWithChildren, createContext, useState } from "react";
import ConfirmPopup from "./ConfirmPopup";
import DeletePopup from "./DeletePopup";
import { PopupProps, PopupType } from "./types";

const POPUP_COMPONENTS = {
  [PopupType.CONFIRM_POPUP]: ConfirmPopup,
  [PopupType.DELETE_POPUP]: DeletePopup
};
type ContextType = {
  showPopup: (popupProps: PopupProps, popupType?: PopupType) => void;
  hidePopup: () => void;
};
export const PopupContext = createContext({} as ContextType);

export const PopupProvider = ({ children }: PropsWithChildren) => {
  const [show, setShow] = useState(false);
  const [popupType, setPopupType] = useState<PopupType>(PopupType.CONFIRM_POPUP);
  const [popup, setPopup] = useState<PopupProps>();

  const showPopup = (popupProps: PopupProps, popupType = PopupType.CONFIRM_POPUP) => {
    setShow(true);
    setPopupType(popupType);
    setPopup(popupProps);
  };

  const hidePopup = () => {
    setShow(false);
    setPopup(undefined);
  };

  const renderComponent = () => {
    if (!popup) return null;

    const ModalComponent = POPUP_COMPONENTS[popupType];

    return <ModalComponent show={show} {...popup} onCancel={hidePopup} />;
  };

  return (
    <PopupContext.Provider value={{ showPopup, hidePopup }}>
      {renderComponent()}
      {children}
    </PopupContext.Provider>
  );
};
