import { PropsWithChildren, useState } from "react";
import { twMerge } from "tailwind-merge";

interface Props extends PropsWithChildren {
  text?: string;
  position?: "left" | "right";
  fixed?: boolean;
  containerStyle?: string;
}
function Tooltip({ text, position = "left", containerStyle, children, fixed }: Props) {
  const [show, setShow] = useState(false);

  if (!text || text === "") return children;

  return (
    <div
      className={`relative ${containerStyle}`}
      onMouseOver={() => setShow(true)}
      onMouseOut={() => setShow(false)}
    >
      {children}
      <span
        className={twMerge(
          "transition-opacity absolute w-max py-1 px-2 bg-gray-900 text-xs font-medium text-white rounded shadow-sm opacity-0",
          "max-w-[360px] whitespace-normal break-words pointer-events-none z-[9999]",
          show && "opacity-90",
          fixed && "fixed",
          position === "left" && "right-6",
          position === "left" && fixed && "-translate-x-2 -translate-y-1/2",
          position === "right" && fixed && "translate-x-2 -translate-y-1/2"
        )}
        role="tooltip"
      >
        {text}
      </span>
    </div>
  );
}
export default Tooltip;
