import { ChevronDownIcon, ChevronUpIcon } from "lucide-react";
import { ReactNode, useCallback, useMemo, useState } from "react";
import { twMerge } from "tailwind-merge";

type CollapseItemProps = {
  key: string | number;
  title: ReactNode;
  children: ReactNode;
};

type CollapseItemComponentProps = {
  item: CollapseItemProps;
  isActive: boolean;
  onToggle: (key: React.Key) => void;
  headerStyle?: string;
  contentStyle?: string;
};

type CollapseProps = {
  items: CollapseItemProps[];
  activeKeys?: React.Key[];
  onChange?: (keys: React.Key[]) => void;
  headerStyle?: string;
  contentStyle?: string;
};

const CollapseItem = ({
  isActive,
  onToggle,
  item,
  headerStyle,
  contentStyle
}: CollapseItemComponentProps) => {
  return (
    <li>
      <div
        className={twMerge("flex items-center justify-between px-4 py-2", "hover:bg-primary/5", headerStyle)}
        onClick={() => onToggle?.(item.key)}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            onToggle?.(item.key);
          }
        }}
        role="button"
        tabIndex={0}
        aria-expanded={isActive}
      >
        <div id={`collapse-${item.key}-title`}>
          {typeof item.title === "object" ? (
            item.title
          ) : (
            <span className="font-medium !text-base">{item.title}</span>
          )}
        </div>
        <span>
          {isActive ? (
            <ChevronUpIcon className="size-4" aria-hidden />
          ) : (
            <ChevronDownIcon className="size-4" aria-hidden />
          )}
        </span>
      </div>
      <div
        className={twMerge("px-4 py-2", isActive ? "block" : "hidden", contentStyle)}
        role="region"
        aria-labelledby={`collapse-${item.key}-title`}
      >
        {item.children}
      </div>
    </li>
  );
};

const Collapse = ({ items, activeKeys = [], onChange, headerStyle, contentStyle }: CollapseProps) => {
  const [currentActiveKeys, setCurrentActiveKeys] = useState<React.Key[]>(activeKeys);

  const toggleActiveKey = useCallback(
    (key: React.Key) => {
      setCurrentActiveKeys((prev) => (prev.includes(key) ? prev.filter((k) => k !== key) : [...prev, key]));
      onChange?.(currentActiveKeys);
    },
    [currentActiveKeys, onChange]
  );

  const renderedItems = useMemo(() => {
    const isActive = (item: CollapseItemProps) => currentActiveKeys.includes(item.key);

    return items.map((item) => (
      <CollapseItem
        item={item}
        key={item.key}
        headerStyle={headerStyle}
        contentStyle={contentStyle}
        isActive={isActive(item)}
        onToggle={toggleActiveKey}
      />
    ));
  }, [contentStyle, currentActiveKeys, headerStyle, items, toggleActiveKey]);

  return <ul className="divide-y">{renderedItems}</ul>;
};

export default Collapse;
export type { CollapseItemProps };
