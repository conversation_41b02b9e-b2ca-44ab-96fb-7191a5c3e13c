import { cloneElement, ReactElement, ReactNode, useEffect, useState } from "react";
import { twMerge } from "tailwind-merge";

interface SegmentedControlItem {
  id: string;
  label: string;
  icon?: ReactNode;
}

type SegmentedControlSize = "sm" | "md" | "lg";

interface SegmentedControlProps {
  items: SegmentedControlItem[];
  value: string;
  onChange?: (value: string) => void;
  size?: SegmentedControlSize;
  className?: string;
  pillClassName?: string;
  buttonClassName?: string;
  activeButtonClassName?: string;
  inactiveButtonClassName?: string;
}

function SegmentedControl({
  items,
  value,
  onChange,
  size = "md",
  className,
  pillClassName,
  buttonClassName,
  activeButtonClassName,
  inactiveButtonClassName
}: SegmentedControlProps) {
  const [activeItem, setActiveItem] = useState(value);

  useEffect(() => {
    if (value) {
      setActiveItem(value);
    }
  }, [value]);

  const handleItemClick = (itemId: string) => {
    setActiveItem(itemId);
    if (onChange) {
      onChange(itemId);
    }
  };

  const sizeClasses = {
    pill: {
      sm: "p-0.5",
      md: "p-1",
      lg: "p-1.5"
    },
    button: {
      sm: "px-3 py-1.5 text-xs",
      md: "px-4 py-2 text-sm",
      lg: "px-5 py-2.5 text-base"
    },
    icon: {
      sm: "mr-1.5",
      md: "mr-2",
      lg: "mr-2"
    }
  };

  return (
    <div className={twMerge("flex", className)}>
      <div
        role="tablist"
        className={twMerge(
          "flex shadow-inner border rounded-lg space-x-1",
          sizeClasses.pill[size],
          pillClassName
        )}
      >
        {items.map((item) => (
          <button
            key={item.id}
            role="tab"
            type="button"
            aria-selected={activeItem === item.id}
            tabIndex={activeItem === item.id ? 0 : -1}
            className={twMerge(
              "flex items-center justify-center font-medium rounded-md transition-colors duration-200 ease-in-out",
              "focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-1 focus-visible:ring-offset-background",
              sizeClasses.button[size],
              buttonClassName,
              activeItem === item.id
                ? twMerge("bg-primary text-white", activeButtonClassName)
                : twMerge(inactiveButtonClassName)
            )}
            onClick={() => handleItemClick(item.id)}
          >
            {item.icon && (
              <span className={twMerge(sizeClasses.icon[size])}>
                {cloneElement(item.icon as ReactElement, {
                  size: size === "sm" ? 14 : size === "md" ? 16 : 20
                })}
              </span>
            )}
            {item.label}
          </button>
        ))}
      </div>
    </div>
  );
}

export default SegmentedControl;
