import { ReactElement, SVGProps } from "react";

export default function Logo(props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>): ReactElement {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 125 35" fill="currentColor" {...props}>
      <path d="M17.1 34.83c-4.86 0-8.91-1.575-12.195-4.725C1.62 26.91 0 22.815 0 17.82 0 12.78 1.665 8.64 4.95 5.4 8.28 2.115 12.33.495 17.145.495c5.94 0 11.205 2.565 14.265 6.57l-5.355 5.625c-2.565-2.745-5.355-4.14-8.37-4.14-2.565 0-4.68.855-6.435 2.61-1.71 1.755-2.565 3.96-2.565 6.615 0 2.565.855 4.725 2.565 6.48 1.755 1.71 3.87 2.565 6.39 2.565 3.285 0 6.165-1.44 8.55-4.275l5.58 5.355c-1.53 2.025-3.6 3.69-6.165 4.995-2.565 1.305-5.4 1.935-8.505 1.935ZM42.834 0v34.335h-7.92V0h7.92ZM66.194 11.745v-2.25h7.47v24.84h-7.47v-2.16c-2.025 1.845-4.455 2.79-7.335 2.79-3.555 0-6.435-1.26-8.73-3.78-2.295-2.52-3.42-5.625-3.42-9.36 0-3.735 1.125-6.84 3.42-9.27 2.295-2.475 5.22-3.69 8.73-3.69 2.88 0 5.31.945 7.335 2.88Zm-.09 10.125c0-1.575-.54-2.925-1.665-4.05s-2.475-1.71-4.095-1.71c-1.665 0-3.015.585-4.095 1.71-1.08 1.125-1.62 2.475-1.62 4.05 0 1.62.54 3.015 1.62 4.14 1.125 1.125 2.475 1.71 4.095 1.71 1.62 0 2.97-.585 4.095-1.71 1.125-1.125 1.665-2.52 1.665-4.14ZM86.603 34.335h-7.92V9.495h7.605v3.06c1.575-2.475 3.78-3.735 6.615-3.735a7.42 7.42 0 0 1 3.105.675l-.63 7.56c-1.17-.405-2.25-.585-3.285-.585-3.645 0-5.49 2.385-5.49 7.155v10.71ZM110.481 35.01c-4.005 0-7.245-1.26-9.765-3.78-2.475-2.52-3.735-5.625-3.735-9.315 0-3.69 1.26-6.795 3.735-9.315 2.52-2.565 5.76-3.825 9.765-3.825 4.005 0 7.29 1.26 9.765 3.825 2.52 2.52 3.78 5.625 3.78 9.315 0 3.69-1.26 6.795-3.78 9.315-2.475 2.52-5.76 3.78-9.765 3.78Zm0-7.425c1.575 0 2.925-.54 4.005-1.62 1.08-1.125 1.62-2.475 1.62-4.095 0-1.575-.54-2.925-1.665-4.005-1.08-1.08-2.385-1.62-3.96-1.62-1.575 0-2.88.54-3.96 1.665-1.08 1.08-1.62 2.385-1.62 3.96 0 1.62.54 2.97 1.62 4.095 1.08 1.08 2.385 1.62 3.96 1.62Z" />
    </svg>
  );
}
