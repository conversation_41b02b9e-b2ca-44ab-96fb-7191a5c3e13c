import { ReactElement, SVGProps } from "react";

const Home = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>): ReactElement => (
  <svg
    // className="flex-shrink-0 size-4"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
    <polyline points="9 22 9 12 15 12 15 22" />
  </svg>
);

export default Home;
