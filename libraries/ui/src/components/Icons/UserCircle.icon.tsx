import { ReactElement, SVGProps } from "react";

const UserCircle = (props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>): ReactElement => (
  <svg viewBox="0 0 36 37" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path
      d="M18 0.5C8.05645 0.5 0 8.55645 0 18.5C0 28.4435 8.05645 36.5 18 36.5C27.9435 36.5 36 28.4435 36 18.5C36 8.55645 27.9435 0.5 18 0.5ZM18 7.46774C21.5274 7.46774 24.3871 10.3274 24.3871 13.8548C24.3871 17.3823 21.5274 20.2419 18 20.2419C14.4726 20.2419 11.6129 17.3823 11.6129 13.8548C11.6129 10.3274 14.4726 7.46774 18 7.46774ZM18 32.4355C13.7395 32.4355 9.92177 30.5048 7.36694 27.4855C8.73145 24.9161 11.4024 23.1452 14.5161 23.1452C14.6903 23.1452 14.8645 23.1742 15.0315 23.225C15.975 23.5298 16.9621 23.7258 18 23.7258C19.0379 23.7258 20.0323 23.5298 20.9685 23.225C21.1355 23.1742 21.3097 23.1452 21.4839 23.1452C24.5976 23.1452 27.2685 24.9161 28.6331 27.4855C26.0782 30.5048 22.2605 32.4355 18 32.4355Z"
      // fill="#545353"
    />
  </svg>
);

export default UserCircle;
