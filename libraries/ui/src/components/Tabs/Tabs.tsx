import { ReactElement, useState } from "react";
import { twMerge } from "tailwind-merge";

export type TabItem = {
  title: string;
  content: ReactElement;
};
type Props = {
  items: TabItem[];
  kind?: "tabs" | "pills";
  tabsStyle?: string;
  tabItemStyle?: string;
  contentStyle?: string;
  onTabChange?: (index: number) => void;
  defaultActiveTab?: number;
  disabled?: boolean;
};
function Tabs({
  items,
  tabsStyle,
  tabItemStyle,
  contentStyle,
  kind = "tabs",
  onTabChange,
  defaultActiveTab = 0,
  disabled
}: Props) {
  const [active, setActive] = useState(defaultActiveTab);
  const onTabClick = (index: number) => {
    setActive(index);
    onTabChange?.(index);
  };

  return (
    <>
      <nav
        className={twMerge(
          "flex gap-3 border-b border-neutral-200 dark:border-neutral-700",
          tabsStyle,
          kind === "tabs" && "justify-evenly",
          kind === "pills" && "gap-0 py-3 px-6"
        )}
        aria-label="Tabs"
        role="tablist"
      >
        {items.length > 0 &&
          items.map((item, i) => (
            <button
              type="button"
              disabled={disabled}
              className={twMerge(
                "inline-flex justify-center items-center text-sm whitespace-nowrap text-neutral-700 dark:text-neutral-200",
                kind === "tabs" &&
                  `flex-1 py-2 px-1 border-b-2 border-transparent hover:text-primary focus:outline-none disabled:opacity-50 disabled:pointer-events-none dark:text-neutral-300 dark:hover:text-neutral-100`,
                kind === "pills" &&
                  "px-4 py-3 border font-medium first:rounded-l-lg last:rounded-r-lg dark:border-neutral hover:bg-neutral-50 dark:hover:bg-neutral-800",
                tabItemStyle,
                i == active && "font-semibold dark:text-white",
                i == active && kind === "tabs" && "text-primary border-primary dark:border-neutral-200",
                i == active && kind === "pills" && "text-text bg-neutral-100 dark:bg-neutral-900"
              )}
              key={`tab-${i}`}
              id={`tab-${i}`}
              onClick={() => onTabClick(i)}
              aria-label={item.title}
              role="tab"
            >
              {item.title}
            </button>
          ))}
      </nav>

      <div role="tabpanel" className={twMerge(`${contentStyle}`)} aria-labelledby={`tab-${active}`}>
        {items[active].content}
      </div>
    </>
  );
}
export default Tabs;
