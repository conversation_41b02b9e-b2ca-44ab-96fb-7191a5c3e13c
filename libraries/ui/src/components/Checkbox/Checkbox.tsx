import { InputHTMLAttributes } from "react";
import { twMerge } from "tailwind-merge";

type Props = InputHTMLAttributes<HTMLInputElement> & {
  label?: string;
  containerStyle?: string;
  inputStyle?: string;
};
function Checkbox({ label, containerStyle, inputStyle, name, ...props }: Props) {
  return (
    <div className={twMerge("flex h-fit gap-3 items-center relative", containerStyle)}>
      <input
        id={name}
        name={name}
        type="checkbox"
        className={twMerge(
          "shrink-0 border-gray-300 rounded text-primary cursor-pointer focus:ring-0 disabled:opacity-50 disabled:pointer-events-none focus:ring-primary [&:disabled~label]:opacity-70 [&:disabled~label]:cursor-not-allowed",
          inputStyle
        )}
        {...props}
      />
      {label ? (
        <label htmlFor={name} className="text-sm cursor-pointer dark:text-white">
          {label}
        </label>
      ) : (
        <label htmlFor={name} className="sr-only" aria-label={`Checkbox for ${name}`}>
          {name}
        </label>
      )}
    </div>
  );
}
export default Checkbox;
