import { ChangeEvent, InputHTMLAttributes, KeyboardEvent, useState } from "react";
import { Input } from "../";
import Chip from "./Chip";

type Props<T = string> = Omit<InputHTMLAttributes<HTMLInputElement>, "value" | "onChange"> & {
  label?: string;
  error?: string;
  containerStyle?: string;
  values?: T[];
  onChange: (values: T[]) => void;
  validate?: (value: string) => boolean;
  disabled?: boolean;
};
function ChipInput({
  label,
  name,
  placeholder = "Type and press Enter",
  containerStyle,
  values = [],
  onChange,
  error,
  validate,
  disabled,
  ...props
}: Props) {
  const [inputValue, setInputValue] = useState("");

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      const newValue = inputValue.trim();

      if (validate && !validate(newValue)) {
        return;
      }

      if (newValue && !values.includes(newValue)) {
        const newValues = [...values, newValue];
        onChange(newValues);
        setInputValue("");
      }
    } else if (e.key === "Backspace" && !inputValue && values.length > 0) {
      const newValues = values.slice(0, -1);
      onChange(newValues);
    }
  };

  const handleRemoveValue = (valueToRemove: string) => {
    const newValues = values.filter((v) => v !== valueToRemove);
    onChange(newValues);
  };

  return (
    <div className={containerStyle}>
      <div className="flex flex-col gap-1">
        {label && (
          <label htmlFor={name} className="block text-xs font-medium dark:text-white">
            {label}
          </label>
        )}
        <div className="relative">
          <div className="flex flex-wrap items-center gap-1 p-1 bg-grey-light rounded-lg dark:bg-transparent dark:border dark:border-neutral-600">
            {values.map((value, index) => (
              <Chip
                key={`${value}-${index}`}
                label={value}
                style="bg-neutral-10"
                onRemove={() => handleRemoveValue(value)}
                disabled={disabled}
              />
            ))}
            <Input
              {...props}
              disabled={disabled}
              inputStyle="!border-none !shadow-none !ring-0 !bg-transparent"
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
            />
          </div>
          {error && <div className="mt-1 text-xs text-red-600">{error}</div>}
        </div>
      </div>
    </div>
  );
}

export default ChipInput;
