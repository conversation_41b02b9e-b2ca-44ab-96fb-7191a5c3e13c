import { XCircleIcon } from "lucide-react";
import { twMerge } from "tailwind-merge";

interface Props {
  label?: string;
  style?: string;
  disabled?: boolean;
  onRemove: () => void;
}
const Chip = ({ label, style, disabled, onRemove }: Props) => (
  <div
    className={twMerge(
      "inline-flex items-center justify-between gap-1 p-1 text-xs bg-neutral-100 border rounded-lg dark:bg-gray-700 dark:border-gray-600 flex-1",
      disabled && "opacity-50 cursor-not-allowed",
      style
    )}
  >
    <div>{label || ""}</div>
    {!disabled && (
      <button
        type="button"
        onClick={onRemove}
        className="hover:bg-neutral-300 rounded-full dark:hover:bg-gray-600"
      >
        <XCircleIcon className="size-4 text-gray-500" />
      </button>
    )}
  </div>
);
export default Chip;
