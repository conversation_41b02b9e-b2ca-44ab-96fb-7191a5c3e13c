import { ChevronLeft, ChevronRight } from "lucide-react";
import { useMemo } from "react";
import { twMerge } from "tailwind-merge";

type PaginationResponse = {
  skip: number;
  limit: number;
  total?: number;
};
type Props = Omit<PaginationResponse, "skip"> & {
  currentPage: number;
  onPageChange(page: number): void;
  // onLimitChange?(limit: number): void;
};

const Pagination = ({ currentPage, total = 0, limit, onPageChange }: Props) => {
  const totalPages = useMemo(() => Math.ceil(total / limit), [limit, total]);

  const pageNumbers = useMemo(() => {
    const maxPageNumbers = 5;
    const halfRange = Math.floor(maxPageNumbers / 2);
    let start = Math.max(currentPage - halfRange, 1);
    const end = Math.min(start + maxPageNumbers - 1, totalPages);

    if (end - start < maxPageNumbers - 1) {
      start = Math.max(end - maxPageNumbers + 1, 1);
    }

    const pageNumbers = [];
    for (let i = start; i <= end; i++) {
      pageNumbers.push(i);
    }

    return pageNumbers;
  }, [currentPage, totalPages]);

  return (
    <nav>
      <ul className="flex items-center bg-white w-fit text-neutral-500 rounded-md shadow-md dark:bg-neutral-800 dark:text-neutral-200">
        <li>
          <button
            className="py-1 px-3 rounded-md hover:bg-neutral-50 disabled:opacity-50 disabled:bg-neutral-50 dark:disabled:bg-neutral-700 hover:dark:bg-neutral-700"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <ChevronLeft size={20} />
          </button>
        </li>
        {pageNumbers.map((number) => (
          <li key={number}>
            <button
              className={twMerge(
                "py-1 w-12 text-sm rounded-md hover:bg-neutral-50 hover:dark:bg-neutral-700",
                currentPage === number && "text-primary font-semibold dark:text-white"
              )}
              onClick={() => onPageChange(number)}
            >
              {number}
            </button>
          </li>
        ))}
        <li>
          <button
            className="py-1 px-3 rounded-md hover:bg-neutral-50 disabled:opacity-50 disabled:bg-neutral-50 dark:disabled:bg-neutral-700 hover:dark:bg-neutral-700"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            <ChevronRight size={20} />
          </button>
        </li>
      </ul>
    </nav>
  );
};
export default Pagination;
