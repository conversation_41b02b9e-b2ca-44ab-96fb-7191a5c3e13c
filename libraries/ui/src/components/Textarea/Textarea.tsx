import { AlertCircle } from "lucide-react";
import { FormEvent, ReactNode, TextareaHTMLAttributes, forwardRef } from "react";
import { twMerge } from "tailwind-merge";

type Props = TextareaHTMLAttributes<HTMLTextAreaElement> & {
  label?: string;
  error?: string;
  hint?: ReactNode;
  info?: string;
  containerStyle?: string;
  inputStyle?: string;
  onTextChange?(text?: string): void;
};
const Textarea = forwardRef<HTMLTextAreaElement, Props>(
  (
    {
      label,
      error,
      hint,
      info,
      containerStyle,
      inputStyle,
      onTextChange,
      onChange,
      rows = 4,
      name,
      ...props
    },
    ref
  ) => {
    const handleTextChange = (e: FormEvent<HTMLTextAreaElement>) => {
      if (onTextChange) {
        const value = e.currentTarget.value;
        if (value !== "") onTextChange(value);
        else onTextChange(undefined);
      }
    };

    return (
      <div className={containerStyle}>
        <div className="flex justify-between items-center">
          {label ? (
            <label htmlFor={name} className="block text-xs font-medium mb-1 dark:text-white">
              {label}
            </label>
          ) : (
            <label htmlFor={name} className="sr-only" aria-label={`Textarea for ${name}`}>
              {name}
            </label>
          )}
          <span className="block mb-1 text-xs text-gray-500 dark:text-neutral-200">{hint}</span>
        </div>
        <div className="relative">
          <textarea
            id={name}
            ref={ref}
            rows={rows}
            className={twMerge(
              "py-2 px-2 block w-full rounded-lg text-xs bg-grey-light border-none dark:border-solid focus:!border-primary focus:!ring-primary disabled:opacity-50 disabled:cursor-not-allowed dark:bg-transparent dark:text-neutral-200 dark:placeholder-neutral-400 dark:focus:!ring-neutral-200 dark:focus:!border-neutral-200",
              error && "!pr-10 border-solid !border-red-500 focus:!border-red-600 focus:!ring-red-600",
              inputStyle
            )}
            onChange={(e) => {
              if (onTextChange) handleTextChange(e);
              else if (onChange) onChange(e);
            }}
            {...props}
          />
          {error && (
            <div className="absolute inset-y-0 end-0 flex items-center pointer-events-none pe-3">
              <AlertCircle className="flex-shrink-0 size-4 text-red-500" />
            </div>
          )}
        </div>
        {error && <div className="mt-1 text-xs text-red-600">{error}</div>}
        {!error && info && <div className="mt-1 text-xs text-gray-500 dark:text-neutral-200">{info}</div>}
      </div>
    );
  }
);
export default Textarea;
