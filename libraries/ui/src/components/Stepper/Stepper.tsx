import { Check } from "lucide-react";
import { twMerge } from "tailwind-merge";

type Props = {
  steps: number;
  step: number;
  onStepChange?: (step: number) => void;
};
function Stepper({ steps, step = 1, onStepChange }: Props) {
  return (
    <div className="relative flex items-center justify-between">
      {/* Progress bar background */}
      <div className="absolute h-1 w-full bg-neutral-200 top-1/2 transform -translate-y-1/2 z-0" />

      {/* Active progress bar */}
      <div
        className="absolute h-1 bg-primary top-1/2 transform -translate-y-1/2 z-0 transition-all duration-300"
        style={{ width: `${((step - 1) / (steps - 1)) * 100}%` }}
      />

      {/* Step circles */}
      {Array.from({ length: steps }, (_, i) => i + 1).map((stepNumber) => (
        <div
          key={stepNumber}
          onClick={() => onStepChange?.(stepNumber)}
          className={twMerge(
            "relative z-10 w-10 h-10 rounded-full flex items-center justify-center text-lg font-medium transition-all duration-300",
            step >= stepNumber ? "bg-primary text-white" : "bg-white text-primary border-2 border-primary",
            onStepChange && "cursor-pointer"
          )}
        >
          {stepNumber < step ? <Check className="size-5" /> : stepNumber}
        </div>
      ))}
    </div>
  );
}
export default Stepper;
