import {
  Clip<PERSON><PERSON><PERSON><PERSON>,
  Filter,
  LoaderCircle,
  PencilIcon,
  PlusIcon,
  RefreshCw,
  Settings,
  Trash2Icon,
  Upload
} from "lucide-react";
import { ButtonHTMLAttributes, ReactElement, useMemo } from "react";
import { twMerge } from "tailwind-merge";

interface Props extends ButtonHTMLAttributes<HTMLButtonElement> {
  label?: string;
  kind?:
    | "outline"
    | "cancel"
    | "delete"
    | "create"
    | "update"
    | "filter"
    | "refresh"
    | "upload"
    | "fields"
    | "edit";
  shape?: "rounded" | "pill";
  loading?: boolean;
  loadingLabel?: string;
  icon?: ReactElement;
  onClick?: React.MouseEventHandler<HTMLButtonElement> | (() => void);
}
function Button({
  label,
  kind,
  loading,
  loadingLabel = "Please wait...",
  icon,
  shape,
  className,
  onClick,
  ...props
}: Props) {
  const shapeStyle = useMemo(() => {
    if (shape === "rounded") {
      return "rounded-full p-2";
    }

    if (shape === "pill") {
      return "py-2 px-4 rounded-full";
    }

    return "py-2 px-4 rounded-lg";
  }, [shape]);

  const style = useMemo(() => {
    switch (kind) {
      case "outline":
      case "fields":
      case "filter":
        return twMerge(
          `border-primary text-primary hover:bg-neutral-100 dark:border-neutral-700 dark:hover:bg-neutral-700`,
          className
        );
      case "cancel":
        return twMerge(
          `border-neutral-200 bg-white text-neutral-800 hover:bg-neutral-50 dark:border-neutral-700 dark:hover:bg-neutral-800`,
          className
        );
      case "delete":
        return twMerge(`border-danger text-danger hover:shadow-sm hover:shadow-danger`, className);
      default:
        return twMerge(`border-transparent bg-primary text-white hover:bg-primary-900`, className);
    }
  }, [className, kind]);

  const renderIcon = useMemo(() => {
    switch (kind) {
      case "create":
        return <PlusIcon size={16} />;
      case "update":
        return <ClipboardCheck size={16} />;
      case "delete":
        return <Trash2Icon size={16} />;
      case "filter":
        return <Filter size={16} />;
      case "refresh":
        return <RefreshCw size={16} />;
      case "upload":
        return <Upload size={16} />;
      case "fields":
        return <Settings size={16} />;
      case "edit":
        return <PencilIcon size={16} />;
      default:
        return icon;
    }
  }, [icon, kind]);

  return (
    <button
      type="button"
      className={twMerge(
        "inline-flex gap-2 items-center justify-center text-sm font-medium border shadow disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 dark:text-white dark:bg-neutral-900",
        shapeStyle,
        style
      )}
      disabled={loading || props.disabled}
      onClick={loading ? undefined : onClick}
      {...props}
    >
      {loading ? <LoaderCircle className="size-4 animate-spin" /> : renderIcon}
      {label && <span>{loading ? loadingLabel : label}</span>}
    </button>
  );
}

export default Button;
