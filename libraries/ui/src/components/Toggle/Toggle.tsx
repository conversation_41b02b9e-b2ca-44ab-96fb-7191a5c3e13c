import { InputHTMLAttributes } from "react";
import { twMerge } from "tailwind-merge";

type Props = InputHTMLAttributes<HTMLInputElement> & {
  label?: string;
  hint?: string;
  containerStyle?: string;
  inputStyle?: string;
};
function Toggle({ label, hint, containerStyle, inputStyle, name, ...props }: Props) {
  return (
    <div className={containerStyle}>
      <div className="flex justify-between items-center">
        {label ? (
          <label aria-label={label} htmlFor={name} className="block text-xs font-medium mb-1 dark:text-white">
            {label}
          </label>
        ) : (
          <label htmlFor={name} className="sr-only" aria-label={`Toggle for ${name}`}>
            {name}
          </label>
        )}
        <span className="block mb-1 text-xs text-gray-500 dark:text-neutral-200">{hint}</span>
      </div>
      <div className="relative">
        <input
          id={name}
          type="checkbox"
          className={twMerge(
            "relative !w-13 h-7 !p-px bg-neutral-200 border-transparent text-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed checked:!bg-none checked:!bg-primary checked:text-primary checked:border-primary dark:bg-neutral-600 dark:border-neutral-700 dark:checked:!bg-neutral-800 dark:checked:border-neutral-900 dark:focus:ring-offset-0 before:inline-block before:size-6 before:bg-white before:translate-x-0 checked:before:translate-x-full before:rounded-full before:shadow before:transform before:ring-0 before:transition before:ease-in-out before:duration-200 dark:before:bg-neutral-200",
            inputStyle
          )}
          {...props}
        />
      </div>
    </div>
  );
}
export default Toggle;
