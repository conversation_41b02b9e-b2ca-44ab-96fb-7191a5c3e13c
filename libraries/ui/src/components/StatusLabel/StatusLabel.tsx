import { HTMLAttributes, ReactElement } from "react";
import { twMerge } from "tailwind-merge";

function colorToStyle(color?: "gray" | "warning" | "info" | "success" | "danger" | "disabled" | string) {
  switch (color) {
    case "gray":
      return "bg-gray-200 text-gray-800 border-gray-300";
    case "warning":
      return "bg-orange-100 text-orange-800 border-orange-300";
    case "info":
      return "bg-sky-100 text-sky-800 border-sky-300";
    case "success":
      return "bg-emerald-100 text-emerald-800 border-emerald-300";
    case "danger":
      return "bg-red-100 text-red-800 border-red-300";
    case "disabled":
      return "bg-stone-100 text-stone-800 border-stone-300";
    default:
      return color;
  }
}
type Props = HTMLAttributes<HTMLDivElement> & {
  text?: string;
  color?: "gray" | "warning" | "info" | "success" | "danger" | "disabled" | string;
  icon?: ReactElement;
};
const StatusLabel = ({ color = "gray", text, className, icon }: Props) => {
  const style = twMerge(
    `text-dark rounded-md px-4 py-1 text-xs inline-flex gap-2 items-center font-medium`,
    colorToStyle(color),
    className
  );

  return (
    <span className={style}>
      {text}
      {icon && <i>{icon}</i>}
    </span>
  );
};
export default StatusLabel;
