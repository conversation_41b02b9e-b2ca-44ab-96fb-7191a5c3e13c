import { ChevronDown } from "lucide-react";
import { PropsWithChildren, useEffect, useReducer } from "react";
import { twMerge } from "tailwind-merge";

type AccordionProps = PropsWithChildren & {
  title: string;
  containerStyle?: string;
  toggleStyle?: string;
  contentStyle?: string;
  onToggle?: (isOpen: boolean) => void;
};

function Accordion({ title, children, containerStyle, toggleStyle, contentStyle, onToggle }: AccordionProps) {
  const [isOpen, toggle] = useReducer((r) => !r, false);

  useEffect(() => {
    onToggle?.(isOpen);
  }, [isOpen, onToggle]);

  return (
    <div className={containerStyle}>
      <div
        className={twMerge("flex justify-between items-center cursor-pointer py-2 border-b", toggleStyle)}
        onClick={toggle}
      >
        <span className="font-medium">{title}</span>
        <ChevronDown
          className={twMerge("size-4 transition-transform duration-200", isOpen && "transform rotate-180")}
        />
      </div>
      <div
        className={twMerge(
          "overflow-y-auto transition-max-height duration-300 ease-in-out",
          isOpen ? "max-h-96" : "max-h-0",
          contentStyle
        )}
      >
        {isOpen && children}
      </div>
    </div>
  );
}

export default Accordion;
