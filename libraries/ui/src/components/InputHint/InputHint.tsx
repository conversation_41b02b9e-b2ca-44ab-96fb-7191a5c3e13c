import { PropsWithChildren } from "react";
import { twMerge } from "tailwind-merge";

interface Props extends PropsWithChildren {
  label?: string;
  containerStyle?: string;
  onClick?: () => void;
}
function InputHint({ label = "Details", containerStyle, onClick }: Props) {
  return (
    <div
      className={twMerge("cursor-pointer underline text-xs", containerStyle)}
      onClick={onClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => e.key === "Enter" && onClick?.()}
    >
      {label}
    </div>
  );
}
export default InputHint;
