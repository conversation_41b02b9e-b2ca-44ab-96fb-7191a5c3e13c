import Alert from "./Alert";

function InvalidBusinessNumber({ show }: { show?: boolean }) {
  return (
    <Alert
      type="error"
      title="Verification Failed"
      message={
        <div className="flex flex-col">
          <p className="leading-5 mb-2">
            We couldn't verify your business information. The Business Number you provided is either invalid
            or does not match the company name on record with the Canada Revenue Agency (CRA).
          </p>
          <p className="leading-5 mb-1">Please double-check that:</p>
          <ul className="list-disc pl-5 leading-5 text-neutral-800 mb-2">
            <li>
              <b>The 15-digit Business Number</b> or <b>Importer Account Number</b> is correct
            </li>
            <li>
              The <b>Company Name</b> exactly matches your CRA registration
            </li>
          </ul>
          <p className="leading-5">
            If the issue persists, please contact your CRA representative or reach out to our support team for
            assistance.
          </p>
        </div>
      }
      compact
      isVisible={show === true}
    />
  );
}
export default InvalidBusinessNumber;
