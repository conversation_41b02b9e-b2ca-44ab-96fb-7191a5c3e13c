import { AlertTriangle, Check, Info, X, XCircle } from "lucide-react";
import { ReactNode, useEffect, useState } from "react";

interface AlertProps {
  type: "error" | "info" | "warning" | "gray" | "success";
  title?: string;
  message?: string | ReactNode;
  icon?: boolean | ReactNode;
  action?: ReactNode;
  actionPosition?: "bottom" | "end";
  isVisible?: boolean;
  dismissible?: boolean;
  onDismiss?: () => void;
  compact?: boolean;
  className?: string;
}

const alertStyles = {
  error: "bg-red-50 border-red-200 text-red-800 dark:bg-red-950 dark:border-red-800 dark:text-red-200",
  info: "bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-950 dark:border-blue-800 dark:text-blue-200",
  warning:
    "bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-950 dark:border-yellow-800 dark:text-yellow-200",
  gray: "bg-gray-50 border-gray-200 text-gray-800 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200",
  success:
    "bg-green-50 border-green-200 text-green-800 dark:bg-green-950 dark:border-green-800 dark:text-green-200"
};

const alertIcons = {
  error: XCircle,
  info: Info,
  warning: AlertTriangle,
  gray: Info,
  success: Check
};

const dismissButtonStyles = {
  error: "hover:bg-red-100 focus:bg-red-100 dark:hover:bg-red-900 dark:focus:bg-red-900",
  info: "hover:bg-blue-100 focus:bg-blue-100 dark:hover:bg-blue-900 dark:focus:bg-blue-900",
  warning: "hover:bg-yellow-100 focus:bg-yellow-100 dark:hover:bg-yellow-900 dark:focus:bg-yellow-900",
  gray: "hover:bg-gray-200 focus:bg-gray-200 dark:hover:bg-gray-700 dark:focus:bg-gray-700",
  success: "hover:bg-green-100 focus:bg-green-100 dark:hover:bg-green-900 dark:focus:bg-green-900"
};

const alertContainerStyles = {
  compact: "py-2 px-3 rounded-md",
  default: "p-4 rounded-lg"
};

const Alert = ({
  type,
  title,
  message,
  icon = true,
  action,
  actionPosition = "bottom",
  dismissible = false,
  isVisible = true,
  onDismiss,
  compact = false,
  className
}: AlertProps) => {
  const [isLeaving, setIsLeaving] = useState(false);
  const [innerIsVisible, setInnerIsVisible] = useState(isVisible);

  useEffect(() => {
    setInnerIsVisible(isVisible);
  }, [isVisible]);

  const handleDismiss = () => {
    setIsLeaving(true);
    setTimeout(() => {
      setInnerIsVisible(false);
      onDismiss?.();
    }, 300);
  };

  useEffect(() => {
    if (isLeaving) {
      const timer = setTimeout(() => setInnerIsVisible(false), 300);
      return () => clearTimeout(timer);
    }
  }, [isLeaving]);

  if (!innerIsVisible) return null;

  const IconComponent = typeof icon === "boolean" ? alertIcons[type] : null;

  return (
    <div
      className={`
        ${alertStyles[type]} border text-sm relative
        transition-all duration-300 ease-in-out
        ${isLeaving ? "opacity-0 translate-x-5" : "opacity-100 translate-x-0"}
        ${alertContainerStyles[compact ? "compact" : "default"]}
        ${className}
      `}
      role="alert"
      tabIndex={-1}
      aria-labelledby={title ? `${type}-alert-title` : undefined}
    >
      {dismissible && actionPosition !== "end" && (
        <button
          type="button"
          onClick={handleDismiss}
          className={`absolute top-1 right-1 inline-flex rounded-lg p-1.5 ${dismissButtonStyles[type]} transition-colors duration-200`}
          aria-label="Dismiss"
        >
          <span className="sr-only">Dismiss</span>
          <X className="size-4" />
        </button>
      )}
      <div className={`flex gap-3 ${actionPosition === "end" ? "items-center" : ""}`}>
        {icon && (
          <div className="shrink-0 mt-0.5">
            {typeof icon === "boolean" ? IconComponent && <IconComponent className="size-4" /> : icon}
          </div>
        )}
        <div className={`flex flex-1 ${actionPosition === "end" ? "items-center" : "flex-col"}`}>
          <div className="flex-1">
            {title && (
              <h3 id={`${type}-alert-title`} className="text-sm font-medium">
                {title}
              </h3>
            )}
            {message &&
              (typeof message === "string" ? (
                <p className={`text-sm ${!title ? "mt-0" : "mt-1"}`}>{message}</p>
              ) : (
                <div className={`text-sm ${!title ? "mt-0" : "mt-1"}`}>{message}</div>
              ))}
          </div>
          {action && (
            <div
              className={`
                ${actionPosition === "end" ? "ms-4 mt-0 md:mt-0" : "mt-4"}
                ${actionPosition === "end" ? "text-right" : ""}
              `}
            >
              <div className="flex gap-x-3">{action}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Alert;
