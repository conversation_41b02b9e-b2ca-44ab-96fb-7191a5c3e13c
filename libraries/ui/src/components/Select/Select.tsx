import { AlertCircle } from "lucide-react";
import { FormEvent, SelectHTMLAttributes } from "react";
import { twMerge } from "tailwind-merge";
import { SelectOption } from "utils";

type Props = SelectHTMLAttributes<HTMLSelectElement> & {
  label?: string;
  error?: string;
  hint?: string;
  info?: string;
  containerStyle?: string;
  options: SelectOption[];
  optional?: boolean;
  inputStyle?: string;
  onSelected?(text?: unknown): void;
};
function Select({
  label,
  error,
  hint,
  info,
  containerStyle,
  options,
  optional,
  onSelected,
  inputStyle,
  onChange,
  name,
  ...props
}: Props) {
  const handleTextChange = (e: FormEvent<HTMLSelectElement>) => {
    if (onSelected) {
      const value = e.currentTarget.value;
      if (value !== "") onSelected(value);
      else onSelected(undefined);
    }
  };

  return (
    <div className={containerStyle}>
      <div className="flex justify-between items-center">
        {label ? (
          <label htmlFor={name} className="block text-xs font-medium mb-1 dark:text-white">
            {label}
          </label>
        ) : (
          <label htmlFor={name} className="sr-only" aria-label={`Select for ${name}`}>
            {name}
          </label>
        )}
        <span className="block mb-1 text-xs text-gray-500 dark:text-neutral-200">{hint}</span>
      </div>
      <div className="relative">
        <select
          id={name}
          name={name}
          className={twMerge(
            "py-2 pl-2 pr-6 pe-10 ps-2 block w-full rounded-lg text-xs bg-grey-light border-none dark:border-solid focus:border-primary focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed dark:bg-transparent dark:text-neutral-200 dark:placeholder-neutral-400 dark:focus:ring-neutral-200 dark:focus:border-neutral-200",
            error && "!pr-10 border-solid !border-red-500 focus:border-red-500 focus:ring-red-500",
            inputStyle
          )}
          onChange={(e) => (onSelected ? handleTextChange(e) : onChange && onChange(e))}
          {...props}
        >
          {optional && (
            <option className="p-4 hover:bg-background dark:bg-dark" key={0} value="">
              Select
            </option>
          )}
          {options.length > 0 &&
            options.map((opt, i) => (
              <option
                className="p-4 hover:bg-background dark:bg-dark"
                key={i}
                value={opt.value}
                disabled={opt.disabled}
              >
                {opt.label}
              </option>
            ))}
        </select>
        {error && (
          <div className="absolute inset-y-0 end-0 flex items-center pointer-events-none pe-8">
            <AlertCircle className="flex-shrink-0 size-4 text-red-500" />
          </div>
        )}
      </div>
      {error && <div className="mt-1 text-xs text-red-600">{error}</div>}
      {!error && info && <div className="mt-1 text-xs text-gray-500 dark:text-neutral-200">{info}</div>}
    </div>
  );
}
export default Select;
