import { ColorResult, TwitterPicker } from "react-color";

type Props = {
  value?: string;
  label?: string;
  hint?: string;
  error?: string;
  alpha?: number;
  containerStyle?: string;

  onChangeRgba?(color: string): void;
  onChangeHex?(color: string): void;
};
function ColorPicker({ value, label, hint, containerStyle, alpha = 0.1, onChangeRgba, onChangeHex }: Props) {
  const onChange = ({ rgb, hex }: ColorResult) => {
    if (onChangeRgba) {
      const rgba = `rgba(${rgb.r},${rgb.g},${rgb.b},${(rgb.a ?? 0 > 0) ? alpha : 0})`;
      onChangeRgba(rgba);
    }

    if (onChangeHex) {
      onChangeHex(hex);
    }
  };
  return (
    <div className={containerStyle}>
      <div className="flex justify-between items-center">
        <label className="block text-sm font-medium mb-1 dark:text-white">{label}</label>
        <span className="block mb-1 text-xs text-gray-500 dark:text-neutral-500">{hint}</span>
      </div>
      <div className="relative">
        <TwitterPicker
          // width="100%"
          color={value}
          colors={[
            "#C82A2C", // primary
            // "#731819",
            // "#F78DA7",
            "#EB144C", // pink
            "#FF6900", // orange
            "#FCB900", // yellow
            // "#7BDCB5",
            "#00D084", // green
            // "#8ED1FC",
            "#0693E3", // blue
            "#9900EF", // purple
            // "#ABB8C3",
            "#968e8f", // grey
            "black",
            "transparent"
          ]}
          onChangeComplete={onChange}
          triangle="top-left"
          // onSwatchHover={(color, event) => console.log(color, event)}
        />
      </div>
    </div>
  );
}
export default ColorPicker;

// SAMPLE
// handleClick = () => {
//   this.setState({ displayColorPicker: !this.state.displayColorPicker })
// };

// handleClose = () => {
//   this.setState({ displayColorPicker: false })
// };

// handleChange = (color) => {
//   this.setState({ color: color.rgb })
// };

// render() {

//   const styles = reactCSS({
//     'default': {
//       color: {
//         width: '36px',
//         height: '14px',
//         borderRadius: '2px',
//         background: `rgba(${ this.state.color.r }, ${ this.state.color.g }, ${ this.state.color.b }, ${ this.state.color.a })`,
//       },
//       swatch: {
//         padding: '5px',
//         background: '#fff',
//         borderRadius: '1px',
//         boxShadow: '0 0 0 1px rgba(0,0,0,.1)',
//         display: 'inline-block',
//         cursor: 'pointer',
//       },
//       popover: {
//         position: 'absolute',
//         zIndex: '2',
//       },
//       cover: {
//         position: 'fixed',
//         top: '0px',
//         right: '0px',
//         bottom: '0px',
//         left: '0px',
//       },
//     },
//   });

//   return (
//     <div>
//       <div style={ styles.swatch } onClick={ this.handleClick }>
//         <div style={ styles.color } />
//       </div>
//       { this.state.displayColorPicker ? <div style={ styles.popover }>
//         <div style={ styles.cover } onClick={ this.handleClose }/>
//         <SketchPicker color={ this.state.color } onChange={ this.handleChange } />
//       </div> : null }

//     </div>
//   )
// }
