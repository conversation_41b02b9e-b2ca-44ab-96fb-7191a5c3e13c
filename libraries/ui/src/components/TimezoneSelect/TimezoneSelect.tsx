import { SelectHTMLAttributes } from "react";
import {
  type ITimezone,
  allTimezones,
  ILabelStyle,
  ITimezoneOption,
  useTimezoneSelect
} from "react-timezone-select";
import Select from "../Select";

const timezones = {
  ...allTimezones,
  "America/Toronto": "Eastern Time",
  "America/Chicago": "Central Time",
  "America/Boise": "Mountain Time",
  "America/Los_Angeles": "Pacific Time"
};

type Props = SelectHTMLAttributes<HTMLSelectElement> & {
  label?: string;
  value: ITimezone;
  error?: string;
  labelStyle?: ILabelStyle;
  optional?: boolean;
  onSelected(value: ITimezoneOption): void;
};
function TimezoneSelect({
  label,
  value,
  error,
  labelStyle = "abbrev",
  optional,
  onSelected,
  ...props
}: Props) {
  const { options, parseTimezone } = useTimezoneSelect({
    labelStyle,
    timezones
  });

  return (
    <Select
      onChange={(e) => onSelected(parseTimezone(e.currentTarget.value))}
      options={options}
      value={value}
      label={label}
      error={error}
      optional={optional}
      {...props}
    />
  );
}
export default TimezoneSelect;
