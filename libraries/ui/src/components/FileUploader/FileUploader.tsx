import { UploadCloud } from "lucide-react";
import { useCallback } from "react";
import { DropzoneOptions, useDropzone } from "react-dropzone";

type FileUploaderProps = DropzoneOptions & {
  name?: string;
  id?: string;
  hint?: string;
  onChange: (files: File[]) => void;
};

const FileUploader = ({ onChange, accept, multiple = true, id, name, hint }: FileUploaderProps) => {
  const onDrop = useCallback(
    (files: File[]) => {
      onChange(files);
    },
    [onChange]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple,
    accept
  });

  return (
    <div {...getRootProps()} className="border-2 border-dashed rounded-lg px-4 py-6 relative overflow-hidden">
      <input {...getInputProps()} name={name} id={id} />
      <div
        className="flex flex-col items-center
        text-gray-500 dark:text-gray-400
      "
      >
        <UploadCloud className="w-12 h-12" />
        <p
          className="
        text-md
        text-gray-500 dark:!text-gray-400 mt-2
        "
        >
          {hint ?? "Drag and drop some files here, or click to select files"}
        </p>
      </div>
      {isDragActive && (
        <div className="absolute inset-0 flex items-center justify-center bg-grey dark:bg-grey-dark">
          <p className="text-md">Drop the files here ...</p>
        </div>
      )}
    </div>
  );
};

export default FileUploader;
