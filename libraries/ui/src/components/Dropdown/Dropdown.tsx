import {
  Placement,
  UseDismissProps,
  autoPlacement,
  offset,
  useClick,
  useDismiss,
  useFloating,
  useInteractions
} from "@floating-ui/react";
import { PropsWithChildren, ReactElement, useReducer } from "react";
import { twMerge } from "tailwind-merge";

interface Props extends PropsWithChildren {
  triggerElement: ReactElement;
  className?: string;
  containerStyle?: string;
  dropdownStyle?: string;
  placement?: Placement;
  dismissProps?: UseDismissProps;
  open?: boolean;
  name?: string;
  hideOnClick?: boolean;
}
function Dropdown({
  triggerElement,
  className,
  containerStyle,
  dropdownStyle,
  placement,
  children,
  dismissProps,
  open = false,
  name = "dropdown",
  hideOnClick = false
}: Props) {
  const [show, toggle] = useReducer((r) => !r, open);
  const { refs, floatingStyles, context } = useFloating({
    open: show,
    onOpenChange: toggle,
    placement: placement ?? "bottom",
    middleware: [offset(5), !placement ? autoPlacement() : undefined]
  });
  const dismiss = useDismiss(context, { ...dismissProps });
  const click = useClick(context);

  const { getReferenceProps, getFloatingProps } = useInteractions([dismiss, click]);

  return (
    <div className={twMerge("relative inline-flex", className)}>
      <div
        id="dropdown"
        ref={refs.setReference}
        className={containerStyle}
        onClick={toggle}
        aria-label={name}
        {...getReferenceProps()}
      >
        {triggerElement}
      </div>

      {show && (
        <div
          className={twMerge(
            "transition-[opacity,margin] duration min-w-48 shadow-md bg-white rounded-lg opacity-0 z-50",
            show && "opacity-100",
            dropdownStyle
          )}
          ref={refs.setFloating}
          style={floatingStyles}
          aria-labelledby="dropdown"
          onClick={hideOnClick ? toggle : undefined}
          {...getFloatingProps()}
        >
          {children}
        </div>
      )}
    </div>
  );
}
export default Dropdown;
