import { ReactNode } from "react";
import { twMerge } from "tailwind-merge";

type Props = {
  label?: string;
  value?: string | number | null;
  addon?: string;
  customElement?: ReactNode;
  direction?: "row" | "column";
  textStyle?: string;
  labelStyle?: string;
  containerStyle?: string;
};

function FieldItem({
  label,
  value,
  addon,
  customElement,
  direction = "column",
  textStyle,
  containerStyle,
  labelStyle
}: Props) {
  return (
    <div
      className={twMerge(
        "flex",
        direction === "column" && "flex-col",
        direction === "row" && "inline-flex gap-1 items-center",
        containerStyle
      )}
    >
      <label
        className={twMerge(
          "text-xs font-medium text-light dark:text-neutral-400",
          direction === "row" && "!leading-5",
          labelStyle
        )}
      >
        {label}
      </label>
      {customElement ? (
        customElement
      ) : (
        <span className={twMerge("text-sm font-medium break-words", textStyle)}>
          {value !== "" && value != null ? value : "--"}
          {addon && <span className="ml-0.5 align-bottom">{addon}</span>}
        </span>
      )}
    </div>
  );
}

export default FieldItem;
