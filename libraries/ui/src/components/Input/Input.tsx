import { AlertCircle } from "lucide-react";
import { FormEvent, InputHTMLAttributes, ReactNode, forwardRef } from "react";
import { twMerge } from "tailwind-merge";
import { Icons } from "..";

export type InputProps = InputHTMLAttributes<HTMLInputElement> & {
  label?: string;
  error?: string;
  hint?: ReactNode;
  info?: string;
  containerStyle?: string;
  inputStyle?: string;
  kind?: "search";
  noDecimal?: boolean;
  icon?: ReactNode;
  onTextChange?(text?: string): void;
  onNumberChange?(e: FormEvent<HTMLInputElement>): void;
};
const Input = forwardRef<HTMLInputElement | null, InputProps>(
  (
    {
      label,
      error,
      hint,
      info,
      containerStyle,
      inputStyle,
      kind,
      onTextChange,
      onNumberChange,
      onChange,
      name,
      noDecimal,
      icon,
      ...props
    },
    ref
  ) => {
    const handleTextChange = (e: FormEvent<HTMLInputElement>) => {
      if (onTextChange) {
        const value = e.currentTarget.value;
        if (value !== "") onTextChange(value);
        else onTextChange(undefined);
      }
    };
    const handleNumberChange = (e: FormEvent<HTMLInputElement>) => {
      if (onNumberChange && e.currentTarget.validity.valid) {
        onNumberChange(e);
      }
    };
    return (
      <div className={containerStyle}>
        {label || hint ? (
          <div className="flex justify-between items-center">
            <label htmlFor={name} className="block text-xs font-medium mb-1 dark:text-white">
              {label}
            </label>

            <span className="block mb-1 text-xs text-gray-500 dark:text-neutral-200">{hint}</span>
          </div>
        ) : (
          <label htmlFor={name} className="sr-only" aria-label={`Input for ${name}`}>
            {name}
          </label>
        )}
        <div className="relative">
          {kind === "search" && (
            <div className="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-4 peer-disabled:opacity-50 peer-disabled:pointer-events-none">
              <Icons.Search className="size-4 text-gray-400" />
            </div>
          )}
          <input
            id={name}
            name={name}
            ref={ref}
            className={twMerge(
              "py-2 px-2 block w-full rounded-lg text-xs bg-grey-light border-none focus:!border-primary focus:!ring-primary disabled:opacity-50 disabled:cursor-not-allowed dark:bg-transparent dark:border-solid dark:text-neutral-200 dark:placeholder-neutral-400 dark:focus:!ring-neutral-200 dark:focus:!border-neutral-200",
              error && "!pr-10 border-solid !border-red-500 focus:!border-red-600 focus:!ring-red-600",
              kind === "search" && "!pl-10",
              props.type === "color" && "h-10 p-1",
              props.type === "date" && "!bg-grey-light dark:!bg-transparent",
              inputStyle
            )}
            {...(onNumberChange && {
              pattern: noDecimal ? "^\\d+$" : "[0-9.]*"
            })}
            onChange={(e) => {
              if (onTextChange) handleTextChange(e);
              else if (onNumberChange) handleNumberChange(e);
              else if (onChange) onChange(e);
            }}
            {...props}
          />
          {error && (
            <div className="absolute inset-y-0 end-0 flex items-center pointer-events-none pe-3">
              <AlertCircle className="flex-shrink-0 size-4 text-red-500" />
            </div>
          )}
          {icon && !error && (
            <div className="absolute inset-y-0 end-0 flex items-center pe-3 cursor-pointer">{icon}</div>
          )}
        </div>
        {error && <div className="mt-1 text-xs text-red-600">{error}</div>}
        {!error && info && <div className="mt-1 text-xs text-gray-500 dark:text-neutral-200">{info}</div>}
      </div>
    );
  }
);
export default Input;
