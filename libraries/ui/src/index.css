@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;

  color-scheme: light dark;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
@media only screen and (max-width: 768px) {
  body {
    font-size: 14px;
  }
}
@layer base {
  html {
    @apply text-text dark:text-white;
  }
  h1 {
    @apply text-3xl font-bold dark:text-white;
  }
  h2 {
    @apply text-2xl font-semibold dark:text-white;
  }
  h3 {
    @apply text-xl font-semibold dark:text-white;
  }
  h4 {
    @apply text-lg font-semibold dark:text-white;
  }
  h5 {
    @apply text-base font-medium dark:text-neutral-50;
  }
  h6 {
    @apply text-sm font-medium text-primary dark:text-white;
  }
  span {
    @apply text-sm;
  }
  p {
    @apply text-sm leading-7 text-neutral-800;
  }
  small {
    @apply text-xs leading-5 text-neutral-800;
  }
}
/* Webkit browsers (Chrome, Safari, newer versions of Edge) */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* ::-webkit-scrollbar-track {
  background: #f1f1f1;
} */

::-webkit-scrollbar-thumb {
  /* background-color: #002858; */
  @apply bg-primary;
}

::-webkit-scrollbar-thumb:hover {
  /* background-color: #004eb3; */
  @apply bg-primary-900;
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #002858 #f1f1f1;
}
/* @media (prefers-color-scheme: light) {
  :root {
    color: #030712;
  }
   a:hover {
    color: #263d42;
  }
} */
/* table {
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Helvetica Neue, Arial, sans-serif;
  font-size: 13px;
} */
/* Example */
/* @layer components {
  .btn-primary {
    @apply py-2 px-5 bg-violet-500 text-white font-semibold rounded-full shadow-md hover:bg-violet-700 focus:outline-none focus:ring focus:ring-violet-400 focus:ring-opacity-75;
  }
  .linear-background {
    background: linear-gradient(to bottom right, white 50%, #ff720d10 50%);
  }
  [type="checkbox"].check-black:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='%23262626' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
  }
} */
