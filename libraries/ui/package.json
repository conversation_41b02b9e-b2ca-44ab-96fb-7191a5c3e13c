{"name": "ui", "private": true, "version": "0.0.1", "description": "Shared UI package", "author": "danny-aistribute <<EMAIL>>", "type": "module", "main": "./dist/ui.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"start": "vite build", "build": "tsc && npm run lint && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives", "preview": "vite preview"}, "dependencies": {"@floating-ui/react": "^0.26.16", "lucide-react": "^0.378.0", "preline": "^2.1.0", "react-color": "^2.19.3", "react-dropzone": "~14.2.10", "react-timezone-select": "~3.2.8", "utils": "workspace:^", "react-phone-number-input": "~3.4.12", "react-google-autocomplete": "~2.7.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@types/node": "^20.14.0", "@types/react-color": "^3.0.12", "@types/react-dom": "^18.2.22", "@types/react": "^18.3.1", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "eslint": "^8.57.0", "postcss": "^8.4.38", "react-dom": "^18.2.0", "react-select": "~5.8.0", "react": "^18.3.1", "tailwind-merge": "~2.5.2", "tailwind": "workspace:^", "tailwindcss": "~3.4.10", "typescript": "~5.5.3", "vite-plugin-dts": "^4.2.1", "vite-plugin-lib-inject-css": "^2.1.1", "vite": "^5.2.0", "@types/google.maps": "~3.58.1"}, "peerDependencies": {"react": "^18.3.1", "react-select": "~5.8.0"}, "sideEffects": ["**/*.css"]}