{"name": "nodejs", "dockerComposeFile": ["../docker-compose.yml"], "service": "node-app", "postCreateCommand": "bash -i -c 'npm install -g @microsoft/rush'", "workspaceFolder": "/workspace", "shutdownAction": "none", "features": {"ghcr.io/devcontainers/features/aws-cli:1.1.2": {}}, "customizations": {"vscode": {"extensions": ["ms-vscode.vscode-typescript-tslint-plugin", "dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "bradlc.vscode-tailwindcss", "orta.vscode-jest"]}}, "forwardPorts": ["5001", "5432", "6379", "5173"]}