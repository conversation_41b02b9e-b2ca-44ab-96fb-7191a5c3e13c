services:
  node-app:
    image: mcr.microsoft.com/devcontainers/typescript-node:20
    volumes:
      - ..:/workspace:cached
    command: sleep infinity
    network_mode: service:db

  db:
    image: postgres:16
    restart: unless-stopped
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=postgres

  redis:
    image: redis:7.4-alpine
    volumes:
      - redis_data:/data
    command: redis-server --save 60 1 --loglevel warning
    network_mode: service:db

volumes:
  postgres_data:
  redis_data:
