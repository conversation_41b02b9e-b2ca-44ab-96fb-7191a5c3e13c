name: claro-services

services:
  # tika:
  #   build:
  #     context: ./tika
  #   volumes:
  #     - ./tika/tika-config.dev.xml:/app/tika-config.xml
  #   ports:
  #     - 9998:9998

  # minio:
  #   image: minio/minio:latest
  #   command: server /data --console-address ":9001"
  #   user: 1000:1000 # change to your user id
  #   ports:
  #     - 9000:9000
  #     - 9001:9001
  #   volumes:
  #     - ./minio/data:/data
  #   environment:
  #     - MINIO_ROOT_USER=minio
  #     - MINIO_ROOT_PASSWORD=password

  redis:
    image: redis:7.4-alpine
    user: 1000:1000
    ports:
      - 6380:6379
    volumes:
      - ./redis/data:/data
    command: redis-server --save 60 1 --loglevel warning
