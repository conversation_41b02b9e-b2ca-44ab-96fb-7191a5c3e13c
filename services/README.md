# Services

This folder contains the docker image and docker-compose file for the third-party services that are used in the application.

## Run the services for development

```bash
docker-compose -f docker-compose.dev.yml up
```

## List of services

- Apache Tika
- Minio

## Known issues of the services

- Tika (up to v3.0.0.0-BETA2)
    - When running OCR on PDF files, tika will nested the OCR result into previous page.

## Licenses
- Minio: [AGPL-3.0](https://github.com/minio/minio/blob/master/LICENSE)
- Tika: [Apache-2.0](https://github.com/apache/tika/blob/trunk/LICENSE.txt)
