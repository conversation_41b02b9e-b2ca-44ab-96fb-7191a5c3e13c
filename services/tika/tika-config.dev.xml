<?xml version="1.0" encoding="UTF-8"?>
<properties>
  <parsers>
    <parser class="org.apache.tika.parser.DefaultParser" />
    <parser class="org.apache.tika.parser.ocr.TesseractOCRParser">
      <params>
        <param name="pageSegMode" type="string">3</param>
        <param name="enableImagePreprocessing" type="bool">true</param>
        <param name="colorspace" type="string">gray</param>
        <param name="depth" type="int">4</param>
        <param name="applyRotation" type="bool">true</param>
        <!-- space delimited key-value pairs -->
        <param name="otherTesseractSettings" type="list">
          <string>thresholding_method 1</string>
        </param>
      </params>
    </parser>
    <parser class="org.apache.tika.parser.pdf.PDFParser">
      <params>
        <param name="OCRStrategy" type="string">auto</param>
      </params>
    </parser>
  </parsers>
  <server>
    <params>
      <logLevel>debug</logLevel>
    </params>
  </server>
</properties>