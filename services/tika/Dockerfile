FROM apache/tika:*******-full

LABEL org.opencontainers.image.authors="<PERSON> <<EMAIL>>"
LABEL tika-version="*******"
LABEL tesseract-support-language="eng, fra, ger, ita, spa, chi_sim, chi_tra"

# From Tika Docker image
ARG UID_GID=35002:35002

# Install tesseract language packages
USER root
RUN apt-get update && \
    apt-get install --yes --no-install-recommends \
    tesseract-ocr-chi-sim \
    tesseract-ocr-chi-tra \
    imagemagick \
    python3-pip

RUN pip3 install scikit-image --break-system-packages

# Clean up
RUN apt-get clean -y && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Switch back to non-root user
USER ${UID_GID}

COPY tika-config.xml /app/tika-config.xml
ENV TIKA_CONFIG=/app/tika-config.xml
