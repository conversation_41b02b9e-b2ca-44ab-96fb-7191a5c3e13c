// Documentation for this file: https://prettier.io/en/configuration.html
module.exports = {
  // We use a larger print width because <PERSON><PERSON><PERSON>'s word-wrapping seems to be tuned for plain JavaScript without type annotations
  printWidth: 110,

  // Use .gitattributes to manage newlines
  endOfLine: "auto",

  // Use single quotes instead of double quotes
  // singleQuote: true,
  singleQuote: false,

  // For ES5, trailing commas cannot be used in function parameters; it is counterintuitive to use them for arrays only
  trailingComma: "none",

  // Print semicolons at the ends of statements.
  semi: true,
  // Specify the number of spaces per indentation-level.
  tabWidth: 2,
  // Print spaces between brackets in object literals.
  bracketSpacing: true,
  // Include parentheses around a sole arrow function parameter
  arrowParens: "always",
  // Put the > of a multi-line element at the end of the last line instead of being alone on the next line
  bracketSameLine: false
};
