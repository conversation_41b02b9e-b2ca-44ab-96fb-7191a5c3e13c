# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov/

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output/

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt/

# Bower dependency directory (https://bower.io/)
bower_components/

# node-waf configuration
.lock-wscript/

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release/

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm/

# Optional eslint cache
.eslintcache/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env*
!.env.example
# .env
# .env.development.local 
# .env.test.local
# .env.production.local
# .env.local

# typedoc generated docs
docs/

# next.js build output
.next/

# Docusaurus cache and generated files
.docusaurus/

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# yarn v2
.yarn/cache/
.yarn/unplugged/
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# OS X temporary files
.DS_Store

# IntelliJ IDEA project files; if you want to commit IntelliJ settings, this recipe may be helpful:
# https://github.com/github/gitignore/blob/main/Global/JetBrains.gitignore
.idea/
*.iml

# Visual Studio Code
# .vscode/
!.vscode/tasks.json
!.vscode/launch.json

# Rush temporary files
common/deploy/
common/temp/
common/autoinstallers/*/.npmrc
**/.rush/temp/
*.lock

# Common toolchain intermediate files
temp/
lib/
lib-amd/
lib-es6/
lib-esnext/
lib-commonjs/
lib-shim/
dist/
dist-storybook/
*.tsbuildinfo

# Heft temporary files
.cache/
.heft/

# API docs
swagger-spec.json

# Dependency graph
dependency-graph.json

# Storybook build
storybook-static/
*storybook.log

# Encryption keys
*.pem

# Cursor rules
.cursorrules

# Repl history
.replhistory

# profile
*.cpuprofile
*.heapprofile
*.heapsnapshot

# CDK asset staging directory
.cdk.staging
cdk.out

# Firebase
*.key
service-account.json